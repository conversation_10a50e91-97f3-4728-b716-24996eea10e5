DROP TABLE IF EXISTS `hst_takeout_item_extends`;
CREATE TABLE `hst_takeout_item_extends` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `guid` varchar(50) DEFAULT NULL COMMENT 'item表的item_guid',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` varchar(50) DEFAULT NULL COMMENT '订单guid',
  `erp_item_sku_guid` varchar(50) DEFAULT NULL COMMENT '门店商品规格guid',
  `cost_price` decimal(12,2) DEFAULT '0.00' COMMENT '成本价',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
  KEY `idx_order_guid` (`order_guid`) USING BTREE,
  KEY `idx_erp_sku_guid` (`erp_item_sku_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 COMMENT='外卖菜品扩展表';