package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ConfigDO
 * @date 2018/08/30 14:46
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hst_takeout_config")
public class ConfigDO implements Serializable {

    private static final long serialVersionUID = 6085295134185403315L;

    /**
     * 主键ID
     */
    private long id;

    /**
     * 配置GUID
     */
    private String configGuid;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 是否为自动接单：0=否，1=是
     */
    @TableField("is_auto_order")
    private Boolean autoOrder;

    /**
     * 是否打印外卖取消小票：0=否，1=是
     */
    @TableField("is_print_cancel_order")
    private Boolean printCancelOrder;

    /**
     * 操作人GUID
     */
    private String staffGuid;

    /**
     * 操作人姓名
     */
    private String staffName;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;
}
