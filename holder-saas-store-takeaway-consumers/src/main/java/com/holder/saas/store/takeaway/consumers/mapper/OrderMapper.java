package com.holder.saas.store.takeaway.consumers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import com.holder.saas.store.takeaway.consumers.entity.query.HandoverPayQuery;
import com.holder.saas.store.takeaway.consumers.entity.query.OrderRemindQuery;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.ItemRespDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderListDTO;
import com.holderzone.saas.store.dto.takeaway.request.BusinessTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutOrderStatisticsRespDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外卖订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-26
 */
public interface OrderMapper extends BaseMapper<OrderDO> {

    OrderReadDO getOrderDetail(OrderDO orderDO);

    List<ItemDO> queryItemSubInfo(SingleListDTO query);

    @Deprecated
    OrderReadDO getOrderAllDetail(OrderDO orderDO);

//    @Deprecated
//    List<OrderReadDO> listOrder(OrderDO orderDO);

    /**
     * 功能描述：查询外卖订单列表
     *
     * @param storeGuid 门店guid
     * @return java.util.List<com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO>
     * @date 2022/3/18
     */
    List<TakeoutOrderListDTO> listOrder(@Param("storeGuid") String storeGuid);

    TakeoutOrderStatisticsRespDTO countOrder(@Param("storeGuid") String storeGuid);

    OrderReadDO getOrderRemind(OrderRemindQuery orderRemindQuery);

    @Deprecated
    List<OrderReadDO> getOrderMoney(HandoverPayQuery handoverPayQuery);


    /**
     * 根据条件统计订单总数
     *
     * @param reqDTO 统计条件
     * @return 统计结果
     */
    int getTakeoutOrderCount(BusinessTakeoutOrderReqDTO reqDTO);

    /**
     * 根据条件查询外面订单数据
     *
     * @param reqDTO 查询条件
     * @return 查询结果
     */
    List<OrderReadDO> getTakeoutOrderList(BusinessTakeoutOrderReqDTO reqDTO);


    void updateThirdCarrierIdByOrderId(@Param("thirdCarrierId") String thirdCarrierId, @Param("orderId") String orderId);


    List<OrderDO> listBusinessDay(@Param("orderIds") List<String> orderIds);

    List<ItemRespDTO> listTakeOutItemSale(@Param("dto")DailyReqDTO request);

    int countAdjustTakeawayOrder(@Param("query")AdjustByOrderListQuery query);

    List<OrderDO> listAdjustTakeawayOrderPage(@Param("query")AdjustByOrderListQuery query);
}
