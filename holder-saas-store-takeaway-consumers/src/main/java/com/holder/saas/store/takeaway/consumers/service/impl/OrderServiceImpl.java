package com.holder.saas.store.takeaway.consumers.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.holder.saas.store.takeaway.consumers.entity.domain.*;
import com.holder.saas.store.takeaway.consumers.entity.query.OrderRemindQuery;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.helper.PageAdapter;
import com.holder.saas.store.takeaway.consumers.mapper.*;
import com.holder.saas.store.takeaway.consumers.mapstruct.ItemMapstruct;
import com.holder.saas.store.takeaway.consumers.mapstruct.ItemPackageMapstruct;
import com.holder.saas.store.takeaway.consumers.mapstruct.OrderMapstruct;
import com.holder.saas.store.takeaway.consumers.service.*;
import com.holder.saas.store.takeaway.consumers.service.rpc.*;
import com.holder.saas.store.takeaway.consumers.utils.*;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.dds.starter.utils.JacksonUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.*;
import com.holderzone.holder.saas.member.terminal.enums.TerminalTypeEnum;
import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsQueryDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.order.common.PushOrderBillsBO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.ItemRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.format.TakeoutFormatDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.BusinessTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.FoodListDTO;
import com.holderzone.saas.store.dto.takeaway.request.OrderCallDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import com.holderzone.saas.store.dto.takeaway.response.*;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderItemRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByTakeawayOrderRespDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import com.holderzone.saas.store.enums.takeaway.TakeawayRefundStatusEnum;
import com.holderzone.saas.store.enums.user.AuthorityReportHideEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * 外卖订单服务实现类
 * </p>
 * <p>
 * fixme 后续把 OrderService 拆分了，分为:
 * fixme 终端用户的外卖单处理
 * fixme 终端用户的配送单处理
 * fixme 外卖平台的回调处理
 * fixme 配送平台的回调处理
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderServiceImpl extends ServiceImpl<OrderMapper, OrderDO> implements OrderService {

    public static final Integer NUMBER_ZERO = 0;

    @Value("${dx.TASK_ID}")
    private String TASK_ID;

    @Value("${dx.DELAY_CALL}")
    private Integer DELAY_CALL;

    @Value("${dx.OVERTIME_TIME}")
    private Integer OVERTIME_TIME;

    @Value("${dx.EXCEED_TIME}")
    private Integer EXCEED_TIME;

    @Value("${mdm.host:#{null}}")
    private String mdmRequestHost;

    @Value("${small.ENTERPRISE_FOR_HESHI}")
    private String enterpriseGuidForHeShi;

    private final RemindMapper remindMapper;

    private final OrderMapper orderMapper;

    private static final String ORDER_CANCELLED = "订单已取消，请勿重复处理";

    private static final String NOT_REPEAT_OPERATION_DISAGREE = "请勿重复操作不同意";

    private final OrderMapstruct orderMapstruct;

    private final ItemService itemService;

    private final ItemExtendsService itemExtendsService;

    private final DiscountService discountService;

    private final PrintService printService;

    private final LogService logService;

    private final OrderCallService orderCallService;

    private final TakeoutMsgFactory takeoutMsgFactory;

    private final TakeoutLogFactory takeoutLogFactory;

    private final OrgFeignClient orgFeignClient;

    private final BizMsgFeignClient bizMsgFeignClient;

    private final ProducerFeignClient producerFeignClient;

    private final OrderCallMapper orderCallMapper;

    private final ItemFeignClient itemFeignClient;

    private final ErpService erpService;

    private final KdsService kdsService;

    private final DynamicHelper dynamicHelper;

    private final DistributedService distributedService;

    private final DeliveryService deliveryService;

    private final TransactionTemplate transactionTemplate;
    private final OrganizationService organizationService;
    private final MappingService mappingService;
    private final ItemMappingService itemMappingService;
    private final ItemMappingMapper itemMappingMapper;
    private final RefundItemService refundItemService;

    private final AbnormalDataMapper abnormalDataMapper;


    private final ItemMapstruct itemMapstruct;

    private final ConfigService configService;

    private final AutoAcceptOrderService autoAcceptOrderService;

    private final AutoRecoveryService autoRecoveryService;

    private final PackageService packageService;

    private final ItemPackageMapstruct itemPackageMapstruct;

    private final AbnormalDataService abnormalDataService;

    private final ExecutorService autoAcceptOrderThreadPool;

    private final ExecutorService stockErpThreadPool;

    private final PrintFeignClient printFeignClient;

    private final StaffFeignClient staffFeignClient;

    private static final ExecutorService EXECUTOR = new ThreadPoolExecutor(5, 10,
            5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10));

    private static final ScheduledExecutorService EXECUTOR_SERVICE = Executors.newScheduledThreadPool(5, new ThreadFactoryBuilder()
            .setNameFormat("退菜异步线程-%d")
            .build());

    private static final String BUSINESS_MT = "美团商家版";

    private static final String BUSINESS_ELM = "饿了么商家版";

    private static final String BUSINESS_OWN = "自营外卖商家版";

    private static final String BUSINESS_TCD = "通吃岛商家版";

    private static final String BUSINESS_JD = "京东商家版";

    // 平台类型映射常量
    private static final Map<Integer, String> PLATFORM_NAME_MAP = new HashMap<>();
    private static final Map<Integer, Integer> PLATFORM_CODE_MAP = new HashMap<>();
    
    static {
        // 初始化平台名称映射
        PLATFORM_NAME_MAP.put(0, "美团");    // MT_TAKEOUT
        PLATFORM_NAME_MAP.put(1, "饿了么");  // ELE_TAKEOUT  
        PLATFORM_NAME_MAP.put(6, "赚餐");    // TCD_TAKEOUT
        PLATFORM_NAME_MAP.put(3, "京东");    // JD_TAKEOUT
        
        // 初始化平台代码映射
        PLATFORM_CODE_MAP.put(0, -2);  // MT_TAKEOUT
        PLATFORM_CODE_MAP.put(1, -1);  // ELE_TAKEOUT
        PLATFORM_CODE_MAP.put(6, -6);  // TCD_TAKEOUT  
        PLATFORM_CODE_MAP.put(3, -3);  // JD_TAKEOUT
    }

    @Override
    public List<TakeoutOrderListDTO> listOrder(TakeoutOrderDTO takeoutOrderDTO) {
        if (StringUtils.isEmpty(takeoutOrderDTO.getStoreGuid())) {
            throw new ParameterException("门店Guid不能为空!");
        }
        List<TakeoutOrderListDTO> orderList = orderMapper.listOrder(takeoutOrderDTO.getStoreGuid());
        if (CollectionUtil.isEmpty(orderList)) {
            return Collections.emptyList();
        }
        for (TakeoutOrderListDTO order : orderList) {
            setEstimateDeliveredTime(order);
            //若是饿了么，则展示隐私号
            if (order.getOrderSubType() == 1) {
                order.setCustomerPhone(order.getPrivacyPhone());
            }
        }
        return orderList;
    }

    @Override
    public Page<TakeoutOrderListDTO> pageOrder(TakeoutOrderDTO takeoutOrderDTO) {
        return new Page<>();
    }

    @Override
    public TakeoutOrderStatisticsRespDTO countOrder(TakeoutOrderDTO takeoutOrderDTO) {
        return new TakeoutOrderStatisticsRespDTO();
    }

    @Override
    public TakeoutOrderStatisticsRespDTO statisticsOrder(TakeoutOrderDTO takeoutOrderDTO) {
        return new TakeoutOrderStatisticsRespDTO();
    }

    @Override
    public TakeoutOrderDTO getOrder(TakeoutOrderDTO takeoutOrderDTO) {
        String orderGuid = takeoutOrderDTO.getOrderGuid();
        OrderReadDO orderDoInDb = baseMapper.getOrderDetail(new OrderDO().setOrderGuid(orderGuid));
        if (null == orderDoInDb) {
            log.error("根据orderGuid[{}]没查到订单详情", orderGuid);
            throw new BusinessException("根据orderGuid:" + orderGuid + "没查到订单详情");
        }
        TakeoutOrderDTO takeoutOrderResult = orderMapstruct.toTakeoutOrder(orderDoInDb);
        // 判断是否有退款操作
        takeoutOrderResult.setRefundedFlag(!StringUtils.isEmpty(orderDoInDb.getCustomerRefundItem()));
        return setEstimateDeliveredTime(takeoutOrderResult);
    }

    @Override
    public TakeoutOrderDTO getOrderDetailMapping(String orderGuid) {
        // 查询外卖订单
        OrderReadDO orderDoInDb = baseMapper.getOrderDetail(new OrderDO().setOrderGuid(orderGuid));
        if (Objects.isNull(orderDoInDb)) {
            throw new BusinessException("外卖订单不存在！");
        }
        // 返回DTO
        return getTakeoutOrderDTO(orderDoInDb);
    }

    private TakeoutOrderDTO getTakeoutOrderDTO(OrderReadDO orderDoInDb) {
        TakeoutOrderDTO takeoutOrderResult = orderMapstruct.toTakeoutOrder(orderDoInDb);

        List<TakeoutOrderDTO.OrderItemDTO> arrayOfItem = takeoutOrderResult.getArrayOfItem();
        Map<String, List<TakeoutOrderDTO.OrderItemDTO>> groupByErpItemSkuGuid = arrayOfItem.stream()
                .filter(e -> !StringUtils.isEmpty(e.getErpItemSkuGuid()))
                .collect(Collectors.groupingBy(TakeoutOrderDTO.OrderItemDTO::getErpItemSkuGuid));
        if (MapUtils.isEmpty(groupByErpItemSkuGuid)) {
            return takeoutOrderResult;
        }

        // 查询sku
        ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(new ArrayList<>(groupByErpItemSkuGuid.keySet()));
        itemStringListDTO.setStoreGuid(orderDoInDb.getStoreGuid());
        List<SkuTakeawayInfoRespDTO> arrayOfSkuTakeawayInfoRespList = itemFeignClient.listSkuInfoAndSub(itemStringListDTO);
        Map<String, SkuTakeawayInfoRespDTO> skuTakeawayInfoMap = arrayOfSkuTakeawayInfoRespList.stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key2));

        for (TakeoutOrderDTO.OrderItemDTO itemDTO : arrayOfItem) {
            if (StringUtils.isEmpty(itemDTO.getErpItemSkuGuid())) {
                continue;
            }
            SkuTakeawayInfoRespDTO skuInfo = skuTakeawayInfoMap.get(itemDTO.getErpItemSkuGuid());
            if (Objects.isNull(skuInfo)) {
                continue;
            }
            itemDTO.setSkuTakeawayInfoRespDTO(skuInfo);
        }
        return takeoutOrderResult;
    }

    @Override
    public TakeoutOrderDTO getOrderByOrderNo(String orderNo, String storeGuid) {
        // 查询外卖订单
        OrderReadDO orderDoInDb = baseMapper.getOrderDetail(new OrderDO().setOrderViewId(orderNo).setStoreGuid(storeGuid));
        if (Objects.isNull(orderDoInDb)) {
            return null;
        }
        // 返回DTO
        return getTakeoutOrderDTO(orderDoInDb);
    }

    private static TakeoutOrderDTO setEstimateDeliveredTime(TakeoutOrderDTO takeoutOrderResult) {
        LocalDateTime estimateDeliveredTime = takeoutOrderResult.getEstimateDeliveredTime();
        String formatMillsStr = (estimateDeliveredTime == null || 0 == DateTimeUtils.localDateTime2Mills(estimateDeliveredTime))
                ? "立即送达" : DateTimeUtils.localDateTime2String(estimateDeliveredTime);
        takeoutOrderResult.setEstimateDeliveredTimeString(formatMillsStr);
        takeoutOrderResult.setCustomerName(StringEscapeUtils.unescapeHtml(takeoutOrderResult.getCustomerName()));
        return takeoutOrderResult;
    }

    private static void setEstimateDeliveredTime(TakeoutOrderListDTO dto) {
        LocalDateTime estimateDeliveredTime = dto.getEstimateDeliveredTime();
        String formatMillsStr = (estimateDeliveredTime == null || 0 == DateTimeUtils.localDateTime2Mills(estimateDeliveredTime))
                ? "立即送达" : DateTimeUtils.localDateTime2String(estimateDeliveredTime);
        dto.setEstimateDeliveredTimeString(formatMillsStr);
        dto.setCustomerName(StringEscapeUtils.unescapeHtml(dto.getCustomerName()));
    }

    /**
     * fixme 逻辑merge到orderSave中，不要用单独的方法，合并到orderSave中
     *
     * @param unOrder
     */
    @Override
    public void orderOwnSave(UnOrder unOrder) {
        log.info("({})订单保存，orderId: {}，处理中", "自营外卖平台", unOrder.getOrderId());
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        //判断该orderID是否在数据库中已经存在
        OrderDO orderInDb = getOne(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getOrderId, orderDO.getOrderId()));
        if (orderInDb != null) {
            log.info("该条数据已经存在,id={}", unOrder.getOrderId());
            // fixme 数据已存在，抛了异常的话mq又会重新消费!记录error或warn日志并返回即可
            throw new BusinessException("该条数据已经存在");
        }
        String orderGuid = IDUtils.nextId();
        // fixme unOrder里面有值，为什么要去producer取!?
        // fixme 而且orderMapstruct.fromUnOrder(unOrder)已经赋值了
        HolderAuthDTO holder = producerFeignClient.getHolder(unOrder.getStoreGuid());
        orderDO.setStoreGuid(holder.getStoreGuid());
        orderDO.setEnterpriseGuid(holder.getEnterpriseGuid());
        //fixme
        orderDO.setOrderGuid(orderGuid);
        orderDO.setOrderStatus(OrderStatus.TO_DO);
        orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
        orderDO.setItemCount(unOrder.getArrayOfUnItem().stream()
                .map(UnItem::getItemCount).reduce(new BigDecimal(0), BigDecimal::add));
        orderDO.setReminded(false);
        //营业日新接口
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        ArrayList<String> storeGuidList = new ArrayList<>();
        storeGuidList.add(UserContextUtils.getStoreGuid());
        reqDTO.setStoreGuidList(storeGuidList);
        reqDTO.setQueryDateTime(LocalDateTime.now());
        LocalDate businessDay = orgFeignClient.queryBusinessDay(reqDTO);
        orderDO.setBusinessDay(businessDay);

        // 查询是否自动接单
        ConfigDO configDO = configService.selectByStoreGuid(unOrder.getStoreGuid());
        boolean autoAccept = configDO != null && configDO.getAutoOrder();
        orderDO.setIsAutoAccept(autoAccept);

        save(orderDO);

        List<ItemDO> arrayOfItemDO = orderMapstruct.fromUnItem(unOrder.getArrayOfUnItem());
        String storeGuid = orderDO.getStoreGuid();
        for (ItemDO itemDO : arrayOfItemDO) {
            itemDO.setStoreGuid(storeGuid);
            itemDO.setOrderGuid(orderGuid);
            itemDO.setItemGuid(DistributedUtils.id());
            itemDO.setItemNamePlatform(itemDO.getItemName() + "(" + getPlatformName(unOrder) + ")");
            itemDO.setOrderSubType(unOrder.getOrderSubType());
        }

        List<DiscountDO> arrayOfDiscountDO = orderMapstruct.fromUnDiscount(unOrder.getArrayOfUnDiscount());
        for (DiscountDO discountDO : arrayOfDiscountDO) {
            discountDO.setOrderGuid(orderGuid);
            discountDO.setDiscountGuid(IDUtils.nextId());
        }
        if (!arrayOfItemDO.isEmpty()) {
            itemService.saveBatch(arrayOfItemDO);
        }
        if (!arrayOfDiscountDO.isEmpty()) {
            discountService.saveBatch(arrayOfDiscountDO);
        }

        // 订单日志
        logService.save(takeoutLogFactory.createCustomerInfo(orderDO));
        logService.save(takeoutLogFactory.createOrderCreated(orderDO));
        // 消息推送
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderCreated(orderDO, false, autoAccept));
        log.info("自营外卖平台订单保存完毕：orderDO={}", orderDO);
    }

    private void doCreateOrder(UnOrder unOrder, boolean acceptAhead) {
        if (!acceptAhead) {
            log.info("({})新订单，orderId: {}，处理中", getPlatformName(unOrder), unOrder.getOrderId());
        } else {
            log.info("({})新订单(接单先于新订单回调)，orderId: {}，处理中", getPlatformName(unOrder), unOrder.getOrderId());
        }
        //保存订单或者更新unOrder订单guid
        String orderGuid = distributedService.nextOrderGuid();
        OrderDO orderDO = saveOrUpdateOrderGuid(unOrder, orderGuid);

        List<ItemDO> arrayOfItemDO = new ArrayList<>();
        List<ItemExtendsDO> itemExtendsDOList = new ArrayList<>();
        for (UnItem unItem : unOrder.getArrayOfUnItem()) {
            ItemDO itemDO = orderMapstruct.fromUnItem(unItem);
            if (!ObjectUtils.isEmpty(itemDO.getSettleType()) && itemDO.getSettleType().equals(1)) {
                itemDO.setItemName("【满赠】" + itemDO.getItemName());
            }
            //数据库最大长度
            itemDO.setItemName(StrUtil.maxLength(itemDO.getItemName(), 80));
            String itemGuid = DistributedUtils.id();
            itemDO.setItemGuid(itemGuid);
            arrayOfItemDO.add(itemDO);

            ItemExtendsDO itemExtendsDO = new ItemExtendsDO();
            itemExtendsDO.setGuid(itemGuid);
            itemExtendsDO.setOrderGuid(orderGuid);
            itemExtendsDO.setSubItemInfo(JacksonUtils.writeValueAsString(unItem.getSubItemList()));
            itemExtendsDOList.add(itemExtendsDO);
        }

        List<PackageDO> listPackage = Lists.newArrayList();

        // 查询绑定的门店商品
        appendMapOfSkuTakeawayInfo(orderDO.getStoreGuid(), orderDO.getOrderSubType(), arrayOfItemDO, itemExtendsDOList);
        for (ItemDO itemDO : arrayOfItemDO) {
            itemDO.setGmtCreate(LocalDateTime.now());
            itemDO.setStoreGuid(orderDO.getStoreGuid());
            itemDO.setStoreName(orderDO.getStoreName());
            itemDO.setBrandGuid(orderDO.getBrandGuid());
            itemDO.setBrandName(orderDO.getBrandName());
            itemDO.setBusinessDay(orderDO.getBusinessDay());
            itemDO.setOrderGuid(orderGuid);
            itemDO.setItemNamePlatform(itemDO.getItemName() + "(" + getPlatformName(unOrder) + ")");
            itemDO.setOrderSubType(unOrder.getOrderSubType());

            //套餐商品明细
            List<SkuInfoPkgDTO> listPkg = itemDO.getListPkg();
            if (CollectionUtil.isNotEmpty(listPkg)) {
                listPkg.forEach(v -> {
                    PackageDO packageDO = itemPackageMapstruct.parseToPackageDO(v);
                    // 此处使用的核算价为外卖核算价
                    packageDO.setAccountingPrice(v.getTakeawayAccountingPrice());
                    packageDO.setTakeoutItemGuid(itemDO.getItemGuid());
                    listPackage.add(packageDO);
                });
            }
        }
        List<DiscountDO> arrayOfDiscountDO = orderMapstruct.fromUnDiscount(unOrder.getArrayOfUnDiscount());
        if (arrayOfDiscountDO != null) {
            for (DiscountDO discountDO : arrayOfDiscountDO) {
                discountDO.setOrderGuid(orderGuid);
                discountDO.setDiscountGuid(IDUtils.nextId());
            }
        }
        if (!arrayOfItemDO.isEmpty()) {
            itemService.saveBatch(arrayOfItemDO);
            itemExtendsService.saveBatch(itemExtendsDOList);

            // 异常数据处理
            List<ItemDO> abnormalList = arrayOfItemDO.stream()
                    .filter(i -> StringUtils.isEmpty(i.getErpItemName()) && !StringUtils.isEmpty(i.getThirdSkuId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(abnormalList)) {
                List<AbnormalDataDO> abnormalDataDOList = itemMapstruct.itemDOList2AbnormalDataDOList(abnormalList);
                abnormalDataDOList.forEach(abnormalDataMapper::insert);
            }
        }
        if (arrayOfDiscountDO != null && !arrayOfDiscountDO.isEmpty()) {
            discountService.saveBatch(arrayOfDiscountDO);
        }

        //保存套餐商品明细
        if (CollectionUtil.isNotEmpty(listPackage)) {
            packageService.saveBatch(listPackage);
        }
        // 订单日志
        logService.save(takeoutLogFactory.createCustomerInfo(orderDO));
        logService.save(takeoutLogFactory.createOrderCreated(orderDO));

        // 新订单消息推送
        // fixme 版本上线后，修改为调用message的mq方法，推送消息
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderCreated(orderDO, acceptAhead, orderDO.getIsAutoAccept()));

        // 自动接单订单记录
        if (!acceptAhead) {
            autoAcceptOrderService.createOrder(orderDO);
        }

        if (!acceptAhead) {
            log.info("({})新订单，orderId: {}，处理完毕", getPlatformName(unOrder), unOrder.getOrderId());
        } else {
            log.info("({})新订单(接单先于新订单回调)，orderId: {}，处理完毕", getPlatformName(unOrder), unOrder.getOrderId());
        }
    }

    private OrderDO saveOrUpdateOrderGuid(UnOrder unOrder, String orderGuid) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);


        orderDO.setOrderGuid(orderGuid);
        orderDO.setOrderStatus(OrderStatus.TO_DO);
        orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
        orderDO.setItemCount(unOrder.getArrayOfUnItem().stream()
                .map(UnItem::getItemCount).reduce(new BigDecimal(0), BigDecimal::add));
        orderDO.setReminded(false);

        // 设置营业日以及门店名称品牌
        StoreDTO storeDTO = organizationService.queryStoreBaseByGuid(unOrder.getStoreGuid());
        if (!ObjectUtils.isEmpty(storeDTO)) {
            orderDO.setStoreName(storeDTO.getName());
            orderDO.setBrandGuid(storeDTO.getBelongBrandGuid());
            orderDO.setBrandName(storeDTO.getBelongBrandName());
            orderDO.setBusinessDay(storeDTO.getBusinessDay());
        }
        // 查询是否自动接单
        ConfigDO configDO = configService.selectByStoreGuid(unOrder.getStoreGuid());
        boolean autoAccept = configDO != null && configDO.getAutoOrder();
        orderDO.setIsAutoAccept(autoAccept);
        OrderDO orderInDb = getOne(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getOrderId, orderDO.getOrderId()));
        log.info("orderId={},orderInDb={}", unOrder.getOrderId(), orderInDb);
        if (orderInDb == null) {
            this.save(orderDO);
            unOrder.setOrderGuid(orderGuid);
        } else {
            unOrder.setOrderGuid(orderInDb.getOrderGuid());
            log.info("orderId={},订单已存在不执行逻辑", unOrder.getOrderId());
        }
        return orderDO;
    }

    private String orderContext(String shopName, String platform, String daySn, String orderId) {
        return shopName + platform + "#" + daySn + "订单[" + orderId + "]";
    }

    @Override
    public void orderCreate(UnOrder unOrder) {
        log.info("({})进入新订单方法，orderId: {}", getPlatformName(unOrder), unOrder.getOrderId());
        boolean lockResult;
        String lockKey = unOrder.getOrderSubType() + ":" + unOrder.getOrderId();
        lockResult = RedissonLockUtil.tryLock(lockKey, 10, 60);
        log.info("orderId:{},lockResult:{}", unOrder.getOrderId(), lockResult);
        if (!lockResult) {
            throw new BusinessException("获取外卖订单分布式锁失败，稍后重试!");
        }
        try {
            //根据外卖商品映射抵扣数量重新设置订单商品的数量
            modifyOrderProductCountByItemMapping(unOrder);
            transactionTemplate.execute(transactionStatus -> {
                boolean isExist = count(wrapperByOrderId(unOrder.getOrderId())) > 0;
                if (isExist) {
                    log.info("({})新订单，orderId: {}，请求重复或滞后，忽略该请求", getPlatformName(unOrder), unOrder.getOrderId());
                } else {
                    // 创建订单，注意与orderAccept的逻辑异同
                    doCreateOrder(unOrder, false);
                }
                return null;
            });
        } finally {
            log.info("新订单方法结束,orderId:{}", unOrder.getOrderId());
            RedissonLockUtil.unlock(lockKey);
        }
    }

    private void modifyOrderProductCountByItemMapping(UnOrder unOrder) {
        List<UnItem> items = unOrder.getArrayOfUnItem();
        if (CollUtil.isEmpty(items)) {
            return;
        }
        List<String> unItemSkuIds = items.stream().map(UnItem::getUnItemSkuId).collect(Collectors.toList());
        Map<String, String> itemByMapperGuidMap = Maps.newHashMap();
        if (unOrder.getOrderSubType() == OrderType.TakeoutSubType.MT_TAKEOUT.getType()) {
            // 美团没有unItemSkuId，使用itemSkuId查询
            List<String> itemSkuIds = items.stream().map(UnItem::getItemSku).collect(Collectors.toList());
            // 查询映射之后的unItemSkuId
            List<ItemMappingDO> itemMappingList = itemMappingService.queryByStoreGuidAndTakeoutTypeAndMapperGuids(unOrder.getStoreGuid(), unOrder.getOrderSubType(), itemSkuIds);
            itemByMapperGuidMap = itemMappingList.stream().collect(Collectors.toMap(ItemMappingDO::getMapperGuid, ItemMappingDO::getUnItemSkuId));
            unItemSkuIds = new ArrayList<>(itemByMapperGuidMap.values());
        }
        Map<String, Integer> mappedCountMap = mappingService.queryItemBindExtendInfo(unOrder.getStoreGuid(), getTakeoutType(unOrder), unItemSkuIds)
                .stream().collect(Collectors.toMap(ItemBindExtendInfoDo::getUnItemSkuId, ItemBindExtendInfoDo::getUnItemCountMapper));
        log.info("创建订单商品映射数量开始,mappedCountMap:{}", mappedCountMap);
        Map<String, String> finalItemByMapperGuidMap = itemByMapperGuidMap;
        items.forEach(i -> {
            //美团映射商品
            if (CollUtil.isNotEmpty(finalItemByMapperGuidMap)) {
                i.setUnItemSkuId(finalItemByMapperGuidMap.get(i.getItemSku()));
            }
            Integer mapper = mappedCountMap.get(i.getUnItemSkuId());
            if (mapper != null && mapper > 1) {
                i.setActualItemCount(BigDecimal.valueOf(mapper).multiply(i.getItemCount()));
                log.info("重设外卖商品[{}({})-{}]购买数量[{}*{}->{}]",
                        i.getItemCode(), i.getItemSku(), i.getItemName(), i.getItemCount(), mapper, i.getActualItemCount());
            } else {
                i.setActualItemCount(i.getItemCount());
            }
            log.info("创建订单商品映射数量完成,items:{},itemName:{},itemCount:{},actualItemCount:{}", i, i.getItemName(), i.getItemCount(), i.getActualItemCount());
        });

    }

    @Override
    public void acceptOrder(TakeoutOrderDTO takeoutOrderDTO) {
        OrderDO orderInDb = getOne(wrapperByOrderGuid(takeoutOrderDTO.getOrderGuid()));
        log.info("({})商户接单，orderId: {}", getPlatformName(orderInDb.getOrderSubType()), orderInDb.getOrderId());
        Integer orderStatus = orderInDb.getOrderStatus();
        if (orderStatus != OrderStatus.TO_DO) {
            idempotent(orderStatus);
        } else {
            // mapstruct 中包含 acceptDeviceId, acceptDeviceType 的填充
            OrderDO orderDO = orderMapstruct.fromTakeoutOrder(takeoutOrderDTO);
            orderDO.setAcceptStaffGuid(UserContextUtils.getUserGuid());
            orderDO.setAcceptStaffName(UserContextUtils.getUserName());
            if (StringUtils.isEmpty(orderDO.getAcceptStaffGuid()) || StringUtils.isEmpty(orderDO.getAcceptStaffName())) {
                orderDO.setAcceptStaffGuid(takeoutOrderDTO.getAcceptStaffGuid());
                orderDO.setAcceptStaffName(takeoutOrderDTO.getAcceptStaffName());
            }
            orderDO.setAcceptDeviceId(takeoutOrderDTO.getDeviceId());
            orderDO.setAcceptDeviceType(takeoutOrderDTO.getDeviceType());

            updateById(orderDO.setId(orderInDb.getId()));
            itemService.update(new ItemDO(), new UpdateWrapper<ItemDO>().lambda()
                    .set(ItemDO::getStoreName, takeoutOrderDTO.getStoreName())
                    .eq(ItemDO::getOrderGuid, takeoutOrderDTO.getOrderGuid()));
            UnOrder unOrder = orderMapstruct.toUnOrder(orderInDb);
            //传递参数新增
            decorateReplyOrder(unOrder);
            unOrder.setOperateStaffName(UserContextUtils.getUserName());
            unOrder.setReplyMsgType(UnOrderReplyMsgType.CONFIRM_ORDER);
            //自营外卖特殊字段
            unOrder.setDistributionType(takeoutOrderDTO.getDistributionType());
            producerFeignClient.reply(unOrder);
        }
    }

    private void decorateReplyOrder(UnOrder unOrder){
        //若是京东外卖需要在参数中拼接品牌
        if(OrderType.TakeoutSubType.JD_TAKEOUT.getTypeInteger() == unOrder.getOrderSubType()){
            BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(unOrder.getStoreGuid());
            unOrder.setBrandGuid(brandDTO.getGuid());
        }
    }

    @Override
    public void orderAccept(UnOrder unOrder) {
        log.info("({})进入已接单方法，orderId: {}", getPlatformName(unOrder), unOrder.getOrderId());
        boolean lockResult = false;
        String lockKey = unOrder.getOrderSubType() + ":" + unOrder.getOrderId();
        try {
            lockResult = acquireLock(lockKey);
            OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
            OrderReadDO orderReadDO = getAndValidateOrder(orderDO, unOrder);
            
            String orderContext = buildOrderContext(orderReadDO, unOrder);
            log.info("{}已确认，处理中", orderContext);
            
            if (!isValidOrderStatus(orderReadDO)) {
                log.info("{}已确认，订单状态不是待处理，忽略该请求", orderContext);
                return;
            }

            // 更新订单状态
            updateOrderStatus(orderReadDO, unOrder, orderContext);
            
            // 处理订单后续流程
            processOrderAfterAccept(orderReadDO, unOrder, orderContext);
            
            log.info("({})订单已确认，orderId: {}，处理完毕", getPlatformName(unOrder), unOrder.getOrderId());
        } catch (BusinessException e) {
            log.error("订单接单处理业务异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("订单接单处理系统异常: {}", e.getMessage(), e);
            throw new BusinessException("订单接单处理失败，请稍后重试");
        } finally {
            releaseLock(lockKey, lockResult, unOrder.getOrderId());
        }
    }

    private boolean acquireLock(String lockKey) {
        boolean lockResult = RedissonLockUtil.tryLock(lockKey, 10, 10);
        log.info("lockKey:{}, lockResult:{}", lockKey, lockResult);
        if (!lockResult) {
            throw new BusinessException("获取外卖订单分布式锁失败，稍后重试!");
        }
        return true;
    }

    private void releaseLock(String lockKey, boolean lockResult, String orderId) {
        log.info("已接单方法结束orderId:{},lockResult:{}", orderId, lockResult);
        if (lockResult) {
            RedissonLockUtil.unlock(lockKey);
        }
    }

    private OrderReadDO getAndValidateOrder(OrderDO orderDO, UnOrder unOrder) {
        OrderReadDO orderReadDO = baseMapper.getOrderDetail(orderDO);
        log.info("订单详情: {}", JacksonUtils.writeValueAsString(orderReadDO));
        
        if (orderReadDO == null) {
            if (0 == orderDO.getOrderSubType() || 6 == orderDO.getOrderSubType()) {
                // 美团商家版自动接单的情况下：
                // 1%的订单:只会推送接单
                // 99%的订单:既推送新订单，也推送接单，但两者几乎同时到达
                modifyOrderProductCountByItemMapping(unOrder);
                doCreateOrder(unOrder, true);
                orderReadDO = baseMapper.getOrderDetail(orderDO);
            } else {
                throw new BusinessException("接单推送先于新订单推送，将在消息重新消费时处理该推送");
            }
        }
        
        // 补全套餐子菜信息
        replenishSubInfo(orderReadDO, unOrder.getStoreGuid());
        // 补全折扣信息
        replenishDiscountInfo(unOrder, orderReadDO);
        
        return orderReadDO;
    }

    private String buildOrderContext(OrderReadDO orderReadDO, UnOrder unOrder) {
        String shopName = orderReadDO.getShopName();
        String platformName = getPlatformName(unOrder);
        String orderDaySn = orderReadDO.getOrderDaySn();
        String orderId = unOrder.getOrderId();
        return orderContext(shopName, platformName, orderDaySn, orderId);
    }

    private boolean isValidOrderStatus(OrderReadDO orderReadDO) {
        if (!Integer.valueOf(OrderType.TakeoutSubType.OWN_TAKEOUT.getType())
                .equals(orderReadDO.getOrderSubType()) &&
                !Integer.valueOf(OrderType.TakeoutSubType.TCD_TAKEOUT.getType())
                        .equals(orderReadDO.getOrderSubType())) {
            return orderReadDO.getOrderStatus() == OrderStatus.TO_DO;
        }
        return true;
    }

    private void updateOrderStatus(OrderReadDO orderReadDO, UnOrder unOrder, String orderContext) {
        OrderDO orderUpdate = new OrderDO();
        orderUpdate.setOrderStatus(OrderStatus.TO_SHIP);
        orderUpdate.setAcceptTime(unOrder.getAcceptTime());
        
        Boolean isYiChengDelivery = isYiChengDelivery(orderReadDO);
        log.info("{}是否接入一城飞客:{}", orderContext, isYiChengDelivery);
        
        if (isYiChengDelivery) {
            orderUpdate.setDeliveryType(1);
        }
        
        updateOrderAdditionalInfo(orderUpdate, unOrder, orderReadDO);
        updateById(orderUpdate.setId(orderReadDO.getId()));

        // 直接赋值刚更新的列，避免再次查询sql
        orderReadDO.setOrderStatus(OrderStatus.TO_SHIP);
        orderReadDO.setAcceptTime(unOrder.getAcceptTime());
        if (!StringUtils.isEmpty(orderUpdate.getAcceptStaffName())) {
            orderReadDO.setAcceptStaffName(orderUpdate.getAcceptStaffName());
        }

        // 订单日志
        OrderDO orderInDb = orderMapstruct.readToDO(orderReadDO);
        logService.save(takeoutLogFactory.createOrderAccepted(orderInDb));
    }

    private void updateOrderAdditionalInfo(OrderDO orderUpdate, UnOrder unOrder, OrderReadDO orderReadDO) {
        if (!StringUtils.isEmpty(unOrder.getWriteOffCode()) && StringUtils.isEmpty(orderReadDO.getWriteOffCode())) {
            orderUpdate.setWriteOffCode(unOrder.getWriteOffCode());
        }
        if (!StringUtils.isEmpty(unOrder.getOrderStatusTcd())) {
            orderUpdate.setOrderStatusTcd(unOrder.getOrderStatusTcd());
        }
        
        if (StringUtils.isEmpty(orderReadDO.getAcceptStaffGuid())
                || StringUtils.isEmpty(orderReadDO.getAcceptStaffName())) {
            orderUpdate.setAcceptStaffName(getAcceptStaffName(orderReadDO.getOrderSubType()));
        }
    }

    private String getAcceptStaffName(Integer orderSubType) {
        if (OrderType.TakeoutSubType.MT_TAKEOUT.getTypeInteger().equals(orderSubType)) {
            return BUSINESS_MT;
        } else if (OrderType.TakeoutSubType.ELE_TAKEOUT.getTypeInteger().equals(orderSubType)) {
            return BUSINESS_ELM;
        } else if (OrderType.TakeoutSubType.OWN_TAKEOUT.getTypeInteger().equals(orderSubType)) {
            return BUSINESS_OWN;
        } else if (OrderType.TakeoutSubType.TCD_TAKEOUT.getTypeInteger().equals(orderSubType)) {
            return BUSINESS_TCD;
        }   else if (OrderType.TakeoutSubType.JD_TAKEOUT.getTypeInteger().equals(orderSubType)) {
            return BUSINESS_JD;
        }
        return "";
    }

    private void processOrderAfterAccept(OrderReadDO orderReadDO, UnOrder unOrder, String orderContext) {
        OrderContextUtils.setOrderContext(orderContext);

        // 通吃岛特殊流程处理
        if (OrderType.TakeoutSubType.TCD_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            if (!unOrder.getPrint()) {
                log.info("orderId:{},return", unOrder.getOrderId());
                return;
            }
        }

        // 处理SKU信息
        Map<String, SkuTakeawayInfoRespDTO> skuInfoMap = processSkuInfo(orderReadDO);
        
        // 打印相关处理
        printService.printAll(orderReadDO, unOrder.getBusinessType(), skuInfoMap, unOrder.getArrayOfUnItem());
        
        // KDS备菜
        kdsService.prepare(getPlatformName(unOrder), orderReadDO, skuInfoMap);
        
        // 处理ERP相关
        processErpRelated(orderReadDO);
        
        // 自动接单移除队列
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        orderDO.setOrderGuid(orderReadDO.getOrderGuid());
        autoAcceptOrderService.removeOrder(orderDO);
        
        // 新订单消息推送
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderConfirmed(orderMapstruct.readToDO(orderReadDO)));
        
        // 处理配送相关
        processDelivery(orderReadDO, unOrder, orderContext);
    }

    private Map<String, SkuTakeawayInfoRespDTO> processSkuInfo(OrderReadDO orderReadDO) {
        Map<String, SkuTakeawayInfoRespDTO> skuInfoMap = mapOfSkuTakeawayInfoRespDTO(orderReadDO);
        Map<String, String> parentSkuGuidMapping = skuInfoMap.values().stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getParentSkuGuid, SkuTakeawayInfoRespDTO::getSkuGuid));
                
        for (ItemDO item : orderReadDO.getArrayOfItem()) {
            if (!skuInfoMap.containsKey(item.getItemSku())) {
                String skuGuid = parentSkuGuidMapping.getOrDefault(item.getItemSku(), item.getItemSku());
                item.setItemSku(skuGuid);
            }
        }
        
        return skuInfoMap;
    }

    private void processErpRelated(OrderReadDO orderReadDO) {
        String jsonStr = UserContextUtils.getJsonStr();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        String traceid = TraceidUtils.getTraceid();
        String orderContext = OrderContextUtils.getOrderContext();
        stockErpThreadPool.execute(() -> {
            UserContextUtils.put(jsonStr);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            TraceidUtils.setTraceid(traceid);
            OrderContextUtils.setOrderContext(orderContext);

            Map<String, SkuTakeawayInfoRespDTO> skuInfoMap = mapOfSkuTakeawayInfoRespDTOV2(orderReadDO);
            List<Long> clearErpItemSkuGuidList = new ArrayList<>();

            for (ItemDO item : orderReadDO.getArrayOfItem()) {
                if (StringUtils.isEmpty(item.getErpItemSkuGuid())) {
                    continue;
                }
                item.setItemSku(item.getErpItemSkuGuid());

                if (!skuInfoMap.containsKey(item.getErpItemSkuGuid())) {
                    clearErpItemSkuGuidList.add(item.getId());
                }
            }

            if (CollectionUtils.isNotEmpty(clearErpItemSkuGuidList)) {
                itemService.clearErpItemSkuGuid(clearErpItemSkuGuidList);
                abnormalDataHandler(orderReadDO.getArrayOfItem(), clearErpItemSkuGuidList);
            }

            // 扣减简易库存
            erpService.reduceStockForOrder(orderReadDO, skuInfoMap);
            // 扣减MDM库存
            PushOrderBillsBO pushOrderBillsBO = erpService.reduceStockForOrderMDM(orderReadDO, skuInfoMap);
            // 扣减微海库存
            erpService.reduceStockForOrderWeihaiErp(pushOrderBillsBO);
        });
    }

    private void processDelivery(OrderReadDO orderReadDO, UnOrder unOrder, String orderContext) {
        Boolean isYiChengDelivery = isYiChengDelivery(orderReadDO);
        if (!isYiChengDelivery) {
            return;
        }

        String platformName = getPlatformName(unOrder);
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        
        if (orderDO.getOrderSubType() == 0 || orderDO.getOrderSubType() == 6) {
            // 美团或赚餐
            log.info("{}调用一城飞客", orderContext);
            UnOrder data = createUnOrder(platformName, orderDO, unOrder, orderReadDO);
            deliveryService.doDelivery(data);
        } else if (orderDO.getOrderSubType() == 1) {
            // 饿了么
            log.info("{}查询订单合同生效类型入参:storeGuid={}", orderContext, orderReadDO.getStoreGuid());
            unOrder.setStoreGuid(orderReadDO.getStoreGuid());
            String contractTypeName = producerFeignClient.getEffectServicePackContract(unOrder);
            log.info("{}订单查询合同类型名称:{}", orderContext, contractTypeName);
            
            UnOrder data = createUnOrder(platformName, orderDO, unOrder, orderReadDO);
            deliveryService.doDelivery(data);
        }
    }

    private void replenishDiscountInfo(UnOrder unOrder, OrderReadDO orderReadDO) {
        List<UnDiscount> arrayOfUnDiscount = unOrder.getArrayOfUnDiscount();
        if (!org.springframework.util.CollectionUtils.isEmpty(arrayOfUnDiscount)) {
            List<String> strings = arrayOfUnDiscount.stream()
                    .filter(unDiscount -> !ObjectUtils.isEmpty(unDiscount.getType()) && unDiscount.getType() == 5)
                    .map(UnDiscount::getDiscountRemark)
                    .collect(Collectors.toList());
            if (!org.springframework.util.CollectionUtils.isEmpty(strings)) {
                String discountRemark = String.join(",", strings);
                orderReadDO.setFullGiftRemark(discountRemark);
            }
        }
    }

    /**
     * 生成用于一城飞客的Unorder，组装菜品数据
     *
     * @param orderDO
     * @param unOrder
     * @return
     */
    public UnOrder createUnOrder(String platformName, OrderDO orderDO, UnOrder unOrder, OrderReadDO orderReadDO) {
        String orderId = orderDO.getOrderId();
        UnOrder data = orderMapstruct.toUnOrder(orderReadDO);

        data.setReplyMsgType(UnOrderReplyMsgType.START_DELIVERY_ORDER);
        data.setAcceptTime(unOrder.getAcceptTime());
        data.setOrderType(unOrder.getOrderType());
        data.setOrderSubType(unOrder.getOrderSubType());
        data.setAcceptTime(unOrder.getAcceptTime());
        data.setDistributionType(unOrder.getDistributionType());
        log.info("({})订单[{}]生成一城飞客unOrder参数打印:shopName={},reserve={},estimateDeliveredTime={}," +
                        "orderDaySn={},packageTotal={},invoiced={}," +
                        "invoiceType={},invoiceTitle={},taxpayerId={},discountTotal={},enterpriseTotal={},platformTotal={}",
                platformName, orderId, orderReadDO.getShopName(), orderReadDO.getReserve(), orderReadDO.getEstimateDeliveredTime(),
                orderReadDO.getOrderDaySn(), orderReadDO.getPackageTotal(), orderReadDO.getInvoiced(),
                orderReadDO.getInvoiceType(), orderReadDO.getInvoiceTitle(), orderReadDO.getTaxpayerId(),
                orderReadDO.getDiscountTotal(), orderReadDO.getEnterpriseDiscount(), orderReadDO.getPlatformDiscount());

        data.setShopName(orderReadDO.getShopName());
        data.setReserve(orderReadDO.getReserve());
        data.setEstimateDeliveredTime(orderReadDO.getEstimateDeliveredTime());
        data.setOrderDaySn(orderReadDO.getOrderDaySn());
        data.setPackageTotal(orderReadDO.getPackageTotal());
        data.setInvoiced(orderReadDO.getInvoiced());
        data.setInvoiceTitle(orderReadDO.getInvoiceTitle());
        data.setTaxpayerId(orderReadDO.getTaxpayerId());

        List<FoodListDTO> foodLists = new ArrayList<>();
        for (ItemDO itemDO : orderReadDO.getArrayOfItem()) {
            FoodListDTO foodListDTO = new FoodListDTO();
            foodListDTO.setFoodName(itemDO.getItemName());
            foodListDTO.setFoodPrice(itemDO.getItemPrice().doubleValue());
            foodListDTO.setFoodCount(itemDO.getItemCount().intValue());
            foodLists.add(foodListDTO);
        }
        log.info("({})订单[{}]一城飞客foodLists参数打印:foodLists={}", platformName, orderId, foodLists);
        data.setFoodLists(foodLists);
        data.setCurRetryTime(1);
        data.setMaxRetryTimes(6);
        data.setDelayTimeLevel(0);
        data.setIsEnableBackOff(true);
        return data;
    }

    private Boolean isYiChengDelivery(OrderReadDO orderReadDO) {
        // 判断商家是否确实接入了一城飞客
        log.info("判断是否接入一城飞客,orderId={},shopId={},shopName={}," +
                        "orderGuid={},orderStatus={},orderSubType={}," +
                        "enterpriseName={},enterpriseGuid={},storeGuid={}," +
                        "orderDaySn={},reserve={},shipLatitude={}," +
                        "shipLongitude={},thirdShipper={},deliveryType={}," +
                        "customerAddress={},createTime={},activeTime={}",
                orderReadDO.getOrderId(), orderReadDO.getShopId(), orderReadDO.getShopName(),
                orderReadDO.getOrderGuid(), orderReadDO.getOrderStatus(), orderReadDO.getOrderSubType(),
                orderReadDO.getEnterpriseName(), orderReadDO.getEnterpriseGuid(), orderReadDO.getStoreGuid(),
                orderReadDO.getOrderDaySn(), orderReadDO.getReserve(), orderReadDO.getShipLatitude(),
                orderReadDO.getShipLongitude(), orderReadDO.getThirdShipper(), orderReadDO.getDeliveryType(),
                orderReadDO.getCustomerAddress(), orderReadDO.getCreateTime(), orderReadDO.getActiveTime());

        //如果客户收货地址包含到店自取，则不调用一城飞客
        // 如果客户收获地址包含 【固定地址：】,则不调用一城飞客
        String shopYourself = "到店自取";
        String FIXED_ADDRESS = "【固定地址】";
        if (orderReadDO.getCustomerAddress() != null && (orderReadDO.getCustomerAddress().contains(shopYourself)
                || orderReadDO.getCustomerAddress().contains(FIXED_ADDRESS))) {
            return Boolean.FALSE;
        }

        StoreAuthDTO param = new StoreAuthDTO();
        param.setStoreGuid(orderReadDO.getStoreGuid());
        StoreAuthDTO storeAuthDTO;
        if (orderReadDO.getOrderSubType() == 0) {
            storeAuthDTO = producerFeignClient.getMtTakeoutAuth(param);
            log.info("(美团)订单[{}]门店配送方式查询返回:{}", orderReadDO.getOrderId(), JacksonUtils.writeValueAsString(storeAuthDTO));
        } else if (orderReadDO.getOrderSubType() == 1) {
            storeAuthDTO = producerFeignClient.getEleTakeoutAuth(param);
            log.info("(饿了么)订单[{}]门店配送方式查询返回:{}", orderReadDO.getOrderId(), JacksonUtils.writeValueAsString(storeAuthDTO));
        } else if (orderReadDO.getOrderSubType() == 6) {
            storeAuthDTO = producerFeignClient.getTcdTakeoutAuth(param);
            log.info("(赚餐)订单[{}]门店配送方式查询返回:{}", orderReadDO.getOrderId(), JacksonUtils.writeValueAsString(storeAuthDTO));
        } else {
            storeAuthDTO = new StoreAuthDTO().setDeliveryType(-1);
        }

        if (storeAuthDTO == null || storeAuthDTO.getDeliveryType() == null) {
            return Boolean.FALSE;
        }

        if (storeAuthDTO.getDeliveryType() == 1) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    //fixme 客户先确认订单，然后骑手再送达，这里就会报空指针。阿里云环境，orderId=55768831929680656
    @Override
    public void updateShipping(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        log.info("({})订单配送中，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        OrderDO orderInDb = getOne(statusQuery(orderId));
        //fixme 解决线上空指针，后续详细研究下
/*        if (orderInDb.getOrderStatus() == OrderStatus.TO_DO
                || orderInDb.getOrderStatus() == OrderStatus.TO_SHIP) {
            orderDO.setOrderStatus(OrderStatus.SHIPPING);
            orderDO.setDeliveryTime(DateTimeUtils.now());
        }*/
        if (orderInDb.getOrderStatus() != OrderStatus.SHIPPING) {
            orderDO.setOrderStatus(OrderStatus.SHIPPING);
        }
        if (orderDO.getDeliveryTime() == null) {
            orderDO.setDeliveryTime(DateTimeUtils.now());
        }
        update(orderDO, wrapperByOrderId(orderId));
        // 订单日志，消息推送
        OrderDO orderNewInDb = getOne(wrapperByOrderId(orderId));
        logService.save(takeoutLogFactory.createOrderShipping(orderNewInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderShipping(orderNewInDb));
        log.info("({})订单配送中，orderId: {}，处理完毕", getPlatformName(unOrder), orderId);
    }

    @Override
    public void updateShippingCompleted(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        log.info("({})订单配送完成，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        OrderDO orderInDb = getOne(statusQuery(orderId));
        //fixme 解决线上空指针，后续详细研究下
//        if (orderInDb.getOrderStatus() == OrderStatus.SHIPPING
//                || orderInDb.getOrderStatus() == OrderStatus.TO_SHIP) {
//            orderDO.setOrderStatus(OrderStatus.SHIP_FINISHED);
//        }
        if (orderInDb.getOrderStatus() != OrderStatus.SHIP_FINISHED && orderInDb.getOrderStatus() != OrderStatus.FINISHED) {
            orderDO.setOrderStatus(OrderStatus.SHIP_FINISHED);
        }
        if (orderInDb.getOrderStatus() == OrderStatus.FINISHED) {
            orderDO.setOrderStatus(OrderStatus.FINISHED);
        }
        if (orderDO.getDeliveredTime() == null) {
            orderDO.setDeliveredTime(DateTimeUtils.now());
        }
        update(orderDO, wrapperByOrderId(orderId));
        // 订单日志，消息推送
        OrderDO orderNewInDb = getOne(wrapperByOrderId(orderId));
        logService.save(takeoutLogFactory.createOrderShippingCompleted(orderNewInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderFinished(orderNewInDb));
        log.info("({})订单配送完成，orderId: {}，处理完毕", getPlatformName(unOrder), orderId);
    }

    @Override
    public void updateShippingDistribute(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        log.info("({})订单被骑手接单，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        //目前只有饿了么才会有骑手接单的状态
        if (OrderType.TakeoutSubType.ELE_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            OrderDO orderInDb = getOne(statusQuery(orderId));
            if (orderInDb.getOrderStatus() == OrderStatus.TO_DO || orderInDb.getOrderStatus() == OrderStatus.TO_SHIP) {
                orderDO.setShipperName(unOrder.getShipperName());
                orderDO.setShipperPhone(unOrder.getShipperPhone());
                update(orderDO, wrapperByOrderId(orderId));
                // 订单日志，消息推送
                OrderDO orderNewInDb = getOne(wrapperByOrderId(orderId));
                logService.save(takeoutLogFactory.createOrderShippingDistribute(orderNewInDb));
                log.info("({})订单被骑手接单，orderId: {}，处理完毕", getPlatformName(unOrder), orderId);
            }
        }
    }

    @Override
    public String goShipping(TakeoutOrderDTO takeoutOrderDTO) {
        return producerFeignClient.goShipping(takeoutOrderDTO);
    }

    @Override
    public String doneShipping(TakeoutOrderDTO takeoutOrderDTO) {
        return producerFeignClient.doneShipping(takeoutOrderDTO);
    }

    @Override
    public String cancelShipping(TakeoutOrderDTO takeoutOrderDTO) {
        return producerFeignClient.cancelShipping(takeoutOrderDTO);
    }

    /**
     * fixme 不要为自营外卖单独创建这个方法，需要合并到统一的方法中，这样子，下游逻辑才是统一的
     * fixme Callback触发需要提到producer中去
     *
     * @param salesUpdateDTO
     * @return
     */
    @Override
    public OwnCallbackResponse orderUpdate(SalesUpdateDTO salesUpdateDTO) {
        try {
            OrderDO orderInDb = getOne(new LambdaQueryWrapper<OrderDO>()
                    .eq(OrderDO::getOrderId, salesUpdateDTO.getOrderID()));
            if (orderInDb == null) {
                return OwnCallbackResponse.PARAMETER_ERROR;
            }

            //接单
            if (salesUpdateDTO.getOrderStatus() == OwnTypeEnum.待配送.getType()) {
                orderInDb.setOrderStatus(OrderStatus.TO_SHIP);
                orderInDb.setAcceptTime(DateTimeUtils.now());
                update(orderInDb, wrapperByOrderId(String.valueOf(salesUpdateDTO.getOrderID())));
                log.info("订单状态变化，接单，orderInDb={}", orderInDb);
                //拒单
            } else if (salesUpdateDTO.getOrderStatus() == OwnTypeEnum.已拒单.getType()) {
                orderInDb.setOrderStatus(OrderStatus.CANCELED);
                orderInDb.setCancelTime(DateTimeUtils.now());
                update(orderInDb, wrapperByOrderId(String.valueOf(salesUpdateDTO.getOrderID())));
                log.info("订单状态变化，拒单，orderInDb={}", orderInDb);

                //配送中
            } else if (salesUpdateDTO.getOrderStatus() == OwnTypeEnum.配送中.getType()) {
                orderInDb.setOrderStatus(OrderStatus.SHIPPING);
                orderInDb.setDeliveryTime(DateTimeUtils.now());
                update(orderInDb, wrapperByOrderId(String.valueOf(salesUpdateDTO.getOrderID())));
                logService.save(takeoutLogFactory.createOrderShipping(orderInDb));
                log.info("订单状态变化，发起配送，orderInDb={}", orderInDb);

                //配送完成
            } else if (salesUpdateDTO.getOrderStatus() == OwnTypeEnum.订单完成.getType()) {
                orderInDb.setOrderStatus(OrderStatus.FINISHED);
                orderInDb.setCompleteTime(DateTimeUtils.now());
                update(orderInDb, wrapperByOrderId(String.valueOf(salesUpdateDTO.getOrderID())));
                logService.save(takeoutLogFactory.createOrderFinished(orderInDb));
                log.info("订单状态变化，配送完成，orderInDb={}", orderInDb);

                //取消配送
            } else if (salesUpdateDTO.getOrderStatus() == OwnTypeEnum.已取消.getType()) {
                orderInDb.setOrderStatus(OrderStatus.CANCELED);
                orderInDb.setCancelTime(DateTimeUtils.now());
                update(orderInDb, wrapperByOrderId(String.valueOf(salesUpdateDTO.getOrderID())));
                logService.save(takeoutLogFactory.createOrderCanceled(orderInDb));
                log.info("订单状态变化，已取消，orderInDb={}", orderInDb);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.info("【自营外卖平台】更新订单状态异常，message={}", e.getMessage());
            return OwnCallbackResponse.DO_ERROR;
        }
        return OwnCallbackResponse.SUCCESS;
    }

    /**
     * fixme 不要单独建这个方法，在通用getOrder方法前切换数据源就可以了
     *
     * @param OrderId
     * @return
     */
    @Override
    public OrderDO getOrderForHeShi(String OrderId) {
        //fixme 上线前，需要将这里手动设置为何师烧烤的enterpriseGuid
        if (log.isInfoEnabled()) {
            log.info("根据enterpriseGuid({})切换数据源", enterpriseGuidForHeShi);
        }
        dynamicHelper.changeDatasource(enterpriseGuidForHeShi);
        OrderDO orderInDb = getOne(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getOrderId, OrderId));
        return orderInDb;
    }

    @Override
    public Page<BusinessTakeoutOrderRespDTO> getTakeoutOrderPage(BusinessTakeoutOrderReqDTO reqDTO) {
        int count = baseMapper.getTakeoutOrderCount(reqDTO);
        if (count == 0) {
            return new Page<>(reqDTO.getCurrentPage(), reqDTO.getPageSize());
        }
        List<OrderReadDO> orderList = baseMapper.getTakeoutOrderList(reqDTO);
        List<BusinessTakeoutOrderRespDTO> businessTakeoutOrderRespList = conversionReadDO(orderList);
        return new Page<>(reqDTO.getCurrentPage(), reqDTO.getPageSize(), count, businessTakeoutOrderRespList);
    }

    @Override
    public BusinessTakeoutOrderDetailRespDTO getTakeoutOrderDetail(String orderGuid) {
        OrderDO orderDO = this.getOne(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getOrderGuid, orderGuid));
        if (orderDO == null) {
            throw new BusinessException("不存在的订单:orderGuid=" + orderGuid);
        }
        BusinessTakeoutOrderDetailRespDTO response = orderMapstruct.do2BusinessTakeoutOrderDTO(orderDO);

        response.setDeliveryTime((orderDO.getEstimateDeliveredTime() == null
                || DateTimeUtils.localDateTime2Mills(orderDO.getEstimateDeliveredTime()) == 0)
                ? "立即送达" : DateTimeUtils.localDateTime2String(orderDO.getEstimateDeliveredTime()));
        response.setPhoneNo(explainPhoneNo(orderDO.getOrderSubType(), orderDO.getCustomerPhone(), orderDO.getPrivacyPhone()));
        response.setOrderState(explainOrderStatus(orderDO.getOrderStatus(), orderDO.getOrderTagStatus()));
        response.setOrderSource(OrderType.TakeoutSubType.ofType(orderDO.getOrderSubType()).getSource());

        if (orderDO.getRefundSuccess()) {
            response.setRefundStateDesc("同意");
            response.setRefundReplyTime(Optional.ofNullable(response.getRefundReplyTime()).orElse(response.getRefundReqTime()));
        }

        List<ItemDO> items = itemService.getItemList(orderGuid);
        //商品映射数量回滚
//        mappingService.rollbackOrderItemCount(items, orderDO.getStoreGuid(), orderDO.getOrderSubType());
        response.setItemInfoList(itemService.getBusinessTakeoutOrderItemList(items));

        if (orderDO.getRefundSuccess()) {
            response.setRefundStateDesc("同意");
            response.setRefundReplyTime(Optional.ofNullable(response.getRefundReplyTime()).orElse(response.getRefundReqTime()));
        }

        return response;
    }

    /**
     * mapstruct不满足实际需求，这里自己进行转换
     */
    private List<BusinessTakeoutOrderRespDTO> conversionReadDO(List<OrderReadDO> readDOList) {
        if (readDOList == null) {
            return Collections.emptyList();
        }
        List<BusinessTakeoutOrderRespDTO> toList = new ArrayList<>(readDOList.size());
        for (OrderReadDO orderReadDO : readDOList) {
            BusinessTakeoutOrderRespDTO respDTO = new BusinessTakeoutOrderRespDTO();
            respDTO.setStoreName(orderReadDO.getStoreName());
            respDTO.setTotal(orderReadDO.getTotal());
            respDTO.setAcceptStaffName(orderReadDO.getAcceptStaffName());
            respDTO.setAcceptTime(orderReadDO.getAcceptTime());
            respDTO.setCustomerActualPay(orderReadDO.getCustomerActualPay());
            respDTO.setGuid(orderReadDO.getOrderGuid());
            respDTO.setItemCount(orderReadDO.getItemCount());
            respDTO.setOrderNo(orderReadDO.getOrderId());
            respDTO.setCustomerName(orderReadDO.getCustomerName());
            respDTO.setOrderSource(OrderType.TakeoutSubType.ofType(orderReadDO.getOrderSubType()).getSource());
            respDTO.setOrderState(explainOrderStatus(orderReadDO.getOrderStatus(), orderReadDO.getOrderTagStatus()));
            respDTO.setCustomerPhone(explainPhoneNo(orderReadDO.getOrderSubType(), orderReadDO.getCustomerPhone(), orderReadDO.getPrivacyPhone()));
            toList.add(respDTO);
        }
        readDOList.clear();
        return toList;
    }

    /**
     * 处理手机号
     *
     * @param orderSubType  订单子类型
     * @param customerPhone 顾客手机号
     * @param privacyPhone  顾客隐私号
     * @return 手机号
     */
    private String explainPhoneNo(Integer orderSubType, String customerPhone, String privacyPhone) {
        if (orderSubType == 0) {
            return dealOrderPhone(customerPhone);
        } else if (orderSubType == 1) {
            return dealOrderPhone(privacyPhone);
        }
        if (!ObjectUtils.isEmpty(customerPhone) && customerPhone.startsWith("[")) {
            List<String> customerLists = JacksonUtils.toObjectList(String.class, customerPhone);
            return String.join(", ", customerLists);
        }
        return customerPhone;
    }

    private String dealOrderPhone(String phone) {
        if (!ObjectUtils.isEmpty(phone)) {
            if (!phone.startsWith("[")) {
                return phone;
            }
            List<String> customerLists = JacksonUtils.toObjectList(String.class, phone);
            if (!ObjectUtils.isEmpty(customerLists)) {
                StringBuilder str = new StringBuilder();
                for (String customerList : customerLists) {
                    str.append(customerList.replaceAll("&", ",")).append("\n");
                }
                return str.toString().substring(0, str.lastIndexOf("\n"));
            }
        }
        return "";
    }

    private String explainOrderStatus(Integer orderStatus, Integer orderTagStatus) {

        if (Objects.isNull(orderStatus) || !Objects.equals(OrderTagStatus.NORMAL, orderTagStatus)) {
            return "异常单";
        }
        String desc = "";
        switch (orderStatus) {
            case OrderStatus.CANCELED:
                desc = "已取消";
                break;
            case OrderStatus.TO_DO:
                desc = "待处理";
                break;
            case OrderStatus.TO_SHIP:
                desc = "待配送";
                break;
            case OrderStatus.SHIPPING:
                desc = "配送中";
                break;
            case OrderStatus.FINISHED:
                desc = "已完成";
                break;
            default:
                desc = "未知状态";
                break;
        }
        return desc;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(TakeoutOrderDTO takeoutOrderDTO) {
        OrderDO orderInDb = getOne(wrapperByOrderGuid(takeoutOrderDTO.getOrderGuid()));
        log.info("({})商户取消订单，orderId: {},deliveryType:{}", getPlatformName(orderInDb), orderInDb.getOrderId(), orderInDb.getDeliveryType());
        Integer orderStatus = orderInDb.getOrderStatus();
        if (orderStatus == OrderStatus.CANCELED) {
            throw new BusinessException("该订单已取消");
        } else {
            // 主动取消订单，饿了么会继续推送订单取消消息，而美团则不会（但ReplyService中模拟了回复，所以这里按常规处理）
            OrderDO orderDO = new OrderDO();
            orderDO.setCancelStaffGuid(UserContextUtils.getUserGuid());
            orderDO.setCancelStaffName(UserContextUtils.getUserName());
            //如果是一城飞客配送的异常单，点击了取消按钮，则去掉异常单标识（bug：17727）
            if (!ObjectUtils.isEmpty(orderInDb) && orderInDb.getDeliveryType() != null && orderInDb.getDeliveryType() == 1) {
                orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
            }
            orderDO.setCancelAsReject(StringUtils.isEmpty(orderInDb.getAcceptStaffName()));
            orderDO.setCancelReason(takeoutOrderDTO.getCancelReason());
            orderDO.setOrderStatus(OrderStatus.CANCELED);
            updateById(orderDO.setId(orderInDb.getId()));
            UnOrder unOrder = orderMapstruct.toUnOrder(orderInDb);
            unOrder.setCancelReason(takeoutOrderDTO.getCancelReason());
            unOrder.setReplyMsgType(UnOrderReplyMsgType.CANCEL_ORDER);
            unOrder.setOperateStaffName(UserContextUtils.getUserName());
            //赚餐端商家接单后，待配送状态下，直接取消订单.因为接单和接单后退单都是从这里走的，所以针对赚餐，要做特殊调整，其他平台不变
            if ((Objects.equals(orderInDb.getOrderSubType(), OrderType.TakeoutSubType.TCD_TAKEOUT.getTypeInteger())
                    || Objects.equals(orderInDb.getOrderSubType(), OrderType.TakeoutSubType.JD_TAKEOUT.getTypeInteger()))
                    && orderInDb.getOrderStatus() == OrderStatus.TO_SHIP) {
                unOrder.setReplyMsgType(UnOrderReplyMsgType.AGREE_REFUND_ORDER);
            }
            producerFeignClient.reply(unOrder);
        }

        // 自动接单移除队列
        autoAcceptOrderService.removeOrder(orderInDb);
    }

    @Override
    public void orderCanceledAsReject(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));
        log.info("({})订单已取消(商家拒绝)，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        orderDO.setCancelAsReject(true);
        orderDO.setOrderStatus(OrderStatus.CANCELED);
        fillCancelReasonAndStaffName(unOrder, orderDO, orderInDb);
        if (Objects.isNull(orderInDb)) {
            return;
        }
        if (update(orderDO, wrapperByOrderId(orderId))) {
            // 更新退单数量
            itemService.updateRefundCountByOrderGuid(orderInDb.getOrderGuid());
            // 更新订单退款状态
            updateOrderRefundStatus(orderInDb.getOrderGuid());
            // 直接赋值刚更新的列，避免再次查询sql
            copyFieldToOrderInDbDirectly(orderDO, orderInDb);
            // 订单日志，消息推送
            logService.save(takeoutLogFactory.createOrderCanceledAsReject(orderInDb));
            bizMsgFeignClient.msg(takeoutMsgFactory.createOrderCanceled(orderInDb));
        } else {
            log.error("订单取消推送先于新订单推送，将在消息重新消费时处理该推送");
            throw new BusinessException("订单取消推送先于新订单推送，将在消息重新消费时处理该推送");
        }
        log.info("({})订单已取消(商家拒绝)，orderId: {}，处理完毕", getPlatformName(unOrder), orderId);
    }

    @Override
    public void orderCanceled(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));
        if (Objects.isNull(orderInDb)) {
            return;
        }
        String platformName = getPlatformName(unOrder);
        OrderReadDO orderReadDO = baseMapper.getOrderDetail(orderDO);
        log.info("({})订单已取消，orderId: {}，处理中 orderReadDO={}", platformName, orderId,
                JacksonUtils.writeValueAsString(orderReadDO));

        orderDO.setCancelAsReject(false);
        orderDO.setOrderStatus(OrderStatus.CANCELED);
        orderReadDO.setCancelAsReject(orderDO.getCancelAsReject());
        orderReadDO.setOrderStatus(orderDO.getOrderStatus());
        fillCancelReasonAndStaffName(unOrder, orderDO, orderInDb);

        if (update(orderDO, wrapperByOrderId(orderId))) {
            // 更新退菜表状态
            refundItemService.updateRefundSuccess(orderInDb.getOrderGuid());
            // 更新退单数量
            itemService.updateRefundCountByOrderGuid(orderInDb.getOrderGuid());
            // 更新订单退款状态
            updateOrderRefundStatus(orderInDb.getOrderGuid());
            // 直接赋值刚更新的列，避免再次查询sql
            copyFieldToOrderInDbDirectly(orderDO, orderInDb);
            //对接库存
            Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = mapOfSkuTakeawayInfoRespDTOV2(orderReadDO);
            List<ItemDO> itemList = orderReadDO.getArrayOfItem();

            for (ItemDO t : itemList) {
                t.setItemSku(t.getErpItemSkuGuid());
            }
            String jsonStr = UserContextUtils.getJsonStr();
            String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
            String traceid = TraceidUtils.getTraceid();
            String orderContext = OrderContextUtils.getOrderContext();
            stockErpThreadPool.execute(() -> {
                UserContextUtils.put(jsonStr);
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
                TraceidUtils.setTraceid(traceid);
                OrderContextUtils.setOrderContext(orderContext);
                try {
                    erpService.addStockForOrderMDM(orderReadDO, mapOfSkuTakeawayInfoRespDTO);
                } catch (Exception e) {
                    log.error("({})订单 {} MDM库存增加失败：{}", platformName, orderId, ThrowableExtUtils.asStringIfAbsent(e));
                }
            });
            // 外卖取消 打印取消单
            printService.printCancelBill(orderReadDO);
            // 订单日志，消息推送
            log.info("订单日志，消息推送:orderInDb={}", orderInDb);
            logService.save(takeoutLogFactory.createOrderCanceled(orderInDb));
            bizMsgFeignClient.msg(takeoutMsgFactory.createOrderCanceled(orderInDb));
            kdsService.refund(orderReadDO, mapOfSkuTakeawayInfoRespDTO);
        } else {
            log.error("订单取消推送先于新订单推送，将在消息重新消费时处理该推送");
            throw new BusinessException("订单取消推送先于新订单推送，将在消息重新消费时处理该推送");
        }

        //加入取消配送
        log.info("({})订单[{}]订单取消：deliveryType={}", platformName, orderId, orderInDb.getDeliveryType());
        if (!ObjectUtils.isEmpty(orderInDb.getDeliveryType()) && orderInDb.getDeliveryType() == 1) {
            log.info("orderInDb.getCancelReason()={},orderDO.getCancelReason()={}"
                    , orderInDb.getCancelReason(), orderDO.getCancelReason());
            if (!StringUtils.isEmpty(orderInDb.getCancelReason())) {
                unOrder.setCancelReason(orderInDb.getCancelReason());
            } else if (!StringUtils.isEmpty(orderDO.getCancelReason())) {
                unOrder.setCancelReason(orderDO.getCancelReason());
            } else {
                unOrder.setCancelReason("申请取消");
            }
            unOrder.setReplyMsgType(UnOrderReplyMsgType.CANCEL_DELIVERY_ORDER);
            producerFeignClient.reply(unOrder);
        }

        // 自动接单移除队列
        autoAcceptOrderService.removeOrder(orderDO);
        log.info("({})订单已取消，orderId: {}，处理完毕", getPlatformName(unOrder), orderId);
    }

    @Override
    public void cancelOrderReq(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        log.info("({})用户申请取消订单，orderId: {}，处理中", getPlatformName(unOrder), orderDO.getOrderId());
        // 将cancelReqReason中的值Copy进cancelReason，因为饿了么无明确reason
        if (StringUtils.isEmpty(orderDO.getCancelReason())) {
            orderDO.setCancelReason(orderDO.getCancelReqReason());
        }
        orderDO.setCancelReqExpireTime(orderDO.getCancelReqTime().plusMinutes(15));
        orderDO.setOrderTagStatus(OrderTagStatus.CANCEL_REQ);
        update(orderDO, wrapperByOrderId(orderId));
        // 订单日志，消息推送
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));
        logService.save(takeoutLogFactory.createOrderCancelReq(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderCancelReq(orderDO));

        // 自动接单移除队列
        autoAcceptOrderService.removeOrder(orderDO);
        log.info("({})用户申请取消订单，orderId: {}，处理完毕", getPlatformName(unOrder), orderDO.getOrderId());
    }

    @Override
    public void cancelCancelOrderReq(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        log.info("({})用户申请取消取消订单请求，orderId: {}，处理中", getPlatformName(unOrder), orderDO.getOrderId());
        orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
        update(orderDO, wrapperByOrderId(orderId));
        // 订单日志，消息推送
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));
        logService.save(takeoutLogFactory.createOrderCancelCancelReq(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderCancelCancelReq(orderDO));

        // 自动接单移除队列
        autoAcceptOrderService.removeOrder(orderDO);
        log.info("({})用户申请取消取消订单请求，orderId: {}，处理完毕", getPlatformName(unOrder), orderDO.getOrderId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String agreeCancelReq(TakeoutOrderDTO takeoutOrderDTO) {
        return replyCancelReq(takeoutOrderDTO, true);
    }

    @Override
    public void cancelReqAgreed(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        log.info("({})商户同意取消订单，orderId: {}，处理中", getPlatformName(unOrder), orderDO.getOrderId());
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));
        // 饿了么会紧接着推送订单已取消消息，所以这里无需处理orderStatus，只需复位orderTagStatus
        orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
        if (StringUtils.isEmpty(orderInDb.getCancelReplyStaffGuid())) {
            orderDO.setCancelReplyStaffName(getAcceptStaffName(orderInDb.getOrderSubType()));
        }
        orderDO.setCancelStaffGuid(orderDO.getCancelReplyStaffGuid());
        orderDO.setCancelStaffName(orderDO.getCancelReplyStaffName());
        update(orderDO, wrapperByOrderId(orderId));
        // 直接赋值刚更新的列，避免再次查询sql
        if (StringUtils.isEmpty(orderInDb.getCancelReplyStaffName())) {
            orderInDb.setCancelReplyStaffName(orderDO.getCancelReplyStaffName());
        }
        orderInDb.setCancelStaffGuid(orderDO.getCancelStaffGuid());
        orderInDb.setCancelStaffName(orderDO.getCancelStaffName());

        // 订单日志，消息推送
        logService.save(takeoutLogFactory.createOrderCancelAgreed(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderCancelAgreed(orderDO));

        // 自动接单移除队列
        autoAcceptOrderService.removeOrder(orderDO);

        log.info("({})商户同意取消订单，orderId: {}，处理完毕", getPlatformName(unOrder), orderDO.getOrderId());
    }

    /**
     * 店家不同意取消订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String disagreeCancelReq(TakeoutOrderDTO takeoutOrderDTO) {
        return replyCancelReq(takeoutOrderDTO, false);
    }

    /**
     * 取消订单请求已被拒绝
     */
    @Override
    public void cancelReqDisagreed(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        log.info("({})商户不同意取消订单，orderId: {}，处理中", getPlatformName(unOrder), orderDO.getOrderId());
        orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));
        if (StringUtils.isEmpty(orderInDb.getCancelReplyStaffGuid())) {
            String acceptStaffName = Integer.valueOf(0).equals(orderInDb.getOrderSubType())
                    ? BUSINESS_MT : BUSINESS_ELM;
            orderDO.setCancelReplyStaffName(acceptStaffName);
        }
        update(orderDO, wrapperByOrderId(orderId));
        // 直接赋值刚更新的列，避免再次查询sql
        if (StringUtils.isEmpty(orderInDb.getCancelReplyStaffName())) {
            orderInDb.setCancelReplyStaffName(orderDO.getCancelReplyStaffName());
        }
        // 订单日志，消息推送
        logService.save(takeoutLogFactory.createOrderCancelDisagreed(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderCancelDisagreed(orderDO));
        log.info("({})商户不同意取消订单，orderId: {}，处理完毕", getPlatformName(unOrder), orderDO.getOrderId());
    }

    @Override
    public void cancelArbitrationEffective(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        log.info("({})客服仲裁取消单申请有效，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        // 未进入结算步骤，所以最终状态的确将是已取消状态；不同于退款退单
        // 不确定仲裁有效后还会不会推送cancel消息，因此在这里就标记为已取消
        String cancelStaffName = Integer.valueOf(0).equals(unOrder.getOrderSubType())
                ? "美团客服" : "饿了么客服";
        orderDO.setCancelStaffName(cancelStaffName);
        orderDO.setOrderStatus(OrderStatus.CANCELED);
        orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
        update(orderDO, wrapperByOrderId(orderId));
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));
        // 更新退单数量
        itemService.updateRefundCountByOrderGuid(orderInDb.getOrderGuid());
        // 更新订单退款状态
        updateOrderRefundStatus(orderInDb.getOrderGuid());
        // 订单日志，消息推送
        logService.save(takeoutLogFactory.createOrderCancelReqArbitrationEffective(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderCancelAgreed(orderDO));
        log.info("({})客服仲裁取消单申请有效，orderId: {}，处理完毕", getPlatformName(unOrder), orderDO.getOrderId());
    }

    @Override
    public void orderCompleted(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        log.info("({})订单已完成，orderId: {}，处理中", getPlatformName(unOrder), orderDO.getOrderId());
        OrderDO orderInDb = getOne(wrapperByOrderId(unOrder.getOrderId()));
        if (orderInDb == null) {
            log.error("orderInDb为空，orderId={}", unOrder.getOrderId());
            return;
        }
        if (OrderStatus.FINISHED == orderInDb.getOrderStatus()) {
            log.info("{}该订单已完成", orderInDb.getOrderId());
        }
        if (OrderStatus.CANCELED == orderInDb.getOrderStatus()) {
            throw new BusinessException("该订单已取消");
        }
        if (orderInDb.getDeliveredTime() == null) {
            orderDO.setDeliveredTime(orderInDb.getDeliveredTime());
        }
        orderDO.setOrderStatus(OrderStatus.FINISHED);

        update(orderDO, wrapperByOrderId(orderDO.getOrderId()));

        // 直接赋值刚更新的列，避免再次查询sql
        orderInDb.setOrderStatus(orderDO.getOrderStatus());
        orderInDb.setDeliveredTime(unOrder.getDeliveredTime());
        orderInDb.setCompleteTime(unOrder.getCompleteTime());
        //预计送达
        orderInDb.setActiveTime(orderDO.getActiveTime());

        // 订单日志、消息推送
        logService.save(takeoutLogFactory.createOrderFinished(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderFinished(orderDO));
        log.info("开始处理电信推送问题》》》》》》》》》》》》》》》》》》》》》》》》》》》");
        //判断实际送达时间与预期送达时间间隔是否大于20分钟 若大于则超时
        try {
            getOrderCallDO(orderInDb);
        } catch (Exception e) {
            log.info("电信调用失败：{}", e.getMessage());
        }


        log.info("({})订单已完成，orderId: {}，处理完毕", getPlatformName(unOrder), orderDO.getOrderId());
    }

    private void getOrderCallDO(OrderDO orderDO) {
        log.info("模板TASK_ID：{}", TASK_ID);
        log.info("订单完成多久后外呼时间DELAY_CALL：{}", DELAY_CALL);
        log.info("非预定超时时间OVERTIME_TIME：{}", OVERTIME_TIME);
        log.info("预定超时时间EXCEED_TIME：{}", EXCEED_TIME);
        if (Objects.isNull(TASK_ID) || StringUtils.isEmpty(TASK_ID)) {
            log.info("未配置模板ID");
            return;
        }
        log.info("orderDO: {}", JSONObject.toJSONString(orderDO));
        LocalDateTime deliveredTime = orderDO.getDeliveredTime() != null ? orderDO.getDeliveredTime() : DateTimeUtils.now();
        log.info("实际送达时间: {}", deliveredTime);
        log.info("订单完成时间: {}", orderDO.getCompleteTime());
        if (checkOrderCall(orderDO)) {
            return;
        }
        log.info("处理推送电信订单号: {}", JSONObject.toJSONString(orderDO));
        OrderCallDO orderCallDO = new OrderCallDO();
        BeanUtils.copyProperties(orderDO, orderCallDO);
        orderCallDO.setGuid(DistributedUtils.id())
                .setBatch(DistributedUtils.id());
        String phone = getPhone(orderDO);
        log.info("phone: {}", JSONObject.toJSONString(phone));
        if (StringUtils.isEmpty(phone)) {
            log.info("获取手机号失败");
            return;
        }
        orderCallDO.setPhone(phone);
        orderCallDO.setSendStatus(BooleanEnum.FALSE.getCode());
        orderCallDO.setCallStatus(OrderCallEnum.NOT_CALL.getType());
        orderCallDO.setEnterpriseGuid(orderDO.getEnterpriseGuid());
        //超时时间
        LocalDateTime overtime = getOvertime(orderDO);
        log.info("最大超时送达时间: {}", overtime);
        log.info("实际送达时间: {}", deliveredTime);
        if (deliveredTime.isBefore(overtime)) {
            orderCallDO.setSendStatus(BooleanEnum.TRUE.getCode());
        }
        log.info("saveDB: {}", JSONObject.toJSONString(orderCallDO));
        orderCallService.save(orderCallDO);

        checkSendCall(orderDO, orderCallDO);
    }

    private boolean checkOrderCall(OrderDO orderDO) {
        if (Objects.nonNull(orderDO.getOrderTypeTcd()) && orderDO.getOrderTypeTcd().equals("PICK_UP")) {
            log.info("赚餐自提订单不外呼");
            return true;
        }

        if ((Objects.nonNull(orderDO.getOrderRemark()) && orderDO.getOrderRemark().equals("到店自取")) ||
                (Objects.nonNull(orderDO.getRecipientAddressDesensitization()) && orderDO.getRecipientAddressDesensitization().equals("到店自取"))) {
            log.info("美团自提订单不外呼");
            return true;
        }
        OrderCallDO orderCallDB = orderCallMapper.selectOne(new LambdaQueryWrapper<OrderCallDO>()
                .eq(OrderCallDO::getOrderId, orderDO.getOrderId()));
        if (Objects.nonNull(orderCallDB)) {
            log.info("已处理过此订单号: {}", JSONObject.toJSONString(orderCallDB));
            return true;
        }
        return false;
    }

    private void checkSendCall(OrderDO orderDO, OrderCallDO orderCallDO) {
        if (orderCallDO.getSendStatus() == 1) {
            //推送电信
            SendOrderCallReq sendOrderCallReq = new SendOrderCallReq();
            sendOrderCallReq.setGuid(orderCallDO.getGuid());
            sendOrderCallReq.setLongTime(orderDO.getCompleteTime().plusMinutes(DELAY_CALL));
            sendOrderCallReq.setBatch(orderCallDO.getBatch());
            sendOrderCallReq.setPhone(orderCallDO.getPhone());
            sendOrderCallReq.setOrderGuid(orderCallDO.getOrderId());
            sendOrderCallReq.setEnterpriseGuid(orderCallDO.getEnterpriseGuid());
            producerFeignClient.sendCall(sendOrderCallReq);
            log.info("订单远程调用推送，{}", JSONObject.toJSONString(sendOrderCallReq));
        } else {
            log.info("订单配送超时，{}", JSONObject.toJSONString(orderCallDO));
        }
    }

    private String getPhone(OrderDO orderDO) {
        String phone;
        if (!StringUtils.isEmpty(orderDO.getPrivacyPhone())) {
            List<String> privacyLists = JacksonUtils.toObjectList(String.class, orderDO.getPrivacyPhone());
            phone = privacyLists.get(0);
            if (orderDO.getOrderSubType() == 1) {
                phone = phone.substring(0, 11);
            }
        } else {
            List<String> customerLists = JacksonUtils.toObjectList(String.class, orderDO.getCustomerPhone());
            phone = customerLists.get(0);
        }
        return phone;
    }


    @NotNull
    private LocalDateTime getOvertime(OrderDO orderDO) {
        LocalDateTime overtime;
        if (orderDO.getReserve().equals(Boolean.TRUE)) {
            //饿了么预计送达时间
            if (orderDO.getOrderSubType() == 1) {
                OleOrder oleOrder = producerFeignClient.getOrderItemOOrder(orderDO.getStoreGuid(), orderDO.getOrderId());
                log.info("获取饿了么订单返参: {}", JacksonUtil.writeValueAsString(oleOrder));
                overtime = getLocalDateTime(oleOrder.getDeliverTime()).plusMinutes(EXCEED_TIME);
                log.info("饿了么预计送达时间: {}", overtime);
            } else {
                //美团预计送达时间
                overtime = orderDO.getEstimateDeliveredTime().plusMinutes(EXCEED_TIME);
                log.info("美团预计送达时间: {}", overtime);
            }
        } else {
            // 根据接单时间判断
            overtime = orderDO.getAcceptTime().plusMinutes(OVERTIME_TIME);
            log.info("非预订单送达时间: {}", overtime);
        }
        return overtime;
    }

    private LocalDateTime getLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();

        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        log.info("Date{}:" + JSON.toJSONString(date));
        log.info("LocalDateTime:{}" + JSON.toJSONString(localDateTime));
        return localDateTime;
    }

    @Override
    public void refundOrderReq(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        log.info("({})用户申请退单，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        try {
            saveRefundItems(unOrder, orderDO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("({})用户申请退单，保存退菜表信息错误:{}", getPlatformName(unOrder), e.getMessage());
        }
        // 订单日志，消息推送
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));
        logService.save(takeoutLogFactory.createOrderRefundReq(orderInDb));

        // 自动接单移除队列
        autoAcceptOrderService.removeOrder(orderDO);

        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderRefundReq(orderDO));
        log.info("({})用户申请退单，orderId: {}，处理完毕", getPlatformName(unOrder), orderDO.getOrderId());
    }


    private void mergeOrderItemInfo(List<RefundItemDO> refundItems, OrderReadDO orderReadDO, Integer orderSubType) {
        if (CollectionUtils.isEmpty(refundItems) || Objects.isNull(orderReadDO)) {
            return;
        }
        // 由于饿了么外卖退单回调接口传过来的商品名称格式为：商品名称-商品规格名称，所以就单独处理
        if (OrderType.TakeoutSubType.ELE_TAKEOUT.getType() == orderSubType) {
            for (ItemDO itemDO : orderReadDO.getArrayOfItem()) {
                // 主要处理规格商品
                if (!StringUtils.isEmpty(itemDO.getItemSpec())) {
                    String spec = "(" + itemDO.getItemSpec() + ")";
                    itemDO.setItemName(itemDO.getItemName().replace(spec, "") + "-" + itemDO.getItemSpec());
                }
                if (!StringUtils.isEmpty(itemDO.getItemProperty())) {
                    String property = "[" + itemDO.getItemProperty() + "]";
                    itemDO.setItemName(itemDO.getItemName() + property);
                }
            }
        }
        buildRefundItems(refundItems, orderReadDO);
    }

    private void buildRefundItems(List<RefundItemDO> refundItems, OrderReadDO orderReadDO) {
        // 一个订单中可能存在多个相同的item
        Map<String, List<ItemDO>> itemDOByThirdSkuIdMap = orderReadDO.getArrayOfItem().stream().collect(Collectors.groupingBy(ItemDO::getThirdSkuId));
        Map<String, List<ItemDO>> itemDOByItemNameMap = orderReadDO.getArrayOfItem().stream().collect(Collectors.groupingBy(ItemDO::getItemName));
        Iterator<RefundItemDO> iterator = refundItems.iterator();
        while (iterator.hasNext()) {
            RefundItemDO e = iterator.next();
            // 优先匹配thirdSkuId
            List<ItemDO> itemDOList = itemDOByThirdSkuIdMap.get(e.getThirdSkuId());
            if (CollectionUtils.isEmpty(itemDOList)) {
                // thirdSkuId 匹配不上，则匹配商品名称
                itemDOList = itemDOByItemNameMap.get(e.getItemName());
            }
            if (CollectionUtils.isEmpty(itemDOList) && e.getItemName().contains("餐盒")) {
                // 饿了么退菜，会将餐盒当作商品返回
                iterator.remove();
                continue;
            }
            if (CollectionUtils.isEmpty(itemDOList)) {
                log.error("无法匹配到数据库中退菜商品，菜品信息:{}", JacksonUtils.writeValueAsString(e));
                iterator.remove();
                continue;
            }
            ItemDO itemDO = queryEqualsItemPriceAndCount(itemDOList, e);
            // copy
            e.setOrderGuid(itemDO.getOrderGuid());
            e.setOrderItemGuid(itemDO.getItemGuid());
            e.setOrderSubType(itemDO.getOrderSubType());
            e.setStoreGuid(itemDO.getStoreGuid());
            e.setStoreName(itemDO.getStoreName());
            e.setBusinessDay(itemDO.getBusinessDay());
            e.setSettleType(itemDO.getSettleType());
            e.setItemSku(itemDO.getItemSku());
            e.setItemPrice(itemDO.getItemPrice());
            e.setItemTotal(e.getItemPrice().multiply(e.getItemCount()));
            BigDecimal ratio = itemDO.getActualItemCount().divide(itemDO.getItemCount(), 2, RoundingMode.HALF_UP);
            e.setActualItemCount(e.getItemCount().multiply(ratio));
        }
    }


    private ItemDO queryEqualsItemPriceAndCount(List<ItemDO> itemDOList, RefundItemDO e) {
        ItemDO itemDO = itemDOList.get(0);
        if (itemDOList.size() > 1) {
            String uniqueItemStr = e.getItemPrice().toPlainString() + "-" + e.getItemCount();
            for (ItemDO item : itemDOList) {
                if (uniqueItemStr.equals(item.getItemPrice().toPlainString() + "-" + item.getItemCount())) {
                    itemDO = item;
                    break;
                }
            }
        }
        return itemDO;
    }


    private void mergeItemMapping(List<RefundItemDO> refundItems, List<ItemDO> arrayOfItem) {
        if (CollectionUtils.isEmpty(refundItems) || CollectionUtils.isEmpty(arrayOfItem)) {
            return;
        }
        Map<String, ItemDO> itemDbMap = arrayOfItem.stream()
                .collect(Collectors.toMap(ItemDO::getItemGuid, Function.identity(), (key1, key2) -> key1));
        String refundGuid = DistributedUtils.id();
        for (RefundItemDO refundItem : refundItems) {
            refundItem.setRefundGuid(refundGuid);
            ItemDO itemDO = itemDbMap.get(refundItem.getOrderItemGuid());
            if (Objects.isNull(itemDO)) {
                continue;
            }
            refundItem.setStoreGuid(itemDO.getStoreGuid());
            refundItem.setStoreName(itemDO.getStoreName());
            refundItem.setErpSkuGuid(itemDO.getErpItemSkuGuid());
            refundItem.setErpItemName(itemDO.getErpItemName());
            refundItem.setErpItemCount(refundItem.getActualItemCount());
            refundItem.setErpItemPrice(itemDO.getErpItemPrice());
            if (Objects.nonNull(refundItem.getErpItemCount()) && Objects.nonNull(refundItem.getErpItemPrice())) {
                refundItem.setErpItemTotal(refundItem.getErpItemCount().multiply(refundItem.getErpItemPrice()));
            }
        }
    }

    private void addOrderItemInfo(List<RefundItemDO> refundItems, OrderReadDO orderReadDO) {
        if (CollectionUtils.isNotEmpty(refundItems) || Objects.isNull(orderReadDO)) {
            return;
        }
        List<RefundItemDO> transferRefundItems = orderMapstruct.fromItemDO(orderReadDO.getArrayOfItem());
        log.info("添加退菜菜品明细,transferRefundItems:{}", JacksonUtils.writeValueAsString(transferRefundItems));
        refundItems.addAll(transferRefundItems);
    }

    @Override
    public void cancelRefundOrderReq(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = orderDO.getOrderId();
        log.info("({})用户申请取消退单请求，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
        update(orderDO, wrapperByOrderId(orderId));
        // 订单日志，消息推送
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));
        logService.save(takeoutLogFactory.createOrderCancelRefundReq(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderCancelRefundReq(orderDO));
        log.info("({})用户申请取消退单请求，orderId: {}，处理完毕", getPlatformName(unOrder), orderId);
    }

    @Override
    public String agreeRefundReq(TakeoutOrderDTO takeoutOrderDTO) {
        return replyRefundReq(takeoutOrderDTO, true);
    }

    @Override
    public void refundReqAgreed(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String platformName = getPlatformName(unOrder);
        log.info("({})商户同意退单，orderId: {}，处理中", platformName, orderDO.getOrderId());
        orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
        orderDO.setRefundSuccess(true);
        OrderDO orderInDb = getOne(wrapperByOrderId(unOrder.getOrderId()));
        orderDO.setCompleteTime(DateTimeUtils.now());
        if (StringUtils.isEmpty(orderInDb.getRefundReplyStaffGuid())) {
            String acceptStaffName = Integer.valueOf(0).equals(orderDO.getOrderSubType())
                    ? BUSINESS_MT : BUSINESS_ELM;
            orderDO.setRefundReplyStaffName(acceptStaffName);
        }

        boolean mtRefundBeforeFinished = OrderType.TakeoutSubType.MT_TAKEOUT.getType() == unOrder.getOrderSubType()
                && OrderStatus.FINISHED != orderInDb.getOrderStatus();
        // 如果美团在未完成情况下申请退款，需要自行将其标记为已完成
        if (mtRefundBeforeFinished) {
            orderDO.setOrderStatus(OrderStatus.FINISHED);
        }
        update(orderDO, wrapperByOrderId(orderDO.getOrderId()));
        // 直接赋值刚更新的列，避免再次查询sql
        orderInDb.setOrderTagStatus(orderInDb.getOrderTagStatus());
        if (StringUtils.isEmpty(orderInDb.getRefundReplyStaffName())) {
            orderInDb.setRefundReplyStaffName(orderDO.getRefundReplyStaffName());
        }
        if (orderInDb.getCompleteTime() == null) {
            orderInDb.setCompleteTime(orderDO.getCompleteTime());
        }
        if (orderInDb.getBusinessDay() == null) {
            orderInDb.setBusinessDay(orderDO.getBusinessDay());
        }
        // 处理订单商品明细表上的退单数量
        handleOrderItemRefundCount(unOrder, orderInDb);
        // 订单日志，消息推送
        // 判断该商家如果确实接入了一城飞客，同意用户退款，则调用小程序端，取消配送
        OrderReadDO orderReadDO = baseMapper.getOrderDetail(orderDO);
        Boolean isYiCheng = isYiChengDelivery(orderReadDO);
        log.info("({})订单[{}]isYiCheng={},part={}",
                platformName, unOrder.getOrderId(), isYiCheng, unOrder.getPart());
        if (isYiCheng) {
            log.info("orderInDb.getCancelReason()={},orderDO.getCancelReason()={}"
                    , orderInDb.getCancelReason(), orderDO.getCancelReason());
            if (!StringUtils.isEmpty(orderInDb.getCancelReason())) {
                unOrder.setCancelReason(orderInDb.getCancelReason());
            } else if (!StringUtils.isEmpty(orderDO.getCancelReason())) {
                unOrder.setCancelReason(orderInDb.getCancelReason());
            } else {
                unOrder.setCancelReason("申请取消");
            }
            //如果是部分退款，则不取消配送
            if (!ObjectUtils.isEmpty(unOrder.getPart()) && unOrder.getPart()) {

            } else {
                unOrder.setReplyMsgType(UnOrderReplyMsgType.CANCEL_DELIVERY_ORDER);
                producerFeignClient.reply(unOrder);
            }
        }
        logService.save(takeoutLogFactory.createOrderRefundAgreed(orderInDb));
        if (mtRefundBeforeFinished) {
            logService.save(takeoutLogFactory.createOrderFinished(orderInDb));
        }

        // 自动接单移除队列
        autoAcceptOrderService.removeOrder(orderDO);

        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderRefundAgreed(orderDO));
        log.info("({})商户同意退单，orderId: {}，处理完毕", getPlatformName(unOrder), orderDO.getOrderId());
    }

    /**
     * 退款成功更新退菜数量
     */
    private void handleOrderItemRefundCount(UnOrder unOrder, OrderDO orderInDb) {
        String jsonStr = UserContextUtils.getJsonStr();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        String traceid = TraceidUtils.getTraceid();
        String orderGuid = orderInDb.getOrderGuid();
        String orderContext = OrderContextUtils.getOrderContext();
        log.info("退款成功，处理退菜商品orderGuid:{}", orderGuid);
        // 线上出现用户发起退菜和商家同意退菜在同一时间，所以更新不了退菜表状态和数量，所以这里使用延迟更新
        EXECUTOR_SERVICE.schedule(() -> {
            try {
                log.info("退款成功，开始处理退菜商品orderGuid:{},enterpriseGuid:{}", orderGuid, enterpriseGuid);
                // 切换数据源
                UserContextUtils.put(jsonStr);
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
                TraceidUtils.setTraceid(traceid);
                OrderContextUtils.setOrderContext(orderContext);
                // 查询当前退单数量
                List<RefundItemDO> refundItems = refundItemService.listItemGroupByOrderItemGuid(orderGuid);
                if (CollectionUtils.isEmpty(refundItems)) {
                    log.warn("当前订单退单商品无记录,orderGuid:{}", orderGuid);
                    // 饿了么出现没有先申请退款 直接收到商家同意退款的消息 （可能是订单完成之后退款,或者仲裁成功退款）
                    // 防止直接推送退款消息
                    refundItems = saveRefundItems(unOrder, orderInDb);
                }
                // 更新退菜表状态
                refundItemService.updateRefundSuccess(orderGuid);
                // 查询修改之前的订单明细
                List<ItemDO> oldItemList = itemService.getItemList(orderGuid);
                // 更新 orderItem
                List<ItemDO> items = itemMapstruct.refundItemDOList2ItemDOList(refundItems);
                itemService.updateBatchRefundCount(items);
                // 更新订单退款状态
                updateOrderRefundStatus(orderGuid);
                // 库存处理
                OrderDO queryOrderDO = new OrderDO();
                queryOrderDO.setOrderGuid(orderGuid);
                OrderReadDO orderReadDO = orderMapper.getOrderDetail(queryOrderDO);
                log.info("退款订单信息:{}", JacksonUtils.writeValueAsString(orderReadDO));
                //对接库存
                Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = mapOfSkuTakeawayInfoRespDTOV2(orderReadDO);
                log.info("退款订单商品信息,items:{},mapOfSkuTakeawayInfoRespDTO:{}", JacksonUtils.writeValueAsString(items),
                        JacksonUtils.writeValueAsString(mapOfSkuTakeawayInfoRespDTO));
                erpService.addStockForOrderMDM(orderReadDO, oldItemList, items, mapOfSkuTakeawayInfoRespDTO);
            } catch (Exception e) {
                log.error("更新退单商品数量失败,orderGuid:{},", orderGuid, e);
            }
        }, 5, TimeUnit.SECONDS);
    }

    /**
     * 保存退菜明细
     */
    private List<RefundItemDO> saveRefundItems(UnOrder unOrder, OrderDO orderDO) {
        List<RefundItemDO> refundItems = orderMapstruct.fromUnRefundItem(unOrder.getArrayOfUnRefundItem());
        if (Objects.isNull(refundItems)) {
            refundItems = Lists.newArrayList();
        }
        OrderReadDO orderReadDO = baseMapper.getOrderDetail(new OrderDO().setOrderId(orderDO.getOrderId()));
        mergeOrderItemInfo(refundItems, orderReadDO, unOrder.getOrderSubType());
        orderDO.setRefundReqExpireTime(orderDO.getRefundReqTime().plusDays(1));
        orderDO.setOrderTagStatus(OrderTagStatus.REFUND_REQ);
        if (Boolean.TRUE.equals(unOrder.getPart())) {
            if (!StringUtils.isEmpty(orderReadDO.getCustomerRefundItem())) {
                orderReadDO.setCustomerRefundItem(orderReadDO.getCustomerRefundItem() + "，");
                orderDO.setCustomerRefundItem(orderReadDO.getCustomerRefundItem() + orderDO.getCustomerRefundItem());
            }
            orderDO.setCustomerRefund(Optional.ofNullable(orderReadDO.getCustomerRefund()).orElse(BigDecimal.ZERO)
                    .add(Optional.ofNullable(orderDO.getCustomerRefund()).orElse(BigDecimal.ZERO)));
        } else {
            // 全部退菜
            // 避免先部分退款，然后再全部退款操作
            refundItemService.removeByOrderGuid(orderReadDO.getOrderGuid());
            orderDO.setCustomerRefund(orderReadDO.getCustomerActualPay());
            String customerRefundItem = orderReadDO.getArrayOfItem().stream()
                    .map(itemDO -> itemDO.getItemName() + "*" + BigDecimalUtils.formatCount(itemDO.getItemCount()))
                    .collect(Collectors.joining("，"));
            orderDO.setCustomerRefundItem(customerRefundItem);
            // 构建退菜菜品信息
            addOrderItemInfo(refundItems, orderReadDO);
        }
        update(orderDO, wrapperByOrderId(orderDO.getOrderId()));
        log.info("({})用户申请退单，菜品信息:{}", getPlatformName(unOrder), JacksonUtils.writeValueAsString(refundItems));
        if (CollectionUtils.isNotEmpty(refundItems)) {
            // 绑定映射关系
            mergeItemMapping(refundItems, orderReadDO.getArrayOfItem());
            // 保存退菜表
            refundItemService.saveBatch(refundItems);
        }
        return refundItems;
    }


    @Override
    public String disagreeRefundReq(TakeoutOrderDTO takeoutOrderDTO) {
        return replyRefundReq(takeoutOrderDTO, false);
    }

    @Override
    public void refundReqDisagreed(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        log.info("({})商户不同意退单，orderId: {}，处理中", getPlatformName(unOrder), orderDO.getOrderId());
        orderDO.setRefundSuccess(false);
        orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
        OrderDO orderInDb = getOne(wrapperByOrderId(unOrder.getOrderId()));
        if (StringUtils.isEmpty(orderInDb.getRefundReplyStaffGuid())) {
            String acceptStaffName = Integer.valueOf(0).equals(orderDO.getOrderSubType())
                    ? BUSINESS_MT : BUSINESS_ELM;
            orderDO.setRefundReplyStaffName(acceptStaffName);
        }
        update(orderDO, wrapperByOrderId(orderDO.getOrderId()));
        // 直接赋值刚更新的列，避免再次查询sql
        orderInDb.setOrderTagStatus(orderDO.getOrderTagStatus());
        if (StringUtils.isEmpty(orderInDb.getRefundReplyStaffName())) {
            orderInDb.setRefundReplyStaffName(orderDO.getRefundReplyStaffName());
        }
        // 订单日志，消息推送
        logService.save(takeoutLogFactory.createOrderRefundDisagreed(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderRefundDisagreed(orderDO));
        log.info("({})商户不同意退单，orderId: {}，处理完毕", getPlatformName(unOrder), orderDO.getOrderId());
    }

    @Override
    public void refundArbitrationEffective(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = unOrder.getOrderId();
        log.info("({})客服仲裁退单有效，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));

        orderDO.setRefundSuccess(true);
        orderDO.setOrderTagStatus(OrderTagStatus.NORMAL);
        update(orderDO, wrapperByOrderId(orderDO.getOrderId()));
        // 处理订单商品明细表上的退单数量
        handleOrderItemRefundCount(unOrder, orderInDb);
        // 直接赋值刚更新的列，避免再次查询sql
        orderInDb.setOrderTagStatus(orderDO.getOrderTagStatus());
        if (orderInDb.getCompleteTime() == null) {
            orderInDb.setCompleteTime(orderDO.getCompleteTime());
        }
        if (orderInDb.getBusinessDay() == null) {
            orderInDb.setBusinessDay(orderDO.getBusinessDay());
        }
        // 订单日志，消息推送
        logService.save(takeoutLogFactory.createOrderRefundReqArbitrationEffective(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderRefundAgreed(orderDO));
        log.info("({})客服仲裁退单有效，orderId: {}，处理完毕", getPlatformName(unOrder), orderDO.getOrderId());
    }

    @Override
    public void orderRemindByUser(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        OrderReadDO orderReadDO = baseMapper.getOrderDetail(orderDO);
        UnRemind unRemind = unOrder.getArrayOfUnRemind().get(0);
        String orderId = orderDO.getOrderId();
        log.info("({})订单已催单，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        RemindDO remindDO = orderMapstruct.fromUnRemind(unRemind);
        OrderDO orderInDb = getOne(wrapperByOrderId(orderId));
        remindDO.setOrderGuid(orderInDb.getOrderGuid());
        remindDO.setRemindGuid(IDUtils.nextId());
        remindDO.setReplied(false);
        remindMapper.insert(remindDO);
        OrderDO orderToUpdate = new OrderDO().setReminded(true).setRemindTime(DateTimeUtils.now());
        updateById(orderToUpdate.setId(orderInDb.getId()));
        // 直接赋值刚更新的列，避免再次查询sql
        orderInDb.setReminded(orderToUpdate.getReminded());
        orderInDb.setRemindTime(orderToUpdate.getRemindTime());
        //通知KDS
        kdsService.urge(orderReadDO);
        // 订单日志，消息推送
        logService.save(takeoutLogFactory.createOrderReminded(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderReminded(orderDO));
        log.info("({})订单已催单，orderId: {}，处理完毕", getPlatformName(unOrder), orderId);
    }

    @Override
    public void replyRemindOrder(TakeoutOrderDTO.OrderRemindDTO orderRemindDTO) {
        RemindDO remindDO = orderMapstruct.fromOrderRemindDTO(orderRemindDTO);
        remindDO.setReplyTime(DateTimeUtils.now());
        remindDO.setReplyStaffGuid(UserContextUtils.getUserGuid());
        remindDO.setReplyStaffName(UserContextUtils.getUserName());
        remindMapper.update(remindDO, new LambdaQueryWrapper<RemindDO>()
                .eq(RemindDO::getRemindGuid, orderRemindDTO.getRemindGuid()));
        OrderReadDO orderInDb = baseMapper.getOrderRemind(new OrderRemindQuery()
                .setRemindGuid(orderRemindDTO.getRemindGuid()));
        log.info("({})商户回复催单，orderId: {}", getPlatformName(orderInDb.getOrderSubType()), orderInDb.getOrderId());
        UnOrder unOrder = orderMapstruct.toUnOrder(orderInDb);
        unOrder.setReplyMsgType(UnOrderReplyMsgType.REPLY_URGE_ORDER);
        producerFeignClient.reply(unOrder);
    }

    /**
     * 配送失败---->到异常单展示
     *
     * @param unOrder
     */
    @Override
    public void deliveryError(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = unOrder.getOrderId();
        log.info("({})订单一城飞客配送失败，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        OrderDO orderInDb = getOne(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getOrderId, orderId));
        orderDO.setOrderTagStatus(OrderTagStatus.CANCEL_REQ);
        orderDO.setDeliveryType(1);
        updateById(orderDO.setId(orderInDb.getId()));
        // 订单日志，消息推送
        orderInDb.setCancelTime(orderDO.getCancelTime());
        orderInDb.setCancelReason(orderDO.getCancelReason());
        orderDO.setOrderSubType(orderInDb.getOrderSubType());
        logService.save(takeoutLogFactory.createDeliveryError(orderInDb));
        bizMsgFeignClient.msg(takeoutMsgFactory.createDeliveryError(orderInDb));
        log.info("({})订单一城飞客配送失败，orderId: {}，处理完毕", getPlatformName(unOrder), orderId);
    }

    @Override
    public void diningOutTcd(UnOrder unOrder) {
        OrderDO orderDO = orderMapstruct.fromUnOrder(unOrder);
        String orderId = unOrder.getOrderId();
        log.info("({})订单出餐，orderId: {}，处理中", getPlatformName(unOrder), orderId);
        OrderDO orderInDb = getOne(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getOrderId, orderId));
        orderDO.setOrderStatusTcd("PERSON_PENDING");
        updateById(orderDO.setId(orderInDb.getId()));
        // 消息推送
        bizMsgFeignClient.msg(takeoutMsgFactory.createOrderDiningOut(orderDO));
        log.info("({})订单出餐，orderId: {}，处理完毕", getPlatformName(unOrder), orderId);
    }

    @Override
    public String printBill(TakeoutOrderDTO takeoutOrderDTO) {
        OrderReadDO orderReadDO = baseMapper.getOrderDetail(orderMapstruct.fromTakeoutOrder(takeoutOrderDTO));
        // 如果是商家版接单打印则提示错误
        if (thirdAcceptOrder(orderReadDO)) {
            throw new BusinessException("需在第三方外卖平台关闭自动接单！");
        }
        // 补全套餐子菜信息
        replenishSubInfo(orderReadDO, takeoutOrderDTO.getStoreGuid());
        // 补全折扣信息
        replenishDiscountInfo(orderReadDO);
        // 外卖重打印用当前设备打印
        orderReadDO.setAcceptDeviceId(takeoutOrderDTO.getDeviceId());
        // 打印单子
        return printService.printBill(orderReadDO);
    }

    private void replenishDiscountInfo(OrderReadDO orderReadDO) {
        List<DiscountDO> discountDOList = discountService.list(new LambdaQueryWrapper<DiscountDO>()
                .eq(DiscountDO::getOrderGuid, orderReadDO.getOrderGuid()));
        if (org.springframework.util.CollectionUtils.isEmpty(discountDOList)) {
            log.warn("[订单无折扣信息]OrderGuid={}", orderReadDO.getOrderGuid());
            return;
        }
        List<String> strings = discountDOList.stream()
                .filter(unDiscount -> !ObjectUtils.isEmpty(unDiscount.getType()) && unDiscount.getType() == 5)
                .map(DiscountDO::getDiscountRemark)
                .collect(Collectors.toList());
        if (!org.springframework.util.CollectionUtils.isEmpty(strings)) {
            String discountRemark = String.join(",", strings);
            orderReadDO.setFullGiftRemark(discountRemark);
        }
    }

    /**
     * 补全套餐子菜信息
     */
    private void replenishSubInfo(OrderReadDO orderReadDO, String storeGuid) {
        TakeoutFormatDTO takeoutFormatDTO = printFeignClient.queryTakeout(storeGuid);
        if (ObjectUtils.isEmpty(takeoutFormatDTO) || !takeoutFormatDTO.getPackageContent().isEnable()) {
            log.warn("[未配置外卖单套餐内容打印]storeGuid={}", storeGuid);
            return;
        }
        List<ItemDO> arrayOfItem = orderReadDO.getArrayOfItem();
        List<String> guidList = arrayOfItem.stream()
                .map(ItemDO::getItemGuid)
                .collect(Collectors.toList());
        SingleListDTO dto = new SingleListDTO();
        dto.setList(guidList);
        log.info("[queryItemSubInfo]dto={}", JacksonUtils.writeValueAsString(dto));
        List<ItemDO> subItemDOList = baseMapper.queryItemSubInfo(dto);
        log.info("[queryItemSubInfo]subItemDOList={}", JacksonUtils.writeValueAsString(subItemDOList));
        if (org.springframework.util.CollectionUtils.isEmpty(subItemDOList)) {
            log.warn("[拓展信息查询为空]guidList={}", guidList);
            return;
        }
        Map<String, ItemDO> subItemDOMap = subItemDOList.stream()
                .collect(Collectors.toMap(ItemDO::getItemGuid, Function.identity(), (v1, v2) -> v1));
        arrayOfItem.forEach(item -> {
            ItemDO itemDO = subItemDOMap.get(item.getItemGuid());
            if (ObjectUtils.isEmpty(itemDO)) {
                log.warn("[未匹配到订单商品]ItemGuid={}", item.getItemGuid());
                return;
            }
            item.setSubItemInfo(itemDO.getSubItemInfo());
        });
        log.info("[补全后套餐子菜信息]arrayOfItem={}", JacksonUtils.writeValueAsString(arrayOfItem));
    }

    private boolean thirdAcceptOrder(OrderReadDO orderReadDO) {
        return StringUtils.isEmpty(orderReadDO.getAcceptStaffGuid()) && StringUtils.isEmpty(orderReadDO.getAcceptDeviceId());
    }

    @Override
    public String printKitchen(TakeoutOrderDTO takeoutOrderDTO) {
        OrderReadDO orderReadDO = baseMapper.getOrderDetail(orderMapstruct.fromTakeoutOrder(takeoutOrderDTO));
        return printService.printKitchen(orderReadDO);
    }

    @Override
    public String printLabel(TakeoutOrderDTO takeoutOrderDTO) {
        OrderReadDO orderReadDO = baseMapper.getOrderDetail(orderMapstruct.fromTakeoutOrder(takeoutOrderDTO));
        return printService.printLabel(orderReadDO);
    }

    @Override
    public HandoverPayDTO getOrderMoney(HandoverPayQueryDTO handoverPayQueryDTO) {
        List<OrderDO> orderList = queryOrdersInTimeRange(handoverPayQueryDTO);
        if (CollectionUtils.isEmpty(orderList)) {
            return null;
        }

        // 使用Map按平台统计支付金额
        Map<Integer, BigDecimal> platformPaymentMap = new HashMap<>();
        BigDecimal totalPayment = BigDecimal.ZERO;
        int validOrderCount = 0;

        for (OrderDO order : orderList) {
            BigDecimal actualPayAmount = calculateActualPayAmount(order, handoverPayQueryDTO);
            
            // 统计有效订单数（非退款订单）
            if (!isRefundedOrder(order)) {
                validOrderCount++;
            }
            
            // 按平台累计支付金额
            if (PLATFORM_NAME_MAP.containsKey(order.getOrderSubType())) {
                platformPaymentMap.merge(order.getOrderSubType(), actualPayAmount, BigDecimal::add);
            }
            
            totalPayment = totalPayment.add(actualPayAmount);
        }

        return buildHandoverPayDTO(platformPaymentMap, totalPayment, validOrderCount);
    }

    /**
     * 查询时间范围内的订单
     */
    private List<OrderDO> queryOrdersInTimeRange(HandoverPayQueryDTO query) {
        return list(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getStoreGuid, query.getStoreGuid())
                .eq(OrderDO::getAcceptStaffGuid, query.getUserGuid())
                .eq(OrderDO::getOrderStatus, 100)
                .ne(OrderDO::getRefundStatus, TakeawayRefundStatusEnum.ALL_REFUND.ordinal())
                .between(OrderDO::getCompleteTime, query.getGmtCreate(), query.getGmtModified()));
    }

    /**
     * 计算订单实际支付金额
     */
    private BigDecimal calculateActualPayAmount(OrderDO order, HandoverPayQueryDTO query) {
        BigDecimal actualPayAmount = Optional.ofNullable(order.getCustomerActualPay())
                .orElse(BigDecimal.ZERO);
                
        if (Boolean.TRUE.equals(order.getRefundSuccess())) {
            BigDecimal refundAmount = calculateRefundAmount(order, query);
            actualPayAmount = actualPayAmount.subtract(refundAmount);
        }
        
        return actualPayAmount;
    }

    /**
     * 计算退款金额
     */
    private BigDecimal calculateRefundAmount(OrderDO order, HandoverPayQueryDTO query) {
        BigDecimal customerRefund = (order.getOrderTagStatus() != 0) ? 
                BigDecimal.ZERO : 
                Optional.ofNullable(order.getCustomerRefund()).orElse(BigDecimal.ZERO);
                
        // 如果订单生成于当前班次之前，退款发生在当前班次，则取退款金额的负值
        if (order.getGmtCreate().isBefore(query.getGmtCreate())) {
            return customerRefund.negate();
        }
        
        return customerRefund;
    }

    /**
     * 判断是否为退款订单
     */
    private boolean isRefundedOrder(OrderDO order) {
        return Boolean.TRUE.equals(order.getRefundSuccess());
    }

    /**
     * 构建交接班支付DTO
     */
    private HandoverPayDTO buildHandoverPayDTO(Map<Integer, BigDecimal> platformPaymentMap, 
                                               BigDecimal totalPayment, int validOrderCount) {
        HandoverPayDTO handoverPayDTO = new HandoverPayDTO();
        handoverPayDTO.setCheckedCount(validOrderCount);
        handoverPayDTO.setSaleIncoming(totalPayment);
        handoverPayDTO.setIncomingDetail(buildIncomingDetailByCode(platformPaymentMap));
        handoverPayDTO.setIncomingDetailStr(buildIncomingDetailByName(platformPaymentMap));
        return handoverPayDTO;
    }

    /**
     * 构建按平台代码的收入详情
     */
    private Map<Integer, BigDecimal> buildIncomingDetailByCode(Map<Integer, BigDecimal> platformPaymentMap) {
        Map<Integer, BigDecimal> result = new HashMap<>();
        platformPaymentMap.forEach((platformType, amount) -> {
            if (amount.compareTo(BigDecimal.ZERO) != 0) {
                Integer platformCode = PLATFORM_CODE_MAP.get(platformType);
                if (platformCode != null) {
                    result.put(platformCode, amount);
                }
            }
        });
        return result;
    }

    /**
     * 构建按平台名称的收入详情
     */
    private Map<String, BigDecimal> buildIncomingDetailByName(Map<Integer, BigDecimal> platformPaymentMap) {
        Map<String, BigDecimal> result = new HashMap<>();
        platformPaymentMap.forEach((platformType, amount) -> {
            if (amount.compareTo(BigDecimal.ZERO) != 0) {
                String platformName = PLATFORM_NAME_MAP.get(platformType);
                if (platformName != null) {
                    result.put(platformName, amount);
                }
            }
        });
        return result;
    }

    /**
     * @deprecated 使用buildIncomingDetailByCode替代
     */
    @Deprecated
    private Map<Integer, BigDecimal> buildHandoverPayIncomingDetail(BigDecimal mtPaymentMoney, BigDecimal elePaymentMoney,
                                                                    BigDecimal zcPaymentMoney, BigDecimal jdPaymentMoney) {
        Map<Integer, BigDecimal> resultMap = new HashMap<>();
        if (mtPaymentMoney.compareTo(BigDecimal.ZERO) != 0) {
            resultMap.put(-2, mtPaymentMoney);
        }
        if (elePaymentMoney.compareTo(BigDecimal.ZERO) != 0) {
            resultMap.put(-1, elePaymentMoney);
        }
        if (zcPaymentMoney.compareTo(BigDecimal.ZERO) != 0) {
            resultMap.put(-6, zcPaymentMoney);
        }
        if (jdPaymentMoney.compareTo(BigDecimal.ZERO) != 0) {
            resultMap.put(-3, jdPaymentMoney);
        }
        return resultMap;
    }

    /**
     * @deprecated 使用buildIncomingDetailByName替代
     */
    @Deprecated
    private Map<String, BigDecimal> buildHandoverPayIncomingDetailStr(BigDecimal mtPaymentMoney, BigDecimal elePaymentMoney,
                                                                      BigDecimal zcPaymentMoney, BigDecimal jdPaymentMoney) {
        Map<String, BigDecimal> resultMap = new HashMap<>();
        if (mtPaymentMoney.compareTo(BigDecimal.ZERO) != 0) {
            resultMap.put("美团", mtPaymentMoney);
        }
        if (elePaymentMoney.compareTo(BigDecimal.ZERO) != 0) {
            resultMap.put("饿了么", elePaymentMoney);
        }
        if (zcPaymentMoney.compareTo(BigDecimal.ZERO) != 0) {
            resultMap.put("赚餐", zcPaymentMoney);
        }
        if (jdPaymentMoney.compareTo(BigDecimal.ZERO) != 0) {
            resultMap.put("京东", jdPaymentMoney);
        }
        return resultMap;
    }

    @Override
    public TakeoutStatsDTO getOpStats(TakeoutStatsQueryDTO takeoutStatsQueryDTO) {
        if (CollectionUtil.isEmpty(takeoutStatsQueryDTO.getStoreGuids()) && StringUtils.isEmpty(takeoutStatsQueryDTO.getStoreGuid())) {
            throw new BusinessException("门店Guid不可为空");
        }
        List<OrderDO> arrayOfOrderDO = list(new LambdaQueryWrapper<OrderDO>()
                .eq(!StringUtils.isEmpty(takeoutStatsQueryDTO.getStoreGuid()), OrderDO::getStoreGuid, takeoutStatsQueryDTO.getStoreGuid())
                .in(CollectionUtil.isNotEmpty(takeoutStatsQueryDTO.getStoreGuids()), OrderDO::getStoreGuid, takeoutStatsQueryDTO.getStoreGuids())
                .in(CollectionUtil.isNotEmpty(takeoutStatsQueryDTO.getStaffGuids()), OrderDO::getAcceptStaffGuid, takeoutStatsQueryDTO.getStaffGuids())
                .eq(OrderDO::getOrderStatus, 100)
                .ne(OrderDO::getRefundStatus, TakeawayRefundStatusEnum.ALL_REFUND.ordinal())
                .between(OrderDO::getCompleteTime, takeoutStatsQueryDTO.getBeginTime(), takeoutStatsQueryDTO.getEndTime()));
        if (arrayOfOrderDO == null || arrayOfOrderDO.isEmpty()) {
            return TakeoutStatsDTO.empty();
        }

        // 订单数
        int orderCount = arrayOfOrderDO.size();
        Map<Integer, Long> orderCountMap = arrayOfOrderDO.stream()
                .collect(Collectors.groupingBy(OrderDO::getOrderSubType, Collectors.counting()));
        // 客流量
        int customerCount = arrayOfOrderDO.size();
        // 退款金额
        BigDecimal refundAmount = BigDecimal.ZERO;
        // 销售收入、优惠总额、销售收入订单数详情、优惠总额订单数详情
        Map<String, BigDecimal> salesIncomingDetail = new HashMap<>();
        Map<String, BigDecimal> discountAmountDetail = new HashMap<>();
        Map<String, Integer> orderCountDetail = new HashMap<>();
        Map<String, Integer> discountOrderCountDetail = new HashMap<>();
        //商家收款
        Map<String, BigDecimal> shopAmountDetail = new HashMap<>();
        for (OrderDO orderDO : arrayOfOrderDO) {
            // 销售收入
            BigDecimal actualPayAmount = orderDO.getCustomerActualPay();
            // 商家收款
            BigDecimal shopAmount = Objects.nonNull(orderDO.getShopTotal()) ? orderDO.getShopTotal() : BigDecimal.ZERO;
            if (actualPayAmount == null) {
                actualPayAmount = BigDecimal.ZERO;
            }
            if (orderDO.getRefundSuccess() != null && orderDO.getRefundSuccess()) {
                BigDecimal customerRefund = (0 != orderDO.getOrderTagStatus()) ? BigDecimal.ZERO
                        : Optional.ofNullable(orderDO.getCustomerRefund()).orElse(BigDecimal.ZERO);
                actualPayAmount = actualPayAmount.subtract(customerRefund);
                shopAmount = shopAmount.subtract(customerRefund);
                refundAmount = refundAmount.add(customerRefund);
            }
            String salesIncomingKey = getCollectAmountKey(orderDO);
            BigDecimal salesIncoming = salesIncomingDetail.computeIfAbsent(salesIncomingKey, key -> BigDecimal.ZERO);
            salesIncomingDetail.put(salesIncomingKey, Objects.requireNonNull(salesIncoming).add(actualPayAmount));
            BigDecimal shopIncoming = shopAmountDetail.computeIfAbsent(salesIncomingKey, key -> BigDecimal.ZERO);
            shopAmountDetail.put(salesIncomingKey, Objects.requireNonNull(shopIncoming).add(shopAmount));
            // 销售收入订单数详情
            Integer salesIncomingOrderCount = orderCountDetail.computeIfAbsent(salesIncomingKey, key -> 0);
            orderCountDetail.put(salesIncomingKey, Objects.requireNonNull(salesIncomingOrderCount) + 1);
            // 优惠总额
            BigDecimal discountTotal = orderDO.getDiscountTotal();
            if (orderDO.getRefundSuccess() != null && orderDO.getRefundSuccess()) {
                discountTotal = BigDecimal.ZERO;
            }
            String discountAmountKey = "";
            if (orderDO.getOrderSubType() == 0) {
                discountAmountKey = "美团外卖优惠";
            } else if (orderDO.getOrderSubType() == 1) {
                discountAmountKey = "饿了么外卖优惠";
            } else if (orderDO.getOrderSubType() == 3) {
                discountAmountKey = "京东外卖优惠";
            }else if (orderDO.getOrderSubType() == 5) {
                discountAmountKey = "自营外卖优惠";
            } else if (orderDO.getOrderSubType() == 6) {
                discountAmountKey = "赚餐外卖优惠";
            }
            BigDecimal discountAmount = discountAmountDetail.computeIfAbsent(discountAmountKey, key -> BigDecimal.ZERO);
            discountAmountDetail.put(discountAmountKey, Objects.requireNonNull(discountAmount).add(discountTotal));
            if (BigDecimalUtil.greaterThanZero(discountTotal)) {
                // 优惠总额订单数详情
                Integer discountOrderCount = discountOrderCountDetail.computeIfAbsent(discountAmountKey, key -> 0);
                discountOrderCountDetail.put(discountAmountKey, Objects.requireNonNull(discountOrderCount) + 1);
            }
        }

        // 查询成本价
        List<String> orderGuidList = arrayOfOrderDO.stream().map(OrderDO::getOrderGuid).distinct().collect(Collectors.toList());
        BigDecimal totalCostPrice = itemExtendsService.getTotalCostPrice(orderGuidList);


        return new TakeoutStatsDTO()
                .setOrderCount(orderCount)
                .setOrderCountMap(orderCountMap)
                .setCustomerCount(customerCount)
                .setCostAmount(totalCostPrice)
                .setSalesIncomingDetail(salesIncomingDetail)
                .setDiscountAmountDetail(discountAmountDetail)
                .setShopAmountDetail(shopAmountDetail)
                .setOrderCountDetail(orderCountDetail)
                .setDiscountOrderCountDetail(discountOrderCountDetail)
                .setRefundAmount(refundAmount);
    }

    @Override
    public List<ItemRespDTO> listTakeOutItemSale(DailyReqDTO request) {
        List<ItemRespDTO> takeoutItemSaleList = orderMapper.listTakeOutItemSale(request);
        if (CollectionUtil.isEmpty(takeoutItemSaleList)) {
            return Collections.emptyList();
        }
        //根据sku查询商品分类
        List<String> skuGuidList = takeoutItemSaleList.stream().map(ItemRespDTO::getGuid).collect(Collectors.toList());
        List<SkuInfoRespDTO> skuInfoRespList = itemFeignClient.listSkuInfo(skuGuidList);
        if (CollectionUtil.isEmpty(skuInfoRespList)) {
            return Collections.emptyList();
        }
        //转换成map
        Map<String, ItemRespDTO> skuMap = takeoutItemSaleList.stream().collect(Collectors.toMap(ItemRespDTO::getGuid, e -> e, (e1, e2) -> {
            e1.setTakeoutQuantum(BigDecimalUtils.nullValue(e1.getTakeoutQuantum()).add(BigDecimalUtils.nullValue(e2.getTakeoutQuantum())));
            e1.setTakeoutAmount(BigDecimalUtils.nullValue(e1.getTakeoutAmount()).add(BigDecimalUtils.nullValue(e2.getTakeoutAmount())));
            return e1;
        }));
        Map<String, ItemRespDTO> respSaleMap = Maps.newHashMap();
        skuInfoRespList.forEach(sku -> {
            ItemRespDTO itemRespDTO = skuMap.get(sku.getSkuGuid());
            itemRespDTO.setGuid(sku.getTypeGuid());
            itemRespDTO.setName(sku.getTypeName());

            ItemRespDTO itemType = respSaleMap.get(sku.getTypeGuid());
            if (itemType == null) {
                respSaleMap.put(sku.getTypeGuid(), itemRespDTO);
                return;
            }
            itemType.setTakeoutAmount(itemRespDTO.getTakeoutAmount().add(itemType.getTakeoutAmount()));
            itemType.setTakeoutQuantum(itemRespDTO.getTakeoutQuantum().add(itemType.getTakeoutQuantum()));
        });
        log.info("商品销售分类统计查询：{}", respSaleMap);
        return Arrays.asList(respSaleMap.values().toArray(new ItemRespDTO[]{}));
    }

    @Override
    public List<ItemRespDTO> listTakeOutGoodsSale(DailyReqDTO request) {
        List<ItemRespDTO> takeoutItemSaleList = orderMapper.listTakeOutItemSale(request);
        if (CollectionUtil.isEmpty(takeoutItemSaleList)) {
            return Collections.emptyList();
        }
        takeoutItemSaleList.removeIf(e -> ObjectUtil.isNull(e.getName()));
        //根据sku查询商品分类
        List<String> skuGuidList = takeoutItemSaleList.stream().map(ItemRespDTO::getGuid).collect(Collectors.toList());
        List<SkuInfoRespDTO> skuInfoRespList = itemFeignClient.listSkuInfo(skuGuidList);
        log.info("[查询出的数据]skuInfoRespList={}", JacksonUtils.writeValueAsString(skuInfoRespList));
        // 商品分类过滤
        if (!CollectionUtil.isEmpty(skuInfoRespList) &&
                !org.springframework.util.CollectionUtils.isEmpty(request.getTypeGuidList())) {
            skuInfoRespList.removeIf(sku -> !request.getTypeGuidList().contains(sku.getTypeGuid()));
            log.info("[分类过滤后数据]skuInfoRespList={}", JacksonUtils.writeValueAsString(skuInfoRespList));
        }
        if (CollectionUtil.isEmpty(skuInfoRespList)) {
            return Collections.emptyList();
        }
        //转换成map
        Map<String, List<ItemRespDTO>> skuMap = takeoutItemSaleList.stream().collect(Collectors.groupingBy(ItemRespDTO::getGuid));
        Map<String, ItemRespDTO> respSaleMap = Maps.newHashMap();
        skuInfoRespList.forEach(sku -> {

            List<ItemRespDTO> list = skuMap.get(sku.getSkuGuid());

            list.forEach(e -> {
                e.setQuantum(e.getTakeoutQuantum());
                e.setAmount(e.getTakeoutAmount());
            });

            if (buildItemResp(respSaleMap, list, sku)) {
                return;
            }

            if (verifySubInfo(respSaleMap, list, sku)) {
                return;
            }

            //若包含子商品
            buildSubItem(respSaleMap, sku, list);
        });
        log.info("商品销售统计查询：{}", respSaleMap);
        return Arrays.asList(respSaleMap.values().toArray(new ItemRespDTO[]{}));
    }

    private boolean verifySubInfo(Map<String, ItemRespDTO> respSaleMap, List<ItemRespDTO> list, SkuInfoRespDTO sku) {
        ItemRespDTO item = respSaleMap.get(sku.getItemGuid());
        if (item.getHasSubInfo() != 1) {
            return false;
        }
        item.getSubs().addAll(list);
        item.setTakeoutAmount(list.stream().map(i -> i.getTakeoutAmount() == null ? BigDecimal.ZERO : i.getTakeoutAmount()).reduce(BigDecimal.ZERO, BigDecimal::add).add(item.getTakeoutAmount()));
        item.setTakeoutQuantum(list.stream().map(i -> i.getTakeoutQuantum() == null ? BigDecimal.ZERO : i.getTakeoutQuantum()).reduce(BigDecimal.ZERO, BigDecimal::add).add(item.getTakeoutQuantum()));

        item.setAmount(item.getTakeoutAmount());
        item.setQuantum(item.getTakeoutQuantum());

        return true;

    }

    private void buildSubItem(Map<String, ItemRespDTO> respSaleMap, SkuInfoRespDTO sku, List<ItemRespDTO> list) {
        ItemRespDTO item = respSaleMap.get(sku.getItemGuid());
        ItemRespDTO pItem = new ItemRespDTO();
        pItem.setGuid(sku.getItemGuid());
        pItem.setName(sku.getItemName());
        pItem.setHasSubInfo(1);
        List<ItemRespDTO> subInfo = Lists.newArrayList(item);
        subInfo.addAll(list);
        pItem.setSubs(subInfo);

        pItem.setTakeoutAmount(list.stream().map(i -> i.getTakeoutAmount() == null ? BigDecimal.ZERO : i.getTakeoutAmount()).reduce(BigDecimal.ZERO, BigDecimal::add).add(item.getTakeoutAmount()));
        pItem.setTakeoutQuantum(list.stream().map(i -> i.getTakeoutQuantum() == null ? BigDecimal.ZERO : i.getTakeoutQuantum()).reduce(BigDecimal.ZERO, BigDecimal::add).add(item.getTakeoutQuantum()));

        pItem.setAmount(pItem.getTakeoutAmount());
        pItem.setQuantum(pItem.getTakeoutQuantum());
        respSaleMap.put(sku.getItemGuid(), pItem);
    }

    private boolean buildItemResp(Map<String, ItemRespDTO> respSaleMap, List<ItemRespDTO> list, SkuInfoRespDTO sku) {

        ItemRespDTO itemResp = new ItemRespDTO();
        itemResp.setGuid(sku.getItemGuid());
        itemResp.setName(sku.getItemName());
        itemResp.setSkuName(sku.getName());

        ItemRespDTO item = respSaleMap.get(sku.getItemGuid());
        if (item == null && list.size() <= 1) {
            ItemRespDTO itemRespDTO = list.get(0);
            itemResp.setTakeoutAmount(itemRespDTO.getTakeoutAmount());
            itemResp.setAmount(itemRespDTO.getTakeoutAmount());

            itemResp.setTakeoutQuantum(itemRespDTO.getTakeoutQuantum());
            itemResp.setQuantum(itemRespDTO.getTakeoutQuantum());
            itemResp.setTakeoutUnitPrice(itemRespDTO.getTakeoutUnitPrice());
            respSaleMap.put(sku.getItemGuid(), itemResp);
            return true;
        }
        if (item != null) {
            return false;
        }
        itemResp.setSubs(list);
        itemResp.setHasSubInfo(1);

        itemResp.setTakeoutAmount(list.stream().map(i -> i.getTakeoutAmount() == null ? BigDecimal.ZERO : i.getTakeoutAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        itemResp.setTakeoutQuantum(list.stream().map(i -> i.getTakeoutQuantum() == null ? BigDecimal.ZERO : i.getTakeoutQuantum()).reduce(BigDecimal.ZERO, BigDecimal::add));

        itemResp.setAmount(itemResp.getTakeoutAmount());
        itemResp.setQuantum(itemResp.getTakeoutQuantum());

        respSaleMap.put(sku.getItemGuid(), itemResp);
        return true;

    }

    @Override
    public TakeoutStatsDTO getReceiptStats(TakeoutStatsQueryDTO takeoutStatsQueryDTO) {
        List<OrderDO> arrayOfOrderDO = list(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getStoreGuid, takeoutStatsQueryDTO.getStoreGuid())
                .eq(OrderDO::getOrderStatus, 100)
                .ne(OrderDO::getRefundStatus, TakeawayRefundStatusEnum.ALL_REFUND.ordinal())
                .between(OrderDO::getCompleteTime, takeoutStatsQueryDTO.getBeginTime(), takeoutStatsQueryDTO.getEndTime()));
        if (arrayOfOrderDO == null || arrayOfOrderDO.isEmpty()) {
            return TakeoutStatsDTO.empty();
        }

        Map<String, BigDecimal> collectAmountDetail = new HashMap<>();
        for (OrderDO orderDO : arrayOfOrderDO) {
            BigDecimal actualPayAmount = orderDO.getCustomerActualPay();
            if (orderDO.getRefundSuccess() != null && orderDO.getRefundSuccess()) {
                BigDecimal customerRefund = (0 != orderDO.getOrderTagStatus()) ? BigDecimal.ZERO
                        : Optional.ofNullable(orderDO.getCustomerRefund()).orElse(BigDecimal.ZERO);
                actualPayAmount = actualPayAmount.subtract(customerRefund);
            }
            String collectAmountKey = getCollectAmountKey(orderDO);
            BigDecimal collectAmount = collectAmountDetail.computeIfAbsent(collectAmountKey, key -> BigDecimal.ZERO);
            collectAmountDetail.put(collectAmountKey, Objects.requireNonNull(collectAmount).add(actualPayAmount));
        }

        return new TakeoutStatsDTO()
                .setSalesIncomingDetail(collectAmountDetail)
                .setCollectAmountDetail(collectAmountDetail);
    }

    private static String getCollectAmountKey(OrderDO orderDO) {
        String collectAmountKey = "";
        if (orderDO.getOrderSubType() == 0) {
            collectAmountKey = "美团外卖";
        } else if (orderDO.getOrderSubType() == 1) {
            collectAmountKey = "饿了么外卖";
        } else if (orderDO.getOrderSubType() == 3) {
            collectAmountKey = "京东外卖";
        } else if (orderDO.getOrderSubType() == 5) {
            collectAmountKey = "自营外卖";
        } else if (orderDO.getOrderSubType() == 6) {
            collectAmountKey = "赚餐外卖";
        }
        return collectAmountKey;
    }

    @Override
    public TakeoutStatsDTO getTradeStats(TakeoutStatsQueryDTO takeoutStatsQueryDTO) {
        List<Integer> hideSubTypes = queryAuthorityReportHideSubTypes();
        List<OrderDO> arrayOfOrderDO = list(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getStoreGuid, takeoutStatsQueryDTO.getStoreGuid())
                .eq(OrderDO::getOrderStatus, 100)
                .ne(OrderDO::getRefundStatus, TakeawayRefundStatusEnum.ALL_REFUND.ordinal())
                .notIn(CollectionUtils.isNotEmpty(hideSubTypes), OrderDO::getOrderSubType, hideSubTypes)
                .between(OrderDO::getCompleteTime, takeoutStatsQueryDTO.getBeginTime(), takeoutStatsQueryDTO.getEndTime()));
        if (arrayOfOrderDO == null || arrayOfOrderDO.size() == 0) {
            return TakeoutStatsDTO.empty();
        }

        // 订单数
        int orderCountSum = arrayOfOrderDO.size();
        // 消费人数
        int customerCountSum = arrayOfOrderDO.size();
        // 销售收入
        BigDecimal salesIncomingSum = BigDecimal.ZERO;
        // 订单数详情、销售收入详情、单均消费详情
        Map<String, Integer> orderCountDetail = new HashMap<>();
        Map<String, BigDecimal> salesIncomingDetail = new HashMap<>();
        Map<String, BigDecimal> salesIncomingAvgDetail = new HashMap<>();
        for (OrderDO orderDO : arrayOfOrderDO) {
            String mapKey = getCollectAmountKey(orderDO);
            // 订单数详情
            Integer orderCount = orderCountDetail.computeIfAbsent(mapKey, key -> 0);
            orderCountDetail.put(mapKey, Objects.requireNonNull(orderCount) + 1);
            // 销售收入
            BigDecimal actualPayAmount = orderDO.getCustomerActualPay();
            if (orderDO.getRefundSuccess() != null && orderDO.getRefundSuccess()) {
                BigDecimal customerRefund = (0 != orderDO.getOrderTagStatus()) ? BigDecimal.ZERO
                        : Optional.ofNullable(orderDO.getCustomerRefund()).orElse(BigDecimal.ZERO);
                actualPayAmount = actualPayAmount.subtract(customerRefund);
            }
            salesIncomingSum = salesIncomingSum.add(actualPayAmount);
            // 销售收入详情
            BigDecimal salesIncoming = salesIncomingDetail.computeIfAbsent(mapKey, key -> BigDecimal.ZERO);
            salesIncomingDetail.put(mapKey, Objects.requireNonNull(salesIncoming).add(actualPayAmount));
        }
        // 单均消费详情
        for (String key : salesIncomingDetail.keySet()) {
            BigDecimal salesIncoming = salesIncomingDetail.get(key);
            BigDecimal orderCount = BigDecimal.valueOf(orderCountDetail.get(key));
            BigDecimal salesIncomingAvg = salesIncoming.divide(orderCount, BigDecimal.ROUND_HALF_UP);
            salesIncomingAvgDetail.put(key, salesIncomingAvg);
        }

        TakeoutStatsDTO take = new TakeoutStatsDTO()
                .setOrderCount(orderCountSum)
                .setCustomerCount(customerCountSum)
                .setSalesIncoming(salesIncomingSum)
                .setOrderCountDetail(orderCountDetail)
                .setSalesIncomingDetail(salesIncomingDetail)
                .setSalesIncomingAvgDetail(salesIncomingAvgDetail);

        return take;
    }

    private List<Integer> queryAuthorityReportHideSubTypes() {
        List<String> reportHideNames = queryAuthorityReportHideNames();
        if (CollectionUtils.isEmpty(reportHideNames)) {
            return Lists.newArrayList();
        }
        List<Integer> reportHideSubTypes = reportHideNames.stream().map(e -> {
                    OrderType.TakeoutSubType takeoutSubType = OrderType.TakeoutSubType.ofDesc(e);
                    if (Objects.isNull(takeoutSubType)) {
                        return null;
                    }
                    return takeoutSubType.getType();
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        log.info("reportHideSubTypes:{}", JacksonUtils.writeValueAsString(reportHideSubTypes));
        return reportHideSubTypes;
    }

    /**
     * 查询一体机报表权限
     * 隐藏第三方数据的code
     */
    private List<String> queryAuthorityReportHideNames() {
        // 查询权限code
        List<MenuSourceDTO> sourceList = staffFeignClient.getSourceByUser(String.valueOf(TerminalTypeEnum.TERMINAL_AIO.getCode()));
        if (CollectionUtils.isEmpty(sourceList)) {
            return Lists.newArrayList();
        }
        List<String> sourceCodeList = sourceList.stream()
                .map(MenuSourceDTO::getSourceCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> namesByCodes = AuthorityReportHideEnum.getNamesByCodes(sourceCodeList);
        log.info("需要隐藏的第三方数据:{}", JacksonUtils.writeValueAsString(namesByCodes));
        return namesByCodes;
    }

    private void idempotent(Integer orderStatus) {
        switch (orderStatus) {
            case OrderStatus.TO_SHIP:
                throw new BusinessException("该订单待配送");
            case OrderStatus.CANCELED:
                throw new BusinessException("该订单已取消");
            case OrderStatus.FINISHED:
                throw new BusinessException("该订单已完成");
            case OrderStatus.SHIPPING:
                throw new BusinessException("该订单配送中");
            default:
                throw new BusinessException("订单状态无效");
        }
    }

    private String replyCancelReq(TakeoutOrderDTO takeoutOrderDTO, boolean agree) {
        OrderDO orderDO = orderMapstruct.fromTakeoutOrder(takeoutOrderDTO);
        orderDO.setCancelReplyStaffGuid(UserContextUtils.getUserGuid());
        orderDO.setCancelReplyStaffName(UserContextUtils.getUserName());
        orderDO.setCancelReplyTime(DateTimeUtils.now());
        OrderDO orderInDb = getOne(wrapperByOrderGuid(takeoutOrderDTO.getOrderGuid()));
        boolean toShip = !ObjectUtils.isEmpty(orderInDb.getOrderStatus()) &&
                !ObjectUtils.isEmpty(orderInDb.getExceptionOrderOperation()) &&
                Objects.equals(OrderStatus.TO_SHIP, orderInDb.getOrderStatus()) &&
                Objects.equals(OrderTagStatus.TX_DISAGREE_CANCEL, orderInDb.getExceptionOrderOperation());
        boolean shipping = !ObjectUtils.isEmpty(orderInDb.getOrderStatus()) &&
                !ObjectUtils.isEmpty(orderInDb.getExceptionOrderOperation()) &&
                Objects.equals(OrderStatus.SHIPPING, orderInDb.getOrderStatus()) &&
                Objects.equals(OrderTagStatus.TX_DISAGREE_CANCEL, orderInDb.getExceptionOrderOperation());
        boolean shipFinished = !ObjectUtils.isEmpty(orderInDb.getOrderStatus()) &&
                !ObjectUtils.isEmpty(orderInDb.getExceptionOrderOperation()) &&
                Objects.equals(OrderStatus.SHIP_FINISHED, orderInDb.getOrderStatus()) &&
                Objects.equals(OrderTagStatus.TX_DISAGREE_CANCEL, orderInDb.getExceptionOrderOperation());
        boolean finished = !ObjectUtils.isEmpty(orderInDb.getOrderStatus()) &&
                !ObjectUtils.isEmpty(orderInDb.getExceptionOrderOperation()) &&
                Objects.equals(OrderStatus.FINISHED, orderInDb.getOrderStatus()) &&
                Objects.equals(OrderTagStatus.TX_DISAGREE_CANCEL, orderInDb.getExceptionOrderOperation());
        boolean canceled = !ObjectUtils.isEmpty(orderInDb.getOrderStatus()) &&
                Objects.equals(OrderStatus.CANCELED, orderInDb.getOrderStatus());
        if (canceled) {
            log.warn(ORDER_CANCELLED);
            return ORDER_CANCELLED;
        }
        if (agree) {
            log.info("({})商户同意取消订单，orderId: {}",
                    getPlatformName(orderInDb.getOrderSubType()), orderInDb.getOrderId());
            orderDO.setExceptionOrderOperation(OrderTagStatus.TX_AGREE_CANCEL);
        } else {
            log.info("({})商户不同意取消订单，orderId: {}",
                    getPlatformName(orderInDb.getOrderSubType()), orderInDb.getOrderId());
            if (shipping || shipFinished || toShip || finished) {
                log.warn(NOT_REPEAT_OPERATION_DISAGREE);
                return NOT_REPEAT_OPERATION_DISAGREE;
            }
            orderDO.setExceptionOrderOperation(OrderTagStatus.TX_DISAGREE_CANCEL);
        }
        orderDO.setCancelStaffGuid(orderDO.getCancelReplyStaffGuid());
        orderDO.setCancelStaffName(orderDO.getCancelReplyStaffName());
        updateById(orderDO.setId(orderInDb.getId()));
        UnOrder unOrder = orderMapstruct.toUnOrder(orderInDb);
        decorateReplyOrder(unOrder);
        if(!agree){
            unOrder.setCancelReason(takeoutOrderDTO.getCancelReplyMessage());
        }
        unOrder.setOperateStaffName(orderDO.getCancelReplyStaffName());
        unOrder.setReplyMsgType(agree ? UnOrderReplyMsgType.AGREE_CANCEL_ORDER
                : UnOrderReplyMsgType.DISAGREE_CANCEL_ORDER);
        producerFeignClient.reply(unOrder);
        // 暂时先不使用OrderTagStatus中间状态
//        int status = agree ? OrderTagStatus.TX_AGREE_CANCEL : OrderTagStatus.TX_DISAGREE_CANCEL;
//        updateById(new OrderDO().setOrderTagStatus(status).setId(orderInDb.getId()));
        if (agree) {
            return "订单已成功取消";
        } else {
            return "操作成功";
        }
    }

    private String replyRefundReq(TakeoutOrderDTO takeoutOrderDTO, boolean agree) {
        OrderDO orderDO = orderMapstruct.fromTakeoutOrder(takeoutOrderDTO);
        orderDO.setRefundReplyStaffGuid(UserContextUtils.getUserGuid());
        orderDO.setRefundReplyStaffName(UserContextUtils.getUserName());
        orderDO.setRefundReplyTime(DateTimeUtils.now());
        OrderDO orderInDb = getOne(wrapperByOrderGuid(takeoutOrderDTO.getOrderGuid()));
        boolean toShip = !ObjectUtils.isEmpty(orderInDb.getOrderStatus()) &&
                !ObjectUtils.isEmpty(orderInDb.getExceptionOrderOperation()) &&
                Objects.equals(OrderStatus.TO_SHIP, orderInDb.getOrderStatus()) &&
                Objects.equals(OrderTagStatus.TX_DISAGREE_REFUND, orderInDb.getExceptionOrderOperation());
        boolean shipping = !ObjectUtils.isEmpty(orderInDb.getOrderStatus()) &&
                !ObjectUtils.isEmpty(orderInDb.getExceptionOrderOperation()) &&
                Objects.equals(OrderStatus.SHIPPING, orderInDb.getOrderStatus()) &&
                Objects.equals(OrderTagStatus.TX_DISAGREE_REFUND, orderInDb.getExceptionOrderOperation());
        boolean shipFinished = !ObjectUtils.isEmpty(orderInDb.getOrderStatus()) &&
                !ObjectUtils.isEmpty(orderInDb.getExceptionOrderOperation()) &&
                Objects.equals(OrderStatus.SHIP_FINISHED, orderInDb.getOrderStatus()) &&
                Objects.equals(OrderTagStatus.TX_DISAGREE_REFUND, orderInDb.getExceptionOrderOperation());
        boolean finished = !ObjectUtils.isEmpty(orderInDb.getOrderStatus()) &&
                !ObjectUtils.isEmpty(orderInDb.getExceptionOrderOperation()) &&
                Objects.equals(OrderStatus.FINISHED, orderInDb.getOrderStatus()) &&
                Objects.equals(OrderTagStatus.TX_DISAGREE_REFUND, orderInDb.getExceptionOrderOperation());
        boolean canceled = !ObjectUtils.isEmpty(orderInDb.getOrderStatus()) &&
                Objects.equals(OrderStatus.CANCELED, orderInDb.getOrderStatus());
        if (canceled) {
            log.warn(ORDER_CANCELLED);
            return ORDER_CANCELLED;
        }
        if (agree) {
            log.info("({})商户同意取消订单，orderId: {}",
                    getPlatformName(orderInDb.getOrderSubType()), orderInDb.getOrderId());
            orderDO.setExceptionOrderOperation(OrderTagStatus.TX_AGREE_REFUND);
        } else {
            log.info("({})商户不同意取消订单，orderId: {}",
                    getPlatformName(orderInDb.getOrderSubType()), orderInDb.getOrderId());
            if (shipping || shipFinished || toShip || finished) {
                log.warn(NOT_REPEAT_OPERATION_DISAGREE);
                return NOT_REPEAT_OPERATION_DISAGREE;
            }
            orderDO.setExceptionOrderOperation(OrderTagStatus.TX_DISAGREE_REFUND);
        }
        updateById(orderDO.setId(orderInDb.getId()));
        UnOrder unOrder = orderMapstruct.toUnOrder(orderInDb);
        decorateReplyOrder(unOrder);
        unOrder.setRefundReplyMessage(takeoutOrderDTO.getRefundReplyMessage());
        unOrder.setOperateStaffName(orderDO.getRefundReplyStaffName());
        unOrder.setReplyMsgType(agree ? UnOrderReplyMsgType.AGREE_REFUND_ORDER
                : UnOrderReplyMsgType.DISAGREE_REFUND_ORDER);
        producerFeignClient.reply(unOrder);
        // 暂时先不使用OrderTagStatus中间状态
//        int status = agree ? OrderTagStatus.TX_AGREE_REFUND : OrderTagStatus.TX_DISAGREE_REFUND;
//        updateById(new OrderDO().setOrderTagStatus(status).setId(orderInDb.getId()));
        if (agree) {
            return "订单已成功取消";
        } else {
            return "操作成功";
        }
    }

    private void fillCancelReasonAndStaffName(UnOrder unOrder, OrderDO orderDO, OrderDO orderInDb) {
        if (Objects.isNull(orderInDb)) {
            log.error("订单取消更新取消原因时查询订单报空");
            return;
        }
        if (null == orderInDb.getCancelTime()) {
            orderDO.setCancelTime(DateTimeUtils.now());
        }
        if (StringUtils.isEmpty(orderInDb.getCancelReason())) {
            if (StringUtils.isEmpty(orderDO.getCancelReason())) {
                orderDO.setCancelReason("未知原因");
            }
        } else {
            orderDO.setCancelReason(null);
        }
        if (StringUtils.isEmpty(orderInDb.getCancelStaffName())) {
            if (!StringUtils.isEmpty(unOrder.getCancelRoleName())) {
                orderDO.setCancelStaffName(unOrder.getCancelRoleName());
            } else {
                orderDO.setCancelStaffName("未知用户");
            }
        } else {
            orderDO.setCancelStaffName(null);
        }
    }

    private void copyFieldToOrderInDbDirectly(OrderDO orderDO, OrderDO orderInDb) {
        orderInDb.setCancelAsReject(orderDO.getCancelAsReject());
        orderInDb.setOrderStatus(orderDO.getOrderStatus());
        if (null == orderInDb.getCancelTime()) {
            orderInDb.setCancelTime(orderDO.getCancelTime());
        }
        if (StringUtils.isEmpty(orderInDb.getCancelStaffName())) {
            orderInDb.setCancelStaffName(orderDO.getCancelStaffName());
        }
        if (StringUtils.isEmpty(orderInDb.getCancelReason())) {
            orderInDb.setCancelReason(orderDO.getCancelReason());
        }
    }

    private Wrapper<OrderDO> wrapperByOrderGuid(String orderGuid) {
        return new LambdaQueryWrapper<OrderDO>()
                .select(OrderDO::getId, OrderDO::getOrderId, OrderDO::getOrderStatus,
                        OrderDO::getOrderType, OrderDO::getOrderSubType,
                        OrderDO::getEnterpriseGuid, OrderDO::getStoreGuid,
                        OrderDO::getDeliveryType, OrderDO::getExceptionOrderOperation)
                .eq(OrderDO::getOrderGuid, orderGuid);
    }

    private Wrapper<OrderDO> statusQuery(String orderId) {
        return new LambdaQueryWrapper<OrderDO>().select(OrderDO::getOrderStatus).eq(OrderDO::getOrderId, orderId);
    }

    private Wrapper<OrderDO> wrapperByOrderId(String orderId) {
        return new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getOrderId, orderId);
    }

    private String getPlatformName(UnOrder unOrder) {
        if (OrderType.TakeoutSubType.MT_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            return "美团";
        } else if (OrderType.TakeoutSubType.ELE_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            return "饿了么";
        } else if (OrderType.TakeoutSubType.OWN_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            return "自营外卖";
        } else if (OrderType.TakeoutSubType.TCD_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            return "赚餐外卖";
        } else if (OrderType.TakeoutSubType.JD_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            return "京东外卖";
        }
        return "";
    }

    private Integer getTakeoutType(UnOrder unOrder) {
        if (OrderType.TakeoutSubType.MT_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            return 0;   //美团
        } else if (OrderType.TakeoutSubType.ELE_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            return 1;   //饿了么
        } else if (OrderType.TakeoutSubType.OWN_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            return 2;   //自营外卖
        } else if (OrderType.TakeoutSubType.TCD_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            return 3;   //赚餐外卖
        }   else if (OrderType.TakeoutSubType.JD_TAKEOUT.getType() == unOrder.getOrderSubType()) {
            return 5;   //京东外卖
        }
        return -1;
    }

    private String getPlatformName(OrderDO orderDO) {
        if (OrderType.TakeoutSubType.MT_TAKEOUT.getType() == orderDO.getOrderSubType()) {
            return "美团";
        } else if (OrderType.TakeoutSubType.ELE_TAKEOUT.getType() == orderDO.getOrderSubType()) {
            return "饿了么";
        } else if (OrderType.TakeoutSubType.OWN_TAKEOUT.getType() == orderDO.getOrderSubType()) {
            return "自营外卖";
        } else if (OrderType.TakeoutSubType.TCD_TAKEOUT.getType() == orderDO.getOrderSubType()) {
            return "赚餐外卖";
        } else if (OrderType.TakeoutSubType.JD_TAKEOUT.getType() == orderDO.getOrderSubType()) {
            return "京东外卖";
        }
        return "";
    }

    private String getPlatformName(int orderSubType) {
        if (OrderType.TakeoutSubType.MT_TAKEOUT.getType() == orderSubType) {
            return "美团";
        } else if (OrderType.TakeoutSubType.ELE_TAKEOUT.getType() == orderSubType) {
            return "饿了么";
        } else if (OrderType.TakeoutSubType.OWN_TAKEOUT.getType() == orderSubType) {
            return "自营";
        } else if (OrderType.TakeoutSubType.TCD_TAKEOUT.getType() == orderSubType) {
            return "赚餐";
        } else if (OrderType.TakeoutSubType.JD_TAKEOUT.getType() == orderSubType) {
            return "京东";
        }else {
            return "";
        }
    }

    private Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO(OrderReadDO orderReadDO) {
        List<ItemDO> arrayOfItemDO = orderReadDO.getArrayOfItem();
        // 多对一映射处理
        List<String> guidList = arrayOfItemDO.stream()
                .map(ItemDO::getItemSku)
                .distinct()
                .collect(Collectors.toList());
        List<String> skuGuidList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(guidList)) {
            List<ItemMappingDO> itemMappingDOList = itemMappingMapper.selectList(new LambdaQueryWrapper<ItemMappingDO>()
                    .eq(ItemMappingDO::getTakeoutType, transferType(orderReadDO.getOrderSubType()))
                    .in(ItemMappingDO::getMapperGuid, guidList)
                    .eq(ItemMappingDO::getStoreGuid, orderReadDO.getStoreGuid())
            );
            if (CollectionUtils.isEmpty(itemMappingDOList)) {
                skuGuidList.addAll(guidList);
            } else {
                Map<String, ItemMappingDO> mappingDOMap = itemMappingDOList.stream()
                        .collect(Collectors.toMap(ItemMappingDO::getMapperGuid, s -> s));
                guidList.forEach(guid -> {
                    ItemMappingDO mappingDO = mappingDOMap.get(guid);
                    if (ObjectUtils.isEmpty(mappingDO)) {
                        skuGuidList.add(guid);
                    } else {
                        arrayOfItemDO.forEach(itemDO -> {
                            if (Objects.nonNull(itemDO.getItemSku()) && itemDO.getItemSku().equals(guid)) {
                                itemDO.setItemSku(mappingDO.getErpItemSkuGuid());
                            }
                        });
                        skuGuidList.add(mappingDO.getErpItemSkuGuid());
                    }
                });
            }
        }
        // 去重
        List<String> distinctSkuGuidList = skuGuidList.stream().distinct().collect(Collectors.toList());
        ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(distinctSkuGuidList);
        itemStringListDTO.setStoreGuid(orderReadDO.getStoreGuid());
        List<SkuTakeawayInfoRespDTO> arrayOfSkuTakeawayInfoRespDTO = itemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO);
        log.info("arrayOfSkuTakeawayInfoRespDTO={}", JacksonUtils.writeValueAsString(arrayOfSkuTakeawayInfoRespDTO));
        arrayOfSkuTakeawayInfoRespDTO.forEach(dto -> {
            if (StringUtils.isEmpty(dto.getParentSkuGuid())) {
                dto.setParentSkuGuid(dto.getSkuGuid());
            }
            if (!ObjectUtils.isEmpty(dto.getIsRack()) && NUMBER_ZERO.equals(dto.getIsRack())) {
                dto.setItemName("(下架)" + dto.getItemName());
            }
        });
        return arrayOfSkuTakeawayInfoRespDTO.stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getSkuGuid, Function.identity()));
    }

    private Integer transferType(Integer orderSubType) {
        if(Objects.equals(OrderType.TakeoutSubType.TCD_TAKEOUT.getType(), orderSubType)){
            return 3;
        }
        if(Objects.equals(OrderType.TakeoutSubType.JD_TAKEOUT.getType(), orderSubType)){
            return 5;
        }

        return orderSubType;
    }

    /**
     * 该方法自mapOfSkuTakeawayInfoRespDTO修改
     * 根据订单明细中的erpItemSkuGuid查询对应商品，并不根据当前的映射关系来查询商品
     */
    private Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTOV2(OrderReadDO orderReadDO) {
        List<ItemDO> arrayOfItemDO = orderReadDO.getArrayOfItem();

        Set<String> skuGuids = arrayOfItemDO.stream()
                .map(ItemDO::getErpItemSkuGuid)
                .filter(erpItemSkuGuid -> !StringUtils.isEmpty(erpItemSkuGuid))
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(skuGuids)) {
            return Maps.newHashMap();
        }

        ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(new ArrayList<>(skuGuids));
        itemStringListDTO.setStoreGuid(orderReadDO.getStoreGuid());
        List<SkuTakeawayInfoRespDTO> arrayOfSkuTakeawayInfoRespDTO = itemFeignClient.selectSkuTakeawayInfoRespDTOListV2(itemStringListDTO);
        log.info("arrayOfSkuTakeawayInfoRespDTO={}", JacksonUtils.writeValueAsString(arrayOfSkuTakeawayInfoRespDTO));
        arrayOfSkuTakeawayInfoRespDTO.forEach(dto -> {
            if (StringUtils.isEmpty(dto.getParentSkuGuid())) {
                dto.setParentSkuGuid(dto.getSkuGuid());
            } else {
                dto.setSkuGuid(dto.getParentSkuGuid());
            }
            if (!ObjectUtils.isEmpty(dto.getIsRack()) && NUMBER_ZERO.equals(dto.getIsRack())) {
                dto.setItemName("(下架)" + dto.getItemName());
            }
        });
        return arrayOfSkuTakeawayInfoRespDTO.stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getSkuGuid, Function.identity()));
    }

    private void appendMapOfSkuTakeawayInfo(String storeGuid, Integer orderSubType,
                                            List<ItemDO> arrayOfItemDO,
                                            List<ItemExtendsDO> itemExtendsDOList) {
        try {
            Map<String, List<ItemDO>> groupByItemSkuMap = arrayOfItemDO.stream().collect(Collectors.groupingBy(ItemDO::getItemSku));
            if (MapUtils.isEmpty(groupByItemSkuMap)) {
                return;
            }
            List<String> skuGuidList = new ArrayList<>();
            List<String> mapperGuidList = new ArrayList<>(groupByItemSkuMap.keySet());
            // 查询映射关系
            List<ItemMappingDO> itemMappingDOList = itemMappingMapper.selectList(new LambdaQueryWrapper<ItemMappingDO>()
                    .eq(ItemMappingDO::getTakeoutType, transferType(orderSubType))
                    .in(ItemMappingDO::getMapperGuid, mapperGuidList)
                    .eq(ItemMappingDO::getStoreGuid, storeGuid));
            // 设置erpItemSkuGuid
            handleErpItemSkuGuid(arrayOfItemDO, itemMappingDOList, skuGuidList, mapperGuidList, groupByItemSkuMap);
            List<String> distinctSkuGuidList = skuGuidList.stream().distinct().collect(Collectors.toList());
            // 适配老数据,根据skuGuid查询，无论是根据门店还是品牌商品SkuGuid
            List<SkuInfoRespDTO> parentSkuInfoList = itemFeignClient.findParentSKUS(distinctSkuGuidList);
            Map<String, SkuInfoRespDTO> parentSkuInfoMap = parentSkuInfoList.stream()
                    .collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key1));
            // 重新设置erpItemSkuGuid
            arrayOfItemDO.forEach(e -> {
                if (StringUtils.isEmpty(e.getErpItemSkuGuid())) {
                    return;
                }
                SkuInfoRespDTO parentSkuInfo = parentSkuInfoMap.get(e.getErpItemSkuGuid());
                if (Objects.isNull(parentSkuInfo)) {
                    return;
                }
                e.setErpItemSkuGuid(parentSkuInfo.getParentGuid());
            });
            // 查询对应模式下商品信息
            List<String> finalSkuGuidList = arrayOfItemDO.stream().map(ItemDO::getErpItemSkuGuid).distinct().collect(Collectors.toList());
            ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
            itemStringListDTO.setDataList(finalSkuGuidList);
            itemStringListDTO.setStoreGuid(storeGuid);
            List<SkuInfoRespDTO> skuInfoList = itemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO);
            log.info("根据门店模式查询商品返回={}", JacksonUtils.writeValueAsString(skuInfoList));

            Map<String, SkuInfoRespDTO> skuMap = skuInfoList.stream()
                    .collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key2));
            Map<String, ItemExtendsDO> itemExtendsDOMap = itemExtendsDOList.stream()
                    .collect(Collectors.toMap(ItemExtendsDO::getGuid, Function.identity(), (key1, key2) -> key2));
            for (ItemDO itemDO : arrayOfItemDO) {
                SkuInfoRespDTO skuInfo = skuMap.get(itemDO.getErpItemSkuGuid());
                if (Objects.isNull(skuInfo)) {
                    itemDO.setErpItemSkuGuid(null);
                    continue;
                }
                ItemExtendsDO extendsDO = itemExtendsDOMap.get(itemDO.getItemGuid());
                extendsDO.setErpItemSkuGuid(itemDO.getErpItemSkuGuid());
                extendsDO.setCostPrice(skuInfo.getCostPrice());
                itemDO.setErpItemName(skuInfo.getItemName());
                if (!StringUtils.isEmpty(skuInfo.getName())) {
                    itemDO.setErpItemName(itemDO.getErpItemName() + "(" + skuInfo.getName() + ")");
                }
                itemDO.setErpItemPrice(skuInfo.getSalePrice());
                //套餐商品明细
                itemDO.setListPkg(skuInfo.getListPkg());
                itemDO.setTakeawayAccountingPrice(skuInfo.getTakeawayAccountingPrice());
            }
        } catch (Exception e) {
            log.error("查询绑定商品信息错误:{}", e.getMessage());
            String jsonStr = UserContextUtils.getJsonStr();
            String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
            String traceid = TraceidUtils.getTraceid();
            // 保存异常数据
            EXECUTOR.execute(() -> {
                // 切换数据源
                UserContextUtils.put(jsonStr);
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
                TraceidUtils.setTraceid(traceid);
                autoRecoveryService.createOrderEx(arrayOfItemDO, e.getMessage());
            });
        }
    }

    /**
     * 设置erpItemSkuGuid
     */
    private void handleErpItemSkuGuid(List<ItemDO> arrayOfItemDO,
                                      List<ItemMappingDO> itemMappingDOList,
                                      List<String> skuGuidList,
                                      List<String> mapperGuidList,
                                      Map<String, List<ItemDO>> groupByItemSkuMap) {
        if (CollectionUtils.isEmpty(itemMappingDOList)) {
            arrayOfItemDO.forEach(e -> e.setErpItemSkuGuid(e.getItemSku()));
            skuGuidList.addAll(mapperGuidList);
        } else {
            Map<String, ItemMappingDO> mappingDOMap = itemMappingDOList.stream()
                    .collect(Collectors.toMap(ItemMappingDO::getMapperGuid, s -> s));
            for (Map.Entry<String, List<ItemDO>> entry : groupByItemSkuMap.entrySet()) {
                ItemMappingDO mappingDO = mappingDOMap.get(entry.getKey());
                if (ObjectUtils.isEmpty(mappingDO)) {
                    entry.getValue().forEach(e -> e.setErpItemSkuGuid(entry.getKey()));
                    skuGuidList.add(entry.getKey());
                } else {
                    entry.getValue().forEach(e -> e.setErpItemSkuGuid(mappingDO.getErpItemSkuGuid()));
                    skuGuidList.add(mappingDO.getErpItemSkuGuid());
                }
            }
        }
    }

    /**
     * 调整单-分页查询外卖订单列表
     * 外卖展示近30天 待配送、配送中、已完成的订单数据
     *
     * @param query 关键字，门店guid，分页数据
     * @return 外卖订单列表
     */
    @Override
    public Page<AdjustByTakeawayOrderRespDTO> pageOrderByAdjust(AdjustByOrderListQuery query) {
        if (StringUtils.isEmpty(query.getStoreGuid())) {
            throw new ParameterException("门店Guid不能为空!");
        }
        int count = orderMapper.countAdjustTakeawayOrder(query);
        if(count == 0){
            return new PageAdapter<>(query.getPageSize(), query.getCurrentPage(),0);
        }
        List<OrderDO> arrayOfOrderDO = orderMapper.listAdjustTakeawayOrderPage(query);
        List<AdjustByTakeawayOrderRespDTO> arrayOfTakeawayOrderDTO = orderMapstruct.orderDOs2AdjustOrders(arrayOfOrderDO);
        for (AdjustByTakeawayOrderRespDTO takeoutOrderInDb : arrayOfTakeawayOrderDTO) {
            //若是饿了么，则展示隐私号
            if (takeoutOrderInDb.getOrderSubType() == 1) {
                takeoutOrderInDb.setCustomerPhone(takeoutOrderInDb.getPrivacyPhone());
            }
        }
        PageAdapter<AdjustByTakeawayOrderRespDTO> pageAdapter = new PageAdapter<>();
        pageAdapter.setData(arrayOfTakeawayOrderDTO);
        pageAdapter.setCurrent(query.getCurrentPage());
        pageAdapter.setSize(query.getPageSize());
        pageAdapter.setTotal(count);
        return pageAdapter;
    }

    /**
     * 调整单-查询外卖订单商品
     * 标记商品是否调整过
     *
     * @param query 订单guid，门店，交易模式
     * @return 订单商品信息列表
     */
    @Override
    public AdjustByOrderRespDTO listOrderItem(AdjustByOrderItemQuery query) {
        String orderGuid = query.getOrderGuid();
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException("订单guid不能为空");
        }
        // 查询外卖订单
        OrderReadDO orderDoInDb = baseMapper.getOrderDetail(new OrderDO().setOrderGuid(orderGuid));
        if (null == orderDoInDb) {
            log.error("根据orderGuid[{}]没查到订单详情", orderGuid);
            throw new BusinessException("根据orderGuid:" + orderGuid + "没查到订单详情");
        }
        AdjustByOrderRespDTO adjustByOrderRespDTO = new AdjustByOrderRespDTO();
        adjustByOrderRespDTO.setOrderGuid(orderGuid);
        adjustByOrderRespDTO.setOrderNo(orderDoInDb.getOrderViewId());
        adjustByOrderRespDTO.setActuallyPayFee(orderDoInDb.getShopTotal());
        adjustByOrderRespDTO.setTradeMode(TradeModeEnum.TAKEOUT.getMode());
        adjustByOrderRespDTO.setCheckoutStaffName("");
        adjustByOrderRespDTO.setGmtCreate(orderDoInDb.getGmtCreate());
        // 商品明细
        List<ItemDO> arrayOfItem = orderDoInDb.getArrayOfItem();
        List<AdjustByOrderItemRespDTO> orderItemList = Lists.newArrayList();
        for (ItemDO itemDO : arrayOfItem) {
            AdjustByOrderItemRespDTO itemRespDTO = new AdjustByOrderItemRespDTO();
            itemRespDTO.setOrderItemGuid(itemDO.getItemGuid());
            itemRespDTO.setItemName(itemDO.getItemName());
            // 因为itemName已经包含了规格名称,所以返给前端就只返回""
            itemRespDTO.setSkuName("");
            itemRespDTO.setItemType(ItemTypeEnum.SINGLE_UNWEIGH.getCode());
            if (!StringUtils.isEmpty(itemRespDTO.getSkuName())) {
                itemRespDTO.setItemType(ItemTypeEnum.MULTI_SKU.getCode());
            }
            itemRespDTO.setOtherAddPrice(BigDecimal.ZERO);
            itemRespDTO.setPropertyName(itemDO.getItemProperty());
            itemRespDTO.setPrice(itemDO.getItemPrice());
            itemRespDTO.setCurrentCount(itemDO.getItemCount());
            itemRespDTO.setTotalPrice(itemDO.getItemCount().multiply(itemDO.getItemPrice()));
            itemRespDTO.setUnit("");
            itemRespDTO.setIsAdjustItem(itemDO.getIsAdjustItem());
            itemRespDTO.setSkuGuid("");
            itemRespDTO.setItemGuid("");
            itemRespDTO.setErpItemSkuGuid(Objects.isNull(itemDO.getErpItemSkuGuid()) ? "" : itemDO.getErpItemSkuGuid());
            orderItemList.add(itemRespDTO);
        }
        adjustByOrderRespDTO.setOrderItemList(orderItemList);
        return adjustByOrderRespDTO;
    }

    @Override
    public void updateBatchIsAdjustItem(String orderGuid) {
        UpdateWrapper<OrderDO> uw = new UpdateWrapper<>();
        uw.lambda().set(OrderDO::getAdjustState, true);
        uw.lambda().eq(OrderDO::getOrderGuid, orderGuid);
        update(uw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adjustTakeoutOrder(AdjustTakeoutOrderReqDTO reqDTO) {
        // 查询外卖订单和订单商品明细
        OrderReadDO orderDoInDb = baseMapper.getOrderDetail(new OrderDO().setOrderGuid(reqDTO.getOrderGuid()));
        if (Objects.isNull(orderDoInDb)) {
            throw new BusinessException("外卖订单不存在！");
        }
        // 校验订单状态是否可调整
        if (OrderStatus.FINISHED != orderDoInDb.getOrderStatus() && OrderStatus.SHIP_FINISHED != orderDoInDb.getOrderStatus()
                && OrderStatus.SHIPPING != orderDoInDb.getOrderStatus() && OrderStatus.TO_SHIP != orderDoInDb.getOrderStatus()) {
            throw new BusinessException("此订单当前状态不支持调整！");
        }
        // 校验订单商品明细是否存在已调整
        Map<String, ItemDO> itemMap = orderDoInDb.getArrayOfItem().stream().collect(Collectors.toMap(ItemDO::getItemGuid, Function.identity(), (key1, key2) -> key1));
        for (String orderItemGuid : reqDTO.getOrderItemGuids()) {
            ItemDO itemDO = itemMap.get(orderItemGuid);
            if (Objects.isNull(itemDO)) {
                continue;
            }
            if (Objects.nonNull(itemDO.getIsAdjustItem()) && itemDO.getIsAdjustItem()) {
                throw new BusinessException("存在已调整的订单明细，请重新选择商品！");
            }
        }
        updateBatchIsAdjustItem(reqDTO.getOrderGuid());
        itemService.updateBatchIsAdjustItem(reqDTO.getOrderItemGuids());
    }

    @Override
    public void updateThirdCarrierId(String thirdCarrierId, String orderId) {
        orderMapper.updateThirdCarrierIdByOrderId(thirdCarrierId, orderId);
    }

    @Override
    public void delayAutoAcceptOrder(TakeoutOrderDTO takeoutOrderDTO) {
        String jsonStr = UserContextUtils.getJsonStr();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        CompletableFuture.runAsync(() -> {
            UserContextUtils.put(jsonStr);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            // 查询当前门店漏单订单
            Set<String> orderGuids = autoAcceptOrderService.getOrderQueue(takeoutOrderDTO.getStoreGuid());
            if (CollectionUtils.isEmpty(orderGuids)) {
                return;
            }
            log.info("轮询查询自动接单漏单订单,reqDTO:{},orderGuids:{}", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                    JacksonUtils.writeValueAsString(orderGuids));
            // 后台接单处理
            orderGuids.forEach(orderGuid -> {
                try {
                    TakeoutOrderDTO acceptOrder = new TakeoutOrderDTO();
                    acceptOrder.setDeviceId(takeoutOrderDTO.getDeviceId());
                    acceptOrder.setDeviceType(takeoutOrderDTO.getDeviceType());
                    acceptOrder.setOrderGuid(orderGuid);
                    acceptOrder.setAcceptStaffGuid(takeoutOrderDTO.getAcceptStaffGuid());
                    acceptOrder.setAcceptStaffName(takeoutOrderDTO.getAcceptStaffName());
                    acceptOrder.setDistributionType(takeoutOrderDTO.getDistributionType());
                    acceptOrder(acceptOrder);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("外卖自动接单补偿异常,orderGuid:{},e:{}", orderGuid, e.getMessage());
                    if ("该订单已取消".equals(e.getMessage()) || "该订单已完成".equals(e.getMessage())
                            || "该订单配送中".equals(e.getMessage()) || "订单状态无效".equals(e.getMessage())
                            || "操作失败，订单状态不合法".equals(e.getMessage()) || "用户申诉退款只能由客服进行操作".equals(e.getMessage())
                            || "业务失败：不存在此订单".equals(e.getMessage()) || "该订单待配送".equals(e.getMessage())
                            || "业务失败：门店未绑定".equals(e.getMessage()) || "操作失败，订单已确认".equals(e.getMessage())) {
                        // 自动接单移除队列
                        OrderDO orderDO = new OrderDO();
                        orderDO.setStoreGuid(takeoutOrderDTO.getStoreGuid());
                        orderDO.setOrderGuid(orderGuid);
                        autoAcceptOrderService.removeOrder(orderDO);
                    }
                }
            });
        }, autoAcceptOrderThreadPool);

    }

    @Override
    public void updateOrderRefundStatus(String orderGuid, Integer refundStatus) {
        UpdateWrapper<OrderDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(OrderDO::getOrderGuid, orderGuid);
        update(new OrderDO().setRefundStatus(refundStatus), uw);
    }

    @Override
    public void updateCallOrder(OrderCallDTO dto) {
        log.info("修改电信订单呼叫状态入参，{}", JacksonUtils.writeValueAsString(dto));
        dynamicHelper.changeDatasource(dto.getEnterpriseGuid());
        // 构造userInfo，以便其他服务能获取到ErpGuid
        UserContextUtils.putErp(dto.getEnterpriseGuid());
        OrderCallDO orderCallDO = orderCallService.getOne(new LambdaQueryWrapper<OrderCallDO>()
                .eq(OrderCallDO::getGuid, dto.getGuid()));
        log.info("orderCallDO，{}", JacksonUtils.writeValueAsString(orderCallDO));
        orderCallDO.setCallStatus(dto.getCallStatus());
        boolean up = orderCallService.updateById(orderCallDO);
        log.info("修改电信订单呼叫状态，{}", up);
    }

    /**
     * 更新订单退款状态
     */
    private void updateOrderRefundStatus(String orderGuid) {
        // 查询此订单明细
        List<ItemDO> itemList = itemService.getItemList(orderGuid);
        BigDecimal itemTotalCount = itemList.stream().map(ItemDO::getItemCount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal refundTotalCount = itemList.stream().map(ItemDO::getRefundCount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 更新订单退款状态
        if (refundTotalCount.compareTo(BigDecimal.ZERO) > 0) {
            int refundStatus = TakeawayRefundStatusEnum.REFUND_ING.ordinal();
            if (refundTotalCount.compareTo(itemTotalCount) == 0) {
                // 如果购买数量 = 退款数量 则表示全部退款
                refundStatus = TakeawayRefundStatusEnum.ALL_REFUND.ordinal();
            }
            updateOrderRefundStatus(orderGuid, refundStatus);
        }
    }

    /**
     * 异常数据处理
     */
    private void abnormalDataHandler(List<ItemDO> itemList, List<Long> clearErpItemSkuGuidList) {
        List<ItemDO> abnormalList = itemList.stream()
                .filter(e -> clearErpItemSkuGuidList.contains(e.getId())).collect(Collectors.toList());
        try {
            if (CollectionUtils.isNotEmpty(abnormalList)) {
                List<AbnormalDataDO> abnormalDataDOList = itemMapstruct.itemDOList2AbnormalDataDOList(abnormalList);
                abnormalDataService.saveBatch(abnormalDataDOList);
            }
        } catch (Exception e) {
            log.error("外卖绑定异常数据处理异常,e:{}", e.getMessage());
            log.error("外卖绑定异常数据处理异常,clearErpItemSkuGuidList:{}", JacksonUtils.writeValueAsString(clearErpItemSkuGuidList));
        }
    }

    @Override
    public void orderPrepared(TakeoutOrderDTO takeoutOrderDTO) {
        if (StringUtils.isEmpty(takeoutOrderDTO)){
            throw new BusinessException("订单guid不能为空！");
        }
        OrderDO order = getOne(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getOrderGuid, takeoutOrderDTO.getOrderGuid()));
        if(order == null){
            throw new BusinessException("订单不存在！");
        }
        //判断是否已点击出餐
        if(OrderTagStatus.ORDER_OPERATE_PREPARED == order.getOrderTagStatus()){
            throw new BusinessException("订单已点击出餐！");
        }
        //判断订单状态是否允许出餐
        if(OrderStatus.TO_SHIP != order.getOrderStatus()){
            throw new BusinessException("订单状态有误！");
        }
        //判断接单时间是否大于三分钟 大于三分钟后才能进行出餐
        if(order.getAcceptTime() == null || order.getAcceptTime().plusMinutes(3L).isAfter(LocalDateTime.now())){
            throw new BusinessException("接单三分钟后才能出餐！");
        }
        //调用出餐
        TakeoutOrderOperateDTO takeoutOrderOperate = new TakeoutOrderOperateDTO();
        takeoutOrderOperate.setOrderId(order.getOrderId());
        takeoutOrderOperate.setStoreGuid(order.getStoreGuid());
        if(Objects.equals(OrderType.TakeoutSubType.JD_TAKEOUT.getTypeInteger(), takeoutOrderDTO.getOrderSubType())){
            BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(takeoutOrderDTO.getStoreGuid());
            takeoutOrderOperate.setBrandGuid(brandDTO.getGuid());
            takeoutOrderOperate.setOperator(UserContextUtils.getUserName());
        }
        producerFeignClient.orderOperate(takeoutOrderDTO.getOrderSubType(),takeoutOrderOperate);
        //设置订单操作出餐标识
        order.setOrderTagStatus(OrderTagStatus.ORDER_OPERATE_PREPARED);
        updateById(order);
    }
}
