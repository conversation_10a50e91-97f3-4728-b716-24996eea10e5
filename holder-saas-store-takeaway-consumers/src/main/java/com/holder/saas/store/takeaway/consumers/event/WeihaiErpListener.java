package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.service.rpc.CloudEnterpriseFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holder.saas.store.takeaway.consumers.utils.ThrowableExtUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseSupplyChainConfigDTO;
import com.holderzone.saas.store.dto.weihai.WeihaiCommonResponse;
import com.holderzone.saas.store.dto.weihai.WeihaiSaleOutRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;
import java.util.Optional;


/**
 * 微海供应链 库存扣减消费
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.WEIHAI_ERP_MESSAGE_TOPIC,
        tags = RocketMqConfig.WEIHAI_ERP_REDUCE_TAG,
        consumerGroup = RocketMqConfig.WEIHAI_ERP_MESSAGE_GROUP)
public class WeihaiErpListener extends AbstractRocketMqConsumer<RocketMqTopic, WeihaiSaleOutRequest> {

    @Value("${weihai.erp.host}")
    private String weihaiErpHost;

    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";

    private static final Integer SUCCESS_CODE = 200;

    @Resource
    private CloudEnterpriseFeignClient cloudEnterpriseFeignClient;

    @Resource
    private DynamicHelper dynamicHelper;

    @Resource
    @Qualifier(value = "weihaiRestTemplate")
    private RestTemplate weihaiRestTemplate;

    @Override
    public boolean consumeMsg(WeihaiSaleOutRequest request, MessageExt messageExt) {
        String orderId = request.getOrderNo();
        if (log.isInfoEnabled()) {
            log.info("订单[{}]扣减/退回微海库存，入参:{}", orderId, JacksonUtils.writeValueAsString(request));
        }
        UserContextUtils.put(messageExt.getProperty(RocketMqConfig.USER_INFO));
        // 切库
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (log.isInfoEnabled()) {
            log.info("扣减/退回微海库存:订单[{}]根据enterpriseGuid({})切换数据源", orderId, enterpriseGuid);
        }
        dynamicHelper.changeDatasource(enterpriseGuid);

        try {
            // 查询当前企业配置
            EnterpriseQueryDTO enterpriseQueryDTO = new EnterpriseQueryDTO();
            enterpriseQueryDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            EnterpriseDTO enterprise = cloudEnterpriseFeignClient.findEnterprise(enterpriseQueryDTO);
            log.info("当前企业信息, enterprise:{}", JacksonUtils.writeValueAsString(enterprise));
            Boolean supportWeiHaiSupplyChain = enterprise.getSupportWeiHaiSupplyChain();
            if (!Boolean.TRUE.equals(supportWeiHaiSupplyChain)) {
                return true;
            }
            EnterpriseSupplyChainConfigDTO supplyChainConfig = enterprise.getSupplyChainConfig();
            if (Objects.isNull(supplyChainConfig) || StringUtils.isEmpty(supplyChainConfig.getYicanAppId())) {
                log.error("微海供应链未配置,enterpriseGuid:{}", UserContextUtils.getEnterpriseGuid());
                return true;
            }
            String unique = IDUtils.nextId();
            String sign = sign(request, supplyChainConfig.getYicanAppSecret(), unique);

            log.info("调用微海供应链库存：订单guid：{}，appId:{}, unique:{}, sign:{}, 入参{}",
                    orderId,
                    supplyChainConfig.getYicanAppId(),
                    unique,
                    sign,
                    JacksonUtils.writeValueAsString(request));
            WeihaiCommonResponse<?> response = request(supplyChainConfig, weihaiErpHost + "/api/cost-open/openApi/powerHolder/order/sale/out",
                    unique, sign, request);
            log.info("调用微海供应链库存：返回{}", response);
        } catch (Exception e) {
            log.error("订单({})调用微海库存消费发生异常：{}", orderId, ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            dynamicHelper.removeThreadLocalDatabaseInfo();
            UserContextUtils.remove();
        }
        return true;
    }

    private WeihaiCommonResponse<?> request(EnterpriseSupplyChainConfigDTO supplyChainConfig,
                                            String url, String unique, String sign,
                                            WeihaiSaleOutRequest request) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        // 添加签名相关的头信息
        httpHeaders.add("appId", supplyChainConfig.getYicanAppId());
        httpHeaders.add("unique", unique);
        httpHeaders.add("Authorization", sign);

        HttpEntity<Object> httpEntity = new HttpEntity<>(JacksonUtils.writeValueAsString(request), httpHeaders);
        ResponseEntity<WeihaiCommonResponse<?>> exchange;
        try {
            log.info("请求入参 -> url:{},httpHeaders:{},data:{}", url,
                    JacksonUtils.writeValueAsString(httpHeaders.toSingleValueMap()),
                    JacksonUtils.writeValueAsString(request));
            ParameterizedTypeReference<WeihaiCommonResponse<?>> typeRef = new ParameterizedTypeReference<WeihaiCommonResponse<?>>() {
            };
            exchange = weihaiRestTemplate.exchange(url, HttpMethod.POST,
                    httpEntity, typeRef);
            log.info("请求返回 -> Http StatusCode: {}, rsp body:{}", exchange.getStatusCode(),
                    JacksonUtils.writeValueAsString(exchange.getBody()));
        } catch (Exception e) {
            log.error("出现异常情况 ->  req body：{}, exception：{}", JacksonUtils.writeValueAsString(httpEntity.getBody()),
                    e.getMessage(), e);
            throw new BusinessException("请求微海异常");
        }
        if (exchange.getStatusCodeValue() != HttpStatus.OK.value()
                || (exchange.getBody() != null && !SUCCESS_CODE.equals(exchange.getBody().getStatus()))) {
            log.error("请求微海返回错误 ->  url: {}, data: {}，HttpStatus：{}，rsp body：{}",
                    url, JacksonUtils.writeValueAsString(request), exchange.getStatusCode(), JacksonUtils.writeValueAsString(exchange.getBody()));
            throw new BusinessException("请求微海异常");
        }
        return exchange.getBody();
    }


    private String sign(WeihaiSaleOutRequest request, String secretKey, String unique) {
        if (StringUtils.isEmpty(secretKey)) {
            log.error("secretKey is null");
            throw new BusinessException("secretKey is null");
        }
        // 获取请求体
        String body = JacksonUtils.writeValueAsString(request);
        // 请求方法和URL
        String method = "post";
        String requestUri = "/openApi/powerHolder/order/sale/out";

        // 按照签名规则：unique + body + secretKey + method + requestUri
        String stringToSign = unique + body + secretKey + method + requestUri;
        log.info("微海API待签名字符串: {}", stringToSign);
        try {
            // 创建HmacSHA256签名对象
            Mac sha256HMAC = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA256_ALGORITHM);
            sha256HMAC.init(secretKeySpec);
            // 计算签名
            byte[] data = sha256HMAC.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            // Base64编码
            String signature = Base64.getUrlEncoder().encodeToString(data);
            log.info("微海API签名结果: {}", signature);
            return signature;
        } catch (Exception e) {
            log.error("微海API签名生成失败", e);
            throw new BusinessException(String.format("签名生成错误: %s",
                    Optional.ofNullable(e.getMessage()).orElse(e.getClass().getSimpleName())));
        }
    }

}
