package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.service.DeliveryService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class DeliveryServiceImpl implements DeliveryService {

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    @Autowired
    public DeliveryServiceImpl(DefaultRocketMqProducer defaultRocketMqProducer) {
        this.defaultRocketMqProducer = defaultRocketMqProducer;
    }


    @Override
    public void doDelivery(UnOrder unOrder) {
        doYiChengAsync(unOrder, RocketMqConfig.DISTRIBUTION_START_TAG);
    }

    private void doYiChengAsync(Object object, String stockTag) {
        Message message = new Message(
                RocketMqConfig.DISTRIBUTION_MESSAGE_TOPIC,
                stockTag,
                JacksonUtils.toJsonByte(object)
        );
        message.getProperties().put(
                RocketMqConfig.USER_INFO,
                UserContextUtils.getJsonStr()
        );
        defaultRocketMqProducer.sendMessage(message);
    }
}