package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * hst_takeout_practice
 *
 * <AUTHOR>
@Data
@NoArgsConstructor
@TableName("hst_takeout_practice")
public class PracticeDO implements Serializable {

    private static final long serialVersionUID = 4876023245827370131L;

    /**
     *
     */
    private Long id;

    /**
     *
     */
    private String orderItemPracticeGuid;

    /**
     *
     */
    private String orderItemGuid;

    /**
     * 费用
     */
    private BigDecimal fees;

    /**
     * 个数
     */
    private Integer feesCount;

    /**
     * 合计
     */
    private BigDecimal subTotal;

    /**
     *
     */
    private LocalDateTime gmtCreate;

    /**
     *
     */
    private LocalDateTime gmtModified;
}