package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.DiscountDO;
import com.holder.saas.store.takeaway.consumers.mapper.DiscountMapper;
import com.holder.saas.store.takeaway.consumers.mapstruct.OrderMapstruct;
import com.holder.saas.store.takeaway.consumers.service.DiscountService;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 外卖菜品折扣服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
@Service
public class DiscountServiceImpl extends ServiceImpl<DiscountMapper, DiscountDO> implements DiscountService {


    private final DynamicHelper dynamicHelper;

    private final OrderMapstruct orderMapstruct;


    public DiscountServiceImpl(DynamicHelper dynamicHelper, OrderMapstruct orderMapstruct) {
        this.dynamicHelper = dynamicHelper;
        this.orderMapstruct = orderMapstruct;
    }




}
