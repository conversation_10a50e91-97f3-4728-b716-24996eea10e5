package com.holder.saas.store.takeaway.consumers.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.ConfigDO;
import com.holder.saas.store.takeaway.consumers.mapper.ConfigMapper;
import com.holder.saas.store.takeaway.consumers.mapstruct.ConfigMapstruct;
import com.holder.saas.store.takeaway.consumers.service.ConfigService;
import com.holder.saas.store.takeaway.consumers.utils.DistributedUtils;
import com.holderzone.framework.base.dto.log.LogDTO;
import com.holderzone.framework.exception.ParamException;
import com.holderzone.framework.log.busines.DefaultLogBuilder;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.log.busines.RecordType;
import com.holderzone.saas.store.dto.log.LogContentObjectDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutAutoRcvDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutConfigDTO;
import com.holderzone.sdk.event.LogPublisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 外卖配置服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
@Service
public class ConfigServiceImpl extends ServiceImpl<ConfigMapper, ConfigDO> implements ConfigService {

    private final LogPublisher logPublisher;

    private final ConfigMapstruct configMapstruct;

    @Autowired
    public ConfigServiceImpl(LogPublisher logPublisher, ConfigMapstruct configMapstruct) {
        this.logPublisher = logPublisher;
        this.configMapstruct = configMapstruct;
    }

    @Override
    public TakeoutConfigDTO selectStoreConfig(TakeoutConfigDTO takeoutConfigDTO) {
        ConfigDO configDO = selectByStoreGuid(takeoutConfigDTO.getStoreGuid());
        takeoutConfigDTO.setAutoOrder(Objects.isNull(configDO) ? Boolean.FALSE : configDO.getAutoOrder());
        takeoutConfigDTO.setPrintCancelOrder(Objects.isNull(configDO) ? Boolean.FALSE : configDO.getPrintCancelOrder());
        return takeoutConfigDTO;
    }

    @Override
    public Boolean saveStoreConfig(TakeoutConfigDTO takeoutConfigDTO) {
        ConfigDO configDO = configMapstruct.fromTakeoutConfig(takeoutConfigDTO);
        ConfigDO storeConfigInDb = selectByStoreGuid(configDO.getStoreGuid());
        if (Objects.isNull(storeConfigInDb)) {
            save(configDO.setConfigGuid(DistributedUtils.id())
                    .setStaffGuid(takeoutConfigDTO.getUserGuid())
                    .setStaffName(takeoutConfigDTO.getUserName()));
        } else {
            updateById(configDO.setId(storeConfigInDb.getId()));
        }
        return true;
    }

    @Override
    public TakeoutAutoRcvDTO selectStoreAutoRcvConfig(TakeoutAutoRcvDTO takeoutAutoRcvDTO) {
        ConfigDO configDO = selectByStoreGuid(takeoutAutoRcvDTO.getStoreGuid());
        takeoutAutoRcvDTO.setAutoOrder(null == configDO ? false : configDO.getAutoOrder());
        return takeoutAutoRcvDTO;
    }

    @Override
    public Boolean saveStoreAutoRcvConfig(TakeoutAutoRcvDTO takeoutAutoRcvDTO) {
        ConfigDO configDO = configMapstruct.fromTakeoutAutoRcv(takeoutAutoRcvDTO);
        ConfigDO storeConfigInDb = selectByStoreGuid(configDO.getStoreGuid());

        if (null == storeConfigInDb) {
            save(configDO.setConfigGuid(DistributedUtils.id())
                    .setStaffGuid(takeoutAutoRcvDTO.getUserGuid())
                    .setStaffName(takeoutAutoRcvDTO.getUserName()));
        } else {
            updateById(configDO.setId(storeConfigInDb.getId()));
        }

        operationLog(takeoutAutoRcvDTO);

        return true;
    }

    @Override
    public ConfigDO selectByStoreGuid(String storeGuid) {
        return getOne(new LambdaQueryWrapper<ConfigDO>().eq(ConfigDO::getStoreGuid, storeGuid));
    }

    /**
     * 操作日志
     *
     * @param takeoutAutoRcvDTO
     */
    private void operationLog(TakeoutAutoRcvDTO takeoutAutoRcvDTO) {
        LogContentObjectDTO logContentObjectDTO = new LogContentObjectDTO();
        logContentObjectDTO.setButton("开关");

        List<String> strs = new ArrayList<>();
        strs.add("外卖设置");
        logContentObjectDTO.setOperationTarget(strs);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("外卖自动接单", takeoutAutoRcvDTO.getAutoOrder() ? "开" : "关");
        logContentObjectDTO.setTdata(jsonObject.toJSONString());

        LogDTO logDTO = new DefaultLogBuilder(takeoutAutoRcvDTO.getUserName(),
                "/takeaway/setStoreAutomaticallyReceive", OperatorType.UPDATE, Platform.OTHER, RecordType.NORMAL)
                .setContentAfter(logContentObjectDTO).build();
        logDTO.setModule("外卖设置");
        try {
            logPublisher.send(this, logDTO);
        } catch (ParamException e) {
            e.printStackTrace();
        }
    }
}
