package com.holder.saas.store.takeaway.consumers.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@AllArgsConstructor
@Getter
public enum TakeoutItemMappingEnum {

    MT_TAKEOUT(0, "美团外卖"),

    ELE_TAKEOUT(1, "饿了么外卖"),

    OWN_TAKEOUT(2, "自营外卖"),

    ZC_TAKEOUT(3, "赚餐"),

    TIKTOK_TAKEOUT(4, "抖音外卖"),

    JD_TAKEOUT(5, "京东外卖");

    private int type;

    /**
     * 描述
     */
    private String desc;

}
