package com.holder.saas.store.takeaway.consumers.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.StoreBindOrderDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.mapper.ItemMappingMapper;
import com.holder.saas.store.takeaway.consumers.service.ErpService;
import com.holder.saas.store.takeaway.consumers.service.ItemClientService;
import com.holder.saas.store.takeaway.consumers.service.rpc.CloudEnterpriseFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.MdmServiceClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.TradeClientService;
import com.holder.saas.store.takeaway.consumers.utils.HttpUtil;
import com.holder.saas.store.takeaway.consumers.utils.OrderContextUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseSupplyChainConfigDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.deposit.req.DepositDish;
import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import com.holderzone.saas.store.dto.erp.SkuInfo;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.mdm.MDMResult;
import com.holderzone.saas.store.dto.order.common.PushOrderBillsBO;
import com.holderzone.saas.store.dto.order.common.SalesOrderDisheseBO;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.dto.takeaway.request.StockStoreBindReqOrderDTO;
import com.holderzone.saas.store.dto.takeaway.response.StockStoreBindResqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderItemRespDTO;
import com.holderzone.saas.store.dto.weihai.WeihaiSaleOutRequest;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class ErpServiceImpl implements ErpService {
    @Value("${mdm.host:#{null}}")
    private String mdmRequestHost;

    private final ItemClientService itemClientService;

    private final TradeClientService tradeClientService;

    private final DefaultRocketMqProducer defaultRocketMqProducer;


    private final ProducerFeignClient producerFeignClient;

    private final ItemMappingMapper itemMappingMapper;

    @Resource
    private CloudEnterpriseFeignClient cloudEnterpriseFeignClient;

    @Resource
    private MdmServiceClient mdmServiceClient;

    public static final String UN_MAPPING_ITEM_LOG = "菜品未关联，storeGuid:{}, orderGuid:{}, itemGuid:{}, itemSku{}";

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Value("${erp.host}")
    private String erpHost;

    @Autowired
    public ErpServiceImpl(ItemClientService itemClientService, DefaultRocketMqProducer defaultRocketMqProducer,
                          ProducerFeignClient producerFeignClient, ItemMappingMapper itemMappingMapper,
                          TradeClientService tradeClientService) {
        this.itemClientService = itemClientService;
        this.defaultRocketMqProducer = defaultRocketMqProducer;
        this.producerFeignClient = producerFeignClient;
        this.itemMappingMapper = itemMappingMapper;
        this.tradeClientService = tradeClientService;
    }

    /**
     * fixme 修改为mq的方式调用内部erp服务，如果内部erp服务未提供mq的方式，联系相关责任人添加
     *
     * @param orderReadDO
     * @param mapOfSkuTakeawayInfoRespDTO
     */
    @Override
    public void reduceStockForOrder(OrderReadDO orderReadDO, Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        // 当映射商品为空时，不进行扣减库存
        if (MapUtils.isEmpty(mapOfSkuTakeawayInfoRespDTO)) {
            return;
        }

        String orderContext = OrderContextUtils.getOrderContext();
        String orderId = orderReadDO.getOrderId();
        OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setOrderId(orderId);
        orderSkuDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        orderSkuDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        orderSkuDTO.setOperatorGuid(Optional.ofNullable(orderReadDO.getAcceptStaffGuid()).orElse("商家版用户"));
        orderSkuDTO.setOperatorName(Optional.ofNullable(orderReadDO.getAcceptStaffName()).orElse("商家版用户"));

        // 商品查询
        List<String> itemGuidList = mapOfSkuTakeawayInfoRespDTO.values().stream()
                .map(SkuTakeawayInfoRespDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        ItemStringListDTO dto = new ItemStringListDTO();
        dto.setDataList(itemGuidList);
        List<ItemInfoRespDTO> itemInfoList = itemClientService.listPkgItemInfo(dto);
        log.info("itemInfoList={}", JacksonUtils.writeValueAsString(itemInfoList));
        Map<String, ItemInfoRespDTO> itemMap = itemInfoList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, i -> i));
        List<String> pkgItemGuidList = new ArrayList<>();

        // 构建映射商品集合
        List<SkuInfo> skuInfoList = buildMappingSkuInfoList(orderReadDO, pkgItemGuidList, mapOfSkuTakeawayInfoRespDTO, itemMap);

        // 套餐商品处理
        Map<String, List<SkuInfo>> skuInfoGroupBySkuGuidMap = skuInfoList.stream()
                .collect(Collectors.groupingBy(SkuInfo::getSkuGuid));
        Map<String, SkuInfo> skuInfoMap = Maps.newHashMap();
        for (Map.Entry<String, List<SkuInfo>> entry : skuInfoGroupBySkuGuidMap.entrySet()) {
            BigDecimal skuCount = entry.getValue().stream().map(SkuInfo::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            skuInfoMap.put(entry.getKey(), new SkuInfo(entry.getKey(), skuCount));
        }

        if (!CollectionUtils.isEmpty(pkgItemGuidList)) {
            pkgItemGuidList.forEach(pkg -> {
                ItemInfoRespDTO itemInfo = itemMap.get(pkg);
                log.info("itemInfo={} pkg={}", JacksonUtils.writeValueAsString(itemInfo), pkg);
                List<SubgroupWebRespDTO> subgroupList = itemInfo.getSubgroupList();
                String itemSkuGuid = itemInfo.getItemSkuGuid();
                SkuInfo skuInfo = skuInfoMap.get(itemSkuGuid);
                subgroupList.forEach(sub -> {
                    if (0 == sub.getPickNum()) {
                        sub.getSubItemSkuList().forEach(si ->
                                // 这里可能会导致SkuInfo里有sku相同的对象
                                skuInfoList.add(new SkuInfo(si.getSkuGuid(), si.getItemNum().multiply(skuInfo.getCount())))
                        );
                    }
                });
                skuInfoList.remove(skuInfo);
            });
        }
        orderSkuDTO.setSkuList(skuInfoList);

        log.info("订单号[{}]erp扣减库存，userInfo:{},请求参数：{}", orderId, orderContext, JacksonUtils.writeValueAsString(orderSkuDTO));
        doReduceStockAsync(orderSkuDTO, RocketMqConfig.ERP_REDUCE_TAG);
        log.info("订单号[{}]erp扣减库存，userInfo:{} 处理完毕", orderId, orderContext);
    }

    /**
     * 构建映射门店商品映射集合
     */
    private List<SkuInfo> buildMappingSkuInfoList(OrderReadDO orderReadDO, List<String> pkgItemGuidList,
                                                  Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO,
                                                  Map<String, ItemInfoRespDTO> itemMap) {
        return orderReadDO.getArrayOfItem().stream()
                .flatMap(itemDO -> {
                    String itemSkuGuid = itemDO.getErpItemSkuGuid();
                    if (StringUtils.isEmpty(itemSkuGuid)) {
                        log.info(UN_MAPPING_ITEM_LOG,
                                orderReadDO.getStoreGuid(),
                                orderReadDO.getOrderGuid(),
                                itemDO.getItemGuid(), itemDO.getItemSku()
                        );
                        return Stream.empty();
                    }
                    SkuTakeawayInfoRespDTO sku = mapOfSkuTakeawayInfoRespDTO.get(itemSkuGuid);
                    if (sku == null) {
                        log.info(UN_MAPPING_ITEM_LOG,
                                orderReadDO.getStoreGuid(),
                                orderReadDO.getOrderGuid(),
                                itemDO.getItemGuid(), itemDO.getItemSku()
                        );
                        return Stream.empty();
                    }
                    // 套餐商品特殊处理
                    ItemInfoRespDTO itemInfo = itemMap.get(sku.getItemGuid());
                    if (ObjectUtils.isEmpty(itemInfo)) {
                        log.info("未查询到商品信息 itemGuid={} itemInfo={}", sku.getItemGuid(), JacksonUtils.writeValueAsString(itemInfo));
                    } else {
                        if (Objects.equals(ItemTypeEnum.PKG.getCode(), itemInfo.getItemType())) {
                            pkgItemGuidList.add(sku.getItemGuid());
                            itemInfo.setItemSkuGuid(itemSkuGuid);
                            log.info("套餐商品单独设置 itemGuid={} itemSkuGuid={}", sku.getItemGuid(), itemSkuGuid);
                        }
                    }
                    return Stream.of(new SkuInfo(itemSkuGuid, itemDO.getActualItemCount()));
                })
                .collect(Collectors.toList());
    }


    private void doReduceStockAsync(Object object, String stockTag) {
        Message message = new Message(
                RocketMqConfig.ERP_MESSAGE_TOPIC,
                stockTag,
                JacksonUtils.toJsonByte(object)
        );
        message.getProperties().put(
                RocketMqConfig.USER_INFO,
                UserContextUtils.getJsonStr()
        );
        defaultRocketMqProducer.sendMessage(message);
    }

    /**
     * fixme 修改为内部MQ的方式调用远程MDM的ERP
     *
     * @param orderReadDO
     * @param mapOfSkuTakeawayInfoRespDTO
     * @throws IOException
     */
    @Override
    public PushOrderBillsBO reduceStockForOrderMDM(OrderReadDO orderReadDO, Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        // 当映射商品为空时，不进行扣减库存
        if (MapUtils.isEmpty(mapOfSkuTakeawayInfoRespDTO)) {
            return null;
        }
        String orderContext = OrderContextUtils.getOrderContext();
        PushOrderBillsBO pushOrderBillsBO = buildPushOrderBillsBO(orderReadDO, mapOfSkuTakeawayInfoRespDTO);
        log.info("{}mdm扣减库存，请求参数：{}",
                orderContext, JacksonUtils.writeValueAsString(pushOrderBillsBO));
        doMDMReduceStockAsync(pushOrderBillsBO, RocketMqConfig.MDM_REDUCE_TAG);
        log.info("{}mdm扣减库存，处理完毕", orderContext);
        return pushOrderBillsBO;
    }


    private PushOrderBillsBO buildPushOrderBillsBO(OrderReadDO orderReadDO,
                                                   Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        PushOrderBillsBO pushOrderBillsBO = new PushOrderBillsBO();
        pushOrderBillsBO.setSalesOrderId(orderReadDO.getOrderId());
        pushOrderBillsBO.setSerialNumber(orderReadDO.getOrderViewId());
        pushOrderBillsBO.setStoreId(orderReadDO.getStoreGuid());
        pushOrderBillsBO.setCreateTime(orderReadDO.getGmtCreate());
        pushOrderBillsBO.setConsumeTotal(orderReadDO.getTotal());
        pushOrderBillsBO.setTradeMode(2);
        pushOrderBillsBO.setCheckStaffId(orderReadDO.getAcceptStaffGuid());
        pushOrderBillsBO.setCheckOutStaffName(orderReadDO.getAcceptStaffName());
        pushOrderBillsBO.setBusinessDay(orderReadDO.getBusinessDay());
        pushOrderBillsBO.setCheckTotal(orderReadDO.getCustomerActualPay());
        pushOrderBillsBO.setPaymentDiscountTotal(orderReadDO.getDiscountTotal());
        pushOrderBillsBO.setAdditionalFeesTotal(BigDecimal.ZERO);
        pushOrderBillsBO.setIsRecovery(false);
        //去查询库存扣减的门店
        stockStoreBind(pushOrderBillsBO);

        // 商品查询
        List<String> itemGuidList = mapOfSkuTakeawayInfoRespDTO.values().stream()
                .map(SkuTakeawayInfoRespDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        ItemStringListDTO dto = new ItemStringListDTO();
        dto.setDataList(itemGuidList);
        List<ItemInfoRespDTO> itemInfoList = itemClientService.listPkgItemInfo(dto);
        Map<String, ItemInfoRespDTO> itemMap = itemInfoList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, i -> i));
        Set<String> pkgItemGuidList = new HashSet<>();

        // 构建映射商品集合
        List<SalesOrderDisheseBO> saleDishheseBOList = buildMappingSaleDishBOList(orderReadDO, pkgItemGuidList,
                mapOfSkuTakeawayInfoRespDTO, itemMap);

        log.info("saleDishheseBOList={}", JacksonUtils.writeValueAsString(saleDishheseBOList));
        Map<String, List<SalesOrderDisheseBO>> saleGroupByStoreDishesIdMap = saleDishheseBOList.stream()
                .collect(Collectors.groupingBy(SalesOrderDisheseBO::getStoreDishesId));
        Map<String, SalesOrderDisheseBO> disheseBOMap = Maps.newHashMap();
        for (Map.Entry<String, List<SalesOrderDisheseBO>> entry : saleGroupByStoreDishesIdMap.entrySet()) {
            BigDecimal saleCheckCount = entry.getValue().stream()
                    .map(SalesOrderDisheseBO::getCheckCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            SalesOrderDisheseBO disheseBO = new SalesOrderDisheseBO();
            BeanUtils.copyProperties(entry.getValue().get(0), disheseBO);
            disheseBO.setCheckCount(saleCheckCount);
            disheseBOMap.put(entry.getKey(), disheseBO);
        }
        log.info("disheseBOMap={}", disheseBOMap);
        log.info("pkgItemGuidList={}", JacksonUtils.writeValueAsString(pkgItemGuidList));
        if (!CollectionUtils.isEmpty(pkgItemGuidList)) {
            pkgItemGuidList.forEach(pkg -> {
                ItemInfoRespDTO itemInfo = itemMap.get(pkg);
                log.info("itemInfo={}", JacksonUtils.writeValueAsString(itemInfo));
                List<SubgroupWebRespDTO> subgroupList = itemInfo.getSubgroupList();
                String itemSkuGuid = itemInfo.getItemSkuGuid();
                SalesOrderDisheseBO salesOrderDisheseBO = disheseBOMap.get(itemSkuGuid);
                subgroupList.forEach(sub -> {
                    if (0 == sub.getPickNum()) {
                        sub.getSubItemSkuList().forEach(si ->
                                {
                                    // 这里可能会导致SkuInfo里有sku相同的对象，并且还有价格上面的异常，暂未处理
                                    SalesOrderDisheseBO subSalesOrderDisheseBO = new SalesOrderDisheseBO(si.getSkuGuid(),
                                            si.getSalePrice(),
                                            si.getItemNum().multiply(salesOrderDisheseBO.getCheckCount()),
                                            si.getItemNum().multiply(salesOrderDisheseBO.getCheckCount())
                                                    .multiply(salesOrderDisheseBO.getPrice()),
                                            si.getItemName(),
                                            si.getCode(),
                                            si.getUnit(),
                                            salesOrderDisheseBO.getPackageDishes());
                                    subSalesOrderDisheseBO.setDishesSkuName(si.getSkuName());
                                    subSalesOrderDisheseBO.setDishesErpName(si.getItemName());
                                    if (StringUtils.isNotEmpty(si.getSkuName())) {
                                        subSalesOrderDisheseBO.setDishesErpName(subSalesOrderDisheseBO.getDishesErpName()
                                                + "(" + si.getSkuName() + ")");
                                    }
                                    subSalesOrderDisheseBO.setDishTypeName(si.getTypeName());
                                    subSalesOrderDisheseBO.setItemPrice(subSalesOrderDisheseBO.getPrice()
                                            .multiply(subSalesOrderDisheseBO.getCheckCount()));
                                    subSalesOrderDisheseBO.setDiscountTotalPrice(subSalesOrderDisheseBO.getItemPrice());
                                    subSalesOrderDisheseBO.setPackageDishes(0);
                                    subSalesOrderDisheseBO.setSubItemFlag(1);
                                    saleDishheseBOList.add(subSalesOrderDisheseBO);
                                }
                        );
                    }
                });
                saleDishheseBOList.remove(salesOrderDisheseBO);
            });
        }
        List<String> storeDishIds = saleDishheseBOList.stream()
                .map(SalesOrderDisheseBO::getStoreDishesId)
                .collect(Collectors.toList());
        Map<String, String> erpSkuGuids = itemClientService.getErpSkuGuids(new SingleDataDTO("jh", storeDishIds));
        saleDishheseBOList.forEach(e -> e.setDishesId(erpSkuGuids.get(e.getStoreDishesId())));
        pushOrderBillsBO.setSalesOrderDisheses(saleDishheseBOList);
        return pushOrderBillsBO;
    }

    @Override
    public void reduceStockForOrderWeihaiErp(PushOrderBillsBO pushOrderBillsBO) {
        if (Objects.isNull(pushOrderBillsBO)) {
            log.warn("没有映射商品，不推送erp");
            return;
        }
        // 查询企业是否支持微海供应链
        EnterpriseQueryDTO enterpriseQueryDTO = new EnterpriseQueryDTO();
        enterpriseQueryDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        EnterpriseDTO enterprise = cloudEnterpriseFeignClient.findEnterprise(enterpriseQueryDTO);
        log.info("当前企业信息, enterprise:{}", JacksonUtils.writeValueAsString(enterprise));
        Boolean supportWeiHaiSupplyChain = enterprise.getSupportWeiHaiSupplyChain();
        if (!Boolean.TRUE.equals(supportWeiHaiSupplyChain)) {
            return;
        }
        EnterpriseSupplyChainConfigDTO supplyChainConfig = enterprise.getSupplyChainConfig();
        if (Objects.isNull(supplyChainConfig) || StringUtils.isEmpty(supplyChainConfig.getYicanAppId())) {
            log.error("微海供应链未配置,enterpriseGuid:{}", UserContextUtils.getEnterpriseGuid());
            return;
        }
        String orderContext = OrderContextUtils.getOrderContext();
        WeihaiSaleOutRequest weihaiSaleOutRequest = transferSaleOutRequest(pushOrderBillsBO);
        log.info("{}微海库存，请求参数：{}",
                orderContext, JacksonUtils.writeValueAsString(weihaiSaleOutRequest));
        doWeihaiErpReduceStockAsync(weihaiSaleOutRequest, RocketMqConfig.WEIHAI_ERP_REDUCE_TAG);
        log.info("{}微海库存，处理完毕", orderContext);
    }

    private WeihaiSaleOutRequest transferSaleOutRequest(PushOrderBillsBO pushOrderBillsBO) {
        // 构建请求对象
        String diningTableName = "外卖-外卖";
        WeihaiSaleOutRequest request = new WeihaiSaleOutRequest();
        String[] diningTableNameSplit = diningTableName.split("-");
        request.setAreaName(diningTableNameSplit[0]);
        request.setBillingType("外卖");
        request.setBusinessDate(pushOrderBillsBO.getBusinessDay().format(DATE_FORMATTER));
        request.setDeskName(diningTableNameSplit[1]);
        request.setDiscountAmount(pushOrderBillsBO.getPaymentDiscountTotal());
        request.setFlowAmount(pushOrderBillsBO.getConsumeTotal());
        request.setIsAdjustOrder(0);
        request.setLcDepartId(transferLcDepartId(pushOrderBillsBO.getStoreId()));
        request.setOpenTime(pushOrderBillsBO.getCreateTime().format(DATETIME_FORMATTER));
        request.setOrderNo(pushOrderBillsBO.getSalesOrderId());
        request.setPaidAmount(pushOrderBillsBO.getCheckTotal());
        request.setPassengerFlow("1");
        request.setProductList(transferProductDetails(request, pushOrderBillsBO));
        return request;
    }

    private List<WeihaiSaleOutRequest.ProductDetail> transferProductDetails(WeihaiSaleOutRequest request, PushOrderBillsBO pushOrderBillsBO) {
        List<SalesOrderDisheseBO> salesOrderDishes = pushOrderBillsBO.getSalesOrderDisheses();
        // 过滤套餐项
        salesOrderDishes.removeIf(e -> e.getPackageDishes() == 1);
        return salesOrderDishes.stream().map(item -> {
            WeihaiSaleOutRequest.ProductDetail detail = new WeihaiSaleOutRequest.ProductDetail();
            detail.setFoodCode(item.getDishesId());
            detail.setFoodOriginalAmount(item.getItemPrice());
            BigDecimal discountTotalPrice = Objects.isNull(item.getDiscountTotalPrice()) ? BigDecimal.ZERO : item.getDiscountTotalPrice();
            detail.setFoodIncomeAmount(discountTotalPrice);
            detail.setFoodDiscountAmount(item.getItemPrice().subtract(discountTotalPrice).setScale(2, RoundingMode.HALF_UP));
            detail.setFoodName(item.getDishesErpName());
            detail.setFoodStyle(item.getDishTypeName());
            detail.setIsAdjustOrder(request.getIsAdjustOrder());
            detail.setIsCost(1);
            detail.setLcDepartId(request.getLcDepartId());
            detail.setOrderNo(request.getOrderNo());
            detail.setPrice(item.getPrice().stripTrailingZeros().toPlainString());
            detail.setSalesQuantity(item.getCheckCount().stripTrailingZeros().toPlainString());
            detail.setSpecification(item.getCheckUnit());
            return detail;
        }).collect(Collectors.toList());
    }

    /**
     * 构建销售出库请求参数
     */
    private WeihaiSaleOutRequest buildSaleOutRequest(DepositErpSyncDTO depositErpSyncDTO) {
        // 构建请求
        return transferSaleOutRequest(depositErpSyncDTO);
    }

    private WeihaiSaleOutRequest transferSaleOutRequest(DepositErpSyncDTO depositErpSyncDTO) {
        // 构建请求对象
        String diningTableName = "外卖-外卖";
        WeihaiSaleOutRequest request = new WeihaiSaleOutRequest();
        String[] diningTableNameSplit = diningTableName.split("-");
        request.setAreaName(diningTableNameSplit[0]);
        request.setBillingType("外卖");
        request.setBusinessDate(depositErpSyncDTO.getBusinessDate().format(DATE_FORMATTER));
        request.setDeskName(diningTableNameSplit[1]);
        request.setDiscountAmount(depositErpSyncDTO.getOrderFee().subtract(depositErpSyncDTO.getActuallyPayFee()));
        request.setFlowAmount(depositErpSyncDTO.getOrderFee());
        request.setIsAdjustOrder(1);
        request.setLcDepartId(transferLcDepartId(depositErpSyncDTO.getStoreId()));
        request.setOpenTime(depositErpSyncDTO.getOrderCreateTime().format(DATETIME_FORMATTER));
        request.setOrderNo(depositErpSyncDTO.getOrderGuid());
        request.setPaidAmount(depositErpSyncDTO.getActuallyPayFee());
        request.setPassengerFlow("1");
        request.setProductList(transferProductDetails(request, depositErpSyncDTO));
        return request;
    }

    private List<WeihaiSaleOutRequest.ProductDetail> transferProductDetails(WeihaiSaleOutRequest request, DepositErpSyncDTO depositErpSyncDTO) {
        List<DepositDish> depositDishes = depositErpSyncDTO.getDepositDishes();
        // 过滤套餐项
        depositDishes.removeIf(e -> e.getItemType() == 1 || e.getItemType() == 5);
        return depositDishes.stream().map(item -> {
            WeihaiSaleOutRequest.ProductDetail detail = new WeihaiSaleOutRequest.ProductDetail();
            detail.setFoodCode(item.getSkuId());
            detail.setFoodOriginalAmount(item.getPrice().multiply(item.getSkuCount()));
            detail.setFoodIncomeAmount(item.getPrice().multiply(item.getSkuCount()));
            detail.setFoodDiscountAmount(BigDecimal.ZERO);
            detail.setFoodName(item.getItemName());
            if (StringUtils.isNotEmpty(item.getSkuName())) {
                detail.setFoodName(detail.getFoodName()
                        + "(" + item.getSkuName() + ")");
            }
            detail.setFoodStyle(item.getTypeName());
            detail.setIsAdjustOrder(request.getIsAdjustOrder());
            detail.setIsCost(1);
            detail.setLcDepartId(request.getLcDepartId());
            detail.setOrderNo(request.getOrderNo());
            detail.setPrice(item.getPrice().stripTrailingZeros().toPlainString());
            BigDecimal skuCount = item.getSkuCount().abs();
            if (item.getType() == 0) {
                skuCount = skuCount.negate();
            }
            detail.setSalesQuantity(skuCount.stripTrailingZeros().toPlainString());
            detail.setSpecification(item.getUnit());
            return detail;
        }).collect(Collectors.toList());
    }

    /**
     * 构建映射门店商品映射集合
     */
    private List<SalesOrderDisheseBO> buildMappingSaleDishBOList(OrderReadDO orderReadDO, Set<String> pkgItemGuidList,
                                                                 Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO,
                                                                 Map<String, ItemInfoRespDTO> itemMap) {
        return orderReadDO.getArrayOfItem().stream()
                .flatMap(saleItemDO -> {
                    String itemSkuGuid = saleItemDO.getErpItemSkuGuid();
                    if (StringUtils.isEmpty(itemSkuGuid)) {
                        log.info(UN_MAPPING_ITEM_LOG,
                                orderReadDO.getStoreGuid(),
                                orderReadDO.getOrderGuid(),
                                saleItemDO.getItemGuid(), itemSkuGuid
                        );
                        return Stream.empty();
                    }
                    SkuTakeawayInfoRespDTO sku = mapOfSkuTakeawayInfoRespDTO.get(itemSkuGuid);
                    if (sku == null) {
                        log.info(UN_MAPPING_ITEM_LOG,
                                orderReadDO.getStoreGuid(),
                                orderReadDO.getOrderGuid(),
                                saleItemDO.getItemGuid(), itemSkuGuid
                        );
                        return Stream.empty();
                    }
                    // 套餐商品特殊处理
                    ItemInfoRespDTO itemInfo = itemMap.get(sku.getItemGuid());
                    if (ObjectUtils.isEmpty(itemInfo)) {
                        log.info("未查询到商品信息 itemGuid={} itemInfo={}", sku.getItemGuid(), JacksonUtils.writeValueAsString(itemInfo));
                    } else {
                        if (Objects.equals(ItemTypeEnum.PKG.getCode(), itemInfo.getItemType())) {
                            pkgItemGuidList.add(sku.getItemGuid());
                            itemInfo.setItemSkuGuid(itemSkuGuid);
                            log.info("套餐商品单独设置 itemGuid={} itemSkuGuid={}", sku.getItemGuid(), itemSkuGuid);
                        }
                    }
                    BigDecimal itemTotal = saleItemDO.getActualItemCount().multiply(saleItemDO.getErpItemPrice())
                            .setScale(2, RoundingMode.HALF_UP);
                    SalesOrderDisheseBO salesOrderDisheseBO = new SalesOrderDisheseBO(itemSkuGuid, saleItemDO.getErpItemPrice(),
                            saleItemDO.getActualItemCount(), itemTotal, saleItemDO.getItemName(), saleItemDO.getItemCode(),
                            "份", 0);
                    salesOrderDisheseBO.setDishesErpName(saleItemDO.getErpItemName());
                    salesOrderDisheseBO.setDishesSkuName(sku.getSkuName());
                    salesOrderDisheseBO.setDishTypeName(sku.getTypeName());
                    salesOrderDisheseBO.setItemPrice(itemTotal);
                    salesOrderDisheseBO.setCheckUnit(sku.getUnit());
                    salesOrderDisheseBO.setDiscountTotalPrice(itemTotal);
                    salesOrderDisheseBO.setPackageDishes(Objects.equals(itemInfo.getItemType(), ItemTypeEnum.PKG.getCode())
                            || Objects.equals(itemInfo.getItemType(), ItemTypeEnum.GROUP.getCode()) ? 1 : 0);
                    return Stream.of(salesOrderDisheseBO);
                })
                .collect(Collectors.toList());
    }

    private void stockStoreBind(PushOrderBillsBO orderReadDO) {
        try {
            StockStoreBindResqDTO storeBindDO = producerFeignClient.getBindStockStore(orderReadDO.getStoreId());
            if (ObjectUtil.isNotNull(storeBindDO)) {
                StockStoreBindReqOrderDTO stockStoreBindReqOrderDTO = new StockStoreBindReqOrderDTO();
                stockStoreBindReqOrderDTO.setOrderId(orderReadDO.getSalesOrderId());
                stockStoreBindReqOrderDTO.setBranchStoreGuid(orderReadDO.getStoreId());
                stockStoreBindReqOrderDTO.setStoreGuid(storeBindDO.getBranchStoreGuid());
                producerFeignClient.bindStockStoreOrder(stockStoreBindReqOrderDTO);

                orderReadDO.setBranchStoreGuid(orderReadDO.getStoreId());
                orderReadDO.setBranchStoreName("");
                orderReadDO.setStoreId(storeBindDO.getBranchStoreGuid());
            }
        } catch (Exception e) {
            log.warn("查询库存绑定门店失败");
        }
    }

    private void addStockStoreBind(PushOrderBillsBO orderReadDO) {
        try {
            StoreBindOrderDO storeBindDO = producerFeignClient.getBindStockStoreOrder(orderReadDO.getStoreId(), orderReadDO.getSalesOrderId());
            if (ObjectUtil.isNotNull(storeBindDO)) {
                orderReadDO.setBranchStoreGuid(orderReadDO.getBranchStoreGuid());
                orderReadDO.setBranchStoreName("");
                orderReadDO.setStoreId(storeBindDO.getStoreGuid());
            }
        } catch (Exception e) {
            log.warn("查询库存绑定门店订单失败");
        }
    }


    private String getStockBindStore(String storeGuid) {
        StockStoreBindResqDTO storeBindDO = producerFeignClient.getBindStockStore(storeGuid);
        if (Objects.nonNull(storeBindDO)) {
            String branchStoreGuid = storeBindDO.getBranchStoreGuid();
            if (!StringUtils.isEmpty(branchStoreGuid)) {
                storeGuid = branchStoreGuid;
            }
        }
        return storeGuid;
    }

    private void doMDMReduceStockAsync(Object object, String stockTag) {
        Message message = new Message(
                RocketMqConfig.MDM_MESSAGE_TOPIC,
                stockTag,
                JacksonUtils.toJsonByte(object)
        );
        message.getProperties().put(
                RocketMqConfig.USER_INFO,
                UserContextUtils.getJsonStr()
        );
        defaultRocketMqProducer.sendMessage(message);
    }

    private void doWeihaiErpReduceStockAsync(Object object, String stockTag) {
        Message message = new Message(
                RocketMqConfig.WEIHAI_ERP_MESSAGE_TOPIC,
                stockTag,
                JacksonUtils.toJsonByte(object)
        );
        message.getProperties().put(
                RocketMqConfig.USER_INFO,
                UserContextUtils.getJsonStr()
        );
        defaultRocketMqProducer.sendMessage(message);
    }


    @Override
    public void addStockForOrderMDM(OrderReadDO orderReadDO, Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) throws IOException {
        // 判断订单是否有调整
        if (Objects.nonNull(orderReadDO.getAdjustState()) && orderReadDO.getAdjustState() == 1) {
            addStockForAdjustOrderBefore(orderReadDO);
            return;
        }

        PushOrderBillsBO pushOrderBillsBO = buildPushOrderBillsBO(orderReadDO, mapOfSkuTakeawayInfoRespDTO);
        pushOrderBillsBO.setIsRecovery(true);
        List<SalesOrderDisheseBO> salesOrderDisheses = pushOrderBillsBO.getSalesOrderDisheses();
        List<SalesOrderDisheseBO> mdmSalesOrderDisheses = salesOrderDisheses.stream()
                .filter(e -> Objects.isNull(e.getSubItemFlag()))
                .collect(Collectors.toList());
        pushOrderBillsBO.setSalesOrderDisheses(mdmSalesOrderDisheses);

        try {
            String httpRequestUrl = String.format("%s/api/mdm/Store/bill", mdmRequestHost).intern();
            log.info("MDM库存增加httpRequestUrl={},pushOrderBillsBO={}", httpRequestUrl, JacksonUtils.writeValueAsString(pushOrderBillsBO));
            String result = HttpUtil.doPostJson(httpRequestUrl, JacksonUtils.writeValueAsString(pushOrderBillsBO));
            log.info("MDM库存增加result={}", result);
        } catch (IOException e) {
            log.error("MDM库存增加异常, e:", e);
        }

        pushOrderBillsBO.setSalesOrderDisheses(salesOrderDisheses);
        pushOrderBillsBO.setIsRecovery(true);
        pushOrderBillsBO.setPaymentDiscountTotal(BigDecimal.ZERO);
        pushOrderBillsBO.setCheckTotal(BigDecimal.ZERO);
        pushOrderBillsBO.setConsumeTotal(BigDecimal.ZERO);
        List<SalesOrderDisheseBO> subSalesOrderDisheses = salesOrderDisheses.stream()
                .filter(e -> Objects.isNull(e.getPackageDishes()) || e.getPackageDishes() == 0)
                .collect(Collectors.toList());
        // 清0
        subSalesOrderDisheses.forEach(e -> {
            e.setCheckCount(BigDecimal.ZERO);
            e.setItemPrice(BigDecimal.ZERO);
            e.setPrice(BigDecimal.ZERO);
            e.setDiscountTotalPrice(BigDecimal.ZERO);
            e.setPracticeSubTotal(BigDecimal.ZERO);
        });
        pushOrderBillsBO.setSalesOrderDisheses(subSalesOrderDisheses);
        reduceStockForOrderWeihaiErp(pushOrderBillsBO);
    }

    @Override
    public void addStockForOrderMDM(OrderReadDO orderReadDO, List<ItemDO> oldItemList, List<ItemDO> items,
                                    Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        // 追加商品套餐信息
        appendSubgroupList(mapOfSkuTakeawayInfoRespDTO);
        List<ItemDO> arrayOfItem = orderReadDO.getArrayOfItem();
        Map<String, ItemDO> arrayOfItemMap = arrayOfItem.stream()
                .collect(Collectors.toMap(ItemDO::getItemGuid, Function.identity(), (key1, key2) -> key1));
        Map<String, ItemDO> arrayOfOldItemMap = oldItemList.stream()
                .collect(Collectors.toMap(ItemDO::getItemGuid, Function.identity(), (key1, key2) -> key1));
        for (ItemDO item : items) {
            ItemDO itemDO = arrayOfItemMap.get(item.getItemGuid());
            item.setActualItemCount(itemDO.getActualItemCount());
            item.setErpItemSkuGuid(itemDO.getErpItemSkuGuid());
            item.setItemCount(itemDO.getItemCount());
        }
        items = items.stream().filter(e -> StringUtils.isNotEmpty(e.getErpItemSkuGuid())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            log.info("无可扣减的商品库存, orderReadDO:{}", JacksonUtils.writeValueAsString(orderReadDO));
            return;
        }
        Map<String, BigDecimal> adjustMap = Maps.newHashMap();
        for (ItemDO item : items) {
            ItemDO itemDb = arrayOfOldItemMap.get(item.getItemGuid());
            // 本次退款数量
            BigDecimal currentRefundCount = item.getRefundCount().subtract(Optional.ofNullable(itemDb.getRefundCount()).orElse(BigDecimal.ZERO));
            if (!BigDecimalUtil.greaterThanZero(currentRefundCount)) {
                continue;
            }
            log.info("当前商品退款数量(外卖方),item:{}, currentRefundCount:{}", JacksonUtils.writeValueAsString(itemDb),
                    currentRefundCount);
            BigDecimal addStockCount = item.getActualItemCount().divide(item.getItemCount(), 2, RoundingMode.DOWN)
                    .multiply(currentRefundCount);
            SkuTakeawayInfoRespDTO skuInfoRespDTO = mapOfSkuTakeawayInfoRespDTO.get(item.getErpItemSkuGuid());
            if (Objects.nonNull(skuInfoRespDTO) && Objects.equals(skuInfoRespDTO.getItemType(), ItemTypeEnum.PKG.getCode())) {
                // 套餐
                List<SubItemSkuWebRespDTO> subItemSkuList = skuInfoRespDTO.getSubgroupList().stream()
                        .flatMap(e -> e.getSubItemSkuList().stream()).collect(Collectors.toList());
                for (SubItemSkuWebRespDTO subItem : subItemSkuList) {
                    BigDecimal subItemCount = subItem.getItemNum().multiply(addStockCount);
                    BigDecimal count = adjustMap.getOrDefault(subItem.getSkuGuid(), BigDecimal.ZERO);
                    adjustMap.put(subItem.getSkuGuid(), subItemCount.add(count));
                }
                continue;
            }
            // 非套餐
            BigDecimal stockCount = adjustMap.getOrDefault(item.getErpItemSkuGuid(), BigDecimal.ZERO);
            adjustMap.put(item.getErpItemSkuGuid(), stockCount.add(addStockCount));
        }
        log.info("退款构建adjustMap：{}", JacksonUtils.writeValueAsString(adjustMap));
        modifyErpRepertory(orderReadDO, adjustMap);
    }


    /**
     * 追加商品套餐信息
     */
    private void appendSubgroupList(Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        if (MapUtils.isEmpty(mapOfSkuTakeawayInfoRespDTO)) {
            return;
        }
        // 查询套餐
        List<String> itemGuidList = mapOfSkuTakeawayInfoRespDTO.values().stream()
                .map(SkuTakeawayInfoRespDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        ItemStringListDTO dto = new ItemStringListDTO();
        dto.setDataList(itemGuidList);
        List<ItemInfoRespDTO> itemInfoList = itemClientService.listPkgItemInfo(dto);
        Map<String, ItemInfoRespDTO> itemMap = itemInfoList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, Function.identity(), (key1, key2) -> key1));
        for (Map.Entry<String, SkuTakeawayInfoRespDTO> entry : mapOfSkuTakeawayInfoRespDTO.entrySet()) {
            SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = entry.getValue();
            if (ItemTypeEnum.PKG.getCode() == skuTakeawayInfoRespDTO.getItemType()) {
                ItemInfoRespDTO itemInfoRespDTO = itemMap.get(skuTakeawayInfoRespDTO.getItemGuid());
                if (Objects.nonNull(itemInfoRespDTO) && CollectionUtils.isNotEmpty(itemInfoRespDTO.getSubgroupList())) {
                    skuTakeawayInfoRespDTO.setSubgroupList(itemInfoRespDTO.getSubgroupList());
                }
            }
        }
    }

    @Override
    public void addStockForOrderWeihaiErp(DepositErpSyncDTO erpSyncDTO) {
        if (Objects.isNull(erpSyncDTO) || CollectionUtils.isEmpty(erpSyncDTO.getDepositDishes())) {
            log.warn("没有映射商品，不推送erp");
            return;
        }
        // 查询企业是否支持微海供应链
        EnterpriseQueryDTO enterpriseQueryDTO = new EnterpriseQueryDTO();
        enterpriseQueryDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        EnterpriseDTO enterprise = cloudEnterpriseFeignClient.findEnterprise(enterpriseQueryDTO);
        log.info("当前企业信息, enterprise:{}", JacksonUtils.writeValueAsString(enterprise));
        Boolean supportWeiHaiSupplyChain = enterprise.getSupportWeiHaiSupplyChain();
        if (!Boolean.TRUE.equals(supportWeiHaiSupplyChain)) {
            return;
        }
        EnterpriseSupplyChainConfigDTO supplyChainConfig = enterprise.getSupplyChainConfig();
        if (Objects.isNull(supplyChainConfig) || StringUtils.isEmpty(supplyChainConfig.getYicanAppId())) {
            log.error("微海供应链未配置,enterpriseGuid:{}", UserContextUtils.getEnterpriseGuid());
            return;
        }
        String orderContext = OrderContextUtils.getOrderContext();
        WeihaiSaleOutRequest weihaiSaleOutRequest = buildSaleOutRequest(erpSyncDTO);
        log.info("{}微海调整库存，请求参数：{}",
                orderContext, JacksonUtils.writeValueAsString(weihaiSaleOutRequest));
        doWeihaiErpReduceStockAsync(weihaiSaleOutRequest, RocketMqConfig.WEIHAI_ERP_REDUCE_TAG);
        log.info("{}微海调整库存，处理完毕", orderContext);
    }

    private void addStockForAdjustOrderBefore(OrderReadDO orderReadDO) {
        // 如果有调整, 则采用调整单入库，不采用反结账入库
        List<ItemDO> arrayOfItem = orderReadDO.getArrayOfItem();
        List<ItemDO> erpItemList = arrayOfItem.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getErpItemSkuGuid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(erpItemList)) {
            return;
        }
        // 回退erp库存Map
        Map<String, BigDecimal> adjustMap = Maps.newHashMap();
        Map<String, ItemDO> adjustItemMap = erpItemList.stream().filter(e -> Objects.nonNull(e.getIsAdjustItem()) && e.getIsAdjustItem())
                .collect(Collectors.toMap(ItemDO::getItemGuid, Function.identity(), (key1, key2) -> key2));
        if (MapUtils.isNotEmpty(adjustItemMap)) {
            List<String> adjustOrderItemGuids = new ArrayList<>(adjustItemMap.keySet());
            // 删除原订单明细已经调整过的明细
            erpItemList.removeIf(e -> adjustOrderItemGuids.contains(e.getItemGuid()));
            // 查询对应调整之后的商品明细
            List<Long> orderItemGuids = adjustOrderItemGuids.stream().map(Long::valueOf).collect(Collectors.toList());
            List<AdjustByOrderItemRespDTO> orderItemRespList = tradeClientService.listByOrderItemGuids(new AdjustOrderQueryDTO()
                    .setOrderItemGuids(orderItemGuids));
            if (CollectionUtils.isNotEmpty(orderItemRespList)) {
                // 构建回退库存
                Map<Long, List<AdjustByOrderItemRespDTO>> pkgItemMap = orderItemRespList.stream().filter(e -> Objects.nonNull(e.getParentItemGuid()))
                        .collect(Collectors.groupingBy(AdjustByOrderItemRespDTO::getParentItemGuid));
                for (AdjustByOrderItemRespDTO itemRespDTO : orderItemRespList) {
                    buildAdjustMap(adjustMap, adjustItemMap, pkgItemMap, itemRespDTO, erpItemList);
                }
            }
            log.info("查询经过调整之后的外卖单，回退商品库存:{}", JacksonUtils.writeValueAsString(adjustMap));
        }
        Map<String, List<ItemDO>> itemGroupByErpItemSkuGuidMap = erpItemList.stream()
                .collect(Collectors.groupingBy(ItemDO::getErpItemSkuGuid));
        // 查询sku
        ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(new ArrayList<>(itemGroupByErpItemSkuGuidMap.keySet()));
        itemStringListDTO.setStoreGuid(orderReadDO.getStoreGuid());
        List<SkuTakeawayInfoRespDTO> arrayOfSkuTakeawayInfoRespList = itemClientService.listSkuInfoAndSub(itemStringListDTO);
        Map<String, SkuTakeawayInfoRespDTO> skuTakeawayInfoMap = arrayOfSkuTakeawayInfoRespList.stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key2));
        for (Map.Entry<String, List<ItemDO>> entry : itemGroupByErpItemSkuGuidMap.entrySet()) {
            appendAdjustMap(adjustMap, skuTakeawayInfoMap, entry.getKey(), entry.getValue());
        }
        log.info("回退商品库存:{}", JacksonUtils.writeValueAsString(adjustMap));
        // 回退库存
        modifyErpRepertory(orderReadDO, adjustMap);
    }


    /**
     * 构建回退库存
     */
    private void buildAdjustMap(Map<String, BigDecimal> adjustMap, Map<String, ItemDO> adjustItemMap,
                                Map<Long, List<AdjustByOrderItemRespDTO>> pkgItemMap,
                                AdjustByOrderItemRespDTO adjustItemRespDTO, List<ItemDO> erpItemList) {
        if (adjustItemRespDTO.getAdjustType() == 0) {
            // 如果是数量调整，将对象转换为ITEMDO，加入List
            ItemDO oldItemDO = adjustItemMap.get(adjustItemRespDTO.getOrderItemGuid());
            if (Objects.isNull(oldItemDO)) {
                return;
            }
            ItemDO itemDO = new ItemDO();
            itemDO.setErpItemSkuGuid(adjustItemRespDTO.getSkuGuid());
            itemDO.setActualItemCount(oldItemDO.getActualItemCount().divide(oldItemDO.getItemCount(), 2, RoundingMode.DOWN)
                    .multiply(adjustItemRespDTO.getCurrentCount()));
            erpItemList.add(itemDO);
            return;
        }
        // 如果是菜品更换，直接加入到对应回退库存的map中
        if (ItemTypeEnum.SINGLE_UNWEIGH.getCode() == adjustItemRespDTO.getItemType()
                && adjustItemRespDTO.getParentItemGuid() == 0) {
            // 如果是单品
            BigDecimal count = adjustMap.get(adjustItemRespDTO.getSkuGuid());
            if (Objects.isNull(count)) {
                count = BigDecimal.ZERO;
            }
            adjustMap.put(adjustItemRespDTO.getSkuGuid(), adjustItemRespDTO.getCurrentCount().add(count));
        } else if (ItemTypeEnum.PKG.getCode() == adjustItemRespDTO.getItemType()) {
            // 如果是套餐，需要将子项加入
            List<AdjustByOrderItemRespDTO> pkgItemList = pkgItemMap.get(adjustItemRespDTO.getGuid());
            if (CollectionUtils.isEmpty(pkgItemList)) {
                return;
            }
            for (AdjustByOrderItemRespDTO pkgItem : pkgItemList) {
                BigDecimal count = adjustMap.get(pkgItem.getSkuGuid());
                if (Objects.isNull(count)) {
                    count = BigDecimal.ZERO;
                }
                BigDecimal pkgItemCount = pkgItem.getPackageDefaultCount().multiply(adjustItemRespDTO.getCurrentCount());
                adjustMap.put(pkgItem.getSkuGuid(), pkgItemCount.add(count));
            }
        }
    }


    /**
     * 构建回退库存
     */
    private void appendAdjustMap(Map<String, BigDecimal> adjustMap, Map<String, SkuTakeawayInfoRespDTO> skuTakeawayInfoMap,
                                 String erpItemSkuGuid, List<ItemDO> itemList) {
        SkuTakeawayInfoRespDTO skuInfo = skuTakeawayInfoMap.get(erpItemSkuGuid);
        if (Objects.isNull(skuInfo)) {
            return;
        }
        BigDecimal totalCount = BigDecimal.ZERO;
        for (ItemDO item : itemList) {
            totalCount = totalCount.add(item.getActualItemCount());
        }
        if (ItemTypeEnum.SINGLE_UNWEIGH.getCode() == skuInfo.getItemType()) {
            // 如果当前绑定门店商品是单品
            BigDecimal count = adjustMap.getOrDefault(erpItemSkuGuid, BigDecimal.ZERO);
            adjustMap.put(erpItemSkuGuid, totalCount.add(count));
        } else if (ItemTypeEnum.PKG.getCode() == skuInfo.getItemType()) {
            // 如果当前绑定门店商品是固定套餐
            List<SubgroupWebRespDTO> subgroupList = Optional.ofNullable(skuInfo.getSubgroupList())
                    .orElse(Lists.newArrayList());
            for (SubgroupWebRespDTO subGroup : subgroupList) {
                List<SubItemSkuWebRespDTO> subItemSkuList = Optional.ofNullable(subGroup.getSubItemSkuList())
                        .orElse(Lists.newArrayList());
                for (SubItemSkuWebRespDTO subItem : subItemSkuList) {
                    BigDecimal subItemCount = subItem.getItemNum().multiply(totalCount);
                    BigDecimal count = adjustMap.getOrDefault(subItem.getSkuGuid(), BigDecimal.ZERO);
                    adjustMap.put(subItem.getSkuGuid(), subItemCount.add(count));
                }
            }
        }
    }

    private void modifyErpRepertory(OrderReadDO orderReadDO, Map<String, BigDecimal> adjustMap) {
        String orderNo = orderReadDO.getOrderViewId();
        LocalDate businessDay = orderReadDO.getBusinessDay();

        if (MapUtils.isEmpty(adjustMap)) {
            return;
        }
        Set<String> skuGuids = adjustMap.keySet();
        // 通过商品skuGuid查询sku基本信息
        List<SkuInfoRespDTO> skus = itemClientService.listSkuInfo(new ArrayList<>(skuGuids));
        log.info("通过商品skuGuid查询sku基本信息：{}", JacksonUtils.writeValueAsString(skus));
        Map<String, SkuInfoRespDTO> skuMap = skus.stream().collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key2));

        List<DepositDish> dishes = new ArrayList<>();
        adjustMap.forEach((key, value) -> {
            SkuInfoRespDTO skuInfo = skuMap.get(key);
            if (Objects.nonNull(skuInfo)) {
                DepositDish dish = new DepositDish();
                // erp库存需要根据品牌库sku进行操作
                dish.setSkuId(Optional.ofNullable(skuInfo.getParentGuid()).orElse(skuInfo.getSkuGuid()));
                dish.setSkuCount(value.abs());

                dish.setItemName(skuInfo.getItemName());
                dish.setSkuName(skuInfo.getName());
                dish.setTypeName(skuInfo.getTypeName());
                dish.setPrice(skuInfo.getSalePrice());
                dish.setItemType(skuInfo.getItemType());
                dish.setUnit(skuInfo.getUnit());

                // 0 入库 1 出库
                dish.setType(value.compareTo(BigDecimal.ZERO) > 0 ? 0 : 1);
                dishes.add(dish);
            }
        });
        if (CollectionUtils.isEmpty(dishes)) {
            return;
        }
        // 查询库存绑定的门店
        String storeGuid = getStockBindStore(orderReadDO.getStoreGuid());

        DepositErpSyncDTO erpSyncDTO = new DepositErpSyncDTO();

        erpSyncDTO.setOrderFee(orderReadDO.getTotal()
                .subtract(Optional.ofNullable(orderReadDO.getCustomerRefund()).orElse(BigDecimal.ZERO)));
        erpSyncDTO.setActuallyPayFee(orderReadDO.getCustomerActualPay()
                .subtract(Optional.ofNullable(orderReadDO.getCustomerRefund()).orElse(BigDecimal.ZERO)));
        erpSyncDTO.setTakeawayFlag(true);
        erpSyncDTO.setOrderCreateTime(orderReadDO.getCreateTime());

        erpSyncDTO.setBusinessType(1);
        erpSyncDTO.setBusinessDate(businessDay);
        erpSyncDTO.setThirdNo(orderNo);
        erpSyncDTO.setOrderGuid(orderReadDO.getOrderId());
        erpSyncDTO.setStoreId(storeGuid);
        erpSyncDTO.setUserGuid(orderReadDO.getAcceptStaffGuid());
        erpSyncDTO.setUsername(orderReadDO.getAcceptStaffName());
        erpSyncDTO.setDepositDishes(dishes);
        try {
            String url = erpHost + "/api/inventory/deposit/adjust";
            log.info("外卖退单调整商品调用erp,url:{}，请求参数：{}", url, JSONUtil.parse(erpSyncDTO));
            String result = HttpRequest.post(url)
                    .header("enterpriseGuid", orderReadDO.getEnterpriseGuid())
                    .body(JacksonUtils.writeValueAsString(erpSyncDTO))
                    .execute().body();
            log.info("外卖退单调整商品调用erp,返回结果：{}", result);
        } catch (HttpException e) {
            log.error("外卖退单调整商品调用erp失败,错误信息：{}", e.getMessage());
        }

        addStockForOrderWeihaiErp(erpSyncDTO);
    }


    private String transferLcDepartId(String storeGuid) {
        MDMResult<OrganizationDTO> organizationResp = mdmServiceClient.queryOrganization(storeGuid);
        log.warn("查询mdm组织信息返回:{}", organizationResp);
        if (Objects.isNull(organizationResp.getData())) {
            log.warn("当前组织未同步到mdm, storeGuid:{}", storeGuid);
            return storeGuid;
        }
        String weihaiId = organizationResp.getData().getWeihaiId();
        if (com.holderzone.framework.util.StringUtils.isEmpty(weihaiId)) {
            log.warn("当前组织未同步到mdm, storeGuid:{}", storeGuid);
            return storeGuid;
        }
        return weihaiId;
    }
}
