package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.AutoRecoveryDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;

import java.util.List;


/**
 * 外卖自动修复异常数据
 */
public interface AutoRecoveryService extends IService<AutoRecoveryDO> {

    List<AutoRecoveryDO> queryLimit();

    /**
     * 创建订单异常
     */
    void createOrderEx(List<ItemDO> itemList, String exMsg);

}
