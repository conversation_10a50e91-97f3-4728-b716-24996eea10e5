package com.holder.saas.store.takeaway.consumers.utils;

import org.slf4j.MDC;

public class TraceidUtils {

    private TraceidUtils() {
    }

    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();

    public static final String TRACEID_KEY = "traceId";

    public static void setTraceid(String traceid) {
        THREAD_LOCAL.set(traceid);
        MDC.put(TRACEID_KEY, traceid);
    }

    public static String getTraceid() {
        return THREAD_LOCAL.get();
    }

    public static void clear() {
        THREAD_LOCAL.remove();
        MDC.clear();
    }

}
