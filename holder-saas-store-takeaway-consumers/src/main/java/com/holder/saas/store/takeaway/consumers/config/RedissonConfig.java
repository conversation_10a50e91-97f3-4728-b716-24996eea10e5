package com.holder.saas.store.takeaway.consumers.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.nio.charset.StandardCharsets;

@Configuration
public class RedissonConfig {

    @Value("${redisson.address}")
    private String host;

    @Value("${redisson.database}")
    private Integer database;

    @Value("${redisson.password}")
    private String password;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
                .setAddress(host)
                .setDatabase(database)
                .setPassword(password)
                .setConnectionMinimumIdleSize(1);
        config.setCodec(new StringCodec(StandardCharsets.UTF_8));
        return Redisson.create(config);
    }
}