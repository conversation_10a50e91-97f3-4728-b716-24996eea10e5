package com.holder.saas.store.takeaway.consumers.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

    @Bean
    public ExecutorService executorService() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("takeout-mapping-pool-%d").build());
    }


    @Bean(value = "storeProductQueryThreadPool")
    public ExecutorService storeProductQueryThreadPool() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("takeout-store-query-product-pool-%d").build());
    }

    @Bean(value = "batchBindProductThreadPool")
    public ExecutorService batchBindProductThreadPool() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("takeout-batch-bind-product-pool-%d").build());
    }

    @Bean(value = "autoAcceptOrderThreadPool")
    public ExecutorService autoAcceptOrderThreadPool() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("auto-accept-bind-order-pool-%d").build());
    }

    @Bean(value = "stockErpThreadPool")
    public ExecutorService stockErpThreadPool() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("stock-erp-pool-%d").build());
    }
}
