package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holder.saas.store.takeaway.consumers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrganizationService
 * @date 2018/09/18 11:27
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-store-organization", fallbackFactory = OrgFeignClient.ServiceFallBack.class)
public interface OrgFeignClient {

    /**
     * 根据门店guid查询主机deviceId
     *
     * @param storeGuid 门店guid
     * @return 门店下的主机信息（一体机）
     */
    @GetMapping("/device/get_master_device_by_storeguid/{storeGuid}")
    StoreDeviceDTO findMasterDevice(@PathVariable("storeGuid") String storeGuid);

    /**
     * 根据门店guid查询门店Tel
     *
     * @param storeGuid
     * @return
     */
    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据门店guid集合及日期时间获取所属营业日期
     *
     * @param businessDateReqDTO 门店列表，查询的时间
     * @return LocalDate
     */
    @PostMapping("store/query_business_day")
    LocalDate queryBusinessDay(@RequestBody BusinessDateReqDTO businessDateReqDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<OrgFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrgFeignClient create(Throwable cause) {
            return new OrgFeignClient() {

                @Override
                public StoreDeviceDTO findMasterDevice(String storeGuid) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "findMasterDevice", storeGuid,
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException("查询门店一体机主机接口熔断");
                }

                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "queryStoreByGuid", storeGuid,
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return null;
                }

                @Override
                public LocalDate queryBusinessDay(BusinessDateReqDTO businessDateReqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "queryBusinessDay",
                                JacksonUtils.writeValueAsString(businessDateReqDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ParameterException("获取营业日期失败!");
                }
            };
        }
    }
}
