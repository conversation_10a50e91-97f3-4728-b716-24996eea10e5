package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemBindExtendInfoDo;
import com.holder.saas.store.takeaway.consumers.mapper.ItemBindExtendInfoMapper;
import com.holder.saas.store.takeaway.consumers.service.ItemBindExtendInfoService;
import com.holderzone.saas.store.dto.takeaway.TcdSyncItemMappingDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @ClassName: ItemBindExtendInfoServiceImpl
 * @Description: TODO
 * @Author: CC
 * @Date 2021/4/27 14:31
 * @ModifyRecords: v1.0 new
 */
@Slf4j
@Service
public class ItemBindExtendInfoServiceImpl extends ServiceImpl<ItemBindExtendInfoMapper, ItemBindExtendInfoDo> implements ItemBindExtendInfoService {

    @Override
    public List<ItemBindExtendInfoDo> selectByStoreGuidsAndTakeoutType(List<String> storeGuids, Integer takeoutType) {
        QueryWrapper<ItemBindExtendInfoDo> qw = new QueryWrapper<>();
        qw.lambda().in(ItemBindExtendInfoDo::getStoreGuid, storeGuids);
        qw.lambda().eq(ItemBindExtendInfoDo::getTakeoutType, takeoutType);
        return list(qw);
    }

    @Override
    public void syncTcdItemMappingCount(List<TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping> tcdItemMappingList) {
        // 同步映射数量
        baseMapper.syncTcdItemMappingCount(tcdItemMappingList);
    }

}
