package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixRecordDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixRecordDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutRecordQueryDTO;


/**
 * <p>
 * 外卖修复审核记录服务类
 * </p>
 */
public interface FixRecordService extends IService<FixRecordDO> {

    Page<TakeoutFixRecordDTO> pageInfo(TakeoutRecordQueryDTO queryDTO);


}
