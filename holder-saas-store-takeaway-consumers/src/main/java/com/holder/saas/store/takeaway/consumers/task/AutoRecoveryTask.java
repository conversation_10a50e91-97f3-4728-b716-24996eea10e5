package com.holder.saas.store.takeaway.consumers.task;

import com.holder.saas.store.takeaway.consumers.entity.domain.AutoRecoveryDO;
import com.holder.saas.store.takeaway.consumers.manage.AutoRecoveryManager;
import com.holder.saas.store.takeaway.consumers.service.AutoRecoveryService;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 数据修复
 * 只修复指定数据库：何师: hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db
 * <p>
 * 1.hst_takeout_item表中erp_item_name,erp_item_price,takeaway_accounting_price数据丢失，排查发现item服务访问不稳定导致数据丢失
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AutoRecoveryTask {

    private final AutoRecoveryService autoRecoveryService;

    private final AutoRecoveryManager autoRecoveryManager;

    private final DynamicHelper dynamicHelper;

    private final RedisTemplate<String, String> redisTemplate;

    @Value("${fix.enterpriseGuid}")
    private String fixEnterpriseGuid;

    public static final String AUTO_RECOVERY_KEY = "AUTO_RECOVERY_KEY";

    /**
     * 每小时执行一次
     */
     @Scheduled(cron = "0 0 0/1 * * ?")
    public void fixExItem() {
        log.info("扫描异常数据开始-----fixEnterpriseGuid:{}", fixEnterpriseGuid);
        dynamicHelper.changeDatasource(fixEnterpriseGuid);
        UserContextUtils.putErp(fixEnterpriseGuid);
        EnterpriseIdentifier.setEnterpriseGuid(fixEnterpriseGuid);
        boolean hasKey = redisTemplate.hasKey(AUTO_RECOVERY_KEY);
        if (hasKey) {
            return;
        }
        redisTemplate.opsForValue().set(AUTO_RECOVERY_KEY, "1", 3, TimeUnit.MINUTES);
        // 查询外卖订单明细表异常数据
        List<AutoRecoveryDO> all = autoRecoveryService.queryLimit();
        if (CollectionUtils.isNotEmpty(all)) {
            log.info("异常数据修复任务执行开始，enterpriseGuid:{}-----------------------------", fixEnterpriseGuid);
            // 避免数据量大，分段处理
            List<AutoRecoveryDO> fixItemBuffer = null;
            for (AutoRecoveryDO item : all) {
                if (fixItemBuffer == null) {
                    fixItemBuffer = new ArrayList<>();
                }
                fixItemBuffer.add(item);
                if (fixItemBuffer.size() >= 500) {
                    autoRecoveryManager.fixItem(fixItemBuffer);
                    fixItemBuffer = null;
                }
            }
            if (CollectionUtils.isNotEmpty(fixItemBuffer)) {
                autoRecoveryManager.fixItem(fixItemBuffer);
            }
            log.info("异常数据修复任务执行结束，enterpriseGuid:{}-----------------------------", fixEnterpriseGuid);
        }
        redisTemplate.delete(AUTO_RECOVERY_KEY);
        log.info("扫描异常数据结束-----fixEnterpriseGuid:{}", fixEnterpriseGuid);
    }
}
