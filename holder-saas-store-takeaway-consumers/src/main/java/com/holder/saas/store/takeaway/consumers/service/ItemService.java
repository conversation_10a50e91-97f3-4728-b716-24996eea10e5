package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderItemRespDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderItemProblemDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO;

import java.util.List;

/**
 * <p>
 * 外卖菜品服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
public interface ItemService extends IService<ItemDO> {


    /**
     * 根据订单号查询订单商品信息
     *
     * @param orderGuid 订单号
     * @return 商品信息
     */
    List<ItemDO> listByOrderGuid(String orderGuid);

    List<ItemDO> listByItemGuids(List<String> itemGuids);

    /**
     * 根据订单号查询B端商品明细信息
     *
     * @param orderGuid 订单号
     * @return 商品信息
     */
    List<BusinessTakeoutOrderItemRespDTO> getBusinessTakeoutOrderItemList(String orderGuid);

    List<BusinessTakeoutOrderItemRespDTO> getBusinessTakeoutOrderItemList(List<ItemDO> itemDoList);

    List<ItemDO> getItemList(String orderGuid);

    /**
     * 批量编辑订单明细 ‘是否调整’字段为已调整
     *
     * @param orderItemGuidList 订单明细guids
     */
    void updateBatchIsAdjustItem(List<String> orderItemGuidList);

    void updateBatchItem(List<TakeoutOrderItemProblemDTO> problemDTOList);

    /**
     * 批量修复外卖商品数据
     */
    void fixBatchItem(List<ItemDO> fixItemList);

    /**
     * 外卖异常数据列表
     *
     * @param reqDTO 外卖异常数据列表请求
     * @return 外卖异常数据列表
     */
    Page<TakeoutItemAbnormalDataRespDTO> pageAbnormalData(TakeoutItemAbnormalDataReqDTO reqDTO);

    /**
     * 数据修复列表
     *
     * @param reqDTO 数据修复列表请求
     * @return 数据修复列表
     */
    List<TakeoutItemDataFixRespDTO> listDataFix(TakeoutItemDataFixReqDTO reqDTO);

    /**
     * 查询未绑定关联数据
     */
    List<ItemDO> selectUnBindList(String storeGuid, String thirdSkuId);

    /**
     * 清除绑定关系
     */
    void clearErpItemSkuGuid(List<Long> ids);

    /**
     * 过滤待接单，已取消的订单明细，这些明细无需修复
     */
    List<ItemDO> filterNoRequiredFixList(List<Long> ids);

    /**
     * 批量更新订单明细退款数量
     */
    void updateBatchRefundCount(List<ItemDO> itemList);

    /**
     * 取消订单 订单退单数量更新
     */
    void updateRefundCountByOrderGuid(String orderGuid);
}
