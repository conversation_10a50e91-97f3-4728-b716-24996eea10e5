package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemMappingDO;
import com.holder.saas.store.takeaway.consumers.mapper.ItemMappingMapper;
import com.holder.saas.store.takeaway.consumers.service.ItemMappingService;
import com.holderzone.saas.store.dto.takeaway.TcdSyncItemMappingDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ItemMappingServiceImpl extends ServiceImpl<ItemMappingMapper, ItemMappingDO> implements ItemMappingService {

    @Override
    public List<ItemMappingDO> queryItemMappingInfo(List<String> mapperGuids) {
        if (CollectionUtils.isEmpty(mapperGuids)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<ItemMappingDO>()
                .in(ItemMappingDO::getMapperGuid, mapperGuids));
    }

    @Override
    public List<ItemMappingDO> queryByStoreGuidAndTakeoutTypeAndMapperGuids(String storeGuid, Integer takeoutType, List<String> mapperGuids) {
        if (CollectionUtils.isEmpty(mapperGuids)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<ItemMappingDO>()
                .eq(ItemMappingDO::getStoreGuid, storeGuid)
                .eq(ItemMappingDO::getTakeoutType, takeoutType)
                .in(ItemMappingDO::getMapperGuid, mapperGuids));
    }

    @Override
    public void syncTcdItemMapping(List<TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping> tcdItemMappingList) {
        baseMapper.syncTcdItemMapping(tcdItemMappingList);
    }
}
