package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.ConfigService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.TakeoutAutoRcvDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutConfigDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/takeout")
@Api(description = "美团相关接口")
public class TakeoutConfigController {

    private final ConfigService configService;

    @Autowired
    public TakeoutConfigController(ConfigService configService) {
        this.configService = configService;
    }

    @PostMapping(value = "/set_config")
    @ApiOperation(value = "商家设置配置", notes = "商家设置配置")
    public Boolean setConfig(@RequestBody TakeoutConfigDTO takeoutConfigDTO) {
        if (log.isInfoEnabled()) {
            log.info("商家设置配置：入参{}", JacksonUtils.writeValueAsString(takeoutConfigDTO));
        }
        return configService.saveStoreConfig(takeoutConfigDTO);
    }

    @PostMapping(value = "/query_config")
    @ApiOperation(value = "商家查询配置", notes = "商家查询配置")
    public TakeoutConfigDTO queryConfig(@RequestBody TakeoutConfigDTO takeoutConfigDTO) {
        if (log.isInfoEnabled()) {
            log.info("商家查询配置：入参{}", JacksonUtils.writeValueAsString(takeoutConfigDTO));
        }
        return configService.selectStoreConfig(takeoutConfigDTO);
    }

    @PostMapping(value = "/set_auto_receive")
    @ApiOperation(value = "商家设置是否自动接单", notes = "商家设置是否自动接单")
    public Boolean setAutoReceive(@RequestBody @Validated(TakeoutAutoRcvDTO.SetAuto.class) TakeoutAutoRcvDTO takeoutAutoRcvDTO) {
        if (log.isInfoEnabled()) {
            log.info("商家设置是否自动接单：入参{}", JacksonUtils.writeValueAsString(takeoutAutoRcvDTO));
        }
        return configService.saveStoreAutoRcvConfig(takeoutAutoRcvDTO);
    }

    @PostMapping(value = "/query_auto_receive")
    @ApiOperation(value = "商家查询是否自动接单", notes = "商家查询是否自动接单")
    public TakeoutAutoRcvDTO queryAutoReceive(@RequestBody @Validated(TakeoutAutoRcvDTO.GetAuto.class) TakeoutAutoRcvDTO takeoutAutoRcvDTO) {
        if (log.isInfoEnabled()) {
            log.info("商家查询是否自动接单：入参{}", JacksonUtils.writeValueAsString(takeoutAutoRcvDTO));
        }
        return configService.selectStoreAutoRcvConfig(takeoutAutoRcvDTO);
    }
}
