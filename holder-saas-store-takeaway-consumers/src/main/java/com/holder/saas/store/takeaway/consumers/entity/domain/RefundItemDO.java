package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * hst_takeout_refund_item
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hst_takeout_refund_item")
public class RefundItemDO implements Serializable {

    private static final long serialVersionUID = 9126807575367828724L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 退单guid 一次退款退单guid相同
     */
    private String refundGuid;

    /**
     * 订单GUID
     */
    private String orderGuid;

    /**
     * 商品明细GUID
     */
    private String orderItemGuid;

    /**
     * 三方平台商品skuId
     */
    // private String thirdSkuId;

    /**
     * 菜品来源：0=美团，1=饿了么
     *
     * @see com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType
     */
    private Integer orderSubType;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 门店名
     */
    private String storeName;

    /**
     * 订单营业日
     */
    private LocalDate businessDay;

    /**
     * 菜品名称
     */
    private String itemName;

    /**
     * 单位
     */
    private String itemUnit;

    /**
     * 菜品规格 多元素使用"",""分割开
     */
    private String itemSpec;

    /**
     * 特殊属性 多元素使用"",""分割开
     */
    private String itemProperty;

    /**
     * 单价
     */
    private BigDecimal itemPrice;

    /**
     * 菜品数量
     */
    private BigDecimal itemCount;

    /**
     * 菜品小计
     */
    private BigDecimal itemTotal;

    /**
     * 餐盒单价
     */
    private BigDecimal boxPrice;

    /**
     * 餐盒数量
     */
    private BigDecimal boxCount;

    /**
     * 餐盒小计
     */
    private BigDecimal boxTotal;

    /**
     * 结算类型 0=普通消费菜品  1=赠送菜品
     */
    private Integer settleType;

    /**
     * 平台实际商品数量（外卖商品有商品映射时改字段值与上面那个字段值不一致）
     */
    private BigDecimal actualItemCount;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 映射商品skuId
     */
    private String erpSkuGuid;

    /**
     * 映射菜品名称
     */
    private String erpItemName;

    /**
     * 映射单位
     */
    private String erpItemUnit;

    /**
     * 映射菜品规格 多元素使用"",""分割开
     */
    private String erpItemSpec;

    /**
     * 映射单价
     */
    private BigDecimal erpItemPrice;

    /**
     * 映射菜品数量
     */
    private BigDecimal erpItemCount;

    /**
     * 映射菜品小计
     */
    private BigDecimal erpItemTotal;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否退款成功
     */
    @TableField("is_refund_success")
    private Boolean refundSuccess;

    @TableField(exist = false)
    private String itemSku;

    @TableField(exist = false)
    private String thirdSkuId;

}