package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holder.saas.store.takeaway.consumers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.print.content.PrintLabelDTO;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintTakeoutDTO;
import com.holderzone.saas.store.dto.print.format.TakeoutFormatDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintService
 * @date 2018/10/16 11:58
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-store-print", fallbackFactory = PrintFeignClient.ServiceFallBack.class)
public interface PrintFeignClient {

    @PostMapping("/print_record/send")
    String printBill(@RequestBody PrintTakeoutDTO takeoutDto);

    @PostMapping("/print_record/send")
    String printKitchen(@RequestBody PrintOrderItemDTO printOrderItemDTODto);

    @PostMapping("/print_record/send")
    String printLabel(@RequestBody PrintLabelDTO printLabelDTODto);

    @GetMapping("/format/query_takeout")
    @ApiOperation(value = "查询外卖单格式")
    TakeoutFormatDTO queryTakeout(@RequestParam("storeGuid") String storeGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<PrintFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public PrintFeignClient create(Throwable cause) {
            return new PrintFeignClient() {

                @Override
                public String printBill(PrintTakeoutDTO takeoutDto) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printBill", JacksonUtils.writeValueAsString(takeoutDto),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String printKitchen(PrintOrderItemDTO printOrderItemDTODto) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printKitchen", JacksonUtils.writeValueAsString(printOrderItemDTODto),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String printLabel(PrintLabelDTO printLabelDTODto) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printLabel", JacksonUtils.writeValueAsString(printLabelDTODto),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public TakeoutFormatDTO queryTakeout(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryTakeout", storeGuid,
                            ThrowableExtUtils.asStringIfAbsent(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
