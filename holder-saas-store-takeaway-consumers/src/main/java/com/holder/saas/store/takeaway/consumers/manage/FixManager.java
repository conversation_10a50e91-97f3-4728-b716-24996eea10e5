package com.holder.saas.store.takeaway.consumers.manage;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.consumers.builder.FixItemBizBuilder;
import com.holder.saas.store.takeaway.consumers.entity.bo.FixItemBiz;
import com.holder.saas.store.takeaway.consumers.entity.domain.*;
import com.holder.saas.store.takeaway.consumers.service.*;
import com.holder.saas.store.takeaway.consumers.service.rpc.ItemFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.TradeClientService;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.deposit.req.DepositDish;
import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutAndStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StockStoreBindResqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.FutureTask;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 外卖异常数据修复 业务层
 */
@Slf4j
@RequiredArgsConstructor
@Component("fixManage")
public class FixManager {

    private final FixItemService fixItemService;

    private final FixRecordService fixRecordService;

    private final ItemService itemService;

    private final MappingService mappingService;

    private final ItemMappingService itemMappingService;

    private final AbnormalDataService abnormalDataService;

    private final ItemFeignClient itemFeignClient;

    private final ProducerFeignClient producerFeignClient;

    private final TradeClientService tradeClientService;

    private final ExecutorService executorService;

    private final PackageService packageService;

    @Value("${erp.host}")
    private String erpHost;


    @Transactional(rollbackFor = Exception.class)
    public void fix(FixItemBiz biz) {
        boolean commitFlag = biz.getCommitFlag();
        boolean fixBindFlag = biz.getFixBindFlag();
        FixRecordDO recordDO = biz.getRecord();
        List<FixItemDO> itemList = biz.getItemList();
        if (!commitFlag) {
            // 立即修复记录状态为1，已修复
            recordDO.setStatus(1);
        }

        // 前端不会处理，这里特殊处理一下截止时间，截止时间不能超过当前时间
        LocalDateTime now = LocalDateTime.now();
        if (recordDO.getEndTime().isAfter(now)) {
            recordDO.setEndTime(now);
        }

        recordDO.setModifiedUserName(UserContextUtils.getUserName());
        recordDO.setModifiedUserGuid(UserContextUtils.getUserGuid());
        if (Objects.isNull(recordDO.getId())) {
            recordDO.setCreateUserGuid(UserContextUtils.getUserGuid());
            recordDO.setCreateUserName(UserContextUtils.getUserName());
        }
        // 保存记录
        fixRecordService.saveOrUpdate(recordDO);
        // 保存商品信息
        itemList.forEach(e -> e.setRecordId(recordDO.getId()));
        if (CollectionUtils.isNotEmpty(itemList)) {
            fixItemService.saveOrUpdateBatch(itemList);
        }
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        if (!commitFlag) {
            quickFixItem(recordDO, itemList);
        }
        if (fixBindFlag) {
            quickFixBind(itemList);
        }
    }

    /**
     * 立即修复
     */
    private void quickFixItem(FixRecordDO fixRecord, List<FixItemDO> itemList) {
        String jsonStr = UserContextUtils.getJsonStr();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        executorService.submit(() -> {
            try {
                // 切换数据源
                UserContextUtils.put(jsonStr);
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);

                // 查询需要修改的数据
                TakeoutItemDataFixReqDTO fixReqDTO = buildFixReqDTO(fixRecord.getStartTime(), fixRecord.getEndTime(), itemList);
                // 避免oom，还是最多查询500条吧，很久之前的手动去修复
                List<ItemDO> unFixItemList = abnormalDataService.listFixItem(fixReqDTO);
                log.info("未修复的外卖数据查询结果:{}", JacksonUtils.writeValueAsString(unFixItemList));
                if (CollectionUtils.isEmpty(unFixItemList)) {
                    return;
                }

                // 立即修复外卖数据
                fixBatchItemByArea(unFixItemList, itemList);

                // 清除异常表数据
                List<Long> idList = unFixItemList.stream().map(ItemDO::getId).collect(toList());
                abnormalDataService.removeByTakeoutItemIds(idList);

                // 对应扣减erp库存
                modifyErpRepertory(unFixItemList, fixRecord.getId());
            } catch (Exception e) {
                log.error("立即修复外卖数据异常 e:", e);
            }
        });
    }

    /**
     * 修复绑定映射关系
     */
    private void quickFixBind(List<FixItemDO> itemList) {
        String jsonStr = UserContextUtils.getJsonStr();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        executorService.submit(() -> {
            try {
                // 切换数据源
                UserContextUtils.put(jsonStr);
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
                // 修复绑定关系
                fixBatchBindInfo(itemList);
            } catch (Exception e) {
                log.error("修复绑定关系异常 e", e);
            }
        });
    }


    private void fixBatchItemByArea(List<ItemDO> unFixItemList, List<FixItemDO> fixItemList) {
        Map<String, FixItemDO> fixItemMap = fixItemList.stream()
                .collect(Collectors.toMap(FixItemDO::getUniqueStoreAndThirdSkuId, Function.identity(), (key1, key2) -> key2));
        List<SkuInfoPkgDTO> skuInfoPkgList = buildErpItemInfo(fixItemMap, unFixItemList);

        log.info("unFixItemList查询结果-处理之后:{}", JacksonUtils.writeValueAsString(unFixItemList));
        // 分批修改
        List<ItemDO> fixItemBuffer = null;
        for (ItemDO itemDO : unFixItemList) {
            if (fixItemBuffer == null) {
                fixItemBuffer = new ArrayList<>();
            }
            fixItemBuffer.add(itemDO);
            if (fixItemBuffer.size() >= 500) {
                itemService.fixBatchItem(fixItemBuffer);
                //修改套餐信息
                packageService.updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
                fixItemBuffer = null;
            }
        }
        if (CollectionUtils.isNotEmpty(fixItemBuffer)) {
            itemService.fixBatchItem(fixItemBuffer);
            packageService.updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        }
    }

    private List<SkuInfoPkgDTO> buildErpItemInfo(Map<String, FixItemDO> fixItemMap, List<ItemDO> unFixItemList) {
        List<SkuInfoPkgDTO> skuInfoPkgList = Lists.newArrayList();
        unFixItemList.forEach(e -> {
            FixItemDO fixItemDO = fixItemMap.get(e.getStoreGuid() + ":" + e.getThirdSkuId());
            if (Objects.isNull(fixItemDO)) {
                return;
            }
            // 此处注意：实际购买数量 = 外卖修复时的商品映射数量 * 外卖平台方购买的商品数量
            e.setActualItemCount(e.getItemCount().multiply(fixItemDO.getErpItemCount()));
            e.setErpItemName(fixItemDO.getErpItemName());
            e.setErpItemSkuGuid(fixItemDO.getErpItemSkuGuid());
            e.setErpItemPrice(fixItemDO.getErpItemPrice());
            e.setTakeawayAccountingPrice(fixItemDO.getTakeawayAccountingPrice());
            e.setErpItemGuid(fixItemDO.getErpItemGuid());
            if (CollectionUtils.isNotEmpty(fixItemDO.getListPkg())) {
                for (SkuInfoPkgDTO pkgDTO : fixItemDO.getListPkg()) {
                    List<String> pkSkuIdList = skuInfoPkgList.stream().map(SkuInfoPkgDTO::getSkuGuid).collect(toList());
                    if (!pkSkuIdList.contains(pkgDTO.getSkuGuid())) {
                        skuInfoPkgList.add(pkgDTO);
                    }
                }
            }
        });
        return skuInfoPkgList;
    }


    /**
     * 修复绑定关系
     */
    private void fixBatchBindInfo(List<FixItemDO> itemList) throws ExecutionException, InterruptedException {
        // 修复数据分组：以门店guid和平台来源作为key，查询对应平台下该平台的外卖方商品
        Map<String, List<FixItemDO>> groupByUniqueKeyMap = itemList.stream()
                .collect(Collectors.groupingBy(FixItemDO::getUniqueStoreAndSubType));

        // 需要修复绑定的商品
        List<UnItemBatchUnbindReq> batchBindList = Lists.newArrayList();

        for (Map.Entry<String, List<FixItemDO>> entry : groupByUniqueKeyMap.entrySet()) {
            UnItemBatchUnbindReq unItemBatchUnbindReq = buildBatchBindList(entry.getKey(), entry.getValue());
            if (Objects.nonNull(unItemBatchUnbindReq)) {
                batchBindList.add(unItemBatchUnbindReq);
            }
        }

        log.info("批量绑定商品映射:{}", JacksonUtils.writeValueAsString(batchBindList));
        if (CollectionUtils.isNotEmpty(batchBindList)) {
            // 批量绑定商品映射
            batchBindList.forEach(mappingService::batchBindItem);
        }
    }

    private UnItemBatchUnbindReq buildBatchBindList(String uniqueKey, List<FixItemDO> fixItemList) throws ExecutionException, InterruptedException {
        String[] uniqueKeySplit = uniqueKey.split(":");
        String storeGuid = uniqueKeySplit[0];
        String type = uniqueKeySplit[1];
        // 查询平台方商品
        // 赚餐在item表里存的是6，查询需要传3
        type = type.equals(String.valueOf(OrderType.TakeoutSubType.TCD_TAKEOUT.getType())) ? "3" : type;
        List<UnMappedItem> unItemList = getUnItemList(storeGuid, Integer.parseInt(type));
        if (CollectionUtils.isEmpty(unItemList)) {
            log.error("当前门店无商品请检查,storeGuid:{},type:{}", storeGuid, type);
            return null;
        }
        // 特殊处理美团方skuId
        if (OrderType.TakeoutSubType.MT_TAKEOUT.getType() == Integer.parseInt(type)) {
            unItemList.forEach(e -> {
                String unItemSkuId = e.getUnItemSkuId();
                e.setUnItemSkuId(e.getMtSkuId());
                e.setMtSkuId(unItemSkuId);
            });
        }
        Map<String, UnMappedItem> unMappedItemMap = unItemList.stream()
                .collect(Collectors.toMap(UnMappedItem::getUnItemSkuId, Function.identity(), (key1, key2) -> key1));
        List<UnItemBaseMapReq> unItemBindList = Lists.newArrayList();
        fixItemList.forEach(e -> {
            UnMappedItem unMappedItem = unMappedItemMap.get(e.getThirdSkuId());
            if (Objects.isNull(unMappedItem)) {
                return;
            }
            // 如果当前三方平台商品有绑定关系，则不覆盖原有绑定关系
            if (!StringUtils.isEmpty(unMappedItem.getErpItemSkuId()) && !unMappedItem.getErpItemSkuId().equals(unMappedItem.getMtSkuId())) {
                return;
            }

            UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
            unItemBaseMapReq.setUnItemId(unMappedItem.getUnItemId());
            unItemBaseMapReq.setUnItemSkuId(OrderType.TakeoutSubType.MT_TAKEOUT.getType() == e.getOrderSubType() ?
                    unMappedItem.getMtSkuId() : unMappedItem.getUnItemSkuId());
            unItemBaseMapReq.setErpItemGuid(e.getErpItemGuid());
            unItemBaseMapReq.setErpItemSkuId(e.getErpItemSkuGuid());
            unItemBaseMapReq.setActualErpItemSkuId(e.getErpItemSkuGuid());
            unItemBaseMapReq.setUnItemTypeId(unMappedItem.getUnItemTypeId());
            unItemBaseMapReq.setExtendValue(unMappedItem.getExtendValue());
            BigDecimal erpItemCount = e.getErpItemCount().setScale(0, RoundingMode.DOWN);
            unItemBaseMapReq.setUnItemCountMapper(Integer.parseInt(erpItemCount.toPlainString()));
            unItemBindList.add(unItemBaseMapReq);
        });
        if (CollectionUtils.isEmpty(unItemBindList)) {
            return null;
        }
        UnItemBatchUnbindReq batchBindReq = new UnItemBatchUnbindReq();
        batchBindReq.setStoreGuid(storeGuid);
        batchBindReq.setTakeoutType(Integer.valueOf(type));
        batchBindReq.setUnItemUnbindList(unItemBindList);
        return batchBindReq;
    }


    /**
     * 查询第三方平台商品，并且查询对应绑定的门店商品，两两进行关联
     */
    private List<UnMappedItem> getUnItemList(String storeGuid, Integer takeoutType) throws ExecutionException, InterruptedException {
        String jsonStr = UserContextUtils.getJsonStr();
        // 请求平台方商品
        FutureTask<List<UnMappedItem>> unItemTask = new FutureTask<>(() -> {
            UserContextUtils.put(jsonStr);
            return producerFeignClient.getItem(storeGuid, takeoutType);
        });
        executorService.submit(unItemTask);

        // 请求ERP方商品
        FutureTask<List<MappingRespDTO>> erpItemTask = new FutureTask<>(() -> {
            UserContextUtils.put(jsonStr);
            ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
            itemSingleDTO.setData(storeGuid);
            //普通+菜谱模式商品都返回，避免显示的时候误认为 未绑定
            return itemFeignClient.mappingAllItems(itemSingleDTO);
        });
        executorService.submit(erpItemTask);

        // 异步任务执行
        List<UnMappedItem> unItemList = unItemTask.get();
        List<MappingRespDTO> erpItemList = erpItemTask.get();

        if (CollectionUtils.isEmpty(unItemList)) {
            return Lists.newArrayList();
        }
        log.info("查询三方商品数据:{}", JacksonUtils.writeValueAsString(unItemList));

        // 多对一绑定查询
        List<String> erpItemSkuIdList = unItemList.stream()
                .map(UnMappedItem::getErpItemSkuId)
                .collect(toList());
        List<ItemMappingDO> itemMappingDOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(erpItemSkuIdList)) {
            itemMappingDOList = itemMappingService.list(new LambdaQueryWrapper<ItemMappingDO>()
                    .eq(ItemMappingDO::getTakeoutType, takeoutType)
                    .in(ItemMappingDO::getMapperGuid, erpItemSkuIdList)
                    .eq(ItemMappingDO::getStoreGuid, storeGuid)
            );
        }
        Map<String, ItemMappingDO> mappingMap = itemMappingDOList.stream()
                .collect(Collectors.toMap(ItemMappingDO::getMapperGuid, i -> i));
        for (UnMappedItem unMappedItem : unItemList) {
            ItemMappingDO itemMappingDO = mappingMap.get(unMappedItem.getErpItemSkuId());
            if (!ObjectUtils.isEmpty(itemMappingDO)) {
                unMappedItem.setErpItemSkuId(itemMappingDO.getErpItemSkuGuid());
            }
        }

        if (takeoutType == OrderType.TakeoutSubType.ELE_TAKEOUT.getType()) {
            Map<String, UnMappedItem> unItemMap = new HashMap<>(unItemList.size());
            for (UnMappedItem unMappedItem : unItemList) {
                unItemMap.put(unMappedItem.getErpItemSkuId(), unMappedItem);
            }
            //替换映射前的编码
            final List<SkuMapDO> skuMapList = mappingService.listSourceGuidsAndGuidsByGuids(new ArrayList<>(unItemMap.keySet()));
            skuMapList.forEach(skuMapDO -> unItemMap.get(skuMapDO.getGuid()).setErpItemSkuId(skuMapDO.getSourceGuid()));
        }

        // 过滤掉不匹配的sku
        log.info("erp商品：{}", JacksonUtils.writeValueAsString(erpItemList));
        Map<String, MappingRespDTO> erpItemSkuIdentityMap = erpItemList.stream()
                .collect(Collectors.toMap(MappingRespDTO::getParentSkuGuid, m -> m, (oldValue, newValue) -> newValue));
        Map<String, String> parentGuidMapping = new HashMap<>();
        erpItemList.forEach(item -> parentGuidMapping.put(item.geteDishSkuCode(), item.getParentSkuGuid()));
        Set<String> erpItemSkuSet = erpItemSkuIdentityMap.keySet();
        for (UnMappedItem unMappedItem : unItemList) {
            if (StringUtils.isEmpty(unMappedItem.getErpItemSkuId())) {
                continue;
            }
            String skuParentGuid = parentGuidMapping.getOrDefault(unMappedItem.getErpItemSkuId(),
                    unMappedItem.getErpItemSkuId());
            if (!erpItemSkuSet.contains(skuParentGuid)) {
                log.warn("unItemName：{} 对应的 erpItemSkuId: {} 不存在于门店: {}，需重新设置", unMappedItem.getUnItemNameWithSku(),
                        unMappedItem.getErpItemSkuId(), storeGuid);
                unMappedItem.setErpItemSkuId(null);
            }
        }
        log.info("查询三方平台商品 过滤不匹配商品之后:{}", JacksonUtils.writeValueAsString(unItemList));
        return unItemList;
    }


    /**
     * 外卖映射修复
     * 将绑定前下单的外卖商品进行数据修复
     *
     * @param unItemBindUnbindReq 绑定参数
     */
    public void autoFix(UnItemBindUnbindReq unItemBindUnbindReq) {
        String jsonStr = UserContextUtils.getJsonStr();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        log.info("外卖映射修复jsonStr:{}", jsonStr);
        executorService.submit(() -> {
            try {
                UserContextUtils.put(jsonStr);
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);

                // 查询未关联数据
                String thirdSkuId = unItemBindUnbindReq.getUnItemSkuId();
                if (OrderType.TakeoutSubType.MT_TAKEOUT.getType() == unItemBindUnbindReq.getTakeoutType()
                        && !ObjectUtils.isEmpty(unItemBindUnbindReq.getMtSkuId())) {
                    // 美团特殊处理
                    thirdSkuId = unItemBindUnbindReq.getMtSkuId();
                }
                List<ItemDO> itemList = itemService.selectUnBindList(unItemBindUnbindReq.getStoreGuid(), thirdSkuId);
                if (!CollectionUtils.isEmpty(itemList)) {
                    // 调用外卖修复 -> 生成记录 -> 修复数据、扣减库存
                    List<String> skuGuidList = new ArrayList<>();
                    skuGuidList.add(unItemBindUnbindReq.getErpItemSkuId());
                    ItemStringListDTO itemStringListDTO = new ItemStringListDTO().setDataList(skuGuidList);
                    itemStringListDTO.setStoreGuid(unItemBindUnbindReq.getStoreGuid());
                    List<SkuInfoRespDTO> skuList = itemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO);
                    log.info("查询该门店商品规格信息，skuList:{}", JacksonUtils.writeValueAsString(skuList));
                    if (CollectionUtils.isEmpty(skuList)) {
                        log.warn("查询该门店商品规格信息为空,storeGuid:{},skuGuid:{}", unItemBindUnbindReq.getStoreGuid(),
                                unItemBindUnbindReq.getErpItemSkuId());
                        return;
                    }
                    SkuInfoRespDTO skuInfoRespDTO = skuList.get(0);
                    // 外卖修复
                    FixItemBiz biz = FixItemBizBuilder.build(unItemBindUnbindReq, itemList, skuInfoRespDTO);
                    fix(biz);
                }
            } catch (Exception e) {
                log.error("外卖映射异步修复异常 e:{}", e.getMessage());
            }
        });
    }


    /**
     * 扣减erp库存
     */
    public void modifyErpRepertory(List<ItemDO> unFixItemList, Long recordId) {
        if (CollectionUtils.isEmpty(unFixItemList)) {
            return;
        }
        // 筛选需要扣减的erp库存商品明细
        unFixItemList = filterModifyErpRepertoryItemList(unFixItemList, recordId);

        // 查询商品信息
        Set<String> erpItemSkuGuidSet = unFixItemList.stream().map(ItemDO::getErpItemSkuGuid).collect(Collectors.toSet());
        List<SkuTakeawayInfoRespDTO> skuInfoList = itemFeignClient.listSkuInfoAndSub(
                new ItemStringListDTO().setDataList(new ArrayList<>(erpItemSkuGuidSet)));
        Map<String, SkuTakeawayInfoRespDTO> skuInfoMap = skuInfoList.stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key1));

        // 根据门店聚合
        Map<String, List<ItemDO>> itemMapGroupByStoreGuid = unFixItemList.stream()
                .collect(Collectors.groupingBy(ItemDO::getStoreGuid));
        log.info("修复erp库存开始,recordId:{},itemMapGroupByStoreGuid:{}", recordId, JacksonUtils.writeValueAsString(itemMapGroupByStoreGuid));
        for (Map.Entry<String, List<ItemDO>> storeEntry : itemMapGroupByStoreGuid.entrySet()) {
            String storeGuid = storeEntry.getKey();
            List<ItemDO> listByStoreGuid = storeEntry.getValue();
            // 查询库存绑定门店
            storeGuid = getStockBindStore(storeGuid);
            // 根据订单聚合
            Map<String, List<ItemDO>> itemMapGroupByOrderViewId = listByStoreGuid.stream()
                    .collect(Collectors.groupingBy(ItemDO::getOrderViewId));
            for (Map.Entry<String, List<ItemDO>> entry : itemMapGroupByOrderViewId.entrySet()) {
                String orderViewId = entry.getKey();
                List<ItemDO> itemListByOrderViewId = entry.getValue();
                // 修复erp库存
                fixErpStock(storeGuid, orderViewId, itemListByOrderViewId, skuInfoMap);
            }
        }
        log.info("修复erp库存结束,recordId:{}", recordId);
    }

    /**
     * 修复erp库存
     */
    private void fixErpStock(String storeGuid, String orderViewId, List<ItemDO> itemListByOrderViewId,
                             Map<String, SkuTakeawayInfoRespDTO> skuInfoMap) {
        UserContext userContext = UserContextUtils.get();
        try {
            // 扣减商品信息
            List<DepositDish> dishes = buildDepositDishes(itemListByOrderViewId, skuInfoMap);
            DepositErpSyncDTO erpSyncDTO = new DepositErpSyncDTO();
            erpSyncDTO.setBusinessType(1);
            erpSyncDTO.setBusinessDate(LocalDate.now());
            erpSyncDTO.setThirdNo(orderViewId);
            erpSyncDTO.setStoreId(storeGuid);
            erpSyncDTO.setUserGuid(userContext.getUserGuid());
            erpSyncDTO.setUsername(userContext.getUserName());
            erpSyncDTO.setDepositDishes(dishes);
            String url = erpHost + "/api/inventory/deposit/adjust";
            log.info("调整商品调用erp,url:{}，请求参数：{}", url, JSONUtil.parse(erpSyncDTO));
            String result = HttpRequest.post(url)
                    .header("enterpriseGuid", userContext.getEnterpriseGuid())
                    .body(JacksonUtils.writeValueAsString(erpSyncDTO))
                    .execute()
                    .body();
            log.info("调整商品调用erp,返回结果：{}", result);
            Thread.sleep(getRandSleepTime());
        } catch (InterruptedException e) {
            log.error("调整商品调用erp失败,e", e);
        }
    }


    /**
     * 筛选需要扣减的erp库存商品明细
     */
    private List<ItemDO> filterModifyErpRepertoryItemList(List<ItemDO> unFixItemList, Long recordId) {
        // 待接单,已取消的订单不扣减库存
        List<Long> idList = unFixItemList.stream().map(ItemDO::getId).collect(toList());
        unFixItemList = itemService.filterNoRequiredFixList(idList);
        if (CollectionUtils.isEmpty(unFixItemList)) {
            log.info("存在待接单，已取消的订单,recordId:{},orderItemGuids:{}", recordId, idList);
            return unFixItemList;
        }
        // 过滤上线后，不扣减历史数据得erp库存
        LocalDateTime onlineTime = LocalDateTime.parse("2022-06-14 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        unFixItemList = unFixItemList.stream().filter(e -> e.getGmtCreate().isAfter(onlineTime)).collect(toList());
        if (CollectionUtils.isEmpty(unFixItemList)) {
            log.info("历史数据不扣减库存,recordId:{},orderItemGuids:{}", recordId, idList);
            return unFixItemList;
        }
        // 查询是否调整
        Set<Long> orderGuidSet = unFixItemList.stream().map(e -> Long.parseLong(e.getOrderGuid())).collect(Collectors.toSet());
        List<Long> orderGuids = tradeClientService.listByOrderGuids(new AdjustOrderQueryDTO().setOrderGuids(new ArrayList<>(orderGuidSet)));
        // 调整过的订单不进行扣减库存
        unFixItemList = unFixItemList.stream().filter(e -> !orderGuids.contains(Long.valueOf(e.getOrderGuid()))).collect(toList());
        log.info("需要修复erp库存的商品数据:{}", JacksonUtils.writeValueAsString(unFixItemList));
        return unFixItemList;
    }

    /**
     * 构建扣减erp库存的商品明细
     */
    private List<DepositDish> buildDepositDishes(List<ItemDO> itemListByOrderViewId, Map<String, SkuTakeawayInfoRespDTO> skuInfoMap) {
        List<DepositDish> dishes = Lists.newArrayList();
        Map<String, BigDecimal> groupBySumMap = itemListByOrderViewId.stream().collect(Collectors.groupingBy(ItemDO::getErpItemSkuGuid,
                Collectors.reducing(BigDecimal.ZERO, ItemDO::getActualItemCount, BigDecimal::add)));
        for (Map.Entry<String, BigDecimal> sku : groupBySumMap.entrySet()) {
            SkuTakeawayInfoRespDTO skuInfo = skuInfoMap.get(sku.getKey());
            // 判断是否套餐商品
            if (ItemTypeEnum.PKG.getCode() == skuInfo.getItemType() && CollectionUtils.isNotEmpty(skuInfo.getSubgroupList())) {
                skuInfo.getSubgroupList().forEach(subItem -> {
                    if (0 == subItem.getPickNum()) {
                        subItem.getSubItemSkuList().forEach(subSku -> {
                            DepositDish dish = new DepositDish();
                            dish.setSkuId(subSku.getSkuGuid());
                            dish.setSkuCount(subSku.getItemNum().multiply(sku.getValue()));
                            // 0 入库 1 出库
                            dish.setType(1);
                            dishes.add(dish);
                        });
                    }
                });
                continue;
            }
            DepositDish dish = new DepositDish();
            dish.setSkuId(sku.getKey());
            dish.setSkuCount(sku.getValue());
            // 0 入库 1 出库
            dish.setType(1);
            dishes.add(dish);
        }
        return dishes;
    }

    /**
     * 查询库存绑定门店
     */
    private String getStockBindStore(String storeGuid) {
        StockStoreBindResqDTO storeBindDO = producerFeignClient.getBindStockStore(storeGuid);
        if (Objects.nonNull(storeBindDO)) {
            String branchStoreGuid = storeBindDO.getBranchStoreGuid();
            if (!StringUtils.isEmpty(storeGuid)) {
                storeGuid = branchStoreGuid;
            }
        }
        return storeGuid;
    }

    /**
     * 构建查询异常数据列表DTO
     */
    private TakeoutItemDataFixReqDTO buildFixReqDTO(LocalDateTime startDateTime, LocalDateTime endDateTime, List<FixItemDO> itemList) {
        TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        List<TakeoutAndStoreReqDTO> takeoutAndStore = Lists.newArrayList();
        for (FixItemDO fixItemDO : itemList) {
            TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
            takeoutAndStoreReqDTO.setStoreGuid(fixItemDO.getStoreGuid());
            takeoutAndStoreReqDTO.setTakeoutItemNumber(fixItemDO.getThirdSkuId());
            takeoutAndStore.add(takeoutAndStoreReqDTO);
        }
        reqDTO.setTakeoutAndStore(takeoutAndStore);
        reqDTO.setStartDateTime(startDateTime);
        reqDTO.setEndDateTime(endDateTime);
        return reqDTO;
    }

    private Integer getRandSleepTime() {
        Random rand = new Random();
        return rand.nextInt(50) + 50;
    }

}
