package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationService.ServiceFallBack.class)
public interface OrganizationService {

    /**
     * 根据门店guid查询门店详细信息
     *
     * @param storeGuid 门店guid
     * @return StoreDTO
     */
    @ApiOperation("根据门店guid查询门店详细信息")
    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据门店guid查询门店基础信息以及营业日
     *
     * @param storeGuid 门店guid
     * @return StoreDTO
     */
    @PostMapping("/store/query_store_base_by_guid")
    StoreDTO queryStoreBaseByGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据品牌guid查询品牌下所有门店Guid
     *
     * @param brandGuid 品牌guid
     * @return 品牌下所有门店
     */
    @ApiOperation("根据品牌guid查询品牌下所有门店Guid")
    @PostMapping("/brand/query_store_guid_list_by_brand_guid")
    List<String> queryStoreGuidListByBrandGui(@RequestBody String brandGuid);

    /**
     * 通过guid 查询门店列表
     */
    @PostMapping("/store/query_store_by_idlist")
    List<StoreDTO> queryStoreByIdList(List<String> storeGuidList);

    /**
     * 根据门店guid查询门店关联的品牌信息
     *
     * @param storeGuid 门店guid
     * @return 品牌信息
     */
    @ApiOperation(value = "根据门店guid查询门店关联的品牌信息", notes = "若门店未关联到品牌则返回为null，后期一个门店可关联多个品牌")
    @RequestMapping("/store/query_brand_by_storeguid")
    BrandDTO queryBrandByStoreGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据品牌guid查询品牌信息
     *
     * @param brandGuid 品牌guid
     * @return 品牌信息
     */
    @ApiOperation("根据品牌guid查询品牌信息")
    @PostMapping("/brand/query_brand_by_guid")
    BrandDTO queryBrandByGuid(@RequestParam("brandGuid") String brandGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<OrganizationService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrganizationService create(Throwable cause) {
            return new OrganizationService() {

                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    log.error("根据门店guid查询门店详细信息 storeGuid={}, e={}", storeGuid, cause.getMessage());
                    return null;
                }

                @Override
                public StoreDTO queryStoreBaseByGuid(String storeGuid) {
                    log.error("根据门店guid查询门店基础信息以及营业日 storeGuid={}, e={}", storeGuid, cause.getMessage());
                    return null;
                }

                @Override
                public List<String> queryStoreGuidListByBrandGui(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "queryStoreGuidListByBrandGui", brandGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<StoreDTO> queryStoreByIdList(List<String> storeGuidList) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByIdList", JacksonUtils.writeValueAsString(storeGuidList), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public BrandDTO queryBrandByStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryBrandByStoreGuid", storeGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public BrandDTO queryBrandByGuid(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "queryBrandByGuid", brandGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

            };
        }
    }
}
