package com.holder.saas.store.takeaway.consumers.service;

import com.holder.saas.store.takeaway.consumers.entity.domain.LogDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import com.holder.saas.store.takeaway.consumers.utils.DistributedUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class TakeoutLogFactory {

    public LogDO createCustomerInfo(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("姓名：", StringEscapeUtils.unescapeHtml(orderDO.getCustomerName()));
        // fixme 自营外卖怎么处理? 为什么美团用customerPhone饿了么用privacyPhone，产品要求吗？ by lys
        //美团用customerPhone饿了么用privacyPhone
        if (orderDO.getOrderSubType() == 0) {
            if (!ObjectUtils.isEmpty(orderDO.getCustomerPhone())) {
                List<String> customerLists = JacksonUtils.toObjectList(String.class, orderDO.getCustomerPhone());
                if (!ObjectUtils.isEmpty(customerLists)) {
                    String str = "";
                    if (customerLists.size() == 1) {
                        str = customerLists.get(0).replaceAll("&", ",");
                    } else if (customerLists.size() > 1) {
                        for (int i = 0; i < customerLists.size(); i++) {
                            str = customerLists.get(i).replaceAll("&", ",") + "\n";
                        }
                        str = str.substring(0, str.lastIndexOf("\n"));
                    }
                    bodyMap.put("联系电话：", str);
                }
            }
        } else if (orderDO.getOrderSubType() == 1) {
            if (!ObjectUtils.isEmpty(orderDO.getCustomerPhone())) {
                List<String> privacyLists = JacksonUtils.toObjectList(String.class, orderDO.getPrivacyPhone());
                if (!ObjectUtils.isEmpty(privacyLists)) {
                    String str = "";
                    if (privacyLists.size() == 1) {
                        str = privacyLists.get(0).replaceAll("&", ",");
                    } else if (privacyLists.size() > 0) {
                        for (int i = 0; i < privacyLists.size(); i++) {
                            str = privacyLists.get(i).replaceAll("&", ",") + "\n";
                        }
                        str = str.substring(0, str.lastIndexOf("\n"));
                    }
                    bodyMap.put("联系电话：", str);
                }
            }
        } else if (orderDO.getOrderSubType() == 6) {
            if (!ObjectUtils.isEmpty(orderDO.getCustomerPhone())) {
            List<String> customerLists = JacksonUtils.toObjectList(String.class, orderDO.getCustomerPhone());
                bodyMap.put("联系电话：", String.join(", ", customerLists));
            }
        }
        bodyMap.put("送餐地址：", orderDO.getCustomerAddress());
        bodyMap.put("送餐时间：", (orderDO.getEstimateDeliveredTime() == null
                || DateTimeUtils.localDateTime2Mills(orderDO.getEstimateDeliveredTime()) == 0)
                ? "立即送达" : DateTimeUtils.localDateTime2String(orderDO.getEstimateDeliveredTime()));
        if (!StringUtils.isEmpty(orderDO.getOrderRemark())) bodyMap.put("备注：", orderDO.getOrderRemark());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(false)
                .setTime(orderDO.getCreateTime())
                .setOperator(customerName(orderDO))
                .setDescription("创建订单")
                .setShowInEndpoint(true)
                .setTitle("送餐信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderCreated(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("订单号：", orderDO.getOrderViewId());
        bodyMap.put("下单时间：", DateTimeUtils.localDateTime2String(orderDO.getCreateTime()));
        bodyMap.put("订单来源：", orderSource(orderDO));
        bodyMap.put("订单金额：", formatMoney(orderDO.getTotal()));
        bodyMap.put("实付金额：", formatMoney(orderDO.getCustomerActualPay()));
        if (!StringUtils.isEmpty(orderDO.getInvoiceTitle())) {
            String str = orderDO.getInvoiceTitle();
            if (!StringUtils.isEmpty(orderDO.getTaxpayerId())) {
                str = str + ";" + orderDO.getTaxpayerId();
            }
            bodyMap.put("发票：", str);
        }
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderViewId())
                .setShowInWebPage(true)
                .setTime(orderDO.getCreateTime())
                .setOperator(customerName(orderDO))
                .setDescription("创建订单")
                .setShowInEndpoint(true)
                .setTitle("订单信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }


    public LogDO createOrderAccepted(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("接单时间：", DateTimeUtils.localDateTime2String(orderDO.getAcceptTime()));
        bodyMap.put("接单操作员：", orderDO.getAcceptStaffName());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getAcceptTime())
                .setOperator(staffName(orderDO.getAcceptStaffName()))
                .setDescription("确认接单，" + acceptDevice(orderDO))
                .setShowInEndpoint(true)
                .setTitle("接单信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }
    public LogDO createDeliveryError(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("取消原因：", orderDO.getCancelReason());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getCancelTime())
                .setDescription("异常订单，原因：" + orderDO.getCancelReason() + "，" + acceptDevice(orderDO))
                .setShowInEndpoint(true)
                .setTitle("配送信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderCanceledAsReject(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("拒单时间：", DateTimeUtils.localDateTime2String(orderDO.getCancelTime()));
        bodyMap.put("拒单原因：", orderDO.getCancelReason());
        bodyMap.put("拒单操作员：", orderDO.getCancelStaffName());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getCancelTime())
                .setOperator(staffName(orderDO.getCancelStaffName()))
                .setDescription("拒接订单，理由：" + orderDO.getCancelReason() + "，" + acceptDevice(orderDO))
                .setShowInEndpoint(true)
                .setTitle("取消信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderCanceled(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("取消时间：", DateTimeUtils.localDateTime2String(orderDO.getCancelTime()));
        bodyMap.put("取消原因：", orderDO.getCancelReason());
        bodyMap.put("取消操作员：", orderDO.getCancelStaffName());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getCancelTime())
                .setOperator(orderDO.getCancelStaffName())
                .setDescription("取消订单，理由：" + orderDO.getCancelReason() + "，" + acceptDevice(orderDO))
                .setShowInEndpoint(true)
                .setTitle("取消信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderShippingDistribute(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("配送时间：", DateTimeUtils.localDateTime2String(orderDO.getDeliveryTime()));
        bodyMap.put("配送员：", orderDO.getShipperName());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setOperator(shipperName(orderDO))
                .setDescription("骑手接单，配送员" + orderDO.getShipperName() + "，电话" + orderDO.getShipperPhone())
                .setShowInEndpoint(true)
                .setTitle("配送信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderShipping(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("配送时间：", DateTimeUtils.localDateTime2String(orderDO.getDeliveryTime()));
        bodyMap.put("配送员：", orderDO.getShipperName());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getDeliveryTime())
                .setOperator(shipperName(orderDO))
                .setDescription("骑手配送中，配送员" + orderDO.getShipperName() + "，电话" + orderDO.getShipperPhone())
                .setShowInEndpoint(true)
                .setTitle("配送信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderShippingCompleted(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("送达时间：", DateTimeUtils.localDateTime2String(orderDO.getDeliveredTime()));
        bodyMap.put("配送员：", orderDO.getShipperName());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getDeliveryTime())
                .setOperator(shipperName(orderDO))
                .setDescription("骑手配送完成，配送员" + orderDO.getShipperName() + "，电话" + orderDO.getShipperPhone())
                .setShowInEndpoint(true)
                .setTitle("配送信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }



    public LogDO createOrderFinished(OrderDO orderDO) {
        LocalDateTime completeTime = orderDO.getCompleteTime() != null ? orderDO.getCompleteTime() : DateTimeUtils.now();
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("完成时间：", DateTimeUtils.localDateTime2String(completeTime));
        bodyMap.put("配送状态：", "已确认送达");
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(completeTime)
                .setOperator(shipperName(orderDO))
                .setDescription("骑手已送达，订单已完成")
                .setShowInEndpoint(true)
                .setTitle("完成信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderCancelReq(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("取消订单申请时间：", DateTimeUtils.localDateTime2String(orderDO.getCancelReqTime()));
        bodyMap.put("取消订单原因：", orderDO.getCancelReqReason());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getCancelReqTime())
                .setOperator(customerName(orderDO))
                .setDescription("用户申请取消订单，"
                        + "金额：" + formatMoney(orderDO.getTotal()) + "，"
                        + "取消理由：" + orderDO.getCancelReqReason())
                .setShowInEndpoint(true)
                .setTitle("取消订单申请")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderCancelCancelReq(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("申请时间：", DateTimeUtils.localDateTime2String(DateTimeUtils.now()));
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(false)
                .setTime(DateTimeUtils.now())
                .setOperator(customerName(orderDO))
                .setDescription("用户取消“取消订单”申请")
                .setShowInEndpoint(false)
                .setTitle("取消“取消订单”申请")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }



    public LogDO createOrderCancelAgreed(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        // 存在商家版同意取消订单的场景，所以cancelReplyTime不一定有值
        String dateTime = Optional.ofNullable(orderDO.getCancelReplyTime())
                .map(DateTimeUtils::localDateTime2String).orElse(DateTimeUtils.nowString());
        bodyMap.put("取消订单处理时间：", dateTime);
        bodyMap.put("取消订单处理结果：", "同意");
        bodyMap.put("处理操作员：", orderDO.getCancelReplyStaffName());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getCancelReplyTime())
                .setOperator(staffName(orderDO.getCancelReplyStaffName()))
                .setDescription("同意取消订单")
                .setShowInEndpoint(true)
                .setTitle("取消订单结果")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }



    public LogDO createOrderCancelDisagreed(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        // 存在商家版不同意取消订单的场景，所以cancelReplyTime不一定有值
        String dateTime = Optional.ofNullable(orderDO.getCancelReplyTime())
                .map(DateTimeUtils::localDateTime2String).orElse(DateTimeUtils.nowString());
        bodyMap.put("取消订单处理时间：", dateTime);
        bodyMap.put("取消订单处理结果：", "不同意");
        bodyMap.put("取消订单处理回复：", orderDO.getCancelReplyMessage());
        bodyMap.put("处理操作员：", orderDO.getCancelReplyStaffName());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getCancelReplyTime())
                .setOperator(staffName(orderDO.getCancelReplyStaffName()))
                .setDescription("不同意取消订单")
                .setShowInEndpoint(true)
                .setTitle("取消订单结果")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderCancelReqArbitrationEffective(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("仲裁时间：", DateTimeUtils.localDateTime2String(DateTimeUtils.now()));
        bodyMap.put("仲裁结果：", "同意");
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(DateTimeUtils.now())
                .setOperator(orderSource(orderDO) + "(客服)")
                .setDescription("客服仲裁取消申请结果有效")
                .setShowInEndpoint(true)
                .setTitle("仲裁信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderRefundReq(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("退款申请时间：", DateTimeUtils.localDateTime2String(orderDO.getRefundReqTime()));
        bodyMap.put("退款菜品：", orderDO.getCustomerRefundItem());
        bodyMap.put("退款原因：", orderDO.getRefundReqReason());
        bodyMap.put("退款金额：", formatMoney(orderDO.getCustomerRefund()));
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getRefundReqTime())
                .setOperator(customerName(orderDO))
                .setDescription("用户申请退款，"
                        + "金额：" + formatMoney(orderDO.getCustomerRefund()) + "，"
                        + "退款理由：" + orderDO.getRefundReqReason())
                .setShowInEndpoint(true)
                .setTitle("退款/退单申请")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderCancelRefundReq(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("申请时间：", DateTimeUtils.localDateTime2String(DateTimeUtils.now()));
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(false)
                .setTime(DateTimeUtils.now())
                .setOperator(customerName(orderDO))
                .setDescription("用户取消退款申请")
                .setShowInEndpoint(false)
                .setTitle("退款/退单申请")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }



    public LogDO createOrderRefundAgreed(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        // 存在商家版同意退款的场景，此时refundReplyTime是没有值的
        String dateTime = Optional.ofNullable(orderDO.getRefundReplyTime())
                .map(DateTimeUtils::localDateTime2String).orElse(DateTimeUtils.nowString());
        bodyMap.put("退款处理时间：", dateTime);
        bodyMap.put("退款处理结果：", "同意");
        bodyMap.put("处理操作员：", orderDO.getRefundReplyStaffName());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getRefundReplyTime())
                .setOperator(staffName(orderDO.getRefundReplyStaffName()))
                .setDescription("同意退款/退单")
                .setShowInEndpoint(true)
                .setTitle("退款/退单结果")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }



    public LogDO createOrderRefundDisagreed(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        // 存在商家版同意退款的情况，此时refundReplyTime是没有值的
        String dateTime = Optional.ofNullable(orderDO.getRefundReplyTime())
                .map(DateTimeUtils::localDateTime2String).orElse(DateTimeUtils.nowString());
        bodyMap.put("退款处理时间：", dateTime);
        bodyMap.put("退款处理结果：", "不同意");
        bodyMap.put("退款处理回复：", orderDO.getRefundReplyMessage());
        bodyMap.put("处理操作员：", orderDO.getRefundReplyStaffName());
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(orderDO.getRefundReplyTime())
                .setOperator(staffName(orderDO.getRefundReplyStaffName()))
                .setDescription("不同意退款/退单")
                .setShowInEndpoint(true)
                .setTitle("退款/退单结果")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderRefundReqArbitrationEffective(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("仲裁时间：", DateTimeUtils.localDateTime2String(DateTimeUtils.now()));
        bodyMap.put("仲裁结果：", "同意");
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(true)
                .setTime(DateTimeUtils.now())
                .setOperator(orderSource(orderDO) + "(客服)")
                .setDescription("客服仲裁取消申请结果有效")
                .setShowInEndpoint(true)
                .setTitle("仲裁信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    public LogDO createOrderReminded(OrderDO orderDO) {
        Map<String, String> bodyMap = new LinkedHashMap<>();
        bodyMap.put("催单时间：", DateTimeUtils.localDateTime2String(DateTimeUtils.now()));
        bodyMap.put("催单说明：", "催单");
        return new LogDO()
                .setGuid(DistributedUtils.id())
                .setOrderGuid(orderDO.getOrderGuid())
                .setOrderId(orderDO.getOrderId())
                .setShowInWebPage(false)
                .setTime(DateTimeUtils.now())
                .setOperator(customerName(orderDO))
                .setDescription("催单")
                .setShowInEndpoint(true)
                .setTitle("催单信息")
                .setBody(JacksonUtils.writeValueAsString(bodyMap));
    }

    private String customerName(OrderDO orderDO) {
        return orderDO.getCustomerName() + "(客户)";
    }

    private String shipperName(OrderDO orderDO) {
        return orderDO.getShipperName() + "(骑手)";
    }

    private String staffName(String staffName) {
        return staffName + "(员工)";
    }

    private String acceptDevice(OrderDO orderDO) {
        int acceptDeviceType = orderDO.getAcceptDeviceType() != null ? orderDO.getAcceptDeviceType() : -1;
        return "接单设备" + BaseDeviceTypeEnum.getDesc(acceptDeviceType) + orderDO.getAcceptDeviceId();
    }

    private String orderSource(OrderDO orderDO) {
        return OrderType.TakeoutSubType.ofType(orderDO.getOrderSubType()).getSource();
    }

    private String formatMoney(BigDecimal money) {
        BigDecimal integerPart = money.setScale(0, RoundingMode.DOWN);
        BigDecimal formatMoney = money.compareTo(integerPart) > 0 ? money : integerPart;
        return "￥" + formatMoney;
    }
}
