package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemExtendsDO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 外卖菜品扩展表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
public interface ItemExtendsService extends IService<ItemExtendsDO> {

    BigDecimal getTotalCostPrice(List<String> orderGuidList);
}
