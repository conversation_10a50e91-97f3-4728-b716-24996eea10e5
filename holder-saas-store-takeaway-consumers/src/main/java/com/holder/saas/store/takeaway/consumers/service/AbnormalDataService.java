package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.AbnormalDataDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holderzone.saas.store.dto.takeaway.request.MoveDTO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO;

import java.util.List;

/**
 * <p>
 * 外卖异常数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
public interface AbnormalDataService extends IService<AbnormalDataDO> {

    /**
     * 外卖异常数据分页查询
     *
     * @param reqDTO 外卖异常数据分页列表请求
     * @return 外卖异常数据分页列表
     */
    Page<TakeoutItemAbnormalDataRespDTO> page(TakeoutItemAbnormalDataReqDTO reqDTO);

    /**
     * 外卖商品迁移异常数据
     */
    void move(MoveDTO moveDTO);

    /**
     * 数据修复列表
     *
     * @param reqDTO 数据修复列表请求
     * @return 数据修复列表
     */
    List<TakeoutItemDataFixRespDTO> listDataFix(TakeoutItemDataFixReqDTO reqDTO);

    /**
     * 查询外卖异常数据
     *
     * @param reqDTO 条件
     * @return 外卖异常数据
     */
    List<ItemDO> listFixItem(TakeoutItemDataFixReqDTO reqDTO);

    void removeByTakeoutItemIds(List<Long> takeoutItemIds);
}
