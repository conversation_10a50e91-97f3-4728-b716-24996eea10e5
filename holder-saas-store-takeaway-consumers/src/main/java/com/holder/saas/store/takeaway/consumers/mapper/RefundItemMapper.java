package com.holder.saas.store.takeaway.consumers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.RefundItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外卖退菜商品明细表 Mapper 接口
 * </p>
 */
public interface RefundItemMapper extends BaseMapper<RefundItemDO> {

    List<RefundItemDO> listItemGroupByOrderItemGuid(@Param("orderGuid") String orderGuid);

    void updateRefundSuccess(@Param("orderGuid") String orderGuid);

}
