package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 外卖修复审核记录表
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hst_takeout_fix_record")
public class FixRecordDO implements Serializable {

    private static final long serialVersionUID = 9165654893673305704L;


    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 修复开始时间
     */
    private LocalDateTime startTime;

    /**
     * 修复结束时间
     */
    private LocalDateTime endTime;

    /**
     * 修复条数
     */
    private Integer fixCount;

    /**
     * 提交状态 0-待审核 1-审核通过(已修复)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 修改人guid
     */
    private String modifiedUserGuid;

    /**
     * 修改人名称
     */
    private String modifiedUserName;

}
