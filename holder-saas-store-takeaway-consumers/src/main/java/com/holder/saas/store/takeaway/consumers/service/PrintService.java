package com.holder.saas.store.takeaway.consumers.service;

import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.takeaway.UnItem;

import java.util.List;
import java.util.Map;

public interface PrintService {

    void printAll(OrderReadDO orderReadDO, int businessType, Map<String, SkuTakeawayInfoRespDTO> mapOfSkuPartDTO, List<UnItem> unItemList);

    String printBill(OrderReadDO orderReadDO);

    void printCancelBill(OrderReadDO orderReadDO);

    /**
     * 打印外卖单和后厨点菜单
     *
     * @param orderReadDO   订单信息
     * @return 结果
     */
    String printKitchen(OrderReadDO orderReadDO);

    String printLabel(OrderReadDO orderReadDO);
}
