package com.holder.saas.store.takeaway.consumers.controller;


import com.holder.saas.store.takeaway.consumers.service.AbnormalDataService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.request.MoveDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 外卖异常数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api(tags = "外卖相关接口")
@RequestMapping("/abnormal_data")
public class TakeoutAbnormalDataController {

    private final AbnormalDataService abnormalDataService;

    /**
     * 外卖异常数据分页查询
     *
     * @param reqDTO 外卖异常数据分页列表请求
     * @return 外卖异常数据分页列表
     */
    @ApiOperation(value = "外卖异常数据分页查询")
    @PostMapping("/page")
    public Page<TakeoutItemAbnormalDataRespDTO> page(@RequestBody @Valid TakeoutItemAbnormalDataReqDTO reqDTO) {
        log.info("外卖异常数据分页查询,入参,reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        return abnormalDataService.page(reqDTO);
    }

    /**
     * 外卖商品迁移异常数据
     */
    @ApiOperation(value = "外卖商品迁移异常数据")
    @PostMapping("/move")
    public void move(@RequestBody MoveDTO moveDTO) {
        log.info("外卖商品迁移异常数据 moveDTO={}", JacksonUtils.writeValueAsString(moveDTO));
        abnormalDataService.move(moveDTO);
    }

    /**
     * 数据修复列表
     *
     * @param reqDTO 数据修复列表请求
     * @return 数据修复列表
     */
    @ApiOperation(value = "数据修复列表")
    @PostMapping("/data_fix_list")
    public List<TakeoutItemDataFixRespDTO> listDataFix(@RequestBody @Valid TakeoutItemDataFixReqDTO reqDTO) {
        log.info("数据修复列表,入参,reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        return abnormalDataService.listDataFix(reqDTO);
    }

}
