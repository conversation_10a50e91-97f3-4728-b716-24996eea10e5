package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 外卖异常数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HstTakeoutAbnormalData对象", description = "外卖异常数据表")
@TableName("hst_takeout_abnormal_data")
public class AbnormalDataDO implements Serializable {

    private static final long serialVersionUID = 3093199927050889337L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "外卖商品id")
    private Long takeoutItemId;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 订单来源：0=美团，1=饿了么，6=赚餐自营外卖
     *
     * @see com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType
     */
    @ApiModelProperty(value = "订单来源：0=美团，1=饿了么，6=赚餐自营外卖")
    private Integer orderSubType;

    @ApiModelProperty(value = "商品名称：外卖名称+规格名称")
    private String itemName;

    @ApiModelProperty(value = "商品单价")
    private BigDecimal itemPrice;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal itemCount;

    @ApiModelProperty(value = "外卖平台方规格id")
    private String thirdSkuId;

    @ApiModelProperty(value = "外卖创建时间")
    private LocalDateTime takeoutCreateTime;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    /**
     * 这里不用逻辑删除，直接删除以保证查询效率
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;


}
