package com.holder.saas.store.takeaway.consumers.mapstruct;

import com.holder.saas.store.takeaway.consumers.entity.domain.AbnormalDataDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.RefundItemDO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderItemProblemDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderItemRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * itemDo 转换工具
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/04
 */
@Component
@Mapper(componentModel = "spring")
public interface ItemMapstruct {

    /**
     * 商品信息集合转B端订单明细商品信息集合
     *
     * @param itemDoList 商品信息集合
     * @return result
     */
    List<BusinessTakeoutOrderItemRespDTO> doList2BusinessTakeoutOrderItemRespDtoList(List<ItemDO> itemDoList);

    ItemDO problemDTO2ItemDO(TakeoutOrderItemProblemDTO problemDTO);

    List<ItemDO> problemDTOList2ItemDOList(List<TakeoutOrderItemProblemDTO> problemDTOList);

    @Mappings({
            @Mapping(target = "takeoutItemId", source = "id"),
            @Mapping(target = "takeoutCreateTime", source = "gmtCreate"),
            @Mapping(target = "isDelete", ignore = true),
            @Mapping(target = "gmtCreate", ignore = true),
            @Mapping(target = "gmtModified", ignore = true),
            @Mapping(target = "id", ignore = true)
    })
    AbnormalDataDO itemDO2AbnormalDataDO(ItemDO itemDO);

    List<AbnormalDataDO> itemDOList2AbnormalDataDOList(List<ItemDO> itemDOList);


    @Mappings({
            @Mapping(target = "itemGuid", source = "orderItemGuid"),
            @Mapping(target = "refundCount", source = "itemCount"),
    })
    ItemDO refundItemDO2ItemDO(RefundItemDO refundItemDO);

    List<ItemDO> refundItemDOList2ItemDOList(List<RefundItemDO> refundItemDOList);
}
