package com.holder.saas.store.takeaway.consumers.service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface DistributedService {

    Long rawId(String tag);

    String nextId(String tag);

    List<String> nextBatchId(String tag, long count);

    String nextOrderGuid();

    List<String> nextBatchOrderGuid(long count);

    String nextFixRecordGuid();

    List<String> nextBatchFixItemGuid(long count);
}
