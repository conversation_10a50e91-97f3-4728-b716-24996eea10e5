package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.manage.OrderConsumeManage;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.TAKEAWAY_CONSUMERS_JD_ORDER_TOPIC,
        tags =  {RocketMqConfig.TAKEAWAY_CONSUMERS_JD_ORDER_TAG},
        consumerGroup = RocketMqConfig.TAKEAWAY_JD_ORDER_CONSUMERS_GROUP
)
@AllArgsConstructor
public class ConsumerJdUnOrderListener extends AbstractRocketMqConsumer<RocketMqTopic, UnOrder>{

    private final OrderConsumeManage orderConsumeManage;
    @Override
    public boolean consumeMsg(UnOrder unOrder, MessageExt messageExt){
        if (log.isInfoEnabled()) {
            log.info("收到京东回调消息，unOrder:{}", JacksonUtils.writeValueAsString(unOrder));
        }
        return orderConsumeManage.process(unOrder);
    }


}
