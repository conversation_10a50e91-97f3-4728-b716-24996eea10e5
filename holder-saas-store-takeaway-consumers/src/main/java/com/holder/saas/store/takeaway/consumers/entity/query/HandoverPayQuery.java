package com.holder.saas.store.takeaway.consumers.entity.query;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverPayQuery
 * @date 2018/09/12 10:34
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@NoArgsConstructor
public class HandoverPayQuery implements Serializable {

    private static final long serialVersionUID = 8335834368027084347L;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 开班时间
     */
    private LocalDateTime startTime;

    /**
     * 交班时间
     */
    private LocalDateTime endTime;
}
