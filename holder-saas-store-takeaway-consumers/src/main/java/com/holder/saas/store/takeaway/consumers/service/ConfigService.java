package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.ConfigDO;
import com.holderzone.saas.store.dto.takeaway.TakeoutAutoRcvDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutConfigDTO;

/**
 * <p>
 * 外卖配置服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
public interface ConfigService extends IService<ConfigDO> {

    /**
     * 查询店家接单配置
     */
    TakeoutConfigDTO selectStoreConfig(TakeoutConfigDTO takeoutConfigDTO);

    /**
     * 修改店家是否接单配置
     */
    Boolean saveStoreConfig(TakeoutConfigDTO takeoutConfigDTO);

    /**
     * 查询店家接单配置
     */
    TakeoutAutoRcvDTO selectStoreAutoRcvConfig(TakeoutAutoRcvDTO takeoutAutoRcvDTO);

    /**
     * 修改店家是否接单配置
     */
    Boolean saveStoreAutoRcvConfig(TakeoutAutoRcvDTO takeoutAutoRcvDTO);

    /**
     * 查询店家配置
     */
    ConfigDO selectByStoreGuid(String storeGuid);

}
