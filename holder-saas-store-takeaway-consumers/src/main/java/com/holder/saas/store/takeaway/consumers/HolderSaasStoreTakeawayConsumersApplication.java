package com.holder.saas.store.takeaway.consumers;

import com.holderzone.sdk.util.SpringContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

@SpringBootApplication
@EnableEurekaClient
@EnableFeignClients
@EnableSwagger2
@EnableApolloConfig
@EnableScheduling
public class HolderSaasStoreTakeawayConsumersApplication {

    public static void main(String[] args) {

        ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreTakeawayConsumersApplication.class, args);

        // 设置Spring容器上下文
        SpringContextUtils.getInstance().setCfgContext(app);
    }
}
