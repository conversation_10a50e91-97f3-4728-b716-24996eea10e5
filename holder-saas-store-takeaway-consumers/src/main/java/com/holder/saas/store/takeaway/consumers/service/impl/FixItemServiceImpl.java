package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixItemDO;
import com.holder.saas.store.takeaway.consumers.mapper.FixItemMapper;
import com.holder.saas.store.takeaway.consumers.mapstruct.FixItemMapstruct;
import com.holder.saas.store.takeaway.consumers.service.FixItemService;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixItemDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 外卖审核记录商品明细服务实现类
 * </p>
 */
@Service
public class FixItemServiceImpl extends ServiceImpl<FixItemMapper, FixItemDO> implements FixItemService {

    @Autowired
    private FixItemMapstruct fixItemMapstruct;

    @Override
    public List<TakeoutFixItemDTO> listByRecordId(Long recordId) {
        QueryWrapper<FixItemDO> qw = new QueryWrapper<>();
        qw.lambda().eq(FixItemDO::getRecordId, recordId);
        List<FixItemDO> list = list(qw);
        return fixItemMapstruct.doList2DTOList(list);
    }

    @Override
    public List<String> queryStoreGuids(Long recordId) {
        return baseMapper.queryStoreGuids(recordId);
    }
}
