package com.holder.saas.store.takeaway.consumers.service;

import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.order.common.PushOrderBillsBO;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface ErpService {

    void reduceStockForOrder(OrderReadDO orderReadDO, Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO);

    PushOrderBillsBO reduceStockForOrderMDM(OrderReadDO orderReadDO, Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO);

    void reduceStockForOrderWeihaiErp(PushOrderBillsBO pushOrderBillsBO);

    /**
     * 反结账：商家拒单导致整单退
     */
    void addStockForOrderMDM(OrderReadDO orderReadDO, Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) throws IOException;

    /**
     * 部分退款/或者用户发起整单退款
     */
    void addStockForOrderMDM(OrderReadDO orderReadDO, List<ItemDO> oldItemList, List<ItemDO> items,
                             Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO);

    void addStockForOrderWeihaiErp(DepositErpSyncDTO erpSyncDTO);

}
