package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import com.holder.saas.store.takeaway.consumers.service.AutoAcceptOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Set;

@Slf4j
@Service
public class AutoAcceptOrderServiceImpl implements AutoAcceptOrderService {

    @Autowired
    private RedisTemplate redisTemplate;

    public static final String AUTO_ACCEPT_ORDER_QUEUE = "AUTO_ACCEPT_ORDER_QUEUE";

    @Override
    public void createOrder(OrderDO orderDO) {
        if (!orderDO.getIsAutoAccept()) {
            return;
        }
        String redisKey = AUTO_ACCEPT_ORDER_QUEUE + ":" + orderDO.getStoreGuid();
        redisTemplate.opsForZSet().add(redisKey, orderDO.getOrderGuid(), LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli());
    }

    @Override
    public void removeOrder(OrderDO orderDO) {
        String redisKey = AUTO_ACCEPT_ORDER_QUEUE + ":" + orderDO.getStoreGuid();
        redisTemplate.opsForZSet().remove(redisKey, orderDO.getOrderGuid());
    }

    @Override
    public Set<String> getOrderQueue(String storeGuid) {
        String redisKey = AUTO_ACCEPT_ORDER_QUEUE + ":" + storeGuid;
        return redisTemplate.opsForZSet().range(redisKey, 0, -1);
    }
}
