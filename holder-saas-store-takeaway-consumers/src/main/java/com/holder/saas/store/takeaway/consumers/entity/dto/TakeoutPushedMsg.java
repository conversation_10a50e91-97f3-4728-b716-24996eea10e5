package com.holder.saas.store.takeaway.consumers.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class TakeoutPushedMsg implements Serializable {

    private static final long serialVersionUID = 1381808430512003279L;

    /**
     * 是否需要自动接单
     */
    private boolean autoRcv;

    /**
     * master设备逻辑id
     */
    private String deviceId;

    /**
     * 门店外卖Guid
     */
    private String orderGuid;

    /**
     * master设备逻辑deviceNo
     */
    private String deviceNo;
}
