package com.holder.saas.store.takeaway.consumers.mapstruct;

import com.holder.saas.store.takeaway.consumers.entity.domain.ConfigDO;
import com.holderzone.saas.store.dto.takeaway.TakeoutAutoRcvDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutConfigDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring")
public interface ConfigMapstruct {

    ConfigDO fromTakeoutAutoRcv(TakeoutAutoRcvDTO takeoutAutoRcvDTO);

    ConfigDO fromTakeoutConfig(TakeoutConfigDTO takeoutConfigDTO);
}
