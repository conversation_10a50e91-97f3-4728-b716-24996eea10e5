package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.entity.dto.TakeoutItemMappingEnum;
import com.holder.saas.store.takeaway.consumers.manage.FixManager;
import com.holder.saas.store.takeaway.consumers.service.ItemBindExtendInfoService;
import com.holder.saas.store.takeaway.consumers.service.MappingService;
import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holder.saas.store.takeaway.consumers.service.rpc.CloudEnterpriseFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemMappingReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemMappingRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping("/item_mapping")
public class TakeoutMappingController {

    @Resource
    private MappingService mappingService;

    @Resource
    private OrderService orderService;

    @Resource
    private FixManager fixManager;

    @Resource
    private ItemBindExtendInfoService itemBindExtendInfoService;

    @Resource
    private CloudEnterpriseFeignClient cloudEnterpriseFeignClient;

    @Resource
    private DynamicHelper dynamicHelper;


    @ApiOperation(value = "查询平台商品列表")
    @PostMapping("/query")
    public TakeoutItemMappingRespDTO getItemBinding(@RequestBody TakeoutItemMappingReqDTO takeoutItemMappingReqDTO)
            throws ExecutionException, InterruptedException {
        if (log.isInfoEnabled()) {
            log.info("查询平台商品列表：入参{}", JacksonUtils.writeValueAsString(takeoutItemMappingReqDTO));
        }
        return mappingService.getItemBinding(takeoutItemMappingReqDTO);
    }

    @ApiOperation(value = "批量查询平台商品列表")
    @PostMapping("/query_items")
    public TakeawayBatchMappingResult getItems(@RequestBody UnItemQueryReq unItemQueryReq) {
        if (log.isInfoEnabled()) {
            log.info("批量查询平台商品列表：入参{}", JacksonUtils.writeValueAsString(unItemQueryReq));
        }
        return mappingService.getItems(unItemQueryReq);
    }

    @PostMapping("/own/query")
    public TakeoutItemMappingRespDTO getOwnItemBinding(@RequestBody TakeoutItemMappingReqDTO takeoutItemMappingReqDTO)
            throws ExecutionException, InterruptedException {
        if (log.isInfoEnabled()) {
            log.info("查询自营商品列表：入参{}", JacksonUtils.writeValueAsString(takeoutItemMappingReqDTO));
        }
        return mappingService.getOwnItemBinding(takeoutItemMappingReqDTO);
    }

    @ApiOperation(value = "设置商品映射")
    @PostMapping("/bind")
    public void bindItem(@RequestBody UnItemBindUnbindReq unItemBindUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("设置商品映射：入参{}", JacksonUtils.writeValueAsString(unItemBindUnbindReq));
        }
        UnItemBindUnbindReq request = new UnItemBindUnbindReq();
        BeanUtils.copyProperties(unItemBindUnbindReq, request);
        mappingService.bindItem(unItemBindUnbindReq);
        // 外卖映射异步修复
        fixManager.autoFix(request);
    }

    @ApiOperation(value = "批量绑定外卖商品")
    @PostMapping("/batch_bind")
    public void batchBind(@RequestBody @Valid UnItemBatchBindUnbindReq unItemBatchBindUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("[批量绑定外卖商品]入参={}", JacksonUtils.writeValueAsString(unItemBatchBindUnbindReq));
        }
        mappingService.batchBind(unItemBatchBindUnbindReq);
    }

    @PostMapping("/update-bind-extend-info/{storeGuid}/{takeoutType}")
    public void updateBindExtendInfo(@PathVariable("storeGuid") String storeGuid, @PathVariable("takeoutType") Integer takeoutType,
                                     @RequestBody UnMappedItem item) {
        if (log.isInfoEnabled()) {
            log.info("更新商品绑定拓展信息：入参 storeGuid: {}, takeoutType: {}, item: {}", storeGuid, takeoutType, JacksonUtils.writeValueAsString(item));
        }
        mappingService.updateItemBindExtendInfo(storeGuid, takeoutType, item);
    }

    @ApiOperation(value = "解除商品映射")
    @PostMapping("/unbind")
    public void unbindItem(@RequestBody UnItemBindUnbindReq unItemBindUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("解除商品映射：入参{}", JacksonUtils.writeValueAsString(unItemBindUnbindReq));
        }
        mappingService.unbindItem(unItemBindUnbindReq);
    }

    @ApiOperation(value = "批量删除商品映射")
    @PostMapping("/batch_unbind")
    public void batchUnbindItem(@RequestBody UnItemBatchUnbindReq unItemBatchUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("批量删除商品映射：入参{}", JacksonUtils.writeValueAsString(unItemBatchUnbindReq));
        }
        mappingService.batchUnbindItem(unItemBatchUnbindReq);
    }

    @ApiOperation(value = "批量解绑外卖商品")
    @PostMapping("/batch_unbind_out")
    public void batchUnbind(@RequestBody UnItemBatchBindUnbindReq batchBindUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("[批量解绑外卖商品]入参{}", JacksonUtils.writeValueAsString(batchBindUnbindReq));
        }
        mappingService.batchUnbind(batchBindUnbindReq);
    }

    @ApiOperation(value = "赚餐同步门店商品")
    @PostMapping("/tcd/sync")
    public void syncTcdItemMappingCount(@RequestBody @Valid TcdSyncItemMappingDTO syncItemMappingDTO) {
        if (log.isInfoEnabled()) {
            log.info("赚餐同步门店商品：入参{}", JacksonUtils.writeValueAsString(syncItemMappingDTO));
        }
        // 切库
        String enterpriseGuid = syncItemMappingDTO.getEnterpriseGuid();
        // 判断企业是否存在
        if (!cloudEnterpriseFeignClient.hasEnterprise(enterpriseGuid)) {
            return;
        }
        if (log.isInfoEnabled()) {
            log.info("根据enterpriseGuid({})切换数据源", enterpriseGuid);
        }
        dynamicHelper.changeDatasource(enterpriseGuid);
        UserContextUtils.putErp(enterpriseGuid);
        mappingService.syncTcdItemMapping(syncItemMappingDTO);
    }
}
