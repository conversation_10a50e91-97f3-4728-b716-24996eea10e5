package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName: ItemBindExtendInfo
 * @Description: 外卖商品与商品库商品绑定关系及拓展数据
 * @Author: CC
 * @Date 2021/4/27 11:38
 * @ModifyRecords: v1.0 new
 */
@Data
@Accessors(chain = true)
@TableName("hst_takeout_item_bind_extend_info")
public class ItemBindExtendInfoDo implements Serializable {
    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 门店GUID
     */
    private String storeGuid;

    @NotNull(
            message = "外卖类型不得为空"
    )
    @Min(
            value = 0L,
            message = "外卖类型(0：美团，1：饿了么,2:自营,3:赚餐)"
    )
    @Max(
            value = 3L,
            message = "外卖类型(0：美团，1：饿了么,2:自营,3:赚餐)"
    )
    @ApiModelProperty(
            value = "外卖类型(0：美团，1：饿了么,2:自营,3:赚餐)",
            required = true
    )
    private Integer takeoutType;

    /**
     * 系统商品库规格ID
     */
    private String erpItemSkuId;

    /**
     * 平台方商品抵扣商品库商品数量
     */
    private Integer unItemCountMapper;

    /**
     * 平台方商品规格ID
     */
    private String unItemSkuId;
}
