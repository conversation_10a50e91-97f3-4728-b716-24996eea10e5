package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * hst_takeout_item
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hst_takeout_item")
public class ItemDO implements Serializable {

    private static final long serialVersionUID = 9126807575367828724L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 门店名
     */
    private String storeName;

    /**
     * 品牌guid
     */
    private String brandGuid;

    /**
     * 品牌name
     */
    private String brandName;

    /**
     * 订单GUID
     */
    private String orderGuid;

    /**
     * 菜品SKU
     */
    private String itemSku;

    /**
     * 菜品GUID
     */
    private String itemGuid;

    /**
     * 菜品名称
     */
    private String itemName;

    /**
     * 菜品名称（平台）
     */
    private String itemNamePlatform;

    /**
     * 菜品来源：0=美团，1=饿了么，6=赚餐自营外卖
     *
     * @see com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType
     */
    private Integer orderSubType;

    /**
     * 菜品code
     */
    private String itemCode;

    /**
     * 单位
     */
    private String itemUnit;

    /**
     * 单价
     */
    private BigDecimal itemPrice;

    /**
     * 菜品数量
     */
    private BigDecimal itemCount;

    /**
     * 退菜数量
     */
    private BigDecimal refundCount;

    /**
     * 平台实际商品数量（外卖商品有商品映射时改字段值与上面那个字段值不一致）
     */
    private BigDecimal actualItemCount;
    /**
     * 菜品小计
     */
    private BigDecimal itemTotal;

    /**
     * 菜品规格 多元素使用"",""分割开
     */
    private String itemSpec;

    /**
     * 特殊属性 多元素使用"",""分割开
     */
    private String itemProperty;

    /**
     * 餐盒单价
     */
    private BigDecimal boxPrice;

    /**
     * 餐盒数量
     */
    private BigDecimal boxCount;

    /**
     * 餐盒小计
     */
    private BigDecimal boxTotal;

    /**
     * 所属口袋 0=1号口袋  1=2号口袋
     */
    private Integer cartId;

    /**
     * 折扣掉的金额单价
     */
    private BigDecimal discountPrice;

    /**
     * 菜品折扣比例 1=无折扣，0.85=8.5折扣
     */
    private BigDecimal discountRatio;

    /**
     * 折扣掉的金额合计
     */
    private BigDecimal discountTotal;

    /**
     * 实付价
     */
    private BigDecimal actualPrice;

    /**
     * 结算类型 0=普通消费菜品  1=赠送菜品
     */
    private Integer settleType;

    /**
     * 三方唯一标识
     */
    private String thirdSkuId;

    /**
     * 门店商品规格guid
     */
    private String erpItemSkuGuid;

    /**
     * 门店商品名称
     */
    private String erpItemName;

    /**
     * 门店商品单价
     */
    private BigDecimal erpItemPrice;

    /**
     * 订单完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 订单营业日
     */
    private LocalDate businessDay;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否调整过商品
     */
    private Boolean isAdjustItem;

    /**
     * 外卖订单流水号
     */
    @TableField(exist = false)
    private String orderViewId;

    /**
     * 外卖核算价
     */
    private BigDecimal takeawayAccountingPrice;

    /**
     * 套餐商品明细
     */
    @TableField(exist = false)
    private List<SkuInfoPkgDTO> listPkg;

    @TableField(exist = false)
    private String erpItemGuid;

    /**
     * 子菜json
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "子菜json")
    private String subItemInfo;
}