package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.OrderTradeDetailDO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderTradeDetailDTO;

/**
 * <p>
 * 订单结算明细 服务类
 * </p>
 */
public interface OrderTradeDetailService extends IService<OrderTradeDetailDO> {

    void create(TakeoutOrderTradeDetailDTO orderTradeDetailDTO);
}
