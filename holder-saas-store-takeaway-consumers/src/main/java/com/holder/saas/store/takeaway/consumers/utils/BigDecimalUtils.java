package com.holder.saas.store.takeaway.consumers.utils;

import java.math.BigDecimal;

public class BigDecimalUtils {

    public static String formatCount(BigDecimal money) {
        BigDecimal integerPart = money.setScale(0, BigDecimal.ROUND_DOWN);
        BigDecimal formatCount = money.compareTo(integerPart) > 0 ? money : integerPart;
        return formatCount.toString();
    }

    public static BigDecimal nullValue(BigDecimal money) {
        return money == null ? BigDecimal.ZERO : money;
    }
}
