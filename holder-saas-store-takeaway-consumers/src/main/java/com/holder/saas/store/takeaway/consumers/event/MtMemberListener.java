package com.holder.saas.store.takeaway.consumers.event;


import com.alibaba.fastjson.JSON;
import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.service.rpc.MemberFeignService;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.member.PlatformMemberConsumeRecordDTO;
import com.holderzone.saas.store.dto.member.PlatformMemberDTO;
import com.holderzone.saas.store.dto.takeaway.MtMemberMqReq;
import com.holderzone.saas.store.enums.takeaway.MtBusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023/9/13
 * @description 美团大众联名卡新会员推送消费
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.MT_CARD_MEMBER_TOPIC,
        tags = RocketMqConfig.MT_CARD_MEMBER_TAG,
        consumerGroup = RocketMqConfig.MT_CARD_MEMBER_GROUP)
public class MtMemberListener extends AbstractRocketMqConsumer<RocketMqTopic, MtMemberMqReq> {

    @Autowired
    private MemberFeignService memberFeignService;

    @Override
    public boolean consumeMsg(MtMemberMqReq mqReq, MessageExt messageExt) {
        log.info("[美团大众联名卡]消费入参：{}", JacksonUtils.writeValueAsString(mqReq));
        if (Objects.equals(MtBusinessTypeEnum.NEW_MEMBER.getCode(), mqReq.getBusinessType()) && !StringUtils.isEmpty(mqReq.getBody())) {
            log.info("[美团大众联名卡][新会员推送]消费入参：{}", JacksonUtils.writeValueAsString(mqReq));
            PlatformMemberDTO platformMember = JSON.parseObject(mqReq.getBody(), PlatformMemberDTO.class);
            return memberFeignService.judgeAndMember(platformMember).getData();
        }
        if (Objects.equals(MtBusinessTypeEnum.INTEGRAL_CALLBACK.getCode(), mqReq.getBusinessType()) && !StringUtils.isEmpty(mqReq.getBody())) {
            log.info("[美团大众联名卡][会员卡积分推送]消费入参：{}", JacksonUtils.writeValueAsString(mqReq));
            PlatformMemberConsumeRecordDTO consumeRecordDTO = JSON.parseObject(mqReq.getBody(), PlatformMemberConsumeRecordDTO.class);
            memberFeignService.createConsumeRecord(consumeRecordDTO);
        }
        return true;
    }

}
