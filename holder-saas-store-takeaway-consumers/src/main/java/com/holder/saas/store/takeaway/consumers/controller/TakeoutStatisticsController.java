package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsQueryDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.ItemRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/takeout")
@Api(description = "美团相关接口")
public class TakeoutStatisticsController {

    private final OrderService orderService;

    @Autowired
    public TakeoutStatisticsController(OrderService orderService) {
        this.orderService = orderService;
    }

    @PostMapping(value = "/get_order_money")
    @ApiOperation(value = "查询交易金额和交易数量", notes = "查询交易金额和交易数量")
    public HandoverPayDTO getOrderMoney(@RequestBody @Validated HandoverPayQueryDTO handoverPayQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询交易金额和交易数量：入参{}", JacksonUtils.writeValueAsString(handoverPayQueryDTO));
        }
        return orderService.getOrderMoney(handoverPayQueryDTO);
    }

    @PostMapping(value = "/get_op_stats")
    @ApiOperation(value = "查询营业概况", notes = "查询营业概况")
    public TakeoutStatsDTO getOpStats(@RequestBody @Validated TakeoutStatsQueryDTO takeoutStatsQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询营业概况：入参{}", JacksonUtils.writeValueAsString(takeoutStatsQueryDTO));
        }
        return orderService.getOpStats(takeoutStatsQueryDTO);
    }

    @PostMapping(value = "/get_receipt_stats")
    @ApiOperation(value = "查询收款统计", notes = "查询收款统计")
    public TakeoutStatsDTO getReceiptStats(@RequestBody @Validated TakeoutStatsQueryDTO takeoutStatsQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询收款统计：入参{}", JacksonUtils.writeValueAsString(takeoutStatsQueryDTO));
        }
        return orderService.getReceiptStats(takeoutStatsQueryDTO);
    }

    @PostMapping(value = "/list_item_sale")
    @ApiOperation(value = "查询商品分类销售统计", notes = "查询商品分类销售统计")
    public List<ItemRespDTO> listItemSale(@RequestBody @Validated DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("查询商品分类销售统计：入参{}", JacksonUtils.writeValueAsString(request));
        }
        return orderService.listTakeOutItemSale(request);
    }

    @PostMapping(value = "/list_goods")
    @ApiOperation(value = "查询商品销售统计", notes = "查询商品销售统计")
    public List<ItemRespDTO> goods(@RequestBody @Validated DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("查询商品销售统计：入参{}", JacksonUtils.writeValueAsString(request));
        }
        return orderService.listTakeOutGoodsSale(request);
    }

    @PostMapping(value = "/get_trade_stats")
    @ApiOperation(value = "查询用餐类型统计", notes = "查询用餐类型统计")
    public TakeoutStatsDTO getTradeStats(@RequestBody @Validated TakeoutStatsQueryDTO takeoutStatsQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询用餐类型统计：入参{}", JacksonUtils.writeValueAsString(takeoutStatsQueryDTO));
        }
        return orderService.getTradeStats(takeoutStatsQueryDTO);
    }
}
