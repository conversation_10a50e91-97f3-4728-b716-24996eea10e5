package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 外卖自动修复异常数据
 */
@Data
@TableName("hst_takeout_auto_recovery")
public class AutoRecoveryDO implements Serializable {

    private static final long serialVersionUID = 3093199927050889337L;

    /**
     * 外卖订单明细表 id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "异常来源")
    private String exSource;

    @ApiModelProperty(value = "异常来源")
    private String exMsg;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;
}
