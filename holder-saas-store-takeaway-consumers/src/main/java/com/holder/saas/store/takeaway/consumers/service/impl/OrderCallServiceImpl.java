package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.LogDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.OrderCallDO;
import com.holder.saas.store.takeaway.consumers.mapper.LogMapper;
import com.holder.saas.store.takeaway.consumers.mapper.OrderCallMapper;
import com.holder.saas.store.takeaway.consumers.service.LogService;
import com.holder.saas.store.takeaway.consumers.service.OrderCallService;
import org.springframework.stereotype.Service;

@Service
public class OrderCallServiceImpl extends ServiceImpl<OrderCallMapper, OrderCallDO> implements OrderCallService {
}
