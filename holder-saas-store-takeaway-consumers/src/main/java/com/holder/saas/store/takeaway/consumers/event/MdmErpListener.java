package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.service.rpc.ErpFeignService;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holder.saas.store.takeaway.consumers.utils.HttpUtil;
import com.holder.saas.store.takeaway.consumers.utils.ThrowableExtUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import com.holderzone.saas.store.dto.order.common.PushOrderBillsBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.MDM_MESSAGE_TOPIC,
        tags = RocketMqConfig.MDM_REDUCE_TAG,
        consumerGroup = RocketMqConfig.MDM_MESSAGE_GROUP)
public class MdmErpListener extends AbstractRocketMqConsumer<RocketMqTopic, PushOrderBillsBO> {

    @Value("${mdm.host:#{null}}")
    private String mdmRequestHost;

    private final DynamicHelper dynamicHelper;

    @Autowired
    public MdmErpListener(DynamicHelper dynamicHelper) {
        this.dynamicHelper = dynamicHelper;
    }

    @Override
    public boolean consumeMsg(PushOrderBillsBO pushOrderBillsBO, MessageExt messageExt) {
        String orderId = pushOrderBillsBO.getSalesOrderId();
        if (log.isInfoEnabled()) {
            log.info("订单[{}]扣减MDM库存，入参:{}", orderId, JacksonUtils.writeValueAsString(pushOrderBillsBO));
        }
        UserContextUtils.put(messageExt.getProperty(RocketMqConfig.USER_INFO));

        // 切库
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (log.isInfoEnabled()) {
            log.info("扣减MDM库存:订单[{}]根据enterpriseGuid({})切换数据源", orderId, enterpriseGuid);
        }
        dynamicHelper.changeDatasource(enterpriseGuid);

        try {
            //fixme 只扣除何师烧烤的库存
            String httpRequestUrl = String.format("%s/api/mdm/Store/bill", mdmRequestHost).intern();
            log.info("订单({})MDM库存扣减httpRequestUrl={},pushOrderBillsBO={}", orderId, httpRequestUrl, JacksonUtils.writeValueAsString(pushOrderBillsBO));
            String result = HttpUtil.doPostJson(httpRequestUrl, JacksonUtils.writeValueAsString(pushOrderBillsBO));
            log.info("订单({})MDM库存扣减result={}", orderId, result);

        } catch (Exception e) {
            log.error("订单({})扣减MDM库存消费发生异常：{}", orderId, ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            dynamicHelper.removeThreadLocalDatabaseInfo();
            UserContextUtils.remove();
        }
        return true;
    }

}
