package com.holder.saas.store.takeaway.consumers.service;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemClientService
 * @date 2018/09/30 11:41
 * @description 微信调用
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-item", fallbackFactory = ItemClientService.FallBack.class)
public interface ItemClientService {

    @PostMapping("item/get_erp_sku_guid")
    Map<String, String> getErpSkuGuids(@RequestBody SingleDataDTO skuIdList);

    /**
     * 根据商品guid获取商品及套餐信息
     *
     * @param itemStringListDTO 商品guid
     * @return 商品及套餐信息
     */
    @ApiOperation(value = "根据商品guid获取商品及套餐信息")
    @PostMapping("/item_pkg/list_pkg_item_info")
    List<ItemInfoRespDTO> listPkgItemInfo(@RequestBody ItemStringListDTO itemStringListDTO);

    @PostMapping("/item/parent_sku")
    List<SkuInfoRespDTO> findParentSKUS(@RequestBody List<String> skuGuids);

    /**
     * 查询规格详情列表
     */
    @PostMapping("/item/list_sku_info")
    List<SkuTakeawayInfoRespDTO> listSkuInfoAndSub(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 根据规格guid查询规格信息
     *
     * @param skuGuidList 规格guid列表
     * @return 规格信息列表
     */
    @ApiOperation(value = "根据规格guid查询规格信息")
    @PostMapping("/item_sku/list_sku_info")
    List<SkuInfoRespDTO> listSkuInfo(@RequestBody List<String> skuGuidList);


    @Component
    class FallBack implements FallbackFactory<ItemClientService> {
        private static final Logger logger = LoggerFactory.getLogger(ItemClientService.FallBack.class);

        @Override
        public ItemClientService create(Throwable throwable) {
            return new ItemClientService() {

                @Override
                public Map<String, String> getErpSkuGuids(SingleDataDTO skuguids) {
                    logger.error("获取erpGuid错误，e={}", throwable.getMessage());
                    throw new ParameterException("获取erpGuid调用异常");
                }

                @Override
                public List<ItemInfoRespDTO> listPkgItemInfo(ItemStringListDTO itemStringListDTO) {
                    logger.error("获取商品及套餐信息，e={}", throwable.getMessage());
                    throw new ParameterException("获取商品及套餐信息调用异常");
                }

                @Override
                public List<SkuInfoRespDTO> findParentSKUS(List<String> skuGuids) {
                    logger.error("通过sku查询品牌库sku错误，e={}", throwable.getMessage());
                    throw new ParameterException("查询sku查询品牌库sku异常");
                }

                @Override
                public List<SkuTakeawayInfoRespDTO> listSkuInfoAndSub(ItemStringListDTO itemStringListDTO) {
                    logger.error("查询sku错误，e={}", throwable.getMessage());
                    throw new ParameterException("查询sku异常");
                }

                @Override
                public List<SkuInfoRespDTO> listSkuInfo(List<String> skuGuidList) {
                    logger.error("查询sku错误，e={}", throwable.getMessage());
                    throw new ParameterException("查询sku异常");
                }
            };
        }
    }

}
