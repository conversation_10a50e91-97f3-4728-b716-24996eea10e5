package com.holder.saas.store.takeaway.consumers.service;

import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import com.holder.saas.store.takeaway.consumers.entity.dto.TakeoutPushedMsg;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.service.rpc.OrgFeignClient;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.takeaway.UnOrderCbMsgType;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TakeoutMsgFactory {

    private final OrgFeignClient orgFeignClient;

    @Autowired
    public TakeoutMsgFactory(OrgFeignClient orgFeignClient) {
        this.orgFeignClient = orgFeignClient;
    }

    public BusinessMessageDTO createOrderCreated(OrderDO orderDO, boolean acceptAhead, boolean isAutoAccept) {
        String storeGuid = orderDO.getStoreGuid();

        isAutoAccept = !acceptAhead && isAutoAccept;

        BusinessMessageDTO businessMessageDTO = buildTakeoutBusinessMsg(orderDO);
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BMT_DETAIL_ORDER.getId());
        switch (OrderType.TakeoutSubType.ofType(orderDO.getOrderSubType())) {
            case MT_TAKEOUT:
                businessMessageDTO.setSubject("您有新的美团订单，请及时处理。");
                break;
            case ELE_TAKEOUT:
                businessMessageDTO.setSubject("您有新的饿了么订单，请及时处理。");
                break;
            case OWN_TAKEOUT:
                businessMessageDTO.setSubject("您有新的自营外卖平台订单，请及时处理。");
                break;
            default:
                businessMessageDTO.setSubject("您有新的订单，请及时处理。");
                break;
        }
        TakeoutPushedMsg takeoutPushedMsg = new TakeoutPushedMsg();
        takeoutPushedMsg.setAutoRcv(isAutoAccept);
        //if (autoRcv) {
        StoreDeviceDTO deviceStatusInCloud = orgFeignClient.findMasterDevice(storeGuid);
        takeoutPushedMsg.setDeviceId(deviceStatusInCloud.getDeviceGuid());
        takeoutPushedMsg.setOrderGuid(orderDO.getOrderGuid());
        takeoutPushedMsg.setDeviceNo(deviceStatusInCloud.getDeviceNo());//bugfix7514
        //}
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(takeoutPushedMsg));
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderConfirmed(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutStateMsg(orderDO);
        businessMessageDTO.setDetailMessageType(UnOrderCbMsgType.ORDER_CONFIRMED);
        businessMessageDTO.setSubject("");
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderShipping(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutStateMsg(orderDO);
        businessMessageDTO.setDetailMessageType(UnOrderCbMsgType.ORDER_SHIPPING);
        businessMessageDTO.setSubject("");
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderFinished(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutStateMsg(orderDO);
        businessMessageDTO.setDetailMessageType(UnOrderCbMsgType.ORDER_FINISHED);
        businessMessageDTO.setSubject("");
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderCanceled(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutBusinessMsg(orderDO);
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BMT_DETAIL_TAKEOUT_NODE.getId());
        switch (OrderType.TakeoutSubType.ofType(orderDO.getOrderSubType())) {
            case MT_TAKEOUT:
                businessMessageDTO.setSubject("您的#" + orderDO.getOrderDaySn() + "号美团订单已被取消");
                break;
            case ELE_TAKEOUT:
                businessMessageDTO.setSubject("您的#" + orderDO.getOrderDaySn() + "号饿了么订单已被取消");
                break;
            default:
                businessMessageDTO.setSubject("您的#" + orderDO.getOrderDaySn() + "号订单已被取消");
                break;
        }
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderReminded(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutBusinessMsg(orderDO);
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BMT_DETAIL_TAKEOUT_NODE.getId());
        switch (OrderType.TakeoutSubType.ofType(orderDO.getOrderSubType())) {
            case MT_TAKEOUT:
                businessMessageDTO.setSubject("您有新的美团催单，请及时处理。");
                break;
            case ELE_TAKEOUT:
                businessMessageDTO.setSubject("您有新的饿了么催单，请及时处理。");
                break;
            default:
                businessMessageDTO.setSubject("您有新的催单，请及时处理。");
                break;
        }
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderDiningOut(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutBusinessMsg(orderDO);
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BMT_DETAIL_TAKEOUT_NODE.getId());
        switch (OrderType.TakeoutSubType.ofType(orderDO.getOrderSubType())) {
            case MT_TAKEOUT:
                businessMessageDTO.setSubject("您有美团订单已出餐，请及时处理。");
                break;
            case ELE_TAKEOUT:
                businessMessageDTO.setSubject("您有饿了么订单已出餐，请及时处理。");
                break;
            case TCD_TAKEOUT:
                businessMessageDTO.setSubject("您有赚餐外卖订单已出餐，请及时处理。");
                break;
            default:
                businessMessageDTO.setSubject("您有订单已出餐，请及时处理。");
                break;
        }
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }


    public BusinessMessageDTO createDeliveryError(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutBusinessMsg(orderDO);
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BMT_DETAIL_TAKEOUT_NODE.getId());
        switch (OrderType.TakeoutSubType.ofType(orderDO.getOrderSubType())) {
            case MT_TAKEOUT:
                businessMessageDTO.setSubject("您的美团订单发起一城飞客配送失败，请主动联系一城飞客发起配送单。");
                break;
            case ELE_TAKEOUT:
                businessMessageDTO.setSubject("您的饿了么订单发起一城飞客配送失败，请主动联系一城飞客发起配送单。");
                break;
            default:
                businessMessageDTO.setSubject("您的订单发起一城飞客配送失败，请主动联系一城飞客发起配送单。");
                break;
        }
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }


    public BusinessMessageDTO createOrderCancelReq(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutBusinessMsg(orderDO);
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BMT_DETAIL_TAKEOUT_NODE.getId());
        setAbnormalOrderSubject(businessMessageDTO, orderDO.getOrderSubType());
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderCancelCancelReq(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutStateMsg(orderDO);
        businessMessageDTO.setDetailMessageType(UnOrderCbMsgType.ORDER_CANCEL_CANCEL_REQ);
        businessMessageDTO.setSubject("");
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderCancelAgreed(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutStateMsg(orderDO);
        businessMessageDTO.setDetailMessageType(UnOrderCbMsgType.ORDER_CANCEL_AGREED);
        businessMessageDTO.setSubject("");
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderCancelDisagreed(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutStateMsg(orderDO);
        businessMessageDTO.setDetailMessageType(UnOrderCbMsgType.ORDER_CANCEL_DISAGREED);
        businessMessageDTO.setSubject("");
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderRefundReq(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutBusinessMsg(orderDO);
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BMT_DETAIL_TAKEOUT_NODE.getId());
        businessMessageDTO.setSubject("您有新的【退单】请求，请及时处理");
        setAbnormalOrderSubject(businessMessageDTO, orderDO.getOrderSubType());
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderCancelRefundReq(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutStateMsg(orderDO);
        businessMessageDTO.setDetailMessageType(UnOrderCbMsgType.ORDER_CANCEL_REFUND_REQ);
        businessMessageDTO.setSubject("");
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderRefundAgreed(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutStateMsg(orderDO);
        businessMessageDTO.setDetailMessageType(UnOrderCbMsgType.ORDER_REFUND_AGREED);
        businessMessageDTO.setSubject("");
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderRefundDisagreed(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutStateMsg(orderDO);
        businessMessageDTO.setDetailMessageType(UnOrderCbMsgType.ORDER_REFUND_DISAGREED);
        businessMessageDTO.setSubject("");
        setOrderGuidAsContent(businessMessageDTO, orderDO.getOrderGuid());
        return businessMessageDTO;
    }

    public BusinessMessageDTO createOrderMappingFailed(OrderReadDO orderReadDO) {
        BusinessMessageDTO businessMessageDTO = buildTakeoutBusinessMsg(orderReadDO.getStoreGuid(), orderReadDO.getStoreName());
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BMT_DETAIL_TAKEOUT_NODE.getId());
        String platform = "";
        if (OrderType.TakeoutSubType.MT_TAKEOUT.getType() == orderReadDO.getOrderSubType()) {
            platform = OrderType.TakeoutSubType.MT_TAKEOUT.getSource();
        } else if (OrderType.TakeoutSubType.ELE_TAKEOUT.getType() == orderReadDO.getOrderSubType()) {
            platform = OrderType.TakeoutSubType.ELE_TAKEOUT.getSource();
        } else if (OrderType.TakeoutSubType.OWN_TAKEOUT.getType() == orderReadDO.getOrderSubType()) {
            platform = OrderType.TakeoutSubType.OWN_TAKEOUT.getSource();
        }
        String subject = "您的#" + orderReadDO.getOrderDaySn() + "号" + platform + "订单，菜品无法打印到后厨，请到收银台取单。";
        businessMessageDTO.setSubject(subject);
        setOrderGuidAsContent(businessMessageDTO, orderReadDO.getOrderGuid());
        return businessMessageDTO;
    }

    private BusinessMessageDTO buildTakeoutBusinessMsg(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(BusinessMsgTypeEnum.BUSINESS_MSG_TYPE.getId());
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid(orderDO.getStoreGuid());
        businessMessageDTO.setStoreName(orderDO.getStoreName());
        return businessMessageDTO;
    }

    private BusinessMessageDTO buildTakeoutBusinessMsg(String storeGuid, String storeName) {
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(BusinessMsgTypeEnum.BUSINESS_MSG_TYPE.getId());
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid(storeGuid);
        businessMessageDTO.setStoreName(storeName);
        return businessMessageDTO;
    }

    private BusinessMessageDTO buildTakeoutStateMsg(OrderDO orderDO) {
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(BusinessMsgTypeEnum.TAKEOUT_STATE_MSG_TYPE.getId());
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid(orderDO.getStoreGuid());
        businessMessageDTO.setStoreName(orderDO.getStoreName());
        return businessMessageDTO;
    }

    private void setAbnormalOrderSubject(BusinessMessageDTO businessMessageDTO, Integer orderSubType) {
        switch (OrderType.TakeoutSubType.ofType(orderSubType)) {
            case MT_TAKEOUT:
                businessMessageDTO.setSubject("您有新的美团异常订单，请及时处理。");
                break;
            case ELE_TAKEOUT:
                businessMessageDTO.setSubject("您有新的饿了么异常订单，请及时处理。");
                break;
            default:
                businessMessageDTO.setSubject("您有新的异常订单，请及时处理。");
                break;
        }
    }

    private void setOrderGuidAsContent(BusinessMessageDTO businessMessageDTO, String orderGuid) {
        TakeoutPushedMsg takeoutPushedMsg = new TakeoutPushedMsg().setOrderGuid(orderGuid);
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(takeoutPushedMsg));
    }
}
