package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 外卖菜品扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hst_takeout_item_extends")
@ApiModel(value="HstTakeoutItemExtends对象", description="外卖菜品扩展表")
public class ItemExtendsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    @ApiModelProperty(value = "item表的item_guid")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "是否删除 0：false,1:true")
    private Boolean isDelete;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 门店商品规格guid
     */
    @ApiModelProperty(value = "门店商品规格guid")
    private String erpItemSkuGuid;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    /**
     * 子菜json
     */
    @ApiModelProperty(value = "子菜json")
    private String subItemInfo;

}
