package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/takeout")
@Api(description = "美团相关接口")
public class TakeoutPrintController {

    private final OrderService orderService;

    @Autowired
    public TakeoutPrintController(OrderService orderService) {
        this.orderService = orderService;
    }

    @ApiOperation(value = "打印账单", notes = "打印账单")
    @PostMapping(value = "/print_bill", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String printBill(@RequestBody @Validated TakeoutOrderDTO takeawayOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印账单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        }
        return orderService.printBill(takeawayOrderDTO);
    }

    @ApiOperation(value = "打印后厨菜单", notes = "打印后厨菜单")
    @PostMapping(value = "/print_kitchen", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String printKitchen(@RequestBody @Validated TakeoutOrderDTO takeawayOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印后厨菜单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        }
        return orderService.printKitchen(takeawayOrderDTO);
    }

    @ApiOperation(value = "打印标签单", notes = "打印标签单")
    @PostMapping(value = "/print_label", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String printLabel(@RequestBody @Validated TakeoutOrderDTO takeawayOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印标签单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        }
        return orderService.printLabel(takeawayOrderDTO);
    }
}
