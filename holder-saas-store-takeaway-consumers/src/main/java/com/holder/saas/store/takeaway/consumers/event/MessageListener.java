package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.service.rpc.BizMsgFeignClient;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import javafx.util.Pair;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024-08-07
 * @description
 */
@Component
@Slf4j
@AllArgsConstructor
public class MessageListener {

    private final BizMsgFeignClient bizMsgFeignClient;

    @EventListener
    @Async
    public void handleMessage(MessageEvent messageEvent){
        try {
            Pair<UserContext, BusinessMessageDTO> businessMessage = messageEvent.getBusinessMessage();
            if(businessMessage == null){
                return;
            }
            log.info("推送消息内容：{}",businessMessage);
            UserContextUtils.put(businessMessage.getKey());
            bizMsgFeignClient.msg(businessMessage.getValue());
        }catch (Exception e){
            log.error("消息处理失败",e);
        }
    }
}
