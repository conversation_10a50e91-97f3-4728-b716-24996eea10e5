package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hsg_groupbuy_order")
public class GroupBuyDO {

    private static final long serialVersionUID = -5321863789854649881L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * oder主键
     */
    private String orderGuid;

    private Integer orderStatus;

    private Integer orderTagStatus;

    /**
     * 订单类型,0=外卖订单  1=微信订单  2=其他订单
     */
    private Integer orderType;

    /**
     * 订单子类
     * OrderType=0：0=美团  1=饿了么  2=百度  3=京东
     * OrderType=1：0=扫码订单  1=微信预订单
     */
    private Integer orderSubType;

    /**
     * 商户名字
     */
    private String enterpriseName;

    /**
     * 门店名字
     */
    private String storeName;

    /**
     * 商户标识
     */
    private String enterpriseGuid;

    /**
     * 门店标识
     */
    private String storeGuid;

    /**
     * 外卖订单ID
     */
    private String orderId;

    /**
     * 外卖订单流水号
     */
    private String orderViewId;

    /**
     * 外卖订单日流水号
     */
    private String orderDaySn;

    /**
     * 外卖订单备注
     */
    private String orderRemark;

    /**
     * 是否是预定单：0=否，1=是
     */
    @TableField("is_reserve")
    private Boolean reserve;

    /**
     * 是否是催单：0=否，1=是
     */
    @TableField("is_reminded")
    private Boolean reminded;

    /**
     * 催单时间
     */
    private LocalDateTime remindTime;

    /**
     * 菜品数量
     * 菜品表数据冗余，避免列表查询时需join菜品表
     */
    private BigDecimal itemCount;

    /**
     * 顾客姓名
     */
    private String customerName;

    /**
     * 顾客手机号
     */
    private String customerPhone;

    /**
     * 顾客地址
     */
    private String customerAddress;

    /**
     * 用餐人数
     */
    private Integer customerNumber;

    /**
     * 首单用户：0=否，1=是
     */
    @TableField("is_first_order")
    private Boolean firstOrder;

    /**
     * 配送地址纬度
     */
    private String shipLatitude;

    /**
     * 配送地址经度
     */
    private String shipLongitude;

    /**
     * 配送员姓名
     */
    private String shipperName;

    /**
     * 配送员手机号
     */
    private String shipperPhone;

    /**
     * 是否三方托运：0=否，1=是
     */
    @TableField("is_third_shipper")
    private Boolean thirdShipper;

    /**
     * 是否有发票：0=无发票，1=有发票
     */
    @TableField("is_invoiced")
    private Boolean invoiced;

    /**
     * 发票类型：-1=未知，0=个人，1=企业
     */
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 纳税人身份证明
     */
    private String taxpayerId;

    /**
     * 总价,包括：菜品(原价)+餐盒+配送
     */
    private BigDecimal total;

    /**
     * 配送费
     */
    private BigDecimal shipTotal;

    /**
     * 菜品消费合计(不含餐盒费)
     */
    private BigDecimal itemTotal;

    /**
     * 餐盒费
     */
    private BigDecimal packageTotal;

    /**
     * 折扣合计 EnterpriseDiscount+PlatformDiscount+OtherDiscount
     */
    private BigDecimal discountTotal;

    /**
     * 商家承担的折扣部分
     */
    private BigDecimal enterpriseDiscount;

    /**
     * 外卖平台承担的折扣部分
     */
    private BigDecimal platformDiscount;

    /**
     * 其他折扣
     */
    private BigDecimal otherDiscount;

    /**
     * 服务费率(平台抽成比例)0.15=15%"
     */
    private BigDecimal serviceFeeRate;

    /**
     * 服务费(平台抽成费用)
     */
    private BigDecimal serviceFee;

    /**
     * 顾客实际支付金额
     */
    private BigDecimal customerActualPay;

    /**
     * 顾客退款菜品
     */
    private String customerRefundItem;

    /**
     * 顾客退款金额
     */
    private BigDecimal customerRefund;

    /**
     * 门店收款，顾客实际支付的金额扣除服务费(平台抽成)考虑扣除配送费?
     */
    private BigDecimal shopTotal;

    /**
     * 是否在线支付：0=线下付款，1=在线支付
     */
    @TableField("is_online_pay")
    private Boolean onlinePay;

    /**
     * 订单创建时间
     */
    private LocalDateTime createTime;

    /**
     * 订单激活时间：非预订单，激活时间与下单时间一致；预订单，激活时间指实际生效时间；
     */
    private LocalDateTime activeTime;

    /**
     * 接单时间
     */
    private LocalDateTime acceptTime;

    /**
     * 接单员工GUID
     */
    private String acceptStaffGuid;

    /**
     * 接单员工名字
     */
    private String acceptStaffName;

    /**
     * 接单设备id
     */
    private String acceptDeviceId;

    /**
     * 接单设备type
     */
    private Integer acceptDeviceType;

    /**
     * 期望送达时间：转时间戳后，若0=立即配送，若大于0=具体要求的送达时间
     */
    private LocalDateTime estimateDeliveredTime;

    /**
     * 配送时间（同取餐时间）
     */
    private LocalDateTime deliveryTime;

    /**
     * 实际送达时间
     */
    private LocalDateTime deliveredTime;

    /**
     * 取消订单请求时间
     */
    private LocalDateTime cancelReqTime;

    /**
     * 取消订单请求原因
     */
    private String cancelReqReason;

    /**
     * 取消订单请求超时时间
     */
    private LocalDateTime cancelReqExpireTime;

    /**
     * 取消订单请求回复时间
     */
    private LocalDateTime cancelReplyTime;

    /**
     * 取消订单请求回复消息体
     */
    private String cancelReplyMessage;

    /**
     * 取消订单请求回复员工GUID
     */
    private String cancelReplyStaffGuid;

    /**
     * 取消订单请求回复员工名字
     */
    private String cancelReplyStaffName;

    /**
     * 退单请求时间
     */
    private LocalDateTime refundReqTime;

    /**
     * 退单请求原因
     */
    private String refundReqReason;

    /**
     * 退单请求超时时间
     */
    private LocalDateTime refundReqExpireTime;

    /**
     * 退单请求回复时间
     */
    private LocalDateTime refundReplyTime;

    /**
     * 退单请求回复消息体
     */
    private String refundReplyMessage;

    /**
     * 退单请求回复员工GUID
     */
    private String refundReplyStaffGuid;

    /**
     * 退单请求回复员工名字
     */
    private String refundReplyStaffName;

    /**
     * 是否（部分）退款成功：0=失败，1=成功
     */
    @TableField("is_refund_success")
    private Boolean refundSuccess;

    /**
     * 订单取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 订单取消原因
     */
    private String cancelReason;

    /**
     * 取消订单员工GUID
     */
    private String cancelStaffGuid;

    /**
     * 取消订单员工名字
     */
    private String cancelStaffName;

    /**
     * 订单取消类型是否是拒单
     */
    @TableField("is_cancel_as_reject")
    private Boolean cancelAsReject;

    /**
     * 订单完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 营业日
     */
    private LocalDate businessDay;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

}
