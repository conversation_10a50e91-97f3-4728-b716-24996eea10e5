package com.holder.saas.store.takeaway.consumers.mapstruct;

import com.holder.saas.store.takeaway.consumers.entity.domain.*;
import com.holder.saas.store.takeaway.consumers.entity.query.HandoverPayQuery;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByTakeawayOrderRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface ItemPackageMapstruct {

    @Mappings({
            @Mapping(source = "parentGuid", target = "packageGuid"),
            @Mapping(source = "itemNum", target = "itemCount")
    })
    PackageDO parseToPackageDO(SkuInfoPkgDTO pkgDTO);

}
