package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单结算明细
 */
@Data
@TableName("hst_takeout_order_trade_detail")
public class OrderTradeDetailDO implements Serializable {

    private static final long serialVersionUID = 3176391123313483187L;

    /**
     * 订单id
     */
    @TableId(value = "order_id", type = IdType.INPUT)
    private String orderId;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单类型 0：美团 1：饿了么 6:赚餐
     */
    private Integer takeoutOrderType;

    /**
     * 品牌guid
     */
    private String brandGuid;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreateTime;

    /**
     * 订单完成时间
     */
    private LocalDateTime orderCompleteTime;

    /**
     * 活动成本明细(仅商家部分)
     * [{“chargeAmount(费用金额,保留2位小数)”：10.00,“chargeDesc(活动费用描述)”:""},{},…]
     */
    private String activityDetails;

    /**
     * 抽佣金额,保留2位小数
     */
    private String commisionAmount;

    /**
     * 商品金额=菜品原价＋餐盒费＋赠品原价（铺货模式赠品原值为0）,保留2为小数
     */
    private String foodAmount;

    /**
     * 美团应付给商家 (仅货到付款)
     */
    private String offlineOrderSkPayAmount;

    /**
     * 支付类型（1:货到付款；2:在线支付）
     */
    private Integer payType;

    /**
     * 结算金额(商家收入)
     */
    private String settleAmount;

    /**
     * 配送费,保留2位小数
     */
    private String shippingAmount;

    /**
     * 配送类型
     * <p>
     * 0000
     * <p>
     * 商家自配送
     * <p>
     * 0002
     * <p>
     * 趣活
     * <p>
     * 0016
     * <p>
     * 达达
     * <p>
     * 0033
     * <p>
     * E代送
     * <p>
     * 1001
     * <p>
     * 美团专送-加盟
     * <p>
     * 1002
     * <p>
     * 美团专送-自建
     * <p>
     * 1003
     * <p>
     * 美团配送-众包
     * <p>
     * 1004
     * <p>
     * 美团专送-城市代理
     * <p>
     * 2001
     * <p>
     * 角马
     * <p>
     * 2002
     * <p>
     * 快送
     * <p>
     * 2010
     * <p>
     * 全城送
     * <p>
     * 3001
     * <p>
     * 混合送，即美团专送+美团快送，使用方式相同
     */
    private String shippingType;

    /**
     * 总活动款,保留2位小数
     */
    private String totalActivityAmount;

    /**
     * 客户线下支付给商家 (仅货到付款)
     */
    private String userPayAmount;

    /**
     * 订单状态
     * 1 用户已提交订单,2 可推送到APP方平台也可推送到商家,3 商家已收到,4 商家已确认,8 已完成,9 已取消
     */
    private Integer status;

    /**
     * 津贴联盟信息
     * {"totalFee(活动总金额，单位为元)": 10.00,"type(活动类型，305代表津贴联盟)": 305,"poiFee(商家承担成本，单位为元)": 5.00,"wmChargeFee(平台承担成本，单位为元)": 5.00,"amountCent(津贴联盟平台扣减费用，单位为分，allowance为空，代表该订单非津贴联盟订单)": 1000}
     */
    private String allowance;

    /**
     * 活动与对应活动的费用组合
     * [{amountCent(津贴联盟平台扣减费用/CPS费用转入广告红包账户):112,type(18003代表津贴联盟;18004代表交易额转推广费活动):18003},{}...]
     */
    private String extendsAmount;

    /**
     * 打包袋价格
     */
    private BigDecimal packageBagMoney;

    /**
     * 订单业务打标枚举，16-拼好饭场景订单，即业务身份
     */
    private String orderTagList;

    /**
     * 顾客单买单送费
     */
    private BigDecimal singleIncreaseAmount;

    /**
     * 顾客实际支付
     */
    private BigDecimal userPayTotalAmount;

    /**
     * 顾客支付给商家
     */
    private BigDecimal phfPayTotalAmountForPoi;

    /**
     * 平台活动补贴(含合作商、第三方)
     */
    private BigDecimal platformPayForPoiAmount;

    /**
     * 商家费用合计=技术服务费+配送服务费（均摊）-配送服务费补贴（均摊）- 技术服务费补贴
     */
    private BigDecimal totalMerchantFees;

}