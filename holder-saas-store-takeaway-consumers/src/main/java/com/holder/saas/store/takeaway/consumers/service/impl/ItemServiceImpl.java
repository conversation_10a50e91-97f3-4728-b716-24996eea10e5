package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.helper.PageAdapter;
import com.holder.saas.store.takeaway.consumers.mapper.ItemMapper;
import com.holder.saas.store.takeaway.consumers.mapstruct.ItemMapstruct;
import com.holder.saas.store.takeaway.consumers.service.ItemService;
import com.holder.saas.store.takeaway.consumers.service.rpc.OrganizationService;
import com.holder.saas.store.takeaway.consumers.service.rpc.StaffFeignClient;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderItemProblemDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderItemRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 外卖菜品服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ItemServiceImpl extends ServiceImpl<ItemMapper, ItemDO> implements ItemService {

    private final ItemMapstruct itemMapstruct;

    private final ItemMapper itemMapper;

    private final OrganizationService organizationService;

    private final StaffFeignClient staffFeignClient;


    @Override
    public List<ItemDO> listByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getOrderGuid, orderGuid));
    }

    @Override
    public List<ItemDO> listByItemGuids(List<String> itemGuids) {
        if (CollectionUtils.isEmpty(itemGuids)) {
            return Lists.newArrayList();
        }
        QueryWrapper<ItemDO> qw = new QueryWrapper<>();
        qw.lambda().in(ItemDO::getItemGuid, itemGuids);
        return list(qw);
    }

    @Override
    public List<BusinessTakeoutOrderItemRespDTO> getBusinessTakeoutOrderItemList(String orderGuid) {
        List<ItemDO> itemDoList = getItemList(orderGuid);
        if (CollectionUtils.isEmpty(itemDoList)) {
            return Collections.emptyList();
        }
        return itemMapstruct.doList2BusinessTakeoutOrderItemRespDtoList(itemDoList);
    }

    @Override
    public List<BusinessTakeoutOrderItemRespDTO> getBusinessTakeoutOrderItemList(List<ItemDO> itemDoList) {
        return itemMapstruct.doList2BusinessTakeoutOrderItemRespDtoList(itemDoList);
    }

    @Override
    public List<ItemDO> getItemList(String orderGuid) {
        return listByOrderGuid(orderGuid);
    }

    @Override
    public void updateBatchIsAdjustItem(List<String> orderItemGuidList) {
        if (CollectionUtils.isEmpty(orderItemGuidList)) {
            return;
        }
        UpdateWrapper<ItemDO> uw = new UpdateWrapper<>();
        uw.lambda().set(ItemDO::getIsAdjustItem, true);
        uw.lambda().in(ItemDO::getItemGuid, orderItemGuidList);
        update(uw);
    }

    @Override
    public void updateBatchItem(List<TakeoutOrderItemProblemDTO> problemDTOList) {
        if (CollectionUtils.isEmpty(problemDTOList)) {
            return;
        }
        List<ItemDO> itemDOS = itemMapstruct.problemDTOList2ItemDOList(problemDTOList);
        baseMapper.updateBatchItem(itemDOS);
    }

    @Override
    public void fixBatchItem(List<ItemDO> fixItemList) {
        baseMapper.fixBatchItem(fixItemList);
    }

    /**
     * 外卖异常数据列表
     *
     * @param reqDTO 外卖异常数据列表请求
     * @return 外卖异常数据列表
     */
    @Override
    public Page<TakeoutItemAbnormalDataRespDTO> pageAbnormalData(TakeoutItemAbnormalDataReqDTO reqDTO) {
        // 品牌过滤处理门店
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "，account：" +
                UserContextUtils.getUserAccount() + "，userName：" + UserContextUtils.getUserName() + "-查询员工授权门店耗时");
        List<String> staffStoreGuidList = staffFeignClient.queryAllStoreGuid();
        stopWatch.stop();
        log.info("员工授权门店-staffStoreGuidList={}", staffStoreGuidList);
        if (CollectionUtils.isEmpty(staffStoreGuidList)) {
            log.warn("权限下没有门店");
            return new PageAdapter<>(reqDTO);
        }
        if (!StringUtils.isEmpty(reqDTO.getBrandGuid())) {
            stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "，brandGuid：" +
                    reqDTO.getBrandGuid() + "-查询品牌下门店耗时");
            List<String> storeGuidList = organizationService.queryStoreGuidListByBrandGui(reqDTO.getBrandGuid());
            stopWatch.stop();
            // 个人权限过滤
            storeGuidList.removeIf(s -> !staffStoreGuidList.contains(s));
            if (CollectionUtils.isEmpty(storeGuidList)) {
                log.warn("该品牌下无授权门店");
                return new PageAdapter<>(reqDTO);
            }
            if (CollectionUtils.isEmpty(reqDTO.getStoreGuidList())) {
                reqDTO.setStoreGuidList(storeGuidList);
            } else {
                reqDTO.getStoreGuidList().removeIf(s -> !storeGuidList.contains(s));
                if (CollectionUtils.isEmpty(reqDTO.getStoreGuidList())) {
                    log.warn("品牌下无此门店");
                    return new PageAdapter<>(reqDTO);
                }
            }
        }
        // 个人权限处理
        if (StringUtils.isEmpty(reqDTO.getBrandGuid()) && CollectionUtils.isEmpty(reqDTO.getStoreGuidList())) {
            reqDTO.setStoreGuidList(staffStoreGuidList);
        }

        stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "-查询异常数据sql耗时");
        IPage<TakeoutItemAbnormalDataRespDTO> abnormalDataIPage = itemMapper.pageAbnormalData(new PageAdapter<>(reqDTO), reqDTO);
        stopWatch.stop();
        log.warn(stopWatch.prettyPrint());
        List<TakeoutItemAbnormalDataRespDTO> records = abnormalDataIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageAdapter<>(abnormalDataIPage, Lists.newArrayList());
        }
        return new PageAdapter<>(abnormalDataIPage, records);
    }

    /**
     * 数据修复列表
     *
     * @param reqDTO 数据修复列表请求
     * @return 数据修复列表
     */
    @Override
    public List<TakeoutItemDataFixRespDTO> listDataFix(TakeoutItemDataFixReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getTakeoutAndStore())) {
            throw new BusinessException("外卖信息为空");
        }
        List<String> spliceList = reqDTO.getTakeoutAndStore().stream()
                .map(t -> t.getTakeoutItemNumber() + "-" + t.getStoreGuid())
                .collect(Collectors.toList());
        reqDTO.setSpliceList(spliceList);
        return itemMapper.listDataFix(reqDTO);
    }

    @Override
    public List<ItemDO> selectUnBindList(String storeGuid, String thirdSkuId) {
        LambdaQueryWrapper<ItemDO> queryWrapper = new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getStoreGuid, storeGuid)
                .eq(ItemDO::getThirdSkuId, thirdSkuId)
                .isNull(ItemDO::getErpItemName);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void clearErpItemSkuGuid(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        baseMapper.clearErpItemSkuGuid(ids);
    }

    @Override
    public List<ItemDO> filterNoRequiredFixList(List<Long> ids) {
        return baseMapper.filterNoRequiredFixList(ids);
    }

    @Override
    public void updateBatchRefundCount(List<ItemDO> itemList) {
        baseMapper.updateBatchRefundCount(itemList);
    }

    @Override
    public void updateRefundCountByOrderGuid(String orderGuid) {
        baseMapper.updateRefundCountByOrderGuid(orderGuid);
    }
}
