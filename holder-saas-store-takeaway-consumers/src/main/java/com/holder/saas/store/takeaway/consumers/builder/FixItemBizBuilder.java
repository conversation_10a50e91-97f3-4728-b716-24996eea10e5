package com.holder.saas.store.takeaway.consumers.builder;

import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.consumers.entity.bo.FixItemBiz;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixRecordDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.service.rpc.ItemFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.SpringContextUtils;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixItemDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixRecordDTO;
import com.holderzone.saas.store.dto.takeaway.UnItemBindUnbindReq;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class FixItemBizBuilder {

    private FixItemBizBuilder() {

    }

    public static FixItemBiz build() {
        FixItemBiz biz = new FixItemBiz();
        // 默认值：立即修复
        biz.setCommitFlag(false);
        // 默认值：不修复绑定关系
        biz.setFixBindFlag(false);
        biz.setRecord(new FixRecordDO());
        biz.setItemList(Lists.newArrayList());
        return biz;
    }

    public static FixItemBiz build(TakeoutFixDTO takeoutFixDTO) {
        FixItemBiz biz = build();

        biz.setCommitFlag(takeoutFixDTO.getCommitFlag());
        biz.setFixBindFlag(takeoutFixDTO.getFixBindFlag());

        TakeoutFixRecordDTO recordReqDTO = takeoutFixDTO.getRecordReqDTO();
        BeanUtils.copyProperties(recordReqDTO, biz.getRecord());
        biz.getRecord().setId(recordReqDTO.getRecordId());

        List<TakeoutFixItemDTO> itemReqDTOList = takeoutFixDTO.getItemReqDTOList();
        itemReqDTOList.forEach(e -> {
            FixItemDO fixItemDO = new FixItemDO();
            BeanUtils.copyProperties(e, fixItemDO);
            biz.getItemList().add(fixItemDO);
        });


        // 补全套餐信息
        List<String> erpItemGuids = itemReqDTOList.stream().map(TakeoutFixItemDTO::getErpItemGuid).distinct().collect(Collectors.toList());
        List<SkuInfoPkgDTO> pkgList = SpringContextUtils.getBean(ItemFeignClient.class).listPkgInfoByItemGuid(erpItemGuids);
        if (CollectionUtils.isNotEmpty(pkgList)) {
            Map<String, List<SkuInfoPkgDTO>> pkgMap = pkgList.stream().collect(Collectors.groupingBy(SkuInfoPkgDTO::getParentGuid));
            biz.getItemList().forEach(e -> {
                List<SkuInfoPkgDTO> skuPkgList = pkgMap.get(e.getErpItemGuid());
                e.setListPkg(skuPkgList);
            });
        }
        return biz;
    }


    /**
     * 外卖绑定关系 异步修复数据
     * 直接构造biz
     *
     * @param unItemBindUnbindReq 外卖绑定关系
     * @param itemDOList          需要修复的数据
     * @param skuInfoRespDTO      门店商品信息
     * @return
     */
    public static FixItemBiz build(UnItemBindUnbindReq unItemBindUnbindReq, List<ItemDO> itemDOList, SkuInfoRespDTO skuInfoRespDTO) {
        FixItemBiz biz = build();

        // 立即修复
        biz.setCommitFlag(false);
        // 不设置修复绑定关系
        biz.setFixBindFlag(false);

        // 修复记录
        FixRecordDO fixRecordDO = biz.getRecord();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 只能修复2022年开始的数据
        fixRecordDO.setStartTime(LocalDateTime.parse("2022-01-01 00:00:00", df));
        fixRecordDO.setEndTime(LocalDateTime.now());
        fixRecordDO.setFixCount(itemDOList.size());
        fixRecordDO.setStatus(1);
        // 修复明细
        ItemDO itemDb = itemDOList.get(itemDOList.size() - 1);

        FixItemDO fixItem = new FixItemDO();
        fixItem.setStoreGuid(unItemBindUnbindReq.getStoreGuid());
        fixItem.setStoreName(itemDb.getStoreName());
        fixItem.setOrderSubType(unItemBindUnbindReq.getTakeoutType());
        fixItem.setTakeoutItemName(itemDb.getItemName());
        fixItem.setThirdSkuId(itemDb.getThirdSkuId());
        fixItem.setErpItemGuid(unItemBindUnbindReq.getErpItemGuid());
        fixItem.setErpItemSkuGuid(unItemBindUnbindReq.getErpItemSkuId());

        String erpItemName = skuInfoRespDTO.getItemName();
        if (!StringUtils.isEmpty(skuInfoRespDTO.getName())) {
            erpItemName = erpItemName + "(" + skuInfoRespDTO.getName() + ")";
        }
        fixItem.setErpItemName(erpItemName);
        fixItem.setErpItemPrice(skuInfoRespDTO.getSalePrice());
        fixItem.setTakeawayAccountingPrice(skuInfoRespDTO.getTakeawayAccountingPrice());
        fixItem.setErpItemCount(BigDecimal.ONE);
        if (Objects.nonNull(unItemBindUnbindReq.getUnItemCountMapper()) && unItemBindUnbindReq.getUnItemCountMapper() > 1) {
            fixItem.setErpItemCount(BigDecimal.valueOf(unItemBindUnbindReq.getUnItemCountMapper()));
        }
        fixItem.setTakeoutOrderCount(itemDOList.size());
        fixItem.setListPkg(skuInfoRespDTO.getListPkg());
        biz.getItemList().add(fixItem);
        return biz;
    }

}
