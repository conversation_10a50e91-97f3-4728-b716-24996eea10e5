package com.holder.saas.store.takeaway.consumers.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.entity.domain.ConfigDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.mapstruct.OrderMapstruct;
import com.holder.saas.store.takeaway.consumers.service.*;
import com.holder.saas.store.takeaway.consumers.service.rpc.*;
import com.holder.saas.store.takeaway.consumers.utils.OrderContextUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.constant.RedisKeyConstant;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.content.PrintLabelDTO;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintTakeoutDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.TakeoutFormatDTO;
import com.holderzone.saas.store.dto.takeaway.OrderStatus;
import com.holderzone.saas.store.dto.takeaway.UnDiscount;
import com.holderzone.saas.store.dto.takeaway.UnItem;
import com.holderzone.saas.store.enums.MchntTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PrintServiceImpl implements PrintService {

    private final TakeoutPrintFactory takeoutPrintFactory;

    private final TakeoutMsgFactory takeoutMsgFactory;

    private final ItemFeignClient itemFeignClient;

    private final PrintFeignClient printFeignClient;

    private final BizMsgFeignClient bizMsgFeignClient;

    private final OrgFeignClient orgFeignClient;

    private final OrderMapstruct orderMapstruct;

    private final StaffFeignClient staffFeignClient;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final ItemClientService itemClientService;

    private final RedissonClient redissonSingleClient;

    private final ConfigService configService;

    private static final String PRINT_ORDER = "PRINT_ORDER:";

    @Override
    public void printAll(OrderReadDO orderReadDO,
                         int businessType,
                         Map<String, SkuTakeawayInfoRespDTO> mapOfSkuPartDTO,
                         List<UnItem> unItemList) {
        log.info("[printAll]orderReadDO:{}", JacksonUtils.writeValueAsString(orderReadDO));
        String orderContext = OrderContextUtils.getOrderContext();
        if (StringUtils.isEmpty(orderReadDO.getAcceptStaffGuid())) {
            log.error("{}打印失败：{}", orderContext, orderReadDO.getAcceptStaffName() + "接单");
            return;
        }
        if (RedissonLockUtil.isLocked(PRINT_ORDER + orderReadDO.getOrderId())) {
            log.error("{}打印失败,重复打印", orderContext);
            return;
        }
        RedissonLockUtil.lock(PRINT_ORDER + orderReadDO.getOrderId(), TimeUnit.HOURS, 4);
        StoreDTO store = getStore(orderReadDO.getStoreGuid());
        String tel = null;
        if (ObjectUtil.isNotNull(store)) {
            orderReadDO.setStoreName(store.getName());
            tel = store.getContactTel();
        }
        // 如果是零售，则直接返回
        String mchntType = staffFeignClient.queryMchntType();
        log.info("storeName={}，mchntType={}", orderReadDO.getStoreName(), mchntType);
        PrintTakeoutDTO billTakeawayDto = takeoutPrintFactory.createBillTakeawayDto(orderReadDO, false);
        log.info("[异常排查][补全子菜]billTakeawayDto={}", JacksonUtils.writeValueAsString(billTakeawayDto));
        // 补充商品分类 (映射的商品分类)
        takeoutPrintFactory.fillTakeoutItemTypeName(billTakeawayDto, mapOfSkuPartDTO);
        if (MchntTypeEnum.RETAIL.getCode().equalsIgnoreCase(mchntType)) {
            billTakeawayDto.setInvoiceType(InvoiceTypeEnum.RETAIL_TAKEOUT.getType());
            doPrintAsync(billTakeawayDto);
            log.info("{}[RETAIL]打印成功", orderContext);
            return;
        }
        billTakeawayDto.setInvoiceType(InvoiceTypeEnum.TAKEOUT.getType());
        doPrintAsync(billTakeawayDto);
        OrderReadDO degradeOrder = degradeOrderReadDO(orderReadDO, mapOfSkuPartDTO);
        if (degradeOrder != null) {
            bizMsgFeignClient.msg(takeoutMsgFactory.createOrderMappingFailed(orderReadDO));
            PrintTakeoutDTO takeawayDto = takeoutPrintFactory.createBillTakeawayDto(degradeOrder, true);
            doPrintAsync(takeawayDto);
            log.info("[abnormal]打印成功");
        }
        Map<String, ItemInfoRespDTO> pkgItemInfoMap = getPkgItemInfoMap(mapOfSkuPartDTO);
        PrintOrderItemDTO kitchenOrderDTO = takeoutPrintFactory.createKitchenOrderDTO(orderReadDO, mapOfSkuPartDTO, pkgItemInfoMap);
        if (!CollectionUtils.isEmpty(kitchenOrderDTO.getItemRecordList())) {
            // 存一份到redis，出餐的时候使用
            TakeoutFormatDTO formatDTO = printFeignClient.queryTakeout(orderReadDO.getStoreGuid());
            if (Objects.nonNull(formatDTO) && Objects.nonNull(formatDTO.getFoodFinishBarCode())
                    && formatDTO.getFoodFinishBarCode().isEnable()) {
                saveTakeoutItemToRedis(orderReadDO, kitchenOrderDTO.getItemRecordList());
            }
            //设置外卖订单拼好饭标识
            kitchenOrderDTO.setTakeoutBusinessType(businessType);
            doPrintAsync(kitchenOrderDTO);
            log.info("[kitchenOrder]打印成功");
        }
        PrintLabelDTO labelPrintLabelDTO = takeoutPrintFactory.createLabelLabelDTO(orderReadDO, mapOfSkuPartDTO);
        if (!CollectionUtils.isEmpty(labelPrintLabelDTO.getItemRecordList())) {
            doPrintAsync(labelPrintLabelDTO.setTel(tel));
            log.info("[Label]打印成功");
        }
        log.info("{}打印成功", orderContext);
    }


    private void saveTakeoutItemToRedis(OrderReadDO orderReadDO, List<PrintItemRecord> itemRecordList) {
        String takeoutItemKey = RedisKeyConstant.TAKEOUT_ITEM + orderReadDO.getStoreGuid() + ":" + orderReadDO.getOrderViewId();
        RBucket<String> takeoutItemBucket = redissonSingleClient.getBucket(takeoutItemKey, new StringCodec(StandardCharsets.UTF_8));
        List<String> orderItemGuidList = itemRecordList.stream()
                .map(PrintItemRecord::getOrderItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> subOrderItemGuidList = itemRecordList.stream()
                .filter(itemRecord -> !CollectionUtils.isEmpty(itemRecord.getSubItemRecords()))
                .flatMap(itemRecord -> itemRecord.getSubItemRecords().stream())
                .map(PrintItemRecord::getOrderItemGuid)
                .distinct()
                .collect(Collectors.toList());
        orderItemGuidList.addAll(subOrderItemGuidList);
        // 这里先设置为3天，有打印失败后重新打印的情况，此时如果相隔超过两天，则打印的单子会无法扫码出餐
        takeoutItemBucket.set(JacksonUtils.writeValueAsString(orderItemGuidList), 3, TimeUnit.DAYS);
    }

    @Override
    public String printBill(OrderReadDO orderReadDO) {
        String mchntType = staffFeignClient.queryMchntType();
        StoreDTO store = getStore(orderReadDO.getStoreGuid());
        if (ObjectUtil.isNotNull(store)) {
            orderReadDO.setStoreName(store.getName());
        }
        List<ItemDO> itemDOList = orderReadDO.getArrayOfItem();
        ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(itemDOList.stream().map(ItemDO::getErpItemSkuGuid).collect(Collectors.toList()));
        List<SkuTakeawayInfoRespDTO> arrayOfSkuTakeawayInfoRespDTO = itemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO);
        Map<String, SkuTakeawayInfoRespDTO> mapOfSkuPartDTO = arrayOfSkuTakeawayInfoRespDTO.stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getSkuGuid, Function.identity()));
        Map<String, String> parentSkuGuidMapping = mapOfSkuPartDTO.values().stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getParentSkuGuid, SkuTakeawayInfoRespDTO::getSkuGuid));
        for (ItemDO t : itemDOList) {
            if (mapOfSkuPartDTO.containsKey(t.getErpItemSkuGuid())) {
                continue;
            }
            String skuGuid = parentSkuGuidMapping.getOrDefault(t.getErpItemSkuGuid(), t.getErpItemSkuGuid());
            t.setErpItemSkuGuid(skuGuid);
        }

        log.info("storeName={}，mchntType={}", orderReadDO.getStoreName(), mchntType);
        PrintTakeoutDTO billTakeawayDto = takeoutPrintFactory.createBillTakeawayDto(orderReadDO, false);
        // 补充商品分类 (映射的商品分类)
        takeoutPrintFactory.fillTakeoutItemTypeName(billTakeawayDto, mapOfSkuPartDTO);
        if (MchntTypeEnum.RETAIL.getCode().equalsIgnoreCase(mchntType)) {
            billTakeawayDto.setInvoiceType(InvoiceTypeEnum.RETAIL_TAKEOUT.getType());
        } else {
            billTakeawayDto.setInvoiceType(InvoiceTypeEnum.TAKEOUT.getType());
        }
        String result = doPrintAsync(billTakeawayDto);
        // 外卖异常单
        log.info("[异常排查]orderReadDO={}", JacksonUtils.writeValueAsString(orderReadDO));
        log.info("[异常排查]mapOfSkuPartDTO={}", JacksonUtils.writeValueAsString(mapOfSkuPartDTO));
        OrderReadDO degradeOrder = getOrderReadDO(orderReadDO, itemDOList, mapOfSkuPartDTO);
        if (!ObjectUtils.isEmpty(degradeOrder)) {
            bizMsgFeignClient.msg(takeoutMsgFactory.createOrderMappingFailed(orderReadDO));
            // 打印外卖单
            doPrintAsync(takeoutPrintFactory.createBillTakeawayDto(degradeOrder, true));
        }
        // 打印后厨点菜单
        Map<String, ItemInfoRespDTO> pkgItemInfoMap = getPkgItemInfoMap(mapOfSkuPartDTO);
        PrintOrderItemDTO kitchenOrderDTO = takeoutPrintFactory.createKitchenOrderDTO(orderReadDO, mapOfSkuPartDTO,
                pkgItemInfoMap, true);
        if (!CollectionUtils.isEmpty(kitchenOrderDTO.getItemRecordList())) {
            return doPrintAsync(kitchenOrderDTO);
        }
        return result;
    }

    @Override
    public void printCancelBill(OrderReadDO orderReadDO) {
        Integer orderStatus = orderReadDO.getOrderStatus();
        if (OrderStatus.CANCELED != orderStatus) {
            log.warn("当前订单状态不打取消订单, orderReadDO:{}", JacksonUtils.writeValueAsString(orderReadDO));
            return;
        }
        ConfigDO configDO = configService.selectByStoreGuid(orderReadDO.getStoreGuid());
        log.info("外卖配置:{}", configDO);
        if (Objects.isNull(configDO) || !Boolean.TRUE.equals(configDO.getPrintCancelOrder())) {
            return;
        }
        String acceptDeviceId = orderReadDO.getAcceptDeviceId();
        if (StringUtils.isEmpty(acceptDeviceId)) {
            log.warn("当前订单未接单或非一体机自动接单后取消不打取消订单, orderReadDO:{}", JacksonUtils.writeValueAsString(orderReadDO));
            return;
        }
        log.info("打印外卖取消订单小票:{}", JacksonUtils.writeValueAsString(orderReadDO));
        // 打印账单
        StoreDTO store = getStore(orderReadDO.getStoreGuid());
        if (Objects.nonNull(store)) {
            orderReadDO.setStoreName(store.getName());
        }
        List<ItemDO> itemDOList = orderReadDO.getArrayOfItem();
        ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(itemDOList.stream()
                .map(ItemDO::getErpItemSkuGuid)
                .collect(Collectors.toList()));
        List<SkuTakeawayInfoRespDTO> arrayOfSkuTakeawayInfoRespDTO = itemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO);
        Map<String, SkuTakeawayInfoRespDTO> mapOfSkuPartDTO = arrayOfSkuTakeawayInfoRespDTO.stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getSkuGuid, Function.identity()));
        PrintTakeoutDTO billTakeawayDto = takeoutPrintFactory.createBillTakeawayDto(orderReadDO, false);
        // 补充商品分类 (映射的商品分类)
        takeoutPrintFactory.fillTakeoutItemTypeName(billTakeawayDto, mapOfSkuPartDTO);
        billTakeawayDto.setInvoiceType(InvoiceTypeEnum.TAKEOUT.getType());
        doPrintAsync(billTakeawayDto);
    }

    private OrderReadDO getOrderReadDO(OrderReadDO orderReadDO,
                                       List<ItemDO> arrayOfItem,
                                       Map<String, SkuTakeawayInfoRespDTO> mapOfSkuPartDTO) {
        boolean allMatch = arrayOfItem.stream()
                .allMatch(itemDO -> check(mapOfSkuPartDTO, itemDO.getErpItemSkuGuid()));
        if (allMatch) {
            return null;
        }
        OrderReadDO degrade = orderMapstruct.readToRead(orderReadDO);
        degrade.getArrayOfItem().removeIf(itemDO -> check(mapOfSkuPartDTO, itemDO.getErpItemSkuGuid()));
        degrade.getArrayOfItem().forEach(item -> {
            SkuTakeawayInfoRespDTO sku = mapOfSkuPartDTO.get(item.getErpItemSkuGuid());
            if (sku == null) {
                item.setItemName(item.getItemName() + "(未关联)");
                return;
            }
            if (sku.getIsRack() != null && sku.getIsRack() == 0) {
                item.setItemName(item.getItemName() + "(已下架)");
            }
        });
        return degrade;
    }

    @Override
    public String printKitchen(OrderReadDO orderReadDO) {
        StoreDTO store = getStore(orderReadDO.getStoreGuid());
        if (ObjectUtil.isNotNull(store)) {
            orderReadDO.setStoreName(store.getName());
        }
        Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = mapOfSkuTakeawayInfoRespDTO(orderReadDO);
        OrderReadDO degradeOrder = degradeOrderReadDO(orderReadDO, mapOfSkuTakeawayInfoRespDTO);
        // 打印外卖单
        if (degradeOrder != null) {
            PrintTakeoutDTO billTakeawayDto = takeoutPrintFactory.createBillTakeawayDto(degradeOrder, true);
            billTakeawayDto.setInvoiceType(InvoiceTypeEnum.TAKEOUT.getType());
            doPrintAsync(billTakeawayDto);
        }
        // 打印后厨点菜单
        Map<String, ItemInfoRespDTO> pkgItemInfoMap = getPkgItemInfoMap(mapOfSkuTakeawayInfoRespDTO);
        PrintOrderItemDTO kitchenOrderDTO = takeoutPrintFactory.createKitchenOrderDTO(orderReadDO, mapOfSkuTakeawayInfoRespDTO, pkgItemInfoMap);
        if (!CollectionUtils.isEmpty(kitchenOrderDTO.getItemRecordList())) {
            return doPrintAsync(kitchenOrderDTO);
        }
        return "打印失败，菜品列表为空";
    }

    /**
     * 通过商品列表查询商品map
     * print
     *
     * @param mapOfSkuTakeawayInfoRespDTO skuMap
     * @return itemMap
     */
    private Map<String, ItemInfoRespDTO> getPkgItemInfoMap(Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        List<String> itemGuidList = mapOfSkuTakeawayInfoRespDTO.values().stream()
                .map(SkuTakeawayInfoRespDTO::getItemGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemGuidList)) {
            log.info("print getPkgItemInfoMap itemGuidList为空");
            return new HashMap<>();
        }
        ItemStringListDTO dto = new ItemStringListDTO();
        dto.setDataList(itemGuidList);
        List<ItemInfoRespDTO> pkgItemInfoList = itemClientService.listPkgItemInfo(dto);
        log.info("print pkgItemInfoList={}", JacksonUtils.writeValueAsString(pkgItemInfoList));
        return pkgItemInfoList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, p -> p));
    }

    @Override
    public String printLabel(OrderReadDO orderReadDO) {
        StoreDTO store = getStore(orderReadDO.getStoreGuid());
        String tel = null;
        if (ObjectUtil.isNotNull(store)) {
            orderReadDO.setStoreName(store.getName());
            tel = store.getContactTel();
        }
        Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = mapOfSkuTakeawayInfoRespDTO(orderReadDO);
        OrderReadDO degradeOrder = degradeOrderReadDO(orderReadDO, mapOfSkuTakeawayInfoRespDTO);
        if (degradeOrder != null) {
            doPrintAsync(takeoutPrintFactory.createBillTakeawayDto(degradeOrder, true));
        }
        PrintLabelDTO labelPrintLabelDTO = takeoutPrintFactory.createLabelLabelDTO(orderReadDO, mapOfSkuTakeawayInfoRespDTO);
        labelPrintLabelDTO.setTel(tel);
        labelPrintLabelDTO.setTradeMode(TradeModeEnum.TAKEOUT.getMode());
        if (!CollectionUtils.isEmpty(labelPrintLabelDTO.getItemRecordList())) {
            return doPrintAsync(labelPrintLabelDTO);
        }
        return "打印失败，菜品列表为空";
    }

    private Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO(OrderReadDO orderReadDO) {
        List<ItemDO> arrayOfItemDO = orderReadDO.getArrayOfItem();
        ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(arrayOfItemDO.stream().map(ItemDO::getItemSku).collect(Collectors.toList()));
        List<SkuTakeawayInfoRespDTO> arrayOfSkuTakeawayInfoRespDTO = itemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO);
        return arrayOfSkuTakeawayInfoRespDTO.stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getSkuGuid, Function.identity()));
    }

    private OrderReadDO degradeOrderReadDO(OrderReadDO orderReadDO,
                                           Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        List<ItemDO> arrayOfItem = orderReadDO.getArrayOfItem();

        boolean allMatch = arrayOfItem.stream()
                .allMatch(itemDO -> check(mapOfSkuTakeawayInfoRespDTO, itemDO.getItemSku()));
        if (allMatch) {
            return null;
        }
        OrderReadDO degradeOrder = orderMapstruct.readToRead(orderReadDO);
        degradeOrder.getArrayOfItem().removeIf(itemDO -> check(mapOfSkuTakeawayInfoRespDTO, itemDO.getItemSku()));
        degradeOrder.getArrayOfItem().forEach(itemDO -> {
            SkuTakeawayInfoRespDTO sku = mapOfSkuTakeawayInfoRespDTO.get(itemDO.getItemSku());
            if (sku == null) {
                itemDO.setItemName(itemDO.getItemName() + "(未关联)");
                return;
            }
            if (sku.getIsRack() != null && sku.getIsRack() == 0) {
                itemDO.setItemName(itemDO.getItemName() + "(已下架)");
            }
        });
        return degradeOrder;
    }

    private boolean check(Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO, String itemSku) {
        if (ObjectUtils.isEmpty(mapOfSkuTakeawayInfoRespDTO)) {
            return false;
        }
        SkuTakeawayInfoRespDTO s = mapOfSkuTakeawayInfoRespDTO.get(itemSku);
        if (s != null) {
            return s.getIsRack() != null && s.getIsRack() == 1;
        }
        return false;
    }

    private StoreDTO getStore(String storeGuid) {
        StoreDTO storeInfo = orgFeignClient.queryStoreByGuid(storeGuid);
        if (storeInfo == null) {
            log.error("根据storeGuid: {}未查找到门店", storeGuid);
            return null;
        }
        String storeTel = storeInfo.getContactTel();
        log.info("根据storeGuid: {}查找到门店tel: {}查找到门店名称: {}", storeGuid, storeTel, storeInfo.getName());
        return storeInfo;
    }

    private String doPrintAsync(Object object) {
        Message message = new Message(
                RocketMqConfig.PRINT_MESSAGE_TOPIC,
                RocketMqConfig.PRINT_MESSAGE_TAG,
                JacksonUtils.toJsonByte(object)
        );
        message.getProperties().put(
                RocketMqConfig.MESSAGE_CONTEXT,
                UserContextUtils.getJsonStr()
        );
        defaultRocketMqProducer.sendMessage(message);
        return "SUCCESS";
    }

}
