package com.holder.saas.store.takeaway.consumers.service;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubItemSkuWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubgroupWebRespDTO;
import com.holderzone.saas.store.dto.print.content.PrintLabelDTO;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintTakeoutDTO;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.dto.takeaway.OrderStatus;
import com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType;
import com.holderzone.saas.store.dto.takeaway.UnItem;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TakeoutPrintFactory {

    /**
     * 创建外卖单 打印DTO
     */
    public PrintTakeoutDTO createBillTakeawayDto(OrderReadDO orderReadDO, boolean abnormal) {
        PrintTakeoutDTO takeoutDto = new PrintTakeoutDTO();
        if (orderReadDO.getInvoiced() == null) {
            takeoutDto.setInvoiced(Boolean.FALSE);
        } else {
            takeoutDto.setInvoiced(orderReadDO.getInvoiced());
        }
        takeoutDto.setCancelFlag(OrderStatus.CANCELED == orderReadDO.getOrderStatus());
        takeoutDto.setDinnersNumber(null);
        takeoutDto.setFullGiftRemark(orderReadDO.getFullGiftRemark());
        takeoutDto.setInvoiceTitle(orderReadDO.getInvoiceTitle());
        takeoutDto.setTaxpayerId(orderReadDO.getTaxpayerId());
        takeoutDto.setInvoiceType(InvoiceTypeEnum.TAKEOUT.getType());
        takeoutDto.setEnterpriseGuid(orderReadDO.getEnterpriseGuid());
        takeoutDto.setStoreGuid(orderReadDO.getStoreGuid());
        takeoutDto.setStoreName(orderReadDO.getStoreName());
        takeoutDto.setOperatorStaffGuid(orderReadDO.getAcceptStaffGuid());
        takeoutDto.setOperatorStaffName(orderReadDO.getAcceptStaffName());
        takeoutDto.setCreateTime(DateTimeUtils.nowMillis());
        takeoutDto.setPlatform(TakeoutSubType.ofType(orderReadDO.getOrderSubType()).getDesc());
        takeoutDto.setDeviceId(orderReadDO.getAcceptDeviceId());
        if(orderReadDO.getAcceptDeviceType() != null){
            takeoutDto.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(orderReadDO.getAcceptDeviceType()));
        }
        takeoutDto.setPrintUid(orderReadDO.getOrderViewId());
        takeoutDto.setOrderNo(orderReadDO.getOrderViewId());
        takeoutDto.setReserve(orderReadDO.getReserve());
        takeoutDto.setAbnormal(abnormal);
        takeoutDto.setIsAutoAccept(orderReadDO.getIsAutoAccept());

        // 优惠
        List<ReduceRecord> arrayOfReduceRecord = new ArrayList<>();
        if (orderReadDO.getPlatformDiscount().compareTo(BigDecimal.ZERO) > 0) {
            String reduceName = TakeoutSubType.ofType(orderReadDO.getOrderSubType()).getDesc() + "优惠";
            arrayOfReduceRecord.add(new ReduceRecord(reduceName, orderReadDO.getPlatformDiscount().negate()));
        }
        if (orderReadDO.getEnterpriseDiscount().compareTo(BigDecimal.ZERO) > 0) {
            arrayOfReduceRecord.add(new ReduceRecord("优惠", orderReadDO.getEnterpriseDiscount().negate()));
        }
        if (orderReadDO.getOrderSubType() == 6 && orderReadDO.getOtherDiscount().compareTo(BigDecimal.ZERO) > 0) {
            arrayOfReduceRecord.add(new ReduceRecord("会员优惠", orderReadDO.getOtherDiscount().negate()));
        }
        takeoutDto.setReduceRecordList(arrayOfReduceRecord);

        takeoutDto.setPlatformOrder("#" + orderReadDO.getOrderDaySn());
        takeoutDto.setPayMsg(Boolean.FALSE.equals(orderReadDO.getOnlinePay()) ? "货到付款" : "已在线支付");
        LocalDateTime estimateDeliveredTime = orderReadDO.getEstimateDeliveredTime();
        String formatMillsStr = (estimateDeliveredTime == null || 0 == DateTimeUtils.localDateTime2Mills
                (estimateDeliveredTime))
                ? "立即送达" : DateTimeUtils.localDateTime2String(estimateDeliveredTime, "yyyy-MM-dd HH:mm");
        takeoutDto.setExpectTime(formatMillsStr);
        takeoutDto.setOrderTime(DateTimeUtils.localDateTime2Mills(orderReadDO.getCreateTime()));
        takeoutDto.setRemark(orderReadDO.getOrderRemark());
        takeoutDto.setItemTotalPrice(orderReadDO.getItemTotal());

        // 附加费
        List<AdditionalCharge> arrayOfAdditionalCharge = new ArrayList<>();
        // 附加费-餐盒费
        AdditionalCharge packageCharge = new AdditionalCharge();
        packageCharge.setChargeName("餐盒费");
        packageCharge.setChargeValue(orderReadDO.getPackageTotal());
        arrayOfAdditionalCharge.add(packageCharge);
        // 附加费-配送费
        AdditionalCharge shippingCharge = new AdditionalCharge();
        shippingCharge.setChargeName("配送费");
        shippingCharge.setChargeValue(orderReadDO.getShipTotal());
        arrayOfAdditionalCharge.add(shippingCharge);
        // 附加费设置
        takeoutDto.setAdditionalChargeList(arrayOfAdditionalCharge);

        takeoutDto.setOriginalPrice(orderReadDO.getTotal());
        takeoutDto.setActuallyPay(orderReadDO.getCustomerActualPay());
        takeoutDto.setReceiverName(StringEscapeUtils.unescapeHtml(orderReadDO.getCustomerName()));
        takeoutDto.setReceiverAddress(orderReadDO.getCustomerAddress());
        takeoutDto.setRecipientAddressDesensitization(orderReadDO.getRecipientAddressDesensitization());
        //真实号
        takeoutDto.setReceiverTel(orderReadDO.getCustomerPhone());
        //隐私号
        log.info("订单[{}]真实号:{},隐私号:{}", orderReadDO.getOrderId(),
                orderReadDO.getCustomerPhone(), orderReadDO.getPrivacyPhone());
        takeoutDto.setPrivacyPhone(orderReadDO.getPrivacyPhone());
        // 商品明细
        takeoutDto.setItemRecordList(buildPrintItemRecordList(orderReadDO));
        return takeoutDto;
    }

    private List<PrintItemRecord> buildPrintItemRecordList(OrderReadDO orderReadDO) {
        List<PrintItemRecord> arrayOfPrintItemRecord = new ArrayList<>();
        int sort = 0;
        for (ItemDO itemDO : orderReadDO.getArrayOfItem()) {
            PrintItemRecord printItemRecord = new PrintItemRecord();
            // 产品要求先使用外卖平台的itemName，insert时已带规格
            printItemRecord.setItemName(itemDO.getItemName());
            printItemRecord.setUnItemSku(itemDO.getItemCode());
            // todo 结帐单无需itemTypeGuid，让超哥放开校验
            // todo 超哥偷懒，让我直接传无意义的字符串，通过校验
            printItemRecord.setItemGuid("-1");
            if (!StringUtils.isEmpty(itemDO.getErpItemSkuGuid())) {
                // 设置为商品映射
                printItemRecord.setSkuGuid(itemDO.getErpItemSkuGuid());
            }
            printItemRecord.setOrderItemGuid(itemDO.getOrderGuid() + "," + itemDO.getThirdSkuId() + "," + itemDO.getItemProperty());
            printItemRecord.setItemTypeGuid("-1");
            printItemRecord.setPrice(itemDO.getItemPrice());
            printItemRecord.setNumber(itemDO.getItemCount());
            printItemRecord.setSubtotal(itemDO.getItemTotal());
            printItemRecord.setProperty(itemDO.getItemProperty());
            printItemRecord.setUnit(itemDO.getItemUnit());
            printItemRecord.setAsGift(false);
            printItemRecord.setAsWeight(false);
            printItemRecord.setAsPackage(false);
            printItemRecord.setSort(sort++);

            printItemRecord.setCartId(itemDO.getCartId());

            if (StringUtils.hasText(itemDO.getSubItemInfo())) {
                List<PrintItemRecord> subItemRecords = new ArrayList<>();
                List<UnItem> subItemList = JacksonUtils.toObjectList(UnItem.class, itemDO.getSubItemInfo());
                subItemList.forEach(subItemRecord -> {
                    PrintItemRecord printSubItemRecord = new PrintItemRecord();
                    printSubItemRecord.setItemName(subItemRecord.getItemName());
                    printSubItemRecord.setNumber(subItemRecord.getItemCount());
                    printSubItemRecord.setItemGuid("-1");
                    printSubItemRecord.setAsPackage(Boolean.FALSE);
                    printSubItemRecord.setAsWeight(Boolean.FALSE);
                    printSubItemRecord.setAsGift(Boolean.FALSE);
                    printSubItemRecord.setItemTypeGuid("-1");
                    printSubItemRecord.setProperty(subItemRecord.getItemProperty());
                    subItemRecords.add(printSubItemRecord);
                });
                printItemRecord.setSubItemRecords(subItemRecords);
            }

            arrayOfPrintItemRecord.add(printItemRecord);
        }
        // 合并商品
        return mergePrintItemRecord(arrayOfPrintItemRecord, false);
    }

    /**
     * 创建外卖点菜单 打印DTO
     */
    public PrintOrderItemDTO createKitchenOrderDTO(OrderReadDO orderReadDO,
                                                   Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO,
                                                   Map<String, ItemInfoRespDTO> pkgItemInfoMap){
        return createKitchenOrderDTO(orderReadDO, mapOfSkuTakeawayInfoRespDTO, pkgItemInfoMap, false);
    }

    /**
     * 创建外卖点菜单 打印DTO
     */
    public PrintOrderItemDTO createKitchenOrderDTO(OrderReadDO orderReadDO,
                                                   Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO,
                                                   Map<String, ItemInfoRespDTO> pkgItemInfoMap, boolean isBill) {
        PrintOrderItemDTO printOrderItemDTODto = new PrintOrderItemDTO();
        printOrderItemDTODto.setInvoiceType(InvoiceTypeEnum.ORDER_ITEM.getType());
        printOrderItemDTODto.setPrintUid(orderReadDO.getOrderViewId());
        printOrderItemDTODto.setDeviceId(orderReadDO.getAcceptDeviceId());
        printOrderItemDTODto.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(orderReadDO.getAcceptDeviceType()));
        printOrderItemDTODto.setStoreGuid(orderReadDO.getStoreGuid());
        printOrderItemDTODto.setEnterpriseGuid(orderReadDO.getEnterpriseGuid());
        printOrderItemDTODto.setOperatorStaffGuid(orderReadDO.getAcceptStaffGuid());
        printOrderItemDTODto.setOperatorStaffName(orderReadDO.getAcceptStaffName());
        printOrderItemDTODto.setCreateTime(DateTimeUtils.nowMillis());
        printOrderItemDTODto.setMarkName(TakeoutSubType.ofType(orderReadDO.getOrderSubType()).getDesc());
        printOrderItemDTODto.setMarkNo("#" + orderReadDO.getOrderDaySn());
        printOrderItemDTODto.setOrderTime(DateTimeUtils.localDateTime2Mills(orderReadDO.getCreateTime()));
        printOrderItemDTODto.setOrderNo(orderReadDO.getOrderViewId());
        printOrderItemDTODto.setPersonNumber(orderReadDO.getCustomerNumber() != null ? orderReadDO.getCustomerNumber() : 1);
        printOrderItemDTODto.setRemark(orderReadDO.getOrderRemark());
        printOrderItemDTODto.setIsAutoAccept(orderReadDO.getIsAutoAccept());
        LocalDateTime estimateDeliveredTime = orderReadDO.getEstimateDeliveredTime();
        String formatMillsStr = (estimateDeliveredTime == null || 0 == DateTimeUtils.localDateTime2Mills
                (estimateDeliveredTime))
                ? "立即送达" : DateTimeUtils.localDateTime2String(estimateDeliveredTime);
        printOrderItemDTODto.setEstimateDeliveredTimeString(formatMillsStr);
        printOrderItemDTODto.setTradeMode(TradeModeEnum.TAKEOUT.getMode());
        // 外卖名称
        printOrderItemDTODto.setOrderTypeName(printOrderItemDTODto.getMarkName());
        // 构建商品列表
        List<PrintItemRecord> arrayOfPrintItemRecord = buildKitchenOrderPrintItemRecord(orderReadDO,
                mapOfSkuTakeawayInfoRespDTO, pkgItemInfoMap, isBill);
        // 合并商品
        List<PrintItemRecord> mergePrintItemRecords = mergePrintItemRecord(arrayOfPrintItemRecord, true);
        printOrderItemDTODto.setItemRecordList(mergePrintItemRecords);
        log.info("后厨点菜单 printOrderItemDTODto: {}", JSONUtil.toJsonStr(printOrderItemDTODto));
        return printOrderItemDTODto;
    }


    /**
     * 合并商品
     */
    private List<PrintItemRecord> mergePrintItemRecord(List<PrintItemRecord> arrayOfPrintItemRecords, boolean merge) {
        if (CollectionUtils.isEmpty(arrayOfPrintItemRecords)) {
            return Lists.newArrayList();
        }
        if (merge) {
            List<PrintItemRecord> subItemRecordList = arrayOfPrintItemRecords.stream()
                    .filter(e -> !CollectionUtils.isEmpty(e.getSubItemRecords()))
                    .flatMap(e -> e.getSubItemRecords().stream())
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(subItemRecordList)) {
                arrayOfPrintItemRecords.removeIf(e -> !CollectionUtils.isEmpty(e.getSubItemRecords()));
                arrayOfPrintItemRecords.addAll(subItemRecordList);
            }
        }
        arrayOfPrintItemRecords.forEach(e -> {
            if (Objects.isNull(e.getCartId())) {
                e.setCartId(0);
            }
            e.setAsPackage(false);
        });
        // 根据 OrderItemGuid = orderReadDO.getOrderGuid() + "," + subItem.getSkuGuid() + "," +  合并
        Map<String, List<PrintItemRecord>> arrayOfPrintItemRecordMap = arrayOfPrintItemRecords.stream()
                .filter(e -> e.getOrderItemGuid() != null)
                .collect(Collectors.groupingBy(PrintItemRecord::getOrderItemGuid));
        List<PrintItemRecord> mergePrintItemRecords = Lists.newArrayList();
        for (Map.Entry<String, List<PrintItemRecord>> entry : arrayOfPrintItemRecordMap.entrySet()) {
            List<PrintItemRecord> innerRecordList = entry.getValue();
            BigDecimal totalNumber = innerRecordList.stream()
                    .map(PrintItemRecord::getNumber)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            PrintItemRecord copyPrintItemRecord = new PrintItemRecord();
            BeanUtils.copyProperties(innerRecordList.get(0), copyPrintItemRecord);
            copyPrintItemRecord.setNumber(totalNumber);
            mergePrintItemRecords.add(copyPrintItemRecord);
        }
        // 排序
        mergePrintItemRecords = mergePrintItemRecords.stream()
                .sorted(Comparator.comparing(PrintItemRecord::getSort))
                .collect(Collectors.toList());
        log.info("最终打印商品列表为:{}", JacksonUtils.writeValueAsString(mergePrintItemRecords));
        return mergePrintItemRecords;
    }

    public PrintLabelDTO createLabelLabelDTO(OrderReadDO orderReadDO,
                                             Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        PrintLabelDTO printLabelDTODto = new PrintLabelDTO();
        printLabelDTODto.setSerialNumber(TakeoutSubType.ofType(orderReadDO.getOrderSubType()).getSource()
                + "#" + orderReadDO.getOrderDaySn());
        printLabelDTODto.setRemark(orderReadDO.getOrderRemark());

        List<PrintItemRecord> arrayOfPrintItemRecord = new ArrayList<>();
        for (ItemDO itemDO : orderReadDO.getArrayOfItem()) {
            SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = mapOfSkuTakeawayInfoRespDTO.get(itemDO.getItemSku());
            if (skuTakeawayInfoRespDTO != null) {
                PrintItemRecord printItemRecord = new PrintItemRecord();
                String itemSpec = StringUtils.hasText(skuTakeawayInfoRespDTO.getSkuName())
                        ? "(" + skuTakeawayInfoRespDTO.getSkuName() + ")" : "";
                printItemRecord.setItemName(skuTakeawayInfoRespDTO.getItemName() + itemSpec);
                printItemRecord.setItemGuid(skuTakeawayInfoRespDTO.getItemGuid());
                printItemRecord.setItemTypeGuid(skuTakeawayInfoRespDTO.getTypeGuid());
                printItemRecord.setNumber(itemDO.getItemCount());
                printItemRecord.setProperty(itemDO.getItemProperty());
                printItemRecord.setUnit(itemDO.getItemUnit());
                printItemRecord.setPrice(Optional.ofNullable(itemDO.getItemPrice()).orElse(BigDecimal.ZERO));
                printItemRecord.setAsGift(false);
                printItemRecord.setAsWeight(false);
                printItemRecord.setAsPackage(false);
                log.info("<----------待打印菜品Guid[{}]---------->", printItemRecord.getItemGuid());
                arrayOfPrintItemRecord.add(printItemRecord);
            }
        }

        printLabelDTODto.setItemRecordList(arrayOfPrintItemRecord);
        printLabelDTODto.setInvoiceType(InvoiceTypeEnum.LABEL.getType());
        printLabelDTODto.setStoreName(orderReadDO.getStoreName());
        printLabelDTODto.setStoreGuid(orderReadDO.getStoreGuid());
        printLabelDTODto.setEnterpriseGuid(orderReadDO.getEnterpriseGuid());
        printLabelDTODto.setPrintUid(orderReadDO.getOrderViewId());
        printLabelDTODto.setOperatorStaffGuid(orderReadDO.getAcceptStaffGuid());
        printLabelDTODto.setOperatorStaffName(orderReadDO.getAcceptStaffName());
        printLabelDTODto.setCreateTime(DateTimeUtils.nowMillis());
        printLabelDTODto.setDeviceId(orderReadDO.getAcceptDeviceId());
        printLabelDTODto.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(orderReadDO.getAcceptDeviceType()));
        printLabelDTODto.setTradeMode(TradeModeEnum.TAKEOUT.getMode());
        return printLabelDTODto;
    }

    /**
     * 填充商品明细上的商品分类名称
     */
    public void fillTakeoutItemTypeName(PrintTakeoutDTO billTakeawayDTO, Map<String, SkuTakeawayInfoRespDTO> mapOfSkuPartMap) {
        List<PrintItemRecord> itemRecordList = billTakeawayDTO.getItemRecordList();
        if (CollectionUtils.isEmpty(itemRecordList)) {
            return;
        }
        itemRecordList.forEach(e -> {
            SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = mapOfSkuPartMap.get(e.getSkuGuid());
            if (Objects.nonNull(skuTakeawayInfoRespDTO)) {
                e.setItemGuid(skuTakeawayInfoRespDTO.getItemGuid());
                e.setItemTypeGuid(skuTakeawayInfoRespDTO.getTypeGuid());
                e.setItemTypeName(skuTakeawayInfoRespDTO.getTypeName());
            }
        });
        List<SkuTakeawayInfoRespDTO> mapOfParentSkuPartList = new ArrayList<>(mapOfSkuPartMap.values());
        Map<String, SkuTakeawayInfoRespDTO> mapOfParentSkuPartMap = mapOfParentSkuPartList.stream()
                .filter(e -> !StringUtils.isEmpty(e.getParentSkuGuid()))
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getParentSkuGuid, Function.identity(), (key1, key2) -> key1));
        itemRecordList.forEach(e -> {
            SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = mapOfParentSkuPartMap.get(e.getSkuGuid());
            if (Objects.nonNull(skuTakeawayInfoRespDTO)) {
                e.setItemGuid(skuTakeawayInfoRespDTO.getItemGuid());
                e.setItemTypeGuid(skuTakeawayInfoRespDTO.getTypeGuid());
                e.setItemTypeName(skuTakeawayInfoRespDTO.getTypeName());
            }
        });
    }


    /**
     * 构建外卖点菜单 商品列表
     */
    private List<PrintItemRecord> buildKitchenOrderPrintItemRecord(OrderReadDO orderReadDO,
                                                                   Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO,
                                                                   Map<String, ItemInfoRespDTO> pkgItemInfoMap,
                                                                   boolean isBill) {
        int sort = 0;
        List<PrintItemRecord> arrayOfPrintItemRecord = new ArrayList<>();
        for (ItemDO itemDO : orderReadDO.getArrayOfItem()) {
            // bill方法请求，使用erpItemSkuGuid匹配
            SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = isBill ?
                    mapOfSkuTakeawayInfoRespDTO.get(itemDO.getErpItemSkuGuid()) :
                    mapOfSkuTakeawayInfoRespDTO.get(itemDO.getItemSku());
            log.info("skuTakeawayInfoRespDTO={} ItemSku={}", JacksonUtils.writeValueAsString(skuTakeawayInfoRespDTO),
                    itemDO.getItemSku());
            if (Objects.isNull(skuTakeawayInfoRespDTO)) {
                continue;
            }
            PrintItemRecord printItemRecord = new PrintItemRecord();
            // 产品要求先使用外卖平台的itemName，insert时已带规格
            String itemSpec = StringUtils.hasText(skuTakeawayInfoRespDTO.getSkuName())
                    ? "(" + skuTakeawayInfoRespDTO.getSkuName() + ")" : "";
            log.info("itemSpec={}", itemSpec);
            log.info("skuTakeawayInfoRespDTO.getItemName()={}", skuTakeawayInfoRespDTO.getItemName());
            log.info("itemDO.getItemName()={}", itemDO.getItemName());
            printItemRecord.setItemName(skuTakeawayInfoRespDTO.getItemName() + itemSpec);
            printItemRecord.setItemGuid(skuTakeawayInfoRespDTO.getItemGuid());
            printItemRecord.setItemTypeGuid(skuTakeawayInfoRespDTO.getTypeGuid());
            printItemRecord.setNumber(itemDO.getActualItemCount() == null ? itemDO.getItemCount() : itemDO.getActualItemCount());
            printItemRecord.setActualNumber(itemDO.getActualItemCount());
            printItemRecord.setProperty(itemDO.getItemProperty());
            printItemRecord.setAsGift(false);
            printItemRecord.setAsWeight(false);
            printItemRecord.setAsPackage(false);
            printItemRecord.setSort(sort++);
            printItemRecord.setOrderItemGuid(orderReadDO.getOrderGuid() + "," + skuTakeawayInfoRespDTO.getSkuGuid() + "," + itemDO.getItemProperty());
            // 套餐子项
            if (Objects.equals(ItemTypeEnum.PKG.getCode(), skuTakeawayInfoRespDTO.getItemType())) {
                List<PrintItemRecord> subPrintItemRecordList = buildKitchenOrderPrintSubItemRecord(printItemRecord,
                        itemDO, skuTakeawayInfoRespDTO, pkgItemInfoMap);
                printItemRecord.setSubItemRecords(subPrintItemRecordList);
            }
            log.info("<----------待打印菜品Guid[{}]---------->", printItemRecord.getItemGuid());
            arrayOfPrintItemRecord.add(printItemRecord);
        }
        return arrayOfPrintItemRecord;
    }

    /**
     * 构建外卖点菜单 商品列表 套餐子项
     */
    private List<PrintItemRecord> buildKitchenOrderPrintSubItemRecord(PrintItemRecord printItemRecord, ItemDO itemDO,
                                                                      SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO,
                                                                      Map<String, ItemInfoRespDTO> pkgItemInfoMap) {
        printItemRecord.setAsPackage(true);
        ItemInfoRespDTO itemInfoDTO = pkgItemInfoMap.get(skuTakeawayInfoRespDTO.getItemGuid());
        log.info("itemInfoDTO={} itemGuid={}", JacksonUtils.writeValueAsString(itemInfoDTO),
                skuTakeawayInfoRespDTO.getItemGuid());
        if (Objects.isNull(itemInfoDTO)) {
            return Lists.newArrayList();
        }
        List<SubgroupWebRespDTO> subgroupList = itemInfoDTO.getSubgroupList();
        if (CollectionUtils.isEmpty(subgroupList)) {
            log.warn("subgroupList为空 itemGuid={}", skuTakeawayInfoRespDTO.getItemGuid());
            return Lists.newArrayList();
        }
        List<PrintItemRecord> subPrintItemRecordList = new ArrayList<>();
        for (SubgroupWebRespDTO sub : subgroupList) {
            for (SubItemSkuWebRespDTO subItem : sub.getSubItemSkuList()) {
                PrintItemRecord subItemRecord = new PrintItemRecord();
                String subItemSpec = StringUtils.hasText(subItem.getSkuName()) ?
                        "(" + subItem.getSkuName() + ")" : "";
                log.info("subItemSpec={}", subItemSpec);
                String itemName = subItem.getItemName();
                log.info("subItem.getItemName()={}", itemName);
                subItemRecord.setItemName(itemName + subItemSpec);
                subItemRecord.setItemGuid(subItem.getItemGuid());
                subItemRecord.setItemTypeGuid(subItem.getTypeGuid());
                subItemRecord.setNumber(printItemRecord.getNumber().multiply(subItem.getItemNum()));
                // zhaoliang: 这附近的数量要多测测，感觉有问题，但又说不上
                subItemRecord.setActualNumber(itemDO.getActualItemCount().multiply(subItem.getItemNum()));
                subItemRecord.setProperty(itemDO.getItemProperty());
                subItemRecord.setAsGift(false);
                subItemRecord.setAsWeight(false);
                subItemRecord.setAsPackage(true);
                subItemRecord.setSort(printItemRecord.getSort());
                subItemRecord.setOrderItemGuid(itemDO.getOrderGuid() + "," + subItem.getSkuGuid() + "," + itemDO.getItemProperty());
                subPrintItemRecordList.add(subItemRecord);
            }
        }
        return subPrintItemRecordList;
    }
}
