package com.holder.saas.store.takeaway.consumers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemMappingDO;
import com.holderzone.saas.store.dto.takeaway.TcdSyncItemMappingDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ItemMappingMapper extends BaseMapper<ItemMappingDO> {

    /**
     * 赚餐同步商品映射数量
     */
    void syncTcdItemMapping(@Param("list") List<TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping> list);

}
