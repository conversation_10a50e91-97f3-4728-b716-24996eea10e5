package com.holder.saas.store.takeaway.consumers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixRecordDO;
import com.holder.saas.store.takeaway.consumers.helper.PageAdapter;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixRecordDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutRecordQueryDTO;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 外卖修复审核记录 Mapper 接口
 * </p>
 */
public interface FixRecordMapper extends BaseMapper<FixRecordDO> {

    IPage<TakeoutFixRecordDTO> pageInfo(PageAdapter<FixRecordDO> adapter, @Param("query") TakeoutRecordQueryDTO queryDTO);
}
