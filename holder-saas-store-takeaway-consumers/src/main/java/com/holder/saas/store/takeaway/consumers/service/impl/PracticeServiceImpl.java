package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.PracticeDO;
import com.holder.saas.store.takeaway.consumers.mapper.PracticeMapper;
import com.holder.saas.store.takeaway.consumers.service.PracticeService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 外卖菜品做法服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
@Service
public class PracticeServiceImpl extends ServiceImpl<PracticeMapper, PracticeDO> implements PracticeService {
}
