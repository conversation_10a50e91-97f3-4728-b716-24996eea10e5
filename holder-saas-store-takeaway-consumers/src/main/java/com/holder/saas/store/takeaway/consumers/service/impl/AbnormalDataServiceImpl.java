package com.holder.saas.store.takeaway.consumers.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.consumers.entity.domain.AbnormalDataDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.helper.PageAdapter;
import com.holder.saas.store.takeaway.consumers.mapper.AbnormalDataMapper;
import com.holder.saas.store.takeaway.consumers.mapper.ItemMapper;
import com.holder.saas.store.takeaway.consumers.mapstruct.ItemMapstruct;
import com.holder.saas.store.takeaway.consumers.service.AbnormalDataService;
import com.holder.saas.store.takeaway.consumers.service.rpc.OrganizationService;
import com.holder.saas.store.takeaway.consumers.service.rpc.StaffFeignClient;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.request.MoveDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 外卖异常数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Slf4j
@Service
@AllArgsConstructor
public class AbnormalDataServiceImpl extends ServiceImpl<AbnormalDataMapper, AbnormalDataDO> implements AbnormalDataService {

    private final AbnormalDataMapper abnormalDataMapper;

    private final OrganizationService organizationService;

    private final StaffFeignClient staffFeignClient;

    private final ItemMapper itemMapper;

    private final ItemMapstruct itemMapstruct;

    /**
     * 外卖异常数据分页查询
     *
     * @param reqDTO 外卖异常数据分页列表请求
     * @return 外卖异常数据分页列表
     */
    @Override
    public Page<TakeoutItemAbnormalDataRespDTO> page(TakeoutItemAbnormalDataReqDTO reqDTO) {
        // 品牌过滤处理门店
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "，account：" +
                UserContextUtils.getUserAccount() + "，userName：" + UserContextUtils.getUserName() + "-查询员工授权门店耗时");
        List<String> staffStoreGuidList = staffFeignClient.queryAllStoreGuid();
        stopWatch.stop();
        log.info("员工授权门店-staffStoreGuidList={}", staffStoreGuidList);
        if (CollectionUtils.isEmpty(staffStoreGuidList)) {
            log.warn("权限下没有门店");
            return new PageAdapter<>(reqDTO);
        }
        if (!StringUtils.isEmpty(reqDTO.getBrandGuid())) {
            stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "，brandGuid：" +
                    reqDTO.getBrandGuid() + "-查询品牌下门店耗时");
            List<String> storeGuidList = organizationService.queryStoreGuidListByBrandGui(reqDTO.getBrandGuid());
            stopWatch.stop();
            // 个人权限过滤
            storeGuidList.removeIf(s -> !staffStoreGuidList.contains(s));
            if (CollectionUtils.isEmpty(storeGuidList)) {
                log.warn("该品牌下无授权门店");
                return new PageAdapter<>(reqDTO);
            }
            if (CollectionUtils.isEmpty(reqDTO.getStoreGuidList())) {
                reqDTO.setStoreGuidList(storeGuidList);
            } else {
                reqDTO.getStoreGuidList().removeIf(s -> !storeGuidList.contains(s));
                if (CollectionUtils.isEmpty(reqDTO.getStoreGuidList())) {
                    log.warn("品牌下无此门店");
                    return new PageAdapter<>(reqDTO);
                }
            }
        }
        // 个人权限处理
        if (StringUtils.isEmpty(reqDTO.getBrandGuid()) && CollectionUtils.isEmpty(reqDTO.getStoreGuidList())) {
            reqDTO.setStoreGuidList(staffStoreGuidList);
        }

        stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "-查询异常数据sql耗时");
        IPage<TakeoutItemAbnormalDataRespDTO> abnormalDataIPage = abnormalDataMapper.page(new PageAdapter<>(reqDTO), reqDTO);
        stopWatch.stop();
        log.warn(stopWatch.prettyPrint());
        List<TakeoutItemAbnormalDataRespDTO> records = abnormalDataIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageAdapter<>(abnormalDataIPage, Lists.newArrayList());
        }
        // 查询门店名称，表中可能存在没有门店名称的情况
        storeNameHandler(records);
        return new PageAdapter<>(abnormalDataIPage, records);
    }

    private void storeNameHandler(List<TakeoutItemAbnormalDataRespDTO> records) {
        // 查询门店名称，表中可能存在没有门店名称的情况
        List<TakeoutItemAbnormalDataRespDTO> noStoreNameList = records.stream()
                .filter(e -> StringUtils.isEmpty(e.getStoreName()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noStoreNameList)) {
            return;
        }
        List<String> noStoreGuidList = noStoreNameList.stream()
                .map(TakeoutItemAbnormalDataRespDTO::getStoreGuid)
                .distinct()
                .collect(Collectors.toList());
        log.info("查询有门店guid但没有门店名称的门店,入参:{}", JacksonUtils.writeValueAsString(noStoreGuidList));
        List<StoreDTO> storeList = organizationService.queryStoreByIdList(noStoreGuidList);
        Map<String, StoreDTO> storeMap = storeList.stream()
                .collect(Collectors.toMap(StoreDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        for (TakeoutItemAbnormalDataRespDTO respDTO : noStoreNameList) {
            respDTO.setStoreName(storeMap.getOrDefault(respDTO.getStoreGuid(), new StoreDTO()).getName());
        }
    }

    /**
     * 外卖商品迁移异常数据
     */
    @Override
    public void move(MoveDTO moveDTO) {
        // 切换数据源
        UserContextUtils.putErp(moveDTO.getEnterpriseGuid());
        EnterpriseIdentifier.setEnterpriseGuid(moveDTO.getEnterpriseGuid());

        List<ItemDO> itemDOList = itemMapper.selectList(new LambdaQueryWrapper<ItemDO>()
                .isNull(ItemDO::getErpItemName)
                .isNotNull(ItemDO::getThirdSkuId)
                .ne(ItemDO::getThirdSkuId, "")
                .ge(!ObjectUtils.isEmpty(moveDTO.getStartDateTime()), ItemDO::getGmtCreate, moveDTO.getStartDateTime())
                .le(!ObjectUtils.isEmpty(moveDTO.getEndDateTime()), ItemDO::getGmtCreate, moveDTO.getEndDateTime())
                .last(!ObjectUtils.isEmpty(moveDTO.getExecuteCount()), "limit " + moveDTO.getExecuteCount())
        );
        if (CollectionUtils.isEmpty(itemDOList)) {
            log.warn("执行完毕或没有查询到异常数据");
            return;
        }
        List<AbnormalDataDO> dataDOList = itemMapstruct.itemDOList2AbnormalDataDOList(itemDOList);
        log.info("dataDOList={}", JacksonUtils.writeValueAsString(dataDOList));
        abnormalDataMapper.insertIgnore(dataDOList);
    }

    /**
     * 数据修复列表
     *
     * @param reqDTO 数据修复列表请求
     * @return 数据修复列表
     */
    @Override
    public List<TakeoutItemDataFixRespDTO> listDataFix(TakeoutItemDataFixReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getTakeoutAndStore())) {
            throw new BusinessException("外卖信息为空");
        }
        List<String> spliceList = reqDTO.getTakeoutAndStore().stream()
                .map(t -> t.getTakeoutItemNumber() + "-" + t.getStoreGuid())
                .collect(Collectors.toList());
        reqDTO.setSpliceList(spliceList);
        List<TakeoutItemDataFixRespDTO> respDTOS = new ArrayList<>();
        Map<String, TakeoutItemDataFixRespDTO> fixMap = abnormalDataMapper.listDataFix(reqDTO).stream()
                .collect(Collectors.toMap(i -> i.getThirdSkuId() + "-" + i.getStoreGuid(), Function.identity(), (v1, v2) -> v2));
        spliceList.forEach(s -> {
            TakeoutItemDataFixRespDTO dto = fixMap.get(s);
            if (!ObjectUtils.isEmpty(dto)) {
                respDTOS.add(dto);
            }
        });

        // 查询门店名称，表中可能存在没有门店名称的情况
        List<TakeoutItemDataFixRespDTO> noStoreNameList = respDTOS.stream()
                .filter(e -> StringUtils.isEmpty(e.getStoreName()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(noStoreNameList)) {
            List<String> noStoreGuidList = noStoreNameList.stream()
                    .map(TakeoutItemDataFixRespDTO::getStoreGuid)
                    .distinct()
                    .collect(Collectors.toList());
            log.info("查询有门店guid但没有门店名称的门店,入参:{}", JacksonUtils.writeValueAsString(noStoreGuidList));
            List<StoreDTO> storeList = organizationService.queryStoreByIdList(noStoreGuidList);
            Map<String, StoreDTO> storeMap = storeList.stream()
                    .collect(Collectors.toMap(StoreDTO::getGuid, Function.identity(), (key1, key2) -> key1));
            for (TakeoutItemDataFixRespDTO respDTO : noStoreNameList) {
                respDTO.setStoreName(storeMap.getOrDefault(respDTO.getStoreGuid(), new StoreDTO()).getName());
            }
        }
        return respDTOS;
    }

    /**
     * 查询外卖异常数据
     *
     * @param reqDTO 条件
     * @return 外卖异常数据
     */
    @Override
    public List<ItemDO> listFixItem(TakeoutItemDataFixReqDTO reqDTO) {
        List<ItemDO> fixItemList = abnormalDataMapper.listFixItem(reqDTO);
        if(CollUtil.isEmpty(fixItemList)){
            return Collections.emptyList();
        }
        //过滤出一一对应得商品和门店关系
        Map<String,String> map = Maps.newHashMap();
        reqDTO.getTakeoutAndStore().forEach(ts -> map.put(ts.getTakeoutItemNumber(),ts.getStoreGuid()));
        if(CollUtil.isEmpty(map)){
            return fixItemList;
        }
        List<ItemDO> removeList = Lists.newArrayList();
        fixItemList.forEach(itemDO -> {
            if(map.get(itemDO.getThirdSkuId()).equalsIgnoreCase(itemDO.getStoreGuid())){
               return;
            }
            removeList.add(itemDO);
        });
        fixItemList.removeAll(removeList);
        return fixItemList;
    }

    @Override
    public void removeByTakeoutItemIds(List<Long> takeoutItemIds) {
        remove(new LambdaQueryWrapper<AbnormalDataDO>()
                .in(AbnormalDataDO::getTakeoutItemId, takeoutItemIds));
    }
}
