package com.holder.saas.store.takeaway.consumers.entity.bo;

import com.holder.saas.store.takeaway.consumers.entity.domain.FixItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixRecordDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FixItemBiz implements Serializable {

    private static final long serialVersionUID = -9213827365716340174L;

    /**
     * 是否是提交记录
     */
    private Boolean commitFlag;

    /**
     *
     */
    private Boolean fixBindFlag;

    /**
     * 提交记录
     */
    private FixRecordDO record;

    /**
     * 提交明细
     */
    private List<FixItemDO> itemList;

}
