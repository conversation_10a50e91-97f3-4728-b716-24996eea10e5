package com.holder.saas.store.takeaway.consumers.event;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import javafx.util.Pair;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @create 2024-08-07
 * @description
 */
@Getter
@Setter
public class MessageEvent extends ApplicationEvent {

    private Pair<UserContext,BusinessMessageDTO> businessMessage;

    public MessageEvent(Pair<UserContext,BusinessMessageDTO> businessMessage) {
        super(businessMessage);
        this.businessMessage = businessMessage;
    }
}
