package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.*;
import com.holder.saas.store.takeaway.consumers.mapper.*;
import com.holder.saas.store.takeaway.consumers.service.*;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderTradeDetailDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;


/**
 * <p>
 * 订单结算明细 服务实现类
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderTradeDetailServiceImpl extends ServiceImpl<OrderTradeDetailMapper, OrderTradeDetailDO> implements OrderTradeDetailService {

    private final OrderService orderService;

    @Override
    public void create(TakeoutOrderTradeDetailDTO orderTradeDetailDTO) {
        if (Objects.isNull(orderTradeDetailDTO)) {
            return;
        }
        OrderTradeDetailDO orderTradeDetailDO = new OrderTradeDetailDO();
        BeanUtils.copyProperties(orderTradeDetailDTO, orderTradeDetailDO);

        // 查询订单
        OrderDO orderDO = orderService.getOne(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getOrderId, orderTradeDetailDTO.getOrderId()));
        if (Objects.nonNull(orderDO)) {
            orderTradeDetailDO.setOrderCreateTime(orderDO.getCreateTime());
            orderTradeDetailDO.setOrderCompleteTime(orderDO.getCompleteTime());
            orderTradeDetailDO.setStoreGuid(orderDO.getStoreGuid());
            orderTradeDetailDO.setBrandGuid(orderDO.getBrandGuid());
            orderTradeDetailDO.setTakeoutOrderType(orderDO.getOrderSubType());
        }
        // 目前都是美团订单
        orderTradeDetailDO.setTakeoutOrderType(0);
        if (Objects.isNull(orderTradeDetailDO.getOrderCreateTime())) {
            orderTradeDetailDO.setOrderCreateTime(LocalDateTime.now());
        }
        if (orderTradeDetailDO.getStatus().equals(8)) {
            orderTradeDetailDO.setOrderCompleteTime(LocalDateTime.now());
        }
        saveOrUpdate(orderTradeDetailDO);
    }
}
