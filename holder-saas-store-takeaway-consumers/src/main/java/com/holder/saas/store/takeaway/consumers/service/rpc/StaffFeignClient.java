package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holder.saas.store.takeaway.consumers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.dto.user.UserSpinnerDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(value = "holder-saas-store-staff", fallbackFactory = StaffFeignClient.ServiceFallBack.class)
public interface StaffFeignClient {

    @PostMapping("/user_data/query_store_spinner")
    UserSpinnerDTO findStoreManagedByUser();

    @GetMapping("/product/query_mchnt_type")
    String queryMchntType();


    @ApiOperation(value = "查询所有门店Guid")
    @PostMapping(value = "/user_data/query_all_store_guid")
    List<String> queryAllStoreGuid();

    @PostMapping("/menu/get_source_by_user")
    List<MenuSourceDTO> getSourceByUser(@RequestParam("terminalCode") String terminalCode);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<StaffFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public StaffFeignClient create(Throwable cause) {
            return new StaffFeignClient() {
                @Override
                public UserSpinnerDTO findStoreManagedByUser() {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "findStoreManagedByUser", "无",
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String queryMchntType() {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "queryMchntType", "无",
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    // 即使rpc出错，也尽量保证打出单据，即使营业类型不对造成的格式不对
                    return null;
                }

                @Override
                public List<String> queryAllStoreGuid() {
                    log.error(HYSTRIX_PATTERN, "queryAllStoreGuid", "无入参", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<MenuSourceDTO> getSourceByUser(String terminalCode) {
                    log.error(HYSTRIX_PATTERN, "getSourceByUser", terminalCode, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
