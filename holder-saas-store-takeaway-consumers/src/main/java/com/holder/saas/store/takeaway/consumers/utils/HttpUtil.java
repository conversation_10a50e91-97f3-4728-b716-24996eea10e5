package com.holder.saas.store.takeaway.consumers.utils;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.HttpClientBuilder;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HttpUtil
 * @date 2019/12/02 9:57
 * @description //TODO
 * @program IdeaProjects
 */
@Slf4j
public class HttpUtil {
    /**
     * HTTP post請求
     * @param url  路径
     * @param jsonString 请求json字符串
     * @return
     * @throws IOException
     */
    public static String doPostJson(String url, String jsonString) throws IOException {
        log.info("httpRequest:---> request-url:{},requestBody:{}", url,jsonString);
        HttpClient httpClient = HttpClientBuilder.create().build();
        StringEntity stringEntity = new StringEntity(jsonString,"utf-8");
        stringEntity.setContentType("application/json");
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(stringEntity);
        httpPost.addHeader("enterpriseGuid", UserContextUtils.getEnterpriseGuid());
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        String execute = httpClient.execute(httpPost,responseHandler);
        log.info("httppostresult：{}", execute);
        return execute;
    }
}