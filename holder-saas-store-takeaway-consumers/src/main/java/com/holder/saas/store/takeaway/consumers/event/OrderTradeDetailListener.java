package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.service.OrderTradeDetailService;
import com.holder.saas.store.takeaway.consumers.service.rpc.CloudEnterpriseFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holder.saas.store.takeaway.consumers.utils.ThrowableExtUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderTradeDetailDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;


/**
 * 订单结算明细
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketListenerHandler(
        topic = RocketMqConfig.TAKEAWAY_CONSUMERS_ORDER_TRADE_DETAIL_TOPIC,
        tags = RocketMqConfig.TAKEAWAY_CONSUMERS_ORDER_TRADE_DETAIL_TAG,
        consumerGroup = RocketMqConfig.TAKEAWAY_ORDER_TRADE_DETAIL_CONSUMERS_GROUP)
public class OrderTradeDetailListener extends AbstractRocketMqConsumer<RocketMqTopic, TakeoutOrderTradeDetailDTO> {

    private final DynamicHelper dynamicHelper;

    private final CloudEnterpriseFeignClient enterpriseFeignClient;

    private final OrderTradeDetailService orderTradeDetailService;

    @Override
    public boolean consumeMsg(TakeoutOrderTradeDetailDTO tradeDetailDTO, MessageExt messageExt) {
        String orderId = tradeDetailDTO.getOrderId();
        if (log.isInfoEnabled()) {
            log.info("订单[{}]结算明细回调，入参:{}", orderId, JacksonUtils.writeValueAsString(tradeDetailDTO));
        }
        // 切库
        String enterpriseGuid = tradeDetailDTO.getEnterpriseGuid();
        //判断企业是否存在
        if (!enterpriseFeignClient.hasEnterprise(enterpriseGuid)) {
            return true;
        }
        if (log.isInfoEnabled()) {
            log.info("根据enterpriseGuid({})切换数据源", enterpriseGuid);
        }
        dynamicHelper.changeDatasource(enterpriseGuid);
        UserContextUtils.putErp(enterpriseGuid);

        try {
            orderTradeDetailService.create(tradeDetailDTO);
        } catch (Exception e) {
            log.error("订单({})结算明细回调发生异常：{}", orderId, ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            dynamicHelper.removeThreadLocalDatabaseInfo();
            UserContextUtils.remove();
        }
        return true;
    }

}
