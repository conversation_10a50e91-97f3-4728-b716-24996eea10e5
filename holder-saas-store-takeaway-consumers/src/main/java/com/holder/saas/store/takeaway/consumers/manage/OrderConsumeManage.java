package com.holder.saas.store.takeaway.consumers.manage;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.SkuMapDO;
import com.holder.saas.store.takeaway.consumers.mapper.SkuMapMapper;
import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holder.saas.store.takeaway.consumers.service.rpc.CloudEnterpriseFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.takeaway.UnItem;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.UnOrderCbMsgType;
import jodd.util.URLDecoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-01-05
 * @description 订单消费业务处理
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderConsumeManage {

    private final DynamicHelper dynamicHelper;

    private final OrderService orderService;

    private final CloudEnterpriseFeignClient enterpriseFeignClient;

    private final SkuMapMapper skuMapMapper;

    public boolean process(UnOrder unOrder){
        // 切库
        String enterpriseGuid = unOrder.getEnterpriseGuid();
        //判断企业是否存在
        if (Boolean.FALSE.equals(enterpriseFeignClient.hasEnterprise(enterpriseGuid))) {
            return true;
        }
        String storeGuid = unOrder.getStoreGuid();
        if (log.isInfoEnabled()) {
            log.info("根据enterpriseGuid({})切换数据源", enterpriseGuid);
        }
        dynamicHelper.changeDatasource(enterpriseGuid);

        // 构造userInfo，以便其他服务能获取到ErpGuid
        UserContextUtils.putErpAndStore(enterpriseGuid, storeGuid);

        try {
            // fix: url decode
            if (StringUtils.hasText(unOrder.getShipperName())) {
                String shipperName = URLDecoder.decode(unOrder.getShipperName());
                unOrder.setShipperName(shipperName);
            }
            //更新饿了吗erpSku.guid映射
            replaceEleSkuGuidMap(unOrder);
            //截取三方返回字段
            judgeAndCutOut(unOrder);
            //处理订单
            consumeUnOrder(unOrder);
        } catch (Exception e) {
            log.error("订单号:{},TPS发送的Callback消息，消费发生异常：", unOrder.getOrderId(), e);
            return false;
        } finally {
            dynamicHelper.removeThreadLocalDatabaseInfo();
            UserContextUtils.remove();
        }
        return true;
    }
    private static final int MAX_LENGTH_DISCOUNT = 50;

    private void judgeAndCutOut(UnOrder unOrder) {
        //截取优惠信息名称
        if(CollUtil.isEmpty(unOrder.getArrayOfUnDiscount())){
            return;
        }
        unOrder.getArrayOfUnDiscount().forEach(e ->{
            if(StringUtils.isEmpty(e.getDiscountName()) || e.getDiscountName().length() <= MAX_LENGTH_DISCOUNT){
                return;
            }
            //若优惠名称大于50则截取
            e.setDiscountName(e.getDiscountName().substring(0,MAX_LENGTH_DISCOUNT - 1));
        });
    }

    private void replaceEleSkuGuidMap(UnOrder unOrder){
        //只有饿了吗不能识别-
        if (unOrder.getOrderSubType() != OrderType.TakeoutSubType.ELE_TAKEOUT.getType()
                || CollectionUtils.isEmpty(unOrder.getArrayOfUnItem())) {
            return;
        }
        Map<String, UnItem> itemMap = new HashMap<>(unOrder.getArrayOfUnItem().size());
        for (UnItem unItem : unOrder.getArrayOfUnItem()) {
            itemMap.put(unItem.getItemSku(), unItem);
        }
        //去sku映射里找
        final List<SkuMapDO> skuMapDOList =
                skuMapMapper.selectList(new LambdaQueryWrapper<SkuMapDO>().select(SkuMapDO::getGuid,
                        SkuMapDO::getSourceGuid).in(SkuMapDO::getGuid, itemMap.keySet()));
        if (!CollectionUtils.isEmpty(skuMapDOList)) {
            //更新原erpSku.guid
            for (SkuMapDO skuMapDO : skuMapDOList) {
                itemMap.get(skuMapDO.getGuid()).setItemSku(skuMapDO.getSourceGuid());
            }
        }
    }

    private void consumeUnOrder(UnOrder unOrder){
        int cbMsgType = unOrder.getCbMsgType();
        switch (cbMsgType) {
            case UnOrderCbMsgType.ORDER_ACTIVATED:
                orderService.orderCreate(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_CONFIRMED:
                orderService.orderAccept(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_SHIPPING:
                orderService.updateShipping(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_SHIP_SUCCEED:
                orderService.updateShippingCompleted(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_SHIPPING_DISTRIBUTE:
                orderService.updateShippingDistribute(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_FINISHED:
                orderService.orderCompleted(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_CANCELED_BY_MCHNT_BEFORE_CONFIRMATION:
                orderService.orderCanceledAsReject(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_CANCELED:
                orderService.orderCanceled(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_URGED:
                orderService.orderRemindByUser(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_CANCEL_REQ:
                orderService.cancelOrderReq(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_CANCEL_CANCEL_REQ:
                orderService.cancelCancelOrderReq(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_CANCEL_AGREED:
                orderService.cancelReqAgreed(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_CANCEL_DISAGREED:
                orderService.cancelReqDisagreed(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_CANCEL_ARBITRATION_EFFECTIVE:
                orderService.cancelArbitrationEffective(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_REFUND_REQ:
                orderService.refundOrderReq(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_CANCEL_REFUND_REQ:
                orderService.cancelRefundOrderReq(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_REFUND_AGREED:
                orderService.refundReqAgreed(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_REFUND_DISAGREED:
                orderService.refundReqDisagreed(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_REFUND_ARBITRATION_EFFECTIVE:
                orderService.refundArbitrationEffective(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_OWN_SAVE:
                orderService.orderOwnSave(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_DELIVERY_ERROR:
                orderService.deliveryError(unOrder);
                break;
            case UnOrderCbMsgType.ORDER_DINING_OUT_TCD:
                orderService.diningOutTcd(unOrder);
                break;
            case -1:
                log.info("<----------cbMsgType未初始化---------->");
                break;
            default:
                log.info("<----------暂不支持该请求---------->");
                break;
        }
    }
}
