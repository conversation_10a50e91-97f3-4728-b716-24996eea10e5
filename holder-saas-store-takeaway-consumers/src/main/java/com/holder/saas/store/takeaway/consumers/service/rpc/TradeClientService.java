package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderItemRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = TradeClientService.TradeFallBack.class)
public interface TradeClientService {

    @PostMapping("/adjust_order/list/byOrderGuids")
    List<AdjustByOrderItemRespDTO> listByOrderItemGuids(@RequestBody AdjustOrderQueryDTO queryDTO);

    @PostMapping("/adjust_order/orderGuids")
    List<Long> listByOrderGuids(@RequestBody AdjustOrderQueryDTO queryDTO);

    @Component
    @Slf4j
    class TradeFallBack implements FallbackFactory<TradeClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TradeClientService create(Throwable throwable) {
            return new TradeClientService() {

                @Override
                public List<AdjustByOrderItemRespDTO> listByOrderItemGuids(AdjustOrderQueryDTO queryDTO) {
                    log.error("获取调整订单明细FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("取调整订单明细失败!" + throwable.getMessage());
                }

                @Override
                public List<Long> listByOrderGuids(AdjustOrderQueryDTO queryDTO) {
                    log.error("根据订单guids查询对应是否调整，throwable={}", throwable.getMessage());
                    throw new BusinessException("根据订单guids查询对应是否调整失败!" + throwable.getMessage());
                }
            };
        }
    }
}
