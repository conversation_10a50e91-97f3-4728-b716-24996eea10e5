package com.holder.saas.store.takeaway.consumers.service;


import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;

import java.util.Set;

/**
 * 外卖自动接单漏单处理
 */
public interface AutoAcceptOrderService {

    /**
     * 创建订单时 将应自动接单的订单放入队列
     */
    void createOrder(OrderDO orderDO);

    /**
     * 接单时 删除队列里的订单
     */
    void removeOrder(OrderDO orderDO);

    /**
     * 获取队列里的订单
     */
    Set<String> getOrderQueue(String storeGuid);

}
