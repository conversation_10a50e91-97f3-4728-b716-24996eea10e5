package com.holder.saas.store.takeaway.consumers.config;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FeignBeanFactoryPostProcessor
 * @date 2018/09/20 14:58
 * @description
 * @program holder-saas-store-takeaway
 */
@Component
public class FeignBeanFactoryPostProcessor implements BeanFactoryPostProcessor {

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory configurableListableBeanFactory) throws BeansException {
        if (containsBeanDefinition(configurableListableBeanFactory, "feignContext", "eurekaAutoServiceRegistration")) {
            BeanDefinition bd = configurableListableBeanFactory.getBeanDefinition("feignContext");
            bd.setDependsOn("eurekaAutoServiceRegistration");
        }
    }

    private boolean containsBeanDefinition(ConfigurableListableBeanFactory beanFactory, String... beans) {
        return Arrays.stream(beans).allMatch(beanFactory::containsBeanDefinition);
    }
}
