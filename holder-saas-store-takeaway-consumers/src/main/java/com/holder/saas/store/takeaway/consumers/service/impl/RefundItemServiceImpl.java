package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.RefundItemDO;
import com.holder.saas.store.takeaway.consumers.mapper.RefundItemMapper;
import com.holder.saas.store.takeaway.consumers.service.RefundItemService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 外卖退菜商品明细服务实现类
 * </p>
 */
@Service
public class RefundItemServiceImpl extends ServiceImpl<RefundItemMapper, RefundItemDO> implements RefundItemService {


    @Override
    public List<RefundItemDO> listItemGroupByOrderItemGuid(String orderGuid) {
        return baseMapper.listItemGroupByOrderItemGuid(orderGuid);
    }

    @Override
    public void updateRefundSuccess(String orderGuid) {
        baseMapper.updateRefundSuccess(orderGuid);
    }

    @Override
    public void removeByOrderGuid(String orderGuid) {
        LambdaQueryWrapper<RefundItemDO> qw = new LambdaQueryWrapper<>();
        qw.eq(RefundItemDO::getOrderGuid, orderGuid);
        remove(qw);
    }
}
