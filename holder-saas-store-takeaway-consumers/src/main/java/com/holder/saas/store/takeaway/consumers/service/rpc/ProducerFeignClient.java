package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holder.saas.store.takeaway.consumers.entity.domain.StoreBindOrderDO;
import com.holder.saas.store.takeaway.consumers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.organization.QueryBrandDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.TokenDTO;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayProducesService
 * @date 2018/09/17 11:58
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-takeaway-producer", fallbackFactory = ProducerFeignClient.ServiceFallBack.class)
public interface ProducerFeignClient {

    /**
     * 根据erp门店id查询饿了么外卖授权表，是否已经授权
     *
     * @param storeAuthorizationDTOList
     * @return
     */
    @PostMapping("/ele/list_auth")
    List<StoreAuthDTO> listEleAuth(@RequestBody List<StoreAuthDTO> storeAuthorizationDTOList);

    /**
     * 根据erp门店id查询饿了么外卖授权表，是否已经授权
     *
     * @param storeAuthDTO
     * @return
     */
    @PostMapping("/ele/get_takeout_auth")
    StoreAuthDTO getEleTakeoutAuth(@RequestBody StoreAuthDTO storeAuthDTO);

    @PostMapping("/ele/update_delivery")
    Boolean eleUpdateDelivery(@RequestBody StoreAuthDTO storeAuthDTO);

    @PostMapping("/mt/update_delivery")
    Boolean mtUpdateDelivery(@RequestBody StoreAuthDTO storeAuthDTO);

    @PostMapping("/tcd/update_delivery")
    Boolean tcdUpdateDelivery(@RequestBody StoreAuthDTO storeAuthDTO);

    @PostMapping(value = "/dx/send_call")
    void sendCall(@RequestBody SendOrderCallReq sendOrderCallReq);

    /**
     * 根据erp门店id查询掌控者外卖授权表，是否已经授权
     *
     * @param storeAuthDTO
     * @return
     */
    @PostMapping("/own/get_takeout_auth")
    StoreAuthDTO getOwnTakeoutAuth(@RequestBody StoreAuthDTO storeAuthDTO);

    /**
     * 根据erp门店id查询掌控者外卖授权表，是否已经授权
     *
     * @param storeAuthDTO
     * @return
     */
    @PostMapping("/tcd/get_takeout_auth")
    StoreAuthDTO getTcdTakeoutAuth(@RequestBody StoreAuthDTO storeAuthDTO);


    /**
     * 根据erp门店id查询掌控者外卖授权表，是否已经授权
     *
     * @param storeGuid
     * @return
     */
    @PostMapping("/own/get_holder_auth")
    HolderAuthDTO getHolder(@RequestBody String storeGuid);

    @PostMapping("/own/go_shipping")
    String goShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @PostMapping("/own/done_shipping")
    String doneShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @PostMapping("/own/cancel_shipping")
    String cancelShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 根据erp门店id查询掌控者外卖配送方式
     *
     * @param baseDTO
     * @return
     */
    @PostMapping("/own/get_distribution")
    List<OwnDistributionDTO> getDistribution(@RequestBody BaseDTO baseDTO);

    /**
     * 根据erp门店id查询美团外卖授权表，是否已经授权
     *
     * @param storeAuthorizationDTOList
     * @return
     */
    @PostMapping("/mt/list_auth")
    List<StoreAuthDTO> listMtAuth(@RequestBody @Validated List<StoreAuthDTO> storeAuthorizationDTOList);

    /**
     * 根据erp门店id查询美团外卖授权表，是否已经授权
     *
     * @param storeAuthDTO
     * @return
     */
    @PostMapping("/mt/get_takeout_auth")
    StoreAuthDTO getMtTakeoutAuth(@RequestBody StoreAuthDTO storeAuthDTO);

    /**
     * 根据erp门店id查询美团团购授权表，是否已经授权
     *
     * @param storeAuthDTO
     * @return
     */
    @PostMapping("/mt/get_tuangou_auth")
    StoreAuthDTO getMtTuanGouAuth(@RequestBody StoreAuthDTO storeAuthDTO);

    /**
     * 根据erp门店id查询单个token
     *
     * @param takeoutShopBindReqDTO
     * @return
     */
    @PostMapping("/mt/query_token")
    TokenDTO queryMtToken(@RequestBody TakeoutShopBindReqDTO takeoutShopBindReqDTO);

    /**
     * 回复外卖平台
     *
     * @param unOrder
     */
    @PostMapping("/reply")
    void reply(@RequestBody UnOrder unOrder);

    /**
     * 验券
     */
    @PostMapping("/mt/check_ticket")
    MtCouponDoCheckRespDTO checkTicket(@RequestBody CouPonReqDTO couPonReqDTO);


    /**
     * 执行验券
     */
    @PostMapping("/mt/do_check")
    MtCouponDoCheckRespDTO doCheck(@RequestBody CouPonReqDTO couPonReqDTO);


    /**
     * 预验券
     */
    @PostMapping("/mt/pre_check")
    MtCouponPreRespDTO preCheck(@RequestBody CouPonPreReqDTO couPonPreReqDTO);

    /**
     * 撤销验券
     */
    @PostMapping("/mt/cancel_ticket")
    MtDelCouponRespDTO cancelTicket(@RequestBody CouponDelReqDTO couponDelReqDTO);


    /**
     * 查询团购订单结算明细
     */
    @PostMapping("/mt/group/trade/detail")
    MtCouponTradeDetailRespDTO queryGroupTradeDetail(@RequestBody CouPonReqDTO couPonReqDTO);

    /**
     * 批量查询外卖平台商品
     */
    @PostMapping("/item_mapping/query_items")
    List<UnMappedItem> getItems(@RequestBody UnItemQueryReq unItemQueryReq);

    /**
     * 获取平台方商品分类列表
     *
     * @param storeGuid
     * @param type
     * @return
     */
    @GetMapping("/item_mapping/query_type/{storeGuid}/{type}")
    List<UnMappedType> getType(@PathVariable("storeGuid") String storeGuid, @PathVariable("type") int type);

    /**
     * 获取平台方商品列表
     *
     * @param storeGuid
     * @param type
     * @return
     */
    @GetMapping("/item_mapping/query_item/{storeGuid}/{type}")
    List<UnMappedItem> getItem(@PathVariable("storeGuid") String storeGuid, @PathVariable("type") int type);

    /**
     * 获取平台方商品列表
     *
     * @param queryBrandDTO 请求参数
     * @return 京东平台方商品信息
     */
    @PostMapping("/item_mapping/query_jd_item")
    List<UnMappedItem> getJdItem(@RequestBody QueryBrandDTO queryBrandDTO);

    /**
     * 设置商品映射
     *
     * @param unItemBindUnbindReq
     */
    @PostMapping("/item_mapping/bind")
    void setMapping(@RequestBody UnItemBindUnbindReq unItemBindUnbindReq);

    /**
     * 删除商品映射
     *
     * @param unItemBindUnbindReq
     */
    @PostMapping("/item_mapping/unbind")
    void cancelMapping(@RequestBody UnItemBindUnbindReq unItemBindUnbindReq);

    /**
     * 删除商品映射
     *
     * @param unItemBatchUnbindReq
     */
    @PostMapping("/item_mapping/batch_unbind")
    void batchCancelMapping(@RequestBody UnItemBatchUnbindReq unItemBatchUnbindReq);

    /**
     * 批量绑定商品映射
     *
     * @param unItemBatchUnbindReq
     */
    @PostMapping("/item_mapping/batch_bind")
    void batchBindMapping(@RequestBody UnItemBatchUnbindReq unItemBatchUnbindReq);

    @PostMapping("ele/get_effect_service_packContract")
    String getEffectServicePackContract(@RequestBody UnOrder unOrder);

    @GetMapping("/takeout/get_bind_stock_store")
    StockStoreBindResqDTO getBindStockStore(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("takeout/bind_stock_store_order")
    void bindStockStoreOrder(@RequestBody StockStoreBindReqOrderDTO reqOrderDTO);

    @GetMapping("/takeout/get_bind_stock_store_order")
    StoreBindOrderDO getBindStockStoreOrder(@RequestParam("branchStoreGuid") String branchStoreGuid, @RequestParam("orderId") String orderId);

    /**
     * 查询饿了么订单商品价格信息
     //     */
    @GetMapping("/ele/get_order_item_price/{storeGuid}/{orderId}")
    List<EleOrderItemPriceDTO> getOrderItemPrice(@PathVariable("storeGuid") String storeGuid, @PathVariable("orderId") String orderId);

    @GetMapping("/ele/get_order/{storeGuid}/{orderId}")
    @ApiOperation(value = "查询饿了么订单信息")
    OleOrder getOrderItemOOrder(@PathVariable("storeGuid") String storeGuid, @PathVariable("orderId") String orderId);

    /**
     * 外卖订单出餐
     * @param type 外卖类型
     * @param orderOperateDTO 入参参数
     */
    @PostMapping("/takeout/order_operate/{type}")
    void orderOperate(@PathVariable("type") Integer type, @RequestBody TakeoutOrderOperateDTO orderOperateDTO);


    @PostMapping("/jd/get_takeout_auth")
    StoreAuthDTO geJdTakeoutAuth(@RequestBody StoreAuthDTO storeAuthDTO);


    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ProducerFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ProducerFeignClient create(Throwable cause) {
            return new ProducerFeignClient() {

                @Override
                public void bindStockStoreOrder(StockStoreBindReqOrderDTO reqOrderDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "bindStockStoreOrder", JacksonUtils.writeValueAsString(reqOrderDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public StoreBindOrderDO getBindStockStoreOrder(String branchStoreGuid, String orderId) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getBindStockStoreOrder", JacksonUtils.writeValueAsString(orderId),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<EleOrderItemPriceDTO> getOrderItemPrice(String storeGuid, String orderId) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getOrderItemPrice", "storeGuid:" + storeGuid + "orderId:" + orderId,
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public OleOrder getOrderItemOOrder(String storeGuid, String orderId) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getOrderItemOOrder", JacksonUtils.writeValueAsString(storeGuid),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public StockStoreBindResqDTO getBindStockStore(String storeGuid) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getBindStockStore", JacksonUtils.writeValueAsString(storeGuid),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<StoreAuthDTO> listEleAuth(List<StoreAuthDTO> storeAuthorizationDTOList) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "listEleAuth", JacksonUtils.writeValueAsString(storeAuthorizationDTOList),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<OwnDistributionDTO> getDistribution(BaseDTO baseDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getDistribution", JacksonUtils.writeValueAsString(baseDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String goShipping(TakeoutOrderDTO takeoutOrderDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "goShipping", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public Boolean tcdUpdateDelivery(StoreAuthDTO storeAuthDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "tcdUpdateDelivery", JacksonUtils.writeValueAsString(storeAuthDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public void sendCall(SendOrderCallReq sendOrderCallReq) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "sendCall", JacksonUtils.writeValueAsString(sendOrderCallReq),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String doneShipping(TakeoutOrderDTO takeoutOrderDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "doneShipping", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String cancelShipping(TakeoutOrderDTO takeoutOrderDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "cancelShipping", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public StoreAuthDTO getEleTakeoutAuth(StoreAuthDTO storeAuthDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getEleTakeoutAuth", JacksonUtils.writeValueAsString(storeAuthDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public StoreAuthDTO getOwnTakeoutAuth(StoreAuthDTO storeAuthDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getOwnTakeoutAuth", JacksonUtils.writeValueAsString(storeAuthDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public HolderAuthDTO getHolder(String storeGuid) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getHolder", JacksonUtils.writeValueAsString(storeGuid),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<StoreAuthDTO> listMtAuth(List<StoreAuthDTO> storeAuthorizationDTOList) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "listMtAuth", JacksonUtils.writeValueAsString(storeAuthorizationDTOList),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public StoreAuthDTO getMtTakeoutAuth(StoreAuthDTO storeAuthDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getMtTakeoutAuth", JacksonUtils.writeValueAsString(storeAuthDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public StoreAuthDTO getMtTuanGouAuth(StoreAuthDTO storeAuthDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getMtTuanGouAuth", JacksonUtils.writeValueAsString(storeAuthDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public TokenDTO queryMtToken(TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "queryMtToken", JacksonUtils.writeValueAsString(takeoutShopBindReqDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public void reply(UnOrder unOrder) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "reply", JacksonUtils.writeValueAsString(unOrder),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public MtCouponDoCheckRespDTO checkTicket(CouPonReqDTO couPonReqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "checkTicket", JacksonUtils.writeValueAsString(couPonReqDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }


                @Override
                public MtCouponDoCheckRespDTO doCheck(CouPonReqDTO couPonReqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "doCheck", JacksonUtils.writeValueAsString(couPonReqDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public MtCouponPreRespDTO preCheck(CouPonPreReqDTO couPonPreReqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "preCheck", JacksonUtils.writeValueAsString(couPonPreReqDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public MtDelCouponRespDTO cancelTicket(CouponDelReqDTO couponDelReqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "cancelTicket", JacksonUtils.writeValueAsString(couponDelReqDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public MtCouponTradeDetailRespDTO queryGroupTradeDetail(CouPonReqDTO couPonReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryGroupTradeDetail", JacksonUtils.writeValueAsString(couPonReqDTO),
                            ThrowableExtUtils.asStringIfAbsent(cause));
                    throw new ServerException();
                }

                @Override
                public List<UnMappedItem> getItems(UnItemQueryReq unItemQueryReq) {
                    log.error(HYSTRIX_PATTERN, "getItems", JacksonUtils.writeValueAsString(unItemQueryReq),
                            ThrowableExtUtils.asStringIfAbsent(cause));
                    throw new ServerException();
                }


                @Override
                public List<UnMappedType> getType(String storeGuid, int type) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getType", "storeGuid=" + storeGuid + ", type=" + type,
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<UnMappedItem> getItem(String storeGuid, int type) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getItem", "storeGuid=" + storeGuid + ", type=" + type,
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<UnMappedItem> getJdItem(QueryBrandDTO queryBrandDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN,"getJdItem",JacksonUtils.writeValueAsString(queryBrandDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public void setMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "setMapping", JacksonUtils.writeValueAsString(unItemBindUnbindReq),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public void cancelMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "cancelMapping", JacksonUtils.writeValueAsString(unItemBindUnbindReq),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public void batchCancelMapping(UnItemBatchUnbindReq unItemBatchUnbindReq) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "unItemBatchUnbindReq", JacksonUtils.writeValueAsString(unItemBatchUnbindReq),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public void batchBindMapping(UnItemBatchUnbindReq unItemBatchUnbindReq) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "batchBindMapping", JacksonUtils.writeValueAsString(unItemBatchUnbindReq),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public Boolean eleUpdateDelivery(StoreAuthDTO storeAuthDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "eleUpdateDelivery", JacksonUtils.writeValueAsString(storeAuthDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public Boolean mtUpdateDelivery(StoreAuthDTO storeAuthDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "mtUpdateDelivery", JacksonUtils.writeValueAsString(storeAuthDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String getEffectServicePackContract(UnOrder unOrder) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getEffectServicePackContract", JacksonUtils.writeValueAsString(unOrder),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public StoreAuthDTO getTcdTakeoutAuth(StoreAuthDTO storeAuthDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "getTakeoutAuth", JacksonUtils.writeValueAsString(storeAuthDTO),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public void orderOperate(Integer type, TakeoutOrderOperateDTO orderOperateDTO) {
                    log.error(HYSTRIX_PATTERN, "orderOperate", JacksonUtils.writeValueAsString(orderOperateDTO),
                            ThrowableExtUtils.asStringIfAbsent(cause));
                    throw new ServerException();
                }

                @Override
                public StoreAuthDTO geJdTakeoutAuth(StoreAuthDTO storeAuthDTO) {
                    log.error(HYSTRIX_PATTERN, "geJdTakeoutAuth", JacksonUtils.writeValueAsString(storeAuthDTO),
                            ThrowableExtUtils.asStringIfAbsent(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
