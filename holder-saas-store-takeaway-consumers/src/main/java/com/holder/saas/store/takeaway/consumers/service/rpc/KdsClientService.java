package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.kds.req.ItemBatchRefundReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemPrepareReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemRefundReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemUrgeReqDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className KdsClientService
 * @date 2018/09/30 11:41
 * @description 微信调用
 * @program holder-saas-store-trade
 */
@Component
@Deprecated
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = KdsClientService.FallBack.class)
public interface KdsClientService {

    @PostMapping("/kitchen_item/prepare")
    void prepare(@RequestBody ItemPrepareReqDTO itemPrepareReqDTO);

    @PostMapping("kitchen_item/refund")
    void refund(@RequestBody ItemBatchRefundReqDTO itemRefundReqDTO);

    @PostMapping("kitchen_item/urge")
    void urge(@RequestBody  ItemUrgeReqDTO itemUrgeReqDTO);

    @Component
    class FallBack implements FallbackFactory<KdsClientService> {
        private static final Logger logger = LoggerFactory.getLogger(KdsClientService.FallBack.class);

        @Override
        public KdsClientService create(Throwable throwable) {
            return new KdsClientService() {

                @Override
                public void prepare(ItemPrepareReqDTO itemPrepareReqDTO) {
                    logger.error("kds菜品入厨房异常，e={}", throwable.getMessage());
                    throw new ParameterException("kds菜品入厨房调用异常");
                }

                @Override
                public void refund(ItemBatchRefundReqDTO itemRefundReqDTO) {
                    logger.error("kds菜品出厨房异常，e={}", throwable.getMessage());
                    throw new ParameterException("kds菜品出厨房调用异常");
                }

                @Override
                public void urge(ItemUrgeReqDTO itemUrgeReqDTO) {
                    logger.error("kds菜品催菜异常，e={}", throwable.getMessage());
                    throw new ParameterException("kds菜品催菜调用异常");
                }
            };
        }
    }

}
