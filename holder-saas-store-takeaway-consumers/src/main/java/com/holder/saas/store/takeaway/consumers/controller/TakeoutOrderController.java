package com.holder.saas.store.takeaway.consumers.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import com.holder.saas.store.takeaway.consumers.mapstruct.OrderMapstruct;
import com.holder.saas.store.takeaway.consumers.service.ItemService;
import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holder.saas.store.takeaway.consumers.service.RocketMqService;
import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holder.saas.store.takeaway.consumers.utils.GPSConverterUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.OrderCallDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.*;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByTakeawayOrderRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 配送API提取到单独的Controller中
 * 自营外卖回调、配送平台回调不要放在consumer中，放到producer中用factory取实现
 */
@Slf4j
@RestController
@RequestMapping("/takeout")
@Api(tags = "外卖相关接口")
public class TakeoutOrderController {

    private final OrderService orderService;

    private DynamicHelper dynamicHelper;

    @Resource
    private ProducerFeignClient producerFeignClient;

    private final OrderMapstruct orderMapstruct;

    private final RocketMqService rocketMqService;

    private final ItemService itemService;

    @Autowired
    public TakeoutOrderController(OrderService orderService, DynamicHelper dynamicHelper,
                                  OrderMapstruct orderMapstruct, RocketMqService rocketMqService, ItemService itemService) {
        this.orderService = orderService;
        this.dynamicHelper = dynamicHelper;
        this.orderMapstruct = orderMapstruct;
        this.rocketMqService = rocketMqService;
        this.itemService = itemService;
    }

    @ApiOperation(value = "查询外卖订单列表", notes = "查询外卖订单列表")
    @PostMapping(value = "/list_order")
    public List<TakeoutOrderListDTO> listOrder(@RequestBody @Validated(TakeoutOrderDTO.ListOrderGroup.class)
                                               TakeoutOrderDTO takeoutOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询外卖订单列表：入参{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        return orderService.listOrder(takeoutOrderDTO);
    }

    @ApiOperation(value = "查询外卖订单列表", notes = "查询外卖订单列表")
    @PostMapping(value = "/page_order")
    public Page<TakeoutOrderListDTO> pageOrder(@RequestBody @Validated(TakeoutOrderDTO.ListOrderGroup.class)
                                               TakeoutOrderDTO takeoutOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询外卖订单分页列表：入参{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        return orderService.pageOrder(takeoutOrderDTO);
    }

    @ApiOperation(value = "查询外卖统计", notes = "查询外卖统计 统计待处理订单数量 + 订单异常状态为非初始状态数量")
    @PostMapping(value = "/count_order")
    public TakeoutOrderStatisticsRespDTO countOrder(@RequestBody @Validated(TakeoutOrderDTO.ListOrderGroup.class)
                                                    TakeoutOrderDTO takeoutOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询外卖统计：入参{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        return orderService.countOrder(takeoutOrderDTO);
    }

    @ApiOperation(value = "查询外卖统计", notes = "查询外卖统计 根据订单平台和状态分组查询")
    @PostMapping(value = "/statistics_order")
    public TakeoutOrderStatisticsRespDTO statisticsOrder(@RequestBody @Validated(TakeoutOrderDTO.ListOrderGroup.class)
                                                         TakeoutOrderDTO takeoutOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询外卖统计：入参{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        return orderService.statisticsOrder(takeoutOrderDTO);
    }

    /**
     * d
     * 调整单-分页查询外卖订单列表
     * 外卖展示近30天 待接单、配送中、已完成的订单数据
     *
     * @param query 关键字，门店guid，分页数据
     * @return 外卖订单列表
     */
    @ApiOperation(value = "调整单-分页查询外卖订单列表")
    @PostMapping(value = "/page_order_by_adjust")
    public Page<AdjustByTakeawayOrderRespDTO> pageOrderByAdjust(@RequestBody AdjustByOrderListQuery query) {
        if (log.isInfoEnabled()) {
            log.info("调整单-分页查询外卖订单列表 入参{}", JacksonUtils.writeValueAsString(query));
        }
        return orderService.pageOrderByAdjust(query);
    }

    @ApiOperation(value = "查询外卖订单详情", notes = "查询外卖订单详情")
    @PostMapping("/get_order")
    public TakeoutOrderDTO getOrderDetail(@RequestBody @Validated(TakeoutOrderDTO.GetOrderDetailGroup.class)
                                          TakeoutOrderDTO takeoutOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询外卖订单详情：入参{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        return orderService.getOrder(takeoutOrderDTO);
    }

    @ApiOperation(value = "查询外卖订单详情", notes = "查询外卖订单详情")
    @GetMapping("/get_order/mapping/{orderGuid}")
    public TakeoutOrderDTO getOrderDetailMapping(@PathVariable String orderGuid) {
        if (log.isInfoEnabled()) {
            log.info("查询外卖订单详情(映射门店商品)：入参{}", orderGuid);
        }
        return orderService.getOrderDetailMapping(orderGuid);
    }

    @ApiOperation(value = "订单编号查询外卖订单详情", notes = "查询外卖订单详情")
    @GetMapping("/get_order_by_orderNo")
    public TakeoutOrderDTO getOrderByOrderNo(@RequestParam("orderNo") String orderNo,
                                             @RequestParam("storeGuid") String storeGuid) {
        log.info("订单编号查询外卖订单详情(映射门店商品),orderNo={},storeGuid={}", orderNo, storeGuid);
        return orderService.getOrderByOrderNo(orderNo, storeGuid);
    }

    @ApiOperation(value = "店家接单", notes = "店家接单")
    @PostMapping(value = "/accept_order")
    public void acceptOrder(@RequestBody @Validated(TakeoutOrderDTO.AcceptOrderGroup.class)
                            TakeoutOrderDTO takeawayOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("店家接单：入参{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        }
        orderService.acceptOrder(takeawayOrderDTO);
    }

    @ApiOperation(value = "店家取消订单", notes = "店家取消订单")
    @PostMapping(value = "/cancel_order")
    public void cancelOrder(@RequestBody @Validated(TakeoutOrderDTO.CancelOrderGroup.class)
                            TakeoutOrderDTO takeawayOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("店家取消订单：入参{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        }
        orderService.cancelOrder(takeawayOrderDTO);
    }

    @ApiOperation(value = "店家同意取消订单", notes = "店家同意取消订单")
    @PostMapping(value = "/agree_cancel_req")
    public String agreeCancelReq(@RequestBody @Validated(TakeoutOrderDTO.AgreeCancelOrderGroup.class)
                                 TakeoutOrderDTO takeawayOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("店家同意取消订单：入参{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        }
        return orderService.agreeCancelReq(takeawayOrderDTO);
    }

    @ApiOperation(value = "店家不同意取消订单", notes = "店家不同意取消订单")
    @PostMapping(value = "/disagree_cancel_req")
    public String disagreeCancelReq(@RequestBody @Validated(TakeoutOrderDTO.DisagreeCancelOrderGroup.class)
                                    TakeoutOrderDTO takeawayOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("店家不同意取消订单：入参{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        }
        return orderService.disagreeCancelReq(takeawayOrderDTO);
    }

    @ApiOperation(value = "店家同意退单", notes = "店家同意退单")
    @PostMapping(value = "/agree_refund_req")
    public String agreeRefundReq(@RequestBody @Validated(TakeoutOrderDTO.AgreeRefundOrderGroup.class)
                                 TakeoutOrderDTO takeawayOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("店家同意退单：入参{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        }
        return orderService.agreeRefundReq(takeawayOrderDTO);
    }

    @ApiOperation(value = "店家拒绝退单", notes = "店家拒绝退单")
    @PostMapping(value = "/disagree_refund_req")
    public String disagreeRefundReq(@RequestBody @Validated(TakeoutOrderDTO.DisagreeRefundOrderGroup.class)
                                    TakeoutOrderDTO takeawayOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("店家拒绝退单：入参{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        }
        return orderService.disagreeRefundReq(takeawayOrderDTO);
    }

    @ApiOperation(value = "店家回复催单", notes = "店家回复催单")
    @PostMapping(value = "/reply_remind_order")
    public void replyRemindOrder(@RequestBody @Validated TakeoutOrderDTO.OrderRemindDTO orderRemindDTO) {
        if (log.isInfoEnabled()) {
            log.info("店家回复催单：入参{}", JacksonUtils.writeValueAsString(orderRemindDTO));
        }
        orderService.replyRemindOrder(orderRemindDTO);
    }

    /**
     * 订单状态变更接口
     * 后续优化时，把这个方法合并到ownController里面的回调中，然后发送消息给consumers处理，变更订单状态。
     */
    @PostMapping(value = "/order_update")
    @ApiOperation(value = "自营外卖平台订单状态变更", notes = "自营外卖平台订单状态变更")
    public OwnCallbackResponse orderUpdate(@RequestBody SalesUpdateDTO salesUpdateDTO) {
        if (log.isInfoEnabled()) {
            log.info("(自营外卖平台)订单状态变更：{}", JacksonUtils.writeValueAsString(salesUpdateDTO));
        }
        HolderAuthDTO holderAuthDTO = producerFeignClient.getHolder(salesUpdateDTO.getStoreGuid());
        //切库
        String enterPriseGuid = holderAuthDTO.getEnterpriseGuid();
        log.info("enterPriseGuid={}", enterPriseGuid);
        dynamicHelper.changeDatasource(enterPriseGuid);

        try {
            //调用接口更改订单状态
            return orderService.orderUpdate(salesUpdateDTO);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("(自营外卖平台)自营外卖平台订单状态变更异常：{}", ThrowableUtils.asString(e));
            }
            return OwnCallbackResponse.DO_ERROR;
        }
    }

    /**
     * 查询配送方式
     */
    @PostMapping(value = "/get_distribution")
    @ApiOperation(value = "自营外卖平台查询配送方式", notes = "自营外卖平台查询配送方式")
    public List<OwnDistributionDTO> getDistribution(@RequestBody BaseDTO BaseDTO) {
        if (log.isInfoEnabled()) {
            log.info("(自营外卖平台)自营外卖平台查询配送方式：{}", JacksonUtils.writeValueAsString(BaseDTO));
        }
        return producerFeignClient.getDistribution(BaseDTO);
    }

    /**
     * 掌控者商家发起配送
     */
    @PostMapping(value = "/go_shipping")
    @ApiOperation(value = "商家发起配送", notes = "商家发起配送")
    public OwnCallbackResponse goShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        OwnCallbackResponse resp = new OwnCallbackResponse();
        if (log.isInfoEnabled()) {
            log.info("(自营外卖平台)商家发起配送：{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        String result = orderService.goShipping(takeoutOrderDTO);
        if ("10000".equals(result)) {
            return resp.SUCCESS;
        } else {
            return resp.GO_SHIPPING_ERROR;
        }
    }

    /**
     * 掌控者商家完成配送
     */
    @PostMapping(value = "/done_shipping")
    @ApiOperation(value = "商家完成配送", notes = "商家完成配送")
    public OwnCallbackResponse doneShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        OwnCallbackResponse resp = new OwnCallbackResponse();
        if (log.isInfoEnabled()) {
            log.info("(自营外卖平台)商家发起配送：{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        String result = orderService.doneShipping(takeoutOrderDTO);
        if ("10000".equals(result)) {
            return resp.SUCCESS;
        } else {
            return resp.DONE_SHIPPING_ERROR;
        }
    }

    /**
     * 掌控者商家取消配送
     */
    @PostMapping(value = "/cancel_shipping")
    @ApiOperation(value = "商家取消配送", notes = "商家取消配送")
    public OwnCallbackResponse cancelShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        OwnCallbackResponse resp = new OwnCallbackResponse();
        if (log.isInfoEnabled()) {
            log.info("(自营外卖平台)商家取消配送：{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        String result = orderService.cancelShipping(takeoutOrderDTO);
        if ("10000".equals(result)) {
            return resp.SUCCESS;
        } else {
            return resp.CANCEL_SHIPPING_ERROR;
        }
    }


    @PostMapping(value = "/delivery_change")
    @ApiOperation(value = "一城飞客（回调）订单状态发生变更", notes = "一城飞客订单状态发生变更")
    public void deliveryChange(@RequestBody TakeoutDeliveryChange takeoutDeliveryChange) {
        String orderId = takeoutDeliveryChange.getOrderId();
        log.info("一城飞客回调，订单[{}]状态发生变更,入参{}", orderId, JacksonUtils.writeValueAsString(takeoutDeliveryChange));
        //当一城飞客订单状态发生变更的时候，会调用这个接口，通过不同的订单配送状态，去通知美团/饿了么
        OrderDO orderDO = orderService.getOrderForHeShi(takeoutDeliveryChange.getOrderId());
        if (ObjectUtils.isEmpty(orderDO)) {
            log.info("查询不到该订单,[{}]", orderId);
            return;
        }
        UnOrder unOrder = orderMapstruct.toUnOrder(orderDO);
        //已分配骑手
        if (takeoutDeliveryChange.getOrderState() == DeliveryChangeEnum.骑手取餐.getCode()) {
            if (!StringUtils.isEmpty(takeoutDeliveryChange.getUserName())) {
                log.info("订单[{}]已分配骑手,骑手名:{},骑手电话:{}", orderId, takeoutDeliveryChange.getUserName(), takeoutDeliveryChange.getUserPhone());
                //更新骑手
                updateShipper(takeoutDeliveryChange, orderDO);
                unOrder.setShipperName(takeoutDeliveryChange.getUserName());
                unOrder.setShipperPhone(takeoutDeliveryChange.getUserPhone());
                unOrder.setReplyMsgType(UnOrderReplyMsgType.DELIVERY_KNIGHT_ACCEPT);
                producerFeignClient.reply(unOrder);
            }
            //骑手已取餐
        } else if (takeoutDeliveryChange.getOrderState() == DeliveryChangeEnum.已取餐.getCode()) {
            unOrder.setReplyMsgType(UnOrderReplyMsgType.DELIVERY_START);
            log.info("订单[{}]骑手已取餐,unOrder：{}", orderId, JacksonUtils.writeValueAsString(unOrder));
            producerFeignClient.reply(unOrder);

            //已完成配送
        } else if (takeoutDeliveryChange.getOrderState() == DeliveryChangeEnum.已完成.getCode()) {
            unOrder.setReplyMsgType(UnOrderReplyMsgType.DELIVERY_COMPLETE);
            log.info("订单[{}]已完成配送,unOrder：{}", orderId, JacksonUtils.writeValueAsString(unOrder));
            producerFeignClient.reply(unOrder);

            //已关闭配送
        } else if (takeoutDeliveryChange.getOrderState() == DeliveryChangeEnum.已关闭.getCode()) {
            unOrder.setReplyMsgType(UnOrderReplyMsgType.DELIVERY_CANCEL);
            log.info("订单[{}]已关闭配送,unOrder：{}", orderId, JacksonUtils.writeValueAsString(unOrder));
            producerFeignClient.reply(unOrder);

        } else {
            log.info("订单[{}]配送状态错误,OrderState={}", orderId, takeoutDeliveryChange.getOrderState());
        }

    }

    private void updateShipper(TakeoutDeliveryChange takeoutDeliveryChange, OrderDO orderDO) {
        if (!StringUtils.isEmpty(takeoutDeliveryChange.getUserName()) &&
                !StringUtils.isEmpty(takeoutDeliveryChange.getUserPhone())) {
            OrderDO orderShipper = new OrderDO();
            orderShipper.setId(orderDO.getId());
            //更新骑手姓名、电话
            orderShipper.setShipperName(takeoutDeliveryChange.getUserName());
            orderShipper.setShipperPhone(takeoutDeliveryChange.getUserPhone());
            orderService.updateById(orderShipper);
        }
    }

    /**
     * erp那边 action: SendWLUserInfo、SendWLUserLocation 都转发到这个接口
     *
     * @param takeoutDeliveryChange
     */
    @PostMapping(value = "/delivery_location")
    @ApiOperation(value = "一城飞客（回调）骑手定位信息", notes = "一城飞客订单骑手定位信息")
    public void deliveryLocation(@RequestBody TakeoutDeliveryChange takeoutDeliveryChange) {
        String orderId = takeoutDeliveryChange.getOrderId();
        log.info("一城飞客回调，订单入参[{}]骑手定位信息,入参{}", orderId, JacksonUtils.writeValueAsString(takeoutDeliveryChange));
        //当一城飞客订单状态发生变更的时候，会调用这个接口，通过不同的订单配送状态，去通知美团/饿了么
        OrderDO orderDO = orderService.getOrderForHeShi(takeoutDeliveryChange.getOrderId());
        if (ObjectUtils.isEmpty(orderDO)) {
            log.error("一城飞客回调，订单不存在");
            return;
        }
        //更新骑手
        updateShipper(takeoutDeliveryChange, orderDO);
        UnOrder unOrder = orderMapstruct.toUnOrder(orderDO);
        unOrder.setShipperName(takeoutDeliveryChange.getUserName());
        unOrder.setShipperPhone(takeoutDeliveryChange.getUserPhone());
        unOrder.setReplyMsgType(UnOrderReplyMsgType.DELIVERY_KNIGHT_LOCATION);
        //一城飞客为百度坐标需要转为火星坐标(美团、饿了吗都只支持火星坐标)
        if (!StringUtils.isEmpty(takeoutDeliveryChange.getLat()) && !StringUtils.isEmpty(takeoutDeliveryChange.getLng())) {
            final GPSConverterUtils.GPS gps =
                    GPSConverterUtils.bd09_To_Gcj02(Double.parseDouble(takeoutDeliveryChange.getLat()),
                            Double.parseDouble(takeoutDeliveryChange.getLng()));
            unOrder.setShipLatitude(String.valueOf(gps.getLat()));
            unOrder.setShipLongitude(String.valueOf(gps.getLon()));
        }
        log.info("一城飞客回调，订单数据：{}", JSON.toJSONString(unOrder));
        rocketMqService.sendUnOrder(unOrder);
    }

    /**
     * 调整单-查询外卖订单商品
     * 标记商品是否调整过
     *
     * @param query 订单guid，门店，交易模式
     * @return 订单商品信息列表
     */
    @ApiOperation(value = "调整单-查询外卖订单商品")
    @PostMapping("/list_order_item")
    public AdjustByOrderRespDTO listOrderItem(@RequestBody AdjustByOrderItemQuery query) {
        log.info("调整单-查询外卖订单商品 入参 query={}", JacksonUtils.writeValueAsString(query));
        return orderService.listOrderItem(query);
    }


    /**
     * 调整单-调整外卖单
     * 修改订单和订单明细上’是否调整‘字段
     */
    @ApiOperation(value = "调整单-调整外卖单")
    @PostMapping("/adjust_order")
    public void adjustOrder(@RequestBody @Valid AdjustTakeoutOrderReqDTO reqDTO) {
        log.info("调整单-调整外卖单 入参 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        orderService.adjustTakeoutOrder(reqDTO);
    }

    @ApiOperation(value = "更新订单的物流单号")
    @PostMapping("/update_third_carrier_id")
    public void updateThirdCarrierId(@RequestBody ThirdCarrierUpdateDTO carrierUpdate) {
        log.info("更新订单的物流单号 入参 carrierUpdate={}", JacksonUtils.writeValueAsString(carrierUpdate));
        if (ObjectUtil.isNull(carrierUpdate.getEnterpriseGuid())) {
            return;
        }
        dynamicHelper.changeDatasource(carrierUpdate.getEnterpriseGuid());
        UserContextUtils.putErp(carrierUpdate.getEnterpriseGuid());
        orderService.updateThirdCarrierId(carrierUpdate.getThirdCarrierId(), carrierUpdate.getOrderId());
    }


    @ApiOperation(value = "批量修改订单商品明细", notes = "批量修改订单商品明细")
    @PostMapping("/update_batch_item_problem")
    public void updateBatchItem(@RequestBody List<TakeoutOrderItemProblemDTO> problemDTOList) {
        itemService.updateBatchItem(problemDTOList);
    }

    /**
     * 外卖异常数据列表
     * 由于何师数据较多，直接查询没有索引性能较差，已弃用
     *
     * @param reqDTO 外卖异常数据列表请求
     * @return 外卖异常数据列表
     * @see com.holder.saas.store.takeaway.consumers.controller.TakeoutAbnormalDataController#page
     */
    @ApiOperation(value = "外卖异常数据列表")
    @PostMapping("/abnormal_data_page")
    public Page<TakeoutItemAbnormalDataRespDTO> pageAbnormalData(@RequestBody @Valid TakeoutItemAbnormalDataReqDTO reqDTO) {
        log.info("外卖异常数据列表,入参,reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        return itemService.pageAbnormalData(reqDTO);
    }

    /**
     * 数据修复列表
     *
     * @param reqDTO 数据修复列表请求
     * @return 数据修复列表
     */
    @ApiOperation(value = "数据修复列表")
    @PostMapping("/data_fix_list")
    public List<TakeoutItemDataFixRespDTO> listDataFix(@RequestBody @Valid TakeoutItemDataFixReqDTO reqDTO) {
        log.info("数据修复列表,入参,reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        return itemService.listDataFix(reqDTO);
    }

    /**
     * 轮询查询自动接单漏单订单
     */
    @ApiOperation(value = "轮询查询自动接单漏单订单")
    @PostMapping("/delay/auto_accept_order")
    public void delayAutoAcceptOrder(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        orderService.delayAutoAcceptOrder(takeoutOrderDTO);
    }


    @ApiOperation(value = "修改更新电信订单记录")
    @PostMapping("/updateCallOrder")
    public void updateCallOrder(@RequestBody OrderCallDTO orderCallDO) {
        orderService.updateCallOrder(orderCallDO);
    }

    /**
     * 商家终端点出餐
     */
    @PostMapping(value = "/prepared")
    public void orderPrepared(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("商家出餐：{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        orderService.orderPrepared(takeoutOrderDTO);
    }
}
