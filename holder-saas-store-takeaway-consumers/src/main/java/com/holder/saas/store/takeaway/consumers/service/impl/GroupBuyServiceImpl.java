package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.GroupBuyDO;
import com.holder.saas.store.takeaway.consumers.mapper.GroupBuyMapper;
import com.holder.saas.store.takeaway.consumers.service.*;
import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;

import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Slf4j
@Service
public class GroupBuyServiceImpl extends ServiceImpl<GroupBuyMapper, GroupBuyDO> implements GroupBuyService {

    private final ProducerFeignClient producerFeignClient;

    @Autowired
    public GroupBuyServiceImpl(ProducerFeignClient producerFeignClient) {
        this.producerFeignClient = producerFeignClient;
    }

    @Override
    public MtCouponDoCheckRespDTO checkTicket(CouPonReqDTO couPonReqDTO) {
        return producerFeignClient.checkTicket(couPonReqDTO);

    }

    @Override
    public MtCouponDoCheckRespDTO doCheck(CouPonReqDTO couPonReqDTO) {
        return producerFeignClient.doCheck(couPonReqDTO);

    }


    @Override
    public MtCouponPreRespDTO preCheck(CouPonPreReqDTO couPonPreReqDTO) {
        return producerFeignClient.preCheck(couPonPreReqDTO);

    }


    @Override
    public MtDelCouponRespDTO cancalTicket(CouponDelReqDTO couponDelReqDTO) {
        return producerFeignClient.cancelTicket(couponDelReqDTO);
    }

    @Override
    public MtCouponTradeDetailRespDTO queryGroupTradeDetail(CouPonReqDTO couPonReqDTO) {
        if (Objects.nonNull(couPonReqDTO.getGroupBuyType()) && GroupBuyTypeEnum.MEI_TUAN.getCode() != couPonReqDTO.getGroupBuyType()) {
            return null;
        }
        return producerFeignClient.queryGroupTradeDetail(couPonReqDTO);
    }
}
