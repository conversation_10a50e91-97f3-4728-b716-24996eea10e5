package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 外卖审核记录商品明细表
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hst_takeout_fix_item")
public class FixItemDO implements Serializable {

    private static final long serialVersionUID = 5604124680629664444L;


    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 审核记录id
     */
    private Long recordId;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名字
     */
    private String storeName;

    /**
     * 菜品来源：0=美团，1=饿了么
     *
     * @see com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType
     */
    private Integer orderSubType;

    /**
     * 外卖商品名称
     */
    private String takeoutItemName;

    /**
     * 外卖方商品唯一标识
     */
    private String thirdSkuId;

    /**
     * 门店商品guid
     */
    private String erpItemGuid;

    /**
     * 门店商品规格guid
     */
    private String erpItemSkuGuid;

    /**
     * 门店商品名称
     */
    private String erpItemName;

    /**
     * 门店商品单价
     */
    private BigDecimal erpItemPrice;

    /**
     * 门店商品核算单价
     */
    private BigDecimal takeawayAccountingPrice;

    /**
     * 门店映射数量
     */
    private BigDecimal erpItemCount;

    /**
     * 异常订单数
     */
    private Integer takeoutOrderCount;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @JsonIgnore
    public String getUniqueStoreAndSubType() {
        return storeGuid + ":" + orderSubType;
    }

    @JsonIgnore
    public String getUniqueStoreAndThirdSkuId() {
        return storeGuid + ":" + thirdSkuId;
    }

    @TableField(exist = false)
    @JsonIgnore
    private List<SkuInfoPkgDTO> listPkg;

}
