package com.holder.saas.store.takeaway.consumers.manage;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.consumers.entity.domain.*;
import com.holder.saas.store.takeaway.consumers.mapstruct.ItemPackageMapstruct;
import com.holder.saas.store.takeaway.consumers.service.AutoRecoveryService;
import com.holder.saas.store.takeaway.consumers.service.ItemService;
import com.holder.saas.store.takeaway.consumers.service.PackageService;
import com.holder.saas.store.takeaway.consumers.service.rpc.ItemFeignClient;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 外卖异常数据自动修复 业务层
 */
@Slf4j
@RequiredArgsConstructor
@Component("autoRecoveryManager")
public class AutoRecoveryManager {

    private final ItemService itemService;

    private final PackageService packageService;

    private final AutoRecoveryService autoRecoveryService;

    private final ItemFeignClient itemFeignClient;

    private final ItemPackageMapstruct itemPackageMapstruct;


    public void fixItem(List<AutoRecoveryDO> recoveryRecords) {
        // 查询原数据
        List<String> orderItemGuids = recoveryRecords.stream().map(AutoRecoveryDO::getId).collect(Collectors.toList());
        List<ItemDO> itemList = itemService.listByItemGuids(orderItemGuids);
        if (CollectionUtils.isEmpty(itemList)) {
            log.error("查询原数据为空,itemList:{}", JacksonUtils.writeValueAsString(orderItemGuids));
            return;
        }
        // 筛选 需要修复的数据
        itemList = verifyItem(itemList);
        if (CollectionUtils.isEmpty(itemList)) {
            autoRecoveryService.removeByIds(orderItemGuids);
            return;
        }
        log.info("开始修复外卖异常数据,size:{}", itemList.size());
        // 根据门店分组
        Map<String, List<ItemDO>> itemGroupByStoreGuidMap = itemList.stream().collect(Collectors.groupingBy(ItemDO::getStoreGuid));
        for (Map.Entry<String, List<ItemDO>> entry : itemGroupByStoreGuidMap.entrySet()) {
            findItemData(entry.getKey(), entry.getValue());
        }
        // 需要查询套餐商品明细是否入库
        List<PackageDO> packageList = findNotExistPackage(itemList);
        // 修复数据
        saveBatch(itemList, packageList, orderItemGuids);
    }

    public void findItemData(String storeGuid, List<ItemDO> itemList) {
        try {
            List<String> distinctSkuGuidList = itemList.stream().map(ItemDO::getErpItemSkuGuid).distinct().collect(Collectors.toList());
            // 适配老数据,根据skuGuid查询，无论是根据门店还是品牌商品SkuGuid
            List<SkuInfoRespDTO> parentSkuInfoList = itemFeignClient.findParentSKUS(distinctSkuGuidList);
            Map<String, SkuInfoRespDTO> parentSkuInfoMap = parentSkuInfoList.stream().collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key1));
            // 重新设置erpItemSkuGuid
            itemList.forEach(e -> {
                if (com.holderzone.framework.util.StringUtils.isEmpty(e.getErpItemSkuGuid())) {
                    return;
                }
                SkuInfoRespDTO parentSkuInfo = parentSkuInfoMap.get(e.getErpItemSkuGuid());
                if (Objects.isNull(parentSkuInfo)) {
                    return;
                }
                e.setErpItemSkuGuid(parentSkuInfo.getParentGuid());
            });
            // 查询对应模式下商品信息
            List<String> finalSkuGuidList = itemList.stream().map(ItemDO::getErpItemSkuGuid).distinct().collect(Collectors.toList());
            ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
            itemStringListDTO.setDataList(finalSkuGuidList);
            itemStringListDTO.setStoreGuid(storeGuid);
            List<SkuInfoRespDTO> skuInfoList = itemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO);
            log.info("根据门店模式查询商品返回={}", JacksonUtils.writeValueAsString(skuInfoList));

            Map<String, SkuInfoRespDTO> skuMap = skuInfoList.stream()
                    .collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key2));
            for (ItemDO itemDO : itemList) {
                SkuInfoRespDTO skuInfo = skuMap.get(itemDO.getErpItemSkuGuid());
                if (Objects.isNull(skuInfo)) {
                    itemDO.setErpItemSkuGuid(null);
                    continue;
                }
                itemDO.setErpItemName(skuInfo.getItemName());
                if (!com.holderzone.framework.util.StringUtils.isEmpty(skuInfo.getName())) {
                    itemDO.setErpItemName(itemDO.getErpItemName() + "(" + skuInfo.getName() + ")");
                }
                itemDO.setErpItemPrice(skuInfo.getSalePrice());
                //套餐商品明细
                itemDO.setListPkg(skuInfo.getListPkg());
                itemDO.setTakeawayAccountingPrice(skuInfo.getTakeawayAccountingPrice());
            }
        } catch (Exception e) {
            log.error("外卖修复数据查询item数据失败,e:{}", e.getMessage());
        }
    }

    /**
     * 查询是否套餐子商品是否入库
     */
    public List<PackageDO> findNotExistPackage(List<ItemDO> itemList) {
        List<String> takeoutItemGuids = itemList.stream().map(ItemDO::getItemGuid).collect(Collectors.toList());
        List<String> takeoutItemGuidsForDb = packageService.listByTakeoutItemGuids(takeoutItemGuids);
        List<PackageDO> packageList = Lists.newArrayList();
        for (ItemDO itemDO : itemList) {
            if (takeoutItemGuidsForDb.contains(itemDO.getItemGuid())) {
                itemDO.setListPkg(Lists.newArrayList());
            }
            if (CollectionUtil.isNotEmpty(itemDO.getListPkg())) {
                itemDO.getListPkg().forEach(v -> {
                    PackageDO fixPackageDO = itemPackageMapstruct.parseToPackageDO(v);
                    fixPackageDO.setTakeoutItemGuid(itemDO.getItemGuid());
                    fixPackageDO.setAccountingPrice(v.getTakeawayAccountingPrice());
                    packageList.add(fixPackageDO);
                });
            }
        }
        return packageList;
    }


    /**
     * 筛选 需要修复的数据
     */
    private List<ItemDO> verifyItem(List<ItemDO> itemList) {
        return itemList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getErpItemSkuGuid()) && StringUtils.isBlank(item.getErpItemName()))
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<ItemDO> itemList, List<PackageDO> packageList, List<String> orderItemIds) {
        // 修改外卖订单明细
        itemService.updateBatchById(itemList);
        // 修复套餐子商品数据
        packageService.saveBatch(packageList);
        // 清楚异常数据
        autoRecoveryService.removeByIds(orderItemIds);
    }

}
