package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.LogDO;
import com.holder.saas.store.takeaway.consumers.mapper.LogMapper;
import com.holder.saas.store.takeaway.consumers.service.LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 外卖订单服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
@Slf4j
@Service
public class LogServiceImpl extends ServiceImpl<LogMapper, LogDO> implements LogService {

}
