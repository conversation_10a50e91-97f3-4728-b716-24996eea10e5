package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * hst_takeout_package
 *
 * <AUTHOR>
@Data
@NoArgsConstructor
@TableName("hst_takeout_package")
public class PackageDO implements Serializable {

    private static final long serialVersionUID = 8234519516376275554L;

    /**
     *
     */
    private Long id;

    @TableLogic
    private Integer isDelete;

    /**
     * 外卖商品表对应itemguid
     */
    private String takeoutItemGuid;

    /**
     * 父商品guid
     */
    private String packageGuid;

    /**
     * 菜品外键
     */
    private String itemGuid;

    /**
     * 套餐菜品数
     */
    private BigDecimal itemCount;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品规格标识
     */
    private String skuGuid;

    /**
     * 计数单位
     */
    private String unit;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * 规格编号
     */
    private String code;

    /**
     * 售卖价格
     */
    private BigDecimal salePrice;

    /**
     * 核算价
     */
    private BigDecimal accountingPrice;

    /**
     * 套餐单位数
     */
    private BigDecimal itemUnitCount;

    /**
     * 增加数量
     */
    private BigDecimal riseAmount;

    /**
     *
     */
    private LocalDateTime gmtCreate;

    /**
     *
     */
    private LocalDateTime gmtModified;
}