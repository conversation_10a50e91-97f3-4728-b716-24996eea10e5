package com.holder.saas.store.takeaway.consumers.config;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketMqConfig
 * @date 2018/09/18 17:46
 * @description
 * @program holder-saas-store-takeaway
 */
public class RocketMqConfig {

    public static final String TAKEAWAY_PRODUCERS_ORDER_TOPIC = "takeaway-producers-order-topic";

    public static final String TAKEAWAY_PRODUCERS_ORDER_TAG = "takeaway-producers-order-tag";

    public static final String TAKEAWAY_ORDER_PRODUCERS_GROUP = "takeaway-order-producers-group";

    public static final String TAKEAWAY_CONSUMERS_ORDER_TOPIC = "takeaway-consumers-order-topic";

    public static final String TAKEAWAY_CONSUMERS_ORDER_TAG = "takeaway-consumers-order-tag";

    /**
     * 饿了么
     */

    public static final String TAKEAWAY_CONSUMERS_ELEM_ORDER_TOPIC = "takeaway-consumers-elem-order-topic";
    public static final String TAKEAWAY_CONSUMERS_ELEM_ORDER_TAG = "takeaway-consumers-elem-order-tag";

    /**
     * 美团
     */
    public static final String TAKEAWAY_CONSUMERS_MT_ORDER_TOPIC = "takeaway-consumers-mt-order-topic";
    public static final String TAKEAWAY_CONSUMERS_MT_ORDER_TAG = "takeaway-consumers-mt-order-tag";

    /**
     * 京东
     */
    public static final String TAKEAWAY_CONSUMERS_JD_ORDER_TOPIC = "takeaway-consumers-jd-order-topic";
    public static final String TAKEAWAY_CONSUMERS_JD_ORDER_TAG = "takeaway-consumers-jd-order-tag";

    public static final String TAKEAWAY_ORDER_CONSUMERS_GROUP = "takeaway-order-consumers-group";

    public static final String TAKEAWAY_ELEM_ORDER_CONSUMERS_GROUP = "takeaway-elem-order-consumers-group";

    public static final String TAKEAWAY_MT_ORDER_CONSUMERS_GROUP = "takeaway-mt-order-consumers-group";

    public static final String TAKEAWAY_JD_ORDER_CONSUMERS_GROUP = "takeaway-jd-order-consumers-group";

    public static final String TAKEAWAY_CONSUMERS_ORDER_TRADE_DETAIL_TOPIC = "takeaway-consumers-order-trade-detail-topic";

    public static final String TAKEAWAY_CONSUMERS_ORDER_TRADE_DETAIL_TAG = "takeaway-consumers-order-trade-detail-tag";

    public static final String TAKEAWAY_ORDER_TRADE_DETAIL_CONSUMERS_GROUP = "takeaway-order-trade-detail-consumers-group";

    public static final String PRINT_MESSAGE_TOPIC = "print-message-topic";

    public static final String PRINT_MESSAGE_TAG = "print-message-tag";

    public static final String MESSAGE_CONTEXT = "message-context";

    public static final String KDS_MESSAGE_TOPIC = "kds-message-topic";

    public static final String KDS_PREPARE_TAG = "kds-prepare-tag";

    public static final String ERP_MESSAGE_TOPIC = "erp-message-topic";

    public static final String ERP_REDUCE_TAG = "erp-reduce-tag";

    public static final String ERP_MESSAGE_GROUP = "erp-message-group";

    public static final String MDM_MESSAGE_TOPIC = "mdm-message-topic";

    public static final String MDM_REDUCE_TAG = "mdm-reduce-tag";

    public static final String MDM_MESSAGE_GROUP = "mdm-message-group";

    public static final String WEIHAI_ERP_MESSAGE_TOPIC = "weihai-erp-message-topic";

    public static final String WEIHAI_ERP_REDUCE_TAG = "weihai-erp-reduce-tag";

    public static final String WEIHAI_ERP_MESSAGE_GROUP = "weihai-erp-message-group";

    public static final String DISTRIBUTION_MESSAGE_TOPIC = "delivery-message-topic";

    public static final String DISTRIBUTION_START_TAG = "delivery-start-tag";

    public static final String DISTRIBUTION_MESSAGE_GROUP = "delivery-message-group";

    public static final String KDS_URGE_TAG = "kds-urge-tag";

    public static final String KDS_REFUND_TAG = "kds-refund-tag";

    public static final String USER_INFO = "userInfo";

    /**
     * 美团大众联名卡-新增会员
     */
    public static final String MT_CARD_MEMBER_TOPIC = "mt-card-member-topic";

    public static final String MT_CARD_MEMBER_TAG = "mt-card-member-tag";

    public static final String MT_CARD_MEMBER_GROUP = "mt-card-member-group";

    public static final String SYSTEM_MSG_TOPIC = "system-message-topic";

    public static final String SYSTEM_MSG_TAG = "system-message-tag";

}
