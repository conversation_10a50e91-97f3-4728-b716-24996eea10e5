package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RemindDO
 * @date 2018/08/30 14:46
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hst_takeout_remind")
public class RemindDO {

    private static final long serialVersionUID = 6085295134185403315L;

    /**
     * 主键ID
     */
    @TableId
    private long id;

    /**
     * 催单回复GUID
     */
    private String remindGuid;

    /**
     * 订单GUID
     */
    private String orderGuid;

    /**
     * 催单回复ID
     */
    private String remindId;

    /**
     * 是否已回复催单
     */
    @TableField("is_replied")
    private <PERSON><PERSON><PERSON> replied;

    /**
     * 催单回复内容
     */
    private String replyContent;

    /**
     * 催单回复员工GUID
     */
    private String replyStaffGuid;

    /**
     * 催单回复员工名字
     */
    private String replyStaffName;

    /**
     * 催单时间
     */
    private LocalDateTime remindTime;

    /**
     * 催单回复时间
     */
    private LocalDateTime replyTime;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;
}
