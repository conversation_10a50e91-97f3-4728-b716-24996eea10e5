package com.holder.saas.store.takeaway.consumers.service;

import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.orderlog.OrderLogMqDTO;
import com.holderzone.saas.store.dto.takeaway.UnOrder;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketMqService
 * @date 2018/09/18 20:45
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
public interface RocketMqService {

    void sendUnOrder(UnOrder unOrder);

    void sendUnOrderLog(OrderLogMqDTO orderLogMqDTO, String enterpriseGuid);

    void sendBusinessMessage(BusinessMessageDTO businessMessage);
}
