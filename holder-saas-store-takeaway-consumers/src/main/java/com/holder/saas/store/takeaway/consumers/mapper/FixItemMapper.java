package com.holder.saas.store.takeaway.consumers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * 外卖审核记录商品明细 Mapper 接口
 * </p>
 */
public interface FixItemMapper extends BaseMapper<FixItemDO> {

    List<String> queryStoreGuids(@Param("recordId") Long recordId);
}
