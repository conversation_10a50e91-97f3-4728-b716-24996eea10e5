package com.holder.saas.store.takeaway.consumers.mapstruct;

import com.holder.saas.store.takeaway.consumers.entity.domain.*;
import com.holder.saas.store.takeaway.consumers.entity.query.HandoverPayQuery;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByTakeawayOrderRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface OrderMapstruct {

    OrderReadDO readToRead(OrderReadDO orderReadDO);

    OrderDO readToDO(OrderReadDO orderReadDO);

    OrderDO fromUnOrder(UnOrder unOrder);

    RemindDO fromUnRemind(UnRemind unRemind);

    List<RemindDO> fromUnRemind(List<UnRemind> unOrderReminds);

    ItemDO fromUnItem(UnItem unItem);

    List<ItemDO> fromUnItem(List<UnItem> unOrderItems);

    DiscountDO fromUnDiscount(UnDiscount unDiscount);

    List<DiscountDO> fromUnDiscount(List<UnDiscount> unDiscounts);

    UnOrder toUnOrder(OrderDO orderDO);

    UnOrder toUnOrder(OrderReadDO orderDO);

    UnRemind toUnRemind(RemindDO remindDO);

    List<UnRemind> toUnRemind(List<RemindDO> remindDO);

    UnItem toUnItem(ItemDO itemDO);

    List<UnItem> toUnItem(List<ItemDO> itemDO);

    UnDiscount toUnDiscount(DiscountDO discountDO);

    List<UnDiscount> toUnDiscount(List<DiscountDO> discountDO);

    OrderDO fromTakeoutOrder(TakeoutOrderDTO takeawayOrderDTO);

    RemindDO fromOrderRemindDTO(TakeoutOrderDTO.OrderRemindDTO orderRemindDTO);

    List<RemindDO> fromOrderRemindDTO(List<TakeoutOrderDTO.OrderRemindDTO> orderRemindDTO);

    TakeoutOrderDTO toTakeoutOrder(OrderDO orderDO);

    List<TakeoutOrderDTO> toTakeoutOrder(List<OrderDO> orderDO);

    TakeoutOrderDTO toTakeoutOrder(OrderReadDO orderReadDO);

    List<TakeoutOrderDTO> toTakeoutOrderFromReadDO(List<OrderReadDO> orderReadDO);

    TakeoutOrderDTO.OrderRemindDTO toOrderRemindDTO(RemindDO remindDO);

    List<TakeoutOrderDTO.OrderRemindDTO> toOrderRemindDTO(List<RemindDO> remindDO);

    TakeoutOrderDTO.OrderItemDTO toOrderItemDTO(ItemDO itemDO);

    List<TakeoutOrderDTO.OrderItemDTO> toOrderItemDTO(List<ItemDO> itemDO);

    TakeoutOrderDTO.OrderDiscountDTO toOrderDiscountDTO(DiscountDO discountDO);

    List<TakeoutOrderDTO.OrderDiscountDTO> toOrderDiscountDTO(List<DiscountDO> discountDO);

    TakeoutOrderDTO.OrderLogDTO toOrderLogDTO(LogDO logDO);

    List<TakeoutOrderDTO.OrderLogDTO> toOrderLogDTO(List<LogDO> logDO);

    @Mappings({
            @Mapping(source = "gmtCreate", target = "startTime"),
            @Mapping(source = "gmtModified", target = "endTime")
    })
    HandoverPayQuery fromHandoverPayQueryDTO(HandoverPayQueryDTO handoverPayQueryDTO);

    /**
     * 订单信息DO转B端订单明细DTO
     *
     * @param orderDO do
     * @return dto
     */
    @Mappings({
            @Mapping(source = "orderGuid", target = "guid"),
            @Mapping(source = "orderId", target = "orderNo"),
            @Mapping(target = "deliveryTime", ignore = true)
    })
    BusinessTakeoutOrderDetailRespDTO do2BusinessTakeoutOrderDTO(OrderDO orderDO);

    @Mappings({
            @Mapping(source = "itemGuid", target = "orderItemGuid"),
            @Mapping(source = "actualPrice", target = "refundPrice")
    })
    RefundItemDO fromItemDO(ItemDO itemDO);

    List<RefundItemDO> fromItemDO(List<ItemDO> itemDO);

    RefundItemDO fromUnRefundItem(UnRefundItem unRefundItem);

    List<RefundItemDO> fromUnRefundItem(List<UnRefundItem> unRefundItems);

    @Mappings({
            @Mapping(source = "total", target = "orderFee")
    })
    AdjustByTakeawayOrderRespDTO orderDO2AdjustOrder(OrderDO orderDO);

    List<AdjustByTakeawayOrderRespDTO> orderDOs2AdjustOrders(List<OrderDO> orderDO);
}
