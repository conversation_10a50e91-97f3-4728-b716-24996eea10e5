package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holder.saas.store.takeaway.consumers.utils.ThrowableExtUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessMessageService
 * @date 2018/09/27 11:44
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-store-business", fallbackFactory = BizManageFeignClient.ServiceFallBack.class)
public interface BizManageFeignClient {

    /**
     * 根据门店GUID、员工GUID查询营业日起始时间
     *
     * @return
     */
    @PostMapping("/handover/query_by_storeGuid_and_userGuid")
    HandoverRecordDTO queryHandoverRecord(@RequestParam("storeGuid") String storeGuid, @RequestParam("userGuid") String userGuid);

    @PostMapping("/storeConfig/query")
    StoreConfigDTO queryStoreConfig(@RequestBody StoreConfigQueryDTO storeConfigQueryDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<BizManageFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public BizManageFeignClient create(Throwable throwable) {
            return new BizManageFeignClient() {
                @Override
                public HandoverRecordDTO queryHandoverRecord(String storeGuid, String userGuid) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "查询交接班", "storeGuid=" + storeGuid + "，userGuid=" + userGuid,
                                ThrowableExtUtils.asStringIfAbsent(throwable));
                    }
                    HandoverRecordDTO handoverRecordDTO = new HandoverRecordDTO();
                    handoverRecordDTO.setGmtCreate(DateTimeUtils.beginTimeOfDay(DateTimeUtils.now()));
                    return handoverRecordDTO;
                }

                @Override
                public StoreConfigDTO queryStoreConfig(StoreConfigQueryDTO storeConfigQueryDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "查询营业日", JacksonUtils.writeValueAsString(storeConfigQueryDTO),
                                ThrowableExtUtils.asStringIfAbsent(throwable));
                    }
                    StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
                    storeConfigDTO.setBusinessStartTime(LocalTime.MIN);
                    storeConfigDTO.setBusinessEndTime(LocalTime.MAX);
                    return storeConfigDTO;
                }
            };
        }
    }
}