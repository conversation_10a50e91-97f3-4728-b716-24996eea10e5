package com.holder.saas.store.takeaway.consumers.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemBindExtendInfoDo;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemMappingDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.SkuMapDO;
import com.holder.saas.store.takeaway.consumers.entity.dto.TakeoutItemMappingEnum;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.manage.FixManager;
import com.holder.saas.store.takeaway.consumers.mapper.SkuMapMapper;
import com.holder.saas.store.takeaway.consumers.service.ItemBindExtendInfoService;
import com.holder.saas.store.takeaway.consumers.service.ItemMappingService;
import com.holder.saas.store.takeaway.consumers.service.MappingService;
import com.holder.saas.store.takeaway.consumers.service.rpc.ItemFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.KdsFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.OrganizationService;
import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.DistributedUtils;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdPointItemDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.QueryBrandDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemMappingReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemMappingRespDTO;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
public class MappingServiceImpl implements MappingService {

    private final ProducerFeignClient producerFeignClient;

    private final ItemFeignClient itemFeignClient;

    private final ExecutorService executorService;

    private final KdsFeignClient kdsFeignClient;

    private final ItemBindExtendInfoService itemBindExtendInfoService;

    private final SkuMapMapper skuMapMapper;

    private final ItemMappingService itemMappingService;

    private final OrganizationService organizationService;

    private final DynamicHelper dynamicHelper;

    @Resource
    @Lazy
    private FixManager fixManager;

    @Qualifier("storeProductQueryThreadPool")
    @Autowired
    private ExecutorService storeProductQueryThreadPool;

    @Qualifier("batchBindProductThreadPool")
    @Autowired
    private ExecutorService batchBindProductThreadPool;

    @Autowired
    public MappingServiceImpl(ProducerFeignClient producerFeignClient, ItemFeignClient itemFeignClient,
                              ExecutorService executorService, KdsFeignClient kdsFeignClient,
                              ItemBindExtendInfoService itemBindExtendInfoService, SkuMapMapper skuMapMapper,
                              ItemMappingService itemMappingService, OrganizationService organizationService,
                              DynamicHelper dynamicHelper) {
        this.producerFeignClient = producerFeignClient;
        this.itemFeignClient = itemFeignClient;
        this.executorService = executorService;
        this.kdsFeignClient = kdsFeignClient;
        this.itemBindExtendInfoService = itemBindExtendInfoService;
        this.skuMapMapper = skuMapMapper;
        this.itemMappingService = itemMappingService;
        this.organizationService = organizationService;
        this.dynamicHelper = dynamicHelper;
    }


    @Override
    public TakeoutItemMappingRespDTO getOwnItemBinding(TakeoutItemMappingReqDTO takeoutItemMappingReqDTO) throws ExecutionException, InterruptedException {
        String jsonStr = UserContextUtils.getJsonStr();
        String storeGuid = takeoutItemMappingReqDTO.getStoreGuid();
        // 请求平台方商品
        List<UnMappedItem> itemList = producerFeignClient.getItem(storeGuid, 2);
        List<UnMappedType> unItemList = new ArrayList<>();
        UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemList(itemList);
        unItemList.add(unMappedType);

        // 请求ERP方商品
        FutureTask<List<MappingRespDTO>> erpItemTask = new FutureTask<>(() -> {
            UserContextUtils.put(jsonStr);
            ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
            itemSingleDTO.setData(storeGuid);
            return itemFeignClient.mappingAllItems(itemSingleDTO);
        });
        executorService.submit(erpItemTask);

        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        FutureTask<List<ItemBindExtendInfoDo>> extendInfoQuery = new FutureTask<>(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            return itemBindExtendInfoService.list(new LambdaQueryWrapper<ItemBindExtendInfoDo>()
                    .eq(ItemBindExtendInfoDo::getStoreGuid, storeGuid)
                    .eq(ItemBindExtendInfoDo::getTakeoutType, 2));
        });
        executorService.submit(extendInfoQuery);
        List<ItemBindExtendInfoDo> extendInfos = extendInfoQuery.get();
//        Map<String, ItemBindExtendInfoDo> erpExtendInfoMap = extendInfos.stream()
//                .collect(Collectors.toMap(ItemBindExtendInfoDo::getErpItemSkuId, i -> i));
        Map<String, ItemBindExtendInfoDo> unExtendInfoMap = extendInfos.stream()
                .collect(Collectors.toMap(ItemBindExtendInfoDo::getUnItemSkuId, i -> i));

        List<MappingRespDTO> erpItemList = erpItemTask.get();

        // 过滤掉不匹配的sku
        Map<String, MappingRespDTO> erpItemSkuIdentityMap =
                erpItemList.stream().collect(Collectors.toMap(MappingRespDTO::geteDishSkuCode, m -> m, (oldValue,
                                                                                                        newValue) -> newValue));
        // 为平台商品填充erp方字段
        Map<String, String> erp2UnItemSkuMap = new HashMap<>();
        for (UnMappedItem unMappedItem : itemList) {
            MappingRespDTO mappingRespDTO = erpItemSkuIdentityMap.get(unMappedItem.getErpItemSkuId());
            if (mappingRespDTO != null) {
                setUnMappedItem(unMappedItem, mappingRespDTO);
                erp2UnItemSkuMap.put(unMappedItem.getErpItemSkuId(), unMappedItem.getUnItemSkuId());
            }
            ItemBindExtendInfoDo extendInfo = unExtendInfoMap.get(unMappedItem.getErpItemSkuId());
            unMappedItem.setExtendInfoId(extendInfo == null ? null : extendInfo.getId());
            unMappedItem.setUnItemCountMapper(extendInfo == null ? 1 : extendInfo.getUnItemCountMapper());
        }

        // 把ERP商品一起返回给前端
        List<ErpMappingType> erpItemTypeList =
                erpItemList.stream().filter(e -> null != e.getIsChoice() && 1 == e.getIsChoice()).collect(groupingBy(o -> o.getCategoryId() + ":" + o.getCategoryName(), LinkedHashMap::new, toList())).entrySet().stream().map(stringListEntry -> {
                    ErpMappingType erpMappingType = new ErpMappingType();
                    String key = stringListEntry.getKey();
                    String categoryId = key.split(":")[0];
                    String categoryName = key.split(":")[1];
                    erpMappingType.setErpItemTypeId(categoryId);
                    erpMappingType.setErpItemTypeName(categoryName);
                    List<ErpMappingItem> erpMappingItemList = stringListEntry.getValue().stream().map(mappingRespDTO -> {
                        ErpMappingItem erpMappingItem = new ErpMappingItem();
                        erpMappingItem.setErpItemGuid(mappingRespDTO.geteDishCode());
                        erpMappingItem.setErpItemName(mappingRespDTO.getDishNameWithSpec());
                        erpMappingItem.setErpItemSkuId(mappingRespDTO.geteDishSkuCode());
                        erpMappingItem.setUnItemSkuId(erp2UnItemSkuMap.get(mappingRespDTO.geteDishSkuCode()));
                        return erpMappingItem;
                    }).collect(Collectors.toList());
                    erpMappingType.setErpItemList(erpMappingItemList);
                    return erpMappingType;
                }).collect(Collectors.toList());

        return new TakeoutItemMappingRespDTO().setUnItemTypeList(unItemList).setErpItemTypeList(erpItemTypeList);
    }


    @Override
    public TakeoutItemMappingRespDTO getItemBinding(TakeoutItemMappingReqDTO takeoutItemMappingReqDTO) throws ExecutionException, InterruptedException {
        //异步获取商品数据集合
        Triple<List<UnMappedItem>,List<MappingRespDTO>,List<ItemBindExtendInfoDo>> itemSource = asyncListTask(takeoutItemMappingReqDTO);
        boolean tripleEmpty = CollUtil.isEmpty(itemSource.getLeft()) && CollUtil.isEmpty(itemSource.getMiddle()) && CollUtil.isEmpty(itemSource.getRight());
        if(tripleEmpty) {
            throw new BusinessException("查询外卖商品失败，联系管理员");
        }
        List<UnMappedItem> unItemList = itemSource.getLeft();
        if (CollUtil.isEmpty(unItemList)) {
            throw new BusinessException("外卖平台未创建商品");
        }
        // 多对一绑定查询并重置
        itemMappingSet(takeoutItemMappingReqDTO,unItemList);
        // 饿了么替换映射数据
        if (takeoutItemMappingReqDTO.getTakeoutType() == OrderType.TakeoutSubType.ELE_TAKEOUT.getType()) {
            elemeReplaceErpItemSku(unItemList);
        }
        //构建分类分组商品
        List<UnMappedType> unItemTypeList = buildUnItemTypeList(takeoutItemMappingReqDTO, itemSource.getRight(),unItemList);
        // 过滤掉不匹配的sku并构建关联map
        Map<String, String> erpUnItemSkuMap = filterNotMatchItemAndBuildFillMap(unItemList, itemSource.getMiddle());
        //排序分类以及分类中的商品
        List<UnMappedType> sortedTypeList = sortedItemAndType(unItemTypeList);
        //构建系统商品
        List<ErpMappingType> erpItemTypeList = buildErpItemTypeList(itemSource.getMiddle(),erpUnItemSkuMap);
        //构建返回结果
        return buildItemBindingRsp(sortedTypeList,erpItemTypeList,takeoutItemMappingReqDTO.getStoreGuid());
    }

    @Override
    public TakeoutItemMappingRespDTO getJdItemBinding(TakeoutItemMappingReqDTO takeoutItemMappingReqDTO) {
        return null;
    }

    private TakeoutItemMappingRespDTO buildItemBindingRsp(List<UnMappedType> sortedTypeList, List<ErpMappingType> erpItemTypeList, String storeGuid) {
        long unBindCount = 0L;
        long invalidBindCount = 0L;
        if (CollectionUtils.isNotEmpty(sortedTypeList)) {
            List<UnMappedItem> unMappedItemList = sortedTypeList.stream().filter(e -> CollectionUtils.isNotEmpty(e.getUnItemList()))
                    .flatMap(e -> e.getUnItemList().stream()).collect(toList());
            unBindCount = unMappedItemList.stream().filter(e -> StringUtils.isEmpty(e.getErpItemSkuId())).count();
            invalidBindCount = unMappedItemList.stream().filter(e -> Objects.nonNull(e.getRealBindFlag())
                    && !e.getRealBindFlag() && !StringUtils.isEmpty(e.getErpItemSkuId())).count();
        }
        TakeoutItemMappingRespDTO respDTO = new TakeoutItemMappingRespDTO().setUnItemTypeList(sortedTypeList)
                .setErpItemTypeList(erpItemTypeList).setUnBindCount(unBindCount).setInvalidBindCount(invalidBindCount);
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        if (!ObjectUtils.isEmpty(brandDTO)) {
            respDTO.setSalesModel(brandDTO.getSalesModel());
        }
        return respDTO;
    }

    private List<ErpMappingType> buildErpItemTypeList(List<MappingRespDTO> erpItemList, Map<String, String> erpUnItemSkuMap) {
        return erpItemList.stream()
                .filter(erp -> Objects.nonNull(erp.getIsRack()) && erp.getIsRack() == 1 && null != erp.getIsChoice()
                        && erp.getIsChoice() == 1)
                .collect(groupingBy(o -> o.getCategoryId() + ":" + o.getCategoryName(), LinkedHashMap::new, toList()))
                .entrySet().stream().map(stringListEntry -> {
                    ErpMappingType erpMappingType = new ErpMappingType();
                    String key = stringListEntry.getKey();
                    String categoryId = key.split(":")[0];
                    String categoryName = key.split(":")[1];
                    erpMappingType.setErpItemTypeId(categoryId);
                    erpMappingType.setErpItemTypeName(categoryName);
                    List<ErpMappingItem> erpMappingItemList = stringListEntry.getValue().stream()
                            .map(mappingRespDTO -> {
                                ErpMappingItem erpMappingItem = new ErpMappingItem();
                                erpMappingItem.setErpItemGuid(mappingRespDTO.geteDishCode());
                                erpMappingItem.setErpItemName(mappingRespDTO.getDishNameWithSpec());
                                erpMappingItem.setErpItemSkuId(mappingRespDTO.getParentSkuGuid());
                                erpMappingItem.setUnItemSkuId(erpUnItemSkuMap.get(mappingRespDTO.getParentSkuGuid()));
                                erpMappingItem.setPlanItemName(mappingRespDTO.getPlanItemName());
                                return erpMappingItem;
                            }).collect(Collectors.toList());
                    erpMappingType.setErpItemList(erpMappingItemList);
                    return erpMappingType;
                }).collect(Collectors.toList());
    }

    private List<UnMappedType> sortedItemAndType(List<UnMappedType> unItemTypeList) {
        // 分类中排序: 未关联排在前，已关联但不存在排第二，已关联排最后
        Comparator<UnMappedItem> byBindFlag = Comparator.comparing(UnMappedItem::getBindingFlag);
        Comparator<UnMappedItem> byRealBindingFlag = Comparator.comparing(UnMappedItem::getRealBindingFlag);
        for (UnMappedType unMappedType : unItemTypeList) {
            if (CollUtil.isEmpty(unMappedType.getUnItemList())) {
                continue;
            }
            unMappedType.setUnItemList(unMappedType.getUnItemList().stream().sorted(byBindFlag.thenComparing(byRealBindingFlag)).collect(toList()));
        }
        //分类排序
        return unItemTypeList.stream().sorted(Comparator.comparing(UnMappedType::getBusinessIdentify).reversed()).collect(toList());
    }

    private Map<String, String> filterNotMatchItemAndBuildFillMap(List<UnMappedItem> unItemList, List<MappingRespDTO> erpItemList) {
        if(CollUtil.isEmpty(erpItemList)){
            log.info("ERP商品列表为空");
            return Maps.newHashMap();
        }
        
        // 构建映射关系
        Map<String, String> parentGuidMapping = buildParentGuidMapping(erpItemList);
        Map<String, List<MappingRespDTO>> erpItemSkuMap = buildErpItemSkuMap(erpItemList);
        List<String> kdsSkuList = buildKdsSkuList();
        
        // 处理每个未映射商品
        Map<String, String> erpUnItemSkuMap = Maps.newHashMap();
        for (UnMappedItem unMappedItem : unItemList) {
            if (StringUtils.isEmpty(unMappedItem.getErpItemSkuId())) {
                continue;
            }
            
            // 获取父级SKU并验证
            String skuParentGuid = parentGuidMapping.getOrDefault(unMappedItem.getErpItemSkuId(), unMappedItem.getErpItemSkuId());
            if (!erpItemSkuMap.containsKey(skuParentGuid)) {
                log.warn("门店商品需重新设置, unMappedItem={}", unMappedItem);
                unMappedItem.setErpItemSkuId(null);
                continue;
            }
            
            // 获取映射商品
            MappingRespDTO mappingResp = getMappingResp(unMappedItem, skuParentGuid, erpItemSkuMap);
            if (mappingResp == null) {
                continue;
            }
    
            // 设置商品属性
            setMappedItemProperties(unMappedItem, mappingResp, kdsSkuList);
            
            // 建立平台商品和系统商品的关联
            erpUnItemSkuMap.put(mappingResp.getParentSkuGuid(), unMappedItem.getUnItemSkuId());
        }
        
        return erpUnItemSkuMap;
    }
    
    // 构建父GUID映射关系
    private Map<String, String> buildParentGuidMapping(List<MappingRespDTO> erpItemList) {
        return erpItemList.stream()
            .collect(Collectors.toMap(
                MappingRespDTO::geteDishSkuCode,
                MappingRespDTO::getParentSkuGuid,
                (key1, key2) -> key1
            ));
    }
    
    // 构建ERP商品SKU映射
    private Map<String, List<MappingRespDTO>> buildErpItemSkuMap(List<MappingRespDTO> erpItemList) {
        return erpItemList.stream()
            .collect(groupingBy(MappingRespDTO::getParentSkuGuid));
    }
    
    // 构建KDS SKU列表
    private List<String> buildKdsSkuList() {
        return Optional.ofNullable(kdsFeignClient.queryAllBoundItem())
            .orElse(Collections.emptyList())
            .stream()
            .map(PrdPointItemDTO::getSkuGuid)
            .collect(toList());
    }
    
    // 获取映射商品
    private MappingRespDTO getMappingResp(UnMappedItem unMappedItem, String skuParentGuid, 
        Map<String, List<MappingRespDTO>> erpItemSkuMap) {
        
        List<MappingRespDTO> mappingRespList = erpItemSkuMap.get(unMappedItem.getErpItemSkuId());
        if (CollUtil.isEmpty(mappingRespList)) {
            mappingRespList = erpItemSkuMap.get(skuParentGuid);
        }
        
        if (CollUtil.isEmpty(mappingRespList)) {
            return null;
        }
        
        return mappingRespList.stream()
            .filter(e -> Objects.nonNull(e.getIsChoice()) && e.getIsChoice() == 1)
            .findFirst()
            .orElse(mappingRespList.get(0));
    }
    
    // 设置映射商品属性
    private void setMappedItemProperties(UnMappedItem unMappedItem, MappingRespDTO mappingResp, List<String> kdsSkuList) {
        setUnMappedItem(unMappedItem, mappingResp);
        unMappedItem.setErpItemIsRack(mappingResp.getIsRack());
        unMappedItem.setErpItemIsKdsBindItem(
            !CollectionUtils.isEmpty(kdsSkuList) && kdsSkuList.contains(mappingResp.geteDishSkuCode())
        );
    }

    private void elemeReplaceErpItemSku(List<UnMappedItem> unItemList) {
        Map<String, UnMappedItem> unItemMap = new HashMap<>(unItemList.size());
        for (UnMappedItem unMappedItem : unItemList) {
            unItemMap.put(unMappedItem.getErpItemSkuId(), unMappedItem);
        }
        //替换映射前的编码
        final List<SkuMapDO> skuMapDOS =
                skuMapMapper.selectList(new LambdaQueryWrapper<SkuMapDO>().select(SkuMapDO::getGuid,
                        SkuMapDO::getSourceGuid).in(SkuMapDO::getGuid, unItemMap.keySet()));
        skuMapDOS.forEach(skuMapDO -> unItemMap.get(skuMapDO.getGuid()).setErpItemSkuId(skuMapDO.getSourceGuid()));

    }

    private void itemMappingSet(TakeoutItemMappingReqDTO req, List<UnMappedItem> unItemList) {
        List<String> erpItemSkuIdList = unItemList.stream().map(UnMappedItem::getErpItemSkuId).collect(toList());
        if (CollUtil.isEmpty(erpItemSkuIdList)) {
            return;
        }
        List<ItemMappingDO> itemMappingList = itemMappingService.list(new LambdaQueryWrapper<ItemMappingDO>()
                .eq(ItemMappingDO::getTakeoutType, req.getTakeoutType())
                .in(ItemMappingDO::getMapperGuid, erpItemSkuIdList)
                .eq(ItemMappingDO::getStoreGuid, req.getStoreGuid())
        );
        if (CollUtil.isEmpty(itemMappingList)) {
            return;
        }
        Map<String, ItemMappingDO> mappingItemMap = itemMappingList.stream().collect(Collectors.toMap(ItemMappingDO::getMapperGuid, i -> i));
        for (UnMappedItem unMappedItem : unItemList) {
            ItemMappingDO itemMappingDO = mappingItemMap.get(unMappedItem.getErpItemSkuId());
            if (ObjectUtils.isEmpty(itemMappingDO)) {
               continue;
            }
            unMappedItem.setErpItemSkuId(itemMappingDO.getErpItemSkuGuid());
        }
    }

    private Triple<List<UnMappedItem>, List<MappingRespDTO>, List<ItemBindExtendInfoDo>> asyncListTask(TakeoutItemMappingReqDTO req) {
        String context = UserContextUtils.getJsonStr();
        // 请求平台方商品
        FutureTask<List<UnMappedItem>> unItemTask = new FutureTask<>(() -> {
            UserContextUtils.put(context);
            if(TakeoutItemMappingEnum.JD_TAKEOUT.getType() == req.getTakeoutType()){
                BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(req.getStoreGuid());
                QueryBrandDTO queryBrandDTO = new QueryBrandDTO();
                queryBrandDTO.setBrandGuid(brandDTO.getGuid());
                queryBrandDTO.setStoreGuid(req.getStoreGuid());
                return producerFeignClient.getJdItem(queryBrandDTO);
            }
            return producerFeignClient.getItem(req.getStoreGuid(), req.getTakeoutType());
        });
        executorService.submit(unItemTask);

        // 请求ERP方商品
        FutureTask<List<MappingRespDTO>> erpItemTask = new FutureTask<>(() -> {
            UserContextUtils.put(context);
            ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
            itemSingleDTO.setData(req.getStoreGuid());
            // 普通+菜谱模式商品都返回，避免显示的时候误认为 未绑定
            return itemFeignClient.mappingAllItems(itemSingleDTO);
        });
        executorService.submit(erpItemTask);

        //查询外卖商品绑定的拓展信息
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        FutureTask<List<ItemBindExtendInfoDo>> extendInfoQuery = new FutureTask<>(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            return itemBindExtendInfoService.list(new LambdaQueryWrapper<ItemBindExtendInfoDo>()
                    .eq(ItemBindExtendInfoDo::getStoreGuid, req.getStoreGuid())
                    .eq(ItemBindExtendInfoDo::getTakeoutType, req.getTakeoutType()));
        });
        executorService.submit(extendInfoQuery);
        MutableTriple<List<UnMappedItem>, List<MappingRespDTO>, List<ItemBindExtendInfoDo>> itemSourceList = new MutableTriple<>();
        try {
            // 异步任务执行
            itemSourceList.setLeft(unItemTask.get());
            itemSourceList.setMiddle(erpItemTask.get());
            itemSourceList.setRight(extendInfoQuery.get());
        }catch (Exception e) {
            log.error("异步获取商品数据列表失败",e);
        }
        return itemSourceList;
    }

    private List<UnMappedType> buildUnItemTypeList(TakeoutItemMappingReqDTO request, List<ItemBindExtendInfoDo> extendInfos, List<UnMappedItem> unItemList) {

        // 平台方分类和商品关联
        Map<String, List<UnMappedItem>> typeMap =
                unItemList.stream().collect(Collectors.groupingBy(UnMappedItem::getUnItemTypeId));

        if(request.getTakeoutType() == TakeoutItemMappingEnum.MT_TAKEOUT.getType()
                || request.getTakeoutType() == TakeoutItemMappingEnum.JD_TAKEOUT.getType()) {
            List<UnMappedType> unItemTypeList = Lists.newArrayList();
            typeMap.forEach((k,v) ->{
                if(CollUtil.isEmpty(v)) {
                    return;
                }
                UnMappedType unMappedType = new UnMappedType();
                unMappedType.setUnItemTypeId(k);
                unMappedType.setUnItemTypeName(v.get(0).getUnItemTypeName());
                unMappedType.setBusinessIdentify(v.get(0).getBusinessIdentify());
                buildUnMappedItemCount(v, extendInfos);
                unMappedType.setUnItemList(v);
                unItemTypeList.add(unMappedType);
            });
            return unItemTypeList;
        }
        //若不是美团外卖查询第三方分类列表
        List<UnMappedType> unItemTypeList = producerFeignClient.getType(request.getStoreGuid(), request.getTakeoutType());
        for (UnMappedType unMappedType : unItemTypeList) {
            List<UnMappedItem> unMappedItems = typeMap.get(unMappedType.getUnItemTypeId());
            if(CollUtil.isEmpty(unMappedItems)) {
                continue;
            }
            buildUnMappedItemCount(unMappedItems, extendInfos);
            unMappedType.setUnItemList(unMappedItems);
        }
        return unItemTypeList;

    }

    private void buildUnMappedItemCount(List<UnMappedItem> unMappedItems, List<ItemBindExtendInfoDo> extendInfos) {
        Map<String, ItemBindExtendInfoDo> unExtendInfoMap = extendInfos.stream()
                .collect(Collectors.toMap(ItemBindExtendInfoDo::getUnItemSkuId, i -> i));
        if(CollUtil.isEmpty(unExtendInfoMap)){
            return;
        }
        //构建映射数量
        for (UnMappedItem unMappedItem : unMappedItems) {
            unMappedItem.setUnItemTypeName(null);
            ItemBindExtendInfoDo extendInfo = unExtendInfoMap.get(unMappedItem.getUnItemSkuId());
            unMappedItem.setExtendInfoId(extendInfo == null ? null : extendInfo.getId());
            unMappedItem.setUnItemCountMapper(extendInfo == null ? 1 : extendInfo.getUnItemCountMapper());
        }
    }

    @Override
    public TakeawayBatchMappingResult getItems(UnItemQueryReq unItemQueryReq) {
        StopWatch stopWatch = new StopWatch();
        // 查询门店商品 待优化
        CompletableFuture<Map<String, List<MappingRespDTO>>> mapCompletableFuture = queryStoreItems(unItemQueryReq.getStoreGuids());

        // 筛选名称放在 producer
        stopWatch.start("平台外卖商品查询");
        List<UnMappedItem> itemList = producerFeignClient.getItems(unItemQueryReq);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(itemList)) {
            log.warn("查询外卖平台商品列表为空，直接返回，入参:{}", JacksonUtils.writeValueAsString(unItemQueryReq));
            return TakeawayBatchMappingResult.buildEmptyResult(unItemQueryReq.getCurrentPage(), unItemQueryReq.getPageSize());
        }

        // 商品排序 把同门店同分类的放一起
        itemList = sortedItemList(itemList);

        stopWatch.start("门店商品查询");
        Map<String, List<MappingRespDTO>> storeItemMap = mapCompletableFuture.join();
        log.info("门店商品：{}", JacksonUtils.writeValueAsString(storeItemMap));
        stopWatch.stop();

        // 合并外卖平台和门店的商品信息
        stopWatch.start("合并外卖平台和门店的商品信息");
        unMappedItemAndStoreItemsHandler(itemList, storeItemMap, unItemQueryReq.getTakeoutType());
        stopWatch.stop();

        log.info(stopWatch.prettyPrint());
        // 清洗数据
        cleanUnMappedItemList(itemList);
        // 筛选是否绑定
        if (Objects.nonNull(unItemQueryReq.getBindingFlag()) && unItemQueryReq.getBindingFlag() != 0) {
            itemList = itemList.stream().filter(e -> unItemQueryReq.getBindingFlag().equals(e.getBindingFlag())).collect(Collectors.toList());
        }
        // 分页
        return transferPage(unItemQueryReq, itemList);
    }

    /**
     * 商品排序
     * 把同门店同分类的放一起
     */
    private List<UnMappedItem> sortedItemList(List<UnMappedItem> itemList) {
        return itemList.stream().sorted(Comparator.comparing(UnMappedItem::getSortHashCode)).sorted(Comparator.comparing(UnMappedItem::getBusinessIdentify).reversed()).collect(Collectors.toList());
    }

    /**
     * 商品列表分页
     */
    private TakeawayBatchMappingResult transferPage(UnItemQueryReq unItemQueryReq, List<UnMappedItem> itemList) {
        // 总条数
        int total = itemList.size();
        // 计算未绑定条数
        long unBindCount = itemList.stream().filter(e -> StringUtils.isEmpty(e.getErpItemNameWithSku())).count();
        // 计算已关联但门店不存在的商品条数
        long lossBindCount = itemList.stream().filter(e -> Boolean.FALSE.equals(e.getRealBindFlag())).count();

        List<UnMappedItem> itemLimitList = limitList(unItemQueryReq, itemList);
        Page<UnMappedItem> unMappedItemPage = new Page<>(unItemQueryReq.getCurrentPage(), unItemQueryReq.getPageSize(), total, itemLimitList);
        return TakeawayBatchMappingResult.buildSuccessResult(unBindCount, lossBindCount, unMappedItemPage);
    }

    /**
     * list 分页
     */
    private List<UnMappedItem> limitList(UnItemQueryReq unItemQueryReq, List<UnMappedItem> itemList) {
        int startLimit = (unItemQueryReq.getCurrentPage() - 1) * unItemQueryReq.getPageSize();
        return itemList.stream()
                .skip(Long.parseLong(String.valueOf(startLimit)))
                .limit(unItemQueryReq.getPageSize()).collect(toList());
    }

    /**
     * 清洗数据
     */
    private void cleanUnMappedItemList(List<UnMappedItem> itemList) {
        // 如果没有映射商品， 则将映射数量清除
        itemList.forEach(e -> {
            if (StringUtils.isEmpty(e.getErpItemNameWithSku())) {
                e.setUnItemCountMapper(null);
                e.setErpItemSkuId(null);
                e.setErpItemIsRack(null);
            }
        });
    }


    /**
     * 批量查询门店商品列表
     */
    private CompletableFuture<Map<String, List<MappingRespDTO>>> queryStoreItems(List<String> storeGuids) {
        String jsonStr = UserContextUtils.getJsonStr();
        return CompletableFuture.supplyAsync(() -> {
            UserContextUtils.put(jsonStr);
            ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
            itemSingleDTO.setStoreGuids(storeGuids);
            // 普通+菜谱模式商品都返回，避免显示的时候误认为 未绑定
            return itemFeignClient.batchMappingAllItems(itemSingleDTO);
        }, storeProductQueryThreadPool).exceptionally(e -> {
            log.error("查询门店商品数据失败,e :{}", e.getMessage());
            throw new BusinessException("查询门店商品数据失败");
        });
    }


    /**
     * 合并外卖平台和门店的商品信息
     */
    private void unMappedItemAndStoreItemsHandler(List<UnMappedItem> unItemList, Map<String, List<MappingRespDTO>> storeItemMap,
                                                  Integer takeoutType) {
        if (MapUtils.isEmpty(storeItemMap)) {
            log.warn("门店商品为空，直接返回");
            return;
        }
        // 多对一处理
        itemMappingHandler(unItemList, takeoutType);
        // 饿了么映射处理
        eleItemMappingHandler(unItemList, takeoutType);
        // 映射数量处理
        itemMappingCountHandler(unItemList, takeoutType);

        // 商品信息处理
        Map<String, List<UnMappedItem>> unItemMap = unItemList.stream().collect(groupingBy(UnMappedItem::getErpStoreGuid));
        for (Map.Entry<String, List<UnMappedItem>> entry : unItemMap.entrySet()) {
            List<MappingRespDTO> storeItemList = storeItemMap.get(entry.getKey());
            if (CollectionUtils.isEmpty(storeItemList)) {
                continue;
            }
            mergeUnMappedItemAndStoreItems(entry.getValue(), storeItemList);
        }
    }

    /**
     * 平台外卖商品 和 门店商品 多对一处理
     */
    private void itemMappingHandler(List<UnMappedItem> unItemList, Integer takeoutType) {
        // 外卖平台方的映射id
        List<String> erpItemSkuIdList = unItemList.stream()
                .map(UnMappedItem::getErpItemSkuId)
                .collect(toList());
        // 门店guids
        List<String> erpStoreGuidList = unItemList.stream().map(UnMappedItem::getErpStoreGuid).collect(toList());
        // 查询映射关系
        List<ItemMappingDO> itemMappingList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(erpItemSkuIdList)) {
            itemMappingList = itemMappingService.list(new LambdaQueryWrapper<ItemMappingDO>()
                    .eq(ItemMappingDO::getTakeoutType, takeoutType)
                    .in(ItemMappingDO::getMapperGuid, erpItemSkuIdList)
                    .in(ItemMappingDO::getStoreGuid, erpStoreGuidList)
            );
        }
        if (CollectionUtils.isEmpty(itemMappingList)) {
            return;
        }
        // 根据门店进行分组
        Map<String, List<ItemMappingDO>> itemMappingGroupByStoreGuidMap = itemMappingList.stream().collect(groupingBy(ItemMappingDO::getStoreGuid));
        for (UnMappedItem unMappedItem : unItemList) {
            List<ItemMappingDO> itemMappingByStoreGuidList = itemMappingGroupByStoreGuidMap.get(unMappedItem.getErpStoreGuid());
            if (CollectionUtils.isEmpty(itemMappingByStoreGuidList)) {
                continue;
            }
            ItemMappingDO itemMapping = itemMappingByStoreGuidList.stream()
                    .filter(e -> e.getMapperGuid().equals(unMappedItem.getErpItemSkuId()))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(itemMapping)) {
                unMappedItem.setErpItemSkuId(itemMapping.getErpItemSkuGuid());
            }
        }
    }

    /**
     * 饿了么映射处理
     */
    private void eleItemMappingHandler(List<UnMappedItem> unItemList, Integer takeoutType) {
        if (takeoutType != OrderType.TakeoutSubType.ELE_TAKEOUT.getType()) {
            return;
        }
        Map<String, UnMappedItem> unItemMap = unItemList.stream()
                .collect(Collectors.toMap(UnMappedItem::getErpItemSkuId, Function.identity(), (key1, key2) -> key1));
        // 替换映射前的编码
        final List<SkuMapDO> skuMapList =
                skuMapMapper.selectList(new LambdaQueryWrapper<SkuMapDO>()
                        .select(SkuMapDO::getGuid, SkuMapDO::getSourceGuid)
                        .in(SkuMapDO::getGuid, unItemMap.keySet()));
        skuMapList.forEach(skuMap -> unItemMap.get(skuMap.getGuid()).setErpItemSkuId(skuMap.getSourceGuid()));
    }

    /**
     * 映射数量处理
     */
    private void itemMappingCountHandler(List<UnMappedItem> unItemList, Integer takeoutType) {
        List<String> storeGuids = unItemList.stream().map(UnMappedItem::getErpStoreGuid).collect(toList());
        List<ItemBindExtendInfoDo> extendInfoList = itemBindExtendInfoService.list(new LambdaQueryWrapper<ItemBindExtendInfoDo>()
                .in(ItemBindExtendInfoDo::getStoreGuid, storeGuids)
                .eq(ItemBindExtendInfoDo::getTakeoutType, takeoutType));
        if (CollectionUtils.isEmpty(extendInfoList)) {
            return;
        }
        Map<String, ItemBindExtendInfoDo> unExtendInfoMap = extendInfoList.stream()
                .collect(Collectors.toMap(ItemBindExtendInfoDo::getUnItemSkuId, Function.identity(), (key1, key2) -> key1));
        for (UnMappedItem unMappedItem : unItemList) {
            ItemBindExtendInfoDo extendInfo = unExtendInfoMap.get(unMappedItem.getUnItemSkuId());
            unMappedItem.setExtendInfoId(extendInfo == null ? null : extendInfo.getId());
            unMappedItem.setUnItemCountMapper(extendInfo == null ? 1 : extendInfo.getUnItemCountMapper());
        }
    }

    /**
     * 合并外卖平台和门店的商品信息
     */
    private void mergeUnMappedItemAndStoreItems(List<UnMappedItem> unItemList, List<MappingRespDTO> erpItemList) {
        Map<String, List<MappingRespDTO>> erpItemSkuIdentityMap = erpItemList.stream()
                .collect(groupingBy(MappingRespDTO::getParentSkuGuid));

        Map<String, String> parentGuidMapping = erpItemList.stream()
                .collect(Collectors.toMap(MappingRespDTO::geteDishSkuCode, MappingRespDTO::getParentSkuGuid, (key1, key2) -> key2));

        Set<String> erpItemSkuSet = erpItemSkuIdentityMap.keySet();

        List<UnMappedItem> filterUnItemList = unItemList.stream().filter(e -> !StringUtils.isEmpty(e.getErpItemSkuId())).collect(toList());
        if (CollectionUtils.isEmpty(filterUnItemList)) {
            return;
        }
        for (UnMappedItem unMappedItem : filterUnItemList) {
            String skuParentGuid = parentGuidMapping.getOrDefault(unMappedItem.getErpItemSkuId(), unMappedItem.getErpItemSkuId());
            if (!erpItemSkuSet.contains(skuParentGuid)) {
                log.warn("unItemName：{} 对应的 erpItemSkuId: {} 不存在于门店: {}，需重新设置", unMappedItem.getUnItemNameWithSku(),
                        unMappedItem.getErpItemSkuId(), unMappedItem.getErpStoreGuid());
                unMappedItem.setErpItemSkuId(null);
            }
        }
        filterUnItemList = filterUnItemList.stream().filter(e -> !StringUtils.isEmpty(e.getErpItemSkuId())).collect(toList());
        if (CollectionUtils.isEmpty(filterUnItemList)) {
            return;
        }
        // 为平台商品填充erp方字段
        for (UnMappedItem unMappedItem : filterUnItemList) {
            List<MappingRespDTO> mappingRespList = erpItemSkuIdentityMap.get(unMappedItem.getErpItemSkuId());
            if (CollectionUtils.isEmpty(mappingRespList)) {
                String skuParentGuid = parentGuidMapping.getOrDefault(unMappedItem.getErpItemSkuId(), unMappedItem.getErpItemSkuId());
                mappingRespList = erpItemSkuIdentityMap.get(skuParentGuid);
            }
            MappingRespDTO mappingRespDTO = null;
            if (CollectionUtils.isNotEmpty(mappingRespList)) {
                mappingRespDTO = mappingRespList.stream()
                        .filter(e -> Objects.nonNull(e.getIsChoice()) && e.getIsChoice() == 1).findFirst().orElse(mappingRespList.get(0));
            }
            log.info("mappingRespDTO={}", JacksonUtils.writeValueAsString(mappingRespDTO));
            if (mappingRespDTO != null) {
                log.info("进入 mappingRespDTO != null");
                // 抽取了公共部分
                setUnMappedItem(unMappedItem, mappingRespDTO);
            }
        }
    }


    private void setUnMappedItem(UnMappedItem unMappedItem, MappingRespDTO mappingRespDTO) {
        unMappedItem.setErpItemId(mappingRespDTO.geteDishCode());
        unMappedItem.setErpItemName(mappingRespDTO.geteDishName());
        unMappedItem.setErpItemSkuName(mappingRespDTO.geteDishSkuName());
        unMappedItem.setErpItemNameWithSku(mappingRespDTO.getDishNameWithSpec());
        unMappedItem.setErpItemSkuUnit(mappingRespDTO.geteDishSkuUnit());
        unMappedItem.setErpItemTypeId(mappingRespDTO.getCategoryId());
        unMappedItem.setErpItemTypeName(mappingRespDTO.getCategoryName());
        unMappedItem.setErpItemIsRack(mappingRespDTO.getIsRack());
        unMappedItem.setRealBindFlag(false);
        if (Objects.nonNull(mappingRespDTO.getIsChoice()) && mappingRespDTO.getIsChoice() == 1) {
            unMappedItem.setRealBindFlag(true);
        }
    }

    @Override
    public void bindItem(UnItemBindUnbindReq unItemBindUnbindReq) {
        // 多对一映射
        constructMappingGuid(unItemBindUnbindReq);
        //饿了吗：老sku带-的做映射
        handleEleSkuMap(unItemBindUnbindReq);
        producerFeignClient.setMapping(unItemBindUnbindReq);
    }

    @Override
    public void batchBindItem(UnItemBatchUnbindReq unItemBatchUnbindReq) {
        log.info("映射处理开始:{}", JacksonUtils.writeValueAsString(unItemBatchUnbindReq));
        unItemBatchUnbindReq.getUnItemUnbindList().forEach(e -> {
            UnItemBindUnbindReq bindUnbindReq = new UnItemBindUnbindReq();
            BeanUtils.copyProperties(e, bindUnbindReq);
            bindUnbindReq.setStoreGuid(unItemBatchUnbindReq.getStoreGuid());
            bindUnbindReq.setTakeoutType(unItemBatchUnbindReq.getTakeoutType());

            // 多对一映射
            constructMappingGuid(bindUnbindReq);
            log.info("映射处理中:{}", JacksonUtils.writeValueAsString(bindUnbindReq));

            //饿了吗：老sku带-的做映射
            handleEleSkuMap(bindUnbindReq);

            // 重新赋值
            e.setErpItemSkuId(bindUnbindReq.getErpItemSkuId());
            e.setActualErpItemSkuId(bindUnbindReq.getActualErpItemSkuId());
        });
        log.info("映射处理结束:{}", JacksonUtils.writeValueAsString(unItemBatchUnbindReq));
        producerFeignClient.batchBindMapping(unItemBatchUnbindReq);
    }

    /**
     * 多对一映射
     *
     * @param unItemBindUnbindReq 绑定入参
     * @return 映射guid
     */
    private void constructMappingGuid(UnItemBindUnbindReq unItemBindUnbindReq) {
        ItemMappingDO mappingDO = itemMappingService.getOne(new LambdaQueryWrapper<ItemMappingDO>()
                .eq(ItemMappingDO::getTakeoutType, unItemBindUnbindReq.getTakeoutType())
                .eq(ItemMappingDO::getUnItemSkuId, unItemBindUnbindReq.getUnItemSkuId())
                .eq(ItemMappingDO::getStoreGuid, unItemBindUnbindReq.getStoreGuid())
        );
        String mapperGuid = "";
        if (ObjectUtils.isEmpty(mappingDO)) {
            /*List<ItemMappingDO> mappingDOList = itemMappingService.list(new LambdaQueryWrapper<ItemMappingDO>()
                    .eq(ItemMappingDO::getTakeoutType, unItemBindUnbindReq.getTakeoutType())
                    .eq(ItemMappingDO::getErpItemSkuGuid, unItemBindUnbindReq.getErpItemSkuId())
                    .eq(ItemMappingDO::getStoreGuid, unItemBindUnbindReq.getStoreGuid())
            );*/
            ItemMappingDO itemMappingDO = new ItemMappingDO();
            itemMappingDO.setTakeoutType(unItemBindUnbindReq.getTakeoutType());
            itemMappingDO.setStoreGuid(unItemBindUnbindReq.getStoreGuid());
            itemMappingDO.setErpItemSkuGuid(unItemBindUnbindReq.getErpItemSkuId());
            itemMappingDO.setUnItemSkuId(unItemBindUnbindReq.getUnItemSkuId());
            itemMappingDO.setMapperGuid(DistributedUtils.id());
            itemMappingService.save(itemMappingDO);
            mapperGuid = itemMappingDO.getMapperGuid();
        } else {
            mappingDO.setErpItemSkuGuid(unItemBindUnbindReq.getErpItemSkuId());
            mappingDO.setMapperGuid(DistributedUtils.id());
            itemMappingService.updateById(mappingDO);
            mapperGuid = mappingDO.getMapperGuid();
        }
        unItemBindUnbindReq.setActualErpItemSkuId(unItemBindUnbindReq.getErpItemSkuId());
        unItemBindUnbindReq.setErpItemSkuId(mapperGuid);
        // 添加映射数量关系
        UnMappedItem unMappedItem = new UnMappedItem();
        BeanUtils.copyProperties(unItemBindUnbindReq, unMappedItem);
        updateItemBindExtendInfo(unItemBindUnbindReq.getStoreGuid(), unItemBindUnbindReq.getTakeoutType(), unMappedItem);
    }

    private void handleEleSkuMap(UnItemBindUnbindReq unItemBindUnbindReq) {
        if (unItemBindUnbindReq.getTakeoutType() == OrderType.TakeoutSubType.ELE_TAKEOUT.getType()
                && unItemBindUnbindReq.getErpItemSkuId().contains("-")) {
            final String erpItemSkuId = unItemBindUnbindReq.getErpItemSkuId().replace("-", "");
            SkuMapDO skuMapDO = skuMapMapper.selectOne(new LambdaQueryWrapper<SkuMapDO>().eq(SkuMapDO::getGuid
                    , erpItemSkuId));
            if (skuMapDO == null) {
                skuMapDO = new SkuMapDO().setGuid(erpItemSkuId).setSourceGuid(unItemBindUnbindReq.getErpItemSkuId())
                        .setSourceType(unItemBindUnbindReq.getTakeoutType());
                skuMapMapper.insert(skuMapDO);
            }
            unItemBindUnbindReq.setErpItemSkuId(erpItemSkuId);
        }
    }

    @Override
    public void updateItemBindExtendInfo(String storeGuid, Integer takeoutType, UnMappedItem item) {
        // 处理为空的情况
        item.setUnItemCountMapper((Objects.isNull(item.getUnItemCountMapper()) || item.getUnItemCountMapper() == 0)
                ? 1 : item.getUnItemCountMapper());
        if (item.getExtendInfoId() == null) {
            //先通过门店guid、外卖类型和系统商品库规格ID查询是否存在数据,防止数据库唯一索引报错
            ItemBindExtendInfoDo existInfo = itemBindExtendInfoService.getOne(new LambdaQueryWrapper<ItemBindExtendInfoDo>()
                    //.eq(ItemBindExtendInfoDo::getErpItemSkuId, item.getErpItemSkuId())
                    .eq(ItemBindExtendInfoDo::getStoreGuid, storeGuid)
                    .eq(ItemBindExtendInfoDo::getTakeoutType, takeoutType)
                    .eq(ItemBindExtendInfoDo::getUnItemSkuId, item.getUnItemSkuId()));
            if (ObjectUtil.isNotNull(existInfo)) {
                // 编辑绑定信息
                log.info("更新商品拓展信息[{}], with data: {}", item.getExtendInfoId(), item.getUnItemCountMapper());
                itemBindExtendInfoService.update(new LambdaUpdateWrapper<ItemBindExtendInfoDo>()
                        .set(ItemBindExtendInfoDo::getErpItemSkuId, item.getErpItemSkuId())
                        .set(ItemBindExtendInfoDo::getUnItemCountMapper, item.getUnItemCountMapper())
                        .eq(ItemBindExtendInfoDo::getId, existInfo.getId()));
                return;
            }

            log.info("新增商品拓展信息 storeGuid={},takeoutType={},unItemCountMapper={}", storeGuid, takeoutType, item.getUnItemCountMapper());
            ItemBindExtendInfoDo infoDo = new ItemBindExtendInfoDo().setStoreGuid(storeGuid).setTakeoutType(takeoutType)
                    .setErpItemSkuId(item.getErpItemSkuId()).setUnItemCountMapper(item.getUnItemCountMapper())
                    .setUnItemSkuId(item.getUnItemSkuId());
            itemBindExtendInfoService.save(infoDo);
        } else {
            log.info("更新商品拓展信息[{}], with data: {}", item.getExtendInfoId(), item.getUnItemCountMapper());
            itemBindExtendInfoService.update(new LambdaUpdateWrapper<ItemBindExtendInfoDo>()
                    .set(ItemBindExtendInfoDo::getUnItemCountMapper, item.getUnItemCountMapper())
                    .set(ItemBindExtendInfoDo::getUnItemSkuId, item.getUnItemSkuId())
                    .eq(ItemBindExtendInfoDo::getId, item.getExtendInfoId()));
        }
    }

    /**
     * 已经不符合新的要求了
     * 外卖商品抵扣数量映射移除
     *
     * @param storeGuid     门店GUID
     * @param takeoutType   外卖类型 0-美团，1-饿了么，2-自营，3-赚餐
     * @param erpItemSkuIds 系统商品库商品 skuIds
     */
    @Override
    public void updateItemBindExtendInfoDel(String storeGuid, Integer takeoutType, List<String> erpItemSkuIds) {
        log.info("更新商品绑定拓展信息移除：入参 storeGuid: {}, takeoutType: {}, erpItemSkuIds: {}", storeGuid, takeoutType,
                JacksonUtils.writeValueAsString(erpItemSkuIds));
        itemBindExtendInfoService.remove(new LambdaQueryWrapper<ItemBindExtendInfoDo>()
                .eq(ItemBindExtendInfoDo::getStoreGuid, storeGuid)
                .eq(ItemBindExtendInfoDo::getTakeoutType, takeoutType)
                .in(ItemBindExtendInfoDo::getErpItemSkuId, erpItemSkuIds));
    }

    @Override
    public List<ItemBindExtendInfoDo> queryItemBindExtendInfo(String storeGuid, Integer takeoutType,
                                                              List<String> dishSkuList) {
        if (CollectionUtils.isNotEmpty(dishSkuList)) {
            return itemBindExtendInfoService.list(new LambdaQueryWrapper<ItemBindExtendInfoDo>()
                    .eq(ItemBindExtendInfoDo::getStoreGuid, storeGuid)
                    .eq(ItemBindExtendInfoDo::getTakeoutType, takeoutType)
                    .in(ItemBindExtendInfoDo::getUnItemSkuId, dishSkuList));
        } else {
            return itemBindExtendInfoService.list(new LambdaQueryWrapper<ItemBindExtendInfoDo>()
                    .eq(ItemBindExtendInfoDo::getStoreGuid, storeGuid)
                    .eq(ItemBindExtendInfoDo::getTakeoutType, takeoutType));
        }
    }


    @Override
    public void unbindItem(UnItemBindUnbindReq unItemBindUnbindReq) {
        Integer takeoutType = unItemBindUnbindReq.getTakeoutType();
        String storeGuid = unItemBindUnbindReq.getStoreGuid();
        String erpItemSkuId = unItemBindUnbindReq.getErpItemSkuId();

        // 多对一映射解绑处理
        mappingHandle(unItemBindUnbindReq);

        if (takeoutType == OrderType.TakeoutSubType.ELE_TAKEOUT.getType()) {
            final String newErpItemSkuId = erpItemSkuId.replace("-", "");
            unItemBindUnbindReq.setErpItemSkuId(newErpItemSkuId);
        }
        producerFeignClient.cancelMapping(unItemBindUnbindReq);

        // 更新商品绑定拓展信息移除
        log.info("解除商品映射 更新商品绑定拓展信息移除：入参 storeGuid: {}, takeoutType: {}, erpItemSkuId: {}", storeGuid,
                takeoutType, erpItemSkuId);
        itemBindExtendInfoService.remove(new LambdaQueryWrapper<ItemBindExtendInfoDo>()
                .eq(ItemBindExtendInfoDo::getStoreGuid, storeGuid)
                .eq(ItemBindExtendInfoDo::getTakeoutType, takeoutType)
                .eq(ItemBindExtendInfoDo::getUnItemSkuId, unItemBindUnbindReq.getUnItemSkuId())
        );
    }

    /**
     * 多对一映射解绑处理
     *
     * @param unItemBindUnbindReq 解绑入参
     */
    private void mappingHandle(UnItemBindUnbindReq unItemBindUnbindReq) {
        ItemMappingDO mappingDO = itemMappingService.getOne(new LambdaQueryWrapper<ItemMappingDO>()
                .eq(ItemMappingDO::getTakeoutType, unItemBindUnbindReq.getTakeoutType())
                .eq(ItemMappingDO::getMapperGuid, unItemBindUnbindReq.getErpItemSkuId())
                .eq(ItemMappingDO::getStoreGuid, unItemBindUnbindReq.getStoreGuid())
        );
        if (!ObjectUtils.isEmpty(mappingDO)) {
            unItemBindUnbindReq.setErpItemSkuId(mappingDO.getErpItemSkuGuid());
            itemMappingService.removeById(mappingDO.getId());
        }
    }

    @Override
    public void batchUnbindItem(UnItemBatchUnbindReq unItemBatchUnbindReq) {
        String storeGuid = unItemBatchUnbindReq.getStoreGuid();
        Integer takeoutType = unItemBatchUnbindReq.getTakeoutType();

        producerFeignClient.batchCancelMapping(unItemBatchUnbindReq);
        List<String> erpItemSkuIds = unItemBatchUnbindReq.getUnItemUnbindList().stream()
                .map(UnItemBaseMapReq::getErpItemSkuId)
                .collect(Collectors.toList());
        List<String> unItemSkuIds = unItemBatchUnbindReq.getUnItemUnbindList().stream()
                .map(UnItemBaseMapReq::getUnItemSkuId)
                .collect(toList());

        // 外卖商品抵扣数量映射移除
        log.info("批量删除商品映射 更新商品绑定拓展信息移除：入参 storeGuid: {}, takeoutType: {}, erpItemSkuIds: {}", storeGuid, takeoutType,
                JacksonUtils.writeValueAsString(erpItemSkuIds));
        List<ItemBindExtendInfoDo> infoDoList = itemBindExtendInfoService.list(new LambdaQueryWrapper<ItemBindExtendInfoDo>()
                .eq(ItemBindExtendInfoDo::getStoreGuid, storeGuid)
                .eq(ItemBindExtendInfoDo::getTakeoutType, takeoutType)
                .in(ItemBindExtendInfoDo::getErpItemSkuId, erpItemSkuIds));
        infoDoList.removeIf(i -> !unItemSkuIds.contains(i.getUnItemSkuId()));
        if (CollectionUtils.isNotEmpty(infoDoList)) {
            itemBindExtendInfoService.removeByIds(infoDoList.stream().map(ItemBindExtendInfoDo::getId).collect(Collectors.toSet()));
        }
    }

    /**
     * 没用没更新
     */
    @Override
    public void rollbackOrderItemCount(OrderReadDO orderReadDO) {
        List<ItemDO> arrayOfItem = orderReadDO.getArrayOfItem();
        if (orderReadDO.getOrderType() == null || orderReadDO.getOrderType() != 0 || arrayOfItem.size() == 0)
            return;
        log.info("外卖订单商品数量回滚{}： {}", orderReadDO.getOrderSubType(), JacksonUtils.writeValueAsString(orderReadDO));
        List<String> itemSkuGuidList = arrayOfItem.stream().map(ItemDO::getItemCode).collect(toList());
        List<ItemBindExtendInfoDo> extendInfos =
                itemBindExtendInfoService.list(new LambdaQueryWrapper<ItemBindExtendInfoDo>().eq(ItemBindExtendInfoDo::getStoreGuid, orderReadDO.getStoreGuid()).eq(ItemBindExtendInfoDo::getTakeoutType, orderReadDO.getOrderSubType()).in(ItemBindExtendInfoDo::getErpItemSkuId, itemSkuGuidList));
        if (extendInfos.size() == 0)
            return;
        Map<String, ItemBindExtendInfoDo> extendInfoMap =
                extendInfos.stream().collect(Collectors.toMap(ItemBindExtendInfoDo::getErpItemSkuId, i -> i));
        arrayOfItem.forEach(i -> {
            ItemBindExtendInfoDo extendInfoDo = extendInfoMap.get(i.getItemCode());
            i.setItemCount(i.getItemCount().divide(BigDecimal.valueOf(extendInfoDo.getUnItemCountMapper()),
                    BigDecimal.ROUND_HALF_UP));
            log.info("商品[{}-{}]数量回滚为[{}]", i.getItemName(), i.getItemSku(), i.getItemCount());
        });
    }

    /**
     * 没用没更新
     */
    @Override
    public void rollbackOrderItemCount(List<ItemDO> items, String storeGuid, Integer takeoutType) {
        if (items.size() == 0)
            return;
        log.info("外卖订单商品数量回滚{}-{}: {}", takeoutType, storeGuid, JacksonUtils.writeValueAsString(items));
        List<String> itemSkuGuidList = items.stream().map(ItemDO::getItemCode).collect(toList());
        List<ItemBindExtendInfoDo> extendInfos =
                itemBindExtendInfoService.list(new LambdaQueryWrapper<ItemBindExtendInfoDo>().eq(ItemBindExtendInfoDo::getStoreGuid, storeGuid).eq(ItemBindExtendInfoDo::getTakeoutType, takeoutType).in(ItemBindExtendInfoDo::getErpItemSkuId, itemSkuGuidList));
        if (extendInfos.size() == 0)
            return;
        Map<String, ItemBindExtendInfoDo> extendInfoMap =
                extendInfos.stream().collect(Collectors.toMap(ItemBindExtendInfoDo::getErpItemSkuId, i -> i));
        items.forEach(i -> {
            ItemBindExtendInfoDo extendInfoDo = extendInfoMap.get(i.getItemCode());
            i.setItemCount(i.getItemCount().divide(BigDecimal.valueOf(extendInfoDo.getUnItemCountMapper()),
                    BigDecimal.ROUND_HALF_UP));
            log.info("商品[{}-{}]数量回滚为[{}]", i.getItemName(), i.getItemCode(), i.getItemCount());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncTcdItemMapping(TcdSyncItemMappingDTO itemMappingDTO) {
        List<TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping> tcdItemMappingList = itemMappingDTO.getStoreMappingList().stream()
                .flatMap(e -> e.getItemMappingList().stream()).collect(Collectors.toList());
        // 同步映射数量
        itemBindExtendInfoService.syncTcdItemMappingCount(tcdItemMappingList);
        // 同步映射关系
        itemMappingService.syncTcdItemMapping(tcdItemMappingList);
    }

    @Override
    public List<SkuMapDO> listSourceGuidsAndGuidsByGuids(List<String> guids) {
        return skuMapMapper.selectList(new LambdaQueryWrapper<SkuMapDO>().select(SkuMapDO::getGuid,
                SkuMapDO::getSourceGuid).in(SkuMapDO::getGuid, guids));
    }

    @Override
    public void batchBind(UnItemBatchBindUnbindReq unItemBatchBindUnbindReq) {
        List<UnItemBindUnbindReq> bindUnbindReqList = unItemBatchBindUnbindReq.getBindUnbindReqList();
        if (CollectionUtils.isEmpty(bindUnbindReqList)) {
            log.warn("批量绑定入参为空");
            return;
        }
        
        // 根据storeGuid和unItemSkuId去重，避免重复绑定
        bindUnbindReqList = bindUnbindReqList.stream()
                .filter(distinctByKey(req -> req.getStoreGuid() + "_" + req.getUnItemSkuId()))
                .collect(toList());
        
        if (CollectionUtils.isEmpty(bindUnbindReqList)) {
            log.warn("去重后批量绑定入参为空");
            return;
        }
        
        log.info("批量绑定商品映射，去重后数量: {}", bindUnbindReqList.size());
        
        int failNum;
        // 判断已选商品的门店中（菜谱、门店商品）是否有该商品，没有则绑定失败
        List<String> storeGuidList = bindUnbindReqList.stream()
                .map(UnItemBindUnbindReq::getStoreGuid)
                .distinct()
                .collect(toList());
        ItemSingleDTO singleDTO = new ItemSingleDTO();
        singleDTO.setData(unItemBatchBindUnbindReq.getBrandGuid());
        singleDTO.setStoreGuids(storeGuidList);
        singleDTO.setSkuGuids(Collections.singletonList(unItemBatchBindUnbindReq.getSkuGuid()));
        singleDTO.setItemList(Collections.singletonList(unItemBatchBindUnbindReq.getItemGuid()));
        List<String> existStoreList = itemFeignClient.queryItemStore(singleDTO);
        log.info("[查询商品是否所在门店]existStoreList={}", existStoreList);
        int allInNum = bindUnbindReqList.size();
        bindUnbindReqList.removeIf(b -> !existStoreList.contains(b.getStoreGuid()));
        failNum = allInNum - bindUnbindReqList.size();

        bindUnbindReqList.forEach(bindUnbindReq -> {
            bindUnbindReq.setErpItemGuid(unItemBatchBindUnbindReq.getItemGuid());
            bindUnbindReq.setErpItemSkuId(unItemBatchBindUnbindReq.getSkuGuid());
            bindUnbindReq.setUnItemCountMapper(unItemBatchBindUnbindReq.getUnItemCountMapper());
            bindUnbindReq.setTakeoutType(unItemBatchBindUnbindReq.getTakeoutType());
        });
        List<UnItemBindUnbindReq> unItemBindUnbindReqs = reqConverter(bindUnbindReqList);

        Map<String, List<UnItemBindUnbindReq>> storeItemMap = bindUnbindReqList.stream()
                .collect(Collectors.groupingBy(UnItemBindUnbindReq::getStoreGuid));
        List<CompletableFuture<Integer>> batchedBindItem = batchBindItem(storeItemMap, unItemBatchBindUnbindReq.getTakeoutType());
        CompletableFuture<Void> all = CompletableFuture.allOf(batchedBindItem.toArray(new CompletableFuture[0]));
        CompletableFuture<List<Integer>> allUser = all.thenApply(v -> batchedBindItem.stream().map(a -> {
            try {
                return a.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("异步返回失败数量", e);
                Thread.currentThread().interrupt();
            }
            return null;
        }).collect(Collectors.toList()));
        List<Integer> failedList = allUser.join();
        Integer failedNum = failedList.stream().reduce(0, Integer::sum);
        log.info("[异步返回失败数量]failedNum={}", failedNum);
        // 外卖映射异步批量修复
        unItemBindUnbindReqs.forEach(fix -> fixManager.autoFix(fix));
        failNum = failNum + failedNum;
        if (failNum > 0) {
            throw new BusinessException(failNum + "条绑定失败，门店商品异常");
        }
    }

    private List<UnItemBindUnbindReq> reqConverter(List<UnItemBindUnbindReq> bindUnbindReqList) {
        if (CollectionUtils.isEmpty(bindUnbindReqList)) {
            return new ArrayList<>();
        }
        List<UnItemBindUnbindReq> unItemBindUnbindReqs = new ArrayList<>();
        bindUnbindReqList.forEach(bind -> {
            UnItemBindUnbindReq req = new UnItemBindUnbindReq();
            req.setStoreGuid(bind.getStoreGuid());
            req.setTakeoutType(bind.getTakeoutType());
            req.setMtSkuId(bind.getMtSkuId());
            req.setUnItemId(bind.getUnItemId());
            req.setUnItemSkuId(bind.getUnItemSkuId());
            req.setUnItemTypeId(bind.getUnItemTypeId());
            req.setExtendValue(bind.getExtendValue());
            req.setErpItemGuid(bind.getErpItemGuid());
            req.setErpItemSkuId(bind.getErpItemSkuId());
            req.setActualErpItemSkuId(bind.getActualErpItemSkuId());
            req.setUnItemCountMapper(bind.getUnItemCountMapper());
            unItemBindUnbindReqs.add(req);
        });
        return unItemBindUnbindReqs;
    }

    private List<CompletableFuture<Integer>> batchBindItem(Map<String, List<UnItemBindUnbindReq>> storeItemMap,
                                                           Integer takeoutType) {
        List<CompletableFuture<Integer>> futureList = new ArrayList<>();
        String jsonStr = UserContextUtils.getJsonStr();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        for (Map.Entry<String, List<UnItemBindUnbindReq>> mapEntry : storeItemMap.entrySet()) {
            CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
                UserContextUtils.put(jsonStr);
                dynamicHelper.changeDatasource(enterpriseGuid);
                String storeGuid = mapEntry.getKey();
                List<UnItemBindUnbindReq> bindReqList = mapEntry.getValue();
                UnItemBatchUnbindReq bindReq = new UnItemBatchUnbindReq();
                bindReq.setStoreGuid(storeGuid);
                bindReq.setTakeoutType(takeoutType);
                bindReq.setUnItemUnbindList(new ArrayList<>(bindReqList));
                bindReq.setBindFlag(Boolean.TRUE);
                this.batchBindItem(bindReq);
                return 0;
            }, batchBindProductThreadPool).exceptionally(e -> {
                log.error("[外卖商品绑定]失败,e :{}", e.getMessage());
                return 1;
            });
            futureList.add(future);
        }
        return futureList;
    }

    @Override
    public void batchUnbind(UnItemBatchBindUnbindReq batchBindUnbindReq) {
        List<UnItemBindUnbindReq> bindUnbindReqList = batchBindUnbindReq.getBindUnbindReqList();
        if (CollectionUtils.isEmpty(bindUnbindReqList)) {
            log.warn("批量解绑入参为空");
            return;
        }
        Map<String, List<UnItemBindUnbindReq>> storeItemMap = bindUnbindReqList.stream()
                .collect(Collectors.groupingBy(UnItemBindUnbindReq::getStoreGuid));
        for (Map.Entry<String, List<UnItemBindUnbindReq>> mapEntry : storeItemMap.entrySet()) {
            String storeGuid = mapEntry.getKey();
            List<UnItemBindUnbindReq> unbindReqList = mapEntry.getValue();
            Map<Integer, List<UnItemBindUnbindReq>> takeoutTypeMap = unbindReqList.stream()
                    .collect(Collectors.groupingBy(UnItemBindUnbindReq::getTakeoutType));
            for (Map.Entry<Integer, List<UnItemBindUnbindReq>> entry : takeoutTypeMap.entrySet()) {
                Integer takeoutType = entry.getKey();
                List<UnItemBindUnbindReq> bindItemList = entry.getValue();
                UnItemBatchUnbindReq bindReq = new UnItemBatchUnbindReq();
                bindReq.setTakeoutType(takeoutType);
                bindReq.setStoreGuid(storeGuid);
                bindReq.setBindFlag(Boolean.FALSE);
                bindReq.setUnItemUnbindList(new ArrayList<>(bindItemList));
                this.batchUnbindItem(bindReq);
            }
        }
    }

    /**
     * Stream去重辅助方法
     * 根据指定的key函数进行去重
     * 
     * @param keyExtractor key提取函数
     * @param <T> 泛型类型
     * @return 去重谓词
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }
}
