package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessMessageService
 * @date 2018/09/27 11:44
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-cloud-enterprise", fallbackFactory = CloudEnterpriseFeignClient.ServiceFallBack.class)
public interface CloudEnterpriseFeignClient {

    /**
     * 根据门店GUID、员工GUID查询营业日起始时间
     *
     * @return
     */
    @GetMapping("/enterprise/hasEnterprise")
    Boolean hasEnterprise(@RequestParam("enterpriseGuid") String enterpriseGuid);


    @ApiOperation(value = "根据主键查询单个企业", notes = "uid、企业guid、企业电话必传其一（其他值无效）")
    @PostMapping("/enterprise/find")
    EnterpriseDTO findEnterprise(@RequestBody EnterpriseQueryDTO query);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<CloudEnterpriseFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}";

        @Override
        public CloudEnterpriseFeignClient create(Throwable throwable) {
            return new CloudEnterpriseFeignClient() {
                /**
                 * 根据门店GUID、员工GUID查询营业日起始时间
                 *
                 * @param enterpriseGuid
                 * @return
                 */
                @Override
                public Boolean hasEnterprise(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/enterprise/hasEnterprise", enterpriseGuid, throwable);
                    return Boolean.TRUE;
                }

                @Override
                public EnterpriseDTO findEnterprise(EnterpriseQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "findEnterprise", JacksonUtils.writeValueAsString(query),
                            throwable);
                    throw new ServerException();
                }
            };
        }
    }
}