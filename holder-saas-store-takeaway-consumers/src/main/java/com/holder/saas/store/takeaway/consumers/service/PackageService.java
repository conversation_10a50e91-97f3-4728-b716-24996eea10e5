package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.PackageDO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;

import java.util.List;

/**
 * <p>
 * 外卖菜品套餐服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
public interface PackageService extends IService<PackageDO> {

    List<String> listByTakeoutItemGuids(List<String> takeoutItemGuids);

    void updateByTakeoutItem(List<ItemDO> fixItemBuffer, List<SkuInfoPkgDTO> skuInfoPkgList);

}
