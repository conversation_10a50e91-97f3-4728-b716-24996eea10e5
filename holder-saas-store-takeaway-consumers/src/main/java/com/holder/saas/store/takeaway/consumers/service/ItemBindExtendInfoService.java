package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemBindExtendInfoDo;
import com.holderzone.saas.store.dto.takeaway.TcdSyncItemMappingDTO;

import java.util.List;

/**
 * @ClassName: ItemBindExtendInfoService
 * @Description: TODO
 * @Author: CC
 * @Date 2021/4/27 14:30
 * @ModifyRecords: v1.0 new
 */
public interface ItemBindExtendInfoService extends IService<ItemBindExtendInfoDo> {

    /**
     * 查询该门店下该外卖平台的绑定映射关系
     */
    List<ItemBindExtendInfoDo> selectByStoreGuidsAndTakeoutType(List<String> storeGuids, Integer takeoutType);

    /**
     * 赚餐同步商品映射数量
     */
    void syncTcdItemMappingCount(List<TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping> tcdItemMappingList);

}
