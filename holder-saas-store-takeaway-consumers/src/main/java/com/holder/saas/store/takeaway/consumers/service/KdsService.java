package com.holder.saas.store.takeaway.consumers.service;

import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;

import java.util.Map;

public interface KdsService {

    void prepare(String platformName, OrderReadDO orderReadDO,
                 Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO);

    void urge(OrderReadDO orderReadDO);

    void refund(OrderReadDO orderReadDO, Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO);
}
