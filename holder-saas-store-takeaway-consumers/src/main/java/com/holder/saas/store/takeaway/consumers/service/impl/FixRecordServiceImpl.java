package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixRecordDO;
import com.holder.saas.store.takeaway.consumers.helper.PageAdapter;
import com.holder.saas.store.takeaway.consumers.mapper.FixRecordMapper;
import com.holder.saas.store.takeaway.consumers.service.FixRecordService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixRecordDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutRecordQueryDTO;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 外卖修复审核记录服务实现类
 * </p>
 */
@Service
public class FixRecordServiceImpl extends ServiceImpl<FixRecordMapper, FixRecordDO> implements FixRecordService {

    @Override
    public Page<TakeoutFixRecordDTO> pageInfo(TakeoutRecordQueryDTO queryDTO) {
        IPage<TakeoutFixRecordDTO> page = baseMapper.pageInfo(new PageAdapter<>(queryDTO), queryDTO);
        return new PageAdapter<>(page, page.getRecords());
    }
}
