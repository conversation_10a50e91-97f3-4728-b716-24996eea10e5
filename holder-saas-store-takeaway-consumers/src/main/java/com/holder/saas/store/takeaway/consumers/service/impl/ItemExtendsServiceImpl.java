package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemExtendsDO;
import com.holder.saas.store.takeaway.consumers.mapper.ItemExtendsMapper;
import com.holder.saas.store.takeaway.consumers.service.ItemExtendsService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 外卖菜品扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Service
public class ItemExtendsServiceImpl extends ServiceImpl<ItemExtendsMapper, ItemExtendsDO> implements ItemExtendsService {

    @Override
    public BigDecimal getTotalCostPrice(List<String> orderGuidList) {
        if (CollectionUtils.isEmpty(orderGuidList)) {
            return BigDecimal.ZERO;
        }
        return baseMapper.getTotalCostPrice(orderGuidList);
    }
}
