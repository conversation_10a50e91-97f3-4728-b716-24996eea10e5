package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holder.saas.store.takeaway.consumers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemFeignClient
 * @date 2018/09/04 11:49
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-store-item", fallbackFactory = ItemFeignClient.ServiceFallBack.class)
public interface ItemFeignClient {

    /**
     * 搜索sku
     */
    @PostMapping("/item_sku/list_sku_info")
    List<SkuInfoRespDTO> listSkuInfo(@RequestBody List<String> skuGuidList);

    /**
     * 查询ERP可用商品
     *
     * @param itemSingleDTO
     * @return
     */
    @PostMapping("/item/mapping")
    List<MappingRespDTO> mapping(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 查询ERP可用商品(普通模式+菜谱模式)
     *
     * @param itemSingleDTO
     * @return
     */
    @PostMapping("/item/mapping_all_Items")
    List<MappingRespDTO> mappingAllItems(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 查询ERP可用商品(普通模式+菜谱模式)
     *
     * @param itemSingleDTO
     * @return
     */
    @PostMapping("/item/batch_mapping_all_Items")
    Map<String, List<MappingRespDTO>> batchMappingAllItems(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 查询菜品类型
     * Require(skuGuid,itemGuid,typeGuid)
     * Optional(itemName,skuName)
     *
     * @param itemStringListDTO
     * @return
     */
    @PostMapping("/item/selectSkuInfo")
    List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOList(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 查询菜品类型
     * Require(skuGuid,itemGuid,typeGuid)
     * Optional(itemName,skuName)
     *
     * @param itemStringListDTO
     * @return
     */
    @PostMapping("/item/selectSkuInfo/v2")
    List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOListV2(@RequestBody ItemStringListDTO itemStringListDTO);


    /**
     * 根据门店当前模式查询规格商品信息
     *
     * @param itemStringListDTO
     * @return
     */
    @PostMapping("/item/list_sku_info/by_mode")
    List<SkuInfoRespDTO> listSkuInfoByRecipeMode(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 根据套餐商品查询套餐明细
     *
     * @param itemGuidList 套餐商品guid
     * @return 套餐明细
     */
    @PostMapping("/item/list_pkg_info/by_item")
    List<SkuInfoPkgDTO> listPkgInfoByItemGuid(@RequestBody List<String> itemGuidList);

    /**
     * 通过sku查询parentSku
     */
    @PostMapping("/item/parent_sku")
    List<SkuInfoRespDTO> findParentSKUS(@RequestBody List<String> skuGuids);


    /**
     * @param skuGuid 根据skuGuid
     * @return SkuInfoRespDTO
     */
    @GetMapping("/item_sku/info")
    SkuInfoRespDTO info(@RequestParam String skuGuid);

    /**
     * 根据商品guid获取商品及套餐信息
     *
     * @param itemStringListDTO 商品guid
     * @return 商品及套餐信息
     */
    @PostMapping("/item_pkg/list_pkg_item_info")
    List<ItemInfoRespDTO> listPkgItemInfo(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 查询规格详情列表
     */
    @PostMapping("/item/list_sku_info")
    List<SkuTakeawayInfoRespDTO> listSkuInfoAndSub(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 查询所有门店外卖绑定的erp商品
     *
     * @param itemStringListDTO 门店guid列表
     * @return 所有门店外卖绑定的erp商品
     */
    @ApiOperation(value = "查询外卖绑定的erp商品")
    @PostMapping("/item/query_erp_item/by_stores")
    List<StoreItemListRespDTO> queryErpItemByStoreGuids(@RequestBody ItemStringListDTO itemStringListDTO);

    @ApiOperation(value = "根据商品guid获取门店商品详情列表（区分销售模式）")
    @PostMapping("/item/list_item_info_by_sales_model")
    List<ItemInfoRespDTO> listItemInfoBySalesModel(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 查询商品是否所在门店
     *
     * @param itemSingleDTO 门店、品牌、商品
     * @return 存在的门店
     */
    @ApiOperation(value = "查询商品是否所在门店")
    @PostMapping("/item/query_item_store")
    List<String> queryItemStore(@RequestBody ItemSingleDTO itemSingleDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ItemFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ItemFeignClient create(Throwable cause) {
            return new ItemFeignClient() {

                @Override
                public List<SkuInfoRespDTO> listSkuInfo(List<String> skuGuidList) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "listSkuInfo",
                                JacksonUtils.writeValueAsString(skuGuidList), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<MappingRespDTO> mapping(ItemSingleDTO itemSingleDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "mapping",
                                JacksonUtils.writeValueAsString(itemSingleDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<MappingRespDTO> mappingAllItems(ItemSingleDTO itemSingleDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "mapping",
                                JacksonUtils.writeValueAsString(itemSingleDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public Map<String, List<MappingRespDTO>> batchMappingAllItems(ItemSingleDTO itemSingleDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "batchMappingAllItems",
                                JacksonUtils.writeValueAsString(itemSingleDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOList(ItemStringListDTO itemStringListDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "selectSkuTakeawayInfoRespDTOList",
                                JacksonUtils.writeValueAsString(itemStringListDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOListV2(ItemStringListDTO itemStringListDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "selectSkuTakeawayInfoRespDTOListV2",
                                JacksonUtils.writeValueAsString(itemStringListDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<SkuInfoRespDTO> listSkuInfoByRecipeMode(ItemStringListDTO itemStringListDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "listSkuInfoByRecipeMode",
                                JacksonUtils.writeValueAsString(itemStringListDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<SkuInfoPkgDTO> listPkgInfoByItemGuid(List<String> itemGuidList) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "listPkgInfoByItemGuid",
                                JacksonUtils.writeValueAsString(itemGuidList), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<SkuInfoRespDTO> findParentSKUS(List<String> skuGuids) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "findParentSKUS",
                                JacksonUtils.writeValueAsString(skuGuids), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public SkuInfoRespDTO info(String skuGuid) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "info",
                                skuGuid, ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<ItemInfoRespDTO> listPkgItemInfo(ItemStringListDTO itemStringListDTO) {
                    log.error("获取商品及套餐信息，e={}", cause.getMessage());
                    throw new ParameterException("获取商品及套餐信息调用异常");
                }

                @Override
                public List<SkuTakeawayInfoRespDTO> listSkuInfoAndSub(ItemStringListDTO itemStringListDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "listSkuInfoAndSub",
                                JacksonUtils.writeValueAsString(itemStringListDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<StoreItemListRespDTO> queryErpItemByStoreGuids(ItemStringListDTO itemStringListDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "queryErpItemByStoreGuids",
                                JacksonUtils.writeValueAsString(itemStringListDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<ItemInfoRespDTO> listItemInfoBySalesModel(ItemStringListDTO itemStringListDTO) {
                    log.error(HYSTRIX_PATTERN, "listItemInfoBySalesModel", JacksonUtils.writeValueAsString(itemStringListDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<String> queryItemStore(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "queryItemStore", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

            };
        }
    }
}
