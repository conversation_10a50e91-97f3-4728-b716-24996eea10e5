package com.holder.saas.store.takeaway.consumers.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SwaggerConfig
 * @date 2018-08-06 11:26:18
 * @description
 * @program holder-saas-store-member
 */
@Configuration
public class SwaggerConfig {

    /**
     * 地址 ${address}/swagger-ui.html
     *
     * @return
     */
    @Bean
    public Docket testApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(true)
                .groupName("member")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.holder.saas.store.takeaway.consumers.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("saas平台外卖")
                .description("saas平台外卖中心")
                .version("1.0.0")
                .build();
    }
}
