package com.holder.saas.store.takeaway.consumers.service;

import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByTypeReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreUsedReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;

import java.util.List;

public interface AuthService {

    List<StoreAuthDTO> queryAuthByType(StoreAuthByTypeReqDTO storeAuthByTypeReqDTO);

    List<StoreAuthDTO> queryTakeoutAuthByStore(StoreAuthByStoreReqDTO storeAuthReqDTO);

    List<StoreAuthDTO> queryTuanGouByStore(StoreAuthByStoreReqDTO storeAuthReqDTO);

    List<StoreDTO> queryStoreUsed(StoreUsedReqDTO storeUsedReqDTO);

    Boolean updateDeliveryType(StoreAuthDTO storeAuthDTO);
}
