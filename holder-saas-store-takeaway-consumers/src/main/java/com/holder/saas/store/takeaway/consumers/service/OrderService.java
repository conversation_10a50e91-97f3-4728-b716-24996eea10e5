package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsQueryDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.ItemRespDTO;
import com.holderzone.saas.store.dto.takeaway.OwnCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderListDTO;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.BusinessTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.OrderCallDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutOrderStatisticsRespDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByTakeawayOrderRespDTO;

import java.util.List;

/**
 * <p>
 * 外卖订单服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
public interface OrderService extends IService<OrderDO> {

    /**
     * 查询外卖订单列表
     */
    List<TakeoutOrderListDTO> listOrder(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 查询外卖订单列表
     */
    Page<TakeoutOrderListDTO> pageOrder(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 查询外卖统计 统计待处理订单数量 + 订单异常状态为非初始状态数量
     */
    TakeoutOrderStatisticsRespDTO countOrder(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 查询外卖统计 根据订单平台和状态分组查询
     */
    TakeoutOrderStatisticsRespDTO statisticsOrder(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 查询外卖订单详情
     */
    TakeoutOrderDTO getOrder(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 查询外卖订单详情(外卖商品明细映射门店明细)
     */
    TakeoutOrderDTO getOrderDetailMapping(String orderGuid);

    /**
     * 创建订单
     */
    void orderCreate(UnOrder unOrder);

    /**
     * 保存订单(只用于自营外卖)
     */
    void orderOwnSave(UnOrder unOrder);


    /**
     * 店家接单
     */
    void acceptOrder(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 店家接单结果更新
     */
    void orderAccept(UnOrder unOrder);

    /**
     * 更新配送信息
     */
    void updateShipping(UnOrder unOrder);

    /**
     * 配送完成
     */
    void updateShippingCompleted(UnOrder unOrder);

    /**
     * 更新骑手接单消息
     */
    void updateShippingDistribute(UnOrder unOrder);

    /**
     * 掌控者商家发起配送
     *
     * @param takeoutOrderDTO
     * @return
     */
    String goShipping(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 掌控者商家完成配送
     *
     * @param takeoutOrderDTO
     * @return
     */
    String doneShipping(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 掌控者商家取消配送
     *
     * @param takeoutOrderDTO
     * @return
     */
    String cancelShipping(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 店家取消订单（含拒单）
     */
    void cancelOrder(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 订单被取消结果更新
     */
    void orderCanceledAsReject(UnOrder unOrder);

    /**
     * 订单被取消结果更新
     */
    void orderCanceled(UnOrder unOrder);

    /**
     * 处理取消订单请求
     */
    void cancelOrderReq(UnOrder unOrder);

    /**
     * 处理取消“取消订单请求”
     */
    void cancelCancelOrderReq(UnOrder unOrder);

    /**
     * 店家同意取消订单
     */
    String agreeCancelReq(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 取消订单请求已同意
     */
    void cancelReqAgreed(UnOrder unOrder);

    /**
     * 店家不同意取消订单
     */
    String disagreeCancelReq(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 取消订单请求已被拒绝
     */
    void cancelReqDisagreed(UnOrder unOrder);

    /**
     * 客服仲裁取消单申请有效
     */
    void cancelArbitrationEffective(UnOrder unOrder);

    /**
     * 订单已完成
     */
    void orderCompleted(UnOrder unOrder);

    /**
     * 处理退单请求
     */
    void refundOrderReq(UnOrder unOrder);

    /**
     * 处理取消“退单请求”
     */
    void cancelRefundOrderReq(UnOrder unOrder);

    /**
     * 店家同意退单
     */
    String agreeRefundReq(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 退单请求已被同意
     */
    void refundReqAgreed(UnOrder unOrder);

    /**
     * 店家不同意退单
     */
    String disagreeRefundReq(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 退单请求已被拒绝
     */
    void refundReqDisagreed(UnOrder unOrder);

    /**
     * 客服仲裁退单有效
     */
    void refundArbitrationEffective(UnOrder unOrder);

    /**
     * 用户催单
     */
    void orderRemindByUser(UnOrder unOrder);

    /**
     * 店家回复催单
     */
    void replyRemindOrder(TakeoutOrderDTO.OrderRemindDTO orderRemindDTO);


    /**
     * 配送方配送异常
     */
    void deliveryError(UnOrder unOrder);

    /**
     * 赚餐外卖出餐成功
     */
    void diningOutTcd(UnOrder unOrder);


    /**
     * 打印账单
     */
    String printBill(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 打印后厨菜单
     */
    String printKitchen(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 打印标签单
     */
    String printLabel(TakeoutOrderDTO takeoutOrderDTO);

    /**
     * 查询交易金额和交易数量
     */
    HandoverPayDTO getOrderMoney(HandoverPayQueryDTO handoverPayQueryDTO);

    /**
     * 查询营业概况
     */
    TakeoutStatsDTO getOpStats(TakeoutStatsQueryDTO takeoutStatsQueryDTO);

    List<ItemRespDTO> listTakeOutItemSale(DailyReqDTO request);

    List<ItemRespDTO> listTakeOutGoodsSale(DailyReqDTO request);

    /**
     * 查询收款统计
     */
    TakeoutStatsDTO getReceiptStats(TakeoutStatsQueryDTO takeoutStatsQueryDTO);

    /**
     * 查询用餐类型统计
     */
    TakeoutStatsDTO getTradeStats(TakeoutStatsQueryDTO takeoutStatsQueryDTO);


    /**
     * 自营外卖平台专用
     */
    OwnCallbackResponse orderUpdate(SalesUpdateDTO salesUpdateDTO);

    /**
     * 查询单个订单——何师烧烤专用
     */
    OrderDO getOrderForHeShi(String OrderId);

    /**
     * 根据条件查询外卖订单列表
     *
     * @param reqDTO 查询条件
     * @return 订单列表数据
     */
    Page<BusinessTakeoutOrderRespDTO> getTakeoutOrderPage(BusinessTakeoutOrderReqDTO reqDTO);

    /**
     * 订单详细
     *
     * @param orderGuid 订单guid
     * @return 详细信息
     */
    BusinessTakeoutOrderDetailRespDTO getTakeoutOrderDetail(String orderGuid);

    /**
     * 调整单-分页查询外卖订单列表
     * 外卖展示近30天 待接单、配送中、已完成的订单数据
     *
     * @param query 关键字，门店guid，分页数据
     * @return 外卖订单列表
     */
    Page<AdjustByTakeawayOrderRespDTO> pageOrderByAdjust(AdjustByOrderListQuery query);

    /**
     * 调整单-查询外卖订单商品
     * 标记商品是否调整过
     *
     * @param query 订单guid，门店，交易模式
     * @return 订单商品信息列表
     */
    AdjustByOrderRespDTO listOrderItem(AdjustByOrderItemQuery query);

    /**
     * 编辑订单 ‘是否调整’字段为已调整
     *
     * @param orderGuid 订单guid
     */
    void updateBatchIsAdjustItem(String orderGuid);

    /**
     * 调整单-调整外卖单
     *
     * @param reqDTO 外卖订单guid 外卖订单明细guids
     */
    void adjustTakeoutOrder(AdjustTakeoutOrderReqDTO reqDTO);

    /**
     * 功能描述：更新订单的物流单号
     *
     * @param thirdCarrierId 物流单号
     * @param orderId        订单id
     * @date 2022/3/30
     */
    void updateThirdCarrierId(String thirdCarrierId, String orderId);

    /**
     * 设置为自动接单 但未接单处理
     */
    void delayAutoAcceptOrder(TakeoutOrderDTO takeoutOrderDTO);


    void updateCallOrder(OrderCallDTO orderCallDO);

    /**
     * 修改订单的退款状态
     */
    void updateOrderRefundStatus(String orderGuid, Integer refundStatus);

    TakeoutOrderDTO getOrderByOrderNo(String orderNo, String storeGuid);

    /**
     * 终端出餐
     * @param takeoutOrderDTO 入参
     */
    void orderPrepared(TakeoutOrderDTO takeoutOrderDTO);
}
