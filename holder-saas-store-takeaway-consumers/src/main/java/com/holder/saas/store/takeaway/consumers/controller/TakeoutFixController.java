package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.builder.FixItemBizBuilder;
import com.holder.saas.store.takeaway.consumers.entity.bo.FixItemBiz;
import com.holder.saas.store.takeaway.consumers.manage.FixManager;
import com.holder.saas.store.takeaway.consumers.service.FixItemService;
import com.holder.saas.store.takeaway.consumers.service.FixRecordService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixItemDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixRecordDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutRecordQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/takeout/fix")
@Api(description = "修复外卖绑定映射关系")
public class TakeoutFixController {

    @Autowired
    private FixRecordService fixRecordService;

    @Autowired
    private FixItemService fixItemService;

    @Autowired
    private FixManager fixManager;

    @ApiOperation(value = "修复审核列表")
    @PostMapping("/page")
    public Page<TakeoutFixRecordDTO> pageInfo(@RequestBody TakeoutRecordQueryDTO queryDTO) {
        return fixRecordService.pageInfo(queryDTO);
    }

    @ApiOperation(value = "查询审核记录详情")
    @GetMapping("/query")
    public List<TakeoutFixItemDTO> listByRecordId(Long recordId) {
        return fixItemService.listByRecordId(recordId);
    }

    @ApiOperation(value = "查询待修复商品列表")
    @GetMapping("/query/storeGuids")
    public List<String> queryStoreGuids(Long recordId) {
        return fixItemService.queryStoreGuids(recordId);
    }

    @ApiOperation(value = "修复数据")
    @PostMapping
    public void fix(@RequestBody @Valid TakeoutFixDTO takeoutFixDTO) {
        log.info("修复外卖绑定映射关系-修复入参：{}", JacksonUtils.writeValueAsString(takeoutFixDTO));
        FixItemBiz biz = FixItemBizBuilder.build(takeoutFixDTO);
        fixManager.fix(biz);
    }
}
