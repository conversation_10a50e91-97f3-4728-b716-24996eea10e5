package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.service.DistributedService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedServiceImpl
 * @date 2018/02/14 09:00
 * @description 分布式id服务实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class DistributedServiceImpl implements DistributedService {

    private static final String TAG_TAKEOUT_ORDER = "takeout/order";

    private static final String TAG_TAKEOUT_FIX_RECORD = "takeout/fix/record";

    private static final String TAG_TAKEOUT_FIX_ITEM = "takeout/fix/item";

    private final RedisTemplate redisTemplate;

    @Autowired
    public DistributedServiceImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Long rawId(String tag) {
        try {
            return BatchIdGenerator.getGuid(redisTemplate, tag);
        } catch (IOException e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public String nextId(String tag) {
        return String.valueOf(rawId(tag));
    }

    @Override
    public List<String> nextBatchId(String tag, long count) {
        return BatchIdGenerator.batchGetGuids(redisTemplate, tag, count)
                .stream().map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public String nextOrderGuid() {
        return nextId(TAG_TAKEOUT_ORDER);
    }

    @Override
    public List<String> nextBatchOrderGuid(long count) {
        return nextBatchId(TAG_TAKEOUT_ORDER, count);
    }

    @Override
    public String nextFixRecordGuid() {
        return nextId(TAG_TAKEOUT_FIX_RECORD);
    }

    @Override
    public List<String> nextBatchFixItemGuid(long count) {
        return nextBatchId(TAG_TAKEOUT_FIX_ITEM, count);
    }
}
