package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hst_takeout_log")
public class LogDO implements Serializable {

    private static final long serialVersionUID = 9126807575367828724L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * 订单GUID
     */
    private String orderGuid;

    /**
     * 外卖订单ID
     */
    private String orderId;

    /**
     * 操作时间
     */
    private LocalDateTime time;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 元数据title
     */
    private String title;

    /**
     * 元数据body
     */
    private String body;

    /**
     * 是否在商户后台显示
     */
    @TableField("is_show_in_web_page")
    private Boolean showInWebPage;

    /**
     * 是否在终端(AIO)显示
     */
    @TableField("is_show_in_endpoint")
    private Boolean showInEndpoint;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;
}
