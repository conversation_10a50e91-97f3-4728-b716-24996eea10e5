package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.GroupBuyService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/groupbuy")
public class GroupBuyController {


    private final GroupBuyService groupBuyService;

    @Autowired
    public GroupBuyController(GroupBuyService groupBuyService) {
        this.groupBuyService = groupBuyService;
    }


    @PostMapping(value = "/check_ticket")
    @ApiOperation(value = "执行验券", notes = "执行验券")
    public MtCouponDoCheckRespDTO checkTicket(@RequestBody @Validated CouPonReqDTO couPonReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("执行验券入参：{}", JacksonUtils.writeValueAsString(couPonReqDTO));
        }
        return groupBuyService.checkTicket(couPonReqDTO);
    }

    @PostMapping(value = "/do_check")
    @ApiOperation(value = "执行验券", notes = "执行验券")
    public MtCouponDoCheckRespDTO doCheck(@RequestBody @Validated CouPonReqDTO couPonReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("执行验券入参：{}", JacksonUtils.writeValueAsString(couPonReqDTO));
        }
        return groupBuyService.doCheck(couPonReqDTO);
    }


    @PostMapping(value = "/pre_check")
    @ApiOperation(value = "预验券", notes = "预验券")
    public MtCouponPreRespDTO preCheck(@RequestBody CouPonPreReqDTO couPonPreReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("预验券入参：{}", JacksonUtils.writeValueAsString(couPonPreReqDTO));
        }
        return groupBuyService.preCheck(couPonPreReqDTO);
    }


    @PostMapping(value = "/cancel_ticket")
    @ApiOperation(value = "撤销验券", notes = "撤销验券")
    public MtDelCouponRespDTO cancalTicket(@RequestBody @Validated CouponDelReqDTO couponDelReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("撤销验券入参：{}", JacksonUtils.writeValueAsString(couponDelReqDTO));
        }
        return groupBuyService.cancalTicket(couponDelReqDTO);
    }

    @PostMapping("/trade/detail")
    @ApiOperation(value = "查询团购订单结算明细", notes = "查询团购订单结算明细")
    public MtCouponTradeDetailRespDTO queryGroupTradeDetail(@RequestBody CouPonReqDTO couPonReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询团购订单结算明细入参：{}", JacksonUtils.writeValueAsString(couPonReqDTO));
        }
        return groupBuyService.queryGroupTradeDetail(couPonReqDTO);
    }
}
