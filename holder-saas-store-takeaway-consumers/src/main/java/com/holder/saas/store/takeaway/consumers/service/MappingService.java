package com.holder.saas.store.takeaway.consumers.service;

import com.holder.saas.store.takeaway.consumers.entity.domain.ItemBindExtendInfoDo;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.SkuMapDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemMappingReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemMappingRespDTO;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface MappingService {

    TakeoutItemMappingRespDTO getItemBinding(TakeoutItemMappingReqDTO takeoutItemMappingReqDTO)
            throws ExecutionException, InterruptedException;

    TakeawayBatchMappingResult getItems(UnItemQueryReq unItemQueryReq);

    TakeoutItemMappingRespDTO getOwnItemBinding(TakeoutItemMappingReqDTO takeoutItemMappingReqDTO)
            throws ExecutionException, InterruptedException;

    void bindItem(UnItemBindUnbindReq unItemBindUnbindReq);

    void batchBindItem(UnItemBatchUnbindReq unItemBatchUnbindReq);

    /**
     * 更新外卖商品抵扣数量映射
     *
     * @param storeGuid   门店GUID
     * @param takeoutType 外卖类型 0-美团，1-饿了么，2-自营，3-赚餐
     * @param item        商品详情
     */
    void updateItemBindExtendInfo(String storeGuid, Integer takeoutType, UnMappedItem item);

    /**
     * 外卖商品抵扣数量映射移除
     *
     * @param storeGuid     门店GUID
     * @param takeoutType   外卖类型 0-美团，1-饿了么，2-自营，3-赚餐
     * @param erpItemSkuIds 系统商品库商品 skuIds
     */
    void updateItemBindExtendInfoDel(String storeGuid, Integer takeoutType, List<String> erpItemSkuIds);

    /**
     * 查询外卖商品抵扣数量映射
     *
     * @param storeGuid   门店GUID
     * @param takeoutType 外卖类型 0-美团，1-饿了么，2-自营，3-赚餐
     * @param dishSkuList 外卖商品skuIds (传则过滤，不传则返回全部)
     */
    List<ItemBindExtendInfoDo> queryItemBindExtendInfo(String storeGuid, Integer takeoutType, List<String> dishSkuList);

    void unbindItem(UnItemBindUnbindReq unItemBindUnbindReq);

    void batchUnbindItem(UnItemBatchUnbindReq unItemBatchUnbindReq);

    void rollbackOrderItemCount(OrderReadDO orderReadDO);

    void rollbackOrderItemCount(List<ItemDO> items, String storeGuid, Integer takeoutType);

    /**
     * 赚餐同步商品映射数量
     */
    void syncTcdItemMapping(TcdSyncItemMappingDTO itemMappingDTO);

    List<SkuMapDO> listSourceGuidsAndGuidsByGuids(List<String> guids);

    void batchBind(UnItemBatchBindUnbindReq unItemBatchBindUnbindReq);

    void batchUnbind(UnItemBatchBindUnbindReq batchBindUnbindReq);

    /**
     * 查询京东外卖商品绑定信息
     * @param takeoutItemMappingReqDTO 请求参数
     * @return 外卖商品绑定信息
     */
    TakeoutItemMappingRespDTO getJdItemBinding(TakeoutItemMappingReqDTO takeoutItemMappingReqDTO);
}
