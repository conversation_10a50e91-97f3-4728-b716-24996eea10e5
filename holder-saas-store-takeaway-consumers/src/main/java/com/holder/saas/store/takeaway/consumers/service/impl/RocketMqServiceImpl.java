package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.service.RocketMqService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.constant.order.OrderLogMQKeysConstant;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.orderlog.OrderLogMqDTO;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.holder.saas.store.takeaway.consumers.config.RocketMqConfig.TAKEAWAY_PRODUCERS_ORDER_TAG;
import static com.holder.saas.store.takeaway.consumers.config.RocketMqConfig.TAKEAWAY_PRODUCERS_ORDER_TOPIC;
import static com.holderzone.saas.store.constant.order.OrderLogMQKeysConstant.ENTERPRISE_GUID;


/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketMqServiceImpl
 * @date 2018/09/18 20:45
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Service
public class RocketMqServiceImpl implements RocketMqService {

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    @Autowired
    public RocketMqServiceImpl(DefaultRocketMqProducer defaultRocketMqProducer) {
        this.defaultRocketMqProducer = defaultRocketMqProducer;
    }

    @Override
    public void sendUnOrder(UnOrder unOrder) {
        if (log.isInfoEnabled()) {
            log.info("发送订单回复消息: {}，unOrder: {}", unOrder.getReplyMsgType(), JacksonUtils.writeValueAsString(unOrder));
        }

        Message message = new Message(TAKEAWAY_PRODUCERS_ORDER_TOPIC, TAKEAWAY_PRODUCERS_ORDER_TAG,
                JacksonUtils.toJsonByte(unOrder));

        if (!defaultRocketMqProducer.sendMessage(message)) {
            if (log.isErrorEnabled()) {
                log.error("企业[{}]，订单[{}]，订单消息发送异常", unOrder.getEnterpriseGuid(), unOrder.getOrderId());
            }
        }
    }

    @Override
    public void sendUnOrderLog(OrderLogMqDTO orderLogMqDTO, String enterpriseGuid) {
        if (log.isInfoEnabled()) {
            log.info("企业[{}]，订单[{}]，发送订单日志{}", enterpriseGuid, orderLogMqDTO.getOrderGuid(),
                    JacksonUtils.writeValueAsString(orderLogMqDTO));
        }

        Message message = new Message(OrderLogMQKeysConstant.ORDER_LOG_TOPIC, OrderLogMQKeysConstant.ORDER_LOG_TAG,
                JacksonUtils.toJsonByte(orderLogMqDTO));
        message.getProperties().put(ENTERPRISE_GUID, enterpriseGuid);

        if (!defaultRocketMqProducer.sendMessage(message)) {
            if (log.isErrorEnabled()) {
                log.error("企业[{}]，订单[{}]，订单日志发送异常", enterpriseGuid, orderLogMqDTO.getOrderGuid());
            }
        }
    }

    @Override
    public void sendBusinessMessage(BusinessMessageDTO businessMessage) {
        if (log.isInfoEnabled()) {
            log.info("发送推送信息message: {}",businessMessage);
        }
        Message message = new Message(
                RocketMqConfig.SYSTEM_MSG_TOPIC,
                RocketMqConfig.SYSTEM_MSG_TAG,
                JacksonUtils.toJsonByte(businessMessage)
        );
        message.getProperties().put(
                RocketMqConfig.MESSAGE_CONTEXT,
                UserContextUtils.getJsonStr()
        );
        if (!defaultRocketMqProducer.sendMessage(message) && log.isErrorEnabled()) {
            log.error("消息日志发送异常：{}",businessMessage);
        }
    }
}
