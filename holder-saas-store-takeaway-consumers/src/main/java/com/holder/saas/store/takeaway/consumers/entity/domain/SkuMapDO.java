package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * sku映射
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hst_takeout_sku_map")
public class SkuMapDO implements Serializable {

    private static final long serialVersionUID = 9126807575367828724L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 新GUID，去掉-的
     */
    private String guid;

    /**
     * 原GUID：带有-的
     */
    private String sourceGuid;

    /**
     * 来源：0=美团，1=饿了么
     */
    private Integer sourceType;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;
}