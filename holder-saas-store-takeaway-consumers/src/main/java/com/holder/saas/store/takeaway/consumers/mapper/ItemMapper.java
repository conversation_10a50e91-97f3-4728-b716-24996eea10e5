package com.holder.saas.store.takeaway.consumers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO;
import com.holderzone.saas.store.dto.trade.ItemPageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外卖订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-26
 */
public interface ItemMapper extends BaseMapper<ItemDO> {

    IPage<ItemDO> pageOrderItem(IPage<ItemDO> iPage,
                                @Param("query") ItemPageQuery query);

    void updateBatchItem(@Param("list") List<ItemDO> list);

    void fixBatchItem(@Param("list") List<ItemDO> fixItemList);

    IPage<TakeoutItemAbnormalDataRespDTO> pageAbnormalData(IPage<ItemDO> iPage,
                                                           @Param("query") TakeoutItemAbnormalDataReqDTO reqDTO);

    List<TakeoutItemDataFixRespDTO> listDataFix(@Param("query") TakeoutItemDataFixReqDTO reqDTO);

    void clearErpItemSkuGuid(@Param("ids") List<Long> ids);

    List<ItemDO> filterNoRequiredFixList(@Param("ids") List<Long> ids);

    void updateBatchRefundCount(@Param("itemList") List<ItemDO> itemList);

    void updateRefundCountByOrderGuid(@Param("orderGuid") String orderGuid);

}
