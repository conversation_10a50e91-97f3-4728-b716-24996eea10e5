package com.holder.saas.store.takeaway.consumers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holder.saas.store.takeaway.consumers.entity.domain.AbnormalDataDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外卖异常数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
public interface AbnormalDataMapper extends BaseMapper<AbnormalDataDO> {

    IPage<TakeoutItemAbnormalDataRespDTO> page(IPage<ItemDO> iPage,
                                               @Param("query") TakeoutItemAbnormalDataReqDTO reqDTO);

    List<TakeoutItemDataFixRespDTO> listDataFix(@Param("query") TakeoutItemDataFixReqDTO reqDTO);

    List<ItemDO> listFixItem(@Param("query") TakeoutItemDataFixReqDTO reqDTO);

    void insertIgnore(List<AbnormalDataDO> dataDOList);
}
