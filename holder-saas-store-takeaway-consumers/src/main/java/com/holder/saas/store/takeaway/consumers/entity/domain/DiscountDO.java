package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * hst_takeaway_order_discount
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hst_takeout_discount")
public class DiscountDO implements Serializable {

    private static final long serialVersionUID = 1323694670939610811L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 订单折扣GUID
     */
    private String discountGuid;

    /**
     * 订单GUID
     */
    private String orderGuid;

    /**
     * 优惠活动名称
     */
    private String discountName;

    /**
     * 备注说明
     */
    private String discountRemark;

    /**
     * 此优惠活动总金额 EnterpriseDiscount+PlatformTotal+OtherTotal
     */
    private BigDecimal totalDiscount;

    /**
     * 商家承担的折扣部分
     */
    private BigDecimal enterpriseDiscount;

    /**
     * 外卖平台承担的折扣部分
     */
    private BigDecimal platformDiscount;

    /**
     * 第三方承担的部分
     */
    private BigDecimal otherDiscount;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 活动类型
     */
    private Integer type;
}