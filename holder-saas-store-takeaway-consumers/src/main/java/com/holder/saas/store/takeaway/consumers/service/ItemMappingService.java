package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemMappingDO;
import com.holderzone.saas.store.dto.takeaway.TcdSyncItemMappingDTO;

import java.util.List;

public interface ItemMappingService extends IService<ItemMappingDO> {

    List<ItemMappingDO> queryItemMappingInfo(List<String> mapperGuids);

    List<ItemMappingDO> queryByStoreGuidAndTakeoutTypeAndMapperGuids(String storeGuid, Integer takeoutType, List<String> mapperGuids);

    void syncTcdItemMapping(List<TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping> tcdItemMappingList);

}
