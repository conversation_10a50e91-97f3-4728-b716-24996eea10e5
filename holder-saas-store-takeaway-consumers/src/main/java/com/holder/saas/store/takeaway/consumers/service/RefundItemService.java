package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.RefundItemDO;

import java.util.List;


/**
 * <p>
 * 外卖退菜商品明细服务类
 * </p>
 */
public interface RefundItemService extends IService<RefundItemDO> {

    List<RefundItemDO> listItemGroupByOrderItemGuid(String orderGuid);

    void updateRefundSuccess(String orderGuid);

    void removeByOrderGuid(String orderGuid);
}
