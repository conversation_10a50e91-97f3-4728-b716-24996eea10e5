package com.holder.saas.store.takeaway.consumers.utils;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.sdk.util.IdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DynamicUtils
 * @date 2018/10/25 15:28
 * @description //TODO
 * @program holder-saas-store-order
 */
@Component
public class DynamicHelper {

    private static final Logger log = LoggerFactory.getLogger(DynamicHelper.class);

    @Value("${self.open-dynamic-datasource}")
    private Boolean openDynamicDatasource;

    private final RedisTemplate redisTemplate;

    @Autowired
    public DynamicHelper(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void changeDatasource(String enterpriseGuid) {
        if (openDynamicDatasource) {
            try {
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            } catch (Exception e) {
                log.error("手动切换数据源异常！enterpriseGuid:" + enterpriseGuid + ":{}", e.getMessage());
            }
        }
    }

    public void changeRedis(String enterpriseGuid) {
        try {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        } catch (Exception e) {
            log.error("手动切换数据源异常！enterpriseGuid:" + enterpriseGuid + ":{}", e.getMessage());
        }
    }

    public void removeThreadLocalDatabaseInfo(){
        EnterpriseIdentifier.remove();
    }


    /**
     * 生成guid
     *
     * @param redisKey
     * @return
     */
    public String generateGuid(String redisKey) {

        return String.valueOf(IdGenerator.builder(redisTemplate).next("takeaway"));


    }


}
