package com.holder.saas.store.takeaway.consumers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemExtendsDO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 外卖菜品扩展表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
public interface ItemExtendsMapper extends BaseMapper<ItemExtendsDO> {

    BigDecimal getTotalCostPrice(@Param("list") List<String> list);
}
