/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:OrderContextUtils.java
 * Date:2020-1-13
 * Author:terry
 */

package com.holder.saas.store.takeaway.consumers.utils;

/**
 * <AUTHOR>
 * @date 2020-01-13 下午12:30
 */
public final class OrderContextUtils {

    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();

    private OrderContextUtils() {
    }

    public static void setOrderContext(String orderContext) {
        THREAD_LOCAL.set(orderContext);
    }

    public static String getOrderContext() {
        return THREAD_LOCAL.get();
    }

    public static void removeOrderContext() {
        THREAD_LOCAL.remove();
    }
}
