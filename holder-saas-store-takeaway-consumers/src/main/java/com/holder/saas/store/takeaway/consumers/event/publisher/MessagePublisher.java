package com.holder.saas.store.takeaway.consumers.event.publisher;

import com.holder.saas.store.takeaway.consumers.event.MessageEvent;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import javafx.util.Pair;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024-08-07
 * @description 消息推送类
 */
@Component
public class MessagePublisher {
    private final ApplicationContext applicationContext;

    public MessagePublisher(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public void send(Pair<UserContext,BusinessMessageDTO> businessMessage){
        applicationContext.publishEvent(new MessageEvent(businessMessage));
    }
}
