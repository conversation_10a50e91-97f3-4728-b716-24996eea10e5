package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ErpClientService
 * @date 2018/08/14 14:12
 * @description //
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = ErpFeignService.ResourceClientServiceFallBack.class)
public interface ErpFeignService {

    @PostMapping("/inOutDocument/reduceStockForOrder")
    void reduceStockForOrder(@RequestBody OrderSkuDTO orderSkuDTO);

    @Component
    class ResourceClientServiceFallBack implements FallbackFactory<ErpFeignService> {

        private static final Logger logger = LoggerFactory.getLogger(ResourceClientServiceFallBack.class);

        @Override
        public ErpFeignService create(Throwable throwable) {

            return new ErpFeignService() {
                @Override
                public void reduceStockForOrder(OrderSkuDTO orderSkuDTO) {
                    logger.error("扣减库存失败e={}", throwable.getMessage());
                }
            };
        }
    }

}
