package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.consumers.entity.domain.AutoRecoveryDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.mapper.AutoRecoveryMapper;
import com.holder.saas.store.takeaway.consumers.service.AutoRecoveryService;
import com.holderzone.saas.store.enums.takeaway.TakeawayExSourceEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 外卖自动修复异常数据
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AutoRecoveryServiceImpl extends ServiceImpl<AutoRecoveryMapper, AutoRecoveryDO> implements AutoRecoveryService {

    @Override
    public List<AutoRecoveryDO> queryLimit() {
        return baseMapper.queryLimit();
    }

    @Override
    public void createOrderEx(List<ItemDO> itemList, String exMsg) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        List<AutoRecoveryDO> autoRecoveryList = itemList.stream().map(item -> {
            AutoRecoveryDO autoRecoveryDO = new AutoRecoveryDO();
            autoRecoveryDO.setId(item.getItemGuid());
            autoRecoveryDO.setOrderGuid(item.getOrderGuid());
            autoRecoveryDO.setExSource(TakeawayExSourceEnum.CREATE_ORDER.name());
            autoRecoveryDO.setExMsg(exMsg);
            return autoRecoveryDO;
        }).collect(Collectors.toList());
        saveOrUpdateBatch(autoRecoveryList);
    }
}
