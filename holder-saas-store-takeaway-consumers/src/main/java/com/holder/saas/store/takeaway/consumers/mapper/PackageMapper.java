package com.holder.saas.store.takeaway.consumers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.PackageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外卖订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-26
 */
@Mapper
public interface PackageMapper extends BaseMapper<PackageDO> {

    void deleteBatchByItem(@Param("itemGuidList") List<String> itemGuidList);
}
