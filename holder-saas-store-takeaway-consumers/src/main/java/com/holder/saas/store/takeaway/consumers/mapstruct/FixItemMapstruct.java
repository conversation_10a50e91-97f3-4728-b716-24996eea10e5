package com.holder.saas.store.takeaway.consumers.mapstruct;

import com.holder.saas.store.takeaway.consumers.entity.domain.FixItemDO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixItemDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * FixItemDO 转换工具
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/04
 */
@Component
@Mapper(componentModel = "spring")
public interface FixItemMapstruct {

    TakeoutFixItemDTO do2DTO(FixItemDO fixItemDO);

    List<TakeoutFixItemDTO> doList2DTOList(List<FixItemDO> fixItemDOList);
}
