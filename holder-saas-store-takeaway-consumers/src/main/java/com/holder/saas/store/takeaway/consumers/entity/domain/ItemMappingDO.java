package com.holder.saas.store.takeaway.consumers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 外卖商品映射表
 * @date 2021/11/12 15:16
 * @className: ItemMappingDO
 */
@Data
@Accessors(chain = true)
@TableName("hst_takeout_item_mapping")
public class ItemMappingDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除：0false 1true
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 外卖平台类型0：美团，1：饿了么,2:自营,3:赚餐
     */
    private Integer takeoutType;

    /**
     * 外卖方商品规格id
     */
    private String unItemSkuId;

    /**
     * 系统方商品规格guid
     */
    private String erpItemSkuGuid;

    /**
     * 绑定映射guid
     */
    private String mapperGuid;

}
