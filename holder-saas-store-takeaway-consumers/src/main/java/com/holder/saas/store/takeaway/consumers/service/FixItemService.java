package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixItemDO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixItemDTO;

import java.util.List;


/**
 * <p>
 * 外卖审核记录商品明细服务类
 * </p>
 */
public interface FixItemService extends IService<FixItemDO> {

    List<TakeoutFixItemDTO> listByRecordId(Long recordId);

    List<String> queryStoreGuids(Long recordId);
}
