package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import com.holder.saas.store.takeaway.consumers.service.AuthService;
import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holder.saas.store.takeaway.consumers.service.rpc.OrgFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.StaffFeignClient;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByTypeReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreUsedReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    private final OrderService orderService;

    private final StaffFeignClient staffFeignClient;

    private final OrgFeignClient orgFeignClient;

    private final ProducerFeignClient producerFeignClient;

    @Autowired
    public AuthServiceImpl(OrderService orderService, StaffFeignClient staffFeignClient,
                           OrgFeignClient orgFeignClient, ProducerFeignClient producerFeignClient) {
        this.orderService = orderService;
        this.staffFeignClient = staffFeignClient;
        this.orgFeignClient = orgFeignClient;
        this.producerFeignClient = producerFeignClient;
    }

    @Override
    public List<StoreAuthDTO> queryAuthByType(StoreAuthByTypeReqDTO storeAuthByTypeReqDTO) {
        //1、绑定页面初始化、根据satfId查询该企业下所有的门店信息
        //根据登陆人id查询相关联的门店信息
        List<StoreDTO> arrayOfStoreDTO = staffFeignClient.findStoreManagedByUser().getArrayOfStoreDTO();
        log.info("查询出来的门店信息organizeDTOlist：{}", JacksonUtils.writeValueAsString(arrayOfStoreDTO));
        if (null == arrayOfStoreDTO || arrayOfStoreDTO.size() == 0) {
            log.info("当前登陆的userGuid：{}，查询出来的门店信息为空", storeAuthByTypeReqDTO.getUserGuid());
            return null;
        }

        //2、根据门店id、外卖类型匹配本地db、看是否有绑定信息
        List<StoreAuthDTO> storeAuthorizationDTOList = new ArrayList<>();
        for (StoreDTO storeDTO : arrayOfStoreDTO) {
            StoreAuthDTO storeAuthorizationDTO1 = new StoreAuthDTO();
            storeAuthorizationDTO1.setStoreNumber(storeDTO.getCode());  // 门店编号
            storeAuthorizationDTO1.setStoreGuid(storeDTO.getGuid());    // 门店Guid
            storeAuthorizationDTO1.setStoreName(storeDTO.getName());    // 门店名称
            storeAuthorizationDTOList.add(storeAuthorizationDTO1);
        }
        List<StoreAuthDTO> newStoreAuthorizationDTOList = new ArrayList<>();
        //饿了么
        if (storeAuthByTypeReqDTO.getTakeoutType() == 2) {
            newStoreAuthorizationDTOList = producerFeignClient.listEleAuth(storeAuthorizationDTOList);
        }
        //美团
        else if (storeAuthByTypeReqDTO.getTakeoutType() == 1) {
            newStoreAuthorizationDTOList = producerFeignClient.listMtAuth(storeAuthorizationDTOList);
        }

        if (storeAuthByTypeReqDTO.getBindingStatus() != null || !"".equals(storeAuthByTypeReqDTO.getQueryString())) {
            //此处为条件查询
            return queryStoreAuthorization(newStoreAuthorizationDTOList, storeAuthByTypeReqDTO);
        }
        return newStoreAuthorizationDTOList;
    }

    @Override
    public List<StoreAuthDTO> queryTakeoutAuthByStore(StoreAuthByStoreReqDTO storeAuthReqDTO) {
        StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setStoreGuid(storeAuthReqDTO.getStoreGuid());

        List<StoreAuthDTO> storeAuthDTOS = new ArrayList<>();

        StoreAuthDTO mtTakeoutAuth = producerFeignClient.getMtTakeoutAuth(storeAuthDTO);
        mtTakeoutAuth.setTakeoutType(OrderType.TakeoutSubType.MT_TAKEOUT.getType());
        mtTakeoutAuth.setPlatformName("美团外卖");
        storeAuthDTOS.add(mtTakeoutAuth);

        StoreAuthDTO eleTakeoutAuth = producerFeignClient.getEleTakeoutAuth(storeAuthDTO);
        eleTakeoutAuth.setTakeoutType(OrderType.TakeoutSubType.ELE_TAKEOUT.getType());
        eleTakeoutAuth.setPlatformName("饿了么");
        storeAuthDTOS.add(eleTakeoutAuth);

        StoreAuthDTO ownTakeoutAuth = producerFeignClient.getOwnTakeoutAuth(storeAuthDTO);
        ownTakeoutAuth.setTakeoutType(OrderType.TakeoutSubType.OWN_TAKEOUT.getType());
        ownTakeoutAuth.setPlatformName("自营外卖平台");
        storeAuthDTOS.add(ownTakeoutAuth);

        StoreAuthDTO tcdTakeoutAuth = producerFeignClient.getTcdTakeoutAuth(storeAuthDTO);
        tcdTakeoutAuth.setTakeoutType(OrderType.TakeoutSubType.TCD_TAKEOUT.getType());
        tcdTakeoutAuth.setPlatformName("赚餐外卖平台");
        storeAuthDTOS.add(tcdTakeoutAuth);

        StoreAuthDTO jdTakeoutAuth = producerFeignClient.geJdTakeoutAuth(storeAuthDTO);
        jdTakeoutAuth.setTakeoutType(OrderType.TakeoutSubType.JD_TAKEOUT.getType());
        jdTakeoutAuth.setPlatformName("京东外卖平台");
        storeAuthDTOS.add(jdTakeoutAuth);

        return storeAuthDTOS;
    }

    @Override
    public List<StoreAuthDTO> queryTuanGouByStore(StoreAuthByStoreReqDTO storeAuthReqDTO) {
        StoreDTO storeDTO = orgFeignClient.queryStoreByGuid(storeAuthReqDTO.getStoreGuid());
        StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setStoreGuid(storeDTO.getGuid());
        List<StoreAuthDTO> storeAuthDTOS = new ArrayList<>();
        /*StoreAuthDTO eleme = new StoreAuthDTO();
        eleme.setPlatformName("饿了么");
        eleme.setBindingStatus(0);
        eleme.setTakeoutType(1);
        eleme.setShopId(null);
        eleme.setShopName(null);
        eleme.setStoreGuid(null);
        eleme.setStoreNumber(null);
        eleme.setStoreName(null);
        storeAuthDTOS.add(eleme);//饿了么没有团购店铺*/
        storeAuthDTOS.add(producerFeignClient.getMtTuanGouAuth(storeAuthDTO).setPlatformName("美团团购").setTakeoutType(0));

        return storeAuthDTOS;
    }

    @Override
    public List<StoreDTO> queryStoreUsed(StoreUsedReqDTO storeUsedReqDTO) {
        // 根据登陆人id查询相关联的门店信息
        List<StoreDTO> arrayOfStoreDTO = staffFeignClient.findStoreManagedByUser().getArrayOfStoreDTO();
        if (null == arrayOfStoreDTO || arrayOfStoreDTO.size() == 0) {
            log.info("当前登陆的userGuid：{}，查询出来的门店信息为空", storeUsedReqDTO.getUserGuid());
            return Collections.emptyList();
        }
        log.info("查询出来的门店信息arrayOfOrganizeDTO：{}", JacksonUtils.writeValueAsString(arrayOfStoreDTO));

        // 根据门店id、外卖类型匹配本地db
        List<StoreAuthDTO> arrayOfStoreAuthorizationDTO = arrayOfStoreDTO.stream()
                .map(organizeDTO -> {
                    StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
                    storeAuthDTO.setStoreNumber(organizeDTO.getCode());
                    storeAuthDTO.setStoreGuid(organizeDTO.getGuid());
                    storeAuthDTO.setStoreName(organizeDTO.getName());
                    return storeAuthDTO;
                })
                .collect(Collectors.toList());
        Set<String> setOfStoreUsed = new HashSet<>();
        producerFeignClient.listMtAuth(arrayOfStoreAuthorizationDTO).stream()
                .filter(it -> Integer.valueOf(1).equals(it.getBindingStatus()))
                .map(StoreAuthDTO::getStoreGuid)
                .forEach(setOfStoreUsed::add);
        producerFeignClient.listEleAuth(arrayOfStoreAuthorizationDTO).stream()
                .filter(it -> Integer.valueOf(1).equals(it.getBindingStatus()))
                .map(StoreAuthDTO::getStoreGuid)
                .forEach(setOfStoreUsed::add);

        List<String> arrayOfStoreUnbind = arrayOfStoreDTO.stream()
                .filter(organizeDTO -> !setOfStoreUsed.contains(organizeDTO.getGuid()))
                .map(StoreDTO::getGuid)
                .collect(Collectors.toList());

        for (String storeUnbind : arrayOfStoreUnbind) {
            LambdaQueryWrapper<OrderDO> wrapper = new LambdaQueryWrapper<OrderDO>()
                    .eq(OrderDO::getStoreGuid, storeUnbind);
            if (orderService.count(wrapper) > 0) {
                setOfStoreUsed.add(storeUnbind);
            }
        }

        return arrayOfStoreDTO.stream()
                .filter(organizeDTO -> setOfStoreUsed.contains(organizeDTO.getGuid()))
                .collect(Collectors.toList());
    }

    @Override
    public Boolean updateDeliveryType(StoreAuthDTO storeAuthDTO) {

        //饿了么
        if (storeAuthDTO.getTakeoutType() == 1) {
            return producerFeignClient.eleUpdateDelivery(storeAuthDTO);
            //美团
        } else if (storeAuthDTO.getTakeoutType() == 0) {
            return producerFeignClient.mtUpdateDelivery(storeAuthDTO);
            //赚餐
        } else if (storeAuthDTO.getTakeoutType() == 6) {
            return producerFeignClient.tcdUpdateDelivery(storeAuthDTO);
        }
        return Boolean.FALSE;
    }

    private List<StoreAuthDTO> queryStoreAuthorization(List<StoreAuthDTO> storeAuthDTOList,
                                                       StoreAuthByTypeReqDTO storeAuthByTypeReqDTO) {
        int status = storeAuthByTypeReqDTO.getBindingStatus();
        String queryString = storeAuthByTypeReqDTO.getQueryString();

        List<StoreAuthDTO> newStoreAuthDTOList = new ArrayList<>();

        for (StoreAuthDTO storeAuthDTO : storeAuthDTOList) {
            //循环初始化结果的list
            if (storeAuthDTO.getBindingStatus() == status || status == 2) {
                //state为2查询全部状态
                //比对状态值相同的
                if (StringUtils.isEmpty(queryString)) {
                    newStoreAuthDTOList.add(storeAuthDTO);
                } else {
                    if (storeAuthDTO.getStoreNumber().contains(queryString)
                            || storeAuthDTO.getStoreName().contains(queryString)) {
                        //比对查询条件（门店编号/门店名称）
                        newStoreAuthDTOList.add(storeAuthDTO);
                    }
                }
            }
        }

        return newStoreAuthDTOList;
    }
}
