package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.service.ItemClientService;
import com.holder.saas.store.takeaway.consumers.service.KdsService;
import com.holder.saas.store.takeaway.consumers.utils.OrderContextUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubItemSkuWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubgroupWebRespDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class KdsServiceImpl implements KdsService {

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final ItemClientService itemClientService;


    /**
     * kds加菜
     *
     * @param platformName
     * @param orderReadDO
     * @param mapOfSkuTakeawayInfoRespDTO
     */
    @Override
    public void prepare(String platformName, OrderReadDO orderReadDO,
                        Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        String orderContext = OrderContextUtils.getOrderContext();
        if (orderReadDO.getOrderSubType() == OrderType.TakeoutSubType.OWN_TAKEOUT.getType()) {
            //fixme 自营暂时先不对接kds，后续需要加上
            log.warn("{}，自营外卖暂不到KDS", orderContext);
            return;
        }
        log.info("prepare: {}", JacksonUtils.writeValueAsString(orderReadDO));
        ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setEnterpriseGuid(orderReadDO.getEnterpriseGuid());
        itemPrepareReqDTO.setEnterpriseName(orderReadDO.getEnterpriseName());
        itemPrepareReqDTO.setStoreName(orderReadDO.getStoreName());

        //ItemDO itemDO : orderReadDO.getArrayOfItem()
        itemPrepareReqDTO.setOrderGuid(orderReadDO.getOrderGuid());
        itemPrepareReqDTO.setDeviceId(orderReadDO.getAcceptDeviceId());
        itemPrepareReqDTO.setStoreGuid(orderReadDO.getStoreGuid());
        itemPrepareReqDTO.setOrderRemark(orderReadDO.getOrderRemark());

        itemPrepareReqDTO.setTradeMode(2);
        itemPrepareReqDTO.setDeviceType(orderReadDO.getAcceptDeviceType());
        TradeTakeoutInfoDTO tradeTakeoutInfoDTO = new TradeTakeoutInfoDTO();
        if (orderReadDO.getOrderType() != 0) {
            throw new BusinessException("发送消息到kds失败：orderGuid=" + orderReadDO.getOrderGuid()
                    + ",orderType=" + orderReadDO.getOrderType() + "订单不是外卖订单,");
        } else {
            if (orderReadDO.getOrderSubType() != 0 && orderReadDO.getOrderSubType() != 1
                    && orderReadDO.getOrderSubType() != 2 && orderReadDO.getOrderSubType() != 3
                    && orderReadDO.getOrderSubType() != 6
            ) {
                tradeTakeoutInfoDTO.setPlatformNumber("-1");
                tradeTakeoutInfoDTO.setPlatformName("请手动指定");
                tradeTakeoutInfoDTO.setPlatformSerialNo("-1");
            } else {
                tradeTakeoutInfoDTO.setPlatformName(
                        OrderType.TakeoutSubType.ofType(orderReadDO.getOrderSubType()).getSource());
                tradeTakeoutInfoDTO.setPlatformNumber(orderReadDO.getOrderId());
                tradeTakeoutInfoDTO.setPlatformSerialNo("#" + orderReadDO.getOrderDaySn());
            }
        }
        tradeTakeoutInfoDTO.setIsAutoAccept(orderReadDO.getIsAutoAccept());
        itemPrepareReqDTO.setTradeTakeoutInfoDTO(tradeTakeoutInfoDTO);
        List<KdsItemDTO> resultKdsItems = new ArrayList<>();
        List<ItemDO> arrayOfItem = orderReadDO.getArrayOfItem();
        //商品映射数量回滚
        Map<String, ItemInfoRespDTO> pkgItemInfoMap = getPkgItemInfoMap(mapOfSkuTakeawayInfoRespDTO);
        for (ItemDO item : arrayOfItem) {
            KdsItemDTO kdsItemDTO = new KdsItemDTO();
            BigDecimal itemCount = item.getActualItemCount() == null ? item.getItemCount() : item.getActualItemCount();
            kdsItemDTO.setIsWeight(false);
            kdsItemDTO.setItemState(1);
            kdsItemDTO.setOrderRemark(orderReadDO.getOrderRemark());
            SkuTakeawayInfoRespDTO sku = mapOfSkuTakeawayInfoRespDTO.get(item.getItemSku());
            // 为了解决发送到kds后因没有备注而被合并的问题
            if (StringUtils.isNotEmpty(item.getItemProperty())) {
                kdsItemDTO.setItemRemark("[" + item.getItemProperty() + "]");
            } else {
                kdsItemDTO.setItemRemark("");
            }
            kdsItemDTO.setAttrGroup(Collections.emptyList());
            if (sku != null) {
                // 外卖绑定套餐处理
                if (Objects.equals(ItemTypeEnum.PKG.getCode(), sku.getItemType())) {
                    ItemInfoRespDTO itemInfo = pkgItemInfoMap.get(sku.getItemGuid());
                    log.info("kds itemInfo={} itemGuid={}", JacksonUtils.writeValueAsString(itemInfo), sku.getItemGuid());
                    if (!ObjectUtils.isEmpty(itemInfo)) {
                        for (SubgroupWebRespDTO group : itemInfo.getSubgroupList()) {
                            for (SubItemSkuWebRespDTO subItem : group.getSubItemSkuList()) {
                                kdsItemDTO.setOrderItemGuid(orderReadDO.getOrderGuid() + "," + subItem.getSkuGuid());
                                kdsItemDTO.setItemGuid(subItem.getItemGuid());
                                kdsItemDTO.setItemName(subItem.getItemName());
                                kdsItemDTO.setSkuCode(subItem.getCode());
                                kdsItemDTO.setSkuGuid(subItem.getSkuGuid());
                                kdsItemDTO.setSkuName(subItem.getSkuName());
                                kdsItemDTO.setSkuUnit(subItem.getUnit());
                                kdsItemDTO.setCurrentCount(itemCount.multiply(subItem.getItemNum()));
                                KdsItemDTO dto = new KdsItemDTO();
                                BeanUtils.copyProperties(kdsItemDTO, dto);
                                resultKdsItems.add(dto);
                            }
                        }
                    }
                } else {
                    kdsItemDTO.setOrderItemGuid(orderReadDO.getOrderGuid() + "," + sku.getSkuGuid());
                    kdsItemDTO.setItemGuid(sku.getItemGuid());
                    kdsItemDTO.setItemName(sku.getItemName());
                    kdsItemDTO.setSkuCode(sku.getCode());
                    kdsItemDTO.setSkuGuid(sku.getSkuGuid());
                    kdsItemDTO.setSkuName(sku.getSkuName());
                    kdsItemDTO.setSkuUnit(sku.getUnit());
                    kdsItemDTO.setCurrentCount(itemCount);
                    KdsItemDTO dto = new KdsItemDTO();
                    BeanUtils.copyProperties(kdsItemDTO, dto);
                    resultKdsItems.add(dto);
                }
            } else {
                log.warn("{}菜品[{}]规格[{}]未关联", orderContext, kdsItemDTO.getItemGuid(), item.getItemSku());
            }
        }
        itemPrepareReqDTO.setItems(resultKdsItems);

        if (ObjectUtils.isEmpty(resultKdsItems)) {
            log.info("{}所有菜品规格未关联", orderContext);
        } else {
            log.info("{}kds备菜，请求参数：{}",
                    orderContext, JacksonUtils.writeValueAsString(itemPrepareReqDTO));
            doKdsAsync(itemPrepareReqDTO, RocketMqConfig.KDS_PREPARE_TAG);
            log.info("{}kds备菜，处理完毕", orderContext);
        }
    }

    /**
     * 通过商品列表查询商品map
     * kds
     *
     * @param mapOfSkuTakeawayInfoRespDTO skuMap
     * @return itemMap
     */
    private Map<String, ItemInfoRespDTO> getPkgItemInfoMap(Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        List<String> itemGuidList = mapOfSkuTakeawayInfoRespDTO.values().stream()
                .map(SkuTakeawayInfoRespDTO::getItemGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemGuidList)) {
            log.info("kds getPkgItemInfoMap itemGuidList为空");
            return new HashMap<>();
        }
        ItemStringListDTO dto = new ItemStringListDTO();
        dto.setDataList(itemGuidList);
        List<ItemInfoRespDTO> pkgItemInfoList = itemClientService.listPkgItemInfo(dto);
        log.info("kds pkgItemInfoList={}", JacksonUtils.writeValueAsString(pkgItemInfoList));
        return pkgItemInfoList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, p -> p));
    }

    /**
     * kds催菜
     *
     * @param orderReadDO
     */
    @Override
    public void urge(OrderReadDO orderReadDO) {
        //fixme 暂时先不对接kds
        if (orderReadDO.getOrderSubType() == OrderType.TakeoutSubType.OWN_TAKEOUT.getType()) {
            return;
        }
        ItemUrgeReqDTO itemUrgeReqDTO = new ItemUrgeReqDTO();
        itemUrgeReqDTO.setOrderItemGuidList(orderReadDO.getArrayOfItem()
                .stream()
                .map(e -> orderReadDO.getOrderGuid() + "," + e.getItemSku())
                .collect(Collectors.toList())
        );
        log.info("kds催菜：itemUrgeReqDTO={}", itemUrgeReqDTO);
        doKdsAsync(itemUrgeReqDTO, RocketMqConfig.KDS_URGE_TAG);
    }

    /**
     * kds退菜
     *
     * @param orderReadDO
     */
    @Override
    public void refund(OrderReadDO orderReadDO, Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO) {
        //fixme 暂时先不对接kds
        if (orderReadDO.getOrderSubType() == OrderType.TakeoutSubType.OWN_TAKEOUT.getType()) {
            return;
        }
        // 查询商品及套餐信息
        Map<String, ItemInfoRespDTO> pkgItemInfoMap = getPkgItemInfoMap(mapOfSkuTakeawayInfoRespDTO);
        List<ItemDO> itemList = orderReadDO.getArrayOfItem();
        log.info("kds退菜：orderReadDO={},itemList={}", orderReadDO, itemList);
        ItemBatchRefundReqDTO itemBatchRefundReqDTO = new ItemBatchRefundReqDTO();
        List<ItemRefundReqDTO> list = new ArrayList<>();
        for (ItemDO item : itemList) {
            BigDecimal itemCount = item.getActualItemCount() == null ? item.getItemCount() : item.getActualItemCount();
            SkuTakeawayInfoRespDTO sku = mapOfSkuTakeawayInfoRespDTO.get(item.getItemSku());
            if (Objects.nonNull(sku) && Objects.equals(ItemTypeEnum.PKG.getCode(), sku.getItemType())) {
                ItemInfoRespDTO itemInfo = pkgItemInfoMap.get(sku.getItemGuid());
                if (!ObjectUtils.isEmpty(itemInfo)) {
                    for (SubgroupWebRespDTO group : itemInfo.getSubgroupList()) {
                        for (SubItemSkuWebRespDTO subItem : group.getSubItemSkuList()) {
                            ItemRefundReqDTO itemRefundReqDTO = new ItemRefundReqDTO();
                            itemRefundReqDTO.setIsWeight(false);
                            itemRefundReqDTO.setOrderItemGuid(orderReadDO.getOrderGuid() + "," + subItem.getSkuGuid());
                            itemRefundReqDTO.setNumber(itemCount.multiply(subItem.getItemNum()).intValue());
                            list.add(itemRefundReqDTO);
                        }
                    }
                }
            } else {
                ItemRefundReqDTO itemRefundReqDTO = new ItemRefundReqDTO();
                itemRefundReqDTO.setIsWeight(false);
                itemRefundReqDTO.setOrderItemGuid(orderReadDO.getOrderGuid() + "," + item.getItemSku());
                itemRefundReqDTO.setNumber(itemCount.intValue());
                list.add(itemRefundReqDTO);
            }
        }
        itemBatchRefundReqDTO.setItemRefundList(list);
        log.info("kds退菜：itemBatchRefundReqDTO={}", itemBatchRefundReqDTO);
        doKdsAsync(itemBatchRefundReqDTO, RocketMqConfig.KDS_REFUND_TAG);
    }

    private void doKdsAsync(Object object, String kdsSomeTag) {
        Message message = new Message(
                RocketMqConfig.KDS_MESSAGE_TOPIC,
                kdsSomeTag,
                JacksonUtils.toJsonByte(object)
        );
        message.getProperties().put(
                RocketMqConfig.USER_INFO,
                UserContextUtils.getJsonStr()
        );
        defaultRocketMqProducer.sendMessage(message);
    }

}