package com.holder.saas.store.takeaway.consumers.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.PackageDO;
import com.holder.saas.store.takeaway.consumers.mapper.PackageMapper;
import com.holder.saas.store.takeaway.consumers.mapstruct.ItemPackageMapstruct;
import com.holder.saas.store.takeaway.consumers.service.PackageService;
import com.holder.saas.store.takeaway.consumers.service.rpc.ItemFeignClient;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 外卖菜品套餐服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PackageServiceImpl extends ServiceImpl<PackageMapper, PackageDO> implements PackageService {

    private final PackageMapper packageMapper;

    private final ItemFeignClient itemFeignClient;

    private final ItemPackageMapstruct itemPackageMapstruct;

    @Override
    public List<String> listByTakeoutItemGuids(List<String> takeoutItemGuids) {
        QueryWrapper<PackageDO> qw = new QueryWrapper<>();
        qw.lambda().in(PackageDO::getTakeoutItemGuid, takeoutItemGuids);
        qw.lambda().select(PackageDO::getTakeoutItemGuid);
        List<PackageDO> list = list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(PackageDO::getTakeoutItemGuid).collect(Collectors.toList());
    }

    @Override
    public void updateByTakeoutItem(List<ItemDO> fixItemBuffer, List<SkuInfoPkgDTO> skuInfoPkgList) {
        if (CollectionUtils.isEmpty(fixItemBuffer)) {
            return;
        }
        // 先删除对应的套餐明细
        List<String> itemGuidList = fixItemBuffer.stream()
                .map(ItemDO::getItemGuid)
                .collect(Collectors.toList());
        packageMapper.deleteBatchByItem(itemGuidList);
        // 查询套餐信息
        if (CollectionUtils.isEmpty(skuInfoPkgList)) {
            log.info("修复数据中查询套餐信息:{}", JacksonUtils.writeValueAsString(fixItemBuffer));
            Set<String> collect = fixItemBuffer.stream().map(ItemDO::getErpItemGuid).collect(Collectors.toSet());
            skuInfoPkgList = itemFeignClient.listPkgInfoByItemGuid(Lists.newArrayList(collect));
        }
        if (CollectionUtils.isEmpty(skuInfoPkgList)) {
            return;
        }
        List<String> list = skuInfoPkgList.stream()
                .map(SkuInfoPkgDTO::getParentGuid)
                .collect(Collectors.toList());
        List<ItemDO> havePackageList = fixItemBuffer.stream()
                .filter(e -> list.contains(e.getErpItemGuid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(havePackageList)) {
            return;
        }
        log.info("保存套餐明细信息：skuInfoPkgList：{}，havePackageList：{}", JacksonUtils.writeValueAsString(skuInfoPkgList),
                JacksonUtils.writeValueAsString(havePackageList));
        //存储套餐明细
        List<PackageDO> packageInfoList = Lists.newArrayList();
        Map<String, List<SkuInfoPkgDTO>> packageMap = skuInfoPkgList.stream().collect(Collectors.groupingBy(SkuInfoPkgDTO::getParentGuid));
        havePackageList.forEach(e -> {
            List<SkuInfoPkgDTO> dtoList = packageMap.get(e.getErpItemGuid());
            if (CollectionUtils.isNotEmpty(dtoList)) {
                dtoList.forEach(d -> {
                    PackageDO packageDO = itemPackageMapstruct.parseToPackageDO(d);
                    packageDO.setTakeoutItemGuid(e.getItemGuid());
                    packageInfoList.add(packageDO);
                });
            }
        });
        if (CollectionUtils.isNotEmpty(havePackageList)) {
            saveBatch(packageInfoList);
        }
    }

}
