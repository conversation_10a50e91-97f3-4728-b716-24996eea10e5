package com.holder.saas.store.takeaway.consumers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.consumers.entity.domain.GroupBuyDO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;


public interface GroupBuyService extends IService<GroupBuyDO> {


    MtCouponDoCheckRespDTO checkTicket(CouPonReqDTO couPonReqDTO);

    MtCouponDoCheckRespDTO doCheck(CouPonReqDTO couPonReqDTO);

    MtCouponPreRespDTO preCheck(CouPonPreReqDTO couPonPreReqDTO);

    MtDelCouponRespDTO cancalTicket(CouponDelReqDTO couponDelReqDTO);

    MtCouponTradeDetailRespDTO queryGroupTradeDetail(CouPonReqDTO couPonReqDTO);

}
