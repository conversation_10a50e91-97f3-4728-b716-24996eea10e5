package com.holder.saas.store.takeaway.consumers.controller;


import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.request.BusinessTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 商户后台数据报表订单业务接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@RestController
@RequestMapping("/order_report")
@Api(description = "商户后台数据报表订单业务接口")
@Slf4j
public class BusinessOrderReportController {

    private final OrderService orderService;

    @Autowired
    public BusinessOrderReportController(OrderService orderService) {
        this.orderService = orderService;
    }

    @ApiOperation(value = "获取订单统计分页数据")
    @PostMapping("/takeoutOrder/page")
    public Page<BusinessTakeoutOrderRespDTO> getTakeoutOrderPage(@RequestBody @Validated BusinessTakeoutOrderReqDTO reqDTO) {
        return orderService.getTakeoutOrderPage(reqDTO);
    }

    @ApiOperation(value = "统计订单 订单明细")
    @GetMapping("/takeoutOrder/detail")
    public BusinessTakeoutOrderDetailRespDTO getTakeoutOrderDetail(@RequestParam("orderGuid") String orderGuid) {
        return orderService.getTakeoutOrderDetail(orderGuid);
    }
}