package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.manage.OrderConsumeManage;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.ConsumeMode;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.TAKEAWAY_CONSUMERS_MT_ORDER_TOPIC,
        tags = {RocketMqConfig.TAKEAWAY_CONSUMERS_MT_ORDER_TAG},
        consumerGroup = RocketMqConfig.TAKEAWAY_MT_ORDER_CONSUMERS_GROUP
)
@AllArgsConstructor
public class ConsumerMtUnOrderListener extends AbstractRocketMqConsumer<RocketMqTopic, UnOrder>{

    private final OrderConsumeManage orderConsumeManage;
    @Override
    public boolean consumeMsg(UnOrder unOrder, MessageExt messageExt){
        if (log.isInfoEnabled()) {
            log.info("收到美团回调消息，unOrder:{}", JacksonUtils.writeValueAsString(unOrder));
        }
        return orderConsumeManage.process(unOrder);
    }


}
