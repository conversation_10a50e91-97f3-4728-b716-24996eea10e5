package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.consumers.service.rpc.ErpFeignService;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holder.saas.store.takeaway.consumers.utils.ThrowableExtUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.ERP_MESSAGE_TOPIC,
        tags = RocketMqConfig.ERP_REDUCE_TAG,
        consumerGroup = RocketMqConfig.ERP_MESSAGE_GROUP)
public class ErpListener extends AbstractRocketMqConsumer<RocketMqTopic, OrderSkuDTO> {

    private final DynamicHelper dynamicHelper;

    private final ErpFeignService erpFeignService;

    @Autowired
    public ErpListener(ErpFeignService erpFeignService, DynamicHelper dynamicHelper) {
        this.erpFeignService = erpFeignService;
        this.dynamicHelper = dynamicHelper;
    }

    @Override
    public boolean consumeMsg(OrderSkuDTO orderSkuDTO, MessageExt messageExt) {
        String orderId = orderSkuDTO.getOrderId();
        if (log.isInfoEnabled()) {
            log.info("订单[{}]扣减简易库存，入参:{}", orderId, JacksonUtils.writeValueAsString(orderSkuDTO));
        }
        UserContextUtils.put(messageExt.getProperty(RocketMqConfig.USER_INFO));

        // 切库
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (log.isInfoEnabled()) {
            log.info("扣减简易库存:订单[{}]根据enterpriseGuid({})切换数据源", orderId, enterpriseGuid);
        }
        dynamicHelper.changeDatasource(enterpriseGuid);

        try {
            erpFeignService.reduceStockForOrder(orderSkuDTO);
            log.info("订单[{}]扣减简易库存消费成功", orderId);
        } catch (Exception e) {
            log.error("订单[{}]扣减简易库存消费发生异常：{}", orderId, ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            dynamicHelper.removeThreadLocalDatabaseInfo();
            UserContextUtils.remove();
        }
        return true;
    }

}
