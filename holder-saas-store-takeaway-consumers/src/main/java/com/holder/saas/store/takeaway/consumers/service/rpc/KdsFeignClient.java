package com.holder.saas.store.takeaway.consumers.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdPointItemDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(value = "holder-saas-store-kds", fallbackFactory = KdsFeignClient.ServiceFallBack.class)
public interface KdsFeignClient {

    /**
     * 根据商品sku查询绑定菜品
     *
     * @param prdPointItemQueryReqDTO
     * @return
     */
    @PostMapping("/production/query_item_by_sku")
    List<PrdPointItemDTO> queryBoundItemBySku(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO);

    /**
     * 查询kds所有绑定的菜品
     *
     * @return
     */
    @PostMapping("/production/query_all_item")
    List<PrdPointItemDTO> queryAllBoundItem();

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<KdsFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public KdsFeignClient create(Throwable cause) {
            return new KdsFeignClient() {

                @Override
                public List<PrdPointItemDTO> queryBoundItemBySku(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryBoundItemBySku",
                            JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<PrdPointItemDTO> queryAllBoundItem() {
                    log.error(HYSTRIX_PATTERN, "queryAllBoundItem",
                            "",
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
