package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.AuthService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByTypeReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreUsedReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/takeout")
@Api(description = "美团相关接口")
public class TakeoutAuthController {

    private final AuthService authService;

    @Autowired
    public TakeoutAuthController(AuthService authService) {
        this.authService = authService;
    }

    @PostMapping(value = "/query_auth_by_type")
    @ApiOperation(value = "门店授权页面（根据平台）", notes = "门店授权页面")
    public List<StoreAuthDTO> queryAuthByType(@RequestBody @Validated StoreAuthByTypeReqDTO storeAuthByTypeReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询授权页面（根据平台）：入参{}", JacksonUtils.writeValueAsString(storeAuthByTypeReqDTO));
        }
        return authService.queryAuthByType(storeAuthByTypeReqDTO);
    }

    @PostMapping(value = "/query_takeout_auth_by_store")
    @ApiOperation(value = "门店授权页面（根据门店）", notes = "门店授权页面")
    public List<StoreAuthDTO> queryTakeoutAuthByStore(@RequestBody @Validated StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询门店授权页面（根据门店）：入参{}", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO));
        }
        return authService.queryTakeoutAuthByStore(storeAuthByStoreReqDTO);
    }

    @PostMapping(value = "/query_tuangou_auth_by_store")
    @ApiOperation(value = "门店授权页面（根据门店）", notes = "门店授权页面")
    public List<StoreAuthDTO> queryTuangouByStore(@RequestBody @Validated StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询门店授权页面（根据门店）：入参{}", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO));
        }
        return authService.queryTuanGouByStore(storeAuthByStoreReqDTO);
    }

    @PostMapping(value = "/query_store_used")
    @ApiOperation(value = "查询已绑定或已产生订单数据的门店列表", notes = "查询已绑定或已产生订单数据的门店列表")
    public List<StoreDTO> queryStoreUsed(@RequestBody @Validated StoreUsedReqDTO storeUsedReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询已绑定或已产生订单数据的门店列表：入参{}", JacksonUtils.writeValueAsString(storeUsedReqDTO));
        }
        return authService.queryStoreUsed(storeUsedReqDTO);
    }


    @PostMapping(value = "/update_delivery")
    @ApiOperation(value = "修改门店配送方式", notes = "修改门店配送方式")
    public Boolean updateDeliveryType(@RequestBody @Validated StoreAuthDTO storeAuthDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改门店配送方式：入参{}", JacksonUtils.writeValueAsString(storeAuthDTO));
        }
        return authService.updateDeliveryType(storeAuthDTO);
    }
}
