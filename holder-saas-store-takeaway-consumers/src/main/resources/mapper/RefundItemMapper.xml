<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.store.takeaway.consumers.mapper.RefundItemMapper">

    <select id="listItemGroupByOrderItemGuid"
            resultType="com.holder.saas.store.takeaway.consumers.entity.domain.RefundItemDO">
        select
            order_item_guid,
            sum(item_count) as "itemCount"
        from
            hst_takeout_refund_item
        where
            order_guid = #{orderGuid} and is_refund_success = 0
        group by
            order_item_guid
    </select>


    <update id="updateRefundSuccess">
        UPDATE
            hst_takeout_refund_item
        SET
            is_refund_success = 1
        WHERE
        refund_guid IN
        (SELECT temp.refund_guid
            from (SELECT max(refund_guid) as "refund_guid"
                    from hst_takeout_refund_item where order_guid = #{orderGuid} ) temp )

    </update>
</mapper>
