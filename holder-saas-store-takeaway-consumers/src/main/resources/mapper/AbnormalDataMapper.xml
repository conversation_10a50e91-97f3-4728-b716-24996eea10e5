<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.store.takeaway.consumers.mapper.AbnormalDataMapper">

    <select id="page"
            resultType="com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO">
        SELECT
            t.storeGuid AS storeGuid,
            t.storeName AS storeName,
            t.orderType AS orderType,
            t.itemName AS itemName,
            t.takeoutItemNumber AS takeoutItemNumber,
            t.abnormalOrderNum AS abnormalOrderNum,
            t.totalPrice AS totalPrice
        FROM (
            SELECT
                tad.takeout_item_id as id,
                tad.store_guid AS storeGuid,
                tad.store_name AS storeName,
                tad.order_sub_type AS orderType,
                tad.item_name AS itemName,
                tad.third_sku_id AS takeoutItemNumber,
                COUNT(*) AS abnormalOrderNum,
                ROUND( SUM( IFNULL( tad.item_price * tad.item_count, 0 )), 2 ) AS totalPrice,
                MAX(tad.takeout_create_time) AS gmtCreate
            FROM
                hst_takeout_abnormal_data tad
            <where>
                tad.is_delete = 0
                AND tad.takeout_item_id NOT IN (
                    SELECT
                        DISTINCT tad.takeout_item_id
                    FROM
                        hst_takeout_abnormal_data tad
                    LEFT JOIN hst_takeout_fix_item tfi ON tfi.third_sku_id = tad.third_sku_id
                    LEFT JOIN hst_takeout_fix_record tfr ON tfr.id = tfi.record_id
                    WHERE
                        tad.takeout_create_time &gt;= tfr.start_time
                        AND tad.takeout_create_time &lt;= tfr.end_time
                        AND tad.store_guid = tfi.store_guid
                        AND tad.order_sub_type = tfi.order_sub_type
                        AND tfr.`status` = 0
                )
                <if test="query.startDateTime != null">
                    <![CDATA[AND tad.takeout_create_time >= #{query.startDateTime}]]>
                </if>
                <if test="query.endDateTime != null">
                    <![CDATA[AND tad.takeout_create_time <= #{query.endDateTime}]]>
                </if>
                <if test="query.orderType != null">
                    AND tad.order_sub_type = #{query.orderType}
                </if>
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND tad.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" separator="
                             ," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.keywords != null and query.keywords != ''">
                    AND (tad.item_name LIKE CONCAT('%',#{query.keywords},'%')
                    OR tad.third_sku_id LIKE CONCAT('%',#{query.keywords},'%'))
                </if>
            </where>
            GROUP BY
                tad.third_sku_id,
                tad.store_guid
        ) t
        <if test="query.sequenceType == null">
            ORDER BY
            t.gmtCreate DESC, t.id asc
        </if>
        <if test="query.sequenceType == 0">
            ORDER BY
            t.totalPrice ASC,t.gmtCreate DESC, t.id asc
        </if>
        <if test="query.sequenceType == 1">
            ORDER BY
            t.totalPrice DESC,t.gmtCreate DESC, t.id asc
        </if>
    </select>

    <select id="listDataFix"
            resultType="com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO">
        SELECT
            t.storeGuid AS storeGuid,
            t.storeName AS storeName,
            t.orderSubType AS orderSubType,
            t.takeoutItemName AS takeoutItemName,
            t.thirdSkuId AS thirdSkuId,
            t.takeoutOrderCount AS takeoutOrderCount
        FROM
        (
            SELECT
                tad.store_guid AS storeGuid,
                tad.store_name AS storeName,
                tad.order_sub_type AS orderSubType,
                tad.item_name AS takeoutItemName,
                tad.third_sku_id AS thirdSkuId,
                COUNT(*) AS takeoutOrderCount,
                ROUND( SUM( IFNULL( tad.item_price * tad.item_count, 0 )), 2 ) AS totalPrice,
                MAX( tad.takeout_create_time ) AS gmtCreate
            FROM
                hst_takeout_abnormal_data tad
            <where>
                tad.is_delete = 0
                AND tad.takeout_item_id NOT IN (
                SELECT
                DISTINCT tad.takeout_item_id
                FROM
                hst_takeout_abnormal_data tad
                LEFT JOIN hst_takeout_fix_item tfi ON tfi.third_sku_id = tad.third_sku_id
                LEFT JOIN hst_takeout_fix_record tfr ON tfr.id = tfi.record_id
                WHERE
                tad.takeout_create_time &gt;= tfr.start_time
                AND tad.takeout_create_time &lt;= tfr.end_time
                AND tad.store_guid = tfi.store_guid
                AND tad.order_sub_type = tfi.order_sub_type
                AND tfr.`status` = 0
                )
                <if test="query.startDateTime != null">
                    <![CDATA[AND tad.takeout_create_time >= #{query.startDateTime}]]>
                </if>
                <if test="query.endDateTime != null">
                    <![CDATA[AND tad.takeout_create_time <= #{query.endDateTime}]]>
                </if>
            </where>
            GROUP BY tad.third_sku_id,tad.store_guid
        ) t
        <where>
            <if test="query.spliceList != null and query.spliceList.size() > 0">
                CONCAT(t.thirdSkuId,'-',t.storeGuid) IN
                <foreach collection="query.spliceList" index="index" item="splice" open="(" separator="," close=")">
                    #{splice}
                </foreach>
            </if>
        </where>
        <if test="query.sequenceType == null">
            ORDER BY
            t.gmtCreate DESC
        </if>
        <if test="query.sequenceType == 0">
            ORDER BY
            t.totalPrice ASC
        </if>
        <if test="query.sequenceType == 1">
            ORDER BY
            t.totalPrice DESC
        </if>
    </select>

    <select id="listFixItem" resultType="com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO">
        SELECT
            tad.takeout_item_id AS id,
            hti.item_guid AS itemGuid,
            tad.store_guid,
            tad.item_count,
            tad.third_sku_id
        FROM
            hst_takeout_abnormal_data tad join hst_takeout_item hti on tad.takeout_item_id = hti.id
        <where>
            tad.is_delete = 0
            <if test="query.takeoutAndStore != null and query.takeoutAndStore.size() > 0">
                AND tad.third_sku_id IN
                <foreach collection="query.takeoutAndStore" index="index" item="item" open="(" separator="," close=")">
                    #{item.takeoutItemNumber}
                </foreach>
                AND tad.store_guid IN
                <foreach collection="query.takeoutAndStore" index="index" item="item" open="(" separator="," close=")">
                    #{item.storeGuid}
                </foreach>
            </if>
            <if test="query.startDateTime != null">
                <![CDATA[AND tad.takeout_create_time >= #{query.startDateTime}]]>
            </if>
            <if test="query.endDateTime != null">
                <![CDATA[AND tad.takeout_create_time <= #{query.endDateTime}]]>
            </if>
        </where>
        order by id desc
        limit 0,500
    </select>

    <insert id="insertIgnore" parameterType="java.util.List">
        INSERT IGNORE INTO hst_takeout_abnormal_data
        (
            `takeout_item_id`,
            `store_guid`,
            `store_name`,
            `order_sub_type`,
            `item_name`,
            `item_price`,
            `item_count`,
            `third_sku_id`,
            `takeout_create_time`
        )
        VALUES
        <foreach collection="list" item="data" index="index" open="" separator="," close="">
            (
            <trim suffixOverrides=",">
                #{data.takeoutItemId},
                #{data.storeGuid},
                #{data.storeName},
                #{data.orderSubType},
                #{data.itemName},
                #{data.itemPrice},
                #{data.itemCount},
                #{data.thirdSkuId},
                #{data.takeoutCreateTime},
            </trim>
            )
        </foreach>
    </insert>

</mapper>
