<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.store.takeaway.consumers.mapper.ItemExtendsMapper">

    <select id="getTotalCostPrice" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(IFNULL(cost_price, 0)), 0)
        FROM
            hst_takeout_item_extends
        WHERE
            is_delete = 0
        and order_guid IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
