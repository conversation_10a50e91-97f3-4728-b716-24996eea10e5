<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.store.takeaway.consumers.mapper.FixRecordMapper">


    <select id="pageInfo" resultType="com.holderzone.saas.store.dto.takeaway.TakeoutFixRecordDTO">
        select
            id as "recordId",
            start_time,
            end_time,
            fix_count,
            gmt_modified,
            modified_user_name
        from
            hst_takeout_fix_record
        where
            status = 0
        order by gmt_modified desc
    </select>
</mapper>
