<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.store.takeaway.consumers.mapper.PackageMapper">

    <update id="deleteBatchByItem">
        update hst_takeout_package set is_delete = 1
        where takeout_item_guid in
        <foreach collection="itemGuidList" item="itemGuid" open="(" separator="," close=")">
            #{itemGuid}
        </foreach>
    </update>
</mapper>
