<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.store.takeaway.consumers.mapper.ItemMapper">

    <update id="updateBatchItem">
        <foreach collection="list" item="item" separator=";" >
            update hst_takeout_item set erp_item_name = #{item.erpItemName}, erp_item_price = #{item.erpItemPrice},
            erp_item_sku_guid = #{item.erpItemSkuGuid} where id = #{item.id}
        </foreach>
    </update>

    <update id="fixBatchItem">
        <foreach collection="list" item="item" separator=";" >
            update
                hst_takeout_item
            set
                actual_item_count = #{item.actualItemCount},
                erp_item_name = #{item.erpItemName},
                erp_item_price = #{item.erpItemPrice},
                takeaway_accounting_price = #{item.takeawayAccountingPrice},
                erp_item_sku_guid = #{item.erpItemSkuGuid}
            where
                id = #{item.id}
        </foreach>
    </update>

    <update id="clearErpItemSkuGuid">
        update
            hst_takeout_item
        set
            actual_item_count = 1,
            erp_item_name = null,
            erp_item_price = null,
            erp_item_sku_guid = null
        where
            id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateBatchRefundCount">
        <foreach collection="itemList" item="item" separator=";" >
            update
                hst_takeout_item
            set
                refund_count = #{item.refundCount}
            where
                item_guid = #{item.itemGuid}
        </foreach>
    </update>

    <update id="updateRefundCountByOrderGuid">
        update
            hst_takeout_item
        set
            refund_count = item_count
        where
            order_guid = #{orderGuid}
    </update>

    <select id="pageOrderItem" resultType="com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO">
        select
            item.id,
            item.store_guid,
            item.order_sub_type,
            item.third_sku_id,
            item.erp_item_sku_guid,
            item.erp_item_price,
            item.erp_item_name,
            case when mapping.mapper_guid is not null then mapping.mapper_guid else item.item_sku end "item_sku"
        from hst_takeout_item item
        left join hst_takeout_item_mapping mapping on item.item_sku = mapping.mapper_guid
        where
            item.gmt_create >= #{query.dateBegin}
        and item.item_sku is not null
        and item.item_sku != '0'
        and item.item_sku != ''
        and item.third_sku_id is not null
        and item.erp_item_name is null
    </select>

    <select id="pageAbnormalData"
            resultType="com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO">
        SELECT
            t.storeGuid AS storeGuid,
            t.storeName AS storeName,
            t.orderType AS orderType,
            t.itemName AS itemName,
            t.takeoutItemNumber AS takeoutItemNumber,
            t.abnormalOrderNum AS abnormalOrderNum,
            t.totalPrice AS totalPrice
        FROM (
            SELECT
                ti.store_guid AS storeGuid,
                ti.store_name AS storeName,
                ti.order_sub_type AS orderType,
                ti.item_name AS itemName,
                ti.third_sku_id AS takeoutItemNumber,
                COUNT(*) AS abnormalOrderNum,
                ROUND( SUM( IFNULL( ti.item_price * item_count, 0 )), 2 ) AS totalPrice,
                MAX(ti.gmt_create) AS gmtCreate
            FROM
                hst_takeout_item ti
            <where>
                ti.third_sku_id IS NOT NULL
                AND ti.third_sku_id != ''
                AND ISNULL(ti.erp_item_name)
                AND ti.id NOT IN (
                    SELECT
                        DISTINCT ti.id
                    FROM
                        hst_takeout_item ti
                    LEFT JOIN hst_takeout_fix_item tfi ON tfi.third_sku_id = ti.third_sku_id
                    LEFT JOIN hst_takeout_fix_record tfr ON tfr.id = tfi.record_id
                    WHERE
                        ti.gmt_create &gt;= tfr.start_time
                        AND ti.gmt_create &lt;= tfr.end_time
                        AND ti.store_guid = tfi.store_guid
                        AND ti.order_sub_type = tfi.order_sub_type
                        AND tfr.`status` = 0
                )
                <if test="query.startDateTime != null">
                    <![CDATA[AND ti.gmt_create >= #{query.startDateTime}]]>
                </if>
                <if test="query.endDateTime != null">
                    <![CDATA[AND ti.gmt_create <= #{query.endDateTime}]]>
                </if>
                <if test="query.orderType != null">
                    AND ti.order_sub_type = #{query.orderType}
                </if>
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND ti.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" separator="
                         ," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.keywords != null and query.keywords != ''">
                    AND (ti.item_name LIKE CONCAT('%',#{query.keywords},'%')
                    OR ti.third_sku_id LIKE CONCAT('%',#{query.keywords},'%'))
                </if>
            </where>
                GROUP BY
                    ti.third_sku_id,
                    ti.store_guid
        ) t
        <if test="query.sequenceType == null">
            ORDER BY
                t.gmtCreate DESC
        </if>
        <if test="query.sequenceType == 0">
            ORDER BY
                t.totalPrice ASC
        </if>
        <if test="query.sequenceType == 1">
            ORDER BY
                t.totalPrice DESC
        </if>
    </select>

    <select id="listDataFix"
            resultType="com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO">
        SELECT
            ti.store_guid AS storeGuid,
            ti.store_name AS storeName,
            ti.order_sub_type AS orderSubType,
            ti.item_name AS takeoutItemName,
            ti.third_sku_id AS thirdSkuId,
            COUNT(*) AS takeoutOrderCount
        FROM
            hst_takeout_item ti
        <where>
            ti.third_sku_id IS NOT NULL
            AND ISNULL( ti.erp_item_name )
            <if test="query.spliceList != null and query.spliceList.size() > 0">
                AND CONCAT(ti.third_sku_id,'-',ti.store_guid) IN
                <foreach collection="query.spliceList" index="index" item="splice" open="(" separator="," close=")">
                    #{splice}
                </foreach>
            </if>
            <if test="query.startDateTime != null">
                <![CDATA[AND ti.gmt_create >= #{query.startDateTime}]]>
            </if>
            <if test="query.endDateTime != null">
                <![CDATA[AND ti.gmt_create <= #{query.endDateTime}]]>
            </if>
        </where>
        GROUP BY ti.third_sku_id,ti.store_guid
    </select>

    <select id="filterNoRequiredFixList"
            resultType="com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO">
        SELECT
            t.id,
            t.store_guid,
            t.erp_item_sku_guid,
            t.actual_item_count,
            t.gmt_create,
            o.order_view_id,
            o.order_guid
        FROM
            hst_takeout_item t
        join hst_takeout_order o on t.order_guid = o.order_guid
        <where>
            t.id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            and o.order_status not in ('-1','0')
        </where>
    </select>
</mapper>
