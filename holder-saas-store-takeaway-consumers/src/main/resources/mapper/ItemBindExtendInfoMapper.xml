<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.store.takeaway.consumers.mapper.ItemBindExtendInfoMapper">

    <update id="syncTcdItemMappingCount">
        <foreach collection="list" item="item" separator=";">
            update hst_takeout_item_bind_extend_info
            set un_item_sku_id = #{item.targetUnItemSkuId}
            where un_item_sku_id = #{item.sourceUnItemSkuId} and takeout_type = 3
        </foreach>
    </update>
</mapper>
