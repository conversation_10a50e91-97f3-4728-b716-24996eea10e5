<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.store.takeaway.consumers.mapper.OrderMapper">

    <resultMap id="OrderResultMap" type="com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="shop_name" jdbcType="VARCHAR" property="shopName"/>
        <result column="order_guid" jdbcType="VARCHAR" property="orderGuid"/>
        <result column="order_status" jdbcType="TINYINT" property="orderStatus"/>
        <result column="order_tag_status" jdbcType="TINYINT" property="orderTagStatus"/>
        <result column="order_type" jdbcType="TINYINT" property="orderType"/>
        <result column="order_sub_type" jdbcType="TINYINT" property="orderSubType"/>
        <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="enterprise_guid" jdbcType="VARCHAR" property="enterpriseGuid"/>
        <result column="store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="order_view_id" jdbcType="VARCHAR" property="orderViewId"/>
        <result column="order_day_sn" jdbcType="VARCHAR" property="orderDaySn"/>
        <result column="order_remark" jdbcType="VARCHAR" property="orderRemark"/>
        <result column="is_reserve" jdbcType="TINYINT" property="reserve"/>
        <result column="is_reminded" jdbcType="TINYINT" property="reminded"/>
        <result column="remind_time" jdbcType="TIMESTAMP" property="remindTime"/>
        <result column="item_count" jdbcType="DECIMAL" property="itemCount"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone"/>
        <result column="privacy_phone" jdbcType="VARCHAR" property="privacyPhone"/>
        <result column="customer_address" jdbcType="VARCHAR" property="customerAddress"/>
        <result column="recipient_address_desensitization" jdbcType="VARCHAR" property="recipientAddressDesensitization"/>
        <result column="customer_number" jdbcType="VARCHAR" property="customerNumber"/>
        <result column="is_first_order" jdbcType="TINYINT" property="firstOrder"/>
        <result column="ship_latitude" jdbcType="VARCHAR" property="shipLatitude"/>
        <result column="ship_longitude" jdbcType="VARCHAR" property="shipLongitude"/>
        <result column="shipper_name" jdbcType="VARCHAR" property="shipperName"/>
        <result column="shipper_phone" jdbcType="VARCHAR" property="shipperPhone"/>
        <result column="is_third_shipper" jdbcType="TINYINT" property="thirdShipper"/>
        <result column="is_invoiced" jdbcType="TINYINT" property="invoiced"/>
        <result column="invoice_type" jdbcType="VARCHAR" property="invoiceType"/>
        <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle"/>
        <result column="taxpayer_id" jdbcType="VARCHAR" property="taxpayerId"/>
        <result column="total" jdbcType="DECIMAL" property="total"/>
        <result column="ship_total" jdbcType="DECIMAL" property="shipTotal"/>
        <result column="item_total" jdbcType="DECIMAL" property="itemTotal"/>
        <result column="package_total" jdbcType="DECIMAL" property="packageTotal"/>
        <result column="discount_total" jdbcType="DECIMAL" property="discountTotal"/>
        <result column="enterprise_discount" jdbcType="DECIMAL" property="enterpriseDiscount"/>
        <result column="platform_discount" jdbcType="DECIMAL" property="platformDiscount"/>
        <result column="other_discount" jdbcType="DECIMAL" property="otherDiscount"/>
        <result column="service_fee_rate" jdbcType="DECIMAL" property="serviceFeeRate"/>
        <result column="service_fee" jdbcType="DECIMAL" property="serviceFee"/>
        <result column="customer_actual_pay" jdbcType="DECIMAL" property="customerActualPay"/>
        <result column="customer_refund" jdbcType="DECIMAL" property="customerRefund"/>
        <result column="customer_refund_item" jdbcType="VARCHAR" property="customerRefundItem"/>
        <result column="shop_total" jdbcType="DECIMAL" property="shopTotal"/>
        <result column="is_online_pay" jdbcType="TINYINT" property="onlinePay"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="active_time" jdbcType="TIMESTAMP" property="activeTime"/>
        <result column="accept_time" jdbcType="TIMESTAMP" property="acceptTime"/>
        <result column="accept_staff_guid" jdbcType="VARCHAR" property="acceptStaffGuid"/>
        <result column="accept_staff_name" jdbcType="VARCHAR" property="acceptStaffName"/>
        <result column="accept_device_id" jdbcType="VARCHAR" property="acceptDeviceId"/>
        <result column="accept_device_type" jdbcType="TINYINT" property="acceptDeviceType"/>
        <result column="estimate_delivered_time" jdbcType="TIMESTAMP" property="estimateDeliveredTime"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="delivered_time" jdbcType="TIMESTAMP" property="deliveredTime"/>
        <result column="cancel_req_time" jdbcType="TIMESTAMP" property="cancelReqTime"/>
        <result column="cancel_req_reason" jdbcType="VARCHAR" property="cancelReqReason"/>
        <result column="cancel_req_expire_time" jdbcType="TIMESTAMP" property="cancelReqExpireTime"/>
        <result column="cancel_reply_time" jdbcType="TIMESTAMP" property="cancelReplyTime"/>
        <result column="cancel_reply_message" jdbcType="VARCHAR" property="cancelReplyMessage"/>
        <result column="cancel_reply_staff_guid" jdbcType="VARCHAR" property="cancelReplyStaffGuid"/>
        <result column="cancel_reply_staff_name" jdbcType="VARCHAR" property="cancelReplyStaffName"/>
        <result column="refund_req_time" jdbcType="TIMESTAMP" property="refundReqTime"/>
        <result column="refund_req_reason" jdbcType="VARCHAR" property="refundReqReason"/>
        <result column="refund_req_expire_time" jdbcType="TIMESTAMP" property="refundReqExpireTime"/>
        <result column="refund_reply_time" jdbcType="TIMESTAMP" property="refundReplyTime"/>
        <result column="refund_reply_message" jdbcType="VARCHAR" property="refundReplyMessage"/>
        <result column="refund_reply_staff_guid" jdbcType="VARCHAR" property="refundReplyStaffGuid"/>
        <result column="refund_reply_staff_name" jdbcType="VARCHAR" property="refundReplyStaffName"/>
        <result column="order_status_tcd" jdbcType="VARCHAR" property="orderStatusTcd"/>
        <result column="write_off_code" jdbcType="VARCHAR" property="writeOffCode"/>
        <result column="order_type_tcd" jdbcType="VARCHAR" property="orderTypeTcd"/>
        <result column="is_refund_success" jdbcType="TINYINT" property="refundSuccess"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
        <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason"/>
        <result column="cancel_staff_guid" jdbcType="VARCHAR" property="cancelStaffGuid"/>
        <result column="cancel_staff_name" jdbcType="VARCHAR" property="cancelStaffName"/>
        <result column="is_cancel_as_reject" jdbcType="TINYINT" property="cancelAsReject"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="business_day" jdbcType="TIMESTAMP" property="businessDay"/>
        <result column="adjust_state" jdbcType="TINYINT" property="adjustState"/>
        <result column="is_auto_accept" jdbcType="TINYINT" property="isAutoAccept"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <collection property="arrayOfItem" columnPrefix="item_" resultMap="ItemResultMap"/>
        <collection property="arrayOfRemind" columnPrefix="remind_" resultMap="RemindResultMap"/>
        <collection property="arrayOfDiscount" columnPrefix="discount_" resultMap="DiscountResultMap"/>
        <collection property="arrayOfLog" columnPrefix="log_" resultMap="LogResultMap"/>
    </resultMap>

    <resultMap id="ItemResultMap" type="com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="order_guid" jdbcType="VARCHAR" property="orderGuid"/>
        <result column="item_sku" jdbcType="VARCHAR" property="itemSku"/>
        <result column="item_guid" jdbcType="VARCHAR" property="itemGuid"/>
        <result column="item_name" jdbcType="VARCHAR" property="itemName"/>
        <result column="item_name_platform" jdbcType="VARCHAR" property="itemNamePlatform"/>
        <result column="order_sub_type" jdbcType="TINYINT" property="orderSubType"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="item_unit" jdbcType="VARCHAR" property="itemUnit"/>
        <result column="item_price" jdbcType="DECIMAL" property="itemPrice"/>
        <result column="item_count" jdbcType="DECIMAL" property="itemCount"/>
        <result column="actual_item_count" jdbcType="DECIMAL" property="actualItemCount"/>
        <result column="item_total" jdbcType="DECIMAL" property="itemTotal"/>
        <result column="item_spec" jdbcType="VARCHAR" property="itemSpec"/>
        <result column="item_property" jdbcType="VARCHAR" property="itemProperty"/>
        <result column="box_price" jdbcType="DECIMAL" property="boxPrice"/>
        <result column="box_count" jdbcType="DECIMAL" property="boxCount"/>
        <result column="box_total" jdbcType="DECIMAL" property="boxTotal"/>
        <result column="cart_id" jdbcType="TINYINT" property="cartId"/>
        <result column="discount_price" jdbcType="DECIMAL" property="discountPrice"/>
        <result column="discount_ratio" jdbcType="DECIMAL" property="discountRatio"/>
        <result column="discount_total" jdbcType="DECIMAL" property="discountTotal"/>
        <result column="actual_price" jdbcType="DECIMAL" property="actualPrice"/>
        <result column="settle_type" jdbcType="TINYINT" property="settleType"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="business_day" jdbcType="TIMESTAMP" property="businessDay"/>
        <result column="is_adjust_item" jdbcType="TINYINT" property="isAdjustItem"/>
        <result column="erp_item_sku_guid" jdbcType="VARCHAR" property="erpItemSkuGuid"/>
        <result column="erp_item_name" jdbcType="VARCHAR" property="erpItemName"/>
        <result column="erp_item_price" jdbcType="VARCHAR" property="erpItemPrice"/>
        <result column="third_sku_id" jdbcType="VARCHAR" property="thirdSkuId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="sub_item_info" jdbcType="VARCHAR" property="subItemInfo"/>
    </resultMap>

    <resultMap id="RemindResultMap" type="com.holder.saas.store.takeaway.consumers.entity.domain.RemindDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="remind_guid" jdbcType="VARCHAR" property="remindGuid"/>
        <result column="order_guid" jdbcType="VARCHAR" property="orderGuid"/>
        <result column="remind_id" jdbcType="VARCHAR" property="remindId"/>
        <result column="is_replied" jdbcType="TINYINT" property="replied"/>
        <result column="reply_content" jdbcType="VARCHAR" property="replyContent"/>
        <result column="reply_staff_guid" jdbcType="VARCHAR" property="replyStaffGuid"/>
        <result column="reply_staff_name" jdbcType="VARCHAR" property="replyStaffName"/>
        <result column="remind_time" jdbcType="TIMESTAMP" property="remindTime"/>
        <result column="reply_time" jdbcType="TIMESTAMP" property="replyTime"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <resultMap id="DiscountResultMap" type="com.holder.saas.store.takeaway.consumers.entity.domain.DiscountDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="discount_guid" jdbcType="VARCHAR" property="discountGuid"/>
        <result column="order_guid" jdbcType="VARCHAR" property="orderGuid"/>
        <result column="discount_name" jdbcType="VARCHAR" property="discountName"/>
        <result column="discount_remark" jdbcType="VARCHAR" property="discountRemark"/>
        <result column="total_discount" jdbcType="DECIMAL" property="totalDiscount"/>
        <result column="enterprise_discount" jdbcType="DECIMAL" property="enterpriseDiscount"/>
        <result column="platform_discount" jdbcType="DECIMAL" property="platformDiscount"/>
        <result column="other_discount" jdbcType="DECIMAL" property="otherDiscount"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <resultMap id="LogResultMap" type="com.holder.saas.store.takeaway.consumers.entity.domain.LogDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_guid" jdbcType="VARCHAR" property="orderGuid"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="time" jdbcType="TIMESTAMP" property="time"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="body" jdbcType="VARCHAR" property="body"/>
        <result column="is_show_in_web_page" jdbcType="TINYINT" property="showInWebPage"/>
        <result column="is_show_in_endpoint" jdbcType="TINYINT" property="showInEndpoint"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <resultMap id="OrderListResultMap" type="com.holderzone.saas.store.dto.takeaway.TakeoutOrderListDTO">
        <result column="delivery_type" jdbcType="TINYINT" property="deliveryType"/>
        <result column="order_guid" jdbcType="VARCHAR" property="orderGuid"/>
        <result column="order_status" jdbcType="TINYINT" property="orderStatus"/>
        <result column="order_tag_status" jdbcType="TINYINT" property="orderTagStatus"/>
        <result column="order_sub_type" jdbcType="TINYINT" property="orderSubType"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="order_day_sn" jdbcType="VARCHAR" property="orderDaySn"/>
        <result column="is_reserve" jdbcType="VARCHAR" property="reserve"/>
        <result column="order_remark" jdbcType="VARCHAR" property="orderRemark"/>
        <result column="is_reminded" jdbcType="TINYINT" property="reminded"/>
        <result column="item_count" jdbcType="DECIMAL" property="itemCount"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone"/>
        <result column="customer_address" jdbcType="VARCHAR" property="customerAddress"/>
        <result column="customer_actual_pay" jdbcType="DECIMAL" property="customerActualPay"/>
        <result column="total" jdbcType="DECIMAL" property="total"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="active_time" jdbcType="TIMESTAMP" property="activeTime"/>
        <result column="accept_time" jdbcType="TIMESTAMP" property="acceptTime"/>
        <result column="refund_req_time" jdbcType="TIMESTAMP" property="refundReqTime"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="cancel_req_time" jdbcType="TIMESTAMP" property="cancelReqTime"/>
        <result column="order_status_tcd" jdbcType="VARCHAR" property="orderStatusTcd"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="write_off_code" jdbcType="VARCHAR" property="writeOffCode"/>
        <result column="order_type_tcd" jdbcType="VARCHAR" property="orderTypeTcd"/>
        <result column="estimate_delivered_time" jdbcType="TIMESTAMP" property="estimateDeliveredTime"/>
        <result column="privacy_phone" jdbcType="VARCHAR" property="privacyPhone"/>
    </resultMap>
    <select id="listOrder" resultMap="OrderListResultMap"
            parameterType="string">
        SELECT
        delivery_type,order_guid,order_status,order_tag_status,order_sub_type,order_id,order_day_sn,is_reserve,order_remark,is_reminded
        ,item_count,customer_name,customer_phone,customer_address,customer_actual_pay,total,create_time,active_time,accept_time
        ,refund_req_time,complete_time,estimate_delivered_time,cancel_req_time,order_status_tcd,cancel_time,delivery_time
        ,write_off_code,order_type_tcd,privacy_phone
        FROM hst_takeout_order
        WHERE store_guid = #{storeGuid}
        AND gmt_create &gt;= DATE_SUB(CURRENT_DATE,INTERVAL 3 DAY)
        ORDER BY gmt_create DESC limit 1000
    </select>

    <select id="getOrderDetail" resultMap="OrderResultMap"
            parameterType="com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO">
        SELECT
        <include refid="OrderItemLogColumn"/>
        FROM hst_takeout_order as o
        LEFT JOIN hst_takeout_item as item on o.order_guid = item.order_guid
        LEFT JOIN hst_takeout_log as log on o.order_guid = log.order_guid and log.is_show_in_endpoint = 1
        WHERE
        <trim prefixOverrides="and|or">
            <if test="orderGuid!=null and orderGuid!=''">
                and o.order_guid = #{orderGuid}
            </if>
            <if test="orderViewId!=null and orderViewId!=''">
                and o.order_view_id = #{orderViewId}
            </if>
            <if test="storeGuid!=null and storeGuid!=''">
                and o.store_guid = #{storeGuid}
            </if>
            <if test="orderId!=null and orderId!=''">
                and o.order_id = #{orderId}
            </if>
        </trim>
    </select>

    <select id="getOrderAllDetail" resultMap="OrderResultMap"
            parameterType="com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO">
        SELECT
        <include refid="OrderCascadeColumn"/>
        FROM hst_takeout_order as o
        LEFT JOIN hst_takeout_item as item on o.order_guid = item.order_guid
        LEFT JOIN hst_takeout_remind as remind on o.order_guid = remind.order_guid
        LEFT JOIN hst_takeout_discount as discount on o.order_guid = discount.order_guid
        LEFT JOIN hst_takeout_log as log on o.order_guid = log.order_guid and log.is_show_in_endpoint = 1
        WHERE
        <trim prefixOverrides="and|or">
            <if test="orderGuid!=null and orderGuid!=''">
                and o.order_guid = #{orderGuid}
            </if>
            <if test="orderId!=null and orderId!=''">
                and o.order_id = #{orderId}
            </if>
        </trim>
    </select>

    <select id="getOrderRemind" resultMap="OrderResultMap"
            parameterType="com.holder.saas.store.takeaway.consumers.entity.query.OrderRemindQuery">
        SELECT
        <include refid="OrderRemindColumn"/>
        FROM hst_takeout_order as o
        LEFT JOIN hst_takeout_remind as remind on o.order_guid = remind.order_guid
        WHERE remind.remind_guid = #{remindGuid}
    </select>

    <select id="getOrderMoney" resultMap="OrderResultMap"
            parameterType="com.holder.saas.store.takeaway.consumers.entity.query.HandoverPayQuery">
        select
        <include refid="OrderColumn"/>
        from hst_takeout_order as o
        WHERE o.store_guid = #{storeGuid}
        AND o.order_status = 100
        AND o.complete_time between #{startTime} and #{endTime}
    </select>

    <select id="getTakeoutOrderCount" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        hst_takeout_order h
        <where>
            <include refid="takeoutOrderQueryConditions"/>
        </where>
    </select>

    <select id="getTakeoutOrderList"
            resultType="com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO">
        SELECT
          h.store_name,
          h.order_guid,
          h.order_id,
          h.order_status,
          h.order_tag_status,
          h.order_sub_type,
          h.accept_time,
          h.accept_staff_name,
          h.customer_name,
          h.customer_phone,
          h.privacy_phone,
          h.item_count,
          h.total,
          (IFNULL(h.customer_actual_pay,0) - IFNULL(h.customer_refund,0)) AS customerActualPay,
          h.gmt_create
        FROM
          hst_takeout_order h
        <where>
            <include refid="takeoutOrderQueryConditions"/>
        </where>
        ORDER BY h.gmt_create DESC
        LIMIT #{index},#{pageSize}
    </select>


    <select id="countOrder"
            resultType="com.holderzone.saas.store.dto.takeaway.response.TakeoutOrderStatisticsRespDTO">
        SELECT
            count(if(order_status=0,1,null)) as "unOrderCount",
            count(if(order_tag_status!=0,1,null)) as "exOrderCount"
        FROM hst_takeout_order
        WHERE store_guid = #{storeGuid}
        AND gmt_create &gt;= DATE_SUB(CURRENT_DATE,INTERVAL 3 DAY) limit 1000
    </select>

    <select id="listBusinessDay" resultType="com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO">
        SELECT
            order_id,
            business_day,
            gmt_create
        FROM
            hst_takeout_order
        where
            order_id in
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
    </select>
    <select id="listTakeOutItemSale"
            resultType="com.holderzone.saas.store.dto.order.response.daily.ItemRespDTO">
        SELECT
            i.erp_item_sku_guid guid,
            any_value ( i.erp_item_name ) NAME,
            i.erp_item_price takeoutUnitPrice,
            sum(
                IFNULL( i.`erp_item_price`, 0 ) * IFNULL( (
                    COALESCE ( i.actual_item_count, 0 ) - ( COALESCE ( i.refund_count, 0 ) ) * ( COALESCE ( i.actual_item_count, 1 )/ COALESCE ( i.item_count, 1 ) )
                ), 0 )
            ) takeoutAmount,
            sum(
                IFNULL( (
                    COALESCE ( i.actual_item_count, 0 ) - ( COALESCE ( i.refund_count, 0 ) ) * ( COALESCE ( i.actual_item_count, 1 )/ COALESCE ( i.item_count, 1 ) )
                ), 0 )
            ) takeoutQuantum
        FROM
            hst_takeout_item i
            JOIN hst_takeout_order o ON i.order_guid = o.order_guid
        WHERE
            o.order_status = 100
            AND o.complete_time BETWEEN concat(#{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
            AND o.store_guid = #{dto.storeGuid}
            AND i.erp_item_sku_guid IS NOT NULL
            <if test="dto.reportHideSubTypes != null and dto.reportHideSubTypes.size()>0">
                and o.order_sub_type not in
                <foreach collection="dto.reportHideSubTypes" item="subType" separator="," open="(" close=")">
                    #{subType}
                </foreach>
            </if>
        GROUP BY
            i.erp_item_sku_guid,
            i.erp_item_price

    </select>
    <select id="countAdjustTakeawayOrder" resultType="java.lang.Integer">
        select count(1) from hst_takeout_order
        where
        <include refid="ADJUST_TAKEAWAY_ORDER_CONDITION"/>
    </select>

    <select id="listAdjustTakeawayOrderPage" resultType="com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO">
        select * from hst_takeout_order
        where
        <include refid="ADJUST_TAKEAWAY_ORDER_CONDITION"/>
        ORDER BY gmt_create DESC
        limit ${(query.currentPage - 1) * query.pageSize},#{query.pageSize}
    </select>

    <select id="queryItemSubInfo"
            resultType="com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO">
        SELECT
            guid AS itemGuid,
            order_guid,
            sub_item_info
        FROM
            hst_takeout_item_extends
        WHERE
            guid in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>

    <sql id="ADJUST_TAKEAWAY_ORDER_CONDITION">
        store_guid = #{query.storeGuid}
        <if test="query.searchKey!=null and query.searchKey!=''">
            AND (order_view_id LIKE CONCAT('%',#{query.searchKey},'%') or customer_phone LIKE CONCAT('%',#{query.searchKey},'%'))
        </if>
        AND order_status IN ( 10, 20, 100 )

        <if test="query.beginTime!=null">
            AND gmt_create >= #{query.beginTime}
        </if>
        <if test="query.finishTime!=null">
            AND gmt_create &lt;= #{query.finishTime}
        </if>
    </sql>
    <sql id="takeoutOrderQueryConditions">
        AND h.order_type = 0
        AND h.order_status != 30
        <if test="startTime!=null and startTime!=''">
            AND h.business_day >= #{startTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            AND h.business_day &lt;= #{endTime}
        </if>
        <if test="phoneNo!=null and phoneNo!=''">
            AND IF(h.order_sub_type = 1, h.privacy_phone LIKE CONCAT('%',#{phoneNo},'%'), h.customer_phone LIKE CONCAT('%',#{phoneNo},'%'))
        </if>
        <if test="orderState!=null">
            <choose>
                <when test="orderState==-2">
                    AND h.order_tag_status != 0
                </when>
                <otherwise>
                    AND h.order_status = #{orderState} AND h.order_tag_status = 0
                </otherwise>
            </choose>
        </if>
        <if test="storeGuidList!=null and storeGuidList.size>0">
            AND h.store_guid IN
            <foreach collection="storeGuidList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="orderSource!=null">
            AND h.order_sub_type = #{orderSource}
        </if>
    </sql>

    <sql id="OrderCascadeColumn">
        <trim suffixOverrides=",">
            <include refid="OrderColumn"/>,
            <include refid="ItemColumn"/>,
            <include refid="RemindColumn"/>,
            <include refid="DiscountColumn"/>,
            <include refid="LogColumn"/>,
        </trim>
    </sql>

    <sql id="OrderItemLogColumn">
        <trim suffixOverrides=",">
            <include refid="OrderColumn"/>,
            <include refid="ItemColumn"/>,
            <include refid="LogColumn"/>,
        </trim>
    </sql>

    <sql id="OrderRemindColumn">
        <trim suffixOverrides=",">
            o.enterprise_guid,
            o.store_guid,
            o.order_id,
            remind.remind_id as remind_remind_id,
            remind.reply_content as remind_reply_content,
        </trim>
    </sql>

    <sql id="OrderColumn">
        <trim suffixOverrides=",">
            o.*,
        </trim>
    </sql>

    <sql id="ItemColumn">
        <trim suffixOverrides=",">
            item.id as item_id,
            item.store_guid as item_store_guid,
            item.store_name as item_store_name,
            item.order_guid as item_order_guid,
            item.item_sku as item_item_sku,
            item.item_guid as item_item_guid,
            item.item_name as item_item_name,
            item.item_name_platform as item_item_name_platform,
            item.order_sub_type as item_order_sub_type,
            item.item_code as item_item_code,
            item.item_unit as item_item_unit,
            item.item_price as item_item_price,
            item.item_count as item_item_count,
            item.actual_item_count as item_actual_item_count,
            item.item_total as item_item_total,
            item.item_spec as item_item_spec,
            item.item_property as item_item_property,
            item.box_price as item_box_price,
            item.box_count as item_box_count,
            item.box_total as item_box_total,
            item.cart_id as item_cart_id,
            item.discount_price as item_discount_price,
            item.discount_ratio as item_discount_ratio,
            item.discount_total as item_discount_total,
            item.actual_price as item_actual_price,
            item.settle_type as item_settle_type,
            item.complete_time as item_complete_time,
            item.business_day as item_business_day,
            item.is_adjust_item as item_is_adjust_item,
            item.erp_item_sku_guid as item_erp_item_sku_guid,
            item.erp_item_name as item_erp_item_name,
            item.erp_item_price as item_erp_item_price,
            item.third_sku_id as item_third_sku_id,
            item.gmt_create as item_gmt_create,
            item.gmt_modified as item_gmt_modified,
            "" as item_sub_item_info,
        </trim>
    </sql>

    <sql id="RemindColumn">
        <trim suffixOverrides=",">
            remind.id as remind_id,
            remind.remind_guid as remind_remind_guid,
            remind.order_guid as remind_order_guid,
            remind.remind_id as remind_remind_id,
            remind.is_replied as remind_is_replied,
            remind.reply_content as remind_reply_content,
            remind.reply_staff_guid as remind_reply_staff_guid,
            remind.reply_staff_name as remind_reply_staff_name,
            remind.remind_time as remind_remind_time,
            remind.reply_time as remind_reply_time,
            remind.gmt_create as remind_gmt_create,
            remind.gmt_modified as remind_gmt_modified,
        </trim>
    </sql>

    <sql id="DiscountColumn">
        <trim suffixOverrides=",">
            discount.id as discount_id,
            discount.discount_guid as discount_discount_guid,
            discount.order_guid as discount_order_guid,
            discount.discount_name as discount_discount_name,
            discount.discount_remark as discount_discount_remark,
            discount.total_discount as discount_total_discount,
            discount.enterprise_discount as discount_enterprise_discount,
            discount.platform_discount as discount_platform_discount,
            discount.other_discount as discount_other_discount,
            discount.gmt_create as discount_gmt_create,
            discount.gmt_modified as discount_gmt_modified
        </trim>
    </sql>

    <sql id="LogColumn">
        <trim suffixOverrides=",">
            log.id as log_id,
            log.order_guid as log_order_guid,
            log.order_id as log_order_id,
            log.time as log_time,
            log.operator as log_operator,
            log.description as log_description,
            log.title as log_title,
            log.body as log_body,
            log.is_show_in_web_page as log_is_show_in_web_page,
            log.is_show_in_endpoint as log_is_show_in_endpoint,
            log.gmt_create as log_gmt_create,
            log.gmt_modified as log_gmt_modified
        </trim>
    </sql>

    <update id="updateThirdCarrierIdByOrderId">
        update hst_takeout_order set third_carrier_id = #{thirdCarrierId}
        where order_id = #{orderId}
    </update>

</mapper>
