<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.store.takeaway.consumers.mapper.FixItemMapper">


    <select id="queryStoreGuids" resultType="java.lang.String">
        SELECT DISTINCT store_guid from hst_takeout_fix_item where record_id = #{recordId}
    </select>
</mapper>
