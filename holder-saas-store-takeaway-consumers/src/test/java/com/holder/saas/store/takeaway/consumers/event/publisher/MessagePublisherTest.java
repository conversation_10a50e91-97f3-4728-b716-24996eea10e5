package com.holder.saas.store.takeaway.consumers.event.publisher;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import javafx.util.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)


public class MessagePublisherTest {

    @Mock
    private ApplicationContext mockApplicationContext;

    private MessagePublisher messagePublisherUnderTest;

    @Before
    public void setUp() throws Exception {
        messagePublisherUnderTest = new MessagePublisher(mockApplicationContext);
    }

    @Test
    public void testSend() {
        // Setup
        final UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("enterpriseGuid");
        userContext.setEnterpriseName("enterpriseName");
        userContext.setEnterpriseNo("enterpriseNo");
        userContext.setStoreGuid("storeGuid");
        userContext.setStoreName("storeName");
        final Pair<UserContext, BusinessMessageDTO> businessMessage = new Pair<>(userContext,
                BusinessMessageDTO.builder().build());

        // Run the test
        messagePublisherUnderTest.send(businessMessage);

        // Verify the results
        verify(mockApplicationContext).publishEvent(any(ApplicationEvent.class));
    }
}
