package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.PackageDO;
import com.holder.saas.store.takeaway.consumers.mapper.PackageMapper;
import com.holder.saas.store.takeaway.consumers.mapstruct.ItemPackageMapstruct;
import com.holder.saas.store.takeaway.consumers.service.rpc.ItemFeignClient;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PackageServiceImplTest {

    @Mock
    private PackageMapper mockPackageMapper;
    @Mock
    private ItemFeignClient mockItemFeignClient;
    @Mock
    private ItemPackageMapstruct mockItemPackageMapstruct;

    private PackageServiceImpl packageServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        packageServiceImplUnderTest = new PackageServiceImpl(mockPackageMapper, mockItemFeignClient,
                mockItemPackageMapstruct);
    }

    @Test
    public void testListByTakeoutItemGuids() {
        assertThat(packageServiceImplUnderTest.listByTakeoutItemGuids(Arrays.asList("value")))
                .isEqualTo(Arrays.asList("value"));
        assertThat(packageServiceImplUnderTest.listByTakeoutItemGuids(Arrays.asList("value")))
                .isEqualTo(Collections.emptyList());
    }

    @Test
    public void testUpdateByTakeoutItem() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setItemGuid("itemGuid");
        skuInfoPkgDTO.setParentGuid("parentGuid");
        skuInfoPkgDTO.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO.setItemName("itemName");
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO);

        // Configure ItemFeignClient.listPkgInfoByItemGuid(...).
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setItemGuid("itemGuid");
        skuInfoPkgDTO1.setParentGuid("parentGuid");
        skuInfoPkgDTO1.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO1.setItemName("itemName");
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgDTOS = Arrays.asList(skuInfoPkgDTO1);
        when(mockItemFeignClient.listPkgInfoByItemGuid(Arrays.asList("value"))).thenReturn(skuInfoPkgDTOS);

        // Configure ItemPackageMapstruct.parseToPackageDO(...).
        final PackageDO packageDO = new PackageDO();
        packageDO.setId(0L);
        packageDO.setIsDelete(0);
        packageDO.setTakeoutItemGuid("itemGuid");
        packageDO.setPackageGuid("packageGuid");
        packageDO.setItemGuid("itemGuid");
        final SkuInfoPkgDTO pkgDTO = new SkuInfoPkgDTO();
        pkgDTO.setItemGuid("itemGuid");
        pkgDTO.setParentGuid("parentGuid");
        pkgDTO.setItemNum(new BigDecimal("0.00"));
        pkgDTO.setItemName("itemName");
        pkgDTO.setSkuGuid("skuGuid");
        when(mockItemPackageMapstruct.parseToPackageDO(pkgDTO)).thenReturn(packageDO);

        // Run the test
        packageServiceImplUnderTest.updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);

        // Verify the results
        verify(mockPackageMapper).deleteBatchByItem(Arrays.asList("value"));
    }

    @Test
    public void testUpdateByTakeoutItem_ItemFeignClientReturnsNoItems() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setItemGuid("itemGuid");
        skuInfoPkgDTO.setParentGuid("parentGuid");
        skuInfoPkgDTO.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO.setItemName("itemName");
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO);
        when(mockItemFeignClient.listPkgInfoByItemGuid(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure ItemPackageMapstruct.parseToPackageDO(...).
        final PackageDO packageDO = new PackageDO();
        packageDO.setId(0L);
        packageDO.setIsDelete(0);
        packageDO.setTakeoutItemGuid("itemGuid");
        packageDO.setPackageGuid("packageGuid");
        packageDO.setItemGuid("itemGuid");
        final SkuInfoPkgDTO pkgDTO = new SkuInfoPkgDTO();
        pkgDTO.setItemGuid("itemGuid");
        pkgDTO.setParentGuid("parentGuid");
        pkgDTO.setItemNum(new BigDecimal("0.00"));
        pkgDTO.setItemName("itemName");
        pkgDTO.setSkuGuid("skuGuid");
        when(mockItemPackageMapstruct.parseToPackageDO(pkgDTO)).thenReturn(packageDO);

        // Run the test
        packageServiceImplUnderTest.updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);

        // Verify the results
        verify(mockPackageMapper).deleteBatchByItem(Arrays.asList("value"));
    }
}
