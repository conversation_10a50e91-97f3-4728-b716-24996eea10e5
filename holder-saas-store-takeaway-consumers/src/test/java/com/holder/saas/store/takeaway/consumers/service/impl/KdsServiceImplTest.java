package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.service.ItemClientService;
import com.holder.saas.store.takeaway.consumers.service.MappingService;
import com.holder.saas.store.takeaway.consumers.service.rpc.KdsClientService;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubItemSkuWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubgroupWebRespDTO;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class KdsServiceImplTest {

    @Mock
    private KdsClientService mockKdsClientService;
    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;
    @Mock
    private MappingService mockMappingService;
    @Mock
    private ItemClientService mockItemClientService;

    private KdsServiceImpl kdsServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(kdsServiceImplUnderTest, "mappingService", mockMappingService);
        ReflectionTestUtils.setField(kdsServiceImplUnderTest, "itemClientService", mockItemClientService);
    }

    @Test
    public void testPrepare() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderType(0);
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseName("enterpriseName");
        orderReadDO.setStoreName("storeName");
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("-1");
        orderReadDO.setOrderDaySn("orderDaySn");
        orderReadDO.setOrderRemark("orderRemark");
        orderReadDO.setAcceptDeviceId("acceptDeviceId");
        orderReadDO.setAcceptDeviceType(0);
        orderReadDO.setIsAutoAccept(false);
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemProperty("itemProperty");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemName("itemName");
        subItemSkuWebRespDTO.setSkuName("skuName");
        subItemSkuWebRespDTO.setCode("code");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setUnit("skuUnit");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDeviceType(0);
        itemStringListDTO.setDeviceId("acceptDeviceId");
        itemStringListDTO.setEnterpriseGuid("enterpriseGuid");
        itemStringListDTO.setEnterpriseName("enterpriseName");
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setStoreName("storeName");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Run the test
        kdsServiceImplUnderTest.prepare("platformName", orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testPrepare_ItemClientServiceReturnsNoItems() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderType(0);
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseName("enterpriseName");
        orderReadDO.setStoreName("storeName");
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("-1");
        orderReadDO.setOrderDaySn("orderDaySn");
        orderReadDO.setOrderRemark("orderRemark");
        orderReadDO.setAcceptDeviceId("acceptDeviceId");
        orderReadDO.setAcceptDeviceType(0);
        orderReadDO.setIsAutoAccept(false);
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemProperty("itemProperty");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDeviceType(0);
        itemStringListDTO.setDeviceId("acceptDeviceId");
        itemStringListDTO.setEnterpriseGuid("enterpriseGuid");
        itemStringListDTO.setEnterpriseName("enterpriseName");
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setStoreName("storeName");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Run the test
        kdsServiceImplUnderTest.prepare("platformName", orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testUrge() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderType(0);
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseName("enterpriseName");
        orderReadDO.setStoreName("storeName");
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("-1");
        orderReadDO.setOrderDaySn("orderDaySn");
        orderReadDO.setOrderRemark("orderRemark");
        orderReadDO.setAcceptDeviceId("acceptDeviceId");
        orderReadDO.setAcceptDeviceType(0);
        orderReadDO.setIsAutoAccept(false);
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemProperty("itemProperty");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        // Run the test
        kdsServiceImplUnderTest.urge(orderReadDO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testRefund() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderType(0);
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseName("enterpriseName");
        orderReadDO.setStoreName("storeName");
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("-1");
        orderReadDO.setOrderDaySn("orderDaySn");
        orderReadDO.setOrderRemark("orderRemark");
        orderReadDO.setAcceptDeviceId("acceptDeviceId");
        orderReadDO.setAcceptDeviceType(0);
        orderReadDO.setIsAutoAccept(false);
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemProperty("itemProperty");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemName("itemName");
        subItemSkuWebRespDTO.setSkuName("skuName");
        subItemSkuWebRespDTO.setCode("code");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setUnit("skuUnit");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDeviceType(0);
        itemStringListDTO.setDeviceId("acceptDeviceId");
        itemStringListDTO.setEnterpriseGuid("enterpriseGuid");
        itemStringListDTO.setEnterpriseName("enterpriseName");
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setStoreName("storeName");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Run the test
        kdsServiceImplUnderTest.refund(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testRefund_ItemClientServiceReturnsNoItems() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderType(0);
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseName("enterpriseName");
        orderReadDO.setStoreName("storeName");
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("-1");
        orderReadDO.setOrderDaySn("orderDaySn");
        orderReadDO.setOrderRemark("orderRemark");
        orderReadDO.setAcceptDeviceId("acceptDeviceId");
        orderReadDO.setAcceptDeviceType(0);
        orderReadDO.setIsAutoAccept(false);
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemProperty("itemProperty");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDeviceType(0);
        itemStringListDTO.setDeviceId("acceptDeviceId");
        itemStringListDTO.setEnterpriseGuid("enterpriseGuid");
        itemStringListDTO.setEnterpriseName("enterpriseName");
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setStoreName("storeName");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Run the test
        kdsServiceImplUnderTest.refund(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }
}
