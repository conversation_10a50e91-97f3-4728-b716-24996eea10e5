package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.mapstruct.OrderMapstruct;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DiscountServiceImplTest {

    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private OrderMapstruct mockOrderMapstruct;

    private DiscountServiceImpl discountServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        discountServiceImplUnderTest = new DiscountServiceImpl(mockDynamicHelper, mockOrderMapstruct);
    }
}
