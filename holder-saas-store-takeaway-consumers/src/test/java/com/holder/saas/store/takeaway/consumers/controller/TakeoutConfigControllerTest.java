package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.ConfigService;
import com.holderzone.saas.store.dto.takeaway.TakeoutAutoRcvDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TakeoutConfigController.class)
public class TakeoutConfigControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ConfigService mockConfigService;

    @Test
    public void testSetAutoReceive() throws Exception {
        // Setup
        when(mockConfigService.saveStoreAutoRcvConfig(new TakeoutAutoRcvDTO(false))).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/set_auto_receive")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSetAutoReceive_ConfigServiceReturnsTrue() throws Exception {
        // Setup
        when(mockConfigService.saveStoreAutoRcvConfig(new TakeoutAutoRcvDTO(false))).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/set_auto_receive")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryAutoReceive() throws Exception {
        // Setup
        when(mockConfigService.selectStoreAutoRcvConfig(new TakeoutAutoRcvDTO(false)))
                .thenReturn(new TakeoutAutoRcvDTO(false));

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/query_auto_receive")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
