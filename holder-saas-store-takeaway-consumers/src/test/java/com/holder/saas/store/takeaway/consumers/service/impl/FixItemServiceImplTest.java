package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.entity.domain.FixItemDO;
import com.holder.saas.store.takeaway.consumers.mapstruct.FixItemMapstruct;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixItemDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FixItemServiceImplTest {

    @Mock
    private FixItemMapstruct mockFixItemMapstruct;

    @InjectMocks
    private FixItemServiceImpl fixItemServiceImplUnderTest;

    @Test
    public void testListByRecordId() {
        // Setup
        final TakeoutFixItemDTO takeoutFixItemDTO = new TakeoutFixItemDTO();
        takeoutFixItemDTO.setId(0L);
        takeoutFixItemDTO.setStoreGuid("storeGuid");
        takeoutFixItemDTO.setStoreName("storeName");
        takeoutFixItemDTO.setOrderSubType(0);
        takeoutFixItemDTO.setTakeoutItemName("takeoutItemName");
        final List<TakeoutFixItemDTO> expectedResult = Arrays.asList(takeoutFixItemDTO);

        // Configure FixItemMapstruct.doList2DTOList(...).
        final TakeoutFixItemDTO takeoutFixItemDTO1 = new TakeoutFixItemDTO();
        takeoutFixItemDTO1.setId(0L);
        takeoutFixItemDTO1.setStoreGuid("storeGuid");
        takeoutFixItemDTO1.setStoreName("storeName");
        takeoutFixItemDTO1.setOrderSubType(0);
        takeoutFixItemDTO1.setTakeoutItemName("takeoutItemName");
        final List<TakeoutFixItemDTO> takeoutFixItemDTOS = Arrays.asList(takeoutFixItemDTO1);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setId(0L);
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        final List<FixItemDO> fixItemDOList = Arrays.asList(fixItemDO);
        when(mockFixItemMapstruct.doList2DTOList(fixItemDOList)).thenReturn(takeoutFixItemDTOS);

        // Run the test
        final List<TakeoutFixItemDTO> result = fixItemServiceImplUnderTest.listByRecordId(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListByRecordId_FixItemMapstructReturnsNoItems() {
        // Setup
        // Configure FixItemMapstruct.doList2DTOList(...).
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setId(0L);
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        final List<FixItemDO> fixItemDOList = Arrays.asList(fixItemDO);
        when(mockFixItemMapstruct.doList2DTOList(fixItemDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TakeoutFixItemDTO> result = fixItemServiceImplUnderTest.listByRecordId(0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryStoreGuids() {
        // Setup
        // Run the test
        final List<String> result = fixItemServiceImplUnderTest.queryStoreGuids(0L);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }
}
