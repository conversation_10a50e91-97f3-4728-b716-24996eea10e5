package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.mapper.FixRecordMapper;
import com.holder.saas.store.takeaway.consumers.mapper.ItemMapper;
import com.holder.saas.store.takeaway.consumers.mapstruct.ItemMapstruct;
import com.holder.saas.store.takeaway.consumers.mapstruct.OrderMapstruct;
import com.holder.saas.store.takeaway.consumers.service.rpc.ItemFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.OrganizationService;
import com.holder.saas.store.takeaway.consumers.service.rpc.StaffFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderItemProblemDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutAndStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderItemRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemServiceImplTest {

    @Mock
    private ItemMapstruct mockItemMapstruct;
    @Mock
    private ItemMapper mockItemMapper;
    @Mock
    private OrganizationService mockOrganizationService;
    @Mock
    private StaffFeignClient mockStaffFeignClient;

    private ItemServiceImpl itemServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        itemServiceImplUnderTest = new ItemServiceImpl(mockItemMapstruct,
                mockItemMapper, mockOrganizationService, mockStaffFeignClient);
    }

    @Test
    public void testListByOrderGuid() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> expectedResult = Arrays.asList(itemDO);

        // Run the test
        final List<ItemDO> result = itemServiceImplUnderTest.listByOrderGuid("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListByItemGuids() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> expectedResult = Arrays.asList(itemDO);

        // Run the test
        final List<ItemDO> result = itemServiceImplUnderTest.listByItemGuids(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetBusinessTakeoutOrderItemList1() {
        // Setup
        final BusinessTakeoutOrderItemRespDTO businessTakeoutOrderItemRespDTO = new BusinessTakeoutOrderItemRespDTO();
        businessTakeoutOrderItemRespDTO.setGuid("ba8e46d5-ae27-4c5b-a271-66e7907e0a5f");
        businessTakeoutOrderItemRespDTO.setItemName("itemName");
        businessTakeoutOrderItemRespDTO.setItemUnit("itemUnit");
        businessTakeoutOrderItemRespDTO.setItemCount(new BigDecimal("0.00"));
        businessTakeoutOrderItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        final List<BusinessTakeoutOrderItemRespDTO> expectedResult = Arrays.asList(businessTakeoutOrderItemRespDTO);

        // Configure ItemMapstruct.doList2BusinessTakeoutOrderItemRespDtoList(...).
        final BusinessTakeoutOrderItemRespDTO businessTakeoutOrderItemRespDTO1 = new BusinessTakeoutOrderItemRespDTO();
        businessTakeoutOrderItemRespDTO1.setGuid("ba8e46d5-ae27-4c5b-a271-66e7907e0a5f");
        businessTakeoutOrderItemRespDTO1.setItemName("itemName");
        businessTakeoutOrderItemRespDTO1.setItemUnit("itemUnit");
        businessTakeoutOrderItemRespDTO1.setItemCount(new BigDecimal("0.00"));
        businessTakeoutOrderItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        final List<BusinessTakeoutOrderItemRespDTO> businessTakeoutOrderItemRespDTOS = Arrays.asList(
                businessTakeoutOrderItemRespDTO1);
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> itemDoList = Arrays.asList(itemDO);
        when(mockItemMapstruct.doList2BusinessTakeoutOrderItemRespDtoList(itemDoList))
                .thenReturn(businessTakeoutOrderItemRespDTOS);

        // Run the test
        final List<BusinessTakeoutOrderItemRespDTO> result = itemServiceImplUnderTest.getBusinessTakeoutOrderItemList(
                "orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetBusinessTakeoutOrderItemList1_ItemMapstructReturnsNoItems() {
        // Setup
        // Configure ItemMapstruct.doList2BusinessTakeoutOrderItemRespDtoList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> itemDoList = Arrays.asList(itemDO);
        when(mockItemMapstruct.doList2BusinessTakeoutOrderItemRespDtoList(itemDoList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<BusinessTakeoutOrderItemRespDTO> result = itemServiceImplUnderTest.getBusinessTakeoutOrderItemList(
                "orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetBusinessTakeoutOrderItemList2() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> itemDoList = Arrays.asList(itemDO);
        final BusinessTakeoutOrderItemRespDTO businessTakeoutOrderItemRespDTO = new BusinessTakeoutOrderItemRespDTO();
        businessTakeoutOrderItemRespDTO.setGuid("ba8e46d5-ae27-4c5b-a271-66e7907e0a5f");
        businessTakeoutOrderItemRespDTO.setItemName("itemName");
        businessTakeoutOrderItemRespDTO.setItemUnit("itemUnit");
        businessTakeoutOrderItemRespDTO.setItemCount(new BigDecimal("0.00"));
        businessTakeoutOrderItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        final List<BusinessTakeoutOrderItemRespDTO> expectedResult = Arrays.asList(businessTakeoutOrderItemRespDTO);

        // Configure ItemMapstruct.doList2BusinessTakeoutOrderItemRespDtoList(...).
        final BusinessTakeoutOrderItemRespDTO businessTakeoutOrderItemRespDTO1 = new BusinessTakeoutOrderItemRespDTO();
        businessTakeoutOrderItemRespDTO1.setGuid("ba8e46d5-ae27-4c5b-a271-66e7907e0a5f");
        businessTakeoutOrderItemRespDTO1.setItemName("itemName");
        businessTakeoutOrderItemRespDTO1.setItemUnit("itemUnit");
        businessTakeoutOrderItemRespDTO1.setItemCount(new BigDecimal("0.00"));
        businessTakeoutOrderItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        final List<BusinessTakeoutOrderItemRespDTO> businessTakeoutOrderItemRespDTOS = Arrays.asList(
                businessTakeoutOrderItemRespDTO1);
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemGuid("itemGuid");
        itemDO1.setThirdSkuId("thirdSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setIsAdjustItem(false);
        final List<ItemDO> itemDoList1 = Arrays.asList(itemDO1);
        when(mockItemMapstruct.doList2BusinessTakeoutOrderItemRespDtoList(itemDoList1))
                .thenReturn(businessTakeoutOrderItemRespDTOS);

        // Run the test
        final List<BusinessTakeoutOrderItemRespDTO> result = itemServiceImplUnderTest.getBusinessTakeoutOrderItemList(
                itemDoList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetBusinessTakeoutOrderItemList2_ItemMapstructReturnsNoItems() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> itemDoList = Arrays.asList(itemDO);

        // Configure ItemMapstruct.doList2BusinessTakeoutOrderItemRespDtoList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemGuid("itemGuid");
        itemDO1.setThirdSkuId("thirdSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setIsAdjustItem(false);
        final List<ItemDO> itemDoList1 = Arrays.asList(itemDO1);
        when(mockItemMapstruct.doList2BusinessTakeoutOrderItemRespDtoList(itemDoList1))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<BusinessTakeoutOrderItemRespDTO> result = itemServiceImplUnderTest.getBusinessTakeoutOrderItemList(
                itemDoList);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetItemList() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> expectedResult = Arrays.asList(itemDO);

        // Run the test
        final List<ItemDO> result = itemServiceImplUnderTest.getItemList("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateBatchIsAdjustItem() {
        // Setup
        // Run the test
        itemServiceImplUnderTest.updateBatchIsAdjustItem(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testUpdateBatchItem() {
        // Setup
        final TakeoutOrderItemProblemDTO takeoutOrderItemProblemDTO = new TakeoutOrderItemProblemDTO();
        takeoutOrderItemProblemDTO.setId("id");
        takeoutOrderItemProblemDTO.setStoreGuid("storeGuid");
        takeoutOrderItemProblemDTO.setItemSku("itemSku");
        takeoutOrderItemProblemDTO.setOrderSubType(0);
        takeoutOrderItemProblemDTO.setThirdSkuId("thirdSkuId");
        final List<TakeoutOrderItemProblemDTO> problemDTOList = Arrays.asList(takeoutOrderItemProblemDTO);

        // Configure ItemMapstruct.problemDTOList2ItemDOList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        final TakeoutOrderItemProblemDTO takeoutOrderItemProblemDTO1 = new TakeoutOrderItemProblemDTO();
        takeoutOrderItemProblemDTO1.setId("id");
        takeoutOrderItemProblemDTO1.setStoreGuid("storeGuid");
        takeoutOrderItemProblemDTO1.setItemSku("itemSku");
        takeoutOrderItemProblemDTO1.setOrderSubType(0);
        takeoutOrderItemProblemDTO1.setThirdSkuId("thirdSkuId");
        final List<TakeoutOrderItemProblemDTO> problemDTOList1 = Arrays.asList(takeoutOrderItemProblemDTO1);
        when(mockItemMapstruct.problemDTOList2ItemDOList(problemDTOList1)).thenReturn(itemDOS);

        // Run the test
        itemServiceImplUnderTest.updateBatchItem(problemDTOList);

        // Verify the results
    }

    @Test
    public void testUpdateBatchItem_ItemMapstructReturnsNoItems() {
        // Setup
        final TakeoutOrderItemProblemDTO takeoutOrderItemProblemDTO = new TakeoutOrderItemProblemDTO();
        takeoutOrderItemProblemDTO.setId("id");
        takeoutOrderItemProblemDTO.setStoreGuid("storeGuid");
        takeoutOrderItemProblemDTO.setItemSku("itemSku");
        takeoutOrderItemProblemDTO.setOrderSubType(0);
        takeoutOrderItemProblemDTO.setThirdSkuId("thirdSkuId");
        final List<TakeoutOrderItemProblemDTO> problemDTOList = Arrays.asList(takeoutOrderItemProblemDTO);

        // Configure ItemMapstruct.problemDTOList2ItemDOList(...).
        final TakeoutOrderItemProblemDTO takeoutOrderItemProblemDTO1 = new TakeoutOrderItemProblemDTO();
        takeoutOrderItemProblemDTO1.setId("id");
        takeoutOrderItemProblemDTO1.setStoreGuid("storeGuid");
        takeoutOrderItemProblemDTO1.setItemSku("itemSku");
        takeoutOrderItemProblemDTO1.setOrderSubType(0);
        takeoutOrderItemProblemDTO1.setThirdSkuId("thirdSkuId");
        final List<TakeoutOrderItemProblemDTO> problemDTOList1 = Arrays.asList(takeoutOrderItemProblemDTO1);
        when(mockItemMapstruct.problemDTOList2ItemDOList(problemDTOList1)).thenReturn(Collections.emptyList());

        // Run the test
        itemServiceImplUnderTest.updateBatchItem(problemDTOList);

        // Verify the results
    }

    @Test
    public void testFixBatchItem() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> fixItemList = Arrays.asList(itemDO);

        // Run the test
        itemServiceImplUnderTest.fixBatchItem(fixItemList);

        // Verify the results
    }

    @Test
    public void testPageAbnormalData() {
        // Setup
        final TakeoutItemAbnormalDataReqDTO reqDTO = new TakeoutItemAbnormalDataReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setOrderType(0);
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");

        when(mockStaffFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));
        when(mockOrganizationService.queryStoreGuidListByBrandGui("brandGuid")).thenReturn(Arrays.asList("value"));

        // Configure ItemMapper.pageAbnormalData(...).
        final TakeoutItemAbnormalDataReqDTO reqDTO1 = new TakeoutItemAbnormalDataReqDTO();
        reqDTO1.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setOrderType(0);
        reqDTO1.setStoreGuidList(Arrays.asList("value"));
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemMapper.pageAbnormalData(any(IPage.class), eq(reqDTO1))).thenReturn(null);

        // Run the test
        final Page<TakeoutItemAbnormalDataRespDTO> result = itemServiceImplUnderTest.pageAbnormalData(reqDTO);

        // Verify the results
    }

    @Test
    public void testPageAbnormalData_StaffFeignClientReturnsNoItems() {
        // Setup
        final TakeoutItemAbnormalDataReqDTO reqDTO = new TakeoutItemAbnormalDataReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setOrderType(0);
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");

        when(mockStaffFeignClient.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        final Page<TakeoutItemAbnormalDataRespDTO> result = itemServiceImplUnderTest.pageAbnormalData(reqDTO);

        // Verify the results
    }

    @Test
    public void testPageAbnormalData_OrganizationServiceReturnsNoItems() {
        // Setup
        final TakeoutItemAbnormalDataReqDTO reqDTO = new TakeoutItemAbnormalDataReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setOrderType(0);
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");

        when(mockStaffFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));
        when(mockOrganizationService.queryStoreGuidListByBrandGui("brandGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final Page<TakeoutItemAbnormalDataRespDTO> result = itemServiceImplUnderTest.pageAbnormalData(reqDTO);

        // Verify the results
    }

    @Test
    public void testListDataFix() {
        // Setup
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        reqDTO.setSpliceList(Arrays.asList("value"));

        final TakeoutItemDataFixRespDTO takeoutItemDataFixRespDTO = new TakeoutItemDataFixRespDTO();
        takeoutItemDataFixRespDTO.setStoreGuid("storeGuid");
        takeoutItemDataFixRespDTO.setStoreName("storeName");
        takeoutItemDataFixRespDTO.setOrderSubType(0);
        takeoutItemDataFixRespDTO.setTakeoutItemName("takeoutItemName");
        takeoutItemDataFixRespDTO.setThirdSkuId("thirdSkuId");
        final List<TakeoutItemDataFixRespDTO> expectedResult = Arrays.asList(takeoutItemDataFixRespDTO);

        // Configure ItemMapper.listDataFix(...).
        final TakeoutItemDataFixRespDTO takeoutItemDataFixRespDTO1 = new TakeoutItemDataFixRespDTO();
        takeoutItemDataFixRespDTO1.setStoreGuid("storeGuid");
        takeoutItemDataFixRespDTO1.setStoreName("storeName");
        takeoutItemDataFixRespDTO1.setOrderSubType(0);
        takeoutItemDataFixRespDTO1.setTakeoutItemName("takeoutItemName");
        takeoutItemDataFixRespDTO1.setThirdSkuId("thirdSkuId");
        final List<TakeoutItemDataFixRespDTO> takeoutItemDataFixRespDTOS = Arrays.asList(takeoutItemDataFixRespDTO1);
        final TakeoutItemDataFixReqDTO reqDTO1 = new TakeoutItemDataFixReqDTO();
        reqDTO1.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO1 = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO1.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO1));
        reqDTO1.setSpliceList(Arrays.asList("value"));
        when(mockItemMapper.listDataFix(reqDTO1)).thenReturn(takeoutItemDataFixRespDTOS);

        // Run the test
        final List<TakeoutItemDataFixRespDTO> result = itemServiceImplUnderTest.listDataFix(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListDataFix_ItemMapperReturnsNoItems() {
        // Setup
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        reqDTO.setSpliceList(Arrays.asList("value"));

        // Configure ItemMapper.listDataFix(...).
        final TakeoutItemDataFixReqDTO reqDTO1 = new TakeoutItemDataFixReqDTO();
        reqDTO1.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO1 = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO1.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO1));
        reqDTO1.setSpliceList(Arrays.asList("value"));
        when(mockItemMapper.listDataFix(reqDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TakeoutItemDataFixRespDTO> result = itemServiceImplUnderTest.listDataFix(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSelectUnBindList() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> expectedResult = Arrays.asList(itemDO);

        // Run the test
        final List<ItemDO> result = itemServiceImplUnderTest.selectUnBindList("storeGuid", "thirdSkuId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testClearErpItemSkuGuid() {
        // Setup
        // Run the test
        itemServiceImplUnderTest.clearErpItemSkuGuid(Arrays.asList(0L));

        // Verify the results
    }

    @Test
    public void testFilterNoRequiredFixList() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> expectedResult = Arrays.asList(itemDO);

        // Run the test
        final List<ItemDO> result = itemServiceImplUnderTest.filterNoRequiredFixList(Arrays.asList(0L));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateBatchRefundCount() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setIsAdjustItem(false);
        final List<ItemDO> itemList = Arrays.asList(itemDO);

        // Run the test
        itemServiceImplUnderTest.updateBatchRefundCount(itemList);

        // Verify the results
    }

    @Test
    public void testUpdateRefundCountByOrderGuid() {
        // Setup
        // Run the test
        itemServiceImplUnderTest.updateRefundCountByOrderGuid("orderGuid");

        // Verify the results
    }
}
