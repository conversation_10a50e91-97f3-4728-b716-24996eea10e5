package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemMappingDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.StoreBindOrderDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.mapper.ItemMappingMapper;
import com.holder.saas.store.takeaway.consumers.mapper.StockStoreBindMapper;
import com.holder.saas.store.takeaway.consumers.service.ItemClientService;
import com.holder.saas.store.takeaway.consumers.service.rpc.ErpFeignService;
import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.TradeClientService;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.takeaway.request.StockStoreBindReqOrderDTO;
import com.holderzone.saas.store.dto.takeaway.response.StockStoreBindResqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderItemRespDTO;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ErpServiceImplTest {

    @Mock
    private ItemClientService mockItemClientService;
    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;
    @Mock
    private ProducerFeignClient mockProducerFeignClient;
    @Mock
    private ItemMappingMapper mockItemMappingMapper;
    @Mock
    private TradeClientService mockTradeClientService;

    private ErpServiceImpl erpServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        erpServiceImplUnderTest = new ErpServiceImpl(mockItemClientService,
                mockDefaultRocketMqProducer, mockProducerFeignClient, mockItemMappingMapper,
                mockTradeClientService);
        ReflectionTestUtils.setField(erpServiceImplUnderTest, "mdmRequestHost", "mdmRequestHost");
        ReflectionTestUtils.setField(erpServiceImplUnderTest, "erpHost", "erpHost");
    }

    @Test
    public void testReduceStockForOrder() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setDiscountTotal(new BigDecimal("0.00"));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        orderReadDO.setAdjustState(0);
        orderReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("erpItemSkuGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setItemName("dishesName");
        itemDO.setItemCode("dishesCode");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setIsAdjustItem(false);
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("erpItemSkuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        itemInfoRespDTO.setItemSkuGuid("erpItemSkuGuid");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Run the test
        erpServiceImplUnderTest.reduceStockForOrder(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testReduceStockForOrder_ItemClientServiceReturnsNoItems() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setDiscountTotal(new BigDecimal("0.00"));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        orderReadDO.setAdjustState(0);
        orderReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("erpItemSkuGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setItemName("dishesName");
        itemDO.setItemCode("dishesCode");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setIsAdjustItem(false);
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Run the test
        erpServiceImplUnderTest.reduceStockForOrder(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testReduceStockForOrderMDM() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setDiscountTotal(new BigDecimal("0.00"));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        orderReadDO.setAdjustState(0);
        orderReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("erpItemSkuGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setItemName("dishesName");
        itemDO.setItemCode("dishesCode");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setIsAdjustItem(false);
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("erpItemSkuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        itemInfoRespDTO.setItemSkuGuid("erpItemSkuGuid");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        when(mockItemClientService.getErpSkuGuids(new SingleDataDTO("jh", Arrays.asList("value"))))
                .thenReturn(new HashMap<>());

        // Run the test
        erpServiceImplUnderTest.reduceStockForOrderMDM(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
        verify(mockProducerFeignClient).bindStockStoreOrder(
                new StockStoreBindReqOrderDTO("storeGuid", "branchStoreGuid", "orderId"));
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testReduceStockForOrderMDM_ItemClientServiceListPkgItemInfoReturnsNoItems() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setDiscountTotal(new BigDecimal("0.00"));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        orderReadDO.setAdjustState(0);
        orderReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("erpItemSkuGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setItemName("dishesName");
        itemDO.setItemCode("dishesCode");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setIsAdjustItem(false);
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(Collections.emptyList());

        when(mockItemClientService.getErpSkuGuids(new SingleDataDTO("jh", Arrays.asList("value"))))
                .thenReturn(new HashMap<>());

        // Run the test
        erpServiceImplUnderTest.reduceStockForOrderMDM(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
        verify(mockProducerFeignClient).bindStockStoreOrder(
                new StockStoreBindReqOrderDTO("storeGuid", "branchStoreGuid", "orderId"));
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testAddStockForOrderMDM() throws Exception {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setDiscountTotal(new BigDecimal("0.00"));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        orderReadDO.setAdjustState(0);
        orderReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("erpItemSkuGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setItemName("dishesName");
        itemDO.setItemCode("dishesCode");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setIsAdjustItem(false);
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure TradeClientService.listByOrderItemGuids(...).
        final AdjustByOrderItemRespDTO adjustByOrderItemRespDTO = new AdjustByOrderItemRespDTO();
        adjustByOrderItemRespDTO.setGuid(0L);
        adjustByOrderItemRespDTO.setOrderItemGuid("orderItemGuid");
        adjustByOrderItemRespDTO.setItemType(0);
        adjustByOrderItemRespDTO.setSkuGuid("erpItemSkuGuid");
        adjustByOrderItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        adjustByOrderItemRespDTO.setAdjustType(0);
        adjustByOrderItemRespDTO.setParentItemGuid(0L);
        adjustByOrderItemRespDTO.setPackageDefaultCount(new BigDecimal("0.00"));
        final List<AdjustByOrderItemRespDTO> adjustByOrderItemRespDTOS = Arrays.asList(adjustByOrderItemRespDTO);
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderItemGuids(queryDTO)).thenReturn(adjustByOrderItemRespDTOS);

        // Configure ItemClientService.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        skuTakeawayInfoRespDTO.setItemGuid("itemGuid");
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("erpItemSkuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ItemClientService.findParentSKUS(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setId(0L);
        skuInfoRespDTO.setTypeGuid("typeGuid");
        skuInfoRespDTO.setItemTypeGuid("itemTypeGuid");
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setParentGuid("parentGuid");
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockItemClientService.findParentSKUS(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getBindStockStoreOrder(...).
        final StoreBindOrderDO storeBindOrderDO = new StoreBindOrderDO();
        storeBindOrderDO.setId(0L);
        storeBindOrderDO.setStoreGuid("storeGuid");
        storeBindOrderDO.setBranchStoreGuid("branchStoreGuid");
        storeBindOrderDO.setOrderId("orderId");
        storeBindOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockProducerFeignClient.getBindStockStoreOrder("storeGuid", "orderId")).thenReturn(storeBindOrderDO);

        // Configure ItemMappingMapper.selectList(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        when(mockItemClientService.getErpSkuGuids(new SingleDataDTO("jh", Arrays.asList("value"))))
                .thenReturn(new HashMap<>());

        // Run the test
        erpServiceImplUnderTest.addStockForOrderMDM(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
    }

    @Test
    public void testAddStockForOrderMDM_TradeClientServiceReturnsNoItems() throws Exception {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setDiscountTotal(new BigDecimal("0.00"));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        orderReadDO.setAdjustState(0);
        orderReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("erpItemSkuGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setItemName("dishesName");
        itemDO.setItemCode("dishesCode");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setIsAdjustItem(false);
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure TradeClientService.listByOrderItemGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderItemGuids(queryDTO)).thenReturn(Collections.emptyList());

        // Configure ItemClientService.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        skuTakeawayInfoRespDTO.setItemGuid("itemGuid");
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("erpItemSkuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ItemClientService.findParentSKUS(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setId(0L);
        skuInfoRespDTO.setTypeGuid("typeGuid");
        skuInfoRespDTO.setItemTypeGuid("itemTypeGuid");
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setParentGuid("parentGuid");
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockItemClientService.findParentSKUS(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Run the test
        erpServiceImplUnderTest.addStockForOrderMDM(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
    }

    @Test
    public void testAddStockForOrderMDM_ItemClientServiceListSkuInfoAndSubReturnsNoItems() throws Exception {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setDiscountTotal(new BigDecimal("0.00"));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        orderReadDO.setAdjustState(0);
        orderReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("erpItemSkuGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setItemName("dishesName");
        itemDO.setItemCode("dishesCode");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setIsAdjustItem(false);
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure TradeClientService.listByOrderItemGuids(...).
        final AdjustByOrderItemRespDTO adjustByOrderItemRespDTO = new AdjustByOrderItemRespDTO();
        adjustByOrderItemRespDTO.setGuid(0L);
        adjustByOrderItemRespDTO.setOrderItemGuid("orderItemGuid");
        adjustByOrderItemRespDTO.setItemType(0);
        adjustByOrderItemRespDTO.setSkuGuid("erpItemSkuGuid");
        adjustByOrderItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        adjustByOrderItemRespDTO.setAdjustType(0);
        adjustByOrderItemRespDTO.setParentItemGuid(0L);
        adjustByOrderItemRespDTO.setPackageDefaultCount(new BigDecimal("0.00"));
        final List<AdjustByOrderItemRespDTO> adjustByOrderItemRespDTOS = Arrays.asList(adjustByOrderItemRespDTO);
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderItemGuids(queryDTO)).thenReturn(adjustByOrderItemRespDTOS);

        // Configure ItemClientService.listSkuInfoAndSub(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listSkuInfoAndSub(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Configure ItemClientService.findParentSKUS(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setId(0L);
        skuInfoRespDTO.setTypeGuid("typeGuid");
        skuInfoRespDTO.setItemTypeGuid("itemTypeGuid");
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setParentGuid("parentGuid");
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockItemClientService.findParentSKUS(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Run the test
        erpServiceImplUnderTest.addStockForOrderMDM(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
    }

    @Test
    public void testAddStockForOrderMDM_ItemClientServiceFindParentSKUSReturnsNoItems() throws Exception {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setDiscountTotal(new BigDecimal("0.00"));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        orderReadDO.setAdjustState(0);
        orderReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("erpItemSkuGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setItemName("dishesName");
        itemDO.setItemCode("dishesCode");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setIsAdjustItem(false);
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure TradeClientService.listByOrderItemGuids(...).
        final AdjustByOrderItemRespDTO adjustByOrderItemRespDTO = new AdjustByOrderItemRespDTO();
        adjustByOrderItemRespDTO.setGuid(0L);
        adjustByOrderItemRespDTO.setOrderItemGuid("orderItemGuid");
        adjustByOrderItemRespDTO.setItemType(0);
        adjustByOrderItemRespDTO.setSkuGuid("erpItemSkuGuid");
        adjustByOrderItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        adjustByOrderItemRespDTO.setAdjustType(0);
        adjustByOrderItemRespDTO.setParentItemGuid(0L);
        adjustByOrderItemRespDTO.setPackageDefaultCount(new BigDecimal("0.00"));
        final List<AdjustByOrderItemRespDTO> adjustByOrderItemRespDTOS = Arrays.asList(adjustByOrderItemRespDTO);
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderItemGuids(queryDTO)).thenReturn(adjustByOrderItemRespDTOS);

        // Configure ItemClientService.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        skuTakeawayInfoRespDTO.setItemGuid("itemGuid");
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("erpItemSkuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        when(mockItemClientService.findParentSKUS(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Run the test
        erpServiceImplUnderTest.addStockForOrderMDM(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
    }

    @Test
    public void testAddStockForOrderMDM_ItemMappingMapperReturnsNoItems() throws Exception {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setDiscountTotal(new BigDecimal("0.00"));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        orderReadDO.setAdjustState(0);
        orderReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("erpItemSkuGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setItemName("dishesName");
        itemDO.setItemCode("dishesCode");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setIsAdjustItem(false);
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();

        // Configure ProducerFeignClient.getBindStockStoreOrder(...).
        final StoreBindOrderDO storeBindOrderDO = new StoreBindOrderDO();
        storeBindOrderDO.setId(0L);
        storeBindOrderDO.setStoreGuid("storeGuid");
        storeBindOrderDO.setBranchStoreGuid("branchStoreGuid");
        storeBindOrderDO.setOrderId("orderId");
        storeBindOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockProducerFeignClient.getBindStockStoreOrder("storeGuid", "orderId")).thenReturn(storeBindOrderDO);

        when(mockItemMappingMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockItemClientService.getErpSkuGuids(new SingleDataDTO("jh", Arrays.asList("value"))))
                .thenReturn(new HashMap<>());

        // Run the test
        erpServiceImplUnderTest.addStockForOrderMDM(orderReadDO, mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
    }
}
