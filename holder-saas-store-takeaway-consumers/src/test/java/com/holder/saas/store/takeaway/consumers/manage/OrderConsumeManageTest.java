package com.holder.saas.store.takeaway.consumers.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.SkuMapDO;
import com.holder.saas.store.takeaway.consumers.mapper.SkuMapMapper;
import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holder.saas.store.takeaway.consumers.service.rpc.CloudEnterpriseFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holderzone.saas.store.dto.takeaway.UnDiscount;
import com.holderzone.saas.store.dto.takeaway.UnItem;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderConsumeManageTest {

    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private OrderService mockOrderService;
    @Mock
    private CloudEnterpriseFeignClient mockEnterpriseFeignClient;
    @Mock
    private SkuMapMapper mockSkuMapMapper;

    private OrderConsumeManage orderConsumeManageUnderTest;

    @Before
    public void setUp() {
        orderConsumeManageUnderTest = new OrderConsumeManage(mockDynamicHelper, mockOrderService,
                mockEnterpriseFeignClient, mockSkuMapMapper);
    }

    @Test
    public void testProcess() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setCbMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        unOrder.setOrderId("orderId");
        unOrder.setShipperName("shipperName");
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unOrder.setArrayOfUnItem(Arrays.asList(unItem));
        final UnDiscount unDiscount = new UnDiscount();
        unDiscount.setDiscountName("discountName");
        unOrder.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        when(mockEnterpriseFeignClient.hasEnterprise("enterpriseGuid")).thenReturn(false);

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("49582eef-41f5-40bc-839a-dffba188fb44");
        skuMapDO.setSourceGuid("itemSku");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        // Run the test
        final boolean result = orderConsumeManageUnderTest.process(unOrder);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");

        // Confirm OrderService.orderCreate(...).
        final UnOrder unOrder1 = new UnOrder();
        unOrder1.setCbMsgType(0);
        unOrder1.setOrderSubType(0);
        unOrder1.setEnterpriseGuid("enterpriseGuid");
        unOrder1.setStoreGuid("storeGuid");
        unOrder1.setOrderId("orderId");
        unOrder1.setShipperName("shipperName");
        final UnItem unItem1 = new UnItem();
        unItem1.setItemSku("itemSku");
        unOrder1.setArrayOfUnItem(Arrays.asList(unItem1));
        final UnDiscount unDiscount1 = new UnDiscount();
        unDiscount1.setDiscountName("discountName");
        unOrder1.setArrayOfUnDiscount(Arrays.asList(unDiscount1));
        verify(mockOrderService).orderCreate(unOrder1);

        // Confirm OrderService.orderAccept(...).
        final UnOrder unOrder2 = new UnOrder();
        unOrder2.setCbMsgType(0);
        unOrder2.setOrderSubType(0);
        unOrder2.setEnterpriseGuid("enterpriseGuid");
        unOrder2.setStoreGuid("storeGuid");
        unOrder2.setOrderId("orderId");
        unOrder2.setShipperName("shipperName");
        final UnItem unItem2 = new UnItem();
        unItem2.setItemSku("itemSku");
        unOrder2.setArrayOfUnItem(Arrays.asList(unItem2));
        final UnDiscount unDiscount2 = new UnDiscount();
        unDiscount2.setDiscountName("discountName");
        unOrder2.setArrayOfUnDiscount(Arrays.asList(unDiscount2));
        verify(mockOrderService).orderAccept(unOrder2);

        // Confirm OrderService.updateShipping(...).
        final UnOrder unOrder3 = new UnOrder();
        unOrder3.setCbMsgType(0);
        unOrder3.setOrderSubType(0);
        unOrder3.setEnterpriseGuid("enterpriseGuid");
        unOrder3.setStoreGuid("storeGuid");
        unOrder3.setOrderId("orderId");
        unOrder3.setShipperName("shipperName");
        final UnItem unItem3 = new UnItem();
        unItem3.setItemSku("itemSku");
        unOrder3.setArrayOfUnItem(Arrays.asList(unItem3));
        final UnDiscount unDiscount3 = new UnDiscount();
        unDiscount3.setDiscountName("discountName");
        unOrder3.setArrayOfUnDiscount(Arrays.asList(unDiscount3));
        verify(mockOrderService).updateShipping(unOrder3);

        // Confirm OrderService.updateShippingCompleted(...).
        final UnOrder unOrder4 = new UnOrder();
        unOrder4.setCbMsgType(0);
        unOrder4.setOrderSubType(0);
        unOrder4.setEnterpriseGuid("enterpriseGuid");
        unOrder4.setStoreGuid("storeGuid");
        unOrder4.setOrderId("orderId");
        unOrder4.setShipperName("shipperName");
        final UnItem unItem4 = new UnItem();
        unItem4.setItemSku("itemSku");
        unOrder4.setArrayOfUnItem(Arrays.asList(unItem4));
        final UnDiscount unDiscount4 = new UnDiscount();
        unDiscount4.setDiscountName("discountName");
        unOrder4.setArrayOfUnDiscount(Arrays.asList(unDiscount4));
        verify(mockOrderService).updateShippingCompleted(unOrder4);

        // Confirm OrderService.updateShippingDistribute(...).
        final UnOrder unOrder5 = new UnOrder();
        unOrder5.setCbMsgType(0);
        unOrder5.setOrderSubType(0);
        unOrder5.setEnterpriseGuid("enterpriseGuid");
        unOrder5.setStoreGuid("storeGuid");
        unOrder5.setOrderId("orderId");
        unOrder5.setShipperName("shipperName");
        final UnItem unItem5 = new UnItem();
        unItem5.setItemSku("itemSku");
        unOrder5.setArrayOfUnItem(Arrays.asList(unItem5));
        final UnDiscount unDiscount5 = new UnDiscount();
        unDiscount5.setDiscountName("discountName");
        unOrder5.setArrayOfUnDiscount(Arrays.asList(unDiscount5));
        verify(mockOrderService).updateShippingDistribute(unOrder5);

        // Confirm OrderService.orderCompleted(...).
        final UnOrder unOrder6 = new UnOrder();
        unOrder6.setCbMsgType(0);
        unOrder6.setOrderSubType(0);
        unOrder6.setEnterpriseGuid("enterpriseGuid");
        unOrder6.setStoreGuid("storeGuid");
        unOrder6.setOrderId("orderId");
        unOrder6.setShipperName("shipperName");
        final UnItem unItem6 = new UnItem();
        unItem6.setItemSku("itemSku");
        unOrder6.setArrayOfUnItem(Arrays.asList(unItem6));
        final UnDiscount unDiscount6 = new UnDiscount();
        unDiscount6.setDiscountName("discountName");
        unOrder6.setArrayOfUnDiscount(Arrays.asList(unDiscount6));
        verify(mockOrderService).orderCompleted(unOrder6);

        // Confirm OrderService.orderCanceledAsReject(...).
        final UnOrder unOrder7 = new UnOrder();
        unOrder7.setCbMsgType(0);
        unOrder7.setOrderSubType(0);
        unOrder7.setEnterpriseGuid("enterpriseGuid");
        unOrder7.setStoreGuid("storeGuid");
        unOrder7.setOrderId("orderId");
        unOrder7.setShipperName("shipperName");
        final UnItem unItem7 = new UnItem();
        unItem7.setItemSku("itemSku");
        unOrder7.setArrayOfUnItem(Arrays.asList(unItem7));
        final UnDiscount unDiscount7 = new UnDiscount();
        unDiscount7.setDiscountName("discountName");
        unOrder7.setArrayOfUnDiscount(Arrays.asList(unDiscount7));
        verify(mockOrderService).orderCanceledAsReject(unOrder7);

        // Confirm OrderService.orderCanceled(...).
        final UnOrder unOrder8 = new UnOrder();
        unOrder8.setCbMsgType(0);
        unOrder8.setOrderSubType(0);
        unOrder8.setEnterpriseGuid("enterpriseGuid");
        unOrder8.setStoreGuid("storeGuid");
        unOrder8.setOrderId("orderId");
        unOrder8.setShipperName("shipperName");
        final UnItem unItem8 = new UnItem();
        unItem8.setItemSku("itemSku");
        unOrder8.setArrayOfUnItem(Arrays.asList(unItem8));
        final UnDiscount unDiscount8 = new UnDiscount();
        unDiscount8.setDiscountName("discountName");
        unOrder8.setArrayOfUnDiscount(Arrays.asList(unDiscount8));
        verify(mockOrderService).orderCanceled(unOrder8);

        // Confirm OrderService.orderRemindByUser(...).
        final UnOrder unOrder9 = new UnOrder();
        unOrder9.setCbMsgType(0);
        unOrder9.setOrderSubType(0);
        unOrder9.setEnterpriseGuid("enterpriseGuid");
        unOrder9.setStoreGuid("storeGuid");
        unOrder9.setOrderId("orderId");
        unOrder9.setShipperName("shipperName");
        final UnItem unItem9 = new UnItem();
        unItem9.setItemSku("itemSku");
        unOrder9.setArrayOfUnItem(Arrays.asList(unItem9));
        final UnDiscount unDiscount9 = new UnDiscount();
        unDiscount9.setDiscountName("discountName");
        unOrder9.setArrayOfUnDiscount(Arrays.asList(unDiscount9));
        verify(mockOrderService).orderRemindByUser(unOrder9);

        // Confirm OrderService.cancelOrderReq(...).
        final UnOrder unOrder10 = new UnOrder();
        unOrder10.setCbMsgType(0);
        unOrder10.setOrderSubType(0);
        unOrder10.setEnterpriseGuid("enterpriseGuid");
        unOrder10.setStoreGuid("storeGuid");
        unOrder10.setOrderId("orderId");
        unOrder10.setShipperName("shipperName");
        final UnItem unItem10 = new UnItem();
        unItem10.setItemSku("itemSku");
        unOrder10.setArrayOfUnItem(Arrays.asList(unItem10));
        final UnDiscount unDiscount10 = new UnDiscount();
        unDiscount10.setDiscountName("discountName");
        unOrder10.setArrayOfUnDiscount(Arrays.asList(unDiscount10));
        verify(mockOrderService).cancelOrderReq(unOrder10);

        // Confirm OrderService.cancelCancelOrderReq(...).
        final UnOrder unOrder11 = new UnOrder();
        unOrder11.setCbMsgType(0);
        unOrder11.setOrderSubType(0);
        unOrder11.setEnterpriseGuid("enterpriseGuid");
        unOrder11.setStoreGuid("storeGuid");
        unOrder11.setOrderId("orderId");
        unOrder11.setShipperName("shipperName");
        final UnItem unItem11 = new UnItem();
        unItem11.setItemSku("itemSku");
        unOrder11.setArrayOfUnItem(Arrays.asList(unItem11));
        final UnDiscount unDiscount11 = new UnDiscount();
        unDiscount11.setDiscountName("discountName");
        unOrder11.setArrayOfUnDiscount(Arrays.asList(unDiscount11));
        verify(mockOrderService).cancelCancelOrderReq(unOrder11);

        // Confirm OrderService.cancelReqAgreed(...).
        final UnOrder unOrder12 = new UnOrder();
        unOrder12.setCbMsgType(0);
        unOrder12.setOrderSubType(0);
        unOrder12.setEnterpriseGuid("enterpriseGuid");
        unOrder12.setStoreGuid("storeGuid");
        unOrder12.setOrderId("orderId");
        unOrder12.setShipperName("shipperName");
        final UnItem unItem12 = new UnItem();
        unItem12.setItemSku("itemSku");
        unOrder12.setArrayOfUnItem(Arrays.asList(unItem12));
        final UnDiscount unDiscount12 = new UnDiscount();
        unDiscount12.setDiscountName("discountName");
        unOrder12.setArrayOfUnDiscount(Arrays.asList(unDiscount12));
        verify(mockOrderService).cancelReqAgreed(unOrder12);

        // Confirm OrderService.cancelReqDisagreed(...).
        final UnOrder unOrder13 = new UnOrder();
        unOrder13.setCbMsgType(0);
        unOrder13.setOrderSubType(0);
        unOrder13.setEnterpriseGuid("enterpriseGuid");
        unOrder13.setStoreGuid("storeGuid");
        unOrder13.setOrderId("orderId");
        unOrder13.setShipperName("shipperName");
        final UnItem unItem13 = new UnItem();
        unItem13.setItemSku("itemSku");
        unOrder13.setArrayOfUnItem(Arrays.asList(unItem13));
        final UnDiscount unDiscount13 = new UnDiscount();
        unDiscount13.setDiscountName("discountName");
        unOrder13.setArrayOfUnDiscount(Arrays.asList(unDiscount13));
        verify(mockOrderService).cancelReqDisagreed(unOrder13);

        // Confirm OrderService.cancelArbitrationEffective(...).
        final UnOrder unOrder14 = new UnOrder();
        unOrder14.setCbMsgType(0);
        unOrder14.setOrderSubType(0);
        unOrder14.setEnterpriseGuid("enterpriseGuid");
        unOrder14.setStoreGuid("storeGuid");
        unOrder14.setOrderId("orderId");
        unOrder14.setShipperName("shipperName");
        final UnItem unItem14 = new UnItem();
        unItem14.setItemSku("itemSku");
        unOrder14.setArrayOfUnItem(Arrays.asList(unItem14));
        final UnDiscount unDiscount14 = new UnDiscount();
        unDiscount14.setDiscountName("discountName");
        unOrder14.setArrayOfUnDiscount(Arrays.asList(unDiscount14));
        verify(mockOrderService).cancelArbitrationEffective(unOrder14);

        // Confirm OrderService.refundOrderReq(...).
        final UnOrder unOrder15 = new UnOrder();
        unOrder15.setCbMsgType(0);
        unOrder15.setOrderSubType(0);
        unOrder15.setEnterpriseGuid("enterpriseGuid");
        unOrder15.setStoreGuid("storeGuid");
        unOrder15.setOrderId("orderId");
        unOrder15.setShipperName("shipperName");
        final UnItem unItem15 = new UnItem();
        unItem15.setItemSku("itemSku");
        unOrder15.setArrayOfUnItem(Arrays.asList(unItem15));
        final UnDiscount unDiscount15 = new UnDiscount();
        unDiscount15.setDiscountName("discountName");
        unOrder15.setArrayOfUnDiscount(Arrays.asList(unDiscount15));
        verify(mockOrderService).refundOrderReq(unOrder15);

        // Confirm OrderService.cancelRefundOrderReq(...).
        final UnOrder unOrder16 = new UnOrder();
        unOrder16.setCbMsgType(0);
        unOrder16.setOrderSubType(0);
        unOrder16.setEnterpriseGuid("enterpriseGuid");
        unOrder16.setStoreGuid("storeGuid");
        unOrder16.setOrderId("orderId");
        unOrder16.setShipperName("shipperName");
        final UnItem unItem16 = new UnItem();
        unItem16.setItemSku("itemSku");
        unOrder16.setArrayOfUnItem(Arrays.asList(unItem16));
        final UnDiscount unDiscount16 = new UnDiscount();
        unDiscount16.setDiscountName("discountName");
        unOrder16.setArrayOfUnDiscount(Arrays.asList(unDiscount16));
        verify(mockOrderService).cancelRefundOrderReq(unOrder16);

        // Confirm OrderService.refundReqAgreed(...).
        final UnOrder unOrder17 = new UnOrder();
        unOrder17.setCbMsgType(0);
        unOrder17.setOrderSubType(0);
        unOrder17.setEnterpriseGuid("enterpriseGuid");
        unOrder17.setStoreGuid("storeGuid");
        unOrder17.setOrderId("orderId");
        unOrder17.setShipperName("shipperName");
        final UnItem unItem17 = new UnItem();
        unItem17.setItemSku("itemSku");
        unOrder17.setArrayOfUnItem(Arrays.asList(unItem17));
        final UnDiscount unDiscount17 = new UnDiscount();
        unDiscount17.setDiscountName("discountName");
        unOrder17.setArrayOfUnDiscount(Arrays.asList(unDiscount17));
        verify(mockOrderService).refundReqAgreed(unOrder17);

        // Confirm OrderService.refundReqDisagreed(...).
        final UnOrder unOrder18 = new UnOrder();
        unOrder18.setCbMsgType(0);
        unOrder18.setOrderSubType(0);
        unOrder18.setEnterpriseGuid("enterpriseGuid");
        unOrder18.setStoreGuid("storeGuid");
        unOrder18.setOrderId("orderId");
        unOrder18.setShipperName("shipperName");
        final UnItem unItem18 = new UnItem();
        unItem18.setItemSku("itemSku");
        unOrder18.setArrayOfUnItem(Arrays.asList(unItem18));
        final UnDiscount unDiscount18 = new UnDiscount();
        unDiscount18.setDiscountName("discountName");
        unOrder18.setArrayOfUnDiscount(Arrays.asList(unDiscount18));
        verify(mockOrderService).refundReqDisagreed(unOrder18);

        // Confirm OrderService.refundArbitrationEffective(...).
        final UnOrder unOrder19 = new UnOrder();
        unOrder19.setCbMsgType(0);
        unOrder19.setOrderSubType(0);
        unOrder19.setEnterpriseGuid("enterpriseGuid");
        unOrder19.setStoreGuid("storeGuid");
        unOrder19.setOrderId("orderId");
        unOrder19.setShipperName("shipperName");
        final UnItem unItem19 = new UnItem();
        unItem19.setItemSku("itemSku");
        unOrder19.setArrayOfUnItem(Arrays.asList(unItem19));
        final UnDiscount unDiscount19 = new UnDiscount();
        unDiscount19.setDiscountName("discountName");
        unOrder19.setArrayOfUnDiscount(Arrays.asList(unDiscount19));
        verify(mockOrderService).refundArbitrationEffective(unOrder19);

        // Confirm OrderService.orderOwnSave(...).
        final UnOrder unOrder20 = new UnOrder();
        unOrder20.setCbMsgType(0);
        unOrder20.setOrderSubType(0);
        unOrder20.setEnterpriseGuid("enterpriseGuid");
        unOrder20.setStoreGuid("storeGuid");
        unOrder20.setOrderId("orderId");
        unOrder20.setShipperName("shipperName");
        final UnItem unItem20 = new UnItem();
        unItem20.setItemSku("itemSku");
        unOrder20.setArrayOfUnItem(Arrays.asList(unItem20));
        final UnDiscount unDiscount20 = new UnDiscount();
        unDiscount20.setDiscountName("discountName");
        unOrder20.setArrayOfUnDiscount(Arrays.asList(unDiscount20));
        verify(mockOrderService).orderOwnSave(unOrder20);

        // Confirm OrderService.deliveryError(...).
        final UnOrder unOrder21 = new UnOrder();
        unOrder21.setCbMsgType(0);
        unOrder21.setOrderSubType(0);
        unOrder21.setEnterpriseGuid("enterpriseGuid");
        unOrder21.setStoreGuid("storeGuid");
        unOrder21.setOrderId("orderId");
        unOrder21.setShipperName("shipperName");
        final UnItem unItem21 = new UnItem();
        unItem21.setItemSku("itemSku");
        unOrder21.setArrayOfUnItem(Arrays.asList(unItem21));
        final UnDiscount unDiscount21 = new UnDiscount();
        unDiscount21.setDiscountName("discountName");
        unOrder21.setArrayOfUnDiscount(Arrays.asList(unDiscount21));
        verify(mockOrderService).deliveryError(unOrder21);

        // Confirm OrderService.diningOutTcd(...).
        final UnOrder unOrder22 = new UnOrder();
        unOrder22.setCbMsgType(0);
        unOrder22.setOrderSubType(0);
        unOrder22.setEnterpriseGuid("enterpriseGuid");
        unOrder22.setStoreGuid("storeGuid");
        unOrder22.setOrderId("orderId");
        unOrder22.setShipperName("shipperName");
        final UnItem unItem22 = new UnItem();
        unItem22.setItemSku("itemSku");
        unOrder22.setArrayOfUnItem(Arrays.asList(unItem22));
        final UnDiscount unDiscount22 = new UnDiscount();
        unDiscount22.setDiscountName("discountName");
        unOrder22.setArrayOfUnDiscount(Arrays.asList(unDiscount22));
        verify(mockOrderService).diningOutTcd(unOrder22);
        verify(mockDynamicHelper).removeThreadLocalDatabaseInfo();
    }

    @Test
    public void testProcess_SkuMapMapperReturnsNoItems() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setCbMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        unOrder.setOrderId("orderId");
        unOrder.setShipperName("shipperName");
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unOrder.setArrayOfUnItem(Arrays.asList(unItem));
        final UnDiscount unDiscount = new UnDiscount();
        unDiscount.setDiscountName("discountName");
        unOrder.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        when(mockEnterpriseFeignClient.hasEnterprise("enterpriseGuid")).thenReturn(false);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = orderConsumeManageUnderTest.process(unOrder);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");

        // Confirm OrderService.orderCreate(...).
        final UnOrder unOrder1 = new UnOrder();
        unOrder1.setCbMsgType(0);
        unOrder1.setOrderSubType(0);
        unOrder1.setEnterpriseGuid("enterpriseGuid");
        unOrder1.setStoreGuid("storeGuid");
        unOrder1.setOrderId("orderId");
        unOrder1.setShipperName("shipperName");
        final UnItem unItem1 = new UnItem();
        unItem1.setItemSku("itemSku");
        unOrder1.setArrayOfUnItem(Arrays.asList(unItem1));
        final UnDiscount unDiscount1 = new UnDiscount();
        unDiscount1.setDiscountName("discountName");
        unOrder1.setArrayOfUnDiscount(Arrays.asList(unDiscount1));
        verify(mockOrderService).orderCreate(unOrder1);

        // Confirm OrderService.orderAccept(...).
        final UnOrder unOrder2 = new UnOrder();
        unOrder2.setCbMsgType(0);
        unOrder2.setOrderSubType(0);
        unOrder2.setEnterpriseGuid("enterpriseGuid");
        unOrder2.setStoreGuid("storeGuid");
        unOrder2.setOrderId("orderId");
        unOrder2.setShipperName("shipperName");
        final UnItem unItem2 = new UnItem();
        unItem2.setItemSku("itemSku");
        unOrder2.setArrayOfUnItem(Arrays.asList(unItem2));
        final UnDiscount unDiscount2 = new UnDiscount();
        unDiscount2.setDiscountName("discountName");
        unOrder2.setArrayOfUnDiscount(Arrays.asList(unDiscount2));
        verify(mockOrderService).orderAccept(unOrder2);

        // Confirm OrderService.updateShipping(...).
        final UnOrder unOrder3 = new UnOrder();
        unOrder3.setCbMsgType(0);
        unOrder3.setOrderSubType(0);
        unOrder3.setEnterpriseGuid("enterpriseGuid");
        unOrder3.setStoreGuid("storeGuid");
        unOrder3.setOrderId("orderId");
        unOrder3.setShipperName("shipperName");
        final UnItem unItem3 = new UnItem();
        unItem3.setItemSku("itemSku");
        unOrder3.setArrayOfUnItem(Arrays.asList(unItem3));
        final UnDiscount unDiscount3 = new UnDiscount();
        unDiscount3.setDiscountName("discountName");
        unOrder3.setArrayOfUnDiscount(Arrays.asList(unDiscount3));
        verify(mockOrderService).updateShipping(unOrder3);

        // Confirm OrderService.updateShippingCompleted(...).
        final UnOrder unOrder4 = new UnOrder();
        unOrder4.setCbMsgType(0);
        unOrder4.setOrderSubType(0);
        unOrder4.setEnterpriseGuid("enterpriseGuid");
        unOrder4.setStoreGuid("storeGuid");
        unOrder4.setOrderId("orderId");
        unOrder4.setShipperName("shipperName");
        final UnItem unItem4 = new UnItem();
        unItem4.setItemSku("itemSku");
        unOrder4.setArrayOfUnItem(Arrays.asList(unItem4));
        final UnDiscount unDiscount4 = new UnDiscount();
        unDiscount4.setDiscountName("discountName");
        unOrder4.setArrayOfUnDiscount(Arrays.asList(unDiscount4));
        verify(mockOrderService).updateShippingCompleted(unOrder4);

        // Confirm OrderService.updateShippingDistribute(...).
        final UnOrder unOrder5 = new UnOrder();
        unOrder5.setCbMsgType(0);
        unOrder5.setOrderSubType(0);
        unOrder5.setEnterpriseGuid("enterpriseGuid");
        unOrder5.setStoreGuid("storeGuid");
        unOrder5.setOrderId("orderId");
        unOrder5.setShipperName("shipperName");
        final UnItem unItem5 = new UnItem();
        unItem5.setItemSku("itemSku");
        unOrder5.setArrayOfUnItem(Arrays.asList(unItem5));
        final UnDiscount unDiscount5 = new UnDiscount();
        unDiscount5.setDiscountName("discountName");
        unOrder5.setArrayOfUnDiscount(Arrays.asList(unDiscount5));
        verify(mockOrderService).updateShippingDistribute(unOrder5);

        // Confirm OrderService.orderCompleted(...).
        final UnOrder unOrder6 = new UnOrder();
        unOrder6.setCbMsgType(0);
        unOrder6.setOrderSubType(0);
        unOrder6.setEnterpriseGuid("enterpriseGuid");
        unOrder6.setStoreGuid("storeGuid");
        unOrder6.setOrderId("orderId");
        unOrder6.setShipperName("shipperName");
        final UnItem unItem6 = new UnItem();
        unItem6.setItemSku("itemSku");
        unOrder6.setArrayOfUnItem(Arrays.asList(unItem6));
        final UnDiscount unDiscount6 = new UnDiscount();
        unDiscount6.setDiscountName("discountName");
        unOrder6.setArrayOfUnDiscount(Arrays.asList(unDiscount6));
        verify(mockOrderService).orderCompleted(unOrder6);

        // Confirm OrderService.orderCanceledAsReject(...).
        final UnOrder unOrder7 = new UnOrder();
        unOrder7.setCbMsgType(0);
        unOrder7.setOrderSubType(0);
        unOrder7.setEnterpriseGuid("enterpriseGuid");
        unOrder7.setStoreGuid("storeGuid");
        unOrder7.setOrderId("orderId");
        unOrder7.setShipperName("shipperName");
        final UnItem unItem7 = new UnItem();
        unItem7.setItemSku("itemSku");
        unOrder7.setArrayOfUnItem(Arrays.asList(unItem7));
        final UnDiscount unDiscount7 = new UnDiscount();
        unDiscount7.setDiscountName("discountName");
        unOrder7.setArrayOfUnDiscount(Arrays.asList(unDiscount7));
        verify(mockOrderService).orderCanceledAsReject(unOrder7);

        // Confirm OrderService.orderCanceled(...).
        final UnOrder unOrder8 = new UnOrder();
        unOrder8.setCbMsgType(0);
        unOrder8.setOrderSubType(0);
        unOrder8.setEnterpriseGuid("enterpriseGuid");
        unOrder8.setStoreGuid("storeGuid");
        unOrder8.setOrderId("orderId");
        unOrder8.setShipperName("shipperName");
        final UnItem unItem8 = new UnItem();
        unItem8.setItemSku("itemSku");
        unOrder8.setArrayOfUnItem(Arrays.asList(unItem8));
        final UnDiscount unDiscount8 = new UnDiscount();
        unDiscount8.setDiscountName("discountName");
        unOrder8.setArrayOfUnDiscount(Arrays.asList(unDiscount8));
        verify(mockOrderService).orderCanceled(unOrder8);

        // Confirm OrderService.orderRemindByUser(...).
        final UnOrder unOrder9 = new UnOrder();
        unOrder9.setCbMsgType(0);
        unOrder9.setOrderSubType(0);
        unOrder9.setEnterpriseGuid("enterpriseGuid");
        unOrder9.setStoreGuid("storeGuid");
        unOrder9.setOrderId("orderId");
        unOrder9.setShipperName("shipperName");
        final UnItem unItem9 = new UnItem();
        unItem9.setItemSku("itemSku");
        unOrder9.setArrayOfUnItem(Arrays.asList(unItem9));
        final UnDiscount unDiscount9 = new UnDiscount();
        unDiscount9.setDiscountName("discountName");
        unOrder9.setArrayOfUnDiscount(Arrays.asList(unDiscount9));
        verify(mockOrderService).orderRemindByUser(unOrder9);

        // Confirm OrderService.cancelOrderReq(...).
        final UnOrder unOrder10 = new UnOrder();
        unOrder10.setCbMsgType(0);
        unOrder10.setOrderSubType(0);
        unOrder10.setEnterpriseGuid("enterpriseGuid");
        unOrder10.setStoreGuid("storeGuid");
        unOrder10.setOrderId("orderId");
        unOrder10.setShipperName("shipperName");
        final UnItem unItem10 = new UnItem();
        unItem10.setItemSku("itemSku");
        unOrder10.setArrayOfUnItem(Arrays.asList(unItem10));
        final UnDiscount unDiscount10 = new UnDiscount();
        unDiscount10.setDiscountName("discountName");
        unOrder10.setArrayOfUnDiscount(Arrays.asList(unDiscount10));
        verify(mockOrderService).cancelOrderReq(unOrder10);

        // Confirm OrderService.cancelCancelOrderReq(...).
        final UnOrder unOrder11 = new UnOrder();
        unOrder11.setCbMsgType(0);
        unOrder11.setOrderSubType(0);
        unOrder11.setEnterpriseGuid("enterpriseGuid");
        unOrder11.setStoreGuid("storeGuid");
        unOrder11.setOrderId("orderId");
        unOrder11.setShipperName("shipperName");
        final UnItem unItem11 = new UnItem();
        unItem11.setItemSku("itemSku");
        unOrder11.setArrayOfUnItem(Arrays.asList(unItem11));
        final UnDiscount unDiscount11 = new UnDiscount();
        unDiscount11.setDiscountName("discountName");
        unOrder11.setArrayOfUnDiscount(Arrays.asList(unDiscount11));
        verify(mockOrderService).cancelCancelOrderReq(unOrder11);

        // Confirm OrderService.cancelReqAgreed(...).
        final UnOrder unOrder12 = new UnOrder();
        unOrder12.setCbMsgType(0);
        unOrder12.setOrderSubType(0);
        unOrder12.setEnterpriseGuid("enterpriseGuid");
        unOrder12.setStoreGuid("storeGuid");
        unOrder12.setOrderId("orderId");
        unOrder12.setShipperName("shipperName");
        final UnItem unItem12 = new UnItem();
        unItem12.setItemSku("itemSku");
        unOrder12.setArrayOfUnItem(Arrays.asList(unItem12));
        final UnDiscount unDiscount12 = new UnDiscount();
        unDiscount12.setDiscountName("discountName");
        unOrder12.setArrayOfUnDiscount(Arrays.asList(unDiscount12));
        verify(mockOrderService).cancelReqAgreed(unOrder12);

        // Confirm OrderService.cancelReqDisagreed(...).
        final UnOrder unOrder13 = new UnOrder();
        unOrder13.setCbMsgType(0);
        unOrder13.setOrderSubType(0);
        unOrder13.setEnterpriseGuid("enterpriseGuid");
        unOrder13.setStoreGuid("storeGuid");
        unOrder13.setOrderId("orderId");
        unOrder13.setShipperName("shipperName");
        final UnItem unItem13 = new UnItem();
        unItem13.setItemSku("itemSku");
        unOrder13.setArrayOfUnItem(Arrays.asList(unItem13));
        final UnDiscount unDiscount13 = new UnDiscount();
        unDiscount13.setDiscountName("discountName");
        unOrder13.setArrayOfUnDiscount(Arrays.asList(unDiscount13));
        verify(mockOrderService).cancelReqDisagreed(unOrder13);

        // Confirm OrderService.cancelArbitrationEffective(...).
        final UnOrder unOrder14 = new UnOrder();
        unOrder14.setCbMsgType(0);
        unOrder14.setOrderSubType(0);
        unOrder14.setEnterpriseGuid("enterpriseGuid");
        unOrder14.setStoreGuid("storeGuid");
        unOrder14.setOrderId("orderId");
        unOrder14.setShipperName("shipperName");
        final UnItem unItem14 = new UnItem();
        unItem14.setItemSku("itemSku");
        unOrder14.setArrayOfUnItem(Arrays.asList(unItem14));
        final UnDiscount unDiscount14 = new UnDiscount();
        unDiscount14.setDiscountName("discountName");
        unOrder14.setArrayOfUnDiscount(Arrays.asList(unDiscount14));
        verify(mockOrderService).cancelArbitrationEffective(unOrder14);

        // Confirm OrderService.refundOrderReq(...).
        final UnOrder unOrder15 = new UnOrder();
        unOrder15.setCbMsgType(0);
        unOrder15.setOrderSubType(0);
        unOrder15.setEnterpriseGuid("enterpriseGuid");
        unOrder15.setStoreGuid("storeGuid");
        unOrder15.setOrderId("orderId");
        unOrder15.setShipperName("shipperName");
        final UnItem unItem15 = new UnItem();
        unItem15.setItemSku("itemSku");
        unOrder15.setArrayOfUnItem(Arrays.asList(unItem15));
        final UnDiscount unDiscount15 = new UnDiscount();
        unDiscount15.setDiscountName("discountName");
        unOrder15.setArrayOfUnDiscount(Arrays.asList(unDiscount15));
        verify(mockOrderService).refundOrderReq(unOrder15);

        // Confirm OrderService.cancelRefundOrderReq(...).
        final UnOrder unOrder16 = new UnOrder();
        unOrder16.setCbMsgType(0);
        unOrder16.setOrderSubType(0);
        unOrder16.setEnterpriseGuid("enterpriseGuid");
        unOrder16.setStoreGuid("storeGuid");
        unOrder16.setOrderId("orderId");
        unOrder16.setShipperName("shipperName");
        final UnItem unItem16 = new UnItem();
        unItem16.setItemSku("itemSku");
        unOrder16.setArrayOfUnItem(Arrays.asList(unItem16));
        final UnDiscount unDiscount16 = new UnDiscount();
        unDiscount16.setDiscountName("discountName");
        unOrder16.setArrayOfUnDiscount(Arrays.asList(unDiscount16));
        verify(mockOrderService).cancelRefundOrderReq(unOrder16);

        // Confirm OrderService.refundReqAgreed(...).
        final UnOrder unOrder17 = new UnOrder();
        unOrder17.setCbMsgType(0);
        unOrder17.setOrderSubType(0);
        unOrder17.setEnterpriseGuid("enterpriseGuid");
        unOrder17.setStoreGuid("storeGuid");
        unOrder17.setOrderId("orderId");
        unOrder17.setShipperName("shipperName");
        final UnItem unItem17 = new UnItem();
        unItem17.setItemSku("itemSku");
        unOrder17.setArrayOfUnItem(Arrays.asList(unItem17));
        final UnDiscount unDiscount17 = new UnDiscount();
        unDiscount17.setDiscountName("discountName");
        unOrder17.setArrayOfUnDiscount(Arrays.asList(unDiscount17));
        verify(mockOrderService).refundReqAgreed(unOrder17);

        // Confirm OrderService.refundReqDisagreed(...).
        final UnOrder unOrder18 = new UnOrder();
        unOrder18.setCbMsgType(0);
        unOrder18.setOrderSubType(0);
        unOrder18.setEnterpriseGuid("enterpriseGuid");
        unOrder18.setStoreGuid("storeGuid");
        unOrder18.setOrderId("orderId");
        unOrder18.setShipperName("shipperName");
        final UnItem unItem18 = new UnItem();
        unItem18.setItemSku("itemSku");
        unOrder18.setArrayOfUnItem(Arrays.asList(unItem18));
        final UnDiscount unDiscount18 = new UnDiscount();
        unDiscount18.setDiscountName("discountName");
        unOrder18.setArrayOfUnDiscount(Arrays.asList(unDiscount18));
        verify(mockOrderService).refundReqDisagreed(unOrder18);

        // Confirm OrderService.refundArbitrationEffective(...).
        final UnOrder unOrder19 = new UnOrder();
        unOrder19.setCbMsgType(0);
        unOrder19.setOrderSubType(0);
        unOrder19.setEnterpriseGuid("enterpriseGuid");
        unOrder19.setStoreGuid("storeGuid");
        unOrder19.setOrderId("orderId");
        unOrder19.setShipperName("shipperName");
        final UnItem unItem19 = new UnItem();
        unItem19.setItemSku("itemSku");
        unOrder19.setArrayOfUnItem(Arrays.asList(unItem19));
        final UnDiscount unDiscount19 = new UnDiscount();
        unDiscount19.setDiscountName("discountName");
        unOrder19.setArrayOfUnDiscount(Arrays.asList(unDiscount19));
        verify(mockOrderService).refundArbitrationEffective(unOrder19);

        // Confirm OrderService.orderOwnSave(...).
        final UnOrder unOrder20 = new UnOrder();
        unOrder20.setCbMsgType(0);
        unOrder20.setOrderSubType(0);
        unOrder20.setEnterpriseGuid("enterpriseGuid");
        unOrder20.setStoreGuid("storeGuid");
        unOrder20.setOrderId("orderId");
        unOrder20.setShipperName("shipperName");
        final UnItem unItem20 = new UnItem();
        unItem20.setItemSku("itemSku");
        unOrder20.setArrayOfUnItem(Arrays.asList(unItem20));
        final UnDiscount unDiscount20 = new UnDiscount();
        unDiscount20.setDiscountName("discountName");
        unOrder20.setArrayOfUnDiscount(Arrays.asList(unDiscount20));
        verify(mockOrderService).orderOwnSave(unOrder20);

        // Confirm OrderService.deliveryError(...).
        final UnOrder unOrder21 = new UnOrder();
        unOrder21.setCbMsgType(0);
        unOrder21.setOrderSubType(0);
        unOrder21.setEnterpriseGuid("enterpriseGuid");
        unOrder21.setStoreGuid("storeGuid");
        unOrder21.setOrderId("orderId");
        unOrder21.setShipperName("shipperName");
        final UnItem unItem21 = new UnItem();
        unItem21.setItemSku("itemSku");
        unOrder21.setArrayOfUnItem(Arrays.asList(unItem21));
        final UnDiscount unDiscount21 = new UnDiscount();
        unDiscount21.setDiscountName("discountName");
        unOrder21.setArrayOfUnDiscount(Arrays.asList(unDiscount21));
        verify(mockOrderService).deliveryError(unOrder21);

        // Confirm OrderService.diningOutTcd(...).
        final UnOrder unOrder22 = new UnOrder();
        unOrder22.setCbMsgType(0);
        unOrder22.setOrderSubType(0);
        unOrder22.setEnterpriseGuid("enterpriseGuid");
        unOrder22.setStoreGuid("storeGuid");
        unOrder22.setOrderId("orderId");
        unOrder22.setShipperName("shipperName");
        final UnItem unItem22 = new UnItem();
        unItem22.setItemSku("itemSku");
        unOrder22.setArrayOfUnItem(Arrays.asList(unItem22));
        final UnDiscount unDiscount22 = new UnDiscount();
        unDiscount22.setDiscountName("discountName");
        unOrder22.setArrayOfUnDiscount(Arrays.asList(unDiscount22));
        verify(mockOrderService).diningOutTcd(unOrder22);
        verify(mockDynamicHelper).removeThreadLocalDatabaseInfo();
    }
}
