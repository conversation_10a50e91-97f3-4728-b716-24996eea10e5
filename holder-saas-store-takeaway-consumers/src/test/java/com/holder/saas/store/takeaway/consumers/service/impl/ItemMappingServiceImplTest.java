package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.entity.domain.ItemMappingDO;
import com.holderzone.saas.store.dto.takeaway.TcdSyncItemMappingDTO;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class ItemMappingServiceImplTest {

    private ItemMappingServiceImpl itemMappingServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        itemMappingServiceImplUnderTest = new ItemMappingServiceImpl();
    }

    @Test
    public void testQueryItemMappingInfo() {
        // Setup
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> expectedResult = Arrays.asList(itemMappingDO);

        // Run the test
        final List<ItemMappingDO> result = itemMappingServiceImplUnderTest.queryItemMappingInfo(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByStoreGuidAndTakeoutTypeAndMapperGuids() {
        // Setup
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> expectedResult = Arrays.asList(itemMappingDO);

        // Run the test
        final List<ItemMappingDO> result = itemMappingServiceImplUnderTest.queryByStoreGuidAndTakeoutTypeAndMapperGuids(
                "storeGuid", 0, Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSyncTcdItemMapping() {
        // Setup
        final TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping tcdItemMapping = new TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping();
        tcdItemMapping.setSourceUnItemSkuId("sourceUnItemSkuId");
        tcdItemMapping.setTargetUnItemSkuId("targetUnItemSkuId");
        tcdItemMapping.setSyncUnItemSkuId("syncUnItemSkuId");
        final List<TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping> tcdItemMappingList = Arrays.asList(
                tcdItemMapping);

        // Run the test
        itemMappingServiceImplUnderTest.syncTcdItemMapping(tcdItemMappingList);

        // Verify the results
    }
}
