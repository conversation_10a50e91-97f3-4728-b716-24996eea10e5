package com.holder.saas.store.takeaway.consumers.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holder.saas.store.takeaway.consumers.entity.bo.FixItemBiz;
import com.holder.saas.store.takeaway.consumers.entity.domain.*;
import com.holder.saas.store.takeaway.consumers.service.*;
import com.holder.saas.store.takeaway.consumers.service.rpc.ItemFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.TradeClientService;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.takeaway.UnItemBaseMapReq;
import com.holderzone.saas.store.dto.takeaway.UnItemBatchUnbindReq;
import com.holderzone.saas.store.dto.takeaway.UnItemBindUnbindReq;
import com.holderzone.saas.store.dto.takeaway.UnMappedItem;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutAndStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StockStoreBindResqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FixManagerTest {

    @Mock
    private FixItemService mockFixItemService;
    @Mock
    private FixRecordService mockFixRecordService;
    @Mock
    private ItemService mockItemService;
    @Mock
    private MappingService mockMappingService;
    @Mock
    private ItemMappingService mockItemMappingService;
    @Mock
    private AbnormalDataService mockAbnormalDataService;
    @Mock
    private ItemFeignClient mockItemFeignClient;
    @Mock
    private ProducerFeignClient mockProducerFeignClient;
    @Mock
    private TradeClientService mockTradeClientService;
    @Mock
    private PackageService mockPackageService;

    @InjectMocks
    private FixManager fixManagerUnderTest;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(fixManagerUnderTest, "executorService", MoreExecutors.newDirectExecutorService());
        ReflectionTestUtils.setField(fixManagerUnderTest, "erpHost", "erpHost");
    }

    @Test
    public void testFix() {
        // Setup
        final FixItemBiz biz = new FixItemBiz();
        biz.setCommitFlag(false);
        biz.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        record.setCreateUserGuid("createUserGuid");
        record.setCreateUserName("createUserName");
        record.setModifiedUserGuid("modifiedUserGuid");
        record.setModifiedUserName("modifiedUserName");
        biz.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        biz.setItemList(Arrays.asList(fixItemDO));

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS1);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.fix(biz);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO1 = new FixItemDO();
        fixItemDO1.setRecordId(0L);
        fixItemDO1.setStoreGuid("storeGuid");
        fixItemDO1.setStoreName("storeName");
        fixItemDO1.setOrderSubType(0);
        fixItemDO1.setTakeoutItemName("itemName");
        fixItemDO1.setThirdSkuId("takeoutItemNumber");
        fixItemDO1.setErpItemGuid("erpItemGuid");
        fixItemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO1.setErpItemName("erpItemName");
        fixItemDO1.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO1.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO1.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO1);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO2);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO3);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testFix_AbnormalDataServiceListFixItemReturnsNoItems() {
        // Setup
        final FixItemBiz biz = new FixItemBiz();
        biz.setCommitFlag(false);
        biz.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        record.setCreateUserGuid("createUserGuid");
        record.setCreateUserName("createUserName");
        record.setModifiedUserGuid("modifiedUserGuid");
        record.setModifiedUserName("modifiedUserName");
        biz.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        biz.setItemList(Arrays.asList(fixItemDO));

        // Configure AbnormalDataService.listFixItem(...).
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(Collections.emptyList());

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.fix(biz);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO1 = new FixItemDO();
        fixItemDO1.setRecordId(0L);
        fixItemDO1.setStoreGuid("storeGuid");
        fixItemDO1.setStoreName("storeName");
        fixItemDO1.setOrderSubType(0);
        fixItemDO1.setTakeoutItemName("itemName");
        fixItemDO1.setThirdSkuId("takeoutItemNumber");
        fixItemDO1.setErpItemGuid("erpItemGuid");
        fixItemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO1.setErpItemName("erpItemName");
        fixItemDO1.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO1.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO1.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO1);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testFix_ItemServiceFilterNoRequiredFixListReturnsNoItems() {
        // Setup
        final FixItemBiz biz = new FixItemBiz();
        biz.setCommitFlag(false);
        biz.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        record.setCreateUserGuid("createUserGuid");
        record.setCreateUserName("createUserName");
        record.setModifiedUserGuid("modifiedUserGuid");
        record.setModifiedUserName("modifiedUserName");
        biz.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        biz.setItemList(Arrays.asList(fixItemDO));

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS);

        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.fix(biz);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO1 = new FixItemDO();
        fixItemDO1.setRecordId(0L);
        fixItemDO1.setStoreGuid("storeGuid");
        fixItemDO1.setStoreName("storeName");
        fixItemDO1.setOrderSubType(0);
        fixItemDO1.setTakeoutItemName("itemName");
        fixItemDO1.setThirdSkuId("takeoutItemNumber");
        fixItemDO1.setErpItemGuid("erpItemGuid");
        fixItemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO1.setErpItemName("erpItemName");
        fixItemDO1.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO1.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO1.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO1);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO1);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO2);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testFix_TradeClientServiceReturnsNoItems() {
        // Setup
        final FixItemBiz biz = new FixItemBiz();
        biz.setCommitFlag(false);
        biz.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        record.setCreateUserGuid("createUserGuid");
        record.setCreateUserName("createUserName");
        record.setModifiedUserGuid("modifiedUserGuid");
        record.setModifiedUserName("modifiedUserName");
        biz.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        biz.setItemList(Arrays.asList(fixItemDO));

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS1);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Collections.emptyList());

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.fix(biz);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO1 = new FixItemDO();
        fixItemDO1.setRecordId(0L);
        fixItemDO1.setStoreGuid("storeGuid");
        fixItemDO1.setStoreName("storeName");
        fixItemDO1.setOrderSubType(0);
        fixItemDO1.setTakeoutItemName("itemName");
        fixItemDO1.setThirdSkuId("takeoutItemNumber");
        fixItemDO1.setErpItemGuid("erpItemGuid");
        fixItemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO1.setErpItemName("erpItemName");
        fixItemDO1.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO1.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO1.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO1);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO2);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO3);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testFix_ItemFeignClientListSkuInfoAndSubReturnsNoItems() {
        // Setup
        final FixItemBiz biz = new FixItemBiz();
        biz.setCommitFlag(false);
        biz.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        record.setCreateUserGuid("createUserGuid");
        record.setCreateUserName("createUserName");
        record.setModifiedUserGuid("modifiedUserGuid");
        record.setModifiedUserName("modifiedUserName");
        biz.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        biz.setItemList(Arrays.asList(fixItemDO));

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS1);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.fix(biz);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO1 = new FixItemDO();
        fixItemDO1.setRecordId(0L);
        fixItemDO1.setStoreGuid("storeGuid");
        fixItemDO1.setStoreName("storeName");
        fixItemDO1.setOrderSubType(0);
        fixItemDO1.setTakeoutItemName("itemName");
        fixItemDO1.setThirdSkuId("takeoutItemNumber");
        fixItemDO1.setErpItemGuid("erpItemGuid");
        fixItemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO1.setErpItemName("erpItemName");
        fixItemDO1.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO1.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO1.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO1);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO2);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO3);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testFix_ProducerFeignClientGetItemReturnsNoItems() {
        // Setup
        final FixItemBiz biz = new FixItemBiz();
        biz.setCommitFlag(false);
        biz.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        record.setCreateUserGuid("createUserGuid");
        record.setCreateUserName("createUserName");
        record.setModifiedUserGuid("modifiedUserGuid");
        record.setModifiedUserName("modifiedUserName");
        biz.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        biz.setItemList(Arrays.asList(fixItemDO));

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS1);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(Collections.emptyList());

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.fix(biz);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO1 = new FixItemDO();
        fixItemDO1.setRecordId(0L);
        fixItemDO1.setStoreGuid("storeGuid");
        fixItemDO1.setStoreName("storeName");
        fixItemDO1.setOrderSubType(0);
        fixItemDO1.setTakeoutItemName("itemName");
        fixItemDO1.setThirdSkuId("takeoutItemNumber");
        fixItemDO1.setErpItemGuid("erpItemGuid");
        fixItemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO1.setErpItemName("erpItemName");
        fixItemDO1.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO1.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO1.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO1);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO2);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO3);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testFix_ItemFeignClientMappingAllItemsReturnsNoItems() {
        // Setup
        final FixItemBiz biz = new FixItemBiz();
        biz.setCommitFlag(false);
        biz.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        record.setCreateUserGuid("createUserGuid");
        record.setCreateUserName("createUserName");
        record.setModifiedUserGuid("modifiedUserGuid");
        record.setModifiedUserName("modifiedUserName");
        biz.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        biz.setItemList(Arrays.asList(fixItemDO));

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS1);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.fix(biz);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO1 = new FixItemDO();
        fixItemDO1.setRecordId(0L);
        fixItemDO1.setStoreGuid("storeGuid");
        fixItemDO1.setStoreName("storeName");
        fixItemDO1.setOrderSubType(0);
        fixItemDO1.setTakeoutItemName("itemName");
        fixItemDO1.setThirdSkuId("takeoutItemNumber");
        fixItemDO1.setErpItemGuid("erpItemGuid");
        fixItemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO1.setErpItemName("erpItemName");
        fixItemDO1.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO1.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO1.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO1);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO2);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO3);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testFix_ItemMappingServiceReturnsNoItems() {
        // Setup
        final FixItemBiz biz = new FixItemBiz();
        biz.setCommitFlag(false);
        biz.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        record.setCreateUserGuid("createUserGuid");
        record.setCreateUserName("createUserName");
        record.setModifiedUserGuid("modifiedUserGuid");
        record.setModifiedUserName("modifiedUserName");
        biz.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        biz.setItemList(Arrays.asList(fixItemDO));

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS1);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.fix(biz);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO1 = new FixItemDO();
        fixItemDO1.setRecordId(0L);
        fixItemDO1.setStoreGuid("storeGuid");
        fixItemDO1.setStoreName("storeName");
        fixItemDO1.setOrderSubType(0);
        fixItemDO1.setTakeoutItemName("itemName");
        fixItemDO1.setThirdSkuId("takeoutItemNumber");
        fixItemDO1.setErpItemGuid("erpItemGuid");
        fixItemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO1.setErpItemName("erpItemName");
        fixItemDO1.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO1.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO1.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO1);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO2);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO3);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testFix_MappingServiceListSourceGuidsAndGuidsByGuidsReturnsNoItems() {
        // Setup
        final FixItemBiz biz = new FixItemBiz();
        biz.setCommitFlag(false);
        biz.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        record.setCreateUserGuid("createUserGuid");
        record.setCreateUserName("createUserName");
        record.setModifiedUserGuid("modifiedUserGuid");
        record.setModifiedUserName("modifiedUserName");
        biz.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        biz.setItemList(Arrays.asList(fixItemDO));

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS1);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        fixManagerUnderTest.fix(biz);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO1 = new FixItemDO();
        fixItemDO1.setRecordId(0L);
        fixItemDO1.setStoreGuid("storeGuid");
        fixItemDO1.setStoreName("storeName");
        fixItemDO1.setOrderSubType(0);
        fixItemDO1.setTakeoutItemName("itemName");
        fixItemDO1.setThirdSkuId("takeoutItemNumber");
        fixItemDO1.setErpItemGuid("erpItemGuid");
        fixItemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO1.setErpItemName("erpItemName");
        fixItemDO1.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO1.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO1.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO1);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO2);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO3);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testAutoFix() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemService.selectUnBindList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(itemDOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS1);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS2 = Arrays.asList(itemDO2);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS2);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setStoreGuid("storeGuid");
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO1)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO3);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setId(0L);
        itemDO4.setStoreGuid("storeGuid");
        itemDO4.setStoreName("storeName");
        itemDO4.setOrderGuid("orderGuid");
        itemDO4.setItemName("itemName");
        itemDO4.setItemCount(new BigDecimal("0.00"));
        itemDO4.setActualItemCount(new BigDecimal("0.00"));
        itemDO4.setThirdSkuId("takeoutItemNumber");
        itemDO4.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO4.setErpItemName("erpItemName");
        itemDO4.setErpItemPrice(new BigDecimal("0.00"));
        itemDO4.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO4.setOrderViewId("orderViewId");
        itemDO4.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO4.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO4);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testAutoFix_ItemServiceSelectUnBindListReturnsNoItems() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(Collections.emptyList());

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
    }

    @Test
    public void testAutoFix_ItemFeignClientListSkuInfoByRecipeModeReturnsNoItems() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemService.selectUnBindList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(itemDOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
    }

    @Test
    public void testAutoFix_AbnormalDataServiceListFixItemReturnsNoItems() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemService.selectUnBindList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(itemDOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        // Configure AbnormalDataService.listFixItem(...).
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(Collections.emptyList());

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testAutoFix_ItemServiceFilterNoRequiredFixListReturnsNoItems() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemService.selectUnBindList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(itemDOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS1);

        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setStoreGuid("storeGuid");
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO1)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO2);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO3);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testAutoFix_TradeClientServiceReturnsNoItems() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemService.selectUnBindList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(itemDOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS1);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS2 = Arrays.asList(itemDO2);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS2);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Collections.emptyList());

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setStoreGuid("storeGuid");
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO1)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO3);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setId(0L);
        itemDO4.setStoreGuid("storeGuid");
        itemDO4.setStoreName("storeName");
        itemDO4.setOrderGuid("orderGuid");
        itemDO4.setItemName("itemName");
        itemDO4.setItemCount(new BigDecimal("0.00"));
        itemDO4.setActualItemCount(new BigDecimal("0.00"));
        itemDO4.setThirdSkuId("takeoutItemNumber");
        itemDO4.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO4.setErpItemName("erpItemName");
        itemDO4.setErpItemPrice(new BigDecimal("0.00"));
        itemDO4.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO4.setOrderViewId("orderViewId");
        itemDO4.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO4.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO4);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testAutoFix_ItemFeignClientListSkuInfoAndSubReturnsNoItems() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemService.selectUnBindList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(itemDOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS1);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS2 = Arrays.asList(itemDO2);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS2);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setStoreGuid("storeGuid");
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO1)).thenReturn(Collections.emptyList());

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO3);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setId(0L);
        itemDO4.setStoreGuid("storeGuid");
        itemDO4.setStoreName("storeName");
        itemDO4.setOrderGuid("orderGuid");
        itemDO4.setItemName("itemName");
        itemDO4.setItemCount(new BigDecimal("0.00"));
        itemDO4.setActualItemCount(new BigDecimal("0.00"));
        itemDO4.setThirdSkuId("takeoutItemNumber");
        itemDO4.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO4.setErpItemName("erpItemName");
        itemDO4.setErpItemPrice(new BigDecimal("0.00"));
        itemDO4.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO4.setOrderViewId("orderViewId");
        itemDO4.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO4.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO4);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testAutoFix_ProducerFeignClientGetItemReturnsNoItems() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemService.selectUnBindList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(itemDOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS1);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS2 = Arrays.asList(itemDO2);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS2);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setStoreGuid("storeGuid");
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO1)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(Collections.emptyList());

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO3);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setId(0L);
        itemDO4.setStoreGuid("storeGuid");
        itemDO4.setStoreName("storeName");
        itemDO4.setOrderGuid("orderGuid");
        itemDO4.setItemName("itemName");
        itemDO4.setItemCount(new BigDecimal("0.00"));
        itemDO4.setActualItemCount(new BigDecimal("0.00"));
        itemDO4.setThirdSkuId("takeoutItemNumber");
        itemDO4.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO4.setErpItemName("erpItemName");
        itemDO4.setErpItemPrice(new BigDecimal("0.00"));
        itemDO4.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO4.setOrderViewId("orderViewId");
        itemDO4.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO4.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO4);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testAutoFix_ItemFeignClientMappingAllItemsReturnsNoItems() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemService.selectUnBindList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(itemDOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS1);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS2 = Arrays.asList(itemDO2);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS2);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setStoreGuid("storeGuid");
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO1)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO3);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setId(0L);
        itemDO4.setStoreGuid("storeGuid");
        itemDO4.setStoreName("storeName");
        itemDO4.setOrderGuid("orderGuid");
        itemDO4.setItemName("itemName");
        itemDO4.setItemCount(new BigDecimal("0.00"));
        itemDO4.setActualItemCount(new BigDecimal("0.00"));
        itemDO4.setThirdSkuId("takeoutItemNumber");
        itemDO4.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO4.setErpItemName("erpItemName");
        itemDO4.setErpItemPrice(new BigDecimal("0.00"));
        itemDO4.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO4.setOrderViewId("orderViewId");
        itemDO4.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO4.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO4);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testAutoFix_ItemMappingServiceReturnsNoItems() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemService.selectUnBindList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(itemDOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS1);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS2 = Arrays.asList(itemDO2);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS2);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setStoreGuid("storeGuid");
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO1)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure MappingService.listSourceGuidsAndGuidsByGuids(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("5c17e0cb-e147-4d49-82a6-9dc168b65458");
        skuMapDO.setSourceGuid("erpItemSkuGuid");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value"))).thenReturn(skuMapDOS);

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO3);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setId(0L);
        itemDO4.setStoreGuid("storeGuid");
        itemDO4.setStoreName("storeName");
        itemDO4.setOrderGuid("orderGuid");
        itemDO4.setItemName("itemName");
        itemDO4.setItemCount(new BigDecimal("0.00"));
        itemDO4.setActualItemCount(new BigDecimal("0.00"));
        itemDO4.setThirdSkuId("takeoutItemNumber");
        itemDO4.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO4.setErpItemName("erpItemName");
        itemDO4.setErpItemPrice(new BigDecimal("0.00"));
        itemDO4.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO4.setOrderViewId("orderViewId");
        itemDO4.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO4.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO4);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testAutoFix_MappingServiceListSourceGuidsAndGuidsByGuidsReturnsNoItems() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("mtSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemService.selectUnBindList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.selectUnBindList("storeGuid", "mtSkuId")).thenReturn(itemDOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        // Configure AbnormalDataService.listFixItem(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS1 = Arrays.asList(itemDO1);
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        when(mockAbnormalDataService.listFixItem(reqDTO)).thenReturn(itemDOS1);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setId(0L);
        itemDO2.setStoreGuid("storeGuid");
        itemDO2.setStoreName("storeName");
        itemDO2.setOrderGuid("orderGuid");
        itemDO2.setItemName("itemName");
        itemDO2.setItemCount(new BigDecimal("0.00"));
        itemDO2.setActualItemCount(new BigDecimal("0.00"));
        itemDO2.setThirdSkuId("takeoutItemNumber");
        itemDO2.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO2.setErpItemName("erpItemName");
        itemDO2.setErpItemPrice(new BigDecimal("0.00"));
        itemDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO2.setOrderViewId("orderViewId");
        itemDO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO2.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS2 = Arrays.asList(itemDO2);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS2);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setStoreGuid("storeGuid");
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO1)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemSkuId("mtSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setErpItemSkuId("erpItemSkuGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem);
        when(mockProducerFeignClient.getItem("storeGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setStoreGuid("storeGuid");
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setErpItemPrice(new BigDecimal("0.00"));
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setParentSkuGuid("parentSkuGuid");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemMappingDO.setMapperGuid("mapperGuid");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        when(mockMappingService.listSourceGuidsAndGuidsByGuids(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        fixManagerUnderTest.autoFix(unItemBindUnbindReq);

        // Verify the results
        // Confirm FixRecordService.saveOrUpdate(...).
        final FixRecordDO entity = new FixRecordDO();
        entity.setId(0L);
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFixCount(0);
        entity.setStatus(0);
        entity.setCreateUserGuid("createUserGuid");
        entity.setCreateUserName("createUserName");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setModifiedUserName("modifiedUserName");
        verify(mockFixRecordService).saveOrUpdate(entity);

        // Confirm FixItemService.saveOrUpdateBatch(...).
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setRecordId(0L);
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("takeoutItemNumber");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("actualErpItemSkuId");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setSkuGuid("skuGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<FixItemDO> entityList = Arrays.asList(fixItemDO);
        verify(mockFixItemService).saveOrUpdateBatch(entityList);

        // Confirm ItemService.fixBatchItem(...).
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setId(0L);
        itemDO3.setStoreGuid("storeGuid");
        itemDO3.setStoreName("storeName");
        itemDO3.setOrderGuid("orderGuid");
        itemDO3.setItemName("itemName");
        itemDO3.setItemCount(new BigDecimal("0.00"));
        itemDO3.setActualItemCount(new BigDecimal("0.00"));
        itemDO3.setThirdSkuId("takeoutItemNumber");
        itemDO3.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO3.setErpItemName("erpItemName");
        itemDO3.setErpItemPrice(new BigDecimal("0.00"));
        itemDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO3.setOrderViewId("orderViewId");
        itemDO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO3.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemList = Arrays.asList(itemDO3);
        verify(mockItemService).fixBatchItem(fixItemList);

        // Confirm PackageService.updateByTakeoutItem(...).
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setId(0L);
        itemDO4.setStoreGuid("storeGuid");
        itemDO4.setStoreName("storeName");
        itemDO4.setOrderGuid("orderGuid");
        itemDO4.setItemName("itemName");
        itemDO4.setItemCount(new BigDecimal("0.00"));
        itemDO4.setActualItemCount(new BigDecimal("0.00"));
        itemDO4.setThirdSkuId("takeoutItemNumber");
        itemDO4.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO4.setErpItemName("erpItemName");
        itemDO4.setErpItemPrice(new BigDecimal("0.00"));
        itemDO4.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO4.setOrderViewId("orderViewId");
        itemDO4.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO4.setErpItemGuid("erpItemGuid");
        final List<ItemDO> fixItemBuffer = Arrays.asList(itemDO4);
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setItemGuid("itemGuid");
        skuInfoPkgDTO2.setParentGuid("parentGuid");
        skuInfoPkgDTO2.setItemNum(new BigDecimal("0.00"));
        skuInfoPkgDTO2.setItemName("itemName");
        skuInfoPkgDTO2.setSkuGuid("skuGuid");
        final List<SkuInfoPkgDTO> skuInfoPkgList = Arrays.asList(skuInfoPkgDTO2);
        verify(mockPackageService).updateByTakeoutItem(fixItemBuffer, skuInfoPkgList);
        verify(mockAbnormalDataService).removeByTakeoutItemIds(Arrays.asList(0L));

        // Confirm MappingService.batchBindItem(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("mtSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        verify(mockMappingService).batchBindItem(unItemBatchUnbindReq);
    }

    @Test
    public void testModifyErpRepertory() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> unFixItemList = Arrays.asList(itemDO);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO1);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Run the test
        fixManagerUnderTest.modifyErpRepertory(unFixItemList, 0L);

        // Verify the results
    }

    @Test
    public void testModifyErpRepertory_ItemServiceReturnsNoItems() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> unFixItemList = Arrays.asList(itemDO);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Run the test
        fixManagerUnderTest.modifyErpRepertory(unFixItemList, 0L);

        // Verify the results
    }

    @Test
    public void testModifyErpRepertory_TradeClientServiceReturnsNoItems() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> unFixItemList = Arrays.asList(itemDO);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO1);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Collections.emptyList());

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setStoreGuid("storeGuid");
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setPickNum(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(skuTakeawayInfoRespDTOS);

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Run the test
        fixManagerUnderTest.modifyErpRepertory(unFixItemList, 0L);

        // Verify the results
    }

    @Test
    public void testModifyErpRepertory_ItemFeignClientReturnsNoItems() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemName("itemName");
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setThirdSkuId("takeoutItemNumber");
        itemDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO.setOrderViewId("orderViewId");
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setErpItemGuid("erpItemGuid");
        final List<ItemDO> unFixItemList = Arrays.asList(itemDO);

        // Configure ItemService.filterNoRequiredFixList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setStoreName("storeName");
        itemDO1.setOrderGuid("orderGuid");
        itemDO1.setItemName("itemName");
        itemDO1.setItemCount(new BigDecimal("0.00"));
        itemDO1.setActualItemCount(new BigDecimal("0.00"));
        itemDO1.setThirdSkuId("takeoutItemNumber");
        itemDO1.setErpItemSkuGuid("actualErpItemSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemDO1.setOrderViewId("orderViewId");
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setErpItemGuid("erpItemGuid");
        final List<ItemDO> itemDOS = Arrays.asList(itemDO1);
        when(mockItemService.filterNoRequiredFixList(Arrays.asList(0L))).thenReturn(itemDOS);

        // Configure TradeClientService.listByOrderGuids(...).
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));
        when(mockTradeClientService.listByOrderGuids(queryDTO)).thenReturn(Arrays.asList(0L));

        // Configure ItemFeignClient.listSkuInfoAndSub(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoAndSub(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Configure ProducerFeignClient.getBindStockStore(...).
        final StockStoreBindResqDTO stockStoreBindResqDTO = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");
        when(mockProducerFeignClient.getBindStockStore("storeGuid")).thenReturn(stockStoreBindResqDTO);

        // Run the test
        fixManagerUnderTest.modifyErpRepertory(unFixItemList, 0L);

        // Verify the results
    }
}
