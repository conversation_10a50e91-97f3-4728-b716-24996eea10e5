package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.entity.domain.ConfigDO;
import com.holder.saas.store.takeaway.consumers.mapstruct.ConfigMapstruct;
import com.holderzone.framework.base.dto.log.LogDTO;
import com.holderzone.framework.exception.ParamException;
import com.holderzone.saas.store.dto.takeaway.TakeoutAutoRcvDTO;
import com.holderzone.sdk.event.LogPublisher;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ConfigServiceImplTest {

    @Mock
    private LogPublisher mockLogPublisher;
    @Mock
    private ConfigMapstruct mockConfigMapstruct;

    private ConfigServiceImpl configServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        configServiceImplUnderTest = new ConfigServiceImpl(mockLogPublisher, mockConfigMapstruct);
    }

    @Test
    public void testSelectStoreConfig() {
        // Setup
        final TakeoutAutoRcvDTO takeoutAutoRcvDTO = new TakeoutAutoRcvDTO(false);
        final TakeoutAutoRcvDTO expectedResult = new TakeoutAutoRcvDTO(false);

        // Run the test
        final TakeoutAutoRcvDTO result = configServiceImplUnderTest.selectStoreAutoRcvConfig(takeoutAutoRcvDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSaveStoreConfig() throws Exception {
        // Setup
        final TakeoutAutoRcvDTO takeoutAutoRcvDTO = new TakeoutAutoRcvDTO(false);

        // Configure ConfigMapstruct.fromTakeoutAutoRcv(...).
        final ConfigDO configDO = new ConfigDO();
        configDO.setId(0L);
        configDO.setConfigGuid("configGuid");
        configDO.setStoreGuid("storeGuid");
        configDO.setAutoOrder(false);
        configDO.setStaffGuid("staffGuid");
        configDO.setStaffName("staffName");
        when(mockConfigMapstruct.fromTakeoutAutoRcv(new TakeoutAutoRcvDTO(false))).thenReturn(configDO);

        // Run the test
        final Boolean result = configServiceImplUnderTest.saveStoreAutoRcvConfig(takeoutAutoRcvDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockLogPublisher).send(any(Object.class), any(LogDTO.class));
    }

    @Test
    public void testSaveStoreConfig_LogPublisherThrowsParamException() throws Exception {
        // Setup
        final TakeoutAutoRcvDTO takeoutAutoRcvDTO = new TakeoutAutoRcvDTO(false);

        // Configure ConfigMapstruct.fromTakeoutAutoRcv(...).
        final ConfigDO configDO = new ConfigDO();
        configDO.setId(0L);
        configDO.setConfigGuid("configGuid");
        configDO.setStoreGuid("storeGuid");
        configDO.setAutoOrder(false);
        configDO.setStaffGuid("staffGuid");
        configDO.setStaffName("staffName");
        when(mockConfigMapstruct.fromTakeoutAutoRcv(new TakeoutAutoRcvDTO(false))).thenReturn(configDO);

        doThrow(ParamException.class).when(mockLogPublisher).send(any(Object.class), any(LogDTO.class));

        // Run the test
        final Boolean result = configServiceImplUnderTest.saveStoreAutoRcvConfig(takeoutAutoRcvDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testSelectByStoreGuid() {
        // Setup
        final ConfigDO expectedResult = new ConfigDO();
        expectedResult.setId(0L);
        expectedResult.setConfigGuid("configGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setAutoOrder(false);
        expectedResult.setStaffGuid("staffGuid");
        expectedResult.setStaffName("staffName");

        // Run the test
        final ConfigDO result = configServiceImplUnderTest.selectByStoreGuid("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
