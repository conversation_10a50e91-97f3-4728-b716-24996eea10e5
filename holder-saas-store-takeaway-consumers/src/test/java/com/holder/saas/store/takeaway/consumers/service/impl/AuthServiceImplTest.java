package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holder.saas.store.takeaway.consumers.service.rpc.OrgFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.StaffFeignClient;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByTypeReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreUsedReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.user.UserSpinnerDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AuthServiceImplTest {

    @Mock
    private OrderService mockOrderService;
    @Mock
    private StaffFeignClient mockStaffFeignClient;
    @Mock
    private OrgFeignClient mockOrgFeignClient;
    @Mock
    private ProducerFeignClient mockProducerFeignClient;

    private AuthServiceImpl authServiceImplUnderTest;

    @Before
    public void setUp() {
        authServiceImplUnderTest = new AuthServiceImpl(mockOrderService, mockStaffFeignClient, mockOrgFeignClient,
                mockProducerFeignClient);
    }

    @Test
    public void testQueryAuthByType() {
        // Setup
        final StoreAuthByTypeReqDTO storeAuthByTypeReqDTO = new StoreAuthByTypeReqDTO();
        storeAuthByTypeReqDTO.setUserGuid("userGuid");
        storeAuthByTypeReqDTO.setTakeoutType(0);
        storeAuthByTypeReqDTO.setBindingStatus(0);
        storeAuthByTypeReqDTO.setQueryString("queryString");

        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> expectedResult = Arrays.asList(storeAuthDTO);

        // Configure StaffFeignClient.findStoreManagedByUser(...).
        final UserSpinnerDTO userSpinnerDTO = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        userSpinnerDTO.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        when(mockStaffFeignClient.findStoreManagedByUser()).thenReturn(userSpinnerDTO);

        // Configure ProducerFeignClient.listEleAuth(...).
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("美团团购");
        storeAuthDTO1.setStoreNumber("code");
        storeAuthDTO1.setStoreGuid("guid");
        storeAuthDTO1.setStoreName("name");
        storeAuthDTO1.setBindingStatus(0);
        final List<StoreAuthDTO> list = Arrays.asList(storeAuthDTO1);
        final StoreAuthDTO storeAuthDTO2 = new StoreAuthDTO();
        storeAuthDTO2.setTakeoutType(0);
        storeAuthDTO2.setPlatformName("美团团购");
        storeAuthDTO2.setStoreNumber("code");
        storeAuthDTO2.setStoreGuid("guid");
        storeAuthDTO2.setStoreName("name");
        storeAuthDTO2.setBindingStatus(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList = Arrays.asList(storeAuthDTO2);
        when(mockProducerFeignClient.listEleAuth(storeAuthorizationDTOList)).thenReturn(list);

        // Configure ProducerFeignClient.listMtAuth(...).
        final StoreAuthDTO storeAuthDTO3 = new StoreAuthDTO();
        storeAuthDTO3.setTakeoutType(0);
        storeAuthDTO3.setPlatformName("美团团购");
        storeAuthDTO3.setStoreNumber("code");
        storeAuthDTO3.setStoreGuid("guid");
        storeAuthDTO3.setStoreName("name");
        storeAuthDTO3.setBindingStatus(0);
        final List<StoreAuthDTO> list1 = Arrays.asList(storeAuthDTO3);
        final StoreAuthDTO storeAuthDTO4 = new StoreAuthDTO();
        storeAuthDTO4.setTakeoutType(0);
        storeAuthDTO4.setPlatformName("美团团购");
        storeAuthDTO4.setStoreNumber("code");
        storeAuthDTO4.setStoreGuid("guid");
        storeAuthDTO4.setStoreName("name");
        storeAuthDTO4.setBindingStatus(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList1 = Arrays.asList(storeAuthDTO4);
        when(mockProducerFeignClient.listMtAuth(storeAuthorizationDTOList1)).thenReturn(list1);

        // Run the test
        final List<StoreAuthDTO> result = authServiceImplUnderTest.queryAuthByType(storeAuthByTypeReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAuthByType_ProducerFeignClientListEleAuthReturnsNoItems() {
        // Setup
        final StoreAuthByTypeReqDTO storeAuthByTypeReqDTO = new StoreAuthByTypeReqDTO();
        storeAuthByTypeReqDTO.setUserGuid("userGuid");
        storeAuthByTypeReqDTO.setTakeoutType(0);
        storeAuthByTypeReqDTO.setBindingStatus(0);
        storeAuthByTypeReqDTO.setQueryString("queryString");

        // Configure StaffFeignClient.findStoreManagedByUser(...).
        final UserSpinnerDTO userSpinnerDTO = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        userSpinnerDTO.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        when(mockStaffFeignClient.findStoreManagedByUser()).thenReturn(userSpinnerDTO);

        // Configure ProducerFeignClient.listEleAuth(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList = Arrays.asList(storeAuthDTO);
        when(mockProducerFeignClient.listEleAuth(storeAuthorizationDTOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreAuthDTO> result = authServiceImplUnderTest.queryAuthByType(storeAuthByTypeReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAuthByType_ProducerFeignClientListMtAuthReturnsNoItems() {
        // Setup
        final StoreAuthByTypeReqDTO storeAuthByTypeReqDTO = new StoreAuthByTypeReqDTO();
        storeAuthByTypeReqDTO.setUserGuid("userGuid");
        storeAuthByTypeReqDTO.setTakeoutType(0);
        storeAuthByTypeReqDTO.setBindingStatus(0);
        storeAuthByTypeReqDTO.setQueryString("queryString");

        // Configure StaffFeignClient.findStoreManagedByUser(...).
        final UserSpinnerDTO userSpinnerDTO = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        userSpinnerDTO.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        when(mockStaffFeignClient.findStoreManagedByUser()).thenReturn(userSpinnerDTO);

        // Configure ProducerFeignClient.listMtAuth(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList = Arrays.asList(storeAuthDTO);
        when(mockProducerFeignClient.listMtAuth(storeAuthorizationDTOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreAuthDTO> result = authServiceImplUnderTest.queryAuthByType(storeAuthByTypeReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryTakeoutAuthByStore() {
        // Setup
        final StoreAuthByStoreReqDTO storeAuthReqDTO = new StoreAuthByStoreReqDTO();
        storeAuthReqDTO.setStoreGuid("guid");
        storeAuthReqDTO.setPoiId("poiId");
        storeAuthReqDTO.setBusinessId((byte) 0b0);

        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> expectedResult = Arrays.asList(storeAuthDTO);

        // Configure ProducerFeignClient.getMtTakeoutAuth(...).
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("美团团购");
        storeAuthDTO1.setStoreNumber("code");
        storeAuthDTO1.setStoreGuid("guid");
        storeAuthDTO1.setStoreName("name");
        storeAuthDTO1.setBindingStatus(0);
        final StoreAuthDTO storeAuthDTO2 = new StoreAuthDTO();
        storeAuthDTO2.setTakeoutType(0);
        storeAuthDTO2.setPlatformName("美团团购");
        storeAuthDTO2.setStoreNumber("code");
        storeAuthDTO2.setStoreGuid("guid");
        storeAuthDTO2.setStoreName("name");
        storeAuthDTO2.setBindingStatus(0);
        when(mockProducerFeignClient.getMtTakeoutAuth(storeAuthDTO2)).thenReturn(storeAuthDTO1);

        // Configure ProducerFeignClient.getEleTakeoutAuth(...).
        final StoreAuthDTO storeAuthDTO3 = new StoreAuthDTO();
        storeAuthDTO3.setTakeoutType(0);
        storeAuthDTO3.setPlatformName("美团团购");
        storeAuthDTO3.setStoreNumber("code");
        storeAuthDTO3.setStoreGuid("guid");
        storeAuthDTO3.setStoreName("name");
        storeAuthDTO3.setBindingStatus(0);
        final StoreAuthDTO storeAuthDTO4 = new StoreAuthDTO();
        storeAuthDTO4.setTakeoutType(0);
        storeAuthDTO4.setPlatformName("美团团购");
        storeAuthDTO4.setStoreNumber("code");
        storeAuthDTO4.setStoreGuid("guid");
        storeAuthDTO4.setStoreName("name");
        storeAuthDTO4.setBindingStatus(0);
        when(mockProducerFeignClient.getEleTakeoutAuth(storeAuthDTO4)).thenReturn(storeAuthDTO3);

        // Configure ProducerFeignClient.getOwnTakeoutAuth(...).
        final StoreAuthDTO storeAuthDTO5 = new StoreAuthDTO();
        storeAuthDTO5.setTakeoutType(0);
        storeAuthDTO5.setPlatformName("美团团购");
        storeAuthDTO5.setStoreNumber("code");
        storeAuthDTO5.setStoreGuid("guid");
        storeAuthDTO5.setStoreName("name");
        storeAuthDTO5.setBindingStatus(0);
        final StoreAuthDTO storeAuthDTO6 = new StoreAuthDTO();
        storeAuthDTO6.setTakeoutType(0);
        storeAuthDTO6.setPlatformName("美团团购");
        storeAuthDTO6.setStoreNumber("code");
        storeAuthDTO6.setStoreGuid("guid");
        storeAuthDTO6.setStoreName("name");
        storeAuthDTO6.setBindingStatus(0);
        when(mockProducerFeignClient.getOwnTakeoutAuth(storeAuthDTO6)).thenReturn(storeAuthDTO5);

        // Configure ProducerFeignClient.getTcdTakeoutAuth(...).
        final StoreAuthDTO storeAuthDTO7 = new StoreAuthDTO();
        storeAuthDTO7.setTakeoutType(0);
        storeAuthDTO7.setPlatformName("美团团购");
        storeAuthDTO7.setStoreNumber("code");
        storeAuthDTO7.setStoreGuid("guid");
        storeAuthDTO7.setStoreName("name");
        storeAuthDTO7.setBindingStatus(0);
        final StoreAuthDTO storeAuthDTO8 = new StoreAuthDTO();
        storeAuthDTO8.setTakeoutType(0);
        storeAuthDTO8.setPlatformName("美团团购");
        storeAuthDTO8.setStoreNumber("code");
        storeAuthDTO8.setStoreGuid("guid");
        storeAuthDTO8.setStoreName("name");
        storeAuthDTO8.setBindingStatus(0);
        when(mockProducerFeignClient.getTcdTakeoutAuth(storeAuthDTO8)).thenReturn(storeAuthDTO7);

        // Run the test
        final List<StoreAuthDTO> result = authServiceImplUnderTest.queryTakeoutAuthByStore(storeAuthReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTuanGouByStore() {
        // Setup
        final StoreAuthByStoreReqDTO storeAuthReqDTO = new StoreAuthByStoreReqDTO();
        storeAuthReqDTO.setStoreGuid("guid");
        storeAuthReqDTO.setPoiId("poiId");
        storeAuthReqDTO.setBusinessId((byte) 0b0);

        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> expectedResult = Arrays.asList(storeAuthDTO);

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrgFeignClient.queryStoreByGuid("guid")).thenReturn(storeDTO);

        // Configure ProducerFeignClient.getMtTuanGouAuth(...).
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("美团团购");
        storeAuthDTO1.setStoreNumber("code");
        storeAuthDTO1.setStoreGuid("guid");
        storeAuthDTO1.setStoreName("name");
        storeAuthDTO1.setBindingStatus(0);
        final StoreAuthDTO storeAuthDTO2 = new StoreAuthDTO();
        storeAuthDTO2.setTakeoutType(0);
        storeAuthDTO2.setPlatformName("美团团购");
        storeAuthDTO2.setStoreNumber("code");
        storeAuthDTO2.setStoreGuid("guid");
        storeAuthDTO2.setStoreName("name");
        storeAuthDTO2.setBindingStatus(0);
        when(mockProducerFeignClient.getMtTuanGouAuth(storeAuthDTO2)).thenReturn(storeAuthDTO1);

        // Run the test
        final List<StoreAuthDTO> result = authServiceImplUnderTest.queryTuanGouByStore(storeAuthReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreUsed() {
        // Setup
        final StoreUsedReqDTO storeUsedReqDTO = new StoreUsedReqDTO();
        storeUsedReqDTO.setUserGuid("userGuid");

        // Configure StaffFeignClient.findStoreManagedByUser(...).
        final UserSpinnerDTO userSpinnerDTO = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        userSpinnerDTO.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        when(mockStaffFeignClient.findStoreManagedByUser()).thenReturn(userSpinnerDTO);

        // Configure ProducerFeignClient.listMtAuth(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> list = Arrays.asList(storeAuthDTO);
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("美团团购");
        storeAuthDTO1.setStoreNumber("code");
        storeAuthDTO1.setStoreGuid("guid");
        storeAuthDTO1.setStoreName("name");
        storeAuthDTO1.setBindingStatus(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList = Arrays.asList(storeAuthDTO1);
        when(mockProducerFeignClient.listMtAuth(storeAuthorizationDTOList)).thenReturn(list);

        // Configure ProducerFeignClient.listEleAuth(...).
        final StoreAuthDTO storeAuthDTO2 = new StoreAuthDTO();
        storeAuthDTO2.setTakeoutType(0);
        storeAuthDTO2.setPlatformName("美团团购");
        storeAuthDTO2.setStoreNumber("code");
        storeAuthDTO2.setStoreGuid("guid");
        storeAuthDTO2.setStoreName("name");
        storeAuthDTO2.setBindingStatus(0);
        final List<StoreAuthDTO> list1 = Arrays.asList(storeAuthDTO2);
        final StoreAuthDTO storeAuthDTO3 = new StoreAuthDTO();
        storeAuthDTO3.setTakeoutType(0);
        storeAuthDTO3.setPlatformName("美团团购");
        storeAuthDTO3.setStoreNumber("code");
        storeAuthDTO3.setStoreGuid("guid");
        storeAuthDTO3.setStoreName("name");
        storeAuthDTO3.setBindingStatus(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList1 = Arrays.asList(storeAuthDTO3);
        when(mockProducerFeignClient.listEleAuth(storeAuthorizationDTOList1)).thenReturn(list1);

        when(mockOrderService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final List<StoreDTO> result = authServiceImplUnderTest.queryStoreUsed(storeUsedReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreUsed_ProducerFeignClientListMtAuthReturnsNoItems() {
        // Setup
        final StoreUsedReqDTO storeUsedReqDTO = new StoreUsedReqDTO();
        storeUsedReqDTO.setUserGuid("userGuid");

        // Configure StaffFeignClient.findStoreManagedByUser(...).
        final UserSpinnerDTO userSpinnerDTO = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        userSpinnerDTO.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        when(mockStaffFeignClient.findStoreManagedByUser()).thenReturn(userSpinnerDTO);

        // Configure ProducerFeignClient.listMtAuth(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList = Arrays.asList(storeAuthDTO);
        when(mockProducerFeignClient.listMtAuth(storeAuthorizationDTOList)).thenReturn(Collections.emptyList());

        // Configure ProducerFeignClient.listEleAuth(...).
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("美团团购");
        storeAuthDTO1.setStoreNumber("code");
        storeAuthDTO1.setStoreGuid("guid");
        storeAuthDTO1.setStoreName("name");
        storeAuthDTO1.setBindingStatus(0);
        final List<StoreAuthDTO> list = Arrays.asList(storeAuthDTO1);
        final StoreAuthDTO storeAuthDTO2 = new StoreAuthDTO();
        storeAuthDTO2.setTakeoutType(0);
        storeAuthDTO2.setPlatformName("美团团购");
        storeAuthDTO2.setStoreNumber("code");
        storeAuthDTO2.setStoreGuid("guid");
        storeAuthDTO2.setStoreName("name");
        storeAuthDTO2.setBindingStatus(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList1 = Arrays.asList(storeAuthDTO2);
        when(mockProducerFeignClient.listEleAuth(storeAuthorizationDTOList1)).thenReturn(list);

        when(mockOrderService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final List<StoreDTO> result = authServiceImplUnderTest.queryStoreUsed(storeUsedReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreUsed_ProducerFeignClientListEleAuthReturnsNoItems() {
        // Setup
        final StoreUsedReqDTO storeUsedReqDTO = new StoreUsedReqDTO();
        storeUsedReqDTO.setUserGuid("userGuid");

        // Configure StaffFeignClient.findStoreManagedByUser(...).
        final UserSpinnerDTO userSpinnerDTO = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        userSpinnerDTO.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        when(mockStaffFeignClient.findStoreManagedByUser()).thenReturn(userSpinnerDTO);

        // Configure ProducerFeignClient.listMtAuth(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> list = Arrays.asList(storeAuthDTO);
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("美团团购");
        storeAuthDTO1.setStoreNumber("code");
        storeAuthDTO1.setStoreGuid("guid");
        storeAuthDTO1.setStoreName("name");
        storeAuthDTO1.setBindingStatus(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList = Arrays.asList(storeAuthDTO1);
        when(mockProducerFeignClient.listMtAuth(storeAuthorizationDTOList)).thenReturn(list);

        // Configure ProducerFeignClient.listEleAuth(...).
        final StoreAuthDTO storeAuthDTO2 = new StoreAuthDTO();
        storeAuthDTO2.setTakeoutType(0);
        storeAuthDTO2.setPlatformName("美团团购");
        storeAuthDTO2.setStoreNumber("code");
        storeAuthDTO2.setStoreGuid("guid");
        storeAuthDTO2.setStoreName("name");
        storeAuthDTO2.setBindingStatus(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList1 = Arrays.asList(storeAuthDTO2);
        when(mockProducerFeignClient.listEleAuth(storeAuthorizationDTOList1)).thenReturn(Collections.emptyList());

        when(mockOrderService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final List<StoreDTO> result = authServiceImplUnderTest.queryStoreUsed(storeUsedReqDTO);

        // Verify the results
    }

    @Test
    public void testUpdateDeliveryType() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);

        // Configure ProducerFeignClient.eleUpdateDelivery(...).
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("美团团购");
        storeAuthDTO1.setStoreNumber("code");
        storeAuthDTO1.setStoreGuid("guid");
        storeAuthDTO1.setStoreName("name");
        storeAuthDTO1.setBindingStatus(0);
        when(mockProducerFeignClient.eleUpdateDelivery(storeAuthDTO1)).thenReturn(false);

        // Configure ProducerFeignClient.mtUpdateDelivery(...).
        final StoreAuthDTO storeAuthDTO2 = new StoreAuthDTO();
        storeAuthDTO2.setTakeoutType(0);
        storeAuthDTO2.setPlatformName("美团团购");
        storeAuthDTO2.setStoreNumber("code");
        storeAuthDTO2.setStoreGuid("guid");
        storeAuthDTO2.setStoreName("name");
        storeAuthDTO2.setBindingStatus(0);
        when(mockProducerFeignClient.mtUpdateDelivery(storeAuthDTO2)).thenReturn(false);

        // Configure ProducerFeignClient.tcdUpdateDelivery(...).
        final StoreAuthDTO storeAuthDTO3 = new StoreAuthDTO();
        storeAuthDTO3.setTakeoutType(0);
        storeAuthDTO3.setPlatformName("美团团购");
        storeAuthDTO3.setStoreNumber("code");
        storeAuthDTO3.setStoreGuid("guid");
        storeAuthDTO3.setStoreName("name");
        storeAuthDTO3.setBindingStatus(0);
        when(mockProducerFeignClient.tcdUpdateDelivery(storeAuthDTO3)).thenReturn(false);

        // Run the test
        final Boolean result = authServiceImplUnderTest.updateDeliveryType(storeAuthDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateDeliveryType_ProducerFeignClientEleUpdateDeliveryReturnsTrue() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);

        // Configure ProducerFeignClient.eleUpdateDelivery(...).
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("美团团购");
        storeAuthDTO1.setStoreNumber("code");
        storeAuthDTO1.setStoreGuid("guid");
        storeAuthDTO1.setStoreName("name");
        storeAuthDTO1.setBindingStatus(0);
        when(mockProducerFeignClient.eleUpdateDelivery(storeAuthDTO1)).thenReturn(true);

        // Run the test
        final Boolean result = authServiceImplUnderTest.updateDeliveryType(storeAuthDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testUpdateDeliveryType_ProducerFeignClientMtUpdateDeliveryReturnsTrue() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);

        // Configure ProducerFeignClient.mtUpdateDelivery(...).
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("美团团购");
        storeAuthDTO1.setStoreNumber("code");
        storeAuthDTO1.setStoreGuid("guid");
        storeAuthDTO1.setStoreName("name");
        storeAuthDTO1.setBindingStatus(0);
        when(mockProducerFeignClient.mtUpdateDelivery(storeAuthDTO1)).thenReturn(true);

        // Run the test
        final Boolean result = authServiceImplUnderTest.updateDeliveryType(storeAuthDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testUpdateDeliveryType_ProducerFeignClientTcdUpdateDeliveryReturnsTrue() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("美团团购");
        storeAuthDTO.setStoreNumber("code");
        storeAuthDTO.setStoreGuid("guid");
        storeAuthDTO.setStoreName("name");
        storeAuthDTO.setBindingStatus(0);

        // Configure ProducerFeignClient.tcdUpdateDelivery(...).
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("美团团购");
        storeAuthDTO1.setStoreNumber("code");
        storeAuthDTO1.setStoreGuid("guid");
        storeAuthDTO1.setStoreName("name");
        storeAuthDTO1.setBindingStatus(0);
        when(mockProducerFeignClient.tcdUpdateDelivery(storeAuthDTO1)).thenReturn(true);

        // Run the test
        final Boolean result = authServiceImplUnderTest.updateDeliveryType(storeAuthDTO);

        // Verify the results
        assertThat(result).isTrue();
    }
}
