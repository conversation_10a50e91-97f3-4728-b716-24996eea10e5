package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holderzone.saas.store.dto.order.common.PushOrderBillsBO;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class MdmErpListenerTest {

    @Mock
    private DynamicHelper mockDynamicHelper;

    private MdmErpListener mdmErpListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        mdmErpListenerUnderTest = new MdmErpListener(mockDynamicHelper);
        ReflectionTestUtils.setField(mdmErpListenerUnderTest, "mdmRequestHost", "mdmRequestHost");
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final PushOrderBillsBO pushOrderBillsBO = new PushOrderBillsBO();
        pushOrderBillsBO.setSalesOrderId("salesOrderId");
        pushOrderBillsBO.setSerialNumber("serialNumber");
        pushOrderBillsBO.setStoreId("storeId");
        pushOrderBillsBO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pushOrderBillsBO.setConsumeTotal(new BigDecimal("0.00"));

        final MessageExt messageExt = new MessageExt();
        messageExt.setQueueId(0);
        messageExt.setBornTimestamp(0L);
        messageExt.setBornHost(new InetSocketAddress("localhost", 80));
        messageExt.setStoreTimestamp(0L);
        messageExt.setStoreHost(new InetSocketAddress("localhost", 80));

        // Run the test
        final boolean result = mdmErpListenerUnderTest.consumeMsg(pushOrderBillsBO, messageExt);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockDynamicHelper).removeThreadLocalDatabaseInfo();
    }
}
