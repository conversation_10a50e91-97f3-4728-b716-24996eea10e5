package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.manage.OrderConsumeManage;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.InetSocketAddress;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ConsumerUnOrderListenerTest {

    @Mock
    private OrderConsumeManage mockOrderConsumeManage;

    private ConsumerUnOrderListener consumerUnOrderListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        consumerUnOrderListenerUnderTest = new ConsumerUnOrderListener(mockOrderConsumeManage);
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setSubStoreId(0L);
        unOrder.setShopName("shopName");
        unOrder.setCbMsgType(0);

        final MessageExt messageExt = new MessageExt();
        messageExt.setQueueId(0);
        messageExt.setBornTimestamp(0L);
        messageExt.setBornHost(new InetSocketAddress("localhost", 80));
        messageExt.setStoreTimestamp(0L);
        messageExt.setStoreHost(new InetSocketAddress("localhost", 80));

        // Configure OrderConsumeManage.process(...).
        final UnOrder unOrder1 = new UnOrder();
        unOrder1.setOrderStatus(0);
        unOrder1.setShopId(0L);
        unOrder1.setSubStoreId(0L);
        unOrder1.setShopName("shopName");
        unOrder1.setCbMsgType(0);
        when(mockOrderConsumeManage.process(unOrder1)).thenReturn(false);

        // Run the test
        final boolean result = consumerUnOrderListenerUnderTest.consumeMsg(unOrder, messageExt);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testConsumeMsg_OrderConsumeManageReturnsTrue() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setSubStoreId(0L);
        unOrder.setShopName("shopName");
        unOrder.setCbMsgType(0);

        final MessageExt messageExt = new MessageExt();
        messageExt.setQueueId(0);
        messageExt.setBornTimestamp(0L);
        messageExt.setBornHost(new InetSocketAddress("localhost", 80));
        messageExt.setStoreTimestamp(0L);
        messageExt.setStoreHost(new InetSocketAddress("localhost", 80));

        // Configure OrderConsumeManage.process(...).
        final UnOrder unOrder1 = new UnOrder();
        unOrder1.setOrderStatus(0);
        unOrder1.setShopId(0L);
        unOrder1.setSubStoreId(0L);
        unOrder1.setShopName("shopName");
        unOrder1.setCbMsgType(0);
        when(mockOrderConsumeManage.process(unOrder1)).thenReturn(true);

        // Run the test
        final boolean result = consumerUnOrderListenerUnderTest.consumeMsg(unOrder, messageExt);

        // Verify the results
        assertThat(result).isTrue();
    }
}
