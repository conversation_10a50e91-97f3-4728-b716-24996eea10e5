package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemBindExtendInfoDo;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemMappingDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.SkuMapDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.manage.FixManager;
import com.holder.saas.store.takeaway.consumers.mapper.SkuMapMapper;
import com.holder.saas.store.takeaway.consumers.service.ItemBindExtendInfoService;
import com.holder.saas.store.takeaway.consumers.service.ItemMappingService;
import com.holder.saas.store.takeaway.consumers.service.rpc.ItemFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.KdsFeignClient;
import com.holder.saas.store.takeaway.consumers.service.rpc.OrganizationService;
import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdPointItemDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemMappingReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemMappingRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MappingServiceImplTest {

    @Mock
    private ProducerFeignClient mockProducerFeignClient;
    @Mock
    private ItemFeignClient mockItemFeignClient;
    @Mock
    private KdsFeignClient mockKdsFeignClient;
    @Mock
    private ItemBindExtendInfoService mockItemBindExtendInfoService;
    @Mock
    private SkuMapMapper mockSkuMapMapper;
    @Mock
    private ItemMappingService mockItemMappingService;
    @Mock
    private OrganizationService mockOrganizationService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private FixManager mockFixManager;

    private MappingServiceImpl mappingServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        mappingServiceImplUnderTest = new MappingServiceImpl(mockProducerFeignClient, mockItemFeignClient,
                MoreExecutors.newDirectExecutorService(), mockKdsFeignClient, mockItemBindExtendInfoService,
                mockSkuMapMapper, mockItemMappingService, mockOrganizationService, mockDynamicHelper);
        ReflectionTestUtils.setField(mappingServiceImplUnderTest, "fixManage", mockFixManager);
        ReflectionTestUtils.setField(mappingServiceImplUnderTest, "storeProductQueryThreadPool",
                MoreExecutors.newDirectExecutorService());
        ReflectionTestUtils.setField(mappingServiceImplUnderTest, "batchBindProductThreadPool",
                MoreExecutors.newDirectExecutorService());
    }

    @Test
    public void testGetOwnItemBinding() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        when(mockProducerFeignClient.getItem("brandGuid", 2)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setIsRack(0);
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishSkuUnit("eDishSkuUnit");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setDishNameWithSpec("dishNameWithSpec");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        mappingRespDTO.setParentSkuGuid("eDishSkuCode");
        mappingRespDTO.setIsChoice(0);
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getOwnItemBinding(
                takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOwnItemBinding_ProducerFeignClientReturnsNoItems() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        when(mockProducerFeignClient.getItem("brandGuid", 2)).thenReturn(Collections.emptyList());

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setIsRack(0);
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishSkuUnit("eDishSkuUnit");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setDishNameWithSpec("dishNameWithSpec");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        mappingRespDTO.setParentSkuGuid("eDishSkuCode");
        mappingRespDTO.setIsChoice(0);
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getOwnItemBinding(
                takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOwnItemBinding_ItemFeignClientReturnsNoItems() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        when(mockProducerFeignClient.getItem("brandGuid", 2)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getOwnItemBinding(
                takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOwnItemBinding_ItemBindExtendInfoServiceReturnsNoItems() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        when(mockProducerFeignClient.getItem("brandGuid", 2)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setIsRack(0);
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishSkuUnit("eDishSkuUnit");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setDishNameWithSpec("dishNameWithSpec");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        mappingRespDTO.setParentSkuGuid("eDishSkuCode");
        mappingRespDTO.setIsChoice(0);
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getOwnItemBinding(
                takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemBinding() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        when(mockProducerFeignClient.getItem("brandGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setIsRack(0);
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishSkuUnit("eDishSkuUnit");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setDishNameWithSpec("dishNameWithSpec");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        mappingRespDTO.setParentSkuGuid("eDishSkuCode");
        mappingRespDTO.setIsChoice(0);
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Configure ProducerFeignClient.getType(...).
        final UnMappedType unMappedType1 = new UnMappedType();
        unMappedType1.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem2 = new UnMappedItem();
        unMappedItem2.setUnItemSkuId("unItemSkuId");
        unMappedItem2.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem2.setUnItemTypeId("unItemTypeId");
        unMappedItem2.setUnItemTypeName("unItemTypeName");
        unMappedItem2.setUnItemCountMapper(0);
        unMappedItem2.setErpItemId("eDishCode");
        unMappedItem2.setErpItemName("eDishName");
        unMappedItem2.setErpItemIsRack(0);
        unMappedItem2.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem2.setErpItemSkuName("eDishSkuName");
        unMappedItem2.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem2.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem2.setErpItemTypeId("categoryId");
        unMappedItem2.setErpItemTypeName("categoryName");
        unMappedItem2.setErpStoreGuid("erpStoreGuid");
        unMappedItem2.setExtendInfoId(0L);
        unMappedItem2.setErpItemIsKdsBindItem(false);
        unMappedItem2.setRealBindFlag(false);
        unMappedType1.setUnItemList(Arrays.asList(unMappedItem2));
        final List<UnMappedType> unMappedTypes = Arrays.asList(unMappedType1);
        when(mockProducerFeignClient.getType("brandGuid", 0)).thenReturn(unMappedTypes);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        // Configure KdsFeignClient.queryAllBoundItem(...).
        final PrdPointItemDTO prdPointItemDTO = new PrdPointItemDTO();
        prdPointItemDTO.setStoreGuid("storeGuid");
        prdPointItemDTO.setDeviceId("deviceId");
        prdPointItemDTO.setPointGuid("pointGuid");
        prdPointItemDTO.setItemGuid("itemGuid");
        prdPointItemDTO.setSkuGuid("skuGuid");
        final List<PrdPointItemDTO> prdPointItemDTOS = Arrays.asList(prdPointItemDTO);
        when(mockKdsFeignClient.queryAllBoundItem()).thenReturn(prdPointItemDTOS);

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d98536ee-6e02-4fa0-b5de-9ee61b86d82c");
        brandDTO.setUuid("ba3a31ca-0de8-443d-a18f-33d18669fdb9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setSalesModel(0);
        when(mockOrganizationService.queryBrandByStoreGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getItemBinding(takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemBinding_ProducerFeignClientGetItemReturnsNoItems() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        when(mockProducerFeignClient.getItem("brandGuid", 0)).thenReturn(Collections.emptyList());

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setIsRack(0);
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishSkuUnit("eDishSkuUnit");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setDishNameWithSpec("dishNameWithSpec");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        mappingRespDTO.setParentSkuGuid("eDishSkuCode");
        mappingRespDTO.setIsChoice(0);
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Configure ProducerFeignClient.getType(...).
        final UnMappedType unMappedType1 = new UnMappedType();
        unMappedType1.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        unMappedType1.setUnItemList(Arrays.asList(unMappedItem1));
        final List<UnMappedType> unMappedTypes = Arrays.asList(unMappedType1);
        when(mockProducerFeignClient.getType("brandGuid", 0)).thenReturn(unMappedTypes);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        // Configure KdsFeignClient.queryAllBoundItem(...).
        final PrdPointItemDTO prdPointItemDTO = new PrdPointItemDTO();
        prdPointItemDTO.setStoreGuid("storeGuid");
        prdPointItemDTO.setDeviceId("deviceId");
        prdPointItemDTO.setPointGuid("pointGuid");
        prdPointItemDTO.setItemGuid("itemGuid");
        prdPointItemDTO.setSkuGuid("skuGuid");
        final List<PrdPointItemDTO> prdPointItemDTOS = Arrays.asList(prdPointItemDTO);
        when(mockKdsFeignClient.queryAllBoundItem()).thenReturn(prdPointItemDTOS);

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d98536ee-6e02-4fa0-b5de-9ee61b86d82c");
        brandDTO.setUuid("ba3a31ca-0de8-443d-a18f-33d18669fdb9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setSalesModel(0);
        when(mockOrganizationService.queryBrandByStoreGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getItemBinding(takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemBinding_ItemFeignClientReturnsNoItems() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        when(mockProducerFeignClient.getItem("brandGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Configure ProducerFeignClient.getType(...).
        final UnMappedType unMappedType1 = new UnMappedType();
        unMappedType1.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem2 = new UnMappedItem();
        unMappedItem2.setUnItemSkuId("unItemSkuId");
        unMappedItem2.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem2.setUnItemTypeId("unItemTypeId");
        unMappedItem2.setUnItemTypeName("unItemTypeName");
        unMappedItem2.setUnItemCountMapper(0);
        unMappedItem2.setErpItemId("eDishCode");
        unMappedItem2.setErpItemName("eDishName");
        unMappedItem2.setErpItemIsRack(0);
        unMappedItem2.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem2.setErpItemSkuName("eDishSkuName");
        unMappedItem2.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem2.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem2.setErpItemTypeId("categoryId");
        unMappedItem2.setErpItemTypeName("categoryName");
        unMappedItem2.setErpStoreGuid("erpStoreGuid");
        unMappedItem2.setExtendInfoId(0L);
        unMappedItem2.setErpItemIsKdsBindItem(false);
        unMappedItem2.setRealBindFlag(false);
        unMappedType1.setUnItemList(Arrays.asList(unMappedItem2));
        final List<UnMappedType> unMappedTypes = Arrays.asList(unMappedType1);
        when(mockProducerFeignClient.getType("brandGuid", 0)).thenReturn(unMappedTypes);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        // Configure KdsFeignClient.queryAllBoundItem(...).
        final PrdPointItemDTO prdPointItemDTO = new PrdPointItemDTO();
        prdPointItemDTO.setStoreGuid("storeGuid");
        prdPointItemDTO.setDeviceId("deviceId");
        prdPointItemDTO.setPointGuid("pointGuid");
        prdPointItemDTO.setItemGuid("itemGuid");
        prdPointItemDTO.setSkuGuid("skuGuid");
        final List<PrdPointItemDTO> prdPointItemDTOS = Arrays.asList(prdPointItemDTO);
        when(mockKdsFeignClient.queryAllBoundItem()).thenReturn(prdPointItemDTOS);

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d98536ee-6e02-4fa0-b5de-9ee61b86d82c");
        brandDTO.setUuid("ba3a31ca-0de8-443d-a18f-33d18669fdb9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setSalesModel(0);
        when(mockOrganizationService.queryBrandByStoreGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getItemBinding(takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemBinding_ItemBindExtendInfoServiceReturnsNoItems() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        when(mockProducerFeignClient.getItem("brandGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setIsRack(0);
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishSkuUnit("eDishSkuUnit");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setDishNameWithSpec("dishNameWithSpec");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        mappingRespDTO.setParentSkuGuid("eDishSkuCode");
        mappingRespDTO.setIsChoice(0);
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure ProducerFeignClient.getType(...).
        final UnMappedType unMappedType1 = new UnMappedType();
        unMappedType1.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem2 = new UnMappedItem();
        unMappedItem2.setUnItemSkuId("unItemSkuId");
        unMappedItem2.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem2.setUnItemTypeId("unItemTypeId");
        unMappedItem2.setUnItemTypeName("unItemTypeName");
        unMappedItem2.setUnItemCountMapper(0);
        unMappedItem2.setErpItemId("eDishCode");
        unMappedItem2.setErpItemName("eDishName");
        unMappedItem2.setErpItemIsRack(0);
        unMappedItem2.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem2.setErpItemSkuName("eDishSkuName");
        unMappedItem2.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem2.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem2.setErpItemTypeId("categoryId");
        unMappedItem2.setErpItemTypeName("categoryName");
        unMappedItem2.setErpStoreGuid("erpStoreGuid");
        unMappedItem2.setExtendInfoId(0L);
        unMappedItem2.setErpItemIsKdsBindItem(false);
        unMappedItem2.setRealBindFlag(false);
        unMappedType1.setUnItemList(Arrays.asList(unMappedItem2));
        final List<UnMappedType> unMappedTypes = Arrays.asList(unMappedType1);
        when(mockProducerFeignClient.getType("brandGuid", 0)).thenReturn(unMappedTypes);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        // Configure KdsFeignClient.queryAllBoundItem(...).
        final PrdPointItemDTO prdPointItemDTO = new PrdPointItemDTO();
        prdPointItemDTO.setStoreGuid("storeGuid");
        prdPointItemDTO.setDeviceId("deviceId");
        prdPointItemDTO.setPointGuid("pointGuid");
        prdPointItemDTO.setItemGuid("itemGuid");
        prdPointItemDTO.setSkuGuid("skuGuid");
        final List<PrdPointItemDTO> prdPointItemDTOS = Arrays.asList(prdPointItemDTO);
        when(mockKdsFeignClient.queryAllBoundItem()).thenReturn(prdPointItemDTOS);

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d98536ee-6e02-4fa0-b5de-9ee61b86d82c");
        brandDTO.setUuid("ba3a31ca-0de8-443d-a18f-33d18669fdb9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setSalesModel(0);
        when(mockOrganizationService.queryBrandByStoreGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getItemBinding(takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemBinding_ProducerFeignClientGetTypeReturnsNoItems() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        when(mockProducerFeignClient.getItem("brandGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setIsRack(0);
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishSkuUnit("eDishSkuUnit");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setDishNameWithSpec("dishNameWithSpec");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        mappingRespDTO.setParentSkuGuid("eDishSkuCode");
        mappingRespDTO.setIsChoice(0);
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        when(mockProducerFeignClient.getType("brandGuid", 0)).thenReturn(Collections.emptyList());

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        // Configure KdsFeignClient.queryAllBoundItem(...).
        final PrdPointItemDTO prdPointItemDTO = new PrdPointItemDTO();
        prdPointItemDTO.setStoreGuid("storeGuid");
        prdPointItemDTO.setDeviceId("deviceId");
        prdPointItemDTO.setPointGuid("pointGuid");
        prdPointItemDTO.setItemGuid("itemGuid");
        prdPointItemDTO.setSkuGuid("skuGuid");
        final List<PrdPointItemDTO> prdPointItemDTOS = Arrays.asList(prdPointItemDTO);
        when(mockKdsFeignClient.queryAllBoundItem()).thenReturn(prdPointItemDTOS);

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d98536ee-6e02-4fa0-b5de-9ee61b86d82c");
        brandDTO.setUuid("ba3a31ca-0de8-443d-a18f-33d18669fdb9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setSalesModel(0);
        when(mockOrganizationService.queryBrandByStoreGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getItemBinding(takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemBinding_ItemMappingServiceReturnsNoItems() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        when(mockProducerFeignClient.getItem("brandGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setIsRack(0);
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishSkuUnit("eDishSkuUnit");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setDishNameWithSpec("dishNameWithSpec");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        mappingRespDTO.setParentSkuGuid("eDishSkuCode");
        mappingRespDTO.setIsChoice(0);
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Configure ProducerFeignClient.getType(...).
        final UnMappedType unMappedType1 = new UnMappedType();
        unMappedType1.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem2 = new UnMappedItem();
        unMappedItem2.setUnItemSkuId("unItemSkuId");
        unMappedItem2.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem2.setUnItemTypeId("unItemTypeId");
        unMappedItem2.setUnItemTypeName("unItemTypeName");
        unMappedItem2.setUnItemCountMapper(0);
        unMappedItem2.setErpItemId("eDishCode");
        unMappedItem2.setErpItemName("eDishName");
        unMappedItem2.setErpItemIsRack(0);
        unMappedItem2.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem2.setErpItemSkuName("eDishSkuName");
        unMappedItem2.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem2.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem2.setErpItemTypeId("categoryId");
        unMappedItem2.setErpItemTypeName("categoryName");
        unMappedItem2.setErpStoreGuid("erpStoreGuid");
        unMappedItem2.setExtendInfoId(0L);
        unMappedItem2.setErpItemIsKdsBindItem(false);
        unMappedItem2.setRealBindFlag(false);
        unMappedType1.setUnItemList(Arrays.asList(unMappedItem2));
        final List<UnMappedType> unMappedTypes = Arrays.asList(unMappedType1);
        when(mockProducerFeignClient.getType("brandGuid", 0)).thenReturn(unMappedTypes);

        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        // Configure KdsFeignClient.queryAllBoundItem(...).
        final PrdPointItemDTO prdPointItemDTO = new PrdPointItemDTO();
        prdPointItemDTO.setStoreGuid("storeGuid");
        prdPointItemDTO.setDeviceId("deviceId");
        prdPointItemDTO.setPointGuid("pointGuid");
        prdPointItemDTO.setItemGuid("itemGuid");
        prdPointItemDTO.setSkuGuid("skuGuid");
        final List<PrdPointItemDTO> prdPointItemDTOS = Arrays.asList(prdPointItemDTO);
        when(mockKdsFeignClient.queryAllBoundItem()).thenReturn(prdPointItemDTOS);

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d98536ee-6e02-4fa0-b5de-9ee61b86d82c");
        brandDTO.setUuid("ba3a31ca-0de8-443d-a18f-33d18669fdb9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setSalesModel(0);
        when(mockOrganizationService.queryBrandByStoreGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getItemBinding(takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemBinding_SkuMapMapperReturnsNoItems() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        when(mockProducerFeignClient.getItem("brandGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setIsRack(0);
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishSkuUnit("eDishSkuUnit");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setDishNameWithSpec("dishNameWithSpec");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        mappingRespDTO.setParentSkuGuid("eDishSkuCode");
        mappingRespDTO.setIsChoice(0);
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Configure ProducerFeignClient.getType(...).
        final UnMappedType unMappedType1 = new UnMappedType();
        unMappedType1.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem2 = new UnMappedItem();
        unMappedItem2.setUnItemSkuId("unItemSkuId");
        unMappedItem2.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem2.setUnItemTypeId("unItemTypeId");
        unMappedItem2.setUnItemTypeName("unItemTypeName");
        unMappedItem2.setUnItemCountMapper(0);
        unMappedItem2.setErpItemId("eDishCode");
        unMappedItem2.setErpItemName("eDishName");
        unMappedItem2.setErpItemIsRack(0);
        unMappedItem2.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem2.setErpItemSkuName("eDishSkuName");
        unMappedItem2.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem2.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem2.setErpItemTypeId("categoryId");
        unMappedItem2.setErpItemTypeName("categoryName");
        unMappedItem2.setErpStoreGuid("erpStoreGuid");
        unMappedItem2.setExtendInfoId(0L);
        unMappedItem2.setErpItemIsKdsBindItem(false);
        unMappedItem2.setRealBindFlag(false);
        unMappedType1.setUnItemList(Arrays.asList(unMappedItem2));
        final List<UnMappedType> unMappedTypes = Arrays.asList(unMappedType1);
        when(mockProducerFeignClient.getType("brandGuid", 0)).thenReturn(unMappedTypes);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure KdsFeignClient.queryAllBoundItem(...).
        final PrdPointItemDTO prdPointItemDTO = new PrdPointItemDTO();
        prdPointItemDTO.setStoreGuid("storeGuid");
        prdPointItemDTO.setDeviceId("deviceId");
        prdPointItemDTO.setPointGuid("pointGuid");
        prdPointItemDTO.setItemGuid("itemGuid");
        prdPointItemDTO.setSkuGuid("skuGuid");
        final List<PrdPointItemDTO> prdPointItemDTOS = Arrays.asList(prdPointItemDTO);
        when(mockKdsFeignClient.queryAllBoundItem()).thenReturn(prdPointItemDTOS);

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d98536ee-6e02-4fa0-b5de-9ee61b86d82c");
        brandDTO.setUuid("ba3a31ca-0de8-443d-a18f-33d18669fdb9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setSalesModel(0);
        when(mockOrganizationService.queryBrandByStoreGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getItemBinding(takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemBinding_KdsFeignClientReturnsNoItems() throws Exception {
        // Setup
        final TakeoutItemMappingReqDTO takeoutItemMappingReqDTO = new TakeoutItemMappingReqDTO();
        takeoutItemMappingReqDTO.setStoreGuid("brandGuid");
        takeoutItemMappingReqDTO.setTakeoutType(0);

        final TakeoutItemMappingRespDTO expectedResult = new TakeoutItemMappingRespDTO();
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("unItemTypeName");
        unMappedItem.setUnItemCountMapper(0);
        unMappedItem.setErpItemId("eDishCode");
        unMappedItem.setErpItemName("eDishName");
        unMappedItem.setErpItemIsRack(0);
        unMappedItem.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem.setErpItemSkuName("eDishSkuName");
        unMappedItem.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem.setErpItemTypeId("categoryId");
        unMappedItem.setErpItemTypeName("categoryName");
        unMappedItem.setErpStoreGuid("erpStoreGuid");
        unMappedItem.setExtendInfoId(0L);
        unMappedItem.setErpItemIsKdsBindItem(false);
        unMappedItem.setRealBindFlag(false);
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        expectedResult.setUnItemTypeList(Arrays.asList(unMappedType));
        final ErpMappingType erpMappingType = new ErpMappingType();
        erpMappingType.setErpItemTypeId("erpItemTypeId");
        erpMappingType.setErpItemTypeName("erpItemTypeName");
        final ErpMappingItem erpMappingItem = new ErpMappingItem();
        erpMappingItem.setErpItemGuid("eDishCode");
        erpMappingItem.setErpItemName("dishNameWithSpec");
        erpMappingItem.setErpItemSkuId("eDishSkuCode");
        erpMappingItem.setUnItemSkuId("unItemSkuId");
        erpMappingItem.setPlanItemName("planItemName");
        erpMappingType.setErpItemList(Arrays.asList(erpMappingItem));
        expectedResult.setErpItemTypeList(Arrays.asList(erpMappingType));
        expectedResult.setUnBindCount(0L);
        expectedResult.setInvalidBindCount(0L);
        expectedResult.setSalesModel(0);

        // Configure ProducerFeignClient.getItem(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        when(mockProducerFeignClient.getItem("brandGuid", 0)).thenReturn(unMappedItems);

        // Configure ItemFeignClient.mappingAllItems(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPlanItemName("planItemName");
        mappingRespDTO.setIsRack(0);
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishSkuUnit("eDishSkuUnit");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setDishNameWithSpec("dishNameWithSpec");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        mappingRespDTO.setParentSkuGuid("eDishSkuCode");
        mappingRespDTO.setIsChoice(0);
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.mappingAllItems(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Configure ProducerFeignClient.getType(...).
        final UnMappedType unMappedType1 = new UnMappedType();
        unMappedType1.setUnItemTypeId("unItemTypeId");
        final UnMappedItem unMappedItem2 = new UnMappedItem();
        unMappedItem2.setUnItemSkuId("unItemSkuId");
        unMappedItem2.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem2.setUnItemTypeId("unItemTypeId");
        unMappedItem2.setUnItemTypeName("unItemTypeName");
        unMappedItem2.setUnItemCountMapper(0);
        unMappedItem2.setErpItemId("eDishCode");
        unMappedItem2.setErpItemName("eDishName");
        unMappedItem2.setErpItemIsRack(0);
        unMappedItem2.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem2.setErpItemSkuName("eDishSkuName");
        unMappedItem2.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem2.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem2.setErpItemTypeId("categoryId");
        unMappedItem2.setErpItemTypeName("categoryName");
        unMappedItem2.setErpStoreGuid("erpStoreGuid");
        unMappedItem2.setExtendInfoId(0L);
        unMappedItem2.setErpItemIsKdsBindItem(false);
        unMappedItem2.setRealBindFlag(false);
        unMappedType1.setUnItemList(Arrays.asList(unMappedItem2));
        final List<UnMappedType> unMappedTypes = Arrays.asList(unMappedType1);
        when(mockProducerFeignClient.getType("brandGuid", 0)).thenReturn(unMappedTypes);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        when(mockKdsFeignClient.queryAllBoundItem()).thenReturn(Collections.emptyList());

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d98536ee-6e02-4fa0-b5de-9ee61b86d82c");
        brandDTO.setUuid("ba3a31ca-0de8-443d-a18f-33d18669fdb9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setSalesModel(0);
        when(mockOrganizationService.queryBrandByStoreGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final TakeoutItemMappingRespDTO result = mappingServiceImplUnderTest.getItemBinding(takeoutItemMappingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setCurrentPage(0);
        unItemQueryReq.setPageSize(0);
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        final TakeawayBatchMappingResult expectedResult = new TakeawayBatchMappingResult();
        expectedResult.setUnBindCount(0L);
        expectedResult.setLossBindCount(0L);
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("unItemName");
        expectedResult.setPageInfo(new Page<>(0L, 0L, Arrays.asList(unMappedItem)));

        // Configure ItemFeignClient.batchMappingAllItems(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.batchMappingAllItems(itemSingleDTO)).thenReturn(new HashMap<>());

        // Configure ProducerFeignClient.getItems(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        final UnItemQueryReq unItemQueryReq1 = new UnItemQueryReq();
        unItemQueryReq1.setCurrentPage(0);
        unItemQueryReq1.setPageSize(0);
        unItemQueryReq1.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq1.setTakeoutType(0);
        unItemQueryReq1.setBindingFlag(0);
        when(mockProducerFeignClient.getItems(unItemQueryReq1)).thenReturn(unMappedItems);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        final TakeawayBatchMappingResult result = mappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItems_ProducerFeignClientReturnsNoItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setCurrentPage(0);
        unItemQueryReq.setPageSize(0);
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        final TakeawayBatchMappingResult expectedResult = new TakeawayBatchMappingResult();
        expectedResult.setUnBindCount(0L);
        expectedResult.setLossBindCount(0L);
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("unItemName");
        expectedResult.setPageInfo(new Page<>(0L, 0L, Arrays.asList(unMappedItem)));

        // Configure ItemFeignClient.batchMappingAllItems(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.batchMappingAllItems(itemSingleDTO)).thenReturn(new HashMap<>());

        // Configure ProducerFeignClient.getItems(...).
        final UnItemQueryReq unItemQueryReq1 = new UnItemQueryReq();
        unItemQueryReq1.setCurrentPage(0);
        unItemQueryReq1.setPageSize(0);
        unItemQueryReq1.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq1.setTakeoutType(0);
        unItemQueryReq1.setBindingFlag(0);
        when(mockProducerFeignClient.getItems(unItemQueryReq1)).thenReturn(Collections.emptyList());

        // Run the test
        final TakeawayBatchMappingResult result = mappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItems_ItemMappingServiceReturnsNoItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setCurrentPage(0);
        unItemQueryReq.setPageSize(0);
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        final TakeawayBatchMappingResult expectedResult = new TakeawayBatchMappingResult();
        expectedResult.setUnBindCount(0L);
        expectedResult.setLossBindCount(0L);
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("unItemName");
        expectedResult.setPageInfo(new Page<>(0L, 0L, Arrays.asList(unMappedItem)));

        // Configure ItemFeignClient.batchMappingAllItems(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.batchMappingAllItems(itemSingleDTO)).thenReturn(new HashMap<>());

        // Configure ProducerFeignClient.getItems(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        final UnItemQueryReq unItemQueryReq1 = new UnItemQueryReq();
        unItemQueryReq1.setCurrentPage(0);
        unItemQueryReq1.setPageSize(0);
        unItemQueryReq1.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq1.setTakeoutType(0);
        unItemQueryReq1.setBindingFlag(0);
        when(mockProducerFeignClient.getItems(unItemQueryReq1)).thenReturn(unMappedItems);

        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        final TakeawayBatchMappingResult result = mappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItems_SkuMapMapperReturnsNoItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setCurrentPage(0);
        unItemQueryReq.setPageSize(0);
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        final TakeawayBatchMappingResult expectedResult = new TakeawayBatchMappingResult();
        expectedResult.setUnBindCount(0L);
        expectedResult.setLossBindCount(0L);
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("unItemName");
        expectedResult.setPageInfo(new Page<>(0L, 0L, Arrays.asList(unMappedItem)));

        // Configure ItemFeignClient.batchMappingAllItems(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.batchMappingAllItems(itemSingleDTO)).thenReturn(new HashMap<>());

        // Configure ProducerFeignClient.getItems(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        final UnItemQueryReq unItemQueryReq1 = new UnItemQueryReq();
        unItemQueryReq1.setCurrentPage(0);
        unItemQueryReq1.setPageSize(0);
        unItemQueryReq1.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq1.setTakeoutType(0);
        unItemQueryReq1.setBindingFlag(0);
        when(mockProducerFeignClient.getItems(unItemQueryReq1)).thenReturn(unMappedItems);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        final TakeawayBatchMappingResult result = mappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItems_ItemBindExtendInfoServiceReturnsNoItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setCurrentPage(0);
        unItemQueryReq.setPageSize(0);
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        final TakeawayBatchMappingResult expectedResult = new TakeawayBatchMappingResult();
        expectedResult.setUnBindCount(0L);
        expectedResult.setLossBindCount(0L);
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("unItemName");
        expectedResult.setPageInfo(new Page<>(0L, 0L, Arrays.asList(unMappedItem)));

        // Configure ItemFeignClient.batchMappingAllItems(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.batchMappingAllItems(itemSingleDTO)).thenReturn(new HashMap<>());

        // Configure ProducerFeignClient.getItems(...).
        final UnMappedItem unMappedItem1 = new UnMappedItem();
        unMappedItem1.setUnItemSkuId("unItemSkuId");
        unMappedItem1.setUnItemNameWithSku("unItemNameWithSku");
        unMappedItem1.setUnItemTypeId("unItemTypeId");
        unMappedItem1.setUnItemTypeName("unItemTypeName");
        unMappedItem1.setUnItemCountMapper(0);
        unMappedItem1.setErpItemId("eDishCode");
        unMappedItem1.setErpItemName("eDishName");
        unMappedItem1.setErpItemIsRack(0);
        unMappedItem1.setErpItemSkuId("actualErpItemSkuId");
        unMappedItem1.setErpItemSkuName("eDishSkuName");
        unMappedItem1.setErpItemNameWithSku("dishNameWithSpec");
        unMappedItem1.setErpItemSkuUnit("eDishSkuUnit");
        unMappedItem1.setErpItemTypeId("categoryId");
        unMappedItem1.setErpItemTypeName("categoryName");
        unMappedItem1.setErpStoreGuid("erpStoreGuid");
        unMappedItem1.setExtendInfoId(0L);
        unMappedItem1.setErpItemIsKdsBindItem(false);
        unMappedItem1.setRealBindFlag(false);
        final List<UnMappedItem> unMappedItems = Arrays.asList(unMappedItem1);
        final UnItemQueryReq unItemQueryReq1 = new UnItemQueryReq();
        unItemQueryReq1.setCurrentPage(0);
        unItemQueryReq1.setPageSize(0);
        unItemQueryReq1.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq1.setTakeoutType(0);
        unItemQueryReq1.setBindingFlag(0);
        when(mockProducerFeignClient.getItems(unItemQueryReq1)).thenReturn(unMappedItems);

        // Configure ItemMappingService.list(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        final List<ItemMappingDO> itemMappingDOS = Arrays.asList(itemMappingDO);
        when(mockItemMappingService.list(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDOS);

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final TakeawayBatchMappingResult result = mappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBindItem() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemMappingService.getOne(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        when(mockItemMappingService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDO);

        // Configure ItemBindExtendInfoService.getOne(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        when(mockItemBindExtendInfoService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDo);

        // Configure SkuMapMapper.selectOne(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockSkuMapMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(skuMapDO);

        // Run the test
        mappingServiceImplUnderTest.bindItem(unItemBindUnbindReq);

        // Verify the results
        // Confirm ItemMappingService.save(...).
        final ItemMappingDO entity = new ItemMappingDO();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setTakeoutType(0);
        entity.setUnItemSkuId("unItemSkuId");
        entity.setErpItemSkuGuid("actualErpItemSkuId");
        entity.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).save(entity);

        // Confirm ItemMappingService.updateById(...).
        final ItemMappingDO entity1 = new ItemMappingDO();
        entity1.setId(0L);
        entity1.setStoreGuid("storeGuid");
        entity1.setTakeoutType(0);
        entity1.setUnItemSkuId("unItemSkuId");
        entity1.setErpItemSkuGuid("actualErpItemSkuId");
        entity1.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).updateById(entity1);
        verify(mockItemBindExtendInfoService).update(any(LambdaUpdateWrapper.class));

        // Confirm ItemBindExtendInfoService.save(...).
        final ItemBindExtendInfoDo entity2 = new ItemBindExtendInfoDo();
        entity2.setId(0L);
        entity2.setStoreGuid("storeGuid");
        entity2.setTakeoutType(0);
        entity2.setErpItemSkuId("actualErpItemSkuId");
        entity2.setUnItemCountMapper(0);
        entity2.setUnItemSkuId("unItemSkuId");
        verify(mockItemBindExtendInfoService).save(entity2);

        // Confirm ProducerFeignClient.setMapping(...).
        final UnItemBindUnbindReq unItemBindUnbindReq1 = new UnItemBindUnbindReq();
        unItemBindUnbindReq1.setUnItemId("unItemId");
        unItemBindUnbindReq1.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq1.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq1.setExtendValue("extendValue");
        unItemBindUnbindReq1.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq1.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setUnItemCountMapper(0);
        unItemBindUnbindReq1.setStoreGuid("storeGuid");
        unItemBindUnbindReq1.setTakeoutType(0);
        unItemBindUnbindReq1.setMtSkuId("mtSkuId");
        verify(mockProducerFeignClient).setMapping(unItemBindUnbindReq1);
    }

    @Test
    public void testBindItem_SkuMapMapperSelectOneReturnsNull() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemMappingService.getOne(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        when(mockItemMappingService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDO);

        // Configure ItemBindExtendInfoService.getOne(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        when(mockItemBindExtendInfoService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDo);

        when(mockSkuMapMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        mappingServiceImplUnderTest.bindItem(unItemBindUnbindReq);

        // Verify the results
        // Confirm ItemMappingService.save(...).
        final ItemMappingDO entity = new ItemMappingDO();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setTakeoutType(0);
        entity.setUnItemSkuId("unItemSkuId");
        entity.setErpItemSkuGuid("actualErpItemSkuId");
        entity.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).save(entity);
        verify(mockItemBindExtendInfoService).update(any(LambdaUpdateWrapper.class));

        // Confirm SkuMapMapper.insert(...).
        final SkuMapDO entity1 = new SkuMapDO();
        entity1.setId(0L);
        entity1.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        entity1.setSourceGuid("actualErpItemSkuId");
        entity1.setSourceType(0);
        entity1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockSkuMapMapper).insert(entity1);

        // Confirm ProducerFeignClient.setMapping(...).
        final UnItemBindUnbindReq unItemBindUnbindReq1 = new UnItemBindUnbindReq();
        unItemBindUnbindReq1.setUnItemId("unItemId");
        unItemBindUnbindReq1.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq1.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq1.setExtendValue("extendValue");
        unItemBindUnbindReq1.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq1.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setUnItemCountMapper(0);
        unItemBindUnbindReq1.setStoreGuid("storeGuid");
        unItemBindUnbindReq1.setTakeoutType(0);
        unItemBindUnbindReq1.setMtSkuId("mtSkuId");
        verify(mockProducerFeignClient).setMapping(unItemBindUnbindReq1);
    }

    @Test
    public void testBatchBindItem1() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        // Configure ItemMappingService.getOne(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        when(mockItemMappingService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDO);

        // Configure ItemBindExtendInfoService.getOne(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        when(mockItemBindExtendInfoService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDo);

        // Configure SkuMapMapper.selectOne(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockSkuMapMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(skuMapDO);

        // Run the test
        mappingServiceImplUnderTest.batchBindItem(unItemBatchUnbindReq);

        // Verify the results
        // Confirm ItemMappingService.save(...).
        final ItemMappingDO entity = new ItemMappingDO();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setTakeoutType(0);
        entity.setUnItemSkuId("unItemSkuId");
        entity.setErpItemSkuGuid("actualErpItemSkuId");
        entity.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).save(entity);

        // Confirm ItemMappingService.updateById(...).
        final ItemMappingDO entity1 = new ItemMappingDO();
        entity1.setId(0L);
        entity1.setStoreGuid("storeGuid");
        entity1.setTakeoutType(0);
        entity1.setUnItemSkuId("unItemSkuId");
        entity1.setErpItemSkuGuid("actualErpItemSkuId");
        entity1.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).updateById(entity1);
        verify(mockItemBindExtendInfoService).update(any(LambdaUpdateWrapper.class));

        // Confirm ItemBindExtendInfoService.save(...).
        final ItemBindExtendInfoDo entity2 = new ItemBindExtendInfoDo();
        entity2.setId(0L);
        entity2.setStoreGuid("storeGuid");
        entity2.setTakeoutType(0);
        entity2.setErpItemSkuId("actualErpItemSkuId");
        entity2.setUnItemCountMapper(0);
        entity2.setUnItemSkuId("unItemSkuId");
        verify(mockItemBindExtendInfoService).save(entity2);

        // Confirm ProducerFeignClient.batchBindMapping(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq1 = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq1.setStoreGuid("storeGuid");
        unItemBatchUnbindReq1.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq1 = new UnItemBaseMapReq();
        unItemBaseMapReq1.setUnItemId("unItemId");
        unItemBaseMapReq1.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq1.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq1.setExtendValue("extendValue");
        unItemBaseMapReq1.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq1.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq1.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq1.setUnItemCountMapper(0);
        unItemBatchUnbindReq1.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq1));
        unItemBatchUnbindReq1.setBindFlag(false);
        verify(mockProducerFeignClient).batchBindMapping(unItemBatchUnbindReq1);
    }

    @Test
    public void testBatchBindItem1_SkuMapMapperSelectOneReturnsNull() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        // Configure ItemMappingService.getOne(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        when(mockItemMappingService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDO);

        // Configure ItemBindExtendInfoService.getOne(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        when(mockItemBindExtendInfoService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDo);

        when(mockSkuMapMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        mappingServiceImplUnderTest.batchBindItem(unItemBatchUnbindReq);

        // Verify the results
        // Confirm ItemMappingService.save(...).
        final ItemMappingDO entity = new ItemMappingDO();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setTakeoutType(0);
        entity.setUnItemSkuId("unItemSkuId");
        entity.setErpItemSkuGuid("actualErpItemSkuId");
        entity.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).save(entity);
        verify(mockItemBindExtendInfoService).update(any(LambdaUpdateWrapper.class));

        // Confirm SkuMapMapper.insert(...).
        final SkuMapDO entity1 = new SkuMapDO();
        entity1.setId(0L);
        entity1.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        entity1.setSourceGuid("actualErpItemSkuId");
        entity1.setSourceType(0);
        entity1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockSkuMapMapper).insert(entity1);

        // Confirm ProducerFeignClient.batchBindMapping(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq1 = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq1.setStoreGuid("storeGuid");
        unItemBatchUnbindReq1.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq1 = new UnItemBaseMapReq();
        unItemBaseMapReq1.setUnItemId("unItemId");
        unItemBaseMapReq1.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq1.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq1.setExtendValue("extendValue");
        unItemBaseMapReq1.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq1.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq1.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq1.setUnItemCountMapper(0);
        unItemBatchUnbindReq1.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq1));
        unItemBatchUnbindReq1.setBindFlag(false);
        verify(mockProducerFeignClient).batchBindMapping(unItemBatchUnbindReq1);
    }

    @Test
    public void testUpdateItemBindExtendInfo() {
        // Setup
        final UnMappedItem item = new UnMappedItem();
        item.setUnItemSkuId("unItemSkuId");
        item.setUnItemNameWithSku("unItemNameWithSku");
        item.setUnItemTypeId("unItemTypeId");
        item.setUnItemTypeName("unItemTypeName");
        item.setUnItemCountMapper(0);
        item.setErpItemId("eDishCode");
        item.setErpItemName("eDishName");
        item.setErpItemIsRack(0);
        item.setErpItemSkuId("actualErpItemSkuId");
        item.setErpItemSkuName("eDishSkuName");
        item.setErpItemNameWithSku("dishNameWithSpec");
        item.setErpItemSkuUnit("eDishSkuUnit");
        item.setErpItemTypeId("categoryId");
        item.setErpItemTypeName("categoryName");
        item.setErpStoreGuid("erpStoreGuid");
        item.setExtendInfoId(0L);
        item.setErpItemIsKdsBindItem(false);
        item.setRealBindFlag(false);

        // Configure ItemBindExtendInfoService.getOne(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        when(mockItemBindExtendInfoService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDo);

        // Run the test
        mappingServiceImplUnderTest.updateItemBindExtendInfo("storeGuid", 0, item);

        // Verify the results
        verify(mockItemBindExtendInfoService).update(any(LambdaUpdateWrapper.class));

        // Confirm ItemBindExtendInfoService.save(...).
        final ItemBindExtendInfoDo entity = new ItemBindExtendInfoDo();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setTakeoutType(0);
        entity.setErpItemSkuId("actualErpItemSkuId");
        entity.setUnItemCountMapper(0);
        entity.setUnItemSkuId("unItemSkuId");
        verify(mockItemBindExtendInfoService).save(entity);
    }

    @Test
    public void testUpdateItemBindExtendInfoDel() {
        // Setup
        // Run the test
        mappingServiceImplUnderTest.updateItemBindExtendInfoDel("storeGuid", 0, Arrays.asList("value"));

        // Verify the results
        verify(mockItemBindExtendInfoService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testQueryItemBindExtendInfo() {
        // Setup
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> expectedResult = Arrays.asList(itemBindExtendInfoDo);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo1 = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo1.setId(0L);
        itemBindExtendInfoDo1.setStoreGuid("storeGuid");
        itemBindExtendInfoDo1.setTakeoutType(0);
        itemBindExtendInfoDo1.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo1.setUnItemCountMapper(0);
        itemBindExtendInfoDo1.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo1);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        final List<ItemBindExtendInfoDo> result = mappingServiceImplUnderTest.queryItemBindExtendInfo("storeGuid", 0,
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryItemBindExtendInfo_ItemBindExtendInfoServiceReturnsNoItems() {
        // Setup
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemBindExtendInfoDo> result = mappingServiceImplUnderTest.queryItemBindExtendInfo("storeGuid", 0,
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testUnbindItem() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure ItemMappingService.getOne(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        when(mockItemMappingService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDO);

        // Run the test
        mappingServiceImplUnderTest.unbindItem(unItemBindUnbindReq);

        // Verify the results
        verify(mockItemMappingService).removeById(0L);

        // Confirm ProducerFeignClient.cancelMapping(...).
        final UnItemBindUnbindReq unItemBindUnbindReq1 = new UnItemBindUnbindReq();
        unItemBindUnbindReq1.setUnItemId("unItemId");
        unItemBindUnbindReq1.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq1.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq1.setExtendValue("extendValue");
        unItemBindUnbindReq1.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq1.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setUnItemCountMapper(0);
        unItemBindUnbindReq1.setStoreGuid("storeGuid");
        unItemBindUnbindReq1.setTakeoutType(0);
        unItemBindUnbindReq1.setMtSkuId("mtSkuId");
        verify(mockProducerFeignClient).cancelMapping(unItemBindUnbindReq1);
        verify(mockItemBindExtendInfoService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testBatchUnbindItem() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        mappingServiceImplUnderTest.batchUnbindItem(unItemBatchUnbindReq);

        // Verify the results
        // Confirm ProducerFeignClient.batchCancelMapping(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq1 = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq1.setStoreGuid("storeGuid");
        unItemBatchUnbindReq1.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq1 = new UnItemBaseMapReq();
        unItemBaseMapReq1.setUnItemId("unItemId");
        unItemBaseMapReq1.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq1.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq1.setExtendValue("extendValue");
        unItemBaseMapReq1.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq1.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq1.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq1.setUnItemCountMapper(0);
        unItemBatchUnbindReq1.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq1));
        unItemBatchUnbindReq1.setBindFlag(false);
        verify(mockProducerFeignClient).batchCancelMapping(unItemBatchUnbindReq1);
        verify(mockItemBindExtendInfoService).removeByIds(Arrays.asList("value"));
    }

    @Test
    public void testBatchUnbindItem_ItemBindExtendInfoServiceListReturnsNoItems() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        mappingServiceImplUnderTest.batchUnbindItem(unItemBatchUnbindReq);

        // Verify the results
        // Confirm ProducerFeignClient.batchCancelMapping(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq1 = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq1.setStoreGuid("storeGuid");
        unItemBatchUnbindReq1.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq1 = new UnItemBaseMapReq();
        unItemBaseMapReq1.setUnItemId("unItemId");
        unItemBaseMapReq1.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq1.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq1.setExtendValue("extendValue");
        unItemBaseMapReq1.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq1.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq1.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq1.setUnItemCountMapper(0);
        unItemBatchUnbindReq1.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq1));
        unItemBatchUnbindReq1.setBindFlag(false);
        verify(mockProducerFeignClient).batchCancelMapping(unItemBatchUnbindReq1);
    }

    @Test
    public void testRollbackOrderItemCount1() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderType(0);
        orderReadDO.setOrderSubType(0);
        orderReadDO.setStoreGuid("storeGuid");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setItemCode("itemCode");
        itemDO.setItemCount(new BigDecimal("0.00"));
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        mappingServiceImplUnderTest.rollbackOrderItemCount(orderReadDO);

        // Verify the results
    }

    @Test
    public void testRollbackOrderItemCount1_ItemBindExtendInfoServiceReturnsNoItems() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderType(0);
        orderReadDO.setOrderSubType(0);
        orderReadDO.setStoreGuid("storeGuid");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setItemCode("itemCode");
        itemDO.setItemCount(new BigDecimal("0.00"));
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        mappingServiceImplUnderTest.rollbackOrderItemCount(orderReadDO);

        // Verify the results
    }

    @Test
    public void testRollbackOrderItemCount2() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setItemCode("itemCode");
        itemDO.setItemCount(new BigDecimal("0.00"));
        final List<ItemDO> items = Arrays.asList(itemDO);

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        mappingServiceImplUnderTest.rollbackOrderItemCount(items, "storeGuid", 0);

        // Verify the results
    }

    @Test
    public void testRollbackOrderItemCount2_ItemBindExtendInfoServiceReturnsNoItems() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setItemCode("itemCode");
        itemDO.setItemCount(new BigDecimal("0.00"));
        final List<ItemDO> items = Arrays.asList(itemDO);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        mappingServiceImplUnderTest.rollbackOrderItemCount(items, "storeGuid", 0);

        // Verify the results
    }

    @Test
    public void testSyncTcdItemMapping() {
        // Setup
        final TcdSyncItemMappingDTO itemMappingDTO = new TcdSyncItemMappingDTO();
        itemMappingDTO.setEnterpriseGuid("enterpriseGuid");
        itemMappingDTO.setStoreGuid("storeGuid");
        final TcdSyncItemMappingDTO.TcdStoreMapping tcdStoreMapping = new TcdSyncItemMappingDTO.TcdStoreMapping();
        tcdStoreMapping.setStoreGuid("storeGuid");
        final TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping tcdItemMapping = new TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping();
        tcdStoreMapping.setItemMappingList(Arrays.asList(tcdItemMapping));
        itemMappingDTO.setStoreMappingList(Arrays.asList(tcdStoreMapping));

        // Run the test
        mappingServiceImplUnderTest.syncTcdItemMapping(itemMappingDTO);

        // Verify the results
        // Confirm ItemBindExtendInfoService.syncTcdItemMappingCount(...).
        final TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping tcdItemMapping1 = new TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping();
        tcdItemMapping1.setSourceUnItemSkuId("sourceUnItemSkuId");
        tcdItemMapping1.setTargetUnItemSkuId("targetUnItemSkuId");
        tcdItemMapping1.setSyncUnItemSkuId("syncUnItemSkuId");
        final List<TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping> tcdItemMappingList = Arrays.asList(
                tcdItemMapping1);
        verify(mockItemBindExtendInfoService).syncTcdItemMappingCount(tcdItemMappingList);

        // Confirm ItemMappingService.syncTcdItemMapping(...).
        final TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping tcdItemMapping2 = new TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping();
        tcdItemMapping2.setSourceUnItemSkuId("sourceUnItemSkuId");
        tcdItemMapping2.setTargetUnItemSkuId("targetUnItemSkuId");
        tcdItemMapping2.setSyncUnItemSkuId("syncUnItemSkuId");
        final List<TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping> tcdItemMappingList1 = Arrays.asList(
                tcdItemMapping2);
        verify(mockItemMappingService).syncTcdItemMapping(tcdItemMappingList1);
    }

    @Test
    public void testListSourceGuidsAndGuidsByGuids() {
        // Setup
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> expectedResult = Arrays.asList(skuMapDO);

        // Configure SkuMapMapper.selectList(...).
        final SkuMapDO skuMapDO1 = new SkuMapDO();
        skuMapDO1.setId(0L);
        skuMapDO1.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO1.setSourceGuid("actualErpItemSkuId");
        skuMapDO1.setSourceType(0);
        skuMapDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SkuMapDO> skuMapDOS = Arrays.asList(skuMapDO1);
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(skuMapDOS);

        // Run the test
        final List<SkuMapDO> result = mappingServiceImplUnderTest.listSourceGuidsAndGuidsByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListSourceGuidsAndGuidsByGuids_SkuMapMapperReturnsNoItems() {
        // Setup
        when(mockSkuMapMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<SkuMapDO> result = mappingServiceImplUnderTest.listSourceGuidsAndGuidsByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testBatchBind() {
        // Setup
        final UnItemBatchBindUnbindReq unItemBatchBindUnbindReq = new UnItemBatchBindUnbindReq();
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");
        unItemBatchBindUnbindReq.setBindUnbindReqList(Arrays.asList(unItemBindUnbindReq));
        unItemBatchBindUnbindReq.setItemGuid("erpItemGuid");
        unItemBatchBindUnbindReq.setSkuGuid("actualErpItemSkuId");
        unItemBatchBindUnbindReq.setUnItemCountMapper(0);
        unItemBatchBindUnbindReq.setTakeoutType(0);
        unItemBatchBindUnbindReq.setBrandGuid("brandGuid");

        // Configure ItemFeignClient.queryItemStore(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.queryItemStore(itemSingleDTO)).thenReturn(Arrays.asList("value"));

        // Configure ItemMappingService.getOne(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        when(mockItemMappingService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDO);

        // Configure ItemBindExtendInfoService.getOne(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        when(mockItemBindExtendInfoService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDo);

        // Configure SkuMapMapper.selectOne(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockSkuMapMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(skuMapDO);

        // Run the test
        mappingServiceImplUnderTest.batchBind(unItemBatchBindUnbindReq);

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");

        // Confirm ItemMappingService.save(...).
        final ItemMappingDO entity = new ItemMappingDO();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setTakeoutType(0);
        entity.setUnItemSkuId("unItemSkuId");
        entity.setErpItemSkuGuid("actualErpItemSkuId");
        entity.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).save(entity);

        // Confirm ItemMappingService.updateById(...).
        final ItemMappingDO entity1 = new ItemMappingDO();
        entity1.setId(0L);
        entity1.setStoreGuid("storeGuid");
        entity1.setTakeoutType(0);
        entity1.setUnItemSkuId("unItemSkuId");
        entity1.setErpItemSkuGuid("actualErpItemSkuId");
        entity1.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).updateById(entity1);
        verify(mockItemBindExtendInfoService).update(any(LambdaUpdateWrapper.class));

        // Confirm ItemBindExtendInfoService.save(...).
        final ItemBindExtendInfoDo entity2 = new ItemBindExtendInfoDo();
        entity2.setId(0L);
        entity2.setStoreGuid("storeGuid");
        entity2.setTakeoutType(0);
        entity2.setErpItemSkuId("actualErpItemSkuId");
        entity2.setUnItemCountMapper(0);
        entity2.setUnItemSkuId("unItemSkuId");
        verify(mockItemBindExtendInfoService).save(entity2);

        // Confirm ProducerFeignClient.batchBindMapping(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);
        verify(mockProducerFeignClient).batchBindMapping(unItemBatchUnbindReq);

        // Confirm FixManage.autoFix(...).
        final UnItemBindUnbindReq unItemBindUnbindReq1 = new UnItemBindUnbindReq();
        unItemBindUnbindReq1.setUnItemId("unItemId");
        unItemBindUnbindReq1.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq1.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq1.setExtendValue("extendValue");
        unItemBindUnbindReq1.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq1.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setUnItemCountMapper(0);
        unItemBindUnbindReq1.setStoreGuid("storeGuid");
        unItemBindUnbindReq1.setTakeoutType(0);
        unItemBindUnbindReq1.setMtSkuId("mtSkuId");
        verify(mockFixManager).autoFix(unItemBindUnbindReq1);
    }

    @Test
    public void testBatchBind_ItemFeignClientReturnsNoItems() {
        // Setup
        final UnItemBatchBindUnbindReq unItemBatchBindUnbindReq = new UnItemBatchBindUnbindReq();
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");
        unItemBatchBindUnbindReq.setBindUnbindReqList(Arrays.asList(unItemBindUnbindReq));
        unItemBatchBindUnbindReq.setItemGuid("erpItemGuid");
        unItemBatchBindUnbindReq.setSkuGuid("actualErpItemSkuId");
        unItemBatchBindUnbindReq.setUnItemCountMapper(0);
        unItemBatchBindUnbindReq.setTakeoutType(0);
        unItemBatchBindUnbindReq.setBrandGuid("brandGuid");

        // Configure ItemFeignClient.queryItemStore(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.queryItemStore(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure ItemMappingService.getOne(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        when(mockItemMappingService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDO);

        // Configure ItemBindExtendInfoService.getOne(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        when(mockItemBindExtendInfoService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDo);

        // Configure SkuMapMapper.selectOne(...).
        final SkuMapDO skuMapDO = new SkuMapDO();
        skuMapDO.setId(0L);
        skuMapDO.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        skuMapDO.setSourceGuid("actualErpItemSkuId");
        skuMapDO.setSourceType(0);
        skuMapDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockSkuMapMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(skuMapDO);

        // Run the test
        mappingServiceImplUnderTest.batchBind(unItemBatchBindUnbindReq);

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");

        // Confirm ItemMappingService.save(...).
        final ItemMappingDO entity = new ItemMappingDO();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setTakeoutType(0);
        entity.setUnItemSkuId("unItemSkuId");
        entity.setErpItemSkuGuid("actualErpItemSkuId");
        entity.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).save(entity);

        // Confirm ItemMappingService.updateById(...).
        final ItemMappingDO entity1 = new ItemMappingDO();
        entity1.setId(0L);
        entity1.setStoreGuid("storeGuid");
        entity1.setTakeoutType(0);
        entity1.setUnItemSkuId("unItemSkuId");
        entity1.setErpItemSkuGuid("actualErpItemSkuId");
        entity1.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).updateById(entity1);
        verify(mockItemBindExtendInfoService).update(any(LambdaUpdateWrapper.class));

        // Confirm ItemBindExtendInfoService.save(...).
        final ItemBindExtendInfoDo entity2 = new ItemBindExtendInfoDo();
        entity2.setId(0L);
        entity2.setStoreGuid("storeGuid");
        entity2.setTakeoutType(0);
        entity2.setErpItemSkuId("actualErpItemSkuId");
        entity2.setUnItemCountMapper(0);
        entity2.setUnItemSkuId("unItemSkuId");
        verify(mockItemBindExtendInfoService).save(entity2);

        // Confirm ProducerFeignClient.batchBindMapping(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);
        verify(mockProducerFeignClient).batchBindMapping(unItemBatchUnbindReq);

        // Confirm FixManage.autoFix(...).
        final UnItemBindUnbindReq unItemBindUnbindReq1 = new UnItemBindUnbindReq();
        unItemBindUnbindReq1.setUnItemId("unItemId");
        unItemBindUnbindReq1.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq1.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq1.setExtendValue("extendValue");
        unItemBindUnbindReq1.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq1.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setUnItemCountMapper(0);
        unItemBindUnbindReq1.setStoreGuid("storeGuid");
        unItemBindUnbindReq1.setTakeoutType(0);
        unItemBindUnbindReq1.setMtSkuId("mtSkuId");
        verify(mockFixManager).autoFix(unItemBindUnbindReq1);
    }

    @Test
    public void testBatchBind_SkuMapMapperSelectOneReturnsNull() {
        // Setup
        final UnItemBatchBindUnbindReq unItemBatchBindUnbindReq = new UnItemBatchBindUnbindReq();
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");
        unItemBatchBindUnbindReq.setBindUnbindReqList(Arrays.asList(unItemBindUnbindReq));
        unItemBatchBindUnbindReq.setItemGuid("erpItemGuid");
        unItemBatchBindUnbindReq.setSkuGuid("actualErpItemSkuId");
        unItemBatchBindUnbindReq.setUnItemCountMapper(0);
        unItemBatchBindUnbindReq.setTakeoutType(0);
        unItemBatchBindUnbindReq.setBrandGuid("brandGuid");

        // Configure ItemFeignClient.queryItemStore(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setItemList(Arrays.asList("value"));
        itemSingleDTO.setStoreGuids(Arrays.asList("value"));
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemFeignClient.queryItemStore(itemSingleDTO)).thenReturn(Arrays.asList("value"));

        // Configure ItemMappingService.getOne(...).
        final ItemMappingDO itemMappingDO = new ItemMappingDO();
        itemMappingDO.setId(0L);
        itemMappingDO.setStoreGuid("storeGuid");
        itemMappingDO.setTakeoutType(0);
        itemMappingDO.setUnItemSkuId("unItemSkuId");
        itemMappingDO.setErpItemSkuGuid("actualErpItemSkuId");
        itemMappingDO.setMapperGuid("actualErpItemSkuId");
        when(mockItemMappingService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemMappingDO);

        // Configure ItemBindExtendInfoService.getOne(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        when(mockItemBindExtendInfoService.getOne(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDo);

        when(mockSkuMapMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        mappingServiceImplUnderTest.batchBind(unItemBatchBindUnbindReq);

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");

        // Confirm ItemMappingService.save(...).
        final ItemMappingDO entity = new ItemMappingDO();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setTakeoutType(0);
        entity.setUnItemSkuId("unItemSkuId");
        entity.setErpItemSkuGuid("actualErpItemSkuId");
        entity.setMapperGuid("actualErpItemSkuId");
        verify(mockItemMappingService).save(entity);
        verify(mockItemBindExtendInfoService).update(any(LambdaUpdateWrapper.class));

        // Confirm SkuMapMapper.insert(...).
        final SkuMapDO entity1 = new SkuMapDO();
        entity1.setId(0L);
        entity1.setGuid("279b83aa-fb11-4cf1-abb0-d30ada932cad");
        entity1.setSourceGuid("actualErpItemSkuId");
        entity1.setSourceType(0);
        entity1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockSkuMapMapper).insert(entity1);

        // Confirm ProducerFeignClient.batchBindMapping(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);
        verify(mockProducerFeignClient).batchBindMapping(unItemBatchUnbindReq);

        // Confirm FixManage.autoFix(...).
        final UnItemBindUnbindReq unItemBindUnbindReq1 = new UnItemBindUnbindReq();
        unItemBindUnbindReq1.setUnItemId("unItemId");
        unItemBindUnbindReq1.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq1.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq1.setExtendValue("extendValue");
        unItemBindUnbindReq1.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq1.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq1.setUnItemCountMapper(0);
        unItemBindUnbindReq1.setStoreGuid("storeGuid");
        unItemBindUnbindReq1.setTakeoutType(0);
        unItemBindUnbindReq1.setMtSkuId("mtSkuId");
        verify(mockFixManager).autoFix(unItemBindUnbindReq1);
    }

    @Test
    public void testBatchUnbind() {
        // Setup
        final UnItemBatchBindUnbindReq batchBindUnbindReq = new UnItemBatchBindUnbindReq();
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");
        batchBindUnbindReq.setBindUnbindReqList(Arrays.asList(unItemBindUnbindReq));
        batchBindUnbindReq.setItemGuid("erpItemGuid");
        batchBindUnbindReq.setSkuGuid("actualErpItemSkuId");
        batchBindUnbindReq.setUnItemCountMapper(0);
        batchBindUnbindReq.setTakeoutType(0);
        batchBindUnbindReq.setBrandGuid("brandGuid");

        // Configure ItemBindExtendInfoService.list(...).
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("actualErpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        itemBindExtendInfoDo.setUnItemSkuId("unItemSkuId");
        final List<ItemBindExtendInfoDo> itemBindExtendInfoDos = Arrays.asList(itemBindExtendInfoDo);
        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(itemBindExtendInfoDos);

        // Run the test
        mappingServiceImplUnderTest.batchUnbind(batchBindUnbindReq);

        // Verify the results
        // Confirm ProducerFeignClient.batchCancelMapping(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);
        verify(mockProducerFeignClient).batchCancelMapping(unItemBatchUnbindReq);
        verify(mockItemBindExtendInfoService).removeByIds(Arrays.asList("value"));
    }

    @Test
    public void testBatchUnbind_ItemBindExtendInfoServiceListReturnsNoItems() {
        // Setup
        final UnItemBatchBindUnbindReq batchBindUnbindReq = new UnItemBatchBindUnbindReq();
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");
        batchBindUnbindReq.setBindUnbindReqList(Arrays.asList(unItemBindUnbindReq));
        batchBindUnbindReq.setItemGuid("erpItemGuid");
        batchBindUnbindReq.setSkuGuid("actualErpItemSkuId");
        batchBindUnbindReq.setUnItemCountMapper(0);
        batchBindUnbindReq.setTakeoutType(0);
        batchBindUnbindReq.setBrandGuid("brandGuid");

        when(mockItemBindExtendInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        mappingServiceImplUnderTest.batchUnbind(batchBindUnbindReq);

        // Verify the results
        // Confirm ProducerFeignClient.batchCancelMapping(...).
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        unItemBatchUnbindReq.setTakeoutType(0);
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBaseMapReq.setUnItemCountMapper(0);
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);
        verify(mockProducerFeignClient).batchCancelMapping(unItemBatchUnbindReq);
    }
}
