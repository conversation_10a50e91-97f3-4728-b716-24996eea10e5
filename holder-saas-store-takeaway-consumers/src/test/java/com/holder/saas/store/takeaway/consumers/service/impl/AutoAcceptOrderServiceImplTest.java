package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AutoAcceptOrderServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    @InjectMocks
    private AutoAcceptOrderServiceImpl autoAcceptOrderServiceImplUnderTest;

    @Test
    public void testCreateOrder() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setId(0L);
        orderDO.setShopId(0L);
        orderDO.setOrderGuid("orderGuid");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setIsAutoAccept(false);

        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        autoAcceptOrderServiceImplUnderTest.createOrder(orderDO);

        // Verify the results
    }

    @Test
    public void testRemoveOrder() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setId(0L);
        orderDO.setShopId(0L);
        orderDO.setOrderGuid("orderGuid");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setIsAutoAccept(false);

        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        autoAcceptOrderServiceImplUnderTest.removeOrder(orderDO);

        // Verify the results
    }

    @Test
    public void testGetOrderQueue() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = autoAcceptOrderServiceImplUnderTest.getOrderQueue("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(new HashSet<>(Arrays.asList("value")));
    }
}
