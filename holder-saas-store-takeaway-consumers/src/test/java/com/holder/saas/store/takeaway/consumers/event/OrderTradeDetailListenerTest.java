package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.service.OrderTradeDetailService;
import com.holder.saas.store.takeaway.consumers.service.rpc.CloudEnterpriseFeignClient;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderTradeDetailDTO;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.InetSocketAddress;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderTradeDetailListenerTest {

    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private CloudEnterpriseFeignClient mockEnterpriseFeignClient;
    @Mock
    private OrderTradeDetailService mockOrderTradeDetailService;

    private OrderTradeDetailListener orderTradeDetailListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        orderTradeDetailListenerUnderTest = new OrderTradeDetailListener(mockDynamicHelper, mockEnterpriseFeignClient,
                mockOrderTradeDetailService);
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final TakeoutOrderTradeDetailDTO tradeDetailDTO = new TakeoutOrderTradeDetailDTO();
        tradeDetailDTO.setEnterpriseGuid("enterpriseGuid");
        tradeDetailDTO.setActivityDetails("activityDetails");
        tradeDetailDTO.setCommisionAmount("commisionAmount");
        tradeDetailDTO.setFoodAmount("foodAmount");
        tradeDetailDTO.setOrderId("orderId");

        final MessageExt messageExt = new MessageExt();
        messageExt.setQueueId(0);
        messageExt.setBornTimestamp(0L);
        messageExt.setBornHost(new InetSocketAddress("localhost", 80));
        messageExt.setStoreTimestamp(0L);
        messageExt.setStoreHost(new InetSocketAddress("localhost", 80));

        when(mockEnterpriseFeignClient.hasEnterprise("enterpriseGuid")).thenReturn(false);

        // Run the test
        final boolean result = orderTradeDetailListenerUnderTest.consumeMsg(tradeDetailDTO, messageExt);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testConsumeMsg_CloudEnterpriseFeignClientReturnsTrue() {
        // Setup
        final TakeoutOrderTradeDetailDTO tradeDetailDTO = new TakeoutOrderTradeDetailDTO();
        tradeDetailDTO.setEnterpriseGuid("enterpriseGuid");
        tradeDetailDTO.setActivityDetails("activityDetails");
        tradeDetailDTO.setCommisionAmount("commisionAmount");
        tradeDetailDTO.setFoodAmount("foodAmount");
        tradeDetailDTO.setOrderId("orderId");

        final MessageExt messageExt = new MessageExt();
        messageExt.setQueueId(0);
        messageExt.setBornTimestamp(0L);
        messageExt.setBornHost(new InetSocketAddress("localhost", 80));
        messageExt.setStoreTimestamp(0L);
        messageExt.setStoreHost(new InetSocketAddress("localhost", 80));

        when(mockEnterpriseFeignClient.hasEnterprise("enterpriseGuid")).thenReturn(true);

        // Run the test
        final boolean result = orderTradeDetailListenerUnderTest.consumeMsg(tradeDetailDTO, messageExt);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");

        // Confirm OrderTradeDetailService.create(...).
        final TakeoutOrderTradeDetailDTO orderTradeDetailDTO = new TakeoutOrderTradeDetailDTO();
        orderTradeDetailDTO.setEnterpriseGuid("enterpriseGuid");
        orderTradeDetailDTO.setActivityDetails("activityDetails");
        orderTradeDetailDTO.setCommisionAmount("commisionAmount");
        orderTradeDetailDTO.setFoodAmount("foodAmount");
        orderTradeDetailDTO.setOrderId("orderId");
        verify(mockOrderTradeDetailService).create(orderTradeDetailDTO);
        verify(mockDynamicHelper).removeThreadLocalDatabaseInfo();
    }
}
