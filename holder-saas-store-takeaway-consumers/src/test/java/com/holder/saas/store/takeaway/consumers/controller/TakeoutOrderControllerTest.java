package com.holder.saas.store.takeaway.consumers.controller;

import com.alibaba.fastjson.JSON;
import com.holder.saas.store.takeaway.consumers.HolderSaasStoreTakeawayConsumersApplication;
import com.holderzone.saas.store.dto.order.BusinessDayDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutAndStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @description 外卖相关接口测试类
 * @date 2022/1/19 11:34
 * @className: TakeoutOrderControllerTest
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreTakeawayConsumersApplication.class)
public class TakeoutOrderControllerTest {

    private static final String userInfo = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"2104211624474030006\",\"storeName\": \"涂山\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";

    private String secret = "A80247ED9AB8DAF35E7CE4059ED88A41EC239814F8867EA3E9CD84A4A91C9243";

    private static final String TAKEOUT = "/takeout";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void listOrder() {
    }

    @Test
    public void pageOrderByAdjust() throws Exception {
        AdjustByOrderListQuery query = new AdjustByOrderListQuery();
        query.setCurrentPage(1);
        query.setPageSize(10);
        String jsonString = JSON.toJSONString(query);
        MvcResult mvcResult = mockMvc.perform(post(TAKEOUT + "/page_order_by_adjust")
                        .header(USER_INFO, userInfo)
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }

    @Test
    public void getOrderDetail() {
    }

    @Test
    public void acceptOrder() {
    }

    @Test
    public void cancelOrder() {
    }

    @Test
    public void agreeCancelReq() {
    }

    @Test
    public void disagreeCancelReq() {
    }

    @Test
    public void agreeRefundReq() {
    }

    @Test
    public void disagreeRefundReq() {
    }

    @Test
    public void replyRemindOrder() {
    }

    @Test
    public void orderUpdate() {
    }

    @Test
    public void getDistribution() {
    }

    @Test
    public void goShipping() {
    }

    @Test
    public void doneShipping() {
    }

    @Test
    public void cancelShipping() {
    }

    @Test
    public void deliveryChange() {
    }

    @Test
    public void deliveryLocation() {
    }

    @Test
    public void pageAbnormalData() throws Exception {
        TakeoutItemAbnormalDataReqDTO query = new TakeoutItemAbnormalDataReqDTO();
        query.setCurrentPage(1);
        query.setPageSize(50);
        query.setStartDateTime(LocalDateTime.parse("2021-04-28 09:48:02", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        query.setEndDateTime(LocalDateTime.parse("2021-04-28 11:03:14", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        query.setSequenceType(1);
        query.setOrderType(0);
        query.setBrandGuid("6788282061309345792");
        query.setKeywords("哈");
        String jsonString = JSON.toJSONString(query);
        MvcResult mvcResult = mockMvc.perform(post(TAKEOUT + "/abnormal_data_page")
                        .header(USER_INFO, userInfo)
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }

    @Test
    public void listDataFix() throws Exception {
        TakeoutItemDataFixReqDTO query = new TakeoutItemDataFixReqDTO();
        query.setStartDateTime(LocalDateTime.parse("2021-04-27 18:22:26", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        query.setEndDateTime(LocalDateTime.parse("2021-04-28 15:25:58", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        List<TakeoutAndStoreReqDTO> takeoutAndStore = new ArrayList<>();
        TakeoutAndStoreReqDTO dto = new TakeoutAndStoreReqDTO();
        dto.setStoreGuid("2104211624474030006");
        dto.setTakeoutItemNumber("1003");
        takeoutAndStore.add(dto);
        query.setTakeoutAndStore(takeoutAndStore);
        String jsonString = JSON.toJSONString(query);
        MvcResult mvcResult = mockMvc.perform(post(TAKEOUT + "/data_fix_list")
                        .header(USER_INFO, userInfo)
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }

    @Test
    public void listBusinessDayDTO() throws Exception {
        List<BusinessDayDTO> businessList = Lists.newArrayList();
        BusinessDayDTO businessDayDTO1 = new BusinessDayDTO();
        businessDayDTO1.setEnterpriseGuid("4895");
        businessDayDTO1.setOrderNo("221019180942177793");
        businessList.add(businessDayDTO1);

        BusinessDayDTO businessDayDTO2 = new BusinessDayDTO();
        businessDayDTO2.setEnterpriseGuid("2009281531195930006");
        businessDayDTO2.setOrderNo("220820150803708980");
        businessList.add(businessDayDTO2);

        String jsonString = JSON.toJSONString(businessList);
        MvcResult mvcResult = mockMvc.perform(post(TAKEOUT + "/businessDay/list")
                        .header(USER_INFO, userInfo)
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }
}