package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.service.rpc.BizMsgFeignClient;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import javafx.util.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class MessageListenerTest {

    @Mock
    private BizMsgFeignClient mockBizMsgFeignClient;

    private MessageListener messageListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        messageListenerUnderTest = new MessageListener(mockBizMsgFeignClient);
    }

    @Test
    public void testHandleMessage() {
        // Setup
        final UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("enterpriseGuid");
        userContext.setEnterpriseName("enterpriseName");
        userContext.setEnterpriseNo("enterpriseNo");
        userContext.setStoreGuid("storeGuid");
        userContext.setStoreName("storeName");
        final MessageEvent messageEvent = new MessageEvent(
                new Pair<>(userContext, BusinessMessageDTO.builder().build()));

        // Run the test
        messageListenerUnderTest.handleMessage(messageEvent);

        // Verify the results
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder().build());
    }
}
