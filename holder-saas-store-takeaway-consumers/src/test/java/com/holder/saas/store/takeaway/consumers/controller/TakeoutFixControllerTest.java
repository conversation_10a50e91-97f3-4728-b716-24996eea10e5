package com.holder.saas.store.takeaway.consumers.controller;

import com.alibaba.fastjson.JSON;
import com.holder.saas.store.takeaway.consumers.HolderSaasStoreTakeawayConsumersApplication;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixItemDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixRecordDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutRecordQueryDTO;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 修复外卖映射
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreTakeawayConsumersApplication.class)
public class TakeoutFixControllerTest {

    private static final String userInfo = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"6619160595813892096\",\"storeName\": \"听雨阁\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";

    private static final String ITEM_FIX = "/takeout/fix";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void pageInfo() throws Exception {
        TakeoutRecordQueryDTO query = new TakeoutRecordQueryDTO();
        String jsonString = JSON.toJSONString(query);

        MvcResult mvcResult = mockMvc.perform(post(ITEM_FIX + "/page")
                .header(USER_INFO, userInfo)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }


    @Test
    public void listByRecordId() throws Exception {
        String recordId = "1";
        MvcResult mvcResult = mockMvc.perform(get(ITEM_FIX + "/query?recordId=" + recordId)
                .header(USER_INFO, userInfo)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }


    @Test
    public void fix() throws Exception {
        TakeoutFixDTO fixDTO = new TakeoutFixDTO();
        fixDTO.setCommitFlag(true);
        fixDTO.setFixBindFlag(false);

        TakeoutFixRecordDTO recordDTO = new TakeoutFixRecordDTO();
        recordDTO.setFixCount(30);
        recordDTO.setStartTime(LocalDateTime.parse("2022-05-11 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        recordDTO.setEndTime(LocalDateTime.parse("2021-05-12 20:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        fixDTO.setRecordReqDTO(recordDTO);

        List<TakeoutFixItemDTO> itemReqDTOList = Lists.newArrayList();
        TakeoutFixItemDTO item = new TakeoutFixItemDTO();
        item.setStoreGuid("2104211624474030006");
        item.setStoreName("涂山");
        item.setTakeoutItemName("烤扇贝（点6送1）1(中份)");
        item.setOrderSubType(0);
        item.setThirdSkuId("1002");
        item.setErpItemName("小菜一碟");
        item.setErpItemPrice(new BigDecimal("10"));
        item.setErpItemSkuGuid("678805875094401843201");
        item.setErpItemCount(new BigDecimal("1"));
        item.setTakeoutOrderCount(10);
        itemReqDTOList.add(item);
        fixDTO.setItemReqDTOList(itemReqDTOList);

        String jsonString = JSON.toJSONString(fixDTO);

        MvcResult mvcResult = mockMvc.perform(post(ITEM_FIX)
                .header(USER_INFO, userInfo)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }

}