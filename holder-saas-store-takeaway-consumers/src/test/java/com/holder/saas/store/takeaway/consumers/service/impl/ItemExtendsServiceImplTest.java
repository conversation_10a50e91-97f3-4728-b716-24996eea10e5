package com.holder.saas.store.takeaway.consumers.service.impl;

import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;

public class ItemExtendsServiceImplTest {

    private ItemExtendsServiceImpl itemExtendsServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        itemExtendsServiceImplUnderTest = new ItemExtendsServiceImpl();
    }

    @Test
    public void testGetTotalCostPrice() {
        // Setup
        // Run the test
        final BigDecimal result = itemExtendsServiceImplUnderTest.getTotalCostPrice(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }
}
