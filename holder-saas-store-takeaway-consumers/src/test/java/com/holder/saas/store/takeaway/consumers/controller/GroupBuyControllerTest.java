package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.GroupBuyService;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(GroupBuyController.class)
public class GroupBuyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private GroupBuyService mockGroupBuyService;

    @Test
    public void testCheckTicket() throws Exception {
        // Setup
        // Configure GroupBuyService.checkTicket(...).
        final MtCouponDoCheckRespDTO mtCouponDoCheckRespDTO = new MtCouponDoCheckRespDTO();
        mtCouponDoCheckRespDTO.setOrderId("orderId");
        mtCouponDoCheckRespDTO.setCouponCodes(Arrays.asList("value"));
        mtCouponDoCheckRespDTO.setDealTitle("dealTitle");
        mtCouponDoCheckRespDTO.setDealValue(0.0);
        mtCouponDoCheckRespDTO.setDealId(0);
        final CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
        couPonReqDTO.setCouponCode("couponCode");
        couPonReqDTO.setCount(0);
        couPonReqDTO.setErpId("erpId");
        couPonReqDTO.setErpName("erpName");
        couPonReqDTO.setErpOrderId("erpOrderId");
        when(mockGroupBuyService.checkTicket(couPonReqDTO)).thenReturn(mtCouponDoCheckRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/groupbuy/check_ticket")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDoCheck() throws Exception {
        // Setup
        // Configure GroupBuyService.doCheck(...).
        final MtCouponDoCheckRespDTO mtCouponDoCheckRespDTO = new MtCouponDoCheckRespDTO();
        mtCouponDoCheckRespDTO.setOrderId("orderId");
        mtCouponDoCheckRespDTO.setCouponCodes(Arrays.asList("value"));
        mtCouponDoCheckRespDTO.setDealTitle("dealTitle");
        mtCouponDoCheckRespDTO.setDealValue(0.0);
        mtCouponDoCheckRespDTO.setDealId(0);
        final CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
        couPonReqDTO.setCouponCode("couponCode");
        couPonReqDTO.setCount(0);
        couPonReqDTO.setErpId("erpId");
        couPonReqDTO.setErpName("erpName");
        couPonReqDTO.setErpOrderId("erpOrderId");
        when(mockGroupBuyService.doCheck(couPonReqDTO)).thenReturn(mtCouponDoCheckRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/groupbuy/do_check")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPreCheck() throws Exception {
        // Setup
        // Configure GroupBuyService.preCheck(...).
        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setIsVoucher(false);
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setHasUseActivity(false);
        when(mockGroupBuyService.preCheck(couPonPreReqDTO)).thenReturn(mtCouponPreRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/groupbuy/pre_check")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCancalTicket() throws Exception {
        // Setup
        // Configure GroupBuyService.cancalTicket(...).
        final CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setCouponCode("couponCode");
        couponDelReqDTO.setErpId("erpId");
        couponDelReqDTO.setErpName("erpName");
        when(mockGroupBuyService.cancalTicket(couponDelReqDTO)).thenReturn(new MtDelCouponRespDTO(0, "message"));

        // Run the test and verify the results
        mockMvc.perform(post("/groupbuy/cancel_ticket")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCancalTicket_GroupBuyServiceReturnsError() throws Exception {
        // Setup
        // Configure GroupBuyService.cancalTicket(...).
        final CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setCouponCode("couponCode");
        couponDelReqDTO.setErpId("erpId");
        couponDelReqDTO.setErpName("erpName");
        when(mockGroupBuyService.cancalTicket(couponDelReqDTO)).thenReturn(MtDelCouponRespDTO.buildError("message"));

        // Run the test and verify the results
        mockMvc.perform(post("/groupbuy/cancel_ticket")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryGroupTradeDetail() throws Exception {
        // Setup
        // Configure GroupBuyService.queryGroupTradeDetail(...).
        final MtCouponTradeDetailRespDTO mtCouponTradeDetailRespDTO = new MtCouponTradeDetailRespDTO();
        mtCouponTradeDetailRespDTO.setStoreGuid("storeGuid");
        mtCouponTradeDetailRespDTO.setMtStoreGuid("mtStoreGuid");
        mtCouponTradeDetailRespDTO.setMtStoreName("mtStoreName");
        mtCouponTradeDetailRespDTO.setBizCost(0.0);
        mtCouponTradeDetailRespDTO.setBuyPrice(0.0);
        final CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
        couPonReqDTO.setCouponCode("couponCode");
        couPonReqDTO.setCount(0);
        couPonReqDTO.setErpId("erpId");
        couPonReqDTO.setErpName("erpName");
        couPonReqDTO.setErpOrderId("erpOrderId");
        when(mockGroupBuyService.queryGroupTradeDetail(couPonReqDTO)).thenReturn(mtCouponTradeDetailRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/groupbuy/trade/detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
