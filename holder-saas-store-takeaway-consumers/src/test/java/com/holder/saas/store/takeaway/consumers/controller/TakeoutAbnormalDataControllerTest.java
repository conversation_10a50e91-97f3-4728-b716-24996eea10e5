package com.holder.saas.store.takeaway.consumers.controller;

import com.alibaba.fastjson.JSON;
import com.holder.saas.store.takeaway.consumers.HolderSaasStoreTakeawayConsumersApplication;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @description 外卖异常数据测试类
 * @date 2022/5/23 16:39
 * @className: TakeoutAbnormalDataControllerTest
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreTakeawayConsumersApplication.class)
public class TakeoutAbnormalDataControllerTest {

    private static final String userInfo = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"2104211624474030006\",\"storeName\": \"涂山\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";

    private static final String ABNORMAL_DATA = "/abnormal_data";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void page() {
    }

    @Test
    public void move() throws Exception {
        String jsonString = JSON.toJSONString("");
        MvcResult mvcResult = mockMvc.perform(post(ABNORMAL_DATA + "/move")
                .header(USER_INFO, userInfo)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }
}