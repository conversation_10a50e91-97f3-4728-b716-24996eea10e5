package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderTradeDetailDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderTradeDetailServiceImplTest {

    @Mock
    private OrderService mockOrderService;

    private OrderTradeDetailServiceImpl orderTradeDetailServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        orderTradeDetailServiceImplUnderTest = new OrderTradeDetailServiceImpl(mockOrderService);
    }

    @Test
    public void testCreate() {
        // Setup
        final TakeoutOrderTradeDetailDTO orderTradeDetailDTO = new TakeoutOrderTradeDetailDTO();
        orderTradeDetailDTO.setEnterpriseGuid("enterpriseGuid");
        orderTradeDetailDTO.setActivityDetails("activityDetails");
        orderTradeDetailDTO.setCommisionAmount("commisionAmount");
        orderTradeDetailDTO.setFoodAmount("foodAmount");
        orderTradeDetailDTO.setOrderId("orderId");

        // Configure OrderService.getOne(...).
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderSubType(0);
        orderDO.setBrandGuid("brandGuid");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderId("orderId");
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderDO);

        // Run the test
        orderTradeDetailServiceImplUnderTest.create(orderTradeDetailDTO);

        // Verify the results
    }
}
