package com.holder.saas.store.takeaway.consumers.service.impl;

import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.mapstruct.OrderMapstruct;
import com.holder.saas.store.takeaway.consumers.service.ConfigService;
import com.holder.saas.store.takeaway.consumers.service.ItemClientService;
import com.holder.saas.store.takeaway.consumers.service.TakeoutMsgFactory;
import com.holder.saas.store.takeaway.consumers.service.TakeoutPrintFactory;
import com.holder.saas.store.takeaway.consumers.service.rpc.*;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.content.PrintLabelDTO;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintTakeoutDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.TakeoutFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrintServiceImplTest {

    @Mock
    private TakeoutPrintFactory mockTakeoutPrintFactory;
    @Mock
    private TakeoutMsgFactory mockTakeoutMsgFactory;
    @Mock
    private ItemFeignClient mockItemFeignClient;
    @Mock
    private PrintFeignClient mockPrintFeignClient;
    @Mock
    private BizMsgFeignClient mockBizMsgFeignClient;
    @Mock
    private StaffFeignClient mockStaffFeignClient;
    @Mock
    private OrgFeignClient mockOrgFeignClient;
    @Mock
    private OrderMapstruct mockOrderMapstruct;
    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;
    @Mock
    private ItemClientService mockItemClientService;
    @Mock
    private RedissonClient mockRedissonSingleClient;
    @Mock
    private ConfigService configService;

    private PrintServiceImpl printServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printServiceImplUnderTest = new PrintServiceImpl(mockTakeoutPrintFactory, mockTakeoutMsgFactory,
                mockItemFeignClient, mockPrintFeignClient, mockBizMsgFeignClient, mockOrgFeignClient, mockOrderMapstruct, mockStaffFeignClient,
                mockDefaultRocketMqProducer, mockItemClientService,
                mockRedissonSingleClient, configService);
    }

    @Test
    public void testPrintAll() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuPartDTO = new HashMap<>();

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("8847bc5b-a134-47c7-9404-c8948fa86b6b");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setContactTel("contactTel");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockStaffFeignClient.queryMchntType()).thenReturn("result");

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO1, false)).thenReturn(printTakeoutDTO);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockOrderMapstruct.readToRead(orderReadDO3)).thenReturn(orderReadDO2);

        // Configure TakeoutMsgFactory.createOrderMappingFailed(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutMsgFactory.createOrderMappingFailed(orderReadDO4)).thenReturn(businessMessageDTO);

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setParentGuid("parentGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Configure TakeoutPrintFactory.createKitchenOrderDTO(...).
        final PrintOrderItemDTO printOrderItemDTO = new PrintOrderItemDTO();
        printOrderItemDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printOrderItemDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        printOrderItemDTO.setEstimateDeliveredTimeString("estimateDeliveredTimeString");
        final OrderReadDO orderReadDO5 = new OrderReadDO();
        orderReadDO5.setStoreName("name");
        orderReadDO5.setStoreGuid("storeGuid");
        orderReadDO5.setOrderId("orderId");
        orderReadDO5.setOrderViewId("orderViewId");
        orderReadDO5.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO5.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO5 = new ItemDO();
        itemDO5.setItemSku("itemSku");
        itemDO5.setItemName("itemName");
        itemDO5.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO5.setArrayOfItem(Arrays.asList(itemDO5));
        when(mockTakeoutPrintFactory.createKitchenOrderDTO(orderReadDO5, new HashMap<>(), new HashMap<>()))
                .thenReturn(printOrderItemDTO);

        // Configure PrintFeignClient.queryTakeout(...).
        final TakeoutFormatDTO takeoutFormatDTO = new TakeoutFormatDTO();
        final FormatMetadata platform = new FormatMetadata();
        platform.setSize(0);
        platform.setEnable(false);
        takeoutFormatDTO.setPlatform(platform);
        final FormatMetadata foodFinishBarCode = new FormatMetadata();
        foodFinishBarCode.setSize(0);
        foodFinishBarCode.setEnable(false);
        takeoutFormatDTO.setFoodFinishBarCode(foodFinishBarCode);
        when(mockPrintFeignClient.queryTakeout("storeGuid")).thenReturn(takeoutFormatDTO);

        when(mockRedissonSingleClient.getBucket(eq("s"), any(Codec.class))).thenReturn(null);

        // Configure TakeoutPrintFactory.createLabelLabelDTO(...).
        final PrintLabelDTO printLabelDTO = new PrintLabelDTO();
        printLabelDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord2 = new PrintItemRecord();
        printItemRecord2.setOrderItemGuid("orderItemGuid");
        printItemRecord2.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printLabelDTO.setItemRecordList(Arrays.asList(printItemRecord2));
        printLabelDTO.setTel("contactTel");
        printLabelDTO.setTradeMode(0);
        final OrderReadDO orderReadDO6 = new OrderReadDO();
        orderReadDO6.setStoreName("name");
        orderReadDO6.setStoreGuid("storeGuid");
        orderReadDO6.setOrderId("orderId");
        orderReadDO6.setOrderViewId("orderViewId");
        orderReadDO6.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO6.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO6 = new ItemDO();
        itemDO6.setItemSku("itemSku");
        itemDO6.setItemName("itemName");
        itemDO6.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO6.setArrayOfItem(Arrays.asList(itemDO6));
        when(mockTakeoutPrintFactory.createLabelLabelDTO(orderReadDO6, new HashMap<>())).thenReturn(printLabelDTO);

        // Run the test
        printServiceImplUnderTest.printAll(orderReadDO, 0, mapOfSkuPartDTO, Lists.newArrayList());

        // Verify the results
        // Confirm TakeoutPrintFactory.fillTakeoutItemTypeName(...).
        final PrintTakeoutDTO billTakeawayDTO = new PrintTakeoutDTO();
        billTakeawayDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord3 = new PrintItemRecord();
        printItemRecord3.setOrderItemGuid("orderItemGuid");
        printItemRecord3.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        billTakeawayDTO.setItemRecordList(Arrays.asList(printItemRecord3));
        billTakeawayDTO.setPlatform("platform");
        verify(mockTakeoutPrintFactory).fillTakeoutItemTypeName(billTakeawayDTO, new HashMap<>());
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgFeignClient.msg(...).
        final BusinessMessageDTO businessMessageDTO1 = new BusinessMessageDTO();
        businessMessageDTO1.setMessageGuid("messageGuid");
        businessMessageDTO1.setSubject("subject");
        businessMessageDTO1.setContent("content");
        businessMessageDTO1.setMessageType(0);
        businessMessageDTO1.setDetailMessageType(0);
        verify(mockBizMsgFeignClient).msg(businessMessageDTO1);
    }

    @Test
    public void testPrintAll_OrgFeignClientReturnsNull() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuPartDTO = new HashMap<>();
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(null);
        when(mockStaffFeignClient.queryMchntType()).thenReturn("result");

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO1, false)).thenReturn(printTakeoutDTO);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockOrderMapstruct.readToRead(orderReadDO3)).thenReturn(orderReadDO2);

        // Configure TakeoutMsgFactory.createOrderMappingFailed(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutMsgFactory.createOrderMappingFailed(orderReadDO4)).thenReturn(businessMessageDTO);

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setParentGuid("parentGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Configure TakeoutPrintFactory.createKitchenOrderDTO(...).
        final PrintOrderItemDTO printOrderItemDTO = new PrintOrderItemDTO();
        printOrderItemDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printOrderItemDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        printOrderItemDTO.setEstimateDeliveredTimeString("estimateDeliveredTimeString");
        final OrderReadDO orderReadDO5 = new OrderReadDO();
        orderReadDO5.setStoreName("name");
        orderReadDO5.setStoreGuid("storeGuid");
        orderReadDO5.setOrderId("orderId");
        orderReadDO5.setOrderViewId("orderViewId");
        orderReadDO5.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO5.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO5 = new ItemDO();
        itemDO5.setItemSku("itemSku");
        itemDO5.setItemName("itemName");
        itemDO5.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO5.setArrayOfItem(Arrays.asList(itemDO5));
        when(mockTakeoutPrintFactory.createKitchenOrderDTO(orderReadDO5, new HashMap<>(), new HashMap<>()))
                .thenReturn(printOrderItemDTO);

        // Configure PrintFeignClient.queryTakeout(...).
        final TakeoutFormatDTO takeoutFormatDTO = new TakeoutFormatDTO();
        final FormatMetadata platform = new FormatMetadata();
        platform.setSize(0);
        platform.setEnable(false);
        takeoutFormatDTO.setPlatform(platform);
        final FormatMetadata foodFinishBarCode = new FormatMetadata();
        foodFinishBarCode.setSize(0);
        foodFinishBarCode.setEnable(false);
        takeoutFormatDTO.setFoodFinishBarCode(foodFinishBarCode);
        when(mockPrintFeignClient.queryTakeout("storeGuid")).thenReturn(takeoutFormatDTO);

        when(mockRedissonSingleClient.getBucket(eq("s"), any(Codec.class))).thenReturn(null);

        // Configure TakeoutPrintFactory.createLabelLabelDTO(...).
        final PrintLabelDTO printLabelDTO = new PrintLabelDTO();
        printLabelDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord2 = new PrintItemRecord();
        printItemRecord2.setOrderItemGuid("orderItemGuid");
        printItemRecord2.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printLabelDTO.setItemRecordList(Arrays.asList(printItemRecord2));
        printLabelDTO.setTel("contactTel");
        printLabelDTO.setTradeMode(0);
        final OrderReadDO orderReadDO6 = new OrderReadDO();
        orderReadDO6.setStoreName("name");
        orderReadDO6.setStoreGuid("storeGuid");
        orderReadDO6.setOrderId("orderId");
        orderReadDO6.setOrderViewId("orderViewId");
        orderReadDO6.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO6.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO6 = new ItemDO();
        itemDO6.setItemSku("itemSku");
        itemDO6.setItemName("itemName");
        itemDO6.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO6.setArrayOfItem(Arrays.asList(itemDO6));
        when(mockTakeoutPrintFactory.createLabelLabelDTO(orderReadDO6, new HashMap<>())).thenReturn(printLabelDTO);

        // Run the test
        printServiceImplUnderTest.printAll(orderReadDO, 0, mapOfSkuPartDTO, Lists.newArrayList());

        // Verify the results
        // Confirm TakeoutPrintFactory.fillTakeoutItemTypeName(...).
        final PrintTakeoutDTO billTakeawayDTO = new PrintTakeoutDTO();
        billTakeawayDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord3 = new PrintItemRecord();
        printItemRecord3.setOrderItemGuid("orderItemGuid");
        printItemRecord3.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        billTakeawayDTO.setItemRecordList(Arrays.asList(printItemRecord3));
        billTakeawayDTO.setPlatform("platform");
        verify(mockTakeoutPrintFactory).fillTakeoutItemTypeName(billTakeawayDTO, new HashMap<>());
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgFeignClient.msg(...).
        final BusinessMessageDTO businessMessageDTO1 = new BusinessMessageDTO();
        businessMessageDTO1.setMessageGuid("messageGuid");
        businessMessageDTO1.setSubject("subject");
        businessMessageDTO1.setContent("content");
        businessMessageDTO1.setMessageType(0);
        businessMessageDTO1.setDetailMessageType(0);
        verify(mockBizMsgFeignClient).msg(businessMessageDTO1);
    }

    @Test
    public void testPrintAll_ItemClientServiceReturnsNoItems() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuPartDTO = new HashMap<>();

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("8847bc5b-a134-47c7-9404-c8948fa86b6b");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setContactTel("contactTel");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockStaffFeignClient.queryMchntType()).thenReturn("result");

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO1, false)).thenReturn(printTakeoutDTO);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockOrderMapstruct.readToRead(orderReadDO3)).thenReturn(orderReadDO2);

        // Configure TakeoutMsgFactory.createOrderMappingFailed(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutMsgFactory.createOrderMappingFailed(orderReadDO4)).thenReturn(businessMessageDTO);

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Configure TakeoutPrintFactory.createKitchenOrderDTO(...).
        final PrintOrderItemDTO printOrderItemDTO = new PrintOrderItemDTO();
        printOrderItemDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printOrderItemDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        printOrderItemDTO.setEstimateDeliveredTimeString("estimateDeliveredTimeString");
        final OrderReadDO orderReadDO5 = new OrderReadDO();
        orderReadDO5.setStoreName("name");
        orderReadDO5.setStoreGuid("storeGuid");
        orderReadDO5.setOrderId("orderId");
        orderReadDO5.setOrderViewId("orderViewId");
        orderReadDO5.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO5.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO5 = new ItemDO();
        itemDO5.setItemSku("itemSku");
        itemDO5.setItemName("itemName");
        itemDO5.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO5.setArrayOfItem(Arrays.asList(itemDO5));
        when(mockTakeoutPrintFactory.createKitchenOrderDTO(orderReadDO5, new HashMap<>(), new HashMap<>()))
                .thenReturn(printOrderItemDTO);

        // Configure PrintFeignClient.queryTakeout(...).
        final TakeoutFormatDTO takeoutFormatDTO = new TakeoutFormatDTO();
        final FormatMetadata platform = new FormatMetadata();
        platform.setSize(0);
        platform.setEnable(false);
        takeoutFormatDTO.setPlatform(platform);
        final FormatMetadata foodFinishBarCode = new FormatMetadata();
        foodFinishBarCode.setSize(0);
        foodFinishBarCode.setEnable(false);
        takeoutFormatDTO.setFoodFinishBarCode(foodFinishBarCode);
        when(mockPrintFeignClient.queryTakeout("storeGuid")).thenReturn(takeoutFormatDTO);

        when(mockRedissonSingleClient.getBucket(eq("s"), any(Codec.class))).thenReturn(null);

        // Configure TakeoutPrintFactory.createLabelLabelDTO(...).
        final PrintLabelDTO printLabelDTO = new PrintLabelDTO();
        printLabelDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord2 = new PrintItemRecord();
        printItemRecord2.setOrderItemGuid("orderItemGuid");
        printItemRecord2.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printLabelDTO.setItemRecordList(Arrays.asList(printItemRecord2));
        printLabelDTO.setTel("contactTel");
        printLabelDTO.setTradeMode(0);
        final OrderReadDO orderReadDO6 = new OrderReadDO();
        orderReadDO6.setStoreName("name");
        orderReadDO6.setStoreGuid("storeGuid");
        orderReadDO6.setOrderId("orderId");
        orderReadDO6.setOrderViewId("orderViewId");
        orderReadDO6.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO6.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO6 = new ItemDO();
        itemDO6.setItemSku("itemSku");
        itemDO6.setItemName("itemName");
        itemDO6.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO6.setArrayOfItem(Arrays.asList(itemDO6));
        when(mockTakeoutPrintFactory.createLabelLabelDTO(orderReadDO6, new HashMap<>())).thenReturn(printLabelDTO);

        // Run the test
        printServiceImplUnderTest.printAll(orderReadDO, 0, mapOfSkuPartDTO, Lists.newArrayList());

        // Verify the results
        // Confirm TakeoutPrintFactory.fillTakeoutItemTypeName(...).
        final PrintTakeoutDTO billTakeawayDTO = new PrintTakeoutDTO();
        billTakeawayDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord3 = new PrintItemRecord();
        printItemRecord3.setOrderItemGuid("orderItemGuid");
        printItemRecord3.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        billTakeawayDTO.setItemRecordList(Arrays.asList(printItemRecord3));
        billTakeawayDTO.setPlatform("platform");
        verify(mockTakeoutPrintFactory).fillTakeoutItemTypeName(billTakeawayDTO, new HashMap<>());
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgFeignClient.msg(...).
        final BusinessMessageDTO businessMessageDTO1 = new BusinessMessageDTO();
        businessMessageDTO1.setMessageGuid("messageGuid");
        businessMessageDTO1.setSubject("subject");
        businessMessageDTO1.setContent("content");
        businessMessageDTO1.setMessageType(0);
        businessMessageDTO1.setDetailMessageType(0);
        verify(mockBizMsgFeignClient).msg(businessMessageDTO1);
    }

    @Test
    public void testPrintBill() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        when(mockStaffFeignClient.queryMchntType()).thenReturn("result");

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("8847bc5b-a134-47c7-9404-c8948fa86b6b");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setContactTel("contactTel");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure ItemFeignClient.selectSkuTakeawayInfoRespDTOList(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setParentSkuGuid("parentSkuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        skuTakeawayInfoRespDTO.setItemGuid("itemGuid");
        skuTakeawayInfoRespDTO.setIsRack(0);
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO))
                .thenReturn(skuTakeawayInfoRespDTOS);

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO1, false)).thenReturn(printTakeoutDTO);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockOrderMapstruct.readToRead(orderReadDO3)).thenReturn(orderReadDO2);

        // Configure TakeoutMsgFactory.createOrderMappingFailed(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutMsgFactory.createOrderMappingFailed(orderReadDO4)).thenReturn(businessMessageDTO);

        // Run the test
        final String result = printServiceImplUnderTest.printBill(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo("result");

        // Confirm TakeoutPrintFactory.fillTakeoutItemTypeName(...).
        final PrintTakeoutDTO billTakeawayDTO = new PrintTakeoutDTO();
        billTakeawayDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        billTakeawayDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        billTakeawayDTO.setPlatform("platform");
        verify(mockTakeoutPrintFactory).fillTakeoutItemTypeName(billTakeawayDTO, new HashMap<>());
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgFeignClient.msg(...).
        final BusinessMessageDTO businessMessageDTO1 = new BusinessMessageDTO();
        businessMessageDTO1.setMessageGuid("messageGuid");
        businessMessageDTO1.setSubject("subject");
        businessMessageDTO1.setContent("content");
        businessMessageDTO1.setMessageType(0);
        businessMessageDTO1.setDetailMessageType(0);
        verify(mockBizMsgFeignClient).msg(businessMessageDTO1);
    }

    @Test
    public void testPrintBill_OrgFeignClientReturnsNull() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        when(mockStaffFeignClient.queryMchntType()).thenReturn("result");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(null);

        // Configure ItemFeignClient.selectSkuTakeawayInfoRespDTOList(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setParentSkuGuid("parentSkuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        skuTakeawayInfoRespDTO.setItemGuid("itemGuid");
        skuTakeawayInfoRespDTO.setIsRack(0);
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO))
                .thenReturn(skuTakeawayInfoRespDTOS);

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO1, false)).thenReturn(printTakeoutDTO);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockOrderMapstruct.readToRead(orderReadDO3)).thenReturn(orderReadDO2);

        // Configure TakeoutMsgFactory.createOrderMappingFailed(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutMsgFactory.createOrderMappingFailed(orderReadDO4)).thenReturn(businessMessageDTO);

        // Run the test
        final String result = printServiceImplUnderTest.printBill(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo("result");

        // Confirm TakeoutPrintFactory.fillTakeoutItemTypeName(...).
        final PrintTakeoutDTO billTakeawayDTO = new PrintTakeoutDTO();
        billTakeawayDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        billTakeawayDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        billTakeawayDTO.setPlatform("platform");
        verify(mockTakeoutPrintFactory).fillTakeoutItemTypeName(billTakeawayDTO, new HashMap<>());
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgFeignClient.msg(...).
        final BusinessMessageDTO businessMessageDTO1 = new BusinessMessageDTO();
        businessMessageDTO1.setMessageGuid("messageGuid");
        businessMessageDTO1.setSubject("subject");
        businessMessageDTO1.setContent("content");
        businessMessageDTO1.setMessageType(0);
        businessMessageDTO1.setDetailMessageType(0);
        verify(mockBizMsgFeignClient).msg(businessMessageDTO1);
    }

    @Test
    public void testPrintBill_ItemFeignClientReturnsNoItems() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        when(mockStaffFeignClient.queryMchntType()).thenReturn("result");

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("8847bc5b-a134-47c7-9404-c8948fa86b6b");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setContactTel("contactTel");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure ItemFeignClient.selectSkuTakeawayInfoRespDTOList(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO))
                .thenReturn(Collections.emptyList());

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO1, false)).thenReturn(printTakeoutDTO);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockOrderMapstruct.readToRead(orderReadDO3)).thenReturn(orderReadDO2);

        // Configure TakeoutMsgFactory.createOrderMappingFailed(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutMsgFactory.createOrderMappingFailed(orderReadDO4)).thenReturn(businessMessageDTO);

        // Run the test
        final String result = printServiceImplUnderTest.printBill(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo("result");

        // Confirm TakeoutPrintFactory.fillTakeoutItemTypeName(...).
        final PrintTakeoutDTO billTakeawayDTO = new PrintTakeoutDTO();
        billTakeawayDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        billTakeawayDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        billTakeawayDTO.setPlatform("platform");
        verify(mockTakeoutPrintFactory).fillTakeoutItemTypeName(billTakeawayDTO, new HashMap<>());
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgFeignClient.msg(...).
        final BusinessMessageDTO businessMessageDTO1 = new BusinessMessageDTO();
        businessMessageDTO1.setMessageGuid("messageGuid");
        businessMessageDTO1.setSubject("subject");
        businessMessageDTO1.setContent("content");
        businessMessageDTO1.setMessageType(0);
        businessMessageDTO1.setDetailMessageType(0);
        verify(mockBizMsgFeignClient).msg(businessMessageDTO1);
    }

    @Test
    public void testPrintKitchen() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("8847bc5b-a134-47c7-9404-c8948fa86b6b");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setContactTel("contactTel");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure ItemFeignClient.selectSkuTakeawayInfoRespDTOList(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setParentSkuGuid("parentSkuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        skuTakeawayInfoRespDTO.setItemGuid("itemGuid");
        skuTakeawayInfoRespDTO.setIsRack(0);
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO))
                .thenReturn(skuTakeawayInfoRespDTOS);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        when(mockOrderMapstruct.readToRead(orderReadDO2)).thenReturn(orderReadDO1);

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO3, true)).thenReturn(printTakeoutDTO);

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setParentGuid("parentGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO1)).thenReturn(itemInfoRespDTOS);

        // Configure TakeoutPrintFactory.createKitchenOrderDTO(...).
        final PrintOrderItemDTO printOrderItemDTO = new PrintOrderItemDTO();
        printOrderItemDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printOrderItemDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        printOrderItemDTO.setEstimateDeliveredTimeString("estimateDeliveredTimeString");
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutPrintFactory.createKitchenOrderDTO(orderReadDO4, new HashMap<>(), new HashMap<>()))
                .thenReturn(printOrderItemDTO);

        // Run the test
        final String result = printServiceImplUnderTest.printKitchen(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo("打印失败，菜品列表为空");
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testPrintKitchen_OrgFeignClientReturnsNull() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(null);

        // Configure ItemFeignClient.selectSkuTakeawayInfoRespDTOList(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setParentSkuGuid("parentSkuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        skuTakeawayInfoRespDTO.setItemGuid("itemGuid");
        skuTakeawayInfoRespDTO.setIsRack(0);
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO))
                .thenReturn(skuTakeawayInfoRespDTOS);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        when(mockOrderMapstruct.readToRead(orderReadDO2)).thenReturn(orderReadDO1);

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO3, true)).thenReturn(printTakeoutDTO);

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setParentGuid("parentGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO1)).thenReturn(itemInfoRespDTOS);

        // Configure TakeoutPrintFactory.createKitchenOrderDTO(...).
        final PrintOrderItemDTO printOrderItemDTO = new PrintOrderItemDTO();
        printOrderItemDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printOrderItemDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        printOrderItemDTO.setEstimateDeliveredTimeString("estimateDeliveredTimeString");
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutPrintFactory.createKitchenOrderDTO(orderReadDO4, new HashMap<>(), new HashMap<>()))
                .thenReturn(printOrderItemDTO);

        // Run the test
        final String result = printServiceImplUnderTest.printKitchen(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo("打印失败，菜品列表为空");
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testPrintKitchen_ItemFeignClientReturnsNoItems() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("8847bc5b-a134-47c7-9404-c8948fa86b6b");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setContactTel("contactTel");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure ItemFeignClient.selectSkuTakeawayInfoRespDTOList(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO))
                .thenReturn(Collections.emptyList());

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        when(mockOrderMapstruct.readToRead(orderReadDO2)).thenReturn(orderReadDO1);

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO3, true)).thenReturn(printTakeoutDTO);

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setParentGuid("parentGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO1)).thenReturn(itemInfoRespDTOS);

        // Configure TakeoutPrintFactory.createKitchenOrderDTO(...).
        final PrintOrderItemDTO printOrderItemDTO = new PrintOrderItemDTO();
        printOrderItemDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printOrderItemDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        printOrderItemDTO.setEstimateDeliveredTimeString("estimateDeliveredTimeString");
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutPrintFactory.createKitchenOrderDTO(orderReadDO4, new HashMap<>(), new HashMap<>()))
                .thenReturn(printOrderItemDTO);

        // Run the test
        final String result = printServiceImplUnderTest.printKitchen(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo("打印失败，菜品列表为空");
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testPrintKitchen_ItemClientServiceReturnsNoItems() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("8847bc5b-a134-47c7-9404-c8948fa86b6b");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setContactTel("contactTel");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure ItemFeignClient.selectSkuTakeawayInfoRespDTOList(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setParentSkuGuid("parentSkuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        skuTakeawayInfoRespDTO.setItemGuid("itemGuid");
        skuTakeawayInfoRespDTO.setIsRack(0);
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO))
                .thenReturn(skuTakeawayInfoRespDTOS);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        when(mockOrderMapstruct.readToRead(orderReadDO2)).thenReturn(orderReadDO1);

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO3, true)).thenReturn(printTakeoutDTO);

        // Configure ItemClientService.listPkgItemInfo(...).
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        when(mockItemClientService.listPkgItemInfo(itemStringListDTO1)).thenReturn(Collections.emptyList());

        // Configure TakeoutPrintFactory.createKitchenOrderDTO(...).
        final PrintOrderItemDTO printOrderItemDTO = new PrintOrderItemDTO();
        printOrderItemDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printOrderItemDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        printOrderItemDTO.setEstimateDeliveredTimeString("estimateDeliveredTimeString");
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutPrintFactory.createKitchenOrderDTO(orderReadDO4, new HashMap<>(), new HashMap<>()))
                .thenReturn(printOrderItemDTO);

        // Run the test
        final String result = printServiceImplUnderTest.printKitchen(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo("打印失败，菜品列表为空");
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testPrintLabel() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("8847bc5b-a134-47c7-9404-c8948fa86b6b");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setContactTel("contactTel");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure ItemFeignClient.selectSkuTakeawayInfoRespDTOList(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setParentSkuGuid("parentSkuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        skuTakeawayInfoRespDTO.setItemGuid("itemGuid");
        skuTakeawayInfoRespDTO.setIsRack(0);
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO))
                .thenReturn(skuTakeawayInfoRespDTOS);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        when(mockOrderMapstruct.readToRead(orderReadDO2)).thenReturn(orderReadDO1);

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO3, true)).thenReturn(printTakeoutDTO);

        // Configure TakeoutPrintFactory.createLabelLabelDTO(...).
        final PrintLabelDTO printLabelDTO = new PrintLabelDTO();
        printLabelDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printLabelDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        printLabelDTO.setTel("contactTel");
        printLabelDTO.setTradeMode(0);
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutPrintFactory.createLabelLabelDTO(orderReadDO4, new HashMap<>())).thenReturn(printLabelDTO);

        // Run the test
        final String result = printServiceImplUnderTest.printLabel(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo("打印失败，菜品列表为空");
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testPrintLabel_OrgFeignClientReturnsNull() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(null);

        // Configure ItemFeignClient.selectSkuTakeawayInfoRespDTOList(...).
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        skuTakeawayInfoRespDTO.setSkuGuid("skuGuid");
        skuTakeawayInfoRespDTO.setParentSkuGuid("parentSkuGuid");
        skuTakeawayInfoRespDTO.setItemType(0);
        skuTakeawayInfoRespDTO.setItemGuid("itemGuid");
        skuTakeawayInfoRespDTO.setIsRack(0);
        final List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = Arrays.asList(skuTakeawayInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO))
                .thenReturn(skuTakeawayInfoRespDTOS);

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        when(mockOrderMapstruct.readToRead(orderReadDO2)).thenReturn(orderReadDO1);

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO3, true)).thenReturn(printTakeoutDTO);

        // Configure TakeoutPrintFactory.createLabelLabelDTO(...).
        final PrintLabelDTO printLabelDTO = new PrintLabelDTO();
        printLabelDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printLabelDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        printLabelDTO.setTel("contactTel");
        printLabelDTO.setTradeMode(0);
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutPrintFactory.createLabelLabelDTO(orderReadDO4, new HashMap<>())).thenReturn(printLabelDTO);

        // Run the test
        final String result = printServiceImplUnderTest.printLabel(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo("打印失败，菜品列表为空");
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testPrintLabel_ItemFeignClientReturnsNoItems() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setStoreName("name");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderViewId");
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("8847bc5b-a134-47c7-9404-c8948fa86b6b");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setContactTel("contactTel");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure ItemFeignClient.selectSkuTakeawayInfoRespDTOList(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.selectSkuTakeawayInfoRespDTOList(itemStringListDTO))
                .thenReturn(Collections.emptyList());

        // Configure OrderMapstruct.readToRead(...).
        final OrderReadDO orderReadDO1 = new OrderReadDO();
        orderReadDO1.setStoreName("name");
        orderReadDO1.setStoreGuid("storeGuid");
        orderReadDO1.setOrderId("orderId");
        orderReadDO1.setOrderViewId("orderViewId");
        orderReadDO1.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO1.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setItemSku("itemSku");
        itemDO1.setItemName("itemName");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO1.setArrayOfItem(Arrays.asList(itemDO1));
        final OrderReadDO orderReadDO2 = new OrderReadDO();
        orderReadDO2.setStoreName("name");
        orderReadDO2.setStoreGuid("storeGuid");
        orderReadDO2.setOrderId("orderId");
        orderReadDO2.setOrderViewId("orderViewId");
        orderReadDO2.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO2.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO2 = new ItemDO();
        itemDO2.setItemSku("itemSku");
        itemDO2.setItemName("itemName");
        itemDO2.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO2.setArrayOfItem(Arrays.asList(itemDO2));
        when(mockOrderMapstruct.readToRead(orderReadDO2)).thenReturn(orderReadDO1);

        // Configure TakeoutPrintFactory.createBillTakeawayDto(...).
        final PrintTakeoutDTO printTakeoutDTO = new PrintTakeoutDTO();
        printTakeoutDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printTakeoutDTO.setItemRecordList(Arrays.asList(printItemRecord));
        printTakeoutDTO.setPlatform("platform");
        final OrderReadDO orderReadDO3 = new OrderReadDO();
        orderReadDO3.setStoreName("name");
        orderReadDO3.setStoreGuid("storeGuid");
        orderReadDO3.setOrderId("orderId");
        orderReadDO3.setOrderViewId("orderViewId");
        orderReadDO3.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO3.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO3 = new ItemDO();
        itemDO3.setItemSku("itemSku");
        itemDO3.setItemName("itemName");
        itemDO3.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO3.setArrayOfItem(Arrays.asList(itemDO3));
        when(mockTakeoutPrintFactory.createBillTakeawayDto(orderReadDO3, true)).thenReturn(printTakeoutDTO);

        // Configure TakeoutPrintFactory.createLabelLabelDTO(...).
        final PrintLabelDTO printLabelDTO = new PrintLabelDTO();
        printLabelDTO.setInvoiceType(0);
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printLabelDTO.setItemRecordList(Arrays.asList(printItemRecord1));
        printLabelDTO.setTel("contactTel");
        printLabelDTO.setTradeMode(0);
        final OrderReadDO orderReadDO4 = new OrderReadDO();
        orderReadDO4.setStoreName("name");
        orderReadDO4.setStoreGuid("storeGuid");
        orderReadDO4.setOrderId("orderId");
        orderReadDO4.setOrderViewId("orderViewId");
        orderReadDO4.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO4.setAcceptStaffName("acceptStaffName");
        final ItemDO itemDO4 = new ItemDO();
        itemDO4.setItemSku("itemSku");
        itemDO4.setItemName("itemName");
        itemDO4.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO4.setArrayOfItem(Arrays.asList(itemDO4));
        when(mockTakeoutPrintFactory.createLabelLabelDTO(orderReadDO4, new HashMap<>())).thenReturn(printLabelDTO);

        // Run the test
        final String result = printServiceImplUnderTest.printLabel(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo("打印失败，菜品列表为空");
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }
}
