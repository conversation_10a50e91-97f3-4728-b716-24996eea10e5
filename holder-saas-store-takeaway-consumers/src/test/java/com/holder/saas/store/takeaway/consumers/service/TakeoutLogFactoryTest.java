package com.holder.saas.store.takeaway.consumers.service;

import com.holder.saas.store.takeaway.consumers.entity.domain.LogDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

public class TakeoutLogFactoryTest {

    private TakeoutLogFactory takeoutLogFactoryUnderTest;

    @Before
    public void setUp() throws Exception {
        takeoutLogFactoryUnderTest = new TakeoutLogFactory();
    }

    @Test
    public void testCreateCustomerInfo() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createCustomerInfo(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCreated() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderCreated(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderAccepted() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderAccepted(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateDeliveryError() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createDeliveryError(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCanceledAsReject() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderCanceledAsReject(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCanceled() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderCanceled(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderShippingDistribute() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderShippingDistribute(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderShipping() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderShipping(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderShippingCompleted() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderShippingCompleted(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderFinished() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderFinished(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelReq() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderCancelReq(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelCancelReq() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderCancelCancelReq(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelAgreed() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderCancelAgreed(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelDisagreed() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderCancelDisagreed(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelReqArbitrationEffective() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderCancelReqArbitrationEffective(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderRefundReq() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderRefundReq(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelRefundReq() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderCancelRefundReq(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderRefundAgreed() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderRefundAgreed(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderRefundDisagreed() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderRefundDisagreed(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderRefundReqArbitrationEffective() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderRefundReqArbitrationEffective(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderReminded() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setOrderId("orderId");
        orderDO.setOrderViewId("orderId");
        orderDO.setOrderRemark("orderRemark");
        orderDO.setCustomerName("customerName");
        orderDO.setCustomerPhone("customerPhone");
        orderDO.setPrivacyPhone("privacyPhone");
        orderDO.setCustomerAddress("customerAddress");
        orderDO.setShipperName("shipperName");
        orderDO.setShipperPhone("shipperPhone");
        orderDO.setInvoiceTitle("invoiceTitle");
        orderDO.setTaxpayerId("taxpayerId");
        orderDO.setTotal(new BigDecimal("0.00"));
        orderDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderDO.setCustomerRefundItem("customerRefundItem");
        orderDO.setCustomerRefund(new BigDecimal("0.00"));
        orderDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setAcceptStaffName("acceptStaffName");
        orderDO.setAcceptDeviceId("acceptDeviceId");
        orderDO.setAcceptDeviceType(0);
        orderDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReqReason("cancelReqReason");
        orderDO.setCancelReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReplyMessage("cancelReplyMessage");
        orderDO.setCancelReplyStaffName("cancelReplyStaffName");
        orderDO.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReqReason("refundReqReason");
        orderDO.setRefundReplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setRefundReplyMessage("refundReplyMessage");
        orderDO.setRefundReplyStaffName("refundReplyStaffName");
        orderDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDO.setCancelReason("cancelReason");
        orderDO.setCancelStaffName("cancelStaffName");
        orderDO.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final LogDO expectedResult = new LogDO();
        expectedResult.setGuid("d855fbad-0396-4bfe-b387-08128d371ed3");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperator("cancelStaffName");
        expectedResult.setDescription("不同意取消订单");
        expectedResult.setTitle("仲裁信息");
        expectedResult.setBody("body");
        expectedResult.setShowInWebPage(false);
        expectedResult.setShowInEndpoint(false);

        // Run the test
        final LogDO result = takeoutLogFactoryUnderTest.createOrderReminded(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
