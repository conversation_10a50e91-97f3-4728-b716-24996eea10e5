package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.entity.dto.MemberResultDTO;
import com.holder.saas.store.takeaway.consumers.service.rpc.MemberFeignService;
import com.holderzone.saas.store.dto.member.PlatformMemberConsumeRecordDTO;
import com.holderzone.saas.store.dto.member.PlatformMemberDTO;
import com.holderzone.saas.store.dto.takeaway.MtMemberMqReq;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.InetSocketAddress;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtMemberListenerTest {

    @Mock
    private MemberFeignService mockMemberFeignService;

    @InjectMocks
    private MtMemberListener mtMemberListenerUnderTest;

    @Test
    public void testConsumeMsg() {
        // Setup
        final MtMemberMqReq mqReq = new MtMemberMqReq();
        mqReq.setBusinessType(0);
        mqReq.setBody("body");

        final MessageExt messageExt = new MessageExt();
        messageExt.setQueueId(0);
        messageExt.setBornTimestamp(0L);
        messageExt.setBornHost(new InetSocketAddress("localhost", 80));
        messageExt.setStoreTimestamp(0L);
        messageExt.setStoreHost(new InetSocketAddress("localhost", 80));

        // Configure MemberFeignService.judgeAndMember(...).
        final MemberResultDTO<Boolean> booleanMemberResultDTO = new MemberResultDTO<>();
        booleanMemberResultDTO.setCode(0);
        booleanMemberResultDTO.setMessage("message");
        booleanMemberResultDTO.setData(false);
        final PlatformMemberDTO platformMember = new PlatformMemberDTO();
        platformMember.setPhone("phone");
        platformMember.setSourceName("sourceName");
        platformMember.setEnterpriseGuid("enterpriseGuid");
        platformMember.setOperSubjectGuid("operSubjectGuid");
        platformMember.setType(0);
        when(mockMemberFeignService.judgeAndMember(platformMember)).thenReturn(booleanMemberResultDTO);

        // Run the test
        final boolean result = mtMemberListenerUnderTest.consumeMsg(mqReq, messageExt);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm MemberFeignService.createConsumeRecord(...).
        final PlatformMemberConsumeRecordDTO platformMemberConsumeRecordDTO = new PlatformMemberConsumeRecordDTO();
        platformMemberConsumeRecordDTO.setEventType("eventType");
        final PlatformMemberConsumeRecordDTO.EventDetail eventDetail = new PlatformMemberConsumeRecordDTO.EventDetail();
        eventDetail.setUserOpenId("userOpenId");
        eventDetail.setPhone("phone");
        eventDetail.setEnterpriseGuid("enterpriseGuid");
        platformMemberConsumeRecordDTO.setEventDetail(eventDetail);
        verify(mockMemberFeignService).createConsumeRecord(platformMemberConsumeRecordDTO);
    }
}
