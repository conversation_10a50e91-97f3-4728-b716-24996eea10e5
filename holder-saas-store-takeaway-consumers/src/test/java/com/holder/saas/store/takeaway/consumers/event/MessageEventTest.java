package com.holder.saas.store.takeaway.consumers.event;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import javafx.util.Pair;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MessageEventTest {

    @Mock
    private Pair<UserContext, BusinessMessageDTO> mockBusinessMessage;

    private MessageEvent messageEventUnderTest;

    @Before
    public void setUp() throws Exception {
        messageEventUnderTest = new MessageEvent(mockBusinessMessage);
    }
}
