package com.holder.saas.store.takeaway.consumers.service;

import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.print.content.PrintLabelDTO;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintTakeoutDTO;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

public class TakeoutPrintFactoryTest {

    private TakeoutPrintFactory takeoutPrintFactoryUnderTest;

    @Before
    public void setUp() throws Exception {
        takeoutPrintFactoryUnderTest = new TakeoutPrintFactory();
    }

    @Test
    public void testCreateBillTakeawayDto() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setStoreName("storeName");
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderNo");
        orderReadDO.setOrderDaySn("orderDaySn");
        orderReadDO.setOrderRemark("orderRemark");
        orderReadDO.setCustomerName("customerName");
        orderReadDO.setCustomerPhone("customerPhone");
        orderReadDO.setPrivacyPhone("privacyPhone");
        orderReadDO.setCustomerAddress("customerAddress");
        orderReadDO.setRecipientAddressDesensitization("recipientAddressDesensitization");
        orderReadDO.setCustomerNumber(0);
        orderReadDO.setInvoiced(false);
        orderReadDO.setInvoiceTitle("invoiceTitle");
        orderReadDO.setTaxpayerId("taxpayerId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setShipTotal(new BigDecimal("0.00"));
        orderReadDO.setItemTotal(new BigDecimal("0.00"));
        orderReadDO.setPackageTotal(new BigDecimal("0.00"));
        orderReadDO.setEnterpriseDiscount(new BigDecimal("0.00"));
        orderReadDO.setPlatformDiscount(new BigDecimal("0.00"));
        orderReadDO.setOtherDiscount(new BigDecimal("0.00"));
        orderReadDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderReadDO.setOnlinePay(false);
        orderReadDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setAcceptDeviceId("acceptDeviceId");
        orderReadDO.setAcceptDeviceType(0);
        orderReadDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderReadDO.setIsAutoAccept(false);
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setItemUnit("itemUnit");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setItemProperty("itemProperty");
        itemDO.setCartId(0);
        itemDO.setActualPrice(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final PrintTakeoutDTO expectedResult = new PrintTakeoutDTO();
        expectedResult.setInvoiceType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPrintUid("orderNo");
        expectedResult.setOperatorStaffGuid("acceptStaffGuid");
        expectedResult.setOperatorStaffName("acceptStaffName");
        expectedResult.setCreateTime(0L);
        expectedResult.setDeviceId("acceptDeviceId");
        expectedResult.setPrintSourceEnum(PrintSourceEnum.AIO);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setItemGuid("-1");
        printItemRecord.setSkuGuid("erpItemSkuGuid");
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeGuid("-1");
        printItemRecord.setItemTypeName("itemTypeName");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setActualNumber(new BigDecimal("0.00"));
        printItemRecord.setUnit("itemUnit");
        printItemRecord.setActualType(0);
        printItemRecord.setActualPrice(new BigDecimal("0.00"));
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setProperty("itemProperty");
        printItemRecord.setCartId(0);
        expectedResult.setItemRecordList(Arrays.asList(printItemRecord));
        expectedResult.setPlatform("desc");
        expectedResult.setPlatformOrder("platformOrder");
        expectedResult.setStoreName("storeName");
        expectedResult.setPayMsg("payMsg");
        expectedResult.setExpectTime("expectTime");
        expectedResult.setOrderTime(0L);
        expectedResult.setOrderNo("orderNo");
        expectedResult.setRemark("orderRemark");
        expectedResult.setItemTotalPrice(new BigDecimal("0.00"));
        final AdditionalCharge additionalCharge = new AdditionalCharge();
        additionalCharge.setChargeName("chargeName");
        additionalCharge.setChargeValue(new BigDecimal("0.00"));
        expectedResult.setAdditionalChargeList(Arrays.asList(additionalCharge));
        final ReduceRecord reduceRecord = new ReduceRecord();
        reduceRecord.setName("优惠");
        reduceRecord.setAmount(new BigDecimal("0.00"));
        expectedResult.setReduceRecordList(Arrays.asList(reduceRecord));
        expectedResult.setOriginalPrice(new BigDecimal("0.00"));
        expectedResult.setActuallyPay(new BigDecimal("0.00"));
        expectedResult.setReceiverName("receiverName");
        expectedResult.setReceiverTel("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setReceiverAddress("customerAddress");
        expectedResult.setRecipientAddressDesensitization("recipientAddressDesensitization");
        expectedResult.setAbnormal(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setDinnersNumber("dinnersNumber");
        expectedResult.setIsAutoAccept(false);

        // Run the test
        final PrintTakeoutDTO result = takeoutPrintFactoryUnderTest.createBillTakeawayDto(orderReadDO, false);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateKitchenOrderDTO() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setStoreName("storeName");
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderNo");
        orderReadDO.setOrderDaySn("orderDaySn");
        orderReadDO.setOrderRemark("orderRemark");
        orderReadDO.setCustomerName("customerName");
        orderReadDO.setCustomerPhone("customerPhone");
        orderReadDO.setPrivacyPhone("privacyPhone");
        orderReadDO.setCustomerAddress("customerAddress");
        orderReadDO.setRecipientAddressDesensitization("recipientAddressDesensitization");
        orderReadDO.setCustomerNumber(0);
        orderReadDO.setInvoiced(false);
        orderReadDO.setInvoiceTitle("invoiceTitle");
        orderReadDO.setTaxpayerId("taxpayerId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setShipTotal(new BigDecimal("0.00"));
        orderReadDO.setItemTotal(new BigDecimal("0.00"));
        orderReadDO.setPackageTotal(new BigDecimal("0.00"));
        orderReadDO.setEnterpriseDiscount(new BigDecimal("0.00"));
        orderReadDO.setPlatformDiscount(new BigDecimal("0.00"));
        orderReadDO.setOtherDiscount(new BigDecimal("0.00"));
        orderReadDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderReadDO.setOnlinePay(false);
        orderReadDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setAcceptDeviceId("acceptDeviceId");
        orderReadDO.setAcceptDeviceType(0);
        orderReadDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderReadDO.setIsAutoAccept(false);
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setItemUnit("itemUnit");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setItemProperty("itemProperty");
        itemDO.setCartId(0);
        itemDO.setActualPrice(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();
        final Map<String, ItemInfoRespDTO> pkgItemInfoMap = new HashMap<>();
        final PrintOrderItemDTO expectedResult = new PrintOrderItemDTO();
        expectedResult.setInvoiceType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPrintUid("orderNo");
        expectedResult.setOperatorStaffGuid("acceptStaffGuid");
        expectedResult.setOperatorStaffName("acceptStaffName");
        expectedResult.setCreateTime(0L);
        expectedResult.setDeviceId("acceptDeviceId");
        expectedResult.setPrintSourceEnum(PrintSourceEnum.AIO);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setItemGuid("-1");
        printItemRecord.setSkuGuid("erpItemSkuGuid");
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeGuid("-1");
        printItemRecord.setItemTypeName("itemTypeName");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setActualNumber(new BigDecimal("0.00"));
        printItemRecord.setUnit("itemUnit");
        printItemRecord.setActualType(0);
        printItemRecord.setActualPrice(new BigDecimal("0.00"));
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setProperty("itemProperty");
        printItemRecord.setCartId(0);
        expectedResult.setItemRecordList(Arrays.asList(printItemRecord));
        expectedResult.setMarkName("desc");
        expectedResult.setMarkNo("markNo");
        expectedResult.setOrderNo("orderNo");
        expectedResult.setPersonNumber(0);
        expectedResult.setOrderTime(0L);
        expectedResult.setTradeMode(0);
        expectedResult.setEstimateDeliveredTimeString("estimateDeliveredTimeString");
        expectedResult.setRemark("orderRemark");
        expectedResult.setIsAutoAccept(false);

        // Run the test
        final PrintOrderItemDTO result = takeoutPrintFactoryUnderTest.createKitchenOrderDTO(orderReadDO,
                mapOfSkuTakeawayInfoRespDTO, pkgItemInfoMap);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateLabelLabelDTO() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setStoreName("storeName");
        orderReadDO.setEnterpriseGuid("enterpriseGuid");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderId("orderId");
        orderReadDO.setOrderViewId("orderNo");
        orderReadDO.setOrderDaySn("orderDaySn");
        orderReadDO.setOrderRemark("orderRemark");
        orderReadDO.setCustomerName("customerName");
        orderReadDO.setCustomerPhone("customerPhone");
        orderReadDO.setPrivacyPhone("privacyPhone");
        orderReadDO.setCustomerAddress("customerAddress");
        orderReadDO.setRecipientAddressDesensitization("recipientAddressDesensitization");
        orderReadDO.setCustomerNumber(0);
        orderReadDO.setInvoiced(false);
        orderReadDO.setInvoiceTitle("invoiceTitle");
        orderReadDO.setTaxpayerId("taxpayerId");
        orderReadDO.setTotal(new BigDecimal("0.00"));
        orderReadDO.setShipTotal(new BigDecimal("0.00"));
        orderReadDO.setItemTotal(new BigDecimal("0.00"));
        orderReadDO.setPackageTotal(new BigDecimal("0.00"));
        orderReadDO.setEnterpriseDiscount(new BigDecimal("0.00"));
        orderReadDO.setPlatformDiscount(new BigDecimal("0.00"));
        orderReadDO.setOtherDiscount(new BigDecimal("0.00"));
        orderReadDO.setCustomerActualPay(new BigDecimal("0.00"));
        orderReadDO.setOnlinePay(false);
        orderReadDO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderReadDO.setAcceptStaffGuid("acceptStaffGuid");
        orderReadDO.setAcceptStaffName("acceptStaffName");
        orderReadDO.setAcceptDeviceId("acceptDeviceId");
        orderReadDO.setAcceptDeviceType(0);
        orderReadDO.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderReadDO.setIsAutoAccept(false);
        final ItemDO itemDO = new ItemDO();
        itemDO.setItemSku("itemSku");
        itemDO.setItemName("itemName");
        itemDO.setItemUnit("itemUnit");
        itemDO.setItemPrice(new BigDecimal("0.00"));
        itemDO.setItemCount(new BigDecimal("0.00"));
        itemDO.setActualItemCount(new BigDecimal("0.00"));
        itemDO.setItemTotal(new BigDecimal("0.00"));
        itemDO.setItemProperty("itemProperty");
        itemDO.setCartId(0);
        itemDO.setActualPrice(new BigDecimal("0.00"));
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        orderReadDO.setArrayOfItem(Arrays.asList(itemDO));

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuTakeawayInfoRespDTO = new HashMap<>();
        final PrintLabelDTO expectedResult = new PrintLabelDTO();
        expectedResult.setInvoiceType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPrintUid("orderNo");
        expectedResult.setOperatorStaffGuid("acceptStaffGuid");
        expectedResult.setOperatorStaffName("acceptStaffName");
        expectedResult.setCreateTime(0L);
        expectedResult.setDeviceId("acceptDeviceId");
        expectedResult.setPrintSourceEnum(PrintSourceEnum.AIO);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setItemGuid("-1");
        printItemRecord.setSkuGuid("erpItemSkuGuid");
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeGuid("-1");
        printItemRecord.setItemTypeName("itemTypeName");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setActualNumber(new BigDecimal("0.00"));
        printItemRecord.setUnit("itemUnit");
        printItemRecord.setActualType(0);
        printItemRecord.setActualPrice(new BigDecimal("0.00"));
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setProperty("itemProperty");
        printItemRecord.setCartId(0);
        expectedResult.setItemRecordList(Arrays.asList(printItemRecord));
        expectedResult.setStoreName("storeName");
        expectedResult.setSerialNumber("serialNumber");
        expectedResult.setTradeMode(0);
        expectedResult.setRemark("orderRemark");

        // Run the test
        final PrintLabelDTO result = takeoutPrintFactoryUnderTest.createLabelLabelDTO(orderReadDO,
                mapOfSkuTakeawayInfoRespDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFillTakeoutItemTypeName() {
        // Setup
        final PrintTakeoutDTO billTakeawayDTO = new PrintTakeoutDTO();
        billTakeawayDTO.setInvoiceType(0);
        billTakeawayDTO.setEnterpriseGuid("enterpriseGuid");
        billTakeawayDTO.setStoreGuid("storeGuid");
        billTakeawayDTO.setPrintUid("orderNo");
        billTakeawayDTO.setOperatorStaffGuid("acceptStaffGuid");
        billTakeawayDTO.setOperatorStaffName("acceptStaffName");
        billTakeawayDTO.setCreateTime(0L);
        billTakeawayDTO.setDeviceId("acceptDeviceId");
        billTakeawayDTO.setPrintSourceEnum(PrintSourceEnum.AIO);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setItemGuid("-1");
        printItemRecord.setSkuGuid("erpItemSkuGuid");
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeGuid("-1");
        printItemRecord.setItemTypeName("itemTypeName");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setActualNumber(new BigDecimal("0.00"));
        printItemRecord.setUnit("itemUnit");
        printItemRecord.setActualType(0);
        printItemRecord.setActualPrice(new BigDecimal("0.00"));
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setProperty("itemProperty");
        printItemRecord.setCartId(0);
        billTakeawayDTO.setItemRecordList(Arrays.asList(printItemRecord));
        billTakeawayDTO.setPlatform("desc");
        billTakeawayDTO.setPlatformOrder("platformOrder");
        billTakeawayDTO.setStoreName("storeName");
        billTakeawayDTO.setPayMsg("payMsg");
        billTakeawayDTO.setExpectTime("expectTime");
        billTakeawayDTO.setOrderTime(0L);
        billTakeawayDTO.setOrderNo("orderNo");
        billTakeawayDTO.setRemark("orderRemark");
        billTakeawayDTO.setItemTotalPrice(new BigDecimal("0.00"));
        final AdditionalCharge additionalCharge = new AdditionalCharge();
        additionalCharge.setChargeName("chargeName");
        additionalCharge.setChargeValue(new BigDecimal("0.00"));
        billTakeawayDTO.setAdditionalChargeList(Arrays.asList(additionalCharge));
        final ReduceRecord reduceRecord = new ReduceRecord();
        reduceRecord.setName("优惠");
        reduceRecord.setAmount(new BigDecimal("0.00"));
        billTakeawayDTO.setReduceRecordList(Arrays.asList(reduceRecord));
        billTakeawayDTO.setOriginalPrice(new BigDecimal("0.00"));
        billTakeawayDTO.setActuallyPay(new BigDecimal("0.00"));
        billTakeawayDTO.setReceiverName("receiverName");
        billTakeawayDTO.setReceiverTel("customerPhone");
        billTakeawayDTO.setPrivacyPhone("privacyPhone");
        billTakeawayDTO.setReceiverAddress("customerAddress");
        billTakeawayDTO.setRecipientAddressDesensitization("recipientAddressDesensitization");
        billTakeawayDTO.setAbnormal(false);
        billTakeawayDTO.setInvoiced(false);
        billTakeawayDTO.setInvoiceTitle("invoiceTitle");
        billTakeawayDTO.setTaxpayerId("taxpayerId");
        billTakeawayDTO.setDinnersNumber("dinnersNumber");
        billTakeawayDTO.setIsAutoAccept(false);

        final Map<String, SkuTakeawayInfoRespDTO> mapOfSkuPartMap = new HashMap<>();

        // Run the test
        takeoutPrintFactoryUnderTest.fillTakeoutItemTypeName(billTakeawayDTO, mapOfSkuPartMap);

        // Verify the results
    }
}
