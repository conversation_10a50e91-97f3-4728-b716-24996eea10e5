package com.holder.saas.store.takeaway.consumers.builder;

import com.holder.saas.store.takeaway.consumers.entity.bo.FixItemBiz;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.FixRecordDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixItemDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixRecordDTO;
import com.holderzone.saas.store.dto.takeaway.UnItemBindUnbindReq;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class FixItemBizBuilderTest {

    @Test
    public void testBuild1() {
        // Setup
        final FixItemBiz expectedResult = new FixItemBiz();
        expectedResult.setCommitFlag(false);
        expectedResult.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        expectedResult.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("thirdSkuId");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("erpItemSkuGuid");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setItemGuid("itemGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        expectedResult.setItemList(Arrays.asList(fixItemDO));

        // Run the test
        final FixItemBiz result = FixItemBizBuilder.build();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBuild2() {
        // Setup
        final TakeoutFixDTO takeoutFixDTO = new TakeoutFixDTO();
        final TakeoutFixRecordDTO recordReqDTO = new TakeoutFixRecordDTO();
        recordReqDTO.setRecordId(0L);
        takeoutFixDTO.setRecordReqDTO(recordReqDTO);
        final TakeoutFixItemDTO takeoutFixItemDTO = new TakeoutFixItemDTO();
        takeoutFixItemDTO.setErpItemGuid("erpItemGuid");
        takeoutFixDTO.setItemReqDTOList(Arrays.asList(takeoutFixItemDTO));
        takeoutFixDTO.setCommitFlag(false);
        takeoutFixDTO.setFixBindFlag(false);

        final FixItemBiz expectedResult = new FixItemBiz();
        expectedResult.setCommitFlag(false);
        expectedResult.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        expectedResult.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("thirdSkuId");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("erpItemSkuGuid");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setItemGuid("itemGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        expectedResult.setItemList(Arrays.asList(fixItemDO));

        // Run the test
        final FixItemBiz result = FixItemBizBuilder.build(takeoutFixDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBuild3() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("erpItemSkuGuid");
        unItemBindUnbindReq.setUnItemCountMapper(0);
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);

        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setItemName("itemName");
        itemDO.setThirdSkuId("thirdSkuId");
        final List<ItemDO> itemDOList = Arrays.asList(itemDO);
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO));

        final FixItemBiz expectedResult = new FixItemBiz();
        expectedResult.setCommitFlag(false);
        expectedResult.setFixBindFlag(false);
        final FixRecordDO record = new FixRecordDO();
        record.setId(0L);
        record.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setFixCount(0);
        record.setStatus(0);
        expectedResult.setRecord(record);
        final FixItemDO fixItemDO = new FixItemDO();
        fixItemDO.setStoreGuid("storeGuid");
        fixItemDO.setStoreName("storeName");
        fixItemDO.setOrderSubType(0);
        fixItemDO.setTakeoutItemName("itemName");
        fixItemDO.setThirdSkuId("thirdSkuId");
        fixItemDO.setErpItemGuid("erpItemGuid");
        fixItemDO.setErpItemSkuGuid("erpItemSkuGuid");
        fixItemDO.setErpItemName("erpItemName");
        fixItemDO.setErpItemPrice(new BigDecimal("0.00"));
        fixItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        fixItemDO.setErpItemCount(new BigDecimal("0.00"));
        fixItemDO.setTakeoutOrderCount(0);
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setItemGuid("itemGuid");
        fixItemDO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        expectedResult.setItemList(Arrays.asList(fixItemDO));

        // Run the test
        final FixItemBiz result = FixItemBizBuilder.build(unItemBindUnbindReq, itemDOList, skuInfoRespDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
