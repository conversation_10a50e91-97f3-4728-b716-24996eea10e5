package com.holder.saas.store.takeaway.consumers.controller;

import com.alibaba.fastjson.JSON;
import com.holder.saas.store.takeaway.consumers.HolderSaasStoreTakeawayConsumersApplication;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemMappingReqDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @description 外卖映射控制器测试接口
 * @date 2022/5/10 14:31
 * @className: TakeoutMappingControllerTest
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreTakeawayConsumersApplication.class)
public class TakeoutMappingControllerTest {

    private static final String userInfo = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"6619160595813892096\",\"storeName\": \"听雨阁\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";

    private static final String ITEM_MAPPING = "/item_mapping";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void getItemBinding() throws Exception{
        TakeoutItemMappingReqDTO query = new TakeoutItemMappingReqDTO();
        query.setStoreGuid("2104211624474030006");
        query.setTakeoutType(0);
        String jsonString = JSON.toJSONString(query);
        MvcResult mvcResult = mockMvc.perform(post(ITEM_MAPPING + "/query")
                .header(USER_INFO, userInfo)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }
}