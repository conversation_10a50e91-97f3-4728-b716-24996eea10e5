package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TakeoutPrintController.class)
public class TakeoutPrintControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OrderService mockOrderService;

    @Test
    public void testPrintBill() throws Exception {
        // Setup
        // Configure OrderService.printBill(...).
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setShopId(0L);
        takeoutOrderDTO.setShopName("shopName");
        takeoutOrderDTO.setOrderGuid("orderGuid");
        takeoutOrderDTO.setOrderStatus(0);
        takeoutOrderDTO.setOrderTagStatus(0);
        when(mockOrderService.printBill(takeoutOrderDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/print_bill")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPrintKitchen() throws Exception {
        // Setup
        // Configure OrderService.printKitchen(...).
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setShopId(0L);
        takeoutOrderDTO.setShopName("shopName");
        takeoutOrderDTO.setOrderGuid("orderGuid");
        takeoutOrderDTO.setOrderStatus(0);
        takeoutOrderDTO.setOrderTagStatus(0);
        when(mockOrderService.printKitchen(takeoutOrderDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/print_kitchen")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPrintLabel() throws Exception {
        // Setup
        // Configure OrderService.printLabel(...).
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setShopId(0L);
        takeoutOrderDTO.setShopName("shopName");
        takeoutOrderDTO.setOrderGuid("orderGuid");
        takeoutOrderDTO.setOrderStatus(0);
        takeoutOrderDTO.setOrderTagStatus(0);
        when(mockOrderService.printLabel(takeoutOrderDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/print_label")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
