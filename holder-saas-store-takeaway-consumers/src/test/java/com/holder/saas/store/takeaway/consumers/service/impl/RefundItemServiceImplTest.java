package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.entity.domain.RefundItemDO;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class RefundItemServiceImplTest {

    private RefundItemServiceImpl refundItemServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        refundItemServiceImplUnderTest = new RefundItemServiceImpl();
    }

    @Test
    public void testListItemGroupByOrderItemGuid() {
        // Setup
        final RefundItemDO refundItemDO = new RefundItemDO();
        refundItemDO.setId(0L);
        refundItemDO.setRefundGuid("refundGuid");
        refundItemDO.setOrderGuid("orderGuid");
        refundItemDO.setOrderItemGuid("orderItemGuid");
        refundItemDO.setOrderSubType(0);
        final List<RefundItemDO> expectedResult = Arrays.asList(refundItemDO);

        // Run the test
        final List<RefundItemDO> result = refundItemServiceImplUnderTest.listItemGroupByOrderItemGuid("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateRefundSuccess() {
        // Setup
        // Run the test
        refundItemServiceImplUnderTest.updateRefundSuccess("orderGuid");

        // Verify the results
    }

    @Test
    public void testRemoveByOrderGuid() {
        // Setup
        // Run the test
        refundItemServiceImplUnderTest.removeByOrderGuid("orderGuid");

        // Verify the results
    }
}
