package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class DeliveryServiceImplTest {

    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;

    private DeliveryServiceImpl deliveryServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        deliveryServiceImplUnderTest = new DeliveryServiceImpl(mockDefaultRocketMqProducer);
    }

    @Test
    public void testDoDelivery() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setShopName("shopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);

        // Run the test
        deliveryServiceImplUnderTest.doDelivery(unOrder);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }
}
