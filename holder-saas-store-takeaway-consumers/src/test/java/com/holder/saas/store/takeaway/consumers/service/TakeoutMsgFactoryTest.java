package com.holder.saas.store.takeaway.consumers.service;

import com.holder.saas.store.takeaway.consumers.entity.domain.OrderDO;
import com.holder.saas.store.takeaway.consumers.entity.read.OrderReadDO;
import com.holder.saas.store.takeaway.consumers.service.rpc.OrgFeignClient;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TakeoutMsgFactoryTest {

    @Mock
    private ConfigService mockConfigService;
    @Mock
    private OrgFeignClient mockOrgFeignClient;

    private TakeoutMsgFactory takeoutMsgFactoryUnderTest;

    @Before
    public void setUp() throws Exception {
        takeoutMsgFactoryUnderTest = new TakeoutMsgFactory(mockOrgFeignClient);
    }

    @Test
    public void testCreateOrderCreated() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Configure OrgFeignClient.findMasterDevice(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        when(mockOrgFeignClient.findMasterDevice("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderCreated(orderDO, false, false);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderConfirmed() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderConfirmed(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderShipping() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderShipping(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderFinished() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderFinished(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCanceled() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderCanceled(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderReminded() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderReminded(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderDiningOut() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderDiningOut(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateDeliveryError() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createDeliveryError(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelReq() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderCancelReq(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelCancelReq() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderCancelCancelReq(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelAgreed() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderCancelAgreed(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelDisagreed() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderCancelDisagreed(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderRefundReq() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderRefundReq(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderCancelRefundReq() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderCancelRefundReq(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderRefundAgreed() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderRefundAgreed(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderRefundDisagreed() {
        // Setup
        final OrderDO orderDO = new OrderDO();
        orderDO.setOrderGuid("orderGuid");
        orderDO.setOrderSubType(0);
        orderDO.setStoreName("storeName");
        orderDO.setStoreGuid("storeGuid");
        orderDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderRefundDisagreed(orderDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderMappingFailed() {
        // Setup
        final OrderReadDO orderReadDO = new OrderReadDO();
        orderReadDO.setOrderGuid("orderGuid");
        orderReadDO.setOrderSubType(0);
        orderReadDO.setStoreName("storeName");
        orderReadDO.setStoreGuid("storeGuid");
        orderReadDO.setOrderDaySn("orderDaySn");

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .subject("subject")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build();

        // Run the test
        final BusinessMessageDTO result = takeoutMsgFactoryUnderTest.createOrderMappingFailed(orderReadDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
