package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.entity.domain.AutoRecoveryDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class AutoRecoveryServiceImplTest {

    private AutoRecoveryServiceImpl autoRecoveryServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        autoRecoveryServiceImplUnderTest = new AutoRecoveryServiceImpl();
    }

    @Test
    public void testQueryLimit() {
        // Setup
        final AutoRecoveryDO autoRecoveryDO = new AutoRecoveryDO();
        autoRecoveryDO.setId("id");
        autoRecoveryDO.setOrderGuid("orderGuid");
        autoRecoveryDO.setExSource("exSource");
        autoRecoveryDO.setExMsg("exMsg");
        autoRecoveryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<AutoRecoveryDO> expectedResult = Arrays.asList(autoRecoveryDO);

        // Run the test
        final List<AutoRecoveryDO> result = autoRecoveryServiceImplUnderTest.queryLimit();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateOrderEx() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setStoreName("storeName");
        itemDO.setOrderGuid("orderGuid");
        itemDO.setItemGuid("id");
        final List<ItemDO> itemList = Arrays.asList(itemDO);

        // Run the test
        autoRecoveryServiceImplUnderTest.createOrderEx(itemList, "exMsg");

        // Verify the results
    }
}
