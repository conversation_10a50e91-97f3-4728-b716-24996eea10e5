package com.holder.saas.store.takeaway.consumers.task;

import com.holder.saas.store.takeaway.consumers.entity.domain.AutoRecoveryDO;
import com.holder.saas.store.takeaway.consumers.manage.AutoRecoveryManager;
import com.holder.saas.store.takeaway.consumers.service.AutoRecoveryService;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AutoRecoveryTaskTest {

    @Mock
    private AutoRecoveryService mockAutoRecoveryService;
    @Mock
    private AutoRecoveryManager mockAutoRecoveryManager;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private RedisTemplate<String, String> mockRedisTemplate;

    private AutoRecoveryTask autoRecoveryTaskUnderTest;

    @Before
    public void setUp() throws Exception {
        autoRecoveryTaskUnderTest = new AutoRecoveryTask(mockAutoRecoveryService, mockAutoRecoveryManager,
                mockDynamicHelper, mockRedisTemplate);
        ReflectionTestUtils.setField(autoRecoveryTaskUnderTest, "fixEnterpriseGuid", "fixEnterpriseGuid");
    }

    @Test
    public void testFixExItem() {
        // Setup
        when(mockRedisTemplate.hasKey(AutoRecoveryTask.AUTO_RECOVERY_KEY)).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure AutoRecoveryService.queryLimit(...).
        final AutoRecoveryDO autoRecoveryDO = new AutoRecoveryDO();
        autoRecoveryDO.setId("id");
        autoRecoveryDO.setOrderGuid("orderGuid");
        autoRecoveryDO.setExSource("exSource");
        autoRecoveryDO.setExMsg("exMsg");
        autoRecoveryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<AutoRecoveryDO> autoRecoveryDOS = Arrays.asList(autoRecoveryDO);
        when(mockAutoRecoveryService.queryLimit()).thenReturn(autoRecoveryDOS);

        // Run the test
        autoRecoveryTaskUnderTest.fixExItem();

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("fixEnterpriseGuid");

        // Confirm AutoRecoveryManager.fixItem(...).
        final AutoRecoveryDO autoRecoveryDO1 = new AutoRecoveryDO();
        autoRecoveryDO1.setId("id");
        autoRecoveryDO1.setOrderGuid("orderGuid");
        autoRecoveryDO1.setExSource("exSource");
        autoRecoveryDO1.setExMsg("exMsg");
        autoRecoveryDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<AutoRecoveryDO> recoveryRecords = Arrays.asList(autoRecoveryDO1);
        verify(mockAutoRecoveryManager).fixItem(recoveryRecords);
        verify(mockRedisTemplate).delete(AutoRecoveryTask.AUTO_RECOVERY_KEY);
    }

    @Test
    public void testFixExItem_RedisTemplateHasKeyReturnsNull() {
        // Setup
        when(mockRedisTemplate.hasKey(AutoRecoveryTask.AUTO_RECOVERY_KEY)).thenReturn(null);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure AutoRecoveryService.queryLimit(...).
        final AutoRecoveryDO autoRecoveryDO = new AutoRecoveryDO();
        autoRecoveryDO.setId("id");
        autoRecoveryDO.setOrderGuid("orderGuid");
        autoRecoveryDO.setExSource("exSource");
        autoRecoveryDO.setExMsg("exMsg");
        autoRecoveryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<AutoRecoveryDO> autoRecoveryDOS = Arrays.asList(autoRecoveryDO);
        when(mockAutoRecoveryService.queryLimit()).thenReturn(autoRecoveryDOS);

        // Run the test
        autoRecoveryTaskUnderTest.fixExItem();

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("fixEnterpriseGuid");

        // Confirm AutoRecoveryManager.fixItem(...).
        final AutoRecoveryDO autoRecoveryDO1 = new AutoRecoveryDO();
        autoRecoveryDO1.setId("id");
        autoRecoveryDO1.setOrderGuid("orderGuid");
        autoRecoveryDO1.setExSource("exSource");
        autoRecoveryDO1.setExMsg("exMsg");
        autoRecoveryDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<AutoRecoveryDO> recoveryRecords = Arrays.asList(autoRecoveryDO1);
        verify(mockAutoRecoveryManager).fixItem(recoveryRecords);
        verify(mockRedisTemplate).delete(AutoRecoveryTask.AUTO_RECOVERY_KEY);
    }

    @Test
    public void testFixExItem_RedisTemplateHasKeyReturnsTrue() {
        // Setup
        when(mockRedisTemplate.hasKey(AutoRecoveryTask.AUTO_RECOVERY_KEY)).thenReturn(true);

        // Run the test
        autoRecoveryTaskUnderTest.fixExItem();

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("fixEnterpriseGuid");
    }

    @Test
    public void testFixExItem_AutoRecoveryServiceReturnsNoItems() {
        // Setup
        when(mockRedisTemplate.hasKey(AutoRecoveryTask.AUTO_RECOVERY_KEY)).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockAutoRecoveryService.queryLimit()).thenReturn(Collections.emptyList());

        // Run the test
        autoRecoveryTaskUnderTest.fixExItem();

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("fixEnterpriseGuid");
        verify(mockRedisTemplate).delete(AutoRecoveryTask.AUTO_RECOVERY_KEY);
    }
}
