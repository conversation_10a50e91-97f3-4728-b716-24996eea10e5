package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.orderlog.OrderLogMqDTO;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RocketMqServiceImplTest {

    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;

    private RocketMqServiceImpl rocketMqServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        rocketMqServiceImplUnderTest = new RocketMqServiceImpl(mockDefaultRocketMqProducer);
    }

    @Test
    public void testSendUnOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setReplyMsgType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setOrderId("orderId");

        when(mockDefaultRocketMqProducer.sendMessage(any(Message.class))).thenReturn(false);

        // Run the test
        rocketMqServiceImplUnderTest.sendUnOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testSendUnOrderLog() {
        // Setup
        final OrderLogMqDTO orderLogMqDTO = new OrderLogMqDTO();
        orderLogMqDTO.setOperationType(0);
        orderLogMqDTO.setOrderGuid("orderGuid");
        orderLogMqDTO.setOrderSource(0);
        orderLogMqDTO.setTradeMode(0);
        orderLogMqDTO.setOperationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockDefaultRocketMqProducer.sendMessage(any(Message.class))).thenReturn(false);

        // Run the test
        rocketMqServiceImplUnderTest.sendUnOrderLog(orderLogMqDTO, "enterpriseGuid");

        // Verify the results
    }
}
