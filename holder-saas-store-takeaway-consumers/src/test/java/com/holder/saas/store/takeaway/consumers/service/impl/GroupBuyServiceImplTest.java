package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.service.rpc.ProducerFeignClient;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GroupBuyServiceImplTest {

    @Mock
    private ProducerFeignClient mockProducerFeignClient;

    private GroupBuyServiceImpl groupBuyServiceImplUnderTest;

    @Before
    public void setUp(){
        groupBuyServiceImplUnderTest = new GroupBuyServiceImpl(mockProducerFeignClient);
    }

    @Test
    public void testCheckTicket() {
        // Setup
        final CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
        couPonReqDTO.setCouponCode("couponCode");
        couPonReqDTO.setCount(0);
        couPonReqDTO.setErpId("erpId");
        couPonReqDTO.setErpName("erpName");
        couPonReqDTO.setGroupBuyType(0);

        final MtCouponDoCheckRespDTO expectedResult = new MtCouponDoCheckRespDTO();
        expectedResult.setOrderId("orderId");
        expectedResult.setCouponCodes(Arrays.asList("value"));
        expectedResult.setDealTitle("dealTitle");
        expectedResult.setDealValue(0.0);
        expectedResult.setDealId(0);

        // Configure ProducerFeignClient.checkTicket(...).
        final MtCouponDoCheckRespDTO mtCouponDoCheckRespDTO = new MtCouponDoCheckRespDTO();
        mtCouponDoCheckRespDTO.setOrderId("orderId");
        mtCouponDoCheckRespDTO.setCouponCodes(Arrays.asList("value"));
        mtCouponDoCheckRespDTO.setDealTitle("dealTitle");
        mtCouponDoCheckRespDTO.setDealValue(0.0);
        mtCouponDoCheckRespDTO.setDealId(0);
        final CouPonReqDTO couPonReqDTO1 = new CouPonReqDTO();
        couPonReqDTO1.setCouponCode("couponCode");
        couPonReqDTO1.setCount(0);
        couPonReqDTO1.setErpId("erpId");
        couPonReqDTO1.setErpName("erpName");
        couPonReqDTO1.setGroupBuyType(0);
        when(mockProducerFeignClient.checkTicket(couPonReqDTO1)).thenReturn(mtCouponDoCheckRespDTO);

        // Run the test
        final MtCouponDoCheckRespDTO result = groupBuyServiceImplUnderTest.checkTicket(couPonReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDoCheck() {
        // Setup
        final CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
        couPonReqDTO.setCouponCode("couponCode");
        couPonReqDTO.setCount(0);
        couPonReqDTO.setErpId("erpId");
        couPonReqDTO.setErpName("erpName");
        couPonReqDTO.setGroupBuyType(0);

        final MtCouponDoCheckRespDTO expectedResult = new MtCouponDoCheckRespDTO();
        expectedResult.setOrderId("orderId");
        expectedResult.setCouponCodes(Arrays.asList("value"));
        expectedResult.setDealTitle("dealTitle");
        expectedResult.setDealValue(0.0);
        expectedResult.setDealId(0);

        // Configure ProducerFeignClient.doCheck(...).
        final MtCouponDoCheckRespDTO mtCouponDoCheckRespDTO = new MtCouponDoCheckRespDTO();
        mtCouponDoCheckRespDTO.setOrderId("orderId");
        mtCouponDoCheckRespDTO.setCouponCodes(Arrays.asList("value"));
        mtCouponDoCheckRespDTO.setDealTitle("dealTitle");
        mtCouponDoCheckRespDTO.setDealValue(0.0);
        mtCouponDoCheckRespDTO.setDealId(0);
        final CouPonReqDTO couPonReqDTO1 = new CouPonReqDTO();
        couPonReqDTO1.setCouponCode("couponCode");
        couPonReqDTO1.setCount(0);
        couPonReqDTO1.setErpId("erpId");
        couPonReqDTO1.setErpName("erpName");
        couPonReqDTO1.setGroupBuyType(0);
        when(mockProducerFeignClient.doCheck(couPonReqDTO1)).thenReturn(mtCouponDoCheckRespDTO);

        // Run the test
        final MtCouponDoCheckRespDTO result = groupBuyServiceImplUnderTest.doCheck(couPonReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPreCheck() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setHasUseActivity(false);

        final MtCouponPreRespDTO expectedResult = new MtCouponPreRespDTO();
        expectedResult.setCount(0);
        expectedResult.setCouponBuyPrice(0.0);
        expectedResult.setCouponCode("couponCode");
        expectedResult.setIsVoucher(false);
        expectedResult.setCouponEndTime("couponEndTime");

        // Configure ProducerFeignClient.preCheck(...).
        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setIsVoucher(false);
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        final CouPonPreReqDTO couPonPreReqDTO1 = new CouPonPreReqDTO();
        couPonPreReqDTO1.setCouponCode("couponCode");
        couPonPreReqDTO1.setErpOrderId("erpOrderId");
        couPonPreReqDTO1.setActivityGuid("activityGuid");
        couPonPreReqDTO1.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO1.setHasUseActivity(false);
        when(mockProducerFeignClient.preCheck(couPonPreReqDTO1)).thenReturn(mtCouponPreRespDTO);

        // Run the test
        final MtCouponPreRespDTO result = groupBuyServiceImplUnderTest.preCheck(couPonPreReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCancalTicket() {
        // Setup
        final CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setCouponCode("couponCode");
        couponDelReqDTO.setErpId("erpId");
        couponDelReqDTO.setErpName("erpName");

        final MtDelCouponRespDTO expectedResult = new MtDelCouponRespDTO(0, "message");

        // Configure ProducerFeignClient.cancelTicket(...).
        final CouponDelReqDTO couponDelReqDTO1 = new CouponDelReqDTO();
        couponDelReqDTO1.setCouponCode("couponCode");
        couponDelReqDTO1.setErpId("erpId");
        couponDelReqDTO1.setErpName("erpName");
        when(mockProducerFeignClient.cancelTicket(couponDelReqDTO1)).thenReturn(new MtDelCouponRespDTO(0, "message"));

        // Run the test
        final MtDelCouponRespDTO result = groupBuyServiceImplUnderTest.cancalTicket(couponDelReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCancalTicket_ProducerFeignClientReturnsError() {
        // Setup
        final CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setCouponCode("couponCode");
        couponDelReqDTO.setErpId("erpId");
        couponDelReqDTO.setErpName("erpName");

        final MtDelCouponRespDTO expectedResult = new MtDelCouponRespDTO(0, "message");

        // Configure ProducerFeignClient.cancelTicket(...).
        final CouponDelReqDTO couponDelReqDTO1 = new CouponDelReqDTO();
        couponDelReqDTO1.setCouponCode("couponCode");
        couponDelReqDTO1.setErpId("erpId");
        couponDelReqDTO1.setErpName("erpName");
        when(mockProducerFeignClient.cancelTicket(couponDelReqDTO1))
                .thenReturn(MtDelCouponRespDTO.buildError("message"));

        // Run the test
        final MtDelCouponRespDTO result = groupBuyServiceImplUnderTest.cancalTicket(couponDelReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryGroupTradeDetail() {
        // Setup
        final CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
        couPonReqDTO.setCouponCode("couponCode");
        couPonReqDTO.setCount(0);
        couPonReqDTO.setErpId("erpId");
        couPonReqDTO.setErpName("erpName");
        couPonReqDTO.setGroupBuyType(0);

        final MtCouponTradeDetailRespDTO expectedResult = new MtCouponTradeDetailRespDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setMtStoreGuid("mtStoreGuid");
        expectedResult.setMtStoreName("mtStoreName");
        expectedResult.setBizCost(0.0);
        expectedResult.setBuyPrice(0.0);

        // Configure ProducerFeignClient.queryGroupTradeDetail(...).
        final MtCouponTradeDetailRespDTO mtCouponTradeDetailRespDTO = new MtCouponTradeDetailRespDTO();
        mtCouponTradeDetailRespDTO.setStoreGuid("storeGuid");
        mtCouponTradeDetailRespDTO.setMtStoreGuid("mtStoreGuid");
        mtCouponTradeDetailRespDTO.setMtStoreName("mtStoreName");
        mtCouponTradeDetailRespDTO.setBizCost(0.0);
        mtCouponTradeDetailRespDTO.setBuyPrice(0.0);
        final CouPonReqDTO couPonReqDTO1 = new CouPonReqDTO();
        couPonReqDTO1.setCouponCode("couponCode");
        couPonReqDTO1.setCount(0);
        couPonReqDTO1.setErpId("erpId");
        couPonReqDTO1.setErpName("erpName");
        couPonReqDTO1.setGroupBuyType(0);
        when(mockProducerFeignClient.queryGroupTradeDetail(couPonReqDTO1)).thenReturn(mtCouponTradeDetailRespDTO);

        // Run the test
        final MtCouponTradeDetailRespDTO result = groupBuyServiceImplUnderTest.queryGroupTradeDetail(couPonReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
