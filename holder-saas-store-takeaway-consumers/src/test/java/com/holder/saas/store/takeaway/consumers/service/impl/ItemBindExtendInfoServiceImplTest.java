package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holder.saas.store.takeaway.consumers.entity.domain.ItemBindExtendInfoDo;
import com.holderzone.saas.store.dto.takeaway.TcdSyncItemMappingDTO;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class ItemBindExtendInfoServiceImplTest {

    private ItemBindExtendInfoServiceImpl itemBindExtendInfoServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        itemBindExtendInfoServiceImplUnderTest = new ItemBindExtendInfoServiceImpl();
    }

    @Test
    public void testSelectByStoreGuidsAndTakeoutType() {
        // Setup
        final ItemBindExtendInfoDo itemBindExtendInfoDo = new ItemBindExtendInfoDo();
        itemBindExtendInfoDo.setId(0L);
        itemBindExtendInfoDo.setStoreGuid("storeGuid");
        itemBindExtendInfoDo.setTakeoutType(0);
        itemBindExtendInfoDo.setErpItemSkuId("erpItemSkuId");
        itemBindExtendInfoDo.setUnItemCountMapper(0);
        final List<ItemBindExtendInfoDo> expectedResult = Arrays.asList(itemBindExtendInfoDo);

        // Run the test
        final List<ItemBindExtendInfoDo> result = itemBindExtendInfoServiceImplUnderTest.selectByStoreGuidsAndTakeoutType(
                Arrays.asList("value"), 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSyncTcdItemMappingCount() {
        // Setup
        final TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping tcdItemMapping = new TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping();
        tcdItemMapping.setSourceUnItemSkuId("sourceUnItemSkuId");
        tcdItemMapping.setTargetUnItemSkuId("targetUnItemSkuId");
        tcdItemMapping.setSyncUnItemSkuId("syncUnItemSkuId");
        final List<TcdSyncItemMappingDTO.TcdStoreMapping.TcdItemMapping> tcdItemMappingList = Arrays.asList(
                tcdItemMapping);

        // Run the test
        itemBindExtendInfoServiceImplUnderTest.syncTcdItemMappingCount(tcdItemMappingList);

        // Verify the results
    }
}
