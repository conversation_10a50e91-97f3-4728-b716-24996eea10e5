package com.holder.saas.store.takeaway.consumers.event;

import com.holder.saas.store.takeaway.consumers.service.rpc.ErpFeignService;
import com.holder.saas.store.takeaway.consumers.utils.DynamicHelper;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.InetSocketAddress;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class ErpListenerTest {

    @Mock
    private ErpFeignService mockErpFeignService;
    @Mock
    private DynamicHelper mockDynamicHelper;

    private ErpListener erpListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        erpListenerUnderTest = new ErpListener(mockErpFeignService, mockDynamicHelper);
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setOut(false);
        orderSkuDTO.setEnterpriseGuid("enterpriseGuid");
        orderSkuDTO.setOperatorGuid("operatorGuid");
        orderSkuDTO.setOperatorName("operatorName");
        orderSkuDTO.setOrderId("orderId");

        final MessageExt messageExt = new MessageExt();
        messageExt.setQueueId(0);
        messageExt.setBornTimestamp(0L);
        messageExt.setBornHost(new InetSocketAddress("localhost", 80));
        messageExt.setStoreTimestamp(0L);
        messageExt.setStoreHost(new InetSocketAddress("localhost", 80));

        // Run the test
        final boolean result = erpListenerUnderTest.consumeMsg(orderSkuDTO, messageExt);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockErpFeignService).reduceStockForOrder(any(OrderSkuDTO.class));
        verify(mockDynamicHelper).removeThreadLocalDatabaseInfo();
    }
}
