package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.request.BusinessTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(BusinessOrderReportController.class)
public class BusinessOrderReportControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OrderService mockOrderService;

    @Test
    public void testGetTakeoutOrderPage() throws Exception {
        // Setup
        // Configure OrderService.getTakeoutOrderPage(...).
        final BusinessTakeoutOrderRespDTO businessTakeoutOrderRespDTO = new BusinessTakeoutOrderRespDTO();
        businessTakeoutOrderRespDTO.setGuid("46594894-e7d3-4bf6-b5d7-209e945ca291");
        businessTakeoutOrderRespDTO.setStoreName("storeName");
        businessTakeoutOrderRespDTO.setOrderNo("orderNo");
        businessTakeoutOrderRespDTO.setOrderState("orderState");
        businessTakeoutOrderRespDTO.setOrderSource("orderSource");
        final Page<BusinessTakeoutOrderRespDTO> businessTakeoutOrderRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(businessTakeoutOrderRespDTO));
        final BusinessTakeoutOrderReqDTO reqDTO = new BusinessTakeoutOrderReqDTO();
        reqDTO.setStartTime("startTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setOrderState(0);
        reqDTO.setPhoneNo("phoneNo");
        reqDTO.setOrderSource(0);
        when(mockOrderService.getTakeoutOrderPage(reqDTO)).thenReturn(businessTakeoutOrderRespDTOPage);

        // Run the test and verify the results
        mockMvc.perform(post("/order_report/takeoutOrder/page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetTakeoutOrderDetail() throws Exception {
        // Setup
        // Configure OrderService.getTakeoutOrderDetail(...).
        final BusinessTakeoutOrderDetailRespDTO businessTakeoutOrderDetailRespDTO = new BusinessTakeoutOrderDetailRespDTO();
        businessTakeoutOrderDetailRespDTO.setGuid("b94c23d6-cbf7-4f98-bd6a-38a96774a6c7");
        businessTakeoutOrderDetailRespDTO.setOrderNo("orderNo");
        businessTakeoutOrderDetailRespDTO.setOrderState("orderState");
        businessTakeoutOrderDetailRespDTO.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessTakeoutOrderDetailRespDTO.setOrderSource("orderSource");
        when(mockOrderService.getTakeoutOrderDetail("orderGuid")).thenReturn(businessTakeoutOrderDetailRespDTO);

        // Run the test and verify the results
        mockMvc.perform(get("/order_report/takeoutOrder/detail")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
