package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.OrderService;
import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsQueryDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.ItemRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TakeoutStatisticsController.class)
public class TakeoutStatisticsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OrderService mockOrderService;

    @Test
    public void testGetOrderMoney() throws Exception {
        // Setup
        when(mockOrderService.getOrderMoney(any(HandoverPayQueryDTO.class)))
                .thenReturn(HandoverPayDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/get_order_money")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetOpStats() throws Exception {
        // Setup
        // Configure OrderService.getOpStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setCustomerCount(0);
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockOrderService.getOpStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/get_op_stats")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetReceiptStats() throws Exception {
        // Setup
        // Configure OrderService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setCustomerCount(0);
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockOrderService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/get_receipt_stats")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListItemSale() throws Exception {
        // Setup
        // Configure OrderService.listTakeOutItemSale(...).
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("51f05354-621e-4737-8729-9cda23e2f16a");
        itemRespDTO.setName("name");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setFreeNum(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO);
        final DailyReqDTO request = new DailyReqDTO();
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        when(mockOrderService.listTakeOutItemSale(request)).thenReturn(itemRespDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/list_item_sale")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListItemSale_OrderServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure OrderService.listTakeOutItemSale(...).
        final DailyReqDTO request = new DailyReqDTO();
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        when(mockOrderService.listTakeOutItemSale(request)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/list_item_sale")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGoods() throws Exception {
        // Setup
        // Configure OrderService.listTakeOutGoodsSale(...).
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("51f05354-621e-4737-8729-9cda23e2f16a");
        itemRespDTO.setName("name");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setFreeNum(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO);
        final DailyReqDTO request = new DailyReqDTO();
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        when(mockOrderService.listTakeOutGoodsSale(request)).thenReturn(itemRespDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/list_goods")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGoods_OrderServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure OrderService.listTakeOutGoodsSale(...).
        final DailyReqDTO request = new DailyReqDTO();
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        when(mockOrderService.listTakeOutGoodsSale(request)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/list_goods")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetTradeStats() throws Exception {
        // Setup
        // Configure OrderService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setCustomerCount(0);
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockOrderService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/get_trade_stats")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
