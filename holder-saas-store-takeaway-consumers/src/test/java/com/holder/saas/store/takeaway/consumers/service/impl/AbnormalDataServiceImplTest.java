package com.holder.saas.store.takeaway.consumers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holder.saas.store.takeaway.consumers.entity.domain.AbnormalDataDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.mapper.AbnormalDataMapper;
import com.holder.saas.store.takeaway.consumers.mapper.ItemMapper;
import com.holder.saas.store.takeaway.consumers.mapstruct.ItemMapstruct;
import com.holder.saas.store.takeaway.consumers.service.rpc.OrganizationService;
import com.holder.saas.store.takeaway.consumers.service.rpc.StaffFeignClient;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.request.MoveDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutAndStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemAbnormalDataReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemDataFixReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemAbnormalDataRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemDataFixRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AbnormalDataServiceImplTest {

    @Mock
    private AbnormalDataMapper mockAbnormalDataMapper;
    @Mock
    private OrganizationService mockOrganizationService;
    @Mock
    private StaffFeignClient mockStaffFeignClient;
    @Mock
    private ItemMapper mockItemMapper;
    @Mock
    private ItemMapstruct mockItemMapstruct;

    private AbnormalDataServiceImpl abnormalDataServiceImplUnderTest;

    @Before
    public void setUp() {
        abnormalDataServiceImplUnderTest = new AbnormalDataServiceImpl(mockAbnormalDataMapper, mockOrganizationService,
                mockStaffFeignClient, mockItemMapper, mockItemMapstruct);
    }

    @Test
    public void testPage() {
        // Setup
        final TakeoutItemAbnormalDataReqDTO reqDTO = new TakeoutItemAbnormalDataReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setOrderType(0);
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");

        when(mockStaffFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));
        when(mockOrganizationService.queryStoreGuidListByBrandGui("brandGuid")).thenReturn(Arrays.asList("value"));

        // Configure AbnormalDataMapper.page(...).
        final TakeoutItemAbnormalDataReqDTO reqDTO1 = new TakeoutItemAbnormalDataReqDTO();
        reqDTO1.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setOrderType(0);
        reqDTO1.setStoreGuidList(Arrays.asList("value"));
        reqDTO1.setBrandGuid("brandGuid");
        when(mockAbnormalDataMapper.page(any(IPage.class), eq(reqDTO1))).thenReturn(null);

        // Configure OrganizationService.queryStoreByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("6f8a53d5-4b72-4eac-9e02-e34274a368d0");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final Page<TakeoutItemAbnormalDataRespDTO> result = abnormalDataServiceImplUnderTest.page(reqDTO);

        // Verify the results
    }

    @Test
    public void testPage_StaffFeignClientReturnsNoItems() {
        // Setup
        final TakeoutItemAbnormalDataReqDTO reqDTO = new TakeoutItemAbnormalDataReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setOrderType(0);
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");

        when(mockStaffFeignClient.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        final Page<TakeoutItemAbnormalDataRespDTO> result = abnormalDataServiceImplUnderTest.page(reqDTO);

        // Verify the results
    }

    @Test
    public void testPage_OrganizationServiceQueryStoreGuidListByBrandGuiReturnsNoItems() {
        // Setup
        final TakeoutItemAbnormalDataReqDTO reqDTO = new TakeoutItemAbnormalDataReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setOrderType(0);
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");

        when(mockStaffFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));
        when(mockOrganizationService.queryStoreGuidListByBrandGui("brandGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final Page<TakeoutItemAbnormalDataRespDTO> result = abnormalDataServiceImplUnderTest.page(reqDTO);

        // Verify the results
    }

    @Test
    public void testPage_OrganizationServiceQueryStoreByIdListReturnsNoItems() {
        // Setup
        final TakeoutItemAbnormalDataReqDTO reqDTO = new TakeoutItemAbnormalDataReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setOrderType(0);
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");

        when(mockStaffFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));
        when(mockOrganizationService.queryStoreGuidListByBrandGui("brandGuid")).thenReturn(Arrays.asList("value"));

        // Configure AbnormalDataMapper.page(...).
        final TakeoutItemAbnormalDataReqDTO reqDTO1 = new TakeoutItemAbnormalDataReqDTO();
        reqDTO1.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setOrderType(0);
        reqDTO1.setStoreGuidList(Arrays.asList("value"));
        reqDTO1.setBrandGuid("brandGuid");
        when(mockAbnormalDataMapper.page(any(IPage.class), eq(reqDTO1))).thenReturn(null);

        when(mockOrganizationService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<TakeoutItemAbnormalDataRespDTO> result = abnormalDataServiceImplUnderTest.page(reqDTO);

        // Verify the results
    }

    @Test
    public void testMove() {
        // Setup
        final MoveDTO moveDTO = new MoveDTO();
        moveDTO.setEnterpriseGuid("enterpriseGuid");
        moveDTO.setExecuteCount(0);
        moveDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        moveDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure ItemMapper.selectList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure ItemMapstruct.itemDOList2AbnormalDataDOList(...).
        final AbnormalDataDO abnormalDataDO = new AbnormalDataDO();
        abnormalDataDO.setId(0L);
        abnormalDataDO.setTakeoutItemId(0L);
        abnormalDataDO.setStoreGuid("storeGuid");
        abnormalDataDO.setStoreName("storeName");
        abnormalDataDO.setOrderSubType(0);
        final List<AbnormalDataDO> abnormalDataDOS = Arrays.asList(abnormalDataDO);
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setThirdSkuId("thirdSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ItemDO> itemDOList = Arrays.asList(itemDO1);
        when(mockItemMapstruct.itemDOList2AbnormalDataDOList(itemDOList)).thenReturn(abnormalDataDOS);

        // Run the test
        abnormalDataServiceImplUnderTest.move(moveDTO);

        // Verify the results
        // Confirm AbnormalDataMapper.insertIgnore(...).
        final AbnormalDataDO abnormalDataDO1 = new AbnormalDataDO();
        abnormalDataDO1.setId(0L);
        abnormalDataDO1.setTakeoutItemId(0L);
        abnormalDataDO1.setStoreGuid("storeGuid");
        abnormalDataDO1.setStoreName("storeName");
        abnormalDataDO1.setOrderSubType(0);
        final List<AbnormalDataDO> dataDOList = Arrays.asList(abnormalDataDO1);
        verify(mockAbnormalDataMapper).insertIgnore(dataDOList);
    }

    @Test
    public void testMove_ItemMapperReturnsNoItems() {
        // Setup
        final MoveDTO moveDTO = new MoveDTO();
        moveDTO.setEnterpriseGuid("enterpriseGuid");
        moveDTO.setExecuteCount(0);
        moveDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        moveDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        abnormalDataServiceImplUnderTest.move(moveDTO);

        // Verify the results
    }

    @Test
    public void testMove_ItemMapstructReturnsNoItems() {
        // Setup
        final MoveDTO moveDTO = new MoveDTO();
        moveDTO.setEnterpriseGuid("enterpriseGuid");
        moveDTO.setExecuteCount(0);
        moveDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        moveDTO.setEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure ItemMapper.selectList(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure ItemMapstruct.itemDOList2AbnormalDataDOList(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setThirdSkuId("thirdSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ItemDO> itemDOList = Arrays.asList(itemDO1);
        when(mockItemMapstruct.itemDOList2AbnormalDataDOList(itemDOList)).thenReturn(Collections.emptyList());

        // Run the test
        abnormalDataServiceImplUnderTest.move(moveDTO);

        // Verify the results
        // Confirm AbnormalDataMapper.insertIgnore(...).
        final AbnormalDataDO abnormalDataDO = new AbnormalDataDO();
        abnormalDataDO.setId(0L);
        abnormalDataDO.setTakeoutItemId(0L);
        abnormalDataDO.setStoreGuid("storeGuid");
        abnormalDataDO.setStoreName("storeName");
        abnormalDataDO.setOrderSubType(0);
        final List<AbnormalDataDO> dataDOList = Arrays.asList(abnormalDataDO);
        verify(mockAbnormalDataMapper).insertIgnore(dataDOList);
    }

    @Test
    public void testListDataFix() {
        // Setup
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        reqDTO.setSpliceList(Arrays.asList("value"));

        final TakeoutItemDataFixRespDTO takeoutItemDataFixRespDTO = new TakeoutItemDataFixRespDTO();
        takeoutItemDataFixRespDTO.setStoreGuid("storeGuid");
        takeoutItemDataFixRespDTO.setStoreName("name");
        takeoutItemDataFixRespDTO.setOrderSubType(0);
        takeoutItemDataFixRespDTO.setTakeoutItemName("takeoutItemName");
        takeoutItemDataFixRespDTO.setThirdSkuId("thirdSkuId");
        final List<TakeoutItemDataFixRespDTO> expectedResult = Arrays.asList(takeoutItemDataFixRespDTO);

        // Configure AbnormalDataMapper.listDataFix(...).
        final TakeoutItemDataFixRespDTO takeoutItemDataFixRespDTO1 = new TakeoutItemDataFixRespDTO();
        takeoutItemDataFixRespDTO1.setStoreGuid("storeGuid");
        takeoutItemDataFixRespDTO1.setStoreName("name");
        takeoutItemDataFixRespDTO1.setOrderSubType(0);
        takeoutItemDataFixRespDTO1.setTakeoutItemName("takeoutItemName");
        takeoutItemDataFixRespDTO1.setThirdSkuId("thirdSkuId");
        final List<TakeoutItemDataFixRespDTO> takeoutItemDataFixRespDTOS = Arrays.asList(takeoutItemDataFixRespDTO1);
        final TakeoutItemDataFixReqDTO reqDTO1 = new TakeoutItemDataFixReqDTO();
        reqDTO1.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO1 = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO1.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO1));
        reqDTO1.setSpliceList(Arrays.asList("value"));
        when(mockAbnormalDataMapper.listDataFix(reqDTO1)).thenReturn(takeoutItemDataFixRespDTOS);

        // Configure OrganizationService.queryStoreByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("6f8a53d5-4b72-4eac-9e02-e34274a368d0");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final List<TakeoutItemDataFixRespDTO> result = abnormalDataServiceImplUnderTest.listDataFix(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListDataFix_AbnormalDataMapperReturnsNoItems() {
        // Setup
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        reqDTO.setSpliceList(Arrays.asList("value"));

        // Configure AbnormalDataMapper.listDataFix(...).
        final TakeoutItemDataFixReqDTO reqDTO1 = new TakeoutItemDataFixReqDTO();
        reqDTO1.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO1 = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO1.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO1));
        reqDTO1.setSpliceList(Arrays.asList("value"));
        when(mockAbnormalDataMapper.listDataFix(reqDTO1)).thenReturn(Collections.emptyList());

        // Configure OrganizationService.queryStoreByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("6f8a53d5-4b72-4eac-9e02-e34274a368d0");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final List<TakeoutItemDataFixRespDTO> result = abnormalDataServiceImplUnderTest.listDataFix(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListDataFix_OrganizationServiceReturnsNoItems() {
        // Setup
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        reqDTO.setSpliceList(Arrays.asList("value"));

        // Configure AbnormalDataMapper.listDataFix(...).
        final TakeoutItemDataFixRespDTO takeoutItemDataFixRespDTO = new TakeoutItemDataFixRespDTO();
        takeoutItemDataFixRespDTO.setStoreGuid("storeGuid");
        takeoutItemDataFixRespDTO.setStoreName("name");
        takeoutItemDataFixRespDTO.setOrderSubType(0);
        takeoutItemDataFixRespDTO.setTakeoutItemName("takeoutItemName");
        takeoutItemDataFixRespDTO.setThirdSkuId("thirdSkuId");
        final List<TakeoutItemDataFixRespDTO> takeoutItemDataFixRespDTOS = Arrays.asList(takeoutItemDataFixRespDTO);
        final TakeoutItemDataFixReqDTO reqDTO1 = new TakeoutItemDataFixReqDTO();
        reqDTO1.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO1 = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO1.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO1));
        reqDTO1.setSpliceList(Arrays.asList("value"));
        when(mockAbnormalDataMapper.listDataFix(reqDTO1)).thenReturn(takeoutItemDataFixRespDTOS);

        when(mockOrganizationService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<TakeoutItemDataFixRespDTO> result = abnormalDataServiceImplUnderTest.listDataFix(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListFixItem() {
        // Setup
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        reqDTO.setSpliceList(Arrays.asList("value"));

        final ItemDO itemDO = new ItemDO();
        itemDO.setId(0L);
        itemDO.setStoreGuid("storeGuid");
        itemDO.setThirdSkuId("thirdSkuId");
        itemDO.setErpItemName("erpItemName");
        itemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ItemDO> expectedResult = Arrays.asList(itemDO);

        // Configure AbnormalDataMapper.listFixItem(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setId(0L);
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setThirdSkuId("thirdSkuId");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ItemDO> itemDOS = Arrays.asList(itemDO1);
        final TakeoutItemDataFixReqDTO reqDTO1 = new TakeoutItemDataFixReqDTO();
        reqDTO1.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO1 = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO1.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO1));
        reqDTO1.setSpliceList(Arrays.asList("value"));
        when(mockAbnormalDataMapper.listFixItem(reqDTO1)).thenReturn(itemDOS);

        // Run the test
        final List<ItemDO> result = abnormalDataServiceImplUnderTest.listFixItem(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListFixItem_AbnormalDataMapperReturnsNoItems() {
        // Setup
        final TakeoutItemDataFixReqDTO reqDTO = new TakeoutItemDataFixReqDTO();
        reqDTO.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO.setStoreGuid("storeGuid");
        reqDTO.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO));
        reqDTO.setSpliceList(Arrays.asList("value"));

        // Configure AbnormalDataMapper.listFixItem(...).
        final TakeoutItemDataFixReqDTO reqDTO1 = new TakeoutItemDataFixReqDTO();
        reqDTO1.setStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TakeoutAndStoreReqDTO takeoutAndStoreReqDTO1 = new TakeoutAndStoreReqDTO();
        takeoutAndStoreReqDTO1.setTakeoutItemNumber("takeoutItemNumber");
        takeoutAndStoreReqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setTakeoutAndStore(Arrays.asList(takeoutAndStoreReqDTO1));
        reqDTO1.setSpliceList(Arrays.asList("value"));
        when(mockAbnormalDataMapper.listFixItem(reqDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemDO> result = abnormalDataServiceImplUnderTest.listFixItem(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testRemoveByTakeoutItemIds() {
        // Setup
        // Run the test
        abnormalDataServiceImplUnderTest.removeByTakeoutItemIds(Arrays.asList(0L));

        // Verify the results
    }
}
