package com.holder.saas.store.takeaway.consumers.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixRecordDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutRecordQueryDTO;
import org.junit.Before;
import org.junit.Test;

public class FixRecordServiceImplTest {

    private FixRecordServiceImpl fixRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        fixRecordServiceImplUnderTest = new FixRecordServiceImpl();
    }

    @Test
    public void testPageInfo() {
        // Setup
        final TakeoutRecordQueryDTO queryDTO = new TakeoutRecordQueryDTO();

        // Run the test
        final Page<TakeoutFixRecordDTO> result = fixRecordServiceImplUnderTest.pageInfo(queryDTO);

        // Verify the results
    }
}
