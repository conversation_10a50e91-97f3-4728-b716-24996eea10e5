package com.holder.saas.store.takeaway.consumers.manage;

import com.holder.saas.store.takeaway.consumers.entity.domain.AutoRecoveryDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.ItemDO;
import com.holder.saas.store.takeaway.consumers.entity.domain.PackageDO;
import com.holder.saas.store.takeaway.consumers.mapstruct.ItemPackageMapstruct;
import com.holder.saas.store.takeaway.consumers.service.AutoRecoveryService;
import com.holder.saas.store.takeaway.consumers.service.ItemService;
import com.holder.saas.store.takeaway.consumers.service.PackageService;
import com.holder.saas.store.takeaway.consumers.service.rpc.ItemFeignClient;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AutoRecoveryManagerTest {

    @Mock
    private ItemService mockItemService;
    @Mock
    private PackageService mockPackageService;
    @Mock
    private AutoRecoveryService mockAutoRecoveryService;
    @Mock
    private ItemFeignClient mockItemFeignClient;
    @Mock
    private ItemPackageMapstruct mockItemPackageMapstruct;

    private AutoRecoveryManager autoRecoveryManagerUnderTest;

    @Before
    public void setUp() {
        autoRecoveryManagerUnderTest = new AutoRecoveryManager(mockItemService, mockPackageService,
                mockAutoRecoveryService, mockItemFeignClient, mockItemPackageMapstruct);
    }

    @Test
    public void testFixItem() {
        // Setup
        final AutoRecoveryDO autoRecoveryDO = new AutoRecoveryDO();
        autoRecoveryDO.setId("id");
        autoRecoveryDO.setOrderGuid("orderGuid");
        autoRecoveryDO.setExSource("exSource");
        autoRecoveryDO.setExMsg("exMsg");
        autoRecoveryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<AutoRecoveryDO> recoveryRecords = Arrays.asList(autoRecoveryDO);

        // Configure ItemService.listByItemGuids(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.listByItemGuids(Arrays.asList("value"))).thenReturn(itemDOS);

        // Configure ItemFeignClient.findParentSKUS(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setParentGuid("erpItemSkuGuid");
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockItemFeignClient.findParentSKUS(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO1 = new SkuInfoRespDTO();
        skuInfoRespDTO1.setSkuGuid("skuGuid");
        skuInfoRespDTO1.setName("name");
        skuInfoRespDTO1.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO1.setParentGuid("erpItemSkuGuid");
        skuInfoRespDTO1.setItemName("erpItemName");
        skuInfoRespDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        skuInfoRespDTO1.setListPkg(Arrays.asList(skuInfoPkgDTO2));
        final List<SkuInfoRespDTO> skuInfoRespDTOS1 = Arrays.asList(skuInfoRespDTO1);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS1);

        when(mockPackageService.listByTakeoutItemGuids(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Configure ItemPackageMapstruct.parseToPackageDO(...).
        final PackageDO packageDO = new PackageDO();
        packageDO.setId(0L);
        packageDO.setIsDelete(0);
        packageDO.setTakeoutItemGuid("itemGuid");
        packageDO.setPackageGuid("packageGuid");
        packageDO.setAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO pkgDTO = new SkuInfoPkgDTO();
        pkgDTO.setItemGuid("itemGuid");
        pkgDTO.setParentGuid("parentGuid");
        pkgDTO.setItemNum(new BigDecimal("0.00"));
        pkgDTO.setItemName("itemName");
        pkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        when(mockItemPackageMapstruct.parseToPackageDO(pkgDTO)).thenReturn(packageDO);

        // Run the test
        autoRecoveryManagerUnderTest.fixItem(recoveryRecords);

        // Verify the results
        verify(mockAutoRecoveryService).removeByIds(Arrays.asList("value"));

        // Confirm ItemService.updateBatchById(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setItemGuid("itemGuid");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO3 = new SkuInfoPkgDTO();
        skuInfoPkgDTO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO3));
        final List<ItemDO> entityList = Arrays.asList(itemDO1);
        verify(mockItemService).updateBatchById(entityList);

        // Confirm PackageService.saveBatch(...).
        final PackageDO packageDO1 = new PackageDO();
        packageDO1.setId(0L);
        packageDO1.setIsDelete(0);
        packageDO1.setTakeoutItemGuid("itemGuid");
        packageDO1.setPackageGuid("packageGuid");
        packageDO1.setAccountingPrice(new BigDecimal("0.00"));
        final List<PackageDO> entityList1 = Arrays.asList(packageDO1);
        verify(mockPackageService).saveBatch(entityList1);
    }

    @Test
    public void testFixItem_ItemServiceListByItemGuidsReturnsNoItems() {
        // Setup
        final AutoRecoveryDO autoRecoveryDO = new AutoRecoveryDO();
        autoRecoveryDO.setId("id");
        autoRecoveryDO.setOrderGuid("orderGuid");
        autoRecoveryDO.setExSource("exSource");
        autoRecoveryDO.setExMsg("exMsg");
        autoRecoveryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<AutoRecoveryDO> recoveryRecords = Arrays.asList(autoRecoveryDO);
        when(mockItemService.listByItemGuids(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        autoRecoveryManagerUnderTest.fixItem(recoveryRecords);

        // Verify the results
    }

    @Test
    public void testFixItem_ItemFeignClientFindParentSKUSReturnsNoItems() {
        // Setup
        final AutoRecoveryDO autoRecoveryDO = new AutoRecoveryDO();
        autoRecoveryDO.setId("id");
        autoRecoveryDO.setOrderGuid("orderGuid");
        autoRecoveryDO.setExSource("exSource");
        autoRecoveryDO.setExMsg("exMsg");
        autoRecoveryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<AutoRecoveryDO> recoveryRecords = Arrays.asList(autoRecoveryDO);

        // Configure ItemService.listByItemGuids(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.listByItemGuids(Arrays.asList("value"))).thenReturn(itemDOS);

        when(mockItemFeignClient.findParentSKUS(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setParentGuid("erpItemSkuGuid");
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        when(mockPackageService.listByTakeoutItemGuids(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Configure ItemPackageMapstruct.parseToPackageDO(...).
        final PackageDO packageDO = new PackageDO();
        packageDO.setId(0L);
        packageDO.setIsDelete(0);
        packageDO.setTakeoutItemGuid("itemGuid");
        packageDO.setPackageGuid("packageGuid");
        packageDO.setAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO pkgDTO = new SkuInfoPkgDTO();
        pkgDTO.setItemGuid("itemGuid");
        pkgDTO.setParentGuid("parentGuid");
        pkgDTO.setItemNum(new BigDecimal("0.00"));
        pkgDTO.setItemName("itemName");
        pkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        when(mockItemPackageMapstruct.parseToPackageDO(pkgDTO)).thenReturn(packageDO);

        // Run the test
        autoRecoveryManagerUnderTest.fixItem(recoveryRecords);

        // Verify the results
        verify(mockAutoRecoveryService).removeByIds(Arrays.asList("value"));

        // Confirm ItemService.updateBatchById(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setItemGuid("itemGuid");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO2));
        final List<ItemDO> entityList = Arrays.asList(itemDO1);
        verify(mockItemService).updateBatchById(entityList);

        // Confirm PackageService.saveBatch(...).
        final PackageDO packageDO1 = new PackageDO();
        packageDO1.setId(0L);
        packageDO1.setIsDelete(0);
        packageDO1.setTakeoutItemGuid("itemGuid");
        packageDO1.setPackageGuid("packageGuid");
        packageDO1.setAccountingPrice(new BigDecimal("0.00"));
        final List<PackageDO> entityList1 = Arrays.asList(packageDO1);
        verify(mockPackageService).saveBatch(entityList1);
    }

    @Test
    public void testFixItem_ItemFeignClientListSkuInfoByRecipeModeReturnsNoItems() {
        // Setup
        final AutoRecoveryDO autoRecoveryDO = new AutoRecoveryDO();
        autoRecoveryDO.setId("id");
        autoRecoveryDO.setOrderGuid("orderGuid");
        autoRecoveryDO.setExSource("exSource");
        autoRecoveryDO.setExMsg("exMsg");
        autoRecoveryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<AutoRecoveryDO> recoveryRecords = Arrays.asList(autoRecoveryDO);

        // Configure ItemService.listByItemGuids(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.listByItemGuids(Arrays.asList("value"))).thenReturn(itemDOS);

        // Configure ItemFeignClient.findParentSKUS(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setParentGuid("erpItemSkuGuid");
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockItemFeignClient.findParentSKUS(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(Collections.emptyList());

        when(mockPackageService.listByTakeoutItemGuids(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Configure ItemPackageMapstruct.parseToPackageDO(...).
        final PackageDO packageDO = new PackageDO();
        packageDO.setId(0L);
        packageDO.setIsDelete(0);
        packageDO.setTakeoutItemGuid("itemGuid");
        packageDO.setPackageGuid("packageGuid");
        packageDO.setAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO pkgDTO = new SkuInfoPkgDTO();
        pkgDTO.setItemGuid("itemGuid");
        pkgDTO.setParentGuid("parentGuid");
        pkgDTO.setItemNum(new BigDecimal("0.00"));
        pkgDTO.setItemName("itemName");
        pkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        when(mockItemPackageMapstruct.parseToPackageDO(pkgDTO)).thenReturn(packageDO);

        // Run the test
        autoRecoveryManagerUnderTest.fixItem(recoveryRecords);

        // Verify the results
        verify(mockAutoRecoveryService).removeByIds(Arrays.asList("value"));

        // Confirm ItemService.updateBatchById(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setItemGuid("itemGuid");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO2));
        final List<ItemDO> entityList = Arrays.asList(itemDO1);
        verify(mockItemService).updateBatchById(entityList);

        // Confirm PackageService.saveBatch(...).
        final PackageDO packageDO1 = new PackageDO();
        packageDO1.setId(0L);
        packageDO1.setIsDelete(0);
        packageDO1.setTakeoutItemGuid("itemGuid");
        packageDO1.setPackageGuid("packageGuid");
        packageDO1.setAccountingPrice(new BigDecimal("0.00"));
        final List<PackageDO> entityList1 = Arrays.asList(packageDO1);
        verify(mockPackageService).saveBatch(entityList1);
    }

    @Test
    public void testFixItem_PackageServiceListByTakeoutItemGuidsReturnsNoItems() {
        // Setup
        final AutoRecoveryDO autoRecoveryDO = new AutoRecoveryDO();
        autoRecoveryDO.setId("id");
        autoRecoveryDO.setOrderGuid("orderGuid");
        autoRecoveryDO.setExSource("exSource");
        autoRecoveryDO.setExMsg("exMsg");
        autoRecoveryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<AutoRecoveryDO> recoveryRecords = Arrays.asList(autoRecoveryDO);

        // Configure ItemService.listByItemGuids(...).
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<ItemDO> itemDOS = Arrays.asList(itemDO);
        when(mockItemService.listByItemGuids(Arrays.asList("value"))).thenReturn(itemDOS);

        // Configure ItemFeignClient.findParentSKUS(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setParentGuid("erpItemSkuGuid");
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockItemFeignClient.findParentSKUS(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO1 = new SkuInfoRespDTO();
        skuInfoRespDTO1.setSkuGuid("skuGuid");
        skuInfoRespDTO1.setName("name");
        skuInfoRespDTO1.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO1.setParentGuid("erpItemSkuGuid");
        skuInfoRespDTO1.setItemName("erpItemName");
        skuInfoRespDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        skuInfoRespDTO1.setListPkg(Arrays.asList(skuInfoPkgDTO2));
        final List<SkuInfoRespDTO> skuInfoRespDTOS1 = Arrays.asList(skuInfoRespDTO1);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS1);

        when(mockPackageService.listByTakeoutItemGuids(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure ItemPackageMapstruct.parseToPackageDO(...).
        final PackageDO packageDO = new PackageDO();
        packageDO.setId(0L);
        packageDO.setIsDelete(0);
        packageDO.setTakeoutItemGuid("itemGuid");
        packageDO.setPackageGuid("packageGuid");
        packageDO.setAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO pkgDTO = new SkuInfoPkgDTO();
        pkgDTO.setItemGuid("itemGuid");
        pkgDTO.setParentGuid("parentGuid");
        pkgDTO.setItemNum(new BigDecimal("0.00"));
        pkgDTO.setItemName("itemName");
        pkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        when(mockItemPackageMapstruct.parseToPackageDO(pkgDTO)).thenReturn(packageDO);

        // Run the test
        autoRecoveryManagerUnderTest.fixItem(recoveryRecords);

        // Verify the results
        verify(mockAutoRecoveryService).removeByIds(Arrays.asList("value"));

        // Confirm ItemService.updateBatchById(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setItemGuid("itemGuid");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO3 = new SkuInfoPkgDTO();
        skuInfoPkgDTO3.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO3));
        final List<ItemDO> entityList = Arrays.asList(itemDO1);
        verify(mockItemService).updateBatchById(entityList);

        // Confirm PackageService.saveBatch(...).
        final PackageDO packageDO1 = new PackageDO();
        packageDO1.setId(0L);
        packageDO1.setIsDelete(0);
        packageDO1.setTakeoutItemGuid("itemGuid");
        packageDO1.setPackageGuid("packageGuid");
        packageDO1.setAccountingPrice(new BigDecimal("0.00"));
        final List<PackageDO> entityList1 = Arrays.asList(packageDO1);
        verify(mockPackageService).saveBatch(entityList1);
    }

    @Test
    public void testFindItemData() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<ItemDO> itemList = Arrays.asList(itemDO);

        // Configure ItemFeignClient.findParentSKUS(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setParentGuid("erpItemSkuGuid");
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockItemFeignClient.findParentSKUS(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO1 = new SkuInfoRespDTO();
        skuInfoRespDTO1.setSkuGuid("skuGuid");
        skuInfoRespDTO1.setName("name");
        skuInfoRespDTO1.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO1.setParentGuid("erpItemSkuGuid");
        skuInfoRespDTO1.setItemName("erpItemName");
        skuInfoRespDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO2 = new SkuInfoPkgDTO();
        skuInfoPkgDTO2.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        skuInfoRespDTO1.setListPkg(Arrays.asList(skuInfoPkgDTO2));
        final List<SkuInfoRespDTO> skuInfoRespDTOS1 = Arrays.asList(skuInfoRespDTO1);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS1);

        // Run the test
        autoRecoveryManagerUnderTest.findItemData("storeGuid", itemList);

        // Verify the results
    }

    @Test
    public void testFindItemData_ItemFeignClientFindParentSKUSReturnsNoItems() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<ItemDO> itemList = Arrays.asList(itemDO);
        when(mockItemFeignClient.findParentSKUS(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setParentGuid("erpItemSkuGuid");
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(skuInfoRespDTOS);

        // Run the test
        autoRecoveryManagerUnderTest.findItemData("storeGuid", itemList);

        // Verify the results
    }

    @Test
    public void testFindItemData_ItemFeignClientListSkuInfoByRecipeModeReturnsNoItems() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<ItemDO> itemList = Arrays.asList(itemDO);

        // Configure ItemFeignClient.findParentSKUS(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setParentGuid("erpItemSkuGuid");
        skuInfoRespDTO.setItemName("erpItemName");
        skuInfoRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockItemFeignClient.findParentSKUS(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Configure ItemFeignClient.listSkuInfoByRecipeMode(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemFeignClient.listSkuInfoByRecipeMode(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Run the test
        autoRecoveryManagerUnderTest.findItemData("storeGuid", itemList);

        // Verify the results
    }

    @Test
    public void testFindNotExistPackage() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<ItemDO> itemList = Arrays.asList(itemDO);
        final PackageDO packageDO = new PackageDO();
        packageDO.setId(0L);
        packageDO.setIsDelete(0);
        packageDO.setTakeoutItemGuid("itemGuid");
        packageDO.setPackageGuid("packageGuid");
        packageDO.setAccountingPrice(new BigDecimal("0.00"));
        final List<PackageDO> expectedResult = Arrays.asList(packageDO);
        when(mockPackageService.listByTakeoutItemGuids(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Configure ItemPackageMapstruct.parseToPackageDO(...).
        final PackageDO packageDO1 = new PackageDO();
        packageDO1.setId(0L);
        packageDO1.setIsDelete(0);
        packageDO1.setTakeoutItemGuid("itemGuid");
        packageDO1.setPackageGuid("packageGuid");
        packageDO1.setAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO pkgDTO = new SkuInfoPkgDTO();
        pkgDTO.setItemGuid("itemGuid");
        pkgDTO.setParentGuid("parentGuid");
        pkgDTO.setItemNum(new BigDecimal("0.00"));
        pkgDTO.setItemName("itemName");
        pkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        when(mockItemPackageMapstruct.parseToPackageDO(pkgDTO)).thenReturn(packageDO1);

        // Run the test
        final List<PackageDO> result = autoRecoveryManagerUnderTest.findNotExistPackage(itemList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindNotExistPackage_PackageServiceReturnsNoItems() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<ItemDO> itemList = Arrays.asList(itemDO);
        when(mockPackageService.listByTakeoutItemGuids(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure ItemPackageMapstruct.parseToPackageDO(...).
        final PackageDO packageDO = new PackageDO();
        packageDO.setId(0L);
        packageDO.setIsDelete(0);
        packageDO.setTakeoutItemGuid("itemGuid");
        packageDO.setPackageGuid("packageGuid");
        packageDO.setAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO pkgDTO = new SkuInfoPkgDTO();
        pkgDTO.setItemGuid("itemGuid");
        pkgDTO.setParentGuid("parentGuid");
        pkgDTO.setItemNum(new BigDecimal("0.00"));
        pkgDTO.setItemName("itemName");
        pkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        when(mockItemPackageMapstruct.parseToPackageDO(pkgDTO)).thenReturn(packageDO);

        // Run the test
        final List<PackageDO> result = autoRecoveryManagerUnderTest.findNotExistPackage(itemList);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSaveBatch() {
        // Setup
        final ItemDO itemDO = new ItemDO();
        itemDO.setStoreGuid("storeGuid");
        itemDO.setItemGuid("itemGuid");
        itemDO.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO.setErpItemName("erpItemName");
        itemDO.setErpItemPrice(new BigDecimal("0.00"));
        itemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO = new SkuInfoPkgDTO();
        skuInfoPkgDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO.setListPkg(Arrays.asList(skuInfoPkgDTO));
        final List<ItemDO> itemList = Arrays.asList(itemDO);
        final PackageDO packageDO = new PackageDO();
        packageDO.setId(0L);
        packageDO.setIsDelete(0);
        packageDO.setTakeoutItemGuid("itemGuid");
        packageDO.setPackageGuid("packageGuid");
        packageDO.setAccountingPrice(new BigDecimal("0.00"));
        final List<PackageDO> packageList = Arrays.asList(packageDO);

        // Run the test
        autoRecoveryManagerUnderTest.saveBatch(itemList, packageList, Arrays.asList("value"));

        // Verify the results
        // Confirm ItemService.updateBatchById(...).
        final ItemDO itemDO1 = new ItemDO();
        itemDO1.setStoreGuid("storeGuid");
        itemDO1.setItemGuid("itemGuid");
        itemDO1.setErpItemSkuGuid("erpItemSkuGuid");
        itemDO1.setErpItemName("erpItemName");
        itemDO1.setErpItemPrice(new BigDecimal("0.00"));
        itemDO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        final SkuInfoPkgDTO skuInfoPkgDTO1 = new SkuInfoPkgDTO();
        skuInfoPkgDTO1.setTakeawayAccountingPrice(new BigDecimal("0.00"));
        itemDO1.setListPkg(Arrays.asList(skuInfoPkgDTO1));
        final List<ItemDO> entityList = Arrays.asList(itemDO1);
        verify(mockItemService).updateBatchById(entityList);

        // Confirm PackageService.saveBatch(...).
        final PackageDO packageDO1 = new PackageDO();
        packageDO1.setId(0L);
        packageDO1.setIsDelete(0);
        packageDO1.setTakeoutItemGuid("itemGuid");
        packageDO1.setPackageGuid("packageGuid");
        packageDO1.setAccountingPrice(new BigDecimal("0.00"));
        final List<PackageDO> entityList1 = Arrays.asList(packageDO1);
        verify(mockPackageService).saveBatch(entityList1);
        verify(mockAutoRecoveryService).removeByIds(Arrays.asList("value"));
    }
}
