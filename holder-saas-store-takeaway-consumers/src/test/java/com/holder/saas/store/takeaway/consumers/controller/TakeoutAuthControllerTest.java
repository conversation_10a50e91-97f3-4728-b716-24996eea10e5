package com.holder.saas.store.takeaway.consumers.controller;

import com.holder.saas.store.takeaway.consumers.service.AuthService;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByTypeReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreUsedReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TakeoutAuthController.class)
public class TakeoutAuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AuthService mockAuthService;

    @Test
    public void testQueryAuthByType() throws Exception {
        // Setup
        // Configure AuthService.queryAuthByType(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        final List<StoreAuthDTO> storeAuthDTOS = Arrays.asList(storeAuthDTO);
        final StoreAuthByTypeReqDTO storeAuthByTypeReqDTO = new StoreAuthByTypeReqDTO();
        storeAuthByTypeReqDTO.setTakeoutType(0);
        storeAuthByTypeReqDTO.setBindingStatus(0);
        storeAuthByTypeReqDTO.setQueryString("queryString");
        when(mockAuthService.queryAuthByType(storeAuthByTypeReqDTO)).thenReturn(storeAuthDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/query_auth_by_type")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryAuthByType_AuthServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure AuthService.queryAuthByType(...).
        final StoreAuthByTypeReqDTO storeAuthByTypeReqDTO = new StoreAuthByTypeReqDTO();
        storeAuthByTypeReqDTO.setTakeoutType(0);
        storeAuthByTypeReqDTO.setBindingStatus(0);
        storeAuthByTypeReqDTO.setQueryString("queryString");
        when(mockAuthService.queryAuthByType(storeAuthByTypeReqDTO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/query_auth_by_type")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testQueryTakeoutAuthByStore() throws Exception {
        // Setup
        // Configure AuthService.queryTakeoutAuthByStore(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        final List<StoreAuthDTO> storeAuthDTOS = Arrays.asList(storeAuthDTO);
        final StoreAuthByStoreReqDTO storeAuthReqDTO = new StoreAuthByStoreReqDTO();
        storeAuthReqDTO.setStoreGuid("storeGuid");
        storeAuthReqDTO.setPoiId("poiId");
        storeAuthReqDTO.setBusinessId((byte) 0b0);
        when(mockAuthService.queryTakeoutAuthByStore(storeAuthReqDTO)).thenReturn(storeAuthDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/query_takeout_auth_by_store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryTakeoutAuthByStore_AuthServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure AuthService.queryTakeoutAuthByStore(...).
        final StoreAuthByStoreReqDTO storeAuthReqDTO = new StoreAuthByStoreReqDTO();
        storeAuthReqDTO.setStoreGuid("storeGuid");
        storeAuthReqDTO.setPoiId("poiId");
        storeAuthReqDTO.setBusinessId((byte) 0b0);
        when(mockAuthService.queryTakeoutAuthByStore(storeAuthReqDTO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/query_takeout_auth_by_store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testQueryTuangouByStore() throws Exception {
        // Setup
        // Configure AuthService.queryTuanGouByStore(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        final List<StoreAuthDTO> storeAuthDTOS = Arrays.asList(storeAuthDTO);
        final StoreAuthByStoreReqDTO storeAuthReqDTO = new StoreAuthByStoreReqDTO();
        storeAuthReqDTO.setStoreGuid("storeGuid");
        storeAuthReqDTO.setPoiId("poiId");
        storeAuthReqDTO.setBusinessId((byte) 0b0);
        when(mockAuthService.queryTuanGouByStore(storeAuthReqDTO)).thenReturn(storeAuthDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/query_tuangou_auth_by_store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryTuangouByStore_AuthServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure AuthService.queryTuanGouByStore(...).
        final StoreAuthByStoreReqDTO storeAuthReqDTO = new StoreAuthByStoreReqDTO();
        storeAuthReqDTO.setStoreGuid("storeGuid");
        storeAuthReqDTO.setPoiId("poiId");
        storeAuthReqDTO.setBusinessId((byte) 0b0);
        when(mockAuthService.queryTuanGouByStore(storeAuthReqDTO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/query_tuangou_auth_by_store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testQueryStoreUsed() throws Exception {
        // Setup
        // Configure AuthService.queryStoreUsed(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("04574dba-3461-4e29-bc95-2cd9f1c9ac0c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final StoreUsedReqDTO storeUsedReqDTO = new StoreUsedReqDTO();
        when(mockAuthService.queryStoreUsed(storeUsedReqDTO)).thenReturn(storeDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/query_store_used")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryStoreUsed_AuthServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure AuthService.queryStoreUsed(...).
        final StoreUsedReqDTO storeUsedReqDTO = new StoreUsedReqDTO();
        when(mockAuthService.queryStoreUsed(storeUsedReqDTO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/query_store_used")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testUpdateDeliveryType() throws Exception {
        // Setup
        // Configure AuthService.updateDeliveryType(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        when(mockAuthService.updateDeliveryType(storeAuthDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/update_delivery")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateDeliveryType_AuthServiceReturnsTrue() throws Exception {
        // Setup
        // Configure AuthService.updateDeliveryType(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        when(mockAuthService.updateDeliveryType(storeAuthDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/takeout/update_delivery")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
