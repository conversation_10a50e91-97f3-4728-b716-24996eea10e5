package com.holder.saas.store.takeaway.consumers.service.impl;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class DistributedServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    private DistributedServiceImpl distributedServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        distributedServiceImplUnderTest = new DistributedServiceImpl(mockRedisTemplate);
    }

    @Test
    public void testRawId() {
        assertThat(distributedServiceImplUnderTest.rawId("tag")).isEqualTo(0L);
    }

    @Test
    public void testNextId() {
        assertThat(distributedServiceImplUnderTest.nextId("tag")).isEqualTo("result");
    }

    @Test
    public void testNextBatchId() {
        assertThat(distributedServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextOrderGuid() {
        assertThat(distributedServiceImplUnderTest.nextOrderGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchOrderGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchOrderGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchOrderGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextFixRecordGuid() {
        assertThat(distributedServiceImplUnderTest.nextFixRecordGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchFixItemGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchFixItemGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchFixItemGuid(0L)).isEqualTo(Collections.emptyList());
    }
}
