## 排队服务技术方案
### 1. 组件
> 1. Mysql 
>> 排队队列的数据存储,队列特性的实现 
> 2. Redis
>> 基于redisson的分布式锁
> 3. <PERSON><PERSON>ke
>> 服务治理
> 4. 其它依赖
>> spring-cloud,spring-boot,Mybatise-plus,redisson
> 5. 服务依赖
>> 机构服务,消息服务,打印服务

### 2. 服务拓扑
> 1. ![结构](https://note.youdao.com/yws/api/personal/file/********************************?method=download&shareKey=11c3062f3944fa75be9e598306f59976)
> 2. ![数据流](https://note.youdao.com/yws/api/personal/file/80C881E1D46F4B5CBE6A11E08B601B7D?method=download&shareKey=b3a0c1e6e421c29ad00f582d2b85085b)

> 3. 服务依赖
>> 1. 机构服务 - 查询门店信息
>>> 支撑基于营业时间的数据隔离
>> 2. 消息服务 - 消息推送接口
>>> 支撑排队信息的增量推送
>> 3. 打印服务 - 打印接口
>>> 提供用户取号后的凭证打印功能

### 3. 服务设计
##### 1. 主体设计
> HolderQueue 队列
>> 排队队列的基本信息
>>
>> 基于门店进行数据隔离
>>
>> 持有同门店下的对立持有唯一Code
>> 
>> 不具备队列的特性(队列的特性由队列元素实现)
>>
> HolderQueueItem 队列元素
>> 队列元素的基本信息
>>
>> 基于门店--> 队列 --> 营业日 隔离
>>
>> 同隔离条件下(同门店并同营业日)持有一个唯一的序列号
>>
>> 基于状态实现队列: 入队,出队操作
>>
##### 2. 表设计
1. 队列表(hsq_queue)

name|type|comment
:---:|:---:|:---:
id| BIGINT(20) |主键
guid| VARCHAR(50) |业务主键
store_guid| VARCHAR(50) |门店guid
name| VARCHAR(50) |队列名称
code| VARCHAR(50) |队列编码
min| TINYINT(3) UNSIGNED |最小人数
max| TINYINT(3) UNSIGNED |最大人数
is_enable| BIT(1) |启用标识
is_deleted| BIT(1) |删除标识
create_staff_guid| VARCHAR(50) |创建人
modified_staff_guid| VARCHAR(50) |修改人
gmt_create| DATETIME |创建时间
gmt_modified| DATETIME |修改时间

2. 队列元素表(hsq_queue_item)

name|type|comment
:---:|:---:|:---:
id|BIGINT(20) |主键
guid|VARCHAR(50) |业务主键
store_guid|VARCHAR(50) |门店guid
queue_guid|VARCHAR(50) |队列guid
status|TINYINT(3) UNSIGNED |状态 0:队列中,1:过号,2:叫号中,3:已就餐
contact|VARCHAR(20) |联系人姓名
phone|VARCHAR(20) |联系人手机号
gender|TINYINT(3) |性别
remark|VARCHAR(20) |备注
people_number|TINYINT(3) |人数
sort|INT(11) |序号
queue_code|VARCHAR(20) |队列编码
call_times|TINYINT(3) |叫号次数
start_time|DATETIME |入队时间
end_time|DATETIME |出队时间
is_enable|BIT(1) |启用标记
is_deleted|BIT(1) |删除标记
create_staff_guid|VARCHAR(50) |创建人
modified_staff_guid|VARCHAR(50) |修改人
gmt_create|DATETIME |创建时间
gmt_modified|DATETIME |修改时间


##### 3. 主要流程
1. 取号(入队)

```mermaid
graph LR
用户取号-->根据人数选择适配的队列
根据人数选择适配的队列-->id{是否存在匹配的队列}
id{是否存在匹配的队列}--不存在-->返回队列不存在
id{是否存在匹配的队列}--存在-->加锁
```

```mermaid
graph LR
加锁-->Ad{当前营业日,该队列下是否重复提交}
Ad{当前营业日,该队列下是否重复提交}--重复-->返回之前提交的元素信息
Ad{当前营业日,该队列下是否重复提交}--不重复-->生成序列号
生成序列号-->入库
入库-->填充该元素在该队列前面的元素个数
```


##### 3. 功能设计
> 队列管理
>> 队列的CRUD

> 取号
>> 1. 根据人数挑选适配的队列
>> 2. 加锁 重复提交验证, 生成序列号
>> 3. 调用message服务,发送取号的消息
>> 4. 调用打印服务,打印排队单据
>>
> 叫号
>> 1. 乐观锁修改状态,被叫次数等
>> 2. 调用message服务,发送叫号的消息
>>
> 就餐
>> 1. 修改状态就餐
>> 2. 调用message服务,发送就餐的消息
>>
> 过号
>> 1. 修改状态过号
>> 2. 调用message服务,发送过号的消息


### 4. 其它方案
name|描述
:---:|:---:
sharding|基于企业Guid的mysql sharding
分布式锁|redisson
### 5. 服务参数
name|参数
:---:|:---:
横向拓展|支持

### 6. 补充说明
1. Redis inc 获取sort 取代 加锁的方案
2. 营业日隔离