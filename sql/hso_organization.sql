ALTER TABLE `hso_brand`
    ADD COLUMN `external_version` INT(32) UNSIGNED NOT NULL DEFAULT 0 COMMENT '外部版本号：每次外部操作版本号+1，内部操作不处理' AFTER `modified_user_guid`;

ALTER TABLE `hso_organization`
    ADD COLUMN `external_version` INT(32) UNSIGNED NOT NULL DEFAULT 0 COMMENT '外部版本号：每次外部操作版本号+1，内部操作不处理' AFTER `latitude`;

ALTER TABLE `hso_r_store_brand`
    ADD COLUMN `external_version` INT(32) UNSIGNED NOT NULL DEFAULT 0 COMMENT '外部版本号：每次外部操作版本号+1，内部操作不处理' AFTER `modified_user_guid`;