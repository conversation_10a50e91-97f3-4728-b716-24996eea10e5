-- 销售明细
-- 已结账订单
SELECT
    temp.store_name as "门店名称",
    temp.order_no as "订单号",
    temp.name as "商品名称【规格】",
    temp.code as "SKU简码",
    temp.item_type_name as "商品分类",
    CASE
        WHEN temp.item_type = 1 THEN '套餐'
        WHEN temp.item_type = 2 THEN '多规格'
        WHEN temp.item_type = 3 THEN '称重'
        WHEN temp.item_type = 4 THEN '单品'
        WHEN temp.item_type = 5 THEN '团餐'
    END as "商品类型",
    temp.current_count as "销售数量",
    temp.free_count as "赠送数量",
    temp.return_count as "退菜数量",
    temp.reason as "赠菜/退菜原因",
    temp.unit as "记数单位",
    temp.price as "单价",
    temp.price *(
        temp.current_count + temp.free_count
    ) AS "商品总额",
    temp.append_fee as "附加费",
    temp.attr_total as "属性加价",
    temp.member_price as "会员价格",
    DATE_FORMAT( temp.gmt_create, '%Y/%m/%d %T' ) as "下单时间",
    DATE_FORMAT( temp.checkout_time, '%Y/%m/%d %T' ) as "结账时间",
    CASE
        WHEN temp.trade_mode = 1 THEN '快餐'
        WHEN temp.trade_mode = 0 THEN '正餐'
    END as "正餐或快餐",
    temp.state as "订单状态",
    temp.create_staff_name as "下单操作员",
    temp.attr_name as "属性"
FROM (
    SELECT
        i.guid as order_item_guid,
        o.store_name,
        o.order_no,
        IF(i.sku_name IS NOT null AND i.sku_name <> '',CONCAT(i.item_name,'【',i.sku_name,'】'),i.item_name) AS name,
        i.code,
        i.item_type_name,
        i.item_type,
        i.current_count - i.refund_count + i.free_refund_count as current_count,
        i.free_count - i.free_refund_count as free_count,
        i.return_count + i.refund_count as return_count,
        GROUP_CONCAT(fr.reason) as reason,
        i.unit,
        i.price,
        o.append_fee,
        i.attr_total,
        i.member_price,
        o.gmt_create,
        o.checkout_time,
        o.trade_mode,
        o.state,
        o.create_staff_name,
        GROUP_CONCAT( DISTINCT attr.attr_name ) as attr_name
    FROM
        hst_order_item i
        LEFT JOIN hst_order o ON i.order_guid = o.guid
        LEFT JOIN hst_free_return_item fr ON fr.order_item_guid = i.guid
        LEFT JOIN hst_item_attr attr on attr.order_item_guid = i.guid
    WHERE
        o.state = 4 and o.recovery_type IN ( 1, 3 )
        AND i.is_delete = 0
        AND i.parent_item_guid = 0
        and o.copy_order_guid = 0
        AND i.item_guid NOT IN ( 6617780813696073728, 6622454990583627776, 6665469858576072704, 6683296207357345792
        , 6716295874126807040, 6721589404856483840, 6721590871914971136, 6810777164678430720, 6839738172407021568
        -- 2022-07-14 何东要求新增梅见盖子、二两郎酒盖子、二两江小白盖子、福佳白盖子、科罗娜盖子
        ,6952445666861776896,6952445783408902144,6952445874039422976,6952445958445596672,6952446087391084544
        )
         [[ and o.checkout_time between {{START_TIME}} and {{END_TIME}} ]]
    group by i.guid
) temp
order by temp.order_item_guid asc, temp.gmt_create asc


