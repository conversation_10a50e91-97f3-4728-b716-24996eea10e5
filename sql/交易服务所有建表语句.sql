-- -----------------------------------------------------
-- Schema hst_trade
-- -----------------------------------------------------
--   交易服务
-- -----------------------------------------------------
-- Table `hst_append_fee`
-- -----------------------------------------------------
CREATE TABLE `hst_append_fee` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` bigint(20) NOT NULL COMMENT '订单guid',
  `amount` decimal(15,2) DEFAULT '0.00' COMMENT '金额',
  `name` varchar(50) DEFAULT NULL COMMENT '名字',
  `type` int(10) DEFAULT NULL COMMENT '收费方式。0=按人，1=按桌。',
  `unit_price` decimal(15,2) DEFAULT NULL COMMENT '单价',
  `area_guid` varchar(50) DEFAULT NULL COMMENT '区域guid',
  `store_guid` varchar(50) NOT NULL COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `staff_guid` varchar(50) DEFAULT NULL COMMENT '操作人guid',
  `staff_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  PRIMARY KEY (`guid`),
  KEY `idx_store_guid` (`store_guid`),
  KEY `idx_order_guid` (`order_guid`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附加费记录';

-- -----------------------------------------------------
-- Table `hst_discount`
-- -----------------------------------------------------
CREATE TABLE `hst_discount` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` bigint(20) NOT NULL COMMENT '订单guid',
  `store_guid` varchar(50) NOT NULL COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `order_item_guid` bigint(20) DEFAULT NULL COMMENT '订单商品guid',
  `coupons_guid` varchar(512) DEFAULT NULL COMMENT '优惠券guid数组',
  `discount_name` varchar(50) DEFAULT NULL COMMENT '折扣方式名字',
  `discount_type` int(10) DEFAULT NULL COMMENT '1-会员折扣，2-整单折扣，3-整单让价,4-系统省零，5-赠送优惠，6-团购验券，7-会员优惠券,8-积分抵扣',
  `discount` decimal(10,2) DEFAULT NULL COMMENT '打几折（整单折扣和会员折扣）',
  `rule` varchar(4069) DEFAULT NULL COMMENT '折扣规则',
  `discount_fee` decimal(10,2) DEFAULT '0.00' COMMENT '折扣总额',
  `discount_state` int(11) DEFAULT '0' COMMENT '折扣状态 0表示正常，1表示反结账折扣',
  `staff_guid` varchar(50) DEFAULT NULL COMMENT '操作人guid',
  `staff_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  PRIMARY KEY (`guid`),
  KEY `idx_store_guid` (`store_guid`),
  KEY `uq_order_guid` (`order_guid`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单优惠记录';

-- -----------------------------------------------------
-- Table `hst_free_return_item`
-- -----------------------------------------------------
CREATE TABLE `hst_free_return_item` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_item_guid` bigint(20) NOT NULL COMMENT '订单商品guid',
  `item_type` int(11) DEFAULT '0' COMMENT '商品类型(1.套餐主项，2.规格，3.称重，4.单品 )',
  `item_guid` varchar(50) NOT NULL COMMENT '商品guid',
  `item_name` varchar(50) NOT NULL COMMENT '商品名称',
  `sku_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '商品的规格',
  `sku_name` varchar(20) NOT NULL DEFAULT '' COMMENT '规格名称',
  `price` decimal(15,2) NOT NULL COMMENT 'sku价格',
  `total_discount_fee` decimal(15,2) DEFAULT '0.00' COMMENT '菜品优惠合计',
  `order_guid` bigint(20) NOT NULL COMMENT '订单guid（反结账取新生成的）',
  `type` int(11) unsigned DEFAULT NULL COMMENT '类型（1：退货，2：赠送）',
  `urge_num` int(11) DEFAULT '0' COMMENT '催品次数',
  `item_state` int(11) DEFAULT '0' COMMENT '商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂，8.已上菜(已划菜) ，9.预定)',
  `is_free` int(11) DEFAULT NULL COMMENT '标记退货是否为赠送（0：否，1：是）',
  `reason` varchar(200) DEFAULT NULL COMMENT '赠送原因',
  `count` decimal(12,3) NOT NULL DEFAULT '0.000' COMMENT '赠送数量',
  `serve_count` decimal(12,3) DEFAULT '0.000' COMMENT '赠送划菜数量',
  `staff_guid` varchar(50) DEFAULT NULL COMMENT '操作人guid',
  `staff_name` varchar(50) DEFAULT NULL COMMENT '操作人名字',
  `store_guid` varchar(50) NOT NULL COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  PRIMARY KEY (`guid`),
  KEY `idx_order_guid` (`order_guid`),
  KEY `idx_store_sku` (`store_guid`,`sku_guid`),
  KEY `idx_order_item_guid` (`order_item_guid`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='赠送/退货记录';



-- -----------------------------------------------------
-- Table `hst_groupon`
-- -----------------------------------------------------
CREATE TABLE `hst_groupon` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` bigint(20) NOT NULL COMMENT '订单guid',
  `amount` decimal(15,2) DEFAULT '0.00' COMMENT '金额',
  `name` varchar(50) DEFAULT NULL COMMENT '名字',
  `code` varchar(50) DEFAULT NULL COMMENT '券码',
  `store_guid` varchar(50) NOT NULL COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `staff_guid` varchar(50) DEFAULT NULL COMMENT '操作人guid',
  `staff_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  PRIMARY KEY (`guid`),
  KEY `idx_store_guid` (`store_guid`),
  KEY `idx_order_guid` (`order_guid`),
  KEY `idx_code` (`code`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团购券';

-- -----------------------------------------------------
-- Table `hst_item_attr`
-- -----------------------------------------------------
CREATE TABLE `hst_item_attr` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` bigint(20) NOT NULL COMMENT '订单guid',
  `order_item_guid` bigint(20) NOT NULL COMMENT '订单商品guid',
  `attr_guid` varchar(50) NOT NULL COMMENT '属性guid',
  `attr_name` varchar(50) NOT NULL COMMENT '属性名称',
  `attr_group_guid` varchar(50) NOT NULL COMMENT '属性组guid',
  `attr_group_name` varchar(50) DEFAULT '' COMMENT '属性组名称',
  `attr_price` decimal(10,2) NOT NULL COMMENT '属性价格',
  `num` int(11) DEFAULT '0' COMMENT '数量',
  `store_guid` varchar(50) NOT NULL COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  PRIMARY KEY (`guid`),
  KEY `idx_order_item_guid` (`order_item_guid`),
  KEY `idx_store_attr_group_attr` (`store_guid`,`attr_group_guid`,`attr_guid`),
  KEY `idx_order_guid` (`order_guid`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品属性';

-- -----------------------------------------------------
-- Table `hst_order`
-- -----------------------------------------------------
CREATE TABLE `hst_order` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号(前端显示用，门店内唯一，格式************)',
  `trade_mode` int(11) DEFAULT NULL COMMENT '交易模式(0：正餐，1：快餐)',
  `device_type` int(11) DEFAULT NULL COMMENT '设备类型(订单来源BaseDeviceTypeEnum)',
  `guest_count` int(11) DEFAULT '0' COMMENT '客人数',
  `business_day` date DEFAULT NULL COMMENT '营业日',
  `dining_table_guid` varchar(50) DEFAULT NULL COMMENT '桌台guid',
  `dining_table_name` varchar(200) DEFAULT NULL COMMENT '桌台名称',
  `virtual_table` int(11) DEFAULT '0' COMMENT '是否虚拟台(0:否，1:是)',
  `cancel_reason` varchar(100) DEFAULT NULL COMMENT '作废原因',
  `remark` varchar(200) DEFAULT NULL COMMENT '整单备注',
  `mark` varchar(50) DEFAULT NULL COMMENT '快餐牌号',
  `print_pre_bill_num` int(11) DEFAULT '0' COMMENT '预结单打印次数',
  `order_fee_for_combine` decimal(15,2) DEFAULT NULL COMMENT '订单金额，只用于桌台页面展示',
  `order_fee` decimal(15,2) DEFAULT '0.00' COMMENT '订单金额（商品总额+附加费）',
  `append_fee` decimal(15,2) DEFAULT '0.00' COMMENT '附加费',
  `change_fee` decimal(15,2) DEFAULT '0.00' COMMENT '找零（收款-应收金额）',
  `actually_pay_fee` decimal(15,2) DEFAULT '0.00' COMMENT '实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））',
  `prepay_fee` decimal(15,2) DEFAULT '0.00' COMMENT '预付金（反结账原单聚合支付转入）',
  `reserve_fee` decimal(15,2) DEFAULT '0.00' COMMENT '定金',
  `state` int(11) DEFAULT NULL COMMENT '1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废',
  `recovery_type` int(11) DEFAULT NULL COMMENT '订单反结账类型1：普通单 2：原单 3：新单 4：退单 ',
  `upper_state` int(11) DEFAULT '0' COMMENT '0:无子单，1:主单， 2:子单',
  `main_order_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '主单guid',
  `reserve_guid` varchar(50) DEFAULT NULL COMMENT '预定单guid',
  `member_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '会员guid',
  `member_consumption_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '会员支付id，反结账传给会员系统',
  `member_card_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '会员卡id',
  `member_phone` varchar(50) DEFAULT NULL COMMENT '会员电话',
  `member_name` varchar(50) DEFAULT NULL COMMENT '会员名字',
  `user_wx_public_open_id` varchar(128) NOT NULL DEFAULT '0' COMMENT '用户微信公众号openId',
  `original_order_guid` varchar(50) DEFAULT NULL COMMENT '反结账原单的guid',
  `recovery_id` varchar(50) DEFAULT NULL COMMENT '反结账id(多次反结账所有原单和新单此id相同)',
  `recovery_reason` varchar(100) DEFAULT NULL COMMENT '反结账原因',
  `recovery_device_type` int(11) DEFAULT NULL COMMENT '反结账设备类型（BaseDeviceTypeEnum）',
  `checkout_device_type` int(11) DEFAULT NULL COMMENT '结账设备类型（BaseDeviceTypeEnum）',
  `calculate_by_member_price` int(11) NOT NULL DEFAULT '0' COMMENT '计算是是否使用会员价（1：使用，0：不使用）',
  `cancel_device_type` int(11) DEFAULT NULL COMMENT '取消订单设备类型（BaseDeviceTypeEnum）',
  `store_guid` varchar(50) NOT NULL COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `checkin_time` datetime DEFAULT NULL COMMENT '开台时间',
  `checkout_time` datetime DEFAULT NULL COMMENT '结算时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '作废时间',
  `create_staff_guid` varchar(50) DEFAULT NULL COMMENT '创建操作人guid',
  `create_staff_name` varchar(50) DEFAULT NULL COMMENT '创建操作人name',
  `recovery_staff_guid` varchar(50) DEFAULT NULL COMMENT '反结账操作人guid',
  `recovery_staff_name` varchar(50) DEFAULT NULL COMMENT '反结账操作人name',
  `checkout_staff_guid` varchar(50) DEFAULT NULL COMMENT '结账操作人guid',
  `checkout_staff_name` varchar(50) DEFAULT NULL COMMENT '结账操作人name',
  `cancel_staff_guid` varchar(50) DEFAULT NULL COMMENT '作废订单操作人guid',
  `cancel_staff_name` varchar(50) DEFAULT NULL COMMENT '作废订单操作人name',
  `is_handle` int(11) DEFAULT NULL COMMENT '是否已处理订单销售明细数据标识（null未处理，0异常未处理，1已处理）',
  `is_updated_es` tinyint(1) DEFAULT '0' COMMENT '是否同步更新到es0未同步 1已经同步',
  PRIMARY KEY (`guid`),
  KEY `idx_user_wx_public_open_id` (`user_wx_public_open_id`),
  KEY `idx_member_guid` (`member_guid`),
  KEY `idx_main_order_guid` (`main_order_guid`),
  KEY `idx_store_guid_order_no` (`store_guid`,`order_no`),
  KEY `idx_recovery_id` (`recovery_id`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE,
  KEY `idx_checkout_time` (`checkout_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单';

-- -----------------------------------------------------
-- Table `hst_order_abnormal_record`
-- -----------------------------------------------------
CREATE TABLE `hst_order_abnormal_record` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `order_guid` varchar(45) NOT NULL COMMENT '订单guid',
  `dining_method_id` int(11) DEFAULT NULL COMMENT '用餐方式id 0.堂食 1.快餐',
  `payment_method_id` int(11) DEFAULT NULL COMMENT '支付方式id 1.微信 2.支付宝',
  `payment_method_name` varchar(45) DEFAULT NULL COMMENT '支付方式名称',
  `payment_statu` int(11) DEFAULT NULL COMMENT '支付状态 2：异常 3：失败 4：成功',
  `checkout_time` datetime DEFAULT NULL COMMENT '支付时间：支付记录的发起时间',
  `payment_amount` decimal(15,2) DEFAULT '0.00' COMMENT '支付金额',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `abnormal_message` varchar(255) DEFAULT NULL COMMENT '异常信息',
  `pay_guid` varchar(45) DEFAULT NULL,
  `other_payments` varchar(4096) DEFAULT NULL COMMENT '其他支付的参数',
  `store_guid` varchar(50) NOT NULL COMMENT '门店guid',
  PRIMARY KEY (`guid`),
  KEY `idx_order_guid` (`order_guid`),
  KEY `idx_checkout_time` (`checkout_time`),
  KEY `idx_store_guid` (`store_guid`) USING BTREE,
  KEY `idx_gmt_create` (`gmt_create`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单异常记录表';

-- -----------------------------------------------------
-- Table `hst_order_item`
-- -----------------------------------------------------
CREATE TABLE `hst_order_item` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` bigint(20) NOT NULL COMMENT '订单guid',
  `user_wx_public_open_id` varchar(128) NOT NULL DEFAULT '0' COMMENT '用户微信公众号openId',
  `wx_batch` varchar(50) NOT NULL DEFAULT '0' COMMENT '微信菜品批次',
  `item_guid` varchar(50) NOT NULL COMMENT '商品guid',
  `item_name` varchar(50) NOT NULL COMMENT '商品名称',
  `code` varchar(50) DEFAULT NULL COMMENT '商品编号',
  `item_type_guid` varchar(50) NOT NULL COMMENT '商品类别guid',
  `item_type_name` varchar(50) DEFAULT NULL COMMENT '商品类别名称',
  `subgroup_guid` varchar(50) DEFAULT NULL COMMENT '套餐分组guid',
  `subgroup_name` varchar(50) DEFAULT NULL COMMENT '套餐分组名字',
  `sku_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '商品的规格',
  `sku_name` varchar(20) NOT NULL DEFAULT '' COMMENT '规格名称',
  `price` decimal(15,2) NOT NULL COMMENT 'sku价格',
  `original_price` decimal(15,2) DEFAULT NULL COMMENT '原价',
  `discount_percent` int(11) DEFAULT NULL COMMENT '折扣 （70代表7折）',
  `member_price` decimal(15,2) DEFAULT NULL COMMENT '会员价格',
  `total_discount_fee` decimal(15,2) DEFAULT '0.00' COMMENT '菜品优惠合计',
  `has_attr` int(11) unsigned DEFAULT NULL COMMENT '是否有属性（0：否，1：是）',
  `attr_total` decimal(15,2) DEFAULT '0.00' COMMENT '属性总价',
  `original_order_item_guid` bigint(20) DEFAULT NULL COMMENT '反结账原菜品guid',
  `parent_item_guid` bigint(20) NOT NULL DEFAULT '0' COMMENT '套餐主项guid',
  `item_type` int(11) DEFAULT '0' COMMENT '商品类型(1.套餐主项，2.规格，3.称重，4.单品 )',
  `item_state` int(11) DEFAULT '0' COMMENT '商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂，8.已上菜(已划菜) ，9.预定)',
  `urge_num` int(11) DEFAULT '0' COMMENT '催品次数',
  `current_count` decimal(12,3) DEFAULT '0.000' COMMENT '当前数量（不包括赠送，不要重复减赠送折扣）',
  `free_count` decimal(12,3) DEFAULT '0.000' COMMENT '赠送数量（销售统计=当前+赠送）',
  `return_count` decimal(12,3) DEFAULT '0.000' COMMENT '退货数量（赠送变为退之后不计入销售和增菜统计）',
  `package_default_count` decimal(12,3) DEFAULT '0.000' COMMENT '套餐预设数量',
  `add_price` decimal(15,2) DEFAULT '0.00' COMMENT '子项加价',
  `unit` varchar(50) DEFAULT '' COMMENT '计数单位',
  `is_zero` int(11) unsigned DEFAULT NULL COMMENT '是否0份售卖（0：否，1：是）',
  `is_pay` int(11) unsigned DEFAULT NULL COMMENT '是否反结账已支付（0：否，1：是）',
  `is_whole_discount` int(11) unsigned DEFAULT NULL COMMENT '是否参与整单折扣(0：否，1：是)',
  `is_member_discount` int(11) unsigned DEFAULT NULL COMMENT '是否参与会员折扣（0：否，1：是）',
  `price_change_type` int(11) NOT NULL DEFAULT '0' COMMENT '改价类型（0：未改价，1：商品改价。2：商品折扣）',
  `remark` varchar(255) DEFAULT NULL COMMENT '商品备注',
  `store_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `create_staff_guid` varchar(50) DEFAULT NULL COMMENT '创建操作人guid',
  `create_staff_name` varchar(50) DEFAULT NULL COMMENT '创建操作人guid',
  PRIMARY KEY (`guid`),
  KEY `idx_order_guid` (`order_guid`),
  KEY `idx_store_sku` (`store_guid`,`sku_guid`),
  KEY `idx_parent_item_guid` (`parent_item_guid`),
  KEY `idx_wx_id` (`user_wx_public_open_id`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品';

-- -----------------------------------------------------
-- Table `hst_order_item_record`
-- -----------------------------------------------------
CREATE TABLE `hst_order_item_record` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `is_from_recovery` tinyint(1) DEFAULT NULL COMMENT '是否是反结账之后的操作（1:是）',
  `order_guid` bigint(20) NOT NULL COMMENT '订单guid',
  `user_wx_public_open_id` varchar(128) NOT NULL DEFAULT '0' COMMENT '用户微信公众号openId',
  `wx_batch` varchar(50) NOT NULL DEFAULT '0' COMMENT '微信菜品批次',
  `order_item_guid` varchar(50) NOT NULL COMMENT '订单菜品表的hst_order_item Guid',
  `type` tinyint(1) NOT NULL COMMENT '(1点菜；2赠菜；3退菜)',
  `item_guid` varchar(50) DEFAULT NULL COMMENT '商品guid',
  `item_name` varchar(50) NOT NULL COMMENT '商品名称',
  `log` varchar(500) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL COMMENT '商品编号',
  `item_type_guid` varchar(50) DEFAULT NULL COMMENT '商品类别guid',
  `item_type_name` varchar(50) DEFAULT NULL COMMENT '商品类别名称',
  `subgroup_guid` varchar(50) DEFAULT NULL COMMENT '套餐分组guid',
  `subgroup_name` varchar(50) DEFAULT NULL COMMENT '套餐分组名字',
  `sku_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '商品的规格',
  `sku_name` varchar(20) NOT NULL DEFAULT '' COMMENT '规格名称',
  `price` decimal(15,2) NOT NULL COMMENT 'sku价格',
  `original_price` decimal(15,2) DEFAULT NULL COMMENT '原价',
  `discount_percent` int(11) DEFAULT NULL COMMENT '折扣 （70代表7折）',
  `member_price` decimal(15,2) DEFAULT NULL COMMENT '会员价格',
  `total_discount_fee` decimal(15,2) DEFAULT '0.00' COMMENT '菜品优惠合计',
  `has_attr` int(11) unsigned DEFAULT NULL COMMENT '是否有属性（0：否，1：是）',
  `attr_total` decimal(15,2) DEFAULT '0.00' COMMENT '属性总价',
  `original_order_item_guid` bigint(20) DEFAULT NULL COMMENT '反结账原菜品guid',
  `parent_item_guid` bigint(20) NOT NULL DEFAULT '0' COMMENT '套餐主项guid',
  `item_type` int(11) DEFAULT '0' COMMENT '商品类型(1.套餐主项，2.规格，3.称重，4.单品 )',
  `item_state` int(11) DEFAULT '0' COMMENT '商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂，8.已上菜(已划菜) ，9.预定)',
  `urge_num` int(11) DEFAULT '0' COMMENT '催品次数',
  `current_count` decimal(12,3) DEFAULT '0.000' COMMENT '点菜数量（当次操作点菜数量）',
  `free_count` decimal(12,3) DEFAULT '0.000' COMMENT '赠送数量（当次操作赠送数量）',
  `return_count` decimal(12,3) DEFAULT '0.000' COMMENT '退货数量（当次操作退货数量）',
  `package_default_count` decimal(12,3) DEFAULT '0.000' COMMENT '套餐预设数量',
  `add_price` decimal(15,2) DEFAULT '0.00' COMMENT '子项加价',
  `unit` varchar(50) DEFAULT '' COMMENT '计数单位',
  `is_zero` int(11) unsigned DEFAULT NULL COMMENT '是否0份售卖（0：否，1：是）',
  `is_pay` int(11) unsigned DEFAULT NULL COMMENT '是否反结账已支付（0：否，1：是）',
  `is_free` int(11) DEFAULT NULL COMMENT '''标记退货是否为赠送（0：否，1：是）''',
  `reason` varchar(200) DEFAULT NULL,
  `is_whole_discount` int(11) unsigned DEFAULT NULL COMMENT '是否参与整单折扣(0：否，1：是)',
  `is_member_discount` int(11) unsigned DEFAULT NULL COMMENT '是否参与会员折扣（0：否，1：是）',
  `price_change_type` int(11) NOT NULL DEFAULT '0' COMMENT '改价类型（0：未改价，1：商品改价。2：商品折扣）',
  `remark` varchar(255) DEFAULT NULL COMMENT '商品备注',
  `store_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `create_staff_guid` varchar(50) DEFAULT NULL COMMENT '创建操作人guid',
  `create_staff_name` varchar(50) DEFAULT NULL COMMENT '创建操作人guid',
  PRIMARY KEY (`guid`),
  KEY `idx_order_guid` (`order_guid`),
  KEY `idx_store_sku` (`store_guid`,`sku_guid`),
  KEY `idx_parent_item_guid` (`parent_item_guid`),
  KEY `idx_wx_id` (`user_wx_public_open_id`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE,
  KEY `idx_order_item_guid` (`order_item_guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品流水表';

-- -----------------------------------------------------
-- Table `hst_order_subsidiary`
-- -----------------------------------------------------
CREATE TABLE `hst_order_subsidiary` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `local_upload_id` varchar(128) NOT NULL DEFAULT '' COMMENT '本地化上传id(服务器生成的为原订单guid，本地化生成的为uuid)',
  `third_party_id` varchar(128) NOT NULL DEFAULT '' COMMENT '第三方订单id',
  `reserve_order_state` int(11) DEFAULT NULL COMMENT '预点餐状态（1：预点餐未下单，2：预点餐已下单）',
  PRIMARY KEY (`guid`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单辅助表';

-- -----------------------------------------------------
-- Table `hst_transaction_record`
-- -----------------------------------------------------
CREATE TABLE `hst_transaction_record` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` bigint(20) NOT NULL COMMENT '订单guid',
  `app_id` varchar(50) DEFAULT NULL COMMENT '聚合支付商户的appId',
  `pay_power_id` int(11) DEFAULT NULL COMMENT '聚合支付对应的支付方式',
  `pay_power_name` varchar(50) DEFAULT NULL COMMENT '聚合支付对应的支付方式名称',
  `item_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `body` varchar(50) DEFAULT NULL COMMENT '商品信息',
  `description` varchar(128) DEFAULT NULL COMMENT '支付单附加描述',
  `terminal_id` varchar(50) DEFAULT NULL COMMENT '设备号',
  `jh_order_guid` varchar(50) DEFAULT NULL COMMENT '聚合支付订单号',
  `bank_transaction_id` varchar(50) DEFAULT '' COMMENT '银行流水号（人脸支付流水号）',
  `code_url` varchar(200) DEFAULT NULL COMMENT '支付二维码链接地址',
  `amount` decimal(15,2) DEFAULT '0.00' COMMENT '交易金额',
  `discount_fee` decimal(15,2) DEFAULT '0.00' COMMENT '聚合支付优惠金额',
  `refundable_fee` decimal(15,2) DEFAULT '0.00' COMMENT '剩余可退款金额',
  `trade_type` int(11) DEFAULT NULL COMMENT '交易类型，1:正常支付转入，2:会员充值转入，3:预付金转入，4:定金转入，5:正常支付退款，6:退单退款，7:会员余额退款，8:预付金退款，9:定金退款，10:自定义退款',
  `state` int(11) DEFAULT NULL COMMENT '1：待支付 2：支付中 3：支付失败 4：支付成功',
  `payment_type` int(11) NOT NULL COMMENT '支付方式 1：现金支付，2：聚合支付，3：银行卡支付，4:会员卡支付，5:人脸支付，6:通吃岛支付，7:预定金支付，10：其他支付方式',
  `payment_type_name` varchar(50) DEFAULT NULL COMMENT '支付方式名',
  `create_time` datetime NOT NULL COMMENT '支付单创建时间',
  `member_guid` varchar(50) DEFAULT '' COMMENT '会员相关信息',
  `business_day` date DEFAULT NULL COMMENT '营业日时间',
  `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
  `remark` varchar(128) DEFAULT NULL COMMENT '备注',
  `face_code` varchar(20) DEFAULT NULL COMMENT '人脸支付失败时安卓自己随机加的3位数字',
  `staff_guid` varchar(50) DEFAULT NULL COMMENT '操作人员guid',
  `staff_name` varchar(50) DEFAULT NULL COMMENT '操作人名字',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名',
  `store_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '门店guid',
  PRIMARY KEY (`guid`),
  KEY `idx_order_guid` (`order_guid`),
  KEY `idx_store_guid` (`store_guid`),
  KEY `idx_business_day` (`business_day`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单交易记录';
