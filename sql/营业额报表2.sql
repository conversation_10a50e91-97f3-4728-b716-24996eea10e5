select
    temp.营业日,
    temp.创建时间,
    temp.品牌,
    temp.门店名称,
    temp.order_id "外卖订单ID",
    temp.订单状态,
    temp.订单子类型,
    temp.third_sku_id "外卖商品SKU",
    temp.商品名称,
    temp.商品规格,
    temp.商品属性,
    temp.商品单位,
    temp.商品单价,
    temp.商品数量,
    temp.商品消费合计,
    temp.餐盒单价,
    temp.餐盒数量,
    temp.餐盒金额,
    temp.门店商品编码,
    temp.门店商品名称,
    temp.门店商品单价,
    temp.门店商品数量,
    temp.门店商品金额合计,
    temp.核算单价,
    temp.核算商品总额
from
(
-- 外卖订单菜品销售明细表
SELECT
    o.order_guid,
    i.item_guid as order_item_guid,
	o.business_day::text "营业日",
	i.gmt_create::text "创建时间",
	case o.brand_guid
	    when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' then '何师烧烤'
	    when '95a8319b-8297-4f06-a39f-a3c08799dad6' then '玉米熊'
	    when '6919138141420912640' then '家婆抄手'
	end as "品牌",
	o.store_guid "门店GUID",
	o.store_name "门店名称",
	o.order_id,
    CASE o.order_status
		WHEN -1 THEN '已取消'
		WHEN 0 THEN '待处理'
		WHEN 10 THEN '已接单'
		WHEN 20 THEN '配送中'
		WHEN 30 THEN '配送完成'
		WHEN 100 THEN '已完成'
	END AS "订单状态",
    CASE o.order_sub_type
		WHEN 0 THEN '美团'
		WHEN 1 THEN	'饿了么'
		WHEN 6 THEN	'赚餐外卖'
	END AS "订单子类型",
	i.item_guid "商品GUID",
	i.third_sku_id,
	i.item_name "商品名称",
	i.item_spec "商品规格",
	i.item_property "商品属性",
	i.item_unit "商品单位",
	case {{VIEW_TYPE}}
		when '0' then i.item_price
		when '1' then (
			case
				when p.takeout_item_guid isnull then i.item_price
				else p.sale_price
			end
		)
	end "商品单价",
	case {{VIEW_TYPE}}
		when '0' then i.item_count
		when '1' then (
			case
				when p.takeout_item_guid isnull then i.item_count
				else p.item_count * i.actual_item_count
			end
		)
	end "商品数量",
	case {{VIEW_TYPE}}
		when '0' then i.item_total
		when '1' then (
			case
				when p.takeout_item_guid isnull then i.item_total
				else p.item_count * i.actual_item_count * p.sale_price
			end
		)
	end "商品消费合计",
	i.box_price "餐盒单价",
	i.box_count "餐盒数量",
	i.box_total "餐盒金额",
	case {{VIEW_TYPE}}
		when '0' then hs.code
		when '1' then (
			case
				when p.takeout_item_guid isnull then hs.code
				else p.code
			end
		)
	end "门店商品编码",
	case {{VIEW_TYPE}}
		when '0' then i.erp_item_name
		when '1' then (
			case
				when p.takeout_item_guid isnull then i.erp_item_name
				else p.item_name
			end
		)
	end "门店商品名称",
	case {{VIEW_TYPE}}
		when '0' then i.erp_item_price
		when '1' then (
			case
				when p.takeout_item_guid isnull then i.erp_item_price
				else p.sale_price
			end
		)
	end "门店商品单价",
	case {{VIEW_TYPE}}
		when '0' then i.actual_item_count
		when '1' then (
			case
				when p.takeout_item_guid isnull then i.actual_item_count
				else p.item_count * i.actual_item_count
			end
		)
	end "门店商品数量",
	case {{VIEW_TYPE}}
		when '0' then i.erp_item_price * i.actual_item_count
		when '1' then (
			case
				when p.takeout_item_guid isnull then i.erp_item_price * i.actual_item_count
				else p.item_count * i.actual_item_count * p.sale_price
			end
		)
	end "门店商品金额合计",
	case {{VIEW_TYPE}}
		when '0' then i.takeaway_accounting_price
		when '1' then (
			case
				when p.takeout_item_guid isnull then i.takeaway_accounting_price
				else p.accounting_price
			end
		)
	end 核算单价,
	case {{VIEW_TYPE}}
		when '0' then coalesce(i.takeaway_accounting_price,0) * coalesce(i.actual_item_count,0)
		when '1' then (
			case
				when p.takeout_item_guid isnull then coalesce(i.takeaway_accounting_price,0) * coalesce(i.actual_item_count,0)
				else coalesce(p.accounting_price,0) * coalesce(i.actual_item_count,0) * coalesce(p.item_count,0)
			end
		)
	end 核算商品总额
FROM
	"hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_item i
	left join (
		select
			rowid,
			takeout_item_guid,
			item_count,
			item_name,
			sku_guid,
			sale_price,
			accounting_price,
			code
		from (
			select
				row_number() over(partition by takeout_item_guid) rowid,
				takeout_item_guid,
				item_count,
				item_name,
				sku_guid,
				sale_price,
				accounting_price,
				code
			from "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_package
			where
				is_delete = 0
				and ods_delete_time isnull
				-- and takeout_item_guid = '2303091042020830009'
		) x
		where
			case {{VIEW_TYPE}}
				when '0' then rowid = 1
				when '1' then true
			end
	) p on p.takeout_item_guid = i.item_guid
	LEFT JOIN "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order o ON i.order_guid = o.order_guid
	left join "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_sku hs on i.erp_item_sku_guid = hs.guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
	acl.chmod
	-- and i.gmt_create between date_trunc('day',{ {START_DATE} } - interval '30 day') and date_trunc('day',{ {END_DATE} } + interval '7 day')
	-- and o.gmt_create between date_trunc('day',{ {START_DATE} } - interval '30 day') and date_trunc('day',{ {END_DATE} } + interval '7 day')
    -- and o.is_refund_success = 0
	[[and o.business_day between {{START_DATE}} AND {{END_DATE}}]]
	[[and i.business_day between {{START_DATE}} AND {{END_DATE}}]]
	[[and o.brand_guid in ({{BRAND_MULTI}}) ]]
    and case acl.list
        when 'true' then true
        when 'false' then false
        -- when 'false' then true  --debug
        else (o.store_guid = any(acl.list::text[]))
    end
	[[and o.order_status in ({{ORDER_STATUS_MULTI}})]]
	[[and o.refund_status in ({{REFUND_STATUS_MULTI}})]]
    [[and o.order_id ~* {{SEARCH}}]]

union all

-- 查询退款
SELECT
    o.order_guid,
    i.item_guid as order_item_guid,
	o.business_day::text "营业日",
	i.gmt_create::text "创建时间",
    case o.brand_guid
        when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' then '何师烧烤'
        when '95a8319b-8297-4f06-a39f-a3c08799dad6' then '玉米熊'
        when '6919138141420912640' then '家婆抄手'
    end as "品牌",
	o.store_guid "门店GUID",
	o.store_name "门店名称",
	o.order_id,
    '已退款' AS "订单状态",
    CASE o.order_sub_type
		WHEN 0 THEN '美团'
		WHEN 1 THEN	'饿了么'
		WHEN 6 THEN	'赚餐外卖'
	END AS "订单子类型",
	i.item_guid "商品GUID",
	i.third_sku_id,
	i.item_name "商品名称",
	i.item_spec "商品规格",
	i.item_property "商品属性",
	i.item_unit "商品单位",
    case {{VIEW_TYPE}}
        when '0' then i.item_price
        when '1' then (
            case
                when p.takeout_item_guid isnull then i.item_price
                else p.sale_price
            end
        )
    end "商品单价",
    case {{VIEW_TYPE}}
        when '0' then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end)
        when '1' then (
            case
                when p.takeout_item_guid isnull then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end)
                else p.item_count * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
            end
        )
    end "商品数量",
    case {{VIEW_TYPE}}
        when '0' then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * i.item_price
        when '1' then (
            case
                when p.takeout_item_guid isnull then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * i.item_price
                else p.item_count * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count) * p.sale_price
            end
        )
    end "商品消费合计",
    i.box_price "餐盒单价",
    i.box_count "餐盒数量",
    i.box_total "餐盒金额",
    case {{VIEW_TYPE}}
        when '0' then hs.code
        when '1' then (
            case
                when p.takeout_item_guid isnull then hs.code
                else p.code
            end
        )
    end "门店商品编码",
    case {{VIEW_TYPE}}
        when '0' then i.erp_item_name
        when '1' then (
            case
                when p.takeout_item_guid isnull then i.erp_item_name
                else p.item_name
            end
        )
    end "门店商品名称",
    case {{VIEW_TYPE}}
        when '0' then i.erp_item_price
        when '1' then (
            case
                when p.takeout_item_guid isnull then i.erp_item_price
                else p.sale_price
            end
        )
    end "门店商品单价",
    case {{VIEW_TYPE}}
        when '0' then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
        when '1' then (
            case
                when p.takeout_item_guid isnull then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
                else p.item_count * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
            end
        )
    end "门店商品数量",
    case {{VIEW_TYPE}}
        when '0' then i.erp_item_price * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
        when '1' then (
            case
                when p.takeout_item_guid isnull then i.erp_item_price * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
                else p.item_count * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count) * p.sale_price
            end
        )
    end "门店商品金额合计",
    case {{VIEW_TYPE}}
        when '0' then i.takeaway_accounting_price
        when '1' then (
            case
                when p.takeout_item_guid isnull then i.takeaway_accounting_price
                else p.accounting_price
            end
        )
    end 核算单价,
    case {{VIEW_TYPE}}
        when '0' then coalesce(i.takeaway_accounting_price,0) * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
        when '1' then (
            case
                when p.takeout_item_guid isnull then coalesce(i.takeaway_accounting_price,0) * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
                else coalesce(p.accounting_price,0) * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count) * coalesce(p.item_count,0)
            end
        )
    end 核算商品总额
FROM
	"hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_item i
	left join (
		select
			rowid,
			takeout_item_guid,
			item_count,
			item_name,
			sku_guid,
			sale_price,
			accounting_price,
			code
		from (
			select
				row_number() over(partition by takeout_item_guid) rowid,
				takeout_item_guid,
				item_count,
				item_name,
				sku_guid,
				sale_price,
				accounting_price,
				code
			from "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_package
			where
				is_delete = 0
				and ods_delete_time isnull
				-- and takeout_item_guid = '2303091042020830009'
		) x
		where
			case {{VIEW_TYPE}}
				when '0' then rowid = 1
				when '1' then true
			end
	) p on p.takeout_item_guid = i.item_guid
	LEFT JOIN "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order o ON i.order_guid = o.order_guid
	left join "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_sku hs on i.erp_item_sku_guid = hs.guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
	acl.chmod
	-- and i.gmt_create between date_trunc('day',{ {START_DATE} } - interval '30 day') and date_trunc('day',{ {END_DATE} } + interval '7 day')
	-- and o.gmt_create between date_trunc('day',{ {START_DATE} } - interval '30 day') and date_trunc('day',{ {END_DATE} } + interval '7 day')
    -- and o.is_refund_success = 0
    and (i.refund_count > 0 or (o.order_status = '-1' and o.is_refund_success = 1 ))
	[[and o.business_day between {{START_DATE}} AND {{END_DATE}}]]
	[[and i.business_day between {{START_DATE}} AND {{END_DATE}}]]
	[[and o.brand_guid in ({{BRAND_MULTI}}) ]]
    and case acl.list
        when 'true' then true
        when 'false' then false
        -- when 'false' then true  --debug
        else (o.store_guid = any(acl.list::text[]))
    end
	[[and o.order_status in ({{ORDER_STATUS_MULTI}})]]
	[[and o.refund_status in ({{REFUND_STATUS_MULTI}})]]
    [[and o.order_id ~* {{SEARCH}}]]

union all

SELECT
    append.order_guid,
    append.order_item_guid,
    append.营业日,
    append.创建时间,
    append.品牌,
    append.store_guid as "门店GUID",
    append.门店名称,
    append.order_id,
    append.订单状态,
    append.订单子类型,
    append.sku_guid as "商品GUID",
    append.third_sku_id,
    append.商品名称,
    append.商品规格,
    append.商品属性,
    append.商品单位,
    append.商品单价,
    append.商品数量,
    append.商品消费合计,
    append.餐盒单价,
    append.餐盒数量,
    append.餐盒金额,
    append.门店商品编码,
    append.门店商品名称,
    append.门店商品单价,
    append.门店商品数量,
    append.门店商品金额合计,
    append.核算单价,
    append.核算商品总额
FROM(
SELECT
    o.guid::text as "order_guid",
    i.item_guid::text as order_item_guid,
	o.business_day::text "营业日",
	i.gmt_create::text "创建时间",
    case
        when o.store_name ~~* '%He%'  then '何师烧烤'
        when o.store_name ~~* '%YM%' then '玉米熊'
        when o.store_name ~~* '%家婆%' then '家婆抄手'
    end as "品牌",
	o.store_guid,
	o.store_name "门店名称",
	o.guid::text as "order_id",
    '已完成' AS "订单状态",
    r.payment_type_name AS "订单子类型",
	i.sku_guid,
	i.sku_guid as "third_sku_id",
	i.item_name "商品名称",
	i.sku_name "商品规格",
	'' "商品属性",
	i.unit "商品单位",
    i.price "商品单价",
    case i.parent_item_guid
        when '0' then i.current_count
        else i.current_count * i.package_default_count * ii.current_count
    end "商品数量",
	case i.parent_item_guid
		when '0' then i.price * (i.current_count + i.free_count)
		else i.price * (ii.current_count + ii.free_count)
	end "商品消费合计",
    0 "餐盒单价",
    0 "餐盒数量",
    0 "餐盒金额",
	case i.parent_item_guid
		when '0' then i.code
		else ii.code
	end "门店商品编码",
    i.item_name "门店商品名称",
    i.price "门店商品单价",
    case i.parent_item_guid
        when '0' then i.current_count
        else i.current_count * i.package_default_count * ii.current_count
    end "门店商品数量",
	case i.parent_item_guid
		when '0' then i.price * (i.current_count + i.free_count)
		else i.price * (ii.current_count + ii.free_count)
	end "门店商品金额合计",
    i.accounting_price 核算单价,
	case i.parent_item_guid
		when '0' then coalesce(i.accounting_price,0) * (i.current_count + i.free_count)
		else coalesce(i.accounting_price,0) * ((i.current_count + i.free_count) * i.package_default_count * ii.current_count)
	end 核算商品总额,
	acl.list
FROM
    "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_transaction_record r
    LEFT JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o ON r.order_guid = o.guid and o.is_delete = 0
	LEFT JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item i ON i.order_guid = o.guid and o.is_delete = 0
	left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item ii on i.parent_item_guid = ii.guid and ii.is_delete = 0,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
	acl.chmod
	and r.payment_type = 10
	and r.payment_type_name in ('美团生食闪购','抖音外卖')
	and r.is_delete = 0
	and o.state = 4
    and case {{VIEW_TYPE}}
        when '0' then i.parent_item_guid = 0
        when '1' then i.item_type != 1
    end
    [[and r.is_delete in ({{REFUND_STATUS_MULTI}}) and r.is_delete = 2]]
	[[and r.business_day between {{START_DATE}} AND {{END_DATE}}]]
	[[and o.business_day between {{START_DATE}} AND {{END_DATE}}]]
	[[and i.business_day between {{START_DATE}} AND {{END_DATE}}]]
	[[
	 and case
        when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (o.store_name ~~* '%He%' or o.store_name ~~* '%YM%' or o.store_name ~~* '%家婆%' )
        when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) then (o.store_name ~~* '%He%' or o.store_name ~~* '%YM%')
        when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (o.store_name ~~* '%He%' or o.store_name ~~* '%家婆%')
        when '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (o.store_name ~~* '%YM%' or o.store_name ~~* '%家婆%')
        when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) then o.store_name ~~* '%He%'
        when '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) then o.store_name ~~* '%YM%'
        when '6919138141420912640' in({{BRAND_MULTI}}) then o.store_name ~~* '%家婆%'
        else 1=1 end
	 ]]
	[[
	and case
	    when 100 in ({{ORDER_STATUS_MULTI}}) then o.state = 4
	    else o.state = -100 end
	]]
    [[and o.guid ~* {{SEARCH}}]]
) append
where
    1=1
    [[append.store_guid in ({{STORE_GUID_MULTI}})]]

union all

-- 餐盒费 转菜品
select
    (sec.js->>'order_guid') "order_guid",
    (sec.js->>'order_item_guid') "order_item_guid",
	(sec.js->>'business_day') "营业日",
	(sec.js->>'创建时间') "创建时间",
	(sec.js->>'brand_name') "品牌",
    sec.js->>'store_guid' "门店GUID",
	sec.js->>'store_name' "门店名称",
	sec.js->>'order_id' "order_id",
	sec.js->>'订单状态' "订单状态",
	sec.js->>'订单子类型' "订单子类型",
	null "商品GUID",
	null "third_sku_id",
	'餐盒费' "商品名称",
    '' "商品规格",
    '' "商品属性",
    '' "商品单位",
    null "商品单价",
    null "商品数量",
    null "商品消费合计",
    null "餐盒单价",
    null "餐盒数量",
    null "餐盒金额",
    '' "门店商品编码",
    '' "门店商品名称",
    null "门店商品单价",
    null "门店商品数量",
    null "门店商品金额合计",
    null "核算单价",
    (sec.jsx).value::numeric 核算商品总额
from (
    select
        row_to_json(row) js,
        jsonb_each(
            row_to_json(row)::jsonb
                -'rowid'
                -'order_guid'
                -'order_item_guid'
                -'business_day'
                -'创建时间'
                -'brand_name'
                -'store_guid'
                -'store_name'
				-'order_id'
				-'订单状态'
				-'订单子类型'
        ) jsx
    from (
        select
            row_number() over(partition by max(i.item_guid)) rowid,
            o.order_guid as "order_guid",
            '0' as order_item_guid,
            max(o.business_day) "business_day",
            max(i.gmt_create::text) "创建时间",
            case max(o.brand_guid)
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' then '何师烧烤'
                when '95a8319b-8297-4f06-a39f-a3c08799dad6' then '玉米熊'
                when '6919138141420912640' then '家婆抄手'
            end as "brand_name",
            max(o.store_guid) "store_guid",
            max(o.store_name) "store_name",
            max(o.order_id) as "order_id",
            CASE max(o.order_status)
                WHEN -1 THEN '已取消'
                WHEN 0 THEN '待处理'
                WHEN 10 THEN '已接单'
                WHEN 20 THEN '配送中'
                WHEN 30 THEN '配送完成'
                WHEN 100 THEN '已完成'
            END AS "订单状态",
            CASE max(o.order_sub_type)
                WHEN 0 THEN '美团'
                WHEN 1 THEN	'饿了么'
                WHEN 6 THEN	'赚餐外卖'
            END AS "订单子类型",
            max(o.package_total) 核算商品总额
        FROM
            "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_item i
        LEFT JOIN "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order o ON i.order_guid = o.order_guid,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
        where
            acl.chmod
            and o.package_total > 0
            [[and o.business_day between {{START_DATE}} AND {{END_DATE}}]]
            [[and i.business_day between {{START_DATE}} AND {{END_DATE}}]]
            [[and o.brand_guid in ({{BRAND_MULTI}}) ]]
            and case acl.list
                when 'true' then true
                when 'false' then false
                -- when 'false' then true  --debug
                else (o.store_guid = any(acl.list::text[]))
            end
            [[and o.order_status in ({{ORDER_STATUS_MULTI}})]]
            [[and o.refund_status in ({{REFUND_STATUS_MULTI}})]]
            [[and o.order_id ~* {{SEARCH}}]]
        group by o.order_guid
    ) row
    where
        row.rowid = 1
) sec
) temp
order by temp.order_guid desc, temp.order_item_guid desc, temp.商品消费合计 desc