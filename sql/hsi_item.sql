ALTER TABLE `hsi_item`
    ADD COLUMN `external_version` INT(32) UNSIGNED NOT NULL DEFAULT 0 COMMENT '外部版本号：每次外部操作版本号+1，内部操作不处理' AFTER `is_delete`;

ALTER TABLE `hsi_sku`
    ADD COLUMN `external_version` INT(32) UNSIGNED NOT NULL DEFAULT 0 COMMENT '外部版本号：每次外部操作版本号+1，内部操作不处理' AFTER `is_delete`;

ALTER TABLE `hsi_type`
    ADD COLUMN `external_version` INT(32) UNSIGNED NOT NULL DEFAULT 0 COMMENT '外部版本号：每次外部操作版本号+1，内部操作不处理' AFTER `is_delete`;