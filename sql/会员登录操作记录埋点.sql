CREATE TABLE `hsb_member_operate_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL COMMENT '操作guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `device_type` tinyint(4) DEFAULT NULL COMMENT '设备类型 BaseDeviceTypeEnum 3一体机 4pos机',
  `module_type` tinyint(4) DEFAULT NULL COMMENT '操作模块 1正餐 2快餐 3会员',
  `login_type` tinyint(4) DEFAULT NULL COMMENT '登录方式 0-扫码 1-手机号录入',
  `trade_mode` tinyint(4) DEFAULT NULL COMMENT '交易模式(0：正餐，1：快餐)',
  `operate_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operator_guid` varchar(50) DEFAULT NULL COMMENT '操作员guid',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作员名称',
  `phone_num` varchar(20) DEFAULT NULL COMMENT '会员手机号',
  `order_guid` varchar(50) DEFAULT NULL COMMENT '订单guid',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员操作记录表';