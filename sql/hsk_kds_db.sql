-- MySQL dump 10.13  Distrib 5.7.20, for Linux (x86_64)
--
-- Host: ***************    Database: hsk_kds_6506431195651982337_db
-- ------------------------------------------------------
-- Server version	5.7.20-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `hsk_device_config`
--

DROP TABLE IF EXISTS `hsk_device_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsk_device_config` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(45) DEFAULT NULL,
  `store_guid` varchar(45) DEFAULT NULL,
  `name` varchar(45) DEFAULT NULL,
  `point_mode` tinyint(8) unsigned DEFAULT NULL,
  `display_mode` tinyint(8) unsigned DEFAULT NULL,
  `display_type` tinyint(8) unsigned DEFAULT '15',
  `is_item_sort` tinyint(1) unsigned DEFAULT '1',
  `is_item_timeout` tinyint(1) unsigned DEFAULT '1',
  `is_show_hanged_item` tinyint(1) unsigned DEFAULT '0',
  `is_produce_hanged_item` tinyint(1) unsigned DEFAULT '0',
  `is_show_reserve_item` tinyint(1) unsigned DEFAULT '0',
  `is_produce_reserve_item` tinyint(1) unsigned DEFAULT '0',
  `is_display_by_max_copied` tinyint(1) unsigned DEFAULT '0',
  `is_manual_confirm` tinyint(1) unsigned DEFAULT '0',
  `is_specified_staff` tinyint(1) unsigned DEFAULT '0',
  `is_specified_table` tinyint(1) unsigned DEFAULT '0',
  `is_print_automatic` tinyint(1) unsigned DEFAULT '1',
  `is_dine_in_order_notice` tinyint(1) unsigned DEFAULT '1',
  `is_snack_order_notice` tinyint(1) unsigned DEFAULT '1',
  `is_takeout_order_notice` tinyint(1) unsigned DEFAULT '1',
  `is_display_item_un_produced` tinyint(1) unsigned DEFAULT '1',
  `is_display_item_timeout` tinyint(1) unsigned DEFAULT '1',
  `is_dispatch_as_print` tinyint(1) unsigned DEFAULT '0',
  `is_dine_in_dispatch_notice` tinyint(1) unsigned DEFAULT '0',
  `is_snack_dispatch_notice` tinyint(1) unsigned DEFAULT '0',
  `is_takeout_dispatch_notice` tinyint(1) unsigned DEFAULT '0',
  `printer_guid` varchar(45) DEFAULT NULL,
  `is_initialized` tinyint(1) unsigned DEFAULT '0',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `idx_printer` (`printer_guid`),
  KEY `idx_store_device` (`store_guid`,`guid`,`name`)
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsk_distribute_area`
--

DROP TABLE IF EXISTS `hsk_distribute_area`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsk_distribute_area` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(45) DEFAULT NULL,
  `store_guid` varchar(45) DEFAULT NULL,
  `device_id` varchar(45) DEFAULT NULL,
  `area_guid` varchar(45) DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_store` (`store_guid`),
  KEY `idx_device` (`device_id`)
) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsk_distribute_item`
--

DROP TABLE IF EXISTS `hsk_distribute_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsk_distribute_item` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(45) DEFAULT NULL,
  `store_guid` varchar(45) DEFAULT NULL,
  `device_id` varchar(45) DEFAULT NULL,
  `item_guid` varchar(45) DEFAULT NULL,
  `sku_guid` varchar(45) DEFAULT NULL,
  `sku_code` varchar(45) DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_store` (`store_guid`),
  KEY `idx_device` (`device_id`)
) ENGINE=InnoDB AUTO_INCREMENT=157 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsk_item_config`
--

DROP TABLE IF EXISTS `hsk_item_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsk_item_config` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(45) DEFAULT NULL,
  `store_guid` varchar(45) DEFAULT NULL,
  `sku_guid` varchar(45) DEFAULT NULL,
  `timeout` int(11) DEFAULT NULL,
  `max_copies` int(11) DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_sku_guid` (`sku_guid`),
  KEY `idx_store_guid` (`store_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsk_kitchen_item`
--

DROP TABLE IF EXISTS `hsk_kitchen_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsk_kitchen_item` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(45) DEFAULT NULL,
  `store_guid` varchar(45) DEFAULT NULL,
  `point_guid` varchar(45) DEFAULT NULL,
  `prd_device_id` varchar(45) DEFAULT NULL,
  `dst_device_id` varchar(45) DEFAULT NULL,
  `display_type` tinyint(8) unsigned DEFAULT NULL,
  `order_guid` varchar(45) DEFAULT NULL,
  `order_desc` varchar(45) DEFAULT NULL,
  `order_number` varchar(45) DEFAULT NULL,
  `area_guid` varchar(45) DEFAULT NULL,
  `table_guid` varchar(45) DEFAULT NULL,
  `table_name` varchar(45) DEFAULT NULL,
  `order_item_guid` varchar(45) DEFAULT NULL,
  `item_guid` varchar(45) DEFAULT NULL,
  `item_name` varchar(45) DEFAULT NULL,
  `sku_guid` varchar(45) DEFAULT NULL,
  `sku_name` varchar(45) DEFAULT NULL,
  `sku_code` varchar(45) DEFAULT NULL,
  `sku_unit` varchar(45) DEFAULT NULL,
  `is_weight` tinyint(1) unsigned DEFAULT NULL,
  `current_count` decimal(8,2) unsigned DEFAULT NULL,
  `return_count` decimal(8,2) unsigned DEFAULT NULL,
  `item_attr_md5` varchar(45) DEFAULT NULL,
  `item_remark` varchar(45) DEFAULT NULL,
  `order_remark` varchar(45) DEFAULT NULL,
  `timeout` int(10) unsigned DEFAULT NULL,
  `urged_times` int(10) unsigned DEFAULT '0',
  `item_state` tinyint(8) unsigned DEFAULT NULL,
  `kitchen_state` tinyint(8) unsigned DEFAULT NULL,
  `is_print_automatic` tinyint(1) unsigned DEFAULT '0',
  `order_sort_time` datetime DEFAULT NULL,
  `prepare_time` datetime DEFAULT NULL,
  `hang_up_time` datetime DEFAULT NULL,
  `call_up_time` datetime DEFAULT NULL,
  `urged_time` datetime DEFAULT NULL,
  `cook_staff_guid` varchar(45) DEFAULT NULL,
  `cook_staff_name` varchar(45) DEFAULT NULL,
  `cook_time` datetime DEFAULT NULL,
  `complete_staff_guid` varchar(45) DEFAULT NULL,
  `complete_staff_name` varchar(45) DEFAULT NULL,
  `complete_time` datetime DEFAULT NULL,
  `distribute_staff_guid` varchar(45) DEFAULT NULL,
  `distribute_staff_name` varchar(45) DEFAULT NULL,
  `distribute_time` datetime DEFAULT NULL,
  `cancel_dst_staff_guid` varchar(45) DEFAULT NULL,
  `cancel_dst_staff_name` varchar(45) DEFAULT NULL,
  `cancel_dst_time` datetime DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_prepare_item` (`order_guid`,`item_state`,`kitchen_state`),
  KEY `idx_dst_item` (`kitchen_state`,`prd_device_id`,`dst_device_id`,`order_item_guid`,`sku_guid`,`display_type`),
  KEY `idx_prd_item` (`prd_device_id`,`kitchen_state`,`item_state`,`point_guid`,`order_item_guid`,`sku_guid`),
  KEY `idx_prd_point_item` (`point_guid`,`item_state`,`kitchen_state`,`order_item_guid`,`sku_guid`,`display_type`)
) ENGINE=InnoDB AUTO_INCREMENT=5078 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsk_kitchen_item_attr`
--

DROP TABLE IF EXISTS `hsk_kitchen_item_attr`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsk_kitchen_item_attr` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(45) DEFAULT NULL,
  `order_item_guid` varchar(45) DEFAULT NULL,
  `group_guid` varchar(45) DEFAULT NULL,
  `group_name` varchar(45) DEFAULT NULL,
  `attr_guid` varchar(45) DEFAULT NULL,
  `attr_name` varchar(45) DEFAULT NULL,
  `attr_number` int(10) unsigned DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order_item_guid` (`order_item_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=243 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsk_point_item`
--

DROP TABLE IF EXISTS `hsk_point_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsk_point_item` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(45) DEFAULT NULL,
  `store_guid` varchar(45) DEFAULT NULL,
  `device_id` varchar(45) DEFAULT NULL,
  `point_guid` varchar(45) DEFAULT NULL,
  `item_guid` varchar(45) DEFAULT NULL,
  `sku_guid` varchar(45) DEFAULT NULL,
  `sku_code` varchar(45) DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_store` (`store_guid`),
  KEY `idx_point` (`point_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=590 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsk_print_record`
--

DROP TABLE IF EXISTS `hsk_print_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsk_print_record` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(45) DEFAULT NULL,
  `record_uid` varchar(45) DEFAULT NULL,
  `store_guid` varchar(45) DEFAULT NULL,
  `device_id` varchar(45) DEFAULT NULL,
  `invoice_type` tinyint(8) unsigned DEFAULT NULL,
  `printer_guid` varchar(45) DEFAULT NULL,
  `print_status` tinyint(8) unsigned DEFAULT NULL,
  `print_status_msg` varchar(45) DEFAULT NULL,
  `print_content` text,
  `create_staff_guid` varchar(45) DEFAULT NULL,
  `is_deleted` tinyint(1) unsigned DEFAULT '0',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=240 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsk_production_point`
--

DROP TABLE IF EXISTS `hsk_production_point`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsk_production_point` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(45) DEFAULT NULL,
  `store_guid` varchar(45) DEFAULT NULL,
  `device_id` varchar(45) DEFAULT NULL,
  `name` varchar(45) DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_store` (`store_guid`),
  KEY `idx_device` (`device_id`),
  KEY `idx_guid` (`guid`)
) ENGINE=InnoDB AUTO_INCREMENT=106 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsk_store_printer`
--

DROP TABLE IF EXISTS `hsk_store_printer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsk_store_printer` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(45) DEFAULT NULL,
  `store_guid` varchar(45) DEFAULT NULL,
  `printer_name` varchar(45) DEFAULT NULL,
  `printer_ip` varchar(45) DEFAULT NULL,
  `printer_port` smallint(16) unsigned DEFAULT NULL,
  `page_size` tinyint(8) unsigned DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2019-07-03 17:57:11
