-- 营业额报表
with base as (
    -- 外卖数据
    select
        business_day,
        store_guid,
        max(store_name) store_name,
        -- total, -- 总价，包括：菜品 + 餐盒 + 配送
        sum(ship_total + package_total + item_total) total,
        sum(item_total) item_total, -- 商品金额
        sum(
            ship_total -- 配送费合计
            + package_total -- 餐盒费合计
        ) add_amont,  --附加费
        sum(customer_actual_pay) actual_pay,  --实收金额
        count(1) table_count,  --桌台数
        1 order_type,
        0 guest_count
    from "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    where
        acl.chmod
        and gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
        and order_status = 100
        and order_sub_type in (0,1,6)
        -- and business_day between '2022-03-06' and '2022-03-07'
        [[and business_day between {{START_TIME}}::date and {{END_TIME}}::date]]
        [[and gmt_create::time between {{START_TIME_INTERVAL}}::TIME and {{END_TIME_INTERVAL}}::time]]

        -- 大于等于 ORDER_MIN 单价大于等于最小值
        -- 小于等于 ORDER_MAX 单价小于等于最大值
        [[ and ship_total + package_total + item_total <= {{ORDER_MAX}}::numeric ]]
        [[ and ship_total + package_total + item_total >= {{ORDER_MIN}}::numeric ]]
        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (store_guid = any(acl.list::text[]))
        end
        -- and order_id in ('5499540228234986','8129418485113931198')
    group by
        store_guid,
        business_day
    union all
    -- 堂食数据清洗
    select
        ho.business_day,
        ho.store_guid,
        max(ho.store_name) store_name,
        sum(ho.order_fee) total,
        sum(
            ho.order_fee -- 订单金额（商品总额+附加费）
            - ho.append_fee -- 附加费
        )  item_total,  --商品金额
        sum(ho.append_fee) add_amont,  --附加费
        sum(ho.actually_pay_fee) actual_pay,  --实收金额
        count(ho.guid) table_count, --桌台数
        0 order_type,
        sum(ho.guest_count) guest_count --堂食人数
    from "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order ho, --堂食库
        -- left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount  hd on ho.guid = hd.order_guid
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    where
        acl.chmod
        and ho.gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
        and ho.copy_order_guid = 0
        -- and hd.discount_state = 0
        and ho.is_delete = 0
        and ho.state = 4
        and ho.recovery_type in (1,3)
        -- and ho.guid in ('6904276426489331712','6904368656285696000')
        -- and ho.business_day between '2022-03-01' and '2022-03-02'
        [[and ho.business_day between {{START_TIME}}::date and {{END_TIME}}::date]]
        [[and ho.gmt_create::time between {{START_TIME_INTERVAL}}::TIME and {{END_TIME_INTERVAL}}::time]]

        -- 大于等于 ORDER_MIN 单价大于等于最小值
        -- 小于等于 ORDER_MAX 单价小于等于最大值
        [[ and ho.order_fee <= {{ORDER_MAX}}::numeric ]]
        [[ and ho.order_fee >= {{ORDER_MIN}}::numeric ]]

        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (ho.store_guid = any(acl.list::text[]))
        end
    group by
        ho.store_guid,
        ho.business_day
),
rows_sum as (
    select
        sum(total) total,
        (
            SELECT
                count(1)
            FROM
                "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount
            WHERE
                order_guid = (SELECT MAX(order_guid) FROM "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount)
        ) lenght,
        case [[ {{ORDER_TYPE}}::int -- ]] 2
            when 0 then (
                SELECT
                    count(1)
                FROM
                    "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount
                WHERE
                    order_guid = (SELECT MAX(order_guid) FROM "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount)
            )
            when 1 then 3
            when 2 then (
                SELECT
                    count(1) + 3
                FROM
                    "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount
                WHERE
                    order_guid = (SELECT MAX(order_guid) FROM "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount)
            )
        end mergerindex
    from base
    [[where base.order_type = {{ORDER_TYPE}}::int]]
),
base_aggregation as (
    select
        -- x.business_day,
        case
            when ($privileges_flag = '0' and $power_list::text = '-1')
                or ($privileges_flag = '-2' and $power_list::text != '-1')
                    then (
                        case
                            when x.business_day isnull then '合计'
                            else x.business_day::text
                        end
                        )
                    else null
            end business_day,
        -- x.store_guid,
        case
            when ($privileges_flag = '0' and $power_list::text = '-1')
                or ($privileges_flag = '-2' and $power_list::text != '-1')
                    then (
                        case
                            when x.store_guid isnull then '合计'
                            else x.store_guid
                        end
                        )
            else null
        end store_guid,
        sum(x.total) total,  --营业额合计
        sum(x.item_total) item_total,  --商品金额
        sum(x.add_amont) add_amont,  --附加费
        sum(x.actual_pay) actual_pay,  --实收
        sum(x.table_count) table_count,  --桌数
        sum(x.total) / sum(x.table_count) table_average,  --桌均营业额=营业额/桌数
        sum(x.actual_pay) / sum(x.table_count) table_average_actual,  --桌均实收金额=实收金额/桌数
        sum(x.guest_count) guest_count,  --堂食人数
        sum(x.total) filter(where x.order_type = 0) / sum(x.guest_count) dpat, --堂食人均营业额=营业额/人数
        sum(x.actual_pay) filter(where x.order_type = 0) / sum(x.guest_count) dpmt, --堂食人均实收=实收金额/人数
        concat(round(coalesce(sum(x.total) / (select total from rows_sum) * 100, 0),2),'%') ratio --营业额百分比
    from base x
    -- _MULTI
    [[where x.order_type = {{ORDER_TYPE}}::int]]
    group by
        -- x.business_day,
        -- x.store_guid
        grouping sets(
            (
                x.store_guid,
                x.business_day),
                ()
            )
),
deshes_discount_base as (
    -- 外卖数据清洗
    select
        w.business_day,
        w.store_guid,
        w.order_type,
        case (w.rec).key
            when '外卖企业折扣' then 100
            when '外卖平台折扣' then 101
            when '外卖其它折扣' then 102
        end discount_type,
        (w.rec).key discount_name,
        (w.rec).value::numeric discount_fee
    from (
        select
            x.business_day,
            x.store_guid,
            x.order_type,
            jsonb_each(
                row_to_json(x)::jsonb
                    -'store_guid'
                    -'business_day'
                    -'order_type'
            ) rec
        from (
            -- 外卖数据
            select
                business_day,
                store_guid,
                coalesce(sum(enterprise_discount),0) "外卖企业折扣",
                coalesce(sum(platform_discount),0) "外卖平台折扣",
                coalesce(sum(other_discount),0) "外卖其它折扣",
                1 order_type
            from "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order,
                LATERAL (
                    select
                        public.getlist(
                            public.getacl(
                                $privileges_flag,
                                ($power_list)::text
                            ),
                            ($power_list)::text,
                            array[
                                [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                                ,
                                [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                            ]
                        ) list,
                        public.getacl(
                            $privileges_flag,
                            ($power_list)::text
                        ) chmod
                ) acl
            where
                acl.chmod
                and gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
                and order_status = 100
                and order_sub_type in (0,1,6)
                -- and business_day between '2022-03-06' and '2022-03-07'
                [[and business_day between {{START_TIME}}::date and {{END_TIME}}::date]]
                [[and gmt_create::time between {{START_TIME_INTERVAL}}::TIME and {{END_TIME_INTERVAL}}::time]]

                -- 大于等于 ORDER_MIN 单价大于等于最小值
                -- 小于等于 ORDER_MAX 单价小于等于最大值
                [[ and ship_total + package_total + item_total <= {{ORDER_MAX}}::numeric ]]
                [[ and ship_total + package_total + item_total >= {{ORDER_MIN}}::numeric ]]
                and case acl.list
                    when 'true' then true
                    when 'false' then false
                    -- when 'false' then true  --debug
                    else (store_guid = any(acl.list::text[]))
                end
                -- and order_id in ('5499540228234986','8129418485113931198')
            group by
                store_guid,
                business_day
        ) x
    ) w
    union all
    -- 堂食数据清洗
    select
        ho.business_day,
        ho.store_guid,
        0 order_type,
        hd.discount_type,
        max(hd.discount_name) discount_name,
        sum(hd.discount_fee) discount_fee
    from "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order ho --堂食库
        left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount hd on ho.guid = hd.order_guid,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    where
        acl.chmod
        and hd.gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
        and ho.gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
        and ho.copy_order_guid = 0
        and hd.discount_state = 0
        and ho.is_delete = 0
        and ho.state = 4
        and ho.recovery_type in (1,3)
        -- and ho.guid in ('6904276426489331712','6904368656285696000')
        -- and ho.business_day between '2022-03-01' and '2022-03-02'
        [[and ho.business_day between {{START_TIME}}::date and {{END_TIME}}::date]]
        [[and ho.gmt_create::time between {{START_TIME_INTERVAL}}::TIME and {{END_TIME_INTERVAL}}::time]]

        -- 大于等于 ORDER_MIN 单价大于等于最小值
        -- 小于等于 ORDER_MAX 单价小于等于最大值
        [[ and ho.order_fee <= {{ORDER_MAX}}::numeric ]]
        [[ and ho.order_fee >= {{ORDER_MIN}}::numeric ]]
        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (ho.store_guid = any(acl.list::text[]))
        end
    group by
        ho.store_guid,
        ho.business_day,
        hd.discount_type
),
deshes_discount_expend as (
    select
        -- db.business_day,
        case
            when ($privileges_flag = '0' and $power_list::text = '-1')
                or ($privileges_flag = '-2' and $power_list::text != '-1')
                    then (
                        case
                            when db.business_day isnull then '合计'
                            else db.business_day::text
                        end
                        )
            else null
        end business_day,
        -- db.store_guid,
        case
            when ($privileges_flag = '0' and $power_list::text = '-1')
                or ($privileges_flag = '-2' and $power_list::text != '-1')
                    then (
                        case
                            when db.store_guid isnull then '合计'
                            else db.store_guid
                        end
                        )
            else null
        end store_guid,
        @_start
        SELECT
            string_agg(
                concat(
                    'sum(
                        case db.discount_type
                            when ',discount_type,' then db.discount_fee
                        end
                    ) as ','"',discount_name,'"'
                ),','
            )
        from (
            SELECT
                discount_type,
                discount_name,
                0 order_type
            FROM
                "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount
            WHERE
                order_guid = (SELECT MAX(order_guid) FROM "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount)
                and gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
            union all
            values
                (100,'外卖企业折扣',1),
                (101,'外卖平台折扣',1),
                (102,'外卖其它折扣',1)
        ) base
        [[where base.order_type = {{ORDER_TYPE}}::int]]
        @_end
    from deshes_discount_base db
    [[where db.order_type = {{ORDER_TYPE}}::int]]
    group by
        -- db.business_day,
        -- db.store_guid
        grouping sets((db.store_guid,db.business_day),())
),
organization as (
    select
        mos.external_part_org_id,
        mos.external_part_org_name,
        u.username,
        ti2.name
    from team.mapping_org_structure mos -- on htr.store_guid = mos.external_part_org_id
        left join team.team_info ti on mos.holder_id = ti.id
        left join team.team_info ti2 on ti.parent_id = ti2.id
        left join team.user u on ti2.responsible_user = u.id or ti.responsible_user = u.id,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    where
        acl.chmod

        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (mos.external_part_org_id = any(acl.list::text[]))
        end
)
select
    os.name "战区",
    os.username "区经理",
    ba.business_day::text "营业日期",
    null "门店编码",
    os.external_part_org_name "门店名称",
    ba.total "合计",
    ba.item_total "商品金额",
    ba.add_amont "附加费",
    de.*,
    ba.actual_pay "实收金额",
    ba.table_count "桌数",
    ba.table_average "桌均营业额",
    ba.table_average_actual "桌均实收金额",
    ba.guest_count "堂食人数",
    ba.dpat "堂食人均营业额",
    ba.dpmt "堂食人均实收",
    -- (select total from rows_sum) hj,
    -- ba.ratio "营业额百分比"
    case when ba.ratio = '0.00%' then null else ba.ratio end "营业额百分比",
    concat('[{"type":"mergeTitle",',
        '"addField":["营业额","优惠金额"],',
        '"refField":[{"start":5,"end":7},',
        '{"start":8,"end":',(select mergerindex + 9 from rows_sum),'}],',
        '"mergerRow":[0,1,2,3,4,',
            (select mergerindex + 10 from rows_sum),',',
            (select mergerindex + 11 from rows_sum),',',
            (select mergerindex + 12 from rows_sum),',',
            (select mergerindex + 13 from rows_sum),',',
            (select mergerindex + 14 from rows_sum),',',
            (select mergerindex + 15 from rows_sum),',',
            (select mergerindex + 16 from rows_sum),',',
            (select mergerindex + 17 from rows_sum),
        ']}]'
    ) 合并规则
from
    base_aggregation ba
    left join organization os on ba.store_guid = os.external_part_org_id
    left join deshes_discount_expend de on ba.business_day = de.business_day and ba.store_guid = de.store_guid
order by
    case
        when ba.store_guid = '合计' then 3
        else 5
    end,
    3,5;