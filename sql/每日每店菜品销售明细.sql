-- 堂食
with trade as (
    SELECT
        o.guid as "order_guid",
        o.state,
        o.store_guid,
        o.store_name,
        o.business_day::text as "business_day",
        i.guid as "order_item_guid",
        i.sku_guid,
        i.sku_name,
        case {{VIEW_TYPE}}
                    when '0' then i.item_name
                    when '1' then ii.item_name
                end as "item_name",
        case i.parent_item_guid
            when '0' then i.current_count
            else i.current_count * i.package_default_count * ii.current_count
        end as "current_count",
        case i.parent_item_guid
            when '0' then i.free_count
            else i.free_count * ii.free_count
        end as "free_count",
        case i.parent_item_guid
            when '0' then i.return_count
            else i.return_count * ii.return_count
        end as "return_count",
        i.accounting_price,
        case i.parent_item_guid
            when '0' then i.current_count + i.free_count
            else (i.current_count + i.free_count) * i.package_default_count * ii.current_count
        end as "accounting_count",
        hsb.brand_guid as "brand_guid"
    FROM
    	"hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o
    	left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item i on i.order_guid = o.guid and i.is_delete = 0
    	left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item ii on i.parent_item_guid = ii.guid and i.is_delete = 0
    	left join "hst_table_887f2181-eb06-4d77-b914-7c37c884952c_db"."hst_table_basic" tt on o.dining_table_guid = tt.guid
		left join "hso_organization_887f2181-eb06-4d77-b914-7c37c884952c_db".hso_r_store_brand hsb on o.store_guid = hsb.store_guid,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    WHERE
        acl.chmod
    	and o.is_delete = 0
        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (o.store_guid = any(acl.list::text[]))
        end
    	and o.copy_order_guid = 0
    	and ( o.state = 4 or ( o.state = 5 and o.recovery_type = 4 and o.recovery_id = '0' ) )
    	and case {{VIEW_TYPE}}
    		when '0' then i.parent_item_guid = 0
    		when '1' then i.item_type != 1
    	end
    	[[
    	   -- 统计时间归属节点
    	   -- 1-下单时间,2-结账时间
    	    and case {{ATTRIBUTE}}
    	        when '1' then (
    	            true
    	            [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}} ]]
                    [[and o.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
                    [[and i.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
    	        )
    	        when '2' then (
    	            true
    	            [[and o.checkout_time between {{START_TIME}} AND {{END_TIME}}]]
    	        )
    			when '3' then (
    				true
    				[[and o.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
    				[[and i.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
    			)
            end
    	]]
        [[ and o.order_no ~~ concat('%',{{ORDER_NO}},'%') ]]
        [[ and i.item_name ~~ concat('%',{{ITEM_NAME}},'%') ]]
        [[
                and case {{BRAND}}
                    when '1' then o.store_name ~~* '%YM%'
                    when '2' then o.store_name !~~* '%YM%'
                end
            ]]
),
-- 外卖
takeaway as (
    select
        o.order_guid,
        i.item_guid as "order_item_guid",
        o.business_day::text business_day,
        o.store_guid,
        o.store_name,
        i.is_adjust_item,
        case {{VIEW_TYPE}}
            when '0' then i.erp_item_name
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.erp_item_name
                    else p.item_name
                end
            )
        end erp_item_name,
        case {{VIEW_TYPE}}
            when '0' then i.erp_item_sku_guid
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.erp_item_sku_guid
                    else p.sku_guid
                end
            )
        end erp_item_sku_guid,
        case {{VIEW_TYPE}}
            when '0' then hs.name
            when '1' then (
                case
                    when p.takeout_item_guid isnull then hs.name
                    else p.sku_name
                end
            )
        end erp_sku_name,
        case {{VIEW_TYPE}}
            when '0' then i.erp_item_price
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.erp_item_price
                    else p.sale_price
                end
            )
        end as "erp_item_price",
        case {{VIEW_TYPE}}
            when '0' then i.actual_item_count
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.actual_item_count
                    else p.item_count * i.actual_item_count
                end
            )
        end as "actual_item_count",
        case {{VIEW_TYPE}}
            when '0' then ( case when o.order_status = -1 then i.item_count else i.refund_count end) * ( i.actual_item_count / i.item_count)
            when '1' then (
                case
                    when p.takeout_item_guid isnull then ( case when o.order_status = -1 then i.item_count else i.refund_count end) * ( i.actual_item_count / i.item_count)
                    else p.item_count * ( case when o.order_status = -1 then i.item_count else i.refund_count end) * ( i.actual_item_count / i.item_count)
                end
            )
        end as "actual_refund_item_count",
        case {{VIEW_TYPE}}
            when '0' then i.takeaway_accounting_price
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.takeaway_accounting_price
                    else p.accounting_price
                end
            )
        end as "takeaway_accounting_price"
    from
        "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_item i
        LEFT JOIN "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order o ON i.order_guid = o.order_guid
        LEFT JOIN "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_sku hs on i.erp_item_sku_guid = hs.guid
        LEFT JOIN (
            select
                rowid,
                takeout_item_guid,
                item_count,
                item_name,
                sku_guid,
                sku_name,
                sale_price,
                accounting_price,
                code
            from (
                select
                    row_number() over(partition by takeout_item_guid) rowid,
                    takeout_item_guid,
                    item_count,
                    item_name,
                    sku_guid,
                    sku_name,
                    sale_price,
                    accounting_price,
                    code
                from "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_package
                where
                    is_delete = 0
                    and ods_delete_time isnull
            ) x
            where
                case {{VIEW_TYPE}}
                    when '0' then rowid = 1
                    when '1' then true
                end
        ) p on p.takeout_item_guid = i.item_guid,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
     where
        acl.chmod
        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (o.store_guid = any(acl.list::text[]))
        end
        and i.erp_item_sku_guid is not null
        [[
           -- 统计时间归属节点
           -- 1-下单时间,2-结账时间
            and case {{ATTRIBUTE}}
                when '1' then (
                    true
                    [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}} ]]
                    [[and o.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
                    [[and i.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
                )
                when '2' then (
                    true
                    [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}}]]
                )
                when '3' then (
                    true
                    [[and o.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
                    [[and i.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
                )
            end
        ]]
        [[
                and case {{BRAND}}
                    when '1' then o.store_name ~~* '%YM%'
                    when '2' then o.store_name !~~* '%YM%'
                end
            ]]
        [[and o.order_id = {{ORDER_NO}}::text]]
)

-- 堂食菜品销售明细
select
    max(list.business_day) 下单日期,
    max(list.store_name) 门店名称,
    case when max(list.trade_mode) = '堂食' and max(list.sku_name) is not null and max(list.sku_name) != ''
                then concat(max(list.item_name), '-', max(list.sku_name)) else max(list.item_name) end 商品名称,
    max(list.trade_mode) 销售来源类型,
    max(hs.code) 菜品编号,
    case when max(list.trade_mode) = '堂食'
                then sum(list.current_count + list.return_count) else sum(list.current_count) end 点餐数量,
    sum(list.free_count) 赠送数量,
    sum(list.return_count) 退菜数量,
    max(list.accounting_price) 核算单价,
    sum(list.accounting_count) 核算数量,
    sum(list.accounting_price * list.accounting_count) 核算金额
from (
    -- 堂食明细 结账+退款
    SELECT
        t.store_guid,
        t.store_name,
        t.business_day,
        '堂食' trade_mode,
        t.sku_guid,
        t.item_name,
        t.sku_name,
        case when t.state = 4 then t.current_count else -t.current_count end current_count,
        case when t.state = 4 then t.free_count else -t.free_count end free_count,
        case when t.state = 4 then t.return_count else -t.return_count end return_count,
        t.accounting_price,
        case when t.state = 4 then t.accounting_count else -t.accounting_count end accounting_count
    from
        trade t

    union all

    -- 外卖 结账+退款
    SELECT
        t.store_guid,
        t.store_name,
        t.business_day,
        '外卖' trade_mode,
        t.erp_item_sku_guid sku_guid,
        t.erp_item_name item_name,
        t.erp_sku_name sku_name,
        t.actual_item_count current_count,
        0 free_count,
        t.actual_refund_item_count return_count,
        t.takeaway_accounting_price accounting_price,
        t.actual_item_count-t.actual_refund_item_count accounting_count
    from
        takeaway t

    union all

    -- 堂食调整单菜品更换需要先扣减数量
    SELECT
        t.store_guid,
        t.store_name,
        t.business_day,
        '堂食' trade_mode,
        t.sku_guid,
        t.item_name,
        t.sku_name,
        -t.current_count,
        0 free_count,
        0 return_count,
        t.accounting_price,
        -t.accounting_count
    from
        (
            SELECT
                DISTINCT ad.order_item_guid,
                ad.adjust_type
            from
                "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details ad
            where
                ad.is_delete = 0
        ) a
        join trade t on t.order_item_guid = a.order_item_guid
    WHERE
        t.state = 4
        and a.adjust_type = 1

    union all

    -- 外卖调整单菜品更换需要先扣减数量
    SELECT
        t.store_guid,
        t.store_name,
        t.business_day,
        '外卖' trade_mode,
        t.erp_item_sku_guid sku_guid,
        t.erp_item_name item_name,
        t.erp_sku_name sku_name,
        -t.actual_item_count current_count,
        0 free_count,
        0 return_count,
        t.takeaway_accounting_price accounting_price,
        -t.actual_item_count accounting_count
    from
        (
            SELECT
                DISTINCT ad.order_item_guid,
                ad.adjust_type
            from
                "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details ad
            where
                ad.is_delete = 0
        ) a
        join takeaway t on t.order_item_guid::BIGINT = a.order_item_guid
    WHERE
        a.adjust_type = 1

    union all

    -- 堂食调整单明细
    SELECT
        t.store_guid,
        t.store_name,
        t.business_day,
        '堂食' trade_mode,
        a.sku_guid,
        case {{VIEW_TYPE}}
                    when '0' then a.item_name
                    when '1' then aa.item_name
                end as item_name,
        t.sku_name,
        case when a.adjust_type = 1 and a.parent_item_guid = 0 then a.current_count
                when a.adjust_type = 1 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count
                when a.adjust_type = 0 and a.parent_item_guid = 0 then a.current_count - t.current_count
                when a.adjust_type = 0 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count - t.current_count
        end current_count,
        0 free_count,
        0 return_count,
        a.accounting_price,
        case when a.adjust_type = 1 and a.parent_item_guid = 0 then a.current_count
                when a.adjust_type = 1 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count
                when a.adjust_type = 0 and a.parent_item_guid = 0 then a.current_count - t.current_count
                when a.adjust_type = 0 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count - t.current_count
        end accounting_count
    from
      "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details a
      left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details aa on a.parent_item_guid = aa.guid and aa.is_delete = 0
      left join trade t on t.order_item_guid = a.order_item_guid
    WHERE
        a.is_delete = 0
        and t.state = 4
        and case {{VIEW_TYPE}}
            when '0' then a.parent_item_guid = 0
            when '1' then a.item_type != 1
        end
        [[ and a.item_name ~~ concat('%',{{ITEM_NAME}},'%') ]]

    union all

    -- 外卖调整单明细
    SELECT
        t.store_guid,
        t.store_name,
        t.business_day,
        '外卖' trade_mode,
        a.sku_guid,
        case {{VIEW_TYPE}}
                    when '0' then a.item_name
                    when '1' then aa.item_name
                end as item_name,
        case {{VIEW_TYPE}}
                    when '0' then a.sku_name
                    when '1' then aa.sku_name
                end as sku_name,
        case when a.adjust_type = 1 and a.parent_item_guid = 0 then a.current_count
                when a.adjust_type = 1 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count
                when a.adjust_type = 0 and a.parent_item_guid = 0 then a.current_count - t.actual_item_count
                when a.adjust_type = 0 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count - t.actual_item_count
        end current_count,
        0 free_count,
        0 return_count,
        a.accounting_price,
        case when a.adjust_type = 1 and a.parent_item_guid = 0 then a.current_count
                when a.adjust_type = 1 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count
                when a.adjust_type = 0 and a.parent_item_guid = 0 then a.current_count - t.actual_item_count
                when a.adjust_type = 0 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count - t.actual_item_count
        end accounting_count
    from
      "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details a
      left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details aa on a.parent_item_guid = aa.guid and aa.is_delete = 0
      left join takeaway t on t.order_item_guid::BIGINT = a.order_item_guid
    WHERE
        a.is_delete = 0
        and case {{VIEW_TYPE}}
            when '0' then a.parent_item_guid = 0
            when '1' then a.item_type != 1
        end
        [[ and a.item_name ~~ concat('%',{{ITEM_NAME}},'%') ]]
) list
LEFT JOIN "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_sku hs on list.sku_guid = hs.guid
WHERE
    list.accounting_count != 0
group by
    list.store_guid, list.business_day, list.trade_mode, list.sku_guid, list.accounting_price
order by
 	list.business_day asc, list.store_guid asc, list.sku_guid asc, list.trade_mode asc