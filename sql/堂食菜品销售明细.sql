with trade as (
    SELECT
        o.guid as "order_guid",
        o.order_no,
        o.state,
        o.store_guid,
        o.store_name,
        o.business_day::text as "business_day",
		tt.area_name,
		tt.table_code,
        REPLACE(o.dining_table_name,'-','') as "dining_table_name",
        o.append_fee,
        o.gmt_create::text "gmt_create",
        o.checkout_time::text "checkout_time",
        o.create_staff_name,
        o.original_order_guid ::text as "original_order_guid",
        i.guid as "order_item_guid",
        i.item_name,
        i.sku_guid,
        i.sku_name,
        i.item_type_name,
        i.code,
        i.item_type,
        i.unit,
        i.price,
        o.remark,
		o.guest_count,
        ii.item_name as "parent_item_name",
        case i.parent_item_guid
            when '0' then i.current_count
            else i.current_count * i.package_default_count * ii.current_count
        end as "current_count",
        case i.parent_item_guid
            when '0' then i.free_count
            else i.free_count * ii.free_count
        end as "free_count",
        case i.parent_item_guid
            when '0' then i.return_count
            else i.return_count * ii.return_count
        end as "return_count",
        i.accounting_price,
        case i.parent_item_guid
            when '0' then i.current_count + i.free_count
            else (i.current_count + i.free_count) * i.package_default_count * ii.current_count
        end as "accounting_count",
        case i.parent_item_guid
            when '0' then i.attr_total
            else ii.attr_total
        end as "attr_total",
        i.member_price,
        case
            when hsb.brand_guid = '95a8319b-8297-4f06-a39f-a3c08799dad6' then
                case o.trade_mode
                    when 0 then '堂食外卖'
                    when 1 then '堂食快餐'
                end
            else
                case o.trade_mode
                    when 0 then '正餐'
                    when 1 then '快餐'
                end
        end as "order_source",
        hsb.brand_guid as "brand_guid"
    FROM
    	"hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o
    	left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item i on i.order_guid = o.guid and i.is_delete = 0
    	left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item ii on i.parent_item_guid = ii.guid and i.is_delete = 0
    	left join "hst_table_887f2181-eb06-4d77-b914-7c37c884952c_db"."hst_table_basic" tt on o.dining_table_guid = tt.guid
		left join "hso_organization_887f2181-eb06-4d77-b914-7c37c884952c_db".hso_r_store_brand hsb on o.store_guid = hsb.store_guid,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    WHERE
        acl.chmod
    	and o.is_delete = 0
        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (o.store_guid = any(acl.list::text[]))
        end
    	and o.copy_order_guid = 0
    	and ( o.state = 4 or ( o.state = 5 and o.recovery_type = 4 and o.recovery_id = '0' ) )
    	and case {{VIEW_TYPE}}
    		when '0' then i.parent_item_guid = 0
    		when '1' then i.item_type != 1
    	end

    	AND i.item_guid NOT IN (
    		'6617780813696073728',
    		'6622454990583627776',
    		'6665469858576072704',
    		'6683296207357345792',
    		'6716295874126807040',
    		'6721589404856483840',
    		'6721590871914971136',
    		'6810777164678430720',
    		'6839738172407021568'
    	)
	[[
        	and case {{BRAND}}
        	    when '1' then o.store_name ~~* '%YM%'
        	    when '2' then o.store_name !~~* '%YM%'
        	end
    	]]
    	[[
    	   -- 统计时间归属节点
    	   -- 1-下单时间,2-结账时间
    	    and case {{ATTRIBUTE}}
    	        when '1' then (
    	            true
    	            [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}} ]]
                    [[and o.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
                    [[and i.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
    	        )
    	        when '2' then (
    	            true
    	            [[and o.checkout_time between {{START_TIME}} AND {{END_TIME}}]]
    	        )
    			when '3' then (
    				true
    				[[and o.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
    				[[and i.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
    			)
            end
    	]]
        [[ and o.order_no ~~ concat('%',{{ORDER_NO}},'%') ]]
        [[ and i.item_type_name ~~ concat('%',{{ITEM_TYPE_NAME}},'%') ]]
        [[ and i.item_name ~~ concat('%',{{ITEM_NAME}},'%') ]]
)
, item_free_return as (
 SELECT

        i.guid as "order_item_guid",

				string_agg(fr.reason, ',') as reason

    FROM
	"hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o
		join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item i  on i.order_guid = o.guid
		join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_free_return_item fr on i.guid = fr.order_item_guid,
		 LATERAL (
		select
			public.getlist(
				public.getacl(
					$privileges_flag,
					($power_list)::text
				),
				($power_list)::text,
				array[
					[[ {{STORE_GUID}} -- ]] '-1'
					,
					[[ {{GROUP_STORE_GUID}} -- ]] '-1'
				]
			) list,
			public.getacl(
				$privileges_flag,
				($power_list)::text
			) chmod
	) acl

    WHERE
           acl.chmod
    	and o.is_delete = 0
		AND fr.reason IS NOT NULL
        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (o.store_guid = any(acl.list::text[]))
        end
    	and o.copy_order_guid = 0
    	and ( o.state = 4 or ( o.state = 5 and o.recovery_type = 4 and o.recovery_id = '0' ) )
    	and case {{VIEW_TYPE}}
    		when '0' then i.parent_item_guid = 0
    		when '1' then i.item_type != 1
    	end

    	AND i.item_guid NOT IN (
    		'6617780813696073728',
    		'6622454990583627776',
    		'6665469858576072704',
    		'6683296207357345792',
    		'6716295874126807040',
    		'6721589404856483840',
    		'6721590871914971136',
    		'6810777164678430720',
    		'6839738172407021568'
    	)
	[[
        	and case {{BRAND}}
        	    when '1' then o.store_name ~~* '%YM%'
        	    when '2' then o.store_name !~~* '%YM%'
        	end
    	]]
    	[[
    	   -- 统计时间归属节点
    	   -- 1-下单时间,2-结账时间
    	    and case {{ATTRIBUTE}}
    	        when '1' then (
    	            true
    	            [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}} ]]
                    [[and o.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
                    [[and i.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
    	        )
    	        when '2' then (
    	            true
    	            [[and o.checkout_time between {{START_TIME}} AND {{END_TIME}}]]
    	        )
    			when '3' then (
    				true
    				[[and o.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
    				[[and i.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
    			)
            end
    	]]
        [[ and o.order_no ~~ concat('%',{{ORDER_NO}},'%') ]]
        [[ and i.item_type_name ~~ concat('%',{{ITEM_TYPE_NAME}},'%') ]]
        [[ and i.item_name ~~ concat('%',{{ITEM_NAME}},'%') ]]
			GROUP BY i.guid
)
, item_attr as (
 SELECT

        i.guid as "order_item_guid",
		string_agg(attr.attr_name, ',') as attr_name

    FROM
		"hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o
    	join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item i on i.order_guid = o.guid
		join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_item_attr attr on i.guid = attr.order_item_guid,
    	LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    WHERE
        acl.chmod
    	and o.is_delete = 0
		AND attr.attr_name IS NOT NULL
        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (o.store_guid = any(acl.list::text[]))
        end
    	and o.copy_order_guid = 0
    	and ( o.state = 4 or ( o.state = 5 and o.recovery_type = 4 and o.recovery_id = '0' ) )
    	and case {{VIEW_TYPE}}
    		when '0' then i.parent_item_guid = 0
    		when '1' then i.item_type != 1
    	end

    	AND i.item_guid NOT IN (
    		'6617780813696073728',
    		'6622454990583627776',
    		'6665469858576072704',
    		'6683296207357345792',
    		'6716295874126807040',
    		'6721589404856483840',
    		'6721590871914971136',
    		'6810777164678430720',
    		'6839738172407021568'
    	)
	[[
        	and case {{BRAND}}
        	    when '1' then o.store_name ~~* '%YM%'
        	    when '2' then o.store_name !~~* '%YM%'
        	end
    	]]
    	[[
    	   -- 统计时间归属节点
    	   -- 1-下单时间,2-结账时间
    	    and case {{ATTRIBUTE}}
    	        when '1' then (
    	            true
    	            [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}} ]]
                    [[and o.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
                    [[and i.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
    	        )
    	        when '2' then (
    	            true
    	            [[and o.checkout_time between {{START_TIME}} AND {{END_TIME}}]]
    	        )
    			when '3' then (
    				true
    				[[and o.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
    				[[and i.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
    			)
            end
    	]]
        [[ and o.order_no ~~ concat('%',{{ORDER_NO}},'%') ]]
        [[ and i.item_type_name ~~ concat('%',{{ITEM_TYPE_NAME}},'%') ]]
        [[ and i.item_name ~~ concat('%',{{ITEM_NAME}},'%') ]]
			GROUP BY i.guid
)
-- 堂食菜品销售明细
select
 		"订单来源" ,
    	"订单号",
    	"门店名称",
        "营业日",
		"区域",
		"桌号",
    	"桌台",
		"就餐人数",
    	"商品名称",
    	"商品规格",
    	"商品分类",
    	"菜品编号",
    	"商品类型",
    	"记数单位",
        "单价",
    	"点餐数量",
    	"赠送数量",
    	"退菜数量",
    	"商品总额",
    	"核算单价",
    	"核算数量",
    	"核算商品总额",
    	"属性加价",
    	"会员价格",
    	"下单时间",
    	"结账时间",
        "订单类型",
    	"下单操作员",
    	"整单备注",
        "赠菜退菜原因",
        "属性",
        "套餐名称"
from (
    SELECT
        t.order_guid::text as "order_guid",
        t.order_item_guid,
        '堂食订单' 订单来源,
        t.order_no AS 订单号,
        t.store_name AS 门店名称,
        t.business_day "营业日",
		t.area_name "区域",
		t.table_code "桌号",
        t.dining_table_name 桌台,
		t.guest_count 就餐人数,
        t.item_name AS 商品名称,
        t.sku_name AS 商品规格,
        t.item_type_name AS 商品分类,
        t.code 菜品编号,
        case t.item_type
            when 1 then '套餐'
            when 2 then '规格'
            when 3 then '称重'
            when 4 then '单品'
        end 商品类型,
        t.unit AS 记数单位,
        t.price 单价,
        t.current_count 点餐数量,
        t.free_count 赠送数量,
        t.return_count 退菜数量,
        (t.current_count + t.free_count) * t.price as 商品总额,
        t.accounting_price 核算单价,
        t.accounting_count 核算数量,
        t.accounting_price * t.accounting_count as 核算商品总额,
        t.append_fee AS 附加费,
        t.attr_total AS 属性加价,
        t.member_price AS 会员价格,
        t.gmt_create 下单时间,
        t.checkout_time 结账时间,
        t.order_source 订单类型,
        t.create_staff_name AS 下单操作员,
        t.remark AS 整单备注,
        ifr.reason AS "赠菜退菜原因",
        ia.attr_name AS 属性,
        t.parent_item_name AS 套餐名称
    from
        trade t left join item_attr ia on t.order_item_guid = ia.order_item_guid left join item_free_return ifr on t.order_item_guid = ifr.order_item_guid
    WHERE
        t.state = 4

    union all

    -- 部分退款单
    SELECT
        t.original_order_guid::text as "order_guid",
        t.order_item_guid,
        '退款单' 订单来源,
        t.order_no AS 订单号,
        t.store_name AS 门店名称,
        t.business_day "营业日",
		t.area_name "区域",
		t.table_code "桌号",
        t.dining_table_name 桌台,
        t.guest_count 就餐人数,
        t.item_name AS 商品名称,
        t.sku_name AS 商品规格,
        t.item_type_name AS 商品分类,
        t.code 菜品编号,
        case t.item_type
            when 1 then '套餐'
            when 2 then '规格'
            when 3 then '称重'
            when 4 then '单品'
        end 商品类型,
        t.unit AS 记数单位,
        t.price 单价,
        - t.current_count 点餐数量,
        - t.free_count 赠送数量,
        0 退菜数量,
        - (t.current_count + t.free_count) * t.price as 商品总额,
        t.accounting_price 核算单价,
        - t.accounting_count 核算数量,
        - t.accounting_price * t.accounting_count as 核算商品总额,
        0 AS 附加费,
        t.attr_total AS 属性加价,
        t.member_price AS 会员价格,
        t.gmt_create 下单时间,
        t.checkout_time 结账时间,
        t.order_source 订单类型,
        t.create_staff_name AS 下单操作员,
        t.remark AS 整单备注,
        ifr.reason AS "赠菜退菜原因",
        ia.attr_name AS 属性,
        t.parent_item_name AS 套餐名称
    from
        trade t left join item_attr ia on t.order_item_guid = ia.order_item_guid left join item_free_return ifr on t.order_item_guid = ifr.order_item_guid
    WHERE
        t.state = 5

    union all

    -- 调整单菜品更换需要先扣减数量
    SELECT
        t.order_guid::text,
        t.order_item_guid,
        '调整单' 订单来源,
        t.order_no AS 订单号,
        t.store_name AS 门店名称,
        t.business_day "营业日",
		t.area_name "区域",
		t.table_code "桌号",
        t.dining_table_name 桌台,
		t.guest_count 就餐人数,
        t.item_name AS 商品名称,
        t.sku_name AS 商品规格,
        t.item_type_name AS 商品分类,
        t.code 菜品编号,
        case t.item_type
            when 1 then '套餐'
            when 2 then '规格'
            when 3 then '称重'
            when 4 then '单品'
        end 商品类型,
        t.unit AS 记数单位,
        t.price 单价,
        - t.current_count 点餐数量,
        - t.free_count 赠送数量,
        - t.return_count 退菜数量,
        - (t.current_count + t.free_count) * t.price as 商品总额,
        t.accounting_price 核算单价,
        - t.accounting_count 核算数量,
        - t.accounting_price * t.accounting_count as 核算商品总额,
        t.append_fee AS 附加费,
        t.attr_total AS 属性加价,
        t.member_price AS 会员价格,
        t.gmt_create 下单时间,
        t.checkout_time 结账时间,
        t.order_source 订单类型,
        t.create_staff_name AS 下单操作员,
        t.remark AS 整单备注,
        ifr.reason AS "赠菜退菜原因",
        ia.attr_name AS 属性,
        t.parent_item_name AS 套餐名称
    from
        (
            SELECT
                DISTINCT ad.order_item_guid,
                ad.gmt_create,
                ad.adjust_type
            from
                "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details ad
            where
                ad.is_delete = 0
        ) a
        join trade t on t.order_item_guid = a.order_item_guid left join item_attr ia on t.order_item_guid = ia.order_item_guid left join item_free_return ifr on t.order_item_guid = ifr.order_item_guid
    WHERE
        t.state = 4
        and a.adjust_type = 1

    union all

    -- 调整单明细
    SELECT
        adjust.order_guid::text,
        adjust.order_item_guid,
        adjust.订单来源,
        adjust.订单号,
        adjust.门店名称,
        adjust.营业日,
		adjust.区域,
		adjust.桌号,
        adjust.桌台,
		adjust.就餐人数,
        adjust.商品名称,
        adjust.商品规格,
        adjust.商品分类,
        sku.code "菜品编号",
        adjust.商品类型,
        adjust.记数单位,
        adjust.price "单价",
        adjust.current_count "点餐数量",
        0 "赠送数量",
        0 "退菜数量",
        adjust.price * adjust.current_count "商品总额",
        adjust.核算单价,
        adjust.current_count "核算数量",
        adjust.核算单价 * adjust.current_count "核算商品总额",
        adjust.附加费,
        adjust.属性加价,
        adjust.会员价格,
        adjust.下单时间,
        adjust.结账时间,
        adjust.订单类型,
        adjust.下单操作员,
        adjust.remark AS 整单备注,
        adjust.reason AS "赠菜退菜原因",
        adjust.attr_name AS 属性,
         adjust.套餐名称
    from (
        SELECT
            t.order_guid,
            t.order_item_guid,
            '调整单' 订单来源,
            t.order_no AS 订单号,
            t.store_name AS 门店名称,
			t.area_name "区域",
			t.table_code "桌号",
            t.business_day "营业日",
            t.dining_table_name 桌台,
			t.guest_count 就餐人数,
            a.item_name AS 商品名称,
            a.sku_name AS 商品规格,
            a.item_type_name AS 商品分类,
            a.sku_guid,
            '' 菜品编号,
            case a.item_type
                when 1 then '套餐'
                when 2 then '规格'
                when 3 then '称重'
                when 4 then '单品'
            end 商品类型,
            a.unit AS 记数单位,
            case when a.adjust_type = 1 and a.parent_item_guid = 0 then a.current_count
            when a.adjust_type = 1 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count
            when a.adjust_type = 0 and a.parent_item_guid = 0 then a.current_count - t.current_count
            when a.adjust_type = 0 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count - t.current_count
            end current_count,
            a.price,
            a.accounting_price 核算单价,
            t.append_fee AS 附加费,
            0 属性加价,
            0 会员价格,
            t.gmt_create 下单时间,
            t.checkout_time 结账时间,
            t.order_source 订单类型,
            t.create_staff_name AS 下单操作员,
            t.remark,
            ifr.reason,
            ia.attr_name,
            t.parent_item_name AS 套餐名称
        from
          "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details a
          left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details aa on a.parent_item_guid = aa.guid and aa.is_delete = 0
          left join trade t on t.order_item_guid = a.order_item_guid
          left join item_attr ia on t.order_item_guid = ia.order_item_guid left join item_free_return ifr on t.order_item_guid = ifr.order_item_guid
        WHERE
            a.is_delete = 0
            and t.state = 4
            and case {{VIEW_TYPE}}
                when '0' then a.parent_item_guid = 0
                when '1' then a.item_type != 1
            end
    ) adjust
    left join "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_sku sku on sku.guid = adjust.sku_guid

    union all

    -- 餐盒费 配送费 附加费 转菜品
    select
        CAST(sec.js->>'order_guid' AS TEXT) as "order_guid",
        CAST(sec.js->>'order_item_guid' AS bigint) as "order_item_guid",
        sec.js->>'订单来源' "订单来源",
        sec.js->>'订单编号' "订单号",
        sec.js->>'门店名称' "门店名称",
        sec.js->>'营业日' "营业日",
		sec.js->>'区域' "区域",
		sec.js->>'桌号' "桌号",
        sec.js->>'桌台' "桌台",
		CAST(sec.js->>'就餐人数' AS bigint) as "就餐人数",
        '餐位费' "商品名称",
        '订单' "商品规格",
        (sec.jsx).key || '系列' "商品分类",
        '' "菜品编号",
        '订单' 商品类型,
        '单' "记数单位",
        (sec.jsx).value::numeric "单价",
        1 "点餐数量",
        0 赠送数量,
        0 退菜数量,
        (sec.jsx).value::numeric "商品总额",
        (sec.jsx).value::numeric 核算单价,
        1 核算数量,
        (sec.jsx).value::numeric 核算商品总额,
        0 附加费,
        0 属性加价,
        0 会员价格,
        (sec.js->>'gmt_create') "下单时间",
        (sec.js->>'结账时间') "结账时间",
        -- null 正餐或快餐,
        sec.js->>'订单类型' "订单类型",
        sec.js->>'操作员' "下单操作员",

        -- sec.js->>'订单子类型' "订单子类型",
        -- null "餐盒费",
        -- sec.js->>'store_guid' "门店GUID"
        '' 整单备注,
        '' "赠菜退菜原因",
        '' 属性,
        '' 套餐名称
    from (
        select
            row_to_json(row) js,
            jsonb_each(
                row_to_json(row)::jsonb
                    -'rowid'
                    -'order_guid'
                    -'order_item_guid'
                    -'订单来源'
                    -'门店名称'
                    -'订单编号'
                    -'订单类型'
                    -'营业日'
                    -'store_guid'
                    -'操作员'
                    -'gmt_create'
                    -'结账时间'
                    -'餐盒费'
                    -'配送费'
					-'区域'
					-'桌号'
                    -'桌台'
					-'就餐人数'
                    -- -'订单子类型'
            ) jsx
        from (
            select
                row_number() over(partition by max(t.order_item_guid)) rowid,
                t.order_guid,
                0 as "order_item_guid",
                '堂食订单' "订单来源",
                max(t.store_name) "门店名称",
                max(t.order_no) "订单编号",
                max(t.business_day) "营业日",
                max(t.order_source) "订单类型",
				max(t.area_name) 区域,
				max(t.table_code) 桌号,
                max(t.dining_table_name) 桌台,
				max(t.guest_count) 就餐人数,
                0 "餐盒费",
                0 "配送费",
                max(t.append_fee) "附加费",
                max(t.store_guid) "store_guid",
                max(t.gmt_create) "gmt_create",
                max(t.checkout_time) "结账时间",
                max(t.create_staff_name) "操作员"
            FROM
                trade t
            where
                t.state = 4
                and t.append_fee > 0
            group by t.order_guid
        ) row
        where
            row.rowid = 1
    ) sec

    union all

    -- 退附加费
    SELECT
        o.guid::text as "order_guid",
        -1 as "order_item_guid",
        '退款单' 订单来源,
        max(o.order_no) AS 订单号,
        max(o.store_name) AS 门店名称,
        max(o.business_day)::text "营业日",
		max(tt.area_name) 区域,
		max(tt.table_code) 桌号,
        max(o.dining_table_name) 桌台,
		max(o.guest_count) 就餐人数,
        '餐位费' "商品名称",
        '订单' "商品规格",
        '附加费系列' "商品分类",
        '' "菜品编号",
        '订单' 商品类型,
        '单' "记数单位",
        sum(a.amount) "单价",
        -1 "点餐数量",
        0 赠送数量,
        0 退菜数量,
        - sum(a.amount) "商品总额",
        sum(a.amount) 核算单价,
        1 核算数量,
        - sum(a.amount) 核算商品总额,
        0 附加费,
        0 属性加价,
        0 会员价格,
        max(o.gmt_create)::text "下单时间",
        max(o.checkout_time)::text "结账时间",
        case
            when max(hsb.brand_guid) = '95a8319b-8297-4f06-a39f-a3c08799dad6' then
                case max(o.trade_mode)
                    when 0 then '堂食外卖'
                    when 1 then '堂食快餐'
                end
            else
                case max(o.trade_mode)
                    when 0 then '正餐'
                    when 1 then '快餐'
                end
        end "订单类型",
        max(o.create_staff_name) "下单操作员",
         '' 整单备注,
        '' "赠菜退菜原因",
        '' 属性,
        '' 套餐名称
    from
        "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_append_fee a
        join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o on o.guid = a.order_guid and o.is_delete = 0
		left join "hst_table_887f2181-eb06-4d77-b914-7c37c884952c_db"."hst_table_basic" tt on o.dining_table_guid = tt.guid
        left join "hso_organization_887f2181-eb06-4d77-b914-7c37c884952c_db".hso_r_store_brand hsb on o.store_guid = hsb.store_guid,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    WHERE
        acl.chmod
        and a.is_delete = 0
        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (o.store_guid = any(acl.list::text[]))
        end
        and o.copy_order_guid = 0
        and o.state = 5 and o.recovery_type = 4 and o.recovery_id = '0'
        [[
            and case {{BRAND}}
                when '1' then o.store_name ~~* '%YM%'
                when '2' then o.store_name !~~* '%YM%'
            end
        ]]
        [[
           -- 统计时间归属节点
           -- 1-下单时间,2-结账时间
            and case {{ATTRIBUTE}}
                when '1' then (
                    true
                    [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}} ]]
                    [[and o.business_day between {{START_TIME}}::TIMESTAMP + '-1 month' AND {{END_TIME}}::TIMESTAMP + '1 month']]
                )
                when '2' then (
                    true
                    [[and o.checkout_time between {{START_TIME}} AND {{END_TIME}}]]
                )
                when '3' then (
                    true
                    [[and o.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
                )
            end
        ]]
        [[ and o.order_no ~~ concat('%',{{ORDER_NO}},'%') ]]
    group by o.guid
) list
where
    order_guid not in (
        SELECT
            order_guid::text
        FROM
            "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_transaction_record
         where
            payment_type = 10 and payment_type_name in ('美团生食闪购','抖音外卖')
    )
order by
 	order_guid asc, order_item_guid asc, 下单时间 asc