DROP TABLE IF EXISTS `hst_multiple_transaction_record`;
CREATE TABLE `hst_multiple_transaction_record` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '多次支付唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` bigint(20) NOT NULL COMMENT '订单guid',
  `transaction_record_guid` bigint(20) NOT NULL COMMENT '交易记录guid',
  `app_id` varchar(50) DEFAULT NULL COMMENT '聚合支付商户的appId',
  `pay_power_id` int(11) DEFAULT NULL COMMENT '聚合支付对应的支付方式',
  `pay_power_name` varchar(50) DEFAULT NULL COMMENT '聚合支付对应的支付方式名称',
  `item_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `body` varchar(50) DEFAULT NULL COMMENT '商品信息',
  `description` varchar(128) DEFAULT NULL COMMENT '支付单附加描述',
  `terminal_id` varchar(50) DEFAULT NULL COMMENT '设备号',
  `jh_order_guid` varchar(50) DEFAULT NULL COMMENT '聚合支付订单号',
  `bank_transaction_id` varchar(50) DEFAULT '' COMMENT '银行流水号（人脸支付流水号）',
  `code_url` varchar(200) DEFAULT NULL COMMENT '支付二维码链接地址',
  `amount` decimal(15,2) DEFAULT '0.00' COMMENT '交易金额',
  `discount_fee` decimal(15,2) DEFAULT '0.00' COMMENT '聚合支付优惠金额',
  `refundable_fee` decimal(15,2) DEFAULT '0.00' COMMENT '剩余可退款金额',
  `trade_type` int(11) DEFAULT NULL COMMENT '交易类型，1:正常支付转入，2:会员充值转入，3:预付金转入，4:定金转入，5:正常支付退款，6:退单退款，7:会员余额退款，8:预付金退款，9:定金退款，10:自定义退款',
  `state` int(11) DEFAULT NULL COMMENT '1：待支付 2：支付中 3：支付失败 4：支付成功',
  `payment_type` int(11) NOT NULL COMMENT '支付方式 1：现金支付，2：聚合支付，3：银行卡支付，4:会员卡支付，5:人脸支付，6:通吃岛支付，7:预定金支付，10：其他支付方式',
  `payment_type_name` varchar(50) DEFAULT NULL COMMENT '支付方式名',
  `create_time` datetime NOT NULL COMMENT '支付单创建时间',
  `member_guid` varchar(50) DEFAULT '' COMMENT '会员相关信息',
  `business_day` date DEFAULT NULL COMMENT '营业日时间',
  `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
  `remark` varchar(128) DEFAULT NULL COMMENT '备注',
  `face_code` varchar(20) DEFAULT NULL COMMENT '人脸支付失败时安卓自己随机加的3位数字',
  `staff_guid` varchar(50) DEFAULT NULL COMMENT '操作人员guid',
  `staff_name` varchar(50) DEFAULT NULL COMMENT '操作人名字',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名',
  `store_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '门店guid',
  `refund_amount` decimal(15,2) DEFAULT '0.00' COMMENT '退款金额',
  PRIMARY KEY (`guid`),
  KEY `idx_order_guid` (`order_guid`),
  KEY `idx_store_guid` (`store_guid`),
  KEY `idx_business_day` (`business_day`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE,
  KEY `idx_staff_guid` (`staff_guid`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单多次支付交易记录';


ALTER TABLE `hst_order_extends` 
ADD COLUMN `is_multiple_agg_pay` tinyint(1) NULL DEFAULT 0 COMMENT '是否多次聚合支付 0否 1是' ;

ALTER TABLE `hst_transaction_record` 
ADD COLUMN `is_multiple_agg_pay` tinyint(1) NULL DEFAULT 0 COMMENT '是否多次聚合支付 0否 1是' ;

ALTER TABLE hst_multiple_transaction_record ADD COLUMN `original_multiple_transaction_record_guid` bigint(20) DEFAULT NULL COMMENT '反结账原单支付记录id';
ALTER TABLE hst_multiple_transaction_record ADD COLUMN `original_order_guid` bigint(20) DEFAULT NULL COMMENT '反结账/并桌子桌原订单guid';

