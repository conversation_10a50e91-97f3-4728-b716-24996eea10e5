ALTER TABLE `hsk_kitchen_item` DROP INDEX `idx_prepare_item`;
ALTER TABLE `hsk_kitchen_item` DROP INDEX `idx_prd_item`;
ALTER TABLE `hsk_kitchen_item` DROP INDEX `idx_prd_point_item`;
ALTER TABLE `hsk_kitchen_item` DROP INDEX `idx_dst_item`;
ALTER TABLE `hsk_kitchen_item` ADD UNIQUE INDEX `uk_guid` (`guid` ASC);
ALTER TABLE `hsk_kitchen_item` ADD INDEX `idx_order_item` (`order_item_guid` ASC);
ALTER TABLE `hsk_kitchen_item` ADD INDEX `idx_prepare_item` (`order_guid` ASC, `kitchen_state` ASC, `item_state` ASC);
ALTER TABLE `hsk_kitchen_item` ADD INDEX `idx_prd_item` (`store_guid` ASC, `prd_device_id` ASC, `kitchen_state` ASC, `item_state` ASC);
ALTER TABLE `hsk_kitchen_item` ADD INDEX `idx_prd_point_item` (`point_guid` ASC, `kitchen_state` ASC, `item_state` ASC);
ALTER TABLE `hsk_kitchen_item` ADD INDEX `idx_dst_item` (`store_guid` ASC, `dst_device_id` ASC, `display_type` ASC, `kitchen_state` ASC, `item_state` ASC);
ALTER TABLE `hsk_kitchen_item` ADD INDEX `idx_dst_item_all` (`store_guid` ASC, `dst_device_id` ASC, `kitchen_state` ASC, `item_state` ASC);
ALTER TABLE `hsk_kitchen_item` ADD INDEX `idx_prepare_remained` (`display_type` ASC, `order_guid` ASC);

ALTER TABLE `hsk_production_point` DROP INDEX `idx_store`;
ALTER TABLE `hsk_production_point` ADD INDEX `idx_store` (`store_guid` ASC, `device_id` ASC, `guid` ASC);

ALTER TABLE `hsk_print_record` DROP INDEX `id_UNIQUE`;
ALTER TABLE `hsk_print_record` ADD UNIQUE INDEX `uk_guid` (`guid` ASC);
ALTER TABLE `hsk_print_record` ADD INDEX `idx_list_record` (`device_id` ASC, `print_status` ASC);

ALTER TABLE `hsk_store_printer` DROP INDEX `id_UNIQUE`;
ALTER TABLE `hsk_store_printer` ADD UNIQUE INDEX `uk_guid` (`guid` ASC);

ALTER TABLE `hsk_queue_item` ADD UNIQUE INDEX `uk_guid` (`guid` ASC);
ALTER TABLE `hsk_queue_item` ADD INDEX `idx_intime` (`store_guid` ASC, `device_guid` ASC, `in_time` ASC);
ALTER TABLE `hsk_queue_item` ADD INDEX `idx_expire` (`store_guid` ASC, `device_guid` ASC, `dst_expire_time` ASC);
ALTER TABLE `hsk_queue_item` ADD INDEX `idx_order_status` (`order_guid` ASC, `status` ASC);