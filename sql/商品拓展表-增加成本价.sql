DROP TABLE IF EXISTS `hst_order_item_extends`;
CREATE TABLE `hst_order_item_extends` (
  `guid` bigint(20) NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` bigint(20) NOT NULL COMMENT '订单guid',
  `user_wx_public_open_id` varchar(128) NOT NULL DEFAULT '0' COMMENT '用户微信公众号openId',
  `wx_batch` varchar(50) NOT NULL DEFAULT '0' COMMENT '微信菜品批次',
  `pad_order_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT 'pad下单guid',
  `cost_price` decimal(12,2) DEFAULT '0.00' COMMENT '成本价',
  PRIMARY KEY (`guid`),
  KEY `idx_order_guid` (`order_guid`) USING BTREE,
  KEY `idx_pad_order_guid` (`pad_order_guid`) USING BTREE,
  KEY `idx_wx_id` (`user_wx_public_open_id`) USING BTREE,
  KEY `idx_gmt` (`gmt_create`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单商品扩展表';