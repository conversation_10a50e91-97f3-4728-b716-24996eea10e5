CREATE DATABASE `hsm_mdm_store_db` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

CREATE TABLE `hsm_mdm_relation` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `uuid` varchar(50) DEFAULT NULL COMMENT 'MDM数据唯一标识',
    `guid` varchar(50) DEFAULT NULL COMMENT '门店数据唯一标识',
    `erp_guid` varchar(50) DEFAULT NULL COMMENT '商户唯一标识',
    `table_name` varchar(50) DEFAULT NULL COMMENT '数据所在表名',
    `is_delete` tinyint(1) DEFAULT '0' COMMENT '逻辑删除：0=未删除，1=已删除',
    `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
    `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `uk_uuid_2_guid` (`uuid`,`table_name`,`guid`),
    KEY `uk_guid_2_uuid` (`guid`,`table_name`,`uuid`),
    KEY `idx_erp_guid` (`erp_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='主数据关系表';