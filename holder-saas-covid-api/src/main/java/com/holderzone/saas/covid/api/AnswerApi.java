package com.holderzone.saas.covid.api;

import com.holderzone.saas.covid.api.dto.AnswerDTO;
import com.holderzone.saas.covid.api.dto.PageDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Api("COVID-表单")
@RequestMapping("/answer")
@FeignClient(value = "holder-saas-covid-form", fallbackFactory = AnswerApi.FallBack.class)
public interface AnswerApi {

    @GetMapping("/query")
    @ApiOperation("根据GUID查询答案")
    AnswerDTO query(@RequestParam("guid") Long guid);

    @PostMapping("/query_by_uid")
    @ApiOperation("根据填写人ID查询答案")
    AnswerDTO queryByUid(@Validated(AnswerDTO.UID.class) @RequestBody AnswerDTO answerDTO);

    @PostMapping("/save")
    @ApiOperation("保存或修改答案")
    Long save(@Validated(AnswerDTO.SAVE.class) @RequestBody AnswerDTO answerDTO);

    @PostMapping("/list")
    @ApiOperation("查询答案列表")
    List<AnswerDTO> list(@Validated(AnswerDTO.LIST.class) @RequestBody AnswerDTO answerDTO);

    @PostMapping("/page")
    @ApiOperation("查询答案列表")
    PageDTO<AnswerDTO> page(@Validated(AnswerDTO.PAGE.class) @RequestBody AnswerDTO answerDTO);

    @PostMapping("/export")
    @ApiOperation("导出答案列表")
    void export(@Validated(AnswerDTO.LIST.class) @RequestBody AnswerDTO answerDTO, HttpServletResponse response) throws IOException;

    @Slf4j
    @Component
    class FallBack implements FallbackFactory<AnswerApi> {

        @Override
        public AnswerApi create(Throwable throwable) {
            return new AnswerApi() {

                @Override
                public AnswerDTO query(Long guid) {
                    return null;
                }

                @Override
                public AnswerDTO queryByUid(AnswerDTO answerDTO) {
                    return null;
                }

                @Override
                public Long save(AnswerDTO answerDTO) {
                    return null;
                }

                @Override
                public List<AnswerDTO> list(AnswerDTO answerDTO) {
                    return null;
                }

                @Override
                public PageDTO<AnswerDTO> page(AnswerDTO answerDTO) {
                    return null;
                }

                @Override
                public void export(AnswerDTO answerDTO, HttpServletResponse response) {

                }
            };
        }
    }
}