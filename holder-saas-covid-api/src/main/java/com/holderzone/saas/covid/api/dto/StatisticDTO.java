/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:StatisticDTO.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.api.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("调查问卷统计")
public class StatisticDTO {

    /**
     * 所属小区
     */
    @ExcelProperty(value = "所属小区", index = 0)
    @ApiModelProperty("所属小区")
    private String community;

    /**
     * 总人数
     */
    @ExcelProperty(value = "总人数", index = 1)
    @ApiModelProperty("总人数")
    private Integer total;

    /**
     * 旧三类小计
     */
    @ExcelProperty(value = "老三类小计(人)", index = 2)
    @ApiModelProperty("老三类小计(人)")
    private String oldThreeCategory;

    /**
     * 是否湖北籍人员，根据idCard规则来判断
     */
    @ExcelProperty(value = "湖北籍人员(人)", index = 3)
    @ApiModelProperty("是否湖北籍人员(人)")
    private Integer huBeiNative;

    /**
     * 近期有疫情居住或旅行史
     */
    @ExcelProperty(value = "近期有疫情居住或旅行史(人)", index = 4)
    @ApiModelProperty("近期有疫情居住或旅行史(人)")
    private Integer liveTravelInfectedArea;

    /**
     * 近期与疫区外来人员密切接触式
     */
    @ExcelProperty(value = "近期与疫区外来人员密切接触史(人)", index = 5)
    @ApiModelProperty("近期与疫区外来人员密切接触史(人)")
    private Integer contactWithInfectedArea;

    /**
     * 与疑似和确诊病人有接触
     */
    @ExcelProperty(value = "与疑似和确诊病人有接触(人)", index = 6)
    @ApiModelProperty("与疑似和确诊病人有接触(人)")
    private Integer contactWithSuspectOrInfected;

    /**
     * 新三类小计
     */
    @ExcelProperty(value = "新三类小计(人)", index = 7)
    @ApiModelProperty("新三类小计(人)")
    private String newThreeCategory;

    /**
     * 与到过武汉的人有接触史
     */
    @ExcelProperty(value = "与到过武汉的人有接触史(人)", index = 8)
    @ApiModelProperty("与到过武汉的人有接触史(人)")
    private Integer contactWithWuHan;

    /**
     * 近期去过疫情或者外地旅游
     */
    @ExcelProperty(value = "近期去过疫情或者外地旅游(人)", index = 9)
    @ApiModelProperty("近期去过疫情或者外地旅游(人)")
    private Integer travelOutSide;

    /**
     * 重庆地区往(返)人员
     */
    @ExcelProperty(value = "重庆地区往(返)人员(人)", index = 10)
    @ApiModelProperty("重庆地区往(返)人员(人)")
    private Integer travelInChongQing;

    /**
     * 浙江、广东、湖南地区往(返)人员
     */
    @ExcelProperty(value = "浙江、广东、湖南地区往(返)人员(人)", index = 11)
    @ApiModelProperty("浙江、广东、湖南地区往(返)人员(人)")
    private Integer travelInZheGuangHu;

    /**
     * 医学观察类小计
     */
    @ExcelProperty(value = "医学观察类小计(人)", index = 12)
    @ApiModelProperty("医学观察类小计(人)")
    private Integer observeCategory;

    /**
     * 居家医学观察人员
     */
    @ExcelProperty(value = "居家医学观察人员(人)", index = 13)
    @ApiModelProperty("居家医学观察人员(人)")
    private Integer underObservation;

    /**
     * 当日满14天人数
     */
    @ExcelProperty(value = "当日满14天人数(人)", index = 14)
    @ApiModelProperty("当日满14天人数(人)")
    private Integer observeFourteen;

    /**
     * 已解除观察人数
     */
    @ExcelProperty(value = "已解除观察人数(人)", index = 15)
    @ApiModelProperty("已解除观察人数(人)")
    private Integer observeTerminate;

    /**
     * 省外往(返)人员
     */
    @ExcelProperty(value = "市外往(返)人员小计(人)", index = 16)
    @ApiModelProperty("市外往(返)人员小计(人)")
    private Integer travelOutsideCity;

    /**
     * 省外往(返)人员
     */
    @ExcelProperty(value = "省外往(返)人员(人)", index = 17)
    @ApiModelProperty("省外往(返)人员(人)")
    private Integer travelOutsideProvince;

    /**
     * 省内非本市往(返)人员
     */
    @ExcelProperty(value = "省内非本市往(返)人员(人)", index = 18)
    @ApiModelProperty("省内非本市往(返)人员(人)")
    private Integer travelNonCityInProvince;

    /**
     * 外登记车辆
     */
    @ExcelProperty(value = "外登记车辆(台)", index = 19)
    @ApiModelProperty("外登记车辆(台)")
    private Integer car;
}
