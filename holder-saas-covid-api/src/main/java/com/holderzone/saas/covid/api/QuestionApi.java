package com.holderzone.saas.covid.api;

import com.holderzone.saas.covid.api.dto.QuestionDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api("COVID-表单")
@RequestMapping("/question")
@FeignClient(value = "holder-saas-covid-form", fallbackFactory = QuestionApi.FallBack.class)
public interface QuestionApi {

    @GetMapping("/query")
    @ApiOperation("根据问卷GUID查询配置")
    QuestionDTO query(@RequestParam("guid") Long guid);

    @GetMapping("/query_by_uid")
    @ApiOperation("根据发起人UID查询配置")
    QuestionDTO queryByUid(@RequestParam("uid") String uid);

    @PostMapping("/save")
    @ApiOperation("保存配置")
    Long save(@RequestBody @Valid QuestionDTO questionDTO);

    @GetMapping("/download_h5")
    @ApiOperation("获取H5二维码链接")
    String downloadH5(@RequestParam("guid") Long guid);

    @GetMapping("/download_mp")
    @ApiOperation("获取小程序二维码链接")
    String downloadMp(@RequestParam("guid") Long guid);

    @Slf4j
    @Component
    class FallBack implements FallbackFactory<QuestionApi> {

        @Override
        public QuestionApi create(Throwable throwable) {
            return new QuestionApi() {

                @Override
                public QuestionDTO query(Long guid) {
                    return null;
                }

                @Override
                public QuestionDTO queryByUid(String uid) {
                    return null;
                }

                @Override
                public Long save(QuestionDTO questionDTO) {
                    return null;
                }

                @Override
                public String downloadH5(Long guid) {
                    return null;
                }

                @Override
                public String downloadMp(Long guid) {
                    return null;
                }
            };
        }
    }
}