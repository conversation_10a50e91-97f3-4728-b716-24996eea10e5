package com.holderzone.saas.covid.api.dto;

import lombok.Data;

@Data
public class ResultDTO<T> {

    private Boolean success;

    private String message;

    private T data;

    public static ResultDTO<Void> success() {
        ResultDTO<Void> result = new ResultDTO<>();
        result.setSuccess(true);
        result.setMessage("成功");
        return result;
    }

    public static <T> ResultDTO success(T data) {
        ResultDTO<T> result = new ResultDTO<>();
        result.setSuccess(true);
        result.setMessage("成功");
        result.setData(data);
        return result;
    }

    public static ResultDTO error() {
        ResultDTO<Void> result = new ResultDTO<>();
        result.setSuccess(false);
        result.setMessage("失败");
        return result;
    }

    public static ResultDTO error(String message) {
        ResultDTO<Void> result = new ResultDTO<>();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
}
