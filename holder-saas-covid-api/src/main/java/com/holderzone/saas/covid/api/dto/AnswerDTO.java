/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:AnswerDTO.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.api.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.holderzone.saas.covid.api.converter.LocalDateConverter;
import com.holderzone.saas.covid.api.converter.LocalDateTimeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.Default;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("调查问卷答案")
public class AnswerDTO {

    public interface UID extends Default {
    }

    public interface SAVE extends Default {
    }

    public interface LIST extends Default {
    }

    public interface PAGE extends LIST {
    }

    /**
     * REDIS主键、调查问卷答案GUID
     */
    @ExcelIgnore
    @ApiModelProperty("调查问卷答案GUID，该值不为空时必传")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long guid;

    /**
     * 填卷人标识
     */
    @ExcelIgnore
    @ApiModelProperty(value = "填卷人标识，查询和保存时必传", required = true)
    @NotEmpty(message = "填卷人标识不得为空", groups = {UID.class, SAVE.class})
    @Size(max = 45, message = "填卷人标识最大长度为45", groups = {UID.class, SAVE.class})
    private String uid;

    /**
     * 开始日期
     */
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "开始日期不得为空", groups = LIST.class)
    @ApiModelProperty(value = "开始日期，查询/分页查询/导出列表时必传", required = true, example = "2020-02-17")
    private LocalDate fromDate;

    /**
     * 结束日期
     */
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "结束日期不得为空", groups = LIST.class)
    @ApiModelProperty(value = "结束日期，查询/分页查询/导出列表时必传", required = true, example = "2020-02-17")
    private LocalDate toDate;

    /**
     * 调查问卷GUID
     */
    @ExcelIgnore
    @ApiModelProperty(value = "所属调研表GUID", required = true)
    @NotNull(message = "所属调研表GUID不得为空，所有接口必传", groups = {UID.class, SAVE.class, LIST.class})
    @JsonSerialize(using = ToStringSerializer.class)
    private Long questionGuid;

    /**
     * 填卷日期
     */
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @ApiModelProperty(value = "填写时间", hidden = true)
    private LocalDateTime gmtCreate;

    /**
     * 填卷日期
     */
    @ApiModelProperty("填写/修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @ExcelProperty(value = {"个人信息", "填写时间"}, index = 0, converter = LocalDateTimeConverter.class)
    private LocalDateTime gmtModified;

    /**
     * 您的姓名
     */
    @ExcelProperty(value = {"个人信息", "姓名"}, index = 1)
    @ApiModelProperty(value = "您的姓名，保存时必传", required = true, example = "张三")
    @NotEmpty(message = "姓名不得为空", groups = SAVE.class)
    @Size(max = 45, message = "姓名最大长度为45", groups = SAVE.class)
    private String name;

    /**
     * 您的性别
     */
    @ExcelProperty(value = {"个人信息", "性别"}, index = 2)
    @ApiModelProperty(value = "您的性别，保存时必传", required = true, example = "男")
    @NotEmpty(message = "性别不得为空", groups = SAVE.class)
    @Size(max = 45, message = "性别最大长度为45", groups = SAVE.class)
    private String gender;

    /**
     * 您的年龄
     */
    @ExcelProperty(value = {"个人信息", "年龄"}, index = 3)
    @ApiModelProperty(value = "您的年龄，保存时必传", required = true, example = "18")
    @NotNull(message = "年龄不得为空", groups = SAVE.class)
    @Min(value = 0, message = "年龄最小为0", groups = SAVE.class)
    @Max(value = 255, message = "年龄最大为255", groups = SAVE.class)
    private Integer age;

    /**
     * 您的身份证号
     */
    @ExcelProperty(value = {"个人信息", "身份证号"}, index = 4)
    @ApiModelProperty(value = "您的身份证号，保存时必传", required = true, example = "513821199001016666")
    @NotEmpty(message = "身份证号不得为空", groups = SAVE.class)
    @Size(max = 45, message = "身份证号最大长度为45", groups = SAVE.class)
    private String idCard;

    /**
     * 您的联系方式
     */
    @ExcelProperty(value = {"个人信息", "联系方式"}, index = 5)
    @ApiModelProperty(value = "您的联系方式，保存时必传", required = true, example = "13888888888")
    @NotEmpty(message = "联系方式不得为空", groups = SAVE.class)
    @Size(max = 45, message = "联系方式最大长度为45", groups = SAVE.class)
    private String phone;

    /**
     * 您的现住地(小区楼栋单元房号)
     * 所在小区
     */
    @ExcelProperty(value = {"个人信息", "现住地小区"}, index = 6)
    @ApiModelProperty(value = "所在小区，保存时必传", required = true, example = "碧桂园")
    @NotEmpty(message = "现住地小区不得为空", groups = SAVE.class)
    @Size(max = 100, message = "现住地小区最大长度为100", groups = SAVE.class)
    private String community;

    /**
     * 您的现住地(小区楼栋单元房号)
     * 楼栋信息
     */
    @ExcelProperty(value = {"个人信息", "现住地房号"}, index = 7)
    @ApiModelProperty(value = "楼栋信息，保存时必传", required = true, example = "1栋1单元101")
    @NotEmpty(message = "现住地房号不得为空", groups = SAVE.class)
    @Size(max = 100, message = "现住地房号最大长度为100", groups = SAVE.class)
    private String building;

    /**
     * 您的所居住的房屋性质
     * 自有住房、租住、其他
     */
    @ExcelProperty(value = {"个人信息", "居住类型"}, index = 8)
    @NotEmpty(message = "居住类型不得为空", groups = SAVE.class)
    @Size(max = 45, message = "居住类型最大长度为45", groups = SAVE.class)
    @ApiModelProperty(value = "居住类型：自有住房、租住、其他，保存时必传", required = true, example = "自有住房")
    private String houseType;

    /**
     * 您的家中常住人数
     */
    @ExcelProperty(value = {"个人信息", "家中常住人数"}, index = 9)
    @ApiModelProperty(value = "您的家中常住人数，保存时必传", required = true, example = "1")
    @NotNull(message = "家中常住人数不得为空", groups = SAVE.class)
    @Min(value = 1, message = "家中常住人数最少为1", groups = SAVE.class)
    @Max(value = 255, message = "家中常住人数最多为255", groups = SAVE.class)
    private Integer residentNum;

    /**
     * 近期(1月8日)以来接触过从湖北、重庆等疫情高发地区来访人员
     */
    @ExcelProperty(value = {"出行情况", "是否来自疫情发放地区"}, index = 10)
    @NotEmpty(message = "是否来自疫情发放地区不得为空", groups = SAVE.class)
    @Size(max = 45, message = "是否来自疫情发放地区最大长度为45", groups = SAVE.class)
    @ApiModelProperty(value = "近期(1月8日)以来接触过从湖北、重庆等疫情高发地区来访人员，保存时必传", required = true, example = "否")
    private String contactWithInfectedArea;

    /**
     * 近期(1月29日)以来接触过外市的来访人员
     */
    @ExcelProperty(value = {"出行情况", "接触过外市的来访人员"}, index = 11)
    @NotEmpty(message = "是否接触过外市的来访人员不得为空", groups = SAVE.class)
    @Size(max = 45, message = "是否接触过外市的来访人员最大长度为45", groups = SAVE.class)
    @ApiModelProperty(value = "近期(1月29日)以来接触过外市的来访人员，保存时必传", required = true, example = "否")
    private String contactWithOutCity;

    /**
     * 1月8日后是否去过市外
     */
    @ExcelProperty(value = {"出行情况", "是否去过外市"}, index = 12)
    @ApiModelProperty(value = "1月8日后是否去过市外，保存时必传", required = true, example = "否")
    @NotEmpty(message = "是否去过外市不得为空", groups = SAVE.class)
    @Size(max = 45, message = "是否去过外市最大长度为45", groups = SAVE.class)
    private String travelOutside;

    /**
     * 所到城市
     */
    @Nullable
    @ExcelProperty(value = {"出行情况", "所到城市"}, index = 13)
    @Size(max = 45, message = "所到城市最大长度为45")
    @ApiModelProperty(value = "所到城市", example = "四川省南充市")
    private String travelCity;

    /**
     * 外出时间
     */
    @Nullable
    @PastOrPresent(message = "请输入过去或当前时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = {"出行情况", "所到时间"}, index = 14, converter = LocalDateConverter.class)
    @ApiModelProperty(value = "外出时间", example = "2020-02-17")
    private LocalDate travelDate;

    /**
     * 出行方式
     */
    @Nullable
    @ExcelIgnore
    @Size(max = 45, message = "出行方式最大长度为45")
    @ApiModelProperty(value = "出行方式", example = "私车")
    private String travelMode;

    /**
     * 车牌或班次
     */
    @Nullable
    @ExcelIgnore
    @Size(max = 45, message = "车牌或班次最大长度为45")
    @ApiModelProperty(value = "车牌或班次", example = "川A0000")
    private String travelDetail;

    /**
     * 出行方式+车牌或班
     */
    @JsonIgnore
    @ExcelProperty(value = {"出行情况", "出行方式"}, index = 15)
    @ApiModelProperty(value = "出行方式+车牌或班", hidden = true)
    private String travelModeDetail;

    /**
     * 您及家人目前的身体状况
     */
    @ExcelProperty(value = {"健康状况", "身体健康状况"}, index = 16)
    @NotEmpty(message = "身体健康状况不得为空", groups = SAVE.class)
    @Size(max = 100, message = "身体健康状况最大长度为100", groups = SAVE.class)
    @ApiModelProperty(value = "您及家人目前的身体状况，保存时必传", required = true, example = "身体健康无异常")
    private String health;

    /**
     * 今日测量体温(℃)
     */
    @ExcelProperty(value = {"健康状况", "今日测量体温"}, index = 17)
    @NotNull(message = "今日测量体温不得为空", groups = SAVE.class)
    @DecimalMin(value = "0.00", message = "今日测量体温最低为0.00", groups = SAVE.class)
    @DecimalMax(value = "99.99", message = "今日测量体温最该为99.99", groups = SAVE.class)
    @ApiModelProperty(value = "今日测量体温(℃)，保存时必传", required = true, example = "36.2")
    private BigDecimal temperature;

    /**
     * 您是否有以下症状
     */
    @ExcelProperty(value = {"健康状况", "今日症状"}, index = 18)
    @NotEmpty(message = "今日症状不得为空", groups = SAVE.class)
    @Size(max = 100, message = "今日症状最大长度为100", groups = SAVE.class)
    @ApiModelProperty(value = "您是否有以下症状，保存时必传", required = true, example = "无上述症状")
    private String symptom;

    /**
     * 您的诊疗请况
     * 诊疗时间
     */
    @Nullable
    @PastOrPresent(message = "请输入过去或当前时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = {"诊疗情况", "诊疗时间"}, index = 19, converter = LocalDateConverter.class)
    @ApiModelProperty(value = "诊疗时间", example = "2020-02-17")
    private LocalDate treatmentDate;

    /**
     * 您的诊疗请况
     * 诊疗机构
     */
    @Nullable
    @ExcelProperty(value = {"诊疗情况", "诊疗机构"}, index = 20)
    @Size(max = 45, message = "诊疗机构最大长度为45")
    @ApiModelProperty(value = "诊疗机构", example = "XXX诊所")
    private String treatmentAgency;

    /**
     * 您的诊疗请况
     * 诊疗结果
     */
    @Nullable
    @ExcelProperty(value = {"诊疗情况", "诊疗结果"}, index = 21)
    @Size(max = 45, message = "诊疗结果最大长度为45")
    @ApiModelProperty(value = "诊疗结果", example = "留观")
    private String treatmentResult;

    /**
     * 您的返(赴)所在区后接触的人员信息
     * 姓名
     * 性别
     * 年龄
     * 现住地
     * 电话
     */
    @Valid
    @Nullable
    @ExcelIgnore
    @ApiModelProperty("您的返(赴)所在区后接触的人员信息")
    private List<ContactDTO> contactPeople;

    @JsonIgnore
    @ExcelProperty(value = "接触人员情况", index = 22)
    @ApiModelProperty(value = "您的返(赴)所在区后接触的人员信息", hidden = true)
    private String contactPeopleString;

    @ExcelIgnore
    @ApiModelProperty(value = "答案对应的调查问卷")
    private QuestionDTO question;

    @ExcelIgnore
    @ApiModelProperty("当前页码，分页查询时必传")
    @NotNull(message = "当前页码不得为空", groups = PAGE.class)
    private Long currentPage;

    @ExcelIgnore
    @ApiModelProperty("每页数量，分页查询时必传")
    @NotNull(message = "每页数量不得为空", groups = PAGE.class)
    private Long pageSize;
}