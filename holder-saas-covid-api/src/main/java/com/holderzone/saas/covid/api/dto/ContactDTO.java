/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:ContactDTO.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020-02-16 下午4:22
 */
@Data
@ApiModel("接触人员")
public class ContactDTO {

    @NotEmpty(message = "接触人员的姓名不得为空")
    @ApiModelProperty(value = "接触人员的姓名", required = true)
    private String name;

    @NotEmpty(message = "接触人员的性别不得为空")
    @ApiModelProperty(value = "接触人员的性别", required = true)
    private String gender;

    @NotNull(message = "接触人员的年龄不得为空")
    @ApiModelProperty(value = "接触人员的年龄", required = true)
    private Integer age;

    @NotEmpty(message = "接触人员的现住地不得为空")
    @ApiModelProperty(value = "接触人员的现住地", required = true)
    private String address;

    @NotEmpty(message = "接触人员的电话不得为空")
    @ApiModelProperty(value = "接触人员的电话", required = true)
    private String phone;

    public String assemble() {
        StringBuilder stringBuilder = new StringBuilder();
        if (name != null) {
            stringBuilder.append(name).append("/");
        }
        if (gender != null) {
            stringBuilder.append(gender).append("/");
        }
        if (age != null) {
            stringBuilder.append(age).append("/");
        }
        if (address != null) {
            stringBuilder.append(address).append("/");
        }
        if (phone != null) {
            stringBuilder.append(phone).append("/");
        }
        if (0 == stringBuilder.length()) {
            return "";
        }
        return stringBuilder.subSequence(0, stringBuilder.length() - 1).toString();
    }
}
