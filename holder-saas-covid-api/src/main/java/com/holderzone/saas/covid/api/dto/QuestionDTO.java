/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:QuestionDTO.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel("调查问卷")
public class QuestionDTO {

    /**
     * REDIS主键、调查问卷GUID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("调查问卷GUID")
    private Long guid;

    /**
     * 持卷人标识
     */
    @Size(min = 1, max = 45, message = "持卷人标识最大长度为45")
    @ApiModelProperty(value = "持卷人标识", required = true)
    private String uid;

    /**
     * 调查问卷城市
     */
    @Size(max = 45, message = "调查问卷城市最大长度为45")
    @ApiModelProperty(value = "调查问卷城市", required = true)
    private String city;

    /**
     * 调查问卷名称
     */
    @Size(max = 45, message = "调查问卷名称最大长度为45")
    @ApiModelProperty(value = "调查问卷名称", required = true)
    private String name;

    /**
     * 调查问卷目的
     */
    @ApiModelProperty("调查问卷目的")
    private String description;

    /**
     * 社区内的小区
     */
    @ApiModelProperty(value = "社区内的小区", required = true)
    private List<String> communities;

    /**
     * 保留字段
     * 题目详情，列表JSON
     */
    @ApiModelProperty("题目详情，列表JSON")
    private String questions;

    /**
     * H5二维码地址
     */
    @ApiModelProperty("H5二维码地址")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String h5Url;

    /**
     * 小程序水滴码地址
     */
    @ApiModelProperty("小程序水滴码地址")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String mpUrl;
}
