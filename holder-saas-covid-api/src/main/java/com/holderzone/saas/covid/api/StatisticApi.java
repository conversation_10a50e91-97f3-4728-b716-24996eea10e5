package com.holderzone.saas.covid.api;

import com.holderzone.saas.covid.api.dto.AnswerDTO;
import com.holderzone.saas.covid.api.dto.StatisticDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Api("COVID-表单")
@RequestMapping("/statistic")
@FeignClient(value = "holder-saas-covid-form", fallbackFactory = StatisticApi.FallBack.class)
public interface StatisticApi {

    @PostMapping("/list")
    @ApiOperation("查询统计")
    List<StatisticDTO> list(@Validated(AnswerDTO.LIST.class) @RequestBody AnswerDTO statisticDTO);

    @PostMapping("/export")
    @ApiOperation("导出统计表")
    void export(@Validated(AnswerDTO.LIST.class) @RequestBody AnswerDTO answerDTO, HttpServletResponse response) throws IOException;

    @Slf4j
    @Component
    class FallBack implements FallbackFactory<StatisticApi> {

        @Override
        public StatisticApi create(Throwable throwable) {
            return new StatisticApi() {

                @Override
                public List<StatisticDTO> list(AnswerDTO statisticDTO) {
                    return null;
                }

                @Override
                public void export(AnswerDTO answerDTO, HttpServletResponse response) {

                }
            };
        }
    }
}