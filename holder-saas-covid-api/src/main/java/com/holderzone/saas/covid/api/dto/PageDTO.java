package com.holderzone.saas.covid.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("分页对象")
public class PageDTO<T> {

    @ApiModelProperty("当前页码")
    private long currentPage;

    @ApiModelProperty("每页数量")
    private long pageSize;

    @ApiModelProperty("总计页数")
    private long totalPage;

    @ApiModelProperty("总计数量")
    private long totalCount;

    @ApiModelProperty("数据集合")
    private List<T> data;
}
