<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.holderzone</groupId>
        <artifactId>holder-saas-covid-resource</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath> <!-- lookup parent from repository -->
    </parent>
    <artifactId>holder-saas-covid-integration</artifactId>
    <name>holder-saas-covid-integration</name>
    <description>covid 远程调用</description>

    <dependencies>
        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>holder-saas-covid-api</artifactId>
        </dependency>
    </dependencies>

</project>
