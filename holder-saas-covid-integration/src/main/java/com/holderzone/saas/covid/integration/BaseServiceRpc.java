package com.holderzone.saas.covid.integration;

import com.holderzone.saas.covid.api.dto.FileDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(name = "base-service", fallbackFactory = BaseServiceRpc.Fallback.class)
public interface BaseServiceRpc {

    @PostMapping("/file")
    String upload(@RequestBody FileDTO fileDTO);

    @DeleteMapping("/file")
    void delete(@RequestParam("fileUrl") String fileUrl);

    @Slf4j
    @Component
    class Fallback implements FallbackFactory<BaseServiceRpc> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常：";

        @Override
        public BaseServiceRpc create(Throwable cause) {
            return new BaseServiceRpc() {

                @Override
                public String upload(FileDTO fileDTO) {
                    log.error(HYSTRIX_PATTERN, "upload", fileDTO, cause);
                    throw new RuntimeException();
                }

                @Override
                public void delete(String fileUrl) {
                    log.error(HYSTRIX_PATTERN, "delete", "fileUrl=" + fileUrl, cause);
                    throw new RuntimeException();
                }
            };
        }
    }
}
