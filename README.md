# 员工服务（V2.0.0）

## Mq产品相关同步逻辑：
* 注：所有的mq消费逻辑暂时未考虑消费幂等处理，后期逻辑完善后考虑对需要的消费接口加上幂等
 
1、资源：
    备注：资源部分将会在q3被mdm替代，故资源相关信息的修改暂未完成
	MSG_TYPE_SOURCE_UPDATE 修改资源 sourceCode, sourceUrl 等基础信息 -> SourceUpdateDTO

2、模块：
	备注：模块部分将会在q3被mdm替代，故模块相关信息的修改暂未完成
	MSG_TYPE_MODULE_UPDATE 修改模块 moduleName 等基础信息 -> ModuleUpdateDTO

3、终端：
    备注：终端部分将会在q3被mdm替代，故终端信息的修改修改暂未完成
	MSG_TYPE_TERMINAL_UPDATE 修改终端 terminalName 等基础信息 -> TerminalUpdateDTO

4、产品：
    备注：产品部分将会在q3被mdm替代，故产品信息的修改赞未完成
	MSG_TYPE_PRODUCT_UPDATE 修改产品 productName 等基础信息 -> ProductUpdateDTO

5、授权产品给门店（或企业）：
    
    对企业或门店授权时会推送两条消息，一条是企业/门店与产品的关系、一条是产品的授权信息
    
    企业/门店与产品的关系与产品的基本信息（产品名、过期时间等）
    
    备注：这样两条消息的消费逻辑都会操作到hss_store_source表，是否会锁表导致消息消费超时？
    可能会导致死锁问题，可以考虑在上层进行串行处理（staff实例多部署情况下失效）或者设高事务隔离级别（可考虑在方法上设高事务隔离级别，该高隔离级别只对新增方法有效）？
    
	MSG_TYPE_PRODUCT_AUTH_ADD 新增(包括叠加) UnMessage -> ProductAuthDTO
	MSG_TYPE_PRODUCT_AUTH_UPDATE 修改(处理逻辑是先删除后插入，而后处理角色资源) -> ProductAuthDTO
	MSG_TYPE_PRODUCT_AUTH_TIME_UPDATE 修改授权产品的过期时间 -> ProductAuthUpdateDTO
	
	修改产品：
	入库逻辑：数据铺平为source层，根据productGuid删除hss_store_source表数据
	同时匹配新产品的source集合，匹配hss_role_source表中相同terminalGuid下的关联关系，在新产品中不存在且存在角色-资源关系的删除，其余保持不变
	
	新增产品（产品叠加）：
	入库逻辑：直接铺平数据并保存到hss_store_source表中
	
	删除产品：暂不考虑
	
	备注：产品授权时云端会同时推送全部菜单，需要对推送的菜单数据解析后入库

6、菜单：
    MSG_TYPE_MENU_ADD 菜单新增 -> MenuDTO 
    MSG_TYPE_MENU_UPDATE 菜单修改 -> MenuDTO
     
    新增菜单：
    入库逻辑：新增时直接铺平menu集合，并保存到hss_menu中
    修改菜单：
    入库逻辑：先删除，在执行新增菜单逻辑 
    
    备注：云端在对菜单的新增、修改、删除、修改下级url会推送当前操作的菜单（增量），对企业或门店授权时会推送当前企业或当前门店的所属企业的菜单，处理逻辑为先删除原来的所有菜单在添加进去
    云端产品授权逻辑：一旦对企业授权了，则不能在对企业下的门店授权；一旦对企业下的门店授权了，则不能对该企业授权，该企业只能使用门店授权的方式购买产品
    
备注：
    1、新增或修改产品授权时，根据影响的企业或门店产品信息，分别推送企业下或门店下的产品授权信息
    2、删除产品时，云端限制产品已分配到企业或门店无法删除
    
## 商户后台获取菜单、获取页面上资源（按钮）逻辑：

- 商户后台根据登陆人的guid，查询该userGuid下关联的角色列表，再由角色列表查询该终端下角色-资源关系，找到用户的资源信息后，根据资源倒推出菜单tree
- 获取页面上的按钮：根据点击的菜单，提交请求，请求中包括该菜单的guid，拿到菜单的guid后可获得资源信息（这里要加上操作权限过滤？）

## 各终端登陆流程及处理逻辑：

- 商户后台：V1.0对商户后台的登陆统一走云端的sso，登陆只校验了商户合法、用户合法，V2.0是否需要对登陆做前置校验？eg：商户或门店产品是否过期？商户后台登陆暂时不做处理，只校验商户与用户的合法性
- 其他终端：V1.0对一体机登陆做了后置校验（sso成功后），处理逻辑如下：  
        1、判断门店是否合法  
        2、判断用户是否属于该门店  
        3、判断设备门店的绑定关系是否合法（该设备已绑定门店或未绑定门店分别有对应的登陆页，主要区分为未绑定门店时需要输入门店编码进行登陆）,若未退出app但设备解绑服务器会返回一个约定好的编码以保证app上能弹出提示信息并跳转至未绑定的登陆页面，未绑定情况下登陆则建立设备门店绑定关系  
        4、判断该员工是否开班，若该员工在该门店下存在开班记录则沿用，否则开班  
        5、返回产品的过期时间等基础信息
             
## 员工模块：

1. 员工列表界面：

    选择组织为用户所属组织（所属组织包含企业、组织、门店 -> 参考OrganizationController#queryEnterpriseAndOrganization）

2. 新建员工界面：

    所属角色可选为：当前登陆员工所能分配的角色
    
    所在组织可选为：全部企业、组织、门店（单选）
    
    使用数据限制：
    
        通用部分：
                1. 选择门店：直接选择企业下所有门店
                2. 条件选择：组织、区域、品牌三者交集，每一类别都能多选，共同组成一个选择规则（交集），未来将支持多个规则，目前仅支持一个规则
                3. 以上两者取并集得到用户所能管理的门店，且条件选择中的配置为持续生效，eg：用户能管理组织A，创建新门店并属于组织A，那么用户也能管理该门店
                4. 条件选择数据存储方式：组织、区域选什么存什么，用时再查出该组织下的门店、该区域下的所有门店并对结果取交集
                5. 组织读取方式：
                    从DB中读取出组织DO列表()，从Organization服务中拉取组织树，
                6. 区域读取方式：用户数据限制的省市区，在数据库入库结果为name字段：省名称，市名称，区名称；code字段为当前级别的code，取出组装为上下级时不需再次请求base-service服务，通过java代码将数据拆分为扁平结构，510000必为省，510100必为市，510101必为区
                    判断逻辑：最后两位大于0必为区，否则后四位是否大于0必为市，否则必为省
                7. 品牌读取方式：从DB中读取出来品牌DO列表(包括品牌GUID和品牌名称)
                    
        特殊数据限制：
            1. 整单折扣：存于hss_user表中，鉴权时使用反射获取DTO中的折扣值进行验证
            2. 整单让价：处理逻辑同上
    
        管理帐号可分配角色：企业所有角色（可分配角色是可以大于用户所拥有角色）
                
    

3. 用户使用数据限制：

    配置的是用户的数据权限，无关用户的操作权限

4. 用户使用数据限制对“页面左上角能访问门店筛选”的影响：
 
    

## 角色模块：

1. 可选操作权限集：

    企业产品和企业下所有门店产品的资源并集（而非当前登录用户所能管理门店的资源并集，否则，操作、数据权限糅在一起了）
    
2. 角色操作权限配置：

    配置的是角色的操作权限（即用户的操作权限，无关用户的数据权限）

3. 角色权限配置对导航树的影响：

    通过用户的角色得到该用户能操作的资源，并根据导航树逆向推导出该用户的导航树
    
    也就是说用户导航树只与用户操作权限有关，与用户数据权限无关。

    用户数据权限
    
4.

## 组织模块：

1. 门店列表页面：

    组织、品牌、区域列表都为当前登陆用户的使用数据限制的部分
    
    用户关联的组织需要将扁平组织结构转换为上下级组织结构
    
    用户关联的区域也需要将扁平组织结构转换为上下级组织结构
    
## 商品库

    略
    
## 门店商品

1. 全部商品-门店下拉框：
    
    用户所能管理的门店铺平结构
    
    从hss_user_data_manage中查询门店, 组织, 区域, 品牌，以此为结果从organization服务中查询扁平的门店DTO列表
    
2. 全部属性-门店下拉框：
   
    处理逻辑同上


