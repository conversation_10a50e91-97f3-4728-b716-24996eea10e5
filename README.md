
## 预定服务技术方案
### 1. 组件
> 1. Mysql 
>> 排队队列的数据存储,队列特性的实现 
> 2. Redis
>> 基于redisson的分布式锁
> 3. <PERSON>urake
>> 服务治理
> 4. 其它依赖
>> spring-cloud,spring-boot,Mybatise-plus,redisson
### 2. 服务拓扑
> 1. ![结构](https://note.youdao.com/yws/api/personal/file/********************************?method=download&shareKey=11c3062f3944fa75be9e598306f59976)
### 3. 领域
> 1. 实体
>> 1. ReserveRecord 预定记录
>> 2. ReserveConfig 预定配置

> 2. 值对象
>> 1. CustomerVo 客户信息
>> 2. TimingSegmentVo 预定时段
>> 3. Table 预定桌台信息
>> 4. Item 预订菜品信息

> 3. 聚合
>> ReserveRecord
>>>
>>> CustomerVo
>>>
>>> Item
>>>
>>> Table
>>>
>>> ReserveConfig
### 4. 细节设计
> 1. 项目结构设计
>> 1. model
>>> api
>>>> 本服务提供的对外接口
>>> core
>>>> 核心的业务流程/api 的实现
>>> intergration
>>>> 引入的外部服务依赖
>> 2. package
>>> domain
>>>> 领域对象
>>>>
>>> dal(database access layer)
>>>> 数据库操作层, 包含 do(database object), mapper

> 2. 主体流程设计

```mermaid
graph TB
customer((用户))--发起预订-->一体机
customer((用户))--发起预订-->微信
微信--提交预定申请-->merchant{商户}
一体机--提交预定申请并接受预定-->reserve((预定流程))
merchant{商户}--审批通过-->reserve((预定流程))
merchant{商户}--审批不通过-->cancle[取消预订]
```
预定流程
```mermaid
graph TB
reserve((预定流程))--s-->A

```

> 2. 基于事件的编程模型
>> ![asd](https://note.youdao.com/yws/api/personal/file/76B07258A0964A26932BE381295C580B?method=download&shareKey=943f906da3e667f2b3e0aa60dccdc2aa)

> 2. 基于模版方法的事件流转(加锁解锁)模型
>> ![asd](https://note.youdao.com/yws/api/personal/file/CA6083273A4941E6A84C78FC5E62225B?method=download&shareKey=0dd4b7beb780e004a5963469db05940f)

> 3. controller --> service(rocket) --> 领域对象 --> 事件机 的处理流程

```
graph LR
controller --> service/rocket
service/rocket --> 领域对象
领域对象 --> 事件机
```
>> ![asd](https://note.youdao.com/yws/api/personal/file/5BB80727632D46ED80B1976A6AE987F7?method=download&shareKey=7d47ba3493020b3330b14b1aa87bf52b)

> 4. 锁设计
>> 1. 桌台时间分段锁
>>> 防止同一时间预定相同桌台

```lua
local segment = ARGV[1];  
local owner = ARGV[2];  
local failStrategy = tonumber(ARGV[3]);  
local fail={};  
for i,key in ipairs(KEYS) do  
  local segmentKey = key..':segment';  
  local modeKey = key..':mode';  
  local ownerKey = key..':owner';  
  local currentSegment = {};   
  string.gsub(segment,'[^-]+',function ( w )   
      table.insert(currentSegment,w)   
  end);  
  local modeExist = redis.call('EXISTS',modeKey);  
  local success = 1;  
  if(modeExist == 0) then   
    redis.call('set',ownerKey,1);  
    redis.call('set',modeKey,1);  
    success = 1;  
  else  
    local mode = redis.call('get',modeKey);  
    if(mode == 0) then   
      success = 0;  
    else  
      local data = redis.call('HGETALL',segmentKey);   
      for i, segment in ipairs(data) do   
        if(i % 2 == 1) then  
          local old = {}   
          string.gsub(segment,'[^-]+',function ( w )   
              table.insert(old,w)   
          end);   
          if(old[1]==currentSegment[1] and old[2]==currentSegment[2]) then    
            success = 0;   
            break;  
          end   
          if(old[1]<currentSegment[1] and old[2]>=currentSegment[1]) then    
            success = 0;   
            break;  
          end   
          if(old[1]<=currentSegment[2] and old[2]>currentSegment[2]) then    
            success = 0;   
            break;  
          end   
        end   
      end   
    end   
  end  
  if(success == 1) then   
    redis.call('hset',segmentKey,segment,owner);   
  else  
    table.insert(fail,key);  
    if(failStrategy == 0) then   
      break;  
    end   
  end  
end  
return fail;;
```
> 2. 预订记录锁
>> redisson
>>
>> 规避自动逾期,自动锁桌过程中,取消/修改操作可能导致的不可逆影响
>>
> 5. 预定状态设计
>> 预定状态
<p style="text-align:right;color:blue;">
<span>@See</span>
&nbsp;
com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum
</span>
</p>

状态|数值|二进制码
:---:|:---:|:---:|
提交|32|0b100000
取消|35|0b100011
系统自动取消|3|0b000011
通过|38|0b100110
到店开台|62|0b111110
到店选台|54|0b110110

>>> 码表

0|0|0|0|0|0
---|---|---|---|---|---
不是系统操作|是否到店|是否按预定开台|是否生效|是否被审核|是否取消

>> 客户端状态设计


<p style="text-align:right;color:blue;">
<span>@See</span>
&nbsp;
com.holderzone.saas.store.reserve.api.enums.ClientStateEnum
</span>
</p>

状态|数值|二进制码|映射
:---:|:---:|:---:|:---:
待审核|32|0b100000|==
取消|35|0b100011|==
预定中|38|0b100110|==
已逾期|3|0b000011|==
到店|54|0b110110|&

> 6. 延时任务设计
>> 1. rocket (可选)
>>> 处理流程
>>>> 每次pick合适的delaylevel丢给rocket
>>> examp:

还有5个小时的任务
1. 查找合适的delaylevel(2 小时) 丢给rocket
2. 2小时后(可能会有一定时间的延迟)收到message
3. message 未到执行时间 并且相差的时间大于阈值 goto 1
4. message 到执行时间 或相差的时间小于阈值
5. 本地schedule thread pool 执行操作
>> 2. zk delay queue(默认; 以下简称zk) 
>>> 处理流程
>>>> 设定处理时间,和处理信息的摘要 丢给 zk
>>>>
>>>> 等待zk执行处理程序
>>> zk 详细设计

offer/put
```
graph LR
task--执行时间-->queue
queue--顺序自动编号-->zk
```
take and process

```mermaid
graph TB
zk--task-->queue
sorted_queue--notify-->wait((queue wait))
queue--sort-->sorted_queue
sorted_queue--firstTask-->task{task 到期}
task{task 到期}--Y-->process((process))
task{task 到期}--N-->wait((queue wait))
wait((queue wait))--到期-->process((process))

```
> 7. 状态(操作)事件流转图
>> 1. 发起/审核
```mermaid
graph TB
微信-->发起
发起-->lanuch事件
发起-->审核通过
一体机--发起-->审核通过
一体机--审核-->审核通过
审核通过-->accept事件
accept事件-->table_prepare事件
accept事件-->delay_event'lock_table'事件
accept事件-->delay_event'be_delay'事件
accept事件-->sucess{是否发送成功短息}
sucess{是否发送成功短息}--Y-->message_event'success'
accept事件-->warn{是否发送到期提醒短息}
warn{是否发送到期提醒短息}--Y-->message_event'warn'
```
>> 2. 取消

```mermaid
graph TB
微信-->取消
一体机-->取消
取消-->cancle事件
cancle事件-->lock{是否已锁定桌台}
lock{是否已锁定桌台}--Y-->unlock_table事件
lock{是否已锁定桌台}--N-->cancle_prepare_table事件
```

>> 3. 开台

```mermaid
graph TB

开台-->effective事件
选台-->compensate事件
compensate事件-->success((success))
effective事件-->take_up{桌台是否占用}
take_up{桌台是否占用}--Y-->一体机
take_up{桌台是否占用}--Y-->微信
take_up{桌台是否占用}--N-->success((success))
一体机-->选台
微信-->选台
```
> 8. 服务依赖图


```mermaid
graph TB
预定-->base-service
预定-->organization
预定-->table
```

> 9. 表设计
>> 1. 预定配置表

字段|类型|描述
:---:|:---:|:---:
id| bigint|id
guid| varchar|业务主键
store_guid| varchar|门店guid
segments_str| text|时间段
lock_table_timing| decimal|锁桌台的时间
un_lock_table_timing| decimal|解锁桌台的时间
send_warn_message_timing| decimal|发送提示消息的时间
is_enable_success_message| bit|预定成功提醒
is_enable_warn_message| bit|预定到期提醒
is_enable| bit|启用标记
is_deleted| bit|删除标记
create_staff_guid| varchar|创建人
modified_staff_guid| varchar|修改人
gmt_create| datetime|创建时间
gmt_modified| datetime|修改时间

>> 2. 预定桌台关联表

字段|类型|描述
:---:|:---:|:---:
id| bigint|id
guid| varchar|guid
table_guid| varchar|桌台guid
table_name| varchar|桌台名称
area_guid| varchar|区域guid
area_name| varchar|区域名称
reserve_record_guid| varchar|预订guid
state| tinyint|状态
is_deleted| bit|删除标记
gmt_create| datetime|创建时间
gmt_modified| datetime|修改时间

>> 3. 预定技能表

字段|类型|描述
:---:|:---:|:---:
id| bigint|id
guid| varchar|guid
store_guid| varchar|门店guid
number| tinyint|人数
state| tinyint|状态
is_locked| bit|是否已锁定
phone| varchar|电话号码
gender| tinyint|1. 男, 0. 女
name| varchar|name
device_type| tinyint|设备类型
is_delay| bit|是否逾期
device_id| varchar|设备id
main_order_guid| varchar|主单号
payment_type| tinyint|支付方式
payment_type_name| varchar|支付方式名称
reserve_amount| decimal|预订金额
tag| varchar|tag
remark| varchar|备注
items_str| text|预点菜品
reserve_start_time| datetime|预订开始时间
reserves_end_time| datetime|预定结束时间
confirm_user_guid| varchar|审核人guid
confirm_user_name| varchar|审核人名称
confirm_time| datetime|确认时间
cancel_user_guid| varchar|取消人guid
cancel_user_name| varchar|取消人名称
cancle_time| datetime|取消时间
cancle_reason| varchar|取消原因
arrive_time| datetime|到店时间
arrive_user_guid| varchar|到店开台人guid
arrive_user_name| varchar|到店开台人名称
is_enable| bit|启用标记
is_deleted| bit|删除标记
create_staff_guid| varchar|创建人
create_staff_name| varchar|创建人名称
modified_staff_guid| varchar|修改人
modified_staff_name| varchar|修改人姓名
gmt_create| datetime|创建时间
gmt_modified| datetime|修改时间



