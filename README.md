# 排队服务

*排队服务一期项目暂时不做*

## 排队类型

### 接口定义

- /queueType/availableCode
- /queueType/create
- /queueType/enable
- /queueType/delete
- /queueType/update
- /queueType/query
- /queueType/queryAll

### 一些待确认的

如果做的天衣无缝，性能会由些许降低

过号：
排队中可以过号
已过号不得再次过号
已就餐不得过号

就餐：
排队中可以就餐
已就餐不得再次就餐
已过号可以就餐

修改人数：
排队中可以修改人数
已就餐不得修改人数
已过号不得修改人数

叫号：
排队中可以叫号
已就餐不得叫号
已过号不得叫号

撤销：
已就餐可以撤销
已过号可以撤销
已撤销不得再次撤销

## 排队记录

### 接口定义

- /queueRecord/create
- /queueRecord/call
- /queueRecord/skip
- /queueRecord/confirm
- /queueRecord/recover
- /queueRecord/guest
- /queueRecord/delete
- /queueRecord/queryAll
- /queueRecord/report

## 排队统计
