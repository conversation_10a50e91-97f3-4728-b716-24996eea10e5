### 组织相关（V2.0.0）
- 本项目中包含对组织相关（品牌、组织、门店）的相关CURD操作  
- 二期品牌、与组织/门店不存在上下级关系，门店有所属组织，所属品牌  
- 门店只能有一个所属组织（上级组织），但所属品牌二期中暂时只能有一个，后面可能为扩展为门店能关联多个品牌

#### 相关表设计
- 组织表的上下级关系用parentIds字段来标识，代替一期中使用parentId的方式来标识上下级，在V2.0.0中每一个组织的parentIds字段都是它的最上级到直属上级的id字符串，用逗号分隔
- 门店设计为组织的最下级，同时门店与品牌的关联关系用中间表进行关联

#### 其他相关

- 云端后台可以创建门店并通过mq同步过来，考虑下所有mq同步的接口在失败重试的情况下是否需要对接口做幂等处理
- 门店的创建、编辑操作需要同步到云端，品牌、组织的CURD操作这一期暂时不同步

#### 相关同步场景
- 商户后台同步云端（门店curd操作）、云端同步到商户后台（门店curd操作）都通过mq
- 商户后台二期第一个迭代中，商户后台创建的门店不能进行授权，只能云端授权都同步过来，考虑在以后的迭代商户后台增加门店授权逻辑  

#### 接口相关  
- BrandController - 品牌相关操作接口
- OrganizationController - 组织相关操作接口
- StoreController - 门店相关操作接口

#### 待完成
- 根据用户查询该用户关联的品牌、关联的组织、关联的门店
- 是否应该提供一个根据查询用户关联的门店列表接口，先找到用户与品牌、用户与组织的关联关系，然后通过关联关系找到相关的门店，再与用户直接关联的门店取去重的并集？

#### 使用技术
- [持久层框架插件 Mybatis-plus](https://mp.baomidou.com/)
- [消息队列 RocketMq](http://rocketmq.apache.org/)
- [业务对象转换框架 MapStruct](http://mapstruct.org/)
