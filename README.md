**系统功能**

        动态路由（需要http的basic认证）
        动态添加filter的白名单（需要http的basic认证）
        权限验证(由filter包下的全局filter类完成)
        
**注意事项**

        由于header不能存中文，所以使用了URLEncode.encode()对userInfo的header进行编码，获取userInfo的header时需要URLDecode.decode()解码        

**网关添加路由配置的方法**

- GatewayControllerEndpoint类中开放了添加路由，删除路由，查询路由，刷新路由等这些接口
        
- 添加路由：

            http-post: host:port/actuator/gateway/routes/{id}
            例子：
                localhost:8562/actuator/gateway/routes/aa
                
                {
                	"uri":"http://localhost:10002",
                	"predicates":[
                		{
                			"name":"Path",
                			"args":{
                				"pattern":"/test2/**"
                			}
                		}	
                	]
                }
                
- 刷新路由:

           http-post: host:port/actuator/gateway/refresh
           
- 查询所有路由:

           http-get: host:port/actuator/gateway/routes

- 其他接口查看GatewayControllerEndpoint类           

                               