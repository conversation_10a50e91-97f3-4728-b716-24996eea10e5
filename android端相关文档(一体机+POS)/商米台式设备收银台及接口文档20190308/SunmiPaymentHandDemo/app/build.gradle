apply plugin: 'com.android.application'
apply plugin: 'me.tatarka.retrolambda'

android {
    compileSdkVersion 28
    buildToolsVersion '28.0.3'
    defaultConfig {
        applicationId "com.sunmi.paymentdemo"
        minSdkVersion 21
        targetSdkVersion 26
        versionCode 9
        versionName "1.9"
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        abortOnError false
    }
    // 配置输出apk的名称
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            output.outputFile = new File(output.outputFile.parent, "SunmiPaymentDemo_${defaultConfig.versionName}_${buildType.name}.apk")
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
}

dependencies {
    compile fileTree(dir: 'libs', include: ['*.jar'])
    compile 'com.android.support:appcompat-v7:26.1.0'
    compile 'com.android.support.constraint:constraint-layout:1.1.3'
    testCompile 'junit:junit:4.12'
    // https://mvnrepository.com/artifact/com.qmuiteam/qmui
    compile group: 'com.qmuiteam', name: 'qmui', version: '1.1.5'
    // https://mvnrepository.com/artifact/com.google.code.gson/gson
    compile group: 'com.google.code.gson', name: 'gson', version: '2.7'
}
