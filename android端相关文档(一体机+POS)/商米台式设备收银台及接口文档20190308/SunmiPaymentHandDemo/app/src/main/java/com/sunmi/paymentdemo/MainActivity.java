package com.sunmi.paymentdemo;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.sunmi.paymentdemo.activity.ConsumeActivity;
import com.sunmi.paymentdemo.activity.PreauthActivity;
import com.sunmi.paymentdemo.activity.PreauthCompleteActivity;
import com.sunmi.paymentdemo.activity.PrintActivity;
import com.sunmi.paymentdemo.activity.QueryActivity;
import com.sunmi.paymentdemo.activity.RefundActivity;
import com.sunmi.paymentdemo.activity.SettlementActivity;
import com.sunmi.paymentdemo.activity.SummaryQueryActivity;


public class MainActivity extends Activity implements View.OnClickListener {
    private QMUIRoundButton consume, refund, preauth, preauth_complete, settlement, print, query, summary_query;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        QMUITopBarLayout topbar = findViewById(R.id.topbar);
        topbar.setTitle(R.string.home_string_title);
        consume = findViewById(R.id.consume);
        refund = findViewById(R.id.refund);
        preauth = findViewById(R.id.preauth);
        preauth_complete = findViewById(R.id.preauth_complete);
        settlement = findViewById(R.id.settlement);
        print = findViewById(R.id.print);
        query = findViewById(R.id.query);
        summary_query = findViewById(R.id.summary_query);

        consume.setOnClickListener(this);
        refund.setOnClickListener(this);
        preauth.setOnClickListener(this);
        preauth_complete.setOnClickListener(this);
        settlement.setOnClickListener(this);
        print.setOnClickListener(this);
        query.setOnClickListener(this);
        summary_query.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.consume:
                startActivity(new Intent(MainActivity.this, ConsumeActivity.class));
                break;
            case R.id.refund:
                startActivity(new Intent(MainActivity.this, RefundActivity.class));
                break;
            case R.id.preauth:
                startActivity(new Intent(MainActivity.this, PreauthActivity.class));
                break;
            case R.id.preauth_complete:
                startActivity(new Intent(MainActivity.this, PreauthCompleteActivity.class));
                break;
            case R.id.settlement:
                startActivity(new Intent(MainActivity.this, SettlementActivity.class));
                break;
            case R.id.print:
                startActivity(new Intent(MainActivity.this, PrintActivity.class));
                break;
            case R.id.query:
                startActivity(new Intent(MainActivity.this, QueryActivity.class));
                break;
            case R.id.summary_query:
                startActivity(new Intent(MainActivity.this, SummaryQueryActivity.class));
                break;
        }
    }
}
