package com.sunmi.paymentdemo.view;

import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sunmi.paymentdemo.R;


public class CustomTextView extends LinearLayout {
    private TextView textView_title, textView_content, textView_indicator;

    public CustomTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public CustomTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.custom_textview, this, true);
        textView_title = findViewById(R.id.textview_title);
        textView_content = findViewById(R.id.textview_content);
        textView_indicator = findViewById(R.id.textview_indicator);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
    }

    public void setText(int title, String content, int indicator) {
        textView_title.setText(title);
        textView_content.setText(content);
        textView_indicator.setText(indicator);
    }

    public String getContent() {
        return textView_content.getText().toString();
    }
}
