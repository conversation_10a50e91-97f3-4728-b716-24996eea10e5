package com.sunmi.paymentdemo.activity;

import android.app.Activity;
import android.content.IntentFilter;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;

import com.google.gson.Gson;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.qmuiteam.qmui.widget.textview.QMUILinkTextView;
import com.sunmi.payment.PaymentService;

import com.sunmi.paymentdemo.R;
import com.sunmi.paymentdemo.bean.Request;
import com.sunmi.paymentdemo.dialog.WaitingDialog;
import com.sunmi.paymentdemo.receiver.ResultReceiver;
import com.sunmi.paymentdemo.view.CustomEditText;
import com.sunmi.paymentdemo.view.CustomTextView;

/**
 * 发起订单打印请求
 */
public class PrintActivity extends Activity implements View.OnClickListener {
    private CustomEditText print_misId, print_orderId;
    private CustomTextView print_transType, print_appId;
    private QMUIRoundButton bt_print_start;
    private QMUILinkTextView tv_print_request, tv_print_result;
    private ResultReceiver resultReceiver;
    private QMUITopBarLayout print_topbar;
    private WaitingDialog waitingDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_print);
        initView();
        registerResultReceiver();
    }

    private void registerResultReceiver() {
        resultReceiver = new ResultReceiver(result -> {
            if (waitingDialog != null && waitingDialog.isShowing()) {
                waitingDialog.dismiss();
            }
            tv_print_result.setText(result);
        });
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ResultReceiver.RESPONSE_ACTION);
        registerReceiver(resultReceiver, intentFilter);
    }

    private void initView() {
        print_topbar = findViewById(R.id.print_topbar);
        print_topbar.setTitle(R.string.home_string_print);
        print_topbar.addLeftBackImageButton().setOnClickListener(v -> finish());

        print_appId = findViewById(R.id.print_appId);
        print_transType = findViewById(R.id.print_transType);
        print_misId = findViewById(R.id.print_misId);
        print_orderId = findViewById(R.id.print_orderId);
        tv_print_request = findViewById(R.id.tv_print_request);

        print_appId.setText(R.string.title_string_appId, "", R.string.indicator_must);
        print_transType.setText(R.string.title_string_transType, "A1", R.string.indicator_must);
        print_misId.setText(R.string.title_string_misId, R.string.indicator_special);
        print_orderId.setText(R.string.title_string_orderId, R.string.indicator_special);

        bt_print_start = findViewById(R.id.bt_print_start);
        bt_print_start.setOnClickListener(this);

        tv_print_result = findViewById(R.id.tv_print_result);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            /**
             * 开始打印请求
             */
            case R.id.bt_print_start:
                tv_print_result.setText("");
                waitingDialog = new WaitingDialog(this);
                waitingDialog.show();
                /**
                 * 请求数据，最终转为json字符串发送到收银台
                 */
                Request request = new Request();
                // 应用包名
                request.appId = getPackageName();
                // 交易类型
                request.transType = print_transType.getContent();
                // Saas软件订单号
                request.orderId = print_orderId.getContent();
                // 收银台流水号
                request.misId = print_misId.getContent();

                Gson gson = new Gson();
                String jsonStr = gson.toJson(request);
                PaymentService.getInstance().callPayment(jsonStr);
                tv_print_request.setText(getString(R.string.message_string_request) + jsonStr);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (resultReceiver != null) {
            unregisterReceiver(resultReceiver);
        }
    }
}
