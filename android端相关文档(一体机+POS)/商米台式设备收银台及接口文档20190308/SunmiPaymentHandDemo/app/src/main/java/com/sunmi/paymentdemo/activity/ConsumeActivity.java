package com.sunmi.paymentdemo.activity;

import android.app.Activity;
import android.content.IntentFilter;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;

import com.google.gson.Gson;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.qmuiteam.qmui.widget.textview.QMUILinkTextView;
import com.sunmi.payment.PaymentService;


import com.sunmi.paymentdemo.R;
import com.sunmi.paymentdemo.bean.Config;
import com.sunmi.paymentdemo.bean.Request;
import com.sunmi.paymentdemo.dialog.WaitingDialog;
import com.sunmi.paymentdemo.receiver.ResultReceiver;
import com.sunmi.paymentdemo.view.CustomEditText;
import com.sunmi.paymentdemo.view.CustomRadioButton;
import com.sunmi.paymentdemo.view.CustomTextView;


/**
 * 发起L3消费交易
 */
public class ConsumeActivity extends Activity implements View.OnClickListener {
    private CustomEditText consume_appType, consume_amount, consume_orderId, consume_orderInfo, consume_payCode, consume_printIdType, consume_remarks;
    private CustomTextView consume_transType, consume_appId;
    private CustomRadioButton consume_resultDisplay, consume_processDisplay, consume_printTicket;
    private QMUIRoundButton bt_consume_start, bt_consume_new_orderId;
    private QMUILinkTextView tv_consume_request, tv_consume_result, consume_warning;
    private ResultReceiver resultReceiver;
    private QMUITopBarLayout consume_topbar;
    private WaitingDialog waitingDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_consume);
        initView();
        registerResultReceiver();
    }

    private void registerResultReceiver() {
        resultReceiver = new ResultReceiver(result -> {
            if (waitingDialog != null && waitingDialog.isShowing()) {
                waitingDialog.dismiss();
            }
            tv_consume_result.setText(result);
        });
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ResultReceiver.RESPONSE_ACTION);
        registerReceiver(resultReceiver, intentFilter);
    }

    private void initView() {
        consume_topbar = findViewById(R.id.consume_topbar);
        consume_topbar.setTitle(R.string.home_string_consume);
        consume_topbar.addLeftBackImageButton().setOnClickListener(v -> finish());

        consume_appType = findViewById(R.id.consume_appType);
        consume_appId = findViewById(R.id.consume_appId);
        consume_transType = findViewById(R.id.consume_transType);
        consume_amount = findViewById(R.id.consume_amount);
        consume_orderId = findViewById(R.id.consume_orderId);
        consume_orderInfo = findViewById(R.id.consume_orderInfo);
        consume_payCode = findViewById(R.id.consume_payCode);
        tv_consume_request = findViewById(R.id.tv_consume_request);
        consume_processDisplay = findViewById(R.id.consume_processDisplay);
        consume_resultDisplay = findViewById(R.id.consume_resultDisplay);
        consume_warning = findViewById(R.id.consume_warning);
        consume_printTicket = findViewById(R.id.consume_printTicket);
        consume_printIdType = findViewById(R.id.consume_printIdType);
        consume_remarks = findViewById(R.id.consume_remarks);

        consume_appType.setText(R.string.title_string_appType, R.string.indicator_must);
        consume_appId.setText(R.string.title_string_appId, "", R.string.indicator_must);
        consume_transType.setText(R.string.title_string_transType, "00", R.string.indicator_must);
        consume_amount.setText(R.string.title_string_amount, R.string.indicator_must);
        consume_orderId.setText(R.string.title_string_orderId, R.string.indicator_optional);
        consume_orderInfo.setText(R.string.title_string_orderInfo, R.string.indicator_optional);
        consume_payCode.setText(R.string.title_string_payCode, R.string.indicator_optional);

        consume_printTicket.setText(R.string.title_string_printTicket, R.string.indicator_optional);
        consume_printTicket.setTitle(R.string.title_string_print_ticket, R.string.title_string_unprint_ticket);

        consume_processDisplay.setTitle(R.string.title_string_process_display, R.string.title_string_process_undisplay);
        consume_processDisplay.setText(R.string.title_string_processDisplay, R.string.indicator_optional);

        consume_resultDisplay.setTitle(R.string.title_string_result_display, R.string.title_string_result_undisplay);
        consume_resultDisplay.setText(R.string.title_string_resultDisplay, R.string.indicator_optional);

        consume_printIdType.setText(R.string.title_string_printIdType, R.string.indicator_optional);

        consume_remarks.setText(R.string.title_string_remarks, R.string.indicator_optional);

        consume_warning.setText(R.string.string_warning);

        bt_consume_start = findViewById(R.id.bt_consume_start);
        bt_consume_start.setOnClickListener(this);
        bt_consume_new_orderId = findViewById(R.id.bt_consume_new_orderId);
        bt_consume_new_orderId.setOnClickListener(this);

        tv_consume_result = findViewById(R.id.tv_consume_result);

        consume_processDisplay.setChecked(true);
        consume_resultDisplay.setChecked(true);
        consume_printTicket.setChecked(true);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            // 自动生一个随机的Saas订单号，并填入
            case R.id.bt_consume_new_orderId:
                String new_order_id = System.currentTimeMillis() / 1000 + "" + (int) (Math.random() * 9000 + 1000);
                consume_orderId.setContent(new_order_id);
                break;
            /**
             * 开始消费交易
             */
            case R.id.bt_consume_start:
                tv_consume_result.setText(R.string.message_string_processing);
                waitingDialog = new WaitingDialog(this);
                waitingDialog.show();
                /**
                 * 请求数据，最终转为json字符串发生到收银台
                 */
                Request request = new Request();
                // 应用类型
                request.appType = consume_appType.getContent();
                // 应用包名
                request.appId = getPackageName();
                // 交易类型
                request.transType = consume_transType.getContent();
                // 交易金额
                Long amount = 0L;
                try {
                    amount = Long.parseLong(consume_amount.getContent());
                } catch (Exception e) {
                }
                request.amount = amount;
                // Saas软件订单号
                request.orderId = consume_orderId.getContent();
                // 商品信息
                request.orderInfo = consume_orderInfo.getContent();
                // 支付码
                request.payCode = consume_payCode.getContent();


                Config config = new Config();
                // 交易过程中是否显示UI界面
                config.processDisplay = consume_processDisplay.getContent();
                // 是否展示交易结果页
                config.resultDisplay = consume_resultDisplay.getContent();
                // 是否打印小票
                config.printTicket = consume_printTicket.getContent();
                // 指定签购单上的退款订单号类型
                config.printIdType = consume_printIdType.getContent();
                // 备注
                config.remarks = consume_remarks.getContent();
                request.config = config;

                Gson gson = new Gson();
                String jsonStr = gson.toJson(request);
                PaymentService.getInstance().callPayment(jsonStr);
                tv_consume_request.setText(getString(R.string.message_string_request) + jsonStr);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (resultReceiver != null) {
            unregisterReceiver(resultReceiver);
        }
    }
}
