package com.sunmi.paymentdemo.activity;

import android.app.Activity;
import android.content.IntentFilter;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;

import com.google.gson.Gson;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.qmuiteam.qmui.widget.textview.QMUILinkTextView;
import com.sunmi.payment.PaymentService;

import com.sunmi.paymentdemo.R;
import com.sunmi.paymentdemo.bean.Config;
import com.sunmi.paymentdemo.bean.Request;
import com.sunmi.paymentdemo.dialog.WaitingDialog;
import com.sunmi.paymentdemo.receiver.ResultReceiver;
import com.sunmi.paymentdemo.view.CustomEditText;
import com.sunmi.paymentdemo.view.CustomRadioButton;
import com.sunmi.paymentdemo.view.CustomTextView;

/**
 * 发起L3退款请求
 */
public class RefundActivity extends Activity implements View.OnClickListener {
    private CustomTextView refund_transType, refund_appId;
    private CustomEditText refund_appType, refund_amount, refund_orderId, refund_oriMisId, refund_oriOrderId, refund_printIdType, refund_remarks, refund_oriPlatformId;
    private CustomRadioButton refund_resultDisplay, refund_processDisplay, refund_printTicket;
    private QMUIRoundButton bt_refund_new_orderId, bt_refund_start;
    private QMUILinkTextView tv_refund_request, tv_refund_result, refund_warning;
    private ResultReceiver resultReceiver;
    private QMUITopBarLayout refund_topbar;
    private WaitingDialog waitingDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_refund);
        initView();
        registerResultReceiver();
    }

    private void registerResultReceiver() {
        resultReceiver = new ResultReceiver(result -> {
            if (waitingDialog != null && waitingDialog.isShowing()) {
                waitingDialog.dismiss();
            }
            tv_refund_result.setText(result);
        });
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ResultReceiver.RESPONSE_ACTION);
        registerReceiver(resultReceiver, intentFilter);
    }

    private void initView() {
        refund_topbar = findViewById(R.id.refund_topbar);
        refund_topbar.setTitle(R.string.home_string_refund);
        refund_topbar.addLeftBackImageButton().setOnClickListener(v -> finish());

        refund_appType = findViewById(R.id.refund_appType);
        refund_appId = findViewById(R.id.refund_appId);
        refund_transType = findViewById(R.id.refund_transType);
        refund_amount = findViewById(R.id.refund_amount);
        refund_orderId = findViewById(R.id.refund_orderId);
        refund_oriMisId = findViewById(R.id.refund_oriMisId);
        refund_oriOrderId = findViewById(R.id.refund_oriOrderId);
        refund_oriPlatformId = findViewById(R.id.refund_oriPlatformId);

        refund_processDisplay = findViewById(R.id.refund_processDisplay);
        refund_resultDisplay = findViewById(R.id.refund_resultDisplay);
        refund_printTicket = findViewById(R.id.refund_printTicket);
        refund_printIdType = findViewById(R.id.refund_printIdType);
        refund_remarks = findViewById(R.id.refund_remarks);

        refund_warning = findViewById(R.id.refund_warning);
        tv_refund_request = findViewById(R.id.tv_refund_request);

        refund_appType.setText(R.string.title_string_appType, R.string.indicator_optional);
        refund_appId.setText(R.string.title_string_appId, "", R.string.indicator_must);
        refund_transType.setText(R.string.title_string_transType, "09", R.string.indicator_must);
        refund_amount.setText(R.string.title_string_amount, R.string.indicator_optional);
        refund_orderId.setText(R.string.title_string_orderId, R.string.indicator_optional);
        refund_oriMisId.setText(R.string.title_string_oriMisId, R.string.indicator_special);
        refund_oriOrderId.setText(R.string.title_string_oriOrderId, R.string.indicator_special);
        refund_oriPlatformId.setText(R.string.title_string_oriPlatformId, R.string.indicator_special);

        refund_printTicket.setText(R.string.title_string_printTicket, R.string.indicator_optional);
        refund_printTicket.setTitle(R.string.title_string_print_ticket, R.string.title_string_unprint_ticket);

        refund_processDisplay.setTitle(R.string.title_string_process_display, R.string.title_string_process_undisplay);
        refund_processDisplay.setText(R.string.title_string_processDisplay, R.string.indicator_optional);

        refund_resultDisplay.setTitle(R.string.title_string_result_display, R.string.title_string_result_undisplay);
        refund_resultDisplay.setText(R.string.title_string_resultDisplay, R.string.indicator_optional);

        refund_printIdType.setText(R.string.title_string_printIdType, R.string.indicator_optional);

        refund_remarks.setText(R.string.title_string_remarks, R.string.indicator_optional);

        refund_warning.setText(R.string.string_warning);


        bt_refund_new_orderId = findViewById(R.id.bt_refund_new_orderId);
        bt_refund_start = findViewById(R.id.bt_refund_start);

        bt_refund_new_orderId.setOnClickListener(this);
        bt_refund_start.setOnClickListener(this);

        tv_refund_result = findViewById(R.id.tv_refund_result);

        refund_resultDisplay.setChecked(true);
        refund_processDisplay.setChecked(true);
        refund_printTicket.setChecked(true);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.bt_refund_new_orderId:
                String new_order_id = System.currentTimeMillis() / 1000 + "" + (int) (Math.random() * 9000 + 1000);
                refund_orderId.setContent(new_order_id);
                break;
            /**
             * 开始退款请求
             */
            case R.id.bt_refund_start:
                tv_refund_result.setText(R.string.message_string_processing);
                waitingDialog = new WaitingDialog(this);
                waitingDialog.show();
                /**
                 * 请求数据，最终转为json字符串发送到收银台
                 */
                Request request = new Request();
                // 应用类型
                request.appType = refund_appType.getContent();
                // 应用包名
                request.appId = getPackageName();
                //交易类型
                request.transType = refund_transType.getContent();
                // 交易金额
                Long amount = 0l;
                try {
                    amount = Long.parseLong(refund_amount.getContent());
                } catch (Exception e) {

                }
                request.amount = amount;
                // Saas软件订单号
                request.orderId = refund_orderId.getContent();
                // 原收银台流水号
                request.oriMisId = refund_oriMisId.getContent();
                // 原Saas软件订单号
                request.oriOrderId = refund_oriOrderId.getContent();
                // 原Saas软件订单号
                request.oriPlatformId = refund_oriPlatformId.getContent();

                Config config = new Config();
                // 交易过程中是否显示UI界面
                config.processDisplay = refund_processDisplay.getContent();
                // 是否展示交易结果页
                config.resultDisplay = refund_resultDisplay.getContent();
                // 是否打印小票
                config.printTicket = refund_printTicket.getContent();
                // 指定签购单上的退款订单号类型
                config.printIdType = refund_printIdType.getContent();
                // 备注
                config.remarks = refund_remarks.getContent();
                request.config = config;

                Gson gson = new Gson();
                String jsonStr = gson.toJson(request);
                PaymentService.getInstance().callPayment(jsonStr);
                tv_refund_request.setText(getString(R.string.message_string_request) + jsonStr);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (resultReceiver != null) {
            unregisterReceiver(resultReceiver);
        }
    }
}
