package com.sunmi.paymentdemo.activity;

import android.app.Activity;
import android.content.IntentFilter;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;

import com.google.gson.Gson;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.qmuiteam.qmui.widget.textview.QMUILinkTextView;
import com.sunmi.payment.PaymentService;
import com.sunmi.paymentdemo.R;

import com.sunmi.paymentdemo.bean.Request;
import com.sunmi.paymentdemo.dialog.WaitingDialog;
import com.sunmi.paymentdemo.receiver.ResultReceiver;
import com.sunmi.paymentdemo.view.CustomEditText;
import com.sunmi.paymentdemo.view.CustomTextView;

/**
 * 发起L3批量查询交易记录
 */
public class SummaryQueryActivity extends Activity implements View.OnClickListener {
    private CustomEditText summary_query_orderType, summary_query_date, summary_query_page, summary_query_pageSize;
    private CustomTextView summary_query_transType, summary_query_appId;
    private QMUIRoundButton bt_summary_query_start;
    private QMUILinkTextView tv_summary_query_request, tv_summary_query_result;
    private ResultReceiver resultReceiver;
    private QMUITopBarLayout summary_query_topbar;
    private WaitingDialog waitingDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_summary_query);
        initView();
        registerResultReceiver();
    }

    private void registerResultReceiver() {
        resultReceiver = new ResultReceiver(result -> {
            if (waitingDialog != null && waitingDialog.isShowing()) {
                waitingDialog.dismiss();
            }
            tv_summary_query_result.setText(result);
        });
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ResultReceiver.RESPONSE_ACTION);
        registerReceiver(resultReceiver, intentFilter);
    }

    private void initView() {
        summary_query_topbar = findViewById(R.id.summary_query_topbar);
        summary_query_topbar.setTitle(R.string.home_string_summary_query);
        summary_query_topbar.addLeftBackImageButton().setOnClickListener(v -> finish());
        summary_query_appId = findViewById(R.id.summary_query_appId);
        summary_query_transType = findViewById(R.id.summary_query_transType);
        summary_query_orderType = findViewById(R.id.summary_query_orderType);
        summary_query_date = findViewById(R.id.summary_query_date);
        summary_query_page = findViewById(R.id.summary_query_page);
        summary_query_pageSize = findViewById(R.id.summary_query_pageSize);
        tv_summary_query_request = findViewById(R.id.tv_summary_query_request);


        summary_query_appId.setText(R.string.title_string_appId, "", R.string.indicator_must);
        summary_query_transType.setText(R.string.title_string_transType, "A3", R.string.indicator_must);
        summary_query_orderType.setText(R.string.title_string_orderType, R.string.indicator_must);
        summary_query_date.setText(R.string.title_string_date, R.string.indicator_must);
        summary_query_page.setText(R.string.title_string_page, R.string.indicator_must);
        summary_query_pageSize.setText(R.string.title_string_pageSize, R.string.indicator_must);

        bt_summary_query_start = findViewById(R.id.bt_summary_query_start);
        bt_summary_query_start.setOnClickListener(this);

        tv_summary_query_result = findViewById(R.id.tv_summary_query_result);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.bt_summary_query_start:
                tv_summary_query_result.setText("");
                waitingDialog = new WaitingDialog(this);
                waitingDialog.show();
                /**
                 * 请求数据，最终转为json字符串发送到收银台
                 */
                Request request = new Request();
                // 应用包名
                request.appId = getPackageName();
                // 交易类型
                request.transType = summary_query_transType.getContent();
                // 订单类型
                request.orderType = summary_query_orderType.getContent();
                // 交易日期
                request.date = summary_query_date.getContent();
                // 分页页码
                Integer page = 0;
                try {
                    page = Integer.parseInt(summary_query_page.getContent());
                } catch (Exception e) {
                }
                request.page = page;
                // 每页返回数量
                Integer pageSize = 0;
                try {
                    pageSize = Integer.parseInt(summary_query_pageSize.getContent());
                } catch (Exception e) {
                }
                request.pageSize = pageSize;

                Gson gson = new Gson();
                String jsonStr = gson.toJson(request);
                PaymentService.getInstance().callPayment(jsonStr);
                tv_summary_query_request.setText(getString(R.string.message_string_request) + jsonStr);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (resultReceiver != null) {
            unregisterReceiver(resultReceiver);
        }
    }
}
