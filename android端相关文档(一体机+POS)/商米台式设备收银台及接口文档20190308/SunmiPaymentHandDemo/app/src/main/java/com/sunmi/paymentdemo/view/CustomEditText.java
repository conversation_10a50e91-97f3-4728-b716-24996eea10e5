package com.sunmi.paymentdemo.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sunmi.paymentdemo.R;


public class CustomEditText extends LinearLayout {
    private static final String TAG = "CustomEditText";
    private TextView edittext_title;
    private EditText edittext_content;
    private TextView edittext_indicator;

    public CustomEditText(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public CustomEditText(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        LayoutInflater.from(context).inflate(R.layout.custom_edittext, this, true);
        edittext_title = findViewById(R.id.edittext_title);
        edittext_content = findViewById(R.id.edittext_content);
        edittext_indicator = findViewById(R.id.edittext_indicator);
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.CustomEditText);
        int inputType = ta.getInt(R.styleable.CustomEditText_inputType, 1);
        edittext_content.setInputType(inputType);
    }

    public void setText(int titleResId, int indicatorResId) {
        edittext_title.setText(titleResId);
        edittext_indicator.setText(indicatorResId);
    }

    public String getContent() {
        return edittext_content.getText().toString();
    }

    public void setContent(String content) {
        edittext_content.setText(content);
    }
}
