package com.sunmi.paymentdemo.activity;

import android.app.Activity;
import android.content.IntentFilter;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;

import com.google.gson.Gson;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.qmuiteam.qmui.widget.textview.QMUILinkTextView;
import com.sunmi.payment.PaymentService;

import com.sunmi.paymentdemo.R;
import com.sunmi.paymentdemo.bean.Config;
import com.sunmi.paymentdemo.bean.Request;
import com.sunmi.paymentdemo.dialog.WaitingDialog;
import com.sunmi.paymentdemo.receiver.ResultReceiver;
import com.sunmi.paymentdemo.view.CustomEditText;
import com.sunmi.paymentdemo.view.CustomRadioButton;
import com.sunmi.paymentdemo.view.CustomTextView;

/**
 * 暂时不支持预授权
 */
public class PreauthActivity extends Activity implements View.OnClickListener {
    private CustomEditText preauth_appType, preauth_amount, preauth_orderId;
    private CustomTextView preauth_appId, preauth_transType;
    private CustomRadioButton preauth_printTicket;
    private ResultReceiver resultReceiver;
    private QMUILinkTextView tv_preauth_request, tv_preauth_result;
    private QMUIRoundButton bt_preauth_start, bt_preauth_new_orderId;
    private QMUITopBarLayout preauth_topbar;
    private WaitingDialog waitingDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_preauth);
        initView();
        registerResultReceiver();
    }

    private void registerResultReceiver() {
        resultReceiver = new ResultReceiver(result -> {
            if (waitingDialog != null && waitingDialog.isShowing()) {
                waitingDialog.dismiss();
            }
            tv_preauth_result.setText(result);
        });
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ResultReceiver.RESPONSE_ACTION);
        registerReceiver(resultReceiver, intentFilter);
    }

    private void initView() {
        preauth_topbar = findViewById(R.id.preauth_topbar);
        preauth_topbar.setTitle(R.string.home_string_preauth);
        preauth_topbar.addLeftBackImageButton().setOnClickListener(v -> finish());

        preauth_appType = findViewById(R.id.preauth_appType);
        preauth_appId = findViewById(R.id.preauth_appId);
        preauth_transType = findViewById(R.id.preauth_transType);
        preauth_amount = findViewById(R.id.preauth_amount);
        preauth_orderId = findViewById(R.id.preauth_orderId);
        preauth_printTicket = findViewById(R.id.preauth_printTicket);
        tv_preauth_request = findViewById(R.id.tv_preauth_request);
        tv_preauth_result = findViewById(R.id.tv_preauth_result);

        preauth_appType.setText(R.string.title_string_appType, R.string.indicator_must);
        preauth_appId.setText(R.string.title_string_appId, "", R.string.indicator_must);
        preauth_transType.setText(R.string.title_string_transType, "03", R.string.indicator_must);
        preauth_amount.setText(R.string.title_string_amount, R.string.indicator_must);
        preauth_orderId.setText(R.string.title_string_orderId, R.string.indicator_optional);
        preauth_printTicket.setText(R.string.title_string_printTicket, R.string.indicator_optional);
        preauth_printTicket.setTitle(R.string.title_string_print_ticket, R.string.title_string_unprint_ticket);


        bt_preauth_start = findViewById(R.id.bt_preauth_start);
        bt_preauth_start.setOnClickListener(this);
        bt_preauth_new_orderId = findViewById(R.id.bt_preauth_new_orderId);
        bt_preauth_new_orderId.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            // 自动生一个随机的Saas订单号，并填入
            case R.id.bt_preauth_new_orderId:
                String new_order_id = System.currentTimeMillis() / 1000 + "" + (int) (Math.random() * 9000 + 1000);
                preauth_orderId.setContent(new_order_id);
                break;
            /**
             * 开始消费交易
             */
            case R.id.bt_preauth_start:
                tv_preauth_result.setText("");
                waitingDialog = new WaitingDialog(this);
                waitingDialog.show();
                /**
                 * 请求数据，最终转为json字符串发生到收银台
                 */
                Request request = new Request();
                // 应用类型
                request.appType = preauth_appType.getContent();
                // 应用包名
                request.appId = getPackageName();
                // 交易类型
                request.transType = preauth_transType.getContent();
                // 交易金额
                Long amount = 0L;
                try {
                    amount = Long.parseLong(preauth_amount.getContent());
                } catch (Exception e) {
                }
                request.amount = amount;
                // Saas软件订单号
                request.orderId = preauth_orderId.getContent();
                Config config = new Config();
                // 是否打印小票
                config.printTicket = preauth_printTicket.getContent();
                request.config = config;

                Gson gson = new Gson();
                String jsonStr = gson.toJson(request);
                PaymentService.getInstance().callPayment(jsonStr);
                tv_preauth_request.setText(getString(R.string.message_string_request) + jsonStr);
                break;
        }
    }
}
