package com.sunmi.paymentdemo.activity;

import android.app.Activity;
import android.content.IntentFilter;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;

import com.google.gson.Gson;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.qmuiteam.qmui.widget.textview.QMUILinkTextView;
import com.sunmi.payment.PaymentService;

import com.sunmi.paymentdemo.R;
import com.sunmi.paymentdemo.bean.Config;
import com.sunmi.paymentdemo.bean.Request;
import com.sunmi.paymentdemo.dialog.WaitingDialog;
import com.sunmi.paymentdemo.receiver.ResultReceiver;
import com.sunmi.paymentdemo.view.CustomEditText;
import com.sunmi.paymentdemo.view.CustomRadioButton;
import com.sunmi.paymentdemo.view.CustomTextView;

/**
 * 暂时不支持预授权完成
 */
public class PreauthCompleteActivity extends Activity implements View.OnClickListener {

    private CustomEditText preauth_complete_appType, preauth_complete_amount, preauth_complete_orderId, preauth_complete_oriMisId, preauth_complete_oriOrderId, preauth_complete_oriPlatformId;
    private CustomTextView preauth_complete_appId, preauth_complete_transType;
    private CustomRadioButton preauth_complete_printTicket;
    private QMUIRoundButton bt_preauth_complete_start, bt_preauth_complete_new_orderId;
    private QMUILinkTextView tv_preauth_complete_request, tv_preauth_complete_result;
    private ResultReceiver resultReceiver;
    private QMUITopBarLayout preauth_complete_topbar;
    private WaitingDialog waitingDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_preauth_complete);
        initView();
        registerResultReceiver();
    }

    private void registerResultReceiver() {
        resultReceiver = new ResultReceiver(result -> {
            if (waitingDialog != null && waitingDialog.isShowing()) {
                waitingDialog.dismiss();
            }
            tv_preauth_complete_result.setText(result);
        });
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ResultReceiver.RESPONSE_ACTION);
        registerReceiver(resultReceiver, intentFilter);
    }

    private void initView() {
        preauth_complete_topbar = findViewById(R.id.preauth_complete_topbar);
        preauth_complete_topbar.setTitle(R.string.home_string_preauth_complete);
        preauth_complete_topbar.addLeftBackImageButton().setOnClickListener(v -> finish());

        preauth_complete_appType = findViewById(R.id.preauth_complete_appType);
        preauth_complete_appId = findViewById(R.id.preauth_complete_appId);
        preauth_complete_transType = findViewById(R.id.preauth_complete_transType);
        preauth_complete_amount = findViewById(R.id.preauth_complete_amount);
        preauth_complete_orderId = findViewById(R.id.preauth_complete_orderId);
        preauth_complete_oriMisId = findViewById(R.id.preauth_complete_oriMisId);
        preauth_complete_oriOrderId = findViewById(R.id.preauth_complete_oriOrderId);
        preauth_complete_oriPlatformId = findViewById(R.id.preauth_complete_oriPlatformId);
        preauth_complete_printTicket = findViewById(R.id.preauth_complete_printTicket);
        tv_preauth_complete_request = findViewById(R.id.tv_preauth_complete_request);

        preauth_complete_appType.setText(R.string.title_string_appType, R.string.indicator_must);
        preauth_complete_appId.setText(R.string.title_string_appId, "", R.string.indicator_must);
        preauth_complete_transType.setText(R.string.title_string_transType, "05", R.string.indicator_must);
        preauth_complete_amount.setText(R.string.title_string_amount, R.string.indicator_optional);
        preauth_complete_orderId.setText(R.string.title_string_orderId, R.string.indicator_optional);
        preauth_complete_oriMisId.setText(R.string.title_string_oriMisId, R.string.indicator_must);
        preauth_complete_oriOrderId.setText(R.string.title_string_oriOrderId, R.string.indicator_must);
        preauth_complete_oriPlatformId.setText(R.string.title_string_oriPlatformId, R.string.indicator_must);
        preauth_complete_printTicket.setText(R.string.title_string_printTicket, R.string.indicator_optional);
        preauth_complete_printTicket.setTitle(R.string.title_string_print_ticket, R.string.title_string_unprint_ticket);

        bt_preauth_complete_start = findViewById(R.id.bt_preauth_complete_start);
        bt_preauth_complete_start.setOnClickListener(this);
        bt_preauth_complete_new_orderId = findViewById(R.id.bt_preauth_complete_new_orderId);
        bt_preauth_complete_new_orderId.setOnClickListener(this);

        tv_preauth_complete_result = findViewById(R.id.tv_preauth_complete_result);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            // 自动生一个随机的Saas订单号，并填入
            case R.id.bt_preauth_complete_new_orderId:
                String new_order_id = System.currentTimeMillis() / 1000 + "" + (int) (Math.random() * 9000 + 1000);
                preauth_complete_orderId.setContent(new_order_id);
                break;
            /**
             * 开始消费交易
             */
            case R.id.bt_preauth_complete_start:
                tv_preauth_complete_result.setText("");
                waitingDialog = new WaitingDialog(this);
                waitingDialog.show();
                /**
                 * 请求数据，最终转为json字符串发生到收银台
                 */
                Request request = new Request();
                // 应用类型
                request.appType = preauth_complete_appType.getContent();
                // 应用包名
                request.appId = getPackageName();
                // 交易类型
                request.transType = preauth_complete_transType.getContent();
                // 交易金额
                Long amount = 0L;
                try {
                    amount = Long.parseLong(preauth_complete_amount.getContent());
                } catch (Exception e) {
                }
                request.amount = amount;
                // Saas软件订单号
                request.orderId = preauth_complete_orderId.getContent();
                // 预授权App订单号
                request.oriMisId = preauth_complete_oriMisId.getContent();
                // 预授权Saas软件订单号
                request.oriOrderId = preauth_complete_oriOrderId.getContent();
                // 原商户(平台)订单号
                request.oriPlatformId = preauth_complete_oriPlatformId.getContent();
                Config config = new Config();
                // 是否打印小票
                config.printTicket = preauth_complete_printTicket.getContent();
                request.config = config;

                Gson gson = new Gson();
                String jsonStr = gson.toJson(request);
                PaymentService.getInstance().callPayment(jsonStr);
                tv_preauth_complete_request.setText(getString(R.string.message_string_request) + jsonStr);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (resultReceiver != null) {
            unregisterReceiver(resultReceiver);
        }
    }
}
