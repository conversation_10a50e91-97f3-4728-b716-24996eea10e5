package com.sunmi.paymentdemo.activity;

import android.app.Activity;
import android.content.IntentFilter;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;

import com.google.gson.Gson;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.qmuiteam.qmui.widget.textview.QMUILinkTextView;
import com.sunmi.payment.PaymentService;

import com.sunmi.paymentdemo.R;
import com.sunmi.paymentdemo.bean.Config;
import com.sunmi.paymentdemo.bean.Request;
import com.sunmi.paymentdemo.dialog.WaitingDialog;
import com.sunmi.paymentdemo.receiver.ResultReceiver;
import com.sunmi.paymentdemo.view.CustomEditText;
import com.sunmi.paymentdemo.view.CustomRadioButton;
import com.sunmi.paymentdemo.view.CustomTextView;

/**
 * 暂时不支持结算功能
 */
public class SettlementActivity extends Activity implements View.OnClickListener {

    private CustomRadioButton settlement_printTicket;
    private CustomTextView settlement_appId, settlement_transType;
    private QMUIRoundButton bt_settlement_start;
    private QMUILinkTextView tv_settlement_request, tv_settlement_result;
    private ResultReceiver resultReceiver;
    private QMUITopBarLayout settlement_topbar;
    private WaitingDialog waitingDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_settlement);
        initView();
        registerResultReceiver();
    }

    private void registerResultReceiver() {
        resultReceiver = new ResultReceiver(result -> {
            if (waitingDialog != null && waitingDialog.isShowing()) {
                waitingDialog.dismiss();
            }
            tv_settlement_result.setText(result);
        });
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ResultReceiver.RESPONSE_ACTION);
        registerReceiver(resultReceiver, intentFilter);
    }

    private void initView() {
        settlement_topbar = findViewById(R.id.settlement_topbar);
        settlement_topbar.setTitle(R.string.home_string_settlement);
        settlement_topbar.addLeftBackImageButton().setOnClickListener(v -> finish());

        settlement_appId = findViewById(R.id.settlement_appId);
        settlement_transType = findViewById(R.id.settlement_transType);
        settlement_printTicket = findViewById(R.id.settlement_printTicket);
        tv_settlement_request = findViewById(R.id.tv_settlement_request);

        settlement_appId.setText(R.string.title_string_appId, "", R.string.indicator_must);
        settlement_transType.setText(R.string.title_string_transType, "07", R.string.indicator_must);
        settlement_printTicket.setText(R.string.title_string_printTicket, R.string.indicator_optional);
        settlement_printTicket.setTitle(R.string.title_string_print_ticket, R.string.title_string_unprint_ticket);

        bt_settlement_start = findViewById(R.id.bt_settlement_start);
        bt_settlement_start.setOnClickListener(this);

        tv_settlement_result = findViewById(R.id.tv_settlement_result);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            /**
             * 开始结算交易
             */
            case R.id.bt_settlement_start:
                tv_settlement_result.setText("");
                waitingDialog = new WaitingDialog(this);
                waitingDialog.show();
                /**
                 * 请求数据，最终转为json字符串发生到收银台
                 */
                Request request = new Request();
                // 应用包名
                request.appId = getPackageName();
                // 交易类型
                request.transType = settlement_transType.getContent();
                Config config = new Config();
                // 是否打印小票
                config.printTicket = settlement_printTicket.getContent();
                request.config = config;

                Gson gson = new Gson();
                String jsonStr = gson.toJson(request);
                PaymentService.getInstance().callPayment(jsonStr);
                tv_settlement_request.setText(getString(R.string.message_string_request) + jsonStr);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (resultReceiver != null) {
            unregisterReceiver(resultReceiver);
        }
    }
}
