package com.sunmi.paymentdemo.activity;

import android.app.Activity;
import android.content.IntentFilter;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;

import com.google.gson.Gson;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.qmuiteam.qmui.widget.textview.QMUILinkTextView;
import com.sunmi.payment.PaymentService;

import com.sunmi.paymentdemo.R;
import com.sunmi.paymentdemo.bean.Request;
import com.sunmi.paymentdemo.dialog.WaitingDialog;
import com.sunmi.paymentdemo.receiver.ResultReceiver;
import com.sunmi.paymentdemo.view.CustomEditText;
import com.sunmi.paymentdemo.view.CustomTextView;

/**
 * 发起L3单条交易查询请求
 */
public class QueryActivity extends Activity implements View.OnClickListener {
    private static final String TAG = "QueryActivity";
    private CustomEditText query_misId, query_orderId, query_platformId;
    private CustomTextView query_transType, query_appId;
    private QMUIRoundButton bt_query_start;
    private QMUILinkTextView tv_query_request, tv_query_result;
    private ResultReceiver resultReceiver;
    private QMUITopBarLayout query_topbar;
    private WaitingDialog waitingDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_query);
        initView();
        registerResultReceiver();
    }

    private void registerResultReceiver() {
        resultReceiver = new ResultReceiver(result -> {
            if (waitingDialog != null && waitingDialog.isShowing()) {
                waitingDialog.dismiss();
            }
            tv_query_result.setText(result);
        });
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ResultReceiver.RESPONSE_ACTION);
        registerReceiver(resultReceiver, intentFilter);
    }

    private void initView() {
        query_topbar = findViewById(R.id.query_topbar);
        query_topbar.setTitle(R.string.home_string_query);
        query_topbar.addLeftBackImageButton().setOnClickListener(v -> finish());

        query_appId = findViewById(R.id.query_appId);
        query_transType = findViewById(R.id.query_transType);
        query_misId = findViewById(R.id.query_misId);
        query_orderId = findViewById(R.id.query_orderId);
        query_platformId = findViewById(R.id.query_platformId);
        tv_query_request = findViewById(R.id.tv_query_request);

        query_appId.setText(R.string.title_string_appId, "", R.string.indicator_must);
        query_transType.setText(R.string.title_string_transType, "A2", R.string.indicator_must);
        query_misId.setText(R.string.title_string_misId, R.string.indicator_special);
        query_orderId.setText(R.string.title_string_orderId, R.string.indicator_special);
        query_platformId.setText(R.string.title_string_platformId, R.string.indicator_special);

        bt_query_start = findViewById(R.id.bt_query_start);
        bt_query_start.setOnClickListener(this);

        tv_query_result = findViewById(R.id.tv_query_result);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.bt_query_start:
                tv_query_result.setText("");
                waitingDialog = new WaitingDialog(this);
                waitingDialog.show();
                /**
                 * 请求数据，最终转为json字符串发送到收银台
                 */
                Request request = new Request();
                // 应用包名
                request.appId = getPackageName();
                // 交易类型
                request.transType = query_transType.getContent();
                // Saas软件订单号
                request.orderId = query_orderId.getContent();
                // 收银台流水号
                request.misId = query_misId.getContent();
                // 商户订单号
                request.platformId = query_platformId.getContent();

                Gson gson = new Gson();
                String jsonStr = gson.toJson(request);
                PaymentService.getInstance().callPayment(jsonStr);
                tv_query_request.setText(getString(R.string.message_string_request) + jsonStr);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (resultReceiver != null) {
            unregisterReceiver(resultReceiver);
        }
    }
}
