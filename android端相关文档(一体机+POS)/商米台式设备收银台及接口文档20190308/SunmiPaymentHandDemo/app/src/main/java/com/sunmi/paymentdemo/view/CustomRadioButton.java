package com.sunmi.paymentdemo.view;

import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import com.sunmi.paymentdemo.R;


public class CustomRadioButton extends LinearLayout {
    private TextView radio_title, radio_indicator;
    private RadioButton radio_content, radio_uncontent;

    public CustomRadioButton(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public CustomRadioButton(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.custom_radiobutton, this, true);
        radio_title = findViewById(R.id.radio_title);
        radio_content = findViewById(R.id.radio_content);
        radio_uncontent = findViewById(R.id.radio_uncontent);
        radio_indicator = findViewById(R.id.radio_indicator);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
    }

    public void setText(int title, int indicator) {
        radio_title.setText(title);
        radio_indicator.setText(indicator);
    }

    public void setTitle(int firstTile, int secondTitle) {
        radio_content.setText(firstTile);
        radio_uncontent.setText(secondTitle);
    }

    public boolean getContent() {
        return radio_content.isChecked();
    }

    public void setChecked(boolean checked) {
        radio_content.setChecked(checked);
        radio_uncontent.setChecked(!checked);
    }
}
