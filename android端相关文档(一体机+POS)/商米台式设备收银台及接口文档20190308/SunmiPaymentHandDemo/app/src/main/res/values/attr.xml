<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="app_primary_color" format="color" /> <!-- topbar -->
    <attr name="app_content_bg_color" format="color" /> <!-- content-->
    <declare-styleable name="CustomEditText">
        <attr name="inputType" />
    </declare-styleable>
    <attr name="inputType">
        <!-- There is no content type.  The text is not editable. -->
        <flag name="none" value="0x00000000" />
        <!-- Just plain old text.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_NORMAL}. -->
        <flag name="text" value="0x00000001" />
        <!-- Can be combined with <var>text</var> and its variations to
             request capitalization of all characters.  Corresponds to
             {@link android.text.InputType#TYPE_TEXT_FLAG_CAP_CHARACTERS}. -->
        <flag name="textCapCharacters" value="0x00001001" />
        <!-- Can be combined with <var>text</var> and its variations to
             request capitalization of the first character of every word.  Corresponds to
             {@link android.text.InputType#TYPE_TEXT_FLAG_CAP_WORDS}. -->
        <flag name="textCapWords" value="0x00002001" />
        <!-- Can be combined with <var>text</var> and its variations to
             request capitalization of the first character of every sentence.  Corresponds to
             {@link android.text.InputType#TYPE_TEXT_FLAG_CAP_SENTENCES}. -->
        <flag name="textCapSentences" value="0x00004001" />
        <!-- Can be combined with <var>text</var> and its variations to
             request auto-correction of text being input.  Corresponds to
             {@link android.text.InputType#TYPE_TEXT_FLAG_AUTO_CORRECT}. -->
        <flag name="textAutoCorrect" value="0x00008001" />
        <!-- Can be combined with <var>text</var> and its variations to
             specify that this field will be doing its own auto-completion and
             talking with the input method appropriately.  Corresponds to
             {@link android.text.InputType#TYPE_TEXT_FLAG_AUTO_COMPLETE}. -->
        <flag name="textAutoComplete" value="0x00010001" />
        <!-- Can be combined with <var>text</var> and its variations to
             allow multiple lines of text in the field.  If this flag is not set,
             the text field will be constrained to a single line.  Corresponds to
             {@link android.text.InputType#TYPE_TEXT_FLAG_MULTI_LINE}. -->
        <flag name="textMultiLine" value="0x00020001" />
        <!-- Can be combined with <var>text</var> and its variations to
             indicate that though the regular text view should not be multiple
             lines, the IME should provide multiple lines if it can.  Corresponds to
             {@link android.text.InputType#TYPE_TEXT_FLAG_IME_MULTI_LINE}. -->
        <flag name="textImeMultiLine" value="0x00040001" />
        <!-- Can be combined with <var>text</var> and its variations to
             indicate that the IME should not show any
             dictionary-based word suggestions.  Corresponds to
             {@link android.text.InputType#TYPE_TEXT_FLAG_NO_SUGGESTIONS}. -->
        <flag name="textNoSuggestions" value="0x00080001" />
        <!-- Text that will be used as a URI.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_URI}. -->
        <flag name="textUri" value="0x00000011" />
        <!-- Text that will be used as an e-mail address.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_EMAIL_ADDRESS}. -->
        <flag name="textEmailAddress" value="0x00000021" />
        <!-- Text that is being supplied as the subject of an e-mail.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_EMAIL_SUBJECT}. -->
        <flag name="textEmailSubject" value="0x00000031" />
        <!-- Text that is the content of a short message.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_SHORT_MESSAGE}. -->
        <flag name="textShortMessage" value="0x00000041" />
        <!-- Text that is the content of a long message.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_LONG_MESSAGE}. -->
        <flag name="textLongMessage" value="0x00000051" />
        <!-- Text that is the name of a person.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_PERSON_NAME}. -->
        <flag name="textPersonName" value="0x00000061" />
        <!-- Text that is being supplied as a postal mailing address.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_POSTAL_ADDRESS}. -->
        <flag name="textPostalAddress" value="0x00000071" />
        <!-- Text that is a password.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_PASSWORD}. -->
        <flag name="textPassword" value="0x00000081" />
        <!-- Text that is a password that should be visible.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_VISIBLE_PASSWORD}. -->
        <flag name="textVisiblePassword" value="0x00000091" />
        <!-- Text that is being supplied as text in a web form.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_WEB_EDIT_TEXT}. -->
        <flag name="textWebEditText" value="0x000000a1" />
        <!-- Text that is filtering some other data.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_FILTER}. -->
        <flag name="textFilter" value="0x000000b1" />
        <!-- Text that is for phonetic pronunciation, such as a phonetic name
             field in a contact entry.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_PHONETIC}. -->
        <flag name="textPhonetic" value="0x000000c1" />
        <!-- Text that will be used as an e-mail address on a web form.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_WEB_EMAIL_ADDRESS}. -->
        <flag name="textWebEmailAddress" value="0x000000d1" />
        <!-- Text that will be used as a password on a web form.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_TEXT} |
             {@link android.text.InputType#TYPE_TEXT_VARIATION_WEB_PASSWORD}. -->
        <flag name="textWebPassword" value="0x000000e1" />
        <!-- A numeric only field.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_NUMBER} |
             {@link android.text.InputType#TYPE_NUMBER_VARIATION_NORMAL}. -->
        <flag name="number" value="0x00000002" />
        <!-- Can be combined with <var>number</var> and its other options to
             allow a signed number.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_NUMBER} |
             {@link android.text.InputType#TYPE_NUMBER_FLAG_SIGNED}. -->
        <flag name="numberSigned" value="0x00001002" />
        <!-- Can be combined with <var>number</var> and its other options to
             allow a decimal (fractional) number.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_NUMBER} |
             {@link android.text.InputType#TYPE_NUMBER_FLAG_DECIMAL}. -->
        <flag name="numberDecimal" value="0x00002002" />
        <!-- A numeric password field.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_NUMBER} |
             {@link android.text.InputType#TYPE_NUMBER_VARIATION_PASSWORD}. -->
        <flag name="numberPassword" value="0x00000012" />
        <!-- For entering a phone number.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_PHONE}. -->
        <flag name="phone" value="0x00000003" />
        <!-- For entering a date and time.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_DATETIME} |
             {@link android.text.InputType#TYPE_DATETIME_VARIATION_NORMAL}. -->
        <flag name="datetime" value="0x00000004" />
        <!-- For entering a date.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_DATETIME} |
             {@link android.text.InputType#TYPE_DATETIME_VARIATION_DATE}. -->
        <flag name="date" value="0x00000014" />
        <!-- For entering a time.  Corresponds to
             {@link android.text.InputType#TYPE_CLASS_DATETIME} |
             {@link android.text.InputType#TYPE_DATETIME_VARIATION_TIME}. -->
        <flag name="time" value="0x00000024" />
    </attr>
</resources>