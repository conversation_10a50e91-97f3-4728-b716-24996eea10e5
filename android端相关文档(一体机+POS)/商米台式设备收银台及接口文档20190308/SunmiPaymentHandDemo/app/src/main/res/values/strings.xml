<resources>
    <string name="app_name">收银台Demo</string>

    <string name="home_string_consume">消费</string>
    <string name="home_string_refund">退款</string>
    <string name="home_string_preauth">预授权(金融设备)</string>
    <string name="home_string_preauth_complete">预授权完成(金融设备)</string>
    <string name="home_string_settlement">结算(金融设备)</string>
    <string name="home_string_print">打印</string>
    <string name="home_string_query">单笔交易记录查询</string>
    <string name="home_string_summary_query">批量交易记录查询</string>
    <string name="home_string_title">收银台Demo</string>

    <string name="indicator_special">(特殊)</string>
    <string name="indicator_optional">(可选)</string>
    <string name="indicator_must">(必选)</string>

    <string name="title_string_appType">应用类型</string>
    <string name="title_string_appId">MIS应用包名</string>
    <string name="title_string_transType">交易类型</string>
    <string name="title_string_amount">交易金额</string>
    <string name="title_string_orderId">Saas软件订单号</string>
    <string name="title_string_platformId">商户订单号</string>
    <string name="title_string_orderInfo">商品信息</string>
    <string name="title_string_payCode">支付码</string>
    <string name="title_string_printTicket">是否打印小票</string>
    <string name="title_string_oriMisId">原收银台流水号</string>
    <string name="title_string_oriOrderId">原Saas软件订单号</string>
    <string name="title_string_oriPlatformId">原商户订单号</string>
    <string name="title_string_misId">收银台流水号</string>
    <string name="title_string_orderType">订单类型</string>
    <string name="title_string_date">日期(格式：2018-01-01)</string>
    <string name="title_string_page">分页页码</string>
    <string name="title_string_pageSize">每页返回数量</string>
    <string name="title_string_resultDisplay">显示交易结果页</string>
    <string name="title_string_result_display">显示</string>
    <string name="title_string_result_undisplay">不显示</string>
    <string name="title_string_process_display">显示</string>
    <string name="title_string_process_undisplay">不显示</string>
    <string name="title_string_processDisplay">显示收银过程UI</string>

    <string name="string_warning">"备注：当不显示结果页，收银台将不再打印小票，“是否打印小票”将不再起作用，需要L3自己处理小票打印流程。\n\t\t当不显示过程页，需要L3自己控制界面，后台在处理交易过程，过程中不要进行其他操作，直到收银台返回相关数据。"</string>
    <string name="string_new_order_id">新Saas订单号</string>
    <string name="string_start">开始</string>
    <string name="message_string_request">请求：</string>
    <string name="message_string_processing">交易请求中，请等待...</string>
    <string name="title_string_print_ticket">打印</string>
    <string name="title_string_unprint_ticket">不打印</string>
    <string name="title_string_printIdType">指定签购单上的订单号类型</string>
    <string name="title_string_remarks">备注</string>
    <string name="title_string_platform">商户订单号</string>
</resources>
