<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.qmuiteam.qmui.widget.QMUITopBarLayout
        android:id="@+id/refund_topbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingEnd="8dp"
            android:paddingStart="8dp">

            <com.sunmi.paymentdemo.view.CustomEditText
                android:id="@+id/refund_appType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.sunmi.paymentdemo.view.CustomTextView
                android:id="@+id/refund_appId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.sunmi.paymentdemo.view.CustomTextView
                android:id="@+id/refund_transType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.sunmi.paymentdemo.view.CustomEditText
                android:id="@+id/refund_amount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:inputType="number" />

            <com.sunmi.paymentdemo.view.CustomEditText
                android:id="@+id/refund_orderId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.sunmi.paymentdemo.view.CustomEditText
                android:id="@+id/refund_oriMisId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.sunmi.paymentdemo.view.CustomEditText
                android:id="@+id/refund_oriOrderId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.sunmi.paymentdemo.view.CustomEditText
                android:id="@+id/refund_oriPlatformId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_marginStart="16dp"
                android:orientation="vertical">

                <com.sunmi.paymentdemo.view.CustomRadioButton
                    android:id="@+id/refund_processDisplay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <com.sunmi.paymentdemo.view.CustomRadioButton
                    android:id="@+id/refund_resultDisplay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <com.sunmi.paymentdemo.view.CustomRadioButton
                    android:id="@+id/refund_printTicket"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <com.sunmi.paymentdemo.view.CustomEditText
                    android:id="@+id/refund_printIdType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <com.sunmi.paymentdemo.view.CustomEditText
                    android:id="@+id/refund_remarks"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </LinearLayout>

            <com.qmuiteam.qmui.widget.textview.QMUILinkTextView
                android:id="@+id/refund_warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/bt_refund_new_orderId"
                    android:layout_width="@dimen/qmui_btn_layout_width"
                    android:layout_height="wrap_content"
                    android:paddingBottom="10dp"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:paddingTop="10dp"
                    android:text="@string/string_new_order_id" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="0dp" />

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/bt_refund_start"
                    android:layout_width="@dimen/qmui_btn_layout_width"
                    android:layout_height="wrap_content"
                    android:paddingBottom="10dp"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:paddingTop="10dp"
                    android:text="@string/string_start" />
            </LinearLayout>

            <com.qmuiteam.qmui.widget.textview.QMUILinkTextView
                android:id="@+id/tv_refund_request"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.qmuiteam.qmui.widget.textview.QMUILinkTextView
                android:id="@+id/tv_refund_result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>