<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.qmuiteam.qmui.widget.QMUITopBarLayout
        android:id="@+id/query_topbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingEnd="8dp"
            android:paddingStart="8dp">

            <com.sunmi.paymentdemo.view.CustomTextView
                android:id="@+id/query_appId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.sunmi.paymentdemo.view.CustomTextView
                android:id="@+id/query_transType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.sunmi.paymentdemo.view.CustomEditText
                android:id="@+id/query_misId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.sunmi.paymentdemo.view.CustomEditText
                android:id="@+id/query_orderId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.sunmi.paymentdemo.view.CustomEditText
                android:id="@+id/query_platformId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/bt_query_start"
                android:layout_width="@dimen/qmui_btn_layout_width"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:paddingBottom="10dp"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:paddingTop="10dp"
                android:text="@string/string_start" />

            <com.qmuiteam.qmui.widget.textview.QMUILinkTextView
                android:id="@+id/tv_query_request"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.qmuiteam.qmui.widget.textview.QMUILinkTextView
                android:id="@+id/tv_query_result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>