<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.sunmi.paymentdemo">

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity android:name=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".activity.ConsumeActivity" />
        <activity android:name=".activity.RefundActivity" />
        <activity android:name=".activity.PrintActivity" />
        <activity android:name=".activity.QueryActivity" />
        <activity android:name=".activity.SummaryQueryActivity" />
        <activity android:name=".activity.PreauthActivity" />
        <activity android:name=".activity.PreauthCompleteActivity" />
        <activity android:name=".activity.SettlementActivity" />
    </application>

</manifest>