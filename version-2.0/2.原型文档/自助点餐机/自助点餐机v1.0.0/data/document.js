$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z,A,[_(u,B,w,C,y,D),_(u,E,w,C,y,F),_(u,G,w,C,y,H,A,[_(u,I,w,C,y,J)]),_(u,K,w,C,y,L),_(u,M,w,C,y,N,A,[_(u,O,w,C,y,P),_(u,Q,w,C,y,R),_(u,S,w,C,y,T)]),_(u,U,w,x,y,z,A,[_(u,V,w,C,y,W)]),_(u,X,w,x,y,z,A,[_(u,Y,w,C,y,Z),_(u,ba,w,C,y,bb),_(u,bc,w,C,y,bd)])])]),be,_(bf,z),bg,_(bh,bi,bj,_(bk,bl,bm,bl),bn,bo),bp,[],bq,_(br,_(bs,bt,bu,bv,bw,bx,by,bz,bA,bB,bC,f,bD,_(bE,bF,bG,bH,bI,bJ),bK,bL,bM,bx,bN,_(bO,bl,bP,bl),bj,_(bk,bl,bm,bl),bQ,d,bR,f,bS,bt,bT,_(bE,bF,bG,bU),bV,_(bE,bF,bG,bW),bX,bY,bZ,bF,bI,bY,ca,cb,cc,cd,ce,cf,cg,cf,ch,cf,ci,cf,cj,_(),ck,cb,cl,cb,cm,_(cn,f,co,cp,cq,cp,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cx)),cy,_(cn,f,co,bl,cq,cp,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cx)),cz,_(cn,f,co,bJ,cq,bJ,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cA))),cB,_(cC,_(bs,cD),cE,_(bs,cF,bX,cb,bT,_(bE,bF,bG,cG)),cH,_(bs,cI,bX,cb,bT,_(bE,bF,bG,cJ)),cK,_(bs,cL),cM,_(bs,cN,bu,bv,bw,bx,bD,_(bE,bF,bG,bH,bI,bJ),bV,_(bE,bF,bG,cO),bX,bY,bT,_(bE,bF,bG,cP),bK,bL,by,bz,bA,bB,bC,f,bZ,bF,ca,cb,bI,bY,cm,_(cn,f,co,cp,cq,cp,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cx)),cy,_(cn,f,co,bl,cq,cp,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cx)),cz,_(cn,f,co,bJ,cq,bJ,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cA)),cc,cd,ce,cf,cg,cf,ch,cf,ci,cf,bM,bx),cQ,_(bs,cR,bX,cb),cS,_(bs,cT,bT,_(bE,bF,bG,cG)),cU,_(bs,cV,ca,cW),cX,_(bs,cY,bA,cZ,bu,da,bX,cb,bT,_(bE,bF,bG,db),bK,dc,cc,dd,ce,cb,cg,cb,ch,cb,ci,cb),de,_(bs,df,bA,dg,bu,da,bX,cb,bT,_(bE,bF,bG,db),bK,dc,cc,dd,ce,cb,cg,cb,ch,cb,ci,cb),dh,_(bs,di,bA,dj,bu,da,bX,cb,bT,_(bE,bF,bG,db),bK,dc,cc,dd,ce,cb,cg,cb,ch,cb,ci,cb),dk,_(bs,dl,bA,dm,bu,da,bX,cb,bT,_(bE,bF,bG,db),bK,dc,cc,dd,ce,cb,cg,cb,ch,cb,ci,cb),dn,_(bs,dp,bu,da,bX,cb,bT,_(bE,bF,bG,db),bK,dc,cc,dd,ce,cb,cg,cb,ch,cb,ci,cb),dq,_(bs,dr,bA,ds,bu,da,bX,cb,bT,_(bE,bF,bG,db),bK,dc,cc,dd,ce,cb,cg,cb,ch,cb,ci,cb),dt,_(bs,du,bA,dm,bX,cb,bT,_(bE,bF,bG,db),bK,dc,cc,dd,ce,cb,cg,cb,ch,cb,ci,cb),dv,_(bs,dw,bX,cb,bT,_(bE,bF,bG,db),bK,dc,cc,dd,ce,cb,cg,cb,ch,cb,ci,cb),dx,_(bs,dy,bT,_(bE,bF,bG,db)),dz,_(bs,dA,bX,cW,bT,_(bE,bF,bG,db)),dB,_(bs,dC,bD,_(bE,bF,bG,dD,bI,bJ),bK,dc,cc,cd),dE,_(bs,dF,bD,_(bE,bF,bG,dD,bI,bJ),bK,dc,cc,dd),dG,_(bs,dH,bD,_(bE,bF,bG,dD,bI,bJ),bK,dc,cc,dd),dI,_(bs,dJ,bD,_(bE,bF,bG,dD,bI,bJ),bK,dc,cc,dd),dK,_(bs,dL,bK,dc,cc,dd),dM,_(bs,dN,bK,dc,cc,dd),dO,_(bs,dP,bK,bL),dQ,_(bs,dR,bX,cb,bT,_(bE,bF,bG,db),bK,dc,cc,cd),dS,_(bs,dT),dU,_(bs,dV,bT,_(bE,bF,bG,db)),dW,_(bs,dX,bu,bv,bw,bx,bD,_(bE,bF,bG,dY,bI,bJ),bV,_(bE,bF,bG,cO),bX,bY,bK,bL,by,dZ,bA,ea,bC,f,bZ,bF,ca,cb,bT,_(bE,bF,bG,bU),bI,bY,cm,_(cn,f,co,cp,cq,cp,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cx)),cy,_(cn,f,co,bl,cq,cp,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cx)),cz,_(cn,f,co,bJ,cq,bJ,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cA)),cc,cd,ce,cf,cg,cf,ch,cf,ci,cf,bM,bx),eb,_(bs,ec,bD,_(bE,bF,bG,bU,bI,bJ),bV,_(bE,bF,bG,bU),bT,_(bE,bF,bG,ed),cm,_(cn,d,co,bJ,cq,bJ,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,ee))),ef,_(bs,eg,bT,_(bE,eh,ei,[_(bG,bU),_(bG,cG),_(bG,ej),_(bG,bU)])),ek,_(bs,el),em,_(bs,en,bu,bv,bw,bx,by,bz,bA,bB,bC,f,bD,_(bE,bF,bG,bH,bI,bJ),bK,bL,bM,bx,bT,_(bE,bF,bG,bU),bV,_(bE,bF,bG,bH),bX,bY,bZ,bF,bI,bY,ca,cb,cc,cd,ce,cf,cg,cf,ch,cf,ci,cf,cm,_(cn,f,co,cp,cq,cp,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cx)),cy,_(cn,f,co,bl,cq,cp,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cx)),cz,_(cn,f,co,bJ,cq,bJ,cr,cp,bG,_(cs,ct,cu,ct,cv,ct,cw,cA))),eo,_(bs,ep,bV,_(bE,bF,bG,dD)),eq,_(bs,er,bX,cb,bT,_(bE,bF,bG,bH))),es,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="商户后台",w="type",x="Folder",y="url",z="",A="children",B="整体设计说明",C="Wireframe",D="整体设计说明.html",E="点餐机主流程图",F="点餐机主流程图.html",G="登录(首次)",H="登录_首次_.html",I="登录(再次)",J="登录_再次_.html",K="全屏广告",L="全屏广告.html",M="点餐-4列",N="点餐-4列.html",O="[对话框]选择规格/含属性商品",P="_对话框_选择规格_含属性商品.html",Q="[对话框]选择套餐商品(可选分组)",R="_对话框_选择套餐商品_可选分组_.html",S="[对话框]整单备注",T="_对话框_整单备注.html",U="支付P1",V="支付",W="支付.html",X="设置",Y="[对话框]设置验证",Z="_对话框_设置验证.html",ba="使用配置",bb="使用配置.html",bc="用户协议",bd="用户协议.html",be="globalVariables",bf="onloadvariable",bg="defaultAdaptiveView",bh="name",bi="Base",bj="size",bk="width",bl=0,bm="height",bn="condition",bo="<=",bp="adaptiveViews",bq="stylesheet",br="defaultStyle",bs="id",bt="627587b6038d43cca051c114ac41ad32",bu="fontWeight",bv="400",bw="fontStyle",bx="normal",by="fontName",bz="'ArialMT', 'Arial'",bA="fontSize",bB="13px",bC="underline",bD="foreGroundFill",bE="fillType",bF="solid",bG="color",bH=0xFF333333,bI="opacity",bJ=1,bK="horizontalAlignment",bL="center",bM="lineSpacing",bN="location",bO="x",bP="y",bQ="visible",bR="limbo",bS="baseStyle",bT="fill",bU=0xFFFFFFFF,bV="borderFill",bW=0xFF797979,bX="borderWidth",bY="1",bZ="linePattern",ca="cornerRadius",cb="0",cc="verticalAlignment",cd="middle",ce="paddingLeft",cf="2",cg="paddingTop",ch="paddingRight",ci="paddingBottom",cj="stateStyles",ck="rotation",cl="textRotation",cm="outerShadow",cn="on",co="offsetX",cp=5,cq="offsetY",cr="blurRadius",cs="r",ct=0,cu="g",cv="b",cw="a",cx=0.349019607843137,cy="innerShadow",cz="textShadow",cA=0.647058823529412,cB="customStyles",cC="box_1",cD="********************************",cE="box_2",cF="********************************",cG=0xFFF2F2F2,cH="box_3",cI="********************************",cJ=0xFFD7D7D7,cK="ellipse",cL="eff044fe6497434a8c5f89f769ddde3b",cM="_形状",cN="40519e9ec4264601bfb12c514e4f4867",cO=0xFFCCCCCC,cP=0x19333333,cQ="image",cR="75a91ee5b9d042cfa01b8d565fe289c0",cS="placeholder",cT="c50e74f669b24b37bd9c18da7326bccd",cU="button",cV="c9f35713a1cf4e91a0f2dbac65e6fb5c",cW="5",cX="heading_1",cY="1111111151944dfba49f67fd55eb1f88",cZ="32px",da="bold",db=0xFFFFFF,dc="left",dd="top",de="heading_2",df="b3a15c9ddde04520be40f94c8168891e",dg="24px",dh="heading_3",di="8c7a4c5ad69a4369a5f7788171ac0b32",dj="18px",dk="heading_4",dl="e995c891077945c89c0b5fe110d15a0b",dm="14px",dn="heading_5",dp="386b19ef4be143bd9b6c392ded969f89",dq="heading_6",dr="fc3b9a13b5574fa098ef0a1db9aac861",ds="10px",dt="label",du="2285372321d148ec80932747449c36c9",dv="paragraph",dw="4988d43d80b44008a4a415096f1632af",dx="line",dy="619b2148ccc1497285562264d51992f9",dz="arrow",dA="d148f2c5268542409e72dde43e40043e",dB="text_field",dC="44157808f2934100b68f2394a66b2bba",dD=0xFF000000,dE="text_area",dF="42ee17691d13435b8256d8d0a814778f",dG="droplist",dH="85f724022aae41c594175ddac9c289eb",dI="list_box",dJ="********************************",dK="checkbox",dL="********************************",dM="radio_button",dN="4eb5516f311c4bdfa0cb11d7ea75084e",dO="html_button",dP="eed12d9ebe2e4b9689b3b57949563dca",dQ="tree_node",dR="93a4c3353b6f4562af635b7116d6bf94",dS="table_cell",dT="33ea2511485c479dbf973af3302f2352",dU="menu_item",dV="2036b2baccbc41f0b9263a6981a11a42",dW="connector",dX="699a012e142a4bcba964d96e88b88bdf",dY=0xFF0000FF,dZ="'PingFangSC-Regular', 'PingFang SC'",ea="12px",eb="marker",ec="a8e305fe5c2a462b995b0021a9ba82b9",ed=0xFF009DD9,ee=0.698039215686274,ef="flow_shape",eg="df01900e3c4e43f284bafec04b0864c4",eh="linearGradient",ei="colors",ej=0xFFE4E4E4,ek="table",el="d612b8c2247342eda6a8bc0663265baa",em="shape",en="98c916898e844865a527f56bc61a500d",eo="horizontal_line",ep="f48196c19ab74fb7b3acb5151ce8ea2d",eq="icon",er="26c731cb771b44a88eb8b6e97e78c80e",es="duplicateStyles";
return _creator();
})());