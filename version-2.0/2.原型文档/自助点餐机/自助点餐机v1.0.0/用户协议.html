<!DOCTYPE html>
<html>
  <head>
    <title>用户协议</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/用户协议/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/用户协议/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (主框架) -->

      <!-- Unnamed (Rectangle) -->
      <div id="u1169" class="ax_default box_3">
        <div id="u1169_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1170" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u1171" class="ax_default box_2">
        <div id="u1171_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1172" class="text" style="visibility: visible;">
          <p><span>&nbsp; &lt;</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u1173" class="ax_default paragraph">
        <img id="u1173_img" class="img " src="images/用户协议/u1173.png"/>
        <!-- Unnamed () -->
        <div id="u1174" class="text" style="visibility: visible;">
          <p><span>holder智慧门店管理软件用户服务协议</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u1175" class="ax_default paragraph">
        <img id="u1175_img" class="img " src="images/用户协议/u1175.png"/>
        <!-- Unnamed () -->
        <div id="u1176" class="text" style="visibility: visible;">
          <p><span style="color:#1B5C57;">1.当前设备：显示设备ID,设备名称，支持修改，（必填，1-10个字）</span></p><p><span style="color:#1B5C57;"><br></span></p><p><span style="color:#1B5C57;">商品列表呈现： 2、3列（</span><span style="color:#FF0000;">本期不做，只页面展示，不可点</span><span style="color:#1B5C57;">），只做页面展示，默认为4列</span></p><p><span style="color:#1B5C57;"><br></span></p><p><span style="color:#1B5C57;">扫码收款方式：默认主扫，同时选择主扫、被扫时，默认被扫＞被扫（</span><span style="color:#FF0000;">本期不做优先级调整</span><span style="color:#1B5C57;">）</span></p><p><span style="color:#1B5C57;"><br></span></p><p><span style="color:#1B5C57;">支持待机广告：默认开启，默认空置时间为1分钟，可修改（内容和修改时间(默认空置时间1分钟)由后台设置</span></p><p><span style="color:#1B5C57;"><br></span></p><p><span style="color:#1B5C57;">小票</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u1177" class="ax_default box_2">
        <div id="u1177_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1178" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u1179" class="ax_default box_2">
        <div id="u1179_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1180" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u1181" class="ax_default box_2">
        <div id="u1181_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1182" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u1183" class="ax_default box_2">
        <div id="u1183_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1184" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u1185" class="ax_default box_2">
        <div id="u1185_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1186" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u1187" class="ax_default box_2">
        <div id="u1187_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1188" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>
    </div>
  </body>
</html>
