<!DOCTYPE html>
<html>
  <head>
    <title>[对话框]选择套餐商品(可选分组)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/_对话框_选择套餐商品_可选分组_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/_对话框_选择套餐商品_可选分组_/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Rectangle) -->
      <div id="u720" class="ax_default box_1">
        <div id="u720_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u721" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u722" class="ax_default paragraph">
        <img id="u722_img" class="img " src="images/_对话框_选择套餐商品_可选分组_/u722.png"/>
        <!-- Unnamed () -->
        <div id="u723" class="text" style="visibility: visible;">
          <p><span>鱼香肉丝套餐</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u724" class="ax_default box_2">
        <div id="u724_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u725" class="text" style="visibility: visible;">
          <p><span>肥牛×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u726" class="ax_default paragraph">
        <img id="u726_img" class="img " src="images/_对话框_选择规格_含属性商品/u570.png"/>
        <!-- Unnamed () -->
        <div id="u727" class="text" style="visibility: visible;">
          <p><span>荤菜</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u728" class="ax_default box_2">
        <div id="u728_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u729" class="text" style="visibility: visible;">
          <p><span>鲜毛肚×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u730" class="ax_default box_2">
        <div id="u730_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u731" class="text" style="visibility: visible;">
          <p><span>千层肚×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u732" class="ax_default box_2">
        <div id="u732_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u733" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u734" class="ax_default box_2">
        <div id="u734_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u735" class="text" style="visibility: visible;">
          <p><span>嫩牛肉×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u736" class="ax_default box_2">
        <div id="u736_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u737" class="text" style="visibility: visible;">
          <p><span>鹌鹑蛋×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u738" class="ax_default paragraph">
        <img id="u738_img" class="img " src="images/_对话框_选择规格_含属性商品/u576.png"/>
        <!-- Unnamed () -->
        <div id="u739" class="text" style="visibility: visible;">
          <p><span>素菜(选99项)</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u740" class="ax_default paragraph">
        <img id="u740_img" class="img " src="images/_对话框_选择规格_含属性商品/u574.png"/>
        <!-- Unnamed () -->
        <div id="u741" class="text" style="visibility: visible;">
          <p><span>时令野菜(选5项）</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u742" class="ax_default box_2">
        <div id="u742_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u743" class="text" style="visibility: visible;">
          <p><span>鸭血</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u744" class="ax_default box_2">
        <div id="u744_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u745" class="text" style="visibility: visible;">
          <p><span>土豆</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u746" class="ax_default box_2">
        <div id="u746_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u747" class="text" style="visibility: visible;">
          <p><span>豆芽</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u748" class="ax_default box_2">
        <div id="u748_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u749" class="text" style="visibility: visible;">
          <p><span>藕片×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u750" class="ax_default box_2">
        <div id="u750_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u751" class="text" style="visibility: visible;">
          <p><span>新鲜海带苗×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u752" class="ax_default paragraph">
        <img id="u752_img" class="img " src="images/_对话框_选择套餐商品_可选分组_/u752.png"/>
        <!-- Unnamed () -->
        <div id="u753" class="text" style="visibility: visible;">
          <p><span>请选择单品</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u754" class="ax_default box_2">
        <div id="u754_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u755" class="text" style="visibility: visible;">
          <p><span>土豆</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u756" class="ax_default box_2">
        <div id="u756_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u757" class="text" style="visibility: visible;">
          <p><span>莴笋</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u758" class="ax_default box_2">
        <div id="u758_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u759" class="text" style="visibility: visible;">
          <p><span>黄喉</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u760" class="ax_default box_2">
        <div id="u760_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u761" class="text" style="visibility: visible;">
          <p><span>功夫土豆</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u762" class="ax_default box_2">
        <div id="u762_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u763" class="text" style="visibility: visible;">
          <p><span>澄阳湖脆藕×99</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u764" class="ax_default box_2">
        <div id="u764_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u765" class="text" style="visibility: visible;">
          <p><span>油麦菜×99&nbsp; &nbsp; ￥99.99 </span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u766" class="ax_default box_2">
        <div id="u766_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u767" class="text" style="visibility: visible;">
          <p><span>喔喔家油条豆浆包腊肉×99.999kg&nbsp; ￥99.99</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u768" class="ax_default ellipse">
        <div id="u768_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u769" class="text" style="visibility: visible;">
          <p><span>确定</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u770" class="ax_default box_1">
        <div id="u770_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u771" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u772" class="ax_default box_2">
        <div id="u772_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u773" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u774" class="ax_default paragraph">
        <img id="u774_img" class="img " src="images/_对话框_选择套餐商品_可选分组_/u774.png"/>
        <!-- Unnamed () -->
        <div id="u775" class="text" style="visibility: visible;">
          <p style="font-size:18px;"><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">￥99999.99</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;font-size:12px;">(肥牛、鲜毛肚、千层肚、喔条…</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u776" class="ax_default paragraph">
        <img id="u776_img" class="img " src="images/_对话框_选择套餐商品_可选分组_/u722.png"/>
        <!-- Unnamed () -->
        <div id="u777" class="text" style="visibility: visible;">
          <p><span>鱼香肉丝套餐</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u778" class="ax_default box_2">
        <div id="u778_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u779" class="text" style="visibility: visible;">
          <p><span>肥牛×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u780" class="ax_default paragraph">
        <img id="u780_img" class="img " src="images/_对话框_选择规格_含属性商品/u570.png"/>
        <!-- Unnamed () -->
        <div id="u781" class="text" style="visibility: visible;">
          <p><span>荤菜</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u782" class="ax_default box_2">
        <div id="u782_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u783" class="text" style="visibility: visible;">
          <p><span>鲜毛肚×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u784" class="ax_default box_2">
        <div id="u784_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u785" class="text" style="visibility: visible;">
          <p><span>千层肚×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u786" class="ax_default box_2">
        <div id="u786_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u787" class="text" style="visibility: visible;">
          <p><span>嫩牛肉×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u788" class="ax_default box_2">
        <div id="u788_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u789" class="text" style="visibility: visible;">
          <p><span>鹌鹑蛋 ×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u790" class="ax_default paragraph">
        <img id="u790_img" class="img " src="images/_对话框_选择规格_含属性商品/u576.png"/>
        <!-- Unnamed () -->
        <div id="u791" class="text" style="visibility: visible;">
          <p><span>素菜(选99项)</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u792" class="ax_default box_2">
        <div id="u792_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u793" class="text" style="visibility: visible;">
          <p><span>鸭血 </span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u794" class="ax_default box_2">
        <div id="u794_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u795" class="text" style="visibility: visible;">
          <p><span>土豆 </span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u796" class="ax_default box_2">
        <div id="u796_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u797" class="text" style="visibility: visible;">
          <p><span>豆芽 </span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u798" class="ax_default box_2">
        <div id="u798_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u799" class="text" style="visibility: visible;">
          <p><span>藕片 ×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u800" class="ax_default box_2">
        <div id="u800_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u801" class="text" style="visibility: visible;">
          <p><span>新鲜海带苗 ×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u802" class="ax_default box_1">
        <div id="u802_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u803" class="text" style="visibility: visible;">
          <p><span>98</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u804" class="ax_default ellipse">
        <div id="u804_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u805" class="text" style="visibility: visible;">
          <p><span>+</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u806" class="ax_default ellipse">
        <div id="u806_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u807" class="text" style="visibility: visible;">
          <p><span>-</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u808" class="ax_default paragraph">
        <img id="u808_img" class="img " src="images/_对话框_选择规格_含属性商品/u574.png"/>
        <!-- Unnamed () -->
        <div id="u809" class="text" style="visibility: visible;">
          <p><span>时令野菜(选5项）</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u810" class="ax_default box_2">
        <div id="u810_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u811" class="text" style="visibility: visible;">
          <p><span>土豆</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u812" class="ax_default box_2">
        <div id="u812_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u813" class="text" style="visibility: visible;">
          <p><span>莴笋</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u814" class="ax_default box_2">
        <div id="u814_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u815" class="text" style="visibility: visible;">
          <p><span>黄喉</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u816" class="ax_default box_2">
        <div id="u816_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u817" class="text" style="visibility: visible;">
          <p><span>功夫土豆</span><span>×1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u818" class="ax_default box_2">
        <div id="u818_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u819" class="text" style="visibility: visible;">
          <p><span>澄阳湖脆藕×99</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u820" class="ax_default box_2">
        <div id="u820_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u821" class="text" style="visibility: visible;">
          <p><span>油麦菜×99&nbsp; &nbsp; ￥99.99 </span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u822" class="ax_default box_2">
        <div id="u822_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u823" class="text" style="visibility: visible;">
          <p><span>喔喔家油条豆浆包腊肉×99.999kg&nbsp; ￥99.99</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u824" class="ax_default box_1">
        <div id="u824_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u825" class="text" style="visibility: visible;">
          <p><span>1</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u826" class="ax_default ellipse">
        <div id="u826_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u827" class="text" style="visibility: visible;">
          <p><span>+</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u828" class="ax_default ellipse">
        <div id="u828_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u829" class="text" style="visibility: visible;">
          <p><span>-</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u830" class="ax_default box_2">
        <div id="u830_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u831" class="text" style="visibility: visible;">
          <p><span>确定</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u832" class="ax_default paragraph">
        <img id="u832_img" class="img " src="images/_对话框_选择套餐商品_可选分组_/u832.png"/>
        <!-- Unnamed () -->
        <div id="u833" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">固定分组</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：初始全部选中状态，不可取消勾选</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u834" class="ax_default connector">
        <img id="u834_seg0" class="img " src="images/_对话框_选择套餐商品_可选分组_/u834_seg0.png" alt="u834_seg0"/>
        <img id="u834_seg1" class="img " src="images/_对话框_选择套餐商品_可选分组_/u834_seg1.png" alt="u834_seg1"/>
        <img id="u834_seg2" class="img " src="images/_对话框_选择套餐商品_可选分组_/u834_seg2.png" alt="u834_seg2"/>
        <img id="u834_seg3" class="img " src="images/点餐-4列/u431_seg3.png" alt="u834_seg3"/>
        <!-- Unnamed () -->
        <div id="u835" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u836" class="ax_default paragraph">
        <img id="u836_img" class="img " src="images/_对话框_选择套餐商品_可选分组_/u836.png"/>
        <!-- Unnamed () -->
        <div id="u837" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">含可选分组的套餐</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.选单品进入</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.1可选分组不支持商户后台的默认勾选配置项；</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.2当可选分组内的单品可售量＜单品配置的初始量时，置灰不可选</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2.已选商品进入</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">已选内容显示选中状态</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u838" class="ax_default connector">
        <img id="u838_seg0" class="img " src="images/_对话框_选择规格_含属性商品/u704_seg0.png" alt="u838_seg0"/>
        <img id="u838_seg1" class="img " src="images/_对话框_选择规格_含属性商品/u704_seg1.png" alt="u838_seg1"/>
        <img id="u838_seg2" class="img " src="images/_对话框_选择规格_含属性商品/u704_seg2.png" alt="u838_seg2"/>
        <!-- Unnamed () -->
        <div id="u839" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u840" class="ax_default paragraph">
        <img id="u840_img" class="img " src="images/_对话框_选择规格_含属性商品/u706.png"/>
        <!-- Unnamed () -->
        <div id="u841" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">可选分组单品，支持重复选择</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.初始未勾选，点击单品内容，切换为已选中状态，显示数量加减按钮(初始数量为单品配置数量)；</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2.添加数量时,（最大量限制优先级为：可销售剩余量＞分组最大数），同时累减可点剩余数量。当可点剩余数量为0时，其他为选项置灰不可选；减掉数量后，解除禁选状态；</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">3.减少数量，同时累加可点剩余数量，到0时，切换为未选中状态</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u842" class="ax_default paragraph">
        <img id="u842_img" class="img " src="images/_对话框_选择规格_含属性商品/u708.png"/>
        <!-- Unnamed () -->
        <div id="u843" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">可选分组单品，不支持重复选择</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.初始未勾选，点击单品内容，切换为已选中状态，当已选数量达设置的可选数量时，其他为选项置灰不可选，减掉数量后，解除禁选状态</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2.再次点击已选单品内容，切换为未选中状态，同时累加可点剩余数量</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u844" class="ax_default connector">
        <img id="u844_seg0" class="img " src="images/_对话框_选择规格_含属性商品/u710_seg0.png" alt="u844_seg0"/>
        <img id="u844_seg1" class="img " src="images/_对话框_选择规格_含属性商品/u710_seg1.png" alt="u844_seg1"/>
        <img id="u844_seg2" class="img " src="images/_对话框_选择规格_含属性商品/u710_seg2.png" alt="u844_seg2"/>
        <!-- Unnamed () -->
        <div id="u845" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u846" class="ax_default paragraph">
        <img id="u846_img" class="img " src="images/_对话框_选择规格_含属性商品/u712.png"/>
        <!-- Unnamed () -->
        <div id="u847" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">已选信息概览</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.选商品进入</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.1初始无价格及选中单品信息</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.2满足设置的选择条件时，显示价格，设置“确定”按钮可点</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2已选商品进入</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">初始已选单品信息；点“确定”保存已选内容并关闭对话框；点关闭/空白区域关闭弹窗，撤销编辑内容并关闭对话框</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u848" class="ax_default connector">
        <img id="u848_seg0" class="img " src="images/点餐-4列/u557_seg0.png" alt="u848_seg0"/>
        <img id="u848_seg1" class="img " src="images/_对话框_选择套餐商品_可选分组_/u848_seg1.png" alt="u848_seg1"/>
        <img id="u848_seg2" class="img " src="images/_对话框_选择套餐商品_可选分组_/u848_seg2.png" alt="u848_seg2"/>
        <img id="u848_seg3" class="img " src="images/_对话框_选择套餐商品_可选分组_/u848_seg3.png" alt="u848_seg3"/>
        <img id="u848_seg4" class="img " src="images/_对话框_选择套餐商品_可选分组_/u848_seg4.png" alt="u848_seg4"/>
        <!-- Unnamed () -->
        <div id="u849" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 关闭内部框架 (Rectangle) -->
      <div id="u850" class="ax_default ellipse" data-label="关闭内部框架">
        <div id="u850_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u851" class="text" style="visibility: visible;">
          <p><span>×</span></p>
        </div>
      </div>

      <!-- 关闭内部框架 (Rectangle) -->
      <div id="u852" class="ax_default ellipse" data-label="关闭内部框架">
        <div id="u852_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u853" class="text" style="visibility: visible;">
          <p><span>×</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
