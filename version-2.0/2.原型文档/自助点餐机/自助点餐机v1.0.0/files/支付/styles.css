body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1648px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u908_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u908 {
  position:absolute;
  left:10px;
  top:10px;
  width:540px;
  height:805px;
}
#u909 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u910_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:238px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u910 {
  position:absolute;
  left:10px;
  top:362px;
  width:540px;
  height:238px;
}
#u911 {
  position:absolute;
  left:2px;
  top:111px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u912 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u913_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u913 {
  position:absolute;
  left:602px;
  top:10px;
  width:478px;
  height:257px;
}
#u914 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u915_img {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:33px;
}
#u915 {
  position:absolute;
  left:789px;
  top:27px;
  width:97px;
  height:33px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
}
#u916 {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  white-space:nowrap;
}
#u917_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u917 {
  position:absolute;
  left:650px;
  top:190px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u918 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u919_img {
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:17px;
}
#u919 {
  position:absolute;
  left:652px;
  top:97px;
  width:133px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u920 {
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  white-space:nowrap;
}
#u921_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u921 {
  position:absolute;
  left:853px;
  top:190px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u922 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u923_img {
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:17px;
}
#u923 {
  position:absolute;
  left:208px;
  top:204px;
  width:112px;
  height:17px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u924 {
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  word-wrap:break-word;
}
#u925_img {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:8px;
}
#u925 {
  position:absolute;
  left:234px;
  top:221px;
  width:53px;
  height:8px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:6px;
  text-align:center;
}
#u926 {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  word-wrap:break-word;
}
#u927_div {
  position:absolute;
  left:0px;
  top:0px;
  width:538px;
  height:60px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:left;
}
#u927 {
  position:absolute;
  left:12px;
  top:12px;
  width:538px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:left;
}
#u928 {
  position:absolute;
  left:2px;
  top:16px;
  width:534px;
  word-wrap:break-word;
}
#u929_img {
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:17px;
}
#u929 {
  position:absolute;
  left:185px;
  top:34px;
  width:158px;
  height:17px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u930 {
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  white-space:nowrap;
}
#u931 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u932_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u932 {
  position:absolute;
  left:602px;
  top:287px;
  width:478px;
  height:257px;
}
#u933 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u934_img {
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:17px;
}
#u934 {
  position:absolute;
  left:780px;
  top:412px;
  width:133px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u935 {
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  white-space:nowrap;
}
#u936_div {
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u936 {
  position:absolute;
  left:849px;
  top:475px;
  width:82px;
  height:31px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u937 {
  position:absolute;
  left:2px;
  top:7px;
  width:78px;
  word-wrap:break-word;
}
#u938_img {
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:53px;
}
#u938 {
  position:absolute;
  left:792px;
  top:329px;
  width:117px;
  height:53px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  text-align:center;
}
#u939 {
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  word-wrap:break-word;
}
#u940_img {
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:17px;
}
#u940 {
  position:absolute;
  left:783px;
  top:482px;
  width:56px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:center;
}
#u941 {
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  white-space:nowrap;
}
#u942 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u943_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u943 {
  position:absolute;
  left:602px;
  top:582px;
  width:478px;
  height:257px;
}
#u944 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u945_img {
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:17px;
}
#u945 {
  position:absolute;
  left:640px;
  top:702px;
  width:157px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u946 {
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  white-space:nowrap;
}
#u947_img {
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:53px;
}
#u947 {
  position:absolute;
  left:765px;
  top:617px;
  width:171px;
  height:53px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  text-align:center;
}
#u948 {
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  word-wrap:break-word;
}
#u949_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u949 {
  position:absolute;
  left:650px;
  top:755px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u950 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u951_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u951 {
  position:absolute;
  left:853px;
  top:755px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u952 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u953 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u954_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u954 {
  position:absolute;
  left:602px;
  top:891px;
  width:478px;
  height:257px;
}
#u955 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u956_img {
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  height:17px;
}
#u956 {
  position:absolute;
  left:754px;
  top:989px;
  width:229px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u957 {
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  white-space:nowrap;
}
#u958_img {
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  height:53px;
}
#u958 {
  position:absolute;
  left:753px;
  top:926px;
  width:195px;
  height:53px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u959 {
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  word-wrap:break-word;
}
#u960_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u960 {
  position:absolute;
  left:652px;
  top:1065px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u961 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u962_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u962 {
  position:absolute;
  left:866px;
  top:1065px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u963 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u964_div {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u964 {
  position:absolute;
  left:31px;
  top:404px;
  width:97px;
  height:38px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u964_div.selected {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u964.selected {
}
#u964_div.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u964.disabled {
}
#u965 {
  position:absolute;
  left:2px;
  top:10px;
  width:93px;
  word-wrap:break-word;
}
#u966_div {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u966 {
  position:absolute;
  left:31px;
  top:441px;
  width:97px;
  height:38px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u967 {
  position:absolute;
  left:2px;
  top:10px;
  width:93px;
  word-wrap:break-word;
}
#u968 {
  position:absolute;
  left:175px;
  top:404px;
  width:358px;
  height:390px;
  overflow:hidden;
}
#u968_state0 {
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:390px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
}
#u968_state0_content {
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u969_div {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:108px;
  background:inherit;
  background-color:rgba(0, 0, 0, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u969 {
  position:absolute;
  left:15px;
  top:10px;
  width:108px;
  height:108px;
}
#u970 {
  position:absolute;
  left:2px;
  top:46px;
  width:104px;
  visibility:hidden;
  word-wrap:break-word;
}
#u971_img {
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:16px;
}
#u971 {
  position:absolute;
  left:133px;
  top:16px;
  width:101px;
  height:16px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:6px;
}
#u972 {
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  word-wrap:break-word;
}
#u973_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:105px;
  height:6px;
}
#u973 {
  position:absolute;
  left:18px;
  top:45px;
  width:102px;
  height:3px;
}
#u974 {
  position:absolute;
  left:2px;
  top:-6px;
  width:98px;
  visibility:hidden;
  word-wrap:break-word;
}
#u975_div {
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#CCCCCC;
}
#u975 {
  position:absolute;
  left:178px;
  top:112px;
  width:129px;
  height:45px;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#CCCCCC;
}
#u976 {
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  white-space:nowrap;
}
#u977_img {
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:50px;
}
#u977 {
  position:absolute;
  left:307px;
  top:121px;
  width:43px;
  height:50px;
}
#u978 {
  position:absolute;
  left:2px;
  top:17px;
  width:39px;
  visibility:hidden;
  word-wrap:break-word;
}
#u979_img {
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:16px;
}
#u979 {
  position:absolute;
  left:265px;
  top:16px;
  width:85px;
  height:16px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u980 {
  position:absolute;
  left:2px;
  top:0px;
  width:81px;
  word-wrap:break-word;
}
#u968_state1 {
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:390px;
  visibility:hidden;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
}
#u968_state1_content {
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u981_div {
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:112px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#999999;
}
#u981 {
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:112px;
  color:#999999;
}
#u982 {
  position:absolute;
  left:2px;
  top:47px;
  width:108px;
  word-wrap:break-word;
}
#u983_img {
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:11px;
}
#u983 {
  position:absolute;
  left:16px;
  top:122px;
  width:87px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-style:normal;
  font-size:8px;
  text-align:center;
}
#u984 {
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  white-space:nowrap;
}
#u985_div {
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:112px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#999999;
}
#u985 {
  position:absolute;
  left:136px;
  top:0px;
  width:112px;
  height:112px;
  color:#999999;
}
#u986 {
  position:absolute;
  left:2px;
  top:47px;
  width:108px;
  word-wrap:break-word;
}
#u987_img {
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:11px;
}
#u987 {
  position:absolute;
  left:149px;
  top:122px;
  width:87px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-style:normal;
  font-size:8px;
  text-align:center;
}
#u988 {
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  white-space:nowrap;
}
#u989_div {
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u989 {
  position:absolute;
  left:266px;
  top:87px;
  width:82px;
  height:36px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u990 {
  position:absolute;
  left:2px;
  top:10px;
  width:78px;
  word-wrap:break-word;
}
#u968_state2 {
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:390px;
  visibility:hidden;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
}
#u968_state2_content {
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u991 {
  position:absolute;
  left:34px;
  top:31px;
  width:119px;
  height:30px;
}
#u991_input {
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u992_div {
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#999999;
}
#u992 {
  position:absolute;
  left:155px;
  top:31px;
  width:60px;
  height:31px;
  font-size:8px;
  color:#999999;
}
#u993 {
  position:absolute;
  left:2px;
  top:10px;
  width:56px;
  word-wrap:break-word;
}
#u994_div {
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:45px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u994 {
  position:absolute;
  left:32px;
  top:86px;
  width:187px;
  height:45px;
  color:#FFFFFF;
}
#u995 {
  position:absolute;
  left:2px;
  top:14px;
  width:183px;
  word-wrap:break-word;
}
#u996_img {
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:11px;
}
#u996 {
  position:absolute;
  left:34px;
  top:12px;
  width:121px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
}
#u997 {
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  word-wrap:break-word;
}
#u998_img {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:2px;
}
#u998 {
  position:absolute;
  left:64px;
  top:478px;
  width:159px;
  height:1px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u999 {
  position:absolute;
  left:2px;
  top:-8px;
  width:155px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1000_div {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1000 {
  position:absolute;
  left:31px;
  top:478px;
  width:97px;
  height:38px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1000_div.selected {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1000.selected {
}
#u1000_div.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1000.disabled {
}
#u1001 {
  position:absolute;
  left:2px;
  top:10px;
  width:93px;
  word-wrap:break-word;
}
#u1002_img {
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:16px;
}
#u1002 {
  position:absolute;
  left:108px;
  top:452px;
  width:57px;
  height:16px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:8px;
}
#u1003 {
  position:absolute;
  left:2px;
  top:2px;
  width:53px;
  word-wrap:break-word;
}
#u1004_img {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:11px;
}
#u1004 {
  position:absolute;
  left:128px;
  top:677px;
  width:221px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-align:center;
}
#u1005 {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  word-wrap:break-word;
}
#u1006_img {
  position:absolute;
  left:0px;
  top:0px;
  width:249px;
  height:51px;
}
#u1006 {
  position:absolute;
  left:1125px;
  top:80px;
  width:249px;
  height:51px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1007 {
  position:absolute;
  left:0px;
  top:0px;
  width:249px;
  white-space:nowrap;
}
#u1008_img {
  position:absolute;
  left:0px;
  top:0px;
  width:285px;
  height:68px;
}
#u1008 {
  position:absolute;
  left:1125px;
  top:287px;
  width:285px;
  height:68px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1009 {
  position:absolute;
  left:0px;
  top:0px;
  width:285px;
  white-space:nowrap;
}
#u1010_img {
  position:absolute;
  left:0px;
  top:0px;
  width:383px;
  height:68px;
}
#u1010 {
  position:absolute;
  left:1125px;
  top:582px;
  width:383px;
  height:68px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1011 {
  position:absolute;
  left:0px;
  top:0px;
  width:383px;
  white-space:nowrap;
}
#u1012_img {
  position:absolute;
  left:0px;
  top:0px;
  width:371px;
  height:119px;
}
#u1012 {
  position:absolute;
  left:1125px;
  top:891px;
  width:371px;
  height:119px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1013 {
  position:absolute;
  left:0px;
  top:0px;
  width:371px;
  white-space:nowrap;
}
#u1014_img {
  position:absolute;
  left:0px;
  top:0px;
  width:523px;
  height:34px;
}
#u1014 {
  position:absolute;
  left:1125px;
  top:9px;
  width:523px;
  height:34px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1015 {
  position:absolute;
  left:0px;
  top:0px;
  width:523px;
  white-space:nowrap;
}
#u1016 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1017_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1017 {
  position:absolute;
  left:602px;
  top:1168px;
  width:478px;
  height:257px;
}
#u1018 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1019_img {
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:17px;
}
#u1019 {
  position:absolute;
  left:748px;
  top:1266px;
  width:205px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1020 {
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  white-space:nowrap;
}
#u1021_img {
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:53px;
}
#u1021 {
  position:absolute;
  left:765px;
  top:1203px;
  width:171px;
  height:53px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u1022 {
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  word-wrap:break-word;
}
#u1023_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1023 {
  position:absolute;
  left:652px;
  top:1342px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1024 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u1025_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1025 {
  position:absolute;
  left:866px;
  top:1342px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1026 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u1027_img {
  position:absolute;
  left:0px;
  top:0px;
  width:371px;
  height:68px;
}
#u1027 {
  position:absolute;
  left:1125px;
  top:1181px;
  width:371px;
  height:68px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1028 {
  position:absolute;
  left:0px;
  top:0px;
  width:371px;
  white-space:nowrap;
}
