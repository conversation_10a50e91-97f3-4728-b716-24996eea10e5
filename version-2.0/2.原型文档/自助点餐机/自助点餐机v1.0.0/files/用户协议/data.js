$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bf),bh,_(bi,bj,bk,bl)),P,_(),bm,_(),bn,bo),_(T,bp,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,bs,bk,bt),t,bu,M,bv,bw,bx,x,_(y,z,A,by),bd,_(be,bz,bg,bz),bA,bB),P,_(),bm,_(),S,[_(T,bC,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,bs,bk,bt),t,bu,M,bv,bw,bx,x,_(y,z,A,by),bd,_(be,bz,bg,bz),bA,bB),P,_(),bm,_())],Q,_(bG,_(bH,bI,bJ,[_(bH,bK,bL,g,bM,[_(bN,bO,bH,bP,bQ,_(bR,k,b,bS,bT,bc),bU,bV)])])),bW,bc,bX,g),_(T,bY,V,W,X,bZ,n,br,ba,bF,bb,bc,s,_(ca,cb,t,cc,bh,_(bi,cd,bk,ce),M,cf,bw,cg,bd,_(be,ch,bg,ci),bA,cj,ck,cl),P,_(),bm,_(),S,[_(T,cm,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(ca,cb,t,cc,bh,_(bi,cd,bk,ce),M,cf,bw,cg,bd,_(be,ch,bg,ci),bA,cj,ck,cl),P,_(),bm,_())],cn,_(co,cp),bX,g),_(T,cq,V,W,X,bZ,n,br,ba,bF,bb,bc,s,_(ca,cr,t,cc,bh,_(bi,cs,bk,ct),bd,_(be,cu,bg,cv),M,cw,bw,cx),P,_(),bm,_(),S,[_(T,cy,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(ca,cr,t,cc,bh,_(bi,cs,bk,ct),bd,_(be,cu,bg,cv),M,cw,bw,cx),P,_(),bm,_())],cn,_(co,cz),bX,g),_(T,cA,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cB,bk,cC),t,bu,bd,_(be,cD,bg,cE)),P,_(),bm,_(),S,[_(T,cF,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cB,bk,cC),t,bu,bd,_(be,cD,bg,cE)),P,_(),bm,_())],bX,g),_(T,cG,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cH,bk,cI),t,bu,bd,_(be,cJ,bg,cK)),P,_(),bm,_(),S,[_(T,cL,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cH,bk,cI),t,bu,bd,_(be,cJ,bg,cK)),P,_(),bm,_())],bX,g),_(T,cM,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cH,bk,cI),t,bu,bd,_(be,cJ,bg,cN)),P,_(),bm,_(),S,[_(T,cO,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cH,bk,cI),t,bu,bd,_(be,cJ,bg,cN)),P,_(),bm,_())],bX,g),_(T,cP,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cH,bk,cI),t,bu,bd,_(be,cJ,bg,cQ)),P,_(),bm,_(),S,[_(T,cR,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cH,bk,cI),t,bu,bd,_(be,cJ,bg,cQ)),P,_(),bm,_())],bX,g),_(T,cS,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cH,bk,cI),t,bu,bd,_(be,cJ,bg,cT)),P,_(),bm,_(),S,[_(T,cU,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cH,bk,cI),t,bu,bd,_(be,cJ,bg,cT)),P,_(),bm,_())],bX,g),_(T,cV,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cH,bk,cI),t,bu,bd,_(be,cJ,bg,cW)),P,_(),bm,_(),S,[_(T,cX,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cH,bk,cI),t,bu,bd,_(be,cJ,bg,cW)),P,_(),bm,_())],bX,g)])),cY,_(cZ,_(l,cZ,n,da,p,Y,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,db,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,dc,x,_(y,z,A,dd),de,_(y,z,A,df),O,dg),P,_(),bm,_(),S,[_(T,dh,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,dc,x,_(y,z,A,dd),de,_(y,z,A,df),O,dg),P,_(),bm,_())],bX,g)]))),di,_(dj,_(dk,dl,dm,_(dk,dn),dp,_(dk,dq)),dr,_(dk,ds),dt,_(dk,du),dv,_(dk,dw),dx,_(dk,dy),dz,_(dk,dA),dB,_(dk,dC),dD,_(dk,dE),dF,_(dk,dG),dH,_(dk,dI),dJ,_(dk,dK),dL,_(dk,dM),dN,_(dk,dO),dP,_(dk,dQ),dR,_(dk,dS),dT,_(dk,dU),dV,_(dk,dW),dX,_(dk,dY),dZ,_(dk,ea)));}; 
var b="url",c="用户协议.html",d="generationDate",e=new Date(1557315597918.57),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="9fbcd231b82a42c4ad2a2acf76b5f248",n="type",o="Axure:Page",p="name",q="用户协议",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="2f66bb3f31634ed699de67c1c0022430",V="label",W="",X="friendlyType",Y="主框架",Z="referenceDiagramObject",ba="styleType",bb="visible",bc=true,bd="location",be="x",bf=10,bg="y",bh="size",bi="width",bj=540,bk="height",bl=805,bm="imageOverrides",bn="masterId",bo="42b294620c2d49c7af5b1798469a7eae",bp="92e121a8d4e64f67a62f2c72efb99d0e",bq="Rectangle",br="vectorShape",bs=538,bt=60,bu="47641f9a00ac465095d6b672bbdffef6",bv="'PingFangSC-Regular', 'PingFang SC'",bw="fontSize",bx="20px",by=0xFFFFFF,bz=12,bA="horizontalAlignment",bB="left",bC="e3ffe99ae7884ed392b60456afcfcddb",bD="isContained",bE="richTextPanel",bF="paragraph",bG="onClick",bH="description",bI="OnClick",bJ="cases",bK="Case 1",bL="isNewIfGroup",bM="actions",bN="action",bO="linkWindow",bP="Open 使用配置 in Current Window",bQ="target",bR="targetType",bS="使用配置.html",bT="includeVariables",bU="linkType",bV="current",bW="tabbable",bX="generateCompound",bY="71f478a2b4764278b605c285739bd8c7",bZ="Paragraph",ca="fontWeight",cb="500",cc="4988d43d80b44008a4a415096f1632af",cd=273,ce=26,cf="'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC'",cg="16px",ch=104,ci=82,cj="center",ck="verticalAlignment",cl="middle",cm="ce36af53e51d4176abe1ebb509414e91",cn="images",co="normal~",cp="images/用户协议/u1173.png",cq="9a2a737f7e0a498ba74198e629111e2e",cr="200",cs=571,ct=153,cu=625,cv=17,cw="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",cx="12px",cy="da19bef7d4014c20882428d4baf48e70",cz="images/用户协议/u1175.png",cA="76391b2d3b2146189e0243af7f82a781",cB=420,cC=14,cD=68,cE=132,cF="7bbb8fedbb4d4f51a1f4974a23bc2170",cG="f70390e49b1b47928fcdbb2acf4d5aeb",cH=447,cI=11,cJ=41,cK=165,cL="c33d2735aef74f70ab17eb37dac055bc",cM="8c98f1e769004ee0b5ffb7c0180f9f8e",cN=192,cO="f42562998fcb4b85897c0e3a158eb055",cP="5f6b6aa8287c4772806b03bc30478ec5",cQ=213,cR="a0ac49a6eba44854aeb2bf23ba0ab18b",cS="c25855a036d94e609280fa6ee50375a1",cT=234,cU="ed0d779fbe7d477cab92c6db888da733",cV="ba256dc971dc46faab584343a7e9f9e2",cW=255,cX="1471f8eee5cb4132935a034cbd1d9fd5",cY="masters",cZ="42b294620c2d49c7af5b1798469a7eae",da="Axure:Master",db="5a1fbc74d2b64be4b44e2ef951181541",dc="0882bfcd7d11450d85d157758311dca5",dd=0x7FF2F2F2,de="borderFill",df=0xFFCCCCCC,dg="1",dh="8523194c36f94eec9e7c0acc0e3eedb6",di="objectPaths",dj="2f66bb3f31634ed699de67c1c0022430",dk="scriptId",dl="u1168",dm="5a1fbc74d2b64be4b44e2ef951181541",dn="u1169",dp="8523194c36f94eec9e7c0acc0e3eedb6",dq="u1170",dr="92e121a8d4e64f67a62f2c72efb99d0e",ds="u1171",dt="e3ffe99ae7884ed392b60456afcfcddb",du="u1172",dv="71f478a2b4764278b605c285739bd8c7",dw="u1173",dx="ce36af53e51d4176abe1ebb509414e91",dy="u1174",dz="9a2a737f7e0a498ba74198e629111e2e",dA="u1175",dB="da19bef7d4014c20882428d4baf48e70",dC="u1176",dD="76391b2d3b2146189e0243af7f82a781",dE="u1177",dF="7bbb8fedbb4d4f51a1f4974a23bc2170",dG="u1178",dH="f70390e49b1b47928fcdbb2acf4d5aeb",dI="u1179",dJ="c33d2735aef74f70ab17eb37dac055bc",dK="u1180",dL="8c98f1e769004ee0b5ffb7c0180f9f8e",dM="u1181",dN="f42562998fcb4b85897c0e3a158eb055",dO="u1182",dP="5f6b6aa8287c4772806b03bc30478ec5",dQ="u1183",dR="a0ac49a6eba44854aeb2bf23ba0ab18b",dS="u1184",dT="c25855a036d94e609280fa6ee50375a1",dU="u1185",dV="ed0d779fbe7d477cab92c6db888da733",dW="u1186",dX="ba256dc971dc46faab584343a7e9f9e2",dY="u1187",dZ="1471f8eee5cb4132935a034cbd1d9fd5",ea="u1188";
return _creator();
})());