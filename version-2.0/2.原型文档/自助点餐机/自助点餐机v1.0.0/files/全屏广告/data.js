$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bh),t,bi,x,_(y,z,A,bj),bk,_(y,z,A,bl),O,bm,bn,_(bo,bp,bq,bp),M,br,bs,_(y,z,A,bt,bu,bv)),P,_(),bw,_(),S,[_(T,bx,V,W,X,null,by,bc,n,bz,ba,bA,bb,bc,s,_(bd,_(be,bf,bg,bh),t,bi,x,_(y,z,A,bj),bk,_(y,z,A,bl),O,bm,bn,_(bo,bp,bq,bp),M,br,bs,_(y,z,A,bt,bu,bv)),P,_(),bw,_())],Q,_(bB,_(bC,bD,bE,[_(bC,bF,bG,g,bH,[_(bI,bJ,bC,bK,bL,_(bM,k,b,bN,bO,bc),bP,bQ)])])),bR,bc,bS,g),_(T,bT,V,W,X,bU,n,Z,ba,bA,bb,bc,s,_(bV,bW,t,bX,bd,_(be,bY,bg,bZ),bn,_(bo,ca,bq,cb),bs,_(y,z,A,cc,bu,bv),M,cd,ce,cf),P,_(),bw,_(),S,[_(T,cg,V,W,X,null,by,bc,n,bz,ba,bA,bb,bc,s,_(bV,bW,t,bX,bd,_(be,bY,bg,bZ),bn,_(bo,ca,bq,cb),bs,_(y,z,A,cc,bu,bv),M,cd,ce,cf),P,_(),bw,_())],ch,_(ci,cj),bS,g)])),ck,_(),cl,_(cm,_(cn,co),cp,_(cn,cq),cr,_(cn,cs),ct,_(cn,cu)));}; 
var b="url",c="全屏广告.html",d="generationDate",e=new Date(1557315595278.41),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="ae6949cfa74640c196c7d9703b2e129f",n="type",o="Axure:Page",p="name",q="全屏广告",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="054120243d0642a2932bfec2d5a1b4c4",V="label",W="",X="friendlyType",Y="Rectangle",Z="vectorShape",ba="styleType",bb="visible",bc=true,bd="size",be="width",bf=540,bg="height",bh=805,bi="0882bfcd7d11450d85d157758311dca5",bj=0x7F999999,bk="borderFill",bl=0xFFCCCCCC,bm="1",bn="location",bo="x",bp=9,bq="y",br="'PingFangSC-Regular', 'PingFang SC'",bs="foreGroundFill",bt=0xFF999999,bu="opacity",bv=1,bw="imageOverrides",bx="ce7b9a7846db45f58cdbaa016a60e8b3",by="isContained",bz="richTextPanel",bA="paragraph",bB="onClick",bC="description",bD="OnClick",bE="cases",bF="Case 1",bG="isNewIfGroup",bH="actions",bI="action",bJ="linkWindow",bK="Open 点餐-4列 in Current Window",bL="target",bM="targetType",bN="点餐-4列.html",bO="includeVariables",bP="linkType",bQ="current",bR="tabbable",bS="generateCompound",bT="2ed57653a6ab4a3fa7a7c070f4a7850b",bU="Paragraph",bV="fontWeight",bW="200",bX="4988d43d80b44008a4a415096f1632af",bY=157,bZ=34,ca=625,cb=17,cc=0xFF1B5C57,cd="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",ce="fontSize",cf="12px",cg="bbabd30dba6a45349c64ead7f0401bbc",ch="images",ci="normal~",cj="images/全屏广告/u220.png",ck="masters",cl="objectPaths",cm="054120243d0642a2932bfec2d5a1b4c4",cn="scriptId",co="u218",cp="ce7b9a7846db45f58cdbaa016a60e8b3",cq="u219",cr="2ed57653a6ab4a3fa7a7c070f4a7850b",cs="u220",ct="bbabd30dba6a45349c64ead7f0401bbc",cu="u221";
return _creator();
})());