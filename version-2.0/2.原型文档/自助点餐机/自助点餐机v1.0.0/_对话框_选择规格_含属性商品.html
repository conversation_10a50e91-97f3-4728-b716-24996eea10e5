<!DOCTYPE html>
<html>
  <head>
    <title>[对话框]选择规格/含属性商品</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/_对话框_选择规格_含属性商品/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/_对话框_选择规格_含属性商品/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Group) -->
      <div id="u563" class="ax_default">

        <!-- Unnamed (Rectangle) -->
        <div id="u564" class="ax_default box_1">
          <div id="u564_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u565" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u566" class="ax_default paragraph">
          <img id="u566_img" class="img " src="images/_对话框_选择规格_含属性商品/u566.png"/>
          <!-- Unnamed () -->
          <div id="u567" class="text" style="visibility: visible;">
            <p><span>酸辣大白菜菜，开口味助消化…</span></p>
          </div>
        </div>

        <!-- 大份 (Rectangle) -->
        <div id="u568" class="ax_default box_2" data-label="大份">
          <div id="u568_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u569" class="text" style="visibility: visible;">
            <p><span>大份</span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u570" class="ax_default paragraph">
          <img id="u570_img" class="img " src="images/_对话框_选择规格_含属性商品/u570.png"/>
          <!-- Unnamed () -->
          <div id="u571" class="text" style="visibility: visible;">
            <p><span>规格(组名一行显示，溢出截断…)</span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u572" class="ax_default paragraph">
          <img id="u572_img" class="img " src="images/_对话框_选择规格_含属性商品/u572.png"/>
          <!-- Unnamed () -->
          <div id="u573" class="text" style="visibility: visible;">
            <p><span>做法</span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u574" class="ax_default paragraph">
          <img id="u574_img" class="img " src="images/_对话框_选择规格_含属性商品/u574.png"/>
          <!-- Unnamed () -->
          <div id="u575" class="text" style="visibility: visible;">
            <p><span>加配菜(多选）</span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u576" class="ax_default paragraph">
          <img id="u576_img" class="img " src="images/_对话框_选择规格_含属性商品/u576.png"/>
          <!-- Unnamed () -->
          <div id="u577" class="text" style="visibility: visible;">
            <p><span>加荤菜(多选，可选）</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u578" class="ax_default box_2">
          <div id="u578_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u579" class="text" style="visibility: visible;">
            <p><span>土豆</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u580" class="ax_default box_2">
          <div id="u580_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u581" class="text" style="visibility: visible;">
            <p><span>清炖</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u582" class="ax_default box_2">
          <div id="u582_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u583" class="text" style="visibility: visible;">
            <p><span>红烧</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u584" class="ax_default box_2">
          <div id="u584_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u585" class="text" style="visibility: visible;">
            <p><span>果木炭烤</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u586" class="ax_default box_2">
          <div id="u586_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u587" class="text" style="visibility: visible;">
            <p><span>炝辣油焖</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u588" class="ax_default box_2">
          <div id="u588_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u589" class="text" style="visibility: visible;">
            <p><span>黄喉</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u590" class="ax_default box_2">
          <div id="u590_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u591" class="text" style="visibility: visible;">
            <p><span>功夫土豆</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u592" class="ax_default box_2">
          <div id="u592_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u593" class="text" style="visibility: visible;">
            <p><span>澄阳湖脆藕</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u594" class="ax_default box_2">
          <div id="u594_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u595" class="text" style="visibility: visible;">
            <p><span>油麦菜&nbsp; ￥99</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u596" class="ax_default box_2">
          <div id="u596_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u597" class="text" style="visibility: visible;">
            <p><span>喔喔家油条豆浆包腊肉&nbsp; ￥2</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u598" class="ax_default box_2">
          <div id="u598_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u599" class="text" style="visibility: visible;">
            <p><span>油麦菜&nbsp; ￥2</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u600" class="ax_default box_2">
          <div id="u600_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u601" class="text" style="visibility: visible;">
            <p><span>油麦菜&nbsp; ￥2</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u602" class="ax_default box_2">
          <div id="u602_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u603" class="text" style="visibility: visible;">
            <p><span>烤肠</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u604" class="ax_default box_2">
          <div id="u604_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u605" class="text" style="visibility: visible;">
            <p><span>腊肉</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u606" class="ax_default box_2">
          <div id="u606_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u607" class="text" style="visibility: visible;">
            <p><span>五花肉</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u608" class="ax_default box_2">
          <div id="u608_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u609" class="text" style="visibility: visible;">
            <p><span>麻辣牛肉</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u610" class="ax_default box_2">
          <div id="u610_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u611" class="text" style="visibility: visible;">
            <p><span>鹅肠(整) ￥99.99</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u612" class="ax_default box_2">
          <div id="u612_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u613" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- 规格属性条 (Paragraph) -->
        <div id="u614" class="ax_default paragraph" data-label="规格属性条">
          <img id="u614_img" class="img " src="images/_对话框_选择规格_含属性商品/规格属性条_u614.png"/>
          <!-- Unnamed () -->
          <div id="u615" class="text" style="visibility: visible;">
            <p><span>请选择规格属性</span></p>
          </div>
        </div>

        <!-- 大份 (Rectangle) -->
        <div id="u616" class="ax_default box_2" data-label="大份">
          <div id="u616_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u617" class="text" style="visibility: visible;">
            <p><span>小份</span></p>
          </div>
        </div>

        <!-- 大份 (Rectangle) -->
        <div id="u618" class="ax_default box_2" data-label="大份">
          <div id="u618_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u619" class="text" style="visibility: visible;">
            <p><span>中份</span></p>
          </div>
        </div>

        <!-- 大份 (Rectangle) -->
        <div id="u620" class="ax_default box_2" data-label="大份">
          <div id="u620_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u621" class="text" style="visibility: visible;">
            <p><span>小朋友份</span></p>
          </div>
        </div>

        <!-- 大份 (Rectangle) -->
        <div id="u622" class="ax_default box_2" data-label="大份">
          <div id="u622_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u623" class="text" style="visibility: visible;">
            <p><span>巨人份</span></p>
          </div>
        </div>

        <!-- 大份 (Rectangle) -->
        <div id="u624" class="ax_default box_2" data-label="大份">
          <div id="u624_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u625" class="text" style="visibility: visible;">
            <p><span>白灼</span></p>
          </div>
        </div>

        <!-- 大份 (Rectangle) -->
        <div id="u626" class="ax_default box_2" data-label="大份">
          <div id="u626_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u627" class="text" style="visibility: visible;">
            <p><span>莴笋</span></p>
          </div>
        </div>

        <!-- 加入订单 (Rectangle) -->
        <div id="u628" class="ax_default ellipse" data-label="加入订单">
          <div id="u628_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u629" class="text" style="visibility: visible;">
            <p><span>确定</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u630" class="ax_default box_1">
        <div id="u630_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u631" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u632" class="ax_default paragraph">
        <img id="u632_img" class="img " src="images/_对话框_选择规格_含属性商品/u566.png"/>
        <!-- Unnamed () -->
        <div id="u633" class="text" style="visibility: visible;">
          <p><span>酸辣大白菜菜，开口味助消化…</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u634" class="ax_default box_2">
        <div id="u634_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u635" class="text" style="visibility: visible;">
          <p><span>大份</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u636" class="ax_default paragraph">
        <img id="u636_img" class="img " src="images/_对话框_选择规格_含属性商品/u570.png"/>
        <!-- Unnamed () -->
        <div id="u637" class="text" style="visibility: visible;">
          <p><span>规格(组名一行显示，溢出截断…)</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u638" class="ax_default box_2">
        <div id="u638_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u639" class="text" style="visibility: visible;">
          <p><span>小份</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u640" class="ax_default box_2">
        <div id="u640_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u641" class="text" style="visibility: visible;">
          <p><span>中份</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u642" class="ax_default box_2">
        <div id="u642_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u643" class="text" style="visibility: visible;">
          <p><span>小朋友份</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u644" class="ax_default box_2">
        <div id="u644_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u645" class="text" style="visibility: visible;">
          <p><span>巨人份</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u646" class="ax_default paragraph">
        <img id="u646_img" class="img " src="images/_对话框_选择规格_含属性商品/u572.png"/>
        <!-- Unnamed () -->
        <div id="u647" class="text" style="visibility: visible;">
          <p><span>做法</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u648" class="ax_default paragraph">
        <img id="u648_img" class="img " src="images/_对话框_选择规格_含属性商品/u574.png"/>
        <!-- Unnamed () -->
        <div id="u649" class="text" style="visibility: visible;">
          <p><span>加配菜(多选）</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u650" class="ax_default paragraph">
        <img id="u650_img" class="img " src="images/_对话框_选择规格_含属性商品/u576.png"/>
        <!-- Unnamed () -->
        <div id="u651" class="text" style="visibility: visible;">
          <p><span>加荤菜(多选，可选）</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u652" class="ax_default box_2">
        <div id="u652_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u653" class="text" style="visibility: visible;">
          <p><span>白灼</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u654" class="ax_default box_2">
        <div id="u654_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u655" class="text" style="visibility: visible;">
          <p><span>清炖￥99.99</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u656" class="ax_default box_2">
        <div id="u656_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u657" class="text" style="visibility: visible;">
          <p><span>红烧</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u658" class="ax_default box_2">
        <div id="u658_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u659" class="text" style="visibility: visible;">
          <p><span>果木炭烤</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u660" class="ax_default box_2">
        <div id="u660_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u661" class="text" style="visibility: visible;">
          <p><span>炝辣油焖</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u662" class="ax_default box_2">
        <div id="u662_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u663" class="text" style="visibility: visible;">
          <p><span>土豆</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u664" class="ax_default box_2">
        <div id="u664_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u665" class="text" style="visibility: visible;">
          <p><span>莴笋</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u666" class="ax_default box_2">
        <div id="u666_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u667" class="text" style="visibility: visible;">
          <p><span>黄喉</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u668" class="ax_default box_2">
        <div id="u668_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u669" class="text" style="visibility: visible;">
          <p><span>功夫土豆</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u670" class="ax_default box_2">
        <div id="u670_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u671" class="text" style="visibility: visible;">
          <p><span>澄阳湖脆藕</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u672" class="ax_default box_2">
        <div id="u672_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u673" class="text" style="visibility: visible;">
          <p><span>油麦菜&nbsp; ￥99</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u674" class="ax_default box_2">
        <div id="u674_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u675" class="text" style="visibility: visible;">
          <p><span>喔喔家油条豆浆包腊肉&nbsp; ￥2</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u676" class="ax_default box_2">
        <div id="u676_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u677" class="text" style="visibility: visible;">
          <p><span>油麦菜&nbsp; ￥2</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u678" class="ax_default box_2">
        <div id="u678_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u679" class="text" style="visibility: visible;">
          <p><span>油麦菜&nbsp; ￥2</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u680" class="ax_default box_2">
        <div id="u680_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u681" class="text" style="visibility: visible;">
          <p><span>烤肠</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u682" class="ax_default box_2">
        <div id="u682_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u683" class="text" style="visibility: visible;">
          <p><span>腊肉</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u684" class="ax_default box_2">
        <div id="u684_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u685" class="text" style="visibility: visible;">
          <p><span>五花肉</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u686" class="ax_default box_2">
        <div id="u686_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u687" class="text" style="visibility: visible;">
          <p><span>鹅肠(整) ￥99.99</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u688" class="ax_default box_2">
        <div id="u688_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u689" class="text" style="visibility: visible;">
          <p><span>生态农场先宰鸭肠(整) ￥99.99</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u690" class="ax_default box_2">
        <div id="u690_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u691" class="text" style="visibility: visible;">
          <p><span>生态农场先宰鸭肠(整) ￥99.99</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u692" class="ax_default box_2">
        <div id="u692_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u693" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u694" class="ax_default paragraph">
        <img id="u694_img" class="img " src="images/_对话框_选择规格_含属性商品/u694.png"/>
        <!-- Unnamed () -->
        <div id="u695" class="text" style="visibility: visible;">
          <p style="font-size:18px;"><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">￥99999.99</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;font-size:12px;">(大份、白灼、土豆、喔喔家油条…</span></p>
        </div>
      </div>

      <!-- 加入订单 (Rectangle) -->
      <div id="u696" class="ax_default ellipse" data-label="加入订单">
        <div id="u696_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u697" class="text" style="visibility: visible;">
          <p><span>确定</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u698" class="ax_default paragraph">
        <img id="u698_img" class="img " src="images/_对话框_选择规格_含属性商品/u698.png"/>
        <!-- Unnamed () -->
        <div id="u699" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">有规格分组时，默认显示名称为规格</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.初始规格项全部未选中状态，点击选中/切换选中内容</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2.规格名称完全显示</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u700" class="ax_default connector">
        <img id="u700_seg0" class="img " src="images/点餐-4列/u557_seg0.png" alt="u700_seg0"/>
        <img id="u700_seg1" class="img " src="images/_对话框_选择规格_含属性商品/u700_seg1.png" alt="u700_seg1"/>
        <img id="u700_seg2" class="img " src="images/_对话框_选择规格_含属性商品/u700_seg2.png" alt="u700_seg2"/>
        <img id="u700_seg3" class="img " src="images/_对话框_选择规格_含属性商品/u700_seg3.png" alt="u700_seg3"/>
        <img id="u700_seg4" class="img " src="images/_对话框_选择规格_含属性商品/u700_seg4.png" alt="u700_seg4"/>
        <!-- Unnamed () -->
        <div id="u701" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u702" class="ax_default paragraph">
        <img id="u702_img" class="img " src="images/_对话框_选择规格_含属性商品/u702.png"/>
        <!-- Unnamed () -->
        <div id="u703" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">有规格的商品/带属性的商品</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">点选规格进入：全部内容未选中状态，可选分组不支持商户后台的默认勾选配置项，当可选分组内的单品可售量＜单品配置的初始量时，置灰不可选</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">已选商品进入：已选项显示为选中状态</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u704" class="ax_default connector">
        <img id="u704_seg0" class="img " src="images/_对话框_选择规格_含属性商品/u704_seg0.png" alt="u704_seg0"/>
        <img id="u704_seg1" class="img " src="images/_对话框_选择规格_含属性商品/u704_seg1.png" alt="u704_seg1"/>
        <img id="u704_seg2" class="img " src="images/_对话框_选择规格_含属性商品/u704_seg2.png" alt="u704_seg2"/>
        <!-- Unnamed () -->
        <div id="u705" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u706" class="ax_default paragraph">
        <img id="u706_img" class="img " src="images/_对话框_选择规格_含属性商品/u706.png"/>
        <!-- Unnamed () -->
        <div id="u707" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">必选、单选的属性组</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.初始未勾选，点击单品内容，切换为已选中状态，显示数量加减按钮(初始数量为单品配置数量)；</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2.添加数量时,（最大量限制优先级为：可销售剩余量＞分组最大数），同时累减可点剩余数量。当可点剩余数量为0时，其他为选项置灰不可选；减掉数量后，解除禁选状态；</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">3.减少数量，同时累加可点剩余数量，到0时，切换为未选中状态</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u708" class="ax_default paragraph">
        <img id="u708_img" class="img " src="images/_对话框_选择规格_含属性商品/u708.png"/>
        <!-- Unnamed () -->
        <div id="u709" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">可选分组单品，不支持重复选择</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.初始未勾选，点击单品内容，切换为已选中状态，当已选数量达设置的可选数量时，其他为选项置灰不可选，减掉数量后，解除禁选状态</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2.再次点击已选单品内容，切换为未选中状态，同时累加可点剩余数量</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u710" class="ax_default connector">
        <img id="u710_seg0" class="img " src="images/_对话框_选择规格_含属性商品/u710_seg0.png" alt="u710_seg0"/>
        <img id="u710_seg1" class="img " src="images/_对话框_选择规格_含属性商品/u710_seg1.png" alt="u710_seg1"/>
        <img id="u710_seg2" class="img " src="images/_对话框_选择规格_含属性商品/u710_seg2.png" alt="u710_seg2"/>
        <!-- Unnamed () -->
        <div id="u711" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u712" class="ax_default paragraph">
        <img id="u712_img" class="img " src="images/_对话框_选择规格_含属性商品/u712.png"/>
        <!-- Unnamed () -->
        <div id="u713" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">已选信息概览</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.点商品进入</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.1初始无价格及选中单品信息</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.2满足设置的选择条件时，显示价格，设置“确定”按钮可点</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2已选商品进入</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">初始已选商品信息；点“确定”保存已选内容并关闭对话框；点关闭/空白区域关闭弹窗，撤销编辑内容并关闭对话框</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u714" class="ax_default connector">
        <img id="u714_seg0" class="img " src="images/点餐-4列/u557_seg0.png" alt="u714_seg0"/>
        <img id="u714_seg1" class="img " src="images/_对话框_选择规格_含属性商品/u714_seg1.png" alt="u714_seg1"/>
        <img id="u714_seg2" class="img " src="images/_对话框_选择规格_含属性商品/u714_seg2.png" alt="u714_seg2"/>
        <img id="u714_seg3" class="img " src="images/_对话框_选择规格_含属性商品/u714_seg3.png" alt="u714_seg3"/>
        <img id="u714_seg4" class="img " src="images/_对话框_选择规格_含属性商品/u714_seg4.png" alt="u714_seg4"/>
        <!-- Unnamed () -->
        <div id="u715" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 关闭内部框架 (Rectangle) -->
      <div id="u716" class="ax_default ellipse" data-label="关闭内部框架">
        <div id="u716_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u717" class="text" style="visibility: visible;">
          <p><span>×</span></p>
        </div>
      </div>

      <!-- 关闭内部框架 (Rectangle) -->
      <div id="u718" class="ax_default ellipse" data-label="关闭内部框架">
        <div id="u718_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u719" class="text" style="visibility: visible;">
          <p><span>×</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
