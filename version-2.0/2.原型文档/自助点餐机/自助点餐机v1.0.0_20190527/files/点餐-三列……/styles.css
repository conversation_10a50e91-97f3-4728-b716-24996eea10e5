body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1138px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u1211_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1211 {
  position:absolute;
  left:10px;
  top:10px;
  width:540px;
  height:805px;
}
#u1212 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1213_div {
  position:absolute;
  left:0px;
  top:0px;
  width:427px;
  height:643px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1213 {
  position:absolute;
  left:122px;
  top:151px;
  width:427px;
  height:643px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1214 {
  position:absolute;
  left:2px;
  top:314px;
  width:423px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1215_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1215 {
  position:absolute;
  left:151px;
  top:164px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1216 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1217_img {
  position:absolute;
  left:0px;
  top:0px;
  width:265px;
  height:115px;
}
#u1217 {
  position:absolute;
  left:675px;
  top:294px;
  width:265px;
  height:115px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-style:normal;
}
#u1218 {
  position:absolute;
  left:0px;
  top:0px;
  width:265px;
  word-wrap:break-word;
}
#u1219_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1219 {
  position:absolute;
  left:151px;
  top:323px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1220 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1221_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1221 {
  position:absolute;
  left:769px;
  top:511px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1222 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1223_div {
  position:absolute;
  left:0px;
  top:0px;
  width:428px;
  height:20px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
#u1223 {
  position:absolute;
  left:121px;
  top:131px;
  width:428px;
  height:20px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
#u1224 {
  position:absolute;
  left:2px;
  top:2px;
  width:424px;
  word-wrap:break-word;
}
#u1225_div {
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:19px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#000000;
}
#u1225 {
  position:absolute;
  left:643px;
  top:151px;
  width:33px;
  height:19px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#000000;
}
#u1226 {
  position:absolute;
  left:2px;
  top:2px;
  width:29px;
  word-wrap:break-word;
}
#u1227_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1227 {
  position:absolute;
  left:151px;
  top:482px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1228 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1229_div {
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:19px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#000000;
}
#u1229 {
  position:absolute;
  left:681px;
  top:151px;
  width:33px;
  height:19px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#000000;
}
#u1230 {
  position:absolute;
  left:2px;
  top:2px;
  width:29px;
  word-wrap:break-word;
}
#u1231_div {
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:19px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#000000;
}
#u1231 {
  position:absolute;
  left:756px;
  top:151px;
  width:58px;
  height:19px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#000000;
}
#u1232 {
  position:absolute;
  left:2px;
  top:2px;
  width:54px;
  word-wrap:break-word;
}
#u1233_div {
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:19px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#000000;
}
#u1233 {
  position:absolute;
  left:718px;
  top:151px;
  width:33px;
  height:19px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#000000;
}
#u1234 {
  position:absolute;
  left:2px;
  top:2px;
  width:29px;
  word-wrap:break-word;
}
#u1235_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1235 {
  position:absolute;
  left:151px;
  top:639px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1236 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1237_img {
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:17px;
}
#u1237 {
  position:absolute;
  left:33px;
  top:94px;
  width:205px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1238 {
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  white-space:nowrap;
}
#u1239 {
  position:absolute;
  left:11px;
  top:132px;
  width:115px;
  height:688px;
}
#u1240_img {
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:60px;
}
#u1240 {
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1241 {
  position:absolute;
  left:2px;
  top:21px;
  width:106px;
  word-wrap:break-word;
}
#u1242_img {
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:60px;
}
#u1242 {
  position:absolute;
  left:0px;
  top:60px;
  width:110px;
  height:60px;
  text-align:left;
}
#u1243 {
  position:absolute;
  left:2px;
  top:21px;
  width:106px;
  word-wrap:break-word;
}
#u1244_img {
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:60px;
}
#u1244 {
  position:absolute;
  left:0px;
  top:120px;
  width:110px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1245 {
  position:absolute;
  left:2px;
  top:21px;
  width:106px;
  word-wrap:break-word;
}
#u1246_img {
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:60px;
}
#u1246 {
  position:absolute;
  left:0px;
  top:180px;
  width:110px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1247 {
  position:absolute;
  left:2px;
  top:10px;
  width:106px;
  word-wrap:break-word;
}
#u1248_img {
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:60px;
}
#u1248 {
  position:absolute;
  left:0px;
  top:240px;
  width:110px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1249 {
  position:absolute;
  left:2px;
  top:21px;
  width:106px;
  word-wrap:break-word;
}
#u1250_img {
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:60px;
}
#u1250 {
  position:absolute;
  left:0px;
  top:300px;
  width:110px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1251 {
  position:absolute;
  left:2px;
  top:21px;
  width:106px;
  word-wrap:break-word;
}
#u1252_img {
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:60px;
}
#u1252 {
  position:absolute;
  left:0px;
  top:360px;
  width:110px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1253 {
  position:absolute;
  left:2px;
  top:21px;
  width:106px;
  word-wrap:break-word;
}
#u1254_img {
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:263px;
}
#u1254 {
  position:absolute;
  left:0px;
  top:420px;
  width:110px;
  height:263px;
}
#u1255 {
  position:absolute;
  left:2px;
  top:124px;
  width:106px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1256_img {
  position:absolute;
  left:0px;
  top:0px;
  width:539px;
  height:2px;
}
#u1256 {
  position:absolute;
  left:11px;
  top:132px;
  width:538px;
  height:1px;
}
#u1257 {
  position:absolute;
  left:2px;
  top:-8px;
  width:534px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1259_div {
  position:absolute;
  left:0px;
  top:0px;
  width:538px;
  height:60px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u1259 {
  position:absolute;
  left:11px;
  top:11px;
  width:538px;
  height:60px;
  font-size:20px;
}
#u1260 {
  position:absolute;
  left:2px;
  top:16px;
  width:534px;
  word-wrap:break-word;
}
#u1262 {
  position:absolute;
  left:11px;
  top:793px;
  width:544px;
  height:65px;
}
#u1263_img {
  position:absolute;
  left:0px;
  top:0px;
  width:421px;
  height:60px;
}
#u1263 {
  position:absolute;
  left:0px;
  top:0px;
  width:421px;
  height:60px;
}
#u1264 {
  position:absolute;
  left:2px;
  top:22px;
  width:417px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1265_img {
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:60px;
}
#u1265 {
  position:absolute;
  left:421px;
  top:0px;
  width:118px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u1266 {
  position:absolute;
  left:2px;
  top:18px;
  width:114px;
  word-wrap:break-word;
}
#u1267_img {
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:33px;
}
#u1267 {
  position:absolute;
  left:28px;
  top:807px;
  width:39px;
  height:33px;
}
#u1268 {
  position:absolute;
  left:2px;
  top:8px;
  width:35px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1269_img {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1269 {
  position:absolute;
  left:57px;
  top:792px;
  width:30px;
  height:30px;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
}
#u1270 {
  position:absolute;
  left:2px;
  top:8px;
  width:26px;
  word-wrap:break-word;
}
#u1271_img {
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:40px;
}
#u1271 {
  position:absolute;
  left:97px;
  top:804px;
  width:103px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:28px;
}
#u1272 {
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  white-space:nowrap;
}
#u1273_img {
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:20px;
}
#u1273 {
  position:absolute;
  left:210px;
  top:820px;
  width:106px;
  height:20px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:14px;
}
#u1274 {
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  white-space:nowrap;
}
#u1275_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:2px;
}
#u1275 {
  position:absolute;
  left:217px;
  top:830px;
  width:99px;
  height:1px;
}
#u1276 {
  position:absolute;
  left:2px;
  top:-8px;
  width:95px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1277_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1277 {
  position:absolute;
  left:286px;
  top:164px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1278 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1279_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1279 {
  position:absolute;
  left:286px;
  top:323px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1280 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1281_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1281 {
  position:absolute;
  left:904px;
  top:511px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1282 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1283_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1283 {
  position:absolute;
  left:286px;
  top:482px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1284 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1285_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1285 {
  position:absolute;
  left:420px;
  top:164px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1286 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1287_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1287 {
  position:absolute;
  left:420px;
  top:323px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1288 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1289_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1289 {
  position:absolute;
  left:1038px;
  top:511px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1290 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1291_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1291 {
  position:absolute;
  left:420px;
  top:482px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1292 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1293_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:23px;
}
#u1293 {
  position:absolute;
  left:163px;
  top:274px;
  width:77px;
  height:23px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1294 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1295_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:19px;
}
#u1295 {
  position:absolute;
  left:432px;
  top:274px;
  width:77px;
  height:19px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1296 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1297_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:23px;
}
#u1297 {
  position:absolute;
  left:298px;
  top:274px;
  width:77px;
  height:23px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1298 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1299_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:23px;
}
#u1299 {
  position:absolute;
  left:163px;
  top:433px;
  width:77px;
  height:23px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1300 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1301_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:19px;
}
#u1301 {
  position:absolute;
  left:432px;
  top:433px;
  width:77px;
  height:19px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1302 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1303_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:23px;
}
#u1303 {
  position:absolute;
  left:298px;
  top:433px;
  width:77px;
  height:23px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1304 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1305_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:23px;
}
#u1305 {
  position:absolute;
  left:163px;
  top:592px;
  width:77px;
  height:23px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1306 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1307_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:19px;
}
#u1307 {
  position:absolute;
  left:432px;
  top:592px;
  width:77px;
  height:19px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1308 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1309_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:23px;
}
#u1309 {
  position:absolute;
  left:298px;
  top:592px;
  width:77px;
  height:23px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1310 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1311_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1311 {
  position:absolute;
  left:286px;
  top:639px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1312 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1313_img {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u1313 {
  position:absolute;
  left:420px;
  top:639px;
  width:100px;
  height:100px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1314 {
  position:absolute;
  left:2px;
  top:42px;
  width:96px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1315_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:23px;
}
#u1315 {
  position:absolute;
  left:163px;
  top:749px;
  width:77px;
  height:23px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1316 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1317_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:19px;
}
#u1317 {
  position:absolute;
  left:432px;
  top:749px;
  width:77px;
  height:19px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1318 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1319_img {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:23px;
}
#u1319 {
  position:absolute;
  left:298px;
  top:749px;
  width:77px;
  height:23px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1320 {
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  word-wrap:break-word;
}
#u1321_div {
  position:absolute;
  left:0px;
  top:0px;
  width:417px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1321 {
  position:absolute;
  left:125px;
  top:774px;
  width:417px;
  height:20px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1322 {
  position:absolute;
  left:2px;
  top:2px;
  width:413px;
  word-wrap:break-word;
}
