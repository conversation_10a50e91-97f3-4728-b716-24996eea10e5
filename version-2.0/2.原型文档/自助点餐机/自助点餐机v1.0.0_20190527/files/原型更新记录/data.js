$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bh),bi,_(bj,bk,bl,bm)),P,_(),bn,_(),S,[_(T,bo,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(bd,_(be,bs,bg,bt),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bA,bl,bB)),P,_(),bn,_(),S,[_(T,bC,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,bs,bg,bt),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bA,bl,bB)),P,_(),bn,_())],bG,_(bH,bI)),_(T,bJ,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(bd,_(be,bK,bg,bt),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bs,bl,bB),bL,bM),P,_(),bn,_(),S,[_(T,bN,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,bK,bg,bt),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bs,bl,bB),bL,bM),P,_(),bn,_())],bG,_(bH,bO)),_(T,bP,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(bd,_(be,bQ,bg,bt),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bR,bl,bB),bL,bM),P,_(),bn,_(),S,[_(T,bS,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,bQ,bg,bt),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bR,bl,bB),bL,bM),P,_(),bn,_())],bG,_(bH,bT)),_(T,bU,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(bd,_(be,bs,bg,bm),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bA,bl,bV)),P,_(),bn,_(),S,[_(T,bW,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,bs,bg,bm),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bA,bl,bV)),P,_(),bn,_())],bG,_(bH,bX)),_(T,bY,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(bd,_(be,bK,bg,bm),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bs,bl,bV),bL,bM),P,_(),bn,_(),S,[_(T,bZ,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,bK,bg,bm),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bs,bl,bV),bL,bM),P,_(),bn,_())],bG,_(bH,ca)),_(T,cb,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(bd,_(be,bQ,bg,bm),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bR,bl,bV),bL,bM),P,_(),bn,_(),S,[_(T,cc,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,bQ,bg,bm),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bR,bl,bV),bL,bM),P,_(),bn,_())],bG,_(bH,cd)),_(T,ce,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(bd,_(be,bs,bg,cf),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bA,bl,cf)),P,_(),bn,_(),S,[_(T,cg,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,bs,bg,cf),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bA,bl,cf)),P,_(),bn,_())],bG,_(bH,ch)),_(T,ci,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(bd,_(be,bK,bg,cf),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bs,bl,cf),bL,bM),P,_(),bn,_(),S,[_(T,cj,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,bK,bg,cf),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bs,bl,cf),bL,bM),P,_(),bn,_())],bG,_(bH,ck)),_(T,cl,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(bd,_(be,bQ,bg,cf),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bR,bl,cf),bL,bM),P,_(),bn,_(),S,[_(T,cm,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,bQ,bg,cf),t,bu,M,bv,bw,bx,by,_(y,z,A,bz),bi,_(bj,bR,bl,cf),bL,bM),P,_(),bn,_())],bG,_(bH,cn)),_(T,co,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(cp,cq,bd,_(be,bs,bg,cf),t,bu,M,cr,bw,bx,by,_(y,z,A,bz)),P,_(),bn,_(),S,[_(T,cs,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(cp,cq,bd,_(be,bs,bg,cf),t,bu,M,cr,bw,bx,by,_(y,z,A,bz)),P,_(),bn,_())],bG,_(bH,ch)),_(T,ct,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(cp,cq,bd,_(be,bK,bg,cf),t,bu,M,cr,bw,bx,by,_(y,z,A,bz),bi,_(bj,bs,bl,bA)),P,_(),bn,_(),S,[_(T,cu,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(cp,cq,bd,_(be,bK,bg,cf),t,bu,M,cr,bw,bx,by,_(y,z,A,bz),bi,_(bj,bs,bl,bA)),P,_(),bn,_())],bG,_(bH,ck)),_(T,cv,V,bp,X,bq,n,br,ba,br,bb,bc,s,_(cp,cq,bd,_(be,bQ,bg,cf),t,bu,M,cr,bw,bx,by,_(y,z,A,bz),bi,_(bj,bR,bl,bA)),P,_(),bn,_(),S,[_(T,cw,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(cp,cq,bd,_(be,bQ,bg,cf),t,bu,M,cr,bw,bx,by,_(y,z,A,bz),bi,_(bj,bR,bl,bA)),P,_(),bn,_())],bG,_(bH,cn))]),_(T,cx,V,bp,X,cy,n,cz,ba,cz,bb,bc,s,_(bd,_(be,cA,bg,cB),t,bu,bi,_(bj,bk,bl,cC),M,bv,bw,bx,by,_(y,z,A,bz)),P,_(),bn,_(),S,[_(T,cD,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,cA,bg,cB),t,bu,bi,_(bj,bk,bl,cC),M,bv,bw,bx,by,_(y,z,A,bz)),P,_(),bn,_())],cE,g),_(T,cF,V,bp,X,cy,n,cz,ba,cz,bb,bc,s,_(bd,_(be,bf,bg,cG),t,bu,bi,_(bj,bk,bl,cH),M,bv,bw,bx,by,_(y,z,A,bz),bL,bM),P,_(),bn,_(),S,[_(T,cI,V,bp,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bd,_(be,bf,bg,cG),t,bu,bi,_(bj,bk,bl,cH),M,bv,bw,bx,by,_(y,z,A,bz),bL,bM),P,_(),bn,_())],cE,g)])),cJ,_(),cK,_(cL,_(cM,cN),cO,_(cM,cP),cQ,_(cM,cR),cS,_(cM,cT),cU,_(cM,cV),cW,_(cM,cX),cY,_(cM,cZ),da,_(cM,db),dc,_(cM,dd),de,_(cM,df),dg,_(cM,dh),di,_(cM,dj),dk,_(cM,dl),dm,_(cM,dn),dp,_(cM,dq),dr,_(cM,ds),dt,_(cM,du),dv,_(cM,dw),dx,_(cM,dy),dz,_(cM,dA),dB,_(cM,dC),dD,_(cM,dE),dF,_(cM,dG),dH,_(cM,dI),dJ,_(cM,dK),dL,_(cM,dM),dN,_(cM,dO),dP,_(cM,dQ),dR,_(cM,dS)));}; 
var b="url",c="原型更新记录.html",d="generationDate",e=new Date(1558951317334.32),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="a9b4ba638ba44b5fbbd17f7f69380c47",n="type",o="Axure:Page",p="name",q="原型更新记录",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="7452d97f3aa64ff592266161c9b67ef7",V="label",W="clsuter",X="friendlyType",Y="Table",Z="table",ba="styleType",bb="visible",bc=true,bd="size",be="width",bf=774,bg="height",bh=132,bi="location",bj="x",bk=25,bl="y",bm=38,bn="imageOverrides",bo="ef98200beea44a95872bba4cea71a176",bp="",bq="Table Cell",br="tableCell",bs=140,bt=30,bu="33ea2511485c479dbf973af3302f2352",bv="'PingFangSC-Regular', 'PingFang SC'",bw="fontSize",bx="12px",by="borderFill",bz=0xFFE4E4E4,bA=0,bB=64,bC="33cb594a39d144fea414820babb9e268",bD="isContained",bE="richTextPanel",bF="paragraph",bG="images",bH="normal~",bI="images/原型更新记录/u1194.png",bJ="62b587c36f794fb7b2e57c6733905617",bK=227,bL="horizontalAlignment",bM="left",bN="3db995c0310e4e1292aa39577d85c797",bO="images/原型更新记录/u1196.png",bP="3298e9b5db6d43dd8b94fdd0ed08969f",bQ=407,bR=367,bS="4e20ca10c3bb4b8ea058787136393f54",bT="images/原型更新记录/u1198.png",bU="ad90fb039aeb4efbab7f6004e683b473",bV=94,bW="5a48e7d9b5fd459ea5c4390cc00da412",bX="images/原型更新记录/u1200.png",bY="d0e77bec1aac4eabacf06686237e9cf3",bZ="672ca81de24c4d5bb95150d482037e39",ca="images/原型更新记录/u1202.png",cb="38ac2d904fd0411088252f63366e595a",cc="207c216878bf48d4b6e22004bf961a85",cd="images/原型更新记录/u1204.png",ce="565df7942551464a9e4f0e88303b882e",cf=32,cg="844f9c86dc714c2794197d989c8566c5",ch="images/原型更新记录/u1182.png",ci="6f49bb3322034fdfb951ea6616a40383",cj="1112b6cd4aac4207a3034fce13e1e7e6",ck="images/原型更新记录/u1184.png",cl="e960b797ff1243249be40a586a5c85b1",cm="44421b74bfb84d7883f9e14006195586",cn="images/原型更新记录/u1186.png",co="ecb6048697ea4ebc908afd7cb8360769",cp="fontWeight",cq="650",cr="'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC'",cs="fb33aa7185094a6cb7a7b5962465977c",ct="3117bf55fa85450e8761c0776bec203e",cu="275ec1c48e4f48799ddbd4b00b0952b2",cv="e552e68b5721469b9867c0642d861cce",cw="3bf99443f8d64fa2af3d3d627aea2f9d",cx="6f5289a53b6e4392afb68937dbaab374",cy="Rectangle",cz="vectorShape",cA=141,cB=68,cC=102,cD="70031f564278421b955dffe22b69eb6f",cE="generateCompound",cF="39c5ed533da8433f9d085d1a6e97627b",cG=34,cH=69,cI="9c25d4eb79494d45bc2149473061f352",cJ="masters",cK="objectPaths",cL="7452d97f3aa64ff592266161c9b67ef7",cM="scriptId",cN="u1181",cO="ecb6048697ea4ebc908afd7cb8360769",cP="u1182",cQ="fb33aa7185094a6cb7a7b5962465977c",cR="u1183",cS="3117bf55fa85450e8761c0776bec203e",cT="u1184",cU="275ec1c48e4f48799ddbd4b00b0952b2",cV="u1185",cW="e552e68b5721469b9867c0642d861cce",cX="u1186",cY="3bf99443f8d64fa2af3d3d627aea2f9d",cZ="u1187",da="565df7942551464a9e4f0e88303b882e",db="u1188",dc="844f9c86dc714c2794197d989c8566c5",dd="u1189",de="6f49bb3322034fdfb951ea6616a40383",df="u1190",dg="1112b6cd4aac4207a3034fce13e1e7e6",dh="u1191",di="e960b797ff1243249be40a586a5c85b1",dj="u1192",dk="44421b74bfb84d7883f9e14006195586",dl="u1193",dm="ef98200beea44a95872bba4cea71a176",dn="u1194",dp="33cb594a39d144fea414820babb9e268",dq="u1195",dr="62b587c36f794fb7b2e57c6733905617",ds="u1196",dt="3db995c0310e4e1292aa39577d85c797",du="u1197",dv="3298e9b5db6d43dd8b94fdd0ed08969f",dw="u1198",dx="4e20ca10c3bb4b8ea058787136393f54",dy="u1199",dz="ad90fb039aeb4efbab7f6004e683b473",dA="u1200",dB="5a48e7d9b5fd459ea5c4390cc00da412",dC="u1201",dD="d0e77bec1aac4eabacf06686237e9cf3",dE="u1202",dF="672ca81de24c4d5bb95150d482037e39",dG="u1203",dH="38ac2d904fd0411088252f63366e595a",dI="u1204",dJ="207c216878bf48d4b6e22004bf961a85",dK="u1205",dL="6f5289a53b6e4392afb68937dbaab374",dM="u1206",dN="70031f564278421b955dffe22b69eb6f",dO="u1207",dP="39c5ed533da8433f9d085d1a6e97627b",dQ="u1208",dR="9c25d4eb79494d45bc2149473061f352",dS="u1209";
return _creator();
})());