$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bl)),P,_(),bn,_(),S,[_(T,bo,V,W,X,null,bp,bc,n,bq,ba,br,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bl)),P,_(),bn,_())],bs,_(bt,bu))])),bv,_(),bw,_(bx,_(by,bz),bA,_(by,bB)));}; 
var b="url",c="点餐机主流程图.html",d="generationDate",e=new Date(1558951315079.43),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="33240016d58e4293a231f0d1ba89195d",n="type",o="Axure:Page",p="name",q="点餐机主流程图",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="7f67b8f283ab4d3f8e3414e05664e752",V="label",W="",X="friendlyType",Y="Image",Z="imageBox",ba="styleType",bb="visible",bc=true,bd="75a91ee5b9d042cfa01b8d565fe289c0",be="size",bf="width",bg=995,bh="height",bi=838,bj="location",bk="x",bl=10,bm="y",bn="imageOverrides",bo="0f84c2787f6a45a4ac1a3ebf32e4c07b",bp="isContained",bq="richTextPanel",br="paragraph",bs="images",bt="normal~",bu="images/点餐机主流程图/u140.png",bv="masters",bw="objectPaths",bx="7f67b8f283ab4d3f8e3414e05664e752",by="scriptId",bz="u140",bA="0f84c2787f6a45a4ac1a3ebf32e4c07b",bB="u141";
return _creator();
})());