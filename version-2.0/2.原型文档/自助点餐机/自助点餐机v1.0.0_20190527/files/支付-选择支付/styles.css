body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1732px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u853_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u853 {
  position:absolute;
  left:10px;
  top:10px;
  width:540px;
  height:805px;
}
#u854 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u855_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:238px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u855 {
  position:absolute;
  left:10px;
  top:362px;
  width:540px;
  height:238px;
}
#u856 {
  position:absolute;
  left:2px;
  top:111px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u857 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u858_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u858 {
  position:absolute;
  left:602px;
  top:97px;
  width:478px;
  height:257px;
}
#u859 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u860_img {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:33px;
}
#u860 {
  position:absolute;
  left:789px;
  top:114px;
  width:97px;
  height:33px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
}
#u861 {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  white-space:nowrap;
}
#u862_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u862 {
  position:absolute;
  left:650px;
  top:277px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u863 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u864_img {
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:17px;
}
#u864 {
  position:absolute;
  left:652px;
  top:184px;
  width:133px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u865 {
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  white-space:nowrap;
}
#u866_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u866 {
  position:absolute;
  left:853px;
  top:277px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u867 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u868_img {
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:17px;
}
#u868 {
  position:absolute;
  left:208px;
  top:204px;
  width:112px;
  height:17px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u869 {
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  word-wrap:break-word;
}
#u870_img {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:8px;
}
#u870 {
  position:absolute;
  left:234px;
  top:221px;
  width:53px;
  height:8px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:6px;
  text-align:center;
}
#u871 {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  word-wrap:break-word;
}
#u872_div {
  position:absolute;
  left:0px;
  top:0px;
  width:538px;
  height:60px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:left;
}
#u872 {
  position:absolute;
  left:12px;
  top:12px;
  width:538px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:left;
}
#u873 {
  position:absolute;
  left:2px;
  top:16px;
  width:534px;
  word-wrap:break-word;
}
#u874_img {
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:17px;
}
#u874 {
  position:absolute;
  left:185px;
  top:34px;
  width:158px;
  height:17px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u875 {
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  white-space:nowrap;
}
#u876 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u877_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u877 {
  position:absolute;
  left:602px;
  top:374px;
  width:478px;
  height:257px;
}
#u878 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u879_img {
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:17px;
}
#u879 {
  position:absolute;
  left:780px;
  top:499px;
  width:133px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u880 {
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  white-space:nowrap;
}
#u881_div {
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u881 {
  position:absolute;
  left:849px;
  top:562px;
  width:82px;
  height:31px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u882 {
  position:absolute;
  left:2px;
  top:7px;
  width:78px;
  word-wrap:break-word;
}
#u883_img {
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:53px;
}
#u883 {
  position:absolute;
  left:792px;
  top:416px;
  width:117px;
  height:53px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  text-align:center;
}
#u884 {
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  word-wrap:break-word;
}
#u885_img {
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:17px;
}
#u885 {
  position:absolute;
  left:783px;
  top:569px;
  width:56px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:center;
}
#u886 {
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  white-space:nowrap;
}
#u887 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u888_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u888 {
  position:absolute;
  left:602px;
  top:669px;
  width:478px;
  height:257px;
}
#u889 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u890_img {
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:17px;
}
#u890 {
  position:absolute;
  left:640px;
  top:789px;
  width:157px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u891 {
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  white-space:nowrap;
}
#u892_img {
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:53px;
}
#u892 {
  position:absolute;
  left:765px;
  top:704px;
  width:171px;
  height:53px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  text-align:center;
}
#u893 {
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  word-wrap:break-word;
}
#u894_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u894 {
  position:absolute;
  left:650px;
  top:842px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u895 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u896_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u896 {
  position:absolute;
  left:853px;
  top:842px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u897 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u898 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u899_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u899 {
  position:absolute;
  left:602px;
  top:978px;
  width:478px;
  height:257px;
}
#u900 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u901_img {
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  height:17px;
}
#u901 {
  position:absolute;
  left:754px;
  top:1076px;
  width:229px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u902 {
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  white-space:nowrap;
}
#u903_img {
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  height:53px;
}
#u903 {
  position:absolute;
  left:753px;
  top:1013px;
  width:195px;
  height:53px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u904 {
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  word-wrap:break-word;
}
#u905_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u905 {
  position:absolute;
  left:652px;
  top:1152px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u906 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u907_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u907 {
  position:absolute;
  left:866px;
  top:1152px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u908 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u909_div {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u909 {
  position:absolute;
  left:31px;
  top:404px;
  width:97px;
  height:38px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u909_div.selected {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u909.selected {
}
#u909_div.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u909.disabled {
}
#u910 {
  position:absolute;
  left:2px;
  top:10px;
  width:93px;
  word-wrap:break-word;
}
#u911_div {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u911 {
  position:absolute;
  left:31px;
  top:441px;
  width:97px;
  height:38px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u912 {
  position:absolute;
  left:2px;
  top:10px;
  width:93px;
  word-wrap:break-word;
}
#u913 {
  position:absolute;
  left:165px;
  top:399px;
  width:360px;
  height:201px;
  overflow:hidden;
}
#u913_state0 {
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:201px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
}
#u913_state0_content {
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u914_div {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:108px;
  background:inherit;
  background-color:rgba(0, 0, 0, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u914 {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:108px;
}
#u915 {
  position:absolute;
  left:2px;
  top:46px;
  width:104px;
  visibility:hidden;
  word-wrap:break-word;
}
#u916_img {
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:16px;
}
#u916 {
  position:absolute;
  left:118px;
  top:6px;
  width:101px;
  height:16px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:6px;
}
#u917 {
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  word-wrap:break-word;
}
#u918_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:105px;
  height:6px;
}
#u918 {
  position:absolute;
  left:3px;
  top:35px;
  width:102px;
  height:3px;
}
#u919 {
  position:absolute;
  left:2px;
  top:-6px;
  width:98px;
  visibility:hidden;
  word-wrap:break-word;
}
#u920_img {
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:16px;
}
#u920 {
  position:absolute;
  left:250px;
  top:6px;
  width:85px;
  height:16px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u921 {
  position:absolute;
  left:2px;
  top:0px;
  width:81px;
  word-wrap:break-word;
}
#u913_state1 {
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:201px;
  visibility:hidden;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
}
#u913_state1_content {
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u922_div {
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:112px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#999999;
}
#u922 {
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:112px;
  color:#999999;
}
#u923 {
  position:absolute;
  left:2px;
  top:47px;
  width:108px;
  word-wrap:break-word;
}
#u924_img {
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:11px;
}
#u924 {
  position:absolute;
  left:16px;
  top:122px;
  width:87px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-style:normal;
  font-size:8px;
  text-align:center;
}
#u925 {
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  white-space:nowrap;
}
#u926_div {
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:112px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#999999;
}
#u926 {
  position:absolute;
  left:136px;
  top:0px;
  width:112px;
  height:112px;
  color:#999999;
}
#u927 {
  position:absolute;
  left:2px;
  top:47px;
  width:108px;
  word-wrap:break-word;
}
#u928_img {
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:11px;
}
#u928 {
  position:absolute;
  left:149px;
  top:122px;
  width:87px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-style:normal;
  font-size:8px;
  text-align:center;
}
#u929 {
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  white-space:nowrap;
}
#u930_div {
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u930 {
  position:absolute;
  left:266px;
  top:87px;
  width:82px;
  height:36px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u931 {
  position:absolute;
  left:2px;
  top:10px;
  width:78px;
  word-wrap:break-word;
}
#u913_state2 {
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:201px;
  visibility:hidden;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
}
#u913_state2_content {
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u932 {
  position:absolute;
  left:42px;
  top:19px;
  width:31px;
  height:30px;
}
#u932_input {
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u933_img {
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:11px;
}
#u933 {
  position:absolute;
  left:42px;
  top:0px;
  width:121px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
}
#u934 {
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  word-wrap:break-word;
}
#u935 {
  position:absolute;
  left:83px;
  top:19px;
  width:31px;
  height:30px;
}
#u935_input {
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u936 {
  position:absolute;
  left:124px;
  top:19px;
  width:31px;
  height:30px;
}
#u936_input {
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u937 {
  position:absolute;
  left:172px;
  top:19px;
  width:31px;
  height:30px;
}
#u937_input {
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u938 {
  position:absolute;
  left:213px;
  top:19px;
  width:31px;
  height:30px;
}
#u938_input {
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u939 {
  position:absolute;
  left:254px;
  top:19px;
  width:31px;
  height:30px;
}
#u939_input {
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u940_div {
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:126px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  border:none;
  border-radius:1px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u940 {
  position:absolute;
  left:35px;
  top:59px;
  width:260px;
  height:126px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u941 {
  position:absolute;
  left:2px;
  top:54px;
  width:256px;
  word-wrap:break-word;
}
#u942_img {
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:44px;
}
#u942 {
  position:absolute;
  left:285px;
  top:2px;
  width:75px;
  height:44px;
}
#u943 {
  position:absolute;
  left:2px;
  top:14px;
  width:71px;
  visibility:hidden;
  word-wrap:break-word;
}
#u944_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:83px;
}
#u944 {
  position:absolute;
  left:324px;
  top:-14px;
  width:1px;
  height:82px;
  -webkit-transform:rotate(300deg);
  -moz-transform:rotate(300deg);
  -ms-transform:rotate(300deg);
  transform:rotate(300deg);
}
#u945 {
  position:absolute;
  left:2px;
  top:33px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u946_img {
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:36px;
}
#u946 {
  position:absolute;
  left:309px;
  top:3px;
  width:47px;
  height:36px;
  -webkit-transform:rotate(30deg);
  -moz-transform:rotate(30deg);
  -ms-transform:rotate(30deg);
  transform:rotate(30deg);
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  text-align:center;
}
#u947 {
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  word-wrap:break-word;
}
#u913_state3 {
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:201px;
  visibility:hidden;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
}
#u913_state3_content {
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u948_div {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:108px;
  background:inherit;
  background-color:rgba(0, 0, 0, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u948 {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:108px;
}
#u949 {
  position:absolute;
  left:2px;
  top:46px;
  width:104px;
  visibility:hidden;
  word-wrap:break-word;
}
#u950_img {
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:8px;
}
#u950 {
  position:absolute;
  left:118px;
  top:6px;
  width:101px;
  height:8px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:6px;
}
#u951 {
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  word-wrap:break-word;
}
#u952_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:105px;
  height:6px;
}
#u952 {
  position:absolute;
  left:3px;
  top:35px;
  width:102px;
  height:3px;
}
#u953 {
  position:absolute;
  left:2px;
  top:-6px;
  width:98px;
  visibility:hidden;
  word-wrap:break-word;
}
#u954_img {
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:44px;
}
#u954 {
  position:absolute;
  left:285px;
  top:2px;
  width:75px;
  height:44px;
}
#u955 {
  position:absolute;
  left:2px;
  top:14px;
  width:71px;
  visibility:hidden;
  word-wrap:break-word;
}
#u956_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:83px;
}
#u956 {
  position:absolute;
  left:324px;
  top:-14px;
  width:1px;
  height:82px;
  -webkit-transform:rotate(300deg);
  -moz-transform:rotate(300deg);
  -ms-transform:rotate(300deg);
  transform:rotate(300deg);
}
#u957 {
  position:absolute;
  left:2px;
  top:33px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u958_img {
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:36px;
}
#u958 {
  position:absolute;
  left:309px;
  top:3px;
  width:47px;
  height:36px;
  -webkit-transform:rotate(30deg);
  -moz-transform:rotate(30deg);
  -ms-transform:rotate(30deg);
  transform:rotate(30deg);
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  text-align:center;
}
#u959 {
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  word-wrap:break-word;
}
#u960_img {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:2px;
}
#u960 {
  position:absolute;
  left:64px;
  top:478px;
  width:159px;
  height:1px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u961 {
  position:absolute;
  left:2px;
  top:-8px;
  width:155px;
  visibility:hidden;
  word-wrap:break-word;
}
#u962_div {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u962 {
  position:absolute;
  left:31px;
  top:478px;
  width:97px;
  height:38px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u962_div.selected {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u962.selected {
}
#u962_div.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u962.disabled {
}
#u963 {
  position:absolute;
  left:2px;
  top:10px;
  width:93px;
  word-wrap:break-word;
}
#u964_img {
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:16px;
}
#u964 {
  position:absolute;
  left:108px;
  top:452px;
  width:57px;
  height:16px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:8px;
}
#u965 {
  position:absolute;
  left:2px;
  top:2px;
  width:53px;
  word-wrap:break-word;
}
#u966_img {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:11px;
}
#u966 {
  position:absolute;
  left:128px;
  top:677px;
  width:221px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-align:center;
}
#u967 {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  word-wrap:break-word;
}
#u968_img {
  position:absolute;
  left:0px;
  top:0px;
  width:249px;
  height:51px;
}
#u968 {
  position:absolute;
  left:1125px;
  top:97px;
  width:249px;
  height:51px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u969 {
  position:absolute;
  left:0px;
  top:0px;
  width:249px;
  white-space:nowrap;
}
#u970_img {
  position:absolute;
  left:0px;
  top:0px;
  width:285px;
  height:68px;
}
#u970 {
  position:absolute;
  left:1125px;
  top:374px;
  width:285px;
  height:68px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u971 {
  position:absolute;
  left:0px;
  top:0px;
  width:285px;
  white-space:nowrap;
}
#u972_img {
  position:absolute;
  left:0px;
  top:0px;
  width:383px;
  height:68px;
}
#u972 {
  position:absolute;
  left:1125px;
  top:669px;
  width:383px;
  height:68px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u973 {
  position:absolute;
  left:0px;
  top:0px;
  width:383px;
  white-space:nowrap;
}
#u974_img {
  position:absolute;
  left:0px;
  top:0px;
  width:371px;
  height:119px;
}
#u974 {
  position:absolute;
  left:1125px;
  top:978px;
  width:371px;
  height:119px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u975 {
  position:absolute;
  left:0px;
  top:0px;
  width:371px;
  white-space:nowrap;
}
#u976_img {
  position:absolute;
  left:0px;
  top:0px;
  width:607px;
  height:51px;
}
#u976 {
  position:absolute;
  left:1125px;
  top:9px;
  width:607px;
  height:51px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u977 {
  position:absolute;
  left:0px;
  top:0px;
  width:607px;
  white-space:nowrap;
}
#u978 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u979_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u979 {
  position:absolute;
  left:602px;
  top:1255px;
  width:478px;
  height:257px;
}
#u980 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u981_img {
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:17px;
}
#u981 {
  position:absolute;
  left:748px;
  top:1353px;
  width:205px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u982 {
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  white-space:nowrap;
}
#u983_img {
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:53px;
}
#u983 {
  position:absolute;
  left:765px;
  top:1290px;
  width:171px;
  height:53px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u984 {
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  word-wrap:break-word;
}
#u985_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u985 {
  position:absolute;
  left:652px;
  top:1429px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u986 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u987_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u987 {
  position:absolute;
  left:866px;
  top:1429px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u988 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u989_img {
  position:absolute;
  left:0px;
  top:0px;
  width:371px;
  height:68px;
}
#u989 {
  position:absolute;
  left:1125px;
  top:1255px;
  width:371px;
  height:68px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u990 {
  position:absolute;
  left:0px;
  top:0px;
  width:371px;
  white-space:nowrap;
}
#u991_img {
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u991 {
  position:absolute;
  left:753px;
  top:19px;
  width:32px;
  height:32px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u992 {
  position:absolute;
  left:2px;
  top:8px;
  width:28px;
  visibility:hidden;
  word-wrap:break-word;
}
#u993_img {
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
}
#u993 {
  position:absolute;
  left:795px;
  top:27px;
  width:49px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u994 {
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  white-space:nowrap;
}
#u995 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u996_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:89px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u996 {
  position:absolute;
  left:602px;
  top:1562px;
  width:478px;
  height:89px;
}
#u997 {
  position:absolute;
  left:2px;
  top:36px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u998_img {
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:17px;
}
#u998 {
  position:absolute;
  left:732px;
  top:1600px;
  width:251px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u999 {
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  white-space:nowrap;
}
#u1000_img {
  position:absolute;
  left:0px;
  top:0px;
  width:272px;
  height:34px;
}
#u1000 {
  position:absolute;
  left:1125px;
  top:1562px;
  width:272px;
  height:34px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1001 {
  position:absolute;
  left:0px;
  top:0px;
  width:272px;
  white-space:nowrap;
}
#u1002 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1003_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:100px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1003 {
  position:absolute;
  left:602px;
  top:1720px;
  width:478px;
  height:100px;
}
#u1004 {
  position:absolute;
  left:2px;
  top:42px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1005_img {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:17px;
}
#u1005 {
  position:absolute;
  left:760px;
  top:1762px;
  width:193px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1006 {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  white-space:nowrap;
}
#u1007_img {
  position:absolute;
  left:0px;
  top:0px;
  width:272px;
  height:34px;
}
#u1007 {
  position:absolute;
  left:1125px;
  top:1720px;
  width:272px;
  height:34px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1008 {
  position:absolute;
  left:0px;
  top:0px;
  width:272px;
  white-space:nowrap;
}
