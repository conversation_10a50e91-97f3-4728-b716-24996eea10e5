$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bf),bh,_(bi,bj,bk,bl)),P,_(),bm,_(),bn,bo),_(T,bp,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,bs,bk,bt),t,bu,M,bv,bw,bx,x,_(y,z,A,by),bd,_(be,bz,bg,bz),bA,bB),P,_(),bm,_(),S,[_(T,bC,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,bs,bk,bt),t,bu,M,bv,bw,bx,x,_(y,z,A,by),bd,_(be,bz,bg,bz),bA,bB),P,_(),bm,_())],Q,_(bG,_(bH,bI,bJ,[_(bH,bK,bL,g,bM,[_(bN,bO,bH,bP,bQ,_(bR,k,b,bS,bT,bc),bU,bV)])])),bW,bc,bX,g),_(T,bY,V,W,X,bZ,n,br,ba,bF,bb,bc,s,_(ca,cb,t,cc,bh,_(bi,cd,bk,ce),M,cf,bw,cg,bd,_(be,ch,bg,ci),bA,cj,ck,cl),P,_(),bm,_(),S,[_(T,cm,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(ca,cb,t,cc,bh,_(bi,cd,bk,ce),M,cf,bw,cg,bd,_(be,ch,bg,ci),bA,cj,ck,cl),P,_(),bm,_())],cn,_(co,cp),bX,g),_(T,cq,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cr,bk,cs),t,bu,bd,_(be,ct,bg,cu)),P,_(),bm,_(),S,[_(T,cv,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cr,bk,cs),t,bu,bd,_(be,ct,bg,cu)),P,_(),bm,_())],bX,g),_(T,cw,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cx,bk,cy),t,bu,bd,_(be,cz,bg,cA)),P,_(),bm,_(),S,[_(T,cB,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cx,bk,cy),t,bu,bd,_(be,cz,bg,cA)),P,_(),bm,_())],bX,g),_(T,cC,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cx,bk,cy),t,bu,bd,_(be,cz,bg,cD)),P,_(),bm,_(),S,[_(T,cE,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cx,bk,cy),t,bu,bd,_(be,cz,bg,cD)),P,_(),bm,_())],bX,g),_(T,cF,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cx,bk,cy),t,bu,bd,_(be,cz,bg,cG)),P,_(),bm,_(),S,[_(T,cH,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cx,bk,cy),t,bu,bd,_(be,cz,bg,cG)),P,_(),bm,_())],bX,g),_(T,cI,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cx,bk,cy),t,bu,bd,_(be,cz,bg,cJ)),P,_(),bm,_(),S,[_(T,cK,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cx,bk,cy),t,bu,bd,_(be,cz,bg,cJ)),P,_(),bm,_())],bX,g),_(T,cL,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,cx,bk,cy),t,bu,bd,_(be,cz,bg,cM)),P,_(),bm,_(),S,[_(T,cN,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,cx,bk,cy),t,bu,bd,_(be,cz,bg,cM)),P,_(),bm,_())],bX,g)])),cO,_(cP,_(l,cP,n,cQ,p,Y,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,cR,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,cS,x,_(y,z,A,cT),cU,_(y,z,A,cV),O,cW),P,_(),bm,_(),S,[_(T,cX,V,W,X,null,bD,bc,n,bE,ba,bF,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,cS,x,_(y,z,A,cT),cU,_(y,z,A,cV),O,cW),P,_(),bm,_())],bX,g)]))),cY,_(cZ,_(da,db,dc,_(da,dd),de,_(da,df)),dg,_(da,dh),di,_(da,dj),dk,_(da,dl),dm,_(da,dn),dp,_(da,dq),dr,_(da,ds),dt,_(da,du),dv,_(da,dw),dx,_(da,dy),dz,_(da,dA),dB,_(da,dC),dD,_(da,dE),dF,_(da,dG),dH,_(da,dI),dJ,_(da,dK),dL,_(da,dM)));}; 
var b="url",c="用户协议.html",d="generationDate",e=new Date(1558951317264.74),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="9fbcd231b82a42c4ad2a2acf76b5f248",n="type",o="Axure:Page",p="name",q="用户协议",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="2f66bb3f31634ed699de67c1c0022430",V="label",W="",X="friendlyType",Y="主框架",Z="referenceDiagramObject",ba="styleType",bb="visible",bc=true,bd="location",be="x",bf=10,bg="y",bh="size",bi="width",bj=540,bk="height",bl=805,bm="imageOverrides",bn="masterId",bo="42b294620c2d49c7af5b1798469a7eae",bp="92e121a8d4e64f67a62f2c72efb99d0e",bq="Rectangle",br="vectorShape",bs=538,bt=60,bu="47641f9a00ac465095d6b672bbdffef6",bv="'PingFangSC-Regular', 'PingFang SC'",bw="fontSize",bx="20px",by=0xFFFFFF,bz=12,bA="horizontalAlignment",bB="left",bC="e3ffe99ae7884ed392b60456afcfcddb",bD="isContained",bE="richTextPanel",bF="paragraph",bG="onClick",bH="description",bI="OnClick",bJ="cases",bK="Case 1",bL="isNewIfGroup",bM="actions",bN="action",bO="linkWindow",bP="Open 使用配置 in Current Window",bQ="target",bR="targetType",bS="使用配置.html",bT="includeVariables",bU="linkType",bV="current",bW="tabbable",bX="generateCompound",bY="71f478a2b4764278b605c285739bd8c7",bZ="Paragraph",ca="fontWeight",cb="500",cc="4988d43d80b44008a4a415096f1632af",cd=273,ce=26,cf="'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC'",cg="16px",ch=104,ci=82,cj="center",ck="verticalAlignment",cl="middle",cm="ce36af53e51d4176abe1ebb509414e91",cn="images",co="normal~",cp="images/用户协议/u1167.png",cq="76391b2d3b2146189e0243af7f82a781",cr=420,cs=14,ct=68,cu=132,cv="7bbb8fedbb4d4f51a1f4974a23bc2170",cw="f70390e49b1b47928fcdbb2acf4d5aeb",cx=447,cy=11,cz=41,cA=165,cB="c33d2735aef74f70ab17eb37dac055bc",cC="8c98f1e769004ee0b5ffb7c0180f9f8e",cD=192,cE="f42562998fcb4b85897c0e3a158eb055",cF="5f6b6aa8287c4772806b03bc30478ec5",cG=213,cH="a0ac49a6eba44854aeb2bf23ba0ab18b",cI="c25855a036d94e609280fa6ee50375a1",cJ=234,cK="ed0d779fbe7d477cab92c6db888da733",cL="ba256dc971dc46faab584343a7e9f9e2",cM=255,cN="1471f8eee5cb4132935a034cbd1d9fd5",cO="masters",cP="42b294620c2d49c7af5b1798469a7eae",cQ="Axure:Master",cR="5a1fbc74d2b64be4b44e2ef951181541",cS="0882bfcd7d11450d85d157758311dca5",cT=0x7FF2F2F2,cU="borderFill",cV=0xFFCCCCCC,cW="1",cX="8523194c36f94eec9e7c0acc0e3eedb6",cY="objectPaths",cZ="2f66bb3f31634ed699de67c1c0022430",da="scriptId",db="u1162",dc="5a1fbc74d2b64be4b44e2ef951181541",dd="u1163",de="8523194c36f94eec9e7c0acc0e3eedb6",df="u1164",dg="92e121a8d4e64f67a62f2c72efb99d0e",dh="u1165",di="e3ffe99ae7884ed392b60456afcfcddb",dj="u1166",dk="71f478a2b4764278b605c285739bd8c7",dl="u1167",dm="ce36af53e51d4176abe1ebb509414e91",dn="u1168",dp="76391b2d3b2146189e0243af7f82a781",dq="u1169",dr="7bbb8fedbb4d4f51a1f4974a23bc2170",ds="u1170",dt="f70390e49b1b47928fcdbb2acf4d5aeb",du="u1171",dv="c33d2735aef74f70ab17eb37dac055bc",dw="u1172",dx="8c98f1e769004ee0b5ffb7c0180f9f8e",dy="u1173",dz="f42562998fcb4b85897c0e3a158eb055",dA="u1174",dB="5f6b6aa8287c4772806b03bc30478ec5",dC="u1175",dD="a0ac49a6eba44854aeb2bf23ba0ab18b",dE="u1176",dF="c25855a036d94e609280fa6ee50375a1",dG="u1177",dH="ed0d779fbe7d477cab92c6db888da733",dI="u1178",dJ="ba256dc971dc46faab584343a7e9f9e2",dK="u1179",dL="1471f8eee5cb4132935a034cbd1d9fd5",dM="u1180";
return _creator();
})());