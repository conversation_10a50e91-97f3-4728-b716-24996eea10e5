body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:2327px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u1471_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1471 {
  position:absolute;
  left:10px;
  top:10px;
  width:540px;
  height:805px;
}
#u1472 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1473_img {
  position:absolute;
  left:0px;
  top:0px;
  width:365px;
  height:482px;
}
#u1473 {
  position:absolute;
  left:111px;
  top:260px;
  width:365px;
  height:482px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1474 {
  position:absolute;
  left:2px;
  top:232px;
  width:361px;
  word-wrap:break-word;
}
#u1475_div {
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1475 {
  position:absolute;
  left:187px;
  top:145px;
  width:169px;
  height:71px;
  text-align:center;
}
#u1476 {
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  white-space:nowrap;
}
#u1478_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1478 {
  position:absolute;
  left:600px;
  top:10px;
  width:540px;
  height:805px;
}
#u1479 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1480_img {
  position:absolute;
  left:0px;
  top:0px;
  width:365px;
  height:482px;
}
#u1480 {
  position:absolute;
  left:701px;
  top:260px;
  width:365px;
  height:482px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1481 {
  position:absolute;
  left:2px;
  top:232px;
  width:361px;
  word-wrap:break-word;
}
#u1482_div {
  position:absolute;
  left:0px;
  top:0px;
  width:261px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1482 {
  position:absolute;
  left:743px;
  top:145px;
  width:261px;
  height:71px;
  text-align:center;
}
#u1483 {
  position:absolute;
  left:0px;
  top:0px;
  width:261px;
  white-space:nowrap;
}
#u1485_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1485 {
  position:absolute;
  left:1188px;
  top:10px;
  width:540px;
  height:805px;
}
#u1486 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1487_img {
  position:absolute;
  left:0px;
  top:0px;
  width:365px;
  height:482px;
}
#u1487 {
  position:absolute;
  left:1289px;
  top:260px;
  width:365px;
  height:482px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1488 {
  position:absolute;
  left:2px;
  top:232px;
  width:361px;
  word-wrap:break-word;
}
#u1489_div {
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1489 {
  position:absolute;
  left:1358px;
  top:145px;
  width:257px;
  height:71px;
  text-align:center;
}
#u1490 {
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  white-space:nowrap;
}
#u1492_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1492 {
  position:absolute;
  left:1787px;
  top:10px;
  width:540px;
  height:805px;
}
#u1493 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1494_img {
  position:absolute;
  left:0px;
  top:0px;
  width:365px;
  height:482px;
}
#u1494 {
  position:absolute;
  left:1888px;
  top:260px;
  width:365px;
  height:482px;
}
#u1495 {
  position:absolute;
  left:2px;
  top:232px;
  width:361px;
  word-wrap:break-word;
}
#u1496_div {
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1496 {
  position:absolute;
  left:1959px;
  top:145px;
  width:225px;
  height:71px;
  text-align:center;
}
#u1497 {
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  white-space:nowrap;
}
