<!DOCTYPE html>
<html>
  <head>
    <title>原型更新记录</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/原型更新记录/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/原型更新记录/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- clsuter (Table) -->
      <div id="u1181" class="ax_default" data-label="clsuter">

        <!-- Unnamed (Table Cell) -->
        <div id="u1182" class="ax_default table_cell">
          <img id="u1182_img" class="img " src="images/原型更新记录/u1182.png"/>
          <!-- Unnamed () -->
          <div id="u1183" class="text" style="visibility: visible;">
            <p><span>修改时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1184" class="ax_default table_cell">
          <img id="u1184_img" class="img " src="images/原型更新记录/u1184.png"/>
          <!-- Unnamed () -->
          <div id="u1185" class="text" style="visibility: visible;">
            <p><span>业务路径</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1186" class="ax_default table_cell">
          <img id="u1186_img" class="img " src="images/原型更新记录/u1186.png"/>
          <!-- Unnamed () -->
          <div id="u1187" class="text" style="visibility: visible;">
            <p><span>修改说明</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1188" class="ax_default table_cell">
          <img id="u1188_img" class="img " src="images/原型更新记录/u1182.png"/>
          <!-- Unnamed () -->
          <div id="u1189" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1190" class="ax_default table_cell">
          <img id="u1190_img" class="img " src="images/原型更新记录/u1184.png"/>
          <!-- Unnamed () -->
          <div id="u1191" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1192" class="ax_default table_cell">
          <img id="u1192_img" class="img " src="images/原型更新记录/u1186.png"/>
          <!-- Unnamed () -->
          <div id="u1193" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1194" class="ax_default table_cell">
          <img id="u1194_img" class="img " src="images/原型更新记录/u1194.png"/>
          <!-- Unnamed () -->
          <div id="u1195" class="text" style="visibility: visible;">
            <p><span>2019年05月27日</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1196" class="ax_default table_cell">
          <img id="u1196_img" class="img " src="images/原型更新记录/u1196.png"/>
          <!-- Unnamed () -->
          <div id="u1197" class="text" style="visibility: visible;">
            <p><span>1.设置-登录验证</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1198" class="ax_default table_cell">
          <img id="u1198_img" class="img " src="images/原型更新记录/u1198.png"/>
          <!-- Unnamed () -->
          <div id="u1199" class="text" style="visibility: visible;">
            <p><span>增加页面标题说明“正在进入设置页，请进行验证”</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1200" class="ax_default table_cell">
          <img id="u1200_img" class="img " src="images/原型更新记录/u1200.png"/>
          <!-- Unnamed () -->
          <div id="u1201" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1202" class="ax_default table_cell">
          <img id="u1202_img" class="img " src="images/原型更新记录/u1202.png"/>
          <!-- Unnamed () -->
          <div id="u1203" class="text" style="visibility: visible;">
            <p><span>2.设置页面-广告轮播等待时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u1204" class="ax_default table_cell">
          <img id="u1204_img" class="img " src="images/原型更新记录/u1204.png"/>
          <!-- Unnamed () -->
          <div id="u1205" class="text" style="visibility: visible;">
            <p><span>1)轮播间隔时间由“1-60秒”改为“1秒 3秒 5秒 10秒”</span></p><p><span>2)默认轮播时间10s,调为5s</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u1206" class="ax_default table_cell">
        <div id="u1206_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1207" class="text" style="visibility: visible;">
          <p><span>2019年05月27日</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u1208" class="ax_default table_cell">
        <div id="u1208_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1209" class="text" style="visibility: visible;">
          <p><span>&nbsp;所属版本 自助点餐 v1.0.0</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
