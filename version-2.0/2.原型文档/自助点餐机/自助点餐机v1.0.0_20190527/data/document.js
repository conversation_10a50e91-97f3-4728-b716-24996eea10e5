$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z,A,[_(u,B,w,C,y,D),_(u,E,w,x,y,z,A,[_(u,F,w,C,y,G),_(u,H,w,C,y,I),_(u,J,w,C,y,K,A,[_(u,L,w,C,y,M)]),_(u,N,w,C,y,O),_(u,P,w,C,y,Q,A,[_(u,R,w,C,y,S),_(u,T,w,C,y,U),_(u,V,w,C,y,W),_(u,X,w,C,y,Y)]),_(u,Z,w,x,y,z,A,[_(u,ba,w,C,y,bb)]),_(u,bc,w,x,y,z,A,[_(u,bd,w,C,y,be),_(u,bf,w,C,y,bg),_(u,bh,w,C,y,bi)]),_(u,bj,w,x,y,z,A,[_(u,bk,w,C,y,bl)])])])]),bm,_(bn,z),bo,_(bp,bq,br,_(bs,bt,bu,bt),bv,bw),bx,[],by,_(bz,_(bA,bB,bC,bD,bE,bF,bG,bH,bI,bJ,bK,f,bL,_(bM,bN,bO,bP,bQ,bR),bS,bT,bU,bF,bV,_(bW,bt,bX,bt),br,_(bs,bt,bu,bt),bY,d,bZ,f,ca,bB,cb,_(bM,bN,bO,cc),cd,_(bM,bN,bO,ce),cf,cg,ch,bN,bQ,cg,ci,cj,ck,cl,cm,cn,co,cn,cp,cn,cq,cn,cr,_(),cs,cj,ct,cj,cu,_(cv,f,cw,cx,cy,cx,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cF)),cG,_(cv,f,cw,bt,cy,cx,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cF)),cH,_(cv,f,cw,bR,cy,bR,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cI))),cJ,_(cK,_(bA,cL),cM,_(bA,cN,cf,cj,cb,_(bM,bN,bO,cO)),cP,_(bA,cQ,cf,cj,cb,_(bM,bN,bO,cR)),cS,_(bA,cT),cU,_(bA,cV,bC,bD,bE,bF,bL,_(bM,bN,bO,bP,bQ,bR),cd,_(bM,bN,bO,cW),cf,cg,cb,_(bM,bN,bO,cX),bS,bT,bG,bH,bI,bJ,bK,f,ch,bN,ci,cj,bQ,cg,cu,_(cv,f,cw,cx,cy,cx,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cF)),cG,_(cv,f,cw,bt,cy,cx,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cF)),cH,_(cv,f,cw,bR,cy,bR,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cI)),ck,cl,cm,cn,co,cn,cp,cn,cq,cn,bU,bF),cY,_(bA,cZ,cf,cj),da,_(bA,db,cb,_(bM,bN,bO,cO)),dc,_(bA,dd,ci,de),df,_(bA,dg,bI,dh,bC,di,cf,cj,cb,_(bM,bN,bO,dj),bS,dk,ck,dl,cm,cj,co,cj,cp,cj,cq,cj),dm,_(bA,dn,bI,dp,bC,di,cf,cj,cb,_(bM,bN,bO,dj),bS,dk,ck,dl,cm,cj,co,cj,cp,cj,cq,cj),dq,_(bA,dr,bI,ds,bC,di,cf,cj,cb,_(bM,bN,bO,dj),bS,dk,ck,dl,cm,cj,co,cj,cp,cj,cq,cj),dt,_(bA,du,bI,dv,bC,di,cf,cj,cb,_(bM,bN,bO,dj),bS,dk,ck,dl,cm,cj,co,cj,cp,cj,cq,cj),dw,_(bA,dx,bC,di,cf,cj,cb,_(bM,bN,bO,dj),bS,dk,ck,dl,cm,cj,co,cj,cp,cj,cq,cj),dy,_(bA,dz,bI,dA,bC,di,cf,cj,cb,_(bM,bN,bO,dj),bS,dk,ck,dl,cm,cj,co,cj,cp,cj,cq,cj),dB,_(bA,dC,bI,dv,cf,cj,cb,_(bM,bN,bO,dj),bS,dk,ck,dl,cm,cj,co,cj,cp,cj,cq,cj),dD,_(bA,dE,cf,cj,cb,_(bM,bN,bO,dj),bS,dk,ck,dl,cm,cj,co,cj,cp,cj,cq,cj),dF,_(bA,dG,cb,_(bM,bN,bO,dj)),dH,_(bA,dI,cf,de,cb,_(bM,bN,bO,dj)),dJ,_(bA,dK,bL,_(bM,bN,bO,dL,bQ,bR),bS,dk,ck,cl),dM,_(bA,dN,bL,_(bM,bN,bO,dL,bQ,bR),bS,dk,ck,dl),dO,_(bA,dP,bL,_(bM,bN,bO,dL,bQ,bR),bS,dk,ck,dl),dQ,_(bA,dR,bL,_(bM,bN,bO,dL,bQ,bR),bS,dk,ck,dl),dS,_(bA,dT,bS,dk,ck,dl),dU,_(bA,dV,bS,dk,ck,dl),dW,_(bA,dX,bS,bT),dY,_(bA,dZ,cf,cj,cb,_(bM,bN,bO,dj),bS,dk,ck,cl),ea,_(bA,eb),ec,_(bA,ed,cb,_(bM,bN,bO,dj)),ee,_(bA,ef,bC,bD,bE,bF,bL,_(bM,bN,bO,eg,bQ,bR),cd,_(bM,bN,bO,cW),cf,cg,bS,bT,bG,eh,bI,ei,bK,f,ch,bN,ci,cj,cb,_(bM,bN,bO,cc),bQ,cg,cu,_(cv,f,cw,cx,cy,cx,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cF)),cG,_(cv,f,cw,bt,cy,cx,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cF)),cH,_(cv,f,cw,bR,cy,bR,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cI)),ck,cl,cm,cn,co,cn,cp,cn,cq,cn,bU,bF),ej,_(bA,ek,bL,_(bM,bN,bO,cc,bQ,bR),cd,_(bM,bN,bO,cc),cb,_(bM,bN,bO,el),cu,_(cv,d,cw,bR,cy,bR,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,em))),en,_(bA,eo,cb,_(bM,ep,eq,[_(bO,cc),_(bO,cO),_(bO,er),_(bO,cc)])),es,_(bA,et),eu,_(bA,ev,bC,bD,bE,bF,bG,bH,bI,bJ,bK,f,bL,_(bM,bN,bO,bP,bQ,bR),bS,bT,bU,bF,cb,_(bM,bN,bO,cc),cd,_(bM,bN,bO,bP),cf,cg,ch,bN,bQ,cg,ci,cj,ck,cl,cm,cn,co,cn,cp,cn,cq,cn,cu,_(cv,f,cw,cx,cy,cx,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cF)),cG,_(cv,f,cw,bt,cy,cx,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cF)),cH,_(cv,f,cw,bR,cy,bR,cz,cx,bO,_(cA,cB,cC,cB,cD,cB,cE,cI))),ew,_(bA,ex,cd,_(bM,bN,bO,dL)),ey,_(bA,ez,cf,cj,cb,_(bM,bN,bO,bP))),eA,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="商户后台",w="type",x="Folder",y="url",z="",A="children",B="原型更新记录",C="Wireframe",D="原型更新记录.html",E="v1.0.0",F="整体设计说明",G="整体设计说明.html",H="点餐机主流程图",I="点餐机主流程图.html",J="登录(首次)",K="登录_首次_.html",L="登录(再次)",M="登录_再次_.html",N="全屏广告",O="全屏广告.html",P="点餐-4列",Q="点餐-4列.html",R="[对话框]选择规格/含属性商品",S="_对话框_选择规格_含属性商品.html",T="[对话框]选择套餐商品(可选分组)",U="_对话框_选择套餐商品_可选分组_.html",V="点餐-三列……",W="点餐-三列…….html",X="点餐-双列……",Y="点餐-双列…….html",Z="支付P1",ba="支付-选择支付",bb="支付-选择支付.html",bc="设置",bd="登录验证",be="登录验证.html",bf="使用配置",bg="使用配置.html",bh="用户协议",bi="用户协议.html",bj="应用市场AD",bk="p1",bl="p1.html",bm="globalVariables",bn="onloadvariable",bo="defaultAdaptiveView",bp="name",bq="Base",br="size",bs="width",bt=0,bu="height",bv="condition",bw="<=",bx="adaptiveViews",by="stylesheet",bz="defaultStyle",bA="id",bB="627587b6038d43cca051c114ac41ad32",bC="fontWeight",bD="400",bE="fontStyle",bF="normal",bG="fontName",bH="'ArialMT', 'Arial'",bI="fontSize",bJ="13px",bK="underline",bL="foreGroundFill",bM="fillType",bN="solid",bO="color",bP=0xFF333333,bQ="opacity",bR=1,bS="horizontalAlignment",bT="center",bU="lineSpacing",bV="location",bW="x",bX="y",bY="visible",bZ="limbo",ca="baseStyle",cb="fill",cc=0xFFFFFFFF,cd="borderFill",ce=0xFF797979,cf="borderWidth",cg="1",ch="linePattern",ci="cornerRadius",cj="0",ck="verticalAlignment",cl="middle",cm="paddingLeft",cn="2",co="paddingTop",cp="paddingRight",cq="paddingBottom",cr="stateStyles",cs="rotation",ct="textRotation",cu="outerShadow",cv="on",cw="offsetX",cx=5,cy="offsetY",cz="blurRadius",cA="r",cB=0,cC="g",cD="b",cE="a",cF=0.349019607843137,cG="innerShadow",cH="textShadow",cI=0.647058823529412,cJ="customStyles",cK="box_1",cL="********************************",cM="box_2",cN="********************************",cO=0xFFF2F2F2,cP="box_3",cQ="********************************",cR=0xFFD7D7D7,cS="ellipse",cT="eff044fe6497434a8c5f89f769ddde3b",cU="_形状",cV="40519e9ec4264601bfb12c514e4f4867",cW=0xFFCCCCCC,cX=0x19333333,cY="image",cZ="75a91ee5b9d042cfa01b8d565fe289c0",da="placeholder",db="c50e74f669b24b37bd9c18da7326bccd",dc="button",dd="c9f35713a1cf4e91a0f2dbac65e6fb5c",de="5",df="heading_1",dg="1111111151944dfba49f67fd55eb1f88",dh="32px",di="bold",dj=0xFFFFFF,dk="left",dl="top",dm="heading_2",dn="b3a15c9ddde04520be40f94c8168891e",dp="24px",dq="heading_3",dr="8c7a4c5ad69a4369a5f7788171ac0b32",ds="18px",dt="heading_4",du="e995c891077945c89c0b5fe110d15a0b",dv="14px",dw="heading_5",dx="386b19ef4be143bd9b6c392ded969f89",dy="heading_6",dz="fc3b9a13b5574fa098ef0a1db9aac861",dA="10px",dB="label",dC="2285372321d148ec80932747449c36c9",dD="paragraph",dE="4988d43d80b44008a4a415096f1632af",dF="line",dG="619b2148ccc1497285562264d51992f9",dH="arrow",dI="d148f2c5268542409e72dde43e40043e",dJ="text_field",dK="44157808f2934100b68f2394a66b2bba",dL=0xFF000000,dM="text_area",dN="42ee17691d13435b8256d8d0a814778f",dO="droplist",dP="85f724022aae41c594175ddac9c289eb",dQ="list_box",dR="********************************",dS="checkbox",dT="********************************",dU="radio_button",dV="4eb5516f311c4bdfa0cb11d7ea75084e",dW="html_button",dX="eed12d9ebe2e4b9689b3b57949563dca",dY="tree_node",dZ="93a4c3353b6f4562af635b7116d6bf94",ea="table_cell",eb="33ea2511485c479dbf973af3302f2352",ec="menu_item",ed="2036b2baccbc41f0b9263a6981a11a42",ee="connector",ef="699a012e142a4bcba964d96e88b88bdf",eg=0xFF0000FF,eh="'PingFangSC-Regular', 'PingFang SC'",ei="12px",ej="marker",ek="a8e305fe5c2a462b995b0021a9ba82b9",el=0xFF009DD9,em=0.698039215686274,en="flow_shape",eo="df01900e3c4e43f284bafec04b0864c4",ep="linearGradient",eq="colors",er=0xFFE4E4E4,es="table",et="d612b8c2247342eda6a8bc0663265baa",eu="shape",ev="98c916898e844865a527f56bc61a500d",ew="horizontal_line",ex="f48196c19ab74fb7b3acb5151ce8ea2d",ey="icon",ez="26c731cb771b44a88eb8b6e97e78c80e",eA="duplicateStyles";
return _creator();
})());