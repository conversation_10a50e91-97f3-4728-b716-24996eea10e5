<!DOCTYPE html>
<html>
  <head>
    <title>登录(首次)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/登录_首次_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/登录_首次_/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Rectangle) -->
      <div id="u142" class="ax_default box_3">
        <div id="u142_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u143" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u144" class="ax_default paragraph">
        <img id="u144_img" class="img " src="images/登录_首次_/u144.png"/>
        <!-- Unnamed () -->
        <div id="u145" class="text" style="visibility: visible;">
          <p><span>掌控者Slogan</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u146" class="ax_default paragraph">
        <img id="u146_img" class="img " src="images/登录_首次_/u146.png"/>
        <!-- Unnamed () -->
        <div id="u147" class="text" style="visibility: visible;">
          <p><span>LOGO</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u148" class="ax_default paragraph">
        <img id="u148_img" class="img " src="images/登录_首次_/u148.png"/>
        <!-- Unnamed () -->
        <div id="u149" class="text" style="visibility: visible;">
          <p><span style="color:#FF0000;">扫码登录本期不做</span></p><p><span style="color:#FF0000;"><br></span></p><p><span style="color:#1B5C57;">1.首次登录时，须输入门店ID/员工账号/登录密码，绑定设备到门店下，绑定成功后初始如下数据</span></p><p><span style="color:#1B5C57;">1）商品列表数据为4列</span></p><p><span style="color:#1B5C57;">2）打印小票为结账单（与一体机结账单内容相同）</span></p><p><span style="color:#1B5C57;">3）打印机为本机打印机，支持小票为80mm</span></p><p><span style="color:#1B5C57;">4）收款方式为主扫</span></p><p><span style="color:#1B5C57;">5）到款提醒未开启</span></p><p><span style="color:#1B5C57;"><br></span></p><p><span style="color:#1B5C57;">2.失败时，正确反馈失败原因</span></p>
        </div>
      </div>

      <!-- 账号登录 (Group) -->
      <div id="u150" class="ax_default" data-label="账号登录">

        <!-- Unnamed (Text Field) -->
        <div id="u151" class="ax_default text_field">
          <input id="u151_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Text Field) -->
        <div id="u152" class="ax_default text_field">
          <input id="u152_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u153" class="ax_default shape">
          <div id="u153_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u154" class="text" style="visibility: visible;">
            <p><span>登录</span></p>
          </div>
        </div>

        <!-- Unnamed (Text Field) -->
        <div id="u155" class="ax_default text_field">
          <input id="u155_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u156" class="ax_default paragraph">
          <img id="u156_img" class="img " src="images/登录_首次_/u156.png"/>
          <!-- Unnamed () -->
          <div id="u157" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u158" class="ax_default paragraph">
          <img id="u158_img" class="img " src="images/登录_首次_/u156.png"/>
          <!-- Unnamed () -->
          <div id="u159" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u160" class="ax_default paragraph">
          <img id="u160_img" class="img " src="images/登录_首次_/u156.png"/>
          <!-- Unnamed () -->
          <div id="u161" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Shape) -->
        <div id="u162" class="ax_default icon">
          <img id="u162_img" class="img " src="images/登录_首次_/u162.png"/>
          <!-- Unnamed () -->
          <div id="u163" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Vertical Line) -->
        <div id="u164" class="ax_default line">
          <img id="u164_img" class="img " src="images/登录_首次_/u164.png"/>
          <!-- Unnamed () -->
          <div id="u165" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u166" class="ax_default paragraph">
          <img id="u166_img" class="img " src="images/登录_首次_/u166.png"/>
          <!-- Unnamed () -->
          <div id="u167" class="text" style="visibility: visible;">
            <p><span>扫码登录</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u168" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u169" class="ax_default paragraph">
          <img id="u169_img" class="img " src="images/登录_首次_/u169.png"/>
          <!-- Unnamed () -->
          <div id="u170" class="text" style="visibility: visible;">
            <p><span>页面/模块名称</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u171" class="ax_default paragraph">
          <img id="u171_img" class="img " src="images/登录_首次_/u169.png"/>
          <!-- Unnamed () -->
          <div id="u172" class="text" style="visibility: visible;">
            <p><span>类型</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u173" class="ax_default paragraph">
          <img id="u173_img" class="img " src="images/登录_首次_/u173.png"/>
          <!-- Unnamed () -->
          <div id="u174" class="text" style="visibility: visible;">
            <p><span>操作</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u175" class="ax_default paragraph">
          <img id="u175_img" class="img " src="images/登录_首次_/u175.png"/>
          <!-- Unnamed () -->
          <div id="u176" class="text" style="visibility: visible;">
            <p><span>点餐</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u177" class="ax_default paragraph">
          <img id="u177_img" class="img " src="images/登录_首次_/u175.png"/>
          <!-- Unnamed () -->
          <div id="u178" class="text" style="visibility: visible;">
            <p><span>基础类</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u179" class="ax_default paragraph">
          <img id="u179_img" class="img " src="images/登录_首次_/u179.png"/>
          <!-- Unnamed () -->
          <div id="u180" class="text" style="visibility: visible;">
            <p><span>选择商品</span></p><p><span>整单备注</span></p><p><span>去结账</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u181" class="ax_default paragraph">
          <img id="u181_img" class="img " src="images/登录_首次_/u181.png"/>
          <!-- Unnamed () -->
          <div id="u182" class="text" style="visibility: visible;">
            <p><span>设置</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u183" class="ax_default paragraph">
          <img id="u183_img" class="img " src="images/登录_首次_/u181.png"/>
          <!-- Unnamed () -->
          <div id="u184" class="text" style="visibility: visible;">
            <p><span>权限类</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u185" class="ax_default paragraph">
          <img id="u185_img" class="img " src="images/登录_首次_/u185.png"/>
          <!-- Unnamed () -->
          <div id="u186" class="text" style="visibility: visible;">
            <p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">设备名称管理(</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#FF0000;">不做</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">)</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">商品陈列设置(</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#FF0000;">不做,</span><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;color:#008000;">后补已完成</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">)</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">收款方式(</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#FF0000;">不做</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">)</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">收款到账语音提醒管理</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">空置广告管理</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u187" class="ax_default paragraph">
        <img id="u187_img" class="img " src="images/登录_首次_/u187.png"/>
        <!-- Unnamed () -->
        <div id="u188" class="text" style="visibility: visible;">
          <p><span>自助点餐机权限内容</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
