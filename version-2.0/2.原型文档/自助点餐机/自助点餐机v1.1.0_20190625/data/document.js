$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z,A,[_(u,B,w,x,y,z,A,[_(u,C,w,D,y,E),_(u,F,w,D,y,G)])])]),H,_(I,z),J,_(K,L,M,_(N,O,P,O),Q,R),S,[],T,_(U,_(V,W,X,Y,Z,ba,bb,bc,bd,be,bf,f,bg,_(bh,bi,bj,bk,bl,bm),bn,bo,bp,ba,bq,_(br,O,bs,O),M,_(N,O,P,O),bt,d,bu,f,bv,W,bw,_(bh,bi,bj,bx),by,_(bh,bi,bj,bz),bA,bB,bC,bi,bl,bB,bD,bE,bF,bG,bH,bI,bJ,bI,bK,bI,bL,bI,bM,_(),bN,bE,bO,bE,bP,_(bQ,f,bR,bS,bT,bS,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,ca)),cb,_(bQ,f,bR,O,bT,bS,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,ca)),cc,_(bQ,f,bR,bm,bT,bm,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,cd))),ce,_(cf,_(V,cg),ch,_(V,ci,bA,bE,bw,_(bh,bi,bj,cj)),ck,_(V,cl,bA,bE,bw,_(bh,bi,bj,cm)),cn,_(V,co),cp,_(V,cq,X,Y,Z,ba,bg,_(bh,bi,bj,bk,bl,bm),by,_(bh,bi,bj,cr),bA,bB,bw,_(bh,bi,bj,cs),bn,bo,bb,bc,bd,be,bf,f,bC,bi,bD,bE,bl,bB,bP,_(bQ,f,bR,bS,bT,bS,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,ca)),cb,_(bQ,f,bR,O,bT,bS,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,ca)),cc,_(bQ,f,bR,bm,bT,bm,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,cd)),bF,bG,bH,bI,bJ,bI,bK,bI,bL,bI,bp,ba),ct,_(V,cu,bA,bE),cv,_(V,cw,bw,_(bh,bi,bj,cj)),cx,_(V,cy,bD,cz),cA,_(V,cB,bd,cC,X,cD,bA,bE,bw,_(bh,bi,bj,cE),bn,cF,bF,cG,bH,bE,bJ,bE,bK,bE,bL,bE),cH,_(V,cI,bd,cJ,X,cD,bA,bE,bw,_(bh,bi,bj,cE),bn,cF,bF,cG,bH,bE,bJ,bE,bK,bE,bL,bE),cK,_(V,cL,bd,cM,X,cD,bA,bE,bw,_(bh,bi,bj,cE),bn,cF,bF,cG,bH,bE,bJ,bE,bK,bE,bL,bE),cN,_(V,cO,bd,cP,X,cD,bA,bE,bw,_(bh,bi,bj,cE),bn,cF,bF,cG,bH,bE,bJ,bE,bK,bE,bL,bE),cQ,_(V,cR,X,cD,bA,bE,bw,_(bh,bi,bj,cE),bn,cF,bF,cG,bH,bE,bJ,bE,bK,bE,bL,bE),cS,_(V,cT,bd,cU,X,cD,bA,bE,bw,_(bh,bi,bj,cE),bn,cF,bF,cG,bH,bE,bJ,bE,bK,bE,bL,bE),cV,_(V,cW,bd,cP,bA,bE,bw,_(bh,bi,bj,cE),bn,cF,bF,cG,bH,bE,bJ,bE,bK,bE,bL,bE),cX,_(V,cY,bA,bE,bw,_(bh,bi,bj,cE),bn,cF,bF,cG,bH,bE,bJ,bE,bK,bE,bL,bE),cZ,_(V,da,bw,_(bh,bi,bj,cE)),db,_(V,dc,bA,cz,bw,_(bh,bi,bj,cE)),dd,_(V,de,bg,_(bh,bi,bj,df,bl,bm),bn,cF,bF,bG),dg,_(V,dh,bg,_(bh,bi,bj,df,bl,bm),bn,cF,bF,cG),di,_(V,dj,bg,_(bh,bi,bj,df,bl,bm),bn,cF,bF,cG),dk,_(V,dl,bg,_(bh,bi,bj,df,bl,bm),bn,cF,bF,cG),dm,_(V,dn,bn,cF,bF,cG),dp,_(V,dq,bn,cF,bF,cG),dr,_(V,ds,bn,bo),dt,_(V,du,bA,bE,bw,_(bh,bi,bj,cE),bn,cF,bF,bG),dv,_(V,dw),dx,_(V,dy,bw,_(bh,bi,bj,cE)),dz,_(V,dA,X,Y,Z,ba,bg,_(bh,bi,bj,dB,bl,bm),by,_(bh,bi,bj,cr),bA,bB,bn,bo,bb,dC,bd,dD,bf,f,bC,bi,bD,bE,bw,_(bh,bi,bj,bx),bl,bB,bP,_(bQ,f,bR,bS,bT,bS,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,ca)),cb,_(bQ,f,bR,O,bT,bS,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,ca)),cc,_(bQ,f,bR,bm,bT,bm,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,cd)),bF,bG,bH,bI,bJ,bI,bK,bI,bL,bI,bp,ba),dE,_(V,dF,bg,_(bh,bi,bj,bx,bl,bm),by,_(bh,bi,bj,bx),bw,_(bh,bi,bj,dG),bP,_(bQ,d,bR,bm,bT,bm,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,dH))),dI,_(V,dJ,bw,_(bh,dK,dL,[_(bj,bx),_(bj,cj),_(bj,dM),_(bj,bx)])),dN,_(V,dO),dP,_(V,dQ,X,Y,Z,ba,bb,bc,bd,be,bf,f,bg,_(bh,bi,bj,bk,bl,bm),bn,bo,bp,ba,bw,_(bh,bi,bj,bx),by,_(bh,bi,bj,bk),bA,bB,bC,bi,bl,bB,bD,bE,bF,bG,bH,bI,bJ,bI,bK,bI,bL,bI,bP,_(bQ,f,bR,bS,bT,bS,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,ca)),cb,_(bQ,f,bR,O,bT,bS,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,ca)),cc,_(bQ,f,bR,bm,bT,bm,bU,bS,bj,_(bV,bW,bX,bW,bY,bW,bZ,cd))),dR,_(V,dS,by,_(bh,bi,bj,df)),dT,_(V,dU,bA,bE,bw,_(bh,bi,bj,bk))),dV,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="商户后台",w="type",x="Folder",y="url",z="",A="children",B="v1.1.0",C="主页",D="Wireframe",E="主页.html",F="[对话框]验证抵扣优惠券",G="_对话框_验证抵扣优惠券.html",H="globalVariables",I="onloadvariable",J="defaultAdaptiveView",K="name",L="基本",M="size",N="width",O=0,P="height",Q="condition",R="<=",S="adaptiveViews",T="stylesheet",U="defaultStyle",V="id",W="627587b6038d43cca051c114ac41ad32",X="fontWeight",Y="400",Z="fontStyle",ba="normal",bb="fontName",bc="'ArialMT', 'Arial'",bd="fontSize",be="13px",bf="underline",bg="foreGroundFill",bh="fillType",bi="solid",bj="color",bk=0xFF333333,bl="opacity",bm=1,bn="horizontalAlignment",bo="center",bp="lineSpacing",bq="location",br="x",bs="y",bt="visible",bu="limbo",bv="baseStyle",bw="fill",bx=0xFFFFFFFF,by="borderFill",bz=0xFF797979,bA="borderWidth",bB="1",bC="linePattern",bD="cornerRadius",bE="0",bF="verticalAlignment",bG="middle",bH="paddingLeft",bI="2",bJ="paddingTop",bK="paddingRight",bL="paddingBottom",bM="stateStyles",bN="rotation",bO="textRotation",bP="outerShadow",bQ="on",bR="offsetX",bS=5,bT="offsetY",bU="blurRadius",bV="r",bW=0,bX="g",bY="b",bZ="a",ca=0.349019607843137,cb="innerShadow",cc="textShadow",cd=0.647058823529412,ce="customStyles",cf="box_1",cg="********************************",ch="box_2",ci="********************************",cj=0xFFF2F2F2,ck="box_3",cl="********************************",cm=0xFFD7D7D7,cn="ellipse",co="eff044fe6497434a8c5f89f769ddde3b",cp="_形状",cq="40519e9ec4264601bfb12c514e4f4867",cr=0xFFCCCCCC,cs=0x19333333,ct="image",cu="75a91ee5b9d042cfa01b8d565fe289c0",cv="placeholder",cw="c50e74f669b24b37bd9c18da7326bccd",cx="button",cy="c9f35713a1cf4e91a0f2dbac65e6fb5c",cz="5",cA="heading_1",cB="1111111151944dfba49f67fd55eb1f88",cC="32px",cD="bold",cE=0xFFFFFF,cF="left",cG="top",cH="heading_2",cI="b3a15c9ddde04520be40f94c8168891e",cJ="24px",cK="heading_3",cL="8c7a4c5ad69a4369a5f7788171ac0b32",cM="18px",cN="heading_4",cO="e995c891077945c89c0b5fe110d15a0b",cP="14px",cQ="heading_5",cR="386b19ef4be143bd9b6c392ded969f89",cS="heading_6",cT="fc3b9a13b5574fa098ef0a1db9aac861",cU="10px",cV="label",cW="2285372321d148ec80932747449c36c9",cX="paragraph",cY="4988d43d80b44008a4a415096f1632af",cZ="line",da="619b2148ccc1497285562264d51992f9",db="arrow",dc="d148f2c5268542409e72dde43e40043e",dd="text_field",de="44157808f2934100b68f2394a66b2bba",df=0xFF000000,dg="text_area",dh="42ee17691d13435b8256d8d0a814778f",di="droplist",dj="85f724022aae41c594175ddac9c289eb",dk="list_box",dl="********************************",dm="checkbox",dn="********************************",dp="radio_button",dq="4eb5516f311c4bdfa0cb11d7ea75084e",dr="html_button",ds="eed12d9ebe2e4b9689b3b57949563dca",dt="tree_node",du="93a4c3353b6f4562af635b7116d6bf94",dv="table_cell",dw="33ea2511485c479dbf973af3302f2352",dx="menu_item",dy="2036b2baccbc41f0b9263a6981a11a42",dz="connector",dA="699a012e142a4bcba964d96e88b88bdf",dB=0xFF0000FF,dC="'PingFangSC-Regular', 'PingFang SC'",dD="12px",dE="marker",dF="a8e305fe5c2a462b995b0021a9ba82b9",dG=0xFF009DD9,dH=0.698039215686274,dI="flow_shape",dJ="df01900e3c4e43f284bafec04b0864c4",dK="linearGradient",dL="colors",dM=0xFFE4E4E4,dN="table",dO="d612b8c2247342eda6a8bc0663265baa",dP="shape",dQ="98c916898e844865a527f56bc61a500d",dR="horizontal_line",dS="f48196c19ab74fb7b3acb5151ce8ea2d",dT="icon",dU="26c731cb771b44a88eb8b6e97e78c80e",dV="duplicateStyles";
return _creator();
})());