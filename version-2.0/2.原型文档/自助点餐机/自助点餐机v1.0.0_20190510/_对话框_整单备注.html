<!DOCTYPE html>
<html>
  <head>
    <title>[对话框]整单备注</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/_对话框_整单备注/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/_对话框_整单备注/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (主框架) -->

      <!-- Unnamed (Rectangle) -->
      <div id="u853" class="ax_default box_3">
        <div id="u853_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u854" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u855" class="ax_default box_1">
        <div id="u855_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u856" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u857" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u858" class="ax_default table_cell">
          <img id="u858_img" class="img " src="images/_对话框_整单备注/u858.png"/>
          <!-- Unnamed () -->
          <div id="u859" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u860" class="ax_default table_cell">
          <img id="u860_img" class="img " src="images/_对话框_整单备注/u858.png"/>
          <!-- Unnamed () -->
          <div id="u861" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u862" class="ax_default table_cell">
          <img id="u862_img" class="img " src="images/_对话框_整单备注/u862.png"/>
          <!-- Unnamed () -->
          <div id="u863" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u864" class="ax_default table_cell">
          <img id="u864_img" class="img " src="images/_对话框_整单备注/u858.png"/>
          <!-- Unnamed () -->
          <div id="u865" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u866" class="ax_default table_cell">
          <img id="u866_img" class="img " src="images/_对话框_整单备注/u858.png"/>
          <!-- Unnamed () -->
          <div id="u867" class="text" style="visibility: visible;">
            <p><span style="font-family:'ArialMT', 'Arial';">输入</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">键盘</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u868" class="ax_default table_cell">
          <img id="u868_img" class="img " src="images/_对话框_整单备注/u862.png"/>
          <!-- Unnamed () -->
          <div id="u869" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u870" class="ax_default table_cell">
          <img id="u870_img" class="img " src="images/_对话框_整单备注/u858.png"/>
          <!-- Unnamed () -->
          <div id="u871" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u872" class="ax_default table_cell">
          <img id="u872_img" class="img " src="images/_对话框_整单备注/u858.png"/>
          <!-- Unnamed () -->
          <div id="u873" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u874" class="ax_default table_cell">
          <img id="u874_img" class="img " src="images/_对话框_整单备注/u862.png"/>
          <!-- Unnamed () -->
          <div id="u875" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u876" class="ax_default table_cell">
          <img id="u876_img" class="img " src="images/_对话框_整单备注/u876.png"/>
          <!-- Unnamed () -->
          <div id="u877" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u878" class="ax_default table_cell">
          <img id="u878_img" class="img " src="images/_对话框_整单备注/u876.png"/>
          <!-- Unnamed () -->
          <div id="u879" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u880" class="ax_default table_cell">
          <img id="u880_img" class="img " src="images/_对话框_整单备注/u880.png"/>
          <!-- Unnamed () -->
          <div id="u881" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Text Area) -->
      <div id="u882" class="ax_default text_area">
        <textarea id="u882_input">请填写你要备注的信息，比如有忌口、喜好口味等</textarea>
      </div>

      <!-- 大份 (Rectangle) -->
      <div id="u883" class="ax_default box_2" data-label="大份">
        <div id="u883_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u884" class="text" style="visibility: visible;">
          <p><span>默认备注内容选项</span></p>
        </div>
      </div>

      <!-- 大份 (Rectangle) -->
      <div id="u885" class="ax_default box_2" data-label="大份">
        <div id="u885_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u886" class="text" style="visibility: visible;">
          <p><span>默认备注内容选项</span></p>
        </div>
      </div>

      <!-- 大份 (Rectangle) -->
      <div id="u887" class="ax_default box_2" data-label="大份">
        <div id="u887_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u888" class="text" style="visibility: visible;">
          <p><span>默认备注内容选项</span></p>
        </div>
      </div>

      <!-- 大份 (Rectangle) -->
      <div id="u889" class="ax_default box_2" data-label="大份">
        <div id="u889_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u890" class="text" style="visibility: visible;">
          <p><span>默认备注内容选项</span></p>
        </div>
      </div>

      <!-- 大份 (Rectangle) -->
      <div id="u891" class="ax_default box_2" data-label="大份">
        <div id="u891_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u892" class="text" style="visibility: visible;">
          <p><span>默认备注内容选项</span></p>
        </div>
      </div>

      <!-- 大份 (Rectangle) -->
      <div id="u893" class="ax_default box_2" data-label="大份">
        <div id="u893_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u894" class="text" style="visibility: visible;">
          <p><span>默认备注内容选项</span></p>
        </div>
      </div>

      <!-- 大份 (Rectangle) -->
      <div id="u895" class="ax_default box_2" data-label="大份">
        <div id="u895_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u896" class="text" style="visibility: visible;">
          <p><span>默认备注内容选项</span></p>
        </div>
      </div>

      <!-- 大份 (Rectangle) -->
      <div id="u897" class="ax_default box_2" data-label="大份">
        <div id="u897_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u898" class="text" style="visibility: visible;">
          <p><span>默认备注内容选项</span></p>
        </div>
      </div>

      <!-- 关闭内部框架 (Rectangle) -->
      <div id="u899" class="ax_default ellipse" data-label="关闭内部框架">
        <div id="u899_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u900" class="text" style="visibility: visible;">
          <p><span>确定</span></p>
        </div>
      </div>

      <!-- 关闭内部框架 (Rectangle) -->
      <div id="u901" class="ax_default ellipse" data-label="关闭内部框架">
        <div id="u901_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u902" class="text" style="visibility: visible;">
          <p><span>×</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u903" class="ax_default paragraph">
        <img id="u903_img" class="img " src="images/_对话框_整单备注/u903.png"/>
        <!-- Unnamed () -->
        <div id="u904" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;color:#1B5C57;">整单备注</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">0-30字，输入内容，点确定，保存内容并关闭对话框，取消清除内容并关闭窗口</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#FF0000;">快速备注--本期不做</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
