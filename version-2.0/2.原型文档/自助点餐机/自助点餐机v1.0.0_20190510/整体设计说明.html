<!DOCTYPE html>
<html>
  <head>
    <title>整体设计说明</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/整体设计说明/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/整体设计说明/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Rectangle) -->
      <div id="u0" class="ax_default label">
        <div id="u0_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1" class="text" style="visibility: visible;">
          <p><span>通用提示： </span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u2" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u3" class="ax_default label">
          <img id="u3_img" class="img " src="images/整体设计说明/u3.png"/>
          <!-- Unnamed () -->
          <div id="u4" class="text" style="visibility: visible;">
            <p><span>类型</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u5" class="ax_default label">
          <img id="u5_img" class="img " src="images/整体设计说明/u5.png"/>
          <!-- Unnamed () -->
          <div id="u6" class="text" style="visibility: visible;">
            <p><span>操作交互</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u7" class="ax_default label">
          <img id="u7_img" class="img " src="images/整体设计说明/u7.png"/>
          <!-- Unnamed () -->
          <div id="u8" class="text" style="visibility: visible;">
            <p><span>提示语</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u9" class="ax_default label">
          <img id="u9_img" class="img " src="images/整体设计说明/u9.png"/>
          <!-- Unnamed () -->
          <div id="u10" class="text" style="visibility: visible;">
            <p><span>按钮</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u11" class="ax_default label">
          <img id="u11_img" class="img " src="images/整体设计说明/u11.png"/>
          <!-- Unnamed () -->
          <div id="u12" class="text" style="visibility: visible;">
            <p><span>网络错误</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u13" class="ax_default label">
          <img id="u13_img" class="img " src="images/整体设计说明/u13.png"/>
          <!-- Unnamed () -->
          <div id="u14" class="text" style="visibility: visible;">
            <p><span>2s弹框,隐藏时，停在当前页</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u15" class="ax_default label">
          <img id="u15_img" class="img " src="images/整体设计说明/u15.png"/>
          <!-- Unnamed () -->
          <div id="u16" class="text" style="visibility: visible;">
            <p><span>网络故障，请稍后使用</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17" class="ax_default label">
          <img id="u17_img" class="img " src="images/整体设计说明/u17.png"/>
          <!-- Unnamed () -->
          <div id="u18" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u19" class="ax_default label">
          <img id="u19_img" class="img " src="images/整体设计说明/u19.png"/>
          <!-- Unnamed () -->
          <div id="u20" class="text" style="visibility: visible;">
            <p><span>输入超出长度限制的内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u21" class="ax_default label">
          <img id="u21_img" class="img " src="images/整体设计说明/u21.png"/>
          <!-- Unnamed () -->
          <div id="u22" class="text" style="visibility: visible;">
            <p><span>当输入内容到达限制长度时，不可继续输入</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u23" class="ax_default label">
          <img id="u23_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u24" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u25" class="ax_default label">
          <img id="u25_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u26" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u27" class="ax_default label">
          <img id="u27_img" class="img " src="images/整体设计说明/u19.png"/>
          <!-- Unnamed () -->
          <div id="u28" class="text" style="visibility: visible;">
            <p><span>输入不合字符类型的内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u29" class="ax_default label">
          <img id="u29_img" class="img " src="images/整体设计说明/u21.png"/>
          <!-- Unnamed () -->
          <div id="u30" class="text" style="visibility: visible;">
            <p><span>禁止输入</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u31" class="ax_default label">
          <img id="u31_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u32" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u33" class="ax_default label">
          <img id="u33_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u34" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u35" class="ax_default label">
          <img id="u35_img" class="img " src="images/整体设计说明/u19.png"/>
          <!-- Unnamed () -->
          <div id="u36" class="text" style="visibility: visible;">
            <p><span>账号密码错误</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u37" class="ax_default label">
          <img id="u37_img" class="img " src="images/整体设计说明/u21.png"/>
          <!-- Unnamed () -->
          <div id="u38" class="text" style="visibility: visible;">
            <p><span>页面提示</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u39" class="ax_default label">
          <img id="u39_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u40" class="text" style="visibility: visible;">
            <p><span>用户名或密码错误(服务器返回有内容按照提供内容显示）</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u41" class="ax_default label">
          <img id="u41_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u42" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u43" class="ax_default label">
          <img id="u43_img" class="img " src="images/整体设计说明/u19.png"/>
          <!-- Unnamed () -->
          <div id="u44" class="text" style="visibility: visible;">
            <p><span>账号禁用，不能登录</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u45" class="ax_default label">
          <img id="u45_img" class="img " src="images/整体设计说明/u21.png"/>
          <!-- Unnamed () -->
          <div id="u46" class="text" style="visibility: visible;">
            <p><span>页面提示</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u47" class="ax_default label">
          <img id="u47_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u48" class="text" style="visibility: visible;">
            <p><span>账号已禁用，无法登陆(服务器返回有内容按照提供内容显示）</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u49" class="ax_default label">
          <img id="u49_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u50" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u51" class="ax_default label">
          <img id="u51_img" class="img " src="images/整体设计说明/u51.png"/>
          <!-- Unnamed () -->
          <div id="u52" class="text" style="visibility: visible;">
            <p><span>商品选择框</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u53" class="ax_default label">
          <img id="u53_img" class="img " src="images/整体设计说明/u53.png"/>
          <!-- Unnamed () -->
          <div id="u54" class="text" style="visibility: visible;">
            <p><span>对话框支持点击关闭按钮或者空白区域隐藏，隐藏时放弃已选内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u55" class="ax_default label">
          <img id="u55_img" class="img " src="images/整体设计说明/u55.png"/>
          <!-- Unnamed () -->
          <div id="u56" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u57" class="ax_default label">
          <img id="u57_img" class="img " src="images/整体设计说明/u57.png"/>
          <!-- Unnamed () -->
          <div id="u58" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u59" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u60" class="ax_default label">
          <img id="u60_img" class="img " src="images/整体设计说明/u60.png"/>
          <!-- Unnamed () -->
          <div id="u61" class="text" style="visibility: visible;">
            <p><span>数据名称</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u62" class="ax_default label">
          <img id="u62_img" class="img " src="images/整体设计说明/u62.png"/>
          <!-- Unnamed () -->
          <div id="u63" class="text" style="visibility: visible;">
            <p><span>字段限制</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u64" class="ax_default label">
          <img id="u64_img" class="img " src="images/整体设计说明/u64.png"/>
          <!-- Unnamed () -->
          <div id="u65" class="text" style="visibility: visible;">
            <p><span>备注</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u66" class="ax_default label">
          <img id="u66_img" class="img " src="images/整体设计说明/u60.png"/>
          <!-- Unnamed () -->
          <div id="u67" class="text" style="visibility: visible;">
            <p><span>门店ID</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u68" class="ax_default label">
          <img id="u68_img" class="img " src="images/整体设计说明/u62.png"/>
          <!-- Unnamed () -->
          <div id="u69" class="text" style="visibility: visible;">
            <p><span>7为数字</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u70" class="ax_default label">
          <img id="u70_img" class="img " src="images/整体设计说明/u64.png"/>
          <!-- Unnamed () -->
          <div id="u71" class="text" style="visibility: visible;">
            <p><span>非法内容禁输入，不足时提示：请输入门店ID,7位数字</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u72" class="ax_default label">
          <img id="u72_img" class="img " src="images/整体设计说明/u60.png"/>
          <!-- Unnamed () -->
          <div id="u73" class="text" style="visibility: visible;">
            <p><span>账号</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u74" class="ax_default label">
          <img id="u74_img" class="img " src="images/整体设计说明/u62.png"/>
          <!-- Unnamed () -->
          <div id="u75" class="text" style="visibility: visible;">
            <p><span>3-6位数字</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u76" class="ax_default label">
          <img id="u76_img" class="img " src="images/整体设计说明/u64.png"/>
          <!-- Unnamed () -->
          <div id="u77" class="text" style="visibility: visible;">
            <p><span>非法内容禁输入，不足时提示：请输入账号3-6位数字</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u78" class="ax_default label">
          <img id="u78_img" class="img " src="images/整体设计说明/u60.png"/>
          <!-- Unnamed () -->
          <div id="u79" class="text" style="visibility: visible;">
            <p><span>密码</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u80" class="ax_default label">
          <img id="u80_img" class="img " src="images/整体设计说明/u62.png"/>
          <!-- Unnamed () -->
          <div id="u81" class="text" style="visibility: visible;">
            <p><span>6位数字</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u82" class="ax_default label">
          <img id="u82_img" class="img " src="images/整体设计说明/u64.png"/>
          <!-- Unnamed () -->
          <div id="u83" class="text" style="visibility: visible;">
            <p><span>非法内容禁输入，不足时提示：请输入账号6位数字</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u84" class="ax_default label">
          <img id="u84_img" class="img " src="images/整体设计说明/u60.png"/>
          <!-- Unnamed () -->
          <div id="u85" class="text" style="visibility: visible;">
            <p><span>商品单价</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u86" class="ax_default label">
          <img id="u86_img" class="img " src="images/整体设计说明/u62.png"/>
          <!-- Unnamed () -->
          <div id="u87" class="text" style="visibility: visible;">
            <p><span>0.01-99999.99</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u88" class="ax_default label">
          <img id="u88_img" class="img " src="images/整体设计说明/u64.png"/>
          <!-- Unnamed () -->
          <div id="u89" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u90" class="ax_default label">
          <img id="u90_img" class="img " src="images/整体设计说明/u60.png"/>
          <!-- Unnamed () -->
          <div id="u91" class="text" style="visibility: visible;">
            <p><span>属性商品单价</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u92" class="ax_default label">
          <img id="u92_img" class="img " src="images/整体设计说明/u62.png"/>
          <!-- Unnamed () -->
          <div id="u93" class="text" style="visibility: visible;">
            <p><span>0.01-99.99</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u94" class="ax_default label">
          <img id="u94_img" class="img " src="images/整体设计说明/u64.png"/>
          <!-- Unnamed () -->
          <div id="u95" class="text" style="visibility: visible;">
            <p><span>口味/做法/加料等统称为属性</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u96" class="ax_default label">
          <img id="u96_img" class="img " src="images/整体设计说明/u60.png"/>
          <!-- Unnamed () -->
          <div id="u97" class="text" style="visibility: visible;">
            <p><span>套餐单项数量（普通)</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u98" class="ax_default label">
          <img id="u98_img" class="img " src="images/整体设计说明/u62.png"/>
          <!-- Unnamed () -->
          <div id="u99" class="text" style="visibility: visible;">
            <p><span>1-99的整数</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u100" class="ax_default label">
          <img id="u100_img" class="img " src="images/整体设计说明/u64.png"/>
          <!-- Unnamed () -->
          <div id="u101" class="text" style="visibility: visible;">
            <p><span>限制由套餐内数值限定，超出禁操作</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u102" class="ax_default label">
          <img id="u102_img" class="img " src="images/整体设计说明/u60.png"/>
          <!-- Unnamed () -->
          <div id="u103" class="text" style="visibility: visible;">
            <p><span>单一商品可选数量(普通)</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u104" class="ax_default label">
          <img id="u104_img" class="img " src="images/整体设计说明/u62.png"/>
          <!-- Unnamed () -->
          <div id="u105" class="text" style="visibility: visible;">
            <p><span>1-999</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u106" class="ax_default label">
          <img id="u106_img" class="img " src="images/整体设计说明/u64.png"/>
          <!-- Unnamed () -->
          <div id="u107" class="text" style="visibility: visible;">
            <p><span>初始值为起卖数，超出禁操作</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u108" class="ax_default label">
          <img id="u108_img" class="img " src="images/整体设计说明/u108.png"/>
          <!-- Unnamed () -->
          <div id="u109" class="text" style="visibility: visible;">
            <p><span>单一商品可选数量(称重)</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u110" class="ax_default label">
          <img id="u110_img" class="img " src="images/整体设计说明/u110.png"/>
          <!-- Unnamed () -->
          <div id="u111" class="text" style="visibility: visible;">
            <p><span>0.001-999.999</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u112" class="ax_default label">
          <img id="u112_img" class="img " src="images/整体设计说明/u112.png"/>
          <!-- Unnamed () -->
          <div id="u113" class="text" style="visibility: visible;">
            <p><span>初始值为起卖数，超出禁操作</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u114" class="ax_default label">
          <img id="u114_img" class="img " src="images/整体设计说明/u60.png"/>
          <!-- Unnamed () -->
          <div id="u115" class="text" style="visibility: visible;">
            <p><span>单笔订单最大可选数量(普通)</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u116" class="ax_default label">
          <img id="u116_img" class="img " src="images/整体设计说明/u62.png"/>
          <!-- Unnamed () -->
          <div id="u117" class="text" style="visibility: visible;">
            <p><span>1-9999</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u118" class="ax_default label">
          <img id="u118_img" class="img " src="images/整体设计说明/u64.png"/>
          <!-- Unnamed () -->
          <div id="u119" class="text" style="visibility: visible;">
            <p style="font-size:13px;"><span style="font-size:12px;color:#1E1E1E;">超出显示：</span><span>您已超出订单可选数量，最大可选数量为9999.999</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u120" class="ax_default label">
          <img id="u120_img" class="img " src="images/整体设计说明/u60.png"/>
          <!-- Unnamed () -->
          <div id="u121" class="text" style="visibility: visible;">
            <p><span>单笔订单最大可选数量(含称重)</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u122" class="ax_default label">
          <img id="u122_img" class="img " src="images/整体设计说明/u62.png"/>
          <!-- Unnamed () -->
          <div id="u123" class="text" style="visibility: visible;">
            <p><span>0.001-9999.999</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u124" class="ax_default label">
          <img id="u124_img" class="img " src="images/整体设计说明/u64.png"/>
          <!-- Unnamed () -->
          <div id="u125" class="text" style="visibility: visible;">
            <p style="font-size:13px;"><span style="font-size:12px;color:#1E1E1E;">超出显示：</span><span>您已超出订单可选数量，最大可选数量为9999.999</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u126" class="ax_default label">
          <img id="u126_img" class="img " src="images/整体设计说明/u126.png"/>
          <!-- Unnamed () -->
          <div id="u127" class="text" style="visibility: visible;">
            <p><span>单笔订单最大订单金额</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u128" class="ax_default label">
          <img id="u128_img" class="img " src="images/整体设计说明/u128.png"/>
          <!-- Unnamed () -->
          <div id="u129" class="text" style="visibility: visible;">
            <p><span>999999999.99</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u130" class="ax_default label">
          <img id="u130_img" class="img " src="images/整体设计说明/u130.png"/>
          <!-- Unnamed () -->
          <div id="u131" class="text" style="visibility: visible;">
            <p><span>超出时提示：您已超出订单最高金额，订单最高金额为999999999.99</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u132" class="ax_default label">
          <img id="u132_img" class="img " src="images/整体设计说明/u132.png"/>
          <!-- Unnamed () -->
          <div id="u133" class="text" style="visibility: visible;">
            <p><span>整单备注输入框</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u134" class="ax_default label">
          <img id="u134_img" class="img " src="images/整体设计说明/u134.png"/>
          <!-- Unnamed () -->
          <div id="u135" class="text" style="visibility: visible;">
            <p><span>可输入0-30个汉字</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u136" class="ax_default label">
          <img id="u136_img" class="img " src="images/整体设计说明/u136.png"/>
          <!-- Unnamed () -->
          <div id="u137" class="text" style="visibility: visible;">
            <p><span>限制不允许输入</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u138" class="ax_default label">
        <div id="u138_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u139" class="text" style="visibility: visible;">
          <p><span>字段限制说明： </span></p>
        </div>
      </div>
    </div>
  </body>
</html>
