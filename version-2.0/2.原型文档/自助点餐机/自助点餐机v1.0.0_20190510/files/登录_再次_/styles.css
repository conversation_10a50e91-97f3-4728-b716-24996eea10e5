body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1060px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u189_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u189 {
  position:absolute;
  left:10px;
  top:10px;
  width:540px;
  height:805px;
}
#u190 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u191_img {
  position:absolute;
  left:0px;
  top:0px;
  width:298px;
  height:42px;
}
#u191 {
  position:absolute;
  left:175px;
  top:63px;
  width:298px;
  height:42px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:30px;
  color:#999999;
}
#u192 {
  position:absolute;
  left:0px;
  top:0px;
  width:298px;
  word-wrap:break-word;
}
#u193_img {
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:50px;
}
#u193 {
  position:absolute;
  left:46px;
  top:55px;
  width:119px;
  height:50px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:36px;
  color:#999999;
  text-align:center;
}
#u194 {
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  word-wrap:break-word;
}
#u195 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u196 {
  position:absolute;
  left:91px;
  top:401px;
  width:374px;
  height:60px;
}
#u196_input {
  position:absolute;
  left:0px;
  top:0px;
  width:374px;
  height:60px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:28px;
  text-decoration:none;
  color:#666666;
  text-align:left;
}
#u197_div {
  position:absolute;
  left:0px;
  top:0px;
  width:383px;
  height:60px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:12px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u197 {
  position:absolute;
  left:82px;
  top:585px;
  width:383px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u198 {
  position:absolute;
  left:2px;
  top:10px;
  width:379px;
  word-wrap:break-word;
}
#u199 {
  position:absolute;
  left:91px;
  top:488px;
  width:374px;
  height:60px;
}
#u199_input {
  position:absolute;
  left:0px;
  top:0px;
  width:374px;
  height:60px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:28px;
  text-decoration:none;
  color:#666666;
  text-align:left;
}
#u200_img {
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:26px;
}
#u200 {
  position:absolute;
  left:96px;
  top:347px;
  width:28px;
  height:26px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:28px;
  color:#999999;
  text-align:center;
}
#u201 {
  position:absolute;
  left:0px;
  top:5px;
  width:28px;
  visibility:hidden;
  word-wrap:break-word;
}
#u202_img {
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:26px;
}
#u202 {
  position:absolute;
  left:96px;
  top:418px;
  width:28px;
  height:26px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:28px;
  color:#999999;
  text-align:center;
}
#u203 {
  position:absolute;
  left:0px;
  top:5px;
  width:28px;
  visibility:hidden;
  word-wrap:break-word;
}
#u204_img {
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:26px;
}
#u204 {
  position:absolute;
  left:96px;
  top:505px;
  width:28px;
  height:26px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:28px;
  color:#999999;
  text-align:center;
}
#u205 {
  position:absolute;
  left:0px;
  top:5px;
  width:28px;
  visibility:hidden;
  word-wrap:break-word;
}
#u206_img {
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:100px;
}
#u206 {
  position:absolute;
  left:348px;
  top:230px;
  width:168px;
  height:100px;
}
#u207 {
  position:absolute;
  left:2px;
  top:42px;
  width:164px;
  visibility:hidden;
  word-wrap:break-word;
}
#u208_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:196px;
}
#u208 {
  position:absolute;
  left:429px;
  top:184px;
  width:1px;
  height:195px;
  -webkit-transform:rotate(300deg);
  -moz-transform:rotate(300deg);
  -ms-transform:rotate(300deg);
  transform:rotate(300deg);
}
#u209 {
  position:absolute;
  left:2px;
  top:90px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u210_img {
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:18px;
}
#u210 {
  position:absolute;
  left:428px;
  top:258px;
  width:82px;
  height:18px;
  -webkit-transform:rotate(30deg);
  -moz-transform:rotate(30deg);
  -ms-transform:rotate(30deg);
  transform:rotate(30deg);
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  text-align:center;
}
#u211 {
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  word-wrap:break-word;
}
#u212_img {
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:11px;
}
#u212 {
  position:absolute;
  left:93px;
  top:461px;
  width:66px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  color:#FF0000;
  text-align:center;
}
#u213 {
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  white-space:nowrap;
}
#u214_img {
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:40px;
}
#u214 {
  position:absolute;
  left:148px;
  top:340px;
  width:169px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:28px;
  text-align:center;
}
#u215 {
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  white-space:nowrap;
}
#u216_img {
  position:absolute;
  left:0px;
  top:0px;
  width:435px;
  height:51px;
}
#u216 {
  position:absolute;
  left:625px;
  top:17px;
  width:435px;
  height:51px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u217 {
  position:absolute;
  left:0px;
  top:0px;
  width:435px;
  white-space:nowrap;
}
