body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:550px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u1193_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1193 {
  position:absolute;
  left:10px;
  top:10px;
  width:540px;
  height:805px;
}
#u1194 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1195_div {
  position:absolute;
  left:0px;
  top:0px;
  width:538px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:left;
}
#u1195 {
  position:absolute;
  left:12px;
  top:12px;
  width:538px;
  height:60px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:left;
}
#u1196 {
  position:absolute;
  left:2px;
  top:16px;
  width:534px;
  word-wrap:break-word;
}
#u1197_img {
  position:absolute;
  left:0px;
  top:0px;
  width:273px;
  height:26px;
}
#u1197 {
  position:absolute;
  left:104px;
  top:82px;
  width:273px;
  height:26px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u1198 {
  position:absolute;
  left:0px;
  top:2px;
  width:273px;
  white-space:nowrap;
}
#u1199_div {
  position:absolute;
  left:0px;
  top:0px;
  width:420px;
  height:14px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1199 {
  position:absolute;
  left:68px;
  top:132px;
  width:420px;
  height:14px;
}
#u1200 {
  position:absolute;
  left:2px;
  top:-1px;
  width:416px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1201_div {
  position:absolute;
  left:0px;
  top:0px;
  width:447px;
  height:11px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1201 {
  position:absolute;
  left:41px;
  top:165px;
  width:447px;
  height:11px;
}
#u1202 {
  position:absolute;
  left:2px;
  top:-2px;
  width:443px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1203_div {
  position:absolute;
  left:0px;
  top:0px;
  width:447px;
  height:11px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1203 {
  position:absolute;
  left:41px;
  top:192px;
  width:447px;
  height:11px;
}
#u1204 {
  position:absolute;
  left:2px;
  top:-2px;
  width:443px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1205_div {
  position:absolute;
  left:0px;
  top:0px;
  width:447px;
  height:11px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1205 {
  position:absolute;
  left:41px;
  top:213px;
  width:447px;
  height:11px;
}
#u1206 {
  position:absolute;
  left:2px;
  top:-2px;
  width:443px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1207_div {
  position:absolute;
  left:0px;
  top:0px;
  width:447px;
  height:11px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1207 {
  position:absolute;
  left:41px;
  top:234px;
  width:447px;
  height:11px;
}
#u1208 {
  position:absolute;
  left:2px;
  top:-2px;
  width:443px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1209_div {
  position:absolute;
  left:0px;
  top:0px;
  width:447px;
  height:11px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1209 {
  position:absolute;
  left:41px;
  top:255px;
  width:447px;
  height:11px;
}
#u1210 {
  position:absolute;
  left:2px;
  top:-2px;
  width:443px;
  visibility:hidden;
  word-wrap:break-word;
}
