#recordPlayHost {
    font-size: 12px;
    color:#333;
    height: 100%;
}


#recordPlayContainer 
{
    overflow: auto;
    width: 100%;
    height: 100%;
    padding: 10px 10px 10px 10px;
}

#recordPlayToolbar 
{
	margin: 5px 5px 5px 5px;
    height: 22px;
}

#recordPlayToolbar .recordPlayButton
{
    float: left;
    width: 22px;
    height: 22px;
    border: 1px solid transparent;
}

#recordPlayToolbar .recordPlayButton:hover
{
    border: 1px solid rgb(0,157,217);
    background-color : rgb(166,221,242);
}

#recordPlayToolbar .recordPlayButton:active
{
    border: 1px solid rgb(0,157,217);
    background-color : rgb(204,235,248);
}

#recordPlayToolbar .recordPlayButtonSelected {
    border: 1px solid rgb(0,157,217);
    background-color : rgb(204,235,248);    
}

#recordButton {
    background: url('../../sitemap/styles/images/233_hyperlink_16.png') no-repeat center center;
}

#playButton {
    background: url('../../sitemap/styles/images/225_responsive_16.png') no-repeat center center;
}

#stopButton {
    background: url('../../sitemap/styles/images/228_togglenotes_16.png') no-repeat center center;
}

#deleteButton {
    background: url('../../sitemap/styles/images/231_event_16.png') no-repeat center center;
}

#recordNameHeader
{
    /* yeah??*/
	font-size: 13px;
	font-weight: bold;
	height: 23px;
	white-space: nowrap;
}

#recordPlayContent 
{
    /* yeah??*/
	overflow: visible;
}

.recordPlayName 
{
	font-size: 12px;
	margin-bottom: 5px;
	text-decoration: underline;
	white-space: nowrap;
}

.recordPlay 
{
	margin-bottom: 10px;
}