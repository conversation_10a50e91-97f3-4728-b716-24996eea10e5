<!DOCTYPE html>
<html>
  <head>
    <title>原型更新记录</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/原型更新记录/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/原型更新记录/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- clsuter (Table) -->
      <div id="u154" class="ax_default" data-label="clsuter">

        <!-- Unnamed (Table Cell) -->
        <div id="u155" class="ax_default table_cell">
          <img id="u155_img" class="img " src="images/原型更新记录/u155.png"/>
          <!-- Unnamed () -->
          <div id="u156" class="text" style="visibility: visible;">
            <p><span>修改时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u157" class="ax_default table_cell">
          <img id="u157_img" class="img " src="images/原型更新记录/u157.png"/>
          <!-- Unnamed () -->
          <div id="u158" class="text" style="visibility: visible;">
            <p><span>业务路径</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u159" class="ax_default table_cell">
          <img id="u159_img" class="img " src="images/原型更新记录/u159.png"/>
          <!-- Unnamed () -->
          <div id="u160" class="text" style="visibility: visible;">
            <p><span>修改说明</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u161" class="ax_default table_cell">
          <img id="u161_img" class="img " src="images/原型更新记录/u155.png"/>
          <!-- Unnamed () -->
          <div id="u162" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u163" class="ax_default table_cell">
          <img id="u163_img" class="img " src="images/原型更新记录/u157.png"/>
          <!-- Unnamed () -->
          <div id="u164" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u165" class="ax_default table_cell">
          <img id="u165_img" class="img " src="images/原型更新记录/u159.png"/>
          <!-- Unnamed () -->
          <div id="u166" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u167" class="ax_default table_cell">
          <img id="u167_img" class="img " src="images/原型更新记录/u167.png"/>
          <!-- Unnamed () -->
          <div id="u168" class="text" style="visibility: visible;">
            <p><span>2019年05月27日</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u169" class="ax_default table_cell">
          <img id="u169_img" class="img " src="images/原型更新记录/u169.png"/>
          <!-- Unnamed () -->
          <div id="u170" class="text" style="visibility: visible;">
            <p><span>1.设置-登录验证</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u171" class="ax_default table_cell">
          <img id="u171_img" class="img " src="images/原型更新记录/u171.png"/>
          <!-- Unnamed () -->
          <div id="u172" class="text" style="visibility: visible;">
            <p><span>增加页面标题说明“正在进入设置页，请进行验证”</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u173" class="ax_default table_cell">
          <img id="u173_img" class="img " src="images/原型更新记录/u173.png"/>
          <!-- Unnamed () -->
          <div id="u174" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u175" class="ax_default table_cell">
          <img id="u175_img" class="img " src="images/原型更新记录/u175.png"/>
          <!-- Unnamed () -->
          <div id="u176" class="text" style="visibility: visible;">
            <p><span>2.设置页面-广告轮播等待时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u177" class="ax_default table_cell">
          <img id="u177_img" class="img " src="images/原型更新记录/u177.png"/>
          <!-- Unnamed () -->
          <div id="u178" class="text" style="visibility: visible;">
            <p><span>1)轮播间隔时间由“1-60秒”改为“1秒 3秒 5秒 10秒”</span></p><p><span>2)默认轮播时间10s,调为5s</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u179" class="ax_default table_cell">
          <img id="u179_img" class="img " src="images/原型更新记录/u155.png"/>
          <!-- Unnamed () -->
          <div id="u180" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u181" class="ax_default table_cell">
          <img id="u181_img" class="img " src="images/原型更新记录/u157.png"/>
          <!-- Unnamed () -->
          <div id="u182" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u183" class="ax_default table_cell">
          <img id="u183_img" class="img " src="images/原型更新记录/u159.png"/>
          <!-- Unnamed () -->
          <div id="u184" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u185" class="ax_default table_cell">
          <img id="u185_img" class="img " src="images/原型更新记录/u173.png"/>
          <!-- Unnamed () -->
          <div id="u186" class="text" style="visibility: visible;">
            <p><span>2019年06月25日</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u187" class="ax_default table_cell">
          <img id="u187_img" class="img " src="images/原型更新记录/u175.png"/>
          <!-- Unnamed () -->
          <div id="u188" class="text" style="visibility: visible;">
            <p><span>点餐-使用优惠券</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u189" class="ax_default table_cell">
          <img id="u189_img" class="img " src="images/原型更新记录/u177.png"/>
          <!-- Unnamed () -->
          <div id="u190" class="text" style="visibility: visible;">
            <p><span>增加优惠券使用业务</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u191" class="ax_default table_cell">
          <img id="u191_img" class="img " src="images/原型更新记录/u173.png"/>
          <!-- Unnamed () -->
          <div id="u192" class="text" style="visibility: visible;">
            <p><span>2019年07月05日</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u193" class="ax_default table_cell">
          <img id="u193_img" class="img " src="images/原型更新记录/u175.png"/>
          <!-- Unnamed () -->
          <div id="u194" class="text" style="visibility: visible;">
            <p><span>去结账--使用优惠</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u195" class="ax_default table_cell">
          <img id="u195_img" class="img " src="images/原型更新记录/u177.png"/>
          <!-- Unnamed () -->
          <div id="u196" class="text" style="visibility: visible;">
            <p><span>因时间和实现需求差异。优惠券入口调整：从已选商品列表调整到结账页面上半部分</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u197" class="ax_default table_cell">
          <img id="u197_img" class="img " src="images/原型更新记录/u197.png"/>
          <!-- Unnamed () -->
          <div id="u198" class="text" style="visibility: visible;">
            <p><span>2019年07月18日</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u199" class="ax_default table_cell">
          <img id="u199_img" class="img " src="images/原型更新记录/u199.png"/>
          <!-- Unnamed () -->
          <div id="u200" class="text" style="visibility: visible;">
            <p><span>点餐主页</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u201" class="ax_default table_cell">
          <img id="u201_img" class="img " src="images/原型更新记录/u201.png"/>
          <!-- Unnamed () -->
          <div id="u202" class="text" style="visibility: visible;">
            <p><span>技术接通估清功能：</span></p><p><span>1）商品列表正确反馈估清信息，当商品被估清或者起卖数＞剩余数时，显示商品售罄，禁用选购操作</span></p><p><span>2）当一体机手动估清操作，同步更新商品销售状态</span></p><p><span>3）当支付时，扣减库存失败，已选列表增加此类异常商品的信息反馈</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u203" class="ax_default table_cell">
          <img id="u203_img" class="img " src="images/原型更新记录/u203.png"/>
          <!-- Unnamed () -->
          <div id="u204" class="text" style="visibility: visible;">
            <p><span>2019年07月18日</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u205" class="ax_default table_cell">
          <img id="u205_img" class="img " src="images/原型更新记录/u205.png"/>
          <!-- Unnamed () -->
          <div id="u206" class="text" style="visibility: visible;">
            <p><span>点餐--去结账--进行支付</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u207" class="ax_default table_cell">
          <img id="u207_img" class="img " src="images/原型更新记录/u207.png"/>
          <!-- Unnamed () -->
          <div id="u208" class="text" style="visibility: visible;">
            <p><span>当开启估清功能后，在进行支付的时候，扣架库存失败，正确反馈信息，并提供操作：</span></p><p><span>重新选购：作废原订单并返回到商品列表。刷新商品列表数据；清空商品已选列表</span></p><p><span>修改商品：作废原订单并返回商品列表。刷新商品列表数据；保留商品数据，并反馈购买异常的商品数据</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u209" class="ax_default table_cell">
          <img id="u209_img" class="img " src="images/原型更新记录/u209.png"/>
          <!-- Unnamed () -->
          <div id="u210" class="text" style="visibility: visible;">
            <p><span>2019年07月22日</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u211" class="ax_default table_cell">
          <img id="u211_img" class="img " src="images/原型更新记录/u211.png"/>
          <!-- Unnamed () -->
          <div id="u212" class="text" style="visibility: visible;">
            <p><span>去结账--使用优惠</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u213" class="ax_default table_cell">
          <img id="u213_img" class="img " src="images/原型更新记录/u213.png"/>
          <!-- Unnamed () -->
          <div id="u214" class="text" style="visibility: visible;">
            <p><span>当优惠抵扣至金额为0时，支付按钮换为(确认下单），下单成功后，正常打印小票，记录订单支付方式为现金，支付金额为0</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u215" class="ax_default table_cell">
        <div id="u215_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u216" class="text" style="visibility: visible;">
          <p><span>2019年05月27日</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u217" class="ax_default table_cell">
        <div id="u217_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u218" class="text" style="visibility: visible;">
          <p><span>&nbsp;所属版本 自助点餐 v1.0.0</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u219" class="ax_default table_cell">
        <div id="u219_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u220" class="text" style="visibility: visible;">
          <p><span>&nbsp;所属版本 自助点餐 v1.1.0</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
