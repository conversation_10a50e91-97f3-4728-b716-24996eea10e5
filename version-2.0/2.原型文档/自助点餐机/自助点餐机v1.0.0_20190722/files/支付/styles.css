body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1843px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u48_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u48 {
  position:absolute;
  left:22px;
  top:21px;
  width:540px;
  height:805px;
}
#u49 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u50_img {
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:230px;
}
#u50 {
  position:absolute;
  left:102px;
  top:128px;
  width:371px;
  height:229px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#000000;
}
#u51 {
  position:absolute;
  left:2px;
  top:106px;
  width:367px;
  visibility:hidden;
  word-wrap:break-word;
}
#u52_div {
  position:absolute;
  left:0px;
  top:0px;
  width:538px;
  height:415px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u52 {
  position:absolute;
  left:23px;
  top:317px;
  width:538px;
  height:415px;
}
#u53 {
  position:absolute;
  left:2px;
  top:200px;
  width:534px;
  visibility:hidden;
  word-wrap:break-word;
}
#u54_img {
  position:absolute;
  left:0px;
  top:0px;
  width:237px;
  height:51px;
}
#u54 {
  position:absolute;
  left:166px;
  top:345px;
  width:237px;
  height:51px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  text-align:center;
}
#u55 {
  position:absolute;
  left:0px;
  top:0px;
  width:237px;
  word-wrap:break-word;
}
#u56_img {
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
}
#u56 {
  position:absolute;
  left:250px;
  top:45px;
  width:73px;
  height:25px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:18px;
  text-align:center;
}
#u57 {
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  white-space:nowrap;
}
#u58_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:37px;
}
#u58 {
  position:absolute;
  left:81px;
  top:482px;
  width:173px;
  height:37px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u58_img.selected {
}
#u58.selected {
}
#u58_img.disabled {
}
#u58.disabled {
}
#u59 {
  position:absolute;
  left:2px;
  top:10px;
  width:169px;
  word-wrap:break-word;
}
#u60_img {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:11px;
}
#u60 {
  position:absolute;
  left:182px;
  top:754px;
  width:221px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-align:center;
}
#u61 {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  word-wrap:break-word;
}
#u62_img {
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:19px;
}
#u62 {
  position:absolute;
  left:240px;
  top:251px;
  width:116px;
  height:19px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  color:#000000;
  text-align:left;
}
#u63 {
  position:absolute;
  left:2px;
  top:4px;
  width:112px;
  word-wrap:break-word;
}
#u64_div {
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:188px;
  background:inherit;
  background-color:rgba(0, 0, 0, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u64 {
  position:absolute;
  left:281px;
  top:479px;
  width:204px;
  height:188px;
}
#u65 {
  position:absolute;
  left:2px;
  top:86px;
  width:200px;
  visibility:hidden;
  word-wrap:break-word;
}
#u66_img {
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:37px;
}
#u66 {
  position:absolute;
  left:79px;
  top:547px;
  width:175px;
  height:37px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u66_img.selected {
}
#u66.selected {
}
#u66_img.disabled {
}
#u66.disabled {
}
#u67 {
  position:absolute;
  left:2px;
  top:10px;
  width:171px;
  word-wrap:break-word;
}
#u68_img {
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:37px;
}
#u68 {
  position:absolute;
  left:79px;
  top:606px;
  width:175px;
  height:37px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u68_img.selected {
}
#u68.selected {
}
#u68_img.disabled {
}
#u68.disabled {
}
#u69 {
  position:absolute;
  left:2px;
  top:10px;
  width:171px;
  word-wrap:break-word;
}
#u70_img {
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:28px;
}
#u70 {
  position:absolute;
  left:44px;
  top:34px;
  width:23px;
  height:28px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u71 {
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  word-wrap:break-word;
}
#u72_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:197px;
  height:6px;
}
#u72 {
  position:absolute;
  left:283px;
  top:561px;
  width:194px;
  height:3px;
}
#u73 {
  position:absolute;
  left:2px;
  top:-6px;
  width:190px;
  visibility:hidden;
  word-wrap:break-word;
}
#u74_img {
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:11px;
}
#u74 {
  position:absolute;
  left:292px;
  top:465px;
  width:182px;
  height:11px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:8px;
  text-align:center;
}
#u75 {
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  word-wrap:break-word;
}
#u76_img {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  height:11px;
}
#u76 {
  position:absolute;
  left:243px;
  top:217px;
  width:164px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
}
#u77 {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  word-wrap:break-word;
}
#u78_img {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:11px;
}
#u78 {
  position:absolute;
  left:243px;
  top:179px;
  width:108px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
}
#u79 {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  white-space:nowrap;
}
#u80_img {
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:2px;
}
#u80 {
  position:absolute;
  left:186px;
  top:189px;
  width:209px;
  height:1px;
}
#u81 {
  position:absolute;
  left:2px;
  top:-8px;
  width:205px;
  visibility:hidden;
  word-wrap:break-word;
}
#u82_img {
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:2px;
}
#u82 {
  position:absolute;
  left:186px;
  top:235px;
  width:209px;
  height:1px;
}
#u83 {
  position:absolute;
  left:2px;
  top:-8px;
  width:205px;
  visibility:hidden;
  word-wrap:break-word;
}
#u84_img {
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:2px;
}
#u84 {
  position:absolute;
  left:186px;
  top:280px;
  width:209px;
  height:1px;
}
#u85 {
  position:absolute;
  left:2px;
  top:-8px;
  width:205px;
  visibility:hidden;
  word-wrap:break-word;
}
#u87_div {
  position:absolute;
  left:0px;
  top:0px;
  width:540px;
  height:805px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u87 {
  position:absolute;
  left:618px;
  top:21px;
  width:540px;
  height:805px;
}
#u88 {
  position:absolute;
  left:2px;
  top:394px;
  width:536px;
  visibility:hidden;
  word-wrap:break-word;
}
#u89_img {
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:230px;
}
#u89 {
  position:absolute;
  left:698px;
  top:128px;
  width:371px;
  height:229px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#000000;
}
#u90 {
  position:absolute;
  left:2px;
  top:106px;
  width:367px;
  visibility:hidden;
  word-wrap:break-word;
}
#u91_div {
  position:absolute;
  left:0px;
  top:0px;
  width:538px;
  height:415px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u91 {
  position:absolute;
  left:619px;
  top:317px;
  width:538px;
  height:415px;
}
#u92 {
  position:absolute;
  left:2px;
  top:200px;
  width:534px;
  visibility:hidden;
  word-wrap:break-word;
}
#u93_img {
  position:absolute;
  left:0px;
  top:0px;
  width:237px;
  height:51px;
}
#u93 {
  position:absolute;
  left:762px;
  top:345px;
  width:237px;
  height:51px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  text-align:center;
}
#u94 {
  position:absolute;
  left:0px;
  top:0px;
  width:237px;
  word-wrap:break-word;
}
#u95_img {
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
}
#u95 {
  position:absolute;
  left:846px;
  top:45px;
  width:73px;
  height:25px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:18px;
  text-align:center;
}
#u96 {
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  white-space:nowrap;
}
#u97_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:37px;
}
#u97 {
  position:absolute;
  left:677px;
  top:486px;
  width:173px;
  height:37px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u97_img.selected {
}
#u97.selected {
}
#u97_img.disabled {
}
#u97.disabled {
}
#u98 {
  position:absolute;
  left:2px;
  top:10px;
  width:169px;
  word-wrap:break-word;
}
#u99_img {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:11px;
}
#u99 {
  position:absolute;
  left:778px;
  top:754px;
  width:221px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  text-align:center;
}
#u100 {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  word-wrap:break-word;
}
#u101_img {
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:19px;
}
#u101 {
  position:absolute;
  left:836px;
  top:251px;
  width:116px;
  height:19px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
  color:#000000;
  text-align:left;
}
#u102 {
  position:absolute;
  left:2px;
  top:4px;
  width:112px;
  word-wrap:break-word;
}
#u103_div {
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:188px;
  background:inherit;
  background-color:rgba(0, 0, 0, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u103 {
  position:absolute;
  left:877px;
  top:479px;
  width:204px;
  height:188px;
}
#u104 {
  position:absolute;
  left:2px;
  top:86px;
  width:200px;
  visibility:hidden;
  word-wrap:break-word;
}
#u105_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:37px;
}
#u105 {
  position:absolute;
  left:677px;
  top:551px;
  width:173px;
  height:37px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u105_img.selected {
}
#u105.selected {
}
#u105_img.disabled {
}
#u105.disabled {
}
#u106 {
  position:absolute;
  left:2px;
  top:10px;
  width:169px;
  word-wrap:break-word;
}
#u107_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:37px;
}
#u107 {
  position:absolute;
  left:677px;
  top:610px;
  width:173px;
  height:37px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u107_img.selected {
}
#u107.selected {
}
#u107_img.disabled {
}
#u107.disabled {
}
#u108 {
  position:absolute;
  left:2px;
  top:10px;
  width:169px;
  word-wrap:break-word;
}
#u109_img {
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:28px;
}
#u109 {
  position:absolute;
  left:640px;
  top:34px;
  width:23px;
  height:28px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u110 {
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  word-wrap:break-word;
}
#u111_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:197px;
  height:6px;
}
#u111 {
  position:absolute;
  left:879px;
  top:561px;
  width:194px;
  height:3px;
}
#u112 {
  position:absolute;
  left:2px;
  top:-6px;
  width:190px;
  visibility:hidden;
  word-wrap:break-word;
}
#u113_img {
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:11px;
}
#u113 {
  position:absolute;
  left:888px;
  top:465px;
  width:182px;
  height:11px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:8px;
  text-align:center;
}
#u114 {
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  word-wrap:break-word;
}
#u115_img {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  height:11px;
}
#u115 {
  position:absolute;
  left:839px;
  top:217px;
  width:164px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
}
#u116 {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  word-wrap:break-word;
}
#u117_img {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:11px;
}
#u117 {
  position:absolute;
  left:839px;
  top:179px;
  width:108px;
  height:11px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:8px;
}
#u118 {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  white-space:nowrap;
}
#u119_img {
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:2px;
}
#u119 {
  position:absolute;
  left:782px;
  top:189px;
  width:209px;
  height:1px;
}
#u120 {
  position:absolute;
  left:2px;
  top:-8px;
  width:205px;
  visibility:hidden;
  word-wrap:break-word;
}
#u121_img {
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:2px;
}
#u121 {
  position:absolute;
  left:782px;
  top:235px;
  width:209px;
  height:1px;
}
#u122 {
  position:absolute;
  left:2px;
  top:-8px;
  width:205px;
  visibility:hidden;
  word-wrap:break-word;
}
#u123_img {
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:2px;
}
#u123 {
  position:absolute;
  left:782px;
  top:280px;
  width:209px;
  height:1px;
}
#u124 {
  position:absolute;
  left:2px;
  top:-8px;
  width:205px;
  visibility:hidden;
  word-wrap:break-word;
}
#u125_img {
  position:absolute;
  left:0px;
  top:0px;
  width:377px;
  height:65px;
}
#u125 {
  position:absolute;
  left:1266px;
  top:60px;
  width:377px;
  height:65px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u126 {
  position:absolute;
  left:0px;
  top:0px;
  width:377px;
  word-wrap:break-word;
}
#u127 {
  position:absolute;
  left:1248px;
  top:252px;
  width:0px;
  height:0px;
  text-align:left;
}
#u127_seg0 {
  position:absolute;
  left:-4px;
  top:0px;
  width:8px;
  height:7px;
}
#u127_seg1 {
  position:absolute;
  left:-286px;
  top:-1px;
  width:290px;
  height:8px;
}
#u127_seg2 {
  position:absolute;
  left:-295px;
  top:-7px;
  width:21px;
  height:20px;
}
#u128 {
  position:absolute;
  left:-192px;
  top:-5px;
  width:100px;
  visibility:hidden;
  word-wrap:break-word;
}
#u129 {
  position:absolute;
  left:1260px;
  top:149px;
  width:0px;
  height:0px;
  text-align:left;
}
#u129_seg0 {
  position:absolute;
  left:-28px;
  top:-4px;
  width:28px;
  height:8px;
}
#u129_seg1 {
  position:absolute;
  left:-28px;
  top:-4px;
  width:8px;
  height:8px;
}
#u129_seg2 {
  position:absolute;
  left:-70px;
  top:-4px;
  width:50px;
  height:8px;
}
#u129_seg3 {
  position:absolute;
  left:-70px;
  top:-142px;
  width:8px;
  height:146px;
}
#u129_seg4 {
  position:absolute;
  left:-674px;
  top:-142px;
  width:612px;
  height:8px;
}
#u129_seg5 {
  position:absolute;
  left:-674px;
  top:-142px;
  width:8px;
  height:258px;
}
#u129_seg6 {
  position:absolute;
  left:-904px;
  top:108px;
  width:238px;
  height:8px;
}
#u129_seg7 {
  position:absolute;
  left:-913px;
  top:102px;
  width:21px;
  height:20px;
}
#u130 {
  position:absolute;
  left:-558px;
  top:-146px;
  width:100px;
  visibility:hidden;
  word-wrap:break-word;
}
#u131 {
  position:absolute;
  left:1252px;
  top:372px;
  width:0px;
  height:0px;
  text-align:left;
}
#u131_seg0 {
  position:absolute;
  left:-281px;
  top:-4px;
  width:285px;
  height:8px;
}
#u131_seg1 {
  position:absolute;
  left:-290px;
  top:-10px;
  width:21px;
  height:20px;
}
#u132 {
  position:absolute;
  left:-190px;
  top:-8px;
  width:100px;
  visibility:hidden;
  word-wrap:break-word;
}
#u133_img {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:17px;
}
#u133 {
  position:absolute;
  left:1266px;
  top:362px;
  width:300px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u134 {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  word-wrap:break-word;
}
#u135_img {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:82px;
}
#u135 {
  position:absolute;
  left:1266px;
  top:135px;
  width:300px;
  height:82px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u136 {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  word-wrap:break-word;
}
#u137_img {
  position:absolute;
  left:0px;
  top:0px;
  width:342px;
  height:65px;
}
#u137 {
  position:absolute;
  left:1266px;
  top:240px;
  width:342px;
  height:65px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u138 {
  position:absolute;
  left:0px;
  top:0px;
  width:342px;
  word-wrap:break-word;
}
#u139 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u140_div {
  position:absolute;
  left:0px;
  top:0px;
  width:478px;
  height:257px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u140 {
  position:absolute;
  left:1275px;
  top:465px;
  width:478px;
  height:257px;
}
#u141 {
  position:absolute;
  left:2px;
  top:120px;
  width:474px;
  visibility:hidden;
  word-wrap:break-word;
}
#u142_img {
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:17px;
}
#u142 {
  position:absolute;
  left:1323px;
  top:585px;
  width:181px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u143 {
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  white-space:nowrap;
}
#u144_img {
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:53px;
}
#u144 {
  position:absolute;
  left:1438px;
  top:500px;
  width:171px;
  height:53px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u145 {
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  word-wrap:break-word;
}
#u146_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u146 {
  position:absolute;
  left:1323px;
  top:638px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u147 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u148_div {
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:53px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u148 {
  position:absolute;
  left:1526px;
  top:638px;
  width:172px;
  height:53px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u149 {
  position:absolute;
  left:2px;
  top:18px;
  width:168px;
  word-wrap:break-word;
}
#u150_img {
  position:absolute;
  left:0px;
  top:0px;
  width:565px;
  height:34px;
}
#u150 {
  position:absolute;
  left:1278px;
  top:735px;
  width:565px;
  height:34px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u151 {
  position:absolute;
  left:0px;
  top:0px;
  width:565px;
  white-space:nowrap;
}
#u152_img {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:34px;
}
#u152 {
  position:absolute;
  left:1277px;
  top:421px;
  width:300px;
  height:34px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u153 {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  word-wrap:break-word;
}
