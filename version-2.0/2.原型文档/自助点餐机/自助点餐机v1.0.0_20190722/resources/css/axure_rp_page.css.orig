/* so the window resize fires within a frame in IE7 */
html, body {
    height: 100%;    
}

p {
	margin: 0px;
}

iframe {
	background: #FFFFFF;
}

/* to match IE with C, FF */
input {
    padding: 1px 0px 1px 0px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}

textarea {
    margin: 0px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}

div.intcases {
	font-family: arial; 
	font-size: 12px;
    text-align:left; 
	border:1px solid #AAA; 
	background:#FFF none repeat scroll 0% 0%; 
	z-index:9999; 
	visibility:hidden; 
	position:absolute;
    padding: 0px;
    border-radius: 3px;
	white-space: nowrap;
}

div.intcaselink {
	cursor: pointer;
    padding: 3px 8px 3px 8px;
    margin: 5px;
	background:#EEE none repeat scroll 0% 0%; 
	border:1px solid #AAA;
    border-radius: 3px;
}

div.refpageimage {
	position: absolute;
    left: 0px;
    top: 0px;
	font-size: 0px;
	width: 16px;
	height: 16px;
	cursor: pointer;
	background-image: url(images/newwindow.gif);
	background-repeat: no-repeat;
}

div.annnoteimage {
	position: absolute;
    left: 0px;
    top: 0px;
	font-size: 0px;
	width: 16px;
	height: 12px;
	cursor: help;
	background-image: url(images/note.gif);
	background-repeat: no-repeat;
}

div.annnotelabel {
	position: absolute;
    left: 0px;
    top: 0px;
	font-family: Arial;
	font-size: 10px;
	border: 1px solid rgb(166,221,242);
	cursor: help;
	background:rgb(0,157,217) none repeat scroll 0% 0%; 
	padding-left:3px; 
	padding-right:3px;
	white-space: nowrap;
	color: white;
}
 
.annotationName {
	font-size: 13px;
	font-weight: bold;
	margin-bottom: 3px;
	white-space: nowrap;
}

.annotation {
	font-size: 12px;
	padding-left: 2px;
	margin-bottom: 5px;
}

/* this is a fix for the issue where dialogs jump around and takes the text-align from the body */
.dialogFix {
    position:absolute;
    text-align:left;
}


@keyframes pulsate {
  from {
    box-shadow: 0 0 10px #74BA11;
  }
  to {
    box-shadow: 0 0 20px #74BA11;
  }
}

@-webkit-keyframes pulsate {
  from {
    -webkit-box-shadow: 0 0 10px #74BA11;
    box-shadow: 0 0 10px #74BA11;
  }
  to {
    -webkit-box-shadow: 0 0 20px #74BA11;
    box-shadow: 0 0 20px #74BA11;
  }
}
 
@-moz-keyframes pulsate {
  from {
    -moz-box-shadow: 0 0 10px #74BA11;
    box-shadow: 0 0 10px #74BA11;
  }
  to {
    -moz-box-shadow: 0 0 20px #74BA11;
    box-shadow: 0 0 20px #74BA11;
  }
}

.legacyPulsateBorder {
	border: 5px solid #74BA11;
	margin: -5px;
}

.pulsateBorder {
  animation-name: pulsate;
  animation-timing-function: ease-in-out;
  animation-duration: 0.9s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  
  -webkit-animation-name: pulsate;
  -webkit-animation-timing-function: ease-in-out;
  -webkit-animation-duration: 0.9s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-direction: alternate;

  -moz-animation-name: pulsate;
  -moz-animation-timing-function: ease-in-out;
  -moz-animation-duration: 0.9s;
  -moz-animation-iteration-count: infinite;
  -moz-animation-direction: alternate;
}