.ax_default {
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  color:#333333;
  text-align:center;
  line-height:normal;
}
.box_1 {
}
.box_2 {
}
.box_3 {
}
.ellipse {
}
._形状 {
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  color:#333333;
  text-align:center;
  line-height:normal;
}
.image {
}
.placeholder {
}
.button {
}
.heading_1 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.heading_2 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
.heading_3 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
.heading_4 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
.heading_5 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
.heading_6 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
.label {
  font-size:14px;
  text-align:left;
}
.paragraph {
  text-align:left;
}
.line {
}
.arrow {
}
.text_field {
  color:#000000;
  text-align:left;
}
.text_area {
  color:#000000;
  text-align:left;
}
.droplist {
  color:#000000;
  text-align:left;
}
.list_box {
  color:#000000;
  text-align:left;
}
.checkbox {
  text-align:left;
}
.radio_button {
  text-align:left;
}
.html_button {
  text-align:center;
}
.tree_node {
  text-align:left;
}
.table_cell {
}
.menu_item {
}
.connector {
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
  line-height:normal;
}
.marker {
  color:#FFFFFF;
}
.flow_shape {
}
.table {
}
.shape {
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  color:#333333;
  text-align:center;
  line-height:normal;
}
.horizontal_line {
}
.icon {
}
