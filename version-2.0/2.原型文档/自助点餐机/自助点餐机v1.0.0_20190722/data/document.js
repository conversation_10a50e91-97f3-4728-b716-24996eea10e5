$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z,A,[_(u,B,w,C,y,D),_(u,E,w,x,y,z,A,[_(u,F,w,C,y,G),_(u,H,w,C,y,I),_(u,J,w,C,y,K)])])]),L,_(M,z),N,_(O,P,Q,_(R,S,T,S),U,V),W,[],X,_(Y,_(Z,ba,bb,bc,bd,be,bf,bg,bh,bi,bj,f,bk,_(bl,bm,bn,bo,bp,bq),br,bs,bt,be,bu,_(bv,S,bw,S),Q,_(R,S,T,S),bx,d,by,f,bz,ba,bA,_(bl,bm,bn,bB),bC,_(bl,bm,bn,bD),bE,bF,bG,bm,bp,bF,bH,bI,bJ,bK,bL,bM,bN,bM,bO,bM,bP,bM,bQ,_(),bR,bI,bS,bI,bT,_(bU,f,bV,bW,bX,bW,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ce)),cf,_(bU,f,bV,S,bX,bW,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ce)),cg,_(bU,f,bV,bq,bX,bq,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ch))),ci,_(cj,_(Z,ck),cl,_(Z,cm,bE,bI,bA,_(bl,bm,bn,cn)),co,_(Z,cp,bE,bI,bA,_(bl,bm,bn,cq)),cr,_(Z,cs),ct,_(Z,cu,bb,bc,bd,be,bk,_(bl,bm,bn,bo,bp,bq),bC,_(bl,bm,bn,cv),bE,bF,bA,_(bl,bm,bn,cw),br,bs,bf,bg,bh,bi,bj,f,bG,bm,bH,bI,bp,bF,bT,_(bU,f,bV,bW,bX,bW,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ce)),cf,_(bU,f,bV,S,bX,bW,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ce)),cg,_(bU,f,bV,bq,bX,bq,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ch)),bJ,bK,bL,bM,bN,bM,bO,bM,bP,bM,bt,be),cx,_(Z,cy,bE,bI),cz,_(Z,cA,bA,_(bl,bm,bn,cn)),cB,_(Z,cC,bH,cD),cE,_(Z,cF,bh,cG,bb,cH,bE,bI,bA,_(bl,bm,bn,cI),br,cJ,bJ,cK,bL,bI,bN,bI,bO,bI,bP,bI),cL,_(Z,cM,bh,cN,bb,cH,bE,bI,bA,_(bl,bm,bn,cI),br,cJ,bJ,cK,bL,bI,bN,bI,bO,bI,bP,bI),cO,_(Z,cP,bh,cQ,bb,cH,bE,bI,bA,_(bl,bm,bn,cI),br,cJ,bJ,cK,bL,bI,bN,bI,bO,bI,bP,bI),cR,_(Z,cS,bh,cT,bb,cH,bE,bI,bA,_(bl,bm,bn,cI),br,cJ,bJ,cK,bL,bI,bN,bI,bO,bI,bP,bI),cU,_(Z,cV,bb,cH,bE,bI,bA,_(bl,bm,bn,cI),br,cJ,bJ,cK,bL,bI,bN,bI,bO,bI,bP,bI),cW,_(Z,cX,bh,cY,bb,cH,bE,bI,bA,_(bl,bm,bn,cI),br,cJ,bJ,cK,bL,bI,bN,bI,bO,bI,bP,bI),cZ,_(Z,da,bh,cT,bE,bI,bA,_(bl,bm,bn,cI),br,cJ,bJ,cK,bL,bI,bN,bI,bO,bI,bP,bI),db,_(Z,dc,bE,bI,bA,_(bl,bm,bn,cI),br,cJ,bJ,cK,bL,bI,bN,bI,bO,bI,bP,bI),dd,_(Z,de,bA,_(bl,bm,bn,cI)),df,_(Z,dg,bE,cD,bA,_(bl,bm,bn,cI)),dh,_(Z,di,bk,_(bl,bm,bn,dj,bp,bq),br,cJ,bJ,bK),dk,_(Z,dl,bk,_(bl,bm,bn,dj,bp,bq),br,cJ,bJ,cK),dm,_(Z,dn,bk,_(bl,bm,bn,dj,bp,bq),br,cJ,bJ,cK),dp,_(Z,dq,bk,_(bl,bm,bn,dj,bp,bq),br,cJ,bJ,cK),dr,_(Z,ds,br,cJ,bJ,cK),dt,_(Z,du,br,cJ,bJ,cK),dv,_(Z,dw,br,bs),dx,_(Z,dy,bE,bI,bA,_(bl,bm,bn,cI),br,cJ,bJ,bK),dz,_(Z,dA),dB,_(Z,dC,bA,_(bl,bm,bn,cI)),dD,_(Z,dE,bb,bc,bd,be,bk,_(bl,bm,bn,dF,bp,bq),bC,_(bl,bm,bn,cv),bE,bF,br,bs,bf,dG,bh,dH,bj,f,bG,bm,bH,bI,bA,_(bl,bm,bn,bB),bp,bF,bT,_(bU,f,bV,bW,bX,bW,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ce)),cf,_(bU,f,bV,S,bX,bW,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ce)),cg,_(bU,f,bV,bq,bX,bq,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ch)),bJ,bK,bL,bM,bN,bM,bO,bM,bP,bM,bt,be),dI,_(Z,dJ,bk,_(bl,bm,bn,bB,bp,bq),bC,_(bl,bm,bn,bB),bA,_(bl,bm,bn,dK),bT,_(bU,d,bV,bq,bX,bq,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,dL))),dM,_(Z,dN,bA,_(bl,dO,dP,[_(bn,bB),_(bn,cn),_(bn,dQ),_(bn,bB)])),dR,_(Z,dS),dT,_(Z,dU,bb,bc,bd,be,bf,bg,bh,bi,bj,f,bk,_(bl,bm,bn,bo,bp,bq),br,bs,bt,be,bA,_(bl,bm,bn,bB),bC,_(bl,bm,bn,bo),bE,bF,bG,bm,bp,bF,bH,bI,bJ,bK,bL,bM,bN,bM,bO,bM,bP,bM,bT,_(bU,f,bV,bW,bX,bW,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ce)),cf,_(bU,f,bV,S,bX,bW,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ce)),cg,_(bU,f,bV,bq,bX,bq,bY,bW,bn,_(bZ,ca,cb,ca,cc,ca,cd,ch))),dV,_(Z,dW,bC,_(bl,bm,bn,dj)),dX,_(Z,dY,bE,bI,bA,_(bl,bm,bn,bo))),dZ,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="商户后台",w="type",x="Folder",y="url",z="",A="children",B="原型更新记录",C="Wireframe",D="原型更新记录.html",E="v1.1.0",F="主页",G="主页.html",H="支付",I="支付.html",J="[对话框]验证抵扣优惠券",K="_对话框_验证抵扣优惠券.html",L="globalVariables",M="onloadvariable",N="defaultAdaptiveView",O="name",P="Base",Q="size",R="width",S=0,T="height",U="condition",V="<=",W="adaptiveViews",X="stylesheet",Y="defaultStyle",Z="id",ba="627587b6038d43cca051c114ac41ad32",bb="fontWeight",bc="400",bd="fontStyle",be="normal",bf="fontName",bg="'ArialMT', 'Arial'",bh="fontSize",bi="13px",bj="underline",bk="foreGroundFill",bl="fillType",bm="solid",bn="color",bo=0xFF333333,bp="opacity",bq=1,br="horizontalAlignment",bs="center",bt="lineSpacing",bu="location",bv="x",bw="y",bx="visible",by="limbo",bz="baseStyle",bA="fill",bB=0xFFFFFFFF,bC="borderFill",bD=0xFF797979,bE="borderWidth",bF="1",bG="linePattern",bH="cornerRadius",bI="0",bJ="verticalAlignment",bK="middle",bL="paddingLeft",bM="2",bN="paddingTop",bO="paddingRight",bP="paddingBottom",bQ="stateStyles",bR="rotation",bS="textRotation",bT="outerShadow",bU="on",bV="offsetX",bW=5,bX="offsetY",bY="blurRadius",bZ="r",ca=0,cb="g",cc="b",cd="a",ce=0.349019607843137,cf="innerShadow",cg="textShadow",ch=0.647058823529412,ci="customStyles",cj="box_1",ck="********************************",cl="box_2",cm="********************************",cn=0xFFF2F2F2,co="box_3",cp="********************************",cq=0xFFD7D7D7,cr="ellipse",cs="eff044fe6497434a8c5f89f769ddde3b",ct="_形状",cu="40519e9ec4264601bfb12c514e4f4867",cv=0xFFCCCCCC,cw=0x19333333,cx="image",cy="75a91ee5b9d042cfa01b8d565fe289c0",cz="placeholder",cA="c50e74f669b24b37bd9c18da7326bccd",cB="button",cC="c9f35713a1cf4e91a0f2dbac65e6fb5c",cD="5",cE="heading_1",cF="1111111151944dfba49f67fd55eb1f88",cG="32px",cH="bold",cI=0xFFFFFF,cJ="left",cK="top",cL="heading_2",cM="b3a15c9ddde04520be40f94c8168891e",cN="24px",cO="heading_3",cP="8c7a4c5ad69a4369a5f7788171ac0b32",cQ="18px",cR="heading_4",cS="e995c891077945c89c0b5fe110d15a0b",cT="14px",cU="heading_5",cV="386b19ef4be143bd9b6c392ded969f89",cW="heading_6",cX="fc3b9a13b5574fa098ef0a1db9aac861",cY="10px",cZ="label",da="2285372321d148ec80932747449c36c9",db="paragraph",dc="4988d43d80b44008a4a415096f1632af",dd="line",de="619b2148ccc1497285562264d51992f9",df="arrow",dg="d148f2c5268542409e72dde43e40043e",dh="text_field",di="44157808f2934100b68f2394a66b2bba",dj=0xFF000000,dk="text_area",dl="42ee17691d13435b8256d8d0a814778f",dm="droplist",dn="85f724022aae41c594175ddac9c289eb",dp="list_box",dq="********************************",dr="checkbox",ds="********************************",dt="radio_button",du="4eb5516f311c4bdfa0cb11d7ea75084e",dv="html_button",dw="eed12d9ebe2e4b9689b3b57949563dca",dx="tree_node",dy="93a4c3353b6f4562af635b7116d6bf94",dz="table_cell",dA="33ea2511485c479dbf973af3302f2352",dB="menu_item",dC="2036b2baccbc41f0b9263a6981a11a42",dD="connector",dE="699a012e142a4bcba964d96e88b88bdf",dF=0xFF0000FF,dG="'PingFangSC-Regular', 'PingFang SC'",dH="12px",dI="marker",dJ="a8e305fe5c2a462b995b0021a9ba82b9",dK=0xFF009DD9,dL=0.698039215686274,dM="flow_shape",dN="df01900e3c4e43f284bafec04b0864c4",dO="linearGradient",dP="colors",dQ=0xFFE4E4E4,dR="table",dS="d612b8c2247342eda6a8bc0663265baa",dT="shape",dU="98c916898e844865a527f56bc61a500d",dV="horizontal_line",dW="f48196c19ab74fb7b3acb5151ce8ea2d",dX="icon",dY="26c731cb771b44a88eb8b6e97e78c80e",dZ="duplicateStyles";
return _creator();
})());