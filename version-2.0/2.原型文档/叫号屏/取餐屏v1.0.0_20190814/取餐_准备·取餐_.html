<!DOCTYPE html>
<html>
  <head>
    <title>取餐(准备·取餐)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/取餐_准备·取餐_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/取餐_准备·取餐_/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (主框架) -->

      <!-- Unnamed (Rectangle) -->
      <div id="u488" class="ax_default box_3">
        <div id="u488_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u489" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u490" class="ax_default box_3">
        <div id="u490_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u491" class="text" style="visibility: visible;">
          <p><span>准备中···</span></p><p><span><br></span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u492" class="ax_default box_3">
        <div id="u492_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u493" class="text" style="visibility: visible;">
          <p><span>请取餐···</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u494" class="ax_default paragraph">
        <img id="u494_img" class="img " src="images/含准备_准备·取餐·ad_/u171.png"/>
        <!-- Unnamed () -->
        <div id="u495" class="text" style="visibility: visible;">
          <p><span>973</span></p><p><span>974</span></p><p><span>975</span></p><p><span>976</span></p><p><span>977</span></p><p><span>978</span></p><p><span>979</span></p><p><span>981</span></p><p><span>982</span></p><p><span>983</span></p><p><span>984</span></p><p><span>985</span></p><p><span>986</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u496" class="ax_default paragraph">
        <img id="u496_img" class="img " src="images/含准备_准备·取餐·ad_/u173.png"/>
        <!-- Unnamed () -->
        <div id="u497" class="text" style="visibility: visible;">
          <p><span>988</span></p><p><span>989</span></p><p><span>990</span></p><p><span>991</span></p><p><span>992</span></p><p><span>993</span></p><p><span>994</span></p><p><span>995</span></p><p><span>996</span></p><p><span>997</span></p><p><span>998</span></p><p><span>999</span></p><p><span>1000</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u498" class="ax_default paragraph">
        <img id="u498_img" class="img " src="images/含准备_准备·取餐·ad_/u175.png"/>
        <!-- Unnamed () -->
        <div id="u499" class="text" style="visibility: visible;">
          <p><span>987</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u500" class="ax_default paragraph">
        <img id="u500_img" class="img " src="images/取餐_准备·取餐_/u500.png"/>
        <!-- Unnamed () -->
        <div id="u501" class="text" style="visibility: visible;">
          <p><span>1001</span></p><p><span>1002</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u502" class="ax_default paragraph">
        <img id="u502_img" class="img " src="images/含准备_准备·取餐·ad_/u179.png"/>
        <!-- Unnamed () -->
        <div id="u503" class="text" style="visibility: visible;">
          <p><span>设置</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
