body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1210px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u420_div {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:675px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u420 {
  position:absolute;
  left:10px;
  top:10px;
  width:1200px;
  height:675px;
}
#u421 {
  position:absolute;
  left:2px;
  top:330px;
  width:1196px;
  visibility:hidden;
  word-wrap:break-word;
}
#u422_img {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:100px;
}
#u422 {
  position:absolute;
  left:536px;
  top:264px;
  width:120px;
  height:100px;
}
#u423 {
  position:absolute;
  left:2px;
  top:42px;
  width:116px;
  visibility:hidden;
  word-wrap:break-word;
}
#u424_div {
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:13px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:73px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u424 {
  position:absolute;
  left:1161px;
  top:235px;
  width:13px;
  height:13px;
}
#u425 {
  position:absolute;
  left:2px;
  top:-2px;
  width:9px;
  visibility:hidden;
  word-wrap:break-word;
}
#u426_div {
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:13px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:73px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u426 {
  position:absolute;
  left:1161px;
  top:270px;
  width:13px;
  height:13px;
}
#u427 {
  position:absolute;
  left:2px;
  top:-2px;
  width:9px;
  visibility:hidden;
  word-wrap:break-word;
}
#u428_div {
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:13px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:73px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u428 {
  position:absolute;
  left:1161px;
  top:308px;
  width:13px;
  height:13px;
}
#u429 {
  position:absolute;
  left:2px;
  top:-2px;
  width:9px;
  visibility:hidden;
  word-wrap:break-word;
}
