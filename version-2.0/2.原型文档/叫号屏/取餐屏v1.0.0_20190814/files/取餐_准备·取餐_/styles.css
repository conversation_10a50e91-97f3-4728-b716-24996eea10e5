body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1210px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u488_div {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:675px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u488 {
  position:absolute;
  left:10px;
  top:10px;
  width:1200px;
  height:675px;
}
#u489 {
  position:absolute;
  left:2px;
  top:330px;
  width:1196px;
  visibility:hidden;
  word-wrap:break-word;
}
#u490_div {
  position:absolute;
  left:0px;
  top:0px;
  width:475px;
  height:45px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:28px;
  text-align:left;
}
#u490 {
  position:absolute;
  left:39px;
  top:53px;
  width:475px;
  height:45px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:28px;
  text-align:left;
}
#u491 {
  position:absolute;
  left:2px;
  top:2px;
  width:471px;
  word-wrap:break-word;
}
#u492_div {
  position:absolute;
  left:0px;
  top:0px;
  width:466px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:28px;
  text-align:left;
}
#u492 {
  position:absolute;
  left:658px;
  top:53px;
  width:466px;
  height:45px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:28px;
  text-align:left;
}
#u493 {
  position:absolute;
  left:2px;
  top:2px;
  width:462px;
  word-wrap:break-word;
}
#u494_img {
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:520px;
}
#u494 {
  position:absolute;
  left:674px;
  top:99px;
  width:153px;
  height:520px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:28px;
}
#u495 {
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  word-wrap:break-word;
}
#u496_img {
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:520px;
}
#u496 {
  position:absolute;
  left:51px;
  top:108px;
  width:143px;
  height:520px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:28px;
}
#u497 {
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  word-wrap:break-word;
}
#u498_img {
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:40px;
}
#u498 {
  position:absolute;
  left:760px;
  top:99px;
  width:93px;
  height:40px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:28px;
}
#u499 {
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  word-wrap:break-word;
}
#u500_img {
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:80px;
}
#u500 {
  position:absolute;
  left:139px;
  top:108px;
  width:143px;
  height:80px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:28px;
}
#u501 {
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  word-wrap:break-word;
}
#u502_img {
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
}
#u502 {
  position:absolute;
  left:1153px;
  top:23px;
  width:25px;
  height:17px;
  font-size:12px;
  color:#1E1E1E;
  text-align:center;
}
#u503 {
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
