body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1224px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u504_div {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:675px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u504 {
  position:absolute;
  left:24px;
  top:30px;
  width:1200px;
  height:675px;
}
#u505 {
  position:absolute;
  left:2px;
  top:330px;
  width:1196px;
  visibility:hidden;
  word-wrap:break-word;
}
#u506_div {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:62px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:left;
}
#u506 {
  position:absolute;
  left:24px;
  top:30px;
  width:1200px;
  height:62px;
  text-align:left;
}
#u507 {
  position:absolute;
  left:2px;
  top:22px;
  width:1196px;
  word-wrap:break-word;
}
#u508 {
  position:absolute;
  left:936px;
  top:51px;
  width:186px;
  height:25px;
}
#u508_input {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:25px;
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u509 {
  position:absolute;
  left:854px;
  top:52px;
  width:82px;
  height:22px;
}
#u509_input {
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:22px;
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  text-decoration:none;
  color:#000000;
}
#u509_input:disabled {
  color:grayText;
}
#u510_img {
  position:absolute;
  left:0px;
  top:0px;
  width:1020px;
  height:240px;
}
#u510 {
  position:absolute;
  left:24px;
  top:92px;
  width:1020px;
  height:240px;
}
#u511 {
  position:absolute;
  left:2px;
  top:112px;
  width:1016px;
  visibility:hidden;
  word-wrap:break-word;
}
#u512_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:1195px;
  height:5px;
}
#u512 {
  position:absolute;
  left:24px;
  top:149px;
  width:1192px;
  height:2px;
}
#u513 {
  position:absolute;
  left:2px;
  top:-7px;
  width:1188px;
  visibility:hidden;
  word-wrap:break-word;
}
#u514_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:1195px;
  height:5px;
}
#u514 {
  position:absolute;
  left:24px;
  top:209px;
  width:1192px;
  height:2px;
}
#u515 {
  position:absolute;
  left:2px;
  top:-7px;
  width:1188px;
  visibility:hidden;
  word-wrap:break-word;
}
#u516_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:1195px;
  height:5px;
}
#u516 {
  position:absolute;
  left:24px;
  top:269px;
  width:1192px;
  height:2px;
}
#u517 {
  position:absolute;
  left:2px;
  top:-7px;
  width:1188px;
  visibility:hidden;
  word-wrap:break-word;
}
#u518_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:1195px;
  height:5px;
}
#u518 {
  position:absolute;
  left:24px;
  top:329px;
  width:1192px;
  height:2px;
}
#u519 {
  position:absolute;
  left:2px;
  top:-7px;
  width:1188px;
  visibility:hidden;
  word-wrap:break-word;
}
#u520_div {
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:46px;
  background:inherit;
  background-color:rgba(68, 120, 231, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u520 {
  position:absolute;
  left:1036px;
  top:157px;
  width:94px;
  height:46px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u521 {
  position:absolute;
  left:2px;
  top:13px;
  width:90px;
  word-wrap:break-word;
}
#u522_div {
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:46px;
  background:inherit;
  background-color:rgba(68, 120, 231, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u522 {
  position:absolute;
  left:1036px;
  top:218px;
  width:94px;
  height:46px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u523 {
  position:absolute;
  left:2px;
  top:13px;
  width:90px;
  word-wrap:break-word;
}
#u524_div {
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:46px;
  background:inherit;
  background-color:rgba(68, 120, 231, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u524 {
  position:absolute;
  left:1036px;
  top:278px;
  width:94px;
  height:46px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u525 {
  position:absolute;
  left:2px;
  top:13px;
  width:90px;
  word-wrap:break-word;
}
