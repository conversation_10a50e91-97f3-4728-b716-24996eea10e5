$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bh),t,bi,x,_(y,z,A,bj),bk,_(y,z,A,bl),O,bm,bn,_(bo,bp,bq,br)),P,_(),bs,_(),S,[_(T,bt,V,W,X,null,bu,bc,n,bv,ba,bw,bb,bc,s,_(bd,_(be,bf,bg,bh),t,bi,x,_(y,z,A,bj),bk,_(y,z,A,bl),O,bm,bn,_(bo,bp,bq,br)),P,_(),bs,_())],bx,g),_(T,by,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bz),t,bi,bn,_(bo,bp,bq,br),bA,bB),P,_(),bs,_(),S,[_(T,bC,V,W,X,null,bu,bc,n,bv,ba,bw,bb,bc,s,_(bd,_(be,bf,bg,bz),t,bi,bn,_(bo,bp,bq,br),bA,bB),P,_(),bs,_())],bx,g),_(T,bD,V,W,X,bE,n,bF,ba,bF,bb,bc,s,_(bd,_(be,bG,bg,bH),bI,_(bJ,_(bK,_(y,z,A,bL,bM,bN))),t,bO,bn,_(bo,bP,bq,bQ)),bR,g,P,_(),bs,_(),bS,bT),_(T,bU,V,W,X,bV,n,bW,ba,bW,bb,bc,s,_(bd,_(be,bX,bg,bY),t,bZ,bn,_(bo,ca,bq,cb)),bR,g,P,_(),bs,_()),_(T,cc,V,W,X,cd,n,ce,ba,ce,bb,bc,s,_(t,cf,bd,_(be,cg,bg,ch),bn,_(bo,bp,bq,ci)),P,_(),bs,_(),S,[_(T,cj,V,W,X,null,bu,bc,n,bv,ba,bw,bb,bc,s,_(t,cf,bd,_(be,cg,bg,ch),bn,_(bo,bp,bq,ci)),P,_(),bs,_())],ck,_(cl,cm)),_(T,cn,V,W,X,co,n,Z,ba,cp,bb,bc,s,_(bd,_(be,cq,bg,cr),t,cs,bn,_(bo,bp,bq,ct),O,cu,bk,_(y,z,A,bl)),P,_(),bs,_(),S,[_(T,cv,V,W,X,null,bu,bc,n,bv,ba,bw,bb,bc,s,_(bd,_(be,cq,bg,cr),t,cs,bn,_(bo,bp,bq,ct),O,cu,bk,_(y,z,A,bl)),P,_(),bs,_())],ck,_(cl,cw),bx,g),_(T,cx,V,W,X,co,n,Z,ba,cp,bb,bc,s,_(bd,_(be,cq,bg,cr),t,cs,bn,_(bo,bp,bq,cy),O,cu,bk,_(y,z,A,bl)),P,_(),bs,_(),S,[_(T,cz,V,W,X,null,bu,bc,n,bv,ba,bw,bb,bc,s,_(bd,_(be,cq,bg,cr),t,cs,bn,_(bo,bp,bq,cy),O,cu,bk,_(y,z,A,bl)),P,_(),bs,_())],ck,_(cl,cw),bx,g),_(T,cA,V,W,X,co,n,Z,ba,cp,bb,bc,s,_(bd,_(be,cq,bg,cr),t,cs,bn,_(bo,bp,bq,cB),O,cu,bk,_(y,z,A,bl)),P,_(),bs,_(),S,[_(T,cC,V,W,X,null,bu,bc,n,bv,ba,bw,bb,bc,s,_(bd,_(be,cq,bg,cr),t,cs,bn,_(bo,bp,bq,cB),O,cu,bk,_(y,z,A,bl)),P,_(),bs,_())],ck,_(cl,cw),bx,g),_(T,cD,V,W,X,co,n,Z,ba,cp,bb,bc,s,_(bd,_(be,cq,bg,cr),t,cs,bn,_(bo,bp,bq,cE),O,cu,bk,_(y,z,A,bl)),P,_(),bs,_(),S,[_(T,cF,V,W,X,null,bu,bc,n,bv,ba,bw,bb,bc,s,_(bd,_(be,cq,bg,cr),t,cs,bn,_(bo,bp,bq,cE),O,cu,bk,_(y,z,A,bl)),P,_(),bs,_())],ck,_(cl,cw),bx,g),_(T,cG,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,cH,bg,cI),t,cJ,bn,_(bo,cK,bq,cL),cM,cN,M,cO,x,_(y,z,A,cP),bK,_(y,z,A,B,bM,bN),cQ,cR,O,J),P,_(),bs,_(),S,[_(T,cS,V,W,X,null,bu,bc,n,bv,ba,bw,bb,bc,s,_(bd,_(be,cH,bg,cI),t,cJ,bn,_(bo,cK,bq,cL),cM,cN,M,cO,x,_(y,z,A,cP),bK,_(y,z,A,B,bM,bN),cQ,cR,O,J),P,_(),bs,_())],bx,g),_(T,cT,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,cH,bg,cI),t,cJ,bn,_(bo,cK,bq,cU),cM,cN,M,cO,x,_(y,z,A,cP),bK,_(y,z,A,B,bM,bN),cQ,cR,O,J),P,_(),bs,_(),S,[_(T,cV,V,W,X,null,bu,bc,n,bv,ba,bw,bb,bc,s,_(bd,_(be,cH,bg,cI),t,cJ,bn,_(bo,cK,bq,cU),cM,cN,M,cO,x,_(y,z,A,cP),bK,_(y,z,A,B,bM,bN),cQ,cR,O,J),P,_(),bs,_())],bx,g),_(T,cW,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,cH,bg,cI),t,cJ,bn,_(bo,cK,bq,cX),cM,cN,M,cO,x,_(y,z,A,cP),bK,_(y,z,A,B,bM,bN),cQ,cR,O,J),P,_(),bs,_(),S,[_(T,cY,V,W,X,null,bu,bc,n,bv,ba,bw,bb,bc,s,_(bd,_(be,cH,bg,cI),t,cJ,bn,_(bo,cK,bq,cX),cM,cN,M,cO,x,_(y,z,A,cP),bK,_(y,z,A,B,bM,bN),cQ,cR,O,J),P,_(),bs,_())],bx,g)])),cZ,_(),da,_(db,_(dc,dd),de,_(dc,df),dg,_(dc,dh),di,_(dc,dj),dk,_(dc,dl),dm,_(dc,dn),dp,_(dc,dq),dr,_(dc,ds),dt,_(dc,du),dv,_(dc,dw),dx,_(dc,dy),dz,_(dc,dA),dB,_(dc,dC),dD,_(dc,dE),dF,_(dc,dG),dH,_(dc,dI),dJ,_(dc,dK),dL,_(dc,dM),dN,_(dc,dO),dP,_(dc,dQ),dR,_(dc,dS),dT,_(dc,dU)));}; 
var b="url",c="kds出堂记录手动呼叫.html",d="generationDate",e=new Date(1565766542269.35),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="f8faeed9a3b4455fb8837f6c11f53aa4",n="type",o="Axure:Page",p="name",q="KDS出堂记录手动呼叫",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="ad189ced35eb4f3e80504c3fa229c3ed",V="label",W="",X="friendlyType",Y="Rectangle",Z="vectorShape",ba="styleType",bb="visible",bc=true,bd="size",be="width",bf=1200,bg="height",bh=675,bi="0882bfcd7d11450d85d157758311dca5",bj=0x7FFFFFFF,bk="borderFill",bl=0xFFCCCCCC,bm="1",bn="location",bo="x",bp=24,bq="y",br=30,bs="imageOverrides",bt="bc9b2894fa9444da92f44fc9c126f4ce",bu="isContained",bv="richTextPanel",bw="paragraph",bx="generateCompound",by="9c1c1db746344ee7a078f79d670e89d6",bz=62,bA="horizontalAlignment",bB="left",bC="80569f4d413840a39f35e30048582cbb",bD="60ddefb2668d4716a9a8c90f54986e89",bE="Text Field",bF="textBox",bG=186,bH=25,bI="stateStyles",bJ="hint",bK="foreGroundFill",bL=0xFF999999,bM="opacity",bN=1,bO="44157808f2934100b68f2394a66b2bba",bP=936,bQ=51,bR="HideHintOnFocused",bS="placeholderText",bT="请输入内容",bU="0d843c4a72e84a44ba785813b41afad6",bV="Droplist",bW="comboBox",bX=82,bY=22,bZ="********************************",ca=854,cb=52,cc="d22620d9f458405e913ee336dac9785e",cd="Image",ce="imageBox",cf="********************************",cg=1020,ch=240,ci=92,cj="f5e4b9e3ebb04418b69183041b685fd3",ck="images",cl="normal~",cm="images/kds出堂记录手动呼叫/u510.jpg",cn="21b8c883f0cc47a485748918bd05de24",co="Horizontal Line",cp="horizontalLine",cq=1192,cr=2,cs="619b2148ccc1497285562264d51992f9",ct=149,cu="2",cv="9ee0974a8f994f928e4ea9d1695e6a3c",cw="images/kds出堂记录手动呼叫/u512.png",cx="892d18cd75d54bcdb8d3509185726424",cy=209,cz="5258da73fbb84f73a5d028b7d370cf9c",cA="5a66ec16cf6441949743efd78cf65827",cB=269,cC="f2048a20edf3446a8032c3db98ec8e1b",cD="c860626a95d24dc2b66d44e7d5e4bc71",cE=329,cF="46e61b9e9ac54f37a85380b44f2383be",cG="181dcc48de9b454184b9031b27bca655",cH=94,cI=46,cJ="4b7bfc596114427989e10bb0b557d0ce",cK=1036,cL=157,cM="cornerRadius",cN="8",cO="'PingFangSC-Regular', 'PingFang SC'",cP=0xFF4478E7,cQ="fontSize",cR="14px",cS="7a67dc8b31a4470a918eec9e3bba9ff2",cT="f9671109d39a4251a70211b96f2abb6a",cU=218,cV="b9670fd25e2c4e2bb6d11af5fa4f03b7",cW="94ae7cb87377471e92dbb9b78a6f6859",cX=278,cY="22b0601dabd5468c9c0ee039215c2e6b",cZ="masters",da="objectPaths",db="ad189ced35eb4f3e80504c3fa229c3ed",dc="scriptId",dd="u504",de="bc9b2894fa9444da92f44fc9c126f4ce",df="u505",dg="9c1c1db746344ee7a078f79d670e89d6",dh="u506",di="80569f4d413840a39f35e30048582cbb",dj="u507",dk="60ddefb2668d4716a9a8c90f54986e89",dl="u508",dm="0d843c4a72e84a44ba785813b41afad6",dn="u509",dp="d22620d9f458405e913ee336dac9785e",dq="u510",dr="f5e4b9e3ebb04418b69183041b685fd3",ds="u511",dt="21b8c883f0cc47a485748918bd05de24",du="u512",dv="9ee0974a8f994f928e4ea9d1695e6a3c",dw="u513",dx="892d18cd75d54bcdb8d3509185726424",dy="u514",dz="5258da73fbb84f73a5d028b7d370cf9c",dA="u515",dB="5a66ec16cf6441949743efd78cf65827",dC="u516",dD="f2048a20edf3446a8032c3db98ec8e1b",dE="u517",dF="c860626a95d24dc2b66d44e7d5e4bc71",dG="u518",dH="46e61b9e9ac54f37a85380b44f2383be",dI="u519",dJ="181dcc48de9b454184b9031b27bca655",dK="u520",dL="7a67dc8b31a4470a918eec9e3bba9ff2",dM="u521",dN="f9671109d39a4251a70211b96f2abb6a",dO="u522",dP="b9670fd25e2c4e2bb6d11af5fa4f03b7",dQ="u523",dR="94ae7cb87377471e92dbb9b78a6f6859",dS="u524",dT="22b0601dabd5468c9c0ee039215c2e6b",dU="u525";
return _creator();
})());