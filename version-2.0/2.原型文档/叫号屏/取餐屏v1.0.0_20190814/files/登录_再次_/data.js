$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bf),bh,_(bi,bj,bk,bl)),P,_(),bm,_(),bn,bo),_(T,bp,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,bw,bk,bx),M,by,bz,bA,bd,_(be,bB,bg,bC),bD,_(y,z,A,bE,bF,bG)),P,_(),bm,_(),S,[_(T,bH,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,bw,bk,bx),M,by,bz,bA,bd,_(be,bB,bg,bC),bD,_(y,z,A,bE,bF,bG)),P,_(),bm,_())],bK,_(bL,bM),bN,g),_(T,bO,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,bQ,bk,bR),M,bS,bz,bT,bd,_(be,bU,bg,bV),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY),P,_(),bm,_(),S,[_(T,bZ,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,bQ,bk,bR),M,bS,bz,bT,bd,_(be,bU,bg,bV),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY),P,_(),bm,_())],bK,_(bL,ca),bN,g),_(T,cb,V,cc,X,cd,n,ce,ba,ce,bb,bc,s,_(bd,_(be,cf,bg,cf)),P,_(),bm,_(),cg,[_(T,ch,V,W,X,ci,n,cj,ba,cj,bb,bc,s,_(bt,bu,bh,_(bi,ck,bk,cl),cm,_(cn,_(bD,_(y,z,A,bE,bF,bG))),t,co,bd,_(be,cp,bg,cq),M,by,bD,_(y,z,A,cr,bF,bG),bz,cs),ct,g,P,_(),bm,_(),cu,cv),_(T,cw,V,W,X,cx,n,br,ba,br,bb,bc,s,_(bd,_(be,cy,bg,cz),bh,_(bi,cA,bk,cl),cB,_(y,z,A,cC),cD,cE,t,cF,M,cG,x,_(y,z,A,bW),bz,cs),P,_(),bm,_(),S,[_(T,cH,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bd,_(be,cy,bg,cz),bh,_(bi,cA,bk,cl),cB,_(y,z,A,cC),cD,cE,t,cF,M,cG,x,_(y,z,A,bW),bz,cs),P,_(),bm,_())],Q,_(cI,_(cJ,cK,cL,[_(cJ,cM,cN,g,cO,[_(cP,cQ,cJ,cR,cS,_(cT,k,b,cU,cV,bc),cW,cX)])])),cY,bc,bN,g),_(T,cZ,V,W,X,ci,n,cj,ba,cj,bb,bc,s,_(bt,bu,bh,_(bi,ck,bk,cl),cm,_(cn,_(bD,_(y,z,A,bE,bF,bG))),t,co,bd,_(be,cp,bg,da),M,by,bD,_(y,z,A,cr,bF,bG),bz,cs),ct,g,P,_(),bm,_(),cu,db),_(T,dc,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dg),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_(),S,[_(T,dj,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dg),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_())],bK,_(bL,dk),bN,g),_(T,dl,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dm),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_(),S,[_(T,dn,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dm),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_())],bK,_(bL,dk),bN,g),_(T,dp,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dq),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_(),S,[_(T,dr,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dq),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_())],bK,_(bL,dk),bN,g),_(T,ds,V,W,X,cx,n,br,ba,br,bb,bc,s,_(t,dt,bh,_(bi,du,bk,du),x,_(y,z,A,bW),bd,_(be,bB,bg,dv),cB,_(y,z,A,cC)),P,_(),bm,_(),S,[_(T,dw,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(t,dt,bh,_(bi,du,bk,du),x,_(y,z,A,bW),bd,_(be,bB,bg,dv),cB,_(y,z,A,cC)),P,_(),bm,_())],bN,g),_(T,dx,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dy,bk,dz),M,by,bX,bY,bd,_(be,dA,bg,dB)),P,_(),bm,_(),S,[_(T,dC,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dy,bk,dz),M,by,bX,bY,bd,_(be,dA,bg,dB)),P,_(),bm,_())],bK,_(bL,dD),bN,g),_(T,dE,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dF,bk,dG),M,by,bz,dH,bX,bY,bd,_(be,dI,bg,dJ),bD,_(y,z,A,dK,bF,bG)),P,_(),bm,_(),S,[_(T,dL,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dF,bk,dG),M,by,bz,dH,bX,bY,bd,_(be,dI,bg,dJ),bD,_(y,z,A,dK,bF,bG)),P,_(),bm,_())],bK,_(bL,dM),bN,g),_(T,dN,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dO,bk,dP),M,by,bz,cs,bX,bY,bd,_(be,dQ,bg,dR)),P,_(),bm,_(),S,[_(T,dS,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dO,bk,dP),M,by,bz,cs,bX,bY,bd,_(be,dQ,bg,dR)),P,_(),bm,_())],bK,_(bL,dT),bN,g)],dU,g),_(T,ch,V,W,X,ci,n,cj,ba,cj,bb,bc,s,_(bt,bu,bh,_(bi,ck,bk,cl),cm,_(cn,_(bD,_(y,z,A,bE,bF,bG))),t,co,bd,_(be,cp,bg,cq),M,by,bD,_(y,z,A,cr,bF,bG),bz,cs),ct,g,P,_(),bm,_(),cu,cv),_(T,cw,V,W,X,cx,n,br,ba,br,bb,bc,s,_(bd,_(be,cy,bg,cz),bh,_(bi,cA,bk,cl),cB,_(y,z,A,cC),cD,cE,t,cF,M,cG,x,_(y,z,A,bW),bz,cs),P,_(),bm,_(),S,[_(T,cH,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bd,_(be,cy,bg,cz),bh,_(bi,cA,bk,cl),cB,_(y,z,A,cC),cD,cE,t,cF,M,cG,x,_(y,z,A,bW),bz,cs),P,_(),bm,_())],Q,_(cI,_(cJ,cK,cL,[_(cJ,cM,cN,g,cO,[_(cP,cQ,cJ,cR,cS,_(cT,k,b,cU,cV,bc),cW,cX)])])),cY,bc,bN,g),_(T,cZ,V,W,X,ci,n,cj,ba,cj,bb,bc,s,_(bt,bu,bh,_(bi,ck,bk,cl),cm,_(cn,_(bD,_(y,z,A,bE,bF,bG))),t,co,bd,_(be,cp,bg,da),M,by,bD,_(y,z,A,cr,bF,bG),bz,cs),ct,g,P,_(),bm,_(),cu,db),_(T,dc,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dg),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_(),S,[_(T,dj,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dg),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_())],bK,_(bL,dk),bN,g),_(T,dl,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dm),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_(),S,[_(T,dn,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dm),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_())],bK,_(bL,dk),bN,g),_(T,dp,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dq),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_(),S,[_(T,dr,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dd,bk,de),M,bS,bz,cs,bd,_(be,df,bg,dq),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dh,di),P,_(),bm,_())],bK,_(bL,dk),bN,g),_(T,ds,V,W,X,cx,n,br,ba,br,bb,bc,s,_(t,dt,bh,_(bi,du,bk,du),x,_(y,z,A,bW),bd,_(be,bB,bg,dv),cB,_(y,z,A,cC)),P,_(),bm,_(),S,[_(T,dw,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(t,dt,bh,_(bi,du,bk,du),x,_(y,z,A,bW),bd,_(be,bB,bg,dv),cB,_(y,z,A,cC)),P,_(),bm,_())],bN,g),_(T,dx,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dy,bk,dz),M,by,bX,bY,bd,_(be,dA,bg,dB)),P,_(),bm,_(),S,[_(T,dC,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dy,bk,dz),M,by,bX,bY,bd,_(be,dA,bg,dB)),P,_(),bm,_())],bK,_(bL,dD),bN,g),_(T,dE,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dF,bk,dG),M,by,bz,dH,bX,bY,bd,_(be,dI,bg,dJ),bD,_(y,z,A,dK,bF,bG)),P,_(),bm,_(),S,[_(T,dL,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dF,bk,dG),M,by,bz,dH,bX,bY,bd,_(be,dI,bg,dJ),bD,_(y,z,A,dK,bF,bG)),P,_(),bm,_())],bK,_(bL,dM),bN,g),_(T,dN,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dO,bk,dP),M,by,bz,cs,bX,bY,bd,_(be,dQ,bg,dR)),P,_(),bm,_(),S,[_(T,dS,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dO,bk,dP),M,by,bz,cs,bX,bY,bd,_(be,dQ,bg,dR)),P,_(),bm,_())],bK,_(bL,dT),bN,g),_(T,dV,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dW,bk,dX),bd,_(be,dY,bg,de),M,by,bz,dZ),P,_(),bm,_(),S,[_(T,ea,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dW,bk,dX),bd,_(be,dY,bg,de),M,by,bz,dZ),P,_(),bm,_())],bK,_(bL,eb),bN,g)])),ec,_(ed,_(l,ed,n,ee,p,Y,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,ef,V,W,X,cx,n,br,ba,br,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,eg,x,_(y,z,A,eh),cB,_(y,z,A,cC),O,ei),P,_(),bm,_(),S,[_(T,ej,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,eg,x,_(y,z,A,eh),cB,_(y,z,A,cC),O,ei),P,_(),bm,_())],bN,g)]))),ek,_(el,_(em,en,eo,_(em,ep),eq,_(em,er)),es,_(em,et),eu,_(em,ev),ew,_(em,ex),ey,_(em,ez),eA,_(em,eB),eC,_(em,eD),eE,_(em,eF),eG,_(em,eH),eI,_(em,eJ),eK,_(em,eL),eM,_(em,eN),eO,_(em,eP),eQ,_(em,eR),eS,_(em,eT),eU,_(em,eV),eW,_(em,eX),eY,_(em,eZ),fa,_(em,fb),fc,_(em,fd),fe,_(em,ff),fg,_(em,fh),fi,_(em,fj),fk,_(em,fl),fm,_(em,fn),fo,_(em,fp)));}; 
var b="url",c="登录_再次_.html",d="generationDate",e=new Date(1565766541253.2),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="02713109d61f405baaf41ef271d60813",n="type",o="Axure:Page",p="name",q="登录(再次)",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="360dbc5ab8ab451f846a9b80abaefea1",V="label",W="",X="friendlyType",Y="主框架",Z="referenceDiagramObject",ba="styleType",bb="visible",bc=true,bd="location",be="x",bf=10,bg="y",bh="size",bi="width",bj=1200,bk="height",bl=675,bm="imageOverrides",bn="masterId",bo="42b294620c2d49c7af5b1798469a7eae",bp="747d8e27593c45589cc9fec48e8f5d11",bq="Paragraph",br="vectorShape",bs="paragraph",bt="fontWeight",bu="200",bv="4988d43d80b44008a4a415096f1632af",bw=298,bx=42,by="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",bz="fontSize",bA="30px",bB=175,bC=63,bD="foreGroundFill",bE=0xFF999999,bF="opacity",bG=1,bH="71a4916f1352464da4177a21e31a123a",bI="isContained",bJ="richTextPanel",bK="images",bL="normal~",bM="images/登录_再次_/u137.png",bN="generateCompound",bO="ca5e60d8a4214e38ac19943be0667df7",bP="500",bQ=119,bR=50,bS="'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC'",bT="36px",bU=46,bV=55,bW=0xFFF2F2F2,bX="horizontalAlignment",bY="center",bZ="b06c56a03c5f4ae785b486f21b200943",ca="images/登录_首次_/u97.png",cb="eecea7b66f7943aaade719dfbb578f59",cc="账号登录",cd="Group",ce="layer",cf=0,cg="objs",ch="612c33a7ddf84b1cb5e8d786ae331452",ci="Text Field",cj="textBox",ck=374,cl=60,cm="stateStyles",cn="hint",co="44157808f2934100b68f2394a66b2bba",cp=603,cq=303,cr=0xFF666666,cs="28px",ct="HideHintOnFocused",cu="placeholderText",cv="      输入员工账号",cw="e00259d6fbd94128969775b1f265c28c",cx="Rectangle",cy=594,cz=487,cA=383,cB="borderFill",cC=0xFFCCCCCC,cD="cornerRadius",cE="12",cF="98c916898e844865a527f56bc61a500d",cG="'PingFangSC-Regular', 'PingFang SC'",cH="77f29e340e784d7790f95349a4389e2d",cI="onClick",cJ="description",cK="OnClick",cL="cases",cM="Case 1",cN="isNewIfGroup",cO="actions",cP="action",cQ="linkWindow",cR="Open 含准备(准备·取餐·AD) in Current Window",cS="target",cT="targetType",cU="含准备_准备·取餐·ad_.html",cV="includeVariables",cW="linkType",cX="current",cY="tabbable",cZ="d0fd975cb17c4b9b8c6bda61e033b538",da=390,db="      输入登录密码",dc="7b5b5eac3cbe4c4e9afbffcac9c84498",dd=28,de=26,df=608,dg=249,dh="verticalAlignment",di="middle",dj="b6a38570073d42699ed874bb22e2b701",dk="images/登录_首次_/u107.png",dl="ae44a1435be844f3b9591259cee8f151",dm=320,dn="b0c9f63f2b354b3b9975faeea5c17e88",dp="6090e62405ed49c286768965ebbed8bd",dq=407,dr="0f1461e44120463d88993b7511c73765",ds="d5949841cb9b43c484b2b8738388dc67",dt="26c731cb771b44a88eb8b6e97e78c80e",du=327,dv=224,dw="638974a4ed1349528840e114247bc4b5",dx="130d84f900f8457f80f88f9f29bd3e10",dy=82,dz=18,dA=312,dB=378,dC="0a6b41a3cbd147ea935a9f6948485822",dD="images/登录_首次_/u115.png",dE="64c78a96ef464b78be68635848d850d6",dF=66,dG=11,dH="8px",dI=605,dJ=363,dK=0xFFFF0000,dL="3d41990315034aaf87d17ee723d8b3bd",dM="images/登录_首次_/u117.png",dN="db2c12c1053246dea3c594d6085d7c3c",dO=169,dP=40,dQ=646,dR=242,dS="a671768f003a47508bd03e3d8bf4d7bf",dT="images/登录_再次_/u158.png",dU="propagate",dV="17f76742a3004216a298555c488a5501",dW=435,dX=51,dY=1263,dZ="12px",ea="cb3b208792d34732b23a5446c8295e17",eb="images/登录_再次_/u160.png",ec="masters",ed="42b294620c2d49c7af5b1798469a7eae",ee="Axure:Master",ef="5a1fbc74d2b64be4b44e2ef951181541",eg="0882bfcd7d11450d85d157758311dca5",eh=0x7FF2F2F2,ei="1",ej="8523194c36f94eec9e7c0acc0e3eedb6",ek="objectPaths",el="360dbc5ab8ab451f846a9b80abaefea1",em="scriptId",en="u134",eo="5a1fbc74d2b64be4b44e2ef951181541",ep="u135",eq="8523194c36f94eec9e7c0acc0e3eedb6",er="u136",es="747d8e27593c45589cc9fec48e8f5d11",et="u137",eu="71a4916f1352464da4177a21e31a123a",ev="u138",ew="ca5e60d8a4214e38ac19943be0667df7",ex="u139",ey="b06c56a03c5f4ae785b486f21b200943",ez="u140",eA="eecea7b66f7943aaade719dfbb578f59",eB="u141",eC="612c33a7ddf84b1cb5e8d786ae331452",eD="u142",eE="e00259d6fbd94128969775b1f265c28c",eF="u143",eG="77f29e340e784d7790f95349a4389e2d",eH="u144",eI="d0fd975cb17c4b9b8c6bda61e033b538",eJ="u145",eK="7b5b5eac3cbe4c4e9afbffcac9c84498",eL="u146",eM="b6a38570073d42699ed874bb22e2b701",eN="u147",eO="ae44a1435be844f3b9591259cee8f151",eP="u148",eQ="b0c9f63f2b354b3b9975faeea5c17e88",eR="u149",eS="6090e62405ed49c286768965ebbed8bd",eT="u150",eU="0f1461e44120463d88993b7511c73765",eV="u151",eW="d5949841cb9b43c484b2b8738388dc67",eX="u152",eY="638974a4ed1349528840e114247bc4b5",eZ="u153",fa="130d84f900f8457f80f88f9f29bd3e10",fb="u154",fc="0a6b41a3cbd147ea935a9f6948485822",fd="u155",fe="64c78a96ef464b78be68635848d850d6",ff="u156",fg="3d41990315034aaf87d17ee723d8b3bd",fh="u157",fi="db2c12c1053246dea3c594d6085d7c3c",fj="u158",fk="a671768f003a47508bd03e3d8bf4d7bf",fl="u159",fm="17f76742a3004216a298555c488a5501",fn="u160",fo="cb3b208792d34732b23a5446c8295e17",fp="u161";
return _creator();
})());