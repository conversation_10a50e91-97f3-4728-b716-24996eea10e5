body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1210px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u120_div {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:675px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u120 {
  position:absolute;
  left:10px;
  top:10px;
  width:1200px;
  height:675px;
}
#u121 {
  position:absolute;
  left:2px;
  top:330px;
  width:1196px;
  visibility:hidden;
  word-wrap:break-word;
}
#u122_div {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:141px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u122 {
  position:absolute;
  left:178px;
  top:297px;
  width:300px;
  height:141px;
}
#u123 {
  position:absolute;
  left:2px;
  top:62px;
  width:296px;
  visibility:hidden;
  word-wrap:break-word;
}
#u124_div {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:141px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u124 {
  position:absolute;
  left:576px;
  top:297px;
  width:300px;
  height:141px;
}
#u125 {
  position:absolute;
  left:2px;
  top:62px;
  width:296px;
  visibility:hidden;
  word-wrap:break-word;
}
#u126_img {
  position:absolute;
  left:0px;
  top:0px;
  width:309px;
  height:40px;
}
#u126 {
  position:absolute;
  left:154px;
  top:157px;
  width:309px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:28px;
  text-align:center;
}
#u127 {
  position:absolute;
  left:0px;
  top:0px;
  width:309px;
  white-space:nowrap;
}
#u128_img {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:109px;
}
#u128 {
  position:absolute;
  left:218px;
  top:313px;
  width:221px;
  height:109px;
}
#u129 {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  word-wrap:break-word;
}
#u130_img {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:109px;
}
#u130 {
  position:absolute;
  left:616px;
  top:313px;
  width:221px;
  height:109px;
  color:#999999;
}
#u131 {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  word-wrap:break-word;
}
#u132_img {
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:32px;
}
#u132 {
  position:absolute;
  left:817px;
  top:298px;
  width:59px;
  height:32px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#999999;
}
#u133 {
  position:absolute;
  left:2px;
  top:7px;
  width:55px;
  word-wrap:break-word;
}
