$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bf),bh,_(bi,bj,bk,bl)),P,_(),bm,_(),bn,bo),_(T,bp,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,bs,bk,bt),t,bu,bd,_(be,bv,bg,bw)),P,_(),bm,_(),S,[_(T,bx,V,W,X,null,by,bc,n,bz,ba,bA,bb,bc,s,_(bh,_(bi,bs,bk,bt),t,bu,bd,_(be,bv,bg,bw)),P,_(),bm,_())],bB,g),_(T,bC,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,bs,bk,bt),t,bu,bd,_(be,bD,bg,bw),O,J),P,_(),bm,_(),S,[_(T,bE,V,W,X,null,by,bc,n,bz,ba,bA,bb,bc,s,_(bh,_(bi,bs,bk,bt),t,bu,bd,_(be,bD,bg,bw),O,J),P,_(),bm,_())],bB,g),_(T,bF,V,W,X,bG,n,br,ba,bA,bb,bc,s,_(bH,bI,t,bJ,bh,_(bi,bK,bk,bL),M,bM,bN,bO,bP,bQ,bd,_(be,bR,bg,bS)),P,_(),bm,_(),S,[_(T,bT,V,W,X,null,by,bc,n,bz,ba,bA,bb,bc,s,_(bH,bI,t,bJ,bh,_(bi,bK,bk,bL),M,bM,bN,bO,bP,bQ,bd,_(be,bR,bg,bS)),P,_(),bm,_())],bU,_(bV,bW),bB,g),_(T,bX,V,W,X,bG,n,br,ba,bA,bb,bc,s,_(t,bJ,bh,_(bi,bY,bk,bZ),bd,_(be,ca,bg,cb)),P,_(),bm,_(),S,[_(T,cc,V,W,X,null,by,bc,n,bz,ba,bA,bb,bc,s,_(t,bJ,bh,_(bi,bY,bk,bZ),bd,_(be,ca,bg,cb)),P,_(),bm,_())],Q,_(cd,_(ce,cf,cg,[_(ce,ch,ci,g,cj,[_(ck,cl,ce,cm,cn,_(co,k,b,cp,cq,bc),cr,cs)])])),ct,bc,bU,_(bV,cu),bB,g),_(T,cv,V,W,X,bG,n,br,ba,bA,bb,bc,s,_(t,bJ,bh,_(bi,bY,bk,bZ),bd,_(be,cw,bg,cb),cx,_(y,z,A,cy,cz,cA)),P,_(),bm,_(),S,[_(T,cB,V,W,X,null,by,bc,n,bz,ba,bA,bb,bc,s,_(t,bJ,bh,_(bi,bY,bk,bZ),bd,_(be,cw,bg,cb),cx,_(y,z,A,cy,cz,cA)),P,_(),bm,_())],bU,_(bV,cu),bB,g),_(T,cC,V,W,X,cD,n,br,ba,br,bb,bc,s,_(bh,_(bi,cE,bk,cF),t,bu,bd,_(be,cG,bg,cH),M,cI,cx,_(y,z,A,cy,cz,cA),cJ,_(y,z,A,cK)),P,_(),bm,_(),S,[_(T,cL,V,W,X,null,by,bc,n,bz,ba,bA,bb,bc,s,_(bh,_(bi,cE,bk,cF),t,bu,bd,_(be,cG,bg,cH),M,cI,cx,_(y,z,A,cy,cz,cA),cJ,_(y,z,A,cK)),P,_(),bm,_())],bU,_(bV,cM),bB,g)])),cN,_(cO,_(l,cO,n,cP,p,Y,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,cQ,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,cR,x,_(y,z,A,cS),cJ,_(y,z,A,cK),O,cT),P,_(),bm,_(),S,[_(T,cU,V,W,X,null,by,bc,n,bz,ba,bA,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,cR,x,_(y,z,A,cS),cJ,_(y,z,A,cK),O,cT),P,_(),bm,_())],bB,g)]))),cV,_(cW,_(cX,cY,cZ,_(cX,da),db,_(cX,dc)),dd,_(cX,de),df,_(cX,dg),dh,_(cX,di),dj,_(cX,dk),dl,_(cX,dm),dn,_(cX,dp),dq,_(cX,dr),ds,_(cX,dt),du,_(cX,dv),dw,_(cX,dx),dy,_(cX,dz),dA,_(cX,dB)));}; 
var b="url",c="选择业务-不做，仅做了解.html",d="generationDate",e=new Date(1565766541186.18),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="9ebd0f08ddb9428ea2a71a404138eeab",n="type",o="Axure:Page",p="name",q="选择业务-不做，仅做了解",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="5f856665e1644307adb59c24f61019cd",V="label",W="",X="friendlyType",Y="主框架",Z="referenceDiagramObject",ba="styleType",bb="visible",bc=true,bd="location",be="x",bf=10,bg="y",bh="size",bi="width",bj=1200,bk="height",bl=675,bm="imageOverrides",bn="masterId",bo="42b294620c2d49c7af5b1798469a7eae",bp="7b7e4b671f4b4dc8998fa8e207662417",bq="Rectangle",br="vectorShape",bs=300,bt=141,bu="4b7bfc596114427989e10bb0b557d0ce",bv=178,bw=297,bx="09a42a0132b0474e9c0f36117a1c6f18",by="isContained",bz="richTextPanel",bA="paragraph",bB="generateCompound",bC="3852d43f852a4e3c907f080f7e8227fc",bD=576,bE="80e30ff8f1b24c029d3661424850f79c",bF="87e74c73afab4c0985a5e15c36502b2f",bG="Paragraph",bH="fontWeight",bI="200",bJ="4988d43d80b44008a4a415096f1632af",bK=309,bL=40,bM="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",bN="fontSize",bO="28px",bP="horizontalAlignment",bQ="center",bR=154,bS=157,bT="7081de6381b34114aaa8afbaa875baf8",bU="images",bV="normal~",bW="images/选择业务-不做，仅做了解/u126.png",bX="140cc2a65c6f4c5896fb8f46e0f042f4",bY=221,bZ=109,ca=218,cb=313,cc="328a8491fcd34854a2f10be900a7e274",cd="onClick",ce="description",cf="OnClick",cg="cases",ch="Case 1",ci="isNewIfGroup",cj="actions",ck="action",cl="linkWindow",cm="Open 含准备(准备·取餐·AD) in Current Window",cn="target",co="targetType",cp="含准备_准备·取餐·ad_.html",cq="includeVariables",cr="linkType",cs="current",ct="tabbable",cu="images/选择业务-不做，仅做了解/u128.png",cv="72cc6b630f22489c9fd144f314c57837",cw=616,cx="foreGroundFill",cy=0xFF999999,cz="opacity",cA=1,cB="01b9d24cfdb84b098d63d64d48d21639",cC="d97e0f6bb86c4103af88ba8ee0d3593f",cD="Left Arrow Button",cE=59,cF=32,cG=817,cH=298,cI="'PingFangSC-Regular', 'PingFang SC'",cJ="borderFill",cK=0xFFCCCCCC,cL="33d49399aac241f283d4bbafb90c6c7c",cM="images/选择业务-不做，仅做了解/u132.png",cN="masters",cO="42b294620c2d49c7af5b1798469a7eae",cP="Axure:Master",cQ="5a1fbc74d2b64be4b44e2ef951181541",cR="0882bfcd7d11450d85d157758311dca5",cS=0x7FF2F2F2,cT="1",cU="8523194c36f94eec9e7c0acc0e3eedb6",cV="objectPaths",cW="5f856665e1644307adb59c24f61019cd",cX="scriptId",cY="u119",cZ="5a1fbc74d2b64be4b44e2ef951181541",da="u120",db="8523194c36f94eec9e7c0acc0e3eedb6",dc="u121",dd="7b7e4b671f4b4dc8998fa8e207662417",de="u122",df="09a42a0132b0474e9c0f36117a1c6f18",dg="u123",dh="3852d43f852a4e3c907f080f7e8227fc",di="u124",dj="80e30ff8f1b24c029d3661424850f79c",dk="u125",dl="87e74c73afab4c0985a5e15c36502b2f",dm="u126",dn="7081de6381b34114aaa8afbaa875baf8",dp="u127",dq="140cc2a65c6f4c5896fb8f46e0f042f4",dr="u128",ds="328a8491fcd34854a2f10be900a7e274",dt="u129",du="72cc6b630f22489c9fd144f314c57837",dv="u130",dw="01b9d24cfdb84b098d63d64d48d21639",dx="u131",dy="d97e0f6bb86c4103af88ba8ee0d3593f",dz="u132",dA="33d49399aac241f283d4bbafb90c6c7c",dB="u133";
return _creator();
})());