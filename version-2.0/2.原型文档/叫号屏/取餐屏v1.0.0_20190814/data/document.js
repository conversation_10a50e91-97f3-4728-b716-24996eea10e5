$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z),_(u,A,w,x,y,B,C,[_(u,D,w,x,y,E),_(u,F,w,x,y,G)]),_(u,H,w,I,y,J,C,[_(u,K,w,x,y,L),_(u,M,w,x,y,N,C,[_(u,O,w,x,y,P)]),_(u,Q,w,x,y,R),_(u,S,w,x,y,T,C,[_(u,U,w,x,y,V)]),_(u,W,w,x,y,X)])]),Y,_(Z,J),ba,_(bb,bc,bd,_(be,bf,bg,bf),bh,bi),bj,[],bk,_(bl,_(bm,bn,bo,bp,bq,br,bs,bt,bu,bv,bw,f,bx,_(by,bz,bA,bB,bC,bD),bE,bF,bG,br,bH,_(bI,bf,bJ,bf),bd,_(be,bf,bg,bf),bK,d,bL,f,bM,bn,bN,_(by,bz,bA,bO),bP,_(by,bz,bA,bQ),bR,bS,bT,bz,bC,bS,bU,bV,bW,bX,bY,bZ,ca,bZ,cb,bZ,cc,bZ,cd,_(),ce,bV,cf,bV,cg,_(ch,f,ci,cj,ck,cj,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cr)),cs,_(ch,f,ci,bf,ck,cj,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cr)),ct,_(ch,f,ci,bD,ck,bD,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cu))),cv,_(cw,_(bm,cx),cy,_(bm,cz,bR,bV,bN,_(by,bz,bA,cA)),cB,_(bm,cC,bR,bV,bN,_(by,bz,bA,cD)),cE,_(bm,cF),cG,_(bm,cH,bo,bp,bq,br,bx,_(by,bz,bA,bB,bC,bD),bP,_(by,bz,bA,cI),bR,bS,bN,_(by,bz,bA,cJ),bE,bF,bs,bt,bu,bv,bw,f,bT,bz,bU,bV,bC,bS,cg,_(ch,f,ci,cj,ck,cj,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cr)),cs,_(ch,f,ci,bf,ck,cj,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cr)),ct,_(ch,f,ci,bD,ck,bD,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cu)),bW,bX,bY,bZ,ca,bZ,cb,bZ,cc,bZ,bG,br),cK,_(bm,cL,bR,bV),cM,_(bm,cN,bN,_(by,bz,bA,cA)),cO,_(bm,cP,bU,cQ),cR,_(bm,cS,bu,cT,bo,cU,bR,bV,bN,_(by,bz,bA,cV),bE,cW,bW,cX,bY,bV,ca,bV,cb,bV,cc,bV),cY,_(bm,cZ,bu,da,bo,cU,bR,bV,bN,_(by,bz,bA,cV),bE,cW,bW,cX,bY,bV,ca,bV,cb,bV,cc,bV),db,_(bm,dc,bu,dd,bo,cU,bR,bV,bN,_(by,bz,bA,cV),bE,cW,bW,cX,bY,bV,ca,bV,cb,bV,cc,bV),de,_(bm,df,bu,dg,bo,cU,bR,bV,bN,_(by,bz,bA,cV),bE,cW,bW,cX,bY,bV,ca,bV,cb,bV,cc,bV),dh,_(bm,di,bo,cU,bR,bV,bN,_(by,bz,bA,cV),bE,cW,bW,cX,bY,bV,ca,bV,cb,bV,cc,bV),dj,_(bm,dk,bu,dl,bo,cU,bR,bV,bN,_(by,bz,bA,cV),bE,cW,bW,cX,bY,bV,ca,bV,cb,bV,cc,bV),dm,_(bm,dn,bu,dg,bR,bV,bN,_(by,bz,bA,cV),bE,cW,bW,cX,bY,bV,ca,bV,cb,bV,cc,bV),dp,_(bm,dq,bR,bV,bN,_(by,bz,bA,cV),bE,cW,bW,cX,bY,bV,ca,bV,cb,bV,cc,bV),dr,_(bm,ds,bN,_(by,bz,bA,cV)),dt,_(bm,du,bR,cQ,bN,_(by,bz,bA,cV)),dv,_(bm,dw,bx,_(by,bz,bA,dx,bC,bD),bE,cW,bW,bX),dy,_(bm,dz,bx,_(by,bz,bA,dx,bC,bD),bE,cW,bW,cX),dA,_(bm,dB,bx,_(by,bz,bA,dx,bC,bD),bE,cW,bW,cX),dC,_(bm,dD,bx,_(by,bz,bA,dx,bC,bD),bE,cW,bW,cX),dE,_(bm,dF,bE,cW,bW,cX),dG,_(bm,dH,bE,cW,bW,cX),dI,_(bm,dJ,bE,bF),dK,_(bm,dL,bR,bV,bN,_(by,bz,bA,cV),bE,cW,bW,bX),dM,_(bm,dN),dO,_(bm,dP,bN,_(by,bz,bA,cV)),dQ,_(bm,dR,bo,bp,bq,br,bx,_(by,bz,bA,dS,bC,bD),bP,_(by,bz,bA,cI),bR,bS,bE,bF,bs,dT,bu,dU,bw,f,bT,bz,bU,bV,bN,_(by,bz,bA,bO),bC,bS,cg,_(ch,f,ci,cj,ck,cj,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cr)),cs,_(ch,f,ci,bf,ck,cj,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cr)),ct,_(ch,f,ci,bD,ck,bD,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cu)),bW,bX,bY,bZ,ca,bZ,cb,bZ,cc,bZ,bG,br),dV,_(bm,dW,bx,_(by,bz,bA,bO,bC,bD),bP,_(by,bz,bA,bO),bN,_(by,bz,bA,dX),cg,_(ch,d,ci,bD,ck,bD,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,dY))),dZ,_(bm,ea,bN,_(by,eb,ec,[_(bA,bO),_(bA,cA),_(bA,ed),_(bA,bO)])),ee,_(bm,ef),eg,_(bm,eh,bo,bp,bq,br,bs,bt,bu,bv,bw,f,bx,_(by,bz,bA,bB,bC,bD),bE,bF,bG,br,bN,_(by,bz,bA,bO),bP,_(by,bz,bA,bB),bR,bS,bT,bz,bC,bS,bU,bV,bW,bX,bY,bZ,ca,bZ,cb,bZ,cc,bZ,cg,_(ch,f,ci,cj,ck,cj,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cr)),cs,_(ch,f,ci,bf,ck,cj,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cr)),ct,_(ch,f,ci,bD,ck,bD,cl,cj,bA,_(cm,cn,co,cn,cp,cn,cq,cu))),ei,_(bm,ej,bP,_(by,bz,bA,dx)),ek,_(bm,el,bR,bV,bN,_(by,bz,bA,bB))),em,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="整体设计说明",w="type",x="Wireframe",y="url",z="整体设计说明.html",A="登录(首次)",B="登录_首次_.html",C="children",D="选择业务-不做，仅做了解",E="选择业务-不做，仅做了解.html",F="登录(再次)",G="登录_再次_.html",H="取餐",I="Folder",J="",K="等位屏-取餐使用分析",L="等位屏-取餐使用分析.html",M="含准备(准备·取餐·AD)",N="含准备_准备·取餐·ad_.html",O="取餐(准备·取餐)",P="取餐_准备·取餐_.html",Q="闲置AD",R="闲置ad.html",S="设置",T="设置.html",U="设置(完整版)",V="设置_完整版_.html",W="KDS出堂记录手动呼叫",X="kds出堂记录手动呼叫.html",Y="globalVariables",Z="onloadvariable",ba="defaultAdaptiveView",bb="name",bc="Base",bd="size",be="width",bf=0,bg="height",bh="condition",bi="<=",bj="adaptiveViews",bk="stylesheet",bl="defaultStyle",bm="id",bn="627587b6038d43cca051c114ac41ad32",bo="fontWeight",bp="400",bq="fontStyle",br="normal",bs="fontName",bt="'ArialMT', 'Arial'",bu="fontSize",bv="13px",bw="underline",bx="foreGroundFill",by="fillType",bz="solid",bA="color",bB=0xFF333333,bC="opacity",bD=1,bE="horizontalAlignment",bF="center",bG="lineSpacing",bH="location",bI="x",bJ="y",bK="visible",bL="limbo",bM="baseStyle",bN="fill",bO=0xFFFFFFFF,bP="borderFill",bQ=0xFF797979,bR="borderWidth",bS="1",bT="linePattern",bU="cornerRadius",bV="0",bW="verticalAlignment",bX="middle",bY="paddingLeft",bZ="2",ca="paddingTop",cb="paddingRight",cc="paddingBottom",cd="stateStyles",ce="rotation",cf="textRotation",cg="outerShadow",ch="on",ci="offsetX",cj=5,ck="offsetY",cl="blurRadius",cm="r",cn=0,co="g",cp="b",cq="a",cr=0.349019607843137,cs="innerShadow",ct="textShadow",cu=0.647058823529412,cv="customStyles",cw="box_1",cx="********************************",cy="box_2",cz="********************************",cA=0xFFF2F2F2,cB="box_3",cC="********************************",cD=0xFFD7D7D7,cE="ellipse",cF="eff044fe6497434a8c5f89f769ddde3b",cG="_形状",cH="40519e9ec4264601bfb12c514e4f4867",cI=0xFFCCCCCC,cJ=0x19333333,cK="image",cL="75a91ee5b9d042cfa01b8d565fe289c0",cM="placeholder",cN="c50e74f669b24b37bd9c18da7326bccd",cO="button",cP="c9f35713a1cf4e91a0f2dbac65e6fb5c",cQ="5",cR="heading_1",cS="1111111151944dfba49f67fd55eb1f88",cT="32px",cU="bold",cV=0xFFFFFF,cW="left",cX="top",cY="heading_2",cZ="b3a15c9ddde04520be40f94c8168891e",da="24px",db="heading_3",dc="8c7a4c5ad69a4369a5f7788171ac0b32",dd="18px",de="heading_4",df="e995c891077945c89c0b5fe110d15a0b",dg="14px",dh="heading_5",di="386b19ef4be143bd9b6c392ded969f89",dj="heading_6",dk="fc3b9a13b5574fa098ef0a1db9aac861",dl="10px",dm="label",dn="2285372321d148ec80932747449c36c9",dp="paragraph",dq="4988d43d80b44008a4a415096f1632af",dr="line",ds="619b2148ccc1497285562264d51992f9",dt="arrow",du="d148f2c5268542409e72dde43e40043e",dv="text_field",dw="44157808f2934100b68f2394a66b2bba",dx=0xFF000000,dy="text_area",dz="42ee17691d13435b8256d8d0a814778f",dA="droplist",dB="85f724022aae41c594175ddac9c289eb",dC="list_box",dD="********************************",dE="checkbox",dF="********************************",dG="radio_button",dH="4eb5516f311c4bdfa0cb11d7ea75084e",dI="html_button",dJ="eed12d9ebe2e4b9689b3b57949563dca",dK="tree_node",dL="93a4c3353b6f4562af635b7116d6bf94",dM="table_cell",dN="33ea2511485c479dbf973af3302f2352",dO="menu_item",dP="2036b2baccbc41f0b9263a6981a11a42",dQ="connector",dR="699a012e142a4bcba964d96e88b88bdf",dS=0xFF0000FF,dT="'PingFangSC-Regular', 'PingFang SC'",dU="12px",dV="marker",dW="a8e305fe5c2a462b995b0021a9ba82b9",dX=0xFF009DD9,dY=0.698039215686274,dZ="flow_shape",ea="df01900e3c4e43f284bafec04b0864c4",eb="linearGradient",ec="colors",ed=0xFFE4E4E4,ee="table",ef="d612b8c2247342eda6a8bc0663265baa",eg="shape",eh="98c916898e844865a527f56bc61a500d",ei="horizontal_line",ej="f48196c19ab74fb7b3acb5151ce8ea2d",ek="icon",el="26c731cb771b44a88eb8b6e97e78c80e",em="duplicateStyles";
return _creator();
})());