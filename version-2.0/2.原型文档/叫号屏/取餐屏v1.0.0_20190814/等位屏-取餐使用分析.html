<!DOCTYPE html>
<html>
  <head>
    <title>等位屏-取餐使用分析</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/等位屏-取餐使用分析/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/等位屏-取餐使用分析/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Paragraph) -->
      <div id="u483" class="ax_default paragraph">
        <img id="u483_img" class="img " src="images/等位屏-取餐使用分析/u483.png"/>
        <!-- Unnamed () -->
        <div id="u484" class="text" style="visibility: visible;">
          <p style="font-size:16px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">等位屏使用分析</span></p><p style="font-size:16px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;"><br></span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">等位屏用于显示当前叫号信息，便于顾客或者工作人员能够快速查看进度，可起到规范工作调配、就餐管理的作用。餐饮行业中，多用于正餐排队、快餐取餐叫号。</span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;"><br></span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">取餐屏</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">：</span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">配合后厨管理终端使用，作为点餐业务的第二屏，显示队列信息，提供广告位，可加入视频、营销活动等，即可缓解用户焦躁，提升门店服务体验；也可通过投放的活动等进行二次营销，拉动创收</span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.适合客户：客流大，有大量顾客需长时间等待取餐的门店</span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2.使用场景</span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1）KDS+取餐屏</span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2）一体机点餐+取餐屏</span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">3）一体机+微信扫码点餐+取餐屏</span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">4）微信扫码点餐+取餐屏</span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">3.主流程控制(主要针对场景1，且为唯一取餐口的情况)</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u485" class="ax_default image">
        <img id="u485_img" class="img " src="images/等位屏-取餐使用分析/u485.jpg"/>
        <!-- Unnamed () -->
        <div id="u486" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>
    </div>
  </body>
</html>
