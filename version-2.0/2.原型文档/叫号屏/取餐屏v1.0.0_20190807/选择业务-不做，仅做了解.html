<!DOCTYPE html>
<html>
  <head>
    <title>选择业务-不做，仅做了解</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/选择业务-不做，仅做了解/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/选择业务-不做，仅做了解/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (主框架) -->

      <!-- Unnamed (Rectangle) -->
      <div id="u120" class="ax_default box_3">
        <div id="u120_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u121" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u122" class="ax_default box_1">
        <div id="u122_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u123" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u124" class="ax_default box_1">
        <div id="u124_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u125" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u126" class="ax_default paragraph">
        <img id="u126_img" class="img " src="images/选择业务-不做，仅做了解/u126.png"/>
        <!-- Unnamed () -->
        <div id="u127" class="text" style="visibility: visible;">
          <p><span>请选择当前屏幕展示信息</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u128" class="ax_default paragraph">
        <img id="u128_img" class="img " src="images/选择业务-不做，仅做了解/u128.png"/>
        <!-- Unnamed () -->
        <div id="u129" class="text" style="visibility: visible;">
          <p style="font-size:28px;text-align:center;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">取餐</span></p><p style="font-size:13px;"><span style="font-family:'ArialMT', 'Arial';"><br></span></p><p style="font-size:13px;text-align:left;"><span style="font-family:'ArialMT', 'Arial';">用于显示当前取餐信息(常用于快餐通过叫号顾客自助取餐的场景）。须配合掌控者后厨管理系统试用</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u130" class="ax_default paragraph">
        <img id="u130_img" class="img " src="images/选择业务-不做，仅做了解/u128.png"/>
        <!-- Unnamed () -->
        <div id="u131" class="text" style="visibility: visible;">
          <p style="font-size:28px;text-align:center;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">排队</span></p><p style="font-size:13px;"><span style="font-family:'ArialMT', 'Arial';"><br></span></p><p style="font-size:13px;text-align:left;"><span style="font-family:'ArialMT', 'Arial';">用于显示当前排队等待信息(适合等待就餐人数较多的情况下）。须配合掌控者排队叫号管理系统试用</span></p>
        </div>
      </div>

      <!-- Unnamed (Left Arrow Button) -->
      <div id="u132" class="ax_default box_1">
        <img id="u132_img" class="img " src="images/选择业务-不做，仅做了解/u132.png"/>
        <!-- Unnamed () -->
        <div id="u133" class="text" style="visibility: visible;">
          <p><span>敬等…</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
