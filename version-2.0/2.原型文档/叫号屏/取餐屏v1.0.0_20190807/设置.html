<!DOCTYPE html>
<html>
  <head>
    <title>设置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/设置/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/设置/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (主框架) -->

      <!-- Unnamed (Rectangle) -->
      <div id="u429" class="ax_default box_3">
        <div id="u429_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u430" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u431" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u432" class="ax_default table_cell">
          <img id="u432_img" class="img " src="images/设置/u432.png"/>
          <!-- Unnamed () -->
          <div id="u433" class="text" style="visibility: visible;">
            <p><span>取餐屏显示的牌号信息依次来自收银机、自助点餐机及微信点餐。取餐叫号由KDS点出堂口控制，如需重复叫号点出堂记录进行操作。</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u434" class="ax_default table_cell">
          <img id="u434_img" class="img " src="images/设置/u432.png"/>
          <!-- Unnamed () -->
          <div id="u435" class="text" style="visibility: visible;">
            <p><span>布局模式</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u436" class="ax_default table_cell">
          <img id="u436_img" class="img " src="images/设置/u432.png"/>
          <!-- Unnamed () -->
          <div id="u437" class="text" style="visibility: visible;">
            <p><span>循环叫号次数</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u438" class="ax_default table_cell">
          <img id="u438_img" class="img " src="images/设置/u432.png"/>
          <!-- Unnamed () -->
          <div id="u439" class="text" style="visibility: visible;">
            <p><span>自动确认取餐</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u440" class="ax_default table_cell">
          <img id="u440_img" class="img " src="images/设置/u440.png"/>
          <!-- Unnamed () -->
          <div id="u441" class="text" style="visibility: visible;">
            <p><span>退出账号</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u442" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u443" class="ax_default table_cell">
          <img id="u443_img" class="img " src="images/设置/u443.png"/>
          <!-- Unnamed () -->
          <div id="u444" class="text" style="visibility: visible;">
            <p><span>&lt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u445" class="ax_default table_cell">
          <img id="u445_img" class="img " src="images/设置/u445.png"/>
          <!-- Unnamed () -->
          <div id="u446" class="text" style="visibility: visible;">
            <p><span>等餐+取餐+广告</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u447" class="ax_default table_cell">
          <img id="u447_img" class="img " src="images/设置/u447.png"/>
          <!-- Unnamed () -->
          <div id="u448" class="text" style="visibility: visible;">
            <p><span>&gt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u449" class="ax_default table_cell">
          <img id="u449_img" class="img " src="images/设置/u449.png"/>
          <!-- Unnamed () -->
          <div id="u450" class="text" style="visibility: visible;">
            <p><span>&lt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u451" class="ax_default table_cell">
          <img id="u451_img" class="img " src="images/设置/u451.png"/>
          <!-- Unnamed () -->
          <div id="u452" class="text" style="visibility: visible;">
            <p><span>1次</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u453" class="ax_default table_cell">
          <img id="u453_img" class="img " src="images/设置/u453.png"/>
          <!-- Unnamed () -->
          <div id="u454" class="text" style="visibility: visible;">
            <p><span>&gt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u455" class="ax_default table_cell">
          <img id="u455_img" class="img " src="images/设置/u443.png"/>
          <!-- Unnamed () -->
          <div id="u456" class="text" style="visibility: visible;">
            <p><span>&lt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u457" class="ax_default table_cell">
          <img id="u457_img" class="img " src="images/设置/u445.png"/>
          <!-- Unnamed () -->
          <div id="u458" class="text" style="visibility: visible;">
            <p><span>1分钟</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u459" class="ax_default table_cell">
          <img id="u459_img" class="img " src="images/设置/u447.png"/>
          <!-- Unnamed () -->
          <div id="u460" class="text" style="visibility: visible;">
            <p><span>&gt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u461" class="ax_default table_cell">
          <img id="u461_img" class="img " src="images/设置/u461.png"/>
          <!-- Unnamed () -->
          <div id="u462" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u463" class="ax_default table_cell">
          <img id="u463_img" class="img " src="images/设置/u463.png"/>
          <!-- Unnamed () -->
          <div id="u464" class="text" style="visibility: visible;">
            <p><span>确定</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u465" class="ax_default table_cell">
          <img id="u465_img" class="img " src="images/设置/u465.png"/>
          <!-- Unnamed () -->
          <div id="u466" class="text" style="visibility: visible;">
            <p><span>&gt;</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u467" class="ax_default flow_shape">
        <div id="u467_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u468" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u469" class="ax_default paragraph">
        <img id="u469_img" class="img " src="images/设置/u469.png"/>
        <!-- Unnamed () -->
        <div id="u470" class="text" style="visibility: visible;">
          <p><span>遥控器控件说明：</span></p><p><span>OK：切换菜单显示状态</span></p><p><span>上下键：切换设置项</span></p><p><span>左右键：对应多选项的设置项，更改当前设置属性；单项执行对应操作</span></p><p><span><br></span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u471" class="ax_default paragraph">
        <img id="u471_img" class="img " src="images/设置/u471.png"/>
        <!-- Unnamed () -->
        <div id="u472" class="text" style="visibility: visible;">
          <p><span>布局模式：等餐+取餐+广告&nbsp; 等餐+取餐</span></p><p><span>循环叫号次数：1次、2次、3次</span></p><p><span>循环叫号次数：30秒 、1分钟、90秒，2分钟，3分钟，5分钟</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
