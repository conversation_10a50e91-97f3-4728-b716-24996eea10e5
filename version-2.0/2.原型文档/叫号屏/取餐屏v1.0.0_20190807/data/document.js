$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z),_(u,A,w,x,y,B,C,[_(u,D,w,x,y,E),_(u,F,w,x,y,G)]),_(u,H,w,I,y,J,C,[_(u,K,w,x,y,L),_(u,M,w,x,y,N,C,[_(u,O,w,x,y,P)]),_(u,Q,w,x,y,R),_(u,S,w,x,y,T,C,[_(u,U,w,x,y,V)])])]),W,_(X,J),Y,_(Z,ba,bb,_(bc,bd,be,bd),bf,bg),bh,[],bi,_(bj,_(bk,bl,bm,bn,bo,bp,bq,br,bs,bt,bu,f,bv,_(bw,bx,by,bz,bA,bB),bC,bD,bE,bp,bF,_(bG,bd,bH,bd),bb,_(bc,bd,be,bd),bI,d,bJ,f,bK,bl,bL,_(bw,bx,by,bM),bN,_(bw,bx,by,bO),bP,bQ,bR,bx,bA,bQ,bS,bT,bU,bV,bW,bX,bY,bX,bZ,bX,ca,bX,cb,_(),cc,bT,cd,bT,ce,_(cf,f,cg,ch,ci,ch,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cp)),cq,_(cf,f,cg,bd,ci,ch,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cp)),cr,_(cf,f,cg,bB,ci,bB,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cs))),ct,_(cu,_(bk,cv),cw,_(bk,cx,bP,bT,bL,_(bw,bx,by,cy)),cz,_(bk,cA,bP,bT,bL,_(bw,bx,by,cB)),cC,_(bk,cD),cE,_(bk,cF,bm,bn,bo,bp,bv,_(bw,bx,by,bz,bA,bB),bN,_(bw,bx,by,cG),bP,bQ,bL,_(bw,bx,by,cH),bC,bD,bq,br,bs,bt,bu,f,bR,bx,bS,bT,bA,bQ,ce,_(cf,f,cg,ch,ci,ch,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cp)),cq,_(cf,f,cg,bd,ci,ch,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cp)),cr,_(cf,f,cg,bB,ci,bB,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cs)),bU,bV,bW,bX,bY,bX,bZ,bX,ca,bX,bE,bp),cI,_(bk,cJ,bP,bT),cK,_(bk,cL,bL,_(bw,bx,by,cy)),cM,_(bk,cN,bS,cO),cP,_(bk,cQ,bs,cR,bm,cS,bP,bT,bL,_(bw,bx,by,cT),bC,cU,bU,cV,bW,bT,bY,bT,bZ,bT,ca,bT),cW,_(bk,cX,bs,cY,bm,cS,bP,bT,bL,_(bw,bx,by,cT),bC,cU,bU,cV,bW,bT,bY,bT,bZ,bT,ca,bT),cZ,_(bk,da,bs,db,bm,cS,bP,bT,bL,_(bw,bx,by,cT),bC,cU,bU,cV,bW,bT,bY,bT,bZ,bT,ca,bT),dc,_(bk,dd,bs,de,bm,cS,bP,bT,bL,_(bw,bx,by,cT),bC,cU,bU,cV,bW,bT,bY,bT,bZ,bT,ca,bT),df,_(bk,dg,bm,cS,bP,bT,bL,_(bw,bx,by,cT),bC,cU,bU,cV,bW,bT,bY,bT,bZ,bT,ca,bT),dh,_(bk,di,bs,dj,bm,cS,bP,bT,bL,_(bw,bx,by,cT),bC,cU,bU,cV,bW,bT,bY,bT,bZ,bT,ca,bT),dk,_(bk,dl,bs,de,bP,bT,bL,_(bw,bx,by,cT),bC,cU,bU,cV,bW,bT,bY,bT,bZ,bT,ca,bT),dm,_(bk,dn,bP,bT,bL,_(bw,bx,by,cT),bC,cU,bU,cV,bW,bT,bY,bT,bZ,bT,ca,bT),dp,_(bk,dq,bL,_(bw,bx,by,cT)),dr,_(bk,ds,bP,cO,bL,_(bw,bx,by,cT)),dt,_(bk,du,bv,_(bw,bx,by,dv,bA,bB),bC,cU,bU,bV),dw,_(bk,dx,bv,_(bw,bx,by,dv,bA,bB),bC,cU,bU,cV),dy,_(bk,dz,bv,_(bw,bx,by,dv,bA,bB),bC,cU,bU,cV),dA,_(bk,dB,bv,_(bw,bx,by,dv,bA,bB),bC,cU,bU,cV),dC,_(bk,dD,bC,cU,bU,cV),dE,_(bk,dF,bC,cU,bU,cV),dG,_(bk,dH,bC,bD),dI,_(bk,dJ,bP,bT,bL,_(bw,bx,by,cT),bC,cU,bU,bV),dK,_(bk,dL),dM,_(bk,dN,bL,_(bw,bx,by,cT)),dO,_(bk,dP,bm,bn,bo,bp,bv,_(bw,bx,by,dQ,bA,bB),bN,_(bw,bx,by,cG),bP,bQ,bC,bD,bq,dR,bs,dS,bu,f,bR,bx,bS,bT,bL,_(bw,bx,by,bM),bA,bQ,ce,_(cf,f,cg,ch,ci,ch,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cp)),cq,_(cf,f,cg,bd,ci,ch,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cp)),cr,_(cf,f,cg,bB,ci,bB,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cs)),bU,bV,bW,bX,bY,bX,bZ,bX,ca,bX,bE,bp),dT,_(bk,dU,bv,_(bw,bx,by,bM,bA,bB),bN,_(bw,bx,by,bM),bL,_(bw,bx,by,dV),ce,_(cf,d,cg,bB,ci,bB,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,dW))),dX,_(bk,dY,bL,_(bw,dZ,ea,[_(by,bM),_(by,cy),_(by,eb),_(by,bM)])),ec,_(bk,ed),ee,_(bk,ef,bm,bn,bo,bp,bq,br,bs,bt,bu,f,bv,_(bw,bx,by,bz,bA,bB),bC,bD,bE,bp,bL,_(bw,bx,by,bM),bN,_(bw,bx,by,bz),bP,bQ,bR,bx,bA,bQ,bS,bT,bU,bV,bW,bX,bY,bX,bZ,bX,ca,bX,ce,_(cf,f,cg,ch,ci,ch,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cp)),cq,_(cf,f,cg,bd,ci,ch,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cp)),cr,_(cf,f,cg,bB,ci,bB,cj,ch,by,_(ck,cl,cm,cl,cn,cl,co,cs))),eg,_(bk,eh,bN,_(bw,bx,by,dv)),ei,_(bk,ej,bP,bT,bL,_(bw,bx,by,bz))),ek,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="整体设计说明",w="type",x="Wireframe",y="url",z="整体设计说明.html",A="登录(首次)",B="登录_首次_.html",C="children",D="选择业务-不做，仅做了解",E="选择业务-不做，仅做了解.html",F="登录(再次)",G="登录_再次_.html",H="取餐",I="Folder",J="",K="等位屏-取餐使用分析",L="等位屏-取餐使用分析.html",M="含准备(准备·取餐·AD)",N="含准备_准备·取餐·ad_.html",O="取餐(准备·取餐)",P="取餐_准备·取餐_.html",Q="闲置AD",R="闲置ad.html",S="设置",T="设置.html",U="设置(完整版)",V="设置_完整版_.html",W="globalVariables",X="onloadvariable",Y="defaultAdaptiveView",Z="name",ba="Base",bb="size",bc="width",bd=0,be="height",bf="condition",bg="<=",bh="adaptiveViews",bi="stylesheet",bj="defaultStyle",bk="id",bl="627587b6038d43cca051c114ac41ad32",bm="fontWeight",bn="400",bo="fontStyle",bp="normal",bq="fontName",br="'ArialMT', 'Arial'",bs="fontSize",bt="13px",bu="underline",bv="foreGroundFill",bw="fillType",bx="solid",by="color",bz=0xFF333333,bA="opacity",bB=1,bC="horizontalAlignment",bD="center",bE="lineSpacing",bF="location",bG="x",bH="y",bI="visible",bJ="limbo",bK="baseStyle",bL="fill",bM=0xFFFFFFFF,bN="borderFill",bO=0xFF797979,bP="borderWidth",bQ="1",bR="linePattern",bS="cornerRadius",bT="0",bU="verticalAlignment",bV="middle",bW="paddingLeft",bX="2",bY="paddingTop",bZ="paddingRight",ca="paddingBottom",cb="stateStyles",cc="rotation",cd="textRotation",ce="outerShadow",cf="on",cg="offsetX",ch=5,ci="offsetY",cj="blurRadius",ck="r",cl=0,cm="g",cn="b",co="a",cp=0.349019607843137,cq="innerShadow",cr="textShadow",cs=0.647058823529412,ct="customStyles",cu="box_1",cv="********************************",cw="box_2",cx="********************************",cy=0xFFF2F2F2,cz="box_3",cA="********************************",cB=0xFFD7D7D7,cC="ellipse",cD="eff044fe6497434a8c5f89f769ddde3b",cE="_形状",cF="40519e9ec4264601bfb12c514e4f4867",cG=0xFFCCCCCC,cH=0x19333333,cI="image",cJ="75a91ee5b9d042cfa01b8d565fe289c0",cK="placeholder",cL="c50e74f669b24b37bd9c18da7326bccd",cM="button",cN="c9f35713a1cf4e91a0f2dbac65e6fb5c",cO="5",cP="heading_1",cQ="1111111151944dfba49f67fd55eb1f88",cR="32px",cS="bold",cT=0xFFFFFF,cU="left",cV="top",cW="heading_2",cX="b3a15c9ddde04520be40f94c8168891e",cY="24px",cZ="heading_3",da="8c7a4c5ad69a4369a5f7788171ac0b32",db="18px",dc="heading_4",dd="e995c891077945c89c0b5fe110d15a0b",de="14px",df="heading_5",dg="386b19ef4be143bd9b6c392ded969f89",dh="heading_6",di="fc3b9a13b5574fa098ef0a1db9aac861",dj="10px",dk="label",dl="2285372321d148ec80932747449c36c9",dm="paragraph",dn="4988d43d80b44008a4a415096f1632af",dp="line",dq="619b2148ccc1497285562264d51992f9",dr="arrow",ds="d148f2c5268542409e72dde43e40043e",dt="text_field",du="44157808f2934100b68f2394a66b2bba",dv=0xFF000000,dw="text_area",dx="42ee17691d13435b8256d8d0a814778f",dy="droplist",dz="85f724022aae41c594175ddac9c289eb",dA="list_box",dB="********************************",dC="checkbox",dD="********************************",dE="radio_button",dF="4eb5516f311c4bdfa0cb11d7ea75084e",dG="html_button",dH="eed12d9ebe2e4b9689b3b57949563dca",dI="tree_node",dJ="93a4c3353b6f4562af635b7116d6bf94",dK="table_cell",dL="33ea2511485c479dbf973af3302f2352",dM="menu_item",dN="2036b2baccbc41f0b9263a6981a11a42",dO="connector",dP="699a012e142a4bcba964d96e88b88bdf",dQ=0xFF0000FF,dR="'PingFangSC-Regular', 'PingFang SC'",dS="12px",dT="marker",dU="a8e305fe5c2a462b995b0021a9ba82b9",dV=0xFF009DD9,dW=0.698039215686274,dX="flow_shape",dY="df01900e3c4e43f284bafec04b0864c4",dZ="linearGradient",ea="colors",eb=0xFFE4E4E4,ec="table",ed="d612b8c2247342eda6a8bc0663265baa",ee="shape",ef="98c916898e844865a527f56bc61a500d",eg="horizontal_line",eh="f48196c19ab74fb7b3acb5151ce8ea2d",ei="icon",ej="26c731cb771b44a88eb8b6e97e78c80e",ek="duplicateStyles";
return _creator();
})());