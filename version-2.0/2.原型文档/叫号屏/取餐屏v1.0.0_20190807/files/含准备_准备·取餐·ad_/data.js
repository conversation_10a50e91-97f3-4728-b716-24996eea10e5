$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bf),bh,_(bi,bj,bk,bl)),P,_(),bm,_(),bn,bo),_(T,bp,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bs,bt,bh,_(bi,bu,bk,bv),t,bw,bd,_(be,bx,bg,by),bz,bA,M,bB,bC,bD,bE,bF,x,_(y,z,A,bG)),P,_(),bm,_(),S,[_(T,bH,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bt,bh,_(bi,bu,bk,bv),t,bw,bd,_(be,bx,bg,by),bz,bA,M,bB,bC,bD,bE,bF,x,_(y,z,A,bG)),P,_(),bm,_())],bL,g),_(T,bM,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,bN,bk,bv),t,bw,bd,_(be,bO,bg,bP),bE,bF,bQ,_(y,z,A,bR,bS,bT)),P,_(),bm,_(),S,[_(T,bU,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bh,_(bi,bN,bk,bv),t,bw,bd,_(be,bO,bg,bP),bE,bF,bQ,_(y,z,A,bR,bS,bT)),P,_(),bm,_())],bL,g),_(T,bV,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bs,bW,bh,_(bi,bX,bk,bv),t,bw,bd,_(be,bY,bg,by),bz,bA,M,bZ,bE,bF,x,_(y,z,A,bG),bC,bD),P,_(),bm,_(),S,[_(T,ca,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bW,bh,_(bi,bX,bk,bv),t,bw,bd,_(be,bY,bg,by),bz,bA,M,bZ,bE,bF,x,_(y,z,A,bG),bC,bD),P,_(),bm,_())],bL,g),_(T,cb,V,W,X,cc,n,br,ba,bK,bb,bc,s,_(bs,bW,t,cd,bh,_(bi,ce,bk,cf),M,bZ,bE,bF,bd,_(be,cg,bg,ch)),P,_(),bm,_(),S,[_(T,ci,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bW,t,cd,bh,_(bi,ce,bk,cf),M,bZ,bE,bF,bd,_(be,cg,bg,ch)),P,_(),bm,_())],cj,_(ck,cl),bL,g),_(T,cm,V,W,X,cc,n,br,ba,bK,bb,bc,s,_(bs,bt,t,cd,bh,_(bi,cn,bk,cf),M,bB,bE,bF,bd,_(be,co,bg,ch)),P,_(),bm,_(),S,[_(T,cp,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bt,t,cd,bh,_(bi,cn,bk,cf),M,bB,bE,bF,bd,_(be,co,bg,ch)),P,_(),bm,_())],cj,_(ck,cq),bL,g),_(T,cr,V,W,X,cc,n,br,ba,bK,bb,bc,s,_(bs,bW,t,cd,bh,_(bi,cs,bk,ct),M,bZ,bE,bF,bd,_(be,cu,bg,ch)),P,_(),bm,_(),S,[_(T,cv,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bW,t,cd,bh,_(bi,cs,bk,ct),M,bZ,bE,bF,bd,_(be,cu,bg,ch)),P,_(),bm,_())],cj,_(ck,cw),bL,g),_(T,cx,V,W,X,cc,n,br,ba,bK,bb,bc,s,_(bs,bt,t,cd,bh,_(bi,cy,bk,cz),M,bB,bE,bF,bd,_(be,cA,bg,ch)),P,_(),bm,_(),S,[_(T,cB,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bt,t,cd,bh,_(bi,cy,bk,cz),M,bB,bE,bF,bd,_(be,cA,bg,ch)),P,_(),bm,_())],cj,_(ck,cC),bL,g)])),cD,_(cE,_(l,cE,n,cF,p,Y,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,cG,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,bw,x,_(y,z,A,cH),cI,_(y,z,A,cJ),O,cK),P,_(),bm,_(),S,[_(T,cL,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,bw,x,_(y,z,A,cH),cI,_(y,z,A,cJ),O,cK),P,_(),bm,_())],bL,g)]))),cM,_(cN,_(cO,cP,cQ,_(cO,cR),cS,_(cO,cT)),cU,_(cO,cV),cW,_(cO,cX),cY,_(cO,cZ),da,_(cO,db),dc,_(cO,dd),de,_(cO,df),dg,_(cO,dh),di,_(cO,dj),dk,_(cO,dl),dm,_(cO,dn),dp,_(cO,dq),dr,_(cO,ds),dt,_(cO,du),dv,_(cO,dw)));}; 
var b="url",c="含准备_准备·取餐·ad_.html",d="generationDate",e=new Date(1565142814848.29),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="2a325a9a608f4f6ba9d3cbe9091d5690",n="type",o="Axure:Page",p="name",q="含准备(准备·取餐·AD)",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="d6ea3894db504bff9af1a8c63c4cfebc",V="label",W="",X="friendlyType",Y="主框架",Z="referenceDiagramObject",ba="styleType",bb="visible",bc=true,bd="location",be="x",bf=10,bg="y",bh="size",bi="width",bj=1200,bk="height",bl=675,bm="imageOverrides",bn="masterId",bo="42b294620c2d49c7af5b1798469a7eae",bp="26bab22f5a3843d7a4afd3a5f2dc2d4c",bq="Rectangle",br="vectorShape",bs="fontWeight",bt="200",bu=155,bv=584,bw="0882bfcd7d11450d85d157758311dca5",bx=39,by=53,bz="verticalAlignment",bA="top",bB="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",bC="horizontalAlignment",bD="left",bE="fontSize",bF="28px",bG=0xFFFFFF,bH="df94836b7ac547028eb64a813d4b1921",bI="isContained",bJ="richTextPanel",bK="paragraph",bL="generateCompound",bM="001d11575340407baf6894aad1250ea0",bN=590,bO=578,bP=50,bQ="foreGroundFill",bR=0xFF999999,bS="opacity",bT=1,bU="1c4947afe4c448939be6ce29483a0d63",bV="6387176c7e304f2288e9a07bd44b0a30",bW="500",bX=193,bY=324,bZ="'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC'",ca="6f85383e99d34ff8b5e5510f9619596b",cb="3053d0959e684c5ba4e1ba4e9d2aab95",cc="Paragraph",cd="4988d43d80b44008a4a415096f1632af",ce=153,cf=520,cg=338,ch=108,ci="aee553df35e74f8ca8b35869e9678e82",cj="images",ck="normal~",cl="images/含准备_准备·取餐·ad_/u171.png",cm="264e80235ff943ccb55399ec38f96fb2",cn=143,co=51,cp="85ef1a973fa54f8bbfc8d96eb0288604",cq="images/含准备_准备·取餐·ad_/u173.png",cr="fb1f41930b01491a9434ffef17a2d406",cs=93,ct=40,cu=424,cv="ef21c39f5b6940b0b4472c5d82f9cbfc",cw="images/含准备_准备·取餐·ad_/u175.png",cx="d2272b7b66614c49ac498ea0ce3023bd",cy=147,cz=80,cA=139,cB="d874009be1f64880a7858e8a8b4dc47e",cC="images/含准备_准备·取餐·ad_/u177.png",cD="masters",cE="42b294620c2d49c7af5b1798469a7eae",cF="Axure:Master",cG="5a1fbc74d2b64be4b44e2ef951181541",cH=0x7FF2F2F2,cI="borderFill",cJ=0xFFCCCCCC,cK="1",cL="8523194c36f94eec9e7c0acc0e3eedb6",cM="objectPaths",cN="d6ea3894db504bff9af1a8c63c4cfebc",cO="scriptId",cP="u162",cQ="5a1fbc74d2b64be4b44e2ef951181541",cR="u163",cS="8523194c36f94eec9e7c0acc0e3eedb6",cT="u164",cU="26bab22f5a3843d7a4afd3a5f2dc2d4c",cV="u165",cW="df94836b7ac547028eb64a813d4b1921",cX="u166",cY="001d11575340407baf6894aad1250ea0",cZ="u167",da="1c4947afe4c448939be6ce29483a0d63",db="u168",dc="6387176c7e304f2288e9a07bd44b0a30",dd="u169",de="6f85383e99d34ff8b5e5510f9619596b",df="u170",dg="3053d0959e684c5ba4e1ba4e9d2aab95",dh="u171",di="aee553df35e74f8ca8b35869e9678e82",dj="u172",dk="264e80235ff943ccb55399ec38f96fb2",dl="u173",dm="85ef1a973fa54f8bbfc8d96eb0288604",dn="u174",dp="fb1f41930b01491a9434ffef17a2d406",dq="u175",dr="ef21c39f5b6940b0b4472c5d82f9cbfc",ds="u176",dt="d2272b7b66614c49ac498ea0ce3023bd",du="u177",dv="d874009be1f64880a7858e8a8b4dc47e",dw="u178";
return _creator();
})());