$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bf),bh,_(bi,bj,bk,bl)),P,_(),bm,_(),bn,bo),_(T,bp,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,bw,bk,bx),M,by,bz,bA,bd,_(be,bB,bg,bC),bD,_(y,z,A,bE,bF,bG)),P,_(),bm,_(),S,[_(T,bH,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,bw,bk,bx),M,by,bz,bA,bd,_(be,bB,bg,bC),bD,_(y,z,A,bE,bF,bG)),P,_(),bm,_())],bK,_(bL,bM),bN,g),_(T,bO,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,bQ,bk,bR),M,bS,bz,bT,bd,_(be,bU,bg,bV),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY),P,_(),bm,_(),S,[_(T,bZ,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,bQ,bk,bR),M,bS,bz,bT,bd,_(be,bU,bg,bV),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY),P,_(),bm,_())],bK,_(bL,ca),bN,g),_(T,cb,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,cc,bk,cd),bd,_(be,ce,bg,cf),M,by,bz,cg),P,_(),bm,_(),S,[_(T,ch,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,cc,bk,cd),bd,_(be,ce,bg,cf),M,by,bz,cg),P,_(),bm,_())],bK,_(bL,ci),bN,g),_(T,cj,V,ck,X,cl,n,cm,ba,cm,bb,bc,s,_(bd,_(be,cn,bg,cn)),P,_(),bm,_(),co,[_(T,cp,V,W,X,cq,n,cr,ba,cr,bb,bc,s,_(bt,bu,bh,_(bi,cs,bk,ct),cu,_(cv,_(bD,_(y,z,A,bE,bF,bG))),t,cw,bd,_(be,cx,bg,cy),M,by,bD,_(y,z,A,cz,bF,bG),bz,cA),cB,g,P,_(),bm,_(),cC,cD),_(T,cE,V,W,X,cq,n,cr,ba,cr,bb,bc,s,_(bt,bu,bh,_(bi,cs,bk,ct),cu,_(cv,_(bD,_(y,z,A,bE,bF,bG))),t,cw,bd,_(be,cx,bg,cF),M,by,bD,_(y,z,A,cz,bF,bG),bz,cA),cB,g,P,_(),bm,_(),cC,cG),_(T,cH,V,W,X,cI,n,br,ba,br,bb,bc,s,_(bd,_(be,cJ,bg,cK),bh,_(bi,cL,bk,ct),cM,_(y,z,A,cN),cO,cP,t,cQ,M,cR,x,_(y,z,A,bW),bz,cA),P,_(),bm,_(),S,[_(T,cS,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bd,_(be,cJ,bg,cK),bh,_(bi,cL,bk,ct),cM,_(y,z,A,cN),cO,cP,t,cQ,M,cR,x,_(y,z,A,bW),bz,cA),P,_(),bm,_())],Q,_(cT,_(cU,cV,cW,[_(cU,cX,cY,g,cZ,[_(da,db,cU,dc,dd,_(de,k,b,df,dg,bc),dh,di)])])),dj,bc,bN,g),_(T,dk,V,W,X,cq,n,cr,ba,cr,bb,bc,s,_(bt,bu,bh,_(bi,cs,bk,ct),cu,_(cv,_(bD,_(y,z,A,bE,bF,bG))),t,cw,bd,_(be,cx,bg,dl),M,by,bD,_(y,z,A,cz,bF,bG),bz,cA),cB,g,P,_(),bm,_(),cC,dm),_(T,dn,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,ds),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_(),S,[_(T,dv,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,ds),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_())],bK,_(bL,dw),bN,g),_(T,dx,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,dy),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_(),S,[_(T,dz,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,dy),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_())],bK,_(bL,dw),bN,g),_(T,dA,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,dB),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_(),S,[_(T,dC,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,dB),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_())],bK,_(bL,dw),bN,g),_(T,dD,V,W,X,cI,n,br,ba,br,bb,bc,s,_(t,dE,bh,_(bi,dF,bk,dF),x,_(y,z,A,bW),bd,_(be,dG,bg,dH),cM,_(y,z,A,cN)),P,_(),bm,_(),S,[_(T,dI,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(t,dE,bh,_(bi,dF,bk,dF),x,_(y,z,A,bW),bd,_(be,dG,bg,dH),cM,_(y,z,A,cN)),P,_(),bm,_())],bN,g),_(T,dJ,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,cd,bk,dK),M,by,bX,bY,bd,_(be,dL,bg,dM)),P,_(),bm,_(),S,[_(T,dN,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,cd,bk,dK),M,by,bX,bY,bd,_(be,dL,bg,dM)),P,_(),bm,_())],bK,_(bL,dO),bN,g),_(T,dP,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dQ,bk,dR),M,by,bz,dS,bX,bY,bd,_(be,dT,bg,dU),bD,_(y,z,A,dV,bF,bG)),P,_(),bm,_(),S,[_(T,dW,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dQ,bk,dR),M,by,bz,dS,bX,bY,bd,_(be,dT,bg,dU),bD,_(y,z,A,dV,bF,bG)),P,_(),bm,_())],bK,_(bL,dX),bN,g)],dY,g),_(T,cp,V,W,X,cq,n,cr,ba,cr,bb,bc,s,_(bt,bu,bh,_(bi,cs,bk,ct),cu,_(cv,_(bD,_(y,z,A,bE,bF,bG))),t,cw,bd,_(be,cx,bg,cy),M,by,bD,_(y,z,A,cz,bF,bG),bz,cA),cB,g,P,_(),bm,_(),cC,cD),_(T,cE,V,W,X,cq,n,cr,ba,cr,bb,bc,s,_(bt,bu,bh,_(bi,cs,bk,ct),cu,_(cv,_(bD,_(y,z,A,bE,bF,bG))),t,cw,bd,_(be,cx,bg,cF),M,by,bD,_(y,z,A,cz,bF,bG),bz,cA),cB,g,P,_(),bm,_(),cC,cG),_(T,cH,V,W,X,cI,n,br,ba,br,bb,bc,s,_(bd,_(be,cJ,bg,cK),bh,_(bi,cL,bk,ct),cM,_(y,z,A,cN),cO,cP,t,cQ,M,cR,x,_(y,z,A,bW),bz,cA),P,_(),bm,_(),S,[_(T,cS,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bd,_(be,cJ,bg,cK),bh,_(bi,cL,bk,ct),cM,_(y,z,A,cN),cO,cP,t,cQ,M,cR,x,_(y,z,A,bW),bz,cA),P,_(),bm,_())],Q,_(cT,_(cU,cV,cW,[_(cU,cX,cY,g,cZ,[_(da,db,cU,dc,dd,_(de,k,b,df,dg,bc),dh,di)])])),dj,bc,bN,g),_(T,dk,V,W,X,cq,n,cr,ba,cr,bb,bc,s,_(bt,bu,bh,_(bi,cs,bk,ct),cu,_(cv,_(bD,_(y,z,A,bE,bF,bG))),t,cw,bd,_(be,cx,bg,dl),M,by,bD,_(y,z,A,cz,bF,bG),bz,cA),cB,g,P,_(),bm,_(),cC,dm),_(T,dn,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,ds),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_(),S,[_(T,dv,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,ds),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_())],bK,_(bL,dw),bN,g),_(T,dx,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,dy),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_(),S,[_(T,dz,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,dy),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_())],bK,_(bL,dw),bN,g),_(T,dA,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,dB),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_(),S,[_(T,dC,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bP,t,bv,bh,_(bi,dp,bk,dq),M,bS,bz,cA,bd,_(be,dr,bg,dB),bD,_(y,z,A,bE,bF,bG),x,_(y,z,A,bW),bX,bY,dt,du),P,_(),bm,_())],bK,_(bL,dw),bN,g),_(T,dD,V,W,X,cI,n,br,ba,br,bb,bc,s,_(t,dE,bh,_(bi,dF,bk,dF),x,_(y,z,A,bW),bd,_(be,dG,bg,dH),cM,_(y,z,A,cN)),P,_(),bm,_(),S,[_(T,dI,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(t,dE,bh,_(bi,dF,bk,dF),x,_(y,z,A,bW),bd,_(be,dG,bg,dH),cM,_(y,z,A,cN)),P,_(),bm,_())],bN,g),_(T,dJ,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,cd,bk,dK),M,by,bX,bY,bd,_(be,dL,bg,dM)),P,_(),bm,_(),S,[_(T,dN,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,cd,bk,dK),M,by,bX,bY,bd,_(be,dL,bg,dM)),P,_(),bm,_())],bK,_(bL,dO),bN,g),_(T,dP,V,W,X,bq,n,br,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dQ,bk,dR),M,by,bz,dS,bX,bY,bd,_(be,dT,bg,dU),bD,_(y,z,A,dV,bF,bG)),P,_(),bm,_(),S,[_(T,dW,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bt,bu,t,bv,bh,_(bi,dQ,bk,dR),M,by,bz,dS,bX,bY,bd,_(be,dT,bg,dU),bD,_(y,z,A,dV,bF,bG)),P,_(),bm,_())],bK,_(bL,dX),bN,g)])),dZ,_(ea,_(l,ea,n,eb,p,Y,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,ec,V,W,X,cI,n,br,ba,br,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,ed,x,_(y,z,A,ee),cM,_(y,z,A,cN),O,ef),P,_(),bm,_(),S,[_(T,eg,V,W,X,null,bI,bc,n,bJ,ba,bs,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,ed,x,_(y,z,A,ee),cM,_(y,z,A,cN),O,ef),P,_(),bm,_())],bN,g)]))),eh,_(ei,_(ej,ek,el,_(ej,em),en,_(ej,eo)),ep,_(ej,eq),er,_(ej,es),et,_(ej,eu),ev,_(ej,ew),ex,_(ej,ey),ez,_(ej,eA),eB,_(ej,eC),eD,_(ej,eE),eF,_(ej,eG),eH,_(ej,eI),eJ,_(ej,eK),eL,_(ej,eM),eN,_(ej,eO),eP,_(ej,eQ),eR,_(ej,eS),eT,_(ej,eU),eV,_(ej,eW),eX,_(ej,eY),eZ,_(ej,fa),fb,_(ej,fc),fd,_(ej,fe),ff,_(ej,fg),fh,_(ej,fi),fj,_(ej,fk)));}; 
var b="url",c="登录_首次_.html",d="generationDate",e=new Date(1565142814432.83),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="d662d67c12704d068eb5cbed10178d6a",n="type",o="Axure:Page",p="name",q="登录(首次)",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="c6dc2a0bd899478d93f8d192c66e34a3",V="label",W="",X="friendlyType",Y="主框架",Z="referenceDiagramObject",ba="styleType",bb="visible",bc=true,bd="location",be="x",bf=10,bg="y",bh="size",bi="width",bj=1200,bk="height",bl=675,bm="imageOverrides",bn="masterId",bo="42b294620c2d49c7af5b1798469a7eae",bp="086ea1d50c534d02bf042c1144ab0f6e",bq="Paragraph",br="vectorShape",bs="paragraph",bt="fontWeight",bu="200",bv="4988d43d80b44008a4a415096f1632af",bw=298,bx=74,by="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",bz="fontSize",bA="30px",bB=175,bC=63,bD="foreGroundFill",bE=0xFF999999,bF="opacity",bG=1,bH="40b78d5d7a5347c8bc666facb3f41c04",bI="isContained",bJ="richTextPanel",bK="images",bL="normal~",bM="images/登录_首次_/u95.png",bN="generateCompound",bO="646679035ffe4736b11c36e5d14b03ae",bP="500",bQ=119,bR=50,bS="'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC'",bT="36px",bU=46,bV=55,bW=0xFFF2F2F2,bX="horizontalAlignment",bY="center",bZ="ee6b14b08f37433792de2852348b985d",ca="images/登录_首次_/u97.png",cb="df4f4b2e54fb4f23919190b7b38bc0f3",cc=380,cd=82,ce=1255,cf=29,cg="12px",ch="9ec4f972cecc4362ad07363477e2bd32",ci="images/登录_首次_/u99.png",cj="b7268ad4414a439fbda1d0d3d86f13ae",ck="账号登录",cl="Group",cm="layer",cn=0,co="objs",cp="af4f17d02f354e3ca5d845f0ba0974c9",cq="Text Field",cr="textBox",cs=374,ct=60,cu="stateStyles",cv="hint",cw="44157808f2934100b68f2394a66b2bba",cx=617.5,cy=236.5,cz=0xFF666666,cA="28px",cB="HideHintOnFocused",cC="placeholderText",cD="      输入门店ID",cE="f625e99f50914b58b11c8d7c56104776",cF=306.5,cG="      输入员工账号",cH="e78d0bebe65e44018bc80fbd51fedabd",cI="Rectangle",cJ=608.5,cK=490.5,cL=383,cM="borderFill",cN=0xFFCCCCCC,cO="cornerRadius",cP="12",cQ="98c916898e844865a527f56bc61a500d",cR="'PingFangSC-Regular', 'PingFang SC'",cS="b2c64e9b205444039090a8c4a831b3e9",cT="onClick",cU="description",cV="OnClick",cW="cases",cX="Case 1",cY="isNewIfGroup",cZ="actions",da="action",db="linkWindow",dc="Open 含准备(准备·取餐·AD) in Current Window",dd="target",de="targetType",df="含准备_准备·取餐·ad_.html",dg="includeVariables",dh="linkType",di="current",dj="tabbable",dk="d7192527e838462eab77063b8fe9cc4b",dl=393.5,dm="      输入登录密码",dn="d80a2df813b94a8998e1625e90500905",dp=28,dq=26,dr=622.5,ds=252.5,dt="verticalAlignment",du="middle",dv="8fe67cd165984bf082247f1a385e1623",dw="images/登录_首次_/u107.png",dx="ea69b714c21e457b96d5ed63f4ef67f0",dy=323.5,dz="2e2dde23126b4b5fbebbe754a3511adc",dA="5afefdad8f554842854f66bb9bf651f3",dB=410.5,dC="165f88fe25944178b68e0d367d4196df",dD="94e6a076b56a442fb4439a35a4632c3a",dE="26c731cb771b44a88eb8b6e97e78c80e",dF=347,dG=195,dH=218,dI="6cc1f92c9c7747bc99ab840dfa40cfa7",dJ="2a55314f06d84ad8af7c905948fcfb84",dK=18,dL=326.5,dM=381.5,dN="f3405eb3c46243adbd760ce3c5836193",dO="images/登录_首次_/u115.png",dP="7c6a892e5c814ae3b79d8979fd8be78b",dQ=66,dR=11,dS="8px",dT=619.5,dU=366.5,dV=0xFFFF0000,dW="f8b29e25616d48c5b9d32d7693ebc0ae",dX="images/登录_首次_/u117.png",dY="propagate",dZ="masters",ea="42b294620c2d49c7af5b1798469a7eae",eb="Axure:Master",ec="5a1fbc74d2b64be4b44e2ef951181541",ed="0882bfcd7d11450d85d157758311dca5",ee=0x7FF2F2F2,ef="1",eg="8523194c36f94eec9e7c0acc0e3eedb6",eh="objectPaths",ei="c6dc2a0bd899478d93f8d192c66e34a3",ej="scriptId",ek="u92",el="5a1fbc74d2b64be4b44e2ef951181541",em="u93",en="8523194c36f94eec9e7c0acc0e3eedb6",eo="u94",ep="086ea1d50c534d02bf042c1144ab0f6e",eq="u95",er="40b78d5d7a5347c8bc666facb3f41c04",es="u96",et="646679035ffe4736b11c36e5d14b03ae",eu="u97",ev="ee6b14b08f37433792de2852348b985d",ew="u98",ex="df4f4b2e54fb4f23919190b7b38bc0f3",ey="u99",ez="9ec4f972cecc4362ad07363477e2bd32",eA="u100",eB="b7268ad4414a439fbda1d0d3d86f13ae",eC="u101",eD="af4f17d02f354e3ca5d845f0ba0974c9",eE="u102",eF="f625e99f50914b58b11c8d7c56104776",eG="u103",eH="e78d0bebe65e44018bc80fbd51fedabd",eI="u104",eJ="b2c64e9b205444039090a8c4a831b3e9",eK="u105",eL="d7192527e838462eab77063b8fe9cc4b",eM="u106",eN="d80a2df813b94a8998e1625e90500905",eO="u107",eP="8fe67cd165984bf082247f1a385e1623",eQ="u108",eR="ea69b714c21e457b96d5ed63f4ef67f0",eS="u109",eT="2e2dde23126b4b5fbebbe754a3511adc",eU="u110",eV="5afefdad8f554842854f66bb9bf651f3",eW="u111",eX="165f88fe25944178b68e0d367d4196df",eY="u112",eZ="94e6a076b56a442fb4439a35a4632c3a",fa="u113",fb="6cc1f92c9c7747bc99ab840dfa40cfa7",fc="u114",fd="2a55314f06d84ad8af7c905948fcfb84",fe="u115",ff="f3405eb3c46243adbd760ce3c5836193",fg="u116",fh="7c6a892e5c814ae3b79d8979fd8be78b",fi="u117",fj="f8b29e25616d48c5b9d32d7693ebc0ae",fk="u118";
return _creator();
})());