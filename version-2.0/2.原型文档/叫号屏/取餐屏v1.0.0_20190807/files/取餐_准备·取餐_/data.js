$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(bd,_(be,bf,bg,bf),bh,_(bi,bj,bk,bl)),P,_(),bm,_(),bn,bo),_(T,bp,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bs,bt,bh,_(bi,bu,bk,bv),t,bw,bd,_(be,bx,bg,by),bz,bA,M,bB,bC,bD,bE,bF,x,_(y,z,A,bG)),P,_(),bm,_(),S,[_(T,bH,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bt,bh,_(bi,bu,bk,bv),t,bw,bd,_(be,bx,bg,by),bz,bA,M,bB,bC,bD,bE,bF,x,_(y,z,A,bG)),P,_(),bm,_())],bL,g),_(T,bM,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bs,bN,bh,_(bi,bO,bk,bv),t,bw,bd,_(be,bP,bg,by),bz,bA,M,bQ,bE,bF,x,_(y,z,A,B),bC,bD),P,_(),bm,_(),S,[_(T,bR,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bN,bh,_(bi,bO,bk,bv),t,bw,bd,_(be,bP,bg,by),bz,bA,M,bQ,bE,bF,x,_(y,z,A,B),bC,bD),P,_(),bm,_())],bL,g),_(T,bS,V,W,X,bT,n,br,ba,bK,bb,bc,s,_(bs,bN,t,bU,bh,_(bi,bV,bk,bW),M,bQ,bE,bF,bd,_(be,bX,bg,bY)),P,_(),bm,_(),S,[_(T,bZ,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bN,t,bU,bh,_(bi,bV,bk,bW),M,bQ,bE,bF,bd,_(be,bX,bg,bY)),P,_(),bm,_())],ca,_(cb,cc),bL,g),_(T,cd,V,W,X,bT,n,br,ba,bK,bb,bc,s,_(bs,bt,t,bU,bh,_(bi,ce,bk,bW),M,bB,bE,bF,bd,_(be,cf,bg,cg)),P,_(),bm,_(),S,[_(T,ch,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bt,t,bU,bh,_(bi,ce,bk,bW),M,bB,bE,bF,bd,_(be,cf,bg,cg)),P,_(),bm,_())],ca,_(cb,ci),bL,g),_(T,cj,V,W,X,bT,n,br,ba,bK,bb,bc,s,_(bs,bN,t,bU,bh,_(bi,ck,bk,cl),M,bQ,bE,bF,bd,_(be,cm,bg,bY)),P,_(),bm,_(),S,[_(T,cn,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bN,t,bU,bh,_(bi,ck,bk,cl),M,bQ,bE,bF,bd,_(be,cm,bg,bY)),P,_(),bm,_())],ca,_(cb,co),bL,g),_(T,cp,V,W,X,bT,n,br,ba,bK,bb,bc,s,_(bs,bt,t,bU,bh,_(bi,ce,bk,cq),M,bB,bE,bF,bd,_(be,cr,bg,cg)),P,_(),bm,_(),S,[_(T,cs,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bs,bt,t,bU,bh,_(bi,ce,bk,cq),M,bB,bE,bF,bd,_(be,cr,bg,cg)),P,_(),bm,_())],ca,_(cb,ct),bL,g)])),cu,_(cv,_(l,cv,n,cw,p,Y,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,cx,V,W,X,bq,n,br,ba,br,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,bw,x,_(y,z,A,cy),cz,_(y,z,A,cA),O,cB),P,_(),bm,_(),S,[_(T,cC,V,W,X,null,bI,bc,n,bJ,ba,bK,bb,bc,s,_(bh,_(bi,bj,bk,bl),t,bw,x,_(y,z,A,cy),cz,_(y,z,A,cA),O,cB),P,_(),bm,_())],bL,g)]))),cD,_(cE,_(cF,cG,cH,_(cF,cI),cJ,_(cF,cK)),cL,_(cF,cM),cN,_(cF,cO),cP,_(cF,cQ),cR,_(cF,cS),cT,_(cF,cU),cV,_(cF,cW),cX,_(cF,cY),cZ,_(cF,da),db,_(cF,dc),dd,_(cF,de),df,_(cF,dg),dh,_(cF,di)));}; 
var b="url",c="取餐_准备·取餐_.html",d="generationDate",e=new Date(1565142815481.09),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="a8842fb215fb4b8daefbf76e800658bf",n="type",o="Axure:Page",p="name",q="取餐(准备·取餐)",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="b1e539593b04455a877e8cd017c0104e",V="label",W="",X="friendlyType",Y="主框架",Z="referenceDiagramObject",ba="styleType",bb="visible",bc=true,bd="location",be="x",bf=10,bg="y",bh="size",bi="width",bj=1200,bk="height",bl=675,bm="imageOverrides",bn="masterId",bo="42b294620c2d49c7af5b1798469a7eae",bp="ce25605891a94bbe9ce2e4df31e3d1f0",bq="Rectangle",br="vectorShape",bs="fontWeight",bt="200",bu=475,bv=45,bw="0882bfcd7d11450d85d157758311dca5",bx=39,by=53,bz="verticalAlignment",bA="top",bB="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",bC="horizontalAlignment",bD="left",bE="fontSize",bF="28px",bG=0xFFF2F2F2,bH="d910ecbb846e414d978531d4ef6b605f",bI="isContained",bJ="richTextPanel",bK="paragraph",bL="generateCompound",bM="ce59f9fc0a984df3b979f1a83186b556",bN="500",bO=466,bP=658,bQ="'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC'",bR="2d5e37ca70154e679c33284016eb6fc6",bS="6790dfaebb984ba680a1612dc49baba2",bT="Paragraph",bU="4988d43d80b44008a4a415096f1632af",bV=153,bW=520,bX=674,bY=99,bZ="03e50b5278e74635bbba2fda70370c93",ca="images",cb="normal~",cc="images/含准备_准备·取餐·ad_/u171.png",cd="8c5ed586c2494842bd7c76c3bf234c0b",ce=143,cf=51,cg=108,ch="1eb06902ac6c416f815524a328cb686a",ci="images/含准备_准备·取餐·ad_/u173.png",cj="d86e5757990044baa70aa0077fe57a87",ck=93,cl=40,cm=760,cn="a628a30941d54073ba5e1af5efd523e6",co="images/含准备_准备·取餐·ad_/u175.png",cp="c92ed111886544d38c420c2a0d6b94c3",cq=80,cr=139,cs="0f561656586649e4acafef55afdb3d1a",ct="images/取餐_准备·取餐_/u490.png",cu="masters",cv="42b294620c2d49c7af5b1798469a7eae",cw="Axure:Master",cx="5a1fbc74d2b64be4b44e2ef951181541",cy=0x7FF2F2F2,cz="borderFill",cA=0xFFCCCCCC,cB="1",cC="8523194c36f94eec9e7c0acc0e3eedb6",cD="objectPaths",cE="b1e539593b04455a877e8cd017c0104e",cF="scriptId",cG="u477",cH="5a1fbc74d2b64be4b44e2ef951181541",cI="u478",cJ="8523194c36f94eec9e7c0acc0e3eedb6",cK="u479",cL="ce25605891a94bbe9ce2e4df31e3d1f0",cM="u480",cN="d910ecbb846e414d978531d4ef6b605f",cO="u481",cP="ce59f9fc0a984df3b979f1a83186b556",cQ="u482",cR="2d5e37ca70154e679c33284016eb6fc6",cS="u483",cT="6790dfaebb984ba680a1612dc49baba2",cU="u484",cV="03e50b5278e74635bbba2fda70370c93",cW="u485",cX="8c5ed586c2494842bd7c76c3bf234c0b",cY="u486",cZ="1eb06902ac6c416f815524a328cb686a",da="u487",db="d86e5757990044baa70aa0077fe57a87",dc="u488",dd="a628a30941d54073ba5e1af5efd523e6",de="u489",df="c92ed111886544d38c420c2a0d6b94c3",dg="u490",dh="0f561656586649e4acafef55afdb3d1a",di="u491";
return _creator();
})());