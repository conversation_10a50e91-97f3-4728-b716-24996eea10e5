$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z,A,[_(u,B,w,x,y,z,A,[_(u,C,w,D,y,E,A,[]),_(u,F,w,D,y,G)]),_(u,H,w,x,y,z,A,[_(u,C,w,D,y,I),_(u,F,w,D,y,J)])])]),K,_(L,z),M,_(N,O,P,_(Q,R,S,R),T,U),V,[],W,_(X,_(Y,Z,ba,bb,bc,bd,be,bf,bg,bh,bi,_(bj,bk,bl,bm,bn,bo),bp,bq,br,f,bs,bt,bu,bf,bv,_(bw,R,bx,R),P,_(Q,R,S,R),by,d,bz,f,bA,Z,bB,_(bj,bk,bl,bC),bD,_(bj,bk,bl,bE),bF,bG,bH,bk,bn,bG,bI,bJ,bK,bL,bM,bN,bO,bN,bP,bN,bQ,bN,bR,_(),bS,bJ,bT,bJ,bU,_(bV,f,bW,bX,bY,bX,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,cf)),cg,_(bV,f,bW,R,bY,bX,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,cf)),ch,_(bV,f,bW,bo,bY,bo,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,ci))),cj,_(ck,_(Y,cl),cm,_(Y,cn,bF,bJ,bB,_(bj,bk,bl,co)),cp,_(Y,cq,bF,bJ,bB,_(bj,bk,bl,cr)),cs,_(Y,ct),cu,_(Y,cv,ba,bb,bc,bd,be,bf,bg,bh,bi,_(bj,bk,bl,bm,bn,bo),bD,_(bj,bk,bl,cw),bF,bG,bB,_(bj,bk,bl,cx),bs,bt,bp,bq,br,f,bH,bk,bI,bJ,bn,bG,bU,_(bV,f,bW,bX,bY,bX,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,cf)),cg,_(bV,f,bW,R,bY,bX,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,cf)),ch,_(bV,f,bW,bo,bY,bo,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,ci)),bK,bL,bM,bN,bO,bN,bP,bN,bQ,bN,bu,bf),cy,_(Y,cz,bF,bJ),cA,_(Y,cB,bI,bh),cC,_(Y,cD,bp,cE,bc,cF,bF,bJ,bB,_(bj,bk,bl,cG),bs,cH,bK,cI,bM,bJ,bO,bJ,bP,bJ,bQ,bJ),cJ,_(Y,cK,bp,cL,bc,cF,bF,bJ,bB,_(bj,bk,bl,cG),bs,cH,bK,cI,bM,bJ,bO,bJ,bP,bJ,bQ,bJ),cM,_(Y,cN,bp,cO,bc,cF,bF,bJ,bB,_(bj,bk,bl,cG),bs,cH,bK,cI,bM,bJ,bO,bJ,bP,bJ,bQ,bJ),cP,_(Y,cQ,bp,cR,bc,cF,bF,bJ,bB,_(bj,bk,bl,cG),bs,cH,bK,cI,bM,bJ,bO,bJ,bP,bJ,bQ,bJ),cS,_(Y,cT,bc,cF,bF,bJ,bB,_(bj,bk,bl,cG),bs,cH,bK,cI,bM,bJ,bO,bJ,bP,bJ,bQ,bJ),cU,_(Y,cV,bp,cW,bc,cF,bF,bJ,bB,_(bj,bk,bl,cG),bs,cH,bK,cI,bM,bJ,bO,bJ,bP,bJ,bQ,bJ),cX,_(Y,cY,bp,cR,bF,bJ,bB,_(bj,bk,bl,cG),bs,cH,bK,cI,bM,bJ,bO,bJ,bP,bJ,bQ,bJ),cZ,_(Y,da,bF,bJ,bB,_(bj,bk,bl,cG),bs,cH,bK,cI,bM,bJ,bO,bJ,bP,bJ,bQ,bJ),db,_(Y,dc,bB,_(bj,bk,bl,cG)),dd,_(Y,de,bF,bh,bB,_(bj,bk,bl,cG)),df,_(Y,dg,bi,_(bj,bk,bl,dh,bn,bo),bs,cH,bK,bL),di,_(Y,dj,bi,_(bj,bk,bl,dh,bn,bo),bs,cH,bK,cI),dk,_(Y,dl,bi,_(bj,bk,bl,dh,bn,bo),bs,cH,bK,cI),dm,_(Y,dn,bi,_(bj,bk,bl,dh,bn,bo),bs,cH,bK,cI),dp,_(Y,dq,bs,cH,bK,cI),dr,_(Y,ds,bs,cH,bK,cI),dt,_(Y,du,bs,bt),dv,_(Y,dw,bF,bJ,bB,_(bj,bk,bl,cG),bs,cH,bK,bL),dx,_(Y,dy),dz,_(Y,dA,bB,_(bj,bk,bl,cG)),dB,_(Y,dC,ba,dD,bc,bd,be,bf,bg,bh,bi,_(bj,bk,bl,dE,bn,bo),bD,_(bj,bk,bl,cw),bF,bG,bs,bt,bp,dF,br,f,bH,bk,bI,bJ,bB,_(bj,bk,bl,bC),bn,bG,bU,_(bV,f,bW,bX,bY,bX,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,cf)),cg,_(bV,f,bW,R,bY,bX,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,cf)),ch,_(bV,f,bW,bo,bY,bo,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,ci)),bK,bL,bM,bN,bO,bN,bP,bN,bQ,bN,bu,bf),dG,_(Y,dH,bi,_(bj,bk,bl,bC,bn,bo),bD,_(bj,bk,bl,bC),bB,_(bj,bk,bl,dI),bU,_(bV,d,bW,bo,bY,bo,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,dJ))),dK,_(Y,dL,bB,_(bj,dM,dN,[_(bl,bC),_(bl,co),_(bl,dO),_(bl,bC)])),dP,_(Y,dQ),dR,_(Y,dS,ba,bb,bc,bd,be,bf,bg,bh,bi,_(bj,bk,bl,bm,bn,bo),bp,bq,br,f,bs,bt,bu,bf,bB,_(bj,bk,bl,bC),bD,_(bj,bk,bl,bm),bF,bG,bH,bk,bn,bG,bI,bJ,bK,bL,bM,bN,bO,bN,bP,bN,bQ,bN,bU,_(bV,f,bW,bX,bY,bX,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,cf)),cg,_(bV,f,bW,R,bY,bX,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,cf)),ch,_(bV,f,bW,bo,bY,bo,bZ,bX,bl,_(ca,cb,cc,cb,cd,cb,ce,ci))),dT,_(Y,dU,bD,_(bj,bk,bl,dh)),dV,_(Y,dW,bF,bJ,bB,_(bj,bk,bl,bm))),dX,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="管理商品",w="type",x="Folder",y="url",z="",A="children",B="商品库",C="添加/编辑单品1.7.0",D="Wireframe",E="添加_编辑单品1_7_0.html",F="商品图库1.7.0",G="商品图库1_7_0.html",H="门店商品",I="添加_编辑单品1_7_0_1.html",J="商品图库1_7_0_1.html",K="globalVariables",L="onloadvariable",M="defaultAdaptiveView",N="name",O="Base",P="size",Q="width",R=0,S="height",T="condition",U="<=",V="adaptiveViews",W="stylesheet",X="defaultStyle",Y="id",Z="627587b6038d43cca051c114ac41ad32",ba="fontName",bb="'ArialMT', 'Arial'",bc="fontWeight",bd="400",be="fontStyle",bf="normal",bg="fontStretch",bh="5",bi="foreGroundFill",bj="fillType",bk="solid",bl="color",bm=0xFF333333,bn="opacity",bo=1,bp="fontSize",bq="13px",br="underline",bs="horizontalAlignment",bt="center",bu="lineSpacing",bv="location",bw="x",bx="y",by="visible",bz="limbo",bA="baseStyle",bB="fill",bC=0xFFFFFFFF,bD="borderFill",bE=0xFF797979,bF="borderWidth",bG="1",bH="linePattern",bI="cornerRadius",bJ="0",bK="verticalAlignment",bL="middle",bM="paddingLeft",bN="2",bO="paddingTop",bP="paddingRight",bQ="paddingBottom",bR="stateStyles",bS="rotation",bT="textRotation",bU="outerShadow",bV="on",bW="offsetX",bX=5,bY="offsetY",bZ="blurRadius",ca="r",cb=0,cc="g",cd="b",ce="a",cf=0.349019607843137,cg="innerShadow",ch="textShadow",ci=0.647058823529412,cj="customStyles",ck="box_1",cl="********************************",cm="box_2",cn="********************************",co=0xFFF2F2F2,cp="box_3",cq="********************************",cr=0xFFD7D7D7,cs="ellipse",ct="eff044fe6497434a8c5f89f769ddde3b",cu="_形状",cv="40519e9ec4264601bfb12c514e4f4867",cw=0xFFCCCCCC,cx=0x19333333,cy="image",cz="75a91ee5b9d042cfa01b8d565fe289c0",cA="button",cB="c9f35713a1cf4e91a0f2dbac65e6fb5c",cC="heading_1",cD="1111111151944dfba49f67fd55eb1f88",cE="32px",cF="bold",cG=0xFFFFFF,cH="left",cI="top",cJ="heading_2",cK="b3a15c9ddde04520be40f94c8168891e",cL="24px",cM="heading_3",cN="8c7a4c5ad69a4369a5f7788171ac0b32",cO="18px",cP="heading_4",cQ="e995c891077945c89c0b5fe110d15a0b",cR="14px",cS="heading_5",cT="386b19ef4be143bd9b6c392ded969f89",cU="heading_6",cV="fc3b9a13b5574fa098ef0a1db9aac861",cW="10px",cX="label",cY="2285372321d148ec80932747449c36c9",cZ="paragraph",da="4988d43d80b44008a4a415096f1632af",db="line",dc="619b2148ccc1497285562264d51992f9",dd="arrow",de="d148f2c5268542409e72dde43e40043e",df="text_field",dg="44157808f2934100b68f2394a66b2bba",dh=0xFF000000,di="text_area",dj="42ee17691d13435b8256d8d0a814778f",dk="droplist",dl="85f724022aae41c594175ddac9c289eb",dm="list_box",dn="********************************",dp="checkbox",dq="********************************",dr="radio_button",ds="4eb5516f311c4bdfa0cb11d7ea75084e",dt="html_button",du="eed12d9ebe2e4b9689b3b57949563dca",dv="tree_node",dw="93a4c3353b6f4562af635b7116d6bf94",dx="table_cell",dy="33ea2511485c479dbf973af3302f2352",dz="menu_item",dA="2036b2baccbc41f0b9263a6981a11a42",dB="connector",dC="699a012e142a4bcba964d96e88b88bdf",dD="'PingFangSC-Regular', 'PingFang SC'",dE=0xFF0000FF,dF="12px",dG="marker",dH="a8e305fe5c2a462b995b0021a9ba82b9",dI=0xFF009DD9,dJ=0.698039215686274,dK="flow_shape",dL="df01900e3c4e43f284bafec04b0864c4",dM="linearGradient",dN="colors",dO=0xFFE4E4E4,dP="table",dQ="d612b8c2247342eda6a8bc0663265baa",dR="shape",dS="98c916898e844865a527f56bc61a500d",dT="horizontal_line",dU="f48196c19ab74fb7b3acb5151ce8ea2d",dV="icon",dW="26c731cb771b44a88eb8b6e97e78c80e",dX="duplicateStyles";
return _creator();
})());