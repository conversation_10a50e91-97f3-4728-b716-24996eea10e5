body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1883px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u912_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:720px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u912 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:720px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u912_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u913 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:440px;
}
#u914_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u914 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u914_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u915 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u915_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u916_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u916 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u916_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u917_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u917 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:left;
}
#u917_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u918_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u918 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u918_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u919_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u919 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:200px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u919_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u920_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u920 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:240px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u920_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u921_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u921 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:280px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u921_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u922_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u922 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:320px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u922_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u923_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u923 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:360px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u923_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u924_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u924 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:400px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:left;
}
#u924_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u925_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:709px;
  height:1px;
}
#u925 {
  border-width:0px;
  position:absolute;
  left:-154px;
  top:424px;
  width:708px;
  height:0px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u925_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:72px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u927 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:1200px;
  height:72px;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u927_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:28px;
  width:1196px;
  word-wrap:break-word;
}
#u928_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:71px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u928 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:1200px;
  height:71px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u928_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u929_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
}
#u929 {
  border-width:0px;
  position:absolute;
  left:1066px;
  top:18px;
  width:55px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
}
#u929_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  white-space:nowrap;
}
#u930_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:21px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u930 {
  border-width:0px;
  position:absolute;
  left:1133px;
  top:16px;
  width:54px;
  height:21px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u930_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:51px;
  white-space:nowrap;
}
#u931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u931 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:17px;
  width:126px;
  height:22px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u931_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  word-wrap:break-word;
}
#u932_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1201px;
  height:2px;
}
#u932 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:1200px;
  height:1px;
}
#u932_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u933 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:10px;
  width:499px;
  height:39px;
}
#u934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:39px;
}
#u934 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u934_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:46px;
  word-wrap:break-word;
}
#u935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u935 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u935_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:39px;
}
#u936 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:0px;
  width:60px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u936_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:56px;
  word-wrap:break-word;
}
#u937_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u937 {
  border-width:0px;
  position:absolute;
  left:190px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u937_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u938_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:39px;
}
#u938 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:74px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u938_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:70px;
  word-wrap:break-word;
}
#u939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u939 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u939_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u940_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:39px;
}
#u940 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:0px;
  width:75px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u940_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:71px;
  word-wrap:break-word;
}
#u941_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u941 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:11px;
  width:34px;
  height:34px;
}
#u941_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:721px;
  height:2px;
}
#u942 {
  border-width:0px;
  position:absolute;
  left:-160px;
  top:429px;
  width:720px;
  height:1px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u942_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u944_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:39px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  -webkit-box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u944 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:71px;
  width:1000px;
  height:39px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u944_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u945_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u945 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:103px;
  width:72px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u945_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  word-wrap:break-word;
}
#u946_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:443px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u946 {
  border-width:0px;
  position:absolute;
  left:1359px;
  top:103px;
  width:443px;
  height:34px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u946_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:443px;
  word-wrap:break-word;
}
#u947 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u948_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:475px;
  height:337px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u948 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:213px;
  width:475px;
  height:337px;
}
#u948_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u949_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:475px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u949 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:213px;
  width:475px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u949_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:471px;
  word-wrap:break-word;
}
#u950_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u950 {
  border-width:0px;
  position:absolute;
  left:1718px;
  top:220px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u950_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u951_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u951 {
  border-width:0px;
  position:absolute;
  left:1320px;
  top:283px;
  width:436px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u951_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  word-wrap:break-word;
}
#u952_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  height:51px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#000000;
}
#u952 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:310px;
  width:436px;
  height:51px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#000000;
}
#u952_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  word-wrap:break-word;
}
#u953_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:9px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u953 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:462px;
  width:140px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u953_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:136px;
  word-wrap:break-word;
}
#u954_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:9px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u954 {
  border-width:0px;
  position:absolute;
  left:1497px;
  top:462px;
  width:140px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u954_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:136px;
  word-wrap:break-word;
}
#u955_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u955 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:393px;
  width:56px;
  height:17px;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u955_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  white-space:nowrap;
}
#u956_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u956 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:81px;
  width:53px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u956_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  white-space:nowrap;
}
#u957_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u957 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:169px;
  width:510px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u957_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  word-wrap:break-word;
}
#u958_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u958 {
  border-width:0px;
  position:absolute;
  left:1366px;
  top:169px;
  width:436px;
  height:34px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u958_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  word-wrap:break-word;
}
#u959_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u959 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:65px;
  width:72px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u959_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  word-wrap:break-word;
}
#u960_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u960 {
  border-width:0px;
  position:absolute;
  left:1359px;
  top:65px;
  width:271px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u960_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  word-wrap:break-word;
}
#u961 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:652px;
  width:583px;
  height:120px;
}
#u962_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u962 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u962_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u963_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u963 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:0px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u963_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u964_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u964 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u964_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u965 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:30px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u965_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u966_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u966 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u966_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u967_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u967 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:60px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u967_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u968_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u968 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:90px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u968_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u969_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u969 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:90px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u969_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u970_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u970 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:796px;
  width:133px;
  height:34px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u970_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  white-space:nowrap;
}
#u971_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u971 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:635px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u971_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  white-space:nowrap;
}
#u972 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:116px;
  width:1000px;
  height:44px;
}
#u973_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:44px;
}
#u973 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:44px;
}
#u973_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u975_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:66px;
}
#u975 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:204px;
  width:78px;
  height:66px;
}
#u975_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u977_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:66px;
}
#u977 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:204px;
  width:78px;
  height:66px;
}
#u977_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u979_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:66px;
}
#u979 {
  border-width:0px;
  position:absolute;
  left:639px;
  top:204px;
  width:78px;
  height:66px;
}
#u979_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u981_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:66px;
}
#u981 {
  border-width:0px;
  position:absolute;
  left:818px;
  top:204px;
  width:78px;
  height:66px;
}
#u981_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u983_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:66px;
}
#u983 {
  border-width:0px;
  position:absolute;
  left:999px;
  top:204px;
  width:78px;
  height:66px;
}
#u983_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u984 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:270px;
  width:908px;
  height:31px;
}
#u985_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:31px;
}
#u985 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:31px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u985_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:10px;
  width:178px;
  word-wrap:break-word;
}
#u986_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:31px;
}
#u986 {
  border-width:0px;
  position:absolute;
  left:182px;
  top:0px;
  width:182px;
  height:31px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u986_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:10px;
  width:178px;
  word-wrap:break-word;
}
#u987_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:31px;
}
#u987 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:0px;
  width:182px;
  height:31px;
  font-size:11px;
  color:#000000;
}
#u987_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:178px;
  word-wrap:break-word;
}
#u988_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:31px;
}
#u988 {
  border-width:0px;
  position:absolute;
  left:546px;
  top:0px;
  width:182px;
  height:31px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u988_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:10px;
  width:178px;
  word-wrap:break-word;
}
#u989_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:180px;
  height:31px;
}
#u989 {
  border-width:0px;
  position:absolute;
  left:728px;
  top:0px;
  width:180px;
  height:31px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u989_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:10px;
  width:176px;
  word-wrap:break-word;
}
#u990 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#0000FF;
}
#u991 {
  border-width:0px;
  position:absolute;
  left:221px;
  top:121px;
  width:62px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#0000FF;
}
#u991_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:58px;
  word-wrap:break-word;
}
#u992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u992 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:121px;
  width:80px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u992_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:76px;
  word-wrap:break-word;
}
#u993 {
  border-width:0px;
  position:absolute;
  left:916px;
  top:121px;
  width:232px;
  height:30px;
}
#u993_input {
  position:absolute;
  left:0px;
  top:0px;
  width:232px;
  height:30px;
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'AppleColorEmoji', 'Apple Color Emoji';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u994 {
  border-width:0px;
  position:absolute;
  left:1120px;
  top:126px;
  width:18px;
  height:18px;
  font-family:'AppleColorEmoji', 'Apple Color Emoji';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u994_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  white-space:nowrap;
}
#u995_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u995 {
  border-width:0px;
  position:absolute;
  left:514px;
  top:121px;
  width:80px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u995_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:76px;
  word-wrap:break-word;
}
#u996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u996 {
  border-width:0px;
  position:absolute;
  left:299px;
  top:121px;
  width:98px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u996_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:94px;
  word-wrap:break-word;
}
#u998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:78px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u998 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:356px;
  width:78px;
  height:78px;
}
#u998_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1000_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:78px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1000 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:356px;
  width:78px;
  height:78px;
}
#u1000_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:78px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1002 {
  border-width:0px;
  position:absolute;
  left:639px;
  top:356px;
  width:78px;
  height:78px;
}
#u1002_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1004_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:78px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1004 {
  border-width:0px;
  position:absolute;
  left:818px;
  top:356px;
  width:78px;
  height:78px;
}
#u1004_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1006_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:78px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1006 {
  border-width:0px;
  position:absolute;
  left:999px;
  top:356px;
  width:78px;
  height:78px;
}
#u1006_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1007 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:434px;
  width:908px;
  height:30px;
}
#u1008_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u1008 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1008_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:178px;
  word-wrap:break-word;
}
#u1009_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u1009 {
  border-width:0px;
  position:absolute;
  left:182px;
  top:0px;
  width:182px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1009_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:178px;
  word-wrap:break-word;
}
#u1010_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u1010 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:0px;
  width:182px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1010_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:178px;
  word-wrap:break-word;
}
#u1011_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u1011 {
  border-width:0px;
  position:absolute;
  left:546px;
  top:0px;
  width:182px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1011_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:178px;
  word-wrap:break-word;
}
#u1012_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:180px;
  height:30px;
}
#u1012 {
  border-width:0px;
  position:absolute;
  left:728px;
  top:0px;
  width:180px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1012_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:176px;
  word-wrap:break-word;
}
#u1013 {
  border-width:0px;
  position:absolute;
  left:258px;
  top:178px;
  width:134px;
  height:117px;
}
#u1014_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:117px;
}
#u1014 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:117px;
}
#u1014_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1015_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1015 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:276px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1015_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1016 {
  border-width:0px;
  position:absolute;
  left:314px;
  top:196px;
  width:83px;
  height:82px;
}
#u1017_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
}
#u1017 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0000FF;
}
#u1017_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:79px;
  word-wrap:break-word;
}
#u1018_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
}
#u1018 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:83px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0000FF;
}
#u1018_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:79px;
  word-wrap:break-word;
}
#u1019_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:22px;
}
#u1019 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:83px;
  height:22px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0000FF;
}
#u1019_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:5px;
  width:79px;
  word-wrap:break-word;
}
#u1020 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1021 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:111px;
  width:1000px;
  height:44px;
}
#u1022_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:44px;
}
#u1022 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:44px;
  text-align:left;
}
#u1022_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1023_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1023 {
  border-width:0px;
  position:absolute;
  left:964px;
  top:118px;
  width:80px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1023_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:76px;
  word-wrap:break-word;
}
#u1024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1024 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:118px;
  width:98px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u1024_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:94px;
  word-wrap:break-word;
}
#u1025_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1025 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:124px;
  width:75px;
  height:18px;
}
#u1025_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  white-space:nowrap;
}
#u1026 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:296px;
  width:100px;
  height:16px;
}
#u1026_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1026_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1027 {
  border-width:0px;
  position:absolute;
  left:317px;
  top:297px;
  width:100px;
  height:16px;
}
#u1027_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1027_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1028 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:297px;
  width:100px;
  height:16px;
}
#u1028_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1028_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1029 {
  border-width:0px;
  position:absolute;
  left:677px;
  top:297px;
  width:100px;
  height:16px;
}
#u1029_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1029_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1030 {
  border-width:0px;
  position:absolute;
  left:1040px;
  top:297px;
  width:100px;
  height:16px;
}
#u1030_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1030_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1031 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:460px;
  width:100px;
  height:16px;
}
#u1031_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1031_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1032 {
  border-width:0px;
  position:absolute;
  left:317px;
  top:461px;
  width:100px;
  height:16px;
}
#u1032_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1032_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1033 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:461px;
  width:100px;
  height:16px;
}
#u1033_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1033_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1034 {
  border-width:0px;
  position:absolute;
  left:677px;
  top:461px;
  width:100px;
  height:16px;
}
#u1034_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1034_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1035 {
  border-width:0px;
  position:absolute;
  left:1040px;
  top:461px;
  width:100px;
  height:16px;
}
#u1035_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1035_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1037_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1037 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:82px;
  width:179px;
  height:17px;
}
#u1037_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  white-space:nowrap;
}
#u1038 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1039_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  height:250px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u1039 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:102px;
  width:195px;
  height:250px;
}
#u1039_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1040_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1040 {
  border-width:0px;
  position:absolute;
  left:429px;
  top:125px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1040_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1041 {
  border-width:0px;
  position:absolute;
  left:282px;
  top:118px;
  width:143px;
  height:30px;
}
#u1041_input {
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1042 {
  border-width:0px;
  position:absolute;
  left:282px;
  top:158px;
  width:165px;
  height:34px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1042_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:147px;
  word-wrap:break-word;
}
#u1042_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1043 {
  border-width:0px;
  position:absolute;
  left:282px;
  top:192px;
  width:151px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1043_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:133px;
  word-wrap:break-word;
}
#u1043_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1044 {
  border-width:0px;
  position:absolute;
  left:282px;
  top:219px;
  width:151px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1044_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:133px;
  word-wrap:break-word;
}
#u1044_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1045 {
  border-width:0px;
  position:absolute;
  left:282px;
  top:246px;
  width:151px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1045_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:133px;
  word-wrap:break-word;
}
#u1045_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1046 {
  border-width:0px;
  position:absolute;
  left:282px;
  top:280px;
  width:151px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1046_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:133px;
  word-wrap:break-word;
}
#u1046_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1047 {
  border-width:0px;
  position:absolute;
  left:282px;
  top:307px;
  width:151px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1047_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:133px;
  word-wrap:break-word;
}
#u1047_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1048_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:10px;
  height:36px;
}
#u1048 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:161px;
  width:5px;
  height:31px;
}
#u1048_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
