body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1979px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u1049 {
  position:absolute;
  left:247px;
  top:528px;
}
#u1049_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:936px;
  height:361px;
  background-image:none;
}
#u1049_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1050_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1050 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:103px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1050_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1052 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:137px;
  width:82px;
  height:198px;
}
#u1053_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1053 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1053_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1054_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:118px;
}
#u1054 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:82px;
  height:118px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1054_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1055_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1055 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:158px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1055_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:78px;
  word-wrap:break-word;
}
#u1055_ann {
  border-width:0px;
  position:absolute;
  left:68px;
  top:158px;
  width:1px;
  height:1px;
}
#u1056 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:175px;
  width:914px;
  height:118px;
}
#u1056_input {
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:118px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1057_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1057 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:331px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1057_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1058_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:27px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1058 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:148px;
  width:67px;
  height:27px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1058_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5px;
  width:67px;
  word-wrap:break-word;
}
#u1059_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:27px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1059 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:148px;
  width:65px;
  height:27px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1059_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5px;
  width:65px;
  word-wrap:break-word;
}
#u1060_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:118px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#999999;
}
#u1060 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:175px;
  width:914px;
  height:118px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#999999;
}
#u1060_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:50px;
  width:910px;
  word-wrap:break-word;
}
#u1062 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:0px;
  width:926px;
  height:87px;
}
#u1063_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:87px;
}
#u1063 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:87px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1063_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1064_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1064 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:5px;
  width:137px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1064_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  white-space:nowrap;
}
#u1064_ann {
  border-width:0px;
  position:absolute;
  left:155px;
  top:1px;
  width:1px;
  height:1px;
}
#u1065 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:28px;
  width:630px;
  height:21px;
}
#u1066_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1066 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1066_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1067_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1067 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1067_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1068_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1068 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1068_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1069_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1069 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1069_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1070_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1070 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1070_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1071_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:21px;
}
#u1071 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:0px;
  width:93px;
  height:21px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1071_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:89px;
  word-wrap:break-word;
}
#u1071_ann {
  border-width:0px;
  position:absolute;
  left:529px;
  top:0px;
  width:1px;
  height:1px;
}
#u1072_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:21px;
}
#u1072 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:0px;
  width:87px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1072_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1073 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:58px;
  width:58px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1073_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:40px;
  word-wrap:break-word;
}
#u1073_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1074 {
  border-width:0px;
  position:absolute;
  left:290px;
  top:58px;
  width:85px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1074_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:67px;
  word-wrap:break-word;
}
#u1074_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1074_ann {
  border-width:0px;
  position:absolute;
  left:368px;
  top:54px;
  width:1px;
  height:1px;
}
#u1075 {
  border-width:0px;
  position:absolute;
  left:379px;
  top:58px;
  width:89px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1075_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:71px;
  word-wrap:break-word;
}
#u1075_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1075_ann {
  border-width:0px;
  position:absolute;
  left:461px;
  top:54px;
  width:1px;
  height:1px;
}
#u1076 {
  border-width:0px;
  position:absolute;
  left:121px;
  top:51px;
  width:69px;
  height:30px;
}
#u1076_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1077 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:51px;
  width:69px;
  height:30px;
}
#u1077_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1077_ann {
  border-width:0px;
  position:absolute;
  left:264px;
  top:47px;
  width:1px;
  height:1px;
}
#u1078 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:58px;
  width:78px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1078_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:60px;
  word-wrap:break-word;
}
#u1078_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1079 {
  border-width:0px;
  position:absolute;
  left:633px;
  top:58px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1079_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:43px;
  word-wrap:break-word;
}
#u1079_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1080 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:58px;
  width:69px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1080_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:51px;
  word-wrap:break-word;
}
#u1080_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1081 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:51px;
  width:69px;
  height:30px;
}
#u1081_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1081_ann {
  border-width:0px;
  position:absolute;
  left:90px;
  top:47px;
  width:1px;
  height:1px;
}
#u1082_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1082 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:36px;
  width:49px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1082_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  white-space:nowrap;
}
#u1083 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1084_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u1084 {
  border-width:0px;
  position:absolute;
  left:146px;
  top:194px;
  width:362px;
  height:237px;
}
#u1084_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1085_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1085 {
  border-width:0px;
  position:absolute;
  left:146px;
  top:194px;
  width:362px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1085_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:358px;
  word-wrap:break-word;
}
#u1086_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1086 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:201px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1086_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1087_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1087 {
  border-width:0px;
  position:absolute;
  left:461px;
  top:201px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1087_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1088 {
  border-width:0px;
  position:absolute;
  left:153px;
  top:233px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1088_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1088_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1089 {
  border-width:0px;
  position:absolute;
  left:153px;
  top:393px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1089_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1089_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1090 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:285px;
  width:126px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1090_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:108px;
  word-wrap:break-word;
}
#u1090_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1091 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:312px;
  width:126px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1091_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:108px;
  word-wrap:break-word;
}
#u1091_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1092 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:339px;
  width:126px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1092_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:108px;
  word-wrap:break-word;
}
#u1092_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1093 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:366px;
  width:126px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1093_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:108px;
  word-wrap:break-word;
}
#u1093_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1094_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:30px;
  height:10px;
}
#u1094 {
  border-width:0px;
  position:absolute;
  left:478px;
  top:250px;
  width:25px;
  height:5px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1094_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1095 {
  border-width:0px;
  position:absolute;
  left:153px;
  top:258px;
  width:75px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1095_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:57px;
  word-wrap:break-word;
}
#u1095_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1096_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1096 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:331px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1096_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1049_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:936px;
  height:739px;
  visibility:hidden;
  background-image:none;
}
#u1049_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1098 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:105px;
  width:926px;
  height:87px;
}
#u1099_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:87px;
}
#u1099 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:87px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1099_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1100_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1100 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:110px;
  width:125px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1100_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  white-space:nowrap;
}
#u1100_ann {
  border-width:0px;
  position:absolute;
  left:140px;
  top:106px;
  width:1px;
  height:1px;
}
#u1101 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:133px;
  width:630px;
  height:21px;
}
#u1102_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1102 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1102_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1103_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1103 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1103_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1104_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1104 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1104_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1105_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1105 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1105_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1106_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1106 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1106_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1107_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1107 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1107_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1108_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1108 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1108_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1108_ann {
  border-width:0px;
  position:absolute;
  left:616px;
  top:0px;
  width:1px;
  height:1px;
}
#u1109 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:165px;
  width:58px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1109_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:40px;
  word-wrap:break-word;
}
#u1109_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1110 {
  border-width:0px;
  position:absolute;
  left:376px;
  top:165px;
  width:85px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1110_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:67px;
  word-wrap:break-word;
}
#u1110_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1110_ann {
  border-width:0px;
  position:absolute;
  left:454px;
  top:161px;
  width:1px;
  height:1px;
}
#u1111 {
  border-width:0px;
  position:absolute;
  left:468px;
  top:165px;
  width:90px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1111_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:72px;
  word-wrap:break-word;
}
#u1111_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1111_ann {
  border-width:0px;
  position:absolute;
  left:551px;
  top:161px;
  width:1px;
  height:1px;
}
#u1112 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:158px;
  width:69px;
  height:30px;
}
#u1112_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1113 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:158px;
  width:69px;
  height:30px;
}
#u1113_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1114 {
  border-width:0px;
  position:absolute;
  left:288px;
  top:158px;
  width:69px;
  height:30px;
}
#u1114_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1114_ann {
  border-width:0px;
  position:absolute;
  left:350px;
  top:154px;
  width:1px;
  height:1px;
}
#u1115 {
  border-width:0px;
  position:absolute;
  left:626px;
  top:165px;
  width:78px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1115_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:60px;
  word-wrap:break-word;
}
#u1115_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1116 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:165px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1116_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:43px;
  word-wrap:break-word;
}
#u1116_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1117 {
  border-width:0px;
  position:absolute;
  left:785px;
  top:165px;
  width:69px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1117_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:51px;
  word-wrap:break-word;
}
#u1117_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1118 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:158px;
  width:69px;
  height:30px;
}
#u1118_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1118_ann {
  border-width:0px;
  position:absolute;
  left:176px;
  top:154px;
  width:1px;
  height:1px;
}
#u1120 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:0px;
  width:926px;
  height:87px;
}
#u1121_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:87px;
}
#u1121 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:87px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1121_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1122 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:5px;
  width:125px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1122_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  white-space:nowrap;
}
#u1122_ann {
  border-width:0px;
  position:absolute;
  left:140px;
  top:1px;
  width:1px;
  height:1px;
}
#u1123 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:28px;
  width:630px;
  height:21px;
}
#u1124_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1124 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1124_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1125_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1125 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1125_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1126_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1126 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1126_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1127_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1127 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1127_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1128_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1128 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1128_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1129_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1129 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1129_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1130_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1130 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1130_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1130_ann {
  border-width:0px;
  position:absolute;
  left:616px;
  top:0px;
  width:1px;
  height:1px;
}
#u1131 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:60px;
  width:58px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1131_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:40px;
  word-wrap:break-word;
}
#u1131_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1132 {
  border-width:0px;
  position:absolute;
  left:376px;
  top:60px;
  width:85px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1132_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:67px;
  word-wrap:break-word;
}
#u1132_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1132_ann {
  border-width:0px;
  position:absolute;
  left:454px;
  top:56px;
  width:1px;
  height:1px;
}
#u1133 {
  border-width:0px;
  position:absolute;
  left:468px;
  top:60px;
  width:90px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1133_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:72px;
  word-wrap:break-word;
}
#u1133_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1133_ann {
  border-width:0px;
  position:absolute;
  left:551px;
  top:56px;
  width:1px;
  height:1px;
}
#u1134 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:53px;
  width:69px;
  height:30px;
}
#u1134_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1135 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:53px;
  width:69px;
  height:30px;
}
#u1135_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1136 {
  border-width:0px;
  position:absolute;
  left:288px;
  top:53px;
  width:69px;
  height:30px;
}
#u1136_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1136_ann {
  border-width:0px;
  position:absolute;
  left:350px;
  top:49px;
  width:1px;
  height:1px;
}
#u1137 {
  border-width:0px;
  position:absolute;
  left:626px;
  top:60px;
  width:78px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1137_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:60px;
  word-wrap:break-word;
}
#u1137_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1138 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:60px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1138_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:43px;
  word-wrap:break-word;
}
#u1138_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1139 {
  border-width:0px;
  position:absolute;
  left:785px;
  top:60px;
  width:69px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1139_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:51px;
  word-wrap:break-word;
}
#u1139_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1140 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:53px;
  width:69px;
  height:30px;
}
#u1140_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1140_ann {
  border-width:0px;
  position:absolute;
  left:176px;
  top:49px;
  width:1px;
  height:1px;
}
#u1141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1141 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:116px;
  width:137px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1141_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  white-space:nowrap;
}
#u1143 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:234px;
  width:82px;
  height:505px;
}
#u1144_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1144 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1144_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:78px;
  word-wrap:break-word;
}
#u1145_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:118px;
}
#u1145 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:82px;
  height:118px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1145_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1146_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1146 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:158px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1146_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:78px;
  word-wrap:break-word;
}
#u1146_ann {
  border-width:0px;
  position:absolute;
  left:68px;
  top:158px;
  width:1px;
  height:1px;
}
#u1147_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:307px;
}
#u1147 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:198px;
  width:82px;
  height:307px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:right;
}
#u1147_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1148 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:272px;
  width:914px;
  height:118px;
}
#u1148_input {
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:118px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1150_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1150 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:529px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1150_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1151 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:431px;
  width:914px;
  height:72px;
}
#u1152_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
}
#u1152 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1152_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:910px;
  word-wrap:break-word;
}
#u1153_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1153 {
  border-width:0px;
  position:absolute;
  left:27px;
  top:467px;
  width:29px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1153_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  white-space:nowrap;
}
#u1154 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:441px;
  width:47px;
  height:24px;
}
#u1155_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1155 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u1155_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1156 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:441px;
  width:47px;
  height:24px;
}
#u1157_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1157 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1157_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1158 {
  border-width:0px;
  position:absolute;
  left:293px;
  top:441px;
  width:47px;
  height:24px;
}
#u1159_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1159 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1159_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1160 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:520px;
  width:914px;
  height:82px;
}
#u1161_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:82px;
}
#u1161 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:82px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1161_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:17px;
  width:910px;
  word-wrap:break-word;
}
#u1162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1162 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:567px;
  width:29px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1162_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  white-space:nowrap;
}
#u1163 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:532px;
  width:104px;
  height:24px;
}
#u1164_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1164 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1164_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1165 {
  border-width:0px;
  position:absolute;
  left:299px;
  top:532px;
  width:104px;
  height:24px;
}
#u1166_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1166 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1166_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1167 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:532px;
  width:104px;
  height:24px;
}
#u1168_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1168 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1168_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1169 {
  border-width:0px;
  position:absolute;
  left:535px;
  top:532px;
  width:104px;
  height:24px;
}
#u1170_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1170 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1170_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1171 {
  border-width:0px;
  position:absolute;
  left:659px;
  top:532px;
  width:104px;
  height:24px;
}
#u1172_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1172 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1172_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1173 {
  border-width:0px;
  position:absolute;
  left:782px;
  top:532px;
  width:104px;
  height:24px;
}
#u1174_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1174 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1174_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1175 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:567px;
  width:149px;
  height:26px;
}
#u1176_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:26px;
}
#u1176 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:26px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1176_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:145px;
  word-wrap:break-word;
}
#u1177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1177 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:701px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1177_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1178 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:619px;
  width:914px;
  height:72px;
}
#u1179_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
}
#u1179 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1179_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:910px;
  word-wrap:break-word;
}
#u1180_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1180 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:667px;
  width:29px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1180_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  white-space:nowrap;
}
#u1181 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:629px;
  width:47px;
  height:24px;
}
#u1182_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1182 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  color:#FF9900;
  text-align:left;
}
#u1182_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1183 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:629px;
  width:47px;
  height:24px;
}
#u1184_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1184 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  color:#FF9900;
  text-align:left;
}
#u1184_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1185 {
  border-width:0px;
  position:absolute;
  left:293px;
  top:629px;
  width:73px;
  height:24px;
}
#u1186_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:24px;
}
#u1186 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1186_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:69px;
  word-wrap:break-word;
}
#u1187_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(0, 0, 255, 0.2);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1187 {
  border-width:0px;
  position:absolute;
  left:914px;
  top:446px;
  width:16px;
  height:16px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1187_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:16px;
  word-wrap:break-word;
}
#u1188 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1189_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:268px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u1189 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:446px;
  width:362px;
  height:268px;
}
#u1189_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1190 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:446px;
  width:362px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1190_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:358px;
  word-wrap:break-word;
}
#u1191_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1191 {
  border-width:0px;
  position:absolute;
  left:438px;
  top:453px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1191_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1192 {
  border-width:0px;
  position:absolute;
  left:473px;
  top:453px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1192_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1193 {
  border-width:0px;
  position:absolute;
  left:374px;
  top:485px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1193_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1193_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1194_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:112px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1194 {
  border-width:0px;
  position:absolute;
  left:174px;
  top:571px;
  width:338px;
  height:112px;
}
#u1194_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1195 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:578px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1195_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1195_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1196 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:605px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1196_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1196_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1197 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:632px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1197_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1197_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1198 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:659px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1198_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1198_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1199_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:30px;
  height:10px;
}
#u1199 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:588px;
  width:25px;
  height:5px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1199_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1200 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:512px;
  width:75px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1200_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:57px;
  word-wrap:break-word;
}
#u1200_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1201_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1201 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:537px;
  width:157px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1201_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  white-space:nowrap;
}
#u1202_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1202 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:485px;
  width:205px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1202_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  white-space:nowrap;
}
#u1203_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1203 {
  border-width:0px;
  position:absolute;
  left:168px;
  top:510px;
  width:157px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1203_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  white-space:nowrap;
}
#u1204 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1205_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u1205 {
  border-width:0px;
  position:absolute;
  left:163px;
  top:494px;
  width:362px;
  height:237px;
}
#u1205_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1206_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1206 {
  border-width:0px;
  position:absolute;
  left:163px;
  top:494px;
  width:362px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1206_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:358px;
  word-wrap:break-word;
}
#u1207_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1207 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:501px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1207_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1208_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1208 {
  border-width:0px;
  position:absolute;
  left:478px;
  top:501px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1208_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1209 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:533px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1209_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1209_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1210 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:693px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1210_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1210_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1211 {
  border-width:0px;
  position:absolute;
  left:189px;
  top:585px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1211_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1211_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1212 {
  border-width:0px;
  position:absolute;
  left:189px;
  top:612px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1212_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1212_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1213 {
  border-width:0px;
  position:absolute;
  left:189px;
  top:639px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1213_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1213_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1214 {
  border-width:0px;
  position:absolute;
  left:189px;
  top:666px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1214_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1214_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1215_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:30px;
  height:10px;
}
#u1215 {
  border-width:0px;
  position:absolute;
  left:495px;
  top:550px;
  width:25px;
  height:5px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1215_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1216 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:558px;
  width:75px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1216_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:57px;
  word-wrap:break-word;
}
#u1216_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1217_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1217 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:36px;
  width:49px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1217_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  white-space:nowrap;
}
#u1218_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1218 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:200px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1218_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1219_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1219 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:150px;
  width:49px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1219_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  white-space:nowrap;
}
#u1220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(0, 0, 255, 0.2);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1220 {
  border-width:0px;
  position:absolute;
  left:919px;
  top:36px;
  width:16px;
  height:16px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1220_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:16px;
  word-wrap:break-word;
}
#u1049_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:936px;
  height:453px;
  visibility:hidden;
  background-image:none;
}
#u1049_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1222 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:0px;
  width:926px;
  height:166px;
}
#u1223_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:166px;
}
#u1223 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:166px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1223_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1224 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:9px;
  width:141px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1224_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  white-space:nowrap;
}
#u1224_ann {
  border-width:0px;
  position:absolute;
  left:146px;
  top:5px;
  width:1px;
  height:1px;
}
#u1225 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:32px;
  width:630px;
  height:121px;
}
#u1226_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1226 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1226_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1227_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1227 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1227_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1228_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1228 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1228_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1229_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1229 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1229_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1230_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1230 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1230_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1231_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:21px;
}
#u1231 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:0px;
  width:93px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1231_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:89px;
  word-wrap:break-word;
}
#u1231_ann {
  border-width:0px;
  position:absolute;
  left:529px;
  top:0px;
  width:1px;
  height:1px;
}
#u1232_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:21px;
}
#u1232 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:0px;
  width:87px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1232_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1233_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1233 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1233_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1234_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1234 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1234_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1235_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1235 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1235_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1236_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1236 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1236_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1237_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1237 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1237_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1238_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:40px;
}
#u1238 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:21px;
  width:93px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1238_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1239_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:40px;
}
#u1239 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:21px;
  width:87px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1239_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1240_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1240 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1240_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1240_ann {
  border-width:0px;
  position:absolute;
  left:76px;
  top:61px;
  width:1px;
  height:1px;
}
#u1241_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1241 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1241_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1241_ann {
  border-width:0px;
  position:absolute;
  left:166px;
  top:61px;
  width:1px;
  height:1px;
}
#u1242_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1242 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1242_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1243_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1243 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1243_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1244_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1244 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1244_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1245_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:21px;
}
#u1245 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:61px;
  width:93px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1245_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:89px;
  word-wrap:break-word;
}
#u1246_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:21px;
}
#u1246 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:61px;
  width:87px;
  height:21px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1246_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1247_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1247 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1247_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1248_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1248 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1248_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1249_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1249 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1249_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1250_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1250 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1250_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1251_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1251 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1251_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1252_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:39px;
}
#u1252 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:82px;
  width:93px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1252_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1253_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:39px;
}
#u1253 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:82px;
  width:87px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1253_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1254 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:62px;
  width:90px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1254_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:72px;
  word-wrap:break-word;
}
#u1254_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1255 {
  border-width:0px;
  position:absolute;
  left:290px;
  top:62px;
  width:85px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1255_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:67px;
  word-wrap:break-word;
}
#u1255_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1255_ann {
  border-width:0px;
  position:absolute;
  left:368px;
  top:58px;
  width:1px;
  height:1px;
}
#u1256 {
  border-width:0px;
  position:absolute;
  left:388px;
  top:62px;
  width:77px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1256_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:59px;
  word-wrap:break-word;
}
#u1256_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1256_ann {
  border-width:0px;
  position:absolute;
  left:458px;
  top:58px;
  width:1px;
  height:1px;
}
#u1257 {
  border-width:0px;
  position:absolute;
  left:121px;
  top:55px;
  width:69px;
  height:30px;
}
#u1257_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1258 {
  border-width:0px;
  position:absolute;
  left:468px;
  top:118px;
  width:78px;
  height:30px;
}
#u1258_input {
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1259 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:118px;
  width:69px;
  height:30px;
}
#u1259_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1260 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:55px;
  width:69px;
  height:30px;
}
#u1260_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1260_ann {
  border-width:0px;
  position:absolute;
  left:264px;
  top:51px;
  width:1px;
  height:1px;
}
#u1261 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:62px;
  width:78px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1261_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:60px;
  word-wrap:break-word;
}
#u1261_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1262 {
  border-width:0px;
  position:absolute;
  left:666px;
  top:62px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1262_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:43px;
  word-wrap:break-word;
}
#u1262_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1263 {
  border-width:0px;
  position:absolute;
  left:737px;
  top:62px;
  width:69px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1263_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:51px;
  word-wrap:break-word;
}
#u1263_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1264 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:55px;
  width:69px;
  height:30px;
}
#u1264_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1264_ann {
  border-width:0px;
  position:absolute;
  left:90px;
  top:51px;
  width:1px;
  height:1px;
}
#u1265 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:118px;
  width:59px;
  height:30px;
}
#u1265_input {
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1266 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:118px;
  width:55px;
  height:30px;
}
#u1266_input {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1267 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:118px;
  width:78px;
  height:30px;
}
#u1267_input {
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1268 {
  border-width:0px;
  position:absolute;
  left:294px;
  top:118px;
  width:55px;
  height:30px;
}
#u1268_input {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1269_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:right;
}
#u1269 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:36px;
  width:47px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:right;
}
#u1269_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  word-wrap:break-word;
}
#u1270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1270 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:188px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1270_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1272 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:229px;
  width:82px;
  height:198px;
}
#u1273_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1273 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1273_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1274_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:118px;
}
#u1274 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:82px;
  height:118px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1274_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1275_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1275 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:158px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1275_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:78px;
  word-wrap:break-word;
}
#u1275_ann {
  border-width:0px;
  position:absolute;
  left:68px;
  top:158px;
  width:1px;
  height:1px;
}
#u1276 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:267px;
  width:914px;
  height:118px;
}
#u1276_input {
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:118px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1277 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:423px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1277_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:27px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1278 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:240px;
  width:67px;
  height:27px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1278_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5px;
  width:67px;
  word-wrap:break-word;
}
#u1279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:27px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1279 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:240px;
  width:65px;
  height:27px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1279_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5px;
  width:65px;
  word-wrap:break-word;
}
#u1280_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:118px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#999999;
}
#u1280 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:267px;
  width:914px;
  height:118px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#999999;
}
#u1280_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:50px;
  width:910px;
  word-wrap:break-word;
}
#u1049_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:946px;
  height:893px;
  visibility:hidden;
  background-image:none;
}
#u1049_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1282 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:180px;
  width:926px;
  height:166px;
}
#u1283_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:166px;
}
#u1283 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:166px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1283_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1284 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:212px;
  width:630px;
  height:121px;
}
#u1285_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1285 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1285_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1286_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1286 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1286_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1287_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1287 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1287_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1288_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1288 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1288_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1289_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1289 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1289_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1290_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1290 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1290_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1291_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1291 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1291_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1291_ann {
  border-width:0px;
  position:absolute;
  left:616px;
  top:0px;
  width:1px;
  height:1px;
}
#u1292_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1292 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1292_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1293_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1293 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1293_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1294_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1294 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1294_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1295_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1295 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1295_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1296_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1296 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1296_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1297_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1297 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1297_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1298_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1298 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1298_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1299_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1299 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1299_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1299_ann {
  border-width:0px;
  position:absolute;
  left:76px;
  top:61px;
  width:1px;
  height:1px;
}
#u1300_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1300 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1300_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1300_ann {
  border-width:0px;
  position:absolute;
  left:166px;
  top:61px;
  width:1px;
  height:1px;
}
#u1301_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1301 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1301_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1302_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1302 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1302_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1303_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1303 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1303_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1304_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1304 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1304_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1305_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1305 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1305_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1306_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1306 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1306_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1307_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1307 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1307_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1308_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1308 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1308_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1309_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1309 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1309_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1310_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1310 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1310_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1311_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1311 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1311_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1312_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1312 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1312_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1313_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1313 {
  border-width:0px;
  position:absolute;
  left:27px;
  top:189px;
  width:126px;
  height:17px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1313_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  white-space:nowrap;
}
#u1313_ann {
  border-width:0px;
  position:absolute;
  left:146px;
  top:185px;
  width:1px;
  height:1px;
}
#u1314 {
  border-width:0px;
  position:absolute;
  left:577px;
  top:244px;
  width:58px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1314_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:40px;
  word-wrap:break-word;
}
#u1314_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1315 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:237px;
  width:69px;
  height:30px;
}
#u1315_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1315_ann {
  border-width:0px;
  position:absolute;
  left:275px;
  top:233px;
  width:1px;
  height:1px;
}
#u1316 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:237px;
  width:69px;
  height:30px;
}
#u1316_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1316_ann {
  border-width:0px;
  position:absolute;
  left:90px;
  top:233px;
  width:1px;
  height:1px;
}
#u1317 {
  border-width:0px;
  position:absolute;
  left:468px;
  top:298px;
  width:78px;
  height:30px;
}
#u1317_input {
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1318 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:298px;
  width:69px;
  height:30px;
}
#u1318_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1319 {
  border-width:0px;
  position:absolute;
  left:294px;
  top:237px;
  width:69px;
  height:30px;
}
#u1319_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1320 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:244px;
  width:78px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1320_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:60px;
  word-wrap:break-word;
}
#u1320_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1321 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:244px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1321_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:43px;
  word-wrap:break-word;
}
#u1321_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1322 {
  border-width:0px;
  position:absolute;
  left:794px;
  top:244px;
  width:69px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1322_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:51px;
  word-wrap:break-word;
}
#u1322_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1323 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:237px;
  width:69px;
  height:30px;
}
#u1323_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1323_ann {
  border-width:0px;
  position:absolute;
  left:182px;
  top:233px;
  width:1px;
  height:1px;
}
#u1324 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:298px;
  width:59px;
  height:30px;
}
#u1324_input {
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1325 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:298px;
  width:55px;
  height:30px;
}
#u1325_input {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1326 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:298px;
  width:78px;
  height:30px;
}
#u1326_input {
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1327 {
  border-width:0px;
  position:absolute;
  left:294px;
  top:298px;
  width:55px;
  height:30px;
}
#u1327_input {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1328 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:244px;
  width:85px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1328_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:67px;
  word-wrap:break-word;
}
#u1328_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1328_ann {
  border-width:0px;
  position:absolute;
  left:471px;
  top:240px;
  width:1px;
  height:1px;
}
#u1329 {
  border-width:0px;
  position:absolute;
  left:491px;
  top:244px;
  width:77px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1329_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:59px;
  word-wrap:break-word;
}
#u1329_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1329_ann {
  border-width:0px;
  position:absolute;
  left:561px;
  top:240px;
  width:1px;
  height:1px;
}
#u1331 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:0px;
  width:926px;
  height:166px;
}
#u1332_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:166px;
}
#u1332 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:166px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1332_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1333 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:32px;
  width:630px;
  height:121px;
}
#u1334_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1334 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1334_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1335_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1335 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1335_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1336_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1336 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1336_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1337_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1337 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1337_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1338_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1338 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1338_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1339_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1339 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1339_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1340_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1340 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1340_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1340_ann {
  border-width:0px;
  position:absolute;
  left:616px;
  top:0px;
  width:1px;
  height:1px;
}
#u1341_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1341 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1341_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1342_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1342 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1342_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1343_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1343 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1343_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1344_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1344 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1344_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1345_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1345 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1345_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1346_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1346 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1346_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1347_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1347 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1347_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1348_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1348 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1348_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1348_ann {
  border-width:0px;
  position:absolute;
  left:76px;
  top:61px;
  width:1px;
  height:1px;
}
#u1349_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1349 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1349_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1349_ann {
  border-width:0px;
  position:absolute;
  left:166px;
  top:61px;
  width:1px;
  height:1px;
}
#u1350_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1350 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1350_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1351_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1351 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1351_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1352_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1352 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1352_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1353_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1353 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1353_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1354_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1354 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1354_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1355_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1355 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1355_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1356_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1356 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1356_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1357_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1357 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1357_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1358_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1358 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1358_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1359_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1359 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1359_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1360_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1360 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1360_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1361_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1361 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1361_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1362_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1362 {
  border-width:0px;
  position:absolute;
  left:27px;
  top:9px;
  width:126px;
  height:17px;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1362_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  white-space:nowrap;
}
#u1362_ann {
  border-width:0px;
  position:absolute;
  left:146px;
  top:5px;
  width:1px;
  height:1px;
}
#u1363 {
  border-width:0px;
  position:absolute;
  left:577px;
  top:64px;
  width:58px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1363_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:40px;
  word-wrap:break-word;
}
#u1363_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1364 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:57px;
  width:69px;
  height:30px;
}
#u1364_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1364_ann {
  border-width:0px;
  position:absolute;
  left:275px;
  top:53px;
  width:1px;
  height:1px;
}
#u1365 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:57px;
  width:69px;
  height:30px;
}
#u1365_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1365_ann {
  border-width:0px;
  position:absolute;
  left:90px;
  top:53px;
  width:1px;
  height:1px;
}
#u1366 {
  border-width:0px;
  position:absolute;
  left:468px;
  top:118px;
  width:78px;
  height:30px;
}
#u1366_input {
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1367 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:118px;
  width:69px;
  height:30px;
}
#u1367_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1368 {
  border-width:0px;
  position:absolute;
  left:294px;
  top:57px;
  width:69px;
  height:30px;
}
#u1368_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1369 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:64px;
  width:78px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1369_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:60px;
  word-wrap:break-word;
}
#u1369_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1370 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:64px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1370_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:43px;
  word-wrap:break-word;
}
#u1370_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1371 {
  border-width:0px;
  position:absolute;
  left:794px;
  top:64px;
  width:69px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1371_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:51px;
  word-wrap:break-word;
}
#u1371_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1372 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:57px;
  width:69px;
  height:30px;
}
#u1372_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1372_ann {
  border-width:0px;
  position:absolute;
  left:182px;
  top:53px;
  width:1px;
  height:1px;
}
#u1373 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:118px;
  width:59px;
  height:30px;
}
#u1373_input {
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1374 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:118px;
  width:55px;
  height:30px;
}
#u1374_input {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1375 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:118px;
  width:78px;
  height:30px;
}
#u1375_input {
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1376 {
  border-width:0px;
  position:absolute;
  left:294px;
  top:118px;
  width:55px;
  height:30px;
}
#u1376_input {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1377 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:64px;
  width:85px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1377_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:67px;
  word-wrap:break-word;
}
#u1377_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1377_ann {
  border-width:0px;
  position:absolute;
  left:471px;
  top:60px;
  width:1px;
  height:1px;
}
#u1378 {
  border-width:0px;
  position:absolute;
  left:491px;
  top:64px;
  width:77px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1378_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:59px;
  word-wrap:break-word;
}
#u1378_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1378_ann {
  border-width:0px;
  position:absolute;
  left:561px;
  top:60px;
  width:1px;
  height:1px;
}
#u1379_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:right;
}
#u1379 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:36px;
  width:47px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:right;
}
#u1379_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  word-wrap:break-word;
}
#u1380_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1380 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:350px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1380_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1381_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1381 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:189px;
  width:123px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1381_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  white-space:nowrap;
}
#u1382_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:right;
}
#u1382 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:222px;
  width:47px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:right;
}
#u1382_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  word-wrap:break-word;
}
#u1383_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(0, 0, 255, 0.2);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1383 {
  border-width:0px;
  position:absolute;
  left:917px;
  top:37px;
  width:16px;
  height:16px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1383_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:16px;
  word-wrap:break-word;
}
#u1385 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:388px;
  width:82px;
  height:505px;
}
#u1386_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1386 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1386_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:78px;
  word-wrap:break-word;
}
#u1387_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:118px;
}
#u1387 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:82px;
  height:118px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1387_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1388_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1388 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:158px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1388_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:78px;
  word-wrap:break-word;
}
#u1388_ann {
  border-width:0px;
  position:absolute;
  left:68px;
  top:158px;
  width:1px;
  height:1px;
}
#u1389_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:307px;
}
#u1389 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:198px;
  width:82px;
  height:307px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:right;
}
#u1389_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1390 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:426px;
  width:914px;
  height:118px;
}
#u1390_input {
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:118px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1392_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1392 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:683px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1392_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1393 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:585px;
  width:914px;
  height:72px;
}
#u1394_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
}
#u1394 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1394_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:910px;
  word-wrap:break-word;
}
#u1395_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1395 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:621px;
  width:29px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1395_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  white-space:nowrap;
}
#u1396 {
  border-width:0px;
  position:absolute;
  left:190px;
  top:595px;
  width:47px;
  height:24px;
}
#u1397_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1397 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u1397_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1398 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:595px;
  width:47px;
  height:24px;
}
#u1399_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1399 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1399_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1400 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:595px;
  width:47px;
  height:24px;
}
#u1401_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1401 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1401_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1402 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:674px;
  width:914px;
  height:82px;
}
#u1403_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:82px;
}
#u1403 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:82px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1403_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:17px;
  width:910px;
  word-wrap:break-word;
}
#u1404_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1404 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:721px;
  width:29px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1404_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  white-space:nowrap;
}
#u1405 {
  border-width:0px;
  position:absolute;
  left:186px;
  top:686px;
  width:104px;
  height:24px;
}
#u1406_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1406 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1406_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1407 {
  border-width:0px;
  position:absolute;
  left:309px;
  top:686px;
  width:104px;
  height:24px;
}
#u1408_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1408 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1408_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1409 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:686px;
  width:104px;
  height:24px;
}
#u1410_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1410 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1410_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1411 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:686px;
  width:104px;
  height:24px;
}
#u1412_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1412 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1412_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1413 {
  border-width:0px;
  position:absolute;
  left:669px;
  top:686px;
  width:104px;
  height:24px;
}
#u1414_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1414 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1414_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1415 {
  border-width:0px;
  position:absolute;
  left:792px;
  top:686px;
  width:104px;
  height:24px;
}
#u1416_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1416 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1416_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1417 {
  border-width:0px;
  position:absolute;
  left:186px;
  top:721px;
  width:149px;
  height:26px;
}
#u1418_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:26px;
}
#u1418 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:26px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1418_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:145px;
  word-wrap:break-word;
}
#u1419_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1419 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:855px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1419_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1420 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:773px;
  width:914px;
  height:72px;
}
#u1421_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
}
#u1421 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1421_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:910px;
  word-wrap:break-word;
}
#u1422_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1422 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:821px;
  width:29px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1422_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  white-space:nowrap;
}
#u1423 {
  border-width:0px;
  position:absolute;
  left:190px;
  top:783px;
  width:47px;
  height:24px;
}
#u1424_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1424 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  color:#FF9900;
  text-align:left;
}
#u1424_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1425 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:783px;
  width:47px;
  height:24px;
}
#u1426_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1426 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  color:#FF9900;
  text-align:left;
}
#u1426_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1427 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:783px;
  width:73px;
  height:24px;
}
#u1428_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:24px;
}
#u1428 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1428_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:69px;
  word-wrap:break-word;
}
#u1429_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(0, 0, 255, 0.2);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1429 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:600px;
  width:16px;
  height:16px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1429_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:16px;
  word-wrap:break-word;
}
#u1430 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1431_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:268px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u1431 {
  border-width:0px;
  position:absolute;
  left:168px;
  top:600px;
  width:362px;
  height:268px;
}
#u1431_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1432_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1432 {
  border-width:0px;
  position:absolute;
  left:168px;
  top:600px;
  width:362px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1432_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:358px;
  word-wrap:break-word;
}
#u1433_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1433 {
  border-width:0px;
  position:absolute;
  left:448px;
  top:607px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1433_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1434_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1434 {
  border-width:0px;
  position:absolute;
  left:483px;
  top:607px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1434_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1435 {
  border-width:0px;
  position:absolute;
  left:384px;
  top:639px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1435_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1435_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1436_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:112px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1436 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:725px;
  width:338px;
  height:112px;
}
#u1436_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1437 {
  border-width:0px;
  position:absolute;
  left:193px;
  top:732px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1437_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1437_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1438 {
  border-width:0px;
  position:absolute;
  left:193px;
  top:759px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1438_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1438_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1439 {
  border-width:0px;
  position:absolute;
  left:193px;
  top:786px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1439_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1439_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1440 {
  border-width:0px;
  position:absolute;
  left:193px;
  top:813px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1440_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1440_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1441_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:30px;
  height:10px;
}
#u1441 {
  border-width:0px;
  position:absolute;
  left:500px;
  top:742px;
  width:25px;
  height:5px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1441_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1442 {
  border-width:0px;
  position:absolute;
  left:354px;
  top:666px;
  width:75px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1442_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:57px;
  word-wrap:break-word;
}
#u1442_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1443_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1443 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:691px;
  width:157px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1443_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  white-space:nowrap;
}
#u1444_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1444 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:639px;
  width:205px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1444_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  white-space:nowrap;
}
#u1445_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1445 {
  border-width:0px;
  position:absolute;
  left:178px;
  top:664px;
  width:157px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1445_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  white-space:nowrap;
}
#u1446 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1447_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u1447 {
  border-width:0px;
  position:absolute;
  left:173px;
  top:648px;
  width:362px;
  height:237px;
}
#u1447_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1448_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1448 {
  border-width:0px;
  position:absolute;
  left:173px;
  top:648px;
  width:362px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1448_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:358px;
  word-wrap:break-word;
}
#u1449_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1449 {
  border-width:0px;
  position:absolute;
  left:453px;
  top:655px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1449_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1450 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:655px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1450_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1451 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:687px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1451_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1451_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1452 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:847px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1452_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1452_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1453 {
  border-width:0px;
  position:absolute;
  left:199px;
  top:739px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1453_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1453_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1454 {
  border-width:0px;
  position:absolute;
  left:199px;
  top:766px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1454_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1454_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1455 {
  border-width:0px;
  position:absolute;
  left:199px;
  top:793px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1455_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1455_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1456 {
  border-width:0px;
  position:absolute;
  left:199px;
  top:820px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1456_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1456_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1457_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:30px;
  height:10px;
}
#u1457 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:704px;
  width:25px;
  height:5px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1457_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1458 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:712px;
  width:75px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1458_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:57px;
  word-wrap:break-word;
}
#u1458_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1049_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:936px;
  height:311px;
  visibility:hidden;
  background-image:none;
}
#u1049_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1460 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:914px;
  height:87px;
}
#u1461_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:87px;
}
#u1461 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:87px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1461_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1462_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1462 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:4px;
  width:125px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1462_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  white-space:nowrap;
}
#u1462_ann {
  border-width:0px;
  position:absolute;
  left:146px;
  top:0px;
  width:1px;
  height:1px;
}
#u1463 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:26px;
  width:630px;
  height:21px;
}
#u1464_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1464 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1464_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1465_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1465 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1465_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1466_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1466 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1466_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1467_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1467 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1467_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1468_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1468 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1468_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1469_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1469 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1469_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1469_ann {
  border-width:0px;
  position:absolute;
  left:526px;
  top:0px;
  width:1px;
  height:1px;
}
#u1470_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1470 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1470_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1471 {
  border-width:0px;
  position:absolute;
  left:491px;
  top:54px;
  width:58px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1471_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:40px;
  word-wrap:break-word;
}
#u1471_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1472 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:47px;
  width:69px;
  height:30px;
}
#u1472_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#999999;
  text-align:left;
}
#u1473 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:47px;
  width:69px;
  height:30px;
}
#u1473_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1474 {
  border-width:0px;
  position:absolute;
  left:549px;
  top:54px;
  width:78px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1474_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:60px;
  word-wrap:break-word;
}
#u1474_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1475 {
  border-width:0px;
  position:absolute;
  left:637px;
  top:54px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1475_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:43px;
  word-wrap:break-word;
}
#u1475_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1476 {
  border-width:0px;
  position:absolute;
  left:708px;
  top:54px;
  width:69px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1476_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:51px;
  word-wrap:break-word;
}
#u1476_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1477 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:47px;
  width:69px;
  height:30px;
}
#u1477_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1477_ann {
  border-width:0px;
  position:absolute;
  left:101px;
  top:43px;
  width:1px;
  height:1px;
}
#u1478 {
  border-width:0px;
  position:absolute;
  left:306px;
  top:54px;
  width:85px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1478_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:67px;
  word-wrap:break-word;
}
#u1478_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1478_ann {
  border-width:0px;
  position:absolute;
  left:384px;
  top:50px;
  width:1px;
  height:1px;
}
#u1479 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:54px;
  width:90px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1479_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:72px;
  word-wrap:break-word;
}
#u1479_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1479_ann {
  border-width:0px;
  position:absolute;
  left:481px;
  top:50px;
  width:1px;
  height:1px;
}
#u1480_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1480 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:36px;
  width:49px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1480_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  white-space:nowrap;
}
#u1482 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:87px;
  width:82px;
  height:198px;
}
#u1483_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1483 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1483_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1484_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:118px;
}
#u1484 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:82px;
  height:118px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1484_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1485_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1485 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:158px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1485_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:78px;
  word-wrap:break-word;
}
#u1485_ann {
  border-width:0px;
  position:absolute;
  left:68px;
  top:158px;
  width:1px;
  height:1px;
}
#u1486 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:125px;
  width:914px;
  height:118px;
}
#u1486_input {
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:118px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1487_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1487 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:281px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1487_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1488_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:27px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1488 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:98px;
  width:67px;
  height:27px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1488_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5px;
  width:67px;
  word-wrap:break-word;
}
#u1489_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:27px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1489 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:98px;
  width:65px;
  height:27px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1489_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5px;
  width:65px;
  word-wrap:break-word;
}
#u1490_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:118px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#999999;
}
#u1490 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:125px;
  width:914px;
  height:118px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#999999;
}
#u1490_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:50px;
  width:910px;
  word-wrap:break-word;
}
#u1049_state5 {
  position:relative;
  left:0px;
  top:0px;
  width:930px;
  height:671px;
  visibility:hidden;
  background-image:none;
}
#u1049_state5_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1492 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:0px;
  width:926px;
  height:165px;
}
#u1493_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:165px;
}
#u1493 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:926px;
  height:165px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1493_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1494_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1494 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:9px;
  width:125px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1494_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  white-space:nowrap;
}
#u1494_ann {
  border-width:0px;
  position:absolute;
  left:140px;
  top:5px;
  width:1px;
  height:1px;
}
#u1495 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:32px;
  width:630px;
  height:121px;
}
#u1496_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1496 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1496_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1497_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1497 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1497_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1498_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1498 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1498_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1499_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1499 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1499_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1500_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1500 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:0px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1500_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1501_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:21px;
}
#u1501 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:0px;
  width:93px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1501_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:89px;
  word-wrap:break-word;
}
#u1501_ann {
  border-width:0px;
  position:absolute;
  left:529px;
  top:0px;
  width:1px;
  height:1px;
}
#u1502_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:21px;
}
#u1502 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:0px;
  width:87px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1502_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1503_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1503 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1503_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1504_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1504 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1504_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1505_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1505 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1505_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1506_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1506 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1506_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1507_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:40px;
}
#u1507 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:21px;
  width:90px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1507_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1508_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:40px;
}
#u1508 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:21px;
  width:93px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1508_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1509_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:40px;
}
#u1509 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:21px;
  width:87px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1509_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1510_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1510 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1510_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1510_ann {
  border-width:0px;
  position:absolute;
  left:76px;
  top:61px;
  width:1px;
  height:1px;
}
#u1511_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1511 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1511_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1511_ann {
  border-width:0px;
  position:absolute;
  left:166px;
  top:61px;
  width:1px;
  height:1px;
}
#u1512_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1512 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1512_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1513_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1513 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1513_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1514_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
}
#u1514 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:61px;
  width:90px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1514_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:86px;
  word-wrap:break-word;
}
#u1515_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:21px;
}
#u1515 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:61px;
  width:93px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1515_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:89px;
  word-wrap:break-word;
}
#u1516_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:21px;
}
#u1516 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:61px;
  width:87px;
  height:21px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1516_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1517_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1517 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1517_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1518_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1518 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1518_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1519_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1519 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1519_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1520_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1520 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1520_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1521_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:39px;
}
#u1521 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:82px;
  width:90px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1521_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1522_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:39px;
}
#u1522 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:82px;
  width:93px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1522_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1523_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:39px;
}
#u1523 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:82px;
  width:87px;
  height:39px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1523_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1524 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:62px;
  width:58px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1524_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:40px;
  word-wrap:break-word;
}
#u1524_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1525 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:55px;
  width:63px;
  height:30px;
}
#u1525_input {
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#999999;
  text-align:left;
}
#u1526 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:118px;
  width:72px;
  height:30px;
}
#u1526_input {
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1527 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:118px;
  width:69px;
  height:30px;
}
#u1527_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1528 {
  border-width:0px;
  position:absolute;
  left:196px;
  top:55px;
  width:69px;
  height:30px;
}
#u1528_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1529 {
  border-width:0px;
  position:absolute;
  left:535px;
  top:62px;
  width:78px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1529_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:60px;
  word-wrap:break-word;
}
#u1529_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1530 {
  border-width:0px;
  position:absolute;
  left:623px;
  top:62px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1530_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:43px;
  word-wrap:break-word;
}
#u1530_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1531 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:62px;
  width:69px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1531_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:51px;
  word-wrap:break-word;
}
#u1531_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1532 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:55px;
  width:69px;
  height:30px;
}
#u1532_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:30px;
  background-color:transparent;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1532_ann {
  border-width:0px;
  position:absolute;
  left:84px;
  top:51px;
  width:1px;
  height:1px;
}
#u1533 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:118px;
  width:63px;
  height:30px;
}
#u1533_input {
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1534 {
  border-width:0px;
  position:absolute;
  left:196px;
  top:118px;
  width:66px;
  height:30px;
}
#u1534_input {
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1535 {
  border-width:0px;
  position:absolute;
  left:385px;
  top:118px;
  width:71px;
  height:30px;
}
#u1535_input {
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1536 {
  border-width:0px;
  position:absolute;
  left:288px;
  top:118px;
  width:55px;
  height:30px;
}
#u1536_input {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1537 {
  border-width:0px;
  position:absolute;
  left:295px;
  top:62px;
  width:85px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1537_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:67px;
  word-wrap:break-word;
}
#u1537_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1537_ann {
  border-width:0px;
  position:absolute;
  left:373px;
  top:58px;
  width:1px;
  height:1px;
}
#u1538 {
  border-width:0px;
  position:absolute;
  left:387px;
  top:62px;
  width:90px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1538_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:72px;
  word-wrap:break-word;
}
#u1538_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1538_ann {
  border-width:0px;
  position:absolute;
  left:470px;
  top:58px;
  width:1px;
  height:1px;
}
#u1539_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:right;
}
#u1539 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:36px;
  width:47px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:right;
}
#u1539_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  word-wrap:break-word;
}
#u1541 {
  border-width:0px;
  position:absolute;
  left:-6px;
  top:166px;
  width:82px;
  height:505px;
}
#u1542_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1542 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1542_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:78px;
  word-wrap:break-word;
}
#u1543_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:118px;
}
#u1543 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:82px;
  height:118px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1543_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1544_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:40px;
}
#u1544 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:158px;
  width:82px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1544_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:78px;
  word-wrap:break-word;
}
#u1544_ann {
  border-width:0px;
  position:absolute;
  left:68px;
  top:158px;
  width:1px;
  height:1px;
}
#u1545_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:307px;
}
#u1545 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:198px;
  width:82px;
  height:307px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:right;
}
#u1545_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1546 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:204px;
  width:914px;
  height:118px;
}
#u1546_input {
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:118px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1548_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1548 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:461px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1548_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1549 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:363px;
  width:914px;
  height:72px;
}
#u1550_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
}
#u1550 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1550_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:910px;
  word-wrap:break-word;
}
#u1551_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1551 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:399px;
  width:29px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1551_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  white-space:nowrap;
}
#u1552 {
  border-width:0px;
  position:absolute;
  left:174px;
  top:373px;
  width:47px;
  height:24px;
}
#u1553_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1553 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u1553_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1554 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:373px;
  width:47px;
  height:24px;
}
#u1555_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1555 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1555_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1556 {
  border-width:0px;
  position:absolute;
  left:287px;
  top:373px;
  width:47px;
  height:24px;
}
#u1557_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1557 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1557_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1558 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:452px;
  width:914px;
  height:82px;
}
#u1559_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:82px;
}
#u1559 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:82px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1559_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:17px;
  width:910px;
  word-wrap:break-word;
}
#u1560_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1560 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:499px;
  width:29px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1560_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  white-space:nowrap;
}
#u1561 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:464px;
  width:104px;
  height:24px;
}
#u1562_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1562 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1562_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1563 {
  border-width:0px;
  position:absolute;
  left:293px;
  top:464px;
  width:104px;
  height:24px;
}
#u1564_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1564 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1564_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1565 {
  border-width:0px;
  position:absolute;
  left:410px;
  top:464px;
  width:104px;
  height:24px;
}
#u1566_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1566 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1566_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1567 {
  border-width:0px;
  position:absolute;
  left:529px;
  top:464px;
  width:104px;
  height:24px;
}
#u1568_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1568 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1568_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1569 {
  border-width:0px;
  position:absolute;
  left:653px;
  top:464px;
  width:104px;
  height:24px;
}
#u1570_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1570 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1570_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1571 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:464px;
  width:104px;
  height:24px;
}
#u1572_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
}
#u1572 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1572_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:100px;
  word-wrap:break-word;
}
#u1573 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:499px;
  width:149px;
  height:26px;
}
#u1574_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:26px;
}
#u1574 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:26px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1574_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:145px;
  word-wrap:break-word;
}
#u1575_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1575 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:633px;
  width:68px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
}
#u1575_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:64px;
  word-wrap:break-word;
}
#u1576 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:551px;
  width:914px;
  height:72px;
}
#u1577_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
}
#u1577 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:914px;
  height:72px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1577_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:910px;
  word-wrap:break-word;
}
#u1578_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1578 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:599px;
  width:29px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1578_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  white-space:nowrap;
}
#u1579 {
  border-width:0px;
  position:absolute;
  left:174px;
  top:561px;
  width:47px;
  height:24px;
}
#u1580_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1580 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  color:#FF9900;
  text-align:left;
}
#u1580_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1581 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:561px;
  width:47px;
  height:24px;
}
#u1582_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
}
#u1582 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:24px;
  color:#FF9900;
  text-align:left;
}
#u1582_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:43px;
  word-wrap:break-word;
}
#u1583 {
  border-width:0px;
  position:absolute;
  left:287px;
  top:561px;
  width:73px;
  height:24px;
}
#u1584_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:24px;
}
#u1584 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:24px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1584_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:69px;
  word-wrap:break-word;
}
#u1585_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(0, 0, 255, 0.2);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1585 {
  border-width:0px;
  position:absolute;
  left:908px;
  top:378px;
  width:16px;
  height:16px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1585_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:16px;
  word-wrap:break-word;
}
#u1586 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1587_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:268px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u1587 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:378px;
  width:362px;
  height:268px;
}
#u1587_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1588_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1588 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:378px;
  width:362px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1588_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:358px;
  word-wrap:break-word;
}
#u1589_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1589 {
  border-width:0px;
  position:absolute;
  left:432px;
  top:385px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1589_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1590_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1590 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:385px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1590_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1591 {
  border-width:0px;
  position:absolute;
  left:368px;
  top:417px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1591_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1591_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1592_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:112px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1592 {
  border-width:0px;
  position:absolute;
  left:168px;
  top:503px;
  width:338px;
  height:112px;
}
#u1592_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1593 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:510px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1593_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1593_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1594 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:537px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1594_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1594_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1595 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:564px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1595_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1595_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1596 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:591px;
  width:168px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1596_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:150px;
  word-wrap:break-word;
}
#u1596_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1597_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:30px;
  height:10px;
}
#u1597 {
  border-width:0px;
  position:absolute;
  left:484px;
  top:520px;
  width:25px;
  height:5px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1597_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1598 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:444px;
  width:75px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1598_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:57px;
  word-wrap:break-word;
}
#u1598_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1599_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1599 {
  border-width:0px;
  position:absolute;
  left:163px;
  top:469px;
  width:157px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1599_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  white-space:nowrap;
}
#u1600_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1600 {
  border-width:0px;
  position:absolute;
  left:163px;
  top:417px;
  width:205px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1600_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  white-space:nowrap;
}
#u1601_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1601 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:442px;
  width:157px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1601_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  white-space:nowrap;
}
#u1602 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1603_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u1603 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:426px;
  width:362px;
  height:237px;
}
#u1603_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1604_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1604 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:426px;
  width:362px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1604_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:358px;
  word-wrap:break-word;
}
#u1605_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1605 {
  border-width:0px;
  position:absolute;
  left:437px;
  top:433px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1605_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1606_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1606 {
  border-width:0px;
  position:absolute;
  left:472px;
  top:433px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1606_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u1607 {
  border-width:0px;
  position:absolute;
  left:164px;
  top:465px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1607_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1607_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1608 {
  border-width:0px;
  position:absolute;
  left:164px;
  top:625px;
  width:94px;
  height:17px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1608_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:76px;
  word-wrap:break-word;
}
#u1608_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1609 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:517px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1609_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1609_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1610 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:544px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1610_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1610_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1611 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:571px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1611_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1611_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1612 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:598px;
  width:190px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1612_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:172px;
  word-wrap:break-word;
}
#u1612_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1613_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:30px;
  height:10px;
}
#u1613 {
  border-width:0px;
  position:absolute;
  left:489px;
  top:482px;
  width:25px;
  height:5px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1613_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1614 {
  border-width:0px;
  position:absolute;
  left:164px;
  top:490px;
  width:75px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1614_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:57px;
  word-wrap:break-word;
}
#u1614_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1616_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:720px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1616 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:720px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1616_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1617 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:440px;
}
#u1618_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1618 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1618_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1619_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1619 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1619_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1620_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1620 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1620_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1621_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1621 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:left;
}
#u1621_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1622_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1622 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1622_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1623_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1623 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:200px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1623_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1624_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1624 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:240px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1624_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1625_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1625 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:280px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1625_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1626_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1626 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:320px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1626_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1627_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1627 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:360px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1627_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1628_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1628 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:400px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:left;
}
#u1628_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u1629_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:709px;
  height:1px;
}
#u1629 {
  border-width:0px;
  position:absolute;
  left:-154px;
  top:424px;
  width:708px;
  height:0px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1629_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1631_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:72px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1631 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:1200px;
  height:72px;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1631_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:28px;
  width:1196px;
  word-wrap:break-word;
}
#u1632_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:71px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1632 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:1200px;
  height:71px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1632_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1633_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
}
#u1633 {
  border-width:0px;
  position:absolute;
  left:1066px;
  top:18px;
  width:55px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
}
#u1633_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  white-space:nowrap;
}
#u1634_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:21px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1634 {
  border-width:0px;
  position:absolute;
  left:1133px;
  top:16px;
  width:54px;
  height:21px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1634_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:51px;
  white-space:nowrap;
}
#u1635_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1635 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:17px;
  width:126px;
  height:22px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1635_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  word-wrap:break-word;
}
#u1636_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1201px;
  height:2px;
}
#u1636 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:1200px;
  height:1px;
}
#u1636_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1637 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:10px;
  width:499px;
  height:39px;
}
#u1638_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:39px;
}
#u1638 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1638_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:46px;
  word-wrap:break-word;
}
#u1639_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u1639 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1639_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u1640_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:39px;
}
#u1640 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:0px;
  width:60px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1640_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:56px;
  word-wrap:break-word;
}
#u1641_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u1641 {
  border-width:0px;
  position:absolute;
  left:190px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1641_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u1642_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:39px;
}
#u1642 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:74px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1642_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:70px;
  word-wrap:break-word;
}
#u1643_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u1643 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1643_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u1644_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:39px;
}
#u1644 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:0px;
  width:75px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1644_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:71px;
  word-wrap:break-word;
}
#u1645_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1645 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:11px;
  width:34px;
  height:34px;
}
#u1645_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1646_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:721px;
  height:2px;
}
#u1646 {
  border-width:0px;
  position:absolute;
  left:-160px;
  top:429px;
  width:720px;
  height:1px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1646_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1648_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:39px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  -webkit-box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1648 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:71px;
  width:1000px;
  height:39px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1648_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1649 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:12px;
  width:66px;
  height:39px;
}
#u1650_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:39px;
}
#u1650 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1650_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1651_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u1651 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:79px;
  width:120px;
  height:20px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u1651_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  white-space:nowrap;
}
#u1653 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:155px;
  width:81px;
  height:363px;
}
#u1654_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:40px;
}
#u1654 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1654_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:77px;
  word-wrap:break-word;
}
#u1655_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:40px;
}
#u1655 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:81px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1655_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:77px;
  word-wrap:break-word;
}
#u1656_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:40px;
}
#u1656 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:81px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1656_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:77px;
  word-wrap:break-word;
}
#u1657_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:40px;
}
#u1657 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:81px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1657_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:77px;
  word-wrap:break-word;
}
#u1658_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:40px;
}
#u1658 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:81px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1658_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:77px;
  word-wrap:break-word;
}
#u1659_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:40px;
}
#u1659 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:200px;
  width:81px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1659_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:77px;
  word-wrap:break-word;
}
#u1660_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:40px;
}
#u1660 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:240px;
  width:81px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1660_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1661_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:43px;
}
#u1661 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:280px;
  width:81px;
  height:43px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1661_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:13px;
  width:77px;
  word-wrap:break-word;
}
#u1662_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:40px;
}
#u1662 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:323px;
  width:81px;
  height:40px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1662_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:77px;
  word-wrap:break-word;
}
#u1663_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1663 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:387px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1663_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1664_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:191px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1664 {
  border-width:0px;
  position:absolute;
  left:379px;
  top:325px;
  width:191px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1664_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:191px;
  word-wrap:break-word;
}
#u1665 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:319px;
  width:42px;
  height:30px;
}
#u1665_input {
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1666 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:162px;
  width:196px;
  height:30px;
}
#u1666_input {
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
}
#u1666_input:disabled {
  color:grayText;
}
#u1667 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:200px;
  width:363px;
  height:30px;
}
#u1667_input {
  position:absolute;
  left:0px;
  top:0px;
  width:363px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1668_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u1668 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:207px;
  width:131px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u1668_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  word-wrap:break-word;
}
#u1669 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:240px;
  width:276px;
  height:30px;
}
#u1669_input {
  position:absolute;
  left:0px;
  top:0px;
  width:276px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1670 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:491px;
  width:58px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1670_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:40px;
  word-wrap:break-word;
}
#u1670_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1671 {
  border-width:0px;
  position:absolute;
  left:397px;
  top:491px;
  width:58px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1671_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:40px;
  word-wrap:break-word;
}
#u1671_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1672 {
  border-width:0px;
  position:absolute;
  left:465px;
  top:491px;
  width:55px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u1672_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:37px;
  word-wrap:break-word;
}
#u1672_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1673 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:280px;
  width:276px;
  height:30px;
}
#u1673_input {
  position:absolute;
  left:0px;
  top:0px;
  width:276px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u1674_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1674 {
  border-width:0px;
  position:absolute;
  left:535px;
  top:169px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1674_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  white-space:nowrap;
}
#u1675_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1675 {
  border-width:0px;
  position:absolute;
  left:389px;
  top:387px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1675_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1676_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1676 {
  border-width:0px;
  position:absolute;
  left:449px;
  top:387px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1676_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1677_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1677 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:387px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1677_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1678_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1678 {
  border-width:0px;
  position:absolute;
  left:569px;
  top:387px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1678_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:16px;
  width:46px;
  word-wrap:break-word;
}
#u1679_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:22px;
  background:inherit;
  background-color:rgba(0, 0, 255, 0.2);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 0, 255, 1);
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1679 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:382px;
  width:22px;
  height:22px;
}
#u1679_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:18px;
  word-wrap:break-word;
}
#u1680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:22px;
  background:inherit;
  background-color:rgba(0, 0, 255, 0.2);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 0, 255, 1);
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1680 {
  border-width:0px;
  position:absolute;
  left:423px;
  top:381px;
  width:22px;
  height:22px;
}
#u1680_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:18px;
  word-wrap:break-word;
}
#u1681_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:22px;
  background:inherit;
  background-color:rgba(0, 0, 255, 0.2);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 0, 255, 1);
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1681 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:381px;
  width:22px;
  height:22px;
}
#u1681_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:18px;
  word-wrap:break-word;
}
#u1682_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:22px;
  background:inherit;
  background-color:rgba(0, 0, 255, 0.2);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 0, 255, 1);
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1682 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:381px;
  width:22px;
  height:22px;
}
#u1682_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:4px;
  width:18px;
  word-wrap:break-word;
}
#u1683 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1684_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:520px;
  height:298px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u1684 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:334px;
  width:520px;
  height:298px;
}
#u1684_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1685_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:520px;
  height:31px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1685 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:335px;
  width:520px;
  height:31px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1685_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1686_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:center;
}
#u1686 {
  border-width:0px;
  position:absolute;
  left:508px;
  top:342px;
  width:189px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:center;
}
#u1686_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  word-wrap:break-word;
}
#u1687 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1688_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u1688 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:366px;
  width:260px;
  height:31px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
}
#u1688_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:7px;
  width:256px;
  word-wrap:break-word;
}
#u1689_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:261px;
  height:31px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1689 {
  border-width:0px;
  position:absolute;
  left:746px;
  top:366px;
  width:261px;
  height:31px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1689_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:7px;
  width:257px;
  word-wrap:break-word;
}
#u1690_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:461px;
  height:82px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-style:normal;
  font-size:12px;
}
#u1690 {
  border-width:0px;
  position:absolute;
  left:506px;
  top:407px;
  width:461px;
  height:82px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-style:normal;
  font-size:12px;
}
#u1690_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:461px;
  word-wrap:break-word;
}
#u1691_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1691 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:491px;
  width:64px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1691_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  word-wrap:break-word;
}
#u1692_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:31px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1692 {
  border-width:0px;
  position:absolute;
  left:508px;
  top:560px;
  width:98px;
  height:31px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1692_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:7px;
  width:94px;
  word-wrap:break-word;
}
#u1693_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:31px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1693 {
  border-width:0px;
  position:absolute;
  left:623px;
  top:560px;
  width:98px;
  height:31px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1693_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:7px;
  width:94px;
  word-wrap:break-word;
}
#u1694_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1694 {
  border-width:0px;
  position:absolute;
  left:964px;
  top:342px;
  width:37px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u1694_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  word-wrap:break-word;
}
#u1695 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1696_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:31px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1696 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:366px;
  width:260px;
  height:31px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
}
#u1696_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:7px;
  width:256px;
  word-wrap:break-word;
}
#u1697_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:261px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
}
#u1697 {
  border-width:0px;
  position:absolute;
  left:746px;
  top:366px;
  width:261px;
  height:31px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
}
#u1697_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:7px;
  width:257px;
  word-wrap:break-word;
}
#u1698_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:31px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1698 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:580px;
  width:98px;
  height:31px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1698_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:7px;
  width:94px;
  word-wrap:break-word;
}
#u1699_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:31px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1699 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:580px;
  width:98px;
  height:31px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1699_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:7px;
  width:94px;
  word-wrap:break-word;
}
#u1700 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:407px;
  width:286px;
  height:25px;
}
#u1700_input {
  position:absolute;
  left:0px;
  top:0px;
  width:286px;
  height:25px;
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1701_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1701 {
  border-width:0px;
  position:absolute;
  left:795px;
  top:411px;
  width:79px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u1701_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  word-wrap:break-word;
}
#u1702_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1702 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:446px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1702_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1703_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1703 {
  border-width:0px;
  position:absolute;
  left:572px;
  top:446px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1703_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1704_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1704 {
  border-width:0px;
  position:absolute;
  left:632px;
  top:446px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1704_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1705_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1705 {
  border-width:0px;
  position:absolute;
  left:692px;
  top:446px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1705_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1706_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1706 {
  border-width:0px;
  position:absolute;
  left:752px;
  top:446px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1706_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1707_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1707 {
  border-width:0px;
  position:absolute;
  left:812px;
  top:446px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1707_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1708_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1708 {
  border-width:0px;
  position:absolute;
  left:872px;
  top:446px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1708_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1709_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1709 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:446px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1709_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1710_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1710 {
  border-width:0px;
  position:absolute;
  left:513px;
  top:507px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1710_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1711_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1711 {
  border-width:0px;
  position:absolute;
  left:573px;
  top:507px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1711_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1712_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1712 {
  border-width:0px;
  position:absolute;
  left:633px;
  top:507px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1712_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1713_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1713 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:507px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1713_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1714_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1714 {
  border-width:0px;
  position:absolute;
  left:753px;
  top:507px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1714_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1715_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1715 {
  border-width:0px;
  position:absolute;
  left:813px;
  top:507px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1715_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1716_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1716 {
  border-width:0px;
  position:absolute;
  left:873px;
  top:507px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1716_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1717_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1717 {
  border-width:0px;
  position:absolute;
  left:933px;
  top:507px;
  width:50px;
  height:50px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1717_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1718 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:481px;
  width:24px;
  height:16px;
}
#u1718_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1718_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1719 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:481px;
  width:24px;
  height:16px;
}
#u1719_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1719_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1720 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:480px;
  width:24px;
  height:16px;
}
#u1720_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1720_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1721 {
  border-width:0px;
  position:absolute;
  left:728px;
  top:481px;
  width:24px;
  height:16px;
}
#u1721_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1721_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1722 {
  border-width:0px;
  position:absolute;
  left:789px;
  top:481px;
  width:24px;
  height:16px;
}
#u1722_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1722_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1723 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:481px;
  width:24px;
  height:16px;
}
#u1723_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1723_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1724 {
  border-width:0px;
  position:absolute;
  left:909px;
  top:480px;
  width:24px;
  height:16px;
}
#u1724_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1724_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1725 {
  border-width:0px;
  position:absolute;
  left:969px;
  top:481px;
  width:24px;
  height:16px;
}
#u1725_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1725_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1726 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:541px;
  width:24px;
  height:16px;
}
#u1726_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1726_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1727 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:541px;
  width:24px;
  height:16px;
}
#u1727_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1727_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1728 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:540px;
  width:24px;
  height:16px;
}
#u1728_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1728_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1729 {
  border-width:0px;
  position:absolute;
  left:728px;
  top:541px;
  width:24px;
  height:16px;
}
#u1729_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1729_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1730 {
  border-width:0px;
  position:absolute;
  left:789px;
  top:541px;
  width:24px;
  height:16px;
}
#u1730_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1730_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1731 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:541px;
  width:24px;
  height:16px;
}
#u1731_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1731_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1732 {
  border-width:0px;
  position:absolute;
  left:909px;
  top:540px;
  width:24px;
  height:16px;
}
#u1732_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1732_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1733 {
  border-width:0px;
  position:absolute;
  left:969px;
  top:541px;
  width:24px;
  height:16px;
}
#u1733_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:6px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1733_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1734 {
  border-width:0px;
  position:absolute;
  left:508px;
  top:450px;
  width:145px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-style:normal;
}
#u1734_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:127px;
  word-wrap:break-word;
}
#u1734_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1735 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:450px;
  width:175px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-style:normal;
}
#u1735_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:157px;
  word-wrap:break-word;
}
#u1735_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u1736 {
  border-width:0px;
  position:absolute;
  left:1235px;
  top:829px;
  width:583px;
  height:120px;
}
#u1737_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u1737 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u1737_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u1738_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u1738 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:0px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u1738_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u1739_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u1739 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u1739_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u1740_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u1740 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:30px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u1740_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u1741_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u1741 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u1741_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u1742_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u1742 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:60px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u1742_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u1743_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u1743 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:90px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u1743_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u1744_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u1744 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:90px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u1744_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u1745_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1745 {
  border-width:0px;
  position:absolute;
  left:1235px;
  top:973px;
  width:133px;
  height:34px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1745_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  white-space:nowrap;
}
#u1746_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1746 {
  border-width:0px;
  position:absolute;
  left:1235px;
  top:812px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u1746_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  white-space:nowrap;
}
#u1747 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:113px;
  width:1000px;
  height:39px;
}
#u1748_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:39px;
}
#u1748 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:39px;
}
#u1748_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1749_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1749 {
  border-width:0px;
  position:absolute;
  left:222px;
  top:122px;
  width:187px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u1749_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  white-space:nowrap;
}
#u1750_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1750 {
  border-width:0px;
  position:absolute;
  left:930px;
  top:116px;
  width:57px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1750_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:53px;
  word-wrap:break-word;
}
#u1751_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1751 {
  border-width:0px;
  position:absolute;
  left:1115px;
  top:116px;
  width:57px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1751_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:53px;
  word-wrap:break-word;
}
#u1752_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1752 {
  border-width:0px;
  position:absolute;
  left:1001px;
  top:116px;
  width:102px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1752_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:98px;
  word-wrap:break-word;
}
#u1753_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:744px;
  height:187px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-style:normal;
  font-size:12px;
}
#u1753 {
  border-width:0px;
  position:absolute;
  left:1235px;
  top:21px;
  width:744px;
  height:187px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-style:normal;
  font-size:12px;
}
#u1753_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:744px;
  word-wrap:break-word;
}
