body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1883px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u1_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:720px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:720px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:440px;
}
#u3_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u3_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u4_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u4_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u5_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u5_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u6_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:left;
}
#u6_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u7_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u7_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u8_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:200px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u8_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u9_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:240px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u9_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u10_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:280px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u10_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u11_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:320px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u11_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u12_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:360px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u12_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u13_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:400px;
  width:200px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:left;
}
#u13_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:12px;
  width:196px;
  word-wrap:break-word;
}
#u14_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:709px;
  height:1px;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:-154px;
  top:424px;
  width:708px;
  height:0px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u14_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u16_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:72px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:1200px;
  height:72px;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u16_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:28px;
  width:1196px;
  word-wrap:break-word;
}
#u17_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:71px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-1px;
  width:1200px;
  height:71px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u17_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u18_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:1066px;
  top:18px;
  width:55px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
}
#u18_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  white-space:nowrap;
}
#u19_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:21px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:1133px;
  top:16px;
  width:54px;
  height:21px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u19_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:2px;
  width:51px;
  white-space:nowrap;
}
#u20_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:17px;
  width:126px;
  height:22px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u20_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  word-wrap:break-word;
}
#u21_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1201px;
  height:2px;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:1200px;
  height:1px;
}
#u21_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:10px;
  width:499px;
  height:39px;
}
#u23_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:39px;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u23_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:46px;
  word-wrap:break-word;
}
#u24_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u24_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u25_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:39px;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:0px;
  width:60px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u25_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:56px;
  word-wrap:break-word;
}
#u26_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:190px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u26_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u27_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:39px;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:74px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u27_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:70px;
  word-wrap:break-word;
}
#u28_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u28_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u29_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:39px;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:0px;
  width:75px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u29_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:71px;
  word-wrap:break-word;
}
#u30_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:11px;
  width:34px;
  height:34px;
}
#u30_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u31_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:721px;
  height:2px;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:-160px;
  top:429px;
  width:720px;
  height:1px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u31_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:39px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  -webkit-box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:71px;
  width:1000px;
  height:39px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u33_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u34_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:121px;
  width:72px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u34_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  word-wrap:break-word;
}
#u35_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:443px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:1359px;
  top:121px;
  width:443px;
  height:34px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u35_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:443px;
  word-wrap:break-word;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:116px;
  width:1000px;
  height:44px;
}
#u37_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:44px;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:44px;
}
#u37_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u38_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:81px;
  width:100px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u38_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  white-space:nowrap;
}
#u40_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:66px;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:204px;
  width:78px;
  height:66px;
}
#u40_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u42_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:66px;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:204px;
  width:78px;
  height:66px;
}
#u42_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u44_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:66px;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:639px;
  top:204px;
  width:78px;
  height:66px;
}
#u44_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u46_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:66px;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:818px;
  top:204px;
  width:78px;
  height:66px;
}
#u46_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u48_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:66px;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:999px;
  top:204px;
  width:78px;
  height:66px;
}
#u48_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:270px;
  width:908px;
  height:31px;
}
#u50_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:31px;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:31px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u50_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:10px;
  width:178px;
  word-wrap:break-word;
}
#u51_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:31px;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:182px;
  top:0px;
  width:182px;
  height:31px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u51_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:10px;
  width:178px;
  word-wrap:break-word;
}
#u52_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:31px;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:0px;
  width:182px;
  height:31px;
  font-size:11px;
  color:#000000;
}
#u52_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:178px;
  word-wrap:break-word;
}
#u53_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:31px;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:546px;
  top:0px;
  width:182px;
  height:31px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u53_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:10px;
  width:178px;
  word-wrap:break-word;
}
#u54_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:180px;
  height:31px;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:728px;
  top:0px;
  width:180px;
  height:31px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u54_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:10px;
  width:176px;
  word-wrap:break-word;
}
#u55 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#0000FF;
}
#u56 {
  border-width:0px;
  position:absolute;
  left:221px;
  top:121px;
  width:62px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  color:#0000FF;
}
#u56_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:58px;
  word-wrap:break-word;
}
#u57_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u57 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:121px;
  width:80px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u57_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:76px;
  word-wrap:break-word;
}
#u58 {
  border-width:0px;
  position:absolute;
  left:916px;
  top:121px;
  width:232px;
  height:30px;
}
#u58_input {
  position:absolute;
  left:0px;
  top:0px;
  width:232px;
  height:30px;
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u59_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'AppleColorEmoji', 'Apple Color Emoji';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u59 {
  border-width:0px;
  position:absolute;
  left:1120px;
  top:126px;
  width:18px;
  height:18px;
  font-family:'AppleColorEmoji', 'Apple Color Emoji';
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u59_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  white-space:nowrap;
}
#u60_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u60 {
  border-width:0px;
  position:absolute;
  left:514px;
  top:121px;
  width:80px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u60_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:76px;
  word-wrap:break-word;
}
#u61_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u61 {
  border-width:0px;
  position:absolute;
  left:299px;
  top:121px;
  width:98px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u61_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:94px;
  word-wrap:break-word;
}
#u63_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:78px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u63 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:356px;
  width:78px;
  height:78px;
}
#u63_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u65_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:78px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u65 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:356px;
  width:78px;
  height:78px;
}
#u65_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u67_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:78px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u67 {
  border-width:0px;
  position:absolute;
  left:639px;
  top:356px;
  width:78px;
  height:78px;
}
#u67_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u69_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:78px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u69 {
  border-width:0px;
  position:absolute;
  left:818px;
  top:356px;
  width:78px;
  height:78px;
}
#u69_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u71_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:78px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u71 {
  border-width:0px;
  position:absolute;
  left:999px;
  top:356px;
  width:78px;
  height:78px;
}
#u71_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u72 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:434px;
  width:908px;
  height:30px;
}
#u73_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u73 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u73_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:178px;
  word-wrap:break-word;
}
#u74_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u74 {
  border-width:0px;
  position:absolute;
  left:182px;
  top:0px;
  width:182px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u74_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:178px;
  word-wrap:break-word;
}
#u75_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u75 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:0px;
  width:182px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u75_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:178px;
  word-wrap:break-word;
}
#u76_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u76 {
  border-width:0px;
  position:absolute;
  left:546px;
  top:0px;
  width:182px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u76_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:178px;
  word-wrap:break-word;
}
#u77_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:180px;
  height:30px;
}
#u77 {
  border-width:0px;
  position:absolute;
  left:728px;
  top:0px;
  width:180px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u77_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:176px;
  word-wrap:break-word;
}
#u78 {
  border-width:0px;
  position:absolute;
  left:258px;
  top:178px;
  width:134px;
  height:117px;
}
#u79_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:117px;
}
#u79 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:117px;
}
#u79_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u80_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u80 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:276px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u80_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u81_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u81 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:187px;
  width:510px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u81_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  word-wrap:break-word;
}
#u82_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u82 {
  border-width:0px;
  position:absolute;
  left:1366px;
  top:187px;
  width:436px;
  height:34px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u82_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  word-wrap:break-word;
}
#u83_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u83 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:65px;
  width:72px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u83_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  word-wrap:break-word;
}
#u84_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u84 {
  border-width:0px;
  position:absolute;
  left:1359px;
  top:65px;
  width:271px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u84_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  word-wrap:break-word;
}
#u85 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:505px;
  width:583px;
  height:120px;
}
#u86_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u86 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u86_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u87_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u87 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:0px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u87_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u88_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u88 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u88_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u89_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u89 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:30px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u89_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u90_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u90 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u90_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u91_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u91 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:60px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u91_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u92_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u92 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:90px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u92_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u93_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:30px;
}
#u93 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:90px;
  width:510px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u93_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:506px;
  word-wrap:break-word;
}
#u94_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u94 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:649px;
  width:133px;
  height:34px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u94_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  white-space:nowrap;
}
#u95_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u95 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:488px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u95_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  white-space:nowrap;
}
#u96_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u96 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:259px;
  width:510px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u96_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  word-wrap:break-word;
}
#u97_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u97 {
  border-width:0px;
  position:absolute;
  left:1402px;
  top:259px;
  width:436px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u97_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  word-wrap:break-word;
}
#u98 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u99_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:475px;
  height:78px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u99 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:283px;
  width:475px;
  height:78px;
}
#u99_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u100_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:28px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:9px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u100 {
  border-width:0px;
  position:absolute;
  left:1620px;
  top:310px;
  width:62px;
  height:28px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u100_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:5px;
  width:58px;
  word-wrap:break-word;
}
#u101_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:28px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:9px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u101 {
  border-width:0px;
  position:absolute;
  left:1694px;
  top:310px;
  width:62px;
  height:28px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u101_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:5px;
  width:58px;
  word-wrap:break-word;
}
#u102 {
  border-width:0px;
  position:absolute;
  left:1310px;
  top:310px;
  width:300px;
  height:28px;
}
#u102_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:28px;
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u103 {
  border-width:0px;
  position:absolute;
  left:314px;
  top:196px;
  width:83px;
  height:82px;
}
#u104_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
}
#u104 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0000FF;
}
#u104_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:79px;
  word-wrap:break-word;
}
#u105_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
}
#u105 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:83px;
  height:30px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0000FF;
}
#u105_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:9px;
  width:79px;
  word-wrap:break-word;
}
#u106_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:22px;
}
#u106 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:83px;
  height:22px;
  font-family:'.PingFangSC-Regular', '.PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0000FF;
}
#u106_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:5px;
  width:79px;
  word-wrap:break-word;
}
#u107 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u108 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:112px;
  width:1000px;
  height:44px;
}
#u109_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:44px;
}
#u109 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:44px;
  text-align:left;
}
#u109_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u110_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u110 {
  border-width:0px;
  position:absolute;
  left:962px;
  top:119px;
  width:80px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u110_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:76px;
  word-wrap:break-word;
}
#u111_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:6px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u111 {
  border-width:0px;
  position:absolute;
  left:848px;
  top:119px;
  width:98px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u111_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:94px;
  word-wrap:break-word;
}
#u112_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u112 {
  border-width:0px;
  position:absolute;
  left:222px;
  top:125px;
  width:75px;
  height:18px;
}
#u112_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  white-space:nowrap;
}
#u113 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:297px;
  width:100px;
  height:16px;
}
#u113_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u113_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u114 {
  border-width:0px;
  position:absolute;
  left:315px;
  top:298px;
  width:100px;
  height:16px;
}
#u114_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u114_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u115 {
  border-width:0px;
  position:absolute;
  left:848px;
  top:298px;
  width:100px;
  height:16px;
}
#u115_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u115_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u116 {
  border-width:0px;
  position:absolute;
  left:675px;
  top:298px;
  width:100px;
  height:16px;
}
#u116_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u116_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u117 {
  border-width:0px;
  position:absolute;
  left:1038px;
  top:298px;
  width:100px;
  height:16px;
}
#u117_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u117_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u118 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:461px;
  width:100px;
  height:16px;
}
#u118_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u118_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u119 {
  border-width:0px;
  position:absolute;
  left:315px;
  top:462px;
  width:100px;
  height:16px;
}
#u119_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u119_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u120 {
  border-width:0px;
  position:absolute;
  left:848px;
  top:462px;
  width:100px;
  height:16px;
}
#u120_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u120_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u121 {
  border-width:0px;
  position:absolute;
  left:675px;
  top:462px;
  width:100px;
  height:16px;
}
#u121_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u121_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u122 {
  border-width:0px;
  position:absolute;
  left:1038px;
  top:462px;
  width:100px;
  height:16px;
}
#u122_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:0px;
  width:82px;
  visibility:hidden;
  word-wrap:break-word;
}
#u122_input {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
}
#u123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u123 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:389px;
  width:510px;
  height:34px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#336633;
}
#u123_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  word-wrap:break-word;
}
#u124 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:475px;
  height:337px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.349019607843137);
}
#u125 {
  border-width:0px;
  position:absolute;
  left:428px;
  top:215px;
  width:475px;
  height:337px;
}
#u125_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u126_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:475px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u126 {
  border-width:0px;
  position:absolute;
  left:428px;
  top:215px;
  width:475px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u126_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:6px;
  width:471px;
  word-wrap:break-word;
}
#u127_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u127 {
  border-width:0px;
  position:absolute;
  left:846px;
  top:222px;
  width:25px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
}
#u127_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  white-space:nowrap;
}
#u128_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u128 {
  border-width:0px;
  position:absolute;
  left:448px;
  top:285px;
  width:436px;
  height:17px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u128_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  word-wrap:break-word;
}
#u129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  height:85px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u129 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:312px;
  width:436px;
  height:85px;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
}
#u129_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  word-wrap:break-word;
}
#u130_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:9px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u130 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:464px;
  width:140px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u130_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:136px;
  word-wrap:break-word;
}
#u131_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:9px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u131 {
  border-width:0px;
  position:absolute;
  left:625px;
  top:464px;
  width:140px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u131_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:11px;
  width:136px;
  word-wrap:break-word;
}
#u132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u132 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:418px;
  width:56px;
  height:17px;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u132_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  white-space:nowrap;
}
#u133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u133 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:363px;
  width:94px;
  height:21px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u133_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
#u134_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:371px;
  height:388px;
}
#u134 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:201px;
  width:371px;
  height:388px;
}
#u134_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  visibility:hidden;
  word-wrap:break-word;
}
