.ax_default {
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  color:#333333;
  text-align:center;
  line-height:normal;
}
._形状 {
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  color:#333333;
  text-align:center;
  line-height:normal;
}
._图片 {
}
._一级标题 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
._二级标题 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
._三级标题 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
._形状1 {
  font-family:'ArialMT', 'Arial';
  font-weight:normal;
  font-style:normal;
}
._文本框 {
  color:#000000;
  text-align:left;
}
._四级标题 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
._五级标题 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
._六级标题 {
  font-family:'ArialMT', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
.label {
  font-size:14px;
  text-align:left;
}
._文本段落 {
  text-align:left;
}
._流程形状 {
}
._连接线 {
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF0000;
  text-align:center;
  line-height:normal;
}
.sticky_1 {
  text-align:left;
}
._文本段落1 {
  text-align:left;
}
._一级标题1 {
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
._图片1 {
  color:#000000;
}
.line {
}
.text_field {
  color:#000000;
  text-align:left;
}
.text_area {
  color:#000000;
  text-align:left;
}
.droplist {
  color:#000000;
  text-align:left;
}
.box_2 {
}
.box_1 {
}
.checkbox {
  text-align:left;
}
.box_3 {
}
.ellipse {
}
._图片2 {
  color:#000000;
}
.button {
}
.primary_button {
  color:#FFFFFF;
}
.flow_shape {
}
.icon {
}
.placeholder {
}
.checkbox1 {
  text-align:left;
}
.radio_button {
  text-align:left;
}
.table_cell {
}
._垂直线 {
}
._水平线 {
}
._形状2 {
}
._复选框 {
  text-align:left;
}
