$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z,A,[_(u,B,w,C,y,D,A,[_(u,E,w,C,y,F,A,[_(u,G,w,C,y,H)]),_(u,I,w,C,y,J,A,[_(u,K,w,C,y,L)]),_(u,M,w,C,y,N),_(u,O,w,C,y,P),_(u,Q,w,C,y,R),_(u,S,w,C,y,T,A,[_(u,U,w,C,y,V),_(u,W,w,C,y,X)]),_(u,Y,w,C,y,Z)]),_(u,ba,w,C,y,bb,A,[_(u,bc,w,C,y,bd)]),_(u,be,w,C,y,bf,A,[_(u,bg,w,C,y,bh,A,[_(u,bi,w,C,y,bj)]),_(u,bk,w,C,y,bl,A,[_(u,bm,w,C,y,bn,A,[_(u,bo,w,C,y,bp),_(u,bq,w,C,y,br)]),_(u,bs,w,C,y,bt,A,[_(u,bu,w,C,y,bv),_(u,bw,w,C,y,bx)]),_(u,by,w,C,y,bz,A,[_(u,bo,w,C,y,bA),_(u,bB,w,C,y,bC)]),_(u,bD,w,C,y,bE)]),_(u,bF,w,C,y,bG,A,[_(u,bH,w,C,y,bI,A,[_(u,bJ,w,C,y,bK)]),_(u,bL,w,C,y,bM,A,[_(u,bN,w,C,y,bO)])]),_(u,bP,w,C,y,bQ),_(u,bR,w,C,y,bS,A,[_(u,bT,w,C,y,bU)]),_(u,bV,w,C,y,bW,A,[_(u,M,w,C,y,bX),_(u,O,w,C,y,bY),_(u,Q,w,C,y,bZ,A,[_(u,ca,w,C,y,cb)])])]),_(u,cc,w,C,y,cd,A,[_(u,cc,w,C,y,ce)]),_(u,cf,w,C,y,cg,A,[_(u,ch,w,C,y,ci)])])]),cj,_(ck,z),cl,_(cm,cn,co,_(cp,cq,cr,cq),cs,ct),cu,[],cv,_(cw,_(cx,cy,cz,cA,cB,cC,cD,cE,cF,cG,cH,f,cI,_(cJ,cK,cL,cM,cN,cO),cP,cQ,cR,cC,cS,_(cT,cq,cU,cq),co,_(cp,cq,cr,cq),cV,d,cW,f,cX,cy,cY,_(cJ,cK,cL,cZ),da,_(cJ,cK,cL,db),dc,dd,de,cK,cN,dd,df,dg,dh,di,dj,dk,dl,dk,dm,dk,dn,dk,dp,_(),dq,dg,dr,dg,ds,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dD)),dE,_(dt,f,du,cq,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dD)),dF,_(dt,f,du,cO,dw,cO,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG))),dH,_(dI,_(cx,dJ,cz,cA,cB,cC,cI,_(cJ,cK,cL,cM,cN,cO),da,_(cJ,cK,cL,db),dc,dg,cY,_(cJ,cK,cL,dK),cP,cQ,cD,cE,cF,cG,cH,f,de,cK,df,dg,cN,dd,ds,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dD)),dE,_(dt,f,du,cq,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dD)),dF,_(dt,f,du,cO,dw,cO,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG)),dh,di,dj,dk,dl,dk,dm,dk,dn,dk,cR,cC),dL,_(cx,dM,dc,dg),dN,_(cx,dO,cF,dP,cz,dQ,dc,dg,cY,_(cJ,cK,cL,dR),cP,dS,dh,dT,dj,dg,dl,dg,dm,dg,dn,dg),dU,_(cx,dV,cF,dW,cz,dQ,dc,dg,cY,_(cJ,cK,cL,dR),cP,dS,dh,dT,dj,dg,dl,dg,dm,dg,dn,dg),dX,_(cx,dY,cF,dZ,cz,dQ,dc,dg,cY,_(cJ,cK,cL,dR),cP,dS,dh,dT,dj,dg,dl,dg,dm,dg,dn,dg),ea,_(cx,eb,cz,cC,cB,cC,ds,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG)),dE,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG)),dF,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG))),ec,_(cx,ed,cI,_(cJ,cK,cL,ee,cN,cO),ds,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG)),dE,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG)),dF,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG)),cP,dS),ef,_(cx,eg,cF,eh,cz,dQ,dc,dg,cY,_(cJ,cK,cL,dR),cP,dS,dh,dT,dj,dg,dl,dg,dm,dg,dn,dg),ei,_(cx,ej,cz,dQ,dc,dg,cY,_(cJ,cK,cL,dR),cP,dS,dh,dT,dj,dg,dl,dg,dm,dg,dn,dg),ek,_(cx,el,cF,em,cz,dQ,dc,dg,cY,_(cJ,cK,cL,dR),cP,dS,dh,dT,dj,dg,dl,dg,dm,dg,dn,dg),en,_(cx,eo,cF,eh,dc,dg,cY,_(cJ,cK,cL,dR),cP,dS,dh,dT,dj,dg,dl,dg,dm,dg,dn,dg),ep,_(cx,eq,dc,dg,cY,_(cJ,cK,cL,dR),cP,dS,dh,dT,dj,dg,dl,dg,dm,dg,dn,dg),er,_(cx,es,cY,_(cJ,et,eu,[_(cL,cZ),_(cL,ev),_(cL,ew),_(cL,cZ)])),ex,_(cx,ey,cz,cA,cB,cC,cI,_(cJ,cK,cL,ez,cN,cO),da,_(cJ,cK,cL,eA),dc,dd,cP,cQ,cD,eB,cF,eh,cH,f,de,cK,df,dg,cY,_(cJ,cK,cL,cZ),cN,dd,ds,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dD)),dE,_(dt,f,du,cq,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dD)),dF,_(dt,f,du,cO,dw,cO,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG)),dh,di,dj,dk,dl,dk,dm,dk,dn,dk,cR,cC),eC,_(cx,eD,dc,dg,cY,_(cJ,cK,cL,eE),ds,_(dt,d,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,eF)),cP,dS,dh,dT,dj,eG,dl,eG,dm,eG,dn,eG),eH,_(cx,eI,da,_(cJ,cK,cL,dR),dc,dg,cY,_(cJ,cK,cL,dR),cP,dS,dh,dT,dj,dg,dl,dg,dm,dg,dn,dg),eJ,_(cx,eK,cz,eL,cF,dP,da,_(cJ,cK,cL,dR),dc,dg,cY,_(cJ,cK,cL,dR),cP,dS,dh,dT,dj,dg,dl,dg,dm,dg,dn,dg),eM,_(cx,eN,cI,_(cJ,cK,cL,ee,cN,cO),dc,dg),eO,_(cx,eP,cY,_(cJ,cK,cL,dR)),eQ,_(cx,eR,cI,_(cJ,cK,cL,ee,cN,cO),cP,dS,dh,di),eS,_(cx,eT,cI,_(cJ,cK,cL,ee,cN,cO),cP,dS,dh,dT),eU,_(cx,eV,cI,_(cJ,cK,cL,ee,cN,cO),cP,dS,dh,dT),eW,_(cx,eX,dc,dg,cY,_(cJ,cK,cL,ev)),eY,_(cx,eZ),fa,_(cx,fb,cP,dS,dh,dT),fc,_(cx,fd,dc,dg,cY,_(cJ,cK,cL,fe)),ff,_(cx,fg),fh,_(cx,fi,cI,_(cJ,cK,cL,ee,cN,cO),dc,dg,ds,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG)),dE,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG)),dF,_(dt,f,du,dv,dw,dv,dx,dv,cL,_(dy,dz,dA,dz,dB,dz,dC,dG))),fj,_(cx,fk,df,fl),fm,_(cx,fn,cI,_(cJ,cK,cL,cZ,cN,cO),dc,dg,df,fl,cY,_(cJ,cK,cL,fo)),fp,_(cx,fq,cY,_(cJ,et,eu,[_(cL,cZ),_(cL,ev),_(cL,ew),_(cL,cZ)])),fr,_(cx,fs,dc,dg,cY,_(cJ,cK,cL,cM)),ft,_(cx,fu,cY,_(cJ,cK,cL,ev)),fv,_(cx,fw,cP,dS,dh,dT),fx,_(cx,fy,cP,dS,dh,dT),fz,_(cx,fA),fB,_(cx,fC,da,_(cJ,cK,cL,ee)),fD,_(cx,fE,da,_(cJ,cK,cL,ee)),fF,_(cx,fG),fH,_(cx,fI,cP,dS,cY,_(cJ,cK,cL,dR),dc,dg,dh,dT)),fJ,_(fK,eZ,fL,eX,fM,eR,fN,dY,fO,fn,fP,fg,fQ,eP,fR,fs,fS,fd,fT,dM,fU,eI,fV,fE,fW,eN,fX,eN)));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="正餐",w="type",x="Folder",y="url",z="",A="children",B="桌台",C="Wireframe",D="桌台.html",E="转台",F="转台.html",G="转台-已选桌",H="转台-已选桌.html",I="并台",J="并台.html",K="并台-已选桌",L="并台-已选桌.html",M="叫起",N="叫起.html",O="催菜",P="催菜.html",Q="划菜",R="划菜.html",S="退菜",T="退菜.html",U="退菜-选择数量",V="退菜-选择数量.html",W="退菜-退菜原因",X="退菜-退菜原因.html",Y="修改人数",Z="修改人数.html",ba="桌台页-相关弹框",bb="桌台页-相关弹框.html",bc="待清台",bd="待清台.html",be="下单",bf="下单.html",bg="点餐-选择商品",bh="点餐-选择商品.html",bi="商品沽清",bj="商品沽清.html",bk="未下单-优化版",bl="未下单-优化版.html",bm="未下单-商品编辑（普通商品/规格商品）",bn="未下单-商品编辑（普通商品_规格商品）.html",bo="未下单-修改数量",bp="未下单-修改数量.html",bq="未下单（普通商品/规格商品）-取消赠送",br="未下单（普通商品_规格商品）-取消赠送.html",bs="未下单-商品编辑（称重商品）",bt="未下单-商品编辑（称重商品）.html",bu="未下单-修改重量",bv="未下单-修改重量.html",bw="未下单（称重商品）-取消赠送",bx="未下单（称重商品）-取消赠送.html",by="未下单-商品编辑（套餐商品）",bz="未下单-商品编辑（套餐商品）.html",bA="未下单-修改数量_1.html",bB="未下单（套餐商品）-取消赠送",bC="未下单（套餐商品）-取消赠送.html",bD="未下单（套餐成分）-编辑",bE="未下单（套餐成分）-编辑.html",bF="已下单-优化版",bG="已下单-优化版.html",bH="已下单-商品编辑（普通商品/规格商品/套餐商品）",bI="已下单-商品编辑（普通商品_规格商品_套餐商品）.html",bJ="已下单（非称重商品）-取消赠送",bK="已下单（非称重商品）-取消赠送.html",bL="已下单-商品编辑（称重商品）",bM="已下单-商品编辑（称重商品）.html",bN="已下单（称重商品）-取消赠送",bO="已下单（称重商品）-取消赠送.html",bP="已退菜",bQ="已退菜.html",bR="整单备注",bS="整单备注.html",bT="整单备注-已填入",bU="整单备注-已填入.html",bV="挂起",bW="挂起.html",bX="叫起_1.html",bY="催菜_1.html",bZ="划菜_1.html",ca="已划菜",cb="已划菜.html",cc="结账",cd="结账.html",ce="结账_1.html",cf="结账-相关提示",cg="结账-相关提示.html",ch="聚合支付流程",ci="聚合支付流程.html",cj="globalVariables",ck="onloadvariable",cl="defaultAdaptiveView",cm="name",cn="基本",co="size",cp="width",cq=0,cr="height",cs="condition",ct="<=",cu="adaptiveViews",cv="stylesheet",cw="defaultStyle",cx="id",cy="627587b6038d43cca051c114ac41ad32",cz="fontWeight",cA="400",cB="fontStyle",cC="normal",cD="fontName",cE="'ArialMT', 'Arial'",cF="fontSize",cG="13px",cH="underline",cI="foreGroundFill",cJ="fillType",cK="solid",cL="color",cM=0xFF333333,cN="opacity",cO=1,cP="horizontalAlignment",cQ="center",cR="lineSpacing",cS="location",cT="x",cU="y",cV="visible",cW="limbo",cX="baseStyle",cY="fill",cZ=0xFFFFFFFF,da="borderFill",db=0xFF797979,dc="borderWidth",dd="1",de="linePattern",df="cornerRadius",dg="0",dh="verticalAlignment",di="middle",dj="paddingLeft",dk="2",dl="paddingTop",dm="paddingRight",dn="paddingBottom",dp="stateStyles",dq="rotation",dr="textRotation",ds="outerShadow",dt="on",du="offsetX",dv=5,dw="offsetY",dx="blurRadius",dy="r",dz=0,dA="g",dB="b",dC="a",dD=0.349019607843137,dE="innerShadow",dF="textShadow",dG=0.647058823529412,dH="customStyles",dI="_形状",dJ="40519e9ec4264601bfb12c514e4f4867",dK=0xFFCCCCCC,dL="_图片",dM="75a91ee5b9d042cfa01b8d565fe289c0",dN="_一级标题",dO="1111111151944dfba49f67fd55eb1f88",dP="32px",dQ="bold",dR=0xFFFFFF,dS="left",dT="top",dU="_二级标题",dV="b3a15c9ddde04520be40f94c8168891e",dW="24px",dX="_三级标题",dY="8c7a4c5ad69a4369a5f7788171ac0b32",dZ="18px",ea="_形状1",eb="426ec94f31334767a34e4d823be7d057",ec="_文本框",ed="add83f5951a94bf8842f5d6b2002aa61",ee=0xFF000000,ef="_四级标题",eg="e995c891077945c89c0b5fe110d15a0b",eh="14px",ei="_五级标题",ej="386b19ef4be143bd9b6c392ded969f89",ek="_六级标题",el="fc3b9a13b5574fa098ef0a1db9aac861",em="10px",en="label",eo="2285372321d148ec80932747449c36c9",ep="_文本段落",eq="4988d43d80b44008a4a415096f1632af",er="_流程形状",es="df01900e3c4e43f284bafec04b0864c4",et="linearGradient",eu="colors",ev=0xFFF2F2F2,ew=0xFFE4E4E4,ex="_连接线",ey="699a012e142a4bcba964d96e88b88bdf",ez=0xFFFF0000,eA=0xFF0099FF,eB="'PingFangSC-Regular', 'PingFang SC'",eC="sticky_1",eD="31e8887730cc439f871dc77ac74c53b6",eE=0xFFFFDF25,eF=0.2,eG="10",eH="_文本段落1",eI="2ec62ba0db1d4e18a7d982a2209cad57",eJ="_一级标题1",eK="66a7c84627cc4501afe697946481f448",eL="700",eM="_图片1",eN="eedf89ad9fb3492ea6caad6ef3274bed",eO="line",eP="6d28dc34d26f448496768109b73cee5b",eQ="text_field",eR="8378d8bcc5914330bbfe3f0aa60635e9",eS="text_area",eT="42ee17691d13435b8256d8d0a814778f",eU="droplist",eV="85f724022aae41c594175ddac9c289eb",eW="box_2",eX="********************************",eY="box_1",eZ="********************************",fa="checkbox",fb="********************************",fc="box_3",fd="********************************",fe=0xFFD7D7D7,ff="ellipse",fg="0565ab6eeea2495995c5b2ec50c6a56a",fh="_图片2",fi="bf3b343990f745c69b06e910443d4a19",fj="button",fk="901241599faa4dd299c17c9a8f3d13fc",fl="5",fm="primary_button",fn="cd64754845384de3872fb4a066432c1f",fo=0xFF169BD5,fp="flow_shape",fq="8ca8f3db21b54150881e709417298be1",fr="icon",fs="26c731cb771b44a88eb8b6e97e78c80e",ft="placeholder",fu="973908d7067843c18fc23fe91ff8223f",fv="checkbox1",fw="********************************",fx="radio_button",fy="4eb5516f311c4bdfa0cb11d7ea75084e",fz="table_cell",fA="33ea2511485c479dbf973af3302f2352",fB="_垂直线",fC="32a20856bf9f49aca3e8ee5140f03309",fD="_水平线",fE="358acd74da7f46eeb6c187f3e572f3e7",fF="_形状2",fG="54dff7eb669f455990807b80b8910708",fH="_复选框",fI="f3015c53b4f74e8f8640dcc5a6cae88c",fJ="duplicateStyles",fK="4b7bfc596114427989e10bb0b557d0ce",fL="47641f9a00ac465095d6b672bbdffef6",fM="44157808f2934100b68f2394a66b2bba",fN="a828a99012434d00889eb5cedf7c98a3",fO="c7a479b38f094c0e9715002c7a3f272d",fP="eff044fe6497434a8c5f89f769ddde3b",fQ="619b2148ccc1497285562264d51992f9",fR="f732fe31b2114f2a923fe22e117d5511",fS="0882bfcd7d11450d85d157758311dca5",fT="a8350516c1f14dacb6ede5ed0f0c3703",fU="9bc82a125be84b5ca7c1f4a68318788d",fV="e8b3063fd7c548aebe2a622af44adb4b",fW="5fad4219dd034d5d9df7ca438321a937",fX="e4adcb2e59ed408f9df282d5ac6a5959";
return _creator();
})());