body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1200px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u19600_div {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u19600 {
  position:absolute;
  left:300px;
  top:15px;
  width:150px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u19601 {
  position:absolute;
  left:2px;
  top:6px;
  width:146px;
  word-wrap:break-word;
}
#u19602_div {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u19602 {
  position:absolute;
  left:550px;
  top:15px;
  width:150px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u19603 {
  position:absolute;
  left:2px;
  top:6px;
  width:146px;
  word-wrap:break-word;
}
#u19604_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:1501px;
}
#u19604 {
  position:absolute;
  left:375px;
  top:45px;
  width:1px;
  height:1500px;
}
#u19605 {
  position:absolute;
  left:2px;
  top:742px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19606_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:1501px;
}
#u19606 {
  position:absolute;
  left:625px;
  top:45px;
  width:1px;
  height:1500px;
}
#u19607 {
  position:absolute;
  left:2px;
  top:742px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19608_div {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u19608 {
  position:absolute;
  left:800px;
  top:15px;
  width:150px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u19609 {
  position:absolute;
  left:2px;
  top:6px;
  width:146px;
  word-wrap:break-word;
}
#u19610_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:1501px;
}
#u19610 {
  position:absolute;
  left:875px;
  top:45px;
  width:1px;
  height:1500px;
}
#u19611 {
  position:absolute;
  left:2px;
  top:742px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19612_div {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u19612 {
  position:absolute;
  left:50px;
  top:15px;
  width:150px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u19613 {
  position:absolute;
  left:2px;
  top:6px;
  width:146px;
  word-wrap:break-word;
}
#u19614_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:1501px;
}
#u19614 {
  position:absolute;
  left:124px;
  top:45px;
  width:1px;
  height:1500px;
}
#u19615 {
  position:absolute;
  left:2px;
  top:742px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19616_div {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:86px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u19616 {
  position:absolute;
  left:115px;
  top:100px;
  width:20px;
  height:86px;
}
#u19617 {
  position:absolute;
  left:2px;
  top:35px;
  width:16px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19618_img {
  position:absolute;
  left:0px;
  top:-9px;
  width:239px;
  height:19px;
}
#u19618p000 {
  position:absolute;
  left:-2px;
  top:-1px;
  width:234px;
  height:4px;
}
#u19618p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19618p001 {
  position:absolute;
  left:216px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19618p001_img {
  position:absolute;
  left:-1px;
  top:0px;
  width:22px;
  height:20px;
}
#u19618.compound {
  width:0px;
  height:0px;
}
#u19618 {
  position:absolute;
  left:135px;
  top:130px;
  width:230px;
  height:1px;
}
#u19619 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19620_div {
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19620 {
  position:absolute;
  left:145px;
  top:105px;
  width:66px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19621 {
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  white-space:nowrap;
}
#u19622_div {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:296px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u19622 {
  position:absolute;
  left:615px;
  top:184px;
  width:20px;
  height:296px;
}
#u19623 {
  position:absolute;
  left:2px;
  top:140px;
  width:16px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19624_img {
  position:absolute;
  left:0px;
  top:-9px;
  width:239px;
  height:19px;
}
#u19624p000 {
  position:absolute;
  left:-2px;
  top:-1px;
  width:234px;
  height:4px;
}
#u19624p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19624p001 {
  position:absolute;
  left:216px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19624p001_img {
  position:absolute;
  left:-1px;
  top:0px;
  width:22px;
  height:20px;
}
#u19624.compound {
  width:0px;
  height:0px;
}
#u19624 {
  position:absolute;
  left:385px;
  top:190px;
  width:230px;
  height:1px;
}
#u19625 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19626_div {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19626 {
  position:absolute;
  left:395px;
  top:150px;
  width:200px;
  height:36px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19627 {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  word-wrap:break-word;
}
#u19628_div {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:252px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u19628 {
  position:absolute;
  left:865px;
  top:228px;
  width:20px;
  height:252px;
}
#u19629 {
  position:absolute;
  left:2px;
  top:118px;
  width:16px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19630_img {
  position:absolute;
  left:0px;
  top:-9px;
  width:239px;
  height:19px;
}
#u19630p000 {
  position:absolute;
  left:-2px;
  top:-1px;
  width:234px;
  height:4px;
}
#u19630p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19630p001 {
  position:absolute;
  left:216px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19630p001_img {
  position:absolute;
  left:-1px;
  top:0px;
  width:22px;
  height:20px;
}
#u19630.compound {
  width:0px;
  height:0px;
}
#u19630 {
  position:absolute;
  left:635px;
  top:250px;
  width:230px;
  height:1px;
}
#u19631 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19632_div {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19632 {
  position:absolute;
  left:645px;
  top:210px;
  width:200px;
  height:36px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19633 {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  word-wrap:break-word;
}
#u19634_div {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:358px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u19634 {
  position:absolute;
  left:365px;
  top:550px;
  width:20px;
  height:358px;
}
#u19635 {
  position:absolute;
  left:2px;
  top:171px;
  width:16px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19636_div {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u19636 {
  position:absolute;
  left:1050px;
  top:15px;
  width:150px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u19637 {
  position:absolute;
  left:2px;
  top:6px;
  width:146px;
  word-wrap:break-word;
}
#u19638_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:1501px;
}
#u19638 {
  position:absolute;
  left:1125px;
  top:45px;
  width:1px;
  height:1500px;
}
#u19639 {
  position:absolute;
  left:2px;
  top:742px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19640_div {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:252px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u19640 {
  position:absolute;
  left:1115px;
  top:228px;
  width:20px;
  height:252px;
}
#u19641 {
  position:absolute;
  left:2px;
  top:118px;
  width:16px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19642_img {
  position:absolute;
  left:0px;
  top:-9px;
  width:239px;
  height:19px;
}
#u19642p000 {
  position:absolute;
  left:-2px;
  top:-1px;
  width:234px;
  height:4px;
}
#u19642p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19642p001 {
  position:absolute;
  left:216px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19642p001_img {
  position:absolute;
  left:-1px;
  top:0px;
  width:22px;
  height:20px;
}
#u19642.compound {
  width:0px;
  height:0px;
}
#u19642 {
  position:absolute;
  left:885px;
  top:250px;
  width:230px;
  height:1px;
}
#u19643 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19644_div {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19644 {
  position:absolute;
  left:895px;
  top:210px;
  width:200px;
  height:36px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19645 {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  word-wrap:break-word;
}
#u19646_img {
  position:absolute;
  left:-9px;
  top:-9px;
  width:240px;
  height:19px;
}
#u19646p000 {
  position:absolute;
  left:-2px;
  top:-1px;
  width:234px;
  height:4px;
}
#u19646p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19646p001 {
  position:absolute;
  left:-7px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19646p001_img {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u19646.compound {
  width:0px;
  height:0px;
}
#u19646 {
  position:absolute;
  left:385px;
  top:230px;
  width:230px;
  height:1px;
}
#u19647 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19648_div {
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19648 {
  position:absolute;
  left:475px;
  top:240px;
  width:131px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19649 {
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  white-space:nowrap;
}
#u19650_img {
  position:absolute;
  left:0px;
  top:-9px;
  width:239px;
  height:19px;
}
#u19650p000 {
  position:absolute;
  left:-2px;
  top:-2px;
  width:234px;
  height:4px;
}
#u19650p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19650p001 {
  position:absolute;
  left:216px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19650p001_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:22px;
  height:22px;
}
#u19650.compound {
  width:0px;
  height:0px;
}
#u19650 {
  position:absolute;
  left:385px;
  top:305px;
  width:230px;
  height:1px;
}
#u19651 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19652_div {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  color:#FF0000;
}
#u19652 {
  position:absolute;
  left:395px;
  top:280px;
  width:200px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  color:#FF0000;
}
#u19653 {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  word-wrap:break-word;
}
#u19654_div {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:198px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u19654 {
  position:absolute;
  left:615px;
  top:710px;
  width:20px;
  height:198px;
}
#u19655 {
  position:absolute;
  left:2px;
  top:91px;
  width:16px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19656_div {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:350px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u19656 {
  position:absolute;
  left:365px;
  top:130px;
  width:20px;
  height:350px;
}
#u19657 {
  position:absolute;
  left:2px;
  top:167px;
  width:16px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19658_img {
  position:absolute;
  left:-9px;
  top:-9px;
  width:240px;
  height:19px;
}
#u19658p000 {
  position:absolute;
  left:-2px;
  top:-1px;
  width:234px;
  height:4px;
}
#u19658p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19658p001 {
  position:absolute;
  left:-7px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19658p001_img {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u19658.compound {
  width:0px;
  height:0px;
}
#u19658 {
  position:absolute;
  left:885px;
  top:340px;
  width:230px;
  height:1px;
}
#u19659 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19660_div {
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19660 {
  position:absolute;
  left:1025px;
  top:345px;
  width:79px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19661 {
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  white-space:nowrap;
}
#u19662_img {
  position:absolute;
  left:-9px;
  top:-9px;
  width:240px;
  height:19px;
}
#u19662p000 {
  position:absolute;
  left:-2px;
  top:-1px;
  width:234px;
  height:4px;
}
#u19662p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19662p001 {
  position:absolute;
  left:-7px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19662p001_img {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u19662.compound {
  width:0px;
  height:0px;
}
#u19662 {
  position:absolute;
  left:635px;
  top:340px;
  width:230px;
  height:1px;
}
#u19663 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19664_div {
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19664 {
  position:absolute;
  left:775px;
  top:345px;
  width:79px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19665 {
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  white-space:nowrap;
}
#u19666_img {
  position:absolute;
  left:-9px;
  top:-9px;
  width:240px;
  height:19px;
}
#u19666p000 {
  position:absolute;
  left:-2px;
  top:-1px;
  width:234px;
  height:4px;
}
#u19666p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19666p001 {
  position:absolute;
  left:-7px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19666p001_img {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u19666.compound {
  width:0px;
  height:0px;
}
#u19666 {
  position:absolute;
  left:385px;
  top:340px;
  width:230px;
  height:1px;
}
#u19667 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19668_div {
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19668 {
  position:absolute;
  left:525px;
  top:345px;
  width:79px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19669 {
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  white-space:nowrap;
}
#u19670_div {
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19670 {
  position:absolute;
  left:420px;
  top:400px;
  width:126px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19671 {
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  white-space:nowrap;
}
#u19672 {
  position:absolute;
  left:385px;
  top:385px;
  width:0px;
  height:0px;
}
#u19672_seg0 {
  position:absolute;
  left:0px;
  top:-4px;
  width:32px;
  height:8px;
}
#u19672_seg1 {
  position:absolute;
  left:24px;
  top:-4px;
  width:8px;
  height:60px;
}
#u19672_seg2 {
  position:absolute;
  left:1px;
  top:48px;
  width:31px;
  height:8px;
}
#u19672_seg3 {
  position:absolute;
  left:-5px;
  top:43px;
  width:19px;
  height:18px;
}
#u19673 {
  position:absolute;
  left:-22px;
  top:18px;
  width:100px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19674_div {
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19674 {
  position:absolute;
  left:420px;
  top:585px;
  width:195px;
  height:36px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19675 {
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  word-wrap:break-word;
}
#u19676 {
  position:absolute;
  left:385px;
  top:572px;
  width:0px;
  height:0px;
}
#u19676_seg0 {
  position:absolute;
  left:0px;
  top:-4px;
  width:32px;
  height:8px;
}
#u19676_seg1 {
  position:absolute;
  left:24px;
  top:-4px;
  width:8px;
  height:60px;
}
#u19676_seg2 {
  position:absolute;
  left:1px;
  top:48px;
  width:31px;
  height:8px;
}
#u19676_seg3 {
  position:absolute;
  left:-5px;
  top:43px;
  width:19px;
  height:18px;
}
#u19677 {
  position:absolute;
  left:-22px;
  top:18px;
  width:100px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19678_img {
  position:absolute;
  left:0px;
  top:-9px;
  width:239px;
  height:19px;
}
#u19678p000 {
  position:absolute;
  left:-2px;
  top:-1px;
  width:234px;
  height:4px;
}
#u19678p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19678p001 {
  position:absolute;
  left:216px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19678p001_img {
  position:absolute;
  left:-1px;
  top:0px;
  width:22px;
  height:20px;
}
#u19678.compound {
  width:0px;
  height:0px;
}
#u19678 {
  position:absolute;
  left:385px;
  top:710px;
  width:230px;
  height:1px;
}
#u19679 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19680_div {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19680 {
  position:absolute;
  left:395px;
  top:670px;
  width:200px;
  height:36px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19681 {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  word-wrap:break-word;
}
#u19682_div {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u19682 {
  position:absolute;
  left:865px;
  top:765px;
  width:20px;
  height:60px;
}
#u19683 {
  position:absolute;
  left:2px;
  top:22px;
  width:16px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19684_div {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u19684 {
  position:absolute;
  left:1115px;
  top:765px;
  width:20px;
  height:60px;
}
#u19685 {
  position:absolute;
  left:2px;
  top:22px;
  width:16px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19686_img {
  position:absolute;
  left:0px;
  top:-9px;
  width:239px;
  height:19px;
}
#u19686p000 {
  position:absolute;
  left:-2px;
  top:-2px;
  width:234px;
  height:4px;
}
#u19686p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19686p001 {
  position:absolute;
  left:216px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19686p001_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:22px;
  height:22px;
}
#u19686.compound {
  width:0px;
  height:0px;
}
#u19686 {
  position:absolute;
  left:385px;
  top:765px;
  width:230px;
  height:1px;
}
#u19687 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19688_div {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19688 {
  position:absolute;
  left:395px;
  top:740px;
  width:200px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19689 {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  word-wrap:break-word;
}
#u19690_img {
  position:absolute;
  left:0px;
  top:-9px;
  width:239px;
  height:19px;
}
#u19690p000 {
  position:absolute;
  left:-2px;
  top:-2px;
  width:234px;
  height:4px;
}
#u19690p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19690p001 {
  position:absolute;
  left:216px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19690p001_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:22px;
  height:22px;
}
#u19690.compound {
  width:0px;
  height:0px;
}
#u19690 {
  position:absolute;
  left:635px;
  top:765px;
  width:230px;
  height:1px;
}
#u19691 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19692_div {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19692 {
  position:absolute;
  left:645px;
  top:740px;
  width:200px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19693 {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  word-wrap:break-word;
}
#u19694_img {
  position:absolute;
  left:0px;
  top:-9px;
  width:239px;
  height:19px;
}
#u19694p000 {
  position:absolute;
  left:-2px;
  top:-2px;
  width:234px;
  height:4px;
}
#u19694p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19694p001 {
  position:absolute;
  left:216px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19694p001_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:22px;
  height:22px;
}
#u19694.compound {
  width:0px;
  height:0px;
}
#u19694 {
  position:absolute;
  left:885px;
  top:765px;
  width:230px;
  height:1px;
}
#u19695 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19696_div {
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19696 {
  position:absolute;
  left:895px;
  top:725px;
  width:220px;
  height:36px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19697 {
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  word-wrap:break-word;
}
#u19698_img {
  position:absolute;
  left:-9px;
  top:-9px;
  width:240px;
  height:19px;
}
#u19698p000 {
  position:absolute;
  left:-2px;
  top:-1px;
  width:234px;
  height:4px;
}
#u19698p000_img {
  position:absolute;
  left:-1px;
  top:-1px;
  width:236px;
  height:6px;
}
#u19698p001 {
  position:absolute;
  left:-7px;
  top:-10px;
  width:20px;
  height:20px;
}
#u19698p001_img {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u19698.compound {
  width:0px;
  height:0px;
}
#u19698 {
  position:absolute;
  left:385px;
  top:802px;
  width:230px;
  height:1px;
}
#u19699 {
  position:absolute;
  left:2px;
  top:-8px;
  width:226px;
  visibility:hidden;
  word-wrap:break-word;
}
#u19700_div {
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19700 {
  position:absolute;
  left:510px;
  top:807px;
  width:92px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19701 {
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  white-space:nowrap;
}
#u19702_div {
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19702 {
  position:absolute;
  left:420px;
  top:866px;
  width:195px;
  height:18px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
#u19703 {
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  word-wrap:break-word;
}
#u19704 {
  position:absolute;
  left:385px;
  top:853px;
  width:0px;
  height:0px;
}
#u19704_seg0 {
  position:absolute;
  left:0px;
  top:-4px;
  width:32px;
  height:8px;
}
#u19704_seg1 {
  position:absolute;
  left:24px;
  top:-4px;
  width:8px;
  height:60px;
}
#u19704_seg2 {
  position:absolute;
  left:1px;
  top:48px;
  width:31px;
  height:8px;
}
#u19704_seg3 {
  position:absolute;
  left:-5px;
  top:43px;
  width:19px;
  height:18px;
}
#u19705 {
  position:absolute;
  left:-22px;
  top:18px;
  width:100px;
  visibility:hidden;
  word-wrap:break-word;
}
