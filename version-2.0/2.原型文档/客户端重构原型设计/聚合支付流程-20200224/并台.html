<!DOCTYPE html>
<html>
  <head>
    <title>并台</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/并台/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/并台/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1075" class="ax_default box_1">
        <div id="u1075_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1076" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 区域导航条 (组合) -->
      <div id="u1077" class="ax_default" data-label="区域导航条" data-width="995" data-height="70">

        <!-- Unnamed (矩形) -->
        <div id="u1078" class="ax_default box_3">
          <div id="u1078_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1079" class="text" style="display:none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1080" class="ax_default _二级标题">
          <div id="u1080_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1081" class="text">
            <p><span>全部</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1082" class="ax_default _二级标题">
          <div id="u1082_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1083" class="text">
            <p><span>大厅</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1084" class="ax_default _二级标题">
          <div id="u1084_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1085" class="text">
            <p><span>包间</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1086" class="ax_default _二级标题">
          <div id="u1086_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1087" class="text">
            <p><span>二楼</span></p>
          </div>
        </div>
      </div>

      <!-- 占用未点餐 (组合) -->
      <div id="u1088" class="ax_default" data-label="占用未点餐" data-width="370" data-height="766">

        <!-- 框架 (组合) -->
        <div id="u1089" class="ax_default" data-label="框架" data-width="369" data-height="766">

          <!-- Unnamed (矩形) -->
          <div id="u1090" class="ax_default box_2">
            <div id="u1090_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1091" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1092" class="ax_default box_2">
            <div id="u1092_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1093" class="text">
              <p><span>确认</span></p>
            </div>
          </div>
        </div>

        <!-- 抬头 (组合) -->
        <div id="u1094" class="ax_default" data-label="抬头" data-width="370" data-height="80">

          <!-- Unnamed (矩形) -->
          <div id="u1095" class="ax_default box_2">
            <div id="u1095_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1096" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u1097" class="ax_default icon">
            <img id="u1097_img" class="img " src="images/转台/返回符号_u918.png"/>
            <!-- Unnamed () -->
            <div id="u1098" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1099" class="ax_default _二级标题">
            <div id="u1099_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1100" class="text">
              <p><span>并台</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- 占用 (组合) -->
      <div id="u1101" class="ax_default" data-label="占用" data-width="160" data-height="125">

        <!-- Unnamed (矩形) -->
        <div id="u1102" class="ax_default box_1">
          <div id="u1102_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1103" class="text" style="display:none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1104" class="ax_default _二级标题">
          <div id="u1104_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1105" class="text">
            <p><span>C002</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1106" class="ax_default _三级标题">
          <div id="u1106_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1107" class="text">
            <p><span>¥120</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1108" class="ax_default _三级标题">
          <div id="u1108_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1109" class="text">
            <p><span>5/8</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1110" class="ax_default _三级标题">
          <div id="u1110_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1111" class="text">
            <p><span>10.2h</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1112" class="ax_default _三级标题">
        <div id="u1112_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1113" class="text">
          <p><span>主桌</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1114" class="ax_default _三级标题">
        <div id="u1114_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1115" class="text">
          <p><span>并桌</span></p>
        </div>
      </div>

      <!-- 桌位列表 (组合) -->
      <div id="u1116" class="ax_default" data-label="桌位列表" data-width="880" data-height="425">

        <!-- 空闲 (组合) -->
        <div id="u1117" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1118" class="ax_default box_1">
            <div id="u1118_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1119" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1120" class="ax_default _二级标题">
            <div id="u1120_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1121" class="text">
              <p><span>A001</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1122" class="ax_default _三级标题">
            <div id="u1122_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1123" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1124" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1125" class="ax_default box_1">
            <div id="u1125_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1126" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1127" class="ax_default _二级标题">
            <div id="u1127_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1128" class="text">
              <p><span>A002</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1129" class="ax_default _三级标题">
            <div id="u1129_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1130" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1131" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1132" class="ax_default box_1">
            <div id="u1132_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1133" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1134" class="ax_default _二级标题">
            <div id="u1134_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1135" class="text">
              <p><span>A004</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1136" class="ax_default _三级标题">
            <div id="u1136_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1137" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1138" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1139" class="ax_default box_1">
            <div id="u1139_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1140" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1141" class="ax_default _二级标题">
            <div id="u1141_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1142" class="text">
              <p><span>C007</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1143" class="ax_default _三级标题">
            <div id="u1143_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1144" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1145" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1146" class="ax_default box_1">
            <div id="u1146_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1147" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1148" class="ax_default _二级标题">
            <div id="u1148_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1149" class="text">
              <p><span>C008</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1150" class="ax_default _三级标题">
            <div id="u1150_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1151" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1152" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1153" class="ax_default box_1">
            <div id="u1153_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1154" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1155" class="ax_default _二级标题">
            <div id="u1155_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1156" class="text">
              <p><span>A005</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1157" class="ax_default _三级标题">
            <div id="u1157_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1158" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1159" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1160" class="ax_default box_1">
            <div id="u1160_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1161" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1162" class="ax_default _二级标题">
            <div id="u1162_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1163" class="text">
              <p><span>C002</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1164" class="ax_default _三级标题">
            <div id="u1164_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1165" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1166" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1167" class="ax_default box_1">
            <div id="u1167_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1168" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1169" class="ax_default _二级标题">
            <div id="u1169_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1170" class="text">
              <p><span>C004</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1171" class="ax_default _三级标题">
            <div id="u1171_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1172" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1173" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1174" class="ax_default box_1">
            <div id="u1174_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1175" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1176" class="ax_default _二级标题">
            <div id="u1176_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1177" class="text">
              <p><span>C006</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1178" class="ax_default _三级标题">
            <div id="u1178_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1179" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 占用 (组合) -->
        <div id="u1180" class="ax_default" data-label="占用" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1181" class="ax_default box_1">
            <div id="u1181_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1182" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1183" class="ax_default _二级标题">
            <div id="u1183_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1184" class="text">
              <p><span>A003</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1185" class="ax_default _三级标题">
            <div id="u1185_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1186" class="text">
              <p><span>¥120</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1187" class="ax_default _三级标题">
            <div id="u1187_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1188" class="text">
              <p><span>5/8</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1189" class="ax_default _三级标题">
            <div id="u1189_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1190" class="text">
              <p><span>10.2h</span></p>
            </div>
          </div>
        </div>

        <!-- 并台占用 (组合) -->
        <div id="u1191" class="ax_default" data-label="并台占用" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1192" class="ax_default box_1">
            <div id="u1192_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1193" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1194" class="ax_default _二级标题">
            <div id="u1194_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1195" class="text">
              <p><span>C003</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1196" class="ax_default _三级标题">
            <div id="u1196_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1197" class="text">
              <p><span>¥0</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1198" class="ax_default _三级标题">
            <div id="u1198_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1199" class="text">
              <p><span>5/8</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1200" class="ax_default _三级标题">
            <div id="u1200_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1201" class="text">
              <p><span>1min</span></p>
            </div>
          </div>
        </div>

        <!-- 占用 (组合) -->
        <div id="u1202" class="ax_default" data-label="占用" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1203" class="ax_default box_1">
            <div id="u1203_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1204" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1205" class="ax_default _二级标题">
            <div id="u1205_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1206" class="text">
              <p><span>C005</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1207" class="ax_default _三级标题">
            <div id="u1207_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1208" class="text">
              <p><span>¥120</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1209" class="ax_default _三级标题">
            <div id="u1209_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1210" class="text">
              <p><span>5/8</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1211" class="ax_default _三级标题">
            <div id="u1211_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1212" class="text">
              <p><span>10.2h</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (水平线) -->
      <div id="u1213" class="ax_default line">
        <img id="u1213_img" class="img " src="images/并台/u1213.png"/>
        <!-- Unnamed () -->
        <div id="u1214" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1215" class="ax_default label">
        <div id="u1215_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1216" class="text">
          <p><span>点击返回到桌位操作页面，依然展开桌位操作侧边栏和更多操作工具栏</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1217" class="ax_default label">
        <div id="u1217_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1218" class="text">
          <p><span>确认按钮操作说明：</span></p><p><span>1，未选被并台桌位点击时，提示“请选择并台桌位”</span></p><p><span>2，已选择被并台桌位点击时，关联原桌位订单与被选择并台的所有桌位订单，如果被关联桌位是空闲状态，则并台成功后自动开台空闲桌位（默认开台人数为桌位标准人数）并关联为并台状态，提示“桌位并台成功”，并返回到桌位列表页面</span></p><p><span>3，如果被选择的桌位或发起并台的桌位，有桌位状态被更改时（不再符合并台条件），弹出并台失败提示，移除并台失败的桌位，保留该页面并刷新被并台桌位列表页面显示</span></p><p><span>4，第一次并台后，再由已并台桌位发起第二次并台时，需要显示上次已被并台的桌位在已选桌位列表，但是不能取消上一次已被并台的桌位的并台状态，该页面只能增加并台桌位，不能撤销已并台桌位</span></p>
        </div>
      </div>

      <!-- Unnamed (连接线) -->
      <div id="u1219" class="ax_default _连接线">
        <img id="u1219_seg0" class="img " src="images/并台/u1219_seg0.png" alt="u1219_seg0"/>
        <img id="u1219_seg1" class="img " src="images/并台/u1219_seg1.png" alt="u1219_seg1"/>
        <img id="u1219_seg2" class="img " src="images/并台/u1219_seg2.png" alt="u1219_seg2"/>
        <!-- Unnamed () -->
        <div id="u1220" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (连接线) -->
      <div id="u1221" class="ax_default _连接线">
        <img id="u1221_seg0" class="img " src="images/并台/u1221_seg0.png" alt="u1221_seg0"/>
        <img id="u1221_seg1" class="img " src="images/并台/u1221_seg1.png" alt="u1221_seg1"/>
        <img id="u1221_seg2" class="img " src="images/并台/u1219_seg2.png" alt="u1221_seg2"/>
        <!-- Unnamed () -->
        <div id="u1222" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1223" class="ax_default label">
        <div id="u1223_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1224" class="text">
          <p><span>被并台桌位列表显示说明：</span></p><p><span>1，此处可显示空闲状态/占用非并台状态/预订未锁定状态中的桌位（待清台状态/预订已锁定状态/占用并台状态3种不显示，占用锁定状态属于特殊状态，被锁时不显示，释放后要显示）</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
