<!DOCTYPE html>
<html>
  <head>
    <title>并台-已选桌</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/并台-已选桌/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/并台-已选桌/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1225" class="ax_default box_1">
        <div id="u1225_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1226" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 区域导航条 (组合) -->
      <div id="u1227" class="ax_default" data-label="区域导航条" data-width="995" data-height="70">

        <!-- Unnamed (矩形) -->
        <div id="u1228" class="ax_default box_3">
          <div id="u1228_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1229" class="text" style="display:none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1230" class="ax_default _二级标题">
          <div id="u1230_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1231" class="text">
            <p><span>全部</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1232" class="ax_default _二级标题">
          <div id="u1232_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1233" class="text">
            <p><span>大厅</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1234" class="ax_default _二级标题">
          <div id="u1234_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1235" class="text">
            <p><span>包间</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1236" class="ax_default _二级标题">
          <div id="u1236_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1237" class="text">
            <p><span>二楼</span></p>
          </div>
        </div>
      </div>

      <!-- 桌位列表 (组合) -->
      <div id="u1238" class="ax_default" data-label="桌位列表" data-width="890" data-height="434">

        <!-- 选中 (矩形) -->
        <div id="u1239" class="ax_default box_1" data-label="选中">
          <div id="u1239_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1240" class="text" style="display:none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- 选中 (矩形) -->
        <div id="u1241" class="ax_default box_1" data-label="选中">
          <div id="u1241_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1242" class="text" style="display:none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1243" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1244" class="ax_default box_1">
            <div id="u1244_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1245" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1246" class="ax_default _二级标题">
            <div id="u1246_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1247" class="text">
              <p><span>A001</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1248" class="ax_default _三级标题">
            <div id="u1248_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1249" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1250" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1251" class="ax_default box_1">
            <div id="u1251_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1252" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1253" class="ax_default _二级标题">
            <div id="u1253_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1254" class="text">
              <p><span>A002</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1255" class="ax_default _三级标题">
            <div id="u1255_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1256" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1257" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1258" class="ax_default box_1">
            <div id="u1258_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1259" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1260" class="ax_default _二级标题">
            <div id="u1260_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1261" class="text">
              <p><span>A004</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1262" class="ax_default _三级标题">
            <div id="u1262_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1263" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1264" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1265" class="ax_default box_1">
            <div id="u1265_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1266" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1267" class="ax_default _二级标题">
            <div id="u1267_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1268" class="text">
              <p><span>C007</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1269" class="ax_default _三级标题">
            <div id="u1269_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1270" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1271" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1272" class="ax_default box_1">
            <div id="u1272_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1273" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1274" class="ax_default _二级标题">
            <div id="u1274_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1275" class="text">
              <p><span>C008</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1276" class="ax_default _三级标题">
            <div id="u1276_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1277" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1278" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1279" class="ax_default box_1">
            <div id="u1279_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1280" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1281" class="ax_default _二级标题">
            <div id="u1281_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1282" class="text">
              <p><span>A005</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1283" class="ax_default _三级标题">
            <div id="u1283_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1284" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1285" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1286" class="ax_default box_1">
            <div id="u1286_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1287" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1288" class="ax_default _二级标题">
            <div id="u1288_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1289" class="text">
              <p><span>C002</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1290" class="ax_default _三级标题">
            <div id="u1290_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1291" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1292" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1293" class="ax_default box_1">
            <div id="u1293_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1294" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1295" class="ax_default _二级标题">
            <div id="u1295_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1296" class="text">
              <p><span>C004</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1297" class="ax_default _三级标题">
            <div id="u1297_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1298" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 空闲 (组合) -->
        <div id="u1299" class="ax_default" data-label="空闲" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1300" class="ax_default box_1">
            <div id="u1300_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1301" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1302" class="ax_default _二级标题">
            <div id="u1302_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1303" class="text">
              <p><span>C006</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1304" class="ax_default _三级标题">
            <div id="u1304_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1305" class="text">
              <p><span>空闲</span></p>
            </div>
          </div>
        </div>

        <!-- 占用 (组合) -->
        <div id="u1306" class="ax_default" data-label="占用" data-width="180" data-height="145">

          <!-- 选中 (矩形) -->
          <div id="u1307" class="ax_default box_1" data-label="选中">
            <div id="u1307_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1308" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1309" class="ax_default box_1">
            <div id="u1309_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1310" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1311" class="ax_default _二级标题">
            <div id="u1311_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1312" class="text">
              <p><span>A003</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1313" class="ax_default _三级标题">
            <div id="u1313_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1314" class="text">
              <p><span>¥120</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1315" class="ax_default _三级标题">
            <div id="u1315_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1316" class="text">
              <p><span>5/8</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1317" class="ax_default _三级标题">
            <div id="u1317_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1318" class="text">
              <p><span>10.2h</span></p>
            </div>
          </div>
        </div>

        <!-- 并台占用 (组合) -->
        <div id="u1319" class="ax_default" data-label="并台占用" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1320" class="ax_default box_1">
            <div id="u1320_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1321" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1322" class="ax_default _二级标题">
            <div id="u1322_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1323" class="text">
              <p><span>C003</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1324" class="ax_default _三级标题">
            <div id="u1324_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1325" class="text">
              <p><span>¥0</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1326" class="ax_default _三级标题">
            <div id="u1326_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1327" class="text">
              <p><span>5/8</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1328" class="ax_default _三级标题">
            <div id="u1328_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1329" class="text">
              <p><span>1min</span></p>
            </div>
          </div>
        </div>

        <!-- 占用 (组合) -->
        <div id="u1330" class="ax_default" data-label="占用" data-width="160" data-height="125">

          <!-- Unnamed (矩形) -->
          <div id="u1331" class="ax_default box_1">
            <div id="u1331_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1332" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1333" class="ax_default _二级标题">
            <div id="u1333_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1334" class="text">
              <p><span>C005</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1335" class="ax_default _三级标题">
            <div id="u1335_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1336" class="text">
              <p><span>¥120</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1337" class="ax_default _三级标题">
            <div id="u1337_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1338" class="text">
              <p><span>5/8</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1339" class="ax_default _三级标题">
            <div id="u1339_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1340" class="text">
              <p><span>10.2h</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- 占用未点餐 (组合) -->
      <div id="u1341" class="ax_default" data-label="占用未点餐" data-width="370" data-height="766">

        <!-- 框架 (组合) -->
        <div id="u1342" class="ax_default" data-label="框架" data-width="369" data-height="766">

          <!-- Unnamed (矩形) -->
          <div id="u1343" class="ax_default box_2">
            <div id="u1343_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1344" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1345" class="ax_default box_2">
            <div id="u1345_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1346" class="text">
              <p><span>确认</span></p>
            </div>
          </div>
        </div>

        <!-- 抬头 (组合) -->
        <div id="u1347" class="ax_default" data-label="抬头" data-width="370" data-height="80">

          <!-- Unnamed (矩形) -->
          <div id="u1348" class="ax_default box_2">
            <div id="u1348_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1349" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u1350" class="ax_default icon">
            <img id="u1350_img" class="img " src="images/转台/返回符号_u918.png"/>
            <!-- Unnamed () -->
            <div id="u1351" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1352" class="ax_default _二级标题">
            <div id="u1352_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u1353" class="text">
              <p><span>并台</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- 占用 (组合) -->
      <div id="u1354" class="ax_default" data-label="占用" data-width="160" data-height="125">

        <!-- Unnamed (矩形) -->
        <div id="u1355" class="ax_default box_1">
          <div id="u1355_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1356" class="text" style="display:none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1357" class="ax_default _二级标题">
          <div id="u1357_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1358" class="text">
            <p><span>C002</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1359" class="ax_default _三级标题">
          <div id="u1359_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1360" class="text">
            <p><span>¥120</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1361" class="ax_default _三级标题">
          <div id="u1361_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1362" class="text">
            <p><span>5/8</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1363" class="ax_default _三级标题">
          <div id="u1363_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1364" class="text">
            <p><span>10.2h</span></p>
          </div>
        </div>
      </div>

      <!-- 空闲 (组合) -->
      <div id="u1365" class="ax_default" data-label="空闲" data-width="330" data-height="60">

        <!-- Unnamed (矩形) -->
        <div id="u1366" class="ax_default box_1">
          <div id="u1366_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1367" class="text">
            <p><span>&nbsp; A003</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1368" class="ax_default _三级标题">
        <div id="u1368_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1369" class="text">
          <p><span>主桌</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1370" class="ax_default _三级标题">
        <div id="u1370_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1371" class="text">
          <p><span>并桌</span></p>
        </div>
      </div>

      <!-- 空闲 (组合) -->
      <div id="u1372" class="ax_default" data-label="空闲" data-width="330" data-height="60">

        <!-- Unnamed (矩形) -->
        <div id="u1373" class="ax_default box_1">
          <div id="u1373_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1374" class="text">
            <p><span>&nbsp; A001</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u1375" class="ax_default icon">
        <img id="u1375_img" class="img " src="images/并台-已选桌/u1375.png"/>
        <!-- Unnamed () -->
        <div id="u1376" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1377" class="ax_default label">
        <div id="u1377_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1378" class="text">
          <p><span>点击返回到桌位操作页面，依然展开桌位操作侧边栏和更多操作工具栏</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1379" class="ax_default label">
        <div id="u1379_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1380" class="text">
          <p><span>确认按钮操作说明：</span></p><p><span>1，未选被并台桌位点击时，提示“请选择并台桌位”</span></p><p><span>2，已选择被并台桌位点击时，关联原桌位订单与被选择并台的所有桌位订单，如果被关联桌位是空闲状态，则并台成功后自动开台空闲桌位（默认开台人数为桌位标准人数）并关联为并台状态，提示“桌位并台成功”，并返回到桌位列表页面</span></p><p><span>3，如果被选择的桌位或发起并台的桌位，有桌位状态被更改时（不再符合并台条件），弹出并台失败提示，移除并台失败的桌位，保留该页面并刷新被并台桌位列表页面显示</span></p><p><span>4，第一次并台后，再由已并台桌位发起第二次并台时，需要显示上次已被并台的桌位在已选桌位列表，但是不能取消上一次已被并台的桌位的并台状态，该页面只能增加并台桌位，不能撤销已并台桌位</span></p><p><span>5，有结账支付成功记录，但是却结账失败的订单，不允许并台，也不允许被并台。操作并台时提示“订单支付中，不允许并台”</span></p>
        </div>
      </div>

      <!-- Unnamed (连接线) -->
      <div id="u1381" class="ax_default _连接线">
        <img id="u1381_seg0" class="img " src="images/并台/u1219_seg0.png" alt="u1381_seg0"/>
        <img id="u1381_seg1" class="img " src="images/并台/u1219_seg1.png" alt="u1381_seg1"/>
        <img id="u1381_seg2" class="img " src="images/并台/u1219_seg2.png" alt="u1381_seg2"/>
        <!-- Unnamed () -->
        <div id="u1382" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (连接线) -->
      <div id="u1383" class="ax_default _连接线">
        <img id="u1383_seg0" class="img " src="images/并台/u1221_seg0.png" alt="u1383_seg0"/>
        <img id="u1383_seg1" class="img " src="images/并台-已选桌/u1383_seg1.png" alt="u1383_seg1"/>
        <img id="u1383_seg2" class="img " src="images/并台/u1219_seg2.png" alt="u1383_seg2"/>
        <!-- Unnamed () -->
        <div id="u1384" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1385" class="ax_default label">
        <div id="u1385_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1386" class="text">
          <p><span>被并台桌位列表显示说明：</span></p><p><span>1，此处可显示空闲状态/占用非并台状态/预订未锁定状态中的桌位（待清台状态/预订已锁定状态/占用并台状态3种不显示，占用锁定状态属于特殊状态，被锁时不显示，释放后要显示）</span></p>
        </div>
      </div>

      <!-- 空闲 (组合) -->
      <div id="u1387" class="ax_default" data-label="空闲" data-width="330" data-height="60">

        <!-- Unnamed (矩形) -->
        <div id="u1388" class="ax_default box_1">
          <div id="u1388_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u1389" class="text">
            <p><span>&nbsp; C003</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u1390" class="ax_default icon">
        <img id="u1390_img" class="img " src="images/并台-已选桌/u1375.png"/>
        <!-- Unnamed () -->
        <div id="u1391" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>
    </div>
  </body>
</html>
