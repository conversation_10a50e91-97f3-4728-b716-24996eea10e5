<!DOCTYPE html>
<html>
  <head>
    <title>点餐-选择商品</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/点餐-选择商品/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/点餐-选择商品/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u5042" class="ax_default box_1">
        <div id="u5042_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u5043" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 菜品栏 (组合) -->
      <div id="u5044" class="ax_default" data-label="菜品栏" data-width="915" data-height="699">

        <!-- Unnamed (组合) -->
        <div id="u5045" class="ax_default" data-width="90" data-height="10">

          <!-- Unnamed (椭圆形) -->
          <div id="u5046" class="ax_default ellipse">
            <img id="u5046_img" class="img " src="images/点餐-选择商品/u5046.png"/>
            <!-- Unnamed () -->
            <div id="u5047" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (椭圆形) -->
          <div id="u5048" class="ax_default ellipse">
            <img id="u5048_img" class="img " src="images/点餐-选择商品/u5048.png"/>
            <!-- Unnamed () -->
            <div id="u5049" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (椭圆形) -->
          <div id="u5050" class="ax_default ellipse">
            <img id="u5050_img" class="img " src="images/点餐-选择商品/u5048.png"/>
            <!-- Unnamed () -->
            <div id="u5051" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (椭圆形) -->
          <div id="u5052" class="ax_default ellipse">
            <img id="u5052_img" class="img " src="images/点餐-选择商品/u5048.png"/>
            <!-- Unnamed () -->
            <div id="u5053" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (椭圆形) -->
          <div id="u5054" class="ax_default ellipse">
            <img id="u5054_img" class="img " src="images/点餐-选择商品/u5048.png"/>
            <!-- Unnamed () -->
            <div id="u5055" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>
        </div>

        <!-- 标题 (组合) -->
        <div id="u5056" class="ax_default" data-label="标题" data-width="915" data-height="79">

          <!-- Unnamed (矩形) -->
          <div id="u5057" class="ax_default box_3">
            <div id="u5057_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u5058" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- 搜索 (组合) -->
          <div id="u5059" class="ax_default" data-label="搜索" data-width="345" data-height="65">

            <!-- Unnamed (矩形) -->
            <div id="u5060" class="ax_default box_1">
              <div id="u5060_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5061" class="text">
                <p><span style="font-family:'ArialMT', 'Arial';">&nbsp; </span><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">搜索</span><span style="font-family:'ArialMT', 'Arial';">菜品名称/编码</span></p>
              </div>
            </div>

            <!-- Unnamed (形状) -->
            <div id="u5062" class="ax_default icon">
              <img id="u5062_img" class="img " src="images/下单/搜索图标_u4783.png"/>
              <!-- Unnamed () -->
              <div id="u5063" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>
          </div>
        </div>

        <!-- 分类列表 (组合) -->
        <div id="u5064" class="ax_default" data-label="分类列表" data-width="150" data-height="415">

          <!-- Unnamed (矩形) -->
          <div id="u5065" class="ax_default box_2">
            <div id="u5065_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u5066" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u5067" class="ax_default" data-width="150" data-height="80">

            <!-- Unnamed (矩形) -->
            <div id="u5068" class="ax_default box_2">
              <div id="u5068_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5069" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5070" class="ax_default _二级标题">
              <div id="u5070_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5071" class="text">
                <p><span>全部</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5072" class="ax_default _二级标题">
              <div id="u5072_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5073" class="text">
                <p><span>60</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u5074" class="ax_default" data-width="150" data-height="80">

            <!-- Unnamed (矩形) -->
            <div id="u5075" class="ax_default box_2">
              <div id="u5075_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5076" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5077" class="ax_default _二级标题">
              <div id="u5077_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5078" class="text">
                <p><span>热销</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5079" class="ax_default _二级标题">
              <div id="u5079_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5080" class="text">
                <p><span>12</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u5081" class="ax_default" data-width="150" data-height="80">

            <!-- Unnamed (矩形) -->
            <div id="u5082" class="ax_default box_2">
              <div id="u5082_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5083" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5084" class="ax_default _二级标题">
              <div id="u5084_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5085" class="text">
                <p><span>热菜</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5086" class="ax_default _二级标题">
              <div id="u5086_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5087" class="text">
                <p><span>20</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u5088" class="ax_default" data-width="150" data-height="80">

            <!-- Unnamed (矩形) -->
            <div id="u5089" class="ax_default box_2">
              <div id="u5089_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5090" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5091" class="ax_default _二级标题">
              <div id="u5091_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5092" class="text">
                <p><span>凉菜</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5093" class="ax_default _二级标题">
              <div id="u5093_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5094" class="text">
                <p><span>20</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u5095" class="ax_default" data-width="150" data-height="80">

            <!-- Unnamed (矩形) -->
            <div id="u5096" class="ax_default box_2">
              <div id="u5096_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5097" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5098" class="ax_default _二级标题">
              <div id="u5098_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5099" class="text">
                <p><span>酒水</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5100" class="ax_default _二级标题">
              <div id="u5100_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5101" class="text">
                <p><span>20</span></p>
              </div>
            </div>
          </div>
        </div>

        <!-- 菜品列表 (组合) -->
        <div id="u5102" class="ax_default" data-label="菜品列表" data-width="720" data-height="560">

          <!-- 规格菜品 (组合) -->
          <div id="u5103" class="ax_default" data-label="规格菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5104" class="ax_default box_1">
              <div id="u5104_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5105" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5106" class="ax_default _二级标题">
              <div id="u5106_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5107" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5108" class="ax_default _三级标题">
              <div id="u5108_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5109" class="text">
                <p><span>规格商品</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5110" class="ax_default _三级标题">
              <div id="u5110_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5111" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5112" class="ax_default box_2">
              <div id="u5112_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5113" class="text">
                <p><span>规格</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5114" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5115" class="ax_default box_1">
              <div id="u5115_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5116" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5117" class="ax_default _二级标题">
              <div id="u5117_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5118" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5119" class="ax_default _三级标题">
              <div id="u5119_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5120" class="text">
                <p><span>普通商品</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5121" class="ax_default _三级标题">
              <div id="u5121_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5122" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 套餐菜品 (组合) -->
          <div id="u5123" class="ax_default" data-label="套餐菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5124" class="ax_default box_1">
              <div id="u5124_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5125" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5126" class="ax_default _二级标题">
              <div id="u5126_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5127" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5128" class="ax_default _三级标题">
              <div id="u5128_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5129" class="text">
                <p><span>套餐商品</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5130" class="ax_default _三级标题">
              <div id="u5130_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5131" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5132" class="ax_default box_2">
              <div id="u5132_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5133" class="text">
                <p><span>套餐</span></p>
              </div>
            </div>
          </div>

          <!-- 称重菜品 (组合) -->
          <div id="u5134" class="ax_default" data-label="称重菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5135" class="ax_default box_1">
              <div id="u5135_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5136" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5137" class="ax_default _二级标题">
              <div id="u5137_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5138" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5139" class="ax_default _三级标题">
              <div id="u5139_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5140" class="text">
                <p><span>称重商品</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5141" class="ax_default _三级标题">
              <div id="u5141_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5142" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5143" class="ax_default box_2">
              <div id="u5143_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5144" class="text">
                <p><span>称重</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5145" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5146" class="ax_default box_1">
              <div id="u5146_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5147" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5148" class="ax_default _二级标题">
              <div id="u5148_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5149" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5150" class="ax_default _三级标题">
              <div id="u5150_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5151" class="text">
                <p><span>柠檬椰果益菌</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5152" class="ax_default _三级标题">
              <div id="u5152_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5153" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5154" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5155" class="ax_default box_1">
              <div id="u5155_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5156" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5157" class="ax_default _二级标题">
              <div id="u5157_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5158" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5159" class="ax_default _三级标题">
              <div id="u5159_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5160" class="text">
                <p><span>柠檬椰果养乐</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5161" class="ax_default _三级标题">
              <div id="u5161_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5162" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5163" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5164" class="ax_default box_1">
              <div id="u5164_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5165" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5166" class="ax_default _二级标题">
              <div id="u5166_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5167" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5168" class="ax_default _三级标题">
              <div id="u5168_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5169" class="text">
                <p><span>柠檬霸</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5170" class="ax_default _三级标题">
              <div id="u5170_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5171" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5172" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5173" class="ax_default box_1">
              <div id="u5173_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5174" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5175" class="ax_default _二级标题">
              <div id="u5175_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5176" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5177" class="ax_default _三级标题">
              <div id="u5177_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5178" class="text">
                <p><span>珍珠奶茶</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5179" class="ax_default _三级标题">
              <div id="u5179_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5180" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5181" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5182" class="ax_default box_1">
              <div id="u5182_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5183" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5184" class="ax_default _二级标题">
              <div id="u5184_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5185" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5186" class="ax_default _三级标题">
              <div id="u5186_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5187" class="text">
                <p><span>柠檬椰果益菌</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5188" class="ax_default _三级标题">
              <div id="u5188_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5189" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5190" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5191" class="ax_default box_1">
              <div id="u5191_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5192" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5193" class="ax_default _二级标题">
              <div id="u5193_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5194" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5195" class="ax_default _三级标题">
              <div id="u5195_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5196" class="text">
                <p><span>柠檬椰果养乐</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5197" class="ax_default _三级标题">
              <div id="u5197_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5198" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5199" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5200" class="ax_default box_1">
              <div id="u5200_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5201" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5202" class="ax_default _二级标题">
              <div id="u5202_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5203" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5204" class="ax_default _三级标题">
              <div id="u5204_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5205" class="text">
                <p><span>柠檬霸</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5206" class="ax_default _三级标题">
              <div id="u5206_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5207" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5208" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5209" class="ax_default box_1">
              <div id="u5209_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5210" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5211" class="ax_default _二级标题">
              <div id="u5211_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5212" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5213" class="ax_default _三级标题">
              <div id="u5213_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5214" class="text">
                <p><span>珍珠奶茶</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5215" class="ax_default _三级标题">
              <div id="u5215_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5216" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5217" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5218" class="ax_default box_1">
              <div id="u5218_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5219" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5220" class="ax_default _二级标题">
              <div id="u5220_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5221" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5222" class="ax_default _三级标题">
              <div id="u5222_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5223" class="text">
                <p><span>柠檬椰果益菌</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5224" class="ax_default _三级标题">
              <div id="u5224_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5225" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5226" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5227" class="ax_default box_1">
              <div id="u5227_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5228" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5229" class="ax_default _二级标题">
              <div id="u5229_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5230" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5231" class="ax_default _三级标题">
              <div id="u5231_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5232" class="text">
                <p><span>柠檬椰果养乐</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5233" class="ax_default _三级标题">
              <div id="u5233_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5234" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5235" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5236" class="ax_default box_1">
              <div id="u5236_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5237" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5238" class="ax_default _二级标题">
              <div id="u5238_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5239" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5240" class="ax_default _三级标题">
              <div id="u5240_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5241" class="text">
                <p><span>柠檬霸</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5242" class="ax_default _三级标题">
              <div id="u5242_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5243" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>

          <!-- 普通菜品 (组合) -->
          <div id="u5244" class="ax_default" data-label="普通菜品" data-width="165" data-height="125">

            <!-- Unnamed (矩形) -->
            <div id="u5245" class="ax_default box_1">
              <div id="u5245_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5246" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5247" class="ax_default _二级标题">
              <div id="u5247_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5248" class="text" style="display:none; visibility: hidden">
                <p><span></span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5249" class="ax_default _三级标题">
              <div id="u5249_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5250" class="text">
                <p><span>珍珠奶茶</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5251" class="ax_default _三级标题">
              <div id="u5251_div" class=""></div>
              <!-- Unnamed () -->
              <div id="u5252" class="text">
                <p><span>¥ 12</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5253" class="ax_default label">
        <div id="u5253_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u5254" class="text">
          <p><span>菜品搜索框：支持全拼搜索，拼音码搜索，中文名称搜索，菜品编码搜索，菜品简称搜索等；输入框支持输入中文，英文，数字等，不支持输入特殊符号</span></p>
        </div>
      </div>

      <!-- Unnamed (连接线) -->
      <div id="u5255" class="ax_default _连接线">
        <img id="u5255_seg0" class="img " src="images/点餐-选择商品/u5255_seg0.png" alt="u5255_seg0"/>
        <img id="u5255_seg1" class="img " src="images/修改人数/u3688_seg3.png" alt="u5255_seg1"/>
        <!-- Unnamed () -->
        <div id="u5256" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5257" class="ax_default label">
        <div id="u5257_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u5258" class="text">
          <p><span>分类顺序，根据商户后台的分类排序显示</span></p><p><span>切换分类后，仅显示当前分类下的商品信息</span></p><p><span><br></span></p><p><span>商品顺序，根据商户后台的商品排序显示</span></p>
        </div>
      </div>

      <!-- Unnamed (连接线) -->
      <div id="u5259" class="ax_default _连接线">
        <img id="u5259_seg0" class="img " src="images/修改人数/u3698_seg0.png" alt="u5259_seg0"/>
        <img id="u5259_seg1" class="img " src="images/修改人数/u3688_seg3.png" alt="u5259_seg1"/>
        <!-- Unnamed () -->
        <div id="u5260" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5261" class="ax_default label">
        <div id="u5261_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u5262" class="text">
          <p><span>热销分类，当前是根据后台商品属性为“推荐”菜品的商品汇总显示</span></p><p><span>后期会考虑根据商品的销售量来做汇总显示</span></p>
        </div>
      </div>

      <!-- Unnamed (连接线) -->
      <div id="u5263" class="ax_default _连接线">
        <img id="u5263_seg0" class="img " src="images/点餐-选择商品/u5263_seg0.png" alt="u5263_seg0"/>
        <img id="u5263_seg1" class="img " src="images/点餐-选择商品/u5263_seg1.png" alt="u5263_seg1"/>
        <img id="u5263_seg2" class="img " src="images/点餐-选择商品/u5263_seg2.png" alt="u5263_seg2"/>
        <img id="u5263_seg3" class="img " src="images/修改人数/u3688_seg3.png" alt="u5263_seg3"/>
        <!-- Unnamed () -->
        <div id="u5264" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 展示栏 (组合) -->
      <div id="u5265" class="ax_default" data-label="展示栏" data-width="449" data-height="766">

        <!-- Unnamed (矩形) -->
        <div id="u5266" class="ax_default box_2">
          <div id="u5266_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u5267" class="text" style="display:none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- 抬头 (组合) -->
        <div id="u5268" class="ax_default" data-label="抬头" data-width="449" data-height="80">

          <!-- Unnamed (矩形) -->
          <div id="u5269" class="ax_default box_3">
            <div id="u5269_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u5270" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u5271" class="ax_default icon">
            <img id="u5271_img" class="img " src="images/转台/返回符号_u918.png"/>
            <!-- Unnamed () -->
            <div id="u5272" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u5273" class="ax_default _二级标题">
            <div id="u5273_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u5274" class="text">
              <p><span>大厅- C001</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u5275" class="ax_default _二级标题">
            <div id="u5275_div" class=""></div>
            <!-- Unnamed () -->
            <div id="u5276" class="text">
              <p><span>5人用餐</span></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u5277" class="ax_default icon">
            <img id="u5277_img" class="img " src="images/点餐-选择商品/u5277.png"/>
            <!-- Unnamed () -->
            <div id="u5278" class="text" style="display:none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>
        </div>

        <!-- 已选菜品列表 (动态面板) -->
        <div id="u5279" class="ax_default" data-label="已选菜品列表">
          <div id="u5279_state0" class="panel_state" data-label="空白栏">
            <div id="u5279_state0_content" class="panel_state_content">
            </div>
          </div>
          <div id="u5279_state1" class="panel_state" data-label="全部菜品" style="display:none; visibility: hidden;">
            <div id="u5279_state1_content" class="panel_state_content">

              <!-- 套餐 (组合) -->
              <div id="u5280" class="ax_default" data-label="套餐" data-width="450" data-height="346">

                <!-- 未下单标记 (组合) -->
                <div id="u5281" class="ax_default" data-label="未下单标记" data-width="121" data-height="30">

                  <!-- Unnamed (矩形) -->
                  <div id="u5282" class="ax_default _三级标题">
                    <div id="u5282_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5283" class="text">
                      <p><span>未下单（3）</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (垂直线) -->
                  <div id="u5284" class="ax_default line">
                    <img id="u5284_img" class="img " src="images/点餐-选择商品/u5284.png"/>
                    <!-- Unnamed () -->
                    <div id="u5285" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>
                </div>

                <!-- 选中状态 (组合) -->
                <div id="u5286" class="ax_default" data-label="选中状态" data-width="450" data-height="306">

                  <!-- Unnamed (矩形) -->
                  <div id="u5287" class="ax_default box_2">
                    <div id="u5287_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5288" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5289" class="ax_default _二级标题">
                    <div id="u5289_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5290" class="text">
                      <p><span style="color:#F2F2F2;">[套]</span><span style="color:#FFFFFF;">酸辣土豆丝</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5291" class="ax_default _二级标题">
                    <div id="u5291_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5292" class="text">
                      <p><span>¥12.00</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5293" class="ax_default box_1">
                    <div id="u5293_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5294" class="text">
                      <p><span>即</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5295" class="ax_default box_2">
                    <div id="u5295_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5296" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5297" class="ax_default icon">
                    <img id="u5297_img" class="img " src="images/点餐-选择商品/u5297.png"/>
                    <!-- Unnamed () -->
                    <div id="u5298" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5299" class="ax_default icon">
                    <img id="u5299_img" class="img " src="images/点餐-选择商品/u5299.png"/>
                    <!-- Unnamed () -->
                    <div id="u5300" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5301" class="ax_default icon">
                    <img id="u5301_img" class="img " src="images/点餐-选择商品/u5301.png"/>
                    <!-- Unnamed () -->
                    <div id="u5302" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5303" class="ax_default box_1">
                    <div id="u5303_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5304" class="text">
                      <p><span>1</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5305" class="ax_default icon">
                    <img id="u5305_img" class="img " src="images/点餐-选择商品/u5305.png"/>
                    <!-- Unnamed () -->
                    <div id="u5306" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- 成分 (组合) -->
                  <div id="u5307" class="ax_default" data-label="成分" data-width="450" data-height="166">

                    <!-- Unnamed (矩形) -->
                    <div id="u5308" class="ax_default box_2">
                      <div id="u5308_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5309" class="text" style="display:none; visibility: hidden">
                        <p><span></span></p>
                      </div>
                    </div>

                    <!-- Unnamed (水平线) -->
                    <div id="u5310" class="ax_default line">
                      <img id="u5310_img" class="img " src="images/点餐-选择商品/u5310.png"/>
                      <!-- Unnamed () -->
                      <div id="u5311" class="text" style="display:none; visibility: hidden">
                        <p><span></span></p>
                      </div>
                    </div>

                    <!-- Unnamed (水平线) -->
                    <div id="u5312" class="ax_default line">
                      <img id="u5312_img" class="img " src="images/点餐-选择商品/u5310.png"/>
                      <!-- Unnamed () -->
                      <div id="u5313" class="text" style="display:none; visibility: hidden">
                        <p><span></span></p>
                      </div>
                    </div>

                    <!-- Unnamed (水平线) -->
                    <div id="u5314" class="ax_default line">
                      <img id="u5314_img" class="img " src="images/点餐-选择商品/u5310.png"/>
                      <!-- Unnamed () -->
                      <div id="u5315" class="text" style="display:none; visibility: hidden">
                        <p><span></span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5316" class="ax_default _二级标题">
                      <div id="u5316_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5317" class="text">
                        <p style="font-size:20px;"><span style="color:#999999;">-| </span><span style="font-size:18px;color:#666666;">纸包鱼(1.205kg)</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5318" class="ax_default _二级标题">
                      <div id="u5318_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5319" class="text">
                        <p><span>X1</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5320" class="ax_default _二级标题">
                      <div id="u5320_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5321" class="text">
                        <p style="font-size:20px;"><span style="color:#999999;">-| </span><span style="font-size:18px;color:#666666;">酸辣土豆丝</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5322" class="ax_default _二级标题">
                      <div id="u5322_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5323" class="text">
                        <p><span>X2</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (水平线) -->
                    <div id="u5324" class="ax_default line">
                      <img id="u5324_img" class="img " src="images/点餐-选择商品/u5324.png"/>
                      <!-- Unnamed () -->
                      <div id="u5325" class="text" style="display:none; visibility: hidden">
                        <p><span></span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5326" class="ax_default _二级标题">
                      <div id="u5326_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5327" class="text">
                        <p style="font-size:20px;"><span style="color:#999999;">-| </span><span style="font-size:18px;color:#666666;">干煸四季豆(大份)</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5328" class="ax_default _二级标题">
                      <div id="u5328_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5329" class="text">
                        <p><span>X1</span></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 规格+备注 (组合) -->
              <div id="u5330" class="ax_default" data-label="规格+备注" data-width="449" data-height="61">

                <!-- Unnamed (矩形) -->
                <div id="u5331" class="ax_default ellipse">
                  <div id="u5331_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5332" class="text">
                    <p><span>即</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5333" class="ax_default _二级标题">
                  <div id="u5333_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5334" class="text">
                    <p><span>酸辣土豆丝（大份）</span></p>
                  </div>
                </div>

                <!-- Unnamed (水平线) -->
                <div id="u5335" class="ax_default line">
                  <img id="u5335_img" class="img " src="images/点餐-选择商品/u5324.png"/>
                  <!-- Unnamed () -->
                  <div id="u5336" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5337" class="ax_default _二级标题">
                  <div id="u5337_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5338" class="text">
                    <p><span>X1</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5339" class="ax_default _二级标题">
                  <div id="u5339_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5340" class="text">
                    <p><span>¥12</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5341" class="ax_default label">
                  <div id="u5341_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5342" class="text">
                    <p><span>这里显示属性/备注信息，多个属性/备注用逗号隔开</span></p>
                  </div>
                </div>
              </div>

              <!-- 称重 (组合) -->
              <div id="u5343" class="ax_default" data-label="称重" data-width="449" data-height="56">

                <!-- Unnamed (矩形) -->
                <div id="u5344" class="ax_default ellipse">
                  <div id="u5344_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5345" class="text">
                    <p><span>即</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5346" class="ax_default _二级标题">
                  <div id="u5346_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5347" class="text">
                    <p><span style="color:#999999;">[重]</span><span style="color:#666666;">酸辣土豆丝</span></p>
                  </div>
                </div>

                <!-- Unnamed (水平线) -->
                <div id="u5348" class="ax_default line">
                  <img id="u5348_img" class="img " src="images/点餐-选择商品/u5324.png"/>
                  <!-- Unnamed () -->
                  <div id="u5349" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5350" class="ax_default _二级标题">
                  <div id="u5350_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5351" class="text">
                    <p><span>X1.25</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5352" class="ax_default _二级标题">
                  <div id="u5352_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5353" class="text">
                    <p><span>¥12</span></p>
                  </div>
                </div>
              </div>

              <!-- 普通 (组合) -->
              <div id="u5354" class="ax_default" data-label="普通" data-width="449" data-height="56">

                <!-- Unnamed (矩形) -->
                <div id="u5355" class="ax_default ellipse">
                  <div id="u5355_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5356" class="text">
                    <p><span>即</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5357" class="ax_default _二级标题">
                  <div id="u5357_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5358" class="text">
                    <p><span>酸辣土豆丝</span></p>
                  </div>
                </div>

                <!-- Unnamed (水平线) -->
                <div id="u5359" class="ax_default line">
                  <img id="u5359_img" class="img " src="images/点餐-选择商品/u5324.png"/>
                  <!-- Unnamed () -->
                  <div id="u5360" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5361" class="ax_default _二级标题">
                  <div id="u5361_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5362" class="text">
                    <p><span>X1</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5363" class="ax_default _二级标题">
                  <div id="u5363_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5364" class="text">
                    <p><span>¥12</span></p>
                  </div>
                </div>
              </div>

              <!-- 套餐+备注 (组合) -->
              <div id="u5365" class="ax_default" data-label="套餐+备注" data-width="450" data-height="256">

                <!-- Unnamed (矩形) -->
                <div id="u5366" class="ax_default ellipse">
                  <div id="u5366_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5367" class="text">
                    <p><span>即</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5368" class="ax_default _二级标题">
                  <div id="u5368_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5369" class="text">
                    <p><span style="color:#999999;">[套]</span><span style="color:#666666;">酸辣土豆丝</span></p>
                  </div>
                </div>

                <!-- Unnamed (水平线) -->
                <div id="u5370" class="ax_default line">
                  <img id="u5370_img" class="img " src="images/点餐-选择商品/u5324.png"/>
                  <!-- Unnamed () -->
                  <div id="u5371" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5372" class="ax_default _二级标题">
                  <div id="u5372_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5373" class="text">
                    <p><span>X1</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5374" class="ax_default _二级标题">
                  <div id="u5374_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5375" class="text">
                    <p><span>¥12</span></p>
                  </div>
                </div>

                <!-- Unnamed (水平线) -->
                <div id="u5376" class="ax_default line">
                  <img id="u5376_img" class="img " src="images/点餐-选择商品/u5310.png"/>
                  <!-- Unnamed () -->
                  <div id="u5377" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (水平线) -->
                <div id="u5378" class="ax_default line">
                  <img id="u5378_img" class="img " src="images/点餐-选择商品/u5310.png"/>
                  <!-- Unnamed () -->
                  <div id="u5379" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5380" class="ax_default _二级标题">
                  <div id="u5380_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5381" class="text">
                    <p style="font-size:20px;"><span style="color:#999999;">-| </span><span style="font-size:18px;color:#666666;">纸包鱼(1.205kg)</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5382" class="ax_default _二级标题">
                  <div id="u5382_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5383" class="text">
                    <p><span>X1</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5384" class="ax_default _二级标题">
                  <div id="u5384_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5385" class="text">
                    <p style="font-size:20px;"><span style="color:#999999;">-| </span><span style="font-size:18px;color:#666666;">酸辣土豆丝</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5386" class="ax_default _二级标题">
                  <div id="u5386_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5387" class="text">
                    <p><span>X2</span></p>
                  </div>
                </div>

                <!-- Unnamed (水平线) -->
                <div id="u5388" class="ax_default line">
                  <img id="u5388_img" class="img " src="images/点餐-选择商品/u5388.png"/>
                  <!-- Unnamed () -->
                  <div id="u5389" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5390" class="ax_default _二级标题">
                  <div id="u5390_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5391" class="text">
                    <p style="font-size:20px;"><span style="color:#999999;">-| </span><span style="font-size:18px;color:#666666;">干煸四季豆(大份)</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5392" class="ax_default _二级标题">
                  <div id="u5392_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5393" class="text">
                    <p><span>X1</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u5394" class="ax_default label">
                  <div id="u5394_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5395" class="text">
                    <p><span>这里显示属性/备注信息，多个属性/备注用逗号隔开，超过一行时，则需要换行显示</span></p>
                  </div>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u5396" class="ax_default box_2">
                <div id="u5396_div" class=""></div>
                <!-- Unnamed () -->
                <div id="u5397" class="text" style="display:none; visibility: hidden">
                  <p><span></span></p>
                </div>
              </div>
            </div>
          </div>
          <div id="u5279_state2" class="panel_state" data-label="规格菜品" style="display:none; visibility: hidden;">
            <div id="u5279_state2_content" class="panel_state_content">

              <!-- 规格 (组合) -->
              <div id="u5398" class="ax_default" data-label="规格" data-width="449" data-height="180">

                <!-- 未下单标记 (组合) -->
                <div id="u5399" class="ax_default" data-label="未下单标记" data-width="121" data-height="30">

                  <!-- Unnamed (矩形) -->
                  <div id="u5400" class="ax_default _三级标题">
                    <div id="u5400_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5401" class="text">
                      <p><span>未下单（3）</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (垂直线) -->
                  <div id="u5402" class="ax_default line">
                    <img id="u5402_img" class="img " src="images/点餐-选择商品/u5284.png"/>
                    <!-- Unnamed () -->
                    <div id="u5403" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>
                </div>

                <!-- 选中状态 (组合) -->
                <div id="u5404" class="ax_default" data-label="选中状态" data-width="449" data-height="140">

                  <!-- Unnamed (矩形) -->
                  <div id="u5405" class="ax_default box_2">
                    <div id="u5405_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5406" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5407" class="ax_default _二级标题">
                    <div id="u5407_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5408" class="text">
                      <p><span>酸辣土豆丝（大份）</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5409" class="ax_default _二级标题">
                    <div id="u5409_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5410" class="text">
                      <p><span>¥12.00</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5411" class="ax_default box_1">
                    <div id="u5411_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5412" class="text">
                      <p><span>即</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5413" class="ax_default box_2">
                    <div id="u5413_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5414" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5415" class="ax_default icon">
                    <img id="u5415_img" class="img " src="images/点餐-选择商品/u5297.png"/>
                    <!-- Unnamed () -->
                    <div id="u5416" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5417" class="ax_default icon">
                    <img id="u5417_img" class="img " src="images/点餐-选择商品/u5299.png"/>
                    <!-- Unnamed () -->
                    <div id="u5418" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5419" class="ax_default icon">
                    <img id="u5419_img" class="img " src="images/点餐-选择商品/u5301.png"/>
                    <!-- Unnamed () -->
                    <div id="u5420" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5421" class="ax_default box_1">
                    <div id="u5421_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5422" class="text">
                      <p><span>1</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5423" class="ax_default icon">
                    <img id="u5423_img" class="img " src="images/点餐-选择商品/u5305.png"/>
                    <!-- Unnamed () -->
                    <div id="u5424" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="u5279_state3" class="panel_state" data-label="普通菜品" style="display:none; visibility: hidden;">
            <div id="u5279_state3_content" class="panel_state_content">

              <!-- 普通 (组合) -->
              <div id="u5425" class="ax_default" data-label="普通" data-width="449" data-height="180">

                <!-- 未下单标记 (组合) -->
                <div id="u5426" class="ax_default" data-label="未下单标记" data-width="121" data-height="30">

                  <!-- Unnamed (矩形) -->
                  <div id="u5427" class="ax_default _三级标题">
                    <div id="u5427_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5428" class="text">
                      <p><span>未下单（3）</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (垂直线) -->
                  <div id="u5429" class="ax_default line">
                    <img id="u5429_img" class="img " src="images/点餐-选择商品/u5284.png"/>
                    <!-- Unnamed () -->
                    <div id="u5430" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>
                </div>

                <!-- 选中状态 (组合) -->
                <div id="u5431" class="ax_default" data-label="选中状态" data-width="449" data-height="140">

                  <!-- Unnamed (矩形) -->
                  <div id="u5432" class="ax_default box_2">
                    <div id="u5432_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5433" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5434" class="ax_default _二级标题">
                    <div id="u5434_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5435" class="text">
                      <p><span>酸辣土豆丝</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5436" class="ax_default _二级标题">
                    <div id="u5436_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5437" class="text">
                      <p><span>¥12.00</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5438" class="ax_default box_1">
                    <div id="u5438_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5439" class="text">
                      <p><span>即</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5440" class="ax_default box_2">
                    <div id="u5440_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5441" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5442" class="ax_default icon">
                    <img id="u5442_img" class="img " src="images/点餐-选择商品/u5297.png"/>
                    <!-- Unnamed () -->
                    <div id="u5443" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5444" class="ax_default icon">
                    <img id="u5444_img" class="img " src="images/点餐-选择商品/u5299.png"/>
                    <!-- Unnamed () -->
                    <div id="u5445" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5446" class="ax_default icon">
                    <img id="u5446_img" class="img " src="images/点餐-选择商品/u5301.png"/>
                    <!-- Unnamed () -->
                    <div id="u5447" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5448" class="ax_default box_1">
                    <div id="u5448_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5449" class="text">
                      <p><span>1</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5450" class="ax_default icon">
                    <img id="u5450_img" class="img " src="images/点餐-选择商品/u5305.png"/>
                    <!-- Unnamed () -->
                    <div id="u5451" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="u5279_state4" class="panel_state" data-label="称重菜品" style="display:none; visibility: hidden;">
            <div id="u5279_state4_content" class="panel_state_content">

              <!-- 称重 (组合) -->
              <div id="u5452" class="ax_default" data-label="称重" data-width="449" data-height="180">

                <!-- 未下单标记 (组合) -->
                <div id="u5453" class="ax_default" data-label="未下单标记" data-width="121" data-height="30">

                  <!-- Unnamed (矩形) -->
                  <div id="u5454" class="ax_default _三级标题">
                    <div id="u5454_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5455" class="text">
                      <p><span>未下单（3）</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (垂直线) -->
                  <div id="u5456" class="ax_default line">
                    <img id="u5456_img" class="img " src="images/点餐-选择商品/u5284.png"/>
                    <!-- Unnamed () -->
                    <div id="u5457" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>
                </div>

                <!-- 选中状态 (组合) -->
                <div id="u5458" class="ax_default" data-label="选中状态" data-width="449" data-height="140">

                  <!-- Unnamed (矩形) -->
                  <div id="u5459" class="ax_default box_2">
                    <div id="u5459_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5460" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5461" class="ax_default _二级标题">
                    <div id="u5461_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5462" class="text">
                      <p><span style="color:#F2F2F2;">[重]</span><span style="color:#FFFFFF;">酸辣土豆丝</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5463" class="ax_default _二级标题">
                    <div id="u5463_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5464" class="text">
                      <p><span>¥12.00</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5465" class="ax_default box_1">
                    <div id="u5465_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5466" class="text">
                      <p><span>即</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5467" class="ax_default box_2">
                    <div id="u5467_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5468" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5469" class="ax_default icon">
                    <img id="u5469_img" class="img " src="images/点餐-选择商品/u5301.png"/>
                    <!-- Unnamed () -->
                    <div id="u5470" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5471" class="ax_default box_1">
                    <div id="u5471_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5472" class="text">
                      <p><span>1.000kg</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5473" class="ax_default icon">
                    <img id="u5473_img" class="img " src="images/点餐-选择商品/u5305.png"/>
                    <!-- Unnamed () -->
                    <div id="u5474" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="u5279_state5" class="panel_state" data-label="套餐菜品" style="display:none; visibility: hidden;">
            <div id="u5279_state5_content" class="panel_state_content">

              <!-- 套餐 (组合) -->
              <div id="u5475" class="ax_default" data-label="套餐" data-width="450" data-height="346">

                <!-- 未下单标记 (组合) -->
                <div id="u5476" class="ax_default" data-label="未下单标记" data-width="121" data-height="30">

                  <!-- Unnamed (矩形) -->
                  <div id="u5477" class="ax_default _三级标题">
                    <div id="u5477_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5478" class="text">
                      <p><span>未下单（3）</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (垂直线) -->
                  <div id="u5479" class="ax_default line">
                    <img id="u5479_img" class="img " src="images/点餐-选择商品/u5284.png"/>
                    <!-- Unnamed () -->
                    <div id="u5480" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>
                </div>

                <!-- 选中状态 (组合) -->
                <div id="u5481" class="ax_default" data-label="选中状态" data-width="450" data-height="306">

                  <!-- Unnamed (矩形) -->
                  <div id="u5482" class="ax_default box_2">
                    <div id="u5482_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5483" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5484" class="ax_default _二级标题">
                    <div id="u5484_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5485" class="text">
                      <p><span style="color:#F2F2F2;">[套]</span><span style="color:#FFFFFF;">酸辣土豆丝</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5486" class="ax_default _二级标题">
                    <div id="u5486_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5487" class="text">
                      <p><span>¥12.00</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5488" class="ax_default box_1">
                    <div id="u5488_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5489" class="text">
                      <p><span>即</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5490" class="ax_default box_2">
                    <div id="u5490_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5491" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5492" class="ax_default icon">
                    <img id="u5492_img" class="img " src="images/点餐-选择商品/u5297.png"/>
                    <!-- Unnamed () -->
                    <div id="u5493" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5494" class="ax_default icon">
                    <img id="u5494_img" class="img " src="images/点餐-选择商品/u5299.png"/>
                    <!-- Unnamed () -->
                    <div id="u5495" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5496" class="ax_default icon">
                    <img id="u5496_img" class="img " src="images/点餐-选择商品/u5301.png"/>
                    <!-- Unnamed () -->
                    <div id="u5497" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u5498" class="ax_default box_1">
                    <div id="u5498_div" class=""></div>
                    <!-- Unnamed () -->
                    <div id="u5499" class="text">
                      <p><span>1</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (形状) -->
                  <div id="u5500" class="ax_default icon">
                    <img id="u5500_img" class="img " src="images/点餐-选择商品/u5305.png"/>
                    <!-- Unnamed () -->
                    <div id="u5501" class="text" style="display:none; visibility: hidden">
                      <p><span></span></p>
                    </div>
                  </div>

                  <!-- 成分 (组合) -->
                  <div id="u5502" class="ax_default" data-label="成分" data-width="450" data-height="166">

                    <!-- Unnamed (矩形) -->
                    <div id="u5503" class="ax_default box_2">
                      <div id="u5503_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5504" class="text" style="display:none; visibility: hidden">
                        <p><span></span></p>
                      </div>
                    </div>

                    <!-- Unnamed (水平线) -->
                    <div id="u5505" class="ax_default line">
                      <img id="u5505_img" class="img " src="images/点餐-选择商品/u5310.png"/>
                      <!-- Unnamed () -->
                      <div id="u5506" class="text" style="display:none; visibility: hidden">
                        <p><span></span></p>
                      </div>
                    </div>

                    <!-- Unnamed (水平线) -->
                    <div id="u5507" class="ax_default line">
                      <img id="u5507_img" class="img " src="images/点餐-选择商品/u5310.png"/>
                      <!-- Unnamed () -->
                      <div id="u5508" class="text" style="display:none; visibility: hidden">
                        <p><span></span></p>
                      </div>
                    </div>

                    <!-- Unnamed (水平线) -->
                    <div id="u5509" class="ax_default line">
                      <img id="u5509_img" class="img " src="images/点餐-选择商品/u5310.png"/>
                      <!-- Unnamed () -->
                      <div id="u5510" class="text" style="display:none; visibility: hidden">
                        <p><span></span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5511" class="ax_default _二级标题">
                      <div id="u5511_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5512" class="text">
                        <p style="font-size:20px;"><span style="color:#999999;">-| </span><span style="font-size:18px;color:#666666;">纸包鱼(1.205kg)</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5513" class="ax_default _二级标题">
                      <div id="u5513_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5514" class="text">
                        <p><span>X1</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5515" class="ax_default _二级标题">
                      <div id="u5515_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5516" class="text">
                        <p style="font-size:20px;"><span style="color:#999999;">-| </span><span style="font-size:18px;color:#666666;">酸辣土豆丝</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5517" class="ax_default _二级标题">
                      <div id="u5517_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5518" class="text">
                        <p><span>X2</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (水平线) -->
                    <div id="u5519" class="ax_default line">
                      <img id="u5519_img" class="img " src="images/点餐-选择商品/u5324.png"/>
                      <!-- Unnamed () -->
                      <div id="u5520" class="text" style="display:none; visibility: hidden">
                        <p><span></span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5521" class="ax_default _二级标题">
                      <div id="u5521_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5522" class="text">
                        <p style="font-size:20px;"><span style="color:#999999;">-| </span><span style="font-size:18px;color:#666666;">干煸四季豆(大份)</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5523" class="ax_default _二级标题">
                      <div id="u5523_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5524" class="text">
                        <p><span>X1</span></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u5525" class="ax_default box_2">
          <div id="u5525_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u5526" class="text">
            <p><span>下单打厨</span></p>
          </div>
        </div>
      </div>

      <!-- 遮障-点餐 (矩形) -->
      <div id="u5527" class="ax_default box_2 ax_default_hidden" data-label="遮障-点餐" style="display:none; visibility: hidden">
        <div id="u5527_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u5528" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 点餐弹框 (动态面板) -->
      <div id="u5529" class="ax_default ax_default_hidden" data-label="点餐弹框" style="display:none; visibility: hidden">
        <div id="u5529_state0" class="panel_state" data-label="套餐商品">
          <div id="u5529_state0_content" class="panel_state_content">

            <!-- 编辑框 (组合) -->
            <div id="u5530" class="ax_default" data-label="编辑框" data-width="560" data-height="766">

              <!-- Unnamed (矩形) -->
              <div id="u5531" class="ax_default box_1">
                <div id="u5531_div" class=""></div>
                <!-- Unnamed () -->
                <div id="u5532" class="text" style="display:none; visibility: hidden">
                  <p><span></span></p>
                </div>
              </div>

              <!-- 底部 (组合) -->
              <div id="u5533" class="ax_default" data-label="底部" data-width="560" data-height="75">

                <!-- Unnamed (矩形) -->
                <div id="u5534" class="ax_default box_1">
                  <div id="u5534_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5535" class="text">
                    <p><span>确认</span></p>
                  </div>
                </div>
              </div>

              <!-- 标题 (组合) -->
              <div id="u5536" class="ax_default" data-label="标题" data-width="560" data-height="49">

                <!-- Unnamed (矩形) -->
                <div id="u5537" class="ax_default _一级标题">
                  <div id="u5537_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5538" class="text">
                    <p><span style="font-family:'Arial-BoldMT', 'Arial Bold', 'Arial';font-weight:700;">[套]酸辣</span><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">土豆丝</span></p>
                  </div>
                </div>

                <!-- Unnamed (水平线) -->
                <div id="u5539" class="ax_default line">
                  <img id="u5539_img" class="img " src="images/点餐-选择商品/u5539.png"/>
                  <!-- Unnamed () -->
                  <div id="u5540" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (图片) -->
                <div id="u5541" class="ax_default _图片">
                  <img id="u5541_img" class="img " src="images/点餐-选择商品/u5541.png"/>
                  <!-- Unnamed () -->
                  <div id="u5542" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>
              </div>

              <!-- 编辑明细 (动态面板) -->
              <div id="u5543" class="ax_default" data-label="编辑明细">
                <div id="u5543_state0" class="panel_state" data-label="商品编辑">
                  <div id="u5543_state0_content" class="panel_state_content">

                    <!-- 属性备注 (组合) -->
                    <div id="u5544" class="ax_default" data-label="属性备注" data-width="535" data-height="565">

                      <!-- 备注 (组合) -->
                      <div id="u5545" class="ax_default" data-label="备注" data-width="530" data-height="120">

                        <!-- Unnamed (矩形) -->
                        <div id="u5546" class="ax_default _三级标题">
                          <div id="u5546_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5547" class="text">
                            <p><span>备注</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (多行文本框) -->
                        <div id="u5548" class="ax_default text_area">
                          <textarea id="u5548_input"></textarea>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5549" class="ax_default label">
                          <div id="u5549_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5550" class="text">
                            <p><span>0/30</span></p>
                          </div>
                        </div>
                      </div>

                      <!-- 主菜 (组合) -->
                      <div id="u5551" class="ax_default" data-label="主菜" data-width="535" data-height="287">

                        <!-- Unnamed (矩形) -->
                        <div id="u5552" class="ax_default _三级标题">
                          <div id="u5552_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5553" class="text">
                            <p><span>主菜（3选1）</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5554" class="ax_default box_2">
                          <div id="u5554_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5555" class="text">
                            <p style="font-size:16px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">&nbsp; 干煸四季豆</span><span style="font-family:'ArialMT', 'Arial';"></span></p><p style="font-size:18px;"><span style="font-family:'ArialMT', 'Arial';">&nbsp; </span><span style="font-family:'ArialMT', 'Arial';font-size:16px;">¥99.00</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5556" class="ax_default box_2">
                          <div id="u5556_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5557" class="text">
                            <p style="font-size:16px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">&nbsp; 番茄炒鸡蛋</span><span style="font-family:'ArialMT', 'Arial';"></span></p><p style="font-size:18px;"><span style="font-family:'ArialMT', 'Arial';">&nbsp; </span><span style="font-family:'ArialMT', 'Arial';font-size:16px;">¥99.00</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5558" class="ax_default box_2">
                          <div id="u5558_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5559" class="text">
                            <p style="font-size:16px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">超香郫县豆瓣红烧鱼…[1.234kg]</span><span style="font-family:'ArialMT', 'Arial';"></span></p><p style="font-size:18px;"><span style="font-family:'ArialMT', 'Arial';">&nbsp;</span><span style="font-family:'ArialMT', 'Arial';font-size:16px;">¥99.00</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5560" class="ax_default box_2">
                          <div id="u5560_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5561" class="text">
                            <p style="font-size:16px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">酸辣土豆丝[2份]</span><span style="font-family:'ArialMT', 'Arial';"></span></p><p style="font-size:18px;"><span style="font-family:'ArialMT', 'Arial';">&nbsp;</span><span style="font-family:'ArialMT', 'Arial';font-size:16px;">¥99.00</span></p>
                          </div>
                        </div>

                        <!-- 数量加减 (组合) -->
                        <div id="u5562" class="ax_default" data-label="数量加减" data-width="120" data-height="37">

                          <!-- NumberInput (文本框) -->
                          <div id="u5563" class="ax_default text_field" data-label="NumberInput">
                            <input id="u5563_input" type="text" value="1"/>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5564" class="ax_default box_2">
                            <div id="u5564_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5565" class="text">
                              <p><span>-</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5566" class="ax_default box_2">
                            <div id="u5566_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5567" class="text">
                              <p><span>+</span></p>
                            </div>
                          </div>
                        </div>

                        <!-- 数量加减 (组合) -->
                        <div id="u5568" class="ax_default" data-label="数量加减" data-width="120" data-height="37">

                          <!-- NumberInput (文本框) -->
                          <div id="u5569" class="ax_default text_field" data-label="NumberInput">
                            <input id="u5569_input" type="text" value="0"/>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5570" class="ax_default box_2">
                            <div id="u5570_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5571" class="text">
                              <p><span>-</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5572" class="ax_default box_2">
                            <div id="u5572_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5573" class="text">
                              <p><span>+</span></p>
                            </div>
                          </div>
                        </div>

                        <!-- 数量加减 (组合) -->
                        <div id="u5574" class="ax_default" data-label="数量加减" data-width="120" data-height="37">

                          <!-- NumberInput (文本框) -->
                          <div id="u5575" class="ax_default text_field" data-label="NumberInput">
                            <input id="u5575_input" type="text" value="0"/>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5576" class="ax_default box_2">
                            <div id="u5576_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5577" class="text">
                              <p><span>-</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5578" class="ax_default box_2">
                            <div id="u5578_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5579" class="text">
                              <p><span>+</span></p>
                            </div>
                          </div>
                        </div>

                        <!-- 数量加减 (组合) -->
                        <div id="u5580" class="ax_default" data-label="数量加减" data-width="120" data-height="37">

                          <!-- NumberInput (文本框) -->
                          <div id="u5581" class="ax_default text_field" data-label="NumberInput">
                            <input id="u5581_input" type="text" value="0"/>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5582" class="ax_default box_2">
                            <div id="u5582_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5583" class="text">
                              <p><span>-</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5584" class="ax_default box_2">
                            <div id="u5584_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5585" class="text">
                              <p><span>+</span></p>
                            </div>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5586" class="ax_default box_2">
                          <div id="u5586_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5587" class="text">
                            <p style="font-size:16px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">&nbsp; 四季豆</span><span style="font-family:'ArialMT', 'Arial';"></span></p><p style="font-size:18px;"><span style="font-family:'ArialMT', 'Arial';">&nbsp; </span><span style="font-family:'ArialMT', 'Arial';font-size:16px;">¥99.00</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5588" class="ax_default box_2">
                          <div id="u5588_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5589" class="text">
                            <p style="font-size:16px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">套餐子菜品名称支持换行显示</span><span style="font-family:'ArialMT', 'Arial';"></span></p><p style="font-size:18px;"><span style="font-family:'ArialMT', 'Arial';">&nbsp;</span><span style="font-family:'ArialMT', 'Arial';font-size:16px;">¥99.00</span></p>
                          </div>
                        </div>

                        <!-- 数量加减 (组合) -->
                        <div id="u5590" class="ax_default" data-label="数量加减" data-width="120" data-height="37">

                          <!-- NumberInput (文本框) -->
                          <div id="u5591" class="ax_default text_field" data-label="NumberInput">
                            <input id="u5591_input" type="text" value="0"/>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5592" class="ax_default box_2">
                            <div id="u5592_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5593" class="text">
                              <p><span>-</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5594" class="ax_default box_2">
                            <div id="u5594_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5595" class="text">
                              <p><span>+</span></p>
                            </div>
                          </div>
                        </div>

                        <!-- 数量加减 (组合) -->
                        <div id="u5596" class="ax_default" data-label="数量加减" data-width="120" data-height="37">

                          <!-- NumberInput (文本框) -->
                          <div id="u5597" class="ax_default text_field" data-label="NumberInput">
                            <input id="u5597_input" type="text" value="0"/>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5598" class="ax_default box_2">
                            <div id="u5598_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5599" class="text">
                              <p><span>-</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5600" class="ax_default box_2">
                            <div id="u5600_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5601" class="text">
                              <p><span>+</span></p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 主食 (组合) -->
                      <div id="u5602" class="ax_default" data-label="主食" data-width="535" data-height="115">

                        <!-- Unnamed (矩形) -->
                        <div id="u5603" class="ax_default _三级标题">
                          <div id="u5603_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5604" class="text">
                            <p><span>主食</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5605" class="ax_default box_2">
                          <div id="u5605_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5606" class="text">
                            <p style="font-size:18px;"><span style="font-size:16px;">&nbsp;</span><span> 米饭[2份]</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5607" class="ax_default box_2">
                          <div id="u5607_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5608" class="text">
                            <p style="font-size:18px;"><span style="font-size:16px;">&nbsp;</span><span> 八宝粥[2份]</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5609" class="ax_default box_2">
                          <div id="u5609_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5610" class="text">
                            <p style="font-size:18px;"><span style="font-size:16px;">&nbsp;</span><span> 黑米粥</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5611" class="ax_default box_2">
                          <div id="u5611_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5612" class="text">
                            <p style="font-size:20px;"><span>&nbsp; </span><span style="font-size:18px;">稀饭[2份]</span></p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="u5543_state1" class="panel_state" data-label="赠送" style="display:none; visibility: hidden;">
                  <div id="u5543_state1_content" class="panel_state_content">

                    <!-- 赠送菜品 (组合) -->
                    <div id="u5613" class="ax_default" data-label="赠送菜品" data-width="205" data-height="32">

                      <!-- 开启赠送 (组合) -->
                      <div id="u5614" class="ax_default ax_default_hidden" data-label="开启赠送" style="display:none; visibility: hidden" data-width="0" data-height="0">

                        <!-- 赠送数量 (组合) -->
                        <div id="u5615" class="ax_default" data-label="赠送数量" data-width="0" data-height="0">

                          <!-- Unnamed (矩形) -->
                          <div id="u5616" class="ax_default _三级标题">
                            <div id="u5616_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5617" class="text">
                              <p><span>赠送数量</span></p>
                            </div>
                          </div>

                          <!-- 数量加减 (组合) -->
                          <div id="u5618" class="ax_default" data-label="数量加减" data-width="0" data-height="0">

                            <!-- Unnamed (矩形) -->
                            <div id="u5619" class="ax_default box_2">
                              <div id="u5619_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5620" class="text" style="display:none; visibility: hidden">
                                <p><span></span></p>
                              </div>
                            </div>

                            <!-- Unnamed (矩形) -->
                            <div id="u5621" class="ax_default box_2">
                              <div id="u5621_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5622" class="text" style="display:none; visibility: hidden">
                                <p><span></span></p>
                              </div>
                            </div>

                            <!-- NumberInput (文本框) -->
                            <div id="u5623" class="ax_default text_field" data-label="NumberInput">
                              <input id="u5623_input" type="text" value="1"/>
                            </div>

                            <!-- Unnamed (矩形) -->
                            <div id="u5624" class="ax_default _文本段落1">
                              <div id="u5624_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5625" class="text">
                                <p><span></span></p>
                              </div>
                            </div>

                            <!-- Unnamed (矩形) -->
                            <div id="u5626" class="ax_default _文本段落1">
                              <div id="u5626_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5627" class="text">
                                <p><span></span></p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 赠送原因 (组合) -->
                        <div id="u5628" class="ax_default" data-label="赠送原因" data-width="0" data-height="0">

                          <!-- Unnamed (矩形) -->
                          <div id="u5629" class="ax_default _三级标题">
                            <div id="u5629_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5630" class="text">
                              <p><span>赠送原因</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (多行文本框) -->
                          <div id="u5631" class="ax_default text_area">
                            <textarea id="u5631_input"></textarea>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5632" class="ax_default label">
                            <div id="u5632_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5633" class="text">
                              <p><span>0/30</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5634" class="ax_default box_2" selectiongroup="tab2">
                            <div id="u5634_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5635" class="text">
                              <p><span>菜品质量问题</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5636" class="ax_default box_2" selectiongroup="tab2">
                            <div id="u5636_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5637" class="text">
                              <p><span>长时间未上菜</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5638" class="ax_default box_2" selectiongroup="tab2">
                            <div id="u5638_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5639" class="text">
                              <p><span>其他原因</span></p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 赠送数量 (组合) -->
                      <div id="u5640" class="ax_default" data-label="赠送数量" data-width="205" data-height="32">

                        <!-- Unnamed (矩形) -->
                        <div id="u5641" class="ax_default _三级标题">
                          <div id="u5641_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5642" class="text">
                            <p><span>赠送菜品</span></p>
                          </div>
                        </div>

                        <!-- SwitchGroup (组合) -->
                        <div id="u5643" class="ax_default" data-label="SwitchGroup" data-width="70" data-height="32">

                          <!-- Border (矩形) -->
                          <div id="u5644" class="ax_default box_3" data-label="Border">
                            <div id="u5644_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5645" class="text">
                              <p><span>OFF</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (椭圆形) -->
                          <div id="u5646" class="ax_default ellipse">
                            <img id="u5646_img" class="img " src="images/点餐-选择商品/u5646.png"/>
                            <!-- Unnamed () -->
                            <div id="u5647" class="text" style="display:none; visibility: hidden">
                              <p><span></span></p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5648" class="ax_default label">
                      <div id="u5648_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5649" class="text">
                        <p><span>非赠商品的赠送操作说明：</span></p><p><span>1，默认关闭赠送开关，该菜品不赠送</span></p><p><span>2，开启赠送后，显示赠送数量操作及赠送原因操作，默认赠送数量为1，可通过“加减”符号调整赠送数量（赠送数量不得大于已选数量，赠送原因必填）</span></p><p><span>3，点击赠送数量的数量框，可以弹出修改数量操作框（限制系统输入法弹出），填写数量点击确定后，覆盖原赠送数量</span></p><p><span>4，开启赠送填写信息后，再关闭赠送，则清空填写的赠送信息，关闭菜品赠送操作</span></p><p><span>5，点击确定按钮后，提交更改并生效（本地修改生效）</span></p>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="u5543_state2" class="panel_state" data-label="关闭赠送" style="display:none; visibility: hidden;">
                  <div id="u5543_state2_content" class="panel_state_content">

                    <!-- 赠送菜品 (组合) -->
                    <div id="u5650" class="ax_default" data-label="赠送菜品" data-width="530" data-height="310">

                      <!-- 关闭赠送 (组合) -->
                      <div id="u5651" class="ax_default" data-label="关闭赠送" data-width="530" data-height="235">

                        <!-- 赠送数量 (组合) -->
                        <div id="u5652" class="ax_default" data-label="赠送数量" data-width="265" data-height="85">

                          <!-- Unnamed (矩形) -->
                          <div id="u5653" class="ax_default _三级标题">
                            <div id="u5653_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5654" class="text">
                              <p><span>已赠送数量</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5655" class="ax_default box_1">
                            <div id="u5655_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5656" class="text">
                              <p><span>1</span></p>
                            </div>
                          </div>
                        </div>

                        <!-- 赠送原因 (组合) -->
                        <div id="u5657" class="ax_default" data-label="赠送原因" data-width="530" data-height="120">

                          <!-- Unnamed (矩形) -->
                          <div id="u5658" class="ax_default _三级标题">
                            <div id="u5658_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5659" class="text">
                              <p><span>赠送原因</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5660" class="ax_default box_1">
                            <div id="u5660_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5661" class="text" style="display:none; visibility: hidden">
                              <p><span></span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5662" class="ax_default label">
                            <div id="u5662_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5663" class="text">
                              <p><span>菜品质量问题</span></p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 赠送开关 (组合) -->
                      <div id="u5664" class="ax_default" data-label="赠送开关" data-width="205" data-height="32">

                        <!-- Unnamed (矩形) -->
                        <div id="u5665" class="ax_default _三级标题">
                          <div id="u5665_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5666" class="text">
                            <p><span>赠送菜品</span></p>
                          </div>
                        </div>

                        <!-- SwitchGroup (组合) -->
                        <div id="u5667" class="ax_default" data-label="SwitchGroup" data-width="70" data-height="32">

                          <!-- Border (矩形) -->
                          <div id="u5668" class="ax_default box_3" data-label="Border">
                            <div id="u5668_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5669" class="text">
                              <p><span>ON</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (椭圆形) -->
                          <div id="u5670" class="ax_default ellipse">
                            <img id="u5670_img" class="img " src="images/点餐-选择商品/u5646.png"/>
                            <!-- Unnamed () -->
                            <div id="u5671" class="text" style="display:none; visibility: hidden">
                              <p><span></span></p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5672" class="ax_default label">
                      <div id="u5672_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5673" class="text">
                        <p><span>赠送商品的赠送操作说明：</span></p><p><span>1，默认开启赠送开关，该菜品已被赠送</span></p><p><span>2，显示赠送商品的已赠送数量和赠送原因信息，已赠送数量和原因不可编辑修改</span></p><p><span>3，关闭赠送后，隐藏已赠送数量及赠送原因信息</span></p><p><span>4，关闭赠送后，再开启赠送，则恢复已赠送数量及赠送原因信息显示，已赠送数量和原因不可编辑修改</span></p><p><span>5，点击确定按钮后，提交更改并生效（本地修改生效）</span></p>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="u5543_state3" class="panel_state" data-label="折扣/改价" style="display:none; visibility: hidden;">
                  <div id="u5543_state3_content" class="panel_state_content">

                    <!-- 折扣改价 (组合) -->
                    <div id="u5674" class="ax_default" data-label="折扣改价" data-width="446" data-height="529">

                      <!-- 折扣方式 (组合) -->
                      <div id="u5675" class="ax_default" data-label="折扣方式" data-width="446" data-height="85">

                        <!-- Unnamed (矩形) -->
                        <div id="u5676" class="ax_default _三级标题">
                          <div id="u5676_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5677" class="text">
                            <p><span>折扣方式</span></p>
                          </div>
                        </div>

                        <!-- 单品折扣 (矩形) -->
                        <div id="u5678" class="ax_default box_1 selected" data-label="单品折扣" selectiongroup="tab3">
                          <div id="u5678_div" class="selected"></div>
                          <!-- Unnamed () -->
                          <div id="u5679" class="text">
                            <p><span>单品折扣</span></p>
                          </div>
                        </div>

                        <!-- 单品改价 (矩形) -->
                        <div id="u5680" class="ax_default box_1" data-label="单品改价" selectiongroup="tab3">
                          <div id="u5680_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5681" class="text">
                            <p><span>单品改价</span></p>
                          </div>
                        </div>

                        <!-- 恢复原价 (矩形) -->
                        <div id="u5682" class="ax_default box_1" data-label="恢复原价" selectiongroup="tab3">
                          <div id="u5682_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5683" class="text">
                            <p><span>恢复原价</span></p>
                          </div>
                        </div>
                      </div>

                      <!-- 折扣明细 (动态面板) -->
                      <div id="u5684" class="ax_default" data-label="折扣明细">
                        <div id="u5684_state0" class="panel_state" data-label="单品折扣">
                          <div id="u5684_state0_content" class="panel_state_content">

                            <!-- Unnamed (矩形) -->
                            <div id="u5685" class="ax_default _三级标题">
                              <div id="u5685_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5686" class="text">
                                <p><span>折扣值</span></p>
                              </div>
                            </div>

                            <!-- Unnamed (组合) -->
                            <div id="u5687" class="ax_default" data-width="440" data-height="290">

                              <!-- Unnamed (矩形) -->
                              <div id="u5688" class="ax_default button">
                                <div id="u5688_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5689" class="text">
                                  <p><span>1</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5690" class="ax_default button">
                                <div id="u5690_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5691" class="text">
                                  <p><span>2</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5692" class="ax_default button">
                                <div id="u5692_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5693" class="text">
                                  <p><span>3</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5694" class="ax_default button">
                                <div id="u5694_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5695" class="text">
                                  <p><span>4</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5696" class="ax_default button">
                                <div id="u5696_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5697" class="text">
                                  <p><span>5</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5698" class="ax_default button">
                                <div id="u5698_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5699" class="text">
                                  <p><span>6</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5700" class="ax_default button">
                                <div id="u5700_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5701" class="text">
                                  <p><span>7</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5702" class="ax_default button">
                                <div id="u5702_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5703" class="text">
                                  <p><span>8</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5704" class="ax_default button">
                                <div id="u5704_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5705" class="text">
                                  <p><span>9</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5706" class="ax_default button">
                                <div id="u5706_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5707" class="text">
                                  <p><span>.</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5708" class="ax_default button">
                                <div id="u5708_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5709" class="text">
                                  <p><span>0</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5710" class="ax_default button">
                                <div id="u5710_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5711" class="text">
                                  <p><span>退格</span></p>
                                </div>
                              </div>
                            </div>

                            <!-- Unnamed (文本框) -->
                            <div id="u5712" class="ax_default text_field">
                              <input id="u5712_input" type="text" value=""/>
                            </div>
                          </div>
                        </div>
                        <div id="u5684_state1" class="panel_state" data-label="单品改价" style="display:none; visibility: hidden;">
                          <div id="u5684_state1_content" class="panel_state_content">

                            <!-- Unnamed (矩形) -->
                            <div id="u5713" class="ax_default _三级标题">
                              <div id="u5713_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5714" class="text">
                                <p><span>改价金额</span></p>
                              </div>
                            </div>

                            <!-- Unnamed (组合) -->
                            <div id="u5715" class="ax_default" data-width="440" data-height="290">

                              <!-- Unnamed (矩形) -->
                              <div id="u5716" class="ax_default button">
                                <div id="u5716_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5717" class="text">
                                  <p><span>1</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5718" class="ax_default button">
                                <div id="u5718_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5719" class="text">
                                  <p><span>2</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5720" class="ax_default button">
                                <div id="u5720_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5721" class="text">
                                  <p><span>3</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5722" class="ax_default button">
                                <div id="u5722_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5723" class="text">
                                  <p><span>4</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5724" class="ax_default button">
                                <div id="u5724_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5725" class="text">
                                  <p><span>5</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5726" class="ax_default button">
                                <div id="u5726_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5727" class="text">
                                  <p><span>6</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5728" class="ax_default button">
                                <div id="u5728_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5729" class="text">
                                  <p><span>7</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5730" class="ax_default button">
                                <div id="u5730_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5731" class="text">
                                  <p><span>8</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5732" class="ax_default button">
                                <div id="u5732_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5733" class="text">
                                  <p><span>9</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5734" class="ax_default button">
                                <div id="u5734_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5735" class="text">
                                  <p><span>.</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5736" class="ax_default button">
                                <div id="u5736_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5737" class="text">
                                  <p><span>0</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5738" class="ax_default button">
                                <div id="u5738_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5739" class="text">
                                  <p><span>退格</span></p>
                                </div>
                              </div>
                            </div>

                            <!-- Unnamed (文本框) -->
                            <div id="u5740" class="ax_default text_field">
                              <input id="u5740_input" type="text" value="¥12"/>
                            </div>
                          </div>
                        </div>
                        <div id="u5684_state2" class="panel_state" data-label="恢复原价" style="display:none; visibility: hidden;">
                          <div id="u5684_state2_content" class="panel_state_content">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 功能点 (组合) -->
              <div id="u5741" class="ax_default" data-label="功能点" data-width="558" data-height="55">

                <!-- 赠送 (矩形) -->
                <div id="u5742" class="ax_default box_1" data-label="赠送" selectiongroup="tab1">
                  <div id="u5742_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5743" class="text">
                    <p><span>赠送</span></p>
                  </div>
                </div>

                <!-- 折扣/改价 (矩形) -->
                <div id="u5744" class="ax_default box_1" data-label="折扣/改价" selectiongroup="tab1">
                  <div id="u5744_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5745" class="text">
                    <p><span>折扣/改价</span></p>
                  </div>
                </div>

                <!-- 无 (矩形) -->
                <div id="u5746" class="ax_default box_1" data-label="无" selectiongroup="tab1">
                  <div id="u5746_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5747" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- 商品编辑 (矩形) -->
                <div id="u5748" class="ax_default box_1 selected" data-label="商品编辑" selectiongroup="tab1">
                  <div id="u5748_div" class="selected"></div>
                  <!-- Unnamed () -->
                  <div id="u5749" class="text">
                    <p><span>商品编辑</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div id="u5529_state1" class="panel_state" data-label="规格商品" style="display:none; visibility: hidden;">
          <div id="u5529_state1_content" class="panel_state_content">

            <!-- 编辑框 (组合) -->
            <div id="u5750" class="ax_default" data-label="编辑框" data-width="560" data-height="766">

              <!-- Unnamed (矩形) -->
              <div id="u5751" class="ax_default box_1">
                <div id="u5751_div" class=""></div>
                <!-- Unnamed () -->
                <div id="u5752" class="text" style="display:none; visibility: hidden">
                  <p><span></span></p>
                </div>
              </div>

              <!-- 底部 (组合) -->
              <div id="u5753" class="ax_default" data-label="底部" data-width="560" data-height="75">

                <!-- Unnamed (矩形) -->
                <div id="u5754" class="ax_default box_1">
                  <div id="u5754_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5755" class="text">
                    <p><span>确认</span></p>
                  </div>
                </div>
              </div>

              <!-- 标题 (组合) -->
              <div id="u5756" class="ax_default" data-label="标题" data-width="560" data-height="49">

                <!-- Unnamed (矩形) -->
                <div id="u5757" class="ax_default _一级标题">
                  <div id="u5757_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5758" class="text">
                    <p><span style="font-family:'Arial-BoldMT', 'Arial Bold', 'Arial';font-weight:700;">酸辣</span><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC';font-weight:650;">土豆丝（大份）</span></p>
                  </div>
                </div>

                <!-- Unnamed (水平线) -->
                <div id="u5759" class="ax_default line">
                  <img id="u5759_img" class="img " src="images/点餐-选择商品/u5539.png"/>
                  <!-- Unnamed () -->
                  <div id="u5760" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (图片) -->
                <div id="u5761" class="ax_default _图片">
                  <img id="u5761_img" class="img " src="images/点餐-选择商品/u5541.png"/>
                  <!-- Unnamed () -->
                  <div id="u5762" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>
              </div>

              <!-- 编辑明细 (动态面板) -->
              <div id="u5763" class="ax_default" data-label="编辑明细">
                <div id="u5763_state0" class="panel_state" data-label="商品编辑">
                  <div id="u5763_state0_content" class="panel_state_content">

                    <!-- 属性备注 (组合) -->
                    <div id="u5764" class="ax_default" data-label="属性备注" data-width="530" data-height="520">

                      <!-- 做法 (组合) -->
                      <div id="u5765" class="ax_default" data-label="做法" data-width="530" data-height="85">

                        <!-- Unnamed (矩形) -->
                        <div id="u5766" class="ax_default _三级标题">
                          <div id="u5766_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5767" class="text">
                            <p><span>做法（单选）</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5768" class="ax_default box_2" selectiongroup="tab2">
                          <div id="u5768_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5769" class="text">
                            <p><span>炝炒</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5770" class="ax_default box_2" selectiongroup="tab2">
                          <div id="u5770_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5771" class="text">
                            <p><span>清炒</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5772" class="ax_default box_2" selectiongroup="tab2">
                          <div id="u5772_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5773" class="text">
                            <p><span>蒜泥</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5774" class="ax_default box_2" selectiongroup="tab2">
                          <div id="u5774_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5775" class="text">
                            <p><span>酸辣</span></p>
                          </div>
                        </div>
                      </div>

                      <!-- 加料 (组合) -->
                      <div id="u5776" class="ax_default" data-label="加料" data-width="530" data-height="140">

                        <!-- Unnamed (矩形) -->
                        <div id="u5777" class="ax_default _三级标题">
                          <div id="u5777_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5778" class="text">
                            <p><span>加料（多选）</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5779" class="ax_default box_2">
                          <div id="u5779_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5780" class="text">
                            <p><span>青椒</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5781" class="ax_default box_2">
                          <div id="u5781_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5782" class="text">
                            <p><span>红椒</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5783" class="ax_default box_2">
                          <div id="u5783_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5784" class="text">
                            <p><span>土豆丝</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5785" class="ax_default box_2">
                          <div id="u5785_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5786" class="text">
                            <p><span>葱姜蒜</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5787" class="ax_default box_2">
                          <div id="u5787_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5788" class="text">
                            <p><span>土豆片</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5789" class="ax_default box_2">
                          <div id="u5789_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5790" class="text">
                            <p><span>小青椒</span></p>
                          </div>
                        </div>
                      </div>

                      <!-- 备注 (组合) -->
                      <div id="u5791" class="ax_default" data-label="备注" data-width="530" data-height="120">

                        <!-- Unnamed (矩形) -->
                        <div id="u5792" class="ax_default _三级标题">
                          <div id="u5792_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5793" class="text">
                            <p><span>备注</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (多行文本框) -->
                        <div id="u5794" class="ax_default text_area">
                          <textarea id="u5794_input"></textarea>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5795" class="ax_default label">
                          <div id="u5795_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5796" class="text">
                            <p><span>0/30</span></p>
                          </div>
                        </div>
                      </div>

                      <!-- 做法 (组合) -->
                      <div id="u5797" class="ax_default" data-label="做法" data-width="530" data-height="110">

                        <!-- Unnamed (矩形) -->
                        <div id="u5798" class="ax_default _三级标题">
                          <div id="u5798_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5799" class="text">
                            <p><span>规格（单选）</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5800" class="ax_default box_2" selectiongroup="tab1">
                          <div id="u5800_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5801" class="text">
                            <p style="font-size:18px;"><span style="font-size:16px;">&nbsp; </span><span>小份</span><span style="font-size:16px;"></span></p><p style="font-size:16px;"><span>&nbsp; ¥99.00</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5802" class="ax_default box_2" selectiongroup="tab1">
                          <div id="u5802_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5803" class="text">
                            <p style="font-size:18px;"><span style="font-size:16px;">&nbsp; </span><span>中份</span><span style="font-size:16px;"></span></p><p style="font-size:16px;"><span>&nbsp; ¥999.00</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5804" class="ax_default box_2" selectiongroup="tab1">
                          <div id="u5804_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5805" class="text">
                            <p style="font-size:18px;"><span style="font-size:16px;">&nbsp; </span><span>大份</span><span style="font-size:16px;"></span></p><p style="font-size:16px;"><span>&nbsp; ¥9999.00</span></p>
                          </div>
                        </div>

                        <!-- Unnamed (矩形) -->
                        <div id="u5806" class="ax_default box_2" selectiongroup="tab1">
                          <div id="u5806_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5807" class="text">
                            <p style="font-size:16px;"><span style="font-size:14px;">超大超大超大大份</span><span></span></p><p style="font-size:16px;"><span>&nbsp;¥99999.00</span></p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="u5763_state1" class="panel_state" data-label="赠送（非称重商品）" style="display:none; visibility: hidden;">
                  <div id="u5763_state1_content" class="panel_state_content">

                    <!-- 赠送菜品 (组合) -->
                    <div id="u5808" class="ax_default" data-label="赠送菜品" data-width="205" data-height="32">

                      <!-- 开启赠送 (组合) -->
                      <div id="u5809" class="ax_default ax_default_hidden" data-label="开启赠送" style="display:none; visibility: hidden" data-width="0" data-height="0">

                        <!-- 赠送数量 (组合) -->
                        <div id="u5810" class="ax_default" data-label="赠送数量" data-width="0" data-height="0">

                          <!-- Unnamed (矩形) -->
                          <div id="u5811" class="ax_default _三级标题">
                            <div id="u5811_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5812" class="text">
                              <p><span>赠送数量</span></p>
                            </div>
                          </div>

                          <!-- 数量加减 (组合) -->
                          <div id="u5813" class="ax_default" data-label="数量加减" data-width="0" data-height="0">

                            <!-- Unnamed (矩形) -->
                            <div id="u5814" class="ax_default box_2">
                              <div id="u5814_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5815" class="text" style="display:none; visibility: hidden">
                                <p><span></span></p>
                              </div>
                            </div>

                            <!-- Unnamed (矩形) -->
                            <div id="u5816" class="ax_default box_2">
                              <div id="u5816_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5817" class="text" style="display:none; visibility: hidden">
                                <p><span></span></p>
                              </div>
                            </div>

                            <!-- NumberInput (文本框) -->
                            <div id="u5818" class="ax_default text_field" data-label="NumberInput">
                              <input id="u5818_input" type="text" value="1"/>
                            </div>

                            <!-- Unnamed (矩形) -->
                            <div id="u5819" class="ax_default _文本段落1">
                              <div id="u5819_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5820" class="text">
                                <p><span></span></p>
                              </div>
                            </div>

                            <!-- Unnamed (矩形) -->
                            <div id="u5821" class="ax_default _文本段落1">
                              <div id="u5821_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5822" class="text">
                                <p><span></span></p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 赠送原因 (组合) -->
                        <div id="u5823" class="ax_default" data-label="赠送原因" data-width="0" data-height="0">

                          <!-- Unnamed (矩形) -->
                          <div id="u5824" class="ax_default _三级标题">
                            <div id="u5824_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5825" class="text">
                              <p><span>赠送原因</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (多行文本框) -->
                          <div id="u5826" class="ax_default text_area">
                            <textarea id="u5826_input"></textarea>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5827" class="ax_default label">
                            <div id="u5827_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5828" class="text">
                              <p><span>0/30</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5829" class="ax_default box_2" selectiongroup="tab2">
                            <div id="u5829_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5830" class="text">
                              <p><span>菜品质量问题</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5831" class="ax_default box_2" selectiongroup="tab2">
                            <div id="u5831_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5832" class="text">
                              <p><span>长时间未上菜</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5833" class="ax_default box_2" selectiongroup="tab2">
                            <div id="u5833_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5834" class="text">
                              <p><span>其他原因</span></p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 赠送开关 (组合) -->
                      <div id="u5835" class="ax_default" data-label="赠送开关" data-width="205" data-height="32">

                        <!-- Unnamed (矩形) -->
                        <div id="u5836" class="ax_default _三级标题">
                          <div id="u5836_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5837" class="text">
                            <p><span>赠送菜品</span></p>
                          </div>
                        </div>

                        <!-- SwitchGroup (组合) -->
                        <div id="u5838" class="ax_default" data-label="SwitchGroup" data-width="70" data-height="32">

                          <!-- Border (矩形) -->
                          <div id="u5839" class="ax_default box_3" data-label="Border">
                            <div id="u5839_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5840" class="text">
                              <p><span>OFF</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (椭圆形) -->
                          <div id="u5841" class="ax_default ellipse">
                            <img id="u5841_img" class="img " src="images/点餐-选择商品/u5646.png"/>
                            <!-- Unnamed () -->
                            <div id="u5842" class="text" style="display:none; visibility: hidden">
                              <p><span></span></p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5843" class="ax_default label">
                      <div id="u5843_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5844" class="text">
                        <p><span>非赠非称重商品的赠送操作说明：</span></p><p><span>1，默认关闭赠送开关，该菜品不赠送</span></p><p><span>2，开启赠送后，显示赠送数量操作及赠送原因操作，默认赠送数量为1，可通过“加减”符号调整赠送数量（赠送数量不得大于已选数量，赠送原因必填）</span></p><p><span>3，点击赠送数量的数量框，可以弹出修改数量操作框（限制系统输入法弹出），填写数量点击确定后，覆盖原赠送数量</span></p><p><span>4，开启赠送填写信息后，再关闭赠送，则清空填写的赠送信息，关闭菜品赠送操作</span></p><p><span>5，点击确定按钮后，提交更改并生效（本地修改生效）</span></p>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="u5763_state2" class="panel_state" data-label="赠送（称重商品）" style="display:none; visibility: hidden;">
                  <div id="u5763_state2_content" class="panel_state_content">

                    <!-- 赠送菜品 (组合) -->
                    <div id="u5845" class="ax_default" data-label="赠送菜品" data-width="205" data-height="32">

                      <!-- 开启赠送 (组合) -->
                      <div id="u5846" class="ax_default ax_default_hidden" data-label="开启赠送" style="display:none; visibility: hidden" data-width="0" data-height="0">

                        <!-- 赠送重量 (组合) -->
                        <div id="u5847" class="ax_default" data-label="赠送重量" data-width="0" data-height="0">

                          <!-- Unnamed (矩形) -->
                          <div id="u5848" class="ax_default _三级标题">
                            <div id="u5848_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5849" class="text">
                              <p><span>赠送重量</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5850" class="ax_default box_1">
                            <div id="u5850_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5851" class="text">
                              <p><span>1.250kg</span></p>
                            </div>
                          </div>
                        </div>

                        <!-- 赠送原因 (组合) -->
                        <div id="u5852" class="ax_default" data-label="赠送原因" data-width="0" data-height="0">

                          <!-- Unnamed (矩形) -->
                          <div id="u5853" class="ax_default _三级标题">
                            <div id="u5853_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5854" class="text">
                              <p><span>赠送原因</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (多行文本框) -->
                          <div id="u5855" class="ax_default text_area">
                            <textarea id="u5855_input"></textarea>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5856" class="ax_default label">
                            <div id="u5856_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5857" class="text">
                              <p><span>0/30</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5858" class="ax_default box_2" selectiongroup="tab2">
                            <div id="u5858_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5859" class="text">
                              <p><span>菜品质量问题</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5860" class="ax_default box_2" selectiongroup="tab2">
                            <div id="u5860_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5861" class="text">
                              <p><span>长时间未上菜</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5862" class="ax_default box_2" selectiongroup="tab2">
                            <div id="u5862_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5863" class="text">
                              <p><span>其他原因</span></p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 赠送开关 (组合) -->
                      <div id="u5864" class="ax_default" data-label="赠送开关" data-width="205" data-height="32">

                        <!-- Unnamed (矩形) -->
                        <div id="u5865" class="ax_default _三级标题">
                          <div id="u5865_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5866" class="text">
                            <p><span>赠送菜品</span></p>
                          </div>
                        </div>

                        <!-- SwitchGroup (组合) -->
                        <div id="u5867" class="ax_default" data-label="SwitchGroup" data-width="70" data-height="32">

                          <!-- Border (矩形) -->
                          <div id="u5868" class="ax_default box_3" data-label="Border">
                            <div id="u5868_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5869" class="text">
                              <p><span>OFF</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (椭圆形) -->
                          <div id="u5870" class="ax_default ellipse">
                            <img id="u5870_img" class="img " src="images/点餐-选择商品/u5646.png"/>
                            <!-- Unnamed () -->
                            <div id="u5871" class="text" style="display:none; visibility: hidden">
                              <p><span></span></p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5872" class="ax_default label">
                      <div id="u5872_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5873" class="text">
                        <p><span>非赠称重商品的赠送操作说明：</span></p><p><span>1，默认关闭赠送开关，该菜品不赠送</span></p><p><span>2，开启赠送后，显示赠送重量及赠送原因操作，默认赠送重量为当前称重商品已选重量，赠送重量不可修改（赠送原因必填）</span></p><p><span>3，开启赠送填写信息后，再关闭赠送，则清空填写的赠送信息，关闭菜品赠送操作</span></p><p><span>4，点击确定按钮后，提交更改并生效（本地修改生效）</span></p>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="u5763_state3" class="panel_state" data-label="关闭赠送（非称重商品）" style="display:none; visibility: hidden;">
                  <div id="u5763_state3_content" class="panel_state_content">

                    <!-- 赠送菜品 (组合) -->
                    <div id="u5874" class="ax_default" data-label="赠送菜品" data-width="530" data-height="310">

                      <!-- 关闭赠送 (组合) -->
                      <div id="u5875" class="ax_default" data-label="关闭赠送" data-width="530" data-height="235">

                        <!-- 赠送数量 (组合) -->
                        <div id="u5876" class="ax_default" data-label="赠送数量" data-width="265" data-height="85">

                          <!-- Unnamed (矩形) -->
                          <div id="u5877" class="ax_default _三级标题">
                            <div id="u5877_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5878" class="text">
                              <p><span>已赠送数量</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5879" class="ax_default box_1">
                            <div id="u5879_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5880" class="text">
                              <p><span>1</span></p>
                            </div>
                          </div>
                        </div>

                        <!-- 赠送原因 (组合) -->
                        <div id="u5881" class="ax_default" data-label="赠送原因" data-width="530" data-height="120">

                          <!-- Unnamed (矩形) -->
                          <div id="u5882" class="ax_default _三级标题">
                            <div id="u5882_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5883" class="text">
                              <p><span>赠送原因</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5884" class="ax_default box_1">
                            <div id="u5884_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5885" class="text" style="display:none; visibility: hidden">
                              <p><span></span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5886" class="ax_default label">
                            <div id="u5886_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5887" class="text">
                              <p><span>菜品质量问题</span></p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 赠送开关 (组合) -->
                      <div id="u5888" class="ax_default" data-label="赠送开关" data-width="205" data-height="32">

                        <!-- Unnamed (矩形) -->
                        <div id="u5889" class="ax_default _三级标题">
                          <div id="u5889_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5890" class="text">
                            <p><span>赠送菜品</span></p>
                          </div>
                        </div>

                        <!-- SwitchGroup (组合) -->
                        <div id="u5891" class="ax_default" data-label="SwitchGroup" data-width="70" data-height="32">

                          <!-- Border (矩形) -->
                          <div id="u5892" class="ax_default box_3" data-label="Border">
                            <div id="u5892_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5893" class="text">
                              <p><span>ON</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (椭圆形) -->
                          <div id="u5894" class="ax_default ellipse">
                            <img id="u5894_img" class="img " src="images/点餐-选择商品/u5646.png"/>
                            <!-- Unnamed () -->
                            <div id="u5895" class="text" style="display:none; visibility: hidden">
                              <p><span></span></p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5896" class="ax_default label">
                      <div id="u5896_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5897" class="text">
                        <p><span>赠送商品的赠送操作说明：</span></p><p><span>1，默认开启赠送开关，该菜品已被赠送</span></p><p><span>2，显示赠送商品的已赠送数量和赠送原因信息，已赠送数量和原因不可编辑修改</span></p><p><span>3，关闭赠送后，隐藏已赠送数量及赠送原因信息</span></p><p><span>4，关闭赠送后，再开启赠送，则恢复已赠送数量及赠送原因信息显示，已赠送数量和原因不可编辑修改</span></p><p><span>5，点击确定按钮后，提交更改并生效（本地修改生效）</span></p>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="u5763_state4" class="panel_state" data-label="关闭赠送（称重商品）" style="display:none; visibility: hidden;">
                  <div id="u5763_state4_content" class="panel_state_content">

                    <!-- 赠送菜品 (组合) -->
                    <div id="u5898" class="ax_default" data-label="赠送菜品" data-width="530" data-height="310">

                      <!-- 关闭赠送 (组合) -->
                      <div id="u5899" class="ax_default" data-label="关闭赠送" data-width="530" data-height="235">

                        <!-- 赠送重量 (组合) -->
                        <div id="u5900" class="ax_default" data-label="赠送重量" data-width="265" data-height="85">

                          <!-- Unnamed (矩形) -->
                          <div id="u5901" class="ax_default _三级标题">
                            <div id="u5901_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5902" class="text">
                              <p><span>已赠送重量</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5903" class="ax_default box_1">
                            <div id="u5903_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5904" class="text">
                              <p><span>1.250kg</span></p>
                            </div>
                          </div>
                        </div>

                        <!-- 赠送原因 (组合) -->
                        <div id="u5905" class="ax_default" data-label="赠送原因" data-width="530" data-height="120">

                          <!-- Unnamed (矩形) -->
                          <div id="u5906" class="ax_default _三级标题">
                            <div id="u5906_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5907" class="text">
                              <p><span>赠送原因</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5908" class="ax_default box_1">
                            <div id="u5908_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5909" class="text" style="display:none; visibility: hidden">
                              <p><span></span></p>
                            </div>
                          </div>

                          <!-- Unnamed (矩形) -->
                          <div id="u5910" class="ax_default label">
                            <div id="u5910_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5911" class="text">
                              <p><span>菜品质量问题</span></p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 赠送开关 (组合) -->
                      <div id="u5912" class="ax_default" data-label="赠送开关" data-width="205" data-height="32">

                        <!-- Unnamed (矩形) -->
                        <div id="u5913" class="ax_default _三级标题">
                          <div id="u5913_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5914" class="text">
                            <p><span>赠送菜品</span></p>
                          </div>
                        </div>

                        <!-- SwitchGroup (组合) -->
                        <div id="u5915" class="ax_default" data-label="SwitchGroup" data-width="70" data-height="32">

                          <!-- Border (矩形) -->
                          <div id="u5916" class="ax_default box_3" data-label="Border">
                            <div id="u5916_div" class=""></div>
                            <!-- Unnamed () -->
                            <div id="u5917" class="text">
                              <p><span>ON</span></p>
                            </div>
                          </div>

                          <!-- Unnamed (椭圆形) -->
                          <div id="u5918" class="ax_default ellipse">
                            <img id="u5918_img" class="img " src="images/点餐-选择商品/u5646.png"/>
                            <!-- Unnamed () -->
                            <div id="u5919" class="text" style="display:none; visibility: hidden">
                              <p><span></span></p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u5920" class="ax_default label">
                      <div id="u5920_div" class=""></div>
                      <!-- Unnamed () -->
                      <div id="u5921" class="text">
                        <p><span>赠送称重商品的赠送操作说明：</span></p><p><span>1，默认开启赠送开关，该菜品已被赠送</span></p><p><span>2，显示赠送商品的已赠送重量和赠送原因信息，已赠送重量和原因不可编辑修改</span></p><p><span>3，关闭赠送后，隐藏已赠送重量及赠送原因信息</span></p><p><span>4，关闭赠送后，再开启赠送，则恢复已赠送重量及赠送原因信息显示，已赠送重量和原因不可编辑修改</span></p><p><span>5，点击确定按钮后，提交更改并生效（本地修改生效）</span></p>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="u5763_state5" class="panel_state" data-label="折扣/改价" style="display:none; visibility: hidden;">
                  <div id="u5763_state5_content" class="panel_state_content">

                    <!-- 折扣改价 (组合) -->
                    <div id="u5922" class="ax_default" data-label="折扣改价" data-width="446" data-height="529">

                      <!-- 折扣方式 (组合) -->
                      <div id="u5923" class="ax_default" data-label="折扣方式" data-width="446" data-height="85">

                        <!-- Unnamed (矩形) -->
                        <div id="u5924" class="ax_default _三级标题">
                          <div id="u5924_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5925" class="text">
                            <p><span>折扣方式</span></p>
                          </div>
                        </div>

                        <!-- 单品折扣 (矩形) -->
                        <div id="u5926" class="ax_default box_1 selected" data-label="单品折扣" selectiongroup="tab3">
                          <div id="u5926_div" class="selected"></div>
                          <!-- Unnamed () -->
                          <div id="u5927" class="text">
                            <p><span>单品折扣</span></p>
                          </div>
                        </div>

                        <!-- 单品改价 (矩形) -->
                        <div id="u5928" class="ax_default box_1" data-label="单品改价" selectiongroup="tab3">
                          <div id="u5928_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5929" class="text">
                            <p><span>单品改价</span></p>
                          </div>
                        </div>

                        <!-- 恢复原价 (矩形) -->
                        <div id="u5930" class="ax_default box_1" data-label="恢复原价" selectiongroup="tab3">
                          <div id="u5930_div" class=""></div>
                          <!-- Unnamed () -->
                          <div id="u5931" class="text">
                            <p><span>恢复原价</span></p>
                          </div>
                        </div>
                      </div>

                      <!-- 折扣明细 (动态面板) -->
                      <div id="u5932" class="ax_default" data-label="折扣明细">
                        <div id="u5932_state0" class="panel_state" data-label="单品折扣">
                          <div id="u5932_state0_content" class="panel_state_content">

                            <!-- Unnamed (矩形) -->
                            <div id="u5933" class="ax_default _三级标题">
                              <div id="u5933_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5934" class="text">
                                <p><span>折扣值</span></p>
                              </div>
                            </div>

                            <!-- Unnamed (组合) -->
                            <div id="u5935" class="ax_default" data-width="440" data-height="290">

                              <!-- Unnamed (矩形) -->
                              <div id="u5936" class="ax_default button">
                                <div id="u5936_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5937" class="text">
                                  <p><span>1</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5938" class="ax_default button">
                                <div id="u5938_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5939" class="text">
                                  <p><span>2</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5940" class="ax_default button">
                                <div id="u5940_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5941" class="text">
                                  <p><span>3</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5942" class="ax_default button">
                                <div id="u5942_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5943" class="text">
                                  <p><span>4</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5944" class="ax_default button">
                                <div id="u5944_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5945" class="text">
                                  <p><span>5</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5946" class="ax_default button">
                                <div id="u5946_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5947" class="text">
                                  <p><span>6</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5948" class="ax_default button">
                                <div id="u5948_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5949" class="text">
                                  <p><span>7</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5950" class="ax_default button">
                                <div id="u5950_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5951" class="text">
                                  <p><span>8</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5952" class="ax_default button">
                                <div id="u5952_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5953" class="text">
                                  <p><span>9</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5954" class="ax_default button">
                                <div id="u5954_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5955" class="text">
                                  <p><span>.</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5956" class="ax_default button">
                                <div id="u5956_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5957" class="text">
                                  <p><span>0</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5958" class="ax_default button">
                                <div id="u5958_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5959" class="text">
                                  <p><span>退格</span></p>
                                </div>
                              </div>
                            </div>

                            <!-- Unnamed (文本框) -->
                            <div id="u5960" class="ax_default text_field">
                              <input id="u5960_input" type="text" value=""/>
                            </div>
                          </div>
                        </div>
                        <div id="u5932_state1" class="panel_state" data-label="单品改价" style="display:none; visibility: hidden;">
                          <div id="u5932_state1_content" class="panel_state_content">

                            <!-- Unnamed (矩形) -->
                            <div id="u5961" class="ax_default _三级标题">
                              <div id="u5961_div" class=""></div>
                              <!-- Unnamed () -->
                              <div id="u5962" class="text">
                                <p><span>改价金额</span></p>
                              </div>
                            </div>

                            <!-- Unnamed (组合) -->
                            <div id="u5963" class="ax_default" data-width="440" data-height="290">

                              <!-- Unnamed (矩形) -->
                              <div id="u5964" class="ax_default button">
                                <div id="u5964_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5965" class="text">
                                  <p><span>1</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5966" class="ax_default button">
                                <div id="u5966_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5967" class="text">
                                  <p><span>2</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5968" class="ax_default button">
                                <div id="u5968_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5969" class="text">
                                  <p><span>3</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5970" class="ax_default button">
                                <div id="u5970_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5971" class="text">
                                  <p><span>4</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5972" class="ax_default button">
                                <div id="u5972_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5973" class="text">
                                  <p><span>5</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5974" class="ax_default button">
                                <div id="u5974_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5975" class="text">
                                  <p><span>6</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5976" class="ax_default button">
                                <div id="u5976_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5977" class="text">
                                  <p><span>7</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5978" class="ax_default button">
                                <div id="u5978_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5979" class="text">
                                  <p><span>8</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5980" class="ax_default button">
                                <div id="u5980_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5981" class="text">
                                  <p><span>9</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5982" class="ax_default button">
                                <div id="u5982_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5983" class="text">
                                  <p><span>.</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5984" class="ax_default button">
                                <div id="u5984_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5985" class="text">
                                  <p><span>0</span></p>
                                </div>
                              </div>

                              <!-- Unnamed (矩形) -->
                              <div id="u5986" class="ax_default button">
                                <div id="u5986_div" class=""></div>
                                <!-- Unnamed () -->
                                <div id="u5987" class="text">
                                  <p><span>退格</span></p>
                                </div>
                              </div>
                            </div>

                            <!-- Unnamed (文本框) -->
                            <div id="u5988" class="ax_default text_field">
                              <input id="u5988_input" type="text" value="¥12"/>
                            </div>
                          </div>
                        </div>
                        <div id="u5932_state2" class="panel_state" data-label="恢复原价" style="display:none; visibility: hidden;">
                          <div id="u5932_state2_content" class="panel_state_content">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 功能点 (组合) -->
              <div id="u5989" class="ax_default" data-label="功能点" data-width="558" data-height="55">

                <!-- 属性/备注 (矩形) -->
                <div id="u5990" class="ax_default box_1 selected" data-label="属性/备注" selectiongroup="tab1">
                  <div id="u5990_div" class="selected"></div>
                  <!-- Unnamed () -->
                  <div id="u5991" class="text">
                    <p><span>商品编辑</span></p>
                  </div>
                </div>

                <!-- 赠送 (矩形) -->
                <div id="u5992" class="ax_default box_1" data-label="赠送" selectiongroup="tab1">
                  <div id="u5992_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5993" class="text">
                    <p><span>赠送</span></p>
                  </div>
                </div>

                <!-- 折扣/改价 (矩形) -->
                <div id="u5994" class="ax_default box_1" data-label="折扣/改价" selectiongroup="tab1">
                  <div id="u5994_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5995" class="text">
                    <p><span>折扣/改价</span></p>
                  </div>
                </div>

                <!-- 无 (矩形) -->
                <div id="u5996" class="ax_default box_1" data-label="无" selectiongroup="tab1">
                  <div id="u5996_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u5997" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div id="u5529_state2" class="panel_state" data-label="称重商品" style="display:none; visibility: hidden;">
          <div id="u5529_state2_content" class="panel_state_content">

            <!-- 商品称重 (组合) -->
            <div id="u5998" class="ax_default" data-label="商品称重" data-width="550" data-height="595">

              <!-- Unnamed (矩形) -->
              <div id="u5999" class="ax_default box_1">
                <div id="u5999_div" class=""></div>
                <!-- Unnamed () -->
                <div id="u6000" class="text" style="display:none; visibility: hidden">
                  <p><span></span></p>
                </div>
              </div>

              <!-- Unnamed (组合) -->
              <div id="u6001" class="ax_default" data-width="548" data-height="79">

                <!-- Unnamed (矩形) -->
                <div id="u6002" class="ax_default box_2">
                  <div id="u6002_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6003" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u6004" class="ax_default icon">
                  <img id="u6004_img" class="img " src="images/转台/返回符号_u918.png"/>
                  <!-- Unnamed () -->
                  <div id="u6005" class="text" style="display:none; visibility: hidden">
                    <p><span></span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6006" class="ax_default _二级标题">
                  <div id="u6006_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6007" class="text">
                    <p><span>称重商品</span></p>
                  </div>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u6008" class="ax_default box_2">
                <div id="u6008_div" class=""></div>
                <!-- Unnamed () -->
                <div id="u6009" class="text">
                  <p><span>确定</span></p>
                </div>
              </div>

              <!-- Unnamed (组合) -->
              <div id="u6010" class="ax_default" data-width="440" data-height="290">

                <!-- Unnamed (矩形) -->
                <div id="u6011" class="ax_default button">
                  <div id="u6011_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6012" class="text">
                    <p><span>1</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6013" class="ax_default button">
                  <div id="u6013_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6014" class="text">
                    <p><span>2</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6015" class="ax_default button">
                  <div id="u6015_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6016" class="text">
                    <p><span>3</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6017" class="ax_default button">
                  <div id="u6017_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6018" class="text">
                    <p><span>4</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6019" class="ax_default button">
                  <div id="u6019_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6020" class="text">
                    <p><span>5</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6021" class="ax_default button">
                  <div id="u6021_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6022" class="text">
                    <p><span>6</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6023" class="ax_default button">
                  <div id="u6023_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6024" class="text">
                    <p><span>7</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6025" class="ax_default button">
                  <div id="u6025_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6026" class="text">
                    <p><span>8</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6027" class="ax_default button">
                  <div id="u6027_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6028" class="text">
                    <p><span>9</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6029" class="ax_default button">
                  <div id="u6029_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6030" class="text">
                    <p><span>.</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6031" class="ax_default button">
                  <div id="u6031_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6032" class="text">
                    <p><span>0</span></p>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u6033" class="ax_default button">
                  <div id="u6033_div" class=""></div>
                  <!-- Unnamed () -->
                  <div id="u6034" class="text">
                    <p><span>退格</span></p>
                  </div>
                </div>
              </div>

              <!-- Unnamed (文本框) -->
              <div id="u6035" class="ax_default text_field">
                <input id="u6035_input" type="text" value=""/>
              </div>
            </div>

            <!-- Unnamed (热区) -->
            <div id="u6036" class="ax_default">
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6037" class="ax_default label">
        <div id="u6037_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u6038" class="text">
          <p><span>下单打厨：商品下单，并打印后厨制作单（点菜单）</span></p>
        </div>
      </div>

      <!-- Unnamed (连接线) -->
      <div id="u6039" class="ax_default _连接线">
        <img id="u6039_seg0" class="img " src="images/点餐-选择商品/u6039_seg0.png" alt="u6039_seg0"/>
        <img id="u6039_seg1" class="img " src="images/点餐-选择商品/u6039_seg1.png" alt="u6039_seg1"/>
        <img id="u6039_seg2" class="img " src="images/并台/u1219_seg2.png" alt="u6039_seg2"/>
        <!-- Unnamed () -->
        <div id="u6040" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6041" class="ax_default label">
        <div id="u6041_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u6042" class="text">
          <p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">点餐说明：</span></p><p><span style="font-family:'Arial-BoldMT', 'Arial Bold', 'Arial';font-weight:700;color:#0000FF;">判断商品是否存在必选属性，如果存在，则点餐时每次必弹商品编辑操作框；</span></p><p><span style="font-family:'Arial-BoldMT', 'Arial Bold', 'Arial';font-weight:700;color:#0000FF;">如果不存在，则点餐时按照下述说明的操作流程显示</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;"></span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">1，</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;color:#FF0000;">普通商品</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;">：</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">——点击商品直接加入商品已选列表</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">——重复连续点击则叠加商品已选数量，商品信息不一致时则不叠加</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">（信息判断包含：属性/备注，赠送/取消赠送，折扣/改价，退菜，规格，套餐子菜品等，除数量外的信息）</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">2，</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;color:#FF0000;">规格商品</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;">：</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">——点击商品弹出规格选框，可多选规格也可以操作不同规格的数量加减，确认规格选择后才加入商品已选列表（多个规格被选中时，按照规格显示顺序倒序排序加入已选列表）</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">——重复点击需要再次弹出规格选框，规格商品信息一致，点击“下单打厨”提交订单则叠加商品</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">（信息判断包含：属性/备注，赠送/取消赠送，折扣/改价，退菜，规格，套餐子菜品等，除数量外的信息）</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">3，</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;color:#FF0000;">称重商品</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;">：</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">——点击商品弹出称重输入框，连接电子秤时则自动称重不允许手动输入，如果未连接电子秤则手动输入重量值，确认称重商品重量值后才加入商品已选列表</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">——重复点击需要再次弹出称重输入框，称重商品不做已选数量叠加</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">4，</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;color:#FF0000;">套餐商品（固定套餐）</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;">：</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">——点击商品直接加入商品已选列表</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;color:#0000FF;">——固定套餐不支持套餐子菜品做加价</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;"></span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">——重复连续点击则叠加商品已选数量，商品信息（包含子菜品）不一致时则不叠加</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">（信息判断包含：属性/备注，赠送/取消赠送，折扣/改价，退菜，规格，套餐子菜品等，除数量外的信息）</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">5，</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;color:#FF0000;">套餐商品（可选套餐）</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;">：</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">——点击商品弹出套餐子菜品选择框，可操作套餐子菜品选择（选择数量不可大于当前组的可选数量，同一个子菜品是否可重复选择根据商户后台配置规则判定，可选成分至少需要选择一个），确认套餐子菜品成分后才加入商品已选列表</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;color:#0000FF;">——可选套餐固定组不支持套餐子菜品做加价</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;color:#0000FF;">——可选套餐可选组支持套餐子菜品加价，加价按照套餐子菜品选择数量为计算基数，而非配置数量*选择数量（套餐子菜品界面显示的价格，是商户后台配置的加价金额）</span><span style="font-family:'ArialMT', 'Arial';font-weight:400;"></span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">——重复点击需要再次弹出套餐子菜品选择框，可选套餐不做已选数量叠加</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;"><br></span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">特殊说明：</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">1，已选商品列表按照点餐顺序倒序排序，最新商品始终处于展开编辑状态，手动点击列表商品可切换显示展开/收起编辑状态</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">2，已选列表只能保留1项商品处于选中编辑状态，切换选中商品时，需要收起上一个商品的编辑状态，展开新选中商品的编辑状态</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">3，点击未下单商品列表的商品，在商品列表展开编辑列，可操作商品数量修改和删除功能</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">4，点击“加减”符号，可操作已选数量加减，点击数量框，可弹出数量修改弹框，输入数量值后覆盖原数量</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">5，点击“删除”符号，删除符合变为红色，再次点击直接从已选列表删除该商品（无需确认弹框），连续点击2次直接删除</span></p><p><span style="font-family:'ArialMT', 'Arial';font-weight:400;">6，点击“编辑”符号，弹出商品更多功能编辑框，可操作“属性/备注菜”，“赠送/取消赠送”，“折扣/改价”等功能</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6043" class="ax_default box_2">
        <div id="u6043_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u6044" class="text" style="display:none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6045" class="ax_default _三级标题">
        <div id="u6045_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u6046" class="text">
          <p><span>当前订单存在未下单的商品，退出后会导致商品丢失！</span></p><p><span>确定要退出吗？</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6047" class="ax_default box_2">
        <div id="u6047_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u6048" class="text">
          <p><span>取消</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6049" class="ax_default box_2">
        <div id="u6049_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u6050" class="text">
          <p><span>退出</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
