<!DOCTYPE html>
<html>
  <head>
    <title>全部属性</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/全部属性/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/全部属性/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (管理菜品) -->

      <!-- Unnamed (Rectangle) -->
      <div id="u16885" class="ax_default box_3">
        <div id="u16885_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u16886" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 管理顾客 (Table) -->
      <div id="u16887" class="ax_default" data-label="管理顾客">

        <!-- Unnamed (Table Cell) -->
        <div id="u16888" class="ax_default table_cell">
          <img id="u16888_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16889" class="text" style="visibility: visible;">
            <p><span>品牌库</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16890" class="ax_default table_cell">
          <img id="u16890_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16891" class="text" style="visibility: visible;">
            <p><span>&nbsp;&nbsp; &nbsp; 商品库</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16892" class="ax_default table_cell">
          <img id="u16892_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16893" class="text" style="visibility: visible;">
            <p><span>&nbsp;&nbsp; &nbsp; 属性库</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16894" class="ax_default table_cell">
          <img id="u16894_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16895" class="text" style="visibility: visible;">
            <p><span>门店商品</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16896" class="ax_default table_cell">
          <img id="u16896_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16897" class="text" style="visibility: visible;">
            <p><span>&nbsp;&nbsp; &nbsp; 商品列表</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16898" class="ax_default table_cell">
          <img id="u16898_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16899" class="text" style="visibility: visible;">
            <p><span>&nbsp;&nbsp; &nbsp; 属性列表</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16900" class="ax_default table_cell">
          <img id="u16900_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16901" class="text" style="visibility: visible;">
            <p><span>&nbsp;&nbsp; &nbsp; 扫码点餐</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16902" class="ax_default table_cell">
          <img id="u16902_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16903" class="text" style="visibility: visible;">
            <p><span>&nbsp;&nbsp; &nbsp; 美团</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16904" class="ax_default table_cell">
          <img id="u16904_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16905" class="text" style="visibility: visible;">
            <p><span>&nbsp;&nbsp; &nbsp; 饿了么</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u16906" class="ax_default horizontal_line">
        <img id="u16906_img" class="img " src="images/全部商品_商品库_/u3183.png"/>
        <!-- Unnamed () -->
        <div id="u16907" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (主框架) -->

      <!-- Unnamed (Rectangle) -->
      <div id="u16909" class="ax_default box_3">
        <div id="u16909_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u16910" class="text" style="visibility: visible;">
          <p><span style="font-family:'ArialMT', 'Arial';">&nbsp;&nbsp; </span><span style="font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';">Holder </span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u16911" class="ax_default box_3">
        <div id="u16911_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u16912" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u16913" class="ax_default paragraph">
        <div id="u16913_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u16914" class="text" style="visibility: visible;">
          <p><span>账号/员姓</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u16915" class="ax_default table_cell">
        <div id="u16915_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u16916" class="text" style="visibility: visible;">
          <p><span>消息 </span><span style="color:#FF0000;">99+</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u16917" class="ax_default paragraph">
        <img id="u16917_img" class="img " src="images/首页-未创建菜品/u546.png"/>
        <!-- Unnamed () -->
        <div id="u16918" class="text" style="visibility: visible;">
          <p><span>门店管理后台</span></p>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u16919" class="ax_default horizontal_line">
        <img id="u16919_img" class="img " src="images/首页-未创建菜品/u548.png"/>
        <!-- Unnamed () -->
        <div id="u16920" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u16921" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u16922" class="ax_default table_cell">
          <img id="u16922_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16923" class="text" style="visibility: visible;">
            <p><span>首页</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16924" class="ax_default table_cell">
          <img id="u16924_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16925" class="text" style="visibility: visible;">
            <p><span>门店及员工</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16926" class="ax_default table_cell">
          <img id="u16926_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16927" class="text" style="visibility: visible;">
            <p><span>会员顾客</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16928" class="ax_default table_cell">
          <img id="u16928_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16929" class="text" style="visibility: visible;">
            <p><span>商品销售</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16930" class="ax_default table_cell">
          <img id="u16930_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16931" class="text" style="visibility: visible;">
            <p><span>活动营销</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16932" class="ax_default table_cell">
          <img id="u16932_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16933" class="text" style="visibility: visible;">
            <p><span>报表中心</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16934" class="ax_default table_cell">
          <img id="u16934_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16935" class="text" style="visibility: visible;">
            <p><span>更多配置</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u16936" class="ax_default box_2">
        <div id="u16936_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u16937" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u16938" class="ax_default horizontal_line">
        <img id="u16938_img" class="img " src="images/员工列表/u631.png"/>
        <!-- Unnamed () -->
        <div id="u16939" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (tab栏) -->

      <!-- Unnamed (Rectangle) -->
      <div id="u16941" class="ax_default box_3">
        <div id="u16941_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u16942" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 门店及员工 (Table) -->
      <div id="u16943" class="ax_default" data-label="门店及员工">

        <!-- Unnamed (Table Cell) -->
        <div id="u16944" class="ax_default table_cell">
          <img id="u16944_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16945" class="text" style="visibility: visible;">
            <p><span>&nbsp;&nbsp; &nbsp; 属性列表</span></p>
          </div>
        </div>
      </div>

      <!-- 门店及员工 (Table) -->
      <div id="u16946" class="ax_default" data-label="门店及员工">

        <!-- Unnamed (Table Cell) -->
        <div id="u16947" class="ax_default table_cell">
          <img id="u16947_img" class="img " src="images/全部商品_商品库_/u3355.png"/>
          <!-- Unnamed () -->
          <div id="u16948" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u16949" class="ax_default paragraph">
        <img id="u16949_img" class="img " src="images/员工列表/u846.png"/>
        <!-- Unnamed () -->
        <div id="u16950" class="text" style="visibility: visible;">
          <p><span>属性列表</span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u16951" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u16952" class="ax_default table_cell">
          <img id="u16952_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u16953" class="text" style="visibility: visible;">
            <p><span>暂无品牌</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u16954" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u16955" class="ax_default table_cell">
          <img id="u16955_img" class="img " src="images/企业品牌/u2963.png"/>
          <!-- Unnamed () -->
          <div id="u16956" class="text" style="visibility: visible;">
            <p><span>全部属性组</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16957" class="ax_default table_cell">
          <img id="u16957_img" class="img " src="images/企业品牌/u2963.png"/>
          <!-- Unnamed () -->
          <div id="u16958" class="text" style="visibility: visible;">
            <p><span>温度</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16959" class="ax_default table_cell">
          <img id="u16959_img" class="img " src="images/企业品牌/u2965.png"/>
          <!-- Unnamed () -->
          <div id="u16960" class="text" style="visibility: visible;">
            <p><span>口味</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16961" class="ax_default table_cell">
          <img id="u16961_img" class="img " src="images/企业品牌/u2963.png"/>
          <!-- Unnamed () -->
          <div id="u16962" class="text" style="visibility: visible;">
            <p><span>🍆加料一组</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16963" class="ax_default table_cell">
          <img id="u16963_img" class="img " src="images/企业品牌/u2969.png"/>
          <!-- Unnamed () -->
          <div id="u16964" class="text" style="visibility: visible;">
            <p><span>🍅加料二组</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16965" class="ax_default table_cell">
          <img id="u16965_img" class="img " src="images/企业品牌/u2963.png"/>
          <!-- Unnamed () -->
          <div id="u16966" class="text" style="visibility: visible;">
            <p><span>🌶加料三组</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16967" class="ax_default table_cell">
          <img id="u16967_img" class="img " src="images/企业品牌/u2963.png"/>
          <!-- Unnamed () -->
          <div id="u16968" class="text" style="visibility: visible;">
            <p><span>做法</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16969" class="ax_default table_cell">
          <img id="u16969_img" class="img " src="images/企业品牌/u2963.png"/>
          <!-- Unnamed () -->
          <div id="u16970" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u16971" class="ax_default table">

        <!-- Unnamed (Table Cell) -->
        <div id="u16972" class="ax_default table_cell">
          <img id="u16972_img" class="img " src="images/企业品牌/u2978.png"/>
          <!-- Unnamed () -->
          <div id="u16973" class="text" style="visibility: visible;">
            <p><span>✎&nbsp; ×</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u16974" class="ax_default horizontal_line">
        <img id="u16974_img" class="img " src="images/组织机构/u2010.png"/>
        <!-- Unnamed () -->
        <div id="u16975" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u16976" class="ax_default paragraph">
        <img id="u16976_img" class="img " src="images/全部商品_商品库_/u3400.png"/>
        <!-- Unnamed () -->
        <div id="u16977" class="text" style="visibility: visible;">
          <p><span>+</span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u16978" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u16979" class="ax_default table_cell">
          <img id="u16979_img" class="img " src="images/属性库/u14214.png"/>
          <!-- Unnamed () -->
          <div id="u16980" class="text" style="visibility: visible;">
            <p><span>属性名称</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16981" class="ax_default table_cell">
          <img id="u16981_img" class="img " src="images/属性库/u14216.png"/>
          <!-- Unnamed () -->
          <div id="u16982" class="text" style="visibility: visible;">
            <p><span>加价属性</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16983" class="ax_default table_cell">
          <img id="u16983_img" class="img " src="images/属性库/u14218.png"/>
          <!-- Unnamed () -->
          <div id="u16984" class="text" style="visibility: visible;">
            <p><span>适用商品分类</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16985" class="ax_default table_cell">
          <img id="u16985_img" class="img " src="images/属性库/u14220.png"/>
          <!-- Unnamed () -->
          <div id="u16986" class="text" style="visibility: visible;">
            <p><span>操作</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16987" class="ax_default table_cell">
          <img id="u16987_img" class="img " src="images/属性库/u14222.png"/>
          <!-- Unnamed () -->
          <div id="u16988" class="text" style="visibility: visible;">
            <p><span>糖醋红烧</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16989" class="ax_default table_cell">
          <img id="u16989_img" class="img " src="images/属性库/u14224.png"/>
          <!-- Unnamed () -->
          <div id="u16990" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16991" class="ax_default table_cell">
          <img id="u16991_img" class="img " src="images/属性库/u14226.png"/>
          <!-- Unnamed () -->
          <div id="u16992" class="text" style="visibility: visible;">
            <p><span>荤菜(可烹炸蒸煮闷烧凉拌)，小吃，素菜，凉菜</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16993" class="ax_default table_cell">
          <img id="u16993_img" class="img " src="images/属性库/u14228.png"/>
          <!-- Unnamed () -->
          <div id="u16994" class="text" style="visibility: visible;">
            <p><span>修改</span><span> </span><span>删除</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16995" class="ax_default table_cell">
          <img id="u16995_img" class="img " src="images/属性库/u14222.png"/>
          <!-- Unnamed () -->
          <div id="u16996" class="text" style="visibility: visible;">
            <p><span>干烧苹果木碳味</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16997" class="ax_default table_cell">
          <img id="u16997_img" class="img " src="images/属性库/u14224.png"/>
          <!-- Unnamed () -->
          <div id="u16998" class="text" style="visibility: visible;">
            <p><span>￥2.00</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u16999" class="ax_default table_cell">
          <img id="u16999_img" class="img " src="images/属性库/u14226.png"/>
          <!-- Unnamed () -->
          <div id="u17000" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17001" class="ax_default table_cell">
          <img id="u17001_img" class="img " src="images/属性库/u14228.png"/>
          <!-- Unnamed () -->
          <div id="u17002" class="text" style="visibility: visible;">
            <p><span style="color:#999999;">来自品牌库不可修改</span><span style="color:#0000FF;"> 删除</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17003" class="ax_default table_cell">
          <img id="u17003_img" class="img " src="images/属性库/u14238.png"/>
          <!-- Unnamed () -->
          <div id="u17004" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17005" class="ax_default table_cell">
          <img id="u17005_img" class="img " src="images/属性库/u14240.png"/>
          <!-- Unnamed () -->
          <div id="u17006" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17007" class="ax_default table_cell">
          <img id="u17007_img" class="img " src="images/属性库/u14242.png"/>
          <!-- Unnamed () -->
          <div id="u17008" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17009" class="ax_default table_cell">
          <img id="u17009_img" class="img " src="images/属性库/u14244.png"/>
          <!-- Unnamed () -->
          <div id="u17010" class="text" style="visibility: visible;">
            <p><span>保存 取消</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17011" class="ax_default table_cell">
          <img id="u17011_img" class="img " src="images/属性库/u14246.png"/>
          <!-- Unnamed () -->
          <div id="u17012" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17013" class="ax_default table_cell">
          <img id="u17013_img" class="img " src="images/属性库/u14248.png"/>
          <!-- Unnamed () -->
          <div id="u17014" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17015" class="ax_default table_cell">
          <img id="u17015_img" class="img " src="images/属性库/u14250.png"/>
          <!-- Unnamed () -->
          <div id="u17016" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17017" class="ax_default table_cell">
          <img id="u17017_img" class="img " src="images/属性库/u14252.png"/>
          <!-- Unnamed () -->
          <div id="u17018" class="text" style="visibility: visible;">
            <p><span>保存 删除</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u17019" class="ax_default horizontal_line">
        <img id="u17019_img" class="img " src="images/属性库/u14254.png"/>
        <!-- Unnamed () -->
        <div id="u17020" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u17021" class="ax_default horizontal_line">
        <img id="u17021_img" class="img " src="images/组织机构/u1899.png"/>
        <!-- Unnamed () -->
        <div id="u17022" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u17023" class="ax_default table_cell">
        <input id="u17023_input" type="text" value="葡萄味原汁+老酸奶+布丁粒"/>
      </div>

      <!-- 主从 (Paragraph) -->
      <div id="u17024" class="ax_default box_2" data-label="主从">
        <img id="u17024_img" class="img " src="images/属性库/主从_u14261.png"/>
        <!-- Unnamed () -->
        <div id="u17025" class="text" style="visibility: visible;">
          <p><span>新建属性</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u17026" class="ax_default table_cell">
        <input id="u17026_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u17027" class="ax_default table_cell">
        <input id="u17027_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u17028" class="ax_default table_cell">
        <select id="u17028_input">
          <option selected value="选择商品分类">选择商品分类</option>
          <option value="荤菜(可烹炸蒸煮闷烧凉拌)">荤菜(可烹炸蒸煮闷烧凉拌)</option>
          <option value="素菜">素菜</option>
          <option value="凉菜">凉菜</option>
          <option value="小吃">小吃</option>
          <option value="饮料">饮料</option>
          <option value="酒类">酒类</option>
          <option value="主食">主食</option>
        </select>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u17029" class="ax_default paragraph">
        <label for="u17029_input">
          <!-- Unnamed () -->
          <div id="u17030" class="text" style="visibility: visible;">
            <p><span>加价</span></p>
          </div>
        </label>
        <input id="u17029_input" type="checkbox" value="checkbox" checked/>
      </div>

      <!-- 添加/编辑属性组 (Group) -->
      <div id="u17031" class="ax_default ax_default_hidden" data-label="添加/编辑属性组" style="display: none; visibility: hidden">

        <!-- Unnamed (Rectangle) -->
        <div id="u17032" class="ax_default box_1">
          <div id="u17032_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u17033" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u17034" class="ax_default box_2">
          <div id="u17034_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u17035" class="text" style="visibility: visible;">
            <p><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">添加</span><span style="font-family:'ArialMT', 'Arial';">/</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC';">编辑属性组</span></p>
          </div>
        </div>

        <!-- 主从 (Paragraph) -->
        <div id="u17036" class="ax_default paragraph" data-label="主从">
          <img id="u17036_img" class="img " src="images/员工列表/u823.png"/>
          <!-- Unnamed () -->
          <div id="u17037" class="text" style="visibility: visible;">
            <p><span>保存</span></p>
          </div>
        </div>

        <!-- 主从 (Paragraph) -->
        <div id="u17038" class="ax_default paragraph" data-label="主从">
          <img id="u17038_img" class="img " src="images/员工列表/u823.png"/>
          <!-- Unnamed () -->
          <div id="u17039" class="text" style="visibility: visible;">
            <p><span>取消</span></p>
          </div>
        </div>

        <!-- Unnamed (Table) -->
        <div id="u17040" class="ax_default">

          <!-- Unnamed (Table Cell) -->
          <div id="u17041" class="ax_default table_cell">
            <img id="u17041_img" class="img " src="images/全部商品_商品库_/u3421.png"/>
            <!-- Unnamed () -->
            <div id="u17042" class="text" style="visibility: visible;">
              <p><span>*属性名称：</span></p>
            </div>
          </div>

          <!-- Unnamed (Table Cell) -->
          <div id="u17043" class="ax_default table_cell">
            <img id="u17043_img" class="img " src="images/全部商品_商品库_/u3421.png"/>
            <!-- Unnamed () -->
            <div id="u17044" class="text" style="visibility: visible;">
              <p><span>排序：</span></p>
            </div>
          </div>

          <!-- Unnamed (Table Cell) -->
          <div id="u17045" class="ax_default table_cell">
            <img id="u17045_img" class="img " src="images/全部商品_商品库_/u3421.png"/>
            <!-- Unnamed () -->
            <div id="u17046" class="text" style="visibility: visible;">
              <p><span>描述：</span></p>
            </div>
          </div>

          <!-- Unnamed (Table Cell) -->
          <div id="u17047" class="ax_default table_cell">
            <img id="u17047_img" class="img " src="images/全部商品_商品库_/u3421.png"/>
            <!-- Unnamed () -->
            <div id="u17048" class="text" style="display: none; visibility: hidden">
              <p><span></span></p>
            </div>
          </div>

          <!-- Unnamed (Table Cell) -->
          <div id="u17049" class="ax_default table_cell">
            <img id="u17049_img" class="img " src="images/全部商品_商品库_/u3421.png"/>
            <!-- Unnamed () -->
            <div id="u17050" class="text" style="visibility: visible;">
              <p><span>状态：</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Text Field) -->
        <div id="u17051" class="ax_default table_cell">
          <input id="u17051_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Horizontal Line) -->
        <div id="u17052" class="ax_default horizontal_line">
          <img id="u17052_img" class="img " src="images/属性库/u14288.png"/>
          <!-- Unnamed () -->
          <div id="u17053" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Horizontal Line) -->
        <div id="u17054" class="ax_default horizontal_line">
          <img id="u17054_img" class="img " src="images/属性库/u14290.png"/>
          <!-- Unnamed () -->
          <div id="u17055" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Checkbox) -->
        <div id="u17056" class="ax_default paragraph">
          <label for="u17056_input">
            <!-- Unnamed () -->
            <div id="u17057" class="text" style="visibility: visible;">
              <p><span>启用</span></p>
            </div>
          </label>
          <input id="u17056_input" type="checkbox" value="checkbox" checked/>
        </div>

        <!-- Unnamed (Text Area) -->
        <div id="u17058" class="ax_default paragraph">
          <textarea id="u17058_input"></textarea>
        </div>

        <!-- Unnamed (Text Field) -->
        <div id="u17059" class="ax_default table_cell">
          <input id="u17059_input" type="text" value="11"/>
        </div>

        <!-- Unnamed (Horizontal Line) -->
        <div id="u17060" class="ax_default horizontal_line">
          <img id="u17060_img" class="img " src="images/属性库/u14288.png"/>
          <!-- Unnamed () -->
          <div id="u17061" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u17062" class="ax_default paragraph">
        <label for="u17062_input">
          <!-- Unnamed () -->
          <div id="u17063" class="text" style="visibility: visible;">
            <p><span>加价</span></p>
          </div>
        </label>
        <input id="u17062_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u17064" class="ax_default table">

        <!-- Unnamed (Table Cell) -->
        <div id="u17065" class="ax_default table_cell">
          <img id="u17065_img" class="img " src="images/企业品牌/u2978.png"/>
          <!-- Unnamed () -->
          <div id="u17066" class="text" style="visibility: visible;">
            <p><span style="color:#999999;">来自品牌库不可修改&nbsp; </span><span style="color:#0000FF;">×</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u17067" class="ax_default table_cell">
        <select id="u17067_input">
          <option value="选择商品分类">选择商品分类</option>
          <option selected value="荤菜(可烹炸蒸煮闷烧凉拌)">荤菜(可烹炸蒸煮闷烧凉拌)</option>
          <option value="素菜">素菜</option>
          <option value="凉菜">凉菜</option>
          <option value="小吃">小吃</option>
          <option value="饮料">饮料</option>
          <option value="酒类">酒类</option>
          <option value="主食">主食</option>
        </select>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u17068" class="ax_default paragraph">
        <img id="u17068_img" class="img " src="images/全部属性/u17068.png"/>
        <!-- Unnamed () -->
        <div id="u17069" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';font-weight:500;color:#1B5C57;">页面初始：</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;"></span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">三个属性组依次为：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">温度：去冰、少冰、多冰、常温、热</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">口味：不辣、微辣、中辣、特辣</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">做法：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;"><br></span></p><p><span style="font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';font-weight:500;color:#1B5C57;">新建/编辑属性组填写要求：</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;"></span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">1.名称，必填，1-20字，品牌下唯一</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">2.排序：必填，初始为品牌下最大顺序值，可修改，限整数，范围1-99999999</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">3.描述：选填，不超过50字</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">4.状态：默认启用(</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#FF0000;">页面不显示</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">)</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;"><br></span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;"><br></span></p><p><span style="font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';font-weight:500;color:#1B5C57;">新建/编辑属性值填写</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC';font-weight:400;color:#1B5C57;">内容：</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;"></span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">1.名称：必填，1-20字，分组内唯一</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">2.加价：默认未勾选，已勾选显示价格输入框，输入范围0.01-99.99；</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">3.适用商品分类： 可选，一级多选；当设置后，前端点餐时，分类下商品自动获取属性组及内容项；如果商品本身绑定有属性，以自身内容为准</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;"><br></span></p><p><span style="font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';font-weight:500;color:#1B5C57;">保存时：</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;"></span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">将保存到同一品牌下内容唯一，</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">♦如内容重复，停在当前内容，提示&quot;</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#FF0000;">内容已存在，请修改</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">&quot;</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">♦如其他失败原因，正确反馈失败原因</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">♦保存成功，刷新数据</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;"><br></span></p><p><span style="font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';font-weight:500;color:#1B5C57;">删除属性/属性组：</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;"></span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#1B5C57;">内容进行二次确认删除</span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u17070" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u17071" class="ax_default table_cell">
          <img id="u17071_img" class="img " src="images/员工列表/u851.png"/>
          <!-- Unnamed () -->
          <div id="u17072" class="text" style="visibility: visible;">
            <p><span>业务模块</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17073" class="ax_default table_cell">
          <img id="u17073_img" class="img " src="images/全部商品_商品库_/u3447.png"/>
          <!-- Unnamed () -->
          <div id="u17074" class="text" style="visibility: visible;">
            <p><span>门店商品</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17075" class="ax_default table_cell">
          <img id="u17075_img" class="img " src="images/员工列表/u851.png"/>
          <!-- Unnamed () -->
          <div id="u17076" class="text" style="visibility: visible;">
            <p><span>页面</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17077" class="ax_default table_cell">
          <img id="u17077_img" class="img " src="images/全部商品_商品库_/u3447.png"/>
          <!-- Unnamed () -->
          <div id="u17078" class="text" style="visibility: visible;">
            <p><span>属性列表</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17079" class="ax_default table_cell">
          <img id="u17079_img" class="img " src="images/员工列表/u851.png"/>
          <!-- Unnamed () -->
          <div id="u17080" class="text" style="visibility: visible;">
            <p><span>类型</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17081" class="ax_default table_cell">
          <img id="u17081_img" class="img " src="images/全部商品_商品库_/u3447.png"/>
          <!-- Unnamed () -->
          <div id="u17082" class="text" style="visibility: visible;">
            <p><span>权限页面</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17083" class="ax_default table_cell">
          <img id="u17083_img" class="img " src="images/员工列表/u863.png"/>
          <!-- Unnamed () -->
          <div id="u17084" class="text" style="visibility: visible;">
            <p><span>操作</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17085" class="ax_default table_cell">
          <img id="u17085_img" class="img " src="images/全部属性/u17085.png"/>
          <!-- Unnamed () -->
          <div id="u17086" class="text" style="visibility: visible;">
            <p><span>新建属性组、编辑属性组、添加属性、删除属性组、设置属性规则、编辑属性、删除属性</span></p><p><span>编辑推送属性组、删除推送属性组、设置推送属性规则、编辑推送属性、删除推送属性</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u17087" class="ax_default paragraph">
        <img id="u17087_img" class="img " src="images/添加_编辑单品-初始/u4565.png"/>
        <!-- Unnamed () -->
        <div id="u17088" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';font-weight:500;">埋点说明：</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">页面各操作的热力图分析</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u17089" class="ax_default paragraph">
        <img id="u17089_img" class="img " src="images/找回密码-输入账号获取验证码/u483.png"/>
        <!-- Unnamed () -->
        <div id="u17090" class="text" style="visibility: visible;">
          <p><span>权限说明：</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u17091" class="ax_default paragraph">
        <img id="u17091_img" class="img " src="images/属性库/u14258.png"/>
        <!-- Unnamed () -->
        <div id="u17092" class="text" style="visibility: visible;">
          <p><span>管理属性内容_口味（必选、单选、有默认勾选属性）</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u17093" class="ax_default paragraph">
        <img id="u17093_img" class="img " src="images/添加_编辑单品-初始/u3611.png"/>
        <!-- Unnamed () -->
        <div id="u17094" class="text" style="visibility: visible;">
          <p><span>设置 </span></p>
        </div>
      </div>

      <!-- Unnamed (下拉及联想输入单选门店) -->

      <!-- Unnamed (Paragraph) -->
      <div id="u17096" class="ax_default paragraph">
        <img id="u17096_img" class="img " src="images/桌位管理/u2654.png"/>
        <!-- Unnamed () -->
        <div id="u17097" class="text" style="visibility: visible;">
          <p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">门店：玉米熊阿里巴巴基地…&nbsp;&nbsp; </span><span style="font-family:'ArialMT', 'Arial';font-weight:400;font-size:10px;">▼</span></p>
        </div>
      </div>

      <!-- 选择门店 (Group) -->
      <div id="u17098" class="ax_default ax_default_hidden" data-label="选择门店" style="display: none; visibility: hidden">

        <!-- Unnamed (Rectangle) -->
        <div id="u17099" class="ax_default box_1">
          <div id="u17099_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u17100" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u17101" class="ax_default paragraph">
          <img id="u17101_img" class="img " src="images/员工列表/u823.png"/>
          <!-- Unnamed () -->
          <div id="u17102" class="text" style="visibility: visible;">
            <p><span>查询</span></p>
          </div>
        </div>

        <!-- Unnamed (Text Field) -->
        <div id="u17103" class="ax_default table_cell">
          <input id="u17103_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Radio Button) -->
        <div id="u17104" class="ax_default paragraph">
          <label for="u17104_input">
            <!-- Unnamed () -->
            <div id="u17105" class="text" style="visibility: visible;">
              <p><span>玉米熊阿里巴巴基地1299号店</span></p>
            </div>
          </label>
          <input id="u17104_input" type="radio" value="radio" name="u17104"/>
        </div>

        <!-- Unnamed (Radio Button) -->
        <div id="u17106" class="ax_default paragraph">
          <label for="u17106_input">
            <!-- Unnamed () -->
            <div id="u17107" class="text" style="visibility: visible;">
              <p><span>玉米熊华阳1号店</span></p>
            </div>
          </label>
          <input id="u17106_input" type="radio" value="radio" name="u17106"/>
        </div>

        <!-- Unnamed (Radio Button) -->
        <div id="u17108" class="ax_default paragraph">
          <label for="u17108_input">
            <!-- Unnamed () -->
            <div id="u17109" class="text" style="visibility: visible;">
              <p><span>玉米熊华阳12店</span></p>
            </div>
          </label>
          <input id="u17108_input" type="radio" value="radio" name="u17108"/>
        </div>

        <!-- Unnamed (Radio Button) -->
        <div id="u17110" class="ax_default paragraph">
          <label for="u17110_input">
            <!-- Unnamed () -->
            <div id="u17111" class="text" style="visibility: visible;">
              <p><span>玉米熊华阳1号店</span></p>
            </div>
          </label>
          <input id="u17110_input" type="radio" value="radio" name="u17110"/>
        </div>

        <!-- Unnamed (Radio Button) -->
        <div id="u17112" class="ax_default paragraph">
          <label for="u17112_input">
            <!-- Unnamed () -->
            <div id="u17113" class="text" style="visibility: visible;">
              <p><span>玉米熊华阳12店</span></p>
            </div>
          </label>
          <input id="u17112_input" type="radio" value="radio" name="u17112"/>
        </div>

        <!-- Unnamed (Radio Button) -->
        <div id="u17114" class="ax_default paragraph">
          <label for="u17114_input">
            <!-- Unnamed () -->
            <div id="u17115" class="text" style="visibility: visible;">
              <p><span>玉米熊华阳12店</span></p>
            </div>
          </label>
          <input id="u17114_input" type="radio" value="radio" name="u17114"/>
        </div>

        <!-- Unnamed (Vertical Line) -->
        <div id="u17116" class="ax_default line">
          <img id="u17116_img" class="img " src="images/桌位管理/u2674.png"/>
          <!-- Unnamed () -->
          <div id="u17117" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
