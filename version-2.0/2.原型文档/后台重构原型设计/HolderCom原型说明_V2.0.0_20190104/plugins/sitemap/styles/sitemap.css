
#sitemapHost {
    font-size: 12px;
    color:#4a4a4a;
    height: 100%;
}

#sitemapHostBtn a {
    background: url('images/sitemap_on.png');
    background: url('images/sitemap_on.svg'),linear-gradient(transparent, transparent);
}

.hashover #sitemapHostBtn a:hover {
    background: url('images/sitemap_hover.png');
    background: url('images/sitemap_hover.svg'),linear-gradient(transparent, transparent);
}

#sitemapHostBtn a.selected, #sitemapHostBtn a.selected:hover {
    background: url('images/sitemap_off.png');
    background: url('images/sitemap_off.svg'),linear-gradient(transparent, transparent);
}

#sitemapHost .pageButtonHeader {
    top: -27px;
}

#sitemapTreeContainer {
    overflow: auto;
    width: 100%;
    height: 100%;
    -webkit-overflow-scrolling: touch;
}

.sitemapTree {
    margin: 0px 0px 10px 0px;
    overflow:visible;
}

.sitemapTree ul {
    list-style-type: none;
    margin: 0px 0px 0px 0px;
    padding-left: 0px;
}

.sitemapPlusMinusLink 
{
}

.sitemapMinus 
{
	vertical-align:middle;
    background: url('images/minus.gif');
    background-repeat: no-repeat;
	margin-right: 3px;	
	margin-bottom: 1px;
    height:9px;
    width:9px;
    display:inline-block;
}

.sitemapPlus 
{
	vertical-align:middle;
    background: url('images/plus.gif');
    background-repeat: no-repeat;
	margin-right: 3px;	
	margin-bottom: 1px;
	height:9px;
    width:9px;
    display:inline-block;
}

.sitemapPageLink 
{
    margin-left: 0px;
}

.sitemapPageIcon 
{
	margin: 0px 0px -3px 4px;
    width: 16px;
    height: 16px;
    display: inline-block;
    background: url('images/page.png') no-repeat center center;
    background: url('images/page.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

.sitemapFlowIcon
{
    background: url('images/flow.png') no-repeat center center;
    background: url('images/flow.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

.sitemapFolderIcon
{
    background: url('images/folder_closed.png') no-repeat center center;
    background: url('images/folder_closed.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

.sitemapFolderOpenIcon
{
    background: url('images/folder_open.png') no-repeat center center;
    background: url('images/folder_open.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

.sitemapPageName 
{
    margin-left: 7px;
}

.sitemapNode 
{
    /*margin:4px 0px 4px 0px;*/
    white-space:nowrap;
}

.sitemapPageLinkContainer {
    /*display: inline-block;*/
    margin-left: 0px;
    padding: 4px 0px 4px 0px;
}
/*
.sitemapNode div
{
	padding-top: 1px; 
	padding-bottom: 3px;
    padding-left: 20px;
	height: 14px;
}
*/

.sitemapExpandableNode 
{
	margin-left: 0px;
}

.sitemapHighlight 
{
    /*display: inline-block;*/
	background-color : #C8E0F0;
    font-weight: bold;
}

.sitemapGreyedName
{
	color: #AAA;
}


.pluginNameHeader
{
    font-family: 'Trebuchet MS';
	font-size: 12px;
    letter-spacing: 1px;
	/*font-weight: bold;*/
	white-space: nowrap;
    margin-bottom: 5px;
}

.pageNameHeader
{
    font-family: 'Trebuchet MS';
    /*display: inline-block;*/
    /*float: left;*/
	font-size: 15px;
	font-weight: bold;
	/*height: 23px;*/
    padding-right: 77px;
	white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.pageButtonHeader
{
    float: right;
    position: relative;
    /*width: 72px;*/
	height: 24px;
    top: -22px;
    margin-bottom: -24px;
}

.sitemapHeader
{
    padding-top: 27px;
    border-bottom: 1px solid #d9d9d9;
    min-width: 110px;
}

.sitemapToolbar {
    margin: 0px 5px 14px 12px;
}

.sitemapToolbarButton
{
    float: left;
    width: 22px;
    height: 22px;
    border: 1px solid transparent;
}

#linksButton {
    background: url('images/share.png') no-repeat center center;
    background: url('images/share.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#linksButton:hover {
    background: url('images/share_hover.png') no-repeat center center;
    background: url('images/share_hover.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#linksButton.sitemapToolbarButtonSelected, .hashover #linksButton.sitemapToolbarButtonSelected:hover {
    background: url('images/share_on.png') no-repeat center center;
    background: url('images/share_on.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#adaptiveButton {
    background: url('images/views.png') no-repeat center center;
    background: url('images/views.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#adaptiveButton:hover {
    background: url('images/views_hover.png') no-repeat center center;
    background: url('images/views_hover.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#adaptiveButton.sitemapToolbarButtonSelected, #adaptiveButton.sitemapToolbarButtonSelected:hover {
    background: url('images/views_on.png') no-repeat center center;
    background: url('images/views_on.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#highlightInteractiveButton {
    background: url('images/hotspots.png') no-repeat center center;
    background: url('images/hotspots.svg') no-repeat center center, linear-gradient(transparent,transparent);
    margin-top: 1px;
}

#highlightInteractiveButton:hover {
    background: url('images/hotspots_hover.png') no-repeat center center;
    background: url('images/hotspots_hover.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#highlightInteractiveButton.sitemapToolbarButtonSelected, #highlightInteractiveButton.sitemapToolbarButtonSelected:hover {
    background: url('images/hotspots_on.png') no-repeat center center;
    background: url('images/hotspots_on.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

/*#variablesButton {
    background: url('images/229_variables_16.png') no-repeat center center;
}*/

#searchButton {
    background: url('images/232_search_16.png') no-repeat center center;
}

.sitemapLinkContainer
{
	margin-top: 8px;
	padding-right: 5px;
	/*font-size: 12px;*/
}

.sitemapLinkField 
{
	width: 100%;
	font-size: 12px;
	margin-top: 3px;
    padding: 5px;
}

.sitemapRadioSelected {
    font-weight: bold;
}

.sitemapOptionContainer
{
	margin-top: 8px;
	padding-right: 5px;
	/*font-size: 12px;*/
}

#sitemapOptionsDiv
{
	margin-top: 10px;
	/*margin-left: 16px;*/
}

#viewSelectDiv
{
    padding: 2px 0px 0px 0px;
	/*margin-left: 5px;*/
}

#viewSelect
{
	width: 70%;
}

.sitemapUrlOption
{
	padding-bottom: 8px;
}

.optionLabel
{
	font-size: 12px;
}

.sitemapPopupContainer
{
    display: none;
    position: absolute;
	background-color: #F4F4F4;
	border: 1px solid #B9B9B9;
	padding: 5px 5px 5px 5px;
	margin: 5px 0px 0px 5px;
    z-index: 1;
}

#sitemapLinksContainer {
    border-top: 1px solid #d9d9d9;
    padding: 10px;
    margin-left: 0px;
    /*line-height: 18px;*/
}

#adaptiveViewsContainer {
    border-top: 1px solid #d9d9d9;
    padding: 10px;
    margin-left: 0px;
    line-height: 18px;
}

.adaptiveViewOption
{
	padding: 2px;
}

.adaptiveViewOption:hover
{ 
	background-color: rgb(204,235,248);
	cursor: pointer;
}

.currentAdaptiveView {
    font-weight: bold;
}

.adaptiveCheckboxDiv {
	height: 15px;
	width: 15px;
	float: left;
}

.checkedAdaptive {
	background: url('images/adaptivecheck.png') no-repeat center center;
}

/*#variablesContainer 
{
	max-height: 350px;
	overflow: auto;
}*/

/*#variablesClearLink 
{
	color: #069;
	left: 5px;
}*/

#searchDiv {
    padding: 8px 12px 11px 12px;
}

#searchBox {
    width: 100%;
    border: none;
    border-top: 1px solid #d9d9d9;
    /*border-bottom: 1px solid #d9d9d9;*/
    padding: 5px;
    font-size: 12px;
}

.searchBoxHint 
{
	color: #8f949a;
	/*font-style: italic;*/
}

#sitemapLinksPageName
{
	font-weight: bold;
}

#sitemapOptionsHeader
{
	font-weight: bold;
}