<!DOCTYPE html>
<html>
  <head>
    <title>v2.0.0调整需求整理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/v2_0_0调整需求整理/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/v2_0_0调整需求整理/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Paragraph) -->
      <div id="u413" class="ax_default paragraph">
        <img id="u413_img" class="img " src="images/v2_0_0调整需求整理/u413.png"/>
        <!-- Unnamed () -->
        <div id="u414" class="text" style="visibility: visible;">
          <p><span>1.浏览器适配</span></p><p><span>2.登录页增加手机登录</span></p><p><span>3.页面结构调整：导航层级名称、页面布局</span></p><p><span>4.组织及人员管理</span></p><p><span>1）去品牌或者区域，统一为下级组织（不同品牌/同一区域问题）</span></p><p><span>2）添加门店名称及信息统一填写提交保存</span></p><p><span>3）增加企业品牌管理；当创建门店时，选择所属组织及品牌</span></p><p><span>4）员工增加所属组织选择、管理品牌、管理区域</span></p><p><span>5）创建角色时，数据权限，与企管员相同</span></p><p><span>5.菜品库→商品</span></p><p><span>1）去原品牌绑定机制；增加直接到门店的授卖操作，包括指定门店及上架状态</span></p><p><span>2）独立分类管理，增加使用门店授权</span></p><p><span>3）属性调整为独立管理区域，去掉价格编辑，增加使用门店授权</span></p><p><span>4）增加商品品牌作为商品创建的基础项（如商超门店系统的商品管理）--后期</span></p><p><span>6.会员管理</span></p><p><span>1)会员规则设置页优化</span></p><p><span>2）会员列表数据项调整</span></p><p><span>7.报表</span></p><p><span>1）查询字段及内容布局调整</span></p><p><span>2）日志--整合</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u415" class="ax_default image">
        <img id="u415_img" class="img " src="images/v2_0_0调整需求整理/u415.jpg"/>
        <!-- Unnamed () -->
        <div id="u416" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>
    </div>
  </body>
</html>
