<!DOCTYPE html>
<html>
  <head>
    <title>登录</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/登录/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/登录/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Rectangle) -->
      <div id="u434" class="ax_default box_3">
        <div id="u434_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u435" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u436" class="ax_default box_1">
        <div id="u436_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u437" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 账号登录 (Group) -->
      <div id="u438" class="ax_default" data-label="账号登录">

        <!-- Unnamed (Text Field) -->
        <div id="u439" class="ax_default text_field">
          <input id="u439_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u440" class="ax_default shape">
          <div id="u440_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u441" class="text" style="visibility: visible;">
            <p><span>登录</span></p>
          </div>
        </div>

        <!-- Unnamed (Text Field) -->
        <div id="u442" class="ax_default text_field">
          <input id="u442_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Text Field) -->
        <div id="u443" class="ax_default text_field">
          <input id="u443_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u444" class="ax_default paragraph">
          <img id="u444_img" class="img " src="images/登录/u444.png"/>
          <!-- Unnamed () -->
          <div id="u445" class="text" style="visibility: visible;">
            <p><span>icon</span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u446" class="ax_default paragraph">
          <img id="u446_img" class="img " src="images/登录/u444.png"/>
          <!-- Unnamed () -->
          <div id="u447" class="text" style="visibility: visible;">
            <p><span>icon</span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u448" class="ax_default paragraph">
          <img id="u448_img" class="img " src="images/登录/u444.png"/>
          <!-- Unnamed () -->
          <div id="u449" class="text" style="visibility: visible;">
            <p><span>icon</span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u450" class="ax_default paragraph">
          <img id="u450_img" class="img " src="images/登录/u450.png"/>
          <!-- Unnamed () -->
          <div id="u451" class="text" style="visibility: visible;">
            <p><span>&nbsp;忘记密码</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u452" class="ax_default paragraph">
        <img id="u452_img" class="img " src="images/登录/u452.png"/>
        <!-- Unnamed () -->
        <div id="u453" class="text" style="visibility: visible;">
          <p><span>门店管理后台</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u454" class="ax_default paragraph">
        <img id="u454_img" class="img " src="images/登录/u454.png"/>
        <!-- Unnamed () -->
        <div id="u455" class="text" style="visibility: visible;">
          <p><span>LOGO</span></p>
        </div>
      </div>

      <!-- 账号密码登录 (Paragraph) -->
      <div id="u456" class="ax_default paragraph" data-label="账号密码登录">
        <img id="u456_img" class="img " src="images/登录/账号密码登录_u456.png"/>
        <!-- Unnamed () -->
        <div id="u457" class="text" style="visibility: visible;">
          <p><span>账号登录</span></p>
        </div>
      </div>

      <!-- 手机短信登录 (Paragraph) -->
      <div id="u458" class="ax_default paragraph" data-label="手机短信登录">
        <img id="u458_img" class="img " src="images/登录/手机短信登录_u458.png"/>
        <!-- Unnamed () -->
        <div id="u459" class="text" style="visibility: visible;">
          <p><span>手机登录</span></p>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u460" class="ax_default horizontal_line">
        <img id="u460_img" class="img " src="images/登录/u460.png"/>
        <!-- Unnamed () -->
        <div id="u461" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 短信登录 (Group) -->
      <div id="u462" class="ax_default ax_default_hidden" data-label="短信登录" style="display: none; visibility: hidden">

        <!-- Unnamed (Text Field) -->
        <div id="u463" class="ax_default text_field">
          <input id="u463_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u464" class="ax_default shape">
          <div id="u464_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u465" class="text" style="visibility: visible;">
            <p><span>登录</span></p>
          </div>
        </div>

        <!-- Unnamed (Text Field) -->
        <div id="u466" class="ax_default text_field">
          <input id="u466_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u467" class="ax_default paragraph">
          <img id="u467_img" class="img " src="images/登录/u444.png"/>
          <!-- Unnamed () -->
          <div id="u468" class="text" style="visibility: visible;">
            <p><span>icon</span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u469" class="ax_default paragraph">
          <img id="u469_img" class="img " src="images/登录/u444.png"/>
          <!-- Unnamed () -->
          <div id="u470" class="text" style="visibility: visible;">
            <p><span>icon</span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u471" class="ax_default paragraph">
          <img id="u471_img" class="img " src="images/登录/u450.png"/>
          <!-- Unnamed () -->
          <div id="u472" class="text" style="visibility: visible;">
            <p><span>&nbsp;忘记密码</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u473" class="ax_default paragraph">
        <img id="u473_img" class="img " src="images/登录/u473.png"/>
        <!-- Unnamed () -->
        <div id="u474" class="text" style="visibility: visible;">
          <p><span>1.增加手机号密码登录</span></p><p><span>2.优化提示：当账号被禁用/绑定角色被禁用时，提示&quot;账号已禁用，启用请联系管理员&quot;</span></p><p><span>3.验证码：初始隐藏验证码输入行，支持直接输入账号密码等；当超过错误超过三次时，须输入验证码后才能提交登录</span></p><p><span>4.调整找回密码的流程</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
