$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z,A,[_(u,B,w,C,y,D),_(u,E,w,C,y,F),_(u,G,w,C,y,H),_(u,I,w,C,y,J,A,[_(u,K,w,C,y,L)]),_(u,M,w,C,y,N,A,[_(u,O,w,C,y,P)]),_(u,Q,w,C,y,R,A,[_(u,S,w,C,y,T),_(u,U,w,C,y,V),_(u,W,w,C,y,X)]),_(u,Y,w,C,y,Z,A,[_(u,ba,w,C,y,bb)]),_(u,bc,w,x,y,z,A,[_(u,bd,w,x,y,z,A,[_(u,be,w,C,y,bf,A,[_(u,bg,w,C,y,bh),_(u,bi,w,C,y,bj)]),_(u,bk,w,C,y,bl,A,[_(u,bm,w,C,y,bn)])]),_(u,bo,w,x,y,z,A,[_(u,bp,w,C,y,bq),_(u,br,w,C,y,bs,A,[_(u,bt,w,C,y,bu),_(u,bv,w,C,y,bw)]),_(u,bx,w,C,y,by),_(u,bz,w,C,y,bA)])])]),_(u,bB,w,x,y,z,A,[_(u,bC,w,x,y,z,A,[_(u,bD,w,C,y,bE),_(u,bF,w,C,y,bG,A,[_(u,bH,w,C,y,bI),_(u,bJ,w,C,y,bK),_(u,bL,w,C,y,bM),_(u,bN,w,C,y,bO),_(u,bP,w,C,y,bQ)]),_(u,bR,w,C,y,bS,A,[_(u,bT,w,C,y,bU),_(u,bV,w,C,y,bW),_(u,bX,w,C,y,bY)]),_(u,bZ,w,C,y,ca)]),_(u,cb,w,x,y,z,A,[_(u,cc,w,C,y,cd),_(u,ce,w,C,y,cf),_(u,cg,w,C,y,ch),_(u,ci,w,C,y,cj)])])]),ck,_(cl,z),cm,_(cn,co,cp,_(cq,cr,cs,cr),ct,cu),cv,[],cw,_(cx,_(cy,cz,cA,cB,cC,cD,cE,cF,cG,cH,cI,f,cJ,_(cK,cL,cM,cN,cO,cP),cQ,cR,cS,cD,cT,_(cU,cr,cV,cr),cp,_(cq,cr,cs,cr),cW,d,cX,f,cY,cz,cZ,_(cK,cL,cM,da),db,_(cK,cL,cM,dc),dd,de,df,cL,cO,de,dg,dh,di,dj,dk,dl,dm,dl,dn,dl,dp,dl,dq,_(),dr,dh,ds,dh,dt,_(du,f,dv,dw,dx,dw,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dE)),dF,_(du,f,dv,cr,dx,dw,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dE)),dG,_(du,f,dv,cP,dx,cP,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dH))),dI,_(dJ,_(cy,dK),dL,_(cy,dM,dd,dh,cZ,_(cK,cL,cM,dN)),dO,_(cy,dP,dd,dh,cZ,_(cK,cL,cM,dQ)),dR,_(cy,dS),dT,_(cy,dU,cA,cB,cC,cD,cJ,_(cK,cL,cM,cN,cO,cP),db,_(cK,cL,cM,dV),dd,de,cZ,_(cK,cL,cM,dW),cQ,cR,cE,cF,cG,cH,cI,f,df,cL,dg,dh,cO,de,dt,_(du,f,dv,dw,dx,dw,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dE)),dF,_(du,f,dv,cr,dx,dw,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dE)),dG,_(du,f,dv,cP,dx,cP,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dH)),di,dj,dk,dl,dm,dl,dn,dl,dp,dl,cS,cD),dX,_(cy,dY,dd,dh),dZ,_(cy,ea,dg,eb),ec,_(cy,ed,cG,ee,cA,ef,dd,dh,cZ,_(cK,cL,cM,eg),cQ,eh,di,ei,dk,dh,dm,dh,dn,dh,dp,dh),ej,_(cy,ek,cG,el,cA,ef,dd,dh,cZ,_(cK,cL,cM,eg),cQ,eh,di,ei,dk,dh,dm,dh,dn,dh,dp,dh),em,_(cy,en,cG,eo,cA,ef,dd,dh,cZ,_(cK,cL,cM,eg),cQ,eh,di,ei,dk,dh,dm,dh,dn,dh,dp,dh),ep,_(cy,eq,cG,er,cA,ef,dd,dh,cZ,_(cK,cL,cM,eg),cQ,eh,di,ei,dk,dh,dm,dh,dn,dh,dp,dh),es,_(cy,et,cA,ef,dd,dh,cZ,_(cK,cL,cM,eg),cQ,eh,di,ei,dk,dh,dm,dh,dn,dh,dp,dh),eu,_(cy,ev,cG,ew,cA,ef,dd,dh,cZ,_(cK,cL,cM,eg),cQ,eh,di,ei,dk,dh,dm,dh,dn,dh,dp,dh),ex,_(cy,ey,cG,er,dd,dh,cZ,_(cK,cL,cM,eg),cQ,eh,di,ei,dk,dh,dm,dh,dn,dh,dp,dh),ez,_(cy,eA,dd,dh,cZ,_(cK,cL,cM,eg),cQ,eh,di,ei,dk,dh,dm,dh,dn,dh,dp,dh),eB,_(cy,eC,cZ,_(cK,cL,cM,eg)),eD,_(cy,eE,dd,eb,cZ,_(cK,cL,cM,eg)),eF,_(cy,eG,cJ,_(cK,cL,cM,eH,cO,cP),cQ,eh,di,dj),eI,_(cy,eJ,cJ,_(cK,cL,cM,eH,cO,cP),cQ,eh,di,ei),eK,_(cy,eL,cJ,_(cK,cL,cM,eH,cO,cP),cQ,eh,di,ei),eM,_(cy,eN,cJ,_(cK,cL,cM,eH,cO,cP),cQ,eh,di,ei),eO,_(cy,eP,cQ,eh,di,ei),eQ,_(cy,eR,cQ,eh,di,ei),eS,_(cy,eT,cQ,cR),eU,_(cy,eV,dd,dh,cZ,_(cK,cL,cM,eg),cQ,eh,di,dj),eW,_(cy,eX),eY,_(cy,eZ,cZ,_(cK,cL,cM,eg)),fa,_(cy,fb,cA,cB,cC,cD,cJ,_(cK,cL,cM,fc,cO,cP),db,_(cK,cL,cM,dV),dd,de,cQ,cR,cE,fd,cG,fe,cI,f,df,cL,dg,dh,cZ,_(cK,cL,cM,da),cO,de,dt,_(du,f,dv,dw,dx,dw,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dE)),dF,_(du,f,dv,cr,dx,dw,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dE)),dG,_(du,f,dv,cP,dx,cP,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dH)),di,dj,dk,dl,dm,dl,dn,dl,dp,dl,cS,cD),ff,_(cy,fg,cJ,_(cK,cL,cM,da,cO,cP),db,_(cK,cL,cM,da),cZ,_(cK,cL,cM,fh),dt,_(du,d,dv,cP,dx,cP,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,fi))),fj,_(cy,fk,cZ,_(cK,fl,fm,[_(cM,da),_(cM,dN),_(cM,fn),_(cM,da)])),fo,_(cy,fp),fq,_(cy,fr,cA,cB,cC,cD,cE,cF,cG,cH,cI,f,cJ,_(cK,cL,cM,cN,cO,cP),cQ,cR,cS,cD,cZ,_(cK,cL,cM,da),db,_(cK,cL,cM,cN),dd,de,df,cL,cO,de,dg,dh,di,dj,dk,dl,dm,dl,dn,dl,dp,dl,dt,_(du,f,dv,dw,dx,dw,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dE)),dF,_(du,f,dv,cr,dx,dw,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dE)),dG,_(du,f,dv,cP,dx,cP,dy,dw,cM,_(dz,dA,dB,dA,dC,dA,dD,dH))),fs,_(cy,ft,db,_(cK,cL,cM,eH)),fu,_(cy,fv,dd,dh,cZ,_(cK,cL,cM,cN))),fw,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="商户后台",w="type",x="Folder",y="url",z="",A="children",B="整体设计说明",C="Wireframe",D="整体设计说明.html",E="数据字段限制",F="数据字段限制.html",G="v2.0.0调整需求整理",H="v2_0_0调整需求整理.html",I="组织机构管理鱼骨图",J="组织机构管理鱼骨图.html",K="权限数据流向图",L="权限数据流向图.html",M="菜品管理主路径图",N="菜品管理主路径图.html",O="菜品管理操作交互逻辑",P="菜品管理操作交互逻辑.html",Q="登录",R="登录.html",S="找回密码-输入账号获取验证码",T="找回密码-输入账号获取验证码.html",U="找回密码-输入新密码",V="找回密码-输入新密码.html",W="修改成功",X="修改成功.html",Y="首页-未创建菜品",Z="首页-未创建菜品.html",ba="首页-有商品无营业数据",bb="首页-有商品无营业数据.html",bc="门店及员工",bd="管理员工",be="员工列表",bf="员工列表.html",bg="新建账号",bh="新建账号.html",bi="编辑员工信息",bj="编辑员工信息.html",bk="角色列表",bl="角色列表.html",bm="角色授权",bn="角色授权.html",bo="管理门店",bp="组织机构",bq="组织机构.html",br="门店列表",bs="门店列表.html",bt="添加门店",bu="添加门店.html",bv="编辑门店",bw="编辑门店.html",bx="桌位管理",by="桌位管理.html",bz="企业品牌",bA="企业品牌.html",bB="管理商品",bC="商品库",bD="全部商品(商品库)",bE="全部商品_商品库_.html",bF="添加/编辑单品-初始",bG="添加_编辑单品-初始.html",bH="添加/编辑单品-更多设置",bI="添加_编辑单品-更多设置.html",bJ="添加/编辑多规格单品-初始",bK="添加_编辑多规格单品-初始.html",bL="添加/编辑多规格单品-更多设置",bM="添加_编辑多规格单品-更多设置.html",bN="添加/编辑称重单品-初始",bO="添加_编辑称重单品-初始.html",bP="添加/编辑称重单品-更多设置",bQ="添加_编辑称重单品-更多设置.html",bR="添加/编辑套餐-初始",bS="添加_编辑套餐-初始.html",bT="添加/编辑套餐-已选商品",bU="添加_编辑套餐-已选商品.html",bV="添加/编辑套餐-添加分组",bW="添加_编辑套餐-添加分组.html",bX="添加/编辑套餐-更多设置",bY="添加_编辑套餐-更多设置.html",bZ="属性库",ca="属性库.html",cb="门店商品",cc="全部商品(门店)",cd="全部商品_门店_.html",ce="添加/编辑单品",cf="添加_编辑单品.html",cg="添加/编辑套餐",ch="添加_编辑套餐.html",ci="全部属性",cj="全部属性.html",ck="globalVariables",cl="onloadvariable",cm="defaultAdaptiveView",cn="name",co="Base",cp="size",cq="width",cr=0,cs="height",ct="condition",cu="<=",cv="adaptiveViews",cw="stylesheet",cx="defaultStyle",cy="id",cz="627587b6038d43cca051c114ac41ad32",cA="fontWeight",cB="400",cC="fontStyle",cD="normal",cE="fontName",cF="'ArialMT', 'Arial'",cG="fontSize",cH="13px",cI="underline",cJ="foreGroundFill",cK="fillType",cL="solid",cM="color",cN=0xFF333333,cO="opacity",cP=1,cQ="horizontalAlignment",cR="center",cS="lineSpacing",cT="location",cU="x",cV="y",cW="visible",cX="limbo",cY="baseStyle",cZ="fill",da=0xFFFFFFFF,db="borderFill",dc=0xFF797979,dd="borderWidth",de="1",df="linePattern",dg="cornerRadius",dh="0",di="verticalAlignment",dj="middle",dk="paddingLeft",dl="2",dm="paddingTop",dn="paddingRight",dp="paddingBottom",dq="stateStyles",dr="rotation",ds="textRotation",dt="outerShadow",du="on",dv="offsetX",dw=5,dx="offsetY",dy="blurRadius",dz="r",dA=0,dB="g",dC="b",dD="a",dE=0.349019607843137,dF="innerShadow",dG="textShadow",dH=0.647058823529412,dI="customStyles",dJ="box_1",dK="********************************",dL="box_2",dM="********************************",dN=0xFFF2F2F2,dO="box_3",dP="********************************",dQ=0xFFD7D7D7,dR="ellipse",dS="eff044fe6497434a8c5f89f769ddde3b",dT="_形状",dU="40519e9ec4264601bfb12c514e4f4867",dV=0xFFCCCCCC,dW=0x19333333,dX="image",dY="75a91ee5b9d042cfa01b8d565fe289c0",dZ="button",ea="c9f35713a1cf4e91a0f2dbac65e6fb5c",eb="5",ec="heading_1",ed="1111111151944dfba49f67fd55eb1f88",ee="32px",ef="bold",eg=0xFFFFFF,eh="left",ei="top",ej="heading_2",ek="b3a15c9ddde04520be40f94c8168891e",el="24px",em="heading_3",en="8c7a4c5ad69a4369a5f7788171ac0b32",eo="18px",ep="heading_4",eq="e995c891077945c89c0b5fe110d15a0b",er="14px",es="heading_5",et="386b19ef4be143bd9b6c392ded969f89",eu="heading_6",ev="fc3b9a13b5574fa098ef0a1db9aac861",ew="10px",ex="label",ey="2285372321d148ec80932747449c36c9",ez="paragraph",eA="4988d43d80b44008a4a415096f1632af",eB="line",eC="619b2148ccc1497285562264d51992f9",eD="arrow",eE="d148f2c5268542409e72dde43e40043e",eF="text_field",eG="44157808f2934100b68f2394a66b2bba",eH=0xFF000000,eI="text_area",eJ="42ee17691d13435b8256d8d0a814778f",eK="droplist",eL="85f724022aae41c594175ddac9c289eb",eM="list_box",eN="********************************",eO="checkbox",eP="********************************",eQ="radio_button",eR="4eb5516f311c4bdfa0cb11d7ea75084e",eS="html_button",eT="eed12d9ebe2e4b9689b3b57949563dca",eU="tree_node",eV="93a4c3353b6f4562af635b7116d6bf94",eW="table_cell",eX="33ea2511485c479dbf973af3302f2352",eY="menu_item",eZ="2036b2baccbc41f0b9263a6981a11a42",fa="connector",fb="699a012e142a4bcba964d96e88b88bdf",fc=0xFF0000FF,fd="'PingFangSC-Regular', 'PingFang SC'",fe="12px",ff="marker",fg="a8e305fe5c2a462b995b0021a9ba82b9",fh=0xFF009DD9,fi=0.698039215686274,fj="flow_shape",fk="df01900e3c4e43f284bafec04b0864c4",fl="linearGradient",fm="colors",fn=0xFFE4E4E4,fo="table",fp="d612b8c2247342eda6a8bc0663265baa",fq="shape",fr="98c916898e844865a527f56bc61a500d",fs="horizontal_line",ft="f48196c19ab74fb7b3acb5151ce8ea2d",fu="icon",fv="26c731cb771b44a88eb8b6e97e78c80e",fw="duplicateStyles";
return _creator();
})());