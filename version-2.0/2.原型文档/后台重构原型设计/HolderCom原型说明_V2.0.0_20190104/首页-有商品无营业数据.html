<!DOCTYPE html>
<html>
  <head>
    <title>首页-有商品无营业数据</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/首页-有商品无营业数据/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/首页-有商品无营业数据/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Paragraph) -->
      <div id="u567" class="ax_default paragraph">
        <img id="u567_img" class="img " src="images/首页-有商品无营业数据/u567.png"/>
        <!-- Unnamed () -->
        <div id="u568" class="text" style="visibility: visible;">
          <p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">欢迎使用掌控者智慧门店服务。</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">营业数据会在您正式使用掌控者点餐收银系统后自动统计。</span></p><p><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">快去</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC';font-weight:400;">试试一体机等点餐收银设备</span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">吧。</span></p>
        </div>
      </div>

      <!-- Unnamed (主框架) -->

      <!-- Unnamed (Rectangle) -->
      <div id="u570" class="ax_default box_3">
        <div id="u570_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u571" class="text" style="visibility: visible;">
          <p><span style="font-family:'ArialMT', 'Arial';">&nbsp;&nbsp; </span><span style="font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';">Holder </span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u572" class="ax_default box_3">
        <div id="u572_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u573" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u574" class="ax_default paragraph">
        <div id="u574_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u575" class="text" style="visibility: visible;">
          <p><span>账号/员姓</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u576" class="ax_default table_cell">
        <div id="u576_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u577" class="text" style="visibility: visible;">
          <p><span>消息 </span><span style="color:#FF0000;">99+</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u578" class="ax_default paragraph">
        <img id="u578_img" class="img " src="images/首页-未创建菜品/u546.png"/>
        <!-- Unnamed () -->
        <div id="u579" class="text" style="visibility: visible;">
          <p><span>门店管理后台</span></p>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u580" class="ax_default horizontal_line">
        <img id="u580_img" class="img " src="images/首页-未创建菜品/u548.png"/>
        <!-- Unnamed () -->
        <div id="u581" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u582" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u583" class="ax_default table_cell">
          <img id="u583_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u584" class="text" style="visibility: visible;">
            <p><span>首页</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u585" class="ax_default table_cell">
          <img id="u585_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u586" class="text" style="visibility: visible;">
            <p><span>门店及员工</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u587" class="ax_default table_cell">
          <img id="u587_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u588" class="text" style="visibility: visible;">
            <p><span>会员顾客</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u589" class="ax_default table_cell">
          <img id="u589_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u590" class="text" style="visibility: visible;">
            <p><span>商品销售</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u591" class="ax_default table_cell">
          <img id="u591_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u592" class="text" style="visibility: visible;">
            <p><span>活动营销</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u593" class="ax_default table_cell">
          <img id="u593_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u594" class="text" style="visibility: visible;">
            <p><span>报表中心</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u595" class="ax_default table_cell">
          <img id="u595_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u596" class="text" style="visibility: visible;">
            <p><span>更多配置</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u597" class="ax_default box_2">
        <div id="u597_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u598" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>
    </div>
  </body>
</html>
