body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1817px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u1878_img {
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u1878 {
  position:absolute;
  left:217px;
  top:148px;
  width:190px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:right;
}
#u1879 {
  position:absolute;
  left:2px;
  top:7px;
  width:186px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1880 {
  position:absolute;
  left:463px;
  top:178px;
  width:85px;
  height:319px;
}
#u1881_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:40px;
}
#u1881 {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1882 {
  position:absolute;
  left:2px;
  top:12px;
  width:76px;
  word-wrap:break-word;
}
#u1883_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:40px;
}
#u1883 {
  position:absolute;
  left:0px;
  top:40px;
  width:80px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1884 {
  position:absolute;
  left:2px;
  top:12px;
  width:76px;
  word-wrap:break-word;
}
#u1885_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:40px;
}
#u1885 {
  position:absolute;
  left:0px;
  top:80px;
  width:80px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1886 {
  position:absolute;
  left:2px;
  top:12px;
  width:76px;
  word-wrap:break-word;
}
#u1887_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:40px;
}
#u1887 {
  position:absolute;
  left:0px;
  top:120px;
  width:80px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1888 {
  position:absolute;
  left:2px;
  top:12px;
  width:76px;
  word-wrap:break-word;
}
#u1889_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:40px;
}
#u1889 {
  position:absolute;
  left:0px;
  top:160px;
  width:80px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1890 {
  position:absolute;
  left:2px;
  top:12px;
  width:76px;
  word-wrap:break-word;
}
#u1891_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:40px;
}
#u1891 {
  position:absolute;
  left:0px;
  top:200px;
  width:80px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1892 {
  position:absolute;
  left:2px;
  top:12px;
  width:76px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1893_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:37px;
}
#u1893 {
  position:absolute;
  left:0px;
  top:240px;
  width:80px;
  height:37px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1894 {
  position:absolute;
  left:2px;
  top:10px;
  width:76px;
  word-wrap:break-word;
}
#u1895_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:37px;
}
#u1895 {
  position:absolute;
  left:0px;
  top:277px;
  width:80px;
  height:37px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u1896 {
  position:absolute;
  left:2px;
  top:10px;
  width:76px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1897_img {
  position:absolute;
  left:0px;
  top:0px;
  width:702px;
  height:2px;
}
#u1897 {
  position:absolute;
  left:446px;
  top:169px;
  width:701px;
  height:1px;
}
#u1898 {
  position:absolute;
  left:2px;
  top:-8px;
  width:697px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1899_img {
  position:absolute;
  left:0px;
  top:0px;
  width:456px;
  height:2px;
}
#u1899 {
  position:absolute;
  left:464px;
  top:169px;
  width:455px;
  height:1px;
}
#u1900 {
  position:absolute;
  left:2px;
  top:-8px;
  width:451px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1901 {
  position:absolute;
  left:217px;
  top:189px;
  width:178px;
  height:234px;
}
#u1902_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:30px;
}
#u1902 {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1903 {
  position:absolute;
  left:2px;
  top:6px;
  width:169px;
  word-wrap:break-word;
}
#u1904_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:31px;
}
#u1904 {
  position:absolute;
  left:0px;
  top:30px;
  width:173px;
  height:31px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1905 {
  position:absolute;
  left:2px;
  top:7px;
  width:169px;
  word-wrap:break-word;
}
#u1906_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:46px;
}
#u1906 {
  position:absolute;
  left:0px;
  top:61px;
  width:173px;
  height:46px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1907 {
  position:absolute;
  left:2px;
  top:14px;
  width:169px;
  word-wrap:break-word;
}
#u1908_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:32px;
}
#u1908 {
  position:absolute;
  left:0px;
  top:107px;
  width:173px;
  height:32px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1909 {
  position:absolute;
  left:2px;
  top:8px;
  width:169px;
  word-wrap:break-word;
}
#u1910_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:30px;
}
#u1910 {
  position:absolute;
  left:0px;
  top:139px;
  width:173px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1911 {
  position:absolute;
  left:2px;
  top:6px;
  width:169px;
  word-wrap:break-word;
}
#u1912_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:30px;
}
#u1912 {
  position:absolute;
  left:0px;
  top:169px;
  width:173px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1913 {
  position:absolute;
  left:2px;
  top:6px;
  width:169px;
  word-wrap:break-word;
}
#u1914_img {
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:30px;
}
#u1914 {
  position:absolute;
  left:0px;
  top:199px;
  width:173px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1915 {
  position:absolute;
  left:2px;
  top:6px;
  width:169px;
  word-wrap:break-word;
}
#u1916 {
  position:absolute;
  left:225px;
  top:219px;
  width:188px;
  height:37px;
}
#u1917_img {
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:32px;
}
#u1917 {
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:32px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  color:#0000FF;
  text-align:right;
}
#u1918 {
  position:absolute;
  left:2px;
  top:2px;
  width:179px;
  word-wrap:break-word;
}
#u1919_img {
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:20px;
}
#u1919 {
  position:absolute;
  left:446px;
  top:137px;
  width:92px;
  height:20px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:14px;
}
#u1920 {
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  white-space:nowrap;
}
#u1921_img {
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:30px;
}
#u1921 {
  position:absolute;
  left:464px;
  top:529px;
  width:57px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
}
#u1922 {
  position:absolute;
  left:2px;
  top:6px;
  width:53px;
  word-wrap:break-word;
}
#u1923_img {
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:30px;
}
#u1923 {
  position:absolute;
  left:531px;
  top:529px;
  width:57px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
}
#u1924 {
  position:absolute;
  left:2px;
  top:6px;
  width:53px;
  word-wrap:break-word;
}
#u1925 {
  position:absolute;
  left:544px;
  top:225px;
  width:321px;
  height:30px;
}
#u1925_input {
  position:absolute;
  left:0px;
  top:0px;
  width:321px;
  height:30px;
  background-color:rgba(255, 255, 255, 0.0980392156862745);
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:13px;
  text-decoration:none;
  color:#333333;
}
#u1925_input:disabled {
  color:grayText;
}
#u1927_div {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:720px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1927 {
  position:absolute;
  left:0px;
  top:71px;
  width:200px;
  height:720px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1928 {
  position:absolute;
  left:2px;
  top:352px;
  width:196px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1929 {
  position:absolute;
  left:11px;
  top:82px;
  width:135px;
  height:565px;
}
#u1930_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1930 {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1931 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1932_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1932 {
  position:absolute;
  left:0px;
  top:40px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1933 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1934_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1934 {
  position:absolute;
  left:0px;
  top:80px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1935 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1936_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1936 {
  position:absolute;
  left:0px;
  top:120px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1937 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1938_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1938 {
  position:absolute;
  left:0px;
  top:160px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1939 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1940_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1940 {
  position:absolute;
  left:0px;
  top:200px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1941 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1942_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1942 {
  position:absolute;
  left:0px;
  top:240px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1943 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1944_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1944 {
  position:absolute;
  left:0px;
  top:280px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1945 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1946_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1946 {
  position:absolute;
  left:0px;
  top:320px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1947 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1948_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1948 {
  position:absolute;
  left:0px;
  top:360px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1949 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1950_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1950 {
  position:absolute;
  left:0px;
  top:400px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1951 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1952_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1952 {
  position:absolute;
  left:0px;
  top:440px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1953 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1954_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1954 {
  position:absolute;
  left:0px;
  top:480px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1955 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1956_img {
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:40px;
}
#u1956 {
  position:absolute;
  left:0px;
  top:520px;
  width:130px;
  height:40px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u1957 {
  position:absolute;
  left:2px;
  top:12px;
  width:126px;
  word-wrap:break-word;
}
#u1958_img {
  position:absolute;
  left:0px;
  top:0px;
  width:721px;
  height:2px;
}
#u1958 {
  position:absolute;
  left:-160px;
  top:430px;
  width:720px;
  height:1px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1959 {
  position:absolute;
  left:2px;
  top:-8px;
  width:716px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1961_div {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:72px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1961 {
  position:absolute;
  left:0px;
  top:-1px;
  width:1200px;
  height:72px;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1962 {
  position:absolute;
  left:2px;
  top:28px;
  width:1196px;
  word-wrap:break-word;
}
#u1963_div {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:71px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1963 {
  position:absolute;
  left:0px;
  top:-1px;
  width:1200px;
  height:71px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1964 {
  position:absolute;
  left:2px;
  top:28px;
  width:1196px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1965_div {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
}
#u1965 {
  position:absolute;
  left:1066px;
  top:18px;
  width:55px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
}
#u1966 {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  white-space:nowrap;
}
#u1967_div {
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:21px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1967 {
  position:absolute;
  left:1133px;
  top:16px;
  width:54px;
  height:21px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1968 {
  position:absolute;
  left:2px;
  top:2px;
  width:50px;
  white-space:nowrap;
}
#u1969_img {
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  height:22px;
}
#u1969 {
  position:absolute;
  left:48px;
  top:17px;
  width:126px;
  height:22px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1970 {
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  word-wrap:break-word;
}
#u1971_img {
  position:absolute;
  left:0px;
  top:0px;
  width:1201px;
  height:2px;
}
#u1971 {
  position:absolute;
  left:0px;
  top:70px;
  width:1200px;
  height:1px;
}
#u1972 {
  position:absolute;
  left:2px;
  top:-8px;
  width:1196px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1973 {
  position:absolute;
  left:194px;
  top:10px;
  width:504px;
  height:44px;
}
#u1974_img {
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:39px;
}
#u1974 {
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1975 {
  position:absolute;
  left:2px;
  top:11px;
  width:46px;
  word-wrap:break-word;
}
#u1976_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u1976 {
  position:absolute;
  left:50px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1977 {
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u1978_img {
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:39px;
}
#u1978 {
  position:absolute;
  left:130px;
  top:0px;
  width:60px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1979 {
  position:absolute;
  left:2px;
  top:11px;
  width:56px;
  word-wrap:break-word;
}
#u1980_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u1980 {
  position:absolute;
  left:190px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1981 {
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u1982_img {
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:39px;
}
#u1982 {
  position:absolute;
  left:270px;
  top:0px;
  width:74px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1983 {
  position:absolute;
  left:2px;
  top:11px;
  width:70px;
  word-wrap:break-word;
}
#u1984_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u1984 {
  position:absolute;
  left:344px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1985 {
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u1986_img {
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:39px;
}
#u1986 {
  position:absolute;
  left:424px;
  top:0px;
  width:75px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1987 {
  position:absolute;
  left:2px;
  top:11px;
  width:71px;
  word-wrap:break-word;
}
#u1988_div {
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1988 {
  position:absolute;
  left:11px;
  top:11px;
  width:34px;
  height:34px;
}
#u1989 {
  position:absolute;
  left:2px;
  top:9px;
  width:30px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1991_div {
  position:absolute;
  left:0px;
  top:0px;
  width:1000px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  -webkit-box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  box-shadow:0px 8px 2px rgba(215, 215, 215, 0.349019607843137);
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1991 {
  position:absolute;
  left:200px;
  top:71px;
  width:1000px;
  height:49px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u1992 {
  position:absolute;
  left:2px;
  top:16px;
  width:996px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1993 {
  position:absolute;
  left:11px;
  top:603px;
  width:113px;
  height:44px;
}
#u1994_img {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:39px;
}
#u1994 {
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:left;
}
#u1995 {
  position:absolute;
  left:2px;
  top:11px;
  width:104px;
  word-wrap:break-word;
}
#u1996 {
  position:absolute;
  left:245px;
  top:10px;
  width:80px;
  height:45px;
}
#u1997_img {
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:40px;
}
#u1997 {
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u1998 {
  position:absolute;
  left:2px;
  top:12px;
  width:71px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1999 {
  position:absolute;
  left:545px;
  top:265px;
  width:320px;
  height:30px;
}
#u1999_input {
  position:absolute;
  left:0px;
  top:0px;
  width:320px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u2000 {
  position:absolute;
  left:545px;
  top:305px;
  width:320px;
  height:30px;
}
#u2000_input {
  position:absolute;
  left:0px;
  top:0px;
  width:320px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u2002 {
  position:absolute;
  left:545px;
  top:342px;
  width:100px;
  height:30px;
}
#u2002_input {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
}
#u2002_input:disabled {
  color:grayText;
}
#u2003 {
  position:absolute;
  left:655px;
  top:342px;
  width:100px;
  height:30px;
}
#u2003_input {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
}
#u2003_input:disabled {
  color:grayText;
}
#u2004 {
  position:absolute;
  left:765px;
  top:342px;
  width:100px;
  height:29px;
}
#u2004_input {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:29px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
}
#u2004_input:disabled {
  color:grayText;
}
#u2005 {
  position:absolute;
  left:545px;
  top:379px;
  width:320px;
  height:30px;
}
#u2005_input {
  position:absolute;
  left:0px;
  top:0px;
  width:320px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u2006 {
  position:absolute;
  left:545px;
  top:419px;
  width:320px;
  height:67px;
}
#u2006_input {
  position:absolute;
  left:0px;
  top:0px;
  width:320px;
  height:67px;
  background-color:transparent;
  font-family:'PingFangSC-Ultralight', 'PingFang SC Ultralight', 'PingFang SC';
  font-weight:100;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u2007 {
  position:absolute;
  left:544px;
  top:185px;
  width:321px;
  height:30px;
}
#u2007_input {
  position:absolute;
  left:0px;
  top:0px;
  width:321px;
  height:30px;
  background-color:transparent;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#333333;
  text-align:left;
}
#u2008_img {
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
}
#u2008 {
  position:absolute;
  left:225px;
  top:88px;
  width:65px;
  height:22px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u2009 {
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  white-space:nowrap;
}
#u2010_img {
  position:absolute;
  left:0px;
  top:0px;
  width:655px;
  height:2px;
}
#u2010 {
  position:absolute;
  left:91px;
  top:464px;
  width:654px;
  height:1px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u2011 {
  position:absolute;
  left:2px;
  top:-8px;
  width:650px;
  visibility:hidden;
  word-wrap:break-word;
}
#u2012_img {
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
}
#u2012 {
  position:absolute;
  left:225px;
  top:155px;
  width:49px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u2013 {
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  white-space:nowrap;
}
#u2014_img {
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:28px;
}
#u2014 {
  position:absolute;
  left:383px;
  top:148px;
  width:24px;
  height:28px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:20px;
  color:#0000FF;
  text-align:center;
}
#u2015 {
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  word-wrap:break-word;
}
#u2016_img {
  position:absolute;
  left:0px;
  top:0px;
  width:582px;
  height:545px;
}
#u2016 {
  position:absolute;
  left:1235px;
  top:21px;
  width:582px;
  height:545px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-style:normal;
  font-size:12px;
}
#u2017 {
  position:absolute;
  left:0px;
  top:0px;
  width:582px;
  word-wrap:break-word;
}
#u2018 {
  position:absolute;
  left:1235px;
  top:617px;
  width:333px;
  height:125px;
}
#u2019_img {
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u2019 {
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u2020 {
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u2021_img {
  position:absolute;
  left:0px;
  top:0px;
  width:255px;
  height:30px;
}
#u2021 {
  position:absolute;
  left:73px;
  top:0px;
  width:255px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u2022 {
  position:absolute;
  left:2px;
  top:6px;
  width:251px;
  word-wrap:break-word;
}
#u2023_img {
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u2023 {
  position:absolute;
  left:0px;
  top:30px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u2024 {
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u2025_img {
  position:absolute;
  left:0px;
  top:0px;
  width:255px;
  height:30px;
}
#u2025 {
  position:absolute;
  left:73px;
  top:30px;
  width:255px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u2026 {
  position:absolute;
  left:2px;
  top:6px;
  width:251px;
  word-wrap:break-word;
}
#u2027_img {
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u2027 {
  position:absolute;
  left:0px;
  top:60px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u2028 {
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u2029_img {
  position:absolute;
  left:0px;
  top:0px;
  width:255px;
  height:30px;
}
#u2029 {
  position:absolute;
  left:73px;
  top:60px;
  width:255px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u2030 {
  position:absolute;
  left:2px;
  top:6px;
  width:251px;
  word-wrap:break-word;
}
#u2031_img {
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:30px;
}
#u2031 {
  position:absolute;
  left:0px;
  top:90px;
  width:73px;
  height:30px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u2032 {
  position:absolute;
  left:2px;
  top:6px;
  width:69px;
  word-wrap:break-word;
}
#u2033_img {
  position:absolute;
  left:0px;
  top:0px;
  width:255px;
  height:30px;
}
#u2033 {
  position:absolute;
  left:73px;
  top:90px;
  width:255px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
  text-align:left;
}
#u2034 {
  position:absolute;
  left:2px;
  top:6px;
  width:251px;
  word-wrap:break-word;
}
#u2035_img {
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:17px;
}
#u2035 {
  position:absolute;
  left:865px;
  top:192px;
  width:109px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u2036 {
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  white-space:nowrap;
}
#u2037_img {
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:17px;
}
#u2037 {
  position:absolute;
  left:1235px;
  top:600px;
  width:61px;
  height:17px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#1B5C57;
}
#u2038 {
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  white-space:nowrap;
}
#u2039_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:215px;
}
#u2039 {
  position:absolute;
  left:225px;
  top:189px;
  width:1px;
  height:214px;
}
#u2040 {
  position:absolute;
  left:2px;
  top:99px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u2041_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:27px;
}
#u2041 {
  position:absolute;
  left:242px;
  top:210px;
  width:1px;
  height:26px;
}
#u2042 {
  position:absolute;
  left:2px;
  top:5px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u2043_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:35px;
}
#u2043 {
  position:absolute;
  left:262px;
  top:239px;
  width:1px;
  height:34px;
}
#u2044 {
  position:absolute;
  left:2px;
  top:9px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u2045_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:36px;
}
#u2045 {
  position:absolute;
  left:282px;
  top:279px;
  width:1px;
  height:35px;
}
#u2046 {
  position:absolute;
  left:2px;
  top:10px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u2047_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:14px;
}
#u2047 {
  position:absolute;
  left:231px;
  top:397px;
  width:1px;
  height:13px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u2048 {
  position:absolute;
  left:2px;
  top:-2px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u2049_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:12px;
}
#u2049 {
  position:absolute;
  left:287px;
  top:308px;
  width:1px;
  height:11px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u2050 {
  position:absolute;
  left:2px;
  top:-2px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u2051_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:8px;
}
#u2051 {
  position:absolute;
  left:267px;
  top:270px;
  width:1px;
  height:7px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u2052 {
  position:absolute;
  left:2px;
  top:-4px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
#u2053_img {
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:11px;
}
#u2053 {
  position:absolute;
  left:247px;
  top:231px;
  width:1px;
  height:10px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u2054 {
  position:absolute;
  left:2px;
  top:-3px;
  width:1px;
  visibility:hidden;
  word-wrap:break-word;
}
