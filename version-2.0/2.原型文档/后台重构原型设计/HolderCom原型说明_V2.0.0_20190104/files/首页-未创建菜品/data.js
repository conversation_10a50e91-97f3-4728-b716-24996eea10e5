$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(t,be,bf,_(bg,bh,bi,bj),bk,_(bl,bm,bn,bo),M,bp),P,_(),bq,_(),S,[_(T,br,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(t,be,bf,_(bg,bh,bi,bj),bk,_(bl,bm,bn,bo),M,bp),P,_(),bq,_())],bu,_(bv,bw),bx,g),_(T,by,V,W,X,bz,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,bA,bi,bB),t,bC,bD,_(y,z,A,bE),bk,_(bl,bF,bn,bG),x,_(y,z,A,bH),O,J),P,_(),bq,_(),S,[_(T,bI,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(bf,_(bg,bA,bi,bB),t,bC,bD,_(y,z,A,bE),bk,_(bl,bF,bn,bG),x,_(y,z,A,bH),O,J),P,_(),bq,_())],Q,_(bJ,_(bK,bL,bM,[_(bK,bN,bO,g,bP,[_(bQ,bR,bK,bS,bT,_(bU,k,b,bV,bW,bd),bX,bY)])])),bZ,bd,bx,g),_(T,ca,V,W,X,bz,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,bA,bi,bB),t,bC,bD,_(y,z,A,bE),bk,_(bl,bG,bn,cb),x,_(y,z,A,bH),O,J),P,_(),bq,_(),S,[_(T,cc,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(bf,_(bg,bA,bi,bB),t,bC,bD,_(y,z,A,bE),bk,_(bl,bG,bn,cb),x,_(y,z,A,bH),O,J),P,_(),bq,_())],Q,_(bJ,_(bK,bL,bM,[_(bK,bN,bO,g,bP,[_(bQ,bR,bK,cd,bT,_(bU,k,bW,bd),bX,bY)])])),bZ,bd,bx,g),_(T,ce,V,W,X,cf,n,cg,ba,cg,bc,bd,s,_(bk,_(bl,ch,bn,ci),bf,_(bg,cj,bi,ck)),P,_(),bq,_(),cl,cm)])),cn,_(co,_(l,co,n,cp,p,cf,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,cq,V,W,X,bz,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,cj,bi,ck),t,cr,cs,ct,cu,_(y,z,A,cv,cw,cx),cy,cz,bD,_(y,z,A,B),x,_(y,z,A,cA)),P,_(),bq,_(),S,[_(T,cB,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(bf,_(bg,cj,bi,ck),t,cr,cs,ct,cu,_(y,z,A,cv,cw,cx),cy,cz,bD,_(y,z,A,B),x,_(y,z,A,cA)),P,_(),bq,_())],bx,g),_(T,cC,V,W,X,bz,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,cj,bi,bm),t,cr,cs,ct,M,cD,cu,_(y,z,A,cv,cw,cx),cy,cz,bD,_(y,z,A,cE),x,_(y,z,A,bE)),P,_(),bq,_(),S,[_(T,cF,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(bf,_(bg,cj,bi,bm),t,cr,cs,ct,M,cD,cu,_(y,z,A,cv,cw,cx),cy,cz,bD,_(y,z,A,cE),x,_(y,z,A,bE)),P,_(),bq,_())],bx,g),_(T,cG,V,W,X,bz,n,Z,ba,Z,bc,bd,s,_(cH,cI,bf,_(bg,cJ,bi,cK),t,be,bk,_(bl,cL,bn,cM),cy,cN,cu,_(y,z,A,cO,cw,cx),M,cP),P,_(),bq,_(),S,[_(T,cQ,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(cH,cI,bf,_(bg,cJ,bi,cK),t,be,bk,_(bl,cL,bn,cM),cy,cN,cu,_(y,z,A,cO,cw,cx),M,cP),P,_(),bq,_())],Q,_(bJ,_(bK,bL,bM,[_(bK,bN,bO,g,bP,[])])),bZ,bd,bx,g),_(T,cR,V,W,X,bz,n,Z,ba,Z,bc,bd,s,_(cH,cI,bf,_(bg,cS,bi,cT),t,cU,bk,_(bl,cV,bn,cK),cy,cN,M,cP,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J),P,_(),bq,_(),S,[_(T,cX,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(cH,cI,bf,_(bg,cS,bi,cT),t,cU,bk,_(bl,cV,bn,cK),cy,cN,M,cP,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J),P,_(),bq,_())],Q,_(bJ,_(bK,bL,bM,[_(bK,bN,bO,g,bP,[_(bQ,bR,bK,cd,bT,_(bU,k,bW,bd),bX,bY)])])),bZ,bd,bx,g),_(T,cY,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(cH,cZ,t,be,bf,_(bg,da,bi,bB),bk,_(bl,bA,bn,db),M,dc,cy,dd,cu,_(y,z,A,de,cw,cx)),P,_(),bq,_(),S,[_(T,df,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(cH,cZ,t,be,bf,_(bg,da,bi,bB),bk,_(bl,bA,bn,db),M,dc,cy,dd,cu,_(y,z,A,de,cw,cx)),P,_(),bq,_())],bu,_(bv,dg),bx,g),_(T,dh,V,W,X,di,n,Z,ba,dj,bc,bd,s,_(bk,_(bl,ch,bn,bm),bf,_(bg,cj,bi,cx),bD,_(y,z,A,cv),t,dk),P,_(),bq,_(),S,[_(T,dl,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(bk,_(bl,ch,bn,bm),bf,_(bg,cj,bi,cx),bD,_(y,z,A,cv),t,dk),P,_(),bq,_())],bu,_(bv,dm),bx,g),_(T,dn,V,W,X,dp,n,dq,ba,dq,bc,bd,s,_(bf,_(bg,dr,bi,ds),bk,_(bl,dt,bn,du)),P,_(),bq,_(),S,[_(T,dv,V,W,X,dw,n,dx,ba,dx,bc,bd,s,_(cH,cI,bf,_(bg,dy,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dz,bn,ch)),P,_(),bq,_(),S,[_(T,dA,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(cH,cI,bf,_(bg,dy,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dz,bn,ch)),P,_(),bq,_())],Q,_(bJ,_(bK,bL,bM,[_(bK,bN,bO,g,bP,[_(bQ,bR,bK,dB,bT,_(bU,k,b,dC,bW,bd),bX,bY)])])),bZ,bd,bu,_(bv,dD)),_(T,dE,V,W,X,dw,n,dx,ba,dx,bc,bd,s,_(cH,cI,bf,_(bg,dF,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dG,bn,ch)),P,_(),bq,_(),S,[_(T,dH,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(cH,cI,bf,_(bg,dF,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dG,bn,ch)),P,_(),bq,_())],Q,_(bJ,_(bK,bL,bM,[_(bK,bN,bO,g,bP,[_(bQ,bR,bK,cd,bT,_(bU,k,bW,bd),bX,bY)])])),bZ,bd,bu,_(bv,dD)),_(T,dI,V,W,X,dw,n,dx,ba,dx,bc,bd,s,_(cH,cI,bf,_(bg,dy,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dJ,bn,ch)),P,_(),bq,_(),S,[_(T,dK,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(cH,cI,bf,_(bg,dy,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dJ,bn,ch)),P,_(),bq,_())],Q,_(bJ,_(bK,bL,bM,[_(bK,bN,bO,g,bP,[_(bQ,bR,bK,cd,bT,_(bU,k,bW,bd),bX,bY)])])),bZ,bd,bu,_(bv,dD)),_(T,dL,V,W,X,dw,n,dx,ba,dx,bc,bd,s,_(cH,cI,bf,_(bg,dM,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dN,bn,ch)),P,_(),bq,_(),S,[_(T,dO,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(cH,cI,bf,_(bg,dM,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dN,bn,ch)),P,_(),bq,_())],bu,_(bv,dD)),_(T,dP,V,W,X,dw,n,dx,ba,dx,bc,bd,s,_(cH,cI,bf,_(bg,dQ,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dR,bn,ch)),P,_(),bq,_(),S,[_(T,dS,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(cH,cI,bf,_(bg,dQ,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dR,bn,ch)),P,_(),bq,_())],Q,_(bJ,_(bK,bL,bM,[_(bK,bN,bO,g,bP,[_(bQ,bR,bK,cd,bT,_(bU,k,bW,bd),bX,bY)])])),bZ,bd,bu,_(bv,dD)),_(T,dT,V,W,X,dw,n,dx,ba,dx,bc,bd,s,_(cH,cI,bf,_(bg,dy,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dU,bn,ch)),P,_(),bq,_(),S,[_(T,dV,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(cH,cI,bf,_(bg,dy,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,dU,bn,ch)),P,_(),bq,_())],Q,_(bJ,_(bK,bL,bM,[_(bK,bN,bO,g,bP,[_(bQ,bR,bK,dW,bT,_(bU,k,b,dX,bW,bd),bX,bY)])])),bZ,bd,bu,_(bv,dD)),_(T,dY,V,W,X,dw,n,dx,ba,dx,bc,bd,s,_(cH,cI,bf,_(bg,dz,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,ch,bn,ch)),P,_(),bq,_(),S,[_(T,dZ,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(cH,cI,bf,_(bg,dz,bi,ds),t,cU,M,cP,cy,cN,x,_(y,z,A,cW),bD,_(y,z,A,bE),O,J,bk,_(bl,ch,bn,ch)),P,_(),bq,_())],Q,_(bJ,_(bK,bL,bM,[_(bK,bN,bO,g,bP,[_(bQ,bR,bK,cd,bT,_(bU,k,bW,bd),bX,bY)])])),bZ,bd,bu,_(bv,dD))]),_(T,ea,V,W,X,bz,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,eb,bi,eb),t,ec,bk,_(bl,du,bn,ed)),P,_(),bq,_(),S,[_(T,ee,V,W,X,null,bs,bd,n,bt,ba,bb,bc,bd,s,_(bf,_(bg,eb,bi,eb),t,ec,bk,_(bl,du,bn,ed)),P,_(),bq,_())],bx,g)]))),ef,_(eg,_(eh,ei),ej,_(eh,ek),el,_(eh,em),en,_(eh,eo),ep,_(eh,eq),er,_(eh,es),et,_(eh,eu,ev,_(eh,ew),ex,_(eh,ey),ez,_(eh,eA),eB,_(eh,eC),eD,_(eh,eE),eF,_(eh,eG),eH,_(eh,eI),eJ,_(eh,eK),eL,_(eh,eM),eN,_(eh,eO),eP,_(eh,eQ),eR,_(eh,eS),eT,_(eh,eU),eV,_(eh,eW),eX,_(eh,eY),eZ,_(eh,fa),fb,_(eh,fc),fd,_(eh,fe),ff,_(eh,fg),fh,_(eh,fi),fj,_(eh,fk),fl,_(eh,fm),fn,_(eh,fo),fp,_(eh,fq),fr,_(eh,fs),ft,_(eh,fu),fv,_(eh,fw),fx,_(eh,fy),fz,_(eh,fA))));}; 
var b="url",c="首页-未创建菜品.html",d="generationDate",e=new Date(1546564660216.15),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="97aeeb90566641e3970155d3c0fad87e",n="type",o="Axure:Page",p="name",q="首页-未创建菜品",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="a4919036349d482dbc0ff434f7147c82",V="label",W="",X="friendlyType",Y="Paragraph",Z="vectorShape",ba="styleType",bb="paragraph",bc="visible",bd=true,be="4988d43d80b44008a4a415096f1632af",bf="size",bg="width",bh=341,bi="height",bj=76,bk="location",bl="x",bm=71,bn="y",bo=98,bp="'PingFangSC-Regular', 'PingFang SC'",bq="imageOverrides",br="0e789880587f4cb3a6ad0b159bae63b1",bs="isContained",bt="richTextPanel",bu="images",bv="normal~",bw="images/首页-未创建菜品/u531.png",bx="generateCompound",by="6f7d53684375442581acd5b18158dc74",bz="Rectangle",bA=48,bB=22,bC="4b7bfc596114427989e10bb0b557d0ce",bD="borderFill",bE=0xFFE4E4E4,bF=313,bG=129,bH=0xFFFFFF,bI="c1d430a18af9411493686de93fb89a20",bJ="onClick",bK="description",bL="OnClick",bM="cases",bN="Case 1",bO="isNewIfGroup",bP="actions",bQ="action",bR="linkWindow",bS="Open 门店列表 in Current Window",bT="target",bU="targetType",bV="门店列表.html",bW="includeVariables",bX="linkType",bY="current",bZ="tabbable",ca="3066d03a7b824fd1abaae046e3451a1d",cb=159,cc="f8866174c25941cca006168203062e3a",cd="Open Link in Current Window",ce="902b656009384ccfb08f2dab5b47de6d",cf="主框架",cg="referenceDiagramObject",ch=0,ci=-3,cj=1200,ck=72,cl="masterId",cm="42b294620c2d49c7af5b1798469a7eae",cn="masters",co="42b294620c2d49c7af5b1798469a7eae",cp="Axure:Master",cq="964c4380226c435fac76d82007637791",cr="0882bfcd7d11450d85d157758311dca5",cs="horizontalAlignment",ct="left",cu="foreGroundFill",cv=0xFFCCCCCC,cw="opacity",cx=1,cy="fontSize",cz="14px",cA=0x7FF2F2F2,cB="f0e6d8a5be734a0daeab12e0ad1745e8",cC="1e3bb79c77364130b7ce098d1c3a6667",cD="'ArialRoundedMTBold', 'Arial Rounded MT Bold'",cE=0xFF666666,cF="136ce6e721b9428c8d7a12533d585265",cG="d6b97775354a4bc39364a6d5ab27a0f3",cH="fontWeight",cI="200",cJ=55,cK=17,cL=1066,cM=19,cN="12px",cO=0xFF1E1E1E,cP="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",cQ="529afe58e4dc499694f5761ad7a21ee3",cR="935c51cfa24d4fb3b10579d19575f977",cS=54,cT=21,cU="33ea2511485c479dbf973af3302f2352",cV=1133,cW=0xF2F2F2,cX="099c30624b42452fa3217e4342c93502",cY="f2df399f426a4c0eb54c2c26b150d28c",cZ="500",da=126,db=18,dc="'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC'",dd="16px",de=0xFF999999,df="649cae71611a4c7785ae5cbebc3e7bca",dg="images/首页-未创建菜品/u546.png",dh="e7b01238e07e447e847ff3b0d615464d",di="Horizontal Line",dj="horizontalLine",dk="f48196c19ab74fb7b3acb5151ce8ea2d",dl="d3a4cb92122f441391bc879f5fee4a36",dm="images/首页-未创建菜品/u548.png",dn="ed086362cda14ff890b2e717f817b7bb",dp="Table",dq="table",dr=499,ds=39,dt=194,du=11,dv="c2345ff754764c5694b9d57abadd752c",dw="Table Cell",dx="tableCell",dy=80,dz=50,dA="25e2a2b7358d443dbebd012dc7ed75dd",dB="Open 员工列表 in Current Window",dC="员工列表.html",dD="resources/images/transparent.gif",dE="d9bb22ac531d412798fee0e18a9dfaa8",dF=60,dG=130,dH="bf1394b182d94afd91a21f3436401771",dI="2aefc4c3d8894e52aa3df4fbbfacebc3",dJ=344,dK="099f184cab5e442184c22d5dd1b68606",dL="79eed072de834103a429f51c386cddfd",dM=74,dN=270,dO="dd9a354120ae466bb21d8933a7357fd8",dP="9d46b8ed273c4704855160ba7c2c2f8e",dQ=75,dR=424,dS="e2a2baf1e6bb4216af19b1b5616e33e1",dT="89cf184dc4de41d09643d2c278a6f0b7",dU=190,dV="903b1ae3f6664ccabc0e8ba890380e4b",dW="Open 全部商品(商品库) in Current Window",dX="全部商品_商品库_.html",dY="8c26f56a3753450dbbef8d6cfde13d67",dZ="fbdda6d0b0094103a3f2692a764d333a",ea="d53c7cd42bee481283045fd015fd50d5",eb=34,ec="47641f9a00ac465095d6b672bbdffef6",ed=12,ee="abdf932a631e417992ae4dba96097eda",ef="objectPaths",eg="a4919036349d482dbc0ff434f7147c82",eh="scriptId",ei="u531",ej="0e789880587f4cb3a6ad0b159bae63b1",ek="u532",el="6f7d53684375442581acd5b18158dc74",em="u533",en="c1d430a18af9411493686de93fb89a20",eo="u534",ep="3066d03a7b824fd1abaae046e3451a1d",eq="u535",er="f8866174c25941cca006168203062e3a",es="u536",et="902b656009384ccfb08f2dab5b47de6d",eu="u537",ev="964c4380226c435fac76d82007637791",ew="u538",ex="f0e6d8a5be734a0daeab12e0ad1745e8",ey="u539",ez="1e3bb79c77364130b7ce098d1c3a6667",eA="u540",eB="136ce6e721b9428c8d7a12533d585265",eC="u541",eD="d6b97775354a4bc39364a6d5ab27a0f3",eE="u542",eF="529afe58e4dc499694f5761ad7a21ee3",eG="u543",eH="935c51cfa24d4fb3b10579d19575f977",eI="u544",eJ="099c30624b42452fa3217e4342c93502",eK="u545",eL="f2df399f426a4c0eb54c2c26b150d28c",eM="u546",eN="649cae71611a4c7785ae5cbebc3e7bca",eO="u547",eP="e7b01238e07e447e847ff3b0d615464d",eQ="u548",eR="d3a4cb92122f441391bc879f5fee4a36",eS="u549",eT="ed086362cda14ff890b2e717f817b7bb",eU="u550",eV="8c26f56a3753450dbbef8d6cfde13d67",eW="u551",eX="fbdda6d0b0094103a3f2692a764d333a",eY="u552",eZ="c2345ff754764c5694b9d57abadd752c",fa="u553",fb="25e2a2b7358d443dbebd012dc7ed75dd",fc="u554",fd="d9bb22ac531d412798fee0e18a9dfaa8",fe="u555",ff="bf1394b182d94afd91a21f3436401771",fg="u556",fh="89cf184dc4de41d09643d2c278a6f0b7",fi="u557",fj="903b1ae3f6664ccabc0e8ba890380e4b",fk="u558",fl="79eed072de834103a429f51c386cddfd",fm="u559",fn="dd9a354120ae466bb21d8933a7357fd8",fo="u560",fp="2aefc4c3d8894e52aa3df4fbbfacebc3",fq="u561",fr="099f184cab5e442184c22d5dd1b68606",fs="u562",ft="9d46b8ed273c4704855160ba7c2c2f8e",fu="u563",fv="e2a2baf1e6bb4216af19b1b5616e33e1",fw="u564",fx="d53c7cd42bee481283045fd015fd50d5",fy="u565",fz="abdf932a631e417992ae4dba96097eda",fA="u566";
return _creator();
})());