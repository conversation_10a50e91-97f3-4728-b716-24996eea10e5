body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1439px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u421_img {
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:16px;
}
#u421 {
  position:absolute;
  left:15px;
  top:13px;
  width:132px;
  height:16px;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FF00FF;
}
#u422 {
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  white-space:nowrap;
}
#u423_img {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  height:16px;
}
#u423 {
  position:absolute;
  left:15px;
  top:1295px;
  width:164px;
  height:16px;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FF00FF;
}
#u424 {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  white-space:nowrap;
}
#u425_img {
  position:absolute;
  left:0px;
  top:0px;
  width:1412px;
  height:72px;
}
#u425 {
  position:absolute;
  left:27px;
  top:1327px;
  width:1412px;
  height:72px;
}
#u426 {
  position:absolute;
  left:2px;
  top:28px;
  width:1408px;
  visibility:hidden;
  word-wrap:break-word;
}
#u427_img {
  position:absolute;
  left:0px;
  top:0px;
  width:1162px;
  height:1096px;
}
#u427 {
  position:absolute;
  left:15px;
  top:58px;
  width:1162px;
  height:1096px;
}
#u428 {
  position:absolute;
  left:2px;
  top:540px;
  width:1158px;
  visibility:hidden;
  word-wrap:break-word;
}
