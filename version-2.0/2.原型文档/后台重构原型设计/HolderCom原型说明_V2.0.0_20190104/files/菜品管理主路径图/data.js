$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(t,be,bf,_(bg,bh,bi,bj),M,bk,bl,bm,bn,_(y,z,A,bo,bp,bq),br,_(bs,bt,bu,bv)),P,_(),bw,_(),S,[_(T,bx,V,W,X,null,by,bd,n,bz,ba,bb,bc,bd,s,_(t,be,bf,_(bg,bh,bi,bj),M,bk,bl,bm,bn,_(y,z,A,bo,bp,bq),br,_(bs,bt,bu,bv)),P,_(),bw,_())],bA,_(bB,bC),bD,g),_(T,bE,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(t,be,bf,_(bg,bF,bi,bj),M,bk,bl,bm,bn,_(y,z,A,bo,bp,bq),br,_(bs,bt,bu,bG)),P,_(),bw,_(),S,[_(T,bH,V,W,X,null,by,bd,n,bz,ba,bb,bc,bd,s,_(t,be,bf,_(bg,bF,bi,bj),M,bk,bl,bm,bn,_(y,z,A,bo,bp,bq),br,_(bs,bt,bu,bG)),P,_(),bw,_())],bA,_(bB,bI),bD,g),_(T,bJ,V,W,X,bK,n,bL,ba,bL,bc,bd,s,_(t,bM,bf,_(bg,bN,bi,bO),br,_(bs,bP,bu,bQ)),P,_(),bw,_(),S,[_(T,bR,V,W,X,null,by,bd,n,bz,ba,bb,bc,bd,s,_(t,bM,bf,_(bg,bN,bi,bO),br,_(bs,bP,bu,bQ)),P,_(),bw,_())],bA,_(bB,bS)),_(T,bT,V,W,X,bK,n,bL,ba,bL,bc,bd,s,_(t,bM,bf,_(bg,bU,bi,bV),br,_(bs,bt,bu,bW)),P,_(),bw,_(),S,[_(T,bX,V,W,X,null,by,bd,n,bz,ba,bb,bc,bd,s,_(t,bM,bf,_(bg,bU,bi,bV),br,_(bs,bt,bu,bW)),P,_(),bw,_())],bA,_(bB,bY))])),bZ,_(),ca,_(cb,_(cc,cd),ce,_(cc,cf),cg,_(cc,ch),ci,_(cc,cj),ck,_(cc,cl),cm,_(cc,cn),co,_(cc,cp),cq,_(cc,cr)));}; 
var b="url",c="菜品管理主路径图.html",d="generationDate",e=new Date(1546564659744.19),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="e53793cf74234a50a9f06e29b79fb60e",n="type",o="Axure:Page",p="name",q="菜品管理主路径图",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="e97482725e6f424db99b88f450e4f58e",V="label",W="",X="friendlyType",Y="Paragraph",Z="vectorShape",ba="styleType",bb="paragraph",bc="visible",bd=true,be="4988d43d80b44008a4a415096f1632af",bf="size",bg="width",bh=132,bi="height",bj=16,bk="'.AppleSystemUIFont'",bl="fontSize",bm="16px",bn="foreGroundFill",bo=0xFFFF00FF,bp="opacity",bq=1,br="location",bs="x",bt=15,bu="y",bv=13,bw="imageOverrides",bx="67a60231e2a74d11a94563aba871e510",by="isContained",bz="richTextPanel",bA="images",bB="normal~",bC="images/菜品管理主路径图/u421.png",bD="generateCompound",bE="7e917ad101cc4c9bbfc22b342c55a9c7",bF=164,bG=1295,bH="9a69a2c8663d4a31a1ad7bfa56279bfa",bI="images/菜品管理主路径图/u423.png",bJ="2053849582ff4bc4b66496ae586a852e",bK="Image",bL="imageBox",bM="********************************",bN=1412,bO=72,bP=27,bQ=1327,bR="2aa728d2a9184416a4b803446533b642",bS="images/菜品管理主路径图/u425.png",bT="06ecae47015c4d1f83fc91a4cc975daf",bU=1162,bV=1096,bW=58,bX="239bea6783fa43e58f7e49ee29225c3f",bY="images/菜品管理主路径图/u427.jpg",bZ="masters",ca="objectPaths",cb="e97482725e6f424db99b88f450e4f58e",cc="scriptId",cd="u421",ce="67a60231e2a74d11a94563aba871e510",cf="u422",cg="7e917ad101cc4c9bbfc22b342c55a9c7",ch="u423",ci="9a69a2c8663d4a31a1ad7bfa56279bfa",cj="u424",ck="2053849582ff4bc4b66496ae586a852e",cl="u425",cm="2aa728d2a9184416a4b803446533b642",cn="u426",co="06ecae47015c4d1f83fc91a4cc975daf",cp="u427",cq="239bea6783fa43e58f7e49ee29225c3f",cr="u428";
return _creator();
})());