$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(t,be,bf,_(bg,bh,bi,bj),bk,_(bl,bm,bn,bo),M,bp,bq,br),P,_(),bs,_(),S,[_(T,bt,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(t,be,bf,_(bg,bh,bi,bj),bk,_(bl,bm,bn,bo),M,bp,bq,br),P,_(),bs,_())],bw,_(bx,by),bz,g),_(T,bA,V,W,X,bB,n,bC,ba,bC,bc,bd,s,_(),P,_(),bs,_(),bD,bE)])),bF,_(bG,_(l,bG,n,bH,p,bB,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,bI,V,W,X,bJ,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,bK,bi,bL),t,bM,bN,bO,bP,_(y,z,A,bQ,bR,bS),bq,bT,bU,_(y,z,A,B),x,_(y,z,A,bV)),P,_(),bs,_(),S,[_(T,bW,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bf,_(bg,bK,bi,bL),t,bM,bN,bO,bP,_(y,z,A,bQ,bR,bS),bq,bT,bU,_(y,z,A,B),x,_(y,z,A,bV)),P,_(),bs,_())],bz,g),_(T,bX,V,W,X,bJ,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,bK,bi,bY),t,bM,bN,bO,M,bZ,bP,_(y,z,A,bQ,bR,bS),bq,bT,bU,_(y,z,A,ca),x,_(y,z,A,cb)),P,_(),bs,_(),S,[_(T,cc,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bf,_(bg,bK,bi,bY),t,bM,bN,bO,M,bZ,bP,_(y,z,A,bQ,bR,bS),bq,bT,bU,_(y,z,A,ca),x,_(y,z,A,cb)),P,_(),bs,_())],bz,g),_(T,cd,V,W,X,bJ,n,Z,ba,Z,bc,bd,s,_(ce,cf,bf,_(bg,cg,bi,ch),t,be,bk,_(bl,ci,bn,cj),bq,br,bP,_(y,z,A,ck,bR,bS),M,bp),P,_(),bs,_(),S,[_(T,cl,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(ce,cf,bf,_(bg,cg,bi,ch),t,be,bk,_(bl,ci,bn,cj),bq,br,bP,_(y,z,A,ck,bR,bS),M,bp),P,_(),bs,_())],Q,_(cm,_(cn,co,cp,[_(cn,cq,cr,g,cs,[])])),ct,bd,bz,g),_(T,cu,V,W,X,bJ,n,Z,ba,Z,bc,bd,s,_(ce,cf,bf,_(bg,cv,bi,cw),t,cx,bk,_(bl,cy,bn,ch),bq,br,M,bp,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J),P,_(),bs,_(),S,[_(T,cA,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(ce,cf,bf,_(bg,cv,bi,cw),t,cx,bk,_(bl,cy,bn,ch),bq,br,M,bp,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J),P,_(),bs,_())],Q,_(cm,_(cn,co,cp,[_(cn,cq,cr,g,cs,[_(cB,cC,cn,cD,cE,_(cF,k,cG,bd),cH,cI)])])),ct,bd,bz,g),_(T,cJ,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(ce,cK,t,be,bf,_(bg,cL,bi,cM),bk,_(bl,cN,bn,cO),M,cP,bq,cQ,bP,_(y,z,A,cR,bR,bS)),P,_(),bs,_(),S,[_(T,cS,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(ce,cK,t,be,bf,_(bg,cL,bi,cM),bk,_(bl,cN,bn,cO),M,cP,bq,cQ,bP,_(y,z,A,cR,bR,bS)),P,_(),bs,_())],bw,_(bx,cT),bz,g),_(T,cU,V,W,X,cV,n,Z,ba,cW,bc,bd,s,_(bk,_(bl,cX,bn,bY),bf,_(bg,bK,bi,bS),bU,_(y,z,A,bQ),t,cY),P,_(),bs,_(),S,[_(T,cZ,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bk,_(bl,cX,bn,bY),bf,_(bg,bK,bi,bS),bU,_(y,z,A,bQ),t,cY),P,_(),bs,_())],bw,_(bx,da),bz,g),_(T,db,V,W,X,dc,n,dd,ba,dd,bc,bd,s,_(bf,_(bg,de,bi,df),bk,_(bl,dg,bn,dh)),P,_(),bs,_(),S,[_(T,di,V,W,X,dj,n,dk,ba,dk,bc,bd,s,_(ce,cf,bf,_(bg,dl,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,dm,bn,cX)),P,_(),bs,_(),S,[_(T,dn,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(ce,cf,bf,_(bg,dl,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,dm,bn,cX)),P,_(),bs,_())],Q,_(cm,_(cn,co,cp,[_(cn,cq,cr,g,cs,[_(cB,cC,cn,dp,cE,_(cF,k,b,dq,cG,bd),cH,cI)])])),ct,bd,bw,_(bx,dr)),_(T,ds,V,W,X,dj,n,dk,ba,dk,bc,bd,s,_(ce,cf,bf,_(bg,dt,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,du,bn,cX)),P,_(),bs,_(),S,[_(T,dv,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(ce,cf,bf,_(bg,dt,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,du,bn,cX)),P,_(),bs,_())],Q,_(cm,_(cn,co,cp,[_(cn,cq,cr,g,cs,[_(cB,cC,cn,cD,cE,_(cF,k,cG,bd),cH,cI)])])),ct,bd,bw,_(bx,dr)),_(T,dw,V,W,X,dj,n,dk,ba,dk,bc,bd,s,_(ce,cf,bf,_(bg,dl,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,dx,bn,cX)),P,_(),bs,_(),S,[_(T,dy,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(ce,cf,bf,_(bg,dl,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,dx,bn,cX)),P,_(),bs,_())],Q,_(cm,_(cn,co,cp,[_(cn,cq,cr,g,cs,[_(cB,cC,cn,cD,cE,_(cF,k,cG,bd),cH,cI)])])),ct,bd,bw,_(bx,dr)),_(T,dz,V,W,X,dj,n,dk,ba,dk,bc,bd,s,_(ce,cf,bf,_(bg,dA,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,dB,bn,cX)),P,_(),bs,_(),S,[_(T,dC,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(ce,cf,bf,_(bg,dA,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,dB,bn,cX)),P,_(),bs,_())],bw,_(bx,dr)),_(T,dD,V,W,X,dj,n,dk,ba,dk,bc,bd,s,_(ce,cf,bf,_(bg,dE,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,dF,bn,cX)),P,_(),bs,_(),S,[_(T,dG,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(ce,cf,bf,_(bg,dE,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,dF,bn,cX)),P,_(),bs,_())],Q,_(cm,_(cn,co,cp,[_(cn,cq,cr,g,cs,[_(cB,cC,cn,cD,cE,_(cF,k,cG,bd),cH,cI)])])),ct,bd,bw,_(bx,dr)),_(T,dH,V,W,X,dj,n,dk,ba,dk,bc,bd,s,_(ce,cf,bf,_(bg,dl,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,dI,bn,cX)),P,_(),bs,_(),S,[_(T,dJ,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(ce,cf,bf,_(bg,dl,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,dI,bn,cX)),P,_(),bs,_())],Q,_(cm,_(cn,co,cp,[_(cn,cq,cr,g,cs,[_(cB,cC,cn,dK,cE,_(cF,k,b,dL,cG,bd),cH,cI)])])),ct,bd,bw,_(bx,dr)),_(T,dM,V,W,X,dj,n,dk,ba,dk,bc,bd,s,_(ce,cf,bf,_(bg,dm,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,cX,bn,cX)),P,_(),bs,_(),S,[_(T,dN,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(ce,cf,bf,_(bg,dm,bi,df),t,cx,M,bp,bq,br,x,_(y,z,A,cz),bU,_(y,z,A,cb),O,J,bk,_(bl,cX,bn,cX)),P,_(),bs,_())],Q,_(cm,_(cn,co,cp,[_(cn,cq,cr,g,cs,[_(cB,cC,cn,cD,cE,_(cF,k,cG,bd),cH,cI)])])),ct,bd,bw,_(bx,dr))]),_(T,dO,V,W,X,bJ,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,dP,bi,dP),t,dQ,bk,_(bl,dh,bn,dR)),P,_(),bs,_(),S,[_(T,dS,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bf,_(bg,dP,bi,dP),t,dQ,bk,_(bl,dh,bn,dR)),P,_(),bs,_())],bz,g)]))),dT,_(dU,_(dV,dW),dX,_(dV,dY),dZ,_(dV,ea,eb,_(dV,ec),ed,_(dV,ee),ef,_(dV,eg),eh,_(dV,ei),ej,_(dV,ek),el,_(dV,em),en,_(dV,eo),ep,_(dV,eq),er,_(dV,es),et,_(dV,eu),ev,_(dV,ew),ex,_(dV,ey),ez,_(dV,eA),eB,_(dV,eC),eD,_(dV,eE),eF,_(dV,eG),eH,_(dV,eI),eJ,_(dV,eK),eL,_(dV,eM),eN,_(dV,eO),eP,_(dV,eQ),eR,_(dV,eS),eT,_(dV,eU),eV,_(dV,eW),eX,_(dV,eY),eZ,_(dV,fa),fb,_(dV,fc),fd,_(dV,fe),ff,_(dV,fg))));}; 
var b="url",c="首页-有商品无营业数据.html",d="generationDate",e=new Date(1546564660303.8),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="13fab4802ace421299b10980327f71ce",n="type",o="Axure:Page",p="name",q="首页-有商品无营业数据",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="45871e59176b457d9734c70c1022b10d",V="label",W="",X="friendlyType",Y="Paragraph",Z="vectorShape",ba="styleType",bb="paragraph",bc="visible",bd=true,be="4988d43d80b44008a4a415096f1632af",bf="size",bg="width",bh=341,bi="height",bj=51,bk="location",bl="x",bm=26,bn="y",bo=123,bp="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",bq="fontSize",br="12px",bs="imageOverrides",bt="21d7b99faa134d70904fb2c652a7feba",bu="isContained",bv="richTextPanel",bw="images",bx="normal~",by="images/首页-有商品无营业数据/u567.png",bz="generateCompound",bA="54631fbd89dd4b7384dfbeb77a0a7867",bB="主框架",bC="referenceDiagramObject",bD="masterId",bE="42b294620c2d49c7af5b1798469a7eae",bF="masters",bG="42b294620c2d49c7af5b1798469a7eae",bH="Axure:Master",bI="964c4380226c435fac76d82007637791",bJ="Rectangle",bK=1200,bL=72,bM="0882bfcd7d11450d85d157758311dca5",bN="horizontalAlignment",bO="left",bP="foreGroundFill",bQ=0xFFCCCCCC,bR="opacity",bS=1,bT="14px",bU="borderFill",bV=0x7FF2F2F2,bW="f0e6d8a5be734a0daeab12e0ad1745e8",bX="1e3bb79c77364130b7ce098d1c3a6667",bY=71,bZ="'ArialRoundedMTBold', 'Arial Rounded MT Bold'",ca=0xFF666666,cb=0xFFE4E4E4,cc="136ce6e721b9428c8d7a12533d585265",cd="d6b97775354a4bc39364a6d5ab27a0f3",ce="fontWeight",cf="200",cg=55,ch=17,ci=1066,cj=19,ck=0xFF1E1E1E,cl="529afe58e4dc499694f5761ad7a21ee3",cm="onClick",cn="description",co="OnClick",cp="cases",cq="Case 1",cr="isNewIfGroup",cs="actions",ct="tabbable",cu="935c51cfa24d4fb3b10579d19575f977",cv=54,cw=21,cx="33ea2511485c479dbf973af3302f2352",cy=1133,cz=0xF2F2F2,cA="099c30624b42452fa3217e4342c93502",cB="action",cC="linkWindow",cD="Open Link in Current Window",cE="target",cF="targetType",cG="includeVariables",cH="linkType",cI="current",cJ="f2df399f426a4c0eb54c2c26b150d28c",cK="500",cL=126,cM=22,cN=48,cO=18,cP="'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC'",cQ="16px",cR=0xFF999999,cS="649cae71611a4c7785ae5cbebc3e7bca",cT="images/首页-未创建菜品/u546.png",cU="e7b01238e07e447e847ff3b0d615464d",cV="Horizontal Line",cW="horizontalLine",cX=0,cY="f48196c19ab74fb7b3acb5151ce8ea2d",cZ="d3a4cb92122f441391bc879f5fee4a36",da="images/首页-未创建菜品/u548.png",db="ed086362cda14ff890b2e717f817b7bb",dc="Table",dd="table",de=499,df=39,dg=194,dh=11,di="c2345ff754764c5694b9d57abadd752c",dj="Table Cell",dk="tableCell",dl=80,dm=50,dn="25e2a2b7358d443dbebd012dc7ed75dd",dp="Open 员工列表 in Current Window",dq="员工列表.html",dr="resources/images/transparent.gif",ds="d9bb22ac531d412798fee0e18a9dfaa8",dt=60,du=130,dv="bf1394b182d94afd91a21f3436401771",dw="2aefc4c3d8894e52aa3df4fbbfacebc3",dx=344,dy="099f184cab5e442184c22d5dd1b68606",dz="79eed072de834103a429f51c386cddfd",dA=74,dB=270,dC="dd9a354120ae466bb21d8933a7357fd8",dD="9d46b8ed273c4704855160ba7c2c2f8e",dE=75,dF=424,dG="e2a2baf1e6bb4216af19b1b5616e33e1",dH="89cf184dc4de41d09643d2c278a6f0b7",dI=190,dJ="903b1ae3f6664ccabc0e8ba890380e4b",dK="Open 全部商品(商品库) in Current Window",dL="全部商品_商品库_.html",dM="8c26f56a3753450dbbef8d6cfde13d67",dN="fbdda6d0b0094103a3f2692a764d333a",dO="d53c7cd42bee481283045fd015fd50d5",dP=34,dQ="47641f9a00ac465095d6b672bbdffef6",dR=12,dS="abdf932a631e417992ae4dba96097eda",dT="objectPaths",dU="45871e59176b457d9734c70c1022b10d",dV="scriptId",dW="u567",dX="21d7b99faa134d70904fb2c652a7feba",dY="u568",dZ="54631fbd89dd4b7384dfbeb77a0a7867",ea="u569",eb="964c4380226c435fac76d82007637791",ec="u570",ed="f0e6d8a5be734a0daeab12e0ad1745e8",ee="u571",ef="1e3bb79c77364130b7ce098d1c3a6667",eg="u572",eh="136ce6e721b9428c8d7a12533d585265",ei="u573",ej="d6b97775354a4bc39364a6d5ab27a0f3",ek="u574",el="529afe58e4dc499694f5761ad7a21ee3",em="u575",en="935c51cfa24d4fb3b10579d19575f977",eo="u576",ep="099c30624b42452fa3217e4342c93502",eq="u577",er="f2df399f426a4c0eb54c2c26b150d28c",es="u578",et="649cae71611a4c7785ae5cbebc3e7bca",eu="u579",ev="e7b01238e07e447e847ff3b0d615464d",ew="u580",ex="d3a4cb92122f441391bc879f5fee4a36",ey="u581",ez="ed086362cda14ff890b2e717f817b7bb",eA="u582",eB="8c26f56a3753450dbbef8d6cfde13d67",eC="u583",eD="fbdda6d0b0094103a3f2692a764d333a",eE="u584",eF="c2345ff754764c5694b9d57abadd752c",eG="u585",eH="25e2a2b7358d443dbebd012dc7ed75dd",eI="u586",eJ="d9bb22ac531d412798fee0e18a9dfaa8",eK="u587",eL="bf1394b182d94afd91a21f3436401771",eM="u588",eN="89cf184dc4de41d09643d2c278a6f0b7",eO="u589",eP="903b1ae3f6664ccabc0e8ba890380e4b",eQ="u590",eR="79eed072de834103a429f51c386cddfd",eS="u591",eT="dd9a354120ae466bb21d8933a7357fd8",eU="u592",eV="2aefc4c3d8894e52aa3df4fbbfacebc3",eW="u593",eX="099f184cab5e442184c22d5dd1b68606",eY="u594",eZ="9d46b8ed273c4704855160ba7c2c2f8e",fa="u595",fb="e2a2baf1e6bb4216af19b1b5616e33e1",fc="u596",fd="d53c7cd42bee481283045fd015fd50d5",fe="u597",ff="abdf932a631e417992ae4dba96097eda",fg="u598";
return _creator();
})());