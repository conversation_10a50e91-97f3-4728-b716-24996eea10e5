$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_(),S,[_(T,bp,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_())],bt,_(bu,bv)),_(T,bw,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,bz,bh,bA),bj,_(bk,bB,bm,bC)),P,_(),bo,_(),bD,bE,bF,g,bG,g,bH,[_(T,bI,V,bJ,n,bK,S,[_(T,bL,V,W,X,Y,bM,bw,bN,bO,n,Z,ba,Z,bb,bc,s,_(t,bd,be,_(bf,bP,bh,bQ)),P,_(),bo,_(),S,[_(T,bR,V,W,X,null,bq,bc,bM,bw,bN,bO,n,br,ba,bs,bb,bc,s,_(t,bd,be,_(bf,bP,bh,bQ)),P,_(),bo,_())],bt,_(bu,bS))],s,_(x,_(y,z,A,bT),C,null,D,w,E,w,F,G),P,_())])])),bU,_(),bV,_(bW,_(bX,bY),bZ,_(bX,ca),cb,_(bX,cc),cd,_(bX,ce),cf,_(bX,cg)));}; 
var b="url",c="菜品管理操作交互逻辑.html",d="generationDate",e=new Date(1546564659799.09),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="27473e39398b4812bf8cb74b1e87f15c",n="type",o="Axure:Page",p="name",q="菜品管理操作交互逻辑",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="4f602baaa4df466eb3a6ade2a378c633",V="label",W="",X="friendlyType",Y="Image",Z="imageBox",ba="styleType",bb="visible",bc=true,bd="75a91ee5b9d042cfa01b8d565fe289c0",be="size",bf="width",bg=2979,bh="height",bi=119,bj="location",bk="x",bl=97,bm="y",bn=11,bo="imageOverrides",bp="f2cc89876c4548f9aef7f214e6e5338e",bq="isContained",br="richTextPanel",bs="paragraph",bt="images",bu="normal~",bv="images/菜品管理操作交互逻辑/u429.png",bw="875d3b1277084d0f9b1d03184ca873ae",bx="Dynamic Panel",by="dynamicPanel",bz=2770,bA=860,bB=19,bC=138,bD="scrollbars",bE="verticalAsNeeded",bF="fitToContent",bG="propagate",bH="diagrams",bI="10bae6c6c3ec42d0af0eb8b2c5012ed8",bJ="State1",bK="Axure:PanelDiagram",bL="f489c4f34e70406ba5a0518d0b7d74bd",bM="parentDynamicPanel",bN="panelIndex",bO=0,bP=3043,bQ=2725,bR="eef862b0a1d049309c174a07f75a9ae3",bS="images/菜品管理操作交互逻辑/u432.png",bT=0xFFFFFF,bU="masters",bV="objectPaths",bW="4f602baaa4df466eb3a6ade2a378c633",bX="scriptId",bY="u429",bZ="f2cc89876c4548f9aef7f214e6e5338e",ca="u430",cb="875d3b1277084d0f9b1d03184ca873ae",cc="u431",cd="f489c4f34e70406ba5a0518d0b7d74bd",ce="u432",cf="eef862b0a1d049309c174a07f75a9ae3",cg="u433";
return _creator();
})());