body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:519px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u525_img {
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:50px;
}
#u525 {
  position:absolute;
  left:31px;
  top:31px;
  width:119px;
  height:50px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:36px;
  color:#999999;
  text-align:center;
}
#u526 {
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  word-wrap:break-word;
}
#u527_img {
  position:absolute;
  left:0px;
  top:0px;
  width:249px;
  height:20px;
}
#u527 {
  position:absolute;
  left:270px;
  top:128px;
  width:249px;
  height:20px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:14px;
}
#u528 {
  position:absolute;
  left:0px;
  top:0px;
  width:249px;
  word-wrap:break-word;
}
#u529_img {
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
}
#u529 {
  position:absolute;
  left:301px;
  top:206px;
  width:49px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#0000FF;
  text-align:center;
}
#u530 {
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  white-space:nowrap;
}
