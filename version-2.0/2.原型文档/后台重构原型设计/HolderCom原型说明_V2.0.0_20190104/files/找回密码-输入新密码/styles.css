body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1010px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u510_img {
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:50px;
}
#u510 {
  position:absolute;
  left:31px;
  top:31px;
  width:119px;
  height:50px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:36px;
  color:#999999;
  text-align:center;
}
#u511 {
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  word-wrap:break-word;
}
#u512 {
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u513 {
  position:absolute;
  left:364px;
  top:214px;
  width:240px;
  height:30px;
}
#u513_input {
  position:absolute;
  left:0px;
  top:0px;
  width:240px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#666666;
  text-align:left;
}
#u514_div {
  position:absolute;
  left:0px;
  top:0px;
  width:313px;
  height:41px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u514 {
  position:absolute;
  left:291px;
  top:298px;
  width:313px;
  height:41px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u515 {
  position:absolute;
  left:2px;
  top:12px;
  width:309px;
  word-wrap:break-word;
}
#u516 {
  position:absolute;
  left:364px;
  top:173px;
  width:240px;
  height:30px;
}
#u516_input {
  position:absolute;
  left:0px;
  top:0px;
  width:240px;
  height:30px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#666666;
  text-align:left;
}
#u517_img {
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:17px;
}
#u517 {
  position:absolute;
  left:302px;
  top:180px;
  width:64px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
  text-align:right;
}
#u518 {
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  word-wrap:break-word;
}
#u519_img {
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:17px;
}
#u519 {
  position:absolute;
  left:291px;
  top:220px;
  width:75px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
  text-align:right;
}
#u520 {
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  word-wrap:break-word;
}
#u521_img {
  position:absolute;
  left:0px;
  top:0px;
  width:249px;
  height:20px;
}
#u521 {
  position:absolute;
  left:270px;
  top:128px;
  width:249px;
  height:20px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:14px;
}
#u522 {
  position:absolute;
  left:0px;
  top:0px;
  width:249px;
  word-wrap:break-word;
}
#u523_img {
  position:absolute;
  left:0px;
  top:0px;
  width:795px;
  height:2px;
}
#u523 {
  position:absolute;
  left:216px;
  top:158px;
  width:794px;
  height:1px;
}
#u524 {
  position:absolute;
  left:2px;
  top:-8px;
  width:790px;
  visibility:hidden;
  word-wrap:break-word;
}
