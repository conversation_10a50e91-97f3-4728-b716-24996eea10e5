$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(be,bf,t,bg,bh,_(bi,bj,bk,bl),M,bm,bn,bo,bp,_(bq,br,bs,br),bt,_(y,z,A,bu,bv,bw),x,_(y,z,A,bx),by,bz),P,_(),bA,_(),S,[_(T,bB,V,W,X,null,bC,bd,n,bD,ba,bb,bc,bd,s,_(be,bf,t,bg,bh,_(bi,bj,bk,bl),M,bm,bn,bo,bp,_(bq,br,bs,br),bt,_(y,z,A,bu,bv,bw),x,_(y,z,A,bx),by,bz),P,_(),bA,_())],bE,_(bF,bG),bH,g),_(T,bI,V,bJ,X,bK,n,bL,ba,bL,bc,bd,s,_(bp,_(bq,bM,bs,bM)),P,_(),bA,_(),bN,[_(T,bO,V,W,X,bP,n,bQ,ba,bQ,bc,bd,s,_(be,bR,bh,_(bi,bS,bk,bT),bU,_(bV,_(bt,_(y,z,A,bu,bv,bw))),t,bW,bp,_(bq,bX,bs,bY),M,bZ,bt,_(y,z,A,ca,bv,bw),bn,cb),cc,g,P,_(),bA,_(),cd,ce),_(T,cf,V,W,X,cg,n,Z,ba,Z,bc,bd,s,_(bp,_(bq,ch,bs,ci),bh,_(bi,cj,bk,ck),cl,_(y,z,A,cm),cn,co,t,cp,M,cq,x,_(y,z,A,bx)),P,_(),bA,_(),S,[_(T,cr,V,W,X,null,bC,bd,n,bD,ba,bb,bc,bd,s,_(bp,_(bq,ch,bs,ci),bh,_(bi,cj,bk,ck),cl,_(y,z,A,cm),cn,co,t,cp,M,cq,x,_(y,z,A,bx)),P,_(),bA,_())],Q,_(cs,_(ct,cu,cv,[_(ct,cw,cx,g,cy,[_(cz,cA,ct,cB,cC,_(cD,k,b,cE,cF,bd),cG,cH)])])),cI,bd,bH,g),_(T,cJ,V,W,X,bP,n,bQ,ba,bQ,bc,bd,s,_(be,bR,bh,_(bi,bS,bk,bT),bU,_(bV,_(bt,_(y,z,A,bu,bv,bw))),t,bW,bp,_(bq,bX,bs,cK),M,bZ,bt,_(y,z,A,ca,bv,bw),bn,cb),cc,g,P,_(),bA,_(),cd,cL),_(T,cM,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(be,bR,t,bg,bh,_(bi,cN,bk,cO),M,bZ,bn,cb,bt,_(y,z,A,cP,bv,bw),by,cQ,bp,_(bq,cR,bs,cS)),P,_(),bA,_(),S,[_(T,cT,V,W,X,null,bC,bd,n,bD,ba,bb,bc,bd,s,_(be,bR,t,bg,bh,_(bi,cN,bk,cO),M,bZ,bn,cb,bt,_(y,z,A,cP,bv,bw),by,cQ,bp,_(bq,cR,bs,cS)),P,_(),bA,_())],bE,_(bF,cU),bH,g),_(T,cV,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(be,bR,t,bg,bh,_(bi,cW,bk,cO),M,bZ,bn,cb,bt,_(y,z,A,cP,bv,bw),by,cQ,bp,_(bq,ch,bs,cX)),P,_(),bA,_(),S,[_(T,cY,V,W,X,null,bC,bd,n,bD,ba,bb,bc,bd,s,_(be,bR,t,bg,bh,_(bi,cW,bk,cO),M,bZ,bn,cb,bt,_(y,z,A,cP,bv,bw),by,cQ,bp,_(bq,ch,bs,cX)),P,_(),bA,_())],bE,_(bF,cZ),bH,g)],da,g),_(T,bO,V,W,X,bP,n,bQ,ba,bQ,bc,bd,s,_(be,bR,bh,_(bi,bS,bk,bT),bU,_(bV,_(bt,_(y,z,A,bu,bv,bw))),t,bW,bp,_(bq,bX,bs,bY),M,bZ,bt,_(y,z,A,ca,bv,bw),bn,cb),cc,g,P,_(),bA,_(),cd,ce),_(T,cf,V,W,X,cg,n,Z,ba,Z,bc,bd,s,_(bp,_(bq,ch,bs,ci),bh,_(bi,cj,bk,ck),cl,_(y,z,A,cm),cn,co,t,cp,M,cq,x,_(y,z,A,bx)),P,_(),bA,_(),S,[_(T,cr,V,W,X,null,bC,bd,n,bD,ba,bb,bc,bd,s,_(bp,_(bq,ch,bs,ci),bh,_(bi,cj,bk,ck),cl,_(y,z,A,cm),cn,co,t,cp,M,cq,x,_(y,z,A,bx)),P,_(),bA,_())],Q,_(cs,_(ct,cu,cv,[_(ct,cw,cx,g,cy,[_(cz,cA,ct,cB,cC,_(cD,k,b,cE,cF,bd),cG,cH)])])),cI,bd,bH,g),_(T,cJ,V,W,X,bP,n,bQ,ba,bQ,bc,bd,s,_(be,bR,bh,_(bi,bS,bk,bT),bU,_(bV,_(bt,_(y,z,A,bu,bv,bw))),t,bW,bp,_(bq,bX,bs,cK),M,bZ,bt,_(y,z,A,ca,bv,bw),bn,cb),cc,g,P,_(),bA,_(),cd,cL),_(T,cM,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(be,bR,t,bg,bh,_(bi,cN,bk,cO),M,bZ,bn,cb,bt,_(y,z,A,cP,bv,bw),by,cQ,bp,_(bq,cR,bs,cS)),P,_(),bA,_(),S,[_(T,cT,V,W,X,null,bC,bd,n,bD,ba,bb,bc,bd,s,_(be,bR,t,bg,bh,_(bi,cN,bk,cO),M,bZ,bn,cb,bt,_(y,z,A,cP,bv,bw),by,cQ,bp,_(bq,cR,bs,cS)),P,_(),bA,_())],bE,_(bF,cU),bH,g),_(T,cV,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(be,bR,t,bg,bh,_(bi,cW,bk,cO),M,bZ,bn,cb,bt,_(y,z,A,cP,bv,bw),by,cQ,bp,_(bq,ch,bs,cX)),P,_(),bA,_(),S,[_(T,cY,V,W,X,null,bC,bd,n,bD,ba,bb,bc,bd,s,_(be,bR,t,bg,bh,_(bi,cW,bk,cO),M,bZ,bn,cb,bt,_(y,z,A,cP,bv,bw),by,cQ,bp,_(bq,ch,bs,cX)),P,_(),bA,_())],bE,_(bF,cZ),bH,g),_(T,db,V,dc,X,Y,n,Z,ba,bb,bc,bd,s,_(be,bf,t,bg,bh,_(bi,dd,bk,de),M,bm,bn,df,bp,_(bq,dg,bs,dh)),P,_(),bA,_(),S,[_(T,di,V,W,X,null,bC,bd,n,bD,ba,bb,bc,bd,s,_(be,bf,t,bg,bh,_(bi,dd,bk,de),M,bm,bn,df,bp,_(bq,dg,bs,dh)),P,_(),bA,_())],bE,_(bF,dj),bH,g),_(T,dk,V,W,X,dl,n,Z,ba,dm,bc,bd,s,_(bp,_(bq,dn,bs,dp),bh,_(bi,dq,bk,bw),cl,_(y,z,A,dr),t,ds),P,_(),bA,_(),S,[_(T,dt,V,W,X,null,bC,bd,n,bD,ba,bb,bc,bd,s,_(bp,_(bq,dn,bs,dp),bh,_(bi,dq,bk,bw),cl,_(y,z,A,dr),t,ds),P,_(),bA,_())],bE,_(bF,du),bH,g)])),dv,_(),dw,_(dx,_(dy,dz),dA,_(dy,dB),dC,_(dy,dD),dE,_(dy,dF),dG,_(dy,dH),dI,_(dy,dJ),dK,_(dy,dL),dM,_(dy,dN),dO,_(dy,dP),dQ,_(dy,dR),dS,_(dy,dT),dU,_(dy,dV),dW,_(dy,dX),dY,_(dy,dZ),ea,_(dy,eb)));}; 
var b="url",c="找回密码-输入新密码.html",d="generationDate",e=new Date(1546564660102.41),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="84b971f3ecde480a86efd4d83bfc8a31",n="type",o="Axure:Page",p="name",q="找回密码-输入新密码",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="847e9f9fe9dc4c96af3a24c6bff8c6a8",V="label",W="",X="friendlyType",Y="Paragraph",Z="vectorShape",ba="styleType",bb="paragraph",bc="visible",bd=true,be="fontWeight",bf="500",bg="4988d43d80b44008a4a415096f1632af",bh="size",bi="width",bj=119,bk="height",bl=50,bm="'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC'",bn="fontSize",bo="36px",bp="location",bq="x",br=31,bs="y",bt="foreGroundFill",bu=0xFF999999,bv="opacity",bw=1,bx=0xFFF2F2F2,by="horizontalAlignment",bz="center",bA="imageOverrides",bB="d091bd0ed5a04d3694e725608bf271a3",bC="isContained",bD="richTextPanel",bE="images",bF="normal~",bG="images/登录/u454.png",bH="generateCompound",bI="237272a60af74ccea10f2477fbffab22",bJ="账号登录",bK="Group",bL="layer",bM=0,bN="objs",bO="d97a9c5c7fab45d4a1b9b9e613ab1d84",bP="Text Field",bQ="textBox",bR="200",bS=240,bT=30,bU="stateStyles",bV="hint",bW="44157808f2934100b68f2394a66b2bba",bX=364,bY=214,bZ="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",ca=0xFF666666,cb="12px",cc="HideHintOnFocused",cd="placeholderText",ce="再次输入新密码",cf="6d1a1d01c46748db9ae3bb466f67377b",cg="Rectangle",ch=291,ci=298,cj=313,ck=41,cl="borderFill",cm=0xFFCCCCCC,cn="cornerRadius",co="5",cp="98c916898e844865a527f56bc61a500d",cq="'PingFangSC-Regular', 'PingFang SC'",cr="c5e701164a754ccd87202305f2a4ed03",cs="onClick",ct="description",cu="OnClick",cv="cases",cw="Case 1",cx="isNewIfGroup",cy="actions",cz="action",cA="linkWindow",cB="Open 首页-未创建菜品 in Current Window",cC="target",cD="targetType",cE="首页-未创建菜品.html",cF="includeVariables",cG="linkType",cH="current",cI="tabbable",cJ="9217e7bfc42d4f018ba6d3423ab7e2fb",cK=173,cL="输入6位数字",cM="b5dad52bb904475cbe447999619d6de9",cN=64,cO=17,cP=0xFF1E1E1E,cQ="right",cR=302,cS=180,cT="d1f80ef250ae487799fec722f4abfde1",cU="images/找回密码-输入账号获取验证码/u485.png",cV="6c029a778f954c40b9e283c315b37b3f",cW=75,cX=220,cY="bd98ed3f60114c35870a68e270d2ea28",cZ="images/找回密码-输入账号获取验证码/u487.png",da="propagate",db="9d1b6bdb97f84138b6154edb77f3dfc6",dc="账号密码登录",dd=249,de=20,df="14px",dg=270,dh=128,di="5d535f35cc3242ad872ab570aecb392e",dj="images/找回密码-输入新密码/账号密码登录_u521.png",dk="bee05d27e8e24ce5a83e15493df91500",dl="Horizontal Line",dm="horizontalLine",dn=216,dp=158,dq=794,dr=0xFFE4E4E4,ds="f48196c19ab74fb7b3acb5151ce8ea2d",dt="5733b347d7f94d829aa204dc019fbb81",du="images/找回密码-输入账号获取验证码/u495.png",dv="masters",dw="objectPaths",dx="847e9f9fe9dc4c96af3a24c6bff8c6a8",dy="scriptId",dz="u510",dA="d091bd0ed5a04d3694e725608bf271a3",dB="u511",dC="237272a60af74ccea10f2477fbffab22",dD="u512",dE="d97a9c5c7fab45d4a1b9b9e613ab1d84",dF="u513",dG="6d1a1d01c46748db9ae3bb466f67377b",dH="u514",dI="c5e701164a754ccd87202305f2a4ed03",dJ="u515",dK="9217e7bfc42d4f018ba6d3423ab7e2fb",dL="u516",dM="b5dad52bb904475cbe447999619d6de9",dN="u517",dO="d1f80ef250ae487799fec722f4abfde1",dP="u518",dQ="6c029a778f954c40b9e283c315b37b3f",dR="u519",dS="bd98ed3f60114c35870a68e270d2ea28",dT="u520",dU="9d1b6bdb97f84138b6154edb77f3dfc6",dV="u521",dW="5d535f35cc3242ad872ab570aecb392e",dX="u522",dY="bee05d27e8e24ce5a83e15493df91500",dZ="u523",ea="5733b347d7f94d829aa204dc019fbb81",eb="u524";
return _creator();
})());