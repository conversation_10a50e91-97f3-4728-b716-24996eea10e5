<!DOCTYPE html>
<html>
  <head>
    <title>找回密码-输入新密码</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/找回密码-输入新密码/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/找回密码-输入新密码/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Paragraph) -->
      <div id="u510" class="ax_default paragraph">
        <img id="u510_img" class="img " src="images/登录/u454.png"/>
        <!-- Unnamed () -->
        <div id="u511" class="text" style="visibility: visible;">
          <p><span>LOGO</span></p>
        </div>
      </div>

      <!-- 账号登录 (Group) -->
      <div id="u512" class="ax_default" data-label="账号登录">

        <!-- Unnamed (Text Field) -->
        <div id="u513" class="ax_default text_field">
          <input id="u513_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u514" class="ax_default shape">
          <div id="u514_div" class=""></div>
          <!-- Unnamed () -->
          <div id="u515" class="text" style="visibility: visible;">
            <p><span>保存</span></p>
          </div>
        </div>

        <!-- Unnamed (Text Field) -->
        <div id="u516" class="ax_default text_field">
          <input id="u516_input" type="text" value=""/>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u517" class="ax_default paragraph">
          <img id="u517_img" class="img " src="images/找回密码-输入账号获取验证码/u485.png"/>
          <!-- Unnamed () -->
          <div id="u518" class="text" style="visibility: visible;">
            <p><span>新密码：</span></p>
          </div>
        </div>

        <!-- Unnamed (Paragraph) -->
        <div id="u519" class="ax_default paragraph">
          <img id="u519_img" class="img " src="images/找回密码-输入账号获取验证码/u487.png"/>
          <!-- Unnamed () -->
          <div id="u520" class="text" style="visibility: visible;">
            <p><span>再次确认：</span></p>
          </div>
        </div>
      </div>

      <!-- 账号密码登录 (Paragraph) -->
      <div id="u521" class="ax_default paragraph" data-label="账号密码登录">
        <img id="u521_img" class="img " src="images/找回密码-输入新密码/账号密码登录_u521.png"/>
        <!-- Unnamed () -->
        <div id="u522" class="text" style="visibility: visible;">
          <p><span>已通过验证， 请设置新密码</span></p>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u523" class="ax_default horizontal_line">
        <img id="u523_img" class="img " src="images/找回密码-输入账号获取验证码/u495.png"/>
        <!-- Unnamed () -->
        <div id="u524" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>
    </div>
  </body>
</html>
