body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1200px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u481_img {
  position:absolute;
  left:0px;
  top:0px;
  width:341px;
  height:51px;
}
#u481 {
  position:absolute;
  left:26px;
  top:123px;
  width:341px;
  height:51px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-style:normal;
  font-size:12px;
}
#u482 {
  position:absolute;
  left:0px;
  top:0px;
  width:341px;
  word-wrap:break-word;
}
#u484_div {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:72px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0.498039215686275);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u484 {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:72px;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u485 {
  position:absolute;
  left:2px;
  top:28px;
  width:1196px;
  word-wrap:break-word;
}
#u486_div {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:71px;
  background:inherit;
  background-color:rgba(228, 228, 228, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u486 {
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:71px;
  font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#CCCCCC;
  text-align:left;
}
#u487 {
  position:absolute;
  left:2px;
  top:28px;
  width:1196px;
  visibility:hidden;
  word-wrap:break-word;
}
#u488_div {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
}
#u488 {
  position:absolute;
  left:1066px;
  top:19px;
  width:55px;
  height:17px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
  color:#1E1E1E;
}
#u489 {
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  white-space:nowrap;
}
#u490_div {
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:21px;
  background:inherit;
  background-color:rgba(242, 242, 242, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u490 {
  position:absolute;
  left:1133px;
  top:17px;
  width:54px;
  height:21px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u491 {
  position:absolute;
  left:2px;
  top:2px;
  width:50px;
  white-space:nowrap;
}
#u492_img {
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  height:22px;
}
#u492 {
  position:absolute;
  left:48px;
  top:18px;
  width:126px;
  height:22px;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u493 {
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  word-wrap:break-word;
}
#u494_img {
  position:absolute;
  left:0px;
  top:0px;
  width:1201px;
  height:2px;
}
#u494 {
  position:absolute;
  left:0px;
  top:71px;
  width:1200px;
  height:1px;
}
#u495 {
  position:absolute;
  left:2px;
  top:-8px;
  width:1196px;
  visibility:hidden;
  word-wrap:break-word;
}
#u496 {
  position:absolute;
  left:194px;
  top:11px;
  width:504px;
  height:44px;
}
#u497_img {
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:39px;
}
#u497 {
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u498 {
  position:absolute;
  left:2px;
  top:11px;
  width:46px;
  word-wrap:break-word;
}
#u499_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u499 {
  position:absolute;
  left:50px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u500 {
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u501_img {
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:39px;
}
#u501 {
  position:absolute;
  left:130px;
  top:0px;
  width:60px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u502 {
  position:absolute;
  left:2px;
  top:11px;
  width:56px;
  word-wrap:break-word;
}
#u503_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u503 {
  position:absolute;
  left:190px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u504 {
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u505_img {
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:39px;
}
#u505 {
  position:absolute;
  left:270px;
  top:0px;
  width:74px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u506 {
  position:absolute;
  left:2px;
  top:11px;
  width:70px;
  word-wrap:break-word;
}
#u507_img {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:39px;
}
#u507 {
  position:absolute;
  left:344px;
  top:0px;
  width:80px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u508 {
  position:absolute;
  left:2px;
  top:11px;
  width:76px;
  word-wrap:break-word;
}
#u509_img {
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:39px;
}
#u509 {
  position:absolute;
  left:424px;
  top:0px;
  width:75px;
  height:39px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u510 {
  position:absolute;
  left:2px;
  top:11px;
  width:71px;
  word-wrap:break-word;
}
#u511_div {
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u511 {
  position:absolute;
  left:11px;
  top:12px;
  width:34px;
  height:34px;
}
#u512 {
  position:absolute;
  left:2px;
  top:9px;
  width:30px;
  visibility:hidden;
  word-wrap:break-word;
}
#u513 {
  position:absolute;
  left:195px;
  top:7px;
  width:53px;
  height:45px;
}
#u514_img {
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:40px;
}
#u514 {
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:40px;
  font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:12px;
}
#u515 {
  position:absolute;
  left:2px;
  top:12px;
  width:44px;
  visibility:hidden;
  word-wrap:break-word;
}
