$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(t,be,bf,_(bg,bh,bi,bj),bk,_(bl,bm,bn,bo),M,bp,bq,br),P,_(),bs,_(),S,[_(T,bt,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(t,be,bf,_(bg,bh,bi,bj),bk,_(bl,bm,bn,bo),M,bp,bq,br),P,_(),bs,_())],bw,_(bx,by),bz,g),_(T,bA,V,W,X,bB,n,bC,ba,bC,bc,bd,s,_(bf,_(bg,bD,bi,bE)),P,_(),bs,_(),bF,bG),_(T,bH,V,bI,X,bJ,n,bK,ba,bK,bc,bd,s,_(bf,_(bg,bL,bi,bM),bk,_(bl,bN,bn,bO)),P,_(),bs,_(),S,[_(T,bP,V,W,X,bQ,n,bR,ba,bR,bc,bd,s,_(bS,bT,bf,_(bg,bL,bi,bM),t,bU,M,bp,bq,br,x,_(y,z,A,bV),bW,_(y,z,A,bX),O,J),P,_(),bs,_(),S,[_(T,bY,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,bT,bf,_(bg,bL,bi,bM),t,bU,M,bp,bq,br,x,_(y,z,A,bV),bW,_(y,z,A,bX),O,J),P,_(),bs,_())],bw,_(bx,bZ))])])),ca,_(cb,_(l,cb,n,cc,p,bB,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,cd,V,W,X,ce,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,bD,bi,bE),t,cf,cg,ch,ci,_(y,z,A,cj,ck,cl),bq,cm,bW,_(y,z,A,B),x,_(y,z,A,cn)),P,_(),bs,_(),S,[_(T,co,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bf,_(bg,bD,bi,bE),t,cf,cg,ch,ci,_(y,z,A,cj,ck,cl),bq,cm,bW,_(y,z,A,B),x,_(y,z,A,cn)),P,_(),bs,_())],bz,g),_(T,cp,V,W,X,ce,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,bD,bi,cq),t,cf,cg,ch,M,cr,ci,_(y,z,A,cj,ck,cl),bq,cm,bW,_(y,z,A,cs),x,_(y,z,A,bX)),P,_(),bs,_(),S,[_(T,ct,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bf,_(bg,bD,bi,cq),t,cf,cg,ch,M,cr,ci,_(y,z,A,cj,ck,cl),bq,cm,bW,_(y,z,A,cs),x,_(y,z,A,bX)),P,_(),bs,_())],bz,g),_(T,cu,V,W,X,ce,n,Z,ba,Z,bc,bd,s,_(bS,bT,bf,_(bg,cv,bi,cw),t,be,bk,_(bl,cx,bn,cy),bq,br,ci,_(y,z,A,cz,ck,cl),M,bp),P,_(),bs,_(),S,[_(T,cA,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,bT,bf,_(bg,cv,bi,cw),t,be,bk,_(bl,cx,bn,cy),bq,br,ci,_(y,z,A,cz,ck,cl),M,bp),P,_(),bs,_())],Q,_(cB,_(cC,cD,cE,[_(cC,cF,cG,g,cH,[])])),cI,bd,bz,g),_(T,cJ,V,W,X,ce,n,Z,ba,Z,bc,bd,s,_(bS,bT,bf,_(bg,cK,bi,cL),t,bU,bk,_(bl,cM,bn,cw),bq,br,M,bp,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J),P,_(),bs,_(),S,[_(T,cO,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,bT,bf,_(bg,cK,bi,cL),t,bU,bk,_(bl,cM,bn,cw),bq,br,M,bp,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J),P,_(),bs,_())],Q,_(cB,_(cC,cD,cE,[_(cC,cF,cG,g,cH,[_(cP,cQ,cC,cR,cS,_(cT,k,cU,bd),cV,cW)])])),cI,bd,bz,g),_(T,cX,V,W,X,Y,n,Z,ba,bb,bc,bd,s,_(bS,cY,t,be,bf,_(bg,cZ,bi,da),bk,_(bl,bL,bn,db),M,dc,bq,dd,ci,_(y,z,A,de,ck,cl)),P,_(),bs,_(),S,[_(T,df,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,cY,t,be,bf,_(bg,cZ,bi,da),bk,_(bl,bL,bn,db),M,dc,bq,dd,ci,_(y,z,A,de,ck,cl)),P,_(),bs,_())],bw,_(bx,dg),bz,g),_(T,dh,V,W,X,di,n,Z,ba,dj,bc,bd,s,_(bk,_(bl,dk,bn,cq),bf,_(bg,bD,bi,cl),bW,_(y,z,A,cj),t,dl),P,_(),bs,_(),S,[_(T,dm,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bk,_(bl,dk,bn,cq),bf,_(bg,bD,bi,cl),bW,_(y,z,A,cj),t,dl),P,_(),bs,_())],bw,_(bx,dn),bz,g),_(T,dp,V,W,X,bJ,n,bK,ba,bK,bc,bd,s,_(bf,_(bg,dq,bi,dr),bk,_(bl,ds,bn,dt)),P,_(),bs,_(),S,[_(T,du,V,W,X,bQ,n,bR,ba,bR,bc,bd,s,_(bS,bT,bf,_(bg,dv,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dw,bn,dk)),P,_(),bs,_(),S,[_(T,dx,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,bT,bf,_(bg,dv,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dw,bn,dk)),P,_(),bs,_())],Q,_(cB,_(cC,cD,cE,[_(cC,cF,cG,g,cH,[_(cP,cQ,cC,dy,cS,_(cT,k,b,dz,cU,bd),cV,cW)])])),cI,bd,bw,_(bx,dA)),_(T,dB,V,W,X,bQ,n,bR,ba,bR,bc,bd,s,_(bS,bT,bf,_(bg,dC,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dD,bn,dk)),P,_(),bs,_(),S,[_(T,dE,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,bT,bf,_(bg,dC,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dD,bn,dk)),P,_(),bs,_())],Q,_(cB,_(cC,cD,cE,[_(cC,cF,cG,g,cH,[_(cP,cQ,cC,cR,cS,_(cT,k,cU,bd),cV,cW)])])),cI,bd,bw,_(bx,dA)),_(T,dF,V,W,X,bQ,n,bR,ba,bR,bc,bd,s,_(bS,bT,bf,_(bg,dv,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dG,bn,dk)),P,_(),bs,_(),S,[_(T,dH,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,bT,bf,_(bg,dv,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dG,bn,dk)),P,_(),bs,_())],Q,_(cB,_(cC,cD,cE,[_(cC,cF,cG,g,cH,[_(cP,cQ,cC,cR,cS,_(cT,k,cU,bd),cV,cW)])])),cI,bd,bw,_(bx,dA)),_(T,dI,V,W,X,bQ,n,bR,ba,bR,bc,bd,s,_(bS,bT,bf,_(bg,dJ,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dK,bn,dk)),P,_(),bs,_(),S,[_(T,dL,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,bT,bf,_(bg,dJ,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dK,bn,dk)),P,_(),bs,_())],bw,_(bx,dA)),_(T,dM,V,W,X,bQ,n,bR,ba,bR,bc,bd,s,_(bS,bT,bf,_(bg,dN,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dO,bn,dk)),P,_(),bs,_(),S,[_(T,dP,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,bT,bf,_(bg,dN,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dO,bn,dk)),P,_(),bs,_())],Q,_(cB,_(cC,cD,cE,[_(cC,cF,cG,g,cH,[_(cP,cQ,cC,cR,cS,_(cT,k,cU,bd),cV,cW)])])),cI,bd,bw,_(bx,dA)),_(T,dQ,V,W,X,bQ,n,bR,ba,bR,bc,bd,s,_(bS,bT,bf,_(bg,dv,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dR,bn,dk)),P,_(),bs,_(),S,[_(T,dS,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,bT,bf,_(bg,dv,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dR,bn,dk)),P,_(),bs,_())],Q,_(cB,_(cC,cD,cE,[_(cC,cF,cG,g,cH,[_(cP,cQ,cC,dT,cS,_(cT,k,b,dU,cU,bd),cV,cW)])])),cI,bd,bw,_(bx,dA)),_(T,dV,V,W,X,bQ,n,bR,ba,bR,bc,bd,s,_(bS,bT,bf,_(bg,dw,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dk,bn,dk)),P,_(),bs,_(),S,[_(T,dW,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bS,bT,bf,_(bg,dw,bi,dr),t,bU,M,bp,bq,br,x,_(y,z,A,cN),bW,_(y,z,A,bX),O,J,bk,_(bl,dk,bn,dk)),P,_(),bs,_())],Q,_(cB,_(cC,cD,cE,[_(cC,cF,cG,g,cH,[_(cP,cQ,cC,dX,cS,_(cT,k,b,dY,cU,bd),cV,cW)])])),cI,bd,bw,_(bx,dA))]),_(T,dZ,V,W,X,ce,n,Z,ba,Z,bc,bd,s,_(bf,_(bg,ea,bi,ea),t,eb,bk,_(bl,dt,bn,ec)),P,_(),bs,_(),S,[_(T,ed,V,W,X,null,bu,bd,n,bv,ba,bb,bc,bd,s,_(bf,_(bg,ea,bi,ea),t,eb,bk,_(bl,dt,bn,ec)),P,_(),bs,_())],bz,g)]))),ee,_(ef,_(eg,eh),ei,_(eg,ej),ek,_(eg,el,em,_(eg,en),eo,_(eg,ep),eq,_(eg,er),es,_(eg,et),eu,_(eg,ev),ew,_(eg,ex),ey,_(eg,ez),eA,_(eg,eB),eC,_(eg,eD),eE,_(eg,eF),eG,_(eg,eH),eI,_(eg,eJ),eK,_(eg,eL),eM,_(eg,eN),eO,_(eg,eP),eQ,_(eg,eR),eS,_(eg,eT),eU,_(eg,eV),eW,_(eg,eX),eY,_(eg,eZ),fa,_(eg,fb),fc,_(eg,fd),fe,_(eg,ff),fg,_(eg,fh),fi,_(eg,fj),fk,_(eg,fl),fm,_(eg,fn),fo,_(eg,fp),fq,_(eg,fr)),fs,_(eg,ft),fu,_(eg,fv),fw,_(eg,fx)));}; 
var b="url",c="首页-有商品无营业数据.html",d="generationDate",e=new Date(1545358768332.01),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="13fab4802ace421299b10980327f71ce",n="type",o="Axure:Page",p="name",q="首页-有商品无营业数据",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="45871e59176b457d9734c70c1022b10d",V="label",W="",X="friendlyType",Y="Paragraph",Z="vectorShape",ba="styleType",bb="paragraph",bc="visible",bd=true,be="4988d43d80b44008a4a415096f1632af",bf="size",bg="width",bh=341,bi="height",bj=51,bk="location",bl="x",bm=26,bn="y",bo=123,bp="'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC'",bq="fontSize",br="12px",bs="imageOverrides",bt="21d7b99faa134d70904fb2c652a7feba",bu="isContained",bv="richTextPanel",bw="images",bx="normal~",by="images/首页-有商品无营业数据/u481.png",bz="generateCompound",bA="54631fbd89dd4b7384dfbeb77a0a7867",bB="主框架",bC="referenceDiagramObject",bD=1200,bE=72,bF="masterId",bG="42b294620c2d49c7af5b1798469a7eae",bH="264b163f408c43f8aa34a625e8a446be",bI="门店及员工",bJ="Table",bK="table",bL=48,bM=40,bN=195,bO=7,bP="68ad03824a4347b49fdc89ca298c5a5a",bQ="Table Cell",bR="tableCell",bS="fontWeight",bT="200",bU="33ea2511485c479dbf973af3302f2352",bV=0xC0000FF,bW="borderFill",bX=0xFFE4E4E4,bY="b10c272ea11243a39c8fab974896a0b6",bZ="images/首页-未创建菜品/u479.png",ca="masters",cb="42b294620c2d49c7af5b1798469a7eae",cc="Axure:Master",cd="964c4380226c435fac76d82007637791",ce="Rectangle",cf="0882bfcd7d11450d85d157758311dca5",cg="horizontalAlignment",ch="left",ci="foreGroundFill",cj=0xFFCCCCCC,ck="opacity",cl=1,cm="14px",cn=0x7FF2F2F2,co="f0e6d8a5be734a0daeab12e0ad1745e8",cp="1e3bb79c77364130b7ce098d1c3a6667",cq=71,cr="'ArialRoundedMTBold', 'Arial Rounded MT Bold'",cs=0xFF666666,ct="136ce6e721b9428c8d7a12533d585265",cu="d6b97775354a4bc39364a6d5ab27a0f3",cv=55,cw=17,cx=1066,cy=19,cz=0xFF1E1E1E,cA="529afe58e4dc499694f5761ad7a21ee3",cB="onClick",cC="description",cD="OnClick",cE="cases",cF="Case 1",cG="isNewIfGroup",cH="actions",cI="tabbable",cJ="935c51cfa24d4fb3b10579d19575f977",cK=54,cL=21,cM=1133,cN=0xF2F2F2,cO="099c30624b42452fa3217e4342c93502",cP="action",cQ="linkWindow",cR="Open Link in Current Window",cS="target",cT="targetType",cU="includeVariables",cV="linkType",cW="current",cX="f2df399f426a4c0eb54c2c26b150d28c",cY="500",cZ=126,da=22,db=18,dc="'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC'",dd="16px",de=0xFF999999,df="649cae71611a4c7785ae5cbebc3e7bca",dg="images/首页-未创建菜品/u457.png",dh="e7b01238e07e447e847ff3b0d615464d",di="Horizontal Line",dj="horizontalLine",dk=0,dl="f48196c19ab74fb7b3acb5151ce8ea2d",dm="d3a4cb92122f441391bc879f5fee4a36",dn="images/首页-未创建菜品/u459.png",dp="ed086362cda14ff890b2e717f817b7bb",dq=499,dr=39,ds=194,dt=11,du="c2345ff754764c5694b9d57abadd752c",dv=80,dw=50,dx="25e2a2b7358d443dbebd012dc7ed75dd",dy="Open 员工列表 in Current Window",dz="员工列表.html",dA="resources/images/transparent.gif",dB="d9bb22ac531d412798fee0e18a9dfaa8",dC=60,dD=130,dE="bf1394b182d94afd91a21f3436401771",dF="2aefc4c3d8894e52aa3df4fbbfacebc3",dG=344,dH="099f184cab5e442184c22d5dd1b68606",dI="79eed072de834103a429f51c386cddfd",dJ=74,dK=270,dL="dd9a354120ae466bb21d8933a7357fd8",dM="9d46b8ed273c4704855160ba7c2c2f8e",dN=75,dO=424,dP="e2a2baf1e6bb4216af19b1b5616e33e1",dQ="89cf184dc4de41d09643d2c278a6f0b7",dR=190,dS="903b1ae3f6664ccabc0e8ba890380e4b",dT="Open 商品列表 in Current Window",dU="商品列表.html",dV="8c26f56a3753450dbbef8d6cfde13d67",dW="fbdda6d0b0094103a3f2692a764d333a",dX="Open 首页-营业数据 in Current Window",dY="首页-营业数据.html",dZ="d53c7cd42bee481283045fd015fd50d5",ea=34,eb="47641f9a00ac465095d6b672bbdffef6",ec=12,ed="abdf932a631e417992ae4dba96097eda",ee="objectPaths",ef="45871e59176b457d9734c70c1022b10d",eg="scriptId",eh="u481",ei="21d7b99faa134d70904fb2c652a7feba",ej="u482",ek="54631fbd89dd4b7384dfbeb77a0a7867",el="u483",em="964c4380226c435fac76d82007637791",en="u484",eo="f0e6d8a5be734a0daeab12e0ad1745e8",ep="u485",eq="1e3bb79c77364130b7ce098d1c3a6667",er="u486",es="136ce6e721b9428c8d7a12533d585265",et="u487",eu="d6b97775354a4bc39364a6d5ab27a0f3",ev="u488",ew="529afe58e4dc499694f5761ad7a21ee3",ex="u489",ey="935c51cfa24d4fb3b10579d19575f977",ez="u490",eA="099c30624b42452fa3217e4342c93502",eB="u491",eC="f2df399f426a4c0eb54c2c26b150d28c",eD="u492",eE="649cae71611a4c7785ae5cbebc3e7bca",eF="u493",eG="e7b01238e07e447e847ff3b0d615464d",eH="u494",eI="d3a4cb92122f441391bc879f5fee4a36",eJ="u495",eK="ed086362cda14ff890b2e717f817b7bb",eL="u496",eM="8c26f56a3753450dbbef8d6cfde13d67",eN="u497",eO="fbdda6d0b0094103a3f2692a764d333a",eP="u498",eQ="c2345ff754764c5694b9d57abadd752c",eR="u499",eS="25e2a2b7358d443dbebd012dc7ed75dd",eT="u500",eU="d9bb22ac531d412798fee0e18a9dfaa8",eV="u501",eW="bf1394b182d94afd91a21f3436401771",eX="u502",eY="89cf184dc4de41d09643d2c278a6f0b7",eZ="u503",fa="903b1ae3f6664ccabc0e8ba890380e4b",fb="u504",fc="79eed072de834103a429f51c386cddfd",fd="u505",fe="dd9a354120ae466bb21d8933a7357fd8",ff="u506",fg="2aefc4c3d8894e52aa3df4fbbfacebc3",fh="u507",fi="099f184cab5e442184c22d5dd1b68606",fj="u508",fk="9d46b8ed273c4704855160ba7c2c2f8e",fl="u509",fm="e2a2baf1e6bb4216af19b1b5616e33e1",fn="u510",fo="d53c7cd42bee481283045fd015fd50d5",fp="u511",fq="abdf932a631e417992ae4dba96097eda",fr="u512",fs="264b163f408c43f8aa34a625e8a446be",ft="u513",fu="68ad03824a4347b49fdc89ca298c5a5a",fv="u514",fw="b10c272ea11243a39c8fab974896a0b6",fx="u515";
return _creator();
})());