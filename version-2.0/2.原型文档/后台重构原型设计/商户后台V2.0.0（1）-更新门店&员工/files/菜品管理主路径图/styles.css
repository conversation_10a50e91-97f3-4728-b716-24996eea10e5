body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1439px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u391_img {
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:16px;
}
#u391 {
  position:absolute;
  left:15px;
  top:13px;
  width:132px;
  height:16px;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FF00FF;
}
#u392 {
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  white-space:nowrap;
}
#u393_img {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  height:16px;
}
#u393 {
  position:absolute;
  left:15px;
  top:1295px;
  width:164px;
  height:16px;
  font-family:'.AppleSystemUIFont';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FF00FF;
}
#u394 {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  white-space:nowrap;
}
#u395_img {
  position:absolute;
  left:0px;
  top:0px;
  width:1412px;
  height:72px;
}
#u395 {
  position:absolute;
  left:27px;
  top:1327px;
  width:1412px;
  height:72px;
}
#u396 {
  position:absolute;
  left:2px;
  top:28px;
  width:1408px;
  visibility:hidden;
  word-wrap:break-word;
}
#u397_img {
  position:absolute;
  left:0px;
  top:0px;
  width:1093px;
  height:1031px;
}
#u397 {
  position:absolute;
  left:15px;
  top:49px;
  width:1093px;
  height:1031px;
}
#u398 {
  position:absolute;
  left:2px;
  top:508px;
  width:1089px;
  visibility:hidden;
  word-wrap:break-word;
}
