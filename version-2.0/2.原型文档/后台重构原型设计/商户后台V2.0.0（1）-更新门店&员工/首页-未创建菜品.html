<!DOCTYPE html>
<html>
  <head>
    <title>首页-未创建菜品</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/首页-未创建菜品/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/首页-未创建菜品/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Paragraph) -->
      <div id="u442" class="ax_default paragraph">
        <img id="u442_img" class="img " src="images/首页-未创建菜品/u442.png"/>
        <!-- Unnamed () -->
        <div id="u443" class="text" style="visibility: visible;">
          <p style="font-size:12px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC';font-weight:400;">欢迎使用掌控者智慧门店服务。请先配置以下基础业务数据：</span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC';font-weight:400;"><br></span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">1.门店数据(如需同时把商品添加到多个门店） </span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#0000FF;">去配置</span></p><p style="font-size:6px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;"><br></span></p><p style="font-size:12px;"><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;">2.商品数据 </span><span style="font-family:'PingFangSC-Thin', 'PingFang SC Thin', 'PingFang SC';font-weight:200;color:#0000FF;">去配置</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u444" class="ax_default box_1">
        <div id="u444_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u445" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u446" class="ax_default box_1">
        <div id="u446_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u447" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (主框架) -->

      <!-- Unnamed (Rectangle) -->
      <div id="u449" class="ax_default box_3">
        <div id="u449_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u450" class="text" style="visibility: visible;">
          <p><span style="font-family:'ArialMT', 'Arial';">&nbsp;&nbsp; </span><span style="font-family:'ArialRoundedMTBold', 'Arial Rounded MT Bold';">Holder </span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u451" class="ax_default box_3">
        <div id="u451_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u452" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u453" class="ax_default paragraph">
        <div id="u453_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u454" class="text" style="visibility: visible;">
          <p><span>账号/员姓</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u455" class="ax_default table_cell">
        <div id="u455_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u456" class="text" style="visibility: visible;">
          <p><span>消息 </span><span style="color:#FF0000;">99+</span></p>
        </div>
      </div>

      <!-- Unnamed (Paragraph) -->
      <div id="u457" class="ax_default paragraph">
        <img id="u457_img" class="img " src="images/首页-未创建菜品/u457.png"/>
        <!-- Unnamed () -->
        <div id="u458" class="text" style="visibility: visible;">
          <p><span>门店管理后台</span></p>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u459" class="ax_default horizontal_line">
        <img id="u459_img" class="img " src="images/首页-未创建菜品/u459.png"/>
        <!-- Unnamed () -->
        <div id="u460" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u461" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u462" class="ax_default table_cell">
          <img id="u462_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u463" class="text" style="visibility: visible;">
            <p><span>首页</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u464" class="ax_default table_cell">
          <img id="u464_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u465" class="text" style="visibility: visible;">
            <p><span>门店及员工</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u466" class="ax_default table_cell">
          <img id="u466_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u467" class="text" style="visibility: visible;">
            <p><span>顾客管理</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u468" class="ax_default table_cell">
          <img id="u468_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u469" class="text" style="visibility: visible;">
            <p><span>商品及销售</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u470" class="ax_default table_cell">
          <img id="u470_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u471" class="text" style="visibility: visible;">
            <p><span>活动营销</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u472" class="ax_default table_cell">
          <img id="u472_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u473" class="text" style="visibility: visible;">
            <p><span>报表中心</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u474" class="ax_default table_cell">
          <img id="u474_img" class="img " src="resources/images/transparent.gif"/>
          <!-- Unnamed () -->
          <div id="u475" class="text" style="visibility: visible;">
            <p><span>更多配置</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u476" class="ax_default box_2">
        <div id="u476_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u477" class="text" style="display: none; visibility: hidden">
          <p><span></span></p>
        </div>
      </div>

      <!-- 门店及员工 (Table) -->
      <div id="u478" class="ax_default" data-label="门店及员工">

        <!-- Unnamed (Table Cell) -->
        <div id="u479" class="ax_default table_cell">
          <img id="u479_img" class="img " src="images/首页-未创建菜品/u479.png"/>
          <!-- Unnamed () -->
          <div id="u480" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
