<!DOCTYPE html>
<html>
  <head>
    <title>整体设计说明</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/整体设计说明/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/整体设计说明/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Rectangle) -->
      <div id="u0" class="ax_default label">
        <div id="u0_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u1" class="text" style="visibility: visible;">
          <p><span>数据分页标准&nbsp; 20条/页</span></p><p><span><br></span></p><p><span>编辑/新建页面通用：新建时，初始化基本编辑输入区域；再次编辑时，输入区域为已保存的内容</span></p><p><span><br></span></p><p><span>操作项：</span><span style="color:#0000FF;">蓝色字体</span></p><p><span style="color:#0000FF;"><br></span></p><p><span>列表数据默认按创建改时间倒序排列；列表内容默认1行展示，溢出时截断，鼠标放入，弹框显示</span><span style="color:#0000FF;"></span></p><p><span style="color:#0000FF;"><br></span></p><p><span style="color:#1E1E1E;">通用提示： </span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u2" class="ax_default text_field">
        <input id="u2_input" type="text" value="输入框"/>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3" class="ax_default shape">
        <div id="u3_div" class=""></div>
        <!-- Unnamed () -->
        <div id="u4" class="text" style="visibility: visible;">
          <p><span>操作按钮</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u5" class="ax_default text_field">
        <select id="u5_input">
          <option value="各类选择控件">各类选择控件</option>
        </select>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u6" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u7" class="ax_default label">
          <img id="u7_img" class="img " src="images/整体设计说明/u7.png"/>
          <!-- Unnamed () -->
          <div id="u8" class="text" style="visibility: visible;">
            <p><span>类型</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u9" class="ax_default label">
          <img id="u9_img" class="img " src="images/整体设计说明/u9.png"/>
          <!-- Unnamed () -->
          <div id="u10" class="text" style="visibility: visible;">
            <p><span>操作交互</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u11" class="ax_default label">
          <img id="u11_img" class="img " src="images/整体设计说明/u11.png"/>
          <!-- Unnamed () -->
          <div id="u12" class="text" style="visibility: visible;">
            <p><span>提示语</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u13" class="ax_default label">
          <img id="u13_img" class="img " src="images/整体设计说明/u13.png"/>
          <!-- Unnamed () -->
          <div id="u14" class="text" style="visibility: visible;">
            <p><span>按钮</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u15" class="ax_default label">
          <img id="u15_img" class="img " src="images/整体设计说明/u15.png"/>
          <!-- Unnamed () -->
          <div id="u16" class="text" style="visibility: visible;">
            <p><span>网络错误</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17" class="ax_default label">
          <img id="u17_img" class="img " src="images/整体设计说明/u17.png"/>
          <!-- Unnamed () -->
          <div id="u18" class="text" style="visibility: visible;">
            <p><span>2s弹框,隐藏时，停在当前页</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u19" class="ax_default label">
          <img id="u19_img" class="img " src="images/整体设计说明/u19.png"/>
          <!-- Unnamed () -->
          <div id="u20" class="text" style="visibility: visible;">
            <p><span>网络故障，请稍后使用</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u21" class="ax_default label">
          <img id="u21_img" class="img " src="images/整体设计说明/u21.png"/>
          <!-- Unnamed () -->
          <div id="u22" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u23" class="ax_default label">
          <img id="u23_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u24" class="text" style="visibility: visible;">
            <p><span>离开正在编辑的页面</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u25" class="ax_default label">
          <img id="u25_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u26" class="text" style="visibility: visible;">
            <p><span>对话框</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u27" class="ax_default label">
          <img id="u27_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u28" class="text" style="visibility: visible;">
            <p><span>返回后，正在创建的信息将丢失</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u29" class="ax_default label">
          <img id="u29_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u30" class="text" style="visibility: visible;">
            <p><span>离开&nbsp; 取消&nbsp; </span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u31" class="ax_default label">
          <img id="u31_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u32" class="text" style="visibility: visible;">
            <p><span>保存正在编辑的内容成功</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u33" class="ax_default label">
          <img id="u33_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u34" class="text" style="visibility: visible;">
            <p><span>2s弹框；隐藏时，非跳转停当页，跳转型进行跳转</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u35" class="ax_default label">
          <img id="u35_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u36" class="text" style="visibility: visible;">
            <p><span>保存成功</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u37" class="ax_default label">
          <img id="u37_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u38" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u39" class="ax_default label">
          <img id="u39_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u40" class="text" style="visibility: visible;">
            <p><span>保存正在编辑的内容失败</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u41" class="ax_default label">
          <img id="u41_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u42" class="text" style="visibility: visible;">
            <p><span>2s弹框；隐藏时，停在当前页，并保留内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u43" class="ax_default label">
          <img id="u43_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u44" class="text" style="visibility: visible;">
            <p><span>保存失败，请重试</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u45" class="ax_default label">
          <img id="u45_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u46" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u47" class="ax_default label">
          <img id="u47_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u48" class="text" style="visibility: visible;">
            <p><span>重复输入唯一型的内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u49" class="ax_default label">
          <img id="u49_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u50" class="text" style="visibility: visible;">
            <p><span>2s弹框；隐藏时，停在当前页，并保留内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u51" class="ax_default label">
          <img id="u51_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u52" class="text" style="visibility: visible;">
            <p><span style="color:#1E1E1E;">[</span><span style="color:#0000FF;">输入项名称</span><span style="color:#1E1E1E;">]已存在，请修改</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u53" class="ax_default label">
          <img id="u53_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u54" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u55" class="ax_default label">
          <img id="u55_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u56" class="text" style="visibility: visible;">
            <p><span>输入超出长度限制的内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u57" class="ax_default label">
          <img id="u57_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u58" class="text" style="visibility: visible;">
            <p><span>当输入内容到达限制长度时，不可继续输入</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u59" class="ax_default label">
          <img id="u59_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u60" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u61" class="ax_default label">
          <img id="u61_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u62" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u63" class="ax_default label">
          <img id="u63_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u64" class="text" style="visibility: visible;">
            <p><span>输入不合字符类型的内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u65" class="ax_default label">
          <img id="u65_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u66" class="text" style="visibility: visible;">
            <p><span>禁止输入</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u67" class="ax_default label">
          <img id="u67_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u68" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u69" class="ax_default label">
          <img id="u69_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u70" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u71" class="ax_default label">
          <img id="u71_img" class="img " src="images/整体设计说明/u71.png"/>
          <!-- Unnamed () -->
          <div id="u72" class="text" style="visibility: visible;">
            <p><span>输入前后不一致的内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u73" class="ax_default label">
          <img id="u73_img" class="img " src="images/整体设计说明/u73.png"/>
          <!-- Unnamed () -->
          <div id="u74" class="text" style="visibility: visible;">
            <p><span>切换输入位置时，2s弹框；隐藏时，停在当前页，并保留内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u75" class="ax_default label">
          <img id="u75_img" class="img " src="images/整体设计说明/u75.png"/>
          <!-- Unnamed () -->
          <div id="u76" class="text" style="visibility: visible;">
            <p><span style="color:#1E1E1E;">[</span><span style="color:#0000FF;">被检测的输入项</span><span style="color:#1E1E1E;">]与[</span><span style="color:#0000FF;">原输入项名称</span><span style="color:#1E1E1E;">]不一致，请检查</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u77" class="ax_default label">
          <img id="u77_img" class="img " src="images/整体设计说明/u77.png"/>
          <!-- Unnamed () -->
          <div id="u78" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u79" class="ax_default label">
          <img id="u79_img" class="img " src="images/整体设计说明/u71.png"/>
          <!-- Unnamed () -->
          <div id="u80" class="text" style="visibility: visible;">
            <p><span>删除（可删除的内容）</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u81" class="ax_default label">
          <img id="u81_img" class="img " src="images/整体设计说明/u73.png"/>
          <!-- Unnamed () -->
          <div id="u82" class="text" style="visibility: visible;">
            <p><span>对话框二次确认</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u83" class="ax_default label">
          <img id="u83_img" class="img " src="images/整体设计说明/u75.png"/>
          <!-- Unnamed () -->
          <div id="u84" class="text" style="visibility: visible;">
            <p><span>正在删除“内容项”，请确认</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u85" class="ax_default label">
          <img id="u85_img" class="img " src="images/整体设计说明/u77.png"/>
          <!-- Unnamed () -->
          <div id="u86" class="text" style="visibility: visible;">
            <p><span>删除 取消</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u87" class="ax_default label">
          <img id="u87_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u88" class="text" style="visibility: visible;">
            <p><span>删除成功</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u89" class="ax_default label">
          <img id="u89_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u90" class="text" style="visibility: visible;">
            <p><span>2s弹框,隐藏时,刷新内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u91" class="ax_default label">
          <img id="u91_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u92" class="text" style="visibility: visible;">
            <p><span>已删除</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u93" class="ax_default label">
          <img id="u93_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u94" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u95" class="ax_default label">
          <img id="u95_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u96" class="text" style="visibility: visible;">
            <p><span>删除（不可删除的内容）</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u97" class="ax_default label">
          <img id="u97_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u98" class="text" style="visibility: visible;">
            <p><span>2s弹框,隐藏时，停在当前页</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u99" class="ax_default label">
          <img id="u99_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u100" class="text" style="visibility: visible;">
            <p><span>[相关项]正在使用，不可删除</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u101" class="ax_default label">
          <img id="u101_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u102" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u103" class="ax_default label">
          <img id="u103_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u104" class="text" style="visibility: visible;">
            <p><span>查询返回空</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u105" class="ax_default label">
          <img id="u105_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u106" class="text" style="visibility: visible;">
            <p><span>2s弹框,隐藏时，停在当前页</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u107" class="ax_default label">
          <img id="u107_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u108" class="text" style="visibility: visible;">
            <p><span>查询结果无内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u109" class="ax_default label">
          <img id="u109_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u110" class="text" style="visibility: visible;">
            <p><span>-</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u111" class="ax_default label">
          <img id="u111_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u112" class="text" style="visibility: visible;">
            <p><span>账号密码错误</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u113" class="ax_default label">
          <img id="u113_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u114" class="text" style="visibility: visible;">
            <p><span>2s弹框,隐藏时，停在当前页</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u115" class="ax_default label">
          <img id="u115_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u116" class="text" style="visibility: visible;">
            <p><span>用户名或密码错误</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u117" class="ax_default label">
          <img id="u117_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u118" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u119" class="ax_default label">
          <img id="u119_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u120" class="text" style="visibility: visible;">
            <p><span>账号禁用，不能登录</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u121" class="ax_default label">
          <img id="u121_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u122" class="text" style="visibility: visible;">
            <p><span>2s弹框,隐藏时，停在当前页</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u123" class="ax_default label">
          <img id="u123_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u124" class="text" style="visibility: visible;">
            <p><span>账号已禁用，无法登陆</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u125" class="ax_default label">
          <img id="u125_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u126" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u127" class="ax_default label">
          <img id="u127_img" class="img " src="images/整体设计说明/u23.png"/>
          <!-- Unnamed () -->
          <div id="u128" class="text" style="visibility: visible;">
            <p><span>获取验证码时，手机号未注册</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u129" class="ax_default label">
          <img id="u129_img" class="img " src="images/整体设计说明/u25.png"/>
          <!-- Unnamed () -->
          <div id="u130" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u131" class="ax_default label">
          <img id="u131_img" class="img " src="images/整体设计说明/u27.png"/>
          <!-- Unnamed () -->
          <div id="u132" class="text" style="visibility: visible;">
            <p><span>未注册手机号不能使用。开通服务请联系客服。</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u133" class="ax_default label">
          <img id="u133_img" class="img " src="images/整体设计说明/u29.png"/>
          <!-- Unnamed () -->
          <div id="u134" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u135" class="ax_default label">
          <img id="u135_img" class="img " src="images/整体设计说明/u135.png"/>
          <!-- Unnamed () -->
          <div id="u136" class="text" style="visibility: visible;">
            <p><span>短信验证码错误</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u137" class="ax_default label">
          <img id="u137_img" class="img " src="images/整体设计说明/u137.png"/>
          <!-- Unnamed () -->
          <div id="u138" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u139" class="ax_default label">
          <img id="u139_img" class="img " src="images/整体设计说明/u139.png"/>
          <!-- Unnamed () -->
          <div id="u140" class="text" style="visibility: visible;">
            <p><span>短信验证码错误，请核对</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u141" class="ax_default label">
          <img id="u141_img" class="img " src="images/整体设计说明/u141.png"/>
          <!-- Unnamed () -->
          <div id="u142" class="text" style="display: none; visibility: hidden">
            <p><span></span></p>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
