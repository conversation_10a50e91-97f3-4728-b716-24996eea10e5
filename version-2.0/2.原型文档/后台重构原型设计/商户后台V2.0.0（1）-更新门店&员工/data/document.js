$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z,A,[_(u,B,w,C,y,D),_(u,E,w,C,y,F),_(u,G,w,C,y,H),_(u,I,w,C,y,J,A,[_(u,K,w,C,y,L)]),_(u,M,w,C,y,N,A,[_(u,O,w,C,y,P)]),_(u,Q,w,C,y,R),_(u,S,w,C,y,T,A,[_(u,U,w,C,y,V),_(u,W,w,C,y,X)]),_(u,Y,w,x,y,z,A,[_(u,Z,w,x,y,z,A,[_(u,ba,w,C,y,bb,A,[_(u,bc,w,C,y,bd),_(u,be,w,C,y,bf)]),_(u,bg,w,C,y,bh,A,[_(u,bi,w,C,y,bj)])]),_(u,bk,w,x,y,z,A,[_(u,bl,w,C,y,bm),_(u,bn,w,C,y,bo,A,[_(u,bp,w,C,y,bq),_(u,br,w,C,y,bs)]),_(u,bt,w,C,y,bu),_(u,bv,w,C,y,bw)])])]),_(u,bx,w,x,y,z,A,[_(u,by,w,x,y,z,A,[_(u,bz,w,C,y,bA,A,[_(u,bB,w,C,y,bC),_(u,bD,w,C,y,bE),_(u,bF,w,C,y,bG),_(u,bH,w,C,y,bI)]),_(u,bJ,w,C,y,bK),_(u,bL,w,C,y,bM),_(u,bN,w,C,y,bO)]),_(u,bP,w,x,y,z,A,[_(u,bz,w,C,y,bQ,A,[_(u,bB,w,C,y,bR),_(u,bD,w,C,y,bS),_(u,bF,w,C,y,bT),_(u,bH,w,C,y,bU)]),_(u,bJ,w,C,y,bV),_(u,bL,w,C,y,bW),_(u,bN,w,C,y,bX)])])]),bY,_(bZ,z),ca,_(cb,cc,cd,_(ce,cf,cg,cf),ch,ci),cj,[],ck,_(cl,_(cm,cn,co,cp,cq,cr,cs,ct,cu,cv,cw,f,cx,_(cy,cz,cA,cB,cC,cD),cE,cF,cG,cr,cH,_(cI,cf,cJ,cf),cd,_(ce,cf,cg,cf),cK,d,cL,f,cM,cn,cN,_(cy,cz,cA,cO),cP,_(cy,cz,cA,cQ),cR,cS,cT,cz,cC,cS,cU,cV,cW,cX,cY,cZ,da,cZ,db,cZ,dc,cZ,dd,_(),de,cV,df,cV,dg,_(dh,f,di,dj,dk,dj,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,ds)),dt,_(dh,f,di,cf,dk,dj,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,ds)),du,_(dh,f,di,cD,dk,cD,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,dv))),dw,_(dx,_(cm,dy),dz,_(cm,dA,cR,cV,cN,_(cy,cz,cA,dB)),dC,_(cm,dD,cR,cV,cN,_(cy,cz,cA,dE)),dF,_(cm,dG),dH,_(cm,dI,co,cp,cq,cr,cx,_(cy,cz,cA,cB,cC,cD),cP,_(cy,cz,cA,dJ),cR,cS,cN,_(cy,cz,cA,dK),cE,cF,cs,ct,cu,cv,cw,f,cT,cz,cU,cV,cC,cS,dg,_(dh,f,di,dj,dk,dj,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,ds)),dt,_(dh,f,di,cf,dk,dj,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,ds)),du,_(dh,f,di,cD,dk,cD,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,dv)),cW,cX,cY,cZ,da,cZ,db,cZ,dc,cZ,cG,cr),dL,_(cm,dM,cR,cV),dN,_(cm,dO,cU,dP),dQ,_(cm,dR,cu,dS,co,dT,cR,cV,cN,_(cy,cz,cA,dU),cE,dV,cW,dW,cY,cV,da,cV,db,cV,dc,cV),dX,_(cm,dY,cu,dZ,co,dT,cR,cV,cN,_(cy,cz,cA,dU),cE,dV,cW,dW,cY,cV,da,cV,db,cV,dc,cV),ea,_(cm,eb,cu,ec,co,dT,cR,cV,cN,_(cy,cz,cA,dU),cE,dV,cW,dW,cY,cV,da,cV,db,cV,dc,cV),ed,_(cm,ee,cu,ef,co,dT,cR,cV,cN,_(cy,cz,cA,dU),cE,dV,cW,dW,cY,cV,da,cV,db,cV,dc,cV),eg,_(cm,eh,co,dT,cR,cV,cN,_(cy,cz,cA,dU),cE,dV,cW,dW,cY,cV,da,cV,db,cV,dc,cV),ei,_(cm,ej,cu,ek,co,dT,cR,cV,cN,_(cy,cz,cA,dU),cE,dV,cW,dW,cY,cV,da,cV,db,cV,dc,cV),el,_(cm,em,cu,ef,cR,cV,cN,_(cy,cz,cA,dU),cE,dV,cW,dW,cY,cV,da,cV,db,cV,dc,cV),en,_(cm,eo,cR,cV,cN,_(cy,cz,cA,dU),cE,dV,cW,dW,cY,cV,da,cV,db,cV,dc,cV),ep,_(cm,eq,cN,_(cy,cz,cA,dU)),er,_(cm,es,cR,dP,cN,_(cy,cz,cA,dU)),et,_(cm,eu,cx,_(cy,cz,cA,ev,cC,cD),cE,dV,cW,cX),ew,_(cm,ex,cx,_(cy,cz,cA,ev,cC,cD),cE,dV,cW,dW),ey,_(cm,ez,cx,_(cy,cz,cA,ev,cC,cD),cE,dV,cW,dW),eA,_(cm,eB,cx,_(cy,cz,cA,ev,cC,cD),cE,dV,cW,dW),eC,_(cm,eD,cE,dV,cW,dW),eE,_(cm,eF,cE,dV,cW,dW),eG,_(cm,eH,cE,cF),eI,_(cm,eJ,cR,cV,cN,_(cy,cz,cA,dU),cE,dV,cW,cX),eK,_(cm,eL),eM,_(cm,eN,cN,_(cy,cz,cA,dU)),eO,_(cm,eP,co,cp,cq,cr,cx,_(cy,cz,cA,eQ,cC,cD),cP,_(cy,cz,cA,dJ),cR,cS,cE,cF,cs,eR,cu,eS,cw,f,cT,cz,cU,cV,cN,_(cy,cz,cA,cO),cC,cS,dg,_(dh,f,di,dj,dk,dj,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,ds)),dt,_(dh,f,di,cf,dk,dj,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,ds)),du,_(dh,f,di,cD,dk,cD,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,dv)),cW,cX,cY,cZ,da,cZ,db,cZ,dc,cZ,cG,cr),eT,_(cm,eU,cx,_(cy,cz,cA,cO,cC,cD),cP,_(cy,cz,cA,cO),cN,_(cy,cz,cA,eV),dg,_(dh,d,di,cD,dk,cD,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,eW))),eX,_(cm,eY,cN,_(cy,eZ,fa,[_(cA,cO),_(cA,dB),_(cA,fb),_(cA,cO)])),fc,_(cm,fd),fe,_(cm,ff,co,cp,cq,cr,cs,ct,cu,cv,cw,f,cx,_(cy,cz,cA,cB,cC,cD),cE,cF,cG,cr,cN,_(cy,cz,cA,cO),cP,_(cy,cz,cA,cB),cR,cS,cT,cz,cC,cS,cU,cV,cW,cX,cY,cZ,da,cZ,db,cZ,dc,cZ,dg,_(dh,f,di,dj,dk,dj,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,ds)),dt,_(dh,f,di,cf,dk,dj,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,ds)),du,_(dh,f,di,cD,dk,cD,dl,dj,cA,_(dm,dn,dp,dn,dq,dn,dr,dv))),fg,_(cm,fh,cP,_(cy,cz,cA,ev)),fi,_(cm,fj,cR,cV,cN,_(cy,cz,cA,cB))),fk,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="商户后台",w="type",x="Folder",y="url",z="",A="children",B="整体设计说明",C="Wireframe",D="整体设计说明.html",E="数据字段限制",F="数据字段限制.html",G="v2.0.0调整需求整理",H="v2_0_0调整需求整理.html",I="组织机构管理鱼骨图",J="组织机构管理鱼骨图.html",K="权限数据流向图",L="权限数据流向图.html",M="菜品管理主路径图",N="菜品管理主路径图.html",O="菜品管理操作交互逻辑",P="菜品管理操作交互逻辑.html",Q="登录",R="登录.html",S="首页-未创建菜品",T="首页-未创建菜品.html",U="首页-有商品无营业数据",V="首页-有商品无营业数据.html",W="首页-营业数据",X="首页-营业数据.html",Y="门店及员工",Z="管理员工",ba="员工列表",bb="员工列表.html",bc="新建账号",bd="新建账号.html",be="编辑员工信息",bf="编辑员工信息.html",bg="角色列表",bh="角色列表.html",bi="角色授权",bj="角色授权.html",bk="管理门店",bl="组织机构",bm="组织机构.html",bn="门店列表",bo="门店列表.html",bp="添加门店",bq="添加门店.html",br="编辑门店",bs="编辑门店.html",bt="桌位管理",bu="桌位管理.html",bv="企业品牌",bw="企业品牌.html",bx="管理商品",by="商品库",bz="商品列表",bA="商品列表.html",bB="添加商品",bC="添加商品.html",bD="编辑普通商品",bE="编辑普通商品.html",bF="编辑称重商品",bG="编辑称重商品.html",bH="编辑套餐商品",bI="编辑套餐商品.html",bJ="商品分类",bK="商品分类.html",bL="属性库",bM="属性库.html",bN="加料加价",bO="加料加价.html",bP="门店商品",bQ="商品列表_1.html",bR="添加商品_1.html",bS="编辑普通商品_1.html",bT="编辑称重商品_1.html",bU="编辑套餐商品_1.html",bV="商品分类_1.html",bW="属性库_1.html",bX="加料加价_1.html",bY="globalVariables",bZ="onloadvariable",ca="defaultAdaptiveView",cb="name",cc="Base",cd="size",ce="width",cf=0,cg="height",ch="condition",ci="<=",cj="adaptiveViews",ck="stylesheet",cl="defaultStyle",cm="id",cn="627587b6038d43cca051c114ac41ad32",co="fontWeight",cp="400",cq="fontStyle",cr="normal",cs="fontName",ct="'ArialMT', 'Arial'",cu="fontSize",cv="13px",cw="underline",cx="foreGroundFill",cy="fillType",cz="solid",cA="color",cB=0xFF333333,cC="opacity",cD=1,cE="horizontalAlignment",cF="center",cG="lineSpacing",cH="location",cI="x",cJ="y",cK="visible",cL="limbo",cM="baseStyle",cN="fill",cO=0xFFFFFFFF,cP="borderFill",cQ=0xFF797979,cR="borderWidth",cS="1",cT="linePattern",cU="cornerRadius",cV="0",cW="verticalAlignment",cX="middle",cY="paddingLeft",cZ="2",da="paddingTop",db="paddingRight",dc="paddingBottom",dd="stateStyles",de="rotation",df="textRotation",dg="outerShadow",dh="on",di="offsetX",dj=5,dk="offsetY",dl="blurRadius",dm="r",dn=0,dp="g",dq="b",dr="a",ds=0.349019607843137,dt="innerShadow",du="textShadow",dv=0.647058823529412,dw="customStyles",dx="box_1",dy="********************************",dz="box_2",dA="********************************",dB=0xFFF2F2F2,dC="box_3",dD="********************************",dE=0xFFD7D7D7,dF="ellipse",dG="eff044fe6497434a8c5f89f769ddde3b",dH="_形状",dI="40519e9ec4264601bfb12c514e4f4867",dJ=0xFFCCCCCC,dK=0x19333333,dL="image",dM="75a91ee5b9d042cfa01b8d565fe289c0",dN="button",dO="c9f35713a1cf4e91a0f2dbac65e6fb5c",dP="5",dQ="heading_1",dR="1111111151944dfba49f67fd55eb1f88",dS="32px",dT="bold",dU=0xFFFFFF,dV="left",dW="top",dX="heading_2",dY="b3a15c9ddde04520be40f94c8168891e",dZ="24px",ea="heading_3",eb="8c7a4c5ad69a4369a5f7788171ac0b32",ec="18px",ed="heading_4",ee="e995c891077945c89c0b5fe110d15a0b",ef="14px",eg="heading_5",eh="386b19ef4be143bd9b6c392ded969f89",ei="heading_6",ej="fc3b9a13b5574fa098ef0a1db9aac861",ek="10px",el="label",em="2285372321d148ec80932747449c36c9",en="paragraph",eo="4988d43d80b44008a4a415096f1632af",ep="line",eq="619b2148ccc1497285562264d51992f9",er="arrow",es="d148f2c5268542409e72dde43e40043e",et="text_field",eu="44157808f2934100b68f2394a66b2bba",ev=0xFF000000,ew="text_area",ex="42ee17691d13435b8256d8d0a814778f",ey="droplist",ez="85f724022aae41c594175ddac9c289eb",eA="list_box",eB="********************************",eC="checkbox",eD="********************************",eE="radio_button",eF="4eb5516f311c4bdfa0cb11d7ea75084e",eG="html_button",eH="eed12d9ebe2e4b9689b3b57949563dca",eI="tree_node",eJ="93a4c3353b6f4562af635b7116d6bf94",eK="table_cell",eL="33ea2511485c479dbf973af3302f2352",eM="menu_item",eN="2036b2baccbc41f0b9263a6981a11a42",eO="connector",eP="699a012e142a4bcba964d96e88b88bdf",eQ=0xFF0000FF,eR="'PingFangSC-Regular', 'PingFang SC'",eS="12px",eT="marker",eU="a8e305fe5c2a462b995b0021a9ba82b9",eV=0xFF009DD9,eW=0.698039215686274,eX="flow_shape",eY="df01900e3c4e43f284bafec04b0864c4",eZ="linearGradient",fa="colors",fb=0xFFE4E4E4,fc="table",fd="d612b8c2247342eda6a8bc0663265baa",fe="shape",ff="98c916898e844865a527f56bc61a500d",fg="horizontal_line",fh="f48196c19ab74fb7b3acb5151ce8ea2d",fi="icon",fj="26c731cb771b44a88eb8b6e97e78c80e",fk="duplicateStyles";
return _creator();
})());