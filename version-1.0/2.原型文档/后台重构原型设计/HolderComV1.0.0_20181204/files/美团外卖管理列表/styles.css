body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1392px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u6_img {
  position:absolute;
  left:0px;
  top:0px;
  width:1384px;
  height:290px;
}
#u6 {
  position:absolute;
  left:8px;
  top:42px;
  width:1384px;
  height:290px;
}
#u7 {
  position:absolute;
  left:2px;
  top:137px;
  width:1380px;
  visibility:hidden;
  word-wrap:break-word;
}
#u8_div {
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#0000FF;
  text-align:left;
}
#u8 {
  position:absolute;
  left:1220px;
  top:211px;
  width:147px;
  height:24px;
  font-size:14px;
  color:#0000FF;
  text-align:left;
}
#u9 {
  position:absolute;
  left:2px;
  top:2px;
  width:143px;
  word-wrap:break-word;
}
#u10_div {
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#0000FF;
  text-align:left;
}
#u10 {
  position:absolute;
  left:1220px;
  top:245px;
  width:147px;
  height:24px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#0000FF;
  text-align:left;
}
#u11 {
  position:absolute;
  left:2px;
  top:2px;
  width:143px;
  word-wrap:break-word;
}
