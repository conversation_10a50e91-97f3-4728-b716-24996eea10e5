$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_(),S,[_(T,bp,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_())],bt,_(bu,bv)),_(T,bw,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,bz,bh,bA),t,bB,bj,_(bk,bC,bm,bD),O,J,bE,_(y,z,A,bF,bG,bH),bI,bJ,bK,bL),P,_(),bo,_(),S,[_(T,bM,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(be,_(bf,bz,bh,bA),t,bB,bj,_(bk,bC,bm,bD),O,J,bE,_(y,z,A,bF,bG,bH),bI,bJ,bK,bL),P,_(),bo,_())],bN,g),_(T,bO,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,bz,bh,bA),t,bB,bj,_(bk,bC,bm,bP),O,J,M,bQ,bE,_(y,z,A,bF,bG,bH),bI,bJ,bK,bL),P,_(),bo,_(),S,[_(T,bR,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(be,_(bf,bz,bh,bA),t,bB,bj,_(bk,bC,bm,bP),O,J,M,bQ,bE,_(y,z,A,bF,bG,bH),bI,bJ,bK,bL),P,_(),bo,_())],bN,g)])),bS,_(),bT,_(bU,_(bV,bW),bX,_(bV,bY),bZ,_(bV,ca),cb,_(bV,cc),cd,_(bV,ce),cf,_(bV,cg)));}; 
var b="url",c="美团外卖管理列表.html",d="generationDate",e=new Date(1543888644518.59),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="241662577b124df0a8eae39b68df11eb",n="type",o="Axure:Page",p="name",q="美团外卖管理列表",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="214314d74e624b558704f93424cf015d",V="label",W="",X="friendlyType",Y="Image",Z="imageBox",ba="styleType",bb="visible",bc=true,bd="75a91ee5b9d042cfa01b8d565fe289c0",be="size",bf="width",bg=1384,bh="height",bi=290,bj="location",bk="x",bl=8,bm="y",bn=42,bo="imageOverrides",bp="741d9f27955b4f5791a4d8b46b16dedd",bq="isContained",br="richTextPanel",bs="paragraph",bt="images",bu="normal~",bv="images/美团外卖管理列表/u6.jpg",bw="1bdc5ce4882848fdb1dfb9c8088a8fa2",bx="Rectangle",by="vectorShape",bz=147,bA=24,bB="4b7bfc596114427989e10bb0b557d0ce",bC=1220,bD=211,bE="foreGroundFill",bF=0xFF0000FF,bG="opacity",bH=1,bI="horizontalAlignment",bJ="left",bK="fontSize",bL="14px",bM="ed6554c0a32c4fdb91d88f2884e7b96a",bN="generateCompound",bO="1a146e3c0caf42ae95d04943b442798d",bP=245,bQ="'PingFangSC-Regular', 'PingFang SC'",bR="d3c40b2ea646481bbdd62c42066c78af",bS="masters",bT="objectPaths",bU="214314d74e624b558704f93424cf015d",bV="scriptId",bW="u6",bX="741d9f27955b4f5791a4d8b46b16dedd",bY="u7",bZ="1bdc5ce4882848fdb1dfb9c8088a8fa2",ca="u8",cb="ed6554c0a32c4fdb91d88f2884e7b96a",cc="u9",cd="1a146e3c0caf42ae95d04943b442798d",ce="u10",cf="d3c40b2ea646481bbdd62c42066c78af",cg="u11";
return _creator();
})());