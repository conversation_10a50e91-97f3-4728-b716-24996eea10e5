$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bl)),P,_(),bn,_(),S,[_(T,bo,V,W,X,null,bp,bc,n,bq,ba,br,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bl)),P,_(),bn,_())],bs,_(bt,bu))])),bv,_(),bw,_(bx,_(by,bz),bA,_(by,bB)));}; 
var b="url",c="外卖-饿了么,订单流程20181123.html",d="generationDate",e=new Date(1543888644382.18),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="159a4a82ebcd44419524f3344c6477cf",n="type",o="Axure:Page",p="name",q="外卖-饿了么,订单流程20181123",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="aacaafa541754e7caa787f92de055ed1",V="label",W="",X="friendlyType",Y="Image",Z="imageBox",ba="styleType",bb="visible",bc=true,bd="75a91ee5b9d042cfa01b8d565fe289c0",be="size",bf="width",bg=1181,bh="height",bi=1478,bj="location",bk="x",bl=10,bm="y",bn="imageOverrides",bo="90f364d14c024b63bec33d55da141ecc",bp="isContained",bq="richTextPanel",br="paragraph",bs="images",bt="normal~",bu="images/外卖-饿了么,订单流程20181123/u0.jpg",bv="masters",bw="objectPaths",bx="aacaafa541754e7caa787f92de055ed1",by="scriptId",bz="u0",bA="90f364d14c024b63bec33d55da141ecc",bB="u1";
return _creator();
})());