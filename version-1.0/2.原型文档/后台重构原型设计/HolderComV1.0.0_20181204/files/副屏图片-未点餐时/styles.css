body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1379px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
#base {
  position:absolute;
  z-index:0;
}
#u1898_img {
  position:absolute;
  left:0px;
  top:0px;
  width:1369px;
  height:699px;
}
#u1898 {
  position:absolute;
  left:10px;
  top:40px;
  width:1369px;
  height:699px;
}
#u1899 {
  position:absolute;
  left:2px;
  top:342px;
  width:1365px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1900_div {
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1900 {
  position:absolute;
  left:245px;
  top:191px;
  width:101px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1901 {
  position:absolute;
  left:2px;
  top:5px;
  width:97px;
  word-wrap:break-word;
}
#u1902_div {
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1902 {
  position:absolute;
  left:362px;
  top:191px;
  width:101px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1903 {
  position:absolute;
  left:2px;
  top:5px;
  width:97px;
  word-wrap:break-word;
}
#u1904_div {
  position:absolute;
  left:0px;
  top:0px;
  width:390px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1904 {
  position:absolute;
  left:541px;
  top:267px;
  width:390px;
  height:30px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1905 {
  position:absolute;
  left:2px;
  top:7px;
  width:386px;
  visibility:hidden;
  word-wrap:break-word;
}
#u1906 {
  position:absolute;
  left:305px;
  top:260px;
  width:134px;
  height:38px;
}
#u1906_input {
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:38px;
  font-family:'ArialMT', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:none;
  color:#000000;
  text-align:left;
}
#u1907_div {
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:39px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1907 {
  position:absolute;
  left:1209px;
  top:328px;
  width:128px;
  height:39px;
  font-family:'PingFangSC-Regular', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1908 {
  position:absolute;
  left:2px;
  top:12px;
  width:124px;
  visibility:hidden;
  word-wrap:break-word;
}
