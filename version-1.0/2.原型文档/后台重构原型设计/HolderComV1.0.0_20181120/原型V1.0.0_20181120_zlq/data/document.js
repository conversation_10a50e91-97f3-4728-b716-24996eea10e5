$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,f,p,f,q,[],r,f),s,_(t,[_(u,v,w,x,y,z),_(u,A,w,B,y,C,D,[_(u,E,w,x,y,F),_(u,G,w,x,y,H),_(u,I,w,x,y,J),_(u,K,w,x,y,L),_(u,M,w,x,y,N),_(u,O,w,x,y,P)]),_(u,Q,w,B,y,C,D,[_(u,R,w,x,y,S)]),_(u,T,w,x,y,U)]),V,_(W,<PERSON>),X,_(Y,Z,ba,_(bb,bc,bd,bc),be,bf),bg,[],bh,_(bi,_(bj,bk,bl,bm,bn,bo,bp,bq,br,bs,bt,f,bu,_(bv,bw,bx,by,bz,bA),bB,bC,bD,bo,bE,_(bF,bc,bG,bc),ba,_(bb,bc,bd,bc),bH,d,bI,f,bJ,bk,bK,_(bv,bw,bx,bL),bM,_(bv,bw,bx,bN),bO,bP,bQ,bw,bz,bP,bR,bS,bT,bU,bV,bW,bX,bW,bY,bW,bZ,bW,ca,_(),cb,bS,cc,bS,cd,_(ce,f,cf,cg,ch,cg,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,co)),cp,_(ce,f,cf,bc,ch,cg,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,co)),cq,_(ce,f,cf,bA,ch,bA,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,cr))),cs,_(ct,_(bj,cu),cv,_(bj,cw,bO,bS,bK,_(bv,bw,bx,cx)),cy,_(bj,cz,bO,bS,bK,_(bv,bw,bx,cA)),cB,_(bj,cC),cD,_(bj,cE,bl,bm,bn,bo,bu,_(bv,bw,bx,by,bz,bA),bM,_(bv,bw,bx,cF),bO,bP,bK,_(bv,bw,bx,cG),bB,bC,bp,bq,br,bs,bt,f,bQ,bw,bR,bS,bz,bP,cd,_(ce,f,cf,cg,ch,cg,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,co)),cp,_(ce,f,cf,bc,ch,cg,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,co)),cq,_(ce,f,cf,bA,ch,bA,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,cr)),bT,bU,bV,bW,bX,bW,bY,bW,bZ,bW,bD,bo),cH,_(bj,cI,bO,bS),cJ,_(bj,cK,bR,cL),cM,_(bj,cN,br,cO,bl,cP,bO,bS,bK,_(bv,bw,bx,cQ),bB,cR,bT,cS,bV,bS,bX,bS,bY,bS,bZ,bS),cT,_(bj,cU,br,cV,bl,cP,bO,bS,bK,_(bv,bw,bx,cQ),bB,cR,bT,cS,bV,bS,bX,bS,bY,bS,bZ,bS),cW,_(bj,cX,br,cY,bl,cP,bO,bS,bK,_(bv,bw,bx,cQ),bB,cR,bT,cS,bV,bS,bX,bS,bY,bS,bZ,bS),cZ,_(bj,da,br,db,bl,cP,bO,bS,bK,_(bv,bw,bx,cQ),bB,cR,bT,cS,bV,bS,bX,bS,bY,bS,bZ,bS),dc,_(bj,dd,bl,cP,bO,bS,bK,_(bv,bw,bx,cQ),bB,cR,bT,cS,bV,bS,bX,bS,bY,bS,bZ,bS),de,_(bj,df,br,dg,bl,cP,bO,bS,bK,_(bv,bw,bx,cQ),bB,cR,bT,cS,bV,bS,bX,bS,bY,bS,bZ,bS),dh,_(bj,di,br,db,bO,bS,bK,_(bv,bw,bx,cQ),bB,cR,bT,cS,bV,bS,bX,bS,bY,bS,bZ,bS),dj,_(bj,dk,bO,bS,bK,_(bv,bw,bx,cQ),bB,cR,bT,cS,bV,bS,bX,bS,bY,bS,bZ,bS),dl,_(bj,dm,bK,_(bv,bw,bx,cQ)),dn,_(bj,dp,bO,cL,bK,_(bv,bw,bx,cQ)),dq,_(bj,dr,bu,_(bv,bw,bx,ds,bz,bA),bB,cR,bT,bU),dt,_(bj,du,bu,_(bv,bw,bx,ds,bz,bA),bB,cR,bT,cS),dv,_(bj,dw,bu,_(bv,bw,bx,ds,bz,bA),bB,cR,bT,cS),dx,_(bj,dy,bu,_(bv,bw,bx,ds,bz,bA),bB,cR,bT,cS),dz,_(bj,dA,bB,cR,bT,cS),dB,_(bj,dC,bB,cR,bT,cS),dD,_(bj,dE,bB,bC),dF,_(bj,dG,bO,bS,bK,_(bv,bw,bx,cQ),bB,cR,bT,bU),dH,_(bj,dI),dJ,_(bj,dK,bK,_(bv,bw,bx,cQ)),dL,_(bj,dM,bl,bm,bn,bo,bu,_(bv,bw,bx,dN,bz,bA),bM,_(bv,bw,bx,cF),bO,bP,bB,bC,bp,dO,br,dP,bt,f,bQ,bw,bR,bS,bK,_(bv,bw,bx,bL),bz,bP,cd,_(ce,f,cf,cg,ch,cg,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,co)),cp,_(ce,f,cf,bc,ch,cg,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,co)),cq,_(ce,f,cf,bA,ch,bA,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,cr)),bT,bU,bV,bW,bX,bW,bY,bW,bZ,bW,bD,bo),dQ,_(bj,dR,bu,_(bv,bw,bx,bL,bz,bA),bM,_(bv,bw,bx,bL),bK,_(bv,bw,bx,dS),cd,_(ce,d,cf,bA,ch,bA,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,dT))),dU,_(bj,dV,bK,_(bv,dW,dX,[_(bx,bL),_(bx,cx),_(bx,dY),_(bx,bL)])),dZ,_(bj,ea),eb,_(bj,ec,bl,bm,bn,bo,bp,bq,br,bs,bt,f,bu,_(bv,bw,bx,by,bz,bA),bB,bC,bD,bo,bK,_(bv,bw,bx,bL),bM,_(bv,bw,bx,by),bO,bP,bQ,bw,bz,bP,bR,bS,bT,bU,bV,bW,bX,bW,bY,bW,bZ,bW,cd,_(ce,f,cf,cg,ch,cg,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,co)),cp,_(ce,f,cf,bc,ch,cg,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,co)),cq,_(ce,f,cf,bA,ch,bA,ci,cg,bx,_(cj,ck,cl,ck,cm,ck,cn,cr))),ed,_(bj,ee,bM,_(bv,bw,bx,ds)),ef,_(bj,eg,bO,bS,bK,_(bv,bw,bx,by))),eh,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="hideAddress",o="preventScroll",p="useLabels",q="enabledViewIds",r="loadFeedbackPlugin",s="sitemap",t="rootNodes",u="pageName",v="角色权限",w="type",x="Wireframe",y="url",z="角色权限.html",A="销售管理",B="Folder",C="",D="children",E="销售管理导航",F="销售管理导航.html",G="菜品库",H="菜品库.html",I="门店菜品",J="门店菜品.html",K="新建/编辑菜品第二步",L="新建_编辑菜品第二步.html",M="副屏图片-未点餐时",N="副屏图片-未点餐时.html",O="副屏图片-正在点餐",P="副屏图片-正在点餐.html",Q="会员营销",R="会员规则",S="会员规则.html",T="报表数据查询字段",U="报表数据查询字段.html",V="globalVariables",W="onloadvariable",X="defaultAdaptiveView",Y="name",Z="Base",ba="size",bb="width",bc=0,bd="height",be="condition",bf="<=",bg="adaptiveViews",bh="stylesheet",bi="defaultStyle",bj="id",bk="627587b6038d43cca051c114ac41ad32",bl="fontWeight",bm="400",bn="fontStyle",bo="normal",bp="fontName",bq="'ArialMT', 'Arial'",br="fontSize",bs="13px",bt="underline",bu="foreGroundFill",bv="fillType",bw="solid",bx="color",by=0xFF333333,bz="opacity",bA=1,bB="horizontalAlignment",bC="center",bD="lineSpacing",bE="location",bF="x",bG="y",bH="visible",bI="limbo",bJ="baseStyle",bK="fill",bL=0xFFFFFFFF,bM="borderFill",bN=0xFF797979,bO="borderWidth",bP="1",bQ="linePattern",bR="cornerRadius",bS="0",bT="verticalAlignment",bU="middle",bV="paddingLeft",bW="2",bX="paddingTop",bY="paddingRight",bZ="paddingBottom",ca="stateStyles",cb="rotation",cc="textRotation",cd="outerShadow",ce="on",cf="offsetX",cg=5,ch="offsetY",ci="blurRadius",cj="r",ck=0,cl="g",cm="b",cn="a",co=0.349019607843137,cp="innerShadow",cq="textShadow",cr=0.647058823529412,cs="customStyles",ct="box_1",cu="********************************",cv="box_2",cw="********************************",cx=0xFFF2F2F2,cy="box_3",cz="********************************",cA=0xFFD7D7D7,cB="ellipse",cC="eff044fe6497434a8c5f89f769ddde3b",cD="_形状",cE="40519e9ec4264601bfb12c514e4f4867",cF=0xFFCCCCCC,cG=0x19333333,cH="image",cI="75a91ee5b9d042cfa01b8d565fe289c0",cJ="button",cK="c9f35713a1cf4e91a0f2dbac65e6fb5c",cL="5",cM="heading_1",cN="1111111151944dfba49f67fd55eb1f88",cO="32px",cP="bold",cQ=0xFFFFFF,cR="left",cS="top",cT="heading_2",cU="b3a15c9ddde04520be40f94c8168891e",cV="24px",cW="heading_3",cX="8c7a4c5ad69a4369a5f7788171ac0b32",cY="18px",cZ="heading_4",da="e995c891077945c89c0b5fe110d15a0b",db="14px",dc="heading_5",dd="386b19ef4be143bd9b6c392ded969f89",de="heading_6",df="fc3b9a13b5574fa098ef0a1db9aac861",dg="10px",dh="label",di="2285372321d148ec80932747449c36c9",dj="paragraph",dk="4988d43d80b44008a4a415096f1632af",dl="line",dm="619b2148ccc1497285562264d51992f9",dn="arrow",dp="d148f2c5268542409e72dde43e40043e",dq="text_field",dr="44157808f2934100b68f2394a66b2bba",ds=0xFF000000,dt="text_area",du="42ee17691d13435b8256d8d0a814778f",dv="droplist",dw="85f724022aae41c594175ddac9c289eb",dx="list_box",dy="********************************",dz="checkbox",dA="********************************",dB="radio_button",dC="4eb5516f311c4bdfa0cb11d7ea75084e",dD="html_button",dE="eed12d9ebe2e4b9689b3b57949563dca",dF="tree_node",dG="93a4c3353b6f4562af635b7116d6bf94",dH="table_cell",dI="33ea2511485c479dbf973af3302f2352",dJ="menu_item",dK="2036b2baccbc41f0b9263a6981a11a42",dL="connector",dM="699a012e142a4bcba964d96e88b88bdf",dN=0xFF0000FF,dO="'PingFangSC-Regular', 'PingFang SC'",dP="12px",dQ="marker",dR="a8e305fe5c2a462b995b0021a9ba82b9",dS=0xFF009DD9,dT=0.698039215686274,dU="flow_shape",dV="df01900e3c4e43f284bafec04b0864c4",dW="linearGradient",dX="colors",dY=0xFFE4E4E4,dZ="table",ea="d612b8c2247342eda6a8bc0663265baa",eb="shape",ec="98c916898e844865a527f56bc61a500d",ed="horizontal_line",ee="f48196c19ab74fb7b3acb5151ce8ea2d",ef="icon",eg="26c731cb771b44a88eb8b6e97e78c80e",eh="duplicateStyles";
return _creator();
})());