$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_(),S,[_(T,bp,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_())],bt,_(bu,bv)),_(T,bw,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,bz,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,bH,bm,bI),bJ,_(y,z,A,B)),P,_(),bo,_(),S,[_(T,bK,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(be,_(bf,bz,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,bH,bm,bI),bJ,_(y,z,A,B)),P,_(),bo,_())],bL,g),_(T,bM,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,bz,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,bN,bm,bI),bJ,_(y,z,A,B)),P,_(),bo,_(),S,[_(T,bO,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(be,_(bf,bz,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,bN,bm,bI),bJ,_(y,z,A,B)),P,_(),bo,_())],bL,g),_(T,bP,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,bQ,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,bR,bm,bS),bJ,_(y,z,A,B)),P,_(),bo,_(),S,[_(T,bT,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(be,_(bf,bQ,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,bR,bm,bS),bJ,_(y,z,A,B)),P,_(),bo,_())],bL,g),_(T,bU,V,W,X,bV,n,bW,ba,bW,bb,bc,s,_(be,_(bf,bX,bh,bY),bZ,_(ca,_(cb,_(y,z,A,cc,cd,ce))),t,cf,bj,_(bk,cg,bm,ch),bE,ci),cj,g,P,_(),bo,_(),ck,cl),_(T,cm,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,cn,bh,co),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,cp,bm,cq),bJ,_(y,z,A,B)),P,_(),bo,_(),S,[_(T,cr,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(be,_(bf,cn,bh,co),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,cp,bm,cq),bJ,_(y,z,A,B)),P,_(),bo,_())],bL,g)])),cs,_(),ct,_(cu,_(cv,cw),cx,_(cv,cy),cz,_(cv,cA),cB,_(cv,cC),cD,_(cv,cE),cF,_(cv,cG),cH,_(cv,cI),cJ,_(cv,cK),cL,_(cv,cM),cN,_(cv,cO),cP,_(cv,cQ)));}; 
var b="url",c="副屏图片-未点餐时.html",d="generationDate",e=new Date(1542705078245.92),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="2f0c604f5ae947f69c45b5b47d088581",n="type",o="Axure:Page",p="name",q="副屏图片-未点餐时",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="4d1e4a5f26d64617985b198a90581b69",V="label",W="",X="friendlyType",Y="Image",Z="imageBox",ba="styleType",bb="visible",bc=true,bd="75a91ee5b9d042cfa01b8d565fe289c0",be="size",bf="width",bg=1369,bh="height",bi=699,bj="location",bk="x",bl=10,bm="y",bn=40,bo="imageOverrides",bp="f5fc8fc724624f71a208e8989e683cd2",bq="isContained",br="richTextPanel",bs="paragraph",bt="images",bu="normal~",bv="images/副屏图片-未点餐时/u10.png",bw="235447d05aa54685b7cb3983ca3a35c4",bx="Rectangle",by="vectorShape",bz=101,bA=30,bB="4b7bfc596114427989e10bb0b557d0ce",bC="cornerRadius",bD="4",bE="fontSize",bF="14px",bG="'PingFangSC-Regular', 'PingFang SC'",bH=245,bI=191,bJ="borderFill",bK="ff7ed25321544b16bf30b464e08d3f87",bL="generateCompound",bM="224c101641eb42e7a940bbf235c99012",bN=362,bO="ec1d3c3e120b446aaf0750203bbb8408",bP="ac7f47079fd84070b6e569ef8c208be5",bQ=390,bR=541,bS=267,bT="4740055520824bff822ad0310a98beaa",bU="6627f5b8033349e8b87b2620e9664ff3",bV="Text Field",bW="textBox",bX=134,bY=38,bZ="stateStyles",ca="hint",cb="foreGroundFill",cc=0xFF999999,cd="opacity",ce=1,cf="44157808f2934100b68f2394a66b2bba",cg=305,ch=260,ci="12px",cj="HideHintOnFocused",ck="placeholderText",cl="菜品编号/名称",cm="8312278e784e4756a1f91ad6442a0bfa",cn=128,co=39,cp=1209,cq=328,cr="79a4ce9493d6436eb3b1107dd46c9ab8",cs="masters",ct="objectPaths",cu="4d1e4a5f26d64617985b198a90581b69",cv="scriptId",cw="u10",cx="f5fc8fc724624f71a208e8989e683cd2",cy="u11",cz="235447d05aa54685b7cb3983ca3a35c4",cA="u12",cB="ff7ed25321544b16bf30b464e08d3f87",cC="u13",cD="224c101641eb42e7a940bbf235c99012",cE="u14",cF="ec1d3c3e120b446aaf0750203bbb8408",cG="u15",cH="ac7f47079fd84070b6e569ef8c208be5",cI="u16",cJ="4740055520824bff822ad0310a98beaa",cK="u17",cL="6627f5b8033349e8b87b2620e9664ff3",cM="u18",cN="8312278e784e4756a1f91ad6442a0bfa",cO="u19",cP="79a4ce9493d6436eb3b1107dd46c9ab8",cQ="u20";
return _creator();
})());