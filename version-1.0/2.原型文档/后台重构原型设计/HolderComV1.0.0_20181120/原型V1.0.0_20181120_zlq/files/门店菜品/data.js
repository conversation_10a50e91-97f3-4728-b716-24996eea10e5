$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_(),S,[_(T,bp,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_())],bt,_(bu,bv))])),bw,_(),bx,_(by,_(bz,bA),bB,_(bz,bC)));}; 
var b="url",c="门店菜品.html",d="generationDate",e=new Date(1542705078155.01),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="744df974d36f44309d0eeabc9a8f97c6",n="type",o="Axure:Page",p="name",q="门店菜品",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="d8253468812548ddab9f5fb347101c8b",V="label",W="",X="friendlyType",Y="Image",Z="imageBox",ba="styleType",bb="visible",bc=true,bd="75a91ee5b9d042cfa01b8d565fe289c0",be="size",bf="width",bg=3057,bh="height",bi=989,bj="location",bk="x",bl=10,bm="y",bn=40,bo="imageOverrides",bp="b6c4a2786c24475fa52a82ff33908e25",bq="isContained",br="richTextPanel",bs="paragraph",bt="images",bu="normal~",bv="images/门店菜品/u6.jpg",bw="masters",bx="objectPaths",by="d8253468812548ddab9f5fb347101c8b",bz="scriptId",bA="u6",bB="b6c4a2786c24475fa52a82ff33908e25",bC="u7";
return _creator();
})());