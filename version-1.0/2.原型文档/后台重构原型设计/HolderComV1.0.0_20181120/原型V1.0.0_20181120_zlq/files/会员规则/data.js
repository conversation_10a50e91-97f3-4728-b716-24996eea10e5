$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_(),S,[_(T,bp,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_())],bt,_(bu,bv))])),bw,_(),bx,_(by,_(bz,bA),bB,_(bz,bC)));}; 
var b="url",c="会员规则.html",d="generationDate",e=new Date(1542705078343.89),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="924f20785c1543e1bba4f8cc3fb6916e",n="type",o="Axure:Page",p="name",q="会员规则",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="c636d340aacb46bcabb8eb44841b22f3",V="label",W="",X="friendlyType",Y="Image",Z="imageBox",ba="styleType",bb="visible",bc=true,bd="75a91ee5b9d042cfa01b8d565fe289c0",be="size",bf="width",bg=1380,bh="height",bi=565,bj="location",bk="x",bl=10,bm="y",bn=40,bo="imageOverrides",bp="0f7afa8f48a5483498994f9da2fdb586",bq="isContained",br="richTextPanel",bs="paragraph",bt="images",bu="normal~",bv="images/会员规则/u32.png",bw="masters",bx="objectPaths",by="c636d340aacb46bcabb8eb44841b22f3",bz="scriptId",bA="u32",bB="0f7afa8f48a5483498994f9da2fdb586",bC="u33";
return _creator();
})());