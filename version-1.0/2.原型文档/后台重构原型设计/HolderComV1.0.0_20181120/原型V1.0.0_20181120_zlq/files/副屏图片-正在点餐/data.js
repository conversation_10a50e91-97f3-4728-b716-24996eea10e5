$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,g,i,[j],k,_(l,m,n,o,p,q,r,_(),s,_(t,u,v,w,x,_(y,z,A,B),C,null,D,w,E,w,F,G,H,null,I,J,K,L,M,N,O,J),P,_(),Q,_(),R,_(S,[_(T,U,V,W,X,Y,n,Z,ba,Z,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_(),S,[_(T,bp,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(t,bd,be,_(bf,bg,bh,bi),bj,_(bk,bl,bm,bn)),P,_(),bo,_())],bt,_(bu,bv)),_(T,bw,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,bz,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,bH,bm,bI),bJ,_(y,z,A,B)),P,_(),bo,_(),S,[_(T,bK,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(be,_(bf,bz,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,bH,bm,bI),bJ,_(y,z,A,B)),P,_(),bo,_())],bL,g),_(T,bM,V,W,X,bN,n,bO,ba,bO,bb,bc,s,_(be,_(bf,bP,bh,bQ),bR,_(bS,_(bT,_(y,z,A,bU,bV,bW))),t,bX,bj,_(bk,bY,bm,bZ),bE,ca),cb,g,P,_(),bo,_(),cc,cd),_(T,ce,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,cf,bh,cg),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,ch,bm,ci),bJ,_(y,z,A,B)),P,_(),bo,_(),S,[_(T,cj,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(be,_(bf,cf,bh,cg),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,ch,bm,ci),bJ,_(y,z,A,B)),P,_(),bo,_())],bL,g),_(T,ck,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,cl,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,cm,bm,cn),bJ,_(y,z,A,B)),P,_(),bo,_(),S,[_(T,co,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(be,_(bf,cl,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,cm,bm,cn),bJ,_(y,z,A,B)),P,_(),bo,_())],bL,g),_(T,cp,V,W,X,bx,n,by,ba,by,bb,bc,s,_(be,_(bf,cl,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,cq,bm,cn),bJ,_(y,z,A,B)),P,_(),bo,_(),S,[_(T,cr,V,W,X,null,bq,bc,n,br,ba,bs,bb,bc,s,_(be,_(bf,cl,bh,bA),t,bB,bC,bD,bE,bF,M,bG,bj,_(bk,cq,bm,cn),bJ,_(y,z,A,B)),P,_(),bo,_())],bL,g)])),cs,_(),ct,_(cu,_(cv,cw),cx,_(cv,cy),cz,_(cv,cA),cB,_(cv,cC),cD,_(cv,cE),cF,_(cv,cG),cH,_(cv,cI),cJ,_(cv,cK),cL,_(cv,cM),cN,_(cv,cO),cP,_(cv,cQ)));}; 
var b="url",c="副屏图片-正在点餐.html",d="generationDate",e=new Date(1542705078295.09),f="isCanvasEnabled",g=false,h="isAdaptiveEnabled",i="variables",j="OnLoadVariable",k="page",l="packageId",m="cf10de3b03fe4e8d8730fd73a7987ea6",n="type",o="Axure:Page",p="name",q="副屏图片-正在点餐",r="notes",s="style",t="baseStyle",u="627587b6038d43cca051c114ac41ad32",v="pageAlignment",w="near",x="fill",y="fillType",z="solid",A="color",B=0xFFFFFFFF,C="image",D="imageHorizontalAlignment",E="imageVerticalAlignment",F="imageRepeat",G="auto",H="favicon",I="sketchFactor",J="0",K="colorStyle",L="appliedColor",M="fontName",N="Applied Font",O="borderWidth",P="adaptiveStyles",Q="interactionMap",R="diagram",S="objects",T="id",U="2b7a85a6090f46c3b3fd14d3873f6eec",V="label",W="",X="friendlyType",Y="Image",Z="imageBox",ba="styleType",bb="visible",bc=true,bd="75a91ee5b9d042cfa01b8d565fe289c0",be="size",bf="width",bg=1383,bh="height",bi=654,bj="location",bk="x",bl=10,bm="y",bn=40,bo="imageOverrides",bp="89db555d2dda4b8c8001049b783b8f9f",bq="isContained",br="richTextPanel",bs="paragraph",bt="images",bu="normal~",bv="images/副屏图片-正在点餐/u21.png",bw="9fa149912fbf49799797b9cabd3cb1d1",bx="Rectangle",by="vectorShape",bz=398,bA=30,bB="4b7bfc596114427989e10bb0b557d0ce",bC="cornerRadius",bD="4",bE="fontSize",bF="14px",bG="'PingFangSC-Regular', 'PingFang SC'",bH=552,bI=249,bJ="borderFill",bK="bd5f38aa24944f229c6b1c9e0be790a5",bL="generateCompound",bM="e6442e18acee4ca49fe4367924b7f430",bN="Text Field",bO="textBox",bP=134,bQ=38,bR="stateStyles",bS="hint",bT="foreGroundFill",bU=0xFF999999,bV="opacity",bW=1,bX="44157808f2934100b68f2394a66b2bba",bY=316,bZ=242,ca="12px",cb="HideHintOnFocused",cc="placeholderText",cd="菜品编号/名称",ce="2564ae39bd7e46eea1ee61b5e6269eee",cf=128,cg=39,ch=1219,ci=303,cj="32c9c821af4d4fa79693d9351f6ac616",ck="3697d82fe15e41e4a563ce4c4f90f0d0",cl=101,cm=255,cn=172,co="32a8852fa7234e2b9a4a170d42c4ae35",cp="759de24a1b114087b0619624ea7634bb",cq=372,cr="77270d60f5e447a1bfe141df20ca41eb",cs="masters",ct="objectPaths",cu="2b7a85a6090f46c3b3fd14d3873f6eec",cv="scriptId",cw="u21",cx="89db555d2dda4b8c8001049b783b8f9f",cy="u22",cz="9fa149912fbf49799797b9cabd3cb1d1",cA="u23",cB="bd5f38aa24944f229c6b1c9e0be790a5",cC="u24",cD="e6442e18acee4ca49fe4367924b7f430",cE="u25",cF="2564ae39bd7e46eea1ee61b5e6269eee",cG="u26",cH="32c9c821af4d4fa79693d9351f6ac616",cI="u27",cJ="3697d82fe15e41e4a563ce4c4f90f0d0",cK="u28",cL="32a8852fa7234e2b9a4a170d42c4ae35",cM="u29",cN="759de24a1b114087b0619624ea7634bb",cO="u30",cP="77270d60f5e447a1bfe141df20ca41eb",cQ="u31";
return _creator();
})());