<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:lang="http://www.springframework.org/schema/lang"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd
           http://www.springframework.org/schema/lang http://www.springframework.org/schema/lang/spring-lang-2.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd"
	default-autowire="byName">
	
	<!-- properties -->
	<bean class="com.alibaba.otter.canal.instance.spring.support.PropertyPlaceholderConfigurer" lazy-init="false">
		<property name="ignoreResourceNotFound" value="true" />
		<property name="systemPropertiesModeName" value="SYSTEM_PROPERTIES_MODE_OVERRIDE"/><!-- 允许system覆盖 -->
		<property name="locationNames">
			<list>
				<value>classpath:canal.properties</value>
				<value>classpath:${canal.instance.destination:}/instance.properties</value>
			</list>
		</property>
	</bean>
	
	<!-- 基于db的实现 -->
	<bean id="tableMetaTSDB" class="com.alibaba.otter.canal.parse.inbound.mysql.tsdb.DatabaseTableMeta" destroy-method="destory">
		<property name="metaHistoryDAO" ref="metaHistoryDAO"/>
		<property name="metaSnapshotDAO" ref="metaSnapshotDAO"/>
	</bean>
	
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
        <property name="driverClassName" value="org.h2.Driver" />
		<property name="url" value="${canal.instance.tsdb.url:}" />
		<property name="username" value="${canal.instance.tsdb.dbUsername:}" />
		<property name="password" value="${canal.instance.tsdb.dbPassword:}" />
      	<property name="maxActive" value="30" />
        <property name="initialSize" value="0" />
        <property name="minIdle" value="1" />
        <property name="maxWait" value="10000" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="300000" />
        <property name="testWhileIdle" value="true" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="useUnfairLock" value="true" />
	</bean>

    <bean id="sqlMapClient" class="org.springframework.orm.ibatis.SqlMapClientFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="configLocation" value="classpath:spring/tsdb/sql-map/sqlmap-config.xml"/>
    </bean>

    <bean id="metaHistoryDAO" class="com.alibaba.otter.canal.parse.inbound.mysql.tsdb.dao.MetaHistoryDAO">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>

    <bean id="metaSnapshotDAO" class="com.alibaba.otter.canal.parse.inbound.mysql.tsdb.dao.MetaSnapshotDAO">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
</beans>
