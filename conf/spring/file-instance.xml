<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:lang="http://www.springframework.org/schema/lang"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd
           http://www.springframework.org/schema/lang http://www.springframework.org/schema/lang/spring-lang-2.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd"
	default-autowire="byName">

	<import resource="classpath:spring/base-instance.xml" />

	<bean id="instance" class="com.alibaba.otter.canal.instance.spring.CanalInstanceWithSpring">
		<property name="destination" value="${canal.instance.destination}" />
		<property name="eventParser">
			<ref local="eventParser" />
		</property>
		<property name="eventSink">
			<ref local="eventSink" />
		</property>
		<property name="eventStore">
			<ref local="eventStore" />
		</property>
		<property name="metaManager">
			<ref local="metaManager" />
		</property>
		<property name="alarmHandler">
			<ref local="alarmHandler" />
		</property>
        <property name="mqConfig">
            <ref local="mqConfig" />
        </property>
	</bean>

	<!-- 报警处理类 -->
	<bean id="alarmHandler" class="com.alibaba.otter.canal.common.alarm.LogAlarmHandler" />

	<bean id="metaManager" class="com.alibaba.otter.canal.meta.FileMixedMetaManager">
		<property name="dataDir" value="${canal.file.data.dir:../conf}" />
		<property name="period" value="${canal.file.flush.period:1000}" />
	</bean>

	<bean id="eventStore" class="com.alibaba.otter.canal.store.memory.MemoryEventStoreWithBuffer">
		<property name="bufferSize" value="${canal.instance.memory.buffer.size:16384}" />
		<property name="bufferMemUnit" value="${canal.instance.memory.buffer.memunit:1024}" />
		<property name="batchMode" value="${canal.instance.memory.batch.mode:MEMSIZE}" />
		<property name="ddlIsolation" value="${canal.instance.get.ddl.isolation:false}" />
		<property name="raw" value="${canal.instance.memory.rawEntry:true}" />
	</bean>

	<bean id="eventSink" class="com.alibaba.otter.canal.sink.entry.EntryEventSink">
		<property name="eventStore" ref="eventStore" />
		<property name="filterTransactionEntry" value="${canal.instance.filter.transaction.entry:false}"/>
	</bean>

	<bean id="eventParser" parent="baseEventParser">
		<property name="destination" value="${canal.instance.destination}" />
		<property name="slaveId" value="${canal.instance.mysql.slaveId:0}" />
		<!-- 心跳配置 -->
		<property name="detectingEnable" value="${canal.instance.detecting.enable:false}" />
		<property name="detectingSQL" value="${canal.instance.detecting.sql}" />
		<property name="detectingIntervalInSeconds" value="${canal.instance.detecting.interval.time:5}" />
		<property name="haController">
			<bean class="com.alibaba.otter.canal.parse.ha.HeartBeatHAController">
				<property name="detectingRetryTimes" value="${canal.instance.detecting.retry.threshold:3}" />
				<property name="switchEnable" value="${canal.instance.detecting.heartbeatHaEnable:false}" />
			</bean>
		</property>

		<property name="alarmHandler" ref="alarmHandler" />

		<!-- 解析过滤处理 -->
		<property name="eventFilter">
			<bean class="com.alibaba.otter.canal.filter.aviater.AviaterRegexFilter" >
				<constructor-arg index="0" value="${canal.instance.filter.regex:.*\..*}" />
			</bean>
		</property>

		<property name="eventBlackFilter">
			<bean class="com.alibaba.otter.canal.filter.aviater.AviaterRegexFilter" >
				<constructor-arg index="0" value="${canal.instance.filter.black.regex:}" />
				<constructor-arg index="1" value="false" />
			</bean>
		</property>
		<!-- 最大事务解析大小，超过该大小后事务将被切分为多个事务投递 -->
		<property name="transactionSize" value="${canal.instance.transaction.size:1024}" />

		<!-- 网络链接参数 -->
		<property name="receiveBufferSize" value="${canal.instance.network.receiveBufferSize:16384}" />
		<property name="sendBufferSize" value="${canal.instance.network.sendBufferSize:16384}" />
		<property name="defaultConnectionTimeoutInSeconds" value="${canal.instance.network.soTimeout:30}" />

		<!-- 解析编码 -->
		<!-- property name="connectionCharsetNumber" value="${canal.instance.connectionCharsetNumber:33}" /-->
		<property name="connectionCharset" value="${canal.instance.connectionCharset:UTF-8}" />

		<!-- 解析位点记录 -->
		<property name="logPositionManager">
			<bean class="com.alibaba.otter.canal.parse.index.FailbackLogPositionManager">
				<constructor-arg>
					<bean class="com.alibaba.otter.canal.parse.index.MemoryLogPositionManager" />
				</constructor-arg>
				<constructor-arg>
					<bean class="com.alibaba.otter.canal.parse.index.MetaLogPositionManager">
						<constructor-arg ref="metaManager"/>
					</bean>
				</constructor-arg>
			</bean>
		</property>

		<!-- failover切换时回退的时间 -->
		<property name="fallbackIntervalInSeconds" value="${canal.instance.fallbackIntervalInSeconds:60}" />

		<!-- 解析数据库信息 -->
		<property name="masterInfo">
			<bean class="com.alibaba.otter.canal.parse.support.AuthenticationInfo" init-method="initPwd">
				<property name="address" value="${canal.instance.master.address}" />
				<property name="username" value="${canal.instance.dbUsername:retl}" />
				<property name="password" value="${canal.instance.dbPassword:retl}" />
				<property name="pwdPublicKey" value="${canal.instance.pwdPublicKey:retl}" />
				<property name="enableDruid" value="${canal.instance.enableDruid:false}" />
				<property name="defaultDatabaseName" value="${canal.instance.defaultDatabaseName:}" />
			</bean>
		</property>
		<property name="standbyInfo">
			<bean class="com.alibaba.otter.canal.parse.support.AuthenticationInfo" init-method="initPwd">
				<property name="address" value="${canal.instance.standby.address}" />
				<property name="username" value="${canal.instance.dbUsername:retl}" />
				<property name="password" value="${canal.instance.dbPassword:retl}" />
				<property name="pwdPublicKey" value="${canal.instance.pwdPublicKey:retl}" />
				<property name="enableDruid" value="${canal.instance.enableDruid:false}" />
				<property name="defaultDatabaseName" value="${canal.instance.defaultDatabaseName:}" />
			</bean>
		</property>

		<!-- 解析起始位点 -->
		<property name="masterPosition">
			<bean class="com.alibaba.otter.canal.protocol.position.EntryPosition">
				<property name="journalName" value="${canal.instance.master.journal.name}" />
				<property name="position" value="${canal.instance.master.position}" />
				<property name="timestamp" value="${canal.instance.master.timestamp}" />
				<property name="gtid" value="${canal.instance.master.gtid}" />
			</bean>
		</property>
		<property name="standbyPosition">
			<bean class="com.alibaba.otter.canal.protocol.position.EntryPosition">
				<property name="journalName" value="${canal.instance.standby.journal.name}" />
				<property name="position" value="${canal.instance.standby.position}" />
				<property name="timestamp" value="${canal.instance.standby.timestamp}" />
				<property name="gtid" value="${canal.instance.standby.gtid}" />
			</bean>
		</property>
		<property name="filterQueryDml" value="${canal.instance.filter.query.dml:false}" />
		<property name="filterQueryDcl" value="${canal.instance.filter.query.dcl:false}" />
		<property name="filterQueryDdl" value="${canal.instance.filter.query.ddl:false}" />
		<property name="useDruidDdlFilter" value="${canal.instance.filter.druid.ddl:true}" />
		<property name="filterRows" value="${canal.instance.filter.rows:false}" />
		<property name="filterTableError" value="${canal.instance.filter.table.error:false}" />
		<property name="supportBinlogFormats" value="${canal.instance.binlog.format}" />
		<property name="supportBinlogImages" value="${canal.instance.binlog.image}" />

		<!--表结构相关-->
		<property name="enableTsdb" value="${canal.instance.tsdb.enable:true}"/>
		<property name="tsdbSpringXml" value="${canal.instance.tsdb.spring.xml:}"/>
		<property name="tsdbSnapshotInterval" value="${canal.instance.tsdb.snapshot.interval:24}" />
		<property name="tsdbSnapshotExpire" value="${canal.instance.tsdb.snapshot.expire:360}" />

		<!--是否启用GTID模式-->
		<property name="isGTIDMode" value="${canal.instance.gtidon:false}"/>

		<!-- parallel parser -->
		<property name="parallel" value="${canal.instance.parser.parallel:true}" />
		<property name="parallelThreadSize" value="${canal.instance.parser.parallelThreadSize}" />
		<property name="parallelBufferSize" value="${canal.instance.parser.parallelBufferSize:256}" />
	</bean>

	<bean id="mqConfig" class="com.alibaba.otter.canal.instance.core.CanalMQConfig">
        <property name="topic" value="${canal.mq.topic}" />
		<property name="dynamicTopic" value="${canal.mq.dynamicTopic}" />
        <property name="partition" value="${canal.mq.partition}" />
        <property name="partitionsNum" value="${canal.mq.partitionsNum}" />
        <property name="partitionHash" value="${canal.mq.partitionHash}" />
	</bean>
</beans>
