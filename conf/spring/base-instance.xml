<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:lang="http://www.springframework.org/schema/lang"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd
           http://www.springframework.org/schema/lang http://www.springframework.org/schema/lang/spring-lang-2.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd"
	default-autowire="byName">

	<!-- properties -->
	<bean class="com.alibaba.otter.canal.instance.spring.support.PropertyPlaceholderConfigurer" lazy-init="false">
		<property name="ignoreResourceNotFound" value="true" />
		<property name="systemPropertiesModeName" value="SYSTEM_PROPERTIES_MODE_OVERRIDE"/><!-- 允许system覆盖 -->
		<property name="locationNames">
			<list>
				<value>classpath:canal.properties</value>
				<value>classpath:${canal.instance.destination:}/instance.properties</value>
			</list>
		</property>
	</bean>
	
	<bean id="socketAddressEditor" class="com.alibaba.otter.canal.instance.spring.support.SocketAddressEditor" />
	<bean class="org.springframework.beans.factory.config.CustomEditorConfigurer"> 
		<property name="propertyEditorRegistrars">
			<list>
				<ref bean="socketAddressEditor" />
			</list>
		</property>
	</bean>
	
	<bean id="baseEventParser" class="com.alibaba.otter.canal.parse.inbound.mysql.rds.RdsBinlogEventParserProxy" abstract="true">
		<property name="accesskey" value="${canal.aliyun.accesskey:}" />
		<property name="secretkey" value="${canal.aliyun.secretkey:}" />
		<property name="instanceId" value="${canal.instance.rds.instanceId:}" />
	</bean>
</beans>