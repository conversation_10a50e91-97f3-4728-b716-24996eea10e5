<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>holder-saas-store-reserve</artifactId>
        <groupId>com.holderzone</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>holder-saas-store-reserve-intergration</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>holder-saas-store-reserve-api</artifactId>
        </dependency>
    </dependencies>
</project>