package com.holderzone.saas.store.reserve.intergration.table;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.ReserveBatchCreateOrderReqDTO;
import com.holderzone.saas.store.dto.reserve.UpdateOrderReserveReqDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/9/3
 * @description trade调用
 */
@Component
@FeignClient(value = "holder-saas-store-trade", fallbackFactory = TradeClientService.TradeClientFallBack.class)
public interface TradeClientService {

    /**
     * trade 取消订单
     *
     * @param cancelOrderReqDTO 取消订单请求DTO
     * @return 取消订单结果
     */
    @PostMapping("/dine_in_order/cancel")
    boolean tradeClient(CancelOrderReqDTO cancelOrderReqDTO);

    /**
     * trade 并台请求
     *
     * @param tableOrderCombineDTO 并台请求入参
     * @return 并单返回
     */
    @PostMapping("/dine_in_order/combine")
    boolean notifyTradeCombine(TableOrderCombineDTO tableOrderCombineDTO);

    @PostMapping("/dine_in_order/split")
    boolean split(TableOrderCombineDTO tableOrderCombineDTO);

    @PostMapping("/dine_in_order/create")
    CreateDineInOrderReqDTO openTable(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping("/dine_in_order/transform")
    boolean notifyTradeTurn(TradeTableDTO turnTableDTO);

    @PostMapping("/reserve/batch_create_order")
    String batchCreateOrder(ReserveBatchCreateOrderReqDTO reserveBatchCreateOrderReqDTO);

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/order_detail/find_by_order_guid")
    OrderDTO findByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    /**
     * 根据并台主单guid查询pad下单信息列表
     *
     * @param combineOrderGuid 并台主单guid
     * @return 下单信息列表
     */
    @ApiOperation("根据guid查询pad下单信息")
    @GetMapping(value = "/pad_order/list_pad_order_by_combine_order_guid")
    List<PadOrderRespDTO> listPadOrderByCombineOrderGuid(@RequestParam("combineOrderGuid") String combineOrderGuid);

    /**
     * 根据并台主单guid查询定订单信息列表
     *
     * @param combineOrderGuid 并台主单guid
     * @return 订单信息列表
     */
    @ApiOperation("根据并台主单guid查询定订单信息列表")
    @GetMapping(value = "/order_detail/list_order_by_combine_order_guid")
    List<OrderDTO> listOrderByCombineOrderGuid(@RequestParam("combineOrderGuid") String combineOrderGuid);

    /**
     * 批量撤销第三方活动记录
     *
     * @param singleDataDTO 订单guid列表
     * @return 成功/失败
     */
    @ApiOperation(value = "批量撤销第三方活动记录")
    @PostMapping("/third_activity_record/batch_revoke_third_activity_record")
    Boolean batchRevokeThirdActivityRecord(@RequestBody SingleDataDTO singleDataDTO);

    @ApiOperation(value = "查询订单中待支付的订单")
    @PostMapping("/order_detail/query_ready_order")
    List<String> queryReadyOrder(@RequestBody SingleListDTO singleListDTO);

    @ApiOperation(value = "更新订单预定信息")
    @PostMapping("/order_detail/update_order_reserve")
    void updateOrderReserve(@RequestBody UpdateOrderReserveReqDTO reqDTO);

    @Component
    @Slf4j
    class TradeClientFallBack implements FallbackFactory<TradeClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TradeClientService create(Throwable throwable) {
            return new TradeClientService() {

                @Override
                public boolean notifyTradeTurn(TradeTableDTO turnTableDTO) {
                    log.error("调用订单服务转台失败 e={} turnTableDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(turnTableDTO));
                    return false;
                }

                @Override
                public String batchCreateOrder(ReserveBatchCreateOrderReqDTO reserveBatchCreateOrderReqDTO) {
                    log.error("调用订单服务批量开单失败 e={} turnTableDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(reserveBatchCreateOrderReqDTO));
                    throw new BusinessException("调用订单服务批量开单失败");
                }

                @Override
                public CreateDineInOrderReqDTO openTable(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("调用订单服务开台失败 e={} createDineInOrderReqDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(createDineInOrderReqDTO));
                    throw new BusinessException("调用订单服务开台失败");
                }

                @Override
                public boolean tradeClient(CancelOrderReqDTO cancelOrderReqDTO) {
                    log.error("拆单异常 e={}", throwable.getMessage());
                    return false;
                }

                @Override
                public boolean split(TableOrderCombineDTO tableOrderCombineDTO) {
                    log.error("取消订单异常 e={}", throwable.getMessage());
                    return false;
                }

                @Override
                public boolean notifyTradeCombine(TableOrderCombineDTO tableOrderCombineDTO) {
                    log.error("通知订单服务并桌异常 e={}", throwable.getMessage());
                    //   throw new BusinessException(throwable.getMessage());
                    return false;
                }

                @Override
                public OrderDTO findByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "findByOrderGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PadOrderRespDTO> listPadOrderByCombineOrderGuid(String combineOrderGuid) {
                    log.error(HYSTRIX_PATTERN, "listPadOrderByCombineOrderGuid", combineOrderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OrderDTO> listOrderByCombineOrderGuid(String combineOrderGuid) {
                    log.error(HYSTRIX_PATTERN, "listOrderByCombineOrderGuid", combineOrderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean batchRevokeThirdActivityRecord(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "batchRevokeThirdActivityRecord", JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<String> queryReadyOrder(SingleListDTO singleListDTO) {
                    log.error(HYSTRIX_PATTERN, "queryReadyOrder", JacksonUtils.writeValueAsString(singleListDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateOrderReserve(UpdateOrderReserveReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateOrderReserve", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
