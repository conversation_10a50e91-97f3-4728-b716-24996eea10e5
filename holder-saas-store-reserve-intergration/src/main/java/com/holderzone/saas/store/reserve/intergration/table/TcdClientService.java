package com.holderzone.saas.store.reserve.intergration.table;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.ShopOrderIslandUserAmountDTO;
import com.holderzone.saas.store.dto.zhuancan.ZhuanCanReserveMerchantDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;


/**
 * 赚餐调用
 */
@Component
@FeignClient(name = "holder-zhuancan", fallbackFactory = TcdClientService.ServiceFallback.class, url = "${zhuancan.host}")
public interface TcdClientService {

    @PostMapping("/shop/api/order-resolve/island-user-amount-refund")
    Map<String, Object> islandUserAmountRefund(@RequestBody ShopOrderIslandUserAmountDTO shopOrderIslandUserAmountDTO);

    @GetMapping("/merchant/api/merchants-reserve/get_reserve_merchant_by_third_no")
    ZhuanCanReserveMerchantDTO getReserveMerchantByThirdNo(@RequestParam("thirdNo") String thirdNo);

    @Slf4j
    @Component
    class ServiceFallback implements FallbackFactory<TcdClientService> {
        @Override
        public TcdClientService create(Throwable throwable) {
            return new TcdClientService() {

                @Override
                public Map<String, Object> islandUserAmountRefund(ShopOrderIslandUserAmountDTO shopOrderIslandUserAmountDTO) {
                    log.error("收益余额扣款失败,入参: {}", JacksonUtils.writeValueAsString(shopOrderIslandUserAmountDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ZhuanCanReserveMerchantDTO getReserveMerchantByThirdNo(String thirdNo) {
                    log.error("查询赚餐预定门店信息失败,入参: {}", thirdNo);
                    throw new BusinessException(throwable.getMessage());
                }

            };
        }
    }

}
