package com.holderzone.saas.store.reserve.intergration.table;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.table.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
@FeignClient(value = "holder-saas-store-table", fallbackFactory = TableClientService.TableClientServiceFallback.class)
public interface TableClientService {
    @PostMapping("/table/reserve/open")
    String open(OpenTableDTO dto);
    @PostMapping("/table/reserve/cancle")
    List<String> cancleReserveLock(List<String> tableGuids);
    @PostMapping("/table/reserve")
    List<String> reserveLock(List<String> tableGuids);
    @PostMapping("/table/reserve/combine")
    List<String> combine(TableCombineDTO dto);
    @PostMapping("/table//web/query")
    List<TableBasicDTO> listByWeb(@RequestBody TableBasicQueryDTO tableBasicQueryDTO);
    @PostMapping("/table/android/query")
    List<TableOrderDTO> listByAndroid(@RequestBody TableBasicQueryDTO tableBasicQueryDTO);
    @PostMapping("/table/query/all/{storeGuid}")
    List<AreaDTO> query(@PathVariable("storeGuid") String storeGuid);
    @PostMapping("/table/reserve/prepare")
    void prepare(@RequestBody ReservePreparedDTO dto);
    @ApiOperation("开台")
    @PostMapping("/table/open/batch")
     ReserveOpenTableDTO batch(@RequestBody BatchOpenTableDTO batchOpenTableDTO);
    @ApiOperation("关台")
    @PostMapping("/table/close")
    public boolean close(@RequestBody CancelOrderReqDTO cancelOrderReqDTO);

    @ApiOperation("批量查询桌台区域列表")
    @PostMapping("/area/batch_query/all")
    List<AreaDTO> batchQueryArea(@RequestBody SingleDataDTO singleDataDTO);

    @ApiOperation("发送桌台状态变化消息")
    @PostMapping("/table/send_table_change_msg")
    void sendTableChangeMsg(@RequestBody SingleDataDTO singleDataDTO);


    @Slf4j
    @Component
    class TableClientServiceFallback  implements FallbackFactory<TableClientService>{
        @Override
        public TableClientService create(Throwable throwable) {
            return new TableClientService() {

                @Override
                public ReserveOpenTableDTO batch(BatchOpenTableDTO batchOpenTableDTO) {
                    log.error("batch fail with param: {}",batchOpenTableDTO);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public boolean close(CancelOrderReqDTO cancelOrderReqDTO) {
                    log.error("close fail with param: {}",cancelOrderReqDTO);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<AreaDTO> batchQueryArea(SingleDataDTO singleDataDTO) {
                    log.error("batch query area fail with param: {}", JacksonUtils.writeValueAsString(singleDataDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public void sendTableChangeMsg(SingleDataDTO singleDataDTO) {
                    log.error("send table change msg fail with param: {}", JacksonUtils.writeValueAsString(singleDataDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public void prepare(@RequestBody ReservePreparedDTO dto) {
                    log.error("prepare fail with param: {}",dto);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<AreaDTO> query(String storeGuid) {
                    log.error("query fail with param: {}",storeGuid);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public String open(OpenTableDTO dto) {
                    log.error("open fail with param: {}",dto);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<TableBasicDTO> listByWeb(TableBasicQueryDTO tableBasicQueryDTO) {
                    log.error("listByAndroid fail with param: {}",tableBasicQueryDTO);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<TableOrderDTO> listByAndroid(TableBasicQueryDTO tableBasicQueryDTO) {
                    log.error("listByAndroid fail with param: {}",tableBasicQueryDTO);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<String> combine(TableCombineDTO dto) {
                    log.error("combine fail with param: {}",dto);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<String> reserveLock(List<String> tableGuids) {
                    log.error("reserveLock fail with param: {}",tableGuids);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<String> cancleReserveLock(List<String> tableGuids) {
                    log.error("reserveLock fail with param: {}",tableGuids);
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
