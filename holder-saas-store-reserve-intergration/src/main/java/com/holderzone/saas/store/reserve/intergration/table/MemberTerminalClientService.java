package com.holderzone.saas.store.reserve.intergration.table;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestConfirmPay;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestRefundPay;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseConfirmPay;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Component
@FeignClient(value = "holder-saas-member-terminal", fallbackFactory = MemberTerminalClientService.MemberTerminalClientServiceFallback.class)
public interface MemberTerminalClientService {

    /**
     * 预定单支付
     */
    @PostMapping("/hsmca/order/reserve_pay")
    ResponseConfirmPay reservePay(@RequestBody RequestConfirmPay request);

    /**
     * 预定单支付退款
     */
    @PostMapping("/hsmca/order/reserve_pay/refund")
    void reserveRefundPay(@RequestBody RequestRefundPay refundPay);


    @Slf4j
    @Component
    class MemberTerminalClientServiceFallback implements FallbackFactory<MemberTerminalClientService> {
        @Override
        public MemberTerminalClientService create(Throwable throwable) {
            return new MemberTerminalClientService() {

                @Override
                public ResponseConfirmPay reservePay(RequestConfirmPay request) {
                    log.error("reserve pay fail with param: {}", request);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public void reserveRefundPay(RequestRefundPay refundPay) {
                    log.error("reserve refund pay fail with param: {}", refundPay);
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
