package com.holderzone.saas.store.reserve.intergration.table;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.pay.AggRefundRespDTO;
import com.holderzone.saas.store.dto.pay.SaasAggPayDTO;
import com.holderzone.saas.store.dto.pay.SaasAggRefundDTO;
import feign.hystrix.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayClientService
 * @date 2018/09/06 19:33
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(value = "holder-saas-store-pay", fallbackFactory = AggPayClientService.SaasAggPayClientFallBack.class)
public interface AggPayClientService {

    @PostMapping("agg/pay")
    AggPayRespDTO pay(@RequestBody SaasAggPayDTO saasAggPayDTO);

    @PostMapping("agg/refund")
    AggRefundRespDTO refund(@RequestBody SaasAggRefundDTO saasAggRefundDTO);

    @Component
    class SaasAggPayClientFallBack implements FallbackFactory<AggPayClientService> {
        @Override
        public AggPayClientService create(Throwable throwable) {
            return new AggPayClientService() {
                @Override
                public AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO) {
                    throw new BusinessException("调用交易中心异常");
                }

                @Override
                public AggRefundRespDTO refund(SaasAggRefundDTO saasAggRefundDTO) {
                    throw new BusinessException("调用交易中心异常");
                }
            };
        }
    }

}
