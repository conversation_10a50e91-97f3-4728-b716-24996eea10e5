package com.holderzone.saas.store.reserve.intergration.table;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrganizationClientService
 * @date 2019/02/18 10:30
 * @description //TODO
 * @program ${MODULE_NAME}
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationClientService.OrganizationFullback.class)
public interface OrganizationClientService {

    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据门店guid查询主机deviceId
     */
    @GetMapping("/device/get_master_device_by_storeguid/{storeGuid}")
    StoreDeviceDTO findMasterDevice(@PathVariable("storeGuid") String storeGuid);


    @Component
    @Slf4j
    class OrganizationFullback implements FallbackFactory<OrganizationClientService> {
        @Override
        public OrganizationClientService create(Throwable throwable) {
            return new OrganizationClientService() {
                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    log.error("通过门店guid查询门店失败,",throwable);
                    throw new RuntimeException("通过门店guid查询门店失败,e:{}" + throwable.getMessage());
                }

                @Override
                public StoreDeviceDTO findMasterDevice(String storeGuid) {
                    log.error("通过门店guid查询门店失败,", throwable);
                    throw new BusinessException("通过门店guid查询门店失败,e:{}" + throwable.getMessage());
                }
            };
        }
    }
}
