微信单张券：
过程：只计算，c端保存，支付再验券
问题：进入支付页，回退再次进入，报重复验券的错误，只能在一体机撤销后，再次走支付流程
	  验券后，不支付，回退到，优惠券列表，不能二次选券
	  只计算，同时一体机验券，金额可能不一致，导致无法支付，形成死循环
	  只计算，同时一体机验券，金额一致，但实际上券数量是错的，支付成功
	  只计算，然后一体机验券，可能是券与券冲突的，验券失败，死循环
微信多张券：
走同样验券流程：列表点击确定后，验券，能解决多端冲突，且金额一致，支付流程正常
问题：用户在列表选券后，由于不提供撤销功能，再次进入不能修改，且无法查看用户的已验券
解决方案：
	1.列表每次都撤销掉，用户所选券，就能修改，问题：第二次进入不点确定，也撤了，相当于没选
	2.点击确定的时候撤，由于看不到已验券列表，需要在微信本地存一份用户的优惠券列表id，第二次查询已验券列表相关信息
	问题：列表加载时间很长，接口调用太多，点确定时间长，逻辑复杂，不好维护，藕合性强

走只计算流程：
支付才验券，但不支付，回退，出现类似单张券问题，
买单页与优惠券同时存在，多人支付时,多个用户与一体机同时验券与计算的问题，微信端死循环，无法支付，一体机结账会用掉所有券

隔离各个端支付流程，改动大





现状冲突的地方，一个订单支持多个端结算，但计算和验券只支持一个端，以最后一个端更新的数据为准；但微信需要多个端进行结算，看到各自的结算信息；叠加券功能支持后，问题更加突出