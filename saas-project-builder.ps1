# SaaS Project Management Script
# This script combines branch switching and build functions

# Statistics variables
$totalProjects = 0
$successProjects = 0
$failedProjects = @()
$skippedProjects = @()

# Function to get all project directories
function Get-ProjectDirectories {
    return Get-ChildItem -Directory | Where-Object { $_.Name -like "holder-saas-*" }
}

# Function to display menu
function Show-MainMenu {
    Write-Host ""
    Write-Host "=====================================================" -ForegroundColor Cyan
    Write-Host "SaaS Project Management Tool" -ForegroundColor Cyan
    Write-Host "=====================================================" -ForegroundColor Cyan
    Write-Host "1. Switch all projects to develop branch" -ForegroundColor White
    Write-Host "2. Build all projects (batch mode)" -ForegroundColor White
    Write-Host "3. Build projects one by one (interactive)" -ForegroundColor White
    Write-Host "4. Build specific project or module" -ForegroundColor White
    Write-Host "5. Exit" -ForegroundColor White
    Write-Host "-----------------------------------------------------" -ForegroundColor Cyan
    $choice = Read-Host "Enter your choice (1-5)"
    return $choice
}

# Function to switch all projects to develop branch
function Switch-AllToDevelop {
    $dirs = Get-ProjectDirectories
    $totalDirs = $dirs.Count
    
    Write-Host "=====================================================" -ForegroundColor Cyan
    Write-Host "Switching all projects to develop branch (Total: $totalDirs)" -ForegroundColor Cyan
    Write-Host "=====================================================" -ForegroundColor Cyan
    
    $counter = 0
    
    foreach ($dir in $dirs) {
        $counter++
        Write-Host "[$counter/$totalDirs] Processing project: $($dir.Name)" -ForegroundColor Green
        
        # Enter project directory
        Push-Location $dir.FullName
        
        try {
            # Check if it's a Git repo
            $isGitRepo = Test-Path ".git"
            
            if ($isGitRepo) {
                # Get current branch
                $currentBranch = git rev-parse --abbrev-ref HEAD
                Write-Host "  Current branch: $currentBranch" -ForegroundColor Cyan
                
                # Save uncommitted changes
                $status = git status --porcelain
                if ($status) {
                    Write-Host "  Uncommitted changes found, saving to stash" -ForegroundColor Yellow
                    git stash save "Auto saved before switching to develop: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
                }
                
                # Fetch all remote updates
                Write-Host "  Fetching remote updates..." -ForegroundColor Cyan
                git fetch --all
                
                # Check if remote develop branch exists
                $remoteBranchExists = git ls-remote --heads origin develop
                
                if ($remoteBranchExists) {
                    # Check if local develop branch exists
                    git show-ref --verify --quiet refs/heads/develop
                    
                    if ($LASTEXITCODE -eq 0) {
                        # Local develop branch exists, switch and pull
                        Write-Host "  Switching to local develop branch and updating" -ForegroundColor Cyan
                        git checkout develop
                        git pull origin develop
                    } else {
                        # Local develop branch doesn't exist, checkout directly
                        Write-Host "  Checking out develop branch from remote" -ForegroundColor Cyan
                        git checkout -b develop origin/develop
                    }
                    Write-Host "  Successfully switched to develop branch" -ForegroundColor Green
                } else {
                    Write-Host "  No remote develop branch found, skipping" -ForegroundColor Red
                }
            } else {
                Write-Host "  Not a Git repository, skipping" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "  Error occurred: $_" -ForegroundColor Red
        }
        finally {
            # Return to parent directory
            Pop-Location
        }
        
        Write-Host "------------------------"
    }
    
    Write-Host "All projects processed!" -ForegroundColor Green
}

# Function to build all projects in batch mode
function Build-AllProjects {
    $global:totalProjects = 0
    $global:successProjects = 0
    $global:failedProjects = @()
    $global:skippedProjects = @()
    
    $dirs = Get-ProjectDirectories
    $totalDirs = $dirs.Count
    
    # Start time
    $startTime = Get-Date
    
    Write-Host "=====================================================" -ForegroundColor Cyan
    Write-Host "Starting build for all projects (Total: $totalDirs)" -ForegroundColor Cyan
    Write-Host "=====================================================" -ForegroundColor Cyan
    
    Write-Host "Choose build options:" -ForegroundColor Cyan
    Write-Host "1. Standard build (clean package -DskipTests)" -ForegroundColor White
    Write-Host "2. Compile only (compile -DskipTests)" -ForegroundColor White
    Write-Host "3. Install (clean install -DskipTests)" -ForegroundColor White
    $buildOption = Read-Host "Enter option (1-3)"
    
    switch ($buildOption) {
        "1" { $mavenCommand = "mvn clean package -DskipTests '-Dmaven.test.skip=true' '-Dmaven.test.execution.skip=true' '-Dskip.surefire.tests' '-Dskip.failsafe.tests'" }
        "2" { $mavenCommand = "mvn clean compile -DskipTests '-Dmaven.test.skip=true'" }
        "3" { $mavenCommand = "mvn clean install -DskipTests '-Dmaven.test.skip=true' '-Dmaven.test.execution.skip=true' '-Dskip.surefire.tests' '-Dskip.failsafe.tests'" }
        default { $mavenCommand = "mvn clean package -DskipTests '-Dmaven.test.skip=true' '-Dmaven.test.execution.skip=true' '-Dskip.surefire.tests' '-Dskip.failsafe.tests'" }
    }
    
    foreach ($dir in $dirs) {
        $global:totalProjects++
        $projectName = $dir.Name
        
        Write-Host ""
        Write-Host "[$global:totalProjects/$totalDirs] Building project: $projectName" -ForegroundColor Yellow
        Write-Host "-----------------------------------------------------" -ForegroundColor Yellow
        
        # Enter project directory
        Push-Location $dir.FullName
        
        try {
            # Check if it's a Maven project (pom.xml exists)
            if (Test-Path "pom.xml") {
                Write-Host "Running: $mavenCommand" -ForegroundColor Cyan
                
                # Execute Maven command and capture exit code
                Invoke-Expression $mavenCommand
                $exitCode = $LASTEXITCODE
                
                if ($exitCode -eq 0) {
                    Write-Host "Project '$projectName' built successfully!" -ForegroundColor Green
                    $global:successProjects++
                } else {
                    Write-Host "Project '$projectName' build failed! (Exit code: $exitCode)" -ForegroundColor Red
                    $global:failedProjects += $projectName
                }
            } else {
                Write-Host "Project '$projectName' is not a Maven project (no pom.xml), skipping" -ForegroundColor DarkYellow
                $global:skippedProjects += $projectName
            }
        }
        catch {
            Write-Host "Error processing project '$projectName': $_" -ForegroundColor Red
            $global:failedProjects += $projectName
        }
        finally {
            # Return to parent directory
            Pop-Location
        }
        
        Write-Host "-----------------------------------------------------" -ForegroundColor Yellow
    }
    
    # End time and duration
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    Show-BuildSummary $startTime $duration
}

# Function to build projects one by one with user interaction
function Build-ProjectsOneByOne {
    $global:totalProjects = 0
    $global:successProjects = 0
    $global:failedProjects = @()
    $global:skippedProjects = @()
    
    $dirs = Get-ProjectDirectories
    $totalDirs = $dirs.Count
    
    # Start time
    $startTime = Get-Date
    
    Write-Host "=====================================================" -ForegroundColor Cyan
    Write-Host "Starting build for each project separately (Total: $totalDirs)" -ForegroundColor Cyan
    Write-Host "=====================================================" -ForegroundColor Cyan
    
    # Process each project
    foreach ($dir in $dirs) {
        $global:totalProjects++
        $projectName = $dir.Name
        
        Write-Host ""
        Write-Host "[$global:totalProjects/$totalDirs] Building project: $projectName" -ForegroundColor Yellow
        Write-Host "-----------------------------------------------------" -ForegroundColor Yellow
        
        # Confirm whether to build this project
        $buildConfirm = Read-Host "Build this project? (Y/N/Skip)"
        
        if ($buildConfirm -eq "Skip") {
            Write-Host "Skipping all remaining projects" -ForegroundColor Yellow
            $global:skippedProjects += $projectName
            $global:skippedProjects += $dirs | Where-Object { $_.Name -ne $projectName -and $global:totalProjects -lt [array]::IndexOf($dirs, $_) + 1 } | Select-Object -ExpandProperty Name
            break
        }
        elseif ($buildConfirm -ne "Y") {
            Write-Host "Skipping project: $projectName" -ForegroundColor Yellow
            $global:skippedProjects += $projectName
            continue
        }
        
        # Enter project directory
        Push-Location $dir.FullName
        
        try {
            # Check if it's a Maven project (pom.xml exists)
            if (Test-Path "pom.xml") {
                $continueWithBuild = $true
                $retryCount = 0
                $maxRetries = 2
                
                while ($continueWithBuild -and $retryCount -le $maxRetries) {
                    if ($retryCount -gt 0) {
                        Write-Host "Retry attempt $retryCount of $maxRetries" -ForegroundColor Yellow
                    }
                    
                    # Ask for Maven command options
                    if ($retryCount -eq 0) {
                        $mavenCommand = "mvn clean package -DskipTests '-Dmaven.test.skip=true' '-Dmaven.test.execution.skip=true' '-Dskip.surefire.tests' '-Dskip.failsafe.tests'"
                    } else {
                        Write-Host "Choose build option:" -ForegroundColor Cyan
                        Write-Host "1. Standard build (clean package -DskipTests)" -ForegroundColor White
                        Write-Host "2. Compile only (compile -DskipTests)" -ForegroundColor White
                        Write-Host "3. Custom command" -ForegroundColor White
                        $option = Read-Host "Enter option (1-3)"
                        
                        switch ($option) {
                            "1" { $mavenCommand = "mvn clean package -DskipTests '-Dmaven.test.skip=true' '-Dmaven.test.execution.skip=true' '-Dskip.surefire.tests' '-Dskip.failsafe.tests'" }
                            "2" { $mavenCommand = "mvn compile -DskipTests '-Dmaven.test.skip=true'" }
                            "3" { 
                                $customCommand = Read-Host "Enter custom Maven command"
                                $mavenCommand = "mvn $customCommand"
                            }
                            default { $mavenCommand = "mvn clean package -DskipTests '-Dmaven.test.skip=true' '-Dmaven.test.execution.skip=true' '-Dskip.surefire.tests' '-Dskip.failsafe.tests'" }
                        }
                    }
                    
                    Write-Host "Running: $mavenCommand" -ForegroundColor Cyan
                    
                    # Execute Maven command and capture exit code
                    Invoke-Expression $mavenCommand
                    $exitCode = $LASTEXITCODE
                    
                    if ($exitCode -eq 0) {
                        Write-Host "Project '$projectName' built successfully!" -ForegroundColor Green
                        $global:successProjects++
                        $continueWithBuild = $false
                    } else {
                        Write-Host "Project '$projectName' build failed! (Exit code: $exitCode)" -ForegroundColor Red
                        
                        if ($retryCount -lt $maxRetries) {
                            $retryOption = Read-Host "Retry with different options? (Y/N/Skip)"
                            
                            if ($retryOption -eq "Y") {
                                $retryCount++
                            } 
                            elseif ($retryOption -eq "Skip") {
                                Write-Host "Skipping project after failed attempt" -ForegroundColor Yellow
                                $global:skippedProjects += $projectName
                                $continueWithBuild = $false
                            }
                            else {
                                $global:failedProjects += $projectName
                                $continueWithBuild = $false
                            }
                        } else {
                            Write-Host "Maximum retry attempts reached" -ForegroundColor Red
                            $global:failedProjects += $projectName
                            $continueWithBuild = $false
                        }
                    }
                }
            } else {
                Write-Host "Project '$projectName' is not a Maven project (no pom.xml), skipping" -ForegroundColor DarkYellow
                $global:skippedProjects += $projectName
            }
        }
        catch {
            Write-Host "Error processing project '$projectName': $_" -ForegroundColor Red
            $global:failedProjects += $projectName
        }
        finally {
            # Return to parent directory
            Pop-Location
        }
        
        Write-Host "-----------------------------------------------------" -ForegroundColor Yellow
    }
    
    # End time and duration
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    Show-BuildSummary $startTime $duration
}

# Function to build a specific project or module
function Build-SpecificProject {
    # Get all directories
    $dirs = Get-ProjectDirectories
    
    Write-Host "=====================================================" -ForegroundColor Cyan
    Write-Host "Build specific project or module" -ForegroundColor Cyan
    Write-Host "=====================================================" -ForegroundColor Cyan
    
    # Choose modules to build
    Write-Host "Choose how to proceed:" -ForegroundColor Cyan
    Write-Host "1. Enter project name" -ForegroundColor White
    Write-Host "2. List all available projects" -ForegroundColor White
    $choice = Read-Host "Enter option (1-2)"
    
    if ($choice -eq "1") {
        $projectName = Read-Host "Enter project name (e.g., holder-saas-store-client)"
        $selectedDirs = $dirs | Where-Object { $_.Name -eq $projectName }
        
        if ($selectedDirs.Count -eq 0) {
            Write-Host "No matching project found with name: $projectName" -ForegroundColor Red
            return
        }
        $selectedProject = $selectedDirs[0]
    } elseif ($choice -eq "2") {
        Write-Host ""
        Write-Host "Available projects:" -ForegroundColor Cyan
        $counter = 1
        foreach ($dir in $dirs) {
            Write-Host "$counter. $($dir.Name)" -ForegroundColor White
            $counter++
        }
        
        $selection = Read-Host "Enter project number to build (1-$($dirs.Count))"
        
        try {
            $index = [int]$selection - 1
            if ($index -ge 0 -and $index -lt $dirs.Count) {
                $selectedProject = $dirs[$index]
            } else {
                Write-Host "Invalid selection" -ForegroundColor Red
                return
            }
        } catch {
            Write-Host "Invalid input" -ForegroundColor Red
            return
        }
    } else {
        Write-Host "Invalid option" -ForegroundColor Red
        return
    }
    
    # Selected project
    Write-Host ""
    Write-Host "Selected project: $($selectedProject.Name)" -ForegroundColor Green
    Write-Host ""
    
    # Start time
    $startTime = Get-Date
    
    # Enter project directory
    Push-Location $selectedProject.FullName
    
    try {
        # Check if it's a Maven project
        if (Test-Path "pom.xml") {
            # Choose build options
            Write-Host "Choose build option:" -ForegroundColor Cyan
            Write-Host "1. Clean and package (skip tests)" -ForegroundColor White
            Write-Host "2. Compile only" -ForegroundColor White
            Write-Host "3. Install (skip tests)" -ForegroundColor White
            Write-Host "4. Custom Maven command" -ForegroundColor White
            $option = Read-Host "Enter option (1-4)"
            
            switch ($option) {
                "1" { $mavenCommand = "mvn clean package -DskipTests '-Dmaven.test.skip=true' '-Dmaven.test.execution.skip=true' '-Dskip.surefire.tests' '-Dskip.failsafe.tests'" }
                "2" { $mavenCommand = "mvn clean compile -DskipTests '-Dmaven.test.skip=true'" }
                "3" { $mavenCommand = "mvn clean install -DskipTests '-Dmaven.test.skip=true' '-Dmaven.test.execution.skip=true' '-Dskip.surefire.tests' '-Dskip.failsafe.tests'" }
                "4" { 
                    $customCommand = Read-Host "Enter custom Maven command"
                    $mavenCommand = "mvn $customCommand"
                }
                default { $mavenCommand = "mvn clean package -DskipTests '-Dmaven.test.skip=true' '-Dmaven.test.execution.skip=true' '-Dskip.surefire.tests' '-Dskip.failsafe.tests'" }
            }
            
            # Process modules
            Write-Host ""
            Write-Host "Checking for modules..." -ForegroundColor Cyan
            
            # Try to get module list using Maven help
            mvn help:evaluate -Dexpression=project.modules -q -DforceStdout | Out-Null
            if ($LASTEXITCODE -eq 0) {
                $modules = mvn help:evaluate -Dexpression=project.modules -q -DforceStdout
                
                if ($modules -and $modules -ne "[]") {
                    Write-Host "Found multi-module project!" -ForegroundColor Green
                    
                    # Extract modules from XML-like output
                    $modulesList = $modules -replace '[\[\]]', '' -split ',' | ForEach-Object { $_.Trim() }
                    
                    Write-Host "Available modules:" -ForegroundColor Cyan
                    $counter = 1
                    foreach ($module in $modulesList) {
                        Write-Host "$counter. $module" -ForegroundColor White
                        $counter++
                    }
                    
                    Write-Host ""
                    Write-Host "Choose how to build:" -ForegroundColor Cyan
                    Write-Host "1. Build specific module" -ForegroundColor White
                    Write-Host "2. Build all modules" -ForegroundColor White
                    $moduleOption = Read-Host "Enter option (1-2)"
                    
                    if ($moduleOption -eq "1") {
                        $moduleSelection = Read-Host "Enter module number to build (1-$($modulesList.Count))"
                        
                        try {
                            $moduleIndex = [int]$moduleSelection - 1
                            if ($moduleIndex -ge 0 -and $moduleIndex -lt $modulesList.Count) {
                                $selectedModule = $modulesList[$moduleIndex]
                                $mavenCommand = "$mavenCommand -pl :$selectedModule -am"
                            } else {
                                Write-Host "Invalid module selection, building all modules" -ForegroundColor Yellow
                            }
                        } catch {
                            Write-Host "Invalid input, building all modules" -ForegroundColor Yellow
                        }
                    }
                } else {
                    Write-Host "No modules found, treating as single module project" -ForegroundColor Yellow
                }
            } else {
                Write-Host "Unable to detect modules, treating as single module project" -ForegroundColor Yellow
            }
            
            # Execute build
            Write-Host ""
            Write-Host "Running: $mavenCommand" -ForegroundColor Cyan
            Write-Host "Starting build process..." -ForegroundColor Yellow
            
            Invoke-Expression $mavenCommand
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host ""
                Write-Host "Build completed successfully!" -ForegroundColor Green
                $global:successProjects++
            } else {
                Write-Host ""
                Write-Host "Build failed with exit code: $LASTEXITCODE" -ForegroundColor Red
                $global:failedProjects += $selectedProject.Name
            }
        } else {
            Write-Host "Not a Maven project (no pom.xml found)" -ForegroundColor Red
            $global:skippedProjects += $selectedProject.Name
        }
    }
    catch {
        Write-Host "Error: $_" -ForegroundColor Red
        $global:failedProjects += $selectedProject.Name
    }
    finally {
        # Return to parent directory
        Pop-Location
    }
    
    # End time and duration
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    $global:totalProjects = 1
    Show-BuildSummary $startTime $duration
}

# Function to show build summary
function Show-BuildSummary($startTime, $duration) {
    Write-Host ""
    Write-Host "=====================================================" -ForegroundColor Cyan
    Write-Host "Build Summary:" -ForegroundColor Cyan
    Write-Host "-----------------------------------------------------" -ForegroundColor Cyan
    Write-Host "Total projects: $global:totalProjects" -ForegroundColor White
    Write-Host "Successful: $global:successProjects" -ForegroundColor Green
    Write-Host "Failed: $($global:failedProjects.Count)" -ForegroundColor $(if ($global:failedProjects.Count -gt 0) { "Red" } else { "Green" })
    Write-Host "Skipped: $($global:skippedProjects.Count)" -ForegroundColor Yellow
    Write-Host "Total duration: $($duration.Hours)h $($duration.Minutes)m $($duration.Seconds)s" -ForegroundColor Cyan
    Write-Host "=====================================================" -ForegroundColor Cyan
    
    # If there are failed projects, display them
    if ($global:failedProjects.Count -gt 0) {
        Write-Host ""
        Write-Host "Failed projects:" -ForegroundColor Red
        foreach ($failedProject in $global:failedProjects) {
            Write-Host "- $failedProject" -ForegroundColor Red
        }
    }
    
    # If there are skipped projects, display them
    if ($global:skippedProjects.Count -gt 0) {
        Write-Host ""
        Write-Host "Skipped projects:" -ForegroundColor Yellow
        foreach ($skippedProject in $global:skippedProjects) {
            Write-Host "- $skippedProject" -ForegroundColor Yellow
        }
    }
}

# Main script execution
do {
    $choice = Show-MainMenu
    
    switch ($choice) {
        "1" { Switch-AllToDevelop }
        "2" { Build-AllProjects }
        "3" { Build-ProjectsOneByOne }
        "4" { Build-SpecificProject }
        "5" { exit }
        default { Write-Host "Invalid option, please try again" -ForegroundColor Red }
    }
    
    Write-Host ""
    $continue = Read-Host "Return to main menu? (Y/N)"
} while ($continue -eq "Y") 