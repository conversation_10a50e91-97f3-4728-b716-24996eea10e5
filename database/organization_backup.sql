-- MySQL dump 10.16  Distrib 10.1.29-MariaDB, for debian-linux-gnu (x86_64)
--
-- Host: ***************    Database: hss_store_6490862829263766529_db
-- ------------------------------------------------------
-- Server version	5.7.20-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `hso_brand`
--

DROP TABLE IF EXISTS `hso_brand`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hso_brand` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(45) COLLATE utf8_bin NOT NULL,
  `name` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '品牌名称',
  `description` varchar(200) COLLATE utf8_bin DEFAULT NULL,
  `logo_url` varchar(100) COLLATE utf8_bin DEFAULT NULL,
  `is_enable` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否启用（默认为1-已启用）',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除（默认为0-未删除）',
  `create_user_guid` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '创建人guid',
  `modified_user_guid` varchar(45) COLLATE utf8_bin NOT NULL,
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='品牌表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hso_organization`
--

DROP TABLE IF EXISTS `hso_organization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hso_organization` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(45) COLLATE utf8_bin NOT NULL COMMENT 'guid',
  `code` varchar(45) COLLATE utf8_bin DEFAULT NULL COMMENT '门店编码',
  `type` tinyint(4) NOT NULL COMMENT '类型（1-组织，2-门店）',
  `name` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '名称',
  `contact_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '联系人姓名',
  `contact_tel` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '联系人电话',
  `parent_ids` varchar(200) COLLATE utf8_bin DEFAULT NULL COMMENT '上级组织id（由最上级组织到直属上级的guid组成，逗号隔开）',
  `province_code` varchar(45) COLLATE utf8_bin DEFAULT NULL COMMENT '省份code',
  `province_name` varchar(45) COLLATE utf8_bin DEFAULT NULL COMMENT '省份名称',
  `city_code` varchar(45) COLLATE utf8_bin DEFAULT NULL COMMENT '城市code',
  `city_name` varchar(45) COLLATE utf8_bin DEFAULT NULL COMMENT '城市名称',
  `county_code` varchar(45) COLLATE utf8_bin DEFAULT NULL COMMENT '区县code',
  `county_name` varchar(45) COLLATE utf8_bin DEFAULT NULL COMMENT '区县名称',
  `address_detail` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '详细地址',
  `description` varchar(200) COLLATE utf8_bin DEFAULT NULL COMMENT '描述',
  `icon` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '图标',
  `is_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用（默认为1-启用）',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除（默认为0-未删除）',
  `business_start` time DEFAULT NULL COMMENT '门店营业开始时间',
  `business_end` time DEFAULT NULL COMMENT '门店营业结束时间',
  `longitude` varchar(45) COLLATE utf8_bin DEFAULT NULL COMMENT '经度',
  `latitude` varchar(45) COLLATE utf8_bin DEFAULT NULL COMMENT '纬度',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `create_user_guid` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '创建人guid',
  `modified_user_guid` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '修改人guid',
  PRIMARY KEY (`id`),
  UNIQUE KEY `guid_UNIQUE` (`guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='组织表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hso_r_store_brand`
--

DROP TABLE IF EXISTS `hso_r_store_brand`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hso_r_store_brand` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(45) COLLATE utf8_bin NOT NULL COMMENT 'guid',
  `store_guid` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '门店guid',
  `brand_guid` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '品牌guid',
  `create_user_guid` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '创建人guid',
  `modified_user_guid` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '更新人guid',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `guid_UNIQUE` (`guid`),
  KEY `idx_brand_guid` (`brand_guid`),
  KEY `idx_store_guid` (`store_guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='门店-品牌关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hso_r_store_device`
--

DROP TABLE IF EXISTS `hso_r_store_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hso_r_store_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增Id',
  `guid` varchar(45) NOT NULL COMMENT 'guid',
  `store_no` varchar(45) NOT NULL COMMENT '门店编号',
  `store_guid` varchar(45) NOT NULL COMMENT '门店Guid',
  `store_name` varchar(45) NOT NULL COMMENT '门店名称',
  `device_no` varchar(45) NOT NULL COMMENT '厂商设备编号',
  `device_guid` varchar(45) NOT NULL COMMENT '系统设备编号（云端生成）',
  `is_binding` tinyint(1) NOT NULL COMMENT '设备是否绑定（0=否，1=是。）',
  `device_type` tinyint(1) NOT NULL COMMENT '设备类型（PC服务端- 0、PC平板- 1、小店通- 2、一体机- 3、POS机- 4、云平板- 5、点菜宝(M1)- 6、PV1(带刷卡的点菜宝)- 7）',
  `device_type_name` varchar(45) DEFAULT NULL COMMENT '设备类型名称',
  `sort` smallint(10) DEFAULT '0' COMMENT '打印设备排序（仅设备类型为一体机有效，默认为0。）',
  `create_user_guid` varchar(45) NOT NULL COMMENT '创建人Guid',
  `create_user_name` varchar(45) DEFAULT NULL COMMENT '创建人姓名',
  `gmt_create` datetime NOT NULL COMMENT '创建时间（设备绑定时间）',
  `unbind_user_guid` varchar(45) DEFAULT NULL COMMENT '解绑人Guid',
  `unbind_user_name` varchar(45) DEFAULT NULL COMMENT '解绑人姓名',
  `gmt_unbind` datetime DEFAULT NULL COMMENT '设备解绑时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_id` (`id`) USING BTREE,
  KEY `idx_store_guid` (`store_guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店-设备绑定表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2019-02-11 14:22:49
