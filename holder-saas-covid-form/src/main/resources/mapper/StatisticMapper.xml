<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.covid.form.mapper.StatisticMapper">

    <select id="statistic" parameterType="com.holderzone.saas.covid.form.entity.StatQuery"
            resultType="com.holderzone.saas.covid.form.entity.StatisticReadDO">
        select
        community,
        count(*) as total,
        sum(old_three_category) as oldThreeCategory,
        sum(hu_bei_native) as huBeiNative,
        sum(live_travel_infected_area) as liveTravelInfectedArea,
        sum(contact_with_infected_area) as contactWithInfectedArea,
        sum(contact_with_suspect_or_infected) as contactWithSuspectOrInfected,
        sum(new_three_category) as newThreeCategory,
        sum(contact_with_wu_han) as contactWith<PERSON>u<PERSON>an,
        sum(travel_out_side) as travelOutSide,
        sum(travel_in_chong_qing) as travelInChongQing,
        sum(travel_in_zhe_guang_hu) as travelInZheGuangHu,
        sum(observe_category) as observeCategory,
        sum(under_observation) as underObservation,
        sum(observe_fourteen) as observeFourteen,
        sum(observe_terminate) as observeTerminate,
        sum(travel_outside_city) as travelOutsideCity,
        sum(travel_outside_province) as travelOutsideProvince,
        sum(travel_non_city_in_province) as travelNonCityInProvince,
        sum(car) as car
        from hsc_form_statistic
        where question_guid = #{questionGuid}
        <if test="community != null and community != ''">
            and community = #{community}
        </if>
        and gmt_create &gt;= #{fromDateTime}
        and gmt_create &lt; #{toDateTime}
        group by community
    </select>

</mapper>
