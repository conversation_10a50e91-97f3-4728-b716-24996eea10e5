spring:
  datasource:
    url: *********************************************************************************************************************************************************************************************************************************************************************************************
    password: LuckyTerry0923
    username: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      connection-error-retry-attempts: 2
      break-after-acquire-failure: true
      min-idle: 10
      max-active: 500
      max-wait: 60000
      initial-size: 10
  redis:
    host: **************
    database: 0
    password:
    port: 6379
  rocketmq:
    namesrv-addr: ***************:9876
    producer-group-name: covid-form
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
oss:
  bucket-name: holderzone-dev
  endpoint: https://oss-cn-beijing.aliyuncs.com
  access-key-secret: zQ5G0FAIKleSGbrgJzjG5yJmYlAJ6h
  access-key-id: LTAIcmjI31yODHq4
  folder-name: framework-dev
  holder-oss-domain-name: https://holderzone-dev.oss-cn-beijing.aliyuncs.com
form:
  h5-url: https://yqdc-sit.holderzone.cn/ncp
  mp-url: http://**************:35552/api/QRCode/GetQrCodeUrl
qr-code:
  width: 400
  height: 400
slf4j:
  print:
    after-enabled: false
    before-enabled: true
