eureka:
  instance:
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 10
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
  client:
    service-url:
      defaultZone: http://***************:8141/eureka/
spring:
  datasource:
    url: ***************************************************************************************************************************************************************************************************************************************************************
    password: mysqlHolder
    username: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      connection-error-retry-attempts: 2
      break-after-acquire-failure: true
      min-idle: 10
      max-active: 500
      max-wait: 60000
      initial-size: 10
  redis:
    host: **************
    database: 1
    password: eIx6TynJq
    port: 36380
  rocketmq:
    namesrv-addr: ***************:9876
    producer-group-name: covid-form
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
form:
  h5-url: https://yqdc-sit.holderzone.cn/ncp
  mp-url: http://**************:35552/api/QRCode/GetQrCodeUrl
qr-code:
  width: 400
  height: 400
slf4j:
  print:
    after-enabled: false
    before-enabled: true