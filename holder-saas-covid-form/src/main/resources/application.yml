spring:
  profiles:
    active: dev
  application:
    name: holder-saas-covid-form
  jackson:
    default-property-inclusion: non_null
  main:
    allow-bean-definition-overriding: true
server:
  port: 9001
  undertow:
    max-http-post-size: 0
    io-threads: 4
    worker-threads: 32
    buffer-size: 1024
    direct-buffers: true
mybatis-plus:
  mapper-locations: classpath*:/mapper/**Mapper.xml
  typeAliasesPackage: com.holderzone.saas.covid.form.entity
  configuration:
    map-underscore-to-camel-case: true
    aggressive-lazy-loading: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    cache-enabled: false
    call-setters-on-nulls: false
  global-config:
    refresh: false
    db-config:
      db-type: mysql
      id-type: input
      field-strategy: not_empty
      logic-delete-value: 1
      logic-not-delete-value: 0
mybatis-page:
  enable: false