package com.holderzone.saas.covid.form.aop;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.covid.api.dto.ResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@Slf4j
@RestControllerAdvice(basePackages = "com.holderzone.saas.covid.form.controller")
public class ResultAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return StringHttpMessageConverter.class.isAssignableFrom(converterType)
                || MappingJackson2HttpMessageConverter.class.isAssignableFrom(converterType);
    }

    @Override
    public Object beforeBodyWrite(
            Object body,
            MethodParameter returnType,
            MediaType selectedContentType,
            Class<? extends HttpMessageConverter<?>> selectedConverterType,
            ServerHttpRequest request,
            ServerHttpResponse response) {

        // 处理特殊路径，不需要做包装
        String url = request.getURI().getPath();
        // 如果匹配到了完全不包装的url，直接返回
        if (url.startsWith("/swagger")
                || url.startsWith("/v2")
                || url.startsWith("/error")) {
            return body;
        }

        Object result;
        if (body instanceof ResultDTO) {
            result = body;
        } else if (body instanceof String) {
            // 因为StringHttpMessageConverter会直接把字符串写入body, 所以字符串特殊处理
            result = JacksonUtils.writeValueAsString(ResultDTO.success(body));
        } else {
            // 其他类型进行统一包装
            result = ResultDTO.success(body);
        }

        return result;
    }
}