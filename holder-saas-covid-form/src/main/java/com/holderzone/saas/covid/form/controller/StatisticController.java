/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:StatisticController.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.controller;

import com.alibaba.excel.EasyExcel;
import com.holderzone.framework.slf4j.starter.anno.LogAfter;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.covid.api.StatisticApi;
import com.holderzone.saas.covid.api.dto.AnswerDTO;
import com.holderzone.saas.covid.api.dto.QuestionDTO;
import com.holderzone.saas.covid.api.dto.StatisticDTO;
import com.holderzone.saas.covid.form.config.StatisticStyleStrategy;
import com.holderzone.saas.covid.form.service.QuestionService;
import com.holderzone.saas.covid.form.service.StatisticService;
import com.holderzone.saas.covid.form.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-16 下午6:24
 */
@Slf4j
@Primary
@RestController
public class StatisticController implements StatisticApi {

    private final StatisticService statisticService;

    private final QuestionService questionService;

    @Autowired
    public StatisticController(StatisticService statisticService, QuestionService questionService) {
        this.statisticService = statisticService;
        this.questionService = questionService;
    }

    @Override
    @LogAfter(value = "查询统计", logLevel = LogLevel.INFO)
    @LogBefore(value = "查询统计", logLevel = LogLevel.INFO)
    public List<StatisticDTO> list(AnswerDTO answerDTO) {
        return statisticService.list(answerDTO);
    }

    @Override
    public void export(AnswerDTO answerDTO, HttpServletResponse response) throws IOException {
        log.info("导出统计表入参：{}", JacksonUtils.writeValueAsString(answerDTO));
        // 查询问卷
        QuestionDTO question = questionService.query(answerDTO.getQuestionGuid());
        // 查询统计
        List<StatisticDTO> statistics = statisticService.list(answerDTO);
        // 下载
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String name = question.getName();
        String desc = "结果统计";
        String fileName = ExcelUtils.fileNameEncoded(answerDTO.getFromDate(), answerDTO.getToDate(), name, desc);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), StatisticDTO.class)
                .registerWriteHandler(new StatisticStyleStrategy())
                .sheet(ExcelUtils.sheetName(name, desc)).doWrite(statistics);
    }
}
