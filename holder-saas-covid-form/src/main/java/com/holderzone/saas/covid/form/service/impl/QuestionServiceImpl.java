/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:QuestionServiceImpl.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.covid.api.dto.FileDTO;
import com.holderzone.saas.covid.api.dto.QuestionDTO;
import com.holderzone.saas.covid.form.entity.MpResult;
import com.holderzone.saas.covid.form.entity.QuestionDO;
import com.holderzone.saas.covid.form.mapper.QuestionMapper;
import com.holderzone.saas.covid.form.mapstruct.PojoMapstruct;
import com.holderzone.saas.covid.form.service.DistributedService;
import com.holderzone.saas.covid.form.service.QuestionService;
import com.holderzone.saas.covid.integration.BaseServiceRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020-02-16 下午7:43
 */
@Slf4j
@Service
public class QuestionServiceImpl extends ServiceImpl<QuestionMapper, QuestionDO> implements QuestionService {

    @Value("${form.h5-url:}")
    private String h5Url;

    @Value("${form.mp-url:}")
    private String mpUrl;

    @Value("${qr-code.width:400}")
    private Integer width;

    @Value("${qr-code.height:400}")
    private Integer height;

    private final RestTemplate remoteRestTemplate;

    private final BaseServiceRpc baseServiceRpc;

    private final PojoMapstruct pojoMapstruct;

    private final DistributedService distributedService;

    @Autowired
    public QuestionServiceImpl(@Qualifier("remoteRestTemplate") RestTemplate remoteRestTemplate,
                               BaseServiceRpc baseServiceRpc,
                               PojoMapstruct pojoMapstruct, DistributedService distributedService) {
        this.remoteRestTemplate = remoteRestTemplate;
        this.baseServiceRpc = baseServiceRpc;
        this.pojoMapstruct = pojoMapstruct;
        this.distributedService = distributedService;
    }

    @Override
    public QuestionDTO query(Long guid) {
        QuestionDO record = getOne(new LambdaQueryWrapper<QuestionDO>().eq(QuestionDO::getGuid, guid));
        if (record == null) {
            log.error("调查问卷记录[{}]不存在", guid);
            throw new IllegalArgumentException("调查问卷记录不存在");
        }
        return parse(record);
    }

    @Override
    public QuestionDTO queryByUid(String uid) {
        return parse(getOne(new LambdaQueryWrapper<QuestionDO>()
                .eq(QuestionDO::getUid, uid).orderByDesc(QuestionDO::getGuid), false));
    }

    @Override
    public Long save(QuestionDTO questionDTO) {
        QuestionDO record = pojoMapstruct.fromDto(questionDTO);
        if (questionDTO.getCommunities() == null) {
            record.setCommunities("[]");
        } else {
            record.setCommunities(JacksonUtils.writeValueAsString(questionDTO.getCommunities()));
        }
        QuestionDO origin = getOne(new LambdaQueryWrapper<QuestionDO>()
                .eq(QuestionDO::getUid, questionDTO.getUid()).orderByDesc(QuestionDO::getGuid), false);
        if (origin != null) {
            this.updateById(record.setGuid(origin.getGuid()));
        } else {
            this.save(record.setGuid(distributedService.nextQuestionGuid()));
        }
        return record.getGuid();
    }

    @Override
    public String downloadH5(Long guid) {
        QuestionDTO questionDTO = queryEffectiveQuestion(guid);
        if (StringUtils.hasText(questionDTO.getH5Url())) {
            return questionDTO.getH5Url();
        }
        FileDTO fileDTO = new FileDTO();
        fileDTO.setFileName(fileName(questionDTO.getUid(), guid));
        try {
            String url = h5Url + "?qid=" + guid;
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            BitMatrix bitMatrix = qrCodeWriter.encode(url, BarcodeFormat.QR_CODE, width, height);
            ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();
            MatrixToImageWriter.writeToStream(bitMatrix, "jpg", pngOutputStream);
            fileDTO.setFileContent(SecurityManager.entryptBase64(pngOutputStream.toByteArray()));
        } catch (WriterException | IOException e) {
            log.error("二维码生成失败", e);
            throw new RuntimeException("二维码生成失败");
        }
        String ossUrl = baseServiceRpc.upload(fileDTO);
        log.info("生成的二维码链接：{}", ossUrl);
        update(new LambdaUpdateWrapper<QuestionDO>().set(QuestionDO::getH5Url, ossUrl).eq(QuestionDO::getGuid, guid));
        return ossUrl;
    }

    @Override
    public String downloadMp(Long guid) {
        QuestionDTO questionDTO = queryEffectiveQuestion(guid);
        if (StringUtils.hasText(questionDTO.getMpUrl())) {
            return questionDTO.getMpUrl();
        }
        String url = UriComponentsBuilder.fromUriString(mpUrl).queryParam("code", guid).toUriString();
        MpResult response = remoteRestTemplate.getForObject(url, MpResult.class);
        if (response == null || !response.isSuccess()) {
            log.error("二维码生成失败：{}", response == null ? "响应体为空" : response.getMessage());
            throw new RuntimeException("二维码生成失败");
        }
        String ossUrl = response.getData();
        log.info("生成的二维码链接：{}", ossUrl);
        update(new LambdaUpdateWrapper<QuestionDO>().set(QuestionDO::getMpUrl, ossUrl).eq(QuestionDO::getGuid, guid));
        return ossUrl;
    }

    private QuestionDTO queryEffectiveQuestion(Long guid) {
        QuestionDTO questionDTO = query(guid);
        if (!StringUtils.hasText(questionDTO.getCity())) {
            log.error("调查问卷[{}]未设置城市名称", guid);
            throw new UnsupportedOperationException("调查问卷未设置城市名称，设置后方可下载");
        }
        if (!StringUtils.hasText(questionDTO.getName())) {
            log.error("调查问卷[{}]未设置表单名称", guid);
            throw new UnsupportedOperationException("调查问卷未设置表单名称，设置后方可下载");
        }
        if (CollectionUtils.isEmpty(questionDTO.getCommunities())) {
            log.error("调查问卷[{}]未设置管辖小区", guid);
            throw new UnsupportedOperationException("调查问卷未设置管辖小区，设置后方可下载");
        }
        return questionDTO;
    }

    private QuestionDTO parse(QuestionDO record) {
        if (record == null) {
            return null;
        }
        QuestionDTO result = pojoMapstruct.toDto(record);
        result.setCommunities(JacksonUtils.toObjectList(String.class, record.getCommunities()));
        return result;
    }

    private String fileName(String uid, Long guid) {
        return "covid_" + uid + "_" + guid + ".jpg";
    }
}
