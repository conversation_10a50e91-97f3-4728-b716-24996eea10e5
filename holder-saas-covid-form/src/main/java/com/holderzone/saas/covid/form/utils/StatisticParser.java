/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:StatisticParser.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.utils;

import com.holderzone.saas.covid.api.dto.AnswerDTO;
import com.holderzone.saas.covid.form.entity.StatisticDO;
import javafx.util.Pair;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-16 下午8:48
 */
public class StatisticParser {

    private static List<String> INFECTED_AREA = new ArrayList<>();

    private static List<String> CHONGQING_AREA = new ArrayList<>();

    private static List<String> ZHE_GUANG_HU_AREA = new ArrayList<>();

    private static List<String> UNDER_OBSERVATION_STAT = new ArrayList<>();

    private static List<String> OBSERVE_TERMINATE_STAT = new ArrayList<>();

    private static List<String> PERSONAL_CAR_NAME = new ArrayList<>();

    static {
        INFECTED_AREA.add("湖北");
        INFECTED_AREA.add("重庆");
        INFECTED_AREA.add("浙江");
        INFECTED_AREA.add("广东");
        INFECTED_AREA.add("湖南");
        CHONGQING_AREA.add("重庆");
        ZHE_GUANG_HU_AREA.add("浙江");
        ZHE_GUANG_HU_AREA.add("广东");
        ZHE_GUANG_HU_AREA.add("湖南");
        UNDER_OBSERVATION_STAT.add("留观");
        UNDER_OBSERVATION_STAT.add("疑似");
        OBSERVE_TERMINATE_STAT.add("解除观察");
        PERSONAL_CAR_NAME.add("私车");
        PERSONAL_CAR_NAME.add("私家车");
    }

    public static StatisticDO parse(AnswerDTO answerDTO) {
        StatisticDO statisticDO = new StatisticDO();
        statisticDO.setHuBeiNative(answerDTO.getIdCard().startsWith("42"));
        statisticDO.setLiveTravelInfectedArea(StringUtils.hasText(answerDTO.getTravelCity())
                && INFECTED_AREA.stream().anyMatch(area -> answerDTO.getTravelCity().contains(area)));
        statisticDO.setContactWithInfectedArea("是".equals(answerDTO.getContactWithInfectedArea()));
        statisticDO.setContactWithSuspectOrInfected(false);
        statisticDO.setOldThreeCategory(statisticDO.getHuBeiNative()
                || statisticDO.getLiveTravelInfectedArea()
                || statisticDO.getContactWithInfectedArea()
                || statisticDO.getContactWithSuspectOrInfected());
        statisticDO.setContactWithWuHan(false);
        statisticDO.setTravelOutSide(StringUtils.hasText(answerDTO.getTravelCity()));
        statisticDO.setTravelInChongQing(StringUtils.hasText(answerDTO.getTravelCity())
                && CHONGQING_AREA.stream().anyMatch(area -> answerDTO.getTravelCity().contains(area)));
        statisticDO.setTravelInZheGuangHu(StringUtils.hasText(answerDTO.getTravelCity())
                && ZHE_GUANG_HU_AREA.stream().anyMatch(area -> answerDTO.getTravelCity().contains(area)));
        statisticDO.setNewThreeCategory(statisticDO.getContactWithWuHan()
                || statisticDO.getTravelOutSide()
                || statisticDO.getTravelInChongQing()
                || statisticDO.getTravelInZheGuangHu());
        statisticDO.setUnderObservation(answerDTO.getTreatmentResult() != null
                && UNDER_OBSERVATION_STAT.stream().anyMatch(stat -> answerDTO.getTreatmentResult().equals(stat)));
        statisticDO.setObserveFourteen(false);
        statisticDO.setObserveTerminate(answerDTO.getTreatmentResult() != null
                && OBSERVE_TERMINATE_STAT.stream().anyMatch(stat -> answerDTO.getTreatmentResult().equals(stat)));
        statisticDO.setObserveCategory(statisticDO.getUnderObservation()
                || statisticDO.getObserveFourteen()
                || statisticDO.getObserveTerminate());
        Pair<String, String> provinceCity = cityDetail(answerDTO.getQuestion().getCity());
        statisticDO.setTravelOutsideProvince(StringUtils.hasText(answerDTO.getTravelCity())
                && !answerDTO.getTravelCity().contains(provinceCity.getKey()));
        statisticDO.setTravelNonCityInProvince(StringUtils.hasText(answerDTO.getTravelCity())
                && answerDTO.getTravelCity().contains(provinceCity.getKey())
                && !answerDTO.getTravelCity().contains(provinceCity.getValue()));
        statisticDO.setTravelOutsideCity(statisticDO.getTravelOutsideProvince()
                || statisticDO.getTravelNonCityInProvince());
        statisticDO.setCar(PERSONAL_CAR_NAME.contains(answerDTO.getTravelMode()));
        return statisticDO;
    }

    private static Pair<String, String> cityDetail(String cityDetail) {
        int idxOfProvince = cityDetail.indexOf("省");
        String province = null;
        if (idxOfProvince >= 0) {
            province = cityDetail.substring(0, idxOfProvince);
        }
        String city = null;
        int idxOfCity = cityDetail.indexOf("市");
        if (idxOfCity >= 0 && idxOfCity > idxOfProvince) {
            city = cityDetail.substring(idxOfProvince < 0 ? 0 : idxOfProvince + 1, idxOfCity);
        }
        if (!StringUtils.hasText(city)) {
            city = "成都";
        }
        if (!StringUtils.hasText(province)) {
            province = city;
        }
        return new Pair<>(province, city);
    }
}
