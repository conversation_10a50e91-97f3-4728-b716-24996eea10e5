package com.holderzone.saas.covid.form.aop;

import com.holderzone.saas.covid.api.dto.ResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.util.stream.Collectors;

@Slf4j
@RestControllerAdvice
public class ErrorAdvice {

    @ExceptionHandler({MethodArgumentNotValidException.class})
    public ResultDTO requestValueError(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map((fieldError) -> String.format("%s:%s", fieldError.getField(), fieldError.getDefaultMessage()))
                .collect(Collectors.joining(","));
        log.error("本地参数错误：{}", message);
        return ResultDTO.error(this.requireNonNullMessage(message, "参数错误"));
    }

    @ExceptionHandler({Exception.class})
    public ResultDTO exception(Exception e) {
        log.error("本地系统异常：{}", this.nonNullDetailMessage(e));
        return ResultDTO.error(this.nonNullMessage(e));
    }

    private String nonNullMessage(Throwable e) {
        return this.nonNullMessage(e.getMessage());
    }

    private String nonNullMessage(String message) {
        return this.requireNonNullMessage(message, "未知异常");
    }

    private String nonNullDetailMessage(Throwable e) {
        return asStringIfAbsent(e);
    }

    private String requireNonNullMessage(String message, String defaultMessage) {
        return StringUtils.hasText(message) ? message : defaultMessage;
    }

    private String asStringIfAbsent(Throwable throwable) {
        String message = throwable.getMessage();
        return message != null ? message : asString(throwable);
    }

    private String asString(Throwable throwable) {
        Writer result = new StringWriter();
        PrintWriter printWriter = new PrintWriter(result);
        throwable.printStackTrace(printWriter);
        return result.toString();
    }
}
