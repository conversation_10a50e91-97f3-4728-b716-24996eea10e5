/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:AnswerDO.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("hsc_form_answer")
public class AnswerDO {

    /**
     * REDIS主键、调查问卷答案GUID
     */
    @TableId
    private Long guid;

    /**
     * 填卷人标识
     */
    private String uid;

    /**
     * 调查问卷GUID
     */
    private Long questionGuid;

    /**
     * 您的姓名
     */
    private String name;

    /**
     * 您的性别
     */
    private String gender;

    /**
     * 您的年龄
     */
    private Integer age;

    /**
     * 您的身份证号
     */
    private String idCard;

    /**
     * 您的联系方式
     */
    private String phone;

    /**
     * 您的现住地(小区楼栋单元房号)
     * 所在小区
     */
    private String community;

    /**
     * 您的现住地(小区楼栋单元房号)
     * 楼栋信息
     */
    private String building;

    /**
     * 您的所居住的房屋性质
     * 自有住房、租住、其他
     */
    private String houseType;

    /**
     * 您的家中常住人数
     */
    private Integer residentNum;

    /**
     * 近期(1月8日)以来接触过从湖北、重庆等疫情高发地区来访人员
     */
    private String contactWithInfectedArea;

    /**
     * 近期(1月29日)以来接触过外市的来访人员
     */
    private String contactWithOutCity;

    /**
     * 1月8日后是否去过市外
     */
    private String travelOutside;

    /**
     * 所到城市
     */
    private String travelCity;

    /**
     * 外出时间
     */
    private LocalDate travelDate;

//    /**
//     * 返回时间
//     */
//    private LocalDateTime backTime;

    /**
     * 出行方式
     */
    private String travelMode;

    /**
     * 车牌或班次
     */
    private String travelDetail;

    /**
     * 您及家人目前的身体状况
     */
    private String health;

    /**
     * 今日测量体温(℃)
     */
    private BigDecimal temperature;

    /**
     * 您是否有以下症状
     */
    private String symptom;

    /**
     * 您的诊疗请况
     * 诊疗时间
     */
    private LocalDate treatmentDate;

    /**
     * 您的诊疗请况
     * 诊疗机构
     */
    private String treatmentAgency;

    /**
     * 您的诊疗请况
     * 诊疗结果：确诊、留观、疑似、解除观察
     */
    private String treatmentResult;

    /**
     * 您的返(赴)所在区后接触的人员信息
     * 姓名
     * 性别
     * 年龄
     * 现住地
     * 电话
     */
    private String contactPeople;

    /**
     * 填卷日期
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改日期
     */
    private LocalDateTime gmtModified;
}
