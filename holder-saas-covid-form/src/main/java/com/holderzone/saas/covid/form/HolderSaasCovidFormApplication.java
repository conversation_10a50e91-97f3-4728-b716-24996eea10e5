package com.holderzone.saas.covid.form;

import com.holderzone.framework.slf4j.starter.EnableSlf4jConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableSwagger2
@EnableSlf4jConfig
@EnableEurekaClient
@SpringBootApplication
@EnableFeignClients(basePackages = "com.holderzone.saas.covid")
public class HolderSaasCovidFormApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasCovidFormApplication.class, args);
    }

}
