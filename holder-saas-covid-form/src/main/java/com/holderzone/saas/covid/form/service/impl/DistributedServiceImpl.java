package com.holderzone.saas.covid.form.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.covid.form.service.DistributedService;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedServiceImpl
 * @date 2018/02/14 09:00
 * @description 分布式id服务实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class DistributedServiceImpl implements DistributedService {

    private static final String TAG_QUESTION = "covid/form/question";

    private static final String TAG_ANSWER = "covid/form/answer";

    private static final String TAG_STATISTIC = "covid/form/statistic";

    private final RedisTemplate redisTemplate;

    @Autowired
    public DistributedServiceImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public long rawId(String tag) {
        try {
            return BatchIdGenerator.getGuid(redisTemplate, tag);
        } catch (IOException e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public String nextId(String tag) {
        try {
            return String.valueOf(BatchIdGenerator.getGuid(redisTemplate, tag));
        } catch (IOException e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public List<String> nextBatchId(String tag, long count) {
        return BatchIdGenerator.batchGetGuids(redisTemplate, tag, count)
                .stream().map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public long nextQuestionGuid() {
        return rawId(TAG_QUESTION);
    }

    @Override
    public long nextAnswerGuid() {
        return rawId(TAG_ANSWER);
    }

    @Override
    public long nextStatisticGuid() {
        return rawId(TAG_STATISTIC);
    }
}
