/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:StatisticDTO.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.entity;

import lombok.Data;

@Data
public class StatisticReadDO {

    /**
     * 所属小区
     */
    private String community;

    /**
     * 总人数
     */
    private Integer total;

    /**
     * 旧三类小计
     */
    private Integer oldThreeCategory;

    /**
     * 是否湖北籍人员，根据idCard规则来判断
     */
    private Integer huBeiNative;

    /**
     * 近期有疫情居住或旅行史
     */
    private Integer liveTravelInfectedArea;

    /**
     * 近期与疫区外来人员密切接触式
     */
    private Integer contactWithInfectedArea;

    /**
     * 与疑似和确诊病人有接触
     */
    private Integer contactWithSuspectOrInfected;

    /**
     * 新三类小计
     */
    private Integer newThreeCategory;

    /**
     * 与到过武汉的人有接触史
     */
    private Integer contactWithWuHan;

    /**
     * 近期去过疫情或者外地旅游
     */
    private Integer travelOutSide;

    /**
     * 重庆地区往(返)人员
     */
    private Integer travelInChongQing;

    /**
     * 浙江、广东、湖南地区往(返)人员
     */
    private Integer travelInZheGuangHu;

    /**
     * 医学观察类小计
     */
    private Integer observeCategory;

    /**
     * 居家医学观察人员
     */
    private Integer underObservation;

    /**
     * 当日满14天人数
     */
    private Integer observeFourteen;

    /**
     * 已解除观察人数
     */
    private Integer observeTerminate;

    /**
     * 省外往(返)人员
     */
    private Integer travelOutsideCity;

    /**
     * 省外往(返)人员
     */
    private Integer travelOutsideProvince;

    /**
     * 省内非本市往(返)人员
     */
    private Integer travelNonCityInProvince;

    /**
     * 外登记车辆
     */
    private Integer car;

    public void increase(StatisticReadDO record) {
        this.total += record.total;
        this.oldThreeCategory += record.oldThreeCategory;
        this.huBeiNative += record.huBeiNative;
        this.liveTravelInfectedArea += record.liveTravelInfectedArea;
        this.contactWithInfectedArea += record.contactWithInfectedArea;
        this.contactWithSuspectOrInfected += record.contactWithSuspectOrInfected;
        this.newThreeCategory += record.newThreeCategory;
        this.contactWithWuHan += record.contactWithWuHan;
        this.travelOutSide += record.travelOutSide;
        this.travelInChongQing += record.travelInChongQing;
        this.travelInZheGuangHu += record.travelInZheGuangHu;
        this.observeCategory += record.observeCategory;
        this.underObservation += record.underObservation;
        this.observeFourteen += record.observeFourteen;
        this.observeTerminate += record.observeTerminate;
        this.travelOutsideCity += record.travelOutsideCity;
        this.travelOutsideProvince += record.travelOutsideProvince;
        this.travelNonCityInProvince += record.travelNonCityInProvince;
        this.car += record.car;
    }

    public static StatisticReadDO empty() {
        return empty("合计");
    }

    public static StatisticReadDO empty(String community) {
        StatisticReadDO statisticDTO = new StatisticReadDO();
        statisticDTO.setCommunity(community);
        statisticDTO.setTotal(0);
        statisticDTO.setOldThreeCategory(0);
        statisticDTO.setHuBeiNative(0);
        statisticDTO.setLiveTravelInfectedArea(0);
        statisticDTO.setContactWithInfectedArea(0);
        statisticDTO.setContactWithSuspectOrInfected(0);
        statisticDTO.setNewThreeCategory(0);
        statisticDTO.setContactWithWuHan(0);
        statisticDTO.setTravelOutSide(0);
        statisticDTO.setTravelInChongQing(0);
        statisticDTO.setTravelInZheGuangHu(0);
        statisticDTO.setObserveCategory(0);
        statisticDTO.setUnderObservation(0);
        statisticDTO.setObserveFourteen(0);
        statisticDTO.setObserveTerminate(0);
        statisticDTO.setTravelOutsideCity(0);
        statisticDTO.setTravelOutsideProvince(0);
        statisticDTO.setTravelNonCityInProvince(0);
        statisticDTO.setCar(0);
        return statisticDTO;
    }
}
