/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:AnswerController.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.controller;

import com.alibaba.excel.EasyExcel;
import com.holderzone.framework.slf4j.starter.anno.LogAfter;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.covid.api.AnswerApi;
import com.holderzone.saas.covid.api.dto.AnswerDTO;
import com.holderzone.saas.covid.api.dto.PageDTO;
import com.holderzone.saas.covid.api.dto.QuestionDTO;
import com.holderzone.saas.covid.form.config.AnswerStyleStrategy;
import com.holderzone.saas.covid.form.service.AnswerService;
import com.holderzone.saas.covid.form.service.QuestionService;
import com.holderzone.saas.covid.form.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-16 下午6:22
 */
@Slf4j
@Primary
@RestController
public class AnswerController implements AnswerApi {

    private final AnswerService answerService;

    private final QuestionService questionService;

    @Autowired
    public AnswerController(AnswerService answerService, QuestionService questionService) {
        this.answerService = answerService;
        this.questionService = questionService;
    }

    @Override
    @LogAfter(value = "根据GUID查询答案", logLevel = LogLevel.INFO)
    @LogBefore(value = "根据GUID查询答案", logLevel = LogLevel.INFO)
    public AnswerDTO query(Long guid) {
        return answerService.query(guid);
    }

    @Override
    @LogAfter(value = "根据填写人ID查询答案", logLevel = LogLevel.INFO)
    @LogBefore(value = "根据填写人ID查询答案", logLevel = LogLevel.INFO)
    public AnswerDTO queryByUid(AnswerDTO answerDTO) {
        return answerService.queryByUid(answerDTO);
    }

    @Override
    @LogAfter(value = "保存或修改答案", logLevel = LogLevel.INFO)
    @LogBefore(value = "保存或修改答案", logLevel = LogLevel.INFO)
    public Long save(AnswerDTO answerDTO) {
        return answerService.save(answerDTO);
    }

    @Override
    @LogBefore(value = "查询答案列表", logLevel = LogLevel.INFO)
    public List<AnswerDTO> list(AnswerDTO answerDTO) {
        return answerService.list(answerDTO);
    }

    @Override
    @LogAfter(value = "分页查询答案列表", logLevel = LogLevel.INFO)
    @LogBefore(value = "分页查询答案列表", logLevel = LogLevel.INFO)
    public PageDTO<AnswerDTO> page(AnswerDTO answerDTO) {
        return answerService.page(answerDTO);
    }

    @Override
    public void export(AnswerDTO answerDTO, HttpServletResponse response) throws IOException {
        log.info("导出答案列表入参：{}", JacksonUtils.writeValueAsString(answerDTO));
        // 查询问卷
        QuestionDTO question = questionService.query(answerDTO.getQuestionGuid());
        // 查询结果
        List<AnswerDTO> answers = answerService.list(answerDTO);
        // 下载
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String name = question.getName();
        String desc = "调查明细";
        String fileName = ExcelUtils.fileNameEncoded(answerDTO.getFromDate(), answerDTO.getToDate(), name, desc);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), AnswerDTO.class)
                .registerWriteHandler(new AnswerStyleStrategy())
                .sheet(ExcelUtils.sheetName(name, desc)).doWrite(answers);
    }
}
