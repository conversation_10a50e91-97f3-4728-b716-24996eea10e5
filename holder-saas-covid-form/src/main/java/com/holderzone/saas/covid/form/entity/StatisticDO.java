/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:StatisticDO.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("hsc_form_statistic")
public class StatisticDO {

    /**
     * 自增主键
     */
    @TableId
    private Long guid;

    /**
     * 用户标识
     */
    private String uid;

    /**
     * 调查问卷GUID
     */
    private Long questionGuid;

    /**
     * 调查问卷答案GUID
     */
    private Long answerGuid;

    /**
     * 所在小区
     */
    private String community;

    /**
     * 旧三类小计
     */
    private Boolean oldThreeCategory;

    /**
     * 是否湖北籍人员，根据idCard规则来判断
     */
    private Boolean huBeiNative;

    /**
     * 近期有疫情居住或旅行史
     */
    private Boolean liveTravelInfectedArea;

    /**
     * 近期与疫区外来人员密切接触式
     */
    private Boolean contactWithInfectedArea;

    /**
     * 与疑似和确诊病人有接触
     */
    private Boolean contactWithSuspectOrInfected;

    /**
     * 新三类小计
     */
    private Boolean newThreeCategory;

    /**
     * 与到过武汉的人有接触史
     */
    private Boolean contactWithWuHan;

    /**
     * 近期去过疫情或者外地旅游
     */
    private Boolean travelOutSide;

    /**
     * 重庆地区往(返)人员
     */
    private Boolean travelInChongQing;

    /**
     * 浙江、广东、湖南地区往(返)人员
     */
    private Boolean travelInZheGuangHu;

    /**
     * 居家医学观察人员
     */
    private Boolean underObservation;

    /**
     * 医学观察类小计
     */
    private Boolean observeCategory;

    /**
     * 当日满14天人数
     */
    private Boolean observeFourteen;

    /**
     * 已解除观察人数
     */
    private Boolean observeTerminate;

    /**
     * 省外往(返)人员
     */
    private Boolean travelOutsideCity;

    /**
     * 省外往(返)人员
     */
    private Boolean travelOutsideProvince;

    /**
     * 省内非本市往(返)人员
     */
    private Boolean travelNonCityInProvince;

    /**
     * 外登记车辆
     */
    private Boolean car;

    /**
     * 填卷日期
     */
    private LocalDateTime gmtCreate;

    /**
     * 填卷修改日期
     */
    private LocalDateTime gmtModified;
}
