package com.holderzone.saas.covid.form.config;

import com.holderzone.saas.covid.form.constant.Constants;
import com.holderzone.saas.covid.form.utils.AuthHelper;
import com.holderzone.saas.covid.form.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * 初始化敏感词库，将敏感词加入到HashMap中，构建DFA算法模型
 *
 * <AUTHOR>
 * @date 2019/06/01 下午 13:54
 * @description
 */
@Slf4j
@Configuration
@Profile({"pc", "dev", "local", "terry"})
public class LocalAuthLoader implements ApplicationListener<ApplicationStartedEvent> {

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.info("应用启动，开始加载认证配置");
        AuthHelper.init(CommonUtil.readFile(Constants.AUTHENTICATION + ".txt"));
        log.info("应用启动，认证配置加载成功");
    }
}
