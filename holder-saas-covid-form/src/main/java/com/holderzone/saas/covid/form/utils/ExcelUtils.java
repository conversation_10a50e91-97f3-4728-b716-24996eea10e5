package com.holderzone.saas.covid.form.utils;

import org.apache.commons.lang3.RegExUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public final class ExcelUtils {

    public static final String CONCAT_1 = " - ";

    private static final String CONCAT_2 = " 至 ";

    private ExcelUtils() {
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        String fileName = fileNameEncoded(
                LocalDate.of(2020, 2, 8),
                LocalDate.of(2020, 2, 22),
                "祥龙社区“新冠”调查表", "调查明细"
        );
        System.out.println(fileName);
    }

    public static String sheetName(String name, String desc) {
        return name + CONCAT_1 + desc;
    }

    public static String fileNameEncoded(LocalDate fromDate, LocalDate toDate, String name, String desc) throws UnsupportedEncodingException {
        return URLEncoder.encode(fileName(fromDate, toDate, name, desc), "UTF-8").replaceAll("\\+", "%20");
    }

    private static String fileName(LocalDate fromDate, LocalDate toDate, String name, String desc) {
        return RegExUtils.removeAll(name, "[\\s\\\\/:\\*\\?\\\"<>\\|]") + CONCAT_1 + desc + dateDesc(fromDate, toDate);
    }

    private static String dateDesc(LocalDate fromDate, LocalDate toDate) {
        StringBuilder sb = new StringBuilder();
        sb.append("（");
        sb.append(dateString(fromDate));
        if (fromDate.compareTo(toDate) < 0) {
            sb.append(CONCAT_2);
            sb.append(dateString(toDate));
        }
        sb.append("）");
        return sb.toString();
    }

    private static String dateString(LocalDate localDate) {
        return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}
