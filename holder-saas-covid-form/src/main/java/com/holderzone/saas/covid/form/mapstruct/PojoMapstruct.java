/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PojoMapstruct.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.mapstruct;

import com.holderzone.saas.covid.api.dto.AnswerDTO;
import com.holderzone.saas.covid.api.dto.QuestionDTO;
import com.holderzone.saas.covid.api.dto.StatisticDTO;
import com.holderzone.saas.covid.form.entity.AnswerDO;
import com.holderzone.saas.covid.form.entity.QuestionDO;
import com.holderzone.saas.covid.form.entity.StatisticReadDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-16 下午4:56
 */
@Component
@Mapper(componentModel = "spring")
public interface PojoMapstruct {

    @Mapping(source = "communities", target = "communities", ignore = true)
    QuestionDTO toDto(QuestionDO questionDO);

    @Mapping(source = "communities", target = "communities", ignore = true)
    QuestionDO fromDto(QuestionDTO questionDTO);

    @Mapping(source = "contactPeople", target = "contactPeople", ignore = true)
    AnswerDTO toDto(AnswerDO answerDO);

    @Mapping(source = "contactPeople", target = "contactPeople", ignore = true)
    AnswerDO fromDto(AnswerDTO answerDTO);

    StatisticDTO toDto(StatisticReadDO statisticReadDO);

    List<StatisticDTO> toDto(List<StatisticReadDO> statisticReadDO);
}
