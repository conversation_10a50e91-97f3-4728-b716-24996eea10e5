/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:QuestionServiceImpl.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.covid.api.dto.AnswerDTO;
import com.holderzone.saas.covid.api.dto.QuestionDTO;
import com.holderzone.saas.covid.api.dto.StatisticDTO;
import com.holderzone.saas.covid.form.entity.StatQuery;
import com.holderzone.saas.covid.form.entity.StatisticDO;
import com.holderzone.saas.covid.form.entity.StatisticReadDO;
import com.holderzone.saas.covid.form.mapper.StatisticMapper;
import com.holderzone.saas.covid.form.mapstruct.PojoMapstruct;
import com.holderzone.saas.covid.form.service.DistributedService;
import com.holderzone.saas.covid.form.service.QuestionService;
import com.holderzone.saas.covid.form.service.StatisticService;
import com.holderzone.saas.covid.form.utils.StatisticParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-02-16 下午7:43
 */
@Slf4j
@Service
public class StatisticServiceImpl extends ServiceImpl<StatisticMapper, StatisticDO> implements StatisticService {

    @Value("${form.ignore-unknown-rule:true}")
    private Boolean ignoreUnknownRule;

    private final PojoMapstruct pojoMapstruct;

    private final QuestionService questionService;

    private final DistributedService distributedService;

    @Autowired
    public StatisticServiceImpl(PojoMapstruct pojoMapstruct, QuestionService questionService, DistributedService distributedService) {
        this.pojoMapstruct = pojoMapstruct;
        this.questionService = questionService;
        this.distributedService = distributedService;
    }

    @Override
    public void save(AnswerDTO answerDTO) {
        StatisticDO record = trans(answerDTO);
        this.save(record.setGuid(distributedService.nextStatisticGuid()));
    }

    @Override
    public void update(AnswerDTO answerDTO) {
        StatisticDO record = trans(answerDTO);
        if (answerDTO.getGuid() != null) {
            this.update(record, new LambdaQueryWrapper<StatisticDO>().eq(StatisticDO::getAnswerGuid, answerDTO.getGuid()));
        } else {
            this.update(record, new LambdaQueryWrapper<StatisticDO>()
                    .eq(StatisticDO::getUid, answerDTO.getGuid())
                    .eq(StatisticDO::getQuestionGuid, answerDTO.getQuestionGuid())
                    .ge(StatisticDO::getGmtModified, LocalDate.now().atStartOfDay()));
        }
    }

    @Override
    public List<StatisticDTO> list(AnswerDTO answerDTO) {
        StatQuery statQuery = new StatQuery();
        statQuery.setQuestionGuid(answerDTO.getQuestionGuid());
        statQuery.setCommunity(answerDTO.getCommunity());
        statQuery.setFromDateTime(answerDTO.getFromDate().atStartOfDay());
        statQuery.setToDateTime(answerDTO.getToDate().atTime(LocalTime.MAX));
        List<StatisticReadDO> statistic = this.baseMapper.statistic(statQuery);
        if (!StringUtils.hasText(answerDTO.getCommunity())) {
            Map<String, StatisticReadDO> statRefMap = statistic.stream()
                    .collect(Collectors.toMap(StatisticReadDO::getCommunity, Function.identity()));
            QuestionDTO question = questionService.query(answerDTO.getQuestionGuid());
            statistic = question.getCommunities().stream()
                    .map(community -> Optional
                            .ofNullable(statRefMap.remove(community))
                            .orElse(StatisticReadDO.empty(community)))
                    .collect(Collectors.toList());
            if (!statRefMap.isEmpty()) {
                statistic.addAll(statRefMap.values());
            }
        } else if (statistic.isEmpty()) {
            statistic.add(StatisticReadDO.empty(answerDTO.getCommunity()));
        }
        StatisticReadDO total = StatisticReadDO.empty();
        statistic.forEach(total::increase);
        statistic.add(0, total);
        List<StatisticDTO> result = pojoMapstruct.toDto(statistic);
        // 应产品要求，未知规则项暂不统计
        if (ignoreUnknownRule) {
            result.forEach(it -> {
                it.setOldThreeCategory("-");
                it.setNewThreeCategory("-");
            });
        }
        return result;
    }

    private StatisticDO trans(AnswerDTO answerDTO) {
        StatisticDO record = StatisticParser.parse(answerDTO);
        record.setUid(answerDTO.getUid());
        record.setQuestionGuid(answerDTO.getQuestionGuid());
        record.setAnswerGuid(answerDTO.getGuid());
        record.setCommunity(answerDTO.getCommunity());
        return record;
    }
}
