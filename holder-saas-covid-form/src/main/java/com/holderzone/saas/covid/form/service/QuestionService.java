/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:QuestionService.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.covid.api.dto.QuestionDTO;
import com.holderzone.saas.covid.form.entity.QuestionDO;

/**
 * <AUTHOR>
 * @date 2020-02-16 下午7:34
 */
public interface QuestionService extends IService<QuestionDO> {

    QuestionDTO query(Long guid);

    QuestionDTO queryByUid(String uid);

    Long save(QuestionDTO questionDTO);

    String downloadH5(Long guid);

    String downloadMp(Long guid);
}
