/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:AuthController.java
 * Date:2020-2-17
 * Author:terry
 */

package com.holderzone.saas.covid.form.controller;

import com.holderzone.framework.slf4j.starter.anno.LogAfter;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.saas.covid.form.utils.AuthHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020-02-17 上午1:54
 */
@Slf4j
@RestController
@Api("COVID-登录")
@RequestMapping("/auth")
public class AuthController {

    @GetMapping("/login")
    @LogAfter(value = "帐号密码登录", logLevel = LogLevel.INFO)
    @LogBefore(value = "帐号密码登录", logLevel = LogLevel.INFO)
    @ApiOperation("帐号密码登录。account即为后台管理接口的uid。附上管理员的 帐号：000，密码：holder666")
    public boolean login(@RequestParam("account") String account, @RequestParam("password") String password) {
        return AuthHelper.passed(account, password);
    }
}
