package com.holderzone.saas.covid.form.config;

import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.core.enums.ConfigFileFormat;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.covid.form.constant.Constants;
import com.holderzone.saas.covid.form.utils.AuthHelper;
import com.holderzone.saas.covid.form.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * 初始化敏感词库，将敏感词加入到HashMap中，构建DFA算法模型
 *
 * <AUTHOR>
 * @date 2019/06/01 下午 13:54
 * @description
 */
@Slf4j
@Configuration
@Profile({"test", "release", "prod", "ctyun"})
@EnableApolloConfig(value = Constants.AUTHENTICATION + ".txt")
public class RemoteAuthLoader implements ApplicationListener<ApplicationStartedEvent> {

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.info("应用启动，开始加载认证配置");
        String content = ConfigService.getConfigFile(Constants.AUTHENTICATION, ConfigFileFormat.TXT).getContent();
        if (StringUtils.isEmpty(content)) {
//            log.info("没有拉取到apollo的认证配置");
//            throw new BusinessException("没有拉取到apollo的认证配置");
            log.info("应用启动，远程认证配置拉取失败");
            AuthHelper.init(CommonUtil.readFile(Constants.AUTHENTICATION + ".txt"));
            log.info("应用启动，本地认证配置加载完毕");
            return;
        }
        AuthHelper.init(CommonUtil.resolveApolloContent(content));
        log.info("应用启动，远程认证配置加载完毕");
    }

    @ApolloConfigChangeListener({Constants.AUTHENTICATION + ".txt"})
    private void sensitiveWordsOnChange(ConfigChangeEvent changeEvent) {
        log.info(Constants.AUTHENTICATION + "配置更新，睡眠5秒，等待内存更新......");
        CommonUtil.sleep(5000L);
        log.info("配置发生变更，开始加载认证配置");
        String content = ConfigService.getConfigFile(Constants.AUTHENTICATION, ConfigFileFormat.TXT).getContent();
        if (StringUtils.isEmpty(content)) {
            log.info("没有拉取到apollo的认证配置");
            throw new BusinessException("没有拉取到apollo的认证配置");
        }
        AuthHelper.init(CommonUtil.resolveApolloContent(content));
    }
}
