package com.holderzone.saas.covid.form.utils;

import com.holderzone.framework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/06/01 下午 14:01
 * @description
 */
public class CommonUtil {

    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);

    private static final String UTF_8 = "UTF-8";

    public static Set<String> readFile(String path) {
        Set<String> set = new HashSet<>();
        BufferedReader reader = null;
        try {
            InputStream stream = CommonUtil.class.getClassLoader().getResourceAsStream(path);
            reader = new BufferedReader(new InputStreamReader(stream, UTF_8));
            String line = null;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!StringUtils.isEmpty(line)) {
                    set.add(line);
                }
            }
        } catch (IOException e) {
            log.error("未发现{}文件", path);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return set;
    }

    /**
     * 解析apollo内容
     *
     * @param content
     * @return
     */
    public static Set<String> resolveApolloContent(String content) {
        if (StringUtils.isEmpty(content)) {
            return new HashSet<>();
        }
        String lineSeparator = null;
        if (content.contains("\r\n")) {
            lineSeparator = "\r\n";
        } else if (content.contains("\r")) {
            lineSeparator = "\r";
        } else {
            lineSeparator = "\n";
        }
        String[] contentArray = content.split(lineSeparator);
        Set<String> keySet = new HashSet<>();
        for (String str : contentArray) {
            str = str.trim();
            if (!StringUtils.isEmpty(str)) {
                keySet.add(str);
            }
        }
        return keySet;
    }

    /**
     * 睡眠指定时间
     *
     * @param mills
     */
    public static void sleep(Long mills) {
        try {
            Thread.sleep(mills);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


}
