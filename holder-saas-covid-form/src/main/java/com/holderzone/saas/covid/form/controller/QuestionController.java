package com.holderzone.saas.covid.form.controller;

import com.holderzone.framework.slf4j.starter.anno.LogAfter;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.saas.covid.api.QuestionApi;
import com.holderzone.saas.covid.api.dto.QuestionDTO;
import com.holderzone.saas.covid.form.service.QuestionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020-02-15 下午1:59
 */
@Slf4j
@Primary
@RestController
public class QuestionController implements QuestionApi {

    private final QuestionService questionService;

    @Autowired
    public QuestionController(QuestionService questionService) {
        this.questionService = questionService;
    }

    @Override
    @LogAfter(value = "根据问卷GUID查询配", logLevel = LogLevel.INFO)
    @LogBefore(value = "根据问卷GUID查询", logLevel = LogLevel.INFO)
    public QuestionDTO query(Long guid) {
        return questionService.query(guid);
    }

    @Override
    @LogAfter(value = "根据发起人UID查询问卷", logLevel = LogLevel.INFO)
    @LogBefore(value = "根据发起人UID查询问卷", logLevel = LogLevel.INFO)
    public QuestionDTO queryByUid(String uid) {
        return questionService.queryByUid(uid);
    }

    @Override
    @LogAfter(value = "保存配置", logLevel = LogLevel.INFO)
    @LogBefore(value = "保存配置", logLevel = LogLevel.INFO)
    public Long save(QuestionDTO questionDTO) {
        return questionService.save(questionDTO);
    }

    @Override
    @LogAfter(value = "获取H5二维码链接", logLevel = LogLevel.INFO)
    @LogBefore(value = "获取H5二维码链接", logLevel = LogLevel.INFO)
    public String downloadH5(Long guid) {
        return questionService.downloadH5(guid);
    }

    @Override
    @LogAfter(value = "获取小程序二维码链接", logLevel = LogLevel.INFO)
    @LogBefore(value = "获取小程序二维码链接", logLevel = LogLevel.INFO)
    public String downloadMp(Long guid) {
        return questionService.downloadMp(guid);
    }
}
