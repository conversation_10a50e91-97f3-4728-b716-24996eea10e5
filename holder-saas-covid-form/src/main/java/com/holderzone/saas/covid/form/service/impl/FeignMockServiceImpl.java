package com.holderzone.saas.covid.form.service.impl;

import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.security.Encodes;
import com.holderzone.saas.covid.api.dto.FileDTO;
import com.holderzone.saas.covid.integration.BaseServiceRpc;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

@Service
@Profile("terry")
public class FeignMockServiceImpl implements BaseServiceRpc {

    private final OssClient ossClient;

    public FeignMockServiceImpl(OssClient ossClient) {
        this.ossClient = ossClient;
    }

    @Override
    public String upload(FileDTO fileDTO) {
        String content = fileDTO.getFileContent();
        byte[] bytes = Encodes.decodeBase64(content);
        return ossClient.upload(fileDTO.getFileName(), bytes);
    }

    @Override
    public void delete(String fileUrl) {
        String url = fileUrl.split("//")[1];
        int position = url.indexOf("/");
        String fileName = url.substring(position + 1);
        ossClient.delete(fileName);
    }
}
