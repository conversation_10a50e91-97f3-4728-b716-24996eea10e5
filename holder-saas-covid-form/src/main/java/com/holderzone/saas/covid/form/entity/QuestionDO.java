/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:QuestionDO.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("hsc_form_question")
public class QuestionDO {

    /**
     * REDIS主键、调查问卷GUID
     */
    @TableId
    private Long guid;

    /**
     * 持卷人标识
     */
    private String uid;

    /**
     * 调查问卷名称
     */
    private String name;

    /**
     * 调查问卷城市
     */
    private String city;

    /**
     * 调查问卷目的
     */
    private String description;

    /**
     * 社区内的小区，列表JSON
     */
    private String communities;

    /**
     * 保留字段
     * 题目详情，列表JSON
     */
    private String questions;

    /**
     * H5二维码地址
     */
    private String h5Url;

    /**
     * 小程序水滴码地址
     */
    private String mpUrl;
}
