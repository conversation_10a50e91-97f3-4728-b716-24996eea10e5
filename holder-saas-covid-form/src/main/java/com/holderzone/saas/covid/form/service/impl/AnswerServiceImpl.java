/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:QuestionServiceImpl.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.covid.api.dto.AnswerDTO;
import com.holderzone.saas.covid.api.dto.ContactDTO;
import com.holderzone.saas.covid.api.dto.PageDTO;
import com.holderzone.saas.covid.form.entity.AnswerDO;
import com.holderzone.saas.covid.form.mapper.AnswerMapper;
import com.holderzone.saas.covid.form.mapstruct.PojoMapstruct;
import com.holderzone.saas.covid.form.service.AnswerService;
import com.holderzone.saas.covid.form.service.DistributedService;
import com.holderzone.saas.covid.form.service.QuestionService;
import com.holderzone.saas.covid.form.service.StatisticService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-02-16 下午7:43
 */
@Slf4j
@Service
public class AnswerServiceImpl extends ServiceImpl<AnswerMapper, AnswerDO> implements AnswerService {

    private final PojoMapstruct pojoMapstruct;

    private final QuestionService questionService;

    private final StatisticService statisticService;

    private final DistributedService distributedService;

    @Autowired
    public AnswerServiceImpl(PojoMapstruct pojoMapstruct, QuestionService questionService,
                             StatisticService statisticService, DistributedService distributedService) {
        this.pojoMapstruct = pojoMapstruct;
        this.questionService = questionService;
        this.statisticService = statisticService;
        this.distributedService = distributedService;
    }

    @Override
    public AnswerDTO query(Long guid) {
        AnswerDO record = this.getOne(new LambdaQueryWrapper<AnswerDO>().eq(AnswerDO::getGuid, guid));
        if (record == null) {
            log.error("问卷结果[{}]不存在", guid);
            throw new IllegalArgumentException("问卷结果不存在");
        }
        return parse(record);
    }

    @Override
    public AnswerDTO queryByUid(AnswerDTO answerDTO) {
        AnswerDTO result = parse(this.getOne(new LambdaQueryWrapper<AnswerDO>()
                .eq(AnswerDO::getUid, answerDTO.getUid())
                .eq(AnswerDO::getQuestionGuid, answerDTO.getQuestionGuid())
                .ge(AnswerDO::getGmtModified, LocalDate.now().atStartOfDay())
                .orderByDesc(AnswerDO::getGmtModified), false));
        if (result == null) {
            result = new AnswerDTO();
            result.setUid(answerDTO.getUid());
            result.setQuestionGuid(answerDTO.getQuestionGuid());
        }
        result.setQuestion(questionService.query(answerDTO.getQuestionGuid()));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(AnswerDTO answerDTO) {
        AnswerDO answerDO = pojoMapstruct.fromDto(answerDTO);
        if (answerDTO.getContactPeople() != null) {
            answerDO.setContactPeople(JacksonUtils.writeValueAsString(answerDTO.getContactPeople()));
        }
        if (answerDTO.getQuestion() == null
                || !StringUtils.hasText(answerDTO.getQuestion().getCity())
                || !(answerDTO.getQuestion().getCity().contains("省") || answerDTO.getQuestion().getCity().contains("市"))) {
            answerDTO.setQuestion(questionService.query(answerDTO.getQuestionGuid()));
        }
        if (answerDTO.getGuid() != null && this.update(answerDO,
                new LambdaQueryWrapper<AnswerDO>().eq(AnswerDO::getGuid, answerDTO.getGuid()))) {
            statisticService.update(answerDTO);
        } else {
            Wrapper<AnswerDO> condition = new LambdaQueryWrapper<AnswerDO>()
                    .eq(AnswerDO::getUid, answerDTO.getUid())
                    .eq(AnswerDO::getQuestionGuid, answerDTO.getQuestionGuid())
                    .ge(AnswerDO::getGmtModified, LocalDate.now().atStartOfDay());
            if (this.count(condition) > 0) {
                this.update(answerDO, condition);
                answerDTO.setGuid(getOne(condition, false).getGuid());
                statisticService.update(answerDTO);
            } else {
                long guid = distributedService.nextAnswerGuid();
                this.save(answerDO.setGuid(guid));
                answerDTO.setGuid(guid);
                statisticService.save(answerDTO);
            }
        }
        return answerDTO.getGuid();
    }

    @Override
    public List<AnswerDTO> list(AnswerDTO answerDTO) {
        List<AnswerDO> records = this.list(new LambdaQueryWrapper<AnswerDO>()
                .eq(AnswerDO::getQuestionGuid, answerDTO.getQuestionGuid())
                .eq(StringUtils.hasText(answerDTO.getCommunity()), AnswerDO::getCommunity, answerDTO.getCommunity())
                .ge(AnswerDO::getGmtModified, answerDTO.getFromDate().atStartOfDay())
                .le(AnswerDO::getGmtModified, answerDTO.getToDate().atTime(LocalTime.MAX)));
        return records.stream().map(this::parse).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public PageDTO<AnswerDTO> page(AnswerDTO answerDTO) {
        IPage<AnswerDO> iPage = new Page<>(answerDTO.getCurrentPage(), answerDTO.getPageSize());
        IPage<AnswerDO> records = this.page(iPage, new LambdaQueryWrapper<AnswerDO>()
                .eq(AnswerDO::getQuestionGuid, answerDTO.getQuestionGuid())
                .eq(StringUtils.hasText(answerDTO.getCommunity()), AnswerDO::getCommunity, answerDTO.getCommunity())
                .ge(AnswerDO::getGmtModified, answerDTO.getFromDate().atStartOfDay())
                .le(AnswerDO::getGmtModified, answerDTO.getToDate().atTime(LocalTime.MAX)));
        PageDTO<AnswerDTO> result = new PageDTO<>();
        result.setCurrentPage(records.getCurrent());
        result.setPageSize(records.getSize());
        result.setTotalPage(records.getPages());
        result.setTotalCount(records.getTotal());
        if (records.getRecords() == null) {
            result.setData(Collections.emptyList());
        } else {
            result.setData(records.getRecords().stream()
                    .map(this::parse).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return result;
    }

    private AnswerDTO parse(AnswerDO record) {
        if (record == null) {
            return null;
        }
        AnswerDTO answerDTO = pojoMapstruct.toDto(record);
        if (record.getContactPeople() != null) {
            List<ContactDTO> contacts = JacksonUtils.toObjectList(ContactDTO.class, record.getContactPeople());
            answerDTO.setContactPeople(contacts);
            answerDTO.setContactPeopleString(contacts.stream()
                    .map(ContactDTO::assemble)
                    .collect(Collectors.joining("，")));
        }
        if (record.getTravelMode() != null) {
            answerDTO.setTravelModeDetail(record.getTravelMode() + (record.getTravelDetail() == null ? "" : record.getTravelDetail()));
        }
        return answerDTO;
    }
}
