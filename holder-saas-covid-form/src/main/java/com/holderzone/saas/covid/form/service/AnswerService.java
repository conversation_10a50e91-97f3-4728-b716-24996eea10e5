/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:QuestionService.java
 * Date:2020-2-16
 * Author:terry
 */

package com.holderzone.saas.covid.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.covid.api.dto.AnswerDTO;
import com.holderzone.saas.covid.api.dto.PageDTO;
import com.holderzone.saas.covid.form.entity.AnswerDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-16 下午7:34
 */
public interface AnswerService extends IService<AnswerDO> {

    AnswerDTO query(Long guid);

    AnswerDTO queryByUid(AnswerDTO answerDTO);

    Long save(AnswerDTO answerDTO);

    List<AnswerDTO> list(AnswerDTO answerDTO);

    PageDTO<AnswerDTO> page(AnswerDTO answerDTO);
}
