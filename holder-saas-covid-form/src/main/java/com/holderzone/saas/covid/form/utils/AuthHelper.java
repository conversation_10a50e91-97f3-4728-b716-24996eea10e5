/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:AuthHelper.java
 * Date:2020-2-17
 * Author:terry
 */

package com.holderzone.saas.covid.form.utils;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2020-02-17 上午2:20
 */
public class AuthHelper {

    private static Map<String, String> AUTHENTICATION = new ConcurrentHashMap<>(1024);

    public static void init(Set<String> rows) {
        AUTHENTICATION.clear();
        for (String row : rows) {
            String[] split = row.split(",");
            AUTHENTICATION.put(split[0], split[1]);
        }
    }

    public static boolean passed(String account, String password) {
        return password.equalsIgnoreCase(AUTHENTICATION.get(account));
    }
}
