<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.holderzone</groupId>
    <artifactId>holder-saas-store-report</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>holder-saas-store-report</name>
    <description>门店项目-报表项目</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.0.5.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven.compile.source>1.8</maven.compile.source>
        <maven.compile.target>1.8</maven.compile.target>
        <framework.skd.version>1.1.7-SNAPSHOT</framework.skd.version>
        <elasticSearch.version>6.7.2</elasticSearch.version>
        <org.mapstruct.version>1.2.0.Final</org.mapstruct.version>
        <spring-cloud.version>Finchley.RELEASE</spring-cloud.version>
        <store.dto.version>0.0.9-SNAPSHOT</store.dto.version>
        <custom_maven_url>http://nexus.holderzone.cn/nexus</custom_maven_url>
        <poi.version>4.0.0</poi.version>
    </properties>

    <dependencies>

        <!--actuator监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>holder-saas-member-merchant-dto</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>

        <!--Feign增强-->
        <!--<dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>framework-feign-starter</artifactId>
            <version>0.0.8-SNAPSHOT</version>
        </dependency>-->

        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>framework-feign-starter</artifactId>
            <version>0.0.10-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.2.12</version>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>${elasticSearch.version}</version>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>${elasticSearch.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>


        <dependency>
            <groupId>com.holderzone.framework</groupId>
            <artifactId>framework-sdk</artifactId>
            <version>${framework.skd.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <!--excel操作-->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
        </dependency>

        <!--swagger-->
        <!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger-ui-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger2-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>

        <!--导入appche替换默认feign的urlConnection链接-->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>holder-saas-store-dto</artifactId>
            <version>${store.dto.version}</version>
        </dependency>

        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>holder-saas-store-client</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>java-emoji-converter</artifactId>
            <version>0.1.1</version>
        </dependency>


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.47</version>
        </dependency>

        <!-- apollo -->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>1.4.0</version>
        </dependency>

        <dependency>
            <groupId>net.sourceforge.jexcelapi</groupId>
            <artifactId>jxl</artifactId>
            <version>2.6.12</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.holderzone.framework</groupId>
            <artifactId>framework-oss-sdk-starter</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.holderzone.resource</groupId>
            <artifactId>holder-saas-cloud-dto</artifactId>
            <version>1.10.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.3.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-annotation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>3.3.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>3.2.5</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <repositories>
        <repository>
            <id>public</id>
            <name>Public Repositories</name>
            <url>${custom_maven_url}/content/groups/public/</url>
        </repository>
        <repository>
            <id>snapshots</id>
            <name>Nexus Snapshots</name>
            <url>${custom_maven_url}/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>thirdparty</id>
            <name>Nexus ThirdParty</name>
            <url>${custom_maven_url}/content/repositories/thirdparty/</url>
        </repository>
        <repository>
            <id>releases</id>
            <name>Nexus Releases</name>
            <url>${custom_maven_url}/content/repositories/releases/</url>
        </repository>

    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>snapshots</id>
            <name>Nexus Snapshots</name>
            <url>${custom_maven_url}/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>thirdparty</id>
            <name>Nexus ThirdParty</name>
            <url>${custom_maven_url}/content/repositories/thirdparty/</url>
        </pluginRepository>
        <pluginRepository>
            <id>releases</id>
            <name>Nexus Releases</name>
            <url>${custom_maven_url}/content/repositories/releases/</url>
        </pluginRepository>

        <pluginRepository>
            <id>public</id>
            <name>Public Repositories</name>
            <url>${custom_maven_url}/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.3</version>
                <configuration>
                   <!--指定生成.exec文件的存放位置-->
                    <destFile>target/coverage-reports/jacoco.exec</destFile>
                    <!--Jacoco是根据.exec存放的位置来生成reports，所以需要指定.exec的存放路径，生成的reports在/target/site目录下-->
                    <dataFile>target/coverage-reports/jacoco.exec</dataFile>
                </configuration>
                <executions>
                   <execution>
                      <goals>
                         <goal>prepare-agent</goal>
                      </goals>
                   </execution>
                   <execution>
                      <id>report</id>
                      <phase>prepare-package</phase>
                      <goals>
                         <goal>report</goal>
                      </goals>
                   </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                   <skip>false</skip>
                   <skipTests>false</skipTests>
                   <forkMode>once</forkMode>
                   <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
