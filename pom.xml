<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" 
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.holderzone</groupId>
    <artifactId>store-saas-platform</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>Store SaaS Platform</name>
    <description>门店SaaS平台 - 父项目</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <spring-boot.version>2.0.3.RELEASE</spring-boot.version>
        <spring-cloud.version>Finchley.RELEASE</spring-cloud.version>
    </properties>

    <modules>
        <!-- 聚合层服务 -->
        <module>holder-saas-aggregation-app</module>
        <module>holder-saas-aggregation-kds</module>
        <module>holder-saas-aggregation-merchant</module>
        <module>holder-saas-aggregation-phoneapp</module>
        <module>holder-saas-aggregation-weixin</module>
        
        <!-- 基础设施服务 -->
        <module>holder-saas-gateway</module>
        <module>holder-saas-register</module>
        <module>holder-saas-sso</module>
        
        <!-- 业务服务 -->
        <module>holder-saas-store-business</module>
        <module>holder-saas-store-calculator</module>
        <module>holder-saas-store-client</module>
        <module>holder-saas-store-config</module>
        <module>holder-saas-store-deposit</module>
        <module>holder-saas-store-dto</module>
        <module>holder-saas-store-erp</module>
        <module>holder-saas-store-hw</module>
        <module>holder-saas-store-item</module>
        <module>holder-saas-store-kds</module>
        <module>holder-saas-store-mdm</module>
        <module>holder-saas-store-media</module>
        <module>holder-saas-store-member</module>
        <module>holder-saas-store-message</module>
        <module>holder-saas-store-organization</module>
        <module>holder-saas-store-pay</module>
        <module>holder-saas-store-print</module>
        <module>holder-saas-store-queue</module>
        <module>holder-saas-store-report</module>
        <module>holder-saas-store-reserve</module>
        <module>holder-saas-store-retail</module>
        <module>holder-saas-store-staff</module>
        <module>holder-saas-store-table</module>
        <module>holder-saas-store-takeaway</module>
        <module>holder-saas-store-trade</module>
        <module>holder-saas-store-weixin</module>
        
        <!-- 其他服务 -->
        <module>holder-saas-covid-resource</module>
        <module>holder-saas-store-docs</module>
    </modules>

    <repositories>
        <repository>
            <id>public</id>
            <name>Public Repositories</name>
            <url>http://nexus.holderzone.cn/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>Public Repositories</name>
            <url>http://nexus.holderzone.cn/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
