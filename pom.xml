<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.0.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.holderzone</groupId>
    <artifactId>holder-saas-store-erp</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>holder-saas-store-erp</name>
    <description>Simple ERP</description>

    <properties>
        <java.version>1.8</java.version>
        <swagger.version>2.8.0</swagger.version>
        <mysql-connect-java.version>8.0.19</mysql-connect-java.version>
        <mybatis-springboot.version>1.3.2</mybatis-springboot.version>
        <spring-cloud.version>Finchley.RELEASE</spring-cloud.version>
        <nexus.url>http://nexus.holderzone.cn</nexus.url>
        <store.dto.version>0.0.9-SNAPSHOT</store.dto.version>
        <sdk.version>1.1.7-SNAPSHOT</sdk.version>
        <mapstruct.version>1.2.0.Final</mapstruct.version>
        <mybatis.plus.version>3.1.0</mybatis.plus.version>
    </properties>

    <repositories>
        <repository>
            <id>snapshots</id>
            <url>${nexus.url}/nexus/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>repository</id>
            <url>${nexus.url}/nexus/content/groups/public/</url>
        </repository>

    </repositories>

    <dependencies>

        <!--actuator监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.holderzone.framework</groupId>
            <artifactId>framework-rocketmq-starter</artifactId>
            <version>1.1.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--Feign增强-->
        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>framework-feign-starter</artifactId>
            <version>0.0.10-SNAPSHOT</version>
        </dependency>

        <!--日志包-->
        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>framework-slf4j-starter</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!--mybatis-plus-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis.plus.version}</version>
        </dependency>

        <!--DTO-->
        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>holder-saas-store-dto</artifactId>
            <version>${store.dto.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>framework-sdk</artifactId>
                    <groupId>com.holderzone.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--sdk-->
        <dependency>
            <groupId>com.holderzone.framework</groupId>
            <artifactId>framework-sdk</artifactId>
            <version>${sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.holderzone.framework</groupId>
            <artifactId>framework-sdk-starter</artifactId>
            <version>1.1.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-dependencies</artifactId>
            <version>${spring-cloud.version}</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <!--eureka-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
            <version>2.1.0.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>2.1.0.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
            <version>9.5.1</version>
        </dependency>

        <!-- swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.version}</version>
        </dependency>

        <!--持久层依赖包-->
        <!--<dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid-starter.version}</version>
        </dependency>-->
        <!--<dependency>-->
        <!--<groupId>org.mybatis.spring.boot</groupId>-->
        <!--<artifactId>mybatis-spring-boot-starter</artifactId>-->
        <!--<version>${mybatis-springboot.version}</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connect-java.version}</version>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.0</version>
        </dependency>

        <dependency>
            <groupId>com.holderzone.framework</groupId>
            <artifactId>framework-dds-starter</artifactId>
            <version>1.2.1-SNAPSHOT</version>
        </dependency>

        <!--mapstruct-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>2.1.0.RELEASE</version>
        </dependency>

        <!--redis-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.10.7</version>
        </dependency>

		<dependency>
			<groupId>com.ctrip.framework.apollo</groupId>
			<artifactId>apollo-client</artifactId>
			<version>1.4.0</version>
		</dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.6</version>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.3</version>
                <configuration>
                    <!--指定生成.exec文件的存放位置-->
                    <destFile>target/coverage-reports/jacoco.exec</destFile>
                    <!--Jacoco是根据.exec存放的位置来生成reports，所以需要指定.exec的存放路径，生成的reports在/target/site目录下-->
                    <dataFile>target/coverage-reports/jacoco.exec</dataFile>
                    <includes>
                        <include>**/service/**</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.21.0</version>
                <configuration>
                    <skip>false</skip>
                    <skipTests>false</skipTests>
                    <forkMode>once</forkMode>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
