<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.holderzone</groupId>
    <artifactId>holder-saas-store-takeaway</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>


    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.0.3.RELEASE</version>
        <relativePath/> <!--  lookup parent from repository -->
    </parent>

    <modules>
        <module>holder-saas-store-takeaway-consumers</module>
        <module>holder-saas-store-takeaway-producers</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven.compile.source>1.8</maven.compile.source>
        <maven.compile.target>1.8</maven.compile.target>
        <custom_maven_url>http://nexus.holderzone.cn/nexus</custom_maven_url>

        <spring-cloud.version>Finchley.RELEASE</spring-cloud.version>
        <!--<spring.bom.version>4.3.3.RELEASE</spring.bom.version>-->
        <servlet_version>3.1.0</servlet_version>
        <log4j.version>1.2.16</log4j.version>
        <slfj.version>1.6.5</slfj.version>
        <fastjson_version>1.2.28</fastjson_version>
        <jackson_lib_version>1.9.13</jackson_lib_version>
        <commons_lang3_version>3.3</commons_lang3_version>
        <swagger_version>2.6.1</swagger_version>
        <springloaded_version>1.2.6.RELEASE</springloaded_version>
        <devtoos_version>1.4.1.RELEASE</devtoos_version>
        <mybatisplus.version>3.1.0</mybatisplus.version>
    </properties>

    <dependencies>

        <!--actuator监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!--validator-->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.0.11.Final</version>
            <exclusions>
                <exclusion>
                    <artifactId>classmate</artifactId>
                    <groupId>com.fasterxml</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- log4j2 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <!--zipkin-sleuth-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-zipkin</artifactId>
        </dependency>

        <!--mybatis-plus-->
        <!--<dependency>-->
            <!--<groupId>com.baomidou</groupId>-->
            <!--<artifactId>mybatis-plus-boot-starter</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.springframework.boot</groupId>-->
            <!--<artifactId>spring-boot-starter-freemarker</artifactId>-->
        <!--</dependency>-->

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>repository</id>
            <name>repository</name>
            <url>${custom_maven_url}/content/groups/public/</url>
        </repository>
        <repository>
            <id>snapshots</id>
            <name>Nexus Snapshots</name>
            <url>${custom_maven_url}/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>thirdparty</id>
            <name>Nexus ThirdParty</name>
            <url>${custom_maven_url}/content/repositories/thirdparty/</url>
        </repository>
        <repository>
            <id>releases</id>
            <name>Nexus Releases</name>
            <url>${custom_maven_url}/content/repositories/releases/</url>
        </repository>

    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>snapshots</id>
            <name>Nexus Snapshots</name>
            <url>${custom_maven_url}/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>thirdparty</id>
            <name>Nexus ThirdParty</name>
            <url>${custom_maven_url}/content/repositories/thirdparty/</url>
        </pluginRepository>
        <pluginRepository>
            <id>releases</id>
            <name>Nexus Releases</name>
            <url>${custom_maven_url}/content/repositories/releases/</url>
        </pluginRepository>

    </pluginRepositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!--<plugin>-->
                <!--<groupId>org.apache.maven.plugins</groupId>-->
                <!--<artifactId>maven-compiler-plugin</artifactId>-->
                <!--<configuration>-->
                    <!--<source>${maven.compile.source}</source>-->
                    <!--<target>${maven.compile.target}</target>-->
                    <!--<encoding>${project.build.sourceEncoding}</encoding>-->
                <!--</configuration>-->
            <!--</plugin>-->
        </plugins>
    </build>

</project>