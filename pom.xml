<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.holderzone</groupId>
    <artifactId>holder-saas-store-dto</artifactId>
    <version>0.0.9-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>holder-saas-store-dto</name>
    <description>门店平台-DTO</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven.compile.source>1.8</maven.compile.source>
        <maven.compile.target>1.8</maven.compile.target>
        <custom_maven_url>http://nexus.holderzone.cn/nexus</custom_maven_url>
        <swagger.version>2.9.2</swagger.version>
        <validation.version>2.0.1.Final</validation.version>
        <jackson.version>2.9.4</jackson.version>
        <holder.saas.cloud.dto>1.10.1-SNAPSHOT</holder.saas.cloud.dto>
    </properties>

    <dependencies>
        <!--数据脱敏所用依赖-->
        <dependency>
            <groupId>io.gitee.chemors</groupId>
            <artifactId>secure-ext-spring-boot-starter</artifactId>
            <version>1.0.3-RELEASE</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework/spring-core -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>5.0.9.RELEASE</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>${validation.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>mapstruct</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.version}</version>
        </dependency>

        <dependency>
            <groupId>com.holderzone.resource</groupId>
            <artifactId>holder-saas-cloud-dto</artifactId>
            <version>${holder.saas.cloud.dto}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.holderzone.resource</groupId>
                    <artifactId>holder-saas-store-dto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>holder-saas-member-dto</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>holder-saas-member-terminal-dto</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.holderzone.framework</groupId>
            <artifactId>framework-sdk</artifactId>
            <version>1.1.7-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.47</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.10</version>
        </dependency>
        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>framework-base-dto</artifactId>
            <version>1.4.3-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>holder-saas-member-wechat-dto</artifactId>
            <version>3.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.3.2</version>
            <scope>compile</scope>
        </dependency>
        <!--<dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>holder-saas-member-merchant-dto</artifactId>
            <version>3.0.4-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>-->
        <dependency>
            <groupId>douyin</groupId>
            <artifactId>doudian-sdk-java</artifactId>
            <version>1.1.0-20240730122808</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>snapshots</id>
            <name>Nexus Snapshots</name>
            <url>${custom_maven_url}/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>thirdparty</id>
            <name>Nexus ThirdParty</name>
            <url>${custom_maven_url}/content/repositories/thirdparty/</url>
        </repository>
        <repository>
            <id>releases</id>
            <name>Nexus Releases</name>
            <url>${custom_maven_url}/content/repositories/releases/</url>
        </repository>
        <!--<repository> <id>spring-snapshots</id> <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url> <snapshots> <enabled>true</enabled>
            </snapshots> </repository> <repository> <id>spring-milestones</id> <name>Spring
            Milestones</name> <url>https://repo.spring.io/milestone</url> <snapshots>
            <enabled>false</enabled> </snapshots> </repository> -->
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>snapshots</id>
            <name>Nexus Snapshots</name>
            <url>${custom_maven_url}/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>thirdparty</id>
            <name>Nexus ThirdParty</name>
            <url>${custom_maven_url}/content/repositories/thirdparty/</url>
        </pluginRepository>
        <pluginRepository>
            <id>releases</id>
            <name>Nexus Releases</name>
            <url>${custom_maven_url}/content/repositories/releases/</url>
        </pluginRepository>
        <!--<pluginRepository> <id>spring-snapshots</id> <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url> <snapshots> <enabled>true</enabled>
            </snapshots> </pluginRepository> <pluginRepository> <id>spring-milestones</id>
            <name>Spring Milestones</name> <url>https://repo.spring.io/milestone</url>
            <snapshots> <enabled>false</enabled> </snapshots> </pluginRepository> -->
    </pluginRepositories>

    <distributionManagement>
        <snapshotRepository>
            <uniqueVersion>false</uniqueVersion>
            <id>snapshots</id>
            <url>http://nexus.holderzone.cn/nexus/content/repositories/snapshots/</url>
            <layout>legacy</layout>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compile.source}</source>
                    <target>${maven.compile.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.7.8</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
