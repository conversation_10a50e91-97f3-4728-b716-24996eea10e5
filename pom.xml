<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.4.RELEASE</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.holderzone</groupId>
	<artifactId>holder-saas-covid-resource</artifactId>
	<packaging>pom</packaging>
	<version>0.0.1-SNAPSHOT</version>
	<name>holder-saas-covid-resource</name>
	<description>covid-19 衍生业务</description>
	<modules>
		<module>holder-saas-covid-api</module>
		<module>holder-saas-covid-form</module>
		<module>holder-saas-covid-integration</module>
	</modules>

	<properties>
		<java.version>1.8</java.version>
		<spring-boot.version>2.2.4.RELEASE</spring-boot.version>
		<spring-cloud.version>Hoxton.SR1</spring-cloud.version>
		<io.springfox.version>2.9.2</io.springfox.version>
		<org.mybatis.version>2.1.1</org.mybatis.version>
		<druid-starter.version>1.1.20</druid-starter.version>
		<org.mapstruct.version>1.2.0.Final</org.mapstruct.version>
<!--		<org.projectlombok.version>1.16.22</org.projectlombok.version>-->
		<org.projectlombok.version>1.18.0</org.projectlombok.version>
		<framework.sdk.starter.version>1.1.6-SNAPSHOT</framework.sdk.starter.version>
		<framework.sdk.version>1.4.3-SNAPSHOT</framework.sdk.version>
		<validation.version>2.0.1.Final</validation.version>
		<mybatis.plus.version>3.1.0</mybatis.plus.version>
		<google.zxing.version>3.4.0</google.zxing.version>
		<google.guava.version>20.0</google.guava.version>
		<com.alibaba.version>2.1.0.RELEASE</com.alibaba.version>
		<custom_maven_url>http://nexus.holderzone.cn/nexus</custom_maven_url>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${org.projectlombok.version}</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
			<version>${org.mapstruct.version}</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>${org.mapstruct.version}</version>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger-ui</artifactId>
				<version>${io.springfox.version}</version>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger2</artifactId>
				<version>${io.springfox.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis.spring.boot</groupId>
				<artifactId>mybatis-spring-boot-starter</artifactId>
				<version>${org.mybatis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-boot-starter</artifactId>
				<version>${mybatis.plus.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid-spring-boot-starter</artifactId>
				<version>${druid-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.ctrip.framework.apollo</groupId>
				<artifactId>apollo-client</artifactId>
				<version>1.4.0</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>2.1.4</version>
			</dependency>
			<dependency>
				<groupId>com.holderzone.framework</groupId>
				<artifactId>framework-sdk-starter</artifactId>
				<version>${framework.sdk.starter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.holderzone</groupId>
				<artifactId>framework-base-dto</artifactId>
				<version>${framework.sdk.version}</version>
			</dependency>
			<dependency>
				<groupId>javax.validation</groupId>
				<artifactId>validation-api</artifactId>
				<version>${validation.version}</version>
			</dependency>
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>2.9.0</version>
			</dependency>
			<dependency>
				<groupId>com.google.zxing</groupId>
				<artifactId>core</artifactId>
				<version>${google.zxing.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.zxing</groupId>
				<artifactId>javase</artifactId>
				<version>${google.zxing.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${google.guava.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>${com.alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.holderzone.framework</groupId>
				<artifactId>framework-oss-sdk-starter</artifactId>
				<version>1.0.3-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>javax.servlet-api</artifactId>
				<version>3.1.0</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.datatype</groupId>
				<artifactId>jackson-datatype-jsr310</artifactId>
				<version>2.10.2</version>
			</dependency>
			<dependency>
				<groupId>com.holderzone.framework</groupId>
				<artifactId>framework-slf4j-starter</artifactId>
				<version>0.0.5-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>com.holderzone</groupId>
				<artifactId>holder-saas-covid-api</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.holderzone</groupId>
				<artifactId>holder-saas-covid-integration</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.holderzone</groupId>
				<artifactId>holder-saas-covid-form</artifactId>
				<version>${project.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<snapshotRepository>
			<uniqueVersion>false</uniqueVersion>
			<id>nexus-snapshots</id>
			<url>${custom_maven_url}/content/repositories/snapshots/</url>
			<layout>legacy</layout>
		</snapshotRepository>
	</distributionManagement>

	<repositories>
		<repository>
			<id>snapshots</id>
			<name>Nexus Snapshots</name>
			<url>${custom_maven_url}/content/repositories/snapshots/</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>thirdparty</id>
			<name>Nexus ThirdParty</name>
			<url>${custom_maven_url}/content/repositories/thirdparty/</url>
		</repository>
		<repository>
			<id>releases</id>
			<name>Nexus Releases</name>
			<url>${custom_maven_url}/content/repositories/releases/</url>
		</repository>
		<repository>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
			<id>jcenter-releases</id>
			<name>jcenter</name>
			<url>https://jcenter.bintray.com</url>
		</repository>
		<repository>
			<id>jcenter-snapshots</id>
			<name>jcenter</name>
			<url>http://oss.jfrog.org/artifactory/oss-snapshot-local/</url>
		</repository>
		<repository>
			<id>spring-snapshots</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/snapshot</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>snapshots</id>
			<name>Nexus Snapshots</name>
			<url>${custom_maven_url}/content/repositories/snapshots/</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</pluginRepository>
		<pluginRepository>
			<id>thirdparty</id>
			<name>Nexus ThirdParty</name>
			<url>${custom_maven_url}/content/repositories/thirdparty/</url>
		</pluginRepository>
		<pluginRepository>
			<id>releases</id>
			<name>Nexus Releases</name>
			<url>${custom_maven_url}/content/repositories/releases/</url>
		</pluginRepository>
		<pluginRepository>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
			<id>jcenter-releases</id>
			<name>jcenter</name>
			<url>https://jcenter.bintray.com</url>
		</pluginRepository>
		<pluginRepository>
			<id>jcenter-snapshots</id>
			<name>jcenter</name>
			<url>http://oss.jfrog.org/artifactory/oss-snapshot-local/</url>
		</pluginRepository>
		<pluginRepository>
			<id>spring-snapshots</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/snapshot</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</pluginRepository>
		<pluginRepository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.0</version>
<!--				<version>3.7.0</version>-->
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF-8</encoding>
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${org.mapstruct.version}</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${org.projectlombok.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
