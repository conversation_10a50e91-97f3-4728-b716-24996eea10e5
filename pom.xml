<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.0.3.RELEASE</version>
    </parent>
    <groupId>com.holderzone</groupId>
    <artifactId>holder-saas-store-media</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>holder-saas-store-media</name>
    <description>holder-saas-store-media</description>
    <packaging>pom</packaging>

    <properties>
        <java.version>1.8</java.version>
        <spring-cloud.version>Hoxton.SR3</spring-cloud.version>
    </properties>

    <modules>
        <module>holder-saas-store-media-core</module>
        <module>holder-saas-store-media-dto</module>
    </modules>

</project>
