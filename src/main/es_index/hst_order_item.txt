DELETE hst_order_item
PUT hst_order_item
{
  "settings": {
    "index.analysis.analyzer.default.type": "ik_max_word",
    "max_result_window": 2147483647
  },
  "mappings": {
    "_doc": {
      "properties": {
        "add_price": {
          "type": "float"
        },
        "attr_total": {
          "type": "float"
        },
        "current_count": {
          "type": "float"
        },
        "enterprise_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "free_count": {
          "type": "float"
        },
        "gmt_create": {
          "type": "date"
        },
        "gmt_modified": {
          "type": "date"
        },
        "guid": {
          "type": "long"
        },
        "is_delete": {
          "type": "long"
        },
        "is_member_discount": {
          "type": "long"
        },
        "is_pay": {
          "type": "long"
        },
        "is_whole_discount": {
          "type": "long"
        },
        "item_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "item_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "item_state": {
          "type": "long"
        },
        "item_type": {
          "type": "long"
        },
        "item_type_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "item_type_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "order_guid": {
          "type": "long"
        },
        "original_price": {
          "type": "float"
        },
        "package_default_count": {
          "type": "float"
        },
        "parent_item_guid": {
          "type": "long"
        },
        "price": {
          "type": "float"
        },
        "price_change_type": {
          "type": "long"
        },
        "return_count": {
          "type": "float"
        },
        "sku_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "store_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "total_discount_fee": {
          "type": "float"
        },
        "unit": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "urge_num": {
          "type": "long"
        },
        "user_wx_public_open_id": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "wx_batch": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        }
      }
    }
  }
}