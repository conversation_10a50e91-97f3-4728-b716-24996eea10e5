DELETE /hst_transaction_record
PUT hst_transaction_record
{
  "settings": {
    "index.analysis.analyzer.default.type": "ik_max_word",
    "max_result_window": **********
  },
  "mappings": {
    "_doc": {
      "properties": {
        "amount": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "bank_transaction_id": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "business_day": {
          "type": "date"
        },
        "create_time": {
          "type": "date"
        },
        "discount_fee": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "enterprise_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "face_code": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "gmt_create": {
          "type": "date"
        },
        "gmt_modified": {
          "type": "date"
        },
        "guid": {
          "type": "long"
        },
        "is_delete": {
          "type": "long"
        },
        "member_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "order_guid": {
          "type": "long"
        },
        "payment_type": {
          "type": "long"
        },
        "payment_type_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "refundable_fee": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "staff_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "staff_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "state": {
          "type": "long"
        },
        "store_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "store_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "terminal_id": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "trade_type": {
          "type": "long"
        }
      }
    }
  }
}