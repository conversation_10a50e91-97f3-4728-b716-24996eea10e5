DELETE /hst_discount
PUT hst_discount
{
  "settings": {
    "index.analysis.analyzer.default.type": "ik_max_word",
    "max_result_window": 2147483647
  },
  "mappings": {
    "_doc": {
      "properties": {
        "actually_pay_fee": {
          "type": "float"
        },
        "append_fee": {
          "type": "float"
        },
        "business_day": {
          "type": "long"
        },
        "calculate_by_member_price": {
          "type": "long"
        },
        "change_fee": {
          "type": "float"
        },
        "checkout_device_type": {
          "type": "long"
        },
        "checkout_staff_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "checkout_staff_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "checkout_time": {
          "type": "date"
        },
        "create_staff_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "create_staff_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "device_type": {
          "type": "long"
        },
        "dining_table_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "dining_table_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "enterprise_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "gmt_create": {
          "type": "date"
        },
        "gmt_modified": {
          "type": "date"
        },
        "guest_count": {
          "type": "long"
        },
        "guid": {
          "type": "long"
        },
        "is_delete": {
          "type": "long"
        },
        "main_order_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "member_card_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "member_consumption_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "member_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "order_fee": {
          "type": "float"
        },
        "order_no": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "prepay_fee": {
          "type": "float"
        },
        "print_pre_bill_num": {
          "type": "long"
        },
        "recovery_type": {
          "type": "long"
        },
        "reserve_fee": {
          "type": "float"
        },
        "state": {
          "type": "long"
        },
        "store_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "store_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "trade_mode": {
          "type": "long"
        },
        "upper_state": {
          "type": "long"
        },
        "user_wx_public_open_id": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "virtual_table": {
          "type": "long"
        }
      }
    }
  }
}