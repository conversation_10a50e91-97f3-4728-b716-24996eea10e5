DELETE /hst_order
PUT hst_order
{
  "settings": {
    "index.analysis.analyzer.default.type": "ik_max_word",
    "max_result_window": 2147483647
  },
  "mappings": {
    "_doc": {
      "properties": {
        "actually_pay_fee": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "append_fee": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "business_day": {
          "type": "date"
        },
        "cancel_device_type": {
          "type": "long"
        },
        "cancel_reason": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "cancel_staff_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "cancel_staff_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "cancel_time": {
          "type": "date"
        },
        "change_fee": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "checkout_device_type": {
          "type": "long"
        },
        "checkout_staff_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "checkout_staff_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "checkout_time": {
          "type": "date"
        },
        "create_staff_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "create_staff_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "device_type": {
          "type": "long"
        },
        "dining_table_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "dining_table_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "enterprise_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "gmt_create": {
          "type": "date"
        },
        "gmt_modified": {
          "type": "date"
        },
        "guest_count": {
          "type": "long"
        },
        "guid": {
          "type": "long"
        },
        "is_delete": {
          "type": "long"
        },
        "main_order_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "mark": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "member_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "member_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "member_phone": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "order_fee": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "order_no": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "original_order_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "prepay_fee": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "print_pre_bill_num": {
          "type": "long"
        },
        "qrcode": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "recovery_device_type": {
          "type": "long"
        },
        "recovery_id": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "recovery_reason": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "recovery_staff_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "recovery_staff_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "recovery_type": {
          "type": "long"
        },
        "remark": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "reserve_fee": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "state": {
          "type": "long"
        },
        "store_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "store_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "trade_mode": {
          "type": "long"
        },
        "upper_state": {
          "type": "long"
        },
        "user_wx_public_open_id": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "virtual_table": {
          "type": "long"
        }
      }
    }
  }
}