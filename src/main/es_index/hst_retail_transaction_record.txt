GET /_cat/indices?v&h=health,status,index

DELETE /hst_retail_transaction_record_v1
PUT hst_retail_transaction_record_v1
{
  "settings": {
    "index.analysis.analyzer.default.type": "ik_max_word",
    "max_result_window": **********
  },
  "mappings": {
    "_doc": {
      "properties": {
        "enterprise_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "gmt_create": {
          "type": "date"
        },
        "gmt_modified": {
          "type": "date"
        },
        "is_delete": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "order_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "bank_transaction_id": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "amount": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "trade_type": {
          "type": "long"
        },
        "state": {
          "type": "long"
        },
        "payment_type": {
          "type": "long"
        },
        "payment_type_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "business_day": {
          "type": "date"
        },
        "face_code": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "staff_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "staff_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "store_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "store_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        }
      }
    }
  }
}


