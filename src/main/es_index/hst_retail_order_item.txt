GET /_cat/indices?v&h=health,status,index

DELETE /hst_retail_order_item
PUT hst_retail_order_item
{
  "settings": {
    "index.analysis.analyzer.default.type": "ik_max_word",
    "max_result_window": **********
  },
  "mappings": {
    "_doc": {
      "properties": {
        "enterprise_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "gmt_create": {
          "type": "date"
        },
        "gmt_modified": {
          "type": "date"
        },
        "is_delete": {
          "type": "long"
        },
        "order_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "item_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "item_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "code": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "item_type_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "item_type_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "sku_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "sku_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "price": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "original_price": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "discount_percent": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "total_discount_fee": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "item_type": {
          "type": "long"
        },
        "item_state": {
          "type": "long"
        },
        "current_count": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "free_count": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "return_count": {
          "type": "scaled_float",
          "scaling_factor": 100
        },
        "unit": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "is_whole_discount": {
          "type": "long"
        },
        "is_member_discount": {
          "type": "long"
        },
        "remark": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "store_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "store_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "create_staff_guid": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "create_staff_name": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "is_price_change": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        }
      }
    }
  }
}