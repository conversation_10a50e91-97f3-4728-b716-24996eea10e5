with turnover as (
    with trade AS (
        with trade_sub as (
        SELECT
            ord.guid as "order_guid",
            ord.store_name AS storeName,
            ord.actually_pay_fee - ord.refund_amount + COALESCE(tr.amount,0) order_fee
        FROM
            "hst_trade_{{enterprise_guid}}_db".hst_order ord
            left join "hst_trade_{{enterprise_guid}}_db".hst_transaction_record tr on tr.order_guid = ord.guid
                and tr.state = 4
                and tr.trade_type = 1
        WHERE
            ord.STATE = 4
            [[ AND ord.business_day between {{START_TIME}} and {{END_TIME}} ]]
            AND ord.recovery_type IN ( 1, 3 )
        ),
        groupon as (
        SELECT
            g.order_guid,
            sum( coupon_buy_price ) AS "coupon_buy_price"
        FROM
            trade_sub t
            LEFT JOIN "hst_trade_{{enterprise_guid}}_db".hst_groupon g ON t.order_guid = g.order_guid
        where
            g.is_delete = 0
            and g.refund_order_guid is null
        group by g.order_guid
        )

        SELECT
            td.storeName,
            td.order_fee + COALESCE ( g.coupon_buy_price, 0 ) as "order_fee"
        FROM
            trade_sub td
            LEFT JOIN groupon g on g.order_guid = td.order_guid
    ),
    takeaway AS (
    SELECT
        ord.store_name AS storeName,
        CASE is_refund_success
            WHEN '1'
                THEN COALESCE(ord.customer_actual_pay,0) - COALESCE(ord.customer_refund,0)
            ELSE ord.customer_actual_pay
        END AS order_fee
    FROM
        "hst_takeaway_{{enterprise_guid}}_db".hst_takeout_order ord
    WHERE
        ord.order_type = 0
        [[ and ord.business_day between {{START_TIME}} and {{END_TIME}} ]]
        AND ord.order_status != '-1' and ord.refund_status != 2
    )

    SELECT
        a.storeName,
        ROUND( SUM ( COALESCE(a.order_fee, 0) ), 2 ) AS order_fee
    FROM
        (
        SELECT
            *
        FROM
            trade

        UNION ALL

        SELECT
            *
        FROM
            takeaway
        ) a
    group by
        a.storeName
),
itemAmount AS (
select
    o.store_name AS storeName,
    ROUND( SUM ( oi.discount_total_price ), 2 ) AS 商品总额
from
    "hst_trade_{{enterprise_guid}}_db".hst_order o
    left join "hst_trade_{{enterprise_guid}}_db".hst_order_item oi on oi.order_guid = o.guid
where
    o.copy_order_guid = 0
    and o.state in (4)
    and o.recovery_type in (1,3)
    [[ and o.business_day between {{START_TIME}} and {{END_TIME}} ]]
group BY
    o.store_name
),
storeDate AS (
select
    o.store_name AS storeName,
    ROUND( SUM ( o.append_fee ), 2 ) AS 附加费总额,
    ROUND( SUM ( d.discount_fee ), 2 ) AS 优惠总金额,
    ROUND( SUM (
    CASE d.discount_type
        WHEN 1 THEN d.discount_fee
        WHEN 9 THEN d.discount_fee
        ELSE 0
    END ), 2 ) AS 会员价优惠,
    ROUND( SUM (
    CASE d.discount_type
        WHEN 5 THEN d.discount_fee
        ELSE 0
    END ), 2 ) AS 赠送金额,
    ROUND( SUM (
    CASE d.discount_type
        WHEN 10 THEN d.discount_fee
        ELSE 0
    END ), 2 ) AS 单品折扣,
    ROUND( SUM (
    CASE d.discount_type
        WHEN 2 THEN d.discount_fee
        ELSE 0
    END ), 2 ) AS 整单折扣,
    ROUND( SUM (
    CASE d.discount_type
        WHEN 3 THEN d.discount_fee
        ELSE 0
    END ), 2 ) AS 整单让价,
    ROUND( SUM (
    CASE d.discount_type
        WHEN 7 THEN d.discount_fee
        ELSE 0
    END ), 2 ) AS 代金券,
    ROUND( SUM (
    CASE d.discount_type
        WHEN 12 THEN d.discount_fee
        ELSE 0
    END ), 2 ) AS 满减满折,
    ROUND( SUM (
    CASE d.discount_type
        WHEN 6 THEN d.discount_fee
        ELSE 0
    END ), 2 ) AS 团购优惠
from
    "hst_trade_{{enterprise_guid}}_db".hst_order o
    left join "hst_trade_{{enterprise_guid}}_db".hst_discount d on d.order_guid = o.guid
        and d.discount_type != 14
        and d.discount_fee > 0
where
    o.copy_order_guid = 0
    and o.state in (4,5)
    and o.recovery_type in (1,3)
    [[ and o.business_day between {{START_TIME}} and {{END_TIME}} ]]
group BY
    o.store_name
)

select
    s.storeName AS 门店名称,
    COALESCE(t.order_fee,0) AS 营业额,
    COALESCE(ia.商品总额,0) as 商品总额,
    s.附加费总额,
    s.优惠总金额,
    s.会员价优惠,
    s.赠送金额,
    s.单品折扣,
    s.整单折扣,
    s.整单让价,
    s.代金券,
    s.满减满折,
    s.团购优惠
FROM
    storeDate s
    LEFT JOIN turnover t on t.storeName = s.storeName
    LEFT JOIN itemAmount ia on ia.storeName = s.storeName

