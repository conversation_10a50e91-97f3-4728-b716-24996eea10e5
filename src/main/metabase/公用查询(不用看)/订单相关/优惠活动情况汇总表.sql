select
    CASE d.discount_type
        WHEN 7 THEN d.discount_name || '-' || mv.volume_name
        WHEN 11 THEN d.discount_name || '-' || mv.volume_name
        ELSE d.discount_name
    END AS 优惠类型,
    o.business_day AS 营业日,
    o.store_name AS 门店,
    COUNT ( DISTINCT o.guid ) AS 优惠订单数,
    ROUND( SUM ( o.order_fee ), 2 ) AS 优惠订单合计应收金额,
    ROUND( SUM ( d.discount_fee ), 2 ) AS 合计优惠金额,
    ROUND( SUM ( o.actually_pay_fee ), 2 ) AS 优惠订单合计实收金额
from
    "hst_trade_{{enterprise_guid}}_db".hst_discount d
    join "hst_trade_{{enterprise_guid}}_db".hst_order o on d.order_guid = o.guid
    left join hsm_alliance_member_platform_db.hsa_member_consumption_use_discounts md on md.order_number = o.guid::text
    left join hsm_alliance_member_platform_db.hsm_member_info_volume mv ON md.discount_guid = mv.guid
        AND md.order_number IS NOT NULL
        AND md.discount_type = 7
where
    o.copy_order_guid = 0
    and d.discount_type != 14
    and d.discount_fee > 0
    and o.state in (4,5)
    and o.recovery_type in (1,3)
    [[ and o.business_day between {{START_TIME}} and {{END_TIME}} ]]
    [[ and o.store_name ilike concat('%',{{STORE_NAME}},'%') ]]
    [[ and d.discount_type = {{DISCOUNT_TYPE}}::int ]]
group BY
    CASE d.discount_type
        WHEN 7 THEN d.discount_name || '-' || mv.volume_name
        WHEN 11 THEN d.discount_name || '-' || mv.volume_name
        ELSE d.discount_name
    END,
    o.business_day,
    o.store_name