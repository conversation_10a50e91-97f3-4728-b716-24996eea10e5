select
    o.order_no AS 订单号,
    o.store_name AS 门店,
    replace(o.dining_table_name, '-', '') AS 桌台,
    o.business_day AS 营业日,
    o.member_phone AS 会员账号,
    o.member_name AS 会员名称,
    o.order_fee AS 应收金额,
    CASE d.discount_type
        WHEN 7 THEN d.discount_name || '-' || mv.volume_name
        WHEN 11 THEN d.discount_name || '-' || mv.volume_name
        ELSE d.discount_name
    END AS 优惠名称,
    d.discount_fee AS 优惠金额,
    o.actually_pay_fee AS 实付金额,
    COALESCE(fd.gift_amount,0) - COALESCE(fd.gift_refund_amount,0) AS 会员赠送金额支付,
    (SELECT
        oi.gmt_create
     FROM
        "hst_trade_{{enterprise_guid}}_db".hst_order_item oi
     where
        oi.order_guid=o.guid
     ORDER BY
        oi.gmt_create
     LIMIT 1
    ) AS 点菜时间,
    o.checkout_time AS 结账时间,
    o.checkout_staff_name AS 结账操作人
from
    "hst_trade_{{enterprise_guid}}_db".hst_order o
    left join "hst_trade_{{enterprise_guid}}_db".hst_discount d on d.order_guid = o.guid
        and d.discount_type != 14
        and d.discount_fee > 0
    left join hsm_alliance_member_platform_db.hsa_member_consumption_use_discounts md on md.order_number = o.guid::text
    left join hsm_alliance_member_platform_db.hsm_member_info_volume mv ON md.discount_guid = mv.guid
        AND md.order_number IS NOT NULL
        AND md.discount_type = 7
    left join hsm_alliance_member_platform_db.hsa_member_consumption mc ON mc.guid = o.member_consumption_guid
    left join hsm_alliance_member_platform_db.hsa_member_funding_detail fd ON fd.member_consumption_guid = mc.guid
        AND fd.amount_source_type = 3
        AND fd.amount_gift_funding_type = 1
        AND fd.is_valid = 1
where
    o.copy_order_guid = 0
    and o.state in (4,5)
    and o.recovery_type in (1,3)
    [[ and o.business_day between {{START_TIME}} and {{END_TIME}} ]]
    [[ and o.store_name ilike concat('%',{{STORE_NAME}},'%') ]]
    [[ and d.discount_type = {{DISCOUNT_TYPE}}::int ]]
