-- 赠送原因分析
WITH totalAmount AS (
	SELECT
		store_guid AS storeGuid,
		ROUND( SUM ( price * COUNT ), 2 ) AS amount
	FROM
		"hst_trade_{{enterprise_guid}}_db".hst_free_return_item
	WHERE
		TYPE = 2
		AND gmt_create BETWEEN {{START_TIME}} and {{END_TIME}}
	GROUP BY
		store_guid
	)

SELECT
	f.store_name AS 门店,
	f.reason AS 赠送原因,
	COUNT ( DISTINCT f.gmt_create ) AS 赠送次数,
	SUM ( f.COUNT ) AS 赠送数量,
	ROUND( SUM ( f.price * f.COUNT ), 2 ) AS 赠送金额,
	CONCAT (
        CASE
            WHEN ta.amount > 0
            THEN
                ROUND( SUM ( f.price * f.COUNT ) / ta.amount * 100, 2 )
            ELSE 0
		END,
		'%'
	) AS 赠送金额占比
FROM
	"hst_trade_{{enterprise_guid}}_db".hst_free_return_item f
	LEFT JOIN totalAmount ta ON ta.storeGuid = f.store_guid
WHERE
	f.TYPE = 2
	[[ AND f.gmt_create BETWEEN {{START_TIME}} and {{END_TIME}} ]]
GROUP BY
	f.store_name,
	ta.amount,
	f.reason