-- 退单原因分析_退单原因
WITH totalAmount AS (
	SELECT
		store_guid AS storeGuid,
		ROUND( SUM ( price * COUNT ), 2 ) AS amount
	FROM
		"hst_trade_{{enterprise_guid}}_db".hst_free_return_item
	WHERE
		TYPE = 1
		AND gmt_create BETWEEN {{START_TIME}} and {{END_TIME}}
	GROUP BY
		store_guid
	)

SELECT
	f.store_name AS 门店,
	f.reason AS 退单原因,
	COUNT ( DISTINCT f.gmt_create ) AS 退单次数,
	SUM ( f.COUNT ) AS 退单数量,
	ROUND( SUM ( f.price * f.COUNT ), 2 ) AS 退单金额,
	CONCAT (
        CASE
            WHEN ta.amount > 0
            THEN
                ROUND( SUM ( f.price * f.COUNT ) / ta.amount * 100, 2 )
            ELSE 0
		END,
		'%'
	) AS 退单金额占比
FROM
	"hst_trade_{{enterprise_guid}}_db".hst_free_return_item f
	LEFT JOIN totalAmount ta ON ta.storeGuid = f.store_guid
WHERE
	f.TYPE = 1
	[[ AND f.gmt_create BETWEEN {{START_TIME}} and {{END_TIME}} ]]
GROUP BY
	f.store_name,
	ta.amount,
	f.reason