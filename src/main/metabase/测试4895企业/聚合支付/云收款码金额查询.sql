WITH paymentInfo AS (
	SELECT
		*
	FROM
		(
		SELECT
			enterprise_guid,
			store_guid,
			app_id,
			app_secret,
			create_time,
			ROW_NUMBER ( ) OVER ( PARTITION BY app_id ORDER BY create_time ASC ) AS row_num
		FROM
			hse_enterprise_db.hse_payment_info
		WHERE
			is_delete = 0
		) subquery
	WHERE
		row_num = 1
	),
	payAppId AS (
	SELECT
		pi.app_id
	FROM
		hsb_business_4895_db.hsb_payment_type bpt
		LEFT JOIN paymentInfo pi ON pi.store_guid = bpt.store_guid
	WHERE
		bpt.payment_type = 1
		AND bpt.STATE = 0
		AND bpt.store_guid IN ( {{STORE_GUID}} )
	)
	SELECT
	COUNT ( DISTINCT ID ) AS "order_count",
	COALESCE( SUM( ROUND( amount :: NUMERIC / 100.0, 2 ) ) ,0) order_fee
FROM
	(
	SELECT ID
		,
		store_name,
		app_id,
		amount,
		pay_power_id,
		pay_power_name,
		pay_state,
		gmt_create,
		pay_guid
	FROM
		hpt_trading_db_01.hpt_order_01
	WHERE
		app_id IN ( SELECT app_id FROM payAppId )
		AND pay_state = 2
		AND pay_power_id IN ( 31, 51 )
		AND gmt_create BETWEEN {{START_TIME}} and {{END_TIME}}
	UNION
	SELECT ID
		,
		store_name,
		app_id,
		amount,
		pay_power_id,
		pay_power_name,
		pay_state,
		gmt_create,
		pay_guid
	FROM
		hpt_trading_db_01.hpt_order_02
	WHERE
		app_id IN ( SELECT app_id FROM payAppId )
		AND pay_state = 2
		AND pay_power_id IN ( 31, 51 )
		AND gmt_create BETWEEN {{START_TIME}} and {{END_TIME}}
	UNION
	SELECT ID
		,
		store_name,
		app_id,
		amount,
		pay_power_id,
		pay_power_name,
		pay_state,
		gmt_create,
		pay_guid
	FROM
		hpt_trading_db_02.hpt_order_01
	WHERE
		app_id IN ( SELECT app_id FROM payAppId )
		AND pay_state = 2
		AND pay_power_id IN ( 31, 51 )
		AND gmt_create BETWEEN {{START_TIME}} and {{END_TIME}}
	UNION
	SELECT ID
		,
		store_name,
		app_id,
		amount,
		pay_power_id,
		pay_power_name,
		pay_state,
		gmt_create,
		pay_guid
	FROM
		hpt_trading_db_02.hpt_order_02
	WHERE
		app_id IN ( SELECT app_id FROM payAppId )
		AND pay_state = 2
		AND pay_power_id IN ( 31, 51 )
		AND gmt_create BETWEEN {{START_TIME}} and {{END_TIME}}
	) pa