-- 门店 订单号 菜品名称（名称+规格） 加菜状态（下单时间大于开台时间20分钟的为加菜）（是/否） 下单时间 开台时间
SELECT
	o.store_name AS 门店,
	o.order_no AS 订单号,
	i.item_type_name,
	CASE
	    WHEN i.sku_name IS NOT null AND i.sku_name <> ''
	    THEN CONCAT(i.item_name,'(',i.sku_name,')')
        ELSE i.item_name
    END AS 菜品名称,
    i.price AS 菜品金额,
    i.current_count AS 菜品数量,
    i.free_count AS 赠菜数量,
    i.return_count AS 退菜数量,
    CASE
        WHEN date_part( 'epoch' , i.gmt_create - o.gmt_create ) > (20*60)
        THEN '是'
        ELSE '否'
    END AS 加菜状态,
    i.gmt_create AS 下单时间,
	o.gmt_create AS 开台时间
FROM
	"hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item i
	LEFT JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o ON i.order_guid = o.guid
WHERE
	o.state = 4
	AND i.parent_item_guid = 0
	AND o.recovery_type in (1,3)
	AND i.is_delete = 0
	[[ AND o.store_name LIKE CONCAT('%',{{STORE_NAME}},'%') ]]
    [[ AND i.item_name LIKE CONCAT('%',{{ITEM_NAME}},'%') ]]
    [[ AND o.checkout_time between {{START_TIME}} and {{END_TIME}} ]]
