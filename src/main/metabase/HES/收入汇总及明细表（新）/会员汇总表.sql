WITH
orderInfo AS (
	SELECT
		c.member_info_guid AS member_info_guid,
		c.business_day AS business_day,
		SUM ( c.order_amount ) AS order_amount,
		SUM ( c.order_discount_amount ) AS order_discount_amount,
		SUM ( c.order_paid_amount ) AS order_paid_amount
	FROM
		"hsm_alliance_member_platform_db".hsa_member_consumption c
		LEFT JOIN "hsm_alliance_member_platform_db".hsa_operation_member_info m ON m.guid = c.member_info_guid
	WHERE
		c.oper_subject_guid IN ( '200814100608985084', '200814100559447054' )
		AND c.is_cancel = 0
		AND c.consumption_type = 0
		[[ AND c.business_day BETWEEN {{START_TIME}} AND {{END_TIME}} ]]
	GROUP BY
		c.member_info_guid,
		c.business_day
),
orderCount AS (
	SELECT
		c.member_info_guid AS member_info_guid,
		COUNT( c.guid ) AS oCount
	FROM
		"hsm_alliance_member_platform_db".hsa_member_consumption c
	WHERE
		c.is_cancel = 0
		AND c.consumption_type = 1
		[[ AND c.business_day BETWEEN {{START_TIME}} AND {{END_TIME}} ]]
	GROUP BY
		c.member_info_guid
),
rancherCount AS (
	SELECT
		c.member_info_guid AS member_info_guid,
		COUNT( c.guid ) rCount
	FROM
		"hsm_alliance_member_platform_db".hsa_member_consumption c
	WHERE
		c.is_cancel = 0
		AND c.consumption_type = 0
		[[ AND c.business_day BETWEEN {{START_TIME}} AND {{END_TIME}} ]]
	GROUP BY
		c.member_info_guid
),
memberAccount AS (
	SELECT
		mc.operation_member_info_guid AS operation_member_info_guid,
		SUM( mc.card_money + mc.gift_money ) AS amount
	FROM
		"hsm_alliance_member_platform_db".hsa_member_info_card mc
		LEFT JOIN "hsm_alliance_member_platform_db".hsa_operation_member_info m ON m.guid = mc.operation_member_info_guid
	WHERE
		mc.card_state = 0
		AND mc.is_del = 0
		AND mc.operation_member_info_guid IN (SELECT member_info_guid FROM orderInfo)
	GROUP BY
		mc.operation_member_info_guid
),
rechargeAmount AS (
	SELECT
		f.member_info_guid AS member_info_guid,
		SUM(f.recharge_amount) AS recharge_amount
	FROM
		"hsm_alliance_member_platform_db".hsa_member_funding_detail f
	WHERE
		f.is_valid = 1
		AND f.amount_source_type = 1
		AND f.amount_recharge_funding_type = 0
		[[ AND f.business_day BETWEEN {{START_TIME}} AND {{END_TIME}} ]]
	GROUP BY
		f.member_info_guid
),
rechargeGiftAmount AS (
	SELECT
		f.member_info_guid AS member_info_guid,
		SUM(f.gift_amount) AS gift_amount
	FROM
		"hsm_alliance_member_platform_db".hsa_member_funding_detail f
	WHERE
		f.is_valid = 1
		AND f.amount_source_type = 1
		AND f.amount_gift_funding_type = 0
		[[ AND f.business_day BETWEEN {{START_TIME}} AND {{END_TIME}} ]]
	GROUP BY
		f.member_info_guid
)

SELECT
    oi.business_day AS 营业日,
    m.phone_num AS 会员账号,
    m.user_name AS 会员名称,
    COALESCE ( m.store_name, '-' )  AS 归属门店,
    ma.amount AS 当前会员余额,
    oc.oCount AS 消费次数,
    oi.order_amount AS （应付）消费金额,
    oi.order_discount_amount AS 消费优惠金额,
    oi.order_paid_amount AS （实付）消费金额,
    rc.rCount AS 充值次数,
		ra.recharge_amount AS 充值金额,
		rga.gift_amount AS 充值赠送金额
FROM
	"hsm_alliance_member_platform_db".hsa_operation_member_info m
	 JOIN orderInfo oi ON m.guid = oi.member_info_guid
	 JOIN memberAccount ma ON ma.operation_member_info_guid = m.guid
	 JOIN orderCount oc ON oc.member_info_guid = m.guid
	 JOIN rancherCount rc ON rc.member_info_guid = m.guid
	 JOIN rechargeAmount ra ON m.guid = ra.member_info_guid
	 JOIN rechargeGiftAmount rga ON m.guid = rga.member_info_guid
	[[
        WHERE (
                m.phone_num ilike concat('%',{{SEARCH}},'%')
                or m.user_name ilike concat('%',{{SEARCH}},'%')
            )
    ]]
