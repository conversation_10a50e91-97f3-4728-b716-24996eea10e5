select
    o.order_no AS 订单号,
    o.store_name AS 门店,
    replace(o.dining_table_name, '-', '') AS 桌台,
    o.business_day AS 营业日,
    o.member_phone AS 会员账号,
    o.member_name AS 会员名称,
    o.order_fee AS 应收金额,
    d.discount_name AS 优惠名称,
    d.discount_fee AS 优惠金额,
    o.actually_pay_fee AS 实付金额,
    (SELECT
        oi.gmt_create
     FROM
        "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item oi
     where
        oi.order_guid = o.guid
     ORDER BY oi.gmt_create
     LIMIT 1
     ) AS 点菜时间,
    o.checkout_time AS 结账时间,
    o.checkout_staff_name AS 结账操作人
from
    "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o
    left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount d on d.order_guid = o.guid and d.discount_type != 14 and d.discount_fee > 0
where
    o.copy_order_guid = 0
    and o.state in (4,5)
    and o.recovery_type in (1,3)
    [[ and o.business_day between {{START_TIME}} and {{END_TIME}} ]]
    [[ and o.store_guid in ( {{STORE_GUID}} ) ]]
    [[ and d.discount_type = {{DISCOUNT_TYPE}}::int ]]
