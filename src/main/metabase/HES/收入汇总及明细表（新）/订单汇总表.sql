select
    o.business_day AS 营业日,
    o.store_name AS 门店名称,
    o.order_no AS 订单号,
    replace(o.dining_table_name, '-', '') AS 桌台,
    o.guest_count AS 就餐人数,
    o.append_fee AS 附加费,
    o.order_fee - o.append_fee as 商品金额,
    o.order_fee AS 应收,
    o.order_fee - o.actually_pay_fee AS 优惠金额,
    o.actually_pay_fee AS 实收,
    CASE o.state
        WHEN 1 THEN '待支付'
        WHEN 2 THEN '支付中'
        WHEN 3 THEN '支付失败'
        WHEN 4 THEN '支付成功'
        WHEN 5 THEN '退款'
        WHEN 6 THEN '已作废'
        WHEN 7 THEN '反结账'
    END 订单状态,
    o.refund_amount AS 退款金额,
    CASE o.device_type
	    WHEN 3 THEN '一体机'
		WHEN 4 THEN 'POS机'
		WHEN 5 THEN '云平板'
		WHEN 6 THEN '点菜宝(M1)'
		WHEN 7 THEN 'PV1(带刷卡的点菜宝)'
		WHEN 8 THEN '微信（公众号）'
		WHEN 15 THEN '通吃岛'
    END 订单来源,
    o.member_phone AS 会员账号,
    o.create_staff_name AS 创建操作人,
	o.checkout_staff_name AS 结账操作人,
    DATE_FORMAT(o.checkout_time,'%Y/%m/%d %T' ) AS 结账时间,
    CASE o.trade_mode
        WHEN 0 THEN '正餐'
        WHEN 1 THEN '快餐'
    END AS 正餐或者快餐
from
    hst_order o
where
    o.copy_order_guid = 0
    and o.state in (4,5)
    and o.recovery_type in (1,3)
    [[ and o.business_day between {{START_TIME}} and {{END_TIME}} ]]
    [[ and o.store_guid in ({{STORE_GUID}}) ]]
    [[ and o.order_no = {{ORDER_NO}} ]]
    [[ and o.trade_mode = {{TRADE_MODE}} ]]