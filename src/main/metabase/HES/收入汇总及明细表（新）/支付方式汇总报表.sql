select
    o.business_day AS 营业日,
    o.store_name AS 门店名称,
    tr.payment_type_name AS 支付方式,
    o.order_no AS 订单号,
    tr.amount - tr.refund_amount AS 支付金额,
    CASE
        WHEN (tr.payment_type = 1 or tr.payment_type = 10)
            THEN '线下支付'
        WHEN tr.payment_type = 4
            THEN '会员支付'
        WHEN (tr.payment_type = 2 or tr.payment_type = 3)
            THEN (
                CASE SUBSTR(tr.bank_transaction_id,1,2)
            		WHEN 44
            		    THEN '工商银行'
            		WHEN 20
            		    THEN '银联'
            		WHEN 24
            		    THEN '建设银行'
            		else ''
            	END
            )
        else ''
    END AS 支付渠道,
    CASE o.state
        WHEN 1
            THEN '待支付'
        WHEN 2
            THEN '支付中'
		WHEN 3
            THEN '支付失败'
		WHEN 4
		    THEN '支付成功'
		else ''
    END 支付状态,
    tr.bank_transaction_id as 银行流水号,
    '' AS 券码
from
    hst_order o
    left join hst_transaction_record tr on tr.order_guid = o.guid
where
    o.copy_order_guid = 0
    and o.state in (4,5)
    and o.recovery_type in (1,3)
    and tr.is_delete=0
    and tr.payment_type != 20
    and tr.payment_type != 13
    [[ and o.business_day between {{START_TIME}} and {{END_TIME}} ]]
    [[ and o.store_guid in ({{STORE_GUID}}) ]]
    [[ and o.order_no = {{ORDER_NO}} ]]
    [[ and tr.payment_type = {{PAYMENT_TYPE}} ]]

union all

select
    o.business_day AS 营业日,
    o.store_name AS 门店名称,
    '第三方团购' AS 支付方式,
    o.order_no AS 订单号,
    g.amount AS 支付金额,
    CASE g.groupon_type
        WHEN 6
            THEN '美团团购'
        WHEN 61
            THEN '抖音团购'
        WHEN 62
            THEN '大众点评'
        WHEN 63
            THEN '赚餐'
        WHEN 64
            THEN '其他'
        WHEN 65
            THEN '支付宝团购'
        WHEN 66
            THEN '农行团购'
        else ''
    END AS 支付渠道,
    CASE o.state
        WHEN 1
            THEN '待支付'
        WHEN 2
            THEN '支付中'
		WHEN 3
            THEN '支付失败'
		WHEN 4
		    THEN '支付成功'
		else ''
    END 支付状态,
    '' as 银行流水号,
    g.code AS 券码
from
    hst_order o
    join hst_groupon g on g.order_guid = o.guid
where
    o.copy_order_guid = 0
    and o.state in (4,5)
    and o.recovery_type in (1,3)
    and g.refund_order_guid is null
    [[ and o.business_day between {{START_TIME}} and {{END_TIME}} ]]
    [[ and o.store_guid in ({{STORE_GUID}}) ]]
    [[ and o.order_no = {{ORDER_NO}} ]]
    [[ and 13 = {{PAYMENT_TYPE}} ]]