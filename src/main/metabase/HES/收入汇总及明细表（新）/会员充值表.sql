(SELECT
	c.order_number 订单编号,
	(CASE WHEN c.consumption_type = 0 THEN '充值' ELSE '消费' END) 类型,
	c.member_store_name 门店,
	mc.card_name 会员卡,
	m.nick_name 姓名,
	w.pay_name 支付方式,
	m.phone_num 手机号,
	c.order_amount 订单金额,
	c.order_paid_amount 订单实付金额,
	c.order_discount_amount 订单赠送金额,
	c.card_balance_pay_amount 卡余额支付金额,
	c.card_residual_balance 卡剩余金额,
	f.gift_amount 赠送金额,
	c.consumption_time 时间,
	c.operator_account_name 操作人,
	c.remark 备注
FROM
	"hsm_alliance_member_platform_db".hsa_member_consumption c
	LEFT JOIN "hsm_alliance_member_platform_db".hsa_member_info_card mc ON mc.guid = c.member_info_card_guid
	LEFT JOIN "hsm_alliance_member_platform_db".hsa_member_funding_detail f ON c.guid = f.member_consumption_guid
	LEFT JOIN "hsm_alliance_member_platform_db".hsa_operation_member_info m ON m.guid = c.member_info_guid
	LEFT JOIN "hsm_alliance_member_platform_db".hsa_member_consumption_pay_way w ON w.consumption_guid = c.guid
WHERE
	c.oper_subject_guid IN ( '200814100608985084', '200814100559447054' )
	AND c.is_cancel = 0
	AND c.consumption_type = 0
	[[AND c.consumption_time between {{START_TIME}} AND {{END_TIME}} ]]
	)
union all
(SELECT
	'' as 订单编号,
	(CASE WHEN f.amount_recharge_funding_type = 0 THEN '充值' ELSE '消费' END) 类型,
	f.store_name 门店,
	mc.card_name 会员卡,
	m.nick_name 姓名,
	'后台调整' as 支付方式,
	m.phone_num 手机号,
	f.recharge_amount 订单金额,
	f.recharge_amount 订单实付金额,
	0 订单赠送金额,
	0 卡余额支付金额,
	f.card_recharge_residual_balance + f.card_gift_residual_balance 卡剩余金额,
	f.gift_amount 赠送金额,
	f.gmt_create 时间,
	f.operator_account_name 操作人 ,
	f.remark 备注
FROM
	"hsm_alliance_member_platform_db".hsa_member_funding_detail f
	LEFT JOIN "hsm_alliance_member_platform_db".hsa_member_info_card mc ON mc.guid = f.member_info_card_guid
	LEFT JOIN "hsm_alliance_member_platform_db".hsa_operation_member_info m ON m.guid = f.member_info_guid
WHERE
	f.oper_subject_guid IN ( '200814100608985084', '200814100559447054' )
	AND f.is_valid = 1
	AND f.amount_recharge_funding_type = 0
	AND f.amount_source_type in (0,4)
	[[AND f.gmt_create between {{START_TIME}} AND {{END_TIME}} ]]
	)