

-- 收款明细
select * from(
	SELECT
		o.store_name AS 门店名称,
		o.order_no AS 订单号,
		CONCAT(' ',o.guid) AS 订单guid,
		CONCAT(' ',r.guid) AS 支付guid,
		replace(o.dining_table_name, '-', '') AS 桌台,
		o.guest_count 就餐人数,
		o.business_day AS 营业日,
	    DATE_FORMAT(o.gmt_create,'%Y/%m/%d %T' ) as 点单时间,
	    o.append_fee AS 附加费,
o.order_fee-o.append_fee as 商品金额,
o.order_fee AS 应收,
		(SELECT GROUP_CONCAT(concat(d.discount_name,':￥',d.discount_fee) SEPARATOR ',') FROM hst_discount d where d.order_guid=o.guid and d.discount_type != 14 and d.discount_type != 6 and d.discount_fee > 0) as 优惠详情,
		(SELECT GROUP_CONCAT(concat(d.discount_name,':￥',d.discount_fee) SEPARATOR ',') FROM hst_discount d where d.order_guid=o.guid and d.discount_type = 6 and d.discount_fee > 0) AS 团购验券,
		CASE o.device_type
	WHEN 3 THEN
		'一体机'
		WHEN 4 THEN
		'POS机'
		WHEN 5 THEN
		'云平板'
		WHEN 6 THEN
		'点菜宝(M1)'
		WHEN 7 THEN
		'PV1(带刷卡的点菜宝)'
		WHEN 8 THEN
		'微信（公众号）'
		WHEN 15 THEN
		'通吃岛'
END '订单来源',
		o.trade_mode AS 正餐0快餐1,
		o.member_phone AS 会员账号,
		r.payment_type_name AS 支付方式,
		CASE SUBSTR(r.bank_transaction_id,1,2)
		WHEN 44
		THEN '工商银行'
		WHEN 20
		THEN '银联'
		WHEN 24
		THEN '建设银行'
		else '' END '支付渠道',
			CASE WHEN o.state=3 AND payment_type=2
	 THEN
		'支付失败'
		WHEN  o.state=4 AND payment_type=2 THEN
		'支付成功'
		else
		''
END '支付状态',
		'' as 券码,
	    r.amount AS 当前支付方式支付金额,
		r.bank_transaction_id as 银行流水号,
		o.create_staff_name AS 创建操作人,
		o.checkout_staff_name AS 结账操作人,
	    DATE_FORMAT(o.checkout_time,'%Y/%m/%d %T' ) as 结账时间,
		case when o.trade_mode=0 then '正餐' else '快餐' end AS 正餐或者快餐
	FROM
		hst_order o
	    JOIN hst_transaction_record r ON o.guid = r.order_guid and r.payment_type not in(20,61,62,65)
	WHERE
		o.state in (3,4)
	    and r.state in (3,4)
	    and r.is_delete=0
	    and o.recovery_type in (1,3)
	    and o.copy_order_guid = 0
	   -- AND o.checkout_time >= CONCAT(DATE_SUB(curdate(),INTERVAL 1 DAY)," ",'08:00:00')
    	-- AND o.checkout_time <= CONCAT(curdate()," ",'08:00:00')
[[ and o.checkout_time between {{START_TIME}} and {{END_TIME}} ]]
[[and o.store_guid in ({{STORE_GUID_MULTI}})]]
union all
	SELECT
		o.store_name AS 门店名称,
		o.order_no AS 订单号,
		CONCAT(' ',o.guid) AS 订单guid,
		'' AS 支付guid,
		replace(o.dining_table_name, '-', '') AS 桌台,
		o.guest_count 就餐人数,
		o.business_day AS 营业日,
	    DATE_FORMAT(o.gmt_create,'%Y/%m/%d %T' ) as 点单时间,
	    o.append_fee AS 附加费,
o.order_fee-o.append_fee as 商品金额,
o.order_fee AS 应收,
		(SELECT GROUP_CONCAT(concat(d.discount_name,':￥',d.discount_fee) SEPARATOR ',') FROM hst_discount d where d.order_guid=o.guid and d.discount_type != 14 and d.discount_type != 6 and d.discount_fee > 0) as 优惠详情,
		(SELECT GROUP_CONCAT(concat(d.discount_name,':￥',d.discount_fee) SEPARATOR ',') FROM hst_discount d where d.order_guid=o.guid and d.discount_type = 6 and d.discount_fee > 0) AS 团购验券,
				CASE o.device_type
	WHEN 3 THEN
		'一体机'
		WHEN 4 THEN
		'POS机'
		WHEN 5 THEN
		'云平板'
		WHEN 6 THEN
		'点菜宝(M1)'
		WHEN 7 THEN
		'PV1(带刷卡的点菜宝)'
		WHEN 8 THEN
		'微信（公众号）'
		WHEN 15 THEN
		'通吃岛'
END '订单来源',
		o.trade_mode AS 正餐0快餐1,
		o.member_phone AS 会员账号,
		'团购验卷' AS 支付方式,
		''  支付渠道,
		'' AS 支付状态,
		g.code as 券码,
	    g.amount AS 当前支付方式支付金额,
		'' as 银行流水号,
		o.create_staff_name AS 创建操作人,
		o.checkout_staff_name AS 结账操作人,
	    DATE_FORMAT(o.checkout_time,'%Y/%m/%d %T' ) as 结账时间,
		case when o.trade_mode=0 then '正餐' else '快餐' end AS 正餐或者快餐
	FROM
		hst_order o
	    join hst_groupon g on g.order_guid = o.guid
	where
		g.is_delete = 0
		and o.state=4
	    and o.recovery_type in (1,3)
	    and o.copy_order_guid = 0
	   -- AND o.checkout_time >= CONCAT(DATE_SUB(curdate(),INTERVAL 1 DAY)," ",'08:00:00')
    	-- AND o.checkout_time <= CONCAT(curdate()," ",'08:00:00');
    	 [[ and o.checkout_time between {{START_TIME}} and {{END_TIME}} ]]
         [[and o.store_guid in ({{STORE_GUID_MULTI}})]]
) as temp order by 订单guid,券码