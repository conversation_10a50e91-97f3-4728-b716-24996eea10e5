-- 套餐菜品销售明细 重构
-- CREATE INDEX hst_order_item_idx_item_name ON "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item USING btree (item_name);
select
    ho.order_no 订单号,
    ho.store_name 门店名称,
    '堂食' 销售类型,
    hoi.item_name 商品名称,
    hoi.unit 记数单位,
    hoi.sku_name 商品规格,
    hoi.code 商品编号,
    -- hoii.unit,
    hoi.current_count * hoi.package_default_count * (hoii.current_count - hoii.refund_count + hoii.free_refund_count ) 点餐数量,
    hoi.free_count * hoii.free_count 赠送数量,
    hoii.price * hoii.current_count 商品总额,
    hoii.accounting_price 核算单价,
    hoi.current_count * hoi.package_default_count * (hoii.current_count - hoii.refund_count + hoii.free_refund_count ) 核算数量,
    coalesce(hoi.current_count * hoi.package_default_count * (hoii.current_count - hoii.refund_count + hoii.free_refund_count ),0) * coalesce(hoii.accounting_price,0) 核算商品总额,
    ho.append_fee 附加费,
    hoii.attr_total 属性加价,
    hoi.member_price 会员价格,
    hoii.item_name 套餐名称,
    ho.state 订单状态,
    ho.gmt_create 下单时间,
    ho.checkout_time 结账时间,
    hoii.item_type 商品类型1套餐其他普通,
    ho.create_staff_name 下单操作员
from "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item hoi -- 单品
	inner join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item hoii on hoi.parent_item_guid = hoii.guid -- 套餐
	left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order ho on hoi.order_guid = ho.guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
where
    [[ {{SALES_TYPE}}::int[] -- ]] '{0,1}'::int[]
        @>array[0]
    and acl.chmod
	and ho.copy_order_guid = 0
    and ho.state = 4
    and ho.recovery_type in (1,3)
    and hoi.is_delete = 0
    and hoii.parent_item_guid = 0
    and hoii.item_type = 1
    and hoii.current_count > '0'::numeric
    [[
        -- 统计时间归属节点
        -- 1-下单时间,2-结账时间,3-营业时间
        and case {{ATTRIBUTE}}
            when '1' then (
                true
                [[and ho.gmt_create between {{START_TIME}} AND {{END_TIME}}]]
            )
            when '2' then (
                true
                [[and ho.checkout_time between {{START_TIME}} AND {{END_TIME}}]]
            )
            when '3' then (
                true
                [[and ho.business_day between {{START_TIME}} AND {{END_TIME}}]]
                [[and hoi.business_day between {{START_TIME}} AND {{END_TIME}}]]
                [[and hoii.business_day between {{START_TIME}} AND {{END_TIME}}]]
            )
        end
	]]
    and case acl.list
        when 'true' then true
        when 'false' then false
        else (ho.store_guid = any(acl.list::text[]))
    end
    -- 订单号
    [[ and ho.order_no ~~ concat('%',{{ORDER_NO}},'%') ]]
    -- 单品
    [[ and hoi.item_name ~~ concat('%',{{ITEM_NAME}},'%') ]]
    -- 套餐名称
    [[ and hoii.item_name ~~ concat('%',{{ITEM_TYPE_NAME}},'%') ]]

union all

select
	hto.order_id 订单号,
	coalesce(hti.store_name,hto.store_name) 门店名称,
	'外卖' 销售类型,
	htp.item_name 商品名称,
	htp.unit 记数单位,
	hti.item_spec 商品规格,
	htp.code 商品编号,
	htp.item_count 点餐数量,
	0 赠送数量,
	hti.item_total 商品总额,
    hti.takeaway_accounting_price 核算单价,
    hti.actual_item_count 核算数量,
    coalesce(hti.actual_item_count,0) * coalesce(hti.takeaway_accounting_price,0) 核算商品总额,
	0 附加费,
	0 属性加价,
	null 会员价格,
	hti.erp_item_name 套餐名称,
	hto.order_status 订单状态,
	hto.gmt_create 下单时间,
	hto.complete_time 结账时间,
	1 商品类型1套餐其他普通,
	hto.accept_staff_name 下单操作员
from
	"hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_package htp
	left join "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_item hti on htp.takeout_item_guid = hti.item_guid
	left join "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order hto on hti.order_guid = hto.order_guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
where
    [[ {{SALES_TYPE}}::int[] -- ]] '{0,1}'::int[]
        @>array[1]
    and acl.chmod
	and htp.is_delete = 0
	and htp.ods_delete_time isnull
	and hti.ods_delete_time isnull
	and hto.ods_delete_time isnull
    and case acl.list
        when 'true' then true
        when 'false' then false
        else (hto.store_guid = any(acl.list::text[]))
    end
    [[and hti.business_day between {{START_TIME}} AND {{END_TIME}}]]
    [[and hto.business_day between {{START_TIME}} AND {{END_TIME}}]]
    [[and hto.order_id ~* {{ORDER_NO}}]]
