-- 堂食收款明细
SELECT
    o.order_no AS 订单号,
	o.store_name AS 门店名称,
    -- DATE_FORMAT(o.checkout_time,'%Y/%m/%d %T' ) as 结账时间,
	o.checkout_time 结账时间,
    o.order_fee AS 订单金额,
    o.actually_pay_fee - o.refund_amount AS 订单实际收款金额,
    r.payment_type_name AS 收款方式,
    r.amount AS 当前支付方式支付金额,
    r.bank_transaction_id as 银行流水号,
    o.business_day::text AS 营业日,
    o.create_staff_name AS 操作人,
    case when o.trade_mode=0 then '正餐' else '快餐' end AS 正餐或者快餐,
    o.member_phone AS 会员账号,
    REPLACE(o.dining_table_name,'-','') AS 桌台,
    -- DATE_FORMAT(o.gmt_create,'%Y/%m/%d %T' ) as 点单时间,
    o.gmt_create 下单时间,
	CONCAT(' ',o.guid) AS 订单guid,
	CONCAT(' ',r.guid) AS 支付guid
	-- o.trade_mode AS 正餐0快餐1
FROM "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o
    JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_transaction_record r ON o.guid = r.order_guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
    acl.chmod
	and o.copy_order_guid = 0
	and o.state = 4
    and r.state = 4
    and r.is_delete = 0
    and o.recovery_type in (1,3)
    -- AND o.checkout_time >= CONCAT(DATE_SUB(curdate(),INTERVAL 1 DAY)," ",'08:00:00')
    -- AND o.checkout_time <= CONCAT(curdate()," ",'08:00:00');
	[[
	   -- 统计时间归属节点
	   -- 1-下单时间,2-结账时间,3-营业时间
	    and case {{ATTRIBUTE}}
	        when '1' then (
	            true
	            [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}}]]
	        )
	        when '2' then (
	            true
	            [[and o.checkout_time between {{START_TIME}} AND {{END_TIME}}]]
	        )
            when '3' then (
                true
                [[and o.business_day between {{START_TIME}} AND {{END_TIME}}]]
            )
        end
	]]
    and case acl.list
        when 'true' then true
        when 'false' then false
        else (o.store_guid = any(acl.list::text[]))
    end
    [[ and o.order_no ~~ concat('%',{{ORDER_NO}},'%') ]]
    [[ and r.payment_type_name = any({{PAYMENT_TYPE_NAME}}::text[]) ]]

UNION ALL

SELECT
    o.order_no AS 订单号,
	o.store_name AS 门店名称,
    -- DATE_FORMAT(o.checkout_time,'%Y/%m/%d %T' ) as 结账时间,
	o.checkout_time 结账时间,
    o.order_fee AS 订单金额,
    0 AS 订单实际收款金额,
    r.payment_type_name AS 收款方式,
    r.amount AS 当前支付方式支付金额,
    r.bank_transaction_id as 银行流水号,
    o.business_day::text AS 营业日,
    o.create_staff_name AS 操作人,
    case when o.trade_mode=0 then '正餐' else '快餐' end AS 正餐或者快餐,
    o.member_phone AS 会员账号,
    REPLACE(o.dining_table_name,'-','') AS 桌台,
    -- DATE_FORMAT(o.gmt_create,'%Y/%m/%d %T' ) as 点单时间,
    o.gmt_create 下单时间,
	CONCAT(' ',o.guid) AS 订单guid,
	CONCAT(' ',r.guid) AS 支付guid
	-- o.trade_mode AS 正餐0快餐1
FROM "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o
left JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order ro ON ro.guid = o.refund_order_guid
    JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_transaction_record r ON ro.guid = r.order_guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
    acl.chmod
	and o.copy_order_guid = 0
	and o.state = 4
	AND o.refund_order_guid is not null
    and r.state = 4
    and r.is_delete = 0
    and o.recovery_type in (1,3)
    -- AND o.checkout_time >= CONCAT(DATE_SUB(curdate(),INTERVAL 1 DAY)," ",'08:00:00')
    -- AND o.checkout_time <= CONCAT(curdate()," ",'08:00:00');
	[[
	   -- 统计时间归属节点
	   -- 1-下单时间,2-结账时间,3-营业时间
	    and case {{ATTRIBUTE}}
	        when '1' then (
	            true
	            [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}}]]
	        )
	        when '2' then (
	            true
	            [[and o.checkout_time between {{START_TIME}} AND {{END_TIME}}]]
	        )
            when '3' then (
                true
                [[and o.business_day between {{START_TIME}} AND {{END_TIME}}]]
            )
        end
	]]
    and case acl.list
        when 'true' then true
        when 'false' then false
        else (o.store_guid = any(acl.list::text[]))
    end
    [[ and o.order_no ~~ concat('%',{{ORDER_NO}},'%') ]]
    [[ and r.payment_type_name = any({{PAYMENT_TYPE_NAME}}::text[]) ]]

