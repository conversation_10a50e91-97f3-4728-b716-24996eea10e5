-- 订单明细

SELECT
    o.order_no 订单号,
    o.store_name as 门店,
    o.guest_count 就餐人数,
    order_fee - append_fee 商品金额,
    o.append_fee AS 附加费,
    o.order_fee AS 订单金额,
    r.payment_type_name 收款方式,
    o.order_fee - o.actually_pay_fee 优惠总额,
    o.actually_pay_fee - o.refund_amount AS 实收金额,
    o.gmt_create 下单时间,
    o.checkout_time 结账时间,
    o.guid::TEXT 订单guid
FROM
    "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o
    LEFT JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_transaction_record r ON r.order_guid=o.guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
    acl.chmod
	and o.copy_order_guid = 0
    and o.state = 4
    and o.recovery_type in (1,3)
	[[
	   -- 统计时间归属节点
	   -- 1-下单时间,2-结账时间
	    and case {{ATTRIBUTE}}
	        when '1' then (
	            true
	            [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}}]]
	        )
	        when '2' then (
	            true
	            -- checkout_time
	            [[and o.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
	        )
        end
	]]
    and case acl.list
        when 'true' then true
        when 'false' then false
        else (o.store_guid = any(acl.list::text[]))
    end
    [[ and o.order_no ~~ concat('%',{{ORDER_NO}},'%') ]]
    [[ and r.payment_type_name = any({{PAYMENT_TYPE_NAME}}::text[]) ]]
