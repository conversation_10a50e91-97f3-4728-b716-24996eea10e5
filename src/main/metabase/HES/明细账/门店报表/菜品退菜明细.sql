-- 菜品退菜明细
SELECT
    o.order_no AS 订单号,
    o.store_name AS 门店名称,
    i.item_name AS 商品名称,
    i.item_type_name AS 商品分类,
    i.code 菜品编号,
    i.unit AS 记数单位,
    i.current_count - i.refund_count + i.free_refund_count AS 点餐数量,
    i.free_count - i.free_refund_count AS 赠送数量,
    i.return_count + i.refund_count AS 退菜数量,
    case when f.item_state in (7,8) then '已出堂' else '未出堂' end AS 是否出堂,
    f.reason AS  退菜理由,
    f.gmt_modified AS 退菜时间,
    f.staff_name AS 退菜操作人,
    o.state AS 订单状态,
    o.gmt_create AS 下单时间,
    o.create_staff_name AS 下单操作员,
    i.item_type AS 商品类型1套餐其他普通,
    o.checkout_time AS 结账时间,
    i.price*(i.current_count+i.return_count) AS 商品总额,-- 退款数量包含在当前数量里
    i.price*(i.return_count + i.refund_count) AS 退菜总额,
    o.append_fee as 附加费,
    i.attr_total AS 属性加价,
    o.guid::TEXT AS 订单guid
from
    "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_free_return_item f
    left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item i on f.order_item_guid = i.guid
    LEFT JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o ON i.order_guid = o.guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
    acl.chmod
	and o.copy_order_guid = 0
	and o.state = 4
    AND i.parent_item_guid = 0
    AND i.return_count > 0
	[[
	   -- 统计时间归属节点
	   -- 1-下单时间,2-退菜时间
	    and case {{ATTRIBUTE}}
	        when '1' then (
	            true
	            [[and o.gmt_create between {{START_TIME}} AND {{END_TIME}}]]
	        )
	        when '2' then (
	            true
                [[and o.business_day between date_trunc('day',{{START_TIME}} - interval '7 day') and date_trunc('day',{{END_TIME}} + interval '7 day')]]
                [[and i.business_day between date_trunc('day',{{START_TIME}} - interval '7 day') and date_trunc('day',{{END_TIME}} + interval '7 day')]]
	            [[and f.gmt_modified between {{START_TIME}} AND {{END_TIME}}]]
	        )
        end
	]]
    and case acl.list
        when 'true' then true
        when 'false' then false
        -- when 'false' then true  --debug
        else (o.store_guid = any(acl.list::text[]))
    end
    [[ and o.order_no ~~ concat('%',{{ORDER_NO}},'%') ]]
    [[ and i.item_name ~~ concat('%',{{ITEM_NAME}},'%') ]]
    -- 1-已出堂,2-未出堂
    [[
        and case {{ITEM_STATE}}
            when '1' then (
                f.item_state in (7,8)
            )
            when '2' then (
                f.item_state not in (7,8)
            )
        end
    ]]
    [[ and f.reason ~~ concat('%',{{REASON}},'%') ]]
