-- 门店菜品销售查询
select
    max(x.store_name) "门店名称",
    max(x.item_type_name) "菜品类型",
    -- max(x.item_name) "菜品名称",
    max(case when x.item_name notnull then x.item_name else x.item_name_m end) "菜品名称",
    max(x.unit) "单位",
    sum(x.item_count) "销售数量合计",
    sum(case when x.order_sub_type = 98 then x.item_count else 0 end) "驻店(正餐+外带)",
	sum(case when x.order_sub_type = 0 then x.item_count else 0 end) "美团",
	sum(case when x.order_sub_type = 1 then x.item_count else 0 end) "饿了吗",
	sum(case when x.order_sub_type = 6 then x.item_count else 0 end) "小程序"
from (
    select
        o.store_guid,
--        i.item_type_guid,
        i.item_guid,
        (i.sku_guid) sku_guid,
        max(o.store_name) "store_name", -- 门店名称
        max(i.item_type_name) "item_type_name", --菜品类型
        max(i.item_name) "item_name_m",
        max(i.item_name) "item_name", -- 菜品名称
        i.unit "unit", -- 单位
        round(sum(i.current_count + i.free_count - i.return_count - i.refund_count),0) "item_count", -- "驻店(正餐+外带)"
        98 order_sub_type
    from
        "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item i
        left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o on i.order_guid = o.guid,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    where
        acl.chmod
        -- true --debug
        and i.gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
        and o.gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
		and o.copy_order_guid = 0
        and o.is_delete = '0'
        and i.is_delete = '0'
        and o.state = '4'
        and o.store_name like '%He''s%'
        and case acl.list
            when 'true' then true
            when 'false' then false
            else (o.store_guid = any(acl.list::text[]))
        end
        [[and o.gmt_create between {{START_TIME}} and {{END_TIME}}]]
        [[and i.item_type_guid = any({{ITEM_TYPE_GUID}}::text[])]]
        [[and i.item_guid = any({{ITEM_GUID}}::text[])]]
        [[
            and (
                o.store_name ilike concat('%',{{SEARCH}},'%')
                or i.item_type_name ilike concat('%',{{SEARCH}},'%')
                or i.item_name ilike concat('%',{{SEARCH}},'%')
            )
        ]]
    group by
        o.store_guid, --门店
        i.item_type_guid, --菜品类型
        i.item_guid, --菜品名称
        i.unit, --单位
        i.sku_guid
	union all
    select
        o.store_guid,
--        '' item_type_guid,
        i.item_guid,
        max(i.item_sku) sku_guid,
--        max(i.item_code) item_code,
        max(o.store_name) store_name,
        '' "item_type_name",
        max(i.item_name) item_name_m,
        max(hi.name) item_name,
        (i.item_unit) unit,
        sum(i.item_count) item_count,
        o.order_sub_type
    from
        "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_item i
        left join "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order o on i.order_guid = o.order_guid
        left join "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_sku s on s.guid = i.item_sku
        left join "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_item hi on s.item_guid = hi.guid,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    where
        acl.chmod
        -- true --debug
        and i.gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
        and o.gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
        and o.order_status = 100
        -- and o.store_name like '%He''s%'
--        and o.enterprise_guid = '887f2181-eb06-4d77-b914-7c37c884952c'
        and o.order_sub_type in (0,1,6)
        [[and o.gmt_create between {{START_TIME}} and {{END_TIME}}]]
        and case acl.list
            when 'true' then true
            when 'false' then false
            else (o.store_guid = any(acl.list::text[]))
        end
        [[and i.item_guid = any({{ITEM_GUID}}::text[])]]
        [[
            and (
                o.store_name ilike concat('%',{{SEARCH}},'%')
                or i.item_name ilike concat('%',{{SEARCH}},'%')
            )
        ]]
    group by
        o.store_guid, --门店
--        i.item_code,
        i.item_guid, --菜品名称
        i.item_unit, --单位
        i.item_sku,
        o.order_sub_type
	) x
group by
	x.store_guid,
	x.sku_guid
-- 	x.item_guid