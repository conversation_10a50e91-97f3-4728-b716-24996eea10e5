-- 门店菜品查询表
-- 正餐 + 外带
-- explain analyse
select
    max(o.store_name) "门店名称",
    max(i.item_type_name) "菜品类型",
    max(i.item_name) "菜品名称",
    i.unit "单位",
    round(sum(i.current_count + i.free_count - i.return_count - i.refund_count),0) "驻店(正餐+外带)"
from (
        select
            item_type_guid,
            item_guid,
            unit,
            item_type_name,
            item_name,
            current_count,
            free_count,
            return_count,
            order_guid,
            refund_count
            -- is_delete
        from
            "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item
        where
            is_delete = 0
            and gmt_create between date_trunc('day',{{START_DATE}} - interval '30 day') and date_trunc('day',{{END_DATE}} + interval '7 day')
            [[ and item_type_name ~~ concat('%',{{ITEM_TYPE_GUID}},'%')]]
    ) i
    inner join (
        select
            guid,
            store_guid,
            store_name
            -- state
            -- is_delete
        from
            "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order,
            LATERAL (
                select
                    public.getlist(
                        public.getacl(
                            $privileges_flag,
                            ($power_list)::text
                        ),
                        ($power_list)::text,
                        array[
                            [[ {{STORE_GUID}} -- ]] '-1'
                            ,
                            [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                        ]
                    ) list,
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ) chmod
            ) acl
        where
            acl.chmod
            -- true --debug
            and gmt_create between date_trunc('day',{{START_DATE}} - interval '30 day') and date_trunc('day',{{END_DATE}} + interval '7 day')
			and copy_order_guid = 0
            and is_delete = 0
            and state = 4
            -- and (store_name)::text ~~ '%He''s%'::text
            [[ and gmt_create between {{START_DATE}} and {{END_DATE}} ]]
            and case acl.list
                when 'true' then true
                when 'false' then false
                -- when 'false' then true  --debug
                else (store_guid = any(acl.list::text[]))
            end
    ) o on i.order_guid = o.guid
group by
    o.store_guid,
    i.item_type_guid,
    i.item_guid,
    i.unit