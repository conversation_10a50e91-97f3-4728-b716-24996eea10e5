-- 订单商品汇总
-- 图型 区域
-- X 门店名称
-- Y 就餐人数,商品数量,单价
SELECT
	-- cast(ord.guid as char) '订单唯一ID',
	concat(
		'<<<{"type":"modal","url":"/dashboard/2240?order_guid=',
		(ord.guid::text),
		'&开始日期=__<开始日期>__&结束日期=__<结束日期>__&门店=__<门店>__&分组=__<分组>__',
		'"}>>>',
		((ord.guid::text))
	) "订单唯一ID",
	-- ord.order_no '订单号',
	concat(
		'<<<{"type":"modal","url":"/dashboard/2240?order_no=',
		(ord.order_no),
		'&开始日期=__<开始日期>__&结束日期=__<结束日期>__&门店=__<门店>__&分组=__<分组>__',
		'"}>>>',
		(ord.order_no)
	) "订单号",
	CASE  ord.state
	    WHEN 1 THEN '待支付'
		WHEN 2 THEN '支付中'
		WHEN 3 THEN '支付失败'
		WHEN 4 THEN '支付成功'
		WHEN 5 THEN '退款'
		WHEN 6 THEN '已作废'
    END "订单状态",
    ord.store_name "门店名称",
	ord.dining_table_name "桌台号",
	ord.guest_count "就餐人数",
	it.item_guid "商品SPU",
	it.item_name "商品名称",
	it.code 菜品编号,
	it.current_count - it.refund_count + it.free_refund_count "商品数量",
	it.original_price  "原价",
	it.price "单价",
	ord.gmt_create "下单时间",
	it.item_type_name "分类名称",
	ord.checkout_time "结账时间"
FROM
    "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item it
	LEFT JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order ord ON it.order_guid = ord.guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
	acl.chmod
	-- true -- debug
	and it.gmt_create between date_trunc('day',{{START_DATE}} - interval '30 day') and date_trunc('day',{{END_DATE}} + interval '7 day')
	and ord.gmt_create between date_trunc('day',{{START_DATE}} - interval '30 day') and date_trunc('day',{{END_DATE}} + interval '7 day')
	and ord.copy_order_guid = 0
	and it.is_delete = '0'
	AND (it.parent_item_guid = 0 OR parent_item_guid IS NULL)
	[[AND ord.gmt_create between {{START_DATE}} and {{END_DATE}}]]
    and case
        acl.list
        when 'true' then true
        when 'false' then false
        -- when 'false' then true  --debug
        else (ord.store_guid = any(acl.list::text[]))
    end
	-- ord.dining_table_guid
	[[AND ord.dining_table_name = any({{table_guid}}::text[])]]
	-- 1-待支付,2-支付中,3-支付失败,4-支付成功,5-退款,6-已作废
	[[and ord.state = any({{ORDER_STATUS}}::int[])]]