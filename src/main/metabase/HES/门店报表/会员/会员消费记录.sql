--会员消费记录
SELECT
	mc.order_number "订单编号",
    CASE mc.consumption_type
		WHEN 0 THEN '充值'
		WHEN 3 THEN '退款'
        ELSE '消费'
	END "类型",
	mc.member_store_name "门店",
	mic.card_name "会员卡",
	m.user_name "姓名",
	mcp.pay_name "支付方式",
	mcp.pay_amount "支付金额",
	m.phone_num "手机号",
	mc.order_amount "订单金额",
	mc.order_paid_amount "订单实付金额",
	mc.order_discount_amount "订单优惠金额",
	mic.card_money "卡剩余金额",
	mc.consumption_time "消费时间",
	mc.operator_account_name "操作人",
    CASE mc.is_cancel
		WHEN 0 THEN '否'
        ELSE '是'
	END "是否被撤销"
FROM
	"hsm_alliance_member_platform_db".hsa_member_consumption mc
	LEFT JOIN "hsm_alliance_member_platform_db".hsa_member_info_card mic ON mc.member_info_card_guid = mic.guid
	LEFT JOIN "hsm_alliance_member_platform_db".hsa_operation_member_info m ON m.guid = mic.operation_member_info_guid
	LEFT JOIN "hsm_alliance_member_platform_db".hsa_member_consumption_pay_way mcp ON mcp.consumption_guid = mc.guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
    acl.chmod
    -- true --debug
    and mc.member_enterprise_guid = '887f2181-eb06-4d77-b914-7c37c884952c'
	[[AND mc.consumption_time BETWEEN {{ START_DATE }} AND {{ END_DATE }}]]
    and case acl.list
        when 'true' then true
        when 'false' then false
        -- when 'false' then true  --debug
        else (mc.member_store_guid = any(acl.list::text[]))
    end