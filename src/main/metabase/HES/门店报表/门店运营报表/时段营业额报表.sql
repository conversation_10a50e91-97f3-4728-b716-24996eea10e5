-- 时段营业额报表

with base as (
    -- 外卖数据
    select
        max(
            (date_trunc('hour',gmt_create) + date_part('minute',gmt_create)::int / 30 * interval '30 min')::time || '-' ||
            ((date_trunc('hour',gmt_create) + date_part('minute',gmt_create)::int / 30 * interval '30 min') + interval '30 min')::time
        )  "time_interval",
        business_day,
        -- store_guid,
        -- max(store_name) store_name,
        -- total, -- 总价，包括：菜品 + 餐盒 + 配送
        sum(ship_total + package_total + item_total) total,
        sum(item_total) item_total, -- 商品金额
        sum(
            ship_total -- 配送费合计
            + package_total -- 餐盒费合计
        ) add_amont,  --附加费
        sum(customer_actual_pay) actual_pay,  --实收金额
        count(1) table_count,  --桌台数
        1 order_type,
        0 guest_count
    from "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    where
        acl.chmod
        and gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
        and order_status = 100
        and order_sub_type in (0,1,6)
        -- and business_day between '2022-03-06' and '2022-03-07'
        [[and business_day between {{START_TIME}}::date and {{END_TIME}}::date]]
        [[and gmt_create::time between {{START_TIME_INTERVAL}}::TIME and {{END_TIME_INTERVAL}}::time]]

        -- 大于等于 ORDER_MIN 单价大于等于最小值
        -- 小于等于 ORDER_MAX 单价小于等于最大值
        [[ and ship_total + package_total + item_total <= {{ORDER_MAX}}::numeric ]]
        [[ and ship_total + package_total + item_total >= {{ORDER_MIN}}::numeric ]]
        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (store_guid = any(acl.list::text[]))
        end
        -- and order_id in ('5499540228234986','8129418485113931198')
    group by
        business_day,
        (date_trunc('hour',gmt_create) + date_part('minute',gmt_create)::int / 30 * interval '30 min')::time
    union all
    -- 堂食数据清洗
    select
        max(
            (date_trunc('hour',ho.gmt_create) + date_part('minute',ho.gmt_create)::int / 30 * interval '30 min')::time || '-' ||
            ((date_trunc('hour',ho.gmt_create) + date_part('minute',ho.gmt_create)::int / 30 * interval '30 min') + interval '30 min')::time
        )  "time_interval",
        ho.business_day,
        -- ho.store_guid,
        -- max(ho.store_name) store_name,
        sum(ho.order_fee) total,
        sum(
            ho.order_fee -- 订单金额（商品总额+附加费）
            - ho.append_fee -- 附加费
            - ho.refund_amount -- 退款金额
        )  item_total,  --商品金额
        sum(ho.append_fee) add_amont,  --附加费
        sum(ho.actually_pay_fee - ho.refund_amount) actual_pay,  --实收金额
        count(1) table_count, --桌台数
        0 order_type,
        sum(ho.guest_count) guest_count --堂食人数
    from "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order ho, --堂食库
        -- left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount  hd on ho.guid = hd.order_guid
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
    where
        acl.chmod
        and ho.gmt_create between date_trunc('day',{{START_TIME}} - interval '30 day') and date_trunc('day',{{END_TIME}} + interval '7 day')
        and ho.copy_order_guid = 0
        -- and hd.discount_state = 0
        and ho.is_delete = 0
        and ho.state = 4
        and ho.recovery_type in (1,3)
        -- and ho.guid in ('6904276426489331712','6904368656285696000')
        -- and ho.business_day between '2022-03-01' and '2022-03-02'
        [[and ho.business_day between {{START_TIME}}::date and {{END_TIME}}::date]]
        [[and ho.gmt_create::time between {{START_TIME_INTERVAL}}::TIME and {{END_TIME_INTERVAL}}::time]]

        -- 大于等于 ORDER_MIN 单价大于等于最小值
        -- 小于等于 ORDER_MAX 单价小于等于最大值
        [[ and ho.order_fee <= {{ORDER_MAX}}::numeric ]]
        [[ and ho.order_fee >= {{ORDER_MIN}}::numeric ]]

        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (ho.store_guid = any(acl.list::text[]))
        end
    group by
        ho.business_day,
        (date_trunc('hour',ho.gmt_create) + date_part('minute',ho.gmt_create)::int / 30 * interval '30 min')::time
),
rows_sum as (
    select
        sum(total) total,
        (
            SELECT
                count(1)
            FROM
                "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount
            WHERE
                order_guid = (SELECT MAX(order_guid) FROM "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount)
        ) lenght,
        case [[ {{ORDER_TYPE}} -- ]] 2
            when 0 then (
                SELECT
                    count(1)
                FROM
                    "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount
                WHERE
                    order_guid = (SELECT MAX(order_guid) FROM "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount)
            )
            when 1 then 3
            when 2 then (
                SELECT
                    count(1) + 3
                FROM
                    "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount
                WHERE
                    order_guid = (SELECT MAX(order_guid) FROM "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_discount)
            )
        end mergerindex
    from base
    [[where base.order_type = {{ORDER_TYPE}}]]
)
select
    coalesce(x.time_interval,'合计') 时段,
    @_start
    -- VALUES
    --     (1,'营业额'),
    --     (2,'桌数'),
    --     (3,'桌均营业额'),
    --     (4,'堂食人数'),
    --     (5,'堂食人均营业额')
    select
        string_agg(
            case {{TYPE_ID}}
                when 1 then concat(
                    'sum(
                        case
                            when x.business_day = ''',days_row.dw::date,''' then x.total
                        end
                    ) as ','"',days_row.df,'"'
                )
                when 2 then concat(
                    'sum(
                        case
                            when x.business_day = ''',days_row.dw::date,''' then x.table_count
                        end
                    ) as ','"',days_row.df,'"'
                )
                when 3 then concat(
                    'sum(
                        case
                            when x.business_day = ''',days_row.dw::date,''' then x.total
                        end
                    ) / sum(
                        case
                            when x.business_day = ''',days_row.dw::date,''' then x.table_count
                        end
                    ) as ','"',days_row.df,'"'
                )
                when 4 then concat(
                    'sum(
                        case
                            when x.business_day = ''',days_row.dw::date,''' then x.guest_count
                        end
                    ) as ','"',days_row.df,'"'
                )
                when 5 then concat(
                    'sum(
                        case
                            when x.business_day = ''',days_row.dw::date,''' and x.order_type = 0 then x.total
                        end
                    ) / sum(
                        case
                            when x.business_day = ''',days_row.dw::date,''' then x.guest_count
                        end
                    ) as ','"',days_row.df,'"'
                )
            end,','
        )
    from (
        select
            dw,
            to_char(dw,'MM/dd ') ||
            case extract(dow from dw)
                when 0 then '日'
                when 1 then '一'
                when 2 then '二'
                when 3 then '三'
                when 4 then '四'
                when 5 then '五'
                when 6 then '六'
            end df
        from (
            select
                generate_series({{START_TIME}}::date,{{END_TIME}}::date,'1 days') dw
        ) x
    ) days_row
    @_end
    -- sum(x.total) total  --营业额合计,
    -- sum(x.item_total) item_total,  --商品金额
    -- sum(x.add_amont) add_amont,  --附加费
    -- sum(x.actual_pay) actual_pay,  --实收
    -- sum(x.table_count) table_count,  --桌数
    -- sum(x.total) / sum(x.table_count) table_average,  --桌均营业额=营业额/桌数
    -- sum(x.actual_pay) / sum(x.table_count) table_average_actual,  --桌均实收金额=实收金额/桌数
    -- sum(x.guest_count) guest_count,  --堂食人数
    -- sum(x.total) filter(where x.order_type = 0) / sum(x.guest_count) dpat, --堂食人均营业额=营业额/人数
    -- sum(x.actual_pay) filter(where x.order_type = 0) / sum(x.guest_count) dpmt, --堂食人均实收=实收金额/人数
    -- concat(round(coalesce(sum(x.total) / (select total from rows_sum) * 100, 0),2),'%') ratio --营业额百分比
from base x
[[where x.order_type = {{ORDER_TYPE}}]]
group by
    grouping sets(
        (
            x.time_interval),
            ()
        )
order by
    case when x.time_interval isnull then '0' else x.time_interval end
