-- 产品销售分类表

with x as (
	-- 外卖订单明细
	SELECT
		-- string_agg(o.store_name,',' order by o.store_name desc) "门店",
		-- array_agg(distinct o.store_name) "门店",
		i.erp_item_sku_guid item_guid,
		ht.guid item_type_guid,
		o.store_name,
		o.business_day,
		(ht.name) item_type_name,  -- 产品类别
		(hs.code) item_code,  -- 产品编码
		(i.erp_item_name) item_name, -- "产品名称"
		-- 外卖销售数量
		(i.actual_item_count) sales_volumes,  -- 销售数量
		(i.settle_type) free_count,  -- 赠送数量
		-- 外卖销售金额
		(i.erp_item_price * i.actual_item_count) sales_amount,  -- "销售金额"
		-- item_state = 99表示退菜
		-- kds.kitchen_state = 7表示出堂
		hki.item_state returned_quantity,  -- 退品数量
		1 mode_type,
		0 guest_count
	FROM
		"hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_item i
		LEFT JOIN "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order o ON i.order_guid = o.order_guid
		left join "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_sku hs on i.erp_item_sku_guid = hs.guid
		left join "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_item hi on hs.item_guid = hi.guid
		left join "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_type ht on hi.type_guid = ht.guid
		left join "hsk_kds_887f2181-eb06-4d77-b914-7c37c884952c_db".hsk_kitchen_item hki on o.order_guid = hki.order_guid,
		LATERAL (
			select
				public.getlist(
					public.getacl(
						$privileges_flag,
						($power_list)::text
					),
					($power_list)::text,
					array[
						[[ {{STORE_GUID_MULTI}} -- ]] '-1'
						,
						[[ {{GROUP_STORE_GUID}} -- ]] '-1'
					]
				) list,
				public.getacl(
					$privileges_flag,
					($power_list)::text
				) chmod
		) acl
	WHERE
		acl.chmod
		-- true --debug
        -- and i.gmt_create between date_trunc('day',{ {START_TIME} } - interval '30 day') and date_trunc('day',{ {END_TIME} } + interval '7 day')
        -- and o.gmt_create between date_trunc('day',{ {START_TIME} } - interval '30 day') and date_trunc('day',{ {END_TIME} } + interval '7 day')
		and o.order_status = 100
		[[and o.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
		[[and i.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
		[[and o.gmt_create::time between {{START_TIME_INTERVAL}}::TIME and {{END_TIME_INTERVAL}}::time]]
		and case acl.list
			when 'true' then true
			when 'false' then false
			-- when 'false' then true  --debug
			else (o.store_guid = any(acl.list::text[]))
		end
		[[and i.erp_item_name ~* {{ITEM_NAME}}]]
        -- 大于等于 ORDER_MIN 单价大于等于最小值
        -- 小于等于 ORDER_MAX 单价小于等于最大值
        [[ and o.ship_total + o.package_total + o.item_total <= {{ORDER_MAX}}::numeric ]]
        [[ and o.ship_total + o.package_total + o.item_total >= {{ORDER_MIN}}::numeric ]]
	union all
	-- 堂食订单明细
	SELECT
		i.item_guid,
		i.item_type_guid,
		o.store_name,
		o.business_day,
		i.item_type_name,  -- 产品类别
		i.code item_code,  -- 产品编码
		i.item_name,  -- 产品名称
		-- 堂食销售数量
		i.current_count - i.refund_count + i.free_refund_count sales_volumes,  -- 销售数量
		i.free_count - i.free_refund_count,  -- 赠送数量

		-- sku价格 * (销售数量 + 赠送数量)
		i.price * (i.current_count + i.free_count - i.refund_count) sales_amount,  -- 销售金额
		i.return_count + i.refund_count returned_quantity ,  -- 退品数量
		0 mode_type,
		o.guest_count
	FROM
		"hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item i
		LEFT JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o ON i.order_guid = o.guid,
		LATERAL (
			select
				public.getlist(
					public.getacl(
						$privileges_flag,
						($power_list)::text
					),
					($power_list)::text,
					array[
						[[ {{STORE_GUID_MULTI}} -- ]] '-1'
						,
						[[ {{GROUP_STORE_GUID}} -- ]] '-1'
					]
				) list,
				public.getacl(
					$privileges_flag,
					($power_list)::text
				) chmod
		) acl
	WHERE
		acl.chmod
		-- true --debug
        -- and i.gmt_create between date_trunc('day',{ {START_TIME} } - interval '30 day') and date_trunc('day',{ {END_TIME} } + interval '7 day')
        -- and o.gmt_create between date_trunc('day',{ {START_TIME} } - interval '30 day') and date_trunc('day',{ {END_TIME} } + interval '7 day')
		and o.copy_order_guid = 0
		and o.state = 4
		AND i.parent_item_guid = 0
		and o.recovery_type in (1,3)
		and i.is_delete = '0'
		[[and o.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
		[[and i.business_day between {{START_TIME}}::date AND {{END_TIME}}::date]]
		[[and o.gmt_create::time between {{START_TIME_INTERVAL}}::TIME and {{END_TIME_INTERVAL}}::time]]
		and case acl.list
			when 'true' then true
			when 'false' then false
			-- when 'false' then true  --debug
			else (o.store_guid = any(acl.list::text[]))
		end
		[[and i.item_name ~* {{ITEM_NAME}}]]
        -- 大于等于 ORDER_MIN 单价大于等于最小值
        -- 小于等于 ORDER_MAX 单价小于等于最大值
        [[ and o.order_fee <= {{ORDER_MAX}}::numeric ]]
        [[ and o.order_fee >= {{ORDER_MIN}}::numeric ]]
),
sum_x as (
	SELECT
		sum(x.sales_amount) sales_amount
	from x
)
SELECT
	-- x.item_guid,
	-- x.item_type_guid,
	array_agg(distinct x.store_name) "门店",
	x.business_day::text 日期,
	max(x.item_type_name) 产品类别,
	max(x.item_code) 产品编码,
	max(x.item_name) 产品名称,
	sum(x.sales_volumes) filter(where x.mode_type = 0) 堂食销售数量,
	sum(x.sales_volumes) filter(where x.mode_type = 1) 外卖销售数量,
	sum(x.free_count) filter(where x.mode_type = 1 and x.free_count = 1) + sum(x.free_count) filter(where x.mode_type = 0) 赠送数量,
	sum(x.sales_volumes) 合计销售数量,
	sum(x.sales_amount) filter(where x.mode_type = 0) 堂食销售金额,
	sum(x.sales_amount) filter(where x.mode_type = 1) 外卖销售金额,
	sum(x.sales_amount) 合计销售金额,
	concat(coalesce(round(sum(x.sales_amount) / (select sales_amount from sum_x) * 100,2),0),'%') 销售百分比,
	sum(x.sales_amount) filter(where x.mode_type = 0) / sum(x.guest_count) * 1000 堂食千次人均产品消费,
	sum(x.returned_quantity) filter(where x.mode_type = 1 and x.returned_quantity = 99) + sum(x.returned_quantity) filter(where x.mode_type = 0) 退品数量
from x
group by
	x.item_guid,
	x.item_type_guid,
	x.business_day
order by
	x.business_day,
	x.item_type_guid,
	x.item_guid