--门店桌台状态
SELECT
	a.store_name 门店名称,
	a.area_name 区域,
	a.table_code 桌台号,
	a.seats 容纳人数,
	(
	CASE

			WHEN b.sub_status IS NULL
			OR b.sub_status = '[]' THEN
				'空闲'
				WHEN b.sub_status LIKE '%20%'
				OR b.sub_status LIKE '%21%' THEN
					'已预订' ELSE '已开台'
				END
				)  状态,
				d.reserve_start_time 预定时间,
				d.number 就餐人数,
				d.phone 手机号,
				d.name 姓名,
				d.reserve_amount 订金,
				d.remark 备注
			FROM
				"hst_table_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_table_basic a
				JOIN "hst_table_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_table_order b ON a.guid = b.table_guid
			LEFT JOIN "hsr_reserve_887f2181-eb06-4d77-b914-7c37c884952c_db".hss_r_reserve_record_table c ON b.table_guid = c.table_guid
	LEFT JOIN "hsr_reserve_887f2181-eb06-4d77-b914-7c37c884952c_db".hss_reserve_record d ON c.reserve_record_guid = d.guid,
 LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
	WHERE
	 acl.chmod
	AND a.enable = 0 AND a.deleted = 0
	AND c.id IN (SELECT max(id) id FROM "hsr_reserve_887f2181-eb06-4d77-b914-7c37c884952c_db".hss_r_reserve_record_table
	GROUP BY table_guid)
	[[and TO_CHAR(d.reserve_start_time, 'YYYY-MM-DD') = TO_CHAR({{START_TIME}},'YYYY-MM-DD')]]
	 and case acl.list
        when 'true' then true
        when 'false' then false
        -- when 'false' then true  --debug
        else (a.store_guid = any(acl.list::text[]))
    end
    [[
        and case {{STATUS}}
            when 0 then (
                b.sub_status IS NULL
			OR b.sub_status = '[]'
            )
            when 1 then (
               b.sub_status LIKE '%20%'
				OR b.sub_status LIKE '%21%'
            )
            when 2 then (
                b.status = 1 and (b.sub_status LIKE '%10%' OR b.sub_status LIKE '%11%' OR b.sub_status LIKE '%12%' OR b.sub_status LIKE '%13%')
            )
        end
    ]];
--预定订单明细
SELECT
    a.store_name 门店名称,
    a.table_code 桌台,
    (CASE WHEN d.state = 38 AND d.is_delay = FALSE  THEN '预定中'
     WHEN d.state = 38 AND d.is_delay = TRUE THEN '已逾期'
     WHEN d.state = 35 or d.state = 47 THEN '已取消'
     WHEN d.state = 62 THEN '已到店'
    ELSE '待处理' END)  预订状态,

    d.reserve_start_time 预订时间,
    d.number 就餐人数,
    d.phone 手机号,
    d.name 姓名,
    d.reserve_amount 订金,
    d.payment_type_name 支付方式,
    d.remark 备注,
    (CASE d.items_str WHEN '[]' THEN '否' ELSE '是' END) 是否预点餐,

    d.modified_staff_name 操作员,
    d.gmt_modified 操作时间
FROM "hst_table_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_table_basic a
 JOIN "hsr_reserve_887f2181-eb06-4d77-b914-7c37c884952c_db".hss_r_reserve_record_table c ON a.guid = c.table_guid
 JOIN "hsr_reserve_887f2181-eb06-4d77-b914-7c37c884952c_db".hss_reserve_record d ON c.reserve_record_guid = d.guid,
  LATERAL (
    select
        public.getlist(
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ),
            ($power_list)::text,
            array[
                [[ {{STORE_GUID}} -- ]] '-1'
                ,
                [[ {{GROUP_STORE_GUID}} -- ]] '-1'
            ]
        ) list,
        public.getacl(
            $privileges_flag,
            ($power_list)::text
        ) chmod
) acl
 WHERE
 acl.chmod
 AND d.is_deleted = FALSE
 AND
 d.is_enable = TRUE

 and case acl.list
    when 'true' then true
    when 'false' then false
    -- when 'false' then true  --debug
    else (a.store_guid = any(acl.list::text[]))
end

 [[and TO_CHAR(d.reserve_start_time, 'YYYY-MM-DD HH:mm:ss') >= TO_CHAR({{START_TIME}},'YYYY-MM-DD 00:00:00')]]
 [[and TO_CHAR(d.reserve_start_time, 'YYYY-MM-DD HH:mm:ss') <= TO_CHAR({{END_TIME}},'YYYY-MM-DD 23:59:59')]]
  [[
    and case {{STATUS}}
        when 0 then (
            d.state = 1
        )
        when 1 then (
          d.state = 38 AND d.is_delay = FALSE
        )
        when 2 then (
            d.state = 38 AND d.is_delay = TRUE
        )
         when 3 then (
            d.state = 62
        )
         when 4 then (
           d.state = 35 or d.state = 47
        )
    end
]]
 ORDER BY reserve_start_time DESC