<script>
    WITH trade AS (
        SELECT
            t.sku_guid,
            SUM( t.item_count ) "item_count",
            SUM( t.item_amount ) "item_amount",
			SUM( t.cost_price ) "cost_price",
            SUM ( t.discount_price ) "item_discount_price"
        FROM (
            SELECT
                oi.sku_guid,
                oi.current_count - oi.refund_count + oi.free_refund_count "item_count",
                ROUND((oi.current_count - oi.refund_count + oi.free_refund_count) * oi.price + COALESCE( a.amount, 0 ), 2) "item_amount",
				round( COALESCE ( oie.cost_price * (oi.current_count - oi.refund_count + oi.free_refund_count) ,0) ,2) AS "cost_price",
                case
                when oi.refund_count - oi.free_refund_count = 0 then COALESCE(oi.discount_total_price,0)
                when oi.refund_count - oi.free_refund_count > 0 then COALESCE(oi.discount_total_price,0) * ((oi.current_count - oi.refund_count + oi.free_refund_count) / oi.current_count)
                else COALESCE(oi.discount_total_price,0) end "discount_price"
            FROM
                "hst_trade_${enterpriseGuid}_db".hst_order_item oi
				LEFT JOIN "hst_trade_${enterpriseGuid}_db"."hst_order_item_extends" oie ON oie.guid = oi.guid and oie.is_delete = 0
                JOIN "hst_trade_${enterpriseGuid}_db".hst_order ord ON ord.guid = oi.order_guid and ord.is_delete = 0
                LEFT JOIN (
                    SELECT
                        s.parent_item_guid guid,
                        sum( s.add_price * s.current_count ) amount
                    FROM
                        "hst_trade_${enterpriseGuid}_db"."hst_order_item" s
                        JOIN "hst_trade_${enterpriseGuid}_db"."hst_order" o ON o.guid = s.order_guid
                    WHERE
                        s.parent_item_guid != 0
                        AND o.business_day between #{startTime} and #{endTime}
                        AND s.business_day between #{startTime} and #{endTime}
                        AND o.state = 4
                        AND CASE WHEN (s.current_count = 0 AND s.free_count = 0) THEN (s.return_count = 0) ELSE 1=1 END
                        AND s.is_delete = 0
                        <if test="orgGuid != null and orgGuid.size()>0 ">
                            AND o.store_guid IN
                            <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                                #{guid, jdbcType=VARCHAR}
                            </foreach>
                        </if>
                        AND o.recovery_type in (1,3)
                    GROUP BY
                    s.parent_item_guid
                ) a ON oi.guid = a.guid
            WHERE
                ord.business_day between #{startTime} and #{endTime}
                and oi.business_day between #{startTime} and #{endTime}
                AND oi.is_delete = 0
                AND oi.parent_item_guid = 0
                <if test="orgGuid != null and orgGuid.size() > 0 ">
                    and ord.store_guid in
                    <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                        #{guid, jdbcType=VARCHAR}
                    </foreach>
                </if>
                AND ord.STATE = 4
                AND ord.recovery_type IN ( 1, 3 )
        ) t
        GROUP BY t.sku_guid
    ),
    --外卖
    takeaway AS (
        SELECT
            t.item_sku,
            SUM( t.item_count ) item_count,
            SUM( t.item_amount ) item_amount,
			SUM( t.cost_price ) cost_price
        FROM (
            SELECT
                ti.erp_item_sku_guid item_sku,
                (ti.item_count - ti.refund_count) * (ti.actual_item_count / ti.item_count) item_count,
                (ti.item_count - ti.refund_count) * (ti.actual_item_count / ti.item_count) * ti.erp_item_price item_amount,
                round( COALESCE ( tie.cost_price * ( COALESCE ( ti.actual_item_count, 0 ) - (COALESCE ( ti.refund_count, 0 ) )
                    * (COALESCE ( ti.actual_item_count, 1 )/COALESCE ( ti.item_count, 1 ) ) ) ,0) ,2)  cost_price
            FROM
                "hst_takeaway_${enterpriseGuid}_db".hst_takeout_item ti
                LEFT JOIN "hst_takeaway_${enterpriseGuid}_db".hst_takeout_item_extends tie ON tie.guid = ti.item_guid AND tie.is_delete = 0
                JOIN "hst_takeaway_${enterpriseGuid}_db".hst_takeout_order ord ON ti.order_guid = ord.order_guid
            WHERE
                ord.business_day between #{startTime} and #{endTime}
                and ti.business_day between #{startTime} and #{endTime}
                AND ord.order_status != '-1'
                AND ord.refund_status != 2
                AND ti.erp_item_sku_guid is not null
                <if test="orgGuid != null and orgGuid.size() > 0 ">
                    and ord.store_guid in
                    <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                        #{guid, jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="relativeId != null and relativeId.size() > 0">
                    AND ti.erp_item_sku_guid in
                    <foreach collection="relativeId" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
        ) t
        GROUP BY t.item_sku
    ),
    item_info as (
        select
            hs.guid sku_guid,
            case hs.name when '' then hi.name else concat(hi.name,'(',hs.name,')') end as item_name
        from
            "hsi_item_${enterpriseGuid}_db".hsi_item hi
            left join "hsi_item_${enterpriseGuid}_db".hsi_sku hs on hi.guid = hs.item_guid and hs.is_delete = 0
        where
            hi.is_delete = 0
    )

    SELECT
        min(ii.sku_guid) id,
        ii.item_name as name,
        SUM(COALESCE(item_count,0)) "v_item_count",
        SUM(COALESCE(item_amount,0)) "item_amount",
        SUM( COALESCE(item_amount,0) - cost_price ) "gross_profit_amount",
        SUM(COALESCE(item_discount_price,0)) "item_discount_price",
        CASE SUM( SUM( COALESCE(item_count,0) ) )over() WHEN 0 THEN 0 ELSE round(100* SUM(COALESCE(item_count,0)) *1.00 / SUM( SUM( COALESCE(item_count,0) ) )over() ,2 ) END AS item_ratio
    FROM
    (
        (
            SELECT
                tw.item_sku sku_guid,
                COALESCE(tw.item_count,0) item_count,
                COALESCE(tw.item_amount,0) item_amount,
                COALESCE(tw.cost_price,0) cost_price,
                COALESCE(tw.item_amount,0) item_discount_price
            FROM
                takeaway tw
        )

        UNION ALL

        (
            SELECT
                case when hs.parent_guid is not null then hs.parent_guid else tra.sku_guid end as sku_guid,
                COALESCE ( tra.item_count, 0 ) item_count,
                COALESCE ( tra.item_amount, 0 ) item_amount,
				COALESCE ( tra.cost_price, 0 ) cost_price,
                COALESCE ( tra.item_discount_price, 0 ) item_discount_price
            FROM
                trade tra
                left join "hsi_item_${enterpriseGuid}_db".hsi_sku hs on hs.guid = tra.sku_guid and hs.is_delete = 0
        )
    ) item_sale
    left join item_info ii on item_sale.sku_guid = ii.sku_guid
    WHERE
        item_sale.item_count != 0
        AND ii.sku_guid IS NOT NULL
        <if test="relativeId != null and relativeId.size() > 0">
            AND ii.sku_guid in
            <foreach collection="relativeId" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    GROUP BY ii.item_name
    ORDER BY "v_item_count" DESC
    LIMIT 20
</script>