<script>
    WITH -- 外卖
    takeaway AS (
        SELECT
            t.item_sku,
            SUM ( COALESCE ( t.item_count, 0 ) ) item_count,
            SUM ( COALESCE ( t.item_amount, 0 ) ) item_amount,
			SUM( t.cost_price ) cost_price
        FROM
            (
                SELECT
                    ti.erp_item_sku_guid item_sku,
                    (ti.item_count - ti.refund_count) * (ti.actual_item_count / ti.item_count) item_count,
                    (ti.item_count - ti.refund_count) * (ti.actual_item_count / ti.item_count) * ti.erp_item_price item_amount,
					round( COALESCE ( tie.cost_price * ( COALESCE ( ti.actual_item_count, 0 ) - (COALESCE ( ti.refund_count, 0 ) )
                        * (COALESCE ( ti.actual_item_count, 1 )/COALESCE ( ti.item_count, 1 ) ) ) ,0) ,2)  cost_price
                FROM
                    "hst_takeaway_${enterpriseGuid}_db".hst_takeout_item ti
                    LEFT JOIN "hst_takeaway_${enterpriseGuid}_db".hst_takeout_item_extends tie ON tie.guid = ti.item_guid AND tie.is_delete = 0
                    JOIN "hst_takeaway_${enterpriseGuid}_db".hst_takeout_order ord on ti.order_guid = ord.order_guid
                WHERE
                    ord.business_day BETWEEN #{startTime} AND #{endTime}
                    and ti.business_day BETWEEN #{startTime} AND #{endTime}
                    AND ti.erp_item_sku_guid IS NOT NULL
                    AND ord.order_status != '-1'
                    AND ord.refund_status != 2
                    <if test="orgGuid != null and orgGuid.size() > 0 ">
                        and ord.store_guid in
                        <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                            #{guid}
                        </foreach>
                    </if>
            ) t
        GROUP BY
        t.item_sku
    ),
    -- 商品分类
    item_type AS (
    SELECT
        hs.guid sku_guid,
        case when ht_p.guid is not null
        then ht_p.guid
        else ht.guid end as type_guid,
        case when ht_p.guid is not null
        then ht_p.name
        else ht.NAME end as type_name
    FROM
        "hsi_item_${enterpriseGuid}_db".hsi_type ht
        LEFT JOIN "hsi_item_${enterpriseGuid}_db".hsi_type ht_p ON ht_p.guid = ht.parent_guid
        LEFT JOIN "hsi_item_${enterpriseGuid}_db".hsi_item hi ON ht.guid = hi.type_guid
        left JOIN "hsi_item_${enterpriseGuid}_db".hsi_sku hs ON hs.item_guid = hi.guid
    where
        hs.guid is not null
    ),
    -- 堂食
    trade AS (
        SELECT
            t.sku_guid,
            SUM ( t.item_count ) "item_count",
            SUM ( t.item_amount ) "item_amount",
			SUM( t.cost_price ) "cost_price",
            SUM ( t.discount_price ) "item_discount_price"
        FROM (
            SELECT
                sku_guid,
                oi.current_count - oi.refund_count + oi.free_refund_count "item_count",
                ROUND( (oi.current_count - oi.refund_count + oi.free_refund_count) * oi.price + COALESCE( a.amount, 0 ), 2 ) "item_amount",
				round( COALESCE ( oie.cost_price * (oi.current_count - oi.refund_count + oi.free_refund_count) ,0) ,2) AS "cost_price",
                case
                when oi.refund_count - oi.free_refund_count = 0 then COALESCE(oi.discount_total_price,0)
                when oi.refund_count - oi.free_refund_count > 0 then COALESCE(oi.discount_total_price,0) * ((oi.current_count - oi.refund_count + oi.free_refund_count) / oi.current_count)
                else COALESCE(oi.discount_total_price,0) end "discount_price"
            FROM
                "hst_trade_${enterpriseGuid}_db".hst_order_item oi
				LEFT JOIN "hst_trade_${enterpriseGuid}_db"."hst_order_item_extends" oie ON oie.guid = oi.guid and oie.is_delete = 0
                JOIN "hst_trade_${enterpriseGuid}_db".hst_order ord ON ord.guid = oi.order_guid AND ord.is_delete = 0
                LEFT JOIN (
                    SELECT
                        s.parent_item_guid guid,
                        sum( s.add_price * s.current_count ) amount
                    FROM
                        "hst_trade_${enterpriseGuid}_db"."hst_order_item" s
                        JOIN "hst_trade_${enterpriseGuid}_db"."hst_order" o ON o.guid = s.order_guid
                    WHERE
                        s.parent_item_guid != 0
                        AND o.business_day between #{startTime} and #{endTime}
                        and s.business_day between #{startTime} and #{endTime}
                        AND o.state = 4
                        AND CASE WHEN (s.current_count = 0 AND s.free_count = 0) THEN (s.return_count = 0) ELSE 1=1 END
                        AND s.is_delete = 0
                        <if test="orgGuid != null and orgGuid.size()>0 ">
                            AND o.store_guid IN
                            <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                                #{guid, jdbcType=VARCHAR}
                            </foreach>
                        </if>
                        AND o.recovery_type in (1,3)
                    GROUP BY s.parent_item_guid
                ) a ON oi.guid = a.guid
            WHERE
                ord.business_day BETWEEN #{startTime} AND #{endTime}
                and oi.business_day BETWEEN #{startTime} AND #{endTime}
                AND oi.is_delete = '0'
                AND oi.parent_item_guid = 0
                <if test="orgGuid != null and orgGuid.size() > 0 ">
                    and ord.store_guid in
                    <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                        #{guid, jdbcType=VARCHAR}
                    </foreach>
                </if>
                AND ord.STATE = 4
                AND ord.recovery_type IN ( 1, 3 )
        ) t
        GROUP BY t.sku_guid
    )
    -- 外卖和堂食商品类型统计
    SELECT
        item_type.type_guid AS "id",
        item_type.type_name AS "name",
        SUM(COALESCE(item_type.type_count,0)) AS "v_type_salce",
        SUM(COALESCE(item_type.type_amount,0)) AS "type_amount",
        SUM( COALESCE(item_type.gross_profit_amount,0) ) "gross_profit_amount",
        SUM(COALESCE(item_type.type_discount_price,0)) AS "type_discount_price",
        CASE SUM(COALESCE(item_type.type_count,0))
        WHEN 0 THEN 0
        ELSE round(100* SUM(COALESCE(item_type.type_count,0)) *1.00 / SUM( SUM(COALESCE(item_type.type_count,0)) )over() ,2 )
        END AS type_ratio
    FROM
    (
        -- 外卖商品分类
        (
            SELECT
                it.type_guid,
                it.type_name,
                SUM(COALESCE(takeaway.item_count,0)) type_count,
                SUM(COALESCE(takeaway.item_amount,0)) type_amount,
                SUM(COALESCE(takeaway.item_amount,0) - COALESCE(takeaway.cost_price,0)) gross_profit_amount,
                SUM(COALESCE(takeaway.item_amount,0)) type_discount_price
            FROM
                takeaway
                LEFT JOIN item_type it ON it.sku_guid = takeaway.item_sku
            GROUP BY it.type_guid,it.type_name
        )

        UNION ALL
        -- 堂食分类
        (
            SELECT
                it.type_guid,
                it.type_name type_name,
                SUM(COALESCE(td.item_count,0)) type_count,
                SUM(COALESCE(td.item_amount,0)) type_amount,
                SUM( COALESCE ( td.item_amount, 0 ) - COALESCE(td.cost_price,0) ) gross_profit_amount,
                SUM(COALESCE(td.item_discount_price,0)) type_discount_price
            FROM
                trade td
                LEFT JOIN item_type it ON td.sku_guid = it.sku_guid
            GROUP BY it.type_guid,it.type_name
        )
    ) AS item_type
    WHERE
        type_count != 0
        <if test="relativeId != null and relativeId.size() > 0">
            AND item_type."type_guid" in
            <foreach collection="relativeId" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    GROUP BY item_type."type_guid",item_type."type_name"
    ORDER BY "v_type_salce" DESC
    LIMIT 20
</script>