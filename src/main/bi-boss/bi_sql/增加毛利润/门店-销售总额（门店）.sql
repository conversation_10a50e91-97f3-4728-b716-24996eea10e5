<script>
    select
    store_guid "id",
    store_name "name",
    COALESCE(SUM(A64_T_1_."order_fee"),0) AS "v_1",
    COALESCE(SUM(A64_T_1_."gross_profit_amount"),0) AS "gross_profit_amount"
    FROM
    (

    with trade as (
    SELECT
    ord.guid as "order_guid",
    ord.store_guid store_guid,
    ord.store_name store_name,
    ord.actually_pay_fee - ord.refund_amount + COALESCE(tr.amount,0) order_fee
    from
    "hst_trade_${enterpriseGuid}_db".hst_order ord
    left join "hst_trade_${enterpriseGuid}_db".hst_transaction_record tr on tr.order_guid = ord.guid
    and tr.payment_type = 13 and tr.state = 4 and tr.trade_type = 1 AND tr.gmt_create between #{startTime}::TIMESTAMP + '-1 month' and #{endTime}::TIMESTAMP + '1 month'
    where
    ord.business_day between #{startTime} and #{endTime}
    AND ord.state = 4
    AND ord.recovery_type IN ( 1, 3 )
    <if test="orgGuid != null and orgGuid.size() > 0 ">
        and ord.store_guid in
        <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
    </if>
    ),
    tradeItem AS (
        SELECT
            oie.order_guid AS order_guid,
            round( COALESCE ( SUM( COALESCE ( oie.cost_price * (oi.current_count - oi.refund_count + oi.free_refund_count), 0 ) ) ,0) ,2) AS cost_price
        FROM
            trade t
            LEFT JOIN "hst_trade_${enterpriseGuid}_db".hst_order_item oi ON oi.order_guid = t.order_guid
            LEFT JOIN "hst_trade_${enterpriseGuid}_db".hst_order_item_extends oie ON oie.guid = oi.guid
        WHERE
          oi.is_delete = 0
          AND oie.is_delete = 0
            AND oi.parent_item_guid = '0'
            AND oi.current_count != 0
            AND oi.business_day between #{startTime} and #{endTime}
        GROUP BY
            oie.order_guid
    ),
    groupon as (
    SELECT
    g.order_guid,
    sum( coupon_buy_price ) AS "coupon_buy_price"
    FROM
        trade t
    LEFT JOIN "hst_trade_${enterpriseGuid}_db".hst_groupon g ON t.order_guid = g.order_guid
    where
    g.is_delete = 0 and g.refund_order_guid is null
    group by g.order_guid
    ),
    takeaway as (
    SELECT
    ord.order_guid order_guid,
    ord.store_guid store_guid,
    ord.store_name store_name,
    CASE is_refund_success
    WHEN '1' THEN COALESCE(ord.customer_actual_pay,0) - COALESCE(ord.customer_refund,0)
    ELSE ord.customer_actual_pay
    END AS order_fee
    FROM
    "hst_takeaway_${enterpriseGuid}_db".hst_takeout_order ord
    where
    ord.business_day between #{startTime} and #{endTime}
    <if test="orgGuid != null and orgGuid.size() > 0 ">
        and ord.store_guid in
        <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
    </if>
    AND ord.order_type = 0
    AND ord.order_status != '-1' and ord.refund_status != 2
    ),
    takeawayItem AS (
        SELECT
            tie.order_guid AS order_guid,
            round( COALESCE ( SUM( COALESCE ( tie.cost_price * ( COALESCE ( ti.actual_item_count, 0 ) -
                (COALESCE ( ti.refund_count, 0 ) ) * (COALESCE ( ti.actual_item_count, 1 )/COALESCE ( ti.item_count, 1 ) )
                ), 0 ) ) ,0) ,2) AS cost_price
        FROM
            takeaway ta
            LEFT JOIN "hst_takeaway_${enterpriseGuid}_db".hst_takeout_item ti ON ti.order_guid = ta.order_guid
            LEFT JOIN "hst_takeaway_${enterpriseGuid}_db".hst_takeout_item_extends tie ON tie.guid = ti.item_guid
        WHERE
          tie.is_delete = 0
            AND ti.erp_item_sku_guid IS NOT NULL
            AND ti.business_day between #{startTime} and #{endTime}
        GROUP BY
            tie.order_guid
    ),
    zhanqu AS (
    SELECT
    org_parent.guid zhanqu_guid,
    org_parent.NAME zhanqu_name,
    org.guid store_guid,
    org.NAME store_name
    FROM
    "hso_organization_${enterpriseGuid}_db".hso_organization org
    LEFT JOIN "hso_organization_${enterpriseGuid}_db".hso_organization org_parent ON org.parent_ids LIKE concat ( '%', org_parent.guid)
    WHERE
    1 = 1
    <if test="id != null and id != ''">
        and org_parent.guid = #{id}
    </if>
    <if test="id != null and id != ''">
        and org_parent.guid = #{id}
    </if>
    <if test="orgGuid != null and orgGuid.size() > 0 ">
        AND org.guid in
        <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
    </if>
    ORDER BY
    org_parent.guid
    )
    select
    zq.store_guid,
    zq.store_name,
    t.order_fee + COALESCE ( g.coupon_buy_price, 0 ) as "order_fee",
    t.order_fee + COALESCE ( g.coupon_buy_price, 0 ) - COALESCE (ti.cost_price, 0 ) as "gross_profit_amount"
    from
    zhanqu zq
    left join trade t on zq.store_guid = t.store_guid
    left join groupon g on g.order_guid = t.order_guid
    LEFT JOIN tradeItem ti ON ti.order_guid = t.order_guid

    union all

    select
    zq.store_guid,
    zq.store_name,
    order_fee,
		order_fee - COALESCE (tai.cost_price, 0 ) as "gross_profit_amount"
    from
    zhanqu zq
    left join takeaway tw on zq.store_guid = tw.store_guid
    LEFT JOIN takeawayItem tai ON tai.order_guid = tw.order_guid

    ) AS A64_T_1_

    <if test="relativeId != null and relativeId.size() > 0">
        WHERE
        store_guid in
        <foreach collection="relativeId" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </if>
    GROUP by "id","name"
    <choose>
        <when test="orderBy !=null and orderBy!=''">
            -- 动态字段排序
            order by ${orderBy}
        </when>
        <otherwise>
            ORDER by "id" asc
        </otherwise>
    </choose>
</script>