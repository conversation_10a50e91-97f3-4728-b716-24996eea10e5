<script>
    WITH torder AS (
        SELECT
            guid
        FROM
        "hst_trade_${enterpriseGuid}_db".hst_order
        WHERE
        business_day between #{startTime} and #{endTime}
        AND is_delete = 0
        <if test="orgGuid != null and orgGuid.size() > 0 ">
            and store_guid in
            <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                #{guid, jdbcType=VARCHAR}
            </foreach>
        </if>
        AND STATE = 4 AND recovery_type IN ( 1, 3 )
    ),
    rorder as (
        SELECT
            guid
        FROM
            "hst_trade_${enterpriseGuid}_db".hst_order
        WHERE
            business_day between #{startTime} and #{endTime}
        AND is_delete = 0
        <if test="orgGuid != null and orgGuid.size() > 0 ">
            and store_guid in
            <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                #{guid, jdbcType=VARCHAR}
            </foreach>
        </if>
        AND STATE = 5 AND recovery_type = 4 and recovery_id = '0'
    ),
    takeaway as (
        SELECT
        ord.order_sub_type + 100 as "type",
        count(1) as "order_count",
        SUM(CASE ord.is_refund_success
        WHEN '1' THEN COALESCE(ord.customer_actual_pay,0) - COALESCE(ord.customer_refund,0)
        ELSE ord.customer_actual_pay
        END) AS amount
        FROM
        "hst_takeaway_${enterpriseGuid}_db".hst_takeout_order ord
        WHERE
        ord.business_day between #{startTime} and #{endTime}
        AND ord.order_type = 0
        AND ord.order_status != '-1' and ord.refund_status != 2
        <if test="orgGuid != null and orgGuid.size() > 0 ">
            and ord.store_guid in
            <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                #{guid}
            </foreach>
        </if>
        group by ord.order_sub_type
    ),
    paymentInfo AS (
    SELECT
        store_guid,
        app_id
    FROM
        (
        SELECT
            store_guid,
            app_id,
            ROW_NUMBER ( ) OVER ( PARTITION BY app_id ORDER BY create_time ) AS row_num
        FROM
            hse_enterprise_db.hse_payment_info
        WHERE
            is_delete = 0
        ) subquery
    WHERE
        row_num = 1
    ),
    payAppId AS (
    SELECT
        pi.app_id
    FROM
        "hsb_business_${enterpriseGuid}_db".hsb_payment_type bpt
        LEFT JOIN paymentInfo pi ON pi.store_guid = bpt.store_guid
    WHERE
        bpt.payment_type = 1
        AND bpt.STATE = 0
        <if test="orgGuid != null and orgGuid.size() > 0 ">
            AND bpt.store_guid in
            <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                #{guid}
            </foreach>
        </if>
    ),
	cloudPay AS (
		SELECT
			200 AS "type",
			'云收款码' AS "type_name",
			COUNT ( DISTINCT id ) AS "order_count",
			COALESCE (SUM ( ROUND( amount :: NUMERIC / 100.0, 2 ) ), 0 ) "amount"
		FROM
			(
			SELECT
				id,
				store_name,
				app_id,
				amount,
				pay_power_id,
				pay_power_name,
				pay_state,
				gmt_create,
				pay_guid
			FROM
				hpt_trading_db_01.hpt_order_01
			WHERE
				app_id IN ( SELECT app_id FROM payAppId )
				AND pay_state = 2
				AND pay_power_id IN ( 31, 51 )
				AND gmt_create between #{startTime} AND #{endTime}

				UNION
			SELECT
				id,
				store_name,
				app_id,
				amount,
				pay_power_id,
				pay_power_name,
				pay_state,
				gmt_create,
				pay_guid
			FROM
				hpt_trading_db_01.hpt_order_02
			WHERE
				app_id IN ( SELECT app_id FROM payAppId )
				AND pay_state = 2
				AND pay_power_id IN ( 31, 51 )
				AND gmt_create between #{startTime} AND #{endTime}
				 UNION
			SELECT
				id,
				store_name,
				app_id,
				amount,
				pay_power_id,
				pay_power_name,
				pay_state,
				gmt_create,
				pay_guid
			FROM
				hpt_trading_db_02.hpt_order_02
			WHERE
				app_id IN ( SELECT app_id FROM payAppId )
				AND pay_state = 2
				AND pay_power_id IN ( 31, 51 )
				AND gmt_create between #{startTime} AND #{endTime}
				 UNION
			SELECT
				id,
				store_name,
				app_id,
				amount,
				pay_power_id,
				pay_power_name,
				pay_state,
				gmt_create,
				pay_guid
			FROM
				hpt_trading_db_02.hpt_order_02
			WHERE
				app_id IN ( SELECT app_id FROM payAppId )
				AND pay_state = 2
				AND pay_power_id IN ( 31, 51 )
				AND gmt_create between #{startTime} AND #{endTime}
			) pa
		)

    SELECT * from (
        SELECT
            t.type as "id",
            case t.type
            when 1 then '现金支付'
            when 2 then '聚合支付'
            when 3 then '银联支付'
            when 4 then '会员余额支付'
            when 5 then '人脸支付'
            when 6 then '通吃岛支付'
            when 7 then '预付金支付'
            when 8 then '挂账支付'
            when 10 then max(t.type_name)
            when 11 then '食堂电子卡支付'
            when 12 then '食堂实体卡支付'
            when 13 then '第三方平台活动'
            when 20 then '美团团购'
            when 30 then '线下退款'
            when 61 then '抖音团购'
            when 62 then '大众点评'
            when 65 then '支付宝验券'
            when 66 then '农行团购验券'
            when 100 then '美团外卖'
            when 101 then '饿了么'
            when 106 then '赚餐外卖'
						WHEN 200 THEN '云收款码'
            else '其他' end as "type",
            SUM( t.order_count ) as "order_count",
            SUM( t.amount ) as "amount",
            CASE sum(SUM( t.amount ))over()
            WHEN 0 THEN 0
            ELSE round(100* SUM( t.amount ) *1.00 / sum(SUM( t.amount ))over() ,2 )
            END AS type_ratio
        from (
            SELECT
            tr.payment_type as "type",
            tr.payment_type_name as "type_name",
            count(DISTINCT ord.guid) as "order_count",
            COALESCE(sum(tr.amount - tr.refund_amount),0) as "amount"
        FROM
            "hst_trade_${enterpriseGuid}_db".hst_transaction_record tr
            JOIN torder ord ON ord.guid = tr.order_guid
        WHERE
            tr.gmt_create between #{startTime}::TIMESTAMP + '-1 month' and #{endTime}::TIMESTAMP + '1 month'
            AND tr.state = 4 and tr.trade_type = 1 and tr.amount - tr.refund_amount > 0
            AND tr.payment_type not in (20, 61, 62, 65, 66)
            AND tr.is_delete = 0
        GROUP BY tr.payment_type, tr.payment_type_name
        union all
        SELECT
            case when g.groupon_type = 6 then 20 else g.groupon_type end as "type",
            '' as "type_name",
            count(DISTINCT ord.guid) as "order_count",
            COALESCE(sum(g.coupon_buy_price),0) as "amount"
        FROM
            "hst_trade_${enterpriseGuid}_db".hst_groupon g
            JOIN torder ord ON ord.guid = g.order_guid
        WHERE
            g.is_delete = 0 and g.refund_order_guid is null
        GROUP BY g.groupon_type
        union all
        SELECT
            a.type,
            '' as "type_name",
            a.order_count,
            a.amount
        FROM
        takeaway a
        UNION ALL
        SELECT
            "type",
            type_name,
            order_count,
            amount
        FROM
            cloudPay
        WHERE
            amount > 0
        ) t
        group by t.type, t.type_name

        union all

        SELECT
            tr.payment_type as "id",
            '线下退款' as "type",
            count(DISTINCT ord.guid) as "order_count",
            COALESCE(sum(tr.amount),0) as "amount",
            null as "type_ratio"
        FROM
            "hst_trade_${enterpriseGuid}_db".hst_transaction_record tr
            JOIN rorder ord ON ord.guid = tr.order_guid
        WHERE
            tr.gmt_create between #{startTime}::TIMESTAMP + '-1 month' and #{endTime}::TIMESTAMP + '1 month'
            AND tr.state = 4 and tr.trade_type = 6 and tr.amount != 0
            AND tr.payment_type = 30
            AND tr.is_delete = 0
        GROUP BY tr.payment_type
    ) temp
    <choose>
        <when test="orderBy !=null and orderBy!=''">
            order by ${orderBy}
        </when>
        <otherwise>
            ORDER BY amount DESC
        </otherwise>
    </choose>
</script>