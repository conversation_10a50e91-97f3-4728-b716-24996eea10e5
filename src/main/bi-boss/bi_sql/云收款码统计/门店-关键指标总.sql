<script>
    with order_data as (
        with trade AS (
            with trade_sub as (
            SELECT
                0 order_type,
                ord.guid as "order_guid",
                ord.trade_mode,
                ord.store_guid,
                ord.actually_pay_fee - ord.refund_amount + COALESCE(tr.amount,0) order_fee
            FROM
                "hst_trade_${enterpriseGuid}_db".hst_order ord
                left join "hst_trade_${enterpriseGuid}_db".hst_transaction_record tr on tr.order_guid = ord.guid
                and tr.payment_type = 13 and tr.state = 4 and tr.trade_type = 1
                and tr.gmt_create between #{startTime}::TIMESTAMP + '-1 month' and #{endTime}::TIMESTAMP + '1 month'
            WHERE
                ord.business_day between #{startTime} and #{endTime}
                <if test="orgGuid != null and orgGuid.size() > 0 ">
                    and ord.store_guid in
                    <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                        #{guid, jdbcType=VARCHAR}
                    </foreach>
                </if>
                AND ord.STATE = 4
                AND ord.recovery_type IN ( 1, 3 )
            ),
            groupon as (
            SELECT
                g.order_guid,
                sum( coupon_buy_price ) AS "coupon_buy_price"
            FROM
                trade_sub t
                LEFT JOIN "hst_trade_${enterpriseGuid}_db".hst_groupon g ON t.order_guid = g.order_guid
            where
                g.is_delete = 0 and g.refund_order_guid is null
            group by g.order_guid
            ),
            table_num AS (
            SELECT
                COUNT ( guid ) AS table_num,
                store_guid
            FROM
                "hst_table_${enterpriseGuid}_db".hst_table_basic
            <if test="orgGuid != null and orgGuid.size() > 0 ">
                WHERE
                store_guid in
                <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                    #{guid}
                </foreach>
            </if>
            GROUP BY store_guid
            )
            SELECT
                td.order_type,
                tn.table_num,
                td.trade_mode,
                td.order_fee + COALESCE ( g.coupon_buy_price, 0 ) as "order_fee"
            FROM
                trade_sub td
                LEFT JOIN table_num tn ON td.store_guid = tn.store_guid
                LEFT JOIN groupon g on g.order_guid = td.order_guid
        ),
        takeaway AS (
        SELECT
            1 order_type,
            0 table_guid,
            1 trade_mode,
            CASE is_refund_success
                WHEN '1'
                    THEN COALESCE(ord.customer_actual_pay,0) - COALESCE(ord.customer_refund,0)
                ELSE ord.customer_actual_pay
            END AS order_fee
        FROM
            "hst_takeaway_${enterpriseGuid}_db".hst_takeout_order ord
        WHERE
            ord.business_day  between #{startTime} and #{endTime}
            <if test="orgGuid != null and orgGuid.size() > 0 ">
                and ord.store_guid in
                <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                    #{guid}
                </foreach>
            </if>
            AND ord.order_type = 0
            AND ord.order_status != '-1' and ord.refund_status != 2
        ),
        paymentInfo AS (
        SELECT
            store_guid,
            app_id
        FROM
            (
            SELECT
                store_guid,
                app_id,
                ROW_NUMBER ( ) OVER ( PARTITION BY app_id ORDER BY create_time ) AS row_num
            FROM
                hse_enterprise_db.hse_payment_info
            WHERE
                is_delete = 0
            ) subquery
        WHERE
            row_num = 1
        ),
        payAppId AS (
        SELECT
            pi.app_id
        FROM
            "hsb_business_${enterpriseGuid}_db".hsb_payment_type bpt
            LEFT JOIN paymentInfo pi ON pi.store_guid = bpt.store_guid
        WHERE
            bpt.payment_type = 1
            AND bpt.STATE = 0
            <if test="orgGuid != null and orgGuid.size() > 0 ">
                AND bpt.store_guid in
                <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                    #{guid}
                </foreach>
            </if>
        ),
        cloudPay AS (
        SELECT
            0 order_type,
            0 table_guid,
            1 trade_mode,
            SUM ( ROUND( amount :: NUMERIC / 100.0, 2 ) ) order_fee
        FROM
            (
            SELECT
                store_name,
                app_id,
                amount,
                pay_power_id,
                pay_power_name,
                pay_state,
                gmt_create,
                pay_guid
            FROM
                hpt_trading_db_01.hpt_order_01
            WHERE
                app_id IN ( SELECT app_id FROM payAppId )
                AND pay_state = 2
                AND pay_power_id IN ( 31, 51 )
                AND gmt_create between #{startTime} AND #{endTime}

                UNION
            SELECT
                store_name,
                app_id,
                amount,
                pay_power_id,
                pay_power_name,
                pay_state,
                gmt_create,
                pay_guid
            FROM
                hpt_trading_db_01.hpt_order_02
            WHERE
                app_id IN ( SELECT app_id FROM payAppId )
                AND pay_state = 2
                AND pay_power_id IN ( 31, 51 )
                AND gmt_create between #{startTime} AND #{endTime}
                 UNION
            SELECT
                store_name,
                app_id,
                amount,
                pay_power_id,
                pay_power_name,
                pay_state,
                gmt_create,
                pay_guid
            FROM
                hpt_trading_db_02.hpt_order_02
            WHERE
                app_id IN ( SELECT app_id FROM payAppId )
                AND pay_state = 2
                AND pay_power_id IN ( 31, 51 )
                AND gmt_create between #{startTime} AND #{endTime}
                 UNION
            SELECT
                store_name,
                app_id,
                amount,
                pay_power_id,
                pay_power_name,
                pay_state,
                gmt_create,
                pay_guid
            FROM
                hpt_trading_db_02.hpt_order_02
            WHERE
                app_id IN ( SELECT app_id FROM payAppId )
                AND pay_state = 2
                AND pay_power_id IN ( 31, 51 )
                AND gmt_create between #{startTime} AND #{endTime}
            ) pa
        )

        SELECT
            *
        FROM
            trade

        UNION ALL

        SELECT
            *
        FROM
            takeaway

        UNION ALL

        SELECT
            *
        FROM
            cloudPay
    )

    SELECT
        COALESCE(SUM(order_fee),0) v_1_,
        COALESCE(SUM( CASE order_type WHEN 0 THEN order_fee END),0) AS v_2_,
        COUNT(CASE WHEN order_type = 0 AND trade_mode = 0 THEN order_fee END) AS v_3_,
        COALESCE(round(AVG( CASE WHEN order_type = 0 AND trade_mode = 0 THEN order_fee END),2),0) AS V_4_,
        case
            when
                (SELECT COUNT ( guid ) AS table_num FROM "hst_table_${enterpriseGuid}_db".hst_table_basic
                <if test="orgGuid != null and orgGuid.size() > 0 ">
                    WHERE
                    store_guid in
                    <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                        #{guid}
                    </foreach>
                </if>) = 0
            then 0
            else
                round(
                (COUNT(case when order_type = 0 and trade_mode = 0 then order_fee end)  * 100)
                /
                (
                SELECT COUNT ( guid ) AS table_num FROM "hst_table_${enterpriseGuid}_db".hst_table_basic
                <if test="orgGuid != null and orgGuid.size() > 0 ">
                    WHERE
                    store_guid in
                    <foreach collection="orgGuid" index="index" item="guid" open="(" close=")" separator=",">
                        #{guid}
                    </foreach>
                </if>
                ) , 2)
        end as v_5_,
        COALESCE(SUM(CASE order_type WHEN 1 THEN order_fee END ),0) AS v_6_,
        COUNT( CASE order_type WHEN 1 THEN order_fee END) AS v_7_,
        COALESCE(round(AVG(  CASE order_type WHEN 1 THEN order_fee END),2),0) AS v_8_
    FROM
        order_data od
</script>
