
-- 增加毛利润
UPDATE `bi_boss`.`bi_report` SET `name` = '销售总额', `sql_list` = '[10]', `deleted` = 0, `gmt_create` = '2022-08-31 18:47:47', `gmt_modified` = '2023-12-20 18:25:03', `parent_id` = 4, `rank` = 3, `component` = '{\'type\':3,\'relative\':1,\'details\':2,\'col\':[{\'key\':\'id\',\'param\':1},{\'key\':\'name\',\'title\':\'名称\'},{\'key\':\'v_1\',\'title\':\'销售额\',\'joint\':\'￥%.2f\',\'orderBy\':1},{\'key\':\'gross_profit_amount\',\'title\':\'毛利润\',\'joint\':\'￥%.2f\',\'orderBy\':1}]}', `param` = '[]' WHERE `id` = 10;
UPDATE `bi_boss`.`bi_report` SET `name` = '商品分类', `sql_list` = '[24]', `deleted` = 0, `gmt_create` = '2022-09-08 10:02:58', `gmt_modified` = '2023-12-20 19:24:31', `parent_id` = 5, `rank` = 5, `component` = '{\r\n  \'type\': 3,\r\n  \'relative\': 1,\r\n  \'col\': [\r\n    {\r\n      \'key\': \'id\',\r\n      \'param\': 1\r\n    },\r\n    {\r\n      \'key\': \'name\',\r\n      \'title\': \'分类名称（品牌）\'\r\n    },\r\n    {\r\n      \'key\': \'v_type_salce\',\r\n      \'title\': \'销量\'\r\n    },\r\n    {\r\n      \'key\': \'type_amount\',\r\n      \'title\': \'销售金额\',\r\n      \"joint\": \"￥%.2f\"\r\n    },\r\n		{\r\n      \'key\': \'gross_profit_amount\',\r\n      \'title\': \'毛利润\',\r\n      \"joint\": \"￥%.2f\"\r\n    },\r\n    {\r\n      \'key\': \'type_discount_price\',\r\n      \'title\': \'实付金额\',\r\n      \"joint\": \"￥%.2f\"\r\n    },\r\n    {\r\n      \'key\': \'type_ratio\',\r\n      \'title\': \'占比\',\r\n      \'joint\': \'%.2f%%\'\r\n    }\r\n  ]\r\n}', `param` = '[]' WHERE `id` = 17;
UPDATE `bi_boss`.`bi_report` SET `name` = '用户运营', `sql_list` = '[]', `deleted` = 0, `gmt_create` = '2022-09-13 16:15:24', `gmt_modified` = '2022-09-13 16:15:24', `parent_id` = 2, `rank` = 1, `component` = NULL, `param` = NULL WHERE `id` = 18;

