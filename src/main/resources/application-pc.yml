#默认路由地址
spring:
  application:
    name: holder-saas-gateway
  zipkin:
    base-url: http://localhost:9411/
  sleuth:
    #将采样比例设置为 1.0，也就是全部都需要。默认是 0.1
    sampler:
      probability: 1.0
  redis:
    host: ***************
    port: 6379
    password: eIx6TynJq
    database: 15
  cloud:
    gateway:
      elapsedTime:
        business: true
        gateway: true
      authen: false
      author: false
      offline: false
      sensitive: true
      basic-auth: Basic YWRtaW46cHVibGlj
      interceptor: /actuator/gateway/.*
      routes:
      - id: app-sso-route
        uri: lb://holder-saas-sso
        predicates:
        - Path=/app/sso/**
        filters:
          - RewritePath=/sso/(?<segment>.*), /$\{segment}
      - id: app-route
        uri: lb://holder-saas-aggregation-app
        predicates:
        - Path=/app/**
      - id: merchant-route
        uri: lb://holder-saas-aggregation-merchant
        predicates:
        - Path=/merchant/**
        filters:
        - name: RequestRateLimiter
          args:
            key-resolver: '#{@remoteAddressKeyResolver}'
            redis-rate-limiter.replenishRate: 30
            redis-rate-limiter.burstCapacity: 30
      - id: cloud-route
        uri: lb://holder-saas-cloud-aggregation
        predicates:
        - Path=/cloud/**
      - id: jhCallBack-route
        uri: lb://holder-saas-store-trading-center
        predicates:
        - Path=/jh/pay/notify
      - id: sso-route
        uri: lb://holder-saas-sso
        predicates:
        - Path=/sso/**
      - id: weixin-route
        uri: lb://holder-saas-aggregation-weixin
        predicates:
        - Path=/weixin/**
      - id: weixin-normal-route
        uri: lb:ws://holder-saas-aggregation-weixin
        predicates:
        - Path=/weixin-normal/**
      - id: phoneapp-route
        uri: lb://holder-saas-aggregation-phoneapp
        predicates:
        - Path=/phoneapp/**
      - id: test-route
        uri: http://localhost:10001
        predicates:
        - Path=/test/**
        filters:
        - name: RequestRateLimiter
          args:
            key-resolver: '#{@remoteAddressKeyResolver}'
            redis-rate-limiter.replenishRate: 10
            redis-rate-limiter.burstCapacity: 10
      - id: merchant-member-route
        uri: lb://holder-saas-aggregation-merchant-member
        predicates:
        - Path=/merchantMember/**
      - id: merchant-member-msm-route
        uri: lb://holder-saas-member-account-msm
        predicates:
        - Path=/merchantMemberMsm/**
      - id: mdm-store-route
        uri: lb://holder-saas-store-mdm
        predicates:
        - Path=/mdm_store/**
      - id: apollo-route
        uri: http://localhost:20000
        predicates:
        - Path=/apollo/**

#放开网关动态配置接口
management:
  endpoints:
    web:
      exposure:
        include: "*"
server:
  port: 8151

#注册中心
eureka:
  instance:
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
  client:
    serviceUrl:
      #      defaultZone: http://***************:8141/eureka/
      defaultZone: http://localhost:8141/eureka/


ribbon:
  ConnectionTimeout: 100000
  ReadTimeout: 100000

switch:
  offline:
    enabled: true
    gray:
      include: all
      exclude: