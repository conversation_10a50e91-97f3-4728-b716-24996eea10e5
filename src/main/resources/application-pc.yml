server:
  port: 8870
  undertow:
    max-http-post-size: 0
    io-threads: 4
    worker-threads: 32
    buffer-size: 1024
    direct-buffers: true

swagger:
  enable: true
verify:
  enable: false

spring:
  rocketmq:
    producer-group-name: ${spring.application.name}
    namesrv-addr: ***************:9876
  redis:
    host: **************
    database: 10
    password: eIx6TynJq
    port: 36379
  jackson:
    date-format: java.text.SimpleDateFormat
  application:
    name: holder-saas-sso
  # 操作日志写入的kafka配置
  kafka:
    bootstrap-servers: ***************:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
eureka:
  instance:
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 10
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
  client:
    service-url:
      defaultZone: http://localhost:8141/eureka
framework:
  log:
    service-url: http://BASE-SERVICE
    max-thread: 6
    core-thread: 3
mybatis:
  type-aliases-package: com.holderzone.authorization.domain
  mapper-locations: classpath:mapper/*.xml
token:
  server:
    secret: HolderSecret
  ttl: 7200000
  app:
    ttl: 86400000
  agent:
    ttl: 31536000000

ribbon:
  ConnectionTimeout: 100000
  ReadTimeout: 100000
hystrix:
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      execution:
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 2000
          thread:
            timeoutInMilliseconds: 15000
        timeout:
          enable: true
  threadpool:
    default:
      coreSize: 50
      maximumSize: 500
      maxQueueSize: 500
      queueSizeRejectionThreshold: 300
      keepAliveTimeMinutes: 10
      allowMaximumSizeToDivergeFromCoreSize: true
      metrics:
        rollingStats:
          numBuckets: 500
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 10000
    max-connections: 500
    time-to-live: 30
    max-connections-per-route: 300
mdm:
  address: http://localhost:7102/user
  skipSSL: true
  signature: raZI/JvkZqiY0KcBUDuwSA==
  developerId: 243798b9-6148-473f-adbc-598f503aee84
  loginUrl: ${mdm.address}/find/info
  findUserByUserGuidOrTelUrl: ${mdm.address}/find
  findUserByAccountUrl: ${mdm.address}/query/account
  updateUserUrl: ${mdm.address}