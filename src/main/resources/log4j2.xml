<?xml version="1.0" encoding="UTF-8"?>
<!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出-->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数-->
<!-- 使用教程详见：http://logging.apache.org/log4j/2.x/manual/ -->
<configuration status="WARN" monitorInterval="30">
    <!--预先定义通用pattern-->
    <properties>
       <!-- <property name="pattern">
            %magenta{[%d{yyyy-MM-dd HH:mm:ss:SSS}{GMT+8}]}[%-5p][%t][%C.%M:%L][%X{traceId}][%highlight{%m}{WARN=bright yellow}] %n
        </property>-->
        <property name="filePath">./logs</property>
        <property name="pattern">
            [%d{yyyy-MM-dd HH:mm:ss:SSS}{GMT+8}][%-5p][%t][%C.%M:%L][%X{traceId}][%X {storeGuid}][%m] %n
        </property>
    </properties>
    <!--先定义所有的appender-->
    <appenders>
        <!--这个输出控制台的配置-->
        <console name="Console-withguid" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${pattern}"/>
        </console>
        <!--这个输出控制台的配置-->
        <console name="Console" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${pattern}"/>
        </console>
        <!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年-月建立的文件夹下面并进行压缩，作为存档-->
        <RollingFile name="RollingFileInfo" fileName="${filePath}/info.log"
                     filePattern="${filePath}/$${date:yyyy-MM}/info-%i.log">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <!-- 该过滤器只将info级别输入到文件-->
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${pattern}"/>
            <Policies>
                <!-- 配置单个文件最大size，size过大追加或删除会存在较大性能损耗 -->
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性若不设置，则默认为最多同一文件夹下7个文件，这里设置为40，则设置的info日志30天最大为1Gb，同时自动清理14天前的日志 -->
            <DefaultRolloverStrategy max="20">
                <Delete basePath="${filePath}" maxDepth="2">
                    <IfFileName glob="*/info-*.log">
                        <IfLastModified age="14d"/>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <RollingFile name="RollingFileWarn" fileName="${filePath}/warn.log"
                     filePattern="${filePath}/$${date:yyyy-MM}/warn-%i.log">
            <!-- 该过滤器只将warn级别输入到文件-->
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${pattern}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="20">
                <Delete basePath="${filePath}" maxDepth="2">
                    <IfFileName glob="*/warn-*.log">
                        <IfLastModified age="14d"/>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <RollingFile name="RollingFileError" fileName="${filePath}/error.log"
                     filePattern="${filePath}/$${date:yyyy-MM}/error-%i.log">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${pattern}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="20">
                <Delete basePath="${filePath}" maxDepth="2">
                    <IfFileName glob="*/error-*.log">
                        <IfLastModified age="14d"/>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </appenders>
    <!--然后定义logger，只有定义了logger并引入的appender，appender才会生效-->
    <loggers>
        <!--过滤掉spring和mybatis的一些无用的DEBUG信息-->
        <logger name="org.springframework" level="INFO"/>
        <!--<logger name="org.mybatis" level="INFO"/>-->
        <root level="INFO">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </root>
        <logger name="com.holderzone.saas.store.order.mapper" level="INFO" additivity="true">
            <appender-ref ref="Console"/>
        </logger>
    </loggers>

</configuration>