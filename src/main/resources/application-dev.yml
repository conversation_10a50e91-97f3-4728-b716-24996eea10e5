ref:
  eureka:
    hostname: ***************
    port: 8141
  rocketmq:
    hostname: ***************
    port: 9876

spring:
  application:
    name: holder-saas-store-mdm
  jackson:
    default-property-inclusion: non_null
  rocketmq:
    namesrv-addr: ${ref.rocketmq.hostname}:${ref.rocketmq.port}
    producer-group-name: storeMdmProducer
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120

eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${ref.eureka.hostname}:${ref.eureka.port}/eureka/

feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000

hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000

canal:
  client:
    enabled: true
    destinations: mdm_hso_organization,mdm_hss_staff,mdm_hsi_item
    instance:
      username:
      password:
      cluster-enabled: true
      zookeeper-address: ***************:2181
    start-async: false
#todo complete the values
mdm:
  developerId: 5cdcd808-cac0-492a-8ba9-2b7fbe4cd4f1
  developerKey: S5+XdwLyIsg+Akl3LFMNiQ==
  reqUrl: http://***************:7101/mdm
  requireSign: false
  initSyncStep: 50
  initAgainAllowed: false
  triggerAgainAllowed: true
  secretKey: a4b90401-d962-46dd-b51e-472123857d7b
  repeatedCode:
    - MD000012
    - MD061015
    - MD062001
dynamic:
  intercept:
    datasource:
      include-url: /**
      exclude-url:
    redis:
      include-url: /**
  redis:
    enabled: true
  server:
    server-code: holder_saas_store_mdm
  servers:
    holder_saas_store_mdm:
      master_data_source:
        url: *******************************************************************************************************************************************************************************************************************************************************************
        password: mysqlHolder
        username: root