dev:
  hostname: ***************
  eureka:
    hostname: ${dev.hostname}
spring:
  application:
    name: holder-saas-store-weixin
  sleuth:
    sampler:
      probability: 0.0
  rocketmq:
    namesrv-addr: ***************:9876
    #    访问地址：***************:8080
    producer-group-name: tradeProduce
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
  #    没有个性化的redis可以去掉
  #  redis:
  #   database: 0
  #   host: **************
  #   port: 6379
  #   password: eIx6TynJq
  #   timeout: 5000ms
  #   jedis:
  #     pool:
  #       max-active: 50
  #       max-idle: 50
  #       min-idle: 20
  #       max-wait: -1ms
  aop:
    auto: true
  jackson:
    default-property-inclusion: non_null

  mqtt:
    username: admin
    password: public
    url: tcp://***************:8080
    client:
      id: mqttId
    default:
      topic: topic
    completionTimeout: 3000



# feign配置，使用appach client 替代默认urlConnection
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 6000
    max-connections: 50
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 50
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000
eureka:
  instance:
    #  定义服务失效的时间，默认为90秒。
    lease-expiration-duration-in-seconds: 120
    #    服务续约任务的调用间隔时间，默认为30秒
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    hostname: ${dev.eureka.hostname}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${dev.eureka.hostname}:8141/eureka/
#      defaultZone: http://***************:48141/eureka/
#      访问地址：**************:31141

framework:
  log:
    service-url: http://base-service
    core-thread: 5
    max-thread: 6

logging:
  level:
    com.holderzone.saas.store.wexin: info
    com.holderzone.saas.store.wexin.mapper: debug

wechat:
#  appId: wx05f4d486f1c65c99
#  appSecret: 3db3093c87ed3e1b8867ca9d234fb54c
#  appId: wx15cb221d6c3578ce
#  appSecret: b6e395f3b1e5d37077b8ac23bc741166
  appId: wxeaf98ac5815cc771
  appSecret: f7eb3fe92cd8aaae1524802a52a18d5c
  #  appId: wxaf5264ac26bde66a
  #  appSecret: e873c783dd3744dbd0753e2bdf95558e
  aesKey: WvS6YdXQoXMDoH6NWfNF9OryyrBVPskrVDtN12Y9xVw
  token: nNGpTv3JX7f1X3GQtQ
  open:
    #三方平台AppId
    componentAppId: wx8384aec332e3ed29
    #三方平台appSecret
#    componentSecret: cbd5a66523f96f2d6d26c539ff8f4dcc 开发三方平台
    #secret
    componentSecret: 109b59106658b38eea7b8bd2b0549e77
    #三方平台token
    componentToken: HelloWorld
    #三方平台AesKey
    componentAesKey: HolderZoneWeixinOpen0123456789abcdefghijklm
  redirectUrl: https://mch-dev.holderzone.cn/gateway/merchant/wx_open/query_auth?enterpriseGuid=%s&brandGuid=%s
  callBackSuccessPage: <script>window.onload=function(){location.href='http://rewq.easy.echosite.cn/authJumpSuccess?message=%s'}</script>
  callBackErrorPage: <script>window.onload=function(){location.href='http://rewq.easy.echosite.cn/authJumpError?message=%s'}</script>
  mp:
    appId: wx03dde6177f8ef6c5
    sendBackMessage: 欢迎光临，请<a href='%s'>点击此处</a>进行点餐
    #带参数二维码，用户扫码关注后推送信息链接地址
    baseJumpUrl: https://mch-dev.holderzone.cn/gateway/weixin/wx_handler/%s/redirect?eventKey=%s
    #普通二维码URL
    baseQrCodeUrl: https://mch-dev.holderzone.cn/gateway/weixin/wx_mp/call_back?eventKey=%s
    #点餐页URL
    orderPageUrl: http://**************:8080/shopmenu/#/?msgkey=%s&enterpriseguid=%s
    #会员结账跳转页
    memberOrderPage: http://**************:8082/orderDetails?enterpriseGuid=%s&storeGuid=%s&openId=%s&orderGuid=%s
    miniProgram: "[{\"erpriseGuid\":\"2002261114270540001\",\"appid\":\"wx4b788f7d5151d9b5\",\"pagepath\":\"package/otherScanOrder/choosePerson/choosePerson\",\"thumbMediaId\":\"DknO94BeKFpqTRTlkK4l0z8R7cfOETWXKTDUgbV6gAM\",\"title\":\"何师烧烤（storeName）欢迎您！当前桌号【tableCode】。\"},{\"erpriseGuid\":\"2002261114270540002\",\"appid\":\"wx4b788f7d5151d9b5\",\"pagepath\":\"package/otherScanOrder/choosePerson/choosePerson\",\"thumbMediaId\":\"DknO94BeKFpqTRTlkK4l0z8R7cfOETWXKTDUgbV6gAM\",\"title\":\"何师烧烤（storeName）欢迎您！当前桌号【tableCode】。\"}]"
#    miniProgram:
#      #需要发送卡片消息的企业id，没有设置为0
#      erpriseGuids: 2002261114270540001
#      #需要发送卡片消息的门店guid，全部设置为all
#      storeGuids: 6638270377413836800
#      #小程序appid
#      appid: wx4b788f7d5151d9b5
#      pagepath: package/otherScanOrder/choosePerson/choosePerson
#      #公众号素材id，卡片封面
#      thumb_media_id: DknO94BeKFpqTRTlkK4l0z8R7cfOETWXKTDUgbV6gAM
#      #卡片标题
#      title: 何师烧烤（storeName）欢迎您！当前桌号【tableCode】。
  queue:
    #排队详情页面
    detailPage: http://rewq.easy.echosite.cn/queueresult?msgkey=%s&enterpriseguid=%s
    #门店列表页面
    shopListPage: http://rewq.easy.echosite.cn/shoplist?msgkey=%s&enterpriseguid=%s
  member:
    loginUrl: http://weixin-dev.holderzone.cn/member/#/login?msg=%s&enterpriseguid=%s&brandguid=%s&appid=%s
    #会员前端地址
    baseUrl: http://weixin-dev.holderzone.cn/member/#/?msgkey=%s&enterpriseguid=%s
    bindingUrl: http://weixin-dev.holderzone.cn/member/#/bindwechat?msgkey=%s&enterpriseguid=%s
  graphic-message:
    keyWord: 魅力城,华阳
    mediaId: KQgdBmtLYcJEeXLNkxlWanVEaOOEehWYv66-2LrZxL4,KQgdBmtLYcJEeXLNkxlWaiC7Pqs6gfMlsC89UCPEGpg
    appId: wx03dde6177f8ef6c5
  #二维码回调域名地址
  qr:
    redirectUrl: https://mch-sit.holderzone.cn

h5:
  appId: wxeaf98ac5815cc771
  appSecret: f7eb3fe92cd8aaae1524802a52a18d5c
  aesKey: WvS6YdXQoXMDoH6NWfNF9OryyrBVPskrVDtN12Y9xVw
  token: nNGpTv3JX7f1X3GQtQ

sina:
  appKey: 2115639166
  appSecret: 9fb80e138df8bf23a83b5ed51ff47b59
  shortenApiUrl: http://api.t.sina.com.cn/short_url/shorten.json
#购物车缓存时间
order_time: 2
map:
  key: e7fe4d1630e790f01c067b13934670f6
wxStorePay:
  appId: 7b44d9d3-2760-41e7-90d5-d08b93d2d693
  mchntName: 成都掌控者网络科技有限公司
  appSecret: ZPKwzfYZMiRI78/URgcioA==
workbench:
  appId: wx05f4d486f1c65c99
  appSecret: 3db3093c87ed3e1b8867ca9d234fb54c
  loginUrl: https://mch-dev.holderzone.cn/#/login?openId=%s
memberPay:
  key: x2ulFqJhFFFh3el9
tongchidao:
  table_ticket_info: https://zhuancan-sit.holderzone.cn/merchant/api/qr-code-order-holder-text
# 测试 table_ticket_info: http://test.canyingdongli.com/merchant/api/qr-code-order-holder-text
  # 正式 http://clt.canyingdongli.com/merchant/api/qr-code-order-holder-text
redisson:
  address: redis://**************:36380
  password: eIx6TynJq
  database: 2
  single:
    enabled: true
member:
  marketing:
    host: https://member-center-h5-sr.holderzone.cn