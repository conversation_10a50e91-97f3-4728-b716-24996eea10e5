test:
  hostname: test-holder-saas-register
  eureka:
    hostname: test-holder-saas-register
  zipkin:
    hostname: ${spring.cloud.client.ip-address}
  rabbitmq:
    hostname: test-holder-saas-rocketmq
  redis:
    hostname: test-holder-saas-redis-mch
spring:
  application:
    name: holder-saas-aggregation-app
  zipkin:
    base-url: http://***************:9411/
    enabled: false
  sleuth:
    enabled: false
    feign:
      enabled: false
  redis:
    database: 0
    host: ${test.redis.hostname}
    port: 6379
    password: eIx6TynJq
    timeout: 5000ms
    jedis:
      pool:
        max-active: 50
        max-idle: 50
        min-idle: 20
        max-wait: -1ms
  rocketmq:
    namesrv-addr: ${test.rabbitmq.hostname}:9876
    producer-group-name:
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
  aop:
    auto: true
  jackson:
    default-property-inclusion: non_null
  kafka:
    bootstrap-servers: test-holder-saas-kafka:9092
    producer:
       key-serializer: org.apache.kafka.common.serialization.StringSerializer
       value-serializer: org.apache.kafka.common.serialization.StringSerializer
  messages:
    basename: i18n/messages
#server:
#  port: 8161
#  tomcat:
#    accept-count: 200
#    max-connections: 5000
#    max-threads: 450


server:
  port: 8161
  undertow:
    max-http-post-size: 0
    # 设置 IO 线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个 CPU 核心一个线程,数量和 CPU 内核数目一样即可
    io-threads: 4
    # 阻塞任务线程池, 当执行类似 servlet 请求阻塞操作, undertow 会从这个线程池中取得线程,它的值设置取决于系统的负载 io-threads*8
    worker-threads: 32
    # 以下的配置会影响 buffer,这些 buffer 会用于服务器连接的 IO 操作,有点类似 netty 的池化内存管理
    # 每块 buffer 的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 每个区分配的 buffer 数量 , 所以 pool 的大小是 buffer-size * buffers-per-region
    # buffers-per-region: 1024 # 这个参数不需要写了
    # 是否分配的直接内存
    direct-buffers: true


# feign配置，使用appach client 替代默认urlConnection
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 30000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    hostname: ${test.eureka.hostname}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${test.eureka.hostname}:48141/eureka/
file:
  size: 3MB
  total:
    size: 5MB

redisson:
  address: redis://${test.redis.hostname}:6379
  password: eIx6TynJq
  database: 0
  single:
    enabled: true

efk:
  enable: true
seata:
  registry:
    type: eureka
    eureka:
      service-url: ${eureka.client.serviceUrl.defaultZone}
  service:
    vgroup-mapping:
      store_tx_group: "holder-saas-seata"
  tx-service-group:  store_tx_group
weChat:
  accessTokenUrl: https://api.weixin.qq.com/cgi-bin/token
  appId: wxd47961addcd29455
  secret: 53e4a178e87282869209500af85fbf67
  ticketUrl: https://api.weixin.qq.com/cgi-bin/ticket/getticket
  #  demo给的是snsapi_login，此处是坑
  scope: snsapi_userinfo
  accessTokenUrl2: https://api.weixin.qq.com/sns/oauth2/access_token
  userInfoUrl: https://api.weixin.qq.com/sns/userinfo
zhuancan:
  sendCallMessage: https://zhuancan-sit.holderzone.cn/shop/api/send_call_message
  batchSendCallMessage: https://zhuancan-sit.holderzone.cn/shop/api/batch_send_call_message
  memberRedeem: https://zhuancan-sit.holderzone.cn/island/api/member/do-redeem
  updateVolumeRelevance: https://zhuancan-sit.holderzone.cn/island/api/member/update/volume/relevance
ipass:
  receiptValidate: https://invoic-sit.holderzone.cn/invoice/receipt/v1/validate
  authResult: https://invoic-sit.holderzone.cn/invoice/tax/v1/auth/result
  noteLogin: https://invoic-sit.holderzone.cn/invoice/tax/v1/login
  authenticationUrl: https://invoic-sit.holderzone.cn/invoice/tax/v1/auth/url
  queryResidueLimit: https://invoic-sit.holderzone.cn/invoice/tax/v1/org/sxed
  orderInvoiceUrl: https://invoic-sit.holderzone.cn/invoice/order/v1/receipt/url
  apiKey: 2qYabEuOr13ip6uA2JBvds3Z
  apiSecret: 7Au7ai3HSnXidgsVASoH4S2XR3tfto
  
api:
  version:
    control:
      - min: 282
        uri: /reserve/launch,/reserve/modify,/reserve/pass,/reserve/obtain,/reserve/cancle
      - min: 291
        uri: /add_item,/change_item,/cancel_change_item

sensitive:
  enable: true

#放开网关动态配置接口
management:
  endpoints:
    web:
      exposure:
        include: "health"
  endpoint:
    shutdown:
      enabled: true
member:
  marketing:
      host: https://member-center-h5-sr.holderzone.cn