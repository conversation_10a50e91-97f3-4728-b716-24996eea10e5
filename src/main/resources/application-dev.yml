test:
  eureka:
    hostname: test-holder-saas-register
  rocketmq:
    hostname: test-holder-saas-rocketmq
  redis:
    hostname: test-holder-saas-redis-mch
spring:
  application:
    name: holder-saas-aggregation-merchant
  zipkin:
    base-url: http://***************:9411/
    enabled: false
    service:
      name: holder-saas-aggregation-merchant
    sender:
      type: web
      enabled: false
  sleuth:
    feign:
      enabled: false
    sampler:
      probability: 1.0
    enabled: false
  redis:
    database: 0
    host: ${test.redis.hostname}
    port: 6379
    password: eIx6TynJq
    timeout: 5000ms
    jedis:
      pool:
        max-active: 50
        max-idle: 50
        min-idle: 20
        max-wait: -1ms
  rocketmq:
    namesrv-addr: ${test.rocketmq.hostname}:9876
    producer-group-name: payProduce
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
  aop:
    auto: true
  jackson:
    default-property-inclusion: non_null
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false
  kafka:
    bootstrap-servers: test-holder-saas-kafka:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

server:
  port: 8162
  undertow:
    max-http-post-size: 0
    # 设置 IO 线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个 CPU 核心一个线程,数量和 CPU 内核数目一样即可
    io-threads: 4
    # 阻塞任务线程池, 当执行类似 servlet 请求阻塞操作, undertow 会从这个线程池中取得线程,它的值设置取决于系统的负载 io-threads*8
    worker-threads: 32
    # 以下的配置会影响 buffer,这些 buffer 会用于服务器连接的 IO 操作,有点类似 netty 的池化内存管理
    # 每块 buffer 的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 每个区分配的 buffer 数量 , 所以 pool 的大小是 buffer-size * buffers-per-region
    # buffers-per-region: 1024 # 这个参数不需要写了
    # 是否分配的直接内存
    direct-buffers: true


# feign配置，使用appach client 替代默认urlConnection
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 600000
        readTimeout: 600000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 60000
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    hostname: ${test.eureka.hostname}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${test.eureka.hostname}:48141/eureka/
file:
  size: 20MB
  total:
    size: 20MB

# 登录时是否验证验证码
validate:
  verify-code-img: false

efk:
  enable: true

small:
  ENTERPRISE_FOR_HESHI: 1911070930206380005

applets:
  downTick: http://test.canyingdongli.com/merchant/api/qr-code-order-holder

queryES:
  # 多个企业逗号分隔
  enterpriseGuidList: 887f2181-eb06-4d77-b914-7c37c884952c

openapi:
  apiKey: 5b221a4f-53d1-48c8-980a-b5369ef55959
  apiSecret: gAQFJ4UjmFEadHb/r+CGdg==
member:
  marketing:
    host: https://member-center-h5-sr.holderzone.cn
