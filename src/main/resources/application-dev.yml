eureka:
  client:
    service-url:
      defaultZone: http://***************:8141/eureka
  instance:
      lease-renewal-interval-in-seconds: 5
      lease-expiration-duration-in-seconds: 10
      prefer-ip-address: true
      instance-id: ${spring.cloud.client.ip-address}:${server.port}
spring:
  rocketmq:
    namesrv-addr: ***************:9876
    producer-group-name: printProducer
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
alibaba:
  acm:
    accessKey: LTAItOsX8t1aGONM
    secretKey: S3fjKGfssXqb2vJcZpZCSPwrSMSw4U
    namespace: 52a12a5d-1982-47d9-b7af-716e2b380ca1
    refresh:
      enabled: true
    time-out: 6000
    group: ${spring.application.group}
    endpoint: acm.aliyun.com
takeout:
  whitelist: all
feign:
  starter:
    async-executor:
      custom-enabled: true
      type: thread_pool
      core-pool-size: 10
      max-pool-size: 20
      keep-alive-seconds: 4
      queue-capacity: 512
      thread-local-supported: true

seata:
  registry:
    type: eureka
    eureka:
      service-url: http://***************:8141/eureka
  service:
    vgroup-mapping:
      store_tx_group: "holder-saas-seata"
    disable-global-transaction: false
  tx-service-group:  store_tx_group
  enable-auto-data-source-proxy: false

feie:
  url: http://api.feieyun.cn/Api/Open/
  user: <EMAIL>
  uKey: 2rauugswq99yPFcc
