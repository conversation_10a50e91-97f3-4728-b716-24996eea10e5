dev:
  hostname: ***************
spring:
  application:
    name: holder-saas-store-pay
  rocketmq:
    namesrv-addr: ***************:9876
    producer-group-name: saas-agg-pay-group
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
  jackson:
    default-property-inclusion: non_null
  redis:
    database: 14
    host: **************
    port: 36380
    password: eIx6TynJq
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 200 #连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 5 #连接池中的最小空闲连接
        max-wait: -1ms # 连接池最大阻塞等待时间（使用负值表示没有限制）

server:
  undertow:
    max-http-post-size: 0
    io-threads: 4
    worker-threads: 32
    buffer-size: 1024
    direct-buffers: true
# feign配置，使用appach client 替代默认urlConnection
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000
eureka:
  instance:
    lease-expiration-duration-in-seconds: 90
    lease-renewal-interval-in-seconds: 30
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${dev.hostname}:8141/eureka/

# 动态数据源
dynamic:
  intercept:
    datasource:
      include-url: /**
      exclude-url:
    #添加需要切换 redis 数据源的接口路径
    redis:
      include-url: /**
  redis:
    enabled: false
  server:
    #如果本服务需要对表分库，那么需要配置本服务的所有表名,以逗号隔开
    #sharding-tables: person
    #必须制定当前服务的名称
    server-code: holder_saas_store_pay

mybatis-plus:
  mapper-locations: classpath*:/mapper/**Mapper.xml
  typeAliasesPackage: com.holderzone.holder.saas.store.pay.entity.domain
  configuration:
    map-underscore-to-camel-case: true
    aggressive-lazy-loading: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    cache-enabled: false
    call-setters-on-nulls: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    refresh: false
    db-config:
      db-type: mysql
      id-type: auto
      field-strategy: not_empty
      logic-delete-value: 1
      logic-not-delete-value: 0

mybatis-page:
  enable: false

developer:
  id: 10000
  key: A6041E8B17CA0082EECA481D623137F2


agg-base:
  own_url: http://${spring.cloud.client.ip-address}:${server.port}
  url: http://pay-dev.holderzone.cn/
  h5Url: http://pay-dev.holderzone.cn/pay

agg-pay:
  callBack: ${agg-base.own_url}/agg/callback
  pay: ${agg-base.url}/zuul/agg/prepay/barcode
  polling: ${agg-base.url}/zuul/agg/prepay/polling
  reserve: ${agg-base.url}/zuul/agg/prepay/reverse
#  reserve: http://dev.wudl.xyz:8762/agg/prepay/reverse
  query: ${agg-base.url}/zuul/agg/prepay/query
  queryPaySt: ${agg-base.url}/zuul/agg/prepay/queryPaySt
  refund: ${agg-base.url}/zuul/agg/refund/refundReq
  refundPolling: ${agg-base.url}/zuul/agg/refund/polling
  refundQuery: ${agg-base.url}/zuul/agg/refund/query
  mchntQuery: ${agg-base.url}/zuul/agg/mchnt/validate
  pollingTimes: 120
  wechath5pay: ${agg-base.h5Url}/pay/preTrading
  wechath5polling: ${agg-base.h5Url}/pay/polling?orderGuid=
