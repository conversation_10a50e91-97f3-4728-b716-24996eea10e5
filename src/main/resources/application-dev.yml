dev:
  hostname: ***************

server:
  port: 8997
  undertow:
    max-http-post-size: 0
    io-threads: 4
    worker-threads: 32
    buffer-size: 1024
    direct-buffers: true
elasticsearch:
  ip: ***************
  port: 9200
  pool: 1
  cluster:
    name: elasticsearch
spring:
  application:
    name: holder-saas-store-report
  redis:
    host: **************
    port: 36380
    database: 3
    password: eIx6TynJq
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
      timeout: 10000ms
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ************************************************************************************************************************************************
    username: postgres
    password: pgtest
    hikari:
      minimum-idle: 30
      maximum-pool-size: 200
      minimumIdle: 5
      maximumPoolSize: 10
      autoCommit: true
      idleTimeout: 30000
      poolName: HikariCP1
      maxLifetime: 1800000
      connectionTimeout: 80000

mybatis-plus:
  #外部化xml配置
  #config-location: classpath:mybatis-config.xml
  #指定外部化 MyBatis Properties 配置，通过该配置可以抽离配置，实现不同环境的配置部署
  #configuration-properties: classpath:mybatis/config.properties
  #xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  mapper-locations: classpath*:/mapper/*.xml
  #MyBaits 别名包扫描路径，通过该属性可以给包中的类注册别名
  type-aliases-package: com.holderzone.holder.saas.store.report.entity
  configuration:
    #开启sql日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    aggressive-lazy-loading: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    cache-enabled: false
    call-setters-on-nulls: false
  global-config:
    refresh: false
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
#      defaultZone: http://${dev.hostname}:8141/eureka/
      defaultZone: http://***************:48141//eureka/

feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 60000
report:
  update-es-job:
    sleepTime: 60
    hours: 4,5,9,10,11,12,13
    sale: true
    size: 10000
    #销售明细数据不同步时间段
    noneSaleHours: 22
    exclusiveEnterpriseGuid: 887f2181-eb06-4d77-b914-7c37c884952c,2009231205009110009,2105061516285210007,2211021421001510002,2303211543213970000,2304181507355250007,2307071417082910004,2308271029561490008,2309061014518340003,6572320320838553601,6590427993548193794

oss:
  bucket-name: holder-dev
  endpoint: https://oss-cn-hangzhou.aliyuncs.com
  access-key-secret: 9hsyhtPPbQb0mBBnKgCdS8SJk03yuO
  access-key-id: LTAIqjcx3sGrF5fH
  folder-name: framework-dev
  holder-oss-domain-name: https://holder-dev.oss-cn-hangzhou.aliyuncs.com