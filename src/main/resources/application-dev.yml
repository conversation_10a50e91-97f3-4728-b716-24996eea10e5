test:
  hostname: ***************
  eureka:
    hostname: ***************
  rabbitmq:
    hostname: ***************
  redis:
    hostname: **************
spring:
  application:
    name: holder-saas-aggregation-weixin
  sleuth:
    sampler:
      probability: 1.0
  redis:
    database: 0
    host: ${test.redis.hostname}
    port: 36380
    password: eIx6TynJq
    timeout: 5000ms
    jedis:
      pool:
        max-active: 50
        max-idle: 50
        min-idle: 20
        max-wait: -1ms
  rocketmq:
    namesrv-addr: ***************:9876
    producer-group-name: aggWxProduce
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
  aop:
    auto: true
  jackson:
    default-property-inclusion: non_null
  kafka:
    bootstrap-servers: ***************:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#server:
#  tomcat:
#    accept-count: 200
#    max-connections: 5000
#    max-threads: 450

server:
  port: 8163
  undertow:
    max-http-post-size: 0
    # 设置 IO 线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个 CPU 核心一个线程,数量和 CPU 内核数目一样即可
    io-threads: 4
    # 阻塞任务线程池, 当执行类似 servlet 请求阻塞操作, undertow 会从这个线程池中取得线程,它的值设置取决于系统的负载 io-threads*8
    worker-threads: 32
    # 以下的配置会影响 buffer,这些 buffer 会用于服务器连接的 IO 操作,有点类似 netty 的池化内存管理
    # 每块 buffer 的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 每个区分配的 buffer 数量 , 所以 pool 的大小是 buffer-size * buffers-per-region
    # buffers-per-region: 1024 # 这个参数不需要写了
    # 是否分配的直接内存
    direct-buffers: true

# feign配置，使用appach client 替代默认urlConnection
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 6000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
    #开启压缩
  compression:
    request:
      enabled: true # 开启请求压缩
      #最小触发压缩的大小
      min-request-size: 2048
      #触发压缩数据类型，（注意不能使用"" ,''）
      mime-types: text/xml, application/xml, application/json
    response:
      enabled: true # 开启响应压缩
  logging:
    level:
      com.holderzone.holder.saas.aggregation.weixin.service.rpc.account: debug
      com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.WxHsmMemberBasicService: debug
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    hostname: ${test.eureka.hostname}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${test.eureka.hostname}:8141/eureka/
file:
  size: 3MB
  total:
    size: 5MB

# 登录时是否验证验证码
validate:
  verify-code-img: false

efk:
  enable: false
frontend:
  ordering-index-page: http://**************:8080/shopmenu?msgKey=%s&enterpriseGuid=%s
  ordering-message-expire: 0
  develop:
    mode: true

memberPay:
  key: x2ulFqJhFFFh3el9
ding:
  enable: false
  mobiles: 18980482041,18380807226
#redisson:
#  address: redis://**************:36380
#  password: eIx6TynJq
#  database: 0
#  single:
#    enabled: true
brandGuidFilter: 6564767921501896704
wxStorePay:
  appId: 7b44d9d3-2760-41e7-90d5-d08b93d2d693
  mchntName: 成都掌控者网络科技有限公司
  appSecret: ZPKwzfYZMiRI78/URgcioA==

mqtt:
  host: tcp://emqtt-sit.holderzone.cn:1883
  userName: admin
  passWord: public123

zhuancan:
  host: https://zhuancan-sit.holderzone.cn