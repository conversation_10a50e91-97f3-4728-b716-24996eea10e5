ref:
  eureka:
    hostname: ***************
    port: 8141
  zipkin:
    hostname: ${spring.cloud.client.ip-address}
    port: 9411
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${ref.eureka.hostname}:${ref.eureka.port}/eureka/
spring:
  application:
    name: holder-saas-store-organization
  jackson:
    default-property-inclusion: non_null
  zipkin:
    base-url: http://${ref.zipkin.hostname}:${ref.zipkin.port}/
    service:
      name: holder-saas-store-organization
    sender:
      type: web
    enabled: true
  sleuth:
    sampler:
      probability: 1.0
    enabled: true
  rocketmq:
      namesrv-addr: ***************:9876
      producer-group-name: MerchantOrganizationProducer
      retry-times-when-send-async-failed: 4
      retry-times-when-send-failed: 4
      compress-msg-body-over-how-much: 5120

feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000