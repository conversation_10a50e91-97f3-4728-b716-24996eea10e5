server:
  port: 8923
  undertow:
      max-http-post-size: 0
      io-threads: 4
      worker-threads: 32
      buffer-size: 1024
      direct-buffers: true
swagger:
  enable: true

dynamic:
  server:
    server-code: holder_saas_store_erp
  mybatis:
    enabled: false
    base-package: com.holderzone.erp.dao
    mapper-locations: classpath:mapper/*.xml
  redis:
    enabled: true
redisson:
  host: redis://***************:6379
  database: 14
  password: eIx6TynJq

spring:
  application:
    name: holder-saas-store-erp
#  datasource:
#    druid:
#      validation-query: SELECT 1 from dual
#      driver-class-name: com.mysql.cj.jdbc.Driver
#      url: jdbc:mysql://${spring.datasource.druid.host}:3307/hse_erp_db?autoReconnect=true&failOverReadOnly=false&useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false&serverTimezone=Asia/Shanghai
#      host: **************
#      username: root
#      password: mysqlHolder
#      initial-size: 5
#      max-active: 20
#      max-wait: 60000
#      min-idle: 5
#      #druid监控信息
#      filter:
#       stat:
#        enabled: true
#  redis:
#    database: 8
#    host: ***************
#    port: 6379
#    password: eIx6TynJq
#    jedis:
#      pool:
#        max-active: 50
#        min-idle: 1
#        max-wait: -1ms

eureka:
  client:
    service-url:
     defaultZone: http://***************:8141/eureka
#     defaultZone: http://localhost:8141/eureka
  instance:
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 10
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
feign:
  hystrix:
    enabled: false
  httpclient:
    enabled: true
    connection-timeout: 300000
    max-connections: 500
    time-to-live: 30
ribbon:
  ConnectionTimeout: 100000
  ReadTimeout: 100000
#熔断配置
hystrix:
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      execution:
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 2000
          thread:
            timeoutInMilliseconds: 15000
        timeout:
          enable: true

mybatis:
  mapper-locations: classpath:mapper/*.xml