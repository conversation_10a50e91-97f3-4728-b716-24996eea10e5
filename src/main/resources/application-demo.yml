ref:
  eureka:
    hostname: ***************
    port: 8141
  zipkin:
    hostname: ${spring.cloud.client.ip-address}
    port: 9411
management:
  endpoints:
    web:
      exposure:
        include: "*"
      cors:
        allowed-origins: "*"
        allowed-methods: "*"

spring:
  application:
    name: holder-saas-store-business
  jackson:
    default-property-inclusion: non_null

  zipkin:
    base-url: http://${ref.zipkin.hostname}:${ref.zipkin.port}/
    service:
      name: holder-saas-store-business
    sender:
      type: web
    enabled: true
  sleuth:
    sampler:
      probability: 1.0
    enabled: true
  rocketmq:
    namesrv-addr: ***************:9876
    producer-group-name: MerchantOrganizationProducer
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120

eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${ref.eureka.hostname}:${ref.eureka.port}/eureka/

feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000

file:
  size: 3MB
  total:
    size: 5MB

#自定义配置：用来判断是否执行手动切换数据源的方法
self:
  open-dynamic-datasource: true

# 动态数据源
dynamic:
  intercept:
    datasource:
      include-url: /**
      exclude-url:
    #添加需要切换 redis 数据源的接口路径
    redis:
      include-url: /**
  redis:
    enabled: true
  server:
    #如果本服务需要对表分库，那么需要配置本服务的所有表名,以逗号隔开
    #sharding-tables: person
    server-code: holder_saas_store_business

jhPay:
  pay:
    url: https://pay.holderzone.net/zuul/agg/prepay/barcode
    polling:
      url: https://pay.holderzone.net/zuul/agg/prepay/polling
      times: 120
    query:
      url: https://pay.holderzone.net/zuul/agg/prepay/query
  notify:
    url: https://mch.holderzone.net/gateway/jh/pay/notify
  refund:
    url: https://pay.holderzone.net/zuul/agg/refund/refundReq
    query:
      url: https://pay.holderzone.net/zuul/agg/refund/query
  mchnt:
    query: https://pay.holderzone.net/zuul/agg/mchnt/validate
