<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.TradeEstimateMapper">

    <sql id="fields">
        e.guid AS "guid",
        b.name AS "brandName",
        e.is_forever_estimate AS "estimateType",
        eo.op_type AS "opType",
        eo.store_name AS "storeName",
        eo.item_name AS "itemName",
        eo.op_time AS "opTime",
        eo.operator_name AS "opLogOperatorName",
        rsd.device_type AS "opLogDeviceType"
    </sql>

    <select id="pageInfo" resultType="com.holderzone.saas.store.dto.report.resp.EstimateReportRespDTO">
        SELECT
            DISTINCT e.guid AS "guid",
            e.gmt_create AS "gmtCreate"
        FROM
            "hsi_item_${query.enterpriseGuid}_db"."hsi_estimate" e
        LEFT JOIN
            "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" rsb ON rsb.store_guid = e.store_guid
        LEFT JOIN
            "hso_organization_${query.enterpriseGuid}_db".hso_brand b ON b.guid = rsb.brand_guid
        <include refid="whereClause"/>
        ORDER BY e.gmt_create DESC
        LIMIT ${query.pageSize} OFFSET ${(query.currentPage - 1) * query.pageSize}
    </select>

    <select id="list" resultType="com.holderzone.saas.store.dto.report.resp.EstimateReportRespDTO">
        SELECT
            DISTINCT e.guid AS "guid",
            e.gmt_create AS "gmtCreate"
        FROM
            "hsi_item_${query.enterpriseGuid}_db"."hsi_estimate" e
        LEFT JOIN
            "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" rsb ON rsb.store_guid = e.store_guid
        LEFT JOIN
            "hso_organization_${query.enterpriseGuid}_db".hso_brand b ON b.guid = rsb.brand_guid
        <include refid="whereClause"/>
        ORDER BY e.gmt_create DESC
    </select>

    <select id="count" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT e.guid)
        FROM
            "hsi_item_${query.enterpriseGuid}_db"."hsi_estimate" e
        LEFT JOIN
            "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" rsb ON rsb.store_guid = e.store_guid
        LEFT JOIN
            "hso_organization_${query.enterpriseGuid}_db".hso_brand b ON b.guid = rsb.brand_guid
        <include refid="whereClause"/>
    </select>

    <select id="listEstimate" resultType="com.holderzone.saas.store.dto.report.resp.EstimateReportRespDTO">
        SELECT
            <include refid="fields" />
        FROM
            "hsi_item_${query.enterpriseGuid}_db"."hsi_estimate" e
        LEFT JOIN
            "hsi_item_${query.enterpriseGuid}_db"."hsi_estimate_op_log" eo ON e.guid = eo.op_refid :: VARCHAR
        LEFT JOIN
            "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" rsb ON rsb.store_guid = e.store_guid
        LEFT JOIN
            "hso_organization_${query.enterpriseGuid}_db".hso_brand b ON b.guid = rsb.brand_guid
        LEFT JOIN
            "hso_organization_${query.enterpriseGuid}_db".hso_r_store_device rsd ON eo.device_guid = rsd.device_guid AND eo.store_guid = rsd.store_guid
        WHERE e.guid IN
        <foreach collection="query.guidList" item="guid" open="(" separator="," close=")">
            #{guid}
        </foreach>
    </select>

    <sql id="whereClause">
        <where>
            <if test="query.startTime != null">
                AND e.gmt_create >= CONCAT(#{query.startTime},' 00:00:00')::TIMESTAMP
            </if>
            <if test="query.endTime != null">
                AND e.gmt_create <![CDATA[ <= ]]> CONCAT(#{query.endTime},' 23:59:59')::TIMESTAMP
            </if>
            <if test="query.brandGuid != null and query.brandGuid != ''">
                and b.guid = #{query.brandGuid}
            </if>
            <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                AND e.store_guid IN
                <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.cancelEstimateType != null or (query.itemName != null and query.itemName != '')">
                AND e.guid IN (
                    SELECT DISTINCT
                        op_refid :: VARCHAR
                    FROM
                        "hsi_item_${query.enterpriseGuid}_db"."hsi_estimate_op_log"
                    <where>
                        <if test="query.cancelEstimateType != null">
                            <choose>
                                <when test="query.cancelEstimateType == 2">
                                    AND op_type = 'SCHEDULING'
                                </when>
                                <when test="query.cancelEstimateType == 3">
                                    AND (op_type = 'BATCH_CANCEL' OR op_type = 'REMOVE')
                                </when>
                                <otherwise>
                                    AND op_refid NOT IN (
                                        SELECT DISTINCT
                                            op_refid
                                        FROM
                                            "hsi_item_${query.enterpriseGuid}_db"."hsi_estimate_op_log"
                                        WHERE
                                            op_type = 'SCHEDULING'
                                            OR op_type = 'BATCH_CANCEL'
                                            OR op_type = 'REMOVE'
                                    )
                                </otherwise>
                            </choose>
                        </if>
                        <if test="query.itemName != null and query.itemName != ''">
                            AND item_name LIKE CONCAT('%', #{query.itemName}, '%')
                        </if>
                    </where>
                )
            </if>
        </where>
    </sql>

</mapper>
