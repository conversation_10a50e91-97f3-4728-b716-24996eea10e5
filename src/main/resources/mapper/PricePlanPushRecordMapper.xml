<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.PricePlanPushRecordMapper">

    <select id="getPushRecords"
            resultType="com.holderzone.saas.store.item.entity.domain.PricePlanPushRecordDO">
        SELECT s.brand_guid, s.plan_guid, s.store_guid, i.item_guid
        FROM hsi_price_plan_store s,
             hsi_price_plan_item i
        WHERE s.plan_guid = i.plan_guid
          AND s.plan_guid = #{planGuid}
          AND i.is_delete = 0
    </select>

    <update id="updatePushStatus">
        UPDATE hsi_price_plan_push_record
        SET push_status   = #{reqDTO.status},
            new_item_guid = #{reqDTO.newItemGuid}
        WHERE plan_guid = #{reqDTO.planGuid}
          AND store_guid = #{reqDTO.storeGuid}
          AND item_guid = #{reqDTO.itemGuid}
    </update>

    <delete id="deletePushRecords">
        DELETE FROM hsi_price_plan_push_record
        WHERE plan_guid = #{planGuid}
    </delete>

</mapper>