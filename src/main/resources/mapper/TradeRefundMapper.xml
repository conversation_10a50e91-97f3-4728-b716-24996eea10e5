<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.TradeRefundMapper">


    <!-- 原始统计查询，保留作为备份 -->
    <select id="statisticsOriginal" resultType="com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO">
        <include refid="WITH_SQL"/>
        SELECT
            count(DISTINCT temp.refund_order_guid) as "totalQuantity",
            sum(temp.refund_amount) as "totalMoney"
        FROM (
        <include refid="REFUND_SQL"/>
        UNION ALL
        <include refid="RECOVERY_SQL"/>
        UNION ALL
        <include refid="NEW_RECOVERY_SQL"/>
        ) temp
    </select>

    <!-- 优化的统计查询 -->
    <select id="statistics" resultType="com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO">
        <choose>
            <!-- 如果有明确的时间范围，使用优化查询 -->
            <when test="query.startTime != null and query.endTime != null">
                <include refid="OPTIMIZED_STATISTICS_QUERY"/>
            </when>
            <!-- 否则使用原始查询 -->
            <otherwise>
                <include refid="WITH_SQL"/>
                SELECT
                    count(DISTINCT temp.refund_order_guid) as "totalQuantity",
                    sum(temp.refund_amount) as "totalMoney"
                FROM (
                <include refid="REFUND_SQL"/>
                UNION ALL
                <include refid="RECOVERY_SQL"/>
                UNION ALL
                <include refid="NEW_RECOVERY_SQL"/>
                ) temp
            </otherwise>
        </choose>
    </select>

    <select id="countOriginal" resultType="java.lang.Integer">
        <include refid="WITH_SQL"/>
        SELECT
            count(1)
        FROM (
        <include refid="REFUND_SQL"/>
        UNION ALL
        <include refid="RECOVERY_SQL"/>
        UNION ALL
        <include refid="NEW_RECOVERY_SQL"/>
        ) temp
    </select>

    <select id="count" resultType="java.lang.Integer">
        <choose>
            <!-- 如果有明确的时间范围，使用优化查询 -->
            <when test="query.startTime != null and query.endTime != null">
                <include refid="OPTIMIZED_REFUND_COUNT"/>
            </when>
            <!-- 否则使用原始查询 -->
            <otherwise>
                <include refid="WITH_SQL"/>
                SELECT
                count(1)
                FROM (
                <include refid="REFUND_SQL"/>
                UNION ALL
                <include refid="RECOVERY_SQL"/>
                UNION ALL
                <include refid="NEW_RECOVERY_SQL"/>
                ) temp
            </otherwise>
        </choose>
    </select>

    <!-- 原始版本，保留作为备份 -->
    <select id="pageInfoOriginal" resultType="com.holderzone.saas.store.dto.report.resp.RefundDetailDTO">
        <include refid="WITH_SQL"/>
        SELECT temp.* FROM
        (
        <include refid="REFUND_SQL"/>
        UNION ALL
        <include refid="RECOVERY_SQL"/>
        UNION ALL
        <include refid="NEW_RECOVERY_SQL"/>
        ) temp
        order by temp.refund_time desc
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <!-- 优化版本 - 分离查询减少复杂度 -->
    <select id="pageInfo" resultType="com.holderzone.saas.store.dto.report.resp.RefundDetailDTO">
        <choose>
            <!-- 如果有明确的时间范围，使用优化查询 -->
            <when test="query.startTime != null and query.endTime != null">
                <include refid="OPTIMIZED_REFUND_QUERY"/>
            </when>
            <!-- 否则使用原始查询 -->
            <otherwise>
                <include refid="WITH_SQL"/>
                SELECT temp.* FROM
                (
                <include refid="REFUND_SQL"/>
                UNION ALL
                <include refid="RECOVERY_SQL"/>
                UNION ALL
                <include refid="NEW_RECOVERY_SQL"/>
                ) temp
                order by temp.refund_time desc
                limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
            </otherwise>
        </choose>
    </select>

    <sql id="WITH_SQL">
        with refund_order as(
            SELECT
                o.trade_mode,
                1 as "refund_type",
                o.guid as "refund_order_record_guid",
                o.guid as "refund_order_guid",
                o.original_order_guid:: BIGINT
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
            <where>
                o.is_delete = 0
                AND o.state = 5
                AND o.recovery_type = 4
                AND o.recovery_id != '0'
                AND o.recovery_id is not null
                <if test="query.startTime != null and query.endTime != null">
                    and o.gmt_create BETWEEN CONCAT(#{query.startTime},' 00:00:00')::TIMESTAMP  AND CONCAT(#{query.endTime},' 23:59:59')::TIMESTAMP
                    and o.business_day BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
                </if>
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    and o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    and ( o.order_no like concat('%', #{query.itemName}, '%') or o.member_phone like concat('%', #{query.itemName}, '%') )
                </if>
                <if test="query.cateringType != null">
                    and o.trade_mode = #{query.cateringType}
                </if>
            </where>

            union all

            SELECT
                ro.trade_mode,
                0 as "refund_type",
                orr.guid as "refund_order_record_guid",
                orr.refund_order_guid,
                orr.order_guid as "original_order_guid"
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order_refund_record" orr
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" ro ON orr.refund_order_guid = ro.guid AND ro.is_delete = 0
            <where>
                orr.is_delete = 0
                <if test="query.startTime != null and query.endTime != null">
                    and orr.gmt_create BETWEEN CONCAT(#{query.startTime},' 00:00:00')::TIMESTAMP AND CONCAT(#{query.endTime},' 23:59:59')::TIMESTAMP
                    and ro.business_day BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
                </if>
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    and ro.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    and ( ro.order_no like concat('%', #{query.itemName}, '%') or ro.member_phone like concat('%', #{query.itemName}, '%') )
                </if>
                <if test="query.cateringType != null">
                    and ro.trade_mode = #{query.cateringType}
                </if>
            </where>

            union all

            SELECT
                recovery_o.trade_mode,
                2 as "refund_type",
                ro.guid as "refund_order_record_guid",
                recovery_o.guid as "refund_order_guid",
                recovery_o.original_order_guid:: BIGINT
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" recovery_o
            LEFT join "hst_trade_${query.enterpriseGuid}_db"."hst_order" ro on recovery_o.original_order_guid = ro.original_order_guid and ro.is_delete = 0 and ro.recovery_type = 4
            <where>
                recovery_o.is_delete = 0
                AND recovery_o.state in (4,6)
                AND recovery_o.recovery_type = 3
                <if test="query.startTime != null and query.endTime != null">
                    and recovery_o.gmt_create BETWEEN CONCAT(#{query.startTime},' 00:00:00')::TIMESTAMP AND CONCAT(#{query.endTime},' 23:59:59')::TIMESTAMP
                    and recovery_o.business_day BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
                    and ro.business_day BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
                </if>
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    and recovery_o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    and ( recovery_o.order_no like concat('%', #{query.itemName}, '%') or recovery_o.member_phone like concat('%', #{query.itemName}, '%') )
                </if>
                <if test="query.cateringType != null">
                    and recovery_o.trade_mode = #{query.cateringType}
                </if>
            </where>
        ),
        original_transaction_record as (
            SELECT
                tr.order_guid as "order_guid",
                sum(tr.amount) as "amount"
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
            <where>
                tr.order_guid in (select original_order_guid from refund_order)
                AND tr.state = 4
                AND tr.trade_type = 1
                AND tr.is_delete = 0
                AND tr.payment_type in (20, 61, 62, 13, 65, 70)
                <if test="query.startTime != null and query.endTime != null">
                    and tr.gmt_create between #{query.startTime}::TIMESTAMP + '-1 month' and #{query.endTime}::TIMESTAMP + '1 month'
                </if>
            </where>
            group by tr.order_guid
        ),
        recovery_refund_transaction_record as (
            SELECT
                with_ro.refund_order_guid as "refund_order_guid",
                sum(tr.amount) as "refund_amount",
                string_agg(concat(tr.payment_type_name, '￥', -tr.amount), '、') as "refund_details"
            FROM
                refund_order with_ro
            join "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr on with_ro.refund_order_guid = tr.order_guid and tr.is_delete = 0
            <where>
                tr.state = 4
                AND tr.trade_type = 6
                and tr.payment_type not in (20, 61, 62, 13, 65, 70)
                AND with_ro.refund_type = 1
                <if test="query.startTime != null and query.endTime != null">
                    and tr.gmt_create between #{query.startTime}::TIMESTAMP + '-1 month' and #{query.endTime}::TIMESTAMP + '1 month'
                </if>
            </where>
            group by with_ro.refund_order_guid
        ),
        recovery_checkout_transaction_record as (
            SELECT
                with_ro.refund_order_guid as "order_guid",
                sum(tr.amount) as "amount"
            FROM
                refund_order with_ro
            join "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr on with_ro.refund_order_guid = tr.order_guid and tr.is_delete = 0
            <where>
                tr.amount > 0
                AND tr.payment_type in (20, 61, 62, 13, 65, 70)
                AND tr.state = 4
                AND with_ro.refund_type = 2
                <if test="query.startTime != null and query.endTime != null">
                    and tr.gmt_create between #{query.startTime}::TIMESTAMP + '-1 month' and #{query.endTime}::TIMESTAMP + '1 month'
                </if>
            </where>
            group by with_ro.refund_order_guid
        ),
        recovery_refund_checkout_transaction_record as (
            SELECT
                with_ro.refund_order_guid as "refund_order_guid",
                sum(tr.amount) as "refund_amount",
                string_agg(concat(tr.payment_type_name, '￥', ABS(tr.amount)), '、') as "refund_details"
            FROM
                refund_order with_ro
            join "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr on with_ro.refund_order_guid = tr.order_guid and tr.is_delete = 0
            <where>
                <![CDATA[ tr.amount < 0 ]]>
                AND tr.state = 4
                AND with_ro.refund_type = 2
                <if test="query.startTime != null and query.endTime != null">
                    and tr.gmt_create between #{query.startTime}::TIMESTAMP + '-1 month' and #{query.endTime}::TIMESTAMP + '1 month'
                </if>
            </where>
            group by with_ro.refund_order_guid
        ),
        recovery_refund_cancel_transaction_record as (
            SELECT
                with_ro.refund_order_record_guid as "refund_order_guid",
                sum(tr.amount) as "refund_amount",
                string_agg(concat(tr.payment_type_name, '￥', ABS(tr.amount)), '、') as "refund_details"
            FROM
                refund_order with_ro
            join "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr on with_ro.refund_order_record_guid = tr.order_guid and tr.is_delete = 0
            <where>
                <![CDATA[ tr.amount < 0 ]]>
                AND tr.state = 4
                AND tr.payment_type in (20, 61, 62, 13, 65, 70)
                AND with_ro.refund_type = 2
                <if test="query.startTime != null and query.endTime != null">
                    and tr.gmt_create between #{query.startTime}::TIMESTAMP + '-1 month' and #{query.endTime}::TIMESTAMP + '1 month'
                </if>
            </where>
            group by with_ro.refund_order_record_guid
        )
    </sql>

    <!-- 优化的退款查询 - 减少CTE复杂度 -->
    <sql id="OPTIMIZED_REFUND_COUNT">
        WITH refund_data AS (
            SELECT
                orr.guid as refund_order_guid,
                o.store_name,
                o.checkout_time,
                orr.gmt_create as refund_time,
                ro.order_no,
                o.trade_mode,
                0 AS type,
                o.dining_table_name,
                o.actually_pay_fee as actually_pay_fee,
                orr.refund_amount,
                orr.refund_reason,
                orr.create_staff_name,
                orr.auth_staff_name,
                orr.refund_details,
                o.member_phone
            FROM "hst_trade_${query.enterpriseGuid}_db"."hst_order_refund_record" orr
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON orr.order_guid = o.guid
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" ro ON orr.refund_order_guid = ro.guid
            WHERE orr.is_delete = 0
                AND o.is_delete = 0
                AND ro.is_delete = 0
                AND orr.gmt_create BETWEEN CONCAT(#{query.startTime},' 00:00:00')::TIMESTAMP AND CONCAT(#{query.endTime},' 23:59:59')::TIMESTAMP
                <if test="query.storeGuids != null and query.storeGuids.size()>0">
                    AND ro.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    AND (ro.order_no LIKE CONCAT('%', #{query.itemName}, '%') OR o.member_phone LIKE CONCAT('%', #{query.itemName}, '%'))
                </if>
                <if test="query.cateringType != null">
                    AND ro.trade_mode = #{query.cateringType}
                </if>

            UNION ALL

            SELECT
                ro.guid as refund_order_guid,
                ro.store_name,
                o.checkout_time,
                ro.gmt_create as refund_time,
                ro.order_no,
                ro.trade_mode,
                1 AS type,
                ro.dining_table_name,
                o.actually_pay_fee,
                COALESCE(ABS(tr.refund_amount), 0) as refund_amount,
                ro.recovery_reason,
                ro.recovery_staff_name,
                '' as auth_staff_name,
                COALESCE(tr.refund_details, '') as refund_details,
                o.member_phone
            FROM "hst_trade_${query.enterpriseGuid}_db"."hst_order" ro
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON ro.original_order_guid :: BIGINT = o.guid
            LEFT JOIN (
                SELECT
                    tr.order_guid,
                    SUM(tr.amount) as refund_amount,
                    STRING_AGG(CONCAT(tr.payment_type_name, '￥', -tr.amount), '、') as refund_details
                FROM "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                WHERE tr.state = 4 AND tr.trade_type = 6 AND tr.is_delete = 0
                    AND tr.gmt_create BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
                GROUP BY tr.order_guid
            ) tr ON tr.order_guid = ro.guid
            WHERE ro.is_delete = 0
                AND o.is_delete = 0
                AND ro.state = 5
                AND ro.recovery_type = 4
                AND ro.recovery_id != '0'
                AND ro.recovery_id IS NOT NULL
                AND ro.gmt_create BETWEEN CONCAT(#{query.startTime},' 00:00:00')::TIMESTAMP AND CONCAT(#{query.endTime},' 23:59:59')::TIMESTAMP
                <if test="query.storeGuids != null and query.storeGuids.size()>0">
                    AND ro.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    AND (ro.order_no LIKE CONCAT('%', #{query.itemName}, '%') OR o.member_phone LIKE CONCAT('%', #{query.itemName}, '%'))
                </if>
                <if test="query.cateringType != null">
                    AND ro.trade_mode = #{query.cateringType}
                </if>
        )
        SELECT COUNT(1) FROM refund_data
    </sql>

    <!-- 优化的退款查询 - 减少CTE复杂度 -->
    <sql id="OPTIMIZED_REFUND_QUERY">
        WITH refund_data AS (
            SELECT
                orr.guid as refund_order_guid,
                o.store_name,
                o.checkout_time,
                orr.gmt_create as refund_time,
                ro.order_no,
                o.trade_mode,
                0 AS type,
                o.dining_table_name,
                o.actually_pay_fee as actually_pay_fee,
                orr.refund_amount,
                orr.refund_reason,
                orr.create_staff_name,
                orr.auth_staff_name,
                orr.refund_details,
                o.member_phone
            FROM "hst_trade_${query.enterpriseGuid}_db"."hst_order_refund_record" orr
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON orr.order_guid = o.guid
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" ro ON orr.refund_order_guid = ro.guid
            WHERE orr.is_delete = 0
                AND o.is_delete = 0
                AND ro.is_delete = 0
                AND orr.gmt_create BETWEEN CONCAT(#{query.startTime},' 00:00:00')::TIMESTAMP AND CONCAT(#{query.endTime},' 23:59:59')::TIMESTAMP
                <if test="query.storeGuids != null and query.storeGuids.size()>0">
                    AND ro.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    AND (ro.order_no LIKE CONCAT('%', #{query.itemName}, '%') OR o.member_phone LIKE CONCAT('%', #{query.itemName}, '%'))
                </if>
                <if test="query.cateringType != null">
                    AND ro.trade_mode = #{query.cateringType}
                </if>

            UNION ALL

            SELECT
                ro.guid as refund_order_guid,
                ro.store_name,
                o.checkout_time,
                ro.gmt_create as refund_time,
                ro.order_no,
                ro.trade_mode,
                1 AS type,
                ro.dining_table_name,
                o.actually_pay_fee,
                COALESCE(ABS(tr.refund_amount), 0) as refund_amount,
                ro.recovery_reason,
                ro.recovery_staff_name,
                '' as auth_staff_name,
                COALESCE(tr.refund_details, '') as refund_details,
                o.member_phone
            FROM "hst_trade_${query.enterpriseGuid}_db"."hst_order" ro
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON ro.original_order_guid :: BIGINT = o.guid
            LEFT JOIN (
                SELECT
                    tr.order_guid,
                    SUM(tr.amount) as refund_amount,
                    STRING_AGG(CONCAT(tr.payment_type_name, '￥', -tr.amount), '、') as refund_details
                FROM "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                WHERE tr.state = 4 AND tr.trade_type = 6 AND tr.is_delete = 0
                    AND tr.gmt_create BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
                GROUP BY tr.order_guid
            ) tr ON tr.order_guid = ro.guid
            WHERE ro.is_delete = 0
                AND o.is_delete = 0
                AND ro.state = 5
                AND ro.recovery_type = 4
                AND ro.recovery_id != '0'
                AND ro.recovery_id IS NOT NULL
                AND ro.gmt_create BETWEEN CONCAT(#{query.startTime},' 00:00:00')::TIMESTAMP AND CONCAT(#{query.endTime},' 23:59:59')::TIMESTAMP
                <if test="query.storeGuids != null and query.storeGuids.size()>0">
                    AND ro.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    AND (ro.order_no LIKE CONCAT('%', #{query.itemName}, '%') OR o.member_phone LIKE CONCAT('%', #{query.itemName}, '%'))
                </if>
                <if test="query.cateringType != null">
                    AND ro.trade_mode = #{query.cateringType}
                </if>
        )
        SELECT * FROM refund_data
        ORDER BY refund_time DESC
        LIMIT ${query.pageSize} OFFSET ${(query.currentPage - 1) * query.pageSize}
    </sql>

    <!-- 优化的统计查询 - 避免复杂UNION，分别统计后合并 -->
    <sql id="OPTIMIZED_STATISTICS_QUERY">
        WITH refund_data AS (
            SELECT
                orr.guid as refund_order_guid,
                o.store_name,
                o.checkout_time,
                orr.gmt_create as refund_time,
                ro.order_no,
                o.trade_mode,
                0 AS type,
                o.dining_table_name,
                o.actually_pay_fee as actually_pay_fee,
                orr.refund_amount,
                orr.refund_reason,
                orr.create_staff_name,
                orr.auth_staff_name,
                orr.refund_details,
                o.member_phone
            FROM "hst_trade_${query.enterpriseGuid}_db"."hst_order_refund_record" orr
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON orr.order_guid = o.guid
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" ro ON orr.refund_order_guid = ro.guid
            WHERE orr.is_delete = 0
                AND o.is_delete = 0
                AND ro.is_delete = 0
                AND orr.gmt_create BETWEEN CONCAT(#{query.startTime},' 00:00:00')::TIMESTAMP AND CONCAT(#{query.endTime},' 23:59:59')::TIMESTAMP
                <if test="query.storeGuids != null and query.storeGuids.size()>0">
                    AND ro.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    AND (ro.order_no LIKE CONCAT('%', #{query.itemName}, '%') OR o.member_phone LIKE CONCAT('%', #{query.itemName}, '%'))
                </if>
                <if test="query.cateringType != null">
                    AND ro.trade_mode = #{query.cateringType}
                </if>

            UNION ALL

            SELECT
                ro.guid as refund_order_guid,
                ro.store_name,
                o.checkout_time,
                ro.gmt_create as refund_time,
                ro.order_no,
                ro.trade_mode,
                1 AS type,
                ro.dining_table_name,
                o.actually_pay_fee,
                COALESCE(ABS(tr.refund_amount), 0) as refund_amount,
                ro.recovery_reason,
                ro.recovery_staff_name,
                '' as auth_staff_name,
                COALESCE(tr.refund_details, '') as refund_details,
                o.member_phone
            FROM "hst_trade_${query.enterpriseGuid}_db"."hst_order" ro
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON ro.original_order_guid :: BIGINT = o.guid
            LEFT JOIN (
                SELECT
                    tr.order_guid,
                    SUM(tr.amount) as refund_amount,
                    STRING_AGG(CONCAT(tr.payment_type_name, '￥', -tr.amount), '、') as refund_details
                FROM "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                WHERE tr.state = 4 AND tr.trade_type = 6 AND tr.is_delete = 0
                    AND tr.gmt_create BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
                GROUP BY tr.order_guid
            ) tr ON tr.order_guid = ro.guid
            WHERE ro.is_delete = 0
                AND o.is_delete = 0
                AND ro.state = 5
                AND ro.recovery_type = 4
                AND ro.recovery_id != '0'
                AND ro.recovery_id IS NOT NULL
                AND ro.gmt_create BETWEEN CONCAT(#{query.startTime},' 00:00:00')::TIMESTAMP AND CONCAT(#{query.endTime},' 23:59:59')::TIMESTAMP
                <if test="query.storeGuids != null and query.storeGuids.size()>0">
                    AND ro.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    AND (ro.order_no LIKE CONCAT('%', #{query.itemName}, '%') OR o.member_phone LIKE CONCAT('%', #{query.itemName}, '%'))
                </if>
                <if test="query.cateringType != null">
                    AND ro.trade_mode = #{query.cateringType}
                </if>
        )
        SELECT
            COUNT(DISTINCT refund_data.refund_order_guid) AS "totalQuantity",
            SUM(refund_data.refund_amount) AS "totalMoney"
        FROM refund_data
    </sql>


    <sql id="REFUND_SQL">
        SELECT
            orr.guid as "refund_order_guid",
            o.store_name,
            o.checkout_time,
            orr.gmt_create as "refund_time",
            ro.order_no,
            o.trade_mode,
            0 AS "type",
            o.dining_table_name,
            o.actually_pay_fee + COALESCE(otr.amount, 0) as "actually_pay_fee",
            orr.refund_amount as "refund_amount",
            orr.refund_reason,
            orr.create_staff_name,
            orr.auth_staff_name,
            orr.refund_details,
            o.member_phone
        FROM
            refund_order with_ro
        JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_refund_record" orr on orr.guid = with_ro.refund_order_record_guid
        JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON orr.order_guid = o.guid AND o.is_delete = 0
        JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" ro ON orr.refund_order_guid = ro.guid AND ro.is_delete = 0
        LEFT JOIN original_transaction_record otr on otr.order_guid = o.guid
        <where>
            with_ro.refund_type = 0
            <if test="query.startTime != null and query.endTime != null">
                and o.business_day BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
                and ro.business_day BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
            </if>
        </where>
    </sql>

    <sql id="RECOVERY_SQL">
        SELECT
            ro.guid as "refund_order_guid",
            ro.store_name,
            o.checkout_time,
            ro.gmt_create as "refund_time",
            ro.order_no,
            ro.trade_mode,
            1 AS "type",
            ro.dining_table_name,
            o.actually_pay_fee + COALESCE(otr.amount, 0) as "actually_pay_fee",
            ABS(rtr.refund_amount) as "refund_amount",
            ro.recovery_reason,
            ro.recovery_staff_name,
            oe.recovery_auth_staff_name as "auth_staff_name",
            rtr.refund_details,
            o.member_phone
        FROM
            refund_order with_ro
        JOIN recovery_refund_transaction_record rtr on rtr.refund_order_guid = with_ro.refund_order_guid
        JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" ro on with_ro.refund_order_guid = ro.guid and ro.is_delete = 0
        JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o on with_ro.original_order_guid = o.guid and o.is_delete = 0
        LEFT JOIN original_transaction_record otr on otr.order_guid = with_ro.original_order_guid
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_extends" oe on oe.guid = ro.guid and oe.is_delete = 0
        <where>
            with_ro.refund_type = 1
            <if test="query.startTime != null and query.endTime != null">
                and o.business_day BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
                and ro.business_day BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
            </if>
        </where>
    </sql>

    <sql id="NEW_RECOVERY_SQL">
        SELECT
            recovery_o.guid as "refund_order_guid",
            recovery_o.store_name,
            recovery_o.checkout_time,
            case when recovery_o.state = 4 then recovery_o.checkout_time else recovery_o.cancel_time end as "refund_time",
            recovery_o.order_no,
            recovery_o.trade_mode,
            1 AS "type",
            recovery_o.dining_table_name,
            case when recovery_o.state = 4 then recovery_o.actually_pay_fee + COALESCE(rctr.amount,0) else 0 end as "actually_pay_fee",
            case when recovery_o.state = 4 then ABS(COALESCE(rrctc.refund_amount,0)) else ABS(COALESCE(rrctr.refund_amount,0)) end as "refund_amount",
            case when recovery_o.state = 4 then '反结账差额退款' when recovery_o.state = 6 then '作废' else '' end as "recovery_reason",
            case when recovery_o.state = 4 then recovery_o.checkout_staff_name else recovery_o.cancel_staff_name end as "recovery_staff_name",
            '' as "auth_staff_name",
            case when recovery_o.state = 4 then rrctc.refund_details else rrctr.refund_details end as "refund_details",
            recovery_o.member_phone
        FROM
            refund_order with_ro
        JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" recovery_o on recovery_o.guid = with_ro.refund_order_guid and recovery_o.is_delete = 0
        LEFT JOIN recovery_checkout_transaction_record rctr on rctr.order_guid = recovery_o.guid
        LEFT JOIN recovery_refund_checkout_transaction_record rrctc on rrctc.refund_order_guid = recovery_o.guid
        LEFT JOIN recovery_refund_cancel_transaction_record rrctr on rrctr.refund_order_guid = with_ro.refund_order_record_guid
        <where>
            with_ro.refund_type = 2 and ( (recovery_o.state = 4 and ABS(rrctc.refund_amount) > 0) or (recovery_o.state = 6 and ABS(rrctr.refund_amount) > 0) )
            <if test="query.startTime != null and query.endTime != null">
                and recovery_o.business_day BETWEEN #{query.startTime}::TIMESTAMP + '-1 month' AND #{query.endTime}::TIMESTAMP + '1 month'
            </if>
        </where>
    </sql>
</mapper>
