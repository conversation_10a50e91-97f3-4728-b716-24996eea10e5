<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.OrderRefundRecordMapper">


    <select id="listByRefundOrderGuid"
            resultType="com.holderzone.saas.store.dto.order.response.dinein.RefundOrderRecordDTO">
        select
            *
        from
            hst_order_refund_record
        where
            refund_order_guid = #{refundOrderGuid} and is_delete = 0
        order by gmt_create desc, guid asc
    </select>

    <select id="handoverRefund"  parameterType="com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO"
            resultType="java.math.BigDecimal">
        SELECT
        SUM(IFNULL(tr.refund_amount, 0)) refund_amount
        FROM
            `hst_order` t
            LEFT JOIN `hst_order_refund_record` tr ON tr.order_guid = t.guid
        <where>
            t.is_delete = 0
            AND t.state = 4
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                and t.store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                and t.checkout_time &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                and t.checkout_time &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and tr.create_staff_guid = #{dto.userGuid}
            </if>
        </where>
    </select>


</mapper>
