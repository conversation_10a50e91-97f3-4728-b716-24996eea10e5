<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.queue.mapper.QueueMapper">
    <select id="selectQueueSizeByStoreGuid" resultType="com.holderzone.saas.store.business.queue.domain.QueueSizeDO">
        select queue_guid,count(*) as `size`
        from hsq_queue_item
        where status = 0
        and is_deleted = 0
        and is_enable = 1
        and store_guid = #{storeGuid}
        group by queue_guid
    </select>

    <select id="selectCountByStoreGuid" resultType="java.lang.Integer">
        select count(*)
        from hsq_queue
        where
         store_guid = #{storeGuid}
    </select>
</mapper>