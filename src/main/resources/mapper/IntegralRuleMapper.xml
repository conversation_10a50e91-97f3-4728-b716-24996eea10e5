<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.member.mapper.IntegralRuleMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.saas.store.member.domain.IntegralRuleDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="integral_rule_guid" jdbcType="VARCHAR" property="integralRuleGuid" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="is_open" jdbcType="TINYINT" property="isOpen" />
    <result column="allow_balance" jdbcType="TINYINT" property="allowBalance" />
    <result column="member_grade_guid" jdbcType="VARCHAR" property="memberGradeGuid" />
    <result column="get_type" jdbcType="INTEGER" property="getType" />
    <result column="consume_type" jdbcType="INTEGER" property="consumeType" />
    <result column="get_integral_unit" jdbcType="INTEGER" property="getIntegralUnit" />
    <result column="get_fee_unit" jdbcType="DECIMAL" property="getFeeUnit" />
    <result column="consume_integral_unit" jdbcType="INTEGER" property="consumeIntegralUnit" />
    <result column="consume_integral_max" jdbcType="INTEGER" property="consumeIntegralMax" />
    <result column="consume_fee_unit" jdbcType="DECIMAL" property="consumeFeeUnit" />
    <result column="consume_fee_min" jdbcType="DECIMAL" property="consumeFeeMin" />
  </resultMap>
  <sql id="Base_Column_List">
    id, gmt_create, gmt_modified, is_delete, integral_rule_guid, `type`, is_open, allow_balance, member_grade_guid,
    get_type, consume_type, get_integral_unit, get_fee_unit, consume_integral_unit, consume_integral_max, 
    consume_fee_unit, consume_fee_min
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hsm_integral_rule
    where integral_rule_guid = #{integralRuleGuid,jdbcType=VARCHAR}
  </select>
    <select id="selectByGradeGuid" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from hsm_integral_rule
      where member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR}
    </select>
  <select id="selectByGetTypeAndGradeGuid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hsm_integral_rule
    where member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR}
    and get_type = #{code}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from hsm_integral_rule
    where iintegral_rule_guid = #{integralRuleGuid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.holderzone.saas.store.member.domain.IntegralRuleDO">
    insert into hsm_integral_rule (id, gmt_create, gmt_modified, 
      is_delete, integral_rule_guid, `type`, 
      is_open, allow_balance, member_grade_guid, get_type,
      consume_type, get_integral_unit, get_fee_unit, 
      consume_integral_unit, consume_integral_max, 
      consume_fee_unit, consume_fee_min)
    values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=BIT}, #{integralRuleGuid,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT}, 
      #{isOpen,jdbcType=TINYINT}, #{allowBalance,jdbcType=TINYINT}, #{memberGradeGuid,jdbcType=VARCHAR}, #{getType,jdbcType=INTEGER},
      #{consumeType,jdbcType=INTEGER}, #{getIntegralUnit,jdbcType=INTEGER}, #{getFeeUnit,jdbcType=DECIMAL}, 
      #{consumeIntegralUnit,jdbcType=INTEGER}, #{consumeIntegralMax,jdbcType=INTEGER}, 
      #{consumeFeeUnit,jdbcType=DECIMAL}, #{consumeFeeMin,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.holderzone.saas.store.member.domain.IntegralRuleDO">
    insert into hsm_integral_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="integralRuleGuid != null">
        integral_rule_guid,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="isOpen != null">
        is_open,
      </if>
      <if test="allowBalance != null">
        allow_balance,
      </if>
      <if test="memberGradeGuid != null">
        member_grade_guid,
      </if>
      <if test="getType != null">
        get_type,
      </if>
      <if test="consumeType != null">
        consume_type,
      </if>
      <if test="getIntegralUnit != null">
        get_integral_unit,
      </if>
      <if test="getFeeUnit != null">
        get_fee_unit,
      </if>
      <if test="consumeIntegralUnit != null">
        consume_integral_unit,
      </if>
      <if test="consumeIntegralMax != null">
        consume_integral_max,
      </if>
      <if test="consumeFeeUnit != null">
        consume_fee_unit,
      </if>
      <if test="consumeFeeMin != null">
        consume_fee_min,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="integralRuleGuid != null">
        #{integralRuleGuid,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="isOpen != null">
        #{isOpen,jdbcType=TINYINT},
      </if>
      <if test="allowBalance != null">
        #{allowBalance,jdbcType=TINYINT},
      </if>
      <if test="memberGradeGuid != null">
        #{memberGradeGuid,jdbcType=VARCHAR},
      </if>
      <if test="getType != null">
        #{getType,jdbcType=INTEGER},
      </if>
      <if test="consumeType != null">
        #{consumeType,jdbcType=INTEGER},
      </if>
      <if test="getIntegralUnit != null">
        #{getIntegralUnit,jdbcType=INTEGER},
      </if>
      <if test="getFeeUnit != null">
        #{getFeeUnit,jdbcType=DECIMAL},
      </if>
      <if test="consumeIntegralUnit != null">
        #{consumeIntegralUnit,jdbcType=INTEGER},
      </if>
      <if test="consumeIntegralMax != null">
        #{consumeIntegralMax,jdbcType=INTEGER},
      </if>
      <if test="consumeFeeUnit != null">
        #{consumeFeeUnit,jdbcType=DECIMAL},
      </if>
      <if test="consumeFeeMin != null">
        #{consumeFeeMin,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.holderzone.saas.store.member.domain.IntegralRuleDO">
    update hsm_integral_rule
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>

      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="isOpen != null">
        is_open = #{isOpen,jdbcType=TINYINT},
      </if>
      <if test="allowBalance != null">
        allow_balance = #{allowBalance,jdbcType=TINYINT},
      </if>
      <if test="memberGradeGuid != null">
        member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR},
      </if>
      <if test="getType != null">
        get_type = #{getType,jdbcType=INTEGER},
      </if>
      <if test="consumeType != null">
        consume_type = #{consumeType,jdbcType=INTEGER},
      </if>
      <if test="getIntegralUnit != null">
        get_integral_unit = #{getIntegralUnit,jdbcType=INTEGER},
      </if>
      <if test="getFeeUnit != null">
        get_fee_unit = #{getFeeUnit,jdbcType=DECIMAL},
      </if>
      <if test="consumeIntegralUnit != null">
        consume_integral_unit = #{consumeIntegralUnit,jdbcType=INTEGER},
      </if>
      <if test="consumeIntegralMax != null">
        consume_integral_max = #{consumeIntegralMax,jdbcType=INTEGER},
      </if>
      <if test="consumeFeeUnit != null">
        consume_fee_unit = #{consumeFeeUnit,jdbcType=DECIMAL},
      </if>
      <if test="consumeFeeMin != null">
        consume_fee_min = #{consumeFeeMin,jdbcType=DECIMAL},
      </if>
    </set>
    where integral_rule_guid = #{integralRuleGuid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.holderzone.saas.store.member.domain.IntegralRuleDO">
    update hsm_integral_rule
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      integral_rule_guid = #{integralRuleGuid,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=TINYINT},
      is_open = #{isOpen,jdbcType=TINYINT},
      allow_balance = #{allowBalance,jdbcType=TINYINT},
      member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR},
      get_type = #{getType,jdbcType=INTEGER},
      consume_type = #{consumeType,jdbcType=INTEGER},
      get_integral_unit = #{getIntegralUnit,jdbcType=INTEGER},
      get_fee_unit = #{getFeeUnit,jdbcType=DECIMAL},
      consume_integral_unit = #{consumeIntegralUnit,jdbcType=INTEGER},
      consume_integral_max = #{consumeIntegralMax,jdbcType=INTEGER},
      consume_fee_unit = #{consumeFeeUnit,jdbcType=DECIMAL},
      consume_fee_min = #{consumeFeeMin,jdbcType=DECIMAL}
    where integral_rule_guid = #{integralRuleGuid,jdbcType=VARCHAR}
  </update>

  <insert id="insertDefault" parameterType="com.holderzone.saas.store.member.domain.IntegralRuleDO">
    insert into hsm_integral_rule
    (gmt_create,
     integral_rule_guid,
     is_open,
     get_type,
     type,
     consume_type,
     allow_balance,
     member_grade_guid,
     consume_integral_unit,
     consume_integral_max,
     consume_fee_unit,
     consume_fee_min )
     values (
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{integralRuleGuid,jdbcType=VARCHAR},
      #{isOpen,jdbcType=TINYINT},
      #{getType,jdbcType=INTEGER},
      #{type,jdbcType=TINYINT},
      #{consumeType,jdbcType=INTEGER},
      #{allowBalance,jdbcType=TINYINT},
      #{memberGradeGuid,jdbcType=VARCHAR},
      #{consumeIntegralUnit,jdbcType=INTEGER},
      #{consumeIntegralMax,jdbcType=INTEGER},
      #{consumeFeeUnit,jdbcType=DECIMAL},
      #{consumeFeeMin,jdbcType=DECIMAL})

  </insert>


  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="rules" item="item" index="index" open="" close="" separator=";">
    update hsm_integral_rule
    <set>
      <if test="item.gmtCreate != null">
        gmt_create = #{item.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="item.gmtModified != null">
        gmt_modified = #{item.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="item.isDelete != null">
        is_delete = #{item.isDelete,jdbcType=BIT},
      </if>

      <if test="item.type != null">
        `type` = #{item.type,jdbcType=TINYINT},
      </if>
      <if test="item.isOpen != null">
        is_open = #{item.isOpen,jdbcType=TINYINT},
      </if>
      <if test="item.allowBalance != null">
        allow_balance = #{item.allowBalance,jdbcType=TINYINT},
      </if>
      <if test="item.memberGradeGuid != null">
        member_grade_guid = #{item.memberGradeGuid,jdbcType=VARCHAR},
      </if>
      <if test="item.getType != null">
        get_type = #{item.getType,jdbcType=INTEGER},
      </if>
      <if test="item.consumeType != null">
        consume_type = #{item.consumeType,jdbcType=INTEGER},
      </if>
      <if test="item.getIntegralUnit != null">
        get_integral_unit = #{item.getIntegralUnit,jdbcType=INTEGER},
      </if>
      <if test="item.getFeeUnit != null">
        get_fee_unit = #{item.getFeeUnit,jdbcType=DECIMAL},
      </if>
      <if test="item.consumeIntegralUnit != null">
        consume_integral_unit = #{item.consumeIntegralUnit,jdbcType=INTEGER},
      </if>
      <if test="item.consumeIntegralMax != null">
        consume_integral_max = #{item.consumeIntegralMax,jdbcType=INTEGER},
      </if>
      <if test="item.consumeFeeUnit != null">
        consume_fee_unit = #{item.consumeFeeUnit,jdbcType=DECIMAL},
      </if>
      <if test="item.consumeFeeMin != null">
        consume_fee_min = #{item.consumeFeeMin,jdbcType=DECIMAL},
      </if>
    </set>
      where integral_rule_guid = #{item.integralRuleGuid,jdbcType=VARCHAR}
    </foreach>

  </update>


  <insert id="insertSelectiveBatch" parameterType="com.holderzone.saas.store.member.domain.IntegralRuleDO">
   <foreach collection="list" index="index" item="item" separator=";">
    insert into hsm_integral_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="item.id != null">
        id,
      </if>
      <if test="item.gmtCreate != null">
        gmt_create,
      </if>
      <if test="item.gmtModified != null">
        gmt_modified,
      </if>
      <if test="item.isDelete != null">
        is_delete,
      </if>
      <if test="item.integralRuleGuid != null">
        integral_rule_guid,
      </if>
      <if test="item.type != null">
        `type`,
      </if>
      <if test="item.isOpen != null">
        is_open,
      </if>
      <if test="item.allowBalance != null">
        allow_balance,
      </if>
      <if test="item.memberGradeGuid != null">
        member_grade_guid,
      </if>
      <if test="item.getType != null">
        get_type,
      </if>
      <if test="item.consumeType != null">
        consume_type,
      </if>
      <if test="item.getIntegralUnit != null">
        get_integral_unit,
      </if>
      <if test="item.getFeeUnit != null">
        get_fee_unit,
      </if>
      <if test="item.consumeIntegralUnit != null">
        consume_integral_unit,
      </if>
      <if test="item.consumeIntegralMax != null">
        consume_integral_max,
      </if>
      <if test="item.consumeFeeUnit != null">
        consume_fee_unit,
      </if>
      <if test="item.consumeFeeMin != null">
        consume_fee_min,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="item.id != null">
        #{item.id,jdbcType=BIGINT},
      </if>
      <if test="item.gmtCreate != null">
        #{item.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="item.gmtModified != null">
        #{item.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="item.isDelete != null">
        #{item.isDelete,jdbcType=BIT},
      </if>
      <if test="item.integralRuleGuid != null">
        #{item.integralRuleGuid,jdbcType=VARCHAR},
      </if>
      <if test="item.type != null">
        #{item.type,jdbcType=TINYINT},
      </if>
      <if test="item.isOpen != null">
        #{item.isOpen,jdbcType=TINYINT},
      </if>
      <if test="item.allowBalance != null">
        #{item.allowBalance,jdbcType=TINYINT},
      </if>
      <if test="item.memberGradeGuid != null">
        #{item.memberGradeGuid,jdbcType=VARCHAR},
      </if>
      <if test="item.getType != null">
        #{item.getType,jdbcType=INTEGER},
      </if>
      <if test="item.consumeType != null">
        #{item.consumeType,jdbcType=INTEGER},
      </if>
      <if test="item.getIntegralUnit != null">
        #{item.getIntegralUnit,jdbcType=INTEGER},
      </if>
      <if test="item.getFeeUnit != null">
        #{item.getFeeUnit,jdbcType=DECIMAL},
      </if>
      <if test="item.consumeIntegralUnit != null">
        #{item.consumeIntegralUnit,jdbcType=INTEGER},
      </if>
      <if test="item.consumeIntegralMax != null">
        #{item.consumeIntegralMax,jdbcType=INTEGER},
      </if>
      <if test="item.consumeFeeUnit != null">
        #{item.consumeFeeUnit,jdbcType=DECIMAL},
      </if>
      <if test="item.consumeFeeMin != null">
        #{item.consumeFeeMin,jdbcType=DECIMAL},
      </if>
    </trim>
   </foreach>
  </insert>
  <select id="selectConsumeRule" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hsm_integral_rule
    where member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR} and type=1

  </select>








  <select id="selectMemberConsumeInfoByGuid" parameterType="java.lang.String" resultMap="MemberConsumeBaseInfo">
    SELECT
       t1.is_open,
       t1.consume_integral_unit,
       t1.consume_integral_max,
       t1.consume_fee_unit,
       t1.consume_fee_min,
       t2.discount
     From hsm_integral_rule t1
     LEFT JOIN
     hsm_member_grade t2
     ON t1.member_grade_guid=t2.member_grade_guid
     where t1.member_grade_guid=#{memberGradeGuid,jdbcType=VARCHAR} and t1.type=1

  </select>

  <resultMap id="MemberConsumeBaseInfo" type="com.holderzone.saas.store.member.domain.MemberConsumeRuleReadDO">
    <result column="consume_integral_unit" jdbcType="INTEGER" property="consumeIntegralUnit" />
    <result column="consume_integral_max" jdbcType="INTEGER" property="consumeIntegralMax" />
    <result column="consume_fee_unit" jdbcType="DECIMAL" property="consumeFeeUnit" />
    <result column="consume_fee_min" jdbcType="DECIMAL" property="consumeFeeMin" />
    <result column="discount"  jdbcType="DECIMAL" property="memberDiscount"></result>
    <result column="is_open" jdbcType="TINYINT" property="isOpen" />
  </resultMap>

</mapper>