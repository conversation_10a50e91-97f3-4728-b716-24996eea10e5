<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.ReasonMapper">


    <update id="updateCount">
        UPDATE hsb_reason_reason SET reason_count = reason_count+1 WHERE reason_guid IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="findReason" resultType="com.holderzone.saas.store.dto.business.reason.ReasonDTO">
        select
        reason_guid,
        store_guid,
        reason,
        reason_type_code
        from hsb_reason_reason
        where
        store_guid=#{storeGuid}
        <if test="reasonTypeCode!=null">
            and
            reason_type_code=#{reasonTypeCode}
        </if>
        order by reason_create desc;
    </select>
</mapper>
