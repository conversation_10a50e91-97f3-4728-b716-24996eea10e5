<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holder.saas.print.mapper.PrintTypeTemplateRStoreMapper">


    <select id="queryRepeatTemplateCount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            `hsp_print_type_template_r_store` s
        LEFT JOIN hsp_print_type_template t on t.guid = s.template_guid and t.is_delete = 0
        <where>
            s.is_delete = 0
            <if test="brandGuid != null and brandGuid != ''">
                and s.brand_guid = #{brandGuid}
            </if>
            <if test="storeGuidList != null and storeGuidList.size()>0">
                and s.store_guid in
                <foreach collection="storeGuidList" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="guid != null and guid != ''">
                and t.guid != #{guid}
            </if>
            <if test="invoiceTypeList != null and invoiceTypeList.size()>0">
                and
                <foreach collection="invoiceTypeList" item="invoiceType" open="(" separator="or" close=")">
                    concat(',',t.invoice_type,',') like concat('%,',#{invoiceType},',%')
                </foreach>
            </if>
        </where>
    </select>


    <select id="queryTemplateStoreGuidList"
            resultType="com.holder.saas.print.entity.domain.type.PrintTypeTemplateRStoreDO">
        SELECT
            *
        FROM
            `hsp_print_type_template_r_store` s
        LEFT JOIN hsp_print_type_template t on t.guid = s.template_guid and t.is_delete = 0
        <where>
            s.is_delete = 0
            <if test="brandGuid != null and brandGuid != ''">
                and s.brand_guid = #{brandGuid}
            </if>
            <if test="templateGuid != null and templateGuid != ''">
                and t.guid != #{templateGuid}
            </if>
            <if test="invoiceTypeList != null and invoiceTypeList.size()>0">
                and
                <foreach collection="invoiceTypeList" item="invoiceType" open="(" separator="or" close=")">
                    concat(',',t.invoice_type,',') like concat('%,',#{invoiceType},',%')
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryTemplateByInvoiceTypeAndStoreGuid"
            resultType="com.holder.saas.print.entity.domain.type.PrintTypeTemplateRStoreDO">
        SELECT
            *
        FROM
            `hsp_print_type_template_r_store` s
        LEFT JOIN hsp_print_type_template t on t.guid = s.template_guid and t.is_delete = 0
        <where>
            s.is_delete = 0 and t.is_enable = 1
            <if test="storeGuid != null and storeGuid != ''">
                and s.store_guid = #{storeGuid}
            </if>
            <if test="invoiceType != null and invoiceType != ''">
                and concat(',',t.invoice_type,',') like concat('%,',#{invoiceType},',%')
            </if>
        </where>
        order by t.gmt_modified desc
        limit 1
    </select>
</mapper>