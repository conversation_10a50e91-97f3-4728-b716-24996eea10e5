<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.kds.mapper.KitchenAssociatedOrderMapper">


    <insert id="create">
        insert into hsk_kitchen_associated_order (`guid`, `order_guid`, `associated_flag`, `associated_sn`,
        `associated_table_guids`, `associated_table_names`)
        values ( #{guid}, #{orderGuid}, #{associatedFlag}, #{associatedSn}, #{associatedTableGuids}, #{associatedTableNames} )
        on duplicate key update
        associated_flag = values(associated_flag),
        associated_sn = values(associated_sn),
        associated_table_guids = values(associated_table_guids),
        associated_table_names = values(associated_table_names),
        gmt_modified = now()
    </insert>
</mapper>
