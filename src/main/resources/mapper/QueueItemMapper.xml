<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.queue.mapper.QueueItemMapper">
    <select id="getMaxOrder" resultType="String">
        select ifnull(max(`code`),'0')
        from hsq_queue_item
        where queue_guid = #{queueGuid}
        and is_deleted = 0
    </select>

    <select id="getPreCount" resultType="int">
        select count(*)
        from hsq_queue_item
        where queue_guid = #{queueGuid}
        and is_deleted = 0
        and status in  (0,2)
        and sort &lt; #{order}
    </select>
</mapper>
