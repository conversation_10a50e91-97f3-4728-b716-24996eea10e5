<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.kds.mapper.QueueItemMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.saas.store.kds.entity.domain.QueueItemDO">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="store_guid" jdbcType="VARCHAR" property="storeGuid" />
    <result column="device_guid" jdbcType="VARCHAR" property="deviceGuid" />
    <result column="order_guid" jdbcType="VARCHAR" property="orderGuid" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_mode" jdbcType="INTEGER" property="orderMode" />
    <result column="order_desc" jdbcType="VARCHAR" property="orderDesc" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="in_time" jdbcType="TIMESTAMP" property="inTime" />
    <result column="dst_time" jdbcType="TIMESTAMP" property="dstTime" />
    <result column="dst_items" jdbcType="LONGVARCHAR" property="dstItems" />
    <result column="dst_expire_time" jdbcType="TIMESTAMP" property="dstExpireTime" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, guid, store_guid, device_guid, order_guid, order_no, order_mode, order_desc, `status`, in_time, dst_time,
    dst_items, dst_expire_time, gmt_create, gmt_modified
  </sql>

  <select id="queryOriQueueItemsOfOrder" resultMap="BaseResultMap"
          parameterType="com.holderzone.saas.store.kds.entity.query.QueueItemQuery">
    select
    <trim suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    from hsk_queue_item
    use index(idx_order_status)
    where store_guid = #{storeGuid}
    and order_guid in
    <foreach collection="orderGuidList" item="orderGuid" separator="," open="(" close=")">
        #{orderGuid}
    </foreach>
  </select>

</mapper>