<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.DebtUnitMapper">

    <select id="nameExists" resultType="java.lang.Boolean">
        SELECT COUNT(1)
        FROM hst_debt_unit
        WHERE name = #{req.name}
          AND is_deleted = 0
    </select>

    <select id="codeExists" resultType="java.lang.Boolean">
        SELECT COUNT(1)
        FROM hst_debt_unit
        WHERE code = #{req.code}
          AND is_deleted = 0
    </select>

    <select id="telExists" resultType="java.lang.Boolean">
        SELECT COUNT(1)
        FROM hst_debt_unit
        WHERE contact_tel = #{req.contactTel}
          AND is_deleted = 0
    </select>

    <select id="unitPage" resultType="com.holderzone.saas.store.dto.trade.DebtUnitPageRespDTO">
        SELECT guid unitGuid, code, name, credit_limit, credit_limit_left, remark,
               password, contact_name, contact_tel, address
        FROM hst_debt_unit
        WHERE is_deleted = 0
        <if test="req.unitName != null and req.unitName != ''">
            AND name LIKE CONCAT('%', #{req.unitName}, '%')
        </if>
    </select>

    <select id="unitDropdownList"
            resultType="com.holderzone.saas.store.dto.trade.DebtUnitDropdownListDTO">
        SELECT guid, code, name
        FROM hst_debt_unit
        WHERE is_deleted = 0
    </select>

    <update id="creditLimitChange">
        UPDATE hst_debt_unit SET credit_limit_left =
        <choose>
            <when test="changeDTO.changeType == 1">
                credit_limit_left + #{changeDTO.creditChange}
            </when>
            <when test="changeDTO.changeType == 2">
                credit_limit_left - #{changeDTO.creditChange}
            </when>
            <otherwise>
                credit_limit_left
            </otherwise>
        </choose>
        WHERE guid = #{changeDTO.unitGuid}
    </update>

</mapper>