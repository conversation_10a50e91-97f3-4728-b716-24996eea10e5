<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.member.mapper.MemberGradeMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.saas.store.member.domain.MemberGradeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="member_grade_guid" jdbcType="VARCHAR" property="memberGradeGuid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="need_integral" jdbcType="INTEGER" property="needIntegral" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="fee_type" jdbcType="TINYINT" property="feeType" />
    <result column="is_default" jdbcType="TINYINT" property="isDefault" />
    <result column="minimum_prepaid_fee" jdbcType="DECIMAL" property="minimumPrepaidFee" />
    <result column="prepaid_rule" jdbcType="TINYINT" property="prepaidRule" />
    <result column="prepaid_limit" jdbcType="VARCHAR" property="prepaidLimit" />
    <result column="style" jdbcType="TINYINT" property="style" />
    <result column="color" jdbcType="INTEGER" property="color" />
    <result column="color_rgba" jdbcType="VARCHAR" property="colorRGBA"></result>
    <result column="picture" jdbcType="VARCHAR" property="picture" />

  </resultMap>
  <sql id="Base_Column_List">
    id, gmt_create, gmt_modified, is_delete, member_grade_guid, `name`, need_integral, 
    discount, fee_type, is_default, minimum_prepaid_fee, prepaid_rule, prepaid_limit, `style`, color,color_rgba,
    picture
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hsm_member_grade
    where member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR}
  </select>
    <select id="getNameById" resultType="java.lang.String">
      select
      name
      from hsm_member_grade
      where member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR}
    </select>
  <select id="selectDefult" resultType="java.lang.String">
    select
      member_grade_guid
      from hsm_member_grade
      where is_default = 1
     order by gmt_create desc
  </select>
  <select id="getByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hsm_member_grade
    where `name` = #{name,jdbcType=VARCHAR}
    and is_delete = 0
  </select>
  <select id="getByMemberGradeGuid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
      from hsm_member_grade
      where member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR}
  </select>
  <select id="selectAllMemberGrade" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hsm_member_grade
    where is_delete = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from hsm_member_grade
   where member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR}
  </delete>
    <update id="deleteMemberGrade">
      update hsm_member_grade
      set
      is_delete = 1
    where member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR}
    </update>
    <insert id="insert" parameterType="com.holderzone.saas.store.member.domain.MemberGradeDO">
    insert into hsm_member_grade (id, gmt_create, gmt_modified, 
      is_delete, member_grade_guid, `name`, 
      need_integral, discount, fee_type, is_default,
      minimum_prepaid_fee, prepaid_rule, prepaid_limit, 
      `style`, color,color_rgba picture
      )
    values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=BIT}, #{memberGradeGuid,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{needIntegral,jdbcType=INTEGER}, #{discount,jdbcType=DECIMAL}, #{feeType,jdbcType=TINYINT}, #{isDefault,jdbcType=TINYINT},
      #{minimumPrepaidFee,jdbcType=DECIMAL}, #{prepaidRule,jdbcType=TINYINT}, #{prepaidLimit,jdbcType=VARCHAR}, 
      #{style,jdbcType=TINYINT}, #{color,jdbcType=INTEGER}, #{colorRGBA,jdbcType=VARCHAR},#{picture,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.holderzone.saas.store.member.domain.MemberGradeDO">
    insert into hsm_member_grade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="memberGradeGuid != null">
        member_grade_guid,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="needIntegral != null">
        need_integral,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="feeType != null">
        fee_type,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
      <if test="minimumPrepaidFee != null">
        minimum_prepaid_fee,
      </if>
      <if test="prepaidRule != null">
        prepaid_rule,
      </if>
      <if test="prepaidLimit != null">
        prepaid_limit,
      </if>
      <if test="style != null">
        `style`,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="colorRGBA != null and colorRGBA != ''">
        color_rgba,
      </if>
      <if test="picture != null">
        picture,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="memberGradeGuid != null">
        #{memberGradeGuid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="needIntegral != null">
        #{needIntegral,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="feeType != null">
        #{feeType,jdbcType=TINYINT},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="minimumPrepaidFee != null">
        #{minimumPrepaidFee,jdbcType=DECIMAL},
      </if>
      <if test="prepaidRule != null">
        #{prepaidRule,jdbcType=TINYINT},
      </if>
      <if test="prepaidLimit != null">
        #{prepaidLimit,jdbcType=VARCHAR},
      </if>
      <if test="style != null">
        #{style,jdbcType=TINYINT},
      </if>
      <if test="color != null">
        #{color,jdbcType=INTEGER},
      </if>
      <if test="colorRGBA != null and colorRGBA!=''">
        #{colorRGBA,jdbcType=VARCHAR},
      </if>
      <if test="picture != null">
        #{picture,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.holderzone.saas.store.member.domain.MemberGradeDO">
    update hsm_member_grade
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="memberGradeGuid != null">
        member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="needIntegral != null">
        need_integral = #{needIntegral,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=DECIMAL},
      </if>
      <if test="feeType != null">
        fee_type = #{feeType,jdbcType=TINYINT},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="minimumPrepaidFee != null">
        minimum_prepaid_fee = #{minimumPrepaidFee,jdbcType=DECIMAL},
      </if>
      <if test="prepaidRule != null">
        prepaid_rule = #{prepaidRule,jdbcType=TINYINT},
      </if>
      <if test="prepaidLimit != null">
        prepaid_limit = #{prepaidLimit,jdbcType=VARCHAR},
      </if>
      <if test="style != null">
        `style` = #{style,jdbcType=TINYINT},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=INTEGER},
      </if>

      <if test="colorRGBA != null and colorRGBA != ''">
        color_rgba = #{colorRGBA,jdbcType=VARCHAR},
      </if>
      <if test="picture != null">
        picture = #{picture,jdbcType=VARCHAR},
      </if>
    </set>
    where member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.holderzone.saas.store.member.domain.MemberGradeDO">
    update hsm_member_grade
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      need_integral = #{needIntegral,jdbcType=INTEGER},
      discount = #{discount,jdbcType=DECIMAL},
      fee_type = #{feeType,jdbcType=TINYINT},
      is_default = #{isDefault,jdbcType=TINYINT},
      minimum_prepaid_fee = #{minimumPrepaidFee,jdbcType=DECIMAL},
      prepaid_rule = #{prepaidRule,jdbcType=TINYINT},
      prepaid_limit = #{prepaidLimit,jdbcType=VARCHAR},
      `style` = #{style,jdbcType=TINYINT},
      color = #{color,jdbcType=INTEGER},
      color_rgba=#{colorRGBA,jdbcType=VARCHAR},
      picture = #{picture,jdbcType=VARCHAR}
    where member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR}
  </update>


  <resultMap id="MemberGradeBase" type="com.holderzone.saas.store.member.domain.MemberGradeListReadDO">

    <result column="member_grade_guid" property="memberGradeGuid"></result>
    <result column="name" property="name"></result>

  </resultMap>

  <select id="getAllMemberGrade" resultMap="MemberGradeBase">
    SELECT
    member_grade_guid,
    name
    from hsm_member_grade
    where is_delete=0
  </select>
</mapper>