<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.ItemTMenuMapper">

    <resultMap id="ItemTemplatesQuery" type="com.holderzone.saas.store.item.entity.query.ItemTemplatesQuery">
        <result column="periodic_mode"  property="periodicMode"/>
        <result column="guid"  property="guid"/>
        <result column="menu_guid"  property="menuGuid"/>
        <result column="weeks"  property="weeks"/>
        <result column="times"  property="times"/>
        <result column="is_delete"  property="isDelete"/>

    </resultMap>

    <select id="getItemTemplateMenus" resultMap="ItemTemplatesQuery">
        SELECT
            t.periodic_mode,
            t.guid ,
            m.guid menu_guid,
            v.`weeks`,
            v.times,
            m.is_delete
        FROM
            hsi_item_template t
        INNER JOIN hsi_item_t_menu m ON t.guid = m.template_guid
        INNER JOIN hsi_itme_t_menu_validity v ON m.guid = v.item_menu_guid
        WHERE
            t.guid = #{dto.data} AND t.is_delete = 0 AND m.is_delete = 0 AND v.is_delete = 0
        ORDER BY m.id  DESC
    </select>
</mapper>
