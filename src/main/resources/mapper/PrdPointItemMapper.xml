<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.kds.mapper.PrdPointItemMapper">

    <select id="queryAll" resultType="com.holderzone.saas.store.dto.kds.resp.PrdPointItemDTO">
        SELECT store_guid,
               device_id,
               point_guid,
               item_guid,
               sku_guid,
               sku_code
        FROM hsk_point_item
    </select>
</mapper>
