<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.InOutDocumentDetailMapper">

    <resultMap id="inAndReturnResultMap" type="com.holderzone.erp.entity.domain.InOutDocumentDetailDO">
        <result column="guid" property="guid"/>
        <result column="material_guid" property="materialGuid" />
        <result column="count" property="count" />
        <result column="return_count" property="returnCount" />
    </resultMap>

    <resultMap id="documentDetailMap" type="com.holderzone.erp.entity.domain.InOutDocumentDetailDO">
        <result column="guid" property="guid" />
        <result column="document_guid" property="documentGuid" />
        <result column="material_guid" property="materialGuid" />
        <result column="material_code" property="materialCode" />
        <result column="material_name" property="materialName" />
        <result column="unit_guid" property="unitGuid" />
        <result column="unit_name" property="unitName" />
        <result column="unit_price" property="unitPrice" />
        <result column="count" property="count" />
        <result column="total_amount" property="totalAmount" />
        <result column="return_count" property="returnCount" />
        <result column="warehouse_guid" property="warehouseGuid" />
    </resultMap>

    <resultMap id="flowDetailMap" type="com.holderzone.erp.entity.domain.InOutDocumentFlowDetailDO">
        <result column="guid" property="guid" />
        <result column="material_guid" property="materialGuid" />
        <result column="material_code" property="materialCode" />
        <result column="material_name" property="materialName" />
        <result column="main_unit_count" property="count" />
        <result column="main_unit_guid" property="unitGuid" />
        <result column="main_unit_name" property="unitName" />
        <result column="document_guid" property="documentGuid" />
        <result column="type" property="type" />
        <result column="in_out_type" property="inOutType" />
        <result column="document_date" property="documentDate" />
    </resultMap>

    <insert id="insertInOutDocumentDetailList" parameterType="java.util.List">
        insert into hse_warehouse_in_out_document_detail
        (guid,document_guid,material_guid,material_code,material_name,stock,`count`,
        unit_guid,unit_name,main_unit_guid,main_unit_name,main_unit_count,unit_price,total_amount)
        values
        <foreach collection="list" item="detail" separator=",">
            (#{detail.guid},#{detail.documentGuid},#{detail.materialGuid},#{detail.materialCode},#{detail.materialName},
            #{detail.stock},#{detail.count},#{detail.unitGuid},#{detail.unitName},#{detail.mainUnitGuid},#{detail.mainUnitName},
            #{detail.mainUnitCount},#{detail.unitPrice},#{detail.totalAmount})
        </foreach>
    </insert>

    <update id="updateInDocumentDetailReturnCount">
        <foreach collection="detailList" item="detail" separator=";">
            UPDATE hse_warehouse_in_out_document_detail
            SET return_count = return_count + #{detail.count}
            WHERE document_guid = #{contactDocumentGuid} AND material_guid = #{detail.materialGuid}
        </foreach>
    </update>

    <select id="selectDocumentInAndReturnCount" resultMap = "inAndReturnResultMap" parameterType="string">
        SELECT guid,material_guid,`count`,return_count
        FROM hse_warehouse_in_out_document_detail
        WHERE document_guid = #{contactDocumentGuid}
    </select>

    <select id="selectInOutDocumentDetailList" parameterType="string"
            resultMap = "documentDetailMap">
        SELECT guid,document_guid,material_guid,material_code,material_name,
        unit_guid,unit_name,`count`,unit_price,total_amount,return_count
        FROM hse_warehouse_in_out_document_detail
        WHERE document_guid = #{documentGuid}
    </select>

    <select id="selectInDocumentMaterialCount" parameterType="string" resultMap="documentDetailMap">
        SELECT detail.guid, detail.material_guid, detail.unit_guid,detail.count,document.warehouse_guid
        FROM hse_warehouse_in_out_document_detail detail
        INNER JOIN hse_warehouse_in_out_document document ON document.guid = detail.document_guid
        WHERE document.in_out_type = 0  AND document_guid = #{documentGuid}
    </select>

    <select id="selectOutDocumentMaterialCount" resultMap="documentDetailMap">
        SELECT detail.guid, detail.material_guid,detail.material_name, detail.unit_guid,detail.count,document.warehouse_guid
        FROM hse_warehouse_in_out_document_detail detail
        INNER JOIN hse_warehouse_in_out_document document ON document.guid = detail.document_guid
        WHERE document.in_out_type = 1 AND document_guid = #{documentGuid}
    </select>
    
    <sql id="flowDetailCriteria" >
        WHERE
        doc.document_date BETWEEN #{startDate} AND #{endDate} and doc.status = 1
        AND doc.warehouse_guid IN
        <foreach collection="warehouseGuidList" item="warehouseGuid" open="(" close=")" separator=",">
            #{warehouseGuid}
        </foreach>
        <if test="type != null">
            AND `type` = #{type}
        </if>
        <if test="searchContent != null and searchContent != ''">
            AND (detail.material_name LIKE #{searchContent}"%" OR detail.material_code LIKE #{searchContent}"%")
        </if>
    </sql>

    <select id="selectInOutDocumentFlowDetailList" parameterType="com.holderzone.erp.entity.domain.InOutDocumentFlowDetailQuery"
            resultMap = "flowDetailMap" >
        SELECT detail.guid,detail.material_guid,detail.material_code,detail.material_name,detail.main_unit_count,
        detail.main_unit_guid,detail.main_unit_name,detail.document_guid,doc.type,doc.document_date,doc.in_out_type
        FROM
        hse_warehouse_in_out_document_detail detail INNER JOIN hse_warehouse_in_out_document doc
        ON detail.document_guid = doc.guid
        <include refid="flowDetailCriteria"/>
        ORDER BY doc.gmt_modified DESC LIMIT #{offset},#{pageSize}
    </select>
    <select id="selectInOutDocumentFlowDetailCount" resultType="java.lang.Long" parameterType="com.holderzone.erp.entity.domain.InOutDocumentFlowDetailQuery">
        SELECT count(*)
        FROM hse_warehouse_in_out_document_detail detail INNER JOIN hse_warehouse_in_out_document doc
        ON detail.document_guid = doc.guid
        <include refid="flowDetailCriteria"/>
    </select>
    <select id="selectInOutDocumentCountByMaterial" resultType="java.lang.Integer" parameterType="string">
        SELECT count(*)
        FROM hse_warehouse_in_out_document_detail
        WHERE material_guid = #{materialGuid}
    </select>

</mapper>