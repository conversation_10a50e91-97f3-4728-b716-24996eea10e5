<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.SkuMapper">
    <update id="updateMonthlySaleInc">
        update hsi_sku
        set monthly_sale=monthly_sale + #{monthlySale}
        where guid = #{guid}
          and monthly_sale is not null
    </update>
    <update id="initMonthlySale">
        update
        hsi_sku
        set
        monthly_sale=0
        WHERE
        guid in
        <foreach item="skuGuid" collection="list" index="index" open="(" separator="," close=")">
            #{skuGuid}
        </foreach>
        and monthly_sale is null
    </update>
    <update id="updateStockStatus">
        update
            hsi_sku
        set stock=#{stock}
                ,
            is_open_stock=#{isOpenStock}
        WHERE guid = #{guid}
    </update>

    <update id="updateOriginPriceStoreGuid">
        UPDATE hsi_sku
        SET sale_price=#{originalSalePrice},
            member_price=#{originalMemberPrice}
        WHERE guid = #{skuGuid}
          AND item_guid = #{itemGuid}
          AND store_guid = #{storeGuid};
    </update>

    <update id="deleteSkuByItemGuidAndStoreGuid">
        DELETE
        FROM hsi_sku
        WHERE item_guid = #{itemGuid}
          AND store_guid = #{storeGuid}
          AND sku_from = 2;
    </update>

    <update id="updateTerminalStatus" parameterType="com.holderzone.saas.store.item.dto.UpdateTerminalStatusDTO">
        UPDATE hsi_sku
        SET is_join_aio              = #{updateTerminalStatusDTO.isJoinAio},
            is_join_pos=#{updateTerminalStatusDTO.isJoinPos},
            is_join_pad=#{updateTerminalStatusDTO.isJoinPad},
            is_join_mini_app_mall=#{updateTerminalStatusDTO.isJoinMiniAppMall},
            is_join_mini_app_takeaway=#{updateTerminalStatusDTO.isJoinMiniAppTakeaway},
            is_join_store=#{updateTerminalStatusDTO.isJoinStore}
        WHERE parent_guid = #{updateTerminalStatusDTO.parentGuid}
          AND store_guid = #{updateTerminalStatusDTO.storeGuid}
          AND sku_from = 2;
    </update>

    <select id="selectSearchSkuByItemGuid" resultType="com.holderzone.saas.store.item.entity.domain.SkuDO">
        select item_guid,
        min(sale_price) as sale_price ,
        min(member_price) as member_price,
        min(virtual_price) as virtual_price,
        max(is_rack) as is_rack
        FROM hsi_sku
        WHERE
        item_guid in
        <foreach item="itemGuid" collection="list" index="index" open="(" separator="," close=")">
            #{itemGuid}
        </foreach>
        and is_delete = 0
        group by item_guid
        ;

    </select>
    <select id="querySkuByStoreGuidAndSkuGuidWithIsDelete"
            resultType="com.holderzone.saas.store.dto.item.resp.ItemSkuSearchRespDTO">

        select t3.*,t4.name as type_name from (
        select
        t1.guid as sku_guid,
        t1.name as sku_name,
        t2.name as item_name,
        t1.sale_price,
        t1.unit,
        t1.item_guid,
        t2.type_guid ,
        t2.picture_url ,
        t1.is_delete
        FROM
        (
        select * from hsi_sku where store_guid=#{storeGuid}
        <if test='skuGuidList!=null and skuGuidList.size()>0'>
            and
            guid in
            <foreach item="skuGuid" collection="skuGuidList" index="index" open="(" separator="," close=")">
                #{skuGuid}
            </foreach>
        </if>
        ) t1 left join
        hsi_item t2
        on t1.item_guid=t2.guid)
        t3 left join hsi_type t4
        on t3.type_guid=t4.guid;
    </select>

    <select id="getSkuListByItemList" resultType="com.holderzone.saas.store.item.entity.domain.SkuDO">
        SELECT guid, item_guid, sale_price, member_price FROM hsi_sku
        WHERE item_guid IN
        <foreach collection="list" item="itemGuid" open="(" close=")" separator=",">
            #{itemGuid}
        </foreach>
    </select>

    <select id="skuCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM hsi_sku
        WHERE item_guid = #{itemGuid}
    </select>

    <select id="getMaxGuid" resultType="java.lang.String">
        SELECT max(guid)
        FROM hsi_sku
        WHERE item_guid = #{itemGuid} and guid like concat(#{tempItemGuid},'%')
    </select>

    <select id="selectAllSkuGuidsByPartSkuGuids" resultType="java.lang.String">
        SELECT sku2.guid
        FROM hsi_sku sku1 inner join hsi_sku sku2 on sku1.item_guid = sku2.item_guid
        where sku1.guid in
        <foreach collection="skuGuids" item="skuGuid" open="(" separator="," close=")">
            #{skuGuid}
        </foreach>
    </select>

    <select id="findByItemGuid" resultType="com.holderzone.saas.store.item.entity.domain.SkuDO">
        SELECT *
        FROM hsi_sku
        WHERE item_guid = #{itemGuid}
    </select>

    <select id="findParentItemSkus" resultType="java.lang.String">
        SELECT CONCAT(it.parent_guid, '_', sku.parent_guid )
        FROM hsi_sku sku LEFT JOIN hsi_item it ON sku.item_guid = it.guid
        WHERE sku.sku_from = 2 AND sku.guid in
        <foreach collection="skuGuids" item="skuGuid" open="(" separator="," close=")">
            #{skuGuid}
        </foreach>
    </select>

    <select id="listBrandItemSkuByRecipe" resultType="com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO">
        SELECT
            s.guid AS skuGuid,
            s.item_guid AS itemGuid,
            IF (ISNULL( s.`name` ) OR '' = s.`name`,i.`name`, CONCAT( i.`name`, '（', s.`name`, '）' )) AS name,
            i.type_guid AS typeGuid,
            s.code,
            i.item_type
        FROM
            `hsi_sku` AS s
            LEFT JOIN `hsi_item` AS i ON i.guid = s.item_guid
            LEFT JOIN `hsi_price_plan_item` AS ppi ON s.guid = ppi.sku_guid
            LEFT JOIN `hsi_price_plan` AS pp ON pp.guid = ppi.plan_guid
        WHERE
            i.is_delete = 0
            AND i.is_enable = 1
            AND i.item_from = 1
            AND s.is_rack = 1
            AND s.is_delete = 0
            AND s.is_enable = 1
            AND s.sku_from = 1
            AND s.brand_guid = #{dto.data}
            AND pp.is_delete = 0
            AND ( pp.`status` = 1 OR pp.`status` = 4 )
        <if test="dto.keywords !=null and dto.keywords !=''">
            AND ( i.`name` LIKE CONCAT('%',#{dto.keywords},'%')
         	      OR s.guid LIKE CONCAT('%',#{dto.keywords},'%')
         	      OR ppi.plan_item_name LIKE CONCAT('%',#{dto.keywords},'%') )
        </if>
        GROUP BY s.guid
    </select>

    <select id="listBrandItemSkuByNormal" resultType="com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO">
        SELECT
            s.guid AS skuGuid,
            s.item_guid AS itemGuid,
            IF (ISNULL( s.`name` ) OR '' = s.`name`,i.`name`, CONCAT( i.`name`, '（', s.`name`, '）' )) AS name,
            i.type_guid AS typeGuid,
            s.code,
            i.item_type
        FROM
            `hsi_sku` AS s
            LEFT JOIN `hsi_item` AS i ON i.guid = s.item_guid
        WHERE
            i.is_delete = 0
            AND i.is_enable = 1
            AND s.is_rack = 1
            AND s.is_delete = 0
            AND s.is_enable = 1
        <choose>
            <when test="dto.singleFlag != null and dto.singleFlag">
                AND s.brand_guid is null
            </when>
            <otherwise>
                AND i.item_from = 1
                AND s.sku_from = 1
                AND s.brand_guid = #{dto.data}
            </otherwise>
        </choose>
        <if test="dto.keywords !=null and dto.keywords !=''">
            AND ( i.`name` LIKE CONCAT('%',#{dto.keywords},'%')
            OR s.guid LIKE CONCAT('%',#{dto.keywords},'%') )
        </if>
        GROUP BY s.guid
    </select>

    <select id="listPkgInfoByItemGuid" resultType="com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO">
        SELECT
            hs.item_guid AS parentGuid,
            hskk.item_guid AS itemGuid,
            hskk.item_num AS itemNum,
            hskk.sku_guid AS skuGuid,
            hi.`name` AS itemName,
            his.unit,
            his.`name` AS skuName,
            his.CODE,
            his.sale_price AS salePrice,
            his.accounting_price AS accountingPrice,
            his.takeaway_accounting_price AS takeawayAccountingPrice
        FROM
            hsi_subgroup hs
                JOIN hsi_r_sku_subgroup hskk ON hs.guid = hskk.subgroup_guid
                LEFT JOIN hsi_sku his ON hskk.sku_guid = his.guid
                LEFT JOIN hsi_item hi ON hskk.item_guid = hi.guid
        WHERE
            his.is_delete = 0
          AND hs.is_delete = 0
          AND hskk.is_delete = 0
          AND his.is_enable = 1
          AND his.is_rack = 1
          AND hi.is_delete = 0
          AND hi.is_enable = 1
          AND hs.subgroup_from = 1
          AND hs.item_guid in
        <foreach collection="itemGuids" item="itemGuid" open="(" separator="," close=")">
            #{itemGuid}
        </foreach>
    </select>

</mapper>
