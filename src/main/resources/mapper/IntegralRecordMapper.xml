<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.member.mapper.IntegralRecordMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.saas.store.member.domain.IntegralRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="integral_record_guid" jdbcType="VARCHAR" property="integralRecordGuid" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="transaction_time" jdbcType="TIMESTAMP" property="transactionTime" />
    <result column="business_day" jdbcType="DATE" property="businessDay" />
    <result column="residual_integral" jdbcType="INTEGER" property="residualIntegral" />
    <result column="transaction_integral" jdbcType="INTEGER" property="transactionIntegral" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="member_guid" jdbcType="VARCHAR" property="memberGuid" />
    <result column="staff_guid" jdbcType="VARCHAR" property="staffGuid" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, gmt_create, gmt_modified, is_delete, integral_record_guid, order_no, transaction_time,
    business_day,
    residual_integral, transaction_integral, `type`, account_number, member_guid, staff_guid, 
    staff_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hsm_integral_record
    where integral_record_guid = #{integralRecordGuid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from hsm_integral_record
   where integral_record_guid = #{integralRecordGuid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.holderzone.saas.store.member.domain.IntegralRecordDO">
    insert into hsm_integral_record (id, gmt_create, gmt_modified, 
      is_delete, integral_record_guid, order_no, 
      transaction_time, business_day,residual_integral, transaction_integral,
      `type`, account_number, member_guid, 
      staff_guid, staff_name)
    values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=BIT}, #{integralRecordGuid,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{transactionTime,jdbcType=TIMESTAMP}, #{businessDay,jdbcType=DATE},#{residualIntegral,jdbcType=INTEGER}, #{transactionIntegral,jdbcType=INTEGER},
      #{type,jdbcType=INTEGER}, #{accountNumber,jdbcType=VARCHAR}, #{memberGuid,jdbcType=VARCHAR}, 
      #{staffGuid,jdbcType=VARCHAR}, #{staffName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.holderzone.saas.store.member.domain.IntegralRecordDO">
    insert into hsm_integral_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="integralRecordGuid != null">
        integral_record_guid,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="transactionTime != null">
        transaction_time,
      </if>
      <if test="businessDay != null">
        business_day,
      </if>
      <if test="residualIntegral != null">
        residual_integral,
      </if>
      <if test="transactionIntegral != null">
        transaction_integral,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="accountNumber != null">
        account_number,
      </if>
      <if test="memberGuid != null">
        member_guid,
      </if>
      <if test="staffGuid != null">
        staff_guid,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="integralRecordGuid != null">
        #{integralRecordGuid,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionTime != null">
        #{transactionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessDay != null">
        #{businessDay,jdbcType=TIMESTAMP},
      </if>
      <if test="residualIntegral != null">
        #{residualIntegral,jdbcType=INTEGER},
      </if>
      <if test="transactionIntegral != null">
        #{transactionIntegral,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="accountNumber != null">
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="memberGuid != null">
        #{memberGuid,jdbcType=VARCHAR},
      </if>
      <if test="staffGuid != null">
        #{staffGuid,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.holderzone.saas.store.member.domain.IntegralRecordDO">
    update hsm_integral_record
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="integralRecordGuid != null">
        integral_record_guid = #{integralRecordGuid,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionTime != null">
        transaction_time = #{transactionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessDay != null">
        business_day = #{businessDay,jdbcType=TIMESTAMP},
      </if>
      <if test="residualIntegral != null">
        residual_integral = #{residualIntegral,jdbcType=INTEGER},
      </if>
      <if test="transactionIntegral != null">
        transaction_integral = #{transactionIntegral,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="memberGuid != null">
        member_guid = #{memberGuid,jdbcType=VARCHAR},
      </if>
      <if test="staffGuid != null">
        staff_guid = #{staffGuid,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
    </set>
    where integral_record_guid = #{integralRecordGuid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.holderzone.saas.store.member.domain.IntegralRecordDO">
    update hsm_integral_record
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      integral_record_guid = #{integralRecordGuid,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      transaction_time = #{transactionTime,jdbcType=TIMESTAMP},
      business_day = #{businessDay,jdbcType=TIMESTAMP},
      residual_integral = #{residualIntegral,jdbcType=INTEGER},
      transaction_integral = #{transactionIntegral,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      account_number = #{accountNumber,jdbcType=VARCHAR},
      member_guid = #{memberGuid,jdbcType=VARCHAR},
      staff_guid = #{staffGuid,jdbcType=VARCHAR},
      staff_name = #{staffName,jdbcType=VARCHAR}
    where integral_record_guid = #{integralRecordGuid,jdbcType=VARCHAR}
  </update>



  <sql id="Base_Integral_Column_List">
    id, gmt_create, gmt_modified,  integral_record_guid, order_no, business_day,transaction_time,
    residual_integral, transaction_integral, `type`, account_number, member_guid, staff_guid,
    staff_name
  </sql>


  <select id="selectMemberIntegral" resultMap="MemberIntegralRecords">
    select
    <include refid="Base_Integral_Column_List" />
    from hsm_integral_record
    where member_guid = #{memberIntegralReqDTO.memberGuid} and is_delete=0 and transaction_integral!=0
    <if test="memberIntegralReqDTO.dateType==0 and memberIntegralReqDTO.timeBegin!=null">
      and business_day &gt;=DATE_FORMAT(#{memberIntegralReqDTO.timeBegin},'%Y-%m-%d')
    </if>
    <if test="memberIntegralReqDTO.dateType==0 and memberIntegralReqDTO.timeEnd!=null">
      and business_day &lt;=DATE_FORMAT(#{memberIntegralReqDTO.timeEnd},'%Y-%m-%d')
    </if>

    <if test="memberIntegralReqDTO.dateType==1 and memberIntegralReqDTO.timeBegin!=null">
      and transaction_time &gt;=#{memberIntegralReqDTO.timeBegin}
    </if>
    <if test="memberIntegralReqDTO.dateType==1 and memberIntegralReqDTO.timeEnd!=null">
      and transaction_time &lt;=#{memberIntegralReqDTO.timeEnd}
    </if>

    <if test="memberIntegralReqDTO.orderNo!=null and memberIntegralReqDTO.orderNo!=''">
      and order_no like concat(concat('%',#{memberIntegralReqDTO.orderNo}),'%')
    </if>
    <if test="memberIntegralReqDTO.integralType!=null and memberIntegralReqDTO.integralType!=-1">
      and type=#{memberIntegralReqDTO.integralType}

    </if>
    order by hsm_integral_record.gmt_create desc
  </select>
  <resultMap id="MemberIntegralRecords" type="com.holderzone.saas.store.member.domain.IntegralRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />

    <result column="integral_record_guid" jdbcType="VARCHAR" property="integralRecordGuid" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="business_day" jdbcType="TIMESTAMP" property="businessDay" />
    <result column="transaction_time" jdbcType="TIMESTAMP" property="transactionTime" />
    <result column="residual_integral" jdbcType="INTEGER" property="residualIntegral" />
    <result column="transaction_integral" jdbcType="INTEGER" property="transactionIntegral" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="member_guid" jdbcType="VARCHAR" property="memberGuid" />
    <result column="staff_guid" jdbcType="VARCHAR" property="staffGuid" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />



  </resultMap>


  <select id="selectRecordByOrderGuid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hsm_integral_record
    where order_no = #{orderGuid,jdbcType=VARCHAR}
    and is_delete=0 and transaction_integral!=0
  </select>


  <insert id="batchInsert" parameterType="java.util.List">
    insert into hsm_integral_record (
    gmt_create,
    gmt_modified,
    is_delete,
    integral_record_guid,
    order_no,
    transaction_time,
    business_day,
    residual_integral,
    transaction_integral,
    type,
    account_number,
    member_guid,
    staff_guid,
    staff_name
    )
    values
    <foreach collection="list" item="integralRecordDO" index="index" separator=",">
    (
     #{integralRecordDO.gmtCreate},
     #{integralRecordDO.gmtModified},
     #{integralRecordDO.isDelete},
     #{integralRecordDO.integralRecordGuid},
     #{integralRecordDO.orderNo},
     #{integralRecordDO.transactionTime},
     #{integralRecordDO.businessDay},
     #{integralRecordDO.residualIntegral},
     #{integralRecordDO.transactionIntegral},
     #{integralRecordDO.type},
     #{integralRecordDO.accountNumber},
     #{integralRecordDO.memberGuid},
     #{integralRecordDO.staffGuid},
     #{integralRecordDO.staffName}
    )
    </foreach>
  </insert>




</mapper>