<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.staff.mapper.StoreSourceMapper">

    <select id="queryModuleType" resultType="com.holderzone.saas.store.staff.entity.domain.StoreSourceDO"
            parameterType="com.holderzone.saas.store.staff.entity.query.ModuleTypeQuery">
        select
          s.module_type as moduleType
        from hss_store_source s
        where s.module_guid = #{moduleGuid} and #{requestUri} regexp s.source_url
    </select>

    <!-- 根据用户id和菜单id查询该条件下拥有的资源 -->
    <select id="querySourceByUserAndMenu" resultType="com.holderzone.saas.store.staff.entity.domain.StoreSourceDO">
        SELECT
            ss.module_name,
            ss.source_guid,
            ss.source_name,
            ss.source_code,
            ss.source_url
        FROM hss_r_user_role AS ur
        INNER JOIN hss_role r
            ON ur.role_guid = r.guid and r.is_enable = 1
        INNER JOIN hss_role_source AS rs
            ON ur.role_guid = rs.role_guid
        INNER JOIN hss_store_source AS ss
            ON rs.source_guid = ss.source_guid
        INNER JOIN hss_menu AS m
            ON ss.module_guid = m.module_guid AND rs.menu_guid = m.menu_guid
        WHERE ur.user_guid = #{userGuid}
        <if test="menuGuid != null">
            AND rs.menu_guid = #{menuGuid}
        </if>
        <if test="pageUrl != null">
            and ss.page_url like concat(#{pageUrl},'%')
            order by ss.module_guid
        </if>
    </select>

    <!-- 管理员查询对应会员营销资源 -->
    <select id="queryAdminSource" resultType="com.holderzone.saas.store.staff.entity.domain.StoreSourceDO">
        SELECT
        ss.module_name,
        ss.source_guid,
        ss.source_name,
        ss.source_code,
        ss.source_url
        FROM  hss_store_source AS ss
        INNER JOIN hss_menu AS m
        ON ss.module_guid = m.module_guid
        WHERE ss.page_url like concat(#{pageUrl},'%')
        order by ss.module_guid

    </select>

    <select id="querySourceByUserAndCode"
            resultType="com.holderzone.saas.store.staff.entity.domain.StoreSourceDO">
        SELECT
            ss.source_guid,
            ss.source_name,
            ss.source_code,
            ss.source_url
        FROM
            hss_r_user_role AS ur
        INNER JOIN hss_role r ON ur.role_guid = r.guid AND r.is_enable = 1
        INNER JOIN hss_role_source AS rs ON ur.role_guid = rs.role_guid
        INNER JOIN hss_store_source AS ss ON rs.source_guid = ss.source_guid
        INNER JOIN hss_menu AS m ON ss.module_guid = m.module_guid AND rs.menu_guid = m.menu_guid
        WHERE
            ur.user_guid = #{userGuid} AND ss.source_code = #{sourceCode} LIMIT 1
    </select>

    <select id="querySourceByCode" resultType="com.holderzone.saas.store.staff.entity.domain.StoreSourceDO">
        SELECT
            source_guid,
            source_name,
            source_code,
            source_url
        FROM
            hss_store_source
        WHERE
            source_code = #{sourceCode} LIMIT 1
    </select>


    <select id="querySourceByCodes" resultType="com.holderzone.saas.store.staff.entity.domain.StoreSourceDO">
        SELECT
            source_guid,
            source_name,
            source_code,
            source_url,
            module_guid
        FROM
            hss_store_source
        WHERE
            source_code in
        <foreach collection="sourceCodes" open="(" close=")" separator="," item="sourceCode">
            #{sourceCode}
        </foreach>
        order by gmt_create desc, id desc
    </select>

</mapper>
