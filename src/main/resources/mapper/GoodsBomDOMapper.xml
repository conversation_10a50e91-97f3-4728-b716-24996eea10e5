<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.GoodsBomDOMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.erp.entity.domain.GoodsBomDO">
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="warehouse_guid" jdbcType="VARCHAR" property="warehouseGuid" />
    <result column="store_guid" jdbcType="VARCHAR" property="storeGuid" />
    <result column="goods_guid" jdbcType="VARCHAR" property="goodsGuid" />
    <result column="goods_sku" jdbcType="VARCHAR" property="goodsSku" />
    <result column="material_guid" jdbcType="VARCHAR" property="materialGuid" />
    <result column="usage" jdbcType="DECIMAL" property="usage" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    guid, warehouse_guid, store_guid, goods_guid, goods_sku, material_guid, usage, unit, 
    gmt_create, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.holderzone.erp.entity.domain.GoodsBomDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from hse_goods_bom
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.holderzone.erp.entity.domain.GoodsBomDOExample">
    delete from hse_goods_bom
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.holderzone.erp.entity.domain.GoodsBomDO">
    insert into hse_goods_bom (guid, warehouse_guid, store_guid, 
      goods_guid, goods_sku, material_guid, 
      usage, unit, gmt_create, 
      gmt_modified)
    values (#{guid,jdbcType=VARCHAR}, #{warehouseGuid,jdbcType=VARCHAR}, #{storeGuid,jdbcType=VARCHAR}, 
      #{goodsGuid,jdbcType=VARCHAR}, #{goodsSku,jdbcType=VARCHAR}, #{materialGuid,jdbcType=VARCHAR}, 
      #{usage,jdbcType=DECIMAL}, #{unit,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.holderzone.erp.entity.domain.GoodsBomDO">
    insert into hse_goods_bom
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        guid,
      </if>
      <if test="warehouseGuid != null">
        warehouse_guid,
      </if>
      <if test="storeGuid != null">
        store_guid,
      </if>
      <if test="goodsGuid != null">
        goods_guid,
      </if>
      <if test="goodsSku != null">
        goods_sku,
      </if>
      <if test="materialGuid != null">
        material_guid,
      </if>
      <if test="usage != null">
        usage,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="warehouseGuid != null">
        #{warehouseGuid,jdbcType=VARCHAR},
      </if>
      <if test="storeGuid != null">
        #{storeGuid,jdbcType=VARCHAR},
      </if>
      <if test="goodsGuid != null">
        #{goodsGuid,jdbcType=VARCHAR},
      </if>
      <if test="goodsSku != null">
        #{goodsSku,jdbcType=VARCHAR},
      </if>
      <if test="materialGuid != null">
        #{materialGuid,jdbcType=VARCHAR},
      </if>
      <if test="usage != null">
        #{usage,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.holderzone.erp.entity.domain.GoodsBomDOExample" resultType="java.lang.Long">
    select count(*) from hse_goods_bom
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update hse_goods_bom
    <set>
      <if test="record.guid != null">
        guid = #{record.guid,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseGuid != null">
        warehouse_guid = #{record.warehouseGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.storeGuid != null">
        store_guid = #{record.storeGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsGuid != null">
        goods_guid = #{record.goodsGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsSku != null">
        goods_sku = #{record.goodsSku,jdbcType=VARCHAR},
      </if>
      <if test="record.materialGuid != null">
        material_guid = #{record.materialGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.usage != null">
        usage = #{record.usage,jdbcType=DECIMAL},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update hse_goods_bom
    set guid = #{record.guid,jdbcType=VARCHAR},
      warehouse_guid = #{record.warehouseGuid,jdbcType=VARCHAR},
      store_guid = #{record.storeGuid,jdbcType=VARCHAR},
      goods_guid = #{record.goodsGuid,jdbcType=VARCHAR},
      goods_sku = #{record.goodsSku,jdbcType=VARCHAR},
      material_guid = #{record.materialGuid,jdbcType=VARCHAR},
      usage = #{record.usage,jdbcType=DECIMAL},
      unit = #{record.unit,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>