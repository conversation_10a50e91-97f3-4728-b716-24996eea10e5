<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.GroupTradeDetailMapper">

    <insert id="saveOrUpdate">
        insert into hst_groupon_trade_detail (`guid`, `store_guid`, `third_store_guid`, `third_store_name`, `coupon_code`,
        `coupon_status_desc`, `deal_id`, `deal_title`, `coupon_use_time`, `deal_begin_time`, `coupon_cancel_status`,
        `coupon_buy_price`, `buy_price`, `deal_value`, `biz_cost`, `due`, `verify_acct`, `verify_type`, `is_voucher`,
        `volume`, `single_value`, `order_id`, `order_guid`, `order_no`, `cancel`)
        values ( #{guid}, #{storeGuid}, #{thirdStoreGuid}, #{thirdStoreName}, #{couponCode}, #{couponStatusDesc}, #{dealId},
        #{dealTitle}, #{couponUseTime}, #{dealBeginTime}, #{couponCancelStatus}, #{couponBuyPrice}, #{buyPrice}, #{dealValue},
        #{bizCost}, #{due}, #{verifyAcct}, #{verifyType}, #{isVoucher}, #{volume}, #{singleValue}, #{orderId}, #{orderGuid},
        #{orderNo}, #{cancel} )
        on duplicate key update
        store_guid = values(store_guid),
        third_store_guid = values(third_store_guid),
        third_store_name = values(third_store_name),
        coupon_status_desc = values(coupon_status_desc),
        deal_id = values(deal_id),
        deal_title = values(deal_title),
        coupon_use_time = values(coupon_use_time),
        deal_begin_time = values(deal_begin_time),
        coupon_cancel_status = values(coupon_cancel_status),
        coupon_buy_price = values(coupon_buy_price),
        buy_price = values(buy_price),
        deal_value = values(deal_value),
        biz_cost = values(biz_cost),
        due = values(due),
        verify_acct = values(verify_acct),
        verify_type = values(verify_type),
        is_voucher = values(is_voucher),
        volume = values(volume),
        single_value = values(single_value),
        order_id = values(order_id),
        order_guid = values(order_guid),
        order_no = values(order_no),
        cancel = values(cancel)
    </insert>
</mapper>
