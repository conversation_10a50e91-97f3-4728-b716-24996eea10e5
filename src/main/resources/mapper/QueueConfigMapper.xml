<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.kds.mapper.QueueConfigMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.saas.store.kds.entity.domain.QueueConfigDO">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="store_guid" jdbcType="VARCHAR" property="storeGuid" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="content" jdbcType="INTEGER" property="content" />
    <result column="layout" jdbcType="INTEGER" property="layout" />
    <result column="call_mode" jdbcType="INTEGER" property="callMode" />
    <result column="confirm_mode" jdbcType="INTEGER" property="confirmMode" />
    <result column="confirm_ttl_level" jdbcType="INTEGER" property="confirmTtlLevel" />
    <result column="is_call_up_enable" jdbcType="TINYINT" property="isCallUpEnable" />
    <result column="voice_template" jdbcType="VARCHAR" property="voiceTemplate" />
    <result column="play_times" jdbcType="INTEGER" property="playTimes" />
    <result column="play_interval" jdbcType="INTEGER" property="playInterval" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, guid, store_guid, `source`, content, layout, call_mode, confirm_mode, confirm_ttl_level, is_call_up_enable,
    voice_template, play_times, play_interval, gmt_create, gmt_modified
  </sql>
</mapper>