<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.CheckoutDocumentMapper">


    <insert id="insertCheckoutDocument" parameterType="com.holderzone.erp.entity.domain.CheckoutDocumentDO">

        insert into hse_warehouse_check_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="guid != null and guid != ''">
                guid,
            </if>
            <if test="code != null and code != ''">
                code,
            </if>
            <if test="warehouseGuid != null and warehouseGuid != '' ">
                warehouse_guid,
            </if>
            <if test="warehouseName != null and warehouseName != '' ">
                warehouse_name,
            </if>
            <if test="storeGuid != null and storeGuid != '' ">
                store_guid,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="operatorGuid != null and operatorGuid != ''">
                operator_guid,
            </if>
            <if test="operatorName != null and operatorName != ''">
                operator_name,
            </if>
            <if test="documentDate != null ">
                document_date,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="lock != null">
                is_lock,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createStaffGuid != null and createStaffGuid != ''">
                create_staff_guid,
            </if>
            <if test="createStaffName != null and createStaffName != ''">
                create_staff_name,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="guid != null and guid != ''">
                #{guid},
            </if>
            <if test="code != null and code != ''">
                #{code},
            </if>
            <if test="warehouseGuid != null and warehouseGuid != '' ">
                #{warehouseGuid},
            </if>
            <if test="warehouseName != null and warehouseName != '' ">
                #{warehouseName},
            </if>
            <if test="storeGuid != null and storeGuid != '' ">
                #{storeGuid},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="operatorGuid != null and operatorGuid != ''">
                #{operatorGuid},
            </if>
            <if test="operatorName != null and operatorName != ''">
                #{operatorName},
            </if>
            <if test="documentDate != null ">
                #{documentDate},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="lock != null">
                #{lock},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="createStaffGuid != null and createStaffGuid != ''">
                #{createStaffGuid},
            </if>
            <if test="createStaffName != null and createStaffName != ''">
                #{createStaffName},
            </if>
        </trim>
    </insert>
    <update id="submitCheckoutDocument" parameterType="string">
        UPDATE hse_warehouse_check_document SET `status` = 1 WHERE guid = #{guid}
    </update>
    <delete id="deleteCheckoutDocumentAndDetail" parameterType="string">
        DELETE master_doc,detail
        FROM hse_warehouse_check_document master_doc
        INNER JOIN hse_warehouse_check_document_detail detail ON master_doc.guid = detail.document_guid
        WHERE master_doc.guid = #{documentGuid} and master_doc.status = 0
    </delete>

    <resultMap id="baseResultMap" type="com.holderzone.erp.entity.domain.CheckoutDocumentDO">
        <result column="guid" property="guid"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="code" property="code"/>
        <result column="warehouse_guid" property="warehouseGuid" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="store_guid" property="storeGuid" />
        <result column="type" property="type"/>
        <result column="operator_guid" property="operatorGuid"/>
        <result column="operator_name" property="operatorName"/>
        <result column="document_date" property="documentDate"/>
        <result column="status" property="status"/>
        <result column="is_lock" property="lock"/>
        <result column="remark" property="remark"/>
        <result column="create_staff_guid" property="createStaffGuid"/>
        <result column="create_staff_name" property="createStaffName"/>
    </resultMap>

    <resultMap id="documentAndDetailMap" type="com.holderzone.erp.entity.domain.CheckoutDocumentDO">
        <result column="guid" property="guid"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="warehouse_guid" property="warehouseGuid" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="store_guid" property="storeGuid" />
        <result column="code" property="code"/>
        <result column="type" property="type"/>
        <result column="operator_guid" property="operatorGuid"/>
        <result column="operator_name" property="operatorName"/>
        <result column="document_date" property="documentDate"/>
        <result column="status" property="status"/>
        <result column="is_lock" property="lock"/>
        <result column="remark" property="remark"/>
        <result column="create_staff_guid" property="createStaffGuid" />
        <result column="create_staff_name" property="createStaffName"/>
        <collection property="detailList" ofType="com.holderzone.erp.entity.domain.CheckoutDocumentDetailDO">
            <result column="detail_guid" property="guid"/>
            <result column="document_guid" property="documentGuid" />
            <result column="material_guid" property="materialGuid"/>
            <result column="material_code" property="materialCode"/>
            <result column="material_name" property="materialName"/>
            <result column="stock" property="stock"/>
            <result column="unit_guid" property="unitGuid"/>
            <result column="unit_name" property="unitName"/>
            <result column="check_count" property="checkCount"/>
            <result column="checkout_result" property="checkoutResult"/>
        </collection>
    </resultMap>
    <select id="selectDocumentAndDetail" resultMap="documentAndDetailMap" parameterType="string">
        SELECT doc.guid,doc.gmt_create,doc.warehouse_guid,doc.warehouse_name,doc.`code`,doc.type,doc.operator_guid,doc.operator_name,
        doc.document_date,doc.`status`,doc.is_lock,doc.remark,doc.create_staff_guid,doc.create_staff_name,doc.store_guid,
        detail.guid AS detail_guid,detail.document_guid , detail.material_guid,detail.material_code,detail.material_name,
        detail.stock,detail.unit_guid, detail.unit_name,detail.check_count,detail.checkout_result
        FROM
        hse_warehouse_check_document doc INNER JOIN hse_warehouse_check_document_detail detail ON doc.guid = detail.document_guid
        WHERE doc.guid = #{documentGuid}
    </select>
    <select id="selectDocumentCount" resultType="java.lang.Integer">
        select count(*)
        from hse_warehouse_check_document
        where guid = #{documentGuid}
    </select>
    <select id="selectDocumentStatus" resultMap="baseResultMap">
        select guid , status
        from hse_warehouse_check_document
        where guid = #{documentGuid}
    </select>

    <sql id="selectDocumentListCriteria">
        WHERE
        document_date between #{startDate} and #{endDate}
        <if test="warehouseGuidList != null and warehouseGuidList.size() > 0">
            AND warehouse_guid in
            <foreach collection="warehouseGuidList" item="warehouseGuid" separator="," open="(" close=")">
                #{warehouseGuid}
            </foreach>
        </if>
        <if test="type != null">
            AND `type` = #{type}
        </if>
        <if test="searchContent != null and searchContent != ''">
            AND (guid like #{searchContent}"%" OR operator_name like #{searchContent}"%" )
        </if>
    </sql>

    <select id="selectCheckoutDocumentList" resultMap="baseResultMap"
            parameterType="com.holderzone.erp.entity.domain.CheckoutDocumentQuery">
        SELECT guid,warehouse_name,`type`,operator_name,document_date,`status`
        FROM hse_warehouse_check_document
        <include refid="selectDocumentListCriteria"/>
        ORDER BY status ASC,gmt_modified DESC
        LIMIT #{offset},#{pageSize}
    </select>
    <select id="selectDocumentListCount" resultType="java.lang.Long">
        SELECT count(*)
        FROM hse_warehouse_check_document
        <include refid="selectDocumentListCriteria"/>
    </select>

    <select id="selectDocumentWarehouseAndStoreAndCreateStaff" resultMap="baseResultMap" parameterType="string">
        SELECT guid,warehouse_guid,warehouse_name,store_guid,create_staff_guid,create_staff_name,status
         FROM hse_warehouse_check_document
        WHERE guid = #{guid}
    </select>
    <select id="selectDocument" resultMap="baseResultMap">
        SELECT guid,gmt_create,warehouse_guid,warehouse_name,`code`,`type`,operator_guid,operator_name,
        document_date,`status`,is_lock,remark,create_staff_guid,create_staff_name,store_guid
        from  hse_warehouse_check_document
        where guid = #{documentGuid}
    </select>
    <select id="selectDocumentWarehouseGuid" resultType="java.lang.String" parameterType="string">
        select warehouse_guid from hse_warehouse_check_document where guid = #{documentGuid}
    </select>
    <select id="selectDocumentStoreGuid" resultType="java.lang.String" parameterType="string">
        select store_guid from hse_warehouse_check_document where guid = #{documentGuid}
    </select>
    <select id="selectDocumentStoreGuidAndWarehouseGuid" parameterType="string" resultMap="baseResultMap">
        select store_guid,warehouse_guid from hse_warehouse_check_document where guid = #{documentGuid}
    </select>
    <select id="selectCountByWarehouseGuid" resultType="java.lang.Integer" parameterType="string">
        select count(*) from hse_warehouse_check_document where warehouse_guid = #{warehouseGuid}
    </select>

</mapper>