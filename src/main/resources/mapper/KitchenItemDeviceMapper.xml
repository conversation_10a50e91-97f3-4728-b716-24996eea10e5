<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.kds.mapper.KitchenItemDeviceMapper">


    <update id="updateBatchKitchenItemGuid">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE
                hsk_kitchen_item_device
            SET
                kitchen_item_guid = #{item.kitchenItemGuid}
            WHERE
                kitchen_item_guid = #{item.originalKitchenItemGuid}
        </foreach>
    </update>


    <select id="limitListByDeviceId"
            resultType="com.holderzone.saas.store.kds.entity.domain.KitchenItemDeviceDO">
        SELECT
            guid,
            device_id,
            kitchen_item_guid,
            point_guid
        FROM
            hsk_kitchen_item_device
        WHERE
            device_id = #{deviceId}
        ORDER BY
            guid DESC
            LIMIT 200
    </select>

</mapper>
