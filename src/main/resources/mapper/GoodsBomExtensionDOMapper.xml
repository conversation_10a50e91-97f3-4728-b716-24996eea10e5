<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.GoodsBomDOMapper">
    <resultMap id="QueryResultMap" type="com.holderzone.erp.entity.domain.GoodsBomDO">
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="warehouse_guid" jdbcType="VARCHAR" property="warehouseGuid"/>
        <result column="store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="goods_guid" jdbcType="VARCHAR" property="goodsGuid"/>
        <result column="goods_sku" jdbcType="VARCHAR" property="goodsSku"/>
        <result column="material_guid" jdbcType="VARCHAR" property="materialGuid"/>
        <result column="usage" jdbcType="DECIMAL" property="usage"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="unitName" property="unitName"/>
        <result column="materialName" property="materialName"/>
        <result column="count" property="materialCategoryCount"/>
    </resultMap>
    <insert id="insertBatch">
        insert into hse_goods_bom (guid, warehouse_guid, store_guid, goods_guid,goods_sku, material_guid,`usage`, unit)
        values
        <foreach collection="list" item="goodsBom" separator=",">
            (
            #{goodsBom.guid},
            ifnull(#{goodsBom.warehouseGuid},''),
            ifnull(#{goodsBom.storeGuid},''),
            #{goodsBom.goodsGuid},
            #{goodsBom.goodsSku},
            #{goodsBom.materialGuid},
            #{goodsBom.usage},
            #{goodsBom.unit}
            )
        </foreach>
    </insert>
    <select id="findGoodsBom" resultMap="QueryResultMap">
        SELECT
	    b.guid,
	    b.warehouse_guid,
	    b.store_guid,
        b.goods_guid,
        b.material_guid,
        b.`usage`,
        b.unit,
        m.`name` AS unitName,
        t.`name` as materialName,
        ( SELECT u.`guid` FROM hse_material_unit u WHERE (u.guid = t.unit or u.guid = t.auxiliary_unit) AND u.guid &lt;> b.unit ) auxiliaryUnit,
        ( SELECT u.`name` FROM hse_material_unit u WHERE (u.guid = t.unit or u.guid = t.auxiliary_unit) AND u.guid &lt;> b.unit ) auxiliaryUnitName
        FROM
        hse_goods_bom b
        INNER JOIN hse_material_unit m ON m.guid = b.unit INNER JOIN hse_material t on t.guid = b.material_guid
        where b.goods_sku=#{goodsSku} and b.goods_guid=#{goodsGuid}
        ORDER BY t.gmt_create desc
    </select>
    <select id="countMaterialTypeBySkuList" resultMap="QueryResultMap">
        select
        b.goods_sku,
        count(b.material_guid) as `count`
        FROM
        hse_goods_bom b
        <where>
            b.goods_sku IN
            <foreach collection="list" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </where>
        GROUP BY
        b.goods_sku
    </select>
    <select id="findBomBySku" resultMap="QueryResultMap">
        SELECT
        b.guid,
        b.goods_sku,
        b.material_guid,
        b.`usage`,
        b.unit
        FROM
        hse_goods_bom b
        <where>
            b.goods_sku in
            <foreach collection="list" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </where>
    </select>
</mapper>