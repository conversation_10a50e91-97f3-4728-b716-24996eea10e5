<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.saas.store.kds.mapper.DisplayStoreMapper">

    <resultMap type="com.holderzone.saas.store.kds.entity.domain.DisplayStoreDO" id="DisplayStoreMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="isDelete" column="is_delete"/>
        <result property="ruleGuid" column="rule_guid"/>
        <result property="storeGuid" column="store_guid"/>
        <result property="ruleType" column="rule_type"/>
    </resultMap>
</mapper>