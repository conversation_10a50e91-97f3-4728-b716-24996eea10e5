<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.MaterialStockDOMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.erp.entity.domain.MaterialStockDO">
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="material_guid" jdbcType="VARCHAR" property="materialGuid" />
    <result column="warehouse_guid" jdbcType="VARCHAR" property="warehouseGuid" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="stock" jdbcType="DECIMAL" property="stock" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>

  <resultMap id="documentAndDetailMap" type="com.holderzone.erp.entity.domain.InOutDocumentDO">
        <result column="guid" property="guid"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="supplier_guid" property="supplierGuid"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="contact_document_guid" property="contactDocumentGuid"/>
        <result column="code" property="code"/>
        <result column="warehouse_guid" property="warehouseGuid"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="document_date" property="documentDate"/>
        <result column="type" property="type"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="should_pay_amount" property="shouldPayAmount"/>
        <result column="operator_guid" property="operatorGuid"/>
        <result column="operator_name" property="operatorName"/>
        <result column="create_staff_guid" property="createStaffGuid"/>
        <result column="create_staff_name" property="createStaffName"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="in_out_type" property="inOutType"/>
        <result column="settle_status" property="settleStatus"/>
        <result column="store_guid" property="storeGuid"/>
        <collection property="detailList" ofType="com.holderzone.erp.entity.domain.InOutDocumentDetailDO">
            <result column="detail_guid" property="guid"/>
            <result column="document_guid" property="documentGuid"/>
            <result column="material_guid" property="materialGuid"/>
            <result column="material_code" property="materialCode"/>
            <result column="material_name" property="materialName"/>
            <result column="stock" property="stock"/>
            <result column="unit_guid" property="unitGuid"/>
            <result column="unit_name" property="unitName"/>
            <result column="unit_price" property="unitPrice"/>
            <result column="count" property="count"/>
            <result column="material_total_amount" property="totalAmount"/>
            <result column="return_count" property="returnCount"/>
        </collection>
    </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    guid, material_guid, warehouse_guid, unit, stock, gmt_create, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.holderzone.erp.entity.domain.MaterialStockDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from hse_r_material_stock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.holderzone.erp.entity.domain.MaterialStockDOExample">
    delete from hse_r_material_stock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.holderzone.erp.entity.domain.MaterialStockDO">
    insert into hse_r_material_stock (guid, material_guid, warehouse_guid, 
      unit, stock, gmt_create,
      gmt_modified)
    values (#{guid,jdbcType=VARCHAR}, #{materialGuid,jdbcType=VARCHAR}, #{warehouseGuid,jdbcType=VARCHAR}, 
      #{unit,jdbcType=VARCHAR}, #{stock,jdbcType=DECIMAL}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.holderzone.erp.entity.domain.MaterialStockDO">
    insert into hse_r_material_stock
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        guid,
      </if>
      <if test="materialGuid != null">
        material_guid,
      </if>
      <if test="warehouseGuid != null">
        warehouse_guid,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="stock != null">
        stock,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="materialGuid != null">
        #{materialGuid,jdbcType=VARCHAR},
      </if>
      <if test="warehouseGuid != null">
        #{warehouseGuid,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="stock != null">
        #{stock,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.holderzone.erp.entity.domain.MaterialStockDOExample" resultType="java.lang.Long">
    select count(*) from hse_r_material_stock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

    <select id="queryMaterialBySupplier" resultType="com.holderzone.erp.entity.domain.InOutDocumentDO">
        SELECT
            d.guid,
            d.gmt_create,
            d.supplier_guid,
            d.supplier_name,
            d.contact_document_guid,
            d.`code`,
            d.warehouse_guid,
            d.warehouse_name,
            d.store_guid,
            d.document_date,
            d.create_staff_name,
            d.type,
            d.total_amount,
            d.should_pay_amount,
            d.operator_guid,
            d.operator_name,
            d.remark,
            d.status,
            d.in_out_type,
            d.create_staff_guid,
            d.create_staff_name,

            dd.guid as detail_guid,
            dd.document_guid,
            dd.material_guid,
            dd.material_code,
            dd.material_name,
            dd.stock,
            dd.count,
            dd.unit_guid,
            dd.unit_name,
            dd.unit_price,
            dd.total_amount AS material_total_amount,
            dd.return_count
        FROM
            hse_warehouse_in_out_document d
            INNER JOIN hse_warehouse_in_out_document_detail dd ON d.guid = dd.document_guid
        WHERE
            d.supplier_guid = #{queryDTO.supplierGuid}
            <if test="queryDTO.status != null">
              and d.status = #{queryDTO.status}
            </if>
            <if test="queryDTO.inOutType != null">
              and d.in_out_type = #{queryDTO.inOutType}
            </if>
    </select>

  <update id="updateByExampleSelective" parameterType="map">
    update hse_r_material_stock
    <set>
      <if test="record.guid != null">
        guid = #{record.guid,jdbcType=VARCHAR},
      </if>
      <if test="record.materialGuid != null">
        material_guid = #{record.materialGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseGuid != null">
        warehouse_guid = #{record.warehouseGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.stock != null">
        count = #{record.stock,jdbcType=DECIMAL},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update hse_r_material_stock
    set guid = #{record.guid,jdbcType=VARCHAR},
      material_guid = #{record.materialGuid,jdbcType=VARCHAR},
      warehouse_guid = #{record.warehouseGuid,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      stock = #{record.stock,jdbcType=DECIMAL},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>