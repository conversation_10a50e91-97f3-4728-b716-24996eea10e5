<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.SystemDiscountMapper">
    <sql id="columns">
        system_discout_guid,store_guid,store_name,discount_fee,round_type,keep_type,state
    </sql>
    <sql id="values">
        (
        #{systemDiscountGuid},#{storeGuid},#{storeName},#{discountFee},#{roundType},#{scale},#{state}
        )
    </sql>
    <insert id="insertSystemDiscout" parameterType="com.holderzone.saas.store.business.entity.domain.SystemDiscountDO">
        insert into hsb_system_discount
        (
        <include refid="columns"/>
        )
        values
        <include refid="values"/>
    </insert>

    <insert id="insertAll" parameterType="list">
        insert into hsb_system_discount
        (
        <include refid="columns"/>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.systemDiscountGuid},#{item.storeGuid},#{item.storeName},#{item.discountFee},#{item.roundType},#{item.scale},#{item.state}
            )
        </foreach>
    </insert>
    <select id="countSameFee" resultType="java.lang.Integer">
        select count(*) from hsb_system_discount where discount_fee=#{discountFee} and store_guid = #{storeGuid}
    </select>

    <select id="getDiscountFee" parameterType="string" resultType="java.math.BigDecimal">
        select discount_fee from hsb_system_discount where system_discout_guid = #{systemDiscountGuid}
    </select>

    <resultMap id="systemDisMap" type="com.holderzone.saas.store.dto.trade.SystemDiscountDTO">
        <result column="system_discout_guid" property="systemDiscountGuid"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="store_name" property="storeName"/>
        <result column="discount_fee" property="discountFee"/>
        <result column="round_type" property="roundType"/>
        <result column="keep_type" property="scale"/>
        <result column="state" property="state"/>
        <result column="gmt_create" property="createTime"/>
        <result column="gmt_modified" property="updateTime"/>
    </resultMap>

    <select id="getAllSystemDiscount" parameterType="string" resultMap="systemDisMap">
        select
        <include refid="columns"/>,gmt_create,gmt_modified
        from hsb_system_discount
        where store_guid = #{storeGuid}
    </select>

    <update id="update" parameterType="com.holderzone.saas.store.business.entity.domain.SystemDiscountDO">
        update hsb_system_discount
        <set>
            <trim suffixOverrides=",">
                <if test="discountFee!=null">
                    discount_fee = #{discountFee},
                </if>
                <if test="roundType!=null">
                    round_type = #{roundType},
                </if>
                <if test="scale!=null">
                    keep_type = #{scale},
                </if>
                <if test="state!=null">
                    state = #{state},
                </if>
            </trim>
        </set>
        where system_discout_guid = #{systemDiscountGuid} and store_guid = #{storeGuid}
    </update>

    <delete id="delete">
        delete from hsb_system_discount
        where system_discout_guid = #{systemDiscountGuid} and store_guid=#{storeGuid}
    </delete>
    <resultMap id="systemDOSMap" type="com.holderzone.saas.store.business.entity.domain.SystemDiscountDO">
        <result column="system_discout_guid" property="systemDiscountGuid"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="store_name" property="storeName"/>
        <result column="round_type" property="roundType"/>
        <result column="keep_type" property="scale"/>
        <result column="discount_fee" property="discountFee"/>
        <result column="state" property="state"/>
        <result column="gmt_create" property="createTime"/>
        <result column="gmt_modified" property="updateTime"/>
    </resultMap>
    <select id="getAllSystemDOS" parameterType="string" resultMap="systemDOSMap">
        select
        <include refid="columns"/>,gmt_create,gmt_modified
        from hsb_system_discount
        where store_guid = #{storeGuid}
    </select>
    <select id="getByStoreGuid" resultMap="systemDOSMap">
        select
        <include refid="columns"/>,gmt_create,gmt_modified
        from hsb_system_discount
        where store_guid = #{storeGuid}
    </select>
    <select id="getById" resultMap="systemDOSMap">
        select
        <include refid="columns"/>,gmt_create,gmt_modified
        from hsb_system_discount
        where system_discout_guid = #{systemDiscountGuid}
    </select>
</mapper>