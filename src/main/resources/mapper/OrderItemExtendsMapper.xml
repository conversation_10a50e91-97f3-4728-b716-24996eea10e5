<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.OrderItemExtendsMapper">

    <update id="restoreOrderItemExtends">
        update hst_order_item_extends set is_delete = 0 where guid in
        <foreach collection="orderItemGuidList" item="orderItemGuid" open="(" separator="," close=")" >
            #{orderItemGuid}
        </foreach>
    </update>
</mapper>
