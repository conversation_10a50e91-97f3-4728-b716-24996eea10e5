<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.OrderAbnormalRecordMapper">

    <update id="deleteRecord">
        update hst_order_abnormal_record set is_delete=1 where order_guid=#{orderGuid} and is_delete=0;
    </update>

    <select id="listAbnormalOrders" resultType="com.holderzone.saas.store.dto.trade.OrderAbnormalRecordRespDTO">
        SELECT
        a.checkout_time,
        o.order_no,
        a.dining_method_id,
        a.payment_amount,
        a.payment_method_id,
        a.payment_method_name,
        a.payment_statu,
        a.pay_guid,
        a.order_guid
        FROM
        hst_order_abnormal_record a
        LEFT JOIN hst_order o ON o.guid = a.order_guid
        <where>
            a.payment_statu = 2
            <if test="dto.storeGuid != null and dto.storeGuid !='' ">
                AND a.store_guid = #{dto.storeGuid}
            </if>
            <if test="dto.beginTime != null ">
                AND a.checkout_time &gt;= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null ">
                AND a.checkout_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.searchKey != null and dto.searchKey !='' ">
                AND(
                o.order_no LIKE CONCAT('%',#{dto.searchKey},'%')
                OR o.dining_table_name LIKE CONCAT('%',#{dto.searchKey},'%')
                OR o.mark LIKE CONCAT('%',#{dto.searchKey},'%')
                OR o.member_phone LIKE CONCAT('%',#{dto.searchKey},'%')
                )
            </if>
            and a.is_delete = 0
        </where>
        ORDER BY a.checkout_time DESC
    </select>
</mapper>
