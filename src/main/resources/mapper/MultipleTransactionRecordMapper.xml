<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.MultipleTransactionRecordMapper">

    <update id="addBatchRefundAmount">
        <foreach collection="list" item="item" separator=";">
            update
                hst_multiple_transaction_record
            set
                refund_amount = refund_amount + ${item.amount},
                refundable_fee = refundable_fee - ${item.amount}
            where
                guid = #{item.guid}
        </foreach>
    </update>
</mapper>
