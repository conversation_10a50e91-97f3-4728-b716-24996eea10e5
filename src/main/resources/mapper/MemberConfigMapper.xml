<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.member.mapper.MemberConfigMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.saas.store.member.domain.MemberConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="member_config_guid" jdbcType="VARCHAR" property="memberConfigGuid" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="key" jdbcType="INTEGER" property="key" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="value" jdbcType="VARCHAR" property="value" />
  </resultMap>
  <sql id="Base_Column_List">
    id, gmt_create, gmt_modified, is_delete, member_config_guid, `type`, `key`, `desc`, 
    `value`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hsm_member_config
    where member_config_guid = #{memberConfigGuid,jdbcType=VARCHAR}
  </select>
    <select id="selectByConfigKey" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from hsm_member_config
      where `key` = #{code,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from hsm_member_config
    where member_config_guid = #{memberConfigGuid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.holderzone.saas.store.member.domain.MemberConfigDO">
    insert into hsm_member_config (id, gmt_create, gmt_modified, 
      is_delete, member_config_guid, `type`, 
      `key`, `desc`, `value`)
    values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=BIT}, #{memberConfigGuid,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{key,jdbcType=INTEGER}, #{desc,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.holderzone.saas.store.member.domain.MemberConfigDO">
    insert into hsm_member_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="memberConfigGuid != null">
        member_config_guid,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="key != null">
        `key`,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="value != null">
        `value`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="memberConfigGuid != null">
        #{memberConfigGuid,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        #{key,jdbcType=INTEGER},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.holderzone.saas.store.member.domain.MemberConfigDO">
    update hsm_member_config
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="memberConfigGuid != null">
        member_config_guid = #{memberConfigGuid,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        `key` = #{key,jdbcType=INTEGER},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        `value` = #{value,jdbcType=VARCHAR},
      </if>
    </set>
    where member_config_guid = #{memberConfigGuid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.holderzone.saas.store.member.domain.MemberConfigDO">
    update hsm_member_config
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      member_config_guid = #{memberConfigGuid,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      `key` = #{key,jdbcType=INTEGER},
      `desc` = #{desc,jdbcType=VARCHAR},
      `value` = #{value,jdbcType=VARCHAR}
   where member_config_guid = #{memberConfigGuid,jdbcType=VARCHAR}
  </update>
</mapper>