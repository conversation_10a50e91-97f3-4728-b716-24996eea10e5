<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.MaterialDOMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.erp.entity.domain.MaterialDO">
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="enterprise_guid" jdbcType="VARCHAR" property="enterpriseGuid" />
    <result column="store_guid" jdbcType="VARCHAR" property="storeGuid" />
    <result column="warehouse_guid" jdbcType="VARCHAR" property="warehouseGuid" />
    <result column="property" jdbcType="VARCHAR" property="property" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="simple_name" jdbcType="VARCHAR" property="simpleName" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="auxiliary_unit" jdbcType="VARCHAR" property="auxiliaryUnit" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="specs" jdbcType="VARCHAR" property="specs" />
    <result column="conversion_main" jdbcType="DECIMAL" property="conversionMain" />
    <result column="conversion_auxiliary" jdbcType="DECIMAL" property="conversionAuxiliary" />
    <result column="lowest_stock" jdbcType="DECIMAL" property="lowestStock" />
    <result column="sales_price" jdbcType="DECIMAL" property="salesPrice" />
    <result column="cost_price" jdbcType="DECIMAL" property="costPrice" />
    <result column="effective_date" jdbcType="INTEGER" property="effectiveDate" />
    <result column="storage_method" jdbcType="VARCHAR" property="storageMethod" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    guid, enterprise_guid, store_guid, warehouse_guid, property, name, simple_name, unit, 
    auxiliary_unit, category, type, code, bar_code, specs, conversion_main, conversion_auxiliary, 
    lowest_stock, sales_price, cost_price, effective_date, storage_method, image,
    enabled, deleted, gmt_create, gmt_modified, remark
  </sql>
  <select id="selectByExample" parameterType="com.holderzone.erp.entity.domain.MaterialDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from hse_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.holderzone.erp.entity.domain.MaterialDOExample">
    delete from hse_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.holderzone.erp.entity.domain.MaterialDO">
    insert into hse_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        guid,
      </if>
      <if test="enterpriseGuid != null">
        enterprise_guid,
      </if>
      <if test="storeGuid != null">
        store_guid,
      </if>
      <if test="warehouseGuid != null">
        warehouse_guid,
      </if>
      <if test="property != null">
        property,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="simpleName != null">
        simple_name,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="auxiliaryUnit != null">
        auxiliary_unit,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="specs != null">
        specs,
      </if>
      <if test="conversionMain != null">
        conversion_main,
      </if>
      <if test="conversionAuxiliary != null">
        conversion_auxiliary,
      </if>
      <if test="lowestStock != null">
        lowest_stock,
      </if>
      <if test="salesPrice != null">
        sales_price,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="effectiveDate != null">
        effective_date,
      </if>
      <if test="storageMethod != null">
        storage_method,
      </if>
      <if test="image != null">
        image,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseGuid != null">
        #{enterpriseGuid,jdbcType=VARCHAR},
      </if>
      <if test="storeGuid != null">
        #{storeGuid,jdbcType=VARCHAR},
      </if>
      <if test="warehouseGuid != null">
        #{warehouseGuid,jdbcType=VARCHAR},
      </if>
      <if test="property != null">
        #{property,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="simpleName != null">
        #{simpleName,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="auxiliaryUnit != null">
        #{auxiliaryUnit,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="specs != null">
        #{specs,jdbcType=VARCHAR},
      </if>
      <if test="conversionMain != null">
        #{conversionMain,jdbcType=DECIMAL},
      </if>
      <if test="conversionAuxiliary != null">
        #{conversionAuxiliary,jdbcType=DECIMAL},
      </if>
      <if test="lowestStock != null">
        #{lowestStock,jdbcType=DECIMAL},
      </if>
      <if test="salesPrice != null">
        #{salesPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="effectiveDate != null">
        #{effectiveDate,jdbcType=INTEGER},
      </if>
      <if test="storageMethod != null">
        #{storageMethod,jdbcType=VARCHAR},
      </if>
      <if test="image != null">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate},
      </if>
      <if test="gmtModified != null">
        #{gmtModified},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.holderzone.erp.entity.domain.MaterialDOExample" resultType="java.lang.Long">
    select count(*) from hse_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update hse_material
    <set>
      <if test="record.guid != null">
        guid = #{record.guid,jdbcType=VARCHAR},
      </if>
      <if test="record.enterpriseGuid != null">
        enterprise_guid = #{record.enterpriseGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.storeGuid != null">
        store_guid = #{record.storeGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseGuid != null">
        warehouse_guid = #{record.warehouseGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.property != null">
        property = #{record.property,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.simpleName != null">
        simple_name = #{record.simpleName,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.auxiliaryUnit != null">
        auxiliary_unit = #{record.auxiliaryUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.specs != null">
        specs = #{record.specs,jdbcType=VARCHAR},
      </if>
      <if test="record.conversionMain != null">
        conversion_main = #{record.conversionMain,jdbcType=DECIMAL},
      </if>
      <if test="record.conversionAuxiliary != null">
        conversion_auxiliary = #{record.conversionAuxiliary,jdbcType=DECIMAL},
      </if>
      <if test="record.lowestStock != null">
        lowest_stock = #{record.lowestStock,jdbcType=DECIMAL},
      </if>
      <if test="record.salesPrice != null">
        sales_price = #{record.salesPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.costPrice != null">
        cost_price = #{record.costPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.effectiveDate != null">
        effective_date = #{record.effectiveDate,jdbcType=INTEGER},
      </if>
      <if test="record.storageMethod != null">
        storage_method = #{record.storageMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.image != null">
        image = #{record.image,jdbcType=VARCHAR},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>