<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.EstimateSellLogMapper">


    <select id="listByStoreGuidAndSkuGuidsLimit1"
            resultType="com.holderzone.saas.store.item.entity.domain.EstimateSellLogDO">
        SELECT
            log1.id,
            log1.store_guid,
            log1.sku_guid,
            log1.sell
        FROM
            hsi_estimate_sell_log log1
        JOIN ( SELECT log2.store_guid,log2.sku_guid,max(log2.id) as "id"
                FROM hsi_estimate_sell_log log2
                WHERE log2.store_guid = #{storeGuid} and log2.sku_guid in
                <foreach collection="skuGuids" open="(" separator="," close=")" item="skuGuid">
                    #{skuGuid}
                </foreach>
                 group by log2.store_guid,log2.sku_guid ) tmp on log1.id = tmp.id
    </select>
</mapper>
