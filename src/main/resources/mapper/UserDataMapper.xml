<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.staff.mapper.UserDataMapper">

    <resultMap id="UserMap" type="com.holderzone.saas.store.staff.entity.domain.UserDataDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <!--<id column="guid" jdbcType="VARCHAR" property="guid"/>-->
        <!--<id column="enterprise_no" jdbcType="VARCHAR" property="enterpriseNo"/>-->
        <!--<id column="account" jdbcType="VARCHAR" property="account"/>-->
        <!--<id column="password" jdbcType="VARCHAR" property="password"/>-->
        <!--<id column="auth_code" jdbcType="VARCHAR" property="authCode"/>-->
        <!--<id column="name" jdbcType="VARCHAR" property="name"/>-->
        <!--<id column="phone" jdbcType="VARCHAR" property="phone"/>-->
        <!--<id column="id_card_no" jdbcType="VARCHAR" property="idCardNo"/>-->
        <!--<id column="id_card_address" jdbcType="VARCHAR" property="idCardAddress"/>-->
        <!--<id column="address" jdbcType="VARCHAR" property="address"/>-->
        <!--<id column="birthday" jdbcType="TIMESTAMP" property="birthday"/>-->
        <!--<id column="on_boarding_time" jdbcType="TIMESTAMP" property="onBoardingTime"/>-->
        <!--<id column="create_staffGuid" jdbcType="VARCHAR" property="createStaffGuid"/>-->
        <!--<id column="update_staffGuid" jdbcType="VARCHAR" property="updateStaffGuid"/>-->
        <!--<id column="is_enable" jdbcType="TINYINT" property="isEnable"/>-->
        <!--<id column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>-->
        <!--<id column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>-->
        <!--<id column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>-->
    </resultMap>

</mapper>