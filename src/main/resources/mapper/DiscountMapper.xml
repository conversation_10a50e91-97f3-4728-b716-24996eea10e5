<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.retail.mapper.DiscountMapper">


    <update id="batchUpdate">
        <foreach collection="list" item="discount" separator=";">
            update hst_discount
            set
            discount_fee = #{discount.discountFee,jdbcType=DECIMAL},
            discount = #{discount.discount,jdbcType=DECIMAL}
            where guid=#{discount.guid,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>
