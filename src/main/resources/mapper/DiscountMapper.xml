<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.DiscountMapper">

    <resultMap id="AmountItemDTO" type="com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO">
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="amount" property="amount"/>
        <result column="order_count" property="orderCount"/>
        <result column="discount_order_count" property="discountOrderCount"/>
    </resultMap>

    <update id="batchUpdate">
        <foreach collection="list" item="discount" separator=";">
            update hst_discount
            set
            discount_fee = #{discount.discountFee,jdbcType=DECIMAL},
            discount = #{discount.discount,jdbcType=DECIMAL}
            where guid=#{discount.guid,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="listHasGrouponCompleteOrder"
            resultType="com.holderzone.saas.store.trade.entity.domain.DiscountDO">
        select
            d.*
        from
            hst_discount d
        join hst_order o on o.guid = d.order_guid
        where
            d.is_delete = 0
        <if test="orderGuid != null and orderGuid != '' ">
            and o.guid = #{orderGuid}
        </if>
        and o.state in (4, 7)
        and o.gmt_create >= #{startTime}
        and d.discount_type in (6, 61, 62, 65, 66)
        and d.discount_fee > 0
    </select>

    <select id="handoverDiscountType" parameterType="com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO"
            resultMap="AmountItemDTO">
        SELECT
        t.`discount_type` code,
        t.`discount_name` name,
        SUM(t.`discount_fee`) amount
        FROM `hst_discount` t
        JOIN `hst_order` o ON o.`guid`=t.`order_guid`
        WHERE
        o.`state` = 4
        AND o.`is_delete` = 0
        AND t.`is_delete` = 0
        AND t.`discount_fee`!= 0
        <if test="null!=dto.storeGuid and dto.storeGuid!=''">
            AND t.store_guid = #{dto.storeGuid}
        </if>
        <if test="null!=dto.gmtCreate">
            AND o.checkout_time &gt;= #{dto.gmtCreate}
        </if>
        <if test="null!=dto.gmtModified">
            AND o.checkout_time &lt;= #{dto.gmtModified}
        </if>
        <if test="null!=dto.userGuid and dto.userGuid!=''">
            AND t.staff_guid = #{dto.userGuid}
        </if>
        GROUP BY t.`discount_type`
    </select>
</mapper>
