<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.TradeDebtMapper">


    <select id="statistics" resultType="com.holderzone.saas.store.dto.report.resp.DebtTotalStatisticsDTO">
        select
            COALESCE(sum(dur.debt_fee), 0) as "debtAmount",
            COALESCE(sum(dur.repayment_fee), 0) as "repaymentAmount",
            COALESCE(sum(dur.debt_fee),0) - COALESCE(sum(dur.repayment_fee), 0) as "unRepaymentAmount",
            COALESCE(count(1),0) as "total"
        from
            "hst_trade_${query.enterpriseGuid}_db"."hst_debt_unit_record" dur
        left join "hst_trade_${query.enterpriseGuid}_db"."hst_debt_unit" du on du.guid = dur.unit_guid
        <include refid="whereSQL" />
    </select>


    <select id="pageInfo" resultType="com.holderzone.saas.store.dto.report.resp.DebtUnitRecordDTO">
        select
            dur.guid,
            dur.debt_invoice_code,
            dur.store_name,
            dur.repayment_status,
            dur.debt_fee,
            dur.repayment_fee,
            dur.payment_type,
            dur.gmt_create,
            case when dur.repayment_status = 1 then dur.gmt_modified else null end as "gmt_modified",
            dur.create_staff_name,
            dur.update_staff_name,

            du.name as unit_name
        from
            "hst_trade_${query.enterpriseGuid}_db"."hst_debt_unit_record" dur
        left join "hst_trade_${query.enterpriseGuid}_db"."hst_debt_unit" du on du.guid = dur.unit_guid
        <include refid="whereSQL" />
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <select id="count" resultType="java.lang.Integer">
        select
            count(1)
        from
            "hst_trade_${query.enterpriseGuid}_db"."hst_debt_unit_record" dur
        left join "hst_trade_${query.enterpriseGuid}_db"."hst_debt_unit" du on du.guid = dur.unit_guid
        <include refid="whereSQL" />
    </select>


    <sql id="whereSQL">
        <where>
            du.is_deleted = 0 and dur.is_delete = 0
            <if test="query.startTime != null and query.endTime != null">
                and dur.gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
            </if>
            <if test="query.repaymentStatus != null">
                and dur.repayment_status = #{query.repaymentStatus}
            </if>
            <if test="query.debtUnitGuid != null and query.debtUnitGuid != ''">
                and dur.unit_guid = #{query.debtUnitGuid}
            </if>
        </where>
    </sql>
</mapper>
