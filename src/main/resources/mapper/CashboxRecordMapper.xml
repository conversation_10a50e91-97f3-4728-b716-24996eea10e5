<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.CashboxRecordMapper">

    <insert id="insert" useGeneratedKeys="true"
            parameterType="com.holderzone.saas.store.business.entity.domain.CashboxRecordDO">
        INSERT INTO hsb_cashbox_record
        (
        <include refid="notNullColumn"/>
        )
        VALUES
        (
        <include refid="notNullColumnValue"/>
        )
    </insert>

    <select id="query" resultMap="CashboxRecordMapResult"
            parameterType="com.holderzone.saas.store.business.entity.domain.CashboxRecordDO">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_cashbox_record
        WHERE
        `cashbox_record_guid` = #{cashboxRecordGuid}
    </select>

    <select id="queryAll" resultMap="CashboxRecordMapResult"
            parameterType="com.holderzone.saas.store.business.entity.domain.CashboxRecordDO">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_cashbox_record
        WHERE
        `store_guid` = #{storeGuid}
        <if test="userGuid!=null and userGuid!=''">
            AND `user_guid` = #{userGuid}
        </if>
        <if test="operationType!=null">
            AND `operation_type` = #{operationType}
        </if>
    </select>

    <select id="queryRecently" resultMap="CashboxRecordMapResult">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_cashbox_record
        WHERE
        `handover_record_guid` = #{handoverRecordGuid}
        ORDER BY `gmt_create` DESC
        LIMIT 1
    </select>

    <resultMap id="CashboxRecordMapResult" type="com.holderzone.saas.store.business.entity.domain.CashboxRecordDO">
        <result property="storeGuid" column="store_guid"/>
        <result property="terminalId" column="terminal_id"/>
        <result property="handoverRecordGuid" column="handover_record_guid"/>
        <result property="cashboxRecordGuid" column="cashbox_record_guid"/>
        <result property="userGuid" column="user_guid"/>
        <result property="userName" column="user_name"/>
        <result property="operationType" column="operation_type"/>
        <result property="sourceType" column="source_type"/>
        <result property="money" column="money"/>
        <!--<result property="balance" column="balance"/>-->
        <result property="remark" column="remark"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="notNullColumn">
        <trim suffixOverrides=",">
            <if test="storeGuid!=null and storeGuid!=''">
                `store_guid`,
            </if>
            <if test="handoverRecordGuid!=null and handoverRecordGuid!=''">
                `handover_record_guid`,
            </if>
            <if test="cashboxRecordGuid!=null and cashboxRecordGuid!=''">
                `cashbox_record_guid`,
            </if>
            <if test="userGuid!=null and userGuid!=''">
                `user_guid`,
            </if>
            <if test="userName!=null and userName!=''">
                `user_name`,
            </if>
            <if test="operationType!=null">
                `operation_type`,
            </if>
            <if test="money!=null">
                `money`,
            </if>
            <if test="remark!=null and remark!=''">
                `remark`,
            </if>
            <if test="terminalId!=null and terminalId!=''">
                `terminal_id`,
            </if>
            <if test="balance!=null">
                `balance`,
            </if>
            <if test="sourceType!=null">
                `source_type`,
            </if>
        </trim>
    </sql>

    <sql id="notNullColumnValue">
        <trim suffixOverrides=",">
            <if test="storeGuid!=null and storeGuid!=''">
                #{storeGuid},
            </if>
            <if test="handoverRecordGuid!=null and handoverRecordGuid!=''">
                #{handoverRecordGuid},
            </if>
            <if test="cashboxRecordGuid!=null and cashboxRecordGuid!=''">
                #{cashboxRecordGuid},
            </if>
            <if test="userGuid!=null and userGuid!=''">
                #{userGuid},
            </if>
            <if test="userName!=null and userName!=''">
                #{userName},
            </if>
            <if test="operationType!=null">
                #{operationType},
            </if>
            <if test="money!=null">
                #{money},
            </if>
            <if test="remark!=null and remark!=''">
                #{remark},
            </if>
            <if test="terminalId!=null and terminalId!=''">
                #{terminalId},
            </if>
            <if test="balance!=null">
                #{balance},
            </if>
            <if test="sourceType!=null">
                #{sourceType},
            </if>
        </trim>
    </sql>

    <sql id="columns">
        <trim suffixOverrides=",">
            `store_guid`,
            `terminal_id`,
            `handover_record_guid`,
            `cashbox_record_guid`,
            `user_guid`,
            `user_name`,
            `operation_type`,
            `source_type`,
            `money`,
            `balance`,
            `remark`,
            DATE_FORMAT(`gmt_create`,"%Y-%m-%d %T") AS gmt_create,
            DATE_FORMAT(`gmt_modified`,"%Y-%m-%d %T") AS gmt_modified,
        </trim>
    </sql>

</mapper>