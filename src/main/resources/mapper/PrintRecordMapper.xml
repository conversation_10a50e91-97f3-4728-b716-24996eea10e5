<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.print.mapper.PrintRecordMapper">

    <resultMap id="ExtensionResultMap" type="com.holder.saas.print.entity.read.PrintRecordReadDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="record_uid" jdbcType="VARCHAR" property="recordUid"/>
        <result column="record_guid" jdbcType="VARCHAR" property="recordGuid"/>
        <result column="invoice_type" jdbcType="CHAR" property="invoiceType"/>
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="print_status" jdbcType="CHAR" property="printStatus"/>
        <result column="print_status_msg" jdbcType="VARCHAR" property="printStatusMsg"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="print_content" jdbcType="LONGVARCHAR" property="printContent"/>
        <association property="printerDO" javaType="com.holder.saas.print.entity.domain.PrinterDO">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="printer_type" jdbcType="CHAR" property="printerType"/>
            <result column="printer_name" jdbcType="VARCHAR" property="printerName"/>
            <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
            <result column="printer_port" jdbcType="CHAR" property="printerPort"/>
            <result column="business_type" jdbcType="CHAR" property="businessType"/>
            <result column="print_count" jdbcType="INTEGER" property="printCount"/>
            <result column="print_page" jdbcType="CHAR" property="printPage"/>
            <result column="print_cut" jdbcType="CHAR" property="printCut"/>
            <result column="device_id" property="deviceId"/>
        </association>
    </resultMap>

    <resultMap id="PrintTaskResultMap" type="com.holder.saas.print.entity.read.PrintRecordReadDO">
        <result column="record_guid" jdbcType="CHAR" property="recordGuid"/>
        <result column="store_guid" jdbcType="CHAR" property="storeGuid"/>
        <result column="invoice_type" jdbcType="CHAR" property="invoiceType"/>
        <result column="print_content" jdbcType="LONGVARCHAR" property="printContent"/>
        <association property="printerDO" javaType="com.holder.saas.print.entity.domain.PrinterDO">
            <result column="business_type" jdbcType="CHAR" property="businessType"/>
            <result column="printer_type" jdbcType="CHAR" property="printerType"/>
            <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
            <result column="printer_port" jdbcType="CHAR" property="printerPort"/>
            <result column="print_count" jdbcType="INTEGER" property="printCount"/>
            <result column="print_page" jdbcType="CHAR" property="printPage"/>
        </association>
    </resultMap>

    <resultMap id="RecordResultMap" type="com.holder.saas.print.entity.read.PrintRecordReadDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="record_uid" jdbcType="VARCHAR" property="recordUid"/>
        <result column="record_guid" jdbcType="VARCHAR" property="recordGuid"/>
        <result column="invoice_type" jdbcType="CHAR" property="invoiceType"/>
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="print_status" jdbcType="CHAR" property="printStatus"/>
        <result column="print_status_msg" jdbcType="VARCHAR" property="printStatusMsg"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="print_content" jdbcType="LONGVARCHAR" property="printContent"/>
    </resultMap>

    <select id="queryByRecordGuid" parameterType="com.holder.saas.print.entity.query.PrintRecordQuery"
            resultMap="PrintTaskResultMap">
        select
        r.record_guid, r.store_guid, r.invoice_type, r.print_content,
        p.print_page, p.print_count, p.business_type, p.printer_type, p.printer_ip, p.printer_port
        from hsp_print_record r
        left join hsp_printer p on p.printer_guid=r.printer_guid
        where r.record_guid=#{recordGuid} and r.is_deleted = 0
    </select>

    <select id="queryInRecordGuid" parameterType="com.holder.saas.print.entity.query.PrintRecordQuery"
            resultMap="PrintTaskResultMap">
        <foreach collection="arrayOfRecordGuid" item="recordGuid" index="index" separator="union all">
            select
            r.record_guid, r.invoice_type, r.print_content,
            p.print_page, p.print_count, p.business_type, p.printer_type, p.printer_ip, p.printer_port
            from hsp_print_record r
            left join hsp_printer p on p.printer_guid=r.printer_guid
            where r.record_guid=#{recordGuid} and r.is_deleted = 0
        </foreach>
    </select>

    <select id="countByDeviceAndStatus" parameterType="com.holder.saas.print.entity.domain.PrintRecordDO"
            resultType="java.lang.Long">
        select count(*)
        from hsp_print_record
        where device_id=#{deviceId} and print_status=#{printStatus} and is_deleted = 0
    </select>

    <select id="listByDeviceAndStatus" parameterType="com.holder.saas.print.entity.domain.PrintRecordDO"
            resultMap="ExtensionResultMap">
        select
        r.*,
        p.business_type, p.printer_type, p.printer_name, p.printer_ip
        from hsp_print_record r
        inner join hsp_printer p on p.printer_guid=r.printer_guid
        where r.is_deleted = 0
        and r.print_status=#{printStatus}
        and r.store_guid=#{storeGuid}
        <if test="deviceId != null and deviceId != ''">
            and r.device_id=#{deviceId}
        </if>
        order by gmt_create desc, invoice_type desc
    </select>

    <select id="listByDeviceAndStatusFor3Days" parameterType="com.holderzone.saas.store.dto.print.PrintRecordDTO"
            resultMap="ExtensionResultMap">
        select
        r.*,
        p.business_type, p.printer_type, p.printer_name, p.printer_ip
        from hsp_print_record r
        inner join hsp_printer p on p.printer_guid=r.printer_guid
        where r.is_deleted = 0
        and r.print_status=#{printStatus}
        and r.store_guid=#{storeGuid}
        <if test="beginTime != null and beginTime != ''">
            and r.gmt_create &gt;= #{beginTime}
        </if>
        <if test="deviceId != null and deviceId != ''">
            and r.device_id=#{deviceId}
        </if>
        order by gmt_create desc, invoice_type desc
    </select>

    <delete id="deleteHistory" parameterType="com.holderzone.saas.store.dto.print.PrintRecordDTO">
       delete  from hsp_print_record where print_status=2
        <if test="beginTime != null and beginTime != ''">
            and gmt_create &lt;= #{beginTime}
        </if>
        limit 1000;
    </delete>

    <select id="listByFakePrinterGuid" parameterType="com.holder.saas.print.entity.domain.PrintRecordDO"
            resultMap="RecordResultMap">
        select r.*
        from hsp_print_record r
        where r.print_status=#{printStatus} and r.printer_guid ='000000' and r.is_deleted = 0
        order by gmt_create desc, invoice_type desc
    </select>

</mapper>
