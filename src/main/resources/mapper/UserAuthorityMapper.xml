<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.staff.mapper.UserAuthorityMapper">

    <select id="queryEmployeePermissions" resultType="com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO">
        SELECT source_code,
               source_name,
               source_url
        FROM hss_authority
        WHERE is_delete = 0;
    </select>

    <select id="queryAuthorize" resultType="com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO">
        SELECT ua.authority_code,
               ua.user_guid,
               ua.source_code,
               ua.authorizer_guid,
               ua.authorizer_account,
               ua.user_guid,
               a.source_from
        FROM hss_user_authority ua
                 INNER JOIN hss_authority a ON a.source_code = ua.source_code
        WHERE a.source_code = #{reqDTO.sourceCode}
          AND ua.authority_code = #{reqDTO.authorityCode}
          AND ua.is_delete = 0;
    </select>
    <select id="queryUserAuthority" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM hss_user_authority ua
                 INNER JOIN hss_authority a ON a.source_code = ua.source_code
        WHERE a.source_code = #{reqDTO.sourceCode}
          AND ua.user_guid = #{reqDTO.userGuid}
          AND ua.is_delete = 0
          AND a.source_from = #{reqDTO.sourceFrom};
    </select>

    <select id="queryAuthorityAnyMatch" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM hss_user_authority ua
        INNER JOIN hss_authority a ON a.source_code = ua.source_code
        WHERE
        ua.authority_code IS NOT NULL
        AND ua.is_delete = 0
        <if test="dto.sourceCode != null and dto.sourceCode != ''">
            AND ua.source_code = #{dto.sourceCode}
        </if>
        <if test="dto.userGuids != null and dto.userGuids.size > 0">
            AND ua.user_guid IN
            <foreach item="userGuid" collection="dto.userGuids" separator="," open="(" close=")">
                #{userGuid}
            </foreach>
        </if>
    </select>
</mapper>
