<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.staff.mapper.RoleSourceMapper">
    <!-- 批量更新 -->
    <update id="batchUpdateSource" parameterType="java.util.List">
        <foreach collection="roleSourceDOList" item="item" index="index" open="" close="" separator=";">
            UPDATE hss_role_source SET
            source_url = #{item.sourceUrl},
            source_code = #{item.sourceCode}
            WHERE source_guid = #{item.sourceGuid}
        </foreach>
    </update>
    <update id="updateModifiedRoleSource">
        update hss_role_source t1,hss_store_source t2
        set t1.source_url=t2.source_url, t1.source_code=t2.source_code
        where
            t1.source_guid=t2.source_guid
          and
            t1.terminal_guid=t2.terminal_guid
        ;
    </update>
    <delete id="deleteUnnecessaryRoleSource">
        delete from hss_role_source where source_guid not in ( select source_guid from hss_store_source
        where terminal_guid in
        <foreach collection="terminalGuidList" item="item" index="terminalGuid" open="(" close=")" separator=",">
            #{terminalGuid}
        </foreach>
        )
        and terminal_guid in
        <foreach collection="terminalGuidList" item="item" index="terminalGuid" open="(" close=")" separator=",">
            #{terminalGuid}
        </foreach>;
    </delete>
</mapper>
