<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.ScreenPictureMapper">

    <sql id="columns">
      screen_picture_guid,oss_url,store_guid,store_name,weight,height,px_type,pic_type
    </sql>
    <sql id="values">
      #{screenPictureGuid},#{ossUrl},#{storeGuid},#{storeName},#{weight},#{height},#{pxType},#{picType}
    </sql>
    <insert id="insertOrUpdate" parameterType="com.holderzone.saas.store.business.entity.domain.ScreenPictureDO">
        insert into hsb_screen_picture
        (
        <include refid="columns"/>
        )
        values
        (
        <include refid="values"/>
        )
        ON DUPLICATE KEY UPDATE
        screen_picture_guid=values(screen_picture_guid),oss_url=values(oss_url),store_guid=values(store_guid),
        store_name=values(store_name),weight=values(weight),height=values(height),px_type=values(px_type),pic_type=values(pic_type)
    </insert>
    <resultMap id="screenMap" type="com.holderzone.saas.store.business.entity.domain.ScreenPictureDO">
        <result column="sp_oss_url" property="ossUrl"/>
        <result column="spt_store_pic_change_time" property="changeMills"/>
        <result column="sp_screen_picture_guid" property="screenPictureGuid"/>
        <result column="sp_pic_type" property="picType"/>
        <result column="sp_px_type" property="pxType"/>
        <result column="sp_weight" property="weight"/>
        <result column="sp_height" property="height"/>
        <result column="sp_store_guid" property="storeGuid"/>
        <result column="sp_store_name" property="storeName"/>
        <result column="sp_gmt_create" property="createTime"/>
        <result column="sp_gmt_modified" property="updateTime"/>
    </resultMap>


    <select id="queryByAndroid" parameterType="string" resultMap="screenMap">
        select
        sp.screen_picture_guid as sp_screen_picture_guid,sp.oss_url as sp_oss_url,sp.store_guid sp_store_guid,
        sp.store_name as sp_store_name,sp.weight as sp_weight,sp.height as sp_height,sp.px_type as sp_px_type,
        sp.pic_type as sp_pic_type,sp.gmt_create as sp_gmt_create,sp.gmt_modified as sp_gmt_modified,
        spt.store_pic_change_time as spt_store_pic_change_time
        from hsb_screen_picture sp left join hsb_store_pic_time spt on sp.store_guid = spt.store_guid and sp.pic_type =
        spt.pic_type
        where sp.store_guid = #{storeGuid}
    </select>

    <select id="queryByWeb" parameterType="com.holderzone.saas.store.dto.business.manage.ScreenPicQuery"
            resultMap="screenMap">
        select
        sp.screen_picture_guid as sp_screen_picture_guid,sp.oss_url as sp_oss_url,sp.store_guid sp_store_guid,
        sp.store_name as sp_store_name,sp.weight as sp_weight,sp.height as sp_height,sp.px_type as sp_px_type,
        sp.pic_type as sp_pic_type,sp.gmt_create as sp_gmt_create,sp.gmt_modified as sp_gmt_modified,
        spt.store_pic_change_time as spt_store_pic_change_time
        from hsb_screen_picture sp left join hsb_store_pic_time spt on sp.store_guid = spt.store_guid and sp.pic_type =
        spt.pic_type
        <where>
            sp.store_guid = #{selectStoreGuid}
            <if test="null != picType">
                and sp.pic_type = #{picType}
            </if>
        </where>
    </select>

    <select id="countPics" resultType="java.lang.Integer">
        select count(*) from hsb_screen_picture where store_guid = #{storeGuid} and pic_type = #{picType}
     </select>

    <delete id="delete" parameterType="string">
        delete from hsb_screen_picture where screen_picture_guid = #{screenPictureGuid}
    </delete>

    <select id="queryOneByGuid" parameterType="string" resultMap="screenMap">
        select
        <include refid="columns"/>,gmt_create,gmt_modified
        from hsb_screen_picture where screen_picture_guid=#{screenPicGuid}
    </select>

    <insert id="insertPicTime" parameterType="com.holderzone.saas.store.business.entity.domain.ScreenPicTimeDO">
        insert  into hsb_store_pic_time
        (pic_time_guid,store_pic_change_time,pic_type,store_guid,store_name)
        values
        (#{picTimeGuid},#{changeMills},#{picType},#{storeGuid},#{storeName});
    </insert>

    <update id="updatePicTime" parameterType="com.holderzone.saas.store.business.entity.domain.ScreenPicTimeDO">
        update hsb_store_pic_time
        <set>
            <trim suffixOverrides=",">
                <if test="null!=changeMills">
                    store_pic_change_time = #{changeMills},
                </if>
                <if test="null!=picType">
                    pic_type = #{picType},
                </if>
            </trim>
        </set>
        where pic_time_guid = #{picTimeGuid}
    </update>

    <resultMap id="timeMap" type="com.holderzone.saas.store.business.entity.domain.ScreenPicTimeDO">
        <result column="pic_time_guid" property="picTimeGuid"/>
        <result column="store_pic_change_time" property="changeMills"/>
        <result column="pic_type" property="picType"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="store_name" property="storeName"/>
    </resultMap>
    <select id="queryPicTime" parameterType="com.holderzone.saas.store.dto.business.manage.ScreenPicTimeDTO"
            resultMap="timeMap">
        select
         pic_time_guid,store_pic_change_time,pic_type,store_guid,store_name
         from hsb_store_pic_time where store_guid = #{selectStoreGuid} and pic_type = #{picType}
    </select>

    <select id="getOssUrl" parameterType="string" resultType="java.lang.String">

        select oss_url from hsb_screen_picture where screen_picture_guid = #{screenPictureGuid}

    </select>

</mapper>