<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.PricePlanMapper">

    <select id="existSameName" resultType="java.lang.Boolean">
        SELECT COUNT(1)
        FROM hsi_price_plan
        WHERE brand_guid = #{req.brandGuid}
          AND name = #{req.name}
          AND is_delete = 0
    </select>

    <select id="planList" resultMap="MapPlanList">
        SELECT guid, brand_guid, name, status, description,
        item_num,
        store_num,
        start_time,
        end_time,
        effective_time,
        instantly_effective_time,
        sell_time_type,
        plan_code,
        update_time
        FROM hsi_price_plan
        WHERE brand_guid = #{req.brandGuid}
        <if test="req.nameOrGuid != null and req.nameOrGuid != ''">
            AND (name LIKE CONCAT('%', #{req.nameOrGuid}, '%') or plan_code LIKE CONCAT('%', #{req.nameOrGuid}, '%'))
        </if>
        <if test="req.status != null">
            AND status = #{req.status}
        </if>
        <if test="req.filterGuids != null and req.filterGuids.size() > 0">
            AND guid NOT IN
            <foreach collection="req.filterGuids" index="index" item="guid" open="(" separator="," close=")">
                #{guid}
            </foreach>
        </if>
        <if test="req.includeGuids != null and req.includeGuids.size() > 0">
            AND guid IN
            <foreach collection="req.includeGuids" index="index" item="guid" open="(" separator="," close=")">
                #{guid}
            </foreach>
        </if>
        AND is_delete = 0
        ORDER BY id DESC
    </select>

    <resultMap id="MapPlanList" type="com.holderzone.saas.store.dto.item.resp.PricePlanRespDTO">
        <result property="guid" column="guid"/>
        <result property="brandGuid" column="brand_guid"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="itemNum" column="item_num"/>
        <result property="storeNum" column="store_num"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="effectiveTime" column="effective_time"/>
        <result property="instantlyEffectiveTime" column="instantly_effective_time"/>
        <result property="sellTimeType" column="sell_time_type"/>
        <result property="planCode" column="plan_code"/>
        <result property="updateTime" column="update_time"/>
        <association property="storeList" column="guid" select="ListPlanPriceStore"/>

    </resultMap>

    <select id="ListPlanPriceStore" resultType="com.holderzone.saas.store.dto.item.common.PricePlanStoreBaseDTO">
        SELECT store_guid AS storeGuid FROM hsi_price_plan_store
        WHERE plan_guid = #{guid} AND is_delete = 0
    </select>

    <select id="planStateList" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanRespDTO">
        SELECT guid,
               brand_guid,
               name,
               status,
               description,
               item_num                 as itemNum,
               store_num                AS storeNum,
               start_time               AS startTime,
               end_time                 AS endTime,
               effective_time           AS effectiveTime,
               instantly_effective_time AS instantlyEffectiveTime,
               sell_time_type           AS sellTimeType,
               push_type                as pushType,
               plan_code                AS planCode
        FROM hsi_price_plan
        WHERE brand_guid = #{req.brandGuid}
          AND status in (1, 4)
          AND is_delete = 0
        ORDER BY id DESC
    </select>

    <update id="updateItemNum">
        UPDATE hsi_price_plan SET
        <if test="type == 1">
            item_num = item_num + #{itemNum}
        </if>
        <if test="type == 2">
            item_num = item_num - #{itemNum}
        </if>
        <if test="type == 3">
            item_Num = #{itemNum}
        </if>
        , gmt_modified = now()
        WHERE guid = #{planGuid}
    </update>

    <update id="batchUpdateItemNum">
        UPDATE hsi_price_plan SET
            <if test="type == 1">
                item_num = item_num + #{itemNum}
            </if>
            <if test="type == 2">
                item_num = item_num - #{itemNum}
            </if>
            <if test="type == 3">
                item_Num = #{itemNum}
            </if>
        WHERE guid in
        <foreach collection="planGuidList" item="planGuid" open="(" close=")" separator=",">
            #{planGuid}
        </foreach>
    </update>


    <select id="existSellTimeType" resultType="java.lang.Boolean">
        SELECT COUNT(1)
        FROM hsi_price_plan
        WHERE brand_guid = #{req.brandGuid}
          AND sell_time_type = #{req.sellTimeType}
          AND is_delete = 0
          AND parent_guid IS NULL
    </select>
    <select id="findIsSoldOutStores" resultType="java.lang.String">
        SELECT DISTINCT
        ps.store_guid
        FROM
        hsi_price_plan_store ps
        LEFT JOIN hsi_price_plan pp ON ps.plan_guid = pp.guid
        LEFT JOIN hsi_price_plan_item pi ON pi.plan_guid = pp.guid
        <where>
            ps.is_delete=0
            and pp.is_delete=0
            and pp.status=1
            and pp.effective_time &lt; #{nowDateTime}
            and pi.sku_guid in
            <foreach collection="skuGuids" item="skuGuid" open="(" separator="," close=")">
                #{skuGuid}
            </foreach>
            AND pp.parent_guid IS NULL
        </where>
    </select>

    <select id="listByPlanGuidList" resultType="com.holderzone.saas.store.item.entity.domain.PricePlanDO">
        select * from hsi_price_plan where guid in
        <foreach collection="planGuidList" item="planGuid" open="(" separator="," close=")">
            #{planGuid}
        </foreach>
    </select>

    <select id="queryDefaultPlan"
            resultType="com.holderzone.saas.store.item.entity.domain.PricePlanDO">
        SELECT
            p.guid,
            p.brand_guid,
            p.`name`,
            p.description,
            p.`status`,
            p.item_num,
            p.store_num,
            p.push_type,
            p.push_date,
            p.gmt_create,
            p.gmt_modified,
            p.is_delete,
            p.plan_code,
            p.start_time,
            p.end_time,
            p.effective_time,
            p.instantly_effective_time,
            p.sell_time_type,
            p.parent_guid,
            p.update_time
        FROM
            `hsi_price_plan` p
            LEFT JOIN hsi_price_plan_store ps ON ps.plan_guid = p.guid
        WHERE
            p.sell_time_type = 0
            AND p.`status` = 1
            AND effective_time &lt;= NOW()
            AND ps.store_guid = #{storeGuid}
            AND p.brand_guid = #{brandGuid}
            AND p.is_delete = 0
        limit 1
    </select>

    <update id="updateStoreNum">
        UPDATE hsi_price_plan SET
        <if test="type == 1">
            store_num = store_num + #{storeNum}
        </if>
        <if test="type == 2">
            store_num = store_num - #{storeNum}
        </if>
        <if test="type == 3">
            store_num = #{storeNum}
        </if>
        WHERE guid = #{planGuid}
    </update>

    <update id="updateStatus">
        UPDATE hsi_price_plan SET status = #{status}
        where guid = #{guid}
    </update>
</mapper>