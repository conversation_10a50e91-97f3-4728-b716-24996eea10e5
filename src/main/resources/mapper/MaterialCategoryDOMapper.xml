<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.MaterialCategoryDOMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.erp.entity.domain.MaterialCategoryDO">
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="enterprise_guid" jdbcType="VARCHAR" property="enterpriseGuid" />
    <result column="store_guid" jdbcType="VARCHAR" property="storeGuid" />
    <result column="warehouse_guid" jdbcType="VARCHAR" property="warehouseGuid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modified" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    guid, enterprise_guid, store_guid, warehouse_guid, name, deleted, gmt_create, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.holderzone.erp.entity.domain.MaterialCategoryDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from hse_material_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.holderzone.erp.entity.domain.MaterialCategoryDOExample">
    delete from hse_material_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.holderzone.erp.entity.domain.MaterialCategoryDO">
    insert into hse_material_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        guid,
      </if>
      <if test="enterpriseGuid != null">
        enterprise_guid,
      </if>
      <if test="storeGuid != null">
        store_guid,
      </if>
      <if test="warehouseGuid != null">
        warehouse_guid,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseGuid != null">
        #{enterpriseGuid,jdbcType=VARCHAR},
      </if>
      <if test="storeGuid != null">
        #{storeGuid,jdbcType=VARCHAR},
      </if>
      <if test="warehouseGuid != null">
        #{warehouseGuid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate},
      </if>
      <if test="gmtModified != null">
        #{gmtModified},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.holderzone.erp.entity.domain.MaterialCategoryDO" resultType="java.lang.Long">
    select count(*) from hse_material_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <select id="listByGuidList" resultType="com.holderzone.saas.store.dto.erp.CategoryDTO">
        select
            guid as categoryGuid,
            name as categoryName
        from
            hse_material_category
        <where>
            deleted = 0
            and guid in
            <foreach collection="dto.list" item="guid" open="(" separator="," close=")">
                #{guid,jdbcType=VARCHAR}
            </foreach>
        </where>
    </select>
  <update id="updateByExampleSelective" parameterType="map">
    update hse_material_category
    <set>
      <if test="record.guid != null">
        guid = #{record.guid,jdbcType=VARCHAR},
      </if>
      <if test="record.enterpriseGuid != null">
        enterprise_guid = #{record.enterpriseGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.storeGuid != null">
        store_guid = #{record.storeGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseGuid != null">
        warehouse_guid = #{record.warehouseGuid,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>