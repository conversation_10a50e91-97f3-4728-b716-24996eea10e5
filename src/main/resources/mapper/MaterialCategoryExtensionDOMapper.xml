<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.MaterialCategoryDOMapper">
    <resultMap id="QueryResultMap" type="com.holderzone.erp.entity.domain.CategoryDO">
        <result column="guid" jdbcType="VARCHAR" property="categoryGuid"/>
        <result column="name" jdbcType="VARCHAR" property="categoryName"/>
        <result column="gmt_create" property="gmtCreate" />
        <collection property="materialDOList" ofType="com.holderzone.erp.entity.domain.MaterialDO">
            <result column="materialName" property="name"/>
            <result column="materialGuid" property="guid"/>
            <result column="unit" property="unit"/>
            <result column="auxiliary_unit" property="auxiliaryUnit"/>
            <result column="auxiliaryUnitName" property="auxiliaryUnitName"/>
            <result column="unitName" property="unitName"/>
            <result column="materialGmtTime" property="gmtCreate" />
        </collection>
    </resultMap>
    <select id="getMaterialCategoryListAll" resultMap="QueryResultMap">
        SELECT
        '' as guid,
        '默认' as name,
        m.`name` AS materialName,
        m.guid AS materialGuid,
        m.unit,
        m.auxiliary_unit,
        m.gmt_create as materialGmtTime,
        ( SELECT u.`name` from hse_material_unit u where u.guid = m.unit ) unitName,
        ( SELECT u.`name` FROM hse_material_unit u where u.guid = m.auxiliary_unit ) auxiliaryUnitName,
        null as gmt_create
        FROM
        hse_material m
        WHERE
        m.category =''
        and m.deleted = 0 and m.enabled = 1
        <if test="storeGuid != null">
            and m.`store_guid`=#{storeGuid}
        </if>
        <if test="searchName != null and searchName != ''">
            and locate(#{searchName}, m.`name`)
        </if>
        union
        SELECT
        c.guid,
        c.`name`,
        m.`name` AS materialName,
        m.guid AS materialGuid,
        m.unit,
        m.auxiliary_unit,
        m.gmt_create as materialGmtTime,
        ( SELECT u.`name` from hse_material_unit u where u.guid = m.unit ) unitName,
        ( SELECT u.`name` FROM hse_material_unit u where u.guid = m.auxiliary_unit ) auxiliaryUnitName,
        c.gmt_create
        FROM
        hse_material_category c
        inner JOIN hse_material m ON m.category = c.guid
        <where>
            m.deleted = 0 and m.enabled = 1
            <if test="storeGuid != null">
                and m.`store_guid`=#{storeGuid}
            </if>
            <!--<if test="storeGuid != null and storeGuid != ''">
                and m.`warehouse_guid`=#{warehouseGuid}
            </if>-->
            <if test="searchName != null and searchName != ''">
                and locate(#{searchName}, m.`name`)
            </if>
        </where>
    </select>
</mapper>