<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.GrouponMapper">


    <select id="queryOrderByCode" resultType="com.holderzone.saas.store.dto.trade.GrouponOrderDTO">
        SELECT
            o.guid as "order_guid",
            o.order_no,
            g.code as "coupon_code",
            g.receipt_channel,
            g.maiton_consume_info
        FROM
            hst_groupon g
        JOIN hst_order o on o.guid = g.order_guid and o.recovery_type in (1, 3)
        where
            g.code = #{code}
    </select>

    <select id="useGrouponTotalAmountByOrderGuids"
            resultType="com.holderzone.saas.store.dto.trade.GrouponOrderDTO">
        SELECT
            d.order_guid,
            case when max(d.receipt_channel) = '1004' then 70 else d.groupon_type end as "groupon_type",
            IFNULL(sum(case when d.coupon_buy_price = 0 then d.amount else d.coupon_buy_price end),0) as "amount",
            max(d.receipt_channel) as "receipt_channel"
        FROM
            `hst_groupon` d
        WHERE
            d.is_delete = '0'
            AND d.refund_order_guid is null
        <if test="orderGuids != null and orderGuids.size()>0">
            and d.order_guid IN
            <foreach collection="orderGuids" item="orderGuid" open="(" separator="," close=")">
                #{orderGuid}
            </foreach>
        </if>
        group by d.order_guid, case when d.receipt_channel = '1004' then 70 else d.groupon_type end
    </select>

    <select id="handover" resultType="com.holderzone.saas.store.trade.entity.domain.GrouponDO">
        SELECT
            a.*
        FROM
            hst_groupon AS a
        left join hst_order o on o.guid = a.order_guid and o.is_delete = 0
        <where>
            a.is_delete = 0 and a.third_code is not null and o.state = 4 and a.refund_order_guid is null
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                and a.store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                and o.checkout_time &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                and o.checkout_time &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and a.staff_guid = #{dto.userGuid}
            </if>
        </where>
    </select>

    <select id="listByRequest" resultType="com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO">
        SELECT
        g.`groupon_type` code,
        SUM(g.`deduction_amount` - g.`coupon_buy_price`) discountAmount,
        SUM(g.`coupon_buy_price`) amount
        FROM `hst_groupon` g
        LEFT JOIN `hst_order` o ON o.`guid`=g.`order_guid`
        WHERE o.`checkout_time` BETWEEN CONCAT(#{request.beginTime},' 00:00:00') AND CONCAT(#{request.endTime},' 23:59:59')
        AND g.`is_delete`=0
        AND o.`state` =4
        AND o.`is_delete`=0
        <if test="request.isMember != null and request.isMember == 1">
            AND o.`member_consumption_guid`!='0'
        </if>
        AND g.`refund_order_guid` is null
        <if test="request.storeGuid != null and request.storeGuid.trim() != ''" >
            AND o.store_guid =  #{request.storeGuid}
        </if>
        <if test="request.storeGuids != null and request.storeGuids.size() > 0">
            AND o.store_guid in
            <foreach collection="request.storeGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.checkoutStaffGuids != null and request.checkoutStaffGuids.size() > 0">
            AND o.checkout_staff_guid in
            <foreach collection="request.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
        GROUP BY g.`groupon_type`
    </select>

    <select id="listByRequestGroupByName" resultType="com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO">
        SELECT
        case when g.receipt_channel = '1004' then 70 else g.`groupon_type` end as code,
        g.`name` name,
        SUM(g.`deduction_amount` - g.`coupon_buy_price`) discountAmount,
        SUM(g.`coupon_buy_price`) amount,
        COUNT(DISTINCT o.guid) order_count,
        COUNT(DISTINCT g.guid) groupon_count,
        SUM(CASE WHEN g.`deduction_amount` - g.`coupon_buy_price` > 0 THEN 1 ELSE 0 END) discount_order_count
        FROM `hst_groupon` g
        LEFT JOIN `hst_order` o ON o.`guid`=g.`order_guid`
        WHERE o.`checkout_time` BETWEEN CONCAT(#{request.beginTime},' 00:00:00') AND CONCAT(#{request.endTime},' 23:59:59')
        AND g.`is_delete`=0
        AND o.`state` =4
        AND o.`is_delete`=0
        <if test="request.isMember != null and request.isMember == 1">
            AND o.`member_consumption_guid`!='0'
        </if>
        AND g.`refund_order_guid` is null
        <if test="request.storeGuid != null and request.storeGuid.trim() != ''" >
            AND o.store_guid =  #{request.storeGuid}
        </if>
        <if test="request.storeGuids != null and request.storeGuids.size() > 0">
            AND o.store_guid in
            <foreach collection="request.storeGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.checkoutStaffGuids != null and request.checkoutStaffGuids.size() > 0">
            AND o.checkout_staff_guid in
            <foreach collection="request.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
        GROUP BY case when g.receipt_channel = '1004' then 70 else g.`groupon_type` end,
        g.`name`;
    </select>

    <select id="listRefundByRequest" resultType="com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO">
        SELECT
        g.`groupon_type` code,
        g.store_guid name,
        SUM(g.`deduction_amount` - g.`coupon_buy_price`) discountAmount,
        SUM(g.`coupon_buy_price`) amount
        FROM `hst_groupon` g
        LEFT JOIN `hst_order` o ON o.`guid`=g.`order_guid`
        WHERE o.`checkout_time` BETWEEN #{request.businessStartDateTime} AND #{request.businessEndDateTime}
        <if test="request.storeGuids != null and request.storeGuids.size()>0">
            AND g.store_guid IN
            <foreach collection="request.storeGuids" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
        AND g.`is_delete`=0
        AND o.`state` =4
        AND o.`is_delete`=0
        AND g.`refund_order_guid` is not null
        GROUP BY g.`groupon_type`,g.store_guid
    </select>

    <update id="updateCouponBuyPriceByCode">
        update
            hst_groupon
        set
            coupon_buy_price = #{couponBuyPrice}
        where
            groupon_type = #{grouponType} and code = #{code}
    </update>

</mapper>
