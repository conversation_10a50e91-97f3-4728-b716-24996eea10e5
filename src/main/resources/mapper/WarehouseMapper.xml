<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.WarehouseMapper">

    <sql id="baseResult">
        `guid`,`name`,`code`,`addr`,`pic`,`tel`,`remark`,`type`,`status`,`deleted`,
        `enabled`,`foreign_key`,`enterprise_guid`,`gmt_create`,`gmt_modified`
    </sql>

    <resultMap id="WarehouseMap" type="com.holderzone.erp.entity.domain.WarehouseDO">
        <result column="guid" property="guid"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="addr" property="addr"/>
        <result column="pic" property="pic"/>
        <result column="tel" property="tel"/>
        <result column="remark" property="remark"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="deleted" property="deleted"/>
        <result column="enabled" property="enabled"/>
        <result column="foreign_key" property="foreignKey"/>
        <result column="enterprise_guid" property="enterpriseGuid"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <insert id="createWarehouse" parameterType="com.holderzone.erp.entity.domain.WarehouseDO">
        insert into hse_warehouse
        (
        <trim suffixOverrides=",">
            `guid`,
            `name`,
            `type`,
            <if test="enterpriseGuid != null and enterpriseGuid != ''">
                `enterprise_guid`,
            </if>
            <if test="code != null and code != ''">
                `code`,
            </if>
            <if test="addr != null and addr != ''">
                `addr`,
            </if>
            <if test="pic != null and pic != ''">
                `pic`,
            </if>
            <if test="tel != null and tel != ''">
                `tel`,
            </if>
            <if test="remark != null and remark != ''">
                `remark`,
            </if>
            <if test="foreignKey != null and foreignKey != ''">
                `foreign_key`,
            </if>
        </trim>
        ) values
        (
        <trim suffixOverrides=",">
            #{guid},
            #{name},
            #{type},
            <if test="enterpriseGuid != null and enterpriseGuid != ''">
                #{enterpriseGuid},
            </if>
            <if test="code != null and code != ''">
                #{code},
            </if>
            <if test="addr != null and addr != ''">
                #{addr},
            </if>
            <if test="pic != null and pic != ''">
                #{pic},
            </if>
            <if test="tel != null and tel != ''">
                #{tel},
            </if>
            <if test="remark != null and remark != ''">
                #{remark},
            </if>
            <if test="foreignKey != null and foreignKey != ''">
                #{foreignKey},
            </if>
        </trim>
        )
    </insert>

    <update id="updateWarehouse">
        update hse_warehouse
        set
        <trim suffixOverrides=",">
            `name` = #{name},
            `code` = #{code},
            `addr` = #{addr},
            `pic` = #{pic},
            `tel` = #{tel},
            `remark` = #{remark},
        </trim>
        where guid = #{guid}
    </update>

    <update id="enableOrDisableWarehouse" parameterType="java.lang.String">
        update hse_warehouse
        set enabled = if(enabled = 1, 0, 1)
        where guid = #{guid}
    </update>

    <update id="lockOrUnlockWarehouse" parameterType="java.lang.String">
        update hse_warehouse
        set status = if(status = 1, 0, 1)
        where guid = #{guid}
    </update>

    <update id="deleteWarehouse" parameterType="java.lang.String">
        update hse_warehouse
        set deleted = 1
        where guid = #{guid}
    </update>

    <update id="updateStoreWarehouse">
        update hse_warehouse
        set `name` = #{name}
        where `foreign_key` = #{guid}
    </update>

    <select id="getWarehouseByGuid" resultMap="WarehouseMap">
        select
        <include refid="baseResult"/>
        from hse_warehouse
        where guid = #{guid}
    </select>

    <select id="getWarehouseList" resultMap="WarehouseMap" parameterType="com.holderzone.erp.entity.domain.WarehouseQueryDO">
        select
        <include refid="baseResult"/>
        from hse_warehouse
        <where>
            deleted = 0 and `type` = 0
            <if test="searchConditions != null and searchConditions != ''">
                and (
                    LOCATE(#{searchConditions},`name`)
                    or LOCATE(#{searchConditions},`code`)
                )
            </if>
        </where>
        ORDER by gmt_create desc
        limit #{start},#{pageSize}
    </select>

    <select id="getWarehouseListTotal" resultType="java.lang.Long" parameterType="com.holderzone.erp.entity.domain.WarehouseQueryDO">
        select
        count(*)
        from hse_warehouse
        <where>
            deleted = 0 and `type` = 0
            <if test="searchConditions != null and searchConditions != ''">
                and (
                LOCATE(#{searchConditions},`name`)
                or LOCATE(#{searchConditions},`code`)
                )
            </if>
        </where>
    </select>

    <select id="getWarehouseListByName" resultMap="WarehouseMap" parameterType="com.holderzone.erp.entity.domain.WarehouseQueryDO">
        select
        <include refid="baseResult"/>
        from hse_warehouse
        <where>
            deleted = 0 and enabled = 1
            <if test="searchConditions != null and searchConditions != ''">
                and LOCATE(#{searchConditions},`name`)
            </if>
        </where>
        ORDER by gmt_create desc
    </select>

    <select id="getMaximumCode" resultType="java.lang.String">
        select
        `code`
        from hse_warehouse
        order by gmt_create desc
        limit 1
    </select>

    <select id="checkCodeRepeat" resultType="java.lang.Integer">
        select
        count(*)
        from hse_warehouse
        where code = #{code}
    </select>

    <select id="getWarehouseByStoreGuid" resultMap="WarehouseMap" parameterType="java.lang.String">
        select
        <include refid="baseResult"/>
        from hse_warehouse
        where
        `type` = 1
        and `deleted` = 0
        and `enabled` = 1
        and `foreign_key` = #{storeGuid}
    </select>

    <select id="checkNameRepeat" resultType="java.lang.Integer">
        select
        count(*)
        from hse_warehouse
        where
        `name` = #{name} and deleted = 0
        <if test="guid != null and guid != ''">
            and guid &lt;> #{guid}
        </if>
    </select>

</mapper>