<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.InOutDocumentMapper">

    <resultMap id="statusAndInOutType" type="com.holderzone.erp.entity.domain.InOutDocumentDO">
        <result column="guid" property="guid"/>
        <result column="status" property="status"/>
        <result column="in_out_type" property="inOutType"/>
    </resultMap>

    <resultMap id="baseResultMap" type="com.holderzone.erp.entity.domain.InOutDocumentDO">
        <result column="guid" property="guid"/>
        <result column="supplier_guid" property="supplierGuid"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="contact_document_guid" property="contactDocumentGuid"/>
        <result column="code" property="code"/>
        <result column="warehouse_guid" property="warehouseGuid"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="document_date" property="documentDate"/>
        <result column="type" property="type"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="should_pay_amount" property="shouldPayAmount"/>
        <result column="operator_guid" property="operatorGuid"/>
        <result column="operator_name" property="operatorName"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="in_out_type" property="inOutType"/>
        <result column="settle_status" property="settleStatus"/>
        <result column="store_guid" property="storeGuid" />
    </resultMap>

    <resultMap id="documentAndDetailMap" type="com.holderzone.erp.entity.domain.InOutDocumentDO">
        <result column="guid" property="guid"/>
        <result column="gmt_create" property="gmtCreate" />
        <result column="supplier_guid" property="supplierGuid"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="contact_document_guid" property="contactDocumentGuid"/>
        <result column="code" property="code"/>
        <result column="warehouse_guid" property="warehouseGuid"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="document_date" property="documentDate"/>
        <result column="type" property="type"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="should_pay_amount" property="shouldPayAmount"/>
        <result column="operator_guid" property="operatorGuid"/>
        <result column="operator_name" property="operatorName"/>
        <result column="create_staff_guid" property="createStaffGuid"/>
        <result column="create_staff_name" property="createStaffName" />
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="in_out_type" property="inOutType"/>
        <result column="settle_status" property="settleStatus"/>
        <result column="store_guid" property="storeGuid" />
        <collection property="detailList" ofType="com.holderzone.erp.entity.domain.InOutDocumentDetailDO">
            <result column="detail_guid" property="guid" />
            <result column="document_guid" property="documentGuid" />
            <result column="material_guid" property="materialGuid" />
            <result column="material_code" property="materialCode" />
            <result column="material_name" property="materialName" />
            <result column="stock" property="stock" />
            <result column="unit_guid" property="unitGuid" />
            <result column="unit_name" property="unitName" />
            <result column="unit_price" property="unitPrice" />
            <result column="count" property="count" />
            <result column="material_total_amount" property="totalAmount" />
            <result column="return_count" property="returnCount" />
        </collection>
    </resultMap>

    <insert id="insertInOutDocument" parameterType="com.holderzone.erp.entity.domain.InOutDocumentDO">
          insert into hse_warehouse_in_out_document
          (guid,supplier_guid,supplier_name,contact_document_guid,code,warehouse_guid,warehouse_name,document_date,`type`,
          total_amount,should_pay_amount,operator_guid,operator_name,remark,in_out_type,store_guid,
          create_staff_guid,create_staff_name)
          values
          (#{guid},#{supplierGuid},#{supplierName},#{contactDocumentGuid},#{code},#{warehouseGuid},#{warehouseName},#{documentDate},
          #{type},#{totalAmount},#{shouldPayAmount},#{operatorGuid},#{operatorName},#{remark},#{inOutType},#{storeGuid},
          #{createStaffGuid},#{createStaffName})
    </insert>
    <update id="submitInOutDocument" parameterType="string">
          UPDATE hse_warehouse_in_out_document SET `status` = 1
          WHERE guid = #{documentGuid};
    </update>

    <delete id="deleteInOutDocumentAndDetail" parameterType="string">
        DELETE master_doc,detail FROM hse_warehouse_in_out_document master_doc
        INNER JOIN hse_warehouse_in_out_document_detail detail ON master_doc.guid = detail.document_guid
        WHERE master_doc.guid = #{inOutDocumentGuid} and master_doc.status = 0
    </delete>

    <select id="inDocumentCount" resultType="java.lang.Integer" parameterType="string">
        SELECT COUNT(*) FROM hse_warehouse_in_out_document
        WHERE guid = #{contactDocumentGuid} AND in_out_type = 0;
    </select>

    <select id="selectWarehouseGuid" resultType="java.lang.String" parameterType="string">
        SELECT warehouse_guid FROM hse_warehouse_in_out_document WHERE guid = #{documentGuid};
    </select>

    <select id="selectDocumentStatusAndInOutType" parameterType="string" resultMap="statusAndInOutType">
        SELECT guid,`status`,in_out_type FROM hse_warehouse_in_out_document
        WHERE guid = #{documentGuid}
    </select>
    <select id="selectInOutDocumentStatus" resultMap="statusAndInOutType" parameterType="string">
        SELECT guid,`status` FROM hse_warehouse_in_out_document WHERE guid = #{contactDocumentGuid}
    </select>
    <select id="selectDocumentType" resultMap="baseResultMap">
        SELECT guid,contact_document_guid,`type` FROM hse_warehouse_in_out_document
        WHERE guid = #{documentGuid}
    </select>

    <select id="selectDocumentGuidList" resultType="java.lang.String" parameterType="com.holderzone.erp.entity.domain.InOutContactDocumentQuery">
        select guid
        FROM hse_warehouse_in_out_document
        where in_out_type = 0 and status = 1
        and warehouse_guid = #{warehouseGuid}
        <if test="supplierGuid != null and supplierGuid != ''">
            and supplier_guid = #{supplierGuid}
        </if>
        and guid like #{documentGuid}"%"
        limit 0,10
    </select>

    <select id="selectDocument" resultMap="baseResultMap" parameterType="string">
        select guid,supplier_guid,supplier_name,contact_document_guid,code,warehouse_guid,warehouse_name,store_guid,
          document_date,`type`,total_amount,should_pay_amount,operator_guid,operator_name,remark,status,in_out_type
        from hse_warehouse_in_out_document
        where guid = #{documentGuid}
    </select>
    <select id="selectDocumentAndDetail" parameterType="string" resultMap="documentAndDetailMap">
        SELECT doc.guid,doc.gmt_create,doc.supplier_guid,doc.supplier_name,doc.contact_document_guid ,
        doc.`code`,doc.warehouse_guid,doc.warehouse_name,doc.store_guid,doc.document_date,doc.create_staff_name,doc.type,
        doc.total_amount,doc.should_pay_amount,doc.operator_guid,doc.operator_name,doc.remark,doc.status,doc.in_out_type,
        doc.create_staff_guid,doc.create_staff_name,
        detail.guid as detail_guid,detail.document_guid ,detail.material_guid,detail.material_code,detail.material_name,detail.stock,
        detail.count,detail.unit_guid,detail.unit_name,detail.unit_price,detail.total_amount AS material_total_amount,detail.return_count
        FROM
        hse_warehouse_in_out_document doc
        INNER JOIN hse_warehouse_in_out_document_detail detail ON doc.guid = detail.document_guid
        WHERE doc.guid = #{documentGuid}
    </select>
    <select id="selectContactDocumentGuidList" resultType="java.lang.String" parameterType="string">
        select guid
        FROM hse_warehouse_in_out_document
        where contact_document_guid = #{documentGuid}
    </select>

    <sql id="selectDocumentListCriteria">
        <if test="warehouseGuidList != null and warehouseGuidList.size() > 0">
            AND warehouse_guid in
            <foreach collection="warehouseGuidList" open="(" close=")" separator="," item="warehouseGuid">
                #{warehouseGuid}
            </foreach>
        </if>
        <if test="type != null">
            AND `type` = #{type}
        </if>
        <if test="status != null">
            AND `status` = #{status}
        </if>
        <if test="supplierGuid != null and supplierGuid != ''">
            AND supplier_guid = #{supplierGuid}
        </if>
        <if test="guid != null and guid != ''">
            AND guid LIKE #{guid}"%"
        </if>
        <if test="startDate != null">
            AND document_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND document_date &lt;= #{endDate}
        </if>
    </sql>

    <select id="selectDocumentListForPage" resultMap="baseResultMap"
            parameterType="com.holderzone.erp.entity.domain.InOutDocumentQuery">
        SELECT guid,warehouse_guid,warehouse_name,document_date,`type`,total_amount,should_pay_amount,`status`,store_guid,in_out_type
        FROM hse_warehouse_in_out_document
        WHERE in_out_type = #{inOutType}
        <include refid="selectDocumentListCriteria"/>
        order by status ASC,gmt_modified DESC
        limit #{offset},#{pageSize}
    </select>
    <select id="selectDocumentCount" resultType="java.lang.Long">
        select count(*)
        from hse_warehouse_in_out_document
        where in_out_type = #{inOutType}
        <include refid="selectDocumentListCriteria" />
    </select>


    <select id="selectSuppliersReconciliationTotal" resultType="java.lang.Long" parameterType="com.holderzone.erp.entity.domain.SuppliersReconciliationQueryDO">
        SELECT count(*)
        FROM `hse_warehouse_in_out_document`
        <include refid="queryConditionSql"/>
    </select>

    <select id="selectSuppliersReconciliationList" resultMap="baseResultMap" parameterType="com.holderzone.erp.entity.domain.SuppliersReconciliationQueryDO">
        SELECT
        `guid`,`supplier_guid`,`supplier_name`,`contact_document_guid`,`code`,`warehouse_guid`,
        `warehouse_name`,`document_date`,`type`,
        if(`type` = 0, `total_amount`, (0 - `total_amount`)) total_amount,
        if(`type` = 0, `should_pay_amount`, if(`should_pay_amount` is null, `should_pay_amount`, (0 - `should_pay_amount`))) should_pay_amount,
        `operator_guid`,`operator_name`,`status`,`settle_status`,`in_out_type`
        FROM `hse_warehouse_in_out_document`
        <include refid="queryConditionSql"/>
        order by settle_status asc,gmt_create desc
        limit #{start}, #{pageSize}
    </select>

    <select id="selectSuppliersReconciliationTotalAmount" resultType="java.math.BigDecimal">
        SELECT
        sum(if(settle_status = 1, 0, if(type = 0, if(should_pay_amount is null, total_amount, should_pay_amount), (0 - if(should_pay_amount is null, total_amount, should_pay_amount)))))
        FROM `hse_warehouse_in_out_document`
        <include refid="queryConditionSql"/>
    </select>
    <select id="selectStoreGuid" resultType="java.lang.String" parameterType="string">
      select store_guid from hse_warehouse_in_out_document where guid = #{documentGuid}
    </select>
    <select id="selectCountByWarehouseGuid" resultType="java.lang.Integer" parameterType="string">
        select count(*) from hse_warehouse_in_out_document where warehouse_guid = #{warehouseGuid}
    </select>
    <select id="selectCountBySupplierGuid" resultType="java.lang.Integer">
        select count(*) from hse_warehouse_in_out_document where supplier_guid = #{supplierGuid}
    </select>
    <select id="selectWarehouseGuidByDocumentGuid" resultType="java.lang.String" parameterType="string">
        select warehouse_guid from hse_warehouse_in_out_document where guid = #{documentGuid}
    </select>
    <select id="selectContactDocumentGuidListForInDocument" resultType="java.lang.String" parameterType="string">
      select guid
        FROM hse_warehouse_in_out_document
        where contact_document_guid = #{documentGuid}
    </select>
    <select id="selectStoreGuidAndWarehouseGuid" resultMap="baseResultMap">
        select store_guid , warehouse_guid from hse_warehouse_in_out_document where guid = #{documentGuid}
    </select>

    <select id="queryDocumentDetailList"
            resultType="com.holderzone.erp.entity.domain.InOutDocumentDO" resultMap="documentAndDetailMap">
        SELECT
            doc.guid,
            doc.gmt_create,
            doc.supplier_guid,
            doc.supplier_name,
            doc.contact_document_guid,
            doc.`code`,
            doc.warehouse_guid,
            doc.warehouse_name,
            doc.store_guid,
            doc.document_date,
            doc.create_staff_name,
            doc.type,
            doc.total_amount,
            doc.should_pay_amount,
            doc.operator_guid,
            doc.operator_name,
            doc.remark,
            doc.status,
            doc.in_out_type,
            doc.create_staff_guid,
            doc.create_staff_name,

            detail.guid as detail_guid,
            detail.document_guid,
            detail.material_guid,
            detail.material_code,
            detail.material_name,
            detail.stock,
            detail.count,
            detail.unit_guid,
            detail.unit_name,
            detail.unit_price,
            detail.total_amount AS material_total_amount,
            detail.return_count
        FROM
            hse_warehouse_in_out_document doc
            INNER JOIN hse_warehouse_in_out_document_detail detail ON doc.guid = detail.document_guid
        WHERE
            doc.guid IN
            <foreach collection="queryDTO.documentGuidList" index="index" item="documentGuid" open="(" separator=","
                     close=")">
                #{documentGuid}
            </foreach>
    </select>


    <sql id="queryConditionSql">
        <where>
            status = 1
            and warehouse_guid in (select guid from hse_warehouse hw where hw.foreign_key = #{guid})
            and (`type` = 0 or `type` = 11)
            <if test="supplierGuid != null and supplierGuid !=''">
                and supplier_guid = #{supplierGuid}
            </if>
            <if test="status != -1">
                and settle_status = #{status}
            </if>
            <if test="startDate != null">
                and UNIX_TIMESTAMP(document_date) >= #{startDate}/1000
            </if>
            <if test="endDate != null">
                and UNIX_TIMESTAMP(document_date) &lt;= #{endDate}/1000
            </if>
        </where>
    </sql>

    <update id="settleSuppliersReconciliation">
        update hse_warehouse_in_out_document
        set settle_status = 1
        where guid in
        <foreach collection="list" item="guid" open="(" separator="," close=")">
            #{guid}
        </foreach>
    </update>

</mapper>