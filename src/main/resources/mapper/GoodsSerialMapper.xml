<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.GoodsSerialMapper">

    <select id="queryGoodsSerial" resultType="com.holderzone.erp.entity.domain.GoodsSerialDO">
        SELECT * FROM hse_goods_serial
        <where>
            goods_guid = #{dto.goodsGuid}
            <if test="dto.invoiceType != 0">
                and invoice_type = #{dto.invoiceType}
            </if>
            <if test="dto.startDate != null and dto.startDate != ''">
                and gmt_create &gt;= #{dto.startDateTime}
            </if>
            <if test="dto.endDate != null and dto.endDate != ''">
                and gmt_create &lt;= #{dto.endDateTime}
            </if>
            and (change_num != 0 or (change_num = 0 and invoice_type = 6))
        </where>
        order by gmt_create DESC
    </select>
</mapper>
