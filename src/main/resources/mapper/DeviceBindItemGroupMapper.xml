<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.kds.mapper.DeviceBindItemGroupMapper">

    <select id="pageBindGroup" resultType="com.holderzone.saas.store.kds.entity.read.GroupBindItemReadDO">
        SELECT
            big.guid,
            big.NAME as "group_name"
        FROM
            hsk_bind_item_group big
            JOIN hsk_device_bind_item_group dbig ON big.guid = dbig.group_guid AND dbig.is_delete = 0
        <where>
            big.is_delete = 0
            <if test="request.deviceId != null and request.deviceId != ''">
               AND dbig.device_id = #{request.deviceId}
            </if>
            <if test="request.storeGuid != null and request.storeGuid != ''">
               AND dbig.store_guid = #{request.storeGuid}
            </if>
        </where>
    </select>



</mapper>
