<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.print.mapper.PrinterMapper">

    <resultMap id="BaseResultMap" type="com.holder.saas.print.entity.domain.PrinterDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="printer_type" jdbcType="CHAR" property="printerType"/>
        <result column="printer_name" jdbcType="VARCHAR" property="printerName"/>
        <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
        <result column="printer_port" jdbcType="CHAR" property="printerPort"/>
        <result column="business_type" jdbcType="CHAR" property="businessType"/>
        <result column="print_count" jdbcType="INTEGER" property="printCount"/>
        <result column="print_page" jdbcType="CHAR" property="printPage"/>
        <result column="print_cut" jdbcType="CHAR" property="printCut"/>
        <result column="store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="is_master" jdbcType="TINYINT" property="isMaster"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
    </resultMap>

    <resultMap id="ExtensionResultMap" type="com.holder.saas.print.entity.read.PrinterReadDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="printer_type" jdbcType="CHAR" property="printerType"/>
        <result column="printer_name" jdbcType="VARCHAR" property="printerName"/>
        <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
        <result column="printer_port" jdbcType="CHAR" property="printerPort"/>
        <result column="business_type" jdbcType="CHAR" property="businessType"/>
        <result column="print_count" jdbcType="INTEGER" property="printCount"/>
        <result column="print_page" jdbcType="CHAR" property="printPage"/>
        <result column="print_cut" jdbcType="CHAR" property="printCut"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="is_master" jdbcType="TINYINT" property="isMaster"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <collection property="arrayOfItemGuid" ofType="java.lang.String">
            <result column="item_guid"/>
        </collection>
    </resultMap>

    <resultMap id="MapWithEverything" type="com.holder.saas.print.entity.read.PrinterReadDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="printer_name" jdbcType="VARCHAR" property="printerName"/>
        <result column="business_type" jdbcType="CHAR" property="businessType"/>
        <result column="printer_type" jdbcType="CHAR" property="printerType"/>
        <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
        <result column="printer_port" jdbcType="CHAR" property="printerPort"/>
        <result column="print_count" jdbcType="INTEGER" property="printCount"/>
        <result column="print_page" jdbcType="CHAR" property="printPage"/>
        <result column="print_cut" jdbcType="CHAR" property="printCut"/>
        <result column="is_master" jdbcType="TINYINT" property="isMaster"/>
        <result column="is_print_hang_up" jdbcType="TINYINT" property="isPrintHangUp"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="device_key" jdbcType="VARCHAR" property="deviceKey"/>
        <result column="manufacturers_type" jdbcType="CHAR" property="manufacturersType"/>
        <result column="print_price_type" jdbcType="TINYINT" property="printPriceType"/>
        <result column="part_refund_print_flag" jdbcType="TINYINT" property="partRefundPrintFlag"/>
        <result column="area_type" jdbcType="TINYINT" property="areaType"/>
        <collection property="arrayOfInvoiceType" ofType="java.lang.Integer">
            <result column="invoice_type"/>
        </collection>
        <collection property="arrayOfInvoiceDO" ofType="com.holder.saas.print.entity.domain.PrinterInvoiceDO">
            <result column="invoice_type" jdbcType="TINYINT" property="invoiceType"/>
            <result column="invoice_name" jdbcType="VARCHAR" property="invoiceName"/>
            <result column="invoice_print_count" jdbcType="INTEGER" property="printCount"/>
        </collection>
        <collection property="arrayOfItemGuid" ofType="java.lang.String">
            <result column="item_guid"/>
        </collection>
        <collection property="arrayOfItemDO" ofType="com.holder.saas.print.entity.domain.PrinterItemDO">
            <result column="item_guid" jdbcType="VARCHAR" property="itemGuid"/>
            <result column="item_name" jdbcType="VARCHAR" property="itemName"/>
        </collection>
        <collection property="arrayOfAreaGuid" ofType="java.lang.String">
            <result column="area_guid"/>
        </collection>
        <collection property="arrayOfTableGuid" ofType="java.lang.String">
            <result column="table_guid"/>
        </collection>
        <collection property="arrayOfAreaDO" ofType="com.holder.saas.print.entity.domain.PrinterAreaDO">
            <result column="area_guid" jdbcType="VARCHAR" property="areaGuid"/>
            <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        </collection>
        <collection property="arrayOfTableDO" ofType="com.holder.saas.print.entity.domain.PrinterTableDO">
            <result column="table_guid" jdbcType="VARCHAR" property="tableGuid"/>
            <result column="table_name" jdbcType="VARCHAR" property="tableName"/>
        </collection>
    </resultMap>

    <resultMap id="MapWithItem" type="com.holder.saas.print.entity.read.PrinterReadDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="printer_name" jdbcType="VARCHAR" property="printerName"/>
        <result column="business_type" jdbcType="CHAR" property="businessType"/>
        <result column="printer_type" jdbcType="CHAR" property="printerType"/>
        <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
        <result column="printer_port" jdbcType="CHAR" property="printerPort"/>
        <result column="print_count" jdbcType="INTEGER" property="printCount"/>
        <result column="print_page" jdbcType="CHAR" property="printPage"/>
        <result column="print_cut" jdbcType="CHAR" property="printCut"/>
        <result column="is_master" jdbcType="TINYINT" property="isMaster"/>
        <result column="is_print_hang_up" jdbcType="TINYINT" property="isPrintHangUp"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="device_key" jdbcType="VARCHAR" property="deviceKey"/>
        <result column="manufacturers_type" jdbcType="CHAR" property="manufacturersType"/>
        <result column="print_price_type" jdbcType="TINYINT" property="printPriceType"/>
        <result column="area_type" jdbcType="TINYINT" property="areaType"/>
        <collection property="arrayOfItemGuid" ofType="java.lang.String">
            <result column="item_guid"/>
        </collection>
        <collection property="arrayOfItemDO" ofType="com.holder.saas.print.entity.domain.PrinterItemDO">
            <result column="item_guid" jdbcType="VARCHAR" property="itemGuid"/>
            <result column="item_name" jdbcType="VARCHAR" property="itemName"/>
        </collection>
    </resultMap>

    <resultMap id="MapWithInvoice" type="com.holder.saas.print.entity.read.PrinterReadDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="printer_name" jdbcType="VARCHAR" property="printerName"/>
        <result column="business_type" jdbcType="CHAR" property="businessType"/>
        <result column="printer_type" jdbcType="CHAR" property="printerType"/>
        <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
        <result column="printer_port" jdbcType="CHAR" property="printerPort"/>
        <result column="print_count" jdbcType="INTEGER" property="printCount"/>
        <result column="print_page" jdbcType="CHAR" property="printPage"/>
        <result column="print_cut" jdbcType="CHAR" property="printCut"/>
        <result column="is_master" jdbcType="TINYINT" property="isMaster"/>
        <result column="is_print_hang_up" jdbcType="TINYINT" property="isPrintHangUp"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="device_key" jdbcType="VARCHAR" property="deviceKey"/>
        <result column="manufacturers_type" jdbcType="CHAR" property="manufacturersType"/>
        <result column="device_model" jdbcType="VARCHAR" property="deviceModel"/>
        <result column="print_price_type" jdbcType="TINYINT" property="printPriceType"/>
        <result column="part_refund_print_flag" jdbcType="TINYINT" property="partRefundPrintFlag"/>
        <result column="area_type" jdbcType="TINYINT" property="areaType"/>
        <collection property="arrayOfInvoiceType" ofType="java.lang.Integer">
            <result column="invoice_type"/>
        </collection>
        <collection property="arrayOfInvoiceDO" ofType="com.holder.saas.print.entity.domain.PrinterInvoiceDO">
            <result column="invoice_type" jdbcType="TINYINT" property="invoiceType"/>
            <result column="invoice_name" jdbcType="VARCHAR" property="invoiceName"/>
            <result column="invoice_print_count" jdbcType="INTEGER" property="printCount"/>
        </collection>
    </resultMap>

    <resultMap id="findCloudPrinterByQueryMap" type="com.holder.saas.print.entity.read.PrinterReadDO">
        <result column="print_cut" jdbcType="TINYINT" property="printCut"/>
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="print_page" jdbcType="VARCHAR" property="printPage"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
        <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
        <result column="print_count" jdbcType="TINYINT" property="printCount"/>
        <result column="printer_type" jdbcType="TINYINT" property="printerType"/>
        <result column="printer_port" jdbcType="VARCHAR" property="printerPort"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="is_print_hang_up" jdbcType="TINYINT" property="isPrintHangUp"/>
        <result column="manufacturers_type" jdbcType="CHAR" property="manufacturersType"/>
        <result column="device_model" jdbcType="VARCHAR" property="deviceModel"/>
        <result column="device_key" jdbcType="VARCHAR" property="deviceKey"/>
        <discriminator javaType="java.lang.String" column="invoice_type">
            <case value="80" resultMap="MapWithItemGuid"/>
            <case value="81" resultMap="MapWithItemGuid"/>
        </discriminator>
    </resultMap>

    <resultMap id="MapWithDiscriminator" type="com.holder.saas.print.entity.read.PrinterReadDO">
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="print_cut" jdbcType="TINYINT" property="printCut"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
        <result column="print_page" jdbcType="VARCHAR" property="printPage"/>
        <result column="print_count" jdbcType="TINYINT" property="printCount"/>
        <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
        <result column="printer_port" jdbcType="VARCHAR" property="printerPort"/>
        <result column="printer_type" jdbcType="TINYINT" property="printerType"/>
        <result column="is_print_hang_up" jdbcType="TINYINT" property="isPrintHangUp"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="device_key" jdbcType="VARCHAR" property="deviceKey"/>
        <result column="manufacturers_type" jdbcType="CHAR" property="manufacturersType"/>
        <result column="device_model" jdbcType="VARCHAR" property="deviceModel"/>
        <result column="print_price_type" jdbcType="TINYINT" property="printPriceType"/>
        <result column="auto_print" jdbcType="TINYINT" property="autoPrint"/>
        <discriminator javaType="java.lang.String" column="business_type">
            <case value="1" resultMap="MapWithItemGuid"/>
            <case value="2" resultMap="MapWithItemGuid"/>
        </discriminator>
    </resultMap>

    <resultMap id="MapWithItemGuid" type="com.holder.saas.print.entity.read.PrinterReadDO">
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="print_cut" jdbcType="TINYINT" property="printCut"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
        <result column="print_page" jdbcType="VARCHAR" property="printPage"/>
        <result column="print_count" jdbcType="TINYINT" property="printCount"/>
        <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
        <result column="printer_port" jdbcType="VARCHAR" property="printerPort"/>
        <result column="printer_type" jdbcType="TINYINT" property="printerType"/>
        <result column="is_print_hang_up" jdbcType="TINYINT" property="isPrintHangUp"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="device_key" jdbcType="VARCHAR" property="deviceKey"/>
        <result column="manufacturers_type" jdbcType="CHAR" property="manufacturersType"/>
        <result column="device_model" jdbcType="VARCHAR" property="deviceModel"/>
        <result column="print_price_type" jdbcType="TINYINT" property="printPriceType"/>
        <result column="auto_print" jdbcType="TINYINT" property="autoPrint"/>
        <collection property="arrayOfItemGuid" ofType="java.lang.String">
            <result column="item_guid"/>
        </collection>
    </resultMap>

    <resultMap id="MapOfKitchenTable" type="com.holder.saas.print.entity.read.PrinterReadDO">
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="print_cut" jdbcType="TINYINT" property="printCut"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
        <result column="print_page" jdbcType="VARCHAR" property="printPage"/>
        <result column="print_count" jdbcType="TINYINT" property="printCount"/>
        <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
        <result column="printer_port" jdbcType="VARCHAR" property="printerPort"/>
        <result column="printer_type" jdbcType="TINYINT" property="printerType"/>
        <result column="is_print_hang_up" jdbcType="TINYINT" javaType="java.lang.Boolean" property="isPrintHangUp"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="device_key" jdbcType="VARCHAR" property="deviceKey"/>
        <result column="manufacturers_type" jdbcType="CHAR" property="manufacturersType"/>
        <result column="device_model" jdbcType="VARCHAR" property="deviceModel"/>
        <result column="print_price_type" jdbcType="TINYINT" property="printPriceType"/>
        <result column="auto_print" jdbcType="TINYINT" property="autoPrint"/>
    </resultMap>

    <select id="findPrinterBindings" parameterType="com.holder.saas.print.entity.domain.PrinterDO"
            resultMap="MapWithEverything">
        select p.id,
               p.store_guid,
               p.store_name,
               p.device_id,
               p.printer_guid,
               p.printer_name,
               p.business_type,
               p.printer_type,
               p.printer_ip,
               p.printer_port,
               p.print_count,
               p.print_page,
               p.print_cut,
               p.is_master,
               p.is_print_hang_up,
               p.device_no,
               p.device_key,
               p.print_price_type,
               p.manufacturers_type,
               p.area_type,
               p.part_refund_print_flag,
               i.invoice_type,
               i.invoice_name,
               i.print_count as invoice_print_count,
               d.item_guid,
               d.item_name,
               a.area_guid,
               a.area_name,
               t.table_guid,
               t.table_name
        from hsp_printer p
                 left join hsp_printer_invoice i on i.printer_guid = p.printer_guid
                 left join hsp_printer_item d on d.printer_guid = p.printer_guid
                 left join hsp_printer_area a on a.printer_guid = p.printer_guid
                 left join hsp_printer_table t on t.printer_guid = p.printer_guid
        where p.printer_guid = #{printerGuid}
    </select>

    <select id="findPrinterOfTheDevice" parameterType="com.holder.saas.print.entity.domain.PrinterDO"
            resultMap="MapWithInvoice">
        select * from (
        select
        <include refid="PrinterAndInvoiceColumn"/>
        from hsp_printer p
        left join hsp_printer_invoice i on i.printer_guid = p.printer_guid
        where p.business_type = 0 and p.device_id = #{deviceId}
        order by p.gmt_create
        ) as front
        union all
        select * from (
        select
        <include refid="PrinterAndInvoiceColumn"/>
        from hsp_printer p
        left join hsp_printer_invoice i on i.printer_guid = p.printer_guid
        where p.business_type = 1 and p.store_guid = #{storeGuid}
        order by p.gmt_create
        ) as kitchen
        union all
        select * from (
        select
        <include refid="PrinterAndInvoiceColumn"/>
        from hsp_printer p
        left join hsp_printer_invoice i on i.printer_guid = p.printer_guid
        where p.business_type = 2 and p.device_id = #{deviceId}
        order by p.gmt_create
        ) as label
    </select>

    <select id="findPrinterOfBizType" parameterType="com.holder.saas.print.entity.domain.PrinterDO"
            resultMap="MapWithInvoice">
        select
        <include refid="PrinterAndInvoiceColumn"/>
        from hsp_printer p
        left join hsp_printer_invoice i on i.printer_guid = p.printer_guid
        where
        1=1
        <if test="businessType != null">
            and p.business_type = #{businessType}
            <if test="businessType == 0 or businessType == 2">
                and p.device_id = #{deviceId}
            </if>
            <if test="businessType == 1">
                and p.store_guid = #{storeGuid}
            </if>
        </if>
        <if test="printerType != null and storeGuid != null">
            and p.printer_type = #{printerType}
            and p.store_guid = #{storeGuid}
        </if>
        order by p.gmt_create
    </select>

    <select id="listPrinterItemByPrinterIdAndItemList" parameterType="com.holder.saas.print.entity.query.PrinterQuery"
            resultMap="MapWithItem">
        select
        p.id,
        p.store_guid,
        p.store_name,
        p.device_id,
        p.printer_guid,
        p.printer_name,
        p.business_type,
        p.printer_type,
        p.printer_ip,
        p.printer_port,
        p.print_count,
        p.print_page,
        p.print_cut,
        p.is_master,
        p.is_print_hang_up,
        p.device_no,
        p.device_key,
        p.manufacturers_type,
        p.area_type,
        i.item_guid,
        i.item_name
        from hsp_printer p
        inner join hsp_printer_item i on i.printer_guid = p.printer_guid
        where p.store_guid=#{storeGuid}
        and p.device_id=#{deviceId}
        and p.business_type=#{businessType}
        <if test="arrayOfItemGuid!=null and arrayOfItemGuid.size>0">
            and i.item_guid in
            <foreach collection="arrayOfItemGuid" item="itemGuid" open="(" separator="," close=")">
                #{itemGuid}
            </foreach>
        </if>
    </select>


    <select id="findPrinterByQuery" parameterType="com.holder.saas.print.entity.query.PrinterQuery"
            resultMap="MapWithDiscriminator">
        select distinct
        <trim suffixOverrides=",">
            p.printer_guid,
            p.print_cut,
            p.business_type,
            p.print_page,
            p.print_count,
            p.printer_type,
            p.printer_ip,
            p.printer_port,
            p.is_print_hang_up,
            p.device_no,
            p.device_key,
            p.manufacturers_type,
            p.device_model,
            p.print_price_type,
            i.auto_print,
            <choose>
                <when test="arrayOfItemGuid!=null and arrayOfItemGuid.size>0">
                    d.item_guid,
                </when>
                <otherwise>

                    '' as item_guid,

                </otherwise>
            </choose>

        </trim>
        from hsp_printer p
        inner join hsp_printer_invoice i on i.printer_guid = p.printer_guid
        <if test="arrayOfItemGuid!=null and arrayOfItemGuid.size>0">
            inner join hsp_printer_item d on d.printer_guid = p.printer_guid
        </if>
        <if test=" areaGuid!=null and areaGuid!=''">
            left join hsp_printer_area a on a.printer_guid = p.printer_guid
        </if>
        <if test="tableGuid!=null and tableGuid!=''">
            left join hsp_printer_table t on t.printer_guid = p.printer_guid
        </if>
        where p.store_guid=#{storeGuid}
        <if test="deviceId!=null and deviceId!=''">
            and p.device_id=#{deviceId}
        </if>
        <if test="invoiceType !=null">
            and i.invoice_type=#{invoiceType}
        </if>
        <if test="(areaGuid!=null and areaGuid!='') or (tableGuid!=null and tableGuid!='')">
            <choose>
                <when test="areaGuid!=null and areaGuid!='' and tableGuid!=null and tableGuid!=''">
                    and ( ( p.area_type = 1 and a.area_guid=#{areaGuid} ) or ( p.area_type = 0 and
                    t.table_guid=#{tableGuid} ) )
                </when>
                <otherwise>
                    <if test="areaGuid!=null and areaGuid!=''">
                        and a.area_guid=#{areaGuid}
                    </if>
                    <if test="tableGuid!=null and tableGuid!=''">
                        and t.table_guid=#{tableGuid}
                    </if>
                </otherwise>
            </choose>
        </if>
        <if test="businessType !=null">
            and p.business_type = #{businessType}
        </if>
        <if test="arrayOfItemGuid!=null and arrayOfItemGuid.size>0">
        and d.item_guid in
        <foreach collection="arrayOfItemGuid" item="itemGuid" open="(" separator="," close=")">
            #{itemGuid}
        </foreach>
        </if>
        <if test="partRefundFlag != null and partRefundFlag">
            and p.part_refund_print_flag in (#{tradeMode}, 2)
        </if>
    </select>

    <select id="findPrinterOfKitchenTable" parameterType="com.holder.saas.print.entity.query.PrinterQuery"
            resultMap="MapOfKitchenTable">
        select p.printer_guid,
               p.print_cut,
               p.business_type,
               p.is_print_hang_up,
               p.device_no,
               p.device_key,
               p.manufacturers_type,
               p.device_model,
               p.print_page,
               p.print_count,
               p.printer_type,
               p.printer_ip,
               p.printer_port,
               p.print_price_type,
               i.auto_print
        from hsp_printer p
                 inner join hsp_printer_invoice i on i.printer_guid = p.printer_guid
                 inner join hsp_printer_area a on a.printer_guid = p.printer_guid
        where p.store_guid = #{storeGuid}
          and i.invoice_type = #{invoiceType}
          and a.area_guid = #{areaGuid}
    </select>

    <select id="findCloudPrinterByQuery" parameterType="com.holder.saas.print.entity.query.PrinterQuery"
            resultMap="findCloudPrinterByQueryMap">
        select
        <trim suffixOverrides=",">
            p.printer_guid,
            p.print_cut,
            p.business_type,
            p.print_page,
            i.print_count,
            i.invoice_type,
            p.printer_type,
            p.printer_ip,
            p.printer_port,
            p.is_print_hang_up,
            p.device_no,
            p.device_key,
            p.manufacturers_type,
            p.device_model,
            <if test="arrayOfItemGuid!=null and arrayOfItemGuid.size>0">
                IFNULL( d.item_guid, "" ) item_guid ,
            </if>
        </trim>
        from hsp_printer p
        inner join hsp_printer_invoice i on i.printer_guid = p.printer_guid
        <if test="arrayOfItemGuid!=null and arrayOfItemGuid.size>0">
            inner join hsp_printer_item d on d.printer_guid = p.printer_guid
        </if>
        where
        printer_type = 3
        and p.store_guid=#{storeGuid}
        and i.invoice_type=#{invoiceType}
        <if test="businessType !=null and businessType != ''">
            and p.business_type = #{businessType}
        </if>
        <if test="arrayOfItemGuid!=null and arrayOfItemGuid.size>0">
            and d.item_guid in
            <foreach collection="arrayOfItemGuid" item="itemGuid" open="(" separator="," close=")">
                #{itemGuid}
            </foreach>
        </if>
    </select>
    <select id="listPrinterBindings" parameterType="com.holder.saas.print.entity.domain.PrinterDO"
            resultMap="MapWithEverything">
        select p.id,
        p.store_guid,
        p.store_name,
        p.device_id,
        p.printer_guid,
        p.printer_name,
        p.business_type,
        p.printer_type,
        p.printer_ip,
        p.printer_port,
        p.print_count,
        p.print_page,
        p.print_cut,
        p.is_master,
        p.is_print_hang_up,
        p.device_no,
        p.device_key,
        p.manufacturers_type,
        p.area_type,
        i.invoice_type,
        i.invoice_name,
        i.print_count as invoice_print_count,
        d.item_guid,
        d.item_name,
        a.area_guid,
        a.area_name,
        t.table_guid,
        t.table_name
        from
        hsp_printer p
        left join hsp_printer_invoice i on i.printer_guid = p.printer_guid
        left join hsp_printer_item d on d.printer_guid = p.printer_guid
        left join hsp_printer_area a on a.printer_guid = p.printer_guid
        left join hsp_printer_table t on t.printer_guid = p.printer_guid
        where
        p.printer_guid in
        <foreach collection="printerGuidList" item="printerGuid" open="(" separator="," close=")">
            #{printerGuid}
        </foreach>
    </select>

    <sql id="PrinterAndInvoiceColumn">
        p
        .
        id
        ,
        p.store_guid, p.store_name, p.device_id,
        p.printer_guid, p.printer_name,
        p.business_type, p.printer_type,
        p.printer_ip, p.printer_port,
        p.print_count, p.print_page, p.print_cut,
        p.is_master,
        p.is_print_hang_up,
        p.device_no,
        p.device_key,
        p.manufacturers_type,
        p.device_model,
        p.print_price_type,
        p.area_type,
        p.part_refund_print_flag,
        i.invoice_type, i.invoice_name,i.print_count as invoice_print_count
    </sql>

</mapper>
