<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.GroupMealMapper">

    <resultMap id="itemRelateGroupMealBO" type="com.holderzone.saas.store.item.entity.bo.ItemRelateGroupMealBO">
        <result column="item_guid" property="itemGuid"/>
        <result column="num" property="num"/>
    </resultMap>

    <select id="mapItem2RelateGroupMealPakNum" resultMap="itemRelateGroupMealBO">
        SELECT
        sub_item_guid AS item_guid ,
        COUNT( sub_item_guid ) AS num
        FROM
        hsi_group_meal
        WHERE
        is_delete = 0
        AND sku_guid IN
        <foreach collection="skuGuids" item="guid" index="index" open="(" separator="," close=")">
            #{guid}
        </foreach>
        GROUP BY
        sub_item_guid
    </select>
</mapper>
