<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.PricePlanItemMapper">

    <select id="itemList" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanItemPageRespDTO">
        SELECT DISTINCT i.guid itemGuid, i.name, t.name typeName , pi.plan_item_name as planItemName , i.item_type as
        itemType
        FROM hsi_price_plan_item pi
        left JOIN hsi_item i ON pi.item_guid = i.guid
        left JOIN hsi_type t ON t.guid = pi.type_guid
        WHERE pi.plan_guid = #{req.planGuid}
        AND pi.is_delete = 0
        AND i.is_delete = 0
        AND t.is_delete = 0
        <if test="req.typeGuid != null and req.typeGuid != ''">
            AND i.type_guid = #{req.typeGuid}
        </if>
        <if test="req.itemNameOrCode != null and req.itemNameOrCode != ''">
            OR pi.plan_item_name like CONCAT('%',#{req.itemNameOrCode},'%')
            OR i.guid like CONCAT('%',#{req.itemNameOrCode},'%')
            OR i.NAME LIKE CONCAT( '%', #{req.itemNameOrCode}, '%' )
        </if>
        ORDER BY CONVERT(i.`name` USING gbk)
    </select>

    <update id="updateItems">
        UPDATE hsi_price_plan_item
        SET sale_price =
        <foreach collection="reqList" item="req" separator=" " open="CASE guid" close="END,">
            WHEN #{req.planItemGuid} THEN #{req.salePrice}
        </foreach>
        member_price =
        <foreach collection="reqList" item="req" separator=" " open="CASE guid" close="END">
            WHEN #{req.planItemGuid} THEN #{req.memberPrice}
        </foreach>
        WHERE guid IN
        <foreach collection="reqList" item="req" separator="," open="(" close=")">
            #{req.planItemGuid}
        </foreach>
    </update>

    <select id="getSyncItems" resultType="com.holderzone.saas.store.item.dto.SyncItemDTO">
        SELECT pi.item_guid,
               pi.sku_guid,
               pi.is_pkg_item,
               pi.sale_price,
               pi.member_price,
               s.is_delete    is_delete,
               s.sale_price   originalSalePrice,
               s.member_price originalMemberPrice,
               s.is_rack,
               s.is_join_aio,
               s.is_join_pad,
               s.is_join_pos
        from (select * FROM hsi_price_plan_item WHERE plan_guid = #{planGuid} and is_delete = 0) pi
                 LEFT JOIN hsi_sku s
                           ON pi.sku_guid = s.guid
        having is_delete = 0
    </select>

    <delete id="deleteItems">
        DELETE
        FROM hsi_price_plan_item
        WHERE plan_guid = #{planGuid}
    </delete>

    <update id="deleteBySku">
        UPDATE hsi_price_plan_item
        SET is_delete = 1
        <where>
            is_delete = 0
            AND sku_guid IN
            <foreach collection="guidList" item="skuGuid" separator="," open="(" close=")">
                #{skuGuid}
            </foreach>
        </where>
    </update>

    <update id="logicDeleteItems">
        UPDATE hsi_price_plan_item
        SET is_delete = 1
        WHERE plan_guid = #{planGuid}
    </update>

    <select id="getCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT item_guid)
        FROM hsi_price_plan_item
        WHERE plan_guid = #{planGuid}
          AND is_delete = 0
    </select>

    <select id="getBrandSkuItemList" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanItemAddQueryRespDTO">
        SELECT
        i.guid itemGuid,
        i.item_type,
        i.name,
        t.guid typeGuid,
        t.name typeName
        FROM hsi_item i
        INNER JOIN hsi_type t ON t.guid = i.type_guid
        WHERE
        i.brand_guid = #{dto.brandGuid}
        AND i.is_delete = 0
        AND t.is_delete = 0
        <if test="dto.typeGuid !=null and dto.typeGuid !=''">
            AND i.type_guid = #{dto.typeGuid}
        </if>
        <if test="dto.keywords !=null and dto.keywords !=''">
            AND i.name like CONCAT('%',#{dto.keywords},'%')
        </if>
        ORDER BY
        CONVERT(i.`name` USING gbk)
    </select>

    <select id="getPlanSkuGuidList" resultType="java.lang.String">
        SELECT sku_guid
        FROM hsi_price_plan_item
        WHERE plan_guid = #{planGuid}
          AND is_delete = 0
    </select>

    <select id="getSyncItem" resultType="com.holderzone.saas.store.item.dto.SyncItemDTO">
        SELECT pi.item_guid,
               pi.sku_guid,
               pi.is_pkg_item,
               pi.sale_price,
               pi.member_price,
               pi.is_delete,
               s.sale_price   originalSalePrice,
               s.member_price originalMemberPrice,
               s.is_rack,
               s.is_join_aio,
               s.is_join_pos,
               s.is_join_pad
        FROM hsi_price_plan_item pi
                 LEFT JOIN hsi_sku s
                           ON pi.sku_guid = s.guid
        WHERE pi.item_guid = #{itemGuid}
          AND pi.sku_guid = #{skuGuid}
          AND pi.is_delete = 0
        ORDER BY pi.id DESC LIMIT 1
    </select>

    <select id="getPlanItemGuidList" resultType="java.lang.String">
        SELECT DISTINCT item_guid
        FROM hsi_price_plan_item
        WHERE plan_guid = #{planGuid}
          AND is_delete = 0
    </select>

    <select id="getSkuList" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanSkuRespDTO">
        SELECT pi.guid                                 planItemGuid,
               s.code,
               pi.sku_guid                             skuGuid,
               s.unit,
               s.name                                  skuName,
               s.sale_price                            originalPrice,
               pi.sale_price,
               IFNULL(pi.member_price, s.member_price) memberPrice,
               pi.is_sold_out            as            isSoldOut,
               pi.picture_url            as            pictureUrl,
               pi.big_picture_url        as            bigPictureUrl,
               pi.detail_big_picture_url as            detailBigPictureUrl,
               pi.description            as            description,
               pi.is_pkg_item            as            isPkgItem,
               t.name                    as            typeName,
               pi.type_guid              as            typeGuid,
               pi.sort                   as            sort,
               pi.plan_item_name         as            planItemName,
               pi.accounting_price       as            accountingPrice,
               pi.takeaway_accounting_price       as            takeawayAccountingPrice,
               pi.line_price             as            linePrice
        FROM hsi_price_plan_item pi
                 LEFT JOIN hsi_sku s ON pi.sku_guid = s.guid
                 LEFT JOIN hsi_type t ON pi.type_guid = t.guid
        WHERE pi.item_guid = #{itemGuid}
          AND pi.plan_guid = #{planGuid}
          AND pi.is_delete = 0
    </select>


    <select id="getSkuListAll" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanSkuRespDTO">
        SELECT pi.guid planItemGuid,
        s.code,
        pi.sku_guid skuGuid,
        s.unit,
        s.name skuName,
        s.sale_price originalPrice,
        pi.sale_price,
        IFNULL(pi.member_price, s.member_price) memberPrice,
        s.accounting_price as accountingPrice,
        pi.is_sold_out as isSoldOut,
        pi.picture_url as pictureUrl,
        pi.description as description
        FROM hsi_price_plan_item pi
        LEFT JOIN hsi_sku s ON pi.sku_guid = s.guid
        WHERE pi.item_guid in
        <foreach collection="itemGuidList" item="itemGuid" open="(" close=")" separator=",">
            #{itemGuid}
        </foreach>
        AND pi.plan_guid in
        <foreach collection="planGuidList" item="planGuid" open="(" close=")" separator=",">
            #{planGuid}
        </foreach>
        AND pi.is_delete = 0
    </select>

    <resultMap id="pricePlanBySkuRespDTO" type="com.holderzone.saas.store.dto.item.resp.PricePlanBySkuRespDTO">
        <result column="plan_guid" property="planGuid"/>
        <result column="item_guid" property="itemGuid"/>
        <result column="sku_guid" property="skuGuid"/>
        <result column="name" property="planName"/>
    </resultMap>

    <select id="listPlanBySku" resultMap="pricePlanBySkuRespDTO">
        SELECT ppi.plan_guid,
               ppi.item_guid,
               ppi.sku_guid,
               pp.`name`
        FROM `hsi_price_plan_item` ppi
            INNER JOIN `hsi_price_plan` pp ON pp.guid = ppi.plan_guid
        WHERE ppi.is_delete = 0
          AND ppi.sku_guid = #{skuGuid}
          AND pp.`status` &lt;&gt; 3
    </select>

    <select id="listPlanBySkuList" resultMap="pricePlanBySkuRespDTO">
        SELECT ppi.plan_guid,
            ppi.item_guid,
            ppi.sku_guid,
            pp.`name`
        FROM `hsi_price_plan_item` ppi
        INNER JOIN `hsi_price_plan` pp ON pp.guid = ppi.plan_guid
        WHERE ppi.is_delete = 0
            AND ppi.sku_guid IN
            <foreach collection="skuList" item="skuGuid" open="(" close=")" separator=",">
                #{skuGuid}
            </foreach>
            AND pp.`status` = 3
    </select>

    <select id="queryPlanItemsByPlan" resultType="com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO">
        SELECT DISTINCT
        hppi.item_guid itemGuid

        FROM `hsi_price_plan_item` hppi
        INNER JOIN hsi_item hi ON hi.guid = hppi.item_guid
        INNER JOIN hsi_sku hs ON hs.guid = hppi.sku_guid
        <where>
            hppi.is_delete = 0
            <if test="dto.planGuid != null and dto.planGuid !=''">
                and hppi.plan_guid = #{dto.planGuid}
            </if>
            <if test="dto.typeGuid != null and dto.typeGuid !=''">
                and hppi.type_guid = #{dto.typeGuid}
            </if>
            <if test="dto.searchKey !=null and dto.searchKey !=''">
                AND(
                hi.`name` like CONCAT('%',#{dto.searchKey},'%')
                or
                hppi.item_guid like CONCAT('%',#{dto.searchKey},'%')
                )
            </if>
            <if test="dto.isWholeDiscount != null">
                AND hs.is_whole_discount = #{dto.isWholeDiscount}
            </if>
            <if test="dto.itemTypeList!= null and dto.itemTypeList.size()>0">
                AND hi.item_type IN
                <foreach collection="dto.itemTypeList" item="itemType" open="(" close=")" separator=",">
                    #{itemType}
                </foreach>
            </if>
            <if test="dto.isRack == 1">
                AND hppi.is_sold_out NOT IN (2,4)
            </if>
            <if test="dto.isRack == 0">
                AND hppi.is_sold_out = 4
            </if>
            <if test="dto.isRack == -1">
                AND hppi.is_sold_out != 2
            </if>
        </where>
    </select>
    <sql id="SELECT_PLAN_ITEM">
        SELECT
        hppi.guid planItemGuid,
        hppi.item_guid itemGuid,
        hppi.sku_guid skuGuid,
        hppi.plan_guid planGuid,
        hppi.sale_price salePrice,
        hppi.member_price memberPrice,
        hppi.is_sold_out isSoldOut,
        hppi.plan_item_name planItemName,
        hppi.picture_url pictureUrl,
        hppi.big_picture_url bigPictureUrl,
        hppi.detail_big_picture_url detailBigPictureUrl,
        hppi.description description,
        hppi.english_brief englishBrief,
        hppi.english_ingredients_desc englishIngredientsDesc,
        hppi.type_guid typeGuid,
        hppi.sort sort,
        hppi.accounting_price,
        hppi.takeaway_accounting_price,
        hppi.line_price,
        hs.is_whole_discount isWholeDiscount,
        hi.`name` name,
        hi.item_type itemType

        FROM `hsi_price_plan_item` hppi
        INNER JOIN hsi_item hi ON hi.guid = hppi.item_guid
        INNER JOIN hsi_sku hs ON hs.guid = hppi.sku_guid
        <where>
            hppi.is_delete = 0
            <if test="dto.planGuid != null and dto.planGuid !=''">
                and hppi.plan_guid = #{dto.planGuid}
            </if>
            <if test="dto.planGuidList != null and dto.planGuidList.size()>0">
                and hppi.plan_guid in
                <foreach collection="dto.planGuidList" item="planGuid" open="(" separator="," close=")">
                    #{planGuid}
                </foreach>
            </if>
            <if test="dto.filterSkuList != null and dto.filterSkuList.size()>0">
                and hppi.sku_guid not in
                <foreach collection="dto.filterSkuList" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test="dto.typeGuid != null and dto.typeGuid !=''">
                and hppi.type_guid = #{dto.typeGuid}
            </if>
            <if test="dto.searchKey !=null and dto.searchKey !=''">
                AND(
                hi.`name` like CONCAT('%',#{dto.searchKey},'%')
                or
                hppi.item_guid like CONCAT('%',#{dto.searchKey},'%')
                or
                hs.`code` like CONCAT('%',#{dto.searchKey},'%')
                )
            </if>
            <if test="dto.isWholeDiscount != null">
                AND hs.is_whole_discount = #{dto.isWholeDiscount}
            </if>
            <if test="dto.itemTypeList!= null and dto.itemTypeList.size()>0">
                AND hi.item_type IN
                <foreach collection="dto.itemTypeList" item="itemType" open="(" close=")" separator=",">
                    #{itemType}
                </foreach>
            </if>
            <if test="dto.planItemList!= null and dto.planItemList.size()>0">
                AND hppi.item_guid IN
                <foreach collection="dto.planItemList" item="itemGuid" open="(" close=")" separator=",">
                    #{itemGuid}
                </foreach>
            </if>
            <if test="dto.isRack == 1">
                AND hppi.is_sold_out NOT IN (2,4)
            </if>
            <if test="dto.isRack == 0">
                AND hppi.is_sold_out = 4
            </if>
            <if test="dto.isRack == -1">
                AND hppi.is_sold_out != 2
            </if>
        </where>
    </sql>
    <select id="queryPlanItemsByPlanAll" resultType="com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO">
        <include refid="SELECT_PLAN_ITEM"/>
        ORDER BY
        hppi.gmt_create desc, hppi.sort ASC
    </select>

    <select id="queryPlanItemsByPlanAllPage" resultType="com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO">
        <include refid="SELECT_PLAN_ITEM"/>
        ORDER BY hppi.sort ASC
    </select>
    <select id="queryPlanItemsByPlanCount" resultType="java.lang.Integer">
        SELECT count(*) from (select distinct hppi.item_guid
        FROM `hsi_price_plan_item` hppi
        INNER JOIN hsi_item hi ON hi.guid = hppi.item_guid
        INNER JOIN hsi_sku hs ON hs.guid = hppi.sku_guid
        <where>
            hppi.is_delete = 0
            <if test="dto.planGuid != null and dto.planGuid !=''">
                and hppi.plan_guid = #{dto.planGuid}
            </if>
            <if test="dto.typeGuid != null and dto.typeGuid !=''">
                and hppi.type_guid = #{dto.typeGuid}
            </if>
            <if test="dto.searchKey !=null and dto.searchKey !=''">
                AND(
                hi.`name` like CONCAT('%',#{dto.searchKey},'%')
                or
                hppi.item_guid like CONCAT('%',#{dto.searchKey},'%')
                )
            </if>
            <if test="dto.isWholeDiscount != null">
                AND hs.is_whole_discount = #{dto.isWholeDiscount}
            </if>
            <if test="dto.itemTypeList!= null and dto.itemTypeList.size()>0">
                AND hi.item_type IN
                <foreach collection="dto.itemTypeList" item="itemType" open="(" close=")" separator=",">
                    #{itemType}
                </foreach>
            </if>
            <if test="dto.isRack == 1">
                AND hppi.is_sold_out NOT IN (2,4)
            </if>
            <if test="dto.isRack == 0">
                AND hppi.is_sold_out = 4
            </if>
            <if test="dto.isRack == -1">
                AND hppi.is_sold_out != 2
            </if>
        </where>
        ) x
    </select>

    <select id="listPlanByItem" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanBySkuRespDTO">
        SELECT ppi.plan_guid,
               ppi.item_guid,
               ppi.sku_guid,
               pp.`name` as planName
        FROM `hsi_price_plan_item` ppi
                 INNER JOIN `hsi_price_plan` pp ON pp.guid = ppi.plan_guid
        WHERE ppi.is_delete = 0
          AND ppi.item_guid = #{itemGuid}
    </select>

    <update id="logicDeleteByItem">
        UPDATE hsi_price_plan_item SET is_delete = 1
        WHERE is_delete = 0 AND item_guid IN
        <foreach collection="list" item="itemGuid" open="(" close=")" separator=",">
            #{itemGuid}
        </foreach>
    </update>

    <update id="updateItemList">
        UPDATE hsi_price_plan_item
        SET sale_price =
        <foreach collection="reqList" item="req" separator=" " open="CASE guid" close="END,">
            WHEN #{req.planItemGuid} THEN #{req.salePrice}
        </foreach>
        member_price =
        <foreach collection="reqList" item="req" separator=" " open="CASE guid" close="END">
            WHEN #{req.planItemGuid} THEN #{req.memberPrice}
        </foreach>
        sort =
        <foreach collection="reqList" item="req" separator=" " open="CASE guid" close="END">
            WHEN #{req.planItemGuid} THEN #{req.sort}
        </foreach>
        item_name =
        <foreach collection="reqList" item="req" separator=" " open="CASE guid" close="END">
            WHEN #{req.planItemGuid} THEN #{req.itemName}
        </foreach>
        description =
        <foreach collection="reqList" item="req" separator=" " open="CASE guid" close="END">
            WHEN #{req.planItemGuid} THEN #{req.description}
        </foreach>
        picture_url =
        <foreach collection="reqList" item="req" separator=" " open="CASE guid" close="END">
            WHEN #{req.planItemGuid} THEN #{req.pictureUrl}
        </foreach>
        WHERE guid IN
        <foreach collection="reqList" item="req" separator="," open="(" close=")">
            #{req.planItemGuid}
        </foreach>
    </update>

    <!--查询所有为已启用、即将启用、暂不启用菜谱的所有商品（去重）-->
    <select id="listAllPlanPriceItem"
            resultType="com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllItemRespDTO">
        SELECT
        hi.item_guid AS itemGuid,
        MAX(ht.name) AS itemName,
        ht.type_guid
        FROM hsi_price_plan hp ,hsi_price_plan_item hi,hsi_item ht
        WHERE
        hi.plan_guid = hp.guid
        AND hp.brand_guid = #{dto.brandGuid}
        AND hp.status IN(1,2,4)
        AND hi.item_guid = ht.guid
        AND hi.is_delete = 0
        AND ht.is_delete = 0
        AND hp.is_delete = 0
        AND ht.brand_guid = hp.brand_guid
        <if test="dto.searchKey != null and dto.searchKey != ''">
            AND ( ht.name LIKE CONCAT('%',#{dto.searchKey},'%')
            OR ht.guid LIKE CONCAT('%',#{dto.searchKey},'%') )
        </if>

        <if test="dto.excludeItemGuidList != null and dto.excludeItemGuidList.size() > 0">
            AND hi.item_guid NOT IN
            <foreach collection="dto.excludeItemGuidList" item="guid" separator="," open="(" close=")">
                #{guid}
            </foreach>
        </if>
        GROUP BY hi.item_guid
    </select>

    <select id="listAllPlanPriceItemCount" resultType="int">
        SELECT COUNT(1) FROM (
        SELECT 1
        FROM hsi_price_plan hp ,hsi_price_plan_item hi,hsi_item ht
        WHERE
        hi.plan_guid = hp.guid
        AND hp.brand_guid = #{brandGuid}
        AND hp.status IN(1,2,4)
        AND hi.item_guid = ht.guid
        AND hi.is_delete = 0
        AND ht.is_delete = 0
        AND ht.brand_guid = hp.brand_guid
        <if test="searchKey != null and searchKey != ''">
            AND ( ht.name LIKE CONCAT('%',#{searchKey},'%')
            OR ht.guid LIKE CONCAT('%',#{searchKey},'%') )
        </if>
        <if test="excludeItemGuidList != null and excludeItemGuidList.size() > 0">
            AND hi.item_guid NOT IN
            <foreach collection="excludeItemGuidList" item="guid" separator="," open="(" close=")">
                #{guid}
            </foreach>
        </if>
        GROUP BY hi.item_guid ) x
    </select>

    <!--查询所有为已启用、即将启用、暂不启用菜谱的所有商品规格（去重）-->
    <select id="listAllPlanPriceItemSku"
            resultType="com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllItemSkuRespDTO">
        SELECT hi.item_guid AS itemGuid,ht.name AS itemName,hi.sku_guid AS skuGuid,hs.name AS skuName,
        IF(hs.name is null or hs.name = '',ht.name,CONCAT(ht.name,'(',hs.name,')')) AS itemSkuName
        FROM hsi_price_plan hp ,hsi_price_plan_item hi,hsi_item ht,hsi_sku hs
        WHERE
        hi.plan_guid = hp.guid
        AND hp.brand_guid = #{dto.brandGuid}
        AND hp.status IN(1,2,4)
        AND hi.item_guid = ht.guid
        AND hi.sku_guid = hs.guid
        AND hi.is_delete = 0
        AND ht.is_delete = 0
        AND hp.is_delete = 0
        AND ht.brand_guid = hp.brand_guid
        <if test="dto.searchKey != null and dto.searchKey != ''">
            AND ht.name LIKE CONCAT('%',#{dto.searchKey},'%')
        </if>

        <if test="dto.excludeSkuGuidList != null and dto.excludeSkuGuidList.size() > 0">
            AND hi.sku_guid NOT IN
            <foreach collection="dto.excludeSkuGuidList" item="guid" separator="," open="(" close=")">
                #{guid}
            </foreach>
        </if>
        GROUP BY hi.sku_guid
        ORDER BY hi.item_guid
    </select>

    <select id="listAllPlanPriceItemSkuCount" resultType="int">
        SELECT COUNT(1) FROM (
        SELECT 1
        FROM hsi_price_plan hp ,hsi_price_plan_item hi,hsi_item ht,hsi_sku hs
        WHERE
        hi.plan_guid = hp.guid
        AND hp.brand_guid = #{dto.brandGuid}
        AND hp.status IN(1,2,4)
        AND hi.item_guid = ht.guid
        AND hi.sku_guid = hs.guid
        AND hi.is_delete = 0
        AND ht.is_delete = 0
        AND hp.is_delete = 0
        AND ht.brand_guid = hp.brand_guid
        <if test="dto.searchKey != null and dto.searchKey != ''">
            AND ht.name LIKE CONCAT('%',#{dto.searchKey},'%')
        </if>

        <if test="dto.excludeSkuGuidList != null and dto.excludeSkuGuidList.size() > 0">
            AND hi.sku_guid NOT IN
            <foreach collection="dto.excludeSkuGuidList" item="guid" separator="," open="(" close=")">
                #{guid}
            </foreach>
        </if>
        GROUP BY hi.sku_guid ) x
    </select>

    <!--根据菜品guid查询菜品规格和已经应用得菜品-->
    <select id="listItemSkuAndPlanPrice" resultMap="ItemSkuAndPlanPriceMap">
        SELECT hi.guid,
               hi.name,
               hi.picture_url,
               hi.description
        FROM hsi_item hi
        WHERE hi.guid = #{itemGuid}
          AND hi.is_delete = 0

    </select>

    <resultMap id="ItemSkuAndPlanPriceMap" type="com.holderzone.saas.store.dto.item.resp.price.ItemSkuAndPlanPriceDTO">
        <result property="itemGuid" column="guid"/>
        <result property="itemName" column="name"/>
        <result property="pictureUrl" column="picture_url"/>
        <result property="description" column="description"/>
        <association property="itemSkuList" column="guid" select="ListPlanPriceEditItemSku"/>
        <association property="planPriceList" column="guid" select="ListPlanPriceEdit"/>
    </resultMap>
    <select id="ListPlanPriceEditItemSku"
            resultType="com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditItemSkuDTO">
        SELECT hs.guid         AS skuGuid,
               hs.name         AS skuName,
               hs.unit,
               hs.sale_price   AS salePrice,
               hs.member_price AS memberPrice,
               hs.accounting_price AS accountingPrice,
               hs.takeaway_accounting_price AS takeawayAccountingPrice
        FROM hsi_sku hs
        WHERE hs.item_guid = #{guid}
          AND hs.is_delete = 0
    </select>

    <select id="ListPlanPriceEdit" resultType="com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO">
        SELECT DISTINCT hpp.name AS planName, hpp.guid AS planGuid, hppi.type_guid AS typeGuid
        FROM hsi_price_plan_item hppi,
             hsi_price_plan hpp
        WHERE hppi.item_guid = #{guid}
          AND hppi.plan_guid = hpp.guid
          AND hpp.is_delete = 0
          AND hppi.is_delete = 0
          AND hpp.status in (1, 2, 4)
          AND hpp.guid NOT IN (SELECT parent_guid FROM hsi_price_plan WHERE parent_guid IS NOT NULL)
        order by hpp.gmt_modified desc
    </select>

    <select id="listPlanPriceEditByItems" resultType="com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO">
        SELECT DISTINCT hpp.guid AS planGuid,hpp.name AS planName,hpp.status,hpp.sell_time_type AS sellTimeType,
        hpp.brand_guid as brandGuid,start_time AS startTime,end_time AS endTime
        FROM hsi_price_plan_item hppi,hsi_price_plan hpp
        WHERE hppi.item_guid IN
        <foreach collection="reqList" item="guid" separator="," open="(" close=")">
            #{guid}
        </foreach>
        AND hppi.plan_guid = hpp.guid
        AND hpp.is_delete = 0
        AND hppi.is_delete = 0
    </select>

    <select id="listPlanPriceExistItem" resultMap="PlanPriceExistItemMap">
        SELECT hpp.guid,hpp.name,hpp.status,hpp.sell_time_type,hpp.brand_guid,start_time,end_time
        FROM hsi_price_plan hpp
        WHERE hpp.guid IN
        <foreach collection="reqList" item="guid" separator="," open="(" close=")">
            #{guid}
        </foreach>
        AND hpp.is_delete = 0
    </select>
    <resultMap id="PlanPriceExistItemMap" type="com.holderzone.saas.store.dto.item.common.PlanPriceExistItemDTO">
        <result property="planGuid" column="guid"/>
        <result property="planName" column="name"/>
        <result property="status" column="status"/>
        <result property="sellTimeType" column="sell_time_type"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="brandGuid" column="brand_guid"/>
        <association property="itemGuidList" column="guid" select="ListExistItem"/>
    </resultMap>

    <select id="ListExistItem" resultType="String">
        SELECT hppi.item_guid
        FROM hsi_price_plan_item hppi
        WHERE hppi.plan_guid = #{guid}
    </select>

    <select id="listAvailablePlanPrice" resultType="com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO">
        SELECT hpp.guid AS planGuid,hpp.name AS planName
        FROM hsi_price_plan hpp
        WHERE
        brand_guid = #{brandGuid}
        AND hpp.is_delete = 0
        AND hpp.status in (1,2,4)
        <if test="itemGuidList != null and itemGuidList.size() > 0">
            AND hpp.guid IN
            (
            SELECT plan_guid FROM hsi_price_plan_item WHERE item_guid IN
            <foreach collection="itemGuidList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="planGuidList != null and planGuidList.size() > 0">
            AND hpp.guid NOT IN
            <foreach collection="planGuidList" item="guid" separator="," open="(" close=")">
                #{guid}
            </foreach>
        </if>

        <if test="planName != null and planName != ''">
            AND hpp.name LIKE CONCAT('%',#{planName},'%')
        </if>
        order by hpp.gmt_modified desc
    </select>

    <select id="listAvailablePlanItemType" resultType="com.holderzone.saas.store.dto.item.resp.ItemTypeRespDTO">
        SELECT DISTINCT guid AS guid, name AS itemTypeName
        FROM hsi_type
        WHERE price_plan_guid = #{planGuid}
          AND is_delete = 0
    </select>

    <select id="listPlanByStoreGuids"
            resultType="com.holderzone.saas.store.dto.item.resp.PricePlanSkuRespDTO">
        select
            pps.store_guid,
            ppt.item_guid,
            ppt.sku_guid,
            ppt.plan_item_name
        from
            hsi_price_plan_store pps
        JOIN hsi_price_plan pp on pp.guid = pps.plan_guid and pp.is_delete = 0
        JOIN hsi_price_plan_item ppt on ppt.plan_guid = pp.guid and ppt.is_delete = 0
        <where>
            pps.brand_guid = #{brandGuid}
            and pps.is_delete = 0
            and pps.store_guid in
            <foreach collection="storeGuids" open="(" item="storeGuid" close=")" separator=",">
                #{storeGuid}
            </foreach>
            and pp.status in (1, 4)
        </where>
    </select>
    
    <select id="countUseStoreNum" resultType="com.holderzone.saas.store.dto.common.CountItemDTO">
        select 
        pi.item_guid item,
        count(DISTINCT ps.store_guid) num
        from hsi_price_plan_item pi
        left join hsi_price_plan_store ps on
        pi.plan_guid = ps.plan_guid
        where pi.item_guid in
        <foreach collection="itemGuidList" open="(" item="itemGuid" close=")" separator=",">
            #{itemGuid}
        </foreach>
        and pi.is_delete = 0
        group by pi.item_guid
    </select>


    <select id="listAllPlanPriceItemTypeGuid" resultType="java.lang.String">
        SELECT
           distinct htype.guid
        FROM
            hsi_price_plan hp ,
            hsi_type htype
        WHERE
            htype.price_plan_guid = hp.guid
            AND hp.brand_guid = #{brandGuid}
            AND hp.status IN (1,2,4)
            AND hp.is_delete = 0
            AND htype.type_from = 5
            AND htype.is_delete = 0
            AND hp.guid NOT IN (SELECT parent_guid FROM hsi_price_plan WHERE parent_guid IS NOT NULL)
    </select>


    <select id="listItemTypeAndPlanPrice"
            resultType="com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO">
        SELECT
            hpp.guid AS planGuid,
            max(hpp.name) AS planName
        FROM
            hsi_price_plan hpp,
            hsi_type htype
        WHERE
            htype.name = #{typeName}
            and htype.price_plan_guid = hpp.guid
            AND hpp.is_delete = 0
            AND hpp.status in (1, 2, 4)
            AND hpp.guid NOT IN (SELECT parent_guid FROM hsi_price_plan WHERE parent_guid IS NOT NULL)
        group by hpp.guid
        order by hpp.gmt_modified desc
    </select>

    <select id="getSkuListByPlanItemGuid"
            resultType="com.holderzone.saas.store.dto.item.resp.PricePlanSkuRespDTO">
        SELECT
            pi.guid AS planItemGuid,
            s.code,
            pi.sku_guid AS skuGuid,
            s.unit,
            s.name AS skuName,
            s.sale_price AS originalPrice,
            pi.sale_price,
            pi.member_price AS memberPrice,
            pi.is_sold_out AS isSoldOut,
            pi.picture_url AS pictureUrl,
            pi.big_picture_url AS bigPictureUrl,
            pi.detail_big_picture_url AS detailBigPictureUrl,
            pi.description AS description,
            pi.is_pkg_item AS isPkgItem,
            t.name AS typeName,
            pi.type_guid AS typeGuid,
            pi.sort AS sort,
            pi.plan_item_name AS planItemName,
            pi.accounting_price AS accountingPrice,
            pi.takeaway_accounting_price AS takeawayAccountingPrice,
            pi.line_price AS linePrice
        FROM
            hsi_price_plan_item pi
            LEFT JOIN hsi_sku s ON pi.sku_guid = s.guid
            LEFT JOIN hsi_type t ON pi.type_guid = t.guid
        WHERE
            pi.item_guid = #{itemGuid}
            AND pi.plan_guid = #{planGuid}
            AND pi.is_delete = 0
    </select>

</mapper>