<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.TradeDetailMapper">

    <select id="listGroupon" resultType="com.holderzone.saas.store.dto.report.resp.GrouponTradeDetailRespDTO">
        SELECT
            a.*
        FROM
        (
        SELECT
            g.source,
            g.coupon_code,
            g.coupon_use_time,
            o.name as "store_name",
            g.third_store_guid,
            g.third_store_name,
            g.order_no,
            g.deal_title,
            g.deal_id,
            g.deal_value,
            g.biz_cost::varchar,
            g.coupon_buy_price,
            g.due::varchar,
            g.guid,
            COALESCE(g.volume, 0) as volume,
            COALESCE(g.single_value, 0)::varchar as single_value
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_groupon_trade_detail" g
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_groupon" gg on gg.code = g.coupon_code
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = g.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        <include refid="grouponWhereSQL" />

        UNION ALL

        SELECT
            1 AS source ,
            gg.code AS coupon_code,
            gg.gmt_create AS coupon_use_time,
            o.name as "store_name",
            '-' AS third_store_guid,
            '-' AS third_store_name,
            ord.order_no,
            '-' AS deal_title,
            '-' AS deal_id,
            gg.deduction_amount AS deal_value,
            '-' AS biz_cost,
            gg.coupon_buy_price,
            '-' AS due,
            gg.guid::varchar,
            0 as volume,
            '-' as single_value
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_groupon" gg
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = gg.store_guid
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" ord on ord.guid = gg.order_guid
        WHERE
		    gg.is_delete = 0
            AND gg.refund_order_guid is null
            AND gg.groupon_type = 61
            <if test="query.startTime != null">
                and gg.gmt_create >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                <![CDATA[ and gg.gmt_create <= to_timestamp(concat(#{query.endTime}::text, ' 23:59:59'), 'yyyy-mm-dd hh24:mi:ss') ]]>
            </if>
            <if test="query.storeGuids != null and query.storeGuids.size()>0">
                and gg.store_guid in
                <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.couponSource != null and query.couponSource != 1">
                and 1 = 2
            </if>

        ) a
        ORDER BY a.coupon_use_time DESC, a.guid asc
    </select>

    <select id="pageGroupon" resultType="com.holderzone.saas.store.dto.report.resp.GrouponTradeDetailRespDTO">
        SELECT
            a.*
        FROM
        (
        SELECT
            g.source,
            g.coupon_code,
            g.coupon_use_time,
            o.name as "store_name",
            g.third_store_name,
            g.order_no,
            g.deal_title,
            g.deal_id,
            g.deal_value,
            g.biz_cost::varchar,
            g.coupon_buy_price,
            g.due::varchar,
            g.guid
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_groupon_trade_detail" g
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_groupon" gg on gg.code = g.coupon_code
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = g.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        <include refid="grouponWhereSQL" />

        UNION ALL

        SELECT
            1 AS source ,
            gg.code AS coupon_code,
            gg.gmt_create AS coupon_use_time,
            o.name as "store_name",
            '-' AS third_store_name,
            ord.order_no,
            '-' AS deal_title,
            '-' AS deal_id,
            gg.deduction_amount AS deal_value,
            '-' AS biz_cost,
            gg.coupon_buy_price,
            '-' AS due,
            gg.guid::varchar
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_groupon" gg
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = gg.store_guid
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" ord on ord.guid = gg.order_guid
        WHERE
		    gg.is_delete = 0
            AND gg.refund_order_guid is null
            AND gg.groupon_type = 61
            <if test="query.startTime != null">
                and gg.gmt_create >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                <![CDATA[ and gg.gmt_create <= to_timestamp(concat(#{query.endTime}::text, ' 23:59:59'), 'yyyy-mm-dd hh24:mi:ss') ]]>
            </if>
            <if test="query.storeGuids != null and query.storeGuids.size()>0">
                and gg.store_guid in
                <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.couponSource != null and query.couponSource != 1">
                and 1 = 2
            </if>

        ) a
        ORDER BY a.coupon_use_time DESC, a.guid asc
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <select id="countGroupon" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
        (
        SELECT
            g.source,
            g.coupon_code,
            g.coupon_use_time,
            o.name as "store_name",
            g.third_store_name,
            g.order_no,
            g.deal_title,
            g.deal_id,
            g.deal_value,
            g.biz_cost,
            g.coupon_buy_price,
            g.due,
            g.guid
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_groupon_trade_detail" g
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_groupon" gg on gg.code = g.coupon_code
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = g.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        <include refid="grouponWhereSQL"/>

        UNION ALL

        SELECT
            1 AS source ,
            gg.code AS coupon_code,
            gg.gmt_create AS coupon_use_time,
            o.name as "store_name",
            '-' AS third_store_name,
            ord.order_no,
            '-' AS deal_title,
            '-' AS deal_id,
            gg.deduction_amount AS deal_value,
            0 AS biz_cost,
            gg.coupon_buy_price,
            0 AS due,
            gg.guid::varchar
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_groupon" gg
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = gg.store_guid
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" ord on ord.guid = gg.order_guid
        WHERE
		    gg.is_delete = 0
            AND gg.refund_order_guid is null
            AND gg.groupon_type = 6
            <if test="query.startTime != null">
                and gg.gmt_create >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                <![CDATA[ and gg.gmt_create <= to_timestamp(concat(#{query.endTime}::text, ' 23:59:59'), 'yyyy-mm-dd hh24:mi:ss') ]]>
            </if>
            <if test="query.storeGuids != null and query.storeGuids.size()>0">
                and gg.store_guid in
                <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.couponSource != null and query.couponSource != '' and query.couponSource != 1">
                and 1 = 2
            </if>

        ) a
    </select>


    <sql id="grouponWhereSQL">
        <where>
            g.is_delete = '0' and g.cancel = '0' and gg.is_delete = 0 and gg.refund_order_guid is null
            <if test="query.startTime != null">
                and g.coupon_use_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                <![CDATA[ and g.coupon_use_time <= to_timestamp(concat(#{query.endTime}::text, ' 23:59:59'), 'yyyy-mm-dd hh24:mi:ss') ]]>
            </if>
            <if test="query.brandGuid != null and query.brandGuid != ''">
                and ro.brand_guid = #{query.brandGuid}
            </if>
            <if test="query.storeGuids != null and query.storeGuids.size()>0">
                and g.store_guid in
                <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.couponSource != null and query.couponSource != ''">
                and g.source = #{query.couponSource}
            </if>
            AND gg.groupon_type =6
        </where>
    </sql>

    <select id="listTakeaway"
            resultType="com.holderzone.saas.store.dto.report.resp.TakeawayTradeDetailRespDTO">
        SELECT
            hto.store_guid,
            o.name as "store_name",
            TO_CHAR (t.order_create_time, 'YYYY-MM-DD') as "order_create_date",
            hto.order_sub_type as "takeout_order_type",
            t.status,
            hto.order_day_sn,
            t.order_id,
            TO_CHAR (t.order_create_time, 'YYYY-MM-DD hh:mm:ss') as "order_create_time",
            TO_CHAR (t.order_complete_time, 'YYYY-MM-DD hh:mm:ss') as "order_complete_time",
            t.food_amount,
            t.shipping_amount,
            hto.package_total as "package_bag_money",
            t.single_increase_amount,
            hto.customer_actual_pay as "user_pay_total_amount",
            t.pay_type,
            t.phf_pay_total_amount_for_poi,
            t.total_activity_amount,
            t.activity_details,
            t.commision_amount,
            t.platform_pay_for_poi_amount,
            t.total_merchant_fees,
            t.offline_order_sk_pay_amount,
            t.settle_amount,
            t.shipping_type
        FROM
            "hst_takeaway_${query.enterpriseGuid}_db"."hst_takeout_order_trade_detail" t
        LEFT JOIN "hst_takeaway_${query.enterpriseGuid}_db"."hst_takeout_order" hto on hto.order_id = t.order_id
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = hto.store_guid
        <include refid="takeawayWhereSQL" />
        order by t.order_create_time desc, t.order_id asc
    </select>

    <select id="pageTakeaway"
            resultType="com.holderzone.saas.store.dto.report.resp.TakeawayTradeDetailRespDTO">
        SELECT
            o.name as "store_name",
            TO_CHAR (t.order_create_time, 'YYYY-MM-DD') as "order_create_date",
            hto.order_sub_type as "takeout_order_type",
            t.status,
            t.order_id,
            t.food_amount,
            t.shipping_amount,
            hto.package_total as "package_bag_money",
            hto.customer_actual_pay as "user_pay_total_amount",
            t.pay_type,
            t.settle_amount
        FROM
            "hst_takeaway_${query.enterpriseGuid}_db"."hst_takeout_order_trade_detail" t
        LEFT JOIN "hst_takeaway_${query.enterpriseGuid}_db"."hst_takeout_order" hto on hto.order_id = t.order_id
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = hto.store_guid
        <include refid="takeawayWhereSQL" />
        order by t.order_create_time desc, t.order_id asc
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <select id="countTakeaway" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            "hst_takeaway_${query.enterpriseGuid}_db"."hst_takeout_order_trade_detail" t
        LEFT JOIN "hst_takeaway_${query.enterpriseGuid}_db"."hst_takeout_order" hto on hto.order_id = t.order_id
        <include refid="takeawayWhereSQL" />
    </select>

    <sql id="takeawayWhereSQL">
        <where>
            t.is_delete = '0' and hto.order_status = '100' and t.status = '8' and hto.refund_status = 0
            <if test="query.startTime != null">
                and t.order_create_time >= #{query.startTime}
                <if test="query.enterpriseGuid == '887f2181-eb06-4d77-b914-7c37c884952c'">
                    and hto.business_day >= #{query.startTime}
                </if>
            </if>
            <if test="query.endTime != null">
                <![CDATA[ and t.order_create_time <= to_timestamp(concat(#{query.endTime}::text, ' 23:59:59'), 'yyyy-mm-dd hh24:mi:ss') ]]>
                <if test="query.enterpriseGuid == '887f2181-eb06-4d77-b914-7c37c884952c'">
                    <![CDATA[ and hto.business_day <= #{query.endTime} ]]>
                </if>
            </if>
            <if test="query.brandGuid != null and query.brandGuid != ''">
                and hto.brand_guid = #{query.brandGuid}
            </if>
            <if test="query.storeGuids != null and query.storeGuids.size()>0">
                and hto.store_guid in
                <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.takeoutOrderType != null">
                and hto.order_sub_type = #{query.takeoutOrderType}
            </if>
        </where>
    </sql>

    <select id="summarizingPaymentConstitute"
            resultType="com.holderzone.saas.store.dto.report.resp.PaymentConstituteDTO">
        WITH
        tranRecoPay AS (
            SELECT
                g.business_day businessDate,
                g.payment_type paymentType,
                g.payment_type_name payMethod,
                g.pay_power_id::VARCHAR payPowerId,
                SUM ( g.salesRevenue ) salesRevenue,
                0 memberRecharge,
                0 deposit
            FROM
            (
                (
                SELECT
                    tr.business_day,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id,
                    SUM ( tr.amount ) salesRevenue
                FROM
                    "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                    LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON o.guid = tr.order_guid
                WHERE
                    tr.is_delete = 0
                    AND tr.business_day >= #{query.startTime}
                    AND tr.business_day <![CDATA[ <= ]]> #{query.endTime}
                    AND tr.STATE = 4
                    AND o.STATE = 4
                    <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                        AND tr.store_guid IN
                        <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                            #{storeGuid}
                        </foreach>
                    </if>
                    AND o.is_delete = 0
                    AND tr.payment_type NOT IN ( 20, 61, 65, 66 )
                GROUP BY
                    tr.business_day,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id
                )
                UNION ALL
                (
                SELECT
                    tr.business_day,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id,
                    SUM( case when tr.is_multiple_agg_pay = 1 then tr.amount - COALESCE(tr.refund_amount, 0) else tr.amount end) AS salesRevenue
                FROM
                    "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                    LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON o.guid = tr.order_guid
                WHERE
                    tr.is_delete = 0
                    AND tr.business_day >= #{query.startTime}
                    AND tr.business_day <![CDATA[ <= ]]> #{query.endTime}
                    AND tr.STATE = 4
                    AND o.STATE = 5
                    AND o.recovery_id = '0'
                    <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                        AND tr.store_guid IN
                        <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                            #{storeGuid}
                        </foreach>
                    </if>
                    AND o.is_delete = 0
                    AND tr.payment_type NOT IN ( 20, 61, 65, 66 )
                GROUP BY
                    tr.business_day,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id
                )
            ) g
            GROUP BY
                g.business_day,
                g.payment_type,
                g.payment_type_name,
                g.pay_power_id
        ),
        membRechPay AS (
            SELECT
                mc.business_day businessDate,
                mcp.pay_way paymentType,
                mcp.pay_name payMethod,
                NULL::VARCHAR payPowerId,
                0 salesRevenue,
                SUM ( mcp.pay_amount ) memberRecharge,
                0 deposit
            FROM
                "hsm_alliance_member_platform_db"."hsa_member_consumption_pay_way" mcp
                INNER JOIN "hsm_alliance_member_platform_db"."hsa_member_consumption" mc ON mcp.consumption_guid = mc.guid
            WHERE
                mc.consumption_type = 0
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND mc.member_store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                AND mc.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                AND mc.consumption_type = 0
                AND mc.is_cancel = 0
            GROUP BY
                mcp.pay_name,
                mcp.pay_way,
                mc.business_day
        ),
        reseRecoPay AS (
            SELECT
                to_char( rr.confirm_time :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
                pr.pay_type paymentType,
                pr.pay_type_name payMethod,
                pr.pay_power_id::VARCHAR payPowerId,
                0 salesRevenue,
                0 memberRecharge,
                SUM ( pr.reserve_amount ) deposit
            FROM
                "hsr_reserve_${query.enterpriseGuid}_db"."hss_reserve_record" rr,
                "hsr_reserve_${query.enterpriseGuid}_db"."hss_reserve_pay_record" pr
            WHERE
                rr.guid = pr.reserve_guid
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND rr.store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                AND rr.reserve_amount > 0
                AND rr.is_deleted = false
                AND rr.confirm_time BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
            GROUP BY
                to_char( rr.confirm_time :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE,
                pr.pay_type,
                pr.pay_type_name,
                pr.pay_power_id
        ),
        takeoutSale AS (
            SELECT
                business_day businessDate,
                99 paymentType,
                CASE
                    order_sub_type
                    WHEN 0 THEN
                    '美团外卖'
                    WHEN 1 THEN
                    '饿了么外卖'
                    WHEN 3 THEN
                    '京东外卖'
                    WHEN 5 THEN
                    '自营外卖'
                    WHEN 6 THEN
                    '赚餐外卖' ELSE ''
                END payMethod,
                NULL::VARCHAR payPowerId,
                SUM ( customer_actual_pay - (
                        CASE is_refund_success
                            WHEN 1 THEN ( CASE order_tag_status WHEN 0 THEN COALESCE ( customer_refund ) ELSE 0 END )
                            ELSE 0 END )
                    ) salesRevenue,
                0 memberRecharge,
                0 deposit
            FROM
                "hst_takeaway_${query.enterpriseGuid}_db"."hst_takeout_order"
            WHERE
                order_status = 100
                AND refund_status <![CDATA[ <> ]]> 2
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                AND business_day BETWEEN #{query.startTime}
                AND #{query.endTime}
            GROUP BY
                business_day,
                order_sub_type
        ),
        grouponPay AS (
            SELECT
                o.business_day businessDate,
            CASE
                    gr.groupon_type
                    WHEN 6 THEN
                    20 ELSE gr.groupon_type
                END paymentType,
            CASE
                gr.groupon_type
                WHEN 6 THEN
                '美团团购'
                WHEN 61 THEN
                '抖音团购'
                WHEN 65 THEN
                '支付宝团购'
                WHEN 66 THEN
                '农行团购'
                ELSE''
                END payMethod,
                NULL::VARCHAR payPowerId,
                SUM ( gr.coupon_buy_price ) salesRevenue,
                0 memberRecharge,
                0 deposit
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_groupon" gr
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON o.guid = gr.order_guid
            WHERE
                gr.is_delete = 0
                AND o.business_day BETWEEN #{query.startTime}
                AND #{query.endTime}
                AND gr.receipt_channel != '1004'
                AND o.STATE = 4
                AND o.is_delete = 0
                AND gr.refund_order_guid IS NULL
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
            GROUP BY
                gr.groupon_type,
                o.business_day
        ),
        paymentInfo AS (
            SELECT
                store_guid,
                app_id
            FROM
                (
                SELECT
                    store_guid,
                    app_id,
                    ROW_NUMBER ( ) OVER ( PARTITION BY app_id ORDER BY create_time ) AS row_num
                FROM
                    hse_enterprise_db.hse_payment_info
                WHERE
                    is_delete = 0
                ) subquery
            WHERE
                row_num = 1
        ),
        payAppId AS (
            SELECT
                pi.app_id
            FROM
                "hsb_business_${query.enterpriseGuid}_db".hsb_payment_type bpt
                LEFT JOIN paymentInfo pi ON pi.store_guid = bpt.store_guid
            WHERE
                bpt.payment_type = 1
                AND bpt.STATE = 0
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND bpt.store_guid in
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        ),
        cloudPay AS (
            SELECT
                pa.businessDate businessDate,
                200 paymentType,
                '云收款码' payMethod,
                NULL::VARCHAR payPowerId,
                COALESCE (SUM ( ROUND( amount :: NUMERIC / 100.0, 2 ) ), 0 ) salesRevenue,
                0 memberRecharge,
                0 deposit
            FROM
                (
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    to_char( gmt_create :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
                    pay_guid
                FROM
                    hpt_trading_db_01.hpt_order_01
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    to_char( gmt_create :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
                    pay_guid
                FROM
                    hpt_trading_db_01.hpt_order_02
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    to_char( gmt_create :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
                    pay_guid
                FROM
                    hpt_trading_db_02.hpt_order_01
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    to_char( gmt_create :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
                    pay_guid
                FROM
                    hpt_trading_db_02.hpt_order_02
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                ) pa
            GROUP BY
                pa.businessDate
        ),
        quickPay AS (
            SELECT
                CASE WHEN pay_power_id = '-1'
                    THEN 4 ELSE 2
                    END AS paymentType,
                CASE WHEN pay_power_id = '-1'
                    THEN '会员余额支付' ELSE '聚合支付'
                    END	AS payMethod,
                pay_power_id::VARCHAR payPowerId,
                gmt_time_paid :: TIMESTAMP :: DATE AS payDate,
                SUM ( amount ) AS amountTotal,
                COUNT ( DISTINCT order_guid ) AS orderCount,
                COUNT ( DISTINCT order_guid ) AS guestCount
            FROM
                "hsp_pay_${query.enterpriseGuid}_db"."hsp_pay_record"
            WHERE
                pay_st = '2'
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
                AND gmt_time_paid BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
            GROUP BY
                pay_power_id,
                gmt_time_paid :: TIMESTAMP :: DATE
        ),
        bankOrder AS(
            SELECT
                tr.business_day AS payDate,
                trade1.bank_transaction_no as transactionNo
            FROM
                 "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_01"."hpt_trading_01" trade1 ON trade1.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        UNION All
            SELECT
                tr.business_day AS payDate,
                trade1.bank_transaction_no as transactionNo
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_01"."hpt_trading_02" trade1 ON trade1.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        UNION All
            SELECT
                tr.business_day AS payDate,
                trade2.bank_transaction_no as transactionNo
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_02"."hpt_trading_01" trade2 ON trade2.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        UNION All
            SELECT
                tr.business_day AS payDate,
                trade2.bank_transaction_no as transactionNo
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_02"."hpt_trading_02" trade2 ON trade2.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        ),
        serviceCharge AS(
            SELECT
                2 AS paymentType,
                '聚合支付' AS payMethod,
                NULL::VARCHAR payPowerId,
                bo.payDate,
                SUM ( ROUND( ob.service_charge_amount :: NUMERIC / 100.0, 2 ) ) AS service_charge
            FROM
                hpt_trading_db_01.hpt_order_bill ob
                JOIN bankOrder bo on ob.bank_order_no = bo.transactionNo
            GROUP BY
                 bo.payDate
        )

        SELECT
            rp.businessDate,
            "min" ( rp.paymentType ) paymentType,
            rp.payMethod,
            rp.payPowerId,
            SUM ( rp.salesRevenue ) salesRevenue,
            SUM ( rp.memberRecharge ) memberRecharge,
            SUM ( rp.deposit ) deposit,
        	SUM ( rp.serviceCharge ) AS serviceCharge,
			SUM ( rp.actualIncome ) AS actualIncome
        FROM
            (
            SELECT
                trp.businessDate,
                trp.paymentType,
                trp.payMethod,
                trp.payPowerId,
                COALESCE ( trp.salesRevenue, 0 ) salesRevenue,
                COALESCE ( trp.memberRecharge, 0 ) memberRecharge,
                COALESCE ( trp.deposit, 0 ) deposit,
                0 AS serviceCharge,
                COALESCE ( trp.salesRevenue, 0 ) + COALESCE ( trp.memberRecharge, 0 ) + COALESCE ( trp.deposit, 0 ) AS actualIncome
            FROM
                tranRecoPay trp
            UNION ALL
            SELECT
                mrp.businessDate,
                mrp.paymentType,
                mrp.payMethod,
                mrp.payPowerId,
                COALESCE ( mrp.salesRevenue, 0 ) salesRevenue,
                COALESCE ( mrp.memberRecharge, 0 ) memberRecharge,
                COALESCE ( mrp.deposit, 0 ) deposit,
                0 AS serviceCharge,
                COALESCE ( mrp.salesRevenue, 0 ) + COALESCE ( mrp.memberRecharge, 0 ) + COALESCE ( mrp.deposit, 0 ) AS actualIncome
            FROM
                membRechPay mrp
        	UNION ALL
            SELECT
                rrp.businessDate,
                rrp.paymentType,
                rrp.payMethod,
                rrp.payPowerId,
                COALESCE ( rrp.salesRevenue, 0 ) salesRevenue,
                COALESCE ( rrp.memberRecharge, 0 ) memberRecharge,
                COALESCE ( rrp.deposit, 0 ) deposit,
                0 AS serviceCharge,
                COALESCE ( rrp.salesRevenue, 0 ) + COALESCE ( rrp.memberRecharge, 0 ) + COALESCE ( rrp.deposit, 0 ) AS actualIncome
            FROM
                reseRecoPay rrp
        	UNION ALL
            SELECT
                ts.businessDate,
                ts.paymentType,
                ts.payMethod,
                ts.payPowerId,
                COALESCE ( ts.salesRevenue, 0 ) salesRevenue,
                COALESCE ( ts.memberRecharge, 0 ) memberRecharge,
                COALESCE ( ts.deposit, 0 ) deposit,
                0 AS serviceCharge,
                COALESCE ( ts.salesRevenue, 0 ) + COALESCE ( ts.memberRecharge, 0 ) + COALESCE ( ts.deposit, 0 ) AS actualIncome
            FROM
                takeoutSale ts
        	UNION ALL
            SELECT
                gp.businessDate,
                gp.paymentType,
                gp.payMethod,
                gp.payPowerId,
                COALESCE ( gp.salesRevenue, 0 ) salesRevenue,
                COALESCE ( gp.memberRecharge, 0 ) memberRecharge,
                COALESCE ( gp.deposit, 0 ) deposit,
                0 AS serviceCharge,
                COALESCE ( gp.salesRevenue, 0 ) + COALESCE ( gp.memberRecharge, 0 ) + COALESCE ( gp.deposit, 0 ) AS actualIncome
            FROM
                grouponPay gp
            UNION ALL
            SELECT
                cp.businessDate,
                cp.paymentType,
                cp.payMethod,
                cp.payPowerId,
                COALESCE ( cp.salesRevenue, 0 ) salesRevenue,
                COALESCE ( cp.memberRecharge, 0 ) memberRecharge,
                COALESCE ( cp.deposit, 0 ) deposit,
                0 AS serviceCharge,
                COALESCE ( cp.salesRevenue, 0 ) + COALESCE ( cp.memberRecharge, 0 ) + COALESCE ( cp.deposit, 0 ) AS actualIncome
            FROM
                cloudPay cp
            UNION ALL
            SELECT
                qp.payDate,
                qp.paymentType,
                qp.payMethod,
                qp.payPowerId,
                COALESCE ( qp.amountTotal, 0 ) salesRevenue,
                0 memberRecharge,
                0 deposit,
                0 AS serviceCharge,
                COALESCE ( qp.amountTotal, 0 ) AS actualIncome
            FROM
                quickPay qp
            UNION ALL
            SELECT
                sc.payDate,
                sc.paymentType,
                sc.payMethod,
                sc.payPowerId,
                0 salesRevenue,
                0 memberRecharge,
                0 deposit,
                COALESCE ( sc.service_charge, 0 ) AS serviceCharge,
                COALESCE ( -sc.service_charge, 0 ) AS actualIncome
            FROM
                serviceCharge sc
            ) rp
        GROUP BY
            rp.businessDate,
            rp.payMethod,
            rp.payPowerId
    </select>

    <select id="singlePaymentConstitute"
            resultType="com.holderzone.saas.store.dto.report.resp.PaymentConstituteDTO">
    WITH
    tranRecoPay AS (
        SELECT
            trp.store_guid storeGuid,
            trp.payment_type paymentType,
            trp.payment_type_name payMethod,
            trp.pay_power_id::VARCHAR payPowerId,
            SUM ( trp.salesRevenue ) salesRevenue,
            0 memberRecharge,
            0 deposit
        FROM
            (
                (
                SELECT
                    tr.store_guid,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id,
                    SUM ( tr.amount ) salesRevenue
                FROM
                    "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                    LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON o.guid = tr.order_guid
                WHERE
                    tr.is_delete = 0
                    AND tr.business_day >= #{query.startTime}
                    AND tr.business_day <![CDATA[ <= ]]> #{query.endTime}
                    AND tr.STATE = 4
                    AND o.STATE = 4
                    <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                        AND tr.store_guid IN
                        <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                            #{storeGuid}
                        </foreach>
                    </if>
                    AND o.is_delete = 0
                    AND tr.payment_type NOT IN ( 20, 61, 65, 66 )
                GROUP BY
                    tr.store_guid,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id
                )
                UNION ALL
                (
                SELECT
                    tr.store_guid,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id,
                    SUM( case when tr.is_multiple_agg_pay = 1 then tr.amount - COALESCE(tr.refund_amount, 0) else tr.amount end) AS salesRevenue
                FROM
                    "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                    LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON o.guid = tr.order_guid
                WHERE
                    tr.is_delete = 0
                    AND tr.business_day >= #{query.startTime}
                    AND tr.business_day <![CDATA[ <= ]]> #{query.endTime}
                    AND tr.STATE = 4
                    AND o.STATE = 5
                    AND o.recovery_id = '0'
                    <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                        AND tr.store_guid IN
                        <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                            #{storeGuid}
                        </foreach>
                    </if>
                    AND o.is_delete = 0
                    AND tr.payment_type NOT IN ( 20, 61, 65, 66 )
                GROUP BY
                    tr.store_guid,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id
                )
            ) trp
        GROUP BY
            trp.store_guid,
            trp.payment_type,
            trp.payment_type_name,
            trp.pay_power_id
    ),
    membRechPay AS (
        SELECT
            mc.member_store_guid storeGuid,
            mcp.pay_way paymentType,
            mcp.pay_name payMethod,
            NULL::VARCHAR payPowerId,
            0 salesRevenue,
            SUM ( mcp.pay_amount ) memberRecharge,
            0 deposit
        FROM
            "hsm_alliance_member_platform_db"."hsa_member_consumption_pay_way" mcp
            INNER JOIN "hsm_alliance_member_platform_db"."hsa_member_consumption" mc ON mcp.consumption_guid = mc.guid
        WHERE
            mc.consumption_type = 0
            <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                AND mc.member_store_guid IN
                <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            AND mc.business_day BETWEEN #{query.startTime} AND #{query.endTime}
            AND mc.consumption_type = 0
            AND mc.is_cancel = 0
        GROUP BY
            mcp.pay_name,
            mcp.pay_way,
            mc.member_store_guid
    ),
    reseRecoPay AS (
        SELECT
            rr.store_guid storeGuid,
            pr.pay_type paymentType,
            pr.pay_type_name payMethod,
            pr.pay_power_id::VARCHAR payPowerId,
            0 salesRevenue,
            0 memberRecharge,
            SUM ( pr.reserve_amount ) deposit
        FROM
            "hsr_reserve_${query.enterpriseGuid}_db"."hss_reserve_record" rr,
            "hsr_reserve_${query.enterpriseGuid}_db"."hss_reserve_pay_record" pr
        WHERE
            rr.guid = pr.reserve_guid
            <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                AND rr.store_guid IN
                <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            AND rr.reserve_amount > 0
            AND rr.is_deleted = false
            AND rr.confirm_time BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
            AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
        GROUP BY
            rr.store_guid,
            pr.pay_type,
            pr.pay_type_name,
            pr.pay_power_id
    ),
    takeoutSale AS (
        SELECT
            store_guid storeGuid,
            99 paymentType,
            CASE
                order_sub_type
                WHEN 0 THEN
                '美团外卖'
                WHEN 1 THEN
                '饿了么外卖'
                WHEN 3 THEN
                '京东外卖'
                WHEN 5 THEN
                '自营外卖'
                WHEN 6 THEN
                '赚餐外卖' ELSE ''
            END payMethod,
            NULL::VARCHAR payPowerId,
            SUM ( customer_actual_pay - (
                    CASE is_refund_success
                        WHEN 1 THEN ( CASE order_tag_status WHEN 0 THEN COALESCE ( customer_refund ) ELSE 0 END )
                        ELSE 0 END )
                ) salesRevenue,
            0 memberRecharge,
            0 deposit
        FROM
            "hst_takeaway_${query.enterpriseGuid}_db"."hst_takeout_order"
        WHERE
            order_status = 100
            AND refund_status <![CDATA[ <> ]]> 2
            <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                AND store_guid IN
                <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            AND business_day BETWEEN #{query.startTime}
            AND #{query.endTime}
        GROUP BY
            store_guid,
            order_sub_type
        ),
        grouponPay AS (
            SELECT
                o.store_guid storeGuid,
            CASE
                    gr.groupon_type
                    WHEN 6 THEN
                    20 ELSE gr.groupon_type
                END paymentType,
            CASE
                gr.groupon_type
                WHEN 6 THEN
                '美团团购'
                WHEN 61 THEN
                '抖音团购'
                WHEN 65 THEN
                '支付宝团购'
                WHEN 66 THEN
                '农行团购' ELSE''
                END payMethod,
                NULL::VARCHAR payPowerId,
                SUM ( gr.coupon_buy_price ) salesRevenue,
                0 memberRecharge,
                0 deposit
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_groupon" gr
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON o.guid = gr.order_guid
            WHERE
                gr.is_delete = 0
                AND o.business_day BETWEEN #{query.startTime}
                AND #{query.endTime}
                AND gr.receipt_channel != '1004'
                AND o.STATE = 4
                AND o.is_delete = 0
                AND gr.refund_order_guid IS NULL
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
            GROUP BY
                gr.groupon_type,
                o.store_guid
        ),
        paymentInfo AS (
            SELECT
                store_guid,
                app_id
            FROM
                (
                SELECT
                    store_guid,
                    app_id,
                    ROW_NUMBER ( ) OVER ( PARTITION BY app_id ORDER BY create_time ) AS row_num
                FROM
                    hse_enterprise_db.hse_payment_info
                WHERE
                    is_delete = 0
                ) subquery
            WHERE
                row_num = 1
        ),
        payAppId AS (
            SELECT
                pi.app_id,
                pi.store_guid
            FROM
                "hsb_business_${query.enterpriseGuid}_db".hsb_payment_type bpt
                LEFT JOIN paymentInfo pi ON pi.store_guid = bpt.store_guid
            WHERE
                bpt.payment_type = 1
                AND bpt.STATE = 0
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND bpt.store_guid in
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        ),
        cloudPay AS (
            SELECT
                pai.store_guid storeGuid,
                200 paymentType,
                '云收款码' payMethod,
                NULL::VARCHAR payPowerId,
                COALESCE (SUM ( ROUND( amount :: NUMERIC / 100.0, 2 ) ), 0 ) salesRevenue,
                0 memberRecharge,
                0 deposit
            FROM
                (
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    pay_guid
                FROM
                    hpt_trading_db_01.hpt_order_01
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    pay_guid
                FROM
                    hpt_trading_db_01.hpt_order_02
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    pay_guid
                FROM
                    hpt_trading_db_02.hpt_order_01
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    pay_guid
                FROM
                    hpt_trading_db_02.hpt_order_02
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                ) pa
                LEFT JOIN payAppId pai ON pai.app_id = pa.app_id
            GROUP BY
                pai.store_guid
        ),
        quickPay AS (
            SELECT
                store_guid,
                CASE WHEN pay_power_id = '-1'
                    THEN 4 ELSE 2
                    END AS paymentType,
                CASE WHEN pay_power_id = '-1'
                    THEN '会员余额支付' ELSE '聚合支付'
                    END	AS payMethod,
                pay_power_id::VARCHAR payPowerId,
                SUM ( amount ) AS amountTotal,
                COUNT ( DISTINCT order_guid ) AS orderCount,
                COUNT ( DISTINCT order_guid ) AS guestCount
            FROM
                "hsp_pay_${query.enterpriseGuid}_db"."hsp_pay_record"
            WHERE
                pay_st = '2'
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
                AND gmt_time_paid BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
            GROUP BY
                pay_power_id,
                store_guid
        ),
        bankOrder AS(
            SELECT
                tr.store_guid AS storeGuid,
                trade1.bank_transaction_no as transactionNo
            FROM
                 "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_01"."hpt_trading_01" trade1 ON trade1.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        UNION All
            SELECT
                tr.store_guid AS storeGuid,
                trade1.bank_transaction_no as transactionNo
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_01"."hpt_trading_02" trade1 ON trade1.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        UNION All
            SELECT
                tr.store_guid AS storeGuid,
                trade2.bank_transaction_no as transactionNo
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_02"."hpt_trading_01" trade2 ON trade2.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        UNION All
            SELECT
                tr.store_guid AS storeGuid,
                trade2.bank_transaction_no as transactionNo
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_02"."hpt_trading_02" trade2 ON trade2.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        ),
        serviceCharge AS(
            SELECT
                2 AS paymentType,
                '聚合支付' AS payMethod,
                NULL::VARCHAR payPowerId,
                bo.storeGuid,
                SUM ( ROUND( ob.service_charge_amount :: NUMERIC / 100.0, 2 ) ) AS service_charge
            FROM
                hpt_trading_db_01.hpt_order_bill ob
                JOIN bankOrder bo on ob.bank_order_no = bo.transactionNo
            GROUP BY
                 bo.storeGuid
        )

    SELECT
        rp.storeGuid,
        "min" ( rp.paymentType ) paymentType,
        rp.payMethod,
        rp.payPowerId,
        SUM ( rp.salesRevenue ) salesRevenue,
        SUM ( rp.memberRecharge ) memberRecharge,
        SUM ( rp.deposit ) deposit,
        SUM ( rp.serviceCharge ) AS serviceCharge,
        SUM ( rp.actualIncome ) AS actualIncome
    FROM
        (
        SELECT
            trp.storeGuid,
            trp.paymentType,
            trp.payMethod,
            trp.payPowerId,
            COALESCE ( trp.salesRevenue, 0 ) salesRevenue,
            COALESCE ( trp.memberRecharge, 0 ) memberRecharge,
            COALESCE ( trp.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( trp.salesRevenue, 0 ) + COALESCE ( trp.memberRecharge, 0 ) + COALESCE ( trp.deposit, 0 ) AS actualIncome
        FROM
            tranRecoPay trp
        UNION ALL
        SELECT
            mrp.storeGuid,
            mrp.paymentType,
            mrp.payMethod,
            mrp.payPowerId,
            COALESCE ( mrp.salesRevenue, 0 ) salesRevenue,
            COALESCE ( mrp.memberRecharge, 0 ) memberRecharge,
            COALESCE ( mrp.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( mrp.salesRevenue, 0 ) + COALESCE ( mrp.memberRecharge, 0 ) + COALESCE ( mrp.deposit, 0 ) AS actualIncome
        FROM
            membRechPay mrp
        UNION ALL
        SELECT
            rrp.storeGuid,
            rrp.paymentType,
            rrp.payMethod,
            rrp.payPowerId,
            COALESCE ( rrp.salesRevenue, 0 ) salesRevenue,
            COALESCE ( rrp.memberRecharge, 0 ) memberRecharge,
            COALESCE ( rrp.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( rrp.salesRevenue, 0 ) + COALESCE ( rrp.memberRecharge, 0 ) + COALESCE ( rrp.deposit, 0 ) AS actualIncome
        FROM
            reseRecoPay rrp
        UNION ALL
        SELECT
            ts.storeGuid,
            ts.paymentType,
            ts.payMethod,
            ts.payPowerId,
            COALESCE ( ts.salesRevenue, 0 ) salesRevenue,
            COALESCE ( ts.memberRecharge, 0 ) memberRecharge,
            COALESCE ( ts.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( ts.salesRevenue, 0 ) + COALESCE ( ts.memberRecharge, 0 ) + COALESCE ( ts.deposit, 0 ) AS actualIncome
        FROM
            takeoutSale ts
        UNION ALL
        SELECT
            gp.storeGuid,
            gp.paymentType,
            gp.payMethod,
            gp.payPowerId,
            COALESCE ( gp.salesRevenue, 0 ) salesRevenue,
            COALESCE ( gp.memberRecharge, 0 ) memberRecharge,
            COALESCE ( gp.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( gp.salesRevenue, 0 ) + COALESCE ( gp.memberRecharge, 0 ) + COALESCE ( gp.deposit, 0 ) AS actualIncome
        FROM
            grouponPay gp
        UNION ALL
        SELECT
            cp.storeGuid,
            cp.paymentType,
            cp.payMethod,
            cp.payPowerId,
            COALESCE ( cp.salesRevenue, 0 ) salesRevenue,
            COALESCE ( cp.memberRecharge, 0 ) memberRecharge,
            COALESCE ( cp.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( cp.salesRevenue, 0 ) + COALESCE ( cp.memberRecharge, 0 ) + COALESCE ( cp.deposit, 0 ) AS actualIncome
        FROM
            cloudPay cp
        UNION ALL
        SELECT
            qp.store_guid,
            qp.paymentType,
            qp.payMethod,
            qp.payPowerId,
            COALESCE ( qp.amountTotal, 0 ) salesRevenue,
            0 memberRecharge,
            0 deposit,
            0 AS serviceCharge,
            COALESCE ( qp.amountTotal, 0 ) AS actualIncome
        FROM
            quickPay qp
        UNION ALL
        SELECT
            sc.storeGuid,
            sc.paymentType,
            sc.payMethod,
            sc.payPowerId,
            0 salesRevenue,
            0 memberRecharge,
            0 deposit,
            COALESCE ( sc.service_charge, 0 ) AS serviceCharge,
            COALESCE ( -sc.service_charge, 0 ) AS actualIncome
        FROM
            serviceCharge sc
        ) rp
    GROUP BY
        rp.storeGuid,
        rp.payMethod,
        rp.payPowerId
    </select>

    <select id="singleDaySinglePaymentConstitute"
            resultType="com.holderzone.saas.store.dto.report.resp.PaymentConstituteDTO">
    WITH
    tranRecoPay AS (
        SELECT
            trp.store_guid storeGuid,
            trp.business_day businessDate,
            trp.payment_type paymentType,
            trp.payment_type_name payMethod,
            trp.pay_power_id::VARCHAR payPowerId,
            SUM ( trp.salesRevenue ) salesRevenue,
            0 memberRecharge,
            0 deposit
        FROM
            (
                (
                SELECT
                    tr.store_guid,
                    tr.business_day,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id,
                    SUM ( tr.amount ) salesRevenue
                FROM
                    "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                    LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON o.guid = tr.order_guid
                WHERE
                    tr.is_delete = 0
                    AND tr.business_day >= #{query.startTime}
                    AND tr.business_day <![CDATA[ <= ]]> #{query.endTime}
                    AND tr.STATE = 4
                    AND o.STATE = 4
                    <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                        AND tr.store_guid IN
                        <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                            #{storeGuid}
                        </foreach>
                    </if>
                    AND o.is_delete = 0
                    AND tr.payment_type NOT IN ( 20, 61, 65, 66 )
                GROUP BY
                    tr.store_guid,
                    tr.business_day,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id
                )
                UNION ALL
                (
                SELECT
                    tr.store_guid,
                    tr.business_day,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id,
                    SUM( case when tr.is_multiple_agg_pay = 1 then tr.amount - COALESCE(tr.refund_amount, 0) else tr.amount end) AS salesRevenue
                FROM
                    "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                    LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON o.guid = tr.order_guid
                WHERE
                    tr.is_delete = 0
                    AND tr.business_day >= #{query.startTime}
                    AND tr.business_day <![CDATA[ <= ]]> #{query.endTime}
                    AND tr.STATE = 4
                    AND o.STATE = 5
                    AND o.recovery_id = '0'
                    <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                        AND tr.store_guid IN
                        <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                            #{storeGuid}
                        </foreach>
                    </if>
                    AND o.is_delete = 0
                    AND tr.payment_type NOT IN ( 20, 61, 65, 66 )
                GROUP BY
                    tr.store_guid,
                    tr.business_day,
                    tr.payment_type,
                    tr.payment_type_name,
                    tr.pay_power_id
                )
            ) trp
        GROUP BY
            trp.store_guid,
            trp.business_day,
            trp.payment_type,
            trp.payment_type_name,
            trp.pay_power_id
    ),
    membRechPay AS (
        SELECT
            mc.member_store_guid storeGuid,
            mc.business_day businessDate,
            mcp.pay_way paymentType,
            mcp.pay_name payMethod,
            NULL::VARCHAR payPowerId,
            0 salesRevenue,
            SUM ( mcp.pay_amount ) memberRecharge,
            0 deposit
        FROM
            "hsm_alliance_member_platform_db"."hsa_member_consumption_pay_way" mcp
            INNER JOIN "hsm_alliance_member_platform_db"."hsa_member_consumption" mc ON mcp.consumption_guid = mc.guid
        WHERE
            mc.consumption_type = 0
            <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                AND mc.member_store_guid IN
                <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            AND mc.business_day BETWEEN #{query.startTime} AND #{query.endTime}
            AND mc.consumption_type = 0
            AND mc.is_cancel = 0
        GROUP BY
            mcp.pay_name,
            mcp.pay_way,
            mc.member_store_guid,
            mc.business_day
    ),
    reseRecoPay AS (
        SELECT
            rr.store_guid storeGuid,
            to_char( rr.confirm_time :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
            pr.pay_type paymentType,
            pr.pay_type_name payMethod,
            pr.pay_power_id::VARCHAR payPowerId,
            0 salesRevenue,
            0 memberRecharge,
            SUM ( pr.reserve_amount ) deposit
        FROM
            "hsr_reserve_${query.enterpriseGuid}_db"."hss_reserve_record" rr,
            "hsr_reserve_${query.enterpriseGuid}_db"."hss_reserve_pay_record" pr
        WHERE
            rr.guid = pr.reserve_guid
            <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                AND rr.store_guid IN
                <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            AND rr.reserve_amount > 0
            AND rr.is_deleted = false
            AND rr.confirm_time BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
            AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
        GROUP BY
            rr.store_guid,
            to_char( rr.confirm_time :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE,
            pr.pay_type,
            pr.pay_type_name,
            pr.pay_power_id
    ),
    takeoutSale AS (
        SELECT
            store_guid storeGuid,
            business_day businessDate,
            99 paymentType,
            CASE
                order_sub_type
                WHEN 0 THEN
                '美团外卖'
                WHEN 1 THEN
                '饿了么外卖'
                WHEN 3 THEN
                '京东外卖'
                WHEN 5 THEN
                '自营外卖'
                WHEN 6 THEN
                '赚餐外卖' ELSE ''
            END payMethod,
            NULL::VARCHAR payPowerId,
            SUM ( customer_actual_pay - (
                    CASE is_refund_success
                        WHEN 1 THEN ( CASE order_tag_status WHEN 0 THEN COALESCE ( customer_refund ) ELSE 0 END )
                        ELSE 0 END )
                ) salesRevenue,
            0 memberRecharge,
            0 deposit
        FROM
            "hst_takeaway_${query.enterpriseGuid}_db"."hst_takeout_order"
        WHERE
            order_status = 100
            AND refund_status <![CDATA[ <> ]]> 2
            <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                AND store_guid IN
                <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            AND business_day BETWEEN #{query.startTime}
            AND #{query.endTime}
        GROUP BY
            store_guid,
            business_day,
            order_sub_type
        ),
        grouponPay AS (
            SELECT
                o.store_guid storeGuid,
                o.business_day businessDate,
            CASE
                    gr.groupon_type
                    WHEN 6 THEN
                    20 ELSE gr.groupon_type
                END paymentType,
            CASE
                gr.groupon_type
                WHEN 6 THEN
                '美团团购'
                WHEN 61 THEN
                '抖音团购'
                WHEN 65 THEN
                '支付宝团购'
                WHEN 66 THEN
                '农行团购' ELSE''
                END payMethod,
                NULL::VARCHAR payPowerId,
                SUM ( gr.coupon_buy_price ) salesRevenue,
                0 memberRecharge,
                0 deposit
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_groupon" gr
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON o.guid = gr.order_guid
            WHERE
                gr.is_delete = 0
                AND o.business_day BETWEEN #{query.startTime}
                AND #{query.endTime}
                AND gr.receipt_channel != '1004'
                AND o.STATE = 4
                AND o.is_delete = 0
                AND gr.refund_order_guid IS NULL
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
            GROUP BY
                gr.groupon_type,
                o.store_guid,
                o.business_day
        ),
        paymentInfo AS (
            SELECT
                store_guid,
                app_id
            FROM
                (
                SELECT
                    store_guid,
                    app_id,
                    ROW_NUMBER ( ) OVER ( PARTITION BY app_id ORDER BY create_time ) AS row_num
                FROM
                    hse_enterprise_db.hse_payment_info
                WHERE
                    is_delete = 0
                ) subquery
            WHERE
                row_num = 1
        ),
        payAppId AS (
            SELECT
                pi.app_id,
                pi.store_guid
            FROM
                "hsb_business_${query.enterpriseGuid}_db".hsb_payment_type bpt
                LEFT JOIN paymentInfo pi ON pi.store_guid = bpt.store_guid
            WHERE
                bpt.payment_type = 1
                AND bpt.STATE = 0
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND bpt.store_guid in
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        ),
        cloudPay AS (
            SELECT
                pai.store_guid storeGuid,
                pa.businessDate businessDate,
                200 paymentType,
                '云收款码' payMethod,
                NULL::VARCHAR payPowerId,
                COALESCE (SUM ( ROUND( amount :: NUMERIC / 100.0, 2 ) ), 0 ) salesRevenue,
                0 memberRecharge,
                0 deposit
            FROM
                (
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    to_char( gmt_create :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
                    pay_guid
                FROM
                    hpt_trading_db_01.hpt_order_01
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    to_char( gmt_create :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
                    pay_guid
                FROM
                    hpt_trading_db_01.hpt_order_02
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    to_char( gmt_create :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
                    pay_guid
                FROM
                    hpt_trading_db_02.hpt_order_01
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    to_char( gmt_create :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
                    pay_guid
                FROM
                    hpt_trading_db_02.hpt_order_02
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51 )
                    AND gmt_create BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                    AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                ) pa
                LEFT JOIN payAppId pai ON pai.app_id = pa.app_id
            GROUP BY
                pai.store_guid,
                pa.businessDate
        ),
        quickPay AS (
            SELECT
                store_guid,
                to_char( gmt_time_paid :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE businessDate,
                CASE WHEN pay_power_id = '-1'
                    THEN 4 ELSE 2
                    END AS paymentType,
                CASE WHEN pay_power_id = '-1'
                    THEN '会员余额支付' ELSE '聚合支付'
                    END	AS payMethod,
                pay_power_id::VARCHAR payPowerId,
                SUM ( amount ) AS amountTotal,
                COUNT ( DISTINCT order_guid ) AS orderCount,
                COUNT ( DISTINCT order_guid ) AS guestCount
            FROM
                "hsp_pay_${query.enterpriseGuid}_db"."hsp_pay_record"
            WHERE
                pay_st = '2'
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
                AND gmt_time_paid BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
            GROUP BY
                pay_power_id,
                store_guid,
                to_char( gmt_time_paid :: TIMESTAMP, 'YYYY-MM-DD' ) :: DATE
        ),
        bankOrder AS(
            SELECT
                tr.store_guid AS storeGuid,
                tr.business_day AS businessDate,
                trade1.bank_transaction_no as transactionNo
            FROM
                 "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_01"."hpt_trading_01" trade1 ON trade1.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
            UNION All
            SELECT
                tr.store_guid AS storeGuid,
                tr.business_day AS businessDate,
                trade1.bank_transaction_no as transactionNo
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_01"."hpt_trading_02" trade1 ON trade1.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
            UNION All
            SELECT
                tr.store_guid AS storeGuid,
                tr.business_day AS businessDate,
                trade2.bank_transaction_no as transactionNo
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_02"."hpt_trading_01" trade2 ON trade2.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
            UNION All
            SELECT
                tr.store_guid AS storeGuid,
                tr.business_day AS businessDate,
                trade2.bank_transaction_no as transactionNo
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr
                JOIN "hpt_trading_db_02"."hpt_trading_02" trade2 ON trade2.out_order_no = tr.order_guid::VARCHAR
            WHERE
                tr.is_delete = 0
                AND tr.STATE = 4
                AND tr.payment_type = 2
                AND tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND tr.store_guid IN
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        ),
        serviceCharge AS(
            SELECT
                2 AS paymentType,
                '聚合支付' AS payMethod,
                NULL::VARCHAR payPowerId,
                bo.storeGuid,
                bo.businessDate,
                SUM ( ROUND( ob.service_charge_amount :: NUMERIC / 100.0, 2 ) ) AS service_charge
            FROM
                hpt_trading_db_01.hpt_order_bill ob
                JOIN bankOrder bo on ob.bank_order_no = bo.transactionNo
            GROUP BY
                 bo.storeGuid,
                 bo.businessDate
        )

    SELECT
        rp.storeGuid,
        rp.businessDate,
        "min" ( rp.paymentType ) paymentType,
        rp.payMethod,
        rp.payPowerId,
        SUM ( rp.salesRevenue ) salesRevenue,
        SUM ( rp.memberRecharge ) memberRecharge,
        SUM ( rp.deposit ) deposit,
        SUM ( rp.serviceCharge ) AS serviceCharge,
        SUM ( rp.actualIncome ) AS actualIncome
    FROM
        (
        SELECT
            trp.storeGuid,
            trp.businessDate,
            trp.paymentType,
            trp.payMethod,
            trp.payPowerId,
            COALESCE ( trp.salesRevenue, 0 ) salesRevenue,
            COALESCE ( trp.memberRecharge, 0 ) memberRecharge,
            COALESCE ( trp.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( trp.salesRevenue, 0 ) + COALESCE ( trp.memberRecharge, 0 ) + COALESCE ( trp.deposit, 0 ) AS actualIncome
        FROM
            tranRecoPay trp
        UNION ALL
        SELECT
            mrp.storeGuid,
            mrp.businessDate,
            mrp.paymentType,
            mrp.payMethod,
            mrp.payPowerId,
            COALESCE ( mrp.salesRevenue, 0 ) salesRevenue,
            COALESCE ( mrp.memberRecharge, 0 ) memberRecharge,
            COALESCE ( mrp.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( mrp.salesRevenue, 0 ) + COALESCE ( mrp.memberRecharge, 0 ) + COALESCE ( mrp.deposit, 0 ) AS actualIncome
        FROM
            membRechPay mrp
        UNION ALL
        SELECT
            rrp.storeGuid,
            rrp.businessDate,
            rrp.paymentType,
            rrp.payMethod,
            rrp.payPowerId,
            COALESCE ( rrp.salesRevenue, 0 ) salesRevenue,
            COALESCE ( rrp.memberRecharge, 0 ) memberRecharge,
            COALESCE ( rrp.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( rrp.salesRevenue, 0 ) + COALESCE ( rrp.memberRecharge, 0 ) + COALESCE ( rrp.deposit, 0 ) AS actualIncome
        FROM
            reseRecoPay rrp
        UNION ALL
        SELECT
            ts.storeGuid,
            ts.businessDate,
            ts.paymentType,
            ts.payMethod,
            ts.payPowerId,
            COALESCE ( ts.salesRevenue, 0 ) salesRevenue,
            COALESCE ( ts.memberRecharge, 0 ) memberRecharge,
            COALESCE ( ts.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( ts.salesRevenue, 0 ) + COALESCE ( ts.memberRecharge, 0 ) + COALESCE ( ts.deposit, 0 ) AS actualIncome
        FROM
            takeoutSale ts
        UNION ALL
        SELECT
            gp.storeGuid,
            gp.businessDate,
            gp.paymentType,
            gp.payMethod,
            gp.payPowerId,
            COALESCE ( gp.salesRevenue, 0 ) salesRevenue,
            COALESCE ( gp.memberRecharge, 0 ) memberRecharge,
            COALESCE ( gp.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( gp.salesRevenue, 0 ) + COALESCE ( gp.memberRecharge, 0 ) + COALESCE ( gp.deposit, 0 ) AS actualIncome
        FROM
            grouponPay gp
        UNION ALL
        SELECT
            cp.storeGuid,
            cp.businessDate,
            cp.paymentType,
            cp.payMethod,
            cp.payPowerId,
            COALESCE ( cp.salesRevenue, 0 ) salesRevenue,
            COALESCE ( cp.memberRecharge, 0 ) memberRecharge,
            COALESCE ( cp.deposit, 0 ) deposit,
            0 AS serviceCharge,
            COALESCE ( cp.salesRevenue, 0 ) + COALESCE ( cp.memberRecharge, 0 ) + COALESCE ( cp.deposit, 0 ) AS actualIncome
        FROM
            cloudPay cp
        UNION ALL
        SELECT
            qp.store_guid,
            qp.businessDate,
            qp.paymentType,
            qp.payMethod,
            qp.payPowerId,
            COALESCE ( qp.amountTotal, 0 ) salesRevenue,
            0 memberRecharge,
            0 deposit,
            0 AS serviceCharge,
            COALESCE ( qp.amountTotal, 0 ) AS actualIncome
        FROM
            quickPay qp
        UNION ALL
        SELECT
            sc.storeGuid,
            sc.businessDate,
            sc.paymentType,
            sc.payMethod,
            sc.payPowerId,
            0 salesRevenue,
            0 memberRecharge,
            0 deposit,
            COALESCE ( sc.service_charge, 0 ) AS serviceCharge,
            COALESCE ( -sc.service_charge, 0 ) AS actualIncome
        FROM
            serviceCharge sc
        ) rp
    GROUP BY
        rp.storeGuid,
        rp.businessDate,
        rp.payMethod,
        rp.payPowerId
    </select>

    <select id="querySalePayDetail"
            resultType="com.holderzone.saas.store.dto.report.openapi.SalePayDetailRespDTO">
        select
            t.order_guid,
            t.amount * 100 as "amount",
            t.payment_type,
            t.payment_type as "pay_way_code",
            t.payment_type_name,
            t.bank_transaction_id,
            t.gmt_create
        from
            "hst_trade_${enterpriseGuid}_db".hst_transaction_record t
        <where>
            t.is_delete = '0'
            <if test="orderGuidList != null and orderGuidList.size()>0">
                and t.order_guid in
                <foreach collection="orderGuidList" item="orderGuid" open="(" close=")" separator="," >
                    #{orderGuid}
                </foreach>
            </if>
        </where>
        order by t.guid asc
    </select>

    <select id="cloudPayConstitute"
            resultType="com.holderzone.saas.store.dto.report.resp.CloudPayConstituteRespDTO">
        WITH
        paymentInfo AS (
            SELECT
                store_guid,
                app_id
            FROM
                (
                SELECT
                    store_guid,
                    app_id,
                    ROW_NUMBER ( ) OVER ( PARTITION BY app_id ORDER BY create_time ) AS row_num
                FROM
                    hse_enterprise_db.hse_payment_info
                WHERE
                    is_delete = 0
                ) subquery
            WHERE
                row_num = 1
        ),
        payAppId AS (
            SELECT
                pi.app_id,
                pi.store_guid
            FROM
                "hsb_business_${query.enterpriseGuid}_db".hsb_payment_type bpt
                LEFT JOIN paymentInfo pi ON pi.store_guid = bpt.store_guid
            WHERE
                bpt.payment_type = 1
                AND bpt.STATE = 0
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                    AND bpt.store_guid in
                    <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                             separator=",">
                        #{storeGuid}
                    </foreach>
                </if>
        ),
        cloudPay AS (
            SELECT
                200 payCode,
                '云收款码' payMethod,
                COALESCE (SUM ( ROUND( amount :: NUMERIC / 100.0, 2 ) ), 0 ) totalAmount
            FROM
                (
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    pay_guid
                FROM
                    hpt_trading_db_01.hpt_order_01
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51, 91 )
                    AND gmt_create BETWEEN #{query.startTime} :: TIMESTAMP
                    AND #{query.endTime} :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    pay_guid
                FROM
                    hpt_trading_db_01.hpt_order_02
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51, 91 )
                    AND gmt_create BETWEEN #{query.startTime} :: TIMESTAMP
                    AND #{query.endTime} :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    pay_guid
                FROM
                    hpt_trading_db_02.hpt_order_01
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51, 91 )
                    AND gmt_create BETWEEN #{query.startTime} :: TIMESTAMP
                    AND #{query.endTime} :: TIMESTAMP
                UNION
                SELECT
                    id,
                    store_name,
                    app_id,
                    amount,
                    pay_power_id,
                    pay_power_name,
                    pay_state,
                    gmt_create,
                    pay_guid
                FROM
                    hpt_trading_db_02.hpt_order_02
                WHERE
                    app_id IN ( SELECT app_id FROM payAppId )
                    AND pay_state = 2
                    AND pay_power_id IN ( 31, 51, 91 )
                    AND gmt_create BETWEEN #{query.startTime} :: TIMESTAMP
                    AND #{query.endTime} :: TIMESTAMP
                ) pa
        )

        SELECT
            cp.payCode,
            cp.payMethod,
            COALESCE ( cp.totalAmount, 0 ) totalAmount
        FROM
            cloudPay cp
    </select>

    <select id="queryAggPayServiceCharge" resultType="java.math.BigDecimal">
		SELECT
            SUM ( ROUND( ob.service_charge_amount :: NUMERIC / 100.0, 2 ) ) AS "service_charge"
        FROM
            hpt_trading_db_01.hpt_order_bill ob
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_transaction_record" tr ON tr.bank_transaction_id = ob.bank_order_no
        WHERE
            tr.business_day BETWEEN #{query.startTime} AND #{query.endTime}
        	AND tr.is_delete = 0
	        AND tr.STATE = 4
            <if test="query.storeGuidList != null and query.storeGuidList.size() > 0 ">
                AND tr.store_guid IN
                <foreach collection="query.storeGuidList" index="index" item="storeGuid" open="(" close=")"
                         separator=",">
                    #{storeGuid}
                </foreach>
            </if>
    </select>

</mapper>
