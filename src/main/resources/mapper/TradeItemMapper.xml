<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.TradeItemMapper">

    <select id="countStoreSaleStatistics" resultType="java.lang.Long">
        <include refid="storeSaleStatisticsSql"/>
        -- 门店商品销量
        SELECT
            COUNT( 1 )
        FROM
        (
            SELECT
                <if test="query.groupByStoreFlag != null and query.groupByStoreFlag">
                    s.store_guid,
                </if>
                s.itemName,
                s.typeName,
                s.itemTypeName,
                s.unit,
                SUM (oc.orderCount) orderCount,
                SUM (s.salesVolume) salesVolume,
                SUM (s.refundCount) refundCount,
                SUM (s.freeCount) freeCount,
                SUM (s.salesAmount) salesAmount,
                SUM (s.discountPrice) discountPrice,
                SUM (s.grossProfitAmount) grossProfitAmount
            FROM
                (
                SELECT * FROM item_sale
                UNION ALL
                SELECT * FROM sub_item_sale
                ) s
                LEFT JOIN order_count oc ON oc.sku_guid = s.sku_guid
            GROUP BY
                <if test="query.groupByStoreFlag != null and query.groupByStoreFlag">
                    s.store_guid,
                </if>
                s.itemName,
                s.typeName,
                s.itemTypeName,
                s.unit
        ) a
    </select>


    <sql id="storeSaleStatisticsSql">
        WITH
        -- 商品分类
        item_type AS (
            SELECT
                hs.guid sku_guid,
                hi."name" AS goodsName,
                CASE
                    WHEN ht_p.guid IS NOT NULL THEN
                    ht_p.guid ELSE ht.guid
                END AS type_guid,
                CASE
                    WHEN ht_p.guid IS NOT NULL THEN
                    ht_p.NAME ELSE ht.NAME
                END AS type_name
            FROM
                "hsi_item_${query.enterpriseGuid}_db".hsi_type ht
                LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_type ht_p ON ht_p.guid = ht.parent_guid
                LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_item hi ON ht.guid = hi.type_guid
                LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_sku hs ON hs.item_guid = hi.guid
            WHERE
                hs.guid IS NOT NULL
        ),
        item_attr AS(
            SELECT
                ia.order_guid,
                ia.order_item_guid,
                round( SUM ( COALESCE(ia.attr_price,0) ) ,2) attrTotal
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
                    AND oi.is_delete = 0
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_item_attr" ia ON ia.order_guid = o.guid
                    AND ia.order_item_guid = oi.guid
            WHERE
                o."state" = 4
                AND o.is_delete = 0
                AND o.recovery_type IN ( 1, 3 )
                AND o.business_day >= #{query.startDate}
                <![CDATA[ AND o.business_day <= #{query.endDate} ]]>
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
            GROUP BY
                ia.order_guid,
                ia.order_item_guid
        ),
        item_sale AS(
            SELECT
                <if test="query.groupByStoreFlag != null and query.groupByStoreFlag">
                    o.store_guid,
                </if>
                oi.sku_guid,
                CASE oi.sku_name
                    WHEN '' THEN oi.item_name
                    WHEN NULL THEN oi.item_name ELSE concat ( oi.item_name, '(', oi.sku_name, ')' )
                END itemName,
                CASE oi.item_type
                    WHEN 1 THEN '套餐'
                    WHEN 2 THEN '规格'
                    WHEN 3 THEN '称重'
                    WHEN 4 THEN '单品'
                    WHEN 5 THEN '团餐'
                    ELSE '其他'
                END itemTypeName,
                oi.unit,
                it.type_name AS typeName,
                SUM ( oi.current_count - oi.refund_count + oi.free_refund_count ) AS salesVolume,
                SUM ( oi.return_count + oi.refund_count ) refundCount,
                SUM ( oi.free_count ) freeCount,
                SUM ( round(oi.price * (oi.current_count - oi.refund_count + oi.free_refund_count),2) ) +
                   round( SUM ( COALESCE(ia.attrTotal,0) * (oi.current_count - oi.refund_count + oi.free_refund_count) ) ,2 ) salesAmount,
                round( SUM ( case
                    WHEN oi.current_count = 0 THEN 0
                    when oi.return_count +oi.refund_count - oi.free_refund_count = 0 then COALESCE(oi.discount_total_price,0)
                    when oi.return_count +oi.refund_count - oi.free_refund_count > 0 then COALESCE(oi.discount_total_price,0) *
                    ((oi.current_count - oi.refund_count + oi.free_refund_count) / oi.current_count)
                    else COALESCE(oi.discount_total_price,0) end ),2 ) as discountPrice,
                round( SUM ( case
                    WHEN oi.current_count = 0 THEN 0
                    when oi.return_count +oi.refund_count - oi.free_refund_count = 0 then (COALESCE(oi.discount_total_price,0) - COALESCE(oie.groupon_discount_total_price,0))
                    when oi.return_count +oi.refund_count - oi.free_refund_count > 0 then ((COALESCE(oi.discount_total_price,0) - COALESCE(oie.groupon_discount_total_price,0))) *
                    ((oi.current_count - oi.refund_count + oi.free_refund_count) / oi.current_count)
                    else (COALESCE(oi.discount_total_price,0) - COALESCE(oie.groupon_discount_total_price,0)) end ),2 ) as dineInDiscountPrice,
                round( SUM ( case
                    WHEN oi.current_count = 0 THEN 0
                    when oi.return_count +oi.refund_count - oi.free_refund_count = 0 then COALESCE(oi.discount_total_price,0)
                    when oi.return_count +oi.refund_count - oi.free_refund_count > 0 then COALESCE(oi.discount_total_price,0) *
                        ((oi.current_count - oi.refund_count + oi.free_refund_count) / oi.current_count)
                    else COALESCE(oi.discount_total_price,0) end ) - COALESCE( SUM( COALESCE(oie.cost_price
                        * (oi.current_count - oi.refund_count + oi.free_refund_count),0) ) ,0 ) ,2 ) as grossProfitAmount
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid and oi.is_delete = 0
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item_extends" oie ON oie.guid = oi.guid and oie.is_delete = 0
                LEFT JOIN item_attr ia ON ia.order_guid = o.guid AND ia.order_item_guid = oi.guid
                LEFT JOIN item_type it on it.sku_guid = oi.sku_guid
            WHERE
                o."state" = 4
                AND o.is_delete = 0
                AND oi.parent_item_guid = '0'
                AND o.recovery_type IN ( 1, 3 )
                AND o.business_day >= #{query.startDate}
                <![CDATA[ AND o.business_day <= #{query.endDate} ]]>
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemType != null">
                    and oi.item_type = #{query.itemType}
                </if>
                <if test="query.typeGuid != null and query.typeGuid != ''">
                    and it.type_name = #{query.typeGuid}
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    and (oi.item_name like concat('%',#{query.itemName},'%') or oi.sku_name like
                    concat('%',#{query.itemName},'%') )
                </if>
                <if test="query.pkgItemGuid != null and query.pkgItemGuid != ''">
                    AND 1=2
                </if>
                AND oi.item_type != 1
            GROUP BY
                <if test="query.groupByStoreFlag != null and query.groupByStoreFlag">
                    o.store_guid,
                </if>
                oi.sku_guid,
                oi.item_name,
                it.type_name,
                oi.sku_name,
                oi.unit,
                oi.item_type
        ),
        pack_item AS(
            SELECT
                oi.guid,
                oi.order_guid,
                oi.current_count,
                oi.return_count,
                oi.refund_count,
                oi.free_refund_count,
                oi.free_count,
                oi.discount_total_price
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid AND oi.is_delete = 0
            WHERE
                o."state" = 4
                AND o.is_delete = 0
                AND o.recovery_type IN ( 1, 3 )
                AND o.business_day >= #{query.startDate}
                AND o.business_day <![CDATA[ <= ]]> #{query.endDate}
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test='query.pkgItemGuid != null and query.pkgItemGuid != "" and query.pkgItemGuid != "0" '>
                    AND oi.item_guid = #{query.pkgItemGuid}
                </if>
                AND oi.parent_item_guid = '0'
                AND oi.item_type in (1, 5)
        ),
        sub_item_sale AS(
            SELECT
                <if test="query.groupByStoreFlag != null and query.groupByStoreFlag">
                    o.store_guid,
                </if>
                oi.sku_guid,
                CASE oi.sku_name
                    WHEN '' THEN oi.item_name
                    WHEN NULL THEN oi.item_name ELSE concat ( oi.item_name, '(', oi.sku_name, ')' )
                END itemName,
                CASE oi.item_type
                    WHEN 1 THEN '套餐'
                    WHEN 2 THEN '规格'
                    WHEN 3 THEN '称重'
                    WHEN 4 THEN '单品'
                    WHEN 5 THEN '团餐'
                    ELSE '其他'
                END itemTypeName,
                oi.unit,
                it.type_name AS typeName,
                SUM ( round( ( pi.current_count - pi.refund_count + pi.free_refund_count ) * oi.current_count *
                    oi.package_default_count ,3)
                ) AS salesVolume,
                SUM ( round( pi.refund_count * oi.current_count * oi.package_default_count ,3) ) refundCount,
                SUM ( round( pi.free_count * oi.current_count * oi.package_default_count ,3) ) freeCount,
                SUM ( round(oi.price * round( ( pi.current_count - pi.refund_count + pi.free_refund_count ) * oi.current_count * oi.package_default_count ,3) ,2)  +
                     round(COALESCE(oi.add_price,0) * (pi.current_count - pi.refund_count + pi.free_refund_count) * oi.current_count ,2) +
                     round(COALESCE(ia.attrTotal,0) * (pi.current_count - pi.refund_count + pi.free_refund_count) * oi.current_count * oi.package_default_count
                ,2) ) salesAmount,
                round( SUM ( case
                    WHEN pi.current_count = 0 THEN 0
                    when pi.return_count +pi.refund_count - pi.free_refund_count = 0 then COALESCE(oi.discount_total_price,0)
                    when pi.return_count +pi.refund_count - pi.free_refund_count > 0 then COALESCE(oi.discount_total_price,0) *
                    ((pi.current_count - pi.refund_count + pi.free_refund_count) / pi.current_count)
                    else COALESCE(oi.discount_total_price,0) end ),2 ) as discountPrice,
                round( SUM ( case
                    WHEN pi.current_count = 0 THEN 0
                    when pi.return_count +pi.refund_count - pi.free_refund_count = 0 then (COALESCE(oi.discount_total_price,0) - COALESCE(oie.groupon_discount_total_price,0))
                    when pi.return_count +pi.refund_count - pi.free_refund_count > 0 then (COALESCE(oi.discount_total_price,0) - COALESCE(oie.groupon_discount_total_price,0)) *
                    ((pi.current_count - pi.refund_count + pi.free_refund_count) / pi.current_count)
                    else (COALESCE(oi.discount_total_price,0) - COALESCE(oie.groupon_discount_total_price,0)) end ),2 ) as dineInDiscountPrice,
                round(
                    round( SUM ( case
                    WHEN pi.current_count = 0 THEN 0
                    when pi.return_count +pi.refund_count - pi.free_refund_count = 0 then COALESCE(oi.discount_total_price,0)
                    when pi.return_count +pi.refund_count - pi.free_refund_count > 0 then COALESCE(oi.discount_total_price,0) *
                    ((pi.current_count - pi.refund_count + pi.free_refund_count) / pi.current_count)
                    else COALESCE(oi.discount_total_price,0) end ),2 )
                    -
                    COALESCE( SUM( COALESCE(oie.cost_price *
                        round( ( pi.current_count - pi.refund_count + pi.free_refund_count ) * oi.current_count * oi.package_default_count ,3)
                    ,0) ) ,0 )
                ,2 ) as grossProfitAmount
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid and oi.is_delete = 0
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item_extends" oie ON oie.guid = oi.guid and oie.is_delete = 0
                LEFT JOIN item_type it on it.sku_guid = oi.sku_guid
                LEFT JOIN pack_item pi ON pi.guid = oi.parent_item_guid AND o.guid = pi.order_guid
                LEFT JOIN item_attr ia ON ia.order_guid = o.guid AND ia.order_item_guid = oi.guid
            WHERE
                o."state" = 4
                AND o.is_delete = 0
                AND oi.parent_item_guid != '0'
                AND o.recovery_type IN ( 1, 3 )
                AND o.business_day >= #{query.startDate}
                AND o.business_day <![CDATA[ <= ]]> #{query.endDate}
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemType != null">
                    and oi.item_type = #{query.itemType}
                </if>
                <if test="query.typeGuid != null and query.typeGuid != ''">
                    and it.type_name = #{query.typeGuid}
                </if>
                <if test="query.itemName != null and query.itemName != ''">
                    and (oi.item_name like concat('%',#{query.itemName},'%') or oi.sku_name like
                    concat('%',#{query.itemName},'%') )
                </if>
                AND oi.item_type != 1
            GROUP BY
                <if test="query.groupByStoreFlag != null and query.groupByStoreFlag">
                    o.store_guid,
                </if>
                oi.sku_guid,
                oi.item_name,
                it.type_name,
                oi.sku_name,
                oi.unit,
                oi.item_type
        ),
        order_count AS(
            SELECT
                <if test="query.groupByStoreFlag != null and query.groupByStoreFlag">
                    o.store_guid,
                </if>
                oi.sku_guid,
                COUNT ( DISTINCT o.guid ) AS orderCount
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid and oi.is_delete = 0
            WHERE
                o."state" = 4
                AND o.is_delete = 0
                AND o.recovery_type IN ( 1, 3 )
                AND o.business_day >= #{query.startDate}
                AND o.business_day <![CDATA[ <= ]]> #{query.endDate}
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                AND oi.item_type != 1
            GROUP BY
                <if test="query.groupByStoreFlag != null and query.groupByStoreFlag">
                    o.store_guid,
                </if>
                oi.sku_guid
        )
    </sql>

    <select id="getStoreSalesTotal" resultType="com.holderzone.saas.store.dto.report.resp.GoodsSalesTotalDTO">
        <include refid="storeSaleStatisticsSql"/>
        SELECT
            COALESCE ( SUM ( s.salesVolume ), 0 ) AS totalSalesVolume,
            COALESCE ( SUM ( s.salesAmount ), 0 ) totalReceivedSumPrice,
            round( COALESCE ( SUM ( s.discountPrice ), 0 ), 2 ) totalDiscountPrice,
            round( COALESCE ( SUM ( s.dineInDiscountPrice ), 0 ), 2 ) totalDineInDiscountPrice,
            round( COALESCE ( SUM ( s.grossProfitAmount ), 0 ), 2 ) totalGrossProfitAmount
        FROM
        (
            SELECT * FROM item_sale
            UNION ALL
            SELECT * FROM sub_item_sale
        ) s
    </select>


    <select id="pageStoreSaleStatistics"
            resultType="com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO">
        <include refid="storeSaleStatisticsSql"/>
        SELECT
            a.*,
            CASE
                COALESCE ( a.salesAmount, 0 )
            WHEN 0 THEN 0
                ELSE round( 100 * COALESCE ( a.salesAmount, 0 ) * 1.00 / #{query.totalSalesAmount}, 2 )
            END AS salesProportion,
            CASE
                WHEN COALESCE ( a.dineInDiscountPrice, 0 ) = 0 THEN 0
                WHEN #{query.totalDineInDiscountPrice} = 0 THEN 0
            ELSE round( 100 * COALESCE ( a.dineInDiscountPrice, 0 ) * 1.00 / #{query.totalDineInDiscountPrice}, 2 )
            END AS salesDineInProportion,
            CASE
                COALESCE ( a.orderCount, 0 )
            WHEN 0 THEN 0
                ELSE round( 100 * COALESCE ( a.orderCount, 0 ) * 1.00 / #{query.totalOrderCount}, 2 )
            END AS spotRate
        FROM
        (
            SELECT
                s.itemName,
                s.typeName,
                s.itemTypeName,
                s.unit,
                SUM (oc.orderCount) orderCount,
                SUM (s.salesVolume) salesVolume,
                SUM (s.refundCount) refundCount,
                SUM (s.freeCount) freeCount,
                SUM (s.salesAmount) salesAmount,
                SUM (s.discountPrice) discountPrice,
                SUM (s.dineInDiscountPrice) dineInDiscountPrice,
                SUM (s.grossProfitAmount) grossProfitAmount
            FROM
            (
                SELECT * FROM item_sale
                UNION ALL
                SELECT * FROM sub_item_sale
            ) s
        LEFT JOIN order_count oc ON oc.sku_guid = s.sku_guid
            GROUP BY
            <if test="query.groupByStoreFlag != null and query.groupByStoreFlag">
                s.store_guid,
            </if>
            s.itemName,
            s.typeName,
            s.itemTypeName,
            s.unit
        ) a
        <choose>
            <when test="query.sort != null and query.sortName != null and query.sort != 0">
                ORDER BY ${query.sortName}
                <if test="query.sort == 1">
                    ASC
                </if>
                <if test="query.sort == 2">
                    DESC
                </if>
            </when>
            <otherwise>
                ORDER BY itemName DESC
            </otherwise>
        </choose>
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <select id="queryItemTypeStatistics2" resultType="com.holderzone.saas.store.dto.report.resp.GoodsSalesDTO">
        SELECT
        o.store_name AS storeName,
        o.store_guid,
        <if test="query.isExport != null and query.isExport">
            CASE
            o.trade_mode
            WHEN 0 THEN
            '正餐'
            WHEN 1 THEN
            '快餐' ELSE'外卖'
            END AS tradeMode,
        </if>
        <if test="query.type == 1">
            oi.item_name as goodsName,
            oi.item_guid as itemGuid,
            oi.sku_guid as skuGuid,
        </if>
        max( oi.price ) as sellingPrise,
        oi.item_type_name AS goodsCategories,
        SUM ( oi.current_count ) AS salesVolume,
        SUM ( oi.attr_total ) AS propertyExtraPrice,
        SUM ( oi.price * oi.current_count ) AS actualReceivedPrice
        FROM
        "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
        <include refid="itemTypeSql"/>
        GROUP BY
        o.store_name,
        o.store_guid,
        <if test="query.type == 1">
            oi.item_guid,
            oi.item_name,
            oi.sku_guid,
        </if>
        oi.item_type_name
        <if test="query.isExport != null and query.isExport">
            ,
            CASE
            o.trade_mode
            WHEN 0 THEN
            '正餐'
            WHEN 1 THEN
            '快餐' ELSE'外卖'
            END
        </if>
    </select>

    <select id="getGoodsSalesTotal" resultType="com.holderzone.saas.store.dto.report.resp.GoodsSalesTotalDTO">
        SELECT
        SUM ( oi.current_count ) AS totalSalesVolume,
        SUM ( oi.price * oi.current_count ) AS totalReceivedSumPrice
        FROM
        "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
        <include refid="itemTypeSql"/>
    </select>

    <select id="queryItemTypeStatistics" resultType="com.holderzone.saas.store.dto.report.resp.GoodsSalesDTO">
        <include refid="itemTypePrePare"/>
        -- 外卖和堂食商品类型统计
        SELECT
            item_type.type_guid AS "goodsCategoriesGuid",
            item_type.type_name AS "goodsCategories",
            item_type.storeName,
            item_type.brandName,
            item_type.store_guid,
            -- item_type.tradeMode,
            -- item_type.sellingPrise,
            round(SUM ( COALESCE ( item_type.actualReceivedPrice, 0 ) ),2) actualReceivedPrice,
            round(SUM ( COALESCE ( item_type.discountPrice, 0 ) ),2) discountPrice,
            round(SUM ( COALESCE ( item_type.grossProfitAmount, 0 ) ),2) grossProfitAmount,
            SUM ( COALESCE ( item_type.salesVolume, 0 ) ) salesVolume,
            CASE
                SUM ( COALESCE ( item_type.salesVolume, 0 ) )
            WHEN 0 THEN 0 || '%'
            ELSE round( 100 * SUM ( COALESCE ( item_type.actualReceivedPrice, 0 ) ) * 1.00 / SUM ( SUM ( COALESCE ( item_type.actualReceivedPrice, 0 ) ) ) OVER ( ), 2 ) || '%'
            END AS chosenRate
        FROM (
            -- 外卖商品分类
                (
                SELECT
                    it.type_guid,
                    it.type_name,
                    takeaway.storeName,
                    takeaway.brandName,
                    takeaway.store_guid,
                    -- takeaway.tradeMode,
                    -- takeaway.sellingPrise,
                    SUM ( COALESCE ( takeaway.actualReceivedPrice, 0 ) ) actualReceivedPrice,
                    SUM ( COALESCE ( takeaway.actualReceivedPrice, 0 ) ) discountPrice,
                    SUM ( COALESCE ( takeaway.actualReceivedPrice, 0 ) - COALESCE ( takeaway.costAmount, 0 ) ) grossProfitAmount,
                    SUM ( COALESCE ( takeaway.salesVolume, 0 ) ) salesVolume
                FROM
                    takeaway
                LEFT JOIN item_type it ON it.sku_guid = takeaway.item_sku
                GROUP BY
                    -- takeaway.tradeMode,
                    -- takeaway.sellingPrise,
                    takeaway.storeName,
                    takeaway.brandName,
                    takeaway.store_guid,
                    it.type_guid,
                    it.type_name
                )
                UNION ALL-- 堂食分类
                (
                SELECT
                    it.type_guid,
                    it.type_name type_name,
                    td.storeName,
                    td.brandName,
                    td.store_guid,
                    -- td.tradeMode,
                    -- td.sellingPrise,
                    SUM ( COALESCE ( td.actualReceivedPrice, 0 ) ) actualReceivedPrice,
                    SUM ( COALESCE ( td.discountPrice, 0 ) ) discountPrice,
                    SUM ( COALESCE ( td.discountPrice, 0 ) - COALESCE ( td.costAmount, 0 ) ) grossProfitAmount,
                    SUM ( COALESCE ( td.salesVolume, 0 ) ) salesVolume
                FROM
                    trade td
                LEFT JOIN item_type it ON td.sku_guid = it.sku_guid
                GROUP BY
                    -- td.tradeMode,
                    -- td.sellingPrise,
                    td.storeName,
                    td.brandName,
                    td.store_guid,
                    it.type_guid,
                    it.type_name
                )
        ) AS item_type
        WHERE
            salesVolume != 0
        GROUP BY
            item_type.storeName,
            item_type.brandName,
            item_type.store_guid,
            -- item_type.tradeMode,
            -- item_type.sellingPrise,
            item_type."type_guid",
            item_type."type_name"
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <select id="queryItemTypeSalesTotal"
            resultType="com.holderzone.saas.store.dto.report.resp.GoodsSalesTotalDTO">
        <include refid="itemTypePrePare"/>
        -- 外卖和堂食商品类型统计
        SELECT
        COALESCE ( round( SUM ( COALESCE ( item_type.actualReceivedPrice, 0 ) ), 2 ), 0 ) totalReceivedSumPrice,
        COALESCE ( round( SUM ( COALESCE ( item_type.discountPrice, 0 ) ), 2 ), 0 ) totalDiscountPrice,
        COALESCE ( round( SUM ( COALESCE ( item_type.grossProfitAmount, 0 ) ), 2 ), 0 ) totalGrossProfitAmount,
        COALESCE ( SUM ( COALESCE ( item_type.salesVolume, 0 ) ), 0 ) totalSalesVolume
        FROM
        (-- 外卖商品分类
        (
        SELECT
        it.type_guid,
        it.type_name,
        SUM ( COALESCE ( takeaway.actualReceivedPrice, 0 ) ) actualReceivedPrice,
        SUM ( COALESCE ( takeaway.actualReceivedPrice, 0 ) ) discountPrice,
        SUM ( COALESCE ( takeaway.actualReceivedPrice, 0 ) - COALESCE ( takeaway.costAmount, 0 ) ) grossProfitAmount,
        SUM ( COALESCE ( takeaway.salesVolume, 0 ) ) salesVolume
        FROM
        takeaway
        LEFT JOIN item_type it ON it.sku_guid = takeaway.item_sku
        GROUP BY
        it.type_guid,
        it.type_name
        ) UNION ALL-- 堂食分类
        (
        SELECT
        it.type_guid,
        it.type_name type_name,
        SUM ( COALESCE ( td.actualReceivedPrice, 0 ) ) actualReceivedPrice,
        SUM ( COALESCE ( td.discountPrice, 0 ) ) discountPrice,
        SUM ( COALESCE ( td.discountPrice, 0 ) - COALESCE ( td.costAmount, 0 ) ) grossProfitAmount,
        SUM ( COALESCE ( td.salesVolume, 0 ) ) salesVolume
        FROM
        trade td
        LEFT JOIN item_type it ON td.sku_guid = it.sku_guid
        GROUP BY
        it.type_guid,
        it.type_name
        )
        ) AS item_type
        WHERE
        salesVolume != 0
    </select>

    <select id="queryGroupItemSaleStatistics"
            resultType="com.holderzone.saas.store.dto.report.resp.GoodsSalesDTO">
        <include refid="groupItemSalePrePare"/>
        -- 外卖和堂食商品类型统计
        SELECT
        item_type.type_guid AS "goodsCategoriesGuid",
        item_type.type_name AS "goodsCategories",
        item_type.storeName,
        item_type.goodsName,
        item_type.brandName,
        item_type.store_guid,
        -- SUM ( COALESCE ( item_type.orderCount, 0 ) ) orderCount,
        round( SUM ( COALESCE ( item_type.discountPrice, 0 ) ), 2 ) discountPrice,
        round( SUM ( COALESCE ( item_type.actualReceivedPrice, 0 ) ), 2 ) actualReceivedPrice,
        SUM ( COALESCE ( item_type.salesVolume, 0 ) ) salesVolume,
        CASE
        SUM ( COALESCE ( item_type.orderCount, 0 ) )
        WHEN 0 THEN 0 || '%'
        ELSE round( 100 * SUM ( COALESCE ( item_type.orderCount, 0 ) ) * 1.00 / #{query.totalOrderCount}, 2 ) || '%'
        END AS chosenRate
        FROM
        (-- 外卖商品分类
        (
        SELECT
        it.type_guid,
        it.type_name,
        takeaway.storeName,
        takeaway.brandName,
        takeaway.store_guid,
        it.goodsName,
        SUM ( COALESCE ( takeaway.orderCount, 0 ) ) orderCount,
        SUM ( COALESCE ( takeaway.actualReceivedPrice, 0 ) ) discountPrice,
        SUM ( COALESCE ( takeaway.actualReceivedPrice, 0 ) ) actualReceivedPrice,
        SUM ( COALESCE ( takeaway.salesVolume, 0 ) ) salesVolume
        FROM
        takeaway
        LEFT JOIN item_type it ON it.sku_guid = takeaway.item_sku
        WHERE 1=1
        <if test="query.goodsNames != null and query.goodsNames != ''">
            and it.goodsName LIKE concat('%',#{query.goodsNames},'%')
        </if>
        GROUP BY
        takeaway.storeName,
        takeaway.brandName,
        takeaway.store_guid,
        it.type_guid,
        it.goodsName,
        it.type_name
        ) UNION ALL-- 堂食分类
        (
        SELECT
        it.type_guid,
        it.type_name type_name,
        td.storeName,
        td.brandName,
        td.store_guid,
        it.goodsName,
        SUM ( COALESCE ( td.orderCount, 0 ) ) orderCount,
        SUM ( COALESCE ( td.discountPrice, 0 ) ) discountPrice,
        SUM ( COALESCE ( td.actualReceivedPrice, 0 ) ) actualReceivedPrice,
        SUM ( COALESCE ( td.salesVolume, 0 ) ) salesVolume
        FROM
        trade td
        LEFT JOIN item_type it ON td.sku_guid = it.sku_guid
        WHERE 1=1
        <if test="query.goodsNames != null and query.goodsNames != ''">
            and it.goodsName LIKE concat('%',#{query.goodsNames},'%')
        </if>
        GROUP BY
        td.storeName,
        td.brandName,
        td.store_guid,
        it.type_guid,
        it.goodsName,
        it.type_name
        )
        ) AS item_type
        WHERE
        salesVolume != 0
        GROUP BY
        item_type.storeName,
        item_type.brandName,
        item_type.store_guid,
        item_type.goodsName,
        item_type."type_guid",
        item_type."type_name"
    </select>

    <select id="getGroupItemSaleTotal"
            resultType="com.holderzone.saas.store.dto.report.resp.GoodsSalesTotalDTO">
        <include refid="groupItemSalePrePare"/>
        -- 外卖和堂食商品类型统计
        SELECT
        COALESCE ( round( SUM ( COALESCE ( item_type.actualReceivedPrice, 0 ) ), 2 ), 0 ) totalReceivedSumPrice,
        COALESCE ( SUM ( COALESCE ( item_type.salesVolume, 0 ) ), 0 ) totalSalesVolume
        FROM
        (-- 外卖商品分类
        (
        SELECT SUM
        ( COALESCE ( takeaway.actualReceivedPrice, 0 ) ) actualReceivedPrice,
        SUM ( COALESCE ( takeaway.salesVolume, 0 ) ) salesVolume
        FROM
        takeaway
        LEFT JOIN item_type it ON it.sku_guid = takeaway.item_sku
        ) UNION ALL-- 堂食分类
        (
        SELECT SUM
        ( COALESCE ( td.actualReceivedPrice, 0 ) ) actualReceivedPrice,
        SUM ( COALESCE ( td.salesVolume, 0 ) ) salesVolume
        FROM
        trade td
        LEFT JOIN item_type it ON td.sku_guid = it.sku_guid
        )
        ) AS item_type
        WHERE
        salesVolume != 0
    </select>

    <select id="pageStoreSaleStatisticsType" resultType="java.lang.String">
        WITH
        -- 商品分类
        item_type AS (
        SELECT
        hs.guid sku_guid,
        hi."name" AS goodsName,
        CASE

        WHEN ht_p.guid IS NOT NULL THEN
        ht_p.guid ELSE ht.guid
        END AS type_guid,
        CASE

        WHEN ht_p.guid IS NOT NULL THEN
        ht_p.NAME ELSE ht.NAME
        END AS type_name
        FROM
        "hsi_item_${query.enterpriseGuid}_db".hsi_type ht
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_type ht_p ON ht_p.guid = ht.parent_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_item hi ON ht.guid = hi.type_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_sku hs ON hs.item_guid = hi.guid
        WHERE
        hs.guid IS NOT NULL
        )

        -- 门店商品销量分类
        SELECT
        DISTINCT it.type_name
        FROM
        "hst_trade_${query.enterpriseGuid}_db".hst_order o
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
        LEFT JOIN item_type it ON it.sku_guid = oi.sku_guid
        WHERE
        1=1
        AND o."state" = 4
        AND o.is_delete = 0
        AND oi.parent_item_guid = '0'
        AND oi.item_type != 1
        AND o.recovery_type IN ( 1, 3 )
        AND o.business_day >= #{query.startDate}
        <![CDATA[ AND o.business_day <= #{query.endDate} ]]>
        <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
            AND o.store_guid IN
            <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
        <if test="query.itemType != null">
            and oi.item_type = #{query.itemType}
        </if>
    </select>

    <select id="queryItemTypeStatisticsCount" resultType="java.lang.Long">
        <include refid="itemTypePrePare"/>
        SELECT
        count(1)
        FROM
        (
            SELECT
            count(1)
            FROM
            (
                -- 外卖商品分类
                (
                    SELECT
                        it.type_guid,
                        it.type_name,
                        takeaway.storeName,
                        takeaway.brandName,
                        takeaway.store_guid,
                        SUM ( COALESCE ( takeaway.actualReceivedPrice, 0 ) ) actualReceivedPrice,
                        SUM ( COALESCE ( takeaway.salesVolume, 0 ) ) salesVolume
                    FROM
                        takeaway
                        LEFT JOIN item_type it ON it.sku_guid = takeaway.item_sku
                    GROUP BY
                        takeaway.storeName,
                        takeaway.brandName,
                        takeaway.store_guid,
                        it.type_guid,
                        it.type_name
                )
                UNION ALL
                -- 堂食分类
                (
                    SELECT
                        it.type_guid,
                        it.type_name type_name,
                        td.storeName,
                        td.brandName,
                        td.store_guid,
                        SUM ( COALESCE ( td.actualReceivedPrice, 0 ) ) actualReceivedPrice,
                        SUM ( COALESCE ( td.salesVolume, 0 ) ) salesVolume
                    FROM
                        trade td
                        LEFT JOIN item_type it ON td.sku_guid = it.sku_guid
                    GROUP BY
                        td.storeName,
                        td.brandName,
                        td.store_guid,
                        it.type_guid,
                        it.type_name
                )
            ) AS item_type
            WHERE
                salesVolume != 0
            GROUP BY
                item_type.storeName,
                item_type.brandName,
                item_type.store_guid,
                item_type."type_guid",
                item_type."type_name"
        ) as gr
    </select>

    <select id="queryGroupItemSaleStatisticsCount" resultType="java.lang.Long">
        <include refid="groupItemSalePrePare"/>
        SELECT
            count(1)
        FROM
        (
            SELECT
                count(1)
            FROM
            (
                -- 外卖商品分类
                (
                    SELECT
                        it.type_guid,
                        it.type_name,
                        takeaway.storeName,
                        takeaway.brandName,
                        takeaway.store_guid,
                        it.goodsName,
                        SUM ( COALESCE ( takeaway.orderCount, 0 ) ) orderCount,
                        SUM ( COALESCE ( takeaway.actualReceivedPrice, 0 ) ) actualReceivedPrice,
                        SUM ( COALESCE ( takeaway.salesVolume, 0 ) ) salesVolume
                    FROM
                        takeaway
                        LEFT JOIN item_type it ON it.sku_guid = takeaway.item_sku
                    WHERE 1=1
                        <if test="query.goodsNames != null and query.goodsNames != ''">
                            and it.goodsName LIKE concat('%',#{query.goodsNames},'%')
                        </if>
                    GROUP BY
                        takeaway.storeName,
                        takeaway.brandName,
                        takeaway.store_guid,
                        it.type_guid,
                        it.goodsName,
                        it.type_name
                )
                UNION ALL
                -- 堂食分类
                (
                    SELECT
                        it.type_guid,
                        it.type_name type_name,
                        td.storeName,
                        td.brandName,
                        td.store_guid,
                        it.goodsName,
                        SUM ( COALESCE ( td.orderCount, 0 ) ) orderCount,
                        SUM ( COALESCE ( td.actualReceivedPrice, 0 ) ) actualReceivedPrice,
                        SUM ( COALESCE ( td.salesVolume, 0 ) ) salesVolume
                    FROM
                        trade td
                        LEFT JOIN item_type it ON td.sku_guid = it.sku_guid
                    WHERE 1=1
                        <if test="query.goodsNames != null and query.goodsNames != ''">
                            and it.goodsName LIKE concat('%',#{query.goodsNames},'%')
                        </if>
                    GROUP BY
                        td.storeName,
                        td.brandName,
                        td.store_guid,
                        it.type_guid,
                        it.goodsName,
                        it.type_name
                )
            ) AS item_type
            WHERE
                salesVolume != 0
            GROUP BY
                item_type.storeName,
                item_type.brandName,
                item_type.store_guid,
                item_type.goodsName,
                item_type."type_guid",
                item_type."type_name"
        ) as gr
    </select>

    <sql id="groupItemSalePrePare">
        WITH
        -- 映射数量
<!--        typeMapping AS (-->
<!--        SELECT-->
<!--        ti.order_sub_type,-->
<!--        CASE-->
<!--        ti.order_sub_type-->
<!--        WHEN 0 THEN-->
<!--        0-->
<!--        WHEN 1 THEN-->
<!--        1 ELSE 3-->
<!--        END AS takeout_type-->
<!--        FROM-->
<!--        "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_item ti-->
<!--        JOIN "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_order ord ON ti.order_guid = ord.order_guid-->
<!--        WHERE-->
<!--        1=1-->
<!--        AND ord.business_day >= #{query.startTime}-->
<!--        <![CDATA[ AND ord.business_day <= #{query.endTime} ]]>-->
<!--        AND ti.business_day >= #{query.startTime}-->
<!--        <![CDATA[ AND ti.business_day <= #{query.endTime} ]]>-->
<!--        <if test="query.storeGuid != null and query.storeGuid.size()>0 ">-->
<!--            AND ti.store_guid IN-->
<!--            <foreach collection="query.storeGuid" item="storeGuid" open="(" separator="," close=")">-->
<!--                #{storeGuid}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        AND ord.order_status != '-1'-->
<!--        AND ord.refund_status != '2'-->
<!--        AND ti.erp_item_sku_guid IS NOT NULL-->
<!--        ),-->
        -- 外卖
        takeaway AS (
        SELECT COUNT
        ( DISTINCT ord.order_guid ) AS orderCount,
        ti.erp_item_sku_guid item_sku,
        ti.store_name AS storeName,
        ti.store_guid,
        b."name" AS brandName,
<!--        ti.item_price  AS sellingPrise,-->
        SUM ( round( ti.erp_item_price * (
        COALESCE ( ti.actual_item_count, 0 ) - (COALESCE ( ti.refund_count, 0 ) ) * (COALESCE ( ti.actual_item_count, 1 )/COALESCE ( ti.item_count, 1 ) )
        ) , 2 )) AS actualReceivedPrice,
        SUM (
            (
            COALESCE ( ti.actual_item_count, 0 ) - (COALESCE ( ti.refund_count, 0 ) ) * (COALESCE ( ti.actual_item_count, 1 )/COALESCE ( ti.item_count, 1 ) )
            )
        ) salesVolume
        FROM
        "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_item ti
        JOIN "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_order ord ON ti.order_guid = ord.order_guid
<!--        LEFT JOIN typeMapping mp ON mp.order_sub_type = ti.order_sub_type-->
<!--        LEFT JOIN "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_item_mapping tim ON tim.erp_item_sku_guid = ti.erp_item_sku_guid-->
<!--        AND tim.store_guid = ti.store_guid-->
<!--        AND tim.takeout_type = mp.takeout_type-->
<!--        LEFT JOIN "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_item_bind_extend_info ibei ON ibei.store_guid = ti.store_guid-->
<!--        AND ibei.erp_item_sku_id = tim.mapper_guid-->
<!--        AND ibei.takeout_type = mp.takeout_type-->
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" rsb ON rsb.store_guid =
        ti.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db".hso_brand b ON b.guid = rsb.brand_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_sku hs ON hs.guid = ti.erp_item_sku_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_item hi ON hi.guid = hs.item_guid
        WHERE
        1=1
        AND ord.business_day >= #{query.startTime}
        <![CDATA[ AND ord.business_day <= #{query.endTime} ]]>
        AND ti.business_day >= #{query.startTime}
        <![CDATA[ AND ti.business_day <= #{query.endTime} ]]>
        <if test="query.storeGuid != null and query.storeGuid.size()>0 ">
            AND ti.store_guid IN
            <foreach collection="query.storeGuid" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
        <if test="query.cateringType != null and query.cateringType != 2">
            AND 1=2
        </if>
        AND ord.order_status != '-1'
        AND ord.refund_status != '2'
        AND ti.erp_item_sku_guid IS NOT NULL
        AND hi.item_type IN ( 1, 5 )
        GROUP BY
        b."name",
        ti.erp_item_sku_guid,
        ti.store_guid,
        ti.item_price,
        ti.store_name
        ),
        -- 商品分类
        item_type AS (
        SELECT
        hs.guid sku_guid,
        hi."name" AS goodsName,
        CASE

        WHEN ht_p.guid IS NOT NULL THEN
        ht_p.guid ELSE ht.guid
        END AS type_guid,
        CASE

        WHEN ht_p.guid IS NOT NULL THEN
        ht_p.NAME ELSE ht.NAME
        END AS type_name
        FROM
        "hsi_item_${query.enterpriseGuid}_db".hsi_type ht
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_type ht_p ON ht_p.guid = ht.parent_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_item hi ON ht.guid = hi.type_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_sku hs ON hs.item_guid = hi.guid
        WHERE
        hs.guid IS NOT NULL
        ),
        -- 堂食
        trade AS (
        SELECT COUNT
        ( DISTINCT o.guid ) AS orderCount,
        oi.sku_guid,
        o.store_name AS storeName,
        o.store_guid,
        b."name" AS brandName,
<!--        MAX ( oi.price ) + SUM ( suboi.add_price ) AS sellingPrise,-->
        SUM ( oi.current_count - oi.refund_count ) AS salesVolume,
        round( SUM ( case
            WHEN oi.current_count = 0 THEN 0
            when oi.return_count +oi.refund_count - oi.free_refund_count = 0 then COALESCE(oi.discount_total_price,0)
            when oi.return_count +oi.refund_count - oi.free_refund_count > 0 then COALESCE(oi.discount_total_price,0) *
                ((oi.current_count - oi.refund_count + oi.free_refund_count) / oi.current_count)
            else COALESCE(oi.discount_total_price,0) end )
        ,2 ) as discountPrice,
        SUM ( oi.price * (oi.current_count - oi.refund_count) ) + SUM ( COALESCE(suboi.add_price,0) * (oi.current_count - oi.refund_count) ) AS actualReceivedPrice
        FROM
        "hst_trade_${query.enterpriseGuid}_db".hst_order o
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
        LEFT JOIN (
        SELECT
        parent_item_guid,
        SUM ( add_price ) AS add_price
        FROM
        "hst_trade_${query.enterpriseGuid}_db".hst_order_item
        WHERE
        parent_item_guid <![CDATA[ <> ]]> '0'
        AND business_day >= #{query.startTime}
        <![CDATA[ AND business_day <= #{query.endTime} ]]>
        GROUP BY parent_item_guid
        ) suboi ON suboi.parent_item_guid = oi.guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" rsb ON rsb.store_guid = o.store_guid
        LEFT JOIN hso_organization_${query.enterpriseGuid}_db.hso_brand b ON b.guid = rsb.brand_guid
        WHERE
        1 = 1
        AND oi.current_count != 0
        AND o.business_day >= #{query.startTime}
        <![CDATA[ AND o.business_day <= #{query.endTime} ]]>
        <if test="query.storeGuid != null and query.storeGuid.size()>0 ">
            AND o.store_guid IN
            <foreach collection="query.storeGuid" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
        <if test="query.cateringType != null">
            AND o.trade_mode = #{query.cateringType}
        </if>
        AND oi.item_type IN ( 1, 5 )
        AND oi.parent_item_guid = '0'
        AND o."state" = 4
        AND o.recovery_type IN ( 1, 3 )
        AND oi.is_delete = '0'
        GROUP BY
        oi.sku_guid,
        o.store_name,
        o.store_guid,
        b."name",
        oi.item_type_name
        )
    </sql>

    <sql id="itemTypePrePare">
        WITH
        takeaway AS (
        SELECT
        ti.erp_item_sku_guid item_sku,
        ti.store_name AS storeName,
        ti.store_guid,b."name" AS brandName,
        SUM ( round( ti.erp_item_price * (
        COALESCE ( ti.actual_item_count, 0 ) - (COALESCE ( ti.refund_count, 0 ) ) * (COALESCE ( ti.actual_item_count, 1 )/COALESCE ( ti.item_count, 1 ) )
        ) , 2 )) AS actualReceivedPrice,
        SUM (
            (
                COALESCE ( ti.actual_item_count, 0 ) - (COALESCE ( ti.refund_count, 0 ) ) * (COALESCE ( ti.actual_item_count, 1 )/COALESCE ( ti.item_count, 1 ) )
            )
        ) salesVolume,
        round( COALESCE( SUM( COALESCE(tie.cost_price * (
        COALESCE ( ti.actual_item_count, 0 ) - (COALESCE ( ti.refund_count, 0 ) ) * (COALESCE ( ti.actual_item_count, 1 )/COALESCE ( ti.item_count, 1 ) )
        ),0) ) ,0) ,2) AS costAmount
        FROM
        "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_item ti
        LEFT JOIN "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_item_extends tie ON tie.guid = ti.item_guid AND tie.is_delete = 0
        JOIN "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_order ord ON ti.order_guid = ord.order_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" rsb on rsb.store_guid =
        ti.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db".hso_brand b on b.guid = rsb.brand_guid
        WHERE
        1=1
        AND ord.business_day >= #{query.startTime}
        <![CDATA[ AND ord.business_day <= #{query.endTime} ]]>
        AND ti.business_day >= #{query.startTime}
        <![CDATA[ AND ti.business_day <= #{query.endTime} ]]>
        <if test="query.storeGuid != null and query.storeGuid.size()>0 ">
            AND ti.store_guid IN
            <foreach collection="query.storeGuid" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
        <if test="query.cateringType != null and query.cateringType != 2">
            AND 1=2
        </if>
        AND ord.order_status != '-1'
        AND ord.refund_status != '2'
        AND ti.erp_item_sku_guid IS NOT NULL
        GROUP BY
        b."name",
        ti.erp_item_sku_guid,
        ti.store_guid,
        ti.store_name
        ),
        item_type AS (
        SELECT
        hs.guid sku_guid,
        CASE

        WHEN ht_p.guid IS NOT NULL THEN
        ht_p.guid ELSE ht.guid
        END AS type_guid,
        CASE

        WHEN ht_p.guid IS NOT NULL THEN
        ht_p.NAME ELSE ht.NAME
        END AS type_name
        FROM
        "hsi_item_${query.enterpriseGuid}_db".hsi_type ht
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_type ht_p ON ht_p.guid = ht.parent_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_item hi ON ht.guid = hi.type_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_sku hs ON hs.item_guid = hi.guid
        WHERE
        hs.guid IS NOT NULL
        ),
        trade AS (
        SELECT
        oi.sku_guid,
        o.store_name AS storeName,
        o.store_guid,b."name" AS brandName,
        SUM ( oi.current_count - oi.refund_count + oi.free_refund_count ) AS salesVolume,
        SUM ( round(oi.price * (oi.current_count - oi.refund_count + oi.free_refund_count) + COALESCE( a.amount, 0 ),2) ) AS actualReceivedPrice,
        SUM ( case
            when oi.refund_count - oi.free_refund_count = 0 then COALESCE(oi.discount_total_price,0)
            when oi.refund_count - oi.free_refund_count > 0 then COALESCE(oi.discount_total_price,0) * ((oi.current_count - oi.refund_count + oi.free_refund_count) / oi.current_count)
            else COALESCE(oi.discount_total_price,0) end ) as discountPrice,
        round( COALESCE( SUM( COALESCE(oie.cost_price * (oi.current_count - oi.refund_count + oi.free_refund_count),0) ) ,0) ,2) AS costAmount
        FROM
        "hst_trade_${query.enterpriseGuid}_db".hst_order o
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item_extends" oie ON oie.guid = oi.guid and oie.is_delete = 0
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" rsb on rsb.store_guid = o.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db".hso_brand b on b.guid = rsb.brand_guid
        LEFT JOIN (
            SELECT
            s.parent_item_guid guid,
            sum( s.add_price * s.current_count ) amount
            FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" s
            INNER JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" o ON o.guid = s.order_guid
            WHERE
            s.parent_item_guid != 0
            AND o.business_day >= #{query.startTime}
            <![CDATA[ AND o.business_day <= #{query.endTime} ]]>
            AND o.state = 4
            AND CASE WHEN (s.current_count = 0 AND s.free_count = 0) THEN (s.return_count = 0)	ELSE 1=1 END
            AND s.is_delete = 0
            <if test="query.storeGuid != null and query.storeGuid.size()>0 ">
                AND o.store_guid IN
                <foreach collection="query.storeGuid" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
            GROUP BY
            s.parent_item_guid
        ) a ON oi.guid = a.guid
        WHERE
        1 = 1
        AND oi.current_count != 0
        AND o.business_day >= #{query.startTime}
        <![CDATA[ AND o.business_day <= #{query.endTime} ]]>
        <if test="query.storeGuid != null and query.storeGuid.size()>0 ">
            AND o.store_guid IN
            <foreach collection="query.storeGuid" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
        <if test="query.cateringType != null">
            AND o.trade_mode = #{query.cateringType}
        </if>
        AND oi.parent_item_guid = '0'
        AND o."state" = 4
        AND o.recovery_type IN ( 1, 3 )
        AND oi.is_delete = '0'
        GROUP BY
        oi.sku_guid,
        o.store_name,
        o.store_guid,b."name" ,
        oi.item_type_name,
        CASE
        o.trade_mode
        WHEN 0 THEN
        '正餐'
        WHEN 1 THEN
        '快餐' ELSE'外卖'
        END
        )
    </sql>

    <sql id="itemTypeSql">
        <where>
            1 = 1
            AND oi.current_count != 0
            AND o."state" = 4
            AND o.is_delete = 0
            AND oi.parent_item_guid = '0'
            AND o.recovery_type IN ( 1, 3 )
            AND o.business_day >= #{query.startTime}
            <![CDATA[ AND o.business_day <= #{query.endTime} ]]>
            <if test="query.storeGuid != null and query.storeGuid.size()>0 ">
                AND o.store_guid IN
                <foreach collection="query.storeGuid" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.goodsNames != null and query.goodsNames != ''">
                AND oi.item_name LIKE concat('%',#{query.goodsNames},'%')
            </if>
            <if test="query.cateringType != null">
                AND o.trade_mode = #{query.cateringType}
            </if>
            <if test="query.itemType == 1 ">
                AND oi.item_type in(1,5)
            </if>
            AND oi.parent_item_guid = '0'
        </where>
    </sql>

    <select id="countSaleDetail" resultType="java.lang.Long">
        <include refid="saleDetailPrePare"/>
        SELECT
            COUNT( 1 )
        FROM
            (
                SELECT * FROM currentItem
                UNION ALL
                SELECT * FROM freeItem
                UNION ALL
                SELECT * FROM returnItem
            ) sd
        WHERE
            <if test="query.state != null">
                <choose>
                    <when test="query.state == 1">
                        sd."operation" = '点菜'
                    </when>
                    <when test="query.state == 2">
                        sd."operation" = '赠菜'
                    </when>
                    <when test="query.state == 3">
                        sd."operation" = '退菜'
                    </when>
                    <otherwise>
                        1 = 1
                    </otherwise>
                </choose>
            </if>
    </select>

    <select id="pageSaleDetail"
            resultType="com.holderzone.saas.store.dto.report.resp.SalesDetailRespDTO">
        <include refid="saleDetailPrePare"/>
        SELECT
            sd.itemName,
            sd.itemTypeName,
            sd.itemType,
            sd."operation",
            sd.orderNumber,
            sd.unit,
            sd.price,
            sd.attrPrice,
            sd.orderNo,
            sd.createTime,
            sd.checkoutTime 
        FROM
        (
            SELECT * FROM currentItem
            UNION ALL
            SELECT * FROM freeItem
            UNION ALL
            SELECT * FROM returnItem
        ) sd
        WHERE
            <if test="query.state != null">
                <choose>
                    <when test="query.state == 1">
                        sd."operation" = '点菜'
                    </when>
                    <when test="query.state == 2">
                        sd."operation" = '赠菜'
                    </when>
                    <when test="query.state == 3">
                        sd."operation" = '退菜'
                    </when>
                    <otherwise>
                        1 = 1
                    </otherwise>
                </choose>
            </if>
        ORDER BY
            sd.dataTime DESC
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <select id="querySaleProductDetail"
            resultType="com.holderzone.saas.store.dto.report.openapi.SaleProductDetailRespDTO">
        WITH
        packageAttr AS (
            SELECT
                i.parent_item_guid,
                COALESCE ( SUM ( case when i.item_type = 3 then ia.attr_price * ia.num else ia.attr_price * ia.num * i.package_default_count end ), 0 ) attrPrice
            FROM
                "hst_trade_${enterpriseGuid}_db"."hst_order_item" i
            LEFT JOIN "hst_trade_${enterpriseGuid}_db"."hst_item_attr" ia ON ia.order_item_guid = i.guid
            WHERE
                i.is_delete = 0
                AND i.parent_item_guid != '0'
                <if test="orderGuidList != null and orderGuidList.size()>0">
                    and i.order_guid in
                    <foreach collection="orderGuidList" item="orderGuid" open="(" close=")" separator="," >
                        #{orderGuid}
                    </foreach>
                </if>
            GROUP BY
                i.parent_item_guid,
                i.order_guid
        ),
        itemAttr AS (
            SELECT
                i.guid,
                round( COALESCE ( SUM ( ia.attr_price * ia.num ), 0 ), 2 ) attrPrice
            FROM
                "hst_trade_${enterpriseGuid}_db"."hst_order_item" i
            LEFT JOIN "hst_trade_${enterpriseGuid}_db"."hst_item_attr" ia ON ia.order_item_guid = i.guid
            WHERE
                i.is_delete = 0
                AND i.parent_item_guid = '0'
                <if test="orderGuidList != null and orderGuidList.size()>0">
                    and i.order_guid in
                    <foreach collection="orderGuidList" item="orderGuid" open="(" close=")" separator="," >
                        #{orderGuid}
                    </foreach>
                </if>
            GROUP BY
                i.guid,
                i.order_guid
        ),
        packageAddPrice AS (
            SELECT
                i.parent_item_guid,
                round( COALESCE ( SUM ( i.current_count * i.add_price ), 0 ), 2 ) addPrice
            FROM
                "hst_trade_${enterpriseGuid}_db"."hst_order_item" i
            WHERE
                i.is_delete = 0
                AND i.parent_item_guid != '0'
                <if test="orderGuidList != null and orderGuidList.size()>0">
                    and i.order_guid in
                    <foreach collection="orderGuidList" item="orderGuid" open="(" close=")" separator="," >
                        #{orderGuid}
                    </foreach>
                </if>
            GROUP BY
                i.parent_item_guid,
                i.order_guid
        )
        SELECT
            i.sku_guid,
            i.order_guid,
            CASE WHEN LENGTH ( i.sku_name ) > 0 THEN concat ( i.item_name, '(', i.sku_name, ')' ) ELSE i.item_name END AS "item_name",
            i.price * 100 AS "price",
            i.current_count,
            i.item_type_guid,
            i.item_type_name,
            (i.price * i.current_count + COALESCE ( pap.addPrice, 0 ) + COALESCE ( ia.attrPrice * i.current_count, 0 ) + COALESCE ( pa.attrPrice * i.current_count, 0 )) * 100 AS "total_price",
            (i.price * i.current_count + COALESCE ( pap.addPrice, 0 ) + COALESCE ( ia.attrPrice * i.current_count, 0 ) + COALESCE ( pa.attrPrice * i.current_count, 0 ) - i.discount_total_price)  * 100 AS "discount_fee",
            i.discount_total_price * 100 AS "actually_payFee",
            i.gmt_create
        FROM
            "hst_trade_${enterpriseGuid}_db".hst_order_item i
        LEFT JOIN itemAttr ia ON ia.guid = i.guid
        LEFT JOIN packageAttr pa ON pa.parent_item_guid = i.guid
        LEFT JOIN packageAddPrice pap ON pap.parent_item_guid = i.guid
        WHERE
            i.is_delete = '0'
            AND i.parent_item_guid = 0
            AND i.current_count > 0
            <if test="orderGuidList != null and orderGuidList.size()>0">
                and i.order_guid in
                <foreach collection="orderGuidList" item="orderGuid" open="(" close=")" separator="," >
                    #{orderGuid}
                </foreach>
            </if>
        ORDER BY
            i.guid ASC
    </select>
    <select id="pageStoreSaleStatisticsGroup"
            resultType="com.holderzone.saas.store.dto.report.resp.GoodsSalesDTO">
        WITH
        -- 商品分类
        item_type AS (
            SELECT
                hs.guid sku_guid,
                hi."name" AS goodsName,
                CASE
                    WHEN ht_p.guid IS NOT NULL THEN
                    ht_p.guid ELSE ht.guid
                END AS type_guid,
                CASE
                    WHEN ht_p.guid IS NOT NULL THEN
                    ht_p.NAME ELSE ht.NAME
                END AS type_name
            FROM
                "hsi_item_${query.enterpriseGuid}_db".hsi_type ht
                LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_type ht_p ON ht_p.guid = ht.parent_guid
                LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_item hi ON ht.guid = hi.type_guid
                LEFT JOIN "hsi_item_${query.enterpriseGuid}_db".hsi_sku hs ON hs.item_guid = hi.guid
            WHERE
                hs.guid IS NOT NULL
        )

        SELECT
            oi.item_guid,
            oi.item_name AS goodsName
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
            AND oi.is_delete = 0
            LEFT JOIN item_type it on it.sku_guid = oi.sku_guid
        WHERE
            o."state" = 4
            AND o.is_delete = 0
            AND oi.parent_item_guid = '0'
            AND o.recovery_type IN ( 1, 3 )
            AND o.business_day >= #{query.startDate}
            AND o.business_day <![CDATA[ <= ]]> #{query.endDate}
            <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                AND o.store_guid IN
                <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.itemType != null">
                and oi.item_type = #{query.itemType}
            </if>
            <if test="query.typeGuid != null and query.typeGuid != ''">
                and it.type_name = #{query.typeGuid}
            </if>
            AND oi.item_type = 1
        GROUP BY
            oi.item_guid,
            oi.item_name
    </select>


    <select id="pageGroupByStoreSaleStatistics"
            resultType="com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO">
        <include refid="storeSaleStatisticsSql"/>
        ,
        ranked_data AS (
            SELECT
                a.*,
                CASE
                    COALESCE ( a.salesAmount, 0 )
                WHEN 0 THEN 0
                    ELSE round( 100 * COALESCE ( a.salesAmount, 0 ) * 1.00 / #{query.totalSalesAmount}, 2 )
                END AS salesProportion,
                CASE
                    WHEN COALESCE ( a.dineInDiscountPrice, 0 ) = 0 THEN 0
                    WHEN #{query.totalDineInDiscountPrice} = 0 THEN 0
                    ELSE round( 100 * COALESCE ( a.dineInDiscountPrice, 0 ) * 1.00 / #{query.totalDineInDiscountPrice}, 2 )
                END AS salesDineInProportion,
                ROW_NUMBER() OVER (
                    PARTITION BY a.store_guid 
                    ORDER BY 
                    <if test="query.sort != null and query.sortName != null and query.sort != 0">
                        <choose>
                            <when test="query.sortName == 'salesVolume'">
                                CASE WHEN #{query.sort} = 1 THEN a.salesVolume END ASC,
                                CASE WHEN #{query.sort} = 2 THEN a.salesVolume END DESC
                            </when>
                            <when test="query.sortName == 'refundCount'">
                                CASE WHEN #{query.sort} = 1 THEN a.refundCount END ASC,
                                CASE WHEN #{query.sort} = 2 THEN a.refundCount END DESC
                            </when>
                            <when test="query.sortName == 'freeCount'">
                                CASE WHEN #{query.sort} = 1 THEN a.freeCount END ASC,
                                CASE WHEN #{query.sort} = 2 THEN a.freeCount END DESC
                            </when>
                            <when test="query.sortName == 'salesAmount' or query.sortName == 'salesProportion'">
                                CASE WHEN #{query.sort} = 1 THEN a.salesAmount END ASC,
                                CASE WHEN #{query.sort} = 2 THEN a.salesAmount END DESC
                            </when>
                            <when test="query.sortName == 'spotRate'">
                                CASE WHEN #{query.sort} = 1 THEN a.orderCount END ASC,
                                CASE WHEN #{query.sort} = 2 THEN a.orderCount END DESC
                            </when>
                            <when test="query.sortName == 'salesDineInProportion'">
                                CASE WHEN #{query.sort} = 1 THEN a.dineInDiscountPrice END ASC,
                                CASE WHEN #{query.sort} = 2 THEN a.dineInDiscountPrice END DESC
                            </when>
                            <otherwise>a.itemName DESC</otherwise>
                        </choose>
                    </if>
                    <if test="query.sort == null or query.sortName == null or query.sort == 0">
                        a.itemName DESC
                    </if>
                ) as rn
            FROM
            (
                SELECT
                    s.store_guid,
                    max (o.name) as "store_name",
                    max (hb.guid) as "brand_guid",
                    max (hb.name) as "brand_name",
                    s.itemName,
                    s.typeName,
                    s.itemTypeName,
                    s.unit,
                    SUM (oc.orderCount) orderCount,
                    SUM (s.salesVolume) salesVolume,
                    SUM (s.refundCount) refundCount,
                    SUM (s.freeCount) freeCount,
                    SUM (s.salesAmount) salesAmount,
                    SUM (s.discountPrice) discountPrice,
                    SUM (s.dineInDiscountPrice) dineInDiscountPrice,
                    SUM (s.grossProfitAmount) grossProfitAmount
                FROM
                (
                    SELECT * FROM item_sale
                    UNION ALL
                    SELECT * FROM sub_item_sale
                ) s
                LEFT JOIN order_count oc ON oc.sku_guid = s.sku_guid and oc.store_guid = s.store_guid
                LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = s.store_guid
                LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
                LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_brand" hb on ro.brand_guid = hb.guid and hb.is_deleted = '0'
                GROUP BY
                    s.store_guid,
                    s.itemName,
                    s.typeName,
                    s.itemTypeName,
                    s.unit
            ) a
        )
        SELECT 
            store_guid,
            store_name,
            brand_guid,
            brand_name,
            itemName,
            typeName,
            itemTypeName,
            unit,
            orderCount,
            salesVolume,
            refundCount,
            freeCount,
            salesAmount,
            discountPrice,
            dineInDiscountPrice,
            grossProfitAmount,
            salesProportion,
            salesDineInProportion
        FROM ranked_data
        ORDER BY store_guid, rn
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <sql id="saleDetailPrePare">
        WITH
        -- 套餐属性
        packageAttr AS (
            SELECT
                oi.parent_item_guid,
                round( COALESCE ( SUM ( ia.attr_price * ia.num ), 0 ), 2 ) attrPrice
            FROM
                "hst_trade_${query.enterpriseGuid}_db".hst_order o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_item_attr" ia ON ia.order_item_guid = oi.guid
            WHERE
                o.is_delete = 0
                AND oi.is_delete = 0
                AND o.checkout_time BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                AND o."state" IN ( 4, 5 )
                AND oi.parent_item_guid <![CDATA[ <> ]]> '0'
                AND o.recovery_type IN ( 1, 3, 4 )
            GROUP BY
                oi.parent_item_guid,
                oi.order_guid
        ),
        itemAttr AS (
            SELECT
                oi.guid,
                round( COALESCE ( SUM ( ia.attr_price * ia.num ), 0 ), 2 ) attrPrice
            FROM
                "hst_trade_${query.enterpriseGuid}_db".hst_order o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_item_attr" ia ON ia.order_item_guid = oi.guid
            WHERE
                o.is_delete = 0
                AND oi.is_delete = 0
                AND o.checkout_time BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                AND o."state" IN ( 4, 5 )
                AND oi.parent_item_guid = '0'
                AND o.recovery_type IN ( 1, 3, 4 )
            GROUP BY
                oi.guid,
                oi.order_guid
	    ),
        packageAddPrice AS (
            SELECT
                oi.parent_item_guid,
                round( COALESCE ( SUM ( oi.current_count * oi.add_price ), 0 ), 2 ) addPrice
            FROM
                "hst_trade_${query.enterpriseGuid}_db".hst_order o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
            WHERE
                o.is_delete = 0
                AND oi.is_delete = 0
                AND o.checkout_time BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                AND o."state" = 4
                AND oi.parent_item_guid <![CDATA[ <> ]]> '0'
                AND o.recovery_type IN ( 1, 3 )
            GROUP BY
                oi.parent_item_guid,
                oi.order_guid
        ),
        -- 点菜商品
        currentItem AS (
            SELECT
                MAX( oi.gmt_create ) dataTime,
                CASE
                    WHEN oi.sku_name = ''
                    THEN oi.item_name
                    ELSE CONCAT ( oi.item_name, '(', oi.sku_name, ')' )
                END itemName,
                oi.item_type_name itemTypeName,
                CASE oi.item_type
                    WHEN 1 THEN
                    '套餐商品'
                    WHEN 2 THEN
                    '普通商品'
                    WHEN 3 THEN
                    '称重商品'
                    WHEN 4 THEN
                    '普通商品'
                    WHEN 5 THEN
                    '宴会套餐'
                    ELSE''
                END itemType,
                '点菜' "operation",
                SUM ( oi.current_count ) orderNumber,
                oi.unit unit,
                oi.price + COALESCE ( pap.addPrice , 0)  price,
                round( COALESCE ( SUM ( ia.attrPrice * oi.current_count ), 0 ), 2 ) +
                    round( COALESCE ( SUM ( pa.attrPrice * oi.current_count ), 0 ), 2 ) attrPrice,
                o.order_no orderNo,
                o.gmt_create createTime,
                o.checkout_time checkoutTime
            FROM
                "hst_trade_${query.enterpriseGuid}_db".hst_order o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
                LEFT JOIN itemAttr ia ON ia.guid = oi.guid
                LEFT JOIN packageAttr pa ON pa.parent_item_guid = oi.guid
                LEFT JOIN packageAddPrice pap ON pap.parent_item_guid = oi.guid
            WHERE
                o.is_delete = 0
                AND oi.is_delete = 0
                AND o.checkout_time BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemType != null">
                    <choose>
                        <when test="query.itemType == 1">
                            AND oi.item_type = 1
                        </when>
                        <when test="query.itemType == 2">
                            AND oi.item_type IN (2 , 4)
                        </when>
                        <when test="query.itemType == 3">
                            AND oi.item_type = 3
                        </when>
                        <when test="query.itemType == 4">
                            AND oi.item_type = 5
                        </when>
                    </choose>
                </if>
                AND o."state" = 4
                AND oi.parent_item_guid = '0'
                AND o.recovery_type IN ( 1, 3 )
                AND oi.current_count > 0
            GROUP BY
                oi.order_guid,
                oi.sku_guid,
                oi.item_type,
                oi.sku_name,
                oi.item_name,
                oi.item_type_name,
                oi.unit,
                oi.price,
                pap.addPrice,
                o.order_no,
                o.gmt_create,
                o.checkout_time
        ),
        -- 赠菜商品
        freeItem AS (
            SELECT
                MAX( oi.gmt_create ) dataTime,
                CASE
                    WHEN oi.sku_name = ''
                    THEN oi.item_name ELSE CONCAT ( oi.item_name, '(', oi.sku_name, ')' )
                    END itemName,
                oi.item_type_name itemTypeName,
                CASE oi.item_type
                    WHEN 1 THEN
                    '套餐商品'
                    WHEN 2 THEN
                    '普通商品'
                    WHEN 3 THEN
                    '称重商品'
                    WHEN 4 THEN
                    '普通商品'
                    WHEN 5 THEN
                    '宴会套餐'
                    ELSE''
                END itemType,
                '赠菜' "operation",
                SUM ( oi.free_count ) orderNumber,
                oi.unit unit,
                oi.price + COALESCE ( pap.addPrice , 0)  price,
                round( COALESCE ( SUM ( ia.attrPrice * oi.free_count ), 0 ), 2 ) +
                    round( COALESCE ( SUM ( pa.attrPrice * oi.free_count ), 0 ), 2 ) attrPrice,
                o.order_no orderNo,
                o.gmt_create createTime,
                o.checkout_time checkoutTime
            FROM
                "hst_trade_${query.enterpriseGuid}_db".hst_order o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
                LEFT JOIN itemAttr ia ON ia.guid = oi.guid
                LEFT JOIN packageAttr pa ON pa.parent_item_guid = oi.guid
                LEFT JOIN packageAddPrice pap ON pap.parent_item_guid = oi.guid
            WHERE
                o.is_delete = 0
                AND oi.is_delete = 0
                AND o.checkout_time BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemType != null">
                    <choose>
                        <when test="query.itemType == 1">
                            AND oi.item_type = 1
                        </when>
                        <when test="query.itemType == 2">
                            AND oi.item_type IN (2 , 4)
                        </when>
                        <when test="query.itemType == 3">
                            AND oi.item_type = 3
                        </when>
                        <when test="query.itemType == 4">
                            AND oi.item_type = 5
                        </when>
                    </choose>
                </if>
                AND o."state" = 4
                AND oi.parent_item_guid = '0'
                AND o.recovery_type IN ( 1, 3 )
                AND oi.free_count > 0
            GROUP BY
                oi.order_guid,
                oi.sku_guid,
                oi.item_type,
                oi.sku_name,
                oi.item_name,
                oi.item_type_name,
                oi.unit,
                oi.price,
                pap.addPrice,
                o.order_no,
                o.gmt_create,
                o.checkout_time
        ),
        -- 退菜商品
        returnItem AS (
            SELECT
                MAX( oi.gmt_create ) dataTime,
                CASE
                    WHEN oi.sku_name = ''
                    THEN oi.item_name
                    ELSE CONCAT ( oi.item_name, '(', oi.sku_name, ')' )
                END itemName,
                oi.item_type_name itemTypeName,
                CASE oi.item_type
                    WHEN 1 THEN
                    '套餐商品'
                    WHEN 2 THEN
                    '普通商品'
                    WHEN 3 THEN
                    '称重商品'
                    WHEN 4 THEN
                    '普通商品'
                    WHEN 5 THEN
                    '宴会套餐'
                    ELSE''
                END itemType,
                '退菜' "operation",
                - SUM ( oi.return_count ) orderNumber,
                oi.unit unit,
                oi.price + COALESCE ( pap.addPrice , 0)  price,
                round( COALESCE ( SUM ( ia.attrPrice * oi.return_count ), 0 ), 2 ) +
                    round( COALESCE ( SUM ( pa.attrPrice * oi.return_count ), 0 ), 2 ) attrPrice,
                o.order_no orderNo,
                o.gmt_create createTime,
                o.checkout_time checkoutTime
            FROM
                "hst_trade_${query.enterpriseGuid}_db".hst_order o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
                LEFT JOIN itemAttr ia ON ia.guid = oi.guid
                LEFT JOIN packageAttr pa ON pa.parent_item_guid = oi.guid
                LEFT JOIN packageAddPrice pap ON pap.parent_item_guid = oi.guid
            WHERE
                o.is_delete = 0
                AND oi.is_delete = 0
                AND o.checkout_time BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
                AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
                <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.itemType != null">
                    <choose>
                        <when test="query.itemType == 1">
                            AND oi.item_type = 1
                        </when>
                        <when test="query.itemType == 2">
                            AND oi.item_type IN (2 , 4)
                        </when>
                        <when test="query.itemType == 3">
                            AND oi.item_type = 3
                        </when>
                        <when test="query.itemType == 4">
                            AND oi.item_type = 5
                        </when>
                    </choose>
                </if>
                AND o."state" = 4
                AND oi.parent_item_guid = '0'
                AND o.recovery_type IN ( 1, 3 )
                AND oi.return_count > 0
            GROUP BY
                oi.order_guid,
                oi.sku_guid,
                oi.item_type,
                oi.sku_name,
                oi.item_name,
                oi.item_type_name,
                oi.unit,
                oi.price,
                pap.addPrice,
                o.order_no,
                o.gmt_create,
                o.checkout_time

        UNION ALL
        -- 部分退款
        SELECT
            MAX( oi.gmt_create ) dataTime,
            CASE
                WHEN oi.sku_name = ''
                THEN oi.item_name
                ELSE CONCAT ( oi.item_name, '(', oi.sku_name, ')' )
            END itemName,
            oi.item_type_name itemTypeName,
            CASE oi.item_type
                WHEN 1 THEN
                '套餐商品'
                WHEN 2 THEN
                '普通商品'
                WHEN 3 THEN
                '称重商品'
                WHEN 4 THEN
                '普通商品'
                WHEN 5 THEN
                '宴会套餐'
                ELSE''
            END itemType,
            '退菜' "operation",
            - SUM ( oi.current_count ) orderNumber,
            oi.unit unit,
            oi.price - round( COALESCE ( SUM ( ia.attrPrice * oi.current_count ), 0 ), 2 )
                - round( COALESCE ( SUM ( pa.attrPrice * oi.current_count ), 0 ), 2 ) price,
            round( COALESCE ( SUM ( ia.attrPrice * oi.current_count ), 0 ), 2 )
                + round( COALESCE ( SUM ( pa.attrPrice * oi.current_count ), 0 ), 2 ) attrPrice,
            o.order_no orderNo,
            o.gmt_create createTime,
            o.checkout_time checkoutTime
        FROM
            "hst_trade_${query.enterpriseGuid}_db".hst_order o
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
            LEFT JOIN itemAttr ia ON ia.guid = oi.guid
            LEFT JOIN packageAttr pa ON pa.parent_item_guid = oi.guid
        WHERE
            o.is_delete = 0
             AND oi.is_delete = 0
            AND o.checkout_time BETWEEN CONCAT ( #{query.startTime}, ' 00:00:00' ) :: TIMESTAMP
            AND CONCAT ( #{query.endTime}, ' 23:59:59' ) :: TIMESTAMP
            <if test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                AND o.store_guid IN
                <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.itemType != null">
                <choose>
                    <when test="query.itemType == 1">
                        AND oi.item_type = 1
                    </when>
                    <when test="query.itemType == 2">
                        AND oi.item_type IN (2 , 4)
                    </when>
                    <when test="query.itemType == 3">
                        AND oi.item_type = 3
                    </when>
                    <when test="query.itemType == 4">
                        AND oi.item_type = 5
                    </when>
                </choose>
            </if>
            AND o."state" = 5
            AND o.recovery_id = '0'
            AND oi.parent_item_guid = '0'
        GROUP BY
            oi.order_guid,
            oi.sku_guid,
            oi.item_type,
            oi.sku_name,
            oi.item_name,
            oi.item_type_name,
            oi.unit,
            oi.price,
            o.order_no,
            o.gmt_create,
            o.checkout_time
        )
    </sql>
</mapper>
