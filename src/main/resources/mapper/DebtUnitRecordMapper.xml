<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.DebtUnitRecordMapper">

    <resultMap id="paymentResultMap" type="com.holderzone.saas.store.trade.entity.dto.DebtRepaymentFeeTotalDTO">
        <result column="paymentType" property="paymentType" jdbcType="INTEGER"/>
        <result column="repaymentFeeTotal" property="repaymentFeeTotal" jdbcType="DECIMAL" />
    </resultMap>

    <select id="pageDebtUnitRecord" resultType="com.holderzone.saas.store.trade.entity.domain.DebtUnitRecordDO"
            parameterType="com.holderzone.saas.store.trade.entity.query.DebtUnitRecordQuery">
        SELECT
            r.guid,
            r.gmt_create,
            r.gmt_modified,
            r.unit_guid,
            u.name as unit_name,
            u.contact_name as unit_contact_name,
            u.contact_tel as unit_contact_tel,
            r.unit_code,
            r.order_guid,
            r.create_staff_guid,
            r.create_staff_name,
            r.update_staff_guid,
            r.update_staff_name,
            r.store_guid,
            r.store_name,
            r.debt_invoice_code,
            r.debt_fee,
            r.payment_type,
            r.repayment_fee,
            r.repayment_status,
            r.remark,
            r.excess_flag,
            r.evidences,
            r.member_info_guid,
            r.member_consumption_guid,
            r.member_card_guid,
            r.member_phone,
            r.member_name,
            r.repayment_batch_number
        FROM
            hst_debt_unit_record r
        LEFT JOIN hst_debt_unit u on u.guid = r.unit_guid
        <where>
            r.is_delete = 0
            <if test="null!=debtUnitRecordQuery.unitGuid and debtUnitRecordQuery.unitGuid!=''">
                and r.unit_guid = #{debtUnitRecordQuery.unitGuid}
            </if>
            <if test="null!=debtUnitRecordQuery.repaymentStatus">
                and r.repayment_status = #{debtUnitRecordQuery.repaymentStatus}
            </if>
            ORDER BY
            r.gmt_modified , r.debt_invoice_code
        </where>
    </select>

    <select id="queryDebtUnitTotal" resultType="decimal">
        SELECT
            SUM( case when repayment_status = 0 then debt_fee
            when repayment_status = 1 and excess_flag = 1  then - debt_fee
            else 0 end )
        FROM
	        hst_debt_unit_record
        WHERE
	        (repayment_status = 0 or ( repayment_status = 1 and excess_flag = 1 ) )
	        AND is_delete = 0
        <if test="null != unitGuid">
            AND unit_guid = #{unitGuid}
        </if>
    </select>

    <select id="existsRecord" resultType="java.lang.Boolean">
        SELECT COUNT(1)
        FROM hst_debt_unit_record
        WHERE unit_guid = #{unitGuid}
          AND is_delete = 0
    </select>

    <select id="queryRepaymentFeeTotal" resultMap="paymentResultMap">
        SELECT
	        payment_type AS paymentType,SUM( repayment_fee )  AS repaymentFeeTotal ,COUNT(1) AS repaymentFeeCount
        FROM
	        hst_debt_unit_record
        WHERE
	        repayment_status = 1
	        AND is_delete = 0
        <if test="null != dto.createStaffGuid and dto.createStaffGuid!=''">
            AND create_staff_guid = #{dto.createStaffGuid}
        </if>
        <if test="null!=dto.startDateTime">
            AND gmt_modified &gt;= #{dto.startDateTime}
        </if>
        <if test="null!=dto.endDateTime">
            AND gmt_modified &lt;= #{dto.endDateTime}
        </if>
	    GROUP BY payment_type
    </select>


    <select id="getByOrderGuid" resultType="com.holderzone.saas.store.trade.entity.domain.DebtUnitRecordDO">
        SELECT
            r.guid,
            r.gmt_create,
            r.gmt_modified,
            r.unit_guid,
            u.name as unit_name,
            u.contact_name as unit_contact_name,
            u.contact_tel as unit_contact_tel,
            r.unit_code,
            r.order_guid,
            r.create_staff_guid,
            r.create_staff_name,
            r.store_guid,
            r.store_name,
            r.debt_invoice_code,
            r.debt_fee,
            r.payment_type,
            r.repayment_fee,
            r.repayment_status,
            r.member_info_guid,
            r.member_consumption_guid,
            r.member_card_guid,
            r.member_phone,
            r.member_name,
            r.remark,
            r.excess_flag,
            r.evidences
        FROM
            hst_debt_unit_record r
        LEFT JOIN hst_debt_unit u on u.guid = r.unit_guid
        where
            r.order_guid = #{orderGuid}
    </select>
</mapper>