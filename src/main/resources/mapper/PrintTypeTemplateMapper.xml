<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holder.saas.print.mapper.PrintTypeTemplateMapper">

    <select id="checkTemplateName" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            hsp_print_type_template
        WHERE
            is_delete = 0
            AND brand_guid = #{brandGuid}
            AND name = #{name}
            <if test="guid != null and guid != ''">
                AND guid != #{guid}
            </if>
    </select>

    <select id="queryPage" resultType="com.holder.saas.print.entity.domain.type.PrintTypeTemplateDO">
        SELECT
            *
        FROM
            `hsp_print_type_template`
        WHERE
            is_delete = 0
            AND brand_guid = #{query.data}
        ORDER BY
            gmt_create DESC
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>


    <select id="queryRepeatTemplateCount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            `hsp_print_type_template`
        <where>
            is_delete = 0
            <if test="brandGuid != null and brandGuid != ''">
                and brand_guid = #{brandGuid}
            </if>
            <if test="guid != null and guid != ''">
                and guid != #{guid}
            </if>
            <if test="invoiceTypeList != null and invoiceTypeList.size()>0">
                and
                <foreach collection="invoiceTypeList" item="invoiceType" open="(" separator="or" close=")">
                    concat(',',invoice_type,',') like concat('%,',#{invoiceType},',%')
                </foreach>
            </if>
        </where>
    </select>

</mapper>