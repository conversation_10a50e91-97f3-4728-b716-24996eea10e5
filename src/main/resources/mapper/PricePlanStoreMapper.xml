<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.PricePlanStoreMapper">

    <select id="getPlanStoreGuidList" resultType="java.lang.String">
        SELECT store_guid
        FROM hsi_price_plan_store
        WHERE plan_guid = #{planGuid}
        <if test="filterGuidList != null and filterGuidList.size()>0">
            and store_guid not in
            <foreach collection="filterGuidList" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
    </select>

    <select id="getPlanStoreGuidListByPlanGuidList" resultType="java.lang.String">
        SELECT DISTINCT store_guid
        FROM hsi_price_plan_store
        WHERE is_delete = 0
        AND plan_guid IN
        <foreach collection="planGuidList" item="planGuid" open="(" separator="," close=")">
            #{planGuid}
        </foreach>
    </select>

    <select id="getPlanStoreListByPlanGuidList" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanStoreInfoDTO">
        SELECT  store_guid as storeGuid,plan_guid as planGuid
        FROM hsi_price_plan_store
        WHERE is_delete = 0
        AND plan_guid IN
        <foreach collection="planGuidList" item="planGuid" open="(" separator="," close=")">
            #{planGuid}
        </foreach>
    </select>

    <delete id="deleteStores">
        DELETE
        FROM hsi_price_plan_store
        WHERE plan_guid = #{planGuid}
    </delete>

    <select id="findPlanStoreGuid" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanStoreInfoDTO">
        SELECT
        ps.store_guid AS storeGuid,
        ps.brand_guid AS brandGuid,
        ps.plan_guid AS planGuid,
        pp.push_type AS pushType,
        pp.push_date AS pushDate,
        pp.sell_time_type AS sellTimeType,
        pp.start_time AS startTime,
        pp.end_time AS endTime
        FROM
        hsi_price_plan_store ps
        LEFT JOIN hsi_price_plan pp ON ps.plan_guid = pp.guid
        <where>
            ps.store_guid in
            <foreach collection="reqDTO.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
            <if test="reqDTO.sellTimeType != null">
                AND pp.sell_time_type = #{reqDTO.sellTimeType}
            </if>
            and pp.is_delete = 0
            AND pp.status IN ( 1, 4 )
            <if test="reqDTO.brandGuid != null and reqDTO.brandGuid != ''">
                AND ps.brand_guid = #{reqDTO.brandGuid}
            </if>
        </where>
    </select>

    <select id="findPlanNowStoreGuidList" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanNowDTO">
        SELECT DISTINCT
        ps.store_guid AS storeGuid,
        ps.brand_guid AS brandGuid,
        ps.plan_guid AS planGuid,
        pp.push_type AS pushType,
        pp.push_date AS pushDate,
        pp.sell_time_type AS sellTimeType,
        pp.start_time AS startTime,
        pp.end_time AS endTime
        FROM
        hsi_price_plan_store ps
        LEFT JOIN hsi_price_plan pp ON ps.plan_guid = pp.guid
        <where>
            ps.is_delete=0
            and pp.is_delete=0
            and pp.status=1
            and pp.effective_time &lt; #{nowDateTime}
            and ps.store_guid in
            <foreach collection="storeGuidList" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
            AND pp.parent_guid IS NULL
        </where>
    </select>

    <select id="findPlanNowStoreGuid" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanNowDTO">
        SELECT DISTINCT
        ps.store_guid AS storeGuid,
        ps.brand_guid AS brandGuid,
        ps.plan_guid AS planGuid,
        pp.push_type AS pushType,
        pp.push_date AS pushDate,
        pp.sell_time_type AS sellTimeType,
        pp.start_time AS startTime,
        pp.end_time AS endTime
        FROM
        hsi_price_plan_store ps
        LEFT JOIN hsi_price_plan pp ON ps.plan_guid = pp.guid
        <where>
            ps.is_delete=0
            and pp.is_delete=0
            and pp.status=1
            and pp.effective_time &lt; #{nowDateTime}
            and ps.store_guid=#{storeGuid}
        </where>
    </select>

    <select id="findPlanMemberStoreGuid" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanNowDTO">
        SELECT DISTINCT
        ps.store_guid AS storeGuid,
        ps.brand_guid AS brandGuid,
        ps.plan_guid AS planGuid,
        pp.push_type AS pushType,
        pp.push_date AS pushDate,
        pp.sell_time_type AS sellTimeType,
        pp.start_time AS startTime,
        pp.end_time AS endTime
        FROM
        hsi_price_plan_store ps
        LEFT JOIN hsi_price_plan pp ON ps.plan_guid = pp.guid
        <where>
            ps.is_delete=0
            and pp.is_delete=0
            and pp.status in(1,4)
            and ps.store_guid=#{storeGuid}
        </where>
    </select>

    <select id="findPlanByStoreGuid" resultType="com.holderzone.saas.store.dto.item.resp.PricePlanNowDTO">
        SELECT
        ps.store_guid AS storeGuid,
        ps.brand_guid AS brandGuid,
        ps.plan_guid AS planGuid,
        pp.push_type AS pushType,
        pp.push_date AS pushDate,
        pp.sell_time_type AS sellTimeType,
        pp.start_time AS startTime,
        pp.end_time AS endTime
        FROM
        hsi_price_plan_store ps
        LEFT JOIN hsi_price_plan pp ON ps.plan_guid = pp.guid
        <where>
            ps.is_delete=0
            and pp.is_delete=0
            and pp.status not in (2, 3)
            and ps.store_guid=#{storeGuid}
        </where>
    </select>
</mapper>