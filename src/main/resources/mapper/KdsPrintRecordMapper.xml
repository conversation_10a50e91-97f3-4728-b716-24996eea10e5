<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.kds.mapper.KdsPrintRecordMapper">

    <resultMap id="ExtensionResultMap" type="com.holderzone.saas.store.kds.entity.read.KdsPrintRecordReadDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="record_uid" jdbcType="VARCHAR" property="recordUid"/>
        <result column="invoice_type" jdbcType="CHAR" property="invoiceType"/>
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="print_status" jdbcType="CHAR" property="printStatus"/>
        <result column="print_status_msg" jdbcType="VARCHAR" property="printStatusMsg"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="print_content" jdbcType="LONGVARCHAR" property="printContent"/>
        <association property="kdsPrinterDO" javaType="com.holderzone.saas.store.kds.entity.domain.KdsPrinterDO">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="printer_name" jdbcType="VARCHAR" property="printerName"/>
            <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
            <result column="printer_port" jdbcType="CHAR" property="printerPort"/>
            <result column="page_size" jdbcType="CHAR" property="pageSize"/>
        </association>
    </resultMap>

    <resultMap id="PrintTaskResultMap" type="com.holderzone.saas.store.kds.entity.read.KdsPrintRecordReadDO">
        <result column="guid" jdbcType="CHAR" property="guid"/>
        <result column="invoice_type" jdbcType="CHAR" property="invoiceType"/>
        <result column="print_content" jdbcType="LONGVARCHAR" property="printContent"/>
        <association property="kdsPrinterDO" javaType="com.holderzone.saas.store.kds.entity.domain.KdsPrinterDO">
            <result column="printer_ip" jdbcType="VARCHAR" property="printerIp"/>
            <result column="printer_port" jdbcType="CHAR" property="printerPort"/>
            <result column="page_size" jdbcType="CHAR" property="pageSize"/>
        </association>
    </resultMap>

    <resultMap id="RecordResultMap" type="com.holderzone.saas.store.kds.entity.read.KdsPrintRecordReadDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="record_uid" jdbcType="VARCHAR" property="recordUid"/>
        <result column="invoice_type" jdbcType="CHAR" property="invoiceType"/>
        <result column="printer_guid" jdbcType="VARCHAR" property="printerGuid"/>
        <result column="print_status" jdbcType="CHAR" property="printStatus"/>
        <result column="print_status_msg" jdbcType="VARCHAR" property="printStatusMsg"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="print_content" jdbcType="LONGVARCHAR" property="printContent"/>
    </resultMap>

    <select id="queryByRecordGuid" parameterType="com.holderzone.saas.store.kds.entity.query.PrintRecordQuery"
            resultMap="PrintTaskResultMap">
        select
        r.guid, r.invoice_type, r.print_content,
        p.page_size, p.printer_ip, p.printer_port
        from hsk_print_record r
        left join hsk_store_printer p on p.guid = r.printer_guid
        where r.guid=#{recordGuid} and r.is_deleted = 0
    </select>

    <select id="queryInRecordGuid" parameterType="com.holderzone.saas.store.kds.entity.query.PrintRecordQuery"
            resultMap="PrintTaskResultMap">
        <foreach collection="arrayOfRecordGuid" item="recordGuid" index="index" separator="union all">
            select
            r.guid, r.invoice_type, r.print_content,
            p.page_size, p.printer_ip, p.printer_port
            from hsk_print_record r
            left join hsk_store_printer p on p.guid = r.printer_guid
            where r.guid=#{recordGuid} and r.is_deleted = 0
        </foreach>
    </select>

    <!--<select id="countByDeviceAndStatus" parameterType="com.holder.saas.print.entity.domain.PrintRecordDO"-->
            <!--resultType="java.lang.Long">-->
        <!--select count(*)-->
        <!--from hsp_print_record-->
        <!--where device_id=#{deviceId} and print_status=#{printStatus} and is_deleted = 0-->
    <!--</select>-->

    <!--<select id="listByDeviceAndStatus" parameterType="com.holder.saas.print.entity.domain.PrintRecordDO"-->
            <!--resultMap="ExtensionResultMap">-->
        <!--select-->
        <!--r.*,-->
        <!--p.business_type, p.printer_type, p.printer_name, p.printer_ip-->
        <!--from hsp_print_record r-->
        <!--inner join hsp_printer p on p.printer_guid=r.printer_guid-->
        <!--where r.is_deleted = 0 and r.print_status=#{printStatus}-->
        <!--<if test="deviceId != null and deviceId != ''">-->
            <!--and r.device_id=#{deviceId}-->
        <!--</if>-->
        <!--order by gmt_create desc, invoice_type desc-->
    <!--</select>-->

</mapper>
