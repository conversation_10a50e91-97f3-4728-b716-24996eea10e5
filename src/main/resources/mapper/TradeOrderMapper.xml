<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.TradeOrderMapper">

    <select id="queryItemTypes" resultType="com.holderzone.saas.store.dto.report.resp.OrderItemTypeRespDTO">
        SELECT
            d.item_type,
            CASE WHEN d.item_type=1 THEN '套餐'
            WHEN d.item_type=2 THEN '规格'
            WHEN d.item_type=3 THEN '称重'
            WHEN d.item_type=4 THEN '单品'
            WHEN d.item_type=5 THEN '团餐' ELSE '其他' END item_name
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" a ON d.order_guid = a.guid and d.is_delete = '0'
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = a.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        <include refid="querySQL" />
        GROUP BY d.item_type
    </select>


    <select id="queryItemCategories" resultType="java.lang.String">
        SELECT
            DISTINCT case when htp.name is null then ht.name else htp.name end
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" a ON d.order_guid = a.guid and d.is_delete = '0'
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = a.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_item" hi ON hi.guid = d.item_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" ht ON hi.type_guid = ht.guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" htp ON ht.parent_guid = htp.guid
        <include refid="querySQL" />
    </select>

    <select id="itemTypeSaleCount" resultType="com.holderzone.saas.store.dto.journaling.resp.SaleCountRespDTO">
        SELECT
            max(ht.name) typeName,
            SUM(d.current_count) saleNumber,
            SUM(d.current_count*d.price) salePrice
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" a ON d.order_guid = a.guid
            JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_item" hi ON hi.guid = d.item_guid
            JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" ht ON hi.type_guid = ht.guid

        WHERE
            a.checkout_time BETWEEN #{businessStartDateTime}  AND
             #{businessEndDateTime}
            AND a.state = 4
            AND a.is_delete = 0
            and a.recovery_type in (1,3)
            and a.store_guid in
            <foreach collection="req.storeGuids" item="storeGuid" open="(" separator=","
                     close=")">
                #{storeGuid}
            </foreach>
        GROUP BY ht.guid
        ORDER BY saleNumber DESC
        LIMIT 5
    </select>

    <select id="queryOrderCount" resultType="java.lang.Long">
        SELECT
            COUNT (DISTINCT o.orderGuid )
        FROM
        (
            (
            SELECT
                o.guid||'' AS orderGuid
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
            WHERE
                o."state" = 4
                AND o.is_delete = 0
                AND o.recovery_type IN ( 1, 3 )
                AND o.business_day >= #{query.startDate}
                <![CDATA[ AND o.business_day <= #{query.endDate} ]]>
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
            )

            UNION ALL

            (
            SELECT
                DISTINCT
                ord.order_guid AS orderGuid
            FROM
                "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_item ti
                JOIN "hst_takeaway_${query.enterpriseGuid}_db".hst_takeout_order ord ON ti.order_guid = ord.order_guid
            WHERE
                1=1
                AND ord.business_day >= #{query.startDate}
                <![CDATA[ AND ord.business_day <= #{query.endDate} ]]>
                AND ti.business_day >= #{query.startDate}
                <![CDATA[ AND ti.business_day <= #{query.endDate} ]]>
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    AND ti.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                AND ord.order_status != '-1'
                AND ord.refund_status != '2'
                AND ti.erp_item_sku_guid IS NOT NULL
            )
        ) as o
    </select>


    <sql id="querySQL">
        <where>
            d.is_delete=0
            <if test="query.startTime != null">
                and a.business_day >= #{query.startTime}
                <if test="query.enterpriseGuid == '887f2181-eb06-4d77-b914-7c37c884952c'">
                    and d.business_day >= #{query.startTime}
                </if>
            </if>
            <if test="query.endTime != null">
                <![CDATA[ and a.business_day <= #{query.endTime} ]]>
                <if test="query.enterpriseGuid == '887f2181-eb06-4d77-b914-7c37c884952c'">
                    <![CDATA[ and d.business_day <= #{query.endTime} ]]>
                </if>
            </if>
            <if test="query.brandGuid != null and query.brandGuid != ''">
                and ro.brand_guid = #{query.brandGuid}
            </if>
            <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                and a.store_guid IN
                <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
        </where>
    </sql>


    <select id="querySaleDetail" resultType="com.holderzone.saas.store.dto.report.openapi.SaleDetailRespDTO">
        SELECT
            o.guid as "order_guid",
            o.order_no,
            ro.order_no as "original_order_no",
            o.store_guid,
            o.store_name,
            o.checkout_staff_name,
            o.checkout_time,
            case when o.state = 4 then (o.actually_pay_fee + COALESCE(oe.total_coupon_buy_price, 0)) * 100
                    else (o.actually_pay_fee + COALESCE(oe.total_coupon_buy_price, 0)) * -100 end as "actually_pay_fee",
            case when o.state = 4 then o.order_fee * 100 else o.order_fee * -100 end as "order_fee",
            case when o.state = 4 then (o.order_fee - (o.actually_pay_fee + COALESCE(oe.total_coupon_buy_price, 0))) * 100
                    else (o.order_fee - (o.actually_pay_fee + COALESCE(oe.total_coupon_buy_price, 0))) * -100 end as "discount_fee",
            o.state,
            case when o.state = 4 then 0 else 1 end as "receipt_type",
            o.gmt_create,
            o.gmt_modified,
            o.remark,
            o.device_type,
            o.member_name,
            o.member_phone
        FROM
            "hst_trade_${query.enterpriseGuid}_db".hst_order o
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db".hst_order ro on ro.guid = o.original_order_guid::bigint and ro.is_delete = '0'
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db".hst_order_extends oe on oe.guid = o.guid and ro.is_delete = '0'
        <where>
            o.is_delete = '0' and (o.state = 4 or ( o.state = 5 and o.recovery_type = 4 )) and o.main_order_guid = '0'
            <if test="query.updateTime != null">
                and o.gmt_modified >= #{query.updateTime}
            </if>
            <if test="query.gmtCreate != null">
                and o.gmt_create >= #{query.gmtCreate}
            </if>
            <if test="query.cursor != null">
                and o.guid <![CDATA[ < ]]> #{query.cursor}
            </if>
            <if test="query.storeGuid != null and query.storeGuid != ''">
                and o.store_guid = #{query.storeGuid}
            </if>
        </where>
        order by o.gmt_create desc, o.guid desc
        <if test="query.limit != null">
            limit ${query.limit}
        </if>
    </select>


    <select id="queryDineInOrderCount" resultType="java.lang.Long">
        SELECT
            COUNT (DISTINCT o.orderGuid )
        FROM
        (
            SELECT
                o.guid||'' AS orderGuid
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
            WHERE
                o."state" = 4
                AND o.is_delete = 0
                AND o.recovery_type IN ( 1, 3 )
                AND o.business_day >= #{query.startDate}
                <![CDATA[ AND o.business_day <= #{query.endDate} ]]>
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
        ) as o
    </select>
    
    
    <select id="queryGroupByStoreDineInOrderCount"
            resultType="com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO">
        SELECT
            o.store_guid,
            COUNT (DISTINCT o.orderGuid ) as "orderCount"
        FROM
        (
            SELECT
                o.store_guid,
                o.guid||'' AS orderGuid
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi ON oi.order_guid = o.guid
            WHERE
                o."state" = 4
                AND o.is_delete = 0
                AND o.recovery_type IN ( 1, 3 )
                AND o.business_day >= #{query.startDate}
                <![CDATA[ AND o.business_day <= #{query.endDate} ]]>
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    AND o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
        ) as o
        group by o.store_guid
    </select>

</mapper>
