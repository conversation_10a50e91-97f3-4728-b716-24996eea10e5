<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.HandoverPayDetailMapper">

    <insert id="batchInsert" useGeneratedKeys="true"  parameterType="java.util.List">
        INSERT INTO hsb_handover_pay_detail (
        `guid`,
        `handover_record_guid`,
        `terminal_id`,
        `pay_type`,
        `pay_type_name`,
        `pay_money`,
        `pay_belong_type`)
        VALUES
        <foreach collection="list" item="item" index="index" open="" separator="," close="">
            (#{item.guid}, #{item.handoverRecordGuid}, #{item.terminalId}, #{item.payType}, #{item.payTypeName}, #{item.payMoney}, #{item.payBelongType})
        </foreach>
    </insert>

    <select id="selectPayDetailById" parameterType="java.lang.String"
            resultType="com.holderzone.saas.store.business.entity.domain.HandoverPayDetailDO">
        SELECT
          `handover_record_guid` AS handoverRecordGuid,
          `guid`,
          `terminal_id` AS terminalId,
          `pay_type` AS payType,
          `pay_type_name` AS payTypeName,
          `pay_money` AS payMoney,
          `gmt_create` AS gmtCreate,
          `gmt_modified` AS gmtModified,
          `pay_belong_type` AS payBelongType
        FROM hsb_handover_pay_detail
        WHERE `handover_record_guid` = #{handoverRecordGuid}
    </select>

    <select id="countByTypeAndId" resultType="int">
        select count(*) from hsb_handover_pay_detail
        WHERE `handover_record_guid` = #{handoverRecordGuid}
        and pay_belong_type = #{belongType}
    </select>
</mapper>