<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.weixin.mapper.WxStoreMerchantOrderMapper">

    
    <select id="selectByOrderRecordGuid" resultType="com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO">
        select mo.gmt_modified,mo.gmt_create,mo.guid ,mo.order_state
        ,ur.nick_name,ur.open_id,ur.head_img_url,ur.is_login,mo.dining_table_guid
        ,remark,actual_guests_no
        from hsw_weixin_merchant_order mo
        <if test="tableGuids!=null">
            LEFT JOIN `hsw_weixin_order_record` o ON o.guid = mo.`order_record_guid`
        </if>
        left join hsw_weixin_user_record ur on ur.open_id = mo.open_id   where
        <include refid="SELECT_CONDITION"/>
    </select>
    <select id="countOrder" resultType="int">
        select count(1) from hsw_weixin_merchant_order mo
        <if test="tableGuids!=null">
            LEFT JOIN `hsw_weixin_order_record` o ON o.guid = mo.`order_record_guid`
        </if>
        where <include refid="SELECT_CONDITION"/>
            and mo.order_state = 0
    </select>
    <sql id="SELECT_CONDITION">
        <choose>
            <when test="tableGuids!=null">
                mo.dining_table_guid in
                <foreach collection="tableGuids" item="tableGuid" open="(" close=")" separator=",">
                    #{tableGuid}
                </foreach>
                and o.table_guid in
                <foreach collection="tableGuids" item="tableGuid" open="(" close=")" separator=",">
                    #{tableGuid}
                </foreach>
                and o.order_state in (0,1)
                and o.order_mode = 0
            </when>
            <otherwise>
                mo.order_record_guid = #{orderRecordGuid}
            </otherwise>
        </choose>
    </sql>
    <select id="queryWeChatOrderList" resultType="com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO">
        select *
        FROM hsw_weixin_merchant_order
        <where>
            is_del = 0
            <if test="storeGuid!=null">
              and  store_guid = #{storeGuid}
            </if>
            and trade_mode = 0
            <choose>
                <when test="req.orderState!=null and req.orderState!='' or req.orderState == 0">
                    and order_state = #{req.orderState}
                </when>
                <otherwise>
                    and order_state in (0,1,2)
                </otherwise>
            </choose>
            <if test="req.tableCode!=null and req.tableCode!=''">
                <choose>
                    <when test="req.tableCode.matches('^\\d+$')">
                        AND (
                            phone LIKE CONCAT(#{req.tableCode},'%')
                            OR table_code LIKE CONCAT(#{req.tableCode},'%')
                        )
                    </when>
                    <otherwise>
                        AND (
                            table_code LIKE CONCAT(#{req.tableCode},'%')
                            OR phone LIKE CONCAT(#{req.tableCode},'%')
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="req.guid!=null and req.guid!=''">
                and guid = #{req.guid}
            </if>
            <if test="req.startTime!=null">
                and gmt_create &gt;= #{req.startTime}
            </if>
            <if test="req.endTime!=null">
                and gmt_create &lt;= #{req.endTime}
            </if>
            <if test="gmtCreate != null and req.lastGuid != null">
                and gmt_create &lt; #{gmtCreate}
                and guid != #{req.lastGuid}
            </if>
        </where>
        ORDER BY gmt_modified DESC
        LIMIT #{req.count}
    </select>
    
    <!-- 性能优化版本：分别查询桌台编码和手机号，使用UNION ALL -->
    <select id="queryWeChatOrderListOptimized" resultType="com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO">
        <if test="req.tableCode!=null and req.tableCode!=''">
            <!-- 查询桌台编码匹配的记录 -->
            (
                select *
                FROM hsw_weixin_merchant_order
                <where>
                    is_del = 0
                    <if test="storeGuid!=null">
                      and store_guid = #{storeGuid}
                    </if>
                    and trade_mode = 0
                    <choose>
                        <when test="req.orderState!=null and req.orderState!='' or req.orderState == 0">
                            and order_state = #{req.orderState}
                        </when>
                        <otherwise>
                            and order_state in (0,1,2)
                        </otherwise>
                    </choose>
                    and table_code LIKE CONCAT(#{req.tableCode},'%')
                    <if test="req.guid!=null and req.guid!=''">
                        and guid = #{req.guid}
                    </if>
                    <if test="req.startTime!=null">
                        and gmt_create &gt;= #{req.startTime}
                    </if>
                    <if test="req.endTime!=null">
                        and gmt_create &lt;= #{req.endTime}
                    </if>
                    <if test="gmtCreate != null and req.lastGuid != null">
                        and gmt_create &lt; #{gmtCreate}
                        and guid != #{req.lastGuid}
                    </if>
                </where>
            )
            UNION ALL
            <!-- 查询手机号匹配的记录 -->
            (
                select *
                FROM hsw_weixin_merchant_order
                <where>
                    is_del = 0
                    <if test="storeGuid!=null">
                      and store_guid = #{storeGuid}
                    </if>
                    and trade_mode = 0
                    <choose>
                        <when test="req.orderState!=null and req.orderState!='' or req.orderState == 0">
                            and order_state = #{req.orderState}
                        </when>
                        <otherwise>
                            and order_state in (0,1,2)
                        </otherwise>
                    </choose>
                    and phone LIKE CONCAT(#{req.tableCode},'%')
                    and table_code NOT LIKE CONCAT(#{req.tableCode},'%') <!-- 避免重复记录 -->
                    <if test="req.guid!=null and req.guid!=''">
                        and guid = #{req.guid}
                    </if>
                    <if test="req.startTime!=null">
                        and gmt_create &gt;= #{req.startTime}
                    </if>
                    <if test="req.endTime!=null">
                        and gmt_create &lt;= #{req.endTime}
                    </if>
                    <if test="gmtCreate != null and req.lastGuid != null">
                        and gmt_create &lt; #{gmtCreate}
                        and guid != #{req.lastGuid}
                    </if>
                </where>
            )
            ORDER BY gmt_modified DESC
            LIMIT #{req.count}
        </if>
        <if test="req.tableCode==null or req.tableCode==''">
            <!-- 没有搜索条件时的普通查询 -->
            select *
            FROM hsw_weixin_merchant_order
            <where>
                is_del = 0
                <if test="storeGuid!=null">
                  and store_guid = #{storeGuid}
                </if>
                and trade_mode = 0
                <choose>
                    <when test="req.orderState!=null and req.orderState!='' or req.orderState == 0">
                        and order_state = #{req.orderState}
                    </when>
                    <otherwise>
                        and order_state in (0,1,2)
                    </otherwise>
                </choose>
                <if test="req.guid!=null and req.guid!=''">
                    and guid = #{req.guid}
                </if>
                <if test="req.startTime!=null">
                    and gmt_create &gt;= #{req.startTime}
                </if>
                <if test="req.endTime!=null">
                    and gmt_create &lt;= #{req.endTime}
                </if>
                <if test="gmtCreate != null and req.lastGuid != null">
                    and gmt_create &lt; #{gmtCreate}
                    and guid != #{req.lastGuid}
                </if>
            </where>
            ORDER BY gmt_modified DESC
            LIMIT #{req.count}
        </if>
    </select>
</mapper>