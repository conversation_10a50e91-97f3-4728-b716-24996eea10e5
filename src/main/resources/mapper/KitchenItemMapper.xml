<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.kds.mapper.KitchenItemMapper">

    <resultMap id="PointItemMap" type="com.holderzone.saas.store.kds.entity.read.PointItemReadDO">
        <id column="point_id" jdbcType="BIGINT" property="id"/>
        <result column="point_guid" jdbcType="VARCHAR" property="guid"/>
        <result column="point_store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="point_device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="point_name" jdbcType="VARCHAR" property="pointName"/>
        <result column="point_gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="point_gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <collection property="items" ofType="com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO"
                    resultMap="KitchenItemMap"/>
    </resultMap>

    <resultMap id="GroupBindItemMap" type="com.holderzone.saas.store.kds.entity.read.GroupBindItemReadDO">
        <id column="group_id" jdbcType="BIGINT" property="id"/>
        <result column="group_guid" jdbcType="VARCHAR" property="guid"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="group_gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="group_gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <collection property="items" ofType="com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO"
                    resultMap="KitchenItemMap"/>
    </resultMap>

    <resultMap id="KitchenItemMap" type="com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO">
        <id column="item_id" jdbcType="BIGINT" property="id"/>
        <result column="item_guid" jdbcType="VARCHAR" property="guid"/>
        <result column="item_store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="item_point_guid" jdbcType="VARCHAR" property="pointGuid"/>
        <result column="item_prd_device_id" jdbcType="VARCHAR" property="prdDeviceId"/>
        <result column="item_dst_device_id" jdbcType="VARCHAR" property="dstDeviceId"/>
        <result column="item_display_type" jdbcType="TINYINT" property="displayType"/>
        <result column="item_order_guid" jdbcType="VARCHAR" property="orderGuid"/>
        <result column="item_order_desc" jdbcType="VARCHAR" property="orderDesc"/>
        <result column="item_order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="item_order_serial_no" jdbcType="VARCHAR" property="orderSerialNo"/>
        <result column="item_order_remark" jdbcType="VARCHAR" property="orderRemark"/>
        <result column="item_area_guid" jdbcType="VARCHAR" property="areaGuid"/>
        <result column="item_table_guid" jdbcType="VARCHAR" property="tableGuid"/>
        <result column="item_order_item_guid" jdbcType="VARCHAR" property="orderItemGuid"/>
        <result column="item_item_guid" jdbcType="VARCHAR" property="itemGuid"/>
        <result column="item_item_name" jdbcType="VARCHAR" property="itemName"/>
        <result column="item_sku_guid" jdbcType="VARCHAR" property="skuGuid"/>
        <result column="item_sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="item_sku_code" jdbcType="VARCHAR" property="skuCode"/>
        <result column="item_sku_unit" jdbcType="VARCHAR" property="skuUnit"/>
        <result column="item_is_weight" jdbcType="TINYINT" property="isWeight"/>
        <result column="item_current_count" jdbcType="DECIMAL" property="currentCount"/>
        <result column="item_return_count" jdbcType="DECIMAL" property="returnCount"/>
        <result column="item_item_attr_md5" jdbcType="VARCHAR" property="itemAttrMd5"/>
        <result column="item_item_remark" jdbcType="VARCHAR" property="itemRemark"/>
        <result column="item_timeout" jdbcType="INTEGER" property="timeout"/>
        <result column="item_urged_times" jdbcType="INTEGER" property="urgedTimes"/>
        <result column="item_item_state" jdbcType="TINYINT" property="itemState"/>
        <result column="item_kitchen_state" jdbcType="TINYINT" property="kitchenState"/>
        <result column="item_is_print_automatic" jdbcType="TINYINT" property="isPrintAutomatic"/>
        <result column="item_prepare_time" jdbcType="TIMESTAMP" property="prepareTime"/>
        <result column="item_call_up_time" jdbcType="TIMESTAMP" property="callUpTime"/>
        <result column="item_hang_up_time" jdbcType="TIMESTAMP" property="hangUpTime"/>
        <result column="item_urged_time" jdbcType="TIMESTAMP" property="urgedTime"/>
        <result column="item_cook_staff_guid" jdbcType="VARCHAR" property="cookStaffGuid"/>
        <result column="item_cook_staff_name" jdbcType="VARCHAR" property="cookStaffName"/>
        <result column="item_cook_time" jdbcType="TIMESTAMP" property="cookTime"/>
        <result column="item_complete_staff_guid" jdbcType="VARCHAR" property="completeStaffGuid"/>
        <result column="item_complete_staff_name" jdbcType="VARCHAR" property="completeStaffName"/>
        <result column="item_complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="item_distribute_staff_guid" jdbcType="VARCHAR" property="distributeStaffGuid"/>
        <result column="item_distribute_staff_name" jdbcType="VARCHAR" property="distributeStaffName"/>
        <result column="item_distribute_time" jdbcType="TIMESTAMP" property="distributeTime"/>
        <result column="item_cancel_dst_staff_guid" jdbcType="VARCHAR" property="cancelDstStaffGuid"/>
        <result column="item_cancel_dst_staff_name" jdbcType="VARCHAR" property="cancelDstStaffName"/>
        <result column="item_cancel_dst_time" jdbcType="TIMESTAMP" property="cancelDstTime"/>
        <result column="item_gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="item_gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="delay_time_minutes" jdbcType="INTEGER" property="delayTimeMinutes"/>
        <result column="display_time" jdbcType="TIMESTAMP" property="displayTime"/>
        <result column="batch" jdbcType="INTEGER" property="batch"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="display_rule_type" jdbcType="INTEGER" property="displayRuleType"/>
        <result column="original_item_sku_name" jdbcType="VARCHAR" property="originalItemSkuName"/>
        <result column="item_add_item_batch" jdbcType="INTEGER" property="addItemBatch"/>
        <result column="item_first_add_item_time" jdbcType="TIMESTAMP" property="firstAddItemTime"/>
        <result column="item_add_item_time" jdbcType="TIMESTAMP" property="addItemTime"/>

        <association property="itemConfig" resultMap="ItemConfigMap"/>
        <collection property="attrs" ofType="com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO"
                    resultMap="ItemAttrMap"/>
    </resultMap>

    <resultMap id="DstItemMap" type="com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO">
        <result column="item_guid" jdbcType="VARCHAR" property="guid"/>
        <result column="item_display_type" jdbcType="TINYINT" property="displayType"/>
        <result column="item_order_guid" jdbcType="VARCHAR" property="orderGuid"/>
        <result column="item_order_desc" jdbcType="VARCHAR" property="orderDesc"/>
        <result column="item_order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="item_order_serial_no" jdbcType="VARCHAR" property="orderSerialNo"/>
        <result column="item_order_remark" jdbcType="VARCHAR" property="orderRemark"/>
        <result column="item_area_guid" jdbcType="VARCHAR" property="areaGuid"/>
        <result column="item_order_item_guid" jdbcType="VARCHAR" property="orderItemGuid"/>
        <result column="item_item_guid" jdbcType="VARCHAR" property="itemGuid"/>
        <result column="item_item_name" jdbcType="VARCHAR" property="itemName"/>
        <result column="item_sku_guid" jdbcType="VARCHAR" property="skuGuid"/>
        <result column="item_sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="item_sku_unit" jdbcType="VARCHAR" property="skuUnit"/>
        <result column="item_is_weight" jdbcType="TINYINT" property="isWeight"/>
        <result column="item_current_count" jdbcType="DECIMAL" property="currentCount"/>
        <result column="item_item_attr_md5" jdbcType="VARCHAR" property="itemAttrMd5"/>
        <result column="item_item_remark" jdbcType="VARCHAR" property="itemRemark"/>
        <result column="item_timeout" jdbcType="INTEGER" property="timeout"/>
        <result column="item_item_state" jdbcType="TINYINT" property="itemState"/>
        <result column="item_kitchen_state" jdbcType="TINYINT" property="kitchenState"/>
        <result column="item_prepare_time" jdbcType="TIMESTAMP" property="prepareTime"/>
        <result column="item_hang_up_time" jdbcType="TIMESTAMP" property="hangUpTime"/>
        <result column="item_urged_time" jdbcType="TIMESTAMP" property="urgedTime"/>
        <result column="item_call_up_time" jdbcType="TIMESTAMP" property="callUpTime"/>
        <result column="batch" jdbcType="INTEGER" property="batch"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="display_rule_type" jdbcType="INTEGER" property="displayRuleType"/>
        <result column="original_item_sku_name" jdbcType="VARCHAR" property="originalItemSkuName"/>
        <result column="item_add_item_batch" jdbcType="INTEGER" property="addItemBatch"/>
        <result column="item_first_add_item_time" jdbcType="TIMESTAMP" property="firstAddItemTime"/>
        <result column="item_add_item_time" jdbcType="TIMESTAMP" property="addItemTime"/>

        <association property="itemConfig" resultMap="DstItemConfigMap"/>
        <collection property="attrs" ofType="com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO"
                    resultMap="DstItemAttrMap"/>
    </resultMap>

    <resultMap id="DstItemConfigMap" type="com.holderzone.saas.store.kds.entity.domain.ItemConfigDO">
        <result column="config_timeout" jdbcType="INTEGER" property="timeout"/>
        <result column="config_display_type" jdbcType="INTEGER" property="displayType"/>
    </resultMap>

    <resultMap id="DstItemAttrMap" type="com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO">
        <result column="attr_group_guid" jdbcType="VARCHAR" property="groupGuid"/>
        <result column="attr_group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="attr_attr_guid" jdbcType="VARCHAR" property="attrGuid"/>
        <result column="attr_attr_name" jdbcType="VARCHAR" property="attrName"/>
        <result column="attr_attr_number" jdbcType="INTEGER" property="attrNumber"/>
    </resultMap>

    <resultMap id="KitchenItemMap2" type="com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO">
        <id column="item_id" jdbcType="BIGINT" property="id"/>
        <result column="item_guid" jdbcType="VARCHAR" property="guid"/>
        <result column="item_store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="item_point_guid" jdbcType="VARCHAR" property="pointGuid"/>
        <result column="item_prd_device_id" jdbcType="VARCHAR" property="prdDeviceId"/>
        <result column="item_dst_device_id" jdbcType="VARCHAR" property="dstDeviceId"/>
        <result column="item_display_type" jdbcType="TINYINT" property="displayType"/>
        <result column="item_order_guid" jdbcType="VARCHAR" property="orderGuid"/>
        <result column="item_order_desc" jdbcType="VARCHAR" property="orderDesc"/>
        <result column="item_order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="item_order_serial_no" jdbcType="VARCHAR" property="orderSerialNo"/>
        <result column="item_order_remark" jdbcType="VARCHAR" property="orderRemark"/>
        <result column="item_area_guid" jdbcType="VARCHAR" property="areaGuid"/>
        <result column="item_table_guid" jdbcType="VARCHAR" property="tableGuid"/>
        <result column="item_order_item_guid" jdbcType="VARCHAR" property="orderItemGuid"/>
        <result column="item_item_guid" jdbcType="VARCHAR" property="itemGuid"/>
        <result column="item_item_name" jdbcType="VARCHAR" property="itemName"/>
        <result column="item_sku_guid" jdbcType="VARCHAR" property="skuGuid"/>
        <result column="item_sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="item_sku_code" jdbcType="VARCHAR" property="skuCode"/>
        <result column="item_sku_unit" jdbcType="VARCHAR" property="skuUnit"/>
        <result column="item_is_weight" jdbcType="TINYINT" property="isWeight"/>
        <result column="item_current_count" jdbcType="DECIMAL" property="currentCount"/>
        <result column="item_return_count" jdbcType="DECIMAL" property="returnCount"/>
        <result column="item_item_attr_md5" jdbcType="VARCHAR" property="itemAttrMd5"/>
        <result column="item_item_remark" jdbcType="VARCHAR" property="itemRemark"/>
        <result column="item_timeout" jdbcType="INTEGER" property="timeout"/>
        <result column="item_urged_times" jdbcType="INTEGER" property="urgedTimes"/>
        <result column="item_item_state" jdbcType="TINYINT" property="itemState"/>
        <result column="item_kitchen_state" jdbcType="TINYINT" property="kitchenState"/>
        <result column="item_is_print_automatic" jdbcType="TINYINT" property="isPrintAutomatic"/>
        <result column="item_prepare_time" jdbcType="TIMESTAMP" property="prepareTime"/>
        <result column="item_call_up_time" jdbcType="TIMESTAMP" property="callUpTime"/>
        <result column="item_hang_up_time" jdbcType="TIMESTAMP" property="hangUpTime"/>
        <result column="item_urged_time" jdbcType="TIMESTAMP" property="urgedTime"/>
        <result column="item_cook_staff_guid" jdbcType="VARCHAR" property="cookStaffGuid"/>
        <result column="item_cook_staff_name" jdbcType="VARCHAR" property="cookStaffName"/>
        <result column="item_cook_time" jdbcType="TIMESTAMP" property="cookTime"/>
        <result column="item_complete_staff_guid" jdbcType="VARCHAR" property="completeStaffGuid"/>
        <result column="item_complete_staff_name" jdbcType="VARCHAR" property="completeStaffName"/>
        <result column="item_complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="item_distribute_staff_guid" jdbcType="VARCHAR" property="distributeStaffGuid"/>
        <result column="item_distribute_staff_name" jdbcType="VARCHAR" property="distributeStaffName"/>
        <result column="item_distribute_time" jdbcType="TIMESTAMP" property="distributeTime"/>
        <result column="item_cancel_dst_staff_guid" jdbcType="VARCHAR" property="cancelDstStaffGuid"/>
        <result column="item_cancel_dst_staff_name" jdbcType="VARCHAR" property="cancelDstStaffName"/>
        <result column="item_cancel_dst_time" jdbcType="TIMESTAMP" property="cancelDstTime"/>
        <result column="item_gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="item_gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="delay_time_minutes" jdbcType="INTEGER" property="delayTimeMinutes"/>
        <result column="display_time" jdbcType="TIMESTAMP" property="displayTime"/>
        <result column="batch" jdbcType="INTEGER" property="batch"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="display_rule_type" jdbcType="INTEGER" property="displayRuleType"/>
        <result column="original_item_sku_name" jdbcType="VARCHAR" property="originalItemSkuName"/>
        <result column="item_add_item_batch" jdbcType="INTEGER" property="addItemBatch"/>
        <result column="item_first_add_item_time" jdbcType="TIMESTAMP" property="firstAddItemTime"/>
        <result column="item_add_item_time" jdbcType="TIMESTAMP" property="addItemTime"/>

        <association property="prdDeviceConfig" resultMap="PrdDeviceConfigMap"/>
        <association property="dstDeviceConfig" resultMap="DstDeviceConfigMap"/>
        <association property="itemConfig" resultMap="ItemConfigMap"/>
        <collection property="attrs" ofType="com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO"
                    resultMap="ItemAttrMap"/>
    </resultMap>

    <resultMap id="OrderItemMap" type="com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO">
        <id column="item_id" jdbcType="BIGINT" property="id"/>
        <result column="item_order_item_guid" jdbcType="VARCHAR" property="orderItemGuid"/>
        <result column="item_sku_guid" jdbcType="VARCHAR" property="skuGuid"/>
        <result column="item_is_weight" jdbcType="TINYINT" property="isWeight"/>
        <result column="item_order_remark" jdbcType="VARCHAR" property="orderRemark"/>
        <result column="item_item_remark" jdbcType="VARCHAR" property="itemRemark"/>
        <collection property="attrs" ofType="com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO"
                    resultMap="OrderItemAttrMap"/>
    </resultMap>

    <resultMap id="PrdDeviceConfigMap" type="com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO">
        <id column="pc_id" jdbcType="BIGINT" property="id"/>
        <result column="pc_is_show_hanged_item" jdbcType="TINYINT" property="isShowHangedItem"/>
        <result column="pc_is_produce_hanged_item" jdbcType="TINYINT" property="isProduceHangedItem"/>
        <result column="pc_is_manual_confirm" jdbcType="TINYINT" property="isManualConfirm"/>
    </resultMap>

    <resultMap id="DstDeviceConfigMap" type="com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO">
        <id column="dc_id" jdbcType="BIGINT" property="id"/>
        <result column="dc_is_display_item_un_produced" jdbcType="TINYINT" property="isDisplayItemUnProduced"/>
    </resultMap>

    <resultMap id="ItemConfigMap" type="com.holderzone.saas.store.kds.entity.domain.ItemConfigDO">
        <id column="config_id" jdbcType="BIGINT" property="id"/>
        <result column="config_guid" jdbcType="VARCHAR" property="guid"/>
        <result column="config_store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="config_sku_guid" jdbcType="VARCHAR" property="skuGuid"/>
        <result column="config_timeout" jdbcType="INTEGER" property="timeout"/>
        <result column="config_max_copies" jdbcType="INTEGER" property="maxCopies"/>
        <result column="config_display_type" jdbcType="INTEGER" property="displayType"/>
        <result column="config_gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="config_gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <resultMap id="ItemAttrMap" type="com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO">
        <id column="attr_id" jdbcType="BIGINT" property="id"/>
        <result column="attr_guid" jdbcType="VARCHAR" property="guid"/>
        <result column="attr_order_item_guid" jdbcType="VARCHAR" property="orderItemGuid"/>
        <result column="attr_group_guid" jdbcType="VARCHAR" property="groupGuid"/>
        <result column="attr_group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="attr_attr_guid" jdbcType="VARCHAR" property="attrGuid"/>
        <result column="attr_attr_name" jdbcType="VARCHAR" property="attrName"/>
        <result column="attr_attr_number" jdbcType="INTEGER" property="attrNumber"/>
        <result column="attr_gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="attr_gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <resultMap id="OrderItemAttrMap" type="com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO">
        <id column="attr_id" jdbcType="BIGINT" property="id"/>
        <result column="attr_attr_guid" jdbcType="VARCHAR" property="attrGuid"/>
    </resultMap>

    <resultMap id="PrdDstOrderMap" type="com.holderzone.saas.store.dto.kds.resp.PrdDstOrderDTO">
        <result column="item_order_guid" jdbcType="VARCHAR" property="orderGuid"/>
        <result column="item_display_type" jdbcType="TINYINT" property="orderType"/>
        <result column="item_order_desc" jdbcType="VARCHAR" property="orderDesc"/>
        <result column="item_order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="item_order_serial_no" jdbcType="VARCHAR" property="orderSerialNo"/>
        <result column="item_order_remark" jdbcType="VARCHAR" property="orderRemark"/>
        <result column="item_prepare_time" jdbcType="TIMESTAMP" property="prepareTime"/>
        <result column="item_call_up_time" jdbcType="TIMESTAMP" property="callUpTime"/>
        <result column="item_hang_up_time" jdbcType="TIMESTAMP" property="hangUpTime"/>
        <result column="item_urged_time" jdbcType="TIMESTAMP" property="urgedTime"/>
    </resultMap>

    <select id="queryPrepareItem" resultMap="KitchenItemMap"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from hsk_kitchen_item ki use index(idx_prepare_item)
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        where ki.item_state &lt; 4
        and ki.order_guid = #{orderGuid}
        and ki.kitchen_state = #{kitchenState}
        <if test="displayType != 8">
            and ki.display_type = #{displayType}
        </if>
        and
        <foreach collection="deviceIdList" item="autoPrintDeviceId" separator="or" open="(" close=")">
            ki.prd_device_id = #{autoPrintDeviceId}
        </foreach>
        and
        <foreach collection="kitchenItemGuidList" item="itemGuid" separator="or" open="(" close=")">
            ki.guid = #{itemGuid}
        </foreach>
        order by ki.gmt_create, ki.id
    </select>

    <select id="queryPrdPointItem" resultMap="PointItemMap"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        select
        <trim suffixOverrides=",">
            <include refid="PointColumn"/>
            <include refid="ItemColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from hsk_production_point p use index(idx_store)
        <choose>
            <when test="allowRepeatFlag != null and allowRepeatFlag">
                left join hsk_kitchen_item_device kid on kid.point_guid = p.guid
                left join hsk_kitchen_item ki on ki.guid = kid.kitchen_item_guid
            </when>
            <otherwise>
                left join hsk_kitchen_item ki use index(idx_prd_point_item) on ki.point_guid = p.guid
            </otherwise>
        </choose>
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        where
        p.device_id = #{deviceId}
        and p.store_guid = #{storeGuid}
        <if test="displayType != 8">
            and ki.display_type = #{displayType}
        </if>
        <if test="keywords != null and keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{keywords},'%')
            )
        </if>
        and ki.item_state &lt; 4
        and ki.kitchen_state = #{kitchenState}
        order by ki.gmt_create, ki.id
    </select>

    <select id="queryPrdItem" resultMap="KitchenItemMap"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from hsk_kitchen_item ki
        <if test="allowRepeatFlag == null or !allowRepeatFlag">
            use index(idx_prd_item)
            INNER JOIN hsk_production_point p ON p.guid = ki.point_guid
        </if>
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        where ki.store_guid = #{storeGuid}
        and ki.item_state &lt; 4
        and ki.kitchen_state = #{kitchenState}
        <choose>
            <when test="allowRepeatFlag != null and allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{deviceId} )
            </when>
            <otherwise>
                AND ki.prd_device_id = #{deviceId}
            </otherwise>
        </choose>
        <if test="displayType != 8">
            and ki.display_type = #{displayType}
        </if>
        <if test="keywords != null and keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{keywords},'%')
            )
        </if>
        <if test="pointGuid != null and pointGuid != ''">
            and ki.point_guid = #{pointGuid}
        </if>
        order by ki.gmt_create, ki.id
    </select>

    <select id="queryDstItem" resultMap="KitchenItemMap2"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="PrdDeviceColumn"/>
            <include refid="DstDeviceColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from hsk_kitchen_item ki
        <if test="allowRepeatFlag == null or !allowRepeatFlag">
            use index(idx_dst_item)
        </if>
        left join hsk_device_config pc on pc.guid = ki.prd_device_id
        left join hsk_device_config dc on dc.guid = ki.dst_device_id
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        where
            ki.store_guid = #{storeGuid}
        <choose>
            <when test="allowRepeatFlag != null and allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{deviceId}
            </otherwise>
        </choose>
        and ki.display_type = #{displayType}
        and ki.kitchen_state = 4
        and ki.item_state &lt; 4
        <if test="keywords != null and keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{keywords},'%')
            )
        </if>
        union all
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="PrdDeviceColumn"/>
            <include refid="DstDeviceColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from hsk_kitchen_item ki
        <if test="allowRepeatFlag == null or !allowRepeatFlag">
            use index(idx_dst_item)
        </if>
        left join hsk_device_config pc on pc.guid = ki.prd_device_id
        left join hsk_device_config dc on dc.guid = ki.dst_device_id
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        where
            ki.store_guid = #{storeGuid}
        <choose>
            <when test="allowRepeatFlag != null and allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{deviceId}
            </otherwise>
        </choose>
        and ki.display_type = #{displayType}
        and ki.kitchen_state = 5
        and ki.item_state &lt; 4
        <if test="keywords != null and keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{keywords},'%')
            )
        </if>
        union all
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="PrdDeviceColumn"/>
            <include refid="DstDeviceColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from hsk_kitchen_item ki
        <if test="allowRepeatFlag == null or !allowRepeatFlag">
            use index(idx_dst_item)
        </if>
        left join hsk_device_config pc on pc.guid = ki.prd_device_id
        left join hsk_device_config dc on dc.guid = ki.dst_device_id
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        where
            ki.store_guid = #{storeGuid}
        <choose>
            <when test="allowRepeatFlag != null and allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{deviceId}
            </otherwise>
        </choose>
        and ki.display_type = #{displayType}
        and ki.kitchen_state = 6
        and ki.item_state &lt; 4
        <if test="keywords != null and keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{keywords},'%')
            )
        </if>
        order by item_gmt_create, item_id
    </select>

    <select id="queryDstItemAll" resultMap="KitchenItemMap2"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="PrdDeviceColumn"/>
            <include refid="DstDeviceColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from hsk_kitchen_item ki
        <if test="allowRepeatFlag == null or !allowRepeatFlag">
            use index(idx_dst_item_all)
        </if>
        left join hsk_device_config pc on pc.guid = ki.prd_device_id
        left join hsk_device_config dc on dc.guid = ki.dst_device_id
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        where
            ki.store_guid = #{storeGuid}
        <choose>
            <when test="allowRepeatFlag != null and allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{deviceId}
            </otherwise>
        </choose>
        and ki.kitchen_state = 4
        and ki.item_state &lt; 4
        <if test="keywords != null and keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{keywords},'%')
            )
        </if>
        union all
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="PrdDeviceColumn"/>
            <include refid="DstDeviceColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from hsk_kitchen_item ki
        <if test="allowRepeatFlag == null or !allowRepeatFlag">
            use index(idx_dst_item_all)
        </if>
        left join hsk_device_config pc on pc.guid = ki.prd_device_id
        left join hsk_device_config dc on dc.guid = ki.dst_device_id
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        where
            ki.store_guid = #{storeGuid}
        <choose>
            <when test="allowRepeatFlag != null and allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{deviceId}
            </otherwise>
        </choose>
        and ki.kitchen_state = 5
        and ki.item_state &lt; 4
        <if test="keywords != null and keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{keywords},'%')
            )
        </if>
        union all
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="PrdDeviceColumn"/>
            <include refid="DstDeviceColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from hsk_kitchen_item ki
        <if test="allowRepeatFlag == null or !allowRepeatFlag">
            use index(idx_dst_item_all)
        </if>
        left join hsk_device_config pc on pc.guid = ki.prd_device_id
        left join hsk_device_config dc on dc.guid = ki.dst_device_id
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        where
            ki.store_guid = #{storeGuid}
        <choose>
            <when test="allowRepeatFlag != null and allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{deviceId}
            </otherwise>
        </choose>
        and ki.kitchen_state = 6
        and ki.item_state &lt; 4
        <if test="keywords != null and keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{keywords},'%')
            )
        </if>
        order by item_gmt_create, item_id
    </select>

    <select id="queryOrderItem" resultMap="OrderItemMap"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        select ki.id              as item_id,
               ki.order_item_guid as item_order_item_guid,
               ki.sku_guid        as item_sku_guid,
               ki.is_weight       as item_is_weight,
               ki.order_remark    as item_order_remark,
               ki.item_remark     as item_item_remark,
               ia.id              as attr_id,
               ia.attr_guid       as attr_attr_guid
        from hsk_kitchen_item ki
                 left join hsk_kitchen_item_attr ia
                           on ia.order_item_guid = ki.order_item_guid
        where ki.order_guid = #{orderGuid}
    </select>

    <sql id="orderByComparable">
        ORDER BY
        -ki.urged_time DESC,
        -ki.call_up_time DESC,
        -ki.sort DESC,
        -ki.prepare_time ASC,
        -ki.hang_up_time DESC
    </sql>

    <sql id="orderByComparableNew">
        ORDER BY
        	if(ISNULL(ki.urged_time),0,ki.urged_time) DESC,
        	if(ISNULL(ki.add_item_batch),0,ki.add_item_batch) DESC,
            if(ISNULL(ki.call_up_time),0,ki.call_up_time) DESC,
            if(ISNULL(ki.prepare_time),0,ki.prepare_time) ASC,
            if(ISNULL(ki.hang_up_time),0,ki.hang_up_time) desc
    </sql>

    <select id="queryPrdItemByOrder" resultMap="KitchenItemMap"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        SELECT
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        FROM
        hsk_kitchen_item ki
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            USE INDEX ( idx_prd_item )
            INNER JOIN hsk_production_point p ON p.guid = ki.point_guid
        </if>
        LEFT JOIN hsk_kitchen_item_attr ia ON ia.order_item_guid = ki.order_item_guid
        LEFT JOIN hsk_item_config ic ON ic.sku_guid = ki.sku_guid
        WHERE
        ki.store_guid = #{request.storeGuid}
        AND ki.item_state &lt; 4
        AND ki.kitchen_state = #{request.kitchenState}
        <choose>
            <when test="request.allowRepeatFlag != null and request.allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{request.deviceId} )
            </when>
            <otherwise>
                AND ki.prd_device_id = #{request.deviceId}
            </otherwise>
        </choose>
        AND (
        ki.urged_time IS NOT NULL
        OR (
        ki.display_time IS NULL
        OR ki.display_time &lt; NOW()))
        <if test="null!=request.orderGuidList and request.orderGuidList.size>0">
            AND ki.order_guid in
            <foreach collection="request.orderGuidList" item="orderGuid" separator="," open="(" close=")">
                #{orderGuid}
            </foreach>
        </if>
        <if test="request.isShowHangedItem != null and request.isShowHangedItem">
            AND (ki.item_state = 1 OR ki.item_state = 3)
        </if>
        <if test="request.keywords != null and request.keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{request.keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{request.keywords},'%')
            )
        </if>
        <if test="request.displayType != 8">
            and ki.display_type = #{request.displayType}
        </if>
        <if test="request.pointGuid != null and request.pointGuid != ''">
            and ki.point_guid = #{request.pointGuid}
        </if>
        <if test="request.displayRuleType == 0">
            AND (ki.display_rule_type = null OR ki.display_rule_type = -1 OR ki.display_rule_type = 0)
        </if>
        <include refid="orderByComparableNew"/>
    </select>

    <select id="queryPrdDstOrderAndItem" resultMap="PrdDstOrderMap"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        SELECT
        a.display_type AS item_display_type,
        a.order_guid AS item_order_guid,
        a.order_desc AS item_order_desc,
        a.order_number AS item_order_number,
        a.order_serial_no AS item_order_serial_no,
        a.order_remark AS item_order_remark,
        a.prepare_time AS item_prepare_time,
        a.hang_up_time AS item_hang_up_time,
        a.call_up_time AS item_call_up_time,
        a.urged_time AS item_urged_time
        FROM
        (
        SELECT
        ki.display_type,
        ki.order_guid,
        ki.order_desc,
        ki.order_number,
        ki.order_serial_no,
        ki.order_remark,
        ki.prepare_time,
        ki.hang_up_time,
        ki.call_up_time,
        ki.urged_time,
        ki.sort
        FROM
        hsk_kitchen_item ki
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            USE INDEX ( idx_prd_item )
            INNER JOIN hsk_production_point p ON p.guid = ki.point_guid
        </if>
        WHERE
        ki.store_guid = #{request.storeGuid}
        AND ki.item_state &lt;4
        AND ki.kitchen_state = #{request.kitchenState}
        <choose>
            <when test="request.allowRepeatFlag != null and request.allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{request.deviceId} )
            </when>
            <otherwise>
                AND ki.prd_device_id = #{request.deviceId}
            </otherwise>
        </choose>
        AND (
        ki.urged_time IS NOT NULL
        OR (
        ki.display_time IS NULL
        OR ki.display_time &lt; NOW()))
        <if test="request.displayType != 8">
            AND ki.display_type = #{request.displayType}
        </if>
        <if test="request.keywords != null and request.keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{request.keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{request.keywords},'%')
            )
        </if>
        <if test="request.pointGuid != null and request.pointGuid != ''">
            AND ki.point_guid = #{request.pointGuid}
        </if>
        <if test="request.isShowHangedItem != null and request.isShowHangedItem">
            AND (ki.item_state = 1 OR ki.item_state = 3)
        </if>
        <if test="request.displayRuleType == 0">
            AND (ki.display_rule_type = null OR ki.display_rule_type = -1 OR ki.display_rule_type = 0)
        </if>
        <include refid="orderByComparableNew"/>
        LIMIT 0,
        999999
        ) a
        GROUP BY
        a.order_guid
        ORDER BY
        if(ISNULL(max(a.urged_time)),0,max(a.urged_time)) DESC,
        if(ISNULL(max(a.call_up_time)),0,max(a.call_up_time)) DESC,
        if(ISNULL(min(a.prepare_time)),0,min(a.prepare_time)) ASC,
        if(ISNULL(max(a.hang_up_time)),0,max(a.hang_up_time)) DESC
    </select>

    <select id="queryPrdItemGroup" resultMap="KitchenItemMap"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        SELECT
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
        </trim>
        FROM
        hsk_kitchen_item ki
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            USE INDEX ( idx_prd_item )
            INNER JOIN hsk_production_point p ON p.guid = ki.point_guid
        </if>
        WHERE
        ki.store_guid = #{request.storeGuid}
        AND ki.item_state &lt;4
        AND ki.kitchen_state = #{request.kitchenState}
        <choose>
            <when test="request.allowRepeatFlag != null and request.allowRepeatFlag">
                AND ki.guid in (select
                                    kitchen_item_guid
                                FROM
                                    hsk_kitchen_item_device
                                where
                                    device_id = #{request.deviceId}
                                <if test="request.pointGuid != null and request.pointGuid != ''">
                                    AND point_guid = #{request.pointGuid}
                                </if>
                                )
            </when>
            <otherwise>
                AND ki.prd_device_id = #{request.deviceId}
            </otherwise>
        </choose>
        AND (
        ki.urged_time IS NOT NULL
        OR (
        ki.display_time IS NULL
        OR ki.display_time &lt; NOW()))
        <if test="request.displayType != 8">
            AND ki.display_type = #{request.displayType}
        </if>
        <if test="request.keywords != null and request.keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{request.keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{request.keywords},'%')
            )
        </if>
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            <if test="request.pointGuid != null and request.pointGuid != ''">
                AND ki.point_guid = #{request.pointGuid}
            </if>
        </if>
        <if test="request.isShowHangedItem != null and request.isShowHangedItem">
            AND (ki.item_state = 1 OR ki.item_state = 3)
        </if>
        <if test="request.displayRuleType == 1">
            AND (ki.item_guid IN (SELECT di.item_guid FROM hsk_display_item di WHERE di.rule_guid IN (
            SELECT dr.guid FROM hsk_display_rule dr WHERE dr.is_delete = 0 AND dr.rule_type =1 AND ( dr.guid in (
            SELECT ds.rule_guid FROM hsk_display_store ds WHERE ds.store_guid = #{request.storeGuid} AND ds.is_delete =0
            ) OR dr.is_all_store=1))) OR ki.display_rule_type = 1)
        </if>
        GROUP BY
        ki.item_attr_md5,
        <if test="request.itemSortType != null and request.itemSortType == 1">
            case when #{request.itemIntervalTime} <![CDATA[ <> ]]> null
                and TIMESTAMPDIFF(MINUTE,ki.first_add_item_time,ki.add_item_time)>#{request.itemIntervalTime}
            then 1
            else 0
            end,
        </if>
        ki.sku_guid,
        ki.order_remark
        <include refid="orderByComparableNew"/>
    </select>


    <select id="pageDstOrderItem" resultMap="PrdDstOrderMap"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        SELECT
        a.display_type AS item_display_type,
        a.order_guid AS item_order_guid,
        a.order_desc AS item_order_desc,
        a.order_number AS item_order_number,
        a.order_serial_no AS item_order_serial_no,
        a.order_remark AS item_order_remark,
        a.prepare_time AS item_prepare_time,
        a.hang_up_time AS item_hang_up_time,
        a.call_up_time AS item_call_up_time,
        a.urged_time AS item_urged_time
        FROM
        (
        SELECT
        ki.display_type,
        ki.order_guid,
        ki.order_desc,
        ki.order_number,
        ki.order_serial_no,
        ki.order_remark,
        ki.prepare_time,
        ki.hang_up_time,
        ki.call_up_time,
        ki.urged_time,
        ki.sort,
        ki.batch
        FROM
        hsk_kitchen_item ki
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            USE INDEX ( idx_dst_item_all )
        </if>
        left join hsk_device_config pc ON pc.guid = ki.prd_device_id
        left join hsk_device_config dc ON dc.guid = ki.dst_device_id
        WHERE
        IF ( dc.is_display_item_un_produced = 0,
        IF (pc.is_manual_confirm = 1, ki.kitchen_state = 6,( ki.kitchen_state = 5 OR ki.kitchen_state = 6)),
        IF ( pc.is_produce_hanged_item = 0,
        (( ki.kitchen_state = 4 AND ( ki.item_state = 1 OR ki.item_state = 3 )) OR ki.kitchen_state = 5 OR
        ki.kitchen_state = 6 ),
        ki.kitchen_state IN ( 4, 5, 6 )
        )
        )
        AND ki.store_guid = #{request.storeGuid}
        <choose>
            <when test="request.allowRepeatFlag != null and request.allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{request.deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{request.deviceId}
            </otherwise>
        </choose>
        AND ki.kitchen_state IN ( 4, 5, 6 )
        AND ki.item_state &lt; 4
        AND (
        ki.urged_time IS NOT NULL
        OR (
        ki.display_time IS NULL
        OR ki.display_time &lt; NOW()))
        <if test="request.displayType != 8">
            and ki.display_type = #{request.displayType}
        </if>
        <if test="request.keywords != null and request.keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{request.keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{request.keywords},'%')
            )
        </if>
        <if test="request.displayRuleType == 0">
            AND (ki.display_rule_type = null OR ki.display_rule_type = -1 OR ki.display_rule_type = 0)
        </if>
        <include refid="orderByComparableNew"/>
        LIMIT 0,
        999999
        ) a
        GROUP BY
        a.order_guid
        ORDER BY
        if(ISNULL(max(a.urged_time)),0,max(a.urged_time)) DESC,
        if(ISNULL(max(a.call_up_time)),0,max(a.call_up_time)) DESC,
        if(ISNULL(min(a.prepare_time)),0,min(a.prepare_time)) ASC,
        if(ISNULL(max(a.hang_up_time)),0,max(a.hang_up_time)) DESC
    </select>

    <select id="queryDstItemByOrder" resultMap="DstItemMap"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        select
        <trim suffixOverrides=",">
            <include refid="DstItemColumn"/>
            <include refid="DstItemAttrColumn"/>
            <include refid="DstItemConfigColumn"/>
        </trim>
        from hsk_kitchen_item ki
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            use index(idx_dst_item_all)
        </if>
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        left join hsk_device_config pc ON pc.guid = ki.prd_device_id
        left join hsk_device_config dc ON dc.guid = ki.dst_device_id
        WHERE
        IF ( dc.is_display_item_un_produced = 0,
        IF (pc.is_manual_confirm = 1, ki.kitchen_state = 6,( ki.kitchen_state = 5 OR ki.kitchen_state = 6)),
        IF ( pc.is_produce_hanged_item = 0,
        (( ki.kitchen_state = 4 AND ( ki.item_state = 1 OR ki.item_state = 3 )) OR ki.kitchen_state = 5 OR
        ki.kitchen_state = 6 ),
        ki.kitchen_state IN ( 4, 5, 6 )
        )
        )
        and ki.store_guid = #{request.storeGuid}
        <choose>
            <when test="request.allowRepeatFlag != null and request.allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{request.deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{request.deviceId}
            </otherwise>
        </choose>
        AND ki.kitchen_state IN ( 4, 5, 6 )
        <if test="request.displayType != 8">
            AND ki.display_type = #{request.displayType}
        </if>
        <if test="request.keywords != null and request.keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{request.keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{request.keywords},'%')
            )
        </if>
        and ki.item_state &lt; 4
        AND (
        ki.urged_time IS NOT NULL
        OR (
        ki.display_time IS NULL
        OR ki.display_time &lt; NOW()))
        <if test="null!=request.orderGuidList and request.orderGuidList.size>0">
            AND ki.order_guid in
            <foreach collection="request.orderGuidList" item="orderGuid" separator="," open="(" close=")">
                #{orderGuid}
            </foreach>
        </if>
        <if test="request.displayRuleType == 0">
            AND (ki.display_rule_type = null OR ki.display_rule_type = -1 OR ki.display_rule_type = 0)
        </if>
        <include refid="orderByComparableNew"/>
    </select>

    <select id="pageDstItemGroup" resultMap="KitchenItemMap2"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
        </trim>
        from
        hsk_kitchen_item ki
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            use index(idx_dst_item_all)
        </if>
        left join hsk_device_config pc ON pc.guid = ki.prd_device_id
        left join hsk_device_config dc ON dc.guid = ki.dst_device_id
        where
        IF ( dc.is_display_item_un_produced = 0,
        IF (pc.is_manual_confirm = 1, ki.kitchen_state = 6,( ki.kitchen_state = 5 OR ki.kitchen_state = 6)),
        IF ( pc.is_produce_hanged_item = 0,
        (( ki.kitchen_state = 4 AND ( ki.item_state = 1 OR ki.item_state = 3 )) OR ki.kitchen_state = 5 OR
        ki.kitchen_state = 6 ),
        ki.kitchen_state IN ( 4, 5, 6 )
        )
        )
        and ki.store_guid = #{request.storeGuid}
        <choose>
            <when test="request.allowRepeatFlag != null and request.allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{request.deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{request.deviceId}
            </otherwise>
        </choose>
        AND ki.kitchen_state IN ( 4 , 5, 6 )
        and ki.item_state &lt; 4
        AND ( ki.urged_time IS NOT NULL OR ( ki.display_time IS NULL OR ki.display_time &lt; NOW()))
        <if test="request.displayType != 8">
            AND ki.display_type = #{request.displayType}
        </if>
        <if test="request.keywords != null and request.keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{request.keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{request.keywords},'%')
            )
        </if>
        <if test="request.displayRuleType == 1">
            AND (ki.item_guid IN (SELECT di.item_guid FROM hsk_display_item di WHERE di.rule_guid IN (
            SELECT dr.guid FROM hsk_display_rule dr WHERE dr.is_delete = 0 AND dr.rule_type =1 AND ( dr.guid in (
            SELECT ds.rule_guid FROM hsk_display_store ds WHERE ds.store_guid = #{request.storeGuid} AND ds.is_delete =0
            ) OR dr.is_all_store=1))) OR ki.display_rule_type = 1)
        </if>
        GROUP BY
        ki.item_attr_md5,
        <if test="request.itemSortType != null and request.itemSortType == 1">
            case when #{request.itemIntervalTime} <![CDATA[ <> ]]> null
                and TIMESTAMPDIFF(MINUTE,ki.first_add_item_time,ki.add_item_time)>#{request.itemIntervalTime}
            then 1
            else 0
            end,
        </if>
        ki.sku_guid,
        ki.order_remark
        <include refid="orderByComparableNew"/>
    </select>


    <select id="pageDstItemAll" resultMap="KitchenItemMap2"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
        </trim>
        from hsk_kitchen_item ki
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            use index(idx_dst_item_all)
        </if>
        left join hsk_device_config pc on pc.guid = ki.prd_device_id
        left join hsk_device_config dc on dc.guid = ki.dst_device_id
        where
        IF ( dc.is_display_item_un_produced = 0,
        IF (pc.is_manual_confirm = 1, ki.kitchen_state = 6,( ki.kitchen_state = 5 OR ki.kitchen_state = 6)),
        IF ( pc.is_produce_hanged_item = 0,
        (( ki.kitchen_state = 4 AND ( ki.item_state = 1 OR ki.item_state = 3 )) OR ki.kitchen_state = 5 OR
        ki.kitchen_state = 6 ),
        ki.kitchen_state IN ( 4, 5, 6 )
        )
        )
        and ki.store_guid = #{request.storeGuid}
        <choose>
            <when test="request.allowRepeatFlag != null and request.allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{request.deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{request.deviceId}
            </otherwise>
        </choose>
        AND ki.kitchen_state IN ( 4 , 5, 6 )
        and ki.item_state &lt; 4
        AND (
        ki.urged_time IS NOT NULL
        OR (
        ki.display_time IS NULL
        OR ki.display_time &lt; NOW()))
        <if test="request.displayType != 8">
            AND ki.display_type = #{request.displayType}
        </if>
        <if test="request.keywords != null and request.keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{request.keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{request.keywords},'%')
            )
        </if>
        <include refid="orderByComparableNew"/>
    </select>

    <select id="queryPrdItemBySku" resultMap="KitchenItemMap"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        SELECT
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        FROM
        hsk_kitchen_item ki
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            USE INDEX ( idx_prd_item )
            INNER JOIN hsk_production_point p ON p.guid = ki.point_guid
        </if>
        LEFT JOIN hsk_kitchen_item_attr ia ON ia.order_item_guid = ki.order_item_guid
        LEFT JOIN hsk_item_config ic ON ic.sku_guid = ki.sku_guid
        WHERE
        ki.store_guid = #{request.storeGuid}
        AND ki.item_state &lt;4
        AND ki.kitchen_state = #{request.kitchenState}
        <choose>
            <when test="request.allowRepeatFlag != null and request.allowRepeatFlag">
                AND ki.guid in (select
                                    kitchen_item_guid
                                FROM
                                    hsk_kitchen_item_device
                                where
                                    device_id = #{request.deviceId}
                                <if test="request.pointGuid != null and request.pointGuid != ''">
                                    AND point_guid = #{request.pointGuid}
                                </if>
                )
            </when>
            <otherwise>
                AND ki.prd_device_id = #{request.deviceId}
            </otherwise>
        </choose>
        AND (
        ki.urged_time IS NOT NULL
        OR (
        ki.display_time IS NULL
        OR ki.display_time &lt; NOW()))
        <if test="null != request.skuGuidList and request.skuGuidList.size>0">
            AND CONCAT(ifnull(ki.item_attr_md5,'') ,'-',ifnull(ki.sku_guid,''),'-',ifnull(ki.order_remark,'')) IN
            <foreach collection="request.skuGuidList" item="skuGuid" separator="," open="(" close=")">
                #{skuGuid}
            </foreach>
        </if>
        <if test="request.displayType != 8">
            and ki.display_type = #{request.displayType}
        </if>
        <if test="request.keywords != null and request.keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{request.keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{request.keywords},'%')
            )
        </if>
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            <if test="request.pointGuid != null and request.pointGuid != ''">
                AND ki.point_guid = #{request.pointGuid}
            </if>
        </if>
        <if test="request.isShowHangedItem != null and request.isShowHangedItem">
            AND (ki.item_state = 1 OR ki.item_state = 3)
        </if>
        <if test="request.displayRuleType == 1">
            AND (ki.item_guid IN (SELECT di.item_guid FROM hsk_display_item di WHERE di.rule_guid IN (
            SELECT dr.guid FROM hsk_display_rule dr WHERE dr.is_delete = 0 AND dr.rule_type =1 AND ( dr.guid in (
            SELECT ds.rule_guid FROM hsk_display_store ds WHERE ds.store_guid = #{request.storeGuid} AND ds.is_delete =0
            ) OR dr.is_all_store=1))) OR ki.display_rule_type = 1)
        </if>
        <include refid="orderByComparableNew"/>
    </select>

    <select id="queryDstItemBySku" resultMap="KitchenItemMap2"
            parameterType="com.holderzone.saas.store.kds.entity.query.ItemQuery">
        select
        <trim suffixOverrides=",">
            <include refid="ItemColumn"/>
            <include refid="PrdDeviceColumn"/>
            <include refid="DstDeviceColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from
        hsk_kitchen_item ki
        <if test="request.allowRepeatFlag == null or !request.allowRepeatFlag">
            use index(idx_dst_item_all)
        </if>
        left join hsk_device_config pc on pc.guid = ki.prd_device_id
        left join hsk_device_config dc on dc.guid = ki.dst_device_id
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        where
        IF ( dc.is_display_item_un_produced = 0,
        IF (pc.is_manual_confirm = 1, ki.kitchen_state = 6,( ki.kitchen_state = 5 OR ki.kitchen_state = 6)),
        IF ( pc.is_produce_hanged_item = 0,
        (( ki.kitchen_state = 4 AND ( ki.item_state = 1 OR ki.item_state = 3 )) OR ki.kitchen_state = 5 OR
        ki.kitchen_state = 6 ),
        ki.kitchen_state IN ( 4, 5, 6 )
        )
        )
        and ki.store_guid = #{request.storeGuid}
        <choose>
            <when test="request.allowRepeatFlag != null and request.allowRepeatFlag">
                AND ki.guid in (select kitchen_item_guid FROM hsk_kitchen_item_device where device_id = #{request.deviceId} )
            </when>
            <otherwise>
                 and ki.dst_device_id = #{request.deviceId}
            </otherwise>
        </choose>
        AND ki.kitchen_state IN ( 4 , 5, 6 )
        and ki.item_state &lt; 4
        AND ( ki.urged_time IS NOT NULL OR ( ki.display_time IS NULL OR ki.display_time &lt; NOW()))
        <if test="request.displayType != 8">
            AND ki.display_type = #{request.displayType}
        </if>
        <if test="request.keywords != null and request.keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{request.keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{request.keywords},'%')
            )
        </if>
        <if test="null != request.skuGuidList and request.skuGuidList.size>0">
            AND CONCAT(ifnull(ki.item_attr_md5,'') ,'-',ifnull(ki.sku_guid,''),'-',ifnull(ki.order_remark,'')) IN
            <foreach collection="request.skuGuidList" item="skuGuid" separator="," open="(" close=")">
                #{skuGuid}
            </foreach>
        </if>
        <if test="null != request.kitchenItemGuidList and request.kitchenItemGuidList.size>0">
            AND ki.guid IN
            <foreach collection="request.kitchenItemGuidList" item="guid" separator="," open="(" close=")">
                #{guid}
            </foreach>
        </if>
        <if test="request.displayRuleType == 1">
            AND (ki.item_guid IN (SELECT di.item_guid FROM hsk_display_item di WHERE di.rule_guid IN (
            SELECT dr.guid FROM hsk_display_rule dr WHERE dr.is_delete = 0 AND dr.rule_type =1 AND ( dr.guid in (
            SELECT ds.rule_guid FROM hsk_display_store ds WHERE ds.store_guid = #{request.storeGuid} AND ds.is_delete =0
            ) OR dr.is_all_store=1))) OR ki.display_rule_type = 1)
        </if>
        <include refid="orderByComparableNew"/>
    </select>

    <select id="queryDstGroupBindItem" resultMap="GroupBindItemMap"
            resultType="com.holderzone.saas.store.kds.entity.read.GroupBindItemReadDO">
        select
        <trim suffixOverrides=",">
            <include refid="GroupColumn"/>
            <include refid="ItemColumn"/>
            <include refid="ItemAttrColumn"/>
            <include refid="ItemConfigColumn"/>
        </trim>
        from
            hsk_kitchen_item_device kid
        join hsk_bind_item_group big on big.guid = kid.group_guid and big.is_delete = 0
        left join hsk_kitchen_item ki on ki.guid = kid.kitchen_item_guid
        left join hsk_kitchen_item_attr ia on ia.order_item_guid = ki.order_item_guid
        left join hsk_item_config ic on ic.sku_guid = ki.sku_guid
        left join hsk_device_config pc on pc.guid = ki.prd_device_id
        left join hsk_device_config dc on dc.guid = ki.dst_device_id
        where
            IF ( dc.is_display_item_un_produced = 0,
            IF (pc.is_manual_confirm = 1, ki.kitchen_state = 6,( ki.kitchen_state = 5 OR ki.kitchen_state = 6)),
            IF ( pc.is_produce_hanged_item = 0,
            (( ki.kitchen_state = 4 AND ( ki.item_state = 1 OR ki.item_state = 3 )) OR ki.kitchen_state = 5 OR
            ki.kitchen_state = 6 ),
            ki.kitchen_state IN ( 4, 5, 6 )
            )
            )
            and kid.device_id = #{deviceId}
        <if test="groupGuids != null and groupGuids.size()>0">
            and kid.group_guid in
            <foreach collection="groupGuids" item="groupGuid" open="(" separator="," close=")">
                #{groupGuid}
            </foreach>
        </if>
        <if test="displayType != 8">
            and ki.display_type = #{displayType}
        </if>
        <if test="keywords != null and keywords != ''">
            AND (
                ki.item_name like CONCAT('%',#{keywords},'%')
                OR
                ki.sku_code like CONCAT('%',#{keywords},'%')
            )
        </if>
        and ki.item_state &lt; 4
        and ki.kitchen_state IN ( 4, 5, 6 )
        order by ki.gmt_create, ki.id
    </select>

    <update id="updateOrderRemark" parameterType="java.util.List">
        update hsk_kitchen_item
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_item_guid = #{item.orderItemGuid} then #{item.orderRemark}
                </foreach>
            </trim>
            <trim prefix="item_attr_md5 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_item_guid = #{item.orderItemGuid} then #{item.itemAttrMd5}
                </foreach>
            </trim>
        </trim>
        where order_item_guid in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.orderItemGuid}
        </foreach>
    </update>

    <update id="cancelDistribute" parameterType="com.holderzone.saas.store.kds.entity.domain.KitchenItemDO">
        update
        hsk_kitchen_item
        set
        kitchen_state = 6,
        <if test="cookTime != null">
            cook_time = #{cookTime},
        </if>
        <if test="cookStaffGuid != null and cookStaffGuid != ''">
            cook_staff_guid = #{cookStaffGuid},
        </if>
        <if test="cookStaffName != null and cookStaffName != ''">
            cook_staff_name = #{cookStaffName},
        </if>
        distribute_time = null,
        distribute_staff_guid = null,
        distribute_staff_name = null,
        cancel_dst_time = #{cancelDstTime},
        cancel_dst_staff_guid = #{cancelDstStaffGuid},
        cancel_dst_staff_name = #{cancelDstStaffName}
        where guid = #{guid} and kitchen_state = 7
    </update>

    <update id="increaseUrgedTimes" parameterType="java.util.List">
        update
        hsk_kitchen_item
        set
        urged_times = urged_times + 1,
        urged_time = now()
        where
        <foreach collection="orderItemGuidList" item="orderItemGuid" separator="or" open="(" close=")">
            order_item_guid = #{orderItemGuid}
        </foreach>
    </update>

    <update id="batchUpdateById" parameterType="java.util.List">
        <foreach collection="list" item="kitchenItem" separator=";">
            update hsk_kitchen_item
            <set>
                <if test="null != kitchenItem.itemGuid and '' != kitchenItem.itemGuid">
                    item_guid = #{kitchenItem.itemGuid},
                </if>
                <if test="null != kitchenItem.itemName and '' != kitchenItem.itemName">
                    item_name = #{kitchenItem.itemName},
                </if>
                <if test="null != kitchenItem.skuGuid and '' != kitchenItem.skuGuid">
                    sku_guid = #{kitchenItem.skuGuid},
                </if>
                <if test="null != kitchenItem.skuUnit and '' != kitchenItem.skuUnit">
                    sku_unit = #{kitchenItem.skuUnit},
                </if>
                <if test="null != kitchenItem.skuCode and '' != kitchenItem.skuCode">
                    sku_code = #{kitchenItem.skuCode},
                </if>
                <if test="null != kitchenItem.isWeight and '' != kitchenItem.isWeight">
                    is_weight = #{kitchenItem.isWeight},
                </if>
                <if test="null != kitchenItem.itemRemark and '' != kitchenItem.itemRemark">
                    item_remark = #{kitchenItem.itemRemark},
                </if>
                <if test="null != kitchenItem.originalItemGuid and '' != kitchenItem.originalItemGuid">
                    original_item_guid = #{kitchenItem.originalItemGuid},
                </if>
                <if test="null != kitchenItem.originalSkuGuid and '' != kitchenItem.originalSkuGuid">
                    original_sku_guid = #{kitchenItem.originalSkuGuid},
                </if>
                <if test="null != kitchenItem.originalItemSkuName and '' != kitchenItem.originalItemSkuName">
                    original_item_sku_name = #{kitchenItem.originalItemSkuName},
                </if>
                <if test="null != kitchenItem.itemAttrMd5 and '' != kitchenItem.itemAttrMd5">
                    item_attr_md5 = #{kitchenItem.itemAttrMd5},
                </if>
                <if test="null != kitchenItem.kitchenState">
                    kitchen_state = #{kitchenItem.kitchenState},
                </if>
                <if test="null != kitchenItem.itemState">
                    item_state = #{kitchenItem.itemState},
                </if>
                <if test="null != kitchenItem.batch">
                    batch = #{kitchenItem.batch},
                </if>
                <if test="null != kitchenItem.displayRuleType">
                    display_rule_type = #{kitchenItem.displayRuleType},
                </if>
                <if test="null != kitchenItem.sort">
                    sort = #{kitchenItem.sort},
                </if>
                <if test="null != kitchenItem.delayTimeMinutes">
                    delay_time_minutes = #{kitchenItem.delayTimeMinutes},
                </if>
                <if test="null != kitchenItem.displayTime">
                    display_time = #{kitchenItem.displayTime},
                </if>
                <if test="null != kitchenItem.hangUpTime">
                    hang_up_time = #{kitchenItem.hangUpTime},
                </if>
                <if test="null != kitchenItem.prepareTime">
                    prepare_time = #{kitchenItem.prepareTime},
                </if>
                <if test="null != kitchenItem.orderSortTime">
                    order_sort_time = #{kitchenItem.orderSortTime},
                </if>
                point_guid = #{kitchenItem.pointGuid},
                prd_device_id = #{kitchenItem.prdDeviceId},
                dst_device_id = #{kitchenItem.dstDeviceId},
                sku_name = #{kitchenItem.skuName}
            </set>
            where
                guid = #{kitchenItem.guid}
        </foreach>
    </update>

    <sql id="PrdDeviceColumn">
        pc
        .
        id
        as pc_id,
        pc.is_show_hanged_item as pc_is_show_hanged_item,
        pc.is_produce_hanged_item as pc_is_produce_hanged_item,
        pc.is_manual_confirm as pc_is_manual_confirm,
    </sql>

    <sql id="DstDeviceColumn">
        dc
        .
        id
        as dc_id,
        dc.is_display_item_un_produced as dc_is_display_item_un_produced,
    </sql>

    <sql id="PointColumn">
        p
        .
        id
        as point_id,
        p.guid as point_guid,
        p.store_guid as point_store_guid,
        p.device_id as point_device_id,
        p.name as point_name,
        p.gmt_create as point_gmt_create,
        p.gmt_modified as point_gmt_modified,
    </sql>

    <sql id="GroupColumn">
        big
        .
        id
        as group_id,
        big.guid as group_guid,
        big.name as group_name,
        big.gmt_create as group_gmt_create,
        big.gmt_modified as group_gmt_modified,
    </sql>

    <sql id="DstItemColumn">
        ki.guid as item_guid,
        ki.display_type as item_display_type,
        ki.order_guid as item_order_guid,
        ki.order_desc as item_order_desc,
        ki.order_number as item_order_number,
        ki.order_serial_no as item_order_serial_no,
        ki.order_remark as item_order_remark,
        ki.area_guid as item_area_guid,
        ki.order_item_guid as item_order_item_guid,
        ki.item_guid as item_item_guid,
        ki.item_name as item_item_name,
        ki.sku_guid as item_sku_guid,
        ki.sku_name as item_sku_name,
        ki.sku_code as item_sku_code,
        ki.sku_unit as item_sku_unit,
        ki.is_weight as item_is_weight,
        ki.current_count as item_current_count,
        ki.item_attr_md5 as item_item_attr_md5,
        ki.item_remark as item_item_remark,
        ki.timeout as item_timeout,
        ki.item_state as item_item_state,
        ki.kitchen_state as item_kitchen_state,
        ki.prepare_time as item_prepare_time,
        ki.hang_up_time as item_hang_up_time,
        ki.call_up_time as item_call_up_time,
        ki.urged_time as item_urged_time,
        ki.batch as batch,
        ki.sort as sort,
        ki.display_rule_type as display_rule_type,
        ki.original_item_sku_name as original_item_sku_name,
        ki.add_item_batch item_add_item_batch,
        ki.first_add_item_time item_first_add_item_time,
        ki.add_item_time item_add_item_time,
    </sql>

    <sql id="ItemColumn">
        ki
        .
        id
        as item_id,
        ki.guid as item_guid,
        ki.store_guid as item_store_guid,
        ki.point_guid as item_point_guid,
        ki.prd_device_id as item_prd_device_id,
        ki.dst_device_id as item_dst_device_id,
        ki.display_type as item_display_type,
        ki.order_guid as item_order_guid,
        ki.order_desc as item_order_desc,
        ki.order_number as item_order_number,
        ki.order_serial_no as item_order_serial_no,
        ki.order_remark as item_order_remark,
        ki.area_guid as item_area_guid,
        ki.table_guid as item_table_guid,
        ki.order_item_guid as item_order_item_guid,
        ki.item_guid as item_item_guid,
        ki.item_name as item_item_name,
        ki.sku_guid as item_sku_guid,
        ki.sku_name as item_sku_name,
        ki.sku_code as item_sku_code,
        ki.sku_unit as item_sku_unit,
        ki.is_weight as item_is_weight,
        ki.current_count as item_current_count,
        ki.return_count as item_return_count,
        ki.item_attr_md5 as item_item_attr_md5,
        ki.item_remark as item_item_remark,
        ki.timeout as item_timeout,
        ki.urged_times as item_urged_times,
        ki.item_state as item_item_state,
        ki.kitchen_state as item_kitchen_state,
        ki.is_print_automatic as item_is_print_automatic,
        ki.prepare_time as item_prepare_time,
        ki.hang_up_time as item_hang_up_time,
        ki.call_up_time as item_call_up_time,
        ki.urged_time as item_urged_time,
        ki.cook_staff_guid as item_cook_staff_guid,
        ki.cook_staff_name as item_cook_staff_name,
        ki.cook_time as item_cook_time,
        ki.complete_staff_guid as item_complete_staff_guid,
        ki.complete_staff_name as item_complete_staff_name,
        ki.complete_time as item_complete_time,
        ki.distribute_staff_guid as item_distribute_staff_guid,
        ki.distribute_staff_name as item_distribute_staff_name,
        ki.distribute_time as item_distribute_time,
        ki.cancel_dst_staff_guid as item_cancel_dst_staff_guid,
        ki.cancel_dst_staff_name as item_cancel_dst_staff_name,
        ki.cancel_dst_time as item_cancel_dst_time,
        ki.gmt_create as item_gmt_create,
        ki.gmt_modified as item_gmt_modified,
        ki.delay_time_minutes as delay_time_minutes,
        ki.display_time as display_time,
        ki.batch as batch,
        ki.sort as sort,
        ki.display_rule_type as display_rule_type,
        ki.original_item_sku_name as original_item_sku_name,
        ki.add_item_batch item_add_item_batch,
        ki.first_add_item_time item_first_add_item_time,
        ki.add_item_time item_add_item_time,
    </sql>
    <sql id="DstItemAttrColumn">

        ia.group_guid as attr_group_guid,
        ia.group_name as attr_group_name,
        ia.attr_guid as attr_attr_guid,
        ia.attr_name as attr_attr_name,
        ia.attr_number as attr_attr_number,
    </sql>
    <sql id="ItemAttrColumn">
        ia
        .
        id
        as attr_id,
        ia.guid as attr_guid,
        ia.order_item_guid as attr_order_item_guid,
        ia.group_guid as attr_group_guid,
        ia.group_name as attr_group_name,
        ia.attr_guid as attr_attr_guid,
        ia.attr_name as attr_attr_name,
        ia.attr_number as attr_attr_number,
        ia.gmt_create as attr_gmt_create,
        ia.gmt_modified as attr_gmt_modified,
    </sql>
    <sql id="DstItemConfigColumn">
        ic.timeout as config_timeout,
        ic.display_type as config_display_type,
    </sql>
    <sql id="ItemConfigColumn">
        ic
        .
        id
        as config_id,
        ic.guid as config_guid,
        ic.store_guid as config_store_guid,
        ic.sku_guid as config_sku_guid,
        ic.timeout as config_timeout,
        ic.max_copies as config_max_copies,
        ic.display_type as config_display_type,
        ic.gmt_create as config_gmt_create,
        ic.gmt_modified as config_gmt_modified,
    </sql>

</mapper>
