<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.MemberMapper">
    
    
    <select id="queryFundingDetail"
            resultType="com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailRespDTO">
        SELECT
            mf.guid,
            mf.store_name,
            (case WHEN mf.amount_recharge_funding_type = 0 THEN +mf.recharge_amount else -mf.recharge_amount END) AS recharge_amount,
            (case WHEN mf.amount_gift_funding_type = 0 THEN +mf.gift_amount else -mf.gift_amount END) AS gift_amount,
            (case WHEN mf.amount_subsidy_funding_type = 0 THEN +mf.subsidy_amount else -mf.subsidy_amount END) AS subsidy_amount,
            pw.pay_name,
            mf.member_info_guid,
            mf.member_info_card_guid,
            mf.gmt_create,
            mf.amount_source_type,
            mf.operator_account_name
        FROM
            "hsm_alliance_member_platform_db"."hsa_member_funding_detail" mf
            LEFT JOIN "hsm_alliance_member_platform_db"."hsa_member_consumption_pay_way" pw ON mf.member_consumption_guid = pw.consumption_guid and mf.amount_source_type = 1
        WHERE
                mf.enterprise_guid = #{query.enterpriseGuid}
            and mf.oper_subject_guid = #{query.operSubjectGuid}
            AND mf.is_valid = 1
            <if test="query.gmtCreate != null">
                and mf.gmt_create >= #{query.gmtCreate}
            </if>
            <if test="query.cursor != null">
                and mf.guid <![CDATA[ < ]]> #{query.cursor}
            </if>
        order by mf.gmt_create desc, mf.guid desc
    </select>
    
    
    
    <select id="queryMember"
            resultType="com.holderzone.saas.store.dto.report.openapi.MemberDetailRespDTO">
        SELECT
            omi.guid,
            omi.phone_num,
            omi.user_name as member_name
        FROM
            "hsm_alliance_member_platform_db"."hsa_operation_member_info" omi
        WHERE
            omi.guid in
            <foreach collection="list" item="guid" open="(" separator="," close=")">
                #{guid}
            </foreach>
    </select>
    
    
    <select id="queryMemberCard"
            resultType="com.holderzone.saas.store.dto.report.openapi.MemberCardRespDTO">
        SELECT
            mic.guid as member_info_card_guid,
            mic.card_num,
            mic.card_level_name
        FROM
            "hsm_alliance_member_platform_db"."hsa_member_info_card" mic
        WHERE
            mic.guid in
            <foreach collection="list" item="guid" open="(" separator="," close=")">
                #{guid}
            </foreach>
    </select>

</mapper>
