<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.member.mapper.MemberMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.saas.store.member.domain.MemberDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="member_guid" jdbcType="VARCHAR" property="memberGuid" />
    <result column="store_guid" jdbcType="VARCHAR" property="storeGuid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="sex" jdbcType="TINYINT" property="sex" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="birthday" jdbcType="DATE" property="birthday" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="expiry_date" jdbcType="DATE" property="expiryDate" />
    <result column="expiry_type" jdbcType="TINYINT" property="expiryType" />
    <result column="expiry_config_key" jdbcType="TINYINT" property="expiryConfigKey" />
    <result column="recently_login_date" jdbcType="TIMESTAMP" property="recentlyLoginDate" />
    <result column="recently_consume_date" jdbcType="TIMESTAMP" property="recentlyConsumeDate" />
    <result column="member_grade_guid" jdbcType="VARCHAR" property="memberGradeGuid" />
    <result column="total_consume_num" jdbcType="INTEGER" property="totalConsumeNum" />
    <result column="total_prepaid_num" jdbcType="INTEGER" property="totalPrepaidNum" />
    <result column="total_integral" jdbcType="INTEGER" property="totalIntegral" />
    <result column="residual_integral" jdbcType="INTEGER" property="residualIntegral" />
    <result column="total_consume_fee" jdbcType="DECIMAL" property="totalConsumeFee" />
    <result column="total_pay_fee" jdbcType="DECIMAL" property="totalPayFee" />
    <result column="total_prepaid_fee" jdbcType="DECIMAL" property="totalPrepaidFee" />
    <result column="total_present_fee" jdbcType="DECIMAL" property="totalPresentFee" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="staff_guid" jdbcType="VARCHAR" property="staffGuid" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, gmt_create, gmt_modified, is_delete, member_guid, store_guid, `name`, phone, 
    sex, account_number, `password`, birthday, start_date, expiry_date, expiry_type, expiry_config_key, recently_login_date,
    recently_consume_date, member_grade_guid, total_consume_num, total_prepaid_num, total_integral, 
    residual_integral, total_consume_fee, total_pay_fee, total_prepaid_fee, total_present_fee, 
    balance, staff_guid, staff_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hsm_member
    where member_guid = #{memberGuid,jdbcType=VARCHAR}
  </select>
  <select id="getPasswordByPhone" resultType="java.lang.String" parameterType="java.lang.String">
    select
    password
    from hsm_member
    where phone = #{phone}
  </select>
  <select id="getMemberByPhone"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hsm_member
    where phone = #{phone,jdbcType=VARCHAR}
  </select>
  <select id="getMemberByGuid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hsm_member
    where member_guid = #{memberGuid,jdbcType=VARCHAR}
  </select>
  <select id="getMemberByGuidForUpdate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hsm_member
    where member_guid = #{memberGuid,jdbcType=VARCHAR}
    for update
  </select>
  <select id="getMemberByPhoneAndPasswd" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hsm_member
    where phone = #{phone,jdbcType=VARCHAR}
    and password = #{password,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from hsm_member
     where member_guid = #{memberGuid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.holderzone.saas.store.member.domain.MemberDO">
    insert into hsm_member (id, gmt_create, gmt_modified, 
      is_delete, member_guid, store_guid, 
      `name`, phone, sex, account_number, 
      `password`, birthday, start_date, 
      expiry_date, expiry_type, expiry_config_key, recently_login_date, recently_consume_date,
      member_grade_guid, total_consume_num, total_prepaid_num, 
      total_integral, residual_integral, total_consume_fee, 
      total_pay_fee, total_prepaid_fee, total_present_fee, 
      balance, staff_guid, staff_name
      )
    values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=BIT}, #{memberGuid,jdbcType=VARCHAR}, #{storeGuid,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{sex,jdbcType=TINYINT}, #{accountNumber,jdbcType=VARCHAR}, 
      #{password,jdbcType=VARCHAR}, #{birthday,jdbcType=DATE}, #{startDate,jdbcType=DATE}, 
      #{expiryDate,jdbcType=DATE},#{expiryType,jdbcType=TINYINT},#{expiryConfigKey,jdbcType=TINYINT}, #{recentlyLoginDate,jdbcType=TIMESTAMP}, #{recentlyConsumeDate,jdbcType=TIMESTAMP},
      #{memberGradeGuid,jdbcType=VARCHAR}, #{totalConsumeNum,jdbcType=INTEGER}, #{totalPrepaidNum,jdbcType=INTEGER}, 
      #{totalIntegral,jdbcType=INTEGER}, #{residualIntegral,jdbcType=INTEGER}, #{totalConsumeFee,jdbcType=DECIMAL}, 
      #{totalPayFee,jdbcType=DECIMAL}, #{totalPrepaidFee,jdbcType=DECIMAL}, #{totalPresentFee,jdbcType=DECIMAL}, 
      #{balance,jdbcType=DECIMAL}, #{staffGuid,jdbcType=VARCHAR}, #{staffName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.holderzone.saas.store.member.domain.MemberDO">
    insert into hsm_member
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="memberGuid != null">
        member_guid,
      </if>
      <if test="storeGuid != null">
        store_guid,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="accountNumber != null">
        account_number,
      </if>
      <if test="password != null">
        `password`,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="expiryDate != null">
        expiry_date,
      </if>
      <if test="expiryType != null">
        expiry_type,
      </if>
      <if test="expiryConfigKey != null">
        expiry_config_key,
      </if>
      <if test="recentlyLoginDate != null">
        recently_login_date,
      </if>
      <if test="recentlyConsumeDate != null">
        recently_consume_date,
      </if>
      <if test="memberGradeGuid != null">
        member_grade_guid,
      </if>
      <if test="totalConsumeNum != null">
        total_consume_num,
      </if>
      <if test="totalPrepaidNum != null">
        total_prepaid_num,
      </if>
      <if test="totalIntegral != null">
        total_integral,
      </if>
      <if test="residualIntegral != null">
        residual_integral,
      </if>
      <if test="totalConsumeFee != null">
        total_consume_fee,
      </if>
      <if test="totalPayFee != null">
        total_pay_fee,
      </if>
      <if test="totalPrepaidFee != null">
        total_prepaid_fee,
      </if>
      <if test="totalPresentFee != null">
        total_present_fee,
      </if>
      <if test="balance != null">
        balance,
      </if>
      <if test="staffGuid != null">
        staff_guid,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="memberGuid != null">
        #{memberGuid,jdbcType=VARCHAR},
      </if>
      <if test="storeGuid != null">
        #{storeGuid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=TINYINT},
      </if>
      <if test="accountNumber != null">
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="expiryDate != null">
        #{expiryDate,jdbcType=DATE},
      </if>
      <if test="expiryType != null">
        #{expiryType,jdbcType=TINYINT},
      </if>
      <if test="expiryConfigKey != null">
        #{expiryConfigKey,jdbcType=TINYINT},
      </if>
      <if test="recentlyLoginDate != null">
        #{recentlyLoginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="recentlyConsumeDate != null">
        #{recentlyConsumeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="memberGradeGuid != null">
        #{memberGradeGuid,jdbcType=VARCHAR},
      </if>
      <if test="totalConsumeNum != null">
        #{totalConsumeNum,jdbcType=INTEGER},
      </if>
      <if test="totalPrepaidNum != null">
        #{totalPrepaidNum,jdbcType=INTEGER},
      </if>
      <if test="totalIntegral != null">
        #{totalIntegral,jdbcType=INTEGER},
      </if>
      <if test="residualIntegral != null">
        #{residualIntegral,jdbcType=INTEGER},
      </if>
      <if test="totalConsumeFee != null">
        #{totalConsumeFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPayFee != null">
        #{totalPayFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrepaidFee != null">
        #{totalPrepaidFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPresentFee != null">
        #{totalPresentFee,jdbcType=DECIMAL},
      </if>
      <if test="balance != null">
        #{balance,jdbcType=DECIMAL},
      </if>
      <if test="staffGuid != null">
        #{staffGuid,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.holderzone.saas.store.member.domain.MemberDO">
    update hsm_member
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="memberGuid != null">
        member_guid = #{memberGuid,jdbcType=VARCHAR},
      </if>
      <if test="storeGuid != null">
        store_guid = #{storeGuid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=TINYINT},
      </if>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=DATE},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
      <if test="expiryDate != null">
        expiry_date = #{expiryDate,jdbcType=DATE},
      </if>
      <if test="expiryType != null">
        expiry_type = #{expiryType,jdbcType=TINYINT},
      </if>
      <if test="expiryConfigKey != null">
        expiry_config_key = #{expiryConfigKey,jdbcType=TINYINT},
      </if>
      <if test="recentlyLoginDate != null">
        recently_login_date = #{recentlyLoginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="recentlyConsumeDate != null">
        recently_consume_date = #{recentlyConsumeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="memberGradeGuid != null">
        member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR},
      </if>
      <if test="totalConsumeNum != null">
        total_consume_num = #{totalConsumeNum,jdbcType=INTEGER},
      </if>
      <if test="totalPrepaidNum != null">
        total_prepaid_num = #{totalPrepaidNum,jdbcType=INTEGER},
      </if>
      <if test="totalIntegral != null">
        total_integral = #{totalIntegral,jdbcType=INTEGER},
      </if>
      <if test="residualIntegral != null">
        residual_integral = #{residualIntegral,jdbcType=INTEGER},
      </if>
      <if test="totalConsumeFee != null">
        total_consume_fee = #{totalConsumeFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPayFee != null">
        total_pay_fee = #{totalPayFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrepaidFee != null">
        total_prepaid_fee = #{totalPrepaidFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPresentFee != null">
        total_present_fee = #{totalPresentFee,jdbcType=DECIMAL},
      </if>
      <if test="balance != null">
        balance = #{balance,jdbcType=DECIMAL},
      </if>
      <if test="staffGuid != null">
        staff_guid = #{staffGuid,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
    </set>
    where member_guid = #{memberGuid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.holderzone.saas.store.member.domain.MemberDO">
    update hsm_member
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      member_guid = #{memberGuid,jdbcType=VARCHAR},
      store_guid = #{storeGuid,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=TINYINT},
      account_number = #{accountNumber,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR},
      birthday = #{birthday,jdbcType=DATE},
      start_date = #{startDate,jdbcType=DATE},
      expiry_date = #{expiryDate,jdbcType=DATE},
      expiry_type = #{expiryType,jdbcType=TINYINT},
      expiry_config_key = #{expiryConfigKey,jdbcType=TINYINT},
      recently_login_date = #{recentlyLoginDate,jdbcType=TIMESTAMP},
      recently_consume_date = #{recentlyConsumeDate,jdbcType=TIMESTAMP},
      member_grade_guid = #{memberGradeGuid,jdbcType=VARCHAR},
      total_consume_num = #{totalConsumeNum,jdbcType=INTEGER},
      total_prepaid_num = #{totalPrepaidNum,jdbcType=INTEGER},
      total_integral = #{totalIntegral,jdbcType=INTEGER},
      residual_integral = #{residualIntegral,jdbcType=INTEGER},
      total_consume_fee = #{totalConsumeFee,jdbcType=DECIMAL},
      total_pay_fee = #{totalPayFee,jdbcType=DECIMAL},
      total_prepaid_fee = #{totalPrepaidFee,jdbcType=DECIMAL},
      total_present_fee = #{totalPresentFee,jdbcType=DECIMAL},
      balance = #{balance,jdbcType=DECIMAL},
      staff_guid = #{staffGuid,jdbcType=VARCHAR},
      staff_name = #{staffName,jdbcType=VARCHAR}
    where member_guid = #{memberGuid,jdbcType=VARCHAR}
  </update>


  <select id="selectMemberList"  resultMap="MemberGradeList">
    select
    t1.id,
    t1.name,
    t1.phone,
    t1.sex,
    t1.birthday,
    t1.gmt_create,
    t1.member_guid,
    t1.total_prepaid_fee,
    t1.total_consume_fee,
    t1.total_consume_num,
    t1.total_prepaid_num,
    t1.member_grade_guid as membergradeguid,
    t2.name as membergradedesc
    from  hsm_member t1 LEFT  JOIN  hsm_member_grade  t2 On t1.member_grade_guid=t2.member_grade_guid



    <where>

      <if test="memberListReqDTO.searchType==1 and memberListReqDTO.searchMainValue!=null">
        and t1.phone like concat(concat('%',#{memberListReqDTO.searchMainValue}),'%')

      </if>

      <if test="memberListReqDTO.searchType==2 and memberListReqDTO.searchMainValue!=null and memberListReqDTO.searchMainValue!=''">
        and t1.name like concat(concat('%',#{memberListReqDTO.searchMainValue}),'%')

      </if>

      <if test="memberListReqDTO.sex!=null and memberListReqDTO.sex!=-1">
        and t1.sex=#{memberListReqDTO.sex}

      </if>
      <if test="memberListReqDTO.birthdayBegin!=null">
        and t1.birthday  &gt;=#{memberListReqDTO.birthdayBegin}

      </if>
      <if test="memberListReqDTO.birthdayEnd!=null">
        and t1.birthday &lt;=#{memberListReqDTO.birthdayEnd}

      </if>
      <if test="memberListReqDTO.gmtCreateBegin!=null">
        and t1.gmt_create &gt;=#{memberListReqDTO.gmtCreateBegin}

      </if>
      <if test="memberListReqDTO.gmtCreateEnd!=null">
        and t1.gmt_create &lt;=#{memberListReqDTO.gmtCreateEnd}

      </if>



      <if test="memberListReqDTO.totalConsumeNumBegin!=null">
        and t1.total_consume_num &gt;=#{memberListReqDTO.totalConsumeNumBegin}

      </if>
      <if test="memberListReqDTO.totalConsumeNumEnd!=null">
        and t1.total_consume_num &lt;=#{memberListReqDTO.totalConsumeNumEnd}

      </if>

      <if test="memberListReqDTO.totalPrepaidNumBegin!=null">
        and t1.total_consume_num &gt;=#{memberListReqDTO.totalPrepaidNumBegin}

      </if>
      <if test="memberListReqDTO.totalPrepaidNumEnd!=null">
        and t1.total_consume_num &lt;=#{memberListReqDTO.totalPrepaidNumEnd}

      </if>

      <if test="memberListReqDTO.memberGradeGuid!=null and memberListReqDTO.memberGradeGuid!='-1'">
        and t1.member_grade_guid=#{memberListReqDTO.memberGradeGuid}

      </if>


      <if test="memberListReqDTO.totalConsumeFeeBegin!=null">
        and t1.total_prepaid_fee &gt;=#{memberListReqDTO.totalConsumeFeeBegin}

      </if>
      <if test="memberListReqDTO.totalConsumeFeeEnd!=null">
        and t1.total_prepaid_fee &lt;=#{memberListReqDTO.totalConsumeFeeEnd}

      </if>
      <if test="memberListReqDTO.totalPrepaidFeeBegin!=null">
        and t1.total_consume_fee &gt;=#{memberListReqDTO.totalPrepaidFeeBegin}

      </if>
      <if test="memberListReqDTO.totalPrepaidFeeEnd!=null">
        and t1.total_consume_fee &lt;=#{memberListReqDTO.totalPrepaidFeeEnd}

      </if>

    </where>
      order by t1.gmt_create desc
  </select>


  <select id="AllMemberList" resultMap="MemberGradeList">
    select
    t1.id,
    t1.name,
    t1.phone,
    t1.sex,
    t1.birthday,
    t1.gmt_create,
    t1.member_guid,
    t1.total_prepaid_fee,
    t1.total_consume_fee,
    t1.total_consume_num,
    t1.total_prepaid_num,
    t1.member_grade_guid as membergradeguid,
    t2.name as membergradedesc
    from  hsm_member t1 LEFT  JOIN  hsm_member_grade  t2 On t1.member_grade_guid=t2.member_grade_guid



    <where>

      <if test="memberListReqDTO.searchType==1 and memberListReqDTO.searchMainValue!=null">
        and t1.phone like concat(concat('%',#{memberListReqDTO.searchMainValue}),'%')

      </if>

      <if test="memberListReqDTO.searchType==2 and memberListReqDTO.searchMainValue!=null and memberListReqDTO.searchMainValue!=''">
        and t1.name like concat(concat('%',#{memberListReqDTO.searchMainValue}),'%')

      </if>

      <if test="memberListReqDTO.sex!=null and memberListReqDTO.sex!=-1">
        and t1.sex=#{memberListReqDTO.sex}

      </if>
      <if test="memberListReqDTO.birthdayBegin!=null">
        and t1.birthday  &gt;=#{memberListReqDTO.birthdayBegin}

      </if>
      <if test="memberListReqDTO.birthdayEnd!=null">
        and t1.birthday &lt;=#{memberListReqDTO.birthdayEnd}

      </if>
      <if test="memberListReqDTO.gmtCreateBegin!=null">
        and t1.gmt_create &gt;=#{memberListReqDTO.gmtCreateBegin}

      </if>
      <if test="memberListReqDTO.gmtCreateEnd!=null">
        and t1.gmt_create &lt;=#{memberListReqDTO.gmtCreateEnd}

      </if>



      <if test="memberListReqDTO.totalConsumeNumBegin!=null">
        and t1.total_consume_num &gt;=#{memberListReqDTO.totalConsumeNumBegin}

      </if>
      <if test="memberListReqDTO.totalConsumeNumEnd!=null">
        and t1.total_consume_num &lt;=#{memberListReqDTO.totalConsumeNumEnd}

      </if>

      <if test="memberListReqDTO.totalPrepaidNumBegin!=null">
        and t1.total_consume_num &gt;=#{memberListReqDTO.totalPrepaidNumBegin}

      </if>
      <if test="memberListReqDTO.totalPrepaidNumEnd!=null">
        and t1.total_consume_num &lt;=#{memberListReqDTO.totalPrepaidNumEnd}

      </if>

      <if test="memberListReqDTO.memberGradeGuid!=null and memberListReqDTO.memberGradeGuid!='-1'">
        and t1.member_grade_guid=#{memberListReqDTO.memberGradeGuid}

      </if>


      <if test="memberListReqDTO.totalConsumeFeeBegin!=null">
        and t1.total_prepaid_fee &gt;=#{memberListReqDTO.totalConsumeFeeBegin}

      </if>
      <if test="memberListReqDTO.totalConsumeFeeEnd!=null">
        and t1.total_prepaid_fee &lt;=#{memberListReqDTO.ttotalConsumeFeeEnd}

      </if>
      <if test="memberListReqDTO.totalPrepaidFeeBegin!=null">
        and t1.total_consume_fee &gt;=#{memberListReqDTO.totalPrepaidFeeBegin}

      </if>
      <if test="memberListReqDTO.totalPrepaidFeeEnd!=null">
        and t1.total_consume_fee &lt;=#{memberListReqDTO.totalPrepaidFeeEnd}

      </if>

    </where>
    order by t1.gmt_create desc
  </select>
  <resultMap id="MemberGradeList" type="com.holderzone.saas.store.member.domain.MemberGradeMeberReadDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone"/>

    <result column="sex" jdbcType="TINYINT" property="sex" />
    <result column="total_prepaid_fee" jdbcType="DECIMAL" property="totalPrepaidFee" />
    <result column="total_consume_fee" jdbcType="DECIMAL" property="totalConsumeFee" />
    <result column="total_consume_num" jdbcType="INTEGER" property="totalConsumeNum" />
    <result column="total_prepaid_num" jdbcType="INTEGER" property="totalPrepaidNum" />
    <result column="member_guid" jdbcType="VARCHAR" property="memberGuid"/>
    <result column="birthday" jdbcType="DATE" property="birthday"/>
    <result column="gmt_create" jdbcType="DATE" property="registerTime"/>

    <result column="membergradeguid" jdbcType="VARCHAR" property="memberGradeGuid"/>
    <result column="membergradedesc" jdbcType="VARCHAR" property="memberGradeDesc"/>

  </resultMap>







  <resultMap id="MemberDetail" type="com.holderzone.saas.store.member.domain.MemberDetailReadDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="member_guid" jdbcType="VARCHAR" property="memberGuid"/>
    <result column="gmt_create" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="sex" jdbcType="TINYINT" property="sex" />
    <result column="birthday" jdbcType="DATE" property="birthday" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="expiry_date" jdbcType="DATE" property="expiryDate" />
    <result column="membergradeguid" jdbcType="VARCHAR" property="memberGradeGuid"/>
    <result column="membergradedesc" jdbcType="VARCHAR" property="memberGradeDesc"/>
    <result column="total_consume_num" jdbcType="INTEGER" property="totalConsumeNum" />
    <result column="total_integral" jdbcType="INTEGER" property="totalIntegral" />
    <result column="residual_integral" jdbcType="INTEGER" property="residualIntegral" />
    <result column="total_consume_fee" jdbcType="DECIMAL" property="totalConsumeFee" />
    <result column="total_pay_fee" jdbcType="DECIMAL" property="totalPayFee" />
    <result column="total_prepaid_fee" jdbcType="DECIMAL" property="totalPrepaidFee" />
    <result column="total_present_fee" jdbcType="DECIMAL" property="totalPresentFee" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
  </resultMap>

  <select id="getMemberDetailByGuid"  resultMap="MemberDetail">
  select
  t1.member_guid,
  t1.id,
  t1.name,
  t1.phone,
  t1.sex,
  t1.birthday,
  t1.gmt_create,
  t1.start_date,
  t1.expiry_date,
  t1.total_integral,
  t1.residual_integral,
  t1.total_prepaid_fee,
  t1.total_consume_fee,
  t1.total_pay_fee,
  t1.total_present_fee,
  t1.total_consume_num,

  t1.balance,

  t1.member_grade_guid as membergradeguid,
  t2.name as membergradedesc

  from  hsm_member t1 LEFT  JOIN  hsm_member_grade  t2
  On t1.member_grade_guid=t2.member_grade_guid where t1.member_guid=#{memberGuid};
  </select>

  <select id="getMemberByPhoneOrCardNum"  resultMap="MemberCardsResultMap">
    select
    t1.id,
    t1.gmt_create,
    t1.gmt_modified,
    t1.is_delete,
    t1.member_guid,
    t1.store_guid,
    t1.`name`,
    t1.phone,
    t1.sex,
    t1.account_number,
    t1.`password`,
    t1.birthday,
    t1.start_date,
    t1.expiry_date,
    t1.expiry_type,
    t1.expiry_config_key,
    t1.recently_login_date,
    t1.recently_consume_date,
    t1.member_grade_guid,
    t1.total_consume_num,
    t1.total_prepaid_num,
    t1.total_integral,
    t1.residual_integral,
    t1.total_consume_fee,
    t1.total_pay_fee,
    t1.total_prepaid_fee,
    t1.total_present_fee,
    t1.balance,
    t1.staff_guid,
    t1.staff_name,
    t2.num,
    t2.type
    from hsm_member t1
    LEFT JOIN
    hsm_member_card t2
    ON  t1.member_guid=t2.member_guid
    where (t1.phone = #{phone} OR t2.num=#{phone}) and t1.is_delete=0
    and t2.is_delete=0

  </select>

  <select id="selectMemberByMemberGuid" resultType="java.lang.Integer">
    select count(*) from hsm_member where member_grade_guid=#{memberGradeGuid}

  </select>

  <resultMap extends="BaseResultMap" id="MemberCardsResultMap" type="com.holderzone.saas.store.member.domain.MemberCardReadDO">

    <collection property="memberCardDOS" ofType="com.holderzone.saas.store.member.domain.MemberCardDO">
      <result column="num" property="num" jdbcType="VARCHAR"></result>
      <result column="type"  property="type" jdbcType="TINYINT"></result>
    </collection>
  </resultMap>
</mapper>