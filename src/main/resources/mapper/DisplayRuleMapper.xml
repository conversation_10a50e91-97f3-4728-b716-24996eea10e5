<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.saas.store.kds.mapper.DisplayRuleMapper">

    <resultMap type="com.holderzone.saas.store.kds.entity.domain.DisplayRuleDO" id="DisplayRuleMap">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="isDelete" column="is_delete"/>
        <result property="displayState" column="display_state"/>
        <result property="delayTime" column="delay_time"/>
        <result property="batch" column="batch"/>
        <result property="effectiveState" column="effective_state"/>
        <result property="effectiveTime" column="effective_time"/>
        <result property="isAllStore" column="is_all_store"/>
        <result property="ruleType" column="rule_type"/>
    </resultMap>


</mapper>