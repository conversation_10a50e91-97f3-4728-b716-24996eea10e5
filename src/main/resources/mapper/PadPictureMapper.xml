<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.PadPictureMapper">

    <select id="queryPadPicture" resultType="com.holderzone.saas.store.dto.item.resp.PadPictureRespDTO">
        SELECT DISTINCT
        hi.guid AS itemGuid,
        hp.guid,
        hi.NAME AS itemName,
        hp.small_picture,
        hi.picture_url as itemSmallPicture,
        hp.big_picture,
        hp.vertical_picture,
        hp.detail_picture,
        ht.menu_classify_picture_type,
        hi.sort
        FROM
        hsi_type ht
        INNER JOIN hsi_item hi ON ht.guid = hi.type_guid
        LEFT JOIN hsi_item_pad_picture hp ON hi.guid = hp.item_guid
        AND ht.store_guid = hp.store_guid
        WHERE
        ht.store_guid = #{dto.storeGuid}
        <if test="dto.typeGuidList != null and dto.typeGuidList.size() > 0">
            AND hi.type_guid IN
            <foreach collection="dto.typeGuidList" item="typeGuid" open="(" separator="," close=")">
                #{typeGuid}
            </foreach>
        </if>
        <if test="dto.keywords != null and dto.keywords !='' ">
            and hi.name LIKE CONCAT ('%',#{dto.keywords},'%')
        </if>
        AND hi.is_delete = 0
        and ht.is_delete = 0
        ORDER BY hi.sort ASC
    </select>


    <select id="queryPricePlanPadPicture" resultType="com.holderzone.saas.store.dto.item.resp.PadPictureRespDTO">
        SELECT DISTINCT
        hppi.item_guid AS itemGuid,
        hppi.plan_item_name AS itemName,
        hipp.small_picture as smallPicture,
        hppi.picture_url as planSmallPicture,
        hipp.big_picture,
        hipp.vertical_picture,
        hipp.detail_picture,
        hipp.guid,
        ht.menu_classify_picture_type,
        hppi.sort
        FROM
        hsi_price_plan_item hppi
        LEFT JOIN hsi_item_pad_picture hipp ON hppi.item_guid = hipp.item_guid
        AND hppi.plan_guid = hipp.plan_guid
        LEFT JOIN hsi_type ht ON ht.guid = hppi.type_guid
        WHERE
        hppi.plan_guid = #{dto.planGuid}
        <if test="dto.typeGuidList != null and dto.typeGuidList.size() > 0">
            AND hppi.type_guid IN
            <foreach collection="dto.typeGuidList" item="typeGuid" open="(" separator="," close=")">
                #{typeGuid}
            </foreach>
        </if>
        <if test="dto.keywords != null and dto.keywords !='' ">
            and hppi.plan_item_name LIKE CONCAT ('%',#{dto.keywords},'%')
        </if>
        AND hppi.is_delete = 0
        ORDER BY hppi.sort ASC
    </select>

</mapper>
