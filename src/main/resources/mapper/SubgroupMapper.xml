<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.SubgroupMapper">
    <delete id="deleteByItemGuidAndStoreGuid">
        DELETE FROM hsi_subgroup WHERE item_guid = #{itemGuid} AND store_guid = #{storeGuid}
    </delete>

    <select id="getGuidListByItemGuidAndStoreGuid" resultType="java.lang.String">
        SELECT guid FROM hsi_subgroup WHERE item_guid = #{itemGuid} AND store_guid = #{storeGuid}
    </select>

    <select id="getGuidListByStoreGuidAndBrandGuid" resultType="java.lang.String">
        SELECT guid FROM hsi_subgroup WHERE  store_guid = #{storeGuid} AND brand_guid = #{brandGuid}
    </select>

    <select id="listParentItemGuidBySubGroupSku" resultType="java.lang.String">
        SELECT
            DISTINCT
            hs.guid
        FROM
            hsi_r_sku_subgroup hrsub
            JOIN hsi_subgroup hsub ON hrsub.subgroup_guid = hsub.guid
            JOIN hsi_item hi ON hsub.item_guid = hi.guid AND hi.item_type = 1
            JOIN hsi_sku hs ON hi.guid =hs.item_guid

        WHERE hrsub.sku_guid IN
            <foreach  collection="skuGuidList" item="skuGuid" open="("  separator=","  close=")">
                #{skuGuid}
            </foreach>
        AND hrsub.is_delete =0
        and hs.is_delete = 0
        and hi.is_delete = 0
    </select>

</mapper>
