<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.TradeChangeMapper">


    <select id="count" resultType="java.lang.Integer">
        <include refid="WITH_SQL"/>
        SELECT
            count(1)
        FROM (
            SELECT
                oic.order_guid,
                oic.change_batch_number
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order_item_changes" oic
                JOIN change_order co on co.order_guid = oic.order_guid::BIGINT
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi on oi.guid = oic.order_item_guid::BIGINT
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oip on oi.parent_item_guid = oip.guid
                <where>
                    oic.is_delete = 0 and oic.original_order_item_guid is not null
                    <if test="query.itemName != null and query.itemName != ''">
                        and oip.item_name like concat('%', #{query.itemName}, '%')
                    </if>
                    <if test="query.startTime != null and query.endTime != null">
                        and oic.business_day BETWEEN #{query.startTime}::TIMESTAMP AND #{query.endTime}::TIMESTAMP
                    </if>
                </where>
            group by oic.order_guid, oic.change_batch_number
        ) temp
    </select>


    <select id="pageInfo" resultType="com.holderzone.saas.store.dto.report.resp.ChangeDetailDTO">
        <include refid="WITH_SQL"/>
        SELECT
            max(hb.name) as "brand_name",
            max(organization.name) as "store_name",
            max(co.order_no) as "order_no",
            max(oic.cancel_flag) as "cancel_flag",
            max(oic.change_node) as "change_node",
            max(oic.gmt_create) as "change_time",
            max(oip.item_name) as "subgroup_item_name",
            case
                when max(oicp.cancel_flag) = 0
                then max(CASE
                        WHEN LENGTH(oicp.sku_name) > 0
                        THEN concat ( oicp.item_name, '（', oicp.sku_name, '）' )
                        ELSE oicp.item_name
                        END)
                else
                    string_agg (
                        CASE
                        WHEN LENGTH ( oicp.sku_name ) > 0
                        THEN concat ( oicp.item_name, '（', oicp.sku_name, '）' ) ELSE oicp.item_name
                        END,
                    '\n'
                    ) end AS "original_item_name",
            case
                when max(oicp.cancel_flag) = 0
                then max(
                        concat(
                            '￥',
                            rtrim(
                                rtrim(
                                    ROUND(
                                        oicp.price + oicp.attr_total + FLOOR(oicp.add_price/oicp.package_default_count * 100)/100,
                                    2)::text,
                                '0'),
                            '.')
                        )
                    )
                else
                string_agg(
                    concat(
                        '￥',
                        rtrim(
                            rtrim(
                                ROUND(
                                    oicp.price + oicp.attr_total + FLOOR(oicp.add_price/oicp.package_default_count * 100)/100,
                                2)::text,
                            '0'),
                        '.')
                    ),
                '\n') end as "original_item_price",
            case
                when max(oicp.cancel_flag) = 0
                then max(
                        concat(
                        '',
                            rtrim(
                                rtrim(
                                    ROUND(
                                        oicp.current_count * oicp.package_default_count,
                                    3)::text,
                                '0'),
                            '.')
                        )
                    )
                else
                string_agg(
                    concat(
                        '',
                        rtrim(
                            rtrim(
                                ROUND(
                                    oicp.current_count * oicp.package_default_count,
                                3)::text,
                            '0'),
                        '.')
                    ),
                '\n') end as "original_item_count",
            case
                when max(oicp.cancel_flag) = 0
                then ROUND(
                            max(
                                ( oicp.price + oicp.attr_total )
                                * oicp.current_count * oicp.package_default_count + (oicp.add_price * oicp.current_count)
                            ),
                         2)
                else
                ROUND(
                    sum(
                        ( oicp.price + oicp.attr_total )
                        * oicp.current_count * oicp.package_default_count + (oicp.add_price * oicp.current_count)
                    ),
                2) end as "original_item_total_price",
            case
                when max(oicp.cancel_flag) = 1
                then max(CASE
                    WHEN LENGTH(oic.sku_name) > 0
                    THEN concat ( oic.item_name, '（', oic.sku_name, '）' )
                    ELSE oic.item_name
                END)
            else
            string_agg (
                CASE
                WHEN LENGTH ( oic.sku_name ) > 0
                THEN concat ( oic.item_name, '（', oic.sku_name, '）' ) ELSE oic.item_name
                END,
            '\n') end AS "change_item_name",
            case
                when max(oicp.cancel_flag) = 1
                then max(
                    concat(
                        '￥',
                        rtrim(
                            rtrim(
                                ROUND(
                                    oic.price + oic.attr_total + FLOOR(oic.add_price/oic.package_default_count * 100)/100,
                                2)::text,
                            '0'),
                        '.')
                    )
                )
            else
            string_agg(
                concat(
                    '￥',
                    rtrim(
                        rtrim(
                            ROUND(
                                oic.price + oic.attr_total + FLOOR(oic.add_price/oic.package_default_count * 100)/100,
                            2)::text,
                        '0'),
                    '.')
                ),
            '\n') end as "change_item_price",
            case
                when max(oicp.cancel_flag) = 1
                then max(
                    concat(
                        '',
                        rtrim(
                            rtrim(
                                ROUND(
                                    oic.current_count * oic.package_default_count,
                                3)::text,
                            '0'),
                        '.')
                        )
                    )
            else
            string_agg(
                concat(
                    '',
                    rtrim(
                        rtrim(
                            ROUND(
                                oic.current_count * oic.package_default_count,
                            3)::text,
                        '0'),
                    '.')
                ),
            '\n') end as "change_item_count",
            case
                when max(oicp.cancel_flag) = 1
                then ROUND(
                        max(
                            ( oic.price + oic.attr_total ) * oic.current_count * oic.package_default_count
                            + (oic.add_price * oic.current_count)
                        ),
                    2)
            else
                ROUND(
                    sum(
                        ( oic.price + oic.attr_total ) * oic.current_count * oic.package_default_count
                        + (oic.add_price * oic.current_count)
                    ),
                2) end as "change_item_total_price",
            max(oic.create_staff_name) as "create_staff_name"
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_order_item_changes" oic
        JOIN change_order co on co.order_guid = oic.order_guid::BIGINT
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oi on oi.guid = oic.order_item_guid::BIGINT
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" oip on oi.parent_item_guid = oip.guid
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item_changes" oicp on oic.original_order_item_guid = oicp.guid::BIGINT and oicp.original_order_item_guid is null
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" organization on organization.guid = co.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on organization.guid = ro.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_brand" hb on ro.brand_guid = hb.guid
        <where>
            oic.is_delete = 0 and oic.original_order_item_guid is not null
            <if test="query.itemName != null and query.itemName != ''">
                and oip.item_name like concat('%', #{query.itemName}, '%')
            </if>
            <if test="query.startTime != null and query.endTime != null">
                and oic.business_day BETWEEN #{query.startTime}::TIMESTAMP AND #{query.endTime}::TIMESTAMP
            </if>
        </where>
        group by oic.order_guid, oic.change_batch_number
        order by max(oic.gmt_create) desc, max(oic.id) desc
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>


    <sql id="WITH_SQL">
        WITH change_order AS (
            SELECT
                o.guid as "order_guid",
                o.store_guid,
                o.order_no
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order" o
            <where>
                o.is_delete = 0 and o.recovery_type in (1,3)
                <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                    and o.store_guid IN
                    <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                        #{storeGuid}
                    </foreach>
                </if>
                <if test="query.cateringType != null">
                    and o.trade_mode = #{query.cateringType}
                </if>
            </where>
        )
    </sql>
</mapper>
