<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.staff.mapper.RoleMapper">
    <select id="countByRoleQueryDTO" parameterType="com.holderzone.saas.store.dto.user.RoleQueryDTO" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM hss_role
        <where>
            `is_deleted` = 0
            <if test="roleName != null and roleName != ''">
                AND `name` LIKE CONCAT ('%',#{roleName},'%')
            </if>
        </where>
    </select>

    <select id="selectListByRoleQueryDTO" resultMap="baseMap" parameterType="com.holderzone.saas.store.dto.user.RoleQueryDTO">
        SELECT
        <include refid="baseColumn"/>
        FROM hss_role
        <where>
            `is_deleted` = 0
            <if test="roleName != null and roleName != ''">
                AND `name` LIKE CONCAT ('%',#{roleName},'%')
            </if>
        </where>
        ORDER BY gmt_create DESC
        LIMIT #{offsetIndex}, #{pageSize}
    </select>

    <sql id="baseColumn">
       `guid`,
       `name`,
       `is_enable`,
       `is_deleted`,
       `create_staff_guid`,
       `modified_staff_guid`,
       `gmt_create`,
       `gmt_modified`
    </sql>

    <resultMap id="baseMap" type="com.holderzone.saas.store.dto.user.RoleDTO">
        <result property="guid" column="guid"/>
        <result property="name" column="name"/>
        <result property="isEnable" column="is_enable" jdbcType="TINYINT"/>
        <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="DATE"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="DATE"/>
        <collection property="enableLoginSourceList" ofType="java.lang.String" select="getEnableLoginSource" column="guid"/>
    </resultMap>

    <!-- 一对多查询，根据角色guid查询角色有权限的终端 -->
    <select id="getEnableLoginSource" resultType="java.lang.String">
        SELECT
        DISTINCT `terminal_name`
        FROM hss_role_source
        WHERE role_guid = #{guid}
    </select>
</mapper>
