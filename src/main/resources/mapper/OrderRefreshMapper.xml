<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.OrderRefreshMapper">

    <update id="refreshModifyTime">
        update hst_order set gmt_modified=(gmt_modified+INTERVAL 1 SECOND),is_updated_es=1  where guid=#{orderGuid};
        update hst_order_item set gmt_modified=(gmt_modified+INTERVAL 1 SECOND)  where order_guid=#{orderGuid};
        update hst_append_fee set gmt_modified=(gmt_modified+INTERVAL 1 SECOND)  where order_guid=#{orderGuid} ;
        update hst_discount set gmt_modified=(gmt_modified+INTERVAL 1 SECOND)  where order_guid=#{orderGuid} ;
        update hst_free_return_item set gmt_modified=(gmt_modified+INTERVAL 1 SECOND)  where order_guid=#{orderGuid} ;
        update hst_item_attr set gmt_modified=(gmt_modified+INTERVAL 1 SECOND)  where order_guid=#{orderGuid} ;
        update hst_transaction_record set gmt_modified=(gmt_modified+INTERVAL 1 SECOND)  where order_guid=#{orderGuid} ;
        update hst_order_item_record set gmt_modified=(gmt_modified+INTERVAL 1 SECOND)  where order_guid=#{orderGuid};
    </update>



    <update id="refreshModifyTimeByDay">
        update hst_order set gmt_modified=(gmt_modified+INTERVAL #{second} SECOND)  where gmt_create>=#{gmtCreateTimeLimit};
        update hst_order_item set gmt_modified=(gmt_modified+INTERVAL #{second} SECOND)  where gmt_create>=#{gmtCreateTimeLimit};
        update hst_append_fee set gmt_modified=(gmt_modified+INTERVAL #{second} SECOND)  where gmt_create>=#{gmtCreateTimeLimit} ;
        update hst_discount set gmt_modified=(gmt_modified+INTERVAL #{second} SECOND)  where gmt_create>=#{gmtCreateTimeLimit} ;
        update hst_free_return_item set gmt_modified=(gmt_modified+INTERVAL #{second} SECOND)  where gmt_create>=#{gmtCreateTimeLimit} ;
        update hst_item_attr set gmt_modified=(gmt_modified+INTERVAL #{second} SECOND)  where gmt_create>=#{gmtCreateTimeLimit} ;
        update hst_transaction_record set gmt_modified=(gmt_modified+INTERVAL #{second} SECOND)  where gmt_create>=#{gmtCreateTimeLimit} ;
        update hst_order_item_record set gmt_modified=(gmt_modified+INTERVAL #{second} SECOND)  where gmt_create>=#{gmtCreateTimeLimit} ;
    </update>



    <select id="getNeedUpdateOrderGuid" resultType="String">
        select guid from hst_order where state in ( 4,5,6)
        and is_updated_es = 0 order by guid desc limit 1
    </select>
</mapper>
