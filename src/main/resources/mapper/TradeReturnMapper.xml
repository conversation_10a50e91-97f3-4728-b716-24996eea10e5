<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.TradeReturnMapper">


    <select id="statistics" resultType="com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO">
        SELECT
            COALESCE(sum(t.return_quantity),0) as "total_quantity",
            COALESCE(sum(round(t.refund ,2)),0) as "total_money",
            COALESCE ( SUM ( t."actuallyRefundFee" ), 0 ) AS "actuallyRefundFee"
        FROM (
            <include refid="CANCEL_ORDER"/>
            UNION ALL
            <include refid="RETURN_ITEM"/>
        ) t
    </select>

    <select id="pageInfo" resultType="com.holderzone.saas.store.dto.report.resp.ReturnItemDTO">
        SELECT
            hb.guid as "brand_guid",
            hb.name as "brand_name",
            o.guid as "store_guid",
            o.name as "store_name",
            case when hs.name is not null and length(hs.name) > 0 then concat(hi.name, '(', hs.name, ')')
            else hi.name end as "goods_name",
            d.item_guid as "goods_guid",
            d.sku_guid,
            ht.name as "goods_categories",
            temp.return_quantity as "return_quantity",
            temp.refund as "refund"
        FROM (
            SELECT
                t.sku_guid,
                max(t.order_item_guid) as "max_order_item_guid",
                max(order_guid) as "max_order_guid",
                COALESCE(sum(t.return_count),0) as "return_quantity",
                COALESCE(sum(round(t.return_count * t.price,2)),0) as "refund"
            FROM (
                SELECT
                    d.guid as "order_item_guid",
                    a.guid as "order_guid",
                    d.sku_guid,
                    case when a.state = 6 then d.current_count + d.free_count + d.return_count else d.return_count + d.refund_count end as "return_count",
                    d.price
                FROM
                    "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d
                LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" a ON d.order_guid = a.guid and d.is_delete = '0'
                LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = a.store_guid
                LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
                LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_item" hi ON hi.guid = d.item_guid
                LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" ht ON hi.type_guid = ht.guid
                where d.is_delete = 0 and ( d.return_count > 0 or d.refund_count > 0 or a.state = 6 ) and d.parent_item_guid = 0 AND A.recovery_type IN ( 1, 3 )
                <include refid="querySQL" />
            ) t
            group by t.sku_guid
            order by max(t.order_item_guid) desc
            limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
        ) temp
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d on temp.max_order_item_guid = d.guid
        <if test="query.startTime != null">
            <![CDATA[ and d.business_day >= #{query.startTime} ]]>
        </if>
        <if test="query.endTime != null">
            <![CDATA[ and d.business_day <= #{query.endTime} ]]>
        </if>
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" a ON d.order_guid = a.guid and d.is_delete = '0'
        <if test="query.startTime != null">
            <![CDATA[ and a.business_day >= #{query.startTime} ]]>
        </if>
        <if test="query.endTime != null">
            <![CDATA[ and a.business_day <= #{query.endTime} ]]>
        </if>
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = a.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_brand" hb on ro.brand_guid = hb.guid and hb.is_deleted = '0'
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_item" hi ON hi.guid = d.item_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_sku" hs ON hs.guid = d.sku_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" ht ON hi.type_guid = ht.guid
    </select>


    <select id="count" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM (
            SELECT
                t.sku_guid
            FROM (
                SELECT
                d.guid as "order_item_guid",
                a.guid as "order_guid",
                d.sku_guid,
                case when a.state = 6 then d.current_count + d.free_count + d.return_count else d.return_count end as "return_count",
                d.price
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" a ON d.order_guid = a.guid and d.is_delete = '0'
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = a.store_guid
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
            LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_item" hi ON hi.guid = d.item_guid
            LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" ht ON hi.type_guid = ht.guid
             where d.is_delete = 0 and ( d.return_count > 0 or a.state = 6 ) and d.parent_item_guid = 0 AND A.recovery_type IN ( 1, 3 )
            <include refid="querySQL" />
            ) t
            group by t.sku_guid
        ) temp
    </select>

    <sql id="CANCEL_ORDER">
        SELECT
            o.guid AS "store_guid",
            o.NAME AS "store_name",
            CASE
            WHEN hs.name IS NOT NULL
            AND LENGTH ( hs.name ) > 0 THEN
            concat ( hi.name, '(', hs.name, ')' ) ELSE hi.name
            END AS "goods_name",
            d.item_guid AS "goods_guid",
            d.sku_guid,
            ht.name AS "goods_categories",
            d.current_count  AS "return_quantity",
            round( d.current_count * d.price ,2) AS "refund",
            A.cancel_time AS "return_time",
            A.gmt_create AS "order_time",
            A.order_no,
            A.guid,
            A.cancel_reason AS reason,
            A.cancel_staff_name AS "staff",
            CASE WHEN A.recovery_type = 3
            THEN '反结账' ELSE ( CASE WHEN A."state" = 4 THEN '已结账' ELSE (CASE WHEN ki.kitchen_state = 7 THEN '未结账' ELSE '未出餐' END ) END )
            END AS "operatorNode",
            CASE
            WHEN trade_mode = 0 THEN
            '正餐' ELSE'快餐'
            END AS "catering_type",
            A.dining_table_name AS "table",
            d.original_price AS "salePrice",
            CASE WHEN A.recovery_type = 3 THEN
            round( fd.discount_total_price * ( CASE WHEN fd.current_count = 0 THEN 0 ELSE (d.current_count / fd.current_count) END ),2)
            ELSE 0 END AS "actuallyRefundFee",
            COALESCE ( oe.recovery_auth_staff_name, '-' ) AS "authorityUser"
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" A ON d.order_guid = A.guid
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o ON o.guid = A.store_guid
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro ON o.guid = ro.store_guid
            LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_item" hi ON hi.guid = d.item_guid
            LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_sku" hs ON hs.guid = d.sku_guid
            LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" ht ON hi.type_guid = ht.guid
            LEFT JOIN "hsk_kds_${query.enterpriseGuid}_db"."hsk_kitchen_item" ki ON ki.order_item_guid = CAST ( d.guid AS VARCHAR )
                AND ki.item_state = 99
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_extends" oe ON oe.guid = A.guid
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" fd ON d.original_order_item_guid = fd.guid
        WHERE
            d.is_delete = 0
            AND d.current_count > 0
            AND A.STATE = 6
            AND d.parent_item_guid = 0
            AND A.recovery_type IN ( 1, 3 )
            <if test="query.itemName != null">
                and hi.name like concat('%',#{query.itemName},'%')
            </if>
            <include refid="querySQLByListDetailToCancel" />
    </sql>

    <sql id="RETURN_ITEM">
        SELECT
            o.guid AS "store_guid",
            o.NAME AS "store_name",
            CASE
            WHEN hs.name IS NOT NULL
            AND LENGTH ( hs.name ) > 0 THEN
            concat ( hi.name, '(', hs.name, ')' ) ELSE hi.name
            END AS "goods_name",
            d.item_guid AS "goods_guid",
            d.sku_guid,
            ht.name AS "goods_categories",
            CASE WHEN i."type" = 1
                THEN
                    i.COUNT + i.refund_count
                ELSE
                    i.refund_count
                END AS "return_quantity",
            round( (CASE WHEN i."type" = 1
                THEN
                    i.COUNT + i.refund_count
                ELSE
                    i.refund_count
                END) * i.price ,2) AS "refund",
            i.gmt_modified AS "return_time",
            A.gmt_create AS "order_time",
            A.order_no,
            A.guid,
            i.reason,
            i.staff_name AS "staff",
            CASE when i.recovery_type = 3
            THEN '反结账'
            ELSE ( CASE when i.order_state = 4
                THEN '已结账'
                ELSE ( CASE when i.is_out_dinner = 0
                    THEN '未出餐'
                    ELSE '未结账'
                    END )
                END )
            END AS "operatorNode",
            CASE
            WHEN trade_mode = 0 THEN
            '正餐' ELSE'快餐'
            END AS "catering_type",
<!--            CASE WHEN a.recovery_type = 3-->
<!--            THEN '反结账' ELSE ( CASE WHEN a."state" = 4 THEN '已结账' ELSE (CASE WHEN ki.kitchen_state = 7 THEN '未结账' ELSE '未出餐' END ) END )-->
<!--            END AS "operatorNode",-->
            a.dining_table_name AS "table",
            d.original_price AS "salePrice",
<!--            CASE WHEN A."state" = 4  THEN-->
<!--                ( CASE WHEN a."recovery_type" = 3-->
<!--                    THEN-->
<!--                        round( ( CASE WHEN i."type" = 1 THEN i.COUNT + i.refund_count ELSE i.refund_count END ) *-->
<!--                        ( CASE WHEN (d.current_count + d.return_count) = 0 THEN 0 ELSE d.discount_total_price / (d.current_count + d.return_count) END ), 2 )-->
<!--                    ELSE round( ( CASE WHEN i."type" = 1 THEN i.COUNT + i.refund_count ELSE i.refund_count END ) *-->
<!--                        ( CASE WHEN d.current_count = 0 THEN 0 ELSE d.discount_total_price / d.current_count END ), 2 )-->
<!--                END )-->
<!--            ELSE-->
<!--                (CASE WHEN a."recovery_type" = 3-->
<!--                    THEN-->
<!--                        round( ( CASE WHEN i."type" = 1 THEN i.COUNT + i.refund_count ELSE i.refund_count END ) *-->
<!--                            ( CASE WHEN fd.current_count = 0 THEN 0 ELSE fd.discount_total_price / fd.current_count END ), 2 )-->
<!--                    ELSE 0-->
<!--                END)-->
<!--            END AS "actuallyRefundFee",-->
            round( CASE WHEN ( A."state" = 4 AND A.recovery_type <![CDATA[ <> ]]> 3 )
                OR (A."state" = 6 AND A.recovery_type = 3)
                OR ( A."state" = 4 AND d.refund_count > 0 )
            THEN ( CASE WHEN d.coupon_code IS NULL AND fd.coupon_code IS NOT NULL
                THEN COALESCE(g.amount,0)
                WHEN d.coupon_code IS NOT NULL AND d.refund_count > 0
                THEN COALESCE(g.amount,0)
                ELSE round( (CASE WHEN i."type" = 1
                        THEN
                        i.COUNT + i.refund_count
                        ELSE
                        i.refund_count
                        END) * i.actually_refund_fee ,2) END )
            ELSE 0
            END , 2 ) AS "actuallyRefundFee",
            CASE WHEN A.recovery_type = 3 THEN COALESCE ( oe.recovery_auth_staff_name, '-' ) ELSE	COALESCE ( i.auth_staff_name, '-' ) END AS "authorityUser"
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_free_return_item" i
            JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" A ON i.order_guid = A.guid
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o ON o.guid = A.store_guid
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro ON o.guid = ro.store_guid
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d ON i.order_item_guid = d.guid
            LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_item" hi ON hi.guid = d.item_guid
            LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_sku" hs ON hs.guid = d.sku_guid
            LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" ht ON hi.type_guid = ht.guid
            LEFT JOIN "hsk_kds_${query.enterpriseGuid}_db"."hsk_kitchen_item" ki ON ki.order_item_guid = CAST ( d.guid AS VARCHAR )
                AND ki.item_state = 99
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_extends" oe ON oe.guid = a.guid
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" fd ON d.original_order_item_guid = fd.guid
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_groupon" g ON (fd.order_guid = g.order_guid AND fd.coupon_code = g.code)
                OR (d.order_guid = g.order_guid AND d.coupon_code = g.code )
        WHERE
            i.is_delete = 0
            AND ( i."type" = 1 OR i.refund_count > 0 )
            AND A.recovery_type IN ( 1, 3 )
            <if test="query.itemName != null">
                and hi.name like concat('%',#{query.itemName},'%')
            </if>
            <include refid="querySQLByListDetail" />
    </sql>

    <select id="listReturnDetail" resultType="com.holderzone.saas.store.dto.report.resp.ReturnDetailItemDTO">
        SELECT temp.* FROM
        (
            <include refid="CANCEL_ORDER"/>
            UNION ALL
            <include refid="RETURN_ITEM"/>
        ) temp

        order by temp.return_time desc
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <select id="countReturnDetail" resultType="java.lang.Integer">
        SELECT count(1) FROM
        (
            <include refid="CANCEL_ORDER"/>
            UNION ALL
            <include refid="RETURN_ITEM"/>
        ) temp
    </select>

    <sql id="querySQLByListDetailToCancel">
        <include refid="businessDayQuery"/>
        <if test="query.brandGuid != null and query.brandGuid != ''">
            and ro.brand_guid = #{query.brandGuid}
        </if>
        <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
            and a.store_guid IN
            <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
        <if test="query.goodsType != null">
            and d.item_type = #{query.goodsType}
        </if>
        <if test="query.goodsCategories != null and query.goodsCategories != ''">
            and ht.name = #{query.goodsCategories}
        </if>
        <if test="query.cateringType != null">
            and a.trade_mode = #{query.cateringType}
        </if>
        <if test="query.operatorNode != null">
            <choose>
                <when test="query.operatorNode == 1">
                    AND A.recovery_type = 3
                </when>
                <when test="query.operatorNode == 2">
                    AND 1 = 2
                </when>
                <when test="query.operatorNode == 3">
                    AND A.recovery_type <![CDATA[ <> ]]> 3 AND A."state" <![CDATA[ <> ]]> 4 AND ki.kitchen_state = 7
                </when>
                <when test="query.operatorNode == 4">
                    AND A.recovery_type <![CDATA[ <> ]]> 3 AND A."state" <![CDATA[ <> ]]> 4 AND ki.kitchen_state <![CDATA[ <> ]]> 7
                </when>
                <otherwise>
                    1 = 1
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="querySQLByListDetail">

        <include refid="businessDayQuery"/>
        <if test="query.brandGuid != null and query.brandGuid != ''">
            and ro.brand_guid = #{query.brandGuid}
        </if>
        <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
            and a.store_guid IN
            <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
        <if test="query.goodsType != null">
            and d.item_type = #{query.goodsType}
        </if>
        <if test="query.goodsCategories != null and query.goodsCategories != ''">
            and ht.name = #{query.goodsCategories}
        </if>
        <if test="query.cateringType != null">
            and a.trade_mode = #{query.cateringType}
        </if>
        <if test="query.operatorNode != null">
            <choose>
                <when test="query.operatorNode == 1">
                    AND i.recovery_type = 3
                </when>
                <when test="query.operatorNode == 2">
                    AND i.recovery_type <![CDATA[ <> ]]> 3 AND i."order_state" = 4
                </when>
                <when test="query.operatorNode == 3">
                    AND i.recovery_type <![CDATA[ <> ]]> 3 AND i."order_state" <![CDATA[ <> ]]> 4 AND i.is_out_dinner = 1
                </when>
                <when test="query.operatorNode == 4">
                    AND i.recovery_type <![CDATA[ <> ]]> 3 AND i."order_state" <![CDATA[ <> ]]> 4 AND i.is_out_dinner <![CDATA[ <> ]]> 1
                </when>
                <otherwise>
                    1 = 1
                </otherwise>
            </choose>
        </if>
    </sql>
    <sql id="businessDayQuery">
        <if test="query.startTime != null">
            <![CDATA[ and a.business_day >= #{query.startTime} ]]>
            <![CDATA[ and d.business_day >= #{query.startTime} ]]>
        </if>
        <if test="query.endTime != null">
            <![CDATA[ and a.business_day <= #{query.endTime} ]]>
            <![CDATA[ and d.business_day <= #{query.endTime} ]]>
        </if>
    </sql>
    <sql id="querySQL">
            <include refid="businessDayQuery"/>

            <if test="query.brandGuid != null and query.brandGuid != ''">
                and ro.brand_guid = #{query.brandGuid}
            </if>
            <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                and a.store_guid IN
                <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.goodsType != null">
                and d.item_type = #{query.goodsType}
            </if>
            <if test="query.goodsCategories != null and query.goodsCategories != ''">
                and ht.name = #{query.goodsCategories}
            </if>
            <if test="query.cateringType != null">
                and a.trade_mode = #{query.cateringType}
            </if>
            <if test="query.operatorNode != null">
                <choose>
                    <when test="query.operatorNode == 1">
                        AND a.recovery_type = 3
                    </when>
                    <when test="query.operatorNode == 2">
                        AND a.recovery_type <![CDATA[ <> ]]> 3 AND a."state" = 4
                    </when>
                    <when test="query.operatorNode == 3">
                        AND a.recovery_type <![CDATA[ <> ]]> 3 AND a."state" <![CDATA[ <> ]]> 4 AND ki.kitchen_state = 7
                    </when>
                    <when test="query.operatorNode == 4">
                        AND a.recovery_type <![CDATA[ <> ]]> 3 AND a."state" <![CDATA[ <> ]]> 4 AND ki.kitchen_state <![CDATA[ <> ]]> 7
                    </when>
                    <otherwise>
                        1 = 1
                    </otherwise>
                </choose>
            </if>

    </sql>

</mapper>
