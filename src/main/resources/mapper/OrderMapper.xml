<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.OrderMapper">

    <resultMap id="OrderPageMap" type="com.holderzone.saas.store.trade.entity.read.OrderReadDO">
        <id column="guid" jdbcType="VARCHAR" property="guid"/>
        <id column="dining_table_name" jdbcType="VARCHAR" property="diningTableName"/>
        <id column="gmt_create" property="gmtCreate"/>
        <id column="checkout_time" property="checkoutTime"/>
        <id column="order_fee" property="orderFee"/>
        <id column="actually_pay_fee" property="actuallyPayFee"/>
        <id column="store_name" property="storeName"/>
        <id column="order_no" property="orderNo"/>
        <id column="trade_mode" property="tradeMode"/>
        <collection property="orderWaiterDOList" javaType="java.util.ArrayList"
                    ofType="com.holderzone.saas.store.trade.entity.domain.OrderWaiterDO"
                    select="queryOrderWaiter" column="{orderGuid=guid}">
        </collection>
    </resultMap>

    <resultMap id="OrderPageDetailsMap" type="com.holderzone.saas.store.trade.entity.read.OrderReadDetailsDO">
        <id column="guid" jdbcType="VARCHAR" property="guid"/>
        <id column="dining_table_name" jdbcType="VARCHAR" property="diningTableName"/>
        <id column="gmt_create" property="gmtCreate"/>
        <id column="checkout_time" property="checkoutTime"/>
        <id column="order_fee" property="orderFee"/>
        <id column="store_name" property="storeName"/>
        <id column="order_no" property="orderNo"/>
        <id column="waiter_guid" property="waiterGuid"/>
        <id column="waiter_name" property="waiterName"/>
        <id column="waiter_type" property="waiterType"/>
        <id column="waiter_no" property="waiterNo"/>
    </resultMap>
    <select id="queryOrderWaiter" resultType="com.holderzone.saas.store.trade.entity.domain.OrderWaiterDO"
            parameterType="java.util.HashMap">
        select *
        from hst_order_waiter
        where order_guid = #{orderGuid}
    </select>
    <select id="handoverOrderCount" parameterType="com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO"
            resultType="int">
        select count(*)
        from
        hst_order
        <where>
            recovery_type in (1, 3)
            and state = 4
            and upper_state in (0,1,3)
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                and store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                and checkout_time &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                and checkout_time &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and checkout_staff_guid = #{dto.userGuid}
            </if>
        </where>
    </select>
    <update id="updateOrderFeeAndAppendFeeByGuid">
        update hst_order
        set order_fee=#{orderFee},
            append_fee=#{appendFee}
        where guid = #{guid}
    </update>
    <select id="selectByGuidOrMainOrderGuid"
            resultType="com.holderzone.saas.store.trade.entity.domain.OrderDO">
        SELECT t2.*
        FROM (SELECT guid, main_order_guid, upper_state FROM hst_order WHERE guid = #{guid} AND upper_state = 2) t1
                 LEFT JOIN hst_order t2 ON t1.main_order_guid = t2.main_order_guid
        UNION ALL
        SELECT t3.*
        FROM (SELECT guid, main_order_guid, upper_state FROM hst_order WHERE guid = #{guid} AND upper_state = 2) t1
                 LEFT JOIN hst_order t3 ON t3.guid = t1.main_order_guid
        UNION ALL
        SELECT *
        FROM hst_order
        WHERE (guid = #{guid} AND upper_state != 2)
           OR (main_order_guid = #{guid} AND upper_state = 2)
    </select>

    <select id="pageQueryOrder" resultMap="OrderPageMap"
            parameterType="com.holderzone.saas.store.trade.entity.query.OrderWaiterQuery">
        SELECT * FROM hst_order
        <where>
            ho.state = 4
            <if test="null!=orderWaiterQuery.storeGuid and orderWaiterQuery.storeGuid!=''">
                and store_guid = #{orderWaiterQuery.storeGuid}
            </if>
            <if test="null!=orderWaiterQuery.startDateTime">
                and gmt_create &gt;= #{orderWaiterQuery.startDateTime}
            </if>
            <if test="null!=orderWaiterQuery.endDateTime">
                and gmt_create &lt;= #{orderWaiterQuery.endDateTime}
            </if>
            ORDER BY
            gmt_create DESC
        </where>
    </select>


    <select id="pageQueryOrderWaiter" resultMap="OrderPageMap"
            parameterType="com.holderzone.saas.store.trade.entity.query.OrderWaiterQuery">
        SELECT
        ho.guid,ho.dining_table_name,ho.gmt_create,ho.trade_mode
        FROM
        hst_order ho
        RIGHT JOIN hst_order_waiter how ON ho.guid = how.order_guid
        <where>
            ho.state = 4
            <if test="null!=orderWaiterQuery.storeGuid and orderWaiterQuery.storeGuid!=''">
                and ho.store_guid = #{orderWaiterQuery.storeGuid}
            </if>
            <if test="null!=orderWaiterQuery.startDateTime">
                and ho.gmt_create &gt;= #{orderWaiterQuery.startDateTime}
            </if>
            <if test="null!=orderWaiterQuery.endDateTime">
                and ho.gmt_create &lt;= #{orderWaiterQuery.endDateTime}
            </if>
            GROUP BY
            ho.guid
            ORDER BY
            ho.gmt_create DESC
        </where>
    </select>

    <select id="getOrderStatisticsCombined"
            resultType="com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsCombinedRespDTO">
        SELECT
            SUM(CASE WHEN upper_state = 4 THEN 0 ELSE 1 END) AS orderCount,
            SUM(IF(state IN (4,7,12) , actually_pay_fee - refund_amount - IFNULL(excess_amount,0) + IFNULL(discount_fee,0) ,0)) AS aggregateAmount
        FROM
            (
                <choose>
                    <when test="(paymentTypeList != null and paymentTypeList.size() > 0) or (grouponTypeList != null and grouponTypeList.size() > 0)">
                        <if test="paymentTypeList != null and paymentTypeList.size() > 0">
                            SELECT
                                <include refid="orderStatisticsCombinedColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_discount d on d.order_guid = h.guid and d.is_delete = 0 and d.discount_type = 14
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_transaction_record it ON it.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsCombinedConditions"/>
                                AND h.state not in (5, 11)
                                AND (h.upper_state = 0 OR h.upper_state = 1 OR h.upper_state = 4 OR (h.upper_state = 3 AND h.state != 7))
                                AND it.state = 4
                                AND it.is_delete = 0
                                AND it.amount > 0
                                AND it.trade_type IN (1, 4)
                                AND it.payment_type_name IN
                                <foreach collection="paymentTypeList" item="paymentTypeName" open="(" separator="," close=")">
                                    #{paymentTypeName}
                                </foreach>
                            </where>
                            UNION
                            SELECT
                                <include refid="orderStatisticsCombinedColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_discount d on d.order_guid = h.guid and d.is_delete = 0 and d.discount_type = 14
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_transaction_record it ON it.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsCombinedConditions"/>
                                AND h.upper_state IN (0,1,3,4)
                                AND h.state not in (5, 11)
                                AND it.state = 4
                                AND it.is_delete = 0
                                AND it.amount > 0
                                AND it.trade_type IN (1, 4)
                                AND it.payment_type_name IN
                                <foreach collection="paymentTypeList" item="paymentTypeName" open="(" separator="," close=")">
                                    #{paymentTypeName}
                                </foreach>
                            </where>
                        </if>
                        <if test="(paymentTypeList != null and paymentTypeList.size() > 0) and (grouponTypeList != null and grouponTypeList.size() > 0)">
                            UNION
                        </if>
                        <if test="grouponTypeList != null and grouponTypeList.size() > 0">
                            SELECT
                                <include refid="orderStatisticsCombinedColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_discount d on d.order_guid = h.guid and d.is_delete = 0 and d.discount_type = 14
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_groupon ig ON ig.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsCombinedConditions"/>
                                AND (h.upper_state = 0 OR h.upper_state = 1 OR h.upper_state = 4 OR (h.upper_state = 3 AND h.state != 7))
                                AND ig.is_delete = 0
                                AND ig.refund_order_guid is null
                                AND ig.groupon_type IN
                                <foreach collection="grouponTypeList" item="grouponType" open="(" separator="," close=")">
                                    #{grouponType}
                                </foreach>
                            </where>
                            UNION

                            SELECT
                                <include refid="orderStatisticsCombinedColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_discount d on d.order_guid = h.guid and d.is_delete = 0 and d.discount_type = 14
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_groupon ig ON ig.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsCombinedConditions"/>
                                AND h.upper_state IN (0,1,3,4)
                                AND (h.state = 4 OR h.state = 12)
                                AND ig.is_delete = 0
                                AND ig.refund_order_guid is null
                                AND ig.groupon_type IN
                                <foreach collection="grouponTypeList" item="grouponType" open="(" separator="," close=")">
                                    #{grouponType}
                                </foreach>
                            </where>
                        </if>
                    </when>
                    <otherwise>
                        SELECT
                            <include refid="orderStatisticsCombinedColumns"/>
                        FROM
                            hst_order h
                        LEFT JOIN hst_discount d on d.order_guid = h.guid and d.is_delete = 0 and d.discount_type = 14
                        LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                        <where>
                            <include refid="orderStatisticsCombinedConditions"/>
                            AND h.upper_state IN (0,1,3,4)
                            AND h.state not in (5, 11)
                        </where>
                    </otherwise>
                </choose>
            ) t
    </select>

    <select id="listOrderBusinessData" resultType="com.holderzone.saas.store.dto.journaling.resp.BusinessDataRespDTO">
        SELECT
            SUM(case o.upper_state when 2 then 0 else o.order_fee - IFNULL(temp.refundDeductionAmount,0)- o.refund_amount end) as businessFee,
            SUM(case o.upper_state when 2 then 0 else o.actually_pay_fee +IFNULL(temp.buyPrice,0)
            - o.refund_amount + IFNULL(tr.amount,0) - IFNULL(o.excess_amount,0) end) as actuallyPayFee,
            SUM(case o.upper_state when 4 then 0 else o.guest_count end) as guestCount,
            CEIL( SUM( CEIL( GREATEST( 1, TIMESTAMPDIFF(MINUTE, o.gmt_create, o.checkout_time) ) ) ) /
                SUM( CASE WHEN o.trade_mode = 0 THEN 1 ELSE 0 END) ) AS orderAverageTimeTotal,
            SUM(case o.upper_state when 2 then 0 when 4 then 0 else 1 end) as orderCount
        FROM
            hst_order o
        LEFT JOIN `hst_transaction_record` tr ON tr.order_guid = o.guid AND tr.payment_type = 13 AND tr.state = 4 AND tr.trade_type = 1
        LEFT JOIN
            (select
            t.guid as "order_guid",
            sum( CASE WHEN g.refund_order_guid IS NULL THEN g.coupon_buy_price ELSE 0 END ) buyPrice,
            sum( CASE WHEN g.refund_order_guid IS NULL THEN 0 ELSE g.coupon_buy_price END ) refundPrice,
            sum( CASE WHEN g.refund_order_guid IS NULL THEN 0 ELSE g.deduction_amount END ) refundDeductionAmount
            from
            hst_groupon g
            left join hst_order t on t.guid = g.order_guid and t.is_delete = 0
            where
            g.is_delete =0
            and t.checkout_time between #{businessStartDateTime} and #{businessEndDateTime}
            and t.state = 4 and t.is_delete = 0 and t.recovery_type in (1,3)
            and t.store_guid in
            <foreach collection="storeGuidList" item="storeGuid" open="(" separator=","
                     close=")">
                #{storeGuid}
            </foreach>
            group by g.order_guid
            ) temp on temp.order_guid = o.guid
        where
            o.checkout_time between #{businessStartDateTime} and #{businessEndDateTime}
        and o.state = 4 and o.is_delete = 0 and o.recovery_type in (1,3)
        and o.store_guid in
        <foreach collection="storeGuidList" item="storeGuid" open="(" separator=","
                 close=")">
            #{storeGuid}
        </foreach>
    </select>

    <select id="listOrderBusinessHisTrend" resultType="com.holderzone.saas.store.trade.entity.dto.BusinessHisTrendDTO">
        SELECT
            SUM(case o.upper_state when 2 then 0 else o.actually_pay_fee +IFNULL(temp.buyPrice,0)
            - o.refund_amount + IFNULL(tr.amount,0) - IFNULL(o.excess_amount,0) end)  as businessFee,
            SUM(case o.upper_state when 4 then 0 else o.guest_count end) as guestCount,
            SUM(case o.upper_state when 2 then 0 when 4 then 0 else 1 end) as orderCount,
            DATE_FORMAT(o.checkout_time,"%Y-%m-%d") date
        FROM
            hst_order o
        LEFT JOIN `hst_transaction_record` tr ON tr.order_guid = o.guid AND tr.payment_type = 13 AND tr.state = 4 AND tr.trade_type = 1
        LEFT JOIN
            (select
            t.guid as "order_guid",
            sum( CASE WHEN g.refund_order_guid IS NULL THEN g.coupon_buy_price ELSE 0 END ) buyPrice,
            sum( CASE WHEN g.refund_order_guid IS NULL THEN 0 ELSE g.coupon_buy_price END ) refundPrice
            from
            hst_groupon g
            left join hst_order t on t.guid = g.order_guid and t.is_delete = 0
            where
            g.is_delete =0
            and t.checkout_time between #{businessStartDateTime} and #{businessEndDateTime}
            and t.state = 4 and t.is_delete = 0 and t.recovery_type in (1,3)
            and t.store_guid in
            <foreach collection="storeGuidList" item="storeGuid" open="(" separator=","
                     close=")">
                #{storeGuid}
            </foreach>
            group by g.order_guid
            ) temp on temp.order_guid = o.guid
        where
            o.checkout_time between #{businessStartDateTime} and #{businessEndDateTime}
        and o.state = 4 and o.is_delete = 0 and o.recovery_type in (1,3)
        and o.store_guid in
        <foreach collection="storeGuidList" item="storeGuid" open="(" separator=","
                 close=")">
            #{storeGuid}
        </foreach>
        GROUP BY DATE_FORMAT(o.checkout_time,"%Y-%m-%d")
    </select>

    <select id="getGrouponOrderStatisticsCombined" resultType="java.math.BigDecimal">
        SELECT
            SUM(CASE WHEN coupon_buy_price = 0 THEN amount ELSE coupon_buy_price END)
        FROM
            (
                <choose>
                    <when test="grouponTypeList != null and grouponTypeList.size() > 0">
                            SELECT
                                g.coupon_buy_price AS coupon_buy_price,
                                g.amount AS amount
                            FROM
                                hst_groupon g
                            LEFT JOIN hst_order h on h.guid = g.order_guid
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            <where>
                                <include refid="orderStatisticsPaymentTypeConditions"/>
                                AND (h.upper_state = 0 OR h.upper_state = 1 OR (h.upper_state = 3 AND h.state != 7))
                                AND g.is_delete = 0
                                AND g.refund_order_guid is null
                                AND g.groupon_type IN
                                <foreach collection="grouponTypeList" item="grouponType" open="(" separator="," close=")">
                                    #{grouponType}
                                </foreach>
                            </where>
                            UNION
                            SELECT
                                g.coupon_buy_price AS coupon_buy_price,
                                g.amount AS amount
                            FROM
                                hst_groupon g
                            LEFT JOIN hst_order h on h.guid = g.order_guid
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            <where>
                                <include refid="orderStatisticsPaymentTypeConditions"/>
                                AND h.upper_state IN (0,1,3)
                                AND (h.state = 4 OR h.state = 12)
                                AND g.is_delete = 0
                                AND g.refund_order_guid is null
                                AND g.groupon_type IN
                                <foreach collection="grouponTypeList" item="grouponType" open="(" separator="," close=")">
                                    #{grouponType}
                                </foreach>
                            </where>
                    </when>
                    <otherwise>
                        SELECT
                            g.coupon_buy_price AS coupon_buy_price,
                            g.amount AS amount
                        FROM
                            hst_groupon g
                            LEFT JOIN hst_order h on h.guid = g.order_guid
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                        <where>
                            g.is_delete = 0
                            AND g.refund_order_guid is null
                            <include refid="orderStatisticsConditions"/>
                        </where>
                    </otherwise>
                </choose>
            ) t
    </select>


    <select id="pageQueryOrderWaiterDetails" resultMap="OrderPageDetailsMap"
            parameterType="com.holderzone.saas.store.trade.entity.query.OrderWaiterQueryDetails">
        SELECT
        ho.guid,
        ho.dining_table_name,
        ho.gmt_create ,
        ho.checkout_time ,
        ho.order_fee ,
        ho.store_name ,
        ho.order_no ,
        ho.trade_mode ,
        how.waiter_guid ,
        how.waiter_name ,
        how.waiter_type ,
        how.waiter_no
        FROM
        hst_order ho
        RIGHT JOIN hst_order_waiter how ON ho.guid = how.order_guid
        <where>
            ho.state = 4
            AND ho.recovery_type in (1, 3)
            <if test="null!=orderWaiterQueryDetails.storeGuidList and orderWaiterQueryDetails.storeGuidList.size>0">
                and ho.store_guid in
                <foreach collection="orderWaiterQueryDetails.storeGuidList" item="storeGuid" open="(" separator=","
                         close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="orderWaiterQueryDetails.timeType == 1 and null!=orderWaiterQueryDetails.startDateTime">
                and ho.gmt_create &gt;= #{orderWaiterQueryDetails.startDateTime}
            </if>
            <if test="orderWaiterQueryDetails.timeType == 1 and null!=orderWaiterQueryDetails.endDateTime">
                and ho.gmt_create &lt;= #{orderWaiterQueryDetails.endDateTime}
            </if>
            <if test="orderWaiterQueryDetails.timeType == 2 and null!=orderWaiterQueryDetails.startDateTime">
                and ho.checkout_time &gt;= #{orderWaiterQueryDetails.startDateTime}
            </if>
            <if test="orderWaiterQueryDetails.timeType == 2 and null!=orderWaiterQueryDetails.endDateTime">
                and ho.checkout_time &lt;= #{orderWaiterQueryDetails.endDateTime}
            </if>
            <if test="null!=orderWaiterQueryDetails.waiterType">
                and how.waiter_type = #{orderWaiterQueryDetails.waiterType}
            </if>
            <if test="orderWaiterQueryDetails.timeType == 1">
                ORDER BY ho.gmt_create DESC
            </if>
            <if test="orderWaiterQueryDetails.timeType == 2">
                ORDER BY ho.checkout_time DESC
            </if>
        </where>
    </select>


    <select id="getOrderStatisticsCount" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            (
                <choose>
                    <when test="(paymentTypeList != null and paymentTypeList.size() > 0) or (grouponTypeList != null and grouponTypeList.size() > 0)">
                        <if test="paymentTypeList != null and paymentTypeList.size() > 0">
                            SELECT
                                <include refid="orderStatisticsColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_transaction_record it ON it.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsPaymentTypeConditions"/>
                                AND h.state not in (5, 11)
                                AND (h.upper_state = 0 OR h.upper_state = 1 OR (h.upper_state = 3 AND h.state != 7))
                                AND it.state = 4
                                AND it.is_delete = 0
                                AND it.amount > 0
                                AND it.trade_type IN (1, 4)
                                AND it.payment_type_name IN
                                <foreach collection="paymentTypeList" item="paymentTypeName" open="(" separator="," close=")">
                                    #{paymentTypeName}
                                </foreach>
                            </where>
                            UNION
                            SELECT
                                <include refid="orderStatisticsColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_transaction_record it ON it.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsPaymentTypeConditions"/>
                                AND h.upper_state IN (0,1,3)
                                AND (h.state = 4 OR h.state = 12)
                                AND it.state = 4
                                AND it.is_delete = 0
                                AND it.amount > 0
                                AND it.trade_type IN (1, 4)
                                AND it.payment_type_name IN
                                <foreach collection="paymentTypeList" item="paymentTypeName" open="(" separator="," close=")">
                                    #{paymentTypeName}
                                </foreach>
                            </where>
                        </if>
                        <if test="(paymentTypeList != null and paymentTypeList.size() > 0) and (grouponTypeList != null and grouponTypeList.size() > 0)">
                            UNION
                        </if>
                        <if test="grouponTypeList != null and grouponTypeList.size() > 0">
                            SELECT
                                <include refid="orderStatisticsColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_groupon ig ON ig.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsPaymentTypeConditions"/>
                                AND (h.upper_state = 0 OR h.upper_state = 1 OR (h.upper_state = 3 AND h.state != 7))
                                AND ig.is_delete = 0
                                AND ig.refund_order_guid is null
                                AND ig.groupon_type IN
                                <foreach collection="grouponTypeList" item="grouponType" open="(" separator="," close=")">
                                    #{grouponType}
                                </foreach>
                            </where>
                            UNION
                            SELECT
                                <include refid="orderStatisticsColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_groupon ig ON ig.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsPaymentTypeConditions"/>
                                AND h.upper_state IN (0,1,3)
                                AND (h.state = 4 OR h.state = 12)
                                AND ig.is_delete = 0
                                AND ig.refund_order_guid is null
                                AND ig.groupon_type IN
                                <foreach collection="grouponTypeList" item="grouponType" open="(" separator="," close=")">
                                    #{grouponType}
                                </foreach>
                            </where>
                        </if>
                    </when>
                    <otherwise>
                        SELECT
                            <include refid="orderStatisticsColumns"/>
                        FROM
                            hst_order h
                        LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                        <where>
                            <include refid="orderStatisticsConditions"/>
                        </where>
                    </otherwise>
                </choose>
            ) t
    </select>

    <select id="getOrderStatisticsPage"
            resultType="com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsRespDTO">
        SELECT
            *
        FROM
            (
                <choose>
                    <when test="(paymentTypeList != null and paymentTypeList.size() > 0) or (grouponTypeList != null and grouponTypeList.size() > 0)">
                        <if test="paymentTypeList != null and paymentTypeList.size() > 0">
                            SELECT
                                <include refid="orderStatisticsColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_transaction_record it ON it.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsPaymentTypeConditions"/>
                                AND h.state not in (5, 11)
                                AND (h.upper_state = 0 OR h.upper_state = 1 OR (h.upper_state = 3 AND h.state != 7))
                                AND it.state = 4
                                AND it.is_delete = 0
                                AND it.amount > 0
                                AND it.trade_type IN (1, 4)
                                AND it.payment_type_name IN
                                <foreach collection="paymentTypeList" item="paymentTypeName" open="(" separator="," close=")">
                                    #{paymentTypeName}
                                </foreach>
                            </where>
                            UNION
                            SELECT
                                <include refid="orderStatisticsColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_transaction_record it ON it.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsPaymentTypeConditions"/>
                                AND h.upper_state IN (0,1,3)
                                AND (h.state = 4 OR h.state = 12)
                                AND it.state = 4
                                AND it.is_delete = 0
                                AND it.amount > 0
                                AND it.trade_type IN (1, 4)
                                AND it.payment_type_name IN
                                <foreach collection="paymentTypeList" item="paymentTypeName" open="(" separator="," close=")">
                                    #{paymentTypeName}
                                </foreach>
                            </where>
                        </if>
                        <if test="(paymentTypeList != null and paymentTypeList.size() > 0) and (grouponTypeList != null and grouponTypeList.size() > 0)">
                            UNION
                        </if>
                        <if test="grouponTypeList != null and grouponTypeList.size() > 0">
                            SELECT
                                <include refid="orderStatisticsColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_groupon ig ON ig.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsPaymentTypeConditions"/>
                                AND (h.upper_state = 0 OR h.upper_state = 1 OR (h.upper_state = 3 AND h.state != 7))
                                AND ig.is_delete = 0
                                AND ig.refund_order_guid is null
                                AND ig.groupon_type IN
                                <foreach collection="grouponTypeList" item="grouponType" open="(" separator="," close=")">
                                    #{grouponType}
                                </foreach>
                            </where>
                            UNION
                            SELECT
                                <include refid="orderStatisticsColumns"/>
                            FROM
                                hst_order h
                            LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                            LEFT JOIN hst_groupon ig ON ig.order_guid = h.guid
                            <where>
                                <include refid="orderStatisticsPaymentTypeConditions"/>
                                AND h.upper_state IN (0,1,3)
                                AND (h.state = 4 OR h.state = 12)
                                AND ig.is_delete = 0
                                AND ig.refund_order_guid is null
                                AND ig.groupon_type IN
                                <foreach collection="grouponTypeList" item="grouponType" open="(" separator="," close=")">
                                    #{grouponType}
                                </foreach>
                            </where>
                        </if>
                    </when>
                    <otherwise>
                        SELECT
                            <include refid="orderStatisticsColumns"/>
                        FROM
                            hst_order h
                        LEFT JOIN hst_order_extends oe on oe.guid = h.guid
                        <where>
                            <include refid="orderStatisticsConditions"/>
                        </where>
                    </otherwise>
                </choose>
            ) t
        ORDER BY createTime DESC
        LIMIT #{index}, #{pageSize}
    </select>

    <select id="pageOrderWaiterMakeUp" resultMap="OrderPageMap"
            parameterType="com.holderzone.saas.store.trade.entity.query.OrderWaiterQueryDetails">
        SELECT
        ho.guid,
        ho.dining_table_name,
        ho.gmt_create ,
        ho.checkout_time ,
        ho.order_fee ,
        ho.actually_pay_fee ,
        ho.store_name ,
        ho.order_no ,
        ho.trade_mode
        FROM
        hst_order ho
        <where>
            ho.state = 4
            AND ho.recovery_type in (1, 3)
            <if test="null!=orderWaiterQueryDetails.storeGuidList and orderWaiterQueryDetails.storeGuidList.size>0">
                and ho.store_guid in
                <foreach collection="orderWaiterQueryDetails.storeGuidList" item="storeGuid" open="(" separator=","
                         close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="null!=orderWaiterQueryDetails.tableGuidList and orderWaiterQueryDetails.tableGuidList.size>0">
                and ho.dining_table_guid in
                <foreach collection="orderWaiterQueryDetails.tableGuidList" item="tableGuid" open="(" separator=","
                         close=")">
                    #{tableGuid}
                </foreach>
            </if>
            <if test="orderWaiterQueryDetails.timeType == 1 and null!=orderWaiterQueryDetails.startDateTime">
                and ho.gmt_create &gt;= #{orderWaiterQueryDetails.startDateTime}
            </if>
            <if test="orderWaiterQueryDetails.timeType == 1 and null!=orderWaiterQueryDetails.endDateTime">
                and ho.gmt_create &lt;= #{orderWaiterQueryDetails.endDateTime}
            </if>
            <if test="orderWaiterQueryDetails.timeType == 2 and null!=orderWaiterQueryDetails.startDateTime">
                and ho.checkout_time &gt;= #{orderWaiterQueryDetails.startDateTime}
            </if>
            <if test="orderWaiterQueryDetails.timeType == 2 and null!=orderWaiterQueryDetails.endDateTime">
                and ho.checkout_time &lt;= #{orderWaiterQueryDetails.endDateTime}
            </if>
            <if test="null!=orderWaiterQueryDetails.orderNo">
                and ho.order_no LIKE CONCAT('%',#{orderWaiterQueryDetails.orderNo},'%')
            </if>
            <if test="null!= orderWaiterQueryDetails.frontOrderOperator">
                and ho.order_fee
                <if test="null!= orderWaiterQueryDetails.frontOrderOperator.operator and orderWaiterQueryDetails.frontOrderOperator.operator == 0">
                    =
                </if>
                <if test="null!= orderWaiterQueryDetails.frontOrderOperator.operator and orderWaiterQueryDetails.frontOrderOperator.operator == 1">
                    &gt;
                </if>
                <if test="null!= orderWaiterQueryDetails.frontOrderOperator.operator and orderWaiterQueryDetails.frontOrderOperator.operator == 2">
                    &gt;=
                </if>
                #{orderWaiterQueryDetails.frontOrderOperator.orderFee}
            </if>

            <if test="null!= orderWaiterQueryDetails.rearOrderOperator and orderWaiterQueryDetails.frontOrderOperator.operator > 0">
                and ho.order_fee
                <if test="null!= orderWaiterQueryDetails.rearOrderOperator.operator and orderWaiterQueryDetails.rearOrderOperator.operator == -1">
                    &lt;
                </if>
                <if test="null!= orderWaiterQueryDetails.rearOrderOperator.operator and orderWaiterQueryDetails.rearOrderOperator.operator == -2">
                    &lt;=
                </if>
                #{orderWaiterQueryDetails.rearOrderOperator.orderFee}
            </if>

            <if test="orderWaiterQueryDetails.timeType == 1">
                ORDER BY ho.is_waiter ASC ,ho.gmt_create DESC
            </if>
            <if test="orderWaiterQueryDetails.timeType == 2">
                ORDER BY ho.is_waiter ASC ,ho.checkout_time DESC
            </if>
        </where>
    </select>


    <sql id="orderStatisticsConditions">
        <if test="startTime!=null and startTime!=''">
            AND h.business_day >= #{startTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            AND h.business_day &lt;= #{endTime}
        </if>
        <if test="state!=null">
            AND h.trade_mode = #{state}
        </if>
        <if test="customerType!=null">
            <choose>
                <when test="customerType==0">
                    AND h.member_guid != 0
                </when>
                <otherwise>
                    AND h.member_guid = 0
                </otherwise>
            </choose>
        </if>
        <if test="orderQueryInState!=null and orderQueryInState!=''">
            AND h.state IN (#{orderQueryInState})
        </if>
        <if test="orderNo!=null and orderNo!=''">
            AND h.order_no = #{orderNo}
        </if>
        <if test="searchKey!=null and searchKey!=''">
            AND  (h.order_no like concat('%',#{searchKey},'%')
                    OR ( IF(h.trade_mode = 0, IF(oe.associated_flag=1,oe.associated_table_names,h.dining_table_name),h.mark) like concat('%',#{searchKey},'%')))
        </if>
        <if test="deviceTypeInSql!=null and deviceTypeInSql!=''">
            AND h.device_type IN (${deviceTypeInSql})
        </if>
        <if test="storeGuidList!=null and storeGuidList.size>0">
            AND h.store_guid IN
            <foreach collection="storeGuidList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="checkoutStaffGuids != null and checkoutStaffGuids.size() > 0">
            AND h.checkout_staff_guid in
            <foreach collection="checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
        <if test="diningTableGuids!=null and diningTableGuids.size() > 0">
            AND h.dining_table_guid in
            <foreach collection="diningTableGuids" item="diningTableGuid" open="(" separator="," close=")">
                #{diningTableGuid}
            </foreach>
        </if>
        <if test="isInvoice != null">
            and oe.is_invoice = #{isInvoice}
        </if>
        AND h.upper_state IN (0,1,3)
        AND h.state not in (5, 11)
        AND h.recovery_type != 4
        AND h.is_delete = 0
    </sql>

    <sql id="orderStatisticsCombinedConditions">
        <if test="startTime!=null and startTime!=''">
            AND h.business_day >= #{startTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            AND h.business_day &lt;= #{endTime}
        </if>
        <if test="state!=null">
            AND h.trade_mode = #{state}
        </if>
        <if test="customerType!=null">
            <choose>
                <when test="customerType==0">
                    AND h.member_guid != 0
                </when>
                <otherwise>
                    AND h.member_guid = 0
                </otherwise>
            </choose>
        </if>
        <if test="orderQueryInState!=null and orderQueryInState!=''">
            AND h.state IN (#{orderQueryInState})
        </if>
        <if test="orderNo!=null and orderNo!=''">
            AND h.order_no = #{orderNo}
        </if>
        <if test="searchKey!=null and searchKey!=''">
            AND  (h.order_no like concat('%',#{searchKey},'%')
            OR ( IF(h.trade_mode = 0, IF(oe.associated_flag=1,oe.associated_table_names,h.dining_table_name),h.mark) like concat('%',#{searchKey},'%')))
        </if>
        <if test="deviceTypeInSql!=null and deviceTypeInSql!=''">
            AND h.device_type IN (${deviceTypeInSql})
        </if>
        <if test="storeGuidList!=null and storeGuidList.size>0">
            AND h.store_guid IN
            <foreach collection="storeGuidList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="checkoutStaffGuids != null and checkoutStaffGuids.size() > 0">
            AND h.checkout_staff_guid in
            <foreach collection="checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
        <if test="diningTableGuids!=null and diningTableGuids.size() > 0">
            AND h.dining_table_guid in
            <foreach collection="diningTableGuids" item="diningTableGuid" open="(" separator="," close=")">
                #{diningTableGuid}
            </foreach>
        </if>
        <if test="isInvoice != null">
            and oe.is_invoice = #{isInvoice}
        </if>
        AND h.recovery_type != 4
        AND h.is_delete = 0
    </sql>

    <sql id="orderStatisticsCombinedColumns">
        h.guid,
        h.upper_state AS upper_state,
        h.state AS state,
        h.actually_pay_fee AS actually_pay_fee,
        h.refund_amount AS refund_amount,
        h.excess_amount AS excess_amount,
        d.discount_fee AS discount_fee
    </sql>

    <sql id="orderStatisticsColumns">
        h.guid,
        h.store_name,
        h.trade_mode,
        h.order_no,
        h.state AS orderState,
        h.device_type AS orderSource,
        h.gmt_create AS createTime,
        h.checkout_time,
        h.dining_table_guid,
        IF(h.trade_mode = 0, IF(oe.associated_flag=1,concat('联台-',oe.associated_sn,' (',JSON_LENGTH(oe.associated_table_guids),')'),h.dining_table_name),h.mark) AS diningTableName,
        case when h.upper_state in (3,4) then IFNULL(h.order_fee_for_combine,0) else h.order_fee end as "orderFeeForCombine",
        h.actually_pay_fee - h.refund_amount - IFNULL(h.excess_amount,0) as "actually_pay_fee",
        h.checkout_staff_name AS cashier,
        h.recovery_type,
        h.refund_order_guid,
        oe.is_invoice,
        h.upper_state AS isMerge,
        oe.total_coupon_buy_price
    </sql>

    <sql id="orderStatisticsPaymentTypeConditions">
        <if test="startTime!=null and startTime!=''">
            AND h.business_day >= #{startTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            AND h.business_day &lt;= #{endTime}
        </if>
        <if test="state!=null">
            AND h.trade_mode = #{state}
        </if>
        <if test="customerType!=null">
            <choose>
                <when test="customerType==0">
                    AND h.member_guid != 0
                </when>
                <otherwise>
                    AND h.member_guid = 0
                </otherwise>
            </choose>
        </if>
        <if test="orderQueryInState!=null and orderQueryInState!=''">
            AND h.state IN (#{orderQueryInState})
        </if>
        <if test="orderNo!=null and orderNo!=''">
            AND h.order_no = #{orderNo}
        </if>
        <if test="searchKey!=null and searchKey!=''">
            AND  (h.order_no like concat('%',#{searchKey},'%')
            OR ( IF(h.trade_mode = 0, IF(oe.associated_flag=1,oe.associated_table_names,h.dining_table_name),h.mark) like concat('%',#{searchKey},'%')))
        </if>
        <if test="deviceTypeInSql!=null and deviceTypeInSql!=''">
            AND h.device_type IN (${deviceTypeInSql})
        </if>
        <if test="storeGuidList!=null and storeGuidList.size>0">
            AND h.store_guid IN
            <foreach collection="storeGuidList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="checkoutStaffGuids != null and checkoutStaffGuids.size() > 0">
            AND h.checkout_staff_guid in
            <foreach collection="checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
        <if test="diningTableGuids!=null and diningTableGuids.size() > 0">
            AND h.dining_table_guid in
            <foreach collection="diningTableGuids" item="diningTableGuid" open="(" separator="," close=")">
                #{diningTableGuid}
            </foreach>
        </if>
        <if test="isInvoice != null">
            and oe.is_invoice = #{isInvoice}
        </if>
        AND h.recovery_type != 4
        AND h.is_delete = 0
    </sql>

    <update id="updateIsWaiters">
        UPDATE hst_order ho,
        ( SELECT order_guid FROM hst_order_waiter
        <if test="null!=orderGuidList and orderGuidList.size>0">
            where order_guid in
            <foreach collection="orderGuidList" item="orderGuid" open="(" separator="," close=")">
                #{orderGuid}
            </foreach>
        </if>
        GROUP BY order_guid HAVING count( 1 ) > 4 ) how
        SET ho.is_waiter = 1
        WHERE
        ho.guid = how.order_guid
    </update>

    <select id="storeGatherTotalListByExport"
            resultType="com.holderzone.saas.store.dto.journaling.resp.StoreGatherReportRespDTO">
        select
            checkout_time as date,store_guid storeGuid,store_name storeName,sum(orderFee) orderFee,sum(orderFee) -
        sum(actuallyPayFee) discountFee,
            sum(savingZero) savingZero,
            sum(memberDiscount) memberDiscount,sum(memberCoupons) memberCoupons,
            sum(itemPresent) itemPresent,sum(fullSinglePrice) fullSinglePrice,sum(groupCoupons) groupCoupons,
            sum(wholeDiscount) wholeDiscount,sum(otherDiscount) otherDiscount,sum(actuallyPayFee) actuallyPayFee,
            sum(orderCount) orderCount,round(sum(actuallyPayFee)/sum(orderCount),2) orderAverageFee,sum(guestCount) guestCount,
            round(sum(actuallyPayFee)/sum(guestCount),2) guestAverageFee
        from
        (
            SELECT
                date_format(o.checkout_time,'%Y-%m-%d') checkout_time,
                o.store_guid,o.store_name,
                (case o.upper_state when 2 then 0 else o.order_fee end) orderFee,
                (case d.discount_type when 4 then d.discount_fee else 0 end) savingZero,
                (case d.discount_type when 1 then d.discount_fee else 0 end) memberDiscount,
                (case d.discount_type when 7 then d.discount_fee else 0 end) memberCoupons,
                (case d.discount_type when 5 then d.discount_fee else 0 end) itemPresent,
                (case d.discount_type when 3 then d.discount_fee else 0 end) fullSinglePrice,
                (case d.discount_type when 6 then d.discount_fee else 0 end) groupCoupons,
                (case d.discount_type when 2 then d.discount_fee else 0 end) wholeDiscount,
                (case when d.discount_type > 7 then d.discount_fee else 0 end) otherDiscount,
                (case o.upper_state when 2 then 0 else o.actually_pay_fee end) actuallyPayFee,
                (case when o.upper_state != 2 and (o.recovery_type = 1 or o.recovery_type = 3) then 1 else 0 end) orderCount,
                o.guest_count guestCount
            FROM
                hst_order o left join hst_discount d on o.guid = d.order_guid and d.discount_fee > 0
            where
                o.state = 4
                AND o.is_delete = 0
                and o.recovery_type != 4
                AND o.checkout_time BETWEEN #{reportReqDTO.businessStartDateTime} and #{reportReqDTO.businessEndDateTime}
                and FIND_IN_SET(o.store_guid,#{reportReqDTO.storeGuidStr}) > 0
                group by o.guid
        ) as temp
        group by checkout_time,store_guid
    </select>

    <select id="listStoreGatherBusiness" resultType="com.holderzone.saas.store.dto.journaling.resp.StoreGatherReportRespDTO">
        SELECT
            SUM(case o.upper_state when 2 then 0 else o.order_fee - IFNULL(x.refundDeductionAmount,0) - o.refund_amount end) as orderFee,
            SUM(case o.upper_state when 2 then 0 else o.actually_pay_fee
            + IFNULL(x.buyPrice,0) - o.refund_amount + IFNULL(tr.amount,0) - IFNULL(o.excess_amount,0) end)  AS actuallyPayFee,

            SUM( case o.upper_state when 4 then 0 else o.guest_count end ) as guestCount,
            SUM( case o.upper_state when 2 then 0 when 4 then 0 else 1 end ) as orderCount,
            DATE_FORMAT(o.checkout_time,"%Y-%m-%d") date,
            CEIL( SUM( CEIL( GREATEST( 1, TIMESTAMPDIFF(MINUTE, o.gmt_create, o.checkout_time) ) ) )
                / SUM( CASE WHEN o.trade_mode = 0 THEN 1 ELSE 0 END) ) AS orderAverageTime,
            o.store_guid
        FROM
            hst_order o
            LEFT JOIN `hst_transaction_record` tr ON tr.order_guid = o.guid
            AND tr.payment_type = 13
            AND tr.state = 4
            AND tr.trade_type = 1
            LEFT JOIN
            (
                SELECT
                    g.order_guid as guid,
                    sum( CASE WHEN g.refund_order_guid IS NULL THEN g.coupon_buy_price ELSE 0 END ) buyPrice,
                    sum( CASE WHEN g.refund_order_guid IS NULL THEN 0 ELSE g.coupon_buy_price END ) refundPrice,
                    sum( CASE WHEN g.refund_order_guid IS NULL THEN 0 ELSE g.deduction_amount END ) refundDeductionAmount
                FROM
                    hst_groupon g
                    JOIN hst_order t on t.guid = g.order_guid
                where
                    g.is_delete =0
                    AND  g.coupon_buy_price > 0
<!--                    and g.refund_order_guid is null-->
                    and t.state = 4
                    AND t.is_delete = 0
                    AND t.recovery_type != 4
                    and t.checkout_time between #{req.businessStartDateTime} and #{req.businessEndDateTime}
                    and t.store_guid in
                    <foreach collection="req.storeGuids" item="storeGuid" open="(" separator=","
                         close=")">
                        #{storeGuid}
                    </foreach> GROUP BY g.order_guid
            ) x ON o.guid=x.guid
        WHERE
            o.checkout_time between #{req.businessStartDateTime} and #{req.businessEndDateTime}
            and o.state = 4 and o.is_delete = 0 and o.recovery_type in (1,3)
            and o.store_guid in
            <foreach collection="req.storeGuids" item="storeGuid" open="(" separator=","
                     close=")">
                #{storeGuid}
            </foreach>
        GROUP BY DATE_FORMAT(o.checkout_time,"%Y-%m-%d"),o.store_guid
    </select>

    <select id="storeGatherTotalList"
            resultType="com.holderzone.saas.store.dto.journaling.resp.StoreGatherReportRespDTO">
        select
            checkout_time as date,store_guid storeGuid,store_name storeName,sum(orderFee) orderFee,sum(orderFee) - sum(actuallyPayFee) discountFee,
            sum(actuallyPayFee) actuallyPayFee,
            sum(orderCount) orderCount,round(sum(actuallyPayFee)/sum(orderCount),2) orderAverageFee,sum(guestCount) guestCount,
            round(sum(actuallyPayFee)/sum(guestCount),2) guestAverageFee
        from
        (
            SELECT
            date_format(o.checkout_time,'%Y-%m-%d') checkout_time,
            o.store_guid,o.store_name,
            (case o.upper_state when 2 then 0 else o.order_fee end) orderFee,
            (case o.upper_state when 2 then 0 else o.actually_pay_fee end) actuallyPayFee,
            (case when o.upper_state != 2 and (o.recovery_type = 1 or o.recovery_type = 3) then 1 else 0 end) orderCount,
            o.guest_count guestCount
            FROM
            hst_order o
            where
                o.state = 4
                AND o.is_delete = 0
                and o.recovery_type in (1,3)
                AND o.checkout_time BETWEEN #{reportReqDTO.businessStartDateTime} and #{reportReqDTO.businessEndDateTime}
                and FIND_IN_SET(o.store_guid,#{reportReqDTO.storeGuidStr}) > 0
        ) as temp
        group by checkout_time,store_guid
    </select>


    <update id="updateAdjustStateIsTrue">
        update hst_order set adjust_state = 1 where guid = #{guid}
    </update>

    <update id="updateOrderIsUpdatedEs">
        update hst_order set is_updated_es = 1 where guid = #{guid}
    </update>

    <update id="updateOrderReserve">
        update
            hst_order
        set
            reserve_fee = #{query.reserveAmount},
            reserve_guid = #{query.reserveGuid}
        where
            guid = #{query.orderGuid}
    </update>

    <update id="updateOrderMemberInfo">
        update
            hst_order
        set
            member_guid = #{query.memberGuid},
            <if test="query.memberCardGuid != null and query.memberCardGuid != ''">
                member_card_guid = #{query.memberCardGuid},
            </if>
            member_phone = #{query.memberPhone},
            member_name = #{query.memberName}
        where
            guid = #{query.orderGuid}
    </update>


    <update id="updateSameOrderFeeForCombine">
        UPDATE hst_order o1,
        (   SELECT
                sum( IFNULL(order_fee_for_combine, IFNULL(order_fee, 0)) ) AS "sub_order_fee_for_combine"
            FROM
                hst_order
            WHERE
                main_order_guid = #{guid} and state in (1, 2, 3, 4, 12) ) o2
        SET
            o1.order_fee_for_combine = o1.order_fee + o2.sub_order_fee_for_combine
        WHERE
            o1.guid = #{guid}
    </update>

    <select id="queryfristorderForStoreGuid" resultType="java.time.LocalDateTime">
        SELECT min(gmt_create) gmt_create FROM `hst_order` where store_guid = #{storeGuid}
    </select>

    <select id="saleStoreStatistics"
            resultType="com.holderzone.saas.store.dto.journaling.resp.StoreStatisticsAppRespDTO$StoreStatisticsDetailDTO">
        SELECT
            o.store_guid AS storeGuid,
            o.store_name AS storeName,
            SUM( case o.upper_state when 4 then 0 else 1 end ) as orderCount,
            SUM( o.actually_pay_fee - o.refund_amount ) + SUM( IFNULL( g.coupon_buy_price, 0 ) )  AS businessFee
        FROM
            hst_order o
            LEFT JOIN hst_groupon g ON o.guid = g.order_guid AND g.is_delete = 0
        WHERE
            o.checkout_time BETWEEN #{query.businessStartDateTime} AND #{query.businessEndDateTime}
            AND o.state = 4
            AND o.is_delete = 0
            AND o.recovery_type IN ( 1, 3 )
            AND o.upper_state IN ( 0, 1, 3, 4 )
            AND o.store_guid IN
            <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator=","
                     close=")">
                #{storeGuid}
            </foreach>
        GROUP BY
            o.store_guid
        ORDER BY
            businessFee DESC
        LIMIT #{query.pageSize}
    </select>

    <select id="saleByHoursStatistics"
            resultType="com.holderzone.saas.store.dto.journaling.resp.StoreSaleItemDTO">
        SELECT
            date_format( checkout_time, '%Y-%m-%d %H' ) AS TIME,
            SUM( actually_pay_fee - refund_amount ) AS sumActuallyPayFee
        FROM
            hst_order
        WHERE
            checkout_time BETWEEN #{query.businessStartDateTime} AND #{query.businessEndDateTime}
            AND state = 4
            AND is_delete = 0
            AND recovery_type IN ( 1, 3 )
            AND upper_state IN ( 0, 1, 3, 4 )
            AND store_guid IN
            <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator=","
                     close=")">
                #{storeGuid}
            </foreach>
        GROUP BY date_format( checkout_time, '%Y-%m-%d %H' )
    </select>
    <select id="getCheckoutStaffs" resultType="com.holderzone.saas.store.dto.user.resp.UserBriefDTO">
        SELECT
            h.checkout_staff_guid as userGuid,
            h.checkout_staff_name as userName
        FROM hst_order h
        LEFT JOIN hst_order_extends oe on oe.guid = h.guid
        <where>
            <include refid="orderStatisticsConditions"/>
        </where>
        GROUP BY h.checkout_staff_guid
    </select>


    <select id="pageOrderInfo" resultType="com.holderzone.saas.store.dto.trade.OrderInfoRespDTO">
        SELECT
            guid,
            gmt_create,
            order_no,
            device_type,
            guest_count,
            business_day,
            dining_table_guid,
            dining_table_name,
            remark,
            mark,
            order_fee,
            append_fee,
            actually_pay_fee,
            state,
            cancel_reason,
            cancel_device_type,
            recovery_type,
            member_guid,
            member_phone,
            member_name,
            store_guid,
            store_name,
            adjust_state,
            refund_order_guid,
            refund_amount,
            excess_amount,
            trade_mode,
            checkout_time
        FROM
            hst_order
        <where>
            is_delete = 0
            and upper_state IN ( 0, 1, 3, 4 )
            and state != 11
            <if test="query.storeGuid != null and query.storeGuid != ''">
                AND store_guid = #{query.storeGuid}
            </if>
            <if test="query.states != null and query.states.size() > 0">
                AND state in
                <foreach collection="query.states" item="state" open="(" separator="," close=")" >
                    #{state}
                </foreach>
            </if>
            <if test="query.recoveryTypes != null and query.recoveryTypes.size() > 0">
                AND recovery_type in
                <foreach collection="query.recoveryTypes" item="recoveryType" open="(" separator="," close=")" >
                    #{recoveryType}
                </foreach>
            </if>
            <if test="query.tradeMode != null">
                AND trade_mode = #{query.tradeMode}
            </if>
            <choose>
                <when test="query.memberInfoGuid != null and query.memberInfoGuid != '' and query.openId != null and query.openId != '' ">
                    AND ( member_guid = #{query.memberInfoGuid} or user_wx_public_open_id = #{query.openId} )
                </when>
                <otherwise>
                    <if test="query.memberInfoGuid != null and query.memberInfoGuid != ''">
                        AND member_guid = #{query.memberInfoGuid}
                    </if>
                    <if test="query.openId != null and query.openId != ''">
                        AND user_wx_public_open_id = #{query.openId}
                    </if>
                </otherwise>
            </choose>
            <if test="query.searchKey != null and query.searchKey != ''">
                AND ( member_phone like concat('%', #{query.searchKey}, '%')
                 or order_no like concat('%', #{query.searchKey}, '%')
                 or mark like concat('%', #{query.searchKey}, '%')
                 or dining_table_name like concat('%', #{query.searchKey}, '%') )
            </if>
            <if test="query.miniAppFlag != null and query.miniAppFlag">
                AND device_type in (13, 15, 17)
            </if>
            <if test="query.miniAppFlag != null and !query.miniAppFlag">
                AND device_type not in (13, 15, 17) AND state = 4
            </if>
        </where>
        order by gmt_create desc, guid desc
    </select>
    <select id="retailHandoverOrderCount" resultType="java.lang.Integer">
        select count(*)
        from
        hst_retail_order
        <where>
             state = 4
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                and store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                and checkout_time &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                and checkout_time &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and checkout_staff_guid = #{dto.userGuid}
            </if>
        </where>
    </select>

    <select id="listPaymentTypeName" resultType="java.lang.String">
        select
            payment_type_name
        from
            hst_transaction_record
        group by
            payment_type_name
        order by
            payment_type
    </select>

    <select id="orderPage"
            resultType="com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO">
        SELECT
            o.guid,
            o.order_no,
            o.gmt_create,
            o.device_type,
            o.recovery_type,
            o.checkout_time,
            o.actually_pay_fee,
            o.state,
            o.upper_state,
            o.store_guid,
            o.store_name,
            o.guest_count,
            o.business_day,
            o.dining_table_guid,
            o.dining_table_name,
            o.mark,
            case when o.upper_state in (3,4) then IFNULL(o.order_fee_for_combine,0) else o.order_fee end as "order_fee",
            o.cancel_reason,
            o.adjust_state,
            o.refund_order_guid,
            o.refund_amount,
            o.excess_amount,
            o.checkout_staff_guid,
            o.checkout_staff_name,
            o.create_staff_guid,
            o.create_staff_name,
            case when o.refund_order_guid is null then false else true end as "hasRefundOrderFlag"
        FROM
            hst_order o
        <where>
            o.is_delete = 0
            and o.store_guid = #{query.storeGuid}
            and o.trade_mode = #{query.tradeMode}
            and o.upper_state not in (2, 4)
            <if test="query.beginTime != null">
                and o.gmt_create &gt;= #{query.beginTime}
            </if>
            <if test="query.endTime != null">
                and o.gmt_create &lt;= #{query.endTime}
            </if>
            <choose>
                <when test="query.state != null">
                    <if test="query.state == 0">
                        and o.state between 1 and 3
                        and (
                            o.upper_state <![CDATA[ <> ]]> 2
                            or
                            o.recovery_id is null
                        )
                        <if test="query.tradeMode != null and query.tradeMode == 1">
                            and o.device_type <![CDATA[ <> ]]> 12
                        </if>
                    </if>
                    <if test="query.state == 1">
                        and o.state = 4
                        and o.upper_state <![CDATA[ <> ]]> 2
                    </if>
                    <if test="query.state == 2">
                        and o.state between 5 and 6
                        and (
                            o.upper_state <![CDATA[ <> ]]> 2
                            or
                            o.recovery_id is null
                        )
                    </if>
                </when>
                <otherwise>
                    and o.state <![CDATA[ <> ]]> 11
                    <if test="query.tradeMode != null and query.tradeMode == 1">
                        and (
                            o.device_type not in (8,12,13,15,17)
                            or
                            o.state <![CDATA[ <> ]]> 1
                        )
                        and (
                            o.cancel_device_type not in (8,12,13,15,17)
                            or
                            o.cancel_device_type is null
                        )
                    </if>
                </otherwise>
            </choose>
            <if test="query.paymentTypeList != null and query.paymentTypeList.size() > 0">
                AND o.guid IN(
                    SELECT
                        DISTINCT order_guid
                    FROM
                        hst_transaction_record
                    WHERE
                        state = 4
                        and is_delete = 0
                        and payment_type_name IN
                        <foreach collection="query.paymentTypeList" item="paymentTypeName" open="(" separator=","
                                 close=")">
                            #{paymentTypeName}
                        </foreach>
                        AND ( payment_type = 1 OR ( payment_type <![CDATA[ <> ]]> 1 AND ( amount - refund_amount ) > 0 ) )
                        and trade_type = 1
                    union
                    SELECT
                        DISTINCT io.main_order_guid as "order_guid"
                    FROM
                        hst_transaction_record it
                    join hst_order io on io.guid = it.order_guid and io.upper_state = 4
                    WHERE
                        it.state = 4
                        and it.is_delete = 0
                        and it.payment_type_name IN
                        <foreach collection="query.paymentTypeList" item="paymentTypeName" open="(" separator=","
                                 close=")">
                            #{paymentTypeName}
                        </foreach>
                        AND ( it.payment_type = 1 OR ( it.payment_type != 1 AND ( it.amount - it.refund_amount ) > 0 ) )
                        and it.trade_type = 1
                )
            </if>
            <if test="query.searchKey != null and query.searchKey != ''">
                and (
                    o.order_no like concat('%', #{query.searchKey}, '%')
                    or
                    o.dining_table_name like concat('%', #{query.searchKey}, '%')
                    or
                    o.mark like concat('%', #{query.searchKey}, '%')
                    or
                    o.member_phone like concat('%', #{query.searchKey}, '%')
                )
            </if>
            <if test="query.sortType != null">
                <if test="query.sortType == 0">
                    order by o.gmt_create desc
                </if>
                <if test="query.sortType == 1">
                    order by o.gmt_create asc
                </if>
                <if test="query.sortType == 2">
                    order by o.gmt_create desc
                </if>
                <if test="query.sortType == 3">
                    order by o.order_fee asc
                </if>
                <if test="query.sortType == 4">
                    order by o.order_fee desc
                </if>
                <if test="query.sortType == 5">
                    ORDER BY IF(checkout_time IS NULL,NOW(),checkout_time) ASC
                </if>
                <if test="query.sortType == 6">
                    ORDER BY IF(checkout_time IS NULL,NOW(),checkout_time) DESC
                </if>
                <if test="query.sortType == 7">
                    ORDER BY o.actually_pay_fee ASC
                </if>
                <if test="query.sortType == 8">
                    ORDER BY o.actually_pay_fee DESC
                </if>
            </if>

        </where>
    </select>

    <select id="handoverOrderOptimization" parameterType="com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO"
            resultType="com.holderzone.saas.store.dto.trade.HandoverOrderOptimizationDTO">
        SELECT
        o.guid,
        o.main_order_guid,
        o.order_fee,
        o.upper_state,
        o.trade_mode,
        o.guest_count,
        o.gmt_create,
        o.checkout_time,
        oe.associated_flag,
        oe.associated_table_guids
        FROM
        hst_order o
        LEFT JOIN hst_order_extends oe ON o.guid = oe.guid
        <where>
            o.recovery_type in (1, 3)
            AND o.state = 4
            AND o.upper_state in (0,1,2,3)
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                AND o.store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                AND o.checkout_time &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                AND o.checkout_time &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                AND o.checkout_staff_guid = #{dto.userGuid}
            </if>
        </where>
    </select>

</mapper>
