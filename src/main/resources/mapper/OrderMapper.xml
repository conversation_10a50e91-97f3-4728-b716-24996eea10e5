<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.retail.mapper.OrderMapper">


    <select id="getOrderList" parameterType="com.holderzone.saas.store.dto.retail.dinein.RetailOrderListReqDTO"
            resultType="com.holderzone.saas.store.dto.retail.dinein.RetailOrderListRespDTO">
        SELECT SQL_CALC_FOUND_ROWS
        o.state,o.order_type,o.order_fee,o.guid,o.gmt_create,o.order_no,o.checkout_staff_guid,o.checkout_staff_name,o.actually_pay_fee,o.checkout_time
        FROM
        hst_retail_order o
        right JOIN hst_retail_transaction_record t ON o.guid=t.order_guid where o.state &lt;&gt; 1 and t.state=4

        <if test="retailOrderListReqDTO.state != null">
            AND o.state = #{retailOrderListReqDTO.state}
        </if>

        <if test="retailOrderListReqDTO.beginTime !=null">
            and o.gmt_create &gt; #{retailOrderListReqDTO.beginTime}
        </if>
        <if test="retailOrderListReqDTO.orderType !=null">
            and o.order_type = #{retailOrderListReqDTO.orderType}
        </if>
        <if test="retailOrderListReqDTO.endTime !=null">
            and o.gmt_create &lt; #{retailOrderListReqDTO.endTime}
        </if>

        <if test="retailOrderListReqDTO.staffGuid != null and retailOrderListReqDTO.staffGuid.size() > 0" >
            and o.checkout_staff_guid in
            <foreach collection="retailOrderListReqDTO.staffGuid" item="staff" open="(" separator="," close=")">
                #{staff}
            </foreach>
        </if>

        <if test="retailOrderListReqDTO.payWay != null and retailOrderListReqDTO.payWay.size() > 0">
            and  t.payment_type_name in
            <foreach collection="retailOrderListReqDTO.payWay" item="pay" open="(" separator="," close=")">
                #{pay}
            </foreach>
        </if>
        <if test="retailOrderListReqDTO.goodsName !=null and retailOrderListReqDTO.goodsName != ''">
            AND (
                o.order_no like ${retailOrderListReqDTO.goodsName} or
                o.guid in (
                    select DISTINCT order_guid from hst_retail_order_item where item_name like
                    ${retailOrderListReqDTO.goodsName} or code like ${retailOrderListReqDTO.goodsName}
                )
            )
        </if>
        order by o.guid desc
        <if test="pageStart !=null and pageSize != null">
            limit #{pageStart},#{pageSize}
        </if>

    </select>

    <select id="findRow" resultType="java.lang.Integer">
        SELECT FOUND_ROWS();
    </select>

</mapper>
