<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.BindupAccountsMapper">


    <select id="queryBindUpAccountsLast"
            resultType="com.holderzone.saas.store.business.entity.domain.BindupAccountsDo">
        SELECT bindup_accounts,max(gmt_create) gmt_create FROM `hsb_bindup_accounts` where store_guid = #{storeGuid}
    </select>
</mapper>