<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.ItemMapper">
    <resultMap id="ItemTemplateSubItemRespDTO"
               type="com.holderzone.saas.store.dto.item.resp.ItemTemplateSubItemRespDTO">
        <result column="guid" property="guid"/>
        <result column="sku_guid" property="skuGuid"/>
        <result column="name" property="name"/>
        <result column="sku_name" property="skuName"/>
        <result column="unit" property="unit"/>
        <result column="type_guid" property="typeGuid"/>
        <result column="type_name" property="typeName"/>
        <result column="sale_price" property="salePrice"/>
        <result column="item_type" property="itemType"/>
    </resultMap>


    <resultMap id="JournalingItemsQuery" type="com.holderzone.saas.store.item.entity.query.JournalingItemsQuery">
        <result column="guid" property="guid"/>
        <result column="brand_guid" property="brandGuid"/>
        <result column="item_from" property="itemFrom"/>
        <result column="parent_guid" property="parentGuid"/>
        <result column="name" property="name"/>
    </resultMap>
    <delete id="deleteByGuid">
        delete
        from hsi_item
        where guid = #{guid};
    </delete>

    <delete id="removeItemByParentId">
        DELETE
        FROM hsi_item
        WHERE parent_guid = #{parentGuid}
          AND store_guid = #{store_guid}
          AND item_from = 2;
    </delete>

    <update id="deleteByGuidAndFrom">
        DELETE
        FROM hsi_item
        WHERE guid = #{guid}
          AND store_guid = #{storeGuid}
          AND item_from = #{itemFrom};
    </update>

    <select id="getSkuItemList" resultMap="ItemTemplateSubItemRespDTO">
        SELECT
        i.guid,
        s.guid sku_guid,
        i.`name`,
        s.`name` sku_name,
        s.unit,
        t.guid type_guid,
        t.`name` type_name,
        s.sale_price
        FROM
        hsi_item i
        INNER JOIN hsi_sku s ON i.guid = s.item_guid
        INNER JOIN hsi_type t ON t.guid = i.type_guid
        WHERE
        i.store_guid = #{dto.storeGuid}
        AND i.is_delete = 0
        AND i.is_enable= 1
        AND t.is_delete = 0
        AND s.is_delete = 0
        AND s.is_enable=1
        <if test="dto.typeGuid !=null and dto.typeGuid !=''">
            AND i.type_guid = #{dto.typeGuid}
        </if>
        <if test="dto.keywords !=null and dto.keywords !=''">
            AND i.name like CONCAT('%',#{dto.keywords},'%')
        </if>
        ORDER BY
        CONVERT(i.`name` USING gbk)

    </select>
    <select id="getItemTemplateGuid" resultType="java.lang.String">
        SELECT
        m.guid
        FROM
        hsi_item i
        INNER JOIN hsi_sku s ON i.guid = s.item_guid
        LEFT JOIN hsi_item_t_menu_subitem m ON s.guid = m.sku_guid
        WHERE
        m.is_delete = 0
        AND i.is_delete = 0
        AND s.is_delete = 0
        AND i.guid IN
        <foreach collection="list" index="index" item="guid" open="(" separator="," close=")">
            #{guid}
        </foreach>
    </select>


    <select id="getJournalingItem" resultMap="JournalingItemsQuery">
        SELECT a.guid,
               b.brand_guid,
               a.item_from,
               a.parent_guid,
               a.`name`
        FROM hsi_item AS a
                 LEFT JOIN hsi_item b ON a.parent_guid = b.guid
        WHERE a.item_from = 2
          and b.brand_guid IS NOT NULL
        UNION ALL
        SELECT guid,
               brand_guid,
               item_from,
               parent_guid,
               `name`
        FROM hsi_item
        WHERE item_from = 0
    </select>

    <resultMap id="GroupMealSubItemBO" type="com.holderzone.saas.store.item.entity.bo.GroupMealSubItemBO">
        <result column="guid" property="guid"/>
        <result column="item_guid" property="itemGuid"/>
        <result column="type_name" property="typeName"/>
        <result column="type_guid" property="typeGuid"/>
        <result column="sort" property="sort"/>
        <result column="sub_item_guid" property="subItemGuid"/>
        <result column="sub_item_type" property="subItemType"/>
        <result column="sub_sku_guid" property="subSkuGuid"/>
        <result column="item_name" property="itemName"/>
        <result column="sku_name" property="skuName"/>
        <result column="sale_price" property="salePrice"/>
        <result column="cost_price" property="costPrice"/>
        <result column="sub_sku_sale_price" property="subSkuSalePrice"/>
        <result column="sub_sku_cost_price" property="subSkuCostPrice"/>
        <result column="num" property="num"/>
        <result column="unit" property="unit"/>
    </resultMap>
    <select id="getGroupMealSubItemS" resultMap="GroupMealSubItemBO">
        SELECT gm.guid,
               gm.item_guid,
               t.`guid`              as type_guid,
               t.`name`              as type_name,
               gm.sort,
               gm.sub_item_guid,
               gm.sku_guid           as sub_sku_guid,
               i.item_type           as sub_item_type,
               i.`name`              as item_name,
               s.`name`              as sku_name,
               s.sale_price          as sub_sku_sale_price,
               s.cost_price          as sub_sku_cost_price,
               s.sale_price * gm.num as sale_price,
               s.cost_price * gm.num as cost_price,
               gm.num,
               s.unit
        FROM hsi_group_meal gm
                 INNER JOIN hsi_item i ON gm.sub_item_guid = i.guid
                 INNER JOIN hsi_sku s ON gm.sku_guid = s.guid
                 LEFT JOIN hsi_type t ON i.type_guid = t.guid
        WHERE gm.is_delete = 0
          AND s.is_delete = 0
          AND i.is_delete = 0
          AND gm.item_guid = #{itemGuid};
    </select>


    <select id="getListGroupMealSubItemS" resultMap="GroupMealSubItemBO">
        SELECT
        gm.guid,
        gm.item_guid,
        t.`guid` as type_guid,
        t.`name` as type_name,
        gm.sort,
        gm.sub_item_guid,
        gm.sku_guid as sub_sku_guid,
        i.item_type as sub_item_type,
        i.`name` as item_name,
        s.`name` as sku_name,
        s.sale_price as sub_sku_sale_price,
        s.cost_price as sub_sku_cost_price,
        s.sale_price * gm.num as sale_price,
        s.cost_price * gm.num as cost_price,
        gm.num,
        s.unit
        FROM
        hsi_group_meal gm
        INNER JOIN hsi_item i ON gm.sub_item_guid = i.guid
        INNER JOIN hsi_sku s ON gm.sku_guid = s.guid
        LEFT JOIN hsi_type t ON i.type_guid = t.guid
        WHERE
        gm.is_delete = 0
        AND s.is_delete = 0
        AND i.is_delete = 0
        AND gm.item_guid in
        <foreach item="item" collection="list" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <resultMap id="itemBatchImportTempRespDTO"
               type="com.holderzone.saas.store.dto.item.resp.ItemBatchImportTempRespDTO">
        <result column="type_name" property="typeName"/>
        <result column="item_name" property="itemName"/>
        <result column="item_type" property="itemType"/>
        <result column="min_order_num" property="minOrderNum"/>
        <result column="unit" property="unit"/>
        <result column="sale_price" property="salePrice"/>
        <result column="cost_price" property="costPrice"/>
        <result column="is_whole_discount" property="isWholeDiscount"/>
        <result column="member_price" property="memberPrice"/>
        <result column="is_rack" property="isRack"/>
        <result column="is_join_we_chat" property="isJoinWechat"/>
        <result column="is_join_buffet" property="isJoinBuffet"/>
        <result column="code" property="code"/>
        <result column="upc" property="upc"/>
        <result column="picture_url" property="pictureUrl"/>
        <result column="is_open_stock" property="isOpenStock"/>
    </resultMap>

    <select id="getItemsBeforeImport" resultMap="itemBatchImportTempRespDTO">
        SELECT
        t.`name` as type_name,
        i.`name` as item_name,
        i.item_type item_type,
        s.min_order_num,
        s.unit,
        s.sale_price,
        s.cost_price,
        s.is_whole_discount,
        s.member_price,
        s.is_rack,
        s.is_join_we_chat,
        s.is_join_buffet,
        s.`code`,
        s.upc,
        i.picture_url,
        s.is_open_stock
        FROM
        hsi_item i
        INNER JOIN hsi_sku s ON i.guid = s.item_guid
        INNER JOIN hsi_type t ON t.guid = i.type_guid
        WHERE
        i.is_delete = 0
        AND s.is_delete = 0
        AND t.is_delete = 0
        AND i.`name` in
        <foreach item="item" collection="dto.itemNames" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="dto.flag == 1">
            AND i.brand_guid = #{dto.guid}
        </if>
        <if test="dto.flag == 0">
            AND i.store_guid = #{dto.guid}
        </if>
    </select>

    <update id="updateHasAttr">
        UPDATE hsi_item
        SET has_attr = #{hasAttr}
        WHERE guid = #{itemGuid}
    </update>
    <update id="voteUp">
        UPDATE hsi_item
        SET up_count=up_count + 1
        WHERE guid = #{guid}
          and up_count is not null
          and is_delete = 0;
    </update>
    <update id="voteDown">
        UPDATE hsi_item
        SET down_count=down_count + 1
        WHERE guid = #{guid}
          and down_count is not null
          and is_delete = 0;
    </update>

    <update id="updateOriginItemStoreGuid">
        UPDATE hsi_item
        SET store_guid=null
        WHERE guid = #{itemGuid}
          AND brand_guid = #{brandGuid}
          AND store_guid = #{storeGuid}
          AND item_from = 1;
    </update>

    <update id="correctStoreItemIsEnable">
        update hsi_item set is_enable=0 where
        parent_guid!=guid
        and
        item_from=2
        <if test='itemGuidList!=null and  itemGuidList.size()>0'>
            and
            parent_guid not in
            <foreach collection="itemGuidList" index="index" item="itemGuid" open="(" separator="," close=")">
                #{itemGuid}
            </foreach>
        </if>
        and store_guid
        in
        <foreach collection="storeGuidList" index="index" item="storeGuid" open="(" separator="," close=")">
            #{storeGuid}
        </foreach>
        and is_delete=0
    </update>

    <update id="correctStoreSkuIsEnable">
        update hsi_sku set is_enable=0 where
        parent_guid!=guid
        and
        sku_from=2
        <if test='skuGuidList!=null and  skuGuidList.size()>0'>
            and
            parent_guid not in
            <foreach collection="skuGuidList" index="index" item="skuGuid" open="(" separator="," close=")">
                #{skuGuid}
            </foreach>
        </if>
        and store_guid
        in
        <foreach collection="storeGuidList" index="index" item="storeGuid" open="(" separator="," close=")">
            #{storeGuid}
        </foreach>
        and is_delete=0
    </update>
    <update id="correctStoreItemIsEnableByBrandPush">
        update hsi_item set is_enable=0 where
        parent_guid!=guid
        and
        item_from=2
        <if test='itemGuidList!=null and  itemGuidList.size()>0'>
            and
            parent_guid in
            <foreach collection="itemGuidList" index="index" item="itemGuid" open="(" separator="," close=")">
                #{itemGuid}
            </foreach>
        </if>
        and store_guid
        not in
        <foreach collection="storeGuidList" index="index" item="storeGuid" open="(" separator="," close=")">
            #{storeGuid}
        </foreach>
        and is_delete=0
    </update>

    <update id="correctStoreSkuIsEnableByBrandPush">
        update hsi_sku set is_enable=0 where
        parent_guid!=guid
        and
        sku_from=2
        <if test='skuGuidList!=null and  skuGuidList.size()>0'>
            and
            parent_guid in
            <foreach collection="skuGuidList" index="index" item="skuGuid" open="(" separator="," close=")">
                #{skuGuid}
            </foreach>
        </if>
        and store_guid
        not in
        <foreach collection="storeGuidList" index="index" item="storeGuid" open="(" separator="," close=")">
            #{storeGuid}
        </foreach>
        and is_delete=0
    </update>
    <update id="correctStoreItemByPlanPriceGuid">
        update hsi_item set is_enable=0 where
        parent_guid!=guid
        and
        item_from=2
        <if test='storeGuidList!=null and  storeGuidList.size()>0'>
            and store_guid
            not in
            <foreach collection="storeGuidList" index="index" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
        and is_delete=0
        AND price_plan_guid=#{planPriceGuid}
    </update>
    <update id="correctStoreSkuByPlanPriceGuid">
        update hsi_sku set is_enable=0 where
        parent_guid!=guid
        and
        sku_from=2
        <if test='storeGuidList!=null and  storeGuidList.size()>0'>
            and store_guid
            not in
            <foreach collection="storeGuidList" index="index" item="storeGuid" open="(" separator="," close=")">
                #{storeGuid}
            </foreach>
        </if>
        and is_delete=0
        AND price_plan_guid=#{planPriceGuid}
    </update>
    <update id="updateStoreItemByPlanPriceGuid">
        update hsi_item set price_plan_guid = #{planPriceGuid} where
        parent_guid!=guid
        and
        item_from=2
        <if test='itemGuidList!=null and  itemGuidList.size()>0'>
            and
            parent_guid in
            <foreach collection="itemGuidList" index="index" item="itemGuid" open="(" separator="," close=")">
                #{itemGuid}
            </foreach>
        </if>
        and store_guid
        in
        <foreach collection="storeGuidList" index="index" item="storeGuid" open="(" separator="," close=")">
            #{storeGuid}
        </foreach>
        and is_delete=0
    </update>


    <select id="getHasAttrItemGuid" resultType="java.lang.String">
        SELECT t1.guid,
               count(t2.guid) AS cnt
        FROM (
                 SELECT guid
                 FROM hsi_item
                 WHERE has_attr != 0
                AND is_delete = 0
             ) t1
                 LEFT JOIN (
            SELECT *
            FROM hsi_r_attr_item_attr_group
            WHERE is_delete = 0
        ) t2 ON t1.guid = t2.guid
        GROUP BY t1.guid
        HAVING cnt = 0
    </select>

    <select id="getItemInfoListByParentId"
            resultType="java.util.Map">
        SELECT * from hsi_item WHERE parent_guid = #{parentItemGuid}
        <if test="storeGuid!=null and storeGuid!=''">
            AND store_guid = #{storeGuid}
        </if>
        AND item_from = 2
    </select>

    <select id="getGuidByStoreGuidAndParentGuid" resultType="java.lang.String">
        SELECT guid, item_type
        FROM hsi_item
        WHERE store_guid = #{storeGuid}
          AND parent_guid = #{parentGuid}
    </select>

    <select id="getItemsByStoreGuid" resultType="java.util.Map">
        SELECT guid
        FROM hsi_item
        WHERE store_guid = #{storeGuid}
          AND item_from = 2
          AND is_delete = 0;
    </select>
    <select id="selectItemTypeGuidList" resultType="com.holderzone.saas.store.item.entity.domain.TypeDO">
        select type_guid as guid,count(*) as item_num from
        hsi_item
        where
        guid in
        <foreach collection="itemGuidList" index="index" item="itemGuid" open="(" separator="," close=")">
            #{itemGuid}
        </foreach>
        and
        is_delete=0

        group by type_guid
        ;
    </select>
    <select id="selectStoreGuidListByPackageChildItemGuid" resultType="java.lang.String">
        select distinct store_guid
        from hsi_item
        where parent_guid in (
            select item_guid
            from hsi_subgroup
            where guid in (
                select subgroup_guid
                from hsi_r_sku_subgroup
                where item_guid = #{itemGuid}
            )
        )
          and is_delete = 0
          and is_enable = 1
          and item_from = 2;
    </select>
    <select id="selectPackageNameListByPackageChildItemGuid" resultType="java.lang.String">
        select distinct name from hsi_item where store_guid in
        <foreach collection="storeGuids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and parent_guid in (
        select item_guid from hsi_subgroup where guid in (
        select subgroup_guid from hsi_r_sku_subgroup
        where item_guid=#{itemGuid} and is_delete = 0
        ) and is_delete = 0
        ) and is_delete=0 and is_enable=1;
    </select>
    <select id="selectItemWithParentGuidList" resultType="com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO">
        select guid as item_guid,name,parent_guid from
        hsi_item
        where
        guid in
        <foreach collection="itemGuidList" index="index" item="itemGuid" open="(" separator="," close=")">
            #{itemGuid}
        </foreach>
        and
        is_delete=0
        and
        is_enable=1
    </select>
    <select id="wxSearchItems" resultType="com.holderzone.saas.store.item.entity.domain.ItemDO">
        SELECT DISTINCT it.guid,it.type_guid,it.item_type,it.is_sold_out,it.has_attr,it.`name`,
        it.pinyin,it.name_abbr,it.sort,it.description,it.picture_url,it.is_bestseller,it.is_new,it.is_sign
        FROM hsi_sku sku JOIN hsi_item it ON it.guid = sku.item_guid
        WHERE sku.store_guid = #{dto.storeGuid} AND sku.is_enable = 1 AND sku.is_rack = 1
        AND sku.is_join_we_chat = 1 AND it.is_delete = 0 AND it.is_enable = 1 AND it.item_type in (1,2,4)
        <!--关键字拆分搜索-->
        <if test="dto.keywords != null and dto.keywords != '' ">
            AND it.`name` LIKE '${dto.keywords}'
        </if>
    </select>

    <resultMap id="selectItemMap" type="com.holderzone.saas.store.dto.item.resp.SelectItemDTO">
        <result column="guid" jdbcType="VARCHAR" property="thirdNo"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type_guid" jdbcType="VARCHAR" property="typeGuid"/>
        <result column="store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="brand_guid" jdbcType="VARCHAR" property="brandGuid"/>
        <result column="item_from" jdbcType="VARCHAR" property="itemFrom"/>
        <result column="item_type" jdbcType="VARCHAR" property="itemType"/>
        <result column="pinyin" jdbcType="VARCHAR" property="pinyin"/>
        <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl"/>
        <result column="item_code" jdbcType="TINYINT" property="itemCode"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <collection property="itemSkuDOList" ofType="com.holderzone.saas.store.dto.item.resp.SelectItemSkuDTO">
            <result column="sku_third_no" property="thirdNo"/>
            <result column="sku_name" property="name"/>
            <result column="sku_upc" property="upc"/>
            <result column="sku_code" property="code"/>
            <result column="sku_sale_price" property="salePrice"/>
            <result column="sku_unit" property="unit"/>
            <result column="sku_sku_from" property="skuFrom"/>
        </collection>
    </resultMap>

    <select id="selectItemList" resultMap="selectItemMap"
            parameterType="com.holderzone.saas.store.dto.item.req.ItemQueryDTO">
        SELECT
        i.guid third_no,
        i.gmt_create create_time,
        i.gmt_modified update_time,
        i.NAME,
        i.type_guid,
        i.store_guid,
        i.brand_guid,
        i.item_from,
        i.item_type,
        i.pinyin,
        i.picture_url,
        i.`code` item_code,
        i.description,
        isku.guid AS sku_third_no,
        isku.NAME AS sku_name,
        isku.upc AS sku_upc,
        isku.CODE AS sku_code,
        isku.sale_price AS sku_sale_price,
        isku.unit AS sku_unit,
        isku.sku_from AS sku_sku_from
        FROM
        hsi_item i
        LEFT JOIN hsi_sku isku ON i.guid = isku.item_guid
        <where>
            <if test="brandGuid != null and brandGuid != null">
                and i.brand_guid = #{brandGuid}
            </if>
            <if test="storeGuid != null and storeGuid != null">
                and i.store_guid = #{storeGuid}
            </if>
            <if test="startTime != null and endTime != null">
                and i.create_time between #{startTime} and #{endTime}
            </if>
            <if test="enterpriseGuid != null and enterpriseGuid != ''">
                and i.enterprise_guid = #{enterpriseGuid}
            </if>
        </where>
    </select>

    <select id="selectExportItemList" parameterType="com.holderzone.saas.store.dto.item.req.ItemExportReqDTO"
            resultType="com.holderzone.saas.store.dto.item.resp.ItemExportRespDTO">
        SELECT
        t.`name` as `typeName`,
        it.guid as itemGuid,
        it.`name` as name,
        ( CASE it.item_type WHEN 3 THEN '计重' ELSE '计数' END ) `itemType`,
        sku.unit as skuUnit,
        sku.sale_price as skuSalePrice,
        sku.cost_price as skuCostPrice,
        ( CASE sku.is_whole_discount WHEN 1 THEN '是' ELSE '否' END ) as isWholeDiscount,
        sku.`code` as skuCode,
        sku.upc as skuUpc,
        ( CASE sku.is_open_stock WHEN 1 THEN '是' ELSE '否' END ) isOpenStock
        FROM
        hsi_item it
        LEFT JOIN hsi_sku sku ON it.guid = sku.item_guid
        LEFT JOIN hsi_type t ON it.type_guid = t.guid
        WHERE
        it.is_delete = 0
        AND it.is_enable = 1
        and  t.store_guid = #{storeGuid}
        order by it.gmt_create desc
    </select>

    <select id="findByGuids" resultType="com.holderzone.saas.store.item.entity.domain.ItemDO"
            parameterType="com.holderzone.saas.store.dto.item.req.ItemQueryDTO">
        SELECT
        *
        FROM
        hsi_item
        where
        guid in
        <foreach collection="itemGuidList" index="index" item="itemGuid" open="(" separator="," close=")">
            #{itemGuid}
        </foreach>
    </select>

    <select id="listRackItemByType" resultType="com.holderzone.saas.store.item.entity.domain.ItemDO">
        SELECT DISTINCT
            i.guid,
            i.type_guid
        FROM
            hsi_item i
                LEFT JOIN hsi_sku s ON s.item_guid = i.guid
        WHERE
            i.is_delete = 0
            AND s.is_delete = 0
            AND s.is_rack = #{isRack}
            AND i.type_guid IN
            <foreach collection="typeGuidList" index="index" item="typeGuid" open="(" separator="," close=")">
                #{typeGuid}
            </foreach>
    </select>
    <select id="countByGuidSet" resultType="java.lang.Integer">
        SELECT count(1)
        FROM
        hsi_item
        where guid in
        <foreach collection="itemGuidList" index="index" item="guid" open="(" separator="," close=")">
            #{guid}
        </foreach>
    </select>
    <select id="listBaseItem" resultType="com.holderzone.saas.store.dto.item.common.ItemDTO">
        select
        hi.guid,
        hi.name,
        hi.item_type as itemType,
        hi.type_guid,
        ht.name as typeName
        from
            hsi_item hi
            left join hsi_type ht on hi.type_guid = ht.guid
        where
        hi.guid in
        <foreach collection="itemGuidList" index="index" item="guid" open="(" separator="," close=")">
            #{guid}
        </foreach>
    </select>
    <select id="getSubItemGuid" resultType="string">
        select guid
        from hsi_item
        where parent_guid = #{parentGuid}
        and store_guid = #{storeGuid}
        limit 1
    </select>

    <select id="getItemNameList" resultType="com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO">
        SELECT
        guid as itemGuid,
        name as name,
        brand_guid as brandGuid,
        type_guid as typeGuid
        FROM
        hsi_item
        where
        guid in
        <foreach collection="req.dataList" index="index" item="itemGuid" open="(" separator="," close=")">
            #{itemGuid}
        </foreach>
    </select>
</mapper>
