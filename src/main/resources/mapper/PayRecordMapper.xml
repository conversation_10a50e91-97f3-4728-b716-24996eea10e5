<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.pay.mapper.PayRecordMapper">

    <resultMap id="QuickPayStatistics" type="com.holderzone.saas.store.dto.pay.QuickPayStatisticsRespDTO">
        <result column="pay_power_id" property="payPowerId"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="amountTotal" property="amountTotal"/>
        <result column="orderCount" property="orderCount"/>
        <result column="guestCount" property="guestCount"/>
        <result column="payDate" property="payDate"/>
    </resultMap>

    <select id="getStatisticsTotalAmount" resultType="java.math.BigDecimal">
        select
            IFNULL( sum(amount), 0 )
        from
            hsp_pay_record
        <where>
            pay_st = 2
            <if test="basePageDTO.maxId != null">
                and id &lt;= #{basePageDTO.maxId}
            </if>
            <if test="basePageDTO.storeGuid != null and basePageDTO.storeGuid != ''">
                and store_guid = #{basePageDTO.storeGuid}
            </if>
            <if test="startTime != null">
                and gmt_time_paid >= #{startTime}
            </if>
        </where>
    </select>

    <select id="queryQuickPayStatistics" resultMap="QuickPayStatistics">
        SELECT
            pay_power_id,
            store_guid,
            DATE_FORMAT(gmt_time_paid, '%Y-%m-%d') as payDate,
            SUM( amount ) AS amountTotal,
            COUNT( DISTINCT order_guid ) AS orderCount,
            COUNT( DISTINCT order_guid ) AS guestCount
        FROM
            `hsp_pay_record`
        WHERE
            pay_st = 2
            <if test="request.storeGuidList != null and request.storeGuidList.size() > 0">
                AND store_guid IN
                <foreach item="storeGuid" collection="request.storeGuidList" index="index" open="(" separator=","
                         close=")">
                    #{storeGuid}
                </foreach>
            </if>
            AND gmt_time_paid BETWEEN #{request.startDateTime}
            AND #{request.endDateTime}
        GROUP BY
            store_guid,
            pay_power_id,
            DATE_FORMAT(gmt_time_paid, '%Y-%m-%d')
    </select>

</mapper>
