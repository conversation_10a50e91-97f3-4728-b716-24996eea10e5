<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.member.mapper.MemberCardMapper">
  <resultMap id="BaseResultMap" type="com.holderzone.saas.store.member.domain.MemberCardDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="member_card_guid" jdbcType="VARCHAR" property="memberCardGuid" />
    <result column="member_guid" jdbcType="VARCHAR" property="memberGuid" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="type" jdbcType="TINYINT" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    id, gmt_create, gmt_modified, is_delete, member_card_guid, member_guid, num, `type`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hsm_member_card
    where member_card_guid = #{memberCardGuid,jdbcType=VARCHAR}
  </select>
    <select id="getElectronCardNum" resultType="java.lang.String">
      select
      num
      from hsm_member_card
      where member_guid = #{memberGuid,jdbcType=VARCHAR}
      and `type` = 0
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from hsm_member_card
     where member_card_guid = #{memberCardGuid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.holderzone.saas.store.member.domain.MemberCardDO">
    insert into hsm_member_card (id, gmt_create, gmt_modified, 
      is_delete, member_card_guid, member_guid, 
      num, `type`)
    values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=BIT}, #{memberCardGuid,jdbcType=VARCHAR}, #{memberGuid,jdbcType=VARCHAR}, 
      #{num,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.holderzone.saas.store.member.domain.MemberCardDO">
    insert into hsm_member_card
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="memberCardGuid != null">
        member_card_guid,
      </if>
      <if test="memberGuid != null">
        member_guid,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="type != null">
        `type`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="memberCardGuid != null">
        #{memberCardGuid,jdbcType=VARCHAR},
      </if>
      <if test="memberGuid != null">
        #{memberGuid,jdbcType=VARCHAR},
      </if>
      <if test="num != null and num!=''">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.holderzone.saas.store.member.domain.MemberCardDO">
    update hsm_member_card
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="memberCardGuid != null">
        member_card_guid = #{memberCardGuid,jdbcType=VARCHAR},
      </if>
      <if test="memberGuid != null">
        member_guid = #{memberGuid,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
    </set>
    where   member_card_guid = #{memberCardGuid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.holderzone.saas.store.member.domain.MemberCardDO">
    update hsm_member_card
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      member_card_guid = #{memberCardGuid,jdbcType=VARCHAR},
      member_guid = #{memberGuid,jdbcType=VARCHAR},
      num = #{num,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=TINYINT}
    where member_card_guid = #{memberCardGuid,jdbcType=VARCHAR}
  </update>




  <resultMap id="BaseCardMap" type="com.holderzone.saas.store.member.domain.MemberCardDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="member_card_guid" jdbcType="VARCHAR" property="memberCardGuid" />
    <result column="member_guid" jdbcType="VARCHAR" property="memberGuid" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="type" jdbcType="TINYINT" property="type" />
  </resultMap>

  <sql id="Base_CardColumn_List">
    id, gmt_create, gmt_modified, is_delete, member_card_guid, member_guid, num, `type`
  </sql>

  <select id="getMemberCards"  resultMap="BaseCardMap">
    select
    <include refid="Base_CardColumn_List" />
    from hsm_member_card
    where member_guid= #{memberCardsReqDTO.memberGuid} and is_delete=0
    <if test="memberCardsReqDTO.num!=null and memberCardsReqDTO.num!=''">
      and num like concat(concat('%',#{memberCardsReqDTO.num}),'%')

    </if>
  </select>

  <select id="getMemberEleCard"  resultMap="BaseCardMap">
    select
    <include refid="Base_CardColumn_List" />
    from hsm_member_card
    where member_guid= #{memberGuid,jdbcType=VARCHAR} and is_delete=0
    and type=0
  </select>


</mapper>