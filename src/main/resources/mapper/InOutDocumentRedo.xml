<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.InOutDocumentRedoMapper">


  <insert id="insertInOutDocumentRedo" parameterType="com.holderzone.erp.entity.domain.InOutDocumentRedoDO">
      insert into hse_warehouse_in_out_document_redo (guid,`data`)
      values(#{guid},#{data})
  </insert>
</mapper>