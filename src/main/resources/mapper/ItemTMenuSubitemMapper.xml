<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.ItemTMenuSubitemMapper">

    <resultMap id="ItemTemplateMenuDetailQuery" type="com.holderzone.saas.store.item.entity.query.ItemTemplateMenuDetailQuery">
        <result column="guid" property="guid"/>
        <result column="is_it_full_time" property="isItFullTime"/>
        <result column="menu_guid" property="menuGuid"/>
        <result column="periodic_mode" property="periodicMode"/>
        <result column="weeks" property="weeks"/>
        <result column="times" property="times"/>
    </resultMap>


    <resultMap id="ItemTemplateMenuSubItemDetailQuery" type="com.holderzone.saas.store.item.entity.query.ItemTemplateMenuSubItemDetailQuery">
        <result column="guid" property="guid"/>
        <result column="menu_guid" property="menuGuid"/>
        <result column="item_name" property="name"/>
        <result column="sku_name" property="skuName"/>
        <result column="sku_guid" property="skuGuid"/>
        <result column="unit" property="unit"/>
        <result column="type_guid" property="typeGuid"/>
        <result column="type_name" property="typeName"/>
        <result column="sale_price" property="salePrice"/>
        <result column="price" property="price"/>
    </resultMap>

    <select id="getItemTemplateMenuDetail" resultMap="ItemTemplateMenuDetailQuery">
       SELECT
           distinct v.guid,
			v.is_it_full_time,
            m.guid menu_guid,
            v.periodic_mode,
            v.`weeks`,
            v.times
        FROM
            hsi_item_t_menu m
        INNER JOIN hsi_itme_t_menu_validity v ON m.guid = v.item_menu_guid
        WHERE
           m.guid = #{dto.guid}
        AND m.is_delete = 0
        AND v.is_delete = 0
    </select>


    <select id="getItemTemplateMenuSubItemDetailQuery"  resultMap="ItemTemplateMenuSubItemDetailQuery">
        SELECT
        m.guid menu_guid,
        ms.guid guid,
        i.`name` item_name,
        s.`name` sku_name,
        s.guid sku_guid,
        s.unit,
        t.guid type_guid,
        t.`name` type_name,
        s.sale_price,
        ms.price price
        FROM
        hsi_item_t_menu m
        INNER JOIN hsi_item_t_menu_subitem ms ON m.guid = ms.item_menu_guid
        INNER JOIN hsi_sku s ON ms.sku_guid = s.guid
        INNER JOIN hsi_item i ON s.item_guid = i.guid
        INNER JOIN hsi_type t ON i.type_guid = t.guid
        <where>
            m.guid = #{dto.guid}
            and ms.is_delete = 0
            <if test="dto.keywords != null and dto.keywords !=''">
                AND i. NAME LIKE CONCAT('%', #{dto.keywords}, '%')
            </if>
            <if test="dto.typeGuid != null and dto.typeGuid !=''">
                AND t.guid = #{dto.typeGuid}
            </if>
        </where>
        ORDER BY
        CONVERT(i.`name` USING gbk)
    </select>
    <select id="getStoreGuid" resultType="java.lang.String">
            SELECT
                    t.store_guid
                FROM
                    hsi_item_t_menu_subitem s
                INNER JOIN hsi_item_t_menu m
                ON s.item_menu_guid = m.guid
                INNER JOIN hsi_item_template t
                ON m.template_guid = t.guid
                WHERE
                    s.guid = #{guid}
    </select>


</mapper>
