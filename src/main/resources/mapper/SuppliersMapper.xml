<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.SuppliersMapper">

    <sql id="baseResult">
        `guid`,`name`,`office_tel`,`contact_name`,`contact_tel`,`addr`,`settlement_interval`,`remark`,
        `enabled`,`deleted`,`foreign_key`,`enterprise_guid`,`gmt_create`,`gmt_modified`
    </sql>

    <resultMap id="SuppliersMap" type="com.holderzone.erp.entity.domain.SuppliersDO">
        <result column="guid" property="guid"/>
        <result column="name" property="name"/>
        <result column="office_tel" property="officeTel"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_tel" property="contactTel"/>
        <result column="addr" property="addr"/>
        <result column="settlement_interval" property="settlementInterval"/>
        <result column="remark" property="remark"/>
        <result column="enabled" property="enabled"/>
        <result column="deleted" property="deleted"/>
        <result column="foreign_key" property="foreignKey"/>
        <result column="enterprise_guid" property="enterpriseGuid"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <insert id="createSuppliers" parameterType="com.holderzone.erp.entity.domain.SuppliersDO">
        INSERT INTO hse_suppliers
        (
        <trim suffixOverrides=",">
            `guid`,
            `name`,
            `foreign_key`,
            <if test="officeTel != null and officeTel != ''">
                `office_tel`,
            </if>
            <if test="contactName != null and contactName != ''">
                `contact_name`,
            </if>
            <if test="contactTel != null and contactTel != ''">
                `contact_tel`,
            </if>
            <if test="addr != null and addr != ''">
                `addr`,
            </if>
            <if test="settlementInterval != null and settlementInterval != ''">
                `settlement_interval`,
            </if>
            <if test="remark != null and remark != ''">
                `remark`,
            </if>
            <if test="enabled != null">
                `enabled`,
            </if>
            <if test="enterpriseGuid != null and enterpriseGuid != ''">
                `enterprise_guid`,
            </if>
        </trim>
        ) VALUES 
        (
        <trim suffixOverrides=",">
            #{guid},
            #{name},
            #{foreignKey},
            <if test="officeTel != null and officeTel != ''">
                #{officeTel},
            </if>
            <if test="contactName != null and contactName != ''">
                #{contactName},
            </if>
            <if test="contactTel != null and contactTel != ''">
                #{contactTel},
            </if>
            <if test="addr != null and addr != ''">
                #{addr},
            </if>
            <if test="settlementInterval != null and settlementInterval != ''">
                #{settlementInterval},
            </if>
            <if test="remark != null and remark != ''">
                #{remark},
            </if>
            <if test="enabled != null">
                #{enabled},
            </if>
            <if test="enterpriseGuid != null and enterpriseGuid != ''">
                #{enterpriseGuid},
            </if>
        </trim>
        )
    </insert>

    <update id="updateSuppliers" parameterType="com.holderzone.erp.entity.domain.SuppliersDO">
        update hse_suppliers
        set
        <trim suffixOverrides=",">
            `name` = #{name},
            `foreign_key` = #{foreignKey},
            `office_tel` = #{officeTel},
            `contact_name` = #{contactName},
            `contact_tel` = #{contactTel},
            `addr` = #{addr},
            `settlement_interval` = #{settlementInterval},
            `remark` = #{remark},
            `enabled` = #{enabled},
            <if test="enterpriseGuid != null and enterpriseGuid != ''">
                `enterprise_guid` = #{enterpriseGuid},
            </if>
        </trim>
        where `guid` = #{guid}
    </update>

    <update id="enableOrDisableSuppliers" parameterType="java.lang.String">
        update hse_suppliers
        set
        enabled = if(enabled = 1, 0, 1)
        where guid = #{guid}
    </update>

    <update id="deleteSuppliers" parameterType="java.lang.String">
        update hse_suppliers
        set
        deleted = 1
        where guid = #{guid}
    </update>

    <select id="getSuppliersByGuid" resultMap="SuppliersMap">
        select
        <include refid="baseResult"/>
        from hse_suppliers
        where guid = #{guid}
    </select>

    <select id="getSuppliersList" resultMap="SuppliersMap" parameterType="com.holderzone.erp.entity.domain.SuppliersQueryDO">
        SELECT
        <include refid="baseResult"/>
        from hse_suppliers
        <where>
            deleted = 0
            <if test="enabled != -1">
                and enabled = #{enabled}
            </if>
            <if test="foreignKey != null and foreignKey != ''">
                and foreign_key = #{foreignKey}
            </if>
            <if test="searchConditions != null and searchConditions != ''">
                and
                (
                LOCATE(#{searchConditions},`name`)
                or LOCATE(#{searchConditions},`contact_name`)
                or LOCATE(#{searchConditions},`contact_tel`)
                )
            </if>
        </where>
        order by gmt_create desc
        <if test="start != -1">
            limit #{start}, #{pageSize}
        </if>
    </select>

    <select id="getSuppliersListTotal" resultType="java.lang.Long" parameterType="com.holderzone.erp.entity.domain.SuppliersQueryDO">
        SELECT
        count(*)
        from hse_suppliers
        <where>
            deleted = 0
            <if test="enabled != -1">
                and enabled = #{enabled}
            </if>
            <if test="foreignKey != null and foreignKey != ''">
                and foreign_key = #{foreignKey}
            </if>
            <if test="searchConditions != null and searchConditions != ''">
                and
                (
                LOCATE(#{searchConditions},`name`)
                or LOCATE(#{searchConditions},`contact_name`)
                or LOCATE(#{searchConditions},`contact_tel`)
                )
            </if>
        </where>
    </select>

    <select id="verifyNameRepeat" resultType="java.lang.Integer">
        SELECT
        count(*)
        from hse_suppliers
        where `name` = #{name}
        and foreign_key = #{foreignKey}
        <if test="guid != null and guid != ''">
            and guid not in(#{guid})
        </if>
    </select>

    <select id="getSuppliersMaterialListAll" resultMap="CategoryDOMap">
        select
        hm.`name`, hm.guid, hm.unit, hm.auxiliary_unit, hm.count, hmc.guid categoryGuid, hmc.`name` categoryName
        from hse_suppliers_pricing_schemes hsps
        left join hse_material hm on hsps.material_guid = hm.guid
        left join hse_material_category hmc on hm.category = hmc.guid
        <where>
            hsps.deleted = 0 and hsps.enabled = 1
            and suppliers_guid = #{suppliersGuid}
            <if test="searchName != null and searchName != ''">
                and locate(#{searchName}, hm.`name`)
            </if>
        </where>
    </select>
    <select id="selectSupplierStatus" resultMap="SuppliersMap" parameterType="string">
        select guid,enabled,deleted from hse_suppliers where guid = #{supplierGuid}
    </select>

    <resultMap id="CategoryDOMap" type="com.holderzone.erp.entity.domain.CategoryDO">
        <result column="categoryGuid" property="categoryGuid"/>
        <result column="categoryName" property="categoryName"/>
        <collection property="materialDOList" ofType="com.holderzone.erp.entity.domain.MaterialDO">
            <result column="name" property="name"/>
            <result column="guid" property="guid"/>
            <result column="unit" property="unit"/>
            <result column="auxiliary_unit" property="auxiliaryUnit"/>
            <result column="count" property="count"/>
        </collection>
    </resultMap>

</mapper>