<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.AppendFeeMapper">

    <delete id="deleteAppendByIds" parameterType="String">
        delete from hst_append_fee where guid in
        <foreach item="id" collection="guids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
