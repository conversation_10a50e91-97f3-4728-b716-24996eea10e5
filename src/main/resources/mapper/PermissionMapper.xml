<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.staff.mapper.PermissionMapper">

    <!-- 根据roleGuid删除权限 -->
    <delete id="deletePermission" parameterType="java.lang.String">
        DELETE FROM hss_permission
        WHERE `role_guid` = #{roleGuid}
    </delete>

    <!-- 根据roleGuid查询权限 -->
    <select id="selectPermissionByRoleGuid" parameterType="java.lang.String"
            resultType="com.holderzone.saas.store.staff.entity.domain.PermissionDO">
        SELECT
        <include refid="selectColumns"/>
        FROM hss_permission
        WHERE `role_guid` = #{roleGuid}
    </select>

    <!-- 根据roleGuid数组查询权限 -->
    <select id="selectPermissionByRoleGuidList" parameterType="java.util.List"
            resultType="com.holderzone.saas.store.staff.entity.domain.PermissionDO">
        SELECT
        <include refid="selectColumns"/>
        FROM hss_permission
        <where>
            <if test="list.size()>0">
                `role_guid` IN
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 批量新增 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO hss_permission (
        `role_guid`,
        `permission_code`,
        `permission_name`,
        `remark`,
        `create_user_guid`,
        `update_user_guid`
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.roleGuid}, #{item.permissionCode}, #{item.permissionName}, #{item.remark}, #{item.createUserGuid}, #{item.updateUserGuid})
        </foreach>
    </insert>

    <sql id="selectColumns">
        `role_guid` AS roleGuid,
        `permission_code` AS permissionCode,
        `permission_name` AS permissionName,
        `remark` AS remark,
        `create_user_guid` AS createUserGuid,
        `update_user_guid` AS updateUserGuid,
        `gmt_create` AS gmtCreate,
        `gmt_modified` AS gmtModified
    </sql>
</mapper>