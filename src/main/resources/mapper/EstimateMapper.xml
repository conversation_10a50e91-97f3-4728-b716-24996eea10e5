<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.EstimateMapper">


	<resultMap id="EstimateItemResidueMemchantRespDTO" type="com.holderzone.saas.store.dto.item.resp.EstimateItemResidueMemchantRespDTO">
		<result column="guid" property="guid"/>
		<result column="sku_guid" property="skuGuid"/>
		<result column="item_name" property="itemName"/>
		<result column="sku_name" property="skuName"/>
		<result column="type_guid" property="typeGuid"/>
		<result column="type_name" property="typeName"/>
		<result column="item_type" property="itemType"/>
		<result column="unit" property="unit"/>
		<result column="residue_quantity" property="residueQuantity"/>
		<result column="store_guid" property="storeGuid"/>
	</resultMap>

	<resultMap id="ItemEstimateForAndroidRespDTO" type="com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidDTO">
		<result column="sku_guid" property="skuGuid"/>
		<result column="is_sold_out" property="isSoldOut"/>
		<result column="is_the_limit" property="isTheLimit"/>
		<result column="residue_quantity" property="residueQuantity"/>
		<result column="reminder_threshold" property="reminderThreshold"/>
	</resultMap>

	<resultMap id="SetMealEstimateQuery" type="com.holderzone.saas.store.item.entity.query.SetMealEstimateQuery">
		<result column="sku_guid" property="skuGuid"/>
		<result column="is_sold_out" property="isSoldOut"/>
		<result column="is_the_limit" property="isTheLimit"/>
		<result column="residue_quantity" property="residueQuantity"/>
		<result column="pick_num" property="pickNum"/>
		<result column="item_guid" property="itemGuid"/>
	</resultMap>

	<resultMap id="QUERY_PLAN_PKG_SKU_DTO" type="com.holderzone.saas.store.dto.item.resp.PkgSkuDTO">
		<result column="sku_guid" property="skuGuid"/>
		<result column="subSkuGuid" property="subSkuGuid"/>
	</resultMap>

	<select id="getItemEstimates"  resultType="com.holderzone.saas.store.dto.item.resp.EstimateMerchantConfigRespDTO">
        SELECT
			e.guid,
			s.`guid` sku_guid,
			i.`name` item_name,
			s.`name` sku_name,
			t.guid type_guid,
			t.`name` type_name,
			i.item_type,
			s.unit,
			e.is_the_limit,
			e.limit_quantity,
			e.is_it_reset,
			e.is_the_limit_reset,
			e.reminder_threshold,
		    e.residue_quantity,
			s.store_guid,
			s.discount,
			s.sale_price,
			s.virtual_price,
			s.min_order_num,
			s.member_price,
			i.picture_url,
			s.item_guid
		FROM
			hsi_sku s
			LEFT JOIN hsi_item i ON s.item_guid = i.guid
			LEFT JOIN hsi_type t ON i.type_guid = t.guid
			LEFT JOIN hsi_estimate e ON s.guid = e.sku_guid
		<where>
			<if test="dto.sortGuid != null and dto.sortGuid !='' ">
				AND t.guid = #{dto.sortGuid}
			</if>
			<if test="dto.typGuid != null and dto.typGuid !='' ">
				AND i.item_type = #{dto.typGuid}
			</if>
			<if test="dto.isTheLimit != null  ">
				AND e.is_the_limit = #{dto.isTheLimit}
			</if>
			<if test="dto.keywords != null and dto.keywords !='' ">
				AND (
				    i.`name` LIKE  CONCAT ('%',#{dto.keywords},'%')
				    OR
				     s.guid = #{dto.keywords}
			       )
			</if>
			<if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
				AND s.store_guid  IN
				<foreach  collection="dto.storeGuids" item="store" open="("  separator=","  close=")">
				 	#{store}
				 </foreach>
			</if>
			AND i.is_delete = 0 and s.is_delete = 0
		</where>
		ORDER BY
				CONVERT(i.`name` USING gbk)
    </select>

    <select id="getEstimateItemResidue" resultMap="EstimateItemResidueMemchantRespDTO">
		SELECT
			e.guid,
			s.`guid` sku_guid,
			i.`name` item_name,
			s.`name` sku_name,
			t.guid type_guid,
			t.`name` type_name,
			i.item_type,
			s.unit,
			e.residue_quantity ,
		 	s.store_guid
		FROM
			hsi_sku s
		LEFT JOIN hsi_item i ON s.item_guid = i.guid
		LEFT JOIN hsi_type t ON i.type_guid = t.guid
		LEFT JOIN hsi_estimate e ON s.guid = e.sku_guid
		<where>
			<if test="dto.sortGuid != null and dto.sortGuid !='' ">
				AND t.guid = #{dto.sortGuid}
			</if>
			<if test="dto.typGuid != null and dto.typGuid !='' ">
				AND i.item_type = #{dto.typGuid}
			</if>
			<if test="dto.keywords != null and dto.keywords !='' ">
				AND i.`name` LIKE  CONCAT ('%',#{dto.keywords},'%')
			</if>
			<if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
				AND s.store_guid  IN
				<foreach  collection="dto.storeGuids" item="store" open="("  separator=","  close=")">
					#{store}
				</foreach>
			</if>
			AND e.reminder_threshold IS NOT NULL
			AND i.is_delete = 0
		    AND e.is_the_limit = 2 and e.is_sold_out = 1
		</where>
		ORDER BY
			CONVERT(i.`name` USING gbk)
	</select>
    <select id="queryEstimateForSyn" resultMap="ItemEstimateForAndroidRespDTO">
		<include refid="ESTIMATE_SYN"/>
		ORDER BY
			i.sort ASC
	</select>

	<sql id="ESTIMATE_SYN">
		SELECT
		s.guid sku_guid,
		e.is_sold_out,
		e.is_the_limit,
		e.residue_quantity,
		e.reminder_threshold
		FROM
		hsi_sku s
		INNER JOIN hsi_item i ON s.item_guid = i.guid
		INNER JOIN hsi_estimate e ON s.guid = e.sku_guid
		WHERE
		s.store_guid = #{dto.storeGuid}
		and s.is_delete = 0 and e.is_delete = 0
	</sql>

	<select id="queryEstimateAllForSyn" resultMap="ItemEstimateForAndroidRespDTO">

		<include refid="ESTIMATE_SYN"/>

		UNION ALL

		SELECT
			s.guid sku_guid,
			e.is_sold_out,
			e.is_the_limit,
			e.residue_quantity,
			e.reminder_threshold
		FROM
			hsi_sku s
			INNER JOIN hsi_item i ON s.item_guid = i.guid and i.item_type = 1
			INNER JOIN hsi_estimate e ON s.guid = e.sku_guid
		WHERE
			s.store_guid = #{dto.storeGuid}
			and s.is_delete = 0
			and e.is_delete = 0

	</select>

	<select id="queryEstimateAllForSynPlan" resultMap="ItemEstimateForAndroidRespDTO">

		<include refid="ESTIMATE_SYN_PLAN"/>

		UNION ALL

		SELECT
			s.sku_guid,
			e.is_sold_out,
			e.is_the_limit,
			e.residue_quantity,
			e.reminder_threshold
		FROM
			hsi_price_plan_item s
			INNER JOIN hsi_subgroup hsub on s.item_guid=hsub.item_guid
			INNER JOIN hsi_r_sku_subgroup hrsub on hsub.guid=hrsub.subgroup_guid
			INNER JOIN hsi_estimate e on s.sku_guid=e.sku_guid
			where s.plan_guid=#{planGuid} and e.store_guid=#{storeGuid}
			and e.is_delete = 0
			and s.is_delete = 0
			and s.is_pkg_item = 1
			AND hrsub.is_delete = 0
	</select>
	<select id="queryEstimateBySkuGuid" resultMap="ItemEstimateForAndroidRespDTO">
		SELECT
			e.sku_guid,
			e.is_sold_out,
			e.is_the_limit,
			e.residue_quantity,
			e.reminder_threshold
		FROM hsi_estimate e
		where e.sku_guid in
		<foreach  collection="skuGuidList" item="skuGuid" open="("  separator=","  close=")">
			#{skuGuid}
		</foreach>
		and e.is_delete = 0
		and e.store_guid = #{storeGuid}
	</select>
	<select id="queryEstimateForSynPlan" resultMap="ItemEstimateForAndroidRespDTO">
		<include refid="ESTIMATE_SYN_PLAN"/>
		ORDER BY
			s.sort ASC
	</select>
	<sql id="ESTIMATE_SYN_PLAN">
		SELECT
		s.sku_guid,
		e.is_sold_out,
		e.is_the_limit,
		e.residue_quantity,
		e.reminder_threshold
		FROM
		hsi_price_plan_item s
		INNER JOIN hsi_estimate e on s.sku_guid=e.sku_guid
		where plan_guid=#{planGuid} and store_guid=#{storeGuid}
		and e.is_delete = 0
	</sql>
    <select id="getSetMealSubitemEstimate"  resultMap="SetMealEstimateQuery">
			SELECT
				g.item_guid,
				g.pick_num,
				s.guid as sku_guid,
				e.is_sold_out,
				is_the_limit,
				e.residue_quantity
			FROM
				hsi_subgroup g
			INNER JOIN hsi_r_sku_subgroup p ON g.guid = p.subgroup_guid
			INNER JOIN hsi_sku s ON p.sku_guid = s.guid
			INNER JOIN hsi_estimate e ON s.guid = e.sku_guid
			WHERE e.is_delete = 0
				AND g.item_guid = #{itemGuid} and e.store_guid = #{storeGuid}
	</select>
    <select id="queryEstimateBySkuGuidList"
            resultType="com.holderzone.saas.store.item.entity.domain.EstimateDO">
		select sku_guid,residue_quantity,is_the_limit from hsi_estimate
		WHERE is_delete = 0
		AND	sku_guid IN
		<foreach  collection="skuGuidList" item="skuGuid" open="("  separator=","  close=")">
			#{skuGuid}
		</foreach>
	</select>
	<select id="queryEstimateBySkuGuidListAndStore"
			resultType="com.holderzone.saas.store.item.entity.domain.EstimateDO">
		select sku_guid,residue_quantity,is_the_limit,is_sold_out from hsi_estimate
		WHERE
		store_guid = #{storeGuid}
		and is_delete = 0
		and sku_guid IN
		<foreach  collection="skuGuidList" item="skuGuid" open="("  separator=","  close=")">
			#{skuGuid}
		</foreach>
	</select>
	<select id="queryEstimateByStoreGuidList"
			resultType="com.holderzone.saas.store.item.entity.domain.EstimateDO">
		select sku_guid,residue_quantity,is_the_limit from hsi_estimate
		WHERE is_delete = 0
		AND	store_guid IN
		<foreach  collection="storeGuidList" item="storeGuid" open="("  separator=","  close=")">
			#{storeGuid}
		</foreach>
	</select>

	<update id="storeItemEstimateReset">
			UPDATE hsi_estimate
				SET residue_quantity = limit_quantity
				WHERE is_delete = 0
				AND is_the_limit = 2
				AND is_it_reset = 2
				AND store_guid IN
				<foreach  collection="storeGuids" item="store" open="("  separator=","  close=")">
					#{store}
				</foreach>
	</update>
	<update id="storeItemEstimateLimitReset">
				UPDATE hsi_estimate
				SET is_the_limit = is_the_limit_reset
				WHERE is_delete = 0
				AND store_guid IN
				<foreach  collection="storeGuids" item="store" open="("  separator=","  close=")">
					#{store}
				</foreach>
	</update>
	<update id="storeItemEstimateUniqueReset">
			UPDATE hsi_estimate
			SET residue_quantity = limit_quantity,
			 unique_reset_flag = 0
				WHERE  is_the_limit = 2
				  	AND is_delete = 0
				    AND  unique_reset_flag  = 1
					AND store_guid IN
					<foreach  collection="storeGuids" item="store" open="("  separator=","  close=")">
						#{store}
					</foreach>
	</update>

	<update id="batchUpdateEstimateBySkuGuid">
		UPDATE
			hsi_estimate
		SET
			is_sold_out = #{status}
		WHERE
			is_delete = 0
			AND store_guid = #{storeGuid}
			AND sku_guid IN
		<foreach  collection="skuGuidList" item="skuGuid" open="("  separator=","  close=")">
			#{skuGuid}
		</foreach>
	</update>

	<update id="batchStopSellBySkuGuid">
		UPDATE
			hsi_estimate
		SET
			is_sold_out = 2,
			residue_quantity = 0,
			gmt_modified = NOW()
		WHERE
			is_delete = 0
			AND store_guid = #{storeGuid}
			AND sku_guid IN
		<foreach  collection="skuGuidList" item="skuGuid" open="("  separator=","  close=")">
			#{skuGuid}
		</foreach>
	</update>

    <update id="batchUpdateEstimateByItemGuid">
        UPDATE
            hsi_estimate
        SET
            is_sold_out = #{status},
			residue_quantity =0,
            gmt_modified = NOW()
        WHERE
            is_delete = 0
            AND store_guid = #{storeGuid}
            AND item_guid IN
        <foreach  collection="itemGuidList" item="itemGuid" open="("  separator=","  close=")">
            #{itemGuid}
        </foreach>
    </update>

	<delete id="batchDeleteEstimateByItemGuid">
		DELETE FROM
			hsi_estimate
		WHERE
			AND store_guid = #{storeGuid}
			AND item_guid IN
		<foreach  collection="itemGuidList" item="itemGuid" open="("  separator=","  close=")">
			#{itemGuid}
		</foreach>
	</delete>

	<delete id="batchDeleteEstimateBySkuGuid">
		DELETE FROM
			hsi_estimate
		WHERE
			 store_guid = #{storeGuid}
			AND sku_guid IN
		<foreach  collection="skuGuidList" item="skuGuid" open="("  separator=","  close=")">
			#{skuGuid}
		</foreach>
	</delete>

	<delete id="batchDeleteEstimateByStoreGuid">
		DELETE FROM
			hsi_estimate
		WHERE
		    is_forever_estimate = 1
			AND store_guid IN
		<foreach  collection="storeList" item="storeGuid" open="("  separator=","  close=")">
			#{storeGuid}
		</foreach>
	</delete>

	<select id="listSubGroupEstimateSkuGuidBySku" resultType="String">
		SELECT DISTINCT
			e.sku_guid
		FROM
			hsi_sku hs
		JOIN hsi_item hi ON hs.item_guid = hi.guid
			AND hi.item_type = 1
		JOIN hsi_subgroup hsub ON hi.guid = hsub.item_guid
		JOIN hsi_r_sku_subgroup hrsub ON hsub.guid = hrsub.subgroup_guid
		JOIN hsi_estimate e ON hrsub.sku_guid = e.sku_guid
		WHERE
			hs.guid IN
			<foreach  collection="skuGuidList" item="skuGuid" open="("  separator=","  close=")">
				#{skuGuid}
			</foreach>
			AND e.is_delete = 0
			AND hrsub.is_delete = 0
	</select>

	<select id="queryPlanPkgSkuDTO" resultMap="QUERY_PLAN_PKG_SKU_DTO">
		SELECT
			s.sku_guid,
			hrsub.sku_guid AS subSkuGuid
		FROM
			hsi_price_plan_item s
			INNER JOIN hsi_subgroup hsub ON s.item_guid = hsub.item_guid
			INNER JOIN hsi_r_sku_subgroup hrsub ON hsub.guid = hrsub.subgroup_guid
		WHERE
			s.plan_guid = #{planGuid}
			AND s.is_delete = 0
			AND s.is_pkg_item = 1
			AND hrsub.is_delete = 0
	</select>

	<select id="queryNormalPkgSkuDTO" resultMap="QUERY_PLAN_PKG_SKU_DTO">
		SELECT
			s.guid AS sku_guid,
			hrsub.sku_guid AS subSkuGuid
		FROM
			hsi_sku s
			INNER JOIN hsi_item i ON s.item_guid = i.guid
			AND i.item_type = 1
			INNER JOIN hsi_subgroup hsub ON s.item_guid = hsub.item_guid
			INNER JOIN hsi_r_sku_subgroup hrsub ON hsub.guid = hrsub.subgroup_guid
		WHERE
			i.store_guid = #{storeGuid}
			AND s.is_delete = 0
			AND hrsub.is_delete = 0
	</select>

</mapper>
