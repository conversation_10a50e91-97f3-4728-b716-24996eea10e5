<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.EstimateMapper">

    <insert id="insertRecord" useGeneratedKeys="true"
            parameterType="com.holderzone.saas.store.business.entity.domain.EstimateRecordDO">
        INSERT INTO hsb_estimate_record
        (
        <trim suffixOverrides=",">
            #{store_guid},
            #{estimate_record_guid},
            #{create_user_guid},
            #{create_user_name},
            #{business_day},
        </trim>
        )
        VALUES
        (
        <trim suffixOverrides=",">
            #{storeGuid},
            #{estimateRecordGuid},
            #{createUserGuid},
            #{createUserName},
            #{businessDay},
        </trim>
        )
    </insert>

    <select id="findByBusinessDay" resultMap="EstimateRecordMapResult"
            parameterType="com.holderzone.saas.store.business.entity.domain.EstimateRecordDO">
        SELECT
        <trim suffixOverrides=",">
            `store_guid`,
            `estimate_record_guid`,
            `create_user_guid`,
            `create_user_name`,
            DATE_FORMAT(`business_day`,"%Y-%m-%d") as business_day,
            DATE_FORMAT(`gmt_create`,"%Y-%m-%d %T") as gmt_create,
            DATE_FORMAT(`gmt_modified`,"%Y-%m-%d %T") as gmt_modified,
        </trim>
        FROM
        hsb_estimate_record
        WHERE
        `business_day` = #{businessDay}
    </select>

    <insert id="batchInsertDishes" useGeneratedKeys="true"
            parameterType="java.util.List">
        INSERT INTO hsb_estimate_dish
        (
        <trim suffixOverrides=",">
            #{estimate_record_guid},
            #{dish_id},
            #{estimate_count},
            #{warning_count},
            #{status},
        </trim>
        )
        VALUES
        <foreach collection="list" item="item" index="index" open="" separator="," close="">
            (
            <trim suffixOverrides=",">
                #{item.estimateRecordGuid},
                #{item.dishId},
                #{item.estimateCount},
                #{item.warningCount},
                #{item.status},
            </trim>
            )
        </foreach>
    </insert>

    <delete id="batchDeleteDishes" parameterType="java.lang.String">
        DELETE FROM
        hsb_estimate_dish
        WHERE
        `estimate_record_guid` = #{estimateRecordGuid}
    </delete>

    <select id="queryDishesByRecordGuid"  resultMap="EstimateDishMapResult"
            parameterType="java.lang.String">
        SELECT
        <trim suffixOverrides=",">
            #{estimate_record_guid},
            #{dish_id},
            #{estimate_count},
            #{warning_count},
            #{status},
        </trim>
        FROM 
        hsb_estimate_dish
        WHERE
        `estimate_record_guid` = #{estimateRecordGuid}
    </select>

    <resultMap id="EstimateRecordMapResult" type="com.holderzone.saas.store.business.entity.domain.EstimateRecordDO">
        <result property="storeGuid" column="store_guid"/>
        <result property="estimateRecord" column="estimate_record_guid"/>
        <result property="createUserGuid" column="create_user_guid"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="businessDay" column="business_day"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <resultMap id="EstimateDishMapResult" type="com.holderzone.saas.store.business.entity.domain.EstimateDishDO">
        <result property="estimateRecordGuid" column="estimate_record_guid"/>
        <result property="dishId" column="dish_id"/>
        <result property="estimateCount" column="estimate_count"/>
        <result property="warningCount" column="warning_count"/>
        <result property="status" column="status"/>
    </resultMap>

</mapper>