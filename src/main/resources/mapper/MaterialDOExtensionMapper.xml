<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.MaterialDOMapper">
    <resultMap id="QueryResultMap" type="com.holderzone.erp.entity.domain.MaterialDO">
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="enterprise_guid" jdbcType="VARCHAR" property="enterpriseGuid"/>
        <result column="warehouse_guid" jdbcType="VARCHAR" property="warehouseGuid"/>
        <result column="property" jdbcType="VARCHAR" property="property"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="simple_name" jdbcType="VARCHAR" property="simpleName"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="bar_code" jdbcType="VARCHAR" property="barCode"/>
        <result column="unitName" property="unitName"/>
        <result column="auxiliary_unit" jdbcType="VARCHAR" property="auxiliaryUnit"/>
        <result column="auxiliaryUnitName" property="auxiliaryUnitName"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="specs" jdbcType="VARCHAR" property="specs"/>
        <result column="conversion_main" jdbcType="DECIMAL" property="conversionMain"/>
        <result column="conversion_auxiliary" jdbcType="DECIMAL" property="conversionAuxiliary"/>
        <result column="lowest_stock" jdbcType="DECIMAL" property="lowestStock"/>
        <result column="stock" jdbcType="DECIMAL" property="stock"/>
        <result column="storage_method" jdbcType="VARCHAR" property="storageMethod"/>
        <result column="enabled" jdbcType="BIT" property="enabled"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="categoryName" property="categoryName"/>
    </resultMap>
    <select id="findByCondition" parameterType="com.holderzone.saas.store.dto.erp.MaterialQueryDTO"
            resultMap="QueryResultMap">
        select m.guid, m.enterprise_guid,m.store_guid,m.warehouse_guid, m.property, m.name, m.simple_name, m.unit,
        m.auxiliary_unit,
        m.category,
        ( SELECT u.`name` from hse_material_unit u where u.guid = m.unit ) unitName,
        ( SELECT u.`name` FROM hse_material_unit u where u.guid = m.auxiliary_unit ) auxiliaryUnitName,
        m.type, m.code, m.specs, m.conversion_main, m.conversion_auxiliary, m.lowest_stock,m.storage_method,
        m.enabled,m.remark,m.bar_code
        from hse_material m
        where 1=1 and m.deleted=0
        <if test="record.storeGuid != null">
            and m.store_guid=#{record.storeGuid}
        </if>
        <if test="record.warehouseGuid != null and record.warehouseGuid !=''">
            and m.warehouse_guid=#{record.warehouseGuid}
        </if>
        <if test="record.category != null">
            and m.category=#{record.category}
        </if>
        <if test="record.state != null and record.state ==1 ">
            and m.enabled=1
        </if>
        <if test="record.state != null and record.state ==0 ">
            and m.enabled=0
        </if>
        <if test="record.materialGuidList!=null and record.materialGuidList.size>0">
            and m.guid in
            <foreach collection="record.materialGuidList" item="guid" separator="," open="(" close=")">
                #{guid}
            </foreach>
        </if>
        <if test="record.searchConditions != null and record.searchConditions !=''">
            and (m.name like "%"#{record.searchConditions}"%" or m.simple_name like "%"#{record.searchConditions}"%")
        </if>
        order by m.gmt_create desc
    </select>
    <select id="findAllByWarehouseOrGuidList" resultMap="QueryResultMap">
        SELECT m.*,
        ( SELECT u.`name` from hse_material_unit u where u.guid = m.unit ) unitName,
        ( SELECT u.`name` FROM hse_material_unit u where u.guid = m.auxiliary_unit ) auxiliaryUnitName,
        0 as stock
        FROM
        hse_material m
        where m.deleted=0
        <if test="storeGuid != null">
            and m.store_guid=#{storeGuid}
        </if>
        <if test="materialGuidList != null and materialGuidList.size > 0">
            and m.guid in
            <foreach collection="materialGuidList" item="materialGuid" open="(" separator="," close=")">
                #{materialGuid}
            </foreach>
        </if>
    </select>

    <select id="findAllByWarehouseOrCodeList" resultMap="QueryResultMap">
        SELECT m.*,
        ( SELECT u.`name` from hse_material_unit u where u.guid = m.unit ) unitName,
        ( SELECT u.`name` FROM hse_material_unit u where u.guid = m.auxiliary_unit ) auxiliaryUnitName,
        0 as stock
        FROM
        hse_material m
        where m.deleted=0
        <if test="storeGuid != null">
            and m.store_guid=#{storeGuid}
        </if>
        <if test="materialCodeList != null and materialCodeList.size > 0">
            and m.code in
            <foreach collection="materialCodeList" item="materialCode" open="(" separator="," close=")">
                #{materialCode}
            </foreach>
        </if>
    </select>

    <select id="findByWarehouseOrGuidList" resultMap="QueryResultMap">
        SELECT m.guid, m.name,m.unit,
        m.auxiliary_unit,m.code, m.conversion_main, m.conversion_auxiliary,
        IFNULL(s.stock,0) stock,
        ( SELECT u.`name` from hse_material_unit u where u.guid = m.unit ) unitName,
        ( SELECT u.`name` FROM hse_material_unit u where u.guid = m.auxiliary_unit ) auxiliaryUnitName
        FROM
        hse_material m left join hse_r_material_stock s on s.material_guid=m.guid
        where 1=1
        <if test="storeGuid != null">
            and m.store_guid=#{storeGuid}
        </if>
        <if test="warehouseGuid!=null">
            and s.warehouse_guid=#{warehouseGuid}
        </if>
        <if test="materialGuidList != null and materialGuidList.size > 0">
            and m.guid in
            <foreach collection="materialGuidList" item="materialGuid" open="(" separator="," close=")">
                #{materialGuid}
            </foreach>
        </if>
    </select>

    <select id="findByWarehouseOrCodeList" resultMap="QueryResultMap">
        SELECT m.guid, m.name,m.unit,
        m.auxiliary_unit,m.code, m.conversion_main, m.conversion_auxiliary,
        IFNULL(s.stock,0) stock,
        ( SELECT u.`name` from hse_material_unit u where u.guid = m.unit ) unitName,
        ( SELECT u.`name` FROM hse_material_unit u where u.guid = m.auxiliary_unit ) auxiliaryUnitName
        FROM
        hse_material m left join hse_r_material_stock s on s.material_guid=m.guid
        where 1=1
        <if test="storeGuid != null">
            and m.store_guid=#{storeGuid}
        </if>
        <if test="warehouseGuid!=null">
            and s.warehouse_guid=#{warehouseGuid}
        </if>
        <if test="materialCodeList != null and materialCodeList.size > 0">
            and m.code in
            <foreach collection="materialCodeList" item="materialCode" open="(" separator="," close=")">
                #{materialCode}
            </foreach>
        </if>
    </select>

    <insert id="addBatch">
        <foreach collection="list" item="materialDO" separator=";">
            insert into hse_material(guid, enterprise_guid, store_guid, warehouse_guid, `name`,simple_name,unit,
            category,`type`, code, bar_code, specs)
            values (#{materialDO.guid},IFNULL(#{materialDO.enterpriseGuid},""),IFNULL(#{materialDO.storeGuid},""),
            IFNULL(#{materialDO.warehouseGuid},""),#{materialDO.name},IFNULL(#{materialDO.simpleName},""),
            IFNULL(#{materialDO.unit},""),IFNULL(#{materialDO.category},""),IFNULL(#{materialDO.type},""),
            #{materialDO.code},IFNULL(#{materialDO.barCode},""),IFNULL(#{materialDO.specs},"")
            )
        </foreach>
    </insert>

    <select id="queryDetailList" resultMap="MaterialDetailMap">
        select dd.material_guid, d.type AS doc_type, d.guid AS doc_guid, dd.gmt_create, dd.stock, dd.count
        from hse_warehouse_in_out_document_detail dd
        left join hse_warehouse_in_out_document d on dd.document_guid = d.guid
        where material_guid in
        <foreach collection="query.materialGuidList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and dd.gmt_create >= #{query.startDateTime}
        and dd.gmt_create <![CDATA[<=]]> #{query.endDateTime}
        and d.warehouse_guid = #{query.warehouseGuid}
        order by d.gmt_create asc
    </select>

    <select id="materialConsumeSum" resultMap="MaterialResultMap">
        SELECT m.guid, m.name, m.code,mc.name AS category, mu.name AS unit, doc.sum_count AS `count` FROM hse_material m
        LEFT JOIN hse_material_category mc ON mc.guid = m.`category`
        LEFT JOIN hse_material_unit mu ON m.unit = mu.guid
        LEFT JOIN hse_r_material_stock s on s.material_guid=m.guid
        LEFT JOIN (
        SELECT dd.material_guid AS m_guid, sum(dd.`count`) AS sum_count
        FROM hse_warehouse_in_out_document d
        LEFT JOIN hse_warehouse_in_out_document_detail dd ON d.guid = dd.document_guid
        WHERE d.document_date &gt;= #{dto.startDate} AND d.document_date
        &lt;= #{dto.endDate}
        AND d.type = #{dto.type} GROUP BY dd.material_guid
        ) doc ON m.guid = doc.m_guid
        <where>
            s.warehouse_guid = #{dto.warehouseGuid}
            <if test="dto.classifyGuid != null">
                AND m.category = #{dto.classifyGuid}
            </if>
            <if test="dto.nameOrCode != null and dto.nameOrCode !=''">
                AND (m.name LIKE CONCAT('%',#{dto.nameOrCode},'%') or m.code LIKE CONCAT(#{dto.nameOrCode},'%'))
            </if>
        </where>
        ORDER BY m.gmt_create
    </select>

    <resultMap id="MaterialResultMap" type="com.holderzone.erp.entity.domain.read.MaterialConsumeReadDO">
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="category" jdbcType="VARCHAR" property="classify"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="count" jdbcType="DECIMAL" property="count"/>
    </resultMap>

    <resultMap id="MaterialDetailMap" type="com.holderzone.erp.entity.domain.read.MaterialDocDetailReadDO">
        <result column="material_guid" jdbcType="VARCHAR" property="materialGuid"/>
        <result column="doc_type" jdbcType="INTEGER" property="docType"/>
        <result column="doc_guid" jdbcType="VARCHAR" property="docGuid"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="stock" jdbcType="DECIMAL" property="stock"/>
        <result column="count" jdbcType="DECIMAL" property="count"/>
    </resultMap>
</mapper>