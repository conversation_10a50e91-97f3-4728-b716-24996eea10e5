<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.deposit.mapper.HsdDepositMapper">

    <resultMap id="DepositDO"
               type="com.holderzone.holder.saas.store.deposit.entity.bo.DepositDO">
    </resultMap>

    <select id="queryDepositRecordFromOrderId" resultMap="DepositDO">
        SELECT * FROM hse_deposit_deposit as i WHERE i.store_guid = #{dto.storeGuid}
        AND i.deposit_order_id = #{dto.condition}
        ORDER BY i.sorted DESC
    </select>

    <select id="queryDepositRecordFromMemberGuid" resultMap="DepositDO">
        SELECT * FROM hse_deposit_deposit as i WHERE i.store_guid = #{dto.storeGuid}
        AND (i.user_guid = #{dto.phoneGuid} or i.user_guid = #{dto.wxGuid})
        ORDER BY i.sorted DESC
    </select>

</mapper>
