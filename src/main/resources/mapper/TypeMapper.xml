<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.TypeMapper">

    <resultMap id="JournalingItemsQuery" type="com.holderzone.saas.store.item.entity.query.JournalingItemsQuery">
        <result column="guid" property="guid"/>
        <result column="brand_guid" property="brandGuid"/>
        <result column="item_from" property="itemFrom"/>
        <result column="parent_guid" property="parentGuid"/>
        <result column="name" property="name"/>
    </resultMap>
    <delete id="deleteByGuid" >
        delete from  hsi_type where guid=#{guid};
    </delete>


    <select id="queryJournalingItemType" resultMap="JournalingItemsQuery">
      SELECT
            a.guid,
            b.brand_guid,
            a.type_from as item_from,
            a.parent_guid,
            a.`name`
        FROM
            hsi_type	AS a
            LEFT JOIN hsi_type b ON a.parent_guid = b.guid
        WHERE
            a.type_from	= 2 and  b.brand_guid IS NOT NULL
            UNION ALL
            (SELECT
            guid,
            brand_guid,
            type_from as item_from ,
            parent_guid,
            `name`
        FROM
            hsi_type
        WHERE
            type_from = 0)

	</select>

    <select id="queryBrandGuidAndStoreName" resultType="com.holderzone.saas.store.dto.item.resp.TypeSynRespDTO">
        SELECT
            parent_guid AS typeGuid,
            `name`,
            sort,
            icon_url,
            description,
            menu_classify_picture_type AS menuClassifyPictureType
        FROM
            `hsi_type`
        WHERE
            store_guid = #{query.storeGuid}
            AND is_delete = 0
            AND is_enable = 1
        ORDER BY
	        sort
    </select>

    <select id="queryOrdinaryByStore" resultType="com.holderzone.saas.store.dto.item.resp.TypeSynRespDTO">
        SELECT
            guid AS typeGuid,
            `name`,
            sort,
            icon_url,
            description,
            menu_classify_picture_type AS menuClassifyPictureType,
            parent_guid
        FROM
            `hsi_type`
        WHERE
            store_guid = #{query.storeGuid}
            AND is_delete = 0
            AND is_enable = 1
        ORDER BY
	        sort
    </select>

    <select id="queryPlanByStore"
            resultType="com.holderzone.saas.store.dto.item.resp.TypeSynRespDTO">
        SELECT
            t.guid AS typeGuid,
            t.`name`,
            t.sort,
            t.icon_url,
            t.description,
            t.menu_classify_picture_type AS menuClassifyPictureType
        FROM
            hsi_type t
            LEFT JOIN `hsi_price_plan_store` pps ON pps.plan_guid = t.price_plan_guid
        WHERE
            pps.store_guid = #{query.storeGuid}
            AND t.is_delete = 0
            AND t.is_enable = 1
        ORDER BY
            t.sort
    </select>

    <select id="queryPlanSku" resultType="com.holderzone.saas.store.dto.item.common.ItemDTO">
        SELECT
            pi.item_guid guid,
            pi.plan_item_name `name`,
            pi.type_guid typeGuid,
            ht.`name` typeName
        FROM
            `hsi_price_plan_item` pi
            LEFT JOIN hsi_type ht ON ht.guid = pi.type_guid
        WHERE
            pi.is_delete = 0
            AND ht.is_delete = 0
            AND pi.plan_guid = #{planGuid}
            AND pi.item_guid in
            <foreach collection="itemGuidList" index="index" item="guid" open="(" separator="," close=")">
                #{guid}
            </foreach>
        GROUP BY
            pi.item_guid,
            pi.plan_item_name,
            pi.type_guid,
            ht.`name`
    </select>

    <!--    Error querying database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 4↵### The error may exist in com/holderzone/saas/store/item/mapper/TypeMapper.java (best guess)↵### The error may involve defaultParameterMap↵### The error occurred while setting parameters↵### SQL: SELECT  guid,id,gmt_create,gmt_modified,is_delete,brand_guid,sort,is_enable,description,icon_url,item_num,name_change,type_from,store_guid,parent_guid,name  FROM hsi_type   WHERE  is_delete=0  AND guid IN ()↵### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 4↵; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 4"-->
</mapper>
