<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.OrderItemMapper">

    <update id="updateBatchIsAdjustItemIsTrue">
        update hst_order_item set is_adjust_item = 1 where guid in
        <foreach collection="guids" item="guid" open="(" separator="," close=")">
            #{guid}
        </foreach>
    </update>
    <update id="clearGroupBuyFlag">
        update hst_order_item set groupon_type = null,coupon_code = null
        where order_guid = #{orderGuid}
        and coupon_code = #{couponCode}
    </update>
    <update id="batchClearGroupBuyFlag">
        update hst_order_item set groupon_type = null,coupon_code = null
        where guid in
        <foreach collection="list" open="(" item="guid" close=")" separator="," >
            #{guid}
        </foreach>
    </update>


    <update id="restoreOrderItem">
        <foreach collection="restoreOrderItemList" item="restoreOrderItem"  separator=";" >
            update hst_order_item set is_delete = 0, current_count = #{restoreOrderItem.currentCount} where guid = #{restoreOrderItem.guid}
        </foreach>
    </update>

    <select id="selectListByPadOrderGuid"
            resultType="com.holderzone.saas.store.trade.entity.domain.OrderItemDO">
        SELECT
            *
        FROM
            hst_order_item
        WHERE
            pad_order_guid = #{padOrderGuid}
          AND is_delete = 0
    </select>

    <select id="screenSaleType" resultType="com.holderzone.saas.store.dto.journaling.resp.SaleCountRespDTO">
        SELECT
            oi.item_type_guid AS itemTypeGuid,
            oi.item_type_name AS typeName,
            SUM( oi.current_count + oi.free_count ) AS saleNumber,
            SUM( ROUND( oi.`price` * ( oi.current_count + oi.free_count ) , 2) ) AS salePrice
        FROM
            hst_order_item oi
            LEFT JOIN hst_order o ON o.guid = oi.order_guid
        WHERE
            o.checkout_time BETWEEN #{query.businessStartDateTime} AND #{query.businessEndDateTime}
            AND o.state = 4
            AND o.is_delete = 0
            AND oi.is_delete = 0
            AND o.recovery_type IN ( 1, 3 )
            AND o.store_guid IN
            <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator=","
                     close=")">
                #{storeGuid}
            </foreach>
        GROUP BY
            oi.item_type_guid
        ORDER BY
            salePrice DESC
    </select>

    <select id="screenSaleItem" resultType="com.holderzone.saas.store.dto.journaling.resp.SaleCountRespDTO">
        SELECT
            oi.item_guid AS itemGuid,
            oi.item_name AS itemName,
            oi.item_type AS itemType,
            SUM( oi.current_count + oi.free_count ) AS saleNumber,
            SUM( ROUND( oi.`price` * ( oi.current_count + oi.free_count ) , 2) ) AS salePrice ,
            oi.sku_guid AS skuGuid,
            oi.`unit` as unit
        FROM
            hst_order_item oi
            LEFT JOIN hst_order o ON o.guid = oi.order_guid
        WHERE
            o.checkout_time BETWEEN #{query.businessStartDateTime} AND #{query.businessEndDateTime}
            AND o.state = 4
            AND o.is_delete = 0
            AND oi.is_delete = 0
            AND o.recovery_type IN ( 1, 3 )
            AND o.store_guid IN
            <foreach collection="query.storeGuidList" item="storeGuid" open="(" separator=","
                     close=")">
                #{storeGuid}
            </foreach>
        GROUP BY
            oi.item_guid
        ORDER BY
            salePrice DESC
    </select>

    <select id="getByGuidContainsDel" resultType="com.holderzone.saas.store.trade.entity.domain.OrderItemDO">
        SELECT
            *
        FROM
            hst_order_item
        WHERE
            guid = #{orderItemGuid}
    </select>


    <select id="listByMainItemGuidContainsDel"
            resultType="com.holderzone.saas.store.trade.entity.domain.OrderItemDO">
        SELECT
            *
        FROM
            hst_order_item
        WHERE
            parent_item_guid = #{orderItemGuid}
    </select>

</mapper>
