<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.RSkuSubgroupMapper">

    <update id="updateIsDelete">
        UPDATE hsi_r_sku_subgroup SET is_delete = 1 WHERE guid = #{guid} AND is_delete = 0 limit 1;
    </update>

    <update id="deleteByItemGuid">
         UPDATE hsi_r_sku_subgroup SET is_delete = 1 WHERE item_guid = #{itemGuid}  AND is_delete = 0 limit 1;
    </update>


</mapper>
