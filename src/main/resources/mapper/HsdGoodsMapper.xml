<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.deposit.mapper.HsdGoodsMapper">

    <resultMap id="ExpireGoodsResult" type="com.holderzone.holder.saas.store.deposit.entity.bo.ExpireGoodsDTO">
        <result column="guid" jdbcType="CHAR" property="guid"/>
        <result column="deposit_guid" jdbcType="CHAR" property="depositGuid"/>
        <result column="expire_time" jdbcType="CHAR" property="expireTime"/>
    </resultMap>

    <select id="queryExpireGoods" resultMap="ExpireGoodsResult">
        select
        r.guid, r.deposit_guid, r.expire_time
        from hse_deposit_goods r where message_status = 0
    </select>

</mapper>
