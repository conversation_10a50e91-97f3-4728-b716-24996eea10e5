<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.print.mapper.PrinterInvoiceMapper">

    <update id="updatePrintInvoiceAuto">
        UPDATE hsp_printer_invoice hpi
        SET hpi.auto_print = #{status}
        WHERE hpi.printer_guid IN (
            SELECT hp.printer_guid from hsp_printer hp where hp.device_id = #{req.deviceId} and hp.store_guid = #{req.storeGuid}
        )
       AND hpi.invoice_type = 0
       AND hpi.store_guid = #{req.storeGuid}
    </update>

    <select id="selectInvoiceAutoStatus" resultType="java.lang.Integer">
        SELECT hpi.auto_print FROM hsp_printer_invoice hpi join hsp_printer hp on hpi.printer_guid = hp.printer_guid
        WHERE
          hp.device_id = #{req.deviceId} and hp.store_guid = #{req.storeGuid}
          AND hpi.invoice_type = 0
          AND hpi.store_guid = #{req.storeGuid}
        limit 1
    </select>
</mapper>
