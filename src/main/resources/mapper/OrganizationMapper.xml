<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.organization.mapper.OrganizationMapper">


    <insert id="saveBatch">
        INSERT INTO `hso_organization`
        ( `guid`,`code`, `type`, `name`, `parent_ids`, `business_start`, `business_end`, `gmt_create`, `gmt_modified`, `create_user_guid`, `modified_user_guid`
        , `is_bu_accounts`
        , `is_show_cash`)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.guid},#{item.code},#{item.type},#{item.name},#{item.parentIds},#{item.businessStart},#{item.businessEnd},
            now(),now(),#{item.createUserGuid},#{item.modifiedUserGuid}
            ,#{item.isBuAccounts}
            ,#{item.isShowCash})
        </foreach>
        ON DUPLICATE KEY UPDATE
        type = 3,
        code=if((ISNULL(VALUES(code))=0 AND LENGTH(VALUES(code)))>0,VALUES(code),code),
        parent_ids=if((ISNULL(VALUES(parent_ids))=0 AND LENGTH(VALUES(parent_ids)))>0,VALUES(parent_ids),parent_ids),
        name=if((ISNULL(VALUES(name))=0 AND LENGTH(VALUES(name)))>0,VALUES(name),name),
        business_start=if((ISNULL(VALUES(business_start))=0 AND LENGTH(VALUES(business_start)))>0,VALUES(business_start),business_start),
        business_end=if((ISNULL(VALUES(business_end))=0 AND LENGTH(VALUES(business_end)))>0,VALUES(business_end),business_end),
        gmt_modified = now()
    </insert>

    <update id="updateStoreLogoUrl">
        update hso_organization
        set
        logo_url=#{logoUrl}
        <where>
            `is_deleted` = 0
            AND
            <foreach collection="list" item="item" separator="or" open="(" close=")">
                guid = #{item}
            </foreach>
        </where>

    </update>
    <update id="updateItemUpload">
        update hso_organization
        set
        is_item_upload=#{isItemUpload}
        <where>
            `is_deleted` = 0
            AND
            guid = #{guid}
        </where>
    </update>
    <update id="batchUpdateBuAccounts">
        update hso_organization
        <set>
            <if test="store.isBuAccounts != null">is_bu_accounts=#{store.isBuAccounts},</if>
            <if test="store.isShowCash != null">is_show_cash=#{store.isShowCash},</if>
            <if test="store.canOpenTable != null">can_open_table=#{store.canOpenTable},</if>
            <if test="store.isMultiHandover != null">is_multi_handover=#{store.isMultiHandover},</if>
        </set>
        WHERE
        guid in
        <foreach  item="item" index="index" collection="storeList" open="(" separator="," close=" )">
            #{item}
        </foreach>
    </update>

    <delete id="removeAllOrganization">
        delete from hso_organization where type = 1
    </delete>

    <select id="selectByCondition" parameterType="com.holderzone.saas.store.dto.organization.QueryStoreDTO"
            resultType="com.holderzone.saas.store.organization.domain.OrganizationDO">
        SELECT
        <include refid="column"/>
        FROM hso_organization
        <where>
            (`type` = 2 or `type` = 3 ) AND `is_deleted` = 0
            <if test="queryStoreDTO.isEnable != null">
                AND is_enable = #{queryStoreDTO.isEnable}
            </if>
            <if test="queryStoreDTO.storeName != null">
                AND `name` LIKE CONCAT ('%', #{queryStoreDTO.storeName}, '%')
            </if>
            <include refid="guidListInQuerrier"/>
        </where>
        ORDER BY gmt_create DESC
    </select>
    <select id="selectByConditionNoPage" parameterType="com.holderzone.saas.store.dto.organization.StoreParserDTO"
            resultType="com.holderzone.saas.store.organization.domain.OrganizationDO">
        SELECT
        *
        FROM hso_organization
        <where>
            (`type` = 2 or `type` = 3 ) AND `is_deleted` = 0
            <include refid="guidListInQuerrier"/>
        </where>
        ORDER BY gmt_create DESC
    </select>
    <sql id="guidListInQuerrier">
        <if test="queryStoreDTO.brandGuidList != null and queryStoreDTO.brandGuidList.size() > 0">
            AND guid IN (
            SELECT store_guid FROM hso_r_store_brand WHERE brand_guid IN
            <foreach collection="queryStoreDTO.brandGuidList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="queryStoreDTO.organizationGuidList != null and queryStoreDTO.organizationGuidList.size() > 0">
            AND
            <foreach collection="queryStoreDTO.organizationGuidList" item="item" separator="or" open="(" close=")">
                -- 这里效率较低，考虑后期优化
                parent_ids LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <if test="queryStoreDTO.regionCodeList !=null and queryStoreDTO.regionCodeList.size() > 0">
            AND
            <foreach collection="queryStoreDTO.regionCodeList" item="item" separator="or" open="(" close=")">
                (province_code = #{item} OR county_code = #{item} OR city_code = #{item})
            </foreach>
        </if>
        <if test="queryStoreDTO.storeGuidList !=null and queryStoreDTO.storeGuidList.size() > 0">
            AND guid IN
            <foreach collection="queryStoreDTO.storeGuidList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <select id="parseByCondition" parameterType="com.holderzone.saas.store.dto.organization.StoreParserDTO"
            resultType="java.lang.String">
        SELECT guid
        FROM hso_organization
        <where>
            (`type` = 2 or `type` = 3 ) AND `is_deleted` = 0
            <if test="storeParserDTO.brandGuidList != null and storeParserDTO.brandGuidList.size() > 0">
                AND guid IN (
                SELECT store_guid FROM hso_r_store_brand WHERE brand_guid IN
                <foreach collection="storeParserDTO.brandGuidList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeParserDTO.organizationGuidList != null and storeParserDTO.organizationGuidList.size() > 0">
                AND
                <foreach collection="storeParserDTO.organizationGuidList" item="item" separator="or" open="(" close=")">
                    -- 这里效率较低，考虑后期优化
                    parent_ids LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            <if test="storeParserDTO.regionCodeList !=null and storeParserDTO.regionCodeList.size() > 0">
                AND
                <foreach collection="storeParserDTO.regionCodeList" item="item" separator="or" open="(" close=")">
                    (province_code = #{item} OR county_code = #{item} OR city_code = #{item})
                </foreach>
            </if>
            <if test="storeParserDTO.allStoreGuidList !=null and storeParserDTO.allStoreGuidList.size() > 0">
                AND guid IN (
                <foreach collection="storeParserDTO.allStoreGuidList" item="item" index="index" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        -- 查询未绑定品牌与未绑定省市区的门店
        UNION
        SELECT guid
        FROM hso_organization
        where (`type` = 2 or `type` = 3 )
        AND `is_deleted` = 0
        AND (
        guid NOT IN (SELECT store_guid FROM hso_r_store_brand)
        OR
        province_code IS NULL
        )
        <if test="storeParserDTO.allStoreGuidList !=null and storeParserDTO.allStoreGuidList.size() > 0">
            AND guid IN (
            <foreach collection="storeParserDTO.allStoreGuidList" item="item" index="index" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="parseByConditionNotUnion" parameterType="com.holderzone.saas.store.dto.organization.StoreParserDTO"
            resultType="java.lang.String">
        SELECT guid
        FROM hso_organization
        <where>
            (`type` = 2 or `type` = 3 ) AND `is_deleted` = 0
            <if test="storeParserDTO.brandGuidList != null and storeParserDTO.brandGuidList.size() > 0">
                AND guid IN (
                SELECT store_guid FROM hso_r_store_brand WHERE brand_guid IN
                <foreach collection="storeParserDTO.brandGuidList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeParserDTO.organizationGuidList != null and storeParserDTO.organizationGuidList.size() > 0">
                AND
                <foreach collection="storeParserDTO.organizationGuidList" item="item" separator="or" open="(" close=")">
                    -- 这里效率较低，考虑后期优化
                    parent_ids LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            <if test="storeParserDTO.regionCodeList !=null and storeParserDTO.regionCodeList.size() > 0">
                AND
                <foreach collection="storeParserDTO.regionCodeList" item="item" separator="or" open="(" close=")">
                    (province_code = #{item} OR county_code = #{item} OR city_code = #{item})
                </foreach>
            </if>
            <if test="storeParserDTO.allStoreGuidList !=null and storeParserDTO.allStoreGuidList.size() > 0">
                AND guid IN (
                <foreach collection="storeParserDTO.allStoreGuidList" item="item" index="index" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="queryStoreByRegionList" parameterType="java.util.List"
            resultType="com.holderzone.saas.store.organization.domain.OrganizationDO">
        SELECT
        <include refid="column"/>
        FROM hso_organization
        <where>
            (`type` = 2 or `type` = 3 )
            <if test="regionDTOList != null and regionDTOList.size() > 0">
                AND
                <foreach collection="regionDTOList" item="item" separator="or" open="(" close=")">
                    (province_code = #{item.code} OR county_code = #{item.code} OR city_code = #{item.code})
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryAllChildOrg" parameterType="java.util.List" resultType="java.lang.String">
        SELECT guid
        FROM hso_organization
        <where>
            `is_deleted` = 0
            AND
            <foreach collection="list" item="item" separator="or" open="(" close=")">
                -- 这里效率较低，考虑后期优化
                parent_ids LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </where>
    </select>

    <select id="queryStoreDetail" parameterType="java.util.List"
            resultType="com.holderzone.saas.store.dto.organization.StoreDTO">
        SELECT
        s.guid,
        s.name,
        s.code,
        s.parent_ids AS parentIds,
        s.province_code AS provinceCode,
        s.province_name AS provinceName,
        s.city_code AS cityCode,
        s.city_name AS cityName,
        s.county_code AS countyCode,
        s.county_name AS countyName,
        b.brand_guid AS belongBrandGuid,
        s.create_user_guid AS createUserGuid,
        s.modified_user_guid AS modifiedUserGuid,
        s.longitude AS longitude,
        s.latitude AS latitude,
        s.is_self_build_items
        FROM hso_organization AS s
        LEFT JOIN hso_r_store_brand AS b ON b.store_guid = s.guid
        <where>
            (s.`type` = 2 or s.`type` = 3 ) and s.`is_deleted` = 0
            AND
            <foreach collection="list" item="item" separator="or" open="(" close=")">
                s.guid = #{item}
            </foreach>
        </where>
    </select>
    <select id="queryByGuid" resultType="com.holderzone.saas.store.organization.domain.OrganizationDO">
        select *
        from hso_organization
        where guid = #{guid} limit 1;
    </select>

    <select id="countOrganization" resultType="java.lang.Integer">
        select count(1)
        from hso_organization
        where is_deleted = 0 and type in('1','3')
    </select>

    <sql id="column">
        id
        ,
        guid,
        code,
        `type`,
        `name`,
        contact_name,
        contact_tel,
        parent_ids,
        province_code,
        province_name,
        city_code,
        city_name,
        county_code,
        county_name,
        address_detail,
        description,
        icon,
        is_enable,
        is_deleted,
        business_start,
        business_end,
        longitude,
        latitude,
        create_user_guid,
        modified_user_guid,
        photos,
        gmt_create,
        gmt_modified,
        is_self_build_items
    </sql>

    <resultMap id="storeResultMap" type="com.holderzone.saas.store.organization.domain.OrganizationDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_tel" jdbcType="VARCHAR" property="contactTel"/>
        <result column="parentIds" jdbcType="VARCHAR" property="parentIds"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="county_code" jdbcType="VARCHAR" property="countyCode"/>
        <result column="county_name" jdbcType="VARCHAR" property="countyName"/>
        <result column="address_detail" jdbcType="VARCHAR" property="addressDetail"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="is_enable" jdbcType="TINYINT" property="isEnable"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="business_start" jdbcType="DATE" property="businessStart"/>
        <result column="business_end" jdbcType="DATE" property="businessEnd"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="gmt_create" jdbcType="DATE" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="DATE" property="gmtModified"/>
        <result column="create_user_guid" jdbcType="VARCHAR" property="createUserGuid"/>
        <result column="modified_user_guid" jdbcType="VARCHAR" property="modifiedUserGuid"/>
        <result column="photos" jdbcType="VARCHAR" property="photos"/>
    </resultMap>

    <select id="getStoreBase" resultType="com.holderzone.saas.store.dto.organization.StoreDTO">
        SELECT
        ho.NAME,
        ho.business_start AS businessStart,
        ho.business_end AS businessEnd,
        hrb.guid AS belongBrandGuid,
        hrb.NAME AS belongBrandName
        FROM
        hso_organization ho
        LEFT JOIN hso_r_store_brand hb ON ho.guid = hb.store_guid left
        JOIN hso_brand hrb ON hb.brand_guid = hrb.guid
        where ho.guid = #{guid} limit 1;
    </select>
</mapper>
