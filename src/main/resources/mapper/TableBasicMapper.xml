<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.table.mapper.TableBasicMapper">

    <sql id="tableBasicColumn">
        guid,store_name,store_guid,area_guid,area_name,table_code,seats,enable,deleted,sort
    </sql>

    <!--<insert id="inertOneForSortAutoIncrease" parameterType="com.holderzone.holder.saas.store.table.domain.TableBasicDO">-->
    <!--insert into hst_table_basic-->
    <!--(-->
    <!--<include refid="tableBasicColumn"/>-->
    <!--)-->
    <!--values-->
    <!--(-->
    <!--#{guid},#{store_name},#{store_guid},#{area_guid},#{area_name},#{table_code},#{seats}-->
    <!--,#{enable},#{deleted},LAST_INSERT_ID()-->
    <!--)-->
    <!--</insert>-->

    <update id="deleteAll" parameterType="list">
        update hst_table_basic
        set deleted = 1
        where
        guid in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>


    <select id="maxSort" resultType="java.lang.Integer">
        select IFNULL(max(sort),0) from hst_table_basic where store_guid = #{storeGuid} and area_guid = #{areaGuid} and deleted = 0;
    </select>

</mapper>
