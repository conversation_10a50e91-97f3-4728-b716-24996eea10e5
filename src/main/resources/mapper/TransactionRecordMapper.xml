<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.TransactionRecordMapper">


    <resultMap id="handoverMap" type="com.holderzone.saas.store.dto.trade.HandoverDTO">
        <result column="store_guid" property="storeGuid"/>
        <result column="staff_guid" property="userGuid"/>
        <result column="staff_name" property="userName"/>
        <result column="payment_type" property="paymentType"/>
        <result column="payment_type_name" property="paymentName"/>
        <result column="amount" property="amount"/>
        <result column="sum_item_order" property="sumItemOrder"/>
    </resultMap>

    <select id="handover" parameterType="com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO"
            resultMap="handoverMap">
        SELECT
        a.store_guid,
        a.staff_guid,
        (SELECT staff_name FROM hst_transaction_record
        <where>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and staff_guid = #{dto.userGuid}
            </if>
        </where>
        LIMIT 1) as staff_name,
        a.payment_type,
        a.payment_type_name,
        SUM( case when a.is_multiple_agg_pay = 1 then a.amount - IFNULL(a.refund_amount, 0) else a.amount end) AS amount
        FROM
        hst_transaction_record AS a
        left join hst_order o on o.guid = a.order_guid and o.is_delete = 0
        <where>
            a.state = 4 and o.state != 12
            and a.is_delete = 0
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                and a.store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                and a.create_time &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                and a.create_time &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and a.staff_guid = #{dto.userGuid}
            </if>
        </where>
        GROUP BY
        a.payment_type,
        a.payment_type_name
    </select>

    <select id="handoverOrderCount" parameterType="com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO"
            resultType="int">
        select count(*)
        from
        ( select a.order_guid
        from hst_transaction_record a
        <where>
            (a.state = 4)
            and a.trade_type in (1,2,3,4,5)
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                and a.store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                and a.create_time &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                and a.create_time &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and a.staff_guid = #{dto.userGuid}
            </if>
        </where>
        GROUP BY a.order_guid ) t
    </select>

    <select id="calExcessAmount" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(sum(o.excess_amount),0)
        FROM
            hst_order o
        <where>
            o.state = 4 and o.is_delete = 0 and o.recovery_type in (1, 3)
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                and o.store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                and o.checkout_time &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                and o.checkout_time &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and o.checkout_staff_guid = #{dto.userGuid}
            </if>
        </where>
    </select>

    <select id="handoverOrderGuids" resultType="java.lang.String">
        SELECT
            distinct a.order_guid
        FROM
            hst_transaction_record AS a
        left join hst_order o on o.guid = a.order_guid and o.is_delete = 0
        <where>
            (a.state = 4)
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                and a.store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                and a.create_time &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                and a.create_time &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and a.staff_guid = #{dto.userGuid}
            </if>
        </where>
    </select>
    <select id="retailHandover" resultType="com.holderzone.saas.store.dto.trade.HandoverDTO" resultMap="handoverMap">
        SELECT
        a.store_guid,
        a.staff_guid,
        (SELECT staff_name FROM hst_retail_transaction_record
        <where>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and staff_guid = #{dto.userGuid}
            </if>
        </where>
        LIMIT 1) as staff_name,
        a.payment_type,
        a.payment_type_name,
        SUM( a.amount ) AS amount
        FROM
        hst_retail_transaction_record AS a
        <where>
            a.state = 4
            and a.is_delete = 0
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                and a.store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                and a.gmt_create &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                and a.gmt_create &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and a.staff_guid = #{dto.userGuid}
            </if>
        </where>
        GROUP BY
        a.payment_type,
        a.payment_type_name
    </select>
</mapper>
