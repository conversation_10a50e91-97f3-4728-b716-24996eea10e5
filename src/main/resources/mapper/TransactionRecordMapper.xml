<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.retail.mapper.TransactionRecordMapper">


    <resultMap id="handoverMap" type="com.holderzone.saas.store.dto.trade.HandoverDTO">
        <result column="store_guid" property="storeGuid"/>
        <result column="staff_guid" property="userGuid"/>
        <result column="staff_name" property="userName"/>
        <result column="payment_type" property="paymentType"/>
        <result column="payment_type_name" property="paymentName"/>
        <result column="amount" property="amount"/>
        <result column="sum_item_order" property="sumItemOrder"/>
    </resultMap>

    <select id="handover" parameterType="com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO"
            resultMap="handoverMap">
                SELECT
                a.store_guid,
                a.staff_guid,
                a.staff_name,
                a.payment_type,
                a.payment_type_name,
                SUM( a.amount ) AS amount
                FROM
                hst_transaction_record AS a
                <where>
                 (a.state = 4)
                    <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                        and a.store_guid = #{dto.storeGuid}
                    </if>
                    <if test="null!=dto.gmtCreate">
                        and a.create_time &gt;= #{dto.gmtCreate}
                    </if>
                    <if test="null!=dto.gmtModified">
                        and a.create_time &lt;= #{dto.gmtModified}
                    </if>
                    <if test="null!=dto.userGuid and dto.userGuid!=''">
                        and a.staff_guid = #{dto.userGuid}
                    </if>
                </where>
                GROUP BY
                a.payment_type,
                a.payment_type_name,
                a.staff_name
    </select>

    <select id="handoverOrderCount" parameterType="com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO"
            resultType="int">
          select count(*)
          from
            (        select a.order_guid
        from hst_transaction_record a
        <where>
            (a.state = 4)
            and a.trade_type in (1,2,3,4,5)
            <if test="null!=dto.storeGuid and dto.storeGuid!=''">
                and a.store_guid = #{dto.storeGuid}
            </if>
            <if test="null!=dto.gmtCreate">
                and a.create_time &gt;= #{dto.gmtCreate}
            </if>
            <if test="null!=dto.gmtModified">
                and a.create_time &lt;= #{dto.gmtModified}
            </if>
            <if test="null!=dto.userGuid and dto.userGuid!=''">
                and a.staff_guid = #{dto.userGuid}
            </if>
        </where>
        GROUP BY a.order_guid ) t
    </select>
</mapper>
