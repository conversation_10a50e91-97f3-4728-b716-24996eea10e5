<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.member.mapper.TransactionRecordMapper">
    <resultMap id="BaseResultMap" type="com.holderzone.saas.store.member.domain.TransactionRecordDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="is_delete" jdbcType="BIT" property="isDelete"/>
        <result column="transaction_record_guid" jdbcType="VARCHAR" property="transactionRecordGuid"/>
        <result column="member_guid" jdbcType="VARCHAR" property="memberGuid"/>
        <result column="sequence_no" jdbcType="VARCHAR" property="sequenceNo"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="business_day" jdbcType="DATE" property="businessDay"/>
        <result column="transaction_time" jdbcType="TIMESTAMP" property="transactionTime"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="balance" jdbcType="DECIMAL" property="balance"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="prepaid_type_name" jdbcType="VARCHAR" property="prepaidTypeName"></result>
        <result column="prepaid_type" jdbcType="TINYINT" property="prepaidType"/>
        <result column="account_number" jdbcType="VARCHAR" property="accountNumber"/>
        <result column="staff_guid" jdbcType="VARCHAR" property="staffGuid"/>
        <result column="staff_name" jdbcType="VARCHAR" property="staffName"/>
        <result column="store_guid" jdbcType="VARCHAR" property="storeGuid"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, gmt_create, gmt_modified, is_delete, transaction_record_guid, member_guid, sequence_no, 
    order_no,business_day,transaction_time, fee, balance, `type`,prepaid_type_name,prepaid_type, account_number, staff_guid,
    staff_name, store_guid, store_name
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hsm_transaction_record
        where transaction_record_guid = #{transactionRecordGuid,jdbcType=VARCHAR}
        order by transaction_time desc
    </select>
    <select id="selectByMemberGuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hsm_transaction_record
        where is_delete = 0

        <include refid="transaction_record_list_query"/>
    </select>

    <sql id="transaction_record_list_query">
        <if test="transactionRecordDTO.memberGuid != null">
            and member_guid = #{transactionRecordDTO.memberGuid}
        </if>
        <if test="transactionRecordDTO.type != null">
            and type = #{transactionRecordDTO.type}
        </if>
        order by transaction_time desc
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from hsm_transaction_record
    where transaction_record_guid = #{transactionRecordGuid,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.holderzone.saas.store.member.domain.TransactionRecordDO">
    insert into hsm_transaction_record (id, gmt_create, gmt_modified, 
      is_delete, transaction_record_guid, member_guid, 
      sequence_no, order_no, business_day,transaction_time,
      fee, balance, `type`,prepaid_type_name,
      prepaid_type, account_number, staff_guid, 
      staff_name, store_guid, store_name)
    values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=BIT}, #{transactionRecordGuid,jdbcType=VARCHAR}, #{memberGuid,jdbcType=VARCHAR}, 
      #{sequenceNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{transactionTime,jdbcType=TIMESTAMP},
      #{fee,jdbcType=DECIMAL}, #{balance,jdbcType=DECIMAL}, #{type,jdbcType=TINYINT},#{prepaidTypeName,jdbcType=VARCHAR},
      #{prepaidType,jdbcType=TINYINT}, #{accountNumber,jdbcType=VARCHAR}, #{staffGuid,jdbcType=VARCHAR}, 
      #{staffName,jdbcType=VARCHAR}, #{storeGuid,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.holderzone.saas.store.member.domain.TransactionRecordDO">
        insert into hsm_transaction_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="transactionRecordGuid != null">
                transaction_record_guid,
            </if>
            <if test="memberGuid != null">
                member_guid,
            </if>
            <if test="sequenceNo != null">
                sequence_no,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="transactionTime != null">
                transaction_time,
            </if>
            <if test="businessDay != null">
                business_day,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="balance != null">
                balance,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="prepaidTypeName!=null and prepaidTypeName!=''">
                prepaid_type_name,

            </if>

            <if test="prepaidType != null">
                prepaid_type,
            </if>
            <if test="accountNumber != null">
                account_number,
            </if>
            <if test="staffGuid != null">
                staff_guid,
            </if>
            <if test="staffName != null">
                staff_name,
            </if>
            <if test="storeGuid != null">
                store_guid,
            </if>
            <if test="storeName != null">
                store_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BIT},
            </if>
            <if test="transactionRecordGuid != null">
                #{transactionRecordGuid,jdbcType=VARCHAR},
            </if>
            <if test="memberGuid != null">
                #{memberGuid,jdbcType=VARCHAR},
            </if>
            <if test="sequenceNo != null">
                #{sequenceNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="transactionTime != null">
                #{transactionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="balance != null">
                #{balance,jdbcType=DECIMAL},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="prepaidTypeName!=null">
                #{prepaidTypeName,jdbcType=VARCHAR},

            </if>
            <if test="prepaidType != null">
                #{prepaidType,jdbcType=TINYINT},
            </if>
            <if test="accountNumber != null">
                #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="staffGuid != null">
                #{staffGuid,jdbcType=VARCHAR},
            </if>
            <if test="staffName != null">
                #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="storeGuid != null">
                #{storeGuid,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                #{storeName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.holderzone.saas.store.member.domain.TransactionRecordDO">
        update hsm_transaction_record
        <set>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=BIT},
            </if>
            <if test="transactionRecordGuid != null">
                transaction_record_guid = #{transactionRecordGuid,jdbcType=VARCHAR},
            </if>
            <if test="memberGuid != null">
                member_guid = #{memberGuid,jdbcType=VARCHAR},
            </if>
            <if test="sequenceNo != null">
                sequence_no = #{sequenceNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="transactionTime != null">
                transaction_time = #{transactionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="transactionTime != null">
                business_day = #{businessDay,jdbcType=TIMESTAMP},
            </if>
            <if test="fee != null">
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="balance != null">
                balance = #{balance,jdbcType=DECIMAL},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=TINYINT},
            </if>
            <if test="prepaidTypeName!=null">
                prepaid_type_name=#{prepaidTypeName,jdbcType=VARCHAR},

            </if>
            <if test="prepaidType != null">
                prepaid_type = #{prepaidType,jdbcType=TINYINT},
            </if>
            <if test="accountNumber != null">
                account_number = #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="staffGuid != null">
                staff_guid = #{staffGuid,jdbcType=VARCHAR},
            </if>
            <if test="staffName != null">
                staff_name = #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="storeGuid != null">
                store_guid = #{storeGuid,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
        </set>
        where transaction_record_guid = #{transactionRecordGuid,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.holderzone.saas.store.member.domain.TransactionRecordDO">
    update hsm_transaction_record
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      transaction_record_guid = #{transactionRecordGuid,jdbcType=VARCHAR},
      member_guid = #{memberGuid,jdbcType=VARCHAR},
      sequence_no = #{sequenceNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      business_day=#{businessDay,jdbcType=DATE},
      transaction_time = #{transactionTime,jdbcType=TIMESTAMP},
      fee = #{fee,jdbcType=DECIMAL},
      balance = #{balance,jdbcType=DECIMAL},
      `type` = #{type,jdbcType=TINYINT},
      prepaid_type_name=#{prepaidTypeName,jdbcType=VARCHAR},
      prepaid_type = #{prepaidType,jdbcType=TINYINT},
      account_number = #{accountNumber,jdbcType=VARCHAR},
      staff_guid = #{staffGuid,jdbcType=VARCHAR},
      staff_name = #{staffName,jdbcType=VARCHAR},
      store_guid = #{storeGuid,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR}
    where  transaction_record_guid = #{transactionRecordGuid,jdbcType=VARCHAR}
  </update>


    <select id="selectMemberTransactions" resultMap="BaseTransactionRecordDO">
        select
        id,
        gmt_create,
        gmt_modified,
        transaction_record_guid,
        member_guid,
        sequence_no,
        order_no,
        business_day,
        transaction_time,
        fee,
        balance,
        type,
        prepaid_type_name,
        prepaid_type,
        account_number,
        staff_guid,
        staff_name,
        store_guid,
        store_name
        from hsm_transaction_record where is_delete=0 and member_guid=#{memberPayRecordReqDTO.memberGuid}
        <if test="memberPayRecordReqDTO.type==1">
            and type!=0
        </if>
        <if test="memberPayRecordReqDTO.type==0">
            and type=0
        </if>
        <if test="memberPayRecordReqDTO.orderNo!=null and memberPayRecordReqDTO.orderNo!='' ">
            and order_no like concat(concat('%',#{memberPayRecordReqDTO.orderNo}),'%')
        </if>
        <if test="memberPayRecordReqDTO.tradingTimeBegin!=null">
            and transaction_time &gt;=#{memberPayRecordReqDTO.tradingTimeBegin}
        </if>
        <if test="memberPayRecordReqDTO.tradingTimeEnd!=null">
            and transaction_time &lt;=#{memberPayRecordReqDTO.tradingTimeEnd}
        </if>
        <if test="memberPayRecordReqDTO.sequenceNo!=null and memberPayRecordReqDTO.sequenceNo!=''">
            and sequence_no like concat(concat('%',#{memberPayRecordReqDTO.sequenceNo}),'%')
        </if>
        order by transaction_time desc
    </select>
    <resultMap id="BaseTransactionRecordDO" type="com.holderzone.saas.store.member.domain.TransactionRecordDO">
        <id column="id" property="id"></id>
        <result column="gmt_create" property="gmtCreate"></result>
        <result column="gmt_modified" property="gmtModified"></result>

        <result column="transaction_record_guid" property="transactionRecordGuid"></result>
        <result column="member_guid" property="memberGuid"></result>
        <result column="sequence_no" property="sequenceNo"></result>
        <result column="order_no" property="orderNo"></result>
        <result column="business_day" property="businessDay"></result>
        <result column="transaction_time" property="transactionTime"></result>
        <result column="fee" property="fee"></result>
        <result column="balance" property="balance"></result>
        <result column="type" property="type"></result>
        <result column="prepaid_type_name" property="prepaidTypeName"></result>
        <result column="prepaid_type" property="prepaidType"></result>
        <result column="account_number" property="accountNumber"></result>
        <result column="staff_guid" property="staffGuid"></result>
        <result column="staff_name" property="staffName"></result>
        <result column="store_guid" property="storeGuid"></result>
        <result column="store_name" property="storeName"></result>
    </resultMap>

    <resultMap id="MemberConsumeResult" type="com.holderzone.saas.store.dto.order.response.daily.MemberConsumeRespDTO">
        <result column="recharge_num" property="prepaidCount"/>
        <result column="recharge_fee" property="prepaidAmount"/>
        <result column="recharge_give" property="prepaidGiveAmount"/>
        <result column="recharge_income" property="prepaidTakeInAmount"/>
    </resultMap>

    <select id="selectBySequenceNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hsm_transaction_record
        where sequence_no = #{sequenceNo,jdbcType=VARCHAR}
        order by transaction_time asc

    </select>
    <select id="getMemberRechargeRecord"
            resultMap="MemberConsumeResult">
          SELECT
            COUNT( CR.id ) AS recharge_num,
            SUM( TR.fee ) AS recharge_fee,
            0 AS recharge_give,
            SUM( TR.fee ) AS recharge_income
         FROM
            hsm_charge_record AS CR
            INNER JOIN hsm_transaction_record AS TR ON CR.guid = TR.order_no
        WHERE
            CR.state = 2
            AND CR.store_guid = #{dto.storeGuid}
            AND TR.type = 0
            AND paid_time BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
    </select>

    <select id="getMemberRechargeStore"
            resultMap="MemberConsumeResult">
        SELECT
	    count(1) AS recharge_num,
	    SUM(TR.fee) AS recharge_fee
       FROM
	     hsm_charge_record AS CR
	      INNER JOIN hsm_transaction_record AS TR ON CR.guid = TR.order_no
       WHERE
        CR.state = 2
        AND TR.type = 0
        AND TR.store_guid = #{dto.storeGuid}
        AND TR.staff_guid = #{dto.staffGuid}
        AND paid_time BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
    </select>
    
    <select id="getBusinessMemberRechargeEarnings" resultType="com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO">
        SELECT
        CR.payment_type AS gatherCode,
        CR.payment_type_name AS gatherName,
        SUM(TR.fee) AS prepaidAmount
        FROM
        hsm_charge_record AS CR
        INNER JOIN hsm_transaction_record AS TR ON CR.guid = TR.order_no
        WHERE
        CR.state = 2
        AND TR.type = 0
        AND TR.store_guid = #{dto.storeGuid}
        AND paid_time BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        GROUP BY CR.payment_type,CR.payment_type_name
    </select>

    <select id="getBusinessMemberRechargeEarningsHandover" resultType="com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO">
        SELECT
        CR.payment_type AS gatherCode,
        CR.payment_type_name AS gatherName,
        SUM(TR.fee) AS prepaidAmount
        FROM
        hsm_charge_record AS CR
        INNER JOIN hsm_transaction_record AS TR ON CR.guid = TR.order_no
        WHERE
        CR.state = 2
        AND TR.type = 0
        AND TR.store_guid = #{dto.storeGuid}
        AND paid_time BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND TR.staff_guid = #{dto.staffGuid}
        GROUP BY CR.payment_type,CR.payment_type_name
    </select>
</mapper>