<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.ItemTemplateMapper">

    <resultMap id="ItemTemplateDO" type="com.holderzone.saas.store.item.entity.domain.ItemTemplateDO">
        <result column="guid" property="guid"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="template_name" property="templateName"/>
        <result column="description" property="description"/>
        <result column="effective_end_time" property="effectiveEndTime"/>
        <result column="effective_start_time" property="effectiveStartTime"/>
        <result column="is_it_activated" property="isItActivated"/>
        <result column="periodic_mode" property="periodicMode"/>
    </resultMap>

    <resultMap id="ItemTemplateRespDTO" type="com.holderzone.saas.store.dto.item.resp.ItemTemplateRespDTO">
        <result column="guid" property="guid"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="template_name" property="templateName"/>
        <result column="effective_start_time" property="effectiveStartTime"/>
        <result column="effective_end_time" property="effectiveEndTime"/>
        <result column="is_it_activated" property="isItActivated"/>
        <result column="current_status" property="currentStatus"/>
        <result column="periodic_mode" property="periodicMode"/>
        <result column="description" property="description"/>
    </resultMap>

    <select id="getStoreItemTemplates"  resultMap="ItemTemplateRespDTO">
        SELECT
        guid,
        store_guid,
        template_name,
        effective_start_time,
        effective_end_time,
        is_it_activated,
        (
            CASE
                WHEN effective_start_time &lt;=  NOW() and effective_end_time &gt;=NOW() THEN 3
                WHEN effective_start_time &gt;  NOW() and effective_end_time &gt; NOW() THEN 2
                WHEN  effective_start_time &lt; NOW() and effective_end_time  &lt; NOW() THEN 1
            END
        ) as current_status ,
        periodic_mode,
        description
        FROM
        hsi_item_template
        <where>
            store_guid = #{dto.storeGuid}
            AND is_delete = 0
            <if test="dto.itActivated !='' and dto.itActivated != null">
                AND is_it_activated =  #{dto.itActivated}
            </if>
            <if test="dto.startTime !='' and dto.startTime != null">
                AND effective_start_time &gt;= concat(#{dto.startTime},' 00:00:00')
            </if>
            <if test="dto.endTime !='' and dto.endTime != null">
                AND effective_end_time &lt;= concat(#{dto.endTime},' 23:59:59')
            </if>
            <if test="dto.keywords !='' and dto.keywords != null">
                AND template_name like concat('%',#{dto.keywords},'%')
            </if>
            <!--  1: 当前状态 1：未开始 2：已结束  3：进行中-->
            <if test="dto.status !='' and dto.status != null">
                <if test="dto.status == 1">
                    AND effective_start_time &lt; NOW() and effective_end_time &lt; NOW()
                </if>
                <if test="dto.status == 2">
                    AND effective_start_time &gt;  NOW() and effective_end_time &gt; NOW()
                </if>
                <if test="dto.status == 3">
                    AND effective_start_time &lt;=  NOW() and effective_end_time &gt;=NOW()
                </if>
            </if>
        </where>
        ORDER BY  current_status DESC , id DESC
    </select>


    <select id="getStoreCurrentUsedTemplateName" resultMap="ItemTemplateDO">
        SELECT
        guid,
        store_guid,
        template_name,
        description,
        effective_end_time,
        effective_start_time,
        is_it_activated,
        periodic_mode
        FROM
        hsi_item_template
        WHERE
        store_guid = #{guid}
        AND is_it_activated = 1
        AND effective_start_time &lt;= NOW()
        AND effective_end_time &gt;= NOW()
        AND is_delete = 0
    </select>
    <select id="getStoreGuidByCode" resultType="java.lang.String">
        SELECT
            t.store_guid
        FROM
            hsi_item_template t
        LEFT JOIN hsi_item_t_menu m ON t.guid = m.template_guid
        <where>
        <if test="type == 0">
            t.guid = #{guid}
        </if><if test="type == 1">
                m.guid =  #{guid}
            </if>
        </where>
        AND t.is_delete = 0
    </select>
</mapper>
