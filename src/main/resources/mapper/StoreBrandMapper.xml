<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.organization.mapper.StoreBrandMapper">

    <resultMap id="queryStoreResultMap" type="com.holderzone.saas.store.dto.organization.StoreDTO">
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="belongBrandGuid" property="belongBrandGuid"/>
        <result column="belongBrandName" property="belongBrandName"/>
        <result column="parent_ids" property="parentIds"/>
        <result column="business_start" property="businessStart"/>
        <result column="business_end" property="businessEnd"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_tel" property="contactTel"/>
        <result column="province_code" property="provinceCode"/>
        <result column="province_name" property="provinceName"/>
        <result column="city_code" property="cityCode"/>
        <result column="city_name" property="cityName"/>
        <result column="county_code" property="countyCode"/>
        <result column="county_name" property="countyName"/>
        <result column="address_detail" property="addressDetail"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="is_enable" property="isEnable"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="photos" property="photos"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="create_user_guid" property="createUserGuid"/>
        <result column="modified_user_guid" property="modifiedUserGuid"/>
        <result column="logo_url" property="logoUrl"/>
        <result column="opening_hours" property="openingHours"/>
        <result column="is_opening" property="isOpening"/>
        <result column="store_grade" property="storeGrade"/>
        <result column="store_online_sales30" property="storeOnlineSales30"/>
        <result column="is_item_upload" property="isItemUpload"/>
        <result column="store_notification" property="storeNotification"/>
        <result column="store_door_photo" property="storeDoorPhoto"/>
        <result column="is_full_reduce" property="isFullReduce"/>
        <result column="is_full_discount" property="isFullDiscount"/>
        <result column="is_self_takeaway" property="isSelfTakeaway"/>
        <result column="is_takeaway" property="isTakeaway"/>
        <result column="is_support_distribution" property="isSupportDistribution"/>
        <result column="is_support_order" property="isSupportOrder"/>
        <result column="pass_status" property="passStatus"/>
        <result column="is_online" property="isOnline"/>
        <result column="is_self_build_items" property="isSelfBuildItems"/>
        <result column="is_bu_accounts" property="isBuAccounts"/>
        <result column="is_show_cash" property="isShowCash"/>
        <result column="can_open_table" property="canOpenTable"/>
        <result column="is_multi_handover" property="isMultiHandover"/>
    </resultMap>

    <insert id="save">
        insert IGNORE into hso_r_store_brand (guid, store_guid, brand_guid, create_user_guid, modified_user_guid,
        gmt_create, gmt_modified )
        values (#{guid,jdbcType=VARCHAR}, #{storeGuid,jdbcType=VARCHAR},
        #{brandGuid,jdbcType=VARCHAR}, #{createUserGuid,jdbcType=VARCHAR}, #{modifiedUserGuid,jdbcType=VARCHAR}, now(),now())
    </insert>

    <select id="getStoreBrandDetail" resultType="com.holderzone.saas.store.dto.organization.BrandStoreDetailDTO">
        select ho.guid as storeGuid,ho.name as storeName,hb.guid as brandGuid,hb.name as brandName,hb.logo_url as brandLogoUrl
        from hso_organization ho,hso_r_store_brand hrsb,hso_brand hb
        where ho.guid = hrsb.store_guid
        and hrsb.brand_guid = hb.guid
        and ho.guid = #{storeGuid}
        and hb.guid = #{brandGuid}
        limit 1
    </select>

    <select id="queryStore" resultMap="queryStoreResultMap">
        SELECT
            o.guid,
            o.`code`,
            o.`name`,
            b.guid AS belongBrandGuid,
            b.name AS belongBrandName,
            o.parent_ids,
            o.business_start,
            o.business_end,
            o.contact_name,
            o.contact_tel,
            o.province_code,
            o.province_name,
            o.city_code,
            o.city_name,
            o.county_code,
            o.county_name,
            o.address_detail,
            o.longitude,
            o.latitude,
            o.is_enable,
            o.is_deleted,
            o.photos,
            o.gmt_create,
            o.gmt_modified,
            o.create_user_guid,
            o.modified_user_guid,
            o.logo_url,
            o.opening_hours,
            o.is_opening,
            o.store_grade,
            o.store_online_sales30,
            o.is_item_upload,
            o.store_notification,
            o.store_door_photo,
            o.is_full_reduce,
            o.is_full_discount,
            o.is_self_takeaway,
            o.is_takeaway,
            o.is_support_distribution,
            o.is_support_order,
            o.pass_status,
            o.is_online,
            o.is_self_build_items,
            o.is_bu_accounts,
            o.is_show_cash,
            o.can_open_table,
            o.is_multi_handover
        FROM
            `hso_r_store_brand` sb
             JOIN hso_organization o ON o.guid = sb.store_guid
             JOIN hso_brand b ON b.guid = sb.brand_guid
        WHERE
            o.is_deleted = 0
            AND o.is_enable = 1
            AND sb.brand_guid = #{req.brandGuid}
    </select>

</mapper>
