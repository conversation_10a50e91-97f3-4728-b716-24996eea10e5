<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.staff.mapper.UserMapper">

    <resultMap id="UserMap" type="com.holderzone.saas.store.staff.entity.read.UserReadDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="guid" jdbcType="VARCHAR" property="guid"/>
        <id column="enterprise_no" jdbcType="VARCHAR" property="enterpriseNo"/>
        <id column="account" jdbcType="VARCHAR" property="account"/>
        <id column="password" jdbcType="VARCHAR" property="password"/>
        <id column="auth_code" jdbcType="VARCHAR" property="authCode"/>
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <id column="phone" jdbcType="VARCHAR" property="phone"/>
        <id column="org_guid" jdbcType="VARCHAR" property="orgGuid"/>
        <id column="office_code" jdbcType="VARCHAR" property="officeCode"/>
        <id column="id_card_no" jdbcType="VARCHAR" property="idCardNo"/>
        <id column="id_card_address" jdbcType="VARCHAR" property="idCardAddress"/>
        <id column="address" jdbcType="VARCHAR" property="address"/>
        <id column="birthday" jdbcType="TIMESTAMP" property="birthday"/>
        <id column="on_boarding_time" jdbcType="TIMESTAMP" property="onBoardingTime"/>
        <id column="create_staffGuid" jdbcType="VARCHAR" property="createStaffGuid"/>
        <id column="update_staffGuid" jdbcType="VARCHAR" property="updateStaffGuid"/>
        <id column="is_enable" jdbcType="TINYINT" property="isEnable"/>
        <id column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <id column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <id column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <resultMap id="UserDetailMap" type="com.holderzone.saas.store.staff.entity.read.UserReadDO">
        <id column="guid" jdbcType="VARCHAR" property="guid"/>
        <id column="account" jdbcType="VARCHAR" property="account"/>
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <id column="phone" jdbcType="VARCHAR" property="phone"/>
        <id column="org_guid" jdbcType="VARCHAR" property="orgGuid"/>
        <id column="office_code" jdbcType="VARCHAR" property="officeCode"/>
        <id column="office_name" jdbcType="VARCHAR" property="officeName"/>
        <id column="id_card_no" jdbcType="VARCHAR" property="idCardNo"/>
        <id column="id_card_address" jdbcType="VARCHAR" property="idCardAddress"/>
        <id column="address" jdbcType="VARCHAR" property="address"/>
        <id column="birthday" jdbcType="TIMESTAMP" property="birthday"/>
        <id column="on_boarding_time" jdbcType="TIMESTAMP" property="onBoardingTime"/>
        <id column="is_enable" jdbcType="TINYINT" property="isEnable"/>
        <id column="is_waiter" jdbcType="TINYINT" property="isWaiter"/>
        <id column="face_code" jdbcType="VARCHAR" property="faceCode"/>
        <id column="is_receive" jdbcType="TINYINT" property="isReceive"/>
        <collection property="roles" ofType="com.holderzone.saas.store.staff.entity.domain.RoleDO">
            <result column="role_guid" jdbcType="VARCHAR" property="guid"/>
            <result column="role_name" jdbcType="VARCHAR" property="name"/>
        </collection>
    </resultMap>

    <resultMap id="UserPageMap" type="com.holderzone.saas.store.staff.entity.read.UserReadDO">
        <id column="guid" jdbcType="VARCHAR" property="guid"/>
        <id column="account" jdbcType="VARCHAR" property="account"/>
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <id column="phone" jdbcType="VARCHAR" property="phone"/>
        <id column="org_guid" jdbcType="VARCHAR" property="orgGuid"/>
        <id column="is_enable" jdbcType="TINYINT" property="isEnable"/>
        <collection property="roles" javaType="java.util.ArrayList"
                    ofType="com.holderzone.saas.store.staff.entity.domain.RoleDO"
                    select="queryUserRolesInline" column="{userGuid=guid}">
        </collection>
    </resultMap>

    <insert id="saveUser">
        INSERT INTO `hss_user`
        (
        <trim suffixOverrides=",">
            guid, is_deleted,
            <if test="enterpriseNo != null">
                enterprise_no,
            </if>
            <if test="account != null">
                account,
            </if>
            <if test=" password != null">
                password,
            </if>
            <if test=" authCode != null">
                auth_code,
            </if>
            <if test=" name != null">
                name,
            </if>
            <if test=" phone != null">
                phone,
            </if>
            <if test=" orgGuid != null">
                org_guid,
            </if>
            <if test=" officeCode != null">
                office_code,
            </if>
            <if test=" officeName != null">
                office_name,
            </if>
            <if test=" idCardNo != null">
                id_card_no,
            </if>
            <if test=" idCardAddress != null">
                id_card_address,
            </if>
            <if test=" address != null">
                address,
            </if>
            <if test=" birthday != null">
                birthday,
            </if>
            <if test=" onBoardingTime != null">
                on_boarding_time,
            </if>
            <if test=" discountThreshold != null">
                discount_threshold,
            </if>
            <if test=" allowanceThreshold != null">
                allowance_threshold,
            </if>
            <if test=" productDiscountThreshold != null">
                product_discount_threshold,
            </if>
            <if test=" refundThreshold != null">
                refund_threshold,
            </if>
            <if test=" rolesDistributable != null">
                roles_distributable,
            </if>
            <if test=" createStaffGuid != null">
                create_staff_guid,
            </if>
            <if test=" updateStaffGuid != null">
                update_staff_guid,
            </if>
            <if test=" isWaiter != null">
                is_waiter,
            </if>
            <if test=" isEnable != null">
                is_enable,
            </if>
            <if test=" gmtCreate != null">
                gmt_create,
            </if>
            <if test=" regType != null">
                reg_type,
            </if>
            <if test=" isReceive != null">
                is_receive,
            </if>
        </trim>
        )
        VALUES
        (
        <trim suffixOverrides=",">
            #{guid}, 0,
            <if test="enterpriseNo != null">
                #{enterpriseNo},
            </if>
            <if test="account != null">
                #{account},
            </if>
            <if test=" password != null">
                #{password},
            </if>
            <if test=" authCode != null">
                #{authCode},
            </if>
            <if test=" name != null">
                #{name},
            </if>
            <if test=" phone != null">
                #{phone},
            </if>
            <if test=" orgGuid != null">
                #{orgGuid},
            </if>
            <if test=" officeCode != null">
                #{officeCode},
            </if>
            <if test=" officeName != null">
                #{officeName},
            </if>
            <if test=" idCardNo != null">
                #{idCardNo},
            </if>
            <if test=" idCardAddress != null">
                #{idCardAddress},
            </if>
            <if test=" address != null">
                #{address},
            </if>
            <if test=" birthday != null">
                #{birthday},
            </if>
            <if test=" onBoardingTime != null">
                #{onBoardingTime},
            </if>
            <if test=" discountThreshold != null">
                #{discountThreshold},
            </if>
            <if test=" allowanceThreshold != null">
                #{allowanceThreshold},
            </if>
            <if test=" productDiscountThreshold != null">
                #{productDiscountThreshold},
            </if>
            <if test=" refundThreshold != null">
                #{refundThreshold},
            </if>
            <if test=" rolesDistributable != null">
                #{rolesDistributable},
            </if>
            <if test=" createStaffGuid != null">
                #{createStaffGuid},
            </if>
            <if test=" updateStaffGuid != null">
                #{updateStaffGuid},
            </if>
            <if test=" isWaiter != null">
                #{isWaiter},
            </if>
            <if test=" isEnable != null">
                #{isEnable},
            </if>
            <if test=" gmtCreate != null">
                #{gmtCreate},
            </if>
            <if test=" regType != null">
                #{regType},
            </if>
            <if test=" isReceive != null">
                #{isReceive},
            </if>
        </trim>
        )
        ON DUPLICATE KEY UPDATE
        name=if((ISNULL(VALUES(name))=0 AND LENGTH(VALUES(name)))>0,VALUES(name),name),
        phone=if((ISNULL(VALUES(phone))=0 AND LENGTH(VALUES(phone)))>0,VALUES(phone),phone),
        password=if((ISNULL(VALUES(password))=0 AND LENGTH(VALUES(password)))>0,VALUES(password),password),
        org_guid=if((ISNULL(VALUES(org_guid))=0 AND LENGTH(VALUES(org_guid)))>0,VALUES(org_guid),org_guid),
        is_enable=1,
        is_deleted=0
    </insert>
    <update id="updateBatchFromCloud">
        <foreach collection="userList" item="user" separator=";">
            update hss_user set phone = #{user.phone}, name = #{user.name} where guid = #{user.guid}
        </foreach>
    </update>


    <select id="queryUserDetail" resultMap="UserDetailMap"
            parameterType="com.holderzone.saas.store.staff.entity.domain.UserDO">
        select
          u.guid, u.account, u.name, u.phone, u.org_guid, u.office_code, u.office_name, u.id_card_no,
          u.id_card_address, u.address, u.birthday, u.on_boarding_time, u.is_enable,u.is_waiter,
          r.guid as role_guid, r.name as role_name,u.face_code,u.is_receive
        from hss_user u
        left join hss_r_user_role ur on u.guid = ur.user_guid
        left join hss_role r on ur.role_guid = r.guid and r.is_deleted = 0
        where u.guid = #{guid} and u.is_deleted = 0
    </select>

    <select id="pageQueryUser" resultMap="UserPageMap"
            parameterType="com.holderzone.saas.store.staff.entity.query.UserCondQuery">
        select
        u.guid, u.account, u.name, u.phone, u.org_guid, u.is_enable
        from hss_user u
        where u.is_deleted = 0 and u.account != '100000'
        <if test="userCondQuery.createStaffGuid != null and userCondQuery.createStaffGuid != ''">
            and u.create_staff_guid = #{userCondQuery.createStaffGuid}
        </if>
        <if test="userCondQuery.isEnable != null">
            and u.is_enable = #{userCondQuery.isEnable}
        </if>
        <if test="userCondQuery.searchKey != null and userCondQuery.searchKey != ''">
            and (
            u.name like concat("%", #{userCondQuery.searchKey}, "%")
            or u.phone like concat("%", #{userCondQuery.searchKey}, "%")
            or u.account like concat("%", #{userCondQuery.searchKey}, "%")
            )
        </if>

        <if test="userCondQuery.orgGuidsWithChildren != null and userCondQuery.orgGuidsWithChildren.size > 0">
            and
            <foreach collection="userCondQuery.orgGuidsWithChildren" item="orgGuid" separator="or" open="(" close=")">
                u.org_guid = #{orgGuid}
            </foreach>
        </if>
        order by u.gmt_create desc,u.id desc
    </select>

    <select id="queryUserRolesInline" resultType="com.holderzone.saas.store.staff.entity.domain.RoleDO"
            parameterType="java.util.HashMap">
        select r.guid, r.name
        from hss_r_user_role ur
        left join hss_role r on ur.role_guid = r.guid and r.is_deleted = 0
        where ur.user_guid = #{userGuid}
    </select>

    <select id="queryUserSourceOfAdmin" resultType="com.holderzone.saas.store.dto.user.MenuSourceDTO"
            parameterType="com.holderzone.saas.store.staff.entity.query.UserSourceQuery">
        select
          ss.source_guid as sourceGuid, ss.source_name as sourceName, ss.source_code as sourceCode
        from hss_store_source ss
         inner join hss_product p on (
            ss.product_guid = p.product_guid
            and p.gmt_product_start <![CDATA[<=]]> #{now}
            and (p.gmt_product_end <![CDATA[>=]]> #{now} or p.gmt_product_end is null)
            and p.is_deleted = 0
        )
        where ss.terminal_code = #{terminalCode}
        and (ss.store_guid = #{storeGuid} or ss.store_guid is null)
    </select>

    <select id="queryUserSource" resultType="com.holderzone.saas.store.dto.user.MenuSourceDTO"
            parameterType="com.holderzone.saas.store.staff.entity.query.UserSourceQuery">
        select
          ss.source_guid as sourceGuid, ss.source_name as sourceName, ss.source_code as sourceCode
        from hss_user u
        inner join hss_r_user_role r on r.user_guid = u.guid and  EXISTS (select id FROM hss_role rr WHERE  rr.is_enable = 1 and rr.guid = r.role_guid)
        inner join hss_role_source rs on (rs.role_guid = r.role_guid and rs.terminal_code = #{terminalCode})
        inner join hss_store_source ss on (
            ss.source_code = rs.source_code
            and ss.terminal_code = #{terminalCode}
            and (ss.store_guid = #{storeGuid} or ss.store_guid is null)
        )
        where u.guid = #{userGuid} and u.is_deleted = 0
    </select>

    <select id="getAllMatchedUrl" resultType="java.lang.String"
            parameterType="com.holderzone.saas.store.staff.entity.query.UserSourceQuery">
        select
          distinct rs.source_url
        from hss_user u
        inner join hss_r_user_role r on r.user_guid = u.guid and exists (select id from hss_role rr where rr.is_enable = 1 and rr.guid = r.role_guid)
        inner join hss_role_source rs on (
          rs.role_guid = r.role_guid
          and rs.terminal_code = #{terminalCode}
          and rs.menu_guid = #{menuGuid}
        )
        where u.guid = #{userGuid} and u.is_deleted = 0
    </select>

    <select id="countMatchedUrlOfAdmin" resultType="java.lang.Integer"
            parameterType="com.holderzone.saas.store.staff.entity.query.UserSourceQuery">
        select
        count(*)
        from
        hss_store_source ss
        <if test="menuGuid != null and menuGuid != ''">
            inner join hss_menu m on (
            ss.module_guid = m.module_guid
            and m.menu_guid = #{menuGuid}
            )
        </if>
        where
        ss.terminal_code = #{terminalCode}
        and
        #{requestUri} regexp ss.source_url
    </select>

    <select id="countMatchedUrl" resultType="java.lang.Integer"
            parameterType="com.holderzone.saas.store.staff.entity.query.UserSourceQuery">
        select
        count(*)
        from
        hss_user u
        inner join hss_r_user_role ur on (
        u.guid = ur.user_guid
        )
        inner join hss_role r on (
        ur.role_guid = r.guid and r.is_enable = 1
        )
        inner join hss_role_source rs on (
        r.guid = rs.role_guid
        and rs.terminal_code = #{terminalCode}
        <if test="menuGuid != null and menuGuid != ''">
            and rs.menu_guid = #{menuGuid}
        </if>
        )
        inner join hss_store_source ss on (
        rs.source_guid = ss.source_guid
        and #{requestUri} regexp ss.source_url
        )
        where u.guid = #{userGuid} and u.is_deleted = 0
    </select>

    <!-- 根据用户guid查询该用户关联的角色中能登陆的终端code -->
    <select id="queryUserManagedTerminal" resultType="java.lang.String">
        SELECT
            DISTINCT t1.terminal_code
        FROM hss_role_source AS t1
        INNER JOIN hss_r_user_role AS t2 ON t2.role_guid = t1.role_guid and  EXISTS (select id FROM hss_role rr WHERE  rr.is_enable = 1 and rr.guid = t2.role_guid)
        WHERE t2.user_guid = #{userGuid}
    </select>

    <!-- 根据用户guid查询该用户关联的角色中能登陆的终端code -->
    <select id="queryUsersByTerminal" resultType="java.lang.String">
        SELECT
        distinct t2.user_guid
        FROM hss_role_source AS t1
        INNER JOIN hss_r_user_role AS t2 ON t2.role_guid = t1.role_guid and  EXISTS (select id FROM hss_role rr WHERE  rr.is_enable = 1 and rr.guid = t2.role_guid)
        WHERE t2.user_guid in
        <foreach collection="userGuidList" item="userGuid" open="(" separator="," close=")">
            #{userGuid}
        </foreach>
        and t1.terminal_code = #{terminalCode}
    </select>

    <select id="queryUserModuleSource" resultType="com.holderzone.saas.store.dto.user.MenuSourceDTO">
        select
          ss.source_guid as sourceGuid, ss.source_name as sourceName, ss.source_code as sourceCode
        from hss_user u
        inner join hss_store_source ss on (
            ss.terminal_code = #{terminalCode}
            and ss.source_url = '/home_module_display'
            and (ss.store_guid = #{storeGuid} or ss.store_guid is null)
        )
        inner join hss_product p on (
            ss.product_guid = p.product_guid
            and p.gmt_product_start <![CDATA[<=]]> #{now}
            and (p.gmt_product_end <![CDATA[>=]]> #{now} or p.gmt_product_end is null)
            and p.is_deleted = 0
        )
        where u.guid = #{userGuid} and u.is_deleted = 0
    </select>
    <select id="queryAIOUsers" resultMap="UserMap">
        SELECT u.guid, u.name
        FROM hss_user u
        LEFT JOIN hss_r_user_role ur on u.guid = ur.user_guid
        LEFT JOIN hss_role_source rs on rs.role_guid = ur.role_guid
        LEFT JOIN hss_user_data ud on u.guid = ud.user_guid
        WHERE rs.terminal_code = '3'
        AND ud.store_guid = #{storeGuid}
        GROUP BY u.guid;
    </select>

    <select id="findByStoreGuid" resultMap="UserMap"
            parameterType="com.holderzone.saas.store.staff.entity.query.UserCondQuery">
        select
        u.guid, u.account, u.name, u.phone, u.org_guid, u.is_enable
        <if test="userCondQuery.showPassword != null and userCondQuery.showPassword == 1">
            ,u.password
        </if>
        from hss_user u INNER JOIN hss_user_data ud on ud.user_guid = u.guid
        where u.is_deleted = 0 and u.account != '100000' and u.is_enable = 1
        <if test="userCondQuery.isWaiter == null or userCondQuery.isWaiter != 1">
            and u.is_waiter = 1
        </if>
        <if test="userCondQuery.createStaffGuid != null and userCondQuery.createStaffGuid != ''">
            and u.create_staff_guid = #{userCondQuery.createStaffGuid}
        </if>
        <if test="userCondQuery.searchKey != null and userCondQuery.searchKey != ''">
            and (
            u.name like concat("%", #{userCondQuery.searchKey}, "%")
            or u.phone like concat("%", #{userCondQuery.searchKey}, "%")
            or u.account like concat("%", #{userCondQuery.searchKey}, "%")
            )
        </if>

        <if test="userCondQuery.orgGuidsWithChildren != null and userCondQuery.orgGuidsWithChildren.size > 0">
            and
            <foreach collection="userCondQuery.orgGuidsWithChildren" item="orgGuid" separator="or" open="(" close=")">
                ud.store_guid = #{orgGuid}
            </foreach>
        </if>
        order by u.gmt_create desc,u.id desc
    </select>

    <select id="queryUserSourceOnOut" resultType="com.holderzone.saas.store.dto.user.MenuSourceDTO">
        select
        ss.source_guid as sourceGuid, ss.source_name as sourceName, ss.source_code as sourceCode
        from hss_user u
        inner join hss_r_user_role r on r.user_guid = u.guid and  EXISTS (select id FROM hss_role rr WHERE  rr.is_enable = 1 and rr.guid = r.role_guid)
        inner join hss_role_source rs on (rs.role_guid = r.role_guid and rs.terminal_code = #{terminalCode})
        inner join hss_store_source ss on (
        ss.source_code = rs.source_code
        and ss.terminal_code = #{terminalCode}
        and ss.store_guid is null
        )
        where u.guid = #{userGuid} and u.is_deleted = 0
    </select>

    <select id="queryUserSourceOfAdminOnOut" resultType="com.holderzone.saas.store.dto.user.MenuSourceDTO">
        select
        ss.source_guid as sourceGuid, ss.source_name as sourceName, ss.source_code as sourceCode
        from hss_store_source ss
        inner join hss_product p on (
        ss.product_guid = p.product_guid
        and p.gmt_product_start <![CDATA[<=]]> NOW()
        and (p.gmt_product_end <![CDATA[>=]]> NOW() or p.gmt_product_end is null)
        and p.is_deleted = 0
        )
        where ss.terminal_code = #{terminalCode}
        and ss.store_guid is null
    </select>

</mapper>