<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.SurchargeMapper">

    <resultMap id="SurchargeMapResult" type="com.holderzone.saas.store.business.entity.domain.SurchargeDO">
        <result property="surchargeGuid" column="surcharge_guid"/>
        <result property="storeGuid" column="store_guid"/>
        <result property="name" column="name"/>
        <result property="amount" column="amount"/>
        <result property="type" column="type"/>
        <result property="isEnable" column="is_enable"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <select id="countByType" parameterType="com.holderzone.saas.store.business.entity.query.SurchargeCountQuery"
            resultType="java.lang.Long">
        SELECT
        count(*)
        FROM
        hsb_surcharge
        WHERE
        `is_deleted` = 0
        AND `store_guid` = #{storeGuid}
        <if test="type != null">
            AND `type` = #{type}
        </if>
    </select>

    <select id="pageByType" parameterType="com.holderzone.saas.store.dto.business.manage.SurchargeListDTO"
            resultMap="SurchargeMapResult">
        SELECT
        `surcharge_guid`,`store_guid`,`name`,`amount`,`type`,`is_enable`, `effective_time`, `trade_mode`
        FROM
        hsb_surcharge
        WHERE
        `is_deleted` = 0
        AND `store_guid` = #{storeGuid}
        <if test="type != null">
            AND `type` = #{type}
        </if>
        ORDER BY gmt_create DESC
    </select>

</mapper>