<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.OrderMultiMemberMapper">

    <update id="updateBatchPayAmount">
        <foreach collection="orderMultiMembers" item="item" separator=";">
            update
                hst_order_multi_member
            set
                amount = amount + ${item.amount}
            where
                order_guid = #{item.orderGuid} and member_card_guid = #{item.memberCardGuid}
        </foreach>
    </update>
</mapper>
