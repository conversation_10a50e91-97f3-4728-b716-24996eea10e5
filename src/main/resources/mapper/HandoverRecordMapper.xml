<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.HandoverRecordMapper">

    <insert id="insert" useGeneratedKeys="true"
            parameterType="com.holderzone.saas.store.business.entity.domain.HandoverRecordDO">
        INSERT INTO hsb_handover_record
        (
        <include refid="notNullColumn"/>
        )
        VALUES
        (
        <include refid="notNullColumnValue"/>
        )
    </insert>

    <update id="update" parameterType="com.holderzone.saas.store.business.entity.domain.HandoverRecordDO">
        UPDATE
        hsb_handover_record
        SET
        <include refid="columnValuePairs"/>
        WHERE
        `handover_record_guid` = #{handoverRecordGuid}
    </update>

    <select id="query" resultMap="HandoverRecordMapResult"
            parameterType="com.holderzone.saas.store.business.entity.domain.HandoverRecordDO">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_handover_record
        WHERE
        `handover_record_guid` = #{handoverRecordGuid}
    </select>

    <select id="queryAll" resultMap="HandoverRecordMapResult"
            parameterType="com.holderzone.saas.store.business.entity.domain.HandoverRecordDO">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_handover_record
        WHERE
        `store_guid` = #{storeGuid}
        <if test="createUserGuid!=null and createUserGuid!=''">
            AND `create_user_guid` = #{createUserGuid}
        </if>
        <if test="status!=null">
            AND `status` = #{status}
        </if>
    </select>

    <select id="queryByPage" resultMap="HandoverRecordMapResult"
            parameterType="com.holderzone.saas.store.dto.business.manage.HandoverRecordQueryAllDTO">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_handover_record
        WHERE
        `store_guid` = #{storeGuid}
        <if test="status!=null">
            AND `status` = #{status}
        </if>
        <if test="startDateTime != null">
            AND gmt_modified >= #{startDateTime}
        </if>
        ORDER BY gmt_modified DESC
        <include refid="optionalLimit"/>
    </select>

    <select id="queryByPageNew" resultType="com.holderzone.saas.store.dto.business.manage.HandoverRecordDTO"
            parameterType="com.holderzone.saas.store.dto.business.manage.HandoverRecordQueryAllDTO">
        SELECT
            relation_record_guid as handoverRecordGuid,
            GROUP_CONCAT( create_user_name SEPARATOR '、' ) as createName,
            MIN( gmt_create ) as gmtCreate,
            MAX( gmt_modified ) as gmtModified,
            SUM( handon_cash ) as handonCash
        FROM
            hsb_handover_record
        WHERE
            `store_guid` = #{storeGuid}
            <if test="status!=null">
                AND `status` = #{status}
            </if>
            <if test="startDateTime != null">
                AND gmt_modified >= #{startDateTime}
            </if>
            GROUP BY relation_record_guid
            ORDER BY gmt_modified DESC
            <include refid="optionalLimit"/>
    </select>

    <select id="count" resultType="java.lang.Long" parameterType="java.lang.String">
        SELECT COUNT(1)
        FROM hsb_handover_record
        WHERE `store_guid` = #{storeGuid}
    </select>

    <select id="countByTerminalIdAndStoreId" resultType="java.lang.Long" parameterType="java.lang.String">
        SELECT COUNT(1)
        FROM hsb_handover_record
        WHERE `terminal_id` = #{terminalId}
        AND `store_guid` = #{storeGuid}
    </select>

    <select id="queryAllByStoreList" resultMap="HandoverRecordMapResult" parameterType="java.util.List">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_handover_record
        WHERE
        `status` = '0' AND `store_guid` IN (
        <foreach collection="list" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

    <!-- 根据员工guid、设备编号、门店guid查询未交班的记录 -->
    <select id="queryByUserGuidAndTerminaId" resultMap="HandoverRecordMapResult" parameterType="java.lang.String">
        SELECT
        <include refid="columns"/>
        FROM
        `hsb_handover_record`
        WHERE `status` = '0' AND `create_user_guid` = #{userGuid} AND `terminal_id` = #{terminalId}
        AND `store_guid` = #{storeGuid}
    </select>

    <!-- 查询未交班记录 -->
    <select id="queryByUserGuid" resultMap="HandoverRecordMapResult"
            parameterType="com.holderzone.saas.store.dto.business.manage.HandoverRecordConfirmDTO">
        SELECT
        <include refid="columns"/>
        FROM
        `hsb_handover_record`
        WHERE `status` = '0'
        <if test="userGuid!=null and userGuid!=''">
            AND `create_user_guid` = #{userGuid}
        </if>
        <if test="storeGuid!=null and storeGuid!=''">
            AND `store_guid` = #{storeGuid}
        </if>
        <if test="userGuidList != null and userGuidList.size() > 0">
            AND `create_user_guid` IN
            <foreach collection="userGuidList" item="userGuid" separator="," open="(" close=")">
                #{userGuid}
            </foreach>
        </if>
    </select>

    <!-- 根据门店guid、设备固件编号查询未交班的记录 -->
    <select id="queryByUserStoreGuidAndTerminalId" resultMap="HandoverRecordMapResult">
        SELECT
        <include refid="columns"/>
        FROM
        `hsb_handover_record`
        WHERE `status` = '0' AND `store_guid` = #{storeGuid} AND `terminal_id` = #{terminalId}
    </select>

    <!-- 根据storeGuid、userGuid查询未交班记录 -->
    <select id="queryByStoreGuidAndUserGuid" resultMap="HandoverRecordMapResult">
        SELECT
        <include refid="columns"/>
        FROM
        `hsb_handover_record`
        WHERE `status` = '0' AND `store_guid` = #{storeGuid} AND `create_user_guid` = #{userGuid}
    </select>

    <resultMap id="HandoverRecordMapResult" type="com.holderzone.saas.store.business.entity.domain.HandoverRecordDO">
        <result property="storeGuid" column="store_guid"/>
        <result property="storeName" column="store_name"/>
        <result property="terminalId" column="terminal_id"/>
        <result property="handoverRecordGuid" column="handover_record_guid"/>
        <result property="createUserGuid" column="create_user_guid"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="paymentMoney" column="payment_money"/>
        <result property="memberChargeMoney" column="member_charge_money"/>
        <result property="businessInComing" column="business_in_coming"/>
        <result property="paymentCount" column="payment_count"/>
        <result property="checkedCount" column="checked_count"/>
        <result property="chargedCount" column="charged_count"/>
        <result property="confirmUserGuid" column="confirm_user_guid"/>
        <result property="confirmUserName" column="confirm_user_name"/>
        <result property="status" column="status"/>
        <result property="collectMoney" column="collect_money"/>
        <result property="flowMoney" column="flow_money"/>
        <result property="sysBalance" column="sys_balance"/>
        <result property="refundCount" column="refund_count"/>
        <result property="refundMoney" column="refund_money"/>
        <result property="differenceMoney" column="difference_money"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="reserveCount" column="reserve_count"/>
        <result property="reserveMoney" column="reserve_money"/>
        <result property="handonCash" column="handon_cash"/>
        <result property="realHandonCash" column="real_handon_cash"/>
    </resultMap>

    <sql id="notNullColumn">
        <trim suffixOverrides=",">
            <if test="storeGuid!=null and storeGuid!=''">
                `store_guid`,
            </if>
            <if test="storeName!=null and storeName!=''">
                `store_name`,
            </if>
            <if test="handoverRecordGuid!=null and handoverRecordGuid!=''">
                `handover_record_guid`,
            </if>
            <if test="createUserGuid!=null and createUserGuid!=''">
                `create_user_guid`,
            </if>
            <if test="createUserName!=null and createUserName!=''">
                `create_user_name`,
            </if>
            <if test="terminalId!=null and terminalId!=''">
                `terminal_id`,
            </if>
            <if test="gmtCreate!=null">
                `gmt_create`,
            </if>
            <if test="gmtModified!=null">
                `gmt_modified`,
            </if>
            <if test="relationRecordGuid !=null and relationRecordGuid !=''">
                `relation_record_guid`,
            </if>
        </trim>
    </sql>

    <sql id="notNullColumnValue">
        <trim suffixOverrides=",">
            <if test="storeGuid!=null and storeGuid!=''">
                #{storeGuid},
            </if>
            <if test="storeName!=null and storeName!=''">
                #{storeName},
            </if>
            <if test="handoverRecordGuid!=null and handoverRecordGuid!=''">
                #{handoverRecordGuid},
            </if>
            <if test="createUserGuid!=null and createUserGuid!=''">
                #{createUserGuid},
            </if>
            <if test="createUserName!=null and createUserName!=''">
                #{createUserName},
            </if>
            <if test="terminalId!=null and terminalId!=''">
                #{terminalId},
            </if>
            <if test="gmtCreate!=null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified!=null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="relationRecordGuid !=null and relationRecordGuid !=''">
                #{relationRecordGuid},
            </if>
        </trim>
    </sql>

    <sql id="columns">
        <trim suffixOverrides=",">
            `store_guid`,
            `store_name`,
            `terminal_id`,
            `handover_record_guid`,
            `create_user_guid`,
            `create_user_name`,
            `payment_money`,
            `member_charge_money`,
            `business_in_coming`,
            `charged_count`,
            `checked_count`,
            `payment_count`,
            `refund_count`,
            `refund_money`,
            `confirm_user_guid`,
            `confirm_user_name`,
            `status`,
            `collect_money`,
            `flow_money`,
            `sys_balance`,
            `difference_money`,
            `reserve_count`,
            `reserve_money`,
            `handon_cash`,
            `real_handon_cash`,
            `excess_amount`,
            DATE_FORMAT(`gmt_create`,"%Y-%m-%d %T") AS gmt_create,
            DATE_FORMAT(`gmt_modified`,"%Y-%m-%d %T") AS gmt_modified,
            `repayment_fee_total`,
            `repayment_fee_count`,
            `relation_record_guid`,
            `is_multi_handover`,
            `traffic`,
            `total_seats`,
            `occupancy_rate_percent`,
            `table_use_count`,
            `table_count`,
            `open_table_rate_percent`,
            `flip_table_rate_percent`,
            `total_dine_in_time`,
            `avg_dine_in_time`,
            `sale_amount`,
            `discount_amount`,
            `refund_amount`,
        </trim>
    </sql>

    <sql id="columnValuePairs">
        <trim suffixOverrides=",">
            <if test="confirmUserGuid!=null and confirmUserGuid!=''">
                `confirm_user_guid` = #{confirmUserGuid},
            </if>
            <if test="confirmUserName!=null and confirmUserName!=''">
                `confirm_user_name` = #{confirmUserName},
            </if>
            <if test="paymentMoney!=null">
                `payment_money` = #{paymentMoney},
            </if>
            <if test="memberChargeMoney!=null">
                `member_charge_money` = #{memberChargeMoney},
            </if>
            <if test="businessInComing!=null">
                `business_in_coming` = #{businessInComing},
            </if>
            <if test="chargedCount!=null">
                `charged_count` = #{chargedCount},
            </if>
            <if test="checkedCount!=null">
                `checked_count` = #{checkedCount},
            </if>
            <if test="paymentCount!=null">
                `payment_count` = #{paymentCount},
            </if>
            <if test="status!=null">
                `status` = #{status},
            </if>
            <if test="refundMoney!=null">
                `refund_money` = #{refundMoney},
            </if>
            <if test="refundCount!=null">
                `refund_count` = #{refundCount},
            </if>
            <if test="gmtModified!=null">
                `gmt_modified` = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="reserveCount!=null">
                `reserve_count` = #{reserveCount},
            </if>
            <if test="reserveMoney!=null">
                `reserve_money` = #{reserveMoney},
            </if>
            <if test="handonCash!=null">
                `handon_cash` = #{handonCash},
            </if>
            <if test="realHandonCash!=null">
                `real_handon_cash` = #{realHandonCash},
            </if>
            <if test="repaymentFeeTotal!=null">
                `repayment_fee_total` = #{repaymentFeeTotal},
            </if>
            <if test="repaymentFeeCount!=null">
                `repayment_fee_count` = #{repaymentFeeCount},
            </if>
            <if test="excessAmount!=null">
                `excess_amount` = #{excessAmount},
            </if>
            <if test="relationRecordGuid!=null">
                `relation_record_guid` = #{relationRecordGuid},
            </if>
            <if test="isMultiHandover!=null">
                `is_multi_handover` = #{isMultiHandover},
            </if>
            <if test="traffic!=null">
                `traffic` = #{traffic},
            </if>
            <if test="totalSeats!=null">
                `total_seats` = #{totalSeats},
            </if>
            <if test="occupancyRatePercent!=null and occupancyRatePercent!=''">
                `occupancy_rate_percent` = #{occupancyRatePercent},
            </if>
            <if test="tableUseCount!=null">
                `table_use_count` = #{tableUseCount},
            </if>
            <if test="tableCount!=null">
                `table_count` = #{tableCount},
            </if>
            <if test="openTableRatePercent!=null and openTableRatePercent!=''">
                `open_table_rate_percent` = #{openTableRatePercent},
            </if>
            <if test="flipTableRatePercent!=null and flipTableRatePercent!=''">
                `flip_table_rate_percent` = #{flipTableRatePercent},
            </if>
            <if test="totalDineInTime!=null">
                `total_dine_in_time` = #{totalDineInTime},
            </if>
            <if test="avgDineInTime!=null">
                `avg_dine_in_time` = #{avgDineInTime},
            </if>
            <if test="saleAmount!=null">
                `sale_amount` = #{saleAmount},
            </if>
            <if test="discountAmount!=null">
                `discount_amount` = #{discountAmount},
            </if>
            <if test="refundAmount!=null">
                `refund_amount` = #{refundAmount},
            </if>
        </trim>
    </sql>

    <sql id="optionalLimit">
        <if test="currentPage!=null and pageSize!=null">
            LIMIT #{offsetIndex}, #{pageSize}
        </if>
    </sql>

    <select id="queryByCondition" resultType="com.holderzone.saas.store.business.entity.domain.HandoverRecordDO">
        SELECT
        <include refid="columns"/>
        FROM `hsb_handover_record`
        WHERE 1=1
        <if test="dto.storeGuid != null and dto.storeGuid != ''">
            AND `store_guid` = #{dto.storeGuid}
        </if>
        <if test="dto.state != -1">
            AND `status` = #{dto.state}
        </if>
        <if test="dto.businessStartDateTime != null">
            AND `gmt_create` &gt;= #{dto.businessStartDateTime}
        </if>
        <if test="dto.businessEndDateTime != null">
            AND `gmt_create` &lt;= #{dto.businessEndDateTime}
        </if>
        <if test="dto.userGuids != null and dto.userGuids.size() > 0">
            AND create_user_guid IN
            <foreach collection="dto.userGuids" item="userGuid" open="(" separator="," close=")">
                #{userGuid}
            </foreach>
        </if>
        <if test="dto.count != null and dto.count > 0">
            LIMIT #{dto.count}
        </if>
    </select>

    <select id="queryNew" resultType="com.holderzone.saas.store.business.entity.domain.HandoverRecordDO">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_handover_record
        WHERE
        `relation_record_guid` = #{handoverRecordGuid}
    </select>
</mapper>