<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.StoreConfigMapper">

    <insert id="insert" useGeneratedKeys="true"
            parameterType="com.holderzone.saas.store.business.entity.domain.StoreConfigDO">
        INSERT INTO hsb_store_config
        (
        <include refid="notNullColumn"/>
        )
        VALUES
        (
        <include refid="notNullColumnValue"/>
        )
    </insert>

    <select id="query" resultMap="StoreConfigMapResult"
            parameterType="com.holderzone.saas.store.business.entity.domain.StoreConfigDO">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_store_config
        WHERE
        `store_guid` = #{storeGuid}
    </select>

    <select id="queryAll" resultMap="StoreConfigMapResult">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_store_config
    </select>

    <select id="queryList" resultMap="StoreConfigMapResult" parameterType="java.util.List">
        SELECT
        <include refid="columns"/>
        FROM
        hsb_store_config
        where `store_guid` IN (
        <foreach collection="list" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

    <update id="update" parameterType="com.holderzone.saas.store.business.entity.domain.StoreConfigDO">
        UPDATE
        hsb_store_config
        SET
        <include refid="columnValuePairs"/>
        WHERE
        `store_guid` = #{storeGuid}
    </update>

    <resultMap id="StoreConfigMapResult" type="com.holderzone.saas.store.business.entity.domain.StoreConfigDO">
        <result property="storeGuid" column="store_guid"/>
        <result property="storeName" column="store_name"/>
        <result property="enablePadPwd" column="is_enable_pad_pwd"/>
        <result property="enableMarkDish" column="is_enable_mark_dish"/>
        <result property="enableMemPrice" column="is_enable_mem_price"/>
        <result property="enableHandover" column="is_enable_handover"/>
        <result property="wechatOrderMode" column="wechat_order_mode"/>
        <result property="serialNumberMode" column="serial_number_mode"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="businessStartTime" column="gmt_business_start"/>
        <result property="businessEndTime" column="gmt_business_end"/>
        <result property="enableAutoMark" column="is_enable_auto_mark"/>
        <result property="initMark" column="init_mark"/>
        <result property="prepTime" column="prep_time"/>
        <result property="finishFoodVoiceSwitch" column="finish_food_voice_switch"/>
        <result property="printItemOrder" column="print_item_order"/>
        <result property="endMark" column="end_mark"/>
    </resultMap>

    <sql id="notNullColumn">
        <trim suffixOverrides=",">
            <if test="storeGuid!=null and storeGuid!=''">
                `store_guid`,
            </if>
            <if test="storeName!=null and storeName!=''">
                `store_name`,
            </if>
            <if test="enablePadPwd!=null">
                `is_enable_pad_pwd`,
            </if>
            <if test="enableMarkDish!=null">
                `is_enable_mark_dish`,
            </if>
            <if test="enableMemPrice!=null">
                `is_enable_mem_price`,
            </if>
            <if test="enableHandover!=null">
                `is_enable_handover`,
            </if>
            <if test="wechatOrderMode!=null">
                `wechat_order_mode`,
            </if>
            <if test="serialNumberMode!=null">
                `serial_number_mode`,
            </if>
            <if test="businessStartTime!=null">
                `gmt_business_start`,
            </if>
            <if test="businessEndTime!=null">
                `gmt_business_end`,
            </if>
            <if test="enableAutoMark!=null">
                `is_enable_auto_mark`
            </if>
            <if test="initMark!=null">
                `init_mark`
            </if>
            <if test="prepTime!=null">
                `prep_time`
            </if>
            <if test="finishFoodVoiceSwitch!=null">
                `finish_food_voice_switch`
            </if>
            <if test="printItemOrder!=null">
                `print_item_order`
            </if>
        </trim>
    </sql>

    <sql id="notNullColumnValue">
        <trim suffixOverrides=",">
            <if test="storeGuid!=null and storeGuid!=''">
                #{storeGuid},
            </if>
            <if test="storeName!=null and storeName!=''">
                #{storeName},
            </if>
            <if test="enablePadPwd!=null">
                #{enablePadPwd},
            </if>
            <if test="enableMarkDish!=null">
                #{enableMarkDish},
            </if>
            <if test="enableMemPrice!=null">
                #{enableMemPrice},
            </if>
            <if test="enableHandover!=null">
                #{enableHandover},
            </if>
            <if test="wechatOrderMode!=null">
                #{wechatOrderMode},
            </if>
            <if test="serialNumberMode!=null">
                #{serialNumberMode},
            </if>
            <if test="businessStartTime!=null">
                #{businessStartTime},
            </if>
            <if test="businessEndTime!=null">
                #{businessEndTime},
            </if>
            <if test="enableAutoMark">
                #{enableAutoMark}
            </if>
            <if test="initMark">
                #{initMark}
            </if>
            <if test="prepTime">
                #{prepTime}
            </if>
            <if test="finishFoodVoiceSwitch">
                #{finishFoodVoiceSwitch}
            </if>
            <if test="printItemOrder!=null">
                #{printItemOrder}
            </if>
        </trim>
    </sql>

    <sql id="columns">
        <trim suffixOverrides=",">
            `store_guid`,
            `store_name`,
            `is_enable_pad_pwd`,
            `is_enable_mark_dish`,
            `is_enable_mem_price`,
            `is_enable_handover`,
            `wechat_order_mode`,
            `serial_number_mode`,
            DATE_FORMAT(`gmt_create`,"%Y-%m-%d %T") AS gmt_create,
            DATE_FORMAT(`gmt_modified`,"%Y-%m-%d %T") AS gmt_modified,
            `gmt_business_start`,
            `gmt_business_end`,
            `is_enable_auto_mark`,
            `init_mark`,
            `prep_time`,
            `finish_food_voice_switch`,
            `print_item_order`,
            `enable_handle_close`,
            `enable_multiple_pay`,
            `enable_dine_in_change_diff_value`,
            `enable_fast_change_diff_value`,
            `end_mark`,
        </trim>
    </sql>

    <sql id="columnValuePairs">
        <trim suffixOverrides=",">
            <if test="enablePadPwd!=null">
                `is_enable_pad_pwd` = #{enablePadPwd},
            </if>
            <if test="enableMarkDish!=null">
                `is_enable_mark_dish` = #{enableMarkDish},
            </if>
            <if test="enableMemPrice!=null">
                `is_enable_mem_price` = #{enableMemPrice},
            </if>
            <if test="enableHandover!=null">
                `is_enable_handover` = #{enableHandover},
            </if>
            <if test="wechatOrderMode!=null">
                `wechat_order_mode` = #{wechatOrderMode},
            </if>
            <if test="serialNumberMode!=null">
                `serial_number_mode` = #{serialNumberMode},
            </if>
            <if test="businessStartTime!=null">
                `gmt_business_start` = #{businessStartTime},
            </if>
            <if test="businessEndTime!=null">
                `gmt_business_end` = #{businessEndTime},
            </if>
            <if test="enableAutoMark!=null">
                `is_enable_auto_mark` = #{enableAutoMark},
            </if>
            <if test="initMark!=null">
                `init_mark` = #{initMark},
            </if>
            <if test="prepTime!=null">
                `prep_time` = #{prepTime},
            </if>
            <if test="finishFoodVoiceSwitch!=null">
                `finish_food_voice_switch` = #{finishFoodVoiceSwitch},
            </if>
            <if test="printItemOrder!=null">
                `print_item_order` = #{printItemOrder},
            </if>
            <if test="enableHandleClose!=null">
                `enable_handle_close` = #{enableHandleClose},
            </if>
            <if test="enableMultiplePay!=null">
                `enable_multiple_pay` = #{enableMultiplePay},
            </if>
            <if test="enableDineInChangeDiffValue!=null">
                `enable_dine_in_change_diff_value` = #{enableDineInChangeDiffValue},
            </if>
            <if test="enableFastChangeDiffValue!=null">
                `enable_fast_change_diff_value` = #{enableFastChangeDiffValue},
            </if>
            <choose>
                <when test="endMark!=null and endMark!=''">
                    `end_mark` = #{endMark},
                </when>
                <when test="endMark==null or endMark==''">
                    `end_mark` = NULL,
                </when>
            </choose>
        </trim>
    </sql>
</mapper>