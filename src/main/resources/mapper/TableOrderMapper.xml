<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.table.mapper.TableOrderMapper">


    <resultMap id="tableAllMap" type="com.holderzone.holder.saas.store.table.domain.TableDO">
        <result column="hb_guid" property="guid"/>
        <result column="hb_area_guid" property="areaGuid"/>
        <result column="hb_area_name" property="areaName"/>
        <result column="hb_table_code" property="tableCode"/>
        <result column="hb_seats" property="seats"/>
        <result column="hb_sort" property="sort"/>
        <result column="hb_store_guid" property="storeGuid"/>
        <result column="hb_store_name" property="storeName"/>
        <result column="ho_main_order_guid" property="mainOrderGuid"/>
        <result column="ho_order_guid" property="orderGuid"/>
        <result column="ho_status" property="status"/>
        <result column="ho_sub_status" property="subStatus"/>
        <result column="ho_open_table_time" property="openTableTime"/>
        <result column="ho_combine_times" property="combineTimes"/>
        <result column="ho_associated_times" property="associatedTimes"/>
        <result column="ho_open_staff_guid" property="openStaffGuid"/>
        <result column="ho_open_staff_name" property="openStaffName"/>
    </resultMap>

    <select id="selectAllTable" parameterType="com.holderzone.saas.store.dto.table.TableBasicQueryDTO"
            resultMap="tableAllMap">
        select
        hb.guid as hb_guid,hb.area_guid as hb_area_guid,hb.area_name as hb_area_name,hb.table_code as
        hb_table_code,hb.seats as hb_seats,
        hb.sort as hb_sort,hb.store_guid as hb_store_guid,hb.store_name as hb_store_name,
        ho.main_order_guid as ho_main_order_guid,ho.order_guid as ho_order_guid,
        ho.status as ho_status,ho.sub_status as ho_sub_status,ho.open_table_time as
        ho_open_table_time,ho.open_staff_guid as ho_open_staff_guid,
        ho.open_staff_name as ho_open_staff_name,ho.combine_times as ho_combine_times, ho.associated_times as ho_associated_times
        from hst_table_basic hb
        left join hst_table_order ho on ho.table_guid = hb.guid
        <where>
            hb.deleted != 1
            <if test="storeGuid !=null and storeGuid!=''">
                and hb.store_guid = #{storeGuid}
            </if>
            <if test="areaGuid!=null and areaGuid!=''">
                and hb.area_guid = #{areaGuid}
            </if>
            <if test="tableGuidList !=null and tableGuidList.size()>0">
                and ho.table_guid in
                <foreach collection="tableGuidList" item="guid" close=")" open="(" separator=",">
                    #{guid}
                </foreach>
            </if>
        </where>
        order by hb.sort asc
    </select>

    <resultMap id="tableOrderMap" type="com.holderzone.holder.saas.store.table.domain.TableOrderDO">
        <result column="guid" property="guid"/>
        <result column="table_guid" property="tableGuid"/>
        <result column="main_order_guid" property="mainOrderGuid"/>
        <result column="status" property="status"/>
        <result column="sub_status" property="subStatus"/>
    </resultMap>

    <select id="querySyn" parameterType="string" resultMap="tableOrderMap">
        select
        guid,table_guid,main_order_guid,status,sub_status
        from hst_table_order where table_guid = #{tableGuid} for update;
    </select>

    <update id="removeCombine" parameterType="list">
        <foreach collection="list" item="item" separator=";">
            update hst_table_order
            <set>
                <if test="null!= item.status">
                    status = #{item.status},
                </if>
                sub_status = #{item.subStatus},
                combine_times = #{item.combineTimes},
                associated_times = #{item.associatedTimes},
                main_order_guid = #{item.mainOrderGuid}
            </set>
            where table_guid = #{item.tableGuid}
        </foreach>
    </update>

    <update id="compensationStatus" parameterType="list">
        <foreach collection="list" item="item" separator=";">
            update hst_table_order
            set main_order_guid =
            null,order_guid=null,status=0,open_staff_guid=null,open_staff_name=null,open_table_time=null,
            combine_times=0,sub_status=null
            where table_guid = #{item.tableGuid} and order_guid = #{item.orderGuid}
        </foreach>
    </update>

    <update id="closeTable">
        update hst_table_order
            set main_order_guid =
            null,order_guid=null,status=0,open_staff_guid=null,open_staff_name=null,open_table_time=null,
            combine_times=null,associated_times=null,
            <if test="subStatus !=null and subStatus != &quot;&quot;">
                sub_status=#{subStatus}
            </if>
            <if test="subStatus ==null or subStatus == &quot;&quot;">
                sub_status=null
            </if>
            where table_guid = #{tableGuid} and order_guid = #{orderGuid}
    </update>

    <select id="selectFullInfo" parameterType="string" resultMap="tableAllMap">
        select
        hb.guid as hb_guid,hb.area_guid as hb_area_guid,hb.area_name as hb_area_name,hb.table_code as
        hb_table_code,hb.seats as hb_seats,
        hb.sort as hb_sort,hb.store_guid as hb_store_guid,hb.store_name as hb_store_name,
        ho.main_order_guid as ho_main_order_guid,ho.order_guid as ho_order_guid,
        ho.status as ho_status,ho.sub_status as ho_sub_status,ho.open_table_time as
        ho_open_table_time,ho.open_staff_guid as ho_open_staff_guid,
        ho.open_staff_name as ho_open_staff_name
        from hst_table_basic hb left join hst_table_order ho on ho.table_guid = hb.guid
        where hb.guid = #{tableGuid} and hb.deleted = 0
    </select>

    <update id="updateAll" parameterType="list">
        <foreach collection="list" item="item" separator=";">
            update hst_table_order
            set main_order_guid = #{item.mainOrderGuid},order_guid=#{item.orderGuid},status=#{item.status},
            open_staff_guid=#{item.openStaffGuid},open_staff_name=#{item.openStaffName},open_table_time=#{item.openTableTime},
            combine_times=#{item.combineTimes},sub_status=#{item.subStatus}
            where table_guid = #{item.tableGuid}
        </foreach>
    </update>


    <select id="tableCombineList" parameterType="string" resultType="com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO">


        SELECT tor.main_order_guid AS mainOrderGuid,tor.order_guid AS orderGuid
        ,tb.guid AS tableGuid ,tb.area_guid AS areaGuid,tb.area_name AS areaName
        ,tb.`table_code`
        FROM hst_table_order tor INNER  JOIN  hst_table_basic tb ON tb.guid = tor.table_guid
        WHERE
            (
            main_order_guid IN (
            SELECT main_order_guid FROM  hst_table_order WHERE table_guid = #{tableGuid}
              AND main_order_guid is not null
            <![CDATA[ and LENGTH(main_order_guid)>5 ]]>
            ))
            OR
            (
            order_guid IN (
            SELECT order_guid FROM  hst_table_order WHERE table_guid = #{tableGuid}
             AND order_guid is not null
            ))


    </select>
</mapper>
