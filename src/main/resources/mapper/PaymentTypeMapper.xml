<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.PaymentTypeMapper">

    <sql id="values">
        #{paymentTypeGuid}
        ,
        #{paymentTypeName},
        #{paymentType},
        #{state},
        #{storeGuid},
        #{storeName},
        #{sorting},
        #{source}
    </sql>

    <sql id="clonms">
        payment_type_guid
        ,parent_payment_type_guid,payment_type_name,payment_type,state,store_guid,store_name,sorting,
        source,payment_mode,gmt_create,gmt_modified,payment_shunt,payment_multi_card
    </sql>

    <insert id="add" parameterType="com.holderzone.saas.store.business.entity.domain.PaymentTypeDO">
        insert into hsb_payment_type
        (
        <include refid="clonms"/>
        )
        values
        (
        <include refid="values"/>
        )
    </insert>

    <select id="getMaxSortingAndMaxPaymentType" resultType="com.holderzone.saas.store.business.entity.domain.PaymentTypeDO">
        SELECT
            IFNULL(MAX(sorting), 0) AS sorting,
            IFNULL(MAX(payment_type), 0) AS payment_type
        FROM hsb_payment_type
        WHERE store_guid = #{storeGuid}
    </select>

    <insert id="updateAllSort" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update hsb_payment_type
            set sorting = #{item.sorting}
            where payment_type_guid = #{item.paymentTypeGuid}
        </foreach>
    </insert>

    <select id="getOne" parameterType="com.holderzone.saas.store.dto.trade.PaymentTypeDTO" resultMap="paymentTypeMap">
        select
        <include refid="clonms"/>
        from hsb_payment_type
        where payment_type_guid = #{paymentTypeGuid} and store_guid = #{storeGuid}
    </select>

    <select id="getAllStorePayNames" parameterType="list" resultType="java.lang.String">
        select payment_type_name from hsb_payment_type where
        store_guid
        in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="countByStoreGuidInAndName" resultType="java.lang.Integer">
        select count(*) from hsb_payment_type where
        store_guid
        in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and payment_type_name = #{name}
        <if test="guid != null and guid != &quot;&quot;">
            and payment_type_guid != #{guid}
        </if>
    </select>
    <insert id="addAll" parameterType="list">
        insert into hsb_payment_type
        (
        <include refid="clonms"/>
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.paymentTypeGuid},#{item.parentPaymentTypeGuid},#{item.paymentTypeName},#{item.paymentType},#{item.state},#{item.storeGuid},#{item.storeName}
            ,#{item.sorting},#{item.source},#{item.paymentMode},#{item.createTime},#{item.updateTime},#{item.paymentShunt},#{item.paymentMultiCard})
        </foreach>
    </insert>
    <update id="update" parameterType="com.holderzone.saas.store.business.entity.domain.PaymentTypeDO">
        update hsb_payment_type
        <set>
            <trim suffixOverrides=",">
                <if test="paymentTypeName!=null and paymentTypeName!=''">
                    payment_type_name= #{paymentTypeName},
                </if>
                <if test="paymentType!=null">
                    payment_type= #{paymentType},
                </if>
                <if test="state!=null">
                    state= #{state},
                </if>
                <if test="sorting!=null">
                    sorting= #{sorting},
                </if>
            </trim>
        </set>
        where payment_type_guid = #{paymentTypeGuid} and store_guid = #{storeGuid}
    </update>

    <delete id="delete" parameterType="string">
        delete
        from hsb_payment_type
        where payment_type_guid = #{paymentTypeGuid}
          and store_guid = #{storeGuid}
    </delete>

    <resultMap id="paymentTypeMap" type="com.holderzone.saas.store.dto.trade.PaymentTypeDTO">
        <result column="payment_type_guid" property="paymentTypeGuid"/>
        <result column="parent_payment_type_guid" property="parentPaymentTypeGuid"/>
        <result column="payment_type_name" property="paymentTypeName"/>
        <result column="payment_type" property="paymentType"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="store_name" property="storeName"/>
        <result column="state" property="state"/>
        <result column="payment_mode" property="paymentMode"/>
        <result column="payment_multi_card" property="paymentMultiCard"/>
        <result column="gmt_create" property="createTime"/>
        <result column="gmt_modified" property="updateTime"/>
    </resultMap>

    <select id="getAll" resultMap="paymentTypeMap">
        select
        <include refid="clonms"/>
        from hsb_payment_type
        <where>
            <if test="storeGuid != null and storeGuid!=''">
                and store_guid = #{storeGuid}
            </if>
            <if test="source != null and source!=0">
                and source = #{source}
            </if>
        </where>
        ORDER BY sorting ASC
    </select>

    <select id="queryDefauktPayTypeExist" parameterType="string" resultType="java.lang.Integer">
        select count(*)
        from hsb_payment_type
        where store_guid = #{storeGuid}
          and source = 1;
    </select>

    <select id="getMultiStorePayWay" resultMap="paymentTypeMap">
        select
        <include refid="clonms"/>,gmt_create,gmt_modified
        from hsb_payment_type
        where store_guid = #{dto.storeGuid}
        ORDER BY sorting ASC
    </select>

    <select id="getPaymentTypeInfo" resultType="com.holderzone.saas.store.dto.trade.PaymentTypeDTO">
        select
        <include refid="clonms"/>
        from hsb_payment_type
        where store_guid = #{storeGuid}
        AND payment_type = #{paymentType}
    </select>

    <update id="updatePaymentTypeMode">
        update hsb_payment_type
        <set>
            payment_mode = #{paymentMode}, payment_multi_card = #{paymentMultiCard}
        </set>
        where payment_type_guid = #{paymentTypeGuid} and store_guid = #{storeGuid}
    </update>

    <update id="updateShunt">
        update hsb_payment_type
        <set>
            payment_shunt = #{paymentShunt}
        </set>
        where payment_type_guid = #{paymentTypeGuid} and store_guid = #{storeGuid}
    </update>
</mapper>