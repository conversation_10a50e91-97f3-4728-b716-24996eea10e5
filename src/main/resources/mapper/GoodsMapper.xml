<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.GoodsMapper">

    <select id="queryGoodsSumInfo" resultType="com.holderzone.erp.entity.domain.GoodsDO">
        SELECT * FROM hse_goods
        <where>
            store_guid = #{dto.storeGuid}
            <if test="dto.goodsClassifyGuid != null and dto.goodsClassifyGuid != ''">
                and goods_classify_guid = #{dto.goodsClassifyGuid}
            </if>
            <if test="dto.nameOrCode != null and dto.nameOrCode != ''">
                and (goods_code like CONCAT('%',#{dto.nameOrCode},'%' ) or goods_name like
                CONCAT('%',#{dto.nameOrCode},'%' ))
            </if>
        </where>
        ORDER BY (remain_repertory_num - IFNULL(safe_num,-999999.99)) ASC
    </select>

</mapper>
