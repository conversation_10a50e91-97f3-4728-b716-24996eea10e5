<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.queue.mapper.QueueTableMapper">

    <sql id="field">
       `guid`, `queue_guid`, `seats`, `table_guid`, `table_name`, `area_guid`, `area_name`
    </sql>

    <insert id="batchInsert">
        <foreach collection="tables" item="item" separator=";">
            insert into hss_r_reserve_record_table (<include refid="field"/> )
            select #{item.guid},#{item.queueGuid},#{item.seats},#{item.tableGuid},#{item.tableName},#{item.areaGuid},#{item.areaName}
            from dual
            where not exists (
            select 1 from
            hsq_r_queue_table t
            where
             t.table_guid = #{item.tableGuid}
            )
        </foreach>
    </insert>
</mapper>