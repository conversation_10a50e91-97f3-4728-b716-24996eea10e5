<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.staff.mapper.ProductMapper">

    <select id="selectByStoreGuid" resultType="com.holderzone.saas.store.staff.entity.domain.ProductDO">
        select * from hss_product where ( store_guid=#{storeGuid}
        <if test="withEnterpriseProduct==true">
            or store_guid is null
        </if>
        )
        and is_deleted=0;
    </select>

    <update id="updateExpirationDateByStoreGuidAndProductGuid">
        update hss_product set gmt_product_end=#{endDate} WHERE store_guid = #{storeGuid} and product_guid = #{productGuid}
    </update>

    <update id="updateExpirationDateByProductGuid">
        update hss_product set gmt_product_end=#{endDate} WHERE product_guid = #{productGuid}
    </update>
</mapper>
