<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.AdjustOrderMapper">

    <select id="listByOrderGuids" resultType="java.lang.Long">
        select
            order_guid
        from
            hst_adjust_order
        where
            is_delete = 0 and order_guid in
        <foreach collection="orderGuids" item="orderGuid" open="(" separator="," close=")">
            #{orderGuid}
        </foreach>
    </select>
</mapper>
