<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.PricingSchemesMapper">

    <sql id="baseResult">
        guid,suppliers_guid,material_guid,deal_price,deal_unit,
        enabled,deleted,gmt_create,gmt_modified
    </sql>

    <resultMap id="PricingSchemesMap" type="com.holderzone.erp.entity.domain.PricingSchemesDO">
        <result column="guid" property="guid"/>
        <result column="suppliers_guid" property="suppliersGuid"/>
        <result column="material_guid" property="materialGuid"/>
        <result column="deal_price" property="dealPrice"/>
        <result column="deal_unit" property="dealUnit"/>
        <result column="enabled" property="enabled"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="unitName" property="unitName"/>
        <result column="auxiliary_unit" jdbcType="VARCHAR" property="auxiliaryUnit"/>
        <result column="auxiliaryUnitName" property="auxiliaryUnitName"/>
        <result column="conversion_main" jdbcType="DECIMAL" property="conversionMain"/>
        <result column="conversion_auxiliary" jdbcType="DECIMAL" property="conversionAuxiliary"/>
        <result column="name" property="materialName"/>
    </resultMap>

    <resultMap id="PricingSchemesLogMap" type="com.holderzone.erp.entity.domain.PricingSchemesLogDO">
        <result column="guid" property="guid"/>
        <result column="pricing_schemes_guid" property="pricingSchemesGuid"/>
        <result column="last_deal_price" property="lastDealPrice"/>
        <result column="current_deal_price" property="currentDealPrice"/>
        <result column="operator" property="operator"/>
        <result column="enabled" property="enabled"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <insert id="savePricingSchemesLog" parameterType="list">
        insert into hse_pricing_schemes_log
        (
        guid,
        pricing_schemes_guid,
        last_deal_price,
        current_deal_price,
        operator
        )
        values
        <foreach collection="list" separator="," item="logDO">
        (
        #{logDO.guid},
        #{logDO.pricingSchemesGuid},
        #{logDO.lastDealPrice},
        #{logDO.currentDealPrice},
        #{logDO.operator}
        )
        </foreach>
    </insert>

    <insert id="savePricingSchemes">
        <foreach collection="list" separator=";" item="pricingSchemes">
        insert into hse_suppliers_pricing_schemes
        (
        guid,
        suppliers_guid,
        material_guid,
        deal_price,
        deal_unit,
        enabled
        )
        values
        (
        #{pricingSchemes.guid},
        #{pricingSchemes.suppliersGuid},
        #{pricingSchemes.materialGuid},
        #{pricingSchemes.dealPrice},
        #{pricingSchemes.dealUnit},
        #{pricingSchemes.enabled}
        )
        on duplicate key update
        guid = #{pricingSchemes.guid},
        suppliers_guid = #{pricingSchemes.suppliersGuid},
        material_guid = #{pricingSchemes.materialGuid},
        deal_price = #{pricingSchemes.dealPrice},
        deal_unit = #{pricingSchemes.dealUnit},
        enabled = #{pricingSchemes.enabled}
        </foreach>
    </insert>

    <update id="deletePricingSchemes" parameterType="list">
        update hse_suppliers_pricing_schemes
        set
        deleted = 1
        where guid in
        (
        <foreach collection="list" item="guid" separator=",">
            #{guid}
        </foreach>
        )
    </update>

    <update id="enableOrDisablePricingSchemes">
        update hse_suppliers_pricing_schemes
        set
        enabled = if(enabled = 1, 0, 1)
        where guid = #{guid}
    </update>

    <select id="getPricingSchemesListBySuppliersGuid" resultMap="PricingSchemesMap" parameterType="java.lang.String">
        select
        m.unit,m.auxiliary_unit,m.conversion_main, m.conversion_auxiliary, m.`name`,
        ( SELECT u.`name` from hse_material_unit u where u.guid = m.unit) unitName,
        ( SELECT u.`name` FROM hse_material_unit u where u.guid = m.auxiliary_unit) auxiliaryUnitName,
        ps.guid, ps.suppliers_guid, ps.material_guid, ps.deal_price, ps.deal_unit,
        ps.enabled, ps.deleted, ps.gmt_create, ps.gmt_modified
        from hse_suppliers_pricing_schemes ps
        left join hse_material m on m.guid = ps.material_guid
        where
        ps.deleted = 0
        and ps.suppliers_guid = #{suppliersGuid}
        order by gmt_create desc
    </select>

    <select id="batchQueryPricingSchemesList" resultMap="PricingSchemesMap">
        select
        <include refid="baseResult"/>
        from hse_suppliers_pricing_schemes
        <where>
            suppliers_guid = #{suppliersGuid}
            and enabled = 1 and deleted = 0
            <if test="list.size() > 0">
                and material_guid in
                (
                <foreach collection="list" item="materialGuid" separator=",">
                    #{materialGuid}
                </foreach>
                )
            </if>
        </where>
    </select>

    <update id="changeMaterialUnitInPricingSchemes">
        <foreach collection="list" item="PricingSchemes" separator=";">
            update hse_suppliers_pricing_schemes
            SET deal_unit = #{PricingSchemes.dealUnit}
            where guid = #{PricingSchemes.guid}
        </foreach>
    </update>

    <select id="getPricingSchemesByMaterialGuid" resultMap="PricingSchemesMap">
        SELECT *
        from hse_suppliers_pricing_schemes
        where material_guid = #{materialGuid}
    </select>

</mapper>