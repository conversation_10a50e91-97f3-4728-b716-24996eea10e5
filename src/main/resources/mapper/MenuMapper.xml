<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.staff.mapper.MenuMapper">
    <!-- 查询该用户、该终端下拥有的资源信息-->
    <select id="getUserModuleList" resultType="com.holderzone.saas.store.staff.entity.domain.StoreSourceDO">
        SELECT t1.module_guid,
               t1.module_name,
               t1.source_guid,
               t1.source_name,
               t1.source_code,
               t1.source_url,
               t1.page_title,
               t1.page_url
        FROM hss_store_source AS t1
                 INNER JOIN hss_role_source AS t2 ON t2.source_guid = t1.source_guid
                 INNER JOIN hss_r_user_role AS t3 ON t3.role_guid = t2.role_guid AND EXISTS(
                SELECT id FROM hss_role rr WHERE rr.is_enable = 1 and rr.guid = t3.role_guid)
        WHERE t1.terminal_code = #{terminalCode}
          AND t3.user_guid = #{userGuid}
    </select>
</mapper>
