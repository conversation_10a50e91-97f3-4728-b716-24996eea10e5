<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.organization.mapper.StoreDeviceMapper">
    <!-- 批量更新sort（排序） -->
    <update id="batchSort" parameterType="java.util.List">
        UPDATE hso_r_store_device
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="sort=case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    WHEN store_guid = #{item.storeGuid} AND device_no = #{item.deviceNo} THEN #{item.sort}
                </foreach>
            </trim>
        </trim>
        WHERE store_guid IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.storeGuid}
        </foreach>
        AND device_no IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.deviceNo}
        </foreach>
        AND is_binding = 1
    </update>

    <select id="queryTableGuidByDeviceNo" resultType="java.lang.String">
        select table_guid
        from hso_r_store_device
        where store_guid = #{storeGuid}
        and device_no = #{deviceNo}
        and is_binding = 1 limit 1
    </select>

    <select id="queryTableGuidByStoreGuid" resultType="java.lang.String">
        select table_guid
        from hso_r_store_device
        where store_guid = #{storeGuid}
        and is_binding = 1
        and table_guid is not null
    </select>

    <update id="updatePadOrderType">
        update hso_r_store_device
        set
        pad_order_type = #{padOrderType},
        table_guid= NULL
        where store_guid = #{storeGuid}
        and device_no = #{deviceNo}
    </update>

    <update id="cancelPadTableBinding">
        update hso_r_store_device
        set
        table_guid= NULL
        where store_guid = #{storeGuid}
        and device_no = #{deviceNo}
    </update>
</mapper>
