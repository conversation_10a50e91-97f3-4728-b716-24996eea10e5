<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.TradeFreeMapper">


    <select id="statistics" resultType="com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO">
        SELECT
            COALESCE(sum(d.free_count),0) as "total_quantity",
            COALESCE(sum(round(d.free_count * d.price,2)),0) as "total_money"
        FROM
        "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" a ON d.order_guid = a.guid and d.is_delete = '0'
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = a.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_item" hi ON hi.guid = d.item_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" ht ON hi.type_guid = ht.guid
        <include refid="querySQL" />
    </select>


    <select id="pageInfo" resultType="com.holderzone.saas.store.dto.report.resp.FreeItemDTO">
        SELECT
            max(d.guid) as "guid",
            max(hb.guid) as "brand_guid",
            max(hb.name) as "brand_name",
            max(o.guid) as "store_guid",
            max(o.name) as "store_name",
            case when max(hs.name) is not null and length(max(hs.name)) > 0 then concat(max(hi.name), '(', max(hs.name), ')')
            else max(hi.name) end as "goods_name",
            max(d.item_guid) as "goods_guid",
            d.sku_guid,
            max(ht.name) as "goods_categories",
            COALESCE(sum(d.free_count),0) as "given_quantity",
            COALESCE(sum(round(d.price*d.free_count,2)),0) as "given_refund"
        FROM
            "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d
        LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" a ON d.order_guid = a.guid and d.is_delete = '0'
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = a.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_brand" hb on ro.brand_guid = hb.guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_item" hi ON hi.guid = d.item_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_sku" hs ON hs.guid = d.sku_guid
        LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" ht ON hi.type_guid = ht.guid
        <include refid="querySQL" />
        group by d.sku_guid
        order by guid desc
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <select id="count" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM (
            SELECT
                d.sku_guid
            FROM
                "hst_trade_${query.enterpriseGuid}_db"."hst_order_item" d
            LEFT JOIN "hst_trade_${query.enterpriseGuid}_db"."hst_order" a ON d.order_guid = a.guid and d.is_delete = '0'
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = a.store_guid
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
            LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_brand" hb on ro.brand_guid = hb.guid
            LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_item" hi ON hi.guid = d.item_guid
            LEFT JOIN "hsi_item_${query.enterpriseGuid}_db"."hsi_type" ht ON hi.type_guid = ht.guid
            <include refid="querySQL" />
            group by d.sku_guid
        ) temp
    </select>

    <sql id="querySQL">
        <where>
            d.is_delete = 0 and d.free_count > 0
            and a.recovery_type IN ( 1, 3 )
            <![CDATA[ and a.state < 6 ]]>
            <if test="query.startTime != null">
                and a.business_day >= #{query.startTime}
                <if test="query.enterpriseGuid == '887f2181-eb06-4d77-b914-7c37c884952c'">
                    and d.business_day >= #{query.startTime}
                </if>
            </if>
            <if test="query.endTime != null">
                <![CDATA[ and a.business_day <= #{query.endTime} ]]>
                <if test="query.enterpriseGuid == '887f2181-eb06-4d77-b914-7c37c884952c'">
                    <![CDATA[ and d.business_day <= #{query.endTime} ]]>
                </if>
            </if>
            <if test="query.brandGuid != null and query.brandGuid != ''">
                and ro.brand_guid = #{query.brandGuid}
            </if>
            <if test="query.storeGuids != null and query.storeGuids.size()>0 ">
                and a.store_guid IN
                <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.goodsType != null">
                and d.item_type = #{query.goodsType}
            </if>
            <if test="query.goodsCategories != null and query.goodsCategories != ''">
                and ht.name = #{query.goodsCategories}
            </if>
            <if test="query.cateringType != null">
                and a.trade_mode = #{query.cateringType}
            </if>
        </where>
    </sql>

</mapper>
