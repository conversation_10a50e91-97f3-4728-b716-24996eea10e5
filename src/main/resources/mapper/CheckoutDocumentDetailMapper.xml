<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.CheckoutDocumentDetailMapper">

    <resultMap id="baseResultMap" type="com.holderzone.erp.entity.domain.CheckoutDocumentDetailDO">
        <result column="guid" property="guid" />
        <result column="document_guid" property="documentGuid" />
        <result column="material_guid" property="materialGuid" />
        <result column="material_code" property="materialCode" />
        <result column="material_name" property="materialName" />
        <result column="stock" property="stock" />
        <result column="unit_guid" property="unitGuid" />
        <result column="unit_name" property="unitName" />
        <result column="check_count" property="checkCount" />
        <result column="checkout_result" property="checkoutResult" />
    </resultMap>
    
    <insert id="insertCheckoutDocumentDetailList" parameterType="java.util.List">
        INSERT INTO hse_warehouse_check_document_detail
        (guid,document_guid,material_guid,material_code,material_name,
        stock,unit_guid,unit_name,check_count,checkout_result)
        values
        <foreach collection="list" item="detail" separator=",">
            (#{detail.guid},#{detail.documentGuid},#{detail.materialGuid},#{detail.materialCode},
            #{detail.materialName},#{detail.stock},#{detail.unitGuid},#{detail.unitName},
            #{detail.checkCount},#{detail.checkoutResult})
        </foreach>
    </insert>
    
    <select id="selectDocumentDetailList" parameterType="string" resultMap = "baseResultMap">
        SELECT guid,document_guid,material_guid,material_code,material_name,
        `stock`,unit_guid,unit_name,check_count,checkout_result
        FROM
        hse_warehouse_check_document_detail
        WHERE document_guid = #{documentGuid}
    </select>
</mapper>