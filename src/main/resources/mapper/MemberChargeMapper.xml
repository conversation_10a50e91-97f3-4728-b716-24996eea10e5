<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.member.mapper.MemberChargeMapper">

    <sql id="columns">
      guid,sequence_no,pay_power_id,agg_pay_order_no,agg_pay_guid,store_name,store_guid,
      bank_transaction_id,state,amount,payment_type,payment_type_name,member_guid,
      member_name,tel_phone_no,business_day,paid_time,operation_staff_guid,operation_staff_name,remark
    </sql>

    <insert id="insert" parameterType="com.holderzone.saas.store.member.domain.MemberChargeDO">
        insert into hsm_charge_record
        (
        <include refid="columns"/>
        )
        values
        (
        #{guid},#{sequenceNo},#{payPowerId},#{aggPayOrderNo},#{aggPayGuid},#{storeName},#{storeGuid},
        #{bankTransactionId},#{state},#{amount},#{paymentType},#{paymentTypeName},#{memberGuid},
        #{memberName},#{telNo},#{businessDay}, #{paidTime},#{operationStaffGuid},#{operationStaffName},#{remark}
        );
    </insert>

    <update id="updateByTradingCallBack">
        update hsm_charge_record
        <set>
            <trim suffixOverrides=",">
                <if test="aggPayPollingRespDTO.orderHolderNo!=null and aggPayPollingRespDTO.orderHolderNo!=''">
                    agg_pay_order_no = #{aggPayPollingRespDTO.orderHolderNo},
                </if>
                <if test="aggPayPollingRespDTO.bankTransactionId!=null and aggPayPollingRespDTO.bankTransactionId!=''">
                    bank_transaction_id = #{aggPayPollingRespDTO.bankTransactionId},
                </if>
                <if test="aggPayPollingRespDTO.paySt!=null and aggPayPollingRespDTO.paySt!=''">
                    state = #{aggPayPollingRespDTO.paySt},
                </if>
                <if test="aggPayPollingRespDTO.timePaid!=null and aggPayPollingRespDTO.timePaid!=''">
                    paid_time = #{aggPayPollingRespDTO.timePaid},
                </if>
                <if test="businessDay != null">
                    business_day = #{businessDay},
                </if>
            </trim>
        </set>
        where guid = #{aggPayPollingRespDTO.orderGUID}
    </update>

    <update id="updateByAggPayNotify" parameterType="com.holderzone.saas.store.dto.pay.AggPayNotifyDTO">
        update hsm_charge_record
        <set>
            <trim suffixOverrides=",">
                <if test="orderHolderNo!=null and orderHolderNo!=''">
                    agg_pay_order_no = #{orderHolderNo},
                </if>
                <if test="bankTransactionId!=null and bankTransactionId!=''">
                    bank_transaction_id = #{bankTransactionId},
                </if>
                <if test="paySt!=null and paySt!=''">
                    state = #{paySt},
                </if>
                <if test="paidTime!=null and paidTime!=''">
                    paid_time = #{paidTime},
                </if>
            </trim>
        </set>
        where guid = #{orderGUID}
    </update>

</mapper>