<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.RepertoryMapper">

    <select id="queryInOutRepertoryList" resultType="com.holderzone.erp.entity.domain.RepertoryDO">

        select * from hse_repertory
        <where>
            in_out = #{dto.inOut}
            <if test="dto.status != 0">
                and status = #{dto.status}
                <if test="dto.inOut == 0">
                    and (invoice_type = 1 or invoice_type = 7)
                </if>
                <if test="dto.inOut == 1">
                    and (invoice_type = 8 or invoice_type = 9)
                </if>
            </if>
            <if test="dto.status == 0">
                <if test="dto.inOut == 0">
                    and (invoice_type = 1 or invoice_type = 7)
                </if>
                <if test="dto.inOut == 1">
                    and (invoice_type = 8 or invoice_type = 9)
                </if>
            </if>
            <if test="dto.invoiceType != 0">
                and invoice_type = #{dto.invoiceType}
            </if>
            <if test="dto.guid != null and dto.guid != ''">
                and guid like CONCAT('%',#{dto.guid},'%' )
            </if>

        </where>
        ORDER BY gmt_create DESC
    </select>
</mapper>
