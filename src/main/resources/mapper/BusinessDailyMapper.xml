<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.retail.mapper.BusinessDailyMapper">
    <resultMap id="DailyItemMap" type="com.holderzone.saas.store.dto.order.response.daily.ItemRespDTO">
        <result column="guid" property="guid"/>
        <result column="name" property="name"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="freeNum" property="freeNum"/>
        <result column="quantum" property="quantum"/>
        <result column="amount" property="amount"/>
        <result column="item_type" property="itemType"/>
        <result column="sku_name" property="skuName"/>
    </resultMap>
    <select id="freeReturn" resultMap="DailyItemMap">
        SELECT
        i.`item_type`,
        t.`item_guid` guid,
        t.`price` unit_price,
        t.item_name AS NAME,
        i.sku_name AS sku_name,
        SUM( t.`price` * t.`count` ) AS amount,
        SUM( t.`count` ) AS quantum
        FROM
        `hst_free_return_item` t
        LEFT JOIN `hst_order` o ON o.`guid` = t.`order_guid`
        LEFT JOIN `hst_order_item` i ON i.`guid` = t.`order_item_guid`
        WHERE
        o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' ) AND concat( #{dto.endTime}, ' 23:59:59' )
        AND t.`type` = #{type}
        <if test="type == 2">
            AND (t.`is_free` = 0 or t.`is_free` is null)
            and t.`count` > 0
        </if>
        AND t.`is_delete` = 0
        AND o.`state` = 4
        AND o.store_guid = #{dto.storeGuid}
        AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
        AND o.`is_delete` = 0
        AND i.`is_delete` = 0
        AND i.`parent_item_guid` = 0
        AND i.item_type != 1
        GROUP BY
        t.`item_guid`,
        t.`item_name`,
        i.sku_name,
        t.`price`,
        i.`item_type` UNION ALL
        SELECT
        any_value ( a.`item_type` ) AS item_type,
        any_value ( a.`guid` ) guid,
        any_value ( a.`unit_price` ) unit_price,
        any_value ( a.NAME ) AS NAME,
        any_value ( a.sku_name ) AS sku_name,
        SUM( a.amount ) AS amount ,
        SUM( a.quantum ) AS quantum
        FROM
        (
        SELECT DISTINCT
        ( i.order_guid ) AS order_guid,
        any_value ( i.`item_type` ) AS item_type,
        any_value ( t.`item_guid` ) guid,
        any_value ( t.`price` ) unit_price,
        any_value ( t.item_name ) AS NAME,
        any_value ( i.sku_name ) AS sku_name,
        SUM( ii.add_price ) * any_value ( t.count ) + any_value ( i.price ) AS amount,
        any_value ( t.count ) AS quantum
        FROM
        hst_order_item i
        INNER JOIN hst_order_item ii ON i.guid = ii.parent_item_guid
        LEFT JOIN hst_free_return_item t ON t.order_item_guid = i.guid
        LEFT JOIN `hst_order` o ON o.`guid` = t.`order_guid`
        WHERE
        i.item_type = 1
        AND o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' ) AND concat( #{dto.endTime}, ' 23:59:59' )
        AND t.`type` = #{type}
        AND o.store_guid = #{dto.storeGuid}
        AND o.`is_delete` = 0
        AND i.`is_delete` = 0
        AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
        <if test="type == 2">
            AND (t.`is_free` = 0 or t.`is_free` is null)
            and t.`count` > 0
        </if>
        AND t.`is_delete` = 0
        AND o.`state` = 4
        GROUP BY
        ii.parent_item_guid,
        i.item_guid
        ) a
        GROUP BY
        guid
    </select>

    <select id="goods" resultMap="DailyItemMap">
         SELECT
                B.guid,
				0  freeNum,
                sum(B.quantum ) AS quantum,
                B.NAME,
                B.item_type,
                B.unit_price,
                B.sku_name
            FROM
                (
            SELECT
                t.`item_type` AS item_type,
                t.`item_guid` guid,
                t.`item_name` NAME,
                ( t.`price` ) unit_price,
                ( t.current_count + t.free_count ) quantum,
                t.`sku_name`
            FROM
                `hst_order_item` t
                LEFT JOIN `hst_order` o ON o.`guid` = t.`order_guid`
            WHERE
                o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                AND o.`state` = 4
                AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                AND o.`is_delete` = 0
                AND t.`is_delete` = 0
                AND o.store_guid = #{dto.storeGuid}
                AND t.`parent_item_guid` = 0
                AND t.item_type != 1
                ) B
            GROUP BY
                B.guid,
                B.NAME,
                B.item_type,
                unit_price,
                B.sku_name,
                B.unit_price
                UNION ALL
            SELECT
                any_value(i.item_guid) guid,
                sum(i.free_count)  freeNum,
							  sum(i.current_count)  quantum,
                any_value(i.item_name) NAME,
                any_value(i.item_type) item_type,
                any_value( i.price + c.c.add_price1 ) unit_price,
                any_value(i.sku_name) sku_name
            FROM
                hst_order_item AS i
                INNER JOIN (
            SELECT
                a.parent_item_guid,
                sum( a.add_price ) AS add_price1
            FROM
                (
            SELECT
                s.parent_item_guid,
                SUM( s.current_count * s.add_price )  add_price
            FROM
                hst_order_item AS s
            WHERE
                s.parent_item_guid != 0
            GROUP BY
                s.parent_item_guid,
                s.item_type
                ) a
            GROUP BY
                a.parent_item_guid
                ) c ON c.parent_item_guid = i.guid
                LEFT JOIN hst_order as o
                ON o.guid = i.order_guid
            WHERE item_type = 1
              and o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                AND o.`state` = 4
                AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                AND o.`is_delete` = 0
                AND i.`is_delete` = 0
            AND o.store_guid = #{dto.storeGuid}
            GROUP BY item_guid,unit_price
    </select>

    <resultMap id="AttrItemMap" type="com.holderzone.saas.store.dto.order.response.daily.AttrItemRespDTO"
               extends="DailyItemMap">
        <result property="attrGroupGuid" column="attr_group_guid"/>
        <result property="attrGroupName" column="attr_group_name"/>
    </resultMap>

    <select id="attr" resultMap="AttrItemMap">
        SELECT
	    t.`attr_group_name`,
        t.`attr_guid` guid,
        t.`attr_name` AS NAME,
        t.`attr_price` unit_price,
        SUM(t.`attr_price` * t.`num` * (oi.free_count +oi.current_count )) amount,
        SUM(t.`num` * (oi.free_count +oi.current_count )) quantum,
        SUM(t.num) AS num,
		oi.item_type As item_type
        FROM
        `hst_item_attr` t
        LEFT JOIN `hst_order_item` oi ON oi.`guid`=t.`order_item_guid`
        LEFT JOIN `hst_order` o ON o.`guid`=oi.`order_guid`
        WHERE
        o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
        AND o.`state`=4
        AND (o.recovery_type=1 or o.recovery_type=3)
        AND o.`is_delete`=0
        AND t.`is_delete`=0
        AND oi.`return_count`=0
        AND o.store_guid = #{dto.storeGuid}
        GROUP BY t.`attr_guid`,t.`attr_name`,t.`attr_price`,t.`attr_group_name`,t.`num`,oi.`item_type`
    </select>

    <select id="classify" resultMap="DailyItemMap">
             SELECT
                    i.item_type_guid guid,
                    any_value ( i.item_type_name ) NAME,
                    sum( i.`price` * ( i.current_count + i.free_count ) + IFNULL( a.amount, 0 ) ) amount,
                    sum( i.current_count + i.free_count ) quantum
                FROM
                    hst_order_item i
                    LEFT JOIN (
                SELECT
                    s.parent_item_guid guid,
                    sum( s.add_price * s.current_count ) amount
                FROM
                    hst_order_item s
                    INNER JOIN hst_order o ON o.guid = s.order_guid
                WHERE
                    s.parent_item_guid != 0
                    AND o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                    AND o.`state` = 4
                    AND s.return_count = 0
                    AND s.`is_delete` = 0
                    AND o.store_guid = #{dto.storeGuid}
                    AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                GROUP BY
                    s.parent_item_guid
                    ) a ON i.guid = a.guid
                    LEFT JOIN `hst_order` o ON o.`guid` = i.`order_guid`
                WHERE
                    i.item_type = 1
                    AND o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                    AND o.`state` = 4
                    AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                    AND o.`is_delete` = 0
                    AND i.return_count = 0
                    AND i.`is_delete` = 0
                    AND o.store_guid = #{dto.storeGuid}
                GROUP BY
                    item_type_guid
                UNION ALL
                SELECT
                    t.`item_type_guid` guid,
                    t.`item_type_name` NAME,
                    SUM( t.`price` * ( t.current_count + t.free_count )   )amount,
                    (sum( t.current_count ) + sum( t.free_count )) quantum
                FROM
                    `hst_order_item` t
                    LEFT JOIN `hst_order` o ON o.`guid` = t.`order_guid`
                WHERE
                    o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                    AND o.`state` = 4
                    AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                    AND o.`is_delete` = 0
                    AND t.return_count = 0
                    AND t.`is_delete` = 0
                    AND t.item_type != 1
                    AND o.store_guid = #{dto.storeGuid}
                    AND  ( parent_item_guid = 0 OR parent_item_guid IS NULL )
                GROUP BY
                    t.`item_type_guid`,
                    t.`item_type_name`
    </select>

    <resultMap id="DiningTypeResult" type="com.holderzone.saas.store.dto.order.response.daily.DiningTypeRespDTO">
        <result property="typeCode" column="type_code"/>
        <result property="typeName" column="type_name"/>
        <result property="orderCount" column="order_count"/>
        <result property="guestCount" column="guest_count"/>
        <result property="amount" column="amount"/>
    </resultMap>


    <select id="diningType" resultMap="DiningTypeResult">
        SELECT
        1 as type_code,
        '正餐' as type_name,
        COUNT(1) order_count,
        SUM(t.`guest_count`) guest_count
        FROM `hst_order` t
        WHERE t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND trade_mode = 0
        and t.`state`=4
        and (t.recovery_type=1 or t.recovery_type=3)
        AND t.`is_delete`=0
        AND t.store_guid =#{dto.storeGuid}
        UNION
        SELECT
        2 as type_code,
        '快餐' as type_name,
        COUNT(1) order_count,
        SUM(t.`guest_count`) guest_count
        FROM `hst_order` t
        WHERE t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND trade_mode = 1
        and t.`state`=4
        and (t.recovery_type=1 or t.recovery_type=3)
        AND t.`is_delete`=0
        AND t.store_guid =#{dto.storeGuid}
    </select>


    <select id="consumerAmount" resultType="decimal">
        SELECT SUM(amount)
            FROM
            hst_transaction_record t
            LEFT JOIN `hst_order` o ON t.`order_guid`=o.`guid`
            WHERE o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
            AND o.`is_delete`=0
            AND o.`state`=4
             AND t.`state`=4
            AND (o.recovery_type=1 OR o.recovery_type=3)
            AND o.trade_mode = 0
            AND o.store_guid = #{dto.storeGuid}
            UNION
            SELECT SUM(amount)
            FROM
            hst_transaction_record t
            LEFT JOIN `hst_order` o ON t.`order_guid`=o.`guid`
            WHERE o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
            AND o.`is_delete`=0
            AND o.`state`=4
             AND t.`state`=4
            AND (o.recovery_type=1 OR o.recovery_type=3)
            AND o.trade_mode = 1
            AND o.store_guid = #{dto.storeGuid}
    </select>

    <resultMap id="MemberConsumeResult" type="com.holderzone.saas.store.dto.order.response.daily.MemberConsumeRespDTO">
        <result column="order_count" property="consumerCount"/>
        <result column="amount" property="consumerAmount"/>
    </resultMap>
    <select id="memberConsume" resultMap="MemberConsumeResult">
     SELECT
            COUNT(1) order_count,
            SUM( r.`amount` ) amount
        FROM
            `hst_order` t
            INNER JOIN hst_transaction_record AS r
            ON t.guid = r.order_guid
        WHERE
            t.`gmt_create` BETWEEN  CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
            AND t.`is_delete` = 0
            AND t.store_guid =  #{dto.storeGuid}
            AND t.`state` = 4
            AND r.payment_type = 4
            AND ( t.recovery_type = 1 OR t.recovery_type = 3 )
    </select>

    <resultMap id="GatherResult" type="com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO">
        <result property="gatherCode" column="gather_code"/>
        <result property="gatherName" column="gather_name"/>
        <result property="consumerAmount" column="amount"/>
    </resultMap>
    <select id="gather" resultMap="GatherResult">
        SELECT
        t.`payment_type` gather_code,
        t.`payment_type_name` gather_name,
        SUM(t.`amount`) amount
        FROM `hst_transaction_record` t
        LEFT JOIN `hst_order` o ON o.`guid`=t.`order_guid`
        WHERE o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND t.`is_delete`=0
        AND t.`state`=4
        AND o.`state`=4
        AND  t.store_guid = #{dto.storeGuid}
        AND (o.recovery_type=1 or o.recovery_type=3)
        AND o.`is_delete`=0
        GROUP BY t.`payment_type`,t.`payment_type_name`
    </select>

    <resultMap id="OverviewResult" type="com.holderzone.saas.store.dto.order.response.daily.OverviewRespDTO">
        <result column="order_count" property="orderCount"/>
        <result column="guest_count" property="guestCount"/>
        <result column="consumer_amount" property="consumerAmount"/>
        <result column="gather_amount" property="gatherAmount"/>
        <result column="discount_amount" property="discountAmount"/>
    </resultMap>

    <resultMap id="handoverPayDTO" type="com.holderzone.saas.store.dto.business.manage.HandoverPayDTO">
        <result column="confirm_user_guid" property="userGuid"/>
        <result column="confirm_user_name" property="userName"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="business_in_coming" property="businessIncoming"/>
    </resultMap>

    <select id="overview" resultMap="OverviewResult">
      SELECT
            COUNT(*) order_count,
            SUM(t.`guest_count`) + (
                SELECT
                    IFNULL(SUM(t.`guest_count`), 0)
                FROM
                    `hst_order` t
                WHERE
                    t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
                AND t.upper_state = 2
                AND t.store_guid =  #{dto.storeGuid}
                AND t.`is_delete` = 0
                AND t.`state` = 4
                AND (
                    t.recovery_type = 1
                    OR t.recovery_type = 3
                )
            ) guest_count,
            (
                SELECT
                    IFNULL(SUM(t.order_fee), 0)
                FROM
                    `hst_order` t
                WHERE
                    t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
                AND t.upper_state <![CDATA[ <> ]]> 2
                AND t.store_guid = #{dto.storeGuid}
                AND t.`is_delete` = 0
                AND t.`state` = 4
            ) - (
                SELECT
                    IFNULL(SUM(t.order_fee), 0)
                FROM
                    `hst_order` t
                WHERE
                    t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
                AND t.store_guid = #{dto.storeGuid}
                AND t.upper_state <![CDATA[ <> ]]> 2
                AND t.`is_delete` = 0
                AND t.`state` = 5
                AND t.recovery_type = 4
            ) consumer_amount,
            (
                SELECT
                    IFNULL(SUM(t.actually_pay_fee), 0)
                FROM
                    `hst_order` t
                WHERE
                    t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
                AND t.upper_state <![CDATA[ <> ]]> 2
                AND t.store_guid = #{dto.storeGuid}
                AND t.`is_delete` = 0
                AND t.`state` = 4
            ) - (
                SELECT
                    IFNULL(SUM(t.actually_pay_fee), 0)
                FROM
                    `hst_order` t
                WHERE
                    t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
                AND t.store_guid = #{dto.storeGuid}
                AND t.upper_state <![CDATA[ <> ]]> 2
                AND t.`is_delete` = 0
                AND t.`state` = 5
                AND t.recovery_type = 4
            ) gather_amount
        FROM
            `hst_order` t
        WHERE
            t.`checkout_time` BETWEEN  CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND t.upper_state <![CDATA[ <> ]]> '2'
        AND t.store_guid = #{dto.storeGuid}
        AND t.`is_delete` = 0
        AND t.`state` = 4
        AND (t.recovery_type=1 or t.recovery_type=3);
    </select>

    <resultMap id="AmountItemDTO" type="com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO">
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="amount" property="amount"/>
    </resultMap>
    <select id="paymentTypeCount" resultMap="AmountItemDTO">
        SELECT
        t.`payment_type` code,
        t.`payment_type_name` name,
        SUM(t.`amount`) amount
        FROM `hst_transaction_record` t
        LEFT JOIN `hst_order` o ON o.`guid`=t.`order_guid`
        WHERE o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND t.store_guid = #{dto.storeGuid}
        AND t.`is_delete`=0
        AND t.`state`=4
        AND o.`state`=4
        AND (o.recovery_type=1 or o.recovery_type=3)
        AND o.`is_delete`=0
        GROUP BY t.`payment_type`,t.`payment_type_name`
    </select>

    <select id="discountTypeCount" resultMap="AmountItemDTO">
        SELECT
        t.`discount_type` code,
        t.`discount_name` name,
        SUM(t.`discount_fee`) amount
        FROM `hst_discount` t
        LEFT JOIN `hst_order` o ON o.`guid`=t.`order_guid`
        WHERE o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND t.store_guid = #{dto.storeGuid}
        AND t.`is_delete`=0
        AND (o.`state`=4 or o.`state`=5)
        AND o.`is_delete`=0
        GROUP BY t.`discount_type`,t.`discount_name`
    </select>

    <select id="handover" resultMap="handoverPayDTO">
        SELECT
            confirm_user_guid,
            confirm_user_name,
            gmt_create,
            gmt_modified,
            business_in_coming
        FROM
            hsb_handover_record
        WHERE
            confirm_user_guid IS NOT NULL
        ORDER BY
            id DESC
    </select>
</mapper>
