<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.BusinessDailyMapper">
    <resultMap id="DailyItemMap" type="com.holderzone.saas.store.dto.order.response.daily.ItemRespDTO">
        <result column="guid" property="guid"/>
        <result column="name" property="name"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="freeNum" property="freeNum"/>
        <result column="quantum" property="quantum"/>
        <result column="amount" property="amount"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="item_type" property="itemType"/>
        <result column="sku_name" property="skuName"/>
    </resultMap>
    <select id="freeReturn" resultMap="DailyItemMap">
        SELECT
        i.`item_type`,
        t.`item_guid` guid,
        t.`price` unit_price,
        t.item_name AS NAME,
        i.sku_name AS sku_name,
        SUM( ROUND(t.`price` * ( t.`count` + t.refund_count ), 2) ) AS amount,
        SUM( t.`count` + t.refund_count ) AS quantum
        FROM
        `hst_free_return_item` t
        LEFT JOIN `hst_order` o ON o.`guid` = t.`order_guid`
        LEFT JOIN `hst_order_item` i ON i.`guid` = t.`order_item_guid`
        WHERE
        o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' ) AND concat( #{dto.endTime}, ' 23:59:59' )
        AND t.`type` = #{type}
        <if test="type == 2">
            AND (t.`is_free` = 0 or t.`is_free` is null)
            and t.`count` > 0
        </if>
        AND t.`is_delete` = 0
        AND o.`state` = 4
        AND o.store_guid = #{dto.storeGuid}
        AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
        AND o.`is_delete` = 0
        AND i.`is_delete` = 0
        AND i.`parent_item_guid` = 0
        AND i.item_type != 1
        GROUP BY
        t.`item_guid`,
        t.`item_name`,
        i.sku_name,
        t.`price`,
        i.`item_type` UNION ALL
        SELECT
        any_value ( a.`item_type` ) AS item_type,
        any_value ( a.`guid` ) guid,
        any_value ( a.`unit_price` ) unit_price,
        any_value ( a.NAME ) AS NAME,
        any_value ( a.sku_name ) AS sku_name,
        SUM( a.amount ) AS amount ,
        SUM( a.quantum ) AS quantum
        FROM
        (
        SELECT DISTINCT
        ( i.order_guid ) AS order_guid,
        any_value ( i.`item_type` ) AS item_type,
        any_value ( t.`item_guid` ) guid,
        any_value ( t.`price` ) unit_price,
        any_value ( t.item_name ) AS NAME,
        any_value ( i.sku_name ) AS sku_name,
        SUM( ii.add_price ) * ( any_value ( t.count ) + any_value ( t.refund_count ) )
        + any_value ( i.price ) AS amount,
        any_value ( t.count ) + any_value ( t.refund_count ) AS quantum
        FROM
        hst_order_item i
        INNER JOIN hst_order_item ii ON i.guid = ii.parent_item_guid
        LEFT JOIN hst_free_return_item t ON t.order_item_guid = i.guid
        LEFT JOIN `hst_order` o ON o.`guid` = t.`order_guid`
        WHERE
        i.item_type = 1
        AND o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' ) AND concat( #{dto.endTime}, ' 23:59:59' )
        AND t.`type` = #{type}
        AND o.store_guid = #{dto.storeGuid}
        AND o.`is_delete` = 0
        AND i.`is_delete` = 0
        AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
        <if test="type == 2">
            AND (t.`is_free` = 0 or t.`is_free` is null)
            and t.`count` > 0
        </if>
        AND t.`is_delete` = 0
        AND o.`state` = 4
        GROUP BY
        ii.parent_item_guid,
        i.item_guid
        ) a
        GROUP BY
        guid
    </select>

    <select id="goods" resultMap="DailyItemMap">
            SELECT
                B.guid,
				0 freeNum,
                sum(B.quantum ) AS quantum,
                sum(amount) as amount,
                sum(discount_amount) as discount_amount,
                B.NAME,
                B.item_type,
                B.unit_price,
                B.sku_name
            FROM
                (
                SELECT
                    t.`item_type` AS item_type,
                    t.`item_guid` guid,
                    t.`item_name` NAME,
                    ( t.`price` ) unit_price,
                    ( t.current_count -IFNULL( t.refund_count, 0 ) + t.free_refund_count ) quantum,
                    ROUND(t.`price` * ( t.current_count - IFNULL( t.refund_count, 0 ) + t.free_refund_count ), 2) amount,
                    case
                    when t.current_count = 0 then 0
                    when t.refund_count - t.free_refund_count = 0 then t.discount_total_price
                    when t.refund_count - t.free_refund_count > 0 then t.discount_total_price * ((t.current_count - t.refund_count + t.free_refund_count) / t.current_count)
                    else t.discount_total_price end as discount_amount,
                    t.`sku_name`
                FROM
                    `hst_order_item` t
                    LEFT JOIN `hst_order` o ON o.`guid` = t.`order_guid`
                WHERE
                    o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                    AND o.`state` = 4
                    AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                    AND o.`is_delete` = 0
                    AND t.`is_delete` = 0
                    AND o.store_guid = #{dto.storeGuid}
                    AND t.`parent_item_guid` = 0
                    AND t.item_type != 1
                    <if test="dto.typeGuidList != null and dto.typeGuidList.size() > 0">
                        AND t.item_type_guid in
                        <foreach collection="dto.typeGuidList" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                ) B
            GROUP BY
                B.guid,
                B.NAME,
                B.item_type,
                unit_price,
                B.sku_name,
                B.unit_price

            UNION ALL

            SELECT
                any_value(i.item_guid) guid,
                sum(i.free_count) freeNum,
				sum(i.current_count - IFNULL( i.refund_count, 0 ) + i.free_refund_count ) quantum,
                sum( ROUND((i.price + c.add_price) * (i.current_count - IFNULL( i.refund_count, 0 ) + i.free_refund_count), 2) ) as amount,
                sum( case
                    when i.current_count = 0 then 0
                    when i.refund_count - i.free_refund_count = 0 then i.discount_total_price
                    when i.refund_count - i.free_refund_count > 0 then i.discount_total_price * ((i.current_count - i.refund_count + i.free_refund_count) / i.current_count)
                    else i.discount_total_price end ) as discount_amount,
                any_value(i.item_name) NAME,
                any_value(i.item_type) item_type,
                any_value( i.price + c.add_price ) unit_price,
                any_value(i.sku_name) sku_name
            FROM
                hst_order_item AS i
                INNER JOIN (
                        SELECT
                            s.parent_item_guid,
                            SUM( s.current_count * s.add_price )  add_price
                        FROM
                            hst_order_item AS s
                        JOIN hst_order as os ON os.guid = s.order_guid
                            and os.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                            AND os.`state` = 4
                            AND ( os.recovery_type = 1 OR os.recovery_type = 3 )
                            AND os.`is_delete` = 0
                            AND os.store_guid = #{dto.storeGuid}
                        WHERE
                            s.parent_item_guid != 0
                        GROUP BY
                            s.parent_item_guid
                ) c ON c.parent_item_guid = i.guid
                LEFT JOIN hst_order as o ON o.guid = i.order_guid
            WHERE
                item_type = 1
                and o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                AND o.`state` = 4
                AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                AND o.`is_delete` = 0
                AND i.`is_delete` = 0
                AND o.store_guid = #{dto.storeGuid}
                <if test="dto.typeGuidList != null and dto.typeGuidList.size() > 0">
                    AND i.item_type_guid in
                    <foreach collection="dto.typeGuidList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            GROUP BY
                item_guid,
                unit_price
        UNION ALL
            SELECT
                x.guid AS guid,
                0 AS freeNum,
                (x.count - IFNULL(y.count,0)) quantum,
                (x.amount - IFNULL(y.amount,0)) AS amount,
                (x.amount - IFNULL(y.amount,0)) AS discount_amount,
                x.name AS NAME,
                0 AS item_type,
                x.unit_price,
                x.name AS sku_name

            FROM
            (SELECT
                any_value(haf.guid) guid,
                haf.name,
                SUM(ROUND(haf.amount/haf.unit_price,2)) count,
                SUM(haf.amount) amount,
                any_value(haf.unit_price) AS unit_price
                FROM
                hst_append_fee haf
            JOIN hst_order ho ON haf.order_guid = ho.guid
                WHERE
                ho.recovery_type IN ( 1, 3 )
                AND ho.state = 4
                AND ho.checkout_time  BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                AND ho.store_guid = #{dto.storeGuid}
                GROUP BY haf.name) x LEFT JOIN
            (SELECT
                any_value(haf.guid) guid,
                haf.name,
                SUM(ROUND(haf.amount/haf.unit_price,2)) count,
                SUM(haf.amount) amount,
                any_value(haf.unit_price) AS unit_price
            FROM
                hst_append_fee haf
                JOIN hst_order hro  ON haf.order_guid = hro.guid
            WHERE
                hro.state = 5
                AND hro.recovery_type =4
                AND hro.recovery_id = 0
                AND hro.cancel_time  BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                AND hro.store_guid = #{dto.storeGuid}
                GROUP BY haf.name) y ON x.name = y.name
    </select>

    <select id="singleGoods" resultMap="DailyItemMap">
        SELECT
            B.guid,
            0  freeNum,
            sum(B.quantum ) AS quantum,
            B.NAME,
            B.item_type,
            B.unit_price,
            B.sku_name
        FROM
            (
                SELECT
                    t.`item_type` AS item_type,
                    t.`item_guid` guid,
                    t.`item_name` NAME,
                    ( t.`price` ) unit_price,
                    ( t.current_count + t.free_count ) quantum,
                    t.`sku_name`
                FROM
                    `hst_order_item` t
                        LEFT JOIN `hst_order` o ON o.`guid` = t.`order_guid`
                WHERE
                    o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                  AND o.`state` = 4
                  AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                  AND o.`is_delete` = 0
                  AND t.`is_delete` = 0
                  AND o.store_guid = #{dto.storeGuid}
                  AND t.`parent_item_guid` = 0
                  <!-- 2多规格 3称重 4单品 -->
                  AND t.item_type IN (2, 3, 4)
            ) B
        GROUP BY
            B.guid,
            B.NAME,
            B.item_type,
            unit_price,
            B.sku_name,
            B.unit_price
    </select>

    <select id="packageGoods" resultMap="DailyItemMap">
        SELECT
            any_value(i.item_guid) guid,
            sum(i.free_count)  freeNum,
            sum(i.current_count)  quantum,
            any_value(i.item_name) NAME,
            any_value(i.item_type) item_type,
            any_value( i.price + c.add_price) unit_price,
            any_value(i.sku_name) sku_name
        FROM
            hst_order_item AS i
                INNER JOIN (
                    SELECT
                        s.parent_item_guid,
                        SUM( s.current_count * s.add_price )  add_price
                    FROM
                        hst_order_item AS s JOIN hst_order as os
                        ON os.guid = s.order_guid
                        and os.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                        AND os.`state` = 4
                        AND ( os.recovery_type = 1 OR os.recovery_type = 3 )
                        AND os.`is_delete` = 0
                        AND os.store_guid = #{dto.storeGuid}
                    WHERE
                        s.parent_item_guid != 0
                    GROUP BY
                        s.parent_item_guid
                ) c
             ON c.parent_item_guid = i.guid
                LEFT JOIN hst_order as o
                          ON o.guid = i.order_guid
        <!-- 1套餐 5宴会套餐 -->
        WHERE item_type IN (1, 5)
          and o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
          AND o.`state` = 4
          AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
          AND o.`is_delete` = 0
          AND i.`is_delete` = 0
          AND o.store_guid = #{dto.storeGuid}
        GROUP BY item_guid,unit_price
    </select>

    <resultMap id="AttrItemMap" type="com.holderzone.saas.store.dto.order.response.daily.AttrItemRespDTO"
               extends="DailyItemMap">
        <result property="attrGroupGuid" column="attr_group_guid"/>
        <result property="attrGroupName" column="attr_group_name"/>
    </resultMap>

    <select id="attr" resultMap="AttrItemMap">
        SELECT
	    t.`attr_group_name`,
        t.`attr_guid` guid,
        t.`attr_name` AS NAME,
        t.`attr_price` unit_price,
        SUM(t.`attr_price` * t.`num` * (oi.free_count +oi.current_count )) amount,
        SUM(t.`num` * (oi.free_count +oi.current_count )) quantum,
        SUM(t.num) AS num,
		oi.item_type As item_type
        FROM
        `hst_item_attr` t
        LEFT JOIN `hst_order_item` oi ON oi.`guid`=t.`order_item_guid`
        LEFT JOIN `hst_order` o ON o.`guid`=oi.`order_guid`
        WHERE
        o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
        AND o.`state`=4
        AND (o.recovery_type=1 or o.recovery_type=3)
        AND o.`is_delete`=0
        AND t.`is_delete`=0
        AND o.store_guid = #{dto.storeGuid}
        GROUP BY t.`attr_guid`,t.`attr_name`,t.`attr_price`,t.`attr_group_name`,t.`num`,oi.`item_type`
    </select>

    <select id="classify" resultMap="DailyItemMap">
            SELECT
                    i.item_type_guid guid,
                    any_value ( i.item_type_name ) NAME,
                    sum( ROUND(i.`price` * ( i.current_count - IFNULL( i.refund_count, 0 ) + IFNULL(i.free_refund_count, 0) ), 2) + IFNULL( a.amount, 0 ) ) amount,
                    sum(case
                        when i.refund_count - i.free_refund_count = 0 then i.discount_total_price
                        when i.refund_count - i.free_refund_count > 0 then i.discount_total_price * ((i.current_count - i.refund_count + i.free_refund_count) / i.current_count)
                        else i.discount_total_price end ) as discount_amount,
                    sum( i.current_count + i.free_count - IFNULL( i.refund_count, 0 ) ) quantum
                FROM
                    hst_order_item i
                    LEFT JOIN (
                        SELECT
                            s.parent_item_guid guid,
                            sum( s.add_price * s.current_count ) amount
                        FROM
                            hst_order_item s
                            INNER JOIN hst_order o ON o.guid = s.order_guid
                        WHERE
                            s.parent_item_guid != 0
                            AND o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                            AND o.`state` = 4
                            AND IF((s.current_count = 0 AND s.free_count = 0),(s.return_count = 0),1=1)
                            AND s.`is_delete` = 0
                            AND o.store_guid = #{dto.storeGuid}
                            AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                        GROUP BY
                            s.parent_item_guid
                    ) a ON i.guid = a.guid
                    LEFT JOIN `hst_order` o ON o.`guid` = i.`order_guid`
                WHERE
                    i.item_type = 1
                    AND o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                    AND o.`state` = 4
                    AND i.current_count != 0
                    AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                    AND o.`is_delete` = 0
                    AND IF((i.current_count = 0 AND i.free_count = 0),(i.return_count = 0),1=1)
                    AND i.`is_delete` = 0
                    AND o.store_guid = #{dto.storeGuid}
                GROUP BY
                    item_type_guid
                UNION ALL
                SELECT
                    t.`item_type_guid` guid,
                    t.`item_type_name` NAME,
                    SUM( ROUND(t.price * ( t.current_count - IFNULL( t.refund_count, 0 ) + IFNULL( t.free_refund_count, 0) ), 2) )amount,
                    sum(case
                        when t.refund_count - t.free_refund_count = 0 then t.discount_total_price
                        when t.refund_count - t.free_refund_count > 0 then t.discount_total_price * ((t.current_count - t.refund_count + t.free_refund_count) / t.current_count)
                        else t.discount_total_price end ) as discount_amount,
                    (sum( t.current_count ) - sum( IFNULL( t.refund_count, 0 )) + sum( IFNULL( t.free_refund_count, 0 )) ) quantum
                FROM
                    `hst_order_item` t
                    LEFT JOIN `hst_order` o ON o.`guid` = t.`order_guid`
                WHERE
                    o.`checkout_time` BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                    AND o.`state` = 4
                    AND t.current_count != 0
                    AND ( o.recovery_type = 1 OR o.recovery_type = 3 )
                    AND o.`is_delete` = 0
                    AND IF((t.current_count = 0 AND t.free_count = 0),(t.return_count = 0),1=1)
                    AND t.`is_delete` = 0
                    AND t.item_type != 1
                    AND o.store_guid = #{dto.storeGuid}
                    AND  ( parent_item_guid = 0 OR parent_item_guid IS NULL )
                GROUP BY
                    t.`item_type_guid`,
                    t.`item_type_name`

                UNION ALL
            SELECT
                IF(x.type =0,"0","1") AS guid,
                CASE x.type WHEN 0 THEN 'SURCHARGE_PER_PERSON' ELSE 'SURCHARGE_PER_TABLE' END AS NAME,
                (IFNULL(x.amount,0) - IFNULL(y.amount,0)) AS amount,
                (IFNULL(x.amount,0) - IFNULL(y.amount,0)) AS discount_amount,
                (IFNULL(x.count,0)-IFNULL(y.count,0)) quantum

            FROM
                (SELECT
                     haf.type,
                     SUM(ROUND(haf.amount/haf.unit_price,2)) count,
	                 SUM(haf.amount) amount
                 FROM
                     hst_append_fee haf
                     JOIN hst_order ho ON haf.order_guid = ho.guid
                 WHERE
                     ho.recovery_type IN ( 1, 3 )
                   AND ho.state = 4
                   AND ho.checkout_time BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                   AND ho.store_guid = #{dto.storeGuid}
                 GROUP BY haf.type) x LEFT JOIN
                (SELECT
                     haf.type,
                     SUM(ROUND(haf.amount/haf.unit_price,2)) count,
	                SUM(haf.amount) amount
                 FROM
                     hst_append_fee haf
                     JOIN hst_order hro  ON haf.order_guid = hro.guid
                 WHERE
                     hro.state = 5
                   AND hro.recovery_type =4
                   AND hro.recovery_id = 0
                   AND hro.cancel_time BETWEEN concat( #{dto.beginTime}, ' 00:00:00' )  AND concat( #{dto.endTime}, ' 23:59:59' )
                   AND hro.store_guid = #{dto.storeGuid}
                 GROUP BY haf.type) y ON x.type = y.type
    </select>

    <resultMap id="DiningTypeResult" type="com.holderzone.saas.store.dto.order.response.daily.DiningTypeRespDTO">
        <result property="typeCode" column="type_code"/>
        <result property="typeName" column="type_name"/>
        <result property="orderCount" column="order_count"/>
        <result property="guestCount" column="guest_count"/>
        <result property="amount" column="amount"/>
    </resultMap>


    <select id="diningType" resultMap="DiningTypeResult">
        SELECT
        1 AS type_code,
        '正餐' AS type_name,
        COUNT( 1 ) order_count,
        SUM( t.`guest_count` ) +(
            SELECT
                IFNULL( SUM( t.`guest_count` ), 0 )
            FROM
                `hst_order` t
            WHERE
                t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
            AND t.upper_state = 2
            AND t.store_guid = #{dto.storeGuid}
            AND t.`is_delete` = 0
            AND t.`state` = 4
            AND ( t.recovery_type = 1 OR t.recovery_type = 3 )
        ) guest_count
        FROM
            `hst_order` t
        WHERE
            t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND trade_mode = 0
        AND t.`state` = 4
        AND t.`upper_state` not in (2, 4)
        AND ( t.recovery_type = 1 OR t.recovery_type = 3 )
        AND t.`is_delete` = 0
        AND t.store_guid = #{dto.storeGuid}

        UNION

        SELECT
            2 AS type_code,
            '快餐' AS type_name,
            COUNT( 1 ) order_count,
            SUM( t.`guest_count` ) guest_count
        FROM
            `hst_order` t
        WHERE
            t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND trade_mode = 1
        AND t.`state` = 4
        AND t.`upper_state` != 2
        AND ( t.recovery_type = 1 OR t.recovery_type = 3 )
        AND t.`is_delete` = 0
        AND t.store_guid = #{dto.storeGuid}
    </select>

    <resultMap id="RefundResult" type="com.holderzone.saas.store.dto.order.response.daily.RefundAmountDTO">
        <result property="refundCode" column="refund_code"/>
        <result property="refundName" column="refund_name"/>
        <result property="refundOrderCount" column="refund_order_count"/>
        <result property="refundAmount" column="refund_amount"/>
    </resultMap>

    <select id="refund" resultMap="RefundResult">
        SELECT 1 AS refund_code,
            '正餐部分退款' AS refund_name,
            COUNT(1) AS refund_order_count,
            IFNULL(SUM(horr.refund_amount), 0) AS refund_amount
        FROM `hst_order` ho
            JOIN `hst_order_refund_record` horr ON ho.guid = horr.order_guid
        WHERE ho.`checkout_time` BETWEEN CONCAT(#{dto.beginTime}, ' 00:00:00') AND CONCAT(#{dto.endTime}, ' 23:59:59')
            AND ho.`trade_mode` = 0
            AND ho.`state` = 4
            AND ho.`upper_state` != 2
            AND (
                ho.recovery_type = 1
                OR ho.recovery_type = 3
            )
            AND ho.`is_delete` = 0
            AND ho.store_guid = #{dto.storeGuid}
            <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                AND ho.checkout_staff_guid in
                <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                    #{checkoutStaffGuid}
                </foreach>
            </if>
        UNION ALL
        SELECT 2 AS refund_code,
            '正餐反结账' AS refund_name,
            COUNT(1) AS refund_order_count,
            IFNULL(SUM(ho.order_fee), 0) AS refund_amount
        FROM `hst_order` ho
        WHERE ho.`checkout_time` BETWEEN CONCAT(#{dto.beginTime}, ' 00:00:00') AND CONCAT(#{dto.endTime}, ' 23:59:59')
            AND ho.`trade_mode` = 0
            AND ho.`state` = 7
            AND ho.`upper_state` != 2
            AND ho.`is_delete` = 0
            AND ho.store_guid = #{dto.storeGuid}
            <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                AND ho.checkout_staff_guid in
                <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                    #{checkoutStaffGuid}
                </foreach>
            </if>
        UNION ALL
        SELECT 3 AS refund_code,
            '快销部分退款' AS refund_name,
            COUNT(1) AS refund_order_count,
            IFNULL(SUM(horr.refund_amount), 0) AS refund_amount
        FROM `hst_order` ho
            JOIN `hst_order_refund_record` horr ON ho.guid = horr.order_guid
        WHERE ho.`checkout_time` BETWEEN CONCAT(#{dto.beginTime}, ' 00:00:00') AND CONCAT(#{dto.endTime}, ' 23:59:59')
            AND ho.`trade_mode` = 1
            AND ho.`state` = 4
            AND ho.`upper_state` != 2
            AND (
                ho.recovery_type = 1
                OR ho.recovery_type = 3
            )
            AND ho.`is_delete` = 0
            AND ho.store_guid = #{dto.storeGuid}
            <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                AND ho.checkout_staff_guid in
                <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                    #{checkoutStaffGuid}
                </foreach>
            </if>
        UNION ALL
        SELECT 4 AS refund_code,
            '快销反结账' AS refund_name,
            COUNT(1) AS refund_order_count,
            IFNULL(SUM(ho.order_fee), 0) AS refund_amount
        FROM `hst_order` ho
        WHERE ho.`checkout_time` BETWEEN CONCAT(#{dto.beginTime}, ' 00:00:00') AND CONCAT(#{dto.endTime}, ' 23:59:59')
            AND ho.`trade_mode` = 1
            AND ho.`state` = 7
            AND ho.`upper_state` != 2
            AND ho.`is_delete` = 0
            AND ho.store_guid = #{dto.storeGuid}
            <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                AND ho.checkout_staff_guid in
                <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                    #{checkoutStaffGuid}
                </foreach>
            </if>
    </select>

    <select id="consumerAmount" resultType="decimal">
        SELECT
        IFNULL(
        SUM(
        ord.actually_pay_fee - ord.refund_amount + IFNULL( tr.amount, 0 ) - IFNULL( ord.excess_amount, 0 )),
        0
        )
        FROM
        hst_order ord
        LEFT JOIN hst_transaction_record tr ON tr.order_guid = ord.guid
        AND tr.payment_type = 13
        AND tr.state = 4
        AND tr.trade_type = 1
        WHERE
        ord.checkout_time BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND ord.store_guid = #{dto.storeGuid}
        AND ord.STATE = 4
        AND ord.recovery_type IN ( 1, 3 )
        AND ord.trade_mode = 0 UNION
        SELECT
        IFNULL(
        SUM(
        ord.actually_pay_fee - ord.refund_amount + IFNULL( tr.amount, 0 ) - IFNULL( ord.excess_amount, 0 )),
        0
        )
        FROM
        hst_order ord
        LEFT JOIN hst_transaction_record tr ON tr.order_guid = ord.guid
        AND tr.payment_type = 13
        AND tr.state = 4
        AND tr.trade_type = 1
        WHERE
        ord.checkout_time BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND ord.store_guid = #{dto.storeGuid}
        AND ord.STATE = 4
        AND ord.recovery_type IN ( 1, 3 )
        AND ord.trade_mode = 1
    </select>

    <select id="consumerGrouponAmount"
            resultType="com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO">
        SELECT
        o.trade_mode as "code",
        SUM(IFNULL(g.coupon_buy_price,0)) as "amount"
        FROM
        `hst_order` o
        LEFT JOIN `hst_groupon` g ON o.`guid` = g.`order_guid` AND g.`is_delete` = 0
        WHERE
        o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND o.`is_delete`=0 AND o.`state` = 4 AND o.store_guid = #{dto.storeGuid} and g.`groupon_type` IN ( 6, 61, 65 )
        AND g.refund_order_guid IS NULL
        group by o.trade_mode
    </select>

    <resultMap id="MemberConsumeResult" type="com.holderzone.saas.store.dto.order.response.daily.MemberConsumeRespDTO">
        <result column="order_count" property="consumerCount"/>
        <result column="amount" property="consumerAmount"/>
    </resultMap>
    <select id="memberConsume" resultMap="MemberConsumeResult">
     SELECT
            COUNT(1) order_count,
            SUM( r.`amount` ) amount
        FROM
            `hst_order` t
            INNER JOIN hst_transaction_record AS r
            ON t.guid = r.order_guid
        WHERE
            t.`gmt_create` BETWEEN  CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
            AND t.`is_delete` = 0
            AND t.store_guid =  #{dto.storeGuid}
            AND t.`state` = 4
            AND r.payment_type = 4
            AND ( t.recovery_type = 1 OR t.recovery_type = 3 )
    </select>

    <resultMap id="GatherResult" type="com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO">
        <result property="gatherCode" column="gather_code"/>
        <result property="gatherName" column="gather_name"/>
        <result property="consumerAmount" column="amount"/>
        <result property="payPowerId" column="pay_power_id"/>
    </resultMap>
    <select id="gather" resultMap="GatherResult">
        SELECT
        g.gather_code,
        g.gather_name,
        SUM( g.amount ) amount,
        g.pay_power_id
        FROM
        (
            (
            SELECT
            t.`payment_type` gather_code,
            t.`payment_type_name` gather_name,
            SUM( case when t.is_multiple_agg_pay = 1 then t.amount - IFNULL(t.refund_amount, 0) else t.amount end) AS amount,
            t.`pay_power_id`
            FROM `hst_transaction_record` t
            LEFT JOIN `hst_order` o ON o.`guid`=t.`order_guid`
            WHERE o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
            AND t.`is_delete`=0
            AND t.`state`=4
            AND o.`state`=4
            AND  t.store_guid = #{dto.storeGuid}
            AND o.`is_delete`=0
            GROUP BY t.`payment_type`,t.`payment_type_name`,t.`pay_power_id`
            )
            UNION ALL
            (
            SELECT
            t.`payment_type` gather_code,
            t.`payment_type_name` gather_name,
            SUM(t.`amount` ) amount,
            t.`pay_power_id`
            FROM `hst_transaction_record` t
            LEFT JOIN `hst_order` o ON o.`guid`=t.`order_guid`
            LEFT JOIN `hst_multiple_transaction_record` mtr on mtr.transaction_record_guid = t.guid
            WHERE o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
            AND mtr.original_multiple_transaction_record_guid is null
            AND t.`is_delete`=0
            AND t.`state`=4
            AND o.`state` = 5
            AND o.recovery_id = '0'
            AND  t.store_guid = #{dto.storeGuid}
            AND o.`is_delete`=0
            GROUP BY t.`payment_type`,t.`payment_type_name`,t.`pay_power_id`
            )
        ) g
        GROUP BY
        g.gather_code,g.pay_power_id, case when g.gather_code = 10 then g.gather_name else null end
    </select>

    <resultMap id="OverviewResult" type="com.holderzone.saas.store.dto.order.response.daily.OverviewRespDTO">
        <result column="order_count" property="orderCount"/>
        <result column="guest_count" property="guestCount"/>
        <result column="consumer_amount" property="consumerAmount"/>
        <result column="gather_amount" property="gatherAmount"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="cost_amount" property="costAmount"/>
        <result column="refund_amount" property="refundAmount"/>
    </resultMap>

    <resultMap id="handoverPayDTO" type="com.holderzone.saas.store.dto.business.manage.HandoverPayDTO">
        <result column="confirm_user_guid" property="userGuid"/>
        <result column="confirm_user_name" property="userName"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="business_in_coming" property="businessIncoming"/>
    </resultMap>

    <select id="overview" resultMap="OverviewResult">
      SELECT
            count(1) as order_count,
            SUM(t.`guest_count`) + (
                SELECT
                    IFNULL(SUM( t.`guest_count` ), 0)
                FROM
                    `hst_order` t
                WHERE
                    t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
                AND t.upper_state = 2
                AND t.`is_delete` = 0
                AND t.`state` =4
                AND (
                    t.recovery_type = 1
                    OR t.recovery_type = 3
                )
                <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
                    AND t.store_guid =  #{dto.storeGuid}
                </if>
                <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
                    AND t.store_guid in
                    <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                    AND t.checkout_staff_guid in
                    <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                        #{checkoutStaffGuid}
                    </foreach>
                </if>
            ) guest_count,
            (
                SELECT
                    IFNULL(SUM(t.order_fee), 0)
                FROM
                    `hst_order` t
                WHERE
                    t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
                AND t.upper_state <![CDATA[ <> ]]> 2
                AND t.`is_delete` = 0
                AND t.`state` =4
                <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
                    AND t.store_guid =  #{dto.storeGuid}
                </if>
                <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
                    AND t.store_guid in
                    <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                    AND t.checkout_staff_guid in
                    <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                        #{checkoutStaffGuid}
                    </foreach>
                </if>
            ) consumer_amount,
            (
                SELECT
                IFNULL(SUM(t.actually_pay_fee - t.refund_amount + IFNULL(tr.amount,0) - IFNULL(t.excess_amount,0) ), 0)
                FROM
                    `hst_order` t
                    LEFT JOIN `hst_transaction_record` tr ON tr.order_guid = t.guid AND tr.payment_type = 13 AND tr.state = 4 AND tr.trade_type = 1
                WHERE
                    t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
                AND t.upper_state <![CDATA[ <> ]]> 2
                AND t.`is_delete` = 0
                AND t.`state` =4
                <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
                    AND t.store_guid =  #{dto.storeGuid}
                </if>
                <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
                    AND t.store_guid in
                    <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                    AND t.checkout_staff_guid in
                    <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                        #{checkoutStaffGuid}
                    </foreach>
                </if>
            ) gather_amount,
            (
                SELECT
                    IFNULL( SUM( round( IFNULL( oie.cost_price * (oi.current_count - oi.refund_count + oi.free_refund_count),0),2) ), 0) cost_amount
                FROM
                    `hst_order` t
                    LEFT JOIN hst_order_item oi ON oi.order_guid = t.guid and oi.is_delete = 0
                    LEFT JOIN hst_order_item_extends oie ON oie.guid = oi.guid and oie.is_delete = 0
                WHERE
                    t.`checkout_time` BETWEEN  CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
                    AND t.upper_state <![CDATA[ <> ]]> '2'
                    AND t.`is_delete` = 0
                    AND t.`state` = 4
                    <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
                        AND t.store_guid =  #{dto.storeGuid}
                    </if>
                    <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
                        AND t.store_guid in
                        <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                        AND t.checkout_staff_guid in
                        <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                            #{checkoutStaffGuid}
                        </foreach>
                    </if>
            ) cost_amount,
            (
                SELECT
                SUM(IFNULL(tr.refund_amount, 0)) refund_amount
                FROM
                    `hst_order` t
                    LEFT JOIN `hst_order_refund_record` tr ON tr.order_guid = t.guid
                WHERE
                    t.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
                AND t.upper_state <![CDATA[ <> ]]> 2
                AND t.`is_delete` = 0
                AND t.`state` =4
                <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
                    AND t.store_guid =  #{dto.storeGuid}
                </if>
                <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
                    AND t.store_guid in
                    <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                    AND t.checkout_staff_guid in
                    <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                        #{checkoutStaffGuid}
                    </foreach>
                </if>
            ) refund_amount
        FROM
            `hst_order` t
        WHERE
            t.`checkout_time` BETWEEN  CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND t.upper_state not in (2, 4)
        AND t.`is_delete` = 0
        AND t.`state`=4
        AND (t.recovery_type=1 or t.recovery_type=3)
        <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
            AND t.store_guid =  #{dto.storeGuid}
        </if>
        <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
            AND t.store_guid in
            <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
            AND t.checkout_staff_guid in
            <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
    </select>

    <resultMap id="AmountItemDTO" type="com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO">
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="amount" property="amount"/>
        <result column="order_count" property="orderCount"/>
        <result column="discount_order_count" property="discountOrderCount"/>
        <result column="pay_power_id" property="payPowerId"/>
    </resultMap>
    <select id="paymentTypeCount" resultMap="AmountItemDTO">
        SELECT
            g.code,
            g.name,
            SUM( g.amount ) amount,
            SUM(g.order_count) order_count,
            g.pay_power_id
        FROM
        (
            (
            SELECT
            t.`payment_type` code,
            t.`payment_type_name` name,
            SUM( case when t.is_multiple_agg_pay = 1 then t.amount - IFNULL(t.refund_amount, 0) else t.amount end) AS amount,
            COUNT(DISTINCT o.guid) order_count,
            t.`pay_power_id`
            FROM `hst_transaction_record` t
            LEFT JOIN `hst_order` o ON o.`guid`=t.`order_guid`
            WHERE o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
            AND t.`is_delete`=0
            AND t.`state`=4
            AND o.`state` =4
            AND o.`is_delete`=0
            <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
                AND t.store_guid =  #{dto.storeGuid}
            </if>
            <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
                AND t.store_guid in
                <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                AND o.checkout_staff_guid in
                <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                    #{checkoutStaffGuid}
                </foreach>
            </if>
            GROUP BY t.`payment_type`,t.`payment_type_name`,t.`pay_power_id`
            )
        UNION ALL
            (
            SELECT
            t.`payment_type` code,
            t.`payment_type_name` name,
            SUM(t.`amount`) amount,
            COUNT(DISTINCT o.guid) order_count,
            t.`pay_power_id`
            FROM `hst_transaction_record` t
            LEFT JOIN `hst_order` o ON o.`guid`=t.`order_guid`
            LEFT JOIN `hst_multiple_transaction_record` mtr on mtr.transaction_record_guid = t.guid
            WHERE o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
            AND mtr.original_multiple_transaction_record_guid is null
            AND t.`is_delete`=0
            AND t.`state`=4
            AND o.`state` = 5
            AND o.recovery_id = '0'
            AND o.`is_delete`=0
            <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
                AND t.store_guid =  #{dto.storeGuid}
            </if>
            <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
                AND t.store_guid in
                <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
                AND o.recovery_staff_guid IN
                <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                    #{checkoutStaffGuid}
                </foreach>
            </if>
            GROUP BY t.`payment_type`,t.`payment_type_name`,t.`pay_power_id`
            )
        ) g
        GROUP BY
        g.code,g.pay_power_id, case when g.code = 10 then g.`name` else null end
    </select>

    <select id="discountTypeCount" resultMap="AmountItemDTO">
        SELECT
        t.`discount_type` code,
        t.`discount_name` name,
        SUM(t.`discount_fee`) amount,
        COUNT(DISTINCT o.guid) order_count
        FROM `hst_discount` t
        JOIN `hst_order` o ON o.`guid`=t.`order_guid`
        WHERE
        1=1
        <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
            AND o.store_guid =  #{dto.storeGuid}
        </if>
        <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
            AND o.store_guid in
            <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND o.`state` =4
        AND o.`is_delete`=0
        AND t.`is_delete`=0
        AND t.`discount_fee` != 0
        <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
            AND t.store_guid =  #{dto.storeGuid}
        </if>
        <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
            AND t.store_guid in
            <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
            AND o.checkout_staff_guid in
            <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
        GROUP BY t.`discount_type`
    </select>

    <select id="handover" resultMap="handoverPayDTO">
        SELECT
            confirm_user_guid,
            confirm_user_name,
            gmt_create,
            gmt_modified,
            business_in_coming
        FROM
            hsb_handover_record
        WHERE
            confirm_user_guid IS NOT NULL
        ORDER BY
            id DESC
    </select>

    <select id="calExcessAmount" resultType="java.math.BigDecimal">
        SELECT
            IFNULL( SUM(o.excess_amount), 0 )
        FROM `hst_order` o
        LEFT JOIN hst_transaction_record t ON o.refund_order_guid = t.order_guid
        AND t.state = 4
        AND t.trade_type = 6
        WHERE
            o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND o.`is_delete`=0
        AND o.`state`=4
        AND o.trade_mode = 0
        AND ( o.refund_order_guid IS NULL OR t.payment_type = 30 )
        <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
            AND o.store_guid =  #{dto.storeGuid}
        </if>
        <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
            AND o.store_guid in
            <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
            AND o.checkout_staff_guid in
            <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
    </select>
    <select id="getCheckoutStaffs" resultType="java.lang.String">
        SELECT DISTINCT checkout_staff_name
        FROM  hst_order t
        WHERE
        t.`checkout_time` BETWEEN  CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND t.upper_state <![CDATA[ <> ]]> '2'
        AND t.`is_delete` = 0
        AND t.`state`=4
        AND (t.recovery_type=1 or t.recovery_type=3)
        <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
            AND t.store_guid =  #{dto.storeGuid}
        </if>
        <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
            AND t.store_guid in
            <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
            AND t.checkout_staff_guid in
            <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
    </select>

    <select id="getRefundCheckoutStaffs" resultType="java.lang.String">
        SELECT DISTINCT checkout_staff_name
        FROM  hst_order t
        JOIN `hst_order_refund_record` r ON t.guid = r.order_guid
        WHERE
        t.`checkout_time` BETWEEN  CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND t.upper_state not in (2, 4)
        AND t.`is_delete` = 0
        AND t.`state`=4
        AND (t.recovery_type=1 or t.recovery_type=3)
        <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
            AND t.store_guid =  #{dto.storeGuid}
        </if>
        <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
            AND t.checkout_staff_guid in
            <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
        UNION
        SELECT DISTINCT checkout_staff_name
        FROM  hst_order t
        WHERE
        t.`checkout_time` BETWEEN  CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND t.upper_state  not in (2, 4)
        AND t.`is_delete` = 0
        AND t.`state`=7
        <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
            AND t.store_guid =  #{dto.storeGuid}
        </if>
        <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
            AND t.checkout_staff_guid in
            <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
    </select>
    <select id="getMtGrouponEstimatedAmount" resultType="java.math.BigDecimal">
        SELECT
        IFNULL( SUM(g.due), 0 )
        FROM `hst_groupon_trade_detail` g
        LEFT JOIN hst_order o ON o.guid = g.order_guid
        AND g.cancel = 0
        AND g.is_delete = 0
        WHERE
        o.`checkout_time` BETWEEN CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND o.`is_delete`=0
        AND o.refund_order_guid IS NULL
        AND o.`state`=4
        <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
            AND o.store_guid in
            <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
            AND o.checkout_staff_guid in
            <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
    </select>

    <resultMap id="overviewStatisticResult" type="com.holderzone.saas.store.dto.order.response.daily.OverviewStatisticRespDTO">
        <result column="guid" property="guid"/>
        <result column="main_order_guid" property="mainOrderGuid"/>
        <result column="guest_count" property="guestCount"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="checkout_time" property="checkoutTime"/>
        <result column="associated_flag" property="associatedFlag"/>
        <result column="associated_table_guids" property="associatedTableGuids"/>
    </resultMap>

    <select id="overviewStatistic" parameterType="com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO"
            resultMap="overviewStatisticResult">
        SELECT
        t.guid,
        t.main_order_guid,
        t.guest_count,
        t.gmt_create,
        t.checkout_time,
        oe.associated_flag,
        oe.associated_table_guids
        FROM
        hst_order t
        LEFT JOIN hst_order_extends oe ON t.guid = oe.guid
        WHERE
            t.`checkout_time` BETWEEN  CONCAT(#{dto.beginTime},' 00:00:00') AND CONCAT(#{dto.endTime},' 23:59:59')
        AND t.upper_state != 4
        AND t.`is_delete` = 0
        AND t.`state`=4
        AND t.trade_mode = 0
        AND (t.recovery_type=1 or t.recovery_type=3)
        <if test="dto.storeGuid != null and dto.storeGuid.trim() != ''" >
            AND t.store_guid =  #{dto.storeGuid}
        </if>
        <if test="dto.storeGuids != null and dto.storeGuids.size() > 0">
            AND t.store_guid in
            <foreach collection="dto.storeGuids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.checkoutStaffGuids != null and dto.checkoutStaffGuids.size() > 0">
            AND t.checkout_staff_guid in
            <foreach collection="dto.checkoutStaffGuids" item="checkoutStaffGuid" open="(" separator="," close=")">
                #{checkoutStaffGuid}
            </foreach>
        </if>
    </select>

</mapper>
