<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.report.mapper.ReserveMapper">

    <sql id="fieldSql">
        r.guid,
        max(r.number) as "number",
        max(r.state) as "state",
        COALESCE( max(r.phone),'-') as "phone",
        max(r.gender) as "gender",
        max(r.name) as "name",
        case max(r.order_type)
        when 1 then 31
        else max(r.device_type) end as "device_type",
        max(r.payment_type) as "payment_type",
        max(r.payment_type_name) as "payment_type_name",
        max(r.reserve_amount) as "reserve_amount",
        max(r.refund_amount) as "refund_amount",
        max(r.remark) as "remark",
        max(r.reserve_start_time) as "reserve_start_time",
        max(r.area::json ->> 'name') as "areaName",
        max(r.gmt_create) as "gmt_create",
        max(r.is_delay::int) as "is_delay",
        max(o.name) as "store_name",
        string_agg(concat(rt.area_name, '-' ,rt.table_name), ', ') as "tables"
    </sql>

    <select id="list" resultType="com.holderzone.saas.store.dto.report.resp.ReserveRespDTO">
        SELECT
            <include refid="fieldSql" />
        FROM
            "hsr_reserve_${query.enterpriseGuid}_db"."hss_reserve_record" r
        LEFT JOIN "hsr_reserve_${query.enterpriseGuid}_db"."hss_r_reserve_record_table" rt on rt.reserve_record_guid = r.guid and rt.is_deleted = '0'
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = r.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        <include refid="whereSQL" />
        GROUP BY r.guid
        ORDER BY to_char(max(r.reserve_start_time), 'YYYY-MM-DD') DESC, to_char(max(r.reserve_start_time), 'HH24:MI:SS') ASC, r.guid desc
    </select>

    <select id="pageInfo" resultType="com.holderzone.saas.store.dto.report.resp.ReserveRespDTO">
        SELECT
            <include refid="fieldSql" />
        FROM
            "hsr_reserve_${query.enterpriseGuid}_db"."hss_reserve_record" r
        LEFT JOIN "hsr_reserve_${query.enterpriseGuid}_db"."hss_r_reserve_record_table" rt on rt.reserve_record_guid = r.guid and rt.is_deleted = '0'
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = r.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        <include refid="whereSQL" />
        GROUP BY r.guid
        ORDER BY to_char(max(r.reserve_start_time), 'YYYY-MM-DD') DESC, to_char(max(r.reserve_start_time), 'HH24:MI:SS') ASC, r.guid desc
        limit ${query.pageSize} offset ${(query.currentPage - 1) * query.pageSize}
    </select>

    <select id="count" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            "hsr_reserve_${query.enterpriseGuid}_db"."hss_reserve_record" r
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_organization" o on o.guid = r.store_guid
        LEFT JOIN "hso_organization_${query.enterpriseGuid}_db"."hso_r_store_brand" ro on o.guid = ro.store_guid
        <include refid="whereSQL" />
    </select>


    <sql id="whereSQL">
        <where>
            r.is_deleted = '0' and r.state not in (39, 47)
            <if test="query.startTime != null">
                and r.reserve_start_time >= to_timestamp(concat(#{query.startTime}::text, ' 00:00:00'), 'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="query.endTime != null">
                <![CDATA[ and r.reserve_start_time <= to_timestamp(concat(#{query.endTime}::text, ' 23:59:59'), 'yyyy-mm-dd hh24:mi:ss') ]]>
            </if>
            <if test="query.brandGuid != null and query.brandGuid != ''">
                and ro.brand_guid = #{query.brandGuid}
            </if>
            <if test="query.storeGuids != null and query.storeGuids.size()>0">
                and r.store_guid in
                <foreach collection="query.storeGuids" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="query.reserveStates != null and query.reserveStates.size()>0">
                and r.state in
                <foreach collection="query.reserveStates" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="query.isDelay != null">
                <choose>
                    <when test="query.isDelay">
                        and r.is_delay = 't'
                    </when>
                    <otherwise>
                        and r.is_delay = 'f'
                    </otherwise>
                </choose>
            </if>
            <if test="query.deviceType != null">
                <choose>
                    <when test="query.deviceType == 31">
                        and r.device_type = 3
                        and r.order_type = 1
                    </when>
                    <when test="query.deviceType == 3">
                        and r.device_type = 3
                        and r.order_type = 0
                    </when>
                    <when test="query.deviceType == 4">
                        and r.device_type in ( 4, 6, 7 )
                    </when>
                    <otherwise>
                        and r.device_type = #{query.deviceType}
                    </otherwise>
                </choose>
            </if>
        </where>
    </sql>

</mapper>
