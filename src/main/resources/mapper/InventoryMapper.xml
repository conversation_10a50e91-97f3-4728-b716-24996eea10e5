<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.InventoryMapper">

    <select id="queryInventoryOverview" resultType="com.holderzone.erp.entity.domain.InventoryDO">
        SELECT * from hse_inventory
        <where>
            inventory_date BETWEEN #{dto.startDateTime} and #{dto.endDateTime}
            <if test="dto.type != 0">
                and invoice_type = #{dto.type}
            </if>
            <if test="dto.orderStatus != 0">
                and status = #{dto.orderStatus}
            </if>
            <if test="dto.operatorOrOrderNo !=null and  dto.operatorOrOrderNo !='' ">
                and (operator like CONCAT('%',#{dto.operatorOrOrderNo},'%' ) or invoice_no like CONCAT('%',#{dto.operatorOrOrderNo},'%' ))
            </if>
        </where>
        order by inventory_date DESC
    </select>

</mapper>

