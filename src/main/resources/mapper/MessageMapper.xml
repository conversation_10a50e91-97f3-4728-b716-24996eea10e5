<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.message.mapper.MessageMapper">

    <sql id="columns">
      message_guid,subject,content,message_type,detail_message_type,state,platform,message_level,store_guid,store_name,push_time
    </sql>
    <sql id="values">
      #{messageGuid},#{subject},#{content},#{messageType},#{detailMessageType},#{state},#{platform},#{messageLevel},#{storeGuid},#{storeName},#{pushTime}
    </sql>
    <insert id="insert" parameterType="com.holderzone.holder.saas.store.message.domain.MessageDO">
        insert into hsm_message
        (
        <include refid="columns"/>
        )
        values
        (
        <include refid="values"/>
        )
    </insert>
    <insert id="insertAll" parameterType="list">
        insert into hsm_message
        (
        <include refid="columns"/>
        )
        values
        <foreach collection="list" separator="," item="item">
            (#{item.messageGuid},#{item.subject},#{item.content},#{item.messageType},#{item.detailMessageType},#{item.state},#{item.platform},#{item.messageLevel},#{item.storeGuid},#{item.storeName},#{item.pushTime})
        </foreach>
    </insert>


    <sql id="whereQuery">
        <where>
            detail_message_type != 32 and detail_message_type != 33 and detail_message_type != 104
            <if test="storeGuid!=null and storeGuid!=''">
                and store_guid = #{storeGuid}
            </if>
            <if test="messageType!=null">
                and message_type = #{messageType}
            </if>
            <if test="state!=null">
                and state = #{state}
            </if>
            <if test="beginTime != null">
                and gmt_create &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                and gmt_modified &lt;= #{endTime}
            </if>
        </where>
    </sql>
    <select id="queryCount" parameterType="com.holderzone.saas.store.dto.message.MsgQuery"
            resultType="java.lang.Integer">
        select
        count(*)
        from hsm_message
        <include refid="whereQuery"/>
    </select>

    <resultMap id="messageMap" type="com.holderzone.holder.saas.store.message.domain.MessageDO">
        <result column="message_guid" property="messageGuid"/>
        <result column="subject" property="subject"/>
        <result column="content" property="content"/>
        <result column="message_type" property="messageType"/>
        <result column="detail_message_type" property="detailMessageType"/>
        <result column="state" property="state"/>
        <result column="platform" property="platform"/>
        <result column="message_level" property="messageLevel"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="store_name" property="storeName"/>
        <result column="push_time" property="pushTime"/>
        <result column="gmt_create" property="createTime"/>
    </resultMap>
    <resultMap id="messageInfoMap" type="com.holderzone.saas.store.dto.message.MsgInfoRespDTO">
        <result column="message_guid" property="messageGuid"/>
        <result column="subject" property="subject"/>
        <result column="gmt_create" property="createTime"/>
        <result column="state" property="state"/>
        <result column="content" property="content"/>
    </resultMap>
    <select id="queryAll" parameterType="com.holderzone.saas.store.dto.message.MsgQuery"
            resultMap="messageInfoMap">
        select
        message_guid,subject,gmt_create,state,content
        from hsm_message
        <include refid="whereQuery"/>
        order by gmt_create desc
        limit #{index},#{pageSize}
    </select>

    <select id="queryDetail" parameterType="string"
            resultMap="messageMap">
        select
        <include refid="columns"/>,gmt_create
        from hsm_message
        where message_guid = #{messageGuid}
    </select>

    <update id="markMsgHasRead">
      update hsm_message set state =1 where message_guid = #{messageGuid}
    </update>

    <update id="readAll">
        UPDATE hsm_message
            SET state = 1
        WHERE
            state = 0
            AND store_guid = #{storeGuid}
            <if test="messageType!=null">
                and message_type = #{messageType}
            </if>
    </update>

    <select id="countUnReadMsg" resultType="java.lang.Integer">
      select count(*) as num from hsm_message
      where
         detail_message_type != 32 and detail_message_type != 33
         and detail_message_type != 104
        and store_guid = #{storeGuid} and state = 0 and message_type in (1,2)
        and gmt_create &gt;= #{begin} and gmt_modified &lt;= #{end}
    </select>

    <select id="countUnReadPrintErrorMsg" parameterType="string" resultType="java.lang.Integer">
         select count(*) from hsm_message where store_guid = #{storeGuid} and state = 0 and message_type = 4
    </select>
</mapper>