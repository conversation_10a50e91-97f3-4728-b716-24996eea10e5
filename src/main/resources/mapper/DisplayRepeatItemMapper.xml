<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.kds.mapper.DisplayRepeatItemMapper">

    <insert id="saveOrUpdateConfig">
        insert into hsk_display_repeat_item (`guid`, `brand_guid`, `allow_repeat_flag`, `all_store_flag`)
        values ( #{guid}, #{brandGuid}, #{allowRepeatFlag}, #{allStoreFlag} )
        on duplicate key update
        allow_repeat_flag = values(allow_repeat_flag),
        all_store_flag = values(all_store_flag),
        gmt_modified = now()
    </insert>

</mapper>
