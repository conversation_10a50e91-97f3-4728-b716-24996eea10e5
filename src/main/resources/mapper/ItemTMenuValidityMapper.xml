<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.item.mapper.ItemTMenuValidityMapper">

    <resultMap id = "ItmeTMenuValidityDO" type="com.holderzone.saas.store.item.entity.domain.ItemTMenuValidityDO">
        <result column="guid" property="guid"/>
        <result column="item_menu_guid" property="itemMenuGuid"/>
        <result column="weeks" property="weeks"/>
        <result column="times" property="times"/>
        <result column="times_quantum" property="timesQuantum"/>
        <result column="weeks_quantum" property="weeksQuantum"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>


    <resultMap id="ItemTemplateExecuteTimeQuery" type="com.holderzone.saas.store.item.entity.query.ItemTemplateExecuteTimeQuery">
        <result column="guid" property="guid"/>
        <result column="now" property="now"/>
        <result column="weeks" property="weeks"/>
        <result column="times" property="times"/>
        <result column="periodic_mode" property="periodicMode"/>
    </resultMap>

    <select id="getMenuValiditys" resultMap="ItmeTMenuValidityDO">
            SELECT
                v.guid,
                v.item_menu_guid,
                v.`weeks`,
                v.times,
                v.times_quantum,
                v.weeks_quantum,
                v.is_delete
             FROM hsi_item_t_menu m
                LEFT JOIN hsi_item_template t
                ON t.guid = m.template_guid
                LEFT JOIN hsi_itme_t_menu_validity v
                ON m.guid = v.item_menu_guid
             <where>
                   t.guid = #{guid}
                 <if test="periodicMode != '' and periodicMode != null" >
                     AND  v.periodic_mode = #{periodicMode}
                 </if>
                 <if test="menuGuid != '' and menuGuid != null " >
                     and m.guid != #{menuGuid}
                 </if>
                 and v.is_delete = 0
             </where>
    </select>

    <select id="getTimeAndTypeForSyn" resultMap="ItemTemplateExecuteTimeQuery">
        SELECT
        to_days(now()) - to_days(effective_start_time) as  difference,
        NOW() now,
        v.`weeks`,
        v.times,
        v.periodic_mode
        FROM
        hsi_item_template t
        INNER JOIN hsi_item_t_menu m ON t.guid = m.template_guid
        INNER JOIN hsi_itme_t_menu_validity v ON m.guid = v.item_menu_guid
        WHERE
        t.store_guid = #{dto.data}
        AND t.is_it_activated = 1
        AND (
        t.effective_start_time &lt;= CONCAT(
        DATE_SUB(curdate(), INTERVAL - 2 DAY),
        ' 00:00:00'
        )
        AND t.effective_end_time &gt;= CONCAT(
        DATE_SUB(curdate(), INTERVAL - 2 DAY),
        ' 23:59:59'
        )
        )
        OR (
        t.effective_start_time &lt;= CONCAT(
        DATE_SUB(curdate(), INTERVAL - 1 DAY),
        ' 00:00:00'
        )
        AND t.effective_end_time &gt;= CONCAT(
        DATE_SUB(curdate(), INTERVAL - 1 DAY),
        ' 23:59:59'
        )
        )
        OR (
        t.effective_start_time &lt;= NOW()
        AND t.effective_end_time &gt;= NOW()
        )
        AND t.is_delete =0 AND m.is_delete = 0 AND v.is_delete = 0
        ORDER BY
	    t.effective_start_time
    </select>

    <select id="getNowMenuExecuteTimes"  resultMap="ItemTemplateExecuteTimeQuery">
        SELECT
        m.guid,
        to_days(now()) - to_days(effective_start_time) AS difference,
        NOW() now,
        v.`weeks`,
        v.times,
        v.periodic_mode
        FROM
        hsi_item_template t
        INNER JOIN hsi_item_t_menu m ON t.guid = m.template_guid
        INNER JOIN hsi_itme_t_menu_validity v ON m.guid = v.item_menu_guid
        WHERE
        t.store_guid = #{guid}
        AND t.is_it_activated = 1 AND (
        t.effective_start_time &lt; NOW()
        AND t.effective_end_time &gt;= NOW()
        )
        AND t.is_delete = 0
        AND m.is_delete = 0
        AND v.is_delete = 0
        ORDER BY
        t.effective_start_time
    </select>
</mapper>
