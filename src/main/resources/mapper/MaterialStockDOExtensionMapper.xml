<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.erp.dao.MaterialStockDOMapper">
    <resultMap id="QueryResultMap" type="com.holderzone.erp.entity.domain.MaterialDO">
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="enterprise_guid" jdbcType="VARCHAR" property="enterpriseGuid"/>
        <result column="warehouse_guid" jdbcType="VARCHAR" property="warehouseGuid"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="simple_name" jdbcType="VARCHAR" property="simpleName"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="unitName" property="unitName"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="specs" jdbcType="VARCHAR" property="specs"/>
        <result column="lowest_stock" jdbcType="DECIMAL" property="lowestStock"/>
        <result column="stock" jdbcType="DECIMAL" property="stock"/>
        <result column="inUnitPrice" jdbcType="DECIMAL" property="inUnitPrice"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="categoryName" property="categoryName"/>
    </resultMap>
    <update id="addStock">
      update hse_r_material_stock set count= count+#{count}
      where material_guid=#{materialGuid} and warehouse_guid=#{warehouseGuid}
    </update>
    <update id="reduceStock">
      update hse_r_material_stock set count= count-#{count}
      where material_guid=#{materialGuid} and warehouse_guid=#{warehouseGuid}
    </update>
    <update id="reduceStockBatch">
        <foreach collection="list" item="stockBO" separator=";">
            update hse_r_material_stock set stock= stock-#{stockBO.count}
            where material_guid=#{stockBO.materialGuid} and warehouse_guid=#{stockBO.warehouseGuid}
        </foreach>
    </update>
    <update id="addStockBatch">
        <foreach collection="list" item="stockBO" separator=";">
            update hse_r_material_stock set stock= stock+#{stockBO.count}
            where material_guid=#{stockBO.materialGuid} and warehouse_guid=#{stockBO.warehouseGuid}
        </foreach>
    </update>
    <insert id="insertStockBatch">
        <foreach collection="list" item="stockDO" separator=";">
            insert into hse_r_material_stock(guid, material_guid,warehouse_guid,unit,stock)
            values(#{stockDO.guid},#{stockDO.materialGuid},#{stockDO.warehouseGuid},#{stockDO.unit},#{stockDO.stock})
        </foreach>
    </insert>
    <select id="findStockPage" resultMap="QueryResultMap">
        SELECT
            m.guid,
            m.`name`,
            m.unit,
            m.`code`,
            m.simple_name,
            m.lowest_stock,
            m.category,
            m.type,
            u.`name` AS unitName,
            s.stock,
            ROUND(SUM( dd.total_amount ) / SUM( dd.main_unit_count ), 2) AS inUnitPrice
        FROM
            hse_r_material_stock s
            LEFT JOIN hse_material m ON m.guid = s.material_guid
            INNER JOIN hse_material_unit u ON u.guid = m.unit
            INNER JOIN hse_warehouse_in_out_document_detail dd ON dd.material_guid = s.material_guid
            INNER JOIN hse_warehouse_in_out_document d ON d.guid = dd.document_guid
        <where>
            d.in_out_type = 0
            and d.status = 1
            <if test="stockQueryDTO.category != null">
                and m.category=#{stockQueryDTO.category}
            </if>
            <if test="stockQueryDTO.warehouseGuid != null and stockQueryDTO.warehouseGuid != ''">
                and s.warehouse_guid=#{stockQueryDTO.warehouseGuid}
            </if>
            <if test="stockQueryDTO.searchConditions != null and stockQueryDTO.searchConditions != ''">
                and (m.name like "%"#{stockQueryDTO.searchConditions}"%"
                or m.code like "%"#{stockQueryDTO.searchConditions}"%")
            </if>
        </where>
        GROUP BY
	        m.guid
        ORDER BY
            if(( s.stock - m.lowest_stock &lt;= 0 and m.lowest_stock &lt;>0),0,1),
            m.gmt_create desc
    </select>
    <!--  <update id="updateByExampleMaterialRecord" parameterType="map">
          update hse_material
          <set>
              <if test="record.guid != null and record.guid !=''">
                  guid = #{record.guid,jdbcType=VARCHAR},
              </if>
              <if test="record.enterpriseGuid != null and record.enterpriseGuid !=''">
                  enterprise_guid = #{record.enterpriseGuid,jdbcType=VARCHAR},
              </if>
              <if test="record.storeGuid != null and record.storeGuid !=''">
                  store_guid = #{record.storeGuid,jdbcType=VARCHAR},
              </if>
              <if test="record.warehouseGuid != null and record.warehouseGuid !=''">
                  warehouse_guid = #{record.warehouseGuid,jdbcType=VARCHAR},
              </if>
              <if test="record.property != null and record.property !=''">
                  property = #{record.property,jdbcType=VARCHAR},
              </if>
              <if test="record.name != null and record.name !=''">
                  name = #{record.name,jdbcType=VARCHAR},
              </if>
              <if test="record.simpleName != null and record.simpleName !=''">
                  simple_name = #{record.simpleName,jdbcType=VARCHAR},
              </if>
              <if test="record.unit != null and record.unit !=''">
                  unit = #{record.unit,jdbcType=VARCHAR},
              </if>
              <if test="record.auxiliaryUnit != null and record.auxiliaryUnit !=''">
                  auxiliary_unit = #{record.auxiliaryUnit,jdbcType=VARCHAR},
              </if>
              <if test="record.category != null and record.category !=''">
                  category = #{record.category,jdbcType=VARCHAR},
              </if>
              <if test="record.type != null and record.type !=''">
                  type = #{record.type,jdbcType=VARCHAR},
              </if>
              <if test="record.code != null and record.code !=''">
                  code = #{record.code,jdbcType=VARCHAR},
              </if>
              <if test="record.barCode != null and record.barCode !=''">
                  bar_code = #{record.barCode,jdbcType=VARCHAR},
              </if>
              <if test="record.specs != null">
                  specs = #{record.specs,jdbcType=VARCHAR},
              </if>
              <if test="record.conversionMain != null">
                  conversion_main = #{record.conversionMain,jdbcType=DECIMAL},
              </if>
              <if test="record.conversionAuxiliary != null">
                  conversion_auxiliary = #{record.conversionAuxiliary,jdbcType=DECIMAL},
              </if>
              <if test="record.lowestStock != null">
                  lowest_stock = #{record.lowestStock,jdbcType=DECIMAL},
              </if>
              <if test="record.salesPrice != null">
                  sales_price = #{record.salesPrice,jdbcType=DECIMAL},
              </if>
              <if test="record.costPrice != null">
                  cost_price = #{record.costPrice,jdbcType=DECIMAL},
              </if>
              <if test="record.effectiveDate != null">
                  effective_date = #{record.effectiveDate,jdbcType=INTEGER},
              </if>
              <if test="record.storageMethod != null">
                  storage_method = #{record.storageMethod,jdbcType=VARCHAR},
              </if>
              <if test="record.image != null and record.image !=''">
                  image = #{record.image,jdbcType=VARCHAR},
              </if>
              <if test="record.enabled != null">
                  enabled = #{record.enabled,jdbcType=BIT},
              </if>
              <if test="record.deleted != null">
                  deleted = #{record.deleted,jdbcType=BIT},
              </if>
              <if test="record.gmtCreate != null">
                  gmt_create = #{record.gmtCreate},
              </if>
              <if test="record.gmtModified != null">
                  gmt_modified = #{record.gmtModified},
              </if>
              <if test="record.remark != null and record.remark !=''">
                  remark = #{record.remark,jdbcType=VARCHAR},
              </if>
          </set>
          <if test="_parameter != null">
              <include refid="Update_By_Example_Where_Clause"/>
          </if>
      </update>-->
</mapper>