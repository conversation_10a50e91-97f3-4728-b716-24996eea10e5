<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.business.mapper.DataSettingMapper">

    <select id="findDataSetting" resultType="com.holderzone.saas.store.business.entity.domain.DataSettingDO">
        SELECT
        guid,
        brand_guid,
        data_setting_type,
        data_setting_code
        FROM hsb_data_setting
        WHERE
        brand_guid = #{query.brandGuid}
        <if test="query.dataSettingTypeList != null and query.dataSettingTypeList.size() > 0">
            AND data_setting_type IN
            <foreach collection="query.dataSettingTypeList" item="dataSettingType" close=")" open="(" separator=",">
                #{dataSettingType}
            </foreach>
        </if>
        ORDER BY data_setting_type ASC
    </select>
</mapper>
