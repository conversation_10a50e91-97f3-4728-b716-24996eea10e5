<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.kds.mapper.BindItemMapper">

    <insert id="saveIgnoreBatch">
        INSERT IGNORE INTO hsk_bind_item
        (
            `guid`,
            `store_guid`,
            `group_guid`,
            `item_guid`,
            `sku_guid`
        )
        VALUES
        <foreach collection="list" item="data" index="index" open="" separator="," close="">
            (
            <trim suffixOverrides=",">
                #{data.guid},
                #{data.storeGuid},
                #{data.groupGuid},
                #{data.itemGuid},
                #{data.skuGuid},
            </trim>
            )
        </foreach>
    </insert>


</mapper>
