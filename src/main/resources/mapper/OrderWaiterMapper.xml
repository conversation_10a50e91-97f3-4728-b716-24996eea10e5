<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.trade.mapper.OrderWaiterMapper">

    <resultMap id="OrderWaiterPageTotalMap" type="com.holderzone.saas.store.trade.entity.read.OrderReadTotalDO">
        <id column="store_name"  property="storeName"/>
        <id column="waiter_guid" property="waiterGuid"/>
        <id column="waiter_name" property="waiterName"/>
        <id column="waiter_type" property="waiterType"/>
        <id column="waiter_no" property="waiterNo"/>
        <id column="orLessCount" property="orLessCount"/>
        <id column="betweenCount" property="betweenCount"/>
        <id column="theAboveCount" property="theAboveCount"/>
    </resultMap>

    <select id="pageQueryOrderWaiterTotal" resultMap="OrderWaiterPageTotalMap"
            parameterType="com.holderzone.saas.store.trade.entity.query.OrderWaiterQueryDetails">
        SELECT
        how.store_name ,
        how.waiter_guid ,
        how.waiter_name ,
        how.waiter_type ,
        how.waiter_no ,
        COUNT( CASE WHEN IFNULL( how.order_fee, 0 ) &gt;= 100 THEN how.waiter_guid END ) AS 'theAboveCount',
        COUNT( CASE WHEN IFNULL( how.order_fee, 0 ) &gt;= 30 AND IFNULL( how.order_fee, 0 ) &lt; 100 THEN how.waiter_guid END ) AS 'betweenCount',
        COUNT( CASE WHEN IFNULL( how.order_fee, 0 ) &lt; 30 THEN how.waiter_guid END ) AS 'orLessCount'
        FROM
        hst_order_waiter AS how LEFT JOIN hst_order AS ho ON how.order_guid = ho.guid
        <where>
            ho.state = 4
            AND ho.recovery_type in (1, 3)
            AND how.waiter_guid  != '-1'
            <if test="null!=orderWaiterQueryDetails.storeGuidList and orderWaiterQueryDetails.storeGuidList.size>0">
                and ho.store_guid in
                <foreach collection="orderWaiterQueryDetails.storeGuidList" item="storeGuid" open="(" separator="," close=")">
                    #{storeGuid}
                </foreach>
            </if>
            <if test="orderWaiterQueryDetails.timeType == 1 and null!=orderWaiterQueryDetails.startDateTime">
                and ho.gmt_create &gt;= #{orderWaiterQueryDetails.startDateTime}
            </if>
            <if test="orderWaiterQueryDetails.timeType == 1 and null!=orderWaiterQueryDetails.endDateTime">
                and ho.gmt_create &lt;= #{orderWaiterQueryDetails.endDateTime}
            </if>
            <if test="orderWaiterQueryDetails.timeType == 2 and null!=orderWaiterQueryDetails.startDateTime">
                and ho.checkout_time &gt;= #{orderWaiterQueryDetails.startDateTime}
            </if>
            <if test="orderWaiterQueryDetails.timeType == 2 and null!=orderWaiterQueryDetails.endDateTime">
                and ho.checkout_time &lt;= #{orderWaiterQueryDetails.endDateTime}
            </if>
            <if test="null!=orderWaiterQueryDetails.waiterType">
                and how.waiter_type = #{orderWaiterQueryDetails.waiterType}
            </if>
            GROUP BY
            how.store_name ,
            how.waiter_no ,
            how.waiter_guid,
            how.waiter_name,
            how.waiter_type
            ORDER BY
            how.waiter_no DESC
        </where>
    </select>
</mapper>
