#ÅäÖÃµÚÒ»Êý¾ÝÔ´
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.url=****************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=mysqlHolder
spring.datasource.driverClassName=com.mysql.jdbc.Driver


# Á¬½Ó³Ø×î´óÊ¹ÓÃÁ¬½ÓÊý
spring.datasource.maxActive=100
# ³õÊ¼»¯Á¬½Ó´óÐ¡
spring.datasource.initialSize=20
#¿ªÆô·Ç¹«Æ½Ëø
spring.datasource.useUnfairLock=true
# Á¬½Ó³Ø×îÐ¡¿ÕÏÐ
spring.datasource.minIdle=1
# ÅäÖÃ¼ä¸ô¶à¾Ã²Å½øÐÐÒ»´Î¼ì²â£¬¼ì²âÐèÒª¹Ø±ÕµÄ¿ÕÏÐÁ¬½Ó£¬µ¥Î»ÊÇºÁÃë
spring.datasource.timeBetweenEvictionRunsMillis=60000
# ÅäÖÃÒ»¸öÁ¬½ÓÔÚ³ØÖÐ×îÐ¡Éú´æµÄÊ±¼ä£¬µ¥Î»ÊÇºÁÃë
spring.datasource.minEvictableIdleTimeMillis=300000
#¼ì²âÁ¬½ÓÊÇ·ñÓÐÐ§sql£¬ÒªÇóÊÇÒ»¸ö²éÑ¯Óï¾ä£¬Èç¹ûÎªnull£¬testOnBorrow¡¢testOnReturn¡¢testWhileIdle¶¼ÎÞÐ§
spring.datasource.validationQuery=SELECT 1 FROM DUAL
#±£Ö¤°²È«£¬ÉêÇëÁ¬½Ó¶øÇÒ¿ÕÏÐÊ±¼ä´óÓÚtimeBetweenEvictionRunsMillisÊ±ºò£¬validationQuery¼ì²â
spring.datasource.testWhileIdle=true
#ÉêÇëÁ¬½ÓÊ±ºòvalidationQuery¼ì²âÁ¬½ÓÊÇ·ñÓÐÐ§£¬»á½µµÍÐÔÄÜ
spring.datasource.testOnBorrow=false
#¹é»¹Á¬½ÓÊ±ºòvalidationQuery¼ì²â£¬»á½µµÍÐÔÄÜ
spring.datasource.testOnReturn=false
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
#ÊÇ·ñPScache»º´æ5.5°æ±¾ÒÔÉÏÖ§³Ö
spring.datasource.poolPreparedStatements=true
#µ±Öµ´óÓÚ0Ä¬ÈÏ¿ªÆôPScache
spring.datasource.maxOpenPreparedStatements=20
#´ò¿ªPScacheºó£¬Ã¿¸öÁ¬½ÓÉÏµÄcache´óÐ¡
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20