##éç½®ç¬¬ä¸æ°æ®æº
##spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
##spring.datasource.url=**********************************************************************************************************************************************************************************************************************************************************
##spring.datasource.username=root
##spring.datasource.password=mysqlHolder
#
##spring.datasource.driverClassName=com.mysql.jdbc.Driver
#
#
## éç½®çæ§ç»è®¡æ¦æªfilters
##spring.datasource.filters=stat
## è¿æ¥æ± æå¤§ä½¿ç¨è¿æ¥æ°
##spring.datasource.maxActive=100
## åå§åè¿æ¥å¤§å°
##spring.datasource.initialSize=20
## è·åè¿æ¥æå¤§ç­å¾æ¶é´ï¼å¼å¯äºå¬å¹³éï¼å¹¶åæçä¸é
##spring.datasource.maxWait=60000
##å¼å¯éå¬å¹³é
##spring.datasource.useUnfairLock=true
## è¿æ¥æ± æå°ç©ºé²
##spring.datasource.minIdle=1
## éç½®é´éå¤ä¹æè¿è¡ä¸æ¬¡æ£æµï¼æ£æµéè¦å³é­çç©ºé²è¿æ¥ï¼åä½æ¯æ¯«ç§
##spring.datasource.timeBetweenEvictionRunsMillis=60000
## éç½®ä¸ä¸ªè¿æ¥å¨æ± ä¸­æå°çå­çæ¶é´ï¼åä½æ¯æ¯«ç§
#spring.datasource.minEvictableIdleTimeMillis=300000
##æ£æµè¿æ¥æ¯å¦ææsqlï¼è¦æ±æ¯ä¸ä¸ªæ¥è¯¢è¯­å¥ï¼å¦æä¸ºnullï¼testOnBorrowãtestOnReturnãtestWhileIdleé½æ æ
#spring.datasource.validationQuery=SELECT 1 FROM DUAL
##ä¿è¯å®å¨ï¼ç³è¯·è¿æ¥èä¸ç©ºé²æ¶é´å¤§äºtimeBetweenEvictionRunsMillisæ¶åï¼validationQueryæ£æµ
#spring.datasource.testWhileIdle=true
##ç³è¯·è¿æ¥æ¶åvalidationQueryæ£æµè¿æ¥æ¯å¦ææï¼ä¼éä½æ§è½
#spring.datasource.testOnBorrow=false
##å½è¿è¿æ¥æ¶åvalidationQueryæ£æµï¼ä¼éä½æ§è½
#spring.datasource.testOnReturn=false
#spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
##æ¯å¦PScacheç¼å­5.5çæ¬ä»¥ä¸æ¯æ
#spring.datasource.poolPreparedStatements=true
##å½å¼å¤§äº0é»è®¤å¼å¯PScache
#spring.datasource.maxOpenPreparedStatements=20
##æå¼PScacheåï¼æ¯ä¸ªè¿æ¥ä¸çcacheå¤§å°
#spring.datasource.maxPoolPreparedStatementPerConnectionSize=20