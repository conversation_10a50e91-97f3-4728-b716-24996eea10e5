spring:
  profiles:
    active: dev
  jackson:
    serialization:
      indent_output: true
    default-property-inclusion: non_null

server:
  port: 8900
  undertow:
    # Maximum size in bytes of the HTTP post content
    max-http-post-size: 0
    # 设置 IO 线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个 CPU 核 心一个线程,数量和 CPU 内核数目一样即可
    io-threads: 4
    # 阻塞任务线程池, 当执行类似 servlet 请求阻塞操作, undertow 会从这个线程池中取得 线程,它的值设置取决于系统的负载  io-threads*8
    worker-threads: 32
    # 以下的配置会影响 buffer,这些 buffer 会用于服务器连接的 IO 操作,有点类似 netty 的 池化内存管理
    # 每块 buffer 的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 每个区分配的 buffer 数量 , 所以 pool 的大小是 buffer-size * buffers-per-region
    #buffers-per-region: 1024 # 这个参数不需要写了
    # 是否分配的直接内存
    direct-buffers: true

# 动态数据源
dynamic:
  redis:
    enabled: true
  server:
    # 如果本服务需要对表分库，那么需要配置本服务的所有表名,以逗号隔开
    # sharding-tables: person
    server-code: holder_saas_store_organization

# mybatis-plus
mybatis-plus:
  mapper-locations: classpath*:/mapper/**Mapper.xml
  typeAliasesPackage: com.holderzone.saas.store.organization.domain
  configuration:
    map-underscore-to-camel-case: true
    aggressive-lazy-loading: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    cache-enabled: false
    call-setters-on-nulls: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    refresh: false
    db-config:
      db-type: mysql
      id-type: auto
      field-strategy: not_empty
      logic-delete-value: 1
      logic-not-delete-value: 0

# 这个配置在starter中配置了matchIfMissing=true，所以可省略
# 禁用、启用framework-sdk-starter中的分页插件
mybatis-page:
  enable: true

#自定义配置：用来判断是否使用动态数据源
self:
  open-dynamic-datasource: true

logging:
  level:
    com.holderzone.framework.slfj.starter: info

# 开放Actuator监控所有端点及开启CORS
management:
  health:
    redis:
      enabled: false
    db:
      enabled: false
  endpoint:
    shutdown:
      enabled: true
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"
      cors:
        allowed-origins: "*"
        allowed-methods: "*"
info:
  app:
    name: ${spring.application.name}