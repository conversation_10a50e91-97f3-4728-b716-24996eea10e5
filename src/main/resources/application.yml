spring:
  profiles:
    active: dev
  jackson:
    serialization:
      indent_output: true
    default-property-inclusion: non_null
order:
  lock:
    releasesTime: 30  #订单锁的自动释放时间
settlementrules:
  list:
    - sequenceNumber: 1
      offer_name: 团购验券-套餐券
      offer_desc: 默认使用团购券，会导致商品券、会员价、单品折扣、限时特价、代金券，会员折扣、第N份优惠、满减、满折、整单折扣无效，可通过第三方活动进行优惠共享设罟
    - sequenceNumber: 2
      offer_name: 赠送
      offer_desc: ——
    - sequenceNumber: 3
      offer_name: 商品券（营销活动）
      offer_desc: 如果使用商品券，会导致单品折扣、限时特价无效；与代金券互斥
    - sequenceNumber: 4
      offer_name: 会员价
      offer_desc: 与限时特价活动生效最低价，两种价格取优惠后更低的价格生效
    - sequenceNumber: 5
      offer_name: 单品改价（一体机手动操作）
      offer_desc: 如果使用单品改价（一体机手动操作），会导致商品会员价、限时特价价无效
    - sequenceNumber: 6
      offer_name: 单品折扣（一体机手动操作）
      offer_desc: 如果使用单品折扣（一体机手动操作），会导致商品会员价、限时特价价无效
    - sequenceNumber: 7
      offer_name: 限时特价（营销活动）
      offer_desc: 如果使用限时特价活动，会导致商品会员价无效；可通过活动进行优惠共享设罟，与商品会员价/会员折扣生效最低价，两种价格取优惠后更低的价格生效
    - sequenceNumber: 8
      offer_name: 团购验券-非套餐券（代金券/买单）
      offer_desc: 默认使用团购券，会导致商品券、会员价、单品折扣、限时特价、代金券，会员折扣，满减、满折、整单折扣无效，可通过第三方活动进行优惠共享设置
    - sequenceNumber: 9
      offer_name: 代金券（营销活动）
      offer_desc: 与商品券互斥
    - sequenceNumber: 10
      offer_name: 会员折扣
      offer_desc: 与限时特价活动生效最低价，两种价格取优惠后更低的价格生效
    - sequenceNumber: 11
      offer_name: 第N份优惠（营销活动）
      offer_desc: 可通过活动进行优惠共享设置
    - sequenceNumber: 12
      offer_name: 满减
      offer_desc: ——
    - sequenceNumber: 13
      offer_name: 满折
      offer_desc: ——
    - sequenceNumber: 14
      offer_name: 整单折扣
      offer_desc: ——
    - sequenceNumber: 15
      offer_name: 积分抵扣
      offer_desc: ——
    - sequenceNumber: 16
      offer_name: 系统省零
      offer_desc: ——
    - sequenceNumber: 17
      offer_name: 整单让价
      offer_desc: ——
    - sequenceNumber: 18
      offer_name: 随行红包优惠（营销活动）
      offer_desc: ——
    - sequenceNumber: 19
      offer_name: 美团一键买单
      offer_desc: 默认与所有优惠共享

# 开放Actuator监控所有端点及开启CORS
management:
  health:
    redis:
      enabled: false
    db:
      enabled: false
  endpoint:
    shutdown:
      enabled: true
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"
      cors:
        allowed-origins: "*"
        allowed-methods: "*"
info:
  app:
    name: ${spring.application.name}


