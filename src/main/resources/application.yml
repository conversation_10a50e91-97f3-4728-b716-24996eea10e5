spring:
  profiles:
    active: dev
  jackson:
    serialization:
      indent_output: true
    default-property-inclusion: non_null
# 动态数据源
#dynamic:
#  intercept:
#    include-url: /**
#    exclude-url: /group
#    #添加需要切换 redis 数据源的接口路径
#    redis:
#      include-url: /**
#  redis:
#    enabled: true
#  server:
#    #如果本服务需要对表分库，那么需要配置本服务的所有表名,以逗号隔开
#    #sharding-tables: person
#    #必须制定当前服务的名称
#    server-code: holder_saas_store_report

# 开放Actuator监控所有端点及开启CORS
management:
  health:
    redis:
      enabled: false
    db:
      enabled: false
  endpoint:
    shutdown:
      enabled: true
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"
      cors:
        allowed-origins: "*"
        allowed-methods: "*"
info:
  app:
    name: ${spring.application.name}