spring:
  profiles:
    active: dev
  jackson:
    serialization:
      indent_output: true
    default-property-inclusion: non_null

server:
  port: 8926
  undertow:
    max-http-post-size: 0
    io-threads: 4
    worker-threads: 32
    buffer-size: 1024
    direct-buffers: true

# 动态数据源
dynamic:
  intercept:
    datasource:
      include-url: /**
      exclude-url:
    redis:
      include-url: /**
  redis:
    enabled: true
  server:
    server-code: holder_saas_store_staff

logging:
  level:
    com.holderzone.holder.saas.store.mdm: debug

mybatis-plus:
  mapper-locations: classpath*:/mapper/**Mapper.xml
  typeAliasesPackage: com.holderzone.holder.saas.store.mdm.entity.domain
  configuration:
    map-underscore-to-camel-case: true
    aggressive-lazy-loading: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    cache-enabled: false
    call-setters-on-nulls: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    refresh: false
    db-config:
      db-type: mysql
      id-type: auto
      field-strategy: not_empty
      logic-delete-value: 1
      logic-not-delete-value: 0

mybatis-page:
  enable: false

# 开放Actuator监控所有端点及开启CORS
management:
  health:
    redis:
      enabled: false
    db:
      enabled: false
  endpoint:
    shutdown:
      enabled: true
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"
      cors:
        allowed-origins: "*"
        allowed-methods: "*"
info:
  app:
    name: ${spring.application.name}