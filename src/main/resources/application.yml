spring:
  profiles:
    active: dev
  application:
    name: holder-saas-store-table
  jackson:
    serialization:
      indent_output: true
    default-property-inclusion: non_null

server:
  port: 8901

mybatis-page:
  enable: false

delay-release-table-lock:
  wxTime: 15
  time: 180
  level: 1
  size: 100000


# 开放Actuator监控所有端点及开启CORS
management:
  health:
    redis:
      enabled: false
    db:
      enabled: false
  endpoint:
    shutdown:
      enabled: true
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"
      cors:
        allowed-origins: "*"
        allowed-methods: "*"
info:
  app:
    name: ${spring.application.name}