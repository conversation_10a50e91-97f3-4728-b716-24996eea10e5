spring:
  jackson:
    serialization:
      indent_output: true
    default-property-inclusion: non_null
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 80MB
      enabled: true
# 动态数据源
dynamic:
  intercept:
    #添加需要切换 redis 数据源的接口路径
    redis:
      include-url: /**
  redis:
    enabled: true

feign:
  enableGlobalException: false

# 开放Actuator监控所有端点及开启CORS
management:
  health:
    redis:
      enabled: false
    db:
      enabled: false
  endpoint:
    shutdown:
      enabled: true
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"
      cors:
        allowed-origins: "*"
        allowed-methods: "*"
info:
  app:
    name: ${spring.application.name}
sensitive:
  enable: true # 默认为true
  depth: false # 是否开启深度脱敏，默认false
