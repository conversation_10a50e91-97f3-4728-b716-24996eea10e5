spring:
  profiles:
    active: local
  jackson:
    default-property-inclusion: non_null
    serialization:
      indent_output: true
order:
  lock:
    releasesTime: 30  #订单锁的自动释放时间
settlementrules:
  list:
    - sequenceNumber: 1
      offer_name: 商品赠送
      offer_desc: ——
    - sequenceNumber: 2
      offer_name: 单品改价（一体机手动操作）
      offer_desc: ——
    - sequenceNumber: 3
      offer_name: 单品折扣（一体机手动操作）
      offer_desc: ——
    - sequenceNumber: 4
      offer_name: 整单折扣
      offer_desc: ——
    - sequenceNumber: 5
      offer_name: 积分抵扣
      offer_desc: ——
    - sequenceNumber: 6
      offer_name: 系统省零
      offer_desc: ——
    - sequenceNumber: 7
      offer_name: 整单让价
      offer_desc: ——

# 开放Actuator监控所有端点及开启CORS
management:
  health:
    redis:
      enabled: false
    db:
      enabled: false
  endpoint:
    shutdown:
      enabled: true
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"
      cors:
        allowed-origins: "*"
        allowed-methods: "*"
info:
  app:
    name: ${spring.application.name}

