spring:
  profiles:
    active: dev
  jackson:
    serialization:
      indent_output: true
    default-property-inclusion: non_null
server:
  port: 8913

# 启用framework-sdk-starter中的分页插件
mybatis-page:
  enable: false

# 开放Actuator监控所有端点及开启CORS
management:
  health:
    redis:
      enabled: false
    db:
      enabled: false
  endpoint:
    shutdown:
      enabled: true
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"
      cors:
        allowed-origins: "*"
        allowed-methods: "*"
info:
  app:
    name: ${spring.application.name}