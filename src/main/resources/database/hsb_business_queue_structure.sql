-- MySQL dump 10.16  Distrib 10.1.29-MariaDB, for debian-linux-gnu (x86_64)
--
-- Host: ***************    Database: hsb_business_queue_db
-- ------------------------------------------------------
-- Server version	5.7.20-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `hsb_queued_record`
--

DROP TABLE IF EXISTS `hsb_queued_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsb_queued_record` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `store_guid` varchar(45) DEFAULT NULL COMMENT '门店guid',
  `queued_type_guid` varchar(45) DEFAULT NULL COMMENT '排队类型guid',
  `queued_record_guid` varchar(45) DEFAULT NULL COMMENT '排队记录guid',
  `create_user_guid` varchar(45) DEFAULT NULL COMMENT '创建人guid',
  `finish_user_guid` varchar(45) DEFAULT NULL COMMENT '确认人guid',
  `seed` smallint(5) unsigned DEFAULT NULL COMMENT '排队记录种子值',
  `code` varchar(10) DEFAULT NULL COMMENT '排队记录编码',
  `customer_count` smallint(5) unsigned DEFAULT NULL COMMENT '客人数量',
  `customer_tel` varchar(45) DEFAULT NULL COMMENT '客人电话',
  `status` tinyint(1) unsigned DEFAULT '0' COMMENT '排队状态：0=排号中，1=完成，2=过号',
  `call_times` smallint(5) unsigned DEFAULT '0' COMMENT '叫号次数',
  `waiting_time` bigint(64) unsigned DEFAULT '0' COMMENT '已等待时间',
  `waiting_index` smallint(5) unsigned DEFAULT '0' COMMENT '当前排号位置',
  `qr_code_url` varchar(45) DEFAULT NULL COMMENT '二维码链接',
  `is_deleted` tinyint(1) unsigned DEFAULT '0' COMMENT '是否已删除：0=未删除，1=已删除',
  `business_day` datetime DEFAULT NULL COMMENT '营业日日期',
  `gmt_finished` datetime DEFAULT NULL COMMENT '完成时间',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_queued_record_guid` (`queued_record_guid`),
  KEY `idx_queued_type_guid` (`queued_type_guid`),
  KEY `idx_store_guid` (`store_guid`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_gmt_modified` (`gmt_modified`),
  KEY `idx_gmt_create` (`gmt_create`),
  KEY `idx_gmt_finished` (`gmt_finished`),
  KEY `idx_business_day` (`business_day`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='营业中心-排队记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsb_queued_type`
--

DROP TABLE IF EXISTS `hsb_queued_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsb_queued_type` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `store_guid` varchar(45) DEFAULT NULL COMMENT '门店guid',
  `queued_type_guid` varchar(45) DEFAULT NULL COMMENT '排队类型guid',
  `name` varchar(45) DEFAULT NULL COMMENT '排队类型名称',
  `code` varchar(10) DEFAULT NULL COMMENT '排队类型编码',
  `left_interval` tinyint(2) DEFAULT NULL COMMENT '人数左区间',
  `right_interval` tinyint(2) DEFAULT NULL COMMENT '人数右区间',
  `is_enable` tinyint(1) DEFAULT '1' COMMENT '是否已启用：0=未启用，1=已启用',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否已删除：0=未删除，1=已删除',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_queued_type_guid` (`queued_type_guid`),
  KEY `idx_store_guid` (`store_guid`),
  KEY `idx_enable` (`is_enable`),
  KEY `idx_deleted` (`is_deleted`),
  KEY `idx_gmt_create` (`gmt_create`),
  KEY `idx_gmt_modified` (`gmt_modified`),
  KEY `idx_right_interval` (`right_interval`),
  KEY `idx_left_interval` (`left_interval`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='营业中心-排队';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2018-10-09 10:46:28
