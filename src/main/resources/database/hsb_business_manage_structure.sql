-- MySQL dump 10.16  Distrib 10.1.29-MariaDB, for debian-linux-gnu (x86_64)
--
-- Host: ***************    Database: hsb_business_manage_db
-- ------------------------------------------------------
-- Server version	5.7.20-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `hsb_addtional_fee`
--

DROP TABLE IF EXISTS `hsb_addtional_fee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsb_addtional_fee` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `store_guid` varchar(45) DEFAULT NULL COMMENT '门店guid',
  `additional_fee_guid` varchar(45) DEFAULT NULL COMMENT '附加费guid',
  `name` varchar(45) DEFAULT NULL COMMENT '附加费名',
  `amount` decimal(10,0) unsigned DEFAULT NULL COMMENT '附加费金额',
  `sort` mediumint(5) unsigned DEFAULT NULL COMMENT '附加费排序',
  `remark` varchar(45) DEFAULT NULL COMMENT '附加费备注',
  `is_enable` tinyint(1) unsigned DEFAULT '1' COMMENT '是否已启用：0=未启用，1=已启用',
  `is_deleted` tinyint(1) unsigned DEFAULT '0' COMMENT '是否已删除：0=未删除，1=已删除',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_additional_fee_guid` (`additional_fee_guid`),
  KEY `idx_store_guid` (`store_guid`),
  KEY `idx_sort` (`sort`),
  KEY `idx_is_enable` (`is_enable`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='营业中心-附加费';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsb_cashbox_record`
--

DROP TABLE IF EXISTS `hsb_cashbox_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsb_cashbox_record` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `store_guid` varchar(45) NOT NULL COMMENT '门店guid',
  `terminal_id` varchar(45) NOT NULL COMMENT '设备Id',
  `handover_record_guid` varchar(45) DEFAULT NULL COMMENT '交接班记录guid',
  `cashbox_record_guid` varchar(45) DEFAULT NULL COMMENT '钱箱记录guid',
  `user_guid` varchar(45) DEFAULT NULL COMMENT '用户guid',
  `user_name` varchar(45) DEFAULT NULL COMMENT '用户名字',
  `operation_type` tinyint(1) unsigned DEFAULT NULL COMMENT '操作类型：0=存入，1=取出',
  `source_type` tinyint(1) unsigned DEFAULT NULL COMMENT '来源类型：0=开钱箱存取款，1=收银存取款',
  `money` decimal(10,0) unsigned DEFAULT NULL COMMENT '现金',
  `remark` varchar(45) DEFAULT NULL COMMENT '备注',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_cashbox_record_guid` (`cashbox_record_guid`),
  KEY `idx_handover_record_guid` (`handover_record_guid`),
  KEY `idx_user_guid` (`user_guid`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_store_guid` (`store_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='营业中心-钱箱记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsb_handover_pay_detail`
--

DROP TABLE IF EXISTS `hsb_handover_pay_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsb_handover_pay_detail` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `handover_record_guid` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '交接班记录guid',
  `terminal_id` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '设备id',
  `pay_type` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '支付方式',
  `pay_type_name` varchar(45) COLLATE utf8_bin NOT NULL COMMENT '支付方式名称',
  `pay_money` decimal(10,2) DEFAULT '0.00' COMMENT '支付金额',
  PRIMARY KEY (`id`),
  KEY `idx_handover_record_guid` (`handover_record_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='营业中心-交接班-支付方式详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsb_handover_record`
--

DROP TABLE IF EXISTS `hsb_handover_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsb_handover_record` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `handover_record_guid` varchar(45) NOT NULL COMMENT '交接班记录guid',
  `store_guid` varchar(45) NOT NULL COMMENT '门店guid',
  `store_name` varchar(45) DEFAULT NULL COMMENT '门店名',
  `terminal_id` varchar(45) NOT NULL COMMENT '设备id',
  `create_user_guid` varchar(45) DEFAULT NULL COMMENT '接班人/开班人guid',
  `create_user_name` varchar(45) DEFAULT NULL COMMENT '接班人/开班人名字',
  `payment_money` decimal(10,2) DEFAULT NULL COMMENT '当班销售收入（销售收入）',
  `member_charge_money` decimal(10,2) DEFAULT NULL COMMENT '当班储值收入（充值收入）',
  `business_in_coming` decimal(10,2) DEFAULT NULL COMMENT '营业额（销售收入+充值收入-会员卡消费金额）',
  `confirm_user_guid` varchar(45) DEFAULT NULL COMMENT '交班人guid',
  `confirm_user_name` varchar(45) DEFAULT NULL COMMENT '交班人名字',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '状态：0=未交班，1=已交班',
  `payment_count` int(10) unsigned DEFAULT NULL COMMENT '收银笔数',
  `checked_count` int(10) unsigned DEFAULT NULL COMMENT '已结账订单数',
  `charged_count` int(10) unsigned DEFAULT NULL COMMENT '充值订单数',
  `collect_money` decimal(10,2) DEFAULT NULL COMMENT '现金收银',
  `flow_money` decimal(10,2) DEFAULT NULL COMMENT '现金周转',
  `sys_balance` decimal(10,2) DEFAULT NULL COMMENT '系统结算',
  `difference_money` decimal(10,2) DEFAULT NULL COMMENT '差额',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（开班时间）',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间（交班时间）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `handover_record_guid_UNIQUE` (`handover_record_guid`),
  KEY `idx_terminal_id` (`terminal_id`),
  KEY `idx_store_guid` (`store_guid`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='营业中心-交接班管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsb_screen_picture`
--

DROP TABLE IF EXISTS `hsb_screen_picture`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsb_screen_picture` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `screen_picture_guid` varchar(64) DEFAULT NULL COMMENT '附屏图片guid',
  `oss_url` varchar(128) DEFAULT NULL COMMENT 'oss服务中图片存放地址',
  `change_mills` varchar(32) DEFAULT NULL COMMENT '图片切换速度',
  `store_guid` varchar(64) DEFAULT NULL COMMENT '门店名',
  `store_name` varchar(32) DEFAULT NULL COMMENT '门店名',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hsb_store_config`
--

DROP TABLE IF EXISTS `hsb_store_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hsb_store_config` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `store_guid` varchar(45) NOT NULL COMMENT '门店guid',
  `store_name` varchar(45) NOT NULL COMMENT '门店名称',
  `is_enable_pad_pwd` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '平板下单是否启用密码：0=不启用，1=启用',
  `is_enable_mark_dish` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否启用划菜：0=未启用，1=已启用',
  `is_enable_mem_price` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否启用会员价：0=未启用，1=已启用',
  `is_enable_handover` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `wechat_order_mode` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '微信点餐模式：0=堂食，1=快餐',
  `serial_number_mode` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '流水号模式：0=自增，1=随机',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gmt_business_start` time NOT NULL COMMENT '门店营业开始时间',
  `gmt_business_end` time NOT NULL COMMENT '门店营业结束时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_store_guid` (`store_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='营业中心-门店营业配置';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2018-10-09 10:39:21
