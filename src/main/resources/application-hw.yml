application-hw:
  yml:
prd:
  eureka:
    hostname: ***********
    port: 8141
  zipkin:
    hostname: ${spring.cloud.client.ip-address}
    port: 9411
  elasticsearch:
    hostname: ************
    port: 9200
server:
  port: 8997
  undertow:
    max-http-post-size: 0
    io-threads: 4
    worker-threads: 32
    buffer-size: 1024
    direct-buffers: true
elasticsearch:
  ip: ${prd.elasticsearch.hostname}
  port: ${prd.elasticsearch.port}
  pool: 1
  cluster:
    name: elasticsearch
spring:
  application:
    name: holder-saas-store-report
  zipkin:
    enabled: false
    base-url: http://${prd.zipkin.hostname}:${prd.zipkin.port}/
  sleuth:
    enabled: false
    sampler:
      probability: 1.0
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${prd.eureka.hostname}:${prd.eureka.port}/eureka/

feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000
