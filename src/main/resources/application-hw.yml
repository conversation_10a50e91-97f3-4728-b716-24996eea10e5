ref:
  eureka:
    hostname: ***********
    port: 8141
  zipkin:
    hostname: ${spring.cloud.client.ip-address}
    port: 9411
  rocketmq:
    hostname: *************
    port: 9876
spring:
  application:
    name: holder-saas-aggregation-phoneapp
  zipkin:
    enabled: false
    base-url: http://${ref.zipkin.hostname}:${ref.zipkin.port}/
  sleuth:
    enabled: false
    feign:
      enabled: true
  rocketmq:
    namesrv-addr: ${ref.rocketmq.hostname}:${ref.rocketmq.port}
    producer-group-name: payProduce
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
  aop:
    auto: true
  jackson:
    default-property-inclusion: non_null
#server:
#  port: 8162
#  tomcat:
#    accept-count: 200
#    max-connections: 5000
#    max-threads: 450

server:
  port: 8164
  undertow:
    max-http-post-size: 0
    # 设置 IO 线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个 CPU 核心一个线程,数量和 CPU 内核数目一样即可
    io-threads: 4
    # 阻塞任务线程池, 当执行类似 servlet 请求阻塞操作, undertow 会从这个线程池中取得线程,它的值设置取决于系统的负载 io-threads*8
    worker-threads: 32
    # 以下的配置会影响 buffer,这些 buffer 会用于服务器连接的 IO 操作,有点类似 netty 的池化内存管理
    # 每块 buffer 的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 每个区分配的 buffer 数量 , 所以 pool 的大小是 buffer-size * buffers-per-region
    # buffers-per-region: 1024 # 这个参数不需要写了
    # 是否分配的直接内存
    direct-buffers: true


# feign配置，使用appach client 替代默认urlConnection
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    hostname: ${ref.eureka.hostname}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${ref.eureka.hostname}:${ref.eureka.port}/eureka/
file:
  size: 3MB
  total:
    size: 5MB

# 登录时是否验证验证码
validate:
  verify-code-img: true
