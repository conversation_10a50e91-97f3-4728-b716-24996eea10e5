ref:
  eureka:
    ip: ***********
    port: 8141
  zipkin:
    ip: **************
    port: 9411
  rocketmq:
    ip: *************
    port: 9876
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${ref.eureka.ip}:${ref.eureka.port}/eureka/
spring:
  zipkin:
    enabled: false
    base-url: http://${ref.zipkin.ip}:${ref.zipkin.port}/
    service:
      name: holder-saas-store-kds
    sender:
      type: web
  rocketmq:
    namesrv-addr: ${ref.rocketmq.ip}:${ref.rocketmq.port}
    producer-group-name: StoreKdsProducer
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120