UNKNOWN_EXCEPTION=Unknown Exception
PHONE_REPEAT=THE mobile phone number is repeated
MANUAL_DATABASE_ERROR=Manual database switch exception, enterpriseGuid is empty.
SYSTEM_ERROR=System exception.
AMAP_API_CALL_ERROR=AMap API call exception.
STICKER_IMAGE_DOWNLOAD_EMPTY=Table sticker image download failed, no corresponding background image, please create the table sticker again.
TABLE_STICKER_NULL=Table sticker parameters are empty.
REQUEST_CANNOT_RECOGNIZED=Current request type cannot be recognized.
QR_CODE_NO_PARAMETERS=No corresponding parameters for the current QR code, please download the QR code again.
ENTERPRISEGUID_ERROR=Incorrect enterpriseGuid.
TABLEGUIDS_NULL=tableGuids cannot be empty.
TABLEGUIDS_ERROR=Incorrect tableGuids.
TRY_AGAIN_TEN_MINUTES=Please try again in 10 minutes.
ORDER_NOT_EXIST=Order data does not exist.
NOT_CHOSEN_DISH=You haven't selected any dishes, unable to proceed with payment. Please add dishes first.
ORDER_PAYED=The order has been paid. You will be redirected to the ordering homepage!
MEMBER_UNAVAILABLE=Member is not available.
ORDER_ERROR=Order data is invalid.
PAYED_RESERVE_TO_SETTLE=You have already paid a deposit, please proceed to the front desk for settlement! Please bring the deposit receipt if you have one~
COUNTER_CHECKOUT_CANT_SETTLE=Counter checkout orders cannot be settled. Please contact the merchant.
ORDER_NOT_PROCESSED=There are unprocessed orders from the merchant. Unable to proceed with payment.
ORDER_CHANGED=Order data has changed.
ABNORMAL_ORDER_NUMBER=Abnormal order number.
ORDER_NOT_FOUND=Order not found.
FAILED_ADD_ORDER_VERSION=Failed to add order version number.
ORDER_NUMBER_CANNOT_EMPTY=Order number cannot be empty.
STATUS_NOT_SUPPORT_ADJUSTMENT=Current status does not support adjustment!
THERE_ARE_ADJUSTED_ORDER_DETAILS=There are adjusted order details. Please select the items again!
TRANSACTION_MODE_CANNOT_EMPTY=Transaction mode cannot be empty.
ORDER_GUID_CANNOT_EMPTY=Order GUID cannot be empty.
CHANGE_STATUS_EMPTY=Change status is empty.
QUERY_TIME_CANNOT_EMPTY=Query time cannot be empty.
QUERY_TIME_RANGE_UP_TO_30_DAYS=Query time range can be up to 30 days.
NO_RECORDS_FOUND_FOR_THIS_ORDER=No records found for this order: <>
FAILED_TO_RETRIEVE_BUSINESS_DAY_INTERVAL=Failed to retrieve business day interval.
SERVICE_DOES_NOT_EXIST=Service does not exist.
STORE_GUID_CANNOT_BE_EMPTY=Store GUID cannot be empty!
ORDER_DETAILS_NOT_FOUND_FOR_ORDER_GUID=Order details not found for orderGuid: <>
TAKEOUT_ORDER_DOES_NOT_EXIST=Takeout order does not exist!
THIS_DATA_ALREADY_EXISTS=This data already exists.
FAILED_DISTRIBUTED_LOCK_TAKEOUT=Failed to acquire distributed lock for takeout order. Please try again later!
NO_DOUYIN_STORES=No Douyin stores found.
PLEASE_ENTER_VALID_DOUYIN_STOR=Please enter a valid Douyin store ID.
DOUYIN_STORE_EMPTY=Douyin store is empty.
STORE_ALREADY_BOUND_DOUYIN=This store is already bound to a Douyin store.
VALIDATION_PARAMETERS_ARE_EMPTY=Validation parameters are empty.
VALIDATION_FAILED_STORE_NOT_BOUND=Validation failed: Store is not bound.
INVALID_DOUYIN_VOUCHER_CODE=Invalid Douyin group buying voucher code.
EXCEPTION_REQUESTING_DOUYIN=Exception while requesting Douyin service.
VOUCHER_CODE_EMPTY=Voucher code is empty.
VERIFICATION_STORE_CANNOT_EMPTY=Verification store cannot be empty.
DOUYIN_STORE_NOT_BOUND=Douyin store is not bound.
INVALID_VOUCHER_VERIFICATION=Invalid voucher verification information.
EMPTY_RESPONSE_DOUYIN_VOUCHER_VERIFICATION_CANCEL=Empty response from Douyin for voucher verification cancellation.
FAILED_BIND_DOUYIN_STORE=Failed to bind Douyin store.
BINDING_STORE_SERVICE_NOT_EXIST=Binding store service does not exist.
FAILED_CANCEL_MEITUAN_VOUCHER=Failed to cancel Meituan voucher.
FAILED_INITIALIZE_AREA_AND_TABLE=Failed to initialize store area and table information!
TABLE_INFORMATION_NULL=Table information is null.
ORDER_GUID_NOT_FOUND=Order GUID not found.
OLD_TABLE_NULL=Old table information is null.
ORDER_QUERY_EMPTY=Order query is empty.
FAILED_TO_GENERATE_GUID=Failed to generate GUID.
FAILED_GENERATE_BATCH_GUID=Failed to generate batch GUID.
CANNOT_LOCK_MULTIPLE_TABLES_TIME=Cannot lock multiple tables at the same time.
THIS_TABLE_EXISTS=This table already exists in the current area.
FAILED_ADD_TABLE=Failed to add table!
FAILED_UPDATE_TABLE=Failed to update table!
QUANTITY_CANNOT_LESS_ZERO=Table quantity cannot be less than 0.
MAXIMUM_QUANTITY_99=Maximum quantity is 99.
MAXIMUM_QUANTITY_999=Maximum quantity is 999.
MAXIMUM_QUANTITY_9999=Maximum quantity is 9999.
STORE_GUID_LIST_CANNOT_EMPTY=storeGuidList cannot be empty.
FAILED_GENERATE_GUID=Failed to generate GUID: <>
ACCOUNT_NOT_SERVICE_ACTIVATED=This account does not have the service activated. Please contact customer support.
ACCOUNT_NOT_LOGIN_PERMISSIONS=This account does not have login permissions.
ENTERPRISE_PRODUCT_NO_RESOURCES=Current enterprise product has no resources.
NO_ENABLED_MENU_UNDER_THIS_TERMINAL=No enabled menu under this terminal.
ROLE_ALREADY_EXISTS=The role name already exists. Please modify it.
FAILED_GENERATE_ROLE_GUID=Failed to generate role GUID using BatchIdGenerator.
CANNOT_DELETE_ROLE=Cannot delete role with associated accounts.
AUTHORIZED_PERSON_GUID_CANNOT_EMPTY=Authorized person GUID cannot be empty.
AUTHORIZATION_CODE_CANNOT_EMPTY=Authorization code cannot be empty.
PERMISSION_TABLE_EMPTY=User permission name table query is empty.
DUPLICATE_AUTHORIZATION_CODE_ENTERPRISE=Duplicate authorization code within the same enterprise.
SOURCE_CODE_CANNOT_EMPTY=sourceCode cannot be empty.
ADMINISTRATORS_CANNOT_MODIFIED=Data permissions for administrators cannot be modified.
ACCOUNT_EXISTS=Account already exists, failed to create.
DUPLICATE_PHONE_NUMBER_MODIFY=Duplicate phone number, failed to modify.
DUPLICATE_ACCOUNT_MODIFY=Duplicate account, failed to modify.
ASSOCIATED_CANNOT_DELETED=This account has associated data and cannot be deleted.
USE_CANNOT_DELETED=This account is currently in use by a store and cannot be deleted.
INCORRECT_ORIGINAL_PASSWORD=Incorrect original password.
USER_NOT_FOUND=User not found.
EMPLOYEE_NOT_FOUND=Employee not found.
DEVICE_NOT_REGISTERED=Device is not registered.
STORE_NOT_EXIST=Current store does not exist.
STORE_NOT_AVAILABLE=Store is not available, please contact the administrator.
PRODUCT_NOT_AUTHORIZED_EXPIRED=Product is not authorized or has expired.
INCONSISTENT_PRODUCT_SERVICE_DATA=Inconsistent product service data.
NOT_HAVE_TERMINAL_PERMISSIONS=User does not have current terminal permissions.
NOT_HAVE_STORE_PERMISSIONS=User does not have current store permissions.
DEVICE_HAS_BEEN_UNBOUND=Device has been unbound, please rebind to a store.
FAILED_CALL_ENTERPRISE=Failed to call enterprise query interface.
GIFT_DISH_REPORT=Gift dish report<>
EXPORT_GIFT_DISH_REPORT=Export gift dish report<>
ORDER_ITEM_TYPE=Order item type<>
ORDER_ITEM_CATEGORY=Order item category<>
RETURNED_ITEM_LIST=Returned item list<>
RETURNED_DISH_DETAIL_LIST=Returned dish detail list<>
EXPORT_RETURNED_DISH_DETAIL=Export returned dish detail<>
BUSY_WITH_BUSINESS=Busy with business, please try again later.
THREAD_DOES_HAVE_ENTERPRISE_GUID=Current thread does not have enterpriseGuid.
FAILED_RETRIEVE_STORE=Failed to retrieve store information.
DATA_QUERY_EXCEPTION=Data query exception.
PARAMETERS_CANNOT_EMPTY=Request parameters cannot be empty.
UNKNOWN_ENTERPRISE_ELSE=Unknown enterprise.
THREAD_NOT_ENTERPRISE=Current thread does not have enterprise ID.
PARAMETER_ERROR=Parameter error.
UNKNOWN_ENTERPRISES=Unknown enterprise.
UNKNOWN_STORES=Unknown store.
UNKNOWN_ENTERPRISE_INFORMATION=Unknown enterprise information.
UNKNOWN_STORE_INFORMATION=Unknown store information.
UNKNOWN_ENTERPRISE=Unknown enterprise information.
DIVISION_ZERO_EXCEPTION=Division by zero exception.
MD_5_NOT_AVAILABLE=No MD5 algorithm available!
PLEASE_SELECT_STORE=Please select a store.
QUEUE_NOT_EXIST_EXCEEDS=Queue does not exist or the number of people exceeds the limit.
CUSTOMERS_LESS_MINIMUM=Number of customers is less than the minimum table configuration.
CUSTOMERS_EXCEEDS_MAXIMUM=Number of customers exceeds the maximum table configuration.
SYSTEM_BUSY=System busy, please try again later.
RECORD_DOES_NOT_EXIST=Record does not exist, cannot call number.
FAILED_TO_CALL_NUMBER=Failed to call number, please try again later.
NUMBER_SKIPPED_CUSTOMER_SEATED=Number has been skipped or the customer has been seated, please switch navigation and refresh to view.
NOT_ALLOW_RECOVERY=This store does not allow recovery to the queue.
PLEASE_SWITCH_NAVIGATION=Recovered to the queue, please switch navigation and refresh to view.
DIRECT_NOT_ALLOWED=Direct dining operation not allowed.
NO_BRAND=No brand information.
QUEUE_CODE_DUPLICATED=Queue code is duplicated: <>.
QUEUE_NOT_EXIST=Queue does not exist.
QUEUE_DELETED_REFRESH=This queue has been deleted, please refresh the page.
NOT_ALLOW_MANUAL_CLEARING_QUEUE=This store does not allow manual clearing of the queue.
DUPLICATE_TABLE_GUID=Duplicate table GUID.
COUNT_DEVICE_PARAMETER_ERROR=countDevice parameter error.
UNSUPPORTED_PAPER=Unsupported paper width type.
NO_BACKUP_PRINTER=No backup printer information available.
MAXIMUM_OF_THREE_TEMPLATES=Can create a maximum of 3 templates.
QUANTITIES_UNABLE_TO_PRINT=All dish quantities are 0, unable to print, <>.
CURRENTLY_ONLY_SUPPORTS=Currently only supports front desk, kitchen, and label.
DISH_LIST_IS_EMPTY=Dish list is empty, unable to print.
PRINTED_ITEM_EMPTY=Printed item information is empty.
NO_CONNECTED_PRINTING=No connected printing devices found.
NO_HOST_POS_MACHINES=No host POS machines found under this store.
PRINTER_NOT_EXIST=Printer does not exist, please select a printer again!
PRINTER_IP_ALREADY_EXISTS=A printer with the same name or IP already exists under this store.
ALREADY_HAS_PRINTER=This device already has a local printer.
M_LOCALE_1=The statistics of regular meals, fast food, and take-out cannot be all empty.
M_LOCALE_2=Unsupported bill type: <>
M_LOCALE_3=This template does not support customization.
M_LOCALE_4=Current custom element is not supported.
M_LOCALE_5=The dish list is empty, cannot print the kitchen order.
M_LOCALE_6=recordGuid must not be empty.
M_LOCALE_7=Brand name is duplicated.
M_LOCALE_8=BatchIdGenerator failed to generate brand guid.
M_LOCALE_9=There are existing stores under this brand, please clean them before deleting.
M_LOCALE_10=There are existing products under this brand, please clean them before deleting.
M_LOCALE_11=Brand guid must not be empty during update.
M_LOCALE_12=Sales mode cannot be empty.
M_LOCALE_13=Organization name already exists, please modify.
M_LOCALE_14=Organization hierarchy maximum depth cannot exceed 5.
M_LOCALE_15=Organization name is duplicated.
M_LOCALE_16=The maximum depth of sub-organizations under the enterprise is 5. Please modify the sub-organizations and try again.
M_LOCALE_17=Accounts exist under the organization, cannot be deleted.
M_LOCALE_18=Sub-organizations exist under the organization, cannot be deleted.
M_LOCALE_19=An exception occurred while initializing organization/store data for enterprise <>.
M_LOCALE_20=BatchIdGenerator failed to generate device-store binding guid.
M_LOCALE_21=Sorting function is only supported by all-in-one machines.
M_LOCALE_22=No all-in-one machine device with minimum sorting value found.
M_LOCALE_23=Failed to unbind the device.
M_LOCALE_24=Only all-in-one machines can be set as hosts.
M_LOCALE_25=The provided device number does not match any device.
M_LOCALE_26=User-initiated ordering mode requires binding of table number.
M_LOCALE_27=Table information not found.
M_LOCALE_28=Table <> has already been bound, please choose another table.
M_LOCALE_29=Store guid cannot be empty.
M_LOCALE_30=Table guid cannot be empty.
M_LOCALE_31=Table guid list cannot be empty.
M_LOCALE_32=Device Guid cannot be empty.
M_LOCALE_33=Timeout, please retry.
M_LOCALE_34=Brand does not exist.
M_LOCALE_35=Store name already exists, please modify.
M_LOCALE_36=Deletion failed, store service is still active.
M_LOCALE_37=Provided store GUID is empty!
M_LOCALE_38=Store does not exist.
M_LOCALE_39=Unsupported type: <>
M_LOCALE_40=Unsupported kitchen product status: <>
M_LOCALE_41=Unsupported transaction mode: code=<>
M_LOCALE_42=Unsupported transaction mode: displayType=<>
M_LOCALE_43=Unsupported delay level: level=<>
M_LOCALE_44=Unsupported PointMode: <>
M_LOCALE_45=Unsupported Status: <>
M_LOCALE_46=Device does not exist.
M_LOCALE_47=One device can only be bound to one printer, please unbind the original printer first!
M_LOCALE_48=Device name [<>] is duplicated.
M_LOCALE_49=Rule type cannot be empty.
M_LOCALE_50=Rule guid cannot be empty.
M_LOCALE_51=Brand guid cannot be empty.
M_LOCALE_52=Store {<>} is already in use.
M_LOCALE_53=Product {<>} is already in use.
M_LOCALE_54=Display status cannot be empty.
M_LOCALE_55=Delay time for display cannot be empty.
M_LOCALE_56=Batch for display cannot be empty.
M_LOCALE_57=Effective status cannot be empty.
M_LOCALE_58=Product list cannot be empty.
M_LOCALE_59=Store list cannot be empty.
M_LOCALE_60=Some regions are already bound, please select again!
M_LOCALE_61=Number of dishes is 0.
M_LOCALE_62=Number of products exceeds the available quantity for return.
M_LOCALE_63=Some products are already bound.
M_LOCALE_64=You can create a maximum of 4 dining areas.
M_LOCALE_65=Dining area name [<>] is duplicated.
M_LOCALE_66=Dining area does not exist.
M_LOCALE_67=This store already has a printer with the same name.
M_LOCALE_68=Cannot add less than 1 coupon.
M_LOCALE_69=Header information cannot be empty!
M_LOCALE_70=Header information parsing error!
M_LOCALE_71=Operational entity cannot be empty!
M_LOCALE_72=Membership system is disabled. Please contact customer service if you have any questions!
M_LOCALE_73=Operational entity does not exist!
M_LOCALE_74=Timeout, please retry!
M_LOCALE_75=Coupon information does not exist.
M_LOCALE_76=To modify a coupon, the coupon Guid must be provided.
M_LOCALE_77=The coupon to be modified does not exist.
M_LOCALE_78=Coupon quantity cannot be less than the quantity sold!
M_LOCALE_79=Coupon list query requires status parameter.
M_LOCALE_80=The entered coupon code is incorrect, please check.
M_LOCALE_81=The coupon code has expired.
M_LOCALE_82=The coupon code has been used.
M_LOCALE_83=Insufficient coupon quantity.
M_LOCALE_84=Failed to query coupon information.
M_LOCALE_85=The quantity of coupons exceeds the maximum quantity allowed per order.
M_LOCALE_86=The amount required for coupon usage must be filled in.
M_LOCALE_87=The amount required for coupon usage must be greater than 0.
M_LOCALE_88=Please select the start and end time.
M_LOCALE_89=Please select the corresponding effective time and end time.
M_LOCALE_90=Please select the corresponding effective time and valid period in days.
M_LOCALE_91=The amount required for coupon usage must be greater than or equal to 0.
M_LOCALE_92=Current activity type does not exist.
M_LOCALE_93=Current page cannot be less than 1.
M_LOCALE_94=Incorrect parameter guid.
M_LOCALE_95=Deleted items cannot be edited.
M_LOCALE_96=Incorrect parameter guid.
M_LOCALE_97=Only in the "Not Expired" state can the "Stop Sending" action be used. Please refresh to check the status.
M_LOCALE_98=Only in the "Stop Sending" state can the "Continue Sending" action be used. Please refresh to check the status.
M_LOCALE_99=Only in the "Not Expired" or "Stop Sending" state can the "Invalid" action be used. Please refresh to check the status.
M_LOCALE_100=Deletion condition: Only items with status "Not Expired" or "Stop Sending" and already sent to users can be deleted.
M_LOCALE_101=Coupon quantity to add cannot exceed 999999.
M_LOCALE_102=This coupon is not an unlimited coupon and cannot be added.
M_LOCALE_103=This coupon has expired and cannot be added.
M_LOCALE_104=The quantity after adding coupons cannot exceed 999999.
M_LOCALE_105=Unregistered
M_LOCALE_106=Membership account has been disabled, please contact the administrator.
M_LOCALE_107=The membership's operational entity has changed, unable to reverse the account!
M_LOCALE_108=The membership QR code has expired.
M_LOCALE_109=Phone number already exists.
M_LOCALE_110=The membership has already opened the primary card.
M_LOCALE_111=Equity card specifications not configured.
M_LOCALE_112=Card rule does not allow this channel to open an equity card.
M_LOCALE_113=Reached the limit for card issuance.
M_LOCALE_114=Membership card does not exist.
M_LOCALE_115=Insufficient available points.
M_LOCALE_116=The canceled order does not exist.
M_LOCALE_117=Invalid phone number format.
M_LOCALE_118=Insufficient balance in the membership card.
M_LOCALE_119=This is not an equity card.
M_LOCALE_120=Payment password cannot be empty.
M_LOCALE_121=Incorrect payment password, please re-enter.
M_LOCALE_122=Membership does not exist.
M_LOCALE_123=Membership card is in abnormal status.
M_LOCALE_124=Membership card has been frozen.
M_LOCALE_125=This membership card has been disabled.
M_LOCALE_126=No tags available.
M_LOCALE_127=Please configure the main card information.
M_LOCALE_128=Please configure the main card level information.
M_LOCALE_129=Only membership card can be opened.
M_LOCALE_130=Multiple default membership levels exist, please check the card level configuration.
M_LOCALE_131=Recharge rules not configured.
M_LOCALE_132=Recharge function not enabled.
M_LOCALE_133=Card information not found.
M_LOCALE_134=Equity card information not found.
M_LOCALE_135=Equity card recharge function not enabled.
M_LOCALE_136=Equity card recharge amount error.
M_LOCALE_137=Membership card does not exist, please set up a membership card first.
M_LOCALE_138=Card already exists, please do not repeat card opening.
M_LOCALE_139=This card has already stopped distribution.
M_LOCALE_140=Membership card recharge failed.
M_LOCALE_141=Recharge limit exceeded.
M_LOCALE_142=Current user does not have a canteen card.
INSUFFICIENT_BALANCE=Insufficient balance, please recharge.
ERROR_CANTEEN_CARD_RECHARGE=Failed to recharge canteen card.
ERROR_STORE_OPEN_CARD=The current store does not have permission to open this card.
ERROR_VOLUME_CANT_USE_IN_BANQUET=Coupons cannot be used for banquet packages at the moment.
ERROR_VOLUME_IN_USE=Coupon is currently in use.
ERROR_VOLUME_ONLY_USE_ONE_TYPE=Only one type of coupon (cash coupon or product coupon) can be used.
ERROR_VOLUME_EXCEED_NUM_LIMIT=Exceeded the maximum number of coupon uses.
ERROR_VOLUME_CANT_USE_TOGETHER=The selected coupons cannot be used together.
ERROR_VOLUME_NOT_ACHIEVE_THRESHOLD=The coupon usage threshold has not been met.
ERROR_VOLUME_NOT_SUPPORT_STORE=This coupon code is not supported for use in this store.
ERROR_VOLUME_NOT_SAME_MEMBER=Coupons can only be used by the owner.
ERROR_VOLUME_MEMBER_DISABLE=The membership associated with this coupon is disabled.
ERROR_VOLUME_NOT_VALID=The coupon is not yet valid.
ERROR_VOLUME_USED=The coupon has been used.
ERROR_VOLUME_VOID=The coupon has been voided.
ERROR_VOLUME_OVERDUE=The coupon has expired.
ERROR_VOLUME_CANT_MEMBER_PRICE=This coupon cannot be used with member prices.
ERROR_VOLUME_EXCEED_PRODUCT_NUM=The selected coupons exceed the number of products eligible for deduction.
ERROR_VOLUME_NOT_SUPPORT_PRODUCT=No products eligible for coupon use.
ERROR_VOLUME_NOT_SUPPORT_GIFT=Gift items are not eligible for coupon use.
ERROR_VOLUME_CODE_NOT_EXSITS=Coupon code does not exist.
ERROR_VOLUME_NOT_BELONG_ONE_PERSON=Coupon code must be used by the same member.
ERROR_MEMBER_PASSWORD_EMPTY=Payment password cannot be empty.
ERROR_MEMBER_PASSWORD_INCORRECT=Incorrect payment password.
ERROR_INTEGRAL_DEDUCTION_DISABLE=Integral deduction is not enabled.
ERROR_NOT_CONDUCT_ACTIVITIES=No ongoing activities.
ERROR_NOT_STORE_CONDUCT_ACTIVITIES=No ongoing activities at the current store.
LOGIN_INPUT_PARAMETER_IS_EMPTY=Login input parameter is empty.
M_LOCALE_143=Missing information required to lock the table
M_LOCALE_144=The table is currently occupied
M_LOCALE_145=The table has already been locked
M_LOCALE_146=Updating member information cannot be empty
M_LOCALE_147=Mobile number or card number cannot be empty
M_LOCALE_148=Member is disabled
M_LOCALE_149=No such member, please re-enter
M_LOCALE_150=File format must be jpg/png/bmp/jpeg/gif!!!
M_LOCALE_151=Please upload a correct image
M_LOCALE_152=File size exceeds the limit
M_LOCALE_153=Document GUID cannot be empty
M_LOCALE_155=Unknown scanning type
M_LOCALE_156=Image URL cannot be empty
M_LOCALE_157=Error reading the image
M_LOCALE_158=File size cannot exceed
M_LOCALE_159=Parameter error
M_LOCALE_160=Error querying message count!!
M_LOCALE_161=Failed to push message to Android
M_LOCALE_162=Error querying message details!!
M_LOCALE_163=Error querying message info!!
M_LOCALE_164=Error querying store information
M_LOCALE_165=Failed!!
M_LOCALE_166=Failed to save the cash-up information
M_LOCALE_167=Failed to get the latest cash-up time for the store
M_LOCALE_168=Prompt MQ information, can perform table opening operation
M_LOCALE_169=Aggregate payment query interface exception!!
M_LOCALE_170=Error querying all payment methods
M_LOCALE_171=Error deleting payment method
M_LOCALE_172=Payment method sorting error
M_LOCALE_173=Error modifying payment method
M_LOCALE_174=Error adding payment method
M_LOCALE_175=Error adding system discount
M_LOCALE_176=Error modifying system discount
M_LOCALE_177=Error deleting system discount
M_LOCALE_178=Error querying all system discounts
M_LOCALE_179=Querying discount information by consumption record GUID
M_LOCALE_180=Error getting member consumption daily report
M_LOCALE_181=Error getting member recharge daily report
M_LOCALE_182=Equity card recharge error
M_LOCALE_183=Query failed
M_LOCALE_184=Failed to find member and card information in a certain store
M_LOCALE_185=Failed to query member basic information and card activation status based on member information GUID and store
M_LOCALE_186=Failed to send verification code for modifying password (forgot original password)
M_LOCALE_187=Successfully modified payment password
M_LOCALE_188=Password validation failed
M_LOCALE_189=Failed to modify member basic information
M_LOCALE_190=Member login (phone number and password) failed
M_LOCALE_191=Coupon confirmation consumption
M_LOCALE_192=Cancel coupon usage
M_LOCALE_193=Coupon verification
M_LOCALE_194=Exception in pre-order menu validation for one machine
M_LOCALE_195=Exception in product Android sync
M_LOCALE_196=Error getting printed product list
M_LOCALE_197=Exception in Android sync querying store SKU inventory list
M_LOCALE_198=Exception in configuring product inventory list for Android sync
M_LOCALE_199=Exception in Android sync getting the list of template execution times for the past three days
M_LOCALE_200=Payment failed!
M_LOCALE_201=Exception in clearing queue information
M_LOCALE_202=Failed to delete suspended order!
M_LOCALE_203=Order list failed!
M_LOCALE_204=Paging history records exception!!
M_LOCALE_205=Failed to get order details!
M_LOCALE_206=Exception in querying queue corresponding table
M_LOCALE_207=Exception in aggregate payment querying payment status
M_LOCALE_208=Exception in skipping a number
M_LOCALE_209=Exception in querying historical records
M_LOCALE_210=Exception in aggregate payment polling
M_LOCALE_211=Aggregate payment callback failed!
M_LOCALE_212=Exception in dining and table opening!!
M_LOCALE_213=Return failed!
M_LOCALE_214=Gift failed!
M_LOCALE_215=Recovery exception!!
M_LOCALE_216=Refund failed!
M_LOCALE_217=Failed to refund order!
M_LOCALE_218=Refund validation failed!
M_LOCALE_219=Exception in confirming dining!!
M_LOCALE_220=Exception calling transaction center
M_LOCALE_221=Exception in joining the queue
M_LOCALE_222=Failed to query the theme
M_LOCALE_223=Failed to add product!
M_LOCALE_224=Failed to print the checkout bill!
M_LOCALE_225=Failed to batch cancel gift!
M_LOCALE_226=Call number exception!!
M_LOCALE_227=Failed to calculate amount!
M_LOCALE_228=Aggregate payment failed!
M_LOCALE_229=Cash-up save error
M_LOCALE_230=Exception in querying queue information
M_LOCALE_231=Failed to suspend order!
M_LOCALE_232=Exception in querying queue table information
M_LOCALE_233=Exception in aggregate payment cancellation
M_LOCALE_234=Failed to update the whole order remarks!
M_LOCALE_235=Failed to get suspended order list!
M_LOCALE_236=Validation failed
M_LOCALE_237=Exception in querying table list
M_LOCALE_238=Exception in querying table area list
M_LOCALE_239=Exception in querying table basic data
M_LOCALE_240=Failed to add adjustment order
M_LOCALE_241=Exception in polling query for automatic acceptance of missed orders
M_LOCALE_242=Failed to reverse the checkout!
M_LOCALE_243=Failed to adjust order products!
M_LOCALE_244=Failed to get additional fee list!
M_LOCALE_245=Failed to cancel aggregate payment!
M_LOCALE_246=Failed to get order seating information!
M_LOCALE_247=Failed to update the number of diners!
M_LOCALE_248=Failed to invalidate order!
M_LOCALE_249=Failed to get order details from the table side!
M_LOCALE_250=Failed to print the pre-checkout bill!
M_LOCALE_251=Failed to print the dish list!
M_LOCALE_252=Failed to print the checkout bill!
M_LOCALE_253=Face payment compensation failed!
M_LOCALE_254=Face payment inventory validation failed!
M_LOCALE_255=Face payment inventory refund failed!
M_LOCALE_256=Failed to check the order lock!
M_LOCALE_257=Failed to increase version number!
M_LOCALE_258=Failed to get version number!
M_LOCALE_259=Order locking failed!
M_LOCALE_260=Order unlocking failed!
M_LOCALE_261=Offline order upload
M_LOCALE_262=Failed to fetch localized data!
M_LOCALE_263=Localized data save!
M_LOCALE_264=Failed to fetch localized data for a single record!
M_LOCALE_265=Failed to check if the service is available!
M_LOCALE_266=Failed to get member coupon list!
M_LOCALE_267=Failed to create adjustment order
M_LOCALE_268=Querying the earliest order time for the store!
M_LOCALE_269=Failed to query quantity!
M_LOCALE_270=Failed to take the order!
M_LOCALE_271=Failed to get the suspended order list!
M_LOCALE_272=Failed to suspend order!
M_LOCALE_273=Failed to store Tongchidao orders!
M_LOCALE_274=Fast food ordering failed!
M_LOCALE_275=Failed to cancel coupon validation!
M_LOCALE_276=Coupon validation failed!
M_LOCALE_277=Failed to query the validated coupon list!
M_LOCALE_278=Failed to modify price!
M_LOCALE_279=Failed to call back!
M_LOCALE_280=Failed to urge dishes!
M_LOCALE_281=Failed to swipe dishes!
M_LOCALE_282=Failed to cancel gift!
M_LOCALE_283=Failed to add dishes for regular meals!
M_LOCALE_284=Failed to clear table!
M_LOCALE_285=Payment link has expired
M_LOCALE_286=Current order status cannot be paid
M_LOCALE_287=Payment information is empty or already completed
M_LOCALE_288=Payment amount is 0, please settle at the front desk
M_LOCALE_289=Failed to open the table!
M_LOCALE_290=No matching report type
M_LOCALE_291=Image upload failed, please try again later
M_LOCALE_292=Product belongs to store cannot be empty
M_LOCALE_293=Enterprise GUID is empty
M_LOCALE_294=Enterprise operation mode not supported
COUPON_CODE_VOUCHER_INVALID=Validation failed: Vouchers must be validated during checkout
ACTIVITY_WRONG=Cannot select package activity without associated products
COUPON_UN_BIND_STORE=Please bind the group-buying platform store in the management background
COUPON_CODE_INVALID=Validation failed: Invalid coupon code
IS_THIRD_SHARE_TIPS=Validation failed: Cannot stack with other group-buying activities
WRONG_LOGIN_METHOD=Incorrect login method
WECHAT_AUTHORIZATION_CODE_IS_EMPTY=WeChat authorization code is empty
OPERATION_SUBJECT_GUID_IS_EMPTY=Operation subject GUID is empty
GUESTS_QUANTITY_IS_EMPTY=Dining guests quantity is empty
ORDER_GUID_IS_EMPTY=Order GUID is empty
M_LOCALE_295=Request parameters are empty
M_LOCALE_296=Failed to revoke the gifted coupon in batches for accurate push functionality
M_LOCALE_297=Failed to disable the member! Please try again!
M_LOCALE_298=Failed to delete the member! Please try again!
M_LOCALE_299=Failed to add the member! Please try again!
M_LOCALE_300=Imported goods are empty
M_LOCALE_301=Store unique identifier cannot be empty
M_LOCALE_302=The HEADER of the merchant's backend cannot have STOREGUID. Please contact the relevant backend developers to resolve it, thank you.
M_LOCALE_303=Imported organization structure cannot be empty
M_LOCALE_304=The inventory record GUID cannot be empty
M_LOCALE_305=No corresponding inventory document information found
M_LOCALE_306=No brand found!
M_LOCALE_307=Please use the import template and do not modify the header
M_LOCALE_308=Please create a store first
M_LOCALE_309=Imported 0 products
M_LOCALE_310=The number of parseable products is: 0, please check
M_LOCALE_311=The account's merchant is not bound to store information!
M_LOCALE_312=Not a valid Excel file
M_LOCALE_313=Failed to get the file
M_LOCALE_314=File path cannot be empty
M_LOCALE_315=The enterprise has not activated the store, please contact the after-sales personnel.
M_LOCALE_316=File size must not exceed <>
M_LOCALE_317=After selecting the store for use, you need to choose the corresponding store
M_LOCALE_318=The GUID of the rights system cannot be null
M_LOCALE_319=When the month is February, the maximum number of days is 28
M_LOCALE_320=The number of days should be between 1 and 30
M_LOCALE_321=Invalid year parameter for validity period
M_LOCALE_322=The month of the validity period should be between 1 and 12
M_LOCALE_323=The highest reward points must be greater than 0
M_LOCALE_324=The highest deductible points must be greater than 0
M_LOCALE_325=The GUID of the rights system cannot be null
M_LOCALE_326=After selecting the reminder method, the corresponding information must also be filled in
M_LOCALE_327=Supports exporting up to 20,000 pieces of data at most
M_LOCALE_328=The current store is not bound to a brand yet
M_LOCALE_329=Cloud call reservation phone number has been used by another merchant!
M_LOCALE_330=Merchant does not exist
M_LOCALE_331=Merchant does not allow reservations
M_LOCALE_332=No matching reservation time slots
M_LOCALE_333=No available seats
M_LOCALE_334=No areas found
M_LOCALE_335=Failed to get the verification code
M_LOCALE_336=Failed to query store options
M_LOCALE_337=Store query based on pagination failed
M_LOCALE_338=Store list query based on search criteria failed
M_LOCALE_339=Failed to batch synchronize stores
M_LOCALE_340=Failed to synchronize the store
M_LOCALE_341=Failed to save the store
M_LOCALE_342=Failed to modify the store
M_LOCALE_343=Failed to delete the store
M_LOCALE_344=Logout failed
M_LOCALE_345=Failed to query enterprise information
M_LOCALE_346=Failed to query menu list after login
M_LOCALE_347=Data transmission error, please try again later
M_LOCALE_348=Exception occurred while querying report - order details
M_LOCALE_349=Exception occurred while querying report - store summary information
M_LOCALE_350=Exception occurred while querying report - business overview - business data information
M_LOCALE_351=Exception occurred while querying report - business overview - historical trends information
M_LOCALE_352=Exception occurred while exporting report
M_LOCALE_353=Exception occurred while querying payment statistics report
M_LOCALE_354=Exception occurred while querying business turnover for the big screen
M_LOCALE_355=Exception occurred while querying product/category sales
M_LOCALE_356=Exception occurred while querying store sales rankings
M_LOCALE_357=Exception occurred while querying sales at a specific time
M_LOCALE_358=Exception occurred while querying business overview data for supermarkets
M_LOCALE_359=Exception occurred while querying historical trends for supermarkets
M_LOCALE_360=Exception occurred while querying payment statistics for supermarkets
M_LOCALE_361=Payment Transactions
M_LOCALE_362=Failed to export sales details
M_LOCALE_363=Failed to query sales details
M_LOCALE_364=Failed to query store payment methods
M_LOCALE_365=Failed to query order details data
M_LOCALE_366=Failed to get URL
M_LOCALE_367=Exception occurred while loading files
M_LOCALE_368=Exception occurred while querying files
M_LOCALE_369=Exception occurred while batch creating files
M_LOCALE_370=Exception occurred while creating a file
M_LOCALE_371=Failed to delete the file
M_LOCALE_372=Failed to move the file
M_LOCALE_373=Failed to rename the file
M_LOCALE_374=Failed to query coupon information for recharge options
M_LOCALE_375=Failed to query coupon information
M_LOCALE_376=Failed to query product coupon information
M_LOCALE_377=Failed to query coupon list information
M_LOCALE_378=Failed to save coupon data
M_LOCALE_379=Failed to save product coupon data
M_LOCALE_380=Failed to update coupon data
M_LOCALE_381=Failed to update product coupon data
M_LOCALE_382=Failed to operate the coupon
M_LOCALE_383=Failed to add the coupon to the card
M_LOCALE_384=Failed to query coupon details
M_LOCALE_385=Failed to query third-class valid card coupons
M_LOCALE_386=Failed to query coupon verification details
M_LOCALE_387=Failed to query coupon verification details total amount
M_LOCALE_388=Failed to query coupon issuance details
M_LOCALE_389=Failed to synchronize brand data
M_LOCALE_390=Failed to get brand list based on enterprise GUID
M_LOCALE_391=Failed to get brand list without parameters
M_LOCALE_392=Failed to delete the brand
M_LOCALE_393=Failed to save brand data
M_LOCALE_394=Failed to update brand data
M_LOCALE_395=Failed to synchronize enterprise data
M_LOCALE_396=Failed to synchronize enterprise list data
M_LOCALE_397=Failed to save enterprise data
M_LOCALE_398=Failed to update enterprise data
M_LOCALE_399=Failed to delete enterprise data
M_LOCALE_400=Query whether the login user is associated with the system based on the enterprise GUID
M_LOCALE_401=Failed to synchronize product data
M_LOCALE_402=Failed to query product data
M_LOCALE_403=Failed to delete product data
M_LOCALE_404=Failed to save product data
M_LOCALE_405=Failed to update product data
M_LOCALE_406=Store pagination query failed
M_LOCALE_407=Store list query failed
M_LOCALE_408=List query based on brand GUID failed
M_LOCALE_409=Failed to batch synchronize store list
M_LOCALE_410=Failed to synchronize store data
M_LOCALE_411=Failed to save store data
M_LOCALE_412=Failed to update store data
M_LOCALE_413=Failed to delete store data
M_LOCALE_414=Failed to get member details
M_LOCALE_415=Failed to update member channel
M_LOCALE_416=Failed to get member channel
M_LOCALE_417=Failed to query card levels under the system
M_LOCALE_418=Failed to add or update member level
M_LOCALE_419=Failed to delete member level
M_LOCALE_420=Failed to copy member benefits
M_LOCALE_421=Failed to find the benefits
M_LOCALE_422=Failed to get member level
M_LOCALE_423=Delete system
M_LOCALE_424=Failed to save/update upgrade benefits
M_LOCALE_425=Failed to save/update discount benefits
M_LOCALE_426=Failed to save/update birthday benefits
M_LOCALE_427=Failed to update the selected options for growth value
M_LOCALE_428=Failed to update member card information
M_LOCALE_429=Failed to get discount benefits
M_LOCALE_430=Failed to get upgrade benefits
M_LOCALE_431=Failed to get birthday benefits
M_LOCALE_432=Get list
M_LOCALE_433=Failed to delete coupon
M_LOCALE_434=Recharge settings failed
M_LOCALE_435=Recharge update failed
M_LOCALE_436=Failed to query selected store information
M_LOCALE_437=Growth value settings failed
M_LOCALE_438=Growth value update failed
M_LOCALE_439=Failed to create a brand
M_LOCALE_440=Failed to query store
M_LOCALE_441=Exception occurred while querying report payment methods
M_LOCALE_442=Failed to create a label
M_LOCALE_443=Exception occurred while querying various indicators of takeaway orders based on a given time period
M_LOCALE_444=Failed to update the label
M_LOCALE_445=Failed to delete the label
M_LOCALE_446=Failed to delete the table
M_LOCALE_447=Failed to query label list
M_LOCALE_448=Exception occurred while exporting the report!
M_LOCALE_449=Failed to copy role
M_LOCALE_450=Failed to query all terminals
M_LOCALE_451=Failed to query role permissions
M_LOCALE_452=Failed to save role permissions
M_LOCALE_453=Exception occurred while querying WeChat table tent/template
M_LOCALE_454=Exception occurred while creating WeChat table tent
M_LOCALE_455=Exception occurred while calling the query table tent interface
M_LOCALE_456=Exception occurred while calling the update table tent interface
M_LOCALE_457=Exception occurred while calling the query template library interface
M_LOCALE_458=Exception occurred while calling the query template category interface
M_LOCALE_459=Exception occurred while calling the table tent download interface
M_LOCALE_460=Exception occurred while calling the table tent creation interface
M_LOCALE_461=Exception occurred while calling the table tent deletion interface
M_LOCALE_462=Data source <> parsing error
M_LOCALE_463=Incorrect file format uploaded
M_LOCALE_464=Invalid unit at line <>
M_LOCALE_465=Invalid material name at line <>
M_LOCALE_466=Invalid type at line <>
M_LOCALE_467=Unit cannot be empty at line <>
M_LOCALE_468=Specification information cannot exceed 10 characters at line <>
M_LOCALE_469=Barcode information does not meet the specification at line <>
M_LOCALE_470=Duplicate material information at line <>: <>
M_LOCALE_471=Card points and growth values have been consolidated into the main card of the member
M_LOCALE_472=The current thread has no user information
M_LOCALE_473=Unable to get the current session store ID
M_LOCALE_474=Product GUID cannot be empty
M_LOCALE_475=The current store has not yet enabled reservation
M_LOCALE_476=Exception
M_LOCALE_477=Error in getting the user's unpaid order
M_LOCALE_478=Payment pre-check failed
M_LOCALE_479=Locking failed
M_LOCALE_480=Unlocking failed
M_LOCALE_481=Failed to open the table
M_LOCALE_482=Querying store area failed
M_LOCALE_483=Exception occurred while attempting to open the table
M_LOCALE_484=Failed to get the table status list information
SSO_REQUEST_FAILED=Authentication failed, request to SSO service failed.
M_LOCALE_485=Failed to fetch sensitive word configuration from Apollo.
M_LOCALE_486=Failed to read the +file+ configuration from Apollo.
M_LOCALE_487=Authentication failed.
M_LOCALE_488=Source is missing.
M_LOCALE_489=deviceGuid is empty.
M_LOCALE_490=JSON conversion exception!
M_LOCALE_491=Error converting object to JSON string byte array!
M_LOCALE_492=Error converting JSON string!
M_LOCALE_493=Error converting JSON string to list!
M_LOCALE_494=An error occurred, rollback.
M_LOCALE_495=Failed to update payment method, store data is empty.
M_LOCALE_496=Data has changed, please refresh and try again.
M_LOCALE_497=Failed to delete payment method, store data is empty.
M_LOCALE_498=Querying multiple store payment method names, input store is empty!!
M_LOCALE_499=State field exception.
M_LOCALE_500=Failed to add system province zero configuration, store data is empty.
M_LOCALE_501=Store data is empty.
M_LOCALE_502=Failed to set aggregate payment account information.
M_LOCALE_503=Failed to query whether the aggregate payment account name exists.
M_LOCALE_504=Failed to delete aggregate payment information based on payment information guid.
M_LOCALE_505=Failed to push message to Android <>.
M_LOCALE_506=Failed to push batch messages to Android <>.
M_LOCALE_507=Update store cash-up field table information.
M_LOCALE_508=Device type is empty.
M_LOCALE_509=Failed to save cash box record, please contact the administrator.
M_LOCALE_510=Store GUID is empty.
M_LOCALE_511=System employee data exception.
M_LOCALE_512=Invalid <> payment merchant number or key, cannot save.
M_LOCALE_513=Header enterprise GUID cannot be empty.
M_LOCALE_514=Store payment method does not exist.
M_LOCALE_515=Failed to update payment method payment mode.
M_LOCALE_516=Unsupported device type <>.
M_LOCALE_517=Image type is empty.
M_LOCALE_518=Data processing - Failed to modify handover record.
M_LOCALE_519=Failed to add 'Reason List'.
M_LOCALE_520=Prohibition of duplication.
M_LOCALE_521=Maximum of 6 images can be uploaded.
M_LOCALE_522=Failed to modify handover record, please contact the administrator.
M_LOCALE_523=At least one account needs to be saved.
M_LOCALE_524=Maximum of 5 reasons can be created under the same type.
M_LOCALE_525=Handover record <> does not exist.
M_LOCALE_526=No opening record exists.
M_LOCALE_527=Type cannot be empty.
M_LOCALE_528=Handover record <> does not exist.
M_LOCALE_529=Failed to add handover record, please contact the administrator.
M_LOCALE_530=Maximum of 10 accounts can be added.
M_LOCALE_531=Accounts cannot be duplicated.
M_LOCALE_532=<> Account name is duplicated, cannot save.
M_LOCALE_533=Payment division switch cannot be empty.
M_LOCALE_534=Failed to modify 'Reason List'.
M_LOCALE_535=Failed to query payment method.
M_LOCALE_536=The store has already initialized payment methods.
M_LOCALE_537=Payment method GUID cannot be empty.
M_LOCALE_538=There are unbalanced records, cannot perform the operation.
M_LOCALE_539=Copy 'Reason List: <>.
M_LOCALE_540=Only one default account can be set.
M_LOCALE_541=Account cannot be duplicated.
M_LOCALE_542=Cash box record <> does not exist.
M_LOCALE_543=Input time range is 1-99.
M_LOCALE_544=Failed to add store configuration, please contact the administrator.
M_LOCALE_545=No takeover record exists, cannot operate the cash box.
M_LOCALE_546=Failed to add payment method, store GUID cannot be empty!!
M_LOCALE_547=Account name cannot be duplicated.
M_LOCALE_548=Failed to query handover data.
M_LOCALE_549=Please set one account as default.
M_LOCALE_550=Please provide at least one configuration modification item.
M_LOCALE_551=Failed to modify store configuration, please contact the administrator.
M_LOCALE_552=<> The store already has the same name as the additional fee name.
M_LOCALE_553=The additional fee with the specified name was not found.
M_LOCALE_554=Please select additional fee.
M_LOCALE_555=Cannot create the same rule under the same store.
M_LOCALE_556=Province zero rule is duplicated.
M_LOCALE_557=Failed to push products to the store.
M_LOCALE_558=Store, enterprise, or SKU cannot be empty.
M_LOCALE_559=Store or shopping cart information cannot be empty.
M_LOCALE_560=Dish cannot be empty.
M_LOCALE_561=Name is required.
M_LOCALE_562=Banquet package category name is already occupied by banquet package function, please rename it.
M_LOCALE_563=Store cannot be empty.
M_LOCALE_564=Source parameters are incorrect.
M_LOCALE_565=The current week has reached the maximum limit.
M_LOCALE_566=Up to <> weeks can be selected.
M_LOCALE_567=Conflicts with the selected time period, please adjust the time period.
M_LOCALE_568=The current time period has reached the maximum limit.
M_LOCALE_569=Up to <> time periods can be selected.
M_LOCALE_570=Time conversion exception.
M_LOCALE_571=Brand or store cannot be empty.
M_LOCALE_572=Backend code is incorrect, attributes under the attribute group are not configured.
M_LOCALE_573=The name already exists, please make a modification.
M_LOCALE_574=There is a configuration error in the backend, attributes under the attribute group cannot be empty.
M_LOCALE_575=Merchant backend configuration is incorrect, the attribute group has no associated attributes.
M_LOCALE_576=Default selection attribute does not support multiple selection.
M_LOCALE_577=Attribute name is duplicated.
M_LOCALE_578=Failed to delete attribute value.
M_LOCALE_579=Attribute group GUID cannot be empty.
M_LOCALE_580=Duplicate name.
M_LOCALE_581=Attribute group does not exist.
M_LOCALE_582=BatchIdGenerator failed to generate product GUID.
M_LOCALE_583=Initialization data failed.
M_LOCALE_584=System is busy, please try again later.
M_LOCALE_585=When turning on inventory, the quantity cannot be less than 0.
M_LOCALE_586=Quantity cannot be less than 0.
M_LOCALE_587=Batch save failed.
M_LOCALE_588=Failed to cancel the estimate, package sub-dishes have been estimated.
M_LOCALE_589=Estimated product specification is empty.
M_LOCALE_590=Failed to query store business hours information.
M_LOCALE_591=Batch canceling estimated product specification GUID is empty.
M_LOCALE_592=Batch-discontinued products have empty Guid.
#M_LOCALE_593=Failed to query products on the shelf.
#M_LOCALE_593=Batch canceling product specification GUID is empty.
M_LOCALE_593=Product GUID cannot be empty.
M_LOCALE_594=Brand or store cannot be empty.
M_LOCALE_595=Please create a category first.
M_LOCALE_596=Package sub-dishes cannot be empty.
M_LOCALE_597=No available products currently.
M_LOCALE_598=Duplicate product number.
M_LOCALE_599=BatchIdGenerator failed to generate SKU GUID.
M_LOCALE_600=Group name is duplicated.
M_LOCALE_601=Product specification is duplicated.
M_LOCALE_602=Minimum sales quantity for packages must be an integer.
M_LOCALE_603=Product has been deleted.
M_LOCALE_604=Due to its association with package <>, the following stores cannot be unallocated: <>.
M_LOCALE_605=Please delete the product from the brand library.
M_LOCALE_606=Multiple selection attribute group must have more than 1 attribute.
M_LOCALE_607=The number of default attributes in the single selection attribute group cannot exceed 1.
M_LOCALE_608=Multiple specification product's specification name must be filled.
M_LOCALE_609=Specification name must be unique within the same product.
M_LOCALE_610=Multiple specification product's SKU code must be 1-16 characters long.
M_LOCALE_611=Package cannot be empty under the combo.
M_LOCALE_612=At least one product needs to be associated under the group.
M_LOCALE_613=SKU number has reached the upper limit and cannot be created.
M_LOCALE_614=QR code GUID.
M_LOCALE_615=Products and pushed stores cannot be empty.
M_LOCALE_616=Product data exception.
M_LOCALE_617=The same name template already exists, failed to save.
M_LOCALE_618=Scheme GUID cannot be empty.
M_LOCALE_619=Key to be saved cannot be empty.
M_LOCALE_620=Module entry parameter error.
M_LOCALE_621=Product type sorting exchange failed.
M_LOCALE_622=Failed to query specification data.
M_LOCALE_623=Specification GUID cannot be empty.
M_LOCALE_624=listItemInfoBySalesModelNew, product GUID cannot be empty.
M_LOCALE_625=Currently, no products are available for sale.
M_LOCALE_626=Product GUID collection cannot be empty.
M_LOCALE_627=Brand not found.
M_LOCALE_628=Category GUID cannot be empty.
M_LOCALE_629=Brand information not found.
M_LOCALE_630=Browsing mode cannot be empty.
M_LOCALE_631=Same scheme already exists.
M_LOCALE_632=Catering scheme product name must be 1-40 characters long.
M_LOCALE_633=Error saving product.
M_LOCALE_634=After unlisting the product, the <> catering product will be empty. Please clear the corresponding menu before saving.
M_LOCALE_635=Scheme product cannot be empty.
M_LOCALE_636=Duplicate parent menu.
M_LOCALE_637=Catering scheme does not exist.
M_LOCALE_638=Scheme information is empty.
M_LOCALE_639=Scheme GUID cannot be empty.
M_LOCALE_640=Failed to batch edit products for catering scheme.
M_LOCALE_641=Scheme information not found.
M_LOCALE_642=The <> catering product will be empty. Please clear the corresponding menu before saving.
M_LOCALE_643=Push type cannot be empty.
M_LOCALE_644=Scheme cannot be empty.
M_LOCALE_645=Push time cannot be empty.
M_LOCALE_646=Unlisting method cannot be empty.
M_LOCALE_647=Error in configuring product attributes, attribute group is not configured under.
M_LOCALE_648=Product configuration error.
M_LOCALE_649=System error.
M_LOCALE_650=Failed to query shelf status of products.
M_LOCALE_651=Scheme <> structure exception.
M_LOCALE_652=Group cannot be empty under the package <>.
M_LOCALE_653=Associating product is empty under group <> under package <>.
M_LOCALE_654=Shelf status field transmission error.
M_LOCALE_655=Duplicated SKU short code, please modify.
M_LOCALE_656=Transmission error in whole order discount status field.
M_LOCALE_657=Store is required.
M_LOCALE_658=Source parameters are incorrect.
REASON_CANNOT_BE_NULL=Reason cannot be empty.
REASON_NAME_CANNOT_BE_NULL=Reason name cannot be empty.
REASON_TYPE_CODE_CANNOT_BE_NULL=Reason type code cannot be empty.
FIELD_INCOMPLETE=Field is incomplete.
ITEM_TYPE_Duplicate=Duplicate item category name.
BRAND_REQUIRED_CHOOSE=Brand must be selected.
BRAND_REQUIRED=Brand is required.
STORE_REQUIRED=Store is required.
LEAST_ONE_CATEGORY=At least one category must be selected.
TYPE_ASSOCIATED_ITEM=This category is already associated with products, operation failed.
TYPE_ASSOCIATED_ATTRIBUTE=This category is already associated with attributes, operation failed.
PLAN_GUID_NULL=Menu plan GUID cannot be empty.
CATEGORY_NAME=Duplicate category name.
FAILED_TO_QUERY_STORES_BRAND=Failed to query all stores under the list of brands!
WRONG_PARAMS=Incorrect parameters.
BRAND_CANNOT_BE_NULL=Brand cannot be empty.
OP_FAIL=Operation failed.
DUPLICATE_ITEM_NAME=This product name already exists.
DEFAULT_NUM_CANNOT_GT_PICK_NUM=The number of default selected product specifications in the group cannot be greater than the number of optional product specifications in the group.
PICK_NUM_GT_ACTUAL_NUM=The number of optional product specifications in the group is greater than the actual number of optional product specifications.
STORE_CANNOT_BE_NULL=Store cannot be empty.
STORE_UNDER_BRAND_IS_EMPTY=No stores found under the brand.
NOT_FOUND_PRICE_PLAN_INFO=Not found, being used in menu plans.
ITEM_EXIT_PLAN=The product exists in the following menu plans: <>, please clear manually.
DUPLICATE_UPC=Duplicate product UPC.
STORE_GUID_NOT_EMPTY=Store GUID cannot be empty.
TIME_CONFLICT=Time conflict.
PRICE_PLAN_CANNOT_BE_EMPTY=Menu plan cannot be empty.
WRONG_ITEM_STRUCTURE=Product structure is abnormal.
DUPLICATE_CODE=SKU short code already exists.
DUPLICATE_RETAIL_CODE=Product article number already exists.

#\u83DC\u5355\u9875
MENU_HOME=HOME
MENU_STORES_AND_STAFF=STORES_AND_STAFF
MENU_EMPLOYEE_MANAGEMENT=EMPLOYEE_MANAGEMENT
MENU_EMPLOYEE_LIST=EMPLOYEE_LIST
MENU_CREATE_ACCOUNT=CREATE_ACCOUNT
MENU_EDIT_ACCOUNT=EDIT_ACCOUNT
MENU_ROLE_AUTHORIZATION=ROLE_AUTHORIZATION
MENU_ROLE_LIST=ROLE_LIST
MENU_STORE_MANAGEMENT=STORE_MANAGEMENT
MENU_STORE_LIST=STORE_LIST
MENU_TABLE_SEATING=TABLE_SEATING
MENU_SETTLEMENT_RULES=SETTLEMENT_RULES
MENU_AD_SPACE_MANAGEMENT=AD_SPACE_MANAGEMENT
MENU_DEVICE_BINDING=DEVICE_BINDING
MENU_STORE_ACCOUNTING=STORE_ACCOUNTING
MENU_PAYMENT_METHODS=PAYMENT_METHODS
MENU_DISCOUNT_RULES=DISCOUNT_RULES
MENU_QUEUE_SETTINGS=QUEUE_SETTINGS
MENU_RESERVATION_SETTINGS=RESERVATION_SETTINGS
MENU_ADDITIONAL_CHARGES=ADDITIONAL_CHARGES
MENU_GROUP_BUYING_MANAGEMENT=GROUP_BUYING_MANAGEMENT
MENU_TAKEAWAY_MANAGEMENT=TAKEAWAY_MANAGEMENT
MENU_REASON_MANAGEMENT=REASON_MANAGEMENT
MENU_STORAGE_MANAGEMENT=STORAGE_MANAGEMENT
MENU_PRINTING_SETTINGS=PRINTING_SETTINGS
MENU_CREATE_NEW_STORE=CREATE_NEW_STORE
MENU_EDIT_STORE=EDIT_STORE
MENU_TAG_MANAGEMENT=TAG_MANAGEMENT
MENU_AGGREGATED_PAYMENT_ACCOUNT_SETTINGS=AGGREGATED_PAYMENT_ACCOUNT_SETTINGS
MENU_ASSOCIATED_PRODUCTS(Meituan)=ASSOCIATED_PRODUCTS(Meituan)
MENU_ASSOCIATED_PRODUCTS(Ele.me)=ASSOCIATED_PRODUCTS(Ele.me)
MENU_ASSOCIATED_PRODUCTS(Jd)=ASSOCIATED_PRODUCTS(Jd)
MENU_ASSOCIATED_PRODUCTS(Self-owned-Store)=ASSOCIATED_PRODUCTS(Self-owned-Store)
MENU_CORPORATE_BRAND=CORPORATE_BRAND
MENU_ORGANIZATIONAL_STRUCTURE=ORGANIZATIONAL_STRUCTURE
MENU_WE_CHAT_OFFICIAL_ACCOUNT=WE_CHAT_OFFICIAL_ACCOUNT
MENU_AUTH_JUMP=AUTH_JUMP
MENU_OFFICIAL_ACCOUNT_AUTHORIZATION=OFFICIAL_ACCOUNT_AUTHORIZATION
MENU_WE_CHAT_STORE=WE_CHAT_STORE
MENU_BUSINESS_CONFIGURATION=BUSINESS_CONFIGURATION
MENU_BULK_ORDER_SETTINGS=BULK_ORDER_SETTINGS
MENU_EDIT_ORDER_CONFIGURATION=EDIT_ORDER_CONFIGURATION
MENU_TABLE_STICKER_SETTINGS=TABLE_STICKER_SETTINGS
MENU_EDIT_QUEUE_CONFIGURATION=EDIT_QUEUE_CONFIGURATION
MENU_BULK_EDIT_QUEUE_SETTINGS=BULK_EDIT_QUEUE_SETTINGS
MENU_PIECEWORK_MANAGEMENT=PIECEWORK_MANAGEMENT
MENU_OPERATOR_SUPPLEMENT=OPERATOR_SUPPLEMENT
MENU_ACCOUNT_CREDIT_MANAGEMENT=ACCOUNT_CREDIT_MANAGEMENT
MENU_ACCOUNT_CREDIT_REPAYMENT=ACCOUNT_CREDIT_REPAYMENT
MENU_ACCOUNT_CREDIT_UNIT=ACCOUNT_CREDIT_UNIT
MENU_PRODUCT_SALES=PRODUCT_SALES
MENU_BRAND_LIBRARY=BRAND_LIBRARY
MENU_PRODUCT_LIBRARY=PRODUCT_LIBRARY
MENU_CREATE_NEW_PRODUCT=CREATE_NEW_PRODUCT
MENU_CREATE_NEW_COMBO=CREATE_NEW_COMBO
MENU_EDIT_SELF_PRODUCT=EDIT_SELF_PRODUCT
MENU_EDIT_COMBO=EDIT_COMBO
MENU_ATTRIBUTE_LIBRARY=ATTRIBUTE_LIBRARY
MENU_BRAND_GALLERY=BRAND_GALLERY
MENU_PRODUCT_SALES_MODES=PRODUCT_SALES_MODES
MENU_MENU_SOLUTIONS=MENU_SOLUTIONS
MENU_EDIT_SOLUTION_PRODUCTS=EDIT_SOLUTION_PRODUCTS
MENU_KDS_PRODUCT_DISPLAY=KDS_PRODUCT_DISPLAY
MENU_CREATE/EDIT_DISPLAY_RULES=CREATE/EDIT_DISPLAY_RULES
MENU_CREATE/EDIT_MENU_SUMMARY=CREATE/EDIT_MENU_SUMMARY
MENU_BULK_EDIT_MENU=BULK_EDIT_MENU
MENU_STORE_PRODUCTS=STORE_PRODUCTS
MENU_PRODUCT_LIST=PRODUCT_LIST
MENU_ATTRIBUTE_LIST=ATTRIBUTE_LIST
MENU_STORE_GALLERY=STORE_GALLERY
MENU_BANQUET_PACKAGES=BANQUET_PACKAGES
MENU_PRODUCT_INVENTORY=PRODUCT_INVENTORY
MENU_SALES_TEMPLATES=SALES_TEMPLATES
MENU_TEMPLATE_MENUS=TEMPLATE_MENUS
MENU_ADD_PRODUCT=ADD_PRODUCT
MENU_EDIT_PRODUCT=EDIT_PRODUCT
MENU_CREATE_BANQUET_PACKAGE=CREATE_BANQUET_PACKAGE
MENU_EDIT_BANQUET_PACKAGE=EDIT_BANQUET_PACKAGE
MENU_EDIT_MENU=EDIT_MENU
MENU_MEMBER_MARKETING=MEMBER_MARKETING
MENU_CUSTOMER_MANAGEMENT=CUSTOMER_MANAGEMENT
MENU_CUSTOMER_LIST=CUSTOMER_LIST
MENU_CUSTOMER_SEGMENTS=CUSTOMER_SEGMENTS
MENU_ADD_SEGMENT(tag)=ADD_SEGMENT(tag)
MENU_EDIT_SEGMENT(tag)=EDIT_SEGMENT(tag)
MENU_CUSTOMER_DETAILS=CUSTOMER_DETAILS
MENU_DETAILS(tag)=DETAILS(tag)
MENU_DELETE_SEGMENT(tag)=DELETE_SEGMENT(tag)
MENU_POINTS_DETAILS=POINTS_DETAILS
MENU_GROWTH_POINTS_DETAILS=GROWTH_POINTS_DETAILS
MENU_MEMBERSHIP_CARD_MANAGEMENT=MEMBERSHIP_CARD_MANAGEMENT
MENU_BASIC_SETTINGS=BASIC_SETTINGS
MENU_LEVELS_AND_PRIVILEGES=LEVELS_AND_PRIVILEGES
MENU_GROWTH_POINTS_RULES=GROWTH_POINTS_RULES
MENU_RECHARGE_RULES=RECHARGE_RULES
MENU_ADD_LEVEL_AND_PRIVILEGES=ADD_LEVEL_AND_PRIVILEGES
MENU_LEVEL_AND_PRIVILEGES_DETAILS=LEVEL_AND_PRIVILEGES_DETAILS
MENU_EDIT_LEVEL_AND_PRIVILEGES=EDIT_LEVEL_AND_PRIVILEGES
MENU_COPY_LEVEL_AND_PRIVILEGES=COPY_LEVEL_AND_PRIVILEGES
MENU_PRIVILEGE_CARD_MANAGEMENT=PRIVILEGE_CARD_MANAGEMENT
MENU_PRIVILEGE_CARD_DETAILS=PRIVILEGE_CARD_DETAILS
MENU_CREATE_NEW_PRIVILEGE_CARD(page)=CREATE_NEW_PRIVILEGE_CARD(page)
MENU_EDIT_PRIVILEGE_CARD(page)=EDIT_PRIVILEGE_CARD(page)
MENU_POINTS_MANAGEMENT=POINTS_MANAGEMENT
MENU_EARNING_RULES=EARNING_RULES
MENU_REDEMPTION_RULES=REDEMPTION_RULES
MENU_EXPIRY_RULES=EXPIRY_RULES
MENU_RULES_EXPLANATION=RULES_EXPLANATION
MENU_POINTS_ACTIVITY_RECORDS=POINTS_ACTIVITY_RECORDS
MENU_MARKETING_CENTER=MARKETING_CENTER
MENU_RANDOM_RED_ENVELOPES=RANDOM_RED_ENVELOPES
MENU_CERTIFICATION_REWARDS=CERTIFICATION_REWARDS
MENU_COUPON_LIST=COUPON_LIST
MENU_MARKETING_ACTIVITIES=MARKETING_ACTIVITIES
MENU_PRECISION_MARKETING=PRECISION_MARKETING
MENU_THIRD_PARTY_PLATFORM_ACTIVITIES=THIRD_PARTY_PLATFORM_ACTIVITIES
MENU_FULL_REDUCTION_AND_DISCOUNTS=FULL_REDUCTION_AND_DISCOUNTS
MENU_RECHARGE_GIFTS=RECHARGE_GIFTS
MENU_SUBSIDY_ACTIVITIES=SUBSIDY_ACTIVITIES
MENU_ADD_COUPON=ADD_COUPON
MENU_ADD_PRODUCT_COUPON=ADD_PRODUCT_COUPON
MENU_CREATE_NEW_ACTIVITY=CREATE_NEW_ACTIVITY
MENU_COUPON_STATISTICS=COUPON_STATISTICS
MENU_VIEW_COUPON=VIEW_COUPON
MENU_EDIT_ACTIVITY=EDIT_ACTIVITY
MENU_EDIT_COUPON=EDIT_COUPON
MENU_ACTIVITY_DETAILS=ACTIVITY_DETAILS
MENU_VIEW_PRODUCT_COUPON=VIEW_PRODUCT_COUPON
MENU_ACTIVITY_STATISTICS=ACTIVITY_STATISTICS
MENU_EDIT_PRODUCT_COUPON=EDIT_PRODUCT_COUPON
MENU_GIFTING_RECORDS=GIFTING_RECORDS
MENU_COUPON_EXCLUSION_SETTINGS=COUPON_EXCLUSION_SETTINGS
MENU_RECHARGE_GIFT_ACTIVITY_DETAILS=RECHARGE_GIFT_ACTIVITY_DETAILS
MENU_CREATE_NEW_RECHARGE_GIFT_ACTIVITY=CREATE_NEW_RECHARGE_GIFT_ACTIVITY
MENU_EDIT_RECHARGE_GIFT_ACTIVITY=EDIT_RECHARGE_GIFT_ACTIVITY
MENU_CREATE_NEW_SUBSIDY_ACTIVITY=CREATE_NEW_SUBSIDY_ACTIVITY
MENU_CREATE_NEW_THIRD_PARTY_PLATFORM_ACTIVITY=CREATE_NEW_THIRD_PARTY_PLATFORM_ACTIVITY
MENU_EDIT_THIRD_PARTY_PLATFORM_ACTIVITY=EDIT_THIRD_PARTY_PLATFORM_ACTIVITY
MENU_VIEW_THIRD_PARTY_PLATFORM_ACTIVITY=VIEW_THIRD_PARTY_PLATFORM_ACTIVITY
MENU_THIRD_PARTY_PLATFORM_ACTIVITY_STATISTICS=THIRD_PARTY_PLATFORM_ACTIVITY_STATISTICS
MENU_DATA_REPORT_NEW=DATA_REPORT_NEW
MENU_BUSINESS_OVERVIEW=BUSINESS_OVERVIEW
MENU_STORE_SUMMARY=STORE_SUMMARY
MENU_DATA_DASHBOARD=DATA_DASHBOARD
MENU_PRODUCT_REPORTS=PRODUCT_REPORTS
MENU_PRODUCT_SALES_STATISTICS=PRODUCT_SALES_STATISTICS
MENU_PRODUCT_CATEGORY_STATISTICS=PRODUCT_CATEGORY_STATISTICS
MENU_COMBO_SALES_STATISTICS=COMBO_SALES_STATISTICS
MENU_RETURN_REPORTS=RETURN_REPORTS
MENU_DISH_RETURN_DETAILS=DISH_RETURN_DETAILS
MENU_PRODUCT_SALES_DETAILS=PRODUCT_SALES_DETAILS
MENU_GIFT_REPORTS=GIFT_REPORTS
MENU_GIFT_DETAILS=GIFT_DETAILS
MENU_STORE_PRODUCT_SALES=STORE_PRODUCT_SALES
MENU_FINANCIAL_REPORTS=FINANCIAL_REPORTS
MENU_TAKEAWAY_SETTLEMENT_DETAILS=TAKEAWAY_SETTLEMENT_DETAILS
MENU_GROUP_BUYING_SETTLEMENT_DETAILS=GROUP_BUYING_SETTLEMENT_DETAILS
MENU_PAYMENT_COMPOSITION=PAYMENT_COMPOSITION
MENU_ORDER_SUMMARY=ORDER_SUMMARY
MENU_ORDER_REPORTS=ORDER_REPORTS
MENU_ORDER_STATISTICS=ORDER_STATISTICS
MENU_ORDER_DETAILS=ORDER_DETAILS
MENU_TAKEAWAY_ORDER_DETAILS=TAKEAWAY_ORDER_DETAILS
MENU_DOWNLOAD_CENTER=DOWNLOAD_CENTER
MENU_OPERATOR_REPORTS=OPERATOR_REPORTS
MENU_SERVICE_DETAILS_REPORT=SERVICE_DETAILS_REPORT
MENU_SERVICE_SUMMARY_REPORT=SERVICE_SUMMARY_REPORT
MENU_TAKEAWAY_ABNORMAL_DATA=TAKEAWAY_ABNORMAL_DATA
MENU_ABNORMAL_DATA=ABNORMAL_DATA
MENU_REPAIR_AUDIT=REPAIR_AUDIT
MENU_DATA_REPAIR=DATA_REPAIR
MENU_AUDIT_DETAILS=AUDIT_DETAILS
MENU_SHIFT_HANDOVER_STATISTICS=SHIFT_HANDOVER_STATISTICS
MENU_MORE=MORE
MENU_SMS_RECHARGE=SMS_RECHARGE
MENU_DATA_REPORT_OLD=DATA_REPORT_OLD
MENU_SALES_DETAILS=SALES_DETAILS
MENU_DAILY_EXPORT=DAILY_EXPORT
MENU_PAYMENT_METHOD_STATISTICS=PAYMENT_METHOD_STATISTICS
MENU_INVENTORY_MANAGEMENT=INVENTORY_MANAGEMENT
MENU_CREATE_MATERIAL=CREATE_MATERIAL
MENU_CREATE_NEW_MATERIAL=CREATE_NEW_MATERIAL
MENU_EDIT_MATERIAL=EDIT_MATERIAL
MENU_MATERIAL_LIST=MATERIAL_LIST
MENU_MATERIAL_CATEGORY=MATERIAL_CATEGORY
MENU_CONFIGURE_PRODUCTS=CONFIGURE_PRODUCTS
MENU_PRODUCT_MATERIAL_RATIO=PRODUCT_MATERIAL_RATIO
MENU_SUPPLIER_LIST=SUPPLIER_LIST
MENU_CREATE_SUPPLIER=CREATE_SUPPLIER
MENU_EDIT_SUPPLIER=EDIT_SUPPLIER
MENU_SUPPLIER_PRICING_SCHEME=SUPPLIER_PRICING_SCHEME
MENU_INBOUND_MANAGEMENT=INBOUND_MANAGEMENT
MENU_INBOUND_LIST=INBOUND_LIST
MENU_CREATE_INBOUND_ORDER=CREATE_INBOUND_ORDER
MENU_EDIT_INBOUND_ORDER=EDIT_INBOUND_ORDER
MENU_VIEW_INBOUND_ORDER=VIEW_INBOUND_ORDER
MENU_OUTBOUND_MANAGEMENT=OUTBOUND_MANAGEMENT
MENU_OUTBOUND_LIST=OUTBOUND_LIST
MENU_CREATE_OUTBOUND_ORDER=CREATE_OUTBOUND_ORDER
MENU_EDIT_OUTBOUND_ORDER=EDIT_OUTBOUND_ORDER
MENU_VIEW_OUTBOUND_ORDER=VIEW_OUTBOUND_ORDER
MENU_STOCKTAKING_MANAGEMENT=STOCKTAKING_MANAGEMENT
MENU_STOCKTAKING_LIST=STOCKTAKING_LIST
MENU_CREATE_STOCKTAKING_ORDER=CREATE_STOCKTAKING_ORDER
MENU_VIEW_STOCKTAKING_ORDER=VIEW_STOCKTAKING_ORDER
MENU_EDIT_STOCKTAKING_ORDER=EDIT_STOCKTAKING_ORDER
MENU_WAREHOUSE_MANAGEMENT=WAREHOUSE_MANAGEMENT
MENU_WAREHOUSE_LIST=WAREHOUSE_LIST
MENU_STOCK_INQUIRY=STOCK_INQUIRY
MENU_IN_OUT_FLOW_DETAILS=IN_OUT_FLOW_DETAILS
MENU_SUPPLIER_RECONCILIATION=SUPPLIER_RECONCILIATION
MENU_MATERIAL_CONSUMPTION_SUMMARY=MATERIAL_CONSUMPTION_SUMMARY
INSUFFICIENT_STOCK=<> insufficient stock, cannot place an order.
#\u7EC8\u7AEF\u663E\u793A
SUCCESSFULLY_PAID_YUAN=<> successfully paid <>\uFFE5
PAYMENT_RECEIVED_WE_CHAT=Payment received, WeChat credited to your account, <>\uFFE5
NEW_FOOD_DELIVERY_ORDER_PROCESSED=You have a new food delivery order that needs to be processed.
NEW_WE_CHAT_ORDER_PROCESSED=You have a new WeChat order that needs to be processed.
NEW_OTHER_ORDER_PROCESSED=You have a new other order that needs to be processed.
NEW_PAD_ORDER_PROCESSED=You have a new PAD order that needs to be processed.
NEW_MEITUAN_ORDER_HANDLE=You have a new Meituan order, please handle it promptly.
NEW_ELE_ME_ORDER_HANDLE=You have a new Ele.me order, please handle it promptly.
NEW_JD_ORDER_HANDLE=You have a new Jd order, please handle it promptly.
NEW_SELF_OPERATED_ORDER_HANDLE=You have a new self-operated food delivery platform order, please handle it promptly.
NEW_ORDER_HANDLE=You have a new order, please handle it promptly.
MEITUAN_REMINDER_HANDLE=You have a new Meituan reminder, please handle it promptly.
ELE_ME_REMINDER_HANDLE=You have a new Ele.me reminder, please handle it promptly.
JD_REMINDER_HANDLE=You have a new Jd reminder, please handle it promptly.
NEW_REMINDER_HANDLE=You have a new reminder, please handle it promptly.
MEITUAN_ORDER_PREPARED_HANDLE=You have a Meituan order that has been prepared, please handle it promptly.
ELE_ME_ORDER_PREPARED_HANDLE=You have an Ele.me order that has been prepared, please handle it promptly.
JD_ORDER_PREPARED_HANDLE=You have an Jd order that has been prepared, please handle it promptly.
SELF_OPERATED_ORDER_PREPARED_HANDLE=You have an order from a self-operated food delivery platform that has been prepared, please handle it promptly.
ORDER_PREPARED_HANDLE=You have an order that has been prepared, please handle it promptly.
REFUND_REQUEST_HANDLE=You have a new request for a refund, please handle it promptly.
MEITUAN_ABNORMAL_ORDER_HANDLE=You have a new Meituan abnormal order, please handle it promptly.
ELE_ME_ABNORMAL_ORDER_HANDLE=You have a new Ele.me abnormal order, please handle it promptly.
JD_ABNORMAL_ORDER_HANDLE=You have a new Jd abnormal order, please handle it promptly.
NEW_ABNORMAL_ORDER_HANDLE=You have a new abnormal order, please handle it promptly.
MEITUAN_ORDER_CANCELED=Your <> number Meituan order has been canceled.
ELE_ME_ORDER_CANCELED=Your <> number Ele.me order has been canceled.
JD_ORDER_CANCELED=Your <> number Jd order has been canceled.

#\u652F\u4ED8\u65B9\u5F0F
CASH_PAY=Cash Payment
JH_PAY=Aggregated Payment
BANK_CARD_PAY=UnionPay
MEMBER_CARD_PAY=Member Balance Payment
FACE_TO_PAY=Face Payment
DEBT_PAY=Debt Payment
CANTEEN_CARD_PAY=Canteen Card Payment
CANTEEN_ELECTRONIC_CARD=Canteen Electronic Card Payment
CANTEEN_PHYSICAL_CARD=Canteen Physical Card Payment
THIRD_ACTIVITY=Third-Party Platform Activity
ZHUAN_CAN=Zhuan-Can Takeaway
MT_GROUPON_PAYMENT=Meituan
DOUYIN_GROUPON_PAYMENT=Douyin
MEIT_TUAN=Meituan Takeaway
E_LE_MA=Ele.me Takeaway
JD=Jd Takeaway
CARD_PAY=Card Payment
ABC_PAYMENT=Abc Payment
ALIPAY_PAYMENT=Alipay Payment

PAY_CASH=Cash Payment
PAY_AGG=Aggregated Payment
PAY_CARD=UnionPay
PAY_MEMBER=Member Balance Payment
PAY_FACE=Face Payment
PAY_TONGCHIDAO=TCD Payment
PAY_RESERVE=Deposit Payment
PAY_DEBT_PAY=Debt Payment
PAY_OTHER=Other Payment
PAY_CANTEEN_ELECTRONIC_CARD=Canteen Electronic Card Payment
PAY_CANTEEN_PHYSICAL_CARD=Canteen Physical Card Payment
PAY_THIRD_ACTIVITY=Third-Party Platform Activity
PAY_MT_GROUPON=Meituan
PAY_DOUYIN_GROUPON=DouYin

#result
INTERFACE_EXCEPTION=Interface exception, please try again later.
DEFAULT_ADMINISTRATOR=Default administrator
OPERATION_SUCCESSFUL=Operation successful
OPERATION_FAILED=Operation failed
CREATION_SUCCESSFUL=Creation successful
MODIFICATION_SUCCESSFUL=Modification successful
ORDER_PREPARATION_SUCCESSFUL=Order preparation successful

#discount
DISCOUNT_MEMBER=Member Discount
DISCOUNT_WHOLE=Whole Order Discount
DISCOUNT_CONCESSIONAL=Whole Order Price Reduction
DISCOUNT_SYSTEM=System Discount
DISCOUNT_FREE=Free Offer
DISCOUNT_GROUPON=Groupon Verification
DISCOUNT_MEMBER_GROUPON=Member Voucher
DISCOUNT_POINTS_DEDUCTION=Points Deduction
DISCOUNT_SINGLE_MEMBER=Single Item Member Discount
DISCOUNT_SINGLE_DISCOUNT=Single Item Discount
DISCOUNT_GOODS_GROUPON=Member Goods Voucher
DISCOUNT_ACTIVITY=Marketing Activity
DISCOUNT_TONGCHIDAO=Tongchidao Discount
DISCOUNT_FOLLOW_RED_PACKET=Follow Red Packet Discount
DISCOUNT_DOU_YIN_GROUPON=Douyin Voucher Verification
DISCOUNT_DA_ZHONG_DIAN_PIN=Dazhong Dianping Voucher Verification
DISCOUNT_DOU_YIN=Douyin Voucher Verification
DISCOUNT_ABC_GROUPON=Abc Voucher Verification

#\u5916\u5356
TAKEOUT_IMMEDIATE_DELIVERY=Immediate delivery
TAKEOUT_DELIVERY_INFORMATION=Delivery Information
TAKEOUT_ORDER_INFORMATION=Order Information
TAKEOUT_ACCEPTANCE_INFORMATION=Acceptance Information
TAKEOUT_NAME=Name
TAKEOUT_CONTACT_PHONE_NUMBER=Contact Phone Number
TAKEOUT_DELIVERY_ADDRESS=Delivery Address
TAKEOUT_PICK_UP_IN_STORE=Pick-up in-store
TAKEOUT_DELIVERY_TIME=Delivery Time
TAKEOUT_NOTE=Note
TAKEOUT_PICK_UP_AT_STORE=Pick-up at Store
TAKEOUT_ORDER_NUMBER=Order Number
TAKEOUT_ORDER_PLACEMENT_TIME=Order Placement Time
TAKEOUT_ORDER_SOURCE=Order Source
TAKEOUT_ORDER_AMOUNT=Order Amount
TAKEOUT_PAID_AMOUNT=Paid Amount
TAKEOUT_ACCEPTANCE_TIME=Acceptance Time
TAKEOUT_ACCEPTANCE_OPERATOR=Acceptance Operator
TAKEOUT_TONGCHIDAOSHANGJIA_EDITION=Tongchidaoshangjia Edition
TAKEOUT_EARN_MEAL_SELF_OPERATED_TAKEAWAY=Earn Meal Self-operated Takeaway
TAKEOUT_ELE_ME=Ele.me
TAKEOUT_JD=Jd
TAKEOUT_DEFAULT_ADMINISTRATOR=Default Administrator
TAKEOUT_COMPLETION_INFORMATION=Completion Information
TAKEOUT_COMPLETION_TIME=Completion Time
TAKEOUT_DELIVERY_STATUS=Delivery Status
TAKEOUT_DELIVERY_CONFIRMED=Delivery Confirmed
TAKEOUT_CANCELLATION_INFORMATION=Cancellation Information
TAKEOUT_CANCELLATION_TIME=Cancellation Time
TAKEOUT_CANCELLATION_REASON=Cancellation Reason
TAKEOUT_CANCELLATION_OPERATOR=Cancellation Operator
TAKEOUT_ORDERING_USER=Ordering User
TAKEOUT_AUTOMATICALLY_CANCELED =Order not confirmed within the time limit, automatically canceled by the system
TAKEOUT_MEITUAN_SYSTEM=Meituan system
TAKEOUT_MEITUAN=Meituan
TAKEOUT_REJECTION_TIME=Rejection time
TAKEOUT_REJECTION_REASON=Rejection reason
TAKEOUT_UNABLE_DELIVER=Unable to deliver
TAKEOUT_REJECTION_OPERATOR=Rejection operator
TAKEOUT_ALL_IN_ONE_MACHINE=All-in-One Machine
TAKEOUT_PROVIDING_MEALS=Providing tableware according to the number of meals
TAKEOUT_PRODUCT_SOLD_OUT=Product sold out
TAKEOUT_REFUND_REQUEST=Refund/Return Request
TAKEOUT_RETURN_REQUEST_TIME=Return Request Time
TAKEOUT_REFUND_ITEMS=Refund Items
TAKEOUT_REASON_FOR_REFUND=Reason for Refund
TAKEOUT_REFUND_AMOUNT=Refund Amount

#order
ORDER_NOT_SETTLED=Not settled
ORDER_CANCELLED=Cancelled
ORDER_REFUNDED=Refunded
ORDER_REVERSED_SETTLEMENT=Reversed settlement
ORDER_SETTLED=Settled
ORDER_PENDING_DELIVERY=Pending Delivery
ORDER_COMPLETED=Completed

#device
MERCHANT=PC Server
All_IN_ONE=All-in-One Machine
POS=POS Terminal
CLOUD_PANEL=Cloud Tablet
M1=Ordering Device (M1)
WECHAT=WeChat (Official Account)
WECHAT_MINI=WeChat (Mini Program)

#mode
TRADE_MODE_DINEIN=Regular Dining
TRADE_MODE_FAST=Fast Food
TRADE_MODE_TAKEOUT=Takeout
TRADE_MODE_TAKEOUT_ZC=Earn Meal Takeout
TRADE_MODE_TAKEOUT_MT=Meituan Takeout
TRADE_MODE_TAKEOUT_ELE=Ele.me Takeout
TRADE_MODE_TAKEOUT_JD=Jd Takeout

#Reason
REASON_UNDO_CHECKOUT=Undo Checkout
REASON_VOID_ORDER=Void Order
REASON_CANCEL_RESERVATION=Cancel Reservation
REASON_REJECT_TAKEAWAY_ORDER=Reject Takeaway Order
REASON_RETURN_TAKEAWAY_ORDER=Return Takeaway Order
REASON_REJECT_REFUND_FOR_TAKEAWAY=Reject Refund for Takeaway
REASON_RETURN_DISH=Return Dish
REASON_COMPLIMENTARY_DISH=Complimentary Dish
REASON_INCORRECT_SETTLEMENT=Incorrect Settlement
REASON_REMAINING_DISHES_NOT_RETURNED=Remaining Dishes Not Returned
REASON_MODIFY_PAYMENT_METHOD=Modify Payment Method
REASON_OTHER=Other
REASON_CUSTOMER_LEAVES=Customer Leaves
REASON_DUPLICATE_ORDER=Duplicate Order
REASON_CUSTOMER_CANCELS_TEMPORARILY=Customer Cancels Temporarily
REASON_NO_AVAILABLE_TABLES=No Available Tables
REASON_CUSTOMER_DIDNT_SHOW_UP=Customer Didn't Show Up
REASON_ITEM_SOLD_OUT=Item Sold Out
REASON_ITEM_ALREADY_SERVED=Item Already Served
REASON_AMOUNT_ALREADY_REFUNDED=Amount Already Refunded
REASON_ORDER_PLACED_INCORRECTLY=Order Placed Incorrectly
REASON_SLOW_SERVICE=Slow Service
REASON_PROMOTIONAL_GIFT=Promotional Gift
REASON_CONSUMPTION_GIFT=Consumption Gift
REASON_BOSS_GIFT=Boss Gift

PERMANENT_VALIDITY=Permanent Validity
RESUME_SALES=Resume Sales
DEFAULT_TEMPLATE=Default Template
ORDER_LOCKED= Order has been locked
NOT_ALLOW_SETTLEMENT= Order status does not allow settlement
PAYMENT_FAILED=Payment failed
PAYMENT_SUCCESSFUL=Payment successful
INVALID_BOOKING_TIME=Invalid Booking Time
DEFAULT_LEVEL=Default level
ONLY_UNPAID_ORDERS_CAN_SETTLED=Only unpaid orders can be settled.
SUBMISSION_FAILED_REJECTED=Submission failed, order rejected
PRICE_TOTAL=Price total

SURCHARGE_PER_PERSON=Additional fee/per person
SURCHARGE_PER_TABLE=Additional fee/per table

#refundType
REFUND_TYPE_DINEIN_PART_REFUND=Partial Refund For Regular Dining
REFUND_TYPE_DINEIN_RECOVERY=Anti Settlement For Regular Dining
REFUND_TYPE_FAST_PART_REFUND=Partial Refund For Fast Food
REFUND_TYPE_FAST_RECOVERY=Anti Settlement For Fast Food
REFUND_TYPE_TOTAL=Total