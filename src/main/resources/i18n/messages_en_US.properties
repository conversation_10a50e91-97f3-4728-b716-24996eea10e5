#\u8FD9\u91CC\u586B\u5199\u82F1\u8BED\u7FFB\u8BD1
checkout_invoice=CHECKOUT
checkout_invoice_header=**CHECKOUT**
item_list_invoice_header=**ITEM LIST**
handover_invoice_header=**HANDOVER**
order_item_invoice_header=**ORDER**
refund_item_invoice_header=**REFUND**
stored_value_invoice_header=**STORED VALUE**
pre_bill_invoice_header=**PRE-BILL**
combined_pre_bill_invoice_header=**COMBINED PRE-BILL**
combined_checkout_invoice_header=**COMBINED CHECKOUT**
table_transfer_invoice_header=**TABLE TRANSFER**
pre_handover_invoice_header=**PRE-HANDOVER**
pre_order_items_statistics_invoice_header=**PRE-ORDER ITEMS STATISTICS**
business_overview_invoice_header=**BUSINESS OVERVIEW**
payment_statistics_invoice_header=**PAYMENT STATISTICS**
member_consumption_statistics_invoice_header=**MEMBER CONSUMPTION STATISTICS**
member_recharge_statistics_invoice_header=**MEMBER RECHARGE STATISTICS**
dining_type_statistics_invoice_header=**DINING TYPE STATISTICS**
category_sales_statistics_invoice_header=**CATEGORY SALES STATISTICS**
item_stats=**PRODUCT SALES STATISTICS**
item_refund_stats=**PRODUCT REFUND STATISTICS**
item_gift_stats=**PRODUCT GIFT STATISTICS**
retail_item_stats=**PRODUCT SALES STATISTICS**
retail_item_refund_stats=**PRODUCT REFUND STATISTICS**
retail_item_gift_stats=**PRODUCT GIFT STATISTICS**
attribute_sales_statistics_invoice_header=**ATTRIBUTE SALES STATISTICS**
call_for_order_invoice_header=**CALL FOR ORDER**
expedite_order_invoice_header=**EXPEDITE ORDER**
suspended_order_invoice_header=**SUSPENDED ORDER**
change_note_list_invoice_header=***CHANGE NOTE***
transfer_item_list_invoice_header=***TRANSFER NOTE***
debt_repayment_invoice_header=**DEBT REPAYMENT**
refund_invoice_header=**REFUND NOTE**
order_cancel= Order Cancel 

mark_name=MarkNo: 
mark_name_without_colon=MarkNo
table_name=TableNo: 
takeout_name=takeoutName: 
table_name_without_colon=TableNo
old_table=OldTable\uFF1A
transfer_table=TransferTable\uFF1A
order_number=OrderNo: 
food_finish_code=FoodFinishCode\uFF1A
customer_number=CustomerNo: 
cashier=Cashier: 
print_time=Date: 
checkout=Date: 
item_total=Total: 
additional_fee=surcharge: 
amount_discounted=Disc: 
amount_should_pay=Sub: 
amount_actually_paid=Paid: 
store_address=Address: 
store_phone=Tel: 
operator=Operator: 
open_table_time_short=Date: 
handover_employee=Handover: 
duration_time=Duration: 
start_time=Begin: 
end_time=End: 
checkout_staffs= Cashier: 
on_duty_turnover=Turnover: 
project_income=Income
sales_income=SalesIncome
recharge_income=RechargeIncome
note_for_turnover=Note: turnover does not include member spending amount
undefined=Undefined
meal_time=MealTime: 
order_remark=Remark: 
total_copies=Copies: 
order_time_short=Date: 
transaction_id=TransId:
recharge_amount=Recharge: 
gift_amount=Gift: 
amount_of_arrival=Arrival: 
recharge_method=Method: 
recharge_card_number=CardNo: 
current_balance=Balance: 
current_points=Points: 
recharge_time=Date: 
takeout_print_warning=[TAKEOUT PRINT ERROR]
takeout_print_warning_description=The following items are not bound to the menu system menu, can not print the kitchen
expected_delivery_time=Delivery: 
reserve=\u3010reserve\u3011
order_time=Date: 
open_table_time=Date: 
remark=Remark: 
no_invoice_required=No invoice required
bill_looked_up=Bill looked up\uFF1A
duty_paragraph=Duty paragraph\uFF1A
tableware=tableware\uFF1A
money_unit=$
colon_sign=: 
money_join=: $
reduce_join=: -$
total_money_join=Total: $
paid_money_join=Paid: $

item_list_invoice=item list ticket 
order_item_invoice=order ticket 
refund_item_invoice=refund ticket 
takeout_invoice=takeout ticket 
turnover_invoice=turnover ticket
turn_table_invoice=turn table ticket
op_stats_invoice=op stats ticket
receipt_stats_invoice=receipt stats ticket
mem_stats_invoice=mem stats ticket
trade_stats_invoice=trade stats ticket
type_stats_invoice=type stats ticket
item_stats_invoice=item stats ticket
prop_stats_invoice=prop stats ticket
item_refund_stats_invoice=item refund stats ticket
item_gift__stats_invoice=item gift stats ticket
label_invoice=label ticket
stored_value_invoice=stored value ticket
queue_invoice=queue ticket
print_failed_please_handle_it_promptly=number {} order printing failed, please handle it in time
print_failed_msg_for_turnover=handover ticket printing failed, please handle it in time
print_failed_msg_for_label=label ticket printing failed, please handle it in time
print_failed_msg_for_stored_value=stored value ticket printing failed, please handle it in time

item=item
item_refund=item refund
quantity=quantity
quantity_refund=quantity refund
sale_quantity=sale quantity
sale_amount=sale amount
price=price
item_sum=itemsum
sub_prop=SubProperty
prop_fee=PropFee
item_price_total=Subtotal
price_total=Total
hang=\u3010hang\u3011
weight=[weight]
set=[set]
compl=[compl]
attr=attr:
prt_time=Prt Time: 
surcharge_total=Surcharge Total
promotion_total=Promotion Total
total_due=Total Due
total_amt_merged_tables=Total Amt Merged Tables
change=Change
paid=Paid
un_paid=Un Paid
checkout_time=Checkout Time: 
transaction=Transaction: 
recharge_card_number1=CardNo: 
recharge_amount1=Recharge
pay_type=PayType
gift_amount1=Gift
current_balance1=Balance
card_num=card_num
member_recharge_amount=Member Recharge Amount
member_gift_amount=Member Gift Amount
member_subsidy_amount=Member Subsidy Amount
debt_unit_name=Debt Unit Name
debt_contact_name=Debt Contact Name
debt_contact_tel=Debt Contact Tel
current_points1=Points
reward_points=Reward Points
pick_up_time=Pick-up Time: 
number_diners=Number of Diners: 
your_number_is=Your number is: 
waiting_tables=In front of you, there are %s tables waiting
scan_qr=Worried about missing your turn? Scan this QR code
proceed_ticket=When your number is called, please proceed with your ticket for dining
store_phone_number=Store Phone: 
provided_company=The system is provided by the HOLDER
original_table=Original Table
transferred_to=Transferred to
transfer_time=Transfer time: 
cannot_printed_kitchen=The following dishes cannot be printed in the kitchen
item_total_amount=Total Amount
original_price=Original Price: 
user_online_payment=User Online Payment: 
name=Name: 
customer_phone_number=Customer Phone%s: 
transfer=Transfer
virtual_phone=Virtual Phone%s: 
address=Address: 
takeout_assurance_card=Takeout Assurance Card: 
maker=Maker: 
body_temperature=Body Temperature: 
packager=Packager: 
delivery_driver=Delivery Driver: 
safety_tip=Food, packaging, and delivery safety can all be traced. Please rest assured while enjoying your meal.
pocket_number=Pocket Number %s
handover_supervisor=Handover Supervisor: 
shift_duration=Shift Duration: 
shift_start_time=Shift Start Time: 
shift_end_time=Shift End Time: 
amount_to_be_handed_over=Amount to be Handed Over: 
actual_handed_over_cash=Actual Handed Over Cash: 
sales_statistics=Sales Statistics
number_of_completed_sales_orders=Number of Completed Sales Orders: 
transactions=Transactions
net_sales_amount=Net Sales Amount (Completed Orders): 
remaining_balance=Remaining Balance
stored_value_statistics=Stored Value Statistics
number_of_stored_value_orders=Number of Stored Value Orders: 
total_stored_value_amount=Total Stored Value Amount: 
reservation_statistics=Reservation Statistics
number_of_reservation_orders=Number of Reservation Orders: 
total_reservation_deposit=Total Reservation Deposit: 
credit_account_statistics=Credit Account Statistics
number_of_credit_account_orders=Number of Credit Account Orders: 
total_credit_account_amount=Total Credit Account Amount: 
single_item=Single Item
combo=Combo
pre_order_quantity=Pre-order quantity
order_count=Order count
customer_traffic=Customer traffic
person=person
sales_revenue=Sales revenue(Total sales revenue of settled orders)
total_net_sales=Total net sales
estimated_amount=estimated amount due
total_discounts=Total discounts
total_refund=Total refund
total_amount_received=Total amount received
payment_methods=Payment methods
net_sales=Net sales
recharge_amount2=Recharge amount
reservation_amount=Reservation amount
member_consumption_overview=Member consumption overview
number_of_consumption_orders=Number of consumption orders
total_consumption_amount=Total consumption amount
member_recharge_overview=Member recharge overview
number_of_recharge_orders=Number of recharge orders
total_recharge_amount=Total recharge amount
recharge_bonus=Recharge bonus
consumers=Consumers
recharge_users=Recharge Users
dine_in_statistics=Dine-in Statistics
average_order_amount=Average Order Amount
average_per_capita_spending=Average Per Capita Spending
snack_statistics=Snack Statistics
takeaway_statistics=Takeaway Statistics
product_categories=Product categories
amount=Amount
product_name=Product name
attribute_name=Attribute name
attribute_group=Attribute group: 
receipt_note=Receipt Note: For in-store use only.
copies=copies
test_table_one=Test table 1
test_table_two=Test table 2
test_item=test item
test_type=test type
admin=Admin
test_store=Test store
cold_dishes_first=Cold dishes first
disinfect_tableware=disinfect tableware
cash=cash
test_store_address=test store address

#\u652F\u4ED8\u65B9\u5F0F
CASH_PAY=Cash Payment
JH_PAY=Aggregated Payment
BANK_CARD_PAY=UnionPay
MEMBER_CARD_PAY=Member Balance Payment
FACE_TO_PAY=Face Payment
DEBT_PAY=Debt Payment
CANTEEN_CARD_PAY=Canteen Card Payment
CANTEEN_ELECTRONIC_CARD=Canteen Electronic Card Payment
CANTEEN_PHYSICAL_CARD=Canteen Physical Card Payment
THIRD_ACTIVITY=Third-Party Platform Activity
ZHUAN_CAN=Zhuan-Can Takeaway
MT_GROUPON_PAYMENT=Meituan
DOUYIN_GROUPON_PAYMENT=Douyin
ALIPAY_PAYMENT=Alipay
ABC_PAYMENT=Abc

#discount
DISCOUNT_MEMBER=Member Discount
DISCOUNT_WHOLE=Whole Order Discount
DISCOUNT_CONCESSIONAL=Whole Order Price Reduction
DISCOUNT_SYSTEM=System Discount
DISCOUNT_FREE=Free Offer
DISCOUNT_GROUPON=Groupon Verification
DISCOUNT_MEMBER_GROUPON=Member Voucher
DISCOUNT_POINTS_DEDUCTION=Points Deduction
DISCOUNT_SINGLE_MEMBER=Single Item Member Discount
DISCOUNT_SINGLE_DISCOUNT=Single Item Discount
DISCOUNT_GOODS_GROUPON=Member Goods Voucher
DISCOUNT_ACTIVITY=Marketing Activity
DISCOUNT_TONGCHIDAO=Tongchidao Discount
DISCOUNT_FOLLOW_RED_PACKET=Follow Red Packet Discount
DISCOUNT_DOU_YIN_GROUPON=Douyin Voucher Verification
DISCOUNT_DA_ZHONG_DIAN_PIN=Dazhong Dianping Voucher Verification
DISCOUNT_DOU_YIN=Douyin Voucher Verification
DISCOUNT_ABC_GROUPON=Abc Voucher Verification

TAKEOUT_IMMEDIATE_DELIVERY=Immediate delivery
TAKEOUT_ONLINE_PAYMENT=Online payment has been made
TAKEOUT_MEAL_BOX_FEE=Meal box fee
TAKEOUT_DELIVERY_FEE=Delivery fee
TAKEOUT_ZC=Earn Meal Takeout
TAKEOUT_MT=Meituan Takeout
TAKEOUT_ELE=Ele.me Takeout
TAKEOUT_JD=Jd Takeout

operator_user=Operator: 
original_item_name=Original Item Name
item_name=Item Name
change_item_name=Change Item Name
change_time=Change Time: 
member_name_colon=Member name: 
member_phone_colon=Member phone: 

debt_repayment_invoice=Debt Repayment Invoice
debt_repayment_amount=Debt Repayment Amount
debt_repayment_unit_name=Debt Repayment Unit Name
debt_repayment_unit_contact_name=Debt Repayment Unit Contact Name
debt_repayment_unit_contact_tel=Debt Repayment Unit Contact Tel
debt_time=Debt Time
debt_repayment_total_fee=Debt Repayment Total Fee
debt_repayment_type=Debt Repayment Type
debt_repayment_time=Debt Repayment Time
debt_table_name=Debt Table Name
debt_member_name=Debt Member Name
debt_member_phone=Debt Member Phone
debt_member_money=Debt Member Money
assembled_meals=(Assembled Meals)
GENDER=Gender: 
BIRTHDAY=Birthday: 
REGISTER_TIME=Register Time: 
MEMBER_GRADE=Member Grade: 
CONSUME_COUNT=Consume Count: 
CONSUME_AMOUNT=Consume Amount: 
GUEST_SINGLE_PRICE=Guest Single Price: 
LAST_CONSUME_TIME=Last Consume Time: 
RECHARGE_COUNT=Recharge Count: 
RECHARGE_AMOUNT=Recharge Amount: 
AVERAGE_RECHARGE_AMOUNT=Average Recharge Amount: 
LAST_RECHARGE_TIME=Last Recharge Time: 

sale_refund_statistics_invoice_header=**REFUND STATISTICS**
dinein_part_refund_statistics=Partial Refund For Regular Dining
dinein_recovery_statistics=Anti Settlement For Regular Dining
fast_part_refund_statistics=Partial Refund For Fast Food
fast_recovery_statistics=Anti Settlement For Fast Food
refund_total__statistics=Total
refund_order_count=Number of refund transactions
refund_amount=Total refund amount

hand_over_traffic=Foot Traffic: 
hand_over_occupancy_rate_percent=Seating Rate: 
hand_over_open_table_tate_percent=Opening Rate: 
hand_over_flip_table_tate_percent=Overturning Rate: 
hand_over_avg_dine_in_time=Average Dining Time (Minutes): 
hand_over_sales_amount=Sales Amount (Completed Orders): 
hand_over_discount_amount=Total Discount Amount: 
hand_over_refund_amount=Total Refund Amount: 
hand_over_before_discount_per_order=Per Order Before Discount (Yuan): 
hand_over_after_discount_per_order=Per Order After Discount (Yuan): 
hand_over_before_discount_per_person=Per Person Before Discount (Yuan): 
hand_over_after_discount_per_person=Per Person After Discount (Yuan): 

occupancy_rate_percent=Seating Rate
open_table_tate_percent=Opening Rate
flip_table_tate_percent=Overturning Rate
avg_dine_in_time=Average Dining Time (Minutes)

business_overview_sale=[Business statistics]
business_overview_net_sale=[Composition of total net sales]
business_overview_discount=[Composition of total discount amount]
business_overview_groupon=[Group buying coupon statistics]
business_overview_estimated=[Expected composition of due amount]
total_net_sales_with_comment=Total net sales(Paid sales by customer for settled orders)
other_receipts=Other receipts
net_sales_other_receipts=Total net sales and other receipts
member_recharge=Member recharge
not_contains_member_recharge=Excluding the amount of 'member recharge'
coupons=coupons
groupon_name=Coupon Name
groupon_count=Number of coupons
groupon_discount=Coupon discounts
groupon_net_sales=Voucher actual payment

reservation_pay_invoice_header=**RESERVATION PAY**
reservation_pay_amount=Reservation pay amount:
payment_type=Payment type:
payment_time=Payment time:
reserve_start_time=Reservation time:
arrive_time=Meal time:
reserve_table_name=Table reservation:
reserve_number=Number of diners:
reserve_name=Reservation person:
gender_man=(Mr.)
gender_woman=(Ms.)
reserve_phone=Reserved phone number:

refund_time=refund time:
refund_only_amount=refund amount
refund_way=refund way
refund_reason=refund reason:
operation_time=operation time: