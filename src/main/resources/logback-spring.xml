<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <!-- 上下文名称 -->
    <contextName>SpringBootDemo</contextName>
    <property name="filePath" value="./logs"/>
    <property name="pattern" value="[%d{yyyy-MM-dd HH:mm:ss:SSS}][%-5p][%t][%C.%M:%L][%X{traceId}][%m] %n"/>
    <property name="projectName" value="holder-saas-store-table"/>
    <timestamp key="bySecond" datePattern="yyyyMMdd'T'HHmmss"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
    </appender>
    <!-- 滚动文件 -->
    <appender name="DEBUG"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${filePath}/debug.log</file>
        <!-- 当发生滚动时，决定RollingFileAppender的行为 TimeBasedRollingPolicy(根据时间来制定滚动策略) -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${filePath}/debug/%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <!-- 控制保留的归档文件的最大数量，超出数量就删除旧文件 -->
            <maxHistory>15</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>200MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!--<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">-->
        <!--<maxFileSize>1KB</maxFileSize>-->
        <!--</triggeringPolicy>-->
        <!-- 以上配置表示每天生成一个日志文件，保存30天的日志文件 -->
        <!--级别过滤器,根据日志级别进行过滤,如果日志级别等于配置级别,过滤器会根据onMath 和 onMismatch接收或拒绝日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>   <!-- 设置过滤级别 -->
            <!-- ACCEPT，日志会被立即处理，不再经过剩余过滤器 -->
            <onMatch>ACCEPT</onMatch>  <!-- 配置符合过滤条件的操作 -->
            <!-- DENY，日志将立即被抛弃不再经过其他过滤器 -->
            <onMismatch>DENY</onMismatch>  <!-- 配置不符合过滤条件的操作 -->
            <!-- NEUTRAL，有序列表里的下个过滤器过接着处理日志 -->
        </filter>
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>
    <appender name="INFO"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${filePath}/info.log</file>
        <!-- 当发生滚动时，决定RollingFileAppender的行为 TimeBasedRollingPolicy(根据时间来制定滚动策略) -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${filePath}/info/%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <!-- 控制保留的归档文件的最大数量，超出数量就删除旧文件 -->
            <maxHistory>15</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy  class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>200MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy >
        </rollingPolicy>
        <!-- 以上配置表示每天生成一个日志文件，保存30天的日志文件 -->
        <!--级别过滤器,根据日志级别进行过滤,如果日志级别等于配置级别,过滤器会根据onMath 和 onMismatch接收或拒绝日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>   <!-- 设置过滤级别 -->
            <!-- ACCEPT，日志会被立即处理，不再经过剩余过滤器 -->
            <onMatch>ACCEPT</onMatch>  <!-- 配置符合过滤条件的操作 -->
            <!-- DENY，日志将立即被抛弃不再经过其他过滤器 -->
            <onMismatch>DENY</onMismatch>  <!-- 配置不符合过滤条件的操作 -->
            <!-- NEUTRAL，有序列表里的下个过滤器过接着处理日志 -->
        </filter>
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>
    <appender name="WARN"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${filePath}/warn.log</file>
        <!-- 当发生滚动时，决定RollingFileAppender的行为 TimeBasedRollingPolicy(根据时间来制定滚动策略) -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${filePath}/warn/%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <!-- 控制保留的归档文件的最大数量，超出数量就删除旧文件 -->
            <maxHistory>15</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>200MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!--<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">-->
        <!--<maxFileSize>50MB</maxFileSize>-->
        <!--</triggeringPolicy>-->
        <!-- 以上配置表示每天生成一个日志文件，保存30天的日志文件 -->
        <!--级别过滤器,根据日志级别进行过滤,如果日志级别等于配置级别,过滤器会根据onMath 和 onMismatch接收或拒绝日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>   <!-- 设置过滤级别 -->
            <!-- ACCEPT，日志会被立即处理，不再经过剩余过滤器 -->
            <onMatch>ACCEPT</onMatch>  <!-- 配置符合过滤条件的操作 -->
            <!-- DENY，日志将立即被抛弃不再经过其他过滤器 -->
            <onMismatch>DENY</onMismatch>  <!-- 配置不符合过滤条件的操作 -->
            <!-- NEUTRAL，有序列表里的下个过滤器过接着处理日志 -->
        </filter>
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>
    <appender name="ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${filePath}/error.log</file>
        <!-- 当发生滚动时，决定RollingFileAppender的行为 TimeBasedRollingPolicy(根据时间来制定滚动策略) -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${filePath}/error/%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <!-- 控制保留的归档文件的最大数量，超出数量就删除旧文件 -->
            <maxHistory>15</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>200MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!--<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">-->
        <!--<maxFileSize>50MB</maxFileSize>-->
        <!--</triggeringPolicy>-->
        <!-- 以上配置表示每天生成一个日志文件，保存30天的日志文件 -->
        <!--级别过滤器,根据日志级别进行过滤,如果日志级别等于配置级别,过滤器会根据onMath 和 onMismatch接收或拒绝日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>   <!-- 设置过滤级别 -->
            <!-- ACCEPT，日志会被立即处理，不再经过剩余过滤器 -->
            <onMatch>ACCEPT</onMatch>  <!-- 配置符合过滤条件的操作 -->
            <!-- DENY，日志将立即被抛弃不再经过其他过滤器 -->
            <onMismatch>DENY</onMismatch>  <!-- 配置不符合过滤条件的操作 -->
            <!-- NEUTRAL，有序列表里的下个过滤器过接着处理日志 -->
        </filter>
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>

    <logger name="com.holderzone.holder.saas.store.table.mapper" level="debug"/>

    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="WARN"/>
        <appender-ref ref="ERROR"/>
    </root>
</configuration>