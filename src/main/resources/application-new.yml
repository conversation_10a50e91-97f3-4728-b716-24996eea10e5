test:
  hostname: **************
  eureka:
    hostname: ***************
  redis:
    hostname: ${test.hostname}
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://***************:8141/eureka/
spring:
  application:
    name: holder-saas-store-queue
  jackson:
    default-property-inclusion: non_null
  zipkin:
    base-url: http://**************:9411/
    service:
      name: holder-saas-store-queue
    sender:
      type: web
    enabled: true
  sleuth:
    sampler:
      probability: 1.0
    enabled: true
  rocketmq:
    namesrv-addr: ***************:9876
    producer-group-name: MerchantOrganizationProducer
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000
redisson:
  address: redis://**************:36380
  password: eIx6TynJq
  database: 0
  single:
    enabled: true