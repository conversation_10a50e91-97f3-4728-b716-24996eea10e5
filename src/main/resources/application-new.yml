ref:
  eureka:
    hostname: ***************
    port: 8141
  zipkin:
    hostname: **************
    port: 9411

eureka:
  client:
    service-url:
      defaultZone: http://${ref.eureka.hostname}:${ref.eureka.port}/eureka
  instance:
      lease-renewal-interval-in-seconds: 5
      lease-expiration-duration-in-seconds: 10
      prefer-ip-address: true
      instance-id: ${spring.cloud.client.ip-address}:${server.port}

spring:
  application:
    name: holder-saas-store-print
    group: com.holder.print.dev
  zipkin:
    base-url: http://${ref.zipkin.hostname}:${ref.zipkin.port}/
    service:
      name: holder-saas-store-print
    sender:
      type: web
    enabled: true
  sleuth:
    sampler:
      probability: 1.0
    enabled: true
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  jackson:
    default-property-inclusion: non_null
  rocketmq:
    namesrv-addr: ***************:9876
    producer-group-name: printProducer
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120

server:
  port: 8917
  undertow:
    max-http-post-size: 0
    # 设置 IO 线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个 CPU 核 心一个线程,数量和 CPU 内核数目一样即可
    io-threads: 4
    # 阻塞任务线程池, 当执行类似 servlet 请求阻塞操作, undertow 会从这个线程池中取得 线程,它的值设置取决于系统的负载  io-threads*8
    worker-threads: 32
    # 以下的配置会影响 buffer,这些 buffer 会用于服务器连接的 IO 操作,有点类似 netty 的 池化内存管理
    # 每块 buffer 的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 每个区分配的 buffer 数量 , 所以 pool 的大小是 buffer-size * buffers-per-region
    #buffers-per-region: 1024 # 这个参数不需要写了
    # 是否分配的直接内存
    direct-buffers: true

alibaba:
  acm:
    accessKey: LTAItOsX8t1aGONM
    secretKey: S3fjKGfssXqb2vJcZpZCSPwrSMSw4U
    namespace: 52a12a5d-1982-47d9-b7af-716e2b380ca1
    refresh:
      enabled: true
    time-out: 6000
    group: ${spring.application.group}
    endpoint: acm.aliyun.com

framework:
  log:
    service-url: http://base-service

feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 300000
    max-connections: 500
    time-to-live: 30
ribbon:
  ConnectionTimeout: 100000
  ReadTimeout: 100000
#熔断配置
hystrix:
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      execution:
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 2000
          thread:
            timeoutInMilliseconds: 15000
        timeout:
          enable: true
  threadpool:
    default:
      coreSize: 50
      maximumSize: 500
      maxQueueSize: 500
      queueSizeRejectionThreshold: 300
      keepAliveTimeMinutes: 10
      allowMaximumSizeToDivergeFromCoreSize: true
      metrics:
        rollingStats:
          numBuckets: 500

# 动态数据源
dynamic:
  intercept:
    datasource:
      include-url: /**
      exclude-url:
    #添加需要切换 redis 数据源的接口路径
    redis:
      include-url: /**
  redis:
    enabled: true
  server:
    #如果本服务需要对表分库，那么需要配置本服务的所有表名,以逗号隔开
    #sharding-tables: person
    #必须制定当前服务的名称
    server-code: holder_saas_store_print

#自定义配置：用来判断是否执行手动切换数据源的方法
self:
  open-dynamic-datasource: true

#logging:
#  level:
#    com.holder.saas.print: debug

mybatis-plus:
  mapper-locations: classpath*:/mapper/**Mapper.xml
  typeAliasesPackage: com.holder.saas.print.entity.domain
  configuration:
    map-underscore-to-camel-case: true
    aggressive-lazy-loading: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    cache-enabled: false
    call-setters-on-nulls: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    refresh: false
    db-config:
      db-type: mysql
      id-type: auto
      field-strategy: not_empty
      logic-delete-value: 1
      logic-not-delete-value: 0

batch-push:
  enable: false

template:
  enable: true

