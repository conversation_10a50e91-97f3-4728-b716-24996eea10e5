<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>
    <classPathEntry
            location="D:\DevSoft\maven\repository\mysql\mysql-connector-java\8.0.19\mysql-connector-java-8.0.19.jar"/>
    <context id="context1">
        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>
        <!-- 数据库链接URL、用户名、密码 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="jdbc:mysql://***************:3307/hse_erp_db?serverTimezone=UTC" userId="root"
                        password="mysqlHolder">
            <!-- <jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver" connectionURL="*************************************" userId="msa" password="msa"> -->
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!-- 生成模型的包名和位置 -->
        <javaModelGenerator targetPackage="com.holderzone.erp.entity.domain" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!-- 生成的映射文件包名和位置 -->
        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!-- 生成DAO的包名和位置 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.holderzone.erp.entity.dao" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
        <!-- 要生成那些表(更改tableName和domainObjectName就可以) -->
        <table tableName="hse_goods_bom" domainObjectName="GoodsBomDO" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true"
               selectByExampleQueryId="false"
               selectByPrimaryKeyQueryId="false" enableSelectByPrimaryKey="false" enableDeleteByPrimaryKey="false"
               enableUpdateByPrimaryKey="false">
            <ignoreColumn column="id"/>
        </table>
        <!--<table tableName="hse_material" domainObjectName="MaterialDO" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true"
               selectByExampleQueryId="false"
               selectByPrimaryKeyQueryId="false" enableSelectByPrimaryKey="false" enableDeleteByPrimaryKey="false"
               enableUpdateByPrimaryKey="false">
            <ignoreColumn column="id"/>
        </table>-->
       <!-- <table tableName="hse_material_category" domainObjectName="MaterialCategoryDO" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true"
               selectByExampleQueryId="false"
               selectByPrimaryKeyQueryId="false" enableSelectByPrimaryKey="false" enableDeleteByPrimaryKey="false"
               enableUpdateByPrimaryKey="false">
            <ignoreColumn column="id"/>
        </table>-->
        <!--<table tableName="hse_warehouse_check_document" domainObjectName="WarehouseCheckDocumentDO"
               mapperName="WarehouseCheckDocumentMapper" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="false"
               selectByPrimaryKeyQueryId="false" enableSelectByPrimaryKey="false" enableDeleteByPrimaryKey="false"
               enableUpdateByPrimaryKey="false">
        </table>
        <table tableName="hse_warehouse_check_document_detail" domainObjectName="WarehouseCheckDocumentDetailDO"
               mapperName="WarehouseCheckDocumentDetailMapper" enableCountByExample="true"
            enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="false"
            selectByPrimaryKeyQueryId="false" enableSelectByPrimaryKey="false" enableDeleteByPrimaryKey="false"
            enableUpdateByPrimaryKey="false">
        </table>
        <table tableName="hse_warehouse_in_out_document" domainObjectName="WarehouseInOutDocumentDO"
               mapperName="WarehouseInOutDocumentMapper" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="false"
               selectByPrimaryKeyQueryId="false" enableSelectByPrimaryKey="false" enableDeleteByPrimaryKey="false"
               enableUpdateByPrimaryKey="false">
        </table>
        <table tableName="hse_warehouse_in_out_document_detail" domainObjectName="WarehouseInOutDocumentDetailDO"
               mapperName="WarehouseInOutDocumentDetailMapper" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="false"
               selectByPrimaryKeyQueryId="false" enableSelectByPrimaryKey="false" enableDeleteByPrimaryKey="false"
               enableUpdateByPrimaryKey="false">
        </table>-->
        <!--<table tableName="hsu_menu"  domainObjectName="MenuDO" enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true"/>-->
        <!--<table tableName="hsu_role"  domainObjectName="RoleDO" enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true"/>-->
        <!--<table tableName="hsu_r_user_role"  domainObjectName="UserRoleDO" enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true"/>-->
        <!--<table tableName="hsu_role_resource"  domainObjectName="RoleResourceDO" enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true"/>-->
        <!-- <table tableName="region_map"  domainObjectName="RegionDO" enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true"/>-->
    </context>
</generatorConfiguration>