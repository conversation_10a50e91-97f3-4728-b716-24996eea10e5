-- 没有更新的sql --
ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_attr`
  ADD COLUMN `origin_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '源属性值guid, 当此属性是被哪个属性推送来的 ' AFTER `is_default`,
  ADD COLUMN `attr_from` int(10) NOT NULL COMMENT '属性值来源：0/门店自建，1/品牌自建，2被推送' AFTER `origin_guid`;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_attr_group`
  MODIFY COLUMN `attr_group_from` int(10) UNSIGNED NOT NULL COMMENT '属性组来源（0：门店自己创建的属性组，1：品牌自己创建的属性组,2:被推送过来的属性组）' AFTER `name_change`,
  ADD COLUMN `origin_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '源属性组guid，被哪个属性组推送的属性组guid' AFTER `attr_group_from`;


ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_item`
CHANGE COLUMN `has_attr` `has_attr` INT(10) UNSIGNED NOT NULL COMMENT '属性组状态:0：无属性; 1:有属性; 2:有必选属性组(如果是套餐，则该字段=0)' ;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_r_sku_subgroup`
CHANGE COLUMN `num` `item_num` DECIMAL(12,3) UNSIGNED NOT NULL COMMENT '每份子菜规格所含数量' ;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_item`
CHANGE COLUMN `name_abbr` `name_abbr` VARCHAR(32) NULL COMMENT '商品名称简写' ;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_item`
CHANGE COLUMN `name` `name` VARCHAR(50) NOT NULL COMMENT '名称' ,
CHANGE COLUMN `pinyin` `pinyin` VARCHAR(50) NOT NULL COMMENT '拼音简码' ;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_attr`
CHANGE COLUMN `origin_guid` `parent_guid` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '源属性值guid, 当此属性是被哪个属性推送来的 ' ,
ADD COLUMN `store_guid` VARCHAR(50) NULL COMMENT '门店GUID' AFTER `attr_from`,
ADD COLUMN `brand_guid` VARCHAR(50) NULL COMMENT '品牌GUID' AFTER `store_guid`;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_attr_group`
CHANGE COLUMN `origin_guid` `parent_guid` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '源属性组guid，被哪个属性组推送的属性组guid' ;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_sku`
ADD COLUMN `store_guid` VARCHAR(50) NULL COMMENT '门店GUID' AFTER `elm_sku`,
ADD COLUMN `brand_guid` VARCHAR(50) NULL COMMENT '品牌GUID' AFTER `store_guid`,
ADD COLUMN `parent_guid` VARCHAR(50) NULL COMMENT '父SKUGUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的商品，则该字段为商品库对应的SKUGUID。' AFTER `brand_guid`;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_subgroup`
ADD COLUMN `store_guid` VARCHAR(50) NULL COMMENT '门店GUID' AFTER `sort`,
ADD COLUMN `brand_guid` VARCHAR(50) NULL COMMENT '品牌GUID' AFTER `store_guid`,
ADD COLUMN `parent_guid` VARCHAR(50) NULL COMMENT '父分组GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的分组，则该字段为商品库对应的分组GUID。' AFTER `brand_guid`;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_sku`
ADD COLUMN `sku_from` INT UNSIGNED NULL COMMENT 'sku来源（0：门店自己创建的sku，1：品牌自己创建的sku,2:被推送过来的sku）' AFTER `parent_guid`;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_subgroup`
ADD COLUMN `subgroup_from` INT UNSIGNED NULL COMMENT 'sUBGROUP来源（0：门店自己创建的subgroup，1：品牌自己创建的subgroup,2:被推送过来的subgroup）' AFTER `parent_guid`;

ALTER TABLE `hsi_item_6491846954039715841_db`.`hsi_r_sku_subgroup`
DROP COLUMN `is_required`;

// 20190308
ALTER TABLE `hsi_item_6506431195651982337_db`.`hsi_subgroup`
CHANGE COLUMN `name` `name` VARCHAR(20) NOT NULL COMMENT '分组名称' ,
CHANGE COLUMN `subgroup_from` `subgroup_from` INT(10) UNSIGNED NOT NULL COMMENT 'sUBGROUP来源（0：门店自己创建的subgroup，1：品牌自己创建的subgroup,2:被推送过来的subgroup）' ;

ALTER TABLE `hsi_item_6506431195651982337_db`.`hsi_item`
CHANGE COLUMN `is_new` `is_new` INT(10) UNSIGNED NOT NULL COMMENT '是否是新品（0：否，1：是）' ,
CHANGE COLUMN `is_bestseller` `is_bestseller` INT(10) UNSIGNED NOT NULL COMMENT '是否热销：0：否，1：是' ,
CHANGE COLUMN `is_sign` `is_sign` INT(10) UNSIGNED NOT NULL COMMENT '是否是招牌：0：否，1：是' ;

ALTER TABLE `hsi_item_6506431195651982337_db`.`hsi_item`
CHANGE COLUMN `name_abbr` `name_abbr` VARCHAR(50) NULL DEFAULT NULL COMMENT '商品名称简写' ;
