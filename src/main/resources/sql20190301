-- <PERSON><PERSON><PERSON> Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='TRADITIONAL,ALLOW_INVALID_DATES';

-- -----------------------------------------------------
-- Schema hsi_item_6491846954039715841_db
-- -----------------------------------------------------
DROP SCHEMA IF EXISTS `hsi_item_6491846954039715841_db` ;

-- -----------------------------------------------------
-- Schema hsi_item_6491846954039715841_db
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `hsi_item_6491846954039715841_db` DEFAULT CHARACTER SET utf8mb4 ;
USE `hsi_item_6491846954039715841_db` ;

-- -----------------------------------------------------
-- Table `hsi_attr`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsi_attr` ;

CREATE TABLE IF NOT EXISTS `hsi_attr` (
  `id` BIGINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `guid` VARCHAR(50) NOT NULL,
  `attr_group_guid` VARCHAR(50) NOT NULL,
  `name` VARCHAR(50) NOT NULL COMMENT '属性名称',
  `price` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '加价',
  `is_default` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否是默认选中项（0：否，1：是）',
  `parent_guid` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '源属性值guid, 当此属性是被哪个属性推送来的 ',
  `attr_from` INT(10) NOT NULL COMMENT '属性值来源：0/门店自建，1/品牌自建，2被推送',
  `store_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '门店GUID',
  `brand_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '品牌GUID',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_attr_group_guid` (`attr_group_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 94
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '属性值（属性组下的属性内容）';


-- -----------------------------------------------------
-- Table `hsi_attr_group`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsi_attr_group` ;

CREATE TABLE IF NOT EXISTS `hsi_attr_group` (
  `id` BIGINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `guid` VARCHAR(50) NOT NULL,
  `brand_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '所属品牌GUID',
  `store_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '所属门店GUID',
  `name` VARCHAR(50) NOT NULL COMMENT '名称',
  `sort` INT(10) NOT NULL COMMENT '排序',
  `description` VARCHAR(100) NULL DEFAULT NULL COMMENT '描述',
  `is_enable` INT(10) UNSIGNED NOT NULL COMMENT '是否启用（0：否，1：是）',
  `icon_url` VARCHAR(255) NULL DEFAULT NULL COMMENT '图标路径',
  `is_required` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否必选:0 否 1 是',
  `is_multi_choice` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否支持多选:0 否 1 是',
  `with_default` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否有默认选项：0：否1：是',
  `name_change` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '名称是否因为重名而修改为（自建）。0：否，1：是。',
  `attr_group_from` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '属性组来源（0：门店自己创建的属性组，1：品牌自己创建的属性组,2:被推送过来的属性组）',
  `parent_guid` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '源属性组guid，被哪个属性组推送的属性组guid',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_brand_guid` (`brand_guid` ASC),
  INDEX `idx_store_guid` (`store_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 103
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '属性组表';


-- -----------------------------------------------------
-- Table `hsi_item`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsi_item` ;

CREATE TABLE IF NOT EXISTS `hsi_item` (
  `id` BIGINT(8) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `guid` VARCHAR(50) NOT NULL COMMENT '商品GUID',
  `type_guid` VARCHAR(50) NOT NULL COMMENT '商品关联的分类GUID',
  `store_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '门店GUID',
  `brand_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '品牌GUID',
  `item_from` INT(10) UNSIGNED NOT NULL COMMENT '商品来源（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）',
  `item_type` INT(10) UNSIGNED NOT NULL COMMENT '商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品。',
  `is_sold_out` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '是否售罄:0 否 1 是',
  `has_attr` INT(10) UNSIGNED NOT NULL COMMENT '属性组状态:0：无属性; 1:有属性; 2:有必选属性组(如果是套餐，则该字段=0)',
  `name` VARCHAR(50) NOT NULL COMMENT '名称',
  `pinyin` VARCHAR(50) NOT NULL COMMENT '拼音简码',
  `name_abbr` VARCHAR(32) NULL DEFAULT NULL COMMENT '商品名称简写',
  `description` VARCHAR(3003) NULL DEFAULT NULL COMMENT '商品描述',
  `picture_url` VARCHAR(260) NULL DEFAULT NULL COMMENT '图片路径数组json',
  `sort` INT(10) UNSIGNED NOT NULL COMMENT '排序',
  `parent_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '父商品GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的商品，则该字段为原商品GUID。',
  `in_order` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '是否产生订单：0：否，1：是。',
  `is_new` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '是否是新品（0：否，1：是）',
  `is_bestseller` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '是否热销：0：否，1：是',
  `is_sign` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '是否是招牌：0：否，1：是',
  `name_change` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '名称是否因为重名而修改为（自建）。0：否，1：是。',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_type_guid` (`type_guid` ASC),
  INDEX `idx_store_guid` (`store_guid` ASC),
  INDEX `idx_brand_guid` (`brand_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 177
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '商品表';


-- -----------------------------------------------------
-- Table `hsi_r_attr_item_attr_group`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsi_r_attr_item_attr_group` ;

CREATE TABLE IF NOT EXISTS `hsi_r_attr_item_attr_group` (
  `id` BIGINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `guid` VARCHAR(50) NOT NULL,
  `attr_guid` VARCHAR(50) NOT NULL COMMENT '属性值GUID',
  `item_attr_group_guid` VARCHAR(50) NOT NULL COMMENT '商品表与属性组关联表GUID',
  `is_default` INT(10) UNSIGNED NOT NULL COMMENT '是否是默认选中项（0：否，1：是）',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_item_attr_group_guid` (`item_attr_group_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 415
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '属性值与商品属性组关联表的关联表';


-- -----------------------------------------------------
-- Table `hsi_r_item_attr_group`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsi_r_item_attr_group` ;

CREATE TABLE IF NOT EXISTS `hsi_r_item_attr_group` (
  `id` BIGINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `guid` VARCHAR(50) NOT NULL,
  `item_guid` VARCHAR(50) NOT NULL COMMENT '商品GUID',
  `attr_group_guid` VARCHAR(50) NOT NULL COMMENT '属性组GUID',
  `is_required` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否必选:0 否 1 是',
  `is_multi_choice` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否支持多选:0 否 1 是',
  `with_default` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否有默认选项：0：否1：是',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_item_guid` (`item_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 196
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '商品与属性组关联表';


-- -----------------------------------------------------
-- Table `hsi_r_sku_subgroup`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsi_r_sku_subgroup` ;

CREATE TABLE IF NOT EXISTS `hsi_r_sku_subgroup` (
  `id` BIGINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `guid` VARCHAR(50) NOT NULL,
  `subgroup_guid` VARCHAR(50) NOT NULL COMMENT '分组GUID',
  `sku_guid` VARCHAR(50) NOT NULL COMMENT '规格GUID',
  `item_guid` VARCHAR(50) NOT NULL COMMENT '冗余的商品GUID',
  `item_num` DECIMAL(12,3) UNSIGNED NOT NULL COMMENT '每份子菜规格所含数量',
  `sort` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '该规格在分组内的排序',
  `add_price` DECIMAL(12,3) NOT NULL COMMENT '商品加价',
  `is_default` INT(10) UNSIGNED NOT NULL COMMENT '是否默认勾选，1：是，0,否',
  `is_repeat` INT(10) UNSIGNED NOT NULL COMMENT '是否可重复选择，0:否,1:是',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_subgroup_guid` (`subgroup_guid` ASC),
  INDEX `idx_item_guid` (`item_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 118
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '规格与分组关联表（规格为套餐分组中的子菜规格）';


-- -----------------------------------------------------
-- Table `hsi_r_type_attr`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsi_r_type_attr` ;

CREATE TABLE IF NOT EXISTS `hsi_r_type_attr` (
  `id` BIGINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `guid` VARCHAR(50) NOT NULL,
  `type_guid` VARCHAR(50) NOT NULL COMMENT '分类GUID',
  `attr_guid` VARCHAR(50) NOT NULL COMMENT '属性值的GUID',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_attr_guid` (`attr_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 68
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '分类与属性值关联表';


-- -----------------------------------------------------
-- Table `hsi_sku`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsi_sku` ;

CREATE TABLE IF NOT EXISTS `hsi_sku` (
  `id` BIGINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `guid` VARCHAR(50) NOT NULL,
  `item_guid` VARCHAR(50) NOT NULL COMMENT '商品GUID',
  `upc` VARCHAR(50) NULL DEFAULT NULL COMMENT 'upc商品条码',
  `stock` DECIMAL(12,3) NULL DEFAULT NULL COMMENT '当日库存',
  `total_stock` DECIMAL(12,3) NULL DEFAULT NULL COMMENT '总库存',
  `name` VARCHAR(45) NOT NULL DEFAULT '' COMMENT '规格名称(固定规格名称为空字符串\"\")',
  `code` VARCHAR(20) NULL DEFAULT NULL COMMENT '编号',
  `sale_price` DECIMAL(10,2) NOT NULL COMMENT '售卖价格',
  `unit` VARCHAR(12) NOT NULL COMMENT '商品规格单位',
  `unit_code` INT(10) NULL DEFAULT NULL COMMENT '称重单位对应的编号',
  `min_order_num` DECIMAL(12,3) NOT NULL COMMENT '起卖数(非称重即为整数，称重即为小数)',
  `is_member_discount` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否参与会员折扣（0：否，1：是）',
  `is_whole_discount` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否加入整单折扣(0：否，1：是)',
  `is_rack` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否上架(0：否，1：是)',
  `is_join_we_chat` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否参与微信点餐（0：否，1：是）',
  `is_join_mt` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否参与美团外卖（0：否，1：是）',
  `is_join_elm` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否参与饿了么外卖（0：否，1：是）',
  `mt_sku` VARCHAR(50) NULL DEFAULT NULL COMMENT '美团sku',
  `elm_sku` VARCHAR(50) NULL DEFAULT NULL COMMENT '饿了么sku',
  `store_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '门店GUID',
  `brand_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '品牌GUID',
  `parent_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '父SKUGUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的商品，则该字段为商品库对应的SKUGUID。',
  `sku_from` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT 'sku来源（0：门店自己创建的sku，1：品牌自己创建的sku,2:被推送过来的sku）',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_item_guid` (`item_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 208
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '商品规格表';


-- -----------------------------------------------------
-- Table `hsi_subgroup`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsi_subgroup` ;

CREATE TABLE IF NOT EXISTS `hsi_subgroup` (
  `id` BIGINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `guid` VARCHAR(50) NOT NULL,
  `item_guid` VARCHAR(50) NOT NULL,
  `name` VARCHAR(20) NULL DEFAULT NULL COMMENT '分组名称',
  `pick_num` INT(10) UNSIGNED NOT NULL COMMENT '选择商品数量:0:为固定套餐',
  `sort` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '该分组在套餐中的排序',
  `store_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '门店GUID',
  `brand_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '品牌GUID',
  `parent_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '父分组GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的分组，则该字段为商品库对应的分组GUID。',
  `subgroup_from` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT 'sUBGROUP来源（0：门店自己创建的subgroup，1：品牌自己创建的subgroup,2:被推送过来的subgroup）',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_item_guid` (`item_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 61
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '套餐的分组';


-- -----------------------------------------------------
-- Table `hsi_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsi_type` ;

CREATE TABLE IF NOT EXISTS `hsi_type` (
  `id` BIGINT(8) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `guid` VARCHAR(50) NOT NULL,
  `brand_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '品牌GUID',
  `store_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '关联的门店GUID',
  `name` VARCHAR(45) NOT NULL COMMENT '名称',
  `sort` INT(10) UNSIGNED NOT NULL COMMENT '排序',
  `is_enable` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否启用（0：否，1：是）',
  `description` VARCHAR(50) NULL DEFAULT NULL COMMENT '分类描述',
  `icon_url` VARCHAR(255) NULL DEFAULT NULL COMMENT '图标',
  `item_num` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '关联该分类的商品数',
  `parent_guid` VARCHAR(50) NULL DEFAULT NULL COMMENT '被推送过来的分类的GUID',
  `name_change` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '名称是否因为重名而修改为（自建）。0：否，1：是。',
  `type_from` INT(10) UNSIGNED NOT NULL DEFAULT '1' COMMENT '分类来源（0：门店自己创建的分类，1：品牌自己创建的分类,2:被推送过来的分类）',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_brand_guid` (`brand_guid` ASC),
  INDEX `idx_store_guid` (`store_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 149
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '商品分类';


SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
