val:
  eureka:
    host: ***************
    port: 8141
  rocketmq:
    host: ***************
    port: 9876
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${val.eureka.host}:${val.eureka.port}/eureka/
spring:
  zipkin:
    base-url: http://***************:9411/
    service:
      name: holder-saas-store-kds
    sender:
      type: web
    enabled: true
  rocketmq:
    namesrv-addr: ${val.rocketmq.host}:${val.rocketmq.port}
    producer-group-name: StoreKdsProducer
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always