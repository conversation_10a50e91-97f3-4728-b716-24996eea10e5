#默认路由地址
spring:
  application:
    name: holder-saas-gateway
  redis:
    host: **************
    port: 36379
    password: eIx6TynJq
    database: 15
  cloud:
    gateway:
      elapsedTime:
        business: true
        gateway: true
      authen: true
      author: true
      offline: true
      sensitive: true
      basic-auth: Basic YWRtaW46cHVibGlj
      interceptor: /whiteList/.*,/actuator/gateway/.*
      routes:
      - id: app-sso-route
        uri: lb://holder-saas-sso
        predicates:
        - Path=/app/sso/**
        filters:
          - RewritePath=/sso/(?<segment>.*), /$\{segment}
      - id: app-route
        uri: lb://holder-saas-aggregation-app
        predicates:
        - Path=/app/**
      - id: merchant-route
        uri: lb://holder-saas-aggregation-merchant
        predicates:
        - Path=/merchant/**
        filters:
        - name: RequestRateLimiter
          args:
            key-resolver: '#{@remoteAddressKeyResolver}'
            redis-rate-limiter.replenishRate: 30
            redis-rate-limiter.burstCapacity: 30
      - id: cloud-route
        uri: lb://holder-saas-cloud-aggregation
        predicates:
        - Path=/cloud/**
      - id: jhCallBack-route
        uri: lb://holder-saas-store-trading-center
        predicates:
        - Path=/jh/pay/notify
      - id: sso-route
        uri: lb://holder-saas-sso
        predicates:
        - Path=/sso/**
      - id: weixin-route
        uri: lb://holder-saas-aggregation-weixin
        predicates:
        - Path=/weixin/**
      - id: weixin-normal-route
        uri: lb:ws://holder-saas-aggregation-weixin
        predicates:
        - Path=/weixin-normal/**
      - id: phoneapp-route
        uri: lb://holder-saas-aggregation-phoneapp
        predicates:
        - Path=/phoneapp/**
      - id: huawei-route
        uri: lb://holder-saas-store-hw
        predicates:
        - Path=/huawei/**
      - id: merchant-member-route
        uri: lb://holder-saas-member-aggregation-merchant
        predicates:
        - Path=/merchantMember/**
      - id: merchant-member-msm-route
        uri: lb://holder-saas-member-account-msm
        predicates:
        - Path=/merchantMemberMsm/**
      - id: mdm-store-route
        uri: lb://holder-saas-store-mdm
        predicates:
        - Path=/mdm_store/**
      - id: kds-sso-route
        uri: lb://holder-saas-sso
        predicates:
          - Path=/kds/sso/**
        filters:
          - RewritePath=/sso/(?<segment>.*), /$\{segment}
      - id: kds-route
        uri: lb://holder-saas-aggregation-kds
        predicates:
          - Path=/kds/**
      - id: member-wechat-route
        uri: lb://holder-saas-member-wechat
        predicates:
          - Path=/member-wechat/**
      - id: mid-gateway-route
        uri: http://**************:8087
        predicates:
          - Path=/mid/**
#放开网关动态配置接口
management:
  endpoints:
    web:
      exposure:
        include: "*"
server:
  port: 8151


#注册中心
eureka:
  instance:
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
  client:
    serviceUrl:
      defaultZone: http://localhost:8141/eureka/
#      defaultZone: http://localhost:8141/eureka/

ribbon:
  ConnectionTimeout: 100000
  ReadTimeout: 100000

switch:
  offline:
    enabled: true
    gray:
      include: all
      exclude: