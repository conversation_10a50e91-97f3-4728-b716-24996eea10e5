val:
  eureka:
    host: ***************
    port: 8141
  rocketmq:
    host: ***************
    port: 9876
spring:
  application:
    name: holder-saas-store-message
  zipkin:
    base-url: http://***************:9411/
    service:
      name: holder-saas-store-message
    sender:
      type: web
    enabled: true
  sleuth:
    sampler:
      probability: 1.0
    enabled: true
  #  redis:
  #    database: 0
  #    host: ***************
  #    port: 6379
  #    password: eIx6TynJq
  #    timeout: 5000ms
  #    jedis:
  #      pool:
  #        max-active: 50
  #        max-idle: 50
  #        min-idle: 20
  #        max-wait: -1ms
  rocketmq:
    namesrv-addr: ${val.rocketmq.host}:${val.rocketmq.port}
    producer-group-name: businessMessageProducer
server:
  port: 8904
  tomcat:
    accept-count: 200
    max-connections: 5000
    max-threads: 450
# feign配置，使用appach client 替代默认urlConnection
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000
eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${val.eureka.host}:${val.eureka.port}/eureka/

#自定义配置：用来判断是否执行手动切换数据源的方法
self:
  open-dynamic-datasource: true

# 动态数据源
dynamic:
  intercept:
    datasource:
      include-url: /**
      exclude-url:
    #添加需要切换 redis 数据源的接口路径
    redis:
      include-url: /**
  redis:
    enabled: true
  server:
    #如果本服务需要对表分库，那么需要配置本服务的所有表名,以逗号隔开
    #sharding-tables: person
    #必须制定当前服务的名称
    server-code: holder_saas_store_message
  #结合 mybatis
  mybatis:
    enabled: true
    base-package: com.holderzone.holder.saas.store.message.mapper
    mapper-locations: classpath:mapper/*.xml
    type-aliases-package: com.holderzone.holder.saas.store.message.domain
