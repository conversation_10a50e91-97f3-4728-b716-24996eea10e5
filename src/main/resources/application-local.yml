val:
  eureka:
    host: ***************
    port: 8141

eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    metadata-map:
      cluster: default
  client:
    serviceUrl:
      defaultZone: http://${val.eureka.host}:${val.eureka.port}/eureka/

spring:
  application:
    name: holder-saas-store-deposit
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
#  datasource:
#    url: **********************************************************************************************************************************
#    password: 123456
#    username: root
##    host: **************
#    host: 127.0.0.1
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    type: com.alibaba.druid.pool.DruidDataSource
#    druid:
#      connection-error-retry-attempts: 2
#      break-after-acquire-failure: true
#      min-idle: 10
#      max-active: 500
#      max-wait: 60000
#      initial-size: 10

dynamic:
  intercept:
    datasource:
      include-url: /**
      exclude-url:
    #添加需要切换 redis 数据源的接口路径
    redis:
      include-url: /**
  redis:
    enabled: true
  server:
    server-code: holder_saas_store_erp

mybatis-plus:
  mapper-locations: classpath*:/mapper/**Mapper.xml
  typeAliasesPackage: com.holderzone.holder.saas.store.deposit.entity
  configuration:
    map-underscore-to-camel-case: true
    aggressive-lazy-loading: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    cache-enabled: false
    call-setters-on-nulls: false
  global-config:
    refresh: false
    db-config:
      db-type: mysql
      id-type: auto
      field-strategy: not_empty
      logic-delete-value: 1
      logic-not-delete-value: 0

mybatis-page:
  enable: false

feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 300000
    max-connections: 500
    time-to-live: 30
ribbon:
  ConnectionTimeout: 100000
  ReadTimeout: 100000
#熔断配置
hystrix:
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      execution:
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 2000
          thread:
            timeoutInMilliseconds: 15000
        timeout:
          enable: true
  threadpool:
    default:
      coreSize: 50
      maximumSize: 500
      maxQueueSize: 500
      queueSizeRejectionThreshold: 300
      keepAliveTimeMinutes: 10
      allowMaximumSizeToDivergeFromCoreSize: true
      metrics:
        rollingStats:
          numBuckets: 500