val:
  eureka:
    host: ***************
    port: 8141
  redis:
    host: **************
    port: 36380
  rocketmq:
    host: ***************
    port: 9876
  mysql:
    host: ***************
    port: 3306
ref:
  hostname: ***************

server:
  port: 8905
  undertow:
    max-http-post-size: 0
    io-threads: 4
    worker-threads: 32
    buffer-size: 1024
    direct-buffers: true

spring:
  application:
    name: holder-saas-store-config
  datasource:
    url: jdbc:mysql://${val.mysql.host}:${val.mysql.port}/hsc_common_db?autoReconnect=true&failOverReadOnly=false&useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai
    password: mysqlHolder
    username: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      connection-error-retry-attempts: 2
      break-after-acquire-failure: true
      min-idle: 10
      max-active: 500
      max-wait: 60000
      initial-size: 10
  redis:
    host: ${val.redis.host}
    database: 1
    password: eIx6TynJq
    port: ${val.redis.port}

#自定义配置：用来判断是否执行手动切换数据源的方法
self:
  open-dynamic-datasource: true



eureka:
  instance:
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 10
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
  client:
    service-url:
      defaultZone: http://${val.eureka.host}:${val.eureka.port}/eureka/

feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000

ribbon:
  ConnectionTimeout: 100000
  ReadTimeout: 100000

hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000

mybatis-plus:
  mapper-locations: classpath*:/mapper/**Mapper.xml
  typeAliasesPackage: com.holderzone.holder.saas.store.config.entity.domain
  configuration:
    map-underscore-to-camel-case: true
    aggressive-lazy-loading: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    cache-enabled: false
    call-setters-on-nulls: false
  global-config:
    refresh: false
    db-config:
      db-type: mysql
      id-type: auto
      field-strategy: not_empty
      logic-delete-value: 1
      logic-not-delete-value: 0
