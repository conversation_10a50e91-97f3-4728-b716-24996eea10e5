Manifest-Version: 1.0
Class-Path: commons-pool2-2.5.0.jar commons-jxpath-1.3.jar jackson-dat
 abind-2.9.6.jar springfox-core-2.7.0.jar mysql-connector-java-8.0.11.
 jar validation-api-2.0.1.Final.jar resource-common-dto-1.0.0-20181011
 .033205-17.jar classmate-1.3.4.jar netty-codec-4.1.25.Final.jar react
 ive-streams-1.0.2.jar hibernate-validator-6.0.10.Final.jar springfox-
 schema-2.7.0.jar jedis-2.9.0.jar guice-4.1.0.jar ezmorph-1.0.6.jar sp
 ring-cloud-netflix-ribbon-2.0.0.RELEASE.jar netty-resolver-4.1.25.Fin
 al.jar spring-boot-starter-jdbc-2.0.3.RELEASE.jar netty-handler-4.1.2
 5.Final.jar feign-httpclient-9.7.0.jar spring-plugin-core-1.2.0.RELEA
 SE.jar byte-buddy-1.7.11.jar xstream-1.4.10.jar jboss-logging-3.3.2.F
 inal.jar spring-core-5.0.7.RELEASE.jar netty-buffer-4.1.25.Final.jar 
 rocketmq-remoting-4.2.0.jar snakeyaml-1.19.jar spring-plugin-metadata
 -1.2.0.RELEASE.jar spring-cloud-openfeign-core-2.0.0.RELEASE.jar slf4
 j-api-1.7.25.jar HikariCP-2.7.9.jar jsr311-api-1.1.1.jar hystrix-core
 -1.5.12.jar spring-cloud-netflix-archaius-2.0.0.RELEASE.jar netflix-c
 ommons-util-0.3.0.jar rocketmq-common-4.2.0.jar swagger-models-1.5.13
 .jar jackson-core-2.9.6.jar feign-java8-9.5.1.jar log4j-jul-2.10.0.ja
 r eureka-client-1.9.2.jar ribbon-httpclient-2.2.5.jar spring-boot-sta
 rter-logging-2.0.3.RELEASE.jar tomcat-embed-core-8.5.31.jar spring-ex
 pression-5.0.7.RELEASE.jar tomcat-embed-websocket-8.5.31.jar spring-b
 oot-2.0.3.RELEASE.jar spring-cloud-netflix-core-2.0.0.RELEASE.jar spr
 ingfox-swagger-common-2.7.0.jar aopalliance-1.0.jar spring-web-5.0.7.
 RELEASE.jar framework-sdk-1.1.3-20180921.030207-2.jar spring-jcl-5.0.
 7.RELEASE.jar spring-cloud-starter-netflix-archaius-2.0.0.RELEASE.jar
  mybatis-spring-1.3.1.jar feign-slf4j-9.5.1.jar ribbon-eureka-2.2.5.j
 ar ribbon-core-2.2.5.jar archaius-core-0.7.6.jar netty-transport-nati
 ve-unix-common-4.1.25.Final.jar woodstox-core-asl-4.4.1.jar jettison-
 1.3.7.jar spring-aop-5.0.7.RELEASE.jar ribbon-transport-2.2.5.jar net
 ty-codec-http-4.1.25.Final.jar framework-base-dto-1.0.0-20180921.0833
 52-3.jar spring-boot-autoconfigure-2.0.3.RELEASE.jar unidecode-0.0.7.
 jar spring-beans-5.0.7.RELEASE.jar spring-cloud-context-2.0.0.RELEASE
 .jar mapstruct-1.2.0.Final.jar commons-collections-3.2.1.jar rxnetty-
 0.4.9.jar springfox-spi-2.7.0.jar spring-boot-starter-log4j2-2.0.3.RE
 LEASE.jar compactmap-1.2.1.jar jcl-over-slf4j-1.7.25.jar netflix-even
 tbus-0.3.0.jar log4j-core-2.10.0.jar httpcore-4.4.9.jar spring-securi
 ty-rsa-1.0.5.RELEASE.jar ribbon-2.2.5.jar spring-oxm-5.0.7.RELEASE.ja
 r swagger-annotations-1.5.13.jar commons-lang3-3.4.jar protobuf-java-
 2.6.0.jar gson-2.8.5.jar spring-context-support-5.0.7.RELEASE.jar jav
 ax.inject-1.jar framework-rocketmq-starter-1.1.0-20180917.083734-2.ja
 r spring-boot-starter-data-redis-2.0.3.RELEASE.jar mybatis-spring-boo
 t-starter-1.3.1.jar rxnetty-contexts-0.4.9.jar springfox-swagger2-2.7
 .0.jar framework-sdk-starter-1.0.4-20180915.085245-9.jar xmlpull-1.1.
 3.1.jar commons-configuration-1.8.jar spring-cloud-starter-netflix-ri
 bbon-2.0.0.RELEASE.jar netty-transport-native-epoll-4.1.25.Final.jar 
 guava-18.0.jar feign-hystrix-9.5.1.jar antlr-2.7.7.jar stringtemplate
 -3.2.1.jar mybatis-3.4.5.jar commons-beanutils-1.8.0.jar tomcat-embed
 -el-8.5.31.jar mybatis-spring-boot-autoconfigure-1.3.1.jar spring-tx-
 5.0.7.RELEASE.jar stax-api-1.0-2.jar xpp3_min-1.1.4c.jar spring-cloud
 -netflix-eureka-client-2.0.0.RELEASE.jar ribbon-loadbalancer-2.2.5.ja
 r commons-lang-2.6.jar commons-math-2.2.jar spring-boot-starter-aop-2
 .0.3.RELEASE.jar netflix-infix-0.3.0.jar lettuce-core-5.0.4.RELEASE.j
 ar spring-cloud-starter-openfeign-2.0.0.RELEASE.jar netty-all-4.1.25.
 Final.jar fastjson-1.2.49.jar eureka-core-1.9.2.jar spring-jdbc-5.0.7
 .RELEASE.jar commons-logging-1.1.1.jar spring-security-crypto-5.0.6.R
 ELEASE.jar httpclient-4.5.5.jar spring-cloud-starter-netflix-eureka-c
 lient-2.0.0.RELEASE.jar aspectjweaver-1.8.13.jar spring-context-5.0.7
 .RELEASE.jar bcprov-jdk15on-1.56.jar jackson-module-parameter-names-2
 .9.6.jar javassist-3.21.0-GA.jar spring-data-redis-2.0.8.RELEASE.jar 
 netflix-statistics-0.1.1.jar jersey-client-1.19.1.jar stax2-api-3.1.4
 .jar spring-webmvc-5.0.7.RELEASE.jar HdrHistogram-2.1.9.jar jersey-co
 re-1.19.1.jar dexx-collections-0.2.jar rxjava-1.3.8.jar json-lib-2.4-
 jdk15.jar netty-common-4.1.25.Final.jar log4j-api-2.10.0.jar feign-co
 re-9.5.1.jar javax.annotation-api-1.3.2.jar reflections-0.9.11.jar ja
 ckson-datatype-jsr310-2.9.6.jar springfox-swagger-ui-2.7.0.jar qr-cod
 e-0.0.0.1.jar servo-core-0.12.21.jar spring-boot-starter-tomcat-2.0.3
 .RELEASE.jar druid-1.1.10.jar spring-boot-starter-web-2.0.3.RELEASE.j
 ar modelmapper-1.1.3.jar jackson-annotations-2.9.0.jar spring-data-ke
 yvalue-2.0.8.RELEASE.jar log4j-slf4j-impl-2.10.0.jar commons-codec-1.
 11.jar stax-api-1.0.1.jar reactor-core-3.1.8.RELEASE.jar spring-cloud
 -starter-2.0.0.RELEASE.jar spring-boot-starter-2.0.3.RELEASE.jar spri
 ng-data-commons-2.0.8.RELEASE.jar netty-transport-4.1.25.Final.jar jo
 da-time-2.9.9.jar jul-to-slf4j-1.7.25.jar springfox-spring-web-2.7.0.
 jar spring-cloud-commons-2.0.0.RELEASE.jar antlr-runtime-3.4.jar spri
 ng-boot-starter-json-2.0.3.RELEASE.jar bcpkix-jdk15on-1.56.jar rxnett
 y-servo-0.4.9.jar jersey-apache-client4-1.19.1.jar jackson-datatype-j
 dk8-2.9.6.jar rocketmq-client-4.2.0.jar
Main-Class: com.holderzone.saas.store.member.HolderSaasStoreMemberAppl
 ication

