package com.holderzone.saas.aggregation.kds.service.feign.kds;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KdsPrinterRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = KdsPrinterRpcService.FallbackFactoryImpl.class)
public interface KdsPrinterRpcService {

    @PostMapping("/kds_printer/create")
    void create(@RequestBody KdsPrinterCreateReqDTO kdsPrinterCreateReqDTO);

    @PostMapping("/kds_printer/update")
    void update(@RequestBody KdsPrinterUpdateReqDTO kdsPrinterUpdateReqDTO);

    @PostMapping("/kds_printer/delete")
    void delete(@RequestBody KdsPrinterDeleteReqDTO kdsPrinterDeleteReqDTO);

    @PostMapping("/kds_printer/page")
    Page<KdsPrinterRespDTO> page(@RequestBody KdsPrinterPageReqDTO kdsPrinterPageReqDTO);

    @PostMapping("/kds_printer/bind")
    void bind(@RequestBody KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO);

    @PostMapping("/kds_printer/rebind")
    void rebind(@RequestBody KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO);

    @PostMapping("/kds_printer/unbind")
    void unbind(@RequestBody KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<KdsPrinterRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public KdsPrinterRpcService create(Throwable throwable) {

            return new KdsPrinterRpcService() {

                @Override
                public void create(KdsPrinterCreateReqDTO kdsPrinterCreateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "create",
                            JacksonUtils.writeValueAsString(kdsPrinterCreateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void update(KdsPrinterUpdateReqDTO kdsPrinterUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "update",
                            JacksonUtils.writeValueAsString(kdsPrinterUpdateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void delete(KdsPrinterDeleteReqDTO kdsPrinterDeleteReqDTO) {
                    log.error(HYSTRIX_PATTERN, "delete",
                            JacksonUtils.writeValueAsString(kdsPrinterDeleteReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<KdsPrinterRespDTO> page(KdsPrinterPageReqDTO kdsPrinterPageReqDTO) {
                    log.error(HYSTRIX_PATTERN, "page",
                            JacksonUtils.writeValueAsString(kdsPrinterPageReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void bind(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "bind",
                            JacksonUtils.writeValueAsString(kdsPrinterBindUnbindReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void rebind(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "rebind",
                            JacksonUtils.writeValueAsString(kdsPrinterBindUnbindReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void unbind(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "unbind",
                            JacksonUtils.writeValueAsString(kdsPrinterBindUnbindReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
