package com.holderzone.saas.aggregation.kds;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableSwagger2
@EnableApolloConfig
@EnableEurekaClient
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.holderzone.saas.aggregation.kds")
@SpringBootApplication(scanBasePackages = "com.holderzone.saas.aggregation.kds")
public class HolderSaasAggregationKdsApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasAggregationKdsApplication.class, args);
    }

}
