package com.holderzone.saas.aggregation.kds.service.feign.organization;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className OrganizationClientService
 * @date 18-9-21 下午5:15
 * @description
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationClientService.ServiceFallBack.class)
public interface OrganizationClientService {
    /**
     * 从云端查询状态
     *
     * @param deviceNo 厂商设备编号
     * @return dto
     */
    @GetMapping(value = "/device/find_device_status/{deviceNo}")
    StoreDeviceDTO findDeviceStatus(@PathVariable("deviceNo") String deviceNo);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<OrganizationClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";


        @Override
        public OrganizationClientService create(Throwable throwable) {
            return new OrganizationClientService() {
                @Override
                public StoreDeviceDTO findDeviceStatus(String deviceNo) {
                    log.error(HYSTRIX_PATTERN, "findDeviceStatus", "设备编号为：" + deviceNo, ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询接口熔断");
                }
            };
        }
    }
}
