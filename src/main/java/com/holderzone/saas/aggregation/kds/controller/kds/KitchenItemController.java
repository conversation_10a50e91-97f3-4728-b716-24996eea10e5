package com.holderzone.saas.aggregation.kds.controller.kds;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.aggregation.kds.service.feign.kds.KitchenItemRpcService;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Api("厨房菜品接口")
@RequestMapping("/kitchen_item")
public class KitchenItemController {

    private final KitchenItemRpcService kitchenItemRpcService;

    @Autowired
    public KitchenItemController(KitchenItemRpcService kitchenItemRpcService) {
        this.kitchenItemRpcService = kitchenItemRpcService;
    }

    /**
     * for benchmark
     *
     * @param itemPrepareReqDTO
     */
    @PostMapping("/prepare")
    @ApiOperation(value = "菜品入厨房")
    public void prepare(@RequestBody @Validated ItemPrepareReqDTO itemPrepareReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("菜品入厨房入参:{}", JacksonUtils.writeValueAsString(itemPrepareReqDTO));
        }
        kitchenItemRpcService.prepare(itemPrepareReqDTO);
    }

    @PostMapping("/prd_query")
    @ApiOperation(value = "查询制作点菜品状态")
    public Result<PrdDstRespDTO> prdQuery(@RequestBody PrdItemStatusReqDTO prdItemStatusReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询制作点菜品状态入参:{}", JacksonUtils.writeValueAsString(prdItemStatusReqDTO));
        }
        return Result.buildSuccessResult(kitchenItemRpcService.prdQuery(prdItemStatusReqDTO));
    }

    @PostMapping("/dst_query")
    @ApiOperation(value = "查询菜品状态")
    public Result<PrdDstRespDTO> dstQuery(@RequestBody DstItemStatusReqDTO dstItemStatusReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询菜品厨房状态入参:{}", JacksonUtils.writeValueAsString(dstItemStatusReqDTO));
        }
        return Result.buildSuccessResult(kitchenItemRpcService.dstQuery(dstItemStatusReqDTO));
    }

    @PostMapping("/cooking")
    @ApiOperation(value = "制作菜品：" +
            "deviceId(must)\n" +
            "prdDstItemList(must)\n" +
            "    isWeight(must)\n" +
            "    currentCount(must)\n" +
            "    weightKitchenItem\n" +
            "        kitchenItemGuid(must)\n" +
            "        areaGuid(must)\n" +
            "        isHanged(must)\n" +
            "        isUrged(must)\n" +
            "    kitchenItemList(和weightKitchenItem二选一)\n" +
            "        kitchenItemGuid(must)\n" +
            "        areaGuid(must)\n" +
            "        isHanged(must)\n" +
            "        isUrged(must)\n" +
            "    orderGuid(must)\n" +
            "    orderDesc(must)\n" +
            "    orderNumber(must)\n" +
            "\n" +
            "    itemGuid(recommend)\n" +
            "    itemName(must)\n" +
            "    skuGuid(recommend)\n" +
            "    skuName(optional)\n" +
            "    skuUnit(optional)\n" +
            "    attrGroup(optional)\n" +
            "        groupName(recommend)\n" +
            "        groupName(must)\n" +
            "        attrs(must)\n" +
            "            attrGuid(recommend)\n" +
            "            attrName(must)\n" +
            "            attrNumber(recommend)\n" +
            "    itemRemark(optional)\n" +
            "    orderRemark(optional)\n" +
            "    itemAttrMd5(recommend)\n" +
            "    urgedItemNumber(must)\n" +
            "    hangedItemNumber(must)")
    public Result cooking(@RequestBody ItemStateTransReqDTO itemStateTransReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("制作菜品入参:{}", JacksonUtils.writeValueAsString(itemStateTransReqDTO));
        }
        kitchenItemRpcService.cooking(itemStateTransReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/complete")
    @ApiOperation(value = "完成菜品")
    public Result complete(@RequestBody ItemStateTransReqDTO itemStateTransReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("完成菜品入参:{}", JacksonUtils.writeValueAsString(itemStateTransReqDTO));
        }
        kitchenItemRpcService.complete(itemStateTransReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/distribute")
    @ApiOperation(value = "菜品出堂")
    public Result distribute(@RequestBody ItemStateTransReqDTO itemStateTransReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("菜品出堂入参:{}", JacksonUtils.writeValueAsString(itemStateTransReqDTO));
        }
        kitchenItemRpcService.distribute(itemStateTransReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/prd_history")
    @ApiOperation(value = "查询制作点历史记录")
    public Result<Page<KitchenItemDTO>> prdHistory(@RequestBody PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询制作点历史记录入参:{}", JacksonUtils.writeValueAsString(prdDstItemHistoryReqDTO));
        }
        return Result.buildSuccessResult(kitchenItemRpcService.prdHistory(prdDstItemHistoryReqDTO));
    }

    @PostMapping("/dst_history")
    @ApiOperation(value = "查询出堂口历史记录")
    public Result<Page<KitchenItemDTO>> dstHistory(@RequestBody PrdDstItemHistoryReqDTO itemStateTransReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询出堂口历史记录入参:{}", JacksonUtils.writeValueAsString(itemStateTransReqDTO));
        }
        return Result.buildSuccessResult(kitchenItemRpcService.dstHistory(itemStateTransReqDTO));
    }

    @PostMapping("/prd_print_again")
    @ApiOperation(value = "制作记录重新打印")
    public Result prdPrintAgain(@RequestBody KitchenItemDTO kitchenItemDTO) {
        if (log.isInfoEnabled()) {
            log.info("制作记录重新打印入参:{}", JacksonUtils.writeValueAsString(kitchenItemDTO));
        }
        kitchenItemRpcService.prdPrintAgain(kitchenItemDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/dst_print_again")
    @ApiOperation(value = "出堂记录重新打印")
    public Result dstPrintAgain(@RequestBody KitchenItemDTO kitchenItemDTO) {
        if (log.isInfoEnabled()) {
            log.info("出堂记录重新打印入参:{}", JacksonUtils.writeValueAsString(kitchenItemDTO));
        }
        kitchenItemRpcService.dstPrintAgain(kitchenItemDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/cancel_dst")
    @ApiOperation(value = "撤销出堂")
    public Result cancelDistribute(@RequestBody ItemCancelDstReqDTO itemCancelDstReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("撤销出堂入参:{}", JacksonUtils.writeValueAsString(itemCancelDstReqDTO));
        }
        kitchenItemRpcService.cancelDistribute(itemCancelDstReqDTO);
        return Result.buildEmptySuccess();
    }
}
