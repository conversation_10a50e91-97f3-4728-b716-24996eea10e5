package com.holderzone.saas.aggregation.kds.service.feign.staff;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.user.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuService
 * @date 19-2-11 下午2:01
 * @description 商户后台获取菜单
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(name = "holder-saas-store-staff", fallbackFactory = UserClientService.ServiceFallBack.class)
public interface UserClientService {

    @PostMapping(value = "/user/validate")
    ValidateRespDTO validate(@RequestBody ValidateDTO validateDTO);

    @PostMapping("/menu/get_source_by_user")
    List<MenuSourceDTO> getSourceByUser(@RequestParam("terminalCode") String terminalCode);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<UserClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public UserClientService create(Throwable cause) {
            return new UserClientService() {

                @Override
                public ValidateRespDTO validate(ValidateDTO validateDTO) {
                    log.error(HYSTRIX_PATTERN, "validate", JacksonUtils.writeValueAsString(validateDTO),
                            ThrowableUtils.asString(cause));
                    throw new BusinessException("验证失败");
                }

                @Override
                public List<MenuSourceDTO> getSourceByUser(String terminalCode) {
                    log.error(HYSTRIX_PATTERN, "getSourceByUser", "终端Code为：" + terminalCode, ThrowableUtils.asString(cause));
                    throw new BusinessException("获取用户资源接口熔断");
                }
            };
        }
    }
}
