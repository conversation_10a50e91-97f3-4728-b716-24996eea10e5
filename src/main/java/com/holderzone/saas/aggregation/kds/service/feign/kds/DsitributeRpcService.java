package com.holderzone.saas.aggregation.kds.service.feign.kds;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DstAreaRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindDetailsRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindStatusRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstTypeBindRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = DsitributeRpcService.FallbackFactoryImpl.class)
public interface DsitributeRpcService {

    @PostMapping("/distribute/query_binding_preview")
    DstBindStatusRespDTO queryBindingPreview(@RequestBody DeviceQueryReqDTO deviceQueryReqDTO);

    @PostMapping("/distribute/query_binding_details")
    DstBindDetailsRespDTO queryBindingDetails(@RequestBody @Validated DstItemQueryReqDTO dstItemQueryReqDTO);

    @PostMapping("/distribute/query_bound_area")
    DstAreaRespDTO queryBoundArea(@RequestBody DstBoundAreaReqDTO dstBoundAreaReqDTO);

    @PostMapping("/distribute/bind_area")
    void bindArea(@RequestBody DstBindAreaReqDTO dstBindAreaReqDTO);

    @PostMapping("/distribute/query_bound_item_of_device")
    List<DstTypeBindRespDTO> queryBoundItemOfDevice(@RequestBody DstItemQueryReqDTO dstItemQueryReqDTO);

    @PostMapping("/distribute/query_all_item_of_store")
    List<DstTypeBindRespDTO> queryAllItemOfStore(@RequestBody DstItemQueryReqDTO dstItemQueryReqDTO);

    @PostMapping("/distribute/bind_item")
    void bindItem(@RequestBody DstBindItemReqDTO dstBindItemReqDTO);

    @PostMapping("/distribute/unbind_item")
    void unbindItem(@RequestBody DstBindItemReqDTO dstBindItemReqDTO);

    @PostMapping("/distribute/update_basic_config")
    void updateDstBasicConfig(@RequestBody DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO);

    @PostMapping("/distribute/update_advanced_config")
    void updateDstAdvancedConfig(@RequestBody DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<DsitributeRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public DsitributeRpcService create(Throwable throwable) {

            return new DsitributeRpcService() {

                @Override
                public DstBindStatusRespDTO queryBindingPreview(DeviceQueryReqDTO deviceQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryBindingPreview",
                            JacksonUtils.writeValueAsString(deviceQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public DstBindDetailsRespDTO queryBindingDetails(DstItemQueryReqDTO dstItemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryBindingDetails",
                            JacksonUtils.writeValueAsString(dstItemQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public DstAreaRespDTO queryBoundArea(DstBoundAreaReqDTO dstBoundAreaReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryBoundArea",
                            JacksonUtils.writeValueAsString(dstBoundAreaReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void bindArea(DstBindAreaReqDTO dstBindAreaReqDTO) {
                    log.error(HYSTRIX_PATTERN, "bindArea",
                            JacksonUtils.writeValueAsString(dstBindAreaReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<DstTypeBindRespDTO> queryBoundItemOfDevice(DstItemQueryReqDTO dstItemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryBoundItemOfDevice",
                            JacksonUtils.writeValueAsString(dstItemQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<DstTypeBindRespDTO> queryAllItemOfStore(DstItemQueryReqDTO dstItemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryAllItemOfStore",
                            JacksonUtils.writeValueAsString(dstItemQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void bindItem(DstBindItemReqDTO dstBindItemReqDTO) {
                    log.error(HYSTRIX_PATTERN, "bindItem",
                            JacksonUtils.writeValueAsString(dstBindItemReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void unbindItem(DstBindItemReqDTO dstBindItemReqDTO) {
                    log.error(HYSTRIX_PATTERN, "unbindItem",
                            JacksonUtils.writeValueAsString(dstBindItemReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateDstBasicConfig(DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateDstBasicConfig",
                            JacksonUtils.writeValueAsString(deviceBasicConfUpdateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateDstAdvancedConfig(DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateDstAdvancedConfig",
                            JacksonUtils.writeValueAsString(deviceAdvanConfUpdateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
