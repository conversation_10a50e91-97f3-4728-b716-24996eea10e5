package com.holderzone.saas.aggregation.kds.execption;

import com.holderzone.feign.spring.boot.core.OpenFeignException;
import com.holderzone.feign.spring.boot.exception.ResultExceptionHandlerAdapter;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.response.ResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2018/09/14 16:01
 * @description 全局异常拦截
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler extends ResultExceptionHandlerAdapter {

    @ExceptionHandler(value = IOException.class)
    public Result ioException(IOException e) {
        String message = e.getMessage();
        log.error("IOException:" + e);
        if (message.contains("Connection reset by peer")) {
            message = "业务繁忙，请稍后再试";
        }
        return Result.buildOpFailedResult(message);
    }

    @Override
    protected Result interceptOpenFeignException(OpenFeignException e) {
        String message = e.getMessage();
        // todo 使用 SpecificException
        // 这里判断staff服务中userController的validate方法的异常code与message做特殊处理，后续有更好的解决方法考虑替换
        if (message.length() >= 10 && "406".equals(message.substring(7, 10))
                && message.contains("设备已解绑，请重新绑定门店")) {
            return Result.buildFailResult(
                    ResultEnum.NO_SUCH_APPLICATION_EXISTS.getResultCode(),
                    "设备已解绑，请重新绑定门店"
            );
        }
        return super.interceptOpenFeignException(e);
    }
}
