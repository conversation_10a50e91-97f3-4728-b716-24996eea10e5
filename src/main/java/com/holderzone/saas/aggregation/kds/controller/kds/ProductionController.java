package com.holderzone.saas.aggregation.kds.controller.kds;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.aggregation.kds.service.feign.kds.ProductionRpcService;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.PointBindDetailsRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PointTypeBindRespDTO;
import com.holderzone.saas.store.dto.kds.resp.ProductionPointRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api("KDS设备接口")
@RequestMapping("/production")
public class ProductionController {

    private final ProductionRpcService productionRpcService;

    @Autowired
    public ProductionController(ProductionRpcService productionRpcService) {
        this.productionRpcService = productionRpcService;
    }

    @PostMapping("/create_point")
    @ApiOperation(value = "创建KDS制作点堂口")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "创建KDS制作点堂口")
    public Result<String> createPoint(@RequestBody PrdPointCreateReqDTO prdPointCreateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("创建KDS制作点堂口入参:{}", JacksonUtils.writeValueAsString(prdPointCreateReqDTO));
        }
        return Result.buildSuccessResult(productionRpcService.createPoint(prdPointCreateReqDTO));
    }

    @PostMapping("/update_point")
    @ApiOperation(value = "更新KDS制作点堂口")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "更新KDS制作点堂口")
    public Result updatePoint(@RequestBody PrdPointUpdateReqDTO prdPointUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新KDS制作点堂口入参:{}", JacksonUtils.writeValueAsString(prdPointUpdateReqDTO));
        }
        productionRpcService.updatePoint(prdPointUpdateReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/delete_point")
    @ApiOperation(value = "删除KDS制作点堂口")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "删除KDS制作点堂口")
    public Result deletePoint(@RequestBody PrdPointDelReqDTO prdPointDelReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除KDS制作点堂口入参:{}", JacksonUtils.writeValueAsString(prdPointDelReqDTO));
        }
        productionRpcService.deletePoint(prdPointDelReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/list_point")
    @ApiOperation(value = "查询KDS制作点堂口列表")
    public Result<List<ProductionPointRespDTO>> listPoint(@RequestBody PrdPointListReqDTO prdPointListReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS制作点堂口列表入参:{}", JacksonUtils.writeValueAsString(prdPointListReqDTO));
        }
        return Result.buildSuccessResult(productionRpcService.listPoint(prdPointListReqDTO));
    }

    @PostMapping("/query_all_point_item")
    @ApiOperation(value = "查询KDS制作点堂口已绑定未绑定菜品")
    public Result<List<PointTypeBindRespDTO>> queryAllPointItem(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS制作点堂口已绑定未绑定菜品入参:{}", JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO));
        }
        return Result.buildSuccessResult(productionRpcService.queryAllPointItem(prdPointItemQueryReqDTO));
    }

    @PostMapping("/query_binding_details")
    @ApiOperation(value = "查询KDS制作点堂口绑定细节")
    public Result<PointBindDetailsRespDTO> queryBindingDetails(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS制作点堂口绑定细节入参:{}", JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO));
        }
        return Result.buildSuccessResult(productionRpcService.queryBindingDetails(prdPointItemQueryReqDTO));
    }

    @PostMapping("/query_bound_point_item")
    @ApiOperation(value = "查询KDS制作点堂口已绑定菜品")
    public Result<List<PointTypeBindRespDTO>> queryBoundPointItem(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS制作点堂口已绑定菜品入参:{}", JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO));
        }
        return Result.buildSuccessResult(productionRpcService.queryBoundPointItem(prdPointItemQueryReqDTO));
    }

    @PostMapping("/update_point_item_config")
    @ApiOperation(value = "更新堂口菜品配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "更新堂口菜品配置")
    public Result updatePointItem(@RequestBody ItemConfBatchUpdateReqDTO itemConfBatchUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新堂口菜品配置入参:{}", JacksonUtils.writeValueAsString(itemConfBatchUpdateReqDTO));
        }
        productionRpcService.updatePointItem(itemConfBatchUpdateReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/bind_point_item")
    @ApiOperation(value = "KDS制作点堂口绑定菜品")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "KDS制作点堂口绑定菜品")
    public Result bindPointItem(@RequestBody PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS制作点堂口绑定菜品入参:{}", JacksonUtils.writeValueAsString(prdPointItemBindReqDTO));
        }
        productionRpcService.bindPointItem(prdPointItemBindReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/unbind_point_item")
    @ApiOperation(value = "KDS制作点堂口解绑菜品")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "KDS制作点堂口解绑菜品")
    public Result unbindPointItem(@RequestBody PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS制作点堂口解绑菜品入参:{}", JacksonUtils.writeValueAsString(prdPointItemBindReqDTO));
        }
        productionRpcService.unbindPointItem(prdPointItemBindReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/update_basic_config")
    @ApiOperation(value = "更新设备基础配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "更新设备基础配置")
    public Result updateBasicConfig(@RequestBody DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新设备基础配置入参:{}", JacksonUtils.writeValueAsString(deviceBasicConfUpdateReqDTO));
        }
        productionRpcService.updatePrdBasicConfig(deviceBasicConfUpdateReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/update_advanced_config")
    @ApiOperation(value = "更新设备高级配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "更新设备高级配置")
    public Result updateAdvancedConfig(@RequestBody DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新设备高级配置入参:{}", JacksonUtils.writeValueAsString(deviceAdvanConfUpdateReqDTO));
        }
        productionRpcService.updatePrdAdvancedConfig(deviceAdvanConfUpdateReqDTO);
        return Result.buildEmptySuccess();
    }
}

