package com.holderzone.saas.aggregation.kds.controller.staff;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.aggregation.kds.service.feign.staff.UserClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.LocalUserInfoDTO;
import com.holderzone.saas.store.dto.user.LocalUserReqDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.dto.user.UserDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuController
 * @date 19-2-11 下午5:58
 * @description
 * @program holder-saas-store-staff
 */
@Slf4j
@RestController
@RequestMapping("/menu")
@Api(tags = "一体机权限相关接口")
public class UserController {

    private final UserClientService userClientService;

    @Autowired
    public UserController(UserClientService userClientService) {
        this.userClientService = userClientService;
    }

    @ApiOperation(value = "根据终端Code获取该终端下的资源信息")
    @PostMapping("/get_source_by_user")
    public Result<List<MenuSourceDTO>> getSourceByUser(@ApiParam("业务请求参数为终端Code") @RequestBody SingleDataDTO singleDataDTO) {
        List<MenuSourceDTO> result = userClientService.getSourceByUser(singleDataDTO.getData());
        log.info("聚合层根据终端code获取终端下的资源信息接口，入参：{}，返回值：{}", "终端code为：" + singleDataDTO.getData(),
                JacksonUtils.writeValueAsString(result));
        return Result.buildSuccessResult(result);
    }
}
