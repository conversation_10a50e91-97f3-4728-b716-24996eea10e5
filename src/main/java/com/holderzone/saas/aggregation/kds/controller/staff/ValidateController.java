package com.holderzone.saas.aggregation.kds.controller.staff;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.response.ResultEnum;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.aggregation.kds.service.feign.organization.OrganizationClientService;
import com.holderzone.saas.aggregation.kds.service.feign.staff.UserClientService;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.user.ValidateDTO;
import com.holderzone.saas.store.dto.user.ValidateRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className ValidateController
 * @date 18-9-17 上午11:45
 * @description 登陆验证相关接口
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@Api(description = "app查询设备注册-绑定状态、登陆验证接口")
public class ValidateController {

    private final UserClientService userClientService;

    private final OrganizationClientService organizationClientService;

    @Autowired
    public ValidateController(UserClientService userClientService, OrganizationClientService organizationClientService) {
        this.userClientService = userClientService;
        this.organizationClientService = organizationClientService;
    }

    @ApiOperation(value = "app登陆验证接口，验证成功返回相关信息")
    @PostMapping(value = "/user/validate")
    public Result<ValidateRespDTO> validate(@RequestBody ValidateDTO validateDTO) {
        log.info("android登陆验证接口入参为：" + JacksonUtils.writeValueAsString(validateDTO));
        ValidateRespDTO dto = userClientService.validate(validateDTO);
        if (dto == null) {
            return Result.buildFailResult(ResultEnum.SERVICE_TEMPORARILY_UNAVAILABLE.getResultCode(), "接口异常，请稍后重试");
        }
        return Result.buildSuccessResult(dto);
    }


    @ApiOperation(value = "根据厂商设备编号查询设备的注册、绑定状态", notes = "分为未注册、已注册未绑定、已注册已绑定三种情况")
    @GetMapping(value = "/device/find_device_status/{deviceNo}")
    public Result<StoreDeviceDTO> findDeviceStatus(@ApiParam(value = "厂商设备编号") @PathVariable("deviceNo") String deviceNo) {
        log.info("设备状态查询请求入参：{}", deviceNo);
        StoreDeviceDTO result = organizationClientService.findDeviceStatus(deviceNo);
        if (result == null) {
            return Result.buildFailResult(ResultEnum.SERVICE_TEMPORARILY_UNAVAILABLE.getResultCode(), "接口异常，请稍后重试");
        }
        log.info("查询到设备状态，result:{}", JacksonUtils.writeValueAsString(result));
        return Result.buildSuccessResult(result);
    }
}
