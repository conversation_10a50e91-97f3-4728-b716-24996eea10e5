package com.holderzone.saas.store.organization.mq;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;
import com.holderzone.saas.store.organization.service.remote.ErpClient;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketListenerHandler(
        topic = MqConstant.DOWNSTREAM_WARE_HOUSE_TOPIC,
        tags = {
                MqConstant.DOWNSTREAM_WARE_HOUSE_CREATE_TAG,
                MqConstant.DOWNSTREAM_WARE_HOUSE_UPDATE_TAG,
        },
        consumerGroup = MqConstant.DOWNSTREAM_WARE_HOUSE_GROUP
)
public class WareHouseListener extends AbstractRocketMqConsumer<RocketMqTopic, WarehouseReqDTO> {

    private final ErpClient erpClient;

    @Autowired
    public WareHouseListener(ErpClient erpClient) {
        this.erpClient = erpClient;
    }

    @Override
    public boolean consumeMsg(WarehouseReqDTO warehouseReqDTO, MessageExt messageExt) {
        UserContextUtils.put(messageExt.getProperty(MqConstant.DOWNSTREAM_CONTEXT));
        switch (messageExt.getTags()) {
            case MqConstant.DOWNSTREAM_WARE_HOUSE_CREATE_TAG:
                warehouseReqDTO.setCode(erpClient.warehouseCode());
                erpClient.createWarehouse(warehouseReqDTO);
                break;
            case MqConstant.DOWNSTREAM_WARE_HOUSE_UPDATE_TAG:
                erpClient.updateStoreWarehouse(warehouseReqDTO);
                break;
            default:
                break;
        }
        return true;
    }
}
