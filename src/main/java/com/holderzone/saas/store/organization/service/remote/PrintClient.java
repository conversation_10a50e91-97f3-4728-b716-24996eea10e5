package com.holderzone.saas.store.organization.service.remote;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className PrintClient
 * @date 19-2-10 上午9:56
 * @description 打印服务
 * @program holder-saas-store-organization
 */
@Component
@FeignClient(value = "holder-saas-store-print", fallbackFactory = PrintClient.ServiceFallBack.class)
public interface PrintClient {

    @PostMapping("/printer/change_master")
    void changeMasterDevice(@RequestBody PrinterDTO printerDTO);

    /**
     * 同步云端删除打印机
     *
     * @param deviceId    设备guid
     * @param storeGuid   门店guid
     * @param countDevice 除了自己门店剩余的已绑定设备数
     */
    @PostMapping("/printer/delete_by_device")
    void deletePrinterByDevice(@RequestParam("deviceId") String deviceId,
                               @RequestParam("storeGuid") String storeGuid,
                               @RequestParam("countDevice") int countDevice);


    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<PrintClient> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public PrintClient create(Throwable cause) {
            return new PrintClient() {
                @Override
                public void changeMasterDevice(PrinterDTO printerDTO) {
                    log.error(HYSTRIX_PATTERN, "changeMasterDevice", JacksonUtils.writeValueAsString(printerDTO),
                              ThrowableUtils.asString(cause));
                }

                @Override
                public void deletePrinterByDevice(String deviceId, String storeGuid, int countDevice) {
                    log.error(HYSTRIX_PATTERN, "deletePrinterByDevice", "deviceId为：" + deviceId + "storeGuid为：" + storeGuid + "countDevice为" + countDevice,
                              ThrowableUtils.asString(cause));
                }
            };
        }
    }
}
