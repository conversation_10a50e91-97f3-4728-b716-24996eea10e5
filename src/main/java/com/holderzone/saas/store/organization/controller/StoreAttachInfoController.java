package com.holderzone.saas.store.organization.controller;

import com.holderzone.saas.store.dto.organization.StoreAttachInfoDTO;
import com.holderzone.saas.store.organization.service.StoreAttachInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className StoreAttachInfoController
 * @date 2020.07.13
 * @description 门店附加信息
 * @program holder-saas-store-organization
 */
@RestController
@RequestMapping("/store-attach-info")
@Api( tags = "门店附加信息接口")
@AllArgsConstructor
@Slf4j
public class StoreAttachInfoController {

    private final StoreAttachInfoService storeAttachInfoService;
    @GetMapping("/info")
    @ApiOperation("查询门店附加信息")
    public StoreAttachInfoDTO info(@RequestParam @NotBlank(message = "storeGuid不能为空") String storeGuid){
       return storeAttachInfoService.info(storeGuid);
    }
    @PostMapping("/save")
    @ApiOperation("保存门店附加信息")
    public boolean save(@RequestBody StoreAttachInfoDTO dto){
        return storeAttachInfoService.save(dto);
    }
}
