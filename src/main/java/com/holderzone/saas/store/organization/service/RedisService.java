package com.holderzone.saas.store.organization.service;

import com.holderzone.saas.store.dto.store.store.StoreAllInfoDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RedisService
 * @date 18-12-21 下午3:58
 * @description Redis操作相关接口
 * @program holder-saas-store-aff
 */
public interface RedisService {

    StoreDeviceDTO getStoreMaster(String storeGuid);

    void putStoreMaster(String storeGuid, StoreDeviceDTO storeDeviceDTO);

    void removeStoreMaster(String storeGuid);

    void putStoreAllInfo(StoreAllInfoDTO storeAllInfoDTO);

    StoreAllInfoDTO getStoreAllInfo(String storeGuid);

    void deleteStoreAllInfoByStoreGuid(String storeGuid);
}
