package com.holderzone.saas.store.organization.service.remote;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className TableClient
 * @date 19-1-3 下午5:57
 * @description
 * @program holder-saas-store-organization
 */
@Component
@FeignClient(value = "holder-saas-store-table", fallbackFactory = TableClient.ServiceFallBack.class)
public interface TableClient {

    /**
     * 初始化桌台
     *
     * @param baseDTO DTO
     * @return 返回值
     */
    @PostMapping("/area/init")
    String initTable(@RequestBody BaseDTO baseDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<TableClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TableClient create(Throwable cause) {
            return baseDTO -> {
                log.error(HYSTRIX_PATTERN, "initTable", JacksonUtils.writeValueAsString(baseDTO),
                          ThrowableUtils.asString(cause));
                return "failure";
            };
        }
    }
}
