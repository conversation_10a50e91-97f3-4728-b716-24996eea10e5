package com.holderzone.saas.store.organization.service.remote;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className BusinessClient
 * @date 18-8-30 上午10:39
 * @description 服务间调用-营业相关服务
 * @program holder-saas-store-organization
 */
@Component
@FeignClient(value = "holder-saas-store-staff", fallbackFactory = UserClient.ServiceFallBack.class)
public interface UserClient {

    /**
     * 根据传入的门店guid集合返回门店使用产品的基本信息
     *
     * @param storeGuidList 门店guid集合
     * @return
     */
    @PostMapping("/product/query_product_by_idlist")
    Map<String, List<StoreProductDTO>> queryProductByIdList(@RequestBody List<String> storeGuidList);

    @PostMapping(value = "/user_data/update_new_store/{storeGuid}")
    void updateNewStore(@PathVariable(value = "storeGuid") String storeGuid);

    @GetMapping(value = "/product/query_mchnt_type")
    String queryMchntType();

    @ApiOperation("根据传入的门店guid返回门店使用产品的基本信息")
    @GetMapping("/product/query_product_by_store_guid")
    List<StoreProductDTO> queryProductByStoreGuid(@PathVariable(value = "storeGuid") String storeGuid,
                                                  @PathVariable(value = "withEnterpriseProduct") boolean withEnterpriseProduct);

    /**
     * 同步holder账号
     */
    @PostMapping(value = "/user/sync/holder")
    void syncHolderUser(@RequestBody List<String> organizationIds);


    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<UserClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public UserClient create(Throwable throwable) {
            return new UserClient() {
                @Override
                public Map<String, List<StoreProductDTO>> queryProductByIdList(List<String> storeGuidList) {
                    log.error(HYSTRIX_PATTERN, "queryProductByIdList", JacksonUtils.writeValueAsString(storeGuidList), ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public void updateNewStore(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "updateNewStore", storeGuid, ThrowableUtils.asString(throwable));
                }

                @Override
                public String queryMchntType() {
                    log.error(HYSTRIX_PATTERN, "queryMchntType", "无", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<StoreProductDTO> queryProductByStoreGuid(String storeGuid, boolean withEnterpriseProduct) {
                    log.error(HYSTRIX_PATTERN, "queryProductByStoreGuid", storeGuid + " " + withEnterpriseProduct, ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public void syncHolderUser(List<String> organizationIds) {
                    log.error(HYSTRIX_PATTERN, "syncHolderUser", organizationIds, ThrowableUtils.asString(throwable));
                }
            };
        }
    }
}
