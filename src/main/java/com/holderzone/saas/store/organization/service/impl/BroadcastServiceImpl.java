package com.holderzone.saas.store.organization.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.device.DeviceDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationCreateDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationUpdateDTO;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.util.MessageType;
import com.holderzone.saas.store.constant.member.MemberSyncConstant;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.mq.MqConstant;
import com.holderzone.saas.store.organization.service.BroadcastService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BroadcastServiceImpl implements BroadcastService {

    private static final String WARE_HOUSE_PATTERN = "%s-仓库";

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    @Autowired
    public BroadcastServiceImpl(DefaultRocketMqProducer defaultRocketMqProducer) {
        this.defaultRocketMqProducer = defaultRocketMqProducer;
    }

    @Override
    public void storeCreated(StoreDTO storeDTO) {
        Message message = new Message(
                MqConstant.DOWNSTREAM_STORE_TOPIC,
                MqConstant.DOWNSTREAM_STORE_CREATE_TAG,
                JacksonUtils.toJsonByte(storeDTO)
        );
        message.getProperties().put(MqConstant.DOWNSTREAM_CONTEXT, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);
    }

    @Override
    public void storeUpdated(StoreDTO storeDTO) {
        Message message = new Message(
                MqConstant.DOWNSTREAM_STORE_TOPIC,
                MqConstant.DOWNSTREAM_STORE_UPDATE_TAG,
                JacksonUtils.toJsonByte(storeDTO)
        );
        message.getProperties().put(MqConstant.DOWNSTREAM_CONTEXT, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);
    }

    @Override
    public void createStoreInCloud(OrganizationDO organizationDO) {
        OrganizationCreateDTO organizationCreateDTO = new OrganizationCreateDTO();
        organizationCreateDTO.setName(organizationDO.getName());
        organizationCreateDTO.setOrganizationGuid(organizationDO.getGuid());
        organizationCreateDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        organizationCreateDTO.setEnterpriseName(UserContextUtils.getEnterpriseName());
        organizationCreateDTO.setOrganizationCode(organizationDO.getCode());
        organizationCreateDTO.setContactNumber(organizationDO.getContactTel());
        organizationCreateDTO.setLevel(2);
        organizationCreateDTO.setContactNumber(organizationDO.getContactTel());
        organizationCreateDTO.setProvince(organizationDO.getProvinceCode());
        organizationCreateDTO.setCity(organizationDO.getCityCode());
        organizationCreateDTO.setDistrict(organizationDO.getCountyCode());
        organizationCreateDTO.setAddress(organizationDO.getAddressDetail());
        organizationCreateDTO.setEnabled(organizationDO.getIsEnable() ? 1 : 0);
        organizationCreateDTO.setDeleted(0);
        String[] split = organizationDO.getParentIds().split(",");
        organizationCreateDTO.setFid(split[split.length - 1]);
        UnMessage<OrganizationCreateDTO> unMessage = new UnMessage<>(
                MessageType.ADD.code(),
                UserContextUtils.getEnterpriseGuid(),
                organizationDO.getGuid(),
                organizationCreateDTO
        );
        final Message message = new Message(
                MqConstant.MERCHANT_SYNC_ORGANIZATION_TOPIC,
                MqConstant.MERCHANT_SYNC_ORGANIZATION_TAG,
                JacksonUtils.toJsonByte(unMessage)
        );
        log.info("云平台同步创建门店，消息体：{}", JacksonUtils.writeValueAsString(unMessage));
        if (defaultRocketMqProducer.sendMessage(message)) {
            log.info("云平台同步创建门店，门店：{}，企业：{}，成功",
                    organizationDO.getGuid(), UserContextUtils.getEnterpriseGuid()
            );
        }
    }

    @Override
    public void updateStoreInCloud(OrganizationDO organizationDO) {
        OrganizationUpdateDTO organizationUpdateDTO = new OrganizationUpdateDTO();
        organizationUpdateDTO.setName(organizationDO.getName());
        organizationUpdateDTO.setOrganizationGuid(organizationDO.getGuid());
        organizationUpdateDTO.setContactNumber(organizationDO.getContactTel());
        organizationUpdateDTO.setLevel(2);
        organizationUpdateDTO.setContactNumber(organizationDO.getContactTel());
        organizationUpdateDTO.setProvince(organizationDO.getProvinceCode());
        organizationUpdateDTO.setCity(organizationDO.getCityCode());
        organizationUpdateDTO.setDistrict(organizationDO.getCountyCode());
        organizationUpdateDTO.setAddress(organizationDO.getAddressDetail());
        organizationUpdateDTO.setIsSelfBuildItems(organizationDO.getIsSelfBuildItems());
        if(organizationDO.getIsEnable()!=null){
            organizationUpdateDTO.setEnabled(organizationDO.getIsEnable() ? 1 : 0);
        }
        String[] split = organizationDO.getParentIds().split(",");
        organizationUpdateDTO.setFid(split[split.length - 1]);
        modifyStoreInCloud(organizationUpdateDTO);
    }

    @Override
    public void enableStoreInCloud(OrganizationDO organizationDO) {
        OrganizationUpdateDTO organizationUpdateDTO = new OrganizationUpdateDTO();
        organizationUpdateDTO.setOrganizationGuid(organizationDO.getGuid());
        organizationUpdateDTO.setEnabled(organizationDO.getIsEnable() ? 1 : 0);
        modifyStoreInCloud(organizationUpdateDTO);
    }

    @Override
    public void deleteStoreInCloud(String storeGuid) {
        UnMessage<String> unMessage = new UnMessage<>(
                MessageType.DELETE.code(),
                UserContextUtils.getEnterpriseGuid(),
                storeGuid,
                storeGuid
        );
        final Message message = new Message(
                MqConstant.MERCHANT_SYNC_ORGANIZATION_TOPIC,
                MqConstant.MERCHANT_SYNC_ORGANIZATION_TAG,
                JacksonUtils.toJsonByte(unMessage)
        );
        log.info("云平台同步删除门店，消息体：{}", JacksonUtils.writeValueAsString(unMessage));
        if (defaultRocketMqProducer.sendMessage(message)) {
            log.info("云平台同步删除门店，门店：{}，企业：{}，成功", storeGuid, UserContextUtils.getEnterpriseGuid());
        }
    }

    private void modifyStoreInCloud(OrganizationUpdateDTO organizationUpdateDTO) {
        UnMessage<OrganizationUpdateDTO> unMessage = new UnMessage<>(
                MessageType.UPDATE.code(),
                UserContextUtils.getEnterpriseGuid(),
                organizationUpdateDTO.getOrganizationGuid(),
                organizationUpdateDTO
        );
        final Message message = new Message(
                MqConstant.MERCHANT_SYNC_ORGANIZATION_TOPIC,
                MqConstant.MERCHANT_SYNC_ORGANIZATION_TAG,
                JacksonUtils.toJsonByte(unMessage)
        );
        log.info("云平台同步修改门店，消息体：{}", JacksonUtils.writeValueAsString(unMessage));
        if (defaultRocketMqProducer.sendMessage(message)) {
            log.info("云平台同步修改门店，门店：{}，企业：{}，成功",
                    organizationUpdateDTO.getOrganizationGuid(), UserContextUtils.getEnterpriseGuid()
            );
        }
    }

    @Override
    public void createWareHouse(OrganizationDO organizationDO) {
        WarehouseReqDTO warehouseReqDTO = new WarehouseReqDTO();
        warehouseReqDTO.setAddr(organizationDO.getAddressDetail());
        warehouseReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        warehouseReqDTO.setName(String.format(WARE_HOUSE_PATTERN, organizationDO.getName()));
        warehouseReqDTO.setForeignKey(organizationDO.getGuid());
        warehouseReqDTO.setTel(organizationDO.getContactTel());
        warehouseReqDTO.setPic(organizationDO.getContactName());
        warehouseReqDTO.setType(1);

        final Message message = new Message(
                MqConstant.DOWNSTREAM_WARE_HOUSE_TOPIC,
                MqConstant.DOWNSTREAM_WARE_HOUSE_CREATE_TAG,
                JacksonUtils.toJsonByte(warehouseReqDTO)
        );
        message.getProperties().put(MqConstant.DOWNSTREAM_CONTEXT, UserContextUtils.getJsonStr());
        log.info("ERP服务同步创建门店，消息体：{}", JacksonUtils.writeValueAsString(warehouseReqDTO));
        if (defaultRocketMqProducer.sendMessage(message)) {
            log.info("ERP服务同步创建门店，门店：{}，企业：{}，成功",
                    organizationDO.getGuid(), UserContextUtils.getEnterpriseGuid()
            );
        }
    }

    @Override
    public void updateWareHouse(StoreDTO storeDTO) {
        WarehouseReqDTO warehouseReqDTO = new WarehouseReqDTO();
        warehouseReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        warehouseReqDTO.setForeignKey(storeDTO.getGuid());
        warehouseReqDTO.setName(String.format(WARE_HOUSE_PATTERN, storeDTO.getName()));
        final Message message = new Message(
                MqConstant.DOWNSTREAM_WARE_HOUSE_TOPIC,
                MqConstant.DOWNSTREAM_WARE_HOUSE_UPDATE_TAG,
                JacksonUtils.toJsonByte(warehouseReqDTO)
        );
        message.getProperties().put(MqConstant.DOWNSTREAM_CONTEXT, UserContextUtils.getJsonStr());
        log.info("ERP服务同步修改门店，消息体：{}", JacksonUtils.writeValueAsString(warehouseReqDTO));
        if (defaultRocketMqProducer.sendMessage(message)) {
            log.info("ERP服务同步修改门店，门店：{}，企业：{}，成功",
                    storeDTO.getGuid(), UserContextUtils.getEnterpriseGuid()
            );
        }
    }

    @Override
    public void createMemberStore(StoreDTO storeDTO) {
        Message messageMemberSync = new Message(
                MemberSyncConstant.MEMBER_STORE_SYNC_TOPIC,
                MemberSyncConstant.MEMBER_STORE_SAVE_TAG,
                JacksonUtils.toJsonByte(storeDTO)
        );
        messageMemberSync.getProperties()
                .put(MemberSyncConstant.MEMBER_SYNC_PROPERTY, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(messageMemberSync);
        log.info("会员平台同步创建门店，门店：{}，企业：{}，成功", storeDTO.getGuid(), UserContextUtils.getEnterpriseGuid());
    }

    @Override
    public void updateMemberStore(StoreDTO storeDTO) {
        Message message = new Message(
                MemberSyncConstant.MEMBER_STORE_SYNC_TOPIC,
                MemberSyncConstant.MEMBER_STORE_MODIFY_TAG,
                JacksonUtils.toJsonByte(storeDTO)
        );
        message.getProperties()
                .put(MemberSyncConstant.MEMBER_SYNC_PROPERTY, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);
        log.info("会员平台同步修改门店，门店：{}，企业：{}，成功", storeDTO.getGuid(), UserContextUtils.getEnterpriseGuid());
    }

    @Override
    public void deleteMemberStore(String storeGuid) {
        StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid(storeGuid);
        Message message = new Message(
                MemberSyncConstant.MEMBER_STORE_SYNC_TOPIC,
                MemberSyncConstant.MEMBER_STORE_DELETE_TAG,
                JacksonUtils.toJsonByte(storeDTO)
        );
        message.getProperties().put(MemberSyncConstant.MEMBER_SYNC_PROPERTY, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);
        log.info("会员平台同步删除门店，门店：{}，企业：{}，成功", storeDTO.getGuid(), UserContextUtils.getEnterpriseGuid());
    }

    @Override
    public void deviceBind(StoreDeviceDTO storeDeviceDTO) {
        Message message = new Message(
                MqConstant.DOWNSTREAM_DEVICE_TOPIC,
                MqConstant.DOWNSTREAM_DEVICE_BIND_TAG,
                JacksonUtils.toJsonByte(storeDeviceDTO)
        );
        message.getProperties().put(MqConstant.DOWNSTREAM_CONTEXT, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);
        log.info("商户后台下游同步绑定设备，设备标识：{}，设备编码：{}，门店：{}，企业：{}，成功",
                storeDeviceDTO.getDeviceGuid(), storeDeviceDTO.getDeviceNo(),
                storeDeviceDTO.getStoreGuid(), UserContextUtils.getEnterpriseGuid());
    }

    @Override
    public void deviceUnbind(StoreDeviceDTO storeDeviceDTO) {
        Message message = new Message(
                MqConstant.DOWNSTREAM_DEVICE_TOPIC,
                MqConstant.DOWNSTREAM_DEVICE_UNBIND_TAG,
                JacksonUtils.toJsonByte(storeDeviceDTO)
        );
        message.getProperties().put(MqConstant.DOWNSTREAM_CONTEXT, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);
        log.info("商户后台下游同步解绑设备，设备标识：{}，设备编码：{}，门店：{}，企业：{}，成功",
                storeDeviceDTO.getDeviceGuid(), storeDeviceDTO.getDeviceNo(),
                storeDeviceDTO.getStoreGuid(), UserContextUtils.getEnterpriseGuid());
    }

    @Override
    public void unBindDeviceToCloud(DeviceDTO deviceDTO){
        Message message = new Message(
                MqConstant.CLOUD_SYNC_DEVICE_TOPIC,
                MqConstant.DEVICE_UNBIND_UPLOAD_TAG,
                JacksonUtils.toJsonByte(deviceDTO)
        );
        message.getProperties().put(MqConstant.DOWNSTREAM_CONTEXT, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);
        log.info("商户后台解绑设备同步到云端，设备标识：{}，设备编码：{}，门店：{}，企业：{}，成功",
                deviceDTO.getDeviceGuid(), deviceDTO.getDeviceNo(),
                deviceDTO.getStoreGuid(), deviceDTO.getEnterpriseGuid());
    }

    @Override
    public void bindDeviceToCloud(DeviceDTO deviceDTO) {
        Message message = new Message(
                MqConstant.CLOUD_SYNC_DEVICE_TOPIC,
                MqConstant.DEVICE_BIND_UPLOAD_TAG,
                JacksonUtils.toJsonByte(deviceDTO)
        );
        message.getProperties().put(MqConstant.DOWNSTREAM_CONTEXT, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);
        log.info("商户后台解绑设备同步到云端，设备标识：{}，设备编码：{}，门店：{}，企业：{}，成功",
                deviceDTO.getDeviceGuid(), deviceDTO.getDeviceNo(),
                deviceDTO.getStoreGuid(), deviceDTO.getEnterpriseGuid());
    }
}
