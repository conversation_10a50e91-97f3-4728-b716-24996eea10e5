package com.holderzone.saas.store.organization.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;


/**
 * <AUTHOR>
 * @version 2.0.0
 * @className ThreadPoolConfig
 * @date 2018/07/10 下午7:39
 * @description 线程池配置（未用）
 * @program holder-saas-store-organization
 */
@Configuration
public class ThreadPoolConfig {

    /*
    @Bean
    public ExecutorService redisCacheThreadPool() {
        return new ThreadPoolExecutor(2, 5,
                                      5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(20),
                                      new ThreadFactoryBuilder().setNameFormat("redisCache-%d").build());
    }
    */

    /**
     * 初始化线程池
     *
     * @return 线程池
     */
    @Bean(name = "taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数为系统核心数两倍（IO密集型任务线程）
        int processors = Runtime.getRuntime().availableProcessors() * 2;
        executor.setCorePoolSize(processors);
        // 设置最大线程数
        executor.setMaxPoolSize(50);
        // 设置队列容量
        executor.setQueueCapacity(1024);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);
        // 设置默认线程名称
        executor.setThreadNamePrefix("task-default-theadPool");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        return executor;
    }


}
