package com.holderzone.saas.store.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.device.DeviceDTO;
import com.holderzone.resource.common.dto.device.DeviceStoreDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.table.PadAreaDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceSortDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceUnbindDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.organization.constant.Constant;
import com.holderzone.saas.store.organization.domain.StoreDeviceDO;
import com.holderzone.saas.store.organization.feign.EnterpriseClientService;
import com.holderzone.saas.store.organization.feign.TableService;
import com.holderzone.saas.store.organization.mapper.StoreDeviceMapper;
import com.holderzone.saas.store.organization.mapstruct.StoreDeviceMapStruct;
import com.holderzone.saas.store.organization.service.*;
import com.holderzone.saas.store.organization.service.remote.DeviceClient;
import com.holderzone.saas.store.organization.service.remote.PrintClient;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className StoreTerminalService
 * @date 19-2-10 下午9:59
 * @description 门店-设备相关接口
 * @program holder-saas-store-organization
 */
@SuppressWarnings("unchecked")
@Slf4j
@Service
public class StoreDeviceServiceImpl extends ServiceImpl<StoreDeviceMapper, StoreDeviceDO> implements StoreDeviceService {

    private final StoreDeviceMapper storeDeviceMapper;

    private final PrintClient printClient;

    private final StoreDeviceMapStruct storeDeviceMapStruct;

    private final DeviceClient deviceClient;

    private final StoreService storeService;

    private final RedisTemplate redisTemplate;

    private final BroadcastService broadcastService;

    private final RedisService redisService;

    private final PushService pushService;

    private final TableService tableService;

    private final EnterpriseClientService enterpriseClientService;


    @Autowired
    public StoreDeviceServiceImpl(StoreDeviceMapper storeDeviceMapper, PrintClient printClient,
                                  StoreDeviceMapStruct storeDeviceMapStruct, DeviceClient deviceClient,
                                  StoreService storeService, RedisTemplate redisTemplate, BroadcastService broadcastService,
                                  RedisService redisService, PushService pushService,
                                  TableService tableService, EnterpriseClientService enterpriseClientService) {
        this.storeDeviceMapper = storeDeviceMapper;
        this.printClient = printClient;
        this.storeDeviceMapStruct = storeDeviceMapStruct;
        this.deviceClient = deviceClient;
        this.storeService = storeService;
        this.redisTemplate = redisTemplate;
        this.broadcastService = broadcastService;
        this.redisService = redisService;
        this.pushService = pushService;
        this.tableService = tableService;
        this.enterpriseClientService = enterpriseClientService;
    }

    @Override
    // todo 设备绑定到门店可能会出先一个设备在数据库中有多条数据的情况，修改插入逻辑
    public boolean create(StoreDeviceDTO storeDeviceDTO) {
        // 设备信息同步到云端
        DeviceDTO deviceBindDTO = new DeviceDTO();
        deviceBindDTO.setEnterpriseGuid(storeDeviceDTO.getEnterpriseGuid());
        deviceBindDTO.setEnterpriseName(UserContextUtils.getEnterpriseName());
        deviceBindDTO.setStoreName(UserContextUtils.getStoreName());
        deviceBindDTO.setDeviceGuid(storeDeviceDTO.getDeviceGuid());
        deviceBindDTO.setStoreGuid(storeDeviceDTO.getStoreGuid());
        broadcastService.bindDeviceToCloud(deviceBindDTO);

        List<StoreDeviceDO> storeDeviceDOS = storeDeviceMapper.selectList(new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getDeviceNo, storeDeviceDTO.getDeviceNo())
                .eq(StoreDeviceDO::getStoreGuid, storeDeviceDTO.getStoreGuid())
                .eq(StoreDeviceDO::getDeviceGuid, storeDeviceDTO.getDeviceGuid())
                .eq(StoreDeviceDO::getIsBinding, Boolean.TRUE)
                .orderByDesc(StoreDeviceDO::getId));
        if (CollectionUtils.isEmpty(storeDeviceDOS)) {
            StoreDeviceDO storeDeviceDO;
            try {
                storeDeviceDO = storeDeviceMapStruct.toStoreDeviceDO(storeDeviceDTO)
                        .setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "storeDevice")))
                        .setGmtCreate(DateTimeUtils.now())
                        .setGmtCreate(DateTimeUtils.now())
                        .setGmtModified(DateTimeUtils.now())
                        .setIsBinding(Boolean.TRUE);
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成设备门店绑定guid失败");
            }
            log.info("保存门店设备绑定关系DO：{}", storeDeviceDO);
            if (BaseDeviceTypeEnum.CLOUD_PANEL.getCode() == storeDeviceDO.getDeviceType()) {
                // 如果是pad绑定，则默认将点餐模式设置为商家点餐
                storeDeviceDO.setPadOrderType(Constant.MERCHANT_ORDER);
            }
            if (storeDeviceMapper.insert(storeDeviceDO) != 1) {
                return false;
            }
            broadcastService.deviceBind(storeDeviceDTO);
            log.info("门店【{}】设备绑定，设备号：【{}】，设备GUID【{}】，清除主机缓存",
                    storeDeviceDO.getStoreGuid(),
                    storeDeviceDO.getDeviceNo(),
                    storeDeviceDO.getDeviceGuid());
            redisService.removeStoreMaster(storeDeviceDTO.getStoreGuid());
            return true;
        } else if (storeDeviceDOS.size() > 0) {
            log.info("该设备【{}】在门店【{}】上有多条数据，将进行清理", storeDeviceDTO.getDeviceNo(), storeDeviceDTO.getStoreGuid());
            StoreDeviceDO storeDeviceDO = storeDeviceDOS.get(0);
            storeDeviceMapper.delete(new LambdaQueryWrapper<StoreDeviceDO>()
                    .eq(StoreDeviceDO::getDeviceNo, storeDeviceDTO.getDeviceNo())
                    .eq(StoreDeviceDO::getStoreGuid, storeDeviceDTO.getStoreGuid())
                    .eq(StoreDeviceDO::getDeviceGuid, storeDeviceDTO.getDeviceGuid())
                    .eq(StoreDeviceDO::getIsBinding, Boolean.TRUE));
            if (storeDeviceMapper.insert(storeDeviceDO.setId(null).setGmtModified(DateTimeUtils.now())) != 1) {
                return false;
            }
            broadcastService.deviceBind(storeDeviceDTO);
            log.info("门店【{}】设备绑定，设备号：【{}】，设备GUID【{}】，清除主机缓存",
                    storeDeviceDO.getStoreGuid(),
                    storeDeviceDO.getDeviceNo(),
                    storeDeviceDO.getDeviceGuid());
            redisService.removeStoreMaster(storeDeviceDTO.getStoreGuid());
            return true;
        } else {
            log.info("该设备【{}】已绑定到门店【{}】上", storeDeviceDTO.getDeviceNo(), storeDeviceDTO.getStoreGuid());
            return true;
        }
    }

    @Override
    public List<StoreDeviceDTO> findStoreDevice(StoreDeviceQueryDTO storeDeviceQueryDTO) {
        // 查询已绑定且指定sort升序，gmtCreateTime升序
        LambdaQueryWrapper<StoreDeviceDO> wrapper = new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getIsBinding, true)
                .orderByAsc(StoreDeviceDO::getSort, StoreDeviceDO::getGmtCreate);
        if (StringUtils.hasLength(storeDeviceQueryDTO.getStoreGuid())) {
            wrapper.eq(StoreDeviceDO::getStoreGuid, storeDeviceQueryDTO.getStoreGuid());
        }
        if (storeDeviceQueryDTO.getDeviceType() != null) {
            wrapper.eq(StoreDeviceDO::getDeviceType, storeDeviceQueryDTO.getDeviceType());
        }
        log.info("查询门店设备结果：\n");
        List<StoreDeviceDO> storeDeviceDOList = storeDeviceMapper.selectList(wrapper);
        for (StoreDeviceDO storeDeviceDO : storeDeviceDOList) {
            if (storeDeviceDO.getDeviceType() == BaseDeviceTypeEnum.All_IN_ONE.getCode()) {
                storeDeviceDO.setSort(-1);
                break;
            }
        }
        List<StoreDeviceDTO> storeDeviceDTOS = storeDeviceMapStruct.toStoreDeviceDTOList(storeDeviceDOList);
        log.info("数据库查询结果：{}", JacksonUtils.writeValueAsString(storeDeviceDOList));
        log.info("返回给前端的数据结果：{}", JacksonUtils.writeValueAsString(storeDeviceDTOS));
        return storeDeviceDTOS;
    }

    @Override
    public boolean sort(List<StoreDeviceSortDTO> storeDeviceSortDTOS) {
        if (storeDeviceSortDTOS.stream().anyMatch(p -> p.getDeviceType() != 3)) {
            throw new BusinessException("仅一体机支持排序功能");
        }
        List<StoreDeviceDO> storeDeviceDOS = storeDeviceMapStruct.toStoreDeviceDOS(storeDeviceSortDTOS);

        // 同步master一体机到云端
        StoreDeviceSortDTO dto = storeDeviceSortDTOS.stream().min(Comparator.comparing(StoreDeviceSortDTO::getSort))
                .orElseThrow(() -> new BusinessException("无排序值最小的一体机设备"));
        log.info("调用云端print服务，同步master一体机到云端，storeGuid为：{}，deviceGuid为：{}",
                dto.getStoreGuid(), dto.getDeviceGuid());
        printClient.changeMasterDevice(new PrinterDTO().setStoreGuid(dto.getStoreGuid()).setDeviceId(dto.getDeviceGuid()));
        boolean result = storeDeviceMapper.batchSort(storeDeviceDOS) == storeDeviceSortDTOS.size();
        if (result) {
            log.info("门店【{}】设备排序，清除主机缓存", dto.getStoreGuid());
            redisService.removeStoreMaster(dto.getStoreGuid());
            StoreDeviceDTO storeMasterDevice = getMasterDeviceByStoreGuid(dto.getStoreGuid());
            if (StringUtils.hasText(storeMasterDevice.getDeviceGuid())) {
                pushService.pushMasterChanged(dto.getStoreGuid(), storeMasterDevice.getDeviceGuid());
            }
        }
        return result;
    }

    @Override
    public boolean unbind(StoreDeviceUnbindDTO storeDeviceUnbindDTO) {
        storeDeviceUnbindDTO.setUnbindUserGuid(UserContextUtils.getUserGuid());
        storeDeviceUnbindDTO.setUnbindUserName(UserContextUtils.getUserName());
        StoreDeviceDO storeDeviceDO = storeDeviceMapStruct.toStoreDeviceDO(storeDeviceUnbindDTO)
                .setIsBinding(false)
                .setGmtModified(DateTimeUtils.now())
                .setGmtUnbind(DateTimeUtils.now());
        LambdaQueryWrapper<StoreDeviceDO> wrapper = new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getStoreGuid, storeDeviceDO.getStoreGuid())
                .eq(StoreDeviceDO::getIsBinding, true);
        List<StoreDeviceDO> list = storeDeviceMapper.selectList(wrapper);
        wrapper.eq(StoreDeviceDO::getDeviceNo, storeDeviceDO.getDeviceNo());
        if (!this.update(storeDeviceDO, wrapper)) {
            throw new BusinessException("设备解绑失败");
        }

        list.stream().filter(p -> p.getDeviceNo().equals(storeDeviceDO.getDeviceNo())).findAny().ifPresent(p -> {
            // 同步云端解绑
            if (!storeDeviceUnbindDTO.getFromCloud()) {
                DeviceDTO unBindDevice = new DeviceDTO();
                unBindDevice.setDeviceGuid(p.getDeviceGuid());
                unBindDevice.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
                unBindDevice.setDeviceNo(p.getDeviceNo());
                unBindDevice.setStoreGuid(p.getStoreGuid());
                broadcastService.unBindDeviceToCloud(unBindDevice);
            }
            // 同步打印解绑
            log.info("调用print服务删除打印机，设备guid为：{}，门店guid为：{}，门店剩余的已绑定设备数：{}",
                    p.getDeviceGuid(), storeDeviceUnbindDTO.getStoreGuid(), list.size() - 1);
            printClient.deletePrinterByDevice(p.getDeviceGuid(),
                    storeDeviceUnbindDTO.getStoreGuid(), list.size() - 1);
            // 广播设备解绑
            StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
            storeDeviceDTO.setStoreGuid(p.getStoreGuid());
            storeDeviceDTO.setDeviceGuid(p.getDeviceGuid());
            storeDeviceDTO.setDeviceNo(p.getDeviceNo());
            broadcastService.deviceUnbind(storeDeviceDTO);
            // 当解绑一体机时，可能是主机
            if (BaseDeviceTypeEnum.All_IN_ONE.getCode() == p.getDeviceType()) {
                // 移除主机缓存、推送主机更改消息
                log.info("门店【{}】设备解绑，设备编号【{}】，设备GUID【{}】，清除主机缓存", p.getStoreGuid(), p.getDeviceNo(), p.getDeviceGuid());
                redisService.removeStoreMaster(p.getStoreGuid());
                StoreDeviceDTO storeMasterDevice = getMasterDeviceByStoreGuid(p.getStoreGuid());
                pushService.pushMasterChanged(p.getStoreGuid(), storeMasterDevice.getDeviceGuid());
            }
        });
        return true;
    }

    @Override
    public StoreDeviceDTO getMasterDeviceByStoreGuid(String storeGuid) {
        // 查询指定门店下的一体机设备已绑定状态下排序值最小的设备
        StoreDeviceDTO storeMasterCached = redisService.getStoreMaster(storeGuid);
        if (storeMasterCached != null) {
            return storeMasterCached;
        }
        StoreDeviceDO storeDeviceDO = storeDeviceMapper.selectOne(new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getStoreGuid, storeGuid)
                .eq(StoreDeviceDO::getDeviceType, 3)
                .eq(StoreDeviceDO::getIsBinding, true)
                .orderByAsc(StoreDeviceDO::getSort, StoreDeviceDO::getGmtCreate)
                .last(" limit 1"));
        log.info("根据storeGuid查询门店master打印机，storeGuid为：{}，enterpriseGuid为{}，结果集为：{}",
                storeGuid, UserContextUtils.getEnterpriseGuid(), JacksonUtils.writeValueAsString(storeDeviceDO));
        StoreDeviceDTO storeDeviceDTO;
        if (storeDeviceDO != null) {
            storeDeviceDTO = storeDeviceMapStruct.toStoreDeviceDTO(storeDeviceDO);
            log.info("查询到门店【{}】主机【{}】", storeDeviceDTO.getStoreGuid(), JacksonUtils.writeValueAsString(storeDeviceDTO));
        } else {
            storeDeviceDTO = new StoreDeviceDTO();
            storeDeviceDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            storeDeviceDTO.setStoreGuid(storeGuid);
            log.info("门店【{}】，无主机设备，设置默认值：storeDeviceDTO【{}】", storeGuid, JacksonUtils.writeValueAsString(storeDeviceDTO));
        }
        log.info("门店主机编号：【{}】，主机GUID【{}】，插入缓存", storeDeviceDTO.getDeviceNo(), storeDeviceDTO.getDeviceGuid());
        redisService.putStoreMaster(storeGuid, storeDeviceDTO);
        return storeDeviceDTO;
    }

    @Override
    public StoreDeviceDTO findDeviceStatus(String deviceNo) {
        // 分为 未注册 - 未注册、未绑定 - 未注册、已绑定 三种情况，通过enterpriseGuid和storeGuid判断
        DeviceStoreDTO deviceStoreDTO = deviceClient.findDeviceStatusInCloud(deviceNo);
        if (deviceStoreDTO == null) {
            log.info("当前设备【{}】未在云端注册", deviceNo);
            return new StoreDeviceDTO().setRegister(Boolean.FALSE);
        }
        StoreDeviceDTO dto = new StoreDeviceDTO()
                .setRegister(Boolean.TRUE)
                .setDeviceNo(deviceNo)
                .setDeviceGuid(deviceStoreDTO.getDeviceGuid());
        if (!StringUtils.hasText(deviceStoreDTO.getEnterpriseGuid())
                || !StringUtils.hasText(deviceStoreDTO.getStoreGuid())) {
            log.info("当前设备【{}】未绑定门店", deviceNo);
            return dto.setBinding(Boolean.FALSE);
        }
        try {
            EnterpriseIdentifier.setEnterpriseGuid(deviceStoreDTO.getEnterpriseGuid());
            // 设备进入时 查询商户后台库是否有设备绑定的数据，如果没有 则返回未绑定
            // 如果数据超过一条 则整理一下数据，让商户后台数据与云端数据保持一致
            List<StoreDeviceDO> storeDeviceDOS = storeDeviceMapper.selectList(new LambdaQueryWrapper<StoreDeviceDO>()
                    .eq(StoreDeviceDO::getDeviceNo, deviceNo)
                    .eq(StoreDeviceDO::getStoreGuid, deviceStoreDTO.getStoreGuid())
                    .eq(StoreDeviceDO::getDeviceGuid, deviceStoreDTO.getDeviceGuid())
                    .eq(StoreDeviceDO::getIsBinding, Boolean.TRUE)
                    .orderByDesc(StoreDeviceDO::getId));
            dto.setBinding(Boolean.TRUE);
            if (CollectionUtils.isEmpty(storeDeviceDOS)) {
                log.info("当前设备【{}】未绑定门店", deviceNo);
                dto.setBinding(Boolean.FALSE);
            } else if (storeDeviceDOS.size() > 1) {
                log.info("当前设备【{}】存在脏数据，清理数据", deviceNo);
                StoreDeviceDO storeDeviceDO = storeDeviceDOS.get(0);
                storeDeviceMapper.delete(new LambdaQueryWrapper<StoreDeviceDO>()
                        .eq(StoreDeviceDO::getDeviceNo, deviceNo)
                        .eq(StoreDeviceDO::getStoreGuid, deviceStoreDTO.getStoreGuid())
                        .eq(StoreDeviceDO::getDeviceGuid, deviceStoreDTO.getDeviceGuid())
                        .eq(StoreDeviceDO::getIsBinding, Boolean.TRUE));
                storeDeviceMapper.insert(storeDeviceDO.setId(null).setGmtModified(DateTimeUtils.now()));
            }
            StoreDTO storeDTO = storeService.queryStoreByGuid(deviceStoreDTO.getStoreGuid());
            if (ObjectUtils.isEmpty(storeDTO)) {
                log.warn("绑定的门店已经被删除，执行解绑程序 {}", JacksonUtils.writeValueAsString(deviceStoreDTO));
                StoreDeviceUnbindDTO storeDeviceUnbindDTO = new StoreDeviceUnbindDTO();
                BeanUtils.copyProperties(deviceStoreDTO, storeDeviceUnbindDTO);
                storeDeviceUnbindDTO.setDeviceNo(deviceNo);
                storeDeviceUnbindDTO.setStoreGuid(deviceStoreDTO.getStoreGuid());
                storeDeviceUnbindDTO.setUnbindUserGuid("system");
                storeDeviceUnbindDTO.setUnbindUserName("system");
                unbind(storeDeviceUnbindDTO);
                return dto.setBinding(Boolean.FALSE);
            }
            return dto.setStoreNo(storeDTO.getCode())
                    .setStoreName(storeDTO.getName())
                    .setEnterpriseGuid(deviceStoreDTO.getEnterpriseGuid())
                    .setDeviceGuid(deviceStoreDTO.getDeviceGuid())
                    .setStoreGuid(deviceStoreDTO.getStoreGuid());
        } finally {
            EnterpriseIdentifier.remove();
        }
    }

    @Override
    public void setMasterDevice(String storeGuid, String deviceGuid) {
        StoreDeviceDO one = getOne(new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getStoreGuid, storeGuid)
                .eq(StoreDeviceDO::getDeviceGuid, deviceGuid)
                .eq(StoreDeviceDO::getDeviceType, BaseDeviceTypeEnum.All_IN_ONE.getCode()));
        if (one == null) {
            throw new BusinessException("只有一体机可设置为主机");
        }
        update(new StoreDeviceDO().setSort(0), new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getStoreGuid, storeGuid));
        updateById(new StoreDeviceDO().setId(one.getId()).setSort(-1));
        log.info("门店【{}】设置主机，设备编号：【{}】， 设备GUID【{}】", storeGuid, one.getDeviceNo(), one.getDeviceGuid());
        redisService.removeStoreMaster(storeGuid);
        pushService.pushMasterChanged(storeGuid, deviceGuid);
    }

    @Override
    public PadOrderTypeRespDTO queryPadOrderType(PadOrderTypeReqDTO padOrderTypeReqDTO) {
        //pad点餐查询点餐模式返回dto
        PadOrderTypeRespDTO dto = new PadOrderTypeRespDTO();
        if (Objects.isNull(padOrderTypeReqDTO.getStoreGuid())) {
            dto.setPadOrderType(Constant.NOT_SET);
            return dto;
        }
        //绑定的设备
        StoreDeviceDO storeDeviceDO = storeDeviceMapper.selectOne(new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getDeviceNo, padOrderTypeReqDTO.getDeviceNo())
                .eq(StoreDeviceDO::getStoreGuid, padOrderTypeReqDTO.getStoreGuid())
                .eq(StoreDeviceDO::getIsBinding, 1));
        if (Objects.isNull(storeDeviceDO)) {
            throw new BusinessException("传入的设备编号未找到设备");
        }
        //如果还没有配置，直接返回
        if (Objects.isNull(storeDeviceDO.getPadOrderType())) {
            dto.setPadOrderType(Constant.NOT_SET);
            return dto;
        }
        dto.setPadOrderType(storeDeviceDO.getPadOrderType());

        // 获取门店绑定的运营主体
        MultiMemberDTO multiMemberDTO = enterpriseClientService.findMemberInfoByOrganizationGuid(padOrderTypeReqDTO.getStoreGuid());
        dto.setOperSubjectGuid(multiMemberDTO.getMultiMemberGuid());
        dto.setEnterpriseGuid(multiMemberDTO.getEnterpriseGuid());
        return dto;
    }

    @Override
    public TableInfoDTO queryBindingTableInfo(PadOrderTypeReqDTO padOrderTypeReqDTO) {
        String tableGuid = storeDeviceMapper.queryTableGuidByDeviceNo(padOrderTypeReqDTO.getStoreGuid(), padOrderTypeReqDTO.getDeviceNo());
        TableInfoDTO bindingTable = new TableInfoDTO();
        //已经绑定了桌台
        if (!StringUtils.isEmpty(tableGuid)) {
            TableBasicDTO tableBasicDTO = tableService.queryTableInfo(tableGuid);
            bindingTable = new TableInfoDTO().setTableName(tableBasicDTO.getTableCode())
                    .setAreaGuid(tableBasicDTO.getAreaGuid())
                    .setAreaName(tableBasicDTO.getAreaName());
            bindingTable.setTableGuid(tableGuid);
            bindingTable.setIsBind(true);
            return bindingTable;
        }
        return bindingTable.setIsBind(false);
    }


    @Override
    public List<PadAreaDTO> queryUnBindingTableInfo(PadOrderTypeReqDTO padOrderTypeReqDTO) {

        //已经绑定的桌台guid集合
        List<String> bindingTableGuids = storeDeviceMapper.queryTableGuidByStoreGuid(padOrderTypeReqDTO.getStoreGuid());
        //查询未绑定的桌台信息
        return tableService.queryUnBindingTableInfo(bindingTableGuids, padOrderTypeReqDTO.getStoreGuid());
    }


    @Override
    public boolean initializePadOrderTypeSet(PadOrderTypeReqDTO padOrderTypeReqDTO) {

        //初始化点餐模式
        storeDeviceMapper.updatePadOrderType(null, padOrderTypeReqDTO.getStoreGuid(),
                padOrderTypeReqDTO.getDeviceNo());
        return true;
    }

    @Override
    public boolean padOrderTypeSet(PadOrderTypeReqDTO padOrderTypeReqDTO) {

        if (Objects.equals(Constant.MERCHANT_ORDER, padOrderTypeReqDTO.getPadOrderType())) {
            //修改pad点餐模式类型
            storeDeviceMapper.updatePadOrderType(padOrderTypeReqDTO.getPadOrderType(), padOrderTypeReqDTO.getStoreGuid(),
                    padOrderTypeReqDTO.getDeviceNo());
            //取消pad桌台的绑定
            //storeDeviceMapper.cancelPadTableBinding(padOrderTypeReqDTO.getStoreGuid(), padOrderTypeReqDTO.getDeviceNo());
            return true;
        }
        if (StringUtils.isEmpty(padOrderTypeReqDTO.getTableGuid())) {
            throw new BusinessException("用户自主点餐模式必须传绑定桌台号");
        }
        //校验传入的桌台号是否被绑定
        int bingingCount = storeDeviceMapper.selectCount(new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getTableGuid, padOrderTypeReqDTO.getTableGuid())
                .eq(StoreDeviceDO::getStoreGuid, padOrderTypeReqDTO.getStoreGuid())
                .eq(StoreDeviceDO::getIsBinding, 1));
        if (bingingCount > 0) {
            TableBasicDTO tableInfo = tableService.queryTableInfo(padOrderTypeReqDTO.getTableGuid());
            if (ObjectUtils.isEmpty(tableInfo)) {
                log.error("未查询到桌台信息 tableGuid={}", padOrderTypeReqDTO.getTableGuid());
                throw new BusinessException("未查询到桌台信息");
            }
            throw new BusinessException(tableInfo.getTableCode() + "桌台已经被绑定，请重新选择桌台");
        }
        return this.update(new LambdaUpdateWrapper<StoreDeviceDO>()
                .set(StoreDeviceDO::getTableGuid, padOrderTypeReqDTO.getTableGuid())
                .set(StoreDeviceDO::getPadOrderType, padOrderTypeReqDTO.getPadOrderType())
                .eq(StoreDeviceDO::getStoreGuid, padOrderTypeReqDTO.getStoreGuid())
                .eq(StoreDeviceDO::getDeviceNo, padOrderTypeReqDTO.getDeviceNo())
                .eq(StoreDeviceDO::getIsBinding, 1)
        );
    }

    /**
     * 查询门店桌台对应设备信息
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guid
     * @return 设备信息
     */
    @Override
    public StoreDeviceDTO queryDeviceByStoreTable(PadOrderTypeReqDTO padOrderTypeReqDTO) {
        if (StringUtils.isEmpty(padOrderTypeReqDTO.getStoreGuid())) {
            throw new BusinessException("门店guid不能为空");
        }
        if (StringUtils.isEmpty(padOrderTypeReqDTO.getTableGuid())) {
            throw new BusinessException("桌台guid不能为空");
        }
        StoreDeviceDO deviceDO = this.getOne(new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getStoreGuid, padOrderTypeReqDTO.getStoreGuid())
                .eq(StoreDeviceDO::getTableGuid, padOrderTypeReqDTO.getTableGuid())
                .eq(StoreDeviceDO::getPadOrderType, Constant.USER_ONESELF_ORDER)
                .eq(StoreDeviceDO::getIsBinding, 1)
        );
        return storeDeviceMapStruct.toStoreDeviceDTO(deviceDO);
    }

    /**
     * 查询门店桌台对应设备信息列表
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guid
     * @return 设备信息列表
     */
    @Override
    public List<StoreDeviceDTO> listDeviceByStoreTable(PadOrderTypeReqDTO padOrderTypeReqDTO) {
        if (StringUtils.isEmpty(padOrderTypeReqDTO.getStoreGuid())) {
            throw new BusinessException("门店guid不能为空");
        }
        if (CollectionUtils.isEmpty(padOrderTypeReqDTO.getTableGuidList())) {
            throw new BusinessException("桌台guid列表不能为空");
        }
        List<StoreDeviceDO> list = this.list(new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getStoreGuid, padOrderTypeReqDTO.getStoreGuid())
                .in(StoreDeviceDO::getTableGuid, padOrderTypeReqDTO.getTableGuidList())
                .eq(StoreDeviceDO::getPadOrderType, Constant.USER_ONESELF_ORDER)
                .eq(StoreDeviceDO::getIsBinding, 1)
        );
        return storeDeviceMapStruct.storeDeviceDOList2StoreDeviceDTOList(list);
    }

    /**
     * 查询门店桌台所有设备信息列表
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guidList
     * @return 设备信息列表
     */
    @Override
    public List<StoreDeviceDTO> listAllDeviceByStoreTable(PadOrderTypeReqDTO padOrderTypeReqDTO) {
        if (StringUtils.isEmpty(padOrderTypeReqDTO.getStoreGuid())) {
            throw new BusinessException("门店guid不能为空");
        }
        if (StringUtils.isEmpty(padOrderTypeReqDTO.getTableGuid())) {
            throw new BusinessException("桌台guid不能为空");
        }
        List<StoreDeviceDO> list = this.list(new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getStoreGuid, padOrderTypeReqDTO.getStoreGuid())
                .eq(StoreDeviceDO::getTableGuid, padOrderTypeReqDTO.getTableGuid())
        );
        return storeDeviceMapStruct.storeDeviceDOList2StoreDeviceDTOList(list);
    }

    /**
     * 查询当前设备绑定的信息
     *
     * @param deviceId 设备Guid
     * @return 设备信息
     */
    @Override
    public StoreDeviceDTO queryDeviceByDeviceId(String deviceId) {
        if (StringUtils.isEmpty(deviceId)) {
            throw new BusinessException("设备Guid不能为空");
        }
        StoreDeviceDO deviceDO = this.getOne(new LambdaQueryWrapper<StoreDeviceDO>()
                .eq(StoreDeviceDO::getDeviceGuid, deviceId)
                .eq(StoreDeviceDO::getIsBinding, 1)
        );
        return storeDeviceMapStruct.toStoreDeviceDTO(deviceDO);
    }
}