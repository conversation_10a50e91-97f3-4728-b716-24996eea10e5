package com.holderzone.saas.store.organization.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 品牌表
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@Data
@Accessors(chain = true)
@TableName(value = "hso_brand")
public class BrandDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * 品牌guid
     */
    private String guid;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 品牌介绍
     */
    @TableField(strategy = FieldStrategy.IGNORED, el = "description,jdbcType=VARCHAR")
    private String description;

    /**
     * 品牌logo（oss下载地址）
     */
    private String logoUrl;

    /**
     * 是否启用（默认为1）
     */
    private Boolean isEnable;

    /**
     * 是否删除（默认为0）
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 修改人guid
     */
    private String modifiedUserGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 销售模式: 1 普通模式 2 菜谱方案
     */
    private Integer salesModel;

    /**
     * 门店是强制扎帐 0 否 1 是
     */
    private Integer isBuAccounts;
    /**
     * 门店上缴现金是否显示 0 否 1 是
     */
    private Integer isShowCash;

    /**
     * 多人交接班 0 否 1是
     */
    private Integer isMultiHandover;
}
