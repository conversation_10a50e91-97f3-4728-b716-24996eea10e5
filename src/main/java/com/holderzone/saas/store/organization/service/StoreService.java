package com.holderzone.saas.store.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.BusinessDayDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.store.store.BindupAccountsSaveDTO;
import com.holderzone.saas.store.dto.store.table.TableStatusDTO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;

/**
 * 门店 服务类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
public interface StoreService extends IService<OrganizationDO> {

    boolean createStore(StoreDTO storeDTO);

    boolean updateStore(StoreDTO storeDTO, boolean isFromCloud);

    boolean itemUploadUpdate(ItemUploadUpdateReq req);

    void createStoreByMdm(StoreDTO storeDTO);

    void updateStoreByMdm(StoreDTO storeDTO);

    /**
     * 更新门店扎帐信息
     */
    void updateBuAccounts(BindupAccountsSaveDTO bindupAccountsSaveDTO);

    boolean enableStore(String storeGuid);

    Page<StoreDTO> queryByCondition(QueryStoreDTO queryStoreDTO);

    /**
     * 目前用于根据门店名称和品牌查询门店列表
     * @param queryStoreDTO 查询条件
     * @return 返回门店列表
     */
    List<StoreDTO> listStoreByCondition(QueryStoreDTO queryStoreDTO);

    List<StoreDTO> queryByConditionNoPage(StoreParserDTO storeParserDTO);

    List<StoreDTO> list(StoreListReq dto);

    List<String> parseByCondition(StoreParserDTO storeParserDTO);

    List<String> parseByConditionNotUnion(StoreParserDTO storeParserDTO);

    boolean deleteStore(String storeGuid);

    StoreDTO queryStoreByGuid(String guid);

    StoreDTO queryStoreBaseByGuid(String guid);

    StoreBizDTO queryStoreBizByGuid(String guid);

    StoreDTO queryStoreByCode(String storeCode);

    List<StoreDTO> queryStoreByRegionList(List<RegionDTO> regionDTOList);

    List<StoreDTO> queryStoreByBrandList(List<String> brandGuidList);

    boolean queryDeleteCondition(String storeGuid);

    BrandDTO queryBrandByStoreGuid(String storeGuid);

    BrandDTO queryBrandByStoreGuidForMember(String storeGuid);

    List<StoreDTO> queryStoreByIdList(List<String> storeGuidList);

    List<StoreDTO> queryStoreByIdListAndBrandId(SingleDataDTO singleDataDTO);

    List<StoreDTO> queryStoreDetailByIdList(List<String> storeGuidList);

    List<StoreDTO> queryStoreAndBrandByIdList(List<String> storeGuidList);

    List<StoreDTO> queryAllStore();

    List<String> queryAllStoreGuid();

    Page<StoreDTO> queryStoreByCondition(QueryStoreDTO queryStoreDTO);

    List<StoreDTO> queryStoreByCityAndBrand(StoreDTO storeDTO);

    /**
     * 根据门店guid集合及日期时间获取所属营业日期
     *
     * @param businessDateReqDTO 门店guid和查询的时间，如果为空则为当前时间
     * @return 该时间在门店所属的营业日期
     */
    LocalDate queryBusinessDay(BusinessDateReqDTO businessDateReqDTO);

    /**
     * 查询门店信息及日期时间获取所属营业日期
     */
    StoreDTO queryBusinessDayInfo(BusinessDateReqDTO businessDateReqDTO);

    /**
     * 根据门店guid查询门店营业时间段（一体机报表的时间查询条件用）
     *
     * @param storeGuid 门店guid
     * @return StoreBusinessDateDTO
     */
    StoreBusinessDateDTO queryBusinessDate(String storeGuid);

    /**
     * pad开始点餐返回dto
     *
     * @param storeGuid 门店guid
     * @return 查询结果
     */
    PadStartOrderRespDTO getPadStartOrderInfo(String storeGuid);

    /**
     * 根据门店guidList查询门店关联的品牌信息
     *
     * @param storeGuidList 门店guidList
     * @return 品牌信息
     */
    List<BrandDTO> queryBrandListByStoreGuidList(List<String> storeGuidList);

    /**
     * 批量根据门店名称查找门店
     *
     * @param storeNameList 门店名字列表
     * @return 门店信息列表
     */
    List<StoreDTO> queryStoreByNameList(List<String> storeNameList);

    /**
     * 验证扎帐功能
     * @param storeGuid
     * @param tableStatusDTOS
     */
    HashMap<String, String> checkBindUpAccount(String storeGuid,String userGuid,String userName, List<TableStatusDTO> tableStatusDTOS);

    /**
     * 修改门店，是否扎帐，是否展示收银，当天是否扎帐
     */
    void batchUpdateBuAccounts(List<String> storeList, StoreDTO storeDTO);

    /**
     * 查询门店营业日
     *
     * @param businessDayDTOList 企业，门店
     * @return 门店营业日
     */
    List<BusinessDayDTO> queryStoreBusinessDay(List<BusinessDayDTO> businessDayDTOList);

    BrandStoreDetailDTO queryStoreBrandDetail(String storeGuid, String brandGuid);
}
