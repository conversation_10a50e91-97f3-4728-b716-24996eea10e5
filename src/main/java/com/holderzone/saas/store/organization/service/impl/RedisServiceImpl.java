package com.holderzone.saas.store.organization.service.impl;

import com.holderzone.saas.store.dto.store.store.StoreAllInfoDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.organization.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RedisServiceImpl
 * @date 18-12-21 下午4:00
 * @description Redis操作相关接口实现
 * @program holder-saas-store-staff
 */
@Service
@SuppressWarnings("unchecked")
public class RedisServiceImpl implements RedisService {

    /**
     * redis key模版
     */
    private final String REDIS_KEY = "organization:storeInfo:{0}";

    private final String MASTER_KEY = "store:device:{0}";

    private final RedisTemplate redisTemplate;

    @Autowired
    public RedisServiceImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public StoreDeviceDTO getStoreMaster(String storeGuid) {
        final String masterKey = MessageFormat.format(MASTER_KEY, storeGuid);
        return (StoreDeviceDTO) redisTemplate.opsForValue().get(masterKey);
    }

    @Override
    public void putStoreMaster(String storeGuid, StoreDeviceDTO storeDeviceDTO) {
        final String masterKey = MessageFormat.format(MASTER_KEY, storeGuid);
        redisTemplate.opsForValue().set(masterKey, storeDeviceDTO, 6, TimeUnit.HOURS);
    }

    @Override
    public void removeStoreMaster(String storeGuid) {
        redisTemplate.delete(MessageFormat.format(MASTER_KEY, storeGuid));
    }

    @Override
    public void putStoreAllInfo(StoreAllInfoDTO storeAllInfoDTO) {
        final String format = MessageFormat.format(
                REDIS_KEY,
                storeAllInfoDTO.getOrganizationGuid()
        );
        redisTemplate.opsForValue().set(format, storeAllInfoDTO, 6, TimeUnit.HOURS);
    }

    @Override
    public StoreAllInfoDTO getStoreAllInfo(String storeGuid) {
        String redisKey = MessageFormat.format(REDIS_KEY, storeGuid);
        return (StoreAllInfoDTO) redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    public void deleteStoreAllInfoByStoreGuid(String storeGuid) {
        redisTemplate.delete(MessageFormat.format(REDIS_KEY, storeGuid));
    }
}
