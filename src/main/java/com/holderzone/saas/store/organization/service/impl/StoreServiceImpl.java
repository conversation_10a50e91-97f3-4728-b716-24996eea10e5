package com.holderzone.saas.store.organization.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.item.resp.PricePlanBingStoreRespDTO;
import com.holderzone.saas.store.dto.order.BusinessDayDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.store.store.BindupAccountsDTO;
import com.holderzone.saas.store.dto.store.store.BindupAccountsSaveDTO;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.dto.store.table.TableStatusDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.enums.SpecialProvinceEnum;
import com.holderzone.saas.store.enums.common.ConfigEnum;
import com.holderzone.saas.store.organization.constant.Constant;
import com.holderzone.saas.store.organization.constant.OrganizationTypeEnum;
import com.holderzone.saas.store.organization.domain.BrandDO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.domain.StoreBrandDO;
import com.holderzone.saas.store.organization.feign.ConfigFeignService;
import com.holderzone.saas.store.organization.feign.EnterpriseClientService;
import com.holderzone.saas.store.organization.feign.WxStoreOrderConfigService;
import com.holderzone.saas.store.organization.mapper.BrandMapper;
import com.holderzone.saas.store.organization.mapper.OrganizationMapper;
import com.holderzone.saas.store.organization.mapper.StoreAttachMapper;
import com.holderzone.saas.store.organization.mapper.StoreBrandMapper;
import com.holderzone.saas.store.organization.mapstruct.BrandMapstruct;
import com.holderzone.saas.store.organization.mapstruct.StoreMapstruct;
import com.holderzone.saas.store.organization.service.*;
import com.holderzone.saas.store.organization.service.remote.BusinessClient;
import com.holderzone.saas.store.organization.service.remote.EnterpriseClient;
import com.holderzone.saas.store.organization.service.remote.ItemClient;
import com.holderzone.saas.store.organization.service.remote.UserClient;
import com.holderzone.saas.store.organization.utils.DynamicHelper;
import com.holderzone.saas.store.organization.utils.GuidUtils;
import com.holderzone.saas.store.organization.utils.RedisUtils;
import com.holderzone.saas.store.organization.vo.BindupAccountsDo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.integration.redis.util.RedisLockRegistry;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 门店 服务实现类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@Slf4j
@Service
@AllArgsConstructor
public class StoreServiceImpl extends ServiceImpl<OrganizationMapper, OrganizationDO> implements StoreService {

    private final static String STORE_BUSINESS_CONFIG = "store:business:";
    private final OrganizationMapper organizationMapper;
    private final StoreMapstruct storeMapstruct;
    private final UserClient userClient;
    private final EnterpriseClient enterpriseClient;
    private final StoreBrandMapper storeBrandMapper;
    private final StoreBrandService storeBrandService;
    private final BrandMapstruct brandMapstruct;
    private final BrandMapper brandMapper;
    private final DistributedIdService distributedIdService;
    private final BroadcastService broadcastService;
    private final RedisUtils redisUtils;
    private final StoreAttachMapper storeAttachMapper;
    private final ItemClient itemClient;
    private final WxStoreOrderConfigService wxStoreOrderConfigService;
    private final RedisLockRegistry redisLockRegistry;
    private final ConfigFeignService configFeignService;
    private final BusinessClient businessClient;

    private final BrandService brandService;

    private final DynamicHelper dynamicHelper;

    private final EnterpriseClientService enterpriseClientService;

    public static final String CREATE_STORE_REDIS_KEY = "CREATE_STORE";


    static DateTimeFormatter df = DateTimeFormatter.ofPattern("HH:mm");
    static DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    static SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createStore(StoreDTO storeDTO) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        Lock lock = redisLockRegistry.obtain(CREATE_STORE_REDIS_KEY + enterpriseGuid);
        try {
            boolean tryLock = lock.tryLock(30, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BusinessException("超时请重试");
            }

            this.validateStoreName(null, storeDTO.getName());
            String paramStoreGuid = storeDTO.getGuid();
            String paramStoreCode = storeDTO.getCode();
            String createUserGuid = storeDTO.getCreateUserGuid() != null
                    ? storeDTO.getCreateUserGuid()
                    : UserContextUtils.getUserGuid();
            String modifiedUserGuid = storeDTO.getModifiedUserGuid() != null
                    ? storeDTO.getModifiedUserGuid()
                    : UserContextUtils.getUserGuid();
            storeDTO.setGuid(StringUtils.hasText(paramStoreGuid)
                    ? paramStoreGuid
                    : String.valueOf(distributedIdService.nextOrganizationGuid()));
            storeDTO.setCode(StringUtils.hasText(paramStoreCode)
                    ? paramStoreCode
                    : this.getOnlyOrganizationCode());
            storeDTO.setCreateUserGuid(createUserGuid);
            storeDTO.setModifiedUserGuid(modifiedUserGuid);
            String storeBrand = storeBrandService.createStoreBrand(storeDTO.getGuid(), storeDTO.getBelongBrandGuid(), createUserGuid, createUserGuid);
            storeDTO.setBelongBrandGuid(storeBrand);
            log.info("当前新建门店品牌guid:{}", JacksonUtils.writeValueAsString(storeBrand));
            //查询品牌信息
            BrandDTO brandDTO = brandService.queryBrandByGuid(storeBrand);
            log.info("查询到品牌信息列表:{}", JacksonUtils.writeValueAsString(brandDTO));
            storeDTO.setIsBuAccounts(brandDTO.getIsBuAccounts());
            storeDTO.setIsShowCash(brandDTO.getIsShowCash());

            OrganizationDO organizationDO = storeMapstruct.storeDTO2DTO(storeDTO)
                    .setType(2)
                    .setParentIds(storeDTO.getParentIds())
                    .setGmtCreate(DateTimeUtils.now())
                    .setGmtModified(DateTimeUtils.now());
            organizationDO.setIsBuAccounts(brandDTO.getIsBuAccounts());
            organizationDO.setIsShowCash(brandDTO.getIsShowCash());
            log.info("新建门店成功，门店信息:{}", JacksonUtils.writeValueAsString(organizationDO));
            baseMapper.saveBatch(Lists.newArrayList(organizationDO));

            // 广播门店创建消息
            storeDTO.setMchntTypeCode(userClient.queryMchntType());
            broadcastService.storeCreated(storeDTO);

            // 同步到云平台：商户端创建门店
            if (!StringUtils.hasText(paramStoreGuid)) {
                broadcastService.createStoreInCloud(organizationDO);
            }

            // 同步到ERP服务
            broadcastService.createWareHouse(organizationDO);
        } catch (Exception e) {
            log.error("创建门店失败:{}", e.getMessage());
            e.printStackTrace();
        } finally {
            lock.unlock();
        }
        return true;
    }

    @Override
    public boolean itemUploadUpdate(ItemUploadUpdateReq req) {

        boolean ret = this.organizationMapper.updateItemUpload(req.getStoreGuid(), req.getIsItemUpload()) > 0;
        if (ret) {
            // 广播门店修改消息
            OrganizationDO original = this.getOne(new LambdaQueryWrapper<OrganizationDO>()
                    .eq(OrganizationDO::getGuid, req.getStoreGuid())
                    .in(OrganizationDO::getType, Lists.newArrayList(2, 3)));
            StoreDTO storeDTO = new StoreDTO();
            BeanUtils.copyProperties(original, storeDTO);
            broadcastService.storeUpdated(storeDTO);
        }
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStore(StoreDTO storeDTO, boolean isFromCloud) {
        log.info("门店更新:{}", JacksonUtils.writeValueAsString(storeDTO));
        if (!StringUtils.hasText(storeDTO.getCityCode())) {
            storeDTO.setCityCode(null);
        }
        if (!StringUtils.hasText(storeDTO.getCityName())) {
            storeDTO.setCityName(null);
        }

        String modifiedUserGuid = UserContextUtils.getUserGuid();
        OrganizationDO storeDO = storeMapstruct.storeDTO2DTO(storeDTO)
                .setModifiedUserGuid(modifiedUserGuid)
                .setGmtModified(DateTimeUtils.now());

        OrganizationDO original = this.getOne(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, storeDTO.getGuid())
                .in(OrganizationDO::getType, Lists.newArrayList(2, 3)));

        if (StringUtils.hasText(storeDO.getName())) {
            // 判断同一企业下门店名称是否重复
            validateStoreName(original.getName(), storeDO.getName());
        }

        // 更新门店品牌
        if (StringUtils.hasText(storeDTO.getBelongBrandGuid()) && !ObjectUtils.isEmpty(storeDTO.getIsSelfBuildItems())) {
            //如果有该门店使用价格方案，判断有无价格方案的绑定，有则不能切换品牌
            if (!ObjectUtils.isEmpty(storeDTO.getIsSelfBuildItems())
                    && Constant.FALSE.equals(storeDTO.getIsSelfBuildItems())) {
                List<PricePlanBingStoreRespDTO> planStoreList = itemClient.getPlanStoreByStoreGuid(storeDTO.getGuid());
                if (!CollectionUtils.isEmpty(planStoreList)
                        && !(planStoreList.get(0).getBrandGuid().equals(storeDTO.getBelongBrandGuid()))) {
                    throw new BusinessException(Constant.CAN_NOT_SWITCH_BRANDS);
                }
            }
            StoreBrandDO storeBrandDO = new StoreBrandDO();
            storeBrandDO.setBrandGuid(storeDTO.getBelongBrandGuid());
            storeBrandDO.setModifiedUserGuid(modifiedUserGuid);
            storeBrandMapper.update(storeBrandDO, new LambdaQueryWrapper<StoreBrandDO>()
                    .eq(StoreBrandDO::getStoreGuid, storeDTO.getGuid()));
        }

        // 更新门店
        log.info("门店更新：{}", JacksonUtils.writeValueAsString(storeDO));
        this.update(storeDO, new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, storeDO.getGuid()));

        // 更新估清配置里的门店营业时间
        if (!ObjectUtils.isEmpty(storeDTO.getBusinessStart())) {
            ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
            configReqQueryDTO.setDicCode(ConfigEnum.DELETE_ESTIMATE_ITEM.getCode());
            configReqQueryDTO.setStoreGuid(storeDO.getGuid());
            ConfigRespDTO configRespDTO = configFeignService.selectEstimateResetTime(configReqQueryDTO);
            LocalDateTime now = LocalDateTime.now();
            if (!ObjectUtils.isEmpty(configRespDTO)) {
                log.info("更新门店营业时间 storeGuid={} businessStart={}", storeDO.getGuid(), storeDTO.getBusinessStart());
                ConfigReqDTO updateConfig = new ConfigReqDTO();
                updateConfig.setGuid(configRespDTO.getGuid());
                updateConfig.setStoreGuid(configRespDTO.getStoreGuid());
                updateConfig.setDicCode(configRespDTO.getDicCode());
                updateConfig.setDicName(configRespDTO.getDicName());
                updateConfig.setDictValue(String.valueOf(storeDTO.getBusinessStart()));
                updateConfig.setEnterpriseGuid(configRespDTO.getEnterpriseGuid());
                updateConfig.setGmtModified(now);
                configFeignService.saveEstimateResetTime(updateConfig);
            }

            // 往前修改营业时间，立即执行一次恢复估清商品
            ArrayList<String> list = new ArrayList<>();
            list.add(storeDO.getGuid());

            // 当查询时间小于开始时间时，营业日期为前一天的营业日
            LocalDate businessDay = now.toLocalDate();
            if (original.getBusinessStart().compareTo(now.toLocalTime()) > 0) {
                businessDay = businessDay.minusDays(1);
            }
            if (storeDTO.getBusinessStart().isBefore(now.toLocalTime()) && businessDay.isBefore(LocalDate.now())) {
                Map<String, List<String>> request = new HashMap<>();
                request.put(UserContextUtils.getEnterpriseGuid(), list);
                itemClient.storeItemEstimateCancel(request);
            }
        }

        if (!ObjectUtils.isEmpty(storeDTO.getBusinessEnd())) {
            String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
            ConfigReqQueryDTO configReqDTO = new ConfigReqQueryDTO();
            configReqDTO.setDicCode(ConfigEnum.BIND_UP_ACCOUNTS.getCode());
            configReqDTO.setStoreGuid(original.getGuid());
            configReqDTO.setEnterpriseGuid(enterpriseGuid);
            ConfigRespDTO configByCode = configFeignService.getConfigByCode(configReqDTO);
            if (configByCode != null) {
                ConfigReqDTO dto = new ConfigReqDTO();
                dto.setGuid(configByCode.getGuid());
                dto.setDicCode(ConfigEnum.BIND_UP_ACCOUNTS.getCode());
                dto.setEnterpriseGuid(enterpriseGuid);
                dto.setStoreGuid(original.getGuid());
                dto.setDictValue(String.valueOf(storeDTO.getBusinessEnd()));
                dto.setDicName(ConfigEnum.BIND_UP_ACCOUNTS.getDesc());
                configFeignService.saveEstimateResetTime(dto);
            }
        }


        // 修改门店时清除缓存里的门店营业数据。 查询时先中缓存 再中数据库
        redisUtils.delete(STORE_BUSINESS_CONFIG + storeDTO.getGuid());

        // 广播门店修改消息
        broadcastService.storeUpdated(storeDTO);

        // 同步到云平台
        if (!isFromCloud) {
            broadcastService.updateStoreInCloud(storeDO);
        }

        // 同步到ERP服务
        if (!original.getName().equals(storeDTO.getName())) {
            broadcastService.updateWareHouse(storeDTO);
        }

        return true;
    }

    @Override
    public void createStoreByMdm(StoreDTO storeDTO) {
        Assert.notNull(storeDTO.getGuid(), "门店标识不得为空");
        Assert.notNull(storeDTO.getBelongBrandGuid(), "门店品牌不得为空");

        OrganizationDO storeNewInSql = this.getOne(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, storeDTO.getGuid())
                .in(OrganizationDO::getType, Lists.newArrayList(2, 3)));
        StoreDTO storeNewDTO = storeMapstruct.organizationDO2DTO(storeNewInSql, storeDTO.getBelongBrandGuid());

        // 广播门店创建消息
        storeDTO.setMchntTypeCode(userClient.queryMchntType());
        broadcastService.storeCreated(storeNewDTO);

        // 同步到云平台：商户端创建门店
        broadcastService.createStoreInCloud(storeNewInSql);

        // 同步到ERP服务
        broadcastService.createWareHouse(storeNewInSql);
    }

    @Override
    public void updateStoreByMdm(StoreDTO storeDTO) {
        Assert.notNull(storeDTO.getGuid(), "门店标识不得为空");
        Assert.notNull(storeDTO.getName(), "门店名称不得为空");
        Assert.notNull(storeDTO.getBelongBrandGuid(), "门店品牌不得为空");
        OrganizationDO storeNewInSql = this.getOne(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, storeDTO.getGuid())
                .in(OrganizationDO::getType, Lists.newArrayList(2, 3)));

        // 修改门店时清除缓存里的门店营业数据。 查询时先中缓存 再中数据库
        redisUtils.delete(STORE_BUSINESS_CONFIG + storeDTO.getGuid());

        StoreDTO storeNewDTO = storeMapstruct.organizationDO2DTO(storeNewInSql, storeDTO.getBelongBrandGuid());

        // 广播门店修改消息
        broadcastService.storeUpdated(storeNewDTO);

        // 同步到云平台
        broadcastService.updateStoreInCloud(storeNewInSql);

        // 同步到ERP服务
        if (!storeNewInSql.getName().equals(storeDTO.getName())) {
            broadcastService.updateWareHouse(storeNewDTO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBuAccounts(BindupAccountsSaveDTO bindupAccountsSaveDTO) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        String brandGuid = bindupAccountsSaveDTO.getBrandGuid();
        Integer isBuAccounts = bindupAccountsSaveDTO.getIsBuAccounts();
        Integer isShowCash = bindupAccountsSaveDTO.getIsShowCash();
        Integer isMultiHandover = bindupAccountsSaveDTO.getIsMultiHandover();
        BrandDTO brandDTO = new BrandDTO()
                .setGuid(brandGuid)
                .setIsBuAccounts(isBuAccounts)
                .setIsShowCash(isShowCash)
                .setIsMultiHandover(isMultiHandover);
        Boolean success = brandService.updateBrandAccountStatus(brandDTO);
        if (!success) {
            throw new BusinessException("品牌不存在");
        }
        // 查询品牌下所有的门店
        List<StoreDTO> storeList = queryStoreByBrandList(Lists.newArrayList(brandGuid));
        if (CollectionUtils.isEmpty(storeList)) {
            log.info("品牌下门店不存在:{}", brandGuid);
            return;
        }
        List<String> storeGuids = storeList.stream().map(StoreDTO::getGuid).collect(Collectors.toList());
        // 更新门店配置
        StoreDTO storeDTO = new StoreDTO()
                .setIsBuAccounts(isBuAccounts)
                .setIsShowCash(isShowCash)
                .setIsMultiHandover(isMultiHandover);
        organizationMapper.batchUpdateBuAccounts(storeGuids, storeDTO);
        // 扎帐定时任务配置更新
        for (StoreDTO store : storeList) {
            LocalTime businessEnd = store.getBusinessEnd();
            String localTime = df.format(businessEnd);
            ConfigReqDTO updateConfig = new ConfigReqDTO();
            updateConfig.setStoreGuid(store.getGuid());
            updateConfig.setDicCode(ConfigEnum.BIND_UP_ACCOUNTS.getCode());
            updateConfig.setDicName(ConfigEnum.BIND_UP_ACCOUNTS.getDesc());
            updateConfig.setEnterpriseGuid(enterpriseGuid);
            updateConfig.setDictValue(localTime);
            // 移除
            if (isBuAccounts == 0) {
                updateConfig.setIsEnable(1);
                configFeignService.saveEstimateResetTime(updateConfig);
            }
            // 添加
            if (isBuAccounts == 1) {
                updateConfig.setIsEnable(0);
                LocalDateTime now = LocalDateTime.now();
                updateConfig.setGmtCreate(now);
                updateConfig.setGmtModified(now);
                configFeignService.saveEstimateResetTime(updateConfig);
            }
        }
    }

    private void validateStoreName(String oriName, String newName) {
        if (newName.equals(oriName)) {
            return;
        }
        Integer result = organizationMapper.selectCount(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getName, newName)
                .in(OrganizationDO::getType, Lists.newArrayList(2, 3))
        );
        if (null != result && result >= 1) {
            throw new BusinessException("门店名称已存在，请修改");
        }
    }

    @Override
    public boolean enableStore(String storeGuid) {
        Wrapper<OrganizationDO> wrapper = identifyWrapper(storeGuid);
        OrganizationDO originalDO = this.getOne(wrapper);
        if (originalDO == null) {
            return false;
        }
        originalDO.setIsEnable(!originalDO.getIsEnable());
        this.update(originalDO, wrapper);
        broadcastService.enableStoreInCloud(originalDO);
        broadcastService.updateMemberStore(storeMapstruct.organizationDO2DTO(originalDO, null));
        return true;
    }

    private Wrapper<OrganizationDO> identifyWrapper(String storeGuid) {
        return new LambdaQueryWrapper<OrganizationDO>().eq(OrganizationDO::getGuid, storeGuid);
    }

    @Override
    public Page<StoreDTO> queryByCondition(QueryStoreDTO queryStoreDTO) {
        if (CollectionUtils.isEmpty(queryStoreDTO.getStoreGuidList())) {
            log.info("当前用户没有能管理的门店");
            return new Page<>(queryStoreDTO.getCurrentPage(), queryStoreDTO.getPageSize());
        }
        List<OrganizationDO> storeInSql = organizationMapper.selectByCondition(queryStoreDTO);
        if (CollectionUtils.isEmpty(storeInSql)) {
            return new Page<>(queryStoreDTO.getCurrentPage(), queryStoreDTO.getPageSize());
        }
        List<StoreDTO> storeDTOList = getStoreDTOS(storeInSql);
        return new Page<>(queryStoreDTO.getCurrentPage(), queryStoreDTO.getPageSize(), queryStoreDTO.getTotalCount(), storeDTOList);
    }

    @Override
    public List<StoreDTO> listStoreByCondition(QueryStoreDTO queryStoreDTO) {
        List<OrganizationDO> storeInSql = organizationMapper.selectByCondition(queryStoreDTO);
        if(CollectionUtil.isEmpty(storeInSql)){
            return Collections.emptyList();
        }
        return storeMapstruct.organizationList2DTOList(storeInSql);
    }

    @Override
    public List<StoreDTO> queryByConditionNoPage(StoreParserDTO storeParserDTO) {
        List<OrganizationDO> storeInSql = organizationMapper.selectByConditionNoPage(storeParserDTO);
        if (CollectionUtils.isEmpty(storeInSql)) {
            return Collections.emptyList();
        }
        List<StoreDTO> storeDTOList = getStoreDTOS(storeInSql);
        return storeDTOList.stream()
                .sorted(Comparator.comparing(StoreDTO::getGmtModified).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<StoreDTO> list(StoreListReq dto) {
        StoreParserDTO storeParserDTO = new StoreParserDTO();
        BeanUtils.copyProperties(dto, storeParserDTO);
        List<OrganizationDO> storeInSql = organizationMapper.selectByConditionNoPage(storeParserDTO);
        return storeMapstruct.organizationList2DTOList(storeInSql);
    }

    private List<StoreDTO> getStoreDTOS(List<OrganizationDO> storeInSql) {
        // 门店、门店品牌
        List<String> orgGuidInSql = storeInSql.stream().map(OrganizationDO::getGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgGuidInSql)) {
            return Collections.emptyList();
        }
        List<StoreBrandDO> storeBrandInSql = storeBrandMapper.selectList(
                new LambdaQueryWrapper<StoreBrandDO>().in(StoreBrandDO::getStoreGuid, orgGuidInSql)
        );
        Map<String, StoreBrandDO> storeBrandMap = storeBrandInSql.stream()
                .collect(Collectors.toMap(StoreBrandDO::getStoreGuid, Functions.identity()));
        List<StoreDTO> storeDTOList = storeInSql.stream()
                .map(store -> {
                    String brandGuid = Optional.ofNullable(storeBrandMap.get(store.getGuid()))
                            .map(StoreBrandDO::getBrandGuid).orElse("");
                    return storeMapstruct.organizationDO2DTO(store, brandGuid);
                })
                .collect(Collectors.toList());

        // 门店产品信息
        Map<String, List<StoreProductDTO>> storeProductMap = userClient.queryProductByIdList(orgGuidInSql);
        if (!CollectionUtils.isEmpty(storeProductMap)) {
            storeDTOList.forEach(storeDTO -> storeDTO.setProductDTOList(storeProductMap.get(storeDTO.getGuid())));
        }
        return storeDTOList;
    }

    @Override
    public List<String> parseByCondition(StoreParserDTO storeParserDTO) {
        Set<String> result = new HashSet<>(Optional.ofNullable(storeParserDTO.getStoreGuidList())
                .orElse(Collections.emptyList()));
        result.addAll(organizationMapper.parseByCondition(storeParserDTO));
        return new ArrayList<>(result);
    }

    @Override
    public List<String> parseByConditionNotUnion(StoreParserDTO storeParserDTO) {
        Set<String> result = new HashSet<>(Optional.ofNullable(storeParserDTO.getStoreGuidList())
                .orElse(Collections.emptyList()));
        result.addAll(organizationMapper.parseByConditionNotUnion(storeParserDTO));
        return new ArrayList<>(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStore(String storeGuid) {
        if (!this.queryDeleteCondition(storeGuid)) {
            throw new BusinessException("删除失败，门店开通服务未到期");
        }

        this.remove(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, storeGuid)
                .in(OrganizationDO::getType, Lists.newArrayList(2, 3)));
        storeBrandMapper.delete(new LambdaQueryWrapper<StoreBrandDO>()
                .eq(StoreBrandDO::getStoreGuid, storeGuid));

        broadcastService.deleteStoreInCloud(storeGuid);

        return true;
    }

    @Override
    public StoreDTO queryStoreByGuid(String guid) {
        OrganizationDO organizationDO = organizationMapper.selectOne(
                new LambdaQueryWrapper<OrganizationDO>().eq(OrganizationDO::getGuid, guid)
        );
        StoreBrandDO storeBrandDO = storeBrandMapper.selectOne(
                new LambdaQueryWrapper<StoreBrandDO>().eq(StoreBrandDO::getStoreGuid, guid)
        );
        String belongBrandGuid = storeBrandDO == null ? null : storeBrandDO.getBrandGuid();
        return storeMapstruct.organizationDO2DTO(organizationDO, belongBrandGuid);
    }

    @Override
    public StoreDTO queryStoreBaseByGuid(String guid){
        StoreDTO storeBase = organizationMapper.getStoreBase(guid);
        if(storeBase == null){
            return null;
        }
        //设置营业时间
        if(storeBase.getBusinessStart() != null && storeBase.getBusinessStart().isAfter(LocalTime.now())){
            storeBase.setBusinessDay(LocalDate.now().minusDays(1));
        }else {
            storeBase.setBusinessDay(LocalDate.now());
        }
        return storeBase;
    }

    @Override
    public StoreBizDTO queryStoreBizByGuid(String guid) {
        StoreBizDTO storeBizDTO = (StoreBizDTO) redisUtils.get(STORE_BUSINESS_CONFIG + guid);
        if (storeBizDTO == null) {
            StoreDTO storeDTO = queryStoreByGuid(guid);
            storeBizDTO = new StoreBizDTO();
            storeBizDTO.setStoreGuid(guid);
            storeBizDTO.setAddressDetail(storeDTO.getAddressDetail());
            storeBizDTO.setBusinessEnd(storeDTO.getBusinessEnd());
            storeBizDTO.setBusinessStart(storeDTO.getBusinessStart());
            storeBizDTO.setContactTel(storeDTO.getContactTel());

            redisUtils.setEx(STORE_BUSINESS_CONFIG + guid, storeBizDTO, 7L, TimeUnit.DAYS);
        }
        return storeBizDTO;
    }

    @Override
    public StoreDTO queryStoreByCode(String storeCode) {
        OrganizationDO organizationDO = organizationMapper.selectOne(
                new LambdaQueryWrapper<OrganizationDO>().eq(OrganizationDO::getCode, storeCode)
        );
        StoreBrandDO storeBrandDO = storeBrandMapper.selectOne(
                new LambdaQueryWrapper<StoreBrandDO>().eq(StoreBrandDO::getStoreGuid, organizationDO.getGuid())
        );
        String belongBrandGuid = storeBrandDO == null ? null : storeBrandDO.getBrandGuid();
        return storeMapstruct.organizationDO2DTO(organizationDO, belongBrandGuid);
    }

    @Override
    public List<StoreDTO> queryStoreByRegionList(List<RegionDTO> regionDTOList) {
        return storeMapstruct.organizationList2DTOList(organizationMapper.queryStoreByRegionList(regionDTOList));
    }

    @Override
    public List<StoreDTO> queryStoreByBrandList(List<String> brandGuidList) {
        if (CollectionUtils.isEmpty(brandGuidList)) {
            return Collections.emptyList();
        }
        List<StoreBrandDO> storeBrandDOS = storeBrandMapper.selectList(
                new LambdaQueryWrapper<StoreBrandDO>().in(StoreBrandDO::getBrandGuid, brandGuidList)
        );
        Map<String, BrandDO> brandDOMap = brandMapper.selectList(
                new LambdaQueryWrapper<BrandDO>().in(BrandDO::getGuid, brandGuidList)
        ).stream().collect(Collectors.toMap(BrandDO::getGuid, Function.identity()));
        Map<String, String> collect = storeBrandDOS.stream()
                .collect(Collectors.toMap(StoreBrandDO::getStoreGuid, StoreBrandDO::getBrandGuid));
        List<String> storeGuidList = storeBrandDOS.stream()
                .map(StoreBrandDO::getStoreGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeGuidList)) {
            return Collections.emptyList();
        }
        final List<OrganizationDO> organizationDOList = organizationMapper.selectList(
                new LambdaQueryWrapper<OrganizationDO>()
                        .in(OrganizationDO::getType, OrganizationTypeEnum.STORE.getType(), OrganizationTypeEnum.ALL.getType())
                        .in(OrganizationDO::getGuid, storeGuidList)
        );
        List<StoreDTO> storeDTOList = storeMapstruct.organizationList2DTOList(organizationDOList);
        storeDTOList.forEach(storeDTO -> {
            String brandGuid = collect.get(storeDTO.getGuid());
            BrandDO brandDO = brandDOMap.get(brandGuid);
            if (!ObjectUtils.isEmpty(brandDO)) {
                BrandDTO brandDTO = new BrandDTO().setGuid(brandDO.getGuid()).setName(brandDO.getName());
                storeDTO.setBelongBrandGuid(brandGuid);
                storeDTO.setBrandDTOList(Collections.singletonList(brandDTO));
            }
        });
        return storeDTOList;
    }

    @Override
    public boolean queryDeleteCondition(String storeGuid) {
        // TODO 二期暂时没做员工的更多信息部分，门店下是否存在帐号是员工的所属组织，这部分暂时不实现
        // 开通服务是否到期需要判断门店使用产品中最早同步过来的那一条（取所有产品中gmtCreate最小那条的授权截止时间与当前时间比较）
        List<StoreProductDTO> productDTOList = userClient.queryProductByStoreGuid(storeGuid, false);
        if (!CollectionUtils.isEmpty(productDTOList)) {
            Optional<StoreProductDTO> dto = productDTOList.stream()
                    .filter(p -> Objects.nonNull(p.getGmtCreate()))
                    .min(Comparator.comparing(StoreProductDTO::getGmtCreate));
            //产品有效期为永久的
            if (dto.isPresent() && ObjectUtils.isEmpty(dto.get().getGmtProductEnd())) {
                return false;
            }
            if (dto.isPresent() && dto.get().getGmtProductEnd().compareTo(LocalDate.now()) > 0) {
                return false;
            }
        }
        return true;
    }

    @Override
    public BrandDTO queryBrandByStoreGuid(String storeGuid) {
        // 后期可能一个门店关联多个品牌
        StoreBrandDO storeBrandDO = storeBrandMapper.selectOne(
                new LambdaQueryWrapper<StoreBrandDO>().eq(StoreBrandDO::getStoreGuid, storeGuid)
        );
        if (ObjectUtils.isEmpty(storeBrandDO)) {
            log.warn("当前门店未绑定品牌:{}", storeGuid);
            return new BrandDTO();
        }
        BrandDO brandDO = brandMapper.selectOne(
                new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getGuid, storeBrandDO.getBrandGuid())
        );
        return brandMapstruct.brandDO2DTO(brandDO);
    }

    /**
     * 根据门店guidList查询门店关联的品牌信息
     *
     * @param storeGuidList 门店guidList
     * @return 品牌信息
     */
    @Override
    public List<BrandDTO> queryBrandListByStoreGuidList(List<String> storeGuidList) {
        if (CollectionUtils.isEmpty(storeGuidList)) {
            log.warn("传入门店guid为空");
            return new ArrayList<>();
        }

        List<StoreBrandDO> storeBrandDOList = storeBrandMapper.selectList(new LambdaQueryWrapper<StoreBrandDO>()
                .in(StoreBrandDO::getStoreGuid, storeGuidList)
        );
        if (CollectionUtils.isEmpty(storeBrandDOList)) {
            log.warn("当前门店未绑定品牌");
            return new ArrayList<>();
        }
        List<String> brandGuidList = storeBrandDOList.stream()
                .map(StoreBrandDO::getBrandGuid)
                .collect(Collectors.toList());
        List<BrandDO> brandDOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(brandGuidList)) {
            brandDOList = brandMapper.selectList(new LambdaQueryWrapper<BrandDO>()
                    .in(BrandDO::getGuid, brandGuidList)
            );
        }
        List<BrandDTO> brandDTOList = brandMapstruct.brandDOList2DTOList(brandDOList);

        brandDTOList.forEach(brand -> {
            List<OrganizationDO> organizationDOList = organizationMapper.selectList(
                    new LambdaQueryWrapper<OrganizationDO>()
                            .in(OrganizationDO::getType, OrganizationTypeEnum.STORE.getType(), OrganizationTypeEnum.ALL.getType())
                            .in(OrganizationDO::getGuid, storeGuidList)
            );
            List<StoreDTO> storeDTOList = storeMapstruct.organizationList2DTOList(organizationDOList);
            brand.setStoreList(storeDTOList);
        });
        return brandDTOList;
    }

    /**
     * 批量根据门店名称查找门店
     *
     * @param storeNameList 门店名字列表
     * @return 门店信息列表
     */
    @Override
    public List<StoreDTO> queryStoreByNameList(List<String> storeNameList) {
        if (CollectionUtils.isEmpty(storeNameList)) {
            log.warn("传入门店名称为空");
            return new ArrayList<>();
        }
        List<OrganizationDO> organizationDOList = organizationMapper.selectList(new LambdaQueryWrapper<OrganizationDO>()
                .in(OrganizationDO::getName, storeNameList)
        );
        if (CollectionUtils.isEmpty(organizationDOList)) {
            log.warn("批量根据门店名称查找门店,查询结果为空");
            return new ArrayList<>();
        }
        return storeMapstruct.organizationList2DTOList(organizationDOList);
    }

    @Override
    public BrandDTO queryBrandByStoreGuidForMember(String storeGuid) {
        // 后期可能一个门店关联多个品牌
        StoreBrandDO storeBrandDO = storeBrandMapper.selectOne(
                new LambdaQueryWrapper<StoreBrandDO>().eq(StoreBrandDO::getStoreGuid, storeGuid)
        );
        if (ObjectUtils.isEmpty(storeBrandDO)) {
            return new BrandDTO();
        }
        BrandDO brandDO = brandMapper.selectOne(
                new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getGuid, storeBrandDO.getBrandGuid())
        );
        return brandMapstruct.brandDO2DTO(brandDO);
    }

    @Override
    public List<StoreDTO> queryStoreByIdList(List<String> storeGuidList) {
        if (storeGuidList.isEmpty()) {
            return Collections.emptyList();
        }
        List<OrganizationDO> organizationDOList = organizationMapper.selectList(
                new LambdaQueryWrapper<OrganizationDO>()
                        .select(OrganizationDO::getGuid,
                                OrganizationDO::getCode,
                                OrganizationDO::getName,
                                OrganizationDO::getBusinessStart,
                                OrganizationDO::getBusinessEnd,
                                OrganizationDO::getIsSelfBuildItems)
                        .in(OrganizationDO::getGuid, storeGuidList)
        );
        return storeMapstruct.organizationList2DTOList(organizationDOList);
    }

    @Override
    public List<StoreDTO> queryStoreByIdListAndBrandId(SingleDataDTO singleDataDTO) {
        if (CollectionUtils.isEmpty(singleDataDTO.getDatas())) {
            return Collections.emptyList();
        }
        List<OrganizationDO> organizationDOList = organizationMapper.selectList(
                new LambdaQueryWrapper<OrganizationDO>()
                        .select(OrganizationDO::getGuid,
                                OrganizationDO::getCode,
                                OrganizationDO::getName)
                        .in(OrganizationDO::getGuid, singleDataDTO.getDatas())
        );
        List<StoreDTO> storeDTOList = storeMapstruct.organizationList2DTOList(organizationDOList);
        if (StringUtils.hasText(singleDataDTO.getData())) {
            List<StoreBrandDO> storeBrandDOS = storeBrandMapper.selectList(
                    new LambdaQueryWrapper<StoreBrandDO>().eq(StoreBrandDO::getBrandGuid, singleDataDTO.getData())
            );
            List<StoreDTO> respList = Lists.newArrayList();
            Map<String, StoreDTO> collect = storeDTOList.stream()
                    .collect(Collectors.toMap(StoreDTO::getGuid, Function.identity()));
            storeBrandDOS.forEach(storeBrandDO -> {
                StoreDTO storeDTO = collect.get(storeBrandDO.getStoreGuid());
                if (!ObjectUtils.isEmpty(storeDTO)) {
                    respList.add(storeDTO);
                }
            });
            return respList;
        }
        return storeDTOList;
    }

    @Override
    public List<StoreDTO> queryStoreDetailByIdList(List<String> storeGuidList) {
        if (storeGuidList.isEmpty()) {
            return Collections.emptyList();
        }
        return organizationMapper.queryStoreDetail(storeGuidList);
    }

    @Override
    public List<StoreDTO> queryStoreAndBrandByIdList(List<String> storeGuidList) {
        if (storeGuidList.isEmpty()) {
            return Collections.emptyList();
        }
        log.info("当前线程enterpriseGuid:{}, 调用方法【organizationMapper.queryStoreDetail(storeGuidList)】",
                UserContextUtils.getEnterpriseGuid());
        List<StoreDTO> storeDTOList = organizationMapper.queryStoreDetail(storeGuidList);
        log.info("当前线程enterpriseGuid:{}, 调用方法【storeBrandMapper.selectList(\n" +
                "                new LambdaQueryWrapper<StoreBrandDO>().in(StoreBrandDO::getStoreGuid, storeGuidList)\n" +
                "        )】", UserContextUtils.getEnterpriseGuid());
        List<StoreBrandDO> storeBrandDOS = storeBrandMapper.selectList(
                new LambdaQueryWrapper<StoreBrandDO>().in(StoreBrandDO::getStoreGuid, storeGuidList)
        );
        Map<String, String> storeAndBrandGuidMap = storeBrandDOS.stream()
                .collect(Collectors.toMap(StoreBrandDO::getStoreGuid, StoreBrandDO::getBrandGuid));
        List<String> brandGuidList = storeBrandDOS.stream()
                .map(StoreBrandDO::getBrandGuid)
                .collect(Collectors.toList());
        log.info("当前线程enterpriseGuid:{}, " +
                        "调用方法【brandMapper.selectList(new LambdaQueryWrapper<BrandDO>().in(BrandDO::getGuid, brandGuidList))】",
                UserContextUtils.getEnterpriseGuid());
        Map<String, BrandDO> brandMapInDB = new HashMap<>();
        if (!CollectionUtils.isEmpty(brandGuidList)) {
            List<BrandDO> brandDOS = brandMapper.selectList(
                    new LambdaQueryWrapper<BrandDO>().in(BrandDO::getGuid, brandGuidList)
            );
            brandMapInDB = brandDOS.stream()
                    .collect(Collectors.toMap(BrandDO::getGuid, Function.identity()));
        }
        Map<String, BrandDO> brandDOMap = brandMapInDB;
        storeDTOList.forEach(storeDTO -> {
            String brandGuid = storeAndBrandGuidMap.get(storeDTO.getGuid());
            BrandDO brandDO = brandDOMap.get(brandGuid);
            if (!ObjectUtils.isEmpty(brandDO)) {
                BrandDTO brandDTO = new BrandDTO().setName(brandDO.getName()).setGuid(brandGuid);
                storeDTO.setBrandDTOList(Collections.singletonList(brandDTO));
                storeDTO.setBelongBrandGuid(brandGuid);
                storeDTO.setBelongBrandName(brandDO.getName());
            }
        });
        return storeDTOList;
    }

    @Override
    public List<StoreDTO> queryAllStore() {
        List<OrganizationDO> organizationDOList = organizationMapper.selectList(
                new LambdaQueryWrapper<OrganizationDO>().in(OrganizationDO::getType, Lists.newArrayList(2, 3))
        );
        return storeMapstruct.organizationList2DTOList(organizationDOList);
    }

    @Override
    public List<String> queryAllStoreGuid() {
        return organizationMapper.selectList(new LambdaQueryWrapper<OrganizationDO>()
                .select(OrganizationDO::getGuid)
                .in(OrganizationDO::getType, Lists.newArrayList(2, 3))
        ).stream().map(OrganizationDO::getGuid).collect(Collectors.toList());
    }

    @Override
    public Page<StoreDTO> queryStoreByCondition(QueryStoreDTO queryStoreDTO) {
        List<StoreDTO> storeDTOList = storeMapstruct.organizationList2DTOList(organizationMapper.selectByCondition(queryStoreDTO));
        if (CollectionUtils.isEmpty(storeDTOList)) {
            return new Page<>(queryStoreDTO.getCurrentPage(), queryStoreDTO.getPageSize(), queryStoreDTO.getTotalCount(), storeDTOList);
        }
        List<String> collect = storeDTOList.stream()
                .map(StoreDTO::getGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return new Page<>(queryStoreDTO.getCurrentPage(), queryStoreDTO.getPageSize(), 0);
        }
        List<StoreBrandDO> storeBrandDOList = storeBrandMapper.selectList(
                new LambdaQueryWrapper<StoreBrandDO>().in(StoreBrandDO::getStoreGuid, collect)
        );
        List<String> collect1 = storeBrandDOList.stream()
                .map(StoreBrandDO::getBrandGuid).collect(Collectors.toList());
        List<BrandDO> brandDOList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(collect1)) {
            brandDOList = brandMapper.selectList(
                    new LambdaQueryWrapper<BrandDO>().in(BrandDO::getGuid, collect1)
            );
        }

        // 根据门店id分组，并将相同门店id的品牌id集合相加
        Map<String, List<String>> map = storeBrandDOList.stream()
                .collect(Collectors.toMap(
                        StoreBrandDO::getStoreGuid,
                        v -> Lists.newArrayList(v.getBrandGuid()),
                        (List<String> oldList, List<String> newList) -> {
                            newList.addAll(oldList);
                            return newList;
                        }
                ));
        // 遍历门店集合，将品牌id解析出品牌详情并赋值
        List<BrandDO> finalBrandDOList = brandDOList; //解决stream里面数据必须是final的问题
        storeDTOList.forEach(p -> {
            if (map.containsKey(p.getGuid())) {
                List<BrandDTO> collect2 = map.get(p.getGuid()).stream()
                        .map(o -> {
                            Optional<BrandDO> brandOptional = finalBrandDOList.stream()
                                    .filter(v -> v.getGuid().equals(o))
                                    .findFirst();
                            return new BrandDTO()
                                    .setName(brandOptional.get().getName())
                                    .setGuid(brandOptional.get().getGuid())
                                    .setIsEnable(brandOptional.get().getIsEnable());
                        }).collect(Collectors.toList());
                p.setBrandDTOList(collect2);
            }
        });
        return new Page<>(queryStoreDTO.getCurrentPage(), queryStoreDTO.getPageSize(), queryStoreDTO.getTotalCount(), storeDTOList);
    }

    /**
     * 首次进入时，前端只传cityName cityCode=null cityName!=null
     * <p>
     * 前端选择其他门店（未配置地址的门店）cityCode="999" cityName=null
     * 选择了城市 cityCode!="999" && cityCode= 直辖市
     * 选择了城市 cityCode!="999" && cityCode!= 直辖市
     * 获取门店所有城市列表 cityCode==null cityName==null
     *
     * @param storeDTO
     * @return
     */
    @Override
    public List<StoreDTO> queryStoreByCityAndBrand(StoreDTO storeDTO) {
        log.info("通过品牌及城市获取门店列表请求参数：{}", JacksonUtils.writeValueAsString(storeDTO));
        List<StoreBrandDO> storeBrandDOS = storeBrandMapper.selectList(new LambdaQueryWrapper<StoreBrandDO>().eq(StoreBrandDO::getBrandGuid, storeDTO.getBelongBrandGuid()));
        List<String> storeGuidList = storeBrandDOS.stream().map(StoreBrandDO::getStoreGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeGuidList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrganizationDO> wrapper = new LambdaQueryWrapper<>();
        String cityCode = storeDTO.getCityCode();
        if (StringUtils.hasText(cityCode)) {
            if (Objects.equals(SpecialProvinceEnum.DEFAULT.getCode(), cityCode)) {
                // 其他城市（未配置地址的门店）
                wrapper.and(l ->
                        l.isNull(OrganizationDO::getCityCode).or().eq(OrganizationDO::getCityCode, "")
                ).and(l ->
                        l.isNull(OrganizationDO::getProvinceCode).or().eq(OrganizationDO::getProvinceCode, "")
                );
            } else if (Objects.equals(SpecialProvinceEnum.DEFAULT.getCode(), SpecialProvinceEnum.getByCode(cityCode).getCode())) {
                // 非直辖市
                wrapper.eq(OrganizationDO::getCityCode, cityCode);
            } else {
                //直辖市
                wrapper.eq(OrganizationDO::getProvinceCode, cityCode);
            }
        } else if (StringUtils.hasText(storeDTO.getCityName())) {
            // 不可能传其他
            if (Objects.equals(SpecialProvinceEnum.DEFAULT.getName(), storeDTO.getCityName())) {
                log.info("cityCode不传时，cityName不能传其他");
                return null;
            }
            if (Objects.equals(SpecialProvinceEnum.DEFAULT.getCode(), SpecialProvinceEnum.getByName(storeDTO.getCityName()).getCode())) {
                //非直辖市
                wrapper.like(OrganizationDO::getCityName, storeDTO.getCityName());
            } else {
                //直辖市
                wrapper.like(OrganizationDO::getProvinceName, storeDTO.getCityName());
            }
        }
        List<OrganizationDO> list = list(wrapper
                .in(OrganizationDO::getType, Lists.newArrayList(2, 3))
                .in(OrganizationDO::getGuid, storeGuidList)
                .eq(OrganizationDO::getIsEnable, true)
        );
        List<StoreDTO> storeDTOList = storeMapstruct.organizationList2DTOList(list);
        log.info("查询到门店列表：{}", JacksonUtils.writeValueAsString(storeDTOList));
        return storeDTOList;
    }

    /**
     * 根据门店guid集合及日期时间获取所属营业日期
     * 如果多个门店营业时间不一致的情况，默认营业时间为0-23:59:59
     *
     * @param businessDateReqDTO 门店guid和查询的时间，如果为空则为当前时间
     * @return 所属营业日期
     */
    @Override
    public LocalDate queryBusinessDay(BusinessDateReqDTO businessDateReqDTO) {
        log.info("营业日期查询入参：{}", JacksonUtils.writeValueAsString(businessDateReqDTO));
        //如果传入时间为空，则默认为当前时间
        LocalDateTime queryDateTime = Optional.ofNullable(businessDateReqDTO.getQueryDateTime())
                .orElse(LocalDateTime.now());

        //根据门店guid集合查询出门店集合
        List<String> storeGuidList = businessDateReqDTO.getStoreGuidList();
        if (CollectionUtils.isEmpty(storeGuidList)) {
            throw new BusinessException("传入门店GUID为空！");
        }
        List<StoreDTO> storeDTOList = this.queryStoreByIdList(storeGuidList);

        //获取门店营业时间
        LocalTime businessStart = storeDTOList.get(0).getBusinessStart();
        LocalTime businessEnd = storeDTOList.get(0).getBusinessEnd();
        //判断门店营业时间是否一致，如果一致，使用该时间，如果不一致，默认营业时间为0-23:59:59
        for (int i = 1; i < storeDTOList.size(); i++) {
            LocalTime businessStart1 = storeDTOList.get(i).getBusinessStart();
            LocalTime businessEnd1 = storeDTOList.get(i).getBusinessEnd();
            if (businessStart.compareTo(businessStart1) != 0 || businessEnd.compareTo(businessEnd1) != 0) {
                businessStart = LocalTime.MIN;
                businessEnd = LocalTime.MAX;
                break;
            }
        }
        //当查询时间小于开始时间时，营业日期为前一天的营业日
        if (businessStart.compareTo(queryDateTime.toLocalTime()) > 0) {
            return queryDateTime.toLocalDate().minusDays(1);
        }
        return queryDateTime.toLocalDate();
    }

    @Override
    public StoreDTO queryBusinessDayInfo(BusinessDateReqDTO businessDateReqDTO) {
        log.info("查询门店信息及日期时间获取所属营业日期入参：{}", JacksonUtils.writeValueAsString(businessDateReqDTO));
        OrganizationDO organizationDO = organizationMapper.selectOne(
                new LambdaQueryWrapper<OrganizationDO>().eq(OrganizationDO::getGuid, businessDateReqDTO.getStoreGuid()));
        if (Objects.isNull(organizationDO)) {
            throw new BusinessException("门店不存在");
        }
        // 如果传入时间为空，则默认为当前时间
        LocalDateTime queryDateTime = Optional.ofNullable(businessDateReqDTO.getQueryDateTime())
                .orElse(LocalDateTime.now());
        // 未查询品牌，需要时再查询
        StoreDTO storeDTO = storeMapstruct.organizationDO2DTO(organizationDO, null);
        // 获取门店营业时间
        LocalTime businessStart = storeDTO.getBusinessStart();
        // 当查询时间小于开始时间时，营业日期为前一天的营业日
        if (businessStart.isAfter(queryDateTime.toLocalTime())) {
            storeDTO.setBusinessDay(queryDateTime.toLocalDate().minusDays(1));
        } else {
            storeDTO.setBusinessDay(queryDateTime.toLocalDate());
        }
        return storeDTO;
    }

    @Override
    public StoreBusinessDateDTO queryBusinessDate(String storeGuid) {
        StoreDTO storeDTO = this.queryStoreByGuid(storeGuid);
        //将营业时间添上日期
        LocalDateTime businessStart = storeDTO.getBusinessStart().atDate(LocalDate.now());
        if (businessStart.isAfter(LocalDateTime.now())) {
            //未到今天的营业时间，查询日期从昨天开始
            businessStart = businessStart.minusDays(1);
        }
        //营业结束时间现在与开始时间相同，相差1天
        LocalDateTime businessEnd = businessStart.plusDays(1);
        StoreBusinessDateDTO storeBusinessDateDTO = new StoreBusinessDateDTO();
        storeBusinessDateDTO.setBusinessStart(businessStart);
        storeBusinessDateDTO.setBusinessEnd(businessEnd);
        return storeBusinessDateDTO;
    }

    @Override
    public PadStartOrderRespDTO getPadStartOrderInfo(String storeGuid) {
        OrganizationDO organizationDO = organizationMapper.selectOne(
                new LambdaQueryWrapper<OrganizationDO>().eq(OrganizationDO::getGuid, storeGuid));
        if (Objects.isNull(organizationDO)) {
            throw new BusinessException("门店不存在");
        }
        BrandDTO brandDTO = this.queryBrandByStoreGuidForMember(storeGuid);
        PadStartOrderRespDTO dto = new PadStartOrderRespDTO()
                .setStoreGuid(organizationDO.getGuid())
                .setStoreName(organizationDO.getName())
                .setBrandLogoUrl(brandDTO.getLogoUrl());
        try {
            WxOrderConfigDTO storeConfig = wxStoreOrderConfigService.getStoreConfig(storeGuid);
            dto.setWxOrderConfigDTO(storeConfig);
        } catch (RuntimeException e) {
            log.error(e.getMessage(), e);
        }
        return dto;
    }


    @Override
    public void batchUpdateBuAccounts(List<String> storeList, StoreDTO storeDTO) {
        organizationMapper.batchUpdateBuAccounts(storeList, storeDTO);
    }

    /**
     * 判断组织code是否在云端重复
     *
     * @return 组织code
     */
    private String getOnlyOrganizationCode() {
        String code = GuidUtils.nextOrganizationGuid();
        Boolean result = enterpriseClient.checkCode(code);
        if (result == null || !result) {
            return this.getOnlyOrganizationCode();
        }
        return code;
    }


    @Override
    public HashMap<String, String> checkBindUpAccount(String storeGuid,
                                                      String userGuid,
                                                      String userName,
                                                      List<TableStatusDTO> tableStatusDTOS) {
        /**
         *  status      1 扎帐不成功   0 成功
         *  bindupAccounts       扎帐成功时间
         */
        HashMap<String, String> map = new HashMap<>();
        //获取当前时间，扎帐时间营业日
        LocalDate localDate = this.currentTimeDay(storeGuid, null);
        if (!tableStatusDTOS.isEmpty()) {
            for (TableStatusDTO tableStatusDTO : tableStatusDTOS) {
                LocalDate tableOpenTime = this.currentTimeDay(storeGuid, tableStatusDTO.getOpenTableTime());
                if (tableOpenTime.compareTo(localDate) < 0) {
                    //存在之前未扎帐信息，提示客户扎帐
                    map.put("status", "0");
                    return map;
                }
                tableStatusDTO.setTableBindUpAccounts(tableOpenTime);
            }
        }
        //昨日扎帐日期
        LocalDate yesterdayTime = localDate.plusDays(-1);
        //获取该门店最新扎帐时间
        BindupAccountsDo bindupAccountsDo = businessClient.queryBindUpAccountsLast(storeGuid);
        BindupAccountsDTO bindupAccountsDTO = null;
        //从未扎帐
        if (bindupAccountsDo.getBindupAccounts() == null) {
            //扎账,昨日未扎帐，应该扎昨日的账单
            bindupAccountsDTO = businessClient.saveBindUpAccounts(storeGuid, userGuid, userName, yesterdayTime);
            map.put("status", "1");
            map.put("data", localDate.format(fmt));
            return map;
        }
        //昨日未扎帐
        if (bindupAccountsDo.getBindupAccounts().toLocalDate().compareTo(yesterdayTime) < 0) {
            bindupAccountsDTO = businessClient.saveBindUpAccounts(storeGuid, userGuid, userName, yesterdayTime);
            map.put("status", "1");
            map.put("data", localDate.format(fmt));
            return map;
        }
        //昨日已经扎帐- 判断桌台是否是今日开台
        if (bindupAccountsDo.getBindupAccounts().toLocalDate().compareTo(yesterdayTime) >= 0) {
            if (!tableStatusDTOS.isEmpty()) {
                map.put("status", "0");
                return map;
            }
        }
        //今日桌台清空才执行
        bindupAccountsDTO = businessClient.saveBindUpAccounts(storeGuid, userGuid, userName, localDate);
        map.put("status", "1");
        map.put("data", localDate.format(fmt));
        return map;
    }

    /**
     * 查询门店营业日
     *
     * @param businessDayDTOList 企业，门店
     * @return 门店营业日
     */
    @Override
    public List<BusinessDayDTO> queryStoreBusinessDay(List<BusinessDayDTO> businessDayDTOList) {
        Map<String, List<BusinessDayDTO>> enterpriseMap = businessDayDTOList.stream()
                .filter(b -> !org.springframework.util.StringUtils.isEmpty(b.getEnterpriseGuid()))
                .collect(Collectors.groupingBy(BusinessDayDTO::getEnterpriseGuid));
        enterpriseMap.forEach((enterpriseGuid, storeList) -> {
            if (CollectionUtils.isEmpty(storeList)) {
                return;
            }
            // 判断企业是否存在
            if (!enterpriseClientService.hasEnterprise(enterpriseGuid)) {
                log.warn("企业不存在,enterpriseGuid={}", enterpriseGuid);
                return;
            }
            List<String> storeGuidList = storeList.stream()
                    .map(BusinessDayDTO::getStoreGuid)
                    .filter(guid -> !StringUtils.isEmpty(guid))
                    .distinct()
                    .collect(Collectors.toList());
            UserContextUtils.putErp(enterpriseGuid);
            dynamicHelper.changeDatasource(enterpriseGuid);
            List<OrganizationDO> organizationDOList = new ArrayList<>();
            try {
                organizationDOList = this.list(new LambdaQueryWrapper<OrganizationDO>()
                        .in(OrganizationDO::getGuid, storeGuidList));
            } catch (Exception e) {
                log.error("查询门店信息异常,企业guid={},异常信息={}", enterpriseGuid, e.getMessage());
                return;
            }
            if (CollectionUtils.isEmpty(organizationDOList)) {
                log.warn("未查询到门店信息,storeGuidList={}", storeGuidList);
                return;
            }
            Map<String, OrganizationDO> storeMap = organizationDOList.stream()
                    .collect(Collectors.toMap(OrganizationDO::getGuid, Function.identity(), (v1, v2) -> v1));
            storeList.forEach(store -> {
                OrganizationDO organizationDO = storeMap.get(store.getStoreGuid());
                if (ObjectUtils.isEmpty(organizationDO)) {
                    return;
                }
                store.setBusinessStart(organizationDO.getBusinessStart());
                store.setBusinessEnd(organizationDO.getBusinessEnd());
            });
        });
        return businessDayDTOList;
    }


    /**
     * 当前时间的营业日
     *
     * @return
     */
    public LocalDate currentTimeDay(String storeGuid, LocalDateTime localDateTime) {
        BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        ArrayList<String> str = new ArrayList<>();
        str.add(storeGuid);
        businessDateReqDTO.setStoreGuidList(str);
        if (localDateTime != null) {
            businessDateReqDTO.setQueryDateTime(localDateTime);
        }
        LocalDate localDate = this.queryBusinessDay(businessDateReqDTO);
        return localDate;
    }

    @Override
    public BrandStoreDetailDTO queryStoreBrandDetail(String storeGuid, String brandGuid) {
        return storeBrandMapper.getStoreBrandDetail(storeGuid,brandGuid);
    }
}
