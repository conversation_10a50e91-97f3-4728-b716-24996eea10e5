package com.holderzone.saas.store.organization;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.saas.store.organization.utils.SpringContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className HolderSaasStoreInfoApplication
 * @date 2018/07/10 下午7:39
 * @description Spring boot启动类
 * @program holder-saas-store-organization
 */
@EnableAsync
@EnableSwagger2
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication
@ServletComponentScan
@EnableApolloConfig
public class HolderSaasStoreOrganizationApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreOrganizationApplication.class, args);

        /**
         * 设置Spring容器上下文
         */
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) app);
    }
}
