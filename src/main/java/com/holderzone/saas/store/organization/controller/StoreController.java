package com.holderzone.saas.store.organization.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.slf4j.starter.anno.LogAfter;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.BusinessDayDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.store.store.BindupAccountsSaveDTO;
import com.holderzone.saas.store.dto.store.table.TableBuAccountsDTO;
import com.holderzone.saas.store.organization.constant.OrganizationTypeEnum;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.service.StoreService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className StoreController
 * @date 19-1-2 下午5:20
 * @description Controller-门店
 * @program holder-saas-store-organization
 */
@Slf4j
@RestController
@RequestMapping("/store")
@Api(tags = "门店相关接口")
public class StoreController {

    private final StoreService storeService;


    @Autowired
    public StoreController(StoreService storeService) {
        this.storeService = storeService;
    }

    /**
     * 创建门店
     *
     * @param storeDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("创建门店")
    @PostMapping("/create")
    @LogBefore(value = "创建门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "创建门店", logLevel = LogLevel.DEBUG)
    public boolean createStore(@RequestBody @Validated StoreDTO storeDTO) {
        return storeService.createStore(storeDTO);
    }

    /**
     * 创建门店
     *
     * @param storeDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("创建门店")
    @PostMapping("/create_by_mdm")
    @LogBefore(value = "创建门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "创建门店", logLevel = LogLevel.DEBUG)
    public void createStoreByMdm(@RequestBody StoreDTO storeDTO) {
        storeService.createStoreByMdm(storeDTO);
    }

    /**
     * 更新门店
     *
     * @param storeDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("更新门店")
    @PostMapping("/update")
    @LogBefore(value = "更新门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "更新门店", logLevel = LogLevel.DEBUG)
    public boolean updateStore(@RequestBody @Validated StoreDTO storeDTO) {
        return storeService.updateStore(storeDTO, false);
    }

    /**
     * 设置商品是否上传
     *
     * @param req DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("设置商品是否上传")
    @PostMapping("/itemUploadUpdate")
    @LogBefore(value = "设置商品是否上传", logLevel = LogLevel.INFO)
    @LogAfter(value = "设置商品是否上传", logLevel = LogLevel.DEBUG)
    public boolean itemUploadUpdate(@RequestBody @Validated ItemUploadUpdateReq req) {
        return storeService.itemUploadUpdate(req);
    }

    /**
     * 更新门店
     *
     * @param storeDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("更新门店")
    @PostMapping("/update_by_mdm")
    @LogBefore(value = "更新门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "更新门店", logLevel = LogLevel.DEBUG)
    public void updateStoreByMdm(@RequestBody StoreDTO storeDTO) {
        storeService.updateStoreByMdm(storeDTO);
    }


    /**
     * 更新门店扎帐设置
     *
     * @return
     */
    @ApiOperation("更新门店扎帐设置")
    @PostMapping("/update_by_accountcard")
    @LogBefore(value = "更新门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "更新门店", logLevel = LogLevel.DEBUG)
    public void updateStoreByAccountAndShowCard(@RequestBody BindupAccountsSaveDTO bindupAccountsSaveDTO) {
        storeService.updateBuAccounts(bindupAccountsSaveDTO);
    }


    /**
     * 启用、禁用门店
     *
     * @param storeGuid 门店guid
     * @return true-成功，false-失败
     */
    @ApiOperation("启用、禁用门店")
    @PostMapping("/enable")
    @LogBefore(value = "启用、禁用门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "启用、禁用门店", logLevel = LogLevel.DEBUG)
    public boolean enableStore(@RequestParam("storeGuid") String storeGuid) {
        return storeService.enableStore(storeGuid);
    }


    /**
     * 删除门店
     *
     * @param storeGuid guid
     * @return true-成功，false-失败
     */
    @ApiOperation("删除门店")
    @PostMapping("/delete")
    @LogBefore(value = "删除门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "删除门店", logLevel = LogLevel.DEBUG)
    public boolean deleteStore(@RequestParam("storeGuid") String storeGuid) {
        return storeService.deleteStore(storeGuid);
    }

    /**
     * 根据条件查询门店（分页）
     *
     * @param queryStoreDTO DTO
     * @return Page对象
     */
    @ApiOperation("根据条件查询门店")
    @PostMapping("/query_by_condition")
    @LogBefore(value = "根据条件查询门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据条件查询门店", logLevel = LogLevel.DEBUG)
    public Page<StoreDTO> queryByCondition(@RequestBody QueryStoreDTO queryStoreDTO) {
        return storeService.queryByCondition(queryStoreDTO);
    }

    @ApiOperation("根据条件查询门店")
    @PostMapping("/list_store_by_condition")
    @LogBefore(value = "根据条件查询门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据条件查询门店", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> listStoreByCondition(@RequestBody QueryStoreDTO queryStoreDTO) {
        return storeService.listStoreByCondition(queryStoreDTO);
    }

    /**
     * 根据条件查询门店（不分页）
     *
     * @param storeParserDTO DTO
     * @return Page对象
     */
    @ApiOperation("根据条件查询门店")
    @PostMapping("/query_by_condition_no_page")
    @LogBefore(value = "根据条件查询门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据条件查询门店", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> queryByConditionNoPage(@RequestBody StoreParserDTO storeParserDTO) {
        return storeService.queryByConditionNoPage(storeParserDTO);
    }

    @ApiOperation("根据条件查询门店GUID列表")
    @PostMapping("/parse_by_condition")
    @LogBefore(value = "根据条件查询门店GUID列表", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据条件查询门店GUID列表", logLevel = LogLevel.DEBUG)
    public List<String> parseByCondition(@RequestBody StoreParserDTO storeParserDTO) {
        return storeService.parseByCondition(storeParserDTO);
    }

    @ApiOperation("根据条件查询门店GUID列表")
    @PostMapping("/parse_by_condition_not_union")
    @LogBefore(value = "根据条件查询门店GUID列表", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据条件查询门店GUID列表", logLevel = LogLevel.DEBUG)
    public List<String> parseByConditionNotUnion(@RequestBody StoreParserDTO storeParserDTO) {
        return storeService.parseByConditionNotUnion(storeParserDTO);
    }

    /**
     * 根据门店guid查询门店详细信息
     *
     * @param storeGuid 门店guid
     * @return StoreDTO
     */
    @ApiOperation("根据门店guid查询门店详细信息")
    @PostMapping("/query_store_by_guid")
    @LogBefore(value = "根据门店guid查询门店详细信息", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据门店guid查询门店详细信息", logLevel = LogLevel.DEBUG)
    public StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid) {
        return storeService.queryStoreByGuid(storeGuid);
    }

    /**
     * 根据门店guid查询门店基础信息以及营业日
     *
     * @param storeGuid 门店guid
     * @return StoreDTO
     */
    @PostMapping("/query_store_base_by_guid")
    public StoreDTO queryStoreBaseByGuid(@RequestParam("storeGuid") String storeGuid) {
        return storeService.queryStoreBaseByGuid(storeGuid);
    }

    @PostMapping("/query_store_brand_by_guid")
    public BrandStoreDetailDTO queryStoreBrandDetail(@RequestParam("storeGuid") String storeGuid,@RequestParam("brandGuid") String brandGuid) {
        return storeService.queryStoreBrandDetail(storeGuid,brandGuid);
    }

    @ApiOperation("根据门店guid查询门店营业信息")
    @PostMapping("/query_store_biz_by_guid")
    @LogBefore(value = "根据门店guid查询门店营业信息", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据门店guid查询门店营业信息", logLevel = LogLevel.DEBUG)
    public StoreBizDTO queryStoreBizByGuid(@RequestParam("storeGuid") String storeGuid) {
        return storeService.queryStoreBizByGuid(storeGuid);
    }

    /**
     * 根据门店code查询门店详细信息
     *
     * @param storeCode storeCode
     * @return StoreDTO
     */
    @ApiOperation("根据门店guid查询门店详细信息")
    @PostMapping("/query_store_by_code")
    @LogBefore(value = "根据门店guid查询门店详细信息", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据门店guid查询门店详细信息", logLevel = LogLevel.DEBUG)
    public StoreDTO queryStoreByCode(@RequestParam("storeCode") String storeCode) {
        return storeService.queryStoreByCode(storeCode);
    }

    /**
     * 根据省市区列表查询该省市区列表下的所有门店
     *
     * @param regionDTOList 省市区集合
     * @return 门店列表
     */
    @ApiOperation("根据省市区数组查询省市区数组下的门店")
    @PostMapping("/query_store_by_regionlist")
    @LogBefore(value = "根据省市区数组查询省市区数组下的门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据省市区数组查询省市区数组下的门店", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> queryStoreByRegionList(@RequestBody List<RegionDTO> regionDTOList) {
        return storeService.queryStoreByRegionList(regionDTOList);
    }

    /**
     * 根据品牌列表查询品牌列表下的所有门店
     *
     * @param brandGuidList 品牌guid数组
     * @return 门店列表
     */
    @ApiOperation("根据品牌列表查询品牌列表下的所有门店")
    @PostMapping("/query_store_by_brandlist")
    @LogBefore(value = "根据品牌列表查询品牌列表下的所有门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据品牌列表查询品牌列表下的所有门店", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> queryStoreByBrandList(@RequestBody List<String> brandGuidList) {
        return storeService.queryStoreByBrandList(brandGuidList);
    }

    /**
     * 根据门店guid查询门店是否可以删除，门店下存在帐号、开通服务未到期无法删除
     *
     * @param storeGuid 门店guid
     * @return true-能删除，false-不可删除
     */
    @ApiOperation(value = "根据门店guid查询门店是否可以删除", notes = "门店下存在帐号，开通服务后未到期则无法删除，true-能删除，false-不可删除")
    @PostMapping("query_delete_condition")
    @LogBefore(value = "根据门店guid查询门店是否可以删除", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据门店guid查询门店是否可以删除", logLevel = LogLevel.DEBUG)
    public boolean queryDeleteCondition(@RequestParam("storeGuid") String storeGuid) {
        return storeService.queryDeleteCondition(storeGuid);
    }

    /**
     * 根据门店guid查询门店关联的品牌信息
     *
     * @param storeGuid 门店guid
     * @return 品牌信息
     */
    @ApiOperation(value = "根据门店guid查询门店关联的品牌信息", notes = "若门店未关联到品牌则返回为null，后期一个门店可关联多个品牌")
    @RequestMapping("/query_brand_by_storeguid")
    @LogBefore(value = "根据门店guid查询门店关联的品牌信息", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据门店guid查询门店关联的品牌信息", logLevel = LogLevel.DEBUG)
    public BrandDTO queryBrandByStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        log.info("根据门店guid查询门店关联的品牌信息 入参：{}", storeGuid);
        return storeService.queryBrandByStoreGuid(storeGuid);
    }

    /**
     * 根据门店guidList查询门店关联的品牌信息
     *
     * @param storeGuidList 门店guidList
     * @return 品牌信息
     */
    @ApiOperation(value = "根据门店guidList查询门店关联的品牌信息")
    @GetMapping("/query_brand_by_store_guid_list")
    public List<BrandDTO> queryBrandListByStoreGuidList(@RequestParam("storeGuidList") List<String> storeGuidList) {
        log.info("根据门店guidList查询门店关联的品牌信息 入参：{}", storeGuidList);
        return storeService.queryBrandListByStoreGuidList(storeGuidList);
    }


    /**
     * 根据门店guid查询门店关联的品牌信息
     *
     * @param storeGuid 门店guid
     * @return 品牌信息
     */
    @ApiOperation(value = "根据门店guid查询门店关联的品牌信息", notes = "若门店未关联到品牌则返回为null，后期一个门店可关联多个品牌")
    @RequestMapping("/query_brand_by_storeguid_for_member")
    @LogBefore(value = "根据门店guid查询门店关联的品牌信息", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据门店guid查询门店关联的品牌信息", logLevel = LogLevel.DEBUG)
    public BrandDTO queryBrandByStoreGuidForMember(@RequestParam("storeGuid") String storeGuid) {
        return storeService.queryBrandByStoreGuidForMember(storeGuid);
    }

    /**
     * 根据传入的guid数组查询门店列表（返回扁平结构、只包含guid和name两列）
     *
     * @param storeGuidList 门店guid集合
     * @return 门店列表（扁平结构、只包含guid和name两列）
     */
    @ApiOperation("根据传入的guid数组查询组织列表")
    @PostMapping("/query_store_by_idlist")
    @LogBefore(value = "根据传入的guid数组查询组织列表", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据传入的guid数组查询组织列表", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> queryStoreByIdList(@ApiParam("门店guid集合") @RequestBody List<String> storeGuidList) {
        return storeService.queryStoreByIdList(storeGuidList);
    }

    /**
     * 根据传入的guid数组查询门店列表（返回扁平结构、只包含guid和name两列）
     *
     * @param singleDataDTO 门店guid集合及品牌guid
     * @return 门店列表（扁平结构、只包含guid和name两列）
     */
    @ApiOperation("根据传入的guid数组查询组织列表")
    @PostMapping("/query_store_by_idlist_and_brand")
    @LogBefore(value = "根据传入的guid数组查询组织列表", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据传入的guid数组查询组织列表", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> queryStoreByIdList(@ApiParam("门店guid集合及品牌guid") @RequestBody SingleDataDTO singleDataDTO) {
        return storeService.queryStoreByIdListAndBrandId(singleDataDTO);
    }

    /**
     * 根据传入的门店id集合查询门店详情
     *
     * @return 门店详情集合
     */
    @ApiOperation("根据传入的门店id集合查询门店详情")
    @PostMapping("/query_store_detail_by_idlist")
    @LogBefore(value = "根据传入的门店id集合查询门店详情", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据传入的门店id集合查询门店详情", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> queryStoreDetailByIdList(@RequestBody List<String> storeGuidList) {
        return storeService.queryStoreDetailByIdList(storeGuidList);
    }

    /**
     * 根据传入的门店id集合查询门店详情
     *
     * @return 门店详情集合
     */
    @ApiOperation("根据传入的门店id集合查询门店详情")
    @PostMapping("/query_store_and_brand_by_id_list")
    @LogBefore(value = "根据传入的门店id集合查询门店详情", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据传入的门店id集合查询门店详情", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> queryStoreAndBrandByIdList(@RequestBody List<String> storeGuidList) {
        return storeService.queryStoreAndBrandByIdList(storeGuidList);
    }

    /**
     * 查询企业下的所有门店
     *
     * @return 门店列表（扁平结构、只包含guid和name两列）
     */
    @ApiOperation("查询企业下的所有门店")
    @PostMapping("/query_all_store")
    @LogBefore(value = "查询企业下的所有门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "查询企业下的所有门店", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> queryAllStore() {
        return storeService.queryAllStore();
    }

    /**
     * 查询企业下的所有门店Guid
     *
     * @return 门店列表（扁平结构、只包含guid和name两列）
     */
    @ApiOperation("查询企业下的所有门店")
    @PostMapping("/query_all_store_guid")
    @LogBefore(value = "查询企业下的所有门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "查询企业下的所有门店", logLevel = LogLevel.DEBUG)
    public List<String> queryAllStoreGuid() {
        return storeService.queryAllStoreGuid();
    }

    /**
     * 根据条件查询门店（不分页）
     *
     * @param dto StoreListReq
     * @return Page对象
     */
    @ApiOperation("查询门店列表")
    @PostMapping("/list")
    @LogBefore(value = "根据条件查询门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据条件查询门店", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> list(@RequestBody StoreListReq dto) {
        return storeService.list(dto);
    }

    /**
     * 根据传入的门店id集合或门店名分页查询门店信息
     *
     * @return 分页结果
     */
    @ApiOperation(value = "根据传入的门店id集合或门店名分页查询门店信息")
    @PostMapping("/query_store_by_condition")
    @LogBefore(value = "根据传入的门店id集合或门店名分页查询门店信息", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据传入的门店id集合或门店名分页查询门店信息", logLevel = LogLevel.DEBUG)
    public Page<StoreDTO> queryStoreByCondition(@RequestBody QueryStoreDTO queryStoreDTO) {
        return storeService.queryStoreByCondition(queryStoreDTO);
    }

    @ApiOperation(value = "根据传入的城市code或者城市名字查询门店信息")
    @PostMapping("/query_store_by_city_and_brand")
    @LogBefore(value = "根据传入的城市code或者城市名字查询门店信息", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据传入的城市code或者城市名字查询门店信息", logLevel = LogLevel.DEBUG)
    public List<StoreDTO> queryStoreByCityAndBrand(@RequestBody StoreDTO storeDTO) {
        return storeService.queryStoreByCityAndBrand(storeDTO);
    }

    @ApiOperation(value = "根据门店guid集合及日期时间获取所属营业日期")
    @PostMapping("/query_business_day")
    public LocalDate queryBusinessDay(@RequestBody BusinessDateReqDTO businessDateReqDTO) {
        return storeService.queryBusinessDay(businessDateReqDTO);
    }


    @ApiOperation(value = "查询门店信息及日期时间获取所属营业日期")
    @PostMapping("/query_business_day_info")
    public StoreDTO queryBusinessDayInfo(@RequestBody BusinessDateReqDTO businessDateReqDTO) {
        return storeService.queryBusinessDayInfo(businessDateReqDTO);
    }

    @PostMapping("/get_alive_store_count")
    public Integer getAliveStoreCount() {
        return storeService.count(new LambdaQueryWrapper<OrganizationDO>()
                .in(OrganizationDO::getType, OrganizationTypeEnum.STORE.getType(), OrganizationTypeEnum.ALL.getType())
                .ge(OrganizationDO::getBusinessEnd, LocalTime.now())
                .le(OrganizationDO::getBusinessStart, LocalTime.now()));
    }

    @ApiOperation("查询营业时间段（带日期）")
    @GetMapping("/query_business_date")
    @LogBefore(value = "查询营业时间段（带日期）", logLevel = LogLevel.INFO)
    @LogAfter(value = "查询营业时间段（带日期）", logLevel = LogLevel.DEBUG)
    public StoreBusinessDateDTO queryBusinessDate(@RequestParam("storeGuid") String storeGuid) {
        return storeService.queryBusinessDate(storeGuid);
    }

    /**
     * 批量根据门店名称查找门店
     *
     * @param storeNameList 门店名字列表
     * @return 门店信息列表
     */
    @ApiOperation("批量根据门店名称查找门店")
    @PostMapping("/query_store_by_name_list")
    public List<StoreDTO> queryStoreByNameList(@RequestBody List<String> storeNameList) {
        log.info("批量根据门店名称查找门店 入参：{}", storeNameList);
        return storeService.queryStoreByNameList(storeNameList);
    }

    /**
     * 查询pad开始点餐返回信息
     *
     * @param storeGuid 门店guid
     * @return 查询结果
     */
    @ApiOperation("查询pad开始点餐返回信息")
    @PostMapping("/get_pad_start_order_info")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeGuid", value = "门店guid")
    })
    public PadStartOrderRespDTO getPadStartOrderInfo(@RequestParam("storeGuid") String storeGuid) {
        log.info("查询pad开始点餐返回信息 入参：{}", storeGuid);
        return storeService.getPadStartOrderInfo(storeGuid);
    }

    @ApiOperation("修改数据门店信息")
    @PostMapping("/bindupAccountupdate")
    public boolean batchUpdateBuAccounts(@RequestBody StoreBindUpAccountsDTO st) {
        StoreDTO storeDTO = new StoreDTO()
                .setIsBuAccounts(st.getIsBuAccounts())
                .setIsShowCash(st.getIsShowCash())
                .setCanOpenTable(st.getCanOpenTable())
                .setIsMultiHandover(st.getIsMultiHandover());
        storeService.batchUpdateBuAccounts(st.getStoreList(), storeDTO);
        return true;
    }

    @PostMapping("/auto/storeTable")
    @ApiOperation(value = "验证扎帐信息功能", notes = "验证扎帐信息功能")
    public HashMap<String, String> autoBindupAccounts(@RequestBody TableBuAccountsDTO tableBuAccountsDTO) {
        HashMap<String, String> map = storeService.checkBindUpAccount(tableBuAccountsDTO.getStoreGuid(),
                tableBuAccountsDTO.getUserGuid(),
                tableBuAccountsDTO.getUserName(),
                tableBuAccountsDTO.getTableStatusDTOS());
        return map;
    }

    /**
     * 查询门店营业日
     *
     * @param businessDayDTOList 企业，门店
     * @return 门店营业日
     */
    @ApiOperation(value = "查询门店营业日")
    @PostMapping("/query_store_business_day")
    public List<BusinessDayDTO> queryStoreBusinessDay(@RequestBody List<BusinessDayDTO> businessDayDTOList) {
        return storeService.queryStoreBusinessDay(businessDayDTOList);
    }
}
