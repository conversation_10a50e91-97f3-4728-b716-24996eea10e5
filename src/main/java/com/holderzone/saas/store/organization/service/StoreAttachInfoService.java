package com.holderzone.saas.store.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.organization.StoreAttachInfoDTO;
import com.holderzone.saas.store.organization.domain.StoreAttachInfoDO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

/**
 * 门店附加信息服务类
 *
 * <AUTHOR>
 * @since 2020-07-13
 */
public interface StoreAttachInfoService extends IService<StoreAttachInfoDO> {
    StoreAttachInfoDTO info(  String storeGuid);
    boolean save(StoreAttachInfoDTO dto);
}
