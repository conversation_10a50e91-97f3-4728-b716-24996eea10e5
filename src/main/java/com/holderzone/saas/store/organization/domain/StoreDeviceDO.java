package com.holderzone.saas.store.organization.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className StoreTerminalDO
 * @date 19-2-10 下午4:04
 * @description 门店-DO
 * @program holder-saas-store-organization
 */
@Data
@Accessors(chain = true)
@TableName(value = "hso_r_store_device")
public class StoreDeviceDO implements Serializable {

    private static final long serialVersionUID = -4977672658326843634L;

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 厂商设备编号
     */
    private String deviceNo;

    /**
     * 系统设备编号（云端生成）
     */
    private String deviceGuid;

    /**
     * 设备是否绑定（false=否，true=是。）
     */
    private Boolean isBinding;

    /**
     * 设备类型
     * PC服务端- 0
     * PC平板- 1
     * 小店通- 2
     * 一体机- 3
     * POS机- 4
     * 云平板- 5
     * 点菜宝(M1)- 6
     * PV1(带刷卡的点菜宝)- 7
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 打印设备排序（仅设备类型为一体机有效，默认为0。）
     */
    private Integer sort;

    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 解绑人Guid
     */
    private String unbindUserGuid;

    /**
     * 解绑人姓名
     */
    private String unbindUserName;

    /**
     * 设备解绑时间
     */
    private LocalDateTime gmtUnbind;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 桌台号guid
     */
    private String tableGuid;

    /**
     * pad点餐类型(0:商家点餐 1:用户自主点餐)
     */
    private Integer padOrderType;
}
