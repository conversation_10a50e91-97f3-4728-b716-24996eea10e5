package com.holderzone.saas.store.organization.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 扎帐信息
 * <AUTHOR>
 */
@Data
public class BindupAccountsDo {

    private long id;

    /**
     * 扎帐时间
     */
    private LocalDateTime bindupAccounts;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 用户id
     */
    private String userGuid;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

}