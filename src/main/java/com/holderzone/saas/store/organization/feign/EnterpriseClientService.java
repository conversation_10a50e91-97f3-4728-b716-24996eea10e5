package com.holderzone.saas.store.organization.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 企业信息ClientService
 * @date 2021/9/18 16:17
 * @className: EnterpriseClientService
 */
@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseClientService.EnterpriseFallBack.class)
public interface EnterpriseClientService {

    @GetMapping("/multi/member/list")
    List<MultiMemberDTO> list(@RequestBody Set<String> multiMemberGuid);

    /**
     * 查询门店属于什么运营主体
     *
     * @param organizationGuid 门店guid
     * @return 运营主体
     */
    @GetMapping("/enterprise/member/info/{organizationGuid}")
    MultiMemberDTO findMemberInfoByOrganizationGuid(@PathVariable(value = "organizationGuid") String organizationGuid);

    /**
     * 根据门店GUID、员工GUID查询营业日起始时间
     *
     * @return
     */
    @GetMapping("/enterprise/hasEnterprise")
    Boolean hasEnterprise(@RequestParam("enterpriseGuid") String enterpriseGuid);

    @Component
    @Slf4j
    class EnterpriseFallBack implements FallbackFactory<EnterpriseClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";


        @Override
        public EnterpriseClientService create(Throwable throwable) {
            return new EnterpriseClientService() {

                @Override
                public List<MultiMemberDTO> list(Set<String> multiMemberGuid) {
                    log.error(HYSTRIX_PATTERN, "list", multiMemberGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询运营主体失败");
                }

                @Override
                public MultiMemberDTO findMemberInfoByOrganizationGuid(String organizationGuid) {
                    log.error(HYSTRIX_PATTERN, "findMemberInfoByOrganizationGuid", organizationGuid,
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询门店属于什么运营主体失败");
                }

                /**
                 * 根据门店GUID、员工GUID查询营业日起始时间
                 */
                @Override
                public Boolean hasEnterprise(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/enterprise/hasEnterprise", enterpriseGuid, throwable);
                    return Boolean.TRUE;
                }
            };
        }
    }

}
