package com.holderzone.saas.store.organization.utils;

import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className LogUtils
 * @date 2018/10/25 15:28
 * @description 日志工具类
 * @program holder-saas-store-organization
 */
@NoArgsConstructor
public class LogUtils {
    public static final String MODULE_ORGANIZATION = "组织管理";

    public static final String CHILD_MODULE_ORGANIZATION_CREATE = "新建组织";

    public static final String CHILD_MODULE_ORGANIZATION_DELETE = "删除组织";

    public static final String CHILD_MODULE_ORGANIZATION_UPDATE = "编辑组织";

    public static final String CHILD_MODULE_ORGANIZATION_ENABLE = "启用/禁用组织";

}
