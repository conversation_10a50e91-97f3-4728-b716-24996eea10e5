package com.holderzone.saas.store.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.resource.common.dto.holder.organization.HolderOrganizationResultDTO;
import com.holderzone.saas.store.dto.organization.OrgGeneralDTO;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;

import java.util.List;
import java.util.Map;

/**
 * 组织表 服务类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
public interface OrganizationService extends IService<OrganizationDO> {

    OrganizationDTO createOrganization(OrganizationDTO organizationDTO);

    void createBatchOrganization(List<HolderOrganizationResultDTO> holderOrganizationResultList);

    /**
     * 降级： 修改所有既是门店又是组织的为只是门店
     */
    void updateDemotedType();

    /**
     * 注：物理删除所有组织
     */
    void removeAllOrganization();

    boolean updateOrganization(OrganizationDTO organizationDTO);

    List<OrganizationDTO> getOptionalOrganization(String organizationGuid);

    List<OrganizationDTO> queryOrganizationList();

    List<OrganizationDTO> queryEnterpriseAndOrganization();

    boolean deleteOrganization(String organizationGuid);

    boolean queryExistOrganizationOrStore(String organizationGuid);

    boolean queryExistAccount(String organizationGuid);

    boolean isExistOrganization();

    List<OrgGeneralDTO> queryAllOrganization();

    List<OrgGeneralDTO> queryErpAndOrgAndStore(Integer queryErp, Integer queryStore);

    List<OrgGeneralDTO> queryOrgByChildIdList(List<String> orgGuidList);

    Map<String, List<OrganizationDTO>> queryOrgParentList(List<String> orgGuidList);

    List<OrganizationDTO> queryOrgByIdList(List<String> orgGuidList);

    List<String> queryAllChildOrg(List<String> orgGuidList);

    boolean initEnterprise(String enterpriseGuid);

    List<String> queryAllOrgGuids();
}
