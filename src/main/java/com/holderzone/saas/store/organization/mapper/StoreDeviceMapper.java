package com.holderzone.saas.store.organization.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.organization.domain.StoreDeviceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 门店-设备关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-10
 */
@Mapper
@Component
public interface StoreDeviceMapper extends BaseMapper<StoreDeviceDO> {

    long batchSort(List<StoreDeviceDO> list);

    String queryTableGuidByDeviceNo(@Param("storeGuid") String storeGuid,@Param("deviceNo") String deviceNo);

    List<String> queryTableGuidByStoreGuid(@Param("storeGuid") String storeGuid);


    int updatePadOrderType(@Param("padOrderType") Integer padOrderType, @Param("storeGuid") String storeGuid,
                           @Param("deviceNo") String deviceNo);

    int cancelPadTableBinding(@Param("storeGuid") String storeGuid, @Param("deviceNo") String deviceNo);
}
