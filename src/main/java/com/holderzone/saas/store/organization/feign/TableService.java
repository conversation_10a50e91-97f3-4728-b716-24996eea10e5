package com.holderzone.saas.store.organization.feign;

import com.holderzone.saas.store.dto.table.PadAreaDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableService
 * @date 2021/8/3 17:14
 * @description 桌台服务调用
 */
@Component
@FeignClient(name = "holder-saas-store-table", fallbackFactory = TableService.TableBack.class)
public interface TableService {

    @GetMapping("/table/query_table_info")
    TableBasicDTO queryTableInfo(@RequestParam("tableGuid") String tableGuid);

    @PostMapping("/table/query_un_binding_table_info")
    List<PadAreaDTO> queryUnBindingTableInfo(@RequestBody List<String> bindingTableGuids,
                                             @RequestParam("tableGuid") String storeGuid);

    @Component
    @Slf4j
    class TableBack implements FallbackFactory<TableService> {

        @Override
        public TableService create(Throwable throwable) {
            return new TableService() {
                @Override
                public TableBasicDTO queryTableInfo(String tableGuid) {
                    log.error("查询桌台服务异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<PadAreaDTO> queryUnBindingTableInfo(List<String> bindingTableGuids, String storeGuid) {
                    log.error("查询桌台服务异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
