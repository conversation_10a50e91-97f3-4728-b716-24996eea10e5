package com.holderzone.saas.store.organization.mq;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.saas.store.organization.service.OrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EnterpriseCreateListener
 * @date 2019/12/18 15:35
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = MqConstant.ENTERPRISE_CREATE_TOPIC,
        tags = MqConstant.ENTERPRISE_CREATE_TAG,
        consumerGroup = MqConstant.ENTERPRISE_CREATE_GROUP
)
public class EnterpriseCreateListener extends AbstractRocketMqConsumer<RocketMqTopic, UnMessage<EnterpriseDTO>> {

    private final OrganizationService organizationService;

    @Autowired
    public EnterpriseCreateListener(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Override
    public boolean consumeMsg(UnMessage<EnterpriseDTO> unMessage, MessageExt messageExt) {
        try {
            log.info("当前时间：{}，云端创建企业[{}]，初始化门店数据",
                    DateTimeUtils.now().toString(), unMessage.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(unMessage.getEnterpriseGuid());
            UserContextUtils.putErp(unMessage.getEnterpriseGuid());
            EnterpriseDTO message = unMessage.getMessage();
            //经营模式“单店”会生成默认门店，“连锁”和“平台”级企业不生成默认门店 bug:19929
            EnterpriseDTO.ManagementModel managementModel = message.getManagementModel();

            log.info("不初始化门店信息，当前企业【{}】，经营模式：【{}】", message.getEnterpriseGuid(), managementModel);
            return true;
        } catch (Exception e) {
            log.error("消费云端" + MqConstant.ENTERPRISE_CREATE_TOPIC + "消息失败，e={}" + e.getMessage());
        } finally {
            EnterpriseIdentifier.remove();
        }
        return false;
    }

}
