package com.holderzone.saas.store.organization.service.remote;

import com.holderzone.framework.base.dto.message.MessageDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PushMsgFeignService
 * @date 2018/02/14 09:00
 * @description 个推消息发送
 * @program holder-saas-store-print
 */
@Component
@FeignClient(value = "base-service", fallbackFactory = MessageClient.DefaultFallbackFactory.class)
public interface MessageClient {

    /**
     * 发送打印消息
     *
     * @param message
     */
    @PostMapping("/message/sendMessage")
    void sendPrintMessage(@RequestBody MessageDTO message);

    @Slf4j
    @Component
    class DefaultFallbackFactory implements FallbackFactory<MessageClient> {

        @Override
        public MessageClient create(Throwable throwable) {
            return message -> log.error("调用base服务推送消息失败", throwable);
        }
    }
}
