package com.holderzone.saas.store.organization.config;

import com.holderzone.framework.slf4j.starter.serializer.LogSerializer;
import com.holderzone.framework.util.JacksonUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LogConfig {

    @Bean
    public LogSerializer logSerializer() {
        return new LogSerializer() {
            @Override
            public String serializer(Object object) {
                return JacksonUtils.writeValueAsString(object);
            }
        };
    }
}
