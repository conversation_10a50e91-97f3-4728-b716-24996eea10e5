package com.holderzone.saas.store.organization.mq;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.enterprise.OrganizationDTO;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.util.MessageType;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.organization.service.StoreService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className OrganizationCloudListener
 * @date 19-1-17 下午4:33
 * @description 监听云端门店新增、修改发送的消息
 * @program holder-saas-store-staff
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = MqConstant.CLOUD_SYNC_ORGANIZATION_TOPIC,
        tags = MqConstant.CLOUD_SYNC_ORGANIZATION_TAG,
        consumerGroup = MqConstant.CLOUD_SYNC_ORGANIZATION_GROUP
)
public class CloudOrgListener extends AbstractRocketMqConsumer<RocketMqTopic, UnMessage> {

    private static LocalTime BUSINESS_START = LocalTime.parse("00:00:00");

    private static LocalTime BUSINESS_END = LocalTime.parse("23:59:59");

    private final StoreService storeService;

    @Autowired
    public CloudOrgListener(StoreService storeService) {
        this.storeService = storeService;
    }

    /**
     * 云端只会同步对门店的新增、修改操作
     *
     * @param unMessage
     * @param messageExt
     * @return
     */
    @Override
    public boolean consumeMsg(UnMessage unMessage, MessageExt messageExt) {
        try {
            MessageType type = MessageType.getTypeByCode(unMessage.getMessageType());
            String ops = type == MessageType.ADD ? "创建"
                    : type == MessageType.UPDATE ? "修改" : "未知操作";
            log.info("当前时间：{}，云端{}门店[{}]，所属企业[{}]",
                    DateTimeUtils.now().toString(), ops, unMessage.getStoreGuid(), unMessage.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(unMessage.getEnterpriseGuid());

            UserContextUtils.putErp(unMessage.getEnterpriseGuid());
            boolean ackResult;
            switch (type) {
                case ADD:
                    ackResult = this.createStore(unMessage);
                    break;
                case UPDATE:
                    ackResult = this.updateStore(unMessage);
                    break;
                default:
                    log.warn("不支持的操作，type：{}", unMessage.getMessageType());
                    ackResult = false;
                    break;
            }
            return ackResult;
        } catch (Exception e) {
            log.error("消费云端" + MqConstant.CLOUD_SYNC_ORGANIZATION_TOPIC + "消息失败，e={}" + e.getMessage());
        } finally {
            EnterpriseIdentifier.remove();
        }
        return false;
    }

    private boolean createStore(UnMessage unMessage) {
        String json = JacksonUtils.writeValueAsString(unMessage.getMessage());
        log.info("云端创建门店参数：{}，所属企业[{}]", json, unMessage.getEnterpriseGuid());

        OrganizationDTO dto = JacksonUtils.toObject(OrganizationDTO.class, json);
        StoreDTO storeDTO = new StoreDTO()
                .setName(dto.getName())
                .setGuid(dto.getOrganizationGuid())
                .setCode(dto.getOrganizationCode())
                .setBusinessStart(BUSINESS_START)
                .setBusinessEnd(BUSINESS_END)
                .setProvinceCode(dto.getProvince())
                .setCityCode(dto.getCity())
                .setCountyCode(dto.getDistrict())
                .setProvinceName(dto.getProvinceName())
                .setCityName(dto.getCityName())
                .setCountyName(dto.getDistrictName())
                .setAddressDetail(dto.getAddress())
                .setLongitude(dto.getLongitude())
                .setLatitude(dto.getLatitude())
                .setContactTel(dto.getContactNumber())
                .setContactName(dto.getContactName())
                .setPhotos(dto.getPhotos())
                .setIsEnable(dto.getEnabled() == 1)
                .setIsDeleted(false)
                .setParentIds(UserContextUtils.getEnterpriseGuid())
                .setCreateUserGuid("云端同步")
                .setModifiedUserGuid("云端同步");

        if (StringUtils.isNotBlank(dto.getParentIds())) {
            storeDTO.setParentIds(dto.getParentIds());
        }

        return storeService.createStore(storeDTO);
    }

    private boolean updateStore(UnMessage unMessage) {
        String json = JacksonUtils.writeValueAsString(unMessage.getMessage());
        log.info("云端修改门店参数：{}，所属企业[{}]", json, unMessage.getEnterpriseGuid());

        OrganizationDTO organizationDTO = JacksonUtils.toObject(OrganizationDTO.class, json);
        StoreDTO storeDTO = new StoreDTO()
                .setGuid(organizationDTO.getOrganizationGuid())
                .setName(organizationDTO.getName())
                .setContactTel(organizationDTO.getContactNumber())
                .setContactName(organizationDTO.getContactName())
                .setProvinceCode(organizationDTO.getProvince())
                .setCityCode(organizationDTO.getCity())
                .setCountyCode(organizationDTO.getDistrict())
                .setProvinceName(organizationDTO.getProvinceName())
                .setCityName(organizationDTO.getCityName())
                .setCountyName(organizationDTO.getDistrictName())
                .setAddressDetail(organizationDTO.getAddress())
                .setLongitude(organizationDTO.getLongitude())
                .setLatitude(organizationDTO.getLatitude())
                .setIsEnable(Objects.isNull(organizationDTO.getEnabled()) ? null : organizationDTO.getEnabled() == 1)
                .setModifiedUserGuid("云端同步");

        return storeService.updateStore(storeDTO, true);
    }
}
