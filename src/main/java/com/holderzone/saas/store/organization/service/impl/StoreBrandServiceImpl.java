package com.holderzone.saas.store.organization.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.organization.domain.StoreBrandDO;
import com.holderzone.saas.store.organization.mapper.StoreBrandMapper;
import com.holderzone.saas.store.organization.service.BrandService;
import com.holderzone.saas.store.organization.service.DistributedIdService;
import com.holderzone.saas.store.organization.service.StoreBrandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

/**
 * 门店-品牌关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2019-01-07
 */
@Slf4j
@Service
public class StoreBrandServiceImpl extends ServiceImpl<StoreBrandMapper, StoreBrandDO> implements StoreBrandService {

    private final BrandService brandService;

    private final DistributedIdService distributedIdService;

    @Autowired
    public StoreBrandServiceImpl(BrandService brandService, DistributedIdService distributedIdService) {
        this.brandService = brandService;
        this.distributedIdService = distributedIdService;
    }

    @Override
    public String createStoreBrand(String storeGuid, @Nullable String brandGuid, String createUserGuid, String modifiedUserGuid) {
        if (!StringUtils.hasText(brandGuid)) {
            brandGuid = brandService.queryDefaultBrand().getGuid();
        }
        StoreBrandDO storeBrandDO = new StoreBrandDO()
                .setGuid(distributedIdService.nextStoreBrandGuid())
                .setStoreGuid(storeGuid)
                .setBrandGuid(brandGuid)
                .setCreateUserGuid(createUserGuid)
                .setModifiedUserGuid(modifiedUserGuid)
                .setGmtCreate(DateTimeUtils.now())
                .setGmtModified(DateTimeUtils.now());
        baseMapper.save(storeBrandDO);

        return brandGuid;
    }
}
