package com.holderzone.saas.store.organization.mapstruct;

import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.organization.domain.BrandDO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className BrandMapstruct
 * @date 19-1-2 下午5:23
 * @description Mapstruct-品牌
 * @program holder-saas-store-organization
 */
@Component
@Mapper(componentModel = "spring")
public interface BrandMapstruct {
    BrandDO brandDTO2DO(BrandDTO brandDO);

    BrandDTO brandDO2DTO(BrandDO brandDO);

    List<BrandDTO> brandDOList2DTOList(List<BrandDO> brandDOList);
}
