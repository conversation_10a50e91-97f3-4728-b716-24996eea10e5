package com.holderzone.saas.store.organization.service.remote;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.trade.StoreDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className TradingClient
 * @date 19-1-3 下午5:58
 * @description
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "holder-saas-store-business", fallbackFactory = TradingClient.ServiceFallBack.class)
public interface TradingClient {
    /**
     * 初始化支付类型
     *
     * @param dto dto
     * @return String String
     */
    @PostMapping(value = "/pay/type/init")
    String initPayType(@RequestBody StoreDTO dto);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<TradingClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TradingClient create(Throwable cause) {
            return dto -> {
                log.error(HYSTRIX_PATTERN, "initPayType", "入参：" + JacksonUtils.writeValueAsString(dto),
                          ThrowableUtils.asString(cause));
                return "failure";
            };
        }
    }
}
