package com.holderzone.saas.store.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.holder.organization.HolderOrganizationResultDTO;
import com.holderzone.saas.store.dto.organization.OrgGeneralDTO;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.organization.constant.OrganizationTypeEnum;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.mapper.OrganizationMapper;
import com.holderzone.saas.store.organization.mapstruct.OrganizationMapstruct;
import com.holderzone.saas.store.organization.mapstruct.StoreMapstruct;
import com.holderzone.saas.store.organization.service.DistributedIdService;
import com.holderzone.saas.store.organization.service.OrganizationService;
import com.holderzone.saas.store.organization.service.StoreService;
import com.holderzone.saas.store.organization.service.remote.UserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 组织 服务实现类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@Service
@Slf4j
public class OrganizationServiceImpl extends ServiceImpl<OrganizationMapper, OrganizationDO> implements OrganizationService {

    private static final int ORGANIZATION = 1;

    private static final int STORE = 2;

    private final OrganizationMapstruct organizationMapstruct;

    private final OrganizationMapper organizationMapper;

    private final DistributedIdService distributedIdService;

    private final StoreService storeService;

    private final StoreMapstruct storeMapstruct;

    private final UserClient userClient;

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    public OrganizationServiceImpl(OrganizationMapstruct organizationMapstruct,
                                   OrganizationMapper organizationMapper,
                                   DistributedIdService distributedIdService, StoreService storeService,
                                   StoreMapstruct storeMapstruct, UserClient userClient) {
        this.organizationMapstruct = organizationMapstruct;
        this.organizationMapper = organizationMapper;
        this.distributedIdService = distributedIdService;
        this.storeService = storeService;
        this.storeMapstruct = storeMapstruct;
        this.userClient = userClient;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrganizationDTO createOrganization(OrganizationDTO organizationDTO) {
        if (this.validateOrganization(organizationDTO)) {
            throw new BusinessException("组织名称已存在，请修改");
        }

        String guid = distributedIdService.nextOrganizationGuid();
        OrganizationDO organizationDO = organizationMapstruct.organizationDTO2DO(organizationDTO)
                .setGuid(guid)
                .setType(ORGANIZATION)
                .setCreateUserGuid(StringUtils.isEmpty(
                        organizationDTO.getCreateUserGuid()) ? UserContextUtils.getUserGuid() : organizationDTO.getCreateUserGuid()
                )
                .setModifiedUserGuid(StringUtils.isEmpty(
                        organizationDTO.getModifiedUserGuid()) ? UserContextUtils.getUserGuid() : organizationDTO.getModifiedUserGuid()
                )
                .setGmtCreate(DateTimeUtils.now())
                .setGmtModified(DateTimeUtils.now());
        this.save(organizationDO);

        // 关联门店
        if (!CollectionUtils.isEmpty(organizationDTO.getStoreList())) {
            organizationDTO.getStoreList().forEach(
                    storeDTO -> {
                        OrganizationDO organization = storeMapstruct.storeDTO2DTO(storeDTO)
                                .setType(STORE)
                                .setParentIds(organizationDTO.getParentIds() + "," + guid)
                                .setGmtModified(DateTimeUtils.now());
                        this.update(organization, new LambdaUpdateWrapper<OrganizationDO>()
                                .eq(OrganizationDO::getType, STORE)
                                .eq(OrganizationDO::getGuid, storeDTO.getGuid())
                        );
                    }
            );
        }

        OrganizationDO savedOrg = getOne(new LambdaQueryWrapper<OrganizationDO>().eq(OrganizationDO::getGuid, guid));
        return organizationMapstruct.organizationDO2DTO(savedOrg);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBatchOrganization(List<HolderOrganizationResultDTO> holderOrganizationResultList) {
        // 降级： 修改所有既是门店又是组织的为只是门店
        updateDemotedType();

        // 物理删除当前所有组织，门店保留
        removeAllOrganization();

        if (CollectionUtils.isEmpty(holderOrganizationResultList)) {
            return;
            // throw new BusinessException("导入组织机构不能为空");
        }
        // 修改parentIds
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        Map<Long, HolderOrganizationResultDTO> holderOrganizationResultMap = holderOrganizationResultList.stream()
                .collect(Collectors.toMap(HolderOrganizationResultDTO::getId, Function.identity(), (key1, key2) -> key1));
        for (HolderOrganizationResultDTO holderResultDTO : holderOrganizationResultList) {
            HolderOrganizationResultDTO parentOrganization = holderOrganizationResultMap.get(holderResultDTO.getParentId());
            if (Objects.isNull(parentOrganization)) {
                holderResultDTO.setParentId(Long.valueOf(enterpriseGuid));
                holderResultDTO.setParentIds(enterpriseGuid);
            }
        }
        // 转换DTO
        List<OrganizationDTO> organizationDTOList = organizationMapstruct.holderOrganizationDTOList2DTOList(holderOrganizationResultList);

        // 批量保存，如果导入组织机构和门店重复，则将type设置为3，既是门店又是组织
        List<OrganizationDO> organizationDOList = Lists.newArrayList();
        for (OrganizationDTO organizationDTO : organizationDTOList) {
            OrganizationDO organizationDO = organizationMapstruct.organizationDTO2DO(organizationDTO)
                    .setType(ORGANIZATION)
                    .setIsBuAccounts(0)
                    .setIsShowCash(1)
                    .setCreateUserGuid("云端同步")
                    .setModifiedUserGuid("云端同步")
                    .setParentIds(Objects.isNull(organizationDTO.getParentIds()) ? "" : organizationDTO.getParentIds())
                    .setGmtCreate(DateTimeUtils.now())
                    .setGmtModified(DateTimeUtils.now());
            organizationDOList.add(organizationDO);
        }
        // 去重
        Map<String, OrganizationDO> organizationMap = organizationDOList.stream().collect(Collectors.toMap(OrganizationDO::getGuid, Function.identity(), (key1, key2) -> key2));
        organizationDOList = new ArrayList<>(organizationMap.values());
        baseMapper.saveBatch(organizationDOList);
        // 同步员工
        UserContext userContext = UserContextUtils.get();
        taskExecutor.execute(() -> {
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            // 查询所有组织guid
            List<String> allOrgGuids = queryAllOrgGuids();
            userClient.syncHolderUser(allOrgGuids);
        });
    }

    @Override
    public void updateDemotedType() {
        UpdateWrapper<OrganizationDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(OrganizationDO::getType, OrganizationTypeEnum.ALL.getType());
        uw.lambda().set(OrganizationDO::getType, OrganizationTypeEnum.STORE.getType());
        update(uw);
    }

    @Override
    public void removeAllOrganization() {
        baseMapper.removeAllOrganization();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrganization(OrganizationDTO organizationDTO) {
        OrganizationDO originalDO = this.getOne(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, organizationDTO.getGuid()));
        // 校验
        if (originalDO.getName().equals(organizationDTO.getName())) {
            if (organizationDTO.getParentIds().split(",").length > 4) {
                throw new BusinessException("组织上下级最大深度不能大于5");
            }
        } else {
            if (this.validateOrganization(organizationDTO)) {
                throw new BusinessException("组织名称重复");
            }
        }

        // 修改了当前组织的上下级
        if (!originalDO.getParentIds().equals(organizationDTO.getParentIds())) {
            // 这里的child为当前修改的组织的所有下级，包括门店
            List<OrganizationDO> child = organizationMapper.selectList(new LambdaQueryWrapper<OrganizationDO>()
                    .like(OrganizationDO::getParentIds, organizationDTO.getGuid())
            );
            // 修改了组织上下级情况下，判断新的组织树的深度是否大于5
            child.forEach(p -> {
                int i = p.getParentIds().indexOf(organizationDTO.getGuid()) - 1;
                p.setParentIds(organizationDTO.getParentIds() + p.getParentIds().substring(i));
                if (p.getParentIds().split(",").length > 4) {
                    throw new BusinessException("企业下组织最大深度为5，请修改当前组织的下级后重试");
                }
            });
            if (!CollectionUtils.isEmpty(child)) {
                this.updateBatchById(child);
            }
        }

        // 关联门店
        if (!CollectionUtils.isEmpty(organizationDTO.getStoreList())) {
            organizationDTO.getStoreList().forEach(
                    storeDTO -> {
                        OrganizationDO organization = storeMapstruct.storeDTO2DTO(storeDTO)
                                .setType(STORE)
                                .setParentIds(organizationDTO.getParentIds() + "," + organizationDTO.getGuid())
                                .setGmtModified(DateTimeUtils.now());
                        this.update(organization, new LambdaUpdateWrapper<OrganizationDO>().eq(OrganizationDO::getType, STORE)
                                .eq(OrganizationDO::getGuid, storeDTO.getGuid())
                        );
                    }
            );
        }

        OrganizationDO organizationDO = organizationMapstruct.organizationDTO2DO(organizationDTO)
                .setGmtModified(DateTimeUtils.now())
                .setModifiedUserGuid(UserContextUtils.getUserGuid());
        return this.update(organizationDO, new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, organizationDTO.getGuid()));
    }

    @Override
    public List<OrganizationDTO> getOptionalOrganization(String organizationGuid) {
        // 查询企业下所有组织并排除掉自己及自己的所有下级
        final List<OrganizationDO> list = this.list(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getType, 1));
        List<OrganizationDTO> organizationDOList = organizationMapstruct.organizationDOList2DTOList(list)
                .stream()
                .filter(p -> !p.getGuid().equals(organizationGuid)
                        && !p.getParentIds().contains(String.valueOf(organizationGuid)))
                .collect(Collectors.toList());

        // 递归组装结果集为上下级结构
        return Collections.singletonList(new OrganizationDTO()
                .setGuid(UserContextUtils.getEnterpriseGuid())
                .setName(UserContextUtils.getEnterpriseName())
                .setChildOrganizationDTOList(this.formatTreeOrganization(
                        organizationDOList, UserContextUtils.getEnterpriseGuid()
                ))
        );
    }

    @Override
    public List<OrganizationDTO> queryOrganizationList() {
        List<OrganizationDO> list = this.list(new LambdaQueryWrapper<OrganizationDO>()
                .in(OrganizationDO::getType, OrganizationTypeEnum.ORGANIZATION.getType(), OrganizationTypeEnum.ALL.getType()));
        return organizationMapstruct.organizationDOList2DTOList(list);
    }

    @Override
    public List<OrganizationDTO> queryEnterpriseAndOrganization() {
        // 查询企业下的所有组织
        List<OrganizationDO> list = this.list(new LambdaQueryWrapper<OrganizationDO>()
                .in(OrganizationDO::getType, OrganizationTypeEnum.ORGANIZATION.getType(), OrganizationTypeEnum.ALL.getType()));
        List<OrganizationDTO> allOrganization = organizationMapstruct.organizationDOList2DTOList(list);
        allOrganization.forEach(
                org -> {
                    List<OrganizationDO> organizationDOList = this.list(new LambdaQueryWrapper<OrganizationDO>()
                            .eq(OrganizationDO::getType, STORE)
                    );
                    ArrayList<OrganizationDO> orgList = new ArrayList<>();
                    for (OrganizationDO organizationDO : organizationDOList) {
                        if (!StringUtils.isEmpty(organizationDO.getParentIds())
                                && organizationDO.getParentIds().endsWith(org.getGuid())) {
                            orgList.add(organizationDO);
                        }
                    }
                    List<StoreDTO> storeDTOList = new ArrayList<>();
                    orgList.forEach(
                            o -> {
                                StoreDTO storeDTO = new StoreDTO();
                                storeDTO.setGuid(o.getGuid());
                                storeDTO.setName(o.getName());
                                storeDTOList.add(storeDTO);
                            }
                    );
                    org.setStoreList(storeDTOList);
                }
        );
        // 将组织结构转换为tree形式
        return Collections.singletonList(new OrganizationDTO()
                .setGuid(UserContextUtils.getEnterpriseGuid())
                .setName(UserContextUtils.getEnterpriseName())
                .setChildOrganizationDTOList(this.formatTreeOrganization(
                        allOrganization, UserContextUtils.getEnterpriseGuid()
                ))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteOrganization(String organizationGuid) {
        OrganizationDO original = this.getOne(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, organizationGuid));

        // TODO 调用staff服务，查询帐号的所属组织（帐号的所属组织暂未实现，该功能暂留）
        if (this.queryExistAccount(organizationGuid)) {
            throw new BusinessException("组织下存在帐号，不可删除");
        }

        // 删除组织逻辑：组织下存在帐号不可删除，删除后将其下级组织及门店挂入上级组织
        // 根据组织guid查询下级组织和门店
        List<OrganizationDO> childOrganizationList = organizationMapper.selectList(
                new LambdaQueryWrapper<OrganizationDO>().like(OrganizationDO::getParentIds, organizationGuid)
        );

        // 只有叶子节点的组织能够删除
        if (!CollectionUtils.isEmpty(childOrganizationList)) {
            throw new BusinessException("组织存在下级组织，不可删除");
        }

        return remove(new LambdaQueryWrapper<OrganizationDO>().eq(OrganizationDO::getGuid, organizationGuid));

        // 设置门店及下属组织的的parentIds为当前组织的parentIds
//        boolean updateChildOrganizationResult = true;
//        if (childOrganizationList != null && !childOrganizationList.isEmpty()) {
//            childOrganizationList.forEach(p -> {
//                p.setParentIds(p.getParentIds().replace("," + organizationGuid, ""));
//            });
//            updateChildOrganizationResult = this.updateBatchById(childOrganizationList, childOrganizationList.size());
//        }
//        boolean updateOrganization = this.remove(
//                new LambdaQueryWrapper<OrganizationDO>().eq(OrganizationDO::getGuid, organizationGuid));
//        return updateChildOrganizationResult && updateOrganization;
    }

    @Override
    public boolean queryExistOrganizationOrStore(String organizationGuid) {
        Integer count = organizationMapper.selectCount(new LambdaQueryWrapper<OrganizationDO>()
                .likeLeft(OrganizationDO::getParentIds, organizationGuid));

        return count == null || !count.equals(0);
    }

    @Override
    public boolean queryExistAccount(String organizationGuid) {
        // TODO 调用staff服务，查询帐号的所属组织（帐号的所属组织暂未实现，该功能暂留）
        return false;
    }

    @Override
    public boolean isExistOrganization() {
        return baseMapper.countOrganization() > 0;
    }

    @Override
    public List<OrgGeneralDTO> queryAllOrganization() {
        return this.queryErpAndOrgAndStore(0, 0);
    }

    @Override
    public List<OrgGeneralDTO> queryErpAndOrgAndStore(Integer queryErp, Integer queryStore) {
        List<OrgGeneralDTO> result = new ArrayList<>();
        List<OrganizationDO> organizationDOList;
        if (queryStore == 1) {
            organizationDOList = organizationMapper.selectList(new LambdaQueryWrapper<>());
        } else {
            organizationDOList = organizationMapper.selectList(
                    new LambdaQueryWrapper<OrganizationDO>().eq(OrganizationDO::getType, 1));
        }
        if (queryErp == 1) {
            result.add(new OrgGeneralDTO(
                    UserContextUtils.getEnterpriseGuid(),
                    UserContextUtils.getEnterpriseName(),
                    0, true,
                    this.formatTreeOrgGeneralDTO(
                            organizationDOList,
                            UserContextUtils.getEnterpriseGuid(),
                            Collections.emptyList()
                    )
            ));
        } else {
            // parentIds size最小为最顶级
            if (CollectionUtils.isEmpty(organizationDOList)) {
                return result;
            }
            int size = organizationDOList.stream()
                    .min(Comparator.comparing(p -> p.getParentIds().split(",").length))
                    .get()
                    .getParentIds()
                    .split(",")
                    .length;
            result = organizationDOList.stream()
                    .filter(p -> p.getParentIds().split(",").length == size)
                    .map(p -> new OrgGeneralDTO(p.getGuid(), p.getName(), p.getType(), true, null))
                    .collect(Collectors.toList());
            result.forEach(p -> p.setChildren(this.formatTreeOrgGeneralDTO(
                    organizationDOList, p.getGuid(), Collections.emptyList())
            ));
        }
        return result;
    }

    @Override
    public List<OrgGeneralDTO> queryOrgByChildIdList(List<String> orgGuidList) {
        if (orgGuidList == null || orgGuidList.isEmpty()) {
            return Collections.emptyList();
        }

        // childList为传入的组织
        List<OrganizationDO> currentDOList = organizationMapper.selectList(
                new LambdaQueryWrapper<OrganizationDO>().in(OrganizationDO::getGuid, orgGuidList)
        );

        // parentIdSet为去重后的上级组织guid集合
        HashSet<String> parentIdSet = new HashSet<>();
        currentDOList.forEach(p -> parentIdSet.addAll(Arrays.asList(p.getParentIds().split(","))));

        // organizationDOList为所有传入组织的上下级
        Collection<OrganizationDO> organizationDOList = new TreeSet<>(Comparator.comparing(OrganizationDO::getGuid));

        LambdaQueryWrapper<OrganizationDO> queryWrapper = new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getType, 1);
        queryWrapper.nested(e -> {
            currentDOList.forEach((c) -> e.or().like(OrganizationDO::getParentIds, c.getGuid()));
            return e;
        });

        // 先添加自己进去
        organizationDOList.addAll(currentDOList);
        // 在添加自己的所有上级
        if (!parentIdSet.isEmpty()) {
            organizationDOList.addAll(organizationMapper.selectList(
                    new LambdaQueryWrapper<OrganizationDO>().in(OrganizationDO::getGuid, parentIdSet))
            );
        }
        // 再添加自己的所有下级
        organizationDOList.addAll(organizationMapper.selectList(queryWrapper));
        if (organizationDOList.isEmpty()) {
            return Collections.emptyList();
        }
        // parentIds的size最小为最顶级的组织（若组织直接挂在企业下则parentIds中不含逗号，size为）
        List<OrgGeneralDTO> resultList;
        if (organizationDOList.stream().anyMatch(p -> !p.getParentIds().contains(","))) {
            resultList = organizationDOList.stream()
                    .filter(p -> p.getParentIds().equals(UserContextUtils.getEnterpriseGuid()))
                    .map(p -> new OrgGeneralDTO(p.getGuid(), p.getName(), p.getType(), true, null))
                    .collect(Collectors.toList());
        } else {
            int size = organizationDOList.stream()
                    .min(Comparator.comparing(p -> p.getParentIds().split(",").length))
                    .get()
                    .getParentIds()
                    .split(",")
                    .length;
            resultList = organizationDOList.stream()
                    .filter(p -> p.getParentIds().split(",").length == size)
                    .map(p -> new OrgGeneralDTO(p.getGuid(), p.getName(), p.getType(), true, null))
                    .collect(Collectors.toList());
        }

        resultList.forEach(p -> p.setChildren(this.formatTreeOrgGeneralDTO(
                new ArrayList<>(organizationDOList), p.getGuid(), Collections.emptyList())
        ));
        return resultList;
    }

    @Override
    public Map<String, List<OrganizationDTO>> queryOrgParentList(List<String> orgGuidList) {
        // 查询出企业下的所有组织
        List<OrganizationDO> doList = organizationMapper.selectList(null);

        Map<String, List<OrganizationDTO>> result = new HashMap<>(orgGuidList.size());
        if (orgGuidList.remove(UserContextUtils.getEnterpriseGuid())) {
            List<OrganizationDTO> value = Collections.singletonList(new OrganizationDTO()
                    .setName(UserContextUtils.getEnterpriseName())
                    .setGuid(UserContextUtils.getEnterpriseGuid())
            );
            result.put(UserContextUtils.getEnterpriseGuid(), value);
        }
        orgGuidList.forEach(p -> {
            OrganizationDO current = doList.stream().filter(i -> i.getGuid().equals(p)).findAny().orElse(null);
            if (Objects.isNull(current)) {
                return;
            }
            List<OrganizationDTO> parentList = new ArrayList<>();
            parentList.add(new OrganizationDTO()
                    .setGuid(UserContextUtils.getEnterpriseGuid())
                    .setName(UserContextUtils.getEnterpriseName())
            );
            List<OrganizationDO> collect = doList.stream()
                    .filter(k -> Arrays.asList(current.getParentIds().split(","))
                            .contains(k.getGuid()) || k.getGuid().equals(p))
                    .sorted(Comparator.comparing(j -> j.getParentIds().split(",").length))
                    .collect(Collectors.toList());
            parentList.addAll(organizationMapstruct.organizationDOList2DTOList(collect));
            result.put(p, parentList);
        });
        return result;
    }

    @Override
    public List<OrganizationDTO> queryOrgByIdList(List<String> orgGuidList) {
        if (CollectionUtils.isEmpty(orgGuidList)) {
            return Collections.emptyList();
        }
        List<OrganizationDO> organizationDOList = organizationMapper.selectList(
                new LambdaQueryWrapper<OrganizationDO>()
                        .select(OrganizationDO::getGuid,
                                OrganizationDO::getName)
                        .in(OrganizationDO::getGuid, orgGuidList)
        );
        return organizationMapstruct.organizationDOList2DTOList(organizationDOList);
    }

    @Override
    public List<String> queryAllChildOrg(List<String> orgGuidList) {
        // 根据传入的组织/门店id集合查询其所有下级（返回值中包括自己）
        List<String> result = organizationMapper.queryAllChildOrg(orgGuidList);
        result.addAll(orgGuidList);
        return result.stream().distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean initEnterprise(String enterpriseGuid) {
        // 切库
        if (StringUtils.isEmpty(enterpriseGuid)) {
            log.info("初始化企业失败，企业GUID为空");
            return false;
        }
        try {
            UserContextUtils.putErp(enterpriseGuid);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);

            log.info("开始初始化企业【{}】的组织/门店/品牌数据", enterpriseGuid);
            // 创建组织
            OrganizationDTO globalOrg = new OrganizationDTO();

            globalOrg.setName("全球");
            globalOrg.setParentIds(enterpriseGuid);
            globalOrg.setCreateUserGuid("企业初始化");
            globalOrg.setModifiedUserGuid("企业初始化");
            globalOrg = createOrganization(globalOrg);
            log.info("企业【{}】-组织【{}】初始化成功", enterpriseGuid, JacksonUtils.writeValueAsString(globalOrg));
            OrganizationDTO chineseOrg = new OrganizationDTO();
            chineseOrg.setParentIds(enterpriseGuid + "," + globalOrg.getGuid());
            chineseOrg.setName("中国");
            chineseOrg.setCreateUserGuid("企业初始化");
            chineseOrg.setModifiedUserGuid("企业初始化");
            chineseOrg = createOrganization(chineseOrg);
            log.info("企业【{}】-组织【{}】初始化成功", enterpriseGuid, JacksonUtils.writeValueAsString(chineseOrg));

            // 创建默认门店 门店创建时会创建默认品牌-我的品牌
            StoreDTO storeDTO = new StoreDTO();
            storeDTO.setName("我的门店");
            storeDTO.setIsEnable(true);
            storeDTO.setParentIds(enterpriseGuid + "," + globalOrg.getGuid() + "," + chineseOrg.getGuid());
            storeDTO.setBusinessStart(LocalTime.MIN);
            storeDTO.setBusinessEnd(LocalTime.of(23, 59, 59, 0));
            storeDTO.setCreateUserGuid("企业初始化");
            storeDTO.setModifiedUserGuid("企业初始化");
            boolean store = storeService.createStore(storeDTO);
            log.info("企业【{}】-门店【{}】创建成功", enterpriseGuid, JacksonUtils.writeValueAsString(storeDTO));
            return store;
        } catch (Exception e) {
            log.error("企业【{}】初始化组织/门店数据发生异常", enterpriseGuid);
            e.printStackTrace();
            throw new BusinessException("企业【" + enterpriseGuid + "】初始化组织/门店数据发生异常");
        }
    }

    @Override
    public List<String> queryAllOrgGuids() {
        QueryWrapper<OrganizationDO> qw = new QueryWrapper<>();
        qw.lambda().in(OrganizationDO::getType, Lists.newArrayList(OrganizationTypeEnum.ALL.getType(), OrganizationTypeEnum.ORGANIZATION.getType()));
        qw.lambda().select(OrganizationDO::getGuid);
        List<OrganizationDO> organizationDOList = list(qw);
        if (CollectionUtils.isEmpty(organizationDOList)) {
            return Lists.newArrayList();
        }
        return organizationDOList.stream().map(OrganizationDO::getGuid).collect(Collectors.toList());
    }

    /**
     * 封装组织扁平结构为上下级结构，同时根据canCheckGuidList中的数据标记返回值中的isCheck字段
     *
     * @param organizationDOList 扁平组织集合
     * @param parentId           上级组织id
     * @param canCheckGuidList   在该集合中存在的数据isCheck为true
     * @return 上下级结构
     */
    private List<OrgGeneralDTO> formatTreeOrgGeneralDTO(List<OrganizationDO> organizationDOList,
                                                        String parentId, List<String> canCheckGuidList) {
        List<OrgGeneralDTO> firstChildList = new ArrayList<>();
        List<OrganizationDO> notFirstChildList = organizationDOList.stream()
                .filter(p -> {
                    boolean flag = p.getParentIds().endsWith(parentId);
                    if (flag) {
                        OrgGeneralDTO e = new OrgGeneralDTO(p.getGuid(), p.getName(), p.getType(),
                                canCheckGuidList.contains(p.getGuid()), null);
                        firstChildList.add(e);
                    }
                    return !flag;
                }).collect(Collectors.toList());

        if (!firstChildList.isEmpty()) {
            firstChildList.forEach(p -> {
                List<OrgGeneralDTO> list = this.formatTreeOrgGeneralDTO(notFirstChildList,
                        String.valueOf(p.getGuid()), canCheckGuidList);
                p.setChildren(list);
            });
        }

        return firstChildList.size() > 0 ? firstChildList : null;
    }

    /**
     * 根据组织列表与上级组织id递归查找该id下的所有下级并装换为tree形式
     *
     * @param organizationDTOList 组织列表
     * @param parentId            上级组织id
     * @return tree结构下级组织
     */
    private List<OrganizationDTO> formatTreeOrganization(List<OrganizationDTO> organizationDTOList, String parentId) {
        List<OrganizationDTO> firstChildList = new ArrayList<>();
        List<OrganizationDTO> notFirstChildList = organizationDTOList.stream()
                .filter(p -> {
                    boolean flag = p.getParentIds().endsWith(parentId);
                    if (flag) {
                        firstChildList.add(p);
                    }
                    return !flag;
                }).collect(Collectors.toList());

        if (!firstChildList.isEmpty()) {
            firstChildList.forEach(p -> {
                List<OrganizationDTO> list = this.formatTreeOrganization(notFirstChildList, String.valueOf(p.getGuid()));
                p.setChildOrganizationDTOList(list);
                // 移除已格式化的组织，提高效率
                notFirstChildList.removeAll(list);
            });
        }
        return firstChildList.isEmpty() ? Collections.emptyList() : firstChildList;
    }

    /**
     * 新增或修改时判断同企业下组织名称是否重复、最大深度是否大于5
     *
     * @param organizationDTO DTO
     * @return true-重复，false-不重复
     */
    private boolean validateOrganization(OrganizationDTO organizationDTO) {
        Integer result = organizationMapper.selectCount(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getName, organizationDTO.getName())
                .eq(OrganizationDO::getType, 1)
        );
        if (organizationDTO.getParentIds().split(",").length > 4) {
            throw new BusinessException("组织上下级最大深度不能大于5");
        }
        return result == null || !result.equals(0);
    }
}
