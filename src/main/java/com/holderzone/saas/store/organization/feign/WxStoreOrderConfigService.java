package com.holderzone.saas.store.organization.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreOrderConfigService
 * @date 2021/8/3 17:14
 * @description 微信配置调用服务
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxStoreOrderConfigService.WxStoreOrderConfigBack.class)
public interface WxStoreOrderConfigService {

    @PostMapping("/wx_store_order_config/get_pad_background_url")
    String getPadBackgroundUrl(@RequestParam("storeGuid") String storeGuid);

    /**
     * 查询门店配置
     *
     * @param storeGuid 门店guid
     * @return 门店配置
     */
    @ApiOperation("查询门店配置")
    @GetMapping("/wx-store-menu-provide/store_config")
    WxOrderConfigDTO getStoreConfig(@RequestParam("storeGuid") String storeGuid);

    @Component
    @Slf4j
    class WxStoreOrderConfigBack implements FallbackFactory<WxStoreOrderConfigService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WxStoreOrderConfigService create(Throwable throwable) {
            return new WxStoreOrderConfigService() {

                @Override
                public String getPadBackgroundUrl(String storeGuid) {
                    log.error("pad背景图查询异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxOrderConfigDTO getStoreConfig(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getStoreConfig", storeGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
