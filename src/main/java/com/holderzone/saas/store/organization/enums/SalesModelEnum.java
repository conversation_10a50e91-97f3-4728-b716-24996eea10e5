package com.holderzone.saas.store.organization.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售模式枚举: 1 普通模式 2 菜谱方案
 */
@Getter
@AllArgsConstructor
public enum SalesModelEnum {

    /**
     * 品牌商品库直接推送商品至门店
     */
    NORMAL_MODE(1, "普通模式"),

    /**
     * 菜谱方案推送商品至门店
     */
    PRICE_PLAN(2, "菜谱方案"),
    ;

    private final Integer code;

    private final String desc;

    public static String getDesc(int code) {
        for (SalesModelEnum e : SalesModelEnum.values()) {
            if (e.getCode() == code) {
                return e.getDesc();
            }
        }
        return "未知状态";
    }
}
