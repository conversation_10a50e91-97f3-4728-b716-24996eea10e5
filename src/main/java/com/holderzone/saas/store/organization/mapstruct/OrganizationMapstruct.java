package com.holderzone.saas.store.organization.mapstruct;

import com.holderzone.resource.common.dto.holder.organization.HolderOrganizationResultDTO;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className OrganizationMapstruct
 * @date 19-1-3 下午4:54
 * @description Mapstruct-组织
 * @program holder-saas-store-organization
 */
@Component
@Mapper(componentModel = "spring")
public interface OrganizationMapstruct {

    OrganizationDO organizationDTO2DO(OrganizationDTO organizationDTO);

    OrganizationDTO organizationDO2DTO(OrganizationDO organizationDO);

    List<OrganizationDTO> organizationDOList2DTOList(List<OrganizationDO> organizationDOList);

    @Mappings({
            @Mapping(target = "guid", source = "id")
    })
    OrganizationDTO holderOrganizationDTO2DTO(HolderOrganizationResultDTO holderOrganizationResultDTO);

    List<OrganizationDTO> holderOrganizationDTOList2DTOList(List<HolderOrganizationResultDTO> holderOrganizationResultDTOList);
}
