package com.holderzone.saas.store.organization.mapstruct;

import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className BrandMapstruct
 * @date 19-1-2 下午5:23
 * @description Mapstruct-门店
 * @program holder-saas-store-organization
 */
@Component
@Mapper(componentModel = "spring")
public interface StoreMapstruct {
    OrganizationDO storeDTO2DTO(StoreDTO storeDTO);

    StoreDTO organizationDO2DTO(OrganizationDO organizationDO, String belongBrandGuid);

    OrganizationDTO organizationDTO2DO(OrganizationDO organizationDO);

    List<OrganizationDTO> organizationList2DOList(List<OrganizationDO> organizationDOList);

    List<StoreDTO> organizationList2DTOList(List<OrganizationDO> organizationDOList);
}
