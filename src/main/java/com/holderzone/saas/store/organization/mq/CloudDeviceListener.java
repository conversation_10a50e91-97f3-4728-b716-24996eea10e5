package com.holderzone.saas.store.organization.mq;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.device.DeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceUnbindDTO;
import com.holderzone.saas.store.organization.service.StoreDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CloudDeviceListener
 * @date 2019/09/25 17:45
 * @description 监听云端设备解绑消息
 * @program holder-saas-store
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = MqConstant.CLOUD_SYNC_DEVICE_TOPIC,
        tags = MqConstant.DEVICE_UNBIND_DOWNLOAD_TAG,
        consumerGroup = MqConstant.DEVICE_UNBIND_GROUP
)
public class CloudDeviceListener extends AbstractRocketMqConsumer<RocketMqTopic, DeviceDTO> {
    private final StoreDeviceService storeDeviceService;

    @Autowired
    public CloudDeviceListener(StoreDeviceService storeDeviceService) {
        this.storeDeviceService = storeDeviceService;
    }

    @Override
    public boolean consumeMsg(DeviceDTO deviceDTO, MessageExt messageExt) {
        try {
            log.info("云端解绑设备，设备信息：{}", JacksonUtils.writeValueAsString(deviceDTO));
            EnterpriseIdentifier.setEnterpriseGuid(deviceDTO.getEnterpriseGuid());
            UserContext userContext = new UserContext();
            userContext.setEnterpriseGuid(deviceDTO.getEnterpriseGuid());
            userContext.setUserGuid(deviceDTO.getStaffGuid());
            userContext.setUserName(deviceDTO.getStaff());
            UserContextUtils.put(userContext);
            StoreDeviceUnbindDTO storeDeviceUnbindDTO = new StoreDeviceUnbindDTO();
            storeDeviceUnbindDTO.setDeviceGuid(deviceDTO.getDeviceGuid());
            storeDeviceUnbindDTO.setDeviceNo(deviceDTO.getDeviceNo());
            storeDeviceUnbindDTO.setUnbindUserGuid(deviceDTO.getStaffGuid());
            storeDeviceUnbindDTO.setUnbindUserName(deviceDTO.getStaff());
            storeDeviceUnbindDTO.setGmtUnbindTime(LocalDateTime.now());
            storeDeviceUnbindDTO.setFromCloud(Boolean.TRUE);
            storeDeviceUnbindDTO.setStoreGuid(deviceDTO.getStoreGuid());
            return storeDeviceService.unbind(storeDeviceUnbindDTO);
        } catch (Exception e) {
            log.error("消费云端" + MqConstant.CLOUD_SYNC_DEVICE_TOPIC + "消息失败，e={}" + e.getMessage());
        } finally {
            EnterpriseIdentifier.remove();
        }
        return false;
    }
}
