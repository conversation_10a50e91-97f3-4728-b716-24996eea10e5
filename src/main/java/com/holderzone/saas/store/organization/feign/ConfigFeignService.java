package com.holderzone.saas.store.organization.feign;

import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(name = "holder-saas-store-config", fallbackFactory = ConfigFeignService.ConfigFallBack.class)
public interface ConfigFeignService {

    /**
     * 保存、更新门店置满时间
     *
     * @param configReqDTO
     * @return 保存更新行数
     */
    @PostMapping("/config/save_config")
    Integer saveEstimateResetTime(@RequestBody ConfigReqDTO configReqDTO);

    /**
     * 查询门店估清置满时间
     *
     * @param configReqQueryDTO
     * @return 查询门店估清置满时间
     */
    @PostMapping("/config/get_config_by_code")
    ConfigRespDTO selectEstimateResetTime(@RequestBody ConfigReqQueryDTO configReqQueryDTO);

    /**
     * 更具门店guid,企业id, code查询
     * @param request
     * @return
     */
    @PostMapping("/config/get_config_by_code")
    ConfigRespDTO getConfigByCode(@RequestBody @Valid ConfigReqQueryDTO request) ;

    /**
     * 刪除指定的数据内容
     * @param request
     */
    @PostMapping("/config/delete_config")
    void deleteConfig(@RequestBody ConfigReqDTO request);

    /**
     * 查询当前时间的数据
     * @param request
     * @return
     */
    @PostMapping("/config/queryconfig")
    List<ConfigRespDTO> queryConfig(@RequestBody ConfigReqDTO request);


    @PostMapping("/config/get_config")
    List<ConfigRespDTO> getConfig(@RequestBody ConfigReqDTO request) ;


    @Component
    @Slf4j
    class ConfigFallBack implements FallbackFactory<ConfigFeignService> {

        @Override
        public ConfigFeignService create(Throwable throwable) {
            return new ConfigFeignService() {

                @Override
                public Integer saveEstimateResetTime(ConfigReqDTO configReqDTO) {
                    log.error("设置估清重置时间异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public ConfigRespDTO selectEstimateResetTime(ConfigReqQueryDTO configReqQueryDTO) {
                    log.error("查询门店估清置满时间异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public ConfigRespDTO getConfigByCode(@Valid ConfigReqQueryDTO request) {
                    log.error("查询门店估清置满时间异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public void deleteConfig(ConfigReqDTO request) {
                    log.error("删除指定的数据内容：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<ConfigRespDTO> queryConfig(ConfigReqDTO request) {
                    log.error("查询当前时间的数据：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<ConfigRespDTO> getConfig(ConfigReqDTO request) {
                    log.error("查询当前时间的数据：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }

}