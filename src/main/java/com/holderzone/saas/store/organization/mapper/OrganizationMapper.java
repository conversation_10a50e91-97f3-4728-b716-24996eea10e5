package com.holderzone.saas.store.organization.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 组织表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@Mapper
@Component
public interface OrganizationMapper extends BaseMapper<OrganizationDO> {

    List<OrganizationDO> selectByCondition(@Param("queryStoreDTO") QueryStoreDTO storeParserDTO);

    List<OrganizationDO> selectByConditionNoPage(@Param("queryStoreDTO") StoreParserDTO storeParserDTO);

    List<String> parseByCondition(@Param("storeParserDTO") StoreParserDTO storeParserDTO);

    List<String> parseByConditionNotUnion(@Param("storeParserDTO") StoreParserDTO storeParserDTO);

    List<OrganizationDO> queryStoreByRegionList(List<RegionDTO> regionDTOList);

    List<String> queryAllChildOrg(List<String> orgGuidList);

    List<StoreDTO> queryStoreDetail(List<String> orgGuidList);

    OrganizationDO queryByGuid(String guid);

    int updateStoreLogoUrl(@Param("logoUrl") String logoUrl, @Param("list") List<String> storeGuidList);

    int updateItemUpload(@Param("guid") String guid, @Param("isItemUpload") Integer isItemUpload);

    void saveBatch(@Param("list") List<OrganizationDO> list);

    void removeAllOrganization();

    int countOrganization();

    /**
     * 批量更新品牌下门店信息
     */
    void batchUpdateBuAccounts(@Param("storeList") List<String> storeList,
                               @Param("store") StoreDTO storeDTO);


    StoreDTO getStoreBase(@Param("guid") String guid);

}
