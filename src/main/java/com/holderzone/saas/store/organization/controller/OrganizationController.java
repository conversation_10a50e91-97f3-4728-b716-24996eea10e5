package com.holderzone.saas.store.organization.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.slf4j.starter.anno.LogAfter;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.organization.OrgGeneralDTO;
import com.holderzone.saas.store.dto.organization.OrganizationBatchCreateDTO;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.organization.service.OrganizationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className OrganizationController
 * @date 19-1-3 下午4:50
 * @description Controller-组织
 * @program holder-saas-store-organization
 */
@Slf4j
@RestController
@RequestMapping("/organization")
@Api(description = "组织相关接口")
public class OrganizationController {

    private final OrganizationService organizationService;

    @Autowired
    public OrganizationController(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    /**
     * 创建组织
     *
     * @param organizationDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("创建组织")
    @PostMapping("/create")
    @LogBefore(value = "创建组织", logLevel = LogLevel.INFO)
    @LogAfter(value = "创建组织", logLevel = LogLevel.DEBUG)
    public OrganizationDTO createOrganization(@RequestBody @Validated OrganizationDTO organizationDTO) {
        return organizationService.createOrganization(organizationDTO);
    }

    @ApiOperation("批量新增或修改组织")
    @PostMapping("/batch/create")
    @LogBefore(value = "批量新增或修改组织", logLevel = LogLevel.INFO)
    @LogAfter(value = "批量新增或修改组织", logLevel = LogLevel.DEBUG)
    public void createBatchOrganization(@RequestBody @Valid OrganizationBatchCreateDTO createDTO) {
        log.info("批量新增组织入参:{}", JacksonUtils.writeValueAsString(createDTO));
        organizationService.createBatchOrganization(createDTO.getHolderOrganizationResultList());
    }

    /**
     * 编辑组织
     *
     * @param organizationDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("编辑组织")
    @PostMapping("/update")
    @LogBefore(value = "编辑组织", logLevel = LogLevel.INFO)
    @LogAfter(value = "编辑组织", logLevel = LogLevel.DEBUG)
    public boolean updateOrganization(@RequestBody OrganizationDTO organizationDTO) {
        return organizationService.updateOrganization(organizationDTO);
    }

    /**
     * 删除组织
     *
     * @param organizationGuid guid
     * @return true-成功，false-失败
     */
    @ApiOperation("删除组织")
    @PostMapping("/delete")
    @LogBefore(value = "删除组织", logLevel = LogLevel.INFO)
    @LogAfter(value = "删除组织", logLevel = LogLevel.DEBUG)
    public boolean deleteOrganization(@RequestParam("organizationGuid") String organizationGuid) {
        return organizationService.deleteOrganization(organizationGuid);
    }

    /**
     * 根据organizationGuid查询是否存在下级组织或门店
     *
     * @param organizationGuid 组织guid
     * @return true-是，false-否
     */
    @ApiOperation("根据organizationGuid查询是否存在下级组织或门店")
    @PostMapping("/query_exist_organization_or_store")
    @LogBefore(value = "根据organizationGuid查询是否存在下级组织或门店", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据organizationGuid查询是否存在下级组织或门店", logLevel = LogLevel.DEBUG)
    public boolean queryExistOrganizationOrStore(@RequestParam("organizationGuid") String organizationGuid) {
        return organizationService.queryExistOrganizationOrStore(organizationGuid);
    }

    /**
     * 根据organizationGuid查询该组织是否关联了帐号
     *
     * @param organizationGuid 组织guid
     * @return true-是，false-否
     */
    @ApiOperation("根据organizationGuid查询该组织是否关联了帐号")
    @PostMapping("/query_exist_account")
    @LogBefore(value = "根据organizationGuid查询该组织是否关联了帐号", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据organizationGuid查询该组织是否关联了帐号", logLevel = LogLevel.DEBUG)
    public boolean queryExistAccount(@RequestParam("organizationGuid") String organizationGuid) {
        return organizationService.queryExistAccount(organizationGuid);
    }

    /**
     * 根据组织guid查询该组织可选的上级组织（排除掉自己以及自己的所有下级）
     *
     * @param organizationGuid 组织guid
     * @return 可选的上级组织
     */
    @ApiOperation("根据组织guid查询该组织可选的上级组织（排除掉自己以及自己的所有下级）")
    @PostMapping("/get_optional_organization")
    @LogBefore(value = "根据组织guid查询该组织可选的上级组织", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据组织guid查询该组织可选的上级组织", logLevel = LogLevel.DEBUG)
    public List<OrganizationDTO> getOptionalOrganization(@RequestParam("organizationGuid") String organizationGuid) {
        return organizationService.getOptionalOrganization(organizationGuid);
    }

    /**
     * 获取企业及企业下的所有组织
     *
     * @return 企业+组织树
     */
    @ApiOperation("获取企业及企业下的所有组织")
    @PostMapping("/query_enterprise_and_organization")
    @LogAfter(value = "获取企业及企业下的所有组织", logLevel = LogLevel.DEBUG)
    public List<OrganizationDTO> queryEnterpriseAndOrganization() {
        return organizationService.queryEnterpriseAndOrganization();
    }

    /**
     * 查询企业下所有组织，列表结构
     */
    @ApiOperation("查询企业下所有组织，列表结构")
    @PostMapping("/query_organization/list")
    @LogAfter(value = "获取企业及企业下的所有组织", logLevel = LogLevel.DEBUG)
    public List<OrganizationDTO> queryOrganizationList(@RequestParam(value = "enterpriseGuid", required = false) String enterpriseGuid) {
        if (!StringUtils.isEmpty(enterpriseGuid)) {
            UserContextUtils.putErp(enterpriseGuid);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        }
        return organizationService.queryOrganizationList();
    }

    /**
     * 查询企业下的所有组织
     *
     * @return 组织列表
     */
    @ApiOperation("查询企业下所有组织")
    @PostMapping("/query_all_organization")
    @LogAfter(value = "获取企业及企业下的所有组织", logLevel = LogLevel.DEBUG)
    public List<OrgGeneralDTO> queryAllOrganization() {
        return organizationService.queryAllOrganization();
    }


    @ApiOperation("查询该企业下是否存在组织机构")
    @GetMapping("/is_exist_organization")
    public Boolean isExistOrganization() {
        return organizationService.isExistOrganization();
    }

    /**
     * 查询企业、组织、门店并返回上下级结构
     *
     * @param queryErp   是否查询企业，0-否，1-是
     * @param queryStore 是否查询门店，0-否，1-是
     * @return 上下级结构
     */
    @ApiOperation("查询企业、组织、门店并返回上下级结构")
    @PostMapping("/query_erp_org_store")
    @LogBefore(value = "查询企业、组织、门店并返回上下级结构", logLevel = LogLevel.INFO)
    @LogAfter(value = "查询企业、组织、门店并返回上下级结构", logLevel = LogLevel.DEBUG)
    public List<OrgGeneralDTO> queryErpOrgStore(@ApiParam("是否查询企业，0-否，1-是") @RequestParam(value = "queryErp", defaultValue = "1") Integer queryErp,
                                                @ApiParam("是否查询门店，0-否，1-是") @RequestParam(value = "queryStore", defaultValue = "1") Integer queryStore) {
        return organizationService.queryErpAndOrgAndStore(queryErp, queryStore);
    }

    /**
     * 根据组织id集合组装它tree结构的上下级组织
     *
     * @param orgGuidList 组织guid集合
     * @return 上下级组织（树形结构）
     */
    @ApiOperation("查询企业、组织并返回树形结构")
    @PostMapping("/query_org_by_child")
    @LogBefore(value = "查询企业、组织并返回树形结构", logLevel = LogLevel.INFO)
    @LogAfter(value = "查询企业、组织并返回树形结构", logLevel = LogLevel.DEBUG)
    public List<OrgGeneralDTO> queryOrgByChildIdList(@ApiParam("组织guid集合") @RequestBody List<String> orgGuidList) {
        return organizationService.queryOrgByChildIdList(orgGuidList);
    }

    /**
     * 根据传入的组织id集合查询自己及自己的上级（包括商户）返回map中key为当前组织id，value为当前组织包括企业的所有上级（扁平结构）
     *
     * @param orgGuidList 组织guid集合
     * @return map
     */
    @ApiOperation("根据传入的组织id集合查询每个组织id的上级组织（包括企业）的扁平结构")
    @PostMapping("/query_org_parent_list")
    @LogBefore(value = "根据传入的组织id集合查询每个组织id的上级组织", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据传入的组织id集合查询每个组织id的上级组织", logLevel = LogLevel.DEBUG)
    public Map<String, List<OrganizationDTO>> queryOrgParentList(@ApiParam("组织guid集合") @RequestBody List<String> orgGuidList) {
        return organizationService.queryOrgParentList(orgGuidList);
    }

    /**
     * 根据传入的guid数组查询组织列表（返回扁平结构、只包含guid和name两列）
     *
     * @param orgGuidList 组织guid集合
     * @return 扁平结构组织列表
     */
    @ApiOperation("根据传入的guid数组查询组织列表")
    @PostMapping("/query_org_by_idlist")
    @LogBefore(value = "根据传入的guid数组查询组织列表", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据传入的guid数组查询组织列表", logLevel = LogLevel.DEBUG)
    public List<OrganizationDTO> queryOrgByIdList(@ApiParam("组织guid集合") @RequestBody List<String> orgGuidList) {
        return organizationService.queryOrgByIdList(orgGuidList);
    }

    /**
     * 根据传入的企业或组织数组查询其所有下级
     *
     * @param orgGuidList 企业或组织数组
     * @return 扁平guid集合
     */
    @ApiOperation(value = "根据传入的企业或组织数组查询其所有下级", notes = "返回扁平guid集合")
    @PostMapping("/query_all_child_org")
    @LogBefore(value = "根据传入的企业或组织数组查询其所有下级", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据传入的企业或组织数组查询其所有下级", logLevel = LogLevel.DEBUG)
    public List<String> queryAllChildOrg(@ApiParam("企业或组织guid集合") @RequestBody List<String> orgGuidList) {
        return organizationService.queryAllChildOrg(orgGuidList);
    }
}