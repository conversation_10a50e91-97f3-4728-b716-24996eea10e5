package com.holderzone.saas.store.organization.service;

import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeRespDTO;
import com.holderzone.saas.store.dto.table.PadAreaDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceSortDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceUnbindDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className StoreTerminalService
 * @date 19-2-10 下午9:59
 * @description 门店-设备相关接口
 * @program holder-saas-store-organization
 */
public interface StoreDeviceService {

    boolean create(StoreDeviceDTO storeDeviceDTO);

    List<StoreDeviceDTO> findStoreDevice(StoreDeviceQueryDTO storeTerminalQueryDTO);

    boolean sort(List<StoreDeviceSortDTO> storeTerminalSortDTOS);

    boolean unbind(StoreDeviceUnbindDTO storeTerminalUnbindDTO);

    StoreDeviceDTO getMasterDeviceByStoreGuid(String storeGuid);

    StoreDeviceDTO findDeviceStatus(String deviceNo);

    void setMasterDevice(String storeGuid, String deviceGuid);

    /**
     * 查询pad点餐模式
     *
     * @param padOrderTypeReqDTO pad点餐设置点餐模式请求dto
     * @return pad点餐查询点餐模式返回dto
     */
    PadOrderTypeRespDTO queryPadOrderType(PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 查询已绑定的桌台信息
     *
     * @param padOrderTypeReqDTO pad点餐设置点餐模式请求dto
     * @return 返回结果
     */
    TableInfoDTO queryBindingTableInfo(PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 初始化pad点餐设置
     *
     * @param padOrderTypeReqDTO pad点餐设置点餐模式请求dto
     * @return 操作结果
     */
    boolean initializePadOrderTypeSet(PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 查询未绑定的桌台信息
     *
     * @param padOrderTypeReqDTO pad点餐设置点餐模式请求dto
     * @return 查询结果
     */
    List<PadAreaDTO> queryUnBindingTableInfo(PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * pad点餐模式设置
     *
     * @param padOrderTypeReqDTO pad点餐设置点餐模式请求dto
     */
    boolean padOrderTypeSet(PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 查询门店桌台对应设备信息
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guid
     * @return 设备信息
     */
    StoreDeviceDTO queryDeviceByStoreTable(PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 查询门店桌台对应设备信息列表
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guid
     * @return 设备信息列表
     */
    List<StoreDeviceDTO> listDeviceByStoreTable(PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 查询门店桌台所有设备信息列表
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guidList
     * @return 设备信息列表
     */
    List<StoreDeviceDTO> listAllDeviceByStoreTable(PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 查询当前设备绑定的信息
     *
     * @param deviceId 设备Guid
     * @return 设备信息
     */
    StoreDeviceDTO queryDeviceByDeviceId(String deviceId);
}
