package com.holderzone.saas.store.organization.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className SwaggerConfig
 * @date 2018/07/10 下午7:39
 * @description Swagger相关
 * @program holder-saas-store-organization
 */
@SuppressWarnings("Duplicates")
@Configuration
public class SwaggerConfig {

    @Bean
    public Docket testApi() {
        List<Parameter> pars = new ArrayList<>();
        pars.add(new ParameterBuilder().name("userInfo")
                                       .description("登陆用户相关信息（utf-8编码后）")
                                       .modelRef(new ModelRef("string"))
                                       .parameterType("header")
                                       .required(true).build());
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("JAVA")
                .apiInfo(apiInfo())
                .select().apis(RequestHandlerSelectors.basePackage("com.holderzone.saas.store"))
                .paths(PathSelectors.any()).build()
                .globalOperationParameters(pars);
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("组织基础信息-品牌、组织、门店相关")
                .version("1.0")
                .build();
    }
}
