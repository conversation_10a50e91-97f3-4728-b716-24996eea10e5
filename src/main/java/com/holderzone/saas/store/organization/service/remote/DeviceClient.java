package com.holderzone.saas.store.organization.service.remote;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.device.DeviceBindDTO;
import com.holderzone.resource.common.dto.device.DeviceStoreDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className DeviceClient
 * @date 19-2-10 下午4:38
 * @description 服务间调用-设备服务
 * @program holder-saas-store-organization
 */
@Component
@FeignClient(value = "holder-saas-cloud-device", fallbackFactory = DeviceClient.ServiceFallback.class)
public interface DeviceClient {
    @PostMapping("/device/bind")
    void bindDevice(@RequestBody DeviceBindDTO deviceBindDTO);

    @GetMapping("/device/findByNo/{deviceNo}")
    DeviceStoreDTO findDeviceStatusInCloud(@PathVariable("deviceNo") String deviceNo);

    @PostMapping("/device/unbind")
    void unbindDevice(@RequestBody DeviceBindDTO deviceBindDTO);

    @Slf4j
    @Component
    class ServiceFallback implements FallbackFactory<DeviceClient> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public DeviceClient create(Throwable cause) {
            return new DeviceClient() {
                @Override
                public void bindDevice(DeviceBindDTO deviceBindDTO) {
                    log.error(HYSTRIX_PATTERN, "bindDevice", JacksonUtils.writeValueAsString(deviceBindDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("云端绑定设备接口熔断");
                }

                @Override
                public DeviceStoreDTO findDeviceStatusInCloud(String deviceNo) {
                    log.error(HYSTRIX_PATTERN, "findDeviceStatusInCloud", "设备编号为：" + deviceNo, ThrowableUtils.asString(cause));
                    throw new BusinessException("云端根据设备编号查询设备状态接口熔断");
                }

                @Override
                public void unbindDevice(DeviceBindDTO deviceBindDTO) {
                    log.error(HYSTRIX_PATTERN, "deleteDevice", JacksonUtils.writeValueAsString(deviceBindDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("云端解绑设备接口熔断");
                }
            };
        }
    }
}
