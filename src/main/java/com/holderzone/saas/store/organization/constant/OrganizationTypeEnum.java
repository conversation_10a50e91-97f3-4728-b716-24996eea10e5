package com.holderzone.saas.store.organization.constant;


/**
 * 组织机构类型
 */
public enum OrganizationTypeEnum {


    ORGANIZATION(1, "组织"),
    STORE(2, "门店"),
    ALL(3, "既是门店也是组织");

    private Integer type;
    private String desc;

    OrganizationTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
