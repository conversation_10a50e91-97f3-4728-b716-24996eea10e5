package com.holderzone.saas.store.organization.service.remote;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className WeixinClient
 * @date 19-2-19 上午10:51
 * @description
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "holder-saas-store-weixin", fallbackFactory = WeixinClient.ServiceFallBack.class)
public interface WeixinClient {
    @PostMapping("/wx_store_order_config/create_store_config")
    boolean createStoreConfig(@RequestBody WxStoreReqDTO wxStoreReqDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<WeixinClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WeixinClient create(Throwable throwable) {
            return wxStoreReqDTO -> {
                log.error(HYSTRIX_PATTERN, "createStoreConfig", JacksonUtils.writeValueAsString(wxStoreReqDTO), ThrowableUtils.asString(throwable));
                return false;
            };
        }
    }
}
