package com.holderzone.saas.store.organization.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.organization.StoreAttachInfoDTO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.domain.StoreAttachInfoDO;
import com.holderzone.saas.store.organization.mapper.OrganizationMapper;
import com.holderzone.saas.store.organization.mapper.StoreAttachMapper;
import com.holderzone.saas.store.organization.service.StoreAttachInfoService;
import com.holderzone.saas.store.organization.service.StoreService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2020年7月13日
 */
@Service
@Slf4j
@AllArgsConstructor
public class StoreAttachInfoServiceImpl extends ServiceImpl<StoreAttachMapper, StoreAttachInfoDO> implements StoreAttachInfoService {

    private OrganizationMapper organizationMapper;

    private StoreAttachInfoDO getStoreAttachInfoDO(String storeGuid) {
        StoreAttachInfoDO storeAttachInfoDO = getById(storeGuid);
        if (storeAttachInfoDO == null) {
            storeAttachInfoDO = new StoreAttachInfoDO();
            storeAttachInfoDO.setStoreGuid(storeGuid);
            storeAttachInfoDO.setCreateUserGuid(UserContextUtils.getUserGuid());
            save(storeAttachInfoDO);
        }
        return storeAttachInfoDO;
    }
    private OrganizationDO getOrganizationDO(String storeGuid){
        OrganizationDO organizationDO = organizationMapper.queryByGuid(storeGuid);
        if (ObjectUtils.isEmpty(organizationDO)) {
            log.warn("门店不存在,storeGuid: {}", storeGuid);
            return null;
        }
        boolean enable = organizationDO.getIsEnable();
        if (!enable) {
            log.warn("门店已经被禁用,storeGuid: {}", storeGuid);
            return null;
        }
        return organizationDO;
    }

    @Override
    public StoreAttachInfoDTO info(String storeGuid) {
        OrganizationDO organizationDO =getOrganizationDO(storeGuid);
        if (ObjectUtils.isEmpty(organizationDO)) {
            return null;
        }
        StoreAttachInfoDO storeAttachInfoDO = getStoreAttachInfoDO(storeGuid);
        StoreAttachInfoDTO storeAttachInfoDTO = new StoreAttachInfoDTO();
        BeanUtils.copyProperties(storeAttachInfoDO, storeAttachInfoDTO);
        return storeAttachInfoDTO;
    }

    @Override
    public boolean save(StoreAttachInfoDTO dto) {
        if (dto == null ) {
            return false;
        }
        OrganizationDO organizationDO =getOrganizationDO(dto.getStoreGuid());
        if (ObjectUtils.isEmpty(organizationDO)) {
            return false;
        }
        StoreAttachInfoDO entity = getStoreAttachInfoDO(dto.getStoreGuid());
        BeanUtils.copyProperties(dto, entity);
        entity.setModifiedUserGuid(UserContextUtils.getUserGuid());
        return updateById(entity);
    }
}
