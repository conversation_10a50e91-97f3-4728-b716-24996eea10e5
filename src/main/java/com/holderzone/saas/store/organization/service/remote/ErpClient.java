package com.holderzone.saas.store.organization.service.remote;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className EnterpeiseClient
 * @date 19-1-4 下午5:21
 * @description
 * @program holder-saas-store-organization
 */
@Component
@FeignClient(value = "holder-saas-store-erp", fallbackFactory = ErpClient.ServiceFallBack.class)
public interface ErpClient {

    /**
     * 查询organizationCode是否重复
     *
     * @param code organizationCode
     * @return true-不存在，该code可用，false-已存在，该code不可用
     */

    @ApiOperation(value = "创建仓库")
    @PostMapping("/warehouse")
     String createWarehouse(@RequestBody WarehouseReqDTO reqDTO);

    @ApiOperation(value = "生成仓库编号")
    @GetMapping("/warehouse/code")
     String warehouseCode();

    @ApiOperation(value = "更新门店仓库名称")
    @PutMapping("/warehouse/store")
    String updateStoreWarehouse(@RequestBody WarehouseReqDTO reqDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ErpClient> {

        @Override
        public ErpClient create(Throwable cause) {
            return new ErpClient() {
                @Override
                public String createWarehouse(WarehouseReqDTO reqDTO) {
                    log.error("warehouseCode fail{} :",reqDTO ,cause);
                    throw new BusinessException(cause.getMessage());
                }

                @Override
                public String warehouseCode() {
                    log.error("warehouseCode fail :" ,cause);
                    throw new BusinessException(cause.getMessage());
                }

                @Override
                public String updateStoreWarehouse(WarehouseReqDTO reqDTO) {
                    log.error("warehouseCode fail :" ,cause);
                    throw new BusinessException(cause.getMessage());
                }
            };
        }
    }
}
