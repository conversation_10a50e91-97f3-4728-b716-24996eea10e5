package com.holderzone.saas.store.organization.mapstruct;

import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceSortDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceUnbindDTO;
import com.holderzone.saas.store.organization.domain.StoreDeviceDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className StoreTerminalMapStruct
 * @date 19-2-10 上午10:53
 * @description 设备管理MapStruct
 * @program holder-saas-store-organization
 */
@Component
@Mapper(componentModel = "spring")
public interface StoreDeviceMapStruct {

    List<StoreDeviceDTO> toStoreDeviceDTOList(List<StoreDeviceDO> storeDeviceDOList);

    List<StoreDeviceDO> toStoreDeviceDOS(List<StoreDeviceSortDTO> storeDeviceSortDTOS);

    StoreDeviceDO toStoreDeviceDO(StoreDeviceUnbindDTO storeDeviceUnbindDTO);

    @Mappings({
                      @Mapping(target = "binding", source = "isBinding")
              })
    StoreDeviceDTO toStoreDeviceDTO(StoreDeviceDO storeDeviceDO);

    List<StoreDeviceDTO> storeDeviceDOList2StoreDeviceDTOList(List<StoreDeviceDO> storeDeviceDOList);

    StoreDeviceDO toStoreDeviceDO(StoreDeviceDTO storeDeviceDTO);
}
