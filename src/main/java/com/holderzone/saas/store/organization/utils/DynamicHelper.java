package com.holderzone.saas.store.organization.utils;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className DynamicUtils
 * @date 2018/10/25 15:28
 * @description 动态数据源相关
 * @program holder-saas-store-organization
 */
@Component
public class DynamicHelper {

    private static final Logger log = LoggerFactory.getLogger(DynamicHelper.class);

    @Value("${self.open-dynamic-datasource}")
    private Boolean openDynamicDatasource;

    /**
     * 使用redis生成唯一guid
     *
     * @param redisKey key
     * @return 生成的唯一guid（备注：在多实例部署的情况下可能存在生成相同key的问题，推荐使用BatchIdGenerator.getGuid()生成唯一id）
     */
    @Deprecated
    public String generateGuid(String redisKey) {
        /*return String.valueOf(IdGenerator.builder(SpringContextUtils.getInstance().getBean("redisTemplate"),
                                                  5).next(redisKey));*/
        try {
            return String.valueOf(BatchIdGenerator.getGuid(SpringContextUtils.getInstance().getBean("redisTemplate"), redisKey));
        } catch (IOException e) {
            log.error("BatchIdGenerator类中生成guid失败");
            return null;
        }
    }

    public void changeDatasource(String enterpriseGuid) {
        if (openDynamicDatasource) {
            try {
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            } catch (Exception e) {
                log.error("手动切换数据源异常！enterpriseGuid:" + enterpriseGuid + ":{}", e.getMessage());
            }
        }
    }
}
