package com.holderzone.saas.store.organization.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.base.dto.message.*;
import com.holderzone.saas.store.organization.service.PushService;
import com.holderzone.saas.store.organization.service.remote.MessageClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PushServiceImpl implements PushService {

    public static final String MESSAGE_TOPIC = "master_device";

    @Autowired
    private MessageClient messageClient;

    @Override
    public void pushMasterChanged(String storeGuid, String deviceGuid) {
        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        pushMessageDTO.setTopicType(TopicType.BUSINESS);
        BusinessMessage businessMessage = new BusinessMessage();
        businessMessage.setBusinessType(MESSAGE_TOPIC);
        businessMessage.setStoreGuid(storeGuid);
        businessMessage.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        pushMessageDTO.setBusinessMessage(businessMessage);
        pushMessageDTO.setData(deviceGuid == null ? "" : deviceGuid);
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessageType(MessageType.PUSH);
        messageDTO.setPushMessage(pushMessageDTO);
        messageClient.sendPrintMessage(messageDTO);
    }
}
