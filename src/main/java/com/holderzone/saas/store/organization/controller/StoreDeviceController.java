package com.holderzone.saas.store.organization.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.slf4j.starter.anno.LogAfter;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeRespDTO;
import com.holderzone.saas.store.dto.table.PadAreaDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceSortDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceUnbindDTO;
import com.holderzone.saas.store.organization.service.StoreDeviceService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className StoreDeviceController
 * @date 19-2-10 下午5:20
 * @description 设备管理Controller
 * @program holder-saas-store-organization
 */
@Slf4j
@RestController
@RequestMapping("/device")
@Api("设备管理接口")
public class StoreDeviceController {

    private final StoreDeviceService storeDeviceService;

    @Autowired
    public StoreDeviceController(StoreDeviceService storeDeviceService) {
        this.storeDeviceService = storeDeviceService;
    }

    @ApiOperation(value = "建立设备门店绑定关系")
    @PostMapping(value = "/bind")
    @LogBefore(value = "建立设备门店绑定关系", logLevel = LogLevel.INFO)
    @LogAfter(value = "建立设备门店绑定关系", logLevel = LogLevel.DEBUG)
    public boolean create(@RequestBody StoreDeviceDTO storeDeviceDTO) {
        log.info("建立设备门店绑定关系接口入参：{}", JacksonUtils.writeValueAsString(storeDeviceDTO));
        return storeDeviceService.create(storeDeviceDTO);
    }

    @ApiOperation(value = "根据门店guid或设备类型查询设备列表")
    @PostMapping(value = "/find_store_device")
    @LogBefore(value = "根据门店guid或设备类型查询设备列表", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据门店guid或设备类型查询设备列表", logLevel = LogLevel.DEBUG)
    public List<StoreDeviceDTO> findStoreDevice(@RequestBody @Validated StoreDeviceQueryDTO storeDeviceQueryDTO) {
        log.info("查询门店设备请求入参：{}", JacksonUtils.writeValueAsString(storeDeviceQueryDTO));
        return storeDeviceService.findStoreDevice(storeDeviceQueryDTO);
    }

    @ApiOperation(value = "设备排序")
    @PostMapping(value = "/sort")
    @LogBefore(value = "设备排序", logLevel = LogLevel.INFO)
    @LogAfter(value = "设备排序", logLevel = LogLevel.DEBUG)
    public boolean sort(@RequestBody @Validated List<StoreDeviceSortDTO> storeDeviceSortDTOS) {
        log.info("设备排序接口入参：{}", JacksonUtils.writeValueAsString(storeDeviceSortDTOS));
        return storeDeviceService.sort(storeDeviceSortDTOS);
    }

    @ApiOperation(value = "设备解绑")
    @PostMapping(value = "/unbind")
    @LogBefore(value = "设备解绑", logLevel = LogLevel.INFO)
    @LogAfter(value = "设备解绑", logLevel = LogLevel.DEBUG)
    public boolean unbind(@RequestBody @Validated StoreDeviceUnbindDTO storeDeviceUnbindDTO) {
        log.info("设备解绑接口入参：{}", JacksonUtils.writeValueAsString(storeDeviceUnbindDTO));
        return storeDeviceService.unbind(storeDeviceUnbindDTO);
    }

    @ApiOperation(value = "设置主机")
    @PostMapping("/set_master/{storeGuid}/{deviceGuid}")
    @LogBefore(value = "设置主机", logLevel = LogLevel.INFO)
    @LogAfter(value = "设置主机", logLevel = LogLevel.DEBUG)
    public void setMasterDevice(@PathVariable("storeGuid") String storeGuid, @PathVariable("deviceGuid") String deviceGuid) {
        storeDeviceService.setMasterDevice(storeGuid, deviceGuid);
    }

    @ApiOperation(value = "根据门店guid查询该门店下的master一体机")
    @GetMapping("/get_master_device_by_storeguid/{storeGuid}")
    @LogBefore(value = "根据门店guid查询该门店下的master一体机", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据门店guid查询该门店下的master一体机", logLevel = LogLevel.DEBUG)
    public StoreDeviceDTO getMasterDeviceByStoreGuid(@PathVariable("storeGuid") String storeGuid) {
        return storeDeviceService.getMasterDeviceByStoreGuid(storeGuid);
    }

    @ApiOperation(value = "根据门店guid查询该门店下的master一体机")
    @GetMapping("/get_master_device/{enterpriseGuid}/{storeGuid}")
    public StoreDeviceDTO getMasterDeviceByStoreGuid(@PathVariable("enterpriseGuid") String enterpriseGuid,
                                                     @PathVariable("storeGuid") String storeGuid) {
        UserContextUtils.putErp(enterpriseGuid);
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        return storeDeviceService.getMasterDeviceByStoreGuid(storeGuid);
    }

    @ApiOperation(value = "根据设备编号查询设备注册、绑定信息", notes = "分为未注册、已注册未绑定、已注册已绑定三种情况")
    @GetMapping("find_device_status/{deviceNo}")
    @LogBefore(value = "根据设备编号查询设备注册、绑定信息", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据设备编号查询设备注册、绑定信息", logLevel = LogLevel.DEBUG)
    public StoreDeviceDTO findDeviceStatus(@ApiParam(value = "厂商设备编号") @PathVariable("deviceNo") String deviceNo) {
        log.info("查询设备状态请求入参：{}", deviceNo);
        return storeDeviceService.findDeviceStatus(deviceNo);
    }


    @ApiOperation(value = "查询pad点餐模式")
    @PostMapping(value = "/query_pad_order_type")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    public PadOrderTypeRespDTO queryPadOrderType(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("查询pad点餐模式入参：{}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return storeDeviceService.queryPadOrderType(padOrderTypeReqDTO);
    }

    @ApiOperation(value = "查询已绑定的桌台信息")
    @PostMapping(value = "/query_binding_table_info")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    public TableInfoDTO queryBindingTableInfo(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("查询已绑定的桌台信息入参：{}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return storeDeviceService.queryBindingTableInfo(padOrderTypeReqDTO);
    }

    @ApiOperation(value = "查询未绑定的桌台信息")
    @PostMapping(value = "/query_un_binding_table_info")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    public List<PadAreaDTO> queryUnBindingTableInfo(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("查询未绑定的桌台信息入参：{}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return storeDeviceService.queryUnBindingTableInfo(padOrderTypeReqDTO);
    }

    @ApiOperation(value = "初始化pad点餐设置")
    @PostMapping(value = "/initialize_pad_order_type_set")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    public boolean initializePadOrderTypeSet(@RequestBody @Validated PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("设备解绑接口入参：{}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return storeDeviceService.initializePadOrderTypeSet(padOrderTypeReqDTO);
    }

    @ApiOperation(value = "pad点餐模式设置")
    @PostMapping(value = "/pad_order_type_set")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    public boolean padOrderTypeSet(@RequestBody @Validated PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("pad点餐模式设置 入参={}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return storeDeviceService.padOrderTypeSet(padOrderTypeReqDTO);
    }

    /**
     * 查询门店桌台对应设备信息
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guid
     * @return 设备信息
     */
    @ApiOperation(value = "查询门店桌台对应设备信息")
    @PostMapping(value = "/query_device_by_store_table")
    public StoreDeviceDTO queryDeviceByStoreTable(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("查询门店桌台对应设备信息 padOrderTypeReqDTO={}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return storeDeviceService.queryDeviceByStoreTable(padOrderTypeReqDTO);
    }

    /**
     * 查询门店桌台对应设备信息列表
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guidList
     * @return 设备信息列表
     */
    @ApiOperation(value = "查询门店桌台对应设备信息列表")
    @PostMapping(value = "/list_device_by_store_table")
    public List<StoreDeviceDTO> listDeviceByStoreTable(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("查询门店桌台对应设备信息 padOrderTypeReqDTO={}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return storeDeviceService.listDeviceByStoreTable(padOrderTypeReqDTO);
    }

    /**
     * 查询门店桌台所有设备信息列表
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guidList
     * @return 设备信息列表
     */
    @ApiOperation(value = "查询门店桌台所有设备信息列表")
    @PostMapping(value = "/list_all_device_by_store_table")
    public List<StoreDeviceDTO> listAllDeviceByStoreTable(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("查询门店桌台所有设备信息列表 padOrderTypeReqDTO={}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return storeDeviceService.listAllDeviceByStoreTable(padOrderTypeReqDTO);
    }

    /**
     * 查询当前设备绑定的信息
     *
     * @param deviceId 设备Guid
     * @return 设备信息
     */
    @ApiOperation(value = "查询当前设备绑定的信息")
    @GetMapping(value = "/query_device_by_deviceId")
    public StoreDeviceDTO queryDeviceByDeviceId(@RequestParam("deviceId") String deviceId) {
        log.info("查询当前设备绑定的信息 deviceId={}", deviceId);
        return storeDeviceService.queryDeviceByDeviceId(deviceId);
    }
}