package com.holderzone.saas.store.organization.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.slf4j.starter.anno.LogAfter;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.QueryBrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.organization.service.BrandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className BrandController
 * @date 19-1-2 下午5:15
 * @description Controller-品牌相关
 * @program holder-saas-store-organization
 */
@Api(description = "品牌相关接口")
@RestController
@RequestMapping("/brand")
public class BrandController {

    private final BrandService brandService;

    @Autowired
    public BrandController(BrandService brandService) {
        this.brandService = brandService;
    }

    /**
     * 创建品牌
     *
     * @param brandDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("创建品牌")
    @PostMapping("/create")
    @LogBefore(value = "创建品牌", logLevel = LogLevel.INFO)
    @LogAfter(value = "创建品牌", logLevel = LogLevel.DEBUG)
    public BrandDTO createBrand(@RequestBody @Validated BrandDTO brandDTO) {
        return brandService.createBrand(brandDTO);
    }

    /**
     * 更新品牌
     *
     * @param brandDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("更新品牌")
    @PostMapping("/update")
    @LogBefore(value = "更新品牌", logLevel = LogLevel.INFO)
    @LogAfter(value = "更新品牌", logLevel = LogLevel.DEBUG)
    public boolean updateBrand(@RequestBody @Validated(BrandDTO.Update.class) BrandDTO brandDTO) {
        return brandService.updateBrand(brandDTO);
    }


    /**
     * 删除品牌
     *
     * @param brandGuid 品牌guid
     * @return true-成功，false-失败
     */
    @ApiOperation("删除品牌")
    @PostMapping("/delete")
    @LogBefore(value = "删除品牌", logLevel = LogLevel.INFO)
    @LogAfter(value = "删除品牌", logLevel = LogLevel.DEBUG)
    public boolean deleteBrand(@RequestParam("brandGuid") String brandGuid) {
        return brandService.deleteBrand(brandGuid);
    }

    /**
     * 根据品牌guid查询品牌信息
     *
     * @param brandGuid 品牌guid
     * @return 品牌信息
     */
    @ApiOperation("根据品牌guid查询品牌信息")
    @PostMapping("/query_brand_by_guid")
    @LogBefore(value = "根据品牌guid查询品牌信息", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据品牌guid查询品牌信息", logLevel = LogLevel.DEBUG)
    public BrandDTO queryBrandByGuid(@RequestParam("brandGuid") String brandGuid) {
        return brandService.queryBrandByGuid(brandGuid);
    }

    /**
     * 根据传入的品牌guid集合查询品牌集合（只包含guid与name两列）
     *
     * @param guidList 品牌guid集合
     * @return 品牌集合（只包含guid与name两列）
     */
    @ApiOperation("根据传入的品牌guid集合查询品牌")
    @PostMapping("/query_brand_by_idlist")
    @LogBefore(value = "根据传入的品牌guid集合查询品牌", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据传入的品牌guid集合查询品牌", logLevel = LogLevel.DEBUG)
    public List<BrandDTO> queryBrandByIdList(@RequestBody List<String> guidList) {
        return brandService.queryBrandByIdList(guidList);
    }

    /**
     * 查询企业下所有品牌
     *
     * @return 品牌列表
     */
    @ApiOperation("查询企业下所有品牌")
    @PostMapping("/query_list")
    @LogBefore(value = "查询企业下所有品牌", logLevel = LogLevel.INFO)
    @LogAfter(value = "查询企业下所有品牌", logLevel = LogLevel.DEBUG)
    public List<BrandDTO> queryList(@RequestBody(required = false) QueryBrandDTO queryBrandDTO) {
        // 赚餐调用切库
        if (!ObjectUtils.isEmpty(queryBrandDTO) && !ObjectUtils.isEmpty(queryBrandDTO.getEnterpriseGuid())) {
            UserContextUtils.putErp(queryBrandDTO.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(queryBrandDTO.getEnterpriseGuid());
        }
        return brandService.queryAllList(queryBrandDTO);
    }

    /**
     * 判断品牌下是否存在门店、商品、商品分类
     *
     * @param brandGuid 品牌guid
     * @return true-存在，false-不存在
     */
    @ApiOperation("根据品牌guid判断品牌下是否存在门店、商品、商品分类，true-存在，false-不存在")
    @PostMapping("/query_exist_store_account/{brandGuid}")
    @LogBefore(value = "根据品牌guid判断品牌下是否存在门店、商品、商品分类", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据品牌guid判断品牌下是否存在门店、商品、商品分类", logLevel = LogLevel.DEBUG)
    public boolean queryExistStoreAccount(@PathVariable("brandGuid") String brandGuid) {
        return brandService.queryExistStoreAccount(brandGuid);
    }

    /**
     * 根据品牌guid查询品牌下所有门店Guid
     *
     * @param brandGuid 品牌guid
     * @return 品牌下所有门店
     */
    @ApiOperation("根据品牌guid查询品牌下所有门店Guid")
    @PostMapping("/query_store_guid_list_by_brand_guid")
    @LogBefore(value = "根据品牌guid查询门店列表", logLevel = LogLevel.INFO)
    @LogAfter(value = "根据品牌guid查询门店列表", logLevel = LogLevel.DEBUG)
    public List<String> queryStoreGuidListByBrandGui(@RequestBody String brandGuid) {
        return brandService.queryStoreGuidListByBrandGuid(brandGuid);
    }

    /**
     * 根据品牌guid查询品牌下所有门店
     *
     * @param reqDTO 品牌guid
     * @return 品牌下所有门店
     */
    @ApiOperation("根据品牌guid查询品牌下所有门店")
    @PostMapping("/query_store_list_by_brand_guid")
    public List<StoreDTO> queryStoreByBrandGuid(@RequestBody SingleDataDTO reqDTO) {
        return brandService.queryStoreByBrandGuid(reqDTO);
    }

    /**
     * 更改销售模式
     *
     * @param brandDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("更改销售模式")
    @PostMapping("/update_sales_model")
    public Boolean updateSalesModel(@RequestBody BrandDTO brandDTO) {
        return brandService.updateSalesModel(brandDTO);
    }

    /**
     * 查询默认品牌
     */
    @ApiOperation("查询默认品牌")
    @GetMapping("/query_default_brandGuid")
    public BrandDTO queryDefaultBrand() {
        return brandService.queryDefaultBrand();
    }
}
