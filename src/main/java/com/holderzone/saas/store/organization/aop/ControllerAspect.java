package com.holderzone.saas.store.organization.aop;

import com.holderzone.framework.util.JacksonUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className ControllerAspect
 * @date 2018/10/25 15:28
 * @description Aop
 * @program holder-saas-store-organization
 */
@SuppressWarnings("Duplicates")
// @Aspect
// @Component
public class ControllerAspect {

    private static final Logger logger = LoggerFactory.getLogger(ControllerAspect.class);

    @Pointcut("execution(public * com.holderzone.saas.store.organization.controller.*.*(..))")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void doBefore(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        Signature signature = joinPoint.getSignature();
        if (args != null && args.length > 0) {
            // 系统日志
            String[] names = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < names.length; i++) {
                if (args[i] instanceof CharSequence) {
                    stringBuilder.append(String.format("%s: %s; ", names[i], args[i]));
                } else if (!(args[i] instanceof HttpServletRequest
                        || args[i] instanceof HttpServletResponse
                        || args[i] instanceof MultipartFile
                        || args[i] instanceof MultipartFile[])) {
                    stringBuilder.append(String.format("%s: %s; ", names[i], JacksonUtils.writeValueAsString(args[i])));
                }
            }
            logger.info("{}#{}方法入参：{}", signature.getDeclaringType().getSimpleName(), signature.getName(), stringBuilder);
        } else {
            // 系统日志
            if (logger.isInfoEnabled()) {
                logger.info("{}#{}方法入参：无", signature.getDeclaringType().getSimpleName(), signature.getName());
            }
        }
    }
}
