package com.holderzone.saas.store.organization.service;

import com.holderzone.resource.common.dto.device.DeviceDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;

public interface BroadcastService {

    void storeCreated(StoreDTO storeDTO);

    void storeUpdated(StoreDTO storeDTO);

    void createStoreInCloud(OrganizationDO organizationDO);

    void updateStoreInCloud(OrganizationDO organizationDO);

    void enableStoreInCloud(OrganizationDO organizationDO);

    void deleteStoreInCloud(String storeGuid);

    void createWareHouse(OrganizationDO organizationDO);

    void updateWareHouse(StoreDTO storeDTO);

    void createMemberStore(StoreDTO storeDTO);

    void updateMemberStore(StoreDTO storeDTO);

    void deleteMemberStore(String storeGuid);

    void deviceBind(StoreDeviceDTO storeDeviceDTO);

    void deviceUnbind(StoreDeviceDTO storeDeviceDTO);

    void unBindDeviceToCloud(DeviceDTO deviceDTO);

    void bindDeviceToCloud(DeviceDTO deviceDTO);
}
