package com.holderzone.saas.store.organization.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.organization.BrandStoreDetailDTO;
import com.holderzone.saas.store.dto.organization.QueryBrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.organization.domain.StoreBrandDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 门店-品牌关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-07
 */
@Mapper
@Component
public interface StoreBrandMapper extends BaseMapper<StoreBrandDO> {

    void save(StoreBrandDO storeBrandDO);

    BrandStoreDetailDTO getStoreBrandDetail(@Param("storeGuid") String storeGuid, @Param("brandGuid") String brandGuid);

    /**
     * 查询门店信息
     */
    List<StoreDTO> queryStore(@Param("req") QueryBrandDTO queryBrandDTO);

}
