package com.holderzone.saas.store.organization.service.remote;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.PricePlanBingStoreRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className TableClient
 * @date 19-1-3 下午5:57
 * @description
 * @program holder-saas-store-organization
 */
@Component
@FeignClient(value = "holder-saas-store-item", fallbackFactory = ItemClient.ServiceFallBack.class)
public interface ItemClient {

    /**
     * 根据门店guid查询方案绑定门店
     *
     * @param brandGuid  brandGuid
     * @param salesModel salesModel
     * @return 方案与门店绑定关系返回对象列表
     */
    @GetMapping("/plan/changeSaleModel")
    void changeSaleModel(@RequestParam("brandGuid") String brandGuid, @RequestParam("salesModel") Integer salesModel);

    /**
     * 查询当前品牌是否有绑定的商品分类或商品
     *
     * @param brandGuid 当前品牌GUID
     * @return true-品牌下有商品分类或商品；false-品牌下无商品分类或商品
     */
    @PostMapping("/item/count_type_or_item")
    boolean countTypeOrItem(@RequestParam("brandGuid") String brandGuid);

    @PostMapping("/default_data/add_data")
    void addDefaultAttr(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 根据门店guid查询方案绑定门店
     *
     * @param storeGuid 门店guid
     * @return 方案与门店绑定关系返回对象列表
     */
    @GetMapping("/plan/get_plan_store_by_store_guid")
    List<PricePlanBingStoreRespDTO> getPlanStoreByStoreGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 商品估清定时恢复
     *
     * @param request 企业和门店map
     * @return boolean
     */
    @ApiOperation(value = "商品估清定时恢复", notes = "job门店估清定时恢复")
    @PostMapping("/estimate/store_item_estimate_cancel")
    Boolean storeItemEstimateCancel(@RequestBody Map<String, List<String>> request);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ItemClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ItemClient create(Throwable cause) {
            return new ItemClient() {
                @Override
                public void changeSaleModel(String brandGuid, Integer salesModel) {
                    log.error(HYSTRIX_PATTERN, "changeSaleModel", "入参brandGuid为：" + brandGuid + "," + salesModel,
                            ThrowableUtils.asString(cause));
                }

                @Override
                public boolean countTypeOrItem(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "countTypeOrItem", "入参brandGuid为：" + brandGuid, ThrowableUtils.asString(cause));
                    return true;
                }

                @Override
                public void addDefaultAttr(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "addDefaultAttr", "itemSingleDTO：" + JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(cause));
                }

                @Override
                public List<PricePlanBingStoreRespDTO> getPlanStoreByStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getPlanStoreByStoreGuid", "入参storeGuid："
                            + storeGuid, ThrowableUtils.asString(cause));
                    throw new RuntimeException("根据门店guid查询方案绑定门店" + cause.getMessage());
                }

                @Override
                public Boolean storeItemEstimateCancel(Map<String, List<String>> request) {
                    log.error(HYSTRIX_PATTERN, "storeItemEstimateCancel", request, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
