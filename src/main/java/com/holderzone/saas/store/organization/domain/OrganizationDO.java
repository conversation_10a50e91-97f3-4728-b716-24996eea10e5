package com.holderzone.saas.store.organization.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 组织表
 *
 * <AUTHOR>
 * @since 2019-01-04
 */
@Data
@Accessors(chain = true)
@TableName(value = "hso_organization")
public class OrganizationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 门店编码
     */
    private String code;

    /**
     * 类型（1-组织，2-门店）
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String contactTel;

    /**
     * 上级组织id（由最上级组织到直属上级的guid组成，逗号隔开）
     */
    private String parentIds;

    /**
     * 省份code
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市code
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cityCode;

    /**
     * 城市名称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cityName;

    /**
     * 区县code
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String countyCode;

    /**
     * 区县名称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String countyName;

    /**
     * 详细地址
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String addressDetail;

    /**
     * 描述
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String description;

    /**
     * 品牌Logo（oss下载地址）
     */
    private String icon;

    /**
     * 是否启用（默认为1-启用）
     */
    private Boolean isEnable;

    /**
     * 是否删除（默认为0-未删除）
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 门店营业开始时间
     */
    private LocalTime businessStart;

    /**
     * 门店营业结束时间
     */
    private LocalTime businessEnd;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 修改人guid
     */
    private String modifiedUserGuid;

    /**
     * 相册集
     */
    private String photos;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
    /**
     * 开放平台增加字段
     */
    @ApiModelProperty("LOGO图片链接")
    private String logoUrl;
    @ApiModelProperty("线上营业时间")
    private String openingHours;
    @ApiModelProperty("是否营业 0否 1是")
    private Integer isOpening;
    @ApiModelProperty("店铺评分")
    private Integer storeGrade;
    @ApiModelProperty("门店近30天线上销量")
    private Integer storeOnlineSales30;
    @ApiModelProperty("是否上传商品")
    private Integer isItemUpload;
    @ApiModelProperty("门店通知")
    private String storeNotification;
    @ApiModelProperty("门头照")
    private String storeDoorPhoto;
    @ApiModelProperty("是否支持满减")
    private Integer isFullReduce;
    @ApiModelProperty("是否支持满折")
    private Integer isFullDiscount;
    @ApiModelProperty("是否支持自提")
    private Integer isSelfTakeaway;
    @ApiModelProperty("是否支持外卖")
    private Integer isTakeaway;
    @ApiModelProperty("是否支持分销")
    private Integer isSupportDistribution;
    @ApiModelProperty("是否支持预定")
    private Integer isSupportOrder;
    @ApiModelProperty(value = "资质审核状态(默认-1 未上传)")
    private Integer passStatus;
    @ApiModelProperty(value = "是否上线 0 否 1 是")
    private Integer isOnline;

    @ApiModelProperty(value = "门店可否自建菜品 0 否 1 是")
    private Integer isSelfBuildItems;

    @ApiModelProperty(value = "门店是强制扎帐 0 否 1 是")
    private Integer isBuAccounts;
    @ApiModelProperty(value = "门店上缴现金是否显示 0 否 1 是")
    private Integer isShowCash;
    @ApiModelProperty(value = "门店当前能否开台 0 否 1 是")
    private Integer canOpenTable;
    @ApiModelProperty(value = "多人交接班 0 否 1是")
    private Integer isMultiHandover;

}
