package com.holderzone.saas.store.organization.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 门店附加信息表
 */
@Data
@Accessors(chain = true)
@TableName(value = "hss_store_attach_info")
public class StoreAttachInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(type=IdType.INPUT)
    @ApiModelProperty("门店guid")
    private String storeGuid;
    @ApiModelProperty("聚合支付账号")
    private String uatAppId;
    @ApiModelProperty("聚合支付密钥")
    private String uatSecret;
    @ApiModelProperty("修改人guid")
    private String modifiedUserGuid;
    @ApiModelProperty("创建人guid")
    private String createUserGuid;
}
