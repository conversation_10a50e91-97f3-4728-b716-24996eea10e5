package com.holderzone.saas.store.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.constant.member.MemberSyncConstant;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.QueryBrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.organization.constant.OrganizationTypeEnum;
import com.holderzone.saas.store.organization.domain.BrandDO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.domain.StoreBrandDO;
import com.holderzone.saas.store.organization.mapper.BrandMapper;
import com.holderzone.saas.store.organization.mapper.OrganizationMapper;
import com.holderzone.saas.store.organization.mapper.StoreBrandMapper;
import com.holderzone.saas.store.organization.mapstruct.BrandMapstruct;
import com.holderzone.saas.store.organization.mapstruct.StoreMapstruct;
import com.holderzone.saas.store.organization.mq.MqConstant;
import com.holderzone.saas.store.organization.service.BrandService;
import com.holderzone.saas.store.organization.service.DistributedIdService;
import com.holderzone.saas.store.organization.service.remote.ItemClient;
import com.holderzone.saas.store.organization.service.remote.UserClient;
import com.holderzone.saas.store.organization.utils.DynamicHelper;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 品牌表 服务实现类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@SuppressWarnings("unchecked")
@Slf4j
@Service
@AllArgsConstructor
public class BrandServiceImpl extends ServiceImpl<BrandMapper, BrandDO> implements BrandService {

    private final BrandMapper brandMapper;

    private final StoreBrandMapper storeBrandMapper;

    private final BrandMapstruct brandMapstruct;

    private final RedisTemplate<String, String> redisTemplate;

    private final ItemClient itemClient;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final DistributedIdService distributedIdService;

    private final UserClient userClient;

    private final OrganizationMapper organizationMapper;

    private final StoreMapstruct storeMapstruct;

    private final DynamicHelper dynamicHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BrandDTO createBrand(BrandDTO brandDTO) {
        if (this.validateBrandName(brandDTO.getName())) {
            throw new BusinessException("品牌名称重复");
        }

        String guid;
        try {
            guid = String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "brand"));
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成品牌guid失败");
        }
        BrandDO brandDO = brandMapstruct.brandDTO2DO(brandDTO)
                .setGuid(guid)
                .setCreateUserGuid(UserContextUtils.getUserGuid())
                .setModifiedUserGuid(UserContextUtils.getUserGuid())
                .setGmtCreate(DateTimeUtils.now())
                .setGmtModified(DateTimeUtils.now());

        // 初始化品牌下的商品信息
//        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
//        itemSingleDTO.setData(guid);
        //itemClient.addDefaultAttr(itemSingleDTO);

        if (!this.save(brandDO)) {
            return null;
        }

        BrandDTO brandDTO1 = brandMapstruct.brandDO2DTO(this.getOne(
                new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getGuid, guid)));
        brandDTO1.setMchntTypeCode(userClient.queryMchntType());

        // 给下游：品牌创建初始化菜品数据等
        Message message = new Message(MqConstant.DOWNSTREAM_BRAND_CREATE_TOPIC
                , MqConstant.DOWNSTREAM_BRAND_CREATE_TAG, JacksonUtils.toJsonByte(brandDTO1));
        message.getProperties().put(MqConstant.DOWNSTREAM_CONTEXT, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);
        // 创建品牌时关联门店
        if (CollectionUtils.isEmpty(brandDTO.getStoreList())) {
            return brandDTO1;
        }
        List<String> guidList = brandDTO.getStoreList().stream()
                .map(StoreDTO::getGuid)
                .collect(Collectors.toList());
        List<StoreDTO> storeDTOList = organizationMapper.queryStoreDetail(guidList);
        if (!CollectionUtils.isEmpty(brandDTO.getStoreList())) {
            storeDTOList.forEach(
                    storeDTO -> {
                        storeBrandMapper.delete(new LambdaQueryWrapper<StoreBrandDO>()
                                .eq(StoreBrandDO::getStoreGuid, storeDTO.getGuid()));

                        StoreBrandDO storeBrandDO = new StoreBrandDO()
                                .setGuid(distributedIdService.nextStoreBrandGuid())
                                .setStoreGuid(storeDTO.getGuid())
                                .setBrandGuid(guid)
                                .setCreateUserGuid(storeDTO.getCreateUserGuid())
                                .setModifiedUserGuid(storeDTO.getModifiedUserGuid())
                                .setGmtCreate(DateTimeUtils.now())
                                .setGmtModified(DateTimeUtils.now());
                        storeBrandMapper.insert(storeBrandDO);
                    }
            );
        }

        return brandDTO1;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBrand(BrandDTO brandDTO) {
        BrandDO brandDO = brandMapstruct.brandDTO2DO(brandDTO)
                .setModifiedUserGuid(UserContextUtils.getUserGuid())
                .setGmtModified(DateTimeUtils.now());

        BrandDO originalDO = this.getOne(new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getGuid, brandDO.getGuid()));
        // 若更新了品牌名称字段，则验证同企业下是否重复
        if (!originalDO.getName().equals(brandDO.getName()) && this.validateBrandName(brandDO.getName())) {
            throw new BusinessException("品牌名称重复");
        }
        if (!this.update(brandDO, new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getGuid, brandDO.getGuid()))) {
            return false;
        }
        List<String> storeGuidList = storeBrandMapper.selectList(new LambdaQueryWrapper<StoreBrandDO>()
                        .eq(StoreBrandDO::getBrandGuid, brandDO.getGuid())).stream().map(StoreBrandDO::getStoreGuid)
                .collect(Collectors.toList());
        if (!StringUtils.isEmpty(brandDO.getLogoUrl()) && !CollectionUtils.isEmpty(storeGuidList)) {
            organizationMapper.updateStoreLogoUrl(brandDO.getLogoUrl(), storeGuidList);
        }

        // 更新品牌时关联门店
        if (!CollectionUtils.isEmpty(brandDTO.getStoreList())) {
            List<String> guidList = brandDTO.getStoreList().stream()
                    .map(StoreDTO::getGuid)
                    .collect(Collectors.toList());
            List<StoreDTO> storeDTOList = organizationMapper.queryStoreDetail(guidList);
            storeBrandMapper.delete(new LambdaQueryWrapper<StoreBrandDO>()
                    .eq(StoreBrandDO::getBrandGuid, brandDO.getGuid()));
            storeDTOList.forEach(
                    storeDTO -> {
                        storeBrandMapper.delete(new LambdaQueryWrapper<StoreBrandDO>()
                                .eq(StoreBrandDO::getStoreGuid, storeDTO.getGuid()));

                        StoreBrandDO storeBrandDO = new StoreBrandDO()
                                .setGuid(distributedIdService.nextStoreBrandGuid())
                                .setStoreGuid(storeDTO.getGuid())
                                .setBrandGuid(brandDO.getGuid())
                                .setCreateUserGuid(storeDTO.getCreateUserGuid())
                                .setModifiedUserGuid(storeDTO.getModifiedUserGuid())
                                .setGmtCreate(DateTimeUtils.now())
                                .setGmtModified(DateTimeUtils.now());
                        storeBrandMapper.insert(storeBrandDO);

                        storeDTO.setIsBuAccounts(originalDO.getIsBuAccounts());
                        storeDTO.setIsShowCash(originalDO.getIsShowCash());
                        //去更新门店信息
                        OrganizationDO storeDO = storeMapstruct.storeDTO2DTO(storeDTO)
                                .setModifiedUserGuid(UserContextUtils.getUserGuid())
                                .setGmtModified(DateTimeUtils.now());
                        organizationMapper.update(storeDO, new LambdaQueryWrapper<OrganizationDO>()
                                .eq(OrganizationDO::getGuid, storeDO.getGuid()));
                    }
            );
        }
        return true;
    }

    @Override
    public boolean deleteBrand(String brandGuid) {
        return !this.queryExistStoreAccount(brandGuid) &&
                this.remove(new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getGuid, brandGuid));
    }

    @Override
    public BrandDTO queryBrandByGuid(String brandGuid) {
        BrandDO brandDO = this.getOne(new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getGuid, brandGuid));
        if (null == brandDO) return null;
        return brandMapstruct.brandDO2DTO(brandDO);
    }

    @Override
    public List<BrandDTO> queryBrandByIdList(List<String> guidList) {
        if (CollectionUtils.isEmpty(guidList)) {
            return Collections.emptyList();
        }
        List<BrandDO> list = this.list(new LambdaQueryWrapper<BrandDO>()
//                .select(BrandDO::getGuid, BrandDO::getName)
                .in(BrandDO::getGuid, guidList));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return brandMapstruct.brandDOList2DTOList(list);
    }

    @Override
    public List<BrandDTO> queryAllList(QueryBrandDTO queryBrandDTO) {
        List<BrandDO> brandList;
        if (!ObjectUtils.isEmpty(queryBrandDTO) && StringUtils.hasText(queryBrandDTO.getBrandName())) {
            brandList = this.list(new LambdaQueryWrapper<BrandDO>()
                    .like(BrandDO::getName, queryBrandDTO.getBrandName()));
        } else {
            brandList = list();
        }
        List<BrandDTO> brandDTOList = brandMapstruct.brandDOList2DTOList(brandList);
        for (BrandDTO brandDTO : brandDTOList) {
            List<StoreBrandDO> storeBrandDOList = storeBrandMapper.selectList(new LambdaQueryWrapper<StoreBrandDO>()
                    .eq(StoreBrandDO::getBrandGuid, brandDTO.getGuid()));
            List<String> storeGuidList = storeBrandDOList.stream()
                    .map(StoreBrandDO::getStoreGuid)
                    .collect(Collectors.toList());
            List<OrganizationDO> organizationDOList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(storeGuidList)) {
                organizationDOList = organizationMapper.selectList(
                        new LambdaQueryWrapper<OrganizationDO>()
                                .in(OrganizationDO::getType, OrganizationTypeEnum.STORE.getType(), OrganizationTypeEnum.ALL.getType())
                                .in(OrganizationDO::getGuid, storeGuidList)
                );
            }
            List<StoreDTO> storeDTOList = storeMapstruct.organizationList2DTOList(organizationDOList);
            brandDTO.setStoreList(storeDTOList);
        }
        return brandDTOList;
    }

    @Override
    public boolean queryExistStoreAccount(String brandGuid) {
        Wrapper<StoreBrandDO> wrapper = new LambdaQueryWrapper<StoreBrandDO>().eq(StoreBrandDO::getBrandGuid, brandGuid);
        if (storeBrandMapper.selectCount(wrapper) > 0) {
            throw new BusinessException("该品牌下已存在门店，请清理后删除");
        }
        if (itemClient.countTypeOrItem(brandGuid)) {
            throw new BusinessException("该品牌下已存在商品，请清理后删除");
        }
        return false;
    }

    @Override
    public List<String> queryStoreGuidListByBrandGuid(String brandGuid) {
        log.info("获取门店guid列表请求参数：{}", brandGuid);
        List<String> list = null;
        List<StoreBrandDO> storeBrandDOS = storeBrandMapper.selectList(new LambdaQueryWrapper<StoreBrandDO>()
                .eq(StoreBrandDO::getBrandGuid, brandGuid));
        if (!ObjectUtils.isEmpty(storeBrandDOS))
            list = storeBrandDOS.stream().map(StoreBrandDO::getStoreGuid).collect(Collectors.toList());
        return list;
    }

    @Override
    public BrandDTO createDefaultBrand() {
        BrandDO brandDO = new BrandDO();
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl(null);
        brandDO.setIsEnable(true);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(DateTimeUtils.now());
        brandDO.setGmtModified(DateTimeUtils.now());
        save(brandDO.setGuid(distributedIdService.nextBrandGuid()));
        BrandDTO brandDTO = brandMapstruct.brandDO2DTO(brandDO);
        brandDTO.setMchntTypeCode(userClient.queryMchntType());
//        // 给默认品牌添加默认商品属性组
//        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
//        itemSingleDTO.setData(brandDTO.getGuid());
//        itemClient.addDefaultAttr(itemSingleDTO);

        log.info("send  msg to Mq ----------------------------- 创建默认品牌{}", JacksonUtils.writeValueAsString(brandDO));
        // MDM上线后 删除 同步给会员的消息
        Message message = new Message(MemberSyncConstant.MEMBER_BRAND_SYNC_TOPIC,
                MemberSyncConstant.MEMBER_BRAND_MODIFY_TAG, JacksonUtils.toJsonByte(brandDTO));
        message.getProperties().put(MemberSyncConstant.MEMBER_SYNC_PROPERTY, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);
        // 给下游：品牌创建初始化菜品数据等
        Message message1 = new Message(MqConstant.DOWNSTREAM_BRAND_CREATE_TOPIC
                , MqConstant.DOWNSTREAM_BRAND_CREATE_TAG, JacksonUtils.toJsonByte(brandDTO));
        message1.getProperties().put(MqConstant.DOWNSTREAM_CONTEXT, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message1);
        return brandDTO;
    }

    @Override
    public BrandDTO queryDefaultBrand() {
        List<BrandDO> list = list(new LambdaQueryWrapper<BrandDO>()
                .orderByAsc(BrandDO::getGmtCreate)
                .last("limit 1"));
        if (CollectionUtils.isEmpty(list)) {
            return createDefaultBrand();
        }
        List<BrandDTO> brandDTOList = brandMapstruct.brandDOList2DTOList(list);
        return brandDTOList.get(0);
    }

    /**
     * 更改销售模式
     *
     * @param brandDTO DTO
     * @return true-成功，false-失败
     */
    @Override
    public Boolean updateSalesModel(BrandDTO brandDTO) {
        if (ObjectUtils.isEmpty(brandDTO.getGuid())) {
            throw new BusinessException("更新时品牌guid不能为空");
        }
        if (ObjectUtils.isEmpty(brandDTO.getSalesModel())) {
            throw new BusinessException("销售模式不为空");
        }

        // 通知模式变更
        UserContext userContext = UserContextUtils.get();
        CompletableFuture.runAsync(() -> {
            dynamicHelper.changeDatasource(UserContextUtils.getEnterpriseGuid());
            UserContextUtils.put(userContext);
            itemClient.changeSaleModel(brandDTO.getGuid(), brandDTO.getSalesModel());
        });
        log.info("商品销售模式更改：brandGuid = {},salesModel = {}", brandDTO.getGuid(), brandDTO.getSalesModel());
        return this.update(new LambdaUpdateWrapper<BrandDO>()
                .set(BrandDO::getSalesModel, brandDTO.getSalesModel())
                .eq(BrandDO::getGuid, brandDTO.getGuid()));
    }

    /**
     * 判断新增或修改时同企业下品牌名称是否重复
     *
     * @param brandName 品牌名称
     * @return 重复-true，未重复-false
     */
    private boolean validateBrandName(String brandName) {
        Wrapper<BrandDO> wrapper = new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getName, brandName);
        return brandMapper.selectCount(wrapper) > 0;
    }

    /**
     * 更新扎帐信息列表
     */
    @Override
    public Boolean updateBrandAccountStatus(BrandDTO brandDTO) {
        BrandDO one = this.getOne(new LambdaQueryWrapper<BrandDO>()
                .eq(BrandDO::getGuid, brandDTO.getGuid()));
        if (one != null) {
            LambdaUpdateWrapper<BrandDO> branw = new UpdateWrapper<BrandDO>().lambda().eq(BrandDO::getGuid, brandDTO.getGuid());
            if (brandDTO.getIsBuAccounts() != null) {
                branw.set(BrandDO::getIsBuAccounts, brandDTO.getIsBuAccounts());
            }
            if (brandDTO.getIsShowCash() != null) {
                branw.set(BrandDO::getIsShowCash, brandDTO.getIsShowCash());
            }
            if (brandDTO.getIsMultiHandover() != null) {
                branw.set(BrandDO::getIsMultiHandover, brandDTO.getIsMultiHandover());
            }
            int update = brandMapper.update(null, branw);
            return update > 0;
        }
        return false;
    }

    @Override
    public List<StoreDTO> queryStoreByBrandGuid(SingleDataDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getData())) {
            throw new BusinessException("品牌guid不能为空");
        }
        QueryBrandDTO queryBrandDTO = new QueryBrandDTO();
        queryBrandDTO.setBrandGuid(reqDTO.getData());
        return storeBrandMapper.queryStore(queryBrandDTO);
    }

}
