package com.holderzone.saas.store.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.organization.domain.StoreBrandDO;
import org.springframework.lang.Nullable;

/**
 * 门店-品牌 服务类
 *
 * <AUTHOR>
 * @since 2019-01-07
 */
public interface StoreBrandService extends IService<StoreBrandDO> {

    String createStoreBrand(String storeGuid, @Nullable String brandGuid, String createUserGuid, String modifiedUserGuid);
}
