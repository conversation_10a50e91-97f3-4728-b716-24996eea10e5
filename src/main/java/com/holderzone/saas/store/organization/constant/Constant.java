package com.holderzone.saas.store.organization.constant;

/**
 * <AUTHOR>
 * @description 常量类
 * @date 2020/11/16 9:47
 */
public class Constant {

    public static final Integer FALSE = 0;

    public static final Integer TRUE = 1;

    public static final Integer NUMBER_ZERO = 0;

    public static final String CAN_NOT_SWITCH_BRANDS = "已绑定价格方案，不能切换品牌";

    public static final String STORE_CANNOT_BE_NULL = "门店不能为空";

    public static final String STORE_UNDER_BRAND_IS_EMPTY = "品牌下门店为空";

    public static final String BRAND_CANNOT_BE_NULL = "品牌不能为空";

    public static final Integer NOT_SET = -1;
    /**
     * pad商家点餐
     */
    public static final Integer MERCHANT_ORDER = 0;

    /**
     * pad用户自主点餐
     */
    public static final Integer USER_ONESELF_ORDER = 1;


}
