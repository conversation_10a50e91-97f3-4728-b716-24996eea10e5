package com.holderzone.saas.store.organization.service.remote;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.store.store.BindupAccountsDTO;
import com.holderzone.saas.store.organization.vo.BindupAccountsDo;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className BusinessClient
 * @date 18-8-30 上午10:39
 * @description 服务间调用-营业相关服务
 * @program holder-saas-store-organization
 */
@Component
@FeignClient(value = "holder-saas-store-business", fallbackFactory = BusinessClient.ServiceFallBack.class)
public interface BusinessClient {

    /**
     * 根据storeGuid查询该门店是否全部设备均已交班
     *
     * @param handoverRecordIsAllConfirmedDTO DTO
     * @return 1=是，0=否
     */
    @PostMapping("/handover/isAllConfirmed")
    Integer isAllConfirmed(@RequestBody HandoverRecordIsAllConfirmedDTO handoverRecordIsAllConfirmedDTO);

    /**
     * 根据storeGuid数组判断这些门店是否已全部交班
     *
     * @param storeGuidList 门店guid数组
     * @return 1=是，0=否
     */
    @PostMapping("/handover/isAllConfirmedByList")
    Integer isAllConfirmedByList(@RequestBody List<String> storeGuidList);

    /**
     * 查询门店配置
     *
     * @param storeConfigQueryDTO DTO
     * @return StoreConfigDTO StoreConfigDTO
     */
    @PostMapping("/storeConfig/query")
    StoreConfigDTO query(@RequestBody StoreConfigQueryDTO storeConfigQueryDTO);

    /**
     * 创建门店配置
     *
     * @param storeConfigCreateDTO DTO
     */
    @PostMapping("/storeConfig/create")
    void create(@RequestBody StoreConfigCreateDTO storeConfigCreateDTO);

    /**
     * 更新门店配置
     *
     * @param storeConfigUpdateDTO DTO
     */
    @PostMapping("/storeConfig/update")
    void update(@RequestBody StoreConfigUpdateDTO storeConfigUpdateDTO);

    /**
     * 根据门店guid数组查询门店配置
     *
     * @param stringList
     * @return
     */
    @PostMapping("/storeConfig/queryByIdList")
    List<StoreConfigDTO> queryByIdList(@RequestBody List<String> stringList);

    /**
     * 根据指定时间查询对应门店的扎帐记录信息
     * @param storeGuid
     * @param currentTime
     * @return
     */
    @PostMapping("/query")
    List<BindupAccountsDo> queryBindUpAccounts(@RequestParam("storeGuid") String storeGuid, String currentTime);

    /**
     * 获取门店的最新扎帐时间
     * @param storeGuid 门店guid
     * @return
     */
    @PostMapping("/bindupAccount/query/last")
    BindupAccountsDo queryBindUpAccountsLast(@RequestParam("storeGuid") String storeGuid);


    /**
     * 保存扎帐信息
     * @param storeGuid
     * @param userGuid
     * @param userName
     * @return
     */
    @PostMapping("/bindupAccount/save")
    BindupAccountsDTO saveBindUpAccounts(@RequestParam("storeGuid") String storeGuid,
                                         @RequestParam("userGuid") String userGuid,
                                         @RequestParam("userName") String userName,
                                         @RequestParam("buAccounts") LocalDate buAccounts);



    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<BusinessClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public BusinessClient create(Throwable throwable) {
            return new BusinessClient() {
                @Override
                public Integer isAllConfirmed(HandoverRecordIsAllConfirmedDTO handoverRecordIsAllConfirmedDTO) {
                    log.error(HYSTRIX_PATTERN, "isAllConfirmed", JacksonUtils.writeValueAsString(handoverRecordIsAllConfirmedDTO),
                              ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public Integer isAllConfirmedByList(List<String> storeGuidList) {
                    log.error(HYSTRIX_PATTERN, "isAllConfirmedByList", JacksonUtils.writeValueAsString(storeGuidList),
                              ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public StoreConfigDTO query(StoreConfigQueryDTO storeConfigQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "query", JacksonUtils.writeValueAsString(storeConfigQueryDTO),
                              ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public void create(StoreConfigCreateDTO storeConfigCreateDTO) {
                    log.error(HYSTRIX_PATTERN, "create", JacksonUtils.writeValueAsString(storeConfigCreateDTO),
                              ThrowableUtils.asString(throwable));
                    throw new BusinessException("初始化门店营业配置失败");
                }

                @Override
                public void update(StoreConfigUpdateDTO storeConfigUpdateDTO) {
                    log.error(HYSTRIX_PATTERN, "update", JacksonUtils.writeValueAsString(storeConfigUpdateDTO),
                              ThrowableUtils.asString(throwable));
                }

                @Override
                public List<StoreConfigDTO> queryByIdList(List<String> stringList) {
                    log.error(HYSTRIX_PATTERN, "queryByIdList", JacksonUtils.writeValueAsString(stringList),
                              ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public List<BindupAccountsDo> queryBindUpAccounts(String storeGuid, String currentTime) {
                    log.error(HYSTRIX_PATTERN, "queryBindUpAccounts", JacksonUtils.writeValueAsString(storeGuid),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询门店信息");
                }

                @Override
                public BindupAccountsDo queryBindUpAccountsLast(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryBindUpAccountsLast", JacksonUtils.writeValueAsString(storeGuid),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询门店最新扎帐信息");
                }

                @Override
                public BindupAccountsDTO saveBindUpAccounts(String storeGuid, String userGuid, String userName, LocalDate buAccounts) {
                    log.error("保存扎帐信息 e={}", throwable.getMessage());
                    throw new BusinessException("保存扎帐信息" + throwable.getMessage());
                }
            };
        }
    }
}
