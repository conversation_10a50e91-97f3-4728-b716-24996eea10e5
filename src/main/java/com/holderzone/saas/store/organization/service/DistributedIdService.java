package com.holderzone.saas.store.organization.service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedIdService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface DistributedIdService {

    Long rawId(String tag);

    String nextId(String tag);

    List<String> nextBatchId(String tag, long count);

    String nextOrganizationGuid();

    List<String> nextBatchPointItemGuid(long count);

    String nextStoreBrandGuid();

    List<String> nextBatchDstAreaGuid(long count);

    List<String> nextBatchDstItemGuid(long count);

    String nextPrintRecordGuid();

    List<String> nextBatchPrintRecordGuid(long count);

    String nextBrandGuid();

    List<String> nextBatchKitchenItemGuid(long count);

    List<String> nextBatchKitchenAttrGuid(long count);
}
