package com.holderzone.saas.store.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.QueryBrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.organization.domain.BrandDO;

import java.util.List;

/**
 * 品牌 服务类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
public interface BrandService extends IService<BrandDO> {

    BrandDTO createBrand(BrandDTO brandDTO);

    boolean updateBrand(BrandDTO brandDTO);

    boolean deleteBrand(String brandGuid);

    BrandDTO queryBrandByGuid(String brandGuid);

    List<BrandDTO> queryBrandByIdList(List<String> guidList);

    List<BrandDTO> queryAllList(QueryBrandDTO queryBrandDTO);

    boolean queryExistStoreAccount(String brandGuid);

    List<String> queryStoreGuidListByBrandGuid(String brandGuid);

    BrandDTO createDefaultBrand();

    BrandDTO queryDefaultBrand();

    /**
     * 更改销售模式
     *
     * @param brandDTO DTO
     * @return true-成功，false-失败
     */
    Boolean updateSalesModel(BrandDTO brandDTO);

    /**
     * 修改品牌扎帐信息
     */
    Boolean updateBrandAccountStatus(BrandDTO brandDTO);

    /**
     * 根据品牌guid查询品牌下所有门店
     *
     * @param reqDTO 品牌guid
     * @return 品牌下所有门店
     */
    List<StoreDTO> queryStoreByBrandGuid(SingleDataDTO reqDTO);

}
