package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreMerchantOrderDO
 * @date 2019/4/9
 */
@Data
@TableName("hsw_weixin_merchant_order")
@JsonIgnoreProperties(ignoreUnknown = true)
public class WxStoreMerchantOrderDO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 5701597375371737666L;
	private Long id;
    @TableId("guid")
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String guid;
	@TableField(fill = FieldFill.INSERT)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDel;
    private BigDecimal totalPrice;
    private String openId;
    private String nickName;

    private Integer actualGuestsNo;
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private Integer tradeMode;
    
    /*
     * 0:待处理，1：接单，2：拒单，3：未结帐，4：已结账,5:已做废, 8订单已失效,9.已退菜
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private Integer orderState;

    /**
     * 拒单原因
     */
    private String denialReason;

    private Integer itemCount;
    /**
     * 总订单id
     */
    private String orderGuid;

    /**
     * 取餐号
     */
    private String mark;
    /**
     * 整单备注
     */
    private String remark;


    @TableField(exist=false,select = false)
    private Integer isLogin;

    /**
     * 结账时间
     */
    private LocalDateTime checkoutTime;


    /**
     * 区域id
     */
    private String areaGuid;
	private String areaName;
	private String storeGuid;
	private String storeName;
	private String brandGuid;
	private String brandName;
	@TableField(strategy = FieldStrategy.NOT_NULL)
	private String diningTableGuid;
	private String tableCode;

    /**
     * 当前订单1表示存在，0表示不存在
     */
    private Integer currentOrder;

    /**
     * 操作人员guid
     */
    private String operationGuid;

    /**
     * 操作来源 0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1)," +
     * 			"7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛
     * 	20200917以前都是一体机或者POS机 以后做区分
     *
     */
    private Integer operationSource;

    /**
     * 操作人员名称
     */
    private String operationName;

	/**
	 * 下单头像
	 */
    private String headImgUrl;

	/**
	 * 微信同一批次的guid
	 */
	private String orderRecordGuid;

	/**
	 * 并桌订单的id
	 */
	private String combine;

    /**
     * 手机号 可为null
     */
    private String phone;

    /***
     * 下单来源 0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1),
     *  7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛  16 翼惠天下
     * 下单源
     */
    private Integer orderSource;
}
