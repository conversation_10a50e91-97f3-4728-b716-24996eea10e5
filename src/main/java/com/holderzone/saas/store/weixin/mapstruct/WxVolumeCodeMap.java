package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.holder.saas.member.wechat.dto.member.MemberInfoVolume;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeCodeDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface WxVolumeCodeMap {
	WxVolumeCodeMap INSTANCE = Mappers.getMapper(WxVolumeCodeMap.class);

//	WxVolumeCodeDTO fromMemberInfoVolume(MemberInfoVolumeDTO memberInfoVolumeDTO);
//
//	List<WxVolumeCodeDTO> fromMemberInfoVolumeList(List<MemberInfoVolumeDTO> memberInfoVolumeDTOS);

	WxVolumeCodeDTO fromNewMemberInfoVolume(MemberInfoVolume memberInfoVolume);

	List<WxVolumeCodeDTO> fromNewMemberInfoVolumeList(List<MemberInfoVolume> memberInfoVolumeList);
}
