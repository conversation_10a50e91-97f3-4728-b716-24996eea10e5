package com.holderzone.saas.store.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.weixin.req.WxQueryThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxThirdPartUserInfoRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxThirdPartUserInfo;

/**
 * <AUTHOR>
 * @date 2020/9/16 18:03
 * @description
 */
public interface WxThirdPartUserInfoService extends IService<WxThirdPartUserInfo> {
    /***
     * 新增或更新第三方会员信息
     * @param wxThirdPartUserInfoReqDTO 请求参数
     * @return 返回参数
     */
    Boolean saveOrUpdateThirdPartUserInfo(WxThirdPartUserInfoReqDTO wxThirdPartUserInfoReqDTO);

    /***
     * 校验第三方会员信息
     * @param wxQueryThirdPartUserInfoReqDTO 请求参数
     * @return 返回参数
     */
    WxThirdPartUserInfoRespDTO checkThirdPartUserInfo(WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO);

    /***
     *  查询第三方会员信息
     * @param wxQueryThirdPartUserInfoReqDTO 请求参数
     * @return 返回参数
     */
    WxThirdPartUserInfoRespDTO queryThirdPartUserInfo(WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO);
}
