package com.holderzone.saas.store.weixin.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStorePendingOrdersQuery
 * @date 2019/4/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class WxStorePendingOrdersQuery {

	private String storeGuid;
	private String diningTableGuid;
	private Integer currentOrder;
	private List<Integer> orderStates;
	private Integer tradeMode;
	private String orderGuid;
	private String combine;
}
