package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import com.holderzone.saas.store.dto.queue.WxQueueListDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.AbstractMemberModelItemService;
import com.holderzone.saas.store.weixin.service.WxConfigOverviewService;
import com.holderzone.saas.store.weixin.service.rpc.WxQueueClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;

@Service
@Slf4j
public class QueueMemberModelItemServiceImpl extends AbstractMemberModelItemService {

	@Autowired
	private WxConfigOverviewService wxConfigOverviewService;

	@Autowired
	private WxQueueClientService wxQueueClientService;


	@Override
	public WxMemberOverviewModelDTO getWxMemberOverviewModel(WxStoreConsumerDTO wxStoreConsumerDTO) {
		List<WxQueueListDTO> wxQueueListDTOS = wxQueueClientService.queryByUser(
				new QueueWechatDTO(wxStoreConsumerDTO.getBrandGuid(), wxStoreConsumerDTO.getOpenId(), null,wxStoreConsumerDTO.getEnterpriseGuid()));
		long count = 0;
		if (!ObjectUtils.isEmpty(wxQueueListDTOS)) {
			count=wxQueueListDTOS.stream().filter(x -> 0==x.getStatus()||2==x.getStatus()).count();
		}
		return new WxMemberOverviewModelDTO(2,"我的排队",(int)count);
	}
}
