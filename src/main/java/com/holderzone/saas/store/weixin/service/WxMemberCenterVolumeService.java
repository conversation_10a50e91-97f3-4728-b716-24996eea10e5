package com.holderzone.saas.store.weixin.service;

import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxMemberVolumeInfoListReqDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;

public interface WxMemberCenterVolumeService {
	ResponseMemberInfoVolume volumeInfoList(WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO);

	WxMemberInfoVolumeDetailsRespDTO volumeCodeDetails(WxVolumeDetailReqDTO wxVolumeDetailReqDTO);
}
