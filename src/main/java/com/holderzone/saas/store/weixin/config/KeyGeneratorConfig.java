package com.holderzone.saas.store.weixin.config;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreConsumerCartItemReqDTO;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Method;

@Configuration
public class KeyGeneratorConfig {

    @Bean("commonsKeyGenerator")
    public KeyGenerator commonsKeyGenerator() {
        return this::commonsCombineKey;
    }

    /**
     * @return
     * @describle 购物车key策略是（业务：storeGuid：openId）
     */
    @Bean("shoppingCartKeyGenerator")
    public KeyGenerator shoppingCartKeyGenerator() {
        return (target, method, params) -> {
            if (params[0] instanceof WxStoreConsumerDTO) {
                WxStoreConsumerDTO wxStoreConsumerDTO = (WxStoreConsumerDTO) params[0];
                return combineKey(BusinessName.SHOPPING_CART, wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getOpenId());
            }
            if (params[0] instanceof WxStoreConsumerCartItemReqDTO) {
                WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO = (WxStoreConsumerCartItemReqDTO) params[0];
                WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreConsumerCartItemReqDTO.getWxStoreConsumerDTO();
                return combineKey(BusinessName.SHOPPING_CART, wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getOpenId());
            } else {
                throw new BusinessException("购物车策略入参错误");
            }

        };
    }

    private String combineKey(String... key) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0, length = key.length; i < length; i++) {
            sb.append(":");
            sb.append(key);
        }
        return String.valueOf(sb.substring(1));
    }

    private String commonsCombineKey(Object target, Method method, Object[] params) {
        StringBuilder sb = new StringBuilder();
        sb.append(target.getClass().getSimpleName());
        sb.append(":");
        sb.append(method.getName());
        for (int i = 0, length = params.length; i < length; i++) {
            sb.append(":");
            sb.append(params.getClass().getSimpleName());
        }
        return String.valueOf(sb);
    }

}
