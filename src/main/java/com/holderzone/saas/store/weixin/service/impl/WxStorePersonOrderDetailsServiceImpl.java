package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.member.terminal.dto.order.ResponseIntegralOffset;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTradeOrderDetailsDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeDetailsGroupDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.enums.weixin.WxOrderStateEnum;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import com.holderzone.saas.store.weixin.service.WxStorePersonOrderDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WxStorePersonOrderDetailsServiceImpl implements WxStorePersonOrderDetailsService {

	private final WxStoreDineInOrderClientService wxStoreDineInOrderClientService;

	private final WxStoreTableClientService wxStoreTableClientService;

	@Lazy
	private final WxOrderRecordService wxOrderRecordService;

	@Autowired
	public WxStorePersonOrderDetailsServiceImpl(WxStoreDineInOrderClientService wxStoreDineInOrderClientService, WxStoreTableClientService wxStoreTableClientService, WxOrderRecordService wxOrderRecordService) {
		this.wxStoreDineInOrderClientService = wxStoreDineInOrderClientService;
		this.wxStoreTableClientService = wxStoreTableClientService;
		this.wxOrderRecordService = wxOrderRecordService;
	}

	@Override
	public WxStoreTradeOrderDetailsRespDTO getPersonOrderDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		DineinOrderDetailRespDTO dineinOrderDetail = getDineinOrderDetail(wxStoreAdvanceConsumerReqDTO.getTradeOrderGuid());
		if (ObjectUtils.isEmpty(dineinOrderDetail)) {
			log.error("我的订单查询为空:{},订单guid:{}", dineinOrderDetail, wxStoreAdvanceConsumerReqDTO.getTradeOrderGuid());
			return null;
		}
		DineinOrderDetailRespDTO mainOrderDetails = getMainOrderDetails(dineinOrderDetail);
		WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetails = getWxStoreTradeOrderDetails(mainOrderDetails);
		wxStoreTradeOrderDetails.setWxStoreAdvanceConsumerReqDTO(wxStoreAdvanceConsumerReqDTO);
		return getWxStoreTradeOrderDetailsResp(dineinOrderDetail, wxStoreTradeOrderDetails, wxStoreAdvanceConsumerReqDTO);
	}

	/**	
	 * 查询商户后台订单
	 * @param orderGuid guid
	 * @return order
	 */
	private DineinOrderDetailRespDTO getDineinOrderDetail(String orderGuid) {
		SingleDataDTO singleDataDTO = new SingleDataDTO();
		singleDataDTO.setData(orderGuid);
		return wxStoreDineInOrderClientService.getOrderDetail(singleDataDTO);
	}

	/**
	 * 封装成子单详情
	 * @param mainOrderDetails order
	 * @return suborder
	 */
	@Override
	public WxStoreTradeOrderDetailsDTO getWxStoreTradeOrderDetails(DineinOrderDetailRespDTO mainOrderDetails) {
		WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
		wxStoreTradeOrderDetailsDTO.setGuid(mainOrderDetails.getGuid());
		wxStoreTradeOrderDetailsDTO.setTableGuid(mainOrderDetails.getDiningTableGuid());
		wxStoreTradeOrderDetailsDTO.setOrderNo(mainOrderDetails.getOrderNo());
		wxStoreTradeOrderDetailsDTO.setMark(mainOrderDetails.getMark());
		wxStoreTradeOrderDetailsDTO.setRemark(mainOrderDetails.getRemark());
		wxStoreTradeOrderDetailsDTO.setDineInItemDTOS(combineItems(mainOrderDetails));
		wxStoreTradeOrderDetailsDTO.setReturnItemDTOS(combineReturnItems(mainOrderDetails));
		wxStoreTradeOrderDetailsDTO.setGmtCreate(mainOrderDetails.getGmtCreate());
		wxStoreTradeOrderDetailsDTO.setCheckoutTime(mainOrderDetails.getCheckoutTime());
		//子单商品总计=订单金额-附加费
		wxStoreTradeOrderDetailsDTO.setTotalPrice(mainOrderDetails.getOrderFee().subtract(mainOrderDetails.getAppendFee()));
		wxStoreTradeOrderDetailsDTO.setOrderModel(mainOrderDetails.getTradeMode());
		wxStoreTradeOrderDetailsDTO.setState(subState(mainOrderDetails));
		return wxStoreTradeOrderDetailsDTO;
	}

	/**
	 * 如果是并桌，合并商品
	 * @param dineinOrderDetailRespDTO
	 * @return
	 */
	private List<DineInItemDTO> combineItems(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
		ArrayList<DineInItemDTO> dineInItemDTOS = new ArrayList<>();
		log.info("我的订单合并商品:{}", dineinOrderDetailRespDTO);
		Optional.ofNullable(dineinOrderDetailRespDTO.getDineInItemDTOS()).ifPresent(dineInItemDTOS::addAll);
		List<DineinOrderDetailRespDTO> subOrderDetails = dineinOrderDetailRespDTO.getSubOrderDetails();
		if (!ObjectUtils.isEmpty(subOrderDetails)) {
			List<DineInItemDTO> collect = subOrderDetails.stream()
				.flatMap(x -> x.getDineInItemDTOS().stream())
				.collect(Collectors.toList());
			Optional.ofNullable(collect).ifPresent(dineInItemDTOS::addAll);
		}
		log.info("我的订单合并商品:{}", dineInItemDTOS);
		return dineInItemDTOS;
	}

	/**
	 * 如果是并桌，合并退货
	 * @param dineinOrderDetailRespDTO
	 * @return
	 */
	private List<ReturnItemDTO> combineReturnItems(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
		ArrayList<ReturnItemDTO> dineInItemDTOS = new ArrayList<>();
		log.info("我的订单合并退货:{}", dineinOrderDetailRespDTO);
		Optional.ofNullable(dineinOrderDetailRespDTO.getReturnItemDTOS()).ifPresent(dineInItemDTOS::addAll);
		List<DineinOrderDetailRespDTO> subOrderDetails = dineinOrderDetailRespDTO.getSubOrderDetails();
		if (!ObjectUtils.isEmpty(subOrderDetails)) {
			List<ReturnItemDTO> collect = subOrderDetails.stream()
				.flatMap(x -> x.getReturnItemDTOS().stream())
				.collect(Collectors.toList());
			Optional.ofNullable(collect).ifPresent(dineInItemDTOS::addAll);
		}
		log.info("我的订单合并退货:{}", dineInItemDTOS);
		return dineInItemDTOS;
	}

	/**
	 * 子单状态
	 * @param dineinOrderDetailRespDTO order
	 * @return state
	 */
	private Integer subState(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
		Integer state = dineinOrderDetailRespDTO.getState();
		log.info("我的订单子单状态封装:{}", dineinOrderDetailRespDTO);
		switch (state) {
			case 1:
				return WxOrderStateEnum.PROCESSED.getCode();
			case 2:
				return WxOrderStateEnum.PAID.getCode();
			case 4:
				return WxOrderStateEnum.CANCELLED.getCode();
			default:
				return 0;
		}
	}

	/**
	 * 封装主单
	 * @param dineinOrderDetailRespDTO order
	 * @return mainOrder
	 */
	@Override
	public WxStoreTradeOrderDetailsRespDTO getWxStoreTradeOrderDetailsResp(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO();
		//验证快餐是否过期
		boolean isFastOrderCancelled = wxOrderRecordService.cancelFastOrder(dineinOrderDetailRespDTO, wxStoreConsumerDTO);
		WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = new WxStoreTradeOrderDetailsRespDTO();
		//子单组
		WxStoreTradeDetailsGroupDTO wxStoreTradeDetailsGroupDTO = new WxStoreTradeDetailsGroupDTO();
		wxStoreTradeDetailsGroupDTO.setWxStoreTradeOrderDetailsDTOS(Collections.singletonList(wxStoreTradeOrderDetailsDTO));
		wxStoreTradeDetailsGroupDTO.setTableCode(dineinOrderDetailRespDTO.getDiningTableName());
		wxStoreTradeDetailsGroupDTO.setAreaName("");
		log.info("我的订单子单组:{}", wxStoreTradeDetailsGroupDTO);
		wxStoreTradeOrderDetailsRespDTO.setWxStoreTradeDetailsGroupDTOS(Collections.singletonList(wxStoreTradeDetailsGroupDTO));
		wxStoreTradeOrderDetailsRespDTO.setOrderNo(dineinOrderDetailRespDTO.getOrderNo());
		wxStoreTradeOrderDetailsRespDTO.setMark(dineinOrderDetailRespDTO.getMark());
		wxStoreTradeOrderDetailsRespDTO.setGmtCreate(dineinOrderDetailRespDTO.getGmtCreate());
		wxStoreTradeOrderDetailsRespDTO.setCheckoutTime(dineinOrderDetailRespDTO.getCheckoutTime());

		Integer tradeMode = dineinOrderDetailRespDTO.getTradeMode();
		if (StringUtils.isEmpty(wxStoreConsumerDTO.getAreaName())) {
			TableDTO table = wxStoreTableClientService.getTableByGuid(wxStoreConsumerDTO.getDiningTableGuid());
			wxStoreConsumerDTO.setAreaName(table.getAreaName());
		}
		wxStoreTradeOrderDetailsRespDTO.setTableCode(tradeMode == 0 ? combineTableCode(dineinOrderDetailRespDTO)
				: wxStoreConsumerDTO.getAreaName().concat(wxStoreConsumerDTO.getTableCode()));
		wxStoreTradeOrderDetailsRespDTO.setGuestCount(combineGuestCount(dineinOrderDetailRespDTO));
		wxStoreTradeOrderDetailsRespDTO.setOrderState(mainState(dineinOrderDetailRespDTO));
		wxStoreTradeOrderDetailsRespDTO.setTradeMode(dineinOrderDetailRespDTO.getTradeMode());
		wxStoreTradeOrderDetailsRespDTO.setAreaName("");
		wxStoreTradeOrderDetailsRespDTO.setStoreName(wxStoreConsumerDTO.getStoreName());
		wxStoreTradeOrderDetailsRespDTO.setBrandName(wxStoreConsumerDTO.getBrandName());


		wxStoreTradeOrderDetailsRespDTO.setTotalPrice(dineinOrderDetailRespDTO.getOrderFee());
		wxStoreTradeOrderDetailsRespDTO.setOrderFee(dineinOrderDetailRespDTO.getOrderFee());
		wxStoreTradeOrderDetailsRespDTO.setChangeFee(dineinOrderDetailRespDTO.getChangeFee());
		wxStoreTradeOrderDetailsRespDTO.setDiscountFee(dineinOrderDetailRespDTO.getDiscountFee());
		wxStoreTradeOrderDetailsRespDTO.setAppendFee(dineinOrderDetailRespDTO.getAppendFee());

		wxStoreTradeOrderDetailsRespDTO.setPayableAmount(dineinOrderDetailRespDTO.getActuallyPayFee());
		wxStoreTradeOrderDetailsRespDTO.setTotalConsumption(dineinOrderDetailRespDTO.getOrderFee());
		wxStoreTradeOrderDetailsRespDTO.setActuallyPayFee(dineinOrderDetailRespDTO.getActuallyPayFee());

		ResponseIntegralOffset integralOffsetResultRespDTO = dineinOrderDetailRespDTO.getIntegralOffsetResultRespDTO();
		if (!ObjectUtils.isEmpty(integralOffsetResultRespDTO) && !ObjectUtils.isEmpty(integralOffsetResultRespDTO.getDeductionMoney())
				&& integralOffsetResultRespDTO.getDeductionMoney().compareTo(BigDecimal.ZERO) > 0) {
			wxStoreTradeOrderDetailsRespDTO.setCardIntegral(integralOffsetResultRespDTO.getUseIntegral());
			wxStoreTradeOrderDetailsRespDTO.setIntegralFee(integralOffsetResultRespDTO.getDeductionMoney());
		}


		OrderFeeDetailDTO orderFeeDetailDTO = dineinOrderDetailRespDTO.getOrderFeeDetailDTO();
		Optional.ofNullable(orderFeeDetailDTO).ifPresent(x -> wxStoreTradeOrderDetailsRespDTO.setOrderFeeDetailDTOS(Collections.singletonList(x)));
		Optional.ofNullable(dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS()).ifPresent(wxStoreTradeOrderDetailsRespDTO::setDiscountFeeDetailDTOS);
		List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS();
		wxStoreTradeOrderDetailsRespDTO.setActuallyPayFeeDetailDTOS(Optional.ofNullable(actuallyPayFeeDetailDTOS).orElse(Collections.emptyList()));
		if (!ObjectUtils.isEmpty(actuallyPayFeeDetailDTOS)) {
			wxStoreTradeOrderDetailsRespDTO.setPayWay(actuallyPayFeeDetailDTOS.get(0).getPaymentTypeName());
		}
		log.info("我的订单主单详情:{}", wxStoreTradeOrderDetailsRespDTO);
		if (isFastOrderCancelled) {
			wxStoreTradeOrderDetailsRespDTO.setOrderState(3);
			wxStoreTradeOrderDetailsDTO.setState(3);
		}
		return wxStoreTradeOrderDetailsRespDTO;
	}

	/**
	 * 并桌合并区域
	 * @param dineinOrderDetailRespDTO
	 * @return
	 */
	private String combineTableCode(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
		String diningTableName = dineinOrderDetailRespDTO.getDiningTableName();
		List<DineinOrderDetailRespDTO> subOrderDetails = dineinOrderDetailRespDTO.getSubOrderDetails();
		if (ObjectUtils.isEmpty(subOrderDetails)) {
			return diningTableName;
		}
		String collect = subOrderDetails.stream()
			.map(DineinOrderDetailRespDTO::getDiningTableName)
			.collect(Collectors.joining(","));
		log.info("我的订单合并区域:{}", collect);
		if (!StringUtils.isEmpty(collect)) {
			return diningTableName.concat(",").concat(collect);
		}
		return diningTableName;
	}

	/**
	 * 并桌后合并就餐人数
	 * @param dineinOrderDetailRespDTO order
	 * @return num
	 */
	private Integer combineGuestCount(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
		Integer totalGuestsCount = dineinOrderDetailRespDTO.getGuestCount();
		List<DineinOrderDetailRespDTO> subOrderDetails = dineinOrderDetailRespDTO.getSubOrderDetails();
		if (!ObjectUtils.isEmpty(subOrderDetails)) {
			totalGuestsCount += subOrderDetails.stream().mapToInt(DineinOrderDetailRespDTO::getGuestCount).sum();
		}
		return totalGuestsCount;
	}

	/**
	 * 主状态
	 * @param dineinOrderDetailRespDTO order
	 * @return int
	 */
	private Integer mainState(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
		Integer state = dineinOrderDetailRespDTO.getState();
		Integer totalState = 0;
		switch (state) {
			case 1: totalState = 1; break;
			case 2: totalState = 2; break;
			case 4: totalState = 3;
		}
		return totalState;
	}

	@Override
	public DineinOrderDetailRespDTO getMainOrderDetails(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
		log.info("订单详情:{}", dineinOrderDetailRespDTO);
		List<DineinOrderDetailRespDTO> subOrderDetails = dineinOrderDetailRespDTO.getSubOrderDetails();
	
		if (!ObjectUtils.isEmpty(subOrderDetails)) {
			return dineinOrderDetailRespDTO;
		}
		String mainOrderGuid = dineinOrderDetailRespDTO.getMainOrderGuid();
		Integer upperState = dineinOrderDetailRespDTO.getUpperState();
		if (!ObjectUtils.isEmpty(mainOrderGuid) && dineinOrderDetailRespDTO.getTradeMode() == 0 && !"0".equals(mainOrderGuid) && !ObjectUtils.isEmpty(upperState) && upperState == 2) {
			SingleDataDTO singleDataDTO = new SingleDataDTO();
			singleDataDTO.setData(mainOrderGuid);
			DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(singleDataDTO);
			log.info("并桌订单详情:{}", orderDetail);
			return orderDetail;
		}
		return dineinOrderDetailRespDTO;
	}

}
