package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.WxSocketDistributionDTO;
import com.holderzone.saas.store.weixin.constant.RocketMqConfig;
import com.holderzone.saas.store.weixin.service.WxSocketMsgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxSocketMsgServiceImpl
 * @date 2019/5/20
 */
@Service
@Slf4j
public class WxSocketMsgServiceImpl implements WxSocketMsgService {

	@Autowired
	private  DefaultRocketMqProducer defaultRocketMqProducer;

	@Override
	public void distribute(WxSocketDistributionDTO wxSocketDistributionDTO) {
		log.info("通过mq推送的socket消息:{}",wxSocketDistributionDTO);
		defaultRocketMqProducer.sendMessage(new Message(RocketMqConfig.WX_STORE_SOCKET_MESSAGE, JacksonUtils.toJsonByte(wxSocketDistributionDTO)));
	}

}
