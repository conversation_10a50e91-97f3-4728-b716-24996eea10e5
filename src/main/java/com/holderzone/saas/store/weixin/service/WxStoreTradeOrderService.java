package com.holderzone.saas.store.weixin.service;

import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolumeDetails;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.SubmitReturnDTO;
import com.holderzone.saas.store.dto.weixin.WebSocketMessageDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxPaidOrderDetailsReqDTO;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description 微信门店订单service
 * @className WxStoreOrderService
 * @date 2019/3/7
 */
public interface WxStoreTradeOrderService {

	/**
	 * @describle	消除预订单相关数据
	 * @param wxStoreAdvanceConsumerReqDTO
	 */
	void clearAdvanceDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);
    /**
     * @param wxStoreAdvanceConsumerReqDTO
     * @describle 正餐：下单之后，orderGuid不能为空
     */
    SubmitReturnDTO submitOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * @describle	获取当前正餐桌台订单详情
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	WebSocketMessageDTO getTableOrderDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    /**
     * @param createDineInOrderReqDTO
     * @return
     * @describle 修改整单备注
     */
    Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    /**
     * @param createDineInOrderReqDTO
     * @return
     * @describle 修改就餐人数
     */
    Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO);


	/**
	 * @describle	获取商户后台订单详情
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	DineinOrderDetailRespDTO getDineinOrderDetailResp(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * @describle	获取当前桌台订单的guid
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	String getOrderGuid(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);


	/**
	 * 计算优惠
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	DineinOrderDetailRespDTO calculate(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);


	void refreshTable(String tableGuid);

	void refreshTable(String tableGuid, String enterpriseGuid, Integer isJump);

	DineinOrderDetailRespDTO getMainOrderDetails(DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

	CardAndVolumeDTO validateCardAndVolume(CardAndVolumeDiscountReqDTO cardAndVolumeDiscountReqDTO);

	WxMemberCardRespDTO cardList(WxMemberCardListReqDTO wxMemberCardListReqDTO);

	WxVolumeCodeRespDTO volumeCodeList(WxVolumeCodeReqDTO wxVolumeCodeReqDTO);

	ResponseMemberInfoVolumeDetails volumeCodeDetails(WxVolumeCodeDetailsReqDTO wxVolumeCodeDetailsReqDTO);

	WebSocketMessageDTO orderDetails(WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO);

	/**
	 * 删除快餐点餐人数
	 */
	void removeFastGuestCount(String diningTableGuid, String openId);

	/**
	 * 订单附加费缓存 -> 桌台附加费缓存
	 */
	void transformSurchargeCache(String tableGuid, String orderGuid);

	/**
	 * 查询桌台 有时限的附加费
	 */
	List<SurchargeLinkDTO> filterTimeLimitTableSurchargeList(String tableGuid, List<SurchargeLinkDTO> surchargeLinkList);

	/**
	 * 查询附加费列表
	 *
	 * @param orderModel 正餐 or 快餐
	 * @param areaGuid   区域guid
	 */
	List<SurchargeLinkDTO> querySurchargeList(Integer orderModel, String areaGuid);

	/**
	 * 设置订单使用附加费缓存
	 */
	void setOrderSurchargeCache(String orderGuid, List<SurchargeLinkDTO> surchargeLinkList);

}
