package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxUserRecordDO
 * @date 2019/04/02 19:08
 * @description 扫码用户信息
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("hsw_weixin_user_record")
public class WxUserRecordDO {

    private Long id;

    @TableId(value = "guid",type= IdType.INPUT)
    private String guid;

    private String openId;

    private String nickName;

    private String headImgUrl;

    private Integer sex;

    private String country;

    private String province;

    private String city;

    private String phone;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    private Boolean isLogin;

    /***
     * 运营主体GUID
     */
    private String operSubjectGuid;

    @TableLogic
    private Integer isDel;
}
