package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.req.WxOperSubjectBrandReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxSendShortMsgReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxUnBandReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandAuthRespDTO;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreBrandAuthController
 * @date 2019/03/01 17:22
 * @description 微信品牌门店公众号授权接口
 * @program holder-saas-store
 */
@RestController
@RequestMapping("/wx_brand_auth")
@Component
@Api(description = "微信品牌公众号绑定控制器")
public class WxStoreBrandAuthController {

    @Autowired
    WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;

    @ApiOperation("获取微信品牌绑定公众号列表")
    @PostMapping("/list_brand_auth")
    public List<WxBrandAuthRespDTO> getBrandList() {
        return wxStoreAuthorizerInfoService.getBrandAuthList();
    }

    @PostMapping("/send_message")
    public String sendShortMessage(@RequestBody WxSendShortMsgReqDTO wxSendShortMsgReqDTO) {
        return wxStoreAuthorizerInfoService.sendShortMessage(wxSendShortMsgReqDTO);
    }

    @PostMapping("/un_band_brand")
    @ApiOperation("通过品牌guid解绑公众号")
    public Integer deleteBrandBand(@RequestBody WxUnBandReqDTO wxUnBandReqDTO) {
        return wxStoreAuthorizerInfoService.unBandBrand(wxUnBandReqDTO);
    }

    @PostMapping("/get_by_brand_guid")
    @ApiOperation("通过品牌guid获取公众号绑定信息")
    public WxBrandAuthRespDTO getByBrandGuid(@RequestBody String brandGuid) {
        return wxStoreAuthorizerInfoService.getByBrandGuid(brandGuid);
    }

    @PostMapping("/bind_subject")
    @ApiOperation("通过品牌guid绑定运营主体与微信公众号信息")
    public Boolean bindSubject(@RequestBody WxOperSubjectBrandReqDTO wxOperSubjectBrandReqDTO) {
        return wxStoreAuthorizerInfoService.bindSubject(wxOperSubjectBrandReqDTO);
    }
}
