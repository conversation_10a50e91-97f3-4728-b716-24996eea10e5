package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.WxStoreMerchantDineInItemDTO;

/**
 * @description 商户商品缓存
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMerchantDineInItemService
 * @date 2019/4/15
 */
public interface WxStoreMerchantDineInItemService {

	/**
	 * 修改商户订单商品
	 * @param wxStoreMerchantDineInItemDTO
	 */
	void update(WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO);

	/**
	 * 删除商户订单商品
	 * @param guid
	 * @return
	 */
	Boolean del(String guid);

	/**
	 * 查询商户订单商品
	 * @param guid
	 * @return
	 */
	WxStoreMerchantDineInItemDTO getWxStoreMerchantDineInItem(String guid);
}
