package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.organization.QueryStoreDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.weixin.service.WxOrganizationService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxOrganizationServiceImpl
 * @date 2019/05/09 17:41
 * @description 微信门店组织信息获取service实现类
 * @program holder-saas-store
 */
@Service
public class WxOrganizationServiceImpl implements WxOrganizationService {
    @Autowired
    OrganizationClientService organizationClientService;

    @Override
    public Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> getStoreConfig(WxStorePageReqDTO wxStorePageReqDTO) {
        Page<StoreDTO> storeDTOPage = getStorePage(wxStorePageReqDTO);
        if (ObjectUtils.isEmpty(storeDTOPage))
            storeDTOPage = new Page<>(wxStorePageReqDTO.getCurrentPage(), wxStorePageReqDTO.getPageSize(), 0);
        List<StoreDTO> storeDTOList = storeDTOPage.getData();
        if (ObjectUtils.isEmpty(storeDTOList))
            storeDTOList = new ArrayList<>();
        List<String> storeGuidList = storeDTOList.stream().map(StoreDTO::getGuid).collect(Collectors.toList());
        return Triple.of(storeDTOPage, storeDTOList, storeGuidList);
    }

    @Override
    public StoreDTO getStoreDTOByGuid(String storeGuid) {
        return organizationClientService.queryStoreByGuid(storeGuid);
    }

    @Override
    public Page<StoreDTO> getStorePage(WxStorePageReqDTO wxStorePageReqDTO) {
        QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setStoreName(wxStorePageReqDTO.getStoreName());
        queryStoreDTO.setCurrentPage(wxStorePageReqDTO.getCurrentPage());
        queryStoreDTO.setPageSize(wxStorePageReqDTO.getPageSize());
        queryStoreDTO.setIsEnable(1);
        return organizationClientService.queryStoreByCondition(queryStoreDTO);
    }
}