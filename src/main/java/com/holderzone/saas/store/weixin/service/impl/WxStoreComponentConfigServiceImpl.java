package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxComponentConfigDTO;
import com.holderzone.saas.store.weixin.config.WxThirdOpenConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreComponentConfigDO;
import com.holderzone.saas.store.weixin.mapper.WxStoreComponentConfigMapper;
import com.holderzone.saas.store.weixin.service.WxStoreComponentConfigService;
import com.holderzone.saas.store.weixin.service.rpc.WxComponentClientService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.bean.message.WxOpenXmlMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WxOpenServiceImpl
 * @date 2019/02/23 11:57
 * @description 微信开放平台service实现类
 * @program holder-saas-store-weixin
 */
@Service
@Slf4j
public class WxStoreComponentConfigServiceImpl extends ServiceImpl<WxStoreComponentConfigMapper, WxStoreComponentConfigDO>
        implements WxStoreComponentConfigService {
    @Autowired
    WxThirdOpenConfig wxThirdOpenConfig;

    @Autowired
    WxComponentClientService wxComponentClientService;

    @Override
    public String receiveTicket(WxCommonReqDTO wxCommonReqDTO) {
        // 记录请求信息
        log.info(JacksonUtils.writeValueAsString(wxCommonReqDTO));

        // 验证请求的合法性
        if (!StringUtils.equalsIgnoreCase("aes", wxCommonReqDTO.getEncrypt_type())
                || !wxThirdOpenConfig.getWxOpenComponentService()
                .checkSignature(wxCommonReqDTO.getTimestamp(), wxCommonReqDTO.getNonce(), wxCommonReqDTO.getSignature())) {
            throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
        }

        // 解密微信消息
        WxOpenXmlMessage wxOpenXmlMessage = WxOpenXmlMessage.fromEncryptedXml(
                wxCommonReqDTO.getXml(), wxThirdOpenConfig.getWxOpenConfigStorage(),
                wxCommonReqDTO.getTimestamp(), wxCommonReqDTO.getNonce(), wxCommonReqDTO.getMsg_signature());
        log.info("接收到微信VerifyTicketXml: \n{}", wxOpenXmlMessage.toString());

        // 验证componentAppId的合法性
        String componentAppId = wxOpenXmlMessage.getAppId();
        if (!StringUtils.equalsIgnoreCase(componentAppId, wxThirdOpenConfig.getWxOpenConfigStorage().getComponentAppId())) {
            throw new IllegalArgumentException(String.format("非法请求，当前componentAppId：%s，可能属于伪造的请求", componentAppId));
        }

        // 获取并设置ComponentVerifyTicket
        String componentVerifyTicket = wxOpenXmlMessage.getComponentVerifyTicket();
        wxThirdOpenConfig.getWxOpenConfigStorage().setComponentVerifyTicket(componentVerifyTicket);

        // 构建WxComponentConfigDTO对象
        WxComponentConfigDTO wxComponentConfigDTO = new WxComponentConfigDTO();
        wxComponentConfigDTO.setComponentAppId(componentAppId);
        wxComponentConfigDTO.setComponentVerifyTicket(componentVerifyTicket);

        // 检查数据库中是否存在配置
        WxComponentConfigDTO isExist = wxComponentClientService.findWxConfig(componentAppId);
        if (ObjectUtils.isEmpty(isExist)) {
            // 如果不存在，添加新配置
            wxComponentClientService.addWxConfig(wxComponentConfigDTO);
            log.info("数据库暂无第三方平台基本数据，添加操作, wxComponentConfigDTO：{}", wxComponentConfigDTO);
        } else {
            // 如果存在，更新配置
            wxComponentClientService.updateWxConfig(wxComponentConfigDTO);
            log.info("查询到第三方平台基本数据，修改操作, wxComponentConfigDTO：{}", wxComponentConfigDTO);
        }
        return "success";
    }

    /**
     * 判断内存中是否有verifyTicket，若没有则设置ticket
     *
     * @param wxComponentConfigDTO
     */
    @Override
    public void setVerifyTicket(WxComponentConfigDTO wxComponentConfigDTO) {
        // 记录传入的wxComponentConfigDTO信息
        log.info("wxComponentConfigDTO111={}", JacksonUtils.writeValueAsString(wxComponentConfigDTO));

        // 从内存中获取verifyTicket
        String verifyTicket = wxThirdOpenConfig.getWxOpenConfigStorage().getComponentVerifyTicket();
        log.info("verifyTicket=" + verifyTicket);

        // 如果内存中没有verifyTicket，则从数据库中获取并设置
        if (ObjectUtils.isEmpty(verifyTicket)) {
            if (ObjectUtils.isEmpty(wxComponentConfigDTO.getComponentVerifyTicket())) {
                log.info("componentVerifyTicket=" + wxComponentConfigDTO.getComponentVerifyTicket());
                wxComponentConfigDTO = wxComponentClientService.findWxConfig(wxComponentConfigDTO.getComponentAppId());
            }
            log.info("wxComponentConfigDTO222={}", JacksonUtils.writeValueAsString(wxComponentConfigDTO));
            wxThirdOpenConfig.getWxOpenConfigStorage().setComponentVerifyTicket(wxComponentConfigDTO.getComponentVerifyTicket());
        }
    }

    /**
     * 获取componentAccessToken
     * 默认从内存中读取，若内存中没有或已过期，则从数据库拉取，
     * 若数据库没有或已过期，则重新从微信服务器拉取
     *
     * @return
     * @throws WxErrorException
     */
    @Override
    public String getAccessToken() throws WxErrorException {
        // 从内存中获取accessToken
        String accessToken = wxThirdOpenConfig.getWxOpenConfigStorage().getComponentAccessToken();
        log.info("accessToken=" + accessToken);

        // 如果内存中有有效的accessToken，直接返回
        if (!ObjectUtils.isEmpty(accessToken) || !wxThirdOpenConfig.getWxOpenConfigStorage().isComponentAccessTokenExpired()) {
            return accessToken;
        }

        // 从数据库中获取WxComponentConfigDTO
        WxComponentConfigDTO wxComponentConfigDTO = wxComponentClientService
                .findWxConfig(wxThirdOpenConfig.getWxOpenConfigStorage().getComponentAppId());
        log.info("wxComponentConfigDTO={}", JacksonUtils.writeValueAsString(wxComponentConfigDTO));

        // 如果数据库中没有配置，抛出异常
        if (ObjectUtils.isEmpty(wxComponentConfigDTO)) {
            throw new BusinessException("当前系统未初始化微信第三方开放平台配置，请联系系统管理员");
        }

        // 检查accessToken是否过期
        String componentExpiresTime = wxComponentConfigDTO.getComponentExpiresTime();
        log.info("componentExpiresTime=" + componentExpiresTime);
        long expiresTime = 0L;
        if (StringUtils.isNotEmpty(componentExpiresTime)) {
            expiresTime = Long.parseLong(componentExpiresTime);
        }
        log.info("ComponentAccessToken=" + wxComponentConfigDTO.getComponentAccessToken());
        accessToken = wxComponentConfigDTO.getComponentAccessToken();

        // 如果accessToken为空或已过期，重新获取
        if (ObjectUtils.isEmpty(accessToken) || System.currentTimeMillis() >= expiresTime) {
            log.info("currentTime=" + System.currentTimeMillis());
            setVerifyTicket(wxComponentConfigDTO);
            accessToken = wxThirdOpenConfig.getWxOpenComponentService().getComponentAccessToken(true);
            log.info("accessToken=" + accessToken);

            // 更新数据库中的accessToken和过期时间
            wxComponentConfigDTO.setComponentAccessToken(accessToken);
            wxComponentConfigDTO.setComponentExpiresTime(Long.toString(System.currentTimeMillis() + (long) (7200 - 200) * 1000L));
            log.info("wxComponentConfigDTO={}", JacksonUtils.writeValueAsString(wxComponentConfigDTO));
            wxComponentClientService.updateWxConfig(wxComponentConfigDTO);
        } else {
            // 更新内存中的accessToken
            int expiresIn = (int) ((expiresTime - System.currentTimeMillis()) / 1000L + 200);
            wxThirdOpenConfig.getWxOpenConfigStorage().updateComponentAccessToken(wxComponentConfigDTO.getComponentAccessToken(), expiresIn);
            log.info("更新内存数据");
        }
        return accessToken;
    }
}
