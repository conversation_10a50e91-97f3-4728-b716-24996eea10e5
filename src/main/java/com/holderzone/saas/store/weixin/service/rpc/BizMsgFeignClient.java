package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessMessageService
 * @date 2018/09/27 11:44
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-store-message", fallbackFactory = BizMsgFeignClient.ServiceFallBack.class)
public interface BizMsgFeignClient {

    /**
     * 消息推送
     *
     * @param businessMessageDTO
     * @return
     */
    @PostMapping("/msg")
    String msg(@RequestBody BusinessMessageDTO businessMessageDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<BizMsgFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public BizMsgFeignClient create(Throwable cause) {
            return new BizMsgFeignClient() {
                @Override
                public String msg(BusinessMessageDTO businessMessageDTO) {
                    log.error(HYSTRIX_PATTERN, "msg", JacksonUtils.writeValueAsString(businessMessageDTO),
                            ThrowableUtils.asString(cause));
                    throw new BusinessException("推送消息至安卓失败");
                }
            };
        }

    }
}