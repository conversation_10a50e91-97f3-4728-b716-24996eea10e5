package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTradeOrderDetailsDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;

/**
 * @describle	已结账或已作废的订单
 * <AUTHOR>
 * @version 1.0
 * @className WxStorePersonOrderDetails
 * @date 2019/6/24
 */
public interface WxStorePersonOrderDetailsService {

	WxStoreTradeOrderDetailsRespDTO getPersonOrderDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	WxStoreTradeOrderDetailsDTO getWxStoreTradeOrderDetails(DineinOrderDetailRespDTO mainOrderDetails);

	WxStoreTradeOrderDetailsRespDTO getWxStoreTradeOrderDetailsResp(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	DineinOrderDetailRespDTO getMainOrderDetails(DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

}
