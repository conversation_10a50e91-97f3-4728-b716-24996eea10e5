package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardOwnedPage;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.weixin.constant.ModelName;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.mapper.WxUserRecordMapper;
import com.holderzone.saas.store.weixin.mapstruct.MemberCardItemMAP;
import com.holderzone.saas.store.weixin.service.WxUserRecordService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxUserRecordServiceImpl
 * @date 2019/04/02 19:31
 * @description 微信扫码用户信息service实现类
 * @program holder-saas-store
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WxUserRecordServiceImpl extends ServiceImpl<WxUserRecordMapper, WxUserRecordDO> implements WxUserRecordService {

    private final RedisUtils redisUtils;

    private final DynamicHelper dynamicHelper;

    private final ExecutorService executorService;

    private final UserMemberSessionUtils userMemberSessionUtils;

    private final HsaBaseClientService hsaBaseClientService;

    private final WxUserRecordMapper wxUserRecordMapper;


    @Override
    public Boolean updateUserLoginByOpenId(String enterpriseGuid, String openId, boolean state, WxMemberSessionDTO memberSessionDTO) {
        dynamicHelper.changeDatasource(enterpriseGuid);
        UserContext userContext = UserContextUtils.get();
        String operSubjectGuid = userContext.getOperSubjectGuid();
        WxUserRecordDO userRecord = findUserRecordByOpenIdAndOperSubjectGuid(openId, operSubjectGuid);

        if (ObjectUtils.isEmpty(userRecord)) {
            handleUserNotFound(openId, operSubjectGuid);
        } else if (userRecord.getIsLogin() == null) {
            userRecord.setIsLogin(state);
            updateById(userRecord);
        }
        return true;
    }

    private WxUserRecordDO findUserRecordByOpenIdAndOperSubjectGuid(String openId, String operSubjectGuid) {
        return getOne(new LambdaQueryWrapper<WxUserRecordDO>()
                .eq(WxUserRecordDO::getOpenId, openId)
                .eq(WxUserRecordDO::getOperSubjectGuid, operSubjectGuid));
    }

    private void handleUserNotFound(String openId, String operSubjectGuid) {
        log.info("未查到该：{}运营主体GUID与openId{}对应的会员信息!", operSubjectGuid, openId);
        List<WxUserRecordDO> userRecords = list(new LambdaQueryWrapper<WxUserRecordDO>().eq(WxUserRecordDO::getOpenId, openId));
        if (!userRecords.isEmpty()) {
            WxUserRecordDO userRecord = userRecords.get(0);
            userRecord.setOperSubjectGuid(operSubjectGuid);
            userRecord.setId(null);
            userRecord.setGuid(redisUtils.generateGuid("WxUserRecord"));
            userRecord.setIsLogin(true);
            userRecord.setGmtCreate(LocalDateTime.now());
            log.info("updateUserLoginByOpenId用户不存在，insert:{}", userRecord);
            save(userRecord);
        }
    }

    @Override
    public Boolean updateUserLogin(WxStoreConsumerDTO wxStoreConsumerDTO) {
        dynamicHelper.changeDatasource(wxStoreConsumerDTO.getEnterpriseGuid());
        log.info("更新微信会员登录状态入参{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
        UserContext userContext = UserContextUtils.get();
        WxUserRecordDO userRecord = findUserRecordByOpenIdAndOperSubjectGuid(wxStoreConsumerDTO.getOpenId(), userContext.getOperSubjectGuid());

        if (ObjectUtils.isEmpty(userRecord)) {
            throw new BusinessException("未查询到该用户");
        }
        userRecord.setIsLogin(Boolean.TRUE.equals(wxStoreConsumerDTO.getIsLogin()));
        updateById(userRecord);
        return true;
    }

    @Override
    public WxUserRecordDO saveOrUpdateUserInfo(WxStoreConsumerDTO wxStoreConsumerDTO) {
        log.info("saveOrUpdateUserInfo保存用户信息请求参数:{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
        String operSubjectGuid = determineOperSubjectGuid(wxStoreConsumerDTO);
        String lockKey = "saveOrUpdateUserInfo:" + wxStoreConsumerDTO.getOpenId() + operSubjectGuid;
        boolean lockSuccess = redisUtils.setNx(lockKey, "1", 30);

        try {
            if (!lockSuccess) {
                log.info("保存用户信息请求重复回调！");
                return null;
            }
            return saveOrUpdateUser(wxStoreConsumerDTO, operSubjectGuid);
        } finally {
            if (lockSuccess) {
                redisUtils.delete(lockKey);
            }
        }
    }

    private String determineOperSubjectGuid(WxStoreConsumerDTO wxStoreConsumerDTO) {
        UserContext userContext = UserContextUtils.get();
        if (StringUtils.hasText(userContext.getOperSubjectGuid())) {
            return userContext.getOperSubjectGuid();
        } else if (StringUtils.hasText(wxStoreConsumerDTO.getOperSubjectGuid())) {
            return wxStoreConsumerDTO.getOperSubjectGuid();
        }
        return "";
    }

    private WxUserRecordDO saveOrUpdateUser(WxStoreConsumerDTO wxStoreConsumerDTO, String operSubjectGuid) {
        WxUserRecordDO userRecord = findUserRecordByOpenIdAndOperSubjectGuid(wxStoreConsumerDTO.getOpenId(), operSubjectGuid);
        log.info("查询出的微信会员信息{}", JacksonUtils.writeValueAsString(userRecord));

        if (ObjectUtils.isEmpty(userRecord)) {
            wxUserRecordMapper.deleteUserRecord(wxStoreConsumerDTO.getOpenId());
            userRecord = createNewUserRecord(wxStoreConsumerDTO);
        } else {
            wxStoreConsumerDTO.setIsLogin(userRecord.getIsLogin());
        }
        updateUserRecordFields(userRecord, wxStoreConsumerDTO, operSubjectGuid);
        saveOrUpdate(userRecord);
        return userRecord;
    }

    private WxUserRecordDO createNewUserRecord(WxStoreConsumerDTO wxStoreConsumerDTO) {
        WxUserRecordDO userRecord = new WxUserRecordDO();
        String guid = redisUtils.generateGuid(ModelName.WX + ":" + wxStoreConsumerDTO.getOpenId() + ":consumer");
        userRecord.setGuid(guid);
        wxStoreConsumerDTO.setUserGuid(guid);
        if (!StringUtils.isEmpty(wxStoreConsumerDTO.getIsLogin())) {
            userRecord.setIsLogin(wxStoreConsumerDTO.getIsLogin());
        }
        return userRecord;
    }

    private void updateUserRecordFields(WxUserRecordDO userRecord, WxStoreConsumerDTO wxStoreConsumerDTO, String operSubjectGuid) {
        userRecord.setOpenId(wxStoreConsumerDTO.getOpenId());
        userRecord.setNickName(wxStoreConsumerDTO.getNickName());
        userRecord.setCity(wxStoreConsumerDTO.getCity());
        userRecord.setCountry(wxStoreConsumerDTO.getCountry());
        userRecord.setProvince(wxStoreConsumerDTO.getProvince());
        userRecord.setSex(wxStoreConsumerDTO.getSex());
        userRecord.setPhone(wxStoreConsumerDTO.getPhoneNum());
        userRecord.setHeadImgUrl(wxStoreConsumerDTO.getHeadImgUrl());
        userRecord.setOperSubjectGuid(operSubjectGuid);
        log.info("开始保存用户信息:{}", userRecord);
    }

    @Override
    public WxUserRecordDO getWxuserRecord(WxStoreConsumerDTO wxStoreConsumerDTO) {
        UserContext userContext = UserContextUtils.get();
        return getOne(new LambdaQueryWrapper<WxUserRecordDO>().eq(WxUserRecordDO::getOpenId, wxStoreConsumerDTO.getOpenId()).eq(WxUserRecordDO::getOperSubjectGuid, userContext.getOperSubjectGuid()));
    }

    @Override
    public WxUserRecordDO getOneByOpenId(String openId) {
        UserContext userContext = UserContextUtils.get();
        return getOne(new LambdaQueryWrapper<WxUserRecordDO>()
                .eq(WxUserRecordDO::getOpenId, openId)
                .eq(WxUserRecordDO::getOperSubjectGuid, userContext.getOperSubjectGuid()));
    }

    @Override
    public Boolean bindPhoneNum(String phoneNum) {
        if (!StringUtils.isEmpty(phoneNum)) {
            UserContext userContext = UserContextUtils.get();
            log.info("绑定手机号请求头:{}", userContext);
            if (userContext != null) {
                String userGuid = userContext.getUserGuid();
                return update(Wrappers.<WxUserRecordDO>lambdaUpdate()
                        .eq(WxUserRecordDO::getOpenId, userGuid)
                        .eq(WxUserRecordDO::getOperSubjectGuid, userContext.getOperSubjectGuid())
                        .set(WxUserRecordDO::getPhone, phoneNum));
            }
        }
        return false;
    }

    @Override
    public WxUserRecordDO obtainByPhone(String phone) {
        if (!StringUtils.isEmpty(phone)) {
            return getOne(Wrappers.<WxUserRecordDO>lambdaQuery().eq(WxUserRecordDO::getPhone, phone), false);
        }
        return null;
    }

    @Override
    public WxUserRecordDO updateUserInfo(String openId) {
        UserContext userContext = UserContextUtils.get();
        //根据运营主体guid和openId查询微信会员信息  如果查询为null
        WxUserRecordDO wxUserRecordDO = getOne(new LambdaQueryWrapper<WxUserRecordDO>().eq(WxUserRecordDO::getOpenId, openId).eq(WxUserRecordDO::getOperSubjectGuid, userContext.getOperSubjectGuid()));
        if (Objects.isNull(wxUserRecordDO)) {
            //再根据openId查询会员信息 如果查出的微信会员信息运营主体为null  证明为老数据 补充运营主体
            List<WxUserRecordDO> list = list(new LambdaQueryWrapper<WxUserRecordDO>().eq(WxUserRecordDO::getOpenId, openId));
            if (CollectionUtils.isNotEmpty(list)) {
                list.stream().forEach(e -> {
                    if (Objects.nonNull(e) && Objects.isNull(e.getOperSubjectGuid())) {
                        e.setOperSubjectGuid(userContext.getOperSubjectGuid());
                        saveOrUpdate(e);
                    }
                });
            }
        }
        log.info("updateUserInfo 更新后的微信会员信息{}", JacksonUtils.writeValueAsString(wxUserRecordDO));
        return wxUserRecordDO;
    }


    /**
     * 异步查询会员卡与优惠券
     *
     * @param wxMemberSessionDTO 用户信息
     */
    @Override
    public void asyncMemberInfo(WxMemberSessionDTO wxMemberSessionDTO, String openId) {
        UserContext userContext = UserContextUtils.get();
        if (StringUtils.isEmpty(userContext.getOperSubjectGuid())) {
            userContext.setOperSubjectGuid(wxMemberSessionDTO.getOperSubjectGuid());
        }
        CompletableFuture.runAsync(() -> {
            DynamicHelper.dynamicSwitch(wxMemberSessionDTO.getEnterpriseGuid(), userContext);
            log.info("首次进入：查询会员卡入参:{},企业信息:{}", openId, wxMemberSessionDTO);
            // 获取user member session
            UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(openId);
            // 会员卡列表缓存
            memberCardListCaChe(wxMemberSessionDTO, openId, userMemberSession);
            // 会员优惠券缓存
            memberInfoVolumeCache(wxMemberSessionDTO, openId, userMemberSession);
            log.info("首次进入：会员userMemberSession:{}", JacksonUtils.writeValueAsString(userMemberSession));
            userMemberSessionUtils.addUserMemberSession(userMemberSession);
        }, executorService);
    }


    /**
     * 会员卡列表缓存
     */
    private void memberCardListCaChe(WxMemberSessionDTO wxMemberSessionDTO, String openId, UserMemberSessionDTO userMemberSession) {
        RequestCardOwnedPage memberCardsOwnedPageReqDTO = new RequestCardOwnedPage();
        memberCardsOwnedPageReqDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        memberCardsOwnedPageReqDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        memberCardsOwnedPageReqDTO.setBrandGuid(wxMemberSessionDTO.getBrandGuid());
        memberCardsOwnedPageReqDTO.setOpenId(openId);
        memberCardsOwnedPageReqDTO.setPage(1);
        memberCardsOwnedPageReqDTO.setPageSize(50);
        List<ResponseMemberCardListOwned> cardList = hsaBaseClientService.getMemberCardByPage(memberCardsOwnedPageReqDTO).getData().getData();
        log.info("会员查询到会员 openid:{},cardList:{}", openId, cardList);
        if (CollectionUtils.isEmpty(cardList)) {
            userMemberSession.setMemberInfoCardGuid("-1");
            return;
        }
        if (StringUtils.isEmpty(userMemberSession.getMemberInfoCardGuid())) {
            // 筛选卡
            ResponseMemberCardListOwned maxItem = cardList.get(0);
            for (int i = 1; i < cardList.size(); i++) {
                ResponseMemberCardListOwned currentItemDTO = cardList.get(i);
                if (maxItem.compareTo(currentItemDTO)) {
                    maxItem = currentItemDTO;
                }
            }
            userMemberSession.setMemberIntegral(1);
            userMemberSession.setMemberInfoCardGuid(maxItem.getMemberInfoCardGuid());
            userMemberSession.setCardGuid(maxItem.getCardGuid());
            userMemberSession.setIsLogin(true);
        }
        // 缓存个人会员卡列表
        userMemberSessionUtils.addCardList(wxMemberSessionDTO.getStoreGuid(), openId, MemberCardItemMAP.INSTANCE.toResponseUserMemberCacheList(cardList));
    }

    /**
     * 会员优惠券缓存
     */
    private void memberInfoVolumeCache(WxMemberSessionDTO wxMemberSessionDTO, String openId, UserMemberSessionDTO userMemberSession) {
        RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setOpenId(openId);
        ResponseMemberInfo memberInfo = hsaBaseClientService.getMemberInfo(requestQueryMemberInfo).getData();
        log.info("首次扫码查询会员信息,memberInfo：{}", memberInfo);
        if (memberInfo == null || StringUtils.isEmpty(memberInfo.getMemberInfoGuid())) {
            log.warn("无法查询到会员!");
            userMemberSession.setVolumeCode("-1");
            return;
        }
        userMemberSession.setMemberInfoGuid(memberInfo.getMemberInfoGuid());
        RequestMemberInfoVolumeQuery volumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        volumeQueryReqDTO.setMayUseVolume(0);
        volumeQueryReqDTO.setVolumeType(-1);
        volumeQueryReqDTO.setBrandGuid(wxMemberSessionDTO.getBrandGuid());
        volumeQueryReqDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        volumeQueryReqDTO.setMemberInfoGuid(memberInfo.getMemberInfoGuid());
        ResponseMemberInfoVolume memberVolume = hsaBaseClientService.getMemberVolume(volumeQueryReqDTO).getData();
        log.info("查询会员优惠券列表:{}", memberVolume);
        if (memberVolume == null || ObjectUtils.isEmpty(memberVolume.getMemberVolumeList())) {
            log.error("查询会员优惠券列表为空");
            userMemberSession.setVolumeCode("-1");
            return;
        }
        List<MemberInfoVolume> memberVolumeList = memberVolume.getMemberVolumeList();
        userMemberSession.setVolumeCode(memberVolumeList.size() == 1 ? memberVolumeList.get(0).getVolumeCode() : "0");
    }
}
