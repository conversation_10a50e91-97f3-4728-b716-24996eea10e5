package com.holderzone.saas.store.weixin.entity.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreConfigListQuery
 * @date 2019/02/14 15:32
 * @description 微信点餐门店配置查询query
 * @program holder-saas-store-weixin
 */
@Data
public class WxStoreConfigListQuery {
    /**
     * 门店名
     */
    private String storeName;

    /**
     * 用户可管理的所有门店guid集合
     */
    private List<String> storeGuidList;

    public WxStoreConfigListQuery() {
    }

    public WxStoreConfigListQuery(String storeName, List<String> storeGuidList) {
        this.storeName = storeName;
        this.storeGuidList = storeGuidList;
    }
}
