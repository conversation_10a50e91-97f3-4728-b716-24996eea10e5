package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMpTemplateDO
 * @date 2019/08/16 14:30
 * @description 微信消息模板DO
 * @program holder-saas-store
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "hsw_weixin_mp_template")
public class WxMpTemplateDO {
    private Long id;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 短Id
     */
    private String shortId;

    /**
     * 消息模板在微信的ID，调用消息模板时必需，与公众号绑定
     */
    private String templateId;

    /**
     * 消息模板对应的微信公众号
     */
    private String appId;

    /**
     * 品牌guid
     */
    private String BrandGuid;

    /**
     * 消息模板类型，0：排队消息模板，1：会员余额消费消息模板
     */
    private Integer type;

}
