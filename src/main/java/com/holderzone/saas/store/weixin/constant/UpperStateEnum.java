package com.holderzone.saas.store.weixin.constant;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum UpperStateEnum {

    GENERAL(0, "无并单"),
    /**
     * 并台订单
     */
    MAIN(1, "主单"),
    SUB(2, "子单"),
    /**
     * 结账不关台的订单
     */
    SAME_MAIN(3, "同桌台主单"),
    SAME_SUB(4, "同桌台子单"),
    ;

    private final int code;
    private final String desc;

    /**
     * 并台桌台订单的状态
     */
    public static final List<Integer> COMBINE_ORDER_STATE = Lists.newArrayList(MAIN.getCode(), SUB.getCode());

    /**
     * 结账不关台订单的状态
     */
    public static final List<Integer> SAME_ORDER_STATE = Lists.newArrayList(SAME_MAIN.getCode(), SAME_SUB.getCode());

}
