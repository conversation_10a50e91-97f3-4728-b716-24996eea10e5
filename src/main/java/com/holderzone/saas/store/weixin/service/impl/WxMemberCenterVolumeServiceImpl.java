package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.wechat.dto.coupon.RequestVolumeStoreAndCard;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeStoreAndProduct;
import com.holderzone.holder.saas.member.wechat.dto.coupon.VolumeInStore;
import com.holderzone.holder.saas.member.wechat.dto.member.MemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberInfoVolumeQuery;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.saas.store.dto.weixin.member.WxApplicableProductStores;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxMemberVolumeInfoListReqDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;
import com.holderzone.saas.store.weixin.service.WxMemberCenterVolumeService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WxMemberCenterVolumeServiceImpl implements WxMemberCenterVolumeService {

    private final MemberClientService memberClientService;

    private final HsaBaseClientService hsaBaseClientService;

    @Autowired
    public WxMemberCenterVolumeServiceImpl(MemberClientService memberClientService, HsaBaseClientService hsaBaseClientService) {
        this.memberClientService = memberClientService;
        this.hsaBaseClientService = hsaBaseClientService;
    }

    @Override
    public ResponseMemberInfoVolume volumeInfoList(WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO) {
        dynamicEnterprise(wxMemberVolumeInfoListReqDTO);
        ResponseMemberInfoVolume memberVolume = fetchMemberVolume(wxMemberVolumeInfoListReqDTO);
        log.info("会员优惠券列表:{}", memberVolume);

        List<MemberInfoVolume> filteredVolumeList = filterVolumes(memberVolume, wxMemberVolumeInfoListReqDTO);
        updateVolumeCounts(memberVolume, filteredVolumeList);

        log.info("返回会员优惠券列表:{}", filteredVolumeList);
        return memberVolume;
    }

    private ResponseMemberInfoVolume fetchMemberVolume(WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO) {
        return hsaBaseClientService.getMemberVolume(initialMemberVolume(wxMemberVolumeInfoListReqDTO)).getData();
    }

    private List<MemberInfoVolume> filterVolumes(ResponseMemberInfoVolume memberVolume, WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO) {
        List<MemberInfoVolume> memberVolumeList = memberVolume.getMemberVolumeList();
        if (memberVolume.getNotUseVolumeNum() > 0 && !ObjectUtils.isEmpty(memberVolumeList)) {
            memberVolumeList = memberVolumeList.stream()
                .filter(x -> x.getMayUseVolume() == 0 || (x.getMayUseVolume() == 1 && x.getVolumeEndDate().isAfter(LocalDateTime.now().plusMonths(-1))))
                .collect(Collectors.toList());
        }
        log.info("过滤一个月失效:{}", JacksonUtils.writeValueAsString(memberVolumeList));
        return memberVolumeList.stream()
            .filter(x -> x.getMayUseVolume().equals(wxMemberVolumeInfoListReqDTO.getMayUseVolume()))
            .collect(Collectors.toList());
    }

    private void updateVolumeCounts(ResponseMemberInfoVolume memberVolume, List<MemberInfoVolume> filteredVolumeList) {
        long count = filteredVolumeList.stream().filter(x -> x.getMayUseVolume() == 1).count();
        memberVolume.setNotUseVolumeNum((int) count);
        memberVolume.setMayUseVolumeNum(filteredVolumeList.size() - memberVolume.getNotUseVolumeNum());
        memberVolume.setMemberVolumeList(filteredVolumeList);
    }

    @Override
    public WxMemberInfoVolumeDetailsRespDTO volumeCodeDetails(WxVolumeDetailReqDTO wxVolumeDetailReqDTO) {
        UserContextUtils.putErp(wxVolumeDetailReqDTO.getEnterpriseGuid());
        WxMemberInfoVolumeDetailsRespDTO memberVolumeDetails = fetchVolumeDetails(wxVolumeDetailReqDTO);
        if (ObjectUtils.isEmpty(memberVolumeDetails)) {
            log.warn("会员优惠券详情为空,memberVolumeGuid={}", wxVolumeDetailReqDTO.getMemberVolumeGuid());
            return memberVolumeDetails;
        }
        enrichVolumeDetails(wxVolumeDetailReqDTO, memberVolumeDetails);
        log.info("返回优惠券详情,memberVolumeDetails={}", JacksonUtils.writeValueAsString(memberVolumeDetails));
        return memberVolumeDetails;
    }

    private WxMemberInfoVolumeDetailsRespDTO fetchVolumeDetails(WxVolumeDetailReqDTO wxVolumeDetailReqDTO) {
        try {
            return hsaBaseClientService.getSpecialMemberVolumeDetails(wxVolumeDetailReqDTO.getMemberVolumeGuid()).getData();
        } catch (Exception e) {
            log.error("查询优惠券详情失败:{}", e.getMessage());
            throw new BusinessException("未找到此券");
        }
    }

    private void enrichVolumeDetails(WxVolumeDetailReqDTO wxVolumeDetailReqDTO, WxMemberInfoVolumeDetailsRespDTO memberVolumeDetails) {
        RequestVolumeStoreAndCard volumeStoreAndCardReqDTO = new RequestVolumeStoreAndCard();
        volumeStoreAndCardReqDTO.setEnterpriseGuid(wxVolumeDetailReqDTO.getEnterpriseGuid());
        volumeStoreAndCardReqDTO.setVolumeGuids(Collections.singletonList(wxVolumeDetailReqDTO.getVolumeInfoGuid()));
        List<ResponseVolumeStoreAndProduct> storeAndProductList = hsaBaseClientService.getVolumeStoreAndProduct(volumeStoreAndCardReqDTO).getData();
        log.info("获取优惠券支持门店及菜品 结果：{}", JacksonUtils.writeValueAsString(storeAndProductList));
        if (!CollectionUtils.isEmpty(storeAndProductList)) {
            ResponseVolumeStoreAndProduct storeAndProduct = storeAndProductList.get(0);
            List<WxApplicableProductStores> wxApplicableProductStore = storeAndProduct.getVolumeInStoreList().stream()
                .map(store -> {
                    WxApplicableProductStores stores = new WxApplicableProductStores();
                    stores.setStoreName(store.getStoreName());
                    return stores;
                }).collect(Collectors.toList());
            memberVolumeDetails.setWxApplicableProductStores(wxApplicableProductStore);
            memberVolumeDetails.setSupportAll(storeAndProduct.getSupportAll());
            memberVolumeDetails.setProductDetailRespDTOS(storeAndProduct.getProductDetailRespDTOS());
        }
    }

    private RequestMemberInfoVolumeQuery initialMemberVolume(WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO) {
        RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setBrandGuid(wxMemberVolumeInfoListReqDTO.getBrandGuid());
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid(wxMemberVolumeInfoListReqDTO.getEnterpriseGuid());
        memberInfoVolumeQueryReqDTO.setVolumeType(wxMemberVolumeInfoListReqDTO.getVolumeType());
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid(wxMemberVolumeInfoListReqDTO.getMemberInfoGuid());
        memberInfoVolumeQueryReqDTO.setStoreGuid(wxMemberVolumeInfoListReqDTO.getStoreGuid());
        memberInfoVolumeQueryReqDTO.setMayUseVolume(2);
        return memberInfoVolumeQueryReqDTO;
    }

    private void dynamicEnterprise(WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO) {
        UserContext userContext = UserContextUtils.get();
        if (ObjectUtils.isEmpty(userContext)) {
            userContext = new UserContext();
        }
        userContext.setEnterpriseGuid(wxMemberVolumeInfoListReqDTO.getEnterpriseGuid());
        UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
        EnterpriseIdentifier.setEnterpriseGuid(wxMemberVolumeInfoListReqDTO.getEnterpriseGuid());
    }
}
