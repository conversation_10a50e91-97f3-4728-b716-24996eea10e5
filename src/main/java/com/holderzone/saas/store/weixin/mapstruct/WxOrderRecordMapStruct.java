package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.WxOrderRecordDTO;
import com.holderzone.saas.store.dto.weixin.deal.PayBackOrderRecordDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface WxOrderRecordMapStruct {

	WxOrderRecordMapStruct INSTANCE = Mappers.getMapper(WxOrderRecordMapStruct.class);

	WxOrderRecordDTO getWxOrderRecord(WxOrderRecordDO wxOrderRecordDO);

	WxOrderRecordDO fromPayBackOrder(PayBackOrderRecordDTO payBackOrderRecordDTO);
}
