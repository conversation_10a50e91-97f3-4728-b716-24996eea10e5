package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeConditionQuery;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import com.holderzone.saas.store.dto.config.resp.DineFoodSettingRespDTO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @description business服务调用
 * @date 2022/3/25 17:01
 * @className: BusinessClientService
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = BusinessClientService.FallBack.class)
public interface BusinessClientService {

    @PostMapping("/system/getAll/{storeGuid}")
    List<SystemDiscountDTO> getSystemDiscount(@PathVariable("storeGuid") String storeGuid);

    /**
     * 通过门店和桌台查询附加费信息
     *
     * @param surchargeDTO 入参
     * @return 附加费信息
     */
    @PostMapping("/surcharge/calculate_surcharge")
    @ApiOperation(value = "通过门店和桌台以及人数计算附加费")
    List<SurchargeLinkDTO> calculateSurcharge(@RequestBody SurchargeCalculateDTO surchargeDTO);

    /**
     * 根据条件查询收费规则
     */
    @PostMapping("/surcharge/list_by_condition")
    List<SurchargeLinkDTO> listSurchargeByCondition(@RequestBody SurchargeConditionQuery query);

    /**
     * 查询当前门店未交班所有员工
     *
     * @param storeGuid 门店guid
     * @return 员工列表
     */
    @GetMapping("/handover/query_on_duty_staffs/{storeGuid}")
    List<HandoverRecordDTO> queryOnDutyStaffs(@PathVariable String storeGuid);

    @ApiOperation(value = "查询正餐设置")
    @PostMapping("/storeConfig/dine_food_setting/query")
    DineFoodSettingRespDTO queryDineFoodSetting(@RequestBody StoreConfigQueryDTO configQueryDTO);

    @Component
    class FallBack implements FallbackFactory<BusinessClientService> {

        private static final Logger logger = LoggerFactory.getLogger(FallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public BusinessClientService create(Throwable throwable) {
            return new BusinessClientService() {

                @Override
                public List<SystemDiscountDTO> getSystemDiscount(String storeGuid) {
                    logger.error("e={}", throwable.getMessage());
                    throw new ParameterException("异常");
                }

                @Override
                public List<SurchargeLinkDTO> calculateSurcharge(SurchargeCalculateDTO surchargeDTO) {
                    logger.error(HYSTRIX_PATTERN, "calculateSurcharge", JacksonUtils.writeValueAsString(surchargeDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public List<SurchargeLinkDTO> listSurchargeByCondition(SurchargeConditionQuery query) {
                    logger.error(HYSTRIX_PATTERN, "listByCondition", JacksonUtils.writeValueAsString(query),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public List<HandoverRecordDTO> queryOnDutyStaffs(String storeGuid) {
                    logger.error(HYSTRIX_PATTERN, "queryOnDutyStaffs", storeGuid, throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public DineFoodSettingRespDTO queryDineFoodSetting(StoreConfigQueryDTO configQueryDTO) {
                    logger.error(HYSTRIX_PATTERN, "queryDineFoodSetting", JacksonUtils.writeValueAsString(configQueryDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

            };
        }
    }


}
