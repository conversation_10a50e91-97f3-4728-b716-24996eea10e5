package com.holderzone.saas.store.weixin.utils;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.weixin.constant.WxStickSizeConstant;
import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PdfUtil
 * @date 2019/03/13 17:24
 * @description Pdf处理工具类
 * @program holder-saas-store
 */
@Component
public class PdfUtil {

    private static final String YH_FONT_PATH = "/msyh.ttf";

    private void createPdfBody(Document document, WxTableStickDTO wxTableStickDTO) {
        BaseFont baseFont = getMsyhFont();
        try {
            if (StringUtils.isEmpty(wxTableStickDTO.getBgUrl())) {
                throw new BusinessException("桌贴图片下载失败，无对应背景图片，请重新创建桌贴");
            }
            String backImageUrl = wxTableStickDTO.getBgUrl();
            if (StringUtils.hasText(wxTableStickDTO.getBackImage())) {
                backImageUrl = wxTableStickDTO.getBackImage();
            }
            // 背景图
            Image image = Image.getInstance(new URL(backImageUrl));
            image.setAbsolutePosition(0, 0);
            float width = image.getWidth();
            float height = image.getHeight();
            if (width != 0 && height != 0) {
                float wPercent = WxStickSizeConstant.WIDTH_SIZE_400 / width;
                float lPercent = WxStickSizeConstant.HEIGHT_SIZE_500 / height;
                image.scalePercent(wPercent * 100, lPercent * 100);
            } else {
                image.scaleAbsolute(WxStickSizeConstant.WIDTH_SIZE_400, WxStickSizeConstant.HEIGHT_SIZE_500);
            }
            document.add(image);


            // 标题
            Font font = new Font(baseFont);
            font.setStyle(Font.BOLD);
            if (!ObjectUtils.isEmpty(wxTableStickDTO.getStoreNameTextColor())) {
                Color color = new Color(Integer.parseInt(wxTableStickDTO.getStoreNameTextColor().replace("#", ""), 16));
                font.setColor(new BaseColor(color));
            } else {
                wxTableStickDTO.setStoreNameText(null);
            }
            font.setSize(22F);
            Paragraph paragraph = new Paragraph(StringUtils.isEmpty(wxTableStickDTO.getStoreNameText()) ? " " : wxTableStickDTO.getStoreNameText(), font);
            paragraph.setAlignment(Element.ALIGN_CENTER);
            paragraph.setLeading(22F);
            paragraph.setSpacingBefore(32F);

            // 小标题
            Font font1 = new Font(baseFont);
            if (!ObjectUtils.isEmpty(wxTableStickDTO.getStoreDescTextColor())) {
                Color color1 = new Color(Integer.parseInt(wxTableStickDTO.getStoreDescTextColor().replace("#", ""), 16));
                font1.setColor(new BaseColor(color1));
            } else {
                wxTableStickDTO.setStoreDescText(null);
            }

            font1.setSize(12F);
            Paragraph paragraph1 = new Paragraph(StringUtils.isEmpty(wxTableStickDTO.getStoreDescText()) ? " " : wxTableStickDTO.getStoreDescText(), font1);
            paragraph1.setAlignment(Element.ALIGN_CENTER);
            paragraph1.setSpacingBefore(8F);
            paragraph1.setLeading(12F);


            // 区域
            Font font2 = new Font(baseFont);
            if (!ObjectUtils.isEmpty(wxTableStickDTO.getAreaTextColor())) {
                Color color2 = new Color(Integer.parseInt(wxTableStickDTO.getAreaTextColor().replace("#", ""), 16));
                font2.setColor(new BaseColor(color2));
            } else {
                wxTableStickDTO.setAreaText(null);
            }
            font2.setSize(15F);
            Paragraph paragraph2 = new Paragraph("1".equals(wxTableStickDTO.getAreaShow())
                    ? wxTableStickDTO.getAreaText() : " ", font2);
            paragraph2.setAlignment(Element.ALIGN_CENTER);
            paragraph2.setSpacingBefore(21F);
            paragraph2.setSpacingAfter(4F);
            paragraph2.setLeading(15F);


            // 桌号
            Font font3 = new Font(baseFont);
            if (!ObjectUtils.isEmpty(wxTableStickDTO.getTableNumberTextColor())) {
                Color color3 = new Color(Integer.parseInt(wxTableStickDTO.getTableNumberTextColor().replace("#", ""), 16));
                font3.setColor(new BaseColor(color3));
            } else {
                wxTableStickDTO.setTableNumberText(null);
            }
            font3.setSize(25F);
            font3.setStyle(Font.BOLD);
            String tableNumberText = StringUtils.isEmpty(wxTableStickDTO.getTableNumberText()) ? " " : wxTableStickDTO.getTableNumberText();
            Paragraph paragraph3 = new Paragraph("1".equals(wxTableStickDTO.getTableNumberShow())
                    ? tableNumberText : " ", font3);
            paragraph3.setAlignment(Element.ALIGN_CENTER);
            paragraph3.setSpacingBefore(4F);
            paragraph3.setLeading(25F);

            // 二维码底图
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            if ("1".equals(wxTableStickDTO.getLogoSwitch()) && 1 == wxTableStickDTO.getIsLogoShow()) { // 判断是否显示logo
                QrCodeUtil.encode(wxTableStickDTO.getQrCode(), wxTableStickDTO.getElementLogo(), byteArrayOutputStream, true);
            } else {
                QrCodeUtil.encode(wxTableStickDTO.getQrCode(), byteArrayOutputStream);
            }
            Image image1 = Image.getInstance(byteArrayOutputStream.toByteArray());
            image1.setAlignment(Element.ALIGN_CENTER);
            image1.setSpacingBefore(8F);
            image1.scaleAbsolute(196F, 196F);

            // 二维码描述
            Font font4 = new Font(baseFont);
            if (!ObjectUtils.isEmpty(wxTableStickDTO.getQrCodeTextColor())) {
                Color color4 = new Color(Integer.parseInt(wxTableStickDTO.getQrCodeTextColor().replace("#", ""), 16));
                font4.setColor(new BaseColor(color4));
            } else {
                wxTableStickDTO.setQrCodeText(null);
            }
            font4.setSize(13F);
            Paragraph paragraph4 = new Paragraph(StringUtils.isEmpty(wxTableStickDTO.getQrCodeText()) ? " " : wxTableStickDTO.getQrCodeText(), font4);
            paragraph4.setLeading(13F);
            paragraph4.setAlignment(Element.ALIGN_CENTER);
            paragraph4.setSpacingBefore(11F);

            // wifi账号
            Font font5 = new Font(baseFont);
            if (!ObjectUtils.isEmpty(wxTableStickDTO.getWifiTextColor())) {
                Color color5 = new Color(Integer.parseInt(wxTableStickDTO.getWifiTextColor().replace("#", ""), 16));
                font5.setColor(new BaseColor(color5));
            } else {
                wxTableStickDTO.setWifiText(null);
            }
            font5.setSize(14F);
            Paragraph paragraph5 = new Paragraph(StringUtils.isEmpty(wxTableStickDTO.getWifiText()) ? " " : wxTableStickDTO.getWifiText(), font5);
            paragraph5.setLeading(14F);
            paragraph5.setAlignment(Element.ALIGN_CENTER);
            paragraph5.setSpacingBefore(34F);

            // wifi密码
            Font font6 = new Font(baseFont);
            if (!ObjectUtils.isEmpty(wxTableStickDTO.getWifiPasswordColor())) {
                Color color6 = new Color(Integer.parseInt(wxTableStickDTO.getWifiPasswordColor().replace("#", ""), 16));
                font6.setColor(new BaseColor(color6));
            } else {
                wxTableStickDTO.setWifiPassword(null);
            }
            font6.setSize(14F);
            Paragraph paragraph6 = new Paragraph(StringUtils.isEmpty(wxTableStickDTO.getWifiPassword()) ? " " : wxTableStickDTO.getWifiPassword(), font6);
            paragraph6.setLeading(14F);
            paragraph6.setAlignment(Element.ALIGN_CENTER);
            paragraph6.setSpacingBefore(4F);

            // 支持
            Font font7 = new Font(baseFont);
            if (!ObjectUtils.isEmpty(wxTableStickDTO.getSponsorsTextColor())) {
                Color color7 = new Color(Integer.parseInt(wxTableStickDTO.getSponsorsTextColor().replace("#", ""), 16));
                font7.setColor(new BaseColor(color7));
            } else {
                wxTableStickDTO.setSponsorsText(null);
            }
            font7.setSize(12F);
            Paragraph paragraph7 = new Paragraph(StringUtils.isEmpty(wxTableStickDTO.getSponsorsText()) ? " " : wxTableStickDTO.getSponsorsText(), font7);
            paragraph7.setLeading(12F);
            paragraph7.setAlignment(Element.ALIGN_CENTER);
            paragraph7.setSpacingBefore(25F);

            document.add(paragraph);
            document.add(paragraph1);
            document.add(paragraph2);
            document.add(paragraph3);
            document.add(image1);
            document.add(paragraph4);
            document.add(paragraph5);
            document.add(paragraph6);
            document.add(paragraph7);
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void pdf2Image(InputStream inputStream, int dpi, String imagePath, String imageName) {
        PDDocument pdDocument;
        try {
            FileUtils.mkdirs(imagePath);
            pdDocument = PDDocument.load(inputStream);
            PDFRenderer renderer = new PDFRenderer(pdDocument);
            /* dpi越大转换后越清晰，相对转换速度越慢 */
            StringBuffer imgFilePathBuffer;
            String imgFilePathPrefix = imagePath + File.separator + imageName;
            imgFilePathBuffer = new StringBuffer();
            imgFilePathBuffer.append(imgFilePathPrefix);
            imgFilePathBuffer.append(".png");
            File dstFile = new File(imgFilePathBuffer.toString());
            BufferedImage image = renderer.renderImageWithDPI(0, dpi);
            ImageIO.write(image, "png", dstFile);
            System.out.println("PDF文档转图片成功！");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public String pdf2ImageString(InputStream inputStream, int dpi, String imageName) {
        PDDocument pdDocument;
        try {
            pdDocument = PDDocument.load(inputStream);
            PDFRenderer renderer = new PDFRenderer(pdDocument);
            /* dpi越大转换后越清晰，相对转换速度越慢 */
            BufferedImage image = renderer.renderImageWithDPI(0, dpi);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", byteArrayOutputStream);
            return SecurityManager.entryptBase64(byteArrayOutputStream.toByteArray());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public InputStream pdf2ImageStream(InputStream inputStream, int dpi, String imageName) {
        PDDocument pdDocument;
        try {
            pdDocument = PDDocument.load(inputStream);
            PDFRenderer renderer = new PDFRenderer(pdDocument);
            /* dpi越大转换后越清晰，相对转换速度越慢 */
            BufferedImage image = renderer.renderImageWithDPI(0, dpi);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", byteArrayOutputStream);
            return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public boolean createDirectory(String folder) {
        File dir = new File(folder);
        if (dir.exists()) {
            return true;
        } else {
            return dir.mkdirs();
        }
    }

    /**
     * 设置微软雅黑字体
     *
     * @return
     */
    public BaseFont getMsyhFont() {
        try {
            return BaseFont.createFont(YH_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public InputStream createStickImage(WxTableStickDTO wxTableStickDTO) {
        if (ObjectUtils.isEmpty(wxTableStickDTO))
            throw new BusinessException("桌贴参数为空");
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        PdfWriter pdfWriter;
        Rectangle rectangle = new Rectangle(WxStickSizeConstant.WIDTH_SIZE_400, WxStickSizeConstant.HEIGHT_SIZE_500);
        Document document = new Document(rectangle, 0, 0, 0, 0);
        String imageName = wxTableStickDTO.getAreaText() + File.separator + wxTableStickDTO.getTableNumberText();
        try {
            pdfWriter = PdfWriter.getInstance(document, byteArrayOutputStream);
            document.open();
            createPdfBody(document, wxTableStickDTO);
            document.close();
            return pdf2ImageStream(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()), 144, imageName);
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String createPreviewImage(WxTableStickDTO wxTableStickDTO, String imageName) {
        if (ObjectUtils.isEmpty(wxTableStickDTO))
            throw new BusinessException("桌贴参数为空");
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        PdfWriter pdfWriter;
        Rectangle rectangle = new Rectangle(WxStickSizeConstant.WIDTH_SIZE_400, WxStickSizeConstant.HEIGHT_SIZE_500);
        Document document = new Document(rectangle, 0, 0, 0, 0);
        try {
            pdfWriter = PdfWriter.getInstance(document, byteArrayOutputStream);
            document.open();
            createPdfBody(document, wxTableStickDTO);
            document.close();
            return pdf2ImageString(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()), 144, imageName);
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
