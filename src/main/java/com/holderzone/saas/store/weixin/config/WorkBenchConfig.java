package com.holderzone.saas.store.weixin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WorkBenchConfig
 * @date 2019/09/06 11:22
 * @description 小掌工作台相关配置
 * @program holder-saas-store
 */
@Data
@Component
@ConfigurationProperties(prefix = "workbench")
public class WorkBenchConfig {
    private String appId;

    private String appSecret;

}
