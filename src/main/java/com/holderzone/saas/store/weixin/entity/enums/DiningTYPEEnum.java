package com.holderzone.saas.store.weixin.entity.enums;

import com.holderzone.saas.store.weixin.utils.LocaleUtils;

public enum DiningTYPEEnum {
    DINNER(1,"堂食"),
    FASTFOOD(2,"快餐"),
    DINEIN(3,"正餐"),
    WEIXIN(4,"微信"),
    PAD(5,"pad"),
    ALI(6,"支付宝"),
    ;

    private int code;
    private String desc;

    DiningTYPEEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (DiningTYPEEnum c : DiningTYPEEnum.values()) {
            if (c.getCode() == code) {
                return LocaleUtils.getMessage(c.name(),c.desc);
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        try {
            return LocaleUtils.getMessage(name(),desc);
        }catch (Exception e){
            return desc;
        }
    }
}
