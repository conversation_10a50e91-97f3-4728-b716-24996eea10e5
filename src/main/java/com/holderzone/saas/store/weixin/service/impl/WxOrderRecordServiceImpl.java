package com.holderzone.saas.store.weixin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.WxStoreMerchantDineInItemDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.BrandStoreDetailDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.deal.PayBackOrderRecordDTO;
import com.holderzone.saas.store.dto.weixin.deal.UnMemberMessageDTO;
import com.holderzone.saas.store.dto.weixin.deal.WxStoreUserOrderItemDTO;
import com.holderzone.saas.store.dto.weixin.req.WxBrandUserOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxUpdateOrderRecordStateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.*;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.enums.weixin.WxOrderStateEnum;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.constant.UpperStateEnum;
import com.holderzone.saas.store.weixin.entity.domain.*;
import com.holderzone.saas.store.weixin.entity.dto.WxMemberConsumeMsgDTO;
import com.holderzone.saas.store.weixin.entity.enums.WxTemplateTypeEnum;
import com.holderzone.saas.store.weixin.entity.query.WxOrderRecordQuery;
import com.holderzone.saas.store.weixin.entity.query.WxStorePendingOrdersQuery;
import com.holderzone.saas.store.weixin.mapper.WxOrderRecordMapper;
import com.holderzone.saas.store.weixin.mapper.WxStoreMerchantOrderMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxOrderRecordMapStruct;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreMerchantOrderMapstruct;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreUserOrderMapper;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreWeChatOrderClientService;
import com.holderzone.saas.store.weixin.utils.BigDecimalFormatUtil;
import com.holderzone.saas.store.weixin.utils.OrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxOrderRecordServiceImpl
 * @date 2019/4/3
 */
@Service
@Slf4j
public class WxOrderRecordServiceImpl extends ServiceImpl<WxOrderRecordMapper, WxOrderRecordDO> implements WxOrderRecordService {

	@Autowired
	private RedisUtils redisUtils;
	@Autowired
	private WxUserRecordService wxUserRecordService;
	@Autowired
	private WxOrderRecordMapper wxOrderRecordMapper;
	@Autowired
	private WxStoreWeChatOrderClientService wxStoreWeChatOrderClientService;
	@Autowired
	private WxStoreUserOrderMapper wxStoreUserOrderMapper;
	@Autowired
	private WxStoreMerchantOrderService wxStoreMerchantOrderService;
	@Autowired
	private WxStoreMerchantDineInItemService wxStoreMerchantDineInItemService;
	@Autowired
	private WxStoreTableClientService wxStoreTableClientService;
	@Autowired
	private WxStoreSessionDetailsService wxStoreSessionDetailsService;
	@Autowired
	private WxStoreDineInOrderClientService wxStoreDineInOrderClientService;
	@Lazy
	@Resource
	private OrderItemService orderItemService;

	@Resource
	private WxOpenMessageService wxOpenMessageService;

	@Autowired
	private WxStoreMerchantOrderMapper wxStoreMerchantOrderMapper;

	@Autowired
	private WxStoreMerchantOrderMapstruct wxStoreMerchantOrderMapstruct;

	@Autowired
	private WeChatConfig weChatConfig;

	@Autowired
	private Executor orderRecordExecutor;

	@Override
	public boolean update(WxStoreAccountDTO wxStoreAccountDTO) {
		log.info("用户记录单修改:{}", wxStoreAccountDTO);
		WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = wxStoreAccountDTO.getWxStoreAdvanceConsumerReqDTO();
		DineinOrderDetailRespDTO detailRespDTO = wxStoreAccountDTO.getDineinOrderDetailRespDTO();
		WxUserRecordDO userRecordDO = getUserRecord(wxStoreAdvanceConsumerReqDTO);
		if (ObjectUtils.isEmpty(userRecordDO)) {
			wxUserRecordService.saveOrUpdateUserInfo(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO());
			userRecordDO = getUserRecord(wxStoreAdvanceConsumerReqDTO);
		}
		String userGuid = userRecordDO.getGuid();
		WxOrderRecordDO wxOrderRecordDO = getOne(new LambdaQueryWrapper<WxOrderRecordDO>().eq(WxOrderRecordDO::getOrderGuid, detailRespDTO.getGuid()));
		if (ObjectUtils.isEmpty(wxOrderRecordDO)) {
			wxOrderRecordDO = new WxOrderRecordDO();
			wxOrderRecordDO.setGuid(redisUtils.generatdDTOGuid(WxOrderRecordDO.class));
			wxOrderRecordDO.setGmtCreate(LocalDateTime.now());
		}
		wxOrderRecordDO.setOrderGuid(detailRespDTO.getGuid());
		wxOrderRecordDO.setUserRecordGuid(userGuid);
		wxOrderRecordDO.setStoreGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
		wxOrderRecordDO.setAreaGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getAreaGuid());
		wxOrderRecordDO.setTableGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
		wxOrderRecordDO.setGmtModified(LocalDateTime.now());
		wxOrderRecordDO.setActuallyPayFee(detailRespDTO.getActuallyPayFee());

		wxOrderRecordDO.setOrderStateName(detailRespDTO.getStateName());
		wxOrderRecordDO.setBrandGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getBrandGuid());
		wxOrderRecordDO.setLogUrl(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getBrandLogo());
		wxOrderRecordDO.setOrderMode(detailRespDTO.getTradeMode());
		if (detailRespDTO.getTradeMode() == 0) {
			//wxOrderRecordDO.setMerchantGuid(wxStoreSessionDetailsService.getMerchantBatchGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid()));
		} else {
			String tradeOrderGuid = wxStoreAdvanceConsumerReqDTO.getTradeOrderGuid();
			//wxOrderRecordDO.setMerchantGuid(tradeOrderGuid);
		}
		//state转换
		wxOrderRecordDO.setOrderState(orderStateTransition(detailRespDTO));
		return saveOrUpdate(wxOrderRecordDO);
	}

	@Override
	public void updateTable(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {

	}

	@Override
	public List<WxOrderRecordDO> getWxOrderRecordByCondition(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxUserRecordDO userRecord = getUserRecord(wxStoreAdvanceConsumerReqDTO);
		LambdaQueryWrapper<WxOrderRecordDO> queryWrapper = new LambdaQueryWrapper<WxOrderRecordDO>()
				.eq(WxOrderRecordDO::getStoreGuid, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
		if (!ObjectUtils.isEmpty(userRecord)) {
			queryWrapper.eq(WxOrderRecordDO::getUserRecordGuid, userRecord.getGuid());
		}
		return wxOrderRecordMapper.selectList(queryWrapper);
	}

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @describle 结账之后删除桌台上订单为0的批次
	 */
	@Override
	public void delWxOrderRecord(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxUserRecordDO userRecord = getUserRecord((wxStoreAdvanceConsumerReqDTO));
//		remove(new LambdaQueryWrapper<WxOrderRecordDO>().eq(WxOrderRecordDO::getUserRecordGuid, userRecord.getGuid())
//				.eq(WxOrderRecordDO::getOrderState, 0));
	}

	/**
	 * 查询当前用户在这家门店所有订单
	 *
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	private List<WxOrderRecordDO> getPersonWxOrderRecord(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxUserRecordDO userRecord = getUserRecord((wxStoreAdvanceConsumerReqDTO));
		if (ObjectUtils.isEmpty(userRecord)) {
			return null;
		}
		LambdaQueryWrapper<WxOrderRecordDO> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(WxOrderRecordDO::getStoreGuid, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
		wrapper.eq(WxOrderRecordDO::getUserRecordGuid, userRecord.getGuid());
		wrapper.orderByDesc(WxOrderRecordDO::getGmtCreate);
		return list(wrapper);
	}

	@Override
	public WxOrderRecordDTO getOrderRecord(String orderGuid) {
		return getOne(WxOrderRecordQuery.builder().orderGuid(orderGuid).build());
	}

	@Override
	public WxStoreUserOrderDTO getWxStoreUserOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("用户订单入参:{}", JacksonUtils.writeValueAsString(wxStoreAdvanceConsumerReqDTO));
		List<WxOrderRecordDO> personWxOrderRecord = getPersonWxOrderRecord(wxStoreAdvanceConsumerReqDTO);
		log.info("用户订单:{}", JacksonUtils.writeValueAsString(personWxOrderRecord));

		List<DineinOrderDetailRespDTO> orderDetailList = new LinkedList<>();
		SingleListDTO singleListDTO = new SingleListDTO();
		//获取商户存储的正餐已结账或已作废订单，快餐未结账订单
		if (org.apache.commons.collections.CollectionUtils.isNotEmpty(personWxOrderRecord)) {

			//过滤未结账的正餐订单
			personWxOrderRecord.removeIf(x -> x.getOrderMode() == 1 && x.getOrderState() == 1);
			singleListDTO.setList(personWxOrderRecord.stream().map(WxOrderRecordDO::getOrderGuid).filter(Objects::nonNull).collect(Collectors.toList()));
			//订单详情集合
			orderDetailList = wxStoreWeChatOrderClientService.getOrderDetails(singleListDTO);
			log.info("用户订单详情集合:{}", JacksonUtils.writeValueAsString(orderDetailList));
			//快餐超时要做废
			cancelFastFood(orderDetailList, wxStoreAdvanceConsumerReqDTO);
			if (!ObjectUtils.isEmpty(orderDetailList)) {
				orderDetailList = orderDetailList.stream().filter(Objects::nonNull).filter(x -> !x.getOrderNo().startsWith("F"))
						.peek(x -> x.setState(orderStateTransition(x))).collect(Collectors.toList());
			}
		}
		//如果当前用户桌台没有关闭，可能还有未结账正餐
		String orderGuid = wxStoreTableClientService.getOrderGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
		if (!StringUtils.isEmpty(orderGuid)) {
			DineinOrderDetailRespDTO dineinOrderDetailResp = wxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder().data(orderGuid).build());
			List<WxStoreMerchantOrderDTO> pendOrders = getPendOrders(wxStoreAdvanceConsumerReqDTO);
			//没有待确认的订单,状态为待支付
			if (ObjectUtils.isEmpty(pendOrders)) {
				//桌台上有订单
				if (!ObjectUtils.isEmpty(dineinOrderDetailResp)) {
					dineinOrderDetailResp.setGuid(null);
					dineinOrderDetailResp.setState(5);
					orderDetailList.add(dineinOrderDetailResp);
				}
			} else {
				//有待确认的订单
				dineinOrderDetailResp.setState(0);
				dineinOrderDetailResp.setGuid(null);
				orderDetailList.add(dineinOrderDetailResp);
			}
		} else {
			//桌台没有开台，有待处理订单
			List<WxStoreMerchantOrderDTO> pendOrders = getPendOrders(wxStoreAdvanceConsumerReqDTO);
			if (!ObjectUtils.isEmpty(pendOrders)) {
				DineinOrderDetailRespDTO dineinOrderDetailResp = new DineinOrderDetailRespDTO();
				dineinOrderDetailResp.setOrderFee(BigDecimal.ZERO);
				dineinOrderDetailResp.setState(0);
				dineinOrderDetailResp.setGuid(null);
				dineinOrderDetailResp.setTradeMode(0);
				dineinOrderDetailResp.setGmtCreate(pendOrders.get(pendOrders.size() - 1).getGmtCreate());
				WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItem = wxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(pendOrders.get(pendOrders.size() - 1).getGuid());
				dineinOrderDetailResp.setDineInItemDTOS(wxStoreMerchantDineInItem.getDineInItemDTOS());
				orderDetailList.add(dineinOrderDetailResp);
			}
		}
		return convertWxStoreUserOrder(orderDetailList, wxStoreAdvanceConsumerReqDTO);
	}

	/**
	 * order表'1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废',
	 * trade交易状态READY状态(1：未结账， 2：已结账， 3：已退款，4：已作废)
	 * 微信订单状态:0待确认，1已下单，2已支付，3已取消，4，已退菜，5待支付，6已完成,7,没有开台且直接拒单
	 *
	 * @param dineinOrderDetailRespDTO
	 * @describle 将我的订单记录转换成微信对应的状态
	 */
	private Integer orderStateTransition(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
		return OrderUtils.orderStateTransition(dineinOrderDetailRespDTO.getState());
	}

	private void cancelFastFood(List<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOS, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		if (ObjectUtils.isEmpty(dineinOrderDetailRespDTOS)) {
			return;
		}
		WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO();
		UserContext userContext = new UserContext();
		userContext.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
		userContext.setStoreGuid(wxStoreConsumerDTO.getStoreGuid());
		dineinOrderDetailRespDTOS.parallelStream().peek(x -> {
			UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
			EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
			cancelFastOrder(x, wxStoreConsumerDTO);
		}).forEach(x -> log.info("快餐判断作废:{}", x));
	}

	private List<WxStoreMerchantOrderDTO> getPendOrders(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxStorePendingOrdersQuery query = new WxStorePendingOrdersQuery();
		query.setOrderGuid(wxStoreSessionDetailsService.getMerchantBatchGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid()));
		query.setTradeMode(0);
		query.setStoreGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
		query.setDiningTableGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
		query.setOrderStates(Collections.singletonList(0));
		return wxStoreMerchantOrderService.getPendingOrders(query);
	}

	/**
	 * 批量封装订单详情
	 *
	 * @param orderDetailLis
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	private WxStoreUserOrderDTO convertWxStoreUserOrder(List<DineinOrderDetailRespDTO> orderDetailLis, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		if (!ObjectUtils.isEmpty(orderDetailLis)) {
			orderDetailLis = orderDetailLis.stream().peek(x -> {
				if (x.getState() == 6 || x.getState() == 2) {
					x.setOrderFee(x.getActuallyPayFee());
				}
			}).collect(Collectors.toList());
		}
		WxStoreUserOrderDTO wxStoreUserOrder = wxStoreUserOrderMapper.getWxStoreUserOrder(wxStoreAdvanceConsumerReqDTO, orderDetailLis);
		List<WxStoreDineinOrderDetailsRespDTO> orders = wxStoreUserOrder.getOrders();
		if (!ObjectUtils.isEmpty(orders)) {
			for (WxStoreDineinOrderDetailsRespDTO wxStoreDineinOrderDetailsRespDTO : orders) {
				if (ObjectUtils.isEmpty(wxStoreDineinOrderDetailsRespDTO.getLogUrl())) {
					wxStoreDineinOrderDetailsRespDTO.setLogUrl(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getBrandLogo());
				}
				wxStoreDineinOrderDetailsRespDTO.setStoreName(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreName());
			}

			orders = orders.stream().peek(e -> {
				if (ObjectUtils.isEmpty(e.getCheckoutTime())) {
					e.setCheckoutTime(e.getGmtCreate());
				}
			}).sorted(Comparator.comparing(WxStoreDineinOrderDetailsRespDTO::getCheckoutTime).reversed()).collect(Collectors.toList());
			wxStoreUserOrder.setOrders(orders);
		}
		log.info("用户订单返回:{}", JacksonUtils.writeValueAsString(wxStoreUserOrder));
		return wxStoreUserOrder;
	}

	/**
	 * 查询用户信息
	 *
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	private WxUserRecordDO getUserRecord(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		return wxUserRecordService.getWxuserRecord(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO());
	}

	@Override
	public WxOrderRecordDTO getOne(WxOrderRecordQuery query) {
		LambdaQueryWrapper<WxOrderRecordDO> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(!ObjectUtils.isEmpty(query.getOrderGuid()), WxOrderRecordDO::getOrderGuid, query.getOrderGuid());
		//wrapper.eq(!ObjectUtils.isEmpty(query.getMerchantGuid()), WxOrderRecordDO::getMerchantGuid, query.getMerchantGuid());
		WxOrderRecordDO one = getOne(wrapper);
		return WxOrderRecordMapStruct.INSTANCE.getWxOrderRecord(one);
	}

	@Override
	public WxBrandUserOrderDTO getWxBrandUserOrder(WxBrandUserOrderReqDTO wxBrandUserOrderReqDTO) {
		log.info("----------------------------------------分割线开始:我的品牌订单-----------------------------------------------");
		List<WxBrandUserOrderTitleGroup> wxBrandUserOrderTitleGroupList = initialBrandUserOrderTitleGroup();
		WxStoreConsumerDTO wxStoreConsumerDTO = wxBrandUserOrderReqDTO.getWxStoreConsumerDTO();
		final List<WxBrandUserOrderItemDTO> wxBrandUserOrderItemDTOList = new ArrayList<>();

		StopWatch stopWatch = new StopWatch();

		BrandDTO brandDetail = wxStoreSessionDetailsService.getBrandDetail(wxStoreConsumerDTO.getBrandGuid());
		WxUserRecordDO user = wxUserRecordService.getWxuserRecord(wxStoreConsumerDTO);
		if (ObjectUtils.isEmpty(user)) {
			log.info("用户不存在:{}", JacksonUtils.writeValueAsString(wxBrandUserOrderReqDTO));
			return new WxBrandUserOrderDTO(wxBrandUserOrderTitleGroupList, Collections.emptyList());
		}

		stopWatch.start("微信订单");
		List<WxOrderRecordDO> wxOrderRecordList = lambdaQuery()
				.eq(WxOrderRecordDO::getBrandGuid, wxStoreConsumerDTO.getBrandGuid())
				.eq(WxOrderRecordDO::getUserRecordGuid, user.getGuid())
				.in(WxOrderRecordDO::getOrderState, 0,1,2, 3, 5, 6, 7).list();
		log.info("我的品牌订单:微信订单:{}", wxOrderRecordList);
		stopWatch.stop();

		//微信的订单列表
		if (!ObjectUtils.isEmpty(wxOrderRecordList)) {
			//直接拒单或未处理
			stopWatch.start("微信订单详情");
			cancelRedirectOrder(wxStoreConsumerDTO, wxBrandUserOrderItemDTOList, brandDetail, wxOrderRecordList);
			log.info("微信订单详情{}",JacksonUtils.writeValueAsString(wxBrandUserOrderItemDTOList));
			stopWatch.stop();
			//已完成和快餐订单
			stopWatch.start("商户订单详情");
			merchantOrder(wxStoreConsumerDTO, wxBrandUserOrderItemDTOList, brandDetail, wxOrderRecordList);
			log.info("商户订单详情{}",JacksonUtils.writeValueAsString(wxBrandUserOrderItemDTOList));
			stopWatch.stop();
		}


		UserContext userContext = UserContextUtils.get();
		if (!ObjectUtils.isEmpty(wxBrandUserOrderItemDTOList)) {
			stopWatch.start("用户补全");
			wxBrandUserOrderItemDTOList.stream().collect(Collectors.groupingBy(WxBrandUserOrderItemDTO::getState))
					.forEach((state, wxBrandUserOrderItemList) -> {
						wxBrandUserOrderTitleGroupList.forEach(i -> {
							if (state.equals(i.getOrderState())) {
								i.setOrderNum(wxBrandUserOrderItemList.size());
							}
						});
					});

			WxBrandUserOrderDTO wxBrandUserOrderDTO = new WxBrandUserOrderDTO(wxBrandUserOrderTitleGroupList, wxBrandUserOrderItemDTOList);
			if (!ObjectUtils.isEmpty(wxBrandUserOrderReqDTO.getOrderState())) {
				List<WxBrandUserOrderItemDTO> collect = wxBrandUserOrderItemDTOList.stream().filter(x -> x.getState()
						.equals(wxBrandUserOrderReqDTO.getOrderState())).collect(Collectors.toList());
				wxBrandUserOrderDTO.setWxBrandUserOrderItemDTOList(collect);
			}
			//时间倒序
			List<WxBrandUserOrderItemDTO> wxBrandUserOrderItemDTOList1 = wxBrandUserOrderDTO.getWxBrandUserOrderItemDTOList();
			if (!ObjectUtils.isEmpty(wxBrandUserOrderItemDTOList1)) {
				List<WxBrandUserOrderItemDTO> collect = wxBrandUserOrderItemDTOList1.parallelStream().peek(x -> {
					UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
					EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
					WxStoreConsumerDTO wxStoreConsumerDTO1 = x.getWxStoreConsumerDTO();
					String diningTableGuid = wxStoreConsumerDTO1.getDiningTableGuid();
					TableDTO tableByGuid = wxStoreSessionDetailsService.getTableDetail(diningTableGuid);
					log.info("查询桌台详情:{}", tableByGuid);
					if (!ObjectUtils.isEmpty(tableByGuid)) {
						wxStoreConsumerDTO1.setTableCode(tableByGuid.getCode());
						wxStoreConsumerDTO1.setAreaGuid(tableByGuid.getAreaGuid());
						wxStoreConsumerDTO1.setAreaName(tableByGuid.getAreaName());
						wxStoreConsumerDTO1.setStoreName(wxStoreSessionDetailsService.getStoreDetail(tableByGuid.getStoreGuid()).getName());
					}
				}).sorted(Comparator.comparing(WxBrandUserOrderItemDTO::getCheckoutTime).reversed()).collect(Collectors.toList());
				wxBrandUserOrderDTO.setWxBrandUserOrderItemDTOList(collect);
			}
			stopWatch.stop();
			log.info("我的品牌订单耗时:{}",stopWatch.prettyPrint());
			return wxBrandUserOrderDTO;
		}
		return new WxBrandUserOrderDTO(wxBrandUserOrderTitleGroupList, Collections.emptyList());
	}

	private void merchantOrder(WxStoreConsumerDTO wxStoreConsumerDTO, List<WxBrandUserOrderItemDTO> wxBrandUserOrderItemDTOList
			, BrandDTO brandDetail, List<WxOrderRecordDO> wxOrderRecordList) {
		List<String> orderGuidList = wxOrderRecordList.stream()
				.map(WxOrderRecordDO::getOrderGuid)
				.filter(x -> !StringUtils.isEmpty(x)).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(orderGuidList)){
			return ;
		}
		SingleListDTO singleListDTO = new SingleListDTO();
		singleListDTO.setList(orderGuidList);

		if (CollectionUtils.isEmpty(wxOrderRecordList)) {
			return;
		}

		List<DineinOrderDetailRespDTO> orderDetailList = wxStoreWeChatOrderClientService.getOrderDetailList(singleListDTO);
		log.info("我的品牌订单:商户订单:{}", JacksonUtils.writeValueAsString(orderDetailList));

		if (!CollectionUtils.isEmpty(orderDetailList)) {
			Map<String, DineinOrderDetailRespDTO> orderDetailMap = orderDetailList.stream()
					.collect(Collectors.toMap(DineinOrderDetailRespDTO::getGuid, Function.identity()));

			for (WxOrderRecordDO wxOrderRecordDO : wxOrderRecordList) {
				DineinOrderDetailRespDTO detailRespDTO = orderDetailMap.get(wxOrderRecordDO.getOrderGuid());
				log.info("我的品牌订单:单个商户订单:{}", JacksonUtils.writeValueAsString(detailRespDTO));
				if (detailRespDTO == null) {
					continue;
				}

				WxBrandUserOrderItemDTO wxBrandUserOrderItemDTO = new WxBrandUserOrderItemDTO();
				setUpState(detailRespDTO, wxBrandUserOrderItemDTO);
				wxBrandUserOrderItemDTO.setCheckoutTime(detailRespDTO.getGmtCreate());
				wxBrandUserOrderItemDTO.setActuallyPayFee(detailRespDTO.getState() == 1 ? detailRespDTO.getOrderFee() : detailRespDTO.getActuallyPayFee());
				wxBrandUserOrderItemDTO.setLogUrl(wxStoreConsumerDTO.getBrandLogo());
				wxBrandUserOrderItemDTO.setTradeOrderGuid(wxOrderRecordDO.getGuid());
				wxBrandUserOrderItemDTO.setTradeMode(detailRespDTO.getTradeMode());
				wxBrandUserOrderItemDTO.setItemNames(getItemNameList(detailRespDTO));
				wxBrandUserOrderItemDTO.setWxStoreConsumerDTO(WxStoreConsumerDTO.builder()
						.openId(wxStoreConsumerDTO.getOpenId()).nickName(wxStoreConsumerDTO.getNickName()).headImgUrl(wxStoreConsumerDTO.getHeadImgUrl())
						.diningTableGuid(wxOrderRecordDO.getTableGuid())
						.storeGuid(wxOrderRecordDO.getStoreGuid())
						.brandGuid(wxStoreConsumerDTO.getBrandGuid()).brandName(brandDetail.getName()).brandLogo(brandDetail.getLogoUrl())
						.enterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid()).enterpriseName(wxStoreConsumerDTO.getEnterpriseName())
						.build());
				wxBrandUserOrderItemDTOList.add(wxBrandUserOrderItemDTO);
			}
		}
	}

	private void cancelRedirectOrder(WxStoreConsumerDTO wxStoreConsumerDTO, List<WxBrandUserOrderItemDTO> wxBrandUserOrderItemDTOList, BrandDTO brandDetail, List<WxOrderRecordDO> wxOrderRecordList) {
		wxOrderRecordList.stream().filter(x -> x.getOrderState() == 7||x.getOrderState()==0&&StringUtils.isEmpty(x.getOrderGuid())).forEach(x -> {
			WxBrandUserOrderItemDTO wxBrandUserOrderItemDTO = new WxBrandUserOrderItemDTO();
			wxBrandUserOrderItemDTO.setCheckoutTime(x.getGmtCreate());
			wxBrandUserOrderItemDTO.setLogUrl(brandDetail.getLogoUrl());
			wxBrandUserOrderItemDTO.setTradeMode(x.getOrderMode());
			wxBrandUserOrderItemDTO.setActuallyPayFee(x.getActuallyPayFee());
			wxBrandUserOrderItemDTO.setTradeOrderGuid(x.getGuid());
			wxBrandUserOrderItemDTO.setState(OrderUtils.orderStateTransition(x.getOrderState()));

			List<WxOrderItemDO> itemDOS = orderItemService.lambdaQuery()
					.eq(WxOrderItemDO::getOrderRecordGuid, x.getGuid())
					.select(WxOrderItemDO::getItemName).list();
			if (!CollectionUtils.isEmpty(itemDOS)) {
				wxBrandUserOrderItemDTO.setItemNames(itemDOS.stream().map(WxOrderItemDO::getItemName).collect(Collectors.toList()));
			}

			wxBrandUserOrderItemDTO.setWxStoreConsumerDTO(WxStoreConsumerDTO.builder()
					.openId(wxStoreConsumerDTO.getOpenId()).nickName(wxStoreConsumerDTO.getNickName())
					.headImgUrl(wxStoreConsumerDTO.getHeadImgUrl())
					.diningTableGuid(x.getTableGuid())
					.brandGuid(wxStoreConsumerDTO.getBrandGuid()).brandName(brandDetail.getName()).brandLogo(brandDetail.getLogoUrl())
					.enterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid()).enterpriseName(wxStoreConsumerDTO.getEnterpriseName())
					.build());
			wxBrandUserOrderItemDTOList.add(wxBrandUserOrderItemDTO);
		});
	}

	private void setUpState(DineinOrderDetailRespDTO detailRespDTO, WxBrandUserOrderItemDTO wxBrandUserOrderItemDTO) {
		wxBrandUserOrderItemDTO.setState(OrderUtils.tradeStateTransition(detailRespDTO.getState()));
		if (wxBrandUserOrderItemDTO.getState() == null) {
			wxBrandUserOrderItemDTO.setState(1);
		}
		//快餐
		if (detailRespDTO.getTradeMode().equals(1) && wxBrandUserOrderItemDTO.getState().equals(1)) {
			LocalDateTime gmtCreate = detailRespDTO.getCheckoutTime();
			if (gmtCreate == null) {
				gmtCreate = detailRespDTO.getGmtCreate();
			}
			LocalDateTime now = LocalDateTime.now();
			Duration dur = java.time.Duration.between(gmtCreate, now);
			long diff = dur.toMillis();
			if (diff >= 1000 * 60 * 15) {
				wxBrandUserOrderItemDTO.setState(3);
			}
		}
	}

	private Integer convertTradeState(Integer state) {
		switch (state) {
			case 1:
				return 5;
			case 2:
				return 6;
			case 3:
			case 4:
				return 3;
		}
		return 5;
	}

	private List<String> getItemNameList(DineinOrderDetailRespDTO detailRespDTO) {
		log.info("订单详情:{}", detailRespDTO);
		if (ObjectUtils.isEmpty(detailRespDTO) || ObjectUtils.isEmpty(detailRespDTO.getDineInItemDTOS())) {
			return Collections.emptyList();
		}
		return detailRespDTO.getDineInItemDTOS().stream().filter(x->x.getParentItemGuid()==0).map(DineInItemDTO::getItemName).collect(Collectors.toList());
	}

	private List<WxBrandUserOrderTitleGroup> initialBrandUserOrderTitleGroup() {
		List<WxBrandUserOrderTitleGroup> wxBrandUserOrderTitleGroupList = new ArrayList<>();
		wxBrandUserOrderTitleGroupList.add(new WxBrandUserOrderTitleGroup(0, "待确认", 0));
		wxBrandUserOrderTitleGroupList.add(new WxBrandUserOrderTitleGroup(0, "已取消", 3));
		wxBrandUserOrderTitleGroupList.add(new WxBrandUserOrderTitleGroup(0, "待支付", 1));
		wxBrandUserOrderTitleGroupList.add(new WxBrandUserOrderTitleGroup(0, "已完成", 2));
		return wxBrandUserOrderTitleGroupList;
	}

	@Override
	public void cancelFastOrder(String orderGuid) {
		update(new LambdaUpdateWrapper<WxOrderRecordDO>()
				.eq(WxOrderRecordDO::getOrderGuid, orderGuid)
				.set(WxOrderRecordDO::getOrderState, 3)
				.set(WxOrderRecordDO::getOrderStateName, "已取消")
		);
	}

	@Override
	public boolean cancelFastOrder(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, WxStoreConsumerDTO wxStoreConsumerDTO) {
		LocalDateTime gmtCreate = dineinOrderDetailRespDTO.getGmtCreate();
		LocalDateTime now = LocalDateTime.now();
		Duration duration = Duration.between(gmtCreate, now);
		long minutes = duration.toMinutes();
		log.info("判断快餐是作废:{}", dineinOrderDetailRespDTO);
		if (minutes >= 15 && dineinOrderDetailRespDTO.getState() == 1 && dineinOrderDetailRespDTO.getTradeMode() == 1) {
			CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
			cancelOrderReqDTO.setTable(false);
			cancelOrderReqDTO.setFastFood(true);
			cancelOrderReqDTO.setReason("过期做废");
			cancelOrderReqDTO.setUserGuid(wxStoreConsumerDTO.getOpenId());
			cancelOrderReqDTO.setUserName(wxStoreConsumerDTO.getNickName());
			cancelOrderReqDTO.setOrderGuid(dineinOrderDetailRespDTO.getGuid());
			cancelOrderReqDTO.setDeviceId(wxStoreConsumerDTO.getOpenId());
			cancelOrderReqDTO.setDeviceType(12);
			cancelOrderReqDTO.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
			cancelOrderReqDTO.setStoreGuid(wxStoreConsumerDTO.getStoreGuid());
			log.info("快餐作废入参:{}", cancelOrderReqDTO);
			Boolean cancel = wxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO);
			log.info("作废结果:{}", cancel);
			if (cancel) {
				dineinOrderDetailRespDTO.setState(4);
				dineinOrderDetailRespDTO.setStateName("已作废");
//				wxStoreEstimateClientService.dineinFail(dineinOrderDetailRespDTO.getDineInItemDTOS());
				//取消后记录到我的订单记录
				cancelFastOrder(dineinOrderDetailRespDTO.getGuid());
				return true;
			}
			return false;
		}
		return false;
	}


	@Override
	public Boolean replaceOrderID(Map<String, String> map) {
		Set<String> orderGuidList = map.keySet();
		for (String orderGuid : orderGuidList) {
			LambdaUpdateWrapper<WxOrderRecordDO> set = new LambdaUpdateWrapper<WxOrderRecordDO>()
					.eq(WxOrderRecordDO::getOrderGuid, orderGuid)
					.set(WxOrderRecordDO::getOrderGuid, map.get(orderGuid));
			update(set);
		}
		return true;
	}

	@Override
	public List<WxOrderRecordDO> getOldOrderList(Set<String> orderGuidList) {
		if (ObjectUtils.isEmpty(orderGuidList)) {
			return Collections.emptyList();
		}
		return list(new LambdaQueryWrapper<WxOrderRecordDO>().in(WxOrderRecordDO::getOrderGuid, orderGuidList));
	}

	@Override
	public Boolean batchSave(List<WxOrderRecordDO> wxOrderRecordDOS) {
		return this.saveOrUpdateBatch(wxOrderRecordDOS);
	}

	@Override
	public List<WxOrderRecordDO> getOutStandingOrders(String tableGuid) {

		return list(new LambdaQueryWrapper<WxOrderRecordDO>()
				.eq(WxOrderRecordDO::getTableGuid, tableGuid)
				.eq(WxOrderRecordDO::getOrderMode, 0)
				.in(WxOrderRecordDO::getOrderState, Arrays.asList(0, 1))
				.orderByDesc(WxOrderRecordDO::getGmtCreate));
	}

	@Override
	public List<WxStoreUserOrderItemDTO> userStoreList() {
		List<WxStoreUserOrderItemDTO> wxStoreUserOrderItemList = Lists.newArrayList();
		WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
		log.info("我的订单：当前会话:{}", wxMemberSessionDTO);
		boolean isLogin = WeixinUserThreadLocal.getIsLogin();
		log.info("我的订单：登录状态:{}", isLogin);
		WxUserRecordDO one = wxUserRecordService.getOneByOpenId(WeixinUserThreadLocal.getOpenId());
		if (one == null) {
			log.info("用户不存在:{}", WeixinUserThreadLocal.getOpenId());
			return wxStoreUserOrderItemList;
		}
		if(StringUtils.isEmpty(WeixinUserThreadLocal.getStoreGuid()) || StringUtils.isEmpty(WeixinUserThreadLocal.getBrandGuid())){
			log.info("门店或者品牌不存在:{},{}",WeixinUserThreadLocal.getStoreGuid(),WeixinUserThreadLocal.getBrandGuid());
			return wxStoreUserOrderItemList;
		}
		BrandStoreDetailDTO detail = wxStoreSessionDetailsService.getStoreBrandDetail(WeixinUserThreadLocal.getStoreGuid(), WeixinUserThreadLocal.getBrandGuid());
		//多线程去查询订单商品信息
		UserContext userInfoDTO = UserContextUtils.get();
		//已完结的商户订单
		CompletableFuture<List<WxStoreUserOrderItemDTO>> finishOrderListFuture = CompletableFuture.supplyAsync(() -> {
			WeixinUserThreadLocal.put(JacksonUtils.writeValueAsString(wxMemberSessionDTO));
			UserContextUtils.put(userInfoDTO);
			EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
			return filterFinshOrderList(one.getGuid(), detail);
		}, orderRecordExecutor).exceptionally(throwable -> {
			log.error("查询已完成订单异常", throwable);
			return null;
		});
		//待处理 待支付
		CompletableFuture<List<WxStoreUserOrderItemDTO>> unPayOrderListFuture = CompletableFuture.supplyAsync(() -> {
			WeixinUserThreadLocal.put(JacksonUtils.writeValueAsString(wxMemberSessionDTO));
			UserContextUtils.put(userInfoDTO);
			EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
			return filterUnPayOrderList(isLogin, wxMemberSessionDTO, detail);
		}, orderRecordExecutor).exceptionally(throwable -> {
			log.error("查询待处理订单异常", throwable);
			return null;
		});
		CompletableFuture<Void> all = CompletableFuture.allOf(finishOrderListFuture, unPayOrderListFuture);
		try {
			all.join();
			if(CollUtil.isNotEmpty(finishOrderListFuture.get())){
				wxStoreUserOrderItemList.addAll(finishOrderListFuture.get());
			}
			if(CollUtil.isNotEmpty(unPayOrderListFuture.get())){
				wxStoreUserOrderItemList.addAll(unPayOrderListFuture.get());
			}
			if (CollectionUtils.isEmpty(wxStoreUserOrderItemList)) {
				return wxStoreUserOrderItemList;
			}
			wxStoreUserOrderItemList.sort(Comparator.comparing(WxStoreUserOrderItemDTO::getCheckoutTime).reversed());
			wxStoreUserOrderItemList.forEach(e -> {
				if (Objects.isNull(e.getUseDisCount())) {
					e.setUseDisCount(0);
				}
				if (Objects.isNull(e.getOriginalFee())) {
					e.setOriginalFee(BigDecimalFormatUtil.format(e.getDisCountFee() == null ? e.getActuallyPayFee() : e.getDisCountFee()));
				}
			});
			return wxStoreUserOrderItemList;
		} catch (Exception e) {
			log.error("获取订单列表异常", e);
			return wxStoreUserOrderItemList;
		}
	}

	@Override
	public List<WxStoreUserOrderItemDTO> getUnFinishStoreUserOrderList() {
		List<WxStoreUserOrderItemDTO> wxStoreUserOrderItemList = userStoreList();
		if (CollectionUtils.isEmpty(wxStoreUserOrderItemList)) {
			return Lists.newArrayList();
		}
		// 过滤 state = 0 1的订单
		WxStoreUserOrderItemDTO unFinishStoreUserOrder = wxStoreUserOrderItemList.stream()
				.filter(e -> WxOrderStateEnum.PENDING.getCode().equals(e.getState())
						|| WxOrderStateEnum.PROCESSED.getCode().equals(e.getState()))
				.max(Comparator.comparing(WxStoreUserOrderItemDTO::getState))
				.orElse(null);
		if (Objects.isNull(unFinishStoreUserOrder)) {
			return Lists.newArrayList();
		}
		return Lists.newArrayList(unFinishStoreUserOrder);
	}

	private List<WxStoreUserOrderItemDTO> filterUnPayOrderList(boolean isLogin, WxMemberSessionDTO wxMemberSessionDTO, BrandStoreDetailDTO detail) {
		if(wxMemberSessionDTO == null){
			return Collections.emptyList();
		}
		String tableGuid = wxMemberSessionDTO.getDiningTableGuid();
		if(StringUtils.isEmpty(tableGuid)){
			return Collections.emptyList();
		}
		List<WxOrderRecordDO> outStandingOrders = getOutStandingOrders(tableGuid);
		log.info("当前桌台订单:{}", outStandingOrders);
		if (ObjectUtils.isEmpty(outStandingOrders)) {
			return Collections.emptyList();
		}
		WxOrderRecordDO wxOrderRecordDO = outStandingOrders.get(0);
		WxStoreUserOrderItemDTO orderItemDTO = new WxStoreUserOrderItemDTO();
		orderItemDTO.setGuid(wxOrderRecordDO.getGuid());
		orderItemDTO.setTradeMode(wxOrderRecordDO.getOrderMode());
		orderItemDTO.setStoreName(detail.getStoreName());
		orderItemDTO.setBrandName(detail.getBrandName());
		orderItemDTO.setLogUrl(detail.getBrandLogoUrl());
		orderItemDTO.setCheckoutTime(wxOrderRecordDO.getGmtCreate());
		orderItemDTO.setActuallyPayFee(BigDecimal.ZERO);

		String orderGuid = wxStoreTableClientService.getOrderGuid(wxOrderRecordDO.getTableGuid());
		orderItemDTO.setState(OrderUtils.orderStateTransition(wxOrderRecordDO.getOrderState()));
		//没开台
		String wxOrderRecordGuid = wxOrderRecordDO.getOrderGuid();
		if (StringUtils.isEmpty(orderGuid) && StringUtils.isEmpty(wxOrderRecordGuid)
				&& assembleNotOpenTable(orderItemDTO, wxOrderRecordDO.getGuid(), isLogin)) {
			return Lists.newArrayList(orderItemDTO);
		}
		if (StringUtils.isNotEmpty(wxOrderRecordGuid)) {
			orderGuid = wxOrderRecordGuid;
		}
		//开台
		if (!StringUtils.isEmpty(orderGuid) && assembleOpenTable(orderItemDTO, orderGuid, wxOrderRecordDO.getGuid())) {
			return Lists.newArrayList(orderItemDTO);
		}
		return Collections.emptyList();

	}

	private boolean assembleOpenTable(WxStoreUserOrderItemDTO orderItemDTO, String orderGuid, String recordGuid) {
		SingleListDTO singleListDTO = new SingleListDTO();
		singleListDTO.setList(Collections.singletonList(orderGuid));

		List<DineinOrderDetailRespDTO> orderDetailList = wxStoreWeChatOrderClientService.getOrderDetailList(singleListDTO);
		log.info("我的订单：当前桌台:{}", JacksonUtils.writeValueAsString(orderDetailList));
		if (CollUtil.isEmpty(orderDetailList)) {
			return false;
		}
		DineinOrderDetailRespDTO orderDetailRespDTO = orderDetailList.get(0);
		// 多单结账的订单
		if (UpperStateEnum.SAME_ORDER_STATE.contains(orderDetailRespDTO.getUpperState())) {
			setStoreOrderPrice(orderDetailRespDTO, orderItemDTO);
			List<DineInItemDTO> dineInItemDTOS = orderDetailRespDTO.getDineInItemDTOS();
			if (!ObjectUtils.isEmpty(dineInItemDTOS)) {
				orderItemDTO.setItemNames(dineInItemDTOS.stream()
						.filter(k -> k.getParentItemGuid() == 0)
						.map(DineInItemDTO::getItemName)
						.collect(Collectors.toList()));
			}
			orderItemDTO.setState(1);
			return true;
		}

		List<DineInItemDTO> dineInItemDTOList = orderDetailRespDTO.getDineInItemDTOS();
		if (!ObjectUtils.isEmpty(dineInItemDTOList)) {
			orderItemDTO.setItemNames(dineInItemDTOList.stream()
					.map(DineInItemDTO::getItemName).collect(Collectors.toList()));
			//订单为支付中状态 orderFee 为 null
			orderItemDTO.setActuallyPayFee(orderDetailRespDTO.getOrderFee() == null ? orderDetailRespDTO.getActuallyPayFee() : orderDetailRespDTO.getOrderFee());
		}
		//一体机下单 微信无商品 直接为待支付
		List<WxOrderItemDO> list = orderItemService.lambdaQuery().eq(WxOrderItemDO::getOrderRecordGuid, recordGuid)
				.select(WxOrderItemDO::getGuid, WxOrderItemDO::getItemName).list();
		if (CollectionUtils.isEmpty(list)) {
			orderItemDTO.setState(1);
		} else {
			List<String> itemNames = orderItemDTO.getItemNames();
			if (itemNames == null) {
				itemNames = new ArrayList<>();
				orderItemDTO.setItemNames(itemNames);
			}

		}
		return true;
	}

	private boolean assembleNotOpenTable(WxStoreUserOrderItemDTO orderItemDTO, String recordGuid, boolean isLogin) {
		List<WxOrderItemDO> list = orderItemService.lambdaQuery().eq(WxOrderItemDO::getOrderRecordGuid, recordGuid)
				.select(WxOrderItemDO::getItemName, WxOrderItemDO::getOriginalPrice, WxOrderItemDO::getMemberPrice)
				.list();
		if (ObjectUtils.isEmpty(list)) {
			return false;
		}
		BigDecimal originPrice = BigDecimal.ZERO;
		BigDecimal memberPrice = BigDecimal.ZERO;
		for (WxOrderItemDO wxOrderItemDO : list) {
			originPrice = originPrice.add(wxOrderItemDO.getOriginalPrice());
			if (isLogin) {
				memberPrice = memberPrice.add(wxOrderItemDO.getMemberPrice());
			}
		}
		orderItemDTO.setActuallyPayFee(originPrice);
		if (memberPrice.compareTo(BigDecimal.ZERO) > 0) {
			orderItemDTO.setUseDisCount(1);
			orderItemDTO.setDisCountFee(memberPrice);
			orderItemDTO.setActuallyPayFee(orderItemDTO.getActuallyPayFee().subtract(orderItemDTO.getDisCountFee()));
		}
		orderItemDTO.setItemNames(list.stream().map(WxOrderItemDO::getItemName).collect(Collectors.toList()));
		return true;
	}

	private List<WxStoreUserOrderItemDTO> filterFinshOrderList(String recordGuid, BrandStoreDetailDTO brandStoreDetail) {
		List<WxOrderRecordDO> wxOrderRecordlist = list(new LambdaQueryWrapper<WxOrderRecordDO>()
				.eq(WxOrderRecordDO::getUserRecordGuid, recordGuid)
				.eq(WxOrderRecordDO::getStoreGuid, WeixinUserThreadLocal.getStoreGuid())
				.in(WxOrderRecordDO::getOrderState, listFinishState()));
		log.info("我的微信门店订单:{}", wxOrderRecordlist);
		if(CollUtil.isEmpty(wxOrderRecordlist)){
			return Collections.emptyList();
		}
		List<WxOrderRecordDO> merchantOrderList = wxOrderRecordlist.stream()
				.filter(x -> !StringUtils.isEmpty(x.getOrderGuid()) && x.getOrderGuid().length() > 8 && x.getOrderState() != 7)
				.collect(Collectors.toList());
		log.info("我的订单：商户订单:{}", merchantOrderList);
		SingleListDTO singleListDTO = new SingleListDTO();
		singleListDTO.setList(merchantOrderList.stream().map(WxOrderRecordDO::getOrderGuid).collect(Collectors.toList()));
		if(CollUtil.isEmpty(singleListDTO.getList())){
			return Collections.emptyList();
		}
		List<DineinOrderDetailRespDTO> orderDetailList = wxStoreWeChatOrderClientService.getOrderDetailList(singleListDTO);
		log.info("我的订单:批量订单:{}", JacksonUtils.writeValueAsString(orderDetailList));
		if(CollUtil.isEmpty(orderDetailList)){
			return Collections.emptyList();
		}
		Map<String, DineinOrderDetailRespDTO> orderDetailMap = orderDetailList.stream()
				.collect(Collectors.toMap(DineinOrderDetailRespDTO::getGuid, Function.identity()));
		List<WxStoreUserOrderItemDTO> collect = merchantOrderList.stream().map(x -> {
			DineinOrderDetailRespDTO orderDetailRespDTO = orderDetailMap.get(x.getOrderGuid());
			if (orderDetailRespDTO == null) {
				return null;
			}
			WxStoreUserOrderItemDTO orderItemDTO = new WxStoreUserOrderItemDTO();
			orderItemDTO.setGuid(x.getGuid());
			orderItemDTO.setTradeMode(x.getOrderMode());
			orderItemDTO.setStoreName(brandStoreDetail.getStoreName());
			orderItemDTO.setBrandName(brandStoreDetail.getBrandName());
			orderItemDTO.setLogUrl(brandStoreDetail.getBrandLogoUrl());
			orderItemDTO.setCheckoutTime(x.getGmtCreate());
			orderItemDTO.setState(OrderUtils.tradeStateTransition(orderDetailRespDTO.getState()));
			if (orderItemDTO.getState() == null) {
				orderItemDTO.setState(OrderUtils.orderStateTransition(x.getOrderState()));
			}
			cannelFastStatue(orderItemDTO);
			orderItemDTO.setCheckoutTime(x.getGmtCreate());
			setStoreOrderPrice(orderDetailRespDTO, orderItemDTO);
			List<DineInItemDTO> dineInItemDTOS = orderDetailRespDTO.getDineInItemDTOS();
			if (!ObjectUtils.isEmpty(dineInItemDTOS)) {
				orderItemDTO.setItemNames(dineInItemDTOS.stream()
						.filter(k->k.getParentItemGuid()==0)
						.map(DineInItemDTO::getItemName)
						.collect(Collectors.toList()));
			} else {
				return null;
			}
			return orderItemDTO;
		}).filter(Objects::nonNull).collect(Collectors.toList());
		log.info("我的订单：转换：商户订单:{}", collect);
		return collect;
	}

	private List<Integer> listFinishState(){
		return Lists.newArrayList(WxOrderStateEnum.PAID.getCode(),
				WxOrderStateEnum.CANCELLED.getCode(),
				WxOrderStateEnum.UNPAID.getCode(),
				WxOrderStateEnum.COMPLETED.getCode(),
				WxOrderStateEnum.REJECTED.getCode());
	}

	private void cannelFastStatue(WxStoreUserOrderItemDTO orderItemDTO) {
		if (!orderItemDTO.getTradeMode().equals(1)) {
			return;
		}
		if (!orderItemDTO.getState().equals(1)) {
			return;
		}
		LocalDateTime gmtCreate = orderItemDTO.getCheckoutTime();
		LocalDateTime now = LocalDateTime.now();
		Duration dur = java.time.Duration.between(gmtCreate, now);
		long diff = dur.toMillis();
		if (diff >= 1000 * 60 * 15) {
			orderItemDTO.setState(3);
		}
	}

	/**
	 * 设置订单价格
	 *
	 * @param orderDetailRespDTO 订单
	 * @param orderItemDTO       商品
	 */
	private void setStoreOrderPrice(DineinOrderDetailRespDTO orderDetailRespDTO, WxStoreUserOrderItemDTO orderItemDTO) {
		BigDecimal actuallyPayFee = orderDetailRespDTO.getActuallyPayFee();
		// 判断订单状态
		if (!Objects.equals(StateEnum.SUCCESS.getCode(), orderDetailRespDTO.getState())) {
			// 非支付成功
			actuallyPayFee = orderDetailRespDTO.getOrderFee().subtract(orderDetailRespDTO.getDiscountFee());
		}
		orderItemDTO.setOriginalFee(orderDetailRespDTO.getOrderFee());
		orderItemDTO.setActuallyPayFee(actuallyPayFee);
		if (orderDetailRespDTO.getDiscountFee().compareTo(BigDecimal.ZERO) > 0) {
			orderItemDTO.setUseDisCount(1);
			orderItemDTO.setDisCountFee(orderDetailRespDTO.getDiscountFee());
		}
	}

	@Override
	public String getMerchantOrderGuid(String orderRecordGuid) {
		return Optional.ofNullable(getById(orderRecordGuid)).map(WxOrderRecordDO::getOrderGuid).orElse(null);
	}

	@Override
	public WxOrderRecordDO getByOrderGuid(String orderGuid) {
		return lambdaQuery().eq(WxOrderRecordDO::getOrderGuid, orderGuid).one();
	}

	@Override
	public IPage<WxStoreUserOrderItemDTO> userStorePage(Integer pageNo, Integer pageSize) {
		WxUserRecordDO user = wxUserRecordService.getOneByOpenId(WeixinUserThreadLocal.getOpenId());
		if (user == null) {
			log.info("用户不存在:{}", WeixinUserThreadLocal.get());
			return emptyOrder();
		}
		IPage<WxOrderRecordDO> page = new Page<>(pageNo, pageSize, false);
		LambdaQueryWrapper<WxOrderRecordDO> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(WxOrderRecordDO::getUserRecordGuid, user.getGuid());
		wrapper.eq(WxOrderRecordDO::getStoreGuid, WeixinUserThreadLocal.getStoreGuid());
		wrapper.and(x -> x.isNotNull(WxOrderRecordDO::getMerchantGuid).or().isNotNull(WxOrderRecordDO::getOrderGuid));
		wrapper.orderByDesc(WxOrderRecordDO::getGmtCreate);
		IPage<WxOrderRecordDO> wxOrderRecordDOIPage = page(page, wrapper);
		if (wxOrderRecordDOIPage != null) {
			List<WxOrderRecordDO> records = wxOrderRecordDOIPage.getRecords();
			if (!ObjectUtils.isEmpty(records)) {
				List<WxStoreUserOrderItemDTO> collect = records.stream().map(x -> {
					WxStoreUserOrderItemDTO orderItemDTO = new WxStoreUserOrderItemDTO();
					orderItemDTO.setGuid(x.getGuid());
					orderItemDTO.setActuallyPayFee(x.getActuallyPayFee());
					orderItemDTO.setBrandName(x.getBrandName());
					orderItemDTO.setCheckoutTime(x.getGmtCreate());
					orderItemDTO.setDisCountFee(BigDecimal.ZERO);
					orderItemDTO.setLogUrl(x.getLogUrl());
					orderItemDTO.setState(OrderUtils.orderStateTransition(x.getOrderState()));
					String itemName = x.getItemName();
					orderItemDTO.setItemNames(StringUtils.isEmpty(itemName) ? Collections.emptyList() : Arrays.asList(itemName.split(",")));
					orderItemDTO.setTradeMode(x.getOrderMode());
					orderItemDTO.setStoreName(x.getStoreName());
					return orderItemDTO;
				}).collect(Collectors.toList());
				IPage<WxStoreUserOrderItemDTO> itemDTOIPage = new Page<>();
				itemDTOIPage.setRecords(collect);
				itemDTOIPage.setSize(wxOrderRecordDOIPage.getSize());
				itemDTOIPage.setCurrent(wxOrderRecordDOIPage.getCurrent());
				return itemDTOIPage;
			}
		}
		return emptyOrder();
	}

	private Page<WxStoreUserOrderItemDTO> emptyOrder() {
		Page<WxStoreUserOrderItemDTO> wxOrderRecordDOIPage = new Page<>();
		wxOrderRecordDOIPage.setCurrent(1);
		wxOrderRecordDOIPage.setTotal(0);
		wxOrderRecordDOIPage.setSize(10);
		wxOrderRecordDOIPage.setRecords(Collections.emptyList());
		return wxOrderRecordDOIPage;
	}

	@Override
	public Boolean dealFastOrderTimeOut(String guid) {
		WxOrderRecordDO wxOrderRecordDO = getById(guid);
		log.info("快餐超时取消订单:{}", wxOrderRecordDO);
		if (wxOrderRecordDO == null) {
			log.error("订单不存在:{},企业:{}", guid, UserContextUtils.get());
			return true;
		}
		WxUserRecordDO user = wxUserRecordService.getById(wxOrderRecordDO.getUserRecordGuid());
		if (user == null || StringUtils.isEmpty(wxOrderRecordDO.getOrderGuid())) {
			log.info("用户不存在:{},企业:{}", wxOrderRecordDO, UserContextUtils.get());
			wxOrderRecordDO.setOrderState(3);
			wxOrderRecordDO.setUserRecordGuid("");
			wxOrderRecordDO.setOrderStateName("已取消");
			return updateById(wxOrderRecordDO);
		}
		OrderWechatDTO orderWechatDTO = wxStoreWeChatOrderClientService.getOrder(wxOrderRecordDO.getOrderGuid());
		if (orderWechatDTO == null) {
			log.info("查询订单状态失败:{}", wxOrderRecordDO);
			wxOrderRecordDO.setOrderState(3);
			wxOrderRecordDO.setUserRecordGuid("");
			wxOrderRecordDO.setOrderStateName("已取消");
			return updateById(wxOrderRecordDO);
		}
		if (orderWechatDTO.getState().equals(2)
				|| orderWechatDTO.getState().equals(3)
				|| orderWechatDTO.getState().equals(5)
		) {
			log.info("订单不是待支付,recordguid:{}", guid);
			return true;
		}


		CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
		cancelOrderReqDTO.setTable(false);
		cancelOrderReqDTO.setFastFood(true);
		cancelOrderReqDTO.setReason("过期做废");
		cancelOrderReqDTO.setUserGuid(user.getOpenId());
		cancelOrderReqDTO.setUserName(user.getNickName());
		cancelOrderReqDTO.setOrderGuid(wxOrderRecordDO.getOrderGuid());
		cancelOrderReqDTO.setDeviceId(user.getOpenId());
		cancelOrderReqDTO.setDeviceType(orderWechatDTO.getDeviceType());
		cancelOrderReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
		cancelOrderReqDTO.setStoreGuid(wxOrderRecordDO.getStoreGuid());
		log.info("快餐作废入参:{}", cancelOrderReqDTO);
		//1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废
		int state = orderWechatDTO.getState().intValue();
		String stateName = "";
		int orderRecordState = 0;
		if (state == 4) {
			orderRecordState = 2;
			stateName = "已支付";
		} else if (state == 6) {
			orderRecordState = 3;
			stateName = "已取消";
		} else if (state == 1) {
			Boolean cancel = wxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO);
			log.info("订单作废,guid:{},orderguid:{},res:{}", wxOrderRecordDO.getGuid(), wxOrderRecordDO.getOrderGuid(), cancel);
			if (!cancel) {
				return false;
			}
			orderRecordState = 3;
			stateName = "已取消";
		}
		wxOrderRecordDO.setOrderState(orderRecordState);
		wxOrderRecordDO.setOrderStateName(stateName);
		wxOrderRecordDO.setUserRecordGuid(user.getGuid());
		boolean update = updateById(wxOrderRecordDO);
		log.info("快餐作废成功更新结果:{}", update);
		LambdaUpdateWrapper<WxStoreMerchantOrderDO> wrapper = Wrappers.<WxStoreMerchantOrderDO>lambdaUpdate()
				.eq(WxStoreMerchantOrderDO::getOrderRecordGuid, wxOrderRecordDO.getGuid())
				.set(WxStoreMerchantOrderDO::getOrderState, 2);
		boolean update1 = wxStoreMerchantOrderService.update(wrapper);
		log.info("快餐作废成功批次更新结果:{}", update1);
		return update1;
	}

	@Override
	public void initialRecord(WxQrCodeInfoDO wxQrCodeInfoDO) {
		WxMemberSessionDTO wxMemberSessionDTO = new WxMemberSessionDTO();
		wxMemberSessionDTO.setDiningTableGuid(wxQrCodeInfoDO.getTableGuid());
		//wxMemberSessionDTO.setDiningTableCode(wxQrCodeInfoDO.gett());
		wxMemberSessionDTO.setAreaGuid(wxQrCodeInfoDO.getAreaGuid());
		wxMemberSessionDTO.setAreaName(wxQrCodeInfoDO.getAreaName());
		wxMemberSessionDTO.setStoreGuid(wxQrCodeInfoDO.getStoreGuid());
		wxMemberSessionDTO.setStoreName(wxQrCodeInfoDO.getStoreName());
		wxMemberSessionDTO.setBrandGuid(wxQrCodeInfoDO.getBrandGuid());
		//wxMemberSessionDTO.setBrandName(wxQrCodeInfoDO.getBrandName());
		initialRecord(wxMemberSessionDTO);
	}


	@Override
	public String initialRecord(WxMemberSessionDTO wxMemberSessionDTO) {
		String newOrderGuid = redisUtils.generatdDTOGuid(WxOrderRecordDO.class);
		String lockKey = CacheName.ORDER_RECORD_GUID + ":" + wxMemberSessionDTO.getDiningTableGuid();
		if (!redisUtils.setNx(lockKey, newOrderGuid, 60)) {
			return String.valueOf(redisUtils.get(lockKey));
		}
		try {
			log.info("locksuccess");
			List<WxOrderRecordDO> outStandingOrders = getOutStandingOrders(wxMemberSessionDTO.getDiningTableGuid());
			if (!ObjectUtils.isEmpty(outStandingOrders)) {
				WxOrderRecordDO wxOrderRecordDO = outStandingOrders.get(0);
				String orderGuid = wxOrderRecordDO.getOrderGuid();
				if (!StringUtils.isEmpty(orderGuid)) {
					//trade 获取订单状态
					OrderWechatDTO order = wxStoreDineInOrderClientService.getOrder(orderGuid);
					if (order == null) {
						return wxOrderRecordDO.getGuid();
					}
					//1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废
					Integer state = order.getState();
					if (state == 1 || state == 12) {
						log.info("getOutStandingOrders,wxOrderRecordDO:{}", wxOrderRecordDO);
						return wxOrderRecordDO.getGuid();
					} else {
						WxOrderRecordDO orderRecordUpdate = new WxOrderRecordDO();
						orderRecordUpdate.setGuid(wxOrderRecordDO.getGuid());
						//TODO
						orderRecordUpdate.setOrderState(state);
						this.updateById(orderRecordUpdate);
					}
				} else {
					log.info("getOutStandingOrders,wxOrderRecordDO:{}", wxOrderRecordDO);
					return wxOrderRecordDO.getGuid();
				}
			}
			//todo order_type kbz?
			WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO();
			wxOrderRecordDO.setGuid(newOrderGuid);
			wxOrderRecordDO.setOrderState(0);
			wxOrderRecordDO.setOrderMode(0);
			wxOrderRecordDO.setTableGuid(wxMemberSessionDTO.getDiningTableGuid());
			wxOrderRecordDO.setTableCode(wxMemberSessionDTO.getDiningTableCode());
			wxOrderRecordDO.setAreaGuid(wxMemberSessionDTO.getAreaGuid());
			wxOrderRecordDO.setAreaName(wxMemberSessionDTO.getAreaName());
			wxOrderRecordDO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
			wxOrderRecordDO.setStoreName(wxMemberSessionDTO.getStoreName());
			wxOrderRecordDO.setBrandGuid(wxMemberSessionDTO.getBrandGuid());
			wxOrderRecordDO.setBrandName(wxMemberSessionDTO.getBrandName());
			wxOrderRecordDO.setLogUrl(wxMemberSessionDTO.getLogUrl());
			wxOrderRecordDO.setGmtCreate(LocalDateTime.now());
			wxOrderRecordDO.setGmtModified(LocalDateTime.now());
			wxOrderRecordDO.setOrderStateName("待确认");
//				wxOrderRecordDO.setOrderGuid(orderGuid);
			save(wxOrderRecordDO);
			log.info("save success,guid:{}", newOrderGuid);
		} catch (Exception e) {
			log.error("新建订单失败:{}", e);
		} finally {
			Boolean delete = redisUtils.delete(lockKey);
			log.info("解锁，lockKey：{},{}", lockKey, delete);
		}
		return newOrderGuid;
	}


	@Override
	public void payBackOrder(PayBackOrderRecordDTO payBackOrderRecordDTO) {
		WxUserRecordDO oneByOpenId = wxUserRecordService.getOneByOpenId(WeixinUserThreadLocal.getOpenId());
		if (oneByOpenId == null) {
			throw new BusinessException("用户不存在");
		}
		payBackOrderRecordDTO.setUserRecordGuid(oneByOpenId.getGuid());
		// 查询数据库原有订单
		String orderGuid = payBackOrderRecordDTO.getOrderGuid();
		if (StringUtils.isNotEmpty(orderGuid)) {
			WxOrderRecordDO orderRecordByOrderGuidDO = getByOrderGuid(orderGuid);
			if (Objects.nonNull(orderRecordByOrderGuidDO)) {
				payBackOrderRecordDTO.setGuid(orderRecordByOrderGuidDO.getGuid());
			}
		}
		WxOrderRecordDO orderRecordDO = WxOrderRecordMapStruct.INSTANCE.fromPayBackOrder(payBackOrderRecordDTO);
		saveOrUpdate(orderRecordDO);
		WxOrderRecordDO byId = getById(payBackOrderRecordDTO.getGuid());
		if (byId != null && Objects.equals(byId.getOrderMode(), 1)) {
			WxStoreMerchantOrderDTO wxStoreMerchantOrder = (WxStoreMerchantOrderDTO) redisUtils
					.get(CacheName.FAST_NOTIFY + ":" + payBackOrderRecordDTO.getOrderGuid());
			if (wxStoreMerchantOrder != null) {
				wxStoreMerchantOrderService.pushOrderMsg(wxStoreMerchantOrder);
			}
		}
	}

	/**
	 * 非会员推送模板消息
	 * @param orderDetails
	 * @param wxOrderRecordDO
	 * @param openId
	 */
	@Override
	public void sendUnMemberMessage(DineinOrderDetailRespDTO orderDetails, WxOrderRecordDO wxOrderRecordDO, String openId) {
		try {
			String url = String.format(weChatConfig.getMEMBER_ORDER_PAGE(), UserContextUtils.getEnterpriseGuid(),
					wxOrderRecordDO.getStoreGuid(), openId, wxOrderRecordDO.getGuid());
			WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO = WxMemberConsumeMsgDTO.builder()
					.brandGuid(wxOrderRecordDO.getBrandGuid()).openId(openId).orderGuid(wxOrderRecordDO.getGuid()).storeGuid(wxOrderRecordDO.getStoreGuid())
					.url(url).wxTemplateTypeEnum(WxTemplateTypeEnum.MEMBER_AMOUNT_CUSTOME_TEMPLATE)
					.first("您好！支付已成功！").remark("期待您再次光临").build();
			/**
			 * 订单号：显示支付成功的订单编号
			 消费金额：获取并显示本单支付金额
			 消费门店：显示所消费门店名称，显示一行，若一行显示不完用...代替
			 消费时间：获取并显示支付时间
			 */
			wxMemberConsumeMsgDTO.addKeywordValue(orderDetails.getOrderNo());
			wxMemberConsumeMsgDTO.addKeywordValue(orderDetails.getActuallyPayFee().toString());
			wxMemberConsumeMsgDTO.addKeywordValue(wxOrderRecordDO.getStoreName());
			wxMemberConsumeMsgDTO.addKeywordValue(orderDetails.getCheckoutTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")));
			log.info("非会员模板推送:{}",wxMemberConsumeMsgDTO);
			wxOpenMessageService.sendMemberMsgNew(wxMemberConsumeMsgDTO);
		} catch (Exception e) {
			log.info("非会员推送失败:{}",e);
		}
	}

	@Override
	public void sendUnMemberMessage(UnMemberMessageDTO unMemberMessageDTO) {
		String orderRecordGuid = unMemberMessageDTO.getOrderRecordGuid();
		WxOrderRecordDO byId = getById(orderRecordGuid);
		if (byId != null) {
			sendUnMemberMessage(unMemberMessageDTO.getOrderDetails(),byId,unMemberMessageDTO.getOpenId());
		}
	}

	@Override
	public WxStoreMerchantOrderDTO getMerchantOrderPhone(String orderGuid) {
		Assert.isTrue(StringUtils.isNotBlank(orderGuid),"请求参数为空！");
		WxStoreMerchantOrderDTO wxStoreMerchantOrder = new WxStoreMerchantOrderDTO();
		//取第一次提交订单
		LambdaQueryWrapper<WxOrderRecordDO> orderWrapper = new LambdaQueryWrapper<WxOrderRecordDO>()
				.eq(WxOrderRecordDO::getOrderGuid, orderGuid)
				.orderByAsc(WxOrderRecordDO::getGmtCreate).last("limit " + 1);
		WxOrderRecordDO wxOrderRecordDO = wxOrderRecordMapper.selectOne(orderWrapper);
		//联表查询没走索引 查询异常慢 做二次查询处理
		log.info("WxOrderRecordDO 查询出的订单信息:{}",JacksonUtils.writeValueAsString(wxOrderRecordDO));
		if (Objects.isNull(wxOrderRecordDO)){
			log.info("无相关订单信息！");
			return wxStoreMerchantOrder;
		}
		//取第一次提交订单
		LambdaQueryWrapper<WxStoreMerchantOrderDO> wrapper = new LambdaQueryWrapper<WxStoreMerchantOrderDO>()
				.eq(WxStoreMerchantOrderDO::getOrderRecordGuid, wxOrderRecordDO.getGuid())
				.orderByAsc(WxStoreMerchantOrderDO::getGmtCreate).last("limit " + 1);
		WxStoreMerchantOrderDO wxStoreMerchantOrderDO = wxStoreMerchantOrderMapper.selectOne(wrapper);
		log.info("wxStoreMerchantOrderDO 查询出的订单信息:{}",JacksonUtils.writeValueAsString(wxStoreMerchantOrderDO));
		if (ObjectUtil.isNull(wxStoreMerchantOrderDO)){
			log.info("无相关订单信息！");
			return wxStoreMerchantOrder;
		}
		wxStoreMerchantOrder = wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDO);
		return wxStoreMerchantOrder;
	}

	@Override
	public WxOrderRecordDO getByOrderHolderNo(String orderHolderNo) {
		LambdaQueryWrapper<WxOrderRecordDO> orderWrapper = new LambdaQueryWrapper<WxOrderRecordDO>()
				.eq(WxOrderRecordDO::getOrderHolderNo, orderHolderNo)
				.orderByDesc(WxOrderRecordDO::getGmtCreate)
				.last("limit 1");
		return getOne(orderWrapper);
	}

    /**
     * 快餐订单查微信订单
     *
     * @param query 订单号列表
     * @return 微信订单
     */
    @Override
    public List<WxStoreMerchantOrderDTO> listByOrderGuid(SingleDataDTO query) {
        // 检查查询参数是否为空
        if (ObjectUtils.isEmpty(query) || CollectionUtils.isEmpty(query.getDatas())) {
            log.warn("[快餐订单查微信订单]参数为空");
            return new ArrayList<>();
        }
        // 构建查询条件，查询相应的微信订单记录，并按创建时间升序排序
        LambdaQueryWrapper<WxOrderRecordDO> orderWrapper = new LambdaQueryWrapper<WxOrderRecordDO>()
                .in(WxOrderRecordDO::getOrderGuid, query.getDatas())
                .orderByAsc(WxOrderRecordDO::getGmtCreate);
        List<WxOrderRecordDO> recordDOList = wxOrderRecordMapper.selectList(orderWrapper);
        // 如果未查询到任何微信订单记录
        if (CollectionUtils.isEmpty(recordDOList)) {
            log.info("[快餐订单查微信订单]未查询到订单信息,orderGuidList={}", query.getDatas());
            return new ArrayList<>();
        }
        // 从微信订单记录中提取唯一的订单记录标识符，并去重
        List<String> recordGuidList = recordDOList.stream()
                .map(WxOrderRecordDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        // 根据订单记录标识符列表，查询相应的商家订单，并按创建时间升序排序
        LambdaQueryWrapper<WxStoreMerchantOrderDO> wrapper = new LambdaQueryWrapper<WxStoreMerchantOrderDO>()
                .in(WxStoreMerchantOrderDO::getOrderRecordGuid, recordGuidList)
                .orderByAsc(WxStoreMerchantOrderDO::getGmtCreate);
        List<WxStoreMerchantOrderDO> orderDOList = wxStoreMerchantOrderMapper.selectList(wrapper);
        // 记录查询到的商家订单信息
        log.info("[快餐订单查微信订单]查询出的订单信息={}", JacksonUtils.writeValueAsString(orderDOList));
        // 如果未查询到任何商家订单信息，则返回空列表
        if (CollectionUtils.isEmpty(orderDOList)) {
            return new ArrayList<>();
        }
        // 将商家订单数据转换为DTO格式，并返回
        return wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(orderDOList);
    }

	@Override
	public void updateOrderRecordState(WxUpdateOrderRecordStateReqDTO reqDTO) {
		if (Objects.isNull(reqDTO.getOrderGuid()) || Objects.isNull(reqDTO.getState())) {
			log.error("修改订单状态参数有误:{}", JacksonUtils.writeValueAsString(reqDTO));
			return;
		}
		WxOrderRecordDO wxOrderRecordDO = getByOrderGuid(String.valueOf(reqDTO.getOrderGuid()));
		if (Objects.isNull(wxOrderRecordDO)) {
			log.warn("微信订单不存在，跳过修改, orderGuid:{}", reqDTO.getOrderGuid());
			return;
		}
		wxOrderRecordDO.setOrderState(reqDTO.getState());
		wxOrderRecordDO.setOrderStateName(WxOrderStateEnum.getOrderStateName(wxOrderRecordDO.getOrderState()));
		updateById(wxOrderRecordDO);
	}

}
