package com.holderzone.saas.store.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxOrderConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderConfigDO;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreOrderConfigService
 * @date 2019/02/14 14:54
 * @description //TODO
 * @program holder-saas-store-weixin
 */
public interface WxStoreOrderConfigService extends IService<WxOrderConfigDO> {

    /**
     * 分页获取微信点餐门店配置列表
     *
     * @param wxStorePageReqDTO
     * @return
     */
    Page pageOrderConfig(WxStorePageReqDTO wxStorePageReqDTO);

    /**
     * 创建微信点餐门店配置
     *
     * @param wxStoreReqDTO
     * @return
     */
    Boolean saveStoreConfig(WxStoreReqDTO wxStoreReqDTO);

    /**
     * 获取微信点餐门店配置详情
     *
     * @param wxStoreReqDTO
     * @return
     */
    WxOrderConfigDTO getDetailConfig(WxStoreReqDTO wxStoreReqDTO);

    /**
     * 获取pad点餐背景图
     *
     * @param storeGuid 门店guid
     * @return 查询结果
     */
    String getPadBackgroundUrl(String storeGuid);

    /**
     * 修改微信点餐门店配置
     *
     * @param wxOrderConfigDTO
     * @return
     */
    Boolean updateStoreConfig(WxOrderConfigDTO wxOrderConfigDTO);

    /**
     * 批量修改微信点餐门店配置
     *
     * @param wxOrderConfigUpdateBatchReqDTO
     * @return
     */
    Boolean updateBatchStoreConfig(WxOrderConfigUpdateBatchReqDTO wxOrderConfigUpdateBatchReqDTO);


    Map<String, WxOrderConfigDO> mapOrderConfigDO(List<String> storeGuidList);

    WxOrderConfigDTO getStoreConfig(String storeGuid);
}
