package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreCartItemDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerShoppingCartDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreShoppingCartDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreConsumerCartItemReqDTO;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreShoppingCartMapStruct;
import com.holderzone.saas.store.weixin.service.WxStoreMenuShopCartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 微信购物车交互实现层
 * @className WxStoreMenuShopCartServiceImpl
 * @date 2019/3/1
 */
@Service
@Slf4j
public class WxStoreMenuShopCartServiceImpl implements WxStoreMenuShopCartService {

    private final RedisUtils redisUtils;
    private final WxStoreShoppingCartMapStruct wxStoreShoppingCartMapStruct;

    @Autowired
    public WxStoreMenuShopCartServiceImpl(RedisUtils redisUtils, WxStoreShoppingCartMapStruct wxStoreShoppingCartMapStruct) {
        this.redisUtils = redisUtils;
        this.wxStoreShoppingCartMapStruct = wxStoreShoppingCartMapStruct;
    }

    @Override
    public WxStoreShoppingCartDTO createShoppingCart(WxStoreConsumerDTO wxStoreConsumerDTO) {
        WxStoreShoppingCartDTO wxStoreShoppingCartDTO = initialWxStoreShoppingCart(wxStoreConsumerDTO);
        log.info("新用户购物车初始化: {}", wxStoreShoppingCartDTO);
        save(wxStoreConsumerDTO, wxStoreShoppingCartDTO);
        return wxStoreShoppingCartDTO;
    }

    /**
     * 将购物车保存到Redis中。
     *
     * @param wxStoreConsumerDTO     消费者信息。
     * @param wxStoreShoppingCartDTO 购物车数据。
     */
    public void save(WxStoreConsumerDTO wxStoreConsumerDTO, WxStoreShoppingCartDTO wxStoreShoppingCartDTO) {
        String redisKey = combineKey(wxStoreConsumerDTO);
        redisUtils.set(redisKey, wxStoreShoppingCartDTO);
    }

    /**
     * 初始化消费者的购物车。
     *
     * @param wxStoreConsumerDTO 消费者信息。
     * @return 初始化后的购物车。
     */
    private WxStoreShoppingCartDTO initialWxStoreShoppingCart(WxStoreConsumerDTO wxStoreConsumerDTO) {
        WxStoreShoppingCartDTO wxStoreShoppingCartDTO = wxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(wxStoreConsumerDTO);
        wxStoreShoppingCartDTO.setGuid(redisUtils.generatdDTOGuid(WxStoreShoppingCartDTO.class));
        wxStoreShoppingCartDTO.setGmtCreate(LocalDateTime.now());
        wxStoreShoppingCartDTO.setGmtUpdate(LocalDateTime.now());
        return wxStoreShoppingCartDTO;
    }

    @Override
    public WxStoreShoppingCartDTO getWxStoreShoppingCart(WxStoreConsumerDTO wxStoreConsumerDTO) {
        String redisKey = combineKey(wxStoreConsumerDTO);
        WxStoreShoppingCartDTO wxStoreShoppingCartDTO = (WxStoreShoppingCartDTO) redisUtils.get(redisKey);
        // 如果购物车不存在，则创建一个新的
        return wxStoreShoppingCartDTO != null ? wxStoreShoppingCartDTO : createShoppingCart(wxStoreConsumerDTO);
    }

    /**
     * 使用业务ID、门店ID和用户OpenID生成Redis键。
     *
     * @param wxStoreConsumerDTO 消费者信息。
     * @return 生成的Redis键。
     */
    private String combineKey(WxStoreConsumerDTO wxStoreConsumerDTO) {
        return redisUtils.keyGenerate(BusinessName.SHOPPING_CART, wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getOpenId());
    }

    @Override
    @CachePut(keyGenerator = "shoppingCartKeyGenerator")
    public WxStoreShoppingCartDTO updateWxStoreShoppingCart(WxStoreConsumerShoppingCartDTO wxStoreConsumerShoppingCartDTO) {
        WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreConsumerShoppingCartDTO.getWxStoreConsumerDTO();
        WxStoreShoppingCartDTO wxStoreShoppingCartDTO = wxStoreConsumerShoppingCartDTO.getWxStoreShoppingCartDTO();
        save(wxStoreConsumerDTO, wxStoreShoppingCartDTO);
        return wxStoreShoppingCartDTO;
    }

    @Override
    @CachePut(keyGenerator = "shoppingCartKeyGenerator")
    public WxStoreShoppingCartDTO createCartItem(WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO) {
        WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreConsumerCartItemReqDTO.getWxStoreConsumerDTO();
        WxStoreCartItemDTO wxStoreCartItemDTO = wxStoreConsumerCartItemReqDTO.getWxStoreCartItemDTO();
        WxStoreShoppingCartDTO wxStoreShoppingCart = getWxStoreShoppingCart(wxStoreConsumerDTO);
        wxStoreShoppingCart.getWxStoreCartItemDTOS().add(wxStoreCartItemDTO);
        save(wxStoreConsumerDTO, wxStoreShoppingCart);
        return wxStoreShoppingCart;
    }

    @Override
    @CachePut(keyGenerator = "shoppingCartKeyGenerator")
    public WxStoreShoppingCartDTO updateCartItem(WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO) {
        WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreConsumerCartItemReqDTO.getWxStoreConsumerDTO();
        WxStoreCartItemDTO wxStoreCartItemDTO = wxStoreConsumerCartItemReqDTO.getWxStoreCartItemDTO();
        WxStoreShoppingCartDTO wxStoreShoppingCart = getWxStoreShoppingCart(wxStoreConsumerDTO);
        List<WxStoreCartItemDTO> wxStoreCartItemDTOs = wxStoreShoppingCart.getWxStoreCartItemDTOS();
        // 替换现有的商品项
        wxStoreCartItemDTOs.replaceAll(item -> item.getItemGuid().equals(wxStoreCartItemDTO.getItemGuid()) ? wxStoreCartItemDTO : item);
        save(wxStoreConsumerDTO, wxStoreShoppingCart);
        return wxStoreShoppingCart;
    }

    @Override
    @CachePut(keyGenerator = "shoppingCartKeyGenerator")
    public WxStoreShoppingCartDTO delCartItem(WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO) {
        WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreConsumerCartItemReqDTO.getWxStoreConsumerDTO();
        WxStoreCartItemDTO wxStoreCartItemDTO = wxStoreConsumerCartItemReqDTO.getWxStoreCartItemDTO();
        WxStoreShoppingCartDTO wxStoreShoppingCart = getWxStoreShoppingCart(wxStoreConsumerDTO);
        wxStoreShoppingCart.getWxStoreCartItemDTOS().removeIf(item -> item.equals(wxStoreCartItemDTO));
        save(wxStoreConsumerDTO, wxStoreShoppingCart);
        return wxStoreShoppingCart;
    }
}
