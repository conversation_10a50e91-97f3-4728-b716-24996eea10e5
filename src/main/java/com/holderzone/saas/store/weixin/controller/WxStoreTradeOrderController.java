package com.holderzone.saas.store.weixin.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolumeDetails;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.SubmitReturnDTO;
import com.holderzone.saas.store.dto.weixin.WebSocketMessageDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxPaidOrderDetailsReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.weixin.annotation.DynamicData;
import com.holderzone.saas.store.weixin.annotation.TableToken;
import com.holderzone.saas.store.weixin.service.WxStoreOrderPayService;
import com.holderzone.saas.store.weixin.service.WxStoreTradeOrderService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberTerminalClientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreOrderController
 * @date 2019/3/6
 */
@RestController
@RequestMapping(value = "/wx_store_order_provide")
@Validated
@Api("微信订单实现层")
@Slf4j
public class WxStoreTradeOrderController {


    private final WxStoreTradeOrderService wxStoreTradeOrderService;

    private final WxStoreOrderPayService wxStoreOrderPayService;

	@Autowired
	private MemberTerminalClientService memberTerminalClientService;

    @Autowired
    public WxStoreTradeOrderController(WxStoreTradeOrderService wxStoreTradeOrderService, WxStoreOrderPayService wxStoreOrderPayService) {
        this.wxStoreTradeOrderService = wxStoreTradeOrderService;
        this.wxStoreOrderPayService = wxStoreOrderPayService;
    }

    @ApiOperation("下单:加菜,废弃20200510")
    @PostMapping("/submit")
	@TableToken(tableGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.diningTableGuid",storeGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid",enterpriseGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.enterpriseGuid")
	@DynamicData(enterpriseGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.enterpriseGuid",storeGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid")
    public SubmitReturnDTO submitOrder(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        log.info("下单入参wxStoreAdvanceConsumerReqDTO{}:", wxStoreAdvanceConsumerReqDTO);
        return wxStoreTradeOrderService.submitOrder(wxStoreAdvanceConsumerReqDTO);
    }

    @ApiOperation("获取订单详情")
    @PostMapping(value = "/table_details")
	@DynamicData(enterpriseGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.enterpriseGuid",storeGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid")
    public WebSocketMessageDTO tableOrderDetails(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        log.info("订单详情入参{}", JacksonUtils.writeValueAsString(wxStoreAdvanceConsumerReqDTO));
        return wxStoreTradeOrderService.getTableOrderDetails(wxStoreAdvanceConsumerReqDTO);
    }

	@ApiOperation("买单")
	@PostMapping(value = "/order_pay")
	@DynamicData(enterpriseGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.enterpriseGuid",storeGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid")
	public WxStoreTradeOrderDetailsRespDTO orderPay(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("买单入参wxStoreAdvanceConsumerReqDTO:{}", wxStoreAdvanceConsumerReqDTO);
		return wxStoreOrderPayService.orderPay(wxStoreAdvanceConsumerReqDTO);
	}

    @ApiOperation(value = "修改整单备注", notes = "修改整单备注")
    @PostMapping("/update_remark")
    public Boolean updateRemark(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        return wxStoreTradeOrderService.updateRemark(createDineInOrderReqDTO);
    }

    @ApiOperation(value = "修改就餐人数", notes = "修改就餐人数")
    @PostMapping("/update_guest_count")
    public Boolean updateGuestCount(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        return wxStoreTradeOrderService.updateGuestCount(createDineInOrderReqDTO);
    }

    @ApiOperation("获取会员卡或优惠券优惠")
	@PostMapping(value = "/validate_card")
	@DynamicData(enterpriseGuid = "#cardAndVolumeDiscountReqDTO.enterpriseGuid",storeGuid = "#cardAndVolumeDiscountReqDTO.storeGuid")
	public CardAndVolumeDTO validateCardAndVolume(@RequestBody CardAndVolumeDiscountReqDTO cardAndVolumeDiscountReqDTO) {
		log.info("获取当前会员卡或优惠券的优惠信息:{}",JacksonUtils.writeValueAsString(cardAndVolumeDiscountReqDTO));
		return wxStoreTradeOrderService.validateCardAndVolume(cardAndVolumeDiscountReqDTO);
	}

	@ApiOperation("会员卡列表")
	@PostMapping(value = "/member_card_list")
	@DynamicData(enterpriseGuid = "#wxMemberCardListReqDTO.enterpriseGuid",storeGuid = "#wxMemberCardListReqDTO.storeGuid")
	public WxMemberCardRespDTO cardList(@RequestBody WxMemberCardListReqDTO wxMemberCardListReqDTO) {
		log.info("获取用户的会员卡列表入参:{}",JacksonUtils.writeValueAsString(wxMemberCardListReqDTO));
		WxMemberCardRespDTO wxMemberCardRespDTO = wxStoreTradeOrderService.cardList(wxMemberCardListReqDTO);
		return wxMemberCardRespDTO;
	}

	@ApiModelProperty(value = "优惠券列表")
	@PostMapping(value = "/member_volume_list")
	@DynamicData(enterpriseGuid = "#wxVolumeCodeReqDTO.enterpriseGuid",storeGuid = "#wxVolumeCodeReqDTO.storeGuid")
	public WxVolumeCodeRespDTO volumeCodeList(@RequestBody WxVolumeCodeReqDTO wxVolumeCodeReqDTO) {
		log.info("优惠券列表入参:{}", JacksonUtils.writeValueAsString(wxVolumeCodeReqDTO));
		WxVolumeCodeRespDTO wxVolumeCodeRespDTO = wxStoreTradeOrderService.volumeCodeList(wxVolumeCodeReqDTO);
		return wxVolumeCodeRespDTO;
	}

	@ApiModelProperty(value = "优惠券详情")
	@PostMapping(value = "/volume_details")
	public ResponseMemberInfoVolumeDetails volumeCodeDetails(@RequestBody WxVolumeCodeDetailsReqDTO wxVolumeCodeDetailsReqDTO) {
		log.info("优惠券详情入参:{}", JacksonUtils.writeValueAsString(wxVolumeCodeDetailsReqDTO));
		return wxStoreTradeOrderService.volumeCodeDetails(wxVolumeCodeDetailsReqDTO);
	}

	@ApiModelProperty("结账后订单详情")
	@PostMapping(value = "/paid_order_details")
	@DynamicData(enterpriseGuid="#wxPaidOrderDetailsReqDTO.enterpriseGuid",storeGuid = "#wxPaidOrderDetailsReqDTO.storeGuid")
	public WebSocketMessageDTO orderDetails(@RequestBody WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO) {
    	log.info("结账后订单详情入参:{}",JacksonUtils.writeValueAsString(wxPaidOrderDetailsReqDTO));
		return wxStoreTradeOrderService.orderDetails(wxPaidOrderDetailsReqDTO);
	}

	@ApiOperation("异常测试2")
	@PostMapping("/exception")
	public String exceptionTest(String enterpriseGuid,String storeGuid,String openId){
		RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO = new RequestQueryStoreAndMemberAndCard();
		queryStoreAndMemberAndCardReqDTO.setEnterpriseGuid(enterpriseGuid);
		queryStoreAndMemberAndCardReqDTO.setStoreGuid(storeGuid);
		queryStoreAndMemberAndCardReqDTO.setPhoneNumOrCardNum(openId);
		UserInfoDTO userInfoDTO = new UserInfoDTO();
		userInfoDTO.setEnterpriseGuid(enterpriseGuid);
		userInfoDTO.setStoreGuid(storeGuid);
		userInfoDTO.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
		UserContextUtils.put(JacksonUtils.writeValueAsString(userInfoDTO));
		ResponseMemberAndCardInfoDTO memberInfoAndCardTwo = memberTerminalClientService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO);
		log.info("会员及当前门店卡列表详情:{}", JacksonUtils.writeValueAsString(memberInfoAndCardTwo));
		return null;
	}


}

