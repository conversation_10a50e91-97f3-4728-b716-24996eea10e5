package com.holderzone.saas.store.weixin.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.AttrSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubItemSkuSynRespDTO;
import com.holderzone.saas.store.dto.weixin.WebSocketMessageDTO;
import com.holderzone.saas.store.dto.weixin.WxSocketDistributionDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceEstimateDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderPriceDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreEstimateItem;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreAttrRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreSkuRespDTO;
import com.holderzone.saas.store.enums.weixin.WxDistributionEnum;
import com.holderzone.saas.store.enums.weixin.WxPageNumEnum;
import com.holderzone.saas.store.weixin.service.WxSocketMsgService;
import com.holderzone.saas.store.weixin.service.WxStoreAdvanceOrderService;
import com.holderzone.saas.store.weixin.service.WxStoreMenuDetailsService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreEstimateClientService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 微信门店预订单实现层
 * @className WxStoreAdvanceOrderServiceImpl
 * @date 2019/3/18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WxStoreAdvanceOrderServiceImpl implements WxStoreAdvanceOrderService {

    private final RedisUtils redisUtils;

    private final WxStoreMenuDetailsService wxStoreMenuDetailsService;

    private final WxStoreSessionDetailsService wxStoreSessionDetailsService;

    private final WxSocketMsgService wxSocketMsgService;

    private final WxStoreEstimateClientService wxStoreEstimateClientService;

    @Override
    public WxStoreAdvanceEstimateDTO createAdvanceOrder(WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO) {
        log.info("创建预订单: {}", wxStoreAdvanceOrderDTO);
        // 设置当前操作的用户上下文
        dynamicMethod(wxStoreAdvanceOrderDTO.getWxStoreAdvanceConsumerReqDTO());
        // 检查订单是否可以进行估算
        WxStoreAdvanceEstimateDTO estimateDTO = checkEstimate(wxStoreAdvanceOrderDTO);

        // 如果估算失败，处理订单存储
        if (Boolean.FALSE.equals(estimateDTO.getEstimateResult())) {
            handleOrderStorage(wxStoreAdvanceOrderDTO);
        }
        return estimateDTO;
    }

    private void handleOrderStorage(WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO) {
        WxStoreAdvanceConsumerReqDTO consumerReqDTO = wxStoreAdvanceOrderDTO.getWxStoreAdvanceConsumerReqDTO();
        String redisKey = combineKey(consumerReqDTO);

        // 判断订单类型并进行相应存储
        if (Boolean.TRUE.equals(wxStoreMenuDetailsService.judgeOrderType(consumerReqDTO))) {
            WxStoreAdvanceOrderDTO existingOrder = (WxStoreAdvanceOrderDTO) redisUtils.hGet(redisKey, consumerReqDTO.getWxStoreConsumerDTO().getOpenId());
            if (!ObjectUtils.isEmpty(existingOrder)) {
                wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(existingOrder.getWxStoreAdvanceConsumerReqDTO());
            }
            redisUtils.hPut(redisKey, consumerReqDTO.getWxStoreConsumerDTO().getOpenId(), wxStoreAdvanceOrderDTO);
            sendSocketMessage(consumerReqDTO, WxDistributionEnum.TABLE_MULTI);
        } else {
            redisUtils.set(redisKey, wxStoreAdvanceOrderDTO);
        }
        // 设置存储订单的过期时间
        redisUtils.expire(redisKey, 2, TimeUnit.HOURS);
    }

    private void sendSocketMessage(WxStoreAdvanceConsumerReqDTO consumerReqDTO, WxDistributionEnum distributionEnum) {
        // 构建并发送WebSocket消息
        wxSocketMsgService.distribute(WxSocketDistributionDTO.builder()
                .distribution(distributionEnum.getCode())
                .tableGuid(consumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid())
                .openId(consumerReqDTO.getWxStoreConsumerDTO().getOpenId())
                .content(JacksonUtils.writeValueAsString(WebSocketMessageDTO.<String>builder()
                        .isJump(0)
                        .type(WxPageNumEnum.ADVANCE.getCode())
                        .content("String")
                        .build()))
                .build());
    }

    @Override
    public WxStoreAdvanceEstimateDTO checkEstimate(WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO) {
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(wxStoreAdvanceOrderDTO.getWxStoreAdvanceConsumerReqDTO().getWxStoreConsumerDTO().getStoreGuid());

        // 从客户端服务查询估算结果
        List<ItemEstimateForAndroidRespDTO> estimates = wxStoreEstimateClientService.queryEstimateForSyn(baseDTO);
        log.info("估算结果: {}", estimates);

        LinkedList<WxStoreEstimateItem> estimateItems = new LinkedList<>();
        if (!ObjectUtils.isEmpty(estimates)) {
            estimateItems = checkEstimateItems(wxStoreAdvanceOrderDTO, estimates);
        }

        // 准备并返回估算DTO
        WxStoreAdvanceEstimateDTO estimateDTO = new WxStoreAdvanceEstimateDTO();
        estimateDTO.setEstimateResult(!estimateItems.isEmpty());
        estimateDTO.setWxStoreEstimateItemList(estimateItems);
        return estimateDTO;
    }


    private LinkedList<WxStoreEstimateItem> checkEstimateItems(WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO, List<ItemEstimateForAndroidRespDTO> estimates) {
        // 过滤售罄的商品并检查每个订单项的估算
        return estimates.stream()
                .filter(estimate -> estimate.getIsSoldOut() == 2)
                .flatMap(estimate -> wxStoreAdvanceOrderDTO.getItemList().stream()
                        .map(item -> checkItemEstimate(item, estimate.getSkuGuid())))
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(LinkedList::new));
    }

    private WxStoreEstimateItem checkItemEstimate(WxStoreItemRespDTO item, String skuGuid) {
        // 检查订单项是否符合估算条件
        if (item.getSkuList().stream().anyMatch(sku -> sku.getUserck() == 1 && skuGuid.equals(sku.getSkuGuid()))) {
            log.info("非套餐估算结果: {}", item.getName());
            return new WxStoreEstimateItem(skuGuid, item.getName(), item.getName(), item.getItemGuid());
        }
        return null;
    }

    @Override
    public Boolean delAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        // 设置用户上下文
        dynamicMethod(wxStoreAdvanceConsumerReqDTO);
        String redisKey = combineKey(wxStoreAdvanceConsumerReqDTO);

        // 判断订单类型并删除相应的订单
        if (Boolean.TRUE.equals(wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO))) {
            redisUtils.hDelete(redisKey, wxStoreAdvanceConsumerReqDTO.getNopenId());
            sendSocketMessage(wxStoreAdvanceConsumerReqDTO, WxDistributionEnum.TABLE_EXCEPT_CURRENT);
            log.info("删除用户订单: {}", wxStoreAdvanceConsumerReqDTO);
            return true;
        } else {
            return redisUtils.delete(redisKey);
        }
    }

    @Override
    public Boolean delAdvanceTableOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        dynamicMethod(wxStoreAdvanceConsumerReqDTO);
        String redisKey = combineKey(wxStoreAdvanceConsumerReqDTO);
        return redisUtils.delete(redisKey);
    }

    @Override
    public WxStoreAdvanceOrderPriceDTO getPersonWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        // 设置用户上下文
        dynamicMethod(wxStoreAdvanceConsumerReqDTO);
        log.info("获取用户订单信息: {}", wxStoreAdvanceConsumerReqDTO);

        // 更新OpenId
        if (StringUtils.isNotBlank(wxStoreAdvanceConsumerReqDTO.getNopenId())) {
            wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().setOpenId(wxStoreAdvanceConsumerReqDTO.getNopenId());
        }

        String redisKey = combineKey(wxStoreAdvanceConsumerReqDTO);
        List<WxStoreAdvanceOrderDTO> orders = fetchOrders(wxStoreAdvanceConsumerReqDTO, redisKey);

        // 构建订单价格DTO
        WxStoreAdvanceOrderPriceDTO orderPriceDTO = new WxStoreAdvanceOrderPriceDTO();
        orderPriceDTO.setWxStoreAdvanceOrderDTOS(orders);
        log.info("获取的用户订单信息: {}", orderPriceDTO);
        return orderPriceDTO;
    }

    private List<WxStoreAdvanceOrderDTO> fetchOrders(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO, String redisKey) {
        List<WxStoreAdvanceOrderDTO> orders = new ArrayList<>();
        WxStoreAdvanceOrderDTO order;

        // 根据订单类型获取订单
        if (Boolean.TRUE.equals(wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO))) {
            order = (WxStoreAdvanceOrderDTO) redisUtils.hGet(redisKey, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
        } else {
            order = (WxStoreAdvanceOrderDTO) redisUtils.get(redisKey);
        }

        if (!ObjectUtils.isEmpty(order)) {
            orders.add(order);
        }
        return orders;
    }

    @Override
    public WxStoreAdvanceOrderPriceDTO getTableWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        // 设置用户上下文
        dynamicMethod(wxStoreAdvanceConsumerReqDTO);

        // 获取桌台的预订单
        List<WxStoreAdvanceOrderDTO> orders = getTableWxStoreAdvanceOrders(wxStoreAdvanceConsumerReqDTO);
        orders = ObjectUtils.isEmpty(orders) ? Collections.emptyList() : orders;

        // 构建订单价格DTO
        WxStoreAdvanceOrderPriceDTO orderPriceDTO = new WxStoreAdvanceOrderPriceDTO();
        orderPriceDTO.setWxStoreAdvanceOrderDTOS(orders);
        orderPriceDTO.setTotalPrice(getTotalPrice(orders));
        orderPriceDTO.setTotalRemark(Optional.ofNullable(getAdvanceOrderRemark(wxStoreAdvanceConsumerReqDTO)).orElse(""));

        // 判断顾客数量
        judgeGuestsCount(wxStoreAdvanceConsumerReqDTO, orderPriceDTO);

        Integer dinnerGuestsCount = wxStoreSessionDetailsService.getDinnerGuestsCount(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
        if (!ObjectUtils.isEmpty(dinnerGuestsCount)) {
            orderPriceDTO.setTradeOrderGuid("orderGuid");
        }
        return orderPriceDTO;
    }

    private void judgeGuestsCount(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO, WxStoreAdvanceOrderPriceDTO orderPriceDTO) {
        // 获取顾客数量并设置到订单价格DTO中
        String key = BusinessName.GUESTS_COUNT + ":" + wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid() + ":" + wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid();
        Integer guestsCount = (Integer) redisUtils.get(key);
        if (!ObjectUtils.isEmpty(guestsCount)) {
            orderPriceDTO.setTradeOrderGuid(Integer.toString(guestsCount));
        }
    }

    @Override
    public List<WxStoreAdvanceOrderDTO> getTableWxStoreAdvanceOrders(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        // 设置用户上下文
        dynamicMethod(wxStoreAdvanceConsumerReqDTO);

        String redisKey = combineKey(wxStoreAdvanceConsumerReqDTO);
        if (Boolean.TRUE.equals(wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO))) {
            return redisUtils.hValues(redisKey);
        } else {
            WxStoreAdvanceOrderDTO order = (WxStoreAdvanceOrderDTO) redisUtils.get(redisKey);
            return ObjectUtils.isEmpty(order) ? Collections.emptyList() : Collections.singletonList(order);
        }
    }

    @Override
    public WxStoreAdvanceOrderDTO getSingleWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        // 获取单个预订单
        String redisKey = combineKey(wxStoreAdvanceConsumerReqDTO);
        if (Boolean.TRUE.equals(wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO))) {
            List<WxStoreAdvanceOrderDTO> orders = redisUtils.hValues(redisKey);
            if (ObjectUtils.isEmpty(orders)) {
                throw new BusinessException("No orders found for the current user");
            }
            return orders.get(0);
        } else {
            return (WxStoreAdvanceOrderDTO) redisUtils.get(redisKey);
        }
    }

    @Override
    public BigDecimal getTotalPrice(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        List<WxStoreAdvanceOrderDTO> orders = getTableWxStoreAdvanceOrders(wxStoreAdvanceConsumerReqDTO);
        return getTotalPrice(orders);
    }

    @Override
    public BigDecimal getTotalPrice(List<WxStoreAdvanceOrderDTO> orders) {
        return orders.stream()
                .filter(Objects::nonNull)
                .flatMap(order -> order.getItemList().stream())
                .map(this::calculateItemPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateItemPrice(WxStoreItemRespDTO item) {
        BigDecimal skuPrice = item.getSkuList().stream()
                .filter(sku -> sku.getUserck() == 1)
                .map(WxStoreSkuRespDTO::getSalePrice)
                .findFirst()
                .orElse(BigDecimal.ZERO);

        BigDecimal attrPrice = item.getAttrGroupList().stream()
                .flatMap(group -> group.getAttrList().stream())
                .filter(attr -> attr.getUserck() == 1)
                .map(WxStoreAttrRespDTO::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal subTotal = calculateSubTotal(item);

        BigDecimal itemPrice = skuPrice.add(attrPrice).add(subTotal).multiply(item.getCurrentCount());
        item.setItemPrice(itemPrice);
        return itemPrice;
    }

    private BigDecimal calculateSubTotal(WxStoreItemRespDTO item) {
        if (item.getItemType() != 1 || item.getIsFixPkg() != 0) {
            return BigDecimal.ZERO;
        }

        return item.getSubgroupList().stream()
                .flatMap(group -> group.getSubItemSkuList().stream())
                .filter(subItem -> subItem.getDefaultNum() > 0)
                .map(subItem -> {
                    BigDecimal attrPriceSum = getSubItemAttrPriceTotal(subItem);
                    BigDecimal addPrice = subItem.getAddPrice().multiply(BigDecimal.valueOf(subItem.getDefaultNum()));
                    return addPrice.add(attrPriceSum);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getSubItemAttrPriceTotal(SubItemSkuSynRespDTO subItem) {
        return subItem.getAttrGroupList().stream()
                .flatMap(group -> group.getAttrList().stream())
                .filter(attr -> attr.getIsDefault() == 1)
                .map(AttrSynRespDTO::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getAttrTotalPrice(WxStoreItemRespDTO item) {
        return calculateSubTotal(item);
    }

    @Override
    public String getAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        dynamicMethod(wxStoreAdvanceConsumerReqDTO);
        String redisKey = remarkCombineKey(wxStoreAdvanceConsumerReqDTO);
        return (String) redisUtils.get(redisKey);
    }

    @Override
    public void updateAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        dynamicMethod(wxStoreAdvanceConsumerReqDTO);
        String redisKey = remarkCombineKey(wxStoreAdvanceConsumerReqDTO);
        redisUtils.set(redisKey, wxStoreAdvanceConsumerReqDTO.getOrderRemark());
        log.info("Updated order remark: {}", wxStoreAdvanceConsumerReqDTO.getOrderRemark());
    }

    @Override
    public void delAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        dynamicMethod(wxStoreAdvanceConsumerReqDTO);
        String redisKey = remarkCombineKey(wxStoreAdvanceConsumerReqDTO);
        redisUtils.delete(redisKey);
    }

    private String combineKey(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        String storeGuid = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid();
        if (Boolean.TRUE.equals(wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO))) {
            return redisUtils.keyGenerate(BusinessName.ADVANCE_ORDER, storeGuid, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
        } else {
            return redisUtils.keyGenerate(BusinessName.ADVANCE_ORDER, storeGuid, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
        }
    }

    private String remarkCombineKey(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        String storeGuid = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid();
        if (Boolean.TRUE.equals(wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO))) {
            return redisUtils.keyGenerate(BusinessName.ADVANCE_REMARK, storeGuid, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
        } else {
            return redisUtils.keyGenerate(BusinessName.ADVANCE_REMARK, storeGuid, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
        }
    }

    /**
     * 设置用户上下文
     *
     * @param wxStoreAdvanceConsumerReqDTO 预订单消费者请求DTO
     */
    private void dynamicMethod(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        // 从请求DTO中获取用户信息
        EnterpriseIdentifier.setEnterpriseGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getEnterpriseGuid());
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getEnterpriseGuid());
        userContext.setStoreGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
        UserContextUtils.put(userContext);
    }
}
