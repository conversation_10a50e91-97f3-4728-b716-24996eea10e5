package com.holderzone.saas.store.weixin.controller;


import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.WxMemberCenterCardRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.annotation.DynamicData;
import com.holderzone.saas.store.weixin.service.WxMemberCenterCardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("会员中心卡")
@RestController
@RequestMapping("/wx_member_card")
@Slf4j
public class WxMemberCenterCardController {

	private final WxMemberCenterCardService wxMemberCenterCardService;

	@Autowired
	public WxMemberCenterCardController(WxMemberCenterCardService wxMemberCenterCardService) {
		this.wxMemberCenterCardService = wxMemberCenterCardService;
	}

	@ApiOperation("会员卡列表")
	@PostMapping("/card_list")
	@DynamicData(enterpriseGuid = "#wxStoreConsumerDTO.enterpriseGuid", storeGuid = "#wxStoreConsumerDTO.storeGuid")
	public WxMemberCenterCardRespDTO cardList(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO) {
		log.info("会员卡列表入参:{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
		return wxMemberCenterCardService.cardList(wxStoreConsumerDTO);
	}

}
