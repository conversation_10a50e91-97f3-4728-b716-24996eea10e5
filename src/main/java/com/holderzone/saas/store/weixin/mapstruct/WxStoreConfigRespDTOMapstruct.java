package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreConfigRespDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

@Mapper(componentModel = "spring")
@Component
public interface WxStoreConfigRespDTOMapstruct {

    WxStoreConfigRespDTO wxStoreOrderConfigRespDTO2WxStoreConfigRespDTO(WxOrderConfigDTO wxOrderConfigDTO);
}
