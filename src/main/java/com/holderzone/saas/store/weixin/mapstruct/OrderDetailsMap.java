package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTradeOrderDetailsDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @description 微信订单详情
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTradeOrderDetailsDTOMapper
 * @date 2019/3/27
 */
@Mapper
public interface OrderDetailsMap {

	OrderDetailsMap INSTANCE = Mappers.getMapper(OrderDetailsMap.class);

	@Mappings({
			@Mapping(target = "orderModel", source = "tradeMode"),
			@Mapping(target = "tableGuid", source = "diningTableGuid"),
			@Mapping(target = "tableCode", source = "diningTableName"),
			@Mapping(target = "mark", source = "mark", defaultValue = ""),
			@Mapping(target = "remark", source = "remark", defaultValue = ""),
			@Mapping(target = "totalPrice", source = "orderFee"),
			@Mapping(target = "orderFeeDetailDTO", source = "orderFeeDetailDTO"),
			@Mapping(target = "actuallyPayFeeDetailDTOS", expression = "java(java.util.Optional.ofNullable(dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS()).orElse(java.util.Collections.emptyList()))"),
			@Mapping(target = "discountFeeDetailDTOS", expression = "java(java.util.Optional.ofNullable(dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS()).orElse(java.util.Collections.emptyList()))"),
	})
	WxStoreTradeOrderDetailsDTO getOrderDetails(DineinOrderDetailRespDTO dineinOrderDetailRespDTO);


	/**
	 * @describle	用户拼接
	 * @param wxStoreMerchantOrderDTO
	 * @return
	 */
	default WxStoreAdvanceConsumerReqDTO getWxStoreAdvanceConsumer(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO){
		WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = new WxStoreAdvanceConsumerReqDTO();
		WxStoreConsumerDTO wxStoreConsumerDTO = new WxStoreConsumerDTO();
		wxStoreAdvanceConsumerReqDTO.setWxStoreConsumerDTO(wxStoreConsumerDTO);
		wxStoreConsumerDTO.setStoreGuid(wxStoreMerchantOrderDTO.getStoreGuid());
		wxStoreConsumerDTO.setDiningTableGuid(wxStoreMerchantOrderDTO.getDiningTableGuid());
		wxStoreConsumerDTO.setTableCode(wxStoreMerchantOrderDTO.getTableCode());
		wxStoreConsumerDTO.setOpenId(wxStoreMerchantOrderDTO.getOpenId());
		wxStoreConsumerDTO.setNickName(wxStoreMerchantOrderDTO.getNickName());
		wxStoreConsumerDTO.setHeadImgUrl(wxStoreMerchantOrderDTO.getHeadImgUrl());
		wxStoreAdvanceConsumerReqDTO.setOrderModel(wxStoreMerchantOrderDTO.getTradeMode());
		wxStoreAdvanceConsumerReqDTO.setUserCount(wxStoreMerchantOrderDTO.getActualGuestsNo());
		return wxStoreAdvanceConsumerReqDTO;
	}

	default Integer getState(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO) {
		Integer orderState = wxStoreMerchantOrderDTO.getOrderState();
		switch (orderState) {
			case 0:return 0;
			case 1:return 1;
			case 2:return 3;
			case 4:return 2;
			default:return 0;
		}
	}
	@Mappings({
			@Mapping(target = "guid", ignore = true),
			@Mapping(target = "wxGuid", source = "guid"),
			@Mapping(target = "batchId", source = "guid"),
			@Mapping(target = "orderNo", source = "orderNo",defaultValue = ""),
			@Mapping(target = "mark", source = "mark",defaultValue = ""),
			@Mapping(target = "remark", source = "remark",defaultValue = ""),
			@Mapping(target = "prepayFee",expression = "java(java.math.BigDecimal.ZERO)"),
			@Mapping(target = "orderFee",expression = "java(java.math.BigDecimal.ZERO)"),
			@Mapping(target = "actuallyPayFee",expression = "java(java.math.BigDecimal.ZERO)"),
			@Mapping(target = "changeFee",expression = "java(java.math.BigDecimal.ZERO)"),
			@Mapping(target = "discountFee",expression = "java(java.math.BigDecimal.ZERO)"),

			@Mapping(target = "orderModel", source = "tradeMode"),
			@Mapping(target = "guestCount", source = "actualGuestsNo"),
			@Mapping(target = "state", expression = "java(getState(wxStoreMerchantOrderDTO))"),
			@Mapping(target = "wxStoreAdvanceConsumerReqDTO", expression = "java(getWxStoreAdvanceConsumer(wxStoreMerchantOrderDTO))"),
			@Mapping(target = "tableGuid",source ="diningTableGuid" ),
			@Mapping(target = "orderFeeDetailDTOS",expression = "java(java.util.Collections.emptyList())"),
			@Mapping(target = "actuallyPayFeeDetailDTOS",expression = "java(java.util.Collections.emptyList())"),
			@Mapping(target = "discountFeeDetailDTOS",expression = "java(java.util.Collections.emptyList())"),
			@Mapping(target = "returnItemDTOS",expression = "java(java.util.Collections.emptyList())"),
	})
	WxStoreTradeOrderDetailsDTO getOrderDetails(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO);

	List<WxStoreTradeOrderDetailsDTO> getOrderDetails(List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS);


	@Mappings({
			@Mapping(target = "tradeMode",source = "orderModel"),
			@Mapping(target = "payableAmount",source = "orderFee"),
	})
	WxStoreTradeOrderDetailsRespDTO getOrderDetailsResp(WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO);
}
