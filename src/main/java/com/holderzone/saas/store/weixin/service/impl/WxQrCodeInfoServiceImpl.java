package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.weixin.constant.ModelName;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import com.holderzone.saas.store.weixin.mapper.WxQrCodeInfoMapper;
import com.holderzone.saas.store.weixin.service.WxQrCodeInfoService;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQrCodeInfoServiceImpl
 * @date 2019/03/22 11:54
 * @description 微信二维码参数信息Service实现类
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxQrCodeInfoServiceImpl extends ServiceImpl<WxQrCodeInfoMapper, WxQrCodeInfoDO>
        implements WxQrCodeInfoService {
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    OrganizationClientService organizationClientService;

    @Autowired
    WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;

    @Override
    public String getSceneStr(WxQrCodeUrlQuery wxQrCodeUrlQuery) {
        WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO();
        BeanUtils.copyProperties(wxQrCodeUrlQuery, wxQrCodeInfoDO);
        wxQrCodeInfoDO.setBrandGuid(wxQrCodeUrlQuery.getBrandGuid());
        log.info("wxQrCodeInfo:{}", wxQrCodeInfoDO);
        String guid = redisUtils.generateGuid(ModelName.WX + ":createScene");
        wxQrCodeInfoDO.setGuid(guid);
        wxQrCodeInfoDO.setGmtCreate(LocalDateTime.now());
        String eventKey = BusinessName.ORDER_CONFIG_2 + "," + wxQrCodeUrlQuery.getEnterpriseGuid() + "," + guid;
        if (Objects.nonNull(wxQrCodeUrlQuery.getAppId())){
            eventKey = eventKey +","+wxQrCodeUrlQuery.getAppId();
        }
        if (Objects.nonNull(wxQrCodeUrlQuery.getIsAgain())) {
            eventKey = eventKey + "," + Constant.WX_AUTHORIZER_EVENT_KEY_AGAIN;
        }
        if (Objects.nonNull(wxQrCodeUrlQuery.getIsMyself())) {
            eventKey = eventKey + "," + Constant.WX_AUTHORIZER_EVENT_KEY_MYSELF;
        }
        if (!StringUtils.isEmpty(wxQrCodeUrlQuery.getMyselfOpenId())) {
            eventKey = eventKey + "," + wxQrCodeUrlQuery.getMyselfOpenId();
        }
        save(wxQrCodeInfoDO);
        return eventKey;
    }

    @Override
    public WxQrCodeInfoDO getWxQrCodeInfoByGuid(String guid) {
        WxQrCodeInfoDO wxQrCodeInfoDO = getById(guid);
        if (!ObjectUtils.isEmpty(wxQrCodeInfoDO)) {
            String storeGuid = wxQrCodeInfoDO.getStoreGuid();
            StoreDTO storeDTO = organizationClientService.queryStoreByGuid(storeGuid);
            if (!ObjectUtils.isEmpty(storeDTO))
                wxQrCodeInfoDO.setStoreName(storeDTO.getName());
        }
        return wxQrCodeInfoDO;
    }

    @Override
    public WxQrCodeInfoDO getCacheWxQrCodeInfoByGuid(String guid) {
    	String wxqrKey = "wxqrcode:"+guid;
        WxQrCodeInfoDO wxQrCodeInfoDO = (WxQrCodeInfoDO) redisUtils.get(wxqrKey);
        if(wxQrCodeInfoDO==null) {
        	wxQrCodeInfoDO = getWxQrCodeInfoByGuid(guid);
        	if (!ObjectUtils.isEmpty(wxQrCodeInfoDO)) {
        		redisUtils.setEx(wxqrKey, wxQrCodeInfoDO, 2, TimeUnit.HOURS);
        	}
        }		
        return wxQrCodeInfoDO;
    }
}
