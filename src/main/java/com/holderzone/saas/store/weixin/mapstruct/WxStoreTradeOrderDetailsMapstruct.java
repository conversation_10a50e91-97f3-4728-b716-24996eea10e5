package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTradeOrderDetailsDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTradeOrderDetailsMapstruct
 * @date 2019/3/30
 */
@Component
@Mapper(componentModel = "spring")
public interface WxStoreTradeOrderDetailsMapstruct {

	WxStoreTradeOrderDetailsMapstruct INSTANCE = Mappers.getMapper(WxStoreTradeOrderDetailsMapstruct.class);
	/**
	 * @descrilbe	订单批次创建时间
	 * @return
	 */
	default LocalDateTime getGmtCreate() {
		return LocalDateTime.now();
	}

	@Mappings({
			@Mapping(target = "wxStoreAdvanceConsumerReqDTO",source = "wxStoreAdvanceConsumerReqDTO"),
			@Mapping(target = "dineInItemDTOS",source = "createDineInOrderReqDTO.dineInItemDTOS"),
			@Mapping(target = "gmtCreate",source = "dineinOrderDetailRespDTO.gmtCreate"),
			//这是每个订单批次的预付金，不是整个订单详情的消费合计
			@Mapping(target = "totalPrice",source = "advanceTotalPrice"),

			@Mapping(target = "guid",source = "dineinOrderDetailRespDTO.guid"),
			@Mapping(target = "orderNo",source = "dineinOrderDetailRespDTO.orderNo"),
			@Mapping(target = "remark",source = "dineinOrderDetailRespDTO.remark"),
			@Mapping(target = "orderModel",source = "dineinOrderDetailRespDTO.tradeMode"),
			@Mapping(target = "guestCount",source = "dineinOrderDetailRespDTO.guestCount"),
			@Mapping(target = "orderFee",source = "dineinOrderDetailRespDTO.orderFee"),
			@Mapping(target = "actuallyPayFee",source = "dineinOrderDetailRespDTO.actuallyPayFee"),
			@Mapping(target = "changeFee",source = "dineinOrderDetailRespDTO.changeFee"),
			@Mapping(target = "discountFee",source = "dineinOrderDetailRespDTO.discountFee"),
			@Mapping(target = "actuallyPayFeeDetailDTOS",source = "dineinOrderDetailRespDTO.actuallyPayFeeDetailDTOS"),
			@Mapping(target = "discountFeeDetailDTOS",source = "dineinOrderDetailRespDTO.discountFeeDetailDTOS"),
	})
	WxStoreTradeOrderDetailsDTO getWxStoreTradeOrderDetails(CreateDineInOrderReqDTO createDineInOrderReqDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO,BigDecimal advanceTotalPrice);

	@Mappings({
			@Mapping(target = "wxStoreAdvanceConsumerReqDTO",source = "wxStoreAdvanceConsumerReqDTO"),
			@Mapping(target = "guid",source = "dineinOrderDetailRespDTO.guid"),
			@Mapping(target = "orderNo",source = "dineinOrderDetailRespDTO.orderNo"),
			//这里用的是商户订单详情的商品集合
			@Mapping(target = "dineInItemDTOS",source = "dineinOrderDetailRespDTO.dineInItemDTOS"),
			@Mapping(target = "gmtCreate",source = "dineinOrderDetailRespDTO.gmtCreate"),
			@Mapping(target = "checkoutTime",source = "dineinOrderDetailRespDTO.checkoutTime"),
			@Mapping(target = "prepayFee",source = "dineinOrderDetailRespDTO.prepayFee"),
			@Mapping(target = "orderModel",source = "dineinOrderDetailRespDTO.tradeMode"),
			@Mapping(target = "guestCount",source = "dineinOrderDetailRespDTO.guestCount"),
			@Mapping(target = "orderFee",source = "dineinOrderDetailRespDTO.orderFee"),
			@Mapping(target = "actuallyPayFee",source = "dineinOrderDetailRespDTO.actuallyPayFee"),
			@Mapping(target = "changeFee",source = "dineinOrderDetailRespDTO.changeFee"),
			@Mapping(target = "discountFee",source = "dineinOrderDetailRespDTO.discountFee"),
			@Mapping(target = "actuallyPayFeeDetailDTOS",source = "dineinOrderDetailRespDTO.actuallyPayFeeDetailDTOS"),
			@Mapping(target = "discountFeeDetailDTOS",source = "dineinOrderDetailRespDTO.discountFeeDetailDTOS"),
	})
	WxStoreTradeOrderDetailsDTO getWxStoreTradeOrderDetails(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

}
