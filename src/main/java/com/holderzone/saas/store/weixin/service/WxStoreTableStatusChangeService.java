package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO; /**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTableStatusChangeService
 * @date 2019/5/17
 */
public interface WxStoreTableStatusChangeService {

	boolean combine(TableCombineDTO tableCombineDTO);

	boolean separate(TableOrderCombineDTO tableOrderCombineDTO);

	boolean turn(TurnTableDTO tableStatus);
}
