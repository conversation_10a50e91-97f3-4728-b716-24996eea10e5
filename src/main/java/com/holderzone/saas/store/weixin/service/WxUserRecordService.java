package com.holderzone.saas.store.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxUserRecordService
 * @date 2019/04/02 19:31
 * @description 微信扫码用户信息service
 * @program holder-saas-store
 */
public interface WxUserRecordService extends IService<WxUserRecordDO> {

	/**
	 * 跳转相关，会员缓存处理
	 * @param wxMemberSessionDTO
	 * @param openId
	 */
	void asyncMemberInfo(WxMemberSessionDTO wxMemberSessionDTO, String openId);

	/**
	 * 修改用户登录状态
	 *
	 * @param wxStoreConsumerDTO
	 * @return
	 */
	Boolean updateUserLogin(WxStoreConsumerDTO wxStoreConsumerDTO);

	/**
	 * 修改用户登录状态
	 *
	 * @param
	 * @return
	 */
	Boolean updateUserLoginByOpenId(String enterpriseGuid,String openId,boolean loginState,WxMemberSessionDTO memberSessionDTO);

	/**
	 * 修改用户信息 不更新登录状态
	 *
	 * @param wxStoreConsumerDTO
	 * @return
	 */
	WxUserRecordDO saveOrUpdateUserInfo(WxStoreConsumerDTO wxStoreConsumerDTO);

	/**
	 * 获取用户领域对象
	 *
	 * @param wxStoreConsumerDTO
	 * @return
	 */
	WxUserRecordDO getWxuserRecord(WxStoreConsumerDTO wxStoreConsumerDTO);

	WxUserRecordDO getOneByOpenId(String openId);

	Boolean bindPhoneNum(String phoneNum);

	WxUserRecordDO obtainByPhone(String phone);

	/**
	 * 更新用户信息 运营主体信息
	 *
	 * @param openId
	 * @return
	 */
	WxUserRecordDO updateUserInfo(String openId);
}
