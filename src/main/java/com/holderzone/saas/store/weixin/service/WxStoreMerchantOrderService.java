package com.holderzone.saas.store.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOperationDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.WechatOrderInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreMerchantOrderRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.query.WxStorePendingOrdersQuery;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreMerchantOrderService
 * @date 2019/4/9
 */
public interface WxStoreMerchantOrderService extends IService<WxStoreMerchantOrderDO> {
    /**
     * @param wxStoreMerchantOrderReqDTO
     * @return
     * @describle 获取商户预订单
     */
    WxStoreMerchantOrderRespDTO getWxStoreMerchantOrderResp(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO);

    /**
     * 通过guid查询当前订单详情
     *
     * @param wxStoreMerchantOrderReqDTO
     * @return
     */
    WxStoreMerchantOrderDTO getDetailPend(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO);

    /**
     * @param wxOperateReqDTO
     * @describle 处理订单
     */
    WxStoreMerchantOperationDTO operationMerchantOrder(WxOperateReqDTO wxOperateReqDTO);

    void pushAutoMsg(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO, StoreDeviceDTO masterDevice, int autoRev);

    void pushMsg(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO);

    /**
     * @param wxStoreMerchantOrderDTO
     * @describle 更新订单
     */
    void updateMerchantOrder(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO);

    void updateFastOrder(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO);

    /**
     * @param wxStoreMerchantOrderDTO
     * @describle 删除订单
     */
    void delMerchantOrder(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO);

    /**
     * 查询门店的待处理订单
     *
     * @param wxStorePendingOrdersQuery
     * @return
     */
    List<WxStoreMerchantOrderDTO> getPendingOrders(WxStorePendingOrdersQuery wxStorePendingOrdersQuery);


    BusinessMessageDTO pushOrderMsg(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO);

    void pushFastMsg(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO);


    boolean combine(List<String> merchantBatchGuidList, String mainOrderGuid);

    List<WxStoreMerchantOrderDO> separate(TableOrderCombineDTO tableOrderCombineDTO);

    void turn(TurnTableDTO turnTableDTO);

    void updateOrderState(String tableGuid, Integer orderState);


    WxStoreMerchantOrderDO getOneByGuid(String orderGuid);

    List<WxStoreMerchantOrderDO> tableListByMerchantGuidOrOrderGuid(WxStorePendingOrdersQuery query);

    /**
     * 查询当前品牌当天未处理正餐
     *
     * @param openId
     * @return
     */
    List<WxStoreMerchantOrderDTO> currentTimePendingOrderList(@NotBlank String openId);

    void updateOutDateOrder(String openId);

    List<WxStoreMerchantOrderDO> selectByOrderRecordGuid(String orderRecordGuid, Collection<String> tableGuids);


    boolean existPendingOrder(String orderRecordGuid, Collection<String> tableGuids);

    int countByStatus(String orderRecordGuid, int status, int tradeMode);

    /**
     * 支付后，处理未完成订单
     *
     * @param orderGuid 订单id
     */
    void dealUnFinishedOrders(String orderGuid);

    WxStoreMerchantOrderDO firstSubmit(String orderRecordGuid);

    /**
     * 更新订单整单备注
     *
     * @param guid
     * @param remark
     * @return
     */
    void updateRemarkById(String guid, String remark);

    /**
     * 更新订单商品备注
     *
     * @param itemGuid
     * @param remark
     * @return
     */
    void updateItemRemarkById(String orderGuid, String itemGuid, String remark);

    /**
     * 根据订单记录id获取订单详情
     *
     * @param orderRecordGuid 微信订单记录guid
     * @return 订单详情
     */
    List<WxStoreMerchantOrderDTO> getDetailByOrderRecordGuid(String orderRecordGuid);

    /**
     * 根据订单Guid查询微信订单金额（接单&未接单）
     *
     * @param orderGuid 订单guid
     * @return 微信订单金额（接单&未接单）
     */
    BigDecimal getWechatOrderFeeByOrderGuid(String orderGuid);

    /**
     * 根据订单Guid查询微信订单信息
     *
     * @param orderGuid 订单guid
     * @return 微信订单信息
     */
    WechatOrderInfoDTO getWechatOrderInfoByOrderGuid(String orderGuid);

    /**
     * 根据Guid查询微信订单信息
     *
     * @param guid 订单guid/订单记录guid
     * @return 微信订单信息
     */
    WechatOrderInfoDTO getWechatOrderInfoByGuid(String guid);
}
