package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.item.resp.AttrGroupSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.AttrSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubItemSkuSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubgroupSynRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreAttrGroupRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreAttrRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreSkuRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreCreateFastFoodReqDTO
 * @date 2019/4/11
 */
@Mapper(componentModel = "spring")
@Component
public interface WxStoreCreateFastFoodReqDTOMapstruct {
	@Mappings({
			@Mapping(target = "guid", source = "attrGuid"),
			@Mapping(target = "attrName", source = "name"),
			@Mapping(target = "attrPrice", source = "price"),
			@Mapping(target = "attrGroupGuid", source = "attrGroupGuid"),
	})
	ItemAttrDTO getItemAttr(WxStoreAttrRespDTO wxStoreAttrRespDTO);

	default List<ItemAttrDTO> getItemAttrList(List<WxStoreAttrGroupRespDTO> wxStoreAttrGroupRespDTOS) {
		List<ItemAttrDTO> itemAttrDTOList = new LinkedList<>();
		for (WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO : wxStoreAttrGroupRespDTOS) {
			List<WxStoreAttrRespDTO> attrList = wxStoreAttrGroupRespDTO.getAttrList();
			List<WxStoreAttrRespDTO> collect = attrList.stream().filter(wxStoreAttr -> wxStoreAttr.getUserck() == 1).collect(Collectors.toList());
			//属性
			for (WxStoreAttrRespDTO wxStoreAttrRespDTO : collect) {
				ItemAttrDTO itemAttr = getItemAttr(wxStoreAttrRespDTO);
				//选中属性
				itemAttr.setAttrGroupName(wxStoreAttrGroupRespDTO.getName());
				itemAttr.setAttrGroupGuid(wxStoreAttrGroupRespDTO.getAttrGroupGuid());
				/**
				 * 属性数量暂时设为1
				 */
				itemAttr.setNum(1);
				itemAttrDTOList.add(itemAttr);
			}
		}
		return itemAttrDTOList;
	}

	/**
	 * @param wxStoreAttrGroupRespDTOS
	 * @return
	 * @describle 获取单个商品的属性总价
	 */
	default BigDecimal getAttrTotalPrice(List<WxStoreAttrGroupRespDTO> wxStoreAttrGroupRespDTOS) {
		List<ItemAttrDTO> itemAttrList = getItemAttrList(wxStoreAttrGroupRespDTOS);
		BigDecimal attrTotalPrice = new BigDecimal("0");
		for (ItemAttrDTO itemAttrDTO : itemAttrList) {
			if (!BigDecimal.ZERO.equals(itemAttrDTO.getAttrPrice())) {
				attrTotalPrice = attrTotalPrice.add(itemAttrDTO.getAttrPrice());
			}

		}
		return attrTotalPrice;
	}

	/**
	 * @param wxStoreItemRespDTO
	 * @return
	 * @describle 抽取选中规格
	 */
	default WxStoreSkuRespDTO getSku(WxStoreItemRespDTO wxStoreItemRespDTO) {
		List<WxStoreSkuRespDTO> skuList = wxStoreItemRespDTO.getSkuList();
		Optional<WxStoreSkuRespDTO> sku = skuList.stream().filter(wxStoreSku -> wxStoreSku.getUserck() == 1).findFirst();
		return sku.get();
	}

	/**
	 * 套餐组
	 * @param wxStoreItemRespDTO a
	 * @return
	 */
	default List<PackageSubgroupDTO> setMealsTransformation(WxStoreItemRespDTO wxStoreItemRespDTO) {
		List<PackageSubgroupDTO> packageSubgroupDTOS = new LinkedList<>();
		if (!ObjectUtils.isEmpty(wxStoreItemRespDTO.getSubgroupList())) {
			//套餐所有组
			List<SubgroupSynRespDTO> subgroupList = wxStoreItemRespDTO.getSubgroupList();
			for(SubgroupSynRespDTO subgroupSynRespDTO:subgroupList){
				//每个小组
				PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
				packageSubgroupDTO.setSubgroupGuid(subgroupSynRespDTO.getSubgroupGuid());
				packageSubgroupDTO.setSubgroupName(subgroupSynRespDTO.getName());
				//套餐子项:商品与规格
				List<SubDineInItemDTO> subDineInItemDTOS = new LinkedList<>();
				List<SubItemSkuSynRespDTO> subItemSkuList = subgroupSynRespDTO.getSubItemSkuList();
				if (!ObjectUtils.isEmpty(subItemSkuList)) {
					for (SubItemSkuSynRespDTO subItemSkuSynRespDTO : subItemSkuList) {
						if (wxStoreItemRespDTO.getIsFixPkg()==1||(wxStoreItemRespDTO.getIsFixPkg() == 0 && subItemSkuSynRespDTO.getDefaultNum() > 0)) {
							SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
							subDineInItemDTO.setItemGuid(subItemSkuSynRespDTO.getItemGuid());
							subDineInItemDTO.setItemName(subItemSkuSynRespDTO.getItemName());
							subDineInItemDTO.setCode(subItemSkuSynRespDTO.getCode());
							subDineInItemDTO.setItemType(subItemSkuSynRespDTO.getItemType());
							subDineInItemDTO.setItemTypeGuid(subItemSkuSynRespDTO.getTypeGuid());
							subDineInItemDTO.setItemTypeName(subItemSkuSynRespDTO.getTypeName());
							subDineInItemDTO.setSkuGuid(subItemSkuSynRespDTO.getSkuGuid());
							subDineInItemDTO.setSkuName(subItemSkuSynRespDTO.getSkuName());
							subDineInItemDTO.setCurrentCount(new BigDecimal(subItemSkuSynRespDTO.getDefaultNum()));
							subDineInItemDTO.setAddPrice(subItemSkuSynRespDTO.getAddPrice());
							subDineInItemDTO.setPackageDefaultCount(subItemSkuSynRespDTO.getItemNum());
							subDineInItemDTO.setUnit(subItemSkuSynRespDTO.getUnit());
							subDineInItemDTO.setRemark(subItemSkuSynRespDTO.getRemark());
							subDineInItemDTO.setPrice(BigDecimal.ZERO);

							//属性集合
							List<ItemAttrDTO> itemAttrDTOS = new LinkedList<>();
							List<AttrGroupSynRespDTO> attrGroupList = subItemSkuSynRespDTO.getAttrGroupList();
							BigDecimal itemTotalPrice = BigDecimal.ZERO;
							if (!ObjectUtils.isEmpty(attrGroupList)) {
								for (AttrGroupSynRespDTO attrGroupSynRespDTO : attrGroupList) {
									List<AttrSynRespDTO> attrList = attrGroupSynRespDTO.getAttrList();
									if (!ObjectUtils.isEmpty(attrList)) {
										if (attrList.stream().noneMatch(x -> 1 == x.getIsDefault()) && 1 == attrGroupSynRespDTO.getIsRequired()) {
											AttrSynRespDTO attrSynRespDTO = attrList.get(0);
											ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
											itemAttrDTO.setAttrGuid(attrSynRespDTO.getAttrGuid());
											itemAttrDTO.setAttrName(attrSynRespDTO.getName());
											itemAttrDTO.setAttrGroupGuid(attrGroupSynRespDTO.getAttrGroupGuid());
											itemAttrDTO.setAttrGroupName(attrGroupSynRespDTO.getName());
											itemAttrDTO.setAttrPrice(attrSynRespDTO.getPrice());
											itemTotalPrice=itemTotalPrice.add(Optional.ofNullable(attrSynRespDTO.getPrice()).orElse(BigDecimal.ZERO));
											itemAttrDTO.setNum(1);
											itemAttrDTOS.add(itemAttrDTO);
										}else{
											for (AttrSynRespDTO attrSynRespDTO : attrList) {
												//这里不是指默认属性，而是选中，只是共用字段
												if (attrSynRespDTO.getIsDefault() == 1) {
													ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
													itemAttrDTO.setAttrGuid(attrSynRespDTO.getAttrGuid());
													itemAttrDTO.setAttrName(attrSynRespDTO.getName());
													itemAttrDTO.setAttrGroupGuid(attrGroupSynRespDTO.getAttrGroupGuid());
													itemAttrDTO.setAttrGroupName(attrGroupSynRespDTO.getName());
													itemAttrDTO.setAttrPrice(attrSynRespDTO.getPrice());
													itemTotalPrice=itemTotalPrice.add(Optional.ofNullable(attrSynRespDTO.getPrice()).orElse(BigDecimal.ZERO));
													itemAttrDTO.setNum(1);
													itemAttrDTOS.add(itemAttrDTO);
												}
											}
										}

									}
								}
							}

							subDineInItemDTO.setItemAttrDTOS(itemAttrDTOS);
							subDineInItemDTO.setSingleItemAttrTotal(itemTotalPrice);
							subDineInItemDTOS.add(subDineInItemDTO);
						}
					}
				}

				//套餐子项:商品与规格
				packageSubgroupDTO.setSubDineInItemDTOS(subDineInItemDTOS);
				packageSubgroupDTOS.add(packageSubgroupDTO);
			}
			if (!ObjectUtils.isEmpty(packageSubgroupDTOS)) {
				return packageSubgroupDTOS;
			}
		}
		return Collections.emptyList();
	}

	@Mappings({
			@Mapping(target = "itemGuid", source = "itemGuid"),
			@Mapping(target = "itemName", source = "name"),
			@Mapping(target = "itemType", source = "itemType"),
			@Mapping(target = "itemTypeGuid", source = "typeGuid"),
			@Mapping(target = "itemTypeName", source = "typeName"),
			@Mapping(target = "skuGuid", expression = "java(getSku(wxStoreItemRespDTO).getSkuGuid())"),
			@Mapping(target = "code", expression = "java(getSku(wxStoreItemRespDTO).getCode())"),
			@Mapping(target = "skuName", expression = "java(getSku(wxStoreItemRespDTO).getName())"),
			@Mapping(target = "price", expression = "java(getSku(wxStoreItemRespDTO).getSalePrice())"),
			@Mapping(target = "originalPrice", expression = "java(getSku(wxStoreItemRespDTO).getSalePrice())"),
			@Mapping(target = "singleItemAttrTotal", expression = "java(getAttrTotalPrice(wxStoreItemRespDTO.getAttrGroupList()))"),
			@Mapping(target = "itemPrice", source = "itemPrice"),
			@Mapping(target = "unit", expression = "java(getSku(wxStoreItemRespDTO).getUnit())"),
			//套餐分组
			@Mapping(target = "packageSubgroupDTOS", expression = "java(setMealsTransformation(wxStoreItemRespDTO))"),
			//属性组集合
			@Mapping(target = "itemAttrDTOS", expression = "java(getItemAttrList(wxStoreItemRespDTO.getAttrGroupList()))")
	})
	DineInItemDTO dineInItem(WxStoreItemRespDTO wxStoreItemRespDTO);

	@Mappings({
			@Mapping(target = "remark", source = "wxStoreAdvanceConsumerReqDTO.orderRemark"),
			@Mapping(target = "guestCount", source = "wxStoreAdvanceConsumerReqDTO.userCount"),
			@Mapping(target = "storeGuid", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid"),
			@Mapping(target = "storeName", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeName"),
			@Mapping(target = "enterpriseGuid", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.enterpriseGuid"),
			@Mapping(target = "deviceId", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.openId"),
			@Mapping(target = "deviceType", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.deviceType", defaultValue = "12"),
			@Mapping(target = "autoMark", expression = "java(Integer.parseInt(\"1\"))"),
			@Mapping(target = "dineInItemDTOS", source = "itemList"),
	})
	CreateFastFoodReqDTO getCreateFastFoodReq(WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO);
}
