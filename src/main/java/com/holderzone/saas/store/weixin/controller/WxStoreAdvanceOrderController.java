package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceEstimateDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderPriceDTO;
import com.holderzone.saas.store.weixin.service.WxStoreAdvanceOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/wx_store_advance_order_provide")
@Api("微信门店预订单管理")
@Slf4j
public class WxStoreAdvanceOrderController {

	private final WxStoreAdvanceOrderService wxStoreAdvanceOrderService;

	@Autowired
	public WxStoreAdvanceOrderController(WxStoreAdvanceOrderService wxStoreAdvanceOrderService) {
		this.wxStoreAdvanceOrderService = wxStoreAdvanceOrderService;
	}

	@ApiOperation("选好了")
	@PostMapping(value = "/add")
	public WxStoreAdvanceEstimateDTO createAdvanceOrder(@RequestBody WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO) {
		log.info("入参wxStoreAdvanceOrderDTO:{}", wxStoreAdvanceOrderDTO);
		return wxStoreAdvanceOrderService.createAdvanceOrder(wxStoreAdvanceOrderDTO);
	}

	@ApiOperation("修改预订单整单备注")
	@PostMapping(value = "/update_remark")
	public void updateAdvanceOrderRemark(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("入参wxStoreAdvanceConsuerReqDTO:{}", wxStoreAdvanceConsumerReqDTO);
		wxStoreAdvanceOrderService.updateAdvanceOrderRemark(wxStoreAdvanceConsumerReqDTO);
	}

	@ApiOperation("删除用户预订单")
	@PostMapping(value = "/del")
	public Boolean delAdvanceOrder(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("入参wxStoreConsumerDTO", wxStoreAdvanceConsumerReqDTO);
		return wxStoreAdvanceOrderService.delAdvanceOrder(wxStoreAdvanceConsumerReqDTO);
	}

	@ApiOperation("获取个人预订单")
	@PostMapping(value = "/advance")
	public WxStoreAdvanceOrderPriceDTO getPersonWxStoreAdvanceOrder(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("入参wxStoreConsumerDTO", wxStoreAdvanceConsumerReqDTO);
		return wxStoreAdvanceOrderService.getPersonWxStoreAdvanceOrder(wxStoreAdvanceConsumerReqDTO);
	}

	@ApiOperation("获取桌台订单")
	@PostMapping(value = "/table")
	public WxStoreAdvanceOrderPriceDTO getTableWxStoreAdvanceOrder(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("入参wxStoreConsumerDTO", wxStoreAdvanceConsumerReqDTO);
		return wxStoreAdvanceOrderService.getTableWxStoreAdvanceOrder(wxStoreAdvanceConsumerReqDTO);
	}
}
