package com.holderzone.saas.store.weixin.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.injector.LogicSqlInjector;
import com.baomidou.mybatisplus.extension.plugins.OptimisticLockerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import org.apache.ibatis.reflection.MetaObject;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

@Configuration
@MapperScan("com.holderzone.saas.store.weixin.mapper")
public class MybatisPlusConfig {

    /**
     * 分页插件
     * 与sdk中的PageInterceptor冲突了，一旦配置这个，RoutingStatementHandler会创建代理类，PageInterceptor反射获取delegate就会获取不到
     */
//    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

    @Bean
    public ISqlInjector sqlInjector() {
        return new LogicSqlInjector();
    }

    @Bean
    public OptimisticLockerInterceptor optimisticLockerInterceptor() {
        return new OptimisticLockerInterceptor();
    }

	@Bean
	public MetaObjectHandler metaObjectHandler() {
		return new MetaObjectHandler() {
			@Override
			public void insertFill(MetaObject metaObject) {
				this.setFieldValByName("gmtCreate", LocalDateTime.now(), metaObject);
				this.setFieldValByName("gmtModified", LocalDateTime.now(), metaObject);
				this.setFieldValByName("createStaffGuid", WeixinUserThreadLocal.getOpenId(),metaObject);
				this.setFieldValByName("createStaffName", WeixinUserThreadLocal.getNickName(), metaObject);
			}

			@Override
			public void updateFill(MetaObject metaObject) {
				this.setFieldValByName("gmtModified", LocalDateTime.now(), metaObject);
			}
		};
	}
}