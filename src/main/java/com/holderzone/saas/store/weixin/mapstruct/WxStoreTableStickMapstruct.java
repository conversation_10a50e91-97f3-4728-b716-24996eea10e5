package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreTableStickDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTableStickMapstruct
 * @date 2019/03/05 15:41
 * @description 微信桌贴mapstruct
 * @program holder-saas-store
 */
@Mapper(componentModel = "spring")
@Component
public interface WxStoreTableStickMapstruct {

    @Mappings({
            @Mapping(target = "isDelete", ignore = true),
            @Mapping(target = "categoryGuid", source = "category")
    })
    WxStoreTableStickDO tableStickDTO2DO(WxTableStickDTO wxTableStickDTO);

    List<WxStoreTableStickDO> tableStickDTOList2DOList(List<WxTableStickDTO> wxTableStickDTOList);

    @Mappings({
            @Mapping(target = "isDelete", ignore = true, defaultValue = "0"),
            @Mapping(target = "category", source = "categoryGuid")
    })
    WxTableStickDTO stickDO2stickDTO(WxStoreTableStickDO wxStoreTableStickDO);

    List<WxTableStickDTO> stickDOList2DTOList(List<WxStoreTableStickDO> wxStoreTableStickDOList);
}
