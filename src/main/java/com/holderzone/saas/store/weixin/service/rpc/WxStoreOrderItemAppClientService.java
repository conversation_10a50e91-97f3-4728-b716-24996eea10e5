package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.saas.store.dto.order.request.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.item.BatchItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.dto.order.request.item.ServeItemReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreOrderItemAppClientService
 * @date 2019/3/28
 */
@Component
@FeignClient(name = "holder-saas-aggregation-app", fallbackFactory = WxStoreOrderItemAppClientService.WxStoreOrderItemAppFullback.class)
public interface WxStoreOrderItemAppClientService {

    String URL_PREFIX = "/order_item";

    @PostMapping(URL_PREFIX + "/add_item")
    Result addItem(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping(URL_PREFIX + "/serve_item")
    Result serveItem(ServeItemReqDTO serveItemReqDTO);

    @PostMapping(URL_PREFIX + "/return")
    Result<BatchItemReturnOrFreeReqDTO> returnItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO);

    @PostMapping(URL_PREFIX + "/free")
    Result<BatchItemReturnOrFreeReqDTO> freeItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO);

    @PostMapping(URL_PREFIX + "/cancel_free")
    Result cancelFree(CancelFreeItemReqDTO cancelFreeItemReqDTO);

    @Component
    @Slf4j
    class WxStoreOrderItemAppFullback implements FallbackFactory<WxStoreOrderItemAppClientService> {
        @Override
        public WxStoreOrderItemAppClientService create(Throwable throwable) {
            return new WxStoreOrderItemAppClientService() {

                @Override
                public Result addItem(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result serveItem(ServeItemReqDTO serveItemReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<BatchItemReturnOrFreeReqDTO> returnItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<BatchItemReturnOrFreeReqDTO> freeItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result cancelFree(CancelFreeItemReqDTO cancelFreeItemReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }
            };
        }
    }
}
