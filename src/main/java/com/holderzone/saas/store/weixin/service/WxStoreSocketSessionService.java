package com.holderzone.saas.store.weixin.service;


import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 会话实现层
 * @className WxStoreSocketSessionService
 * @date 2019/3/22
 */
public interface WxStoreSocketSessionService {

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @describle 创建会话
	 */
	void createSession(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 * @describle 获取会话
	 */
	List<WxStoreAdvanceConsumerReqDTO> getSession(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);


	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 * @describle 获取所有顾客
	 */
	List<WxStoreAdvanceConsumerReqDTO> getAllConsumer(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @describle 删除个人会话
	 */
	void delSession(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	void delTableSession(String tableGuid);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 * @describle 获取单个会话
	 */
	WxStoreAdvanceConsumerReqDTO getSingleSession(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	List<WxStoreAdvanceConsumerReqDTO> getSession(String tableGuid);

	WxStoreAdvanceConsumerReqDTO getSingleSession(String openId);
}
