package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.saas.store.dto.weixin.WxCategoryDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickModelClientService
 * @date 2019/03/13 15:30
 * @description 桌贴模板库clientService
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-cloud-wechat", fallbackFactory = WxStickModelClientService.FallBackClass.class)
public interface WxStickModelClientService {

    @PostMapping("/stick/category/list")
    List<WxTableStickDTO> getTableStickList(WxStickModelReqDTO reqDTO);

    @GetMapping("/category/list")
    List<WxCategoryDTO> listCategory();

    @Component
    @Slf4j
    class FallBackClass implements FallbackFactory<WxStickModelClientService> {
        @Override
        public WxStickModelClientService create(Throwable throwable) {
            return new WxStickModelClientService() {
                @Override
                public List<WxTableStickDTO> getTableStickList(WxStickModelReqDTO reqDTO) {
                    log.error("获取模板库列表失败，throwable={}", throwable.getMessage());
                    throw new RuntimeException("获取模板库列表失败，throwable={}" + throwable);
                }

                @Override
                public List<WxCategoryDTO> listCategory() {
                    return null;
                }
            };
        }
    }
}
