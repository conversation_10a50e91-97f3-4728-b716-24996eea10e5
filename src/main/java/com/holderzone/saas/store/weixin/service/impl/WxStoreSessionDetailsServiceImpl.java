package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.BrandStoreDetailDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.execption.CorrectResult;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberTerminalClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreSessionDetailsService
 * @date 2019/4/26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WxStoreSessionDetailsServiceImpl implements WxStoreSessionDetailsService {

	private final RedisUtils redisUtils;

	private final WxStoreTableClientService wxStoreTableClientService;

	private final MemberTerminalClientService memberTerminalClientService;

	private final WxStoreDineInOrderClientService wxStoreDineInOrderClientService;

	private final WxStoreDineInBillClientService wxStoreDineInBillClientService;

	private final WxStoreOrderConfigService wxStoreOrderConfigService;

	private final OrganizationClientService organizationClientService;

	private final EnterpriseClientService enterpriseClientService;

	private final HsaBaseClientService hsaBaseClientService;


	/**
	 * OrderGuid的key
	 *
	 * @param
	 * @return
	 */
	private String orderGuidCombineKey() {
		return BusinessName.ORDER_GUID;
	}

	@Override
	public void saveOrderGuid(String key, String orderGuid) {
		String redisKey = orderGuidCombineKey();
		redisUtils.hPut(redisKey, key, orderGuid);
	}

	@Override
	public void delOrderGuid(String key) {
		String redisKey = orderGuidCombineKey();
		redisUtils.hDelete(redisKey, key);
	}

	@Override
	public String getOrderGuid(String key) {
		String redisKey = orderGuidCombineKey();
		return (String) redisUtils.hGet(redisKey, key);
	}

	@Override
	public void saveFastOrderGuid(String storeGuid, String openId, String orderGuid) {
		redisUtils.hPut(BusinessName.FAST_ORDER_GUID + ":" + storeGuid, openId, orderGuid);
	}

	@Override
	public String getFastOrderGuid(String storeGuid, String openId) {
		return (String) redisUtils.hGet(BusinessName.FAST_ORDER_GUID + ":" + storeGuid, openId);
	}

	private String combineOrderStateKey() {
		return BusinessName.ORDER_STATE;
	}

	/**
	 * 订单状态:0待确认，1已下单，2已支付，3已取消，4，已退菜，5待支付，6已完成
	 *
	 * @param tableGuid  桌台id
	 * @param orderState 订单状态
	 */
	@Override
	public void saveOrderState(String tableGuid, Integer orderState) {
		redisUtils.hPut(combineOrderStateKey(), tableGuid, orderState);
		redisUtils.persist(combineOrderStateKey());
	}

	@Override
	public Integer getOrderState(String tableGuid) {
		return (Integer) redisUtils.hGet(combineOrderStateKey(), tableGuid);
	}

	@Override
	public void delOrderState(String tableGuid) {
		redisUtils.hDelete(combineOrderStateKey(), tableGuid);
	}

	private String combineMerchantBatchGuid() {
		return BusinessName.MERCHANT_ORDER_GUID;
	}

	@Override
	public void updateMerchantBatchGuid(String key, String merchatBatchGuid) {
		redisUtils.hPut(combineMerchantBatchGuid(), key, merchatBatchGuid);
		redisUtils.persist(combineMerchantBatchGuid());
	}

	@Override
	public String getMerchantBatchGuid(String key) {
		return (String) redisUtils.hGet(combineMerchantBatchGuid(), key);
	}

	@Override
	public void delMerchantBatchGuid(String key) {
		redisUtils.hDelete(combineMerchantBatchGuid(), key);
	}

	private String combineDinnerGuestsCount() {
		return BusinessName.GUESTS_COUNT;
	}

	@Override
	public void delDinnerGuestsCount(String key) {
		redisUtils.hDelete(combineDinnerGuestsCount(), key);
	}

	@Override
	public void updateDinnerGuestsCount(String tableGuid, Integer count) {
		redisUtils.hPut(combineDinnerGuestsCount(), tableGuid, count);
	}

	@Override
	public Integer getDinnerGuestsCount(String key) {
		return (Integer) redisUtils.hGet(combineDinnerGuestsCount(), key);
	}

	private String combinePaidUser(String storeGuid) {
		return BusinessName.PAID_USER + ":" + storeGuid;
	}

	@Override
	public void savePaidUser(String storeGuid, String key, WxH5PayReqDTO wxH5PayReqDTO) {
		log.info("缓存支付下单详情:{}",JacksonUtils.writeValueAsString(wxH5PayReqDTO));
		redisUtils.hPut(combinePaidUser(storeGuid), key, wxH5PayReqDTO);
	}

	@Override
	public void delPaidUser(String storeGuid, String key) {
		redisUtils.hDelete(combinePaidUser(storeGuid), key);
	}

	@Override
	public WxH5PayReqDTO getPaidUser(String storeGuid, String key) {
		return (WxH5PayReqDTO) redisUtils.hGet(combinePaidUser(storeGuid), key);
	}

	/**
	 * 根据桌台存买单人信息，一体机或微信
	 *
	 * @return
	 */
	private String combineTablePaidUser() {
		return "tablePaidUser";
	}

	@Override
	public void saveTablePaidUser(WxStoreConsumerDTO wxStoreConsumerDTO) {
		redisUtils.hPut(BusinessName.TABLE_PAID_DETAILS+":",wxStoreConsumerDTO.getDiningTableGuid(), wxStoreConsumerDTO);
	}

	@Override
	public void delTablePaidUser(String tableGuid) {
		redisUtils.hDelete(combineTablePaidUser(), tableGuid);
	}

	@Override
	public WxStoreConsumerDTO getTablePaidUser(String tableGuid) {
		return (WxStoreConsumerDTO) redisUtils.hGet(BusinessName.TABLE_PAID_DETAILS + ":", tableGuid);
	}


	@Override
	public WxStoreConsumerDTO getBrandInfo(String storeGuid) {
		return (WxStoreConsumerDTO) redisUtils.get(BusinessName.BRAND_INFO + storeGuid);
	}

	@Override
	public void saveFirstPerson(WxStoreConsumerDTO wxStoreConsumerDTO) {
		redisUtils.set(BusinessName.FIRST_PERSON + wxStoreConsumerDTO.getOpenId(), wxStoreConsumerDTO);
	}

	@Override
	public void delFirstPerson(String openId) {
		redisUtils.delete(BusinessName.FIRST_PERSON + openId);
	}

	@Override
	public WxStoreConsumerDTO getFirstPerson(String opendId) {
		return (WxStoreConsumerDTO) redisUtils.get(BusinessName.FIRST_PERSON + opendId);
	}

	@Override
	public void saveTableToken(String tableGuid) {
		redisUtils.hPut(BusinessName.TABLE_TOKEN + ":", tableGuid, tableGuid);
	}

	@Override
	public void delTableToken(String tableGuid) {
		redisUtils.hDelete(BusinessName.TABLE_TOKEN + ":", tableGuid);
	}

	@Override
	public String getTableToken(String tableGuid) {
		return (String) redisUtils.hGet(BusinessName.TABLE_TOKEN + ":", tableGuid);
	}

	@Override
	public void savePrepay(WxPrepayReqDTO wxPrepayReqDTO) {
		redisUtils.hPut(BusinessName.PREPAY + ":" + wxPrepayReqDTO.getStoreGuid(), wxPrepayReqDTO.getOpenId(), wxPrepayReqDTO);
	}

	@Override
	public void delPrepay(String storeGuid, String openId) {
		redisUtils.hDelete(BusinessName.PREPAY + ":" + storeGuid, openId);
	}

	@Override
	public WxPrepayReqDTO getPrepay(String storeGuid, String openId) {
		return (WxPrepayReqDTO) redisUtils.hGet(BusinessName.PREPAY + ":" + storeGuid, openId);
	}

	@Override
	public ResponseMemberAndCardInfoDTO getMemberInfoAndCardList(String enterpriseGuid, String storeGuid, String openId) {
		RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO = new RequestQueryStoreAndMemberAndCard();
		queryStoreAndMemberAndCardReqDTO.setEnterpriseGuid(enterpriseGuid);
		queryStoreAndMemberAndCardReqDTO.setStoreGuid(storeGuid);
		queryStoreAndMemberAndCardReqDTO.setPhoneNumOrCardNum(openId);
		ResponseMemberAndCardInfoDTO memberInfoAndCardTwo = memberTerminalClientService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO);
		log.info("会员及当前门店卡列表详情:{}", JacksonUtils.writeValueAsString(memberInfoAndCardTwo));
		return memberInfoAndCardTwo;
	}

	@Override
	public CorrectResult<DineinOrderDetailRespDTO> orderDetails(String orderGuid) {
		Assert.hasLength(orderGuid,"当前桌台没有订单");
		try {
			return CorrectResult.<DineinOrderDetailRespDTO>builder().obj(wxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder().data(orderGuid).build())).build();
		} catch (Exception e) {
			log.info("查询订单详情失败:{}", orderGuid);
			return CorrectResult.<DineinOrderDetailRespDTO>builder().errorMsg("无法获取订单").build();
		}
	}

	@Override
	public CorrectResult<DineinOrderDetailRespDTO> calculateDetails(String orderGuid,String openId, String memberInfoCardGuid, Integer memberIntegral, String volumeCode) {
		try {
			BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
			billCalculateReqDTO.setDeviceId(openId);
			billCalculateReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
			billCalculateReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
			billCalculateReqDTO.setOrderGuid(orderGuid);
			billCalculateReqDTO.setMemberInfoCardGuid(ObjectUtils.isEmpty(memberInfoCardGuid) || "-1".equals(memberInfoCardGuid) ? null : memberInfoCardGuid);
			billCalculateReqDTO.setMemberPhone(ObjectUtils.isEmpty(memberInfoCardGuid) || "-1".equals(memberInfoCardGuid) ? null : openId);
			billCalculateReqDTO.setMemberIntegral(memberIntegral);
			billCalculateReqDTO.setMemberLogin(ObjectUtils.isEmpty(memberInfoCardGuid) || "-1".equals(memberInfoCardGuid) ? 2 :1);
			billCalculateReqDTO.setVolumeCode(ObjectUtils.isEmpty(volumeCode) || "-1".equals(volumeCode) ? null : volumeCode);
			billCalculateReqDTO.setVerify(StringUtils.isEmpty(volumeCode) || "-1".equals(volumeCode) ? null : 3);
			log.info("计算订单优惠入参:{}", billCalculateReqDTO);
			DineinOrderDetailRespDTO calculate = wxStoreDineInBillClientService.calculate(billCalculateReqDTO);

			calculate.setState(switchState(calculate.getState()));
			return calculate.getState() != 1
					? CorrectResult.<DineinOrderDetailRespDTO>builder().result(2).errorMsg("订单信息发生变化").build()
					: CorrectResult.<DineinOrderDetailRespDTO>builder().result(0).obj(calculate).build();

		} catch (Exception e) {
			e.printStackTrace();
			String message = e.getCause().getMessage();
			message = message.substring(message.lastIndexOf("\n") + 1);
			return CorrectResult.<DineinOrderDetailRespDTO>builder().result(1).errorMsg(message).build();
		}
	}

	private Integer switchState(Integer state) {
		switch (state) {
			case 1:
			case 2:
			case 3:
				state = 1;
				break;
			case 4:
				state = 2;
				break;
			case 5:
			case 6:
				state = 3;
		}
		return state;
	}

	@Override
	public CorrectResult<DineinOrderDetailRespDTO> calculateDetails2(String orderGuid, String openId, String memberInfoCardGuid, Integer memberIntegral, String volumeCode) {
		try {
			BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
			billCalculateReqDTO.setDeviceId(openId);
			billCalculateReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
			billCalculateReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
			billCalculateReqDTO.setOrderGuid(orderGuid);
			billCalculateReqDTO.setMemberInfoCardGuid(ObjectUtils.isEmpty(memberInfoCardGuid)||"-1".equals(memberInfoCardGuid)?null:memberInfoCardGuid);
			billCalculateReqDTO.setMemberPhone(ObjectUtils.isEmpty(memberInfoCardGuid)||"-1".equals(memberInfoCardGuid)?null:openId);
			billCalculateReqDTO.setMemberIntegral(ObjectUtils.isEmpty(memberInfoCardGuid)||"-1".equals(memberInfoCardGuid)?null:memberIntegral);
			billCalculateReqDTO.setMemberLogin(ObjectUtils.isEmpty(memberInfoCardGuid) ? 2 : 1);
			billCalculateReqDTO.setVolumeCode(ObjectUtils.isEmpty(volumeCode)||"-1".equals(volumeCode)?null:volumeCode);
			billCalculateReqDTO.setVerify(ObjectUtils.isEmpty(volumeCode)||"-1".equals(volumeCode)?null:1);
			log.info("计算订单优惠入参:{}",billCalculateReqDTO);
			DineinOrderDetailRespDTO calculate = wxStoreDineInBillClientService.calculate(billCalculateReqDTO);
			calculate.setState(switchState(calculate.getState()));
			return calculate.getState() != 1
					? CorrectResult.<DineinOrderDetailRespDTO>builder().result(2).errorMsg("订单信息发生变化").build()
					: CorrectResult.<DineinOrderDetailRespDTO>builder().result(0).obj(calculate).build();
		} catch (Exception e) {
			e.printStackTrace();
			String message = e.getCause().getMessage();
			message=message.substring(message.lastIndexOf("\n") + 1);
			return unCorrectResult(message);
		}
	}

	/**
	 * 友好提示异常转换
	 * @param message error
	 * @return meg
	 */
	private CorrectResult<DineinOrderDetailRespDTO> unCorrectResult(String message) {
		if (message.contains("会员不可用")) {
			return CorrectResult.<DineinOrderDetailRespDTO>builder().result(3).errorMsg("会员不可用").build();
		}
		if (message.contains("优惠券")) {
			return CorrectResult.<DineinOrderDetailRespDTO>builder().result(3).errorMsg("优惠券不可用").build();
		}
		if (message.contains("根据菜单")) {
			return CorrectResult.<DineinOrderDetailRespDTO>builder().result(3).errorMsg("会员相关菜品没有同步，请联系商家").build();
		}
		if (message.contains("只有未结账订单")) {
			return CorrectResult.<DineinOrderDetailRespDTO>builder().result(2).errorMsg("订单已经结账").build();
		}
		return CorrectResult.<DineinOrderDetailRespDTO>builder().result(3).errorMsg(message).build();
	}

	@Override
	public String getOrderGuid(String storeGuid, String tableGuid, String openId) {
		WxOrderConfigDTO detailConfig = wxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder().storeGuid(storeGuid).build());
		String orderGuid;
		if (Objects.isNull(detailConfig) || ObjectUtils.isEmpty(detailConfig.getOrderModel())) {
			throw new BusinessException("当前门店暂无配置");
		}
		if (Objects.isNull(detailConfig.getOrderModel())) {
			throw new BusinessException("当前门店没有配置正餐还是快餐");
		}
		if (detailConfig.getOrderModel() == 0) {
			orderGuid = wxStoreTableClientService.getOrderGuid(tableGuid);
			return ObjectUtils.isEmpty(orderGuid) ? getOrderGuid(tableGuid):orderGuid;
		}else{
			return getFastOrderGuid(storeGuid, openId);
		}
	}

	@Override
	public ResponseMemberInfo getMemberInfo(String enterpriseGuid, String openId) {
		log.info("enterpriseGuid{},openId{}",enterpriseGuid,openId);
		RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
		requestQueryMemberInfo.setOpenId(openId);
		ResponseMemberInfo hsmMemberInfoAndQrCodeRespDTO=hsaBaseClientService.getMemberInfo(requestQueryMemberInfo).getData();
		log.info("getMemberInfo:{}",hsmMemberInfoAndQrCodeRespDTO);
		return hsmMemberInfoAndQrCodeRespDTO;
	}

	private String getMemberInfoGuid(String enterpriseGuid,String brandGuid, String openId) {
//		String memberInfoGuid = (String) redisUtils.hGet(BusinessName.MEMBER_INFO_GUID + ":" + brandGuid, openId);
//		if (StringUtils.isEmpty(memberInfoGuid)) {
		ResponseMemberInfo memberInfo = getMemberInfo(enterpriseGuid, openId);
		if (ObjectUtils.isEmpty(memberInfo) || StringUtils.isEmpty(memberInfo.getMemberInfoGuid())) {
			throw new BusinessException("未查询到会员信息:{}"+openId);
		}
//			memberInfoGuid = memberInfo.getMemberInfoGuid();
//			redisUtils.hPut2(BusinessName.MEMBER_INFO_GUID + ":" + brandGuid, openId,memberInfoGuid);
//		}
		return memberInfo.getMemberInfoGuid();
	}

	@Override
	public ResponseMemberCardAll getMemberCard(String enterpriseGuid,String brandGuid, String openId) {
        String memberInfoGuid = getMemberInfoGuid(enterpriseGuid, brandGuid, openId);
        if(StringUtils.isEmpty(memberInfoGuid)){
            return null;
        }
		RequestQueryMemberCardList memberCardListQueryReqDTO = new RequestQueryMemberCardList();
		memberCardListQueryReqDTO.setMemberInfoGuid(memberInfoGuid);
		memberCardListQueryReqDTO.setBrandGuid(brandGuid);
		memberCardListQueryReqDTO.setEnterpriseGuid(enterpriseGuid);
		log.info("查询开通和未开通会员卡入参:{}",memberCardListQueryReqDTO);
//		MemberCardRespDTO memberCard = memberClientService.getMemberCard(memberCardListQueryReqDTO);
		ResponseMemberCardAll memberCard = hsaBaseClientService.getMemberCard(memberCardListQueryReqDTO).getData();
		log.info("查询登录会员卡:{}",memberCard);
		return memberCard;
	}

	@Override
	public Integer getMemberValidVolumeNumber(String enterpriseGuid,String brandGuid, String openId) {
		String memberInfoGuid = getMemberInfoGuid(enterpriseGuid, brandGuid, openId);
		if(StringUtils.isEmpty(memberInfoGuid)){
		    return 0;
        }
		RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
		memberInfoVolumeQueryReqDTO.setMemberInfoGuid(memberInfoGuid);
		memberInfoVolumeQueryReqDTO.setVolumeType(0);
		memberInfoVolumeQueryReqDTO.setEnterpriseGuid(enterpriseGuid);
		memberInfoVolumeQueryReqDTO.setBrandGuid(brandGuid);
		memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
		//全部门店传“”
		memberInfoVolumeQueryReqDTO.setStoreGuid("");
		return hsaBaseClientService.getMemberValidVolumeNumber(memberInfoVolumeQueryReqDTO).getData();
	}

	@Override
	public BrandDTO getBrandDetail(String brandGuid){
		BrandDTO brandDTO = organizationClientService.queryBrandByGuid(brandGuid);
		if (brandDTO == null) {
			throw new BusinessException("无法查询到品牌信息:" + brandGuid);
		}
		return brandDTO;
	}

	@Override
	public TableDTO getTableDetail(String tableGuid) {
		TableDTO tableDTO =  wxStoreTableClientService.getTableByGuid(tableGuid);
		if (tableDTO == null){
			throw new BusinessException("无法查询到桌台信息:" + tableGuid);
		}
		return tableDTO;
	}

	@Override
	public StoreDTO getStoreDetail(String storeGuid) {
		StoreDTO	storeDTO = organizationClientService.queryStoreByGuid(storeGuid);
		if (storeDTO == null) {
			throw new BusinessException("无法查询到门店信息:" + storeGuid);
		}
		return storeDTO;
	}

	@Override
	public BrandDTO getBrandInfoDetails(String storeGuid) {
		BrandDTO brandDTO = organizationClientService.queryBrandByStoreGuid(storeGuid);
		if (brandDTO == null) {
			throw new BusinessException("无法查询到品牌信息:" + storeGuid);
		}
		return brandDTO;
	}

	@Override
	public EnterpriseDTO getEnterpriseDetail() {
		String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
		log.info("查询企业详情:{}",enterpriseGuid);
		EnterpriseDTO enterpriseDTO = null;
		Boolean result = false;
		//缓存查询异常 降级查询数据库
		try {
			enterpriseDTO = (EnterpriseDTO) redisUtils.get(BusinessName.ENTERPRISE_DETAILS + ":" + enterpriseGuid);
		}catch (Exception e){
			result = true;
			log.info("企业信息查询缓存异常");
		}
		if (ObjectUtils.isEmpty(enterpriseDTO) || result) {
			BaseDTO baseDTO = new BaseDTO();
			baseDTO.setEnterpriseGuid(enterpriseGuid);
			enterpriseDTO = enterpriseClientService.findEnterprise(baseDTO);
			if (ObjectUtils.isEmpty(enterpriseDTO) || StringUtils.isEmpty(enterpriseDTO.getName())) {
				log.error("enterpriseDetails:{}",enterpriseDTO);
				throw new BusinessException("无法查询到企业详情:" + enterpriseGuid);
			}
			redisUtils.set(BusinessName.ENTERPRISE_DETAILS+":"+enterpriseGuid,enterpriseDTO);
		}
		return enterpriseDTO;
	}

	@Override
	public void savePayCallBack(String orderGuid, WeChatPayReqDTO weChatPayReqDTO) {
		log.info("支付下单dine缓存:{}", orderGuid);
		redisUtils.set("WX:CALCULATE:" + orderGuid, weChatPayReqDTO);
	}

	@Override
	public void delPayCallBack(String orderGuid) {
		redisUtils.delete("WX:CALCULATE:" + orderGuid);
	}

	@Override
	public WeChatPayReqDTO getPayCallBack(String orderGuid) {
		return (WeChatPayReqDTO) redisUtils.get("WX:CALCULATE:" + orderGuid);
	}

	@Override
	public BrandStoreDetailDTO getStoreBrandDetail(String storeGuid, String brandGuid) {
		BrandStoreDetailDTO brandStoreDetailDTO = organizationClientService.queryStoreBrandDetail(storeGuid, brandGuid);
		if (brandStoreDetailDTO == null) {
			throw new BusinessException("无法查询到门店品牌信息");
		}
		return brandStoreDetailDTO;
	}
}
