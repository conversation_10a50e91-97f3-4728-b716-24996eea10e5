package com.holderzone.saas.store.weixin.event;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.dto.table.TableStatusChangeDTO;
import com.holderzone.saas.store.enums.table.TableStatusChangeEnum;
import com.holderzone.saas.store.weixin.constant.RocketMqConfig;
import com.holderzone.saas.store.weixin.service.WxStoreOrderStatusChangeService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
@RocketListenerHandler(
		topic = RocketMqConfig.TABLE_STATUS_CHANGE_MQ_TOPIC,
		tags = RocketMqConfig.TABLE_STATUS_CHANGE_MQ_TAG,
		consumerGroup = RocketMqConfig.ORDER_STATUS_CHANGE_MQ_GROUP
)
public class WxStoreOrderStatusChangeListener extends AbstractRocketMqConsumer<RocketMqTopic, TableStatusChangeDTO> {

	@Autowired
	private WxStoreOrderStatusChangeService wxStoreOrderStatusChangeService;

	@Autowired
	private DynamicHelper dynamicHelper;

	@Override
	public boolean consumeMsg(TableStatusChangeDTO tableStatusChangeDTO, MessageExt messageExt) {
		boolean result=true;
		try{
			Integer tableStatusChange = tableStatusChangeDTO.getTableStatusChange();
			if (Objects.equals(tableStatusChange, TableStatusChangeEnum.CLOSE_WEIXIN_NOTIFY.getId())
					||Objects.equals(tableStatusChange, TableStatusChangeEnum.CHECKOUT.getId())) {
				dynamicHelper.changeDatasource(tableStatusChangeDTO.getEnterpriseGuid());
				wxStoreOrderStatusChangeService.complete(tableStatusChangeDTO);
			}
		}catch(Exception e){
			log.error("",e);
		    result=false;
		}
		return result;
	}
}
