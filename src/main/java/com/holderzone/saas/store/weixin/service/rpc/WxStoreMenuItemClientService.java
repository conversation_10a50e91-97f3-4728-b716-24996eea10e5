package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMenuItemClientService
 * @date 2019/2/22 14:14
 * @description 微信点餐调用获取商品菜单及分类信息
 * @package com.holderzone.saas.store.weixin.service
 */
@Service
@FeignClient(name = "holder-saas-store-item", fallbackFactory = WxStoreMenuItemClientService.WxMenuFallBack.class)
public interface WxStoreMenuItemClientService {
    /**
     * @describe 调用item服务，获取菜品
     * <AUTHOR>
     * @date 2019/2/23 15:31
     */
    @PostMapping("/item/query_for_synchronize")
    ItemAndTypeForAndroidRespDTO getItemsForWeixin(BaseDTO baseDTO);

    /**
     * @param itemSingleDTO
     * @return
     * @describe 调用item获取菜品详情
     */
    @PostMapping("/item/get_item_info")
    ItemInfoRespDTO getItemInfo(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 根据规格guid查询没有加入微信点餐的商品
     *
     * @param skuGuidList 规格guid列表
     * @return 没有加入微信点餐的商品
     */
    @ApiOperation(value = "根据规格guid查询没有加入微信点餐的商品")
    @PostMapping("/item_sku/query_not_join_wechat_item")
    List<String> queryNotJoinWechatItem(@RequestBody List<String> skuGuidList);

    @Slf4j
    @Component
    class WxMenuFallBack implements FallbackFactory<WxStoreMenuItemClientService> {
        @Override
        public WxStoreMenuItemClientService create(Throwable throwable) {
            return new WxStoreMenuItemClientService() {

                @Override
                public ItemAndTypeForAndroidRespDTO getItemsForWeixin(BaseDTO baseDTO) {
                    log.error("获取商品信息失败", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public ItemInfoRespDTO getItemInfo(ItemSingleDTO itemSingleDTO) {
                    log.error("获取商品详细信息失败", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<String> queryNotJoinWechatItem(List<String> skuGuidList) {
                    log.error("查询没有加入微信点餐的商品失败", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }

}
