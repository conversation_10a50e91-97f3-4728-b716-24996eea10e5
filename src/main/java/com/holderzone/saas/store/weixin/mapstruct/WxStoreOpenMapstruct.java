package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.resp.WxConfigRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.open.bean.auth.WxOpenAuthorizationInfo;
import me.chanjar.weixin.open.bean.auth.WxOpenAuthorizerInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreOpenMapstruct
 * @date 2019/02/28 12:09
 * @description 微信开放平台mapstruct
 * @program holder-saas-store
 */
@Mapper(componentModel = "spring")
@Component
public interface WxStoreOpenMapstruct {

    @Mapping(target = "funcInfo", ignore = true)
    WxStoreAuthorizerInfoDO authorizerInfo2DO(WxOpenAuthorizerInfo wxOpenAuthorizerInfo, WxOpenAuthorizationInfo wxOpenAuthorizationInfo);

    @Mappings({
            @Mapping(target = "wxCommonReqDTO.signature", source = "wxJsapiSignature.signature"),
            @Mapping(target = "wxCommonReqDTO.nonce", source = "wxJsapiSignature.nonceStr"),
            @Mapping(target = "wxCommonReqDTO.timestamp", source = "wxJsapiSignature.timestamp")
    })
    WxConfigRespDTO jsapiSignature2RespDTO(WxJsapiSignature wxJsapiSignature);
}
