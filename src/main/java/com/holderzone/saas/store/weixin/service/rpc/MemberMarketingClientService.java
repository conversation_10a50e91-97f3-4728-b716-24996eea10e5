package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.feign.FeignModel;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ConsumptionGiftDetailDTO;
import com.holderzone.holder.saas.member.wechat.dto.activitie.MemberConsumptionGiftDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 新会员 营销中心
 **/
@Component
@FeignClient(name = "holder-member-marketing", fallbackFactory = MemberMarketingClientService.ServiceFallBack.class, url = "${member.marketing.host}")
public interface MemberMarketingClientService {

    /**
     * 企微 - 用户授权
     */
    @GetMapping(value = "/marketing/wx_cp/query/authorize_url")
    FeignModel<String> queryWxCpAuthorizeUrl(@RequestParam("operSubjectGuid") String operSubjectGuid,
                                             @RequestParam("params") String params);

    /**
     * 计算消费赠送处理
     * @param memberConsumptionGiftQO memberConsumptionGiftQO
     */
    @PostMapping("/marketing/consumption/activity/deal/consumption/gift")
    void dealConsumptionGift(@RequestBody MemberConsumptionGiftDTO memberConsumptionGiftQO);

    /**
     * 查询订单优惠
     * @param orderNumber
     * @return
     */
    @GetMapping("/marketing/consumption/activity/get/order/gift")
    ConsumptionGiftDetailDTO getOrderGift(@RequestParam("orderNumber") String orderNumber);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberMarketingClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MemberMarketingClientService create(Throwable throwable) {

            return new MemberMarketingClientService() {

                @Override
                public FeignModel<String> queryWxCpAuthorizeUrl(String operSubjectGuid, String params) {
                    log.info(HYSTRIX_PATTERN, "方法：queryWxCpAuthorizeUrl， 调用会员营销服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , operSubjectGuid, params, throwable);
                    throw new BusinessException("企微 - 用户授权失败");
                }

                @Override
                public void dealConsumptionGift(MemberConsumptionGiftDTO memberConsumptionGiftQO) {
                    log.info(HYSTRIX_PATTERN, "方法：dealConsumptionGift， 调用会员营销服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , JacksonUtils.writeValueAsString(memberConsumptionGiftQO), throwable);
                    throw new BusinessException("计算消费有礼赠送处理失败");
                }

                @Override
                public ConsumptionGiftDetailDTO getOrderGift(String orderNumber) {
                    log.info(HYSTRIX_PATTERN, "方法：getOrderGift， 调用会员营销服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , orderNumber, throwable);
                    throw new BusinessException("查询订单优惠处理失败");
                }
            };
        }
    }
}
