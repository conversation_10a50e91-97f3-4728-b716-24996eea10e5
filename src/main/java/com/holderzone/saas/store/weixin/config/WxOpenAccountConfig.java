package com.holderzone.saas.store.weixin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WechatOpenAccountConfig
 * @date 2019/02/23 10:53
 * @description 微信开放平台账号配置
 * @program holder-saas-store-weixin
 */
@Data
@Component
@ConfigurationProperties(prefix = "wechat.open")
@RefreshScope
public class WxOpenAccountConfig {
    private String componentAppId;

    private String componentSecret;

    private String componentToken;

    private String componentAesKey;


}
