package com.holderzone.saas.store.weixin.service.impl;

import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpMessageHandler;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.*;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxOpenMessageServiceImpl
 * @date 2019/01/17 10:14
 * @description 微信消息服务具体实现类
 * @program holder-saas-store-weixin
 */
//@Slf4j
@Service
public class WxMpMessageHandlerImpl implements WxMpMessageHandler {


    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMpXmlMessage, Map<String, Object> map,
                                    WxMpService wxMpService, WxSessionManager wxSessionManager) throws WxErrorException {
        String msgType = wxMpXmlMessage.getMsgType();
        switch (msgType) {
            case WxConsts.XmlMsgType.EVENT:
                return handleEventMessage(wxMpXmlMessage);
            case WxConsts.XmlMsgType.TEXT:
                return handleTextMessage(wxMpXmlMessage);
            case WxConsts.XmlMsgType.IMAGE:
                return handleImageMessage(wxMpXmlMessage);
            case WxConsts.XmlMsgType.VOICE:
                return handleVoiceMessage(wxMpXmlMessage);
            default:
                return null;
        }
    }

    // 处理文本消息
    private WxMpXmlOutMessage handleTextMessage(WxMpXmlMessage wxMpXmlMessage) {
        return createTextResponse(wxMpXmlMessage, wxMpXmlMessage.getContent());
    }

    // 处理语音消息
    private WxMpXmlOutMessage handleVoiceMessage(WxMpXmlMessage wxMpXmlMessage) {
        return createVoiceResponse(wxMpXmlMessage, wxMpXmlMessage.getMediaId());
    }

    // 处理图片消息
    private WxMpXmlOutMessage handleImageMessage(WxMpXmlMessage wxMpXmlMessage) {
        return createImageResponse(wxMpXmlMessage, "b51NZq0WFhxx0GX29CD_iBuDMcd7zfXYd7c62-nafawE4u074jUFNfszjUfYjZG4");
    }

    // 处理事件消息
    private WxMpXmlOutMessage handleEventMessage(WxMpXmlMessage wxMpXmlMessage) {
        String event = wxMpXmlMessage.getEvent();
        if (WxConsts.EventType.SUBSCRIBE.equals(event)) {
            return createTextResponse(wxMpXmlMessage, "感谢您的关注！");
        } else if (WxConsts.EventType.LOCATION.equals(event)) {
            String content = "已获得您的地理位置：（" + wxMpXmlMessage.getLatitude() + ", " + wxMpXmlMessage.getLongitude() + ")";
            return createTextResponse(wxMpXmlMessage, content);
        }
        return null;
    }

    // 创建文本响应
    private WxMpXmlOutTextMessage createTextResponse(WxMpXmlMessage wxMpXmlMessage, String content) {
        WxMpXmlOutTextMessage response = new WxMpXmlOutTextMessage();
        setCommonResponseFields(response, wxMpXmlMessage);
        response.setMsgType(WxConsts.XmlMsgType.TEXT);
        response.setContent(content);
        return response;
    }

    // 创建语音响应
    private WxMpXmlOutVoiceMessage createVoiceResponse(WxMpXmlMessage wxMpXmlMessage, String mediaId) {
        WxMpXmlOutVoiceMessage response = new WxMpXmlOutVoiceMessage();
        setCommonResponseFields(response, wxMpXmlMessage);
        response.setMsgType(WxConsts.XmlMsgType.VOICE);
        response.setMediaId(mediaId);
        return response;
    }

    // 创建图片响应
    private WxMpXmlOutImageMessage createImageResponse(WxMpXmlMessage wxMpXmlMessage, String mediaId) {
        WxMpXmlOutImageMessage response = new WxMpXmlOutImageMessage();
        setCommonResponseFields(response, wxMpXmlMessage);
        response.setMsgType(WxConsts.XmlMsgType.IMAGE);
        response.setMediaId(mediaId);
        return response;
    }

    // 设置响应的公共字段
    private void setCommonResponseFields(WxMpXmlOutMessage response, WxMpXmlMessage wxMpXmlMessage) {
        response.setCreateTime(System.currentTimeMillis());
        response.setFromUserName(wxMpXmlMessage.getToUser());
        response.setToUserName(wxMpXmlMessage.getFromUser());
    }
}
