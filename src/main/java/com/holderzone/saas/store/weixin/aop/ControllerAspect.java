package com.holderzone.saas.store.weixin.aop;

import com.alibaba.fastjson.JSON;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.undertow.servlet.spec.HttpServletRequestImpl;
import io.undertow.servlet.spec.HttpServletResponseImpl;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ControllerAspect
 * @date 2018/10/16 上午10:56
 * @description //controller切面
 * @program holder-saas-aggregation-merchant
 */
@Aspect
@Component
@Slf4j
public class ControllerAspect {
	
	@Value(value = "${log.enable.controller:true}")
	private Boolean logEnable = true;

	@Resource
    HttpServletRequest request;
	
    @Pointcut("execution(* com.holderzone.saas.store.weixin.controller.*.*(..))")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void doBefore(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                if (arg instanceof BaseDTO) {
                    if (!StringUtils.isEmpty(UserContextUtils.getEnterpriseGuid())) {
                        ((BaseDTO) arg).setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
                    }
                    if (!StringUtils.isEmpty(UserContextUtils.getUserGuid())) {
                        ((BaseDTO) arg).setUserGuid(UserContextUtils.getUserGuid());
                    }
                    if (!StringUtils.isEmpty(UserContextUtils.getUserGuid())) {
                        ((BaseDTO) arg).setUserName(UserContextUtils.getUserName());
                    }
                    if (!StringUtils.isEmpty(UserContextUtils.getUserAccount())) {
                        ((BaseDTO) arg).setAccount(UserContextUtils.getUserAccount());
                    }
                    if (!StringUtils.isEmpty(UserContextUtils.getStoreGuid())) {
                        ((BaseDTO) arg).setStoreGuid(UserContextUtils.getStoreGuid());
                    }
                }
            }
        }
    }
    
    @Around("pointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
    	if(logEnable.booleanValue()) {
    		try {
        		log.info("mothed:{},URI:{}",request.getMethod(),request.getRequestURI());
    	        log.info("controller:{}",point.getSignature());
    	        Object[] args = point.getArgs();
    	        List<Object> argObjs = new ArrayList<>();
    	        for(Object obj:args) {
    	        	boolean isHttp = obj instanceof  HttpServletResponseImpl || obj instanceof  HttpServletRequestImpl;
    	        	if(!isHttp) {
    	        		argObjs.add(obj);
    	        	}
    	        }
    	        log.info("param:{}", JacksonUtils.writeValueAsString(argObjs));
        	}catch (Exception e) {
    			log.error("logger日志输出异常",e);
    		}
    	}
    	Object result = null;
    	long beginTime = System.currentTimeMillis();
    	try {
    		result = point.proceed();
    	}catch (Throwable e) {
			log.error("",e);
			throw e;
		}
    	if(!logEnable.booleanValue()) {
    		return result;
    	}
    	long time = System.currentTimeMillis() - beginTime;
        try {
        	log.info("uri:{},耗时:{}ms，return:{}",request.getRequestURI(),time, JSON.toJSONString(result));
        }catch (Exception e) {
        	log.error("logger日志输出异常",e);
		}
        return result;
       
    }
}
