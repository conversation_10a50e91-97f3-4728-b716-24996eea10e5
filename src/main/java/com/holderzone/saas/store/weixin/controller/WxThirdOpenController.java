package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.open.WxOpenAuthDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPreCodReqDTO;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.service.WxStoreComponentConfigService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxComponentController
 * @date 2019/02/23 11:36
 * @description 微信开放平台相关Controller
 * @program holder-saas-store-weixin
 */
@RestController
@RequestMapping("/wx_open")
@Api(value = "微信开放平台相关Controller")
@Slf4j
public class  WxThirdOpenController {

    @Autowired
    WxStoreComponentConfigService wxThirdOpenService;

    @Autowired
    WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;

    @Autowired
    DynamicHelper dynamicHelper;

    @PostMapping("/receive_ticket")
    @ApiOperation(value = "接收微信方推送的component_verify_ticket，验证后直接返回success字符串")
    public String receiveVerifyTicket(@RequestBody WxCommonReqDTO wxCommonReqDTO) {
        return wxThirdOpenService.receiveTicket(wxCommonReqDTO);
    }

    @PostMapping("/get_pre_code")
    public String getPreCode(@RequestBody WxPreCodReqDTO wxPreCodReqDTO) throws WxErrorException {
        log.info("微信服务已收到预授权请求参数：{}", JacksonUtils.writeValueAsString(wxPreCodReqDTO));
        return wxStoreAuthorizerInfoService.getPreAuthCode(wxPreCodReqDTO);
    }

    @PostMapping("/query_auth")
    public String queryAuth(@RequestBody WxOpenAuthDTO wxOpenAuthDTO) throws WxErrorException {
        log.info("已收到微信公众号授权回调：授权码：{}，品牌guid：{}，授权码过期时间： {}"
                , wxOpenAuthDTO.getAuth_code(), wxOpenAuthDTO.getBrandGuid(), wxOpenAuthDTO.getExpires_in());
        //手动切库
        log.info("手动切库，enterpriseGuid：{}", wxOpenAuthDTO.getEnterpriseGuid());
        dynamicHelper.changeDatasource(wxOpenAuthDTO.getEnterpriseGuid());
        try {
            return wxStoreAuthorizerInfoService.queryAuth(wxOpenAuthDTO);
        } catch (UnsupportedEncodingException e) {
            throw new BusinessException("编码错误，请稍后重试");
        }
    }
}
