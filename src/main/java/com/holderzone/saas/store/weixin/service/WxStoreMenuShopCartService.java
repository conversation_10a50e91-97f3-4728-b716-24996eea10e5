package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerShoppingCartDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreShoppingCartDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreConsumerCartItemReqDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 微信购物车实现层
 * @className WxStoreMenuShopCartService
 * @date 2019/3/1
 */
public interface WxStoreMenuShopCartService {
    /**
     * @param wxStoreConsumerDTO
     * @describle 创建购物车
     */
    WxStoreShoppingCartDTO createShoppingCart(WxStoreConsumerDTO wxStoreConsumerDTO);

    /**
     * @param wxStoreConsumerDTO
     * @return
     * @describle 获取当前用户在这家门店的购物车
     */
    WxStoreShoppingCartDTO getWxStoreShoppingCart(WxStoreConsumerDTO wxStoreConsumerDTO);

    /**
     * @param wxStoreConsumerShoppingCartDTO
     * @describle 更新购物车
     */
    WxStoreShoppingCartDTO updateWxStoreShoppingCart(WxStoreConsumerShoppingCartDTO wxStoreConsumerShoppingCartDTO);

    /**
     * @param wxStoreConsumerCartItemReqDTO
     * @describle 新增商品项
     */
    WxStoreShoppingCartDTO createCartItem(WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO);

    /**
     * @param wxStoreConsumerCartItemReqDTO
     * @describle 修改商品项属性
     */
    WxStoreShoppingCartDTO updateCartItem(WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO);

    /**
     * |@describle	删除商品项
     *
     * @param wxStoreConsumerCartItemReqDTO
     */
    WxStoreShoppingCartDTO delCartItem(WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO);

}
