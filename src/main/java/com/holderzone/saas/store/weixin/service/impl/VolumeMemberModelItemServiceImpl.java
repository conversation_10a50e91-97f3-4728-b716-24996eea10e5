package com.holderzone.saas.store.weixin.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.AbstractMemberModelItemService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class VolumeMemberModelItemServiceImpl extends AbstractMemberModelItemService{

	@Autowired
	private WxStoreSessionDetailsService wxStoreSessionDetailsService;
	
	@Autowired
	MemberClientService memberClientService;

	@Override
	public WxMemberOverviewModelDTO getWxMemberOverviewModel(WxStoreConsumerDTO wxStoreConsumerDTO) {
		//memberClientService.getMemberValidVolumeNumber(memberInfoVolumeQueryReqDTO)
		Integer count = wxStoreSessionDetailsService.getMemberValidVolumeNumber(wxStoreConsumerDTO.getEnterpriseGuid()
				, wxStoreConsumerDTO.getBrandGuid(), wxStoreConsumerDTO.getOpenId());
		if(count==null) {
			count = 0;
		}
		return new WxMemberOverviewModelDTO(1, "我的券", (int)count);
	}
}
