package com.holderzone.saas.store.weixin.aop;

import com.holderzone.saas.store.dto.weixin.SubmitReturnDTO;
import com.holderzone.saas.store.weixin.annotation.TableToken;
import com.holderzone.saas.store.weixin.service.WxStoreMenuDetailsService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Method;

@Component
@Aspect
@Slf4j
public class TableTokenAspect {

	private final WxStoreSessionDetailsService wxStoreSessionDetailsService;

	private final LocalVariableTableParameterNameDiscoverer localVariableTableParameterNameDiscoverer;

	private final SpelExpressionParser spelExpressionParser;

	private final StandardEvaluationContext standardEvaluationContext;

	private final WxStoreMenuDetailsService wxStoreMenuDetailsService;

	private final DynamicHelper dynamicHelper;

	@Autowired
	public TableTokenAspect(WxStoreSessionDetailsService wxStoreSessionDetailsService, LocalVariableTableParameterNameDiscoverer localVariableTableParameterNameDiscoverer, SpelExpressionParser spelExpressionParser, StandardEvaluationContext standardEvaluationContext, WxStoreMenuDetailsService wxStoreMenuDetailsService, DynamicHelper dynamicHelper) {
		this.wxStoreSessionDetailsService = wxStoreSessionDetailsService;
		this.localVariableTableParameterNameDiscoverer = localVariableTableParameterNameDiscoverer;
		this.spelExpressionParser = spelExpressionParser;
		this.standardEvaluationContext = standardEvaluationContext;
		this.wxStoreMenuDetailsService = wxStoreMenuDetailsService;
		this.dynamicHelper = dynamicHelper;
	}

	@Around("@annotation(tableToken)")
	public SubmitReturnDTO around(ProceedingJoinPoint proceedingJoinPoint,TableToken tableToken){
		String table = null;
		try {
			Object[] args = proceedingJoinPoint.getArgs();
			MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
			Method method = signature.getMethod();
			String[] parameterNames = localVariableTableParameterNameDiscoverer.getParameterNames(method);
			String tableGuid = tableToken.tableGuid();
			String storeGuid = tableToken.storeGuid();
			String enterpriseGuid = tableToken.enterpriseGuid();
			if (!ObjectUtils.isEmpty(args)&& !ObjectUtils.isEmpty(parameterNames)) {
				for(int i=0;i<parameterNames.length;i++) {
					standardEvaluationContext.setVariable(parameterNames[i],args[i]);
				}
				table = spelExpressionParser.parseExpression(tableGuid).getValue(standardEvaluationContext, String.class);
				String store = spelExpressionParser.parseExpression(storeGuid).getValue(standardEvaluationContext, String.class);
				String enterprise = spelExpressionParser.parseExpression(enterpriseGuid).getValue(standardEvaluationContext, String.class);
				log.info("table:{}",table);
				if (!ObjectUtils.isEmpty(table)&&!ObjectUtils.isEmpty(store)&& !ObjectUtils.isEmpty(enterprise)) {
					dynamicHelper.changeDatasource(enterprise);
					if (wxStoreMenuDetailsService.judgeOrderType(store)) {
						String token = wxStoreSessionDetailsService.getTableToken(table);
						if (!StringUtils.isEmpty(token)) {
							return SubmitReturnDTO.builder().errorCode(0).errorMsg("有人正在下单，请勿重复下单").build();
						}
						wxStoreSessionDetailsService.saveTableToken(table);
					}
				}
			}
			return (SubmitReturnDTO) proceedingJoinPoint.proceed();
		} catch (Throwable throwable) {
			throwable.printStackTrace();
			return SubmitReturnDTO.builder().errorMsg("订单服务异常，请稍后再试").errorCode(0).build();
		}finally {
			if (!StringUtils.isEmpty(table)) {
				wxStoreSessionDetailsService.delTableToken(table);
			}
		}
	}
}
