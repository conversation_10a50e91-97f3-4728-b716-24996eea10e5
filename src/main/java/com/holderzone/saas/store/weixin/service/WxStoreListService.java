package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.WxPositionDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreListDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreCityListRespDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreListService
 * @date 2019/05/17 20:03
 * @description 微信门店功能及信息列表Service
 * @program holder-saas-store
 */
public interface WxStoreListService {
    WxStoreListDTO listStoreConfig(WxPositionDTO wxPositionDTO);

    WxStoreCityListRespDTO listStoreCity(WxPositionDTO wxPositionDTO);
}
