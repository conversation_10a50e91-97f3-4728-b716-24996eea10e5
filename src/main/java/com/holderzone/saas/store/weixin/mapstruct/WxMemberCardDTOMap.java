package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.saas.store.dto.weixin.member.WxMemberCardDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface WxMemberCardDTOMap {

	WxMemberCardDTOMap INSTANCE = Mappers.getMapper(WxMemberCardDTOMap.class);


//	List<WxMemberCardDTO> fromMemberCardList(List<MemberCardListOwnedRespDTO> memberCardListOwnedRespDTOS);
//
//	List<WxMemberCardDTO> fromMemberCardListResp(List<MemberCardRespDTO> memberCardRespDTOS);
//
//	WxMemberCardDTO fromMemberCard(MemberCardListOwnedRespDTO memberCardListOwnedRespDTOS);
//
//	@Mappings({
//			@Mapping(target = "validityType",source = "cardValidityType")
//	})
//	WxMemberCardDTO fromMemberCardResp(MemberCardRespDTO memberCardRespDTO);


	List<WxMemberCardDTO> fromNewMemberCardListResp(List<ResponseMemberCard> memberCardRespDTOS);

	@Mappings({
			@Mapping(target = "validityType",source = "cardValidityType")
	})
	WxMemberCardDTO fromNewMemberCardResp(ResponseMemberCard memberCardRespDTO);
}
