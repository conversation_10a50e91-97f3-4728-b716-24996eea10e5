package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.menu.WxMenuUrlDTO;
import com.holderzone.saas.store.dto.weixin.open.WxMessageHandleReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxMpTemplateDTO;
import com.holderzone.saas.store.dto.weixin.open.WxSendMessageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.TempMsgCreateDTO;
import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxSubjectReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxConfigRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxSubjectRespDTO;
import com.holderzone.saas.store.weixin.service.WxMpTemplateService;
import com.holderzone.saas.store.weixin.service.WxOpenMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMpHandler
 * @date 2019/03/26 17:38
 * @description 微信第三方平台待公众号处理消息
 * @program holder-saas-store
 */
@RestController
@RequestMapping("/wx_handler")
@Api(description = "用以代公众号处理微信消息")
@Slf4j
public class WxOpenMessageHandler {
    @Autowired
    WxOpenMessageService wxOpenMessageService;

    @Autowired
    WxMpTemplateService wxMpTemplateService;

    @PostMapping("/handle_message")
    @ApiOperation("代公众号处理消息")
    public String handleMessage(@RequestBody WxMessageHandleReqDTO wxMessageHandleReqDTO) throws WxErrorException {
        return wxOpenMessageService.getWxMpXmlMessage(wxMessageHandleReqDTO);
    }

    //微信点餐带参二维码获取用户信息
    @PostMapping("/get_user_info")
    @ApiOperation("用户授权回调")
    public String getUserInfo(@RequestBody WxAuthorizeReqDTO wxAuthorizeReqDTO) throws WxErrorException {
        return wxOpenMessageService.getUserInfo(wxAuthorizeReqDTO);
    }

    @PostMapping("/create_msg_temp")
    @ApiOperation("创建消息模板")
    public void createMsgTemp(@RequestBody TempMsgCreateDTO tempMsgCreateDTO) throws WxErrorException {
        wxOpenMessageService.createQueueMsgTemp(tempMsgCreateDTO);
    }

    @PostMapping("/shop_list")
    public String shopList(@RequestBody WxPortalReqDTO wxPortalReqDTO) throws WxErrorException {
        log.info("微信服务：跳转至门店列表页面请求参数：{}", JacksonUtils.writeValueAsString(wxPortalReqDTO));
        return wxOpenMessageService.shopList(wxPortalReqDTO);
    }

    @PostMapping("/wx_config")
    public WxConfigRespDTO getWxConfig(@RequestBody WxPortalReqDTO wxPortalReqDTO) throws WxErrorException {
        log.info("获取微信jsSdk请求参数：{}", JacksonUtils.writeValueAsString(wxPortalReqDTO));
        return wxOpenMessageService.generateDTO(wxPortalReqDTO);
    }

    @PostMapping("/login")
    public String memberLogin(@RequestBody WxPortalReqDTO wxPortalReqDTO) throws WxErrorException {
        log.info("会员登录请求入参：{}", JacksonUtils.writeValueAsString(wxPortalReqDTO));
        return wxOpenMessageService.memberLogin(wxPortalReqDTO);
    }

    @PostMapping("/new_member_login")
    public String newMemberLogin(@RequestBody WxPortalReqDTO wxPortalReqDTO) throws WxErrorException {
        log.info("会员登录请求入参：{}", JacksonUtils.writeValueAsString(wxPortalReqDTO));
        return wxOpenMessageService.newMemberLogin(wxPortalReqDTO);
    }

    @PostMapping("/open_member_login_jump_url")
    public String openMemberLogin(@RequestBody WxPortalReqDTO wxPortalReqDTO) {
        log.info("开放API会员登录请求入参：{}", JacksonUtils.writeValueAsString(wxPortalReqDTO));
        return wxOpenMessageService.openMemberLogin(wxPortalReqDTO);
    }

    @PostMapping("/get_wx_subject")
    public WxSubjectRespDTO getWxSubject(@RequestBody WxSubjectReqDTO wxSubjectReqDTO) {
        log.info("获取微信运营主体请求参数：{}", JacksonUtils.writeValueAsString(wxSubjectReqDTO));
        return wxOpenMessageService.getWxSubject(wxSubjectReqDTO);
    }

    /**
     * 微信公众号 获取老板助手授权地址
     */
    @PostMapping("/menu_authorize_url/boss")
    public String getBossAuthorizeUrl(@RequestBody WxMenuUrlDTO wxMenuUrlDTO) {
        log.info("微信公众号: 获取老板助手授权地址请求入参，wxMenuUrlDTO: {}", wxMenuUrlDTO);
        return wxOpenMessageService.getBossAuthorizeUrl(wxMenuUrlDTO);
    }

    /**
     * 微信公众号 获取老板助手项目地址
     */
    @PostMapping("/menu_redirect_url/boss")
    public String getBossRedirectUrl(@RequestBody WxAuthorizeReqDTO wxAuthorizeReqDTO) {
        log.info("微信公众号: 获取老板助手重定向地址请求入参，wxAuthorizeReqDTO: {}", wxAuthorizeReqDTO);
        String bossRedirectUrl = wxOpenMessageService.getBossRedirectUrl(wxAuthorizeReqDTO);
        log.info("微信公众号: 老板助手重定向地址:{}", bossRedirectUrl);
        return bossRedirectUrl;
    }

    /**
     * 微信公众号 保存老板助手登录token
     */
    @PostMapping("/save/boss/token")
    public void saveBossAuthToken(@RequestBody WxMenuUrlDTO wxMenuUrlDTO) {
        log.info("微信公众号: 保存老板助手token请求入参，wxMenuUrlDTO: {}", wxMenuUrlDTO);
        wxOpenMessageService.saveBossAuthToken(wxMenuUrlDTO);
    }


    /**
     * 微信公众号 清除老板助手登录token
     */
    @PostMapping("/clean/boss/token")
    public void cleanBossAuthToken(@RequestBody WxMenuUrlDTO wxMenuUrlDTO) {
        log.info("微信公众号: 清除老板助手token请求入参，wxMenuUrlDTO: {}", wxMenuUrlDTO);
        wxOpenMessageService.cleanBossAuthToken(wxMenuUrlDTO);
    }

    @PostMapping("/send_call_message")
    @ApiOperation("发送商家出餐通知")
    public void sendCallMessage(@RequestBody WxSendMessageReqDTO sendMessageReqDTO) {
        log.info("[发送商家出餐通知]sendMessageReqDTO={}", JacksonUtils.writeValueAsString(sendMessageReqDTO));
        wxOpenMessageService.sendCallMessage(sendMessageReqDTO);
    }

    @PostMapping("/add_msg_template")
    @ApiOperation("手动添加消息模版")
    public void addMsgTemplate(@RequestBody WxMpTemplateDTO wxMpTemplateDTO) {
        log.info("[手动添加消息模版]sendMessageReqDTO={}", JacksonUtils.writeValueAsString(wxMpTemplateDTO));
        wxMpTemplateService.addMsgTemplate(wxMpTemplateDTO);
    }

}
