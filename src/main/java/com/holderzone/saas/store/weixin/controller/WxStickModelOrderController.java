package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.WxStickOrderCallBackDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickOrderRespDTO;
import com.holderzone.saas.store.weixin.service.WxStickModelOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickModelOrderController
 * @date 2019/03/15 16:23
 * @description 微信桌贴模板购买controller
 * @program holder-saas-store
 */
@RestController
@RequestMapping("/wx_stick_order")
@Api(description = "微信桌贴模板购买controller")
@Slf4j
public class WxStickModelOrderController {
	@Autowired
	WxStickModelOrderService wxStickModelOrderService;

	@ApiOperation(value = "下单")
	@PostMapping("/order")
	public WxStickOrderRespDTO order(@RequestBody WxStickModelOrderDTO wxStickModelOrderDTO) {
		log.info("桌贴模板购买：下单参数：{}", JacksonUtils.writeValueAsString(wxStickModelOrderDTO));
		return wxStickModelOrderService.order(wxStickModelOrderDTO);
	}

	@ApiOperation(value = "轮询")
	@PostMapping("/polling")
	public WxStickOrderRespDTO polling(@RequestBody WxStickOrderRespDTO wxStickOrderRespDTO) {
		log.info("桌贴模板购买：轮询参数：{}", JacksonUtils.writeValueAsString(wxStickOrderRespDTO));
		return wxStickModelOrderService.polling(wxStickOrderRespDTO);
	}

	@ApiOperation(value = "回调")
	@PostMapping("/call_back")
	public String callBack(@RequestBody WxStickOrderCallBackDTO wxStickOrderCallBackDTO) {
		log.info("桌贴模板购买：回调参数：{}", JacksonUtils.writeValueAsString(wxStickOrderCallBackDTO));
		return wxStickModelOrderService.callBack(wxStickOrderCallBackDTO);
	}
}
