package com.holderzone.saas.store.weixin.service.rpc;


import com.holderzone.saas.store.dto.pay.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStorePayClientService
 * @date 2019/4/3
 */
@Component
@FeignClient(name = "holder-saas-store-pay", fallbackFactory = WxStorePayClientService.WxStorePayFullback.class)
public interface WxStorePayClientService {

    String URL_PREFIX = "/agg";

    @PostMapping(URL_PREFIX + "/pay")
    AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO);

    @PostMapping(URL_PREFIX + "/refund")
    AggRefundRespDTO refund(SaasAggRefundDTO saasAggRefundDTO);

    @PostMapping(URL_PREFIX + "/refund/polling")
    AggRefundPollingRespDTO refundPolling(SaasPollingDTO saasPollingDTO);

    @PostMapping(URL_PREFIX + "/polling")
    AggPayPollingRespDTO polling(SaasPollingDTO saasPollingDTO);

    @PostMapping(URL_PREFIX + "/wechat/public/polling")
    AggPayPollingRespDTO pollingWeChatPublic(SaasPollingDTO saasPollingDTO);

    @PostMapping(URL_PREFIX + "/query")
    AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO);

    @PostMapping(URL_PREFIX + "/wechat/public/query")
    AggPayPollingRespDTO queryWeChatPublic(SaasPollingDTO saasPollingDTO);

    @PostMapping(URL_PREFIX + "/wechat/public")
    String weChatPublic(SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO);

    @Component
    @Slf4j
    class WxStorePayFullback implements FallbackFactory<WxStorePayClientService> {
        @Override
        public WxStorePayClientService create(Throwable throwable) {
            return new WxStorePayClientService() {

                @Override
                public AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO) {
                    log.error("远程调用失败:{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public AggRefundRespDTO refund(SaasAggRefundDTO saasAggRefundDTO) {
                    log.error("远程调用失败:{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public AggRefundPollingRespDTO refundPolling(SaasPollingDTO saasPollingDTO) {
                    log.error("远程调用失败:{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public AggPayPollingRespDTO polling(SaasPollingDTO saasPollingDTO) {
                    log.error("远程调用失败:{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public AggPayPollingRespDTO pollingWeChatPublic(SaasPollingDTO saasPollingDTO) {
                    log.error("远程调用失败:{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO) {
                    log.error("远程调用失败:{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public AggPayPollingRespDTO queryWeChatPublic(SaasPollingDTO saasPollingDTO) {
                    log.error("远程调用失败:{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public String weChatPublic(SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO) {
                    log.error("远程调用失败:{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
