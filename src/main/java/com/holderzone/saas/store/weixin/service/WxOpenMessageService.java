package com.holderzone.saas.store.weixin.service;

import com.holderzone.framework.response.Result;
import com.holderzone.saas.store.dto.queue.HolderQueueQueueRecordDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.menu.WxMenuUrlDTO;
import com.holderzone.saas.store.dto.weixin.open.WxMessageHandleReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxSendMessageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.TempMsgCreateDTO;
import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxSubjectReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxConfigRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxSubjectRespDTO;
import com.holderzone.saas.store.weixin.entity.dto.WxMemberConsumeMsgDTO;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxOpenMessageService
 * @date 2019/03/26 18:08
 * @description 微信公众号消息处理service
 * @program holder-saas-store
 */
public interface WxOpenMessageService {

    /**
     * 获取公众号回复消息内容xml
     *
     * @param wxMessageHandleReqDTO
     * @return
     * @throws WxErrorException
     */
    String getWxMpXmlMessage(WxMessageHandleReqDTO wxMessageHandleReqDTO) throws WxErrorException;

    /**
     * 通过用户授权code获取用户信息并保存，
     *
     * @param wxAuthorizeReqDTO
     * @return 重定向地址
     * @throws WxErrorException
     */
    String getUserInfo(WxAuthorizeReqDTO wxAuthorizeReqDTO) throws WxErrorException;

    String getOpenUserInfo(WxAuthorizeReqDTO wxAuthorizeReqDTO) throws WxErrorException;

    void createQueueMsgTemp(TempMsgCreateDTO tempMsgCreateDTO) throws WxErrorException;

    void sendQueueMsg(WxStoreConsumerDTO wxStoreConsumerDTO, HolderQueueQueueRecordDTO obtain, WxPortalReqDTO wxPortalReqDTO) throws WxErrorException;

    String shopList(WxPortalReqDTO wxPortalReqDTO) throws WxErrorException;

    String getBossAuthorizeUrl(WxMenuUrlDTO wxMenuUrlDTO);

    String getBossRedirectUrl(WxAuthorizeReqDTO wxAuthorizeReqDTO);

    void saveBossAuthToken(WxMenuUrlDTO wxMenuUrlDTO);

    void cleanBossAuthToken(WxMenuUrlDTO wxMenuUrlDTO);

    void removeBossAuthToken(String openId);

    WxConfigRespDTO generateDTO(WxPortalReqDTO wxPortalReqDTO) throws WxErrorException;

    String memberLogin(WxPortalReqDTO wxPortalReqDTO);

    String newMemberLogin(WxPortalReqDTO wxPortalReqDTO);

    String openMemberLogin(WxPortalReqDTO wxPortalReqDTO);

    void sendMemberMsg(WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO);

    Result<String> sendMemberMsg(String brandGuid, int messageCode, WxMpTemplateMessage wxMpTemplateMessage);

    Result<String> sendMemberMsgNew(WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO);

    WxSubjectRespDTO getWxSubject(WxSubjectReqDTO wxSubjectReqDTO);

    void sendCallMessage(WxSendMessageReqDTO sendMessageReqDTO);
}
