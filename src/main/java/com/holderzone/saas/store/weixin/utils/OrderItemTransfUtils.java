package com.holderzone.saas.store.weixin.utils;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import org.springframework.util.ObjectUtils;

import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoAttrDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoAttrGroupDTO;

public class OrderItemTransfUtils {

	OrderItemTransfUtils(){}
	/**
	 * 属性合并
	 * @param itemInfoAttrGroupDTOS 属性组
	 * @return 属性集合
	 */
	public static List<ItemAttrDTO> transformItemAttrGroup(List<ItemInfoAttrGroupDTO> itemInfoAttrGroupDTOS) {
		if (ObjectUtils.isEmpty(itemInfoAttrGroupDTOS)) {
			return Collections.emptyList();
		}
		LinkedList<ItemAttrDTO> itemAttrDTOS = new LinkedList<>();
		for (ItemInfoAttrGroupDTO itemInfoAttrGroupDTO : itemInfoAttrGroupDTOS) {
			List<ItemInfoAttrDTO> attrList = itemInfoAttrGroupDTO.getAttrList();
			if (ObjectUtils.isEmpty(attrList)) {
				continue;
			}
			for (ItemInfoAttrDTO itemInfoAttrDTO : attrList) {
				ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
				itemAttrDTO.setGuid(itemInfoAttrDTO.getAttrGuid());
				itemAttrDTO.setAttrGuid(itemInfoAttrDTO.getAttrGuid());
				itemAttrDTO.setAttrName(itemInfoAttrDTO.getName());
				itemAttrDTO.setAttrPrice(itemInfoAttrDTO.getPrice());
				itemAttrDTO.setAttrGroupGuid(itemInfoAttrGroupDTO.getAttrGroupGuid());
				itemAttrDTO.setAttrGroupName(itemInfoAttrGroupDTO.getName());
				itemAttrDTO.setNum(1);
				itemAttrDTOS.add(itemAttrDTO);
			}
		}
		return itemAttrDTOS;
	}
}
