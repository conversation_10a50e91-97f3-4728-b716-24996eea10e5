package com.holderzone.saas.store.weixin.utils;

import com.thoughtworks.xstream.XStream;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.StringWriter;

/**
 * <AUTHOR>
 * @version 1.0
 * @className XmlUtil
 * @date 2019/01/15 16:07
 * @description
 * @program holder-saas-store-weixin
 */
//@Slf4j
public class XmlUtil {
    private static final XStream xStream = XStreamFactory.getXStream();

    /**
     * 将对象直接转换成String类型的XML输出
     *
     * @param object
     * @return
     */
    public static String objToXml(Object object) {
        StringWriter sw = new StringWriter();
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(object.getClass());
            Marshaller marshaller = jaxbContext.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
            marshaller.marshal(object, sw);
        } catch (JAXBException e) {
//            log.info("对象转XML时发生异常，Exception：{}", e);
        }
        return sw.toString();
    }

    public static String xstreamObjToXml(Object object) {
        xStream.processAnnotations(object.getClass());
        xStream.autodetectAnnotations(true);
        return xStream.toXML(object);
    }
}
