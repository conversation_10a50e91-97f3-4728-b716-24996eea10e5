package com.holderzone.saas.store.weixin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.weixin.entity.domain.WxQrRedirectDo;
import org.springframework.stereotype.Repository;

/**
 * 微信默认二维码回调地址
 *
 * <AUTHOR>
 * @date 2021/4/7 21:20
 */
@Repository
public interface WxQrRedirectMapper extends BaseMapper<WxQrRedirectDo> {

    int insertMaster(WxQrRedirectDo redirectDo);

}
