package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreCartItemDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDineInOrderDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring")
public interface WxStoreDineInOrderMapstruct {

    DineInItemDTO wxStoreCartItemDTO2DineInItemDTO(WxStoreCartItemDTO wxStoreCartItemDTO);

    @Mappings({
            @Mapping(target = "guid", ignore = true),
            @Mapping(source = "wxStoreCartItemDTOS", target = "dineInItemDTOS")
    })
    CreateDineInOrderReqDTO wxStoreConsumerDineInOrderDTO2CreateDineInOrderDTO(WxStoreConsumerDineInOrderDTO wxStoreConsumerDineInOrderDTO);

}
