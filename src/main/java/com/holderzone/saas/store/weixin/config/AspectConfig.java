package com.holderzone.saas.store.weixin.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

@Configuration
public class AspectConfig {

	@Bean
	public LocalVariableTableParameterNameDiscoverer localVariableTableParameterNameDiscoverer() {
		return new LocalVariableTableParameterNameDiscoverer();
	}

	@Bean
	public SpelExpressionParser spelExpressionParser(){
		return new SpelExpressionParser();
	}

	@Bean
	public StandardEvaluationContext standardEvaluationContext(){
		return new StandardEvaluationContext();
	}
}
