package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.weixin.service.WxStoreTableStatusChangeService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreTableStatusChangeController
 * @date 2019/5/16
 */
@RestController
@RequestMapping("/wx_store_table_status_change")
@Slf4j
@Api("微信监听桌台状态变化")
public class WxStoreTableStatusChangeController {

	private final WxStoreTableStatusChangeService wxStoreTableStatusChangeService;

	private final DynamicHelper dynamicHelper;

	@Autowired
	public WxStoreTableStatusChangeController(WxStoreTableStatusChangeService wxStoreTableStatusChangeService, DynamicHelper dynamicHelper) {
		this.wxStoreTableStatusChangeService = wxStoreTableStatusChangeService;
		this.dynamicHelper = dynamicHelper;
	}

	@ApiOperation("并台")
	@PostMapping(value = "/combine")
	public boolean combine(@RequestBody TableCombineDTO tableCombineDTO) {
		log.info("微信接收并台详情:{}", tableCombineDTO);
		dynamicHelper.changeDatasource(tableCombineDTO.getEnterpriseGuid());
		return wxStoreTableStatusChangeService.combine(tableCombineDTO);
	}

	@ApiOperation("转台")
	@PostMapping(value = "/turn")
	public boolean turn(@RequestBody TurnTableDTO turnTableDTO) {
		dynamicHelper.changeDatasource(turnTableDTO.getEnterpriseGuid());
		return wxStoreTableStatusChangeService.turn(turnTableDTO);
	}

	@ApiOperation("拆台")
	@PostMapping(value = "/separate")
	public boolean separate(@RequestBody TableOrderCombineDTO tableOrderCombineDTO) {
		dynamicHelper.changeDatasource(tableOrderCombineDTO.getEnterpriseGuid());
		return wxStoreTableStatusChangeService.separate(tableOrderCombineDTO);
	}


}
