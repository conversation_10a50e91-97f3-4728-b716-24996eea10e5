package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.trade.DiscountDTO;
import com.holderzone.saas.store.dto.weixin.member.MemberDiscountDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberDiscountMap {

	MemberDiscountMap INSTANCE = Mappers.getMapper(MemberDiscountMap.class);

	List<MemberDiscountDTO> toMemberDiscount(List<DiscountFeeDetailDTO> discountFeeDetailDTOS);

	DiscountDTO toDiscountDTO(DiscountFeeDetailDTO discountFeeDetailDTO);

	List<DiscountDTO> toDiscountDTOList(List<DiscountFeeDetailDTO> discountFeeDetailDTOS);
}
