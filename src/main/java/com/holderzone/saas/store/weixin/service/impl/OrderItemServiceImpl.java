package com.holderzone.saas.store.weixin.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.BigDecimalUtil;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.OpenTableDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.dto.weixin.PricePairDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.deal.mq.FastOrderDelayMQDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import com.holderzone.saas.store.enums.order.AppendFeeTypeEnum;
import com.holderzone.saas.store.weixin.constant.RocketMqConfig;
import com.holderzone.saas.store.weixin.entity.domain.*;
import com.holderzone.saas.store.weixin.entity.enums.WxQrTypeEnum;
import com.holderzone.saas.store.weixin.entity.query.WxStorePendingOrdersQuery;
import com.holderzone.saas.store.weixin.event.delay.AutoConfirmListener;
import com.holderzone.saas.store.weixin.event.delay.ConfirmPromptListener;
import com.holderzone.saas.store.weixin.mapper.WxOrderItemMapper;
import com.holderzone.saas.store.weixin.mapper.WxQrRedirectMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreMerchantOrderMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.deal.BusinessClientService;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import com.holderzone.saas.store.weixin.service.deal.TableClientService;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderItemServiceImpl extends ServiceImpl<WxOrderItemMapper, WxOrderItemDO> implements OrderItemService {

    private final RedisUtils redisUtils;
    private final TradeClientService tradeClientService;
    private final WxStoreMerchantOrderService wxStoreMerchantOrderService;
    private final WxOrderRecordService wxOrderRecordService;
    private final TableClientService tableClientService;
    private final PriceCalculationUtils priceCalculationUtils;
    private final WxStoreOrderConfigService wxStoreOrderConfigService;
    private final WxStoreSessionDetailsService wxStoreSessionDetailsService;
    private final OrganizationClientService organizationClientService;
    private final DefaultRocketMqProducer defaultRocketMqProducer;
    private final WxUserRecordService wxUserRecordService;
    private final WxStoreEstimateClientService wxStoreEstimateClientService;
    private final EnterpriseClientService enterpriseClientService;
    private final RedisDelayedQueue redisDelayedQueue;
    private final WxStoreMerchantOrderMapstruct wxStoreMerchantOrderMapstruct;
    private final WxStoreMenuItemClientService itemClientService;

    private final WxStoreTableClientService wxStoreTableClientService;
    private final HsaBaseClientService wechatClientService;
    private final WxQrRedirectMapper wxQrRedirectMapper;
    @Resource
    private BusinessClientService businessClientService;
    @Resource
    private WxStoreTradeOrderService wxStoreTradeOrderService;
    @Resource
    private TcdOrderService tcdOrderService;
    @Resource
    private UserMemberSessionUtils userMemberSessionUtils;
    @Resource
    private GrouponService grouponService;
    @Autowired
    private Executor tableStaffRelationExecutor;

    @Autowired
    public OrderItemServiceImpl(RedisUtils redisUtils, TradeClientService tradeClientService,
                                WxStoreMerchantOrderService wxStoreMerchantOrderService,
                                WxOrderRecordService wxOrderRecordService, TableClientService tableClientService,
                                PriceCalculationUtils priceCalculationUtils,
                                WxStoreOrderConfigService wxStoreOrderConfigService,
                                WxStoreSessionDetailsService wxStoreSessionDetailsService,
                                OrganizationClientService organizationClientService,
                                DefaultRocketMqProducer defaultRocketMqProducer,
                                WxUserRecordService wxUserRecordService,
                                WxStoreEstimateClientService wxStoreEstimateClientService,
                                EnterpriseClientService enterpriseClientService,
                                RedisDelayedQueue redisDelayedQueue,
                                WxStoreMerchantOrderMapstruct wxStoreMerchantOrderMapstruct,
                                WxStoreMenuItemClientService itemClientService,
                                WxStoreTableClientService wxStoreTableClientService,
                                HsaBaseClientService wechatClientService,
                                WxQrRedirectMapper wxQrRedirectMapper,
                                GrouponService grouponService) {
        this.redisUtils = redisUtils;
        this.tradeClientService = tradeClientService;
        this.wxStoreMerchantOrderService = wxStoreMerchantOrderService;
        this.wxOrderRecordService = wxOrderRecordService;
        this.tableClientService = tableClientService;
        this.priceCalculationUtils = priceCalculationUtils;
        this.wxStoreOrderConfigService = wxStoreOrderConfigService;
        this.wxStoreSessionDetailsService = wxStoreSessionDetailsService;
        this.organizationClientService = organizationClientService;
        this.defaultRocketMqProducer = defaultRocketMqProducer;
        this.wxUserRecordService = wxUserRecordService;
        this.wxStoreEstimateClientService = wxStoreEstimateClientService;
        this.enterpriseClientService = enterpriseClientService;
        this.redisDelayedQueue = redisDelayedQueue;
        this.wxStoreMerchantOrderMapstruct = wxStoreMerchantOrderMapstruct;
        this.itemClientService = itemClientService;
        this.wxStoreTableClientService = wxStoreTableClientService;
        this.wechatClientService = wechatClientService;
        this.wxQrRedirectMapper = wxQrRedirectMapper;
        this.grouponService = grouponService;
    }


    private List<WxOrderItemDO> transformOrderItem(List<ShopCartItemReqDTO> shopCartItemReqDTOS, String orderGuid, String orderRecordGuid, String merchantGuid) {
        return ObjectUtils.isEmpty(shopCartItemReqDTOS)
                ? Collections.emptyList()
                : shopCartItemReqDTOS.stream().map(x -> {
            WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
            wxOrderItemDO.setGuid(redisUtils.generatdDTOGuid(WxOrderItemDO.class));
            ItemInfoDTO itemInfoDTO = x.getItemInfoDTO();
            wxOrderItemDO.setOrderGuid(orderGuid);
            wxOrderItemDO.setMerchantGuid(merchantGuid);
            wxOrderItemDO.setOrderRecordGuid(orderRecordGuid);
            wxOrderItemDO.setOpenId(WeixinUserThreadLocal.getOpenId());
            wxOrderItemDO.setItemGuid(itemInfoDTO.getItemGuid());
            wxOrderItemDO.setItemName(itemInfoDTO.getName());
            wxOrderItemDO.setItemTypeGuid(itemInfoDTO.getTypeGuid());
            wxOrderItemDO.setItemTypeName(itemInfoDTO.getTypeName());
            wxOrderItemDO.setItemType(itemInfoDTO.getItemType() == 5 ? 1 : itemInfoDTO.getItemType());
            wxOrderItemDO.setPictureUrl(itemInfoDTO.getPictureUrl());
            ItemInfoSkuDTO itemInfoSkuDTO = getUckSku(itemInfoDTO);
            wxOrderItemDO.setSkuGuid(itemInfoSkuDTO.getSkuGuid());
            wxOrderItemDO.setSkuName(itemInfoSkuDTO.getName());
            wxOrderItemDO.setSkuUnit(itemInfoSkuDTO.getUnit());
            wxOrderItemDO.setSkuPrice(itemInfoSkuDTO.getSalePrice());
            wxOrderItemDO.setSkuMemberPrice(itemInfoSkuDTO.getMemberPrice());
            wxOrderItemDO.setCode(itemInfoSkuDTO.getCode());
            PricePairDTO pricePairDTO = priceCalculationUtils.itemPrice(itemInfoDTO);
            wxOrderItemDO.setOriginalPrice(pricePairDTO.getOriginPrice());
            BigDecimal memberPrice = pricePairDTO.getMemberPrice();
            if (memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0) {
                wxOrderItemDO.setMemberPrice(memberPrice);
            } else {
                wxOrderItemDO.setMemberPrice(null);
            }
            wxOrderItemDO.setAttrTotal(priceCalculationUtils.getAttrTotalPrice(itemInfoDTO.getAttrGroupList()));
            wxOrderItemDO.setHasAttr(ObjectUtils.isEmpty(itemInfoDTO.getAttrGroupList()) ? 0 : 1);
            wxOrderItemDO.setCurrentCount(itemInfoDTO.getCurrentCount());
            wxOrderItemDO.setIsMemberDiscount(itemInfoSkuDTO.getIsMemberDiscount());
            wxOrderItemDO.setIsWholeDiscount(itemInfoSkuDTO.getIsWholeDiscount());
            wxOrderItemDO.setSubgroup(JSON.toJSONString(itemInfoDTO.getSubgroupList()));
            wxOrderItemDO.setItemAttr(JSON.toJSONString(itemInfoDTO.getAttrGroupList()));
            wxOrderItemDO.setCouponInfo(buildItemCouponInfoDTO(itemInfoDTO));
            return wxOrderItemDO;
        }).collect(Collectors.toList());
    }

    /**
     * 构建商品使用团购验券信息
     */
    private String buildItemCouponInfoDTO(ItemInfoDTO itemInfoDTO) {
        GroupVerifyDTO groupVerify = itemInfoDTO.getGroupVerify();
        if (Objects.isNull(groupVerify)) {
            return null;
        }
        ItemCouponInfoDTO itemCouponInfoDTO = new ItemCouponInfoDTO();
        itemCouponInfoDTO.setCouponPreRespDTO(itemInfoDTO.getCouponPreRespDTO());
        itemCouponInfoDTO.setGroupVerify(itemInfoDTO.getGroupVerify());
        return JacksonUtils.writeValueAsString(itemCouponInfoDTO);
    }


    /**
     * 下单
     *
     * @return 下单返回
     */
    @Override
    public OrderSubmitRespDTO submit(OrderSubmitReqDTO orderSubmitReqDTO) {
        WxOrderConfigDTO detailConfig = wxStoreOrderConfigService.getDetailConfig(new WxStoreReqDTO(WeixinUserThreadLocal.getStoreGuid()));
        log.info("查询门店配置返回:{}", JacksonUtils.writeValueAsString(detailConfig));
        return Optional.ofNullable(detailConfig).map(x -> {
            Integer orderModel = x.getOrderModel();
            return ObjectUtils.isEmpty(orderModel)
                    ? OrderSubmitRespDTO.unSet()
                    : orderModel == 0
                    ? submitDine(orderSubmitReqDTO)
                    : submitFast(orderSubmitReqDTO);
        }).orElse(OrderSubmitRespDTO.unSet());
    }

    @Override
    public Boolean enableKeyboard() {
        String tableGuid = WeixinUserThreadLocal.getDiningTableGuid();
        WxStorePendingOrdersQuery query = new WxStorePendingOrdersQuery();
        query.setOrderStates(Arrays.asList(0, 1));
        query.setDiningTableGuid(WeixinUserThreadLocal.getDiningTableGuid());
        query.setTradeMode(0);
        log.info("查询当前桌台订单入参:{}", JacksonUtils.writeValueAsString(query));
        List<WxStoreMerchantOrderDTO> pendingOrders = wxStoreMerchantOrderService.getPendingOrders(query);
        if (!ObjectUtils.isEmpty(pendingOrders)) {
            return false;
        }
        log.error("没查询到正餐订单:{},企业:{}", tableGuid, WeixinUserThreadLocal.getEnterpriseGuid());
        return true;
    }


    /**
     * 正餐下单
     *
     * @param orderSubmitReqDTO 就餐人数
     * @return 就餐返回
     */
    @Override
    public OrderSubmitRespDTO submitDine(OrderSubmitReqDTO orderSubmitReqDTO) {
        log.info("进入正餐下单方法:{}", JacksonUtils.writeValueAsString(orderSubmitReqDTO));
        //正餐顾客人数需大于0
        if (orderSubmitReqDTO != null && orderSubmitReqDTO.getUserCount() != null && orderSubmitReqDTO.getUserCount() > 0) {
            redisUtils.setEx(CacheName.USER_COUNT + ":" + WeixinUserThreadLocal.getDiningTableGuid(), orderSubmitReqDTO.getUserCount(), 10, TimeUnit.HOURS);
        }
        Integer count = (Integer) redisUtils.get(CacheName.USER_COUNT + ":" + WeixinUserThreadLocal.getDiningTableGuid());
        if (orderSubmitReqDTO == null) {
            orderSubmitReqDTO = new OrderSubmitReqDTO();
        }
        orderSubmitReqDTO.setUserCount(count);
        TableDTO tableDTO = tableClientService.getTableByGuid(WeixinUserThreadLocal.getDiningTableGuid());
        log.info("进入正餐下单方法-查询桌台返回:{}", JacksonUtils.writeValueAsString(tableDTO));
        if (tableDTO == null) {
            log.error("查询桌台失败:{}", WeixinUserThreadLocal.get());
            return OrderSubmitRespDTO.submitFailed();
        }
        String shopcartKey = CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getDiningTableGuid();
        List<ShopCartItemReqDTO> itemList = redisUtils.hValues(shopcartKey);
        log.info("进入正餐下单方法-查询购物车返回:{}", JacksonUtils.writeValueAsString(itemList));
        if (ObjectUtils.isEmpty(itemList)) {
            return OrderSubmitRespDTO.noItem();
        }
        // 排序
        itemList.sort(Comparator.comparing(ShopCartItemReqDTO::getGmtCreate).reversed());
        List<ItemInfoDTO> collect = itemList.stream().map(ShopCartItemReqDTO::getItemInfoDTO).collect(Collectors.toList());
        String msg = orderUpperLimit(collect);
        if (!StringUtils.isEmpty(msg)) {
            return OrderSubmitRespDTO.upperLimit(msg);
        }

        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
        baseDTO.setEnterpriseGuid(WeixinUserThreadLocal.getEnterpriseGuid());
        List<ItemEstimateForAndroidRespDTO> estimate = wxStoreEstimateClientService.queryEstimateForSyn(baseDTO);
        List<ItemInfoEstimateVerifyDTO> verifyDTOS = itemEstimateVerify(collect, estimate);

        // 校验下单的商品是否加入微信点餐，没有按估清处理
        try {
            itemJoinWeChatVerify(collect, verifyDTOS);
        } catch (Exception e) {
            log.error("校验下单商品是否加入微信点餐异常 e={}", e.getMessage());
        }
        if (!CollectionUtils.isEmpty(verifyDTOS)) {
            // 商品被估清提示信息
            return OrderSubmitRespDTO.estimateFaild(buildItemEstimateTips(collect, verifyDTOS));
        }
        subFilter(collect);
        attrFilter(collect);
        log.info("进入正餐下单方法-WeixinUserThreadLocal:{}", JacksonUtils.writeValueAsString(WeixinUserThreadLocal.get()));
        String orderRecordGuid = wxOrderRecordService.initialRecord(WeixinUserThreadLocal.get());
        log.info("进入正餐下单方法-查询订单或初始化方法返回:{}", JacksonUtils.writeValueAsString(orderRecordGuid));
        if (StringUtils.isEmpty(orderRecordGuid)) {
            log.error("下单失败，没有生成订单id");
            return OrderSubmitRespDTO.submitFailed();
        }
        // 提交正餐订单
        return submitDineOrder(orderSubmitReqDTO, orderRecordGuid, itemList, tableDTO);
    }

    private OrderSubmitRespDTO submitDineOrder(OrderSubmitReqDTO orderSubmitReqDTO, String orderRecordGuid,
                                               List<ShopCartItemReqDTO> itemList, TableDTO tableDTO) {
        String shopcartKey = CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getDiningTableGuid();
        String lockKey = CacheName.DINE_TABLE_LOCK + ":" + WeixinUserThreadLocal.getDiningTableGuid();
        boolean lock = redisUtils.setNx(lockKey, "1", 10);
        log.info("进入正餐下单方法-正餐桌台锁:lockKey:{},lock:{}", lockKey, lock);
        if (!lock) {
            log.error("进入正餐下单方法-正餐桌台锁错误,lockKey:{}", lockKey);
            return new OrderSubmitRespDTO().setOrderRecordGuid(orderRecordGuid);
        }
        String orderGuid = "";
        List<ItemInfoDTO> itemInfoDTOList = itemList.stream().map(ShopCartItemReqDTO::getItemInfoDTO).collect(Collectors.toList());
        try {
            // 预验券
            grouponService.grouponDoCheck(tableDTO.getOrderGuid(), orderRecordGuid, itemList);
            // 提交订单
            List<ItemInfoDTO> collect = itemList.stream().map(ShopCartItemReqDTO::getItemInfoDTO).collect(Collectors.toList());
            orderGuid = submitOrder(orderSubmitReqDTO, itemList, collect, tableDTO, orderRecordGuid);
            if (ObjectUtils.isEmpty(orderGuid)) {
                grouponService.revokeGroupon(itemInfoDTOList);
                return OrderSubmitRespDTO.tableError().setOrderRecordGuid(orderRecordGuid);
            }
        } catch (BusinessException e) {
            log.error("下单失败", e);
            grouponService.revokeGroupon(itemInfoDTOList);
            return OrderSubmitRespDTO.submitFailed(e.getMessage());
        } catch (Exception e) {
            log.error("下单失败", e);
            grouponService.revokeGroupon(itemInfoDTOList);
            return OrderSubmitRespDTO.submitFailed();
        } finally {
            redisUtils.delete(shopcartKey);
            if (lock) {
                redisUtils.delete(lockKey);
            }
        }
        return afterSubmit(orderGuid, orderRecordGuid, orderSubmitReqDTO.getDeviceType());
    }

    /**
     * 构建返回商品估清提示信息
     */
    private String buildItemEstimateTips(List<ItemInfoDTO> collect, List<ItemInfoEstimateVerifyDTO> verifyDTOS) {
        Map<String, ItemInfoDTO> itemInfoDTOMap = collect.stream().collect(Collectors.toMap(i -> i.getSkuList().get(0).getSkuGuid(), i -> i));
        verifyDTOS.forEach(i -> {
            ItemInfoDTO itemInfoDTO = itemInfoDTOMap.get(i.getSkuGuid());
            if (itemInfoDTO == null)
                log.info("{}-{}", JacksonUtils.writeValueAsString(i), JSONObject.toJSONString(itemInfoDTOMap));
            else
                delCartItem(itemInfoDTO, 0);
        });

        StringBuilder dineErrorMsg = new StringBuilder("以下商品库存不足不可下单：");
        verifyDTOS.forEach(e -> {
            String productName = e.getItemName();
            if (StringUtils.isNotBlank(e.getSkuName())) {
                productName = productName + "-" + e.getSkuName();
            }
            BigDecimal remainNum = Optional.ofNullable(e.getRemainNum()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP);
            dineErrorMsg.append(productName).append(" (").append(BigDecimalFormatUtil.format(remainNum)).append(e.getUnit()).append(")、");
        });
        return dineErrorMsg.substring(0, dineErrorMsg.length() - 1);
    }


    /**
     * 提交订单
     */
    private String submitOrder(OrderSubmitReqDTO orderSubmitReqDTO, List<ShopCartItemReqDTO> itemList, List<ItemInfoDTO> collect,
                               TableDTO tableDTO, String orderRecordGuid) {
        String orderGuid = "";
        PricePairDTO pricePairDTO = priceCalculationUtils.orderPrice(collect);
        String merchantGuid = redisUtils.generatdDTOGuid(WxStoreMerchantOrderDO.class);
        WxStoreMerchantOrderDO wxStoreMerchantOrderDO = initialDineMerchantOrder(orderRecordGuid, orderSubmitReqDTO, tableDTO
                , null, merchantGuid, collect, pricePairDTO);
        wxStoreMerchantOrderService.save(wxStoreMerchantOrderDO);
        log.info("进入正餐下单方法-新增商户订单:{}", JacksonUtils.writeValueAsString(wxStoreMerchantOrderDO));
        List<WxOrderItemDO> wxOrderItemDOS = transformOrderItem(itemList, null, orderRecordGuid, merchantGuid);
        saveBatch(wxOrderItemDOS);
        log.info("进入正餐下单方法-新增商品明细完成:{}", JacksonUtils.writeValueAsString(wxOrderItemDOS));

        resetOrderRecord(tableDTO, pricePairDTO, orderRecordGuid);
        log.info("进入正餐下单方法-重置订单完成");
        redisUtils.delete(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getDiningTableGuid());
        WxStoreMerchantOrderDTO wxStoreMerchantOrder = WxStoreMerchantOrderMapstruct.INSTANCE.getWxStoreMerchantOrder(wxStoreMerchantOrderDO);
        // 如果是赚餐，尝试开台，若已开台，则会返回当前桌台上的订单guid，否则返回新的订单guid
        if (!ObjectUtils.isEmpty(orderSubmitReqDTO.getDeviceType())
                && (BaseDeviceTypeEnum.isApplet(orderSubmitReqDTO.getDeviceType())
                || Objects.equals(BaseDeviceTypeEnum.WECHAT.getCode(), orderSubmitReqDTO.getDeviceType()))) {
            TableDTO table = wxStoreTableClientService.getTableByGuid(WeixinUserThreadLocal.getDiningTableGuid());
            if (!ObjectUtils.isEmpty(table) && StringUtils.isNotEmpty(table.getOrderGuid())) {
                orderGuid = table.getOrderGuid();
            } else {
                OpenTableDTO openTableDTO = new OpenTableDTO();
                openTableDTO.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
                openTableDTO.setTableCode(tableDTO.getCode());
                openTableDTO.setTableGuid(WeixinUserThreadLocal.getDiningTableGuid());
                openTableDTO.setActualGuestsNo(orderSubmitReqDTO.getUserCount());
                openTableDTO.setDeviceType(orderSubmitReqDTO.getDeviceType());
                openTableDTO.setAreaName(tableDTO.getAreaName());
                openTableDTO.setAreaGuid(tableDTO.getAreaGuid());
                openTableDTO.setUserGuid(wxStoreMerchantOrderDO.getOpenId());
                openTableDTO.setUserName(wxStoreMerchantOrderDO.getNickName());
                rePutUserContext(wxStoreMerchantOrderDO);
                orderGuid = wxStoreTableClientService.tryOpen(openTableDTO);
                if (StringUtils.isEmpty(orderGuid)) {
                    return null;
                }
            }
            log.info("赚餐下单开台成功，当前订单号：{}", orderGuid);
            wxStoreMerchantOrderDO.setOrderGuid(orderGuid);
            wxStoreMerchantOrderService.updateById(wxStoreMerchantOrderDO);
        }
        UserContext userContext = UserContextUtils.get();
        if (ObjectUtil.isNotNull(userContext) && StringUtils.isEmpty(userContext.getOperSubjectGuid())
                && StringUtils.isNotEmpty(WeixinUserThreadLocal.get().getOperSubjectGuid())) {
            userContext.setOperSubjectGuid(WeixinUserThreadLocal.get().getOperSubjectGuid());
        }
        autoAccept(userContext, WeixinUserThreadLocal.getDiningTableGuid(), orderRecordGuid, merchantGuid, wxStoreMerchantOrder);
        return orderGuid;
    }

    private void rePutUserContext(WxStoreMerchantOrderDO wxStoreMerchantOrderDO) {
        UserContext userContext = UserContextUtils.get();
        if (ObjectUtils.isEmpty(userContext)) {
            return;
        }
        String userGuid = userContext.getUserGuid();
        String userName = userContext.getUserName();
        if (StringUtils.isEmpty(userGuid) || StringUtils.isEmpty(userName)) {
            userContext.setUserGuid(wxStoreMerchantOrderDO.getOpenId());
            userContext.setUserName(wxStoreMerchantOrderDO.getNickName());
            UserContextUtils.put(userContext);
        }
    }

    /**
     * 提交订单后置处理
     */
    private OrderSubmitRespDTO afterSubmit(String orderGuid, String orderRecordGuid, Integer deviceType) {
        OrderSubmitRespDTO respDTO = new OrderSubmitRespDTO();
        respDTO.setOrderRecordGuid(orderRecordGuid);
        if (StringUtils.isNotEmpty(orderGuid)) {
            respDTO.setOrderGuid(orderGuid);
            // 将桌台和员工关联信息存Redis
            tableStaffRelationExecutor.execute(() -> setTableStaffInfo(orderGuid, WeixinUserThreadLocal.getDiningTableGuid()));
        }
        if (Objects.isNull(deviceType)) {
            deviceType = BaseDeviceTypeEnum.WECHAT.getCode();
        }
        respDTO.setDeviceType(deviceType);
        return respDTO;
    }


    @Override
    public void setTableStaffInfo(String orderGuid, String diningTableGuid) {
        //查询是否为特殊二维码（二维码关联员工信息）
        WxQrRedirectDo wxQrRedirectDo = wxQrRedirectMapper.selectOne(new LambdaQueryWrapper<WxQrRedirectDo>()
                .eq(WxQrRedirectDo::getTableGuid, diningTableGuid)
                .eq(WxQrRedirectDo::getType, WxQrTypeEnum.NORMAL_QR.getCode())
                .isNotNull(WxQrRedirectDo::getStaffGuid));
        if (Objects.nonNull(wxQrRedirectDo)) {
            redisUtils.setEx("WX:TABLE:STAFF:" + UserContextUtils.getEnterpriseGuid() + ":" + orderGuid,
                    new UserBriefDTO(wxQrRedirectDo.getStaffGuid(), wxQrRedirectDo.getStaffName())
                    , 16, TimeUnit.MINUTES);
        }
    }


    /**
     * 商品参与微信点餐校验
     *
     * @param collect    下单的所有商品
     * @param verifyDTOS 估清的商品
     */
    private void itemJoinWeChatVerify(List<ItemInfoDTO> collect, List<ItemInfoEstimateVerifyDTO> verifyDTOS) {
        List<String> allSkuGuids = collect.stream()
                .map(i -> getUckSku(i).getSkuGuid())
                .collect(Collectors.toList());
        List<String> estimateSkuGuids = verifyDTOS.stream()
                .map(ItemInfoEstimateVerifyDTO::getSkuGuid)
                .collect(Collectors.toList());
        allSkuGuids.removeIf(estimateSkuGuids::contains);
        List<String> notJoinWechatSkuGuids = itemClientService.queryNotJoinWechatItem(allSkuGuids);
        if (CollectionUtils.isNotEmpty(notJoinWechatSkuGuids)) {
            Map<String, ItemInfoDTO> itemInfoDTOMap = collect.stream().collect(
                    Collectors.toMap(i -> getUckSku(i).getSkuGuid(), Function.identity(), (dto1, dto2) -> dto2));
            notJoinWechatSkuGuids.forEach(sku -> {
                ItemInfoDTO itemInfoDTO = itemInfoDTOMap.get(sku);
                ItemInfoSkuDTO skuDTO = getUckSku(itemInfoDTO);
                ItemInfoEstimateVerifyDTO verifyDTO = new ItemInfoEstimateVerifyDTO();
                verifyDTO.setItemName(itemInfoDTO.getName());
                verifyDTO.setSkuGuid(sku);
                verifyDTO.setSkuName(skuDTO.getName());
                verifyDTO.setUnit(skuDTO.getUnit());
                verifyDTO.setIsSoldOut(true);
                verifyDTOS.add(verifyDTO);
            });
        }
    }

    private void autoAccept(UserContext userContext, String tableGuid, String orderRecordGuid
            , String merchantGuid, WxStoreMerchantOrderDTO wxStoreMerchantOrder) {
        CompletableFuture.runAsync(() -> {
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
            wxStoreReqDTO.setStoreGuid(userContext.getStoreGuid());
            WxOrderConfigDTO detailConfig = wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
            log.info("自动接单：门店配置:{}", detailConfig);
            BusinessMessageDTO businessMessageDTO = wxStoreMerchantOrderService.pushOrderMsg(wxStoreMerchantOrder);
            log.info("下单：异步发送emq:{}", businessMessageDTO);
            wxStoreMerchantOrderService.pushMsg(wxStoreMerchantOrder);
            //自动接单标志
            boolean isAutoFlag = detailConfig != null && detailConfig.getAutoConfirm() != null
                    && detailConfig.getAutoConfirm().equals(1);
            //已结单的单子
            List<WxStoreMerchantOrderDO> list = wxStoreMerchantOrderService.lambdaQuery()
                    .eq(WxStoreMerchantOrderDO::getOrderRecordGuid, orderRecordGuid)
                    .eq(WxStoreMerchantOrderDO::getOrderState, 1)
                    .select(WxStoreMerchantOrderDO::getGuid, WxStoreMerchantOrderDO::getOrderState).list();
            boolean toAutoConfirm = true;
            if (isAutoFlag) {
                //若自动确认时间为0分钟则立即确认
                if (ObjectUtils.isEmpty(detailConfig.getAutoConfirmTime()) || detailConfig.getAutoConfirmTime().equals(0L)) {
                    toAutoConfirm = false;
                    orderAccept(merchantGuid, wxStoreMerchantOrder);
                } else {
                    if (!ObjectUtils.isEmpty(list) && detailConfig.getTakingModel().equals(1)) {
                        toAutoConfirm = false;
                        orderAccept(merchantGuid, wxStoreMerchantOrder);
                    } else {
                        //若自动确认时间大于0则放入延时队列去通知
                        ConfirmConfigTaskDTO taskDTO = ConfirmConfigTaskDTO.builder()
                                .enterpriseGuid(userContext.getEnterpriseGuid())
                                .autoConfirm(true)
                                .orderRecordGuid(orderRecordGuid)
                                .merchantGuid(merchantGuid)
                                .storeGuid(wxStoreMerchantOrder.getStoreGuid())
                                .storeName(userContext.getStoreName())
                                .userGuid(userContext.getUserGuid())
                                .userName(userContext.getUserName())
                                .account(userContext.getAccount())
                                .openId(wxStoreMerchantOrder.getOpenId())
                                .nickName(wxStoreMerchantOrder.getNickName())
                                .actualGuestsNo(wxStoreMerchantOrder.getActualGuestsNo())
                                .takingModel(detailConfig.getTakingModel())
                                .build();
                        redisDelayedQueue.addQueue(taskDTO, detailConfig.getAutoConfirmTime()
                                , TimeUnit.MINUTES, AutoConfirmListener.class.getName());
                    }
                }
            } else {
                if (!ObjectUtils.isEmpty(list) && detailConfig != null && detailConfig.getTakingModel().equals(1)) {
                    orderAccept(merchantGuid, wxStoreMerchantOrder);
                }
            }
//			if (detailConfig != null && detailConfig.getTakingModel() == 1) {
//				List<WxStoreMerchantOrderDO> list = wxStoreMerchantOrderService.lambdaQuery()
//						.eq(WxStoreMerchantOrderDO::getOrderRecordGuid, orderRecordGuid)
//						.eq(WxStoreMerchantOrderDO::getOrderState, 1)
//						.select(WxStoreMerchantOrderDO::getGuid,WxStoreMerchantOrderDO::getOrderState).list();
//				log.info("自动接单：订单:{}",list);
//				if (!ObjectUtils.isEmpty(list)) {
//					//AutoAcceptDTO autoAcceptDTO = (AutoAcceptDTO) redisUtils.get("autoAccept:" + tableGuid);
//					//log.info("自动接单:缓存:{}", autoAcceptDTO);
//					//if (autoAcceptDTO != null) {
//						WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO();
//								/*List<DineInItemDTO> itemListByMerchantGuid = orderItemService.getItemListByMerchantGuid(wxStoreMerchantOrderReqDTO.getGuid());*/
//						wxOperateReqDTO.setGuid(merchantGuid);
//						wxOperateReqDTO.setOrderState(1);
//                        wxOperateReqDTO.setDeviceId(wxStoreMerchantOrder.getOpenId());
//						wxOperateReqDTO.setDeviceType(12);
//						wxOperateReqDTO.setUserGuid(wxStoreMerchantOrder.getOpenId());
//						wxOperateReqDTO.setUserName(wxStoreMerchantOrder.getNickName());
//						wxOperateReqDTO.setActualGuestsNo(wxStoreMerchantOrder.getActualGuestsNo());
//						wxStoreMerchantOrderService.operationMerchantOrder(wxOperateReqDTO);
//					//}
//				}
//			}
            //若填写了确认提示推到延时队列进行提醒
            if (detailConfig != null && !ObjectUtils.isEmpty(detailConfig.getConfirmPromptTime()) && toAutoConfirm) {
                ConfirmConfigTaskDTO taskDTO = ConfirmConfigTaskDTO.builder()
                        .enterpriseGuid(userContext.getEnterpriseGuid())
                        .confirmPrompt(true)
                        .orderRecordGuid(orderRecordGuid)
                        .merchantGuid(merchantGuid)
                        .storeGuid(wxStoreMerchantOrder.getStoreGuid())
                        .storeName(userContext.getStoreName())
                        .userGuid(userContext.getUserGuid())
                        .userName(userContext.getUserName())
                        .account(userContext.getAccount())
                        .guid(wxStoreMerchantOrder.getGuid())
                        .confirmPromptTime(detailConfig.getConfirmPromptTime())
                        .takingModel(detailConfig.getTakingModel())
                        .build();
                redisDelayedQueue.addQueue(taskDTO, detailConfig.getConfirmPromptTime()
                        , TimeUnit.MINUTES, ConfirmPromptListener.class.getName());
            }
        });
    }


    @Override
    public void dealRedisDelayedTask(ConfirmConfigTaskDTO taskDTO) {
        UserContext useContext = UserContext.builder().enterpriseGuid(taskDTO.getEnterpriseGuid())
                .storeGuid(taskDTO.getStoreGuid())
                .storeName(taskDTO.getStoreName())
                .account(taskDTO.getAccount())
                .userGuid(taskDTO.getUserGuid())
                .userName(taskDTO.getUserName())
                .build();
        UserContextUtils.put(useContext);
        EnterpriseIdentifier.setEnterpriseGuid(taskDTO.getEnterpriseGuid());
        if (ObjectUtils.isEmpty(taskDTO)) {
            return;
        }
        //去查询此单是否已经接单
        String merchantGuid = taskDTO.getMerchantGuid();
        if (StringUtils.isEmpty(merchantGuid)) {
            return;
        }
        WxStoreMerchantOrderDO one = wxStoreMerchantOrderService.getOneByGuid(merchantGuid);
        if (ObjectUtils.isEmpty(one)) {
            return;
        }
        if (!Objects.equals(one.getOrderState(), 0)) {
            return;
        }
        //若是自动确认
        if (Objects.nonNull(taskDTO.getAutoConfirm()) && taskDTO.getAutoConfirm()) {
            WxStoreMerchantOrderDTO dto = new WxStoreMerchantOrderDTO();
            dto.setOpenId(taskDTO.getOpenId());
            dto.setRemark(one.getRemark());
            dto.setNickName(taskDTO.getNickName());
            dto.setActualGuestsNo(taskDTO.getActualGuestsNo());
            dealOrderAutoConfirm(taskDTO.getTakingModel(), taskDTO.getOrderRecordGuid(), taskDTO.getMerchantGuid(), dto);
        }
        //若是确认提示
        if (Objects.nonNull(taskDTO.getConfirmPrompt()) && taskDTO.getConfirmPrompt()) {
            //若是确认提示需要轮询发送
            redisDelayedQueue.addQueue(taskDTO, taskDTO.getConfirmPromptTime()
                    , TimeUnit.MINUTES, ConfirmPromptListener.class.getName());
            WxStoreMerchantOrderDTO dto = new WxStoreMerchantOrderDTO();
            dto.setStoreGuid(taskDTO.getStoreGuid());
            dto.setGuid(taskDTO.getGuid());
            dto.setRemark(one.getRemark());
            dealOrderConfirmPrompt(taskDTO.getTakingModel(), taskDTO.getOrderRecordGuid(), dto);
        }
    }

    private void dealOrderConfirmPrompt(Integer takingModel, String orderRecordGuid, WxStoreMerchantOrderDTO wxStoreMerchantOrder) {
        if (ObjectUtils.isEmpty(takingModel)) {
            return;
        }
        //所有订单均需确认
        if (takingModel.equals(0)) {
            BusinessMessageDTO businessMessageDTO = wxStoreMerchantOrderService.pushOrderMsg(wxStoreMerchantOrder);
            log.info("下单：异步发送emq:{}", businessMessageDTO);
        }
        //首单需确认
        if (takingModel.equals(1)) {
            List<WxStoreMerchantOrderDO> list = wxStoreMerchantOrderService.lambdaQuery()
                    .eq(WxStoreMerchantOrderDO::getOrderRecordGuid, orderRecordGuid)
                    .eq(WxStoreMerchantOrderDO::getOrderState, 1)
                    .select(WxStoreMerchantOrderDO::getGuid, WxStoreMerchantOrderDO::getOrderState).list();
            if (ObjectUtils.isEmpty(list)) {
                BusinessMessageDTO businessMessageDTO = wxStoreMerchantOrderService.pushOrderMsg(wxStoreMerchantOrder);
                log.info("下单：异步发送emq:{}", businessMessageDTO);
            }
        }
    }

    private void dealOrderAutoConfirm(Integer takingModel, String orderRecordGuid, String merchantGuid
            , WxStoreMerchantOrderDTO wxStoreMerchantOrder) {
        if (ObjectUtils.isEmpty(takingModel)) {
            return;
        }
        //所有订单均需确认
        if (takingModel.equals(0)) {
            orderAccept(merchantGuid, wxStoreMerchantOrder);
        }
        //首单需确认
        if (takingModel.equals(1)) {
            List<WxStoreMerchantOrderDO> list = wxStoreMerchantOrderService.lambdaQuery()
                    .eq(WxStoreMerchantOrderDO::getOrderState, 0)
                    .eq(WxStoreMerchantOrderDO::getOrderRecordGuid, orderRecordGuid)
                    .orderByAsc(WxStoreMerchantOrderDO::getGmtCreate)
                    .list();
            if (!ObjectUtils.isEmpty(list)) {
                for (WxStoreMerchantOrderDO orderDO : list) {
                    orderAccept(orderDO.getGuid(), wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(orderDO));
                }
//				orderAccept(merchantGuid,wxStoreMerchantOrder);
            }
        }
    }

    private void orderAccept(String merchantGuid, WxStoreMerchantOrderDTO wxStoreMerchantOrder) {
        WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO();
        wxOperateReqDTO.setGuid(merchantGuid);
        wxOperateReqDTO.setOrderState(1);
        wxOperateReqDTO.setDeviceId(wxStoreMerchantOrder.getOpenId());
        wxOperateReqDTO.setDeviceType(12);
        wxOperateReqDTO.setUserGuid(wxStoreMerchantOrder.getOpenId());
        wxOperateReqDTO.setUserName(wxStoreMerchantOrder.getNickName());
        wxOperateReqDTO.setActualGuestsNo(wxStoreMerchantOrder.getActualGuestsNo());
        wxOperateReqDTO.setRemark(wxStoreMerchantOrder.getRemark());
        wxStoreMerchantOrderService.operationMerchantOrder(wxOperateReqDTO);
    }

    private List<ItemInfoEstimateVerifyDTO> itemEstimateVerify(List<ItemInfoDTO> itemInfoDTOS, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        long startTime = System.currentTimeMillis();
        log.info("估清校验:{}", estimateSkuList);
        List<ItemInfoEstimateVerifyDTO> verifyDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemInfoDTOS) || CollectionUtils.isEmpty(estimateSkuList)) {
            return verifyDTOS;
        }
        for (ItemEstimateForAndroidRespDTO estimate : estimateSkuList) {
            for (ItemInfoDTO itemInfoDTO : itemInfoDTOS) {
                if (ItemTypeEnum.PKG.getCode() == itemInfoDTO.getItemType()) {
                    // 套餐不再校验估清逻辑，即使此时已经估清，需要用户自己去换菜
                    continue;
                }
                estimateMatching(estimate, itemInfoDTO, verifyDTOS);
                // h5正餐套餐不校验套餐子菜估清，由客户自己去换菜，如果需要恢复点此查看
            }
        }
        log.info("估清校验耗时:{}ms", System.currentTimeMillis() - startTime);
        return verifyDTOS;
    }

    private void estimateMatching(ItemEstimateForAndroidRespDTO estimate,
                                  ItemInfoDTO itemInfoDTO,
                                  List<ItemInfoEstimateVerifyDTO> verifyDTOS) {
        ItemInfoSkuDTO uckSku = getUckSku(itemInfoDTO);
        if (estimate.getSkuGuid().equals(uckSku.getSkuGuid())) {
            log.info("下单：估清匹配商品:{},规格:{}", itemInfoDTO, estimate);
            if (estimate.getIsSoldOut() == 1 && estimate.getResidueQuantity().compareTo(itemInfoDTO.getCurrentCount()) < 0) {
                ItemInfoEstimateVerifyDTO itemInfoEstimateVerifyDTO = new ItemInfoEstimateVerifyDTO(itemInfoDTO.getName(), uckSku.getSkuGuid()
                        , uckSku.getName(), uckSku.getUnit(), false, estimate.getResidueQuantity());
                log.info("余量匹配:{}", itemInfoEstimateVerifyDTO);
                verifyDTOS.add(itemInfoEstimateVerifyDTO);
            } else if (estimate.getIsSoldOut() == 2) {
                ItemInfoEstimateVerifyDTO itemInfoEstimateVerifyDTO = new ItemInfoEstimateVerifyDTO(itemInfoDTO.getName(), uckSku.getSkuGuid()
                        , uckSku.getName(), uckSku.getUnit(), true, null);
                log.info("售尽匹配:{}", itemInfoEstimateVerifyDTO);
                verifyDTOS.add(itemInfoEstimateVerifyDTO);
            }
        }
    }

    /**
     * 重置订单
     *
     * @param tableDTO        桌台
     * @param pricePairDTO    金额
     * @param orderRecordGuid 订单id
     */
    private void resetOrderRecord(TableDTO tableDTO, PricePairDTO pricePairDTO, String orderRecordGuid) {
        WxOrderRecordDO orderRecordDO = wxOrderRecordService.getById(orderRecordGuid);
        log.info("下单：微信订单详情:{}", orderRecordDO);
        if (orderRecordDO != null) {
            orderRecordDO.setOrderState(0).setOrderStateName("待确认");
            orderRecordDO.setOrderGuid(StringUtils.isEmpty(tableDTO.getMainOrderGuid()) ? tableDTO.getOrderGuid() : tableDTO.getMainOrderGuid());
            if (StringUtils.isEmpty(orderRecordDO.getUserRecordGuid())) {
                WxUserRecordDO user = wxUserRecordService.getOneByOpenId(WeixinUserThreadLocal.getOpenId());
                if (user != null) {
                    orderRecordDO.setUserRecordGuid(user.getGuid());
                    orderRecordDO.setIsLogin(WeixinUserThreadLocal.getIsLogin());
                    orderRecordDO.setGmtCreate(LocalDateTime.now());
                }
            }
            BigDecimal actuallyPayFee = Optional.ofNullable(orderRecordDO.getActuallyPayFee()).orElse(BigDecimal.ZERO);
            BigDecimal memberPrice = Optional.ofNullable(orderRecordDO.getUnMemberPrice()).orElse(BigDecimal.ZERO);
            orderRecordDO.setActuallyPayFee(actuallyPayFee.add(pricePairDTO.getOriginPrice()));
            orderRecordDO.setUnMemberPrice(memberPrice.add(pricePairDTO.getMemberPrice()));
            wxOrderRecordService.updateById(orderRecordDO);
        }
    }

    private void subFilter(List<ItemInfoDTO> itemInfoDTOS) {
        if (ObjectUtils.isEmpty(itemInfoDTOS)) {
            return;
        }
        for (ItemInfoDTO itemInfoDTO : itemInfoDTOS) {
            List<ItemInfoSubgroupDTO> subgroupList = itemInfoDTO.getSubgroupList();
            if (!ObjectUtils.isEmpty(subgroupList)) {
                for (ItemInfoSubgroupDTO itemInfoSubgroupDTO : subgroupList) {
                    Integer pickNum = itemInfoSubgroupDTO.getPickNum();
                    List<ItemInfoSubSkuDTO> subItemSkuList = itemInfoSubgroupDTO.getSubItemSkuList();
                    subItemSkuList = subItemSkuList.stream().filter(x -> pickNum == 0 || x.getDefaultNum() > 0).collect(Collectors.toList());

                    for (ItemInfoSubSkuDTO itemInfoSubSkuDTO : subItemSkuList) {
                        List<ItemInfoAttrGroupDTO> attrGroupList = itemInfoSubSkuDTO.getAttrGroupList();
                        if (!ObjectUtils.isEmpty(attrGroupList)) {
                            for (ItemInfoAttrGroupDTO itemInfoAttrGroupDTO : attrGroupList) {
                                List<ItemInfoAttrDTO> attrList = itemInfoAttrGroupDTO.getAttrList();
                                if (!ObjectUtils.isEmpty(attrList)) {
                                    attrList = attrList.stream().filter(x -> x.getUck() == 1).collect(Collectors.toList());
                                    itemInfoAttrGroupDTO.setAttrList(attrList);
                                }
                            }
                        }
                    }

                    itemInfoSubgroupDTO.setSubItemSkuList(subItemSkuList);
                }
            }
        }
    }

    private void attrFilter(List<ItemInfoDTO> itemInfoDTOS) {
        if (ObjectUtils.isEmpty(itemInfoDTOS)) {
            return;
        }
        for (ItemInfoDTO itemInfoDTO : itemInfoDTOS) {
            List<ItemInfoAttrGroupDTO> attrGroupList = itemInfoDTO.getAttrGroupList();
            if (!ObjectUtils.isEmpty(attrGroupList)) {
                for (ItemInfoAttrGroupDTO itemInfoAttrGroupDTO : attrGroupList) {
                    List<ItemInfoAttrDTO> attrList = itemInfoAttrGroupDTO.getAttrList();
                    if (!ObjectUtils.isEmpty(attrList)) {
                        attrList = attrList.stream().filter(x -> x.getUck() == 1).collect(Collectors.toList());
                        itemInfoAttrGroupDTO.setAttrList(attrList);
                    }
                }
            }
        }
    }

    /**
     * @param itemInfoDTOS 商品集合
     * @return 订单超重判断
     */
    private String orderUpperLimit(List<ItemInfoDTO> itemInfoDTOS) {
        if (!ObjectUtils.isEmpty(itemInfoDTOS)) {
            boolean isWeight = itemInfoDTOS.stream().anyMatch(x -> x.getItemType() == 3);
            if (isWeight) {
                BigDecimal bigDecimal = itemInfoDTOS.stream().map(x -> x.getItemType() == 3 ? BigDecimal.ZERO
                        : x.getCurrentCount()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                if (bigDecimal.intValue() > 9999.999) {
                    return "单次下单数量不可超过9999.999";
                }
            } else {
                BigDecimal bigDecimal = itemInfoDTOS.stream().map(x -> x.getItemType() == 3 ? BigDecimal.ZERO
                        : x.getCurrentCount()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                if (bigDecimal.intValue() > 9999) {
                    return "单次下单数量不可超过9999";
                }
            }
        }
        return null;
    }

    private List<String> estimateItems(List<ItemInfoDTO> itemInfoDTOS) {
        if (ObjectUtils.isEmpty(itemInfoDTOS)) {
            log.error("当前没有商品，无法估清");
            return null;
        }
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        if (wxMemberSessionDTO == null) {
            log.error("无法获取当前会话，不能沽清");
            return null;
        }
        String storeGuid = wxMemberSessionDTO.getStoreGuid();
        if (StringUtils.isEmpty(storeGuid)) {
            log.error("当前会员话没有门店id，无法沽清:{}", wxMemberSessionDTO);
            return null;
        }
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(storeGuid);
        baseDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        List<ItemEstimateForAndroidRespDTO> estimate = wxStoreEstimateClientService.queryEstimateForSyn(baseDTO);
        log.info("下单：估清商品:{}", estimate);
        if (ObjectUtils.isEmpty(estimate)) {
            log.error("下单：估清商品为空");
            return null;
        }
//		Map<String, ItemEstimateForAndroidRespDTO> collect = estimate.stream()
//				.filter(x -> x.getIsSoldOut() == 2).collect(Collectors.toMap(ItemEstimateForAndroidRespDTO::getSkuGuid, Function.identity()));
//		if (ObjectUtils.isEmpty(collect)) {
//			log.error("下单：确认估清商品:{}", collect);
//			return null;
//		}
//		Set<String> set = collect.keySet();
//
//		List<ItemInfoDTO> estimateItem = itemInfoDTOS.stream().filter(x -> {
//			ItemInfoSkuDTO uckSku = getUckSku(x);
//			if (set.contains(uckSku.getSkuGuid())) {
//				ItemEstimateForAndroidRespDTO itemEstimate = collect.get(uckSku.getSkuGuid());
//				log.info("估清规格:{}",itemEstimate);
//				BigDecimal residueQuantity = itemEstimate.getResidueQuantity();
//				return residueQuantity.compareTo(x.getCurrentCount()) < 0;
//			}
//			return false;
//		}).collect(Collectors.toList());


        List<String> collect = estimate.stream().filter(x -> x.getIsSoldOut() == 2)
                .map(ItemEstimateForAndroidRespDTO::getSkuGuid).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(collect)) {
            log.error("下单：确认估清商品:{}", collect);
            return null;
        }

        List<ItemInfoDTO> estimateItem = itemInfoDTOS.stream().filter(x -> collect.contains(getUckSku(x).getSkuGuid())).collect(Collectors.toList());


        if (!ObjectUtils.isEmpty(estimateItem)) {
            return estimateItem.stream().map(ItemInfoDTO::getName).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 正单下单merchant初始化
     *
     * @param orderSubmitReqDTO 人数
     * @param tableDTO          桌台
     * @param orderGuid         商户订单guid
     * @param merchantGuid      批次id
     * @param collect           商品集合
     * @param pricePairDTO      金额计算
     */
    private WxStoreMerchantOrderDO initialDineMerchantOrder(String orderRecordGuid, OrderSubmitReqDTO orderSubmitReqDTO, TableDTO tableDTO, String orderGuid, String merchantGuid, List<ItemInfoDTO> collect, PricePairDTO pricePairDTO) {
        WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setOrderGuid(orderGuid);
        wxStoreMerchantOrderDO.setGuid(merchantGuid);
        wxStoreMerchantOrderDO.setOrderRecordGuid(orderRecordGuid);
        wxStoreMerchantOrderDO.setActualGuestsNo(orderSubmitReqDTO.getUserCount());
        wxStoreMerchantOrderDO.setTotalPrice(pricePairDTO.getOriginPrice());
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        log.info("initialDineMerchantOrder缓存中的会员信息{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        WxUserInfoDTO wxUserInfoDTO = wxMemberSessionDTO.getWxUserInfoDTO();
        Assert.notNull(wxUserInfoDTO, "下单人不能为空");
        wxStoreMerchantOrderDO.setOpenId(wxUserInfoDTO.getOpenId());
        wxStoreMerchantOrderDO.setNickName(wxUserInfoDTO.getNickname());
        wxStoreMerchantOrderDO.setHeadImgUrl(wxUserInfoDTO.getHeadImgUrl());
        wxStoreMerchantOrderDO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        wxStoreMerchantOrderDO.setStoreName(wxMemberSessionDTO.getStoreName());
        wxStoreMerchantOrderDO.setBrandGuid(wxMemberSessionDTO.getBrandGuid());
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setDiningTableGuid(wxMemberSessionDTO.getDiningTableGuid());
        wxStoreMerchantOrderDO.setTableCode(tableDTO.getCode());
        wxStoreMerchantOrderDO.setAreaGuid(tableDTO.getAreaGuid());
        wxStoreMerchantOrderDO.setAreaName(tableDTO.getAreaName());
        wxStoreMerchantOrderDO.setCurrentOrder(1);
        wxStoreMerchantOrderDO.setItemCount(chargeItemCount(collect));
        wxStoreMerchantOrderDO.setRemark(orderSubmitReqDTO.getRemark());
        wxStoreMerchantOrderDO.setCombine(tableDTO.getMainOrderGuid());
        //TODO 下单新增 订单来源  赚餐未作区分  翼惠天下作区分
        if (Objects.nonNull(orderSubmitReqDTO.getDeviceType())) {
            wxStoreMerchantOrderDO.setOrderSource(orderSubmitReqDTO.getDeviceType());
        }
        if (StringUtils.isNotBlank(wxMemberSessionDTO.getPhoneNum())) {
            wxStoreMerchantOrderDO.setPhone(wxMemberSessionDTO.getPhoneNum());
        }
        return wxStoreMerchantOrderDO;
    }

    /**
     * 获取商品数量
     *
     * @param itemInfoDTOS 商品集合
     * @return 数量
     */
    private Integer chargeItemCount(List<ItemInfoDTO> itemInfoDTOS) {
        return ObjectUtils.isEmpty(itemInfoDTOS)
                ? 0
                : itemInfoDTOS.stream().map(x -> x.getItemType() == 3
                        ? BigDecimal.ONE
                        : x.getCurrentCount())
                .reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
    }

    private void delCartItem(ItemInfoDTO itemInfoDTO, Integer orderModel) {
        log.info("移除购物车商品[{}-{}]", itemInfoDTO.getItemGuid(), itemInfoDTO.getName());
        String uniqueKey = ItemInfoDTO.getUniqueKey(itemInfoDTO);
        delCartItem(uniqueKey, orderModel);
    }

    private void delCartItem(String itemUck, Integer orderModel) {
        if (orderModel == 0) {
            redisUtils.hDelete(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getDiningTableGuid(), itemUck);
        } else {
            redisUtils.hDelete(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getOpenId(), itemUck);
        }
    }

    /**
     * 快餐下单
     *
     * @param orderSubmitReqDTO 人数
     * @return 快餐返回
     */
    @Override
    public OrderSubmitRespDTO submitFast(OrderSubmitReqDTO orderSubmitReqDTO) {
        List<ItemInfoDTO> shopCartItemList = orderSubmitReqDTO.getShopCartItemList();
        String merchantGuid = redisUtils.generatdDTOGuid(WxStoreMerchantOrderDO.class);
        CreateFastFoodReqDTO createFastFoodReqDTO;
        try {
            createFastFoodReqDTO = initialFastOrder(orderSubmitReqDTO, shopCartItemList, merchantGuid);
        } catch (Exception e) {
            log.error("[快餐下单初始化]e={}", e.getMessage(), e);
            return OrderSubmitRespDTO.upperLimit(e.getMessage());
        }
        log.info("快餐下单入参:{}", createFastFoodReqDTO);
        EstimateItemRespDTO estimateResult = tradeClientService.createAppletsOrder(createFastFoodReqDTO);
        String orderGuid = estimateResult.getOrderGuid();
        log.info("快餐订单id:{}", orderGuid);
        if (ObjectUtils.isEmpty(orderGuid)) {
            log.error("快餐下单失败:{}", createFastFoodReqDTO);
            return OrderSubmitRespDTO.submitFailed();
        }
        PricePairDTO pricePairDTO = priceCalculationUtils.orderPrice(shopCartItemList);
        String orderRecordGuid = redisUtils.generatdDTOGuid(CreateFastFoodReqDTO.class);
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        log.info("订单id：{}", orderRecordGuid);
        initialFastOrderRecord(orderGuid, orderRecordGuid, wxMemberSessionDTO, merchantGuid, pricePairDTO);
        initialFastOrderItem(merchantGuid, orderGuid, orderRecordGuid, orderSubmitReqDTO.getShopCartItemReqDTOS());
        initialFastMerchant(orderRecordGuid, orderSubmitReqDTO.getUserCount(), orderGuid, merchantGuid,
                shopCartItemList, wxMemberSessionDTO, pricePairDTO.getOriginPrice(), orderSubmitReqDTO.getRemark());
        // 同步订单到赚餐
        asyncTcdOrder(orderSubmitReqDTO, orderGuid);
        Message message = new Message(RocketMqConfig.FAST_ORDER_DELAY_TOPIC, RocketMqConfig.FAST_ORDER_DELAY_TAG,
                JacksonUtils.toJsonByte(new FastOrderDelayMQDTO(wxMemberSessionDTO.getEnterpriseGuid(), orderRecordGuid)));
        message.setDelayTimeLevel(18);//15m延迟队列
        defaultRocketMqProducer.sendMessage(message);
        redisUtils.setEx(CacheName.FAST_ESTIMATE + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getOpenId(),
                shopCartItemList, 20, TimeUnit.MINUTES);
        redisUtils.delete(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getOpenId());
        //将桌台和员工关联信息存Redis
        tableStaffRelationExecutor.execute(() -> setTableStaffInfo(orderGuid, WeixinUserThreadLocal.getDiningTableGuid()));
        // 将订单使用附加费存入redis
        wxStoreTradeOrderService.setOrderSurchargeCache(orderGuid, createFastFoodReqDTO.getSurchargeLinkList());
        return new OrderSubmitRespDTO().setOrderRecordGuid(orderRecordGuid).setOrderGuid(orderGuid);
    }

    /**
     * 同步订单到赚餐
     */
    private void asyncTcdOrder(OrderSubmitReqDTO orderSubmitReqDTO, String orderGuid) {
        // 快餐小程序下单 需要同步到赚餐
        if (!BaseDeviceTypeEnum.isApplet(orderSubmitReqDTO.getDeviceType())) {
            return;
        }
        tcdOrderService.asyncOrder(orderGuid);
    }

    /**
     * 订单商品初始化
     *
     * @param orderGuid       商户订单id
     * @param orderRecordGuid 用户订单id
     */
    private void initialFastOrderItem(String merchantGuid, String orderGuid, String orderRecordGuid, List<ShopCartItemReqDTO> shopCartItemReqDTOS) {
        saveBatch(transformOrderItem(shopCartItemReqDTOS, orderGuid, orderRecordGuid, merchantGuid));
    }

    /**
     * 快餐用户订单初始化
     *
     * @param orderGuid       订单id
     * @param orderRecordGuid 用户订单id
     */
    private void initialFastOrderRecord(String orderGuid, String orderRecordGuid, WxMemberSessionDTO wxMemberSessionDTO, String merchantGuid, PricePairDTO pricePairDTO) {
        WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO();
        WxUserRecordDO user = wxUserRecordService.getOneByOpenId(WeixinUserThreadLocal.getOpenId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        wxOrderRecordDO.setUserRecordGuid(user.getGuid());
        wxOrderRecordDO.setGuid(orderRecordGuid);
        wxOrderRecordDO.setOrderGuid(orderGuid);
        wxOrderRecordDO.setMerchantGuid(merchantGuid);
        wxOrderRecordDO.setOrderMode(1);

        wxOrderRecordDO.setOrderState(5);
        wxOrderRecordDO.setOrderStateName("待支付");
        wxOrderRecordDO.setActuallyPayFee(pricePairDTO.getOriginPrice());
        wxOrderRecordDO.setUnMemberPrice(pricePairDTO.getMemberPrice());
        wxOrderRecordDO.setTableGuid(wxMemberSessionDTO.getDiningTableGuid());
        wxOrderRecordDO.setTableCode(wxMemberSessionDTO.getDiningTableCode());
        wxOrderRecordDO.setAreaGuid(wxMemberSessionDTO.getAreaGuid());
        wxOrderRecordDO.setAreaName(wxMemberSessionDTO.getAreaName());
        wxOrderRecordDO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        wxOrderRecordDO.setStoreName(wxMemberSessionDTO.getStoreName());
        wxOrderRecordDO.setBrandGuid(wxMemberSessionDTO.getBrandGuid());
        BrandDTO brandDetail = wxStoreSessionDetailsService.getBrandDetail(wxMemberSessionDTO.getBrandGuid());
        wxOrderRecordDO.setLogUrl(brandDetail.getLogoUrl());
        wxOrderRecordDO.setBrandName(brandDetail.getName());
        boolean save = wxOrderRecordService.save(wxOrderRecordDO);
        log.info("快餐下单：微信订单保存是否成功：{}", save);
    }

    /**
     * 快餐下单初始化
     *
     * @return 订单
     */
    private CreateFastFoodReqDTO initialFastOrder(OrderSubmitReqDTO orderSubmitReqDTO, List<ItemInfoDTO> itemInfoDTOS, String merchantGuid) {
        CreateFastFoodReqDTO createFastFoodReqDTO = new CreateFastFoodReqDTO();
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        WxUserInfoDTO wxUserInfoDTO = WeixinUserThreadLocal.getWxUserInfoDTO();
        createFastFoodReqDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        createFastFoodReqDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        createFastFoodReqDTO.setStoreName(wxMemberSessionDTO.getStoreName());
        createFastFoodReqDTO.setDeviceId(wxUserInfoDTO.getOpenId());
        createFastFoodReqDTO.setDeviceType(BaseDeviceTypeEnum.WECHAT.getCode());
        if (Objects.nonNull(createFastFoodReqDTO.getDeviceType())) {
            createFastFoodReqDTO.setDeviceType(orderSubmitReqDTO.getDeviceType());
        }
        createFastFoodReqDTO.setMemberGuid(wxMemberSessionDTO.getMemberInfoGuid());
        createFastFoodReqDTO.setMemberCardGuid(orderSubmitReqDTO.getMemberCardGuid());
        RequestQueryMemberInfo queryMemberInfo = new RequestQueryMemberInfo();
        queryMemberInfo.setMemberInfoGuid(wxMemberSessionDTO.getMemberInfoGuid());
        ResponseMemberInfo memberInfo = wechatClientService.getMemberInfo(queryMemberInfo).getData();
        if (!ObjectUtils.isEmpty(memberInfo)) {
            createFastFoodReqDTO.setMemberPhone(memberInfo.getPhoneNum());
            createFastFoodReqDTO.setMemberName(memberInfo.getUserName());
        }
        createFastFoodReqDTO.setUserName(wxUserInfoDTO.getNickname());
        createFastFoodReqDTO.setUserGuid(wxUserInfoDTO.getOpenId());
        createFastFoodReqDTO.setUserWxPublicOpenId(wxUserInfoDTO.getOpenId());
        createFastFoodReqDTO.setWeixinTablleGuid(wxMemberSessionDTO.getDiningTableGuid());
        createFastFoodReqDTO.setWeixinTableCode(wxMemberSessionDTO.getAreaName() + "-" + wxMemberSessionDTO.getDiningTableCode());
        createFastFoodReqDTO.setRemark(orderSubmitReqDTO.getRemark());
        createFastFoodReqDTO.setGuestCount(orderSubmitReqDTO.getUserCount());
        createFastFoodReqDTO.setAutoMark(1);
        List<DineInItemDTO> calculateItemResultList = orderSubmitReqDTO.getCalculateItemResultList();
        calculateItemResultList.forEach(calculateItemResult -> {
            handleUckAttrGroup(calculateItemResult.getAttrGroupList());
            handleUckSubGroup(calculateItemResult.getSubgroupList());
        });
        log.info("[处理后计算商品]calculateItemResultList={}", JacksonUtils.writeValueAsString(calculateItemResultList));
        Map<String, DineInItemDTO> itemSkuCalculateMap = calculateItemResultList.stream()
                .collect(Collectors.toMap(i -> getGroupByKey(i.getSkuList(), i.getAttrGroupList(), i.getSubgroupList()),
                        Function.identity(), (v1, v2) -> v1));
        log.info("[分组map]itemSkuCalculateMap={}", JacksonUtils.writeValueAsString(itemSkuCalculateMap));
        createFastFoodReqDTO.setDineInItemDTOS(transformFastItem(itemInfoDTOS, merchantGuid, itemSkuCalculateMap));
        // 附加费
        createFastFoodReqDTO.setSurchargeLinkList(transformSurchargeLinkList(orderSubmitReqDTO.getUserCount(), orderSubmitReqDTO.getSurchargeList()));
        if (Objects.isNull(orderSubmitReqDTO.getUserCount()) || orderSubmitReqDTO.getUserCount() == 0) {
            createFastFoodReqDTO.setGuestCount(1);
        }
        // 优惠信息
        createFastFoodReqDTO.setDiscountFeeDetailDTOS(orderSubmitReqDTO.getDiscountFeeDetailDTOS());
        return createFastFoodReqDTO;
    }

    /**
     * 子项选中
     */
    private void handleUckSubGroup(List<ItemInfoSubgroupDTO> subgroupList) {
        if (org.springframework.util.CollectionUtils.isEmpty(subgroupList)) {
            return;
        }
        subgroupList.forEach(subgroup -> {
            List<ItemInfoSubSkuDTO> subItemSkuList = subgroup.getSubItemSkuList();
            if (org.springframework.util.CollectionUtils.isEmpty(subItemSkuList)) {
                return;
            }
            subItemSkuList.removeIf(subItemSku -> subItemSku.getDefaultNum() == BooleanEnum.FALSE.getCode());
            if (!org.springframework.util.CollectionUtils.isEmpty(subItemSkuList)) {
                subItemSkuList.forEach(subItem -> {
                    handleUckAttrGroup(subItem.getAttrGroupList());
                });
            }
        });
    }

    /**
     * 规格-属性-子项
     * 注意：调用该方法前要在外面处理选中的问题
     */
    public String getGroupByKey(List<ItemInfoSkuDTO> skuList,
                                List<ItemInfoAttrGroupDTO> attrGroupList,
                                List<ItemInfoSubgroupDTO> subgroupList) {
        List<String> groupByKeyList = new ArrayList<>();
        // 规格选中
        ItemInfoSkuDTO uckSku = getUckSku(skuList);
        groupByKeyList.add(0, uckSku.getSkuGuid());

        // 属性选中
        if (!org.springframework.util.CollectionUtils.isEmpty(attrGroupList)) {
            List<String> attrGuidList = attrGroupList.stream()
                    .flatMap(g -> g.getAttrList().stream())
                    .map(ItemInfoAttrDTO::getAttrGuid)
                    .filter(Objects::nonNull)
                    .sorted()
                    .collect(Collectors.toList());
            groupByKeyList.addAll(attrGuidList);
        }

        // 子项选中
        if (!org.springframework.util.CollectionUtils.isEmpty(subgroupList)) {
            List<String> subSkuGuidList = subgroupList.stream()
                    .flatMap(g -> g.getSubItemSkuList().stream())
                    .map(ItemInfoSubSkuDTO::getSkuGuid)
                    .filter(Objects::nonNull)
                    .sorted()
                    .collect(Collectors.toList());
            groupByKeyList.addAll(subSkuGuidList);

            // 子项属性
            List<String> subSkuAttrGuidList = subgroupList.stream()
                    .flatMap(g -> g.getSubItemSkuList().stream())
                    .flatMap(sg -> sg.getAttrGroupList().stream())
                    .flatMap(sa -> sa.getAttrList().stream())
                    .map(ItemInfoAttrDTO::getAttrGuid)
                    .filter(Objects::nonNull)
                    .sorted()
                    .collect(Collectors.toList());
            groupByKeyList.addAll(subSkuAttrGuidList);
        }

        log.info("groupByKeyList={}", groupByKeyList);
        return DigestUtils.md5DigestAsHex(String.join("-", groupByKeyList).getBytes());
    }

    /**
     * 规格选中
     */
    private ItemInfoSkuDTO getUckSku(List<ItemInfoSkuDTO> skuList) {
        Assert.isTrue(!ObjectUtils.isEmpty(skuList), "规格不能为空");
        if (skuList.size() == 1) {
            return skuList.get(0);
        }
        Optional<ItemInfoSkuDTO> first = skuList.stream()
                .filter(x -> x.getUck() == 1)
                .findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        throw new BusinessException("规格必选");
    }

    /**
     * 属性选中
     */
    private void handleUckAttrGroup(List<ItemInfoAttrGroupDTO> attrGroupList) {
        if (org.springframework.util.CollectionUtils.isEmpty(attrGroupList)) {
            return;
        }
        attrGroupList.forEach(attrGroup -> {
            attrGroup.getAttrList().removeIf(attr -> attr.getUck() == BooleanEnum.FALSE.getCode());
        });
        attrGroupList.removeIf(attrGroup -> org.springframework.util.CollectionUtils.isEmpty(attrGroup.getAttrList()));
    }

    /**
     * 快餐merchant订单初始化
     *
     * @param userCount 人数
     */
    private void initialFastMerchant(String orderRecordGuid, Integer userCount, String orderGuid, String merchantGuid, List<ItemInfoDTO> itemInfoDTOS
            , WxMemberSessionDTO wxMemberSessionDTO, BigDecimal orderPrice, String remark) {
        WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setOrderGuid(orderGuid);
        wxStoreMerchantOrderDO.setOrderRecordGuid(orderRecordGuid);
        wxStoreMerchantOrderDO.setGuid(merchantGuid);
        wxStoreMerchantOrderDO.setActualGuestsNo(userCount);
        wxStoreMerchantOrderDO.setTotalPrice(orderPrice);
        WxUserInfoDTO wxUserInfoDTO = WeixinUserThreadLocal.getWxUserInfoDTO();
        Assert.notNull(wxUserInfoDTO, "下单人不能为空");
        wxStoreMerchantOrderDO.setOpenId(wxUserInfoDTO.getOpenId());
        wxStoreMerchantOrderDO.setNickName(wxUserInfoDTO.getNickname());
        wxStoreMerchantOrderDO.setHeadImgUrl(wxUserInfoDTO.getHeadImgUrl());
        wxStoreMerchantOrderDO.setOrderState(1);
        wxStoreMerchantOrderDO.setTradeMode(1);
        wxStoreMerchantOrderDO.setCurrentOrder(1);
        wxStoreMerchantOrderDO.setDiningTableGuid(wxMemberSessionDTO.getDiningTableGuid());
        wxStoreMerchantOrderDO.setTableCode(wxMemberSessionDTO.getDiningTableCode());
        wxStoreMerchantOrderDO.setAreaGuid(wxMemberSessionDTO.getAreaGuid());
        wxStoreMerchantOrderDO.setAreaName(wxMemberSessionDTO.getAreaName());
        wxStoreMerchantOrderDO.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
        wxStoreMerchantOrderDO.setStoreName(WeixinUserThreadLocal.get().getStoreName());
        wxStoreMerchantOrderDO.setBrandGuid(wxMemberSessionDTO.getBrandGuid());
        wxStoreMerchantOrderDO.setBrandName(wxMemberSessionDTO.getBrandName());
        if (StringUtils.isNotBlank(wxMemberSessionDTO.getPhoneNum())) {
            wxStoreMerchantOrderDO.setPhone(wxMemberSessionDTO.getPhoneNum());
        }
        wxStoreMerchantOrderDO.setItemCount(chargeItemCount(itemInfoDTOS));
        wxStoreMerchantOrderDO.setRemark(remark);
        boolean save = wxStoreMerchantOrderService.save(wxStoreMerchantOrderDO);
        WxStoreMerchantOrderDTO wxStoreMerchantOrder = WxStoreMerchantOrderMapstruct.INSTANCE.getWxStoreMerchantOrder(wxStoreMerchantOrderDO);
        redisUtils.setEx(CacheName.FAST_NOTIFY + ":" + orderGuid, wxStoreMerchantOrder, 20, TimeUnit.MINUTES);
        log.info("快餐下单，批次保存状态:{},批次:{}", save, wxStoreMerchantOrderDO);
    }


    /**
     * 根据订单批次guid查询商
     *
     * @param merchantGuid merchantGuid
     * @return 转换
     */
    @Override
    public List<DineInItemDTO> getItemListByMerchantGuid(String merchantGuid) {
        return transformDineInItem(lambdaQuery().eq(WxOrderItemDO::getMerchantGuid, merchantGuid).list());
    }

    @Override
    public List<DineInItemDTO> getItemListByOrderGuid(String orderGuid) {
        List<WxOrderItemDO> wxOrderItemDOS = list(new LambdaQueryWrapper<WxOrderItemDO>()
                .eq(WxOrderItemDO::getOrderGuid, orderGuid));
        return transformDineInItem(wxOrderItemDOS);
    }

    /**
     * 商品转换
     *
     * @param wxOrderItemDOS 商品集合
     * @return 转换
     */
    private List<DineInItemDTO> transformDineInItem(List<WxOrderItemDO> wxOrderItemDOS) {
        return ObjectUtils.isEmpty(wxOrderItemDOS)
                ? Collections.emptyList()
                : wxOrderItemDOS.stream().map(x -> {
            DineInItemDTO dineInItemDTO = new DineInItemDTO();
            dineInItemDTO.setUserWxPublicOpenId(WeixinUserThreadLocal.getOpenId());
            dineInItemDTO.setItemGuid(x.getItemGuid());
            dineInItemDTO.setItemName(x.getItemName());
            dineInItemDTO.setCode(x.getCode());
            dineInItemDTO.setItemType(x.getItemType() == 5 ? 1 : x.getItemType());
            dineInItemDTO.setItemState(1);
            dineInItemDTO.setItemTypeGuid(x.getItemTypeGuid());
            dineInItemDTO.setItemTypeName(x.getItemTypeName());
            dineInItemDTO.setSkuGuid(x.getSkuGuid());
            dineInItemDTO.setSkuName(x.getSkuName());
            dineInItemDTO.setPrice(x.getSkuPrice());
            dineInItemDTO.setItemPrice(x.getOriginalPrice());
            BigDecimal skuMemberPrice = x.getSkuMemberPrice();
            dineInItemDTO.setMemberPrice(skuMemberPrice == null || skuMemberPrice.compareTo(BigDecimal.ZERO) == 0 ?
                    null : skuMemberPrice);
            dineInItemDTO.setOriginalPrice(x.getSkuPrice());
            dineInItemDTO.setCurrentCount(x.getCurrentCount());
            dineInItemDTO.setFreeCount(BigDecimal.ZERO);
            dineInItemDTO.setReturnCount(BigDecimal.ZERO);
            dineInItemDTO.setUnit(x.getSkuUnit());
            dineInItemDTO.setIsPay(0);
            dineInItemDTO.setIsMemberDiscount(x.getIsMemberDiscount());
            dineInItemDTO.setIsWholeDiscount(x.getIsWholeDiscount());
            dineInItemDTO.setMinOrderNum(x.getMinOrderNum());
            dineInItemDTO.setPriceChangeType(0);
            dineInItemDTO.setFreeItemDTOS(Collections.emptyList());
            String itemAttr = x.getItemAttr();
            String subgroup = x.getSubgroup();
            List<ItemInfoAttrGroupDTO> itemInfoAttrGroupDTOS = JacksonUtils.toObjectList(ItemInfoAttrGroupDTO.class, itemAttr);
            List<ItemInfoSubgroupDTO> itemInfoSubgroupDTOS = JacksonUtils.toObjectList(ItemInfoSubgroupDTO.class, subgroup);
            dineInItemDTO.setItemAttrDTOS(OrderItemTransfUtils.transformItemAttrGroup(itemInfoAttrGroupDTOS));
            dineInItemDTO.setPackageSubgroupDTOS(transformSubgroup(itemInfoSubgroupDTOS));
            dineInItemDTO.setSingleItemAttrTotal(priceCalculationUtils.getAttrTotalPrice(itemInfoAttrGroupDTOS));
            ItemCouponInfoDTO itemCouponInfoDTO = buildItemCouponInfoDTO(x);
            dineInItemDTO.setCouponInfo(itemCouponInfoDTO.getCouponPreRespDTO());
            dineInItemDTO.setGroupVerify(itemCouponInfoDTO.getGroupVerify());
            return dineInItemDTO;
        }).collect(Collectors.toList());
    }

    private ItemCouponInfoDTO buildItemCouponInfoDTO(WxOrderItemDO wxOrderItemDO) {
        String couponInfo = wxOrderItemDO.getCouponInfo();
        if (StringUtils.isEmpty(couponInfo)) {
            return new ItemCouponInfoDTO();
        }
        return JacksonUtils.toObject(ItemCouponInfoDTO.class, couponInfo);
    }


    /**
     * 快餐商品转换
     *
     * @param itemInfoDTOS        商品集合
     * @param itemSkuCalculateMap
     * @return 商品集合
     */
    private List<DineInItemDTO> transformFastItem(List<ItemInfoDTO> itemInfoDTOS,
                                                  String merchantGuid,
                                                  Map<String, DineInItemDTO> itemSkuCalculateMap) {
        return ObjectUtils.isEmpty(itemInfoDTOS)
                ? Collections.emptyList()
                : itemInfoDTOS.stream().map(x -> {
            DineInItemDTO dineInItemDTO = new DineInItemDTO();
            dineInItemDTO.setUserWxPublicOpenId(WeixinUserThreadLocal.getOpenId());
            dineInItemDTO.setWxBatch(merchantGuid);
            dineInItemDTO.setItemGuid(x.getItemGuid());
            dineInItemDTO.setItemName(x.getName());
            ItemInfoSkuDTO uckSku = getUckSku(x);
            handleUckAttrGroup(x.getAttrGroupList());
            String groupByKey = getGroupByKey(x.getSkuList(), x.getAttrGroupList(), x.getSubgroupList());
            log.warn("groupByKey={}", groupByKey);
            DineInItemDTO calculateItem = itemSkuCalculateMap.get(groupByKey);
            if (ObjectUtils.isEmpty(calculateItem) || !BigDecimalUtil.equal(x.getCurrentCount(), calculateItem.getCurrentCount())) {
                throw new BusinessException("商品价格不一致，请刷新购物车");
            }
            dineInItemDTO.setCode(uckSku.getCode());
            dineInItemDTO.setItemType(x.getItemType() == 5 ? 1 : x.getItemType());
            dineInItemDTO.setItemState(1);
            dineInItemDTO.setItemTypeGuid(x.getTypeGuid());
            dineInItemDTO.setItemTypeName(x.getTypeName());
            dineInItemDTO.setSkuGuid(uckSku.getSkuGuid());
            dineInItemDTO.setSkuName(uckSku.getName());
            dineInItemDTO.setPrice(uckSku.getSalePrice());
            dineInItemDTO.setItemPrice(priceCalculationUtils.itemPrice(x).getOriginPrice());
            BigDecimal memberPrice = uckSku.getMemberPrice();
            dineInItemDTO.setOriginalPrice(uckSku.getSalePrice());
            dineInItemDTO.setMemberPrice(memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0 ? memberPrice : null);
            dineInItemDTO.setCurrentCount(x.getCurrentCount());
            dineInItemDTO.setFreeCount(BigDecimal.ZERO);
            dineInItemDTO.setReturnCount(BigDecimal.ZERO);
            dineInItemDTO.setUnit(uckSku.getUnit());
            dineInItemDTO.setIsPay(0);
            dineInItemDTO.setPriceChangeType(0);
            dineInItemDTO.setSingleItemAttrTotal(priceCalculationUtils.getAttrTotalPrice(x.getAttrGroupList()));
            dineInItemDTO.setIsMemberDiscount(uckSku.getIsMemberDiscount());
            dineInItemDTO.setIsWholeDiscount(uckSku.getIsWholeDiscount());
            dineInItemDTO.setMinOrderNum(uckSku.getMinOrderNum());
            dineInItemDTO.setFreeItemDTOS(Collections.emptyList());
            dineInItemDTO.setItemAttrDTOS(OrderItemTransfUtils.transformItemAttrGroup(x.getAttrGroupList()));
            dineInItemDTO.setPackageSubgroupDTOS(transformSubgroup(x.getSubgroupList()));
            dineInItemDTO.setDiscountTotalPrice(calculateItem.getDiscountTotalPrice());
            return dineInItemDTO;
        }).collect(Collectors.toList());
    }

    private List<SurchargeLinkDTO> transformSurchargeLinkList(Integer userCount, List<PreOrderSurchargeDTO> surchargeList) {
        if (CollectionUtils.isEmpty(surchargeList)) {
            return Lists.newArrayList();
        }
        if (userCount == 0) {
            surchargeList = surchargeList.stream().filter(e -> AppendFeeTypeEnum.BY_TABLE.getCode() == e.getType()).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(surchargeList)) {
            return Lists.newArrayList();
        }
        return surchargeList.stream().map(e -> {
            SurchargeLinkDTO surchargeLinkDTO = new SurchargeLinkDTO();
            BeanUtils.copyProperties(e, surchargeLinkDTO);
            return surchargeLinkDTO;

        }).collect(Collectors.toList());
    }

    /**
     * 套餐分组转换
     *
     * @param itemInfoSubgroupDTOS 分组集合
     * @return 转换
     */
    private List<PackageSubgroupDTO> transformSubgroup(List<ItemInfoSubgroupDTO> itemInfoSubgroupDTOS) {
        if (ObjectUtils.isEmpty(itemInfoSubgroupDTOS)) {
            return Collections.emptyList();
        }
        LinkedList<PackageSubgroupDTO> packageSubgroupDTOS = new LinkedList<>();
        for (ItemInfoSubgroupDTO itemInfoSubgroupDTO : itemInfoSubgroupDTOS) {
            PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
            packageSubgroupDTO.setSubgroupGuid(itemInfoSubgroupDTO.getSubgroupGuid());
            packageSubgroupDTO.setSubgroupName(itemInfoSubgroupDTO.getName());
            packageSubgroupDTO.setSubDineInItemDTOS(transformSubDineInItem(itemInfoSubgroupDTO.getSubItemSkuList(), itemInfoSubgroupDTO.getPickNum()));
            packageSubgroupDTOS.add(packageSubgroupDTO);
        }
        return packageSubgroupDTOS;
    }

    /**
     * 套餐子项转换
     *
     * @param subItemSkuList 子项
     * @return 转换
     */
    private List<SubDineInItemDTO> transformSubDineInItem(List<ItemInfoSubSkuDTO> subItemSkuList, Integer pickNum) {
        LinkedList<SubDineInItemDTO> subDineInItemDTOS = new LinkedList<>();
        for (ItemInfoSubSkuDTO itemInfoSubSkuDTO : subItemSkuList) {
            log.info("商品转换:套餐子商品:{}", itemInfoSubSkuDTO);
            if (pickNum == 0 || itemInfoSubSkuDTO.getDefaultNum() > 0) {
                SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
                subDineInItemDTO.setItemGuid(itemInfoSubSkuDTO.getItemGuid());
                subDineInItemDTO.setItemName(itemInfoSubSkuDTO.getItemName());
                subDineInItemDTO.setCode(itemInfoSubSkuDTO.getCode());
                subDineInItemDTO.setItemType(itemInfoSubSkuDTO.getItemType());
                subDineInItemDTO.setItemTypeGuid(itemInfoSubSkuDTO.getItemTypeGuid());
                subDineInItemDTO.setItemTypeName(itemInfoSubSkuDTO.getItemTypeName());
                subDineInItemDTO.setSkuGuid(itemInfoSubSkuDTO.getSkuGuid());
                subDineInItemDTO.setSkuName(itemInfoSubSkuDTO.getSkuName());
                subDineInItemDTO.setUnit(itemInfoSubSkuDTO.getUnit());
                subDineInItemDTO.setPrice(itemInfoSubSkuDTO.getSalePrice());
                subDineInItemDTO.setCurrentCount(new BigDecimal(itemInfoSubSkuDTO.getDefaultNum()));
                subDineInItemDTO.setPackageDefaultCount(itemInfoSubSkuDTO.getItemNum());
                subDineInItemDTO.setSingleItemAttrTotal(priceCalculationUtils.getAttrTotalPrice(itemInfoSubSkuDTO.getAttrGroupList()));
                subDineInItemDTO.setAddPrice(itemInfoSubSkuDTO.getAddPrice());
                subDineInItemDTO.setItemAttrDTOS(OrderItemTransfUtils.transformItemAttrGroup(itemInfoSubSkuDTO.getAttrGroupList()));
                subDineInItemDTOS.add(subDineInItemDTO);
            }
        }
        return subDineInItemDTOS;
    }


    /**
     * 规格选中
     *
     * @param itemInfoDTO 商品
     * @return 规格
     */
    private ItemInfoSkuDTO getUckSku(ItemInfoDTO itemInfoDTO) {
        List<ItemInfoSkuDTO> skuList = itemInfoDTO.getSkuList();
        Assert.isTrue(!ObjectUtils.isEmpty(skuList), "规格不能为空");
        if (skuList.size() == 1) {
            return skuList.get(0);
        }
        Optional<ItemInfoSkuDTO> first = skuList.stream().filter(x -> x.getUck() == 1).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        log.error("商品:{}", JacksonUtils.writeValueAsString(itemInfoDTO));
        throw new RuntimeException("规格必选");
    }

}
