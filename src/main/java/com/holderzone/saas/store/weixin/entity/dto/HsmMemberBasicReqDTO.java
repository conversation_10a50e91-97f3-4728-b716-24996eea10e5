package com.holderzone.saas.store.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmMemberBasicReqDTO
 * @date 2019/06/25 14:23
 * @description 会员登录请求入参
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ApiModel("会员登录请求入参")
public class HsmMemberBasicReqDTO {
    @ApiModelProperty(value = "会员来源企业Guid")
    private String memberEnterpriseGuid;

    @ApiModelProperty(value = "会员来源企业名字")
    private String memberEnterpriseName;

    @ApiModelProperty(value = "微信openID")
    private String openid;

    @ApiModelProperty(value = "会员电话号码")
    private String phoneNum;

    @ApiModelProperty(value = "登录或注册验证码(当soureType=25时，smsCode非必填)")
    private String smsCode;

    @ApiModelProperty(value = "会员注册来源,0后台添加,1POS机注册,1一体机注册，20微信关注，21微信扫码点餐，22预定，23排队，24微信注册.25微信C端后台注册")
    private Integer sourceType;

    @ApiModelProperty(value = "微信UnionID")
    private String unionId;

    @ApiModelProperty(value = "微信昵称")
    private String nickName;

    @ApiModelProperty(value = "微信头像url")
    private String headImageUrl;

    @ApiModelProperty(value = "性别")
    private Integer sex;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;
}
