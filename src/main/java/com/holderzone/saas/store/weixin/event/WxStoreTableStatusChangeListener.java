package com.holderzone.saas.store.weixin.event;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TableStatusChangeMQDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.weixin.constant.RocketMqConfig;
import com.holderzone.saas.store.weixin.helper.WebsocketMessageHelper;
import com.holderzone.saas.store.weixin.service.WxStoreTableStatusChangeService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.TimeZone;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTableStatusChangeListener
 * @date 2019/5/20
 */
@Component
@Slf4j
@RocketListenerHandler(
		topic = RocketMqConfig.TABLE_STATUS_CHANGE_MQ_TABLE,
		tags = {RocketMqConfig.TABLE_STATUS_CHANGE_MQ_COMBINE,RocketMqConfig.TABLE_STATUS_CHANGE_MQ_SEPARATE,RocketMqConfig.TABLE_STATUS_CHANGE_MQ_TURN},
		consumerGroup = RocketMqConfig.TABLE_STATUS_CHANGE_WEIXIN_GROUP
		)
public class WxStoreTableStatusChangeListener extends AbstractRocketMqConsumer<RocketMqTopic, String> {

	private final WxStoreTableStatusChangeService wxStoreTableStatusChangeService;

	private final WebsocketMessageHelper websocketMessageHelper;

	private static ObjectMapper objectMapper;
	static {
		objectMapper = new ObjectMapper();
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		objectMapper.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
		objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
		objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.registerModule(new JavaTimeModule());
	}

	@Autowired
	public WxStoreTableStatusChangeListener(WxStoreTableStatusChangeService wxStoreTableStatusChangeService, WebsocketMessageHelper websocketMessageHelper) {
		this.wxStoreTableStatusChangeService = wxStoreTableStatusChangeService;
		this.websocketMessageHelper = websocketMessageHelper;
	}

	private <T> T toObject(String jsonStr, TypeReference<T> typeReference) {
		try {
			return objectMapper.readValue(jsonStr, typeReference);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	@Override
	public boolean consumeMsg(String jsonStr, MessageExt messageExt) {
		JSONObject jsonObject = JacksonUtils.toJSONObject(jsonStr);
		String type = jsonObject.getString("type");

		if (RocketMqConfig.TABLE_STATUS_CHANGE_MQ_COMBINE.equals(type)) {
			TableStatusChangeMQDTO<TableCombineDTO> obj = toObject(jsonStr,
					new TypeReference<TableStatusChangeMQDTO<TableCombineDTO>>() {});
			wxStoreTableStatusChangeService.combine(obj.getTableStatus());
		} else if (RocketMqConfig.TABLE_STATUS_CHANGE_MQ_TURN.equals(type)) {
			TableStatusChangeMQDTO<TurnTableDTO> obj = toObject(jsonStr,
					new TypeReference<TableStatusChangeMQDTO<TurnTableDTO>>() {});
			TurnTableDTO turnTableDTO = obj.getTableStatus();
			wxStoreTableStatusChangeService.turn(turnTableDTO);
		} else if (RocketMqConfig.TABLE_STATUS_CHANGE_MQ_SEPARATE.equals(type)) {
			TableStatusChangeMQDTO<TableOrderCombineDTO> obj = toObject(jsonStr,
					new TypeReference<TableStatusChangeMQDTO<TableOrderCombineDTO>>() {});
			wxStoreTableStatusChangeService.separate(obj.getTableStatus());
		}
		return true;
	}


}
