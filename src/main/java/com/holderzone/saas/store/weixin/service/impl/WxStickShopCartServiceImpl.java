package com.holderzone.saas.store.weixin.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxStickShopCartDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickShopCartRemoveDTO;
import com.holderzone.saas.store.weixin.constant.ModelName;
import com.holderzone.saas.store.weixin.entity.domain.WxStickShopCartDO;
import com.holderzone.saas.store.weixin.mapper.WxStickShopCartMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStickShopCartMapstruct;
import com.holderzone.saas.store.weixin.service.WxStickShopCartService;
import com.holderzone.saas.store.weixin.service.rpc.WxStickModelClientService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickShopCartServiceImpl
 * @date 2019/03/12 11:08
 * @description 微信桌贴购物车service实现类
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxStickShopCartServiceImpl extends ServiceImpl<WxStickShopCartMapper, WxStickShopCartDO>
        implements WxStickShopCartService {

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    WxStickShopCartMapstruct wxStickShopCartMapstruct;

    @Autowired
    WxStickModelClientService wxStickModelClientService;

    @Override
    public List<WxStickShopCartDTO> listShopCart() {
        List<WxStickShopCartDO> shopCartDOList = list();
        if (shopCartDOList.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> guidList = shopCartDOList.stream()
                .map(WxStickShopCartDO::getModelGuid)
                .collect(Collectors.toList());

        WxStickModelReqDTO wxStickModelReqDTO = new WxStickModelReqDTO();
        wxStickModelReqDTO.setList(guidList);

        List<WxTableStickDTO> wxTableStickDTOList = wxStickModelClientService.getTableStickList(wxStickModelReqDTO);
        Map<String, WxTableStickDTO> wxTableStickDTOMap = wxTableStickDTOList.stream()
                .collect(Collectors.toMap(WxTableStickDTO::getGuid, Function.identity()));

        return guidList.stream()
                .map(guid -> {
                    WxTableStickDTO wxTableStickDTO = wxTableStickDTOMap.get(guid);
                    if (wxTableStickDTO != null) {
                        return new WxStickShopCartDTO(guid, wxTableStickDTO.getName(), wxTableStickDTO.getPreviewImg(), wxTableStickDTO.getPrice());
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public void addModels(List<WxStickShopCartDTO> wxStickShopCartDTOList) {
        if (wxStickShopCartDTOList == null || wxStickShopCartDTOList.isEmpty()) {
            return;
        }

        List<String> newGuids = wxStickShopCartDTOList.stream()
                .map(WxStickShopCartDTO::getModelGuid)
                .distinct()
                .filter(guid -> list().stream().noneMatch(cart -> cart.getModelGuid().equals(guid)))
                .collect(Collectors.toList());

        List<WxStickShopCartDO> wxStickShopCartDOList = newGuids.stream()
                .map(modelGuid -> {
                    WxStickShopCartDO wxStickShopCartDO = new WxStickShopCartDO();
                    String guid = redisUtils.generateGuid(ModelName.WX + ":" + modelGuid + ":createModel");
                    wxStickShopCartDO.setGuid(guid);
                    wxStickShopCartDO.setGmtCreate(LocalDateTime.now());
                    wxStickShopCartDO.setModelGuid(modelGuid);
                    return wxStickShopCartDO;
                })
                .collect(Collectors.toList());

        if (!wxStickShopCartDOList.isEmpty()) {
            saveBatch(wxStickShopCartDOList);
        }
    }

    @Override
    public void removeModels(WxStickShopCartRemoveDTO wxStickShopCartRemoveDTO) {
        if (wxStickShopCartRemoveDTO == null) {
            return;
        }

        if (wxStickShopCartRemoveDTO.getIsClear() == 1) {
            remove(new LambdaQueryWrapper<>());
        } else {
            List<String> guidList = wxStickShopCartRemoveDTO.getGuidList();
            if (guidList != null && !guidList.isEmpty()) {
                remove(new LambdaQueryWrapper<WxStickShopCartDO>().in(WxStickShopCartDO::getModelGuid, guidList));
            }
        }
    }
}
