package com.holderzone.saas.store.weixin.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreWebSocketUserDTO;
import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreMenuReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.*;
import com.holderzone.saas.store.enums.weixin.WxOrderStateEnum;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreConfigRespDTOMapstruct;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreItemRespMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMenuDetailsServiceImpl
 * @date 2019/2/22 13:50
 * 微信点餐菜单信息service
 * com.holderzone.saas.store.weixin.service.impl
 */
@Service
@Slf4j
public class WxStoreMenuDetailsServiceImpl implements WxStoreMenuDetailsService {

	private final WxStoreMenuItemClientService wxStoreMenuItemClientService;
	private final WxStoreOrderConfigService wxStoreOrderConfigService;
	private final WxStoreConfigRespDTOMapstruct wxStoreConfigRespDTOMapstruct;
	private final WxStoreItemRespMapstruct wxStoreItemRespMapstruct;
	private final RedisUtils redisUtils;
	private final DynamicHelper dynamicHelper;
	private final WxStoreDineInOrderClientService wxStoreDineInOrderClientService;
	private final WxStoreTableClientService wxStoreTableClientService;
	private final WxStoreSessionDetailsService wxStoreSessionDetailsService;
	private final WxConfigOverviewService wxConfigOverviewService;
	private final WxStoreEstimateClientService wxStoreEstimateClientService;
	private final WxUserRecordService wxUserRecordService;

	private final WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;

	private final EnterpriseClientService enterpriseClientService;
	@Lazy
	@Autowired
	private WxStoreSocketSessionService wxStoreSocketSessionService;

	@Resource
	private MemberClientService memberClientService;

	@Resource
	private HsaBaseClientService hsaBaseClientService;

	@Autowired
	public WxStoreMenuDetailsServiceImpl(
			WxStoreMenuItemClientService wxStoreMenuItemClientService,
			WxStoreOrderConfigService wxStoreOrderConfigService,
			WxStoreConfigRespDTOMapstruct wxStoreConfigRespDTOMapstruct, WxStoreItemRespMapstruct wxStoreItemRespMapstruct, RedisUtils redisUtils, DynamicHelper dynamicHelper, WxStoreDineInOrderClientService wxStoreDineInOrderClientService, WxStoreTableClientService wxStoreTableClientService, WxStoreSessionDetailsService wxStoreSessionDetailsService, WxConfigOverviewService wxConfigOverviewService, WxStoreEstimateClientService wxStoreEstimateClientService, WxUserRecordService wxUserRecordService, WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService, EnterpriseClientService enterpriseClientService) {
		this.wxStoreMenuItemClientService = wxStoreMenuItemClientService;
		this.wxStoreOrderConfigService = wxStoreOrderConfigService;
		this.wxStoreConfigRespDTOMapstruct = wxStoreConfigRespDTOMapstruct;
		this.wxStoreItemRespMapstruct = wxStoreItemRespMapstruct;
		this.redisUtils = redisUtils;
		this.dynamicHelper = dynamicHelper;
		this.wxStoreDineInOrderClientService = wxStoreDineInOrderClientService;
		this.wxStoreTableClientService = wxStoreTableClientService;
		this.wxStoreSessionDetailsService = wxStoreSessionDetailsService;
		this.wxConfigOverviewService = wxConfigOverviewService;
		this.wxStoreEstimateClientService = wxStoreEstimateClientService;
		this.wxUserRecordService = wxUserRecordService;
		this.wxStoreAuthorizerInfoService = wxStoreAuthorizerInfoService;
		this.enterpriseClientService = enterpriseClientService;
	}

    /**
     * @param menuRequest menu
     * @return WxMenuDetailsDTO
     * 找远程调用的菜品，分类，标签封装
     * <AUTHOR>
     * getWxMenuDetailsDTO
     * @date 2019/2/22 13:52
     */
    @Override
    public WxMenuDetailsDTO getWxMenuDetails(WxStoreMenuReqDTO menuRequest) {
        StopWatch stopWatch = new StopWatch();
        WxStoreConsumerDTO consumerDTO = menuRequest.getWxStoreConsumerDTO();
        setupDataSource(consumerDTO);

        stopWatch.start("Clear Single Cache");
        wxStoreSessionDetailsService.delPrepay(consumerDTO.getStoreGuid(), consumerDTO.getOpenId());
        stopWatch.stop();

        stopWatch.start("Clear Multiple Caches");
        clearOrderCache(consumerDTO);
        stopWatch.stop();

        stopWatch.start("Page Navigation");
        if (shouldNavigateToOrderDetails(menuRequest, consumerDTO)) {
            log.info("Navigating to order details");
            return WxMenuDetailsDTO.builder().isJump(1).build();
        }
        stopWatch.stop();

        stopWatch.start("Fetch Items");
        ItemAndTypeForAndroidRespDTO items = fetchItemsForConsumer(consumerDTO);
        stopWatch.stop();

        stopWatch.start("Fetch Store Config");
        WxOrderConfigDTO orderConfig = fetchStoreOrderConfig(consumerDTO);
        stopWatch.stop();

        validateStoreOpenStatus(consumerDTO, orderConfig);

        try {
            WxMenuDetailsDTO menuDetails = encapsulateStoreTags(items, orderConfig);
            stopWatch.start("Estimate Items");
            estimateItems(menuDetails.getItemList(), createUserInfoDTO(menuRequest));
            stopWatch.stop();

            saveFirstConsumer(consumerDTO, menuDetails);
            return menuDetails;
        } catch (Exception e) {
            return WxMenuDetailsDTO.builder()
                    .itemList(Collections.emptyList())
                    .wxStoreConfigRespDTO(new WxStoreConfigRespDTO())
                    .typeList(Collections.emptyList())
                    .build();
        }
    }

    private void setupDataSource(WxStoreConsumerDTO consumerDTO) {
        UserContext userContext = UserContextUtils.get();
        if (ObjectUtils.isEmpty(userContext)) {
            userContext = new UserContext();
        }
        userContext.setEnterpriseGuid(consumerDTO.getEnterpriseGuid());
        userContext.setStoreGuid(consumerDTO.getStoreGuid());
        UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
        EnterpriseIdentifier.setEnterpriseGuid(consumerDTO.getEnterpriseGuid());
    }

    private boolean shouldNavigateToOrderDetails(WxStoreMenuReqDTO menuRequest, WxStoreConsumerDTO consumerDTO) {
        return menuRequest.getWxPermission() == 1 && isOrderTypeValid(consumerDTO.getStoreGuid()) && isStatusValid(consumerDTO);
    }

    /**
     * 清理订单缓存
     *
     * @param consumerDTO 消费者信息
     */
    private void clearOrderCache(WxStoreConsumerDTO consumerDTO) {
        Integer orderState = wxStoreSessionDetailsService.getOrderState(consumerDTO.getDiningTableGuid());
        if (!ObjectUtils.isEmpty(orderState) && (orderState == 3 || orderState == 2 || orderState == 6 || orderState == 7)) {
            if (isOrderTypeValid(consumerDTO.getStoreGuid())) {
                wxStoreSessionDetailsService.delOrderState(consumerDTO.getDiningTableGuid());
                wxStoreSessionDetailsService.delOrderGuid(consumerDTO.getDiningTableGuid());
                wxStoreSessionDetailsService.delMerchantBatchGuid(consumerDTO.getDiningTableGuid());
                wxStoreSessionDetailsService.delDinnerGuestsCount(consumerDTO.getDiningTableGuid());
                wxStoreSessionDetailsService.delTablePaidUser(consumerDTO.getDiningTableGuid());
                wxStoreSessionDetailsService.delFirstPerson(consumerDTO.getOpenId());
                wxStoreSessionDetailsService.saveOrderState(consumerDTO.getDiningTableGuid(), WxOrderStateEnum.PENDING.getCode());
                WxStoreAdvanceConsumerReqDTO singleSession = wxStoreSocketSessionService.getSingleSession(WxStoreAdvanceConsumerReqDTO.builder().wxStoreConsumerDTO(consumerDTO).build());
                wxStoreSocketSessionService.delTableSession(consumerDTO.getDiningTableGuid());
                if (!Objects.isNull(singleSession)) {
                    wxStoreSocketSessionService.createSession(singleSession);
                }
            }
        }
    }

    /**
     * 检查订单类型是否有效
     *
     * @param storeGuid 门店唯一标识
     * @return 是否有效
     */
    private boolean isOrderTypeValid(String storeGuid) {
        WxOrderConfigDTO detailConfig = getDetailConfig(storeGuid);
        // 判断订单模式是否为正餐模式
        return detailConfig.getOrderModel() == 0;
    }

    /**
     * 检查消费者状态是否有效
     *
     * @param consumerDTO 消费者信息
     * @return 是否有效
     */
    private boolean isStatusValid(WxStoreConsumerDTO consumerDTO) {
        if (ObjectUtils.isEmpty(consumerDTO.getDiningTableGuid())) {
            log.error("用户传参tableGuid为空:{}", consumerDTO);
            return false;
        }
        String orderGuid = wxStoreTableClientService.getOrderGuid(consumerDTO.getDiningTableGuid());
        if (!StringUtils.isEmpty(orderGuid)) {
            try {
                DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder().data(orderGuid).build());
                // 检查订单详情是否存在并且包含就餐项目
                return !ObjectUtils.isEmpty(orderDetail) && !ObjectUtils.isEmpty(orderDetail.getDineInItemDTOS());
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    /**
     * 估算商品
     *
     * @param itemList    商品列表
     * @param userInfoDTO 用户信息
     */
    private void estimateItems(List<WxStoreItemRespDTO> itemList, UserInfoDTO userInfoDTO) {
        UserContextUtils.put(JacksonUtils.writeValueAsString(userInfoDTO));
        List<ItemEstimateForAndroidRespDTO> estimates = getItemEstimates(userInfoDTO);
        itemList.forEach(item -> estimateItem(item, estimates));
    }

    /**
     * 创建用户信息DTO
     *
     * @param menuRequest 菜单请求
     * @return 用户信息DTO
     */
    private UserInfoDTO createUserInfoDTO(WxStoreMenuReqDTO menuRequest) {
        WxStoreConsumerDTO consumerDTO = menuRequest.getWxStoreConsumerDTO();
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid(consumerDTO.getEnterpriseGuid());
        userInfoDTO.setStoreGuid(consumerDTO.getStoreGuid());
        return userInfoDTO;
    }

    /**
     * 保存首位消费者信息
     *
     * @param consumerDTO 消费者信息
     * @param menuDetails 菜单详情
     */
    private void saveFirstConsumer(WxStoreConsumerDTO consumerDTO, WxMenuDetailsDTO menuDetails) {
        WxStoreConsumerDTO firstConsumer = wxStoreSessionDetailsService.getFirstPerson(consumerDTO.getOpenId());
        if (ObjectUtils.isEmpty(firstConsumer)) {
            menuDetails.setFirst(0);
            wxStoreSessionDetailsService.saveFirstPerson(consumerDTO);
        }
    }

    /**
     * 估算单个商品
     *
     * @param item      商品
     * @param estimates 估算信息列表
     */
    private void estimateItem(WxStoreItemRespDTO item, List<ItemEstimateForAndroidRespDTO> estimates) {
        item.getSkuList().forEach(sku -> {
            estimates.stream()
                    .filter(estimate -> sku.getSkuGuid().equals(estimate.getSkuGuid()))
                    .findFirst()
                    .ifPresent(estimate -> {
                        sku.setIsSoldOut(estimate.getIsSoldOut());
                        sku.setResidueQuantity(estimate.getResidueQuantity());
                    });
        });
    }

    /**
     * 获取商品估算信息
     *
     * @param userInfoDTO 用户信息
     * @return 商品估算列表
     */
    private List<ItemEstimateForAndroidRespDTO> getItemEstimates(UserInfoDTO userInfoDTO) {
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
        baseDTO.setStoreGuid(userInfoDTO.getStoreGuid());
        return wxStoreEstimateClientService.queryEstimateForSyn(baseDTO);
    }

    private ItemAndTypeForAndroidRespDTO fetchItemsForConsumer(WxStoreConsumerDTO consumerDTO) {
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setEnterpriseGuid(consumerDTO.getEnterpriseGuid());
        baseDTO.setStoreGuid(consumerDTO.getStoreGuid());
        return wxStoreMenuItemClientService.getItemsForWeixin(baseDTO);
    }

    private WxOrderConfigDTO fetchStoreOrderConfig(WxStoreConsumerDTO consumerDTO) {
        WxStoreReqDTO storeRequest = new WxStoreReqDTO();
        storeRequest.setStoreGuid(consumerDTO.getStoreGuid());
        return wxStoreOrderConfigService.getDetailConfig(storeRequest);
    }

    private void validateStoreOpenStatus(WxStoreConsumerDTO consumerDTO, WxOrderConfigDTO orderConfig) {
        Pair<WxStoreInfoDTO, Boolean> storeOpenStatus = wxConfigOverviewService.isStoreOpen(0, consumerDTO.getStoreGuid());
        if (ObjectUtils.isEmpty(storeOpenStatus) || !storeOpenStatus.getRight()) {
            throw new BusinessException("当前门店暂未开启微信点餐功能");
        }
        updateOrderConfig(orderConfig, storeOpenStatus);
        reverseStoreConfig(orderConfig);
    }

    private void updateOrderConfig(WxOrderConfigDTO orderConfig, Pair<WxStoreInfoDTO, Boolean> storeOpenStatus) {
        WxStoreInfoDTO storeInfo = storeOpenStatus.getLeft();
        orderConfig.setStoreGuid(storeInfo.getStoreGuid());
        orderConfig.setStoreName(storeInfo.getStoreName());
        orderConfig.setBrandNameList(storeInfo.getBrandNameList());
        orderConfig.setIsOpened(storeInfo.getIsOpened());
    }

    private void reverseStoreConfig(WxOrderConfigDTO orderConfig) {
        Integer menuType = orderConfig.getMenuType();
        if (!ObjectUtils.isEmpty(menuType)) {
            orderConfig.setMenuType(menuType == 1 ? 0 : 1);
        }
    }

    /**
     * 封装商店标签和配置
     *
     * @param itemsForWeixin 商品和类型信息
     * @param orderConfig    订单配置
     * @return 菜单详情DTO
     */
    private WxMenuDetailsDTO encapsulateStoreTags(ItemAndTypeForAndroidRespDTO itemsForWeixin, WxOrderConfigDTO orderConfig) {
        WxMenuDetailsDTO menuDetails = new WxMenuDetailsDTO();
        List<WxTypeAndTagDTO> typeAndTagList = new ArrayList<>();

        if (combineItemsAndTypes(itemsForWeixin, menuDetails, typeAndTagList)) {
            return WxMenuDetailsDTO.builder()
                    .isJump(0)
                    .itemList(Collections.emptyList())
                    .wxStoreConfigRespDTO(new WxStoreConfigRespDTO())
                    .typeList(Collections.emptyList())
                    .build();
        }

        combineTagsAndConfig(orderConfig, menuDetails, typeAndTagList);

        // 排序并设置类型列表
        menuDetails.setTypeList(typeAndTagList.stream()
                .sorted(Comparator.comparing(WxTypeAndTagDTO::getSort))
                .collect(Collectors.toList()));

        setDefaultAttributes(menuDetails);
        return menuDetails;
    }

    /**
     * 组合商品和类型
     *
     * @param itemsForWeixin 商品和类型信息
     * @param menuDetails    菜单详情
     * @param typeAndTagList 类型和标签列表
     * @return 是否成功组合
     */
    private boolean combineItemsAndTypes(ItemAndTypeForAndroidRespDTO itemsForWeixin, WxMenuDetailsDTO menuDetails, List<WxTypeAndTagDTO> typeAndTagList) {
        if (!ObjectUtils.isEmpty(itemsForWeixin)) {
            List<ItemSynRespDTO> filteredItems = filterItemList(itemsForWeixin.getItemList());
            log.info("接收商品推送:{}", filteredItems);
            List<TypeSynRespDTO> typeList = itemsForWeixin.getTypeList();
            return combineItems(menuDetails, typeAndTagList, filteredItems, typeList);
        }
        return false;
    }

    /**
     * 组合商品和类型
     * @param menuDetails 菜单详情
     * @param typeAndTagList 类型和标签列表
     * @param itemList 商品列表
     * @param typeList 类型列表
     * @return 是否成功组合
     */
    private boolean combineItems(WxMenuDetailsDTO menuDetails, List<WxTypeAndTagDTO> typeAndTagList, List<ItemSynRespDTO> itemList, List<TypeSynRespDTO> typeList) {
        if (ObjectUtils.isNotEmpty(itemList)) {
            validateItems(itemList);
            List<WxStoreItemRespDTO> wxStoreItems = wxStoreItemRespMapstruct.getWxStoreItem(itemList);
            setDefaultSelections(wxStoreItems);
            addTypesToTagList(typeAndTagList, typeList);
            revertTagsToEnglish(wxStoreItems);
            menuDetails.setItemList(wxStoreItems);
        } else {
            return true;
        }
        return false;
    }

    /**
     * 验证商品属性
     * @param itemList 商品列表
     */
    private void validateItems(List<ItemSynRespDTO> itemList) {
        itemList.forEach(item -> {
            item.getAttrGroupList().forEach(attrGroup -> {
                if (attrGroup.getIsMultiChoice() == 0) {
                    long defaultCount = attrGroup.getAttrList().stream().filter(attr -> attr.getIsDefault() == 1).count();
                    if (defaultCount > 1) {
                        throw new BusinessException("商品默认属性错误");
                    }
                }
            });
        });
    }

    /**
     * 设置默认选择
     * @param wxStoreItems 商品列表
     */
    private void setDefaultSelections(List<WxStoreItemRespDTO> wxStoreItems) {
        wxStoreItems.forEach(item -> {
            List<WxStoreSkuRespDTO> skuList = item.getSkuList();
            if (!skuList.isEmpty()) {
                skuList.get(0).setUserck(1); // 默认选中第一个规格
                item.setCurrentCount(skuList.get(0).getMinOrderNum()); // 默认选择数量为第一个规格的起卖数
                for (int i = 1; i < skuList.size(); i++) {
                    skuList.get(i).setUserck(0);
                }
            }
        });
    }

    /**
     * 添加类型到标签列表
     * @param typeAndTagList 类型和标签列表
     * @param typeList 类型列表
     */
    private void addTypesToTagList(List<WxTypeAndTagDTO> typeAndTagList, List<TypeSynRespDTO> typeList) {
        typeList.forEach(type -> {
            WxTypeAndTagDTO typeDTO = new WxTypeAndTagDTO();
            BeanUtils.copyProperties(type, typeDTO);
            typeDTO.setIsType(1); // 设置为1表示类型
            typeAndTagList.add(typeDTO);
        });
    }

    /**
     * 将标签的名字中文换成英文
     *
     * @param wxStoreItems 商品列表
     */
    private void revertTagsToEnglish(List<WxStoreItemRespDTO> wxStoreItems) {
        wxStoreItems.forEach(item -> item.getTagList().forEach(tag -> tag.setName(tag.getId())));
    }

    /**
     * 组合标签和配置
     *
     * @param orderConfig    订单配置
     * @param menuDetails    菜单详情
     * @param typeAndTagList 类型和标签列表
     */
    private void combineTagsAndConfig(WxOrderConfigDTO orderConfig, WxMenuDetailsDTO menuDetails, List<WxTypeAndTagDTO> typeAndTagList) {
        if (!ObjectUtils.isEmpty(orderConfig)) {
            WxStoreConfigRespDTO storeConfig = wxStoreConfigRespDTOMapstruct.wxStoreOrderConfigRespDTO2WxStoreConfigRespDTO(orderConfig);
            List<String> tagList = Arrays.asList(orderConfig.getTagNames().split(","));
            combineTags(typeAndTagList, tagList);
            menuDetails.setWxStoreConfigRespDTO(storeConfig);
        }
    }

    /**
     * 组合标签
     *
     * @param typeAndTagList 类型和标签列表
     * @param tagList        标签列表
     */
    private void combineTags(List<WxTypeAndTagDTO> typeAndTagList, List<String> tagList) {
        if (!ObjectUtils.isEmpty(tagList)) {
            tagList.forEach(tagName -> {
                WxTypeAndTagDTO tagDTO = new WxTypeAndTagDTO();
                tagDTO.setName(tagName);
                tagDTO.setIsType(0); // 设置为0表示标签
                tagDTO.setSort(-1);
                tagDTO.setTypeGuid(tagName);
                typeAndTagList.add(tagDTO);
            });
        }
    }

    /**
     * 设置默认属性
     *
     * @param menuDetails 菜单详情
     */
    private void setDefaultAttributes(WxMenuDetailsDTO menuDetails) {
        menuDetails.getItemList().forEach(item -> item.getSubgroupList()
                .forEach(subgroup -> subgroup.getSubItemSkuList()
                        .forEach(subItemSku -> subItemSku.getAttrGroupList().forEach(attrGroup -> {
                            if (attrGroup.getIsRequired() == 1 && attrGroup.getAttrList().stream().noneMatch(attr -> attr.getIsDefault() == 1)) {
                                attrGroup.getAttrList().get(0).setIsDefault(1);
                            }
                        }))));
    }

    /**
     * @param wxStoreConsumerDTO consumer
     * @return config
     * 获取当前门店配置详情
     */
    @Override
    public WxOrderConfigDTO getStoreConfiguration(WxStoreConsumerDTO wxStoreConsumerDTO) {
        WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
        wxStoreReqDTO.setStoreGuid(wxStoreConsumerDTO.getStoreGuid());
        WxOrderConfigDTO detailConfig = wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
        if (ObjectUtils.isEmpty(detailConfig))
            throw new BusinessException("当前门店暂未开启微信点餐功能");
        log.info("微信点餐，已获取到门店配置详情：{}", detailConfig);
        return detailConfig;
    }

	@Override
	public WxOrderConfigDTO getStoreConfiguration(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		return getStoreConfiguration(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO());
	}

	@Override
	public Boolean judgeOrderType(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxOrderConfigDTO storeConfiguration = getStoreConfiguration(wxStoreAdvanceConsumerReqDTO);
		if (Objects.isNull(storeConfiguration) || ObjectUtils.isEmpty(storeConfiguration.getOrderModel())) {
			log.error("当前门店没有配置");
		}
		if (Objects.isNull(storeConfiguration.getOrderModel())) {
			log.error("当前门店没有配置正餐还是快餐");
		}
		return storeConfiguration.getOrderModel() == 0;
	}

	@Override
	public Boolean judgeOrderType(WxStoreWebSocketUserDTO wxStoreWebSocketUserDTO) {
		return judgeOrderType(wxStoreWebSocketUserDTO.getWxStoreConsumerDTO().getStoreGuid());
	}

	@Override
	public Boolean judgeOrderType(String storeGuid) {
		WxOrderConfigDTO detailConfig =getDetailConfig(storeGuid);
		return detailConfig.getOrderModel() == 0;
	}
	
	@Override
	public WxOrderConfigDTO getDetailConfig(String storeGuid) {
		WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
		wxStoreReqDTO.setStoreGuid(storeGuid);
		return wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
	}

	@Override
	public Integer getTakingModel(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxOrderConfigDTO storeConfiguration = getStoreConfiguration(wxStoreAdvanceConsumerReqDTO);
		return storeConfiguration.getTakingModel();
	}

	@Override
	public Integer getTakingModel(String storeGuid) {
		WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
		wxStoreReqDTO.setStoreGuid(storeGuid);
		WxOrderConfigDTO detailConfig = wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
		return detailConfig.getTakingModel();
	}

	@Override
	public WxStoreConsumerDTO getConsumerInfo(WxPortalReqDTO wxPortalReqDTO) {
		Assert.notBlank(wxPortalReqDTO.getEnterpriseGuid(), BusinessName.GLOBAL_DEFAULT_ERROR_MSG);
		dynamicHelper.changeDatasource(wxPortalReqDTO.getEnterpriseGuid());
		String consumerJson = (String) redisUtils.get(wxPortalReqDTO.getMsgKey());
		if (StringUtils.hasText(consumerJson)) {
			WxStoreConsumerDTO wxStoreConsumerDTO = JacksonUtils.toObject(WxStoreConsumerDTO.class, consumerJson);
			if(StringUtils.isEmpty(wxStoreConsumerDTO.getStoreGuid())) {
				wxStoreConsumerDTO.setStoreGuid(wxPortalReqDTO.getStoreGuid());
			}

			WxMemberSessionDTO memberByOpenId = WxMemberSessionUtil.getMemberByOpenId(redisUtils, wxStoreConsumerDTO.getOpenId());
			log.info("Redis中的会员信息{}",JacksonUtils.writeValueAsString(memberByOpenId));
			RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
			requestQueryMemberInfo.setOpenId(memberByOpenId.getWxUserInfoDTO().getOpenId());
			ResponseMemberInfo memberInfo =
					hsaBaseClientService.getMemberInfo(requestQueryMemberInfo).getData();
			log.info("首次进入：查询到会员详细{}", memberInfo);
			//TODO openId与运营主体GUID对应一个会员信息  openId与运营主体GUID不一致查不出会员信息 则默认会员不登录？
			if(memberInfo!=null&& org.apache.commons.lang3.StringUtils.isNotEmpty(memberInfo.getPhoneNum())){
				//weixinMemberSessionDTO.setPhoneNum(memberInfo.getPhoneNum());
				wxStoreConsumerDTO.setPhoneNum(memberInfo.getPhoneNum());
				wxStoreConsumerDTO.setIsLogin(memberByOpenId.getWxUserInfoDTO().getIsLogin());
			}else {
				wxStoreConsumerDTO.setPhoneNum("");
				wxStoreConsumerDTO.setIsLogin(false);
			}
			wxStoreConsumerDTO.setBrandLogo(memberByOpenId.getLogUrl());
			wxStoreConsumerDTO.setBrandName(memberByOpenId.getBrandName());
			wxStoreConsumerDTO.setNickName(memberByOpenId.getWxUserInfoDTO().getNickname());
			wxStoreConsumerDTO.setHeadImgUrl(memberByOpenId.getWxUserInfoDTO().getHeadImgUrl());


			if (Objects.nonNull(wxPortalReqDTO.getBrandGuid())){
				WxStoreAuthorizerInfoDO infoDO = wxStoreAuthorizerInfoService.getByBrandId(wxPortalReqDTO.getBrandGuid());
				log.info("品牌绑定的相关运营主体信息{}",JacksonUtils.writeValueAsString(infoDO));
				//运营主体是否为联盟
				Boolean isAlliance = false;
				//运营主体状态
				Boolean multiMemberStatus = false;
				//运营主体guid
				String operSubjectGuid = "";
				if (Objects.nonNull(infoDO)){
					isAlliance = infoDO.getIsAlliance();
					operSubjectGuid = infoDO.getOperSubjectGuid();
				}
				if (!StringUtils.isEmpty(operSubjectGuid)){
					List<MultiMemberDTO> multiMemberDTOS = enterpriseClientService.list(new HashSet<>(Collections.singleton(operSubjectGuid)));
					if (org.apache.commons.collections.CollectionUtils.isNotEmpty(multiMemberDTOS)){
						multiMemberStatus = multiMemberDTOS.get(0).getEnabled();
					}
				}
				wxStoreConsumerDTO.setIsAlliance(isAlliance);
				wxStoreConsumerDTO.setMultiMemberStatus(multiMemberStatus);
				wxStoreConsumerDTO.setOperSubjectGuid(operSubjectGuid);
			}
			log.info("getConsumerInfo 最终返回信息：{}",JacksonUtils.writeValueAsString(memberByOpenId));
			return wxStoreConsumerDTO;
			//WxUserRecordDO oneByOpenId = wxUserRecordService.getOneByOpenId(wxStoreConsumerDTO.getOpenId());
			//redisUtils.delete(wxPortalReqDTO.getMsgKey());
			/*
			WxStoreConsumerDTO consumerInfo = wxUserRecordService.getConsumerInfo(wxStoreConsumerDTO.getOpenId());
			wxStoreConsumerDTO.setNickName(consumerInfo.getNickName());
			wxStoreConsumerDTO.setHeadImgUrl(consumerInfo.getHeadImgUrl());
			wxStoreConsumerDTO.setIsLogin(consumerInfo.getIsLogin());
			HsmMemberInfoAndQrCodeRespDTO memberInfo =
					memberClientService.getMemberInfo(wxStoreConsumerDTO.getOpenId(), wxStoreConsumerDTO.getEnterpriseGuid());
			if(memberInfo!=null&&!StringUtils.isEmpty(memberInfo.getPhoneNum())){
				wxStoreConsumerDTO.setPhoneNum(memberInfo.getPhoneNum());
			}
			//WxMemberSessionUtil.getMemberByOpenId(redisUtils,wxStoreConsumerDTO.getOpenId())
			return wxStoreConsumerDTO;
			*/
		}
		return null;
	}

    @Override
    public void estimateItem(List<WxStoreItemRespDTO> wxStoreItem, UserInfoDTO userInfoDTO) {
        UserContextUtils.put(JacksonUtils.writeValueAsString(userInfoDTO));
        itemEstimate(wxStoreItem, getItemEstimateForAndroidRespDTOS(userInfoDTO));
    }

	/**
	 * 商品估清
	 *
	 * @param wxStoreItem                    item
	 * @param itemEstimateForAndroidRespDTOS estimate
	 */
	private void itemEstimate(List<WxStoreItemRespDTO> wxStoreItem, List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS) {
		for (WxStoreItemRespDTO wxStoreItemRespDTO : wxStoreItem) {
			List<WxStoreSkuRespDTO> skuList = wxStoreItemRespDTO.getSkuList();
			//商品sku估清
			itemSkuEstimate(itemEstimateForAndroidRespDTOS, skuList);
			subGroupEstimate(itemEstimateForAndroidRespDTOS, wxStoreItemRespDTO, wxStoreItemRespDTO.getSubgroupList());
		}
	}

	/**
	 * 套餐估清
	 *
	 * @param itemEstimateForAndroidRespDTOS estimate
	 * @param wxStoreItemRespDTO             item
	 * @param subgroupList                   group
	 */
	private void subGroupEstimate(List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS, WxStoreItemRespDTO wxStoreItemRespDTO, List<SubgroupSynRespDTO> subgroupList) {
		if (!ObjectUtils.isEmpty(subgroupList)) {
			boolean flag = true;
			for (SubgroupSynRespDTO subgroupSynRespDTO : subgroupList) {
				List<SubItemSkuSynRespDTO> subItemSkuList = subgroupSynRespDTO.getSubItemSkuList();
				if (!ObjectUtils.isEmpty(subItemSkuList)) {
					//商品sku
					for (SubItemSkuSynRespDTO subItemSkuSynRespDTO : subItemSkuList) {
						for (ItemEstimateForAndroidRespDTO itemEstimateForAndroidRespDTO : itemEstimateForAndroidRespDTOS) {
							if (subItemSkuSynRespDTO.getSkuGuid().equals(itemEstimateForAndroidRespDTO.getSkuGuid())) {
								subItemSkuSynRespDTO.setIsSoldOut(itemEstimateForAndroidRespDTO.getIsSoldOut());
								subItemSkuSynRespDTO.setResidueQuantity(itemEstimateForAndroidRespDTO.getResidueQuantity());
								//如果是必选分组则直接估清
								if (subgroupSynRespDTO.getPickNum() == 0 && subItemSkuSynRespDTO.getIsSoldOut() == 2) {
									List<WxStoreSkuRespDTO> skuList1 = wxStoreItemRespDTO.getSkuList();
									skuList1.get(0).setIsSoldOut(2);
								}
							}
						}
						if (subItemSkuSynRespDTO.getIsSoldOut() == 1) {
							flag = false;
						}
					}
				}
			}
			//可选套餐，统统估清了，则套餐估清
			if (wxStoreItemRespDTO.getIsFixPkg() == 0 && flag) {
				List<WxStoreSkuRespDTO> skuList1 = wxStoreItemRespDTO.getSkuList();
				skuList1.get(0).setIsSoldOut(2);
			}
		}
	}

	private void itemSkuEstimate(List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS, List<WxStoreSkuRespDTO> skuList) {
		for (WxStoreSkuRespDTO wxStoreSkuRespDTO : skuList) {
			//估清表
			for (ItemEstimateForAndroidRespDTO itemEstimateForAndroidRespDTO : itemEstimateForAndroidRespDTOS) {
				if (wxStoreSkuRespDTO.getSkuGuid().equals(itemEstimateForAndroidRespDTO.getSkuGuid())) {
					wxStoreSkuRespDTO.setIsSoldOut(itemEstimateForAndroidRespDTO.getIsSoldOut());
					wxStoreSkuRespDTO.setResidueQuantity(itemEstimateForAndroidRespDTO.getResidueQuantity());
					break;
				}
			}
		}
	}

	/**
	 * 估清清单
	 *
	 * @param userInfoDTO info
	 * @return estimate
	 */
	private List<ItemEstimateForAndroidRespDTO> getItemEstimateForAndroidRespDTOS(UserInfoDTO userInfoDTO) {
		BaseDTO baseDTO = new BaseDTO();
		baseDTO.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
		baseDTO.setStoreGuid(userInfoDTO.getStoreGuid());
		return wxStoreEstimateClientService.queryEstimateForSyn(baseDTO);
	}

    /**
     * 过滤商品列表，保留微信上架的商品
     * @param itemSynRespDTOList 原始商品列表
     * @return 过滤后的商品列表
     */
    private List<ItemSynRespDTO> filterItemList(List<ItemSynRespDTO> itemSynRespDTOList) {
        if (CollectionUtils.isEmpty(itemSynRespDTOList)) {
            return itemSynRespDTOList;
        }

        List<ItemSynRespDTO> filteredItems = itemSynRespDTOList.stream()
                .filter(this::isItemValid)
                .collect(Collectors.toList());

        log.info("过滤后的商品列表: {}", JacksonUtils.writeValueAsString(filteredItems));
        return filteredItems;
    }

    /**
     * 检查商品是否有效
     * @param item 商品
     * @return 是否有效
     */
    private boolean isItemValid(ItemSynRespDTO item) {
        if (CollectionUtils.isEmpty(item.getSkuList())) {
            log.info("商品【{}】未配置SKU", JacksonUtils.writeValueAsString(item));
            return false;
        }

        List<SkuSynRespDTO> validSkus = item.getSkuList().stream()
                .filter(sku -> Objects.equals(1, sku.getIsJoinWeChat()))
                .collect(Collectors.toList());

        if (validSkus.isEmpty()) {
            return false;
        }

        item.setSkuList(validSkus);

        if (isPackageItem(item)) {
            return isPackageValid(item);
        }

        return true;
    }

    /**
     * 检查商品是否为套餐
     * @param item 商品
     * @return 是否为套餐
     */
    private boolean isPackageItem(ItemSynRespDTO item) {
        return Objects.equals(1, item.getItemType()) && item.getIsFixPkg() == 0;
    }

    /**
     * 检查套餐是否有效
     * @param item 套餐商品
     * @return 是否有效
     */
    private boolean isPackageValid(ItemSynRespDTO item) {
        List<SubgroupSynRespDTO> validSubgroups = item.getSubgroupList().stream()
                .filter(this::isSubgroupValid)
                .collect(Collectors.toList());

        if (validSubgroups.isEmpty()) {
            return false;
        }

        item.setSubgroupList(validSubgroups);
        return true;
    }

    /**
     * 检查子分组是否有效
     * @param subgroup 子分组
     * @return 是否有效
     */
    private boolean isSubgroupValid(SubgroupSynRespDTO subgroup) {
        if (CollectionUtils.isEmpty(subgroup.getSubItemSkuList())) {
            log.info("子分组【{}】无子商品信息", JacksonUtils.writeValueAsString(subgroup));
            return false;
        }

        if (subgroup.getPickNum() == 0) {
            return subgroup.getSubItemSkuList().stream()
                    .noneMatch(sku -> Objects.equals(0, sku.getIsJoinWeChat()));
        }

        List<SubItemSkuSynRespDTO> validSubItemSkus = subgroup.getSubItemSkuList().stream()
                .filter(sku -> Objects.equals(1, sku.getIsJoinWeChat()))
                .collect(Collectors.toList());

        subgroup.setSubItemSkuList(validSubItemSkus);
        return !validSubItemSkus.isEmpty();
    }

}
