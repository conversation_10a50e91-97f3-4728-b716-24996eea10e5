package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.AbstractMemberModelItemService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DepositMemberModelItemServiceImpl extends AbstractMemberModelItemService {

	private WxStoreSessionDetailsService wxStoreSessionDetailsService;

	@Override
	public WxMemberOverviewModelDTO getWxMemberOverviewModel(WxStoreConsumerDTO wxStoreConsumerDTO) {
		return new WxMemberOverviewModelDTO(4, "我的寄存", 0);
	}
}
