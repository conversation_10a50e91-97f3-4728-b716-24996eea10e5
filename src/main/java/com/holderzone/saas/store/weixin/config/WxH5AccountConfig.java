package com.holderzone.saas.store.weixin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxAccountConfig
 * @date 2019/04/01 14:22
 * @description 微信公众号账号信息配置
 * @program holder-saas-store
 */
@Component
@ConfigurationProperties(prefix = "h5")
@Data
public class WxH5AccountConfig {

    private String token;

    private String appId;

    private String appSecret;

    private String componentAppId;

}
