package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsw_weixin_order_config")
public class WxOrderConfigDO implements Serializable {

    private static final long serialVersionUID = 6780296333227633073L;

    private Long id;
    /**
     * 主键
     */
    @TableId("guid")
    private String guid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 菜单样式（0双图，1单图，2小图）
     */
    private Integer menuType;

    /**
     * 点餐模式（0正餐，1快餐）
     */
    private Integer orderModel;

    /**
     * 接单模式（0手动接单，2首单手动，加菜自动）
     * 接单模式（0所有订单均需确认，1首单需确认）
     */
    private Integer takingModel;

    /**自动确认（0关闭1开启）*/
    private Integer autoConfirm;

    /**自动确认时间（分钟，自动确认接单时间）*/
    @TableField(strategy = FieldStrategy.IGNORED)
    private Long autoConfirmTime;

    /**用户下单后每隔多久进行提示（分钟，不填写默认提示一次）*/
    @TableField(strategy = FieldStrategy.IGNORED)
    private Long confirmPromptTime;

    /**
     * 是否开启线上买单（0关闭，1开启）
     */
    private Integer isOnlinePayed;

    /**
     * 顾客是否可点称重商品（0顾客不可点，1顾客可点）
     */
    private Integer isWeighingOrdered;

    /**
     * 是否开启单品备注（0关闭，1开启）
     */
    private Integer isRemarked;

    /**
     * 跳转页面url
     */
    private Integer urlType;

    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String tagNames;

    /**
     * 是否营业（0关闭，1开启）
     */
    private Integer isOrderOpen;
    /**
     * pad点餐主题类型(0深色主题,1浅色主题)
     */
    private Integer subjectType;

    /**
     * pad点餐背景图
     */
    private String padBackgroundUrl;

    /**
     * 是否结账不清台
     */
    private Integer isCheckoutUnCloseTable;

    /**
     * 是否游客模式 （0关闭，1开启）
     */
    private Integer guestFlag;

    /**
     * 支付团购平台
     */
    private String supportGrouponTypes;

    /**
     * 色系
     */
    private String styleColor;

    /**
     * 是否自动带入会员详细地址
     */
    private Integer autoInMemberInfoFlag;

    public WxOrderConfigDO setId(Long id) {
        this.id = id;
        return this;
    }

    public WxOrderConfigDO setGuid(String guid) {
        this.guid = guid;
        return this;
    }

    public WxOrderConfigDO setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
        return this;
    }

    public WxOrderConfigDO setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
        return this;
    }

    public WxOrderConfigDO setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
        return this;
    }

    public WxOrderConfigDO setMenuType(Integer menuType) {
        this.menuType = menuType;
        return this;
    }

    public WxOrderConfigDO setOrderModel(Integer orderModel) {
        this.orderModel = orderModel;
        return this;
    }

    public WxOrderConfigDO setTakingModel(Integer takingModel) {
        this.takingModel = takingModel;
        return this;
    }

    public WxOrderConfigDO setIsOnlinePayed(Integer isOnlinePayed) {
        this.isOnlinePayed = isOnlinePayed;
        return this;
    }

    public WxOrderConfigDO setIsWeighingOrdered(Integer isWeighingOrdered) {
        this.isWeighingOrdered = isWeighingOrdered;
        return this;
    }

    public WxOrderConfigDO setIsRemarked(Integer isRemarked) {
        this.isRemarked = isRemarked;
        return this;
    }

    public WxOrderConfigDO setUrlType(Integer urlType) {
        this.urlType = urlType;
        return this;
    }

    public WxOrderConfigDO setTagNames(String tagNames) {
        this.tagNames = tagNames;
        return this;
    }

    public WxOrderConfigDO setIsOrderOpen(Integer isOrderOpen) {
        this.isOrderOpen = isOrderOpen;
        return this;
    }

    public WxOrderConfigDO setGuestFlag(Integer guestFlag) {
        this.guestFlag = guestFlag;
        return this;
    }

    public WxOrderConfigDO setSupportGrouponTypes(String supportGrouponTypes) {
        this.supportGrouponTypes = supportGrouponTypes;
        return this;
    }


}