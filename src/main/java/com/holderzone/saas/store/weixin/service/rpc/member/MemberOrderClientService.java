//package com.holderzone.saas.store.weixin.service.rpc.member;
//
//import com.holderzone.holder.saas.cmember.app.dto.order.request.DiscountReqDTO;
//import com.holderzone.holder.saas.cmember.app.dto.order.request.VolumeConsumeReqDTO;
//import com.holderzone.holder.saas.cmember.app.dto.order.response.DiscountRespDTO;
//import com.holderzone.holder.saas.cmember.app.dto.order.response.VolumeCalculateRespDTO;
//import feign.hystrix.FallbackFactory;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.stereotype.Service;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//
//@Service
//@FeignClient(name = "HOLDER-SAAS-CMEMBER-ORDER-SERVICE", fallbackFactory = MemberOrderClientService.FallBackClass.class)
//public interface MemberOrderClientService {
//
////	@GetMapping("/hsmca/volume/consumeVolumeList")
////	List<VolumeListRespDTO> verifyVolumeList(@RequestParam(value = "orderNumber") String orderNumber);
//
//	@PostMapping("/hsmca/volume/{volumeCode}/calculate")
//	VolumeCalculateRespDTO satisfyVolume(@PathVariable("volumeCode") String volumeCode, @RequestBody VolumeConsumeReqDTO volumeConsumeReqDTO);
//
//	 @PostMapping("/hsmca/order/{memberInfoCardGuid}/discount")
//	  DiscountRespDTO discount(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid, @RequestBody DiscountReqDTO discountReqDTO);
//
//	@Component
//	@Slf4j
//	class FallBackClass implements FallbackFactory<MemberOrderClientService> {
//		@Override
//		public MemberOrderClientService create(Throwable throwable) {
//			return new MemberOrderClientService() {
////				@Override
////				public List<VolumeListRespDTO> verifyVolumeList(String orderGuid) {
////					log.error("查询已验券失败:{}",orderGuid);
////					throw new RuntimeException(throwable.getCause());
////				}
//
//				@Override
//				public VolumeCalculateRespDTO satisfyVolume(String volumeCode, VolumeConsumeReqDTO volumeConsumeReqDTO) {
//					log.error("查询优惠券是否满足失败:{}",volumeCode);
//					throw new RuntimeException(throwable.getCause());
//				}
//
//				@Override
//				public DiscountRespDTO discount(String memberInfoCardGuid, DiscountReqDTO discountReqDTO) {
//					log.error("查询会员折扣:{}",memberInfoCardGuid);
//					DiscountRespDTO discountRespDTO = new DiscountRespDTO();
//					discountRespDTO.setDishInfoDTOList(discountReqDTO.getDishInfoDTOList());
//					return discountRespDTO;
//				}
//			};
//		}
//	}
//}
