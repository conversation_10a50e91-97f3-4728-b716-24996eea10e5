package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单商品
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsw_weixin_order_item")
public class WxOrderItemDO implements Serializable {

	/**
	 * 全局唯一主键
	 */
	@TableId
	private String guid;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime gmtCreate;

	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime gmtModified;

	/**
	 * 是否删除 0：false,1:true
	 */
	@TableLogic
	private Boolean isDel;

	/**
	 * 用户微信公众号openId
	 */
	private String openId;

	/**
	 * 订单guid
	 */
	private String orderGuid;

	/**
	 * 批次guid
	 */
	private String merchantGuid;

		/**
	 * 原始订单guid
	 */
	private String orderRecordGuid;
	
	/**
	 * 商品guid
	 */
	private String itemGuid;

	/**
	 * 商品名称
	 */
	private String itemName;

	/**
	 * 商品编号
	 */
	private String code;

	/**
	 * 商品图片
	 */
	private String pictureUrl;

	/**
	 * 商品类别guid
	 */
	private String itemTypeGuid;

	/**
	 * 商品类别名称
	 */
	private String itemTypeName;

	/**
	 * 商品类型(1.套餐主项，2.规格，3.称重，4.单品 )
	 */
	private Integer itemType;

	/**
	 * 套餐分组
	 */
	private String subgroup;
	/**
	 * 商品的规格
	 */
	private String skuGuid;

	/**
	 * 规格名称
	 */
	private String skuName;

	/**
	 * sku价格
	 */
	private BigDecimal skuPrice;

	private BigDecimal skuMemberPrice;

	/**
	 * 计数单位
	 */
	private String skuUnit;

	/**
	 * 会员价格
	 */
	private BigDecimal memberPrice;
	/**
	 * 商品原价
	 */
	private BigDecimal originalPrice;

	/**
	 * 属性总价
	 */
	private BigDecimal attrTotal;

	/**
	 * 是否有属性（0：否，1：是）
	 */
	private Integer hasAttr;


	/**
	 * 当前数量（不包括赠送，不要重复减赠送折扣）
	 */
	private BigDecimal currentCount;


	/**
	 * 是否参与整单折扣(0：否，1：是)
	 */
	private Integer isWholeDiscount;

	/**
	 * 是否参与会员折扣（0：否，1：是）
	 */
	private Integer isMemberDiscount;

	/**
	 * 商品备注
	 */
	private String remark;


	/**
	 * 创建操作人guid
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createStaffGuid;

	/**
	 * 创建操作人guid
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createStaffName;


	/**
	 * 属性组的json数组
	 */
	private String itemAttr;

	@ApiModelProperty(value = "起卖数")
	private BigDecimal minOrderNum;

	/**
	 * 团购验券信息
	 */
	private String couponInfo;

}
