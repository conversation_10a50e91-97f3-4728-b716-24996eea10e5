package com.holderzone.saas.store.weixin.aop;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.saas.store.weixin.annotation.DynamicData;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DynamicDataAspect
 * @date 2019/8/12
 */
@Aspect
@Component
public class DynamicDataAspect {

	private final LocalVariableTableParameterNameDiscoverer localVariableTableParameterNameDiscoverer;

	private final SpelExpressionParser spelExpressionParser;

	private final StandardEvaluationContext standardEvaluationContext;

	@Autowired
	public DynamicDataAspect(LocalVariableTableParameterNameDiscoverer localVariableTableParameterNameDiscoverer, SpelExpressionParser spelExpressionParser, StandardEvaluationContext standardEvaluationContext) {
		this.localVariableTableParameterNameDiscoverer = localVariableTableParameterNameDiscoverer;
		this.spelExpressionParser = spelExpressionParser;
		this.standardEvaluationContext = standardEvaluationContext;
	}

	@Before("@annotation(dynamicData)")
	public void before(JoinPoint joinPoint, DynamicData dynamicData){
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();
		String[] parameterNames = localVariableTableParameterNameDiscoverer.getParameterNames(method);
		Object[] args = joinPoint.getArgs();
		if (!ObjectUtils.isEmpty(parameterNames) && !ObjectUtils.isEmpty(args)) {
			for(int i=0;i<parameterNames.length;i++) {
				standardEvaluationContext.setVariable(parameterNames[i],args[i]);
			}
			UserContext userContext = UserContextUtils.get();
			if(ObjectUtils.isEmpty(userContext)){
				userContext = new UserContext();
			}
			userContext.setAllianceId("********************************");
			userContext.setEnterpriseGuid(spelExpressionParser.parseExpression(dynamicData.enterpriseGuid()).getValue(standardEvaluationContext, String.class));
			userContext.setStoreGuid(spelExpressionParser.parseExpression(dynamicData.storeGuid()).getValue(standardEvaluationContext,String.class));
			UserContextUtils.put(userContext);
			EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
		}
	}
}
