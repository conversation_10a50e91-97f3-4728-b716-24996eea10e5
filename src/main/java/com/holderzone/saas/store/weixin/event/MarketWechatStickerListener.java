package com.holderzone.saas.store.weixin.event;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.dto.weixin.WxStickOrderCallBackDTO;
import com.holderzone.saas.store.weixin.constant.RocketMqConfig;
import com.holderzone.saas.store.weixin.service.WxStickModelOrderService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RocketListenerHandler(
		topic = RocketMqConfig.MARKET_WECHAT_STICKER_TOPIC,
		tags = RocketMqConfig.MARKET_WECHAT_STICKER_TAG,
		consumerGroup = RocketMqConfig.MARKET_WECHAT_STICKER_CONSUME_GROUP
)
public class MarketWechatStickerListener  extends AbstractRocketMqConsumer<RocketMqTopic, WxStickOrderCallBackDTO> {


	@Autowired
	private WxStickModelOrderService wxStickModelOrderService;

	@Autowired
	private DynamicHelper dynamicHelper;

	@Override
	public boolean consumeMsg(WxStickOrderCallBackDTO wxStickOrderCallBackDTO, MessageExt messageExt) {
		log.info("购物车回调清空入参:{}",wxStickOrderCallBackDTO);
		dynamicHelper.changeDatasource(wxStickOrderCallBackDTO.getEnterpriseGuid());
		String s = wxStickModelOrderService.callBack(wxStickOrderCallBackDTO);
		return "SUCCESS".equals(s);
	}


}
