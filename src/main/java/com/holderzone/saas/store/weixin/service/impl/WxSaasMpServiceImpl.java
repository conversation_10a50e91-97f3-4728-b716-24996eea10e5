package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.weixin.config.WxThirdOpenConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.service.WxSaasMpService;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.service.WxStoreComponentConfigService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxSaasMpServiceImpl
 * @date 2019/03/22 17:31
 * @description 微信公众号服务实现类
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxSaasMpServiceImpl implements WxSaasMpService {
    @Autowired
    WxThirdOpenConfig wxThirdOpenConfig;

    @Autowired
    WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;

    @Autowired
    WxStoreComponentConfigService wxStoreComponentConfigService;

    @Override
    public WxMpService getWxMpService(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) throws WxErrorException{
        wxStoreComponentConfigService.getAccessToken();
        getAuthorizerAccessToken(wxStoreAuthorizerInfoDO);
        return wxThirdOpenConfig.getWxOpenComponentService().getWxMpServiceByAppid(wxStoreAuthorizerInfoDO.getAuthorizerAppid());
    }

    /**
     * 获取当前公众号的accessToken，若没有或已过期，则重新从微信获取
     *
     * @param wxStoreAuthorizerInfoDO
     * @return
     */

    private String getAuthorizerAccessToken(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) throws WxErrorException {
        if (Objects.isNull(wxStoreAuthorizerInfoDO)) {
            return null;
        }
        String appId = wxStoreAuthorizerInfoDO.getAuthorizerAppid();
        if (ObjectUtils.isEmpty(appId)) {
            throw new BusinessException("公众号appId为空，请确认后重试");
        }
        String refreshToken = wxThirdOpenConfig.getWxOpenConfigStorage().getAuthorizerRefreshToken(appId);
        if (ObjectUtils.isEmpty(refreshToken)) {
            log.info("获取refreshToken为空:{}", appId);
            if (ObjectUtils.isEmpty(wxStoreAuthorizerInfoDO.getAuthorizerRefreshToken())) {
                throw new BusinessException("公众号信息有误，请确认后重试");
            }
            refreshToken = wxStoreAuthorizerInfoDO.getAuthorizerRefreshToken();
            wxThirdOpenConfig.getWxOpenConfigStorage().setAuthorizerRefreshToken(appId, refreshToken);
        }
        String accessToken = wxStoreAuthorizerInfoDO.getAuthorizerAccessToken();
        Long expiresIn = wxStoreAuthorizerInfoDO.getExpiresIn();
        if (ObjectUtils.isEmpty(accessToken) || System.currentTimeMillis() > expiresIn) {
            wxThirdOpenConfig.getWxOpenComponentService().getAuthorizerAccessToken(appId, true);
            accessToken = wxThirdOpenConfig.getWxOpenConfigStorage().getAuthorizerAccessToken(appId);
            wxStoreAuthorizerInfoDO.setAuthorizerAccessToken(accessToken);
            wxStoreAuthorizerInfoService.updateWxAuthorizeInfo(wxStoreAuthorizerInfoDO);
        }
        return accessToken;
    }
}
