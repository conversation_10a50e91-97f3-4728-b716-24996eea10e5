package com.holderzone.saas.store.weixin.service;


import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoDTO;
import com.holderzone.saas.store.dto.weixin.deal.ShopCartItemReqDTO;

import java.util.List;

/**
 * 团购验券
 */
public interface GrouponService {

    void grouponDoCheck(String orderGuid, String orderRecordGuid, List<ShopCartItemReqDTO> itemList);

    void revokeGroupon(List<ItemInfoDTO> itemInfoList);

    void revokeVerifyGroupon(List<GroupVerifyDTO> groupVerifyList);

    void preCheckParamVerify(String orderGuid, List<MtCouponPreRespDTO> mtCouponPreRespDTOByItemInfo);

}
