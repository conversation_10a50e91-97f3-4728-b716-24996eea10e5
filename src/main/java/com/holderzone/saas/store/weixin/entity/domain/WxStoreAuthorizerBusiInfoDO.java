package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreAuthorizerBusiInfoDO
 * @date 2019/02/28 15:12
 * @description 微信授权方功能开通情况DO
 * @program holder-saas-store-weixin
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("hsw_weixin_auth_business_info")
public class WxStoreAuthorizerBusiInfoDO {

    private Long id;

    @TableId("guid")
    private String guid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    private String authorizerAppId;

    private byte isOpenStore;

    private byte isOpenScan;

    private byte isOpenPay;

    private byte isOpenCard;

    private byte isOpenShake;

    @TableLogic
    private Integer isDeleted;

}
