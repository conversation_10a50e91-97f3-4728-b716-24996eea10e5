package com.holderzone.saas.store.weixin.constant;

import com.holderzone.saas.store.weixin.utils.SnowFlakeUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GuestEnum {

    GUEST("游客"),

    ;

    /**
     * 描述
     */
    private final String des;


    public static String getOpenId() {
        return String.valueOf(SnowFlakeUtil.getInstance().nextId());
    }

}
