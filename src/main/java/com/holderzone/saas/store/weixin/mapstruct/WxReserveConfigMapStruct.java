package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.req.WxReserveConfigDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxReserveConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxReserveConfigMapStruct
 * @date 2019/12/17 11:52
 * @description
 * @program holder-saas-store
 */
@Mapper
public interface WxReserveConfigMapStruct {

    WxReserveConfigMapStruct INSTANCE = Mappers.getMapper(WxReserveConfigMapStruct.class);

    WxReserveConfigDTO do2DTO(WxReserveConfigDO wxReserveConfigDO);

    WxReserveConfigDO dto2DO(WxReserveConfigDTO wxReserveConfigDTO);

	List<WxReserveConfigDTO> toList(List<WxReserveConfigDO> wxReserveConfigDOS);
}
