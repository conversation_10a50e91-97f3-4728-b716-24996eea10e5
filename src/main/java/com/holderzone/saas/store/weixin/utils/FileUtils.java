package com.holderzone.saas.store.weixin.utils;

import com.holderzone.framework.util.StringUtils;

import java.io.File;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileUtils
 * @date 2019/03/14 15:33
 * @description 文件工具类
 * @program holder-saas-store
 */
public class FileUtils {
    /**
     * user: Rex
     * date: 2016年12月29日  上午12:25:04
     *
     * @param dir void
     *            TODO 判断路径是否存在，如果不存在则创建
     */
    public static void mkdirs(String dir) {
        if (StringUtils.isEmpty(dir)) {
            return;
        }

        File file = new File(dir);
        if (file.isDirectory()) {
            return;
        } else {
            file.mkdirs();
        }
    }

//    public static void deleteDirectory(String filePath) {
//        File file = new File(filePath);
//        if (!file.exists() || !file.isDirectory())
//            return;
//        File[] files = file.listFiles();
//        for (int i = 0; i < files.length; i++) {
//            //删除子文件
//            if (files[i].isFile()) {
//                deleteFile(files[i].getAbsolutePath());
//            } //删除子目录
//            else {
//                deleteDirectory(files[i].getAbsolutePath());
//            }
//        }
//        file.delete();
//    }

//    public static void deleteFile(String filePath) {
//        File file = new File(filePath);
//        // 路径为文件且不为空则进行删除
//        if (file.isFile() && file.exists()) {
//            file.delete();
//        }
//    }
}
