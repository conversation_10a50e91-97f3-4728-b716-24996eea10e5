package com.holderzone.saas.store.weixin.event.delay;

import com.holderzone.saas.store.dto.weixin.deal.ConfirmConfigTaskDTO;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 确认提示监听
 * @date 2021/4/14
 */
@Component
@Slf4j
public class ConfirmPromptListener implements  RedisDelayedQueueListener<ConfirmConfigTaskDTO>{

    @Autowired
    OrderItemService orderItemService;

    @Override
    public void invoke(ConfirmConfigTaskDTO taskDTO) {
        log.info("处理延时队列确认提示数据：{}",taskDTO);
        orderItemService.dealRedisDelayedTask(taskDTO);
    }
}
