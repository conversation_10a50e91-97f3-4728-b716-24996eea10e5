package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.table.OpenTableDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDineInOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreShoppingCartDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 购物车转开台
 * @className WxStoreOpenTableMapstruct
 * @date 2019/3/7
 */
@Mapper(componentModel = "spring")
@Component
public interface WxStoreOpenTableMapstruct {



	@Mappings({
			@Mapping(target = "tableGuid",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.diningTableGuid"),
			@Mapping(target = "tableCode",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.tableCode"),
			@Mapping(target = "areaName",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.areaName"),
			@Mapping(target = "actualGuestsNo",source = "userCount"),
			@Mapping(target = "deviceType",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.deviceType"),
			@Mapping(target = "deviceId",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.openId"),
			@Mapping(target = "enterpriseGuid",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.enterpriseGuid"),
			@Mapping(target = "storeGuid",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid"),

	})
	OpenTableDTO getOpenTable(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

}
