package com.holderzone.saas.store.weixin.config;

import com.holderzone.feign.spring.boot.exception.ExceptionHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.error.WxMpErrorMsgEnum;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2019/03/22 18:29
 * @description 统一异常拦截
 * @program holder-saas-store
 */
@SuppressWarnings("Duplicates")
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler extends ExceptionHandlerAdapter {

	@ExceptionHandler(value = WxErrorException.class)
	public ResponseEntity<String> exception(WxErrorException e) {
		int errorCode = e.getError().getErrorCode();
		String message = WxMpErrorMsgEnum.findMsgByCode(errorCode);
		if (log.isErrorEnabled()) {
			log.error("调用微信API时出现异常：{}", e.getMessage());
		}
		return new ResponseEntity<>(message, HttpStatus.CONFLICT);
	}
}
