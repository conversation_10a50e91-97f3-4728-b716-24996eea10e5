package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxOrderConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreConfigController
 * @date 2019/02/14 11:05
 * @description 微信点餐门店配置信息Controller
 * @program holder-saas-store-weixin
 */
@RestController
@RequestMapping("/wx_store_order_config")
@Slf4j
@Api(description = "微信点餐门店配置Controller")
public class WxStoreOrderConfigController {

    @Autowired
    WxStoreOrderConfigService wxStoreOrderConfigService;


    @PostMapping("/list_order_config")
    @ApiOperation(value = "获取微信点餐门店配置信息列表")
    @ApiImplicitParam(name = "微信点餐门店配置列表请求参数", value = "wxStorePageReqDTO")
    public Page<WxOrderConfigDTO> getOrderConfigList(@RequestBody WxStorePageReqDTO wxStorePageReqDTO) {
        log.info("获取微信门店配置列表详情请求参数：\nstoreGuid:{}, \n storeName:{}, \n currentPage:{}, pageSize:{}",
                wxStorePageReqDTO.getStoreGuid(), wxStorePageReqDTO.getStoreName(),
                wxStorePageReqDTO.getCurrentPage(), wxStorePageReqDTO.getPageSize());
        return wxStoreOrderConfigService.pageOrderConfig(wxStorePageReqDTO);
    }

    /**
     * 获取门店配置详细信息
     * 包含微信点餐及PAD端用户点餐买单设置
     *
     * @param wxStoreReqDTO
     * @return
     */
    @PostMapping("/get_detail_config")
    @ApiOperation(value = "获取微信点餐门店配置详细信息")
    @ApiImplicitParam(name = "微信点餐门店配置详细信息请求参数", value = "wxStoreReqDTO")
    public WxOrderConfigDTO getDetailConfig(@RequestBody WxStoreReqDTO wxStoreReqDTO) {
        log.info("获取微信点餐门店配置详细信息请求参数：{}", wxStoreReqDTO.toString());
        return wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
    }

    @PostMapping("/create_store_config")
    @Deprecated
    @ApiOperation(value = "初始化微信点餐门店配置信息")
    public boolean createStoreConfig(@RequestBody WxStoreReqDTO wxStoreReqDTO) {
        log.info("微信点餐门店配置信息初始化请求参数：{}", wxStoreReqDTO.toString());
        return wxStoreOrderConfigService.saveStoreConfig(wxStoreReqDTO);
    }

    @PostMapping("/update_store_config")
    @ApiOperation(value = "修改微信点餐门店配置")
    @ApiImplicitParam(name = "微信门店配置修改参数", value = "wxStoreUpdateReqDTO")
    public Boolean updateStoreConfig(@RequestBody WxOrderConfigDTO wxOrderConfigDTO) {

        return wxStoreOrderConfigService.updateStoreConfig(wxOrderConfigDTO);
    }

    @PostMapping("/get_pad_background_url")
    @ApiOperation(value = "获取pad点餐背景图")
    public String getPadBackgroundUrl(@RequestParam("storeGuid") String storeGuid) {
        log.info("获取pad点餐背景图请求参数：{}", storeGuid);
        return wxStoreOrderConfigService.getPadBackgroundUrl(storeGuid);
    }

    @PostMapping("/update_batch_store_config")
    public Boolean updateStoreConfigBatch(@RequestBody WxOrderConfigUpdateBatchReqDTO wxOrderConfigUpdateBatchReqDTO) {
        return wxStoreOrderConfigService.updateBatchStoreConfig(wxOrderConfigUpdateBatchReqDTO);
    }

    @ApiOperation("查询门店配置重构")
    @GetMapping("/store_config")
    public WxOrderConfigDTO getStoreConfig(@RequestParam("storeGuid") String storeGuid) {
        log.info("查询门店配置入参:{}", storeGuid);
//		EnterpriseIdentifier.setEnterpriseGuid("6506431195651982337");
//		UserContext userContext = new UserContext();
//		userContext.setEnterpriseGuid("6506431195651982337");
//		UserContextUtils.put(userContext);
        return wxStoreOrderConfigService.getStoreConfig(storeGuid);
    }

}
