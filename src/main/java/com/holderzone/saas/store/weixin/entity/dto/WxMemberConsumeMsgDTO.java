package com.holderzone.saas.store.weixin.entity.dto;

import com.holderzone.saas.store.weixin.entity.enums.WxTemplateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMemberConsumeMsgDTO
 * @date 2019/08/22 17:32
 * @description 微信会员结账消息模板通知参数
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxMemberConsumeMsgDTO{

    private String openId;

    private String brandGuid;

    private String storeGuid;

    private String orderGuid;

    private String first;

    private String remark;

    private String url;

    private WxTemplateTypeEnum wxTemplateTypeEnum;



    private String[] args;

    private List<String> keywordValues = new ArrayList<>();

    public void addKeywordValue(String keywordValue){
        if(keywordValues==null){
            keywordValues = new ArrayList<>();
        }
        keywordValues.add(keywordValue);
    }

}
