package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.req.WxReserveConfigDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxReserveConfigDO;
import com.holderzone.saas.store.weixin.mapper.WxReserveConfigMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxReserveConfigMapStruct;
import com.holderzone.saas.store.weixin.service.WxOrganizationService;
import com.holderzone.saas.store.weixin.service.WxReserveConfigService;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxReserveConfigServiceImpl
 * @date 2019/12/17 10:12
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Service
public class WxReserveConfigServiceImpl extends ServiceImpl<WxReserveConfigMapper, WxReserveConfigDO> implements WxReserveConfigService {

	private static final BatchIdGenerator ID_GENERATOR = new BatchIdGenerator();
	private final WxOrganizationService wxOrganizationService;
	private final RedisTemplate redisTemplate;

	@Autowired
	public WxReserveConfigServiceImpl(WxOrganizationService wxOrganizationService, RedisTemplate redisTemplate) {
		this.wxOrganizationService = wxOrganizationService;
		this.redisTemplate = redisTemplate;
	}

	@Override
	public Page<WxReserveConfigDTO> listConfig(WxStorePageReqDTO wxStorePageReqDTO) {
		Page<StoreDTO> storePage = wxOrganizationService.getStorePage(wxStorePageReqDTO);
		if (storePage == null || CollectionUtils.isEmpty(storePage.getData())) {
			return new Page<>(wxStorePageReqDTO.getCurrentPage(), wxStorePageReqDTO.getPageSize(), 0);
		}
		List<StoreDTO> storeDTOList = storePage.getData();
		List<String> storeGuidList = storeDTOList.stream()
												.map(StoreDTO::getGuid)
												.collect(Collectors.toList());
		List<WxReserveConfigDO> wxReserveConfigDOS = initWxReserveConfig(storeGuidList);
		Map<String, WxReserveConfigDO> doMaps = wxReserveConfigDOS.stream()
																	.collect(Collectors.toMap(WxReserveConfigDO::getStoreGuid, Function.identity()));
		List<WxReserveConfigDTO> respList = storeDTOList.stream()
														.map(s -> {
															WxReserveConfigDTO dto = WxReserveConfigMapStruct.INSTANCE.do2DTO(doMaps.get(s.getGuid()));
															dto.setStoreGuid(s.getGuid());
															dto.setStoreName(s.getName());
															return dto;
														})
														.collect(Collectors.toList());
		return new Page<>(storePage.getCurrentPage(), storePage.getPageSize(), storePage.getTotalCount(), respList);
	}

	@Override
	public Boolean updateConfig(WxReserveConfigDTO wxReserveConfigDTO) {
		if (ObjectUtils.isEmpty(wxReserveConfigDTO)) {
			return false;
		}
		WxReserveConfigDO wxReserveConfigDO = WxReserveConfigMapStruct.INSTANCE.dto2DO(wxReserveConfigDTO);
		if (StringUtils.isEmpty(wxReserveConfigDO.getGuid())) {
			try {
				wxReserveConfigDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "wx_reserve_config")));
			} catch (IOException e) {
				log.error("Error generating GUID for WxReserveConfig: ", e);
				throw new BusinessException("System is busy, please try again later");
			}
		}
		return this.saveOrUpdate(wxReserveConfigDO);
	}

	@Override
	public WxReserveConfigDTO getConfig(String guid) {
		return WxReserveConfigMapStruct.INSTANCE.do2DTO(this.getById(guid));
	}

	@Override
	public List<WxReserveConfigDO> initWxReserveConfig(List<String> storeGuidList) {
		List<WxReserveConfigDO> configDOS = this.list(new LambdaQueryWrapper<WxReserveConfigDO>()
				.in(WxReserveConfigDO::getStoreGuid, storeGuidList));
		Map<String, WxReserveConfigDO> doMaps = configDOS.stream()
														.collect(Collectors.toMap(WxReserveConfigDO::getStoreGuid, Function.identity()));
		List<String> needInitStoreGuidList = storeGuidList.stream()
														.filter(s -> !doMaps.containsKey(s))
														.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(needInitStoreGuidList)) {
			return configDOS;
		}
		List<Long> guidList = BatchIdGenerator.batchGetGuids(redisTemplate, "wx_reserve_config", needInitStoreGuidList.size());
		List<WxReserveConfigDO> wxReserveConfigDOS = needInitStoreGuidList.stream()
																		.map(WxReserveConfigDO::DEFAULT_WITH_OUT_GUID)
																		.peek(s -> s.setGuid(String.valueOf(guidList.remove(guidList.size() - 1))))
																		.collect(Collectors.toList());
		this.saveBatch(wxReserveConfigDOS);
		configDOS.addAll(wxReserveConfigDOS);
		return configDOS;
	}

	@Override
	public WxReserveConfigDTO store(String storeGuid) {
		if (StringUtils.isEmpty(storeGuid)) {
			return null;
		}
		return WxReserveConfigMapStruct.INSTANCE.do2DTO(getOne(new LambdaQueryWrapper<WxReserveConfigDO>()
		.eq(WxReserveConfigDO::getStoreGuid, storeGuid),false));
	}

	@Override
	public List<WxReserveConfigDTO> storeList(List<String> storeGuidList) {
		if(CollectionUtils.isEmpty(storeGuidList)){
			return Collections.emptyList();
		}
		return WxReserveConfigMapStruct.INSTANCE.toList(lambdaQuery().in(WxReserveConfigDO::getStoreGuid,storeGuidList).list());
	}
}
