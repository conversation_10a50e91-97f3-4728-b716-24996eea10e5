package com.holderzone.saas.store.weixin.manager;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseAccountStatus;
import com.holderzone.holder.saas.weixin.entry.dto.*;
import com.holderzone.holder.saas.weixin.entry.dto.req.WxOrderDetailReqDTO;
import com.holderzone.holder.saas.weixin.utils.*;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import com.holderzone.saas.store.dto.weixin.resp.ItemImgDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.constant.UpperStateEnum;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderItemDO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.helper.OrderDetailHelper;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import com.holderzone.saas.store.weixin.service.WxStoreMerchantOrderService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.WxUserRecordService;
import com.holderzone.saas.store.weixin.service.deal.BusinessClientService;
import com.holderzone.saas.store.weixin.service.deal.ItemClientService;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import com.holderzone.saas.store.weixin.service.deal.TableClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.utils.OrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WxOrderManager {


    @Resource
    WxStoreTableClientService wxStoreTableClientService;


//	@Resource
//	MemberOrderClientService memberOrderClientService;

    @Resource
    WxOrderRecordService wxOrderRecordService;
    @Resource
    OrderItemService orderItemService;

    @Resource
    WxStoreMerchantOrderService wxStoreMerchantOrderService;

    @Resource
    WxStoreDineInOrderClientService wxStoreDineInOrderClientService;
    @Resource
    ItemClientService itemClientService;

    @Resource
    WxStoreSessionDetailsService wxStoreSessionDetailsService;
    @Resource
    RedisUtils redisUtils;

    @Resource
    WxUserRecordService wxUserRecordService;
    @Resource
    TableClientService tableClientService;

//	@Resource
//	private WeChatClientService weChatClientService;

    @Resource
    private HsaBaseClientService hsaBaseClientService;

    @Resource
    private BusinessClientService businessClientService;

    private WxOrderRecordDO queryOrderRecord(String guid) {
        if (StringUtils.isEmpty(guid)) {
            return null;
        }
        //查询微信本地订单记录数据
        WxOrderRecordDO orderRecord = wxOrderRecordService.getById(guid);
        if (orderRecord == null) {
            // 再次根据orderHolderNo查询
            orderRecord = wxOrderRecordService.getByOrderHolderNo(guid);
        }
        return orderRecord;
    }

    public OrderDetailDTO detail(WxOrderDetailReqDTO req) {
        String guid = req.getGuid();
        //查询微信本地订单记录数据
        WxOrderRecordDO orderRecord = queryOrderRecord(guid);
        if (orderRecord == null) {
            return null;
        }
        //快餐自动取消
        if (orderRecord.getOrderMode().equals(1) && orderRecord.getOrderState().equals(5)) {
            LocalDateTime gmtCreate = orderRecord.getGmtCreate();
            LocalDateTime now = LocalDateTime.now();
            Duration dur = java.time.Duration.between(gmtCreate, now);
            long diff = dur.toMillis();
            if (diff >= 1000 * 60 * 15) {
                orderRecord.setOrderState(3);
                wxOrderRecordService.dealFastOrderTimeOut(guid);
            }
        }
        TableDTO tableDetail = wxStoreSessionDetailsService.getTableDetail(orderRecord.getTableGuid());
        StoreDTO storeDetail = wxStoreSessionDetailsService.getStoreDetail(tableDetail.getStoreGuid());
        tableDetail.setStoreName(storeDetail.getName());
        // 查询订单详情
        DineinOrderDetailRespDTO orderDetailResp = getTradeOrderDetail(orderRecord.getOrderGuid());
        log.info("查询订单详情返回:{}", orderDetailResp);
        OrderDetailDTO orderDetailDTO = buildOrderDetailDTO(req, orderRecord, tableDetail, orderDetailResp);
        // 如果是多单结账 则构建多单的订单信息
        if (Objects.nonNull(orderDetailResp) && CollectionUtils.isNotEmpty(orderDetailResp.getOtherOrderDetails())) {
            List<OrderDetailDTO> innerOrderDetailDTOList = Lists.newArrayList();
            for (DineinOrderDetailRespDTO otherOrderDetail : orderDetailResp.getOtherOrderDetails()) {
                innerOrderDetailDTOList.add(buildOrderDetailDTO(req, orderRecord, tableDetail, otherOrderDetail));
            }
            log.info("多单结账信息:{}", JacksonUtils.writeValueAsString(innerOrderDetailDTOList));
            orderDetailDTO.setOtherOrderDetails(innerOrderDetailDTOList);
            // 多单结账信息处理
            mergeOtherOrderDetails(orderDetailDTO);
        }
        return orderDetailDTO;
    }


    /**
     * 合并多单结账金额
     */
    private void mergeOtherOrderDetails(OrderDetailDTO orderDetailDTO) {
        // 主单追加到集合
        appendMainOrderToOtherOrderDetails(orderDetailDTO);
        List<OrderDetailDTO> otherOrderDetails = orderDetailDTO.getOtherOrderDetails();
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            return;
        }
        // 删除重复的本地批次商品
        removeBatchCodeItem(orderDetailDTO);
        // 倒序
        Collections.reverse(otherOrderDetails);
        // 非并台
        orderDetailDTO.setCombine(0);
        // 替换订单状态
        replaceOtherOrderState(orderDetailDTO);
        // 计算订单金额
        calculateOtherOrderPrice(orderDetailDTO);
        // 合并支付方式
        mergeSameOrderPayWay(orderDetailDTO);
    }

    private void removeBatchCodeItem(OrderDetailDTO orderDetailDTO) {
        List<OrderDetailDTO> otherOrderDetails = orderDetailDTO.getOtherOrderDetails();
        otherOrderDetails = otherOrderDetails.stream().filter(e -> CollectionUtils.isNotEmpty(e.getTableOrderDetailDTO()))
                .collect(Collectors.toList());
        Set<String> allBatchCode = Sets.newHashSet();
        for (OrderDetailDTO otherOrderDetail : otherOrderDetails) {
            List<OrderTableItemDTO> tableOrderDetailDTO = otherOrderDetail.getTableOrderDetailDTO();
            for (OrderTableItemDTO orderTableItemDTO : tableOrderDetailDTO) {
                if (CollectionUtils.isEmpty(orderTableItemDTO.getOrderBatchDTOs())) {
                    continue;
                }
                List<OrderBatchItemDTO> orderBatchDTOs = orderTableItemDTO.getOrderBatchDTOs();
                Iterator<OrderBatchItemDTO> iterator = orderBatchDTOs.iterator();
                while (iterator.hasNext()) {
                    OrderBatchItemDTO orderBatchDTO = iterator.next();
                    String batchCode = orderBatchDTO.getBatchCode();
                    if (!allBatchCode.contains(batchCode) || StringUtils.isEmpty(batchCode) || "t".equals(batchCode) || "-999".equals(batchCode)) {
                        allBatchCode.add(batchCode);
                        continue;
                    }
                    iterator.remove();
                }
            }
        }
    }

    private void mergeSameOrderPayWay(OrderDetailDTO orderDetailDTO) {
        List<OrderDetailDTO> otherOrderDetails = orderDetailDTO.getOtherOrderDetails();
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            return;
        }
        List<String> payWayList = Lists.newArrayList();
        for (OrderDetailDTO otherOrderDetail : otherOrderDetails) {
            String payWay = otherOrderDetail.getPayWay();
            if (StringUtils.isEmpty(payWay)) {
                continue;
            }
            String[] splitPayWay = payWay.split("、");
            payWayList.addAll(Arrays.asList(splitPayWay));
        }
        payWayList = payWayList.stream().distinct().collect(Collectors.toList());
        orderDetailDTO.setPayWay(org.apache.commons.lang3.StringUtils.join(payWayList, "、"));
    }


    private void appendMainOrderToOtherOrderDetails(OrderDetailDTO orderDetailDTO) {
        OrderDetailDTO mainOrder = JacksonUtils.toObject(OrderDetailDTO.class,
                JacksonUtils.writeValueAsString(orderDetailDTO));
        mainOrder.setOtherOrderDetails(null);
        if (CollectionUtils.isEmpty(orderDetailDTO.getOtherOrderDetails())) {
            orderDetailDTO.setOtherOrderDetails(Lists.newArrayList());
        }
        orderDetailDTO.getOtherOrderDetails().add(0, mainOrder);
    }

    private void replaceOtherOrderState(OrderDetailDTO orderDetailDTO) {
        List<OrderDetailDTO> otherOrderDetails = orderDetailDTO.getOtherOrderDetails();
        for (OrderDetailDTO otherOrderDetail : otherOrderDetails) {
            Integer tradeState = otherOrderDetail.getTradeState();
            if (tradeState == 2 || tradeState == 12) {
                // 已支付
                otherOrderDetail.setOrderState(2);
                List<OrderBatchItemDTO> orderBatchItemList = otherOrderDetail.getTableOrderDetailDTO().stream()
                        .filter(e -> CollectionUtils.isNotEmpty(e.getOrderBatchDTOs()))
                        .flatMap(e -> e.getOrderBatchDTOs().stream())
                        .collect(Collectors.toList());
                for (OrderBatchItemDTO orderBatchItemDTO : orderBatchItemList) {
                    if (orderBatchItemDTO.getState() == 1) {
                        // 更新状态
                        orderBatchItemDTO.setState(2);
                    }
                }
            }
        }
    }

    /**
     * 多单结账 计算金额
     */
    private void calculateOtherOrderPrice(OrderDetailDTO orderDetailDTO) {
        List<OrderDetailDTO> otherOrderDetails = orderDetailDTO.getOtherOrderDetails();
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            return;
        }
        // 订单已付金额重新计算
        BigDecimal totalPayAmount = otherOrderDetails.stream()
                .map(OrderDetailDTO::getPayAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetailDTO.setPayAmount(totalPayAmount);
        // 计算totalPrice
        List<OrderDetailDTO> unPaySuccessOrderList = otherOrderDetails.stream()
                .filter(e -> Objects.equals(1, e.getTradeState()))
                .collect(Collectors.toList());
        BigDecimal unPayTotalPrice = unPaySuccessOrderList.stream()
                .map(OrderDetailDTO::getTotlePrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unPaySaleTotalPrice = unPaySuccessOrderList.stream()
                .map(OrderDetailDTO::getSaleTotlePrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetailDTO.setTotlePrice(totalPayAmount.add(unPayTotalPrice));
        orderDetailDTO.setSaleTotlePrice(totalPayAmount.add(unPaySaleTotalPrice));
    }

    /**
     * 构建返回数据结构
     */
    private OrderDetailDTO buildOrderDetailDTO(WxOrderDetailReqDTO req, WxOrderRecordDO orderRecord,
                                               TableDTO tableDetail, DineinOrderDetailRespDTO orderDetailResp) {
        OrderDetailHelper detailBuilder = new OrderDetailHelper(orderRecord, req.getEnableMemberPrice(), req.getMemberInfoCardGuid());
        detailBuilder.setTableDTO(tableDetail);
        detailBuilder.getOrderDetailDTO().setDiningTableCode(tableDetail.getCode());
        if (WeixinUserThreadLocal.get() != null) {
            detailBuilder.getOrderDetailDTO().setLoginStatus(WeixinUserThreadLocal.getIsLogin());
            detailBuilder.setBrandName(WeixinUserThreadLocal.get().getBrandName());
            detailBuilder.getOrderDetailDTO().setAreaGuid(WeixinUserThreadLocal.get().getAreaGuid());
            detailBuilder.getOrderDetailDTO().setAreaName(WeixinUserThreadLocal.get().getAreaName());
            //detailBuilder.
        }
        String areaGuid = detailBuilder.getOrderDetailDTO().getAreaGuid();
        if (StringUtils.isEmpty(areaGuid)) {
            detailBuilder.getOrderDetailDTO().setAreaName(tableDetail.getAreaName());
            detailBuilder.getOrderDetailDTO().setBrandName(orderRecord.getBrandName());
        }
        Integer orderState = orderRecord.getOrderState();

        if (orderRecord.getOrderMode().equals(1)) {//(0：正餐，1：快餐)
            WxUserRecordDO wxUserRecordDO = new WxUserRecordDO();
            wxUserRecordDO.setNickName(WeixinUserThreadLocal.getNickName());
            wxUserRecordDO.setHeadImgUrl(WeixinUserThreadLocal.get().getWxUserInfoDTO().getHeadImgUrl());
            detailBuilder.setWxUserRecordDO(wxUserRecordDO);
            detailBuilder.setRealTable(false);
        } else if (orderState.equals(0) || orderState.equals(1)) {
            initAreaMap(orderRecord, detailBuilder);
        }
        initTradeInfo(orderRecord, detailBuilder, orderDetailResp);
        Integer staticOrderState = detailBuilder.getOrderDetailDTO().getOrderState();
        if (detailBuilder.getOrderDetailTrade() != null) {
            staticOrderState = OrderUtils.tradeStateTransition(detailBuilder.getOrderDetailTrade().getState());
        }
        staticOrderState = staticOrderState == null ? OrderUtils.orderStateTransition(orderState) : staticOrderState;
        detailBuilder.getOrderDetailDTO().setOrderState(staticOrderState);
        if (orderRecord.getOrderMode().equals(1)) {
            // 快餐
            detailBuilder.structTrade();
            detailBuilder.getOrderDetailDTO().setMemberCardGuid(detailBuilder.getOrderDetailTrade().getMemberCardGuid());
            initOrderItemImg(detailBuilder);
            detailBuilder.hanldTables();
            return detailBuilder.buiderMemberDishinfo();
        }

        //已经完结的订单
        if (staticOrderState.equals(2)) {
            String userRecordGuid = orderRecord.getUserRecordGuid();
            if (!StringUtils.isEmpty(userRecordGuid)) {
                WxUserRecordDO wxUserRecordDO = wxUserRecordService.getById(userRecordGuid);
                detailBuilder.setWxUserRecordDO(wxUserRecordDO);
            }
            //orderState = 2;
            // 2已支付，微信用户自己支付；6已完成,一体机结账
            detailBuilder.setRealTable(false);
            //detailBuilder.setEnableMemberPrice(detailBuilder.getOrderDetailTrade().getSingleItemUsedMemeberPrice());
            //并台数据处理
            detailBuilder.structTrade();
            detailBuilder.hanldTables();
            initOrderItemImg(detailBuilder);
            handleMergeTabelEx(orderRecord, detailBuilder);
            //loginStatus
            return detailBuilder.buiderMemberDishinfo();
        } else detailBuilder.setRealTable(!staticOrderState.equals(3));
        handleMergeTabelEx(orderRecord, detailBuilder);
        updateCurrentSession(tableDetail, orderRecord.getOrderMode());
        detailBuilder.setLocalTableDTO(tableDetail);
        detailBuilder.setMemberInfoCardGuid(req.getMemberInfoCardGuid());
        detailBuilder.structTrade();
        //查询本地批次
        boolean existMerchant = initMerchantOrder(req.getGuid(), detailBuilder, detailBuilder.getTableMap().keySet());
        //图片获取
        initOrderItemImg(detailBuilder);

        //orderDetailDTO
        //设置本地批次和整单商品
        if (existMerchant) {
            initMerchantItemAndMemberDishInfo(orderRecord, detailBuilder);
        } else {
            //没有批次
            DineinOrderDetailRespDTO orderDetailTrade = detailBuilder.getOrderDetailTrade();
            detailBuilder.getOrderDetailDTO().setOrderState(buildDefaultOrderState(orderDetailTrade));
        }
        detailBuilder.hanldTables();
        TableDTO tableDTO = detailBuilder.getTableDTO();
        if (StringUtils.isEmpty(detailBuilder.getOrderDetailDTO())) {
            detailBuilder.getOrderDetailDTO().setMergeInfo(tableDTO.getAreaName() + tableDTO.getCode());
        }
        return detailBuilder.buiderMemberDishinfo();
    }

    private Integer buildDefaultOrderState(DineinOrderDetailRespDTO orderDetailTrade) {
        if (Objects.isNull(orderDetailTrade)) {
            return 1;
        }
        Integer orderState = OrderUtils.tradeStateTransition(orderDetailTrade.getState());
        if (Objects.nonNull(orderState)) {
            return orderState;
        }
        return 1;
    }


    private void handleMergeTabelEx(WxOrderRecordDO orderRecord, OrderDetailHelper detailBuilder) {
        try {
            log.info("处理并台,orderRecord:{}, detailBuilder:{}", JacksonUtils.writeValueAsString(orderRecord),
                    JacksonUtils.writeValueAsString(detailBuilder));
            handleMergeTable(orderRecord, detailBuilder);
        } catch (Exception e) {
            log.info("处理并台消息失败", e);
        }
    }

    private void handleMergeTable(WxOrderRecordDO orderRecord, OrderDetailHelper detailBuilder) {
        DineinOrderDetailRespDTO orderDetailTrade = detailBuilder.getOrderDetailTrade();
        if (orderDetailTrade == null) {
            return;
        }
        //0无并单，1主单， 2子单
        Integer upperState = orderDetailTrade.getUpperState();
        if (upperState.equals(0)) {
            return;
        }
        String mergeInfoKey = "mergeInfo:" + orderRecord.getOrderGuid();
        String mergeInfoStr = null;//(String)redisUtils.get(mergeInfoKey);
        if (!StringUtils.isEmpty(mergeInfoStr)) {
            detailBuilder.getOrderDetailDTO().setMergeInfo(mergeInfoStr);
            return;
        }
        if (upperState.equals(2)) {
            String mainOrderGuid = orderDetailTrade.getMainOrderGuid();
            orderRecord.setOrderGuid(mainOrderGuid);
            initTradeInfo(orderRecord, detailBuilder, null);
        }

        orderDetailTrade = detailBuilder.getOrderDetailTrade();
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetailTrade.getSubOrderDetails();
        if (!CollectionUtils.isEmpty(subOrderDetails)) {
            List<String> tableGuids = new ArrayList<>();
            subOrderDetails.forEach((a) -> {
                tableGuids.add(a.getDiningTableGuid());
            });
            String mainTableGuid = orderDetailTrade.getDiningTableGuid();
            tableGuids.add(mainTableGuid);
            TableBasicQueryDTO listids = new TableBasicQueryDTO();
            listids.setTableGuidList(tableGuids);
            listids.setStoreGuid(orderDetailTrade.getStoreGuid());
            List<TableBasicDTO> tableBasicDTOS = tableClientService.listByWeb(listids);
            if (CollectionUtils.isEmpty(tableBasicDTOS)) {
                return;
            }
            TableBasicDTO mainTable = null;
            Map<String, List<String>> mergeInfo = new HashMap<>();
            for (TableBasicDTO tableBasicDTO : tableBasicDTOS) {
                String tableGuid = tableBasicDTO.getGuid();
                String areaName = tableBasicDTO.getAreaName();
                List<String> singAreaMergeInfoList = mergeInfo.get(areaName);
                String tableCode = tableBasicDTO.getTableCode();
                if (singAreaMergeInfoList == null) {
                    singAreaMergeInfoList = new ArrayList<>();
                    mergeInfo.put(areaName, singAreaMergeInfoList);
                }
                if (tableGuid.equals(mainTableGuid)) {
                    mainTable = tableBasicDTO;
                    singAreaMergeInfoList.add(0, tableCode);
                } else {
                    singAreaMergeInfoList.add(tableCode);
                }
            }

            StringBuilder sb = new StringBuilder();
            if (Objects.nonNull(mainTable) && org.apache.commons.lang3.StringUtils.isNotBlank(mainTable.getAreaName())) {
                List<String> tableCodes = mergeInfo.get(mainTable.getAreaName());
                String join = StringSerializeUtils.joinToString(tableCodes, " ");
                sb.append(mainTable.getAreaName()).append("(").append(join).append(")");
                mergeInfo.remove(mainTable.getAreaName());
            }
            Iterator<Map.Entry<String, List<String>>> iterator = mergeInfo.entrySet().iterator();
            while (iterator.hasNext()) {
                sb.append("  ");
                Map.Entry<String, List<String>> listTh = iterator.next();
                String joinTh = StringSerializeUtils.joinToString(listTh.getValue(), " ");
                sb.append(listTh.getKey()).append("(").append(joinTh).append(")");
            }
            detailBuilder.getOrderDetailDTO().setMergeInfo(sb.toString());
            //redisUtils.setEx(mergeInfoKey,sb.toString(),2,TimeUnit.HOURS);
        }
    }

    private void updateCurrentSession(TableDTO tableDetail, int tradeMode) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        if (tableDetail.getTableGuid().equals(WeixinUserThreadLocal.getDiningTableGuid())) {
            return;
        }
        String storeGuid = tableDetail.getStoreGuid();
        BrandDTO brandInfoDetails = wxStoreSessionDetailsService.getBrandInfoDetails(storeGuid);
        if (brandInfoDetails != null && wxMemberSessionDTO != null) {
            wxMemberSessionDTO.setBrandGuid(brandInfoDetails.getGuid());
            wxMemberSessionDTO.setBrandName(brandInfoDetails.getName());
        }
        WxMemberSessionUtil.updateMemberSession(redisUtils, tableDetail, tradeMode);
    }


    public void initOrderItemImg(OrderDetailHelper detailBuilder) {
        Set<String> itemGuidImgSet = detailBuilder.getItemGuidImgSet();
        if (CollectionUtils.isEmpty(itemGuidImgSet)) {
            return;
        }
        //[6589349262889320448, 6589349262885126144, 6589349262880931840, 6572731250471659521]
        SingleDataDTO dto = new SingleDataDTO(null, new ArrayList<>(itemGuidImgSet));
        List<ItemImgDTO> ItemImgs = itemClientService.getItemPictureUrls(dto);
        detailBuilder.initItemImgs(ItemImgs);
    }


    private void initMerchantItemAndMemberDishInfo(WxOrderRecordDO orderRecord, OrderDetailHelper detailBuilder) {
        //只微信本地批次对应的商品项 只需要处理未接单和拒单的数据
        Map<String, OrderBatchItemDTO> batchDineItemMap = detailBuilder.getBatchDineItemMap();
        LambdaQueryWrapper<WxOrderItemDO> lambdaQuery = new LambdaQueryWrapper<>();
        //lambdaQuery.eq(WxOrderItemDO::getOrderRecordGuid, orderRecord.getGuid());
        Set<String> batchIds = batchDineItemMap.keySet();
        lambdaQuery.in(WxOrderItemDO::getMerchantGuid, batchIds);
		/*if(StringUtils.hasText(orderRecord.getOrderGuid())) {
			lambdaQuery.eq(WxOrderItemDO::getOrderGuid, orderRecord.getOrderGuid());
		}else {
			lambdaQuery.eq(WxOrderItemDO::getOrderRecordGuid, orderRecord.getGuid());
		}*/
        //lambdaQuery.in
        try {
            List<WxOrderItemDO> wxOrderItemDOList = orderItemService.list(lambdaQuery);
            detailBuilder.addWxOrderItemDOs(wxOrderItemDOList);
        } catch (Exception e) {
            log.error("", e);
        }


    }

    private boolean initMerchantOrder(String guid, OrderDetailHelper detailBuilder, Collection<String> tableGuids) {
        List<String> coll = null;
        if (tableGuids != null && tableGuids.size() > 1) {
            coll = new ArrayList<>(tableGuids);
        }
        log.info("查询桌台对应的订单:guid={},coll={}", guid, coll == null ? "" : JSON.toJSONString(coll));
        List<WxStoreMerchantOrderDO> list = wxStoreMerchantOrderService.selectByOrderRecordGuid(guid, coll);
        if (CollectionUtils.isEmpty(list)) {
            log.info("orderRecordGuid:{},微信端不存在批次", guid);
            return false;
        }
        log.info("查询桌台对应的订单批次:list={}", list);
        Map<String, OrderBatchItemDTO> batchDineItemMap = detailBuilder.getBatchDineItemMap();
        if (batchDineItemMap == null) {
            batchDineItemMap = new HashMap<>();
            detailBuilder.setBatchDineItemMap(batchDineItemMap);
        }
        int cannelOrderCount = 0;
        int pendingOrderCount = 0;
        for (WxStoreMerchantOrderDO merchantOrder : list) {
            orderBatchItemHandler(detailBuilder, merchantOrder);
            // 0:待处理，1：接单  2：拒单
            if (merchantOrder.getOrderState().equals(2)) {
                cannelOrderCount++;
            } else if (merchantOrder.getOrderState().equals(0)) {
                pendingOrderCount++;
            }
        }

        OrderDetailDTO orderDetailDTO = detailBuilder.getOrderDetailDTO();
        Integer orderState = orderDetailDTO.getOrderState();
        if (Integer.valueOf(3).equals(orderState)) {
            return true;
        }
        if (Integer.valueOf(2).equals(orderState)) {
            return true;
        }
        //全部拒单
        if (cannelOrderCount == list.size() && detailBuilder.getOrderDetailTrade() == null) {
            orderDetailDTO.setOrderState(3);
        } else if (pendingOrderCount > 0) {
            orderDetailDTO.setOrderState(0);
        } else {
            orderDetailDTO.setOrderState(1);
        }
        return true;
    }

    private void orderBatchItemHandler(OrderDetailHelper detailBuilder, WxStoreMerchantOrderDO merchantOrder) {
        final Map<String, OrderBatchItemDTO> batchDineItemMapFinal = detailBuilder.getBatchDineItemMap();
        final Map<String, OrderTableItemDTO> orderTableItemMap = detailBuilder.getOrderTableItemMap();
        UserContext userContext = UserContextUtils.get();

        String batchkey = merchantOrder.getGuid();
        OrderBatchItemDTO orderBatchItemDTO = batchDineItemMapFinal.computeIfAbsent(batchkey, v -> {
            OrderBatchItemDTO initOrderBatchItemDTO = OrderBatchItemDTO.builder().build();
            initOrderBatchItemDTO.setUpdateTime(merchantOrder.getGmtCreate());
            initOrderBatchItemDTO.setBatchCode(merchantOrder.getGuid());
            OrderTableItemDTO orderTableItemDTO = orderTableItemMap.get(merchantOrder.getDiningTableGuid());
            if (orderTableItemDTO == null) {
                return initOrderBatchItemDTO;
            }
            Set<OrderBatchItemDTO> orderBatchDTOs = orderTableItemDTO.getOrderBatchDTOset();
            orderBatchDTOs.add(initOrderBatchItemDTO);
            return initOrderBatchItemDTO;
        });
        detailBuilder.getOrderDetailDTO().setGuestCount(merchantOrder.getActualGuestsNo());
        final Integer orderState = merchantOrder.getOrderState();
        orderBatchItemDTO.setState(OrderUtils.orderBatchStateTransition(orderState));
        orderBatchItemDTO.setRemark(merchantOrder.getRemark());
        String openid = merchantOrder.getOpenId();
        if (merchantOrder.getIsLogin() == null || merchantOrder.getIsLogin() == 0) {
            orderBatchItemDTO.setLoginStatus(false);
        } else {
            UserContextUtils.put(userContext);
            orderBatchItemDTO.setLoginStatus(getmemberStateByCache(openid, userContext.getEnterpriseGuid()) == 0);
        }
        orderBatchItemDTO.setNickname(merchantOrder.getNickName());
        orderBatchItemDTO.setHeadPortrait(merchantOrder.getHeadImgUrl());
        orderBatchItemDTO.setOpenid(openid);
        orderBatchItemDTO.setOperateGuid(null);
    }


    public int getmemberStateByCache(String openid, String enterpriseGuid) {
        String cacheKey = openid + enterpriseGuid;
        Integer accountState = 2;
        Object state = redisUtils.get(cacheKey);
        if (state == null) {
            ResponseModel<ResponseAccountStatus> memberState = hsaBaseClientService.getMemberState(openid, enterpriseGuid);
            if (memberState.getCode() == 0) {
                //0正常,1冻结,2不存在
                accountState = memberState.getData().getAccountState();
            } else {
                //运营主体被禁用
                accountState = 2;
            }
            redisUtils.setNx(cacheKey, accountState.toString(), 300);
        } else {
            accountState = (Integer) state;
        }
        return accountState;
    }

    public void initAreaMap(WxOrderRecordDO orderRecord, OrderDetailHelper detailBuilder) {
        List<WxStoreTableCombineDTO> tableList = wxStoreTableClientService.tableCombineList(
                orderRecord.getTableGuid(),
                orderRecord.getOrderGuid() == null ? "" : orderRecord.getOrderGuid()
        );
        if (CollectionUtils.isEmpty(tableList)) {
            log.info("桌台数据为空,tableGuid:{},orderGuid：{}", orderRecord.getTableGuid(), orderRecord.getOrderGuid());
            return;
        }
        log.info("---------orderRecordGuid:{},桌台订单数据:{}---------", orderRecord.getGuid(), JSON.toJSONString(tableList));

        Map<String, List<String>> mergerInfoMap = new LinkedHashMap<>();
        Map<String, WxStoreTableCombineDTO> tableMap = new HashMap<>();
        String orderGuidNew = null;
        WxStoreTableCombineDTO mainTable = null;
        for (WxStoreTableCombineDTO tableCombine : tableList) {
            tableMap.put(tableCombine.getTableGuid(), tableCombine);
            boolean isMain = false;
            if (!StringUtils.isEmpty(tableCombine.getMainOrderGuid()) && tableCombine.getMainOrderGuid().length() > 5) {
                orderGuidNew = tableCombine.getMainOrderGuid();
            } else {
                orderGuidNew = tableCombine.getOrderGuid();
            }
            String tableCode = tableCombine.getTableCode();
            isMain = tableCombine.getMainOrderGuid() != null && tableCombine.getMainOrderGuid().equalsIgnoreCase(tableCombine.getOrderGuid());
            String areaName = tableCombine.getAreaName();
            List<String> strings = mergerInfoMap.get(areaName);
            if (strings == null) {
                strings = new ArrayList<>();
                mergerInfoMap.put(areaName, strings);
            }
            if (isMain) {
                mainTable = tableCombine;
                strings.add(0, tableCode);
            } else {
                strings.add(tableCode);
            }
        }
        String orderGuid = orderRecord.getOrderGuid();
        if (orderGuid == null || !orderGuid.equals(orderGuidNew)) {
            if (orderRecord.getOrderState().equals(0) || orderRecord.getOrderState().equals(1)) {
                orderRecord.setOrderGuid(orderGuidNew);
                updateOrderRecord(orderRecord.getGuid(), orderGuidNew);
            }
        }
        if (tableList.size() <= 1) {
            return;
        }
        detailBuilder.setTableMap(tableMap);
        StringBuilder stringBuilderMerger = new StringBuilder();
        List<String> tables = mergerInfoMap.get(mainTable.getAreaName());
        stringBuilderMerger.append(mainTable.getAreaName()).append("(")
                .append(StringSerializeUtils.joinToString(tables, " ")).append(")");
        mergerInfoMap.remove(mainTable.getAreaName());
        Set<Map.Entry<String, List<String>>> entries = mergerInfoMap.entrySet();
        Iterator<Map.Entry<String, List<String>>> iterator = entries.iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<String>> sbt = iterator.next();
            String key = sbt.getKey();
            stringBuilderMerger.append("  ");
            stringBuilderMerger.append(key).append("(").append(StringSerializeUtils.joinToString(sbt.getValue(), " "));
            stringBuilderMerger.append(")");
        }

        detailBuilder.getOrderDetailDTO().setMergeInfo(stringBuilderMerger.toString());
    }

    public DineinOrderDetailRespDTO getTradeOrderDetail(String orderGuid) {
        if (orderGuid == null) {
            return null;
        }
        OrderDetailQueryDTO orderDetailQueryDTO = new OrderDetailQueryDTO();
        orderDetailQueryDTO.setData(orderGuid);
        orderDetailQueryDTO.setSingleFlag(true);
        return wxStoreDineInOrderClientService.getOrderDetailForWx(orderDetailQueryDTO);
    }


    private void initTradeInfo(WxOrderRecordDO orderRecord, OrderDetailHelper builder, DineinOrderDetailRespDTO orderDetailResp) {
        String orderGuid = orderRecord.getOrderGuid();
        if (StringUtils.isEmpty(orderGuid)) {
            builder.initOrderStateAndOrderDetailTrade(null);
            return;
        }

        //查询订单数据
        if (Objects.isNull(orderDetailResp)) {
            orderDetailResp = getTradeOrderDetail(orderGuid);
        }
        log.info("获取订单数据,orderGuid:{},orderDetailResp:{}", orderGuid, JSON.toJSONString(orderDetailResp));

        // 如果订单未结账，并且存在附加费，则重新获取最新附加费
        if (TradeModeEnum.DINEIN.getCode() == orderDetailResp.getTradeMode()
                && orderDetailResp.getState() == 1
                && orderDetailResp.getAppendFee().compareTo(BigDecimal.ZERO) > 0
                && !UpperStateEnum.SAME_ORDER_STATE.contains(orderDetailResp.getUpperState())) {
            // 查询最新附加费
            SurchargeCalculateDTO surchargeCalculateDTO = new SurchargeCalculateDTO();
            surchargeCalculateDTO.setGuestCount(orderDetailResp.getGuestCount());
            surchargeCalculateDTO.setTableGuid(orderDetailResp.getDiningTableGuid());
            surchargeCalculateDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            List<SurchargeLinkDTO> surchargeList = businessClientService.calculateSurcharge(surchargeCalculateDTO);
            log.info("未结账订单重新获取订单附加费:{}", JacksonUtils.writeValueAsString(surchargeList));
            orderDetailResp.setAppendFee(calculateAppendFee(surchargeList, orderDetailResp.getGuestCount()));
        }

        // 此处特殊处理第三方活动支付的订单
        thirdActivityPayHandler(orderDetailResp);
        builder.initOrderStateAndOrderDetailTrade(orderDetailResp);
    }

    public void updateOrderRecord(String orderRecordGuid, String orderGuid) {
        if (StringUtils.isEmpty(orderGuid)) {
            return;
        }
        WxOrderRecordDO orderRecordUpdate = new WxOrderRecordDO();
        orderRecordUpdate.setGuid(orderRecordGuid);
        orderRecordUpdate.setOrderGuid(orderGuid);
        wxOrderRecordService.updateById(orderRecordUpdate);
    }


    /**
     * 特殊处理第三方活动支付的订单
     */
    private void thirdActivityPayHandler(DineinOrderDetailRespDTO orderDetailResp) {
        try {
            List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = orderDetailResp.getActuallyPayFeeDetailDTOS();
            if (CollectionUtils.isNotEmpty(actuallyPayFeeDetailDTOS)) {
                ActuallyPayFeeDetailDTO thirdActivityPay = actuallyPayFeeDetailDTOS.stream()
                        .filter(e -> Objects.nonNull(e.getPaymentType()) && e.getPaymentType() == PaymentTypeEnum.THIRD_ACTIVITY.getCode())
                        .findFirst().orElse(null);
                if (Objects.nonNull(thirdActivityPay) && BigDecimalUtil.greaterThanZero(thirdActivityPay.getAmount())) {
                    BigDecimal thirdActivityAmount = thirdActivityPay.getAmount();
                    orderDetailResp.setDiscountFee(orderDetailResp.getDiscountFee().add(thirdActivityAmount));
                    DiscountFeeDetailDTO detailDTO = new DiscountFeeDetailDTO();
                    detailDTO.setDiscountFee(thirdActivityAmount);
                    detailDTO.setDiscountName(DiscountTypeEnum.THIRD_ACTIVITY.getDesc());
                    detailDTO.setDiscountType(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
                    orderDetailResp.getDiscountFeeDetailDTOS().add(detailDTO);
                    // 订单实付金额 = 实付金额 - 第三方活动金额
                    BigDecimal actuallyPayFee = orderDetailResp.getActuallyPayFee().subtract(thirdActivityAmount);
                    if (BigDecimalUtil.lessThanZero(actuallyPayFee)) {
                        actuallyPayFee = BigDecimal.ZERO;
                    }
                    orderDetailResp.setActuallyPayFee(actuallyPayFee);

                    // 删除实收明细里的第三方活动
                    actuallyPayFeeDetailDTOS.removeIf(pay -> Objects.equals(PaymentTypeEnum.THIRD_ACTIVITY.getCode(), pay.getPaymentType()));
                }
            }
        } catch (Exception e) {
            log.error("解析第三方活动支付的订单数据错误:", e);
        }
    }

    /**
     * 计算附加费
     */
    private BigDecimal calculateAppendFee(List<SurchargeLinkDTO> surchargeList, Integer guestCount) {
        BigDecimal appendFee = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(surchargeList)) {
            return appendFee;
        }
        for (SurchargeLinkDTO surchargeLinkDTO : surchargeList) {
            // 按人
            if (surchargeLinkDTO.getType() == 0) {
                appendFee = appendFee.add(surchargeLinkDTO.getAmount().multiply(new BigDecimal(guestCount)));
            }
            // 按桌
            if (surchargeLinkDTO.getType() == 1) {
                appendFee = appendFee.add(surchargeLinkDTO.getAmount());
            }
        }
        return appendFee;
    }


}
