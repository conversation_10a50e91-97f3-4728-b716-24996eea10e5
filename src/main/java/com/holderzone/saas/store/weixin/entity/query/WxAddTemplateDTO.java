package com.holderzone.saas.store.weixin.entity.query;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/10
 * @description addTemplate
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "addTemplate", description = "addTemplate")
public class WxAddTemplateDTO {

    private String template_id_short;

    private List<String> keyword_name_list;
}
