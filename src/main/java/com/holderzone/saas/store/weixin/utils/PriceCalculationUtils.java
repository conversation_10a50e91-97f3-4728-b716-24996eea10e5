package com.holderzone.saas.store.weixin.utils;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.weixin.PricePairDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 价格计算工具类
 */
@Component
@Slf4j
public class PriceCalculationUtils {
	/**
	 * 属性加价计算
	 *
	 * @param itemInfoAttrGroupDTOS 属性集合
	 * @return 价格
	 */
	public BigDecimal getAttrTotalPrice(List<ItemInfoAttrGroupDTO> itemInfoAttrGroupDTOS) {
		return ObjectUtils.isEmpty(itemInfoAttrGroupDTOS)
				? BigDecimal.ZERO
				: itemInfoAttrGroupDTOS.stream().map(x -> {
			List<ItemInfoAttrDTO> attrList = x.getAttrList();
			if (!ObjectUtils.isEmpty(attrList)) {
				return attrList.stream().filter(k -> k.getUck() == 1).map(k ->
						Optional.ofNullable(k.getPrice()).orElse(BigDecimal.ZERO))
						.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
			}
			return BigDecimal.ZERO;
		}).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
	}

	/**
	 * 订单价格
	 *
	 * @param itemInfoDTOS 订单
	 * @return 价格
	 */
	public PricePairDTO orderPrice(List<ItemInfoDTO> itemInfoDTOS) {
		return ObjectUtils.isEmpty(itemInfoDTOS)
				? new PricePairDTO(BigDecimal.ZERO, BigDecimal.ZERO)
				: new PricePairDTO()
				.setOriginPrice(itemInfoDTOS.stream().map(x -> itemPrice(x).getOriginPrice()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
				.setMemberPrice(itemInfoDTOS.stream().map(x -> itemPrice(x).getMemberPrice()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
	}

	/**
	 * 商品价格
	 *
	 * @param itemInfoDTO 商品
	 * @return 价格
	 */
	public PricePairDTO itemPrice(ItemInfoDTO itemInfoDTO) {
		Integer itemType = itemInfoDTO.getItemType();
		switch (itemType) {
			case 1:
			case 5:
				return packageItemPrice(itemInfoDTO);
			case 3:
				return weightItemPrice(itemInfoDTO);
			case 2:
			case 4:
				return nonWeightItemPrice(itemInfoDTO);
		}
		return new PricePairDTO(BigDecimal.ZERO, BigDecimal.ZERO);
	}

	public static PricePairDTO itemPrice(DineInItemDTO dineInItemDTO) {
		Integer itemType = dineInItemDTO.getItemType();
		switch (itemType) {
			case 1:
				return packageItemPrice(dineInItemDTO);
			case 3:
				return weightItemPrice(dineInItemDTO);
			case 2:
			case 4:
				return nonWeightItemPrice(dineInItemDTO);
			default:
				return new PricePairDTO(BigDecimal.ZERO, BigDecimal.ZERO);
		}
	}

	private static BigDecimal attrTotalPrice(List<ItemAttrDTO> itemAttrDTOS) {
		return CollectionUtils.isEmpty(itemAttrDTOS)
				? BigDecimal.ZERO
				: itemAttrDTOS.stream().map(x -> Optional.ofNullable(x.getAttrPrice())
				.orElse(BigDecimal.ZERO)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
	}

	/**
	 * @param dineInItemDTO 商品
	 * @return 非称重计算
	 */
	private static PricePairDTO nonWeightItemPrice(DineInItemDTO dineInItemDTO) {
		BigDecimal memberPrice = dineInItemDTO.getMemberPrice();
		BigDecimal price = dineInItemDTO.getPrice();
		BigDecimal attrTotalPrice = attrTotalPrice(dineInItemDTO.getItemAttrDTOS());
		BigDecimal originPrice = dineInItemDTO.getCurrentCount().multiply(price.add(attrTotalPrice));
		BigDecimal preferencePrice = memberPrice!=null&&memberPrice.compareTo(BigDecimal.ZERO) > 0
				? dineInItemDTO.getCurrentCount().multiply(memberPrice.add(attrTotalPrice))
				: BigDecimal.ZERO;
		return new PricePairDTO(originPrice, preferencePrice);
	}

	private static PricePairDTO weightItemPrice(DineInItemDTO dineInItemDTO) {
		BigDecimal memberPrice = dineInItemDTO.getMemberPrice();
		BigDecimal price = dineInItemDTO.getPrice();
		BigDecimal attrTotalPrice = attrTotalPrice(dineInItemDTO.getItemAttrDTOS());

		BigDecimal originPrice = dineInItemDTO.getCurrentCount().multiply(price).add(attrTotalPrice);
		BigDecimal preferencePrice = memberPrice!=null&&memberPrice.compareTo(BigDecimal.ZERO) > 0
				? dineInItemDTO.getCurrentCount().multiply(memberPrice).add(attrTotalPrice)
				: BigDecimal.ZERO;
		return new PricePairDTO(originPrice, preferencePrice);
	}

	private static PricePairDTO packageItemPrice(DineInItemDTO dineInItemDTO) {
		BigDecimal memberPrice = dineInItemDTO.getMemberPrice();
		BigDecimal salePrice = dineInItemDTO.getPrice();



		List<SubDineInItemDTO> collect = dineInItemDTO.getPackageSubgroupDTOS().stream()
				.flatMap(x -> x.getSubDineInItemDTOS().stream())
				.collect(Collectors.toList());

		BigDecimal attrFee = subAttrFee2(collect);

		BigDecimal preferencePrice = memberPrice!=null&&memberPrice.compareTo(BigDecimal.ZERO) > 0
				? dineInItemDTO.getCurrentCount().multiply(memberPrice.add(attrFee))
				: BigDecimal.ZERO;
		BigDecimal originPrice = dineInItemDTO.getCurrentCount().multiply(salePrice.add(attrFee));
		return new PricePairDTO(originPrice	, preferencePrice);
	}

	private static BigDecimal subAttrFee2(List<SubDineInItemDTO> subDineInItemDTOS) {
		return ObjectUtils.isEmpty(subDineInItemDTOS)
				? BigDecimal.ZERO
				: subDineInItemDTOS.stream().map(x -> {
			BigDecimal itemNum = x.getPackageDefaultCount();
			BigDecimal defaultNum = x.getCurrentCount();
			BigDecimal attrTotalPrice = attrTotalPrice(x.getItemAttrDTOS());
			return attrTotalPrice.multiply(
					defaultNum.multiply(x.getItemType() == 3 ? BigDecimal.ONE : itemNum)
					.add(x.getAddPrice().multiply(defaultNum)));
		}).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
	}

	/**
	 * 非称重商品计算
	 *
	 * @param itemInfoDTO 商品
	 * @return 价格
	 */
	private PricePairDTO nonWeightItemPrice(ItemInfoDTO itemInfoDTO) {
		ItemInfoSkuDTO uckSku = getUckSku(itemInfoDTO);
		BigDecimal memberPrice = uckSku.getMemberPrice();
		BigDecimal salePrice = uckSku.getSalePrice();
		BigDecimal originPrice = itemInfoDTO.getCurrentCount().multiply(salePrice.add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList())));
		BigDecimal preferencePrice = memberPrice!=null&&memberPrice.compareTo(BigDecimal.ZERO) > 0
				? itemInfoDTO.getCurrentCount().multiply(memberPrice.add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList())))
				: BigDecimal.ZERO;
		return new PricePairDTO(originPrice, preferencePrice);
	}

	/**
	 * 称重商品计算
	 *
	 * @param itemInfoDTO 商品
	 * @return 价格
	 */
	private PricePairDTO weightItemPrice(ItemInfoDTO itemInfoDTO) {
		ItemInfoSkuDTO uckSku = getUckSku(itemInfoDTO);
		BigDecimal memberPrice = uckSku.getMemberPrice();
		BigDecimal salePrice = uckSku.getSalePrice();

		BigDecimal originPrice = itemInfoDTO.getCurrentCount().multiply(salePrice).add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList()));
		BigDecimal preferencePrice = memberPrice!=null&&memberPrice.compareTo(BigDecimal.ZERO) > 0
				? itemInfoDTO.getCurrentCount().multiply(memberPrice).add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList()))
				: BigDecimal.ZERO;
		return new PricePairDTO(originPrice, preferencePrice);
	}

	/**
	 * 套餐计算
	 *
	 * @param itemInfoDTO 商品
	 * @return 价格
	 */
	private PricePairDTO packageItemPrice(ItemInfoDTO itemInfoDTO) {
		ItemInfoSkuDTO uckSku = getUckSku(itemInfoDTO);
		BigDecimal memberPrice = uckSku.getMemberPrice();
		BigDecimal salePrice = uckSku.getSalePrice();
		List<ItemInfoSubgroupDTO> subgroupList = itemInfoDTO.getSubgroupList();
		List<ItemInfoSubSkuDTO> collect = subgroupList.stream()
				.flatMap(x -> x.getSubItemSkuList().stream())
				.collect(Collectors.toList());
		BigDecimal bigDecimal = subAttrFee(collect);

		BigDecimal preferencePrice = memberPrice.compareTo(BigDecimal.ZERO) > 0
				? itemInfoDTO.getCurrentCount().multiply(memberPrice.add(bigDecimal))
				: BigDecimal.ZERO;
		return new PricePairDTO(itemInfoDTO.getCurrentCount().multiply(salePrice.add(bigDecimal))
				, preferencePrice);
	}

	/**
	 * 套餐子项属性加价
	 *
	 * @param itemInfoSubSkuDTOS 套餐子项商品
	 * @return 价格
	 */
	public BigDecimal subAttrFee(List<ItemInfoSubSkuDTO> itemInfoSubSkuDTOS) {
		return ObjectUtils.isEmpty(itemInfoSubSkuDTOS)
				? BigDecimal.ZERO
				: itemInfoSubSkuDTOS.stream().map(x -> {
			BigDecimal itemNum = x.getItemNum();
			Integer defaultNum = x.getDefaultNum();
			BigDecimal attrTotalPrice = getAttrTotalPrice(x.getAttrGroupList());
			if (itemNum == null) {
				itemNum = BigDecimal.ONE;
			}
			return attrTotalPrice.multiply(new BigDecimal(defaultNum))
					.multiply(x.getItemType() == 3 ? BigDecimal.ONE : itemNum)
					.add(x.getAddPrice().multiply(new BigDecimal(defaultNum)));
		}).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
	}

	public ItemInfoSkuDTO getUckSku(ItemInfoDTO itemInfoDTO) {
		List<ItemInfoSkuDTO> skuList = itemInfoDTO.getSkuList();
		Assert.isTrue(!ObjectUtils.isEmpty(skuList), "规格不能为空");
		if (skuList.size() == 1) {
			return skuList.get(0);
		}
		Optional<ItemInfoSkuDTO> first = skuList.stream().filter(x -> x.getUck() == 1).findFirst();
		if (first.isPresent()) {
			return first.get();
		}
		log.error("商品:{}", JacksonUtils.writeValueAsString(itemInfoDTO));
		throw new RuntimeException("规格必选");
	}

}
