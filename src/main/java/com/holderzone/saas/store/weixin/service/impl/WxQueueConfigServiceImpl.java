package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxQueueConfigDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxQueueConfigDO;
import com.holderzone.saas.store.weixin.mapper.WxQueueConfigMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxQueueConfigMapstruct;
import com.holderzone.saas.store.weixin.service.WxConfigOverviewService;
import com.holderzone.saas.store.weixin.service.WxOrganizationService;
import com.holderzone.saas.store.weixin.service.WxQueueConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueConfigServiceImpl
 * @date 2019/05/09 17:26
 * @description 门店微信线上排队配置service实现类
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxQueueConfigServiceImpl extends ServiceImpl<WxQueueConfigMapper, WxQueueConfigDO> implements WxQueueConfigService {

    @Resource
    WxOrganizationService wxOrganizationService;

    @Resource
    WxQueueConfigMapstruct wxQueueConfigMapstruct;

    @Resource
    WxConfigOverviewService wxConfigOverviewService;

    @Override
    public Page<WxQueueConfigDTO> pageQueueConfig(WxStorePageReqDTO wxStorePageReqDTO) {
        Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> triple = wxOrganizationService.getStoreConfig(wxStorePageReqDTO);
        Page<StoreDTO> storeDTOPage = triple.getLeft();
        List<StoreDTO> storeDTOList = triple.getMiddle();
        List<String> storeGuidList = triple.getRight();
        Page<WxQueueConfigDTO> respDTOPage = new Page<>(storeDTOPage.getCurrentPage(), storeDTOPage.getPageSize(), storeDTOPage.getTotalCount());

        if (!storeDTOList.isEmpty()) {
            List<WxQueueConfigDO> queueConfigDOList = list(new LambdaQueryWrapper<WxQueueConfigDO>()
                    .in(WxQueueConfigDO::getStoreGuid, storeGuidList));
            Map<String, WxQueueConfigDO> queueConfigDOMap = queueConfigDOList.stream()
                    .collect(Collectors.toMap(WxQueueConfigDO::getStoreGuid, Function.identity()));

            List<WxQueueConfigDTO> respDTOList = storeDTOList.stream().map(storeDTO -> {
                WxQueueConfigDO wxQueueConfigDO = queueConfigDOMap.get(storeDTO.getGuid());
                if (ObjectUtils.isEmpty(wxQueueConfigDO)) {
                    throw new BusinessException("门店：【" + storeDTO.getName() + "】尚未初始化，请进入概览页面初始化");
                }
                WxQueueConfigDTO wxQueueConfigDTO = wxQueueConfigMapstruct.queueConfigDO2DTO(wxQueueConfigDO);
                wxQueueConfigDTO.setMaxDistant(new BigDecimal(wxQueueConfigDO.getDistant().toString())
                        .divide(new BigDecimal("1000"), 2, RoundingMode.HALF_UP));
                wxQueueConfigDTO.setStoreName(storeDTO.getName());
                List<String> brandNameList = Optional.ofNullable(storeDTO.getBrandDTOList())
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(BrandDTO::getName)
                        .collect(Collectors.toList());
                wxQueueConfigDTO.setBrandNameList(brandNameList);
                return wxQueueConfigDTO;
            }).collect(Collectors.toList());

            respDTOPage.setData(respDTOList);
            log.info("微信排队配置返回数据：{}", JacksonUtils.writeValueAsString(respDTOList));
        }
        return respDTOPage;
    }

    @Override
    public Boolean updateQueueConfig(WxQueueConfigDTO wxQueueConfigDTO) {
        if (Boolean.FALSE.equals(wxQueueConfigDTO.getIsOnlyState())) {
            wxConfigOverviewService.couldEdit(Collections.singletonList(wxQueueConfigDTO.getStoreGuid()), 1);
        }
        WxQueueConfigDO wxQueueConfigDO = validateDTO(wxQueueConfigDTO);
        return updateById(wxQueueConfigDO);
    }

    @Override
    public Boolean updateQueueConfigBatch(WxQueueConfigUpdateBatchReqDTO wxQueueConfigUpdateBatchReqDTO) {
        List<String> storeGuidList = wxQueueConfigUpdateBatchReqDTO.getStoreGuidList();
        wxConfigOverviewService.couldEdit(storeGuidList, 1);

        List<WxQueueConfigDO> needUpdateDOList = list(new LambdaQueryWrapper<WxQueueConfigDO>().in(WxQueueConfigDO::getStoreGuid, storeGuidList));
        List<WxQueueConfigDO> wxQueueConfigDOList = needUpdateDOList.stream().map(needUpdateDO -> {
            WxQueueConfigDO wxQueueConfigDO = validateDTO(wxQueueConfigUpdateBatchReqDTO.getWxQueueConfigDTO());
            wxQueueConfigDO.setGuid(needUpdateDO.getGuid());
            return wxQueueConfigDO;
        }).collect(Collectors.toList());

        return updateBatchById(wxQueueConfigDOList);
    }

    @Override
    public WxQueueConfigDTO getQueueConfig(WxQueueConfigDTO wxQueueConfigDTO) {
        WxQueueConfigDO wxQueueConfigDO = getById(wxQueueConfigDTO.getGuid());
        WxQueueConfigDTO wxQueueConfigDTO1 = wxQueueConfigMapstruct.queueConfigDO2DTO(wxQueueConfigDO);
        wxQueueConfigDTO1.setMaxDistant(new BigDecimal(wxQueueConfigDO.getDistant().toString())
                .divide(new BigDecimal("1000"), 2, RoundingMode.HALF_UP));
        return wxQueueConfigDTO1;
    }

    private WxQueueConfigDO validateDTO(WxQueueConfigDTO wxQueueConfigDTO) {
        WxQueueConfigDO wxQueueConfigDO = wxQueueConfigMapstruct.queueConfigDTO2DO(wxQueueConfigDTO);
        if (ObjectUtils.isEmpty(wxQueueConfigDTO.getMaxDistant()) && Objects.equals(1, wxQueueConfigDTO.getDistantConstraint())) {
            throw new BusinessException("请输入排队限制距离");
        }
        if (!ObjectUtils.isEmpty(wxQueueConfigDTO.getMaxDistant())) {
            wxQueueConfigDO.setDistant(wxQueueConfigDTO.getMaxDistant().multiply(new BigDecimal("1000")).intValue());
        }
        return wxQueueConfigDO;
    }

    @Override
    public WxQueueConfigDO getOne(String storeGuid) {
        return getOne(new LambdaQueryWrapper<WxQueueConfigDO>().eq(WxQueueConfigDO::getStoreGuid, storeGuid));
    }
}
