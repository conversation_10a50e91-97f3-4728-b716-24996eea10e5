package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.WxStickShopCartDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickShopCartRemoveDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickShopCartService
 * @date 2019/03/12 10:36
 * @description 微信桌贴购物车service
 * @program holder-saas-store
 */
public interface WxStickShopCartService {

    /**
     * 查询模板购物车集合
     *
     * @return
     */
    List<WxStickShopCartDTO> listShopCart();

    /**
     * 购物车新增商品
     *
     * @param wxStickShopCartDTOList
     */
    void addModels(List<WxStickShopCartDTO> wxStickShopCartDTOList);

    /**
     * 购物车删除商品
     *
     * @param wxStickShopCartRemoveDTO
     */
    void removeModels(WxStickShopCartRemoveDTO wxStickShopCartRemoveDTO);
}
