package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.WxPositionDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreListDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreCityListRespDTO;
import com.holderzone.saas.store.weixin.service.WxStoreListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreListController
 * @date 2019/05/17 20:01
 * @description 微信门店功能及信息列表Controller
 * @program holder-saas-store
 */
@RestController
@Slf4j
@RequestMapping("/wx_store")
@Api("微信门店功能及信息列表Controller")
public class WxStoreListController {

    @Autowired
    WxStoreListService wxStoreListService;

    @ApiOperation("获取门店列表")
    @PostMapping("/list_store_config")
    @ApiImplicitParam(value = "企业guid与品牌guid必传")
    public WxStoreListDTO listStoreConfig(@RequestBody WxPositionDTO wxPositionDTO) {
        log.info("获取门店微信功能列表请求参数：{}", JacksonUtils.writeValueAsString(wxPositionDTO));
        return wxStoreListService.listStoreConfig(wxPositionDTO);
    }

    @PostMapping("/list_store_city")
    public WxStoreCityListRespDTO listStoreCity(@RequestBody WxPositionDTO wxPositionDTO) {
        //log.info("获取城市列表请求参数：{}", JacksonUtils.writeValueAsString(wxPositionDTO));
        return wxStoreListService.listStoreCity(wxPositionDTO);
    }
}
