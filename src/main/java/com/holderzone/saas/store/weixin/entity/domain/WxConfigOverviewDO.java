package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxConfigOverviewDO
 * @date 2019/05/09 10:57
 * @description 微信门店功能预览DO
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("hsw_weixin_config_overview")
public class WxConfigOverviewDO {
    private Long id;

    @TableId("guid")
    private String guid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店微信相关功能是否开启（0关闭，1开启）
     */
    private Integer isOpened;

    /**
     * 门店微信点餐功能是否开启（0关闭，1开启）
     */
    private Integer forHereStatus;

    /**
     * 门店微信排队功能是否开启（0关闭，1开启）
     */
    private Integer queueUpStatus;

    /**
     * 门店微信预定功能是否开启（0关闭，1开启）
     */
    private Integer bookingStatus;

    /**
     * 门店微信外卖功能是否开启（0关闭，1开启）
     */
    private Integer takeawayStatus;
}