package com.holderzone.saas.store.weixin.controller.deal;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.OrderSubmitReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.OrderSubmitRespDTO;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/deal/order_item")
@Slf4j
@Api(tags = "订单批次")
public class OrderItemController {

    private final OrderItemService orderItemService;

    @Autowired
    public OrderItemController(OrderItemService orderItemService) {
        this.orderItemService = orderItemService;
    }

    @ApiOperation("下单，使用中")
    @PostMapping(value = "/submit")
    public OrderSubmitRespDTO submit(@RequestBody(required = false) OrderSubmitReqDTO orderSubmitReqDTO) {
        log.info("下单入参:{},WeixinUserThreadLocal:{}", JacksonUtils.writeValueAsString(orderSubmitReqDTO), WeixinUserThreadLocal.get());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("下单");
        OrderSubmitRespDTO submit = orderItemService.submit(orderSubmitReqDTO);
        log.info("下单submit返回参数:{}", JacksonUtils.writeValueAsString(submit));
        stopWatch.stop();
        log.warn(stopWatch.prettyPrint());
        return submit;
    }

    @ApiOperation("正餐下单")
    @PostMapping(value = "/submitDine")
    public OrderSubmitRespDTO submitDine(@RequestBody(required = false) OrderSubmitReqDTO orderSubmitReqDTO) {
        log.info("[正餐下单]入参={},WeixinUserThreadLocal={}", JacksonUtils.writeValueAsString(orderSubmitReqDTO),
                WeixinUserThreadLocal.get());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("下单");
        OrderSubmitRespDTO submit = orderItemService.submitDine(orderSubmitReqDTO);
        stopWatch.stop();
        log.warn(stopWatch.prettyPrint());
        return submit;
    }

    @ApiOperation("快餐下单")
    @PostMapping(value = "/submitFast")
    public OrderSubmitRespDTO submitFast(@RequestBody(required = false) OrderSubmitReqDTO orderSubmitReqDTO) {
        log.info("[快餐下单]入参={},WeixinUserThreadLocal:{}", JacksonUtils.writeValueAsString(orderSubmitReqDTO),
                WeixinUserThreadLocal.get());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("下单");
        OrderSubmitRespDTO submit = orderItemService.submitFast(orderSubmitReqDTO);
        stopWatch.stop();
        log.warn(stopWatch.prettyPrint());
        return submit;
    }

    @ApiModelProperty(value = "是否启用输入人数键盘")
    @GetMapping(value = "/keyboard")
    public Boolean enableKeyboard() {
        return orderItemService.enableKeyboard();
    }

    @ApiOperation(value = "通过订单guid查询商品")
    @GetMapping("/get_item_list_by_order_guid")
    public List<DineInItemDTO> getItemListByOrderGuid(@RequestParam("orderGuid") String orderGuid) {
        log.info("[通过订单guid查询商品]入参,orderGuid={}", orderGuid);
        return orderItemService.getItemListByOrderGuid(orderGuid);
    }

}
