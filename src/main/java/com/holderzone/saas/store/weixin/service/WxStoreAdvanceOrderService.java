package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceEstimateDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderPriceDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreAdvanceOrderService
 * @date 2019/3/18
 */
public interface WxStoreAdvanceOrderService {

	/**
	 * @param wxStoreAdvanceOrderDTO
	 * @return
	 * @describle 创建预订单
	 */
	WxStoreAdvanceEstimateDTO createAdvanceOrder(WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 * @describle 删除用户预订单
	 */
	Boolean delAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @describle 删除当前桌台预订单
	 */
	Boolean delAdvanceTableOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 * @describle 查询个人预订单
	 */
	WxStoreAdvanceOrderPriceDTO getPersonWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 * @describle 查询桌台预订单:正餐
	 */
	WxStoreAdvanceOrderPriceDTO getTableWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 * @describle 修改预订单的整单备注
	 */
	void updateAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * @param wxStoreAdvanceOrderDTOS
	 * @return
	 * @describle 桌台预订单总价
	 */
	BigDecimal getTotalPrice(List<WxStoreAdvanceOrderDTO> wxStoreAdvanceOrderDTOS);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @describle 删除预订单整单备注
	 */
	void delAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * 查询所有订单
	 *
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	List<WxStoreAdvanceOrderDTO> getTableWxStoreAdvanceOrders(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * 获取单个订单
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	WxStoreAdvanceOrderDTO getSingleWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * 查询预订单总价
	 *
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	BigDecimal getTotalPrice(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);


	BigDecimal getAttrTotalPrice(WxStoreItemRespDTO wxStoreItemRespDTO);

	/**
	 * 查询整单备注
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	String getAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);
	
	public WxStoreAdvanceEstimateDTO checkEstimate(WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO);
}
