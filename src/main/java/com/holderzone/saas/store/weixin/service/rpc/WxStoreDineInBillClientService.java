package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.member.request.MemberConsumeReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.order.request.bill.*;
import com.holderzone.saas.store.dto.order.response.bill.BillAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description 订单结算接口
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreDineInBillClientService
 * @date 2019/4/9
 */
@Component
@FeignClient(name = "holder-saas-store-trade"
		//,url ="http://***************:8912"
		,fallbackFactory = WxStoreDineInBillClientService.WxStoreDineInBillFallBack.class)
public interface WxStoreDineInBillClientService {

	String URL_PREFIX = "/dine_in_bill";

	@ApiOperation(value = "计算账单优惠", notes = "计算账单优惠")
	@PostMapping(URL_PREFIX+"/calculate")
	DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO);

	@PostMapping(URL_PREFIX+"/pay")
	EstimateItemRespDTO pay(@RequestBody BillPayReqDTO billPayReqDTO);

	@ApiOperation(value = "聚合支付", notes = "聚合支付")
	@PostMapping(URL_PREFIX+"/agg_pay")
	BillAggPayRespDTO aggPay(BillAggPayReqDTO billPayReqDTO);

	@ApiOperation(value = "聚合支付回调", notes = "聚合支付回调")
	@PostMapping(URL_PREFIX+"/callback")
	String aggCallBack(SaasNotifyDTO saasNotifyDTO);

	@ApiOperation(value = "反结账", notes = "反结账")
	@PostMapping(URL_PREFIX+"/recovery")
	String recovery(RecoveryReqDTO recoveryReqDTO);

	@ApiOperation(value = "校验是否有聚合支付退款", notes = "校验是否有聚合支付退款")
	@PostMapping(URL_PREFIX+"/validat_agg_refund")
	Boolean validatAggRefund(ValidatAggReturnReqDTO validatAggReturnReqDTO);

	@ApiOperation(value = "退款", notes = "退款")
	@PostMapping(URL_PREFIX+"/refund")
	Boolean refund(RefundReqDTO refundReqDTO);

	@ApiOperation(value = "会员消费记录列表", notes = "会员消费记录列表")
	@PostMapping(URL_PREFIX+"/member_consume_records")
	Page<MemberConsumeRespDTO> memberConsumeRecords(MemberConsumeReqDTO memberConsumeReqDTO);

	@Component
	@Slf4j
	class WxStoreDineInBillFallBack implements FallbackFactory<WxStoreDineInBillClientService> {
		@Override
		public WxStoreDineInBillClientService create(Throwable throwable) {
			return new WxStoreDineInBillClientService() {

				@Override
				public DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
					return null;
				}

				@Override
				public EstimateItemRespDTO pay(BillPayReqDTO billPayReqDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
				}

				@Override
				public BillAggPayRespDTO aggPay(BillAggPayReqDTO billPayReqDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
				}

				@Override
				public String aggCallBack(SaasNotifyDTO saasNotifyDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
				}

				@Override
				public String recovery(RecoveryReqDTO recoveryReqDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
				}

				@Override
				public Boolean validatAggRefund(ValidatAggReturnReqDTO validatAggReturnReqDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
				}

				@Override
				public Boolean refund(RefundReqDTO refundReqDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
				}

				@Override
				public Page<MemberConsumeRespDTO> memberConsumeRecords(MemberConsumeReqDTO memberConsumeReqDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
				}
			};
		}
	}
}
