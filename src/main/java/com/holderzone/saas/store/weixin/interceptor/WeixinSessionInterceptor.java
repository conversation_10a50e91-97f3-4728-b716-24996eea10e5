package com.holderzone.saas.store.weixin.interceptor;


import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.resource.common.util.LoginSource;
import com.holderzone.saas.store.dto.common.CommonConstant;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.Objects;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WebInterceptor
 * @date 2018/10/29 16:26
 * @description
 * @program holder-saas-aggregation-weixin
 */
@Configuration
@Slf4j
public class WeixinSessionInterceptor implements HandlerInterceptor {

    @Resource
    @Lazy
    WxStoreSessionDetailsService wxStoreSessionDetailsService;

    private static final String NULL_STR = "null";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws
            Exception {
        String enterpriseGuid = request.getHeader(CommonConstant.ENTERPRISEGUID_KEY);
        // 数据源
        if (StringUtils.isNotEmpty(enterpriseGuid)) {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        }
        log.info("header头中enterpriseGuid:{}", enterpriseGuid);
        String phone = request.getHeader(CommonConstant.PHONE);
        log.info("header头中的手机号:{}", phone);
        String wxSessionUser = request.getHeader(CommonConstant.WX_SESSION_USER);
        log.info("header头中的微信会员基础信息:{}", wxSessionUser);
        WxMemberSessionDTO wxMemberSessionDTO = new WxMemberSessionDTO();
        if (StringUtils.isNotEmpty(wxSessionUser)) {
            wxSessionUser = URLDecoder.decode(wxSessionUser, "utf-8");
            wxMemberSessionDTO = JacksonUtils.toObject(WxMemberSessionDTO.class, wxSessionUser);
            if (StringUtils.isEmpty(wxMemberSessionDTO.getBrandName()) && StringUtils.isNotEmpty(wxMemberSessionDTO.getBrandGuid())) {
                BrandDTO brandDetail = wxStoreSessionDetailsService.getBrandDetail(wxMemberSessionDTO.getBrandGuid());
                if (brandDetail != null) {
                    wxMemberSessionDTO.setBrandName(brandDetail.getName());
                }
            }

        }
        // 赚餐通过header头将手机号传过来
        if (StringUtils.isNotEmpty(phone) && !NULL_STR.equals(phone)) {
            wxMemberSessionDTO.setPhoneNum(phone);
        }
        UserContext userContext = afterSetUserContext(wxMemberSessionDTO, enterpriseGuid);
        log.info("store-weixin拦截器设置UserContext:{}", JacksonUtils.writeValueAsString(userContext));
        log.info("store-weixin拦截器设置wxSessionUser:{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        UserContextUtils.put(userContext);
        WeixinUserThreadLocal.put(JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        return true;
    }

    private UserContext afterSetUserContext(WxMemberSessionDTO wxMemberSessionDTO, String enterpriseGuid) {
        UserContext userContext = UserContextUtils.get();
        userContext.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
        if (wxMemberSessionDTO.getEnterpriseGuid() != null) {
            userContext.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        }
        WxUserInfoDTO wxUserInfoDTO = wxMemberSessionDTO.getWxUserInfoDTO();
        if (Objects.nonNull(wxUserInfoDTO)) {
            userContext.setUserGuid(StringUtils.isEmpty(userContext.getUserGuid()) ? wxUserInfoDTO.getOpenId() : userContext.getUserGuid());
            userContext.setUserName(StringUtils.isEmpty(userContext.getUserName()) ? wxUserInfoDTO.getNickname() : userContext.getUserName());
        }
        userContext.setStoreName(StringUtils.isEmpty(userContext.getStoreName()) ? wxMemberSessionDTO.getStoreName() : userContext.getStoreName());
        userContext.setStoreGuid(StringUtils.isEmpty(userContext.getStoreGuid()) ? wxMemberSessionDTO.getStoreGuid() : userContext.getStoreGuid());
        userContext.setSource(StringUtils.isEmpty(userContext.getSource()) ? LoginSource.WECHAT.code() : userContext.getSource());
        if (userContext.getEnterpriseGuid() == null && enterpriseGuid != null) {
            userContext.setEnterpriseGuid(enterpriseGuid);
        }
        //context.set
        return userContext;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView
            modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception
            ex) throws Exception {
        WeixinUserThreadLocal.remove();
        UserContextUtils.remove();
        EnterpriseIdentifier.remove();
    }
}
