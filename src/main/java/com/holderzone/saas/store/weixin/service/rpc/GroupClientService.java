package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@Component
@FeignClient(name = "holder-saas-takeaway-producer", fallbackFactory = GroupClientService.GroupClientServiceFallBack.class)
public interface GroupClientService {

    /**
     * 验券
     */
    @PostMapping("/group/coupon/verify")
    List<GroupVerifyDTO> verifyCoupon(@RequestBody CouPonReqDTO couPonReq);

    /**
     * 撤销验券
     */
    @PostMapping("/group/coupon/revoke")
    MtDelCouponRespDTO revokeCoupon(@RequestBody GroupVerifyDTO revokeReq);


    @Component
    class GroupClientServiceFallBack implements FallbackFactory<GroupClientService> {

        private static final Logger logger = LoggerFactory.getLogger(GroupClientServiceFallBack.class);

        @Override
        public GroupClientService create(Throwable throwable) {
            return new GroupClientService() {

                @Override
                public List<GroupVerifyDTO> verifyCoupon(CouPonReqDTO couPonReq) {
                    logger.error("验券 e={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public MtDelCouponRespDTO revokeCoupon(GroupVerifyDTO revokeReq) {
                    logger.error("撤销验券 e={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

            };
        }

    }


}
