package com.holderzone.saas.store.weixin.config;

import com.holderzone.holder.saas.common.exception.enums.ExceptionEnums;
import lombok.Data;

import java.io.Serializable;

/**
 * 前端数据返回基类
 *
 * <AUTHOR>
 * @date 2019年6月5日
 */
@Data
public class ResponseModel<T> implements Serializable {

    private static final long serialVersionUID = 3644322851500370651L;

    public ResponseModel() {
    }

    public ResponseModel(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public ResponseModel(String message) {
        this.code = 0;
        this.message = message;
    }

    public ResponseModel(ExceptionEnums exceptionEnums) {
        this.code = exceptionEnums.getCode();
        this.message = exceptionEnums.getMessage();
    }

    public ResponseModel(T data) {
        this.data = data;
    }

    private Integer code = 0;

    private String message = "ok";

    private T data;

}
