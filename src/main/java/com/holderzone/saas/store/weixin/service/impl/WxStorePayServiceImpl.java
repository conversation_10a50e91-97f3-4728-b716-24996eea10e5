package com.holderzone.saas.store.weixin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseIntegralOffset;
import com.holderzone.holder.saas.member.wechat.dto.activitie.GrouponListRespDTO;
import com.holderzone.holder.saas.member.wechat.dto.activitie.MemberConsumptionGiftDTO;
import com.holderzone.holder.saas.member.wechat.dto.label.RequestManualLabel;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.config.resp.DineFoodSettingRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.order.OrderLockDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayLockReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountRuleDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.pay.constant.AggPayStateEnum;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderMemberInfoReqDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.enums.weixin.WxOrderStateEnum;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.constant.RedisConstants;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.entity.dto.WxMemberConsumeMsgDTO;
import com.holderzone.saas.store.weixin.entity.dto.WxPayCallbackDTO;
import com.holderzone.saas.store.weixin.entity.enums.WxTemplateTypeEnum;
import com.holderzone.saas.store.weixin.event.NotifyMessageQueue;
import com.holderzone.saas.store.weixin.execption.CorrectResult;
import com.holderzone.saas.store.weixin.helper.WebsocketMessageHelper;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.utils.AesEncryptUtils;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStorePayServiceImpl
 * @date 2019/4/3
 */
@Service
@Slf4j
public class WxStorePayServiceImpl implements WxStorePayService {
    public static final String ORDER_CHANGE = "订单信息发生变化";
    private final static String WECHAT_CALLBACK = "http://holder-saas-store-weixin/wx_store_pay/result_operation?storeGuid=%s&openId=%s&allianceId=%s";
    @Autowired
    WxStoreOrderConfigService wxStoreOrderConfigService;
    @Autowired
    DynamicHelper dynamicHelper;
    @Autowired
    private WxStorePayClientService wxStorePayClientService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private WxStoreMenuDetailsService wxStoreMenuDetailsService;
    @Autowired
    private WxStoreDineInOrderClientService wxStoreDineInOrderClientService;
    @Autowired
    private WxStoreMerchantOrderService wxStoreMerchantOrderService;
    @Autowired
    private WxOrderRecordService wxOrderRecordService;
    @Autowired
    private WxStoreSessionDetailsService wxStoreSessionDetailsService;
    @Autowired
    private WxStoreDineInBillClientService wxStoreDineInBillClientService;
    @Autowired
    private WxStoreTableClientService wxStoreTableClientService;
    @Autowired
    private WxStoreEstimateClientService wxStoreEstimateClientService;
    @Autowired
    private NotifyMessageQueue notifyMessageQueue;
    @Autowired
    private ExecutorService executorService;
    @Autowired
    private WxStoreAdvanceOrderService wxStoreAdvanceOrderService;
    @Autowired
    private UserMemberSessionUtils userMemberSessionUtils;
    @Autowired
    private WxUserRecordService wxUserRecordService;
    @Resource
    private WebsocketMessageHelper websocketMessageHelper;
    @Resource
    private WxOpenMessageService wxOpenMessageService;
    @Resource
    private OrganizationClientService organizationClientService;
    @Resource
    private BusinessClientService businessClientService;

    @Resource
    private HsaBaseClientService hsaBaseClientService;

    @Autowired
    WeChatConfig weChatConfig;

    @Resource
    private WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;
    @Resource
    private WxStoreTradeOrderService wxStoreTradeOrderService;

    @Resource
    private MemberMarketingClientService memberMarketingClientService;
    @Resource
    private TcdOrderService tcdOrderService;

    @Resource
    private EnterpriseClientService enterpriseClientService;

    @Value("${wechat.mp.appId}")
    private String mpAppId;

    @Override
    public WxPayRespDTO weChatPublic(WxH5PayReqDTO wxH5PayReqDTO) {
        return wxStoreMenuDetailsService.judgeOrderType(wxH5PayReqDTO.getWxStoreConsumerDTO().getStoreGuid()) ? payDineInOrder(wxH5PayReqDTO) : payFast(wxH5PayReqDTO);
    }

    private WxPayRespDTO payFast(WxH5PayReqDTO wxH5PayReqDTO) {
        WxStoreConsumerDTO wxStoreConsumerDTO = wxH5PayReqDTO.getWxStoreConsumerDTO();
        String payGuid = redisUtils.generateGuid("hstTransactionRecord");
        UserContext userInfoDTO = UserContextUtils.get();

        //缓存买单人
        @NotBlank String storeGuid = wxStoreConsumerDTO.getStoreGuid();
        @NotBlank String openId = wxStoreConsumerDTO.getOpenId();
        String format = String.format(WECHAT_CALLBACK, storeGuid, openId, userInfoDTO.getAllianceId());

        CorrectResult<DineinOrderDetailRespDTO> correctResult = getCorrectResult(wxStoreConsumerDTO);
        if (!ObjectUtils.isEmpty(correctResult.getErrorMsg())) {
            return WxPayRespDTO.builder().couldPay(0).errorMsg(correctResult.getErrorMsg()).build();
        }
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = correctResult.getObj();
        if (!StringUtils.isEmpty(dineinOrderDetailRespDTO.getTip())) {
            return WxPayRespDTO.builder().couldPay(0).errorMsg(dineinOrderDetailRespDTO.getTip()).build();
        }
        log.error("payGuid:{},orderGuid", payGuid, dineinOrderDetailRespDTO.getOriginalOrderGuid());

        SaasAggWeChatPublicAccountPayDTO saasAggWeChatPublicAccountPayDTO = initialSaasAggWeChatPublicAccountPay(wxH5PayReqDTO, payGuid, format, dineinOrderDetailRespDTO);
        wxH5PayReqDTO.setOrderGuid(dineinOrderDetailRespDTO.getGuid());
        wxStoreSessionDetailsService.savePaidUser(storeGuid, openId, wxH5PayReqDTO);
        WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = (WxStoreAdvanceOrderDTO) redisUtils.get("estimate:" + dineinOrderDetailRespDTO.getGuid());
        WxStoreAdvanceEstimateDTO wxStoreAdvanceEstimateDTO = wxStoreAdvanceOrderService.checkEstimate(wxStoreAdvanceOrderDTO);
        log.info("估清结果:{}", wxStoreAdvanceEstimateDTO);
        if (!ObjectUtils.isEmpty(wxStoreAdvanceEstimateDTO)) {
            Boolean estimateResult = wxStoreAdvanceEstimateDTO.getEstimateResult();
            List<WxStoreEstimateItem> wxStoreEstimateItemList = wxStoreAdvanceEstimateDTO.getWxStoreEstimateItemList();
            if (estimateResult && !ObjectUtils.isEmpty(wxStoreEstimateItemList)) {
                return WxPayRespDTO.builder().couldPay(0).errorMsg("订单所含商品已售完").build();
            }
        }
        //获取h5支付页面
        log.info("调用h5支付参数：{}", saasAggWeChatPublicAccountPayDTO);
        return tryPay(saasAggWeChatPublicAccountPayDTO, wxStoreConsumerDTO, dineinOrderDetailRespDTO);
    }

    private WxPayRespDTO payDineInOrder(WxH5PayReqDTO wxH5PayReqDTO) {
        UserContext userInfoDTO = UserContextUtils.get();
        String payGuid = redisUtils.generateGuid("hstTransactionRecord");
        WxStoreConsumerDTO wxStoreConsumerDTO = wxH5PayReqDTO.getWxStoreConsumerDTO();
        WxPrepayReqDTO prepay = wxStoreSessionDetailsService.getPrepay(wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getOpenId());
        if (ObjectUtils.isEmpty(prepay)) {
            log.info("prepay过期了", prepay);
            return WxPayRespDTO.builder().couldPay(0).errorMsg("订单信息发生变化").build();
        }
        //查询订单
        CorrectResult<DineinOrderDetailRespDTO> correctResult = getCorrectResult(wxStoreConsumerDTO);
        if (!ObjectUtils.isEmpty(correctResult.getErrorMsg()) && correctResult.getResult() == 2) {
            return WxPayRespDTO.builder().couldPay(2).errorMsg("订单已完成支付").build();
        }
        if (!ObjectUtils.isEmpty(correctResult.getErrorMsg())) {
            return WxPayRespDTO.builder().couldPay(0).errorMsg(correctResult.getErrorMsg()).build();
        }
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = correctResult.getObj();
        if (!StringUtils.isEmpty(dineinOrderDetailRespDTO.getTip())) {
            return WxPayRespDTO.builder().couldPay(0).errorMsg(dineinOrderDetailRespDTO.getTip()).build();
        }
        WeChatPayLockReqDTO weChatPayLockReqDTO = new WeChatPayLockReqDTO();
        weChatPayLockReqDTO.setOrderGuid(dineinOrderDetailRespDTO.getGuid());
        weChatPayLockReqDTO.setVersion(prepay.getVersion());
        if (!wxStoreDineInOrderClientService.prepay(weChatPayLockReqDTO)) {
            return WxPayRespDTO.builder().couldPay(0).errorMsg("已经有人买单").build();
        }

        log.info("正餐支付下单详情:{}", dineinOrderDetailRespDTO);
        wxH5PayReqDTO.setOrderGuid(dineinOrderDetailRespDTO.getGuid());
        //缓存买单人
        String storeGuid = wxStoreConsumerDTO.getStoreGuid();
        String openId = wxStoreConsumerDTO.getOpenId();
        wxStoreSessionDetailsService.savePaidUser(storeGuid, openId, wxH5PayReqDTO);
        //缓存桌台买单人，方便订单详情查
        wxStoreSessionDetailsService.saveTablePaidUser(wxStoreConsumerDTO);

        String format = String.format(WECHAT_CALLBACK, storeGuid, openId, userInfoDTO.getAllianceId());

        SaasAggWeChatPublicAccountPayDTO saasAggWeChatPublicAccountPayDTO = initialSaasAggWeChatPublicAccountPay(wxH5PayReqDTO, payGuid, format, dineinOrderDetailRespDTO);

        log.info("调用h5支付参数：{}", saasAggWeChatPublicAccountPayDTO);
        //存回调结果
        return tryPay(saasAggWeChatPublicAccountPayDTO, wxStoreConsumerDTO, dineinOrderDetailRespDTO);
    }

    /**
     * 验证是否开启，获取h5支付地址
     *
     * @param aggWeChatPublicAccountPay
     * @param wxStoreConsumerDTO
     * @param dineinOrderDetailRespDTO
     * @return
     */
    private WxPayRespDTO tryPay(SaasAggWeChatPublicAccountPayDTO aggWeChatPublicAccountPay, WxStoreConsumerDTO wxStoreConsumerDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        log.info("[支付信息]dineinOrderDetailRespDTO={}", JacksonUtils.writeValueAsString(dineinOrderDetailRespDTO));
        WxPayRespDTO wxPayRespDTO = new WxPayRespDTO();
        try {
            WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
            wxStoreReqDTO.setStoreGuid(wxStoreConsumerDTO.getStoreGuid());
            WxOrderConfigDTO wxOrderConfigDTO = wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
            if (ObjectUtils.isEmpty(wxOrderConfigDTO.getIsOnlinePayed()) || wxOrderConfigDTO.getIsOnlinePayed() == 0) {
                wxPayRespDTO.setCouldPay(0);
                wxPayRespDTO.setErrorMsg("该门店暂未开启线上支付功能，请联系商家结账");
            } else {
                log.info("支付下单:{}", aggWeChatPublicAccountPay);
                String result = wxStorePayClientService.weChatPublic(aggWeChatPublicAccountPay);
                wxPayRespDTO.setPayUrl(result);
                wxPayRespDTO.setCouldPay(1);

                WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
                weChatPayReqDTO.setDeviceId(wxStoreConsumerDTO.getOpenId());
                weChatPayReqDTO.setOrderGuid(dineinOrderDetailRespDTO.getGuid());
                AggWeChatPublicAccountPayDTO publicAccountPayDTO = aggWeChatPublicAccountPay.getPublicAccountPayDTO();
                weChatPayReqDTO.setPayGuid(publicAccountPayDTO.getPayGUID());
                weChatPayReqDTO.setActuallyPayFee(dineinOrderDetailRespDTO.getActuallyPayFee());
                weChatPayReqDTO.setOrderFee(dineinOrderDetailRespDTO.getOrderFee());
                weChatPayReqDTO.setDiscountFee(dineinOrderDetailRespDTO.getDiscountFee());
                weChatPayReqDTO.setDiscountFeeDetailDTOS(dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS());
                weChatPayReqDTO.setMemberGuid(weChatPayReqDTO.getMemberGuid());
                log.info("支付下单dine缓存:{}", dineinOrderDetailRespDTO.getGuid());
                wxStoreSessionDetailsService.savePayCallBack(dineinOrderDetailRespDTO.getGuid(), weChatPayReqDTO);
            }
            OrderLockDTO orderLockDto = new OrderLockDTO();
            orderLockDto.setOrderGuid(dineinOrderDetailRespDTO.getGuid());
            orderLockDto.setDeviceId(wxStoreConsumerDTO.getOpenId());
            wxStoreDineInOrderClientService.lockorder(orderLockDto);
        } catch (Exception e) {
            e.printStackTrace();
            wxPayRespDTO.setCouldPay(0);
            wxPayRespDTO.setErrorMsg("该门店暂未开启线上支付功能，请联系商家结账");
        }
        log.info("wxPayRespDTO:{}", wxPayRespDTO);
        return wxPayRespDTO;
    }

    @Override
    public String wxResultOperation(WxPayCallbackDTO wxStoreCallbackNotifyDTO) {
        log.info("聚合支付通知支付结果:{}", wxStoreCallbackNotifyDTO);
        String openId = wxStoreCallbackNotifyDTO.getOpenId();
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(openId);
        log.info("微信支付回调后查询出Redis会员信息{}", JacksonUtils.writeValueAsString(userMemberSession));
        AggPayPollingRespDTO aggPayPollingRespDTO = wxStoreCallbackNotifyDTO.getSaasNotifyDTO().getAggPayPollingRespDTO();
        if (!AggPayStateEnum.SUCCESS.getId().equals(aggPayPollingRespDTO.getPaySt())) {
            return AggPayStateEnum.FAILURE.getDesc();
        }
        // 支付成功处理
        successPayHandler(wxStoreCallbackNotifyDTO, userMemberSession);
        return "success";
    }


    /**
     * 支付成功处理
     */
    private void successPayHandler(WxPayCallbackDTO wxStoreCallbackNotifyDTO, UserMemberSessionDTO userMemberSession) {
        AggPayPollingRespDTO aggPayPollingRespDTO = wxStoreCallbackNotifyDTO.getSaasNotifyDTO().getAggPayPollingRespDTO();
        String openId = wxStoreCallbackNotifyDTO.getOpenId();
        String storeGuid = wxStoreCallbackNotifyDTO.getStoreGuid();
        dynamicDataSource(wxStoreCallbackNotifyDTO, storeGuid, userMemberSession);
        String orderGuid = aggPayPollingRespDTO.getOrderGUID();
        WeChatPayReqDTO payCallBack = wxStoreSessionDetailsService.getPayCallBack(orderGuid);
        if (payCallBack == null) {
            log.error("当前订单已经处理:{}", orderGuid);
            return;
        }
        payCallBack.setAmount(aggPayPollingRespDTO.getAmount());
        payCallBack.setPayPowerId(aggPayPollingRespDTO.getPayPowerId());
        //取聚合支付回调的支付guid
        payCallBack.setPayGuid(aggPayPollingRespDTO.getPayGUID());
        //银行流水号
        payCallBack.setBankTransactionId(aggPayPollingRespDTO.getOrderNo());
        // 是否结账不关台
        if (!StringUtils.isEmpty(aggPayPollingRespDTO.getAttachData())) {
            BaseDTO baseDTO = JacksonUtils.toObject(BaseDTO.class, aggPayPollingRespDTO.getAttachData());
            payCallBack.setCloseTableFlag(baseDTO.getCloseTableFlag());
        }
        log.info("trade订单回调:{}", payCallBack);
        Boolean pay = wxStoreDineInOrderClientService.pay(payCallBack);
        if (Boolean.FALSE.equals(pay)) {
            log.error("trade支付失败");
            return;
        }
        OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setOrderGuid(aggPayPollingRespDTO.getOrderGUID());
        orderLockDto.setDeviceId(openId);
        wxStoreDineInOrderClientService.unLockOrder(orderLockDto);
        wxStoreSessionDetailsService.delPayCallBack(orderGuid);
        WxOrderRecordDO wxOrderRecordDO = wxOrderRecordService.getByOrderGuid(orderGuid);
        log.info("支付回调微信订单:{}", wxOrderRecordDO);
        wxOrderRecordDO.setOrderState(2);
        wxOrderRecordDO.setOrderStateName("已支付");
        if (StringUtils.isEmpty(userMemberSession.getUserGuid())) {
            WxUserRecordDO oneByOpenId = wxUserRecordService.getOneByOpenId(openId);
            if (oneByOpenId == null) {
                log.error("用户不存在");
            } else {
                wxOrderRecordDO.setUserRecordGuid(oneByOpenId.getGuid());
            }
        } else {
            wxOrderRecordDO.setUserRecordGuid(userMemberSession.getUserGuid());
        }
        wxOrderRecordDO.setIsLogin(userMemberSession.getIsLogin());
        String memberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
        String volumeCode = userMemberSession.getVolumeCode();
        wxOrderRecordDO.setMemberInfoCardGuid(!StringUtils.isEmpty(memberInfoCardGuid)
                && memberInfoCardGuid.length() > 5 ? memberInfoCardGuid : null);
        wxOrderRecordDO.setVolumeCode(!StringUtils.isEmpty(volumeCode) && volumeCode.length() > 5
                && Boolean.TRUE.equals(userMemberSession.getVolumeVerify()) ? volumeCode : null);
        wxOrderRecordDO.setActuallyPayFee(aggPayPollingRespDTO.getAmount());
        wxOrderRecordDO.setOrderHolderNo(aggPayPollingRespDTO.getBankOrderNo());
        wxOrderRecordDO.setUnMemberPrice(userMemberSession.getUnMemberFee());
        // 是否结账不关台
        isCloseTableAfterHandler(payCallBack, wxOrderRecordDO);
        wxOrderRecordService.saveOrUpdate(wxOrderRecordDO);
        log.info("OrderState异步后:{}", wxOrderRecordDO.getOrderState());
        async(wxStoreCallbackNotifyDTO, wxOrderRecordDO, openId, userMemberSession);
        log.info("OrderState异步后:{}", wxOrderRecordDO.getOrderState());
        if (wxOrderRecordDO.getOrderMode() == 0) {
            Set<String> openIDS = redisUtils.hKeyList(CacheName.DINE_USER + ":" + "hash" + ":" + wxOrderRecordDO.getTableGuid());
            userMemberSessionUtils.delAllUserSession(openIDS);
            redisUtils.delete(CacheName.DINE_USER + ":" + "hash" + ":" + wxOrderRecordDO.getTableGuid());
            userMemberSessionUtils.delTableCardList(wxOrderRecordDO.getStoreGuid(), openIDS);
        } else {
            userMemberSessionUtils.delAllUserSession(Collections.singleton(openId));
            userMemberSessionUtils.delTableCardList(wxOrderRecordDO.getStoreGuid(), Collections.singleton(openId));
            // 删除点餐人数
            wxStoreTradeOrderService.removeFastGuestCount(wxOrderRecordDO.getTableGuid(), openId);
            // 订单附加费缓存 -> 桌台附加费缓存
            wxStoreTradeOrderService.transformSurchargeCache(wxOrderRecordDO.getTableGuid(), aggPayPollingRespDTO.getOrderGUID());
            // 设置会员标签
            setMemberLabel(payCallBack);
        }
        log.info("支付完成，订单状态更改成功");
    }

    private void isCloseTableAfterHandler(WeChatPayReqDTO payCallBack, WxOrderRecordDO wxOrderRecordDO) {
        if (!Objects.equals(BooleanEnum.TRUE.getCode(), payCallBack.getCloseTableFlag())) {
            return;
        }
        wxOrderRecordDO.setOrderState(WxOrderStateEnum.PROCESSED.getCode());
        wxOrderRecordDO.setOrderStateName(WxOrderStateEnum.PROCESSED.getOrderStateName());
        // 新的记录
        WxOrderRecordDO newWxOrderRecordDO = new WxOrderRecordDO();
        BeanUtils.copyProperties(wxOrderRecordDO, newWxOrderRecordDO);
        newWxOrderRecordDO.setId(null);
        newWxOrderRecordDO.setGuid(redisUtils.generatdDTOGuid(WxOrderRecordDO.class));
        newWxOrderRecordDO.setOrderState(WxOrderStateEnum.PAID.getCode());
        newWxOrderRecordDO.setOrderStateName(WxOrderStateEnum.PAID.getOrderStateName());
        newWxOrderRecordDO.setUnMemberPrice(null);
        newWxOrderRecordDO.setActuallyPayFee(null);
        newWxOrderRecordDO.setItemName(null);
        newWxOrderRecordDO.setOrderHolderNo(null);
        newWxOrderRecordDO.setGmtCreate(null);
        newWxOrderRecordDO.setGmtModified(null);
        log.info("结账不关台, newWxOrderRecordDO::{}", JacksonUtils.writeValueAsString(newWxOrderRecordDO));
        wxOrderRecordService.save(newWxOrderRecordDO);
    }

    /**
     * 设置会员标签
     */
    private void setMemberLabel(WeChatPayReqDTO payCallBack) {
        if (CollectionUtils.isNotEmpty(payCallBack.getDiscountFeeDetailDTOS())) {
            DiscountFeeDetailDTO specialsDiscountDTO = payCallBack.getDiscountFeeDetailDTOS().stream()
                    .filter(d -> d.getDiscountType().equals(DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode()))
                    .findFirst()
                    .orElse(null);
            if (!ObjectUtils.isEmpty(specialsDiscountDTO)) {
                String rule = specialsDiscountDTO.getRule();
                DiscountRuleDTO discountRuleDTO = JacksonUtils.toObject(DiscountRuleDTO.class, rule);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(discountRuleDTO.getLabelGuidList())) {
                    RequestManualLabel addManualLabel = new RequestManualLabel();
                    addManualLabel.setMemberInfoGuidArray(payCallBack.getMemberGuid());
                    addManualLabel.setLabelSettingGuid(String.join(",", discountRuleDTO.getLabelGuidList()));
                    hsaBaseClientService.batchAddManualLabel(addManualLabel);
                    log.info("[会员标签设置完毕]addManualLabel={}", JacksonUtils.writeValueAsString(addManualLabel));
                }
            }
        }
    }

    public void async(WxPayCallbackDTO wxStoreCallbackNotifyDTO, WxOrderRecordDO wxOrderRecordDO,
                      String openId, UserMemberSessionDTO userMemberSession) {
        CompletableFuture.runAsync(() ->
                        paySuccessAsyncHandler(wxStoreCallbackNotifyDTO, wxOrderRecordDO, openId, userMemberSession), executorService)
                .whenComplete((v, e) -> log.error("异常,{}", v, e));
    }

    /**
     * 支付成功异步处理
     */
    private void paySuccessAsyncHandler(WxPayCallbackDTO wxStoreCallbackNotifyDTO, WxOrderRecordDO wxOrderRecordDO,
                                        String openId, UserMemberSessionDTO userMemberSession) {
        log.info("订单完成后异步处理async，{}", wxStoreCallbackNotifyDTO);
        String storeGuid = wxOrderRecordDO.getStoreGuid();
        String orderGuid = wxOrderRecordDO.getOrderGuid();
        String orderRecordGuid = wxOrderRecordDO.getGuid();
        dynamicDataSource(wxStoreCallbackNotifyDTO, storeGuid, userMemberSession);
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        singleDataDTO.setData(orderGuid);
        Boolean aBoolean = false;
        DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(singleDataDTO);
        String url = String.format(weChatConfig.getMEMBER_ORDER_PAGE(), UserContextUtils.getEnterpriseGuid(),
                storeGuid, openId,
                orderRecordGuid);
        WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO = WxMemberConsumeMsgDTO.builder()
                .brandGuid(wxOrderRecordDO.getBrandGuid()).openId(openId).storeGuid(storeGuid)
                .url(url).wxTemplateTypeEnum(WxTemplateTypeEnum.MEMBER_AMOUNT_CUSTOME_TEMPLATE)
                .first("您好！支付已成功！").remark("期待您再次光临").build();
        /**
         * 订单号：显示支付成功的订单编号
         消费金额：获取并显示本单支付金额
         消费门店：显示所消费门店名称，显示一行，若一行显示不完用...代替
         消费时间：获取并显示支付时间
         */
        wxMemberConsumeMsgDTO.addKeywordValue(orderDetail.getOrderNo());
        wxMemberConsumeMsgDTO.addKeywordValue(orderDetail.getActuallyPayFee().toString());
        wxMemberConsumeMsgDTO.addKeywordValue(orderDetail.getStoreName());
        wxMemberConsumeMsgDTO.addKeywordValue(orderDetail.getCheckoutTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")));
        wxOpenMessageService.sendMemberMsgNew(wxMemberConsumeMsgDTO);
        if (wxOrderRecordDO.getOrderMode() == 0) {
            Set<String> openIDS = redisUtils.hKeyList(CacheName.DINE_USER + ":hash:" + wxOrderRecordDO.getTableGuid());
            websocketMessageHelper.sendOrderEmqMessage(wxOrderRecordDO.getTableGuid(), openIDS);
            redisUtils.delete(CacheName.DINE_USER + ":hash:" + wxOrderRecordDO.getTableGuid());
            log.info("OrderState改前:{}", wxOrderRecordDO.getOrderState());
            wxStoreMerchantOrderService.dealUnFinishedOrders(orderGuid);
            log.info("OrderState改后:{}", wxOrderRecordDO.getOrderState());
        } else {
            log.info("支付成功，查询订单详情:{}", orderDetail);
            if (orderDetail.getDineInItemDTOS() != null) {
                WxStoreMerchantOrderDTO wxStoreMerchantOrder = (WxStoreMerchantOrderDTO) redisUtils
                        .get(CacheName.FAST_NOTIFY + ":" + orderDetail.getGuid());
                log.info("快餐缓存:{}", wxStoreMerchantOrder);
                if (wxStoreMerchantOrder != null) {
                    wxStoreMerchantOrderService.pushFastMsg(wxStoreMerchantOrder);
                }
                EstimateResultRespDTO estimateResultRespDTO = wxStoreEstimateClientService.verifyDineInItemEstimate(orderDetail.getDineInItemDTOS());
                aBoolean = estimateResultRespDTO.getSuccess();
            }
            // 删除多设备点餐人数
            if (!StringUtils.isEmpty(orderDetail.getUserWxPublicOpenId())
                    && !orderDetail.getUserWxPublicOpenId().equals(openId)) {
                wxStoreTradeOrderService.removeFastGuestCount(wxOrderRecordDO.getTableGuid(), orderDetail.getUserWxPublicOpenId());
            }
            log.info("回调扣库存:{}", aBoolean);
        }
        //调用消费有礼接口
        dealSendGift(userMemberSession, orderDetail);
    }

    private void dealSendGift(UserMemberSessionDTO userMemberSession, DineinOrderDetailRespDTO orderDetail) {
        try {
            //查询会员详情
            RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
            requestQueryMemberInfo.setMemberInfoGuid(orderDetail.getMemberGuid());
            log.info("会员详情请求:{}", requestQueryMemberInfo);
            ResponseMemberInfo responseMemberInfo = hsaBaseClientService.getMemberInfo(requestQueryMemberInfo).getData();

            log.info("会员详情:{}", responseMemberInfo);
            MemberConsumptionGiftDTO memberConsumptionGiftQO = new MemberConsumptionGiftDTO();
            BeanUtils.copyProperties(orderDetail, memberConsumptionGiftQO);
            if (CollUtil.isNotEmpty(orderDetail.getGrouponListRespDTOS())){
                //团购列表
                memberConsumptionGiftQO.setGrouponListRespDTOS(orderDetail.getGrouponListRespDTOS().stream()
                        .map(groupon -> {
                            GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
                            BeanUtils.copyProperties(groupon, grouponListRespDTO);
                            return grouponListRespDTO;
                        }).collect(Collectors.toList()));
            } else {
                //团购列表
                memberConsumptionGiftQO.setGrouponListRespDTOS(new ArrayList<>());
            }
            //会员
            memberConsumptionGiftQO.setOperSubjectGuid(userMemberSession.getOperSubjectGuid());
            memberConsumptionGiftQO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            memberConsumptionGiftQO.setMemberInfoGuid(orderDetail.getMemberGuid());
            memberConsumptionGiftQO.setMemberPhone(orderDetail.getMemberPhone());
            memberConsumptionGiftQO.setMemberName(StringUtils.isEmpty(responseMemberInfo.getNickName()) ?
                    responseMemberInfo.getUserName() : responseMemberInfo.getNickName());

            //订单
            memberConsumptionGiftQO.setOrderAmount(orderDetail.getActuallyPayFee());
            memberConsumptionGiftQO.setOrderNumber(orderDetail.getGuid());
            memberConsumptionGiftQO.setOrderTime(Objects.nonNull(orderDetail.getCheckoutTime()) ?
                    orderDetail.getCheckoutTime() : orderDetail.getGmtCreate());
            memberConsumptionGiftQO.setActivitySceneType(orderDetail.getTradeMode());
            memberConsumptionGiftQO.setRechargeStatus(1);

            //门店
            memberConsumptionGiftQO.setStoreName(orderDetail.getStoreName());
            memberConsumptionGiftQO.setStoreGuid(orderDetail.getStoreGuid());

            memberConsumptionGiftQO.setChangeSource(BaseDeviceTypeEnum.getDesc(orderDetail.getDeviceType()));
            if (Objects.equals(memberConsumptionGiftQO.getChangeSource(), BaseDeviceTypeEnum.TCD.getDesc())) {
                memberConsumptionGiftQO.setChangeSource("微信");
            }

            memberConsumptionGiftQO.setMemberInfoGradeGuid(responseMemberInfo.getOperationMemberInfoCardLevelGuid());
            memberConsumptionGiftQO.setMemberLabelGuid(responseMemberInfo.getLabelSettingGuid());

            log.info("微信支付调用会员消费有礼入参：{}", JacksonUtils.writeValueAsString(memberConsumptionGiftQO));
            memberMarketingClientService.dealConsumptionGift(memberConsumptionGiftQO);
        } catch (Exception e) {
            log.info("微信支付消费有礼调用异常", e);
        }
    }


    @Override
    public WxStorePayResultDTO memberPay(WxMemberPayDTO wxMemberPayDTO) throws Exception {
        updateMemberSource();
        WxPrepayReqDTO prepay = wxStoreSessionDetailsService.getPrepay(wxMemberPayDTO.getStoreGuid(), wxMemberPayDTO.getOpenId());
        if (ObjectUtils.isEmpty(prepay) || ObjectUtils.isEmpty(prepay.getOrderGuid())) {
            return WxStorePayResultDTO.builder().result(1).errorMsg("订单数据已经发生变化，请刷新").build();
        }
        wxMemberPayDTO.setMemberPassWord(AesEncryptUtils.aesDecrypt(wxMemberPayDTO.getMemberPassWord(), weChatConfig.getMemberPaykey()));
        String orderGuid = prepay.getOrderGuid();
        DineinOrderDetailRespDTO calculate = null;
        BillPayReqDTO billPayReqDTO = null;
        try {
            CorrectResult<DineinOrderDetailRespDTO> correctResult = wxStoreSessionDetailsService.calculateDetails2(orderGuid,
                    wxMemberPayDTO.getOpenId(), prepay.getMemberInfoCardGuid(), prepay.getMemberIntegral(), prepay.getVolumeCode());
            if (!ObjectUtils.isEmpty(correctResult.getErrorMsg())) {
                return WxStorePayResultDTO.builder().result(correctResult.getResult() == 3 ? 2 : 1).errorMsg(correctResult.getErrorMsg()).build();
            }
            calculate = correctResult.getObj();
            billPayReqDTO = initialBillPay(wxMemberPayDTO, prepay, orderGuid, calculate);
            log.info("会员支付入参:{}", billPayReqDTO);
            log.info("会员支付请求头:{}", JacksonUtils.writeValueAsString(UserContextUtils.get()));
            EstimateItemRespDTO pay = wxStoreDineInBillClientService.pay(billPayReqDTO);
            if (pay.getResult()) {
                wxStoreSessionDetailsService.delPrepay(wxMemberPayDTO.getStoreGuid(), wxMemberPayDTO.getOpenId());
                WxStoreAdvanceConsumerReqDTO build = WxStoreAdvanceConsumerReqDTO.builder().wxStoreConsumerDTO(wxMemberPayDTO.getWxStoreConsumerDTO()).build();
                //更新结账状态
                updateOrderDetails(wxMemberPayDTO, prepay, wxMemberPayDTO.getWxStoreConsumerDTO());
                //更新我的订单状态
                updateMyOwnOrder(calculate, build);
                return WxStorePayResultDTO.builder().result(0).build();
            } else if (pay.getEstimate()) {
                return WxStorePayResultDTO.builder().result(1).errorMsg("商品售尽").build();
            }
            return WxStorePayResultDTO.builder().result(1).errorMsg("支付失败").build();
        } catch (RuntimeException e) {
            log.error("", e);
            return exceptionNotify(e);
        }
    }

    //会员支付异常统坟
    private WxStorePayResultDTO exceptionNotify(RuntimeException e) {
        if (e.getCause().getMessage().contains("支付密码不正确")) {
            return WxStorePayResultDTO.builder().result(2).errorMsg("密码错误").build();
        }
        if (e.getCause().getMessage().contains("找不到对应的菜单")) {
            return WxStorePayResultDTO.builder().result(1).errorMsg("找不到对应的菜单，无法使用会员支付").build();
        }
        if (e.getCause().getMessage().contains("会员不")) {
            return WxStorePayResultDTO.builder().result(1).errorMsg("会员不存在，无法使用会员支付").build();
        }
        if (e.getCause().getMessage().contains("订单数据发生变化")) {
            return WxStorePayResultDTO.builder().result(1).errorMsg("订单数据发生变化").build();
        }
        return WxStorePayResultDTO.builder().result(2).errorMsg("支付失败").build();
    }

    /**
     * 修改会员结账来源
     */
    private void updateMemberSource() {
        UserContext userInfoDTO = UserContextUtils.get();
        userInfoDTO.setSource("8");
        UserContextUtils.put(JacksonUtils.writeValueAsString(userInfoDTO));
    }

    private void updateOrderDetails(WxMemberPayDTO wxMemberPayDTO, WxPrepayReqDTO prepay, WxStoreConsumerDTO wxStoreConsumerDTO) {
        if (prepay.getMode() == 0) {
            wxStoreSessionDetailsService.saveOrderState(wxMemberPayDTO.getTableGuid(), WxOrderStateEnum.PAID.getCode());
            wxStoreMerchantOrderService.updateOrderState(wxMemberPayDTO.getTableGuid(), 4);
            wxStoreSessionDetailsService.saveTablePaidUser(wxStoreConsumerDTO);
        } else {
            String redisKey = "WxFast:" + wxMemberPayDTO.getStoreGuid()
                    + ":" + wxMemberPayDTO.getOpenId();
            WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = (WxStoreMerchantOrderDTO) redisUtils.get(redisKey);
            wxStoreMerchantOrderService.pushFastMsg(wxStoreMerchantOrderDTO);
        }
    }

    private BillPayReqDTO initialBillPay(WxMemberPayDTO wxMemberPayDTO, WxPrepayReqDTO prepay, String orderGuid, DineinOrderDetailRespDTO calculate) {
        BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setVersion(calculate.getVersion());
        billPayReqDTO.setUserGuid(wxMemberPayDTO.getWxStoreConsumerDTO().getOpenId());
        billPayReqDTO.setUserName(wxMemberPayDTO.getWxStoreConsumerDTO().getNickName());
        billPayReqDTO.setDeviceId(wxMemberPayDTO.getOpenId());
        billPayReqDTO.setEnterpriseGuid(wxMemberPayDTO.getEnterpriseGuid());
        billPayReqDTO.setStoreGuid(wxMemberPayDTO.getStoreGuid());
        billPayReqDTO.setStoreName(wxMemberPayDTO.getWxStoreConsumerDTO().getStoreName());
        billPayReqDTO.setDeviceType(12);
        log.info("会员支付1:{},consumer:{}", billPayReqDTO, wxMemberPayDTO);

        UserContext userContext = UserContextUtils.get();
        userContext.setStoreName(wxMemberPayDTO.getWxStoreConsumerDTO().getStoreName());
        userContext.setUserGuid(wxMemberPayDTO.getOpenId());
        userContext.setUserName(wxMemberPayDTO.getNickName());
        UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
        log.info("会员支付请求头:{}", userContext);

        billPayReqDTO.setOpenId(wxMemberPayDTO.getWxStoreConsumerDTO().getOpenId());
        billPayReqDTO.setOrderGuid(orderGuid);
        billPayReqDTO.setFastFood(prepay.getMode() == 1);
        billPayReqDTO.setOrderFee(calculate.getOrderFee());
        billPayReqDTO.setAppendFee(calculate.getAppendFee());
        billPayReqDTO.setActuallyPayFee(calculate.getActuallyPayFee());
        billPayReqDTO.setDiscountFee(calculate.getDiscountFee());
        billPayReqDTO.setChangeFee(calculate.getChangeFee());
        billPayReqDTO.setDiscountFeeDetailDTOS(calculate.getDiscountFeeDetailDTOS());
        BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(calculate.getActuallyPayFee());
        payment.setPaymentType(4);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Collections.singletonList(payment));
        billPayReqDTO.setMemberPassWord(wxMemberPayDTO.getMemberPassWord());
        billPayReqDTO.setMemberInfoCardGuid(prepay.getMemberInfoCardGuid());
        ResponseIntegralOffset integralOffsetResultRespDTO = calculate.getIntegralOffsetResultRespDTO();
        billPayReqDTO.setUseIntegral(!ObjectUtils.isEmpty(integralOffsetResultRespDTO) && !ObjectUtils.isEmpty(integralOffsetResultRespDTO.getUseIntegral()) && integralOffsetResultRespDTO.getUseIntegral() > 0 ? integralOffsetResultRespDTO.getUseIntegral() : null);
        billPayReqDTO.setIntegralDiscountMoney(!ObjectUtils.isEmpty(integralOffsetResultRespDTO) && !ObjectUtils.isEmpty(integralOffsetResultRespDTO.getUseIntegral()) && integralOffsetResultRespDTO.getUseIntegral() > 0 ? integralOffsetResultRespDTO.getDeductionMoney() : null);
        //新增
        log.info("会员支付传参:{}", billPayReqDTO);
        return billPayReqDTO;
    }


    private void updateMyOwnOrder(DineinOrderDetailRespDTO calculate, WxStoreAdvanceConsumerReqDTO build) {
        calculate.setState(2);
        calculate.setStateName("已支付");
        updateMyOrder(build, calculate);
    }

    @Override
    public WxPayWayRespDTO getAllPayWay(WxStorePayReqDTO wxStorePayReqDTO) {
        try {
            WxPrepayReqDTO prepay = wxStoreSessionDetailsService.getPrepay(wxStorePayReqDTO.getStoreGuid(), wxStorePayReqDTO.getOpenId());
            ResponseMemberAndCardInfoDTO memberInfoAndCardList = wxStoreSessionDetailsService.getMemberInfoAndCardList(UserContextUtils.getEnterpriseGuid(), UserContextUtils.getStoreGuid(), wxStorePayReqDTO.getOpenId());
            if (!ObjectUtils.isEmpty(memberInfoAndCardList) && !ObjectUtils.isEmpty(memberInfoAndCardList.getMemberCardListRespDTOs())) {
                List<ResponseMemberCard> memberCardListRespDTOs = memberInfoAndCardList.getMemberCardListRespDTOs();
                Optional<ResponseMemberCard> first = memberCardListRespDTOs.stream().filter(x -> x.getMemberInfoCardGuid().equals(prepay.getMemberInfoCardGuid())).findFirst();
                if (first.isPresent()) {
                    ResponseMemberCard memberCardRespDTO = first.get();
                    return WxPayWayRespDTO.builder().whetherCard(0).cardName(memberCardRespDTO.getCardName()).balance(memberCardRespDTO.getCardMoney())
                            .payAmount(prepay.getPayAmount()).enable(prepay.getPayAmount().compareTo(memberCardRespDTO.getCardMoney()) > 0 ? 1 : 0).build();
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        log.error("没有选择会员:{}", wxStorePayReqDTO.getOpenId());
        return WxPayWayRespDTO.builder().whetherCard(1).build();
    }

    @Override
    public WxPrepayRespDTO prepay(WxPrepayReqDTO wxPrepayReqDTO) {
        WxPrepayReqDTO prepay = wxStoreSessionDetailsService.getPrepay(wxPrepayReqDTO.getStoreGuid(), wxPrepayReqDTO.getOpenId());
//		String orderGuid = wxStoreMenuDetailsService.judgeOrderType(wxPrepayReqDTO.getStoreGuid())
////				? wxStoreTableClientService.getOrderGuid(wxPrepayReqDTO.getTableGuid())
////				: wxStoreSessionDetailsService.getFastOrderGuid(wxPrepayReqDTO.getStoreGuid(), wxPrepayReqDTO.getOpenId());
        if (ObjectUtils.isEmpty(prepay) || StringUtils.isEmpty(prepay.getOrderGuid())) {
            return WxPrepayRespDTO.builder().result(1).errorMsg("订单已经失效,请刷新").build();
        }
        String orderGuid = prepay.getOrderGuid();
        try {
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setData(orderGuid);
            DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(singleDataDTO);
            if (ObjectUtils.isEmpty(orderDetail) || orderDetail.getState() != 1) {
                return WxPrepayRespDTO.builder().result(1).errorMsg("订单已经发生变化，请刷新").build();
            } else {
                BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
                billCalculateReqDTO.setOrderGuid(orderGuid);
                billCalculateReqDTO.setEnterpriseGuid(wxPrepayReqDTO.getEnterpriseGuid());
                billCalculateReqDTO.setStoreGuid(wxPrepayReqDTO.getStoreGuid());
                billCalculateReqDTO.setMemberInfoCardGuid(prepay.getMemberInfoCardGuid());
                billCalculateReqDTO.setMemberIntegral(wxPrepayReqDTO.getMemberIntegral());
                billCalculateReqDTO.setVolumeCode(prepay.getVolumeCode());
                billCalculateReqDTO.setMemberLogin(ObjectUtils.isEmpty(prepay.getMemberInfoCardGuid())
                        && ObjectUtils.isEmpty(prepay.getMemberIntegral())
                        && ObjectUtils.isEmpty(prepay.getVolumeCode()) ? -1 : 1);
                billCalculateReqDTO.setVerify(ObjectUtils.isEmpty(prepay.getVolumeCode()) ? null : 1);
                log.info("计算订单优惠入参:{}", billCalculateReqDTO);
                CorrectResult<DineinOrderDetailRespDTO> correctResult = wxStoreSessionDetailsService.calculateDetails(orderGuid, wxPrepayReqDTO.getOpenId(), prepay.getMemberInfoCardGuid(), prepay.getMemberIntegral(), prepay.getVolumeCode());
                if (!ObjectUtils.isEmpty(correctResult.getErrorMsg())) {
                    return WxPrepayRespDTO.builder().result(1).errorMsg(correctResult.getErrorMsg()).build();
                }
                if (correctResult.getResult() == 2) {
                    return WxPrepayRespDTO.builder().result(1).errorMsg("订单已经发生变化，请刷新").build();
                }
                DineinOrderDetailRespDTO calculate = correctResult.getObj();
                if (wxPrepayReqDTO.getPayAmount().compareTo(calculate.getActuallyPayFee()) != 0) {
                    return WxPrepayRespDTO.builder().result(1).errorMsg("订单已经发生变化，请刷新").build();
                }
                if (!StringUtils.isEmpty(calculate.getTip())) {
                    return WxPrepayRespDTO.builder().result(1).errorMsg(calculate.getTip()).build();
                }
                prepay.setPayAmount(calculate.getActuallyPayFee());
                wxStoreSessionDetailsService.savePrepay(prepay);
                return WxPrepayRespDTO.builder().result(0).build();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return WxPrepayRespDTO.builder().result(1).errorMsg("无法获取订单").build();
        }
    }

    @Override
    public WxPrepayRespDTO validateOrder(WxStorePayReqDTO wxStorePayReqDTO) {
        String orderGuid;
        if (wxStoreMenuDetailsService.judgeOrderType(wxStorePayReqDTO.getStoreGuid())) {
            orderGuid = wxStoreTableClientService.getOrderGuid(wxStorePayReqDTO.getTableGuid());
        } else {
            orderGuid = wxStoreSessionDetailsService.getFastOrderGuid(wxStorePayReqDTO.getStoreGuid(), wxStorePayReqDTO.getOpenId());
        }
        if (ObjectUtils.isEmpty(orderGuid)) {
            return WxPrepayRespDTO.builder().result(0).errorMsg("当前订单已经成功处理").build();
        }
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        singleDataDTO.setData(orderGuid);
        try {
            DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(singleDataDTO);
            if (!ObjectUtils.isEmpty(orderDetail) && orderDetail.getState() == 2) {
                return WxPrepayRespDTO.builder().result(0).errorMsg("订单已经处理").build();
            }
        } catch (Exception e) {
            log.error("订单验证:{}", e.getCause());
        }
        return WxPrepayRespDTO.builder().result(1).errorMsg("订单还未处理").build();
    }

    @Override
    public WxPrepayConfirmRespDTO memberConfirm(WxPrepayConfirmReqDTO wxPrepayConfirmReqDTO) {
        CorrectResult<DineinOrderDetailRespDTO> correctResult = wxStoreSessionDetailsService.calculateDetails(
                wxStoreSessionDetailsService.getOrderGuid(wxPrepayConfirmReqDTO.getStoreGuid(), wxPrepayConfirmReqDTO.getTableGuid(), wxPrepayConfirmReqDTO.getOpenId())
                , wxPrepayConfirmReqDTO.getOpenId(), wxPrepayConfirmReqDTO.getMemberInfoCardGuid(), wxPrepayConfirmReqDTO.getMemberIntegral(), wxPrepayConfirmReqDTO.getVolumeCode());
        if (correctResult.getResult() == 2) {
            wxStoreSessionDetailsService.delPrepay(wxPrepayConfirmReqDTO.getStoreGuid(), wxPrepayConfirmReqDTO.getOpenId());
            return WxPrepayConfirmRespDTO.builder().result(2).errorMsg("订单详情发生变化").build();
        }
        if (!ObjectUtils.isEmpty(correctResult.getErrorMsg())) {
            return WxPrepayConfirmRespDTO.builder().result(3).errorMsg(correctResult.getErrorMsg()).build();
        }
        WxPrepayReqDTO prepay = wxStoreSessionDetailsService.getPrepay(wxPrepayConfirmReqDTO.getStoreGuid(), wxPrepayConfirmReqDTO.getOpenId());
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = correctResult.getObj();
        log.info("查看是否有错误信息提示:{}", dineinOrderDetailRespDTO.getTip());
        if (!StringUtils.isEmpty(dineinOrderDetailRespDTO.getTip())) {
            return WxPrepayConfirmRespDTO.builder().result(3).errorMsg(dineinOrderDetailRespDTO.getTip()).build();
        }
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS();
        if (!ObjectUtils.isEmpty(discountFeeDetailDTOS)) {
            Optional<DiscountFeeDetailDTO> first = discountFeeDetailDTOS.stream().filter(x -> x.getDiscountType() == 7).findFirst();
            if (first.isPresent()) {
                if (first.get().getDiscountFee().compareTo(BigDecimal.ZERO) == 0 && !StringUtils.isEmpty(wxPrepayConfirmReqDTO
                        .getVolumeCode()) && !"-1".equals(wxPrepayConfirmReqDTO.getVolumeCode())) {
                    return WxPrepayConfirmRespDTO.builder().result(3).errorMsg("当前优惠券没有优惠").build();
                }
            }
        }
        if (ObjectUtils.isEmpty(prepay)) {
            prepay = WxPrepayReqDTO.builder().enterpriseGuid(wxPrepayConfirmReqDTO.getEnterpriseGuid()).brandGuid(wxPrepayConfirmReqDTO.getBrandGuid())
                    .storeGuid(wxPrepayConfirmReqDTO.getStoreGuid()).tableGuid(wxPrepayConfirmReqDTO.getTableGuid()).openId(wxPrepayConfirmReqDTO.getOpenId()).orderGuid(dineinOrderDetailRespDTO.getGuid()).mode(dineinOrderDetailRespDTO.getTradeMode()).build();
        }
        if (!ObjectUtils.isEmpty(wxPrepayConfirmReqDTO.getMemberInfoCardGuid())) {
            if (Objects.equals("-1", wxPrepayConfirmReqDTO.getMemberInfoCardGuid())) {
                prepay.setCardGuid("-1");
                prepay.setMemberIntegral(null);
                prepay.setCardGuid("-1");
                prepay.setMemberInfoCardGuid("-1");
                prepay.setMemberCardGuid("-1");
            } else {
                prepay.setCardGuid(wxPrepayConfirmReqDTO.getCardGuid());
                prepay.setMemberInfoCardGuid(wxPrepayConfirmReqDTO.getMemberInfoCardGuid());
                prepay.setMemberIntegral(wxPrepayConfirmReqDTO.getMemberIntegral());
                prepay.setMemberCardGuid(wxPrepayConfirmReqDTO.getCardGuid());
            }
        }
        if (!ObjectUtils.isEmpty(wxPrepayConfirmReqDTO.getVolumeCode())) {
            if (Objects.equals("-1", wxPrepayConfirmReqDTO.getVolumeCode())) {
                prepay.setVolumeCode("-1");
            } else {
                prepay.setVolumeCode(wxPrepayConfirmReqDTO.getVolumeCode());
            }
        }
        wxStoreSessionDetailsService.savePrepay(prepay);
        return WxPrepayConfirmRespDTO.builder().result(0).build();
    }

    @Override
    public WxStorePayResultDTO zeroPay(WxZeroPayReqDTO wxZeroPayReqDTO) {
        updateMemberSource();
        WxStoreConsumerDTO wxStoreConsumerDTO = wxZeroPayReqDTO.getWxStoreConsumerDTO();
        WxPrepayReqDTO prepay = wxStoreSessionDetailsService.getPrepay(wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getOpenId());
        if (ObjectUtils.isEmpty(prepay) || ObjectUtils.isEmpty(prepay.getOrderGuid())) {
            log.error("订单支付信息为空或orderGuid为空,prepay:{}", JacksonUtils.writeValueAsString(prepay));
            return WxStorePayResultDTO.builder().result(1).errorMsg(ORDER_CHANGE).build();
        }
        CorrectResult<DineinOrderDetailRespDTO> correctResult = wxStoreSessionDetailsService.calculateDetails2(prepay.getOrderGuid(), wxStoreConsumerDTO.getOpenId(),
                prepay.getMemberInfoCardGuid(), prepay.getMemberIntegral(), prepay.getVolumeCode());
        if (!ObjectUtils.isEmpty(correctResult.getErrorMsg())) {
            return WxStorePayResultDTO.builder().result(1).errorMsg(correctResult.getErrorMsg()).build();
        }
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = correctResult.getObj();
        if (dineinOrderDetailRespDTO.getActuallyPayFee().compareTo(BigDecimal.ZERO) != 0) {
            return WxStorePayResultDTO.builder().result(1).errorMsg(ORDER_CHANGE).build();
        }
        if (!StringUtils.isEmpty(dineinOrderDetailRespDTO.getTip())) {
            return WxStorePayResultDTO.builder().result(1).errorMsg(dineinOrderDetailRespDTO.getTip()).build();
        }
        BillPayReqDTO billPayReqDTO = initialZeroBillPay(prepay, wxStoreConsumerDTO, dineinOrderDetailRespDTO);
        try {
            EstimateItemRespDTO pay = wxStoreDineInBillClientService.pay(billPayReqDTO);
            if (pay.getResult()) {
                log.info("0元支付成功:{}", JacksonUtils.writeValueAsString(pay));
                WxMemberPayDTO wxMemberPayDTO = WxMemberPayDTO.builder().wxStoreConsumerDTO(wxStoreConsumerDTO).enterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid()).storeGuid(wxStoreConsumerDTO.getStoreGuid()).tableGuid(wxStoreConsumerDTO.getDiningTableGuid()).openId(wxStoreConsumerDTO.getOpenId()).headImgUrl(wxStoreConsumerDTO.getHeadImgUrl()).nickName(wxStoreConsumerDTO.getNickName()).storeName(wxStoreConsumerDTO.getStoreName()).build();
                WxStoreAdvanceConsumerReqDTO build = WxStoreAdvanceConsumerReqDTO.builder().wxStoreConsumerDTO(wxMemberPayDTO.getWxStoreConsumerDTO()).build();
                //更新桌台相关信息
                updateOrderDetails(wxMemberPayDTO, prepay, wxStoreConsumerDTO);
                //更新我的订单状态
                updateMyOwnOrder(dineinOrderDetailRespDTO, build);
                wxStoreSessionDetailsService.delPrepay(wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getOpenId());
                return WxStorePayResultDTO.builder().result(0).build();
            } else if (pay.getEstimate()) {
                return WxStorePayResultDTO.builder().result(1).errorMsg("商品售尽").build();
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getCause().toString().contains("根据菜单guid")) {
                return WxStorePayResultDTO.builder().result(1).errorMsg("商品没有同步，无法使用会员支付").build();
            }
        }

        return WxStorePayResultDTO.builder().result(1).errorMsg("支付失败").build();
    }


    /**
     * 更新我的订单
     *
     * @param dineinOrderDetailRespDTO 订单
     */
    private void updateMyOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        WxStoreAccountDTO wxStoreAccountDTO = new WxStoreAccountDTO();
        wxStoreAccountDTO.setWxStoreAdvanceConsumerReqDTO(wxStoreAdvanceConsumerReqDTO);
        wxStoreAccountDTO.setDineinOrderDetailRespDTO(dineinOrderDetailRespDTO);
        wxOrderRecordService.update(wxStoreAccountDTO);
    }

    /**
     * 支付下单入参初始化
     *
     * @param
     * @param format
     * @param dineinOrderDetailRespDTO
     * @return
     */
    private SaasAggWeChatPublicAccountPayDTO initialSaasAggWeChatPublicAccountPay(WxH5PayReqDTO wxH5PayReqDTO, String payGuid, String format, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        SaasAggWeChatPublicAccountPayDTO saasAggWeChatPublicAccountPayDTO = new SaasAggWeChatPublicAccountPayDTO();
        AggWeChatPublicAccountPayDTO aggWeChatPublicAccountPayDTO = new AggWeChatPublicAccountPayDTO();
        aggWeChatPublicAccountPayDTO.setRedirectUrl(wxH5PayReqDTO.getOutNotifyUrl());
        aggWeChatPublicAccountPayDTO.setAppId(weChatConfig.getPayAppId());
        aggWeChatPublicAccountPayDTO.setMchntName(weChatConfig.getMchntName());
        aggWeChatPublicAccountPayDTO.setAppSecret(weChatConfig.getAppSecret());
        aggWeChatPublicAccountPayDTO.setStoreName(wxH5PayReqDTO.getWxStoreConsumerDTO().getStoreName());
        aggWeChatPublicAccountPayDTO.setGoodsName(dineinOrderDetailRespDTO.getGuid());
        aggWeChatPublicAccountPayDTO.setBody(dineinOrderDetailRespDTO.getGuid());
        if (StringUtils.isEmpty(wxH5PayReqDTO.getWxStoreConsumerDTO().getEnterpriseName())) {
            wxH5PayReqDTO.getWxStoreConsumerDTO().setEnterpriseName(wxStoreSessionDetailsService.getEnterpriseDetail().getName());
        }
        aggWeChatPublicAccountPayDTO.setEnterpriseName(wxH5PayReqDTO.getWxStoreConsumerDTO().getEnterpriseName());
        aggWeChatPublicAccountPayDTO.setAmount(dineinOrderDetailRespDTO.getActuallyPayFee());
        aggWeChatPublicAccountPayDTO.setOrderGUID(dineinOrderDetailRespDTO.getGuid());
        aggWeChatPublicAccountPayDTO.setPayGUID(payGuid);
        saasAggWeChatPublicAccountPayDTO.setEnterpriseGuid(wxH5PayReqDTO.getWxStoreConsumerDTO().getEnterpriseGuid());
        saasAggWeChatPublicAccountPayDTO.setStoreGuid(wxH5PayReqDTO.getWxStoreConsumerDTO().getStoreGuid());
        saasAggWeChatPublicAccountPayDTO.setDeviceType(12);
        saasAggWeChatPublicAccountPayDTO.setSaasCallBackUrl(format);
        saasAggWeChatPublicAccountPayDTO.setPublicAccountPayDTO(aggWeChatPublicAccountPayDTO);
        return saasAggWeChatPublicAccountPayDTO;
    }

    /**
     * 0元支付
     *
     * @param prepay
     * @param wxStoreConsumerDTO
     * @param calculate
     * @return
     */
    private BillPayReqDTO initialZeroBillPay(WxPrepayReqDTO prepay, WxStoreConsumerDTO wxStoreConsumerDTO, DineinOrderDetailRespDTO calculate) {
        BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setVersion(calculate.getVersion());
        billPayReqDTO.setOrderGuid(calculate.getGuid());
        billPayReqDTO.setFastFood(prepay.getMode() == 1);
        billPayReqDTO.setOrderFee(calculate.getOrderFee());
        billPayReqDTO.setAppendFee(calculate.getAppendFee());
        billPayReqDTO.setActuallyPayFee(calculate.getActuallyPayFee());
        billPayReqDTO.setDiscountFee(calculate.getDiscountFee());
        billPayReqDTO.setChangeFee(calculate.getChangeFee());
        billPayReqDTO.setDiscountFeeDetailDTOS(calculate.getDiscountFeeDetailDTOS());
        BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(calculate.getActuallyPayFee());
        if (!ObjectUtils.isEmpty(prepay.getMemberInfoCardGuid()) && !ObjectUtils.isEmpty(prepay.getVolumeCode())) {
            payment.setPaymentType(10);
            payment.setPaymentTypeName("在线支付");
        } else {
            payment.setPaymentType(4);
            payment.setPaymentTypeName("会员卡支付");
            billPayReqDTO.setMemberInfoCardGuid(prepay.getMemberInfoCardGuid());
            ResponseIntegralOffset integralOffsetResultRespDTO = calculate.getIntegralOffsetResultRespDTO();
            billPayReqDTO.setUseIntegral(!ObjectUtils.isEmpty(integralOffsetResultRespDTO) && !ObjectUtils.isEmpty(integralOffsetResultRespDTO.getUseIntegral()) && integralOffsetResultRespDTO.getUseIntegral() > 0 ? integralOffsetResultRespDTO.getUseIntegral() : null);
            billPayReqDTO.setIntegralDiscountMoney(!ObjectUtils.isEmpty(integralOffsetResultRespDTO) && !ObjectUtils.isEmpty(integralOffsetResultRespDTO.getUseIntegral()) && integralOffsetResultRespDTO.getUseIntegral() > 0 ? integralOffsetResultRespDTO.getDeductionMoney() : null);
        }
        billPayReqDTO.setPayments(Collections.singletonList(payment));

        billPayReqDTO.setOpenId(wxStoreConsumerDTO.getOpenId());
        billPayReqDTO.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
        billPayReqDTO.setStoreGuid(wxStoreConsumerDTO.getStoreGuid());
        billPayReqDTO.setUserGuid(wxStoreConsumerDTO.getOpenId());
        billPayReqDTO.setUserName(wxStoreConsumerDTO.getNickName());
        billPayReqDTO.setDeviceId(wxStoreConsumerDTO.getOpenId());
        billPayReqDTO.setDeviceType(12);

        billPayReqDTO.setVersion(calculate.getVersion());
        log.info("会员支付传参:{}", billPayReqDTO);
        UserContext userContext = UserContextUtils.get();
        userContext.setStoreName(wxStoreConsumerDTO.getStoreName());
        userContext.setUserName(wxStoreConsumerDTO.getNickName());
        userContext.setUserGuid(wxStoreConsumerDTO.getOpenId());
        UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
        log.info("会员支付请求头入参:{}", userContext);
        return billPayReqDTO;
    }

    private CorrectResult<DineinOrderDetailRespDTO> getCorrectResult(WxStoreConsumerDTO wxStoreConsumerDTO) {
        WxPrepayReqDTO prepay = wxStoreSessionDetailsService.getPrepay(wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getOpenId());
        if (ObjectUtils.isEmpty(prepay)) {
            prepay = WxPrepayReqDTO.builder().enterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid()).brandGuid(wxStoreConsumerDTO.getBrandGuid()).storeGuid(wxStoreConsumerDTO.getStoreGuid())
                    .tableGuid(wxStoreConsumerDTO.getDiningTableGuid()).openId(wxStoreConsumerDTO.getOpenId()).build();
        }
        String orderGuid = wxStoreSessionDetailsService.getOrderGuid(wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getDiningTableGuid(), wxStoreConsumerDTO.getOpenId());
        if (ObjectUtils.isEmpty(orderGuid)) {
            log.error("桌台订单不存在,wxStoreConsumerDTO:{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
            return CorrectResult.<DineinOrderDetailRespDTO>builder().errorMsg("订单信息发生变化").build();
        }
        return wxStoreSessionDetailsService.calculateDetails2(orderGuid, wxStoreConsumerDTO.getOpenId(), prepay.getMemberInfoCardGuid(), prepay.getMemberIntegral(), prepay.getVolumeCode());
    }


    @Override
    public WxPayRespDTO weChatPay(WeChatH5PayReqDTO weChatH5PayReqDTO) {
        WxUserInfoDTO wxUserInfoDTO = WeixinUserThreadLocal.getWxUserInfoDTO();
        String orderRecordGuid = weChatH5PayReqDTO.getOrderGuid();
        WxOrderRecordDO orderRecordDO = wxOrderRecordService.getById(orderRecordGuid);
        if (orderRecordDO == null || StringUtils.isEmpty(orderRecordDO.getOrderGuid())) {
            log.error("支付：微信订单异常：{}", JacksonUtils.writeValueAsString(orderRecordDO));
            return WxPayRespDTO.changeFailed();
        }

        Integer orderState = orderRecordDO.getOrderState();
        if (Arrays.asList(2, 3, 6).contains(orderState)) {
            log.error("订单已完结:{}", orderRecordDO);
            return WxPayRespDTO.changeFailed();
        }
        DineinOrderDetailRespDTO calculate = calculateOrderDetail(orderRecordDO, orderRecordDO.getOrderGuid());
        // 订单校验
        WxPayRespDTO wxPayRespDTO = verifyOrder(weChatH5PayReqDTO, calculate);
        if (Objects.nonNull(wxPayRespDTO)) {
            return wxPayRespDTO;
        }
        // 新增运营主体
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(wxUserInfoDTO.getOpenId());
        userMemberSession.setOperSubjectGuid(UserContextUtils.get().getOperSubjectGuid());
        if (orderRecordDO.getOrderMode() == 1) {
            List<ItemInfoDTO> collect = (List<ItemInfoDTO>) redisUtils.get(CacheName.FAST_ESTIMATE
                    + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getOpenId());
            log.info("快餐缓存:{}", collect);
            if (!ObjectUtils.isEmpty(collect)) {
                BaseDTO baseDTO = new BaseDTO();
                baseDTO.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
                baseDTO.setEnterpriseGuid(WeixinUserThreadLocal.getEnterpriseGuid());
                List<ItemEstimateForAndroidRespDTO> estimate = wxStoreEstimateClientService.queryEstimateForSyn(baseDTO);
                List<ItemInfoEstimateVerifyDTO> verifyDTOS = itemEstimateVerify(collect, estimate);
                if (!CollectionUtils.isEmpty(verifyDTOS)) {
                    return WxPayRespDTO.payFailed(0, "部分菜品已售罄");
                }
            }
        }
        wxPayRespDTO = pay(calculate, weChatH5PayReqDTO, orderRecordDO);
        //发起支付后 更新会员信息运营主体
        userMemberSessionUtils.addUserMemberSession(userMemberSession);
        log.info("userMemberSession:{}", JacksonUtils.writeValueAsString(userMemberSession));
        log.info("wxPayRespDTO:{}", wxPayRespDTO);
        return wxPayRespDTO;
    }

    /**
     * 计算订单
     */
    private DineinOrderDetailRespDTO calculateOrderDetail(WxOrderRecordDO orderRecordDO,
                                                          String orderGuid) {
        DineinOrderDetailRespDTO calculate;
        if (Objects.equals(TradeModeEnum.DINEIN.getCode(), orderRecordDO.getOrderMode())) {
            UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
            log.info("[weChatPay]会员支付session:{}", userMemberSession);
            BillCalculateReqDTO billCalculateReqDTO = buildBillCalculateReqDTO(orderGuid, userMemberSession);
            try {
                calculate = wxStoreDineInOrderClientService.calculate(billCalculateReqDTO);
            } catch (Exception e) {
                String message = e.getMessage();
                if (message.contains("优惠券正在使用中")
                        && !org.apache.commons.lang3.StringUtils.isEmpty(billCalculateReqDTO.getVolumeCode())
                        && billCalculateReqDTO.getVolumeCode().length() > 5) {
                    billCalculateReqDTO.setVerify(2);
                    calculate = wxStoreDineInOrderClientService.calculate(billCalculateReqDTO);
                    log.info("[weChatPay]会员支付撤销券结果:{}", calculate);
                }
                if (message.contains("优惠劵已被使用")) {
                    userMemberSession.setVolumeCode("0");
                    userMemberSessionUtils.addUserMemberSession(userMemberSession);
                    return null;
                }
                log.error("[weChatPay]会员支付接口算价异常.memberPayCalculate.billCalculateReqDTO:{}", billCalculateReqDTO, e);
                return null;
            }
        } else {
            calculate = wxStoreDineInOrderClientService.getOrderDetail(orderGuid);
            calculate.setMemberCardGuid(orderRecordDO.getMemberInfoCardGuid());
        }
        return calculate;
    }

    private BillCalculateReqDTO buildBillCalculateReqDTO(String merchantOrderGuid, UserMemberSessionDTO userMemberSession) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        BillCalculateReqDTO reqDTO = new BillCalculateReqDTO();
        reqDTO.setOrderGuid(merchantOrderGuid);
        reqDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        reqDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        reqDTO.setStoreName(wxMemberSessionDTO.getStoreName());
        reqDTO.setUserGuid(WeixinUserThreadLocal.getOpenId());
        reqDTO.setUserName(WeixinUserThreadLocal.getNickName());
        reqDTO.setDeviceId(WeixinUserThreadLocal.getOpenId());
        reqDTO.setMemberLogin(2);
        reqDTO.setDeviceType(12);
        if (!wxMemberSessionDTO.getWxUserInfoDTO().getIsLogin()) {
            log.info("trade计算接口入参:{}", reqDTO);
            return reqDTO;
        }
        String volumeCode = userMemberSession.getVolumeCode();
        String memberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
        Integer enableIntegral = userMemberSession.getMemberIntegral();
        reqDTO.setMemberInfoCardGuid(org.apache.commons.lang3.StringUtils.isEmpty(memberInfoCardGuid) || memberInfoCardGuid.length() < 5 ? null : memberInfoCardGuid);
        reqDTO.setMemberIntegral(enableIntegral != null && enableIntegral == 1 ? 1 : 2);
        reqDTO.setVolumeCode(org.apache.commons.lang3.StringUtils.isEmpty(volumeCode) || volumeCode.length() < 5 ? null : volumeCode);
        reqDTO.setVerify(org.apache.commons.lang3.StringUtils.isEmpty(volumeCode) || volumeCode.length() < 5 ? null : 1);
        reqDTO.setMemberLogin(!org.springframework.util.StringUtils.isEmpty(reqDTO.getMemberInfoCardGuid())
                || !org.apache.commons.lang3.StringUtils.isEmpty(reqDTO.getVolumeCode()) ? 1 : 2);
        reqDTO.setMemberPhone(reqDTO.getMemberLogin() == 1 ? WeixinUserThreadLocal.getOpenId() : null);
        log.info("trade计算接口入参:{}", reqDTO);
        return reqDTO;
    }

    private WxPayRespDTO pay(DineinOrderDetailRespDTO calculate, WeChatH5PayReqDTO weChatH5PayReqDTO, WxOrderRecordDO orderRecordDO) {
        WxPayRespDTO wxPayRespDTO = new WxPayRespDTO();
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        WxUserInfoDTO wxUserInfoDTO = WeixinUserThreadLocal.getWxUserInfoDTO();
        try {
            WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
            wxStoreReqDTO.setStoreGuid(orderRecordDO.getStoreGuid());
            WxOrderConfigDTO wxOrderConfigDTO = wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
            if (ObjectUtils.isEmpty(wxOrderConfigDTO.getIsOnlinePayed()) || wxOrderConfigDTO.getIsOnlinePayed() == 0) {
                wxPayRespDTO.setCouldPay(0);
                wxPayRespDTO.setErrorMsg("该门店暂未开启线上支付功能，请联系商家结账");
            } else {
                SaasAggPayDTO saasAggPayDTO = buildSaasAggPayDTO(weChatH5PayReqDTO, orderRecordDO, calculate,
                        wxUserInfoDTO.getOpenId(), wxOrderConfigDTO);
                log.info("支付下单:{}", JacksonUtils.writeValueAsString(saasAggPayDTO));
                AggPayRespDTO payResp = wxStorePayClientService.pay(saasAggPayDTO);
                log.info("支付下单结果:{}", JacksonUtils.writeValueAsString(payResp));
                wxPayRespDTO.setResult(payResp);
                wxPayRespDTO.setCouldPay(1);

                WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
                weChatPayReqDTO.setDeviceId(wxUserInfoDTO.getOpenId());
                weChatPayReqDTO.setOrderGuid(calculate.getGuid());
                weChatPayReqDTO.setPayGuid(saasAggPayDTO.getReqDTO().getPayGUID());
                weChatPayReqDTO.setActuallyPayFee(weChatH5PayReqDTO.getPayAmount());
                weChatPayReqDTO.setOrderFee(calculate.getOrderFee());
                weChatPayReqDTO.setDiscountFee(calculate.getDiscountFee());
                weChatPayReqDTO.setDiscountFeeDetailDTOS(calculate.getDiscountFeeDetailDTOS());
                weChatPayReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
                weChatPayReqDTO.setDeviceId(wxUserInfoDTO.getOpenId());
                weChatPayReqDTO.setDeviceType(saasAggPayDTO.getDeviceType());
                wrapUserInfo(orderRecordDO.getOrderGuid(), orderRecordDO.getStoreGuid(), wxUserInfoDTO, weChatPayReqDTO);
                weChatPayReqDTO.setStoreName(orderRecordDO.getStoreName());
                weChatPayReqDTO.setStoreGuid(orderRecordDO.getStoreGuid());
                weChatPayReqDTO.setSuccess(true);
                log.info("支付下单dine缓存:{}", calculate.getGuid());
                wxStoreSessionDetailsService.savePayCallBack(calculate.getGuid(), weChatPayReqDTO);
            }
            OrderLockDTO orderLockDto = new OrderLockDTO();
            orderLockDto.setOrderGuid(calculate.getGuid());
            orderLockDto.setDeviceId(wxUserInfoDTO.getOpenId());
            wxStoreDineInOrderClientService.lockorder(orderLockDto);
            if (wxOrderConfigDTO.getOrderModel() == 0) {
                redisUtils.delete(CacheName.USER_COUNT + ":" + wxMemberSessionDTO.getDiningTableGuid());
                redisUtils.delete("autoAccept:" + wxMemberSessionDTO.getDiningTableGuid());
            }
        } catch (Exception e) {
            log.error("支付异常:{}", e.getMessage());
            wxPayRespDTO.setCouldPay(0);
            wxPayRespDTO.setErrorMsg("该门店暂未开启线上支付功能，请联系商家结账");
        }
        return wxPayRespDTO;
    }

    /**
     * 封装结账操作人信息
     *
     * @param orderGuid       订单guid
     * @param wxUserInfoDTO   微信用户信息
     * @param weChatPayReqDTO 微信支付请求对象
     */
    private void wrapUserInfo(String orderGuid, String storeGuid, WxUserInfoDTO wxUserInfoDTO, WeChatPayReqDTO weChatPayReqDTO) {
        //查询是否为特殊二维码（二维码关联员工信息）
        UserBriefDTO userBriefDTO = (UserBriefDTO) redisUtils.get("WX:TABLE:STAFF:" + WeixinUserThreadLocal.getEnterpriseGuid() + ":" + orderGuid);
        if (Objects.nonNull(userBriefDTO)) {
            weChatPayReqDTO.setUserName(userBriefDTO.getUserName());
            weChatPayReqDTO.setUserGuid(userBriefDTO.getUserGuid());
            return;
        }
        // 非特殊二维码 需要查询当班员工
        UserBriefDTO onDutyUserBrief = queryOnDutyStaff(storeGuid);
        if (Objects.nonNull(onDutyUserBrief)) {
            weChatPayReqDTO.setUserName(onDutyUserBrief.getUserName());
            weChatPayReqDTO.setUserGuid(onDutyUserBrief.getUserGuid());
            return;
        }
        weChatPayReqDTO.setUserName(wxUserInfoDTO.getNickname());
        weChatPayReqDTO.setUserGuid(wxUserInfoDTO.getOpenId());
    }

    /**
     * 查询当班员工
     */
    private UserBriefDTO queryOnDutyStaff(String storeGuid) {
        // 查询当班所有员工
        List<HandoverRecordDTO> handoverRecordList = businessClientService.queryOnDutyStaffs(storeGuid);
        log.info("查询到当前门店当班员工结果,门店guid:{},未交班列表:{}", storeGuid, JacksonUtils.writeValueAsString(handoverRecordList));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(handoverRecordList)) {
            return null;
        }
        // 查询主机设备
        StoreDeviceDTO masterDeviceByStoreGuid = organizationClientService.findMasterDevice(storeGuid);
        if (Objects.nonNull(masterDeviceByStoreGuid)) {
            log.info("查询到当前门店主机设备信息, 门店guid:{}, 主机信息:{}", storeGuid, JacksonUtils.writeValueAsString(masterDeviceByStoreGuid));
            handoverRecordList = handoverRecordList.stream().filter(e -> e.getTerminalId().equals(masterDeviceByStoreGuid.getDeviceNo())).collect(Collectors.toList());
        }
        // 主机当班人员未找到，则找其他设备的当班人员
        HandoverRecordDTO randomHandoverRecord = getRandomHandoverRecord(handoverRecordList);
        return copyProperties(randomHandoverRecord);
    }

    /**
     * 当班员工列表中随机获取一位员工
     */
    private HandoverRecordDTO getRandomHandoverRecord(List<HandoverRecordDTO> handoverRecordList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(handoverRecordList)) {
            return null;
        }
        return handoverRecordList.get(new Random().nextInt(handoverRecordList.size()));
    }

    private UserBriefDTO copyProperties(HandoverRecordDTO handoverRecordDTO) {
        if (Objects.isNull(handoverRecordDTO)) {
            return null;
        }
        UserBriefDTO userBriefDTO = new UserBriefDTO();
        userBriefDTO.setUserGuid(handoverRecordDTO.getCreateGuid());
        userBriefDTO.setUserName(handoverRecordDTO.getCreateName());
        return userBriefDTO;
    }

    /**
     * 规格选中
     *
     * @param itemInfoDTO 商品
     * @return 规格
     */
    private ItemInfoSkuDTO getUckSku(ItemInfoDTO itemInfoDTO) {
        List<ItemInfoSkuDTO> skuList = itemInfoDTO.getSkuList();
        Assert.isTrue(!ObjectUtils.isEmpty(skuList), "规格不能为空");
        if (skuList.size() == 1) {
            return skuList.get(0);
        }
        Optional<ItemInfoSkuDTO> first = skuList.stream().filter(x -> x.getUck() == 1).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        log.error("商品:{}", JacksonUtils.writeValueAsString(itemInfoDTO));
        throw new RuntimeException("规格必选");
    }

    private List<ItemInfoEstimateVerifyDTO> itemEstimateVerify(List<ItemInfoDTO> itemInfoDTOS, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        log.info("估清校验:{}", estimateSkuList);
        List<ItemInfoEstimateVerifyDTO> verifyDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemInfoDTOS) || CollectionUtils.isEmpty(estimateSkuList)) {
            return verifyDTOS;
        }
        for (ItemEstimateForAndroidRespDTO estimate : estimateSkuList) {
            for (ItemInfoDTO itemInfoDTO : itemInfoDTOS) {
                ItemInfoSkuDTO uckSku = getUckSku(itemInfoDTO);
                if (estimate.getSkuGuid().equals(uckSku.getSkuGuid())) {
                    log.info("下单：估清匹配商品:{},规格:{}", itemInfoDTO, estimate);
                    if (estimate.getIsSoldOut() == 1 && estimate.getResidueQuantity().compareTo(itemInfoDTO.getCurrentCount()) < 0) {
                        ItemInfoEstimateVerifyDTO itemInfoEstimateVerifyDTO = new ItemInfoEstimateVerifyDTO(itemInfoDTO.getName(), uckSku.getSkuGuid()
                                , uckSku.getName(), uckSku.getUnit(), false, estimate.getResidueQuantity());
                        log.info("余量匹配:{}", itemInfoEstimateVerifyDTO);
                        verifyDTOS.add(itemInfoEstimateVerifyDTO);
                    } else if (estimate.getIsSoldOut() == 2) {
                        ItemInfoEstimateVerifyDTO itemInfoEstimateVerifyDTO = new ItemInfoEstimateVerifyDTO(itemInfoDTO.getName(), uckSku.getSkuGuid()
                                , uckSku.getName(), uckSku.getUnit(), true, null);
                        log.info("售尽匹配:{}", itemInfoEstimateVerifyDTO);
                        verifyDTOS.add(itemInfoEstimateVerifyDTO);
                    }
                }
                List<ItemInfoSubgroupDTO> subgroupList = itemInfoDTO.getSubgroupList();
                if (!CollectionUtils.isEmpty(subgroupList)) {
                    for (ItemInfoSubgroupDTO itemInfoSubgroupDTO : subgroupList) {
                        Integer pickNum = itemInfoSubgroupDTO.getPickNum();
                        List<ItemInfoSubSkuDTO> subItemSkuList = itemInfoSubgroupDTO.getSubItemSkuList();
                        if (!CollectionUtils.isEmpty(subItemSkuList)) {
                            for (ItemInfoSubSkuDTO itemInfoSubSkuDTO : subItemSkuList) {
                                Integer defaultNum = itemInfoSubSkuDTO.getDefaultNum();
                                if (pickNum == 0 || defaultNum > 0) {
                                    if (estimate.getSkuGuid().equals(itemInfoSubSkuDTO.getSkuGuid())) {
                                        log.info("下单：估清匹配套餐:{}", itemInfoSubSkuDTO);
                                        BigDecimal current = new BigDecimal(defaultNum).multiply(itemInfoSubSkuDTO.getItemNum()).multiply(itemInfoDTO.getCurrentCount());
                                        if (estimate.getIsSoldOut() == 1 && estimate.getResidueQuantity().compareTo(current) < 0) {
                                            ItemInfoEstimateVerifyDTO itemInfoEstimateVerifyDTO =
                                                    new ItemInfoEstimateVerifyDTO(itemInfoSubSkuDTO.getItemName(), itemInfoSubSkuDTO.getSkuGuid()
                                                            , itemInfoSubSkuDTO.getSkuName(), itemInfoSubSkuDTO.getUnit(), false, estimate.getResidueQuantity());
                                            log.info("余量匹配:{}", itemInfoEstimateVerifyDTO);
                                            verifyDTOS.add(itemInfoEstimateVerifyDTO);
                                        } else if (estimate.getIsSoldOut() == 2) {
                                            ItemInfoEstimateVerifyDTO itemInfoEstimateVerifyDTO =
                                                    new ItemInfoEstimateVerifyDTO(itemInfoSubSkuDTO.getItemName(), itemInfoSubSkuDTO.getSkuGuid()
                                                            , itemInfoSubSkuDTO.getSkuName(), itemInfoSubSkuDTO.getUnit(), true, null);
                                            log.info("售尽匹配:{}", itemInfoEstimateVerifyDTO);
                                            verifyDTOS.add(itemInfoEstimateVerifyDTO);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return verifyDTOS;
    }

    private SaasAggPayDTO buildSaasAggPayDTO(WeChatH5PayReqDTO weChatH5PayReqDTO, WxOrderRecordDO orderRecordDO,
                                             DineinOrderDetailRespDTO calculate, String openId, WxOrderConfigDTO wxOrderConfigDTO) {
        String payGuid = redisUtils.generateGuid("hstTransactionRecord");
        String callBackUrl = String.format(WECHAT_CALLBACK, calculate.getStoreGuid(), openId, UserContextUtils.get().getAllianceId());
        String appId = mpAppId;
        AggPayPreTradingReqDTO aggWeChatPublicAccountPayDTO = new AggPayPreTradingReqDTO();
        aggWeChatPublicAccountPayDTO.setStoreName(orderRecordDO.getStoreName());
        aggWeChatPublicAccountPayDTO.setGoodsName(calculate.getOrderNo());
        aggWeChatPublicAccountPayDTO.setBody(calculate.getOrderNo());
        aggWeChatPublicAccountPayDTO.setPayPowerId(PayPowerId.YL_WX_PUBLIC_NO.getId());
        if (Objects.nonNull(weChatH5PayReqDTO.getDeviceType()) && BaseDeviceTypeEnum.TCD.getCode() == weChatH5PayReqDTO.getDeviceType()) {
            aggWeChatPublicAccountPayDTO.setPayPowerId(PayPowerId.WX_MINI_PROGRAM_ONLINE.getId());
            aggWeChatPublicAccountPayDTO.setTerminalId(payGuid);
            appId = weChatH5PayReqDTO.getAppId();
        }
        if (Objects.nonNull(weChatH5PayReqDTO.getDeviceType()) && BaseDeviceTypeEnum.ALI.getCode() == weChatH5PayReqDTO.getDeviceType()) {
            aggWeChatPublicAccountPayDTO.setPayPowerId(PayPowerId.ALI_YL_APPLET.getId());
            aggWeChatPublicAccountPayDTO.setTerminalId(payGuid);
            appId = weChatH5PayReqDTO.getAppId();
        }
        if (Objects.nonNull(weChatH5PayReqDTO.getDeviceType()) && BaseDeviceTypeEnum.WECHAT.getCode() == weChatH5PayReqDTO.getDeviceType()) {
            // 如果是麓豆企业 并且授权了自己公众号则覆盖入参
            WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = queryLudouAuthorizerInfo(orderRecordDO);
            if (Objects.nonNull(wxStoreAuthorizerInfoDO)) {
                String redisKey = String.format(RedisConstants.H5_WECHAT_OPENID_RELATION_KEY, orderRecordDO.getBrandGuid(), openId);
                String myselfOpenId = (String) redisUtils.get(redisKey);
                log.info("替换openId,old:{}, new:{}", openId, myselfOpenId);
                openId = Optional.ofNullable(myselfOpenId).orElse(openId);
                appId = wxStoreAuthorizerInfoDO.getAuthorizerAppid();
            }
        }
        if (!StringUtils.isEmpty(weChatH5PayReqDTO.getThirdAppId())) {
            appId = weChatH5PayReqDTO.getThirdAppId();
            openId = weChatH5PayReqDTO.getThirdOpenId();
            log.info("使用外部传入的appId和openId, appId:{}, openId:{}", appId, openId);
            aggWeChatPublicAccountPayDTO.setPayPowerId(PayPowerId.WX_MINI_PROGRAM_ONLINE.getId());
            aggWeChatPublicAccountPayDTO.setTerminalId(payGuid);
        }

        aggWeChatPublicAccountPayDTO.setEnterpriseName(wxStoreSessionDetailsService.getEnterpriseDetail().getName());
        aggWeChatPublicAccountPayDTO.setAmount(weChatH5PayReqDTO.getPayAmount());
        aggWeChatPublicAccountPayDTO.setOrderGUID(calculate.getGuid());
        aggWeChatPublicAccountPayDTO.setPayGUID(payGuid);

        aggWeChatPublicAccountPayDTO.setSubAppId(appId);
        aggWeChatPublicAccountPayDTO.setSubOpenId(openId);
        aggWeChatPublicAccountPayDTO.setClientIp(weChatH5PayReqDTO.getClientIp());
        SaasAggPayDTO saasAggPayDTO = new SaasAggPayDTO();
        saasAggPayDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        saasAggPayDTO.setStoreGuid(orderRecordDO.getStoreGuid());
        // 操作设备类型
        saasAggPayDTO.setDeviceType(Optional.ofNullable(weChatH5PayReqDTO.getDeviceType()).orElse(BaseDeviceTypeEnum.WECHAT.getCode()));
        saasAggPayDTO.setIsLast(true);
        saasAggPayDTO.setSaasCallBackUrl(callBackUrl);
        saasAggPayDTO.setReqDTO(aggWeChatPublicAccountPayDTO);
        // 是否结账不清台
        saasAggPayDTO.setCloseTableFlag(queryEnableHandleClose(weChatH5PayReqDTO, wxOrderConfigDTO)
                ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        return saasAggPayDTO;
    }

    private WxStoreAuthorizerInfoDO queryLudouAuthorizerInfo(WxOrderRecordDO orderRecordDO) {
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        EnterpriseDTO enterprise = enterpriseClientService.findEnterprise(baseDTO);
        Boolean supportLudouPayFlag = enterprise.getSupportLudouPayFlag();
        // 查询企业是否是麓豆企业
        if (!Boolean.TRUE.equals(supportLudouPayFlag)) {
            return null;
        }
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getByBrandId(orderRecordDO.getBrandGuid());
        if (Objects.nonNull(wxStoreAuthorizerInfoDO) && Objects.nonNull(wxStoreAuthorizerInfoDO.getAuthorizerAppid())) {
            return wxStoreAuthorizerInfoDO;
        }
        return null;
    }

    /**
     * 是否配置结账不清台
     */
    private boolean queryEnableHandleClose(WeChatH5PayReqDTO weChatH5PayReqDTO, WxOrderConfigDTO wxOrderConfigDTO) {
        if (wxOrderConfigDTO.getOrderModel() == 1) {
            return false;
        }
        if (!Objects.equals(BaseDeviceTypeEnum.WECHAT.getCode(), weChatH5PayReqDTO.getDeviceType())) {
            return false;
        }
        String storeGuid = WeixinUserThreadLocal.getStoreGuid();
        WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
        wxStoreReqDTO.setStoreGuid(storeGuid);
        log.info("查询微信门店配置:{}", wxOrderConfigDTO);
        if (Objects.nonNull(wxOrderConfigDTO.getIsCheckoutUnCloseTable())
                && wxOrderConfigDTO.getIsCheckoutUnCloseTable() == 1) {
            return true;
        }
        // 如果扫码点餐没有开启结账不清台，则需要查询门店一体机配置
        StoreConfigQueryDTO storeConfigQueryDTO = new StoreConfigQueryDTO();
        storeConfigQueryDTO.setStoreGuid(storeGuid);
        DineFoodSettingRespDTO dineFoodSettingRespDTO = businessClientService.queryDineFoodSetting(storeConfigQueryDTO);
        log.info("查询一体机正餐配置:{}", dineFoodSettingRespDTO);
        return Objects.nonNull(dineFoodSettingRespDTO) && Objects.nonNull(dineFoodSettingRespDTO.getEnableHandleClose())
                && dineFoodSettingRespDTO.getEnableHandleClose() == 1;
    }

    /**
     * 订算订单优惠并验券
     *
     * @return 支付结果
     */
//	private CorrectResult<DineinOrderDetailRespDTO> verifyVolume(WxOrderRecordDO orderRecordDO, WxMemberSessionDTO wxMemberSessionDTO, WxUserInfoDTO wxUserInfoDTO) {
//		String openId = wxUserInfoDTO.getOpenId();
//
//
//		DineinOrderDetailRespDTO calculate = wxStoreDineInBillClientService.calculate(billCalculateReqDTO);
//		if (calculate == null || calculate.getState() != 1) {
//			log.error("计算订单优惠失败:{}", billCalculateReqDTO);
//			log.error("计算订单优惠失败header:{}", UserContextUtils.get());
//			log.error("订单已完结:{}", calculate);
//			return CorrectResult.changeFailed();
//		}
//		if (!StringUtils.isEmpty(volumeCode) && volumeCode.length() > 5 && !volumeVerify) {
//			userMemberSession.setVolumeVerify(true);
//		}
//		userMemberSession.setIsLogin(WeixinUserThreadLocal.getIsLogin());
//		List<DiscountFeeDetailDTO> discountFeeDetailDTOS = calculate.getDiscountFeeDetailDTOS();
//		if (!ObjectUtils.isEmpty(discountFeeDetailDTOS)) {
//			BigDecimal unMemberFee = discountFeeDetailDTOS.stream().filter(x -> Arrays.asList(1, 8, 9).contains(x.getDiscountType()))
//					.map(x -> Optional.ofNullable(x.getDiscountFee()).orElse(BigDecimal.ZERO))
//					.reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//			if (unMemberFee.intValue() > 0) {
//				userMemberSession.setUnMemberFee(unMemberFee);
//			}
//		}
//		userMemberSessionUtils.addUserMemberSession(userMemberSession);
//		return new CorrectResult<DineinOrderDetailRespDTO>().setResult(0).setObj(calculate);
//	}

    /**
     * 手动切换数据源
     *
     * @param wxStoreCallbackNotifyDTO 支付回调dto
     * @param storeGuid                门店id
     */
    private void dynamicDataSource(WxPayCallbackDTO wxStoreCallbackNotifyDTO, String storeGuid, UserMemberSessionDTO userMemberSession) {
        UserContext userContext = UserContextUtils.get();
        if (ObjectUtils.isEmpty(userContext)) {
            userContext = new UserContext();
        }
        userContext.setEnterpriseGuid(wxStoreCallbackNotifyDTO.getSaasNotifyDTO().getBaseInfo().getEnterpriseGuid());
        userContext.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
        userContext.setStoreGuid(storeGuid);
        userContext.setOperSubjectGuid(userMemberSession.getOperSubjectGuid());
        UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
        EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
    }


    /**
     * 支付入参 校验金额
     */
    private boolean verifyPayAmount(DineinOrderDetailRespDTO calculate, WeChatH5PayReqDTO weChatH5PayReqDTO) {
        // 传入的支付金额
        BigDecimal payAmount = weChatH5PayReqDTO.getPayAmount();
        BigDecimal orderFee = calculate.getOrderFee();
        BigDecimal discountFee = Optional.ofNullable(calculate.getDiscountFee()).orElse(BigDecimal.ZERO);
        return payAmount.compareTo(orderFee.subtract(discountFee)) != 0;
    }

    /**
     * 订单校验
     */
    private WxPayRespDTO verifyOrder(WeChatH5PayReqDTO weChatH5PayReqDTO, DineinOrderDetailRespDTO calculate) {
        if (Objects.isNull(calculate)) {
            log.error("微信支付失败, calculate is null");
            return WxPayRespDTO.payAmountFailed();
        }
        log.info("支付订单详情:{}", JacksonUtils.writeValueAsString(calculate));
        if (Boolean.TRUE.equals(calculate.getIsMultipleAggPay())) {
            log.error("微信支付失败, calculate is null");
            return WxPayRespDTO.payFailed(1, "该订单已支付部分金额，请前往收银台结账，谢谢！");
        }
        // 支付入参 校验金额
        if (verifyPayAmount(calculate, weChatH5PayReqDTO)) {
            return WxPayRespDTO.payAmountFailed();
        }
        // 校验订单会员信息
        verifyOrderMemberInfo(calculate);
        return null;
    }


    /**
     * 校验订单会员信息
     */
    private void verifyOrderMemberInfo(DineinOrderDetailRespDTO calculate) {
        // 当前订单上的memberGuid
        String memberGuid = calculate.getMemberGuid();
        if (!StringUtils.isEmpty(memberGuid) && !"0".equals(memberGuid)) {
            return;
        }
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        String memberInfoGuid = wxMemberSessionDTO.getMemberInfoGuid();
        if (StringUtils.isEmpty(memberInfoGuid) || "0".equals(memberInfoGuid)) {
            return;
        }
        if (!memberGuid.equals(memberInfoGuid)) {
            UpdateOrderMemberInfoReqDTO reqDTO = new UpdateOrderMemberInfoReqDTO();
            reqDTO.setOrderGuid(calculate.getGuid());
            reqDTO.setMemberGuid(memberInfoGuid);
            reqDTO.setMemberPhone(wxMemberSessionDTO.getPhoneNum());
            reqDTO.setMemberName(Optional.ofNullable(wxMemberSessionDTO.getWxUserInfoDTO()).orElse(new WxUserInfoDTO()).getNickname());
            log.info("更新订单会员信息, reqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
            wxStoreDineInOrderClientService.updateOrderMemberInfo(reqDTO);
        }
    }
}
