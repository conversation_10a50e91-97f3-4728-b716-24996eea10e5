package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.WxStickShopCartDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickShopCartRemoveDTO;
import com.holderzone.saas.store.weixin.service.WxStickShopCartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickShopCartController
 * @date 2019/03/12 14:37
 * @description 微信桌贴购物车controller
 * @program holder-saas-store
 */
@RestController
@RequestMapping("/wx_stick_shop_cart")
@Slf4j
@Api(description = "微信桌贴购物车controller")
public class WxStickShopCartController {

    @Autowired
    WxStickShopCartService wxStickShopCartService;

    @PostMapping("/list_shop_cart")
    @ApiOperation("查询购物车列表")
    public List<WxStickShopCartDTO> listShopCart() {
        return wxStickShopCartService.listShopCart();
    }

    @PostMapping("/add_models")
    @ApiOperation("购物车批量添加")
    public void addModels(@RequestBody List<WxStickShopCartDTO> shopCartDTOList) {
        wxStickShopCartService.addModels(shopCartDTOList);

    }

    @PostMapping("/remove_models")
    @ApiOperation("购物车批量删除")
    public void removeModels(@RequestBody WxStickShopCartRemoveDTO wxStickShopCartRemoveDTO) {
        wxStickShopCartService.removeModels(wxStickShopCartRemoveDTO);
    }
}
