package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQrCodeInfoDO
 * @date 2019/03/22 11:48
 * @description 微信二维码对应参数表
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("hsw_weixin_qr_code_info")
public class WxQrCodeInfoDO {

    private Long id;
    /**
     * guid
     */
    @TableId("guid")
    private String guid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名字
     */
    private String storeName;

    /**
     * 区域guid
     */
    private String areaGuid;

    /**
     * 区域名字
     */
    private String areaName;

    /**
     * 桌台guid
     */
    private String tableGuid;

    /**
     * 桌台名字
     */
    private String tableName;

    private String brandGuid;

    /***
     *  二维码类型:0普通二维码，1带参数二维码 10 kbz
     *  //todo 微信和kbz的码重复？
     */
    private Integer qrCodeType;

    /***
     *  当前品牌对应的appId
     */
    private String appId;
}
