package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreConfigMapstruct
 * @date 2019/02/14 15:59
 * @description
 * @program holder-saas-store-weixin
 */
@Mapper(componentModel = "spring")
@Component
public interface WxStoreConfigMapstruct {

    @Mappings({
            @Mapping(target = "supportGrouponTypes", expression = "java(wxOrderConfigDO.getSupportGrouponTypes() != null ? com.holderzone.framework.util.JacksonUtils.toObjectList(String.class, wxOrderConfigDO.getSupportGrouponTypes()) : com.google.common.collect.Lists.newArrayList())"),
    })
    WxOrderConfigDTO orderConfigDO2OrderConfigRespDTO(WxOrderConfigDO wxOrderConfigDO);

    @Mappings({
            @Mapping(target = "supportGrouponTypes", expression = "java(wxOrderConfigDTO.getSupportGrouponTypes() != null ? com.holderzone.framework.util.JacksonUtils.writeValueAsString(wxOrderConfigDTO.getSupportGrouponTypes()) : null)"),
    })
    WxOrderConfigDO updateDTO2WxStoreOrderConfigDO(WxOrderConfigDTO wxOrderConfigDTO);

}
