package com.holderzone.saas.store.weixin.aop;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOperationDTO;
import com.holderzone.saas.store.weixin.annotation.OrderToken;

import lombok.extern.slf4j.Slf4j;

@Aspect
@Component
@Slf4j
public class OrderTokenAspect {

	private final StandardEvaluationContext standardEvaluationContext;

	private final SpelExpressionParser spelExpressionParser;

	private final LocalVariableTableParameterNameDiscoverer localVariableTableParameterNameDiscoverer;
	@Autowired
	private RedisUtils redisUtils;

	public OrderTokenAspect(StandardEvaluationContext standardEvaluationContext, SpelExpressionParser spelExpressionParser, LocalVariableTableParameterNameDiscoverer localVariableTableParameterNameDiscoverer) {
		this.standardEvaluationContext = standardEvaluationContext;
		this.spelExpressionParser = spelExpressionParser;
		this.localVariableTableParameterNameDiscoverer = localVariableTableParameterNameDiscoverer;
	}

	@Around("@annotation(orderToken)")
	public WxStoreMerchantOperationDTO around(ProceedingJoinPoint proceedingJoinPoint, OrderToken orderToken) {
		String orderGuid = null;
		try {
			log.info("接单---------------------------------------------------------------------------------");
			String orderGuidAnno = orderToken.orderGuid();
			MethodSignature signature = (MethodSignature)proceedingJoinPoint.getSignature();
			String[] parameterNames = localVariableTableParameterNameDiscoverer.getParameterNames(signature.getMethod());
			Object[] args = proceedingJoinPoint.getArgs();
			for(int i=0;i<parameterNames.length;i++) {
				standardEvaluationContext.setVariable(parameterNames[i],args[i]);
			}
			orderGuid = spelExpressionParser.parseExpression(orderGuidAnno).getValue(standardEvaluationContext, String.class);
			UserContext userContext = UserContextUtils.get();
			if (ObjectUtils.isEmpty(userContext) || StringUtils.isEmpty(userContext.getEnterpriseGuid())||StringUtils.isEmpty(userContext.getStoreGuid())) {
				log.error("接单请求头:{}",userContext);
				return WxStoreMerchantOperationDTO.builder().errorMsg("请求头不能为空").build();
			}
			Boolean token1 = redisUtils.setNx(orderGuid, "token", 3);
			if (!token1) {
				return null;
			}
			return (WxStoreMerchantOperationDTO) proceedingJoinPoint.proceed();
		} catch (Exception e) {
			e.printStackTrace();
		} catch (Throwable throwable) {
			throwable.printStackTrace();
		}
		return WxStoreMerchantOperationDTO.builder().errorMsg("接单失败").build();
	}
}
