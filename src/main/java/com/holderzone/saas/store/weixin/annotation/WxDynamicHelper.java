package com.holderzone.saas.store.weixin.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxDynamicHelper
 * @date 2019/4/19
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface WxDynamicHelper {
	String value();
}
