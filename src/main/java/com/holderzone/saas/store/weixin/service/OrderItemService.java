package com.holderzone.saas.store.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.ConfirmConfigTaskDTO;
import com.holderzone.saas.store.dto.weixin.deal.OrderSubmitReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.OrderSubmitRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderItemDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemService
 * @date 2019/11/11
 * 下单商品类
 */
public interface OrderItemService extends IService<WxOrderItemDO> {

    OrderSubmitRespDTO submit(OrderSubmitReqDTO orderSubmitReqDTO);

    Boolean enableKeyboard();

    OrderSubmitRespDTO submitDine(OrderSubmitReqDTO orderSubmitReqDTO);

    /**
     * 将桌台和员工关联信息存Redis
     *
     * @param orderGuid       订单guid
     * @param diningTableGuid 桌台guid
     */
    void setTableStaffInfo(String orderGuid, String diningTableGuid);

    /**
     * 功能描述：门店配置延迟处理订单确认和提示
     *
     * @param taskDTO 延时任务处理实体
     * @date 2021/4/14
     */
    void dealRedisDelayedTask(ConfirmConfigTaskDTO taskDTO);

    OrderSubmitRespDTO submitFast(OrderSubmitReqDTO orderSubmitReqDTO);

    List<DineInItemDTO> getItemListByMerchantGuid(String orderRecordGuid);

    /**
     * 通过订单guid查询商品
     */
    List<DineInItemDTO> getItemListByOrderGuid(String orderGuid);
}
