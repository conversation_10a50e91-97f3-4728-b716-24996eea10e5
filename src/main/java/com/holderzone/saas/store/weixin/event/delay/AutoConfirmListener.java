package com.holderzone.saas.store.weixin.event.delay;

import com.holderzone.saas.store.dto.weixin.deal.ConfirmConfigTaskDTO;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 自动确认监听类
 * @date 2021/4/14
 */
@Component
@Slf4j
public class AutoConfirmListener implements RedisDelayedQueueListener<ConfirmConfigTaskDTO>{

    @Autowired
    OrderItemService orderItemService;

    @Override
    public void invoke(ConfirmConfigTaskDTO taskDTO) {
        log.info("处理延时队列数据：{}",taskDTO);
        orderItemService.dealRedisDelayedTask(taskDTO);
    }
}
