package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/9/16 17:58
 * @description 第三方会员信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("hsw_weixin_third_part_user_info")
public class WxThirdPartUserInfo {

    @TableId(value = "guid",type= IdType.INPUT)
    private String guid;
    /***
     * 昵称
     */
    private String nickName;

    /***
     *第三方openId
     */
    private String openId;

    /***
     *手机号
     */
    private String phone;

    /***
     *用户来源：翼惠天下:13
     */
    private Integer source;

    /***
     *密码
     */
    private String password;


    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
