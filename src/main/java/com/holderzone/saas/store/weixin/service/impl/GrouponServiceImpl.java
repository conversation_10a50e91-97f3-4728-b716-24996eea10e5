package com.holderzone.saas.store.weixin.service.impl;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoDTO;
import com.holderzone.saas.store.dto.weixin.deal.ShopCartItemReqDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.order.RuleTypeEnum;
import com.holderzone.saas.store.weixin.service.GrouponService;
import com.holderzone.saas.store.weixin.service.rpc.GroupClientService;
import com.holderzone.saas.store.weixin.service.rpc.ThirdActivityClientService;
import com.holderzone.saas.store.weixin.service.rpc.TradeClientService;
import com.holderzone.saas.store.weixin.service.rpc.TradeThirdActivityClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class GrouponServiceImpl implements GrouponService {

    private final TradeClientService tradeClientService;

    private final GroupClientService groupClientService;

    private final TradeThirdActivityClientService tradeThirdActivityClientService;

    private final ThirdActivityClientService thirdActivityClientService;

    private static final ScheduledExecutorService UNDO_COUPON_EXECUTOR_SERVICE = Executors.newScheduledThreadPool(5, new ThreadFactoryBuilder()
            .setNameFormat("再次撤销团购验券异步线程-%d")
            .build());

    /**
     * 团购验券
     * 属于加菜中 执行验券，和第三方活动强关联
     * 一次只验一种类型的券
     */
    @Override
    public void grouponDoCheck(String orderGuid, String orderRecordGuid, List<ShopCartItemReqDTO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        // 过滤使用团购验券的商品
        List<ItemInfoDTO> itemInfoList = itemList.stream()
                .map(ShopCartItemReqDTO::getItemInfoDTO)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemInfoList)) {
            return;
        }
        List<ItemInfoDTO> useCouponItemInfoList = itemInfoList.stream()
                .filter(e -> Objects.nonNull(e.getCouponPreRespDTO()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(useCouponItemInfoList)) {
            return;
        }
        log.info("团购验券商品:{}", JacksonUtils.writeValueAsString(useCouponItemInfoList));
        // 校验第三方活动
        List<MtCouponPreRespDTO> mtCouponPreRespDTOByItemInfo = getMtCouponPreRespDTOByItemInfo(useCouponItemInfoList);
        preCheckParamVerify(orderGuid, mtCouponPreRespDTOByItemInfo);
        // 根据不同平台批量验券
        Map<Integer, List<ItemInfoDTO>> useCouponItemInfoMap = useCouponItemInfoList.stream()
                .collect(Collectors.groupingBy(e -> e.getCouponPreRespDTO().getGroupBuyType()));
        List<GroupVerifyDTO> results = Lists.newArrayList();
        for (Map.Entry<Integer, List<ItemInfoDTO>> entry : useCouponItemInfoMap.entrySet()) {
            List<ItemInfoDTO> innerUseCouponItemInfoList = entry.getValue();
            Map<String, List<ItemInfoDTO>> innerUseCouponItemInfoMap = innerUseCouponItemInfoList.stream()
                    .collect(Collectors.groupingBy(e -> e.getCouponPreRespDTO().getCouponCode()));
            for (Map.Entry<String, List<ItemInfoDTO>> listEntry : innerUseCouponItemInfoMap.entrySet()) {
                // 验券
                List<GroupVerifyDTO> verifyResults = verifyCoupon(orderGuid, orderRecordGuid, listEntry.getValue());
                if (CollectionUtils.isNotEmpty(verifyResults)) {
                    results.addAll(verifyResults);
                }
            }
        }
        if (results.size() != useCouponItemInfoList.size()) {
            log.error("验券失败,验券数量和成功返回数量不一致:{}", JacksonUtils.writeValueAsString(results));
            // 撤销验券
            revokeGroupon(useCouponItemInfoList);
            throw new BusinessException("验券失败，请重新进入验券页面再次验券");
        }
    }


    /**
     * 验券 规则校验
     */
    @Override
    public void preCheckParamVerify(String orderGuid, List<MtCouponPreRespDTO> mtCouponPreRespDTOByItemInfo) {
        List<ThirdActivityRecordDTO> usedThirdActivityRecordList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(orderGuid)) {
            // 无论先使用第三方活动的券 还是 直接使用团购验券 都要校验订单上已验的券
            // 查询订单上已使用的第三方活动
            usedThirdActivityRecordList = tradeThirdActivityClientService.listThirdActivityByOrderGuid(orderGuid);
            log.info("查询订单上已使用的第三方活动信息,orderGuid:{},活动明细:{}", orderGuid, JacksonUtils.writeValueAsString(usedThirdActivityRecordList));
        }
        // 获取待提交的商品中验券商品对应的第三方活动
        List<ThirdActivityRespDTO> shopCartThirdActivityList = queryShopCartThirdActivityList(mtCouponPreRespDTOByItemInfo);
        // 校验共享互斥规则
        checkThirdActivityShareRule(orderGuid, shopCartThirdActivityList, usedThirdActivityRecordList);
        // 校验使用上限
        checkThirdActivityLimitRule(mtCouponPreRespDTOByItemInfo, shopCartThirdActivityList, usedThirdActivityRecordList);
    }


    private List<GroupVerifyDTO> verifyCoupon(String orderGuid, String orderRecordGuid, List<ItemInfoDTO> innerUseCouponItemInfoList) {
        // 构建验券请求参数
        CouPonReqDTO couPonReqDTO = buildCouPonReqDTO(orderGuid, orderRecordGuid, innerUseCouponItemInfoList);
        // 执行验券
        try {
            List<GroupVerifyDTO> verifyResults = groupClientService.verifyCoupon(couPonReqDTO);
            log.info("执行团购验券完成,results:{}", JacksonUtils.writeValueAsString(verifyResults));
            // 验券成功
            for (int i = 0; i < innerUseCouponItemInfoList.size(); i++) {
                if (i >= verifyResults.size()) {
                    continue;
                }
                innerUseCouponItemInfoList.get(i).setGroupVerify(verifyResults.get(i));
            }
            return verifyResults;
        } catch (Exception e) {
            log.error("验券失败,请求参数:{},e:{}", JacksonUtils.writeValueAsString(couPonReqDTO), e.getMessage());
            // 撤销验券
            revokeGroupon(innerUseCouponItemInfoList);
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public void revokeVerifyGroupon(List<GroupVerifyDTO> revokeList) {
        if (CollectionUtils.isEmpty(revokeList)) {
            return;
        }
        log.info("团购验券回退入参:{}", JacksonUtils.writeValueAsString(revokeList));
        for (GroupVerifyDTO groupVerifyDTO : revokeList) {
            // 去撤销团购券
            groupVerifyDTO.setErpId(UserContextUtils.getStoreGuid());
            groupVerifyDTO.setErpName(UserContextUtils.getStoreName());
            groupVerifyDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            log.warn("团购验券调用第三方撤销验券参数:{}", JacksonUtils.writeValueAsString(groupVerifyDTO));
            MtDelCouponRespDTO undoResult = groupClientService.revokeCoupon(groupVerifyDTO);
            log.warn("团购验券调用第三方撤销验券返回参数,undoResult:{}", JacksonUtils.writeValueAsString(undoResult));
            if (Objects.isNull(undoResult) || undoResult.getResult() != 0) {
                UNDO_COUPON_EXECUTOR_SERVICE.schedule(() -> {
                    log.warn("团购验券再次调用第三方撤销验券,groupVerifyDTO:{}", JacksonUtils.writeValueAsString(groupVerifyDTO));
                    groupClientService.revokeCoupon(groupVerifyDTO);
                }, 6, TimeUnit.SECONDS);
            }
        }
    }

    /**
     * 撤销券
     */
    @Override
    public void revokeGroupon(List<ItemInfoDTO> itemInfoList) {
        List<ItemInfoDTO> revokeItemList = itemInfoList.stream()
                .filter(e -> Objects.nonNull(e.getGroupVerify()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(revokeItemList)) {
            return;
        }
        List<GroupVerifyDTO> revokeList = revokeItemList.stream()
                .map(ItemInfoDTO::getGroupVerify)
                .collect(Collectors.toList());
        revokeVerifyGroupon(revokeList);
        // clear
        revokeItemList.forEach(e -> e.setGroupVerify(null));
    }

    /**
     * 构建验券请求参数
     */
    private CouPonReqDTO buildCouPonReqDTO(String orderGuid, String orderRecordGuid, List<ItemInfoDTO> useCouponItemInfoList) {
        MtCouponPreRespDTO couponInfo = useCouponItemInfoList.get(0).getCouponPreRespDTO();
        Integer groupBuyType = couponInfo.getGroupBuyType();
        String couponCode = couponInfo.getCouponCode();
        CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
        couPonReqDTO.setErpId(UserContextUtils.getStoreGuid());
        couPonReqDTO.setErpName(UserContextUtils.getStoreName());
        couPonReqDTO.setErpOrderId(orderGuid);
        if (StringUtils.isEmpty(couPonReqDTO.getErpOrderId())) {
            couPonReqDTO.setErpOrderId(orderRecordGuid);
        }
        couPonReqDTO.setGroupBuyType(groupBuyType);
        if (GroupBuyTypeEnum.MEI_TUAN.getCode() == groupBuyType) {
            couPonReqDTO.setCouponCode(couponCode);
        }
        couPonReqDTO.setCount(useCouponItemInfoList.size());
        couPonReqDTO.setUserId(couponInfo.getUserId());
        couPonReqDTO.setOrderId(couponInfo.getOrderId());
        List<String> couponCodes = useCouponItemInfoList.stream()
                .map(ItemInfoDTO::getCouponPreRespDTO)
                .map(MtCouponPreRespDTO::getCouponCode)
                .collect(Collectors.toList());
        couPonReqDTO.setCouponCodeList(couponCodes);
        return couPonReqDTO;
    }

    private List<MtCouponPreRespDTO> getMtCouponPreRespDTOByItemInfo(List<ItemInfoDTO> useCouponItemInfoList) {
        return Optional.ofNullable(useCouponItemInfoList).orElse(Lists.newArrayList())
                .stream()
                .map(ItemInfoDTO::getCouponPreRespDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取购物车中验券商品对应的第三方活动
     */
    private List<ThirdActivityRespDTO> queryShopCartThirdActivityList(List<MtCouponPreRespDTO> mtCouponPreRespDTOByItemInfo) {
        if (CollectionUtils.isNotEmpty(mtCouponPreRespDTOByItemInfo)) {
            List<String> activityGuidList = mtCouponPreRespDTOByItemInfo.stream()
                    .map(MtCouponPreRespDTO::getNewActivityGuid)
                    .distinct()
                    .collect(Collectors.toList());
            return thirdActivityClientService.listByGuid(activityGuidList);
        }
        return Lists.newArrayList();
    }

    /**
     * 校验第三方活动共享规则
     */
    private void checkThirdActivityShareRule(String orderGuid,
                                             List<ThirdActivityRespDTO> shopCartThirdActivityList,
                                             List<ThirdActivityRecordDTO> thirdActivityRecords) {
        // 判断当前是否存在互斥活动
        List<ThirdActivityRecordDTO> usedNotSharedActivityList = thirdActivityRecords.stream()
                .filter(e -> e.getIsThirdShare() == 0)
                .collect(Collectors.toList());
        List<ThirdActivityRespDTO> shopCartNotSharedActivityList = shopCartThirdActivityList.stream()
                .filter(e -> e.getIsThirdShare() == 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(usedNotSharedActivityList) && CollectionUtils.isEmpty(shopCartNotSharedActivityList)) {
            return;
        }
        // 活动总数
        List<String> thirdActivityRecordGuids = thirdActivityRecords.stream()
                .map(ThirdActivityRecordDTO::getActivityGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> shopCartThirdActivityGuids = shopCartThirdActivityList.stream()
                .map(ThirdActivityRespDTO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> allActivityGuids = Stream.concat(thirdActivityRecordGuids.stream(), shopCartThirdActivityGuids.stream())
                .distinct()
                .collect(Collectors.toList());
        if (allActivityGuids.size() > 1) {
            throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
        }
        if (CollectionUtils.isNotEmpty(shopCartNotSharedActivityList) && StringUtils.isNotEmpty(orderGuid)) {
            // 如果没有使用其他第三方活动，则需要去查询是否有团购验券
            List<GrouponListRespDTO> grouponList = tradeClientService.useGrouponList(orderGuid, null);
            if (CollectionUtils.isNotEmpty(grouponList)) {
                throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
            }
        }
    }

    /**
     * 校验第三方活动使用上限
     */
    private void checkThirdActivityLimitRule(List<MtCouponPreRespDTO> mtCouponPreRespDTOByItemInfo,
                                             List<ThirdActivityRespDTO> shopCartThirdActivityList,
                                             List<ThirdActivityRecordDTO> thirdActivityRecords) {
        // ⑥点击“☑”按钮需要判断该活动的叠加数量是否达上限（判断数量为已展示在上方的券码），达上限提示：最多可叠加x张
        shopCartThirdActivityList = shopCartThirdActivityList.stream()
                .filter(e -> RuleTypeEnum.AMOUNT_DEDUCTION.getCode() == e.getRuleType())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopCartThirdActivityList)) {
            return;
        }
        Map<String, ThirdActivityRespDTO> thirdActivityMap = shopCartThirdActivityList.stream()
                .collect(Collectors.toMap(ThirdActivityRespDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        for (Map.Entry<String, ThirdActivityRespDTO> entry : thirdActivityMap.entrySet()) {
            int useLimit = entry.getValue().getUseLimit();
            long usedCount = thirdActivityRecords.stream()
                    .filter(e -> e.getActivityGuid().equals(entry.getKey()))
                    .flatMap(e -> e.getThirdActivityCodeList().stream())
                    .distinct()
                    .count();
            long shopCartCount = mtCouponPreRespDTOByItemInfo.stream()
                    .filter(e -> Objects.equals(e.getNewActivityGuid(), entry.getKey()))
                    .count();
            if (usedCount + shopCartCount > useLimit) {
                throw new BusinessException("最多可叠加" + useLimit + "张");
            }
        }
    }
}
