package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.config.WxH5AccountConfig;
import com.holderzone.saas.store.weixin.config.WxH5OpenIdRelationConfig;
import com.holderzone.saas.store.weixin.config.WxOpenAccountConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxQrRedirectDo;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.enums.WxQrTypeEnum;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import com.holderzone.saas.store.weixin.mapper.WxQrRedirectMapper;
import com.holderzone.saas.store.weixin.service.WxQrCodeInfoService;
import com.holderzone.saas.store.weixin.service.WxSaasMpService;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.service.WxStoreMpService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import com.holderzone.saas.store.weixin.utils.WxConsumerParsUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.http.URIUtil;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.open.api.WxOpenComponentService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStroreMpServiceImpl
 * @date 2019/04/01 16:40
 * @description 微信公众号service实现类
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxStroreMpServiceImpl implements WxStoreMpService {

    private static final String WORKBENCH = "workbench";

    private static final String STATE = "holder";
    @Autowired
    WxMpService wxMpService;
    @Autowired
    @Qualifier(value = "workBenchService")
    WxMpService workBenchService;
    @Autowired
    WxQrCodeInfoService wxQrCodeInfoService;
    @Autowired
    WxConsumerParsUtil wxConsumerParsUtil;
    @Autowired
    DynamicHelper dynamicHelper;
    @Autowired
    WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;
    @Autowired
    WxSaasMpService wxSaasMpService;
    @Autowired
    WxOpenComponentService wxOpenComponentService;
    @Autowired
    WeChatConfig weChatConfig;
    @Autowired
    WxOpenAccountConfig wxOpenAccountConfig;
    @Autowired
    WxH5AccountConfig wxH5AccountConfig;
    @Autowired
    WxH5OpenIdRelationConfig wxH5OpenIdRelationConfig;
    @Autowired
    WxQrRedirectMapper wxQrRedirectMapper;
    @Value("${wechat.qr.redirectUrl}")
    String qrRedirectUrl;
    @Value("${h5.componentAuthorizer:#{null}}")
    Boolean componentAuthorizerFlag;

    @Override
    public String getQrCodeUrl(WxQrCodeUrlQuery wxQrCodeUrlQuery) {
//        WxStoreAuthorizerInfoDO byBrandId = wxStoreAuthorizerInfoService.getByBrandId(wxQrCodeUrlQuery.getBrandGuid());
//        WxMpService wxMpServiceTmp = null;
//        try {
//            if (Objects.nonNull(byBrandId)){
//                wxQrCodeUrlQuery.setAppId(byBrandId.getAuthorizerAppid());
//                wxMpServiceTmp = wxSaasMpService.getWxMpService(byBrandId);
//            }else {
//                wxMpServiceTmp = wxMpService;
//            }
//            String sceneStr = wxQrCodeInfoService.getSceneStr(wxQrCodeUrlQuery);
//            String jumpUrl = String.format(baseQrCodeUrl, sceneStr);
//            String redirectUrl = wxMpServiceTmp.getOAuth2Service().buildAuthorizationUrl(jumpUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, "holder");
//            String redirectUrl = wxMpServiceTmp.getCommentService(jumpUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, "holder");
//            log.info("默认二维码生成成功：{}", redirectUrl);
//            return redirectUrl;
//        } catch (Exception e) {
//            log.error("生成默认二维码URL失败, e:{}", e.getCause());
//            return null;
//        }
        WxStoreAuthorizerInfoDO byBrandId = wxStoreAuthorizerInfoService.getByBrandId(wxQrCodeUrlQuery.getBrandGuid());
        String appId = null;
        try {
            if (Objects.nonNull(byBrandId)) {
                appId = byBrandId.getAuthorizerAppid();
                wxQrCodeUrlQuery.setAppId(appId);
            } else {
                appId = weChatConfig.getInternalAppId();
            }
            String sceneStr = wxQrCodeInfoService.getSceneStr(wxQrCodeUrlQuery);
            String jumpUrl = String.format(weChatConfig.getBaseQrCodeUrl(), sceneStr);
            String redirectUrl = wxOpenComponentService.oauth2buildAuthorizationUrl(appId, jumpUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, STATE);
            log.info("默认二维码生成成功：{}", redirectUrl);
            return redirectUrl;
        } catch (Exception e) {
            log.error("生成默认二维码URL失败!", e);
            return null;
        }
    }

    @Override
    public String getAuthorizeUrl(WxQrCodeUrlQuery wxQrCodeUrlQuery, String memberInfoGuid, String thirdAppId, String thirdOpenId) {
        try {
            String sceneStr = wxQrCodeInfoService.getSceneStr(wxQrCodeUrlQuery);
            String jumpUrl = String.format(weChatConfig.getBaseQrCodeUrl(), sceneStr);
            if (StringUtils.isNotEmpty(memberInfoGuid)) {
                jumpUrl = jumpUrl + "&memberInfoGuid=" + memberInfoGuid;
            }
            if (StringUtils.isNotEmpty(thirdAppId)) {
                jumpUrl = jumpUrl + "&thirdAppId=" + thirdAppId;
            }
            if (StringUtils.isNotEmpty(thirdOpenId)) {
                jumpUrl = jumpUrl + "&thirdOpenId=" + thirdOpenId;
            }
            String redirectUrl = String.format("https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&connect_redirect=1" +
                            "&scope=snsapi_base&state=%s#wechat_redirect",
                    wxQrCodeUrlQuery.getAppId(), URIUtil.encodeURIComponent(jumpUrl), StringUtils.trimToEmpty(STATE));
            if (wxH5OpenIdRelationConfig.getBrandGuids().contains(wxQrCodeUrlQuery.getBrandGuid())
                    || (Boolean.TRUE.equals(wxQrCodeUrlQuery.getIsMyself()) && !Boolean.TRUE.equals(wxQrCodeUrlQuery.getIsAgain()))
                    || Boolean.TRUE.equals(componentAuthorizerFlag)) {
                redirectUrl = String.format("https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&connect_redirect=1" +
                                "&scope=snsapi_base&state=%s&component_appid=%s#wechat_redirect",
                        wxQrCodeUrlQuery.getAppId(), URIUtil.encodeURIComponent(jumpUrl), StringUtils.trimToEmpty(STATE), wxOpenAccountConfig.getComponentAppId());
            }
            log.info("普通二维码扫码微信授权重定向地址生成成功：{}", redirectUrl);
            return redirectUrl;
        } catch (Exception e) {
            log.error("普通二维码扫码微信授权重定向地址生成失败!", e);
            return null;
        }
    }

    @Override
    public String getUserInfo(WxAuthorizeReqDTO wxAuthorizeReqDTO) throws WxErrorException {
        if (Objects.equals(WORKBENCH, wxAuthorizeReqDTO.getEventKey())) {
            WxMpOAuth2AccessToken accessToken = workBenchService.getOAuth2Service().getAccessToken(wxAuthorizeReqDTO.getCode());
            WxMpUser user = workBenchService.getOAuth2Service().getUserInfo(accessToken, null);
            log.info("[workbench]微信用户信息：{}", JacksonUtils.writeValueAsString(user));
//            WxMpOAuth2AccessToken accessToken = workBenchService.oauth2getAccessToken(wxAuthorizeReqDTO.getCode());
//            WxMpUser user = workBenchService.oauth2getUserInfo(accessToken, null);
            return String.format(weChatConfig.getWorkbenchUrl(), user.getOpenId());
        }
        WxMpOAuth2AccessToken accessToken = workBenchService.getOAuth2Service().getAccessToken(wxAuthorizeReqDTO.getCode());
        WxMpUser user = workBenchService.getOAuth2Service().getUserInfo(accessToken, null);
//        WxMpOAuth2AccessToken accessToken = workBenchService.oauth2getAccessToken(wxAuthorizeReqDTO.getCode());
//        WxMpUser user = workBenchService.oauth2getUserInfo(accessToken, null);
        log.info("微信用户信息：{}", user);
        String base64EventKey = wxAuthorizeReqDTO.getEventKey();
        String eventKey = wxConsumerParsUtil.changeDateSourceByEventKey(base64EventKey);
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getByAppId(wxAuthorizeReqDTO.getAppId());
        return wxConsumerParsUtil.parse2Consumer(eventKey, user, wxStoreAuthorizerInfoDO);
    }

    @Override
    public String verifyMessage(WxCommonReqDTO wxCommonReqDTO) {
        log.info("接收到来自微信服务器的认证消息：[signature: {}" +
                        ", timestamp: {}, nonce: {}, echostr: {}]",
                wxCommonReqDTO.getSignature(), wxCommonReqDTO.getTimestamp(),
                wxCommonReqDTO.getNonce(), wxCommonReqDTO.getEchostr());
        if (StringUtils.isAnyBlank(wxCommonReqDTO.getSignature(),
                wxCommonReqDTO.getTimestamp(), wxCommonReqDTO.getNonce(), wxCommonReqDTO.getEchostr())) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }
        if (wxMpService.checkSignature(wxCommonReqDTO.getTimestamp(), wxCommonReqDTO.getNonce(), wxCommonReqDTO.getSignature())) {
            log.info("微信服务验证通过，返回echostr:{}", wxCommonReqDTO.getEchostr());
            return wxCommonReqDTO.getEchostr();
        }
        return "非法请求";
    }

    @Override
    public String shortenUrl(String longUrl, String tableGuid,Integer type) throws WxErrorException {
        //桌台Guid为空不做处理
        if (StringUtils.isEmpty(tableGuid)) {
            return longUrl;
        }
        String url;
        //若是赚餐小程序二维码
        if (type == WxQrTypeEnum.ZHUANCAN_QR.getCode()) {
            url = longUrl.substring(0, longUrl.indexOf("?") + 1) + "t=%s,%s";
        } else if (type == WxQrTypeEnum.WX_CP_QR.getCode()) {
            url = qrRedirectUrl + "/gateway/weixin/wx_mp/cp/authorize?t=%s,%s&w=1";
        } else {
            //实际跳转地址
//            redirect = wxMpService.shortUrl(longUrl);
            url = qrRedirectUrl + "/gateway/weixin/wx_mp/qr?t=%s,%s";
        }
        //跳转地址入库
        WxQrRedirectDo wxQrRedirectDo = new WxQrRedirectDo();
        wxQrRedirectDo.setUrl(longUrl);
        wxQrRedirectDo.setTableGuid(tableGuid);
        wxQrRedirectDo.setType(type);
        wxQrRedirectMapper.insertMaster(wxQrRedirectDo);
        //拼接链接地址
        url = String.format(url,UserContextUtils.getEnterpriseGuid(),wxQrRedirectDo.getId());
        log.info("下载默认二维码信息：" + url);
        return url;
    }
}
