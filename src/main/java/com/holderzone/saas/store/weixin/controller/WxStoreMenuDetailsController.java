package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreMenuReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxMenuDetailsDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.annotation.DynamicData;
import com.holderzone.saas.store.weixin.service.WxStoreMenuDetailsService;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreMenuItemClientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMenuDetailsController
 * @date 2019/2/22 11:49
 * @description 微信点餐菜单信息Controller
 * @package com.holderzone.saas.store.weixin.controller
 */
@RestController
@RequestMapping("/wx-store-menu-provide")
@Api("微信点餐菜单信息")
@Slf4j
public class WxStoreMenuDetailsController {

	@Autowired
	private WxStoreMenuItemClientService wxStoreMenuItemClientService;
    @Autowired
    private WxStoreMenuDetailsService wxStoreMenuDetailsService;
	@Autowired
	private WxStoreOrderConfigService wxStoreOrderConfigService;

    @ApiOperation(value = "获取微信点餐基本菜单，分类，标签信息")
    @ApiImplicitParam(name = "baseDTO", value = "门店基本信息")
    @PostMapping("/details")
    public WxMenuDetailsDTO getWxMenuDetails(@RequestBody WxStoreMenuReqDTO wxStoreMenuReqDTO) {
        log.info("获取微信点餐菜单信息{}", JacksonUtils.writeValueAsString(wxStoreMenuReqDTO));
        return wxStoreMenuDetailsService.getWxMenuDetails(wxStoreMenuReqDTO);
    }
    
    
    @ApiOperation(value = "获取微信点餐基本菜单，分类，标签信息")
    @ApiImplicitParam(name = "baseDTO", value = "门店基本信息")
    @PostMapping("/test")
    public WxMenuDetailsDTO test(@RequestBody WxStoreMenuReqDTO wxStoreMenuReqDTO) {
     return new WxMenuDetailsDTO();
    }


    @ApiOperation("获取微信门店当前配置信息")
    @ApiImplicitParam(name = "baseDTO", value = "当前门店基本信息")
    @PostMapping("/configuration")
	@DynamicData(enterpriseGuid = "#wxStoreConsumerDTO.enterpriseGuid",storeGuid="#wxStoreConsumerDTO.storeGuid")
    public WxOrderConfigDTO getwxOrderConfig(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO) {
        log.info("获取微信门店配置详情{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
        return wxStoreMenuDetailsService.getStoreConfiguration(wxStoreConsumerDTO);
    }

    @PostMapping("/get_consumer_info")
    public WxStoreConsumerDTO getConsumerInfo(@RequestBody WxPortalReqDTO wxPortalReqDTO) {
        log.info("获取Consumer信息，请求参数wxConsumerReqDTO:{}", wxPortalReqDTO);
        return wxStoreMenuDetailsService.getConsumerInfo(wxPortalReqDTO);
    }

	@ApiOperation("查询门店配置")
    @GetMapping("/store_config")
    public WxOrderConfigDTO getStoreConfig(@RequestParam("storeGuid") String storeGuid){
		log.info("查询门店配置入参:{}",storeGuid);
		return wxStoreOrderConfigService.getDetailConfig(new WxStoreReqDTO(storeGuid));
	}

}
