package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOrderConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.OrderTradeModeEnum;
import com.holderzone.saas.store.weixin.constant.ModelName;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderConfigDO;
import com.holderzone.saas.store.weixin.mapper.WxStoreOrderConfigMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreConfigMapstruct;
import com.holderzone.saas.store.weixin.service.WxConfigOverviewService;
import com.holderzone.saas.store.weixin.service.WxOrganizationService;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.weixin.common.BusinessName.WX_STORE_CONFIG;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreOrderConfigServiceImpl
 * @date 2019/02/14 14:56
 * @description 微信点餐门店配置service实现类
 * @program holder-saas-store-weixin
 */

@Service
@Slf4j
public class WxStoreOrderConfigServiceImpl extends ServiceImpl<WxStoreOrderConfigMapper, WxOrderConfigDO> implements WxStoreOrderConfigService {

    @Autowired
    WxStoreConfigMapstruct wxStoreConfigMapstruct;

    @Autowired
    OrganizationClientService organizationClientService;

    @Autowired
    WxOrganizationService wxOrganizationService;
    @Autowired
    WxConfigOverviewService wxConfigOverviewService;
    @Autowired
    RedisUtils redisUtils;

    /**
     * @param wxStorePageReqDTO（不传默认查所有）
     * @return Page<WxOrderConfigDTO>
     */
    @Override
    public Page<WxOrderConfigDTO> pageOrderConfig(WxStorePageReqDTO wxStorePageReqDTO) {
        // Retrieve store configuration details
        Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> triple = wxOrganizationService.getStoreConfig(wxStorePageReqDTO);
        Page<StoreDTO> storeDTOPage = triple.getLeft();
        List<StoreDTO> storeDTOList = triple.getMiddle();
        List<String> storeGuidList = triple.getRight();
        Page<WxOrderConfigDTO> respPage = new Page<>(storeDTOPage.getCurrentPage(), storeDTOPage.getPageSize(), storeDTOPage.getTotalCount());

        if (!storeDTOList.isEmpty()) {
            List<WxOrderConfigDTO> respDTOS = new ArrayList<>();
            Map<String, WxOrderConfigDO> wxOrderConfigDOMap = list(new LambdaQueryWrapper<WxOrderConfigDO>()
                    .in(WxOrderConfigDO::getStoreGuid, storeGuidList)).stream()
                    .collect(Collectors.toMap(WxOrderConfigDO::getStoreGuid, Function.identity()));

            for (StoreDTO storeDTO : storeDTOList) {
                WxOrderConfigDO wxOrderConfigDO = wxOrderConfigDOMap.get(storeDTO.getGuid());
                if (ObjectUtils.isEmpty(wxOrderConfigDO)) {
                    throw new BusinessException("门店：【" + storeDTO.getName() + "】尚未初始化，请进入概览页面初始化");
                }
                WxOrderConfigDTO wxOrderConfigDTO = wxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(wxOrderConfigDO);
                wxOrderConfigDTO.setStoreName(storeDTO.getName());
                wxOrderConfigDTO.setTagNames(wxOrderConfigDTO.getTagNames().replaceAll("\\[([^]]*)]", "$1"));
                wxOrderConfigDTO.setBrandNameList(Optional.ofNullable(storeDTO.getBrandDTOList())
                        .map(list -> list.stream().map(BrandDTO::getName).collect(Collectors.toList()))
                        .orElseGet(Lists::newArrayList));
                respDTOS.add(wxOrderConfigDTO);
            }
            respPage.setData(respDTOS);
        }
        return respPage;
    }


    @Override
    public Boolean saveStoreConfig(WxStoreReqDTO wxStoreReqDTO) {
        // Check if store configuration already exists
        WxOrderConfigDO wxOrderConfigDO = getOne(new LambdaQueryWrapper<WxOrderConfigDO>()
                .eq(WxOrderConfigDO::getStoreGuid, wxStoreReqDTO.getStoreGuid()));
        if (ObjectUtils.isEmpty(wxOrderConfigDO)) {
            wxOrderConfigDO = new WxOrderConfigDO();
            try {
                wxOrderConfigDO.setStoreGuid(wxStoreReqDTO.getStoreGuid());
                wxOrderConfigDO.setGmtCreate(LocalDateTime.now());
                wxOrderConfigDO.setGuid(redisUtils.generateGuid(ModelName.WX + ":"
                        + wxStoreReqDTO.getStoreGuid() + ":saveConfig"));
                save(wxOrderConfigDO);
                redisUtils.setEx(redisUtils.keyGenerate(WX_STORE_CONFIG, wxOrderConfigDO.getStoreGuid()),
                        wxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(wxOrderConfigDO), 24, TimeUnit.HOURS);
            } catch (Exception e) {
                throw new RuntimeException("初始化门店配置信息失败,e:{}", e);
            }
        }
        return true;
    }

    /**
     * 获取门店配置详细信息
     * 包含微信点餐及PAD端用户点餐买单设置
     *
     * @param wxStoreReqDTO
     * @return
     */
    @Override
    public WxOrderConfigDTO getDetailConfig(WxStoreReqDTO wxStoreReqDTO) {
        // Retrieve detailed store configuration
        if (wxStoreReqDTO.getGuid() != null)
            return wxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(getById(wxStoreReqDTO.getGuid()));
        WxOrderConfigDTO wxOrderConfigDTO =
                (WxOrderConfigDTO) redisUtils.get(redisUtils.keyGenerate(WX_STORE_CONFIG, wxStoreReqDTO.getStoreGuid()));
        if (!ObjectUtils.isEmpty(wxOrderConfigDTO))
            return wxOrderConfigDTO;
        WxOrderConfigDO one = getOne(new LambdaQueryWrapper<WxOrderConfigDO>()
                .eq(WxOrderConfigDO::getStoreGuid, wxStoreReqDTO.getStoreGuid()));
        if (ObjectUtils.isEmpty(one))
            return null;
        wxOrderConfigDTO = wxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(one);
        String keyGenerate = redisUtils.keyGenerate(WX_STORE_CONFIG, wxOrderConfigDTO.getStoreGuid());
        redisUtils.setEx(keyGenerate, wxOrderConfigDTO, 24, TimeUnit.HOURS);
        log.info("getDetailConfig 微信门店配置={}", JacksonUtils.writeValueAsString(wxOrderConfigDTO));
        return wxOrderConfigDTO;
    }

    @Override
    public String getPadBackgroundUrl(String storeGuid) {

        if (StringUtils.isEmpty(storeGuid)) {
            return null;
        }
        WxOrderConfigDTO wxOrderConfigDTO = (WxOrderConfigDTO) redisUtils.get(redisUtils.keyGenerate(WX_STORE_CONFIG,
                storeGuid));
        if (Objects.nonNull(wxOrderConfigDTO)) {
            return wxOrderConfigDTO.getPadBackgroundUrl();
        }
        WxOrderConfigDO wxOrderConfigDO = getOne(new LambdaQueryWrapper<WxOrderConfigDO>().eq(WxOrderConfigDO::getStoreGuid, storeGuid));
        if (Objects.nonNull(wxOrderConfigDO)) {
            return wxOrderConfigDO.getPadBackgroundUrl();
        }
        log.info("getPadBackgroundUrl 没有查到微信门店配置，门店guid{}", storeGuid);
        return null;
    }

    @Override
    public Boolean updateStoreConfig(WxOrderConfigDTO wxOrderConfigDTO) {
        // Update store configuration
        if (!wxOrderConfigDTO.getIsOnlyState()) {
            List<String> storeGuidList = Arrays.asList(wxOrderConfigDTO.getStoreGuid());
            wxConfigOverviewService.couldEdit(storeGuidList, 0);
        }
        //检查自动确认时间和提示时间
        checkConfirmTime(wxOrderConfigDTO.getAutoConfirm(), wxOrderConfigDTO.getAutoConfirmTime()
                , wxOrderConfigDTO.getConfirmPromptTime());
        if (Objects.nonNull(wxOrderConfigDTO.getGuestFlag())) {
            wxOrderConfigDTO.setGuestFlag(Objects.equals(OrderTradeModeEnum.正餐.getCode(), wxOrderConfigDTO.getOrderModel())
                    && Objects.equals(BooleanEnum.TRUE.getCode(), wxOrderConfigDTO.getGuestFlag())
                    ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        }
        WxOrderConfigDO wxOrderConfigDO = wxStoreConfigMapstruct.updateDTO2WxStoreOrderConfigDO(wxOrderConfigDTO);
        Boolean flag = updateById(wxOrderConfigDO);
        WxOrderConfigDTO updateDTO = wxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(getById(wxOrderConfigDO.getGuid()));
        redisUtils.setEx(redisUtils.keyGenerate(WX_STORE_CONFIG, wxOrderConfigDTO.getStoreGuid()), updateDTO, 24, TimeUnit.HOURS);
        return flag;
    }

    private final static Integer CONFIRM_TIME_LESS = 1;

    private final static Integer AUTO_CONFIRM_TIME_LESS = 0;

    private final static Integer CONFIRM_TIME_MORE = 999999;

    private void checkConfirmTime(Integer autoConfirm, Long autoConfirmTime, Long confirmPromptTime) {
        // Validate confirmation times
        boolean isInvalidTime = !ObjectUtils.isEmpty(confirmPromptTime)
                && (confirmPromptTime < CONFIRM_TIME_LESS || confirmPromptTime > CONFIRM_TIME_MORE);
        if (isInvalidTime) {
            throw new BusinessException("配置确认时间有误");
        }
        if (ObjectUtils.isEmpty(autoConfirm) || autoConfirm.equals(0)) {
            return;
        }
        if (ObjectUtils.isEmpty(autoConfirmTime)) {
            throw new BusinessException("配置自动确认接单时间有误");
        }
        if (autoConfirmTime < AUTO_CONFIRM_TIME_LESS || autoConfirmTime > CONFIRM_TIME_MORE) {
            throw new BusinessException("配置自动确认接单时间有误");
        }
        if (!ObjectUtils.isEmpty(confirmPromptTime) && confirmPromptTime > autoConfirmTime) {
            throw new BusinessException("自动确认时间不能小于提示时间");
        }
    }

    @Override
    public Boolean updateBatchStoreConfig(WxOrderConfigUpdateBatchReqDTO wxOrderConfigUpdateBatchReqDTO) {
        // Batch update store configurations
        List<String> storeGuidList = wxOrderConfigUpdateBatchReqDTO.getStoreGuidList();
        List<WxOrderConfigDO> orderConfigDOS = list(new LambdaQueryWrapper<WxOrderConfigDO>()
                .in(WxOrderConfigDO::getStoreGuid, storeGuidList));
        WxOrderConfigDTO wxOrderConfigDTO = wxOrderConfigUpdateBatchReqDTO.getWxOrderConfigDTO();
        //检查自动确认时间和提示时间
        checkConfirmTime(wxOrderConfigDTO.getAutoConfirm(), wxOrderConfigDTO.getAutoConfirmTime()
                , wxOrderConfigDTO.getConfirmPromptTime());
        List<WxOrderConfigDO> updateDOList = new ArrayList<>();
        List<Pair<String, Object>> paramList = new ArrayList<>();
        for (WxOrderConfigDO wxOrderConfigDO : orderConfigDOS) {
            WxOrderConfigDO updateDO = wxStoreConfigMapstruct.updateDTO2WxStoreOrderConfigDO(wxOrderConfigDTO);
            updateDO.setGuid(wxOrderConfigDO.getGuid());
            updateDO.setStoreGuid(wxOrderConfigDO.getStoreGuid());
            updateDOList.add(updateDO);
            WxOrderConfigDTO updateDTO = wxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(updateDO);
            Pair<String, Object> param = Pair.of(redisUtils.keyGenerate(WX_STORE_CONFIG, wxOrderConfigDTO.getStoreGuid()), updateDTO);
            paramList.add(param);
        }
        Boolean flag = updateBatchById(updateDOList);
        redisUtils.setParamListEx(paramList, 24, TimeUnit.HOURS);
        return flag;

    }


    @Override
    public Map<String, WxOrderConfigDO> mapOrderConfigDO(List<String> storeGuidList) {
        List<WxOrderConfigDO> orderConfigDOList = list(new LambdaQueryWrapper<WxOrderConfigDO>()
                .in(WxOrderConfigDO::getStoreGuid, storeGuidList));
        return orderConfigDOList.stream().collect(Collectors.toMap(WxOrderConfigDO::getStoreGuid, Function.identity()));
    }

    @Override
    public WxOrderConfigDTO getStoreConfig(String storeGuid) {
        WxOrderConfigDTO detailConfig = getDetailConfig(new WxStoreReqDTO(storeGuid));
        Pair<WxStoreInfoDTO, Boolean> storeOpen = wxConfigOverviewService.isStoreOpen(0, storeGuid);
        log.info("getStoreConfig返回storeOpen门店配置:{}", JacksonUtils.writeValueAsString(detailConfig));
        assertEnableMenuConfig(storeOpen);
        toOrderConfig(detailConfig, storeOpen);
        log.info("getStoreConfig返回的门店配置:{}", JacksonUtils.writeValueAsString(detailConfig));
        return detailConfig;
    }

    private void toOrderConfig(WxOrderConfigDTO wxOrderConfigDTO, Pair<WxStoreInfoDTO, Boolean> storeOpen) {
        WxStoreInfoDTO wxStoreInfoDTO = storeOpen.getLeft();
        wxOrderConfigDTO.setStoreGuid(wxStoreInfoDTO.getStoreGuid());
        wxOrderConfigDTO.setStoreName(wxStoreInfoDTO.getStoreName());
        wxOrderConfigDTO.setBrandNameList(wxStoreInfoDTO.getBrandNameList());
        wxOrderConfigDTO.setIsOpened(wxStoreInfoDTO.getIsOpened());
    }

    private void assertEnableMenuConfig(Pair<WxStoreInfoDTO, Boolean> storeOpen) {
        if (com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isEmpty(storeOpen) || !storeOpen.getRight())
            throw new BusinessException("当前门店暂未开启微信点餐功能");
    }

}
