package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxComponentConfigDTO;
import me.chanjar.weixin.common.error.WxErrorException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreComponentConfigService
 * @date 2019/02/23 11:56
 * @description //TODO
 * @program ${MODULE_NAME}
 */
public interface WxStoreComponentConfigService {

    /**
     * 接收微信方推送的receiveTicket
     *
     * @param wxCommonReqDTO
     * @return 默认字段：'success'
     */
    String receiveTicket(WxCommonReqDTO wxCommonReqDTO);

    /**
     * 获取第三方平台accessToken
     *
     * @return
     * @throws WxErrorException
     */
    String getAccessToken() throws WxErrorException;

    /**
     *
     * @param wxComponentConfigDTO
     */
    void setVerifyTicket(WxComponentConfigDTO wxComponentConfigDTO);
}
