package com.holderzone.saas.store.weixin.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketMqConfig
 * @date 2019/03/05 10:34
 * @description 微信Mq配置
 * @program holder-saas-store
 */
public interface RocketMqConfig {
	String WECHAT_TABLE_STICKER_TOPIC = "wechat-table-sticker-topic";

	String WECHAT_TABLE_STICKER_TAG = "wechat-table-sticker-tag";

	String WECHAT_TABLE_STICKER_GROUP = "wechat-table-sticker-group";

	String WX_STORE_SOCKET_MESSAGE = "table-store-socket-message";

	String WX_STORE_SOCKET_MESSAGE_GROUP = "table-store-socket-message-group";

	String TABLE_STATUS_CHANGE_MQ_TURN = "table-status-change-turn";

	String TABLE_STATUS_CHANGE_MQ_COMBINE = "table-status-change-combine";

	String TABLE_STATUS_CHANGE_MQ_SEPARATE = "table-status-change-separate";

	String TABLE_STATUS_CHANGE_MQ_TABLE = "table-status-change-table";

	String TABLE_STATUS_CHANGE_WEIXIN_GROUP = "table-status-change-weixin-group";

	String TABLE_STATUS_CHANGE_MQ_TOPIC = "table-status-change-topic";

	String TABLE_STATUS_CHANGE_MQ_TAG = "table-status-change-tag";

	String ORDER_STATUS_CHANGE_MQ_GROUP = "order-status-change-group";

	String DOWNSTREAM_CONTEXT = "downstream-context";

	String DOWNSTREAM_STORE_TOPIC = "downstream-store-topic";

	String DOWNSTREAM_STORE_CREATE_TAG = "downstream-store-create-tag";

	String DOWNSTREAM_STORE_INIT_WEIXIN_GROUP = "downstream-store-init-weixin-group";

	/**
	 * 云端购物车回调
	 */
	String MARKET_WECHAT_STICKER_TOPIC = "market-wechat-sticker-topic";

	String MARKET_WECHAT_STICKER_TAG = "market-wechat-sticker-tag";

	String MARKET_WECHAT_STICKER_CONSUME_GROUP = "market-wechat-sticker-consume-group";

	/**
	 * 快餐延迟队列
	 */
	//String FAST_ORDER_DELAY_TOPIC = "fast-order-delay-topic";

	String FAST_ORDER_DELAY_TOPIC = "weixin-fast-order-delay";

	String FAST_ORDER_DELAY_TAG = "fast-order-delay-tag";

	String FAST_ORDER_DELAY_GROUP = "fast-order-delay-group";

	/**
	 * 本地化订单id同步
	 */
	String WECHAT_ORDER_CHANGE_MQ_TOPIC = "weChat-order-change-topic";

	String WECHAT_ORDER_CHANGE_MQ_TAG = "weChat-order-change-tag";

	String WECHAT_ORDER_CHANGE_MQ_GROUP = "weChat-order-change-group";

}
