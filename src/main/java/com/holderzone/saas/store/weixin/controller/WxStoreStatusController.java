package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreStatusUpdateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxCouldEditStoreDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreStatusRespDTO;
import com.holderzone.saas.store.weixin.service.WxConfigOverviewService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreStatusController
 * @date 2019/02/21 18:26
 * @description 微信门店各业务状态Controller
 * @program holder-saas-store-weixin
 */
@RestController
@RequestMapping("/wx_store_status")
@Api(value = "微信门店各业务状态Controller")
public class WxStoreStatusController {

    @Autowired
    WxConfigOverviewService wxConfigOverviewService;

    @PostMapping("/page_wx_store_status")
    public Page<WxStoreStatusRespDTO> getWxStoreStatus(@RequestBody WxStorePageReqDTO wxStorePageReqDTO) {
        return wxConfigOverviewService.pageWxStoreStatus(wxStorePageReqDTO);
    }

    @PostMapping("/update_status_by_guid")
    public Boolean updateStatusByGuid(@RequestBody WxStoreStatusUpdateReqDTO wxStoreStatusUpdateReqDTO) {
        return wxConfigOverviewService.updateWxStoreStatus(wxStoreStatusUpdateReqDTO);
    }

    @PostMapping("/list_could_edit_store")
    public List<WxCouldEditStoreDTO> listCouldEditStore(@RequestBody WxStoreReqDTO wxStoreReqDTO) {
        return wxConfigOverviewService.listCouldEditStore(wxStoreReqDTO);
    }



}
