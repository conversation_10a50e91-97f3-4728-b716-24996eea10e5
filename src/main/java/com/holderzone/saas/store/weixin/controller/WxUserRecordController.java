package com.holderzone.saas.store.weixin.controller;

import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.weixin.WxUserRecordDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.mapstruct.WxUserRecordMapstruct;
import com.holderzone.saas.store.weixin.service.WxUserRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/wx_user_record")
@Api(value = "修改微信登录状态")
@Slf4j
public class WxUserRecordController {

	@Autowired
    WxUserRecordService wxUserRecordService;
	@Autowired
	  private WxUserRecordMapstruct wxUserRecordMapstruct;
	@Resource
	RedisUtils redisUtils;

	@GetMapping("/getUserByOpenId/{openId}")
    @ApiOperation(value = "openid获取用户")
	public WxUserRecordDTO getUserByOpenId(@PathVariable(value="openId")String openId) {
		WxUserRecordDO oneByOpenId = wxUserRecordService.getOneByOpenId(openId);
		if(oneByOpenId==null) {
			return null;
		}
		return wxUserRecordMapstruct.getWxUserRecordDTO(oneByOpenId);
	}

	@PostMapping(value = "/bind_phone")
	@ApiOperation(value="绑定手机号")
	public Boolean bindPhoneNum(@RequestParam("phoneNum") String phoneNum) {
		log.info("绑定手机号入参:{}",phoneNum);
		return wxUserRecordService.bindPhoneNum(phoneNum);
	}

	@ApiOperation("根据手机号查询")
	@GetMapping(value = "/getByPhone")
	public WxUserRecordDTO obtainByPhone(@RequestParam("phone") String phone) {
		log.info("根据手机号查询用户:{}",phone);
		return WxUserRecordMapstruct.INSTANCE.getWxUserRecordDTO(wxUserRecordService.obtainByPhone(phone));
	}


	@GetMapping("/loginWxUserRecordAndFlushCard")
	public void loginWxUserRecordAndFlushCard(
			@RequestParam("enterpriseGuid")String enterpriseGuid,
			@RequestParam("phoneNum")String phoneNum,@RequestParam("openId")String openId){

		WxMemberSessionDTO memberSessionDTO = WeixinUserThreadLocal.get();
		if(memberSessionDTO!=null&&StringUtils.isNotEmpty(memberSessionDTO.getStoreGuid())){
			//点餐场景
			//更新session
			log.info("点餐更新session,loginWxUserRecordAndFlushCard:{}",memberSessionDTO);
			wxUserRecordService.asyncMemberInfo(memberSessionDTO, openId);
		}
		wxUserRecordService.updateUserLoginByOpenId(enterpriseGuid,openId,true,memberSessionDTO);


	}
}
