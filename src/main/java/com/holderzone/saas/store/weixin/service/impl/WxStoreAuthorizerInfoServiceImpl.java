package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.framework.base.dto.message.ShortMessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOpenAuthDTO;
import com.holderzone.saas.store.dto.weixin.req.*;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandAuthRespDTO;
import com.holderzone.saas.store.enums.weixin.WxServiceTypeEnum;
import com.holderzone.saas.store.enums.weixin.WxUnBandRespEnum;
import com.holderzone.saas.store.enums.weixin.WxVerifyTypeEnum;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.config.WxThirdOpenConfig;
import com.holderzone.saas.store.weixin.constant.ModelName;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.enums.WxTemplateTypeEnum;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import com.holderzone.saas.store.weixin.mapper.WxStoreAuthorizerInfoMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreOpenMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.BaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpQrcodeService;
import me.chanjar.weixin.mp.api.impl.WxMpQrcodeServiceImpl;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import me.chanjar.weixin.open.api.WxOpenComponentService;
import me.chanjar.weixin.open.bean.auth.WxOpenAuthorizationInfo;
import me.chanjar.weixin.open.bean.result.WxOpenAuthorizerInfoResult;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreAuthorizerInfoServiceImpl
 * @date 2019/02/28 9:30
 * @description 微信授权放账号信息Service实现类
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxStoreAuthorizerInfoServiceImpl extends ServiceImpl<WxStoreAuthorizerInfoMapper, WxStoreAuthorizerInfoDO>
        implements WxStoreAuthorizerInfoService {
    @Autowired
    WxOpenComponentService wxOpenComponentService;
    @Autowired
    WxStoreComponentConfigService wxStoreComponentConfigService;
    @Autowired
    WxThirdOpenConfig wxThirdOpenConfig;
    @Autowired
    WxStoreOpenMapstruct wxStoreOpenMapstruct;
    @Autowired
    WxStoreAuthorizerBusiInfoService wxStoreAuthorizerBusiInfoService;
    @Autowired
    OrganizationClientService organizationClientService;
    @Autowired
    BaseClientService baseClientService;
    @Autowired
    WxSaasMpService wxSaasMpService;
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    WxQrCodeInfoService wxQrCodeInfoService;
    @Autowired
    WxStoreMpService wxStoreMpService;
    @Autowired
    @Lazy
    WxMpTemplateService wxMpTemplateService;
    @Autowired
    DynamicHelper dynamicHelper;
    @Autowired
    WeChatConfig weChatConfig;

    public static final String SUCCESS = "success";

    @Override
    public String getPreAuthCode(WxPreCodReqDTO wxPreCodReqDTO) throws WxErrorException {
        // 获取访问令牌
        wxStoreComponentConfigService.getAccessToken();
        // 构建重定向URL
        String redirectUrl = buildRedirectUrl(wxPreCodReqDTO);
        log.info("redirectUrl=" + redirectUrl);
        // 获取预授权URL
        String preAuthUrl = wxThirdOpenConfig.getWxOpenComponentService().getPreAuthUrl(redirectUrl);
        log.info("微信回调地址：{}", redirectUrl);
        log.info("封装后的微信预授权请求地址：{}", preAuthUrl);
        return preAuthUrl;
    }

    /**
     * 构建重定向URL
     */
    private String buildRedirectUrl(WxPreCodReqDTO wxPreCodReqDTO) {
        String redirectUrl = String.format(weChatConfig.getREDIRECT_URI(), UserContextUtils.getEnterpriseGuid(), wxPreCodReqDTO.getBrandGuid());
        if (redirectUrl.endsWith("]")) {
            redirectUrl = redirectUrl.substring(0, redirectUrl.length() - 1);
        }
        return redirectUrl;
    }

    @Override
    public String queryAuth(WxOpenAuthDTO wxOpenAuthDTO) throws WxErrorException, UnsupportedEncodingException {
        // 通过授权码获取公众号信息
        WxOpenAuthorizerInfoResult wxOpenAuthorizerInfoResult = getWxOpenAuthorizerInfo(wxOpenAuthDTO.getAuth_code());
        String appId = wxOpenAuthorizerInfoResult.getAuthorizationInfo().getAuthorizerAppid();
        String brandGuid = wxOpenAuthDTO.getBrandGuid();

        // 查询公众号是否已有绑定的品牌
        WxStoreAuthorizerInfoDO appIdIsExistDO = getAuthorizerInfoByAppId(appId);
        // 查询该品牌是否已绑定公众号
        WxStoreAuthorizerInfoDO brandGuidIsExistDO = getAuthorizerInfoByBrandGuid(brandGuid);

        // 根据现有记录处理授权
        return appIdIsExistDO == null
                ? handleNewAuthorization(wxOpenAuthDTO, wxOpenAuthorizerInfoResult, brandGuidIsExistDO)
                : handleExistingAuthorization(wxOpenAuthorizerInfoResult, appIdIsExistDO, brandGuidIsExistDO);
    }

    /**
     * 根据appId获取授权信息
     */
    private WxStoreAuthorizerInfoDO getAuthorizerInfoByAppId(String appId) {
        return getOne(new LambdaQueryWrapper<WxStoreAuthorizerInfoDO>()
                .eq(WxStoreAuthorizerInfoDO::getAuthorizerAppid, appId));
    }

    /**
     * 根据brandGuid获取授权信息
     */
    private WxStoreAuthorizerInfoDO getAuthorizerInfoByBrandGuid(String brandGuid) {
        return getById(brandGuid);
    }

    /**
     * 处理新的授权情况
     */
    private String handleNewAuthorization(WxOpenAuthDTO wxOpenAuthDTO, WxOpenAuthorizerInfoResult wxOpenAuthorizerInfoResult, WxStoreAuthorizerInfoDO brandGuidIsExistDO) throws UnsupportedEncodingException {
        if (brandGuidIsExistDO == null) {
            log.info("当前公众号暂无品牌绑定，将绑定到guid：{}，对应品牌", wxOpenAuthDTO.getBrandGuid());
            String result = saveOrUpdateAuthorizeInfo(wxOpenAuthorizerInfoResult, 0, wxOpenAuthDTO.getBrandGuid(), generateGuidForAuthInfo(wxOpenAuthDTO.getBrandGuid()), null, null);
            return handleResult(result);
        } else {
            log.info("当前品牌已绑定了其他公众号,品牌guid:{}, 公众号appId：{}", wxOpenAuthDTO.getBrandGuid(), brandGuidIsExistDO.getAuthorizerAppid());
            return handleError("当前品牌已绑定了其他公众号，若要绑定该公众号，请先解绑");
        }
    }

    /**
     * 处理已存在的授权情况
     */
    private String handleExistingAuthorization(WxOpenAuthorizerInfoResult wxOpenAuthorizerInfoResult, WxStoreAuthorizerInfoDO appIdIsExistDO, WxStoreAuthorizerInfoDO brandGuidIsExistDO) throws UnsupportedEncodingException {
        String result = saveOrUpdateAuthorizeInfo(wxOpenAuthorizerInfoResult, 0, appIdIsExistDO.getBrandGuid(), appIdIsExistDO.getGuid(), appIdIsExistDO.getVersion(), appIdIsExistDO.getTemplateMsgId());
        if (!SUCCESS.equals(result)) {
            return handleResult(result);
        }
        if (brandGuidIsExistDO == null) {
            log.info("当前公众号已被其他品牌绑定，品牌guid：{}, 公众号appID：{}", appIdIsExistDO.getBrandGuid(), appIdIsExistDO.getAuthorizerAppid());
            return handleError("当前公众号已被其他品牌绑定，若要使用该公众号，请先解绑");
        } else if (appIdIsExistDO.getBrandGuid().equals(brandGuidIsExistDO.getBrandGuid())) {
            return weChatConfig.getCALL_BACK_SUCCESS_PAGE();
        } else {
            return handleError("当前公众号已被其他品牌绑定，且当前品牌已绑定其他公众号，若要在该品牌上绑定该公众号，请先分别解绑");
        }
    }

    /**
     * 生成授权信息的唯一GUID
     */
    private String generateGuidForAuthInfo(String brandGuid) {
        return redisUtils.generateGuid(ModelName.WX + ":" + brandGuid + ":saveAuthInfo");
    }

    /**
     * 处理授权结果
     */
    private String handleResult(String result) throws UnsupportedEncodingException {
        if (!SUCCESS.equals(result)) {
            return String.format(weChatConfig.getCALL_BACK_ERROR_PAGE(), URLEncoder.encode(result, "UTF-8"));
        }
        return weChatConfig.getCALL_BACK_SUCCESS_PAGE();
    }

    /**
     * 处理错误信息并返回错误页面URL
     */
    private String handleError(String message) throws UnsupportedEncodingException {
        return String.format(weChatConfig.getCALL_BACK_ERROR_PAGE(), URLEncoder.encode(message, "UTF-8"));
    }

    /**
     * 通过授权码获取公众号信息
     * 内存中已保存当前公众号appId，accessToken， refreshToken信息
     * 通过wxThirdOpenConfig.getWxOpenConfigStorage()中，appId为key获取
     *
     * @return 微信SDK封装的DTO
     */
    private WxOpenAuthorizerInfoResult getWxOpenAuthorizerInfo(String queryAuthCode) throws WxErrorException {
        wxStoreComponentConfigService.getAccessToken();
        WxOpenQueryAuthResult wxOpenQueryAuthResult = wxThirdOpenConfig.getWxOpenComponentService().getQueryAuth(queryAuthCode);
        WxOpenAuthorizationInfo wxOpenAuthorizationInfo = wxOpenQueryAuthResult.getAuthorizationInfo();
        WxOpenAuthorizerInfoResult wxOpenAuthorizerInfoResult = wxThirdOpenConfig.getWxOpenComponentService()
                .getAuthorizerInfo(wxOpenAuthorizationInfo.getAuthorizerAppid());
        wxOpenAuthorizerInfoResult.setAuthorizationInfo(wxOpenAuthorizationInfo);
        return wxOpenAuthorizerInfoResult;
    }

    /**
     * 修改或保存公众号授权信息
     *
     * @param wxOpenAuthorizerInfoResult 公众号授权信息
     * @param isDeleted                  是否删除
     * @param brandGuid                  品牌guid
     * @param guid                       guid
     * @param version                    版本号
     */
    private String saveOrUpdateAuthorizeInfo(WxOpenAuthorizerInfoResult wxOpenAuthorizerInfoResult, Integer isDeleted, String brandGuid, String guid, Integer version, String templateId) {
        // 将公众号信息转换成DO
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO =
                wxStoreOpenMapstruct.authorizerInfo2DO(wxOpenAuthorizerInfoResult.getAuthorizerInfo(),
                        wxOpenAuthorizerInfoResult.getAuthorizationInfo());
        wxStoreAuthorizerInfoDO.setQrcodeUrl(getImageSSOUrl(wxStoreAuthorizerInfoDO.getQrcodeUrl(),
                wxStoreAuthorizerInfoDO.getAuthorizerAppid(), "qrCode"));
        wxStoreAuthorizerInfoDO.setHeadImg(getImageSSOUrl(wxStoreAuthorizerInfoDO.getHeadImg(),
                wxStoreAuthorizerInfoDO.getAuthorizerAppid(), "headImg"));
        WxOpenAuthorizationInfo wxOpenAuthorizationInfo = wxOpenAuthorizerInfoResult.getAuthorizationInfo();
        wxStoreAuthorizerInfoDO.setFuncInfo(JacksonUtils.writeValueAsString(wxOpenAuthorizationInfo.getFuncInfo()));
        wxStoreAuthorizerInfoDO.setIsDeleted(1 == isDeleted ? isDeleted : 0);
        Long expiresIn = System.currentTimeMillis() + (wxOpenAuthorizationInfo.getExpiresIn() - 200) * 1000L;
        wxStoreAuthorizerInfoDO.setExpiresIn(expiresIn);
        wxStoreAuthorizerInfoDO.setBrandGuid(brandGuid);
        wxStoreAuthorizerInfoDO.setGuid(guid);
        wxStoreAuthorizerInfoDO.setVersion(ObjectUtils.isEmpty(version) ? 0 : version);
        wxStoreAuthorizerInfoDO.setTemplateMsgId(templateId);
        //创建默认消息模板
        createDefaultMsgTemplate(wxStoreAuthorizerInfoDO);

        wxStoreAuthorizerInfoDO.setTemplateMsgId(null);
        boolean flag = saveOrUpdate(wxStoreAuthorizerInfoDO);
        log.info("公众号信息修改结果:{}", flag);
        // 若公众号授权信息修改或保存成功，则更新公众号权限集
        if (flag) {
            Map<String, Integer> businessInfo = wxOpenAuthorizerInfoResult.getAuthorizerInfo().getBusinessInfo();
            businessInfo.put("is_deleted", 1 == isDeleted ? isDeleted : 0);
            wxStoreAuthorizerBusiInfoService.saveOrUpdateStoreAuthorizeBusiInfo(wxOpenAuthorizationInfo.getAuthorizerAppid(), businessInfo);
        }
        return SUCCESS;
    }

    private void createDefaultMsgTemplate(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) {
        CompletableFuture.runAsync(() -> {
            //排队消息模板
            wxMpTemplateService.createMsgTemplate(wxStoreAuthorizerInfoDO, WxTemplateTypeEnum.QUEUE_TEMPLATE.getCode());
            //会员余额消费模板消息
            wxMpTemplateService.createMsgTemplate(wxStoreAuthorizerInfoDO, WxTemplateTypeEnum.MEMBER_AMOUNT_TEMPLATE.getCode());

        }).exceptionally(throwable -> {
            log.error("创建默认消息模板失败", throwable);
            return null;
        });

    }

    /**
     * 将微信图片保存到sso服务器
     * 解决微信防盗链问题：商户后台查看微信图片出现"此图片来自微信公众平台未经允许不可引用"
     *
     * @param originalUrl 原url
     * @param appId       公众号appId
     * @param type        图片类型（头像/二维码），用于拼装图片名字
     */
    private String getImageSSOUrl(String originalUrl, String appId, String type) {
        try {
            if (!ObjectUtils.isEmpty(originalUrl)) {
                BufferedImage bufferedImage = ImageIO.read(new URL(originalUrl));
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                ImageIO.write(bufferedImage, "jpg", byteArrayOutputStream);
                FileDto fileDto = new FileDto();
                fileDto.setFileName(appId + "_" + type + ".jpg");
                fileDto.setFileContent(SecurityManager.entryptBase64(byteArrayOutputStream.toByteArray()));
                return baseClientService.upload(fileDto);
            }
        } catch (IOException e) {
            throw new BusinessException("公众号二维码图片拉取失败，e:{}", e);
        }
        return null;
    }

    @Override
    public List<WxBrandAuthRespDTO> getBrandAuthList() {
        List<BrandDTO> brandDTOList = organizationClientService.queryBrandList();
        if (ObjectUtils.isEmpty(brandDTOList)) {
            return null;
        }
        Map<String, BrandDTO> brandDTOMap = brandDTOList.stream()
                .collect(Collectors.toMap(BrandDTO::getGuid, Function.identity()));
        Set<String> brandGuidSet = brandDTOMap.keySet();
        List<WxStoreAuthorizerInfoDO> wxStoreAuthorizerInfoDOList =
                list(new LambdaQueryWrapper<WxStoreAuthorizerInfoDO>()
                        .in(WxStoreAuthorizerInfoDO::getBrandGuid, brandGuidSet)
                        .eq(WxStoreAuthorizerInfoDO::getIsDeleted, 0));
        Map<String, WxStoreAuthorizerInfoDO> wxStoreAuthorizerInfoDOMap =
                wxStoreAuthorizerInfoDOList.stream()
                        .collect(Collectors.toMap(WxStoreAuthorizerInfoDO::getBrandGuid, Function.identity()));
        List<WxBrandAuthRespDTO> wxBrandAuthRespDTOList = new ArrayList<>();
        brandDTOMap.forEach((brandGuid, brandDTO) -> {
            WxBrandAuthRespDTO wxBrandAuthRespDTO = new WxBrandAuthRespDTO();
            WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoDOMap.get(brandGuid);
            if (!ObjectUtils.isEmpty(wxStoreAuthorizerInfoDO)) {
                wxBrandAuthRespDTO = parseDO2DTO(wxStoreAuthorizerInfoDO);
            }
            wxBrandAuthRespDTO.setBrandGuid(brandGuid);
            wxBrandAuthRespDTO.setBrandName(brandDTO.getName());
            wxBrandAuthRespDTOList.add(wxBrandAuthRespDTO);
        });
        return wxBrandAuthRespDTOList;
    }

    @Override
    public String sendShortMessage(WxSendShortMsgReqDTO wxSendShortMsgReqDTO) {
        // 封装短信请求
        MessageDTO messageDTO = createMessageDTO(wxSendShortMsgReqDTO);
        // 发送成功返回true
        boolean isSent = baseClientService.sendMessage(messageDTO);
        if (isSent) {
            String messageKey = cacheVerificationCode(wxSendShortMsgReqDTO, messageDTO.getShortMessage().getParams().get("Code"));
            log.info("已发送短信验证码，redisKey:{}, code:{}", messageKey, messageDTO.getShortMessage().getParams().get("Code"));
            return messageKey;
        }
        log.info("短信验证码发送失败，请稍后重试");
        throw new BusinessException("短信验证码发送失败，请稍后重试");
    }

    /**
     * 创建短信消息DTO
     */
    private MessageDTO createMessageDTO(WxSendShortMsgReqDTO wxSendShortMsgReqDTO) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessageType(MessageType.SHORT_MESSAGE);
        ShortMessageDTO shortMessageDTO = new ShortMessageDTO();
        Integer code = generateVerificationCode();
        Map<String, String> param = new HashMap<>();
        param.put("Code", code.toString());
        shortMessageDTO.setParams(param);
        shortMessageDTO.setPhoneNumber(wxSendShortMsgReqDTO.getTel());
        shortMessageDTO.setShortMessageType(ShortMessageType.UNBIND_VERCODE);
        messageDTO.setShortMessage(shortMessageDTO);
        return messageDTO;
    }

    /**
     * 生成验证码
     */
    private Integer generateVerificationCode() {
        return (int) ((Math.random() * 9 + 1) * 100000);
    }

    /**
     * 缓存验证码
     */
    private String cacheVerificationCode(WxSendShortMsgReqDTO wxSendShortMsgReqDTO, String code) {
        String messageKey = ModelName.WX + ":" + wxSendShortMsgReqDTO.getUserGuid() + ":"
                + wxSendShortMsgReqDTO.getUserName() + ":" + wxSendShortMsgReqDTO.getTel() + ":" + "SHORT-MESSAGE";
        redisUtils.setEx(messageKey, code, 60L * 5, TimeUnit.SECONDS);
        return messageKey;
    }

    @Override
    public Integer unBandBrand(WxUnBandReqDTO wxUnBandReqDTO) {
        // 验证短信验证码
        if (!validateVerificationCode(wxUnBandReqDTO)) {
            return WxUnBandRespEnum.ERROR_CODE.getCode();
        }
        // 删除验证码缓存
        redisUtils.delete(wxUnBandReqDTO.getMessageKey());
        // 更新解绑操作人员信息
        return updateUnBandInfo(wxUnBandReqDTO);
    }

    /**
     * 验证短信验证码
     */
    private boolean validateVerificationCode(WxUnBandReqDTO wxUnBandReqDTO) {
        String localCode = (String) redisUtils.get(wxUnBandReqDTO.getMessageKey());
        if (StringUtils.isEmpty(localCode)) {
            return false;
        }
        return wxUnBandReqDTO.getCode().equals(localCode);
    }

    /**
     * 更新解绑操作人员信息
     */
    private Integer updateUnBandInfo(WxUnBandReqDTO wxUnBandReqDTO) {
        try {
            WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = getById(wxUnBandReqDTO.getBrandGuid());
            String[] userInfoArray = wxUnBandReqDTO.getMessageKey().split(":");
            wxStoreAuthorizerInfoDO.setUnBandUserGuid(userInfoArray[1]);
            wxStoreAuthorizerInfoDO.setUnBandUserName(userInfoArray[2]);
            wxStoreAuthorizerInfoDO.setUnBandUserTel(userInfoArray[3]);
            wxStoreAuthorizerInfoDO.setUnBandTime(LocalDateTime.now());
            updateById(wxStoreAuthorizerInfoDO);
            removeById(wxStoreAuthorizerInfoDO.getBrandGuid());
            return WxUnBandRespEnum.SUCCESS.getCode();
        } catch (Exception e) {
            log.error("公众号解绑失败：", e);
            return WxUnBandRespEnum.ERROR_CODE.getCode();
        }
    }

    /**
     * 将DO转换成DTO，并手动封装部分参数
     */
    private WxBrandAuthRespDTO parseDO2DTO(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) {
        WxBrandAuthRespDTO wxBrandAuthRespDTO =
                new WxBrandAuthRespDTO();
        wxBrandAuthRespDTO.setBrandGuid(wxStoreAuthorizerInfoDO.getBrandGuid());
        wxBrandAuthRespDTO.setMpAppId(wxStoreAuthorizerInfoDO.getAuthorizerAppid());
        wxBrandAuthRespDTO.setNickName(wxStoreAuthorizerInfoDO.getNickName());
        wxBrandAuthRespDTO.setQrcodeUrl(wxStoreAuthorizerInfoDO.getQrcodeUrl());
        wxBrandAuthRespDTO.setMpType(WxVerifyTypeEnum.getDescByCode(wxStoreAuthorizerInfoDO.getVerifyTypeInfo())
                + WxServiceTypeEnum.getDescByCode(wxStoreAuthorizerInfoDO.getServiceTypeInfo()));
        if (Objects.nonNull(wxStoreAuthorizerInfoDO.getOperSubjectGuid())) {
            wxBrandAuthRespDTO.setMultiMemberGuid(wxStoreAuthorizerInfoDO.getOperSubjectGuid());
        }
        List<Integer> funcInfoList = JacksonUtils.toObjectList(Integer.class, wxStoreAuthorizerInfoDO.getFuncInfo());
        wxBrandAuthRespDTO.setPermissions(funcInfoList);
        wxBrandAuthRespDTO.setAlreadyBand(1);
        return wxBrandAuthRespDTO;
    }

    @Override
    public WxBrandAuthRespDTO getByBrandGuid(String brandGuid) {
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = getById(brandGuid);
        if (ObjectUtils.isEmpty(wxStoreAuthorizerInfoDO)) {
            throw new BusinessException("当前门店所属品牌暂未绑定公众号，请先绑定公众号");
        }
        return parseDO2DTO(wxStoreAuthorizerInfoDO);
    }

    @Override
    public String getQrCodeUrl(WxQrCodeUrlQuery wxQrCodeUrlQuery) throws WxErrorException {
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = getById(wxQrCodeUrlQuery.getBrandGuid());
        if (ObjectUtils.isEmpty(wxStoreAuthorizerInfoDO)) {
            throw new BusinessException("当前品牌暂未绑定公众号，请绑定后重试");
        }
        // 生成二维码SceneStr
        String sceneStr = generateSceneStr(wxQrCodeUrlQuery);
        log.info("请求微信二维码参数值，sceneStr:{}", sceneStr);
        // 获取二维码URL
        return generateQrCodeUrl(wxStoreAuthorizerInfoDO, sceneStr);
    }

    /**
     * 生成二维码SceneStr
     */
    private String generateSceneStr(WxQrCodeUrlQuery wxQrCodeUrlQuery) {
        return wxQrCodeInfoService.getSceneStr(wxQrCodeUrlQuery);
    }

    /**
     * 生成二维码URL
     */
    private String generateQrCodeUrl(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO, String sceneStr) throws WxErrorException {
        WxMpQrcodeService wxMpQrcodeService = new WxMpQrcodeServiceImpl(wxSaasMpService.getWxMpService(wxStoreAuthorizerInfoDO));
        WxMpQrCodeTicket wxMpQrCodeTicket = wxMpQrcodeService.qrCodeCreateLastTicket(sceneStr);
        log.info("已获取到微信二维码，wxMpQrCodeTicket:{}", wxMpQrCodeTicket);
        return wxMpQrCodeTicket.getUrl();
    }

    @Override
    public String getQueueQrCodeUrl(WxQueueInfoReqDTO wxQueueInfoReqDTO) throws WxErrorException {
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(wxQueueInfoReqDTO.getStoreGuid());
        WxStoreAuthorizerInfoDO byId = getById(storeDTO.getBelongBrandGuid());
        String qrCodeUrl;
        redisUtils.setEx(wxQueueInfoReqDTO.getQueueGuid(), wxQueueInfoReqDTO, 43210, TimeUnit.SECONDS);
        if (ObjectUtils.isEmpty(byId) || !(WxVerifyTypeEnum.WECHAT_AUTH.getCode().equals(byId.getVerifyTypeInfo()) && WxServiceTypeEnum.SERVICE_NUMBER.getCode().equals(byId.getServiceTypeInfo()))) {
            // 当前商户无公众号，或者商户公众号不是微信认证服务号，则使用默认排队二维码
            //todo 优化代码
            String respUrl = String.format(weChatConfig.getQUEUE_DETAIL_PAGE(), wxQueueInfoReqDTO.getQueueGuid(), wxQueueInfoReqDTO.getEnterpriseGuid());
            qrCodeUrl = wxStoreMpService.shortenUrl(respUrl, null, null);
        } else {
            WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = getById(byId.getBrandGuid());
            // 代公众号生成带参数二维码（临时二维码）
            WxMpQrcodeService wxMpQrcodeService = new WxMpQrcodeServiceImpl(wxSaasMpService.getWxMpService(wxStoreAuthorizerInfoDO));
            String sceneStr = BusinessName.QUEUE_INFO + "," + wxQueueInfoReqDTO.getEnterpriseGuid() + "," + wxQueueInfoReqDTO.getQueueGuid();
            WxMpQrCodeTicket wxMpQrCodeTicket = wxMpQrcodeService.qrCodeCreateTmpTicket(sceneStr, 43200);
            qrCodeUrl = wxMpQrCodeTicket.getUrl();
        }
        log.info("微信二维码url:{}", qrCodeUrl);
        return qrCodeUrl;
    }

    @Override
    public boolean updateWxAuthorizeInfo(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) {
        log.info("修改微信公众号信息：wxStoreAuthorizerInfoDO:{}", JacksonUtils.writeValueAsString(wxStoreAuthorizerInfoDO));
        return update(wxStoreAuthorizerInfoDO, new LambdaQueryWrapper<WxStoreAuthorizerInfoDO>()
                .eq(WxStoreAuthorizerInfoDO::getAuthorizerAppid, wxStoreAuthorizerInfoDO.getAuthorizerAppid()));
    }

    @Override
    public WxStoreAuthorizerInfoDO getByAppId(String appId) {
        return getOne(new LambdaQueryWrapper<WxStoreAuthorizerInfoDO>().eq(WxStoreAuthorizerInfoDO::getAuthorizerAppid, appId));
    }

    @Override
    public WxStoreAuthorizerInfoDO getByBrandId(String brandId) {
        LambdaQueryWrapper<WxStoreAuthorizerInfoDO> eq = new LambdaQueryWrapper<WxStoreAuthorizerInfoDO>()
                .eq(WxStoreAuthorizerInfoDO::getBrandGuid, brandId);
        eq.orderByDesc(WxStoreAuthorizerInfoDO::getId).last("limit 1");
        return getOne(eq);
    }

    @Override
    public Boolean bindSubject(WxOperSubjectBrandReqDTO wxOperSubjectBrandReqDTO) {
        log.info("绑定微信公众号与运营主体信息：bindSubject:{}", JacksonUtils.writeValueAsString(wxOperSubjectBrandReqDTO));
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = getByBrandId(wxOperSubjectBrandReqDTO.getBrandGuid());
        wxStoreAuthorizerInfoDO.setOperSubjectGuid(wxOperSubjectBrandReqDTO.getOperSubjectGuid());
        wxStoreAuthorizerInfoDO.setIsAlliance(wxOperSubjectBrandReqDTO.getIsAlliance());
        return update(wxStoreAuthorizerInfoDO, new LambdaQueryWrapper<WxStoreAuthorizerInfoDO>()
                .eq(WxStoreAuthorizerInfoDO::getAuthorizerAppid, wxStoreAuthorizerInfoDO.getAuthorizerAppid()));
    }
}
