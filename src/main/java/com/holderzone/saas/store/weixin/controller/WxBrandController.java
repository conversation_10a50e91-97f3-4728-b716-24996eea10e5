package com.holderzone.saas.store.weixin.controller;

import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberCardList;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxBrandController
 * @date 2019/10/11 14:53
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@Api("品牌信息")
@RestController
@RequestMapping("/wx_brand")
@Slf4j
public class WxBrandController {

    @Autowired
    private WxStoreSessionDetailsService wxStoreSessionDetailsService;
    @Autowired
    DynamicHelper dynamicHelper;

    @PostMapping("/get_by_brand_guid")
    @ApiOperation("通过品牌guid获取公众号绑定信息")
    public BrandDTO getBrandDetail(@RequestBody RequestQueryMemberCardList memberCardListQueryReqDTO) {
        dynamicHelper.changeDatasource(memberCardListQueryReqDTO.getEnterpriseGuid());
        return wxStoreSessionDetailsService.getBrandDetail(memberCardListQueryReqDTO.getBrandGuid());
    }
}