package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.saas.store.dto.weixin.WxMemberCenterCardRespDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.Optional;

@Service
@Slf4j
public class WxMemberOverviewServiceImpl implements WxMemberOverviewService {

	private final WxMemberCenterCardService wxMemberCenterCardService;

	private final WxStoreSessionDetailsService wxStoreSessionDetailsService;

	private final QueueMemberModelItemServiceImpl queueMemberModelItemService;

	private final VolumeMemberModelItemServiceImpl volumeMemberModelItemService;

	private final DiningMemberModelItemServiceImpl diningMemberModelItemService;

	private final MemberClientService memberClientService;


	private final WxUserRecordService wxUserRecordService;


	@Autowired
	public WxMemberOverviewServiceImpl(WxMemberCenterCardService wxMemberCenterCardService, WxStoreSessionDetailsService wxStoreSessionDetailsService, QueueMemberModelItemServiceImpl queueMemberModelItemService, VolumeMemberModelItemServiceImpl volumeMemberModelItemService, DiningMemberModelItemServiceImpl diningMemberModelItemService, MemberClientService memberClientService, WxUserRecordService wxUserRecordService) {
		this.wxMemberCenterCardService = wxMemberCenterCardService;
		this.wxStoreSessionDetailsService = wxStoreSessionDetailsService;
		this.queueMemberModelItemService = queueMemberModelItemService;
		this.volumeMemberModelItemService = volumeMemberModelItemService;
		this.diningMemberModelItemService = diningMemberModelItemService;
		this.memberClientService = memberClientService;
		this.wxUserRecordService = wxUserRecordService;
	}

	@Override
	public WxMemberOverviewRespDTO allModel(WxStoreConsumerDTO wxStoreConsumerDTO) {
		UserContext userContext = getUserInfo(wxStoreConsumerDTO);
		CompletableFuture<WxMemberCenterCardRespDTO> memberCenterFuture = fetchMemberCenterCardAsync(wxStoreConsumerDTO, userContext);

		ResponseMemberInfo memberInfo = fetchMemberInfo(wxStoreConsumerDTO);
		wxUserRecordService.updateUserInfo(wxStoreConsumerDTO.getOpenId());

		List<WxMemberOverviewModelDTO> subModelList = fetchSubModelList(wxStoreConsumerDTO, userContext);

		CompletableFuture.allOf(memberCenterFuture).join();
		WxMemberCenterCardRespDTO wxMemberCenterCardRespDTO = memberCenterFuture.join();

		return buildOverviewResponse(memberInfo, subModelList, wxMemberCenterCardRespDTO);
	}

	private CompletableFuture<WxMemberCenterCardRespDTO> fetchMemberCenterCardAsync(WxStoreConsumerDTO wxStoreConsumerDTO, UserContext userContext) {
		return CompletableFuture.supplyAsync(() -> {
			dynamicData(userContext);
			return wxMemberCenterCardService.cardList(wxStoreConsumerDTO);
		}).exceptionally(e -> {
			log.error("Failed to fetch member center card", e);
			throw new BusinessException("会员卡信息查询失败:" + JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
		});
	}

	private ResponseMemberInfo fetchMemberInfo(WxStoreConsumerDTO wxStoreConsumerDTO) {
		return wxStoreSessionDetailsService.getMemberInfo(wxStoreConsumerDTO.getEnterpriseGuid(), wxStoreConsumerDTO.getOpenId());
	}

	private List<WxMemberOverviewModelDTO> fetchSubModelList(WxStoreConsumerDTO wxStoreConsumerDTO, UserContext userContext) {
		if (wxStoreConsumerDTO.getIsLogin()) {
			return fetchLoggedInSubModelList(wxStoreConsumerDTO, userContext);
		} else {
			return fetchLoggedOutSubModelList();
		}
	}

	private List<WxMemberOverviewModelDTO> fetchLoggedInSubModelList(WxStoreConsumerDTO wxStoreConsumerDTO, UserContext userContext) {
		List<AbstractMemberModelItemService> memberModelItemServiceList = initialsSubModel();
		return memberModelItemServiceList.stream()
			.map(service -> CompletableFuture.supplyAsync(() -> {
				dynamicData(userContext);
				return service.getWxMemberOverviewModel(wxStoreConsumerDTO);
			}))
			.collect(Collectors.toList())
			.stream()
			.map(future -> future.exceptionally(e -> {
				log.warn("getWxMemberOverviewModel异常", e);
				return new WxMemberOverviewModelDTO(-1, "error", 0);
			}).join())
			.filter(model -> model.getModelId() != -1)
			.sorted(Comparator.comparing(WxMemberOverviewModelDTO::getModelId))
			.collect(Collectors.toList());
	}

	private List<WxMemberOverviewModelDTO> fetchLoggedOutSubModelList() {
		List<WxMemberOverviewModelDTO> subModelList = new ArrayList<>();
		initialLogout(subModelList);
		return subModelList;
	}

	private WxMemberOverviewRespDTO buildOverviewResponse(ResponseMemberInfo memberInfo, List<WxMemberOverviewModelDTO> subModelList, WxMemberCenterCardRespDTO wxMemberCenterCardRespDTO) {
		boolean hasPhoneNum = memberInfo != null && !StringUtils.isEmpty(memberInfo.getPhoneNum());
		String phoneNum = hasPhoneNum ? memberInfo.getPhoneNum() : "";
		String memberInfoGuid = memberInfo != null ? memberInfo.getMemberInfoGuid() : "";
		return new WxMemberOverviewRespDTO(hasPhoneNum, phoneNum, subModelList, wxMemberCenterCardRespDTO, memberInfoGuid);
	}

	private void initialLogout(List<WxMemberOverviewModelDTO> subModelList) {
		subModelList.add(new WxMemberOverviewModelDTO(1, "我的券", 0));
		subModelList.add(new WxMemberOverviewModelDTO(2, "我的排队", 0));
		subModelList.add(new WxMemberOverviewModelDTO(5, "我的就餐", 0));
	}

	private UserContext getUserInfo(WxStoreConsumerDTO wxStoreConsumerDTO) {
		UserContext userContext = Optional.ofNullable(UserContextUtils.get()).orElse(new UserContext());
		userContext.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
		UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
			EnterpriseIdentifier.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
		return userContext;
	}

	private List<AbstractMemberModelItemService> initialsSubModel() {
		List<AbstractMemberModelItemService> wxMemberOverviewModelDTOS = new ArrayList<>();
		wxMemberOverviewModelDTOS.add(volumeMemberModelItemService);
		wxMemberOverviewModelDTOS.add(queueMemberModelItemService);
		wxMemberOverviewModelDTOS.add(diningMemberModelItemService);
		return wxMemberOverviewModelDTOS;
	}

	private void dynamicData(UserContext userContext) {
		UserContextUtils.put(userContext);
		EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
	}
}
