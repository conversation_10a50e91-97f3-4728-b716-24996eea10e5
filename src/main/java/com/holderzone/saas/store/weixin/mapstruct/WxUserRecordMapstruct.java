package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxUserRecordDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxOrderRecordMapstruct
 * @date 2019/4/3
 */
@Component
@Mapper(componentModel = "spring")
public interface WxUserRecordMapstruct {

	WxUserRecordMapstruct INSTANCE = Mappers.getMapper(WxUserRecordMapstruct.class);


	@Mappings({
			@Mapping(target = "guid",source = "consumerGuid"),
			@Mapping(target = "openId",source = "openId"),
	})
	WxUserRecordDO getWxorderRecord(WxStoreConsumerDTO wxStoreConsumerDTO);
	
	
	WxUserRecordDTO getWxUserRecordDTO(WxUserRecordDO wxUserRecordDO);

	@Mappings({
			@Mapping(target = "consumerGuid",source = "guid")
	})
	WxStoreConsumerDTO getWxStoreConsumer(WxUserRecordDO wxUserRecordDO);



	List<WxStoreConsumerDTO> getWxStoreConsumerList(List<WxUserRecordDO> wxUserRecordDOS);

	@Mappings({
			@Mapping(target = "orderGuid",source = "wxStoreAdvanceConsumerReqDTO.tradeOrderGuid"),
			@Mapping(target = "storeGuid",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid"),
			@Mapping(target = "areaGuid",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.areaGuid"),
			@Mapping(target = "tableGuid",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.diningTableGuid"),
			@Mapping(target = "orderMode",source = "wxStoreAdvanceConsumerReqDTO.orderModel"),
	})
	WxOrderRecordDO findWxOrderRecord(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);
}
