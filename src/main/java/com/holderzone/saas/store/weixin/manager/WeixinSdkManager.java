package com.holderzone.saas.store.weixin.manager;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.holderzone.saas.store.dto.weixin.exception.CheckBusinessException;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.service.WxSaasMpService;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.open.api.WxOpenComponentService;

@Component
@Slf4j
public class WeixinSdkManager {

	/**
	 * 默认公众号wxService
	 */
	@Resource
	private WxMpService wxMpService;

	/**
	 * 公众号支付wxService
	 */
	@Resource
	@Qualifier(value = "wxH5MpService")
	private WxMpService wxH5MpService;

	/**
	 * 三方平台管理的公众号
	 */
	@Resource
	private WxSaasMpService wxSaasMpService;

	/**
	 * 小掌工作台
	 */
	@Resource
	@Qualifier(value = "workBenchService")
	private WxMpService workBenchService;

	@Resource
	private WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;

	@Resource
	private WxOpenComponentService wxOpenComponentService;

	@Resource
	@Qualifier(value = "wxBiBossMpService")
	private WxMpService wxBiBossMpService;

	private WxMpService buildOpenWxMpService(String appId) throws WxErrorException {
		WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getByAppId(appId);
		return wxSaasMpService.getWxMpService(wxStoreAuthorizerInfoDO);
	}

	/**
	 * Returns the appropriate WxMpService based on the type.
	 * 1: Default public account, 2: Workbench, 3: H5 public account, 4: BiBoss public account
	 * @param type the type of service required
	 * @return the corresponding WxMpService
	 */
	public WxMpService buildWxMpService(int type) {
		switch (type) {
			case 1:
				return wxMpService;
			case 2:
				return workBenchService;
			case 3:
				return wxH5MpService;
			case 4:
				return wxBiBossMpService;
			default:
				log.warn("Invalid type provided: {}", type);
				return null;
		}
	}
}
