package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.queue.ItemGuidDTO;
import com.holderzone.saas.store.dto.weixin.WxInQueueReqDTO;
import com.holderzone.saas.store.dto.weixin.WxQueueDetailDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxInQueueRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxQueueConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxTotalQueueDTO;
import com.holderzone.saas.store.weixin.service.WxQueueConfigService;
import com.holderzone.saas.store.weixin.service.WxQueueService;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueConfigController
 * @date 2019/05/13 10:55
 * @description 门店微信线上排队配置Controller
 * @program holder-saas-store
 */
@RestController
@Slf4j
@RequestMapping("/wx_queue")
@Api("门店微信线上排队配置Controller")
public class WxQueueConfigController {

	@Autowired
	WxQueueConfigService wxQueueConfigService;
	@Autowired
	WxQueueService wxQueueService;

	@Autowired
	WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;

	@PostMapping("/page_config")
	public Page<WxQueueConfigDTO> pageConfig(@RequestBody WxStorePageReqDTO wxStorePageReqDTO) {
		log.info("微信服务：分页查询排队配置信息入参：{}", JacksonUtils.writeValueAsString(wxStorePageReqDTO));
		return wxQueueConfigService.pageQueueConfig(wxStorePageReqDTO);
	}

	@PostMapping("/get_by_guid")
	public WxQueueConfigDTO getByGuid(@RequestBody WxQueueConfigDTO wxQueueConfigDTO) {
		log.info("微信服务：查询排队配置详情入参：{}", JacksonUtils.writeValueAsString(wxQueueConfigDTO));
		return wxQueueConfigService.getQueueConfig(wxQueueConfigDTO);
	}

	@PostMapping("/update_by_guid")
	public Boolean updateByGuid(@RequestBody WxQueueConfigDTO wxQueueConfigDTO) {
		log.info("微信服务：修改排队配置请求入参：{}", JacksonUtils.writeValueAsString(wxQueueConfigDTO));
		return wxQueueConfigService.updateQueueConfig(wxQueueConfigDTO);
	}

	@PostMapping("/batch_update")
	public Boolean batchUpdate(@RequestBody WxQueueConfigUpdateBatchReqDTO wxQueueConfigUpdateBatchReqDTO) {
		log.info("微信服务：批量修改排队配置请求入参：{}", JacksonUtils.writeValueAsString(wxQueueConfigUpdateBatchReqDTO));
		return wxQueueConfigService.updateQueueConfigBatch(wxQueueConfigUpdateBatchReqDTO);
	}

	@PostMapping("/get_queue_qr_code")
	@ApiOperation("获取微信二维码")
	public String getQueueQrCode(@RequestBody WxQueueInfoReqDTO wxQueueInfoReqDTO) throws WxErrorException {
		log.info("微信服务：获取排队二维码请求入参：{}", JacksonUtils.writeValueAsString(wxQueueInfoReqDTO));
		return wxStoreAuthorizerInfoService.getQueueQrCodeUrl(wxQueueInfoReqDTO);
	}

	@PostMapping("/detail")
	public WxQueueDetailDTO detailDTO(@RequestBody WxPortalReqDTO wxPortalReqDTO) {
		log.info("微信服务：获取排队详情请求参数：{}", JacksonUtils.writeValueAsString(wxPortalReqDTO));
		return wxQueueService.getDetail(wxPortalReqDTO);
	}

	@PostMapping("/in_queue")
	public WxInQueueRespDTO inQueue(@RequestBody WxInQueueReqDTO wxInQueueReqDTO) {
		log.info("微信服务：立即取号请求参数：{}", JacksonUtils.writeValueAsString(wxInQueueReqDTO));
		return wxQueueService.inQueueByWx(wxInQueueReqDTO);
	}

	@PostMapping("/call_up_notify")
	public void callUpNotify(@RequestBody List<ItemGuidDTO> itemGuidDTOS) {
		log.info("微信服务：叫号通知请求入参：{}", JacksonUtils.writeValueAsString(itemGuidDTOS));
		wxQueueService.callUpNotify(itemGuidDTOS);
	}

	@PostMapping("/total_detail")
	public WxTotalQueueDTO getTotalDetail(@RequestBody WxPortalReqDTO wxPortalReqDTO) {
		log.info("微信服务：获取门店排队队列详情请求入参：{}", JacksonUtils.writeValueAsString(wxPortalReqDTO));
		return wxQueueService.getTotalDetail(wxPortalReqDTO);
	}
}
