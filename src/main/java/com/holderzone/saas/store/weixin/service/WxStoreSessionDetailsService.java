package com.holderzone.saas.store.weixin.service;

import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardAll;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.BrandStoreDetailDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.weixin.execption.CorrectResult;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreSesssionDetailsService
 * @date 2019/4/26
 */
public interface WxStoreSessionDetailsService {

	void saveOrderGuid(String tableGuid,String orderGuid);

	void delOrderGuid(String tableGuid);

	String getOrderGuid(String tableGuid);

	void saveFastOrderGuid(String storeGuid, String openId, String orderGuid);

	String getFastOrderGuid(String storeGuid, String openId);

	void saveOrderState(String tableGuid, Integer orderState);

	Integer getOrderState(String tableGuid);

	void delOrderState(String tableGuid);

	void updateMerchantBatchGuid( String key, String merchatBatchGuid);

	String getMerchantBatchGuid(String key);

	void delMerchantBatchGuid(String key);

	void delDinnerGuestsCount(String diningTableGuid);

	void updateDinnerGuestsCount(String tableGuid, Integer count);

	Integer getDinnerGuestsCount(String tableGuid);

	void savePaidUser(String storeGuid, String openId,WxH5PayReqDTO wxH5PayReqDTO);

	void delPaidUser(String storeGuid, String openId);

	WxH5PayReqDTO getPaidUser(String storeGuid, String openId);

	void saveTablePaidUser(WxStoreConsumerDTO wxStoreConsumerDTO);

	void delTablePaidUser(String tableGuid);

	WxStoreConsumerDTO getTablePaidUser(String tableGuid);


	WxStoreConsumerDTO getBrandInfo(String storeGuid);

	void saveFirstPerson(WxStoreConsumerDTO wxStoreConsumerDTO);

	void delFirstPerson(String openId);

	WxStoreConsumerDTO getFirstPerson(String opendId);

	void saveTableToken(String tableGuid);

	void delTableToken(String tableGuid);

	String getTableToken(String tableGuid);

	void savePrepay(WxPrepayReqDTO wxPrepayReqDTO);

	void delPrepay(String storeGuid, String openId);

	WxPrepayReqDTO getPrepay(String storeGuid, String openId);

	ResponseMemberAndCardInfoDTO getMemberInfoAndCardList(String enterpriseGuid, String storeGuid, String openId);

	CorrectResult<DineinOrderDetailRespDTO> orderDetails(String orderGuid);

	CorrectResult<DineinOrderDetailRespDTO> calculateDetails(String orderGuid, String openId, String memberInfoCardGuid, Integer memberIntegral, String volumeCode);

	CorrectResult<DineinOrderDetailRespDTO> calculateDetails2(String orderGuid, String openId, String memberInfoCardGuid, Integer memberIntegral, String volumeCode);

	String getOrderGuid(String storeGuid, String tableGuid, String openId);

	ResponseMemberInfo getMemberInfo(String enterpriseGuid, String openId);

	ResponseMemberCardAll getMemberCard(String enterpriseGuid, String brandGuid, String openId);

	Integer getMemberValidVolumeNumber(String enterpriseGuid, String brandGuid, String openId);

	BrandDTO getBrandDetail(String brandGuid);

	TableDTO getTableDetail(String tableGuid);

	StoreDTO getStoreDetail(String storeGuid);

	BrandDTO getBrandInfoDetails(String storeGuid);

	EnterpriseDTO getEnterpriseDetail();

	/**
	 * 支付回调商品
	 * @param orderGuid 订单id
	 * @param weChatPayReqDTO 回调对象
	 */
	void savePayCallBack(String orderGuid, WeChatPayReqDTO weChatPayReqDTO);

	/**
	 * 删除支付回调缓存
	 * @param orderGuid 订单id
	 */
	void delPayCallBack(String orderGuid);

	/**
	 * @param orderGuid 订单id
	 * @return 获取支付回调对象
	 */
	WeChatPayReqDTO getPayCallBack(String orderGuid);


    BrandStoreDetailDTO getStoreBrandDetail(String storeGuid, String brandGuid);
}
