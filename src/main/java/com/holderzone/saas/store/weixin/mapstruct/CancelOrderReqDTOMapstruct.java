package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className CancelOrderReqDTOMapstruct
 * @date 2019/3/29
 */
@Component
@Mapper(componentModel = "spring")
public interface CancelOrderReqDTOMapstruct {

	default boolean isFastFood(Integer orderModel){
		return orderModel==1;
	}

	@Mappings({
			@Mapping(target = "tableGuid",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.diningTableGuid"),
			@Mapping(target = "orderGuid",source = "tradeOrderGuid"),
			@Mapping(target = "deviceId",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.openId"),
			@Mapping(target = "enterpriseGuid",source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.enterpriseGuid"),
			@Mapping(target = "fastFood",expression = "java(isFastFood(wxStoreAdvanceConsumerReqDTO.getOrderModel()))")
	})
	CancelOrderReqDTO getCancelOrderReq(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);
}
