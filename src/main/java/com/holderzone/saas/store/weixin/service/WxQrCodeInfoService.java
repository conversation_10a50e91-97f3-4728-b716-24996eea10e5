package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.req.WxQueueInfoReqDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQrCodeInfoService
 * @date 2019/03/22 11:54
 * @description 微信二维码参数信息Service
 * @program holder-saas-store
 */
public interface WxQrCodeInfoService {
    /**
     * 生成带参数二维码场景字符串
     *
     * @param wxQrCodeUrlQuery
     * @return 场景字符串，用于扫码后获取门店、桌台等信息
     * @throws UnsupportedEncodingException
     */
    String getSceneStr(WxQrCodeUrlQuery wxQrCodeUrlQuery);

    /**
     * 通过guid查询当前二维码对应信息
     *
     * @param guid 穿透字段中的guid
     * @return
     */
    WxQrCodeInfoDO getWxQrCodeInfoByGuid(String guid);
    
    public WxQrCodeInfoDO getCacheWxQrCodeInfoByGuid(String guid) ;
}
