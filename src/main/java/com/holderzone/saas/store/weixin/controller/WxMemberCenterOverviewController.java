package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxMemberOverviewService;
import com.holderzone.saas.store.weixin.service.impl.QueueMemberModelItemServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/wx_member_center_overview")
@Api("会员中心主页，最新使用")
@Slf4j
public class WxMemberCenterOverviewController {

	private final WxMemberOverviewService wxMemberOverviewService;

	@Autowired
	private QueueMemberModelItemServiceImpl queueMemberModelItemService;

	@Autowired
	public WxMemberCenterOverviewController(WxMemberOverviewService wxMemberOverviewService) {
		this.wxMemberOverviewService = wxMemberOverviewService;
	}

	/**
	 * 最新使用的
	 * @param wxStoreConsumerDTO
	 * @return
	 */
	@PostMapping(value = "/all_model")
	@ApiOperation("查询主页所有子模块，微信会员首页主数据")
	public WxMemberOverviewRespDTO allModel(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO){
		log.info("查询所有模块入参:{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
		return wxMemberOverviewService.allModel(wxStoreConsumerDTO);
	}

	@PostMapping(value = "/queue_config")
	@ApiOperation("排队测试")
	public WxMemberOverviewModelDTO queueConfig(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO) {
		EnterpriseIdentifier.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
		return queueMemberModelItemService.getWxMemberOverviewModel(wxStoreConsumerDTO);
	}

}
