package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.ShortMsgPollingRespDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickJHPayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickOrderRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickOrderClientService
 * @date 2019/03/15 17:23
 * @description 微信桌贴购买ClientService
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-cloud-market", fallbackFactory = WxStickOrderClientService.WxStuckOrderFallBack.class)
public interface WxStickOrderClientService {

    @PostMapping("/market/wechat/order")
    WxStickJHPayRespDTO order(WxStickModelOrderDTO wxStickModelOrderDTO);

    @PostMapping("/market/jh/polling")
    ShortMsgPollingRespDTO polling(WxStickOrderRespDTO wxStickOrderRespDTO);

    @Component
    @Slf4j
    class WxStuckOrderFallBack implements FallbackFactory<WxStickOrderClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WxStickOrderClientService create(Throwable throwable) {
            return new WxStickOrderClientService() {
                @Override
                public WxStickJHPayRespDTO order(WxStickModelOrderDTO wxStickModelOrderDTO) {
                    if (log.isWarnEnabled()) {
                        log.warn(HYSTRIX_PATTERN, "order", wxStickModelOrderDTO, ThrowableUtils.asString(throwable));
                    }
                    throwable.printStackTrace();
                    throw new BusinessException("调用云端异常！");
                }

                @Override
                public ShortMsgPollingRespDTO polling(WxStickOrderRespDTO wxStickOrderRespDTO) {

                    if (log.isWarnEnabled()) {
                        log.warn(HYSTRIX_PATTERN, "polling", wxStickOrderRespDTO, ThrowableUtils.asString(throwable));
                    }
                    throwable.printStackTrace();
                    throw new BusinessException("调用云端异常！");
                }
            };
        }
    }
}
