package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.WxStickShopCartDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxStickShopCartDO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickShopCartMapstruct
 * @date 2019/03/12 14:41
 * @description 微信桌贴购物车mapstruct
 * @program holder-saas-store
 */
@Component
@Mapper(componentModel = "spring")
public interface WxStickShopCartMapstruct {
    WxStickShopCartDTO wxStickShopCartDO2wxStickShopCartDTO(WxStickShopCartDO wxStickShopCartDO);

    List<WxStickShopCartDTO> wxStickShopCartDOList2wxStickShopCartDTOList(List<WxStickShopCartDO> wxStickShopCartDOList);

    List<WxStickShopCartDO> shopCartDTOList2ShopCartDOList(List<WxStickShopCartDTO> wxStickShopCartDTOList);
}
