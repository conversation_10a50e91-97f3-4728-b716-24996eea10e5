package com.holderzone.saas.store.weixin.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxOrganizationService
 * @date 2019/05/09 17:31
 * @description 微信门店组织信息获取service
 * @program holder-saas-store
 */
public interface WxOrganizationService {
    Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> getStoreConfig(WxStorePageReqDTO wxStorePageReqDTO);

    StoreDTO getStoreDTOByGuid(String storeGuid);

    Page<StoreDTO> getStorePage(WxStorePageReqDTO wxStorePageReqDTO);
}