package com.holderzone.saas.store.weixin.event;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.weixin.StickModelMessageDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.weixin.constant.RocketMqConfig;
import com.holderzone.saas.store.weixin.service.WxStoreTableStickService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxTableStickerListener
 * @date 2019/03/05 10:45
 * @description 微信桌贴监听器
 * @program holder-saas-store
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.WECHAT_TABLE_STICKER_TOPIC,
        tags = RocketMqConfig.WECHAT_TABLE_STICKER_TAG,
        consumerGroup = RocketMqConfig.WECHAT_TABLE_STICKER_GROUP
)
public class WxTableStickerListener extends AbstractRocketMqConsumer<RocketMqTopic, StickModelMessageDTO> {

    private final WxStoreTableStickService wxStoreTableStickService;

    private final DynamicHelper dynamicHelper;

    @Autowired
    public WxTableStickerListener(WxStoreTableStickService wxStoreTableStickService, DynamicHelper dynamicHelper) {
        this.wxStoreTableStickService = wxStoreTableStickService;
        this.dynamicHelper = dynamicHelper;
    }

    @Override
    public boolean consumeMsg(StickModelMessageDTO message, MessageExt messageExt) {
        List<WxTableStickDTO> tableStickDTOList = message.getMessage();
        if (log.isInfoEnabled()) {
            log.info("收到TPS发送的tableStickDTO消息，tableStickDTO:{}", JacksonUtils.writeValueAsString(tableStickDTOList));
            if (ObjectUtils.isEmpty(tableStickDTOList)) {
                log.info("tableStickDTO消息为空, tableStickDTO：{}", JacksonUtils.writeValueAsString(tableStickDTOList));
                return false;
            }
        }
        // 切库
        String enterpriseGuid = message.getEnterpriseGuid();
        if (log.isInfoEnabled()) {
            log.info("根据enterpriseGuid({})切换数据源", enterpriseGuid);
        }
        dynamicHelper.changeDatasource(enterpriseGuid);
//        dynamicHelper.changeRedis(enterpriseGuid);

        // 构造userInfo，以便其他服务能获取到ErpGuid
        UserContextUtils.putErp(enterpriseGuid);
        try {
            wxStoreTableStickService.handleStickMessage(message.getMessage(), message.getMessageType());
        } finally {
            dynamicHelper.removeThreadLocalDatabaseInfo();
            UserContextUtils.remove();
        }
        return true;
    }

}
