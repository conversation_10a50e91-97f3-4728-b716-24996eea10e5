package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreEstimateClientService
 * @date 2019/6/3
 */
@Component
@FeignClient(name = "holder-saas-store-item", fallbackFactory = WxStoreEstimateClientService.WxStoreEstimateFullback.class)
public interface WxStoreEstimateClientService {

    String URL_PREFIX = "/estimate";

    @PostMapping(value = URL_PREFIX + "/query_estimate_for_synchronize")
    List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(@RequestBody BaseDTO baseDTO);

    @PostMapping(value = URL_PREFIX + "/verify_dinein_item_estimate")
    EstimateResultRespDTO verifyDineInItemEstimate(@RequestBody List<DineInItemDTO> request);

    @PostMapping(value = URL_PREFIX + "/dinein_fail")
    Boolean dineinFail(@RequestBody List<DineInItemDTO> request);

    @Component
    @Slf4j
    class WxStoreEstimateFullback implements FallbackFactory<WxStoreEstimateClientService> {
        @Override
        public WxStoreEstimateClientService create(Throwable throwable) {
            return new WxStoreEstimateClientService() {

                @Override
                public List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(BaseDTO baseDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public EstimateResultRespDTO verifyDineInItemEstimate(List<DineInItemDTO> request) {
                    log.error("估清失败,throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean dineinFail(List<DineInItemDTO> request) {
                    log.error("估清退还失败,throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }
            };
        }
    }
}
