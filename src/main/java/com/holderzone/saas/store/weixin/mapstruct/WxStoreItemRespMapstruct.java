package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.item.resp.AttrSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubgroupSynRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreAttrGroupRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreAttrRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreSkuRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 微信门店菜品转换
 * @className WxStoreItemRespMapstruct
 * @date 2019/3/15
 */
@Component
@Mapper(componentModel = "spring")
public interface WxStoreItemRespMapstruct {



    @Mappings({
            @Mapping(target = "userck", source = "isDefault",defaultValue = "0")
    })
    WxStoreAttrRespDTO getWxStoreAttr(AttrSynRespDTO attrSynRespDTO);

    WxStoreAttrGroupRespDTO getWxStoreAttrGroup(SubgroupSynRespDTO subgroupSynRespDTO);

	WxStoreSkuRespDTO getWxStoreSkuResp(SkuSynRespDTO skuSynRespDTO);

    List<WxStoreItemRespDTO> getWxStoreItem(List<ItemSynRespDTO> itemSynRespDTO);
}
