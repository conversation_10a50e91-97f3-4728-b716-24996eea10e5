package com.holderzone.saas.store.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreStatusUpdateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxCouldEditStoreDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreStatusRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxConfigOverviewDO;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxConfigOverviewService
 * @date 2019/05/09 11:06
 * @description 门店微信功能开启状态Service
 * @program holder-saas-store
 */
public interface WxConfigOverviewService extends IService<WxConfigOverviewDO> {
    /**
     * 查询当前用户可管理的所有门店微信共能开启状态
     *
     * @param wxStorePageReqDTO 门店名称，分页条件
     * @return
     */
    Page<WxStoreStatusRespDTO> pageWxStoreStatus(WxStorePageReqDTO wxStorePageReqDTO);

    /**
     * 修改门店微信功能开启状态
     *
     * @param wxStoreStatusUpdateReqDTO
     * @return
     */
    boolean updateWxStoreStatus(WxStoreStatusUpdateReqDTO wxStoreStatusUpdateReqDTO);

    /**
     * 判断微信功能配置当前是否可更改
     *
     * @param storeGuidList 门店guid集合
     * @param type          0：微信点餐，1：微信排队，2：微信预定（不做），3：微信外卖（不做）
     */
    void couldEdit(List<String> storeGuidList, Integer type);

    List<WxStoreStatusRespDTO> listByStoreGuidList(List<String> storeGuidList);

    /**
     * 查询当前可编辑的门店列表
     *
     * @param wxStoreReqDTO
     * @return
     */
    List<WxCouldEditStoreDTO> listCouldEditStore(WxStoreReqDTO wxStoreReqDTO);

    /**
     * 通过storeGuid 查询当前门店基本信息，通过type查询门店对应功能是否打开
     *
     * @param type      查询的对应功能0：点餐，1：排队，2：预定，3：外卖
     * @param storeGuid
     * @return WxStoreInfoDTO: 门店基本信息， Boolean 对应功能是否打开
     */
    Pair<WxStoreInfoDTO, Boolean> isStoreOpen(Integer type, String storeGuid);

    void initWxStore(String storeGuid);

	boolean whetherQueueConfig(String storeGuid);
}
