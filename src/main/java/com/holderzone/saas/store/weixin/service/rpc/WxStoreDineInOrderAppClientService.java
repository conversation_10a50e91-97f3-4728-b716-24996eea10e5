package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

@Component
@FeignClient(name = "holder-saas-aggregation-app", fallbackFactory = WxStoreDineInOrderAppClientService.WxStoreDineInOrderAppFullback.class)
public interface WxStoreDineInOrderAppClientService {

    String URL_PREFIX = "/dine_in_order";

    @PostMapping(value = URL_PREFIX + "/create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<CreateDineInOrderReqDTO> createOrderBill(CreateDineInOrderReqDTO creatOrderReqDTO);

    @PostMapping(value = URL_PREFIX + "/get_order_detail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<DineinOrderDetailRespDTO> getOrderDetail(SingleDataDTO singleDataDTO);

    @PostMapping(URL_PREFIX + "/order_list")
    Result<Page<DineInOrderListRespDTO>> orderList(DineInOrderListReqDTO dineInOrderListReqDTO);

    @PostMapping(URL_PREFIX + "/update_remark")
    Result updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping(URL_PREFIX + "/update_guest_count")
    Result updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping(URL_PREFIX + "/cancel")
    Result cancelOrder(CancelOrderReqDTO cancelOrderReqDTO);

    @PostMapping(URL_PREFIX + "/print_pre_bill")
    Result printPreBill(SingleDataDTO singleDataDTO);

    @PostMapping(URL_PREFIX + "/print_item_detail")
    Result printItemDetail(SingleDataDTO singleDataDTO);

    @PostMapping(URL_PREFIX + "/print_check_out")
    Result printCheckOut(SingleDataDTO singleDataDTO);

    @Component
    @Slf4j
    class WxStoreDineInOrderAppFullback implements FallbackFactory<WxStoreDineInOrderAppClientService> {
        @Override
        public WxStoreDineInOrderAppClientService create(Throwable throwable) {
            return new WxStoreDineInOrderAppClientService() {

                @Override
                public Result<CreateDineInOrderReqDTO> createOrderBill(CreateDineInOrderReqDTO creatOrderReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<DineinOrderDetailRespDTO> getOrderDetail(SingleDataDTO singleDataDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<Page<DineInOrderListRespDTO>> orderList(DineInOrderListReqDTO dineInOrderListReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result cancelOrder(CancelOrderReqDTO cancelOrderReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result printPreBill(SingleDataDTO singleDataDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result printItemDetail(SingleDataDTO singleDataDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result printCheckOut(SingleDataDTO singleDataDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }
            };
        }
    }
}
