package com.holderzone.saas.store.weixin.entity.enums;

import com.holderzone.saas.store.enums.weixin.WxUnBandRespEnum;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayWayEnum
 * @date 2019/03/16 9:41
 * @description 微信桌贴购买支付方式枚举
 * @program holder-saas-store
 */
public enum PayWayEnum {
    ALI_PAY(),
    WX_PAY();

    private String code;

    private String desc;

    public static String getDescByCode(Integer code) {
        return Arrays.stream(WxUnBandRespEnum.values()).filter(p -> p.getCode().equals(code))
                .findFirst().orElse(null).getMessage();
    }
}
