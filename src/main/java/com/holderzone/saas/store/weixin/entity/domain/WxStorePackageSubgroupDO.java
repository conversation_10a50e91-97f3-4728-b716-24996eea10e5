package com.holderzone.saas.store.weixin.entity.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @describle 套餐分组
 * @athor zhouxinwen
 * @dateTime 2019/3/1
 */
@Data
@ApiModel("套餐分组")
public class WxStorePackageSubgroupDO {
    @ApiModelProperty(value = "分组guid", required = true)
    private String subgroupGuid;

    @ApiModelProperty(value = "分组名称", required = true)
    private String subgroupName;

}
