package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreComponentConfigDO
 * @date 2019/02/26 11:49
 * @description 微信三方平台账户配置信息DO
 * @program holder-saas-store-weixin
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsw_store_component_config")
public class WxStoreComponentConfigDO {
    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    private String guid;

    private String componentAppId;

    private String componentAccessToken;

    private Long componentExpiresTime;

    private String componentVerifyTicket;

    private String componentRefreshToken;
}
