package com.holderzone.saas.store.weixin.controller;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestOpenMemberWechatInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAuthorizerInfoDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.exception.CheckBusinessException;
import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.weixin.config.WxH5AccountConfig;
import com.holderzone.saas.store.weixin.config.WxH5OpenIdRelationConfig;
import com.holderzone.saas.store.weixin.constant.GuestEnum;
import com.holderzone.saas.store.weixin.constant.RedisConstants;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import com.holderzone.saas.store.weixin.manager.WeixinSdkManager;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreConsumerMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.deal.ItemClientService;
import com.holderzone.saas.store.weixin.service.deal.TableClientService;
import com.holderzone.saas.store.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import com.holderzone.saas.store.weixin.utils.QrCodeUtil;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.error.WxMpErrorMsgEnum;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.open.api.WxOpenComponentService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Api("会员登录session管理")
@RestController
@RequestMapping("/session")
@Slf4j
public class WeixinSessionBuildController {
    @Resource
    WxStoreSessionDetailsService wxStoreSessionDetailsService;
    @Resource
    WxSaasMpService wxSaasMpService;
    @Resource
    private WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;
    @Resource
    private WeixinSdkManager weixinSdkManager;
    @Resource
    private WxQrCodeInfoService wxQrCodeInfoService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private WxStoreConsumerMapstruct wxStoreConsumerMapstruct;
    @Resource
    private WxOpenComponentService wxOpenComponentService;
    @Resource
    private WxUserRecordService wxUserRecordService;
    @Resource
    private WxStoreMpService wxStoreMpService;
    @Resource
    private MemberClientService memberClientService;
    @Resource
    private ItemClientService itemClientService;
    @Resource
    private ExecutorService executorService;
    @Autowired
    @Qualifier("openIdRelationExecutor")
    private ExecutorService openIdRelationExecutor;
    @Resource
    private WxOrderRecordService wxOrderRecordService;
    @Resource
    private WxStoreOrderConfigService wxStoreOrderConfigService;
    @Resource
    private UserMemberSessionUtils userMemberSessionUtils;
    @Resource
    TableClientService tableClientService;
    @Resource
    HsaBaseClientService hsaBaseClientService;
    @Resource
    EnterpriseClientService enterpriseClientService;
    @Autowired
    WxH5AccountConfig wxH5AccountConfig;
    @Autowired
    WxH5OpenIdRelationConfig wxH5OpenIdRelationConfig;

    @PostMapping("/getToken")
    public String getToken(@RequestBody WxMemberSessionDTO weixinMemberSessionDTO) {
        if (StringUtils.isNotBlank(weixinMemberSessionDTO.getWxUserInfoDTO().getOpenId()) && "null".equals(weixinMemberSessionDTO.getWxUserInfoDTO().getOpenId())) {
            log.info("openId为null！");
            return null;
        }
        //赚餐前端可能会拿不到运营主体  传undefined的情况
        if (Objects.isNull(weixinMemberSessionDTO.getOperSubjectGuid()) || "undefined".equals(weixinMemberSessionDTO.getOperSubjectGuid())) {
            log.info("运营主体为null！");
            return null;
        }
        String enterpriseGuid = weixinMemberSessionDTO.getEnterpriseGuid();
        asyncStoreItemInfo(weixinMemberSessionDTO.getStoreGuid(), enterpriseGuid);
        wxUserRecordService.asyncMemberInfo(weixinMemberSessionDTO, weixinMemberSessionDTO.getWxUserInfoDTO().getOpenId());
        // 获取门店及桌台信息
        WxMemberSessionDTO storeAndTableInfo = getStoreAndTableInfo(weixinMemberSessionDTO.getDiningTableGuid());
        weixinMemberSessionDTO.setStoreName(storeAndTableInfo.getStoreName());
        weixinMemberSessionDTO.setStoreGuid(storeAndTableInfo.getStoreGuid());
        weixinMemberSessionDTO.setDiningTableCode(storeAndTableInfo.getDiningTableCode());
        weixinMemberSessionDTO.setAreaName(storeAndTableInfo.getAreaName());
        weixinMemberSessionDTO.setAreaGuid(storeAndTableInfo.getAreaGuid());
        weixinMemberSessionDTO.getWxUserInfoDTO().setIsLogin(true);
        WxOrderConfigDTO detailConfig =
                wxStoreOrderConfigService.getDetailConfig(new WxStoreReqDTO().setStoreGuid(weixinMemberSessionDTO.getStoreGuid()));
        executorService.execute(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            UserContext userContext = new UserContext();
            userContext.setEnterpriseGuid(enterpriseGuid);
            UserContextUtils.put(userContext);
            if (detailConfig == null || detailConfig.getOrderModel() == 1) {
                log.error("门店配置无需创建正餐订单,门店配置:{}", detailConfig);
                return;
            }
            wxOrderRecordService.initialRecord(weixinMemberSessionDTO);
        });
        weixinMemberSessionDTO.setOrderState(detailConfig.getOrderModel());
        //wxMemberSessionDTO.set
        String token = redisUtils.generateGuid("wx-token");
        log.info("保存会员信息请求参数,token:{},weixinMemberSessionDTO:{}", token, JacksonUtils.writeValueAsString(weixinMemberSessionDTO));
        boolean success = this.saveOrUpdateUserInfo(weixinMemberSessionDTO);
        if (!success) {
            return null;
        }
        WxMemberSessionUtil.setMemberSeesion(redisUtils, token, weixinMemberSessionDTO);
        return token;
    }

    /**
     * 获取门店及桌台信息
     */
    private WxMemberSessionDTO getStoreAndTableInfo(String diningTableGuid) {
        String storeTableInfoKey = "WX:STORE_TABLE_INFO:%s";
        String redisKey = String.format(storeTableInfoKey, diningTableGuid);
        // cache
        Object storeAndTableInfoObj = redisUtils.get(redisKey);
        if (Objects.isNull(storeAndTableInfoObj)) {
            TableDTO tableByGuid = tableClientService.getTableByGuid(diningTableGuid);
            StoreDTO storeDetail = wxStoreSessionDetailsService.getStoreDetail(tableByGuid.getStoreGuid());
            WxMemberSessionDTO weixinMemberSessionDTO = new WxMemberSessionDTO();
            weixinMemberSessionDTO.setStoreName(storeDetail.getName());
            weixinMemberSessionDTO.setStoreGuid(tableByGuid.getStoreGuid());
            weixinMemberSessionDTO.setDiningTableCode(tableByGuid.getCode());
            weixinMemberSessionDTO.setAreaName(tableByGuid.getAreaName());
            weixinMemberSessionDTO.setAreaGuid(tableByGuid.getAreaGuid());
            // set cache
            redisUtils.setEx(redisKey, JacksonUtils.writeValueAsString(weixinMemberSessionDTO), 5, TimeUnit.MINUTES);
            return weixinMemberSessionDTO;
        }
        return JacksonUtils.toObject(WxMemberSessionDTO.class, storeAndTableInfoObj.toString());
    }

    public boolean saveOrUpdateUserInfo(WxMemberSessionDTO wxMemberSessionDTO) {
        WxUserInfoDTO wxUserInfoDTO = wxMemberSessionDTO.getWxUserInfoDTO();
        String openId = wxUserInfoDTO.getOpenId();
        WxStoreConsumerDTO wxUserRecordDO = new WxStoreConsumerDTO();
        wxUserRecordDO.setIsLogin(true);
        wxUserRecordDO.setHeadImgUrl(wxUserInfoDTO.getHeadImgUrl());
        wxUserRecordDO.setNickName(wxUserInfoDTO.getNickname());
        wxUserRecordDO.setOpenId(openId);
        wxUserRecordDO.setCity(wxUserInfoDTO.getCity());
        wxUserRecordDO.setProvince(wxUserInfoDTO.getProvince());
        wxUserRecordDO.setCountry(wxUserInfoDTO.getCountry());
        wxUserRecordDO.setSex(changeWeixSex(wxUserInfoDTO.getSex()));
        //新增运营主体
        wxUserRecordDO.setOperSubjectGuid(wxMemberSessionDTO.getOperSubjectGuid());
        wxUserRecordDO.setPhoneNum(wxMemberSessionDTO.getPhoneNum());
        wxUserRecordService.saveOrUpdateUserInfo(wxUserRecordDO);
        return true;
    }

    @PostMapping("/build")
    public WxMemberSessionDTO buildSession(@RequestBody WxAuthorizeReqDTO wxAuthorizeReqDTO) {
        log.info("buildSession入参{}", JacksonUtils.writeValueAsString(wxAuthorizeReqDTO));
        if (StringUtils.isNotBlank(wxAuthorizeReqDTO.getEventKey()) && !StringUtils.isNotBlank(wxAuthorizeReqDTO.getAppId())) {
            String last = wxAuthorizeReqDTO.getEventKey().substring(wxAuthorizeReqDTO.getEventKey().lastIndexOf(',') + 1);
            if (last.contains("wx")) {
                wxAuthorizeReqDTO.setAppId(last);
            }
        }
        WxMemberSessionDTO weixinMemberSessionDTO = new WxMemberSessionDTO();
        WxMpUser wxMpUser = null;
        String operSubjectGuid = "";
        try {
            Pair<String, WxMpUser> wxMpUserPair = getAndBuildWeixinUserSessionInfo(weixinMemberSessionDTO, wxAuthorizeReqDTO);
            if (StringUtils.isNotBlank(wxMpUserPair.getLeft())) {
                weixinMemberSessionDTO.setCode(2);
                weixinMemberSessionDTO.setMessage(wxMpUserPair.getLeft());
                return weixinMemberSessionDTO;
            }
            wxMpUser = wxMpUserPair.getRight();
            //根据storeGuid获取运营主体guid
            MultiMemberDTO memberInfoByOrganizationGuid = enterpriseClientService.findMemberInfoByOrganizationGuid(weixinMemberSessionDTO.getStoreGuid());
            operSubjectGuid = memberInfoByOrganizationGuid.getMultiMemberGuid();
        } catch (CheckBusinessException e) {
            log.error("", e);
            weixinMemberSessionDTO.setCode(e.getCode());
            weixinMemberSessionDTO.setMessage(e.getMessage());
        } catch (WxErrorException e) {
            int errorCode = e.getError().getErrorCode();
            String message = WxMpErrorMsgEnum.findMsgByCode(errorCode);
            log.error("调用微信API时出现异常：", e);
            weixinMemberSessionDTO.setCode(errorCode);
            weixinMemberSessionDTO.setMessage(message);
        }
        if (wxMpUser == null) {
            return weixinMemberSessionDTO;
        }
        String weixinToken = wxAuthorizeReqDTO.getWeixinToken();
        WxUserRecordDO wxUserRecordDO = buildwxUserRecordDO(wxMpUser);
        //依据会员缓存信息 更新登录状态
        WxMemberSessionDTO memberByOpenId = WxMemberSessionUtil.getMemberByOpenId(redisUtils, wxUserRecordDO.getOpenId());
        log.info("buildSession Redis中的会员信息{}", JacksonUtils.writeValueAsString(memberByOpenId));
        if (Objects.nonNull(memberByOpenId)) {
            weixinMemberSessionDTO.getWxUserInfoDTO().setIsLogin(memberByOpenId.getWxUserInfoDTO().getIsLogin());
        } else {
            weixinMemberSessionDTO.getWxUserInfoDTO().setIsLogin(wxUserRecordDO.getIsLogin());
        }
        //授权后的微信公众号 获取运营主体相关信息
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getByAppId(wxAuthorizeReqDTO.getAppId());
        log.info("查出的微信公众号配置信息{}", JacksonUtils.writeValueAsString(wxStoreAuthorizerInfoDO));
        if (Objects.nonNull(wxStoreAuthorizerInfoDO) && StringUtils.isNotBlank(wxStoreAuthorizerInfoDO.getOperSubjectGuid())) {
            operSubjectGuid = wxStoreAuthorizerInfoDO.getOperSubjectGuid();
        }
        //补充运营主体GUID
        UserContext userContext = UserContextUtils.get();
        if (StringUtils.isNotBlank(operSubjectGuid)) {
            userContext.setOperSubjectGuid(operSubjectGuid);
            UserContextUtils.put(userContext);
            wxUserRecordDO.setOperSubjectGuid(operSubjectGuid);
            weixinMemberSessionDTO.setOperSubjectGuid(operSubjectGuid);
        }
        // 查询并设置会员信息
        queryAndSetMemberInfo(wxAuthorizeReqDTO, weixinMemberSessionDTO);
        if (org.springframework.util.StringUtils.isEmpty(weixinMemberSessionDTO.getPhoneNum())) {
            weixinMemberSessionDTO.setPhoneNum("");
        }
        log.info("保存前组装完成会员信息{}", JacksonUtils.writeValueAsString(wxUserRecordDO));
        saveOrUpdate(wxUserRecordDO);
        initWeixinMemberSessionOrderModel(weixinMemberSessionDTO);
        WxMemberSessionUtil.setMemberSeesion(redisUtils, weixinToken, weixinMemberSessionDTO);
        // 存储外部openId、openAppId
        saveThirdRelationCache(wxAuthorizeReqDTO);
        //后面可以异步处理
        //查桌台主订单同步
        log.info("buildSession===>weixinMemberSessionDTO:{}", weixinMemberSessionDTO);
        return weixinMemberSessionDTO;
    }


    private void saveThirdRelationCache(WxAuthorizeReqDTO wxAuthorizeReqDTO) {
        // 存储外部openId、openAppId
        if (StringUtils.isEmpty(wxAuthorizeReqDTO.getThirdOpenId())) {
            return;
        }
        String thirdRelationCacheKey = String.format(RedisConstants.H5_WECHAT_THIRD_OPENID_APP_ID_KEY, wxAuthorizeReqDTO.getWeixinToken());
        redisUtils.setEx(thirdRelationCacheKey, wxAuthorizeReqDTO.getThirdAppId() + "," + wxAuthorizeReqDTO.getThirdOpenId(), 1, TimeUnit.DAYS);
    }

    /**
     * 查询并设置会员信息
     */
    private void queryAndSetMemberInfo(WxAuthorizeReqDTO wxAuthorizeReqDTO, WxMemberSessionDTO weixinMemberSessionDTO) {
        if (StringUtils.isNotEmpty(wxAuthorizeReqDTO.getMemberInfoGuid())) {
            // 如果手动传入了会员guid
            UserContext userContext = UserContextUtils.get();
            RequestOpenMemberWechatInfo queryMemberInfo = new RequestOpenMemberWechatInfo();
            queryMemberInfo.setOpenId(weixinMemberSessionDTO.getWxUserInfoDTO().getOpenId());
            queryMemberInfo.setOperSubjectGuid(userContext.getOperSubjectGuid());
            queryMemberInfo.setMemberInfoGuid(wxAuthorizeReqDTO.getMemberInfoGuid());
            log.info("免密登录会员入参:{}", JacksonUtils.writeValueAsString(queryMemberInfo));
            ResponseMemberInfo memberInfo = hsaBaseClientService.bindMemberWechatInfo(queryMemberInfo).getData();
            log.info("免密登录会员信息:{}", JacksonUtils.writeValueAsString(memberInfo));
            if (memberInfo != null) {
                setMemberSessionDTO(memberInfo, weixinMemberSessionDTO);
            }
            return;
        }
        if (StringUtils.isEmpty(weixinMemberSessionDTO.getPhoneNum())) {
            RequestQueryMemberInfo queryMemberInfo = new RequestQueryMemberInfo();
            queryMemberInfo.setOpenId(weixinMemberSessionDTO.getWxUserInfoDTO().getOpenId());
            ResponseMemberInfo memberInfo = hsaBaseClientService.getMemberInfo(queryMemberInfo).getData();
            log.info("首次进入：查询到会员详细{}", memberInfo);
            if (memberInfo != null) {
                setMemberSessionDTO(memberInfo, weixinMemberSessionDTO);
            }
        }
    }

    private void setMemberSessionDTO(ResponseMemberInfo memberInfo, WxMemberSessionDTO weixinMemberSessionDTO) {
        if (StringUtils.isNotEmpty(memberInfo.getPhoneNum())) {
            weixinMemberSessionDTO.setPhoneNum(memberInfo.getPhoneNum());
        }
        if (StringUtils.isNotEmpty(memberInfo.getMemberInfoGuid())) {
            weixinMemberSessionDTO.setMemberInfoGuid(memberInfo.getMemberInfoGuid());
        }
        if (StringUtils.isEmpty(weixinMemberSessionDTO.getWxUserInfoDTO().getNickname()) &&
                StringUtils.isNotEmpty(memberInfo.getNickName())) {
            weixinMemberSessionDTO.getWxUserInfoDTO().setNickname(memberInfo.getNickName());
        }
        if (StringUtils.isEmpty(weixinMemberSessionDTO.getWxUserInfoDTO().getHeadImgUrl()) &&
                StringUtils.isNotEmpty(memberInfo.getHeadImgUrl())) {
            weixinMemberSessionDTO.getWxUserInfoDTO().setHeadImgUrl(memberInfo.getHeadImgUrl());
        }
        // 会员登录状态
        weixinMemberSessionDTO.getWxUserInfoDTO().setIsLogin(true);
    }

    private void initWeixinMemberSessionOrderModel(WxMemberSessionDTO weixinMemberSessionDTO) {
        Integer orderState = weixinMemberSessionDTO.getOrderState();
        if (Objects.nonNull(orderState) || StringUtils.isEmpty(weixinMemberSessionDTO.getStoreGuid())) {
            return;
        }
        WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
        wxStoreReqDTO.setStoreGuid(weixinMemberSessionDTO.getStoreGuid());
        log.info("查询门店配置入参:{}", JacksonUtils.writeValueAsString(wxStoreReqDTO));
        WxOrderConfigDTO detailConfig = wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
        if (Objects.isNull(detailConfig)) {
            return;
        }
        log.info("获取门店配置:{}", detailConfig);
        weixinMemberSessionDTO.setOrderState(detailConfig.getOrderModel());
    }

    private Pair<String, WxMpUser> getAndBuildWeixinUserSessionInfo(WxMemberSessionDTO weixinMemberSessionDTO,
                                                                    WxAuthorizeReqDTO wxAuthorizeReqDTO) throws CheckBusinessException, WxErrorException {

        String eventKey = wxAuthorizeReqDTO.getEventKey();
        String code = wxAuthorizeReqDTO.getCode();
        String enterpriseGuid = wxAuthorizeReqDTO.getEnterpriseGuid();
        String wxqrCodeGuid = wxAuthorizeReqDTO.getWxqrCodeGuid();
        String appId = wxAuthorizeReqDTO.getAppId();
        String memberInfoGuid = wxAuthorizeReqDTO.getMemberInfoGuid();
        String thirdAppId = wxAuthorizeReqDTO.getThirdAppId();
        String thirdOpenId = wxAuthorizeReqDTO.getThirdOpenId();
        weixinMemberSessionDTO.setEnterpriseGuid(enterpriseGuid);
        WxMpService wxMpService = null;
        WxMpOAuth2AccessToken accessToken = null;
        WxMpUser wxMpUser = null;
        StopWatch stopWatch = new StopWatch();
        if (!eventKey.startsWith(BusinessName.ORDER_CONFIG)
                && !eventKey.startsWith(BusinessName.ORDER_CONFIG_2)) {
            return afterBuildWxMpUser(null, null, null, weixinMemberSessionDTO, code, stopWatch);
        }
        stopWatch.start("点餐二维码");
        WxQrCodeInfoDO wxQrCodeInfoDO = wxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(wxqrCodeGuid);
        log.info("二维码参数：{},", wxQrCodeInfoDO);
        stopWatch.stop();

        //门店商品，缓存
        stopWatch.start("门店商品，缓存");
        asyncStoreItemInfo(wxQrCodeInfoDO.getStoreGuid(), enterpriseGuid);
        stopWatch.stop();

        verifyWxQrCodeInfo(wxQrCodeInfoDO, wxqrCodeGuid);
        if (Objects.equals(0, wxQrCodeInfoDO.getQrCodeType())) {
            log.info("不带参二维码，优先使用商户自己的公众号");
            // 查询门店点餐配置
            WxOrderConfigDTO detailConfig = wxStoreOrderConfigService.getDetailConfig(new WxStoreReqDTO().setStoreGuid(wxQrCodeInfoDO.getStoreGuid()));
            if (Objects.equals(BooleanEnum.TRUE.getCode(), detailConfig.getGuestFlag())) {
                // 游客模式
                buildDealMemberSession(wxQrCodeInfoDO, weixinMemberSessionDTO);
                WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO();
                wxUserInfoDTO.setOpenId(GuestEnum.getOpenId());
                wxUserInfoDTO.setNickname(GuestEnum.GUEST.getDes());
                weixinMemberSessionDTO.setWxUserInfoDTO(wxUserInfoDTO);
                wxMpUser = new WxMpUser();
                wxMpUser.setNickname(wxUserInfoDTO.getNickname());
                wxMpUser.setOpenId(wxUserInfoDTO.getOpenId());
                accessToken = new WxMpOAuth2AccessToken();
            } else {
                //普通二维码 (需授权)
                WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getByBrandId(wxQrCodeInfoDO.getBrandGuid());
                log.info("查询微信公众号授权信息{}", JacksonUtils.writeValueAsString(wxStoreAuthorizerInfoDO));
                if (wxStoreAuthorizerInfoDO != null) {
                    //log.info("不带参二维码，优先使用商户自己的公众号,商户品牌已经授权公众号");
                    appId = wxStoreAuthorizerInfoDO.getAuthorizerAppid();
                    // 获取绑定公众号的openId
                    getOpenIdByMySelf(wxStoreAuthorizerInfoDO, wxQrCodeInfoDO, wxAuthorizeReqDTO);
                    // 构建eventKey
                    eventKey = buildEventKey(wxAuthorizeReqDTO.getEventKey(), wxQrCodeInfoDO.getBrandGuid());
                }
                // 重新获取微信授权地址
                String authorizeUrl = reGetAuthorizeUrl(wxQrCodeInfoDO, wxStoreAuthorizerInfoDO, eventKey, appId,
                        memberInfoGuid, thirdAppId, thirdOpenId);
                if (StringUtils.isNotBlank(authorizeUrl)) {
                    return Pair.of(authorizeUrl, null);
                }
                wxMpService = weixinSdkManager.buildWxMpService(3);
                accessToken = wxMpService.getOAuth2Service().getAccessToken(code);
                log.info("h5 accessToken:{}", JacksonUtils.writeValueAsString(accessToken));
                wxMpUser = new WxMpUser();
                wxMpUser.setOpenId(accessToken.getOpenId());
                wxMpUser.setNickname("微信用户");
                asyncSaveOpenIdRelation(eventKey, wxQrCodeInfoDO.getBrandGuid(), accessToken.getOpenId());
            }
        }
        if (!StringUtils.isEmpty(appId) && Objects.isNull(accessToken)) {
            WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = verifyStoreAuthorizerInfo(null, wxQrCodeInfoDO, appId);
            stopWatch.start("三方平台微信wxMpService和accessToken获取");
            wxMpService = wxSaasMpService.getWxMpService(wxStoreAuthorizerInfoDO);
            log.info("appId:{}======>code:{}======>三方平台的AccessToken:{}", appId, code, wxMpService.getWxMpConfigStorage().getAccessToken());
            accessToken = wxOpenComponentService.oauth2getAccessToken(appId, code);
            stopWatch.stop();
        }
        stopWatch.start("buildDealMemberSession");
        buildDealMemberSession(wxQrCodeInfoDO, weixinMemberSessionDTO);
        executorService.execute(() -> initialRecord(enterpriseGuid, wxQrCodeInfoDO, weixinMemberSessionDTO));
        stopWatch.stop();
        return afterBuildWxMpUser(wxMpService, accessToken, wxMpUser, weixinMemberSessionDTO, code, stopWatch);
    }

    private Pair<String, WxMpUser> afterBuildWxMpUser(WxMpService wxMpService, WxMpOAuth2AccessToken accessToken,
                                                      WxMpUser wxMpUser, WxMemberSessionDTO weixinMemberSessionDTO,
                                                      String code, StopWatch stopWatch) throws WxErrorException {
        if (accessToken == null) {
            stopWatch.start("默认微信wxMpService和accessToken获取");
            wxMpService = weixinSdkManager.buildWxMpService(1);
            accessToken = wxMpService.getOAuth2Service().getAccessToken(code);
            stopWatch.stop();
        }
        if (Objects.isNull(wxMpUser)) {
            wxMpUser = wxMpService.getOAuth2Service().getUserInfo(accessToken, null);
        }
        WxUserInfoDTO wxUserInfo = wxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(wxMpUser);
        wxUserInfo.setSex(changeWeixSex(wxMpUser.getSex()));
        weixinMemberSessionDTO.setWxUserInfoDTO(wxUserInfo);
        //获取会员相关
        wxUserRecordService.asyncMemberInfo(weixinMemberSessionDTO, wxUserInfo.getOpenId());
        log.info("{}", stopWatch.prettyPrint());
        return Pair.of(null, wxMpUser);
    }

    private String buildEventKey(String eventKey, String brandGuid) {
        if (!wxH5OpenIdRelationConfig.getBrandGuids().contains(brandGuid)) {
            return eventKey;
        }
        // 第二次及第三次
        if (eventKey.contains(Constant.WX_AUTHORIZER_EVENT_KEY_MYSELF)) {
            return eventKey;
        }
        // 第一次
        if (!eventKey.contains(Constant.WX_AUTHORIZER_EVENT_KEY_AGAIN)) {
            return eventKey + "," + Constant.WX_AUTHORIZER_EVENT_KEY_MYSELF;
        }
        return eventKey;
    }

    private void getOpenIdByMySelf(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO, WxQrCodeInfoDO wxQrCodeInfoDO, WxAuthorizeReqDTO wxAuthorizeReqDTO)
            throws CheckBusinessException, WxErrorException {
        String eventKey = wxAuthorizeReqDTO.getEventKey();
        if (!eventKey.contains(Constant.WX_AUTHORIZER_EVENT_KEY_MYSELF) || eventKey.contains(Constant.WX_AUTHORIZER_EVENT_KEY_AGAIN)) {
            return;
        }
        if (!wxH5OpenIdRelationConfig.getBrandGuids().contains(wxQrCodeInfoDO.getBrandGuid())) {
            return;
        }
        String code = wxAuthorizeReqDTO.getCode();
        String appId = wxStoreAuthorizerInfoDO.getAuthorizerAppid();
        wxStoreAuthorizerInfoDO = verifyStoreAuthorizerInfo(wxStoreAuthorizerInfoDO, wxQrCodeInfoDO, appId);
        WxMpService wxMpService = wxSaasMpService.getWxMpService(wxStoreAuthorizerInfoDO);
        log.info("appId:{}======>code:{}======>三方平台的AccessToken:{}", appId, code, wxMpService.getWxMpConfigStorage().getAccessToken());
        WxMpOAuth2AccessToken accessToken = wxOpenComponentService.oauth2getAccessToken(appId, code);
        String openId = accessToken.getOpenId();
        // 存储openId
        log.info("获取当前门店绑定的公众号对应的openId:{}", openId);
        wxAuthorizeReqDTO.setEventKey(eventKey + "," + Constant.WX_AUTHORIZER_EVENT_KEY_OPENID + openId);
    }

    /**
     * 保存授权不同公众号的openId关系
     */
    private void asyncSaveOpenIdRelation(String eventKey, String brandGuid, String openId) {
        String myselfOpenId = getOpenIdByEventKey(eventKey);
        if (StringUtils.isEmpty(myselfOpenId)) {
            return;
        }
        UserContext userContext = UserContextUtils.get();
        openIdRelationExecutor.execute(() -> {
            // 切换数据源
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            String redisKey = String.format(RedisConstants.H5_WECHAT_OPENID_RELATION_KEY, brandGuid, openId);
            String relationOpenId = myselfOpenId.replace(Constant.WX_AUTHORIZER_EVENT_KEY_OPENID, Strings.EMPTY);
            redisUtils.setEx(redisKey, relationOpenId, 7, TimeUnit.DAYS);
        });
    }

    public WxStoreAuthorizerInfoDO verifyStoreAuthorizerInfo(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO,
                                                             WxQrCodeInfoDO wxQrCodeInfoDO, String appId) throws CheckBusinessException {
        if (wxStoreAuthorizerInfoDO == null) {
            wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getByAppId(appId);
        }
        if (wxStoreAuthorizerInfoDO == null) {
            log.error("app：wxQrCodeInfoDO:{},", wxQrCodeInfoDO);
            throw new CheckBusinessException(50000, "当前品牌二维码配置错误");
        }
        if (!QrCodeUtil.checkQrCode(wxQrCodeInfoDO.getBrandGuid(), wxStoreAuthorizerInfoDO.getBrandGuid())) {
            log.error("app：wxStoreAuthorizerInfoDO:{},", wxStoreAuthorizerInfoDO);
            throw new CheckBusinessException(50000, "当前二维码品牌配置不一致");
        }
        return wxStoreAuthorizerInfoDO;
    }

    private void verifyWxQrCodeInfo(WxQrCodeInfoDO wxQrCodeInfoDO, String wxqrCodeGuid) throws CheckBusinessException {
        if (ObjectUtils.isEmpty(wxQrCodeInfoDO)) {
            log.error("未查询到WxQrCodeInfoDO数据：wxqrCodeGuid：{},", wxqrCodeGuid);
            throw new CheckBusinessException(50000, "当前二维码无对应参数，请重新下载二维码");
        }
    }

    /**
     * 重新获取微信授权地址
     */
    private String reGetAuthorizeUrl(WxQrCodeInfoDO wxQrCodeInfoDO, WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO,
                                     String eventKey, String appId, String memberInfoGuid, String thirdAppId, String thirdOpenId) {
        // 默认appid
        String currentAppId = wxH5AccountConfig.getAppId();
        log.info("回调参数:{},memberInfoGuid:{}", JacksonUtils.writeValueAsString(eventKey), memberInfoGuid);
        if (eventKey.contains(Constant.WX_AUTHORIZER_EVENT_KEY_AGAIN)) {
            return null;
        }
        Boolean isAgain = null;
        Boolean myself = null;
        String myselfOpenId = Strings.EMPTY;
        if (eventKey.contains(Constant.WX_AUTHORIZER_EVENT_KEY_MYSELF)) {
            currentAppId = appId;
            myself = true;
        }
        if (eventKey.contains(Constant.WX_AUTHORIZER_EVENT_KEY_OPENID)) {
            isAgain = true;
            currentAppId = wxH5AccountConfig.getAppId();
            myselfOpenId = getOpenIdByEventKey(eventKey);
        }
        if (!wxH5OpenIdRelationConfig.getBrandGuids().contains(wxQrCodeInfoDO.getBrandGuid())) {
            isAgain = true;
        } else {
            // 处理设置授权多次但没有绑定公众号的情况
            if (Objects.isNull(wxStoreAuthorizerInfoDO)) {
                isAgain = true;
            }
        }
        // 获取重定向参数
        WxQrCodeUrlQuery wxQrCodeUrlQuery = new WxQrCodeUrlQuery(UserContextUtils.getEnterpriseGuid(), wxQrCodeInfoDO.getStoreGuid(),
                wxQrCodeInfoDO.getStoreName(), wxQrCodeInfoDO.getAreaGuid(), wxQrCodeInfoDO.getAreaName(), wxQrCodeInfoDO.getTableGuid(),
                wxQrCodeInfoDO.getTableName(), wxQrCodeInfoDO.getQrCodeType(), wxQrCodeInfoDO.getBrandGuid(), currentAppId, isAgain, myself, myselfOpenId);
        // 生成重定向地址
        try {
            String authorizeUrl = wxStoreMpService.getAuthorizeUrl(wxQrCodeUrlQuery, memberInfoGuid, thirdAppId, thirdOpenId);
            log.info("重新生成微信授权地址：{}", authorizeUrl);
            return authorizeUrl;
        } catch (Exception e) {
            log.error("重新微信授权地址生成失败,e:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 从eventKey中获取openId
     */
    private String getOpenIdByEventKey(String eventKey) {
        String[] split = eventKey.split(",");
        for (String key : split) {
            if (key.contains(Constant.WX_AUTHORIZER_EVENT_KEY_OPENID)) {
                return key;
            }
        }
        return Strings.EMPTY;
    }

    /**
     * 初始化订单
     */
    private void initialRecord(String enterpriseGuid, WxQrCodeInfoDO wxQrCodeInfoDO, WxMemberSessionDTO weixinMemberSessionDTO) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid(enterpriseGuid);
        UserContextUtils.put(userContext);
        WxOrderConfigDTO detailConfig = wxStoreOrderConfigService.getDetailConfig(new WxStoreReqDTO().setStoreGuid(wxQrCodeInfoDO.getStoreGuid()));
        if (detailConfig == null || detailConfig.getOrderModel() == 1) {
            log.error("门店配置无需创建正餐订单:{},门店配置:{}", wxQrCodeInfoDO, detailConfig);
            return;
        }
        wxOrderRecordService.initialRecord(weixinMemberSessionDTO);
    }

    @PostMapping("/get_wx_auth_info")
    public WxStoreAuthorizerInfoDTO getWxAuthInfo(@RequestParam("brandGuid") String brandGuid) {
        WxStoreAuthorizerInfoDTO dto = new WxStoreAuthorizerInfoDTO();
        WxStoreAuthorizerInfoDO byBrandId = wxStoreAuthorizerInfoService.getByBrandId(brandGuid);
        if (Objects.nonNull(byBrandId)) {
            BeanUtils.copyProperties(byBrandId, dto);
        }
        log.info("getWxAuthInfo获取微信授权信息返回参数{}", JacksonUtils.writeValueAsString(dto));
        return dto;
    }

    @PostMapping("/find_wx_auth_info")
    public WxStoreAuthorizerInfoDTO findWxAuthInfo(@RequestParam("appId") String appId) {
        WxStoreAuthorizerInfoDTO dto = new WxStoreAuthorizerInfoDTO();
        WxStoreAuthorizerInfoDO byBrandId = wxStoreAuthorizerInfoService.getByAppId(appId);
        if (Objects.nonNull(byBrandId)) {
            BeanUtils.copyProperties(byBrandId, dto);
        }
        log.info("findWxAuthInfo获取微信授权信息返回参数{}", JacksonUtils.writeValueAsString(dto));
        return dto;
    }

    /**
     * 异步加载当前门店商品，缓存 60秒最多进入一次
     */
    private void asyncStoreItemInfo(String storeGuid, String enterpriseGuid) {
        UserContext userContext = UserContextUtils.get();
        CompletableFuture.runAsync(() -> {
            dynamicSwitch(enterpriseGuid, userContext);
            String lockKey = "wx:lock:" + storeGuid;
            boolean lock = redisUtils.setNx(lockKey, "1", 60);
            if (!lock) {
                return;
            }
            try {
                BaseDTO baseDTO = new BaseDTO();
                baseDTO.setStoreGuid(storeGuid);
                ItemAndTypeForAndroidRespDTO item = itemClientService.getItemsForWeixin(baseDTO);
                if (!ObjectUtils.isEmpty(item)) {
                    redisUtils.set(CacheName.ITEM_DETAILS + storeGuid, item);
                }
            } catch (Exception e) {
                log.error("asyncStoreItemInfo异常", e);
                redisUtils.delete(lockKey);
            } finally {

            }

        }, executorService);
    }


//    /**
//     * @return 会员价与匹配规则
//     */
//    private ProductDiscountRespDTO enableMemberPrice(Boolean isLogin) {
//        ProductDiscountRespDTO productDiscountRespDTO = null;
//        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
//        String memberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
//        if (!StringUtils.isEmpty(memberInfoCardGuid) && memberInfoCardGuid.length() > 5 && isLogin) {
//            productDiscountRespDTO = memberClientService.getDiscountProducts(memberInfoCardGuid);
//            log.info("门店首页：会员匹配规则：{}", productDiscountRespDTO);
//        }
//
//        if (productDiscountRespDTO == null) {
//            productDiscountRespDTO = new ProductDiscountRespDTO();
//            productDiscountRespDTO.setMemberPrice(false);
//        }
//        log.info("门店首页：会员匹配规则：{}", productDiscountRespDTO);
//        userMemberSession.setWhetherSupportMemberPrice(productDiscountRespDTO.isMemberPrice());
//        userMemberSessionUtils.addUserMemberSession(userMemberSession);
//        return productDiscountRespDTO;
//    }

    private void dynamicSwitch(String enterpriseGuid, UserContext userContext) {
        DynamicHelper.dynamicSwitch(enterpriseGuid, userContext);
    }

    private void saveOrUpdate(WxUserRecordDO wxUserRecordDO) {
        WxUserRecordDO recordDB = wxUserRecordService.getOneByOpenId(wxUserRecordDO.getOpenId());
        if (recordDB == null) {
            wxUserRecordDO.setGuid(redisUtils.generateGuid("WxUserRecord"));
            wxUserRecordDO.setIsLogin(true);
            wxUserRecordDO.setGmtCreate(LocalDateTime.now());
            log.info("用户不存在，insert:{}", wxUserRecordDO);
            wxUserRecordService.save(wxUserRecordDO);
        } else {
            log.info("用户存在，update:{},login:{}", wxUserRecordDO.getOpenId(), recordDB.getIsLogin());
            wxUserRecordDO.setGuid(recordDB.getGuid());
            wxUserRecordDO.setHeadImgUrl(wxUserRecordDO.getHeadImgUrl());
            wxUserRecordDO.setIsLogin(recordDB.getIsLogin());
            wxUserRecordDO.setGmtModified(LocalDateTime.now());
            wxUserRecordService.updateById(wxUserRecordDO);
            wxUserRecordDO.setIsLogin(recordDB.getIsLogin());
        }
    }

    /**
     * 点餐的session信息
     */
    private void buildDealMemberSession(WxQrCodeInfoDO wxQrCodeInfoDO, WxMemberSessionDTO weixinMemberSessionDTO) {
        BrandDTO brandDTO = wxStoreSessionDetailsService.getBrandInfoDetails(wxQrCodeInfoDO.getStoreGuid());
        if (!ObjectUtils.isEmpty(brandDTO)) {
            weixinMemberSessionDTO.setBrandGuid(brandDTO.getGuid());
            weixinMemberSessionDTO.setBrandName(brandDTO.getName());
            weixinMemberSessionDTO.setLogUrl(brandDTO.getLogoUrl());
        }
        weixinMemberSessionDTO.setDiningTableGuid(wxQrCodeInfoDO.getTableGuid());
        weixinMemberSessionDTO.setStoreGuid(wxQrCodeInfoDO.getStoreGuid());
        weixinMemberSessionDTO.setStoreName(wxStoreSessionDetailsService.getStoreDetail(wxQrCodeInfoDO.getStoreGuid()).getName());
        weixinMemberSessionDTO.setAreaGuid(wxQrCodeInfoDO.getAreaGuid());
        weixinMemberSessionDTO.setAreaName(wxQrCodeInfoDO.getAreaName());
        weixinMemberSessionDTO.setBrandGuid(wxQrCodeInfoDO.getBrandGuid());
        weixinMemberSessionDTO.setBrandName(brandDTO.getName());
        weixinMemberSessionDTO.setDiningTableCode(wxQrCodeInfoDO.getTableName());
    }


    private WxUserRecordDO buildwxUserRecordDO(WxMpUser wxMpUser) {
        WxUserRecordDO wxMpUser2WxuserRecord = wxStoreConsumerMapstruct.wxMpUser2WxuserRecord(wxMpUser);
        int userRecordSex = changeWeixSex(wxMpUser.getSex());
        wxMpUser2WxuserRecord.setSex(userRecordSex);
        wxMpUser2WxuserRecord.setIsLogin(true);
        return wxMpUser2WxuserRecord;
    }

    private int changeWeixSex(Integer wxsex) {
        if (wxsex == null) {
            return 2;
        }
        int userRecordSex = 1;
        // 微信0未知，1男，2女；排队服务0女，1男，2未知
        if (wxsex == 0) {// 微信未知
            userRecordSex = 2;
        } else if (wxsex == 2) {// 微信女
            userRecordSex = 0;
        }
        return userRecordSex;
    }
}
