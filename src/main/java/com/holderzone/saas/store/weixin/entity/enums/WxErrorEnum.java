package com.holderzone.saas.store.weixin.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxErrorEnum
 * @date 2019/03/22 19:01
 * @description 微信返回码枚举
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum WxErrorEnum {
    SUCCESS(0, "请求成功"),
    INVALID_ACCESS_TOKEN(40001, "access_token无效"),
    WRONG_TYPE_OF_DOCUMENT(40002, "不合法的凭证类型"),
    WRONG_OPEN_ID(40003, "不合法的OpenID"),
    WRONG_MEDIA_TYPE(40004, "不合法的媒体文件类型"),
    WRONG_FILE_TYPE(40005, "不合法的文件类型"),
    WRONG_FILE_SIZE(40006, "不合法的文件大小"),
    WRONG_MEDIA_ID(40007, "不合法的媒体文件id"),
    API_UN_AUTHORIZED(48001, "当前公众号，无该功能或该功能未授权"),
    INVALID_ACTION_INFO(40053, "请求参数有误，请确认请求参数"),
    COMPONENT_TICKET_EXPIRED(61005, "微信异常，验证token无效或已过期"),
    DEFAULT(-1, "调用微信API发生异常");

    private int errorCode;

    private String errorMsg;

    public static String getErrorMsgByCode(Integer errorCode) {
        return Arrays.stream(WxErrorEnum.values()).filter(p -> errorCode.equals(p.getErrorCode()))
                .findFirst().orElse(DEFAULT).getErrorMsg();
    }
}
