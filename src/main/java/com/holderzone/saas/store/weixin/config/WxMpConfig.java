package com.holderzone.saas.store.weixin.config;


import me.chanjar.weixin.mp.api.WxMpMaterialService;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpMaterialServiceImpl;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.WxMpConfigStorage;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMpConfig
 * @date 2019/04/01 14:23
 * @description 微信公众号配置
 * @program holder-saas-store
 */
@EnableConfigurationProperties({WxAccountConfig.class})
@Configuration
public class WxMpConfig {

    @Autowired
    WxAccountConfig wxAccountConfig;

    @Autowired
    WxH5AccountConfig wxH5AccountConfig;

    @Autowired
    WorkBenchConfig workBenchConfig;

    @Autowired
    private WxBiBossAccountConfig wxBiBossAccountConfig;

    private WxMpService wxMpService;

    @Bean
    public WxMpService wxMpService() {
        WxMpService wxMpServiceImpl = new WxMpServiceImpl();
        wxMpServiceImpl.setWxMpConfigStorage(wxMpConfigStorage());
        this.wxMpService = wxMpServiceImpl;
        return wxMpService;
    }

    @Bean("workBenchService")
    public WxMpService workBenchService() {
        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(wxMpConfigStorage2());
        this.wxMpService = wxMpService;
        return wxMpService;
    }

    @Bean("wxH5MpService")
    public WxMpService wxH5MpService() {
        WxMpService wxMpServiceImpl = new WxMpServiceImpl();
        wxMpServiceImpl.setWxMpConfigStorage(wxH5MpConfigStorage());
        this.wxMpService = wxMpServiceImpl;
        return wxMpService;
    }

    @Bean("wxBiBossMpService")
    public WxMpService wxBiBossMpService() {
        WxMpService wxMpServiceImpl = new WxMpServiceImpl();
        wxMpServiceImpl.setWxMpConfigStorage(wxBiBossMpConfigStorage());
        this.wxMpService = wxMpServiceImpl;
        return wxMpService;
    }


    @Bean
    public WxMpMaterialService wxMpMaterialService() {
        WxMpMaterialService wxMpMaterialService = new WxMpMaterialServiceImpl(wxMpService);
        return wxMpMaterialService;
    }

    @Bean
    public WxMpConfigStorage wxMpConfigStorage() {
        WxMpDefaultConfigImpl wxMpConfigStorage = new WxMpDefaultConfigImpl();
        wxMpConfigStorage.setAppId(wxAccountConfig.getAppId());
        wxMpConfigStorage.setSecret(wxAccountConfig.getAppSecret());
        wxMpConfigStorage.setToken(wxAccountConfig.getToken());
        return wxMpConfigStorage;
    }

    @Bean
    public WxMpConfigStorage wxMpConfigStorage2() {
        WxMpDefaultConfigImpl wxMpConfigStorage = new WxMpDefaultConfigImpl();
        wxMpConfigStorage.setAppId(workBenchConfig.getAppId());
        wxMpConfigStorage.setSecret(workBenchConfig.getAppSecret());
        return wxMpConfigStorage;
    }

    @Bean
    public WxMpConfigStorage wxH5MpConfigStorage() {
        WxMpDefaultConfigImpl wxMpConfigStorage = new WxMpDefaultConfigImpl();
        wxMpConfigStorage.setAppId(wxH5AccountConfig.getAppId());
        wxMpConfigStorage.setSecret(wxH5AccountConfig.getAppSecret());
        wxMpConfigStorage.setToken(wxH5AccountConfig.getToken());
        return wxMpConfigStorage;
    }

    @Bean
    public WxMpConfigStorage wxBiBossMpConfigStorage() {
        WxMpDefaultConfigImpl wxMpConfigStorage = new WxMpDefaultConfigImpl();
        wxMpConfigStorage.setAppId(wxBiBossAccountConfig.getAppId());
        wxMpConfigStorage.setSecret(wxBiBossAccountConfig.getAppSecret());
        wxMpConfigStorage.setToken(wxBiBossAccountConfig.getToken());
        return wxMpConfigStorage;
    }
}
