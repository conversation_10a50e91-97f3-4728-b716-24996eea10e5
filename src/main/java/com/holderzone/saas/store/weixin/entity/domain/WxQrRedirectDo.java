package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/4/7 21:14
 */
@Data
@AllArgsConstructor
@RequiredArgsConstructor
@TableName("hsw_weixin_qr_redirect")
public class WxQrRedirectDo implements Serializable {

    @TableId("id")
    private Long id;
    /**
     * 重定向地址
     */
    private String url;
    /**
     * 桌台Guid
     */
    private String tableGuid;
    /**
     * 码类型
     */
    private int type;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 员工guid
     */
    private String staffGuid;
    /**
     * 员工名称
     */
    private String staffName;
}
