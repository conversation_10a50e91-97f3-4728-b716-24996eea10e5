package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreOrderPayService
 * @date 2019/6/24
 */
public interface WxStoreOrderPayService {
	WxStoreTradeOrderDetailsRespDTO orderPay(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);
}
