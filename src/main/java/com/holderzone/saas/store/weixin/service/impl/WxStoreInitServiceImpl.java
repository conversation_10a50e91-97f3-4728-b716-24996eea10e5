package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.enums.item.TagEnum;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderConfigDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQueueConfigDO;
import com.holderzone.saas.store.weixin.service.WxQueueConfigService;
import com.holderzone.saas.store.weixin.service.WxReserveConfigService;
import com.holderzone.saas.store.weixin.service.WxStoreInitService;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreInitServiceImpl
 * @date 2019/05/10 10:06
 * @description 门店微信功能初始化Service实现类
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxStoreInitServiceImpl implements WxStoreInitService {

    private final WxStoreOrderConfigService wxStoreOrderConfigService;

    private final WxQueueConfigService wxQueueConfigService;

    private final WxReserveConfigService wxReserveConfigService;

    private final RedisUtils redisUtils;

    @Autowired
    public WxStoreInitServiceImpl(WxStoreOrderConfigService wxStoreOrderConfigService,
                                  WxQueueConfigService wxQueueConfigService,
                                  WxReserveConfigService wxReserveConfigService,
                                  RedisUtils redisUtils) {
        this.wxStoreOrderConfigService = wxStoreOrderConfigService;
        this.wxQueueConfigService = wxQueueConfigService;
        this.wxReserveConfigService = wxReserveConfigService;
        this.redisUtils = redisUtils;
    }

    @Override
    public void initWxStore(List<String> storeGuidList) {
        // 检查门店GUID列表是否为空
        if (ObjectUtils.isEmpty(storeGuidList)) {
            return;
        }
        // 初始化微信订单配置
        initWxOrderConfig(storeGuidList);
        // 初始化微信排队配置
        initWxQueueConfig(storeGuidList);
        // 初始化微信预约配置
        wxReserveConfigService.initWxReserveConfig(storeGuidList);
    }

    @Override
    public void initWxStoreByMQ(String storeGuid) {
        // 检查单个门店GUID是否为空
        if (StringUtils.isEmpty(storeGuid)) {
            return;
        }
        // 通过消息队列初始化微信订单配置
        initWxOrderConfigByMQ(storeGuid);
        // 通过消息队列初始化微信排队配置
        initWxQueueConfigByMQ(storeGuid);
        // 初始化微信预约配置
        wxReserveConfigService.initWxReserveConfig(Collections.singletonList(storeGuid));
    }

    private void initWxOrderConfig(List<String> storeGuidList) {
        List<WxOrderConfigDO> needInitDOList = new ArrayList<>();
        // 获取已存在的订单配置
        Map<String, WxOrderConfigDO> existDOMap = wxStoreOrderConfigService.list(new LambdaQueryWrapper<WxOrderConfigDO>()
                .in(WxOrderConfigDO::getStoreGuid, storeGuidList)).stream()
                .collect(Collectors.toMap(WxOrderConfigDO::getStoreGuid, Function.identity()));
        storeGuidList.forEach(storeGuid -> {
            WxOrderConfigDO wxOrderConfigDO = existDOMap.get(storeGuid);
            // 如果配置不存在，则创建新的配置
            if (ObjectUtils.isEmpty(wxOrderConfigDO)) {
                String guid = redisUtils.generateGuid(redisUtils.keyGenerate(BusinessName.WX, BusinessName.ORDER_CONFIG));
                wxOrderConfigDO = new WxOrderConfigDO();
                needInitDOList.add(wxOrderConfigDO
                        .setGuid(guid)
                        .setStoreGuid(storeGuid)
                        .setOrderModel(0)
                        .setMenuType(0)
                        .setTakingModel(0)
                        .setIsOnlinePayed(1)
                        .setIsWeighingOrdered(1)
                        .setIsRemarked(1)
                        .setIsOrderOpen(1)
                        .setUrlType(0)
                        .setTagNames(StringUtils.trimWhitespace(TagEnum.getTagIds())));
            }
        });
        // 批量保存新的订单配置
        wxStoreOrderConfigService.saveBatch(needInitDOList);
    }

    private void initWxQueueConfig(List<String> storeGuidList) {
        List<WxQueueConfigDO> needInitDOList = new ArrayList<>();
        // 获取已存在的排队配置
        List<WxQueueConfigDO> wxQueueConfigDOList = wxQueueConfigService.list(new LambdaQueryWrapper<WxQueueConfigDO>()
                .in(WxQueueConfigDO::getStoreGuid, storeGuidList));
        Map<String, WxQueueConfigDO> wxQueueMap = wxQueueConfigDOList.stream()
                .collect(Collectors.toMap(WxQueueConfigDO::getStoreGuid, Function.identity()));
        storeGuidList.forEach(storeGuid -> {
            WxQueueConfigDO wxQueueConfigDO = wxQueueMap.get(storeGuid);
            // 如果配置不存在，则创建新的配置
            if (ObjectUtils.isEmpty(wxQueueConfigDO)) {
                wxQueueConfigDO = new WxQueueConfigDO();
                String guid = redisUtils.generateGuid(redisUtils.keyGenerate(BusinessName.WX, BusinessName.QUEUE_CONFIG));
                wxQueueConfigDO.setGuid(guid);
                wxQueueConfigDO.setStoreGuid(storeGuid);
                needInitDOList.add(wxQueueConfigDO);
            }
        });
        // 批量保存新的排队配置
        wxQueueConfigService.saveBatch(needInitDOList);
    }

    private void initWxOrderConfigByMQ(String storeGuid) {
        // 获取单个订单配置
        WxOrderConfigDO one = wxStoreOrderConfigService.getOne(new LambdaQueryWrapper<WxOrderConfigDO>().eq(WxOrderConfigDO::getStoreGuid, storeGuid));
        // 如果配置不存在，则创建新的配置
        if (one == null) {
            one = new WxOrderConfigDO();
            one.setGuid(redisUtils.generateGuid(redisUtils.keyGenerate(BusinessName.WX, BusinessName.ORDER_CONFIG)))
                    .setStoreGuid(storeGuid)
                    .setOrderModel(0)
                    .setMenuType(0)
                    .setTakingModel(0)
                    .setIsOnlinePayed(1)
                    .setIsWeighingOrdered(1)
                    .setIsRemarked(1)
                    .setIsOrderOpen(1)
                    .setUrlType(0)
                    .setTagNames(StringUtils.trimWhitespace(TagEnum.getTagIds()));
            // 保存新的订单配置
            if (!wxStoreOrderConfigService.save(one)) {
                throw new BusinessException("门店【" + storeGuid + "】微信点餐配置初始化失败");
            }
        }
    }

    private void initWxQueueConfigByMQ(String storeGuid) {
        // 获取单个排队配置
        WxQueueConfigDO one = wxQueueConfigService.getOne(new LambdaQueryWrapper<WxQueueConfigDO>().eq(WxQueueConfigDO::getStoreGuid, storeGuid));
        // 如果配置不存在，则创建新的配置
        if (one == null) {
            one = new WxQueueConfigDO();
            one.setStoreGuid(storeGuid);
            one.setGuid(redisUtils.generateGuid(redisUtils.keyGenerate(BusinessName.WX, BusinessName.QUEUE_CONFIG)));
            // 保存新的排队配置
            if (!wxQueueConfigService.save(one)) {
                throw new BusinessException("门店【" + storeGuid + "】排队配置初始化失败");
            }
        }
    }
}
