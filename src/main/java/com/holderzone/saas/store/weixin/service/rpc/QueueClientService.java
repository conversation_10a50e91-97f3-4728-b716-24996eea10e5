package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.saas.store.dto.queue.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueClientService
 * @date 2019/05/15 10:13
 * @description 排队client服务
 * @program holder-saas-store
 */
@Service
@FeignClient(name = "holder-saas-store-queue", fallbackFactory = QueueClientService.FallBackClass.class)
public interface QueueClientService {

    @PostMapping("/queue/getQueueDetail")
    @ApiOperation("查询排队详情")
    HolderQueueQueueRecordDTO getQueueDetails(@RequestBody ItemGuidDTO itemGuidDTO);

    @GetMapping("/queue/cancel")
    @ApiOperation("取消排队")
    Boolean cancel(@RequestParam("queueGuid") String queueGuid, @RequestParam("enterpriseGuid") String enterpriseGuid);

    @PostMapping("/queue/queryByUser")
    @ApiOperation("根据用户查询排队信息")
    List<WxQueueListDTO> queryByUser(@RequestBody @Valid QueueWechatDTO queueWechatDTO);

    @PostMapping("/queue/table/obtain")
    @ApiOperation("查询队列对应桌台")
    QueueTableDTO obtain(@RequestBody QueueGuidDTO queueGuidDTO);

    @PostMapping("/queue/item/obtain")
    @ApiOperation("查询指定的排队记录")
    HolderQueueQueueRecordDTO obtain(@RequestBody ItemGuidDTO dto);

    @PostMapping("/queue/item/inQueue")
    HolderQueueItemDetailDTO inQueue(@RequestBody HolderQueueItemDTO holderQueueItemDTO);

    @GetMapping("/queue/config")
    StoreConfigDTO query(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/queue/listByStore")
    List<HolderQueueDTO> listByStore(@RequestBody StoreGuidDTO storeGuid);

    @PostMapping("/queue/allEmpty")
    Boolean allEmpty(@RequestBody StoreGuidDTO storeGuid);

    @Component
    @Slf4j
    class FallBackClass implements FallbackFactory<QueueClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";
        @Override
        public QueueClientService create(Throwable throwable) {
            return new QueueClientService() {
                @Override
                public HolderQueueQueueRecordDTO getQueueDetails(ItemGuidDTO itemGuidDTO) {
                    log.error(HYSTRIX_PATTERN, "/queue/cancel/{enterpriseGuid}", itemGuidDTO, throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public Boolean cancel(String queueGuid, String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/queue/cancel/{enterpriseGuid}", queueGuid, throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public List<WxQueueListDTO> queryByUser(@Valid QueueWechatDTO queueWechatDTO) {
                    log.error(HYSTRIX_PATTERN, "/queue/queryByUser", queueWechatDTO, throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public QueueTableDTO obtain(QueueGuidDTO queueGuidDTO) {
                    log.error(HYSTRIX_PATTERN, "/queue/table/obtain", queueGuidDTO, throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public HolderQueueQueueRecordDTO obtain(ItemGuidDTO dto) {
                    log.error(HYSTRIX_PATTERN, "/queue/item/obtain", dto, throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public HolderQueueItemDetailDTO inQueue(HolderQueueItemDTO holderQueueItemDTO) {
                    log.error(HYSTRIX_PATTERN, "/queue/item/inQueue", holderQueueItemDTO, throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public StoreConfigDTO query(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "/queue/config", storeGuid, throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public List<HolderQueueDTO> listByStore(StoreGuidDTO storeGuid) {
                    log.error(HYSTRIX_PATTERN, "/queue/listByStore", storeGuid, throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public Boolean allEmpty(StoreGuidDTO storeGuid) {
                    log.error(HYSTRIX_PATTERN, "/queue/allEmpty", storeGuid, throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }
            };
        }
    }
}
