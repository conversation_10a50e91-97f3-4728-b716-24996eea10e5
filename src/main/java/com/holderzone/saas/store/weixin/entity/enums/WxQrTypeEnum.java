package com.holderzone.saas.store.weixin.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum WxQrTypeEnum {
    NORMAL_QR(0, "普通二维码"),

    PARAMETERS_QR(1, "带参数二维码"),

    ZHUANCAN_QR(2, "赚餐小程序二维码"),

    WX_CP_QR(3, "赚餐小程序二维码(带企微)"),
    ;
    private int code;

    private String desc;

}
