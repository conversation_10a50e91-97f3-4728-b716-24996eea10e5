package com.holderzone.saas.store.weixin.event;

import java.util.concurrent.DelayQueue;
import java.util.concurrent.ExecutorService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.common.FastOrderCancelDelayDTO;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import com.holderzone.saas.store.weixin.service.WxUserRecordService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreEstimateClientService;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class FastOrderDeal {

	private DelayQueue<FastOrderCancelDelayDTO> queue = new DelayQueue<>();

	private final ExecutorService executorService;

	private final WxStoreDineInOrderClientService wxStoreDineInOrderClientService;

	private final WxStoreEstimateClientService wxStoreEstimateClientService;

	private final WxOrderRecordService wxOrderRecordService;

	@Autowired
	WxUserRecordService wxUserRecordService;

	@Autowired
	private RedisUtils redisUtils;

	private String fastZsetKey= "FastOrder:guid";
	private int timeout = 1000*60*15;

	public FastOrderDeal(ExecutorService executorService, WxStoreDineInOrderClientService wxStoreDineInOrderClientService, WxStoreEstimateClientService wxStoreEstimateClientService, WxOrderRecordService wxOrderRecordService) {
		this.executorService = executorService;
		this.wxStoreDineInOrderClientService = wxStoreDineInOrderClientService;
		this.wxStoreEstimateClientService = wxStoreEstimateClientService;
		this.wxOrderRecordService = wxOrderRecordService;
	}

//	@PostConstruct
//	public void initial() throws InterruptedException {
//		executorService.execute(()->{
//			while (true) {
//				Set<String> zRangeByScore = redisUtils.zRangeByScore(fastZsetKey, 0, System.currentTimeMillis()-timeout);
//				if(CollectionUtils.isEmpty(zRangeByScore)) {
//					zRangeByScore.forEach(orderInfo->{
//						boolean ok = cancelOrder(orderInfo);
//						if(ok) {
//							remove(orderInfo);
//						}
//					});
//				}else {
//					try {
//						Thread.sleep(200);
//					} catch (InterruptedException e) {
//
//					}
//				}
//				/*FastOrderCancelDelayDTO take = null;
//				try {
//					take = queue.take();
//					log.info("订单延迟作废:{}",take);
//				} catch (InterruptedException e) {
//					e.printStackTrace();
//				}
//				FastOrderCancel(take);
//				*/
//			}
//		});
//	}
//
//
//	private boolean cancelOrder(String orderInfo) {
//		if (StringUtils.isEmpty(orderInfo)){
//			return false;
//		}
//		String[] split = orderInfo.split(",");
//		String enterpriseGuid = split[0];
//		String orderGuid = split[1];
//
//		WxOrderRecordDTO one = wxOrderRecordService.getOne(WxOrderRecordQuery.builder().orderGuid(orderGuid).build());
//		WxUserRecordDO wxuserRecord = wxUserRecordService.getWxuserRecord(WxStoreConsumerDTO.builder().consumerGuid(one.getUserRecordGuid()).build());
//		UserInfoDTO userInfoDTO = UserInfoDTO.builder().enterpriseGuid(enterpriseGuid).storeGuid(one.getStoreGuid()).build();
//
//		CompletableFuture.runAsync(() -> {
//			//ThreadLocalCache.put(JacksonUtils.writeValueAsString(userInfoDTO));
//			EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
//
//			DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder().data(orderGuid).build());
//			//
//			if (orderDetail.getTradeMode() == 1 && orderDetail.getState() == 1) {
//				//orderDetail.get
//
//				CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
//				cancelOrderReqDTO.setTable(false);
//				cancelOrderReqDTO.setFastFood(true);
//				cancelOrderReqDTO.setReason("过期做废");
//				cancelOrderReqDTO.setUserGuid(wxuserRecord.getOpenId());
//				cancelOrderReqDTO.setUserName(wxuserRecord.getNickName());
//				cancelOrderReqDTO.setOrderGuid(orderGuid);
//				cancelOrderReqDTO.setDeviceId(wxuserRecord.getOpenId());
//				cancelOrderReqDTO.setDeviceType(12);
//				cancelOrderReqDTO.setEnterpriseGuid(enterpriseGuid);
//				cancelOrderReqDTO.setStoreGuid(one.getStoreGuid());
//				log.info("快餐作废入参:{}",cancelOrderReqDTO);
//				Boolean cancel = wxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO);
//				if (!cancel) {
//					log.error("快餐作废失败:{}",orderInfo);
//				}else {
//					wxStoreEstimateClientService.dineinFail(orderDetail.getDineInItemDTOS());
//					wxOrderRecordService.cancelFastOrder(orderDetail.getGuid());
//				}
//			}
//		}, executorService);
//		return false;
//	}
//
//
//
//	private void FastOrderCancel(FastOrderCancelDelayDTO take) {
//		WxStoreConsumerDTO wxStoreConsumerDTO = take.getWxStoreConsumerDTO();
//		String enterpriseGuid = wxStoreConsumerDTO.getEnterpriseGuid();
//		String storeGuid = wxStoreConsumerDTO.getStoreGuid();
//		UserInfoDTO userInfoDTO = new UserInfoDTO();
//		userInfoDTO.setEnterpriseGuid(enterpriseGuid);
//		userInfoDTO.setStoreGuid(storeGuid);
//
//		CompletableFuture.runAsync(() -> {
//			//ThreadLocalCache.put(JacksonUtils.writeValueAsString(userInfoDTO));
//			EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
//			String orderGuid = take.getOrderGuid();
//
//			DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder().data(orderGuid).build());
//			//
//			if (orderDetail.getTradeMode() == 1 && orderDetail.getState() == 1) {
//				//orderDetail.get
//
//				CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
//				cancelOrderReqDTO.setTable(false);
//				cancelOrderReqDTO.setFastFood(true);
//				cancelOrderReqDTO.setReason("过期做废");
//				cancelOrderReqDTO.setUserGuid(wxStoreConsumerDTO.getOpenId());
//				cancelOrderReqDTO.setUserName(wxStoreConsumerDTO.getNickName());
//				cancelOrderReqDTO.setOrderGuid(orderGuid);
//				cancelOrderReqDTO.setDeviceId(wxStoreConsumerDTO.getOpenId());
//				cancelOrderReqDTO.setDeviceType(12);
//				cancelOrderReqDTO.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
//				cancelOrderReqDTO.setStoreGuid(wxStoreConsumerDTO.getStoreGuid());
//				log.info("快餐作废入参:{}",cancelOrderReqDTO);
//				Boolean cancel = wxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO);
//				if (!cancel) {
//					log.error("快餐作废失败:{}",JacksonUtils.writeValueAsString(take));
//				}else {
//					wxStoreEstimateClientService.dineinFail(orderDetail.getDineInItemDTOS());
//					wxOrderRecordService.cancelFastOrder(orderDetail.getGuid());
//				}
//			}
//		}, executorService);
//	}
//
//	public void remove(String orderGuid) {
//		redisUtils.zRemove(fastZsetKey, orderGuid);
//	}
//	public void add(String orderGuid, WxStoreConsumerDTO wxStoreConsumerDTO) {
//		/*FastOrderCancelDelayDTO delayDTO = new FastOrderCancelDelayDTO();
//		delayDTO.setOrderGuid(orderGuid);
//		delayDTO.setWxStoreConsumerDTO(wxStoreConsumerDTO);
//		delayDTO.setRemainingTime(timeout);
//		queue.add(delayDTO);*/
//		redisUtils.zAdd(fastZsetKey, wxStoreConsumerDTO.getEnterpriseGuid()+","+orderGuid, System.currentTimeMillis()+1000*60*15);
//	}
}
