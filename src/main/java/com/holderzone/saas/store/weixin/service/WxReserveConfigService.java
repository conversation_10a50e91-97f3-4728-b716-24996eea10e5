package com.holderzone.saas.store.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxReserveConfigDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxReserveConfigDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxReserveConfigService
 * @date 2019/12/17 10:12
 * @description
 * @program holder-saas-store
 */
public interface WxReserveConfigService extends IService<WxReserveConfigDO> {
    Page<WxReserveConfigDTO> listConfig(WxStorePageReqDTO wxStorePageReqDTO);

    Boolean updateConfig(WxReserveConfigDTO wxReserveConfigDTO);

    WxReserveConfigDTO getConfig(String guid);

    List<WxReserveConfigDO> initWxReserveConfig(List<String> storeGuidList);

	WxReserveConfigDTO store(String storeGuid);

	List<WxReserveConfigDTO> storeList(List<String> storeGuidList);
}
