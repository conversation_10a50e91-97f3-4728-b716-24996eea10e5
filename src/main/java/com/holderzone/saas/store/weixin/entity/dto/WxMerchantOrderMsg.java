package com.holderzone.saas.store.weixin.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class WxMerchantOrderMsg implements Serializable{

	private static final long serialVersionUID = 1381808430512720039L;

	@ApiModelProperty("是否自动接单，0是，1否")
	private Integer autoRev;

	@ApiModelProperty("预订单guid")
	private String guid;

	@ApiModelProperty("订单状态")
	private Integer orderState;

	@ApiModelProperty("刷新栏目，0全部，1待处理，2已接单，3已拒单")
	private Integer bar;

	/**
	 * 系统设备编号
	 */
	@ApiModelProperty(value = "系统设备编号（云端生成）")
	private String deviceGuid;
}
