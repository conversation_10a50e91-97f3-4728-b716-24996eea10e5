package com.holderzone.saas.store.weixin.utils;

public class OrderUtils {
    OrderUtils(){}
    /**
     trade交易状态READY状态(1：未结账， 2：已结账， 3：已退款，4：已作废)
    前端4种状态 0待确认(待接单,快餐不存在)，1已接单，2已完成，3已取消
     */
    public static Integer tradeStateTransition(Integer state) {
        switch (state.intValue()) {
            case 1:
                //不确定前端0和1
                return null;
            case 2:
                return 2;
            case 12:
                return 1;
            case 3:
                return 3;
            case 4:
                return 3;
        }
        return 3;
    }

    /**
     * 总的订单状态：
     * 正餐4种状态
     * 0：待确认，1：已接单，2：已完成，3：已取消快餐3种状态
     * 1：已接单，2：已完成，3：已取消
     * @param batchStateDB
     * @return
     */


    //batchStateDB:0:待处理，1：接单，2：拒单，3：未结帐，4：已结账,5:已做废, 8订单已失效,9.已退菜
    /**orderRecordDB订单状态:0待确认下单的初始状态；1已下单，已接单正餐；
    //2已支付，微信用户自己支付
    //	 * 3已取消，
    //	 * 4已退菜，
    //	 * 5待支付，快餐
    //	 * 6已完成,一体机结账
    //	 * 7没有开台且直接拒单
*/
    //微信正餐4种状态 0：待确认(待接单,快餐不存在)，1：已接单，2：已完成，3：已取消
    //微信快餐3种状态 0:待确认(待接单，快餐不存在),1：已接单，2：已完成，3：已取消 ，5：待支付（快餐）转1
    //0：待确认（待接单）,1：已下单（正餐接单）,2：已支付，3：已取消 4：已退菜 5：待支付（快餐） 6：已完成 转2
    public static Integer orderBatchStateTransition(Integer batchStateDB) {
        if(batchStateDB==null){
            return 0;
        }
        switch (batchStateDB) {
            case 0:
                return 0;
            case 1:
                return 1;
            case 2:
                return 3;
            case 3:
                return 1;
            case 4:
                return 2;
            case 5:
                return 3;
            case 8:
                return 3;
            case 9:
                return 3;

        }
        return 0;
    }


    /**orderRecordDB订单状态:
     * 0待确认下单的初始状态；
     * 1已下单，已接单正餐；
     //2已支付，微信用户自己支付
     //	 * 3已取消，
     //	 * 4已退菜，
     //	 * 5待支付，快餐
     //	 * 6已完成,一体机结账
     //	 * 7没有开台且直接拒单
     */
    //前端4种状态 0待确认(待接单,快餐不存在)，1已接单，2已完成，3已取消
    public static Integer orderStateTransition(Integer orderStateDB) {
        switch (orderStateDB) {
            case 0:
                return 0;
            case 1:
                return 1;
            case 2:
                return 2;
            case 3:
                return 3;
            case 4:
                return 3;
            case 5:
                return 1;
            case 6:
                return 2;
            case 7:
                return 3;

        }
        return 0;
    }
}
