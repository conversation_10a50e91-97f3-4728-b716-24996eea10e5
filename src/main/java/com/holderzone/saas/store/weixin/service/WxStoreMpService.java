package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import me.chanjar.weixin.common.error.WxErrorException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMpService
 * @date 2019/04/01 16:39
 * @description 微信公众号service
 * @program holder-saas-store
 */
public interface WxStoreMpService {

    /**
     * 桌贴下载，获取默认二维码
     *
     * @param wxQrCodeUrlQuery
     * @return
     */
    String getQrCodeUrl(WxQrCodeUrlQuery wxQrCodeUrlQuery);

    /**
     * 生成h5支付微信授权地址
     */
    String getAuthorizeUrl(WxQrCodeUrlQuery wxQrCodeUrlQuery, String memberInfoGuid, String thirdAppId, String thirdOpenId);

    /**
     * 默认桌贴扫描后获取用户信息
     *
     * @param wxAuthorizeReqDTO
     * @return
     * @throws WxErrorException
     */
    String getUserInfo(WxAuthorizeReqDTO wxAuthorizeReqDTO) throws WxErrorException;

    String verifyMessage(WxCommonReqDTO wxCommonReqDTO);

    String shortenUrl(String longUrl, String tableGuid,Integer type) throws WxErrorException;
}
