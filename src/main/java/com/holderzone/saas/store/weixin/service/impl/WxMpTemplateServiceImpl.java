package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.weixin.open.WxMpTemplateDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxMpTemplateDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.enums.WxTemplateTypeEnum;
import com.holderzone.saas.store.weixin.mapper.WxMpTemplateMapper;
import com.holderzone.saas.store.weixin.service.WxMpTemplateService;
import com.holderzone.saas.store.weixin.service.WxSaasMpService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.WxMpTemplateMsgService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplate;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateIndustry;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateIndustryEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMpTemplateServiceImpl
 * @date 2019/08/16 15:03
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Service
public class WxMpTemplateServiceImpl extends ServiceImpl<WxMpTemplateMapper, WxMpTemplateDO> implements WxMpTemplateService {

    private final WxSaasMpService wxSaasMpService;

    @Autowired
    public WxMpTemplateServiceImpl(WxSaasMpService wxSaasMpService) {
        this.wxSaasMpService = wxSaasMpService;
    }

    @Override
    public String createMsgTemplate(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO, Integer type) {
        if (type == null) {
            log.error("type == null");
            return "未知的模板类型,入参type为空";
        }
        if (Boolean.FALSE.equals(checkIndustry(wxStoreAuthorizerInfoDO))) {
            String result = "公众号行业类别设置失败，请确认公众号是否认证";
            log.error(result);
            return result;
        }
        try {
            WxMpTemplateDO existingTemplate = findExistingTemplate(wxStoreAuthorizerInfoDO, type);
            if (existingTemplate == null || StringUtils.isEmpty(existingTemplate.getTemplateId())) {
                return handleTemplateCreation(wxStoreAuthorizerInfoDO, type);
            }
        } catch (WxErrorException e) {
            log.error("微信服务器异常，请稍后重试", e);
            return "微信服务器异常，请稍后重试";
        }
        return "success";
    }

    private WxMpTemplateDO findExistingTemplate(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO, Integer type) {
        return this.getOne(new LambdaQueryWrapper<WxMpTemplateDO>()
                .eq(WxMpTemplateDO::getAppId, wxStoreAuthorizerInfoDO.getAuthorizerAppid())
                .eq(WxMpTemplateDO::getBrandGuid, wxStoreAuthorizerInfoDO.getBrandGuid())
                .eq(WxMpTemplateDO::getType, type));
    }

    private String handleTemplateCreation(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO, Integer type) throws WxErrorException {
        WxMpTemplateMsgService templateMsgService = getTemplateMsgService(wxStoreAuthorizerInfoDO);
        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
        if (!CollectionUtils.isEmpty(allPrivateTemplate) && allPrivateTemplate.size() >= 25) {
            String result = "当前公众号消息模板已达到上限，请前往公众号后台进行清理，并确保至少能添加两条消息模板";
            log.error(result);
            return result;
        }
        String longId = createOrRetrieveTemplateId(wxStoreAuthorizerInfoDO, type, templateMsgService, allPrivateTemplate);
        return storeTemplate(wxStoreAuthorizerInfoDO, type, longId);
    }

    private String createOrRetrieveTemplateId(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO, Integer type, WxMpTemplateMsgService templateMsgService, List<WxMpTemplate> allPrivateTemplate) throws WxErrorException {
        String shortIdByCode = WxTemplateTypeEnum.getShortIdByCode(type);
        if (Objects.equals(0, type) && StringUtils.hasText(wxStoreAuthorizerInfoDO.getTemplateMsgId())) {
            String longId = wxStoreAuthorizerInfoDO.getTemplateMsgId();
            String finalLongId = longId;
            if (allPrivateTemplate.stream().noneMatch(wxMpTemplate -> Objects.equals(wxMpTemplate.getTemplateId(), finalLongId))) {
                longId = templateMsgService.addTemplate(shortIdByCode);
            }
            return longId;
        } else {
            return templateMsgService.addTemplate(shortIdByCode);
        }
    }

    private String storeTemplate(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO, Integer type, String longId) {
        WxMpTemplateDO build = WxMpTemplateDO.builder()
                .appId(wxStoreAuthorizerInfoDO.getAuthorizerAppid())
                .BrandGuid(wxStoreAuthorizerInfoDO.getBrandGuid())
                .guid(String.valueOf(IdWorker.getId()))
                .shortId(WxTemplateTypeEnum.getShortIdByCode(type))
                .templateId(longId)
                .type(type)
                .build();
        if (!save(build)) {
            String result = "公众号信息初始化失败，请稍后重试";
            log.error(result);
            return result;
        }
        return "success";
    }

    @Override
    public void addMsgTemplate(WxMpTemplateDTO wxMpTemplateDTO) {
        WxMpTemplateDO one = this.getOne(new LambdaQueryWrapper<WxMpTemplateDO>()
                .eq(WxMpTemplateDO::getShortId, wxMpTemplateDTO.getShortId())
                .eq(WxMpTemplateDO::getBrandGuid, wxMpTemplateDTO.getBrandGuid())
                .eq(WxMpTemplateDO::getAppId, wxMpTemplateDTO.getAppId())
                .eq(WxMpTemplateDO::getType, wxMpTemplateDTO.getType())
        );
        if (Objects.nonNull(one)) {
            one.setTemplateId(wxMpTemplateDTO.getTemplateId());
            this.updateById(one);
            return;
        }
        WxMpTemplateDO templateDO = new WxMpTemplateDO();
        templateDO.setGuid(String.valueOf(IdWorker.getId()));
        templateDO.setShortId(wxMpTemplateDTO.getShortId());
        templateDO.setTemplateId(wxMpTemplateDTO.getTemplateId());
        templateDO.setAppId(wxMpTemplateDTO.getAppId());
        templateDO.setBrandGuid(wxMpTemplateDTO.getBrandGuid());
        templateDO.setType(wxMpTemplateDTO.getType());

        this.save(templateDO);
    }

    /**
     * check公众号的行业类别，如果没有则自动设置一个
     * 如果不设置，会导致添加不了消息模板
     */
    private Boolean checkIndustry(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) {
        WxMpTemplateMsgService templateMsgService = getTemplateMsgService(wxStoreAuthorizerInfoDO);
        try { // 查询当前公众号是否配置了行业类别
            WxMpTemplateIndustry wxMpTemplateIndustry = templateMsgService.getIndustry();
            log.info("WxMpTemplateIndustry:{}", wxMpTemplateIndustry);
            return true;
        } catch (WxErrorException e) { // 未配置行业类别的公众号 调用查询接口会跑WxErrorException,捕获后配置行业类别
            WxMpTemplateIndustry wxMpTemplateIndustry = new WxMpTemplateIndustry();
            wxMpTemplateIndustry.setPrimaryIndustry(WxMpTemplateIndustryEnum.E_COMMERCE);
            wxMpTemplateIndustry.setSecondIndustry(WxMpTemplateIndustryEnum.REPAST);
            try {
                return templateMsgService.setIndustry(wxMpTemplateIndustry);
            } catch (WxErrorException e1) {
                log.error("设置公众号行业类型失败, e:", e1);
                return false;
            }
        }
    }

    private WxMpTemplateMsgService getTemplateMsgService(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) {
        try {
            WxMpService wxMpService = wxSaasMpService.getWxMpService(wxStoreAuthorizerInfoDO);
            return wxMpService.getTemplateMsgService();
        } catch (WxErrorException e) {
            throw new BusinessException("初始化公众号消息模板发生异常");
        }
    }
}
