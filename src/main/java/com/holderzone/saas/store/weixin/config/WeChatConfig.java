package com.holderzone.saas.store.weixin.config;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.StringUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/9 9:56
 * @description 微信参数配置相关
 */
@Configuration
@RefreshScope
@Data
public class WeChatConfig {
    @Value("${wechat.graphic-message.keyWord:魅力城,华阳}")
    private String keyWords;

    @Value("${wechat.graphic-message.mediaId}")
    private String mediaId;

    @Value("${wechat.graphic-message.appId}")
    private String appId;

    @Value("${wechat.mp.sendBackMessage}")
    private String sendBackMessage;

    @Value("${wechat.mp.baseJumpUrl}")
    private String baseJumpUrl;

    @Value("${wechat.mp.bossAuthorizeUrl}")
    private String bossAuthorizeUrl;

    @Value("${wechat.mp.bossRedirectUrl}")
    private String bossRedirectUrl;

    @Value("${wechat.mp.biBossRedirectUrl}")
    private String biBossRedirectUrl;

    @Value("${wechat.mp.orderPageUrl}")
    private String orderPageUrl;

    @Value("${wechat.queue.detailPage}")
    private String QUEUE_DETAIL_PAGE;

    @Value("${wechat.mp.memberOrderPage}")
    private String MEMBER_ORDER_PAGE;

//    @Value("${wechat.mp.miniProgram.erpriseGuids}")
//    private String miniProgramentErpriseGuids;
//
//    @Value("${wechat.mp.miniProgram.storeGuids}")
//    private String miniProgramentStoreGuids;

    @Value("${wechat.queue.shopListPage}")
    private String shopListPage;

    @Value("${wechat.member.loginUrl}")
    private String loginUrl;

    @Value("${wechat.member.baseUrl}")
    private String baseUrl;

    @Value("${wechat.member.bindingUrl}")
    private String bindingUrl;

//    @Value("${wechat.mp.miniProgram.thumb_media_id}")
//    String thumb_media_id ;
//
//    @Value("${wechat.mp.miniProgram.appid}")
//    String miniAppid ;
//
//    @Value("${wechat.mp.miniProgram.pagepath}")
//    String pagepath;
//
//    @Value("${wechat.mp.miniProgram.title}")
//    String title;

    @Value("${wechat.redirectUrl}")
    private String REDIRECT_URI;

    @Value("${wechat.callBackSuccessPage}")
    private String CALL_BACK_SUCCESS_PAGE;

    @Value("${wechat.callBackErrorPage}")
    private String CALL_BACK_ERROR_PAGE;

    @Value("${wxStorePay.appId}")
    private String payAppId;

    @Value("${wxStorePay.mchntName}")
    private String mchntName;

    @Value("${wxStorePay.appSecret}")
    private String appSecret;

    @Value("${memberPay.key}")
    private String memberPaykey;

    @Value("${wechat.mp.baseQrCodeUrl}")
    private String baseQrCodeUrl;

    @Value("${workbench.loginUrl}")
    private String workbenchUrl;

    @Value("${wechat.appId}")
    private String internalAppId;


    @Value("${tongchidao.table_ticket_info}")
    String tongChiDaoTableTicketInfo;


    @Value("${wechat.mp.miniProgram}")
    private String miniProgram;

    /**
     * 获取关键词与图片id
     * @return
     */
    public static Map<String,String> getKeyWords(String keyWords,String mediaId){
        if(StringUtils.isEmpty(keyWords)){
            return Collections.emptyMap();
        }
        String[] keyWordsStr = keyWords.split(",");
        String[] mediaIdStr = mediaId.split(",");
        Map<String,String> keyWordsMap = new HashMap<>();
        for (int i = 0; i<keyWordsStr.length; i++){
            keyWordsMap.put(keyWordsStr[i],mediaIdStr[i]);
        }
        return keyWordsMap;
    }

    /**
     * 获取关键词对应的图片二维码id
     * @return map
     */
    public Map<String,String> getKeyWordsMap(){
        return getKeyWords(keyWords,mediaId);
    }

    /**
     * 获取配置的可发送卡片消息企业相关信息
     * @return
     */
    public static Map<String,WeChatCardMessageConfig> getCardMessageConfigMap(String miniProgram){
        if(StringUtils.isEmpty(miniProgram)){
            return Collections.emptyMap();
        }
        List<WeChatCardMessageConfig> configList = JSON.parseArray(miniProgram, WeChatCardMessageConfig.class);
        if (CollectionUtils.isEmpty(configList)){
            return Collections.emptyMap();
        }
        Map<String, WeChatCardMessageConfig> configMap = configList.stream().collect(Collectors.toMap(WeChatCardMessageConfig::getEnterpriseGuid, weChatCardMessageConfig -> weChatCardMessageConfig));
        return configMap;
    }

    /***
     *  获取卡片消息发生配置
     * @return
     */
    public WeChatCardMessageConfig getCardMessageConfig(String enterpriseGuid){
        Map<String, WeChatCardMessageConfig> cardMessageConfigMap = getCardMessageConfigMap(miniProgram);
        if (MapUtils.isEmpty(cardMessageConfigMap)){
            return new WeChatCardMessageConfig();
        }
        return cardMessageConfigMap.get(enterpriseGuid);
    }
}
