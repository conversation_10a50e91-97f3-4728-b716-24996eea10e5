package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.holderzone.saas.store.enums.weixin.WxServiceTypeEnum;
import com.holderzone.saas.store.enums.weixin.WxVerifyTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreAuthorizerInfoDO
 * @date 2019/02/27 17:59
 * @description 微信授权方信息DO
 * @program holder-saas-store-weixin
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hsw_weixin_authorizer_info")
public class WxStoreAuthorizerInfoDO {

    private Long id;

    private String guid;

    /**
     * 品牌guid
     */
    @TableId(value = "brand_guid")
    private String brandGuid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 授权方AppId
     */
    private String authorizerAppid;

    /**
     * 授权方accessToken
     */
    private String authorizerAccessToken;

    /**
     * 过期时间，存入到期时间的时间戳，若当前时间戳大于该时间戳，则已过期
     */
    private Long expiresIn;

    /**
     * 授权方刷新token
     */
    private String authorizerRefreshToken;

    /**
     * 已授权权限集合（以JSON格式存入）
     */
    private String funcInfo;

    /**
     * 授权方昵称（公众号名）
     */
    private String nickName;

    /**
     * 授权方头像
     */
    private String headImg;

    /**
     * 授权方公众号类型
     * 0代表订阅号，
     * 1代表由历史老帐号升级后的订阅号，
     * 2代表服务号
     */
    private Integer serviceTypeInfo;

    /**
     * 授权方认证类型
     * -1代表未认证，
     * 0代表微信认证，
     * 1代表新浪微博认证，
     * 2代表腾讯微博认证，
     * 3代表已资质认证通过但还未通过名称认证，
     * 4代表已资质认证通过、还未通过名称认证，但通过了新浪微博认证，
     * 5代表已资质认证通过、还未通过名称认证，但通过了腾讯微博认证
     */
    private Integer verifyTypeInfo;

    /**
     * 授权方公众号的原始ID
     */
    private String userName;

    /**
     * 公众号的主体名称
     */
    private String principalName;

    /**
     * 授权方公众号所设置的微信号，可能为空
     */
    private String alias;

    /**
     * 二维码图片的URL
     */
    private String qrcodeUrl;

    /**
     * 帐号介绍
     */
    private String signature;

    /**
     * 解绑用户guid
     */
    private String unBandUserGuid;

    /**
     * 解绑用户姓名
     */
    private String unBandUserName;

    /**
     * 解绑用户电话
     */
    private String unBandUserTel;

    /**
     * 解绑时间
     */
    private LocalDateTime unBandTime;

    @TableLogic
    private Integer isDeleted;

    @Version
    private Integer version = 0;

    /**
     * 微信消息模板id
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String templateMsgId;

    /***
     * 是否是联盟主体
     */
    private Boolean isAlliance;
    /***
     * 运营主体Guid
     */
    private String operSubjectGuid;

    public Boolean isAuthService() {
        return Objects.equals(WxVerifyTypeEnum.WECHAT_AUTH.getCode(), this.getVerifyTypeInfo()) &&
                Objects.equals(WxServiceTypeEnum.SERVICE_NUMBER.getCode(), this.getServiceTypeInfo());
    }
}
