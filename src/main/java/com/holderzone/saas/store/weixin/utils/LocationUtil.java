package com.holderzone.saas.store.weixin.utils;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LocationUtil
 * @date 2019/01/16 13:59
 * @description 地理直线距离计算工具类
 * @program holder-saas-store-weixin
 */
public class LocationUtil {
    private static final double EARTH_RADIUS = 6378137; // 平均半径,单位：m

    private static double DEF_PI = Math.PI;// PI
    private static double DEF_2PI = Math.PI * 2;// 2*PI
    private static double DEF_PI180 = Math.PI / 180;// PI/180.0

    /**
     * 短距离直线距离计算
     *
     * @param lonA A点经度
     * @param latA A点纬度
     * @param lonB B点经度
     * @param latB B点纬度
     * @return A-B两点间的直线距离，单位m
     */
    public static Double GetShortDistance(Double lonA, Double latA, Double lonB, Double latB) {
        Double ew1, ns1, ew2, ns2;
        Double dx, dy, dew;
        Double distance;
        // 角度转换为弧度
        ew1 = lonA * DEF_PI180;
        ns1 = latA * DEF_PI180;
        ew2 = lonB * DEF_PI180;
        ns2 = latB * DEF_PI180;
        // 经度差
        dew = ew1 - ew2;
        // 若跨东经和西经180 度，进行调整
        if (dew > DEF_PI)
            dew = DEF_2PI - dew;
        else if (dew < -DEF_PI)
            dew = DEF_2PI + dew;
        dx = EARTH_RADIUS * Math.cos(ns1) * dew;// 东西方向长度(在纬度圈上的投影长度)
        dy = EARTH_RADIUS * (ns1 - ns2);// 南北方向长度(在经度圈上的投影长度)
        // 勾股定理求斜边长
        distance = Math.sqrt(dx * dx + dy * dy);
        return distance;
    }

    /**
     * 长距离直线距离计算
     *
     * @param lonA A点经度
     * @param latA A点纬度
     * @param lonB B点经度
     * @param latB B点纬度
     * @return A-B两点间的直线距离，单位m
     */
    public static Double GetLongDistance(Double lonA, Double latA, Double lonB, Double latB) {
        Double ew1, ns1, ew2, ns2;
        Double distance;
        // 角度转换为弧度
        ew1 = lonA * DEF_PI180;
        ns1 = latA * DEF_PI180;
        ew2 = lonB * DEF_PI180;
        ns2 = latB * DEF_PI180;
        // 求大圆劣弧与球心所夹的角(弧度)
        distance = Math.sin(ns1) * Math.sin(ns2) + Math.cos(ns1) * Math.cos(ns2) * Math.cos(ew1 - ew2);
        // 调整到[-1..1]范围内，避免溢出
        if (distance > 1.0)
            distance = 1.0;
        else if (distance < -1.0)
            distance = -1.0;
        // 求大圆劣弧长度
        distance = EARTH_RADIUS * Math.acos(distance);
        return distance;
    }
}
