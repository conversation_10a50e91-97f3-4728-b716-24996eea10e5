package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreWebSocketUserDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreMenuReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxMenuDetailsDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMenuDetailsService
 * @date 2019/2/22 13:46
 * @description 微信点餐获取菜单基本信息service
 * @package com.holderzone.saas.store.weixin.service
 */
public interface WxStoreMenuDetailsService {
    /**
     * @param wxStoreMenuReqDTO
     * @return WxMenuDetailsDTO
     * @describe 返回微信点餐获取菜单基本情况
     * <AUTHOR>
     * @name getWxMenuDetailsDTO
     * @date 2019/2/23 15:18
     */
    WxMenuDetailsDTO getWxMenuDetails(WxStoreMenuReqDTO wxStoreMenuReqDTO);

    /**
     * @param wxStoreConsumerDTO
     * @return
     * @describle 获取当前门店配置详情
     */
    WxOrderConfigDTO getStoreConfiguration(WxStoreConsumerDTO wxStoreConsumerDTO);

    WxStoreConsumerDTO getConsumerInfo(WxPortalReqDTO wxPortalReqDTO);

    /**
     * 获取门店配置详情
     *
     * @param wxStoreAdvanceConsumerReqDTO
     * @return
     */
    WxOrderConfigDTO getStoreConfiguration(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    /**
     * @param wxStoreAdvanceConsumerReqDTO
     * @return
     * @describle 判断是否是正餐
     */
    Boolean judgeOrderType(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    /**
     * 判断是否是正餐
     *
     * @param wxStoreWebSocketUserDTO
     * @return
     */
    Boolean judgeOrderType(WxStoreWebSocketUserDTO wxStoreWebSocketUserDTO);

    /**
     * 接单模式 （0所有订单均需手动确认，1首单需手动确认，加菜无需确认，2无需确认（仅适用于快餐模式））
     *
     * @param wxStoreAdvanceConsumerReqDTO
     * @return
     */
    Integer getTakingModel(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    /**
     * 接单模式
     *
     * @param storeGuid
     * @return
     */
    Integer getTakingModel(String storeGuid);

    Boolean judgeOrderType(String storeGuid);



	void estimateItem(List<WxStoreItemRespDTO> wxStoreItem, UserInfoDTO userInfoDTO);
	
	public WxOrderConfigDTO getDetailConfig(String storeGuid);
}
