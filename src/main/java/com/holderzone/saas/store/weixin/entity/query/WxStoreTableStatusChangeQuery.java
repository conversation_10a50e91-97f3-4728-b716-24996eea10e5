package com.holderzone.saas.store.weixin.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description 
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTableStatusChangeQuery
 * @date 2019/5/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxStoreTableStatusChangeQuery {
	
	private List<String> merchantBatchGuidList;

	private String combine;

}
