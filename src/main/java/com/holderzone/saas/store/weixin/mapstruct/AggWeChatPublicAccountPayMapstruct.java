package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.pay.AggWeChatPublicAccountPayDTO;
import com.holderzone.saas.store.dto.pay.SaasAggWeChatPublicAccountPayDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className AggWeChatPublicAccountPayMapstruct
 * @date 2019/4/3
 */
@Component
@Mapper(componentModel = "spring")
public interface AggWeChatPublicAccountPayMapstruct {

	@Mappings({
			@Mapping(target = "amount",source = "actuallyPayFee"),
			@Mapping(target = "orderGUID",source = "orderNo"),
			@Mapping(target = "payGUID",source = "orderNo"),
			@Mapping(target = "body",source = "orderNo"),
	})
	AggWeChatPublicAccountPayDTO getAggWeChatPublicAccountPay(DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

	@Mappings({
			@Mapping(target = "publicAccountPayDTO", source = "dineinOrderDetailRespDTO"),
			@Mapping(target = "saasCallBackUrl", source = "callbackUrl"),
			@Mapping(target = "enterpriseGuid", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.enterpriseGuid"),
			@Mapping(target = "storeGuid", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid"),
	})
	SaasAggWeChatPublicAccountPayDTO getSaasAggWeChatPublicAccountPay(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, String callbackUrl,WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);
}
