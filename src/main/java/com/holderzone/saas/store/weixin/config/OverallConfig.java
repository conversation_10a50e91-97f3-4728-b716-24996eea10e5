package com.holderzone.saas.store.weixin.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @date 2020/9/25 11:45
 * @description 配置
 */
@Configuration
@RefreshScope
@Data
public class OverallConfig {
    @Value("${tongchidao.push_order_detail_url}")
    String pushOrderDetailUrl;
}
