package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueConfigDO
 * @date 2019/05/09 17:09
 * @description 门店微信线上排队配置DO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("hsw_weixin_queue_config")
public class WxQueueConfigDO {
    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableId("guid")
    private String guid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 是否开启线上排队（0关闭，1开启）（0暂停排队，1开启排队）
     */
    private Integer isQueueOpen;

    /**
     * 排队最远距离
     */
    private Integer distant;

    /**
     * 是否开启距离限制（0关闭，1开启）
     */
    private Integer distantConstraint;

    /**
     * 排队附加费（不做）
     */
    private BigDecimal queueFee;

    /**
     * 是否可以排队预点单（0关闭，1开启。不做）
     */
    private Integer couldQueueOrder;
}
