package com.holderzone.saas.store.weixin.event;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.dto.table.LocalOrderSaveMQDTO;
import com.holderzone.saas.store.weixin.constant.RocketMqConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
@RocketListenerHandler(topic = RocketMqConfig.WECHAT_ORDER_CHANGE_MQ_TOPIC,tags=RocketMqConfig.WECHAT_ORDER_CHANGE_MQ_TAG,consumerGroup = RocketMqConfig.WECHAT_ORDER_CHANGE_MQ_GROUP)
public class OrderIDReplaceListener extends AbstractRocketMqConsumer<RocketMqTopic, LocalOrderSaveMQDTO> {

	private final WxOrderRecordService wxOrderRecordService;

	@Autowired
	public OrderIDReplaceListener(WxOrderRecordService wxOrderRecordService) {
		this.wxOrderRecordService = wxOrderRecordService;
	}

	@Override
	public boolean consumeMsg(LocalOrderSaveMQDTO localOrderSaveMQDTO, MessageExt messageExt) {
		log.info("接收本地化同步:{}",localOrderSaveMQDTO);
		if (localOrderSaveMQDTO != null) {
			String enterpriseGuid = localOrderSaveMQDTO.getEnterpriseGuid();
			Map<String, String> orderMap = localOrderSaveMQDTO.getOrderMap();
			if (!StringUtils.isEmpty(enterpriseGuid)&& !ObjectUtils.isEmpty(orderMap)) {
				UserContextUtils.put(UserContext.builder().enterpriseGuid(enterpriseGuid).build());
				EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
//				wxOrderRecordService.replaceOrderID(orderMap);
				Set<String> oldGuidList = orderMap.keySet();
				List<WxOrderRecordDO> oldOrderList = wxOrderRecordService.getOldOrderList(oldGuidList);
				log.info("本地化旧订单:{}",oldOrderList);
				if (!ObjectUtils.isEmpty(oldOrderList)) {
					List<WxOrderRecordDO> newOrderList=oldOrderList.stream().peek(x -> {
						String orderGuid = x.getOrderGuid();
						String newOrderGuid = orderMap.get(orderGuid);
						if (!StringUtils.isEmpty(newOrderGuid)) {
							x.setOrderGuid(newOrderGuid);
						}
					}).collect(Collectors.toList());
					log.info("本地化新订单:{}",newOrderList);
					wxOrderRecordService.batchSave(newOrderList);
				}
				return true;
			}
		}
		return false;
	}

}
