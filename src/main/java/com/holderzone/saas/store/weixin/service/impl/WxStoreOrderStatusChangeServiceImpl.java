package com.holderzone.saas.store.weixin.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseAccountStatus;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.table.TableStatusChangeDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberModeNotifyReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberTradeNotifyReqDTO;
import com.holderzone.saas.store.enums.weixin.WxOrderStateEnum;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderItemDO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.event.NotifyMessageQueue;
import com.holderzone.saas.store.weixin.helper.WebsocketMessageHelper;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import com.holderzone.saas.store.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WxStoreOrderStatusChangeServiceImpl implements WxStoreOrderStatusChangeService {

    @Autowired
    private WxStoreSessionDetailsService wxStoreSessionDetailsService;
    @Autowired
    private WxStoreMerchantOrderService wxStoreMerchantOrderService;
    @Autowired
    private WxStoreDineInOrderClientService wxStoreDineInOrderClientService;
    @Autowired
    private WxOrderRecordService wxOrderRecordService;
    @Autowired
    private WxStoreTradeOrderService wxStoreTradeOrderService;
    @Autowired
    private MemberClientService memberClientService;
    @Lazy
    @Resource
    private OrderItemService orderItemService;
    @Resource
    private WxUserRecordService wxUserRecordService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private UserMemberSessionUtils userMemberSessionUtils;
    @Resource
    private WebsocketMessageHelper websocketMessageHelper;
    @Resource
    private NotifyMessageQueue notifyMessageQueue;
    @Autowired
    private HsaBaseClientService hsaBaseClientService;
    @Autowired
    private EnterpriseClientService enterpriseClientService;

    @Override
    public void complete(TableStatusChangeDTO tableStatusChangeDTO) {
        log.info("complete请求参数：{}", JacksonUtils.writeValueAsString(tableStatusChangeDTO));
        DineinOrderDetailRespDTO orderDetails = getOrderDetails(tableStatusChangeDTO);
        if (orderDetails == null) {
            log.info("监听关台，订单为空，不予处理:{}", tableStatusChangeDTO);
            return;
        }

        // 获取订单GUID并查询相关微信订单
        String orderGuid = tableStatusChangeDTO.getOrderGuid();
        List<WxStoreMerchantOrderDO> merchantOrders = wxStoreMerchantOrderService.list(
            new LambdaQueryWrapper<WxStoreMerchantOrderDO>()
                .eq(WxStoreMerchantOrderDO::getOrderGuid, orderGuid)
                .eq(WxStoreMerchantOrderDO::getIsDel, 0)
                .eq(WxStoreMerchantOrderDO::getOrderState, 0)
        );

        if (!CollectionUtils.isEmpty(merchantOrders)) {
            // 删除缓存中的用户计数
            redisUtils.delete(CacheName.USER_COUNT + ":" + tableStatusChangeDTO.getTableGuid());

            // 处理订单记录
            processOrderRecords(merchantOrders);

            // 更新商户订单状态
            updateMerchantOrders(merchantOrders, tableStatusChangeDTO);
        }

        // 检查订单商品信息
        List<DineInItemDTO> dineInItems = orderDetails.getDineInItemDTOS();
        List<ReturnItemDTO> returnItems = orderDetails.getReturnItemDTOS();
        if (CollectionUtils.isEmpty(dineInItems) && CollectionUtils.isEmpty(returnItems)) {
            log.info("监听关台，订单没有商品，不予处理:{}", tableStatusChangeDTO);
            return;
        }

        // 合并退货商品到堂食商品列表
        if (CollectionUtils.isEmpty(dineInItems)) {
            dineInItems = returnItems.stream()
                .map(ReturnItemDTO::getDineInItemDTO)
                .collect(Collectors.toList());
        }

        // 处理订单状态
        handleOrderState(orderDetails, tableStatusChangeDTO, dineInItems);
    }

    private void processOrderRecords(List<WxStoreMerchantOrderDO> merchantOrders) {
        List<String> orderRecordGuidList = merchantOrders.stream()
            .map(WxStoreMerchantOrderDO::getOrderRecordGuid)
            .distinct()
            .collect(Collectors.toList());

        Collection<WxOrderRecordDO> wxOrderRecords = wxOrderRecordService.listByIds(orderRecordGuidList);
        log.info("关台拒单，wxOrderRecordDOList={}", JacksonUtils.writeValueAsString(wxOrderRecords));

        wxOrderRecords.forEach(orderRecordDO -> {
            if (orderRecordDO != null) {
                List<WxOrderItemDO> orderItems = orderItemService.lambdaQuery()
                    .eq(WxOrderItemDO::getOrderRecordGuid, orderRecordDO.getGuid())
                    .select(WxOrderItemDO::getItemName, WxOrderItemDO::getOriginalPrice, WxOrderItemDO::getMemberPrice)
                    .list();
                log.info("关台拒单，商品列表:{}", orderItems);

                if (!ObjectUtils.isEmpty(orderItems)) {
                    BigDecimal originPrice = BigDecimal.ZERO;
                    BigDecimal memberPrice = BigDecimal.ZERO;
                    for (WxOrderItemDO item : orderItems) {
                        originPrice = originPrice.add(item.getOriginalPrice());
                        if (Boolean.TRUE.equals(orderRecordDO.getIsLogin())) {
                            memberPrice = memberPrice.add(item.getMemberPrice());
                        }
                    }
                    orderRecordDO.setActuallyPayFee(originPrice);
                    orderRecordDO.setUnMemberPrice(memberPrice);
                    orderRecordDO.setItemName(orderItems.stream()
                        .map(WxOrderItemDO::getItemName)
                        .collect(Collectors.joining(",")));
                }
                orderRecordDO.setOrderState(WxOrderStateEnum.REJECTED.getCode());
                orderRecordDO.setOrderStateName(WxOrderStateEnum.REJECTED.getOrderStateName());
                boolean update = wxOrderRecordService.updateById(orderRecordDO);
                log.info("关台拒单，更新订单:{},{}", update, orderRecordDO);
            }
        });
    }

    private void updateMerchantOrders(List<WxStoreMerchantOrderDO> merchantOrders, TableStatusChangeDTO tableStatusChangeDTO) {
        merchantOrders.forEach(order -> {
            order.setOrderState(2);
            order.setDenialReason("关台拒单");
            order.setOperationGuid(tableStatusChangeDTO.getUserGuid());
            order.setOperationName(tableStatusChangeDTO.getUserName());
            order.setOperationSource(tableStatusChangeDTO.getDeviceType());
        });
        wxStoreMerchantOrderService.updateBatchById(merchantOrders);
    }

    private void handleOrderState(DineinOrderDetailRespDTO orderDetails, TableStatusChangeDTO tableStatusChangeDTO, List<DineInItemDTO> dineInItems) {
        Integer state = orderDetails.getState();
        log.info("监听到有微信订单结账或作废:{}", orderDetails);

        if ((2 != state && 4 != state) || orderDetails.getTradeMode() == 1 || StringUtils.isEmpty(orderDetails.getOrderNo())
                || orderDetails.getOrderNo().startsWith("F")) {
            log.error("没有查询到订单数据:{},或者订单没有结账或作废", tableStatusChangeDTO);
            return;
        }

        List<WxOrderRecordDO> outStandingOrders = wxOrderRecordService.getOutStandingOrders(tableStatusChangeDTO.getTableGuid());
        log.info("关台时：查询当前桌台微信订单:{}", outStandingOrders);
        if (ObjectUtils.isEmpty(outStandingOrders)) {
            return;
        }

        WxOrderRecordDO wxOrderRecordDO = outStandingOrders.get(0);
        Integer orderState = wxOrderRecordDO.getOrderState();
        if (orderState == null || !Arrays.asList(0, 1, 5).contains(orderState)) {
            log.info("订单已完结，无需处理:{}", wxOrderRecordDO);
            return;
        }

        orderState = state == 2 ? 2 : 3;
        String orderStateName = state == 2 ? "已支付MQ" : "已取消MQ";
        wxOrderRecordDO.setOrderGuid(orderDetails.getGuid());
        wxOrderRecordDO.setOrderState(orderState).setOrderStateName(orderStateName);
        String collect = dineInItems.stream().map(DineInItemDTO::getItemName).collect(Collectors.joining(","));
        wxOrderRecordDO.setItemName(collect);
        WxStoreMerchantOrderDO wxStoreMerchantOrderDO = wxStoreMerchantOrderService.firstSubmit(wxOrderRecordDO.getGuid());
        log.info("第一个下单人:{}", wxStoreMerchantOrderDO);
        if (wxStoreMerchantOrderDO == null) {
            log.info("找不到下单批次:{}", wxOrderRecordDO);
            return;
        }
        WxUserRecordDO one = wxUserRecordService.getOneByOpenId(wxStoreMerchantOrderDO.getOpenId());
        log.info("第一个下单人id:{}", one);
        if (one == null) {
            log.info("找不到用户:{}", wxStoreMerchantOrderDO);
            return;
        }
        wxOrderRecordDO.setUserRecordGuid(one.getGuid());
        wxOrderRecordService.updateById(wxOrderRecordDO);

        LambdaUpdateWrapper<WxStoreMerchantOrderDO> wrappers = Wrappers.<WxStoreMerchantOrderDO>lambdaUpdate()
                .eq(WxStoreMerchantOrderDO::getOrderRecordGuid, wxOrderRecordDO.getGuid())
                .in(WxStoreMerchantOrderDO::getOrderState, 0)
                .set(WxStoreMerchantOrderDO::getOrderState, 2);
        boolean update = wxStoreMerchantOrderService.update(wrappers);
        log.info("一体机完结:批次处理结果:{}", update);
        redisUtils.delete(CacheName.USER_COUNT + ":" + tableStatusChangeDTO.getTableGuid());

        Set<String> openIDS = redisUtils.hKeyList(CacheName.DINE_USER + ":hash:" + wxOrderRecordDO.getTableGuid());
        log.info("桌台就餐人:{}", openIDS);
        websocketMessageHelper.sendOrderEmqMessage(tableStatusChangeDTO.getTableGuid(), openIDS);
        userMemberSessionUtils.delTableCardList(wxOrderRecordDO.getStoreGuid(), openIDS);
        redisUtils.delete(CacheName.DINE_USER + ":hash:" + wxOrderRecordDO.getTableGuid());

        handleMemberNotification(orderDetails, tableStatusChangeDTO, wxOrderRecordDO);
    }

    private void handleMemberNotification(DineinOrderDetailRespDTO orderDetails, TableStatusChangeDTO tableStatusChangeDTO, WxOrderRecordDO wxOrderRecordDO) {
        String memberGuid = orderDetails.getMemberGuid();
        log.info("关台:订单会员id:{}", memberGuid);
        if (!StringUtils.isEmpty(memberGuid) && orderDetails.getState() == 2) {
            UserContext userContext = UserContextUtils.get();
            userContext.setEnterpriseGuid(tableStatusChangeDTO.getEnterpriseGuid());
            userContext.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
            UserContextUtils.put(userContext);
            String openId = hsaBaseClientService.findOpenIDByMemberGuid(memberGuid).getData();
            log.info("根据会员id查询openId:{}", openId);
            if (StringUtils.isEmpty(openId)) {
                return;
            }

            ResponseAccountStatus memberState = hsaBaseClientService.getMemberState(openId, tableStatusChangeDTO.getEnterpriseGuid()).getData();
            log.info("关台：会员状态:{}", memberState);
            if (memberState == null || memberState.getAccountState() == 1) {
                return;
            }
            WxMemberTradeNotifyReqDTO wxMemberTradeNotifyReqDTO = new WxMemberTradeNotifyReqDTO();
            wxMemberTradeNotifyReqDTO.setEnterpriseGuid(tableStatusChangeDTO.getEnterpriseGuid());
            wxMemberTradeNotifyReqDTO.setActuallyPayFee(orderDetails.getActuallyPayFee());
            wxMemberTradeNotifyReqDTO.setBrandGuid(wxOrderRecordDO.getBrandGuid());
            wxMemberTradeNotifyReqDTO.setMemberInfoCardGuid(memberGuid);
            wxMemberTradeNotifyReqDTO.setOpenId(openId);
            wxMemberTradeNotifyReqDTO.setOrderGuid(wxOrderRecordDO.getGuid());
            wxMemberTradeNotifyReqDTO.setStoreGuid(wxOrderRecordDO.getStoreGuid());
            wxMemberTradeNotifyReqDTO.setStoreName(wxOrderRecordDO.getStoreName());
            wxMemberTradeNotifyReqDTO.setMemberInfoCardGuid(orderDetails.getMemberCardGuid());
            log.info("订单完成微信模板消息：{}", wxMemberTradeNotifyReqDTO);

            WxMemberModeNotifyReqDTO wxMemberModeNotifyReqDTO = new WxMemberModeNotifyReqDTO();
            wxMemberModeNotifyReqDTO.setWxMemberTradeNotifyReqDTO(wxMemberTradeNotifyReqDTO);
            wxMemberModeNotifyReqDTO.setRemainingTime(System.currentTimeMillis() + 30000L);
            UserInfoDTO userInfoDTO = new UserInfoDTO();
            userInfoDTO.setEnterpriseGuid(tableStatusChangeDTO.getEnterpriseGuid());
            userInfoDTO.setStoreGuid(wxOrderRecordDO.getStoreGuid());
            userInfoDTO.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
            userInfoDTO.setSource("8");
            wxMemberModeNotifyReqDTO.setUserInfoDTO(userInfoDTO);
            log.info("订单完成微信模板消息：{}", wxMemberTradeNotifyReqDTO);
            notifyMessageQueue.add(wxMemberModeNotifyReqDTO);
        }
    }

    @Override
    public void cancel(TableStatusChangeDTO tableStatusChangeDTO) {
        String merchantBatchGuid = wxStoreSessionDetailsService.getMerchantBatchGuid(tableStatusChangeDTO.getTableGuid());
        if (StringUtils.isEmpty(merchantBatchGuid)) {
            log.info("订单状态变化没有微信订单，不予处理");
            return;
        }
    }

    /**
     * 获取订单详情
     *
     * @param tableStatusChangeDTO
     * @return
     */
    private DineinOrderDetailRespDTO getOrderDetails(TableStatusChangeDTO tableStatusChangeDTO) {
        String enterpriseGuid = tableStatusChangeDTO.getEnterpriseGuid();
        String storeGuid = tableStatusChangeDTO.getStoreGuid();
        String operSubjectGuid = "";
        //后续查询会员信息时 需要运营主体
        if (StringUtils.isNotBlank(storeGuid)) {
            try {
                MultiMemberDTO memberInfoByOrganizationGuid = enterpriseClientService.findMemberInfoByOrganizationGuid(storeGuid);
                log.info("getOrderDetails 查询门店的运营主体Guid{}", JacksonUtils.writeValueAsString(memberInfoByOrganizationGuid));
                if (Objects.nonNull(memberInfoByOrganizationGuid)) {
                    operSubjectGuid = memberInfoByOrganizationGuid.getMultiMemberGuid();
                }
            } catch (Exception e) {
                log.error("查询门店运营主体异常！", e);
            }
        }
        UserContextUtils.put(JacksonUtils.writeValueAsString(UserInfoDTO.builder().enterpriseGuid(enterpriseGuid).storeGuid(storeGuid).build()));
        if (StringUtils.isNotBlank(operSubjectGuid)) {
            UserContext userContext = UserContextUtils.get();
            userContext.setOperSubjectGuid(operSubjectGuid);
            UserContextUtils.put(userContext);
        }
        DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder().data(tableStatusChangeDTO.getOrderGuid()).build());
        return wxStoreTradeOrderService.getMainOrderDetails(orderDetail);
    }
}
