package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.dto.trade.req.PadOrderPlacementReqDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderPlacementRespDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.PadPriceRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @description 订单远程调用
 * @date 2021/8/27 11:03
 * @className: TradeClientService
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = TradeClientService.FastFoodFallBack.class)
public interface TradeClientService {

    @ApiOperation(value = "计算账单优惠", notes = "计算账单优惠")
    @PostMapping("/dine_in_bill/calculate")
    DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO);

    /**
     * pad下单
     *
     * @param orderPlacementReqDTO pad下单请求实体
     * @return 订单guid
     */
    @ApiOperation("pad下单")
    @PostMapping(value = "/pad_order/order_placement")
    PadOrderPlacementRespDTO orderPlacement(@RequestBody PadOrderPlacementReqDTO orderPlacementReqDTO);

    /**
     * pad购物车价格计算
     *
     * @param orderPlacementReqDTO 购物车商品数据
     * @return 购物车总价
     */
    @ApiOperation("购物车价格计算")
    @PostMapping(value = "/pad_order/calculate_shop_car")
    PadPriceRespDTO calculateShopCar(@RequestBody PadOrderPlacementReqDTO orderPlacementReqDTO);

    /**
     * 查询门店下的下单表
     *
     * @param request 条件信息
     * @return 下单列表
     */
    @ApiOperation("查询门店下的下单表")
    @PostMapping(value = "/pad_order/list_pad_order")
    List<PadOrderRespDTO> listPadOrder(@RequestBody WxStoreMerchantOrderReqDTO request);

    /**
     * 根据guid查询pad下单信息
     *
     * @param guid 下单表guid
     * @return 下单信息
     */
    @ApiOperation("根据guid查询pad下单信息")
    @GetMapping(value = "/pad_order/get_pad_order_by_guid")
    PadOrderRespDTO getPadOrderByGuid(@RequestParam("guid") String guid);

    /**
     * 远程调用获取pad下单的加菜商品信息
     *
     * @param key 订单guid+下单表guid，以:分割
     * @return 加菜商品信息
     */
    @ApiOperation("远程调用获取pad下单的加菜商品信息")
    @GetMapping(value = "/pad_order/get_pad_order_add_item_info_by_redis")
    CreateDineInOrderReqDTO getPadOrderAddItemInfoByRedis(@RequestParam("key") String key);

    /**
     * 根据guid更新数据
     *
     * @param padOrderRespDTO padOrderRespDTO
     * @return Boolean
     */
    @ApiOperation("根据guid更新数据")
    @PostMapping(value = "/pad_order/update_by_id")
    Boolean updateById(@RequestBody PadOrderRespDTO padOrderRespDTO);

    /**
     * 根据pad下单guid查询下单商品
     *
     * @param padOrderGuid pad下单guid
     * @return 商品列表
     */
    @ApiOperation(value = "根据pad下单guid查询下单商品", notes = "根据pad下单guid查询下单商品")
    @GetMapping("/order_item/query_item_by_pad_guid")
    List<DineInItemDTO> queryItemByPadGuid(@RequestParam("padOrderGuid") String padOrderGuid);

    /**
     * 根据guid删除pad下单数据
     *
     * @param padOrderGuid pad下单guid
     * @return Boolean
     */
    @ApiOperation("根据guid删除pad下单数据")
    @GetMapping(value = "/pad_order/remove_by_guid")
    Boolean removeByGuid(@RequestParam("padOrderGuid") String padOrderGuid);

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/order_detail/find_by_order_guid")
    OrderDTO findByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    @ApiOperation(value = "获取多个订单详情", notes = "获取多个订单详情")
    @PostMapping("/order_detail/list_by_order_guid")
    List<OrderDTO> listByOrderGuid(@RequestBody SingleDataDTO query);

    /**
     * 根据并台主单guid查询pad下单信息列表
     *
     * @param combineOrderGuid 并台主单guid
     * @return 下单信息列表
     */
    @ApiOperation("根据guid查询pad下单信息")
    @GetMapping(value = "/pad_order/list_pad_order_by_combine_order_guid")
    List<PadOrderRespDTO> listPadOrderByCombineOrderGuid(@RequestParam("combineOrderGuid") String combineOrderGuid);

    @ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
    @PostMapping("/we_chat/get_order")
    OrderWechatDTO getOrder(@RequestBody String orderGuid);

    @ApiOperation("快餐下单")
    @PostMapping("/fast_food/add_item")
    EstimateItemRespDTO addItem(CreateFastFoodReqDTO createFastFoodReqDTO);

    @ApiOperation("小程序快餐下单")
    @PostMapping("/fast_food/create_applets_order")
    EstimateItemRespDTO createAppletsOrder(@RequestBody CreateFastFoodReqDTO createFastFoodReqDTO);

    @ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
    @PostMapping("/order_detail/find_by_wx_order_guid")
    OrderDetailPushMqDTO findByWxOrderGuid(@RequestBody OrderGuidsDTO orderGuidsDTO);

    @GetMapping("/groupon/list")
    List<GrouponListRespDTO> useGrouponList(@RequestParam("orderGuid") String orderGuid,
                                            @RequestParam(value = "grouponType", required = false) Integer grouponType);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<TradeClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TradeClientService create(Throwable throwable) {
            return new TradeClientService() {

                @Override
                public DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "calculate", JacksonUtils.writeValueAsString(billCalculateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PadOrderPlacementRespDTO orderPlacement(PadOrderPlacementReqDTO orderPlacementReqDTO) {
                    log.error(HYSTRIX_PATTERN, "orderPlacement", JacksonUtils.writeValueAsString(orderPlacementReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PadPriceRespDTO calculateShopCar(PadOrderPlacementReqDTO orderPlacementReqDTO) {
                    log.error(HYSTRIX_PATTERN, "calculateShopCar", JacksonUtils.writeValueAsString(orderPlacementReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PadOrderRespDTO> listPadOrder(WxStoreMerchantOrderReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "listPadOrder", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PadOrderRespDTO getPadOrderByGuid(String guid) {
                    log.error(HYSTRIX_PATTERN, "getPadOrderByGuid", guid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CreateDineInOrderReqDTO getPadOrderAddItemInfoByRedis(String key) {
                    log.error(HYSTRIX_PATTERN, "getPadOrderAddItemInfoByRedis", key, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean updateById(PadOrderRespDTO padOrderRespDTO) {
                    log.error(HYSTRIX_PATTERN, "updateById", JacksonUtils.writeValueAsString(padOrderRespDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<DineInItemDTO> queryItemByPadGuid(String padOrderGuid) {
                    log.error(HYSTRIX_PATTERN, "queryItemByPadGuid", padOrderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean removeByGuid(String padOrderGuid) {
                    log.error(HYSTRIX_PATTERN, "removeByGuid", padOrderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OrderDTO findByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "findByOrderGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OrderDTO> listByOrderGuid(SingleDataDTO query) {
                    log.error(HYSTRIX_PATTERN, "listByOrderGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PadOrderRespDTO> listPadOrderByCombineOrderGuid(String combineOrderGuid) {
                    log.error(HYSTRIX_PATTERN, "listPadOrderByCombineOrderGuid", combineOrderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OrderWechatDTO getOrder(String orderGuid) {
                    log.error("查询订单失败:{}", orderGuid);
                    return null;
                }

                @Override
                public EstimateItemRespDTO addItem(CreateFastFoodReqDTO createFastFoodReqDTO) {
                    log.error("快餐下单失败:{}", createFastFoodReqDTO, throwable);
                    return null;
                }

                @Override
                public EstimateItemRespDTO createAppletsOrder(CreateFastFoodReqDTO createFastFoodReqDTO) {
                    log.error("小程序快餐下单失败:{}", createFastFoodReqDTO, throwable);
                    return null;
                }

                @Override
                public OrderDetailPushMqDTO findByWxOrderGuid(OrderGuidsDTO orderGuidsDTO) {
                    log.error(HYSTRIX_PATTERN, "findByWxOrderGuid", JacksonUtils.writeValueAsString(orderGuidsDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<GrouponListRespDTO> useGrouponList(String orderGuid, Integer grouponType) {
                    log.error(HYSTRIX_PATTERN, "useGrouponList", orderGuid + "-" + grouponType,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
