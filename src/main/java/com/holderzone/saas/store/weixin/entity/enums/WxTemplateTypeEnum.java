package com.holderzone.saas.store.weixin.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxTemplateTypeEnum
 * @date 2019/08/16 15:22
 * @description 微信消息模板类型枚举
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum WxTemplateTypeEnum {
    QUEUE_TEMPLATE(0, "排队消息模板", "OPENTM207508364"),
    MEMBER_AMOUNT_TEMPLATE(1, "会员余额消费模板消息", "OPENTM408246679"),


    MEMBER_AMOUNT_CUSTOME_TEMPLATE(2, "会员消费模板消息", "OPENTM201642055"),

    MERCHANT_MEAL_NOTIFICATIONS(3, "商家出餐通知", "45945"),
    ;
    private Integer code;

    private String desc;

    private String shortId;


    public static String getShortIdByCode(Integer code) {

        return Arrays.stream(WxTemplateTypeEnum.values())
                .filter(wxTemplateTypeEnum -> Objects.equals(code, wxTemplateTypeEnum.getCode()))
                .findFirst().orElse(null).getShortId();
    }
}
