package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("hsw_weixin_order_record")
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class WxOrderRecordDO {

	private Long id;

	@TableId("guid")
	private String guid;

	private String orderGuid;

	/**
	 * 第一次下单批次号
	 */
	private String merchantGuid;

	private String userRecordGuid;

	private String brandGuid;

	private String brandName;

	private String logUrl;

	private String storeGuid;

	private String storeName;

	private String areaGuid;

	private String areaName;

	private String tableGuid;

	private String tableCode;

	/**
	 * 交易模式：交易模式(0：正餐，1：快餐)
	 */
	private Integer orderMode;

	/**微信订单状态:
	 * 0待确认，下单的初始状态
	 * 1已下单，已接单
	 * 2已支付，微信用户自己支付
	 * 3已取消，
	 * 4已退菜，
	 * 5待支付，快餐
	 * 6已完成,一体机结账
	 * 7没有开台且直接拒单
	 * @see  com.holderzone.saas.store.enums.weixin.WxOrderStateEnum
	 * **/
	private Integer orderState;

	private String orderStateName;

	/**
	 *实付金额
	 */
	private BigDecimal actuallyPayFee;

	/**
	 * 非会员小计
	 */
	private BigDecimal unMemberPrice;

	/**
	 * 结账人是否登录了会员
	 */
	private Boolean isLogin;

	/**
	 * 会员持卡id
	 */
	private String memberInfoCardGuid;

	/**
	 * 优惠券券码
	 */
	private String volumeCode;

	/**
	 * 商品名称列表
	 */
    private String itemName;

	/**
	 * 聚合支付支付订单号
	 */
	private String orderHolderNo;


	@TableField(fill = FieldFill.INSERT)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime gmtCreate;

	@TableField(fill = FieldFill.INSERT_UPDATE)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime gmtModified;

	@TableLogic
	private Integer isDel = 0;
}
