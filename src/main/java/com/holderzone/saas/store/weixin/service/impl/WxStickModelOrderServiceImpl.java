package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.business.manage.ShortMsgPollingRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStickOrderCallBackDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelOrderDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickShopCartRemoveDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickJHPayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickOrderRespDTO;
import com.holderzone.saas.store.weixin.service.WxStickModelOrderService;
import com.holderzone.saas.store.weixin.service.WxStickShopCartService;
import com.holderzone.saas.store.weixin.service.WxStoreTableStickService;
import com.holderzone.saas.store.weixin.service.rpc.WxStickModelClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStickOrderClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Optional;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickModelOrderServiceImpl
 * @date 2019/03/15 17:13
 * @description 微信桌贴模板购买ServiceImpl
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxStickModelOrderServiceImpl implements WxStickModelOrderService {

    private static final String SUCCESS_CODE = "10000";

    private static final String SUCCESS_STR = "done";

    private static final Map<String, WxStickOrderRespDTO> MAP = new ConcurrentHashMap<>();

    private static final Map<String, Boolean> FLAG_MAP = new ConcurrentHashMap<>();

    private final AtomicInteger mCount = new AtomicInteger(0);

    private final ScheduledExecutorService se = Executors.newScheduledThreadPool(3, r -> new Thread(r, "Stick_msg_polling-" + mCount.incrementAndGet()));

    @Autowired
    WxStickOrderClientService wxStickOrderClientService;

    @Autowired
    WxStickModelClientService wxStickModelClientService;

    @Autowired
    WxStickShopCartService wxStickShopCartService;

    @Autowired
    WxStoreTableStickService wxStoreTableStickService;
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    DynamicHelper dynamicHelper;

    /**
     * 下单接口
     *
     * @param wxStickModelOrderDTO 轮询返回支付结果
     * @return
     */
    @Override
    public WxStickOrderRespDTO order(WxStickModelOrderDTO wxStickModelOrderDTO) {
        Boolean isNotBought = wxStoreTableStickService.checkIsBought(wxStickModelOrderDTO.getTableStickGuidList());
        if (!isNotBought)
            throw new BusinessException("当前订单中存在已购买模板，请刷新页面后重试");
		if (StringUtils.isEmpty(wxStickModelOrderDTO.getStaffName())) {
			wxStickModelOrderDTO.setStaffName(UserContextUtils.getUserName());
		}
		if(StringUtils.isEmpty(wxStickModelOrderDTO.getStaffAccount())){
			wxStickModelOrderDTO.setStaffAccount(UserContextUtils.getUserAccount());
		}
		if (StringUtils.isEmpty(wxStickModelOrderDTO.getStaffGuid())) {
			wxStickModelOrderDTO.setStaffGuid(UserContextUtils.getUserGuid());
		}
        wxStickModelOrderDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        WxStickOrderRespDTO wxStickOrderRespDTO = new WxStickOrderRespDTO();
        wxStickOrderRespDTO.setGuidList(wxStickModelOrderDTO.getTableStickGuidList());
        log.info("桌贴购买入参:{}",wxStickModelOrderDTO);
        WxStickJHPayRespDTO wxStickJHPayRespDTO = wxStickOrderClientService.order(wxStickModelOrderDTO);// 调用云端下单接口
        if (!ObjectUtils.isEmpty(wxStickJHPayRespDTO)) {
            wxStickOrderRespDTO.setCode(wxStickJHPayRespDTO.getCode());
            wxStickOrderRespDTO.setMsg(wxStickJHPayRespDTO.getMsg());
            if (SUCCESS_CODE.equals(wxStickJHPayRespDTO.getCode())) {// 下单成功
                String attachData = wxStickJHPayRespDTO.getAttachData();
                if (SUCCESS_STR.equals(wxStickJHPayRespDTO.getResult())) {// 0元桌贴购买成功
                    wxStickOrderRespDTO.setPaySt("2");
                    wxStickOrderRespDTO.setBuyStatus(1);
                    handleModels(wxStickOrderRespDTO.getGuidList());
                    return wxStickOrderRespDTO;
                }
                if (StringUtils.isEmpty(attachData)) {
                    throw new BusinessException("桌贴购买异常，未获取到当前订单号， attachData:" + attachData);
                }
                String[] split = attachData.split(":");
                if (split.length >= 3) {
                    String paymentGuid = split[1];
                    String payGuid = split[2];
                    wxStickOrderRespDTO.setPayGuid(payGuid);
                    wxStickOrderRespDTO.setPaymentGuid(paymentGuid);
                    ShortMsgPollingRespDTO polling = wxStickOrderClientService.polling(wxStickOrderRespDTO);// 下单成功后手动调一次轮询接口
                    wxStickOrderRespDTO.setCodeUrl(polling.getCodeUrl());
                    wxStickOrderRespDTO.setPaySt(polling.getPaySt());
                    wxStickOrderRespDTO.setBuyStatus(0);
                    // 如果当前用户切换支付方式时，取消前一个轮询
                    MAP.put(payGuid, wxStickOrderRespDTO);
                    FLAG_MAP.put(payGuid, true);
                    if (!ObjectUtils.isEmpty(wxStickModelOrderDTO.getLastPayGuid())) {
                        FLAG_MAP.put(wxStickModelOrderDTO.getLastPayGuid(), false);
                    }
                }
                doPolling(wxStickOrderRespDTO, UserContextUtils.getEnterpriseGuid());
            }
        }
        log.info("微信桌贴模板购买下单结果：code={}, msg={}，wxStickOrderRespDTO={}"
                , wxStickJHPayRespDTO.getCode(), wxStickJHPayRespDTO.getMsg(), JacksonUtils.writeValueAsString(wxStickOrderRespDTO));
        return wxStickOrderRespDTO;
    }

    /**
     * 轮询接口
     */
    @Override
    public WxStickOrderRespDTO polling(WxStickOrderRespDTO wxStickOrderRespDTO) {
        return Optional.ofNullable(MAP.get(wxStickOrderRespDTO.getPayGuid()))
                .orElseGet(() -> {
                    ShortMsgPollingRespDTO polling = wxStickOrderClientService.polling(wxStickOrderRespDTO);
                    if (polling == null) {
                        return null;
                    }
                    WxStickOrderRespDTO respDTO = new WxStickOrderRespDTO();
                    BeanUtils.copyProperties(polling, respDTO);
                    return respDTO;
                });
    }

    /**
     * 主动轮询订单结果
     */
    private void doPolling(WxStickOrderRespDTO wxStickOrderRespDTO, String enterpriseGuid) {
        AtomicInteger atomicInteger = new AtomicInteger(0);
        Map<String, Future<?>> map = new ConcurrentHashMap<>(10);

        ScheduledFuture<?> scheduledFuture = se.scheduleWithFixedDelay(() -> {
            try {
                if (shouldStopPolling(wxStickOrderRespDTO, atomicInteger, map)) return;

                ShortMsgPollingRespDTO polling = wxStickOrderClientService.polling(wxStickOrderRespDTO);
                log.info("轮询结果 polling={}", JacksonUtils.writeValueAsString(polling));

                if (polling != null && SUCCESS_CODE.equals(polling.getCode())) {
                    updateOrderStatus(wxStickOrderRespDTO, polling, enterpriseGuid, map);
                }
            } catch (Exception e) {
                log.error("桌贴购买主动轮询订单结果时出现异常，取消轮询，e:", e);
                MAP.remove(wxStickOrderRespDTO.getPayGuid());
                map.get("id").cancel(true);
            }
        }, 1, 1, TimeUnit.SECONDS);

        map.put("id", scheduledFuture);
    }

    private boolean shouldStopPolling(WxStickOrderRespDTO wxStickOrderRespDTO, AtomicInteger atomicInteger, Map<String, Future<?>> map) {
        int count = atomicInteger.incrementAndGet();
        if (count == 50) {
            log.info("当前订单：{}，轮询已达到次数上限，等待回调", wxStickOrderRespDTO.getPayGuid());
            map.get("id").cancel(true);
            return true;
        }
        if (Boolean.FALSE.equals(FLAG_MAP.get(wxStickOrderRespDTO.getPayGuid()))) {
            log.info("当前订单：{}，已切换支付方式或取消支付，停止轮询", wxStickOrderRespDTO.getPayGuid());
            MAP.remove(wxStickOrderRespDTO.getPayGuid());
            FLAG_MAP.remove(wxStickOrderRespDTO.getPayGuid());
            map.get("id").cancel(true);
            return true;
        }
        return false;
    }

    private void updateOrderStatus(WxStickOrderRespDTO wxStickOrderRespDTO, ShortMsgPollingRespDTO polling, String enterpriseGuid, Map<String, Future<?>> map) {
        wxStickOrderRespDTO.setCodeUrl(polling.getCodeUrl());
        wxStickOrderRespDTO.setPaySt(polling.getPaySt());

        if (Arrays.asList("2", "3", "5").contains(polling.getPaySt())) {
            log.info("取消轮询 pollingSt={}", polling.getPaySt());
            if ("2".equals(polling.getPaySt())) {
                wxStickOrderRespDTO.setBuyStatus(1);
                dynamicHelper.changeDatasource(enterpriseGuid);
                handleModels(wxStickOrderRespDTO.getGuidList());
            } else {
                wxStickOrderRespDTO.setBuyStatus(2);
            }
            MAP.put(wxStickOrderRespDTO.getPayGuid(), wxStickOrderRespDTO);
            FLAG_MAP.remove(wxStickOrderRespDTO.getPayGuid());
            map.get("id").cancel(true);
        }
    }

    /**
     * 回掉处理
     */
    @Override
    public String callBack(WxStickOrderCallBackDTO wxStickOrderCallBackDTO) {
        String payGuid = wxStickOrderCallBackDTO.getPayGuid();
        String paySt = wxStickOrderCallBackDTO.getPaySt();

        if ("2".equals(paySt)) {
            handleSuccessfulCallback(wxStickOrderCallBackDTO);
        }

        WxStickOrderRespDTO wxStickOrderRespDTO = new WxStickOrderRespDTO();
        wxStickOrderRespDTO.setPaySt(paySt);
        MAP.put(payGuid, wxStickOrderRespDTO);
        return "SUCCESS";
    }

    private void handleSuccessfulCallback(WxStickOrderCallBackDTO wxStickOrderCallBackDTO) {
        EnterpriseIdentifier.setEnterpriseGuid(wxStickOrderCallBackDTO.getEnterpriseGuid());
        UserContextUtils.putErp(wxStickOrderCallBackDTO.getEnterpriseGuid());

        wxStickShopCartService.removeModels(new WxStickShopCartRemoveDTO(wxStickOrderCallBackDTO.getModelGuidList(), 0));
        handleModels(wxStickOrderCallBackDTO.getModelGuidList());
        EnterpriseIdentifier.remove();
    }

    /**
     * 拉取模板信息并保存
     */
    private void handleModels(List<String> modelGuidList) {
        WxStickModelReqDTO wxStickModelReqDTO = new WxStickModelReqDTO();
        wxStickModelReqDTO.setList(modelGuidList);
        List<WxTableStickDTO> wxTableStickDTOList = wxStickModelClientService.getTableStickList(wxStickModelReqDTO);
        wxStoreTableStickService.saveOrUpdateModels(wxTableStickDTOList);
        wxStickShopCartService.removeModels(new WxStickShopCartRemoveDTO(modelGuidList, 0));
    }
}
