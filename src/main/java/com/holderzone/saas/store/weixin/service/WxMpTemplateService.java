package com.holderzone.saas.store.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.weixin.open.WxMpTemplateDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxMpTemplateDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMpTemplateService
 * @date 2019/08/16 15:02
 * @description
 * @program holder-saas-store
 */
public interface WxMpTemplateService extends IService<WxMpTemplateDO> {
    String createMsgTemplate(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO, Integer type);

    /**
     * 手动添加模版
     */
    void addMsgTemplate(WxMpTemplateDTO wxMpTemplateDTO);

}
