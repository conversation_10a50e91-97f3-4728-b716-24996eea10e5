package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.WxPrepayRespDTO;
import com.holderzone.saas.store.dto.weixin.WxZeroPayReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.WeChatH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.weixin.entity.dto.WxPayCallbackDTO;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStorePayservice
 * @date 2019/4/3
 */
public interface WxStorePayService {

    /**
     * @param wxH5PayReqDTO
     * @return
     * @describle 支付下单接口，返回
     */
    WxPayRespDTO weChatPublic(WxH5PayReqDTO wxH5PayReqDTO) throws UnsupportedEncodingException;

    /**
     * @param wxStoreCallbackNotifyDTO
     * @describle 微信回调结果处理
     */
    String wxResultOperation(WxPayCallbackDTO wxStoreCallbackNotifyDTO);

    /**
     * 会员支付
     *
     * @param wxMemberPayDTO mem
     * @return result
     */
    WxStorePayResultDTO memberPay(WxMemberPayDTO wxMemberPayDTO) throws Exception;

    WxPayWayRespDTO getAllPayWay(WxStorePayReqDTO wxStorePayReqDTO);

    WxPrepayRespDTO prepay(WxPrepayReqDTO wxPrepayReqDTO);

    WxPrepayRespDTO validateOrder(WxStorePayReqDTO wxStorePayReqDTO);

    WxPrepayConfirmRespDTO memberConfirm(WxPrepayConfirmReqDTO wxPrepayConfirmReqDTO);

    /**
     * @param wxZeroPayReqDTO 支付入参
     * @return 0元支付
     */
    WxStorePayResultDTO zeroPay(WxZeroPayReqDTO wxZeroPayReqDTO);

    /**
     * 微信公众号支付/微信小程序支付/支付宝小程序支付
     *
     * @param weChatH5PayReqDTO 支付入参
     * @return 支付回调对象
     */
    WxPayRespDTO weChatPay(WeChatH5PayReqDTO weChatH5PayReqDTO);
}
