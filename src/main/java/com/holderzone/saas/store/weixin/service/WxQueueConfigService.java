package com.holderzone.saas.store.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxQueueConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxQueueConfigDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxQueueConfigDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueConfigService
 * @date 2019/05/09 17:26
 * @description 门店微信线上排队配置service
 * @program holder-saas-store
 */
public interface WxQueueConfigService extends IService<WxQueueConfigDO> {
    /**
     * 分页查询排队配置
     *
     * @param wxStorePageReqDTO
     * @return
     */
    Page<WxQueueConfigDTO> pageQueueConfig(WxStorePageReqDTO wxStorePageReqDTO);

    /**
     * 修改排队配置（单门店）
     *
     * @param wxQueueConfigDTO
     * @return
     */
    Boolean updateQueueConfig(WxQueueConfigDTO wxQueueConfigDTO);

    /**
     * 批量修改排队配置
     *
     * @param wxQueueConfigUpdateBatchReqDTO
     * @return
     */
    Boolean updateQueueConfigBatch(WxQueueConfigUpdateBatchReqDTO wxQueueConfigUpdateBatchReqDTO);

    /**
     * 获取排队配置详情
     *
     * @param wxQueueConfigDTO
     * @return
     */
    WxQueueConfigDTO getQueueConfig(WxQueueConfigDTO wxQueueConfigDTO);


	WxQueueConfigDO getOne(String storeGuid);
}
