package com.holderzone.saas.store.weixin.service.impl;

import com.alibaba.fastjson.JSON;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardAll;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListUnowned;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberCenterCardRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxMemberCenterCardService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WxMemberCenterCardServiceImpl implements WxMemberCenterCardService {

    private final MemberClientService memberClientService;

    private final RedisTemplate redisTemplate;

    private final WxStoreSessionDetailsService wxStoreSessionDetailsService;

    private final HsaBaseClientService hsaBaseClientService;

    @Autowired
    public WxMemberCenterCardServiceImpl(MemberClientService memberClientService,
                                         RedisTemplate redisTemplate,
                                         WxStoreSessionDetailsService wxStoreSessionDetailsService,
                                         HsaBaseClientService hsaBaseClientService) {
        this.memberClientService = memberClientService;
        this.redisTemplate = redisTemplate;
        this.wxStoreSessionDetailsService = wxStoreSessionDetailsService;
        this.hsaBaseClientService = hsaBaseClientService;
    }

    @Override
    public WxMemberCenterCardRespDTO cardList(WxStoreConsumerDTO wxStoreConsumerDTO) {
        log.info("Processing card list for consumer: {}", JSON.toJSON(wxStoreConsumerDTO));
        boolean isLogin = Optional.ofNullable(wxStoreConsumerDTO.getIsLogin()).orElse(false);
        return isLogin ? loginCard(wxStoreConsumerDTO) : logoutCard(wxStoreConsumerDTO);
    }

    private WxMemberCenterCardRespDTO logoutCard(WxStoreConsumerDTO wxStoreConsumerDTO) {
        try {
            return Optional.ofNullable(hsaBaseClientService.getDefaultCard(wxStoreConsumerDTO.getMemberInfoGuid()).getData())
                    .map(defaultCard -> {
                        log.info("Default card found: {}", defaultCard);
                        return buildDefaultCardResponse(wxStoreConsumerDTO, defaultCard);
                    })
                    .orElseGet(() -> buildEmptyCardResponse(false));
        } catch (Exception e) {
            log.error("Error retrieving default card", e);
            return buildEmptyCardResponse(false);
        }
    }

    private WxMemberCenterCardRespDTO loginCard(WxStoreConsumerDTO wxStoreConsumerDTO) {
        try {
            return Optional.ofNullable(wxStoreSessionDetailsService.getMemberCard(wxStoreConsumerDTO.getEnterpriseGuid(), wxStoreConsumerDTO.getBrandGuid(), wxStoreConsumerDTO.getOpenId()))
                    .map(memberCard -> buildLoginCardResponse(wxStoreConsumerDTO, memberCard))
                    .orElseGet(() -> buildEmptyCardResponse(false));
        } catch (Exception e) {
            log.error("Error retrieving login card for consumer: {}", wxStoreConsumerDTO, e);
            return buildEmptyCardResponse(false);
        }
    }

    private WxMemberCenterCardRespDTO buildDefaultCardResponse(WxStoreConsumerDTO wxStoreConsumerDTO, ResponseMemberCardListOwned defaultCard) {
        defaultCard.setCardLevelName("");
        defaultCard.setCardLevelNum(-1);
        defaultCard.setCardLogo(wxStoreSessionDetailsService.getBrandDetail(wxStoreConsumerDTO.getBrandGuid()).getLogoUrl());
        defaultCard.setCardIcon(Optional.ofNullable(defaultCard.getCardIcon()).orElse(""));
        return WxMemberCenterCardRespDTO.builder().memberCardList(Collections.singletonList(defaultCard)).ownCard(false).build();
    }

    private WxMemberCenterCardRespDTO buildLoginCardResponse(WxStoreConsumerDTO wxStoreConsumerDTO, ResponseMemberCardAll memberCard) {
        List<ResponseMemberCardListOwned> memberCardList = memberCard.getOpenedCardList();
        List<ResponseMemberCardListUnowned> cardList = memberCard.getNonactivatedCardList();
        boolean ownCard = !ObjectUtils.isEmpty(cardList) && cardList.stream().anyMatch(x -> !StringUtils.isEmpty(x.getCardName()) || !StringUtils.isEmpty(x.getCardGuid()));

        if (ObjectUtils.isEmpty(memberCardList) || memberCardList.stream().allMatch(x -> StringUtils.isEmpty(x.getCardName()) && StringUtils.isEmpty(x.getCardGuid()))) {
            return buildEmptyCardResponse(ownCard);
        }

        BrandDTO brandDTO = wxStoreSessionDetailsService.getBrandDetail(wxStoreConsumerDTO.getBrandGuid());
        if (!ObjectUtils.isEmpty(brandDTO) && !StringUtils.isEmpty(brandDTO.getLogoUrl())) {
            memberCardList = memberCardList.stream().peek(x -> x.setCardLogo(brandDTO.getLogoUrl())).collect(Collectors.toList());
        }
        memberCardList.forEach(x -> x.setCardIcon(Optional.ofNullable(x.getCardIcon()).orElse("")));
        return WxMemberCenterCardRespDTO.builder().memberCardList(memberCardList).ownCard(ownCard).build();
    }

    private WxMemberCenterCardRespDTO buildEmptyCardResponse(boolean ownCard) {
        return WxMemberCenterCardRespDTO.builder().memberCardList(Collections.emptyList()).ownCard(ownCard).build();
    }
}
