package com.holderzone.saas.store.weixin.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WebSocketMessageDTO;
import com.holderzone.saas.store.dto.weixin.WxSocketDistributionDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreOrderPushNotifyDTO;
import com.holderzone.saas.store.enums.weixin.WebSocketMessageEnum;
import com.holderzone.saas.store.enums.weixin.WxDistributionEnum;
import com.holderzone.saas.store.weixin.service.WxSocketMsgService;
import com.holderzone.saas.store.weixin.service.WxStoreMenuDetailsService;
import com.holderzone.saas.store.weixin.service.WxStoreSocketSessionService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreSocketSessionServiceImpl
 * @date 2019/3/22
 */
@Service
@Slf4j
public class WxStoreSocketSessionServiceImpl implements WxStoreSocketSessionService {


    private final RedisUtils redisUtils;

    private final WxStoreMenuDetailsService wxStoreMenuDetailsService;

    private final WxSocketMsgService wxSocketMsgService;

    @Autowired
    public WxStoreSocketSessionServiceImpl(RedisUtils redisUtils, WxStoreMenuDetailsService wxStoreMenuDetailsService, WxSocketMsgService wxSocketMsgService) {
        this.redisUtils = redisUtils;
		this.wxStoreMenuDetailsService = wxStoreMenuDetailsService;
		this.wxSocketMsgService = wxSocketMsgService;
	}

    @Override
    public void createSession(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        dynamicMethod(wxStoreAdvanceConsumerReqDTO);
        String redisKey = combineKey(wxStoreAdvanceConsumerReqDTO);
        
        // 判断订单类型
        if (Boolean.TRUE.equals(wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO))) {
            WxStoreAdvanceConsumerReqDTO existingConsumer = (WxStoreAdvanceConsumerReqDTO)
					redisUtils.hGet(redisKey, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
            
            // 如果用户不存在，进行推送
            if (ObjectUtils.isEmpty(existingConsumer)) {
                WebSocketMessageDTO<WxStoreOrderPushNotifyDTO> orderPush = WebSocketMessageDTO.<WxStoreOrderPushNotifyDTO>builder()
                        .type(WebSocketMessageEnum.ORDER_PUSH.getCode())
                        .content(new WxStoreOrderPushNotifyDTO(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getNickName(),
								wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getHeadImgUrl()))
                        .build();
                
                // 新建用户桌台推送
                wxSocketMsgService.distribute(WxSocketDistributionDTO.builder()
                        .distribution(WxDistributionEnum.TABLE_EXCEPT_CURRENT.getCode())
                        .tableGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid())
                        .openId(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId())
                        .content(JacksonUtils.writeValueAsString(orderPush))
                        .build());
                log.info("点餐推送用户:{}", wxStoreAdvanceConsumerReqDTO);
            }
            redisUtils.hPut(redisKey, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId(), wxStoreAdvanceConsumerReqDTO);
        } else {
            redisUtils.set(redisKey, wxStoreAdvanceConsumerReqDTO);
        }
    }

    @Override
    public List<WxStoreAdvanceConsumerReqDTO> getSession(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        dynamicMethod(wxStoreAdvanceConsumerReqDTO);
        String redisKey = combineKey(wxStoreAdvanceConsumerReqDTO);
        List<WxStoreAdvanceConsumerReqDTO> list = new ArrayList<>();
        
        // 判断订单类型
        if (Boolean.TRUE.equals(wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO))) {
            List<WxStoreAdvanceConsumerReqDTO> consumerReqDTOS = redisUtils.hValues(redisKey);
            if (!ObjectUtils.isEmpty(consumerReqDTOS)) {
                list = consumerReqDTOS;
            }
        } else {
            WxStoreAdvanceConsumerReqDTO consumerReqDTO = (WxStoreAdvanceConsumerReqDTO) redisUtils.get(redisKey);
            log.info("快餐订单详情用户：{}", consumerReqDTO);
            if (!ObjectUtils.isEmpty(consumerReqDTO)) {
                list.add(consumerReqDTO);
            }
        }
        log.info("当前桌台所有会话用户：{}", list);
        return list;
    }

    @Override
	public List<WxStoreAdvanceConsumerReqDTO> getAllConsumer(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {

		return null;
	}

    @Override
    public void delSession(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		dynamicMethod(wxStoreAdvanceConsumerReqDTO);

		String redisKey = combineKey(wxStoreAdvanceConsumerReqDTO);
		redisUtils.delete(redisKey);
    }


    @Override
	public void delTableSession(String tableGuid) {
		String redisKey = combineKey(tableGuid);
		redisUtils.delete(redisKey);
	}

	@Override
	public WxStoreAdvanceConsumerReqDTO getSingleSession(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		dynamicMethod(wxStoreAdvanceConsumerReqDTO);
		String key = combineKey(wxStoreAdvanceConsumerReqDTO);
		WxStoreAdvanceConsumerReqDTO consumerReqDTO;
		
		// 判断订单类型
		if (wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO)) {
			consumerReqDTO = (WxStoreAdvanceConsumerReqDTO) redisUtils.hGet(key, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
		} else {
			consumerReqDTO = (WxStoreAdvanceConsumerReqDTO) redisUtils.get(key);
		}
		log.info("获取单个会话用户信息:{}", consumerReqDTO);
		return consumerReqDTO;
	}

	@Override
	public List<WxStoreAdvanceConsumerReqDTO> getSession(String tableGuid) {
		return redisUtils.hValues(combineKey(tableGuid));
	}

	@Override
	public WxStoreAdvanceConsumerReqDTO getSingleSession(String openId) {
		return (WxStoreAdvanceConsumerReqDTO) redisUtils.get(combineKey(openId));
	}


	private void dynamicMethod(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		EnterpriseIdentifier.setEnterpriseGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getEnterpriseGuid());
	}


	private String combineKey(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		if (wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO)) {
			return combineKey(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
		}else{
			return combineKey(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
		}
	}

	private String combineKey(String key) {
		return BusinessName.SOCKET_USER + ":" + key;
	}
}
