package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreShoppingCartDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreShoppingCartMapStruct
 * @date 2019/3/4
 */
@Mapper(componentModel = "spring")
@Component
public interface WxStoreShoppingCartMapStruct {
    WxStoreShoppingCartDTO wxStoreConsumerDTO2WxStoreShoppingCartDTO(WxStoreConsumerDTO wxStoreConsumerDTO);
}
