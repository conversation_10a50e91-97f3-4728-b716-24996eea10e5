package com.holderzone.saas.store.weixin.controller;


import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxUserRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxUpdateUserLoginController
 * @date 2019/09/11 15:36
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/wx_open")
@Api(value = "修改微信登录状态")
@Slf4j
public class WxUpdateUserLoginController {

    @Autowired
    WxUserRecordService wxUserRecordService;
    @Autowired
    RedisUtils redisUtils;

    @PostMapping("/update_login")
    @ApiOperation(value = "修改登录状态")
    public Boolean updateUserLogin(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO) {


       boolean succ = wxUserRecordService.updateUserLogin(wxStoreConsumerDTO);
       if(succ){
           WxMemberSessionDTO memberByOpenId = WxMemberSessionUtil.getMemberByOpenId(redisUtils, wxStoreConsumerDTO.getOpenId());
           if(memberByOpenId!=null){
               memberByOpenId.getWxUserInfoDTO().setIsLogin(wxStoreConsumerDTO.getIsLogin());
               WxMemberSessionUtil.initMemberSessionWithOpenId(redisUtils,memberByOpenId);
           }
       }
       return succ;
    }
}