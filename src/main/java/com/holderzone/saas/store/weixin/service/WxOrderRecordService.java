package com.holderzone.saas.store.weixin.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.deal.PayBackOrderRecordDTO;
import com.holderzone.saas.store.dto.weixin.deal.UnMemberMessageDTO;
import com.holderzone.saas.store.dto.weixin.deal.WxStoreUserOrderItemDTO;
import com.holderzone.saas.store.dto.weixin.req.WxBrandUserOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxUpdateOrderRecordStateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandUserOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreUserOrderDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.query.WxOrderRecordQuery;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxOrderRecordService
 * @date 2019/4/3
 */
public interface WxOrderRecordService extends IService<WxOrderRecordDO> {
	/**
	 * @param wxStoreAccountDTO
	 * @describle 更新用户订单记录
	 */
	boolean update(WxStoreAccountDTO wxStoreAccountDTO);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @describle 更新桌台所有用户订单记录
	 */
	void updateTable(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * 查询用户订单记录
	 *
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @return
	 */
	List<WxOrderRecordDO> getWxOrderRecordByCondition(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @describle 删除个人订单记录
	 */
	void delWxOrderRecord(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	WxOrderRecordDTO getOrderRecord(String orderGuid);

	/**
	 * @param wxStoreAdvanceConsumerReqDTO
	 * @describle 查询并封装我的订单记录
	 */
	WxStoreUserOrderDTO getWxStoreUserOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);


	WxOrderRecordDTO getOne(WxOrderRecordQuery query);

	WxBrandUserOrderDTO getWxBrandUserOrder(WxBrandUserOrderReqDTO wxBrandUserOrderReqDTO);

	void cancelFastOrder(String orderGuid);

	boolean cancelFastOrder(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, WxStoreConsumerDTO wxStoreConsumerDTO);

	Boolean replaceOrderID(Map<String, String> map);

	List<WxOrderRecordDO> getOldOrderList(Set<String> orderGuidList);


	Boolean batchSave(List<WxOrderRecordDO> wxOrderRecordDOS);

	List<WxOrderRecordDO> getOutStandingOrders(String tableGuid);

	/**
	 * @return 用户门店订单：重构
	 */
	List<WxStoreUserOrderItemDTO> userStoreList();

	/**
	 * 查询我的或者桌台没有完结的订单
	 */
	List<WxStoreUserOrderItemDTO> getUnFinishStoreUserOrderList();

	String getMerchantOrderGuid(String orderRecordGuid);

	/**
	 * @param orderGuid 订单id
	 * @return 根据商户订单id查微信订单
	 */
	WxOrderRecordDO getByOrderGuid(String orderGuid);

	IPage<WxStoreUserOrderItemDTO> userStorePage(Integer pageNo, Integer pageSize);

	/**
	 * @param guid 订单id
	 * @return 快餐超时取消
	 */
	Boolean dealFastOrderTimeOut(String guid);

	void initialRecord(WxQrCodeInfoDO wxQrCodeInfoDO);

	String initialRecord(WxMemberSessionDTO wxMemberSessionDTO);


	/**
	 * @param payBackOrderRecordDTO 会员支付回调更新订单
	 */
	void payBackOrder(PayBackOrderRecordDTO payBackOrderRecordDTO);

	void sendUnMemberMessage(DineinOrderDetailRespDTO orderDetails, WxOrderRecordDO wxOrderRecordDO, String openId);

	void sendUnMemberMessage(UnMemberMessageDTO unMemberMessageDTO);

	WxStoreMerchantOrderDTO getMerchantOrderPhone(String orderGuid);

	/**
	 * 根据聚合支付平台支付订单号查询订单
	 */
	WxOrderRecordDO getByOrderHolderNo(String orderHolderNo);

    /**
     * 快餐订单查微信订单
     *
     * @param query 订单号列表
     * @return 微信订单
     */
	List<WxStoreMerchantOrderDTO> listByOrderGuid(SingleDataDTO query);

	/**
	 * 修改微信订单状态
	 */
	void updateOrderRecordState(WxUpdateOrderRecordStateReqDTO reqDTO);

}
