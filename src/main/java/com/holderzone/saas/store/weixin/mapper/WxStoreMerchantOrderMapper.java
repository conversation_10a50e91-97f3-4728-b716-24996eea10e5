package com.holderzone.saas.store.weixin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMerchantOrder
 * @date 2019/4/9
 */
@Repository
public interface WxStoreMerchantOrderMapper extends BaseMapper<WxStoreMerchantOrderDO> {


    List<WxStoreMerchantOrderDO> selectByOrderRecordGuid(@Param("orderRecordGuid") String orderRecordGuid
            ,@Param("tableGuids") Collection<String> tableGuids);

    int countOrder(@Param("orderRecordGuid") String orderRecordGuid,@Param("tableGuids") Collection<String> tableGuids);

    List<WxStoreMerchantOrderDO> queryWeChatOrderList(@Param("req") WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO
            , @Param("storeGuid")String storeGuid, @Param("gmtCreate") LocalDateTime gmtCreate);
    List<WxStoreMerchantOrderDO> queryWeChatOrderListOptimized(@Param("req") WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO
            , @Param("storeGuid")String storeGuid, @Param("gmtCreate") LocalDateTime gmtCreate);
}
