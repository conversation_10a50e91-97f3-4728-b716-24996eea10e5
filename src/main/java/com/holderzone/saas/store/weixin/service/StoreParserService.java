package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.organization.StoreParserBaseDTO;
import com.holderzone.saas.store.dto.organization.StoreParserBasePageDTO;
import com.holderzone.saas.store.dto.organization.StoreParserDTO;
import com.holderzone.saas.store.dto.organization.StoreParserPageDTO;

public interface StoreParserService {

    void parseByCondition(StoreParserDTO storeParserDTO);

    void parseByCondition(StoreParserBaseDTO storeParserBaseDTO);

    void parseByCondition(StoreParserPageDTO storeParserPageDTO);

    void parseByCondition(StoreParserBasePageDTO storeParserBasePageDTO);
}
