package com.holderzone.saas.store.weixin.service.impl;


import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Service;

import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.WxStoreMerchantDineInItemDTO;
import com.holderzone.saas.store.weixin.service.WxStoreMerchantDineInItemService;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMerchantDineInItemServiceImpl
 * @date 2019/4/15
 */
@Service
public class WxStoreMerchantDineInItemServiceImpl implements WxStoreMerchantDineInItemService {

	private final RedisUtils redisUtils;

	public WxStoreMerchantDineInItemServiceImpl(RedisUtils redisUtils) {
		this.redisUtils = redisUtils;
	}

	@Override
	public void update(WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO) {
		redisUtils.setEx(BusinessName.WX_MERCHANT_ITEM+":"+wxStoreMerchantDineInItemDTO.getGuid(),wxStoreMerchantDineInItemDTO,24, TimeUnit.HOURS);
	}

	@Override
	public Boolean del(String guid) {
		return redisUtils.delete(BusinessName.WX_MERCHANT_ITEM+":"+guid);
	}

	@Override
	public WxStoreMerchantDineInItemDTO getWxStoreMerchantDineInItem(String guid) {
		return (WxStoreMerchantDineInItemDTO) redisUtils.get(BusinessName.WX_MERCHANT_ITEM+":"+guid);
	}
}
