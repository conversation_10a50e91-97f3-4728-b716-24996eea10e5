package com.holderzone.saas.store.weixin.config;

import com.holderzone.saas.store.weixin.service.rpc.WxComponentClientService;
import me.chanjar.weixin.open.api.WxOpenComponentService;
import me.chanjar.weixin.open.api.WxOpenConfigStorage;
import me.chanjar.weixin.open.api.impl.WxOpenComponentServiceImpl;
import me.chanjar.weixin.open.api.impl.WxOpenInMemoryConfigStorage;
import me.chanjar.weixin.open.api.impl.WxOpenServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxThirdOpenConfig
 * @date 2019/02/23 14:58
 * @description 微信第三方开放平台配置
 * @program holder-saas-store-weixin
 */
@Component
@EnableConfigurationProperties({WxOpenAccountConfig.class})
public class WxThirdOpenConfig extends WxOpenServiceImpl {

    @Autowired
    WxOpenAccountConfig wxOpenAccountConfig;

    @Autowired
    WxComponentClientService wxComponentClientService;

    @PostConstruct
    public void init() {
        WxOpenConfigStorage wxOpenConfigStorage = new WxOpenInMemoryConfigStorage();
        wxOpenConfigStorage.setComponentAppId(wxOpenAccountConfig.getComponentAppId());
        wxOpenConfigStorage.setComponentAesKey(wxOpenAccountConfig.getComponentAesKey());
        wxOpenConfigStorage.setComponentToken(wxOpenAccountConfig.getComponentToken());
        wxOpenConfigStorage.setComponentAppSecret(wxOpenAccountConfig.getComponentSecret());
        setWxOpenConfigStorage(wxOpenConfigStorage);
    }

    @Bean
    @Override
    public synchronized WxOpenComponentService getWxOpenComponentService() {
    	WxOpenComponentService   wxOpenComponentService = new WxOpenComponentServiceImpl(this);
        return wxOpenComponentService;
    }

}
