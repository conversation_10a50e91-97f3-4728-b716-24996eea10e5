package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.req.WxStoreStatusUpdateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreStatusRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxConfigOverviewDO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxConfigOverviewMapstruct
 * @date 2019/05/09 15:33
 * @description 门店微信功能配置概览mapstruct
 * @program holder-saas-store
 */
@Mapper(componentModel = "spring")
@Component
public interface WxConfigOverviewMapstruct {

    WxStoreStatusRespDTO wxConfigOverViewDO2DTO(WxConfigOverviewDO wxConfigOverviewDO);

    List<WxStoreStatusRespDTO> doList2DTOList(List<WxConfigOverviewDO> wxConfigOverviewDOList);

    WxConfigOverviewDO wxConfigOverviewDTO2DO(WxStoreStatusUpdateReqDTO wxStoreStatusUpdateReqDTO);
}
