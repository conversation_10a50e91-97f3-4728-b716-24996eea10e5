package com.holderzone.saas.store.weixin.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.helper.WebsocketMessageHelper;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTableStatusChangeServiceImpl
 * @date 2019/6/3
 */
@Service
@Slf4j
public class WxStoreTableStatusChangeServiceImpl implements WxStoreTableStatusChangeService {

    private final WxStoreMerchantOrderService wxStoreMerchantOrderService;
    private final DynamicHelper dynamicHelper;
    private final RedisUtils redisUtils;
    private final WxOrderRecordService wxOrderRecordService;
    private final WxUserRecordService wxUserRecordService;
    private final WebsocketMessageHelper websocketMessageHelper;

    public static final String CACHE_KEY = ":hash:";


    @Autowired
    public WxStoreTableStatusChangeServiceImpl(WxStoreMerchantOrderService wxStoreMerchantOrderService,
                                               DynamicHelper dynamicHelper, RedisUtils redisUtils, WxOrderRecordService wxOrderRecordService,
                                               WxUserRecordService wxUserRecordService, WebsocketMessageHelper websocketMessageHelper) {
        this.wxStoreMerchantOrderService = wxStoreMerchantOrderService;
        this.dynamicHelper = dynamicHelper;
        this.redisUtils = redisUtils;
        this.wxOrderRecordService = wxOrderRecordService;
        this.wxUserRecordService = wxUserRecordService;
        this.websocketMessageHelper = websocketMessageHelper;
    }

    @Override
    public boolean combine(TableCombineDTO tableCombineDTO) {
        log.info("监听桌台并台:{}", tableCombineDTO);

        if (tableCombineDTO == null) {
            return false;
        }

        dynamicHelper.changeDatasource(tableCombineDTO.getEnterpriseGuid());
        List<String> tableGuidList = tableCombineDTO.getTableGuidList();
        if (CollectionUtils.isEmpty(tableGuidList)) {
            return false;
        }

        for (String tableGuid : tableGuidList) {
            if (StringUtils.isEmpty(tableGuid)) {
                continue;
            }
            Set<String> openIDS = redisUtils.hKeyList(CacheName.DINE_USER + CACHE_KEY + tableGuid);
            log.info("并台：{}桌台用户:{}", tableGuid, openIDS);
            if (!CollectionUtils.isEmpty(openIDS)) {
                websocketMessageHelper.sendOrderEmqMessage(tableGuid, openIDS);
            }
        }
        return true;
    }

    @Override
    public boolean separate(TableOrderCombineDTO tableOrderCombineDTO) {
        log.info("监听桌台拆台:{}", tableOrderCombineDTO);

        dynamicHelper.changeDatasource(tableOrderCombineDTO.getEnterpriseGuid());
        List<TableInfoDTO> tableInfoDTOS = tableOrderCombineDTO.getTableInfoDTOS();
        if (CollectionUtils.isEmpty(tableInfoDTOS)) {
            return false;
        }

        for (TableInfoDTO tableInfoDTO : tableInfoDTOS) {
            String tableGuid = tableInfoDTO.getTableGuid();
            if (StringUtils.isEmpty(tableGuid)) {
                continue;
            }
            Set<String> openIDS = redisUtils.hKeyList(CacheName.DINE_USER + CACHE_KEY + tableGuid);
            log.info("拆台：{}桌台用户:{}", tableGuid, openIDS);
            if (!CollectionUtils.isEmpty(openIDS)) {
                websocketMessageHelper.sendOrderEmqMessage(tableGuid, openIDS);
            }
        }
        return true;
    }

    @Override
    public boolean turn(TurnTableDTO turnTableDTO) {
        log.info("监听桌台转台:{}", turnTableDTO);

        dynamicHelper.changeDatasource(turnTableDTO.getEnterpriseGuid());
        String newTableGuid = turnTableDTO.getNewTableGuid();
        String originTableGuid = turnTableDTO.getOriginTableGuid();

        // 清空新桌台购物车与备注
        clearTableCache(newTableGuid);
        clearTableCache(originTableGuid);

        // 处理原始桌台的openid
        handleOriginTableOpenId(turnTableDTO, originTableGuid, newTableGuid);

        // 处理新桌台的openid
        handleNewTableOpenId(turnTableDTO, newTableGuid);

        fromTableDeal(newTableGuid);
        toTableDeal(turnTableDTO, redisUtils.hKeyList(CacheName.DINE_USER + CACHE_KEY + originTableGuid));
        return true;
    }

    private void clearTableCache(String tableGuid) {
        redisUtils.delete(CacheName.SHOP_CART_ITEM + ":" + tableGuid);
        // redisUtils.hDelete(CacheName.ORDER_REMARK, tableGuid);
    }

    private void handleOriginTableOpenId(TurnTableDTO turnTableDTO, String originTableGuid, String newTableGuid) {
        String oldTableRedisKey = CacheName.DINE_USER + CACHE_KEY + originTableGuid;
        Set<String> inIDS = redisUtils.hKeyList(oldTableRedisKey);
        String inMsg = "您的桌台已由【" + turnTableDTO.getOriginTableCode() + "】转至【" + turnTableDTO.getNewTableCode() + "】";
        websocketMessageHelper.sendTableEmqMessage(originTableGuid, turnTableDTO, true, inMsg, inIDS);

        if (!CollectionUtils.isEmpty(inIDS)) {
            redisUtils.rename(oldTableRedisKey, CacheName.DINE_USER + CACHE_KEY + newTableGuid);
        }
    }

    private void handleNewTableOpenId(TurnTableDTO turnTableDTO, String newTableGuid) {
        Set<String> outIDS = redisUtils.hKeyList(CacheName.DINE_USER + CACHE_KEY + newTableGuid);
        log.info("转台需要处理的openid,inIDS:{},outIDS:{}", redisUtils.hKeyList(CacheName.DINE_USER + CACHE_KEY + turnTableDTO.getOriginTableGuid()), outIDS);

        String outMsg = "当前桌台已被【" + turnTableDTO.getOriginTableCode() + "】桌顾客换桌占用";
        websocketMessageHelper.sendTableEmqMessage(newTableGuid, null, false, outMsg, outIDS);

        if (!CollectionUtils.isEmpty(outIDS)) {
            redisUtils.delete(CacheName.DINE_USER + CACHE_KEY + newTableGuid);
        }
    }

	private void toTableDeal(TurnTableDTO turnTableDTO, Set<String> inIDS) {
		List<WxOrderRecordDO> originOutStandingOrders = wxOrderRecordService.getOutStandingOrders(turnTableDTO.getOriginTableGuid());
		log.info("转台：旧桌台详情:{}", originOutStandingOrders);
		if (!ObjectUtils.isEmpty(originOutStandingOrders)) {
			//更改转台微信订单
			WxOrderRecordDO wxOrderRecordDO = originOutStandingOrders.get(0);
			wxOrderRecordDO.setTableGuid(turnTableDTO.getNewTableGuid());
			wxOrderRecordDO.setTableCode(turnTableDTO.getNewTableCode());
			wxOrderRecordDO.setAreaGuid(turnTableDTO.getNewTableAreaGuid());
			wxOrderRecordDO.setAreaName(turnTableDTO.getNewTableAreaName());
			boolean orderRecord = wxOrderRecordService.updateById(wxOrderRecordDO);
			log.info("转台:旧微信订单更新结果:{}", orderRecord);
			LambdaUpdateWrapper<WxStoreMerchantOrderDO> newMerchant = Wrappers.<WxStoreMerchantOrderDO>lambdaUpdate()
					.eq(WxStoreMerchantOrderDO::getOrderRecordGuid, wxOrderRecordDO.getGuid())
					.set(WxStoreMerchantOrderDO::getDiningTableGuid, turnTableDTO.getNewTableGuid())
					.set(WxStoreMerchantOrderDO::getTableCode, turnTableDTO.getNewTableCode())
					.set(WxStoreMerchantOrderDO::getAreaGuid, turnTableDTO.getNewTableAreaGuid())
					.set(WxStoreMerchantOrderDO::getAreaName, turnTableDTO.getNewTableAreaName());
			boolean update = wxStoreMerchantOrderService.update(newMerchant);
			log.info("转台:旧微信批次订单更新结果:{}", update);

			TableDTO tableDTO = new TableDTO();
			tableDTO.setAreaGuid(turnTableDTO.getNewTableAreaGuid());
			tableDTO.setAreaName(turnTableDTO.getNewTableAreaName());
			tableDTO.setTableGuid(turnTableDTO.getNewTableGuid());
			tableDTO.setCode(turnTableDTO.getNewTableCode());
			log.info("转台处理openid对应到新的桌台，tableDTO：{}",tableDTO);
			if (!ObjectUtils.isEmpty(inIDS)) {
				for (String openId : inIDS) {
					log.info("转台处理openid:{}",openId);
					WxMemberSessionUtil.updateMemberSession(redisUtils, tableDTO, openId);
				}
			}

		}
	}

	/**
	 * 被占台处理
	 *
	 * @param newTableGuid 桌台id
	 */
	private void fromTableDeal(String newTableGuid) {
		List<WxOrderRecordDO> outStandingOrders = wxOrderRecordService.getOutStandingOrders(newTableGuid);
		log.info("转台：新桌台详情:{}", outStandingOrders);
		if (!ObjectUtils.isEmpty(outStandingOrders)) {
			//拒单所有批次
			WxOrderRecordDO wxOrderRecordDO = outStandingOrders.get(0);
			List<WxStoreMerchantOrderDO> merchantOrderDOS = wxStoreMerchantOrderService.lambdaQuery()
					.eq(WxStoreMerchantOrderDO::getOrderRecordGuid, wxOrderRecordDO.getGuid())
					.orderByAsc(WxStoreMerchantOrderDO::getGmtCreate)
					.list();
			log.info("被转台批次拒单:{}", merchantOrderDOS);
			if (!ObjectUtils.isEmpty(merchantOrderDOS)) {
				WxStoreMerchantOrderDO wxStoreMerchantOrderDO = merchantOrderDOS.get(0);
				String openId = wxStoreMerchantOrderDO.getOpenId();
				log.info("转台:下单人:{}", openId);
				if (!StringUtils.isEmpty(openId)) {
					WxUserRecordDO one = wxUserRecordService.getOneByOpenId(openId);
					if (one != null) {
						wxOrderRecordDO.setUserRecordGuid(one.getGuid());
						wxOrderRecordDO.setActuallyPayFee(merchantOrderDOS.stream()
								.map(x -> Optional.ofNullable(x.getTotalPrice()).orElse(BigDecimal.ZERO))
								.reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
						wxOrderRecordDO.setIsLogin(one.getIsLogin());
						wxOrderRecordDO.setOrderState(7);
						wxOrderRecordDO.setOrderStateName("已取消");
						log.info("被转台订单取消:{}", wxOrderRecordDO);
						boolean orderRcord = wxOrderRecordService.updateById(wxOrderRecordDO);
						log.info("被转台订单取消成功与否:{}", orderRcord);
						merchantOrderDOS = merchantOrderDOS.stream().peek(x -> x.setOrderState(2)).collect(Collectors.toList());
						log.info("被转台拒单批次:{}", merchantOrderDOS);
						wxStoreMerchantOrderService.updateBatchById(merchantOrderDOS);
					}
				} else {
					log.info("当前批次查询不到下单人:{}", wxStoreMerchantOrderDO);
					wxOrderRecordService.removeById(wxOrderRecordDO.getGuid());
				}
			} else {
				log.info("根据当前record查不到merchant批次:{}", wxOrderRecordDO);
				wxOrderRecordService.removeById(wxOrderRecordDO.getGuid());
			}


        }
    }
}
