package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.weixin.service.WxStoreMpService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMpMessageHandler
 * @date 2019/04/01 14:47
 * @description 微信公众号消息处理
 * @program holder-saas-store
 */
@RequestMapping("/wx_mp")
@Slf4j
@RestController
public class WxMpMessageHandler {

    @Autowired
    WxStoreMpService wxStoreMpService;

    @PostMapping("/verify")
    public String verify(@RequestBody WxCommonReqDTO wxCommonReqDTO) {
        return wxStoreMpService.verifyMessage(wxCommonReqDTO);
    }

    @PostMapping("/get_user_info")
    public String getUserInfo(@RequestBody WxAuthorizeReqDTO wxAuthorizeReqDTO) throws WxErrorException {
        return wxStoreMpService.getUserInfo(wxAuthorizeReqDTO);
    }
}
