package com.holderzone.saas.store.weixin.service.impl;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.map.GeoCodeDTO;
import com.holderzone.saas.store.dto.map.GeoCodeRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.queue.StoreGuidDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxPositionDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreCityDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConfigDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreListDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreQueueConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreCityListRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreStatusRespDTO;
import com.holderzone.saas.store.enums.SpecialProvinceEnum;
import com.holderzone.saas.store.weixin.entity.domain.WxQueueConfigDO;
import com.holderzone.saas.store.weixin.service.WxConfigOverviewService;
import com.holderzone.saas.store.weixin.service.WxQueueConfigService;
import com.holderzone.saas.store.weixin.service.WxStoreListService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import com.holderzone.saas.store.weixin.service.rpc.QueueClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import com.holderzone.saas.store.weixin.utils.MapUtil;

import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreListServiceImpl
 * @date 2019/05/17 20:05
 * @description
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxStoreListServiceImpl implements WxStoreListService {

    @Autowired
    OrganizationClientService organizationClientService;

    @Autowired
    DynamicHelper dynamicHelper;

    @Autowired
    WxQueueConfigService wxQueueConfigService;

    @Autowired
    WxConfigOverviewService wxConfigOverviewService;

    @Autowired
    QueueClientService queueClientService;

    @Autowired
    WxStoreTableClientService wxStoreTableClientService;

    @Autowired
    MapUtil mapUtil;

    @Override
    public WxStoreListDTO listStoreConfig(WxPositionDTO wxPositionDTO) {
        dynamicHelper.changeDatasource(wxPositionDTO.getEnterpriseGuid());
        log.info("區號入參:{}", wxPositionDTO);
        String brandGuid = wxPositionDTO.getBrandGuid();
        if (StringUtils.isEmpty(brandGuid)) {
            throw new BusinessException("品牌guid不能为空");
        }
        WxStoreListDTO wxStoreListDTO = new WxStoreListDTO();
        // 获取品牌基本信息
        BrandDTO brandDTO = organizationClientService.queryBrandByGuid(brandGuid);
        wxStoreListDTO.setEnterpriseGuid(wxPositionDTO.getEnterpriseGuid());
        wxStoreListDTO.setBrandGuid(brandGuid);
        wxStoreListDTO.setBrandName(brandDTO.getName());
        wxStoreListDTO.setBrandLogoUrl(brandDTO.getLogoUrl());

        StoreDTO queryDTO = new StoreDTO();
        queryDTO.setBelongBrandGuid(brandGuid);

        String cityCode = wxPositionDTO.getCityCode();
        String cityName = wxPositionDTO.getCityName();
        // 确保城市名称不会误传为空字符串
        cityName = StringUtils.isEmpty(cityName) ? null : cityName.trim();
        // 当城市名称为空而code码不为空时，确保code传入的值必定设置为999
        cityCode = StringUtils.isEmpty(cityName) && !StringUtils.isEmpty(cityCode) ? "999" : cityCode;

        queryDTO.setCityCode(cityCode);
        queryDTO.setCityName(cityName);

        log.info("通过品牌及地址查询门店请求参数：{}", JacksonUtils.writeValueAsString(queryDTO));
        // 查询当前品牌下所有门店信息
        List<StoreDTO> storeDTOList = organizationClientService.queryStoreByCityAndBrand(queryDTO);
        if (ObjectUtils.isEmpty(storeDTOList)) {
            log.info("该查询条件下无门店信息");
            return null;
        }

        List<String> storeGuidList = storeDTOList.stream()
                .map(StoreDTO::getGuid)
                .collect(Collectors.toList());

        // 获取微信功能总览信息
        List<WxStoreStatusRespDTO> wxStoreStatusRespDTOList = wxConfigOverviewService.listByStoreGuidList(storeGuidList);
        Map<String, WxStoreStatusRespDTO> wxStoreStatusRespDTOMap = wxStoreStatusRespDTOList.stream()
                .collect(Collectors.toMap(WxStoreStatusRespDTO::getStoreGuid, Function.identity()));

        List<WxQueueConfigDO> queueConfigDOList = wxQueueConfigService.list(
                new LambdaQueryWrapper<WxQueueConfigDO>().in(WxQueueConfigDO::getStoreGuid, storeGuidList));
        Map<String, WxQueueConfigDO> queueConfigDOMap = queueConfigDOList.stream()
                .collect(Collectors.toMap(WxQueueConfigDO::getStoreGuid, Function.identity()));

        List<WxStoreConfigDTO> wxStoreConfigDTOList = getListByThread(storeDTOList, wxPositionDTO, wxStoreStatusRespDTOMap, queueConfigDOMap);

        wxStoreListDTO.setStoreConfigDTOS(wxStoreConfigDTOList);
        wxStoreListDTO.setStoreCount(wxStoreConfigDTOList.size());

        return wxStoreListDTO;
    }

    @Override
    public WxStoreCityListRespDTO listStoreCity(WxPositionDTO wxPositionDTO) {
        dynamicHelper.changeDatasource(wxPositionDTO.getEnterpriseGuid());
        StoreDTO queryDTO = new StoreDTO();
        queryDTO.setBelongBrandGuid(wxPositionDTO.getBrandGuid());
        List<StoreDTO> storeDTOList = organizationClientService.queryStoreByCityAndBrand(queryDTO);
        if (ObjectUtils.isEmpty(storeDTOList))
            return null;
        List<WxStoreCityDTO> otherCities = new ArrayList<>();
        List<WxStoreCityDTO> collect = storeDTOList.stream()
                .collect(Collectors.groupingBy(o ->
                        getCityByAddress(o.getCityCode(), o.getCityName(), o.getProvinceCode(), o.getProvinceName())))
                .entrySet().stream().map(stringListEntry -> {
                    WxStoreCityDTO wxStoreCityDTO = new WxStoreCityDTO();
                    String[] split = stringListEntry.getKey().split(":");
                    wxStoreCityDTO.setCode(split[0]);
                    wxStoreCityDTO.setCityName(split[1]);
                    wxStoreCityDTO.setCount(stringListEntry.getValue().size());
                    if (Objects.equals("999", wxStoreCityDTO.getCode()))
                        otherCities.add(wxStoreCityDTO);
                    return wxStoreCityDTO;
                }).collect(Collectors.toList());
        collect.removeAll(otherCities);
        WxStoreCityListRespDTO wxStoreCityListRespDTO = new WxStoreCityListRespDTO();
        wxStoreCityListRespDTO.setTotalCount(storeDTOList.size());
        wxStoreCityListRespDTO.setCityList(collect.stream().sorted(Comparator.comparing(WxStoreCityDTO::getCode)).collect(Collectors.toList()));
        wxStoreCityListRespDTO.setOtherCities(otherCities);
        return wxStoreCityListRespDTO;
    }

    /**
     * 判断该门店是否营业中
     * 判断条件：当前处于营业时间内，且门店微信开关打开
     *
     * @param storeDTO
     * @param isOpened
     * @return
     */
    private Boolean isStoreOpen(StoreDTO storeDTO, Integer isOpened) {
        LocalTime startTime = storeDTO.getBusinessStart();
        LocalTime endTime = storeDTO.getBusinessEnd();
        // 判断当前时间是否在营业时间内
        Boolean isInTime = LocalTime.now().isAfter(startTime) && LocalTime.now().isBefore(endTime);
        return isInTime && Objects.equals(1, isOpened);
    }

    /**
     * 是否需要排队
     * 不需要排队条件：门店无排队队列，且门店桌台有空闲
     *
     * @param storeDTO
     * @return
     */
    private Boolean needQueue(StoreDTO storeDTO) {
        StoreGuidDTO storeGuidDTO = new StoreGuidDTO();
        storeGuidDTO.setStoreGuid(storeDTO.getGuid());
        Boolean queueEmpty = queueClientService.allEmpty(storeGuidDTO);
        TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid(storeDTO.getGuid());
        List<TableOrderDTO> tableOrderDTOS = wxStoreTableClientService.listByAndroid(tableBasicQueryDTO);
        List<TableOrderDTO> collect = tableOrderDTOS.stream().filter(e -> Objects.equals(0, e.getStatus())).collect(Collectors.toList());
        return queueEmpty && !ObjectUtils.isEmpty(collect);
    }

    private void caculateDistant(WxStoreConfigDTO wxStoreConfigDTO, StoreDTO storeDTO, BigDecimal userLon, BigDecimal userLat) {
        if (ObjectUtils.isEmpty(storeDTO.getLongitude()) || ObjectUtils.isEmpty(storeDTO.getLatitude())) {
            String detailAdd = storeDTO.getProvinceName() + storeDTO.getCityName() + storeDTO.getCountyName() + storeDTO.getAddressDetail();
            GeoCodeRespDTO geoCodeRespDTO = mapUtil.geoLocation(detailAdd);
            List<GeoCodeDTO> geoCodeDTOList = geoCodeRespDTO.getGeoCodeDTOList();
            if (ObjectUtils.isEmpty(geoCodeDTOList)) {
                return;
            }
            Pair<BigDecimal, BigDecimal> pair = mapUtil.splitLocation(geoCodeDTOList.get(0).getLocation());
            if (!ObjectUtils.isEmpty(pair)) {
                storeDTO.setLongitude(pair.getKey().toString());
                storeDTO.setLatitude(pair.getValue().toString());
            } else {
                return;
            }
        }
        BigDecimal storeLon = new BigDecimal(storeDTO.getLongitude());
        BigDecimal storeLat = new BigDecimal(storeDTO.getLatitude());
        // 计算用户与门店之间的距离
        Integer distance = mapUtil.distant(userLon, userLat, storeLon, storeLat);
        wxStoreConfigDTO.setLongitude(storeLon);
        wxStoreConfigDTO.setLatitude(storeLat);
        wxStoreConfigDTO.setDistant(distance);
    }

    /**
     * 判断当前门店所配置的城市code是否是直辖市或者是港澳台
     * cityCode == null, cityName == null, provinceCode == null, provinceName == null; 999:其他
     * provinceCode == 直辖市 || provinceName == 直辖市
     *
     * @param cityCode
     * @param cityName
     * @param provinceCode
     * @param provinceName
     * @return cityCode:cityName 拼装的字符串，无地址的为'999:其他'，直辖市及港澳台返回provinceCode:provinceName；
     */
    private String getCityByAddress(String cityCode, String cityName, String provinceCode, String provinceName) {
        if (StringUtils.hasText(provinceCode) && StringUtils.hasText(provinceName))
            if (!Objects.equals(SpecialProvinceEnum.DEFAULT, SpecialProvinceEnum.getByCode(provinceCode)))
                return provinceCode + ":" + provinceName;
        if (StringUtils.hasText(cityCode) && StringUtils.hasText(cityName))
            return cityCode + ":" + cityName;
        return SpecialProvinceEnum.DEFAULT.getCode() + ":" + SpecialProvinceEnum.DEFAULT.getName();
    }

    private List<WxStoreConfigDTO> getListByThread(List<StoreDTO> storeDTOList, WxPositionDTO wxPositionDTO, Map<String, WxStoreStatusRespDTO> wxStoreStatusRespDTOMap, Map<String, WxQueueConfigDO> queueConfigDOMap) {
        List<WxStoreConfigDTO> wxStoreConfigDTOList = Collections.synchronizedList(new ArrayList<>());
        storeDTOList.parallelStream().forEach(storeDTO -> {
            dynamicHelper.changeDatasource(wxPositionDTO.getEnterpriseGuid());
            WxStoreConfigDTO wxStoreConfigDTO = new WxStoreConfigDTO();
            wxStoreConfigDTO.setStoreGuid(storeDTO.getGuid());
            wxStoreConfigDTO.setStoreName(storeDTO.getName());
            wxStoreConfigDTO.setAddress(storeDTO.getAddressDetail());
            wxStoreConfigDTO.setTel(storeDTO.getContactTel());
            // 用户经纬度不为空且门店经纬度不为空时 计算当前用户与门店之间距离
            if (!ObjectUtils.isEmpty(wxPositionDTO.getLatitude()) && !ObjectUtils.isEmpty(wxPositionDTO.getLongitude())
                    && !ObjectUtils.isEmpty(storeDTO.getAddressDetail())) {
                caculateDistant(wxStoreConfigDTO, storeDTO, wxPositionDTO.getLongitude(), wxPositionDTO.getLatitude());
            }
            WxStoreStatusRespDTO wxStoreStatusRespDTO = wxStoreStatusRespDTOMap.get(storeDTO.getGuid());
            wxStoreConfigDTO.setIsOpened(isStoreOpen(storeDTO, wxStoreStatusRespDTO.getIsOpened()));
            WxQueueConfigDO wxQueueConfigDO = queueConfigDOMap.get(storeDTO.getGuid());
            WxStoreQueueConfigDTO wxStoreQueueConfigDTO = new WxStoreQueueConfigDTO();
            wxStoreQueueConfigDTO.setShowQueueButton(Objects.equals(1, wxStoreStatusRespDTO.getQueueUpStatus()));
            wxStoreQueueConfigDTO.setDistantConstraint(Objects.equals(1, wxQueueConfigDO.getDistantConstraint()));
            wxStoreQueueConfigDTO.setMaxDistant(wxQueueConfigDO.getDistant());
            wxStoreQueueConfigDTO.setCouldQueue(Objects.equals(1, wxQueueConfigDO.getIsQueueOpen()));
            // 设置是否需要排队
            wxStoreQueueConfigDTO.setNeedQueue(!needQueue(storeDTO));
            wxStoreConfigDTO.setWxStoreQueueConfigDTO(wxStoreQueueConfigDTO);
            wxStoreConfigDTOList.add(wxStoreConfigDTO);
        });

        if (!ObjectUtils.isEmpty(wxStoreConfigDTOList)) {
            wxStoreConfigDTOList.sort(Comparator.comparing(WxStoreConfigDTO::getDistant, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(WxStoreConfigDTO::getStoreGuid));
        }
        return wxStoreConfigDTOList;
    }
}
