package com.holderzone.saas.store.weixin.entity.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQrCodeUrlQuery
 * @date 2019/03/19 11:01
 * @description 微信二维码url获取参数
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("微信二维码url获取参数")
public class WxQrCodeUrlQuery {

    @ApiModelProperty("企业guid")
    private String enterpriseGuid;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("门店名字")
    private String storeName;

    @ApiModelProperty("区域guid")
    private String areaGuid;

    @ApiModelProperty("区域名字")
    private String areaName;

    @ApiModelProperty("桌台guid")
    private String tableGuid;

    @ApiModelProperty("桌台名字")
    private String tableName;

    @ApiModelProperty("二维码类型")
    private Integer qrCodeType;

    @ApiModelProperty("品牌guid")
    private String brandGuid;

    @ApiModelProperty("当前品牌对应的appId")
    private String appId;

    @ApiModelProperty("是否再次")
    private Boolean isAgain;

    @ApiModelProperty("是否自己")
    private Boolean isMyself;

    @ApiModelProperty("自己公众号的openId")
    private String myselfOpenId;
}
