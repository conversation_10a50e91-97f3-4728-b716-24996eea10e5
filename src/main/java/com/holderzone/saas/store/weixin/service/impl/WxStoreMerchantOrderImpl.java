package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WebsocketUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.OpenTableDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.deal.ItemCouponInfoDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.deal.WechatOrderInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreMerchantOrderRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.order.OrderStateEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderItemDO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.entity.dto.PadOrderMsg;
import com.holderzone.saas.store.weixin.entity.dto.WxMerchantOrderMsg;
import com.holderzone.saas.store.weixin.entity.enums.DiningTYPEEnum;
import com.holderzone.saas.store.weixin.entity.query.WxStorePendingOrdersQuery;
import com.holderzone.saas.store.weixin.helper.WebsocketMessageHelper;
import com.holderzone.saas.store.weixin.mapper.WxOrderItemMapper;
import com.holderzone.saas.store.weixin.mapper.WxStoreMerchantOrderMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreMerchantOrderMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreMerchantOrderImpl
 * @date 2019/4/9
 */
@Service
@Slf4j
public class WxStoreMerchantOrderImpl extends ServiceImpl<WxStoreMerchantOrderMapper, WxStoreMerchantOrderDO> implements WxStoreMerchantOrderService {

    private final WxStoreMerchantOrderMapstruct wxStoreMerchantOrderMapstruct;

    private final RedisUtils redisUtils;

    private final OrganizationClientService organizationClientService;
    private final BizMsgFeignClient bizMsgFeignClient;
    @Autowired
    WxStoreTableClientService wxStoreTableClientService;
    @Autowired
    EnterpriseClientService enterpriseClientService;
    @Autowired
    private WxStoreDineInOrderClientService wxStoreDineInOrderClientService;
    @Autowired
    private WxStoreMenuDetailsService wxStoreMenuDetailsService;
    @Autowired
    private WxStoreMerchantDineInItemService wxStoreMerchantDineInItemService;
    @Autowired
    private WxStoreOrderConfigService wxStoreOrderConfigService;

    @Autowired
    private WxStoreSessionDetailsService wxStoreSessionDetailsService;
    @Autowired
    private WxStoreMerchantOrderMapper wxStoreMerchantOrderMapper;
    @Lazy
    @Resource
    private OrderItemService orderItemService;

    @Autowired
    private WxOrderItemMapper wxOrderItemMapper;

    @Lazy
    @Resource
    private WxOrderRecordService wxOrderRecordService;

    @Resource
    private WxUserRecordService wxUserRecordService;

    @Resource
    private WebsocketUtils websocketUtils;


    @Resource
    private WebsocketMessageHelper websocketMessageHelper;

    @Resource
    private UserMemberSessionUtils userMemberSessionUtils;

    @Resource
    private WxStoreDineInBillClientService wxStoreDineInBillClientService;

    @Resource
    MemberClientService memberClientService;

    @Resource
    HsaBaseClientService hsaBaseClientService;

    @Resource
    private GrouponService grouponService;

    private final TradeClientService tradeClientService;

    private final WxStoreTableClientService tableClientService;

    @Autowired
    public WxStoreMerchantOrderImpl(WxStoreMerchantOrderMapstruct wxStoreMerchantOrderMapstruct, RedisUtils redisUtils,
                                    OrganizationClientService organizationClientService, BizMsgFeignClient bizMsgFeignClient,
                                    TradeClientService tradeClientService, WxStoreTableClientService tableClientService) {
        this.wxStoreMerchantOrderMapstruct = wxStoreMerchantOrderMapstruct;
        this.redisUtils = redisUtils;
        this.organizationClientService = organizationClientService;
        this.bizMsgFeignClient = bizMsgFeignClient;
        this.tradeClientService = tradeClientService;
        this.tableClientService = tableClientService;
    }

    /**
     * 获取当前门店所有订单
     *
     * @param wxStoreMerchantOrderReqDTO WxStoreMerchantOrderReqDTO
     * @return WxStoreMerchantOrderRespDTO
     */
    @Override
    public WxStoreMerchantOrderRespDTO getWxStoreMerchantOrderResp(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO) {
        String storeGuid = UserContextUtils.getStoreGuid();
        Assert.hasText(storeGuid, "门店guid不能为空");
        Integer count = wxStoreMerchantOrderReqDTO.getCount();

        // 查询pad的下单表
        List<PadOrderRespDTO> padOrderRespDTOList = tradeClientService.listPadOrder(wxStoreMerchantOrderReqDTO);
        List<WxStoreMerchantOrderDTO> merchantOrderDTOList = wxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(padOrderRespDTOList);
        // 查询微信的下单表
        List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = queryWeChatOrder(wxStoreMerchantOrderReqDTO, storeGuid);
        merchantOrderDTOList.addAll(wxStoreMerchantOrderDTOS);
        if (CollectionUtils.isEmpty(merchantOrderDTOList)) {
            log.warn("未查询到下单信息");
            return new WxStoreMerchantOrderRespDTO();
        }

        // 分页
        int size = merchantOrderDTOList.size();
        if (size > count) {
            merchantOrderDTOList.subList(0, count);
        } else {
            merchantOrderDTOList.subList(0, size);
        }

        // 按照下单时间倒序
        merchantOrderDTOList.sort(Comparator.comparing(WxStoreMerchantOrderDTO::getGmtCreate).reversed());
        log.info("分页后的数据 merchantOrderDTOList={}", JacksonUtils.writeValueAsString(merchantOrderDTOList));
        return new WxStoreMerchantOrderRespDTO(merchantOrderDTOList);
    }

    /**
     * 根据条件查询微信下单列表
     *
     * @param wxStoreMerchantOrderReqDTO 条件
     * @param storeGuid                  门店guid
     * @return 微信下单列表
     */
    private List<WxStoreMerchantOrderDTO> queryWeChatOrder(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO, String storeGuid) {
        //设置数量默认值
        if(ObjectUtils.isEmpty(wxStoreMerchantOrderReqDTO.getCount())){
            wxStoreMerchantOrderReqDTO.setCount(100);
        }
        String lastGuid = wxStoreMerchantOrderReqDTO.getLastGuid();
        LocalDateTime gmtCreate = null;
        if (!StringUtils.isEmpty(lastGuid)) {
            WxStoreMerchantOrderDO lastDO = getById(lastGuid);
            if (!ObjectUtils.isEmpty(lastDO) && !ObjectUtils.isEmpty(lastDO.getGmtCreate())) {
                gmtCreate = lastDO.getGmtCreate();
            }
        }

        List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOList = wxStoreMerchantOrderMapper.queryWeChatOrderListOptimized(wxStoreMerchantOrderReqDTO,storeGuid,gmtCreate);
        return wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOList);
    }

    /**
     * 一体机获取订单详情
     * 包含pad和微信的订单
     *
     * @param wxStoreMerchantOrderReqDTO 预下单的guid（pad和微信）
     * @return 订单详情
     */
    @Override
    public WxStoreMerchantOrderDTO getDetailPend(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO) {
        WxStoreMerchantOrderDTO wxOrderDTO = new WxStoreMerchantOrderDTO();
        WxStoreMerchantOrderDO wxStoreMerchantOrderDO = getById(wxStoreMerchantOrderReqDTO.getGuid());
        if (!ObjectUtils.isEmpty(wxStoreMerchantOrderDO)) {
            wxOrderDTO = wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDO);
//            String tradeGuid = wxStoreMerchantOrderDO.getOrderGuid();
//            DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
//            if (StringUtils.isNotEmpty(tradeGuid)) {
//                SingleDataDTO singleDataDTO = new SingleDataDTO();
//                singleDataDTO.setData(tradeGuid);
//                singleDataDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
//                singleDataDTO.setStoreGuid(UserContextUtils.getStoreGuid());
//                dineinOrderDetailRespDTO = wxStoreDineInOrderClientService.getOrderDetail(singleDataDTO);
//            }
//            log.info("微信订单详情，wxStoreMerchantOrderDTO:{}", wxStoreMerchantOrderDTO);
//            WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItem =
//                    wxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(wxStoreMerchantOrderDTO.getGuid());
//            if (!ObjectUtils.isEmpty(wxStoreMerchantDineInItem)) {
//                wxStoreMerchantOrderDTO.setItemCount(wxStoreMerchantDineInItem.getItemCount());
//                wxStoreMerchantOrderDTO.setDineInItemDTOList(wxStoreMerchantDineInItem.getDineInItemDTOS());
//            } else if (!ObjectUtils.isEmpty(wxStoreMerchantOrderReqDTO.getOrderState()) && wxStoreMerchantOrderReqDTO.getOrderState() == 1) {
//                List<DineInItemDTO> dineInItemDTOS = dineinOrderDetailRespDTO.getDineInItemDTOS();
//                List<DineInItemDTO> batchItemDTOS = dineInItemDTOS.stream().filter(dineInItemDTO ->
//                        StringUtils.isNotEmpty(dineInItemDTO.getWxBatch())
//                                && dineInItemDTO.getWxBatch().equals(wxStoreMerchantOrderReqDTO.getGuid()))
//                        .collect(Collectors.toList());
//                wxStoreMerchantOrderDTO.setDineInItemDTOList(batchItemDTOS);
//                Integer itemCount = 0;
//                for (DineInItemDTO dineInItemDTO : batchItemDTOS) {
//                    BigDecimal currentCount = dineInItemDTO.getCurrentCount();
//                    if (3 == dineInItemDTO.getItemType()) {
//                        currentCount = BigDecimal.ONE;
//                    }
//                    itemCount = itemCount + currentCount.intValue();
//                }
//                wxStoreMerchantOrderDTO.setActualGuestsNo(dineinOrderDetailRespDTO.getGuestCount());
//                wxStoreMerchantOrderDTO.setTotalPrice(dineinOrderDetailRespDTO.getOrderFee());
//                wxStoreMerchantOrderDTO.setItemCount(itemCount);
//            }
//            if (ObjectUtils.isEmpty(wxStoreMerchantOrderDTO.getDineInItemDTOList())) {//如果商品信息为空  ，则将订单状态修改为失效
//                wxStoreMerchantOrderDTO.setOrderState(8);
//            }
            wxOrderDTO.setMsgSource(0);
            wxOrderDTO.setMsgSourceName(DiningTYPEEnum.WEIXIN.getDesc());
            wxOrderDTO.setTradeModeName(DiningTYPEEnum.DINEIN.getDesc());
            wxOrderDTO.setOperationSource(3);
            wxOrderDTO.setOperationSourceName(DiningTYPEEnum.WEIXIN.getDesc());
            if (wxOrderDTO.getOrderSource() != null && BaseDeviceTypeEnum.ALI.getCode() == wxOrderDTO.getOrderSource()) {
                wxOrderDTO.setMsgSource(2);
                wxOrderDTO.setMsgSourceName(DiningTYPEEnum.ALI.getDesc());
                wxOrderDTO.setOperationSourceName(DiningTYPEEnum.ALI.getDesc());
            }

            List<DineInItemDTO> itemListByMerchantGuid = orderItemService.getItemListByMerchantGuid(wxStoreMerchantOrderReqDTO.getGuid());
            log.info("订单详情商品:{}", itemListByMerchantGuid);
            if (CollectionUtils.isEmpty(itemListByMerchantGuid)) {
                removeById(wxStoreMerchantOrderReqDTO.getGuid());
                throw new BusinessException("订单已失效，请刷新");
            }
            wxOrderDTO.setDineInItemDTOList(itemListByMerchantGuid);
            return wxOrderDTO;
        }

        PadOrderRespDTO padOrderRespDTO = tradeClientService.getPadOrderByGuid(wxStoreMerchantOrderReqDTO.getGuid());
        if (!ObjectUtils.isEmpty(padOrderRespDTO)) {
            wxOrderDTO = wxStoreMerchantOrderMapstruct.padOrderDTO2wxOrderDTO(padOrderRespDTO);
            wxOrderDTO.setMsgSource(1);
            wxOrderDTO.setMsgSourceName(DiningTYPEEnum.PAD.getDesc());
            wxOrderDTO.setTradeModeName(TradeModeEnum.DINEIN.getDesc());
            wxOrderDTO.setOperationSource(DiningTYPEEnum.PAD.getCode());
            wxOrderDTO.setOperationSourceName(DiningTYPEEnum.PAD.getDesc());
            OrderDTO orderDTO = tradeClientService.findByOrderGuid(padOrderRespDTO.getOrderGuid());
            wxOrderDTO.setOrderNo(orderDTO.getOrderNo());

            List<DineInItemDTO> itemDTOList = tradeClientService.queryItemByPadGuid(wxStoreMerchantOrderReqDTO.getGuid());
            log.info("订单详情商品 itemDTOList={}", JacksonUtils.writeValueAsString(itemDTOList));
            if (CollectionUtils.isEmpty(itemDTOList)) {
                tradeClientService.removeByGuid(wxStoreMerchantOrderReqDTO.getGuid());
                throw new BusinessException("订单已失效，请刷新");
            }
            // 商品小计计算
            itemDTOList.forEach(dineInItemDTO -> {
                Integer itemType = dineInItemDTO.getItemType();
                switch (itemType) {
                    case 1:
                        packageItemPrice(dineInItemDTO);
                        break;
                    case 2:
                    case 4:
                        nonWeightItemPrice(dineInItemDTO);
                        break;
                    default:
                        log.info("Unexpected value: " + itemType);
                        break;
                }
            });
            wxOrderDTO.setDineInItemDTOList(itemDTOList);
        }
        return wxOrderDTO;
    }

    private void nonWeightItemPrice(DineInItemDTO dineInItemDTO) {
        BigDecimal salePrice = dineInItemDTO.getPrice();
        BigDecimal itemPrice = dineInItemDTO.getCurrentCount().multiply(
                salePrice.add(getAttrTotalPrice(dineInItemDTO.getItemAttrDTOS())));
        dineInItemDTO.setItemPrice(itemPrice);
    }

    /**
     * 属性加价计算
     *
     * @param itemAttrDTOList 属性集合
     * @return 价格
     */
    public BigDecimal getAttrTotalPrice(List<ItemAttrDTO> itemAttrDTOList) {
        return ObjectUtils.isEmpty(itemAttrDTOList) ? BigDecimal.ZERO :
                itemAttrDTOList.stream()
                        .map(attr -> attr.getAttrPrice().multiply(BigDecimal.valueOf(attr.getNum())))
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
    }

    /**
     * 套餐计算和设置值
     *
     * @param dineInItemDTO 商品
     */
    private void packageItemPrice(DineInItemDTO dineInItemDTO) {
        BigDecimal salePrice = dineInItemDTO.getPrice();
        List<SubDineInItemDTO> subDineInItemDTOList = dineInItemDTO.getPackageSubgroupDTOS().stream()
                .flatMap(p -> p.getSubDineInItemDTOS().stream())
                .collect(Collectors.toList());
        BigDecimal subAddPrice = subAttrFee(subDineInItemDTOList);
        BigDecimal itemPrice = dineInItemDTO.getCurrentCount().multiply(salePrice.add(subAddPrice));
        dineInItemDTO.setItemPrice(itemPrice);
    }

    /**
     * 套餐子项属性加价
     *
     * @param subDineInItemDTOList 套餐子项商品
     * @return 价格
     */
    public BigDecimal subAttrFee(List<SubDineInItemDTO> subDineInItemDTOList) {
        return ObjectUtils.isEmpty(subDineInItemDTOList) ? BigDecimal.ZERO :
                subDineInItemDTOList.stream().map(x -> {
                    // pad点餐里的套餐没有属性，没有称重
                    return x.getAddPrice().multiply(x.getCurrentCount());
                }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * 微信和pad接单
     *
     * @param wxOperateReqDTO 微信和pad接单信息
     * @return 微信接单返回，pad也用这个返回
     */
    @Override
    public WxStoreMerchantOperationDTO operationMerchantOrder(WxOperateReqDTO wxOperateReqDTO) {
        log.info("--------------------------- 商家接单入参信息----------------------------------");
        log.info("wxOperateReqDTO：" + JacksonUtils.writeValueAsString(wxOperateReqDTO));
        WxStoreMerchantOrderDO wxStoreMerchantOrderDO = getById(wxOperateReqDTO.getGuid());
        WxStoreMerchantOperationDTO wxStoreMerchantOperationDTO = new WxStoreMerchantOperationDTO();

        // 如果微信查出来了就做微信处理，没有查出来就做pad处理
        if (ObjectUtils.isEmpty(wxStoreMerchantOrderDO)) {
            log.info("------------pad接单-----------");
            return dealWithPadAcceptOrder(wxOperateReqDTO, wxStoreMerchantOperationDTO);
        }

        //待处理
        String adjustRemark = wxOperateReqDTO.getRemark();
        //如果修改的备注信息为空，那么就用自己之前存库的备注
        if (!StringUtils.isNotBlank(adjustRemark)) {
            wxOperateReqDTO.setRemark(wxStoreMerchantOrderDO.getRemark());
        }
        if (ObjectUtils.isEmpty(wxStoreMerchantOrderDO) || 0 != wxStoreMerchantOrderDO.getOrderState()) { // 判断该订单是否还未处理
            if (1 == wxStoreMerchantOrderDO.getOrderState()) {
                return new WxStoreMerchantOperationDTO().setErrorMsg("提交失败，已接单");
            }
            if (2 == wxStoreMerchantOrderDO.getOrderState()) {
                return new WxStoreMerchantOperationDTO().setErrorMsg("提交失败，已拒单");
            }
            return WxStoreMerchantOperationDTO.outDate();
        }

        log.info("------------------------------- 接单方法  -------------------------------------");
        UserContext userContext = UserContextUtils.get();
        Assert.notNull(userContext, "请求头没有会话");
        if (1 == wxOperateReqDTO.getOrderState()) {
            //订单状态为加菜
            WxStoreMerchantOperationDTO operationResultDTO = acceptOrder(wxOperateReqDTO,
                    wxStoreMerchantOrderDO, wxStoreMerchantOperationDTO);
            if (Objects.nonNull(operationResultDTO)) {
                return operationResultDTO;
            }
        }
        wxStoreMerchantOrderDO.setOrderState(wxOperateReqDTO.getOrderState() == 1 ? 1 : 2);
        wxStoreMerchantOrderDO.setDenialReason(wxOperateReqDTO.getDenialReason());
        wxStoreMerchantOrderDO.setOperationGuid(wxOperateReqDTO.getUserGuid());
        wxStoreMerchantOrderDO.setOperationName(wxOperateReqDTO.getUserName());
        wxStoreMerchantOrderDO.setOperationSource(wxOperateReqDTO.getDeviceType());
        wxStoreMerchantOrderDO.setActualGuestsNo(wxOperateReqDTO.getActualGuestsNo());
        //如果商品数量修改，需设置?
//        wxStoreMerchantOrderDO.setItemCount();

        log.info("订单中wxStoreMerchantOrderDO信息:{}", wxStoreMerchantOrderDO);
        updateById(wxStoreMerchantOrderDO);
        WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = null;
        try {
            log.info("修改后进入方法wxStoreMerchantOrderDO：{}", wxStoreMerchantOrderDO);
            wxStoreMerchantOrderDTO = wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDO);
            wxStoreMerchantOrderDTO.setOrderNo(wxStoreMerchantOrderDO.getOrderGuid());
            log.info("订单中wxStoreMerchantOrderDTO执行后:{}", wxStoreMerchantOrderDTO);
        } catch (Exception e) {
            log.error("修改后打印错误信息：{}", e);
        }

        try {
            pushMsg(wxStoreMerchantOrderDTO);

            Set<String> openIdsByTableGuid = websocketUtils.getOpenIdsByTableGuid(wxStoreMerchantOrderDO.getDiningTableGuid());
            if (!CollectionUtils.isEmpty(openIdsByTableGuid)) {
                websocketMessageHelper.sendOrderEmqMessage(wxStoreMerchantOrderDO.getDiningTableGuid()
                        , new ArrayList<>(openIdsByTableGuid));
            }
        } catch (Exception e) {
            log.error("websocketMessageHelper.sendOrderEmqMessage异常", e);
        }
        //emq

        //检测是否所有订单已经被取消
        if (wxOperateReqDTO.getOrderState() == 2) {
            updateRecordOrder(wxStoreMerchantOrderDO, wxOperateReqDTO);
            return wxStoreMerchantOperationDTO;
        } else {
            String orderRecordGuid = wxStoreMerchantOrderDO.getOrderRecordGuid();
            log.info("接单后，微信订单id:{}", orderRecordGuid);
            int i = countByStatus(orderRecordGuid, 0, 0);
            log.info("待确认订单:{}", i);
            String orderGuid = wxStoreTableClientService.getOrderGuid(wxStoreMerchantOrderDO.getDiningTableGuid());
            if (i == 0) {
                WxOrderRecordDO updateDO = new WxOrderRecordDO();
                updateDO.setGuid(orderRecordGuid);
                updateDO.setOrderState(1);
                updateDO.setOrderStateName("已接单");
                if (!StringUtils.isEmpty(orderGuid)) {
                    updateDO.setOrderGuid(orderGuid);
                }
                boolean b = wxOrderRecordService.updateById(updateDO);
                log.info("订单确认更新成功,{}", b);
            }
            try {
                if (!StringUtils.isEmpty(orderGuid)) {
                    updateTradeMemberCard(wxStoreMerchantOrderDO.getOpenId(), orderGuid, wxStoreMerchantOrderDO.getStoreGuid());
                } else {
                    log.info("订单orderGuid为空不处理");
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
        wxStoreMerchantOperationDTO.setDiningTableGuid(wxStoreMerchantOrderDO.getDiningTableGuid());
        wxStoreMerchantOperationDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        wxStoreMerchantOperationDTO.setStoreGuid(wxStoreMerchantOrderDO.getStoreGuid());
        wxStoreMerchantOperationDTO.setTableCode(wxStoreMerchantOrderDO.getTableCode());
        wxStoreMerchantOperationDTO.setAreaName(wxStoreMerchantOrderDO.getAreaName());
        wxStoreMerchantOperationDTO.setStoreName(UserContextUtils.get().getStoreName());
        wxStoreMerchantOperationDTO.setBrandName(wxStoreMerchantOrderDO.getBrandName());

        //一体机、pos机，处理微信订单时，可以修改整单备注和商品备注信息。
        if ("3".equals(wxStoreMerchantOrderDO.getOperationSource()) || "4".equals(wxStoreMerchantOrderDO.getOperationSource())) {
            //修改订单整单备注
            this.updateRemarkById(wxStoreMerchantOrderDO.getGuid(), wxOperateReqDTO.getRemark());
            //修改订单商品备注
            List<DineInItemDTO> dineInItemDTOList = wxOperateReqDTO.getDineInItemDTOList();
            if (!CollectionUtils.isEmpty(dineInItemDTOList)) {
                dineInItemDTOList.forEach(dineInItemDTO -> {
                    this.updateItemRemarkById(wxStoreMerchantOrderDO.getGuid(), dineInItemDTO.getItemGuid(), dineInItemDTO.getRemark());
                });
            }
        }

        return wxStoreMerchantOperationDTO;
    }

    private int getActualGuestsNo(Integer actualGuestsNo) {
        if (Objects.isNull(actualGuestsNo)) {
            return 1;
        }
        return actualGuestsNo;
    }

    /**
     * 接单操作
     */
    private WxStoreMerchantOperationDTO acceptOrder(WxOperateReqDTO wxOperateReqDTO,
                                                    WxStoreMerchantOrderDO wxStoreMerchantOrderDO,
                                                    WxStoreMerchantOperationDTO wxStoreMerchantOperationDTO) {
        // 尝试开台，若以开台，则会返回当前桌台上的订单guid，否则返回新的订单guid
        OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setStoreGuid(wxStoreMerchantOrderDO.getStoreGuid());
        openTableDTO.setTableCode(wxStoreMerchantOrderDO.getTableCode());
        openTableDTO.setTableGuid(wxStoreMerchantOrderDO.getDiningTableGuid());
        openTableDTO.setActualGuestsNo(wxStoreMerchantOrderDO.getActualGuestsNo());
        openTableDTO.setAreaName(wxStoreMerchantOrderDO.getAreaName());
//			openTableDTO.setDeviceType(12);// 表示微信订单
        //微信扫码点餐  订单区分订单来源  无订单来源默认为 12
        if (Objects.nonNull(wxStoreMerchantOrderDO.getOrderSource())) {
            openTableDTO.setDeviceType(wxStoreMerchantOrderDO.getOrderSource());
        } else {
            openTableDTO.setDeviceType(12);// 表示微信订单
        }
        openTableDTO.setDeviceId(wxOperateReqDTO.getDeviceId());
        openTableDTO.setAreaName(wxStoreMerchantOrderDO.getAreaName());
        openTableDTO.setUserGuid(wxStoreMerchantOrderDO.getOpenId());
        openTableDTO.setUserName(wxStoreMerchantOrderDO.getNickName());

        UserContext userContext = UserContextUtils.get();
        if (StringUtils.isEmpty(userContext.getUserGuid()) || StringUtils.isEmpty(userContext.getUserName())) {
            userContext.setUserGuid(wxStoreMerchantOrderDO.getOpenId());
            userContext.setUserName(wxStoreMerchantOrderDO.getNickName());
            UserContextUtils.put(userContext);
        }

        String orderGuid = wxStoreTableClientService.tryOpen(openTableDTO);
        if (ObjectUtils.isEmpty(orderGuid)) {
            return WxStoreMerchantOperationDTO.tableError();
        }
        log.info("开台成功，当前订单号：{}", orderGuid);
        orderItemService.setTableStaffInfo(orderGuid, wxStoreMerchantOrderDO.getDiningTableGuid());
        String remark = wxOperateReqDTO.getRemark();
        log.info("接单整单备注:{}", remark);
        if (!StringUtils.isEmpty(remark)) {
            wxStoreMerchantOrderDO.setRemark(remark);
            CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
            createDineInOrderReqDTO.setGuid(orderGuid);
            createDineInOrderReqDTO.setRemark(remark);
            Boolean updateRemark = wxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO);
            log.info("修改整单备注结果:{}", updateRemark);
        }

        CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setUserWxPublicOpenId(wxStoreMerchantOrderDO.getOpenId());

        // 封装菜品信息
        //获取接单时修正的菜品集合列表，最终订单产品集合以接单菜品列表为准？还是以查询接口为准？选择两种兼容。
        List<DineInItemDTO> dineInItemDTOS = wxOperateReqDTO.getDineInItemDTOList();
        List<DineInItemDTO> itemList = orderItemService.getItemListByMerchantGuid(wxOperateReqDTO.getGuid());
        if (CollectionUtils.isEmpty(itemList)) {
            removeById(wxOperateReqDTO.getGuid());
            return WxStoreMerchantOperationDTO.outDate();
        }
        // 接单校验 团购商品
        WxStoreMerchantOperationDTO verifyGrouponItemResult = acceptVerifyGrouponItem(orderGuid, itemList);
        if (Objects.nonNull(verifyGrouponItemResult)) {
            return verifyGrouponItemResult;
        }
        dineInItemDTOS = CollectionUtils.isEmpty(dineInItemDTOS) ? itemList : dineInItemDTOS;

        dineInItemDTOS = dineInItemDTOS.stream().peek(x -> {
            Optional<DineInItemDTO> optional = itemList.stream()
                    .filter(dineInItemDTO -> dineInItemDTO.getItemGuid().equals(x.getItemGuid()))
                    .findFirst();
            optional.ifPresent(dineInItemDTO -> {
                x.setItemTypeGuid(dineInItemDTO.getItemTypeGuid());
                x.setItemTypeName(dineInItemDTO.getItemTypeName());
            });
            x.setWxBatch(wxStoreMerchantOrderDO.getGuid());
            x.setUserWxPublicOpenId(wxStoreMerchantOrderDO.getOpenId());
        }).collect(Collectors.toList());
        createDineInOrderReqDTO.setDineInItemDTOS(dineInItemDTOS);

        createDineInOrderReqDTO.setGuid(orderGuid);
        //从UserContextUtils.getStoreGuid()可能不对，造成窜单
        createDineInOrderReqDTO.setStoreGuid(wxStoreMerchantOrderDO.getStoreGuid());
        createDineInOrderReqDTO.setStoreName(wxStoreMerchantOrderDO.getStoreName());
        createDineInOrderReqDTO.setAreaName(wxStoreMerchantOrderDO.getAreaName());
        createDineInOrderReqDTO.setDeviceId(wxOperateReqDTO.getDeviceId());
        createDineInOrderReqDTO.setDeviceType(wxOperateReqDTO.getDeviceType());
        createDineInOrderReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        createDineInOrderReqDTO.setGuestCount(getActualGuestsNo(wxStoreMerchantOrderDO.getActualGuestsNo()));
        createDineInOrderReqDTO.setPrint(1);
        createDineInOrderReqDTO.setDiningTableGuid(wxStoreMerchantOrderDO.getDiningTableGuid());
        createDineInOrderReqDTO.setDiningTableName(wxStoreMerchantOrderDO.getTableCode());
        createDineInOrderReqDTO.setRemark(wxStoreMerchantOrderDO.getRemark());
        createDineInOrderReqDTO.setContinueCheckGrouponFlag(true);

        EstimateItemRespDTO estimateItemRespDTO = null;
        try {
            userContext.setUserName(wxStoreMerchantOrderDO.getNickName());
            //防止窜单，必须和订单的门店一致
            Optional.ofNullable(wxStoreMerchantOrderDO.getStoreGuid()).ifPresent(userContext::setStoreGuid);
            Optional.ofNullable(wxStoreMerchantOrderDO.getStoreName()).ifPresent(userContext::setStoreName);
            UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
            createDineInOrderReqDTO.setUserGuid(wxStoreMerchantOrderDO.getOpenId());
            createDineInOrderReqDTO.setUserName(wxStoreMerchantOrderDO.getNickName());
            createDineInOrderReqDTO.setEstimateType(1); //  1：代表接单操作时不受手动估清添加商品影响
            log.info("加菜入参:{}", createDineInOrderReqDTO);
            estimateItemRespDTO = wxStoreDineInOrderClientService.batchAddItems(createDineInOrderReqDTO);// 加菜
            log.info("订单中estimateItemRespDTO信息:{}", estimateItemRespDTO);
        } catch (Exception e) {
            log.info("订单中estimateItemRespDTO报错,卖完了:{}", estimateItemRespDTO);
            log.error("订单报错了，卖完了，订单拒绝", e);
            return WxStoreMerchantOperationDTO.estimate();
        }
        wxStoreMerchantOperationDTO.setEstimateItemRespDTO(estimateItemRespDTO);
        if (!ObjectUtils.isEmpty(estimateItemRespDTO) && !ObjectUtils.isEmpty(estimateItemRespDTO
                .getEstimate()) && Boolean.TRUE.equals(estimateItemRespDTO.getEstimate())
                && !Boolean.TRUE.equals(estimateItemRespDTO.getResult())) {
            log.info("卖完了，订单拒绝");
            return WxStoreMerchantOperationDTO.estimate();
        }
        return null;
    }


    /**
     * 接单校验
     * 团购商品
     */
    private WxStoreMerchantOperationDTO acceptVerifyGrouponItem(String orderGuid, List<DineInItemDTO> itemList) {
        List<MtCouponPreRespDTO> couponPreRespList = itemList.stream()
                .map(DineInItemDTO::getCouponInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(couponPreRespList)) {
            return null;
        }
        try {
            grouponService.preCheckParamVerify(orderGuid, couponPreRespList);
            return null;
        } catch (BusinessException e) {
            log.error("订单校验团购商品接单失败, 失败原因:{}, e", e.getMessage(), e);
            return WxStoreMerchantOperationDTO.constMsg(e.getMessage());
        } catch (Exception e) {
            log.error("订单团购验券校验异常, e:", e);
            return WxStoreMerchantOperationDTO.constMsg("订单团购验券校验异常");
        }
    }

    /**
     * 处理pad订单接单
     *
     * @param wxOperateReqDTO             入参
     * @param wxStoreMerchantOperationDTO 返回实体
     * @return wxStoreMerchantOperationDTO
     */
    private WxStoreMerchantOperationDTO dealWithPadAcceptOrder(WxOperateReqDTO wxOperateReqDTO, WxStoreMerchantOperationDTO wxStoreMerchantOperationDTO) {
        // 更改设备类型为pad点餐
        wxOperateReqDTO.setDeviceType(BaseDeviceTypeEnum.CLOUD_PANEL.getCode());
        PadOrderRespDTO padOrderRespDTO = tradeClientService.getPadOrderByGuid(wxOperateReqDTO.getGuid());
        if (ObjectUtils.isEmpty(padOrderRespDTO)) {
            log.warn("未查询到预下单");
            return WxStoreMerchantOperationDTO.outDate();
        }
        if (Objects.equals(OrderStateEnum.ACCEPT_ORDER.getCode(), padOrderRespDTO.getOrderState())) {
            return new WxStoreMerchantOperationDTO().setErrorMsg("提交失败，已接单");
        }
        if (Objects.equals(OrderStateEnum.REJECT_ORDER.getCode(), padOrderRespDTO.getOrderState())) {
            return new WxStoreMerchantOperationDTO().setErrorMsg("提交失败，已拒单");
        }

        // 接单
        if (Objects.equals(OrderStateEnum.ACCEPT_ORDER.getCode(), wxOperateReqDTO.getOrderState())) {
            String remark = wxOperateReqDTO.getRemark();
            if (!StringUtils.isEmpty(remark)) {
                padOrderRespDTO.setRemark(remark);
                CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
                createDineInOrderReqDTO.setGuid(padOrderRespDTO.getOrderGuid());
                createDineInOrderReqDTO.setRemark(remark);
                Boolean updateRemark = wxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO);
                log.info("修改整单备注结果:{}", updateRemark);
            }

            // 通过下单表guid和订单guid查询加菜商品
            String key = padOrderRespDTO.getOrderGuid() + ":" + padOrderRespDTO.getGuid();
            CreateDineInOrderReqDTO dineInOrderReqDTO = tradeClientService.getPadOrderAddItemInfoByRedis(key);
            if (ObjectUtils.isEmpty(dineInOrderReqDTO)) {
                log.warn("没有查询到订单商品信息 key={}", key);
                return WxStoreMerchantOperationDTO.noItem();
            }
            dineInOrderReqDTO.setDeviceId(wxOperateReqDTO.getDeviceId());
            dineInOrderReqDTO.setDeviceType(wxOperateReqDTO.getDeviceType());
            dineInOrderReqDTO.setPrint(1);
            // 带有商品备注的商品
            List<DineInItemDTO> dineInItemDTOList = wxOperateReqDTO.getDineInItemDTOList();
            if (!CollectionUtils.isEmpty(dineInItemDTOList)) {
                Map<String, DineInItemDTO> dineInItemDTOMap = dineInItemDTOList.stream()
                        .collect(Collectors.toMap(DineInItemDTO::getItemGuid, dii -> dii));
                dineInOrderReqDTO.getDineInItemDTOS().forEach(dineInItemDTO -> {
                    DineInItemDTO itemDTO = dineInItemDTOMap.get(dineInItemDTO.getItemGuid());
                    if (ObjectUtils.isEmpty(itemDTO)) {
                        return;
                    }
                    dineInItemDTO.setRemark(itemDTO.getRemark());
                });
            }

            EstimateItemRespDTO estimateItemRespDTO;
            try {
                log.info("加菜入参:{}", JacksonUtils.writeValueAsString(dineInOrderReqDTO));
                estimateItemRespDTO = wxStoreDineInOrderClientService.batchAddItems(dineInOrderReqDTO);
            } catch (Exception e) {
                log.error("订单报错了，卖完了，订单拒绝", e);
                return WxStoreMerchantOperationDTO.constMsg(e.getMessage());
            }
            wxStoreMerchantOperationDTO.setEstimateItemRespDTO(estimateItemRespDTO);
            if (!ObjectUtils.isEmpty(estimateItemRespDTO) && !ObjectUtils.isEmpty(estimateItemRespDTO
                    .getEstimate()) && estimateItemRespDTO.getEstimate() && !estimateItemRespDTO.getResult()) {
                log.info("卖完了，订单拒绝");
                return WxStoreMerchantOperationDTO.estimate();
            }
        }
        padOrderRespDTO.setOrderState(wxOperateReqDTO.getOrderState() == 1 ? 1 : 2);
        padOrderRespDTO.setDenialReason(wxOperateReqDTO.getDenialReason());
        padOrderRespDTO.setOperationGuid(wxOperateReqDTO.getUserGuid());
        padOrderRespDTO.setOperationName(wxOperateReqDTO.getUserName());
        padOrderRespDTO.setOrderSource(wxOperateReqDTO.getDeviceType());
        padOrderRespDTO.setActualGuestsNo(wxOperateReqDTO.getActualGuestsNo());
        //设置接单时间
        padOrderRespDTO.setGmtModified(LocalDateTime.now());
        tradeClientService.updateById(padOrderRespDTO);
        pushMsg(padOrderRespDTO);

        // 判断当前订单和桌台订单是否一致，一致才推送消息
        TableDTO tableDTO = tableClientService.getTableByGuid(padOrderRespDTO.getDiningTableGuid());
        log.info("tableDTO={}", JacksonUtils.writeValueAsString(tableDTO));
        if (ObjectUtils.isEmpty(tableDTO)) {
            log.error("桌台查询异常 diningTableGuid={}", padOrderRespDTO.getDiningTableGuid());
            return wxStoreMerchantOperationDTO.setErrorMsg("桌台查询异常");
        }
        boolean pushMsgToPad = Objects.equals(padOrderRespDTO.getOrderGuid(), tableDTO.getOrderGuid());
        log.info("当前订单和桌台订单是否一致={}", pushMsgToPad);

        String combineOrderGuid = padOrderRespDTO.getCombineOrderGuid();

        // 拒单,发送订单更变消息
        if (Objects.equals(OrderStateEnum.REJECT_ORDER.getCode(), wxOperateReqDTO.getOrderState())) {
            if (pushMsgToPad) {
                // 并桌订单的id为空则是普通桌台，不为空为并台
                if (StringUtils.isEmpty(combineOrderGuid)) {
                    // 发送拒单消息-订单变化消息
                    rejectOrderPushMsg(wxOperateReqDTO, padOrderRespDTO);
                } else {
                    // 如果是并台，发送消息给pad的各个桌台
                    combineRejectOrderPushMsg(wxOperateReqDTO, combineOrderGuid);
                }
            }
            return wxStoreMerchantOperationDTO;
        }

        wxStoreMerchantOperationDTO.setDiningTableGuid(padOrderRespDTO.getDiningTableGuid());
        wxStoreMerchantOperationDTO.setEnterpriseGuid(wxOperateReqDTO.getEnterpriseGuid());
        wxStoreMerchantOperationDTO.setStoreGuid(padOrderRespDTO.getStoreGuid());
        wxStoreMerchantOperationDTO.setTableCode(padOrderRespDTO.getTableCode());
        wxStoreMerchantOperationDTO.setAreaName(padOrderRespDTO.getAreaName());
        wxStoreMerchantOperationDTO.setStoreName(UserContextUtils.get().getStoreName());

        if (pushMsgToPad) {
            // 并桌订单的id为空则是普通桌台，不为空为并台
            if (StringUtils.isEmpty(combineOrderGuid)) {
                // 发送接单消息
                acceptOrderPushMsg(wxOperateReqDTO, padOrderRespDTO);
            } else {
                // 如果是并台，发送消息给pad的各个桌台
                combineAcceptOrderPushMsg(wxOperateReqDTO, combineOrderGuid);
            }
        }

        return wxStoreMerchantOperationDTO;
    }

    /**
     * 发送拒单消息-订单变化消息
     *
     * @param wxOperateReqDTO WxOperateReqDTO
     * @param padOrderRespDTO PadOrderRespDTO
     */
    private void rejectOrderPushMsg(WxOperateReqDTO wxOperateReqDTO, PadOrderRespDTO padOrderRespDTO) {
        BusinessMessageDTO rejectOrderMessageDTO = new BusinessMessageDTO();
        rejectOrderMessageDTO.setSubject(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getName());
        rejectOrderMessageDTO.setContent("一体机拒单");
        rejectOrderMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        rejectOrderMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getId());
        rejectOrderMessageDTO.setPlatform("2");

        // 查询门店桌台对应设备信息
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(wxOperateReqDTO.getStoreGuid());
        reqDTO.setTableGuid(padOrderRespDTO.getDiningTableGuid());
        StoreDeviceDTO storeDeviceDTO = organizationClientService.queryDeviceByStoreTable(reqDTO);
        if (ObjectUtils.isEmpty(storeDeviceDTO)) {
            log.error("未查询到设备信息 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        rejectOrderMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + storeDeviceDTO.getDeviceNo());
        rejectOrderMessageDTO.setStoreGuid(wxOperateReqDTO.getStoreGuid());
        rejectOrderMessageDTO.setStoreName(wxOperateReqDTO.getStoreName());
        log.info("拒单 rejectOrderMessageDTO={}", JacksonUtils.writeValueAsString(rejectOrderMessageDTO));
        bizMsgFeignClient.msg(rejectOrderMessageDTO);
    }

    /**
     * 并台拒单推送消息
     *
     * @param wxOperateReqDTO  WxOperateReqDTO
     * @param combineOrderGuid 并台订单guid
     */
    private void combineRejectOrderPushMsg(WxOperateReqDTO wxOperateReqDTO, String combineOrderGuid) {
        BusinessMessageDTO combineRejectOrderMessageDTO = new BusinessMessageDTO();
        combineRejectOrderMessageDTO.setSubject(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getName());
        combineRejectOrderMessageDTO.setContent("一体机拒单");
        combineRejectOrderMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        combineRejectOrderMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getId());
        combineRejectOrderMessageDTO.setPlatform("2");
        combineRejectOrderMessageDTO.setStoreGuid(wxOperateReqDTO.getStoreGuid());
        combineRejectOrderMessageDTO.setStoreName(wxOperateReqDTO.getStoreName());

        // 查询子桌设备号
        PadOrderTypeReqDTO query = new PadOrderTypeReqDTO();
        query.setStoreGuid(wxOperateReqDTO.getStoreGuid());

        // 查询并台的所有桌台号
        List<PadOrderRespDTO> padOrderDTOList = tradeClientService.listPadOrderByCombineOrderGuid(combineOrderGuid);
        if (CollectionUtils.isEmpty(padOrderDTOList)) {
            log.error("并台订单查询为空 combineOrderGuid={}", combineOrderGuid);
            throw new BusinessException("并台订单查询为空");
        }
        List<String> tableGuidList = padOrderDTOList.stream()
                .map(PadOrderRespDTO::getDiningTableGuid)
                .distinct()
                .collect(Collectors.toList());
        query.setTableGuidList(tableGuidList);
        List<StoreDeviceDTO> storeDeviceDTOList = organizationClientService.listDeviceByStoreTable(query);
        if (!CollectionUtils.isEmpty(storeDeviceDTOList)) {
            storeDeviceDTOList.forEach(device -> {
                combineRejectOrderMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + device.getDeviceNo());
                log.info("并台拒单 combineRejectOrderMessageDTO={}", JacksonUtils.writeValueAsString(combineRejectOrderMessageDTO));
                bizMsgFeignClient.msg(combineRejectOrderMessageDTO);
            });
        }
    }

    /**
     * 接单推送消息
     *
     * @param wxOperateReqDTO WxOperateReqDTO
     * @param padOrderRespDTO PadOrderRespDTO
     */
    private void acceptOrderPushMsg(WxOperateReqDTO wxOperateReqDTO, PadOrderRespDTO padOrderRespDTO) {
        BusinessMessageDTO acceptOrderMessageDTO = new BusinessMessageDTO();
        acceptOrderMessageDTO.setSubject(BusinessMsgTypeEnum.ACCEPT_ORDER.getName());
        acceptOrderMessageDTO.setContent("一体机接单");
        acceptOrderMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        acceptOrderMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ACCEPT_ORDER.getId());
        acceptOrderMessageDTO.setPlatform("2");
        // 查询门店桌台对应设备信息
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(wxOperateReqDTO.getStoreGuid());
        reqDTO.setTableGuid(padOrderRespDTO.getDiningTableGuid());
        StoreDeviceDTO storeDeviceDTO = organizationClientService.queryDeviceByStoreTable(reqDTO);
        if (ObjectUtils.isEmpty(storeDeviceDTO)) {
            log.error("未查询到设备信息 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        acceptOrderMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + storeDeviceDTO.getDeviceNo());
        acceptOrderMessageDTO.setStoreGuid(wxOperateReqDTO.getStoreGuid());
        acceptOrderMessageDTO.setStoreName(wxOperateReqDTO.getStoreName());
        log.info("接单 acceptOrderMessageDTO={}", JacksonUtils.writeValueAsString(acceptOrderMessageDTO));
        bizMsgFeignClient.msg(acceptOrderMessageDTO);
    }

    /**
     * 并台接单推送消息
     *
     * @param wxOperateReqDTO  WxOperateReqDTO
     * @param combineOrderGuid 并台订单guid
     */
    private void combineAcceptOrderPushMsg(WxOperateReqDTO wxOperateReqDTO, String combineOrderGuid) {
        BusinessMessageDTO combineAcceptOrderMessageDTO = new BusinessMessageDTO();
        combineAcceptOrderMessageDTO.setSubject(BusinessMsgTypeEnum.ACCEPT_ORDER.getName());
        combineAcceptOrderMessageDTO.setContent("一体机接单");
        combineAcceptOrderMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        combineAcceptOrderMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ACCEPT_ORDER.getId());
        combineAcceptOrderMessageDTO.setPlatform("2");
        combineAcceptOrderMessageDTO.setStoreGuid(wxOperateReqDTO.getStoreGuid());
        combineAcceptOrderMessageDTO.setStoreName(wxOperateReqDTO.getStoreName());

        // 查询子桌设备号
        PadOrderTypeReqDTO query = new PadOrderTypeReqDTO();
        query.setStoreGuid(wxOperateReqDTO.getStoreGuid());

        // 查询并台的所有桌台号
        List<PadOrderRespDTO> padOrderDTOList = tradeClientService.listPadOrderByCombineOrderGuid(combineOrderGuid);
        if (CollectionUtils.isEmpty(padOrderDTOList)) {
            log.error("并台订单查询为空 combineOrderGuid={}", combineOrderGuid);
            throw new BusinessException("并台订单查询为空");
        }
        List<String> tableGuidList = padOrderDTOList.stream()
                .map(PadOrderRespDTO::getDiningTableGuid)
                .distinct()
                .collect(Collectors.toList());
        query.setTableGuidList(tableGuidList);
        List<StoreDeviceDTO> storeDeviceDTOList = organizationClientService.listDeviceByStoreTable(query);
        if (!CollectionUtils.isEmpty(storeDeviceDTOList)) {
            storeDeviceDTOList.forEach(device -> {
                combineAcceptOrderMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + device.getDeviceNo());
                log.info("并台接单 combineAcceptOrderMessageDTO={}", JacksonUtils.writeValueAsString(combineAcceptOrderMessageDTO));
                bizMsgFeignClient.msg(combineAcceptOrderMessageDTO);
            });
        }
    }

    /**
     * 一体机推送消息
     *
     * @param padOrderRespDTO pad预订单
     */
    private void pushMsg(PadOrderRespDTO padOrderRespDTO) {
        PadOrderMsg padOrderMsg = new PadOrderMsg();
        padOrderMsg.setGuid(String.valueOf(padOrderRespDTO.getGuid()));
        Integer orderState = padOrderRespDTO.getOrderState();
        padOrderMsg.setOrderState(orderState);
        switch (orderState) {
            case 1:
                padOrderMsg.setBar(2);
                break;
            case 2:
                padOrderMsg.setBar(3);
                break;
            default:
                padOrderMsg.setBar(0);
        }
        log.info("发送给商户端的接单消息初始化：{}", padOrderMsg);
        BusinessMessageDTO businessMessageDTO = pushOrderStateMsg(padOrderRespDTO, padOrderMsg);
        //修改时发消息
        log.info("发送给商户端的接单消息：{}", padOrderMsg);
        bizMsgFeignClient.msg(businessMessageDTO);
    }

    @Resource
    private ExecutorService executorService;


    private void updateTradeMemberCard(String openId, String orderGuid, String storeGuid) {
        UserContext userContext = UserContextUtils.get();
        try {
            executorService.execute(() -> {
                log.info("请求userContext值：{}", userContext);
                updateTradeMemberCardAsyn(openId, orderGuid, storeGuid, userContext);
            });
        } catch (Exception e) {
            log.error("", e.getMessage());
        }
    }


    private void updateTradeMemberCardAsyn(String openId, String orderGuid, String storeGuid, UserContext userContext) {
        try {
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            userContext.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
            UserContextUtils.put(userContext);
            UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(openId);
            if (userMemberSession == null) {
                log.info("userMemberSession为空,不做处理");
                return;
            }
            String memberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
            log.info("userMemberSession,会员卡:{}", memberInfoCardGuid);
            if (StringUtils.isEmpty(memberInfoCardGuid) || memberInfoCardGuid.length() < 5) {
                log.info("userMemberSession没有卡不做处理，memberInfoCardGuid:{}", memberInfoCardGuid);
                return;
            }
            BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
            try {
                RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
                requestQueryMemberInfo.setOpenId(openId);
                ResponseMemberInfo memberInfo = hsaBaseClientService.getMemberInfo(requestQueryMemberInfo).getData();
                if (memberInfo != null && !StringUtils.isEmpty(memberInfo.getPhoneNum())) {
                    billCalculateReqDTO.setMemberPhone(memberInfo.getPhoneNum());
                } else {
                    log.warn("会员没有绑定手机不做处理,openId:{},EnterpriseGuid:{}", openId, userContext.getEnterpriseGuid());
                    return;
                }
            } catch (Exception e) {
                log.info("查询会员手机号失败,{}", e.getMessage());
                return;
            }
            String lockKey = "init-trade:" + orderGuid;
            boolean lockSucc = redisUtils.setNx(lockKey, "1", 60 * 60 * 5);
            if (!lockSucc) {
                log.info("lockKey失败不做处理,已初始化");
                return;
            }


            billCalculateReqDTO.setDeviceId(openId);
            billCalculateReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            billCalculateReqDTO.setStoreGuid(storeGuid);
            billCalculateReqDTO.setOrderGuid(orderGuid);
            billCalculateReqDTO.setDeviceType(12);
            billCalculateReqDTO.setMemberInfoCardGuid(memberInfoCardGuid);
            if (StringUtils.isEmpty(billCalculateReqDTO.getMemberPhone())) {
                billCalculateReqDTO.setMemberPhone(openId);
            }
            billCalculateReqDTO.setMemberIntegral(2);
            billCalculateReqDTO.setMemberLogin(1);
            try {
                log.info("接单自动登录会员卡,billCalculateReqDTO:{}", billCalculateReqDTO);
                DineinOrderDetailRespDTO orderDetailResp = wxStoreDineInBillClientService.calculate(billCalculateReqDTO);
                log.info("接单自动登录会员卡,{}", orderDetailResp);
            } catch (Exception e) {
                log.error("", e.getMessage());
                redisUtils.delete(lockKey);
            }
        } catch (Exception e) {
            log.error("", e.getMessage());
        }

    }

    /**
     * 假如，一体机退了所有的微信订单，与一体机的点餐，则更新订单表为已取消
     *
     * @param wxStoreMerchantOrderDO 批次
     * @param wxOperateReqDTO
     */
    private void updateRecordOrder(WxStoreMerchantOrderDO wxStoreMerchantOrderDO, WxOperateReqDTO wxOperateReqDTO) {
        String orderRecordGuid = wxStoreMerchantOrderDO.getOrderRecordGuid();
        if (StringUtils.isEmpty(orderRecordGuid)) {
            log.error("orderRecordGuid为空, wxStoreMerchantOrderDO:{}", wxStoreMerchantOrderDO);
            return;
        }
        List<WxStoreMerchantOrderDO> list = lambdaQuery()
                .eq(WxStoreMerchantOrderDO::getOrderRecordGuid, orderRecordGuid)
                .select(WxStoreMerchantOrderDO::getOpenId, WxStoreMerchantOrderDO::getOrderState)
                .list();
        // 团购验券商品回退
        List<DineInItemDTO> itemDTOList = orderItemService.getItemListByMerchantGuid(wxStoreMerchantOrderDO.getGuid());
        wxOrderItemCouponRollback(itemDTOList);
        if (CollectionUtils.isEmpty(list) || list.stream().anyMatch(x -> x.getOrderState() != 2)) {
            log.info("list is empty or list has order_state !=2, list:{}", JacksonUtils.writeValueAsString(list));
            return;
        }
        redisUtils.delete(CacheName.USER_COUNT + ":" + wxStoreMerchantOrderDO.getDiningTableGuid());
        WxOrderRecordDO orderRecordDO = wxOrderRecordService.getById(orderRecordGuid);
        log.info("拒单后,微信订单详情:{}", orderRecordDO);
        if (Objects.isNull(orderRecordDO)) {
            log.error("订单详情为空,orderRecordGuid:{}", orderRecordGuid);
            return;
        }
        String userRecordGuid = orderRecordDO.getUserRecordGuid();
        if (StringUtils.isEmpty(userRecordGuid)) {
            WxUserRecordDO user = wxUserRecordService.getOne(
                    Wrappers.<WxUserRecordDO>lambdaQuery()
                            .eq(WxUserRecordDO::getOpenId, wxStoreMerchantOrderDO.getOpenId())
                            .select(WxUserRecordDO::getIsLogin, WxUserRecordDO::getGuid)
                    , false);
            orderRecordDO.setUserRecordGuid(user.getGuid());
            orderRecordDO.setIsLogin(user.getIsLogin());
        }
        List<WxOrderItemDO> orderItemDOS = orderItemService.lambdaQuery()
                .eq(WxOrderItemDO::getOrderRecordGuid, orderRecordGuid)
                .select(WxOrderItemDO::getItemName, WxOrderItemDO::getOriginalPrice,
                        WxOrderItemDO::getMemberPrice, WxOrderItemDO::getCouponInfo)
                .list();
        log.info("拒单后，商品列表:{}", orderItemDOS);
        if (!ObjectUtils.isEmpty(orderItemDOS)) {
            BigDecimal originPrice = BigDecimal.ZERO;
            BigDecimal memberPrice = BigDecimal.ZERO;
            for (WxOrderItemDO wxOrderItemDO : orderItemDOS) {
                originPrice = originPrice.add(wxOrderItemDO.getOriginalPrice());
                if (Boolean.TRUE.equals(orderRecordDO.getIsLogin())) {
                    memberPrice = memberPrice.add(memberPrice);
                }
            }
            orderRecordDO.setActuallyPayFee(originPrice);
            orderRecordDO.setUnMemberPrice(memberPrice);
            orderRecordDO.setItemName(orderItemDOS.stream()
                    .map(WxOrderItemDO::getItemName)
                    .collect(Collectors.joining(",")));
        }
        orderRecordDO.setOrderState(7).setOrderStateName("没有开台且直接拒单");
        boolean update = wxOrderRecordService.updateById(orderRecordDO);
        log.info("拒单后，更新订单:{},{}", update, orderRecordDO);

        // 赚餐订单如果所有订单都是拒单则关台
        if (BaseDeviceTypeEnum.isApplet(wxStoreMerchantOrderDO.getOrderSource())) {
            CancelOrderReqDTO close = new CancelOrderReqDTO();
            close.setTableGuid(orderRecordDO.getTableGuid());
            close.setOrderGuid(wxStoreMerchantOrderDO.getOrderGuid());
            // 用赚餐作为来源
            close.setDeviceType(wxStoreMerchantOrderDO.getOrderSource());
            close.setDeviceId(wxOperateReqDTO.getDeviceId());
            close.setEnterpriseGuid(wxOperateReqDTO.getEnterpriseGuid());
            close.setEnterpriseName(wxOperateReqDTO.getEnterpriseName());
            close.setStoreGuid(wxOperateReqDTO.getStoreGuid());
            close.setStoreName(wxOperateReqDTO.getStoreName());
            close.setUserGuid(wxOperateReqDTO.getUserGuid());
            close.setUserName(wxOperateReqDTO.getUserName());
            boolean closeTable = tableClientService.closeTable(close);
            log.info("赚餐拒单后关台 closeTable={},close={}", closeTable, JacksonUtils.writeValueAsString(close));
        }
    }

    private void wxOrderItemCouponRollback(List<DineInItemDTO> itemDTOList) {
        if (CollectionUtils.isEmpty(itemDTOList)) {
            return;
        }
        List<DineInItemDTO> hasCouponOrderItemList = itemDTOList.stream()
                .filter(e -> Objects.nonNull(e.getGroupVerify()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasCouponOrderItemList)) {
            return;
        }
        log.info("拒单后需要回退的商品:{}", JacksonUtils.writeValueAsString(hasCouponOrderItemList));
        List<GroupVerifyDTO> groupVerifyList = hasCouponOrderItemList.stream()
                .map(DineInItemDTO::getGroupVerify)
                .collect(Collectors.toList());
        grouponService.revokeVerifyGroupon(groupVerifyList);
    }

    @Override
    public void pushAutoMsg(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO, StoreDeviceDTO masterDevice, int autoRev) {
        WxMerchantOrderMsg wxMerchantOrderMsg = new WxMerchantOrderMsg();
        wxMerchantOrderMsg.setGuid(wxStoreMerchantOrderDTO.getGuid());
        wxMerchantOrderMsg.setOrderState(wxMerchantOrderMsg.getOrderState());
        log.info("当前门店主机:{}", masterDevice);
//		wxMerchantOrderMsg.setDeviceGuid(ObjectUtils.isEmpty(masterDevice) || ObjectUtils.isEmpty(masterDevice.getDeviceGuid())
//				? "234" : masterDevice.getDeviceGuid());
        Integer orderState = wxStoreMerchantOrderDTO.getOrderState();
        if (wxStoreMerchantOrderDTO.getTradeMode() == 0) {
            wxMerchantOrderMsg.setAutoRev(0);
//			WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
//			wxStoreReqDTO.setStoreGuid(wxStoreMerchantOrderDTO.getStoreGuid());
//			WxOrderConfigDTO detailConfig = wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
//			if (detailConfig != null) {
//				Integer takingModel = detailConfig.getTakingModel();
//				if (takingModel != null && takingModel == 1&&autoRev==0) {
//					wxMerchantOrderMsg.setAutoRev(0);
//				}
//			}
        } else {
            wxMerchantOrderMsg.setAutoRev(0);
        }
        wxMerchantOrderMsg.setOrderState(orderState);
        switch (orderState) {
            case 1:
                wxMerchantOrderMsg.setBar(2);
                break;
            case 2:
                wxMerchantOrderMsg.setBar(3);
                break;
            default:
                wxMerchantOrderMsg.setBar(0);
        }
        log.info("发送给商户端的接单消息初始化：{}", wxMerchantOrderMsg);
        BusinessMessageDTO businessMessageDTO = pushOrderStateMsg(wxStoreMerchantOrderDTO, wxMerchantOrderMsg);
        //修改时发消息
        log.info("发送给商户端的接单消息：{}", businessMessageDTO);
        bizMsgFeignClient.msg(businessMessageDTO);
    }


    @Override
    public void pushMsg(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO) {
        WxMerchantOrderMsg wxMerchantOrderMsg = new WxMerchantOrderMsg();
        wxMerchantOrderMsg.setGuid(wxStoreMerchantOrderDTO.getGuid());
        wxMerchantOrderMsg.setOrderState(wxMerchantOrderMsg.getOrderState());
//		StoreDeviceDTO masterDevice = organizationClientService.findMasterDevice(wxStoreMerchantOrderDTO.getStoreGuid());
//		//设置deviceid避免重复
//		log.info("当前门店主机:{}", masterDevice);
//		wxMerchantOrderMsg.setDeviceGuid(ObjectUtils.isEmpty(masterDevice) || ObjectUtils.isEmpty(masterDevice.getDeviceGuid())
//				? "234" : masterDevice.getDeviceGuid());
        Integer orderState = wxStoreMerchantOrderDTO.getOrderState();
//		if (wxStoreMerchantOrderDTO.getTradeMode() == 0) {
//			judgeAutoAccept(wxStoreMerchantOrderDTO, wxMerchantOrderMsg);
////			wxMerchantOrderMsg.setDeviceGuid(masterDevice.getDeviceGuid());
////			wxMerchantOrderMsg.setAutoRev(0);
//		} else {
//			wxMerchantOrderMsg.setAutoRev(0);
//		}
        wxMerchantOrderMsg.setOrderState(orderState);
        switch (orderState) {
            case 1:
                wxMerchantOrderMsg.setBar(2);
                break;
            case 2:
                wxMerchantOrderMsg.setBar(3);
                break;
            default:
                wxMerchantOrderMsg.setBar(0);
        }
        log.info("发送给商户端的接单消息初始化：{}", wxMerchantOrderMsg);
        BusinessMessageDTO businessMessageDTO = pushOrderStateMsg(wxStoreMerchantOrderDTO, wxMerchantOrderMsg);
        //修改时发消息
        log.info("发送给商户端的接单消息：{}", wxMerchantOrderMsg);
        bizMsgFeignClient.msg(businessMessageDTO);
    }

    @Override
    public void updateMerchantOrder(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO) {
        WxStoreMerchantOrderDO wxStoreMerchantOrder = wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDTO);
        if (Objects.isNull(wxStoreMerchantOrder.getGuid()) || Objects.isNull(getById(wxStoreMerchantOrder.getGuid()))) {
            wxStoreMerchantOrder.setGuid(redisUtils.generatdDTOGuid(WxStoreMerchantOrderDTO.class));
            wxStoreMerchantOrderDTO.setGuid(wxStoreMerchantOrder.getGuid());
            pushOrderMsg(wxStoreMerchantOrderDTO);
            //mqtt
            pushMsg(wxStoreMerchantOrderDTO);
        }
        saveOrUpdate(wxStoreMerchantOrder);
    }

    @Override
    public void updateFastOrder(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO) {
        WxStoreMerchantOrderDO wxStoreMerchantOrder = wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDTO);
        if (Objects.isNull(wxStoreMerchantOrder.getGuid()) || Objects.isNull(getById(wxStoreMerchantOrder.getGuid()))) {
            wxStoreMerchantOrder.setGuid(redisUtils.generatdDTOGuid(WxStoreMerchantOrderDTO.class));
            wxStoreMerchantOrderDTO.setGuid(wxStoreMerchantOrder.getGuid());
        }
        saveOrUpdate(wxStoreMerchantOrder);
        String redisKey = "WxFast:" + wxStoreMerchantOrderDTO.getStoreGuid() + ":" + wxStoreMerchantOrderDTO.getOpenId();
        redisUtils.set(redisKey, wxStoreMerchantOrderDTO);
    }

    @Override
    public void delMerchantOrder(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO) {

    }

    /**
     * 查询门店待处理订单
     *
     * @param query
     * @return
     */
    @Override
    public List<WxStoreMerchantOrderDTO> getPendingOrders(WxStorePendingOrdersQuery query) {
        LambdaQueryWrapper<WxStoreMerchantOrderDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(!StringUtils.isEmpty(query.getDiningTableGuid()), WxStoreMerchantOrderDO::getDiningTableGuid, query.getDiningTableGuid());
        wrapper.eq(!StringUtils.isEmpty(query.getOrderGuid()), WxStoreMerchantOrderDO::getOrderGuid, query.getOrderGuid());
        wrapper.eq(!StringUtils.isEmpty(query.getCombine()), WxStoreMerchantOrderDO::getCombine, query.getCombine());
        wrapper.eq(!ObjectUtils.isEmpty(query.getTradeMode()), WxStoreMerchantOrderDO::getTradeMode, query.getTradeMode());
        wrapper.in(!ObjectUtils.isEmpty(query.getOrderStates()), WxStoreMerchantOrderDO::getOrderState, query.getOrderStates());
        wrapper.orderByDesc(WxStoreMerchantOrderDO::getGmtCreate);
        List<WxStoreMerchantOrderDO> list = list(wrapper);
        return wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(list);
    }

    @Override
    public BusinessMessageDTO pushOrderMsg(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO) {
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid(redisUtils.generatdDTOGuid(BusinessMessageDTO.class));
        businessMessageDTO.setMessageType(1);
        businessMessageDTO.setStoreGuid(wxStoreMerchantOrderDTO.getStoreGuid());
        businessMessageDTO.setDetailMessageType(82);
        businessMessageDTO.setPlatform(OrderType.WECHAT_ORDER.getDesc());
        String desc = OrderType.WECHAT_ORDER.getDesc();
        if ( wxStoreMerchantOrderDTO.getOrderSource() != null && BaseDeviceTypeEnum.ALI.getCode() == wxStoreMerchantOrderDTO.getOrderSource()) {
            desc = OrderType.ALI_ORDER.getDesc();
        }
        String subject = "您有一个新的" + desc + "需要处理。";
        businessMessageDTO.setSubject(subject);
        WxMerchantOrderMsg wxMerchantOrderMsg = new WxMerchantOrderMsg();
        WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = new WxStoreAdvanceConsumerReqDTO();
        WxStoreConsumerDTO wxStoreConsumerDTO = new WxStoreConsumerDTO();
        wxStoreAdvanceConsumerReqDTO.setWxStoreConsumerDTO(wxStoreConsumerDTO);
        wxStoreConsumerDTO.setStoreGuid(wxStoreMerchantOrderDTO.getStoreGuid());
        StoreDTO storeDTO = wxStoreSessionDetailsService.getStoreDetail(wxStoreMerchantOrderDTO.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
//		wxMerchantOrderMsg.setAutoRev(wxStoreMenuDetailsService.getTakingModel(wxStoreAdvanceConsumerReqDTO));
        wxMerchantOrderMsg.setAutoRev(0);
        wxMerchantOrderMsg.setGuid(wxStoreMerchantOrderDTO.getGuid());
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(wxMerchantOrderMsg));
        businessMessageDTO.setState("0");
        businessMessageDTO.setCreateTime(LocalDateTime.now());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        log.info("新订单businessMessageDTO:{}", businessMessageDTO);
        bizMsgFeignClient.msg(businessMessageDTO);
        return businessMessageDTO;
    }


    /**
     * 状态变更推送消息
     *
     * @param wxStoreMerchantOrderDTO
     * @param wxMerchantOrderMsg
     * @return
     */
    private BusinessMessageDTO pushOrderStateMsg(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO, WxMerchantOrderMsg wxMerchantOrderMsg) {
        log.info("微信订单状态变更wxStoreMerchantOrderDTO:{}", wxStoreMerchantOrderDTO);
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid(redisUtils.generatdDTOGuid(BusinessMessageDTO.class));
        businessMessageDTO.setMessageType(81);
        businessMessageDTO.setStoreGuid(wxStoreMerchantOrderDTO.getStoreGuid());
        businessMessageDTO.setDetailMessageType(81);
        businessMessageDTO.setPlatform(OrderType.WECHAT_ORDER.getDesc());
        String subject = "有一个微信订单" + OrderType.WECHAT_ORDER.getDesc() + "状态变更。";
        businessMessageDTO.setSubject(subject);
        WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = new WxStoreAdvanceConsumerReqDTO();
        WxStoreConsumerDTO wxStoreConsumerDTO = new WxStoreConsumerDTO();
        wxStoreAdvanceConsumerReqDTO.setWxStoreConsumerDTO(wxStoreConsumerDTO);
        wxStoreConsumerDTO.setStoreGuid(wxStoreMerchantOrderDTO.getStoreGuid());
        StoreDTO storeDTO = wxStoreSessionDetailsService.getStoreDetail(wxStoreMerchantOrderDTO.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(wxMerchantOrderMsg));
        businessMessageDTO.setState("0");
        businessMessageDTO.setCreateTime(wxStoreMerchantOrderDTO.getGmtCreate());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        return businessMessageDTO;
    }

    /**
     * 状态变更推送消息
     *
     * @param padOrderRespDTO pad预下单信息
     * @param padOrderMsg     pad订单消息
     * @return BusinessMessageDTO
     */
    private BusinessMessageDTO pushOrderStateMsg(PadOrderRespDTO padOrderRespDTO, PadOrderMsg padOrderMsg) {
        log.info("pad订单状态变更 padOrderRespDTO={}", padOrderRespDTO);
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid(redisUtils.generatdDTOGuid(BusinessMessageDTO.class));
        // 为了复用，暂且不改值
        businessMessageDTO.setMessageType(81);
        businessMessageDTO.setStoreGuid(padOrderRespDTO.getStoreGuid());
        businessMessageDTO.setDetailMessageType(81);
        businessMessageDTO.setPlatform("2");
        String subject = "有一个PAD订单" + OrderType.PAD_ORDER.getDesc() + "状态变更。";
        businessMessageDTO.setSubject(subject);
        WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = new WxStoreAdvanceConsumerReqDTO();
        WxStoreConsumerDTO wxStoreConsumerDTO = new WxStoreConsumerDTO();
        wxStoreAdvanceConsumerReqDTO.setWxStoreConsumerDTO(wxStoreConsumerDTO);
        wxStoreConsumerDTO.setStoreGuid(padOrderRespDTO.getStoreGuid());
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(padOrderRespDTO.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(padOrderMsg));
        businessMessageDTO.setState("0");
        businessMessageDTO.setCreateTime(padOrderRespDTO.getGmtCreate());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        return businessMessageDTO;
    }

    /**
     * 判断自动接单
     *
     * @param wxStoreMerchantOrderDTO
     * @param wxMerchantOrderMsg
     * @return
     */
    private void judgeAutoAccept(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO, WxMerchantOrderMsg wxMerchantOrderMsg) {
        String orderGuid = wxStoreTableClientService.getOrderGuid(wxStoreMerchantOrderDTO.getDiningTableGuid());
        WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
        wxStoreReqDTO.setStoreGuid(wxStoreMerchantOrderDTO.getStoreGuid());
        WxOrderConfigDTO detailConfig = wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
        if (StringUtils.isNotEmpty(orderGuid)) {
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setData(orderGuid);
            try {
                DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(singleDataDTO);
                log.info("-----当前桌台：{}，判断是否自动接单开始-----", wxStoreMerchantOrderDTO.getDiningTableGuid());
                log.info("-----桌台订单guid:{}， 预订单guid：{}", orderGuid, wxMerchantOrderMsg.getGuid());
                if (!ObjectUtils.isEmpty(orderDetail)) {
                    if (!ObjectUtils.isEmpty(orderDetail.getDineInItemDTOS())) {
                        List<DineInItemDTO> dineInItemDTO =
                                orderDetail.getDineInItemDTOS().stream().filter(e -> !"0".equals(e.getWxBatch())).collect(Collectors.toList());
                        if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                            log.info("-------当前订单:{}，自动接单-----", wxMerchantOrderMsg.getGuid());
                            wxMerchantOrderMsg.setAutoRev(0);
                            if (0 == detailConfig.getTakingModel()) {
                                wxMerchantOrderMsg.setAutoRev(1);
                            }
                        } else {
                            log.info("-------当前订单:{}，手动接单-----", wxMerchantOrderMsg.getGuid());
                            wxMerchantOrderMsg.setAutoRev(1);
                        }
                    }
                } else {
                    log.info("-------当前订单:{}，手动接单-----", wxMerchantOrderMsg.getGuid());
                    wxMerchantOrderMsg.setAutoRev(1);
                }
            } catch (Exception e) {
                log.info("-------查询桌台订单异常：{}-----", wxMerchantOrderMsg.getGuid());
                wxMerchantOrderMsg.setAutoRev(1);
            }
        } else {
            log.info("-------当前桌台没有开台：{}-----", wxMerchantOrderMsg.getGuid());
            wxMerchantOrderMsg.setAutoRev(1);
        }
    }


    @Override
    public void pushFastMsg(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO) {
        pushOrderMsg(wxStoreMerchantOrderDTO);
        pushMsg(wxStoreMerchantOrderDTO);
    }


    @Override
    public boolean combine(List<String> merchantBatchGuidList, String mainOrderGuid) {
        List<WxStoreMerchantOrderDO> list = list(new LambdaQueryWrapper<WxStoreMerchantOrderDO>().in(WxStoreMerchantOrderDO::getOrderGuid, merchantBatchGuidList));
        List<WxStoreMerchantOrderDO> collect = list.stream().filter(Objects::nonNull).peek(x -> x.setCombine(mainOrderGuid)).collect(Collectors.toList());
        return updateBatchById(collect);
    }

    @Override
    public List<WxStoreMerchantOrderDO> separate(TableOrderCombineDTO tableOrderCombineDTO) {
        LambdaQueryWrapper<WxStoreMerchantOrderDO> in = new LambdaQueryWrapper<WxStoreMerchantOrderDO>().eq(WxStoreMerchantOrderDO::getCombine, tableOrderCombineDTO.getMainOrderGuid());
        List<TableInfoDTO> tableInfoDTOS = tableOrderCombineDTO.getTableInfoDTOS();
        if (!ObjectUtils.isEmpty(tableInfoDTOS)) {
            List<String> collect = tableInfoDTOS.stream().map(TableInfoDTO::getTableGuid).collect(Collectors.toList());
            in.in(WxStoreMerchantOrderDO::getDiningTableGuid, collect);
        }
        log.info("微信拆台条件:{}", tableInfoDTOS.size());
        List<WxStoreMerchantOrderDO> list = list(in);
        if (ObjectUtils.isEmpty(list)) {
            log.info("没有微信相关的订单");
            return null;
        }
        log.info("拆台微信订单:{}", list);
        List<WxStoreMerchantOrderDO> collect = list.stream().peek(x -> x.setCombine("-1")).collect(Collectors.toList());
        boolean b = updateBatchById(collect);
        if (b) {
            return collect;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void turn(TurnTableDTO turnTableDTO) {
        String newMerchantTableGuid = wxStoreSessionDetailsService.getMerchantBatchGuid(turnTableDTO.getNewTableGuid());
        String originMerchantTableGuid = wxStoreSessionDetailsService.getMerchantBatchGuid(turnTableDTO.getOriginTableGuid());
        if (!ObjectUtils.isEmpty(newMerchantTableGuid)) {
            log.info("清理桌台未处理的微信订单:{}", newMerchantTableGuid);
            remove(new LambdaQueryWrapper<WxStoreMerchantOrderDO>().eq(WxStoreMerchantOrderDO::getOrderGuid, newMerchantTableGuid));
        }
        String mainOrderGuid = getMainOrderGuid(turnTableDTO.getOriginTableGuid());
        if (!ObjectUtils.isEmpty(originMerchantTableGuid)) {
            List<WxStoreMerchantOrderDO> list = ObjectUtils.isEmpty(mainOrderGuid)
                    ? list(new LambdaQueryWrapper<WxStoreMerchantOrderDO>().eq(WxStoreMerchantOrderDO::getOrderGuid, originMerchantTableGuid))
                    : list(new LambdaQueryWrapper<WxStoreMerchantOrderDO>().eq(WxStoreMerchantOrderDO::getCombine, mainOrderGuid));
            log.info("被转台的微信订单:{}", list);
            if (!ObjectUtils.isEmpty(list)) {
                List<WxStoreMerchantOrderDO> collect = list.stream().peek(wxStoreMerchantOrderDO -> {
                    wxStoreMerchantOrderDO.setDiningTableGuid(turnTableDTO.getNewTableGuid());
                    wxStoreMerchantOrderDO.setTableCode(turnTableDTO.getNewTableCode());
                    wxStoreMerchantOrderDO.setAreaGuid(turnTableDTO.getNewTableAreaGuid());
                    wxStoreMerchantOrderDO.setAreaName(turnTableDTO.getNewTableAreaName());
                }).collect(Collectors.toList());
                updateBatchById(collect);
            }
        }
    }


    /**
     * 查询主桌单号
     *
     * @param tableGuid
     * @return
     */
    private String getMainOrderGuid(String tableGuid) {
        List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = wxStoreTableClientService.tableList(tableGuid);
        if (!ObjectUtils.isEmpty(wxStoreTableCombineDTOS)) {
            return wxStoreTableCombineDTOS.get(0).getMainOrderGuid();
        }
        return null;
    }

    /**
     * 结账以后,更新已接单的定单
     *
     * @param tableGuid
     * @param orderState
     */
    @Override
    public void updateOrderState(String tableGuid, Integer orderState) {
        String merchantBatchGuid = wxStoreSessionDetailsService.getMerchantBatchGuid(tableGuid);
        LambdaQueryWrapper<WxStoreMerchantOrderDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxStoreMerchantOrderDO::getOrderGuid, merchantBatchGuid);
        List<WxStoreMerchantOrderDO> list = list(wrapper);
        log.info("结账或作废时修改商户已接单:{}", list);
        if (!ObjectUtils.isEmpty(list)) {
            List<WxStoreMerchantOrderDO> collect = list.stream().filter(x -> 1 == x.getOrderState() || 0 == x.getOrderState()).collect(Collectors.toList());
            log.info("已接单的订单或未处理的订单:{}", collect);
            if (!ObjectUtils.isEmpty(collect)) {
                collect.forEach(x -> {
                    if (x.getOrderState() == 1) {
                        x.setOrderState(orderState);
                    } else if (x.getOrderState() == 0) {
                        x.setOrderState(2);
                    }
                });
                updateBatchById(collect);
            }
        }
    }


    @Override
    public WxStoreMerchantOrderDO getOneByGuid(String merchantGuid) {
        log.info("查询单个订单:{}", merchantGuid);
        return getById(merchantGuid);
    }

    @Override
    public List<WxStoreMerchantOrderDO> tableListByMerchantGuidOrOrderGuid(WxStorePendingOrdersQuery query) {
        LambdaQueryWrapper<WxStoreMerchantOrderDO> wrapper = new LambdaQueryWrapper<WxStoreMerchantOrderDO>()
                .eq(!StringUtils.isEmpty(query.getCombine()), WxStoreMerchantOrderDO::getCombine, query.getCombine())
                .or()
                .eq(!StringUtils.isEmpty(query.getOrderGuid()), WxStoreMerchantOrderDO::getOrderGuid, query.getOrderGuid());
        return list(wrapper);
    }

    @Override
    public List<WxStoreMerchantOrderDTO> currentTimePendingOrderList(@NotBlank String openId) {
        LambdaQueryWrapper<WxStoreMerchantOrderDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WxStoreMerchantOrderDO::getOpenId, openId);
        wrapper.eq(WxStoreMerchantOrderDO::getTradeMode, 0);
        wrapper.in(WxStoreMerchantOrderDO::getOrderState, Arrays.asList(0, 1));
        return wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(list(wrapper));
    }

    @Override
    public void updateOutDateOrder(String openId) {
        List<WxStoreMerchantOrderDO> list = lambdaQuery()
                .eq(WxStoreMerchantOrderDO::getOpenId, openId)
                .eq(WxStoreMerchantOrderDO::getOrderState, 0)
                .list();
        if (!ObjectUtils.isEmpty(list)) {
            List<WxStoreMerchantOrderDO> collect = list.stream()
                    .filter(x -> wxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(x.getGuid()) == null)
                    .peek(x -> x.setOrderState(8))
                    .collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(collect)) {
                saveOrUpdateBatch(collect);
            }
        }
    }

    @Override
    public List<WxStoreMerchantOrderDO> selectByOrderRecordGuid(String orderRecordGuid, Collection<String> tableGuids) {
        if (tableGuids != null && tableGuids.size() <= 1) {
            //只处理并桌的情况，非并桌的只用orderRecordGuid查询
            tableGuids = null;
        }
        return wxStoreMerchantOrderMapper.selectByOrderRecordGuid(orderRecordGuid, tableGuids);
    }

    @Override
    public boolean existPendingOrder(String orderRecordGuid, Collection<String> tableGuids) {
        if (tableGuids != null && tableGuids.size() <= 1) {
            //只处理并桌的情况，非并桌的只用orderRecordGuid查询
            tableGuids = null;
        }
        return wxStoreMerchantOrderMapper.countOrder(orderRecordGuid, tableGuids) > 0;
    }

    @Override
    public int countByStatus(String orderRecordGuid, int status, int tradeMode) {
        LambdaQueryWrapper<WxStoreMerchantOrderDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WxStoreMerchantOrderDO::getOrderRecordGuid, orderRecordGuid);
        wrapper.eq(WxStoreMerchantOrderDO::getOrderState, status);
        wrapper.eq(WxStoreMerchantOrderDO::getTradeMode, tradeMode);
        return wxStoreMerchantOrderMapper.selectCount(wrapper);
    }

    @Override
    public void dealUnFinishedOrders(String orderGuid) {
//		List<WxStoreMerchantOrderDO> list = lambdaQuery().eq(WxStoreMerchantOrderDO::getOrderGuid, orderGuid).list();
//		if (!ObjectUtils.isEmpty(list)) {
//			for (WxStoreMerchantOrderDO wxStoreMerchantOrderDO : list) {
//				Integer orderState = wxStoreMerchantOrderDO.getOrderState();
//				if (orderState == 0) {
//					wxStoreMerchantOrderDO.setOrderState(2);
//					wxStoreMerchantOrderDO.setDenialReason("超时未处理订单");
//				}
//				if (orderState == 1) {
//					wxStoreMerchantOrderDO.setOrderState(4);
//				}
//			}
//			updateBatchById(list);
//		}
//		lambdaUpdate()
//				.eq(WxStoreMerchantOrderDO::getOrderGuid, orderGuid)
//				.eq(WxStoreMerchantOrderDO::getOrderState, 0)
//				.set(WxStoreMerchantOrderDO::getOrderState, 2);
//		WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        LambdaUpdateWrapper<WxStoreMerchantOrderDO> set = new LambdaUpdateWrapper<WxStoreMerchantOrderDO>()
                .eq(WxStoreMerchantOrderDO::getOrderGuid, orderGuid)
                .eq(WxStoreMerchantOrderDO::getOrderState, 0)
                .set(WxStoreMerchantOrderDO::getOrderState, 2);
        update(set);
    }

    @Override
    public WxStoreMerchantOrderDO firstSubmit(String orderRecordGuid) {
        if (!StringUtils.isEmpty(orderRecordGuid)) {
            List<WxStoreMerchantOrderDO> list = lambdaQuery()
                    .eq(WxStoreMerchantOrderDO::getOrderRecordGuid, orderRecordGuid)
                    .orderByAsc(WxStoreMerchantOrderDO::getGmtCreate).list();
            if (!ObjectUtils.isEmpty(list)) {
                return list.get(0);
            } else {
                log.info("空订单:{}", orderRecordGuid);
                return null;
            }
        }
        throw new RuntimeException("微信订单id不能为空");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRemarkById(String guid, String remark) {
        //更新微信订单整单备注
        WxStoreMerchantOrderDO wxStoreMerchantOrderDO = wxStoreMerchantOrderMapper.selectOne(Wrappers.<WxStoreMerchantOrderDO>lambdaQuery()
                .eq(WxStoreMerchantOrderDO::getGuid, guid));
        if (!ObjectUtils.isEmpty(wxStoreMerchantOrderDO)) {
            wxStoreMerchantOrderDO.setRemark(remark);
            int id = wxStoreMerchantOrderMapper.updateById(wxStoreMerchantOrderDO);

            //更新交易订单整单备注
            WxOrderRecordDO wxOrderRecordServiceOne = wxOrderRecordService.getOne(Wrappers.<WxOrderRecordDO>lambdaQuery()
                    .eq(WxOrderRecordDO::getMerchantGuid, wxStoreMerchantOrderDO.getGuid()));
            if (!ObjectUtils.isEmpty(wxOrderRecordServiceOne)) {
                wxStoreDineInOrderClientService.updateOrderRemarkById(wxOrderRecordServiceOne.getOrderGuid(), remark);
            }
        }
    }

    @Override
    public void updateItemRemarkById(String orderGuid, String itemGuid, String remark) {
        WxOrderItemDO orderItemDO = orderItemService.getOne(Wrappers.<WxOrderItemDO>lambdaQuery().eq(WxOrderItemDO::getMerchantGuid, orderGuid)
                .eq(WxOrderItemDO::getItemGuid, itemGuid));
        if (!ObjectUtils.isEmpty(orderItemDO) && null != remark && !remark.equals(orderItemDO.getRemark())) {
            orderItemDO.setRemark(remark);
            wxOrderItemMapper.updateById(orderItemDO);

            //更新交易订单商品备注信息
            WxOrderRecordDO orderRecordServiceOne = wxOrderRecordService.getOne(Wrappers.<WxOrderRecordDO>lambdaQuery()
                    .eq(WxOrderRecordDO::getMerchantGuid, orderItemDO.getMerchantGuid()));
            if (!ObjectUtils.isEmpty(orderRecordServiceOne)) {
                //通过交易订单guid和订单商品guid定位对应商品，进行修改备注
                wxStoreDineInOrderClientService.updateOrderItemRemarkById(orderRecordServiceOne.getOrderGuid(), orderItemDO.getItemGuid(), remark);
            }
        }
    }

    /**
     * 根据订单记录id获取订单详情
     *
     * @param orderRecordGuid 微信订单记录guid
     * @return 订单详情
     */
    @Override
    public List<WxStoreMerchantOrderDTO> getDetailByOrderRecordGuid(String orderRecordGuid) {
        if (ObjectUtils.isEmpty(orderRecordGuid)) {
            throw new BusinessException("orderRecordGuid不能为空");
        }
        List<WxStoreMerchantOrderDO> list = this.list(new LambdaQueryWrapper<WxStoreMerchantOrderDO>()
                .eq(WxStoreMerchantOrderDO::getOrderRecordGuid, orderRecordGuid));
        return wxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(list);
    }

    /**
     * 根据订单Guid查询微信订单金额（接单&未接单）
     *
     * @param orderGuid 订单guid
     * @return 微信订单金额（接单&未接单）
     */
    @Override
    public BigDecimal getWechatOrderFeeByOrderGuid(String orderGuid) {
        List<WxStoreMerchantOrderDO> list = this.list(new LambdaQueryWrapper<WxStoreMerchantOrderDO>()
                .eq(WxStoreMerchantOrderDO::getOrderGuid, orderGuid)
                .eq(WxStoreMerchantOrderDO::getOrderState, 0));
        if (!CollectionUtils.isEmpty(list)) {
            return list.stream()
                    .map(WxStoreMerchantOrderDO::getTotalPrice)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
        }
        log.info("未查询到微信下单信息 orderGuid={}", orderGuid);
        return BigDecimal.ZERO;
    }

    /**
     * 根据订单Guid查询微信订单信息
     *
     * @param orderGuid 订单guid
     * @return 微信订单信息
     */
    @Override
    public WechatOrderInfoDTO getWechatOrderInfoByOrderGuid(String orderGuid) {
        WechatOrderInfoDTO wechatOrderInfoDTO = new WechatOrderInfoDTO();
        BigDecimal wechatOrderFee = getWechatOrderFeeByOrderGuid(orderGuid);
        OrderDTO orderDTO = tradeClientService.findByOrderGuid(orderGuid);
        if (ObjectUtils.isEmpty(orderDTO)) {
            log.warn("未查询到订单信息 orderGuid={}", orderGuid);
            wechatOrderInfoDTO.setWechatOrderFee(wechatOrderFee);
            return wechatOrderInfoDTO;
        }
        wechatOrderInfoDTO.setWechatOrderFee(wechatOrderFee.add(orderDTO.getOrderFee()));
        wechatOrderInfoDTO.setOrderState(orderDTO.getState());
        wechatOrderInfoDTO.setOrderSource(orderDTO.getDeviceType());
        wechatOrderInfoDTO.setOrderGuid(orderDTO.getGuid());
        return wechatOrderInfoDTO;
    }

    /**
     * 根据Guid查询微信订单信息
     *
     * @param guid 订单guid/订单记录guid
     * @return 微信订单信息
     */
    @Override
    public WechatOrderInfoDTO getWechatOrderInfoByGuid(String guid) {
        // 先查询微信表
        List<WxStoreMerchantOrderDO> list = this.list(new LambdaQueryWrapper<WxStoreMerchantOrderDO>()
                .eq(WxStoreMerchantOrderDO::getOrderRecordGuid, guid));
        if (!CollectionUtils.isEmpty(list)) {
            String orderGuid = list.get(0).getOrderGuid();
            OrderDTO orderDTO = tradeClientService.findByOrderGuid(orderGuid);
            if (ObjectUtils.isEmpty(orderDTO)) {
                log.warn("未查询到订单信息，orderGuid={}", orderGuid);
                return null;
            }
            return new WechatOrderInfoDTO().setOrderGuid(orderGuid).setOrderSource(orderDTO.getDeviceType());
        }
        // 如果微信表没有就去查订单表
        WxOrderRecordDO wxOrderRecordDO = wxOrderRecordService.getOne(new LambdaQueryWrapper<WxOrderRecordDO>()
                .eq(WxOrderRecordDO::getGuid, guid));
        if (!ObjectUtils.isEmpty(wxOrderRecordDO)) {
            String orderGuid = wxOrderRecordDO.getOrderGuid();
            if (StringUtils.isEmpty(orderGuid)) {
                log.warn("微信订单记录表里订单guid为空，guid={}", guid);
                return null;
            }
            OrderDTO orderDTO = tradeClientService.findByOrderGuid(orderGuid);
            if (ObjectUtils.isEmpty(orderDTO)) {
                log.warn("未查询到订单信息，orderGuid={}", orderGuid);
                return null;
            }
            return new WechatOrderInfoDTO().setOrderGuid(orderDTO.getGuid())
                    .setOrderSource(orderDTO.getDeviceType());
        }
        log.warn("这个guid有问题，啥都没查到 guid={}", guid);
        return null;
    }

}
