package com.holderzone.saas.store.weixin.entity.query;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreConfigBatchUpdateQuery
 * @date 2019/02/22 16:32
 * @description 批量修改微信门店配置query
 * @program holder-saas-store-weixin
 */
@Data
public class WxStoreConfigBatchUpdateQuery {
	@NotNull
	private List<String> storeGuidList;

	/**
	 * 点餐模式（0正餐，1快餐）
	 */
	private Byte orderModel;

	/**
	 * 接单模式（0手动接单，1首单手动，加菜自动，2无需确认（仅适用于快餐））
	 */
	private Byte takingModel;

	/**
	 * 是否开启线上买单（0关闭，1开启（快餐只能选择开启））
	 */
	private Byte isOnlinePayed;

	/**
	 * 结账模式（0线上支付后，需店员手动结账和清台，1线上支付后自动结账，需店员手动清台，2，自动结账（仅支持快餐））
	 */
	private Byte settleModel;

	private Byte isWeighingOrdered;

	private Byte isRemarked;

	private Byte urlType;

	private Byte menuType;

	private Byte isConfigured;
}
