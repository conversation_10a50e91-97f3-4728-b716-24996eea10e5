package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxReserveConfigDO
 * @date 2019/12/16 17:41
 * @description
 * @program holder-saas-store
 */
@Data
@TableName("hsw_weixin_reserve_config")
public class WxReserveConfigDO {

    private String id;

    @TableId("guid")
    private String guid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    private Boolean isDelete;

    private String storeGuid;

    private Boolean status;

    private Integer reserveType;

    private Integer daysBefore;

    private Double hoursBefore;

    private Integer reserveArea;

    private Integer daysRange;

    /**
     * 通过门店guid初始化预定配置，
     * 无GUID
     *
     * @param storeGuid
     * @return
     */
    public static WxReserveConfigDO DEFAULT_WITH_OUT_GUID(String storeGuid) {
        WxReserveConfigDO wxReserveConfigDO = new WxReserveConfigDO();
        wxReserveConfigDO.setGmtCreate(LocalDateTime.now());
        wxReserveConfigDO.setGmtModified(LocalDateTime.now());
        wxReserveConfigDO.setIsDelete(false);
        wxReserveConfigDO.setStoreGuid(storeGuid);
        wxReserveConfigDO.setStatus(false);
        wxReserveConfigDO.setReserveType(0);
        wxReserveConfigDO.setDaysBefore(0);
        wxReserveConfigDO.setHoursBefore(0D);
        wxReserveConfigDO.setReserveArea(0);
        wxReserveConfigDO.setDaysRange(15);
        return wxReserveConfigDO;
    }
}
