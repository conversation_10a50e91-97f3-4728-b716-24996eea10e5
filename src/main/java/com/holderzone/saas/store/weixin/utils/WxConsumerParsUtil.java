package com.holderzone.saas.store.weixin.utils;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.common.enums.SexEnum;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestOpenMemberWechatInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.constant.ModelName;
import com.holderzone.saas.store.weixin.constant.RedisConstants;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreConsumerMapstruct;
import com.holderzone.saas.store.weixin.service.WxQrCodeInfoService;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.WxUserRecordService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxConsumerParsUtil
 * @date 2019/04/02 20:09
 * @description 微信用户信息转化工具类
 * @program holder-saas-store
 */
@Component
@Slf4j
public class WxConsumerParsUtil {

    private final static String BASE_64_PATTERN = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$";
    @Autowired
    WxQrCodeInfoService wxQrCodeInfoService;
    @Autowired
    WxUserRecordService wxUserRecordService;
    @Autowired
    WxStoreOrderConfigService wxStoreOrderConfigService;
    @Autowired
    OrganizationClientService organizationClientService;
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    DynamicHelper dynamicHelper;
    @Autowired
    WxStoreConsumerMapstruct wxStoreConsumerMapstruct;
    @Autowired
    MemberClientService memberClientService;
    @Resource
    WxStoreSessionDetailsService wxStoreSessionDetailsService;
    @Autowired
    HsaBaseClientService hsaBaseClientService;
    @Autowired
    WeChatConfig weChatConfig;


    //    private Pair<String, WxQrCodeInfoDO> getQrCodeByEventKey(String eventKey) {
//        log.info("eventKey:{}", eventKey);
//        String[] arrayStr = eventKey.split(",");
//        String enterpriseGuid = arrayStr[1];
//
//        // 获取二维码参数
//        WxQrCodeInfoDO wxQrCodeInfoDO = wxQrCodeInfoService.getWxQrCodeInfoByGuid(arrayStr[2]);
//        if (ObjectUtils.isEmpty(wxQrCodeInfoDO)) {
//            throw new BusinessException("当前二维码无对应参数，请重新下载二维码");
//        }
//        log.info("二维码参数：{}", wxQrCodeInfoDO);
//        Pair<String, WxQrCodeInfoDO> pair = new Pair<>(enterpriseGuid, wxQrCodeInfoDO);
//        return pair;
//    }

    public String parse2Consumer(String eventKey, WxMpUser wxMpUser, WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) {
        String[] split = eventKey.split(",");
        String jumpUrl;
        if (!ObjectUtils.isEmpty(split) && split.length == 3) {
            String eventType = split[0].replace("qrscene_", "");
            String enterpriseGuid = split[1];
            dynamicHelper.changeDatasource(enterpriseGuid);
            String eventCode = split[2];
            WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreConsumerMapstruct.wxUser2ConsumerDTO(wxMpUser);
            if (wxStoreAuthorizerInfoDO != null) {
                wxStoreConsumerDTO.setBrandGuid(wxStoreAuthorizerInfoDO.getBrandGuid());
            }
            Integer sex = wxStoreConsumerDTO.getSex();
            // 排队服务与微信公众号定义的性别枚举不一致，手动转，微信： 0、未知，1、男，2、女；排队服务：0、女，1、男，2未知
            if (0 == sex) {
                sex = 2;
            } else if (2 == sex) {
                sex = 0;
            }
            wxStoreConsumerDTO.setSex(sex);
            wxStoreConsumerDTO.setEnterpriseGuid(enterpriseGuid);
            if (Objects.equals(BusinessName.ORDER_CONFIG, eventType)) {
                Boolean flag = getOrderPageUrl(eventCode, wxStoreConsumerDTO, wxStoreAuthorizerInfoDO);
                jumpUrl = weChatConfig.getOrderPageUrl();
                if (!flag) {
                    try {
                        String concat = weChatConfig.getOrderPageUrl().substring(0, weChatConfig.getOrderPageUrl().lastIndexOf("?") + 1).concat("errorMsg=").concat(URLEncoder.encode("当前公众号已授权至其他品牌，请重新生成二维码", "UTF-8"));
                        log.info("当前公众号已授权至其他品牌，请重新生成二维码，跳转页面：{}", concat);
                        return concat;
                    } catch (UnsupportedEncodingException e) {
                        throw new BusinessException("系统异常");
                    }
                }
            } else if (Objects.equals(BusinessName.STORE_LIST, eventType)) {
                jumpUrl = weChatConfig.getShopListPage();
                wxStoreConsumerDTO.setBrandGuid(eventCode);
            } else if (Objects.equals(BusinessName.MEMBER_LOGIN, eventType)||Objects.equals(BusinessName.NEW_MEMBER_LOGIN, eventType)) {
            	/*WechatLoginResultRespDTO loginOrRegister = wxUserRecordService.loginOrRegister(wxStoreConsumerDTO);
				if(!loginOrRegister.isLoginState()) {
					wxStoreConsumerDTO.setIsLogin(false);
				}*/
                wxStoreConsumerDTO.setBrandGuid(eventCode);
                jumpUrl = weChatConfig.getBaseUrl();
            } else if (Objects.equals(BusinessName.MEMBER_BINDING, eventType)) {
                jumpUrl = weChatConfig.getBindingUrl();
            } else {
                log.info("无法识别到当前请求类型, eventCode:{}", eventCode);
                throw new BusinessException("无法识别到当前请求类型");
            }
            if (wxStoreConsumerDTO.getIsLogin() == null) {
                wxStoreConsumerDTO.setIsLogin(false);
            }
            //补充运营主体
            if (Objects.nonNull(wxStoreAuthorizerInfoDO)){
                wxStoreConsumerDTO.setOperSubjectGuid(wxStoreAuthorizerInfoDO.getOperSubjectGuid());
            }
            WxUserRecordDO wxUserRecordDO = wxUserRecordService.saveOrUpdateUserInfo(wxStoreConsumerDTO);
            String consumerInfo = JacksonUtils.writeValueAsString(wxStoreConsumerDTO).trim();
            log.info("微信桌贴扫码用户信息，wxStoreConsumerDTO:{}", wxStoreConsumerDTO);
            String msgKey = redisUtils.generateGuid(redisUtils.keyGenerate(ModelName.WX, wxStoreConsumerDTO.getOpenId(), wxStoreConsumerDTO.getStoreGuid()));
			log.info("msgkey:{}",msgKey);
            redisUtils.setEx(msgKey, consumerInfo,2,TimeUnit.DAYS);
            //executorService.execute(()->
                buildDealSession(wxMpUser,  wxStoreConsumerDTO, wxUserRecordDO, msgKey,wxStoreAuthorizerInfoDO);
            //);

            //创建点餐服务的缓存
            //WxMemberSessionUtil.setMemberSeesion(redisUtils, msgKey, weixinMemberSessionDTO);

            //weixinMemberSessionDTO.set
           // redisUtils.set(key, value);
          //  redisUtils.setEx(wxStoreConsumerDTO.getOpenId(),consumerInfo,60*60*24,consumerInfo);
            String result = String.format(jumpUrl, msgKey, enterpriseGuid);
            log.info("用户信息换取凭证msgKey：{}, 跳转地址：{}", msgKey, result);
            if (!ObjectUtils.isEmpty(wxStoreAuthorizerInfoDO)){
                result = result + "&appid=" + wxStoreAuthorizerInfoDO.getAuthorizerAppid();

            }
            return result;
        }
        log.info("解析失败：eventKey：{}", eventKey);
        return null;
    }

    public String parse2Consumer(String eventKey, WxMpUser wxMpUser, WxAuthorizeReqDTO wxAuthorizeReqDTO) {
        String[] split = eventKey.split(",");
        if (ObjectUtils.isEmpty(split) || split.length != 3) {
            log.info("解析失败：eventKey：{}", eventKey);
            return null;
        }
        String eventType = split[0].replace("qrscene_", "");
        String enterpriseGuid = split[1];
        dynamicHelper.changeDatasource(enterpriseGuid);
        String brandGuid = split[2];
        WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreConsumerMapstruct.wxUser2ConsumerDTO(wxMpUser);
        wxStoreConsumerDTO.setSex(SexEnum.SEX_WEIZHI.getCode());
        wxStoreConsumerDTO.setEnterpriseGuid(enterpriseGuid);
        wxStoreConsumerDTO.setOperSubjectGuid(wxAuthorizeReqDTO.getOperSubjectGuid());
        wxStoreConsumerDTO.setMemberInfoGuid(wxAuthorizeReqDTO.getMemberInfoGuid());
        wxStoreConsumerDTO.setIsLogin(true);
        if (Objects.equals(BusinessName.NEW_MEMBER_LOGIN, eventType)) {
            wxStoreConsumerDTO.setBrandGuid(brandGuid);
        } else {
            log.info("无法识别到当前请求类型, eventType:{}", eventType);
            throw new BusinessException("无法识别到当前请求类型");
        }
        log.info("当前wxStoreConsumerDTO:{}", wxStoreConsumerDTO);
        WxUserRecordDO wxUserRecordDO = wxUserRecordService.saveOrUpdateUserInfo(wxStoreConsumerDTO);
        String msgKey = redisUtils.generateGuid(redisUtils.keyGenerate(ModelName.WX,
                wxStoreConsumerDTO.getOpenId(), wxStoreConsumerDTO.getStoreGuid()));
        log.info("msgKey:{}", msgKey);
        String consumerInfo = JacksonUtils.writeValueAsString(wxStoreConsumerDTO).trim();
        redisUtils.setEx(msgKey, consumerInfo, 2, TimeUnit.DAYS);
        // 存储外部openId、openAppId
        if (StringUtils.isNotEmpty(wxAuthorizeReqDTO.getThirdOpenId())) {
            String thirdRelationCacheKey = String.format(RedisConstants.H5_PERSONAL_WECHAT_THIRD_OPENID_APP_ID_KEY, msgKey);
            redisUtils.setEx(thirdRelationCacheKey, wxAuthorizeReqDTO.getThirdAppId() + "," + wxAuthorizeReqDTO.getThirdOpenId(), 1, TimeUnit.DAYS);
        }
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO();
        wxStoreAuthorizerInfoDO.setOperSubjectGuid(wxAuthorizeReqDTO.getOperSubjectGuid());
        wxStoreAuthorizerInfoDO.setIsAlliance(true);
        buildDealSession(wxMpUser, wxStoreConsumerDTO, wxUserRecordDO, msgKey, wxStoreAuthorizerInfoDO);
        String result = String.format(weChatConfig.getBaseUrl(), msgKey, enterpriseGuid);
        if (StringUtils.isNotEmpty(wxAuthorizeReqDTO.getAppId())) {
            result = result + "&appid=" + wxAuthorizeReqDTO.getAppId();
        }
        log.info("个人中心跳转地址：{}", result);
        return result;
    }

    private void buildDealSession(WxMpUser wxMpUser, WxStoreConsumerDTO wxStoreConsumerDTO, WxUserRecordDO wxUserRecordDO,
                                  String msgKey ,WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) {
        WxUserInfoDTO wxUserInfo = wxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(wxMpUser);
        WxMemberSessionDTO memberSessionDTO = WxMemberSessionUtil.getMemberByOpenId(redisUtils, wxUserInfo.getOpenId());
        if(memberSessionDTO!=null){
            wxUserInfo.setIsLogin(wxStoreConsumerDTO.getIsLogin());
            memberSessionDTO.setWxUserInfoDTO(wxUserInfo);
            //memberSessionDTO.getWxUserInfoDTO().
        }else{
            memberSessionDTO = new WxMemberSessionDTO();
            wxUserInfo.setIsLogin(wxUserRecordDO.getIsLogin());
            wxUserInfo.setSex(wxStoreConsumerDTO.getSex());
            memberSessionDTO.setWxUserInfoDTO(wxUserInfo);
            memberSessionDTO.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
            memberSessionDTO.setBrandGuid(wxStoreConsumerDTO.getBrandGuid());
        }

        if(StringUtils.isEmpty(wxStoreConsumerDTO.getBrandName())){
            BrandDTO brandDetail = wxStoreSessionDetailsService.getBrandDetail(wxStoreConsumerDTO.getBrandGuid());
            if(brandDetail!=null){
                memberSessionDTO.setBrandName(brandDetail.getName());
                memberSessionDTO.setLogUrl(brandDetail.getLogoUrl());
            }
        }
        //透传运营主体信息
        UserContext userContext = UserContextUtils.get();
        userContext.setOperSubjectGuid(wxStoreAuthorizerInfoDO.getOperSubjectGuid());
        userContext.setIsAlliance(wxStoreAuthorizerInfoDO.getIsAlliance());
        UserContextUtils.put(userContext);
        ResponseMemberInfo memberInfo = queryAndSetMemberInfo(wxStoreConsumerDTO.getMemberInfoGuid(), wxUserInfo.getOpenId());
        log.info("buildDealSession首次进入：查询到会员详细{}", memberInfo);

        if(memberInfo!=null&&memberInfo.getPhoneNum()!=null){
            memberSessionDTO.setPhoneNum(memberInfo.getPhoneNum());
        }else{
            memberSessionDTO.setPhoneNum("");
        }

        log.info("member_buildSession,weixinToken:{},weixinMemberSessionDTO:{}", memberSessionDTO);
        WxMemberSessionUtil.setMemberSeesion(redisUtils, msgKey, memberSessionDTO);
    }


    /**
     * 查询并设置会员信息
     */
    private ResponseMemberInfo queryAndSetMemberInfo(String memberInfoGuid, String openId) {
        if (StringUtils.isNotEmpty(memberInfoGuid)) {
            // 如果手动传入了手机号
            UserContext userContext = UserContextUtils.get();
            RequestOpenMemberWechatInfo queryMemberInfo = new RequestOpenMemberWechatInfo();
            queryMemberInfo.setOpenId(openId);
            queryMemberInfo.setOperSubjectGuid(userContext.getOperSubjectGuid());
            queryMemberInfo.setMemberInfoGuid(memberInfoGuid);
            log.info("免密登录会员入参:{}", JacksonUtils.writeValueAsString(queryMemberInfo));
            ResponseMemberInfo memberInfo = hsaBaseClientService.bindMemberWechatInfo(queryMemberInfo).getData();
            log.info("免密登录会员信息:{}", JacksonUtils.writeValueAsString(memberInfo));
            return memberInfo;
        }
        RequestQueryMemberInfo queryMemberInfo = new RequestQueryMemberInfo();
        queryMemberInfo.setOpenId(openId);
        ResponseMemberInfo memberInfo = hsaBaseClientService.getMemberInfo(queryMemberInfo).getData();
        log.info("首次进入：查询到会员详细{}", memberInfo);
        return memberInfo;
    }


    private Boolean getOrderPageUrl(String qrGuid, WxStoreConsumerDTO wxStoreConsumerDTO, WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) {
        WxQrCodeInfoDO wxQrCodeInfoDO = wxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(qrGuid);
        if (ObjectUtils.isEmpty(wxQrCodeInfoDO)) {
            throw new BusinessException("当前二维码无对应参数，请重新下载二维码");
        }
        log.info("二维码参数：{}", wxQrCodeInfoDO);
        if (!ObjectUtils.isEmpty(wxStoreAuthorizerInfoDO)) {
            boolean flag = QrCodeUtil.checkQrCode(wxQrCodeInfoDO.getBrandGuid(), wxStoreAuthorizerInfoDO.getBrandGuid());
            if (!flag) {
                return false;
            }
        }
        wxStoreConsumerDTO.setDiningTableName(wxQrCodeInfoDO.getTableName());
        wxStoreConsumerDTO.setDiningTableGuid(wxQrCodeInfoDO.getTableGuid());
        wxStoreConsumerDTO.setStoreGuid(wxQrCodeInfoDO.getStoreGuid());
        wxStoreConsumerDTO.setStoreName(wxQrCodeInfoDO.getStoreName());
        wxStoreConsumerDTO.setAreaGuid(wxQrCodeInfoDO.getAreaGuid());
        wxStoreConsumerDTO.setAreaName(wxQrCodeInfoDO.getAreaName());
        wxStoreConsumerDTO.setBrandGuid(wxQrCodeInfoDO.getBrandGuid());
        wxStoreConsumerDTO.setTableCode(wxQrCodeInfoDO.getTableName());
        TableDTO tableDTO = new TableDTO();
        tableDTO.setAreaGuid(wxStoreConsumerDTO.getAreaGuid());
        tableDTO.setAreaName(wxStoreConsumerDTO.getAreaName());
        tableDTO.setTableGuid(wxStoreConsumerDTO.getDiningTableGuid());
        tableDTO.setName(wxStoreConsumerDTO.getDiningTableName());
        String brandGuid = wxQrCodeInfoDO.getBrandGuid();
        if (StringUtils.isNotEmpty(brandGuid)) {
            BrandDTO brandDTO = organizationClientService.queryBrandByGuid(brandGuid);
            if (!ObjectUtils.isEmpty(brandDTO)) {
                wxStoreConsumerDTO.setBrandGuid(brandGuid);
                wxStoreConsumerDTO.setBrandName(brandDTO.getName());
                wxStoreConsumerDTO.setBrandLogo(brandDTO.getLogoUrl());
                redisUtils.set("WX-STORE:BRAND:" + wxStoreConsumerDTO.getStoreGuid(), brandDTO);
            }
        }
        redisUtils.set("WX-STORE:WX-TABLE" + tableDTO.getTableGuid(), tableDTO);
        WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
        wxStoreReqDTO.setStoreGuid(wxStoreConsumerDTO.getStoreGuid());
        wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
        return true;
    }

  
    
    public String changeDateSourceByEventKey(String base64EventKey) {
        String eventKey = base64EventKey;
        if (eventKey.matches(BASE_64_PATTERN)) {
            try {
            	 //enterpriseGuid,qrcodeguid 阿里老数据，旧的桌贴
                eventKey = BusinessName.ORDER_CONFIG + "," + new String(Base64.decodeBase64(base64EventKey.getBytes("UTF-8")));
            } catch (UnsupportedEncodingException e) {
                log.error("不支持的编码格式：{}", e);
                return null;
            }
        }
        log.info("eventKey:{}", eventKey);
        String[] arrayStr = eventKey.split(",");
        String enterpriseGuid = arrayStr[1];
        // 手动切库
        dynamicHelper.changeDatasource(enterpriseGuid);
        return eventKey;
    }
}