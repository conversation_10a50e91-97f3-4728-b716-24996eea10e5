package com.holderzone.saas.store.weixin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import org.apache.ibatis.annotations.Delete;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxUserRecordMapper
 * @date 2019/04/02 19:30
 * @description 微信扫码用户信息mapper
 * @program holder-saas-store
 */
@Repository
public interface WxUserRecordMapper extends BaseMapper<WxUserRecordDO> {

    @Delete("DELETE FROM hsw_weixin_user_record WHERE open_id = #{openId} AND oper_subject_guid  IS NULL")
    int deleteUserRecord(String openId);
}
