package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.table.TableLockDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring")

public interface TableLockDTOMapstruct {

	@Mappings({
			@Mapping(target = "tableGuid",source = "wxStoreConsumerDTO.diningTableGuid"),
			@Mapping(target = "orderGuid",source = "tradeOrderGuid"),
			@Mapping(target = "deviceId",source = "wxStoreConsumerDTO.openId"),
			@Mapping(target = "enterpriseGuid",source = "wxStoreConsumerDTO.enterpriseGuid"),
			@Mapping(target = "storeGuid",source = "wxStoreConsumerDTO.storeGuid"),
			@Mapping(target = "storeName",source = "wxStoreConsumerDTO.storeName"),
			@Mapping(target = "enterpriseName",source = "wxStoreConsumerDTO.enterpriseName"),
	})
	TableLockDTO getTableLock(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

}
