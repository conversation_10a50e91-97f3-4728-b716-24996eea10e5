package com.holderzone.saas.store.weixin.service.impl;

import java.time.LocalDateTime;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.weixin.constant.ModelName;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerBusiInfoDO;
import com.holderzone.saas.store.weixin.mapper.WxStoreAuthorizerBusiInfoMapper;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerBusiInfoService;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreAuthorizerBusiInfoServiceImpl
 * @date 2019/02/28 15:28
 * @description 微信授权方功能开启情况Service实现类
 * @program holder-saas-store
 */
@Service
public class WxStoreAuthorizerBusiInfoServiceImpl extends ServiceImpl<WxStoreAuthorizerBusiInfoMapper, WxStoreAuthorizerBusiInfoDO>
        implements WxStoreAuthorizerBusiInfoService {

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public boolean saveOrUpdateStoreAuthorizeBusiInfo(String authorizerAppId, Map<String, Integer> businessInfo) {
        // 使用 authorizerAppId 获取现有的业务信息
        WxStoreAuthorizerBusiInfoDO existingInfo = getOne(
            new LambdaQueryWrapper<WxStoreAuthorizerBusiInfoDO>()
                .eq(WxStoreAuthorizerBusiInfoDO::getAuthorizerAppId, authorizerAppId)
        );

        // 将输入数据转换为领域对象
        WxStoreAuthorizerBusiInfoDO updatedInfo = trans2DO(authorizerAppId, businessInfo);

        if (ObjectUtils.isEmpty(existingInfo)) {
            // 如果没有现有记录，则生成新的 GUID 并设置创建时间
            updatedInfo.setGuid(redisUtils.generateGuid(ModelName.WX + ":" + authorizerAppId + ":BusiInfo"));
            updatedInfo.setGmtCreate(LocalDateTime.now());
        } else {
            // 使用现有的 GUID 进行更新
            updatedInfo.setGuid(existingInfo.getGuid());
        }

        // 保存或更新业务信息
        return saveOrUpdate(updatedInfo);
    }

    private WxStoreAuthorizerBusiInfoDO trans2DO(String authorizerAppId, Map<String, Integer> businessInfo) {
        WxStoreAuthorizerBusiInfoDO infoDO = new WxStoreAuthorizerBusiInfoDO();
        infoDO.setAuthorizerAppId(authorizerAppId);

        // 从 Map 中设置业务信息字段
        infoDO.setIsOpenCard(businessInfo.get("open_card").byteValue());
        infoDO.setIsOpenPay(businessInfo.get("open_pay").byteValue());
        infoDO.setIsOpenScan(businessInfo.get("open_scan").byteValue());
        infoDO.setIsOpenShake(businessInfo.get("open_shake").byteValue());
        infoDO.setIsOpenStore(businessInfo.get("open_store").byteValue());
        infoDO.setIsDeleted(businessInfo.get("is_deleted"));

        return infoDO;
    }

}
