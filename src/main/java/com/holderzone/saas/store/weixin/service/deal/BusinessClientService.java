package com.holderzone.saas.store.weixin.service.deal;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.manage.SurchargeConditionQuery;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @description business服务调用
 * @date 2022/3/25 17:01
 * @className: BusinessClientService
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = BusinessClientService.BusinessFallBack.class)
public interface BusinessClientService {

    /**
     * 通过门店和桌台查询附加费信息
     *
     * @param surchargeDTO 入参
     * @return 附加费信息
     */
    @PostMapping("/surcharge/calculate_surcharge")
    @ApiOperation(value = "通过门店和桌台以及人数计算附加费")
    List<SurchargeLinkDTO> calculateSurcharge(@RequestBody SurchargeCalculateDTO surchargeDTO);

    /**
     * 根据条件查询收费规则
     */
    @PostMapping("/surcharge/list_by_condition")
    List<SurchargeLinkDTO> listSurchargeByCondition(@RequestBody SurchargeConditionQuery query);

    @Component
    class BusinessFallBack implements FallbackFactory<BusinessClientService> {

        private static final Logger logger = LoggerFactory.getLogger(BusinessFallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public BusinessClientService create(Throwable throwable) {
            return new BusinessClientService() {

                @Override
                public List<SurchargeLinkDTO> calculateSurcharge(SurchargeCalculateDTO surchargeDTO) {
                    logger.error(HYSTRIX_PATTERN, "calculateSurcharge", JacksonUtils.writeValueAsString(surchargeDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public List<SurchargeLinkDTO> listSurchargeByCondition(SurchargeConditionQuery query) {
                    logger.error(HYSTRIX_PATTERN, "listByCondition", JacksonUtils.writeValueAsString(query),
                            throwable.getMessage());
                    throw new ServerException();
                }

            };
        }
    }


}
