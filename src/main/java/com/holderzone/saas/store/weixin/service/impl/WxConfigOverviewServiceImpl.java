package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreStatusUpdateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxCouldEditStoreDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreStatusRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxConfigOverviewDO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderConfigDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQueueConfigDO;
import com.holderzone.saas.store.weixin.mapper.WxConfigOverviewMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxConfigOverviewMapstruct;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreConfigMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.weixin.common.BusinessName.WX_STORE_CONFIG;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxConfigOverviewServiceImpl
 * @date 2019/05/09 11:07
 * @description 门店微信功能开启状态Service实现层
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxConfigOverviewServiceImpl extends ServiceImpl<WxConfigOverviewMapper, WxConfigOverviewDO> implements WxConfigOverviewService {

    @Autowired
    WxOrganizationService wxOrganizationService;

    @Autowired
    WxConfigOverviewMapstruct wxConfigOverviewMapstruct;

    @Autowired
    @Lazy
    WxStoreInitService wxStoreInitService;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    OrganizationClientService organizationClientService;

    @Autowired
    WxQueueConfigService wxQueueConfigService;

    @Autowired
    WxStoreConfigMapstruct wxStoreConfigMapstruct;
    @Resource
    private WxStoreOrderConfigService wxStoreOrderConfigService;

    @Override
    public Page<WxStoreStatusRespDTO> pageWxStoreStatus(WxStorePageReqDTO wxStorePageReqDTO) {
        Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> triple = wxOrganizationService.getStoreConfig(wxStorePageReqDTO);
        Page<StoreDTO> storeDTOPage = triple.getLeft();
        List<StoreDTO> storeDTOList = triple.getMiddle();
        Page<WxStoreStatusRespDTO> respDTOPage = new Page<>(storeDTOPage.getCurrentPage(), storeDTOPage.getPageSize(), storeDTOPage.getTotalCount());
        if (!ObjectUtils.isEmpty(storeDTOList)) {
            respDTOPage.setData(getStoreStatusByStoreDTOList(storeDTOList));
        }
        return respDTOPage;
    }

    @Override
    public boolean updateWxStoreStatus(WxStoreStatusUpdateReqDTO wxStoreStatusUpdateReqDTO) {
        if (isForHereStatusEnabled(wxStoreStatusUpdateReqDTO)) {
            validateStoreBrandBinding(wxStoreStatusUpdateReqDTO.getGuid());
        }
        WxConfigOverviewDO wxConfigOverviewDO = wxConfigOverviewMapstruct.wxConfigOverviewDTO2DO(wxStoreStatusUpdateReqDTO);
        boolean updateResult = updateById(wxConfigOverviewDO);
        if (updateResult) {
            updateRedisCache(wxConfigOverviewDO);
        }
        return updateResult;
    }

    private boolean isForHereStatusEnabled(WxStoreStatusUpdateReqDTO wxStoreStatusUpdateReqDTO) {
        return Objects.equals(1, wxStoreStatusUpdateReqDTO.getForHereStatus());
    }

    private void validateStoreBrandBinding(String guid) {
        WxConfigOverviewDO byId = getById(guid);
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(byId.getStoreGuid());
        if (storeDTO == null || StringUtils.isEmpty(storeDTO.getBelongBrandGuid())) {
            throw new BusinessException("当前门店暂未绑定品牌，无法开启点餐功能");
        }
    }

    private void updateRedisCache(WxConfigOverviewDO wxConfigOverviewDO) {
        WxConfigOverviewDO byId = getById(wxConfigOverviewDO.getGuid());
        WxOrderConfigDO one = wxStoreOrderConfigService.getOne(new LambdaQueryWrapper<WxOrderConfigDO>().eq(WxOrderConfigDO::getStoreGuid, byId.getStoreGuid()), false);
        WxOrderConfigDTO wxOrderConfigDTO = wxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(one);
        redisUtils.setEx(redisUtils.keyGenerate(WX_STORE_CONFIG, byId.getStoreGuid()), wxOrderConfigDTO, 24, TimeUnit.HOURS);
    }

    @Override
    public void couldEdit(List<String> storeGuidList, Integer type) {
        if (ObjectUtils.isEmpty(storeGuidList)) {
            throw new BusinessException("判断门店是否可编辑：门店guid不能为空");
        }

        List<WxConfigOverviewDO> configList = list(new LambdaQueryWrapper<WxConfigOverviewDO>()
                .in(WxConfigOverviewDO::getStoreGuid, storeGuidList));

        if (ObjectUtils.isEmpty(configList)) {
            throw new BusinessException("门店微信配置尚未初始化，请先进入概览页面初始化");
        }

        List<String> couldNotEditStoreNames = configList.stream()
                .filter(config -> isStoreOpenForType(config, type))
                .map(config -> wxOrganizationService.getStoreDTOByGuid(config.getStoreGuid()).getName())
                .collect(Collectors.toList());

        if (!ObjectUtils.isEmpty(couldNotEditStoreNames)) {
            throw new BusinessException("请先关闭业务再编辑");
        }
    }

    private boolean isStoreOpenForType(WxConfigOverviewDO config, Integer type) {
        if (Objects.equals(0, config.getIsOpened())) return false;
        switch (type) {
            case 0:
                return Objects.equals(1, config.getForHereStatus());
            case 1:
                return Objects.equals(1, config.getQueueUpStatus());
            case 2:
                return Objects.equals(1, config.getBookingStatus());
            case 3:
                return Objects.equals(1, config.getTakeawayStatus());
            default:
                log.info("无法识别当前type:{}", type);
                return false;
        }
    }

    @Override
    public List<WxStoreStatusRespDTO> listByStoreGuidList(List<String> storeGuidList) {
        if (ObjectUtils.isEmpty(storeGuidList)) return null;
        List<StoreDTO> storeDTOList = organizationClientService.queryStoreByIdList(storeGuidList);
        return ObjectUtils.isEmpty(storeDTOList) ? null : getStoreStatusByStoreDTOList(storeDTOList);
    }

    private List<WxStoreStatusRespDTO> getStoreStatusByStoreDTOList(List<StoreDTO> storeDTOList) {
        List<String> storeGuidList = storeDTOList.stream().map(StoreDTO::getGuid).collect(Collectors.toList());
        List<WxConfigOverviewDO> wxConfigOverviewDOList = list(new LambdaQueryWrapper<WxConfigOverviewDO>().in(WxConfigOverviewDO::getStoreGuid, storeGuidList));
        Map<String, WxConfigOverviewDO> wxConfigOverviewDOMap = wxConfigOverviewDOList.stream().collect(Collectors.toMap(WxConfigOverviewDO::getStoreGuid, Function.identity()));
        List<WxStoreStatusRespDTO> respDTOList = new ArrayList<>();
        List<WxConfigOverviewDO> configOverviewDOS = new ArrayList<>();
        storeDTOList.forEach(storeDTO -> {
            String storeGuid = storeDTO.getGuid();
            WxConfigOverviewDO wxConfigOverviewDO = wxConfigOverviewDOMap.get(storeGuid);
            if (ObjectUtils.isEmpty(wxConfigOverviewDO)) {
                wxConfigOverviewDO = createDefaultWxConfigOverviewDO(storeGuid);
                configOverviewDOS.add(wxConfigOverviewDO);
            }
            WxStoreStatusRespDTO wxStoreStatusRespDTO = wxConfigOverviewMapstruct.wxConfigOverViewDO2DTO(wxConfigOverviewDO);
            wxStoreStatusRespDTO.setBrandNameList(getBrandNames(storeDTO));
            wxStoreStatusRespDTO.setStoreName(storeDTO.getName());
            respDTOList.add(wxStoreStatusRespDTO);
        });
        log.info("门店概览入库信息：{}", JacksonUtils.writeValueAsString(configOverviewDOS));
        saveBatch(configOverviewDOS);
        wxStoreInitService.initWxStore(storeGuidList);
        return respDTOList;
    }

    private WxConfigOverviewDO createDefaultWxConfigOverviewDO(String storeGuid) {
        WxConfigOverviewDO wxConfigOverviewDO = new WxConfigOverviewDO();
        String guid = redisUtils.generateGuid(redisUtils.keyGenerate(BusinessName.WX, BusinessName.OVERVIEW_CONFIG));
        wxConfigOverviewDO.setGuid(guid);
        wxConfigOverviewDO.setStoreGuid(storeGuid);
        wxConfigOverviewDO.setIsOpened(1);
        wxConfigOverviewDO.setForHereStatus(0);
        wxConfigOverviewDO.setQueueUpStatus(0);
        wxConfigOverviewDO.setBookingStatus(0);
        wxConfigOverviewDO.setTakeawayStatus(0);
        return wxConfigOverviewDO;
    }

    private List<String> getBrandNames(StoreDTO storeDTO) {
        List<BrandDTO> brandDTOList = storeDTO.getBrandDTOList();
        return ObjectUtils.isEmpty(brandDTOList) ? new ArrayList<>() : brandDTOList.stream().map(BrandDTO::getName).collect(Collectors.toList());
    }

    @Override
    public List<WxCouldEditStoreDTO> listCouldEditStore(WxStoreReqDTO wxStoreReqDTO) {
        WxStorePageReqDTO wxStorePageReqDTO = createPageRequest(wxStoreReqDTO);
        Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> triple = wxOrganizationService.getStoreConfig(wxStorePageReqDTO);
        List<StoreDTO> storeDTOList = triple.getMiddle();
        LambdaQueryWrapper<WxConfigOverviewDO> wrapper = createWrapper(triple.getRight(), wxStoreReqDTO.getType());
        return getCouldEditStoreList(storeDTOList, wrapper);
    }

    private WxStorePageReqDTO createPageRequest(WxStoreReqDTO wxStoreReqDTO) {
        WxStorePageReqDTO wxStorePageReqDTO = new WxStorePageReqDTO();
        wxStorePageReqDTO.setStoreName(wxStoreReqDTO.getStoreName());
        wxStorePageReqDTO.setPageSize(Integer.MAX_VALUE);
        wxStorePageReqDTO.setCurrentPage(1);
        return wxStorePageReqDTO;
    }

    private LambdaQueryWrapper<WxConfigOverviewDO> createWrapper(List<String> storeGuids, Integer type) {
        LambdaQueryWrapper<WxConfigOverviewDO> wrapper = new LambdaQueryWrapper<WxConfigOverviewDO>()
                .in(WxConfigOverviewDO::getStoreGuid, storeGuids);
        setWrapperCondition(wrapper, type);
        return wrapper;
    }

    private void setWrapperCondition(LambdaQueryWrapper<WxConfigOverviewDO> wrapper, Integer type) {
        switch (type) {
            case 0:
                wrapper.eq(WxConfigOverviewDO::getForHereStatus, 0);
                break;
            case 1:
                wrapper.eq(WxConfigOverviewDO::getQueueUpStatus, 0);
                break;
            case 2:
                wrapper.eq(WxConfigOverviewDO::getBookingStatus, 0);
                break;
            case 3:
                wrapper.eq(WxConfigOverviewDO::getTakeawayStatus, 0);
                break;
            default:
                throw new BusinessException("请求参数有误，请确认后重试");
        }
    }

    private List<WxCouldEditStoreDTO> getCouldEditStoreList(List<StoreDTO> storeDTOList, LambdaQueryWrapper<WxConfigOverviewDO> wrapper) {
        List<WxCouldEditStoreDTO> respList = new ArrayList<>();
        Map<String, WxConfigOverviewDO> wxConfigOverviewDOMap = list(wrapper).stream().collect(Collectors.toMap(WxConfigOverviewDO::getStoreGuid, Function.identity()));
        storeDTOList.forEach(storeDTO -> {
            WxConfigOverviewDO wxConfigOverviewDO = wxConfigOverviewDOMap.get(storeDTO.getGuid());
            if (!ObjectUtils.isEmpty(wxConfigOverviewDO)) {
                WxCouldEditStoreDTO wxCouldEditStoreDTO = new WxCouldEditStoreDTO();
                wxCouldEditStoreDTO.setStoreName(storeDTO.getName());
                wxCouldEditStoreDTO.setStoreGuid(storeDTO.getGuid());
                wxCouldEditStoreDTO.setBrandNames(getBrandGuids(storeDTO));
                respList.add(wxCouldEditStoreDTO);
            }
        });
        return respList;
    }

    private List<String> getBrandGuids(StoreDTO storeDTO) {
        List<BrandDTO> brandDTOList = storeDTO.getBrandDTOList();
        return ObjectUtils.isEmpty(brandDTOList) ? new ArrayList<>() : brandDTOList.stream().map(BrandDTO::getGuid).collect(Collectors.toList());
    }

    @Override
    public Pair<WxStoreInfoDTO, Boolean> isStoreOpen(Integer type, String storeGuid) {
        if (type == null || storeGuid == null) return null;
        WxConfigOverviewDO configOverviewDO = getOne(new LambdaQueryWrapper<WxConfigOverviewDO>().eq(WxConfigOverviewDO::getStoreGuid, storeGuid));
        log.info("获取查询数据库门店配置信息：{}", JacksonUtils.writeValueAsString(configOverviewDO));
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(storeGuid);
        log.info("获取storeDTO信息：{}", JacksonUtils.writeValueAsString(configOverviewDO));
        boolean isInTime = isInTime(storeDTO);
        log.info("获取isInTime是否当前时间：{}", isInTime);
        WxStoreInfoDTO wxStoreInfoDTO = new WxStoreInfoDTO();
        wxStoreInfoDTO.setStoreGuid(storeGuid);
        wxStoreInfoDTO.setStoreName(storeDTO.getName());
        wxStoreInfoDTO.setBrandNameList(getBrandNames(storeDTO));
        wxStoreInfoDTO.setIsOpened(configOverviewDO.getIsOpened() == 1 && isInTime);
        boolean flag = checkStoreOpenStatus(type, configOverviewDO);
        return Pair.of(wxStoreInfoDTO, flag);
    }

    private boolean checkStoreOpenStatus(Integer type, WxConfigOverviewDO configOverviewDO) {
        switch (type) {
            case 0:
                return configOverviewDO.getForHereStatus() == 1;
            case 1:
                return configOverviewDO.getQueueUpStatus() == 1;
            case 2:
                return configOverviewDO.getBookingStatus() == 1;
            case 3:
                return configOverviewDO.getTakeawayStatus() == 1;
            default:
                log.info("无法识别当前type：{}", type);
                return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initWxStore(String storeGuid) {
        WxConfigOverviewDO one = getOne(new LambdaQueryWrapper<WxConfigOverviewDO>().eq(WxConfigOverviewDO::getStoreGuid, storeGuid));
        if (one == null) {
            one = createDefaultWxConfigOverviewDO(storeGuid);
            if (save(one)) {
                wxStoreInitService.initWxStoreByMQ(storeGuid);
            } else {
                log.info("门店【{}】初始化微信配置概览失败", storeGuid);
                throw new BusinessException("微信门店初始化失败");
            }
        }
    }

    @Override
    public boolean whetherQueueConfig(String storeGuid) {
        WxConfigOverviewDO wxConfigOverviewDO = getOne(new LambdaQueryWrapper<WxConfigOverviewDO>().eq(WxConfigOverviewDO::getStoreGuid, storeGuid));
        return wxConfigOverviewDO != null && wxConfigOverviewDO.getQueueUpStatus() == 1 && isQueueOpen(storeGuid);
    }

    private boolean isQueueOpen(String storeGuid) {
        WxQueueConfigDO one = wxQueueConfigService.getOne(storeGuid);
        return one != null && one.getIsQueueOpen() == 1;
    }

    private boolean isInTime(StoreDTO storeDTO) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime businessStart = storeDTO.getBusinessStart() == null ? LocalDateTime.of(LocalDate.now(), LocalTime.MIN) : LocalDateTime.of(LocalDate.now(), storeDTO.getBusinessStart());
        LocalDateTime businessEnd = storeDTO.getBusinessEnd() == null ? LocalDateTime.of(LocalDate.now(), LocalTime.MAX) : LocalDateTime.of(LocalDate.now().plusDays(1L), storeDTO.getBusinessEnd());
        boolean isInTime = now.isAfter(businessStart) && now.isBefore(businessEnd);
        if (!isInTime && businessEnd.isBefore(businessStart)) {
            isInTime = now.isBefore(businessEnd.plusDays(1)) || now.isAfter(businessStart);
        }
        return isInTime;
    }
}
