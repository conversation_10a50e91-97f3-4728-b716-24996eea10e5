package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreWeChatOrderClientService
 * @date 2019/4/8
 */
@Service
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = WxStoreWeChatOrderClientService.WxStoreWeChatOrderFallBack.class)
public interface WxStoreWeChatOrderClientService {

	String URL_PREFIX = "/we_chat";

	@PostMapping(URL_PREFIX+"/get_order_detail_list")
	List<DineinOrderDetailRespDTO> getOrderDetailList(SingleListDTO singleListDTO);

	@PostMapping(URL_PREFIX+"/check_volume")
	DineinOrderDetailRespDTO checkVolume(BillCalculateReqDTO billCalculateReqDTO);

	@PostMapping(URL_PREFIX+"/get_order_details")
	List<DineinOrderDetailRespDTO> getOrderDetails(@RequestBody SingleListDTO singleListDTO);

	@ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
	@PostMapping(URL_PREFIX+"/get_order")
	OrderWechatDTO getOrder(@RequestBody String orderGuid);

	@Slf4j
	@Component
	class WxStoreWeChatOrderFallBack implements FallbackFactory<WxStoreWeChatOrderClientService> {
		@Override
		public WxStoreWeChatOrderClientService create(Throwable throwable) {
			return new WxStoreWeChatOrderClientService() {

				@Override
				public List<DineinOrderDetailRespDTO> getOrderDetailList(SingleListDTO singleListDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
					throw new BusinessException("失败!!" + throwable.getMessage());
				}

				@Override
				public DineinOrderDetailRespDTO checkVolume(BillCalculateReqDTO billCalculateReqDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
					throw new BusinessException("失败!!" + throwable.getMessage());
				}

				@Override
				public List<DineinOrderDetailRespDTO> getOrderDetails(SingleListDTO singleListDTO) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
					throw new BusinessException("失败!!" + throwable.getMessage());
				}

				@Override
				public OrderWechatDTO getOrder(String orderGuid) {
					log.error("查询订单状态失败:{}",orderGuid);
					return new OrderWechatDTO();
				}
			};
		}
	}
}
