package com.holderzone.saas.store.weixin.execption;

import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Data
public class CorrectResult<T> {

	private String errorMsg;

	@ApiModelProperty("0:正确，1：无法查询，2：已结账或作废")
	private Integer result=0;

	private T obj;

	public static CorrectResult<DineinOrderDetailRespDTO> changeFailed(){
		return new CorrectResult().setResult(1).setErrorMsg("订单详情发生变化");
	}

	public static CorrectResult<DineinOrderDetailRespDTO> prohibit(){
		return new <DineinOrderDetailRespDTO>CorrectResult().setResult(1).setErrorMsg("暂未开启线上支付，请联系商家");
	}
}
