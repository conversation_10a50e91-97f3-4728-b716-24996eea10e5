package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreMerchantOrderMapper
 * @date 2019/4/9
 */
@Mapper(componentModel = "spring")
@Component
public interface WxStoreMerchantOrderMapstruct {

    WxStoreMerchantOrderMapstruct INSTANCE = Mappers.getMapper(WxStoreMerchantOrderMapstruct.class);

    WxStoreMerchantOrderDO getWxStoreMerchantOrder(WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO);

    @Mappings({
            @Mapping(target = "phoneNum", source = "phone")
    })
    WxStoreMerchantOrderDTO getWxStoreMerchantOrder(WxStoreMerchantOrderDO wxStoreMerchantOrderDO);

    List<WxStoreMerchantOrderDTO> getWxStoreMerchantOrder(List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS);

    /**
     * 下单时，首次存入订单
     *
     * @param wxStoreAdvanceConsumerReqDTO
     * @param totalPrice
     * @return
     */
    @Mappings({
            @Mapping(target = "totalPrice", source = "totalPrice"),
            @Mapping(target = "nickName", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.nickName"),
            @Mapping(target = "diningTableGuid", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.diningTableGuid"),
            @Mapping(target = "tableCode", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.tableCode"),
            @Mapping(target = "storeGuid", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid"),
            @Mapping(target = "tradeMode", source = "wxStoreAdvanceConsumerReqDTO.orderModel"),
            @Mapping(target = "openId", source = "wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.openId")
    })
    WxStoreMerchantOrderDTO initialWxStoreMerchantOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO, BigDecimal totalPrice);

    @Mappings({
            @Mapping(target = "operationSource", source = "orderSource"),
    })
    WxStoreMerchantOrderDTO padOrderDTO2wxOrderDTO(PadOrderRespDTO padOrderRespDTO);

    List<WxStoreMerchantOrderDTO> padOrderDTOList2wxOrderDTOList(List<PadOrderRespDTO> padOrderRespDTOList);

    WxStoreMerchantOrderDO padOrderDTO2wxOrderDO(PadOrderRespDTO padOrderRespDTO);
}
