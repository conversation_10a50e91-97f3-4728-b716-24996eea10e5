package com.holderzone.saas.store.weixin.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.weixin.service.WxStoreSocketSessionService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description 
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreSocketSessionController
 * @date 2019/5/15
 */
@RestController
@RequestMapping(value = "/wx_store_socket_session")
@Slf4j
@ApiOperation("订单确认长连接服务")
public class WxStoreSocketSessionController {

	private final WxStoreSocketSessionService wxStoreSocketSessionService;

	private final DynamicHelper dynamicHelper;


	@Autowired
	public WxStoreSocketSessionController(WxStoreSocketSessionService wxStoreSocketSessionService, DynamicHelper dynamicHelper) {
		this.wxStoreSocketSessionService = wxStoreSocketSessionService;
		this.dynamicHelper = dynamicHelper;
	}

	@ApiOperation("存储会话")
	@PostMapping(value = "/create")
	public void createSession(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("存储会话wxStoreAdvanceConsumerReqDTO:{}", wxStoreAdvanceConsumerReqDTO);
		wxStoreSocketSessionService.createSession(wxStoreAdvanceConsumerReqDTO);
	}

	@ApiOperation("删除当前桌台会话")
	@PostMapping(value = "/del_session")
	public void delSession(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("删除当前桌台会话wxStoreAdvanceConsumerReqDTO:{}", wxStoreAdvanceConsumerReqDTO);
		wxStoreSocketSessionService.delSession(wxStoreAdvanceConsumerReqDTO);
	}

	@ApiOperation("获取当前桌台所有会话")
	@PostMapping(value = "/find_session")
	public List<WxStoreAdvanceConsumerReqDTO> getWebSocketUser(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("获取当前桌台所有会话wxStoreAdvanceConsumerReqDTO:{}", wxStoreAdvanceConsumerReqDTO);
		return wxStoreSocketSessionService.getSession(wxStoreAdvanceConsumerReqDTO);
	}

	@ApiOperation("获取单个会话")
	@PostMapping(value = "/single_session")
	public WxStoreAdvanceConsumerReqDTO getPersonSocketUser(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("获取单个会话wxStoreAdvanceConsumerReqDTO:{}", wxStoreAdvanceConsumerReqDTO);
		return wxStoreSocketSessionService.getSingleSession(wxStoreAdvanceConsumerReqDTO);
	}

	@ApiOperation("获取当前桌台所有会话")
	@PostMapping(value = "/table_user")
	public List<WxStoreAdvanceConsumerReqDTO> getWebSocketUser(@RequestBody String tableGuid) {
		dynamicHelper.changeDatasource(UserContextUtils.getEnterpriseGuid());
		log.info("获取当前桌台所有会话tableGuid:{}", tableGuid);
		return wxStoreSocketSessionService.getSession(tableGuid);
	}

	@ApiOperation("获取单个会话")
	@PostMapping(value = "/single_user")
	public WxStoreAdvanceConsumerReqDTO getPersonSocketUser(@RequestBody String openId) {
		dynamicHelper.changeDatasource(UserContextUtils.getEnterpriseGuid());
		log.info("获取单个会话openId:{}", openId);
		return wxStoreSocketSessionService.getSingleSession(openId);
	}

}
