package com.holderzone.saas.store.weixin.service.rpc.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardOwnedPage;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardRightDetails;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import com.holderzone.holder.saas.member.wechat.dto.coupon.RequestVolumeStoreAndCard;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeList;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeStoreAndProduct;
import com.holderzone.holder.saas.member.wechat.dto.label.RequestManualLabel;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 微信端会员基础操作FeignClient
 *
 * <AUTHOR>
 * @date 2019/6/25 15:54
 */
@Component
@FeignClient(name = "holder-saas-member-wechat",
        fallbackFactory = HsaBaseClientService.HsaBaseClientServiceFallBack.class)
public interface HsaBaseClientService {

    @ApiOperation(value = "查询会员信息")
    @GetMapping("/hsa-member/getMemberInfo")
    ResponseModel<ResponseMemberInfo> getMemberInfo(@RequestBody RequestQueryMemberInfo requestQueryMemberInfo);


    @ApiOperation(value = "会员绑定微信")
    @PostMapping("/hsa-member/bindMemberWechatInfo")
    ResponseModel<ResponseMemberInfo> bindMemberWechatInfo(@RequestBody RequestOpenMemberWechatInfo memberInfo);

    @ApiOperation(value = "查询会员是否被禁用")
    @GetMapping("/hsa-member/getMemberState")
    ResponseModel<ResponseAccountStatus> getMemberState(@ApiParam(name = "phoneNumOrOpenid", value = "会员电话号码或微信OpenId", required = true) @RequestParam(value = "phoneNumOrOpenid", required = true) String phoneNumOrOpenid,
                                        @ApiParam(name = "enterpriseGuid", value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid);



    @ApiOperation("查询会员优惠券列表")
    @PostMapping(value = "/hsa-member-volume/getMemberVolume")
    ResponseModel<ResponseMemberInfoVolume> getMemberVolume(RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO);

    @ApiOperation("查看优惠券详情")
    @RequestMapping(value = "/hsa-member-volume/getMemberVolumeDetails")
    ResponseModel<ResponseMemberInfoVolumeDetails> getMemberVolumeDetails(@RequestParam("memberVolumeGuid") String memberVolumeGuid);

    @ApiOperation("获取优惠券类型")
    @GetMapping("/hsa-member-volume/getVolumeTypes")
    ResponseModel<List<ResponseMemberSourceType>> getVolumeTypes();

    @ApiOperation("获取优惠券数量")
    @PostMapping(value = "/hsa-member-volume/getMemberVolumeNumber")
    ResponseModel<Integer> getMemberValidVolumeNumber(@RequestBody RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO);

    @ApiOperation("查找会员卡及未开通卡信息")
    @PostMapping(value = "/hsa-card/getMemberCard")
    ResponseModel<ResponseMemberCardAll> getMemberCard(@RequestBody RequestQueryMemberCardList memberCardListQueryReqDTO);

    @ApiOperation("默认卡详细")
    @GetMapping("/hsa-card/default")
    ResponseModel<ResponseMemberCardListOwned> getDefaultCard(@RequestParam("memberInfoGuid") String memberInfoGuid);

    @GetMapping("/hsa-member-volume/consumeVolumeList")
    @ApiOperation(value = "优惠劵的已校验列表---返回集合", response = ResponseVolumeList.class)
    ResponseModel<List<ResponseVolumeList>> consumeVolumeList(@ApiParam("外部订单guid") @RequestParam("orderNumber") String orderNumber);

    @ApiOperation("查询会员卡权益详情")
    @PostMapping("/hsa-member-volume/getCardRightDetails")
    ResponseModel<List<ResponseCardRight>> getCardRightDetails(@RequestBody @Validated RequestCardRightDetails requestCardRightDetails);

    @ApiOperation("分页查找会员卡及未开通个卡信息")
    @PostMapping("/hsa-card/getMemberCardByPage")
    ResponseModel<Page<ResponseMemberCardListOwned>> getMemberCardByPage(@RequestBody @Validated RequestCardOwnedPage memberCardsOwnedPageReqDTO);

    @ApiOperation("查看优惠券详情（特殊API）包含适用门店优惠券")
    @RequestMapping(value = "/hsa-member-volume/getMemberVolumeDetails")
    ResponseModel<WxMemberInfoVolumeDetailsRespDTO> getSpecialMemberVolumeDetails(@RequestParam("memberVolumeGuid") String memberVolumeGuid);

    @ApiOperation(value = "查找会员openid")
    @GetMapping("/hsa-member/findOpenIDByMemberGuid")
    ResponseModel<String> findOpenIDByMemberGuid(@RequestParam("memberGuid") String memberGuid);

    @ApiOperation("开通会员卡（权益卡）")
    @PostMapping("/hsa-member/openMemberCard")
    ResponseModel<Boolean> openMemberCard(@Validated @RequestBody RequestMemberCardOpen requestMemberCardOpen);

    @ApiOperation("获取优惠券支持门店及菜品")
    @PostMapping("/hsa-member-volume/getVolumeStoreAndProduct")
    ResponseModel<List<ResponseVolumeStoreAndProduct>> getVolumeStoreAndProduct(@RequestBody RequestVolumeStoreAndCard volumeGuids);

    @ApiOperation("会员批量打手动标签")
    @PostMapping("/label/batch_add_manual_label")
    void batchAddManualLabel(@Validated @RequestBody RequestManualLabel req);

    @Slf4j
    @Component
    class HsaBaseClientServiceFallBack implements FallbackFactory<HsaBaseClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HsaBaseClientService create(Throwable throwable) {
            return new HsaBaseClientService() {

                @Override
                public ResponseModel<ResponseMemberInfo>  getMemberInfo(RequestQueryMemberInfo requestQueryMemberInfo) {
                    log.error("查询会员信息失败:{}", requestQueryMemberInfo);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberInfo> bindMemberWechatInfo(RequestOpenMemberWechatInfo memberInfo) {
                    log.error("会员绑定微信失败:{}", memberInfo);
                    throw new BusinessException(throwable.getMessage());
                }
                @Override
                public ResponseModel<ResponseAccountStatus> getMemberState(String phoneNumOrOpenid, String enterpriseGuid) {
                    log.error("查询会员是否被禁用失败:phoneNum={},enterpriseGuid={},msg={}", phoneNumOrOpenid,enterpriseGuid,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberInfoVolume> getMemberVolume(RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO) {
                    log.error("查询会员优惠券列表失败:{},msg={}", memberInfoVolumeQueryReqDTO,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberInfoVolumeDetails> getMemberVolumeDetails(String memberVolumeGuid) {
                    log.error("查询会员优惠券详情失败:memberVolumeGuid={},msg={}", memberVolumeGuid,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<List<ResponseMemberSourceType>> getVolumeTypes() {
                    log.error("获取优惠券类型失败:msg={}",throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Integer> getMemberValidVolumeNumber(RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO) {
                    log.error("查询会员优惠券数量失败:memberVolumeGuid={},msg={}", memberInfoVolumeQueryReqDTO,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberCardAll> getMemberCard(RequestQueryMemberCardList memberCardListQueryReqDTO) {
                    log.error("查询会员所有会员卡失败:{}",memberCardListQueryReqDTO);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberCardListOwned> getDefaultCard(String memberInfoGuid) {
                    log.error("默认卡详细失败:memberInfoGuid={}",memberInfoGuid);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<List<ResponseVolumeList>> consumeVolumeList(String orderNumber) {
                    log.error("优惠劵的已校验列表失败:orderNumber={},msg={}",orderNumber,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<List<ResponseCardRight>> getCardRightDetails(RequestCardRightDetails requestCardRightDetails) {
                    log.error("查询会员卡权益详情失败:requestCardRightDetails={},msg={}",requestCardRightDetails,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Page<ResponseMemberCardListOwned>> getMemberCardByPage(RequestCardOwnedPage memberCardsOwnedPageReqDTO) {
                    log.error("分页查找会员卡及未开通个卡信息失败:{},msg={}",memberCardsOwnedPageReqDTO,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<WxMemberInfoVolumeDetailsRespDTO> getSpecialMemberVolumeDetails(String memberVolumeGuid) {
                    log.error("查询会员优惠券详情失败:memberVolumeGuid={},msg={}", memberVolumeGuid,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<String> findOpenIDByMemberGuid(String memberGuid) {
                    log.error("查找会员openid失败:memberGuid={},msg={}", memberGuid,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> openMemberCard(RequestMemberCardOpen requestMemberCardOpen) {
                    log.error("开通会员卡（权益卡）失败:requestMemberCardOpen={},msg={}", requestMemberCardOpen,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<List<ResponseVolumeStoreAndProduct>> getVolumeStoreAndProduct(RequestVolumeStoreAndCard volumeGuids) {
                    log.error("获取优惠券支持门店及菜品失败:requestMemberCardOpen={},msg={}", volumeGuids,throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public void batchAddManualLabel(RequestManualLabel req) {
                    log.info(HYSTRIX_PATTERN, "batchAddManualLabel", req,
                            throwable.getCause());
                    throw new ServerException();
                }

            };
        }
    }
}
