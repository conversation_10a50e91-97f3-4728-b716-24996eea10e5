package com.holderzone.saas.store.weixin.mapstruct;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;

import me.chanjar.weixin.mp.bean.result.WxMpUser;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreConsumerMapstruct
 * @date 2019/04/09 15:38
 * @description 微信扫码用户mapstruct
 * @program holder-saas-store
 */
@Mapper(componentModel = "spring")
@Component
public interface WxStoreConsumerMapstruct {

    @Mappings({
            @Mapping(target = "diningTableGuid", source = "wxQrCodeInfoDO.tableGuid"),
            @Mapping(target = "diningTableName", source = "wxQrCodeInfoDO.tableName"),
            @Mapping(target = "nickName", source = "wxMpUser.nickname")
    })
    WxStoreConsumerDTO parse2ConsumerDTO(WxQrCodeInfoDO wxQrCodeInfoDO, WxMpUser wxMpUser);

    @Mapping(target = "nickName", source = "wxMpUser.nickname")
    WxStoreConsumerDTO wxUser2ConsumerDTO(WxMpUser wxMpUser);
    
    
    WxUserInfoDTO wxMpUser2WeixinUserDTO(WxMpUser wxMpUser);
    @Mapping(target = "nickName", source = "wxMpUser.nickname")
    WxUserRecordDO wxMpUser2WxuserRecord(WxMpUser wxMpUser);
}
