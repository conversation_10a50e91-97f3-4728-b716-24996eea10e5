package com.holderzone.saas.store.weixin.controller;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.weixin.WxPrepayRespDTO;
import com.holderzone.saas.store.dto.weixin.WxZeroPayReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.WeChatH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberModeNotifyReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberTradeNotifyReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.weixin.annotation.DynamicData;
import com.holderzone.saas.store.weixin.entity.dto.WxPayCallbackDTO;
import com.holderzone.saas.store.weixin.event.NotifyMessageQueue;
import com.holderzone.saas.store.weixin.service.WxStorePayService;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStorePayController
 * @date 2019/4/3
 */
@RestController
@RequestMapping(value = "/wx_store_pay")
@Slf4j
public class WxStorePayController {

    private final WxStorePayService wxStorePayService;

    @Resource
    private NotifyMessageQueue notifyMessageQueue;

    @Autowired
    public WxStorePayController(WxStorePayService wxStorePayService) {
        this.wxStorePayService = wxStorePayService;
    }

    @ApiOperation("微信h5支付")
    @PostMapping("/wechat/public")
    @DynamicData(enterpriseGuid = "#wxH5PayReqDTO.wxStoreConsumerDTO.enterpriseGuid", storeGuid = "#wxH5PayReqDTO.wxStoreConsumerDTO.storeGuid")
    //貌似废弃
    public WxPayRespDTO weChatPublic(@RequestBody WxH5PayReqDTO wxH5PayReqDTO) throws UnsupportedEncodingException {
        log.info("微信h5支付入参:{}", wxH5PayReqDTO);
        return wxStorePayService.weChatPublic(wxH5PayReqDTO);
    }

    @ApiOperation("微信公众号支付/微信小程序支付/支付宝小程序支付")
    @PostMapping("/wechat_pay")
    public WxPayRespDTO appletPay(@RequestBody WeChatH5PayReqDTO weChatH5PayReqDTO) {
        if(weChatH5PayReqDTO.getDeviceType() == null){
            throw new BusinessException("支付来源不能为空");
        }
        log.info("{}支付入参:{}", BaseDeviceTypeEnum.getDesc(weChatH5PayReqDTO.getDeviceType()), weChatH5PayReqDTO);
        return wxStorePayService.weChatPay(weChatH5PayReqDTO);
    }

    @ApiOperation("微信支付结果处理")
    @PostMapping("/result_operation")
    public String wxResultOperation(@RequestParam("openId") String openId, @RequestParam("storeGuid") String storeGuid, @RequestParam("allianceId") String allianceId, @RequestBody SaasNotifyDTO saasNotifyDTO) {
        WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO(storeGuid, openId, saasNotifyDTO);
        log.info("业务层微信回调结果入参wxStoreCallbackNotifyDTO:{}", wxStoreCallbackNotifyDTO);
        return wxStorePayService.wxResultOperation(wxStoreCallbackNotifyDTO);
    }

    /**
     * 使用统一的支付结果？？result_operation
     *
     * @param openId
     * @param storeGuid
     * @param allianceId
     * @param saasNotifyDTO
     * @return
     */
    @ApiOperation("聚合支付结果处理")
    @PostMapping("/result_agg_pay")
    public String aggPayResultOperation(@RequestParam("openId") String openId,
                                        @RequestParam("storeGuid") String storeGuid, @RequestParam("allianceId") String allianceId, @RequestBody SaasNotifyDTO saasNotifyDTO) {
        WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO(storeGuid, openId, saasNotifyDTO);
        log.info("业务层微信回调结果入参wxStoreCallbackNotifyDTO:{}", wxStoreCallbackNotifyDTO);
        return wxStorePayService.wxResultOperation(wxStoreCallbackNotifyDTO);
    }

    @ApiOperation(value = "支付", notes = "支付")
    @PostMapping("/member_pay")
    @DynamicData(enterpriseGuid = "#wxMemberPayDTO.enterpriseGuid", storeGuid = "#wxMemberPayDTO.storeGuid")
    public WxStorePayResultDTO memberPay(@RequestBody WxMemberPayDTO wxMemberPayDTO) throws Exception {
        log.info("会员支付入参：{}", JacksonUtils.writeValueAsString(wxMemberPayDTO));
        return wxStorePayService.memberPay(wxMemberPayDTO);
    }


    /**
     * userInfoDTO.getEnterpriseGuid().wxMemberPayDTO.getOpenId()
     * //wxMemberPayDTO.getStoreGuid()wxMemberPayDTO.getStoreName()
     * //calculate.getActuallyPayFee().setScale(2, BigDecimal.ROUND_HALF_UP
     * //UserContextUtils.getStoreGuid()
     * //billPayReqDTO.getOrderGuid()
     *
     * @return
     */
    @ApiOperation(value = "支付完成模板消息推送", notes = "支付完成模板消息推送")
    @PostMapping("/sendWeixinNotifyMessage")
    public boolean sendWeixinNotifyMessage(@RequestBody WxMemberTradeNotifyReqDTO wxMemberTradeNotifyReqDTO) {
        WxMemberModeNotifyReqDTO wxMemberModeNotifyReqDTO = new WxMemberModeNotifyReqDTO();
        wxMemberModeNotifyReqDTO.setWxMemberTradeNotifyReqDTO(wxMemberTradeNotifyReqDTO);
        wxMemberModeNotifyReqDTO.setRemainingTime(System.currentTimeMillis() + 30000L);
        UserContext userContext = UserContextUtils.get();
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid(userContext.getEnterpriseGuid());
        userInfoDTO.setStoreGuid(userContext.getStoreGuid());
        userInfoDTO.setAllianceId(userContext.getAllianceId());
        userInfoDTO.setSource(userContext.getSource());
        wxMemberModeNotifyReqDTO.setUserInfoDTO(userInfoDTO);
        log.info("订单完成微信模板消息：{}", wxMemberTradeNotifyReqDTO);
        notifyMessageQueue.add(wxMemberModeNotifyReqDTO);
        //wxStorePayService.memberModeNotify(wxMemberPayDTO, wxPrepayReqDTO, dineinOrderDetailRespDTO,billPayReqDTO);
        return true;
    }

    @ApiOperation("获取所有支付方式")
    @PostMapping("/pay_way")
    @DynamicData(enterpriseGuid = "#wxStorePayReqDTO.enterpriseGuid", storeGuid = "#wxStorePayReqDTO.storeGuid")
    public WxPayWayRespDTO getAllPayWay(@RequestBody WxStorePayReqDTO wxStorePayReqDTO) {
        log.info("支付方式入参:{}", JacksonUtils.writeValueAsString(wxStorePayReqDTO));
        return wxStorePayService.getAllPayWay(wxStorePayReqDTO);
    }

    @ApiModelProperty(value = "买单支付")
    @PostMapping(value = "/prepay")
    @DynamicData(enterpriseGuid = "#WxPrepayReqDTO.enterpriseGuid", storeGuid = "#WxPrepayReqDTO.storeGuid")
    public WxPrepayRespDTO prepay(@RequestBody WxPrepayReqDTO WxPrepayReqDTO) {
        log.info("买单支付入参:{}", JacksonUtils.writeValueAsString(WxPrepayReqDTO));
        return wxStorePayService.prepay(WxPrepayReqDTO);
    }

    @PostMapping(value = "/validate_order")
    @ApiOperation("订单处理验证")
    @DynamicData(enterpriseGuid = "#wxStorePayReqDTO.enterpriseGuid", storeGuid = "#wxStorePayReqDTO.storeGuid")
    public WxPrepayRespDTO validateOrder(@RequestBody WxStorePayReqDTO wxStorePayReqDTO) {
        log.info("订单验证入参:{}", JacksonUtils.writeValueAsString(wxStorePayReqDTO));
        return wxStorePayService.validateOrder(wxStorePayReqDTO);
    }

    @ApiOperation("会员卡与优惠券选择确认")
    @PostMapping(value = "/prepay_confirm")
    @DynamicData(enterpriseGuid = "#wxPrepayConfirmReqDTO.enterpriseGuid", storeGuid = "#wxPrepayConfirmReqDTO.storeGuid")
    public WxPrepayConfirmRespDTO memberConfirm(@RequestBody WxPrepayConfirmReqDTO wxPrepayConfirmReqDTO) {
        log.info("会员选择确认入参:{}", wxPrepayConfirmReqDTO);
        return wxStorePayService.memberConfirm(wxPrepayConfirmReqDTO);
    }

    @ApiOperation("0元支付")
    @PostMapping("/zero_pay")
    @DynamicData(enterpriseGuid = "#wxZeroPayReqDTO.wxStoreConsumerDTO.enterpriseGuid", storeGuid = "#wxZeroPayReqDTO.wxStoreConsumerDTO.storeGuid")
    public WxStorePayResultDTO zeroPay(@RequestBody WxZeroPayReqDTO wxZeroPayReqDTO) {
        log.info("0元支付入参:{}", wxZeroPayReqDTO);
        return wxStorePayService.zeroPay(wxZeroPayReqDTO);
    }

}
