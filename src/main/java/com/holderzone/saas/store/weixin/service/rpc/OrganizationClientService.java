package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrganizationClientService
 * @date 2019/02/18 10:30
 * @description 门店组织远程调用
 * @program ${MODULE_NAME}
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationClientService.OrganizationFullback.class)
public interface OrganizationClientService {
    @PostMapping("/store/query_store_by_condition")
    Page<StoreDTO> queryStoreByCondition(QueryStoreDTO queryStoreDTO);

    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/brand/query_list")
    List<BrandDTO> queryBrandList();

    @PostMapping("/brand/query_brand_by_guid")
    BrandDTO queryBrandByGuid(@RequestParam("brandGuid") String brandGuid);

    @GetMapping("/brand/query_default_brandGuid")
    BrandDTO queryDefaultBrand();

    @PostMapping("/store/query_store_by_city_and_brand")
    List<StoreDTO> queryStoreByCityAndBrand(@RequestBody StoreDTO storeDTO);

    @PostMapping("/store/query_store_by_idlist")
    List<StoreDTO> queryStoreByIdList(List<String> storeGuidList);

    @PostMapping("/store/parse_by_condition")
    List<String> parseByCondition(@RequestBody StoreParserDTO storeParserDTO);

    @ApiOperation(value = "根据门店guid查询门店关联的品牌信息", notes = "若门店未关联到品牌则返回为null，后期一个门店可关联多个品牌")
    @PostMapping("/store/query_brand_by_storeguid")
    BrandDTO queryBrandByStoreGuid(@RequestParam(value = "storeGuid") String storeGuid);

    /**
     * 根据门店guid查询主机deviceId
     *
     * @param storeGuid 门店guid
     * @return 门店下的主机信息（一体机）
     */
    @GetMapping("/device/get_master_device_by_storeguid/{storeGuid}")
    StoreDeviceDTO findMasterDevice(@PathVariable("storeGuid") String storeGuid);

    /**
     * 查询门店桌台对应设备信息
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guid
     * @return 设备信息
     */
    @ApiOperation(value = "查询门店桌台对应设备信息")
    @PostMapping(value = "/device/query_device_by_store_table")
    StoreDeviceDTO queryDeviceByStoreTable(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 查询门店桌台对应设备信息列表
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guidList
     * @return 设备信息列表
     */
    @ApiOperation(value = "查询门店桌台对应设备信息列表")
    @PostMapping(value = "/device/list_device_by_store_table")
    List<StoreDeviceDTO> listDeviceByStoreTable(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 查询门店品牌信息
     * @param storeGuid 门店guid
     * @param brandGuid 品牌guid
     * @return 门店品牌信息
     */
    @PostMapping("/store/query_store_brand_by_guid")
    BrandStoreDetailDTO queryStoreBrandDetail(@RequestParam("storeGuid") String storeGuid,@RequestParam("brandGuid") String brandGuid);

    @Component
    @Slf4j
    class OrganizationFullback implements FallbackFactory<OrganizationClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrganizationClientService create(Throwable throwable) {
            return new OrganizationClientService() {
                @Override
                public Page<StoreDTO> queryStoreByCondition(QueryStoreDTO queryStoreDTO) {
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    throw new RuntimeException("通过门店guid查询门店失败,e:{}" + throwable.getMessage());
                }

                @Override
                public List<BrandDTO> queryBrandList() {
                    throw new RuntimeException("获取品牌列表失败,e:{}" + throwable.getMessage());
                }

                @Override
                public BrandDTO queryBrandByGuid(String brandGuid) {
                    throw new RuntimeException("获取品牌列表失败,e:{}" + throwable.getMessage());
                }

                @Override
                public BrandDTO queryDefaultBrand() {
                    log.error(HYSTRIX_PATTERN, "queryDefaultBrand", "",
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询默认品牌接口熔断");
                }

                @Override
                public List<StoreDTO> queryStoreByCityAndBrand(StoreDTO storeDTO) {
                    throw new RuntimeException("通过品牌和城市查询门店列表失败,e:{}" + throwable.getMessage());
                }

                @Override
                public List<StoreDTO> queryStoreByIdList(List<String> storeGuidList) {
                    throw new RuntimeException("通过guidList查询门店信息失败,e:{}" + throwable.getMessage());
                }

                @Override
                public List<String> parseByCondition(StoreParserDTO storeParserDTO) {
                    throw new RuntimeException("转换门店信息失败,e:{}" + throwable.getMessage());
                }

                @Override
                public BrandDTO queryBrandByStoreGuid(String storeGuid) {
                    throw new RuntimeException("查询品牌失败,e:{}" + throwable.getMessage());
                }

                @Override
                public StoreDeviceDTO findMasterDevice(String storeGuid) {
                    throw new RuntimeException("查询品牌失败,e:{}" + throwable.getMessage());
                }

                @Override
                public StoreDeviceDTO queryDeviceByStoreTable(PadOrderTypeReqDTO padOrderTypeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryDeviceByStoreTable", JacksonUtils.writeValueAsString(padOrderTypeReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询门店桌台对应设备信息接口熔断");
                }

                @Override
                public List<StoreDeviceDTO> listDeviceByStoreTable(PadOrderTypeReqDTO padOrderTypeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "listDeviceByStoreTable", JacksonUtils.writeValueAsString(padOrderTypeReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询门店桌台对应设备信息列表接口熔断");
                }

                @Override
                public BrandStoreDetailDTO queryStoreBrandDetail(String storeGuid, String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "queryStoreBrandDetail", JacksonUtils.writeValueAsString(storeGuid),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询门店品牌信息接口熔断");
                }
            };
        }
    }
}
