package com.holderzone.saas.store.weixin.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.cmember.app.dto.common.DishInfoDTO;
import com.holderzone.holder.saas.cmember.app.dto.order.request.DiscountReqDTO;
import com.holderzone.holder.saas.cmember.app.dto.order.response.DiscountRespDTO;
import com.holderzone.holder.saas.weixin.entry.dto.*;
import com.holderzone.holder.saas.weixin.utils.BigDecimalUtil;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.PricePairDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoAttrGroupDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoSubSkuDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoSubgroupDTO;
import com.holderzone.saas.store.dto.weixin.resp.ItemImgDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.weixin.constant.UpperStateEnum;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderItemDO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.utils.OrderItemTransfUtils;
import com.holderzone.saas.store.weixin.utils.OrderUtils;
import com.holderzone.saas.store.weixin.utils.PriceCalculationUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class OrderDetailHelper {


    static Logger logger = LoggerFactory.getLogger(OrderDetailHelper.class);
    //trade 订单数据
    DineinOrderDetailRespDTO orderDetailTrade;
    //是否为查询预支付信息，是否合并桌台、批次数据 true为真实桌台数据 false为一个
    boolean realTable;

    //前端数据
    OrderDetailDTO orderDetailDTO;
    @ApiModelProperty(value = "订单所有的批次map,key为wxBatch， 只有正餐会有多个批次",hidden = true)
    Map<String, OrderBatchItemDTO> batchDineItemMap;

    DiscountReqDTO discountReqDTO = new DiscountReqDTO();

    WxUserRecordDO wxUserRecordDO;

    Map<String,WxUserRecordDO> wxUserRecordMap = new HashMap<>();

    /**
     * 订单所有的item,key为订单项表中的guid，非itemguid
     */
    Map<String,DineItemDTO> dineOrderItemMap = new HashMap<>();

    Map<String, OrderTableItemDTO> orderTableItemMap = new HashMap<>();

    OrderTableItemDTO mainOrderTableItemDTO = null;

    /**
     * 订单所有的item,key商品itemguid
     */
    Map<String,List<DineItemDTO>> itemMap = new HashMap<>();

    List<WxOrderItemDO> wxOrderItemDOList = null;
    DiscountRespDTO memberDiscountDTO;
    String memberInfoCardGuid;

    BigDecimal totelPrice = BigDecimal.ZERO;
    BigDecimal saleTotalPrice = BigDecimal.ZERO;

    boolean enableMemberPrice;

    /**订单状态
     * 前端4种状态 0待确认(待接单,快餐不存在)，1已接单，2已完成，3已取消
     * */
    private Integer orderState;
    Map<String,WxStoreTableCombineDTO>  tableMap = new HashMap<>();
    //需要查询图片的itemGuid列表
    Set<String> itemGuidImgSet = new HashSet<>();
    //需要查询图片的itemGuid列表
    List<ItemImgDTO> ItemImgDTOs;
    WxOrderRecordDO orderRecord;
    String brandName ;
    TableDTO tableDTO;

    Integer wxOrderstatus;


    BigDecimal  grouponCouponFee = BigDecimal.ZERO;


    BigDecimal goodsCouponFee = BigDecimal.ZERO;

    public OrderDetailHelper(WxOrderRecordDO orderRecord, Boolean enableMemberPrice,
                             String memberInfoCardGuid
    ) {
        this.orderRecord = orderRecord;
        this.orderDetailDTO = OrderDetailDTO.builder().build();
        orderDetailDTO.setStoreGuid(orderRecord.getStoreGuid());

        orderDetailDTO.setGmtCreate(orderRecord.getGmtCreate());
        this.batchDineItemMap = new LinkedHashMap<>();
        List<DishInfoDTO> dishInfoDTOList = new ArrayList<>();
        discountReqDTO.setDishInfoDTOList(dishInfoDTOList);
        discountReqDTO.setPayMoney(BigDecimal.ZERO);
        discountReqDTO.setTotalMoney(BigDecimal.ZERO);
        discountReqDTO.setStoreGuid(orderRecord.getStoreGuid());
        this.memberInfoCardGuid = memberInfoCardGuid;
        this.enableMemberPrice = enableMemberPrice==null?false:enableMemberPrice;
        this.orderDetailDTO.setGmtCreate(orderRecord.getGmtCreate());
        //orderDetailDTO.setOrderState();
        //this.orderDetailDTO
    }
    public OrderDetailHelper(WxOrderRecordDO orderRecord, DineinOrderDetailRespDTO orderDetailResp, boolean prePayPage, boolean enableMemberPrice, String memberInfoCardGuid) {
        this.orderDetailTrade = orderDetailResp;
        //orderDetailTrade.getState()
        this.realTable = prePayPage;
        this.orderDetailDTO = OrderDetailDTO.builder().build();
        this.orderRecord = orderRecord;
        this.orderDetailDTO.setGmtCreate(orderRecord.getGmtCreate());
        batchDineItemMap = new LinkedHashMap<>();
        List<DishInfoDTO> dishInfoDTOList = new ArrayList<>();
        discountReqDTO.setDishInfoDTOList(dishInfoDTOList);
        discountReqDTO.setPayMoney(BigDecimal.ZERO);
        discountReqDTO.setTotalMoney(BigDecimal.ZERO);
        discountReqDTO.setStoreGuid(orderRecord.getStoreGuid());
        this.memberInfoCardGuid = memberInfoCardGuid;
        this.enableMemberPrice = enableMemberPrice;
        orderDetailDTO.setStoreName("");
    }

    /**
     * 微信端的批次
     * @param wxOrderItemDOList
     */
    public void addWxOrderItemDOs(List<WxOrderItemDO> wxOrderItemDOList) {
        if(CollectionUtils.isEmpty(wxOrderItemDOList)) {
            return;
        }
        if(this.wxOrderItemDOList==null) {
            this.wxOrderItemDOList =wxOrderItemDOList;
        }else {
            if(this.orderRecord.getOrderMode().equals(1)){
                //快餐
                return;
            }
            this.wxOrderItemDOList.addAll(wxOrderItemDOList);
        }

        for(WxOrderItemDO item:this.wxOrderItemDOList){
            String batchkey = item.getMerchantGuid();
            OrderBatchItemDTO orderBatchItemDTO = batchDineItemMap.get(batchkey);

            if(orderBatchItemDTO.getState().equals(1)){
                continue;
            }
            if(orderBatchItemDTO.getState().equals(2)){
                continue;
            }

            List<DineItemDTO> dineItemDTOs = orderBatchItemDTO.getDineItemDTOs();
            DineItemDTO dineItem = transf(item);
            dineOrderItemMap.put(item.getGuid(), dineItem);
            dineItemDTOs = Optional.ofNullable(dineItemDTOs).orElse(new ArrayList<>());
            dineItemDTOs.add(dineItem);
            orderBatchItemDTO.setDineItemDTOs(dineItemDTOs);
            if(orderBatchItemDTO.getState()!=null){
                int state = orderBatchItemDTO.getState().intValue();
                if(state==1) {
                    //本地批次接单才计入总价
                    this.totelPrice = this.totelPrice.add(dineItem.getTotlePrice());
                    if(dineItem.getHasMemberPrice()){
                        this.saleTotalPrice = this.saleTotalPrice.add(dineItem.getSaleTotlePrice());
                    }else{
                        this.saleTotalPrice = this.saleTotalPrice.add(dineItem.getTotlePrice());
                    }
                    continue;
                }
                if(state==0) {
                    transfAndAddDish(item);
                }

            }
            //待接单 放入会员卡折扣优惠计算
        }
    }
    private void transfAndAddDish(WxOrderItemDO item) {
        if(!StringUtils.hasText(this.memberInfoCardGuid)) {
            return;
        }
        DishInfoDTO transfDish = transfDish(item);
        addDishInfoDTO(transfDish);
    }

    public void transfDishAndAdd(DineInItemDTO dineInItemDTO) {
        if(!StringUtils.hasText(this.memberInfoCardGuid)) {
            return;
        }
        if(dineInItemDTO.getCurrentCount().intValue()!=0) {
            DishInfoDTO dishInfoDTO = convertFromDineInItemDTO(dineInItemDTO);
            addDishInfoDTO(dishInfoDTO);
        }
    }









    private DishInfoDTO transfDish(WxOrderItemDO item) {
        DishInfoDTO dishInfoDTO = new DishInfoDTO();
        dishInfoDTO.setOrderItemGuid(item.getGuid());
        dishInfoDTO.setDishGuid(item.getItemGuid());
        dishInfoDTO.setDishName(item.getItemName());
        dishInfoDTO.setDishSpecification(item.getSkuGuid());
        dishInfoDTO.setDishUnit(item.getSkuUnit());
        //9.21早与蔡商议，因加商品券增加赠送数量GiftDishNum字段，原DishNum字段仍然传当前数量与赠送数量之和
        //无赠品
        dishInfoDTO.setDishNum(item.getCurrentCount());
        dishInfoDTO.setGiftDishNum(BigDecimal.ZERO);
        dishInfoDTO.setMainGoodGuid(null);
        dishInfoDTO.setIsMainGood(1);
        dishInfoDTO.setSurcharge(BigDecimal.ZERO);
        dishInfoDTO.setRemark(item.getRemark());
        dishInfoDTO.setDishType(item.getItemType());
        //dishInfoDTO.setSubtotal(dineInItemDTO.getItemPrice());
        dishInfoDTO.setDishOriginalUnitPrice(item.getSkuPrice());
        dishInfoDTO.setDishSellUnitPrice(item.getSkuPrice());
        BigDecimal memberPrice = item.getMemberPrice();
        BigDecimal originalPrice = item.getOriginalPrice();
        if(enableMemberPrice&&memberPrice!=null) {
            dishInfoDTO.setPayPrice(memberPrice);
        }else {
            dishInfoDTO.setPayPrice(originalPrice);
        }
        dishInfoDTO.setSubtotal(dishInfoDTO.getPayPrice());
        return dishInfoDTO;
    }


    private void addDishInfoDTO(DishInfoDTO transfDish) {
        discountReqDTO.setPayMoney(discountReqDTO.getPayMoney().add(transfDish.getPayPrice()));
        discountReqDTO.getDishInfoDTOList().add(transfDish);

    }


    public void structTrade() {
        orderDetailDTO.setTradeMode(this.orderRecord.getOrderMode());
        if(orderDetailTrade==null) {
            OrderTableItemDTO orderTableItemDTO = OrderTableItemDTO.builder().build();
            orderTableItemDTO.setTableGuid(tableDTO.getTableGuid());
            orderTableItemDTO.setTableCode(tableDTO.getCode());
            orderTableItemDTO.setAreaName(tableDTO.getAreaName());
            orderTableItemDTO.setDiningTableName(tableDTO.getName());
            this.orderTableItemMap.put(tableDTO.getTableGuid(),orderTableItemDTO);
            return;
        }
        orderDetailDTO.setCheckoutTime(orderDetailTrade.getCheckoutTime());
        // 实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））
        orderDetailDTO.setPayAmount(orderDetailTrade.getActuallyPayFee());
        if (Objects.isNull(orderDetailTrade.getDiscountFee())) {
            orderDetailTrade.setDiscountFee(BigDecimal.ZERO);
        }
        // 未支付
        if (TradeModeEnum.FAST.getCode() == orderDetailDTO.getTradeMode() &&
                (orderDetailTrade.getState() == StateEnum.READY.getCode() || orderDetailTrade.getState() == StateEnum.CANCEL.getCode())) {
            orderDetailDTO.setDeprecPayAmount(orderDetailTrade.getOrderFee().subtract(orderDetailTrade.getDiscountFee()));
            orderDetailDTO.setPayAmount(orderDetailDTO.getDeprecPayAmount());
        }
        orderDetailDTO.setGuestCount(orderDetailTrade.getGuestCount());
        orderDetailDTO.setStoreName(orderDetailTrade.getStoreName());
        orderDetailDTO.setMark(orderDetailTrade.getMark());
        orderDetailDTO.setOrderNo(orderDetailTrade.getOrderNo());
        orderDetailDTO.setRemark(orderDetailTrade.getRemark());
        //0:无并单，1:主单， 2:子单
        if (orderDetailTrade.getUpperState().equals(1)) {
            orderDetailDTO.setOrderGuid(orderDetailTrade.getMainOrderGuid());
        } else {
            orderDetailDTO.setOrderGuid(orderDetailTrade.getGuid());
        }
        // 构建订单支付方式
        buildPayWay(orderDetailTrade.getActuallyPayFeeDetailDTOS());
        // 预定金
        if (orderDetailTrade.getReserveFee() != null && orderDetailTrade.getReserveFee().compareTo(BigDecimal.ZERO) > 0) {
            orderDetailDTO.setReserveFee(orderDetailTrade.getReserveFee());
            if (orderDetailTrade.getActuallyPayFee() != null) {
                orderDetailDTO.setReserveFeeChange(orderDetailTrade.getActuallyPayFee().subtract(orderDetailTrade.getReserveFee()));
            }
        }
        // 结算项
        List<SettlementItemDTO> settlementItemDTOs = orderDetailDTO.getSettlementItemDTOs();
        if (settlementItemDTOs == null) {
            settlementItemDTOs = new ArrayList<>();
            orderDetailDTO.setSettlementItemDTOs(settlementItemDTOs);
        }
        if (orderDetailTrade.getAppendFee() != null && orderDetailTrade.getAppendFee().compareTo(BigDecimal.ZERO) > 0) {
            SettlementItemDTO appSettlement = new SettlementItemDTO("附加费", orderDetailTrade.getAppendFee(), 1, 1);
            settlementItemDTOs.add(appSettlement);
        }
        List<DiscountFeeDetailDTO> discountFeeDetailList = Optional.ofNullable(orderDetailTrade.getDiscountFeeDetailDTOS()).orElse(Collections.emptyList());
        List<SettlementItemDTO> settlementItemList = buildSettlementItemList(discountFeeDetailList);
        if (!CollectionUtils.isEmpty(settlementItemList)) {
            settlementItemDTOs.addAll(settlementItemList);
        }
        // 附加费明细
        orderDetailDTO.setAppendFeeDetailList(buildAppendFeeDetails());
        //构建桌台
        buildTableItems(orderDetailTrade, realTable);
    }

    /**
     * 构建订单支付方式
     */
    private void buildPayWay(List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailList) {
        if (CollectionUtils.isEmpty(actuallyPayFeeDetailList)) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : actuallyPayFeeDetailList) {
            logger.info("支付项数据：{}", actuallyPayFeeDetailDTO);
            if (actuallyPayFeeDetailDTO.getAmount() != null && actuallyPayFeeDetailDTO.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            String paymentTypeName = actuallyPayFeeDetailDTO.getPaymentTypeName();
            if (sb.length() > 0) {
                sb.append("、");
            }
            sb.append(paymentTypeName);
        }
        orderDetailDTO.setPayWay(sb.toString());
    }


    /**
     * 构建结算项优惠
     */
    private List<SettlementItemDTO> buildSettlementItemList(List<DiscountFeeDetailDTO> discountFeeDetailList) {
        List<SettlementItemDTO> settlementItemList = Lists.newArrayList();
        //状态(1：未结账， 2：已结账， 3：已退款，4：已作废)
        int state = orderDetailTrade.getState();
        for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailList) {
            if (discountFeeDetailDTO == null || discountFeeDetailDTO.getDiscountFee() == null
                    || discountFeeDetailDTO.getDiscountFee().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            if (state != 1) {
                SettlementItemDTO dto = transf(discountFeeDetailDTO);
                BigDecimal moneyAmount = dto.getMoneyAmount();
                if (moneyAmount.compareTo(BigDecimal.ZERO) < 0) {
                    moneyAmount = moneyAmount.multiply(BigDecimal.valueOf(-1));
                    dto.setMoneyAmount(moneyAmount);
                    dto.setIoType(1);
                }
                dto.setType(0);
                settlementItemList.add(dto);
                continue;
            }
            SettlementItemDTO settlementItemDTO = settlementDiscountFee(discountFeeDetailDTO);
            if (Objects.nonNull(settlementItemDTO)) {
                settlementItemList.add(settlementItemDTO);
            }
        }
        return settlementItemList;
    }


    /**
     * 设置订单各个优惠金额
     */
    private SettlementItemDTO settlementDiscountFee(DiscountFeeDetailDTO discountFeeDetailDTO) {
        // @see DiscountTypeEnum
        // 1-会员折扣，2-整单折扣，3-整单让价,4-系统省零，5-赠送优惠，6-团购验券，7-会员优惠券,8-积分抵扣
        int discountType = discountFeeDetailDTO.getDiscountType();
        if (discountType == DiscountTypeEnum.SINGLE_MEMBER.getCode()) {
            return null;
        } else if (discountType == DiscountTypeEnum.MEMBER_GROUPON.getCode()) {
            orderDetailDTO.setCouponFee(orderDetailDTO.getCouponFee().add(discountFeeDetailDTO.getDiscountFee()));
            return null;
        } else if (discountType == DiscountTypeEnum.GOODS_GROUPON.getCode()) {
            orderDetailDTO.setCouponFee(orderDetailDTO.getCouponFee().add(discountFeeDetailDTO.getDiscountFee()));
            goodsCouponFee = discountFeeDetailDTO.getDiscountFee();
            return null;
        } else if (discountType == DiscountTypeEnum.MEMBER.getCode()
                || discountType == DiscountTypeEnum.POINTS_DEDUCTION.getCode()) {
            orderDetailDTO.setMemberCardFee(orderDetailDTO.getMemberCardFee().add(discountFeeDetailDTO.getDiscountFee()));
            return null;
        } else if (discountType == DiscountTypeEnum.SYSTEM.getCode()) {
            BigDecimal saveZero = discountFeeDetailDTO.getDiscountFee();
            logger.info("系统省0: discountFeeDetailDTO:{}", discountFeeDetailDTO);
            int savingZeroType = 0; //0-  1+
            if (saveZero.compareTo(BigDecimal.ZERO) < 0) { //-0.86
                saveZero = discountFeeDetailDTO.getDiscountFee().multiply(BigDecimal.valueOf(-1L));
                savingZeroType = 1;
            }
            orderDetailDTO.setSavingZero(saveZero);
            orderDetailDTO.setSavingZeroType(savingZeroType);
            return null;
        } else if (discountType == DiscountTypeEnum.GROUPON.getCode()) {
            // GROUPON(6, "团购验券", 1),
            grouponCouponFee = discountFeeDetailDTO.getDiscountFee();
        }
        return transf(discountFeeDetailDTO);
    }


    private void buildTableItems(DineinOrderDetailRespDTO orderDetailResp,boolean realTable) {
        //并和到一个桌台
        //子桌列表
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetailResp.getSubOrderDetails();
        subOrderDetails = Optional.ofNullable(subOrderDetails).orElse(Collections.emptyList());
        //主桌商品
        List<DineInItemDTO> dineInItemDTOS = Optional.ofNullable(orderDetailResp.getDineInItemDTOS()).orElse(new ArrayList<>());

        if (realTable) {
            //真实桌台数据
            buildRealTableItems(orderDetailResp);
            return;
        }
        //合并为单个桌台 主桌台和商品项
        OrderTableItemDTO tableItem = transfTable(orderDetailResp,realTable);
        //setLocalTableDTO(tableDTO);
        Set<OrderBatchItemDTO> orderBatchDTOs = tableItem .getOrderBatchDTOset();
        OrderBatchItemDTO batchItem = orderBatchDTOs.iterator().next();
        List<DineItemDTO> dineItemDTOs = batchItem.getDineItemDTOs();
        this.orderTableItemMap.put(tableItem.getTableGuid(),tableItem);
        int guestCount = orderDetailResp.getGuestCount();
        //构建子桌商品
        for(DineinOrderDetailRespDTO subOrder:subOrderDetails) {
            //
            guestCount =subOrder.getGuestCount()+guestCount;
            //子桌商品
            List<DineInItemDTO> subDineInItemDTOS = subOrder.getDineInItemDTOS();
            logger.info("添加子单批次商品,subDineInItemDTOS:{}",subDineInItemDTOS);
            if(CollectionUtils.isEmpty(subDineInItemDTOS)) {
                continue;
            }
            dineInItemDTOS.addAll(subDineInItemDTOS);
            for(DineInItemDTO dineInItemDTO :subDineInItemDTOS) {
                //构建赠品
                DineItemDTO itemGift = transfItem(dineInItemDTO,true);
                //构建非赠品
                DineItemDTO item = transfItem(dineInItemDTO,false);
                if (item!=null) {
                    dineItemDTOs.add(item);
                    addItem(item);
                }
                if (itemGift!=null) {
                    dineItemDTOs.add(itemGift);
                    addItem(itemGift);
                }
            }
        }
        tableItem.setGuestCount(guestCount);
    }

    private void buildRealTableItems(DineinOrderDetailRespDTO orderDetailResp) {
        //子桌列表
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetailResp.getSubOrderDetails();
        subOrderDetails = Optional.ofNullable(subOrderDetails).orElse(Collections.emptyList());
        //子单列表
        List<DineinOrderDetailRespDTO> otherOrderDetails = orderDetailResp.getOtherOrderDetails();
        otherOrderDetails = Optional.ofNullable(otherOrderDetails).orElse(Collections.emptyList());
        //真实桌台数据
        List<DineinOrderDetailRespDTO> mergerRespList = new ArrayList<>();
        mergerRespList.add(orderDetailResp);
        if (!CollectionUtils.isEmpty(subOrderDetails)) {
            mergerRespList.addAll(subOrderDetails);
        }
        if (!CollectionUtils.isEmpty(otherOrderDetails)) {
            mergerRespList.addAll(otherOrderDetails);
        }
        for (int i = 0; i < mergerRespList.size(); i++) {
            OrderTableItemDTO tableItemThis = transfTable(mergerRespList.get(i), realTable);
            logger.info("trade_table:{}", JSON.toJSONString(tableItemThis));
            this.orderTableItemMap.put(tableItemThis.getTableGuid(), tableItemThis);
            if (tableItemThis.getMainTable().equals(0)) {
                this.mainOrderTableItemDTO = tableItemThis;
            }
        }
    }



    public DishInfoDTO convertFromDineInItemDTO(DineInItemDTO dineInItemDTO) {
        DishInfoDTO dishInfoDTO = new DishInfoDTO();
        dishInfoDTO.setOrderItemGuid(dineInItemDTO.getGuid());
        dishInfoDTO.setDishGuid(dineInItemDTO.getItemGuid());
        dishInfoDTO.setDishName(dineInItemDTO.getItemName());
        dishInfoDTO.setDishSpecification(dineInItemDTO.getSkuGuid());
        dishInfoDTO.setDishUnit(dineInItemDTO.getUnit());
        //9.21早与蔡商议，因加商品券增加赠送数量GiftDishNum字段，原DishNum字段仍然传当前数量与赠送数量之和
        dishInfoDTO.setDishNum(dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount()));
        dishInfoDTO.setGiftDishNum(dineInItemDTO.getFreeCount());
        dishInfoDTO.setMainGoodGuid(null);
        dishInfoDTO.setIsMainGood(1);
        dishInfoDTO.setSurcharge(BigDecimal.ZERO);
        dishInfoDTO.setRemark(dineInItemDTO.getRemark());
        dishInfoDTO.setDishType(dineInItemDTO.getItemType());
        //dishInfoDTO.setSubtotal(dineInItemDTO.getItemPrice());
        dishInfoDTO.setDishOriginalUnitPrice(dineInItemDTO.getPrice());
        dishInfoDTO.setDishSellUnitPrice(dineInItemDTO.getPrice());
        BigDecimal memberPrice = dineInItemDTO.getMemberPrice();
        BigDecimal price = dineInItemDTO.getPrice();
        BigDecimal beforeItemPrice = dineInItemDTO.getBeforeItemPrice();
        if(enableMemberPrice&&memberPrice!=null) {
            BigDecimal subtract = price.subtract(memberPrice);
            subtract = subtract.multiply(dineInItemDTO.getCurrentCount());
            beforeItemPrice = beforeItemPrice.subtract(subtract);
        }
        //beforeItemPrice.subtract(dineInItemDTO.getCurrentCount());
        //price = price.add(dineInItemDTO.get)
        dishInfoDTO.setPayPrice(beforeItemPrice);
        dishInfoDTO.setSubtotal(dishInfoDTO.getPayPrice());
        return dishInfoDTO;

    }




    /**
     * 订单列表 构建桌台
     * @param dineinOrderDetailRespDTO
     * @return
     */
    //订单转桌台= 子单主单
    private OrderTableItemDTO transfTable(DineinOrderDetailRespDTO dineinOrderDetailRespDTO,boolean realTable) {
        OrderTableItemDTO tableItem= OrderTableItemDTO.builder().build();
        String diningTableGuid = dineinOrderDetailRespDTO.getDiningTableGuid();
        String diningTableName = dineinOrderDetailRespDTO.getDiningTableName();
        if(StringUtils.isEmpty(diningTableGuid)){
            diningTableGuid = this.tableDTO.getTableGuid();
            diningTableName = this.tableDTO.getCode();
        }
        tableItem.setTableGuid(diningTableGuid);
        tableItem.setDiningTableName(diningTableName);
        // 设置桌台主桌
        setTableByUpperState(dineinOrderDetailRespDTO, tableItem);

        List<DineInItemDTO> dineInItemDTOS = dineinOrderDetailRespDTO.getDineInItemDTOS();
        if (realTable) {
            //多个批次
            Set<OrderBatchItemDTO> innerBatchDineItemMap = getBatchDineItemMap(dineinOrderDetailRespDTO, diningTableGuid);
            tableItem.setOrderBatchDTOset(innerBatchDineItemMap);
            tableItem.setGuestCount(dineinOrderDetailRespDTO.getGuestCount());
            //tableItem.setTableGuid();
            //orderBatchDTOs.addAll(batchDineItemMap.values());
        }else {
            tableItem.setMainTable(1);
            tableItem.setTableCode(tableDTO.getCode());
            String batchId =  "t";
            //一个批次
            OrderBatchItemDTO batch = this.batchDineItemMap.get(batchId);
            if(batch==null){
                batch = new OrderBatchItemDTO();
                List<DineItemDTO> dineItemDTOs = batch.getDineItemDTOs();
                if(dineItemDTOs==null){
                    dineItemDTOs = new ArrayList<>();
                    batch.setDineItemDTOs(dineItemDTOs);
                }
                batch.setBatchCode(batchId);
                this.batchDineItemMap.put(batch.getBatchCode(),batch);
            }
            //batch.setBatchCode("0");
            batch.setUpdateTime(LocalDateTime.now());
            batch.setLoginStatus(false);
            tableItem.setAreaName("");
            if(wxUserRecordDO!=null){
                batch.setOperateGuid(null);
                batch.setOpenid(wxUserRecordDO.getOpenId());
                batch.setNickname(wxUserRecordDO.getNickName()==null?"":wxUserRecordDO.getNickName());
                batch.setHeadPortrait(wxUserRecordDO.getHeadImgUrl());
                batch.setLoginStatus(StringUtils.hasText(orderDetailTrade.getMemberGuid())&&!"0".equals(orderDetailTrade.getMemberGuid()));
            }else{
                batch.setNickname("服务员");
                batch.setOperateGuid("1");
            }
            //orderRecord.getorder
            //状态(1：未结账， 2：已结账， 3：已退款，4：已作废)
            int tradeStatu = orderDetailTrade.getState().intValue();
            if(tradeStatu==1){
                batch.setState(OrderUtils.orderStateTransition(orderRecord.getOrderState()));
            }else{
                //批次状态,0待确认，1已下单，2已支付，3已取消，4，已退菜，5待支付，6已完成
                batch.setState(OrderUtils.tradeStateTransition(tradeStatu));
            }
            batch.setRemark(orderDetailTrade.getRemark());
            batch.setUpdateTime(dineinOrderDetailRespDTO.getGmtCreate());
            if(!CollectionUtils.isEmpty(dineInItemDTOS)){
                for (int i = 0; i < dineInItemDTOS.size(); i++) {
                    batchDineItem(dineInItemDTOS.get(i),batch);
                }
            }
            tableItem.getOrderBatchDTOset().add(batch);
            logger.info("查询为一个批次,为{}",batch);
        }
        tableItem.setAreaName("");
        return tableItem;
    }

    private void setTableByUpperState(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, OrderTableItemDTO tableItem) {
        // 0:无并单，1:主单， 2:子单
        Integer upperState = dineinOrderDetailRespDTO.getUpperState();
        if (upperState == UpperStateEnum.MAIN.getCode() || upperState == UpperStateEnum.SAME_MAIN.getCode()) {
            getOrderDetailDTO().setGmtCreate(dineinOrderDetailRespDTO.getGmtCreate());
        }
        //0表示主桌，1表示次桌
        tableItem.setMainTable(upperState.equals(1) ? 0 : 1);
        if (UpperStateEnum.SAME_ORDER_STATE.contains(upperState)) {
            tableItem.setMainTable(upperState.equals(3) ? 0 : 1);
        }
    }

    private Set<OrderBatchItemDTO> getBatchDineItemMap(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, String tableGuid) {
        List<DineInItemDTO> dineInItemDTOS = dineinOrderDetailRespDTO.getDineInItemDTOS();
        Set<OrderBatchItemDTO> set = new TreeSet<>();
        if (CollectionUtils.isEmpty(dineInItemDTOS)) {
            if (UpperStateEnum.SAME_ORDER_STATE.contains(dineinOrderDetailRespDTO.getUpperState())
                    && BigDecimalUtil.greaterThanZero(dineinOrderDetailRespDTO.getAppendFee())) {
                OrderBatchItemDTO orderBatchItem = OrderBatchItemDTO.builder().build();
                orderBatchItem.setBatchCode("-999");
                orderBatchItem.setOperateGuid("1");
                orderBatchItem.setUpdateTime(LocalDateTime.now());
                orderBatchItem.setNickname("服务员");
                int state = orderDetailTrade.getState();
                if (state == 1) {
                    orderBatchItem.setState(OrderUtils.orderStateTransition(orderRecord.getOrderState()));
                } else {
                    //批次状态,0待确认，1已下单，2已支付，3已取消，4，已退菜，5待支付，6已完成
                    orderBatchItem.setState(OrderUtils.tradeStateTransition(state));
                }
                set.add(orderBatchItem);
            }
            return set;
        }

        for(DineInItemDTO dineInItemDTO :dineInItemDTOS) {
            String wxBatch = Optional.ofNullable(dineInItemDTO.getWxBatch()).orElse("t"+tableGuid);
            if("0".equals(wxBatch)){
                wxBatch = "t"+tableGuid;
            }
            OrderBatchItemDTO orderBatchItem = batchDineItemMap.get(wxBatch);
            if(orderBatchItem==null){
                orderBatchItem = OrderBatchItemDTO.builder().build();
                orderBatchItem.setBatchCode(wxBatch);
                orderBatchItem.setOperateGuid("1");
                orderBatchItem.setUpdateTime(dineInItemDTO.getGmtCreate());
                orderBatchItem.setNickname("服务员");
                //orderBatchItem.setState(1);
                orderBatchItem.setState(OrderUtils.tradeStateTransition(this.orderDetailTrade.getState()));
                batchDineItemMap.put(wxBatch, orderBatchItem);
                set.add(orderBatchItem);
            }
            if(orderBatchItem.getState()==null){
                orderBatchItem.setState(1);
            }
            batchDineItem(dineInItemDTO, orderBatchItem);

        }
        return set;
    }

    private void batchDineItem(DineInItemDTO dineInItemDTO, OrderBatchItemDTO orderBatchItem) {
        //构建非赠品
        DineItemDTO item = transfItem(dineInItemDTO,false);
        //构建赠品
        DineItemDTO itemGift = transfItem(dineInItemDTO,true);
        List<DineItemDTO> dineItemDTOs = orderBatchItem.getDineItemDTOs();
        if(dineItemDTOs==null){
            dineItemDTOs = new ArrayList<>();
            orderBatchItem.setDineItemDTOs(dineItemDTOs);
        }
        if (item!=null) {
            dineItemDTOs.add(item);
            addItem(item);
            //itemGuidImgSet.add(dineInItemDTO.getItemGuid());
            //dineOrderItemMap.put(dineInItemDTO.getGuid(), item);
        }
        if (itemGift!=null) {
            dineItemDTOs.add(itemGift);
            addItem(itemGift);
            //itemGuidImgSet.add(dineInItemDTO.getItemGuid());
            //dineOrderItemMap.put(dineInItemDTO.getGuid(), itemGift);
        }
        transfDishAndAdd(dineInItemDTO);
    }

    //微信订单项转换
    private DineItemDTO transf(WxOrderItemDO wxItem) {


        DineItemDTO item = DineItemDTO.builder().build();
        item.setPresentTag(false);
        item.setCode(wxItem.getCode());
        if(StringUtils.hasText(wxItem.getItemAttr())) {
            List<ItemInfoAttrGroupDTO> itemInfoAttrGroupDTOS = JacksonUtils.toObjectList(ItemInfoAttrGroupDTO.class, wxItem.getItemAttr());
            item.setItemAttrDTOS(OrderItemTransfUtils.transformItemAttrGroup(itemInfoAttrGroupDTOS) );
        }
        //套餐分组处理
        if(StringUtils.hasText(wxItem.getSubgroup())) {
            List<ItemInfoSubgroupDTO> itemInfoSubgroupDTOS = JacksonUtils.toObjectList(ItemInfoSubgroupDTO.class, wxItem.getSubgroup());
            List<PackageSubitemDTO> packageSubitems = item.getPackageSubitems();
            if(packageSubitems==null) {
                packageSubitems = new ArrayList<>();
                item.setPackageSubitems(packageSubitems);
            }
            for(ItemInfoSubgroupDTO subGroup:itemInfoSubgroupDTOS) {
                List<PackageSubitemDTO> subItems = transfSubItem(subGroup);
                packageSubitems.addAll(subItems);
            }
        }
        item.setGuid(wxItem.getGuid());
        item.setAmount(wxItem.getCurrentCount());
        item.setItemGuid(wxItem.getItemGuid());
        item.setItemName(wxItem.getItemName());
        if (!StringUtils.isEmpty(wxItem.getCouponInfo())) {
            item.setItemName("【团】" + item.getItemName());
        }
        item.setItemType(wxItem.getItemType());
        item.setSkuGuid(wxItem.getSkuGuid());
        item.setItemImg(wxItem.getPictureUrl());
        item.setSkuName(wxItem.getSkuName());
        item.setTotlePrice(wxItem.getOriginalPrice());
        if (Objects.nonNull(wxItem.getCouponInfo())) {
            item.setTotlePrice(BigDecimal.ZERO);
        }
        logger.info("wx商品信息:{}", JacksonUtils.writeValueAsString(wxItem));
        item.setHasMemberPrice(enableMemberPrice && Objects.isNull(wxItem.getCouponInfo())
                && wxItem.getMemberPrice() != null && wxItem.getMemberPrice().compareTo(BigDecimal.ZERO) > 0);
        if (item.getHasMemberPrice()) {
            //TODO
            item.setSaleTotlePrice(wxItem.getMemberPrice());
        }
        //dineOrderItemMap.put(wxItem.getGuid(), item);
        //itemGuidImgSet.add(e)

        return item;
    }

    private List<PackageSubitemDTO> transfSubItem(ItemInfoSubgroupDTO subGroup) {
        List<ItemInfoSubSkuDTO> subDineInItemDTOS = subGroup.getSubItemSkuList();
        List<PackageSubitemDTO> lists = new ArrayList<>();
        for(ItemInfoSubSkuDTO subItem:subDineInItemDTOS) {
            PackageSubitemDTO packageSubitemDTO = new PackageSubitemDTO();
            packageSubitemDTO.setSubgroupGuid(subGroup.getSubgroupGuid());
            packageSubitemDTO.setSubgroupName(subGroup.getName());
            packageSubitemDTO.setPresentTag(false);
            packageSubitemDTO.setCode(subItem.getCode());
            packageSubitemDTO.setItemAttrDTOS(OrderItemTransfUtils.transformItemAttrGroup(subItem.getAttrGroupList()));
            packageSubitemDTO.setItemGuid(subItem.getItemGuid());
            packageSubitemDTO.setItemName(subItem.getItemName());
            packageSubitemDTO.setItemType(subItem.getItemType());
            packageSubitemDTO.setSkuGuid(subItem.getSkuGuid());
            packageSubitemDTO.setSkuName(subItem.getSkuName());
            packageSubitemDTO.setPrice(subItem.getAddPrice());
            packageSubitemDTO.setAmount(subItem.getItemNum());
            //packageSubitemDTO.setItemImg(subItem.getPictureUrl());
            lists.add(packageSubitemDTO);
        }
        return lists;
    }
    /**
     * trade订单项转换
     * @param dineInItemDTO
     * @param gift
     * @return
     */
    private DineItemDTO transfItem(DineInItemDTO dineInItemDTO, boolean gift) {
        logger.info("trade订单项转换transfItem,dineInItemDTO:{}", JSONObject.toJSONString(dineInItemDTO));
        BigDecimal freeCount = dineInItemDTO.getFreeCount();
        BigDecimal currentCount = dineInItemDTO.getCurrentCount();
        if (gift && freeCount.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        if (!gift && currentCount.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        DineItemDTO item = DineItemDTO.builder().build();
        item.setItemType(dineInItemDTO.getItemType());
        int itemType = dineInItemDTO.getItemType();
        if (itemType == 1 || itemType == 5) {
            item.setItemType(1);
            //处理套餐
            List<PackageSubitemDTO> packageSubitems = Optional.ofNullable(item.getPackageSubitems()).orElse(Lists.newArrayList());
            item.setPackageSubitems(packageSubitems);
            for (PackageSubgroupDTO sub : dineInItemDTO.getPackageSubgroupDTOS()) {
                //套餐子分组
                List<PackageSubitemDTO> subItems = transf(sub);
                packageSubitems.addAll(subItems);
            }
        }
        item.setPresentTag(gift);
        item.setCode(dineInItemDTO.getCode());
        item.setItemAttrDTOS(dineInItemDTO.getItemAttrDTOS());
        item.setItemGuid(dineInItemDTO.getItemGuid());
        item.setGuid(dineInItemDTO.getGuid());
        item.setItemName(dineInItemDTO.getItemName());
        if (!StringUtils.isEmpty(dineInItemDTO.getCouponCode())) {
            item.setItemName("【团】" + item.getItemName());
        }
        item.setSkuGuid(dineInItemDTO.getSkuGuid());
        item.setRemark(dineInItemDTO.getRemark());
        item.setSkuName(dineInItemDTO.getSkuName());
        item.setAmountUnit(dineInItemDTO.getUnit());
        if (gift) {
            //构建赠品
            item.setAmount(dineInItemDTO.getFreeCount());
            List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
            item.setTotlePrice(BigDecimal.ZERO);
            if (!CollectionUtils.isEmpty(freeItemDTOS)) {
                BigDecimal beforeItemPrice = freeItemDTOS.get(0).getItemPrice().setScale(2, RoundingMode.HALF_UP);
                item.setTotlePrice(beforeItemPrice);
            }
        } else {
            //构建非赠品
            unGiftItemHandler(item, dineInItemDTO);
        }
        item.setPrice(dineInItemDTO.getPrice());
        item.setHasMemberPrice(StringUtils.isEmpty(dineInItemDTO.getCouponCode()) && item.getSaleTotlePrice() != null);
        logger.info("trade订单项转换后的结果transfItem,item:{}", JSONObject.toJSONString(item));
        return item;
    }

    private void unGiftItemHandler(DineItemDTO item, DineInItemDTO dineInItemDTO) {
        //构建非赠品
        item.setAmount(dineInItemDTO.getCurrentCount());
        BigDecimal price = dineInItemDTO.getPrice();
        BigDecimal beforeItemPrice = dineInItemDTO.getBeforeItemPrice();
        beforeItemPrice = beforeItemPrice.setScale(2, RoundingMode.HALF_UP);
        if (!StringUtils.isEmpty(dineInItemDTO.getCouponCode())) {
            item.setSaleTotlePrice(null);
            item.setTotlePrice(BigDecimal.ZERO);
            return;
        }
        boolean change = dineInItemDTO.getPriceChangeType() != null && dineInItemDTO.getPriceChangeType().equals(1);
        if (change) {
            item.setSaleTotlePrice(null);
            item.setTotlePrice(price.multiply(dineInItemDTO.getCurrentCount()));
        } else if (grouponCouponFee.compareTo(BigDecimal.ZERO) > 0) {
            item.setSaleTotlePrice(null);
            item.setTotlePrice(beforeItemPrice);
        } else if (dineInItemDTO.getIsCaculatByMemberPrice() != null && dineInItemDTO.getIsCaculatByMemberPrice().equals(1)) {
            item.setSaleTotlePrice(dineInItemDTO.getItemPrice());
            item.setTotlePrice(beforeItemPrice);
            //已完成订单也计算会员价
        } else if (enableMemberPrice && dineInItemDTO.getMemberPrice() != null
                && dineInItemDTO.getMemberPrice().compareTo(BigDecimal.ZERO) > 0) {
            //原价- （原单价-会员价）*数量 1.01-（0.01-1.01）=2.01
            hanldeMemberPrice(dineInItemDTO, item);
            item.setTotlePrice(beforeItemPrice);
        } else {
            item.setTotlePrice(beforeItemPrice);
        }
        tradeOrderTotalPriceHandler(item, dineInItemDTO);
    }

    private void tradeOrderTotalPriceHandler(DineItemDTO item, DineInItemDTO dineInItemDTO) {
        DineinOrderDetailRespDTO innerOrderDetailTrade = this.getOrderDetailTrade();
        //状态(1：未结账， 2：已结账， 3：已退款，4：已作废， 12:子单结账)
        if (Objects.nonNull(innerOrderDetailTrade) && (innerOrderDetailTrade.getState() == 2 || innerOrderDetailTrade.getState() == 12)) {
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS = Optional.ofNullable(innerOrderDetailTrade.getDiscountFeeDetailDTOS())
                    .orElse(Lists.newArrayList());
            boolean hasMemberPrice = discountFeeDetailDTOS.stream()
                    .anyMatch(e -> Objects.equals(DiscountTypeEnum.SINGLE_MEMBER.getCode(), e.getDiscountType()));
            if (hasMemberPrice && Objects.isNull(item.getSaleTotlePrice())) {
                hanldeMemberPrice(dineInItemDTO, item);
            }
            if (Objects.nonNull(item.getSaleTotlePrice()) && !hasMemberPrice) {
                item.setSaleTotlePrice(null);
            }
        }
    }


    private void hanldeMemberPrice(DineInItemDTO dineInItemDTO, DineItemDTO item) {
        logger.info("计算会员价SaleTotlePrice,dineInItemDTO:{}", JSONObject.toJSONString(dineInItemDTO));
        //dineInItemDTO.getPrice()
        /*BigDecimal originalPrice = dineInItemDTO.getPrice();
        BigDecimal subtract1 = originalPrice.subtract(dineInItemDTO.getMemberPrice());
        BigDecimal memberSaleTotelFee = subtract1.multiply(dineInItemDTO.getCurrentCount());
        //igDecimal memberSubtract = dineInItemDTO.getItemPrice().subtract(memberSaleTotel);
        BigDecimal itemPrice = dineInItemDTO.getBeforeItemPrice().subtract(memberSaleTotelFee);
        logger.info("memberSaleTotel:{},memberSaleTotelFee:{},itemPrice:{}",subtract1, memberSaleTotelFee,itemPrice);
        logger.info("{}-({}-{})*{}={}",dineInItemDTO.getBeforeItemPrice()
                ,originalPrice,dineInItemDTO.getMemberPrice()
                ,dineInItemDTO.getCurrentCount(),itemPrice);
        itemPrice = itemPrice.setScale(2,BigDecimal.ROUND_HALF_UP);
        */

        PricePairDTO pricePairDTO = PriceCalculationUtils.itemPrice(dineInItemDTO);
        BigDecimal memberPrice = pricePairDTO.getMemberPrice();
        //logger.info("会员价计算：memberPrice={}",memberPrice);
        if(memberPrice!=null&&memberPrice.compareTo(BigDecimal.ZERO)>0){
            item.setSaleTotlePrice(memberPrice);
        }

    }


    private List<PackageSubitemDTO> transf(PackageSubgroupDTO packageSubgroupDTO) {
        List<SubDineInItemDTO> subDineInItemDTOS = packageSubgroupDTO.getSubDineInItemDTOS();
        List<PackageSubitemDTO> lists = new ArrayList<>();
        for(SubDineInItemDTO subItem:subDineInItemDTOS) {
            PackageSubitemDTO packageSubitemDTO = new PackageSubitemDTO();
            packageSubitemDTO.setSubgroupGuid(packageSubgroupDTO.getSubgroupGuid());
            packageSubitemDTO.setSubgroupName(packageSubgroupDTO.getSubgroupName());
            packageSubitemDTO.setPresentTag(false);
            packageSubitemDTO.setCode(subItem.getCode());
            packageSubitemDTO.setItemAttrDTOS(subItem.getItemAttrDTOS()==null?new ArrayList<>(1):subItem.getItemAttrDTOS());
            packageSubitemDTO.setItemGuid(subItem.getGuid());
            packageSubitemDTO.setItemName(subItem.getItemName());
            packageSubitemDTO.setItemType(subItem.getItemType());
            packageSubitemDTO.setSkuGuid(subItem.getSkuGuid());
            packageSubitemDTO.setSkuName(subItem.getSkuName());
            packageSubitemDTO.setPrice(subItem.getPrice());
            BigDecimal packageDefaultCount = subItem.getPackageDefaultCount();
            packageDefaultCount = packageDefaultCount==null?BigDecimal.valueOf(1):packageDefaultCount;
            BigDecimal count = subItem.getCurrentCount().multiply(packageDefaultCount);
            logger.info("套餐子项数量合计:{}*{}={}",packageDefaultCount,subItem.getCurrentCount(),count);
            packageSubitemDTO.setAmount(count);
            //log
            packageSubitemDTO.setAmountUnit(subItem.getUnit());
            lists.add(packageSubitemDTO);
        }

        return lists;
    }


    private SettlementItemDTO transf(DiscountFeeDetailDTO discountFeeDetailDTO) {
        String discountName = discountFeeDetailDTO.getDiscountName();
        //1000以上是折扣
        Integer discountType = discountFeeDetailDTO.getDiscountType();
        BigDecimal discount = discountFeeDetailDTO.getDiscountFee();
        //boolean sub = discountFeeDetailDTO.getDiscountFee().compareTo(BigDecimal.ZERO)<0;
        return new SettlementItemDTO(discountName, discount, 0, discountType);
    }

    public void setBrandName(String brandName){
        this.brandName = brandName;
        this.orderDetailDTO.setBrandName(brandName);
    }
    public void setLocalTableDTO(TableDTO tableDetail){
        if(this.orderTableItemMap.containsKey(tableDTO.getTableGuid())){
            return;
        }
        this.orderDetailDTO.setStoreName(tableDetail.getStoreName());
        this.orderDetailDTO.setDiningTableCode(tableDetail.getCode());
        OrderTableItemDTO orderTableItemDTO = OrderTableItemDTO.builder().build();
        orderTableItemDTO.setTableGuid(tableDTO.getTableGuid());
        orderTableItemDTO.setTableCode(tableDTO.getCode());
        orderTableItemDTO.setAreaName(tableDTO.getAreaName());
        orderTableItemDTO.setDiningTableName(tableDTO.getName());
        //orderTableItemDTO.setGuestCount(this.orderRecord.get);
        //orderTableItemDTO.setGuestCount();
        this.orderTableItemMap.put(tableDTO.getTableGuid(),orderTableItemDTO);

        //this.
    }

    /**
     * 设置会员价总计和反结账状态 始终为已接单处理
     * @return
     */
    public OrderDetailDTO buiderMemberDishinfo() {
        if(this.saleTotalPrice==null){
            this.saleTotalPrice = BigDecimal.ZERO;
        }
        if(this.saleTotalPrice.compareTo(this.totelPrice)==0){
            this.saleTotalPrice = null;
        }else
        if(this.saleTotalPrice.compareTo(BigDecimal.ZERO)==0){
            this.saleTotalPrice = null;
        }
        if(this.saleTotalPrice == null){
            orderDetailDTO.setEnableMemberPrice(false);
        }else{
            this.saleTotalPrice = this.saleTotalPrice.setScale(2,BigDecimal.ROUND_HALF_UP);
            orderDetailDTO.setEnableMemberPrice(true);
        }

        if(this.totelPrice!=null){
            this.totelPrice = this.totelPrice.setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        orderDetailDTO.setSaleTotlePrice(this.saleTotalPrice);
        orderDetailDTO.setTotlePrice(this.totelPrice);
        String orderNo = orderDetailDTO.getOrderNo();
        //反结账订单
        if(orderNo!=null&&(orderNo.startsWith("F")||orderNo.startsWith("f"))){
            orderDetailDTO.setOrderState(1);
        }
        // 如果是反结账， 展示商家已取消
        if (Objects.nonNull(this.orderDetailTrade) && Objects.nonNull(this.orderDetailTrade.getRecoveryType())
                && this.orderDetailTrade.getRecoveryType() != 1 && this.orderDetailTrade.getRecoveryType() != 3) {
            orderDetailDTO.setOrderState(-1);
        }
        return orderDetailDTO;
    }

    public void hanldTables() {

        List<OrderTableItemDTO> tableOrderDetailDTOs = orderDetailDTO.getTableOrderDetailDTO();
        if(this.mainOrderTableItemDTO!=null){
            tableOrderDetailDTOs.add(this.mainOrderTableItemDTO);
            this.orderTableItemMap.remove(this.mainOrderTableItemDTO.getTableGuid());
        }
       // List<OrderTableItemDTO> newTableOrderDetailDTOs = new ArrayList<>();
        tableOrderDetailDTOs.addAll(this.orderTableItemMap.values());
        //orderDetailDTO.setDineinItemCount(itemMap);
        //就餐人数
        int  count = 0;
        //Integer orderState = this.getOrderDetailDTO().getOrderState();
        List<DineItemDTO> listDineItems = new ArrayList<>();
        for(OrderTableItemDTO table:tableOrderDetailDTOs){
            count = count+(table.getGuestCount()==null||table.getGuestCount()==0?orderDetailDTO.getGuestCount()
                    :table.getGuestCount());
            Set<OrderBatchItemDTO> batchItems = table.getOrderBatchDTOset();
            table.setOrderBatchDTOs(filterTableOrderBatchDTOs(table));
            table.setOrderBatchDTOset(null);
            List<OrderBatchItemDTO> orderBatchDTOs = table.getOrderBatchDTOs();
            //批次未空
            if(CollectionUtils.isEmpty(orderBatchDTOs)){
                table.setDineinItemEmpty(1);
                continue;
            }
            for(OrderBatchItemDTO dto:batchItems){
                listDineItems.addAll(dto.getDineItemDTOs());
            }
            if(CollectionUtils.isEmpty(listDineItems)){
                table.setDineinItemEmpty(1);
            }else{
                table.setDineinItemEmpty(0);
            }

        }
        orderDetailDTO.setGuestCount(count);
        //并桌数据
        if(CollectionUtils.isEmpty(tableOrderDetailDTOs)){
            logger.error("没有桌台数据,wxOrder:{},tradeOrder:{}",
                    JSON.toJSONString(orderDetailDTO),JSON.toJSONString(orderDetailTrade));
            return ;
        }

        OrderTableItemDTO orderTableItemDTO = tableOrderDetailDTOs.get(0);
        List<OrderBatchItemDTO> orderBatchDTOs = orderTableItemDTO.getOrderBatchDTOs();
        if(CollectionUtils.isEmpty(orderBatchDTOs)){
            return;
        }
        if(!this.isRealTable()){
            tableOrderDetailDTOs = new ArrayList<>(1);
            OrderBatchItemDTO orderBatchItemDTO = orderBatchDTOs.get(0);
            orderTableItemDTO.setOrderBatchDTOs(new ArrayList<>(1));
            List<OrderBatchItemDTO> orderBatchDTOs1 = orderTableItemDTO.getOrderBatchDTOs();
            orderBatchDTOs1.add(orderBatchItemDTO);
            orderBatchItemDTO.setDineItemDTOs(listDineItems);
            tableOrderDetailDTOs.add(orderTableItemDTO);
            this.orderDetailDTO.setTableOrderDetailDTO(tableOrderDetailDTOs);
        }

        //this.getOrderDetailDTO().set
        List<DineItemDTO> dineItemDTOs = orderBatchDTOs.get(0).getDineItemDTOs();
        if(dineItemDTOs!=null){
            orderTableItemDTO.setFirstBatchItemCount(orderBatchDTOs.get(0).getDineItemDTOs().size());
        }else{
            orderTableItemDTO.setFirstBatchItemCount(0);
        }
        //orderDetailDTO.setTableOrderDetailDTO(tableOrderDetailDTOs);
        //orderTableItemDTO.set

        //return newTableOrderDetailDTOs;
    }

    private List<OrderBatchItemDTO> filterTableOrderBatchDTOs(OrderTableItemDTO table) {
        return new ArrayList<>(table.getOrderBatchDTOset()).stream()
                .filter(bat -> !CollectionUtils.isEmpty(bat.getDineItemDTOs()) || "-999".equals(bat.getBatchCode()))
                .collect(Collectors.toList());
    }


    public void   initOrderStateAndOrderDetailTrade(DineinOrderDetailRespDTO orderDetailResp) {
        Integer orderMode = orderRecord.getOrderMode();
        orderDetailDTO.setTradeMode(orderMode);
        //orderDetailDTO.setOrderState(orderRecord.getOrderState());
        if (orderDetailResp == null) {
            return;
        }
        orderDetailDTO.setTradeState(orderDetailResp.getState());
        this.orderDetailTrade = orderDetailResp;
        //trade状态(1：未结账， 2：已结账， 3：已退款，4：已作废)
        int tradeState = orderDetailTrade.getState().intValue();
        //0:无并单，1:主单， 2:子单
        int i = orderDetailTrade.getUpperState().intValue();
        if(i==0){
            orderDetailDTO.setCombine(0);
        }else{
            orderDetailDTO.setCombine(1);
        }
        //tradeState=1未结账
        Integer state = OrderUtils.tradeStateTransition(tradeState);
        orderDetailDTO.setOrderState(state);
        Integer guestCount = orderDetailResp.getGuestCount();
        orderDetailDTO.setGuestCount(guestCount);
        //orderDetailDTO.setCombine(1);

    }


    public void initItemImgs(List<ItemImgDTO> itemImgDTOs) {
        //6589349262889320448, 6589349262885126144, 6589349262880931840, 6572731250471659521
        this.ItemImgDTOs = itemImgDTOs;
        //logger.info("查询到商品图片:{},订单商品guids:{}",itemImgDTOs,itemMap.keySet());
        //6572731250471659521
        for(ItemImgDTO imgDTO:itemImgDTOs) {
            List<DineItemDTO> dineItemDTOS = itemMap.get(imgDTO.getItemGuid());
            if(dineItemDTOS==null) {
                continue;
            }
            for(DineItemDTO dineItemDTO:dineItemDTOS){
                String imgUrl = imgDTO.getImgUrl();
                if(!StringUtils.isEmpty(imgUrl)){
                    dineItemDTO.setItemImg(imgDTO.getImgUrl().split(",")[0]);
                }

            }

            //logger.info("商品图片,dineItemName:{},img:{}",dineItemDTO.getItemGuid(),imgDTO.getImgUrl());
        }

    }

    public void addItem(DineItemDTO dineItemDTO) {
        if(dineItemDTO==null) {
            return;
        }
        /**图片需要*/
        List<DineItemDTO> dineItemDTOS = itemMap.get(dineItemDTO.getItemGuid());
        if(dineItemDTOS==null){
            dineItemDTOS = new ArrayList<>();
            itemMap.put(dineItemDTO.getItemGuid(),dineItemDTOS);
        }
        dineItemDTOS.add(dineItemDTO);
        //itemMap.put(dineItemDTO.getItemGuid(), dineItemDTO);
        if(!dineItemDTO.getPresentTag().booleanValue()) {
            //会员算价
            dineOrderItemMap.put(dineItemDTO.getGuid(), dineItemDTO);
            //原价求和
            totelPrice = totelPrice.add(dineItemDTO.getTotlePrice());
            if(dineItemDTO.getSaleTotlePrice()!=null
                    &&dineItemDTO.getSaleTotlePrice().compareTo(BigDecimal.ZERO)>0
                    &&enableMemberPrice){
                //BigDecimal add = this.getSaleTotalPrice().add(dineItemDTO.getSaleTotlePrice());
                saleTotalPrice = saleTotalPrice.add(dineItemDTO.getSaleTotlePrice());
            }else{
                saleTotalPrice = saleTotalPrice.add(dineItemDTO.getTotlePrice());
            }
            //orderDetailDTO.setSaleTotlePrice(saleTotalPrice);

        }
        /**图片需要*/
        if(StringUtils.isEmpty(dineItemDTO.getItemImg())) {
            itemGuidImgSet.add(dineItemDTO.getItemGuid());
        }

    }

    /**
     * 构建附加费明细
     */
    private List<AppendFeeDetailDTO> buildAppendFeeDetails() {
        OrderFeeDetailDTO orderFeeDetailDTO = orderDetailTrade.getOrderFeeDetailDTO();
        if (Objects.isNull(orderFeeDetailDTO)) {
            return Collections.emptyList();
        }
        List<com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO> appendFeeDetailList =
                orderFeeDetailDTO.getAppendFeeDetailDTOS();
        if (CollectionUtils.isEmpty(appendFeeDetailList)) {
            return Collections.emptyList();
        }
        return appendFeeDetailList.stream().map(e -> {
            AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
            appendFeeDetailDTO.setName(e.getName());
            appendFeeDetailDTO.setType(e.getType());
            appendFeeDetailDTO.setTotalAmount(e.getAmount());
            BigDecimal count = e.getAmount().divide(e.getUnitPrice(), 0, RoundingMode.DOWN);
            appendFeeDetailDTO.setCount(Integer.valueOf(count.toPlainString()));
            appendFeeDetailDTO.setAmount(e.getUnitPrice());
            return appendFeeDetailDTO;
        }).collect(Collectors.toList());
    }
}
