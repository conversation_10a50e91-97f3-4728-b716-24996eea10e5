package com.holderzone.saas.store.weixin.helper;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.base.dto.message.*;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.weixin.service.rpc.BaseClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Component
@Slf4j
public class WebsocketMessageHelper {
	
	public static final String DINNER_CODE = "dinner";
	public static final String SHOPCART_CODE = "shopcart";
	public static final String ORDER_CODE = "order";
	public static final String TABLE_TURN_ON = "table_turn_on";
	public static final String TABLE_TURN_OFF = "table_turn_off";
	
	@Resource
	BaseClientService baseClientService;
	
	public boolean sendDinnerEmqMessage(String tableGuid,List<String> openIds,String nickname,String headImgUrl) {
		if (CollectionUtils.isEmpty(openIds)) {
			log.info("传入用户为空,桌台id:{}",tableGuid);
			return false;
		}
		JSONObject json = new JSONObject();
		json.put("nickName", nickname);
		json.put("headImgUrl", headImgUrl);
		json.put("code", DINNER_CODE);
		String message = json.toJSONString();
		for(String openid:openIds) {
			emqSend("/"+tableGuid+"/"+openid, message);
		}
		return true;
	}
	
	public void sendOrderEmqMessage(String tableGuid,List<String> openIds) {
		if (CollectionUtils.isEmpty(openIds)) {
			log.info("传入用户为空,桌台id:{}",tableGuid);
			return;
		}
		JSONObject json = new JSONObject();
		json.put("code", ORDER_CODE);
		String message = json.toJSONString();
		for(String openid:openIds) {
			emqSend("/"+tableGuid+"/"+openid, message);
		}
	}

	public void sendOrderEmqMessage(String tableGuid, Set<String> openIds) {
		if (CollectionUtils.isEmpty(openIds)) {
			log.info("传入用户为空,桌台id:{}",tableGuid);
			return;
		}
		JSONObject json = new JSONObject();
		json.put("code", ORDER_CODE);
		String message = json.toJSONString();
		for(String openid:openIds) {
			emqSend("/"+tableGuid+"/"+openid, message);
		}
	}

	public void sendShopcartEmqMessage(String tableGuid,List<String> openIds) {
		if (CollectionUtils.isEmpty(openIds)) {
			log.info("传入用户为空,桌台id:{}",tableGuid);
			return;
		}
		JSONObject json = new JSONObject();
		json.put("code", SHOPCART_CODE);
		String message = json.toJSONString();
		for(String openid:openIds) {
			emqSend("/"+tableGuid+"/"+openid, message);
		}
	}

	public void emqSend(String topic, String message) {
		MessageDTO messageDTO = new MessageDTO();
		PushMessageDTO pushMessage = new PushMessageDTO();
		pushMessage.setTopicType(TopicType.SYSTEM);
		SysMessage sysMessage = new SysMessage();
		sysMessage.setTopic(topic);
		pushMessage.setSysMessage(sysMessage);
		messageDTO.setMessageType(MessageType.PUSH);
		pushMessage.setData(message);
		messageDTO.setPushMessage(pushMessage );
		try{
			boolean sendMessage = baseClientService.sendMessage(messageDTO);
			log.info("emq_message_send,topic:{},result:{},message:{}",topic,sendMessage,message);
		}catch (Exception e){
			log.error("emq_message_send 异常",e);
		}

	}

	/**
	 * true：转到其它桌台，false：被占用
	 */
	public void sendTableEmqMessage(String tableGuid, TurnTableDTO turnTableDTO, boolean flag, String msg, Set<String> openIds) {
		JSONObject json = new JSONObject();
		if (CollectionUtils.isEmpty(openIds)) {
			log.info("转台用户为空");
			return;
		}
		if (flag) {
			//TODO 临时处理
			json.put("code", TABLE_TURN_ON);
			json.put("turnOn", msg);
			json.put("diningTableGuid", turnTableDTO.getNewTableGuid());
			json.put("diningTableName", turnTableDTO.getNewTableCode());
			json.put("areaGuid", turnTableDTO.getNewTableAreaGuid());
			json.put("areaName", turnTableDTO.getNewTableAreaName());
			json.put("tableCode", turnTableDTO.getNewTableCode());
		}else{
			json.put("code", TABLE_TURN_OFF);
			json.put("turnOff",  msg);
		}

		String message = json.toJSONString();
		for(String openid:openIds) {
			emqSend("/"+tableGuid+"/"+openid, message);
		}
	}

	private JSONObject toTableJSON(TableDTO tableDTO) {
		JSONObject json = new JSONObject();
		json.put("tableName", tableDTO.getCode());
		json.put("areaName",tableDTO.getAreaName());
		return json;
	}
	
}
