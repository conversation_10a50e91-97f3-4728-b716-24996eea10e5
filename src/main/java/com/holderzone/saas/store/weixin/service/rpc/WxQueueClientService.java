package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import com.holderzone.saas.store.dto.queue.WxQueueListDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueClientService
 * @date 2019/9/19
 */
@Component
@FeignClient(name = "holder-saas-store-queue", fallbackFactory = WxQueueClientService.WxQueueClientServiceFallBack.class)
public interface WxQueueClientService {

	@PostMapping("/queue/queryByUser")
	List<WxQueueListDTO> queryByUser(@RequestBody @Valid QueueWechatDTO queueWechatDTO);

	@Slf4j
	@Component
	class WxQueueClientServiceFallBack implements FallbackFactory<WxQueueClientService> {
		@Override
		public WxQueueClientService create(Throwable throwable) {
			return new WxQueueClientService() {

				@Override
				public List<WxQueueListDTO> queryByUser(@Valid QueueWechatDTO queueWechatDTO) {
					log.error("根据用户查询排队记录失败:{},error:{}", queueWechatDTO,throwable.getMessage());
					return Collections.emptyList();
					//throw new BusinessException("根据用户查询排队记录失败:" + throwable.getMessage());
				}
			};
		}
	}
}
