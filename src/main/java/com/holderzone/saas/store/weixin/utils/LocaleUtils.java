package com.holderzone.saas.store.weixin.utils;


import org.springframework.context.i18n.LocaleContextHolder;

import java.util.ResourceBundle;

/**
 * 国际化转换工具
*/
public class LocaleUtils {

    private LocaleUtils(){}

    public static String getMessage(String msgKey) {
        return getMessage(msgKey,null);
    }

    public static String getMessage(String msgKey,String message) {
        try {
            ResourceBundle bundle = ResourceBundle.getBundle("i18n/messages", LocaleContextHolder.getLocale());
            return bundle.getString(msgKey);
        } catch (Exception e) {
            return message;
        }
    }
}
