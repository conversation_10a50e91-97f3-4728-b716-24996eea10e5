package com.holderzone.saas.store.weixin.service;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickDownloadReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickIsModelDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickDownloadRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickModelRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTableStickService
 * @date 2019/03/05 14:02
 * @description 微信桌贴Service
 * @program holder-saas-store
 */
public interface WxStoreTableStickService {

    void handleStickMessage(List<WxTableStickDTO> wxTableStickDTO, String messageType);

    List<WxTableStickDTO> listTableStick(Integer isModel);

    boolean addOrUpdateStick(WxTableStickDTO wxTableStickDTO);

    WxTableStickDTO findByGuid(String guid);

    List<WxStickModelRespDTO> listStickModel(WxStickModelReqDTO wxStickModelReqDTO);

    void createStickZip(WxStickDownloadReqDTO wxStickDownloadReqDTO, String enterpriseGuid, JSONObject contentTemp);

    WxStickDownloadRespDTO downloadStickZip(String downloadKey);

    boolean isWeatherConfig(String storeGuid);

    /**
     * 通过guid删除我的桌贴，只能删除桌贴，不能删除模板
     *
     * @param wxStickIsModelDTO
     * @return
     */
    Boolean deleteMyStick(WxStickIsModelDTO wxStickIsModelDTO);

    Boolean checkIsBought(List<String> modelGuidList);

    void saveOrUpdateModels(List<WxTableStickDTO> wxTableStickDTOList);

    String downloadOssStickZip(String downloadKey);

    /**
     * 查询二维码重定向地址
     *
     * @param enterpriseTable 企业和桌台Guid拼接信息
     * @return 重定向地址
     */
    String getRedirectUrl(String enterpriseTable,String lang);
}
