package com.holderzone.saas.store.weixin.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxCategoryDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickDownloadReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickIsModelDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandAuthRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickDownloadRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickModelRespDTO;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.constant.ModelName;
import com.holderzone.saas.store.weixin.entity.enums.WxQrTypeEnum;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.service.WxStoreTableStickService;
import com.holderzone.saas.store.weixin.service.rpc.WxStickModelClientService;
import com.holderzone.saas.store.weixin.utils.HttpsClientUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxTableStickController
 * @date 2019/03/08 16:17
 * @description 微信桌贴相关Controller
 * @program holder-saas-store
 */
@RestController
@Api(description = "微信桌贴相关controller")
@RequestMapping("/wx_table_stick")
@Slf4j
public class WxTableStickController {

    @Autowired
    WxStoreTableStickService wxStoreTableStickService;

    @Autowired
    WxStickModelClientService wxStickModelClientService;

    @Autowired
    WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    WeChatConfig weChatConfig;

    @PostMapping("/list_model_and_stick")
    public List<WxTableStickDTO> listModelAndTicket(@RequestBody WxStickIsModelDTO wxStickIsModelDTO) {
        log.info("微信服务：开始查询当前企业已拥有模板列表");
        return wxStoreTableStickService.listTableStick(wxStickIsModelDTO.getIsModel());
    }

    @PostMapping("/save_or_update_stick")
    public boolean saveOrUpdate(@RequestBody WxTableStickDTO wxTableStickDTO) {
        log.info("微信服务：开始创建企业桌贴，wxTableStick：{}", wxTableStickDTO);
        wxTableStickDTO.setIsModel(0);
        return wxStoreTableStickService.addOrUpdateStick(wxTableStickDTO);
    }

    @PostMapping("/find_by_guid")
    public WxTableStickDTO findByGuid(@RequestBody String guid) {
        log.info("微信服务：开始查询桌贴信息，guid:{}", guid);
        WxTableStickDTO wxTableStickDTO = wxStoreTableStickService.findByGuid(guid);
        return wxTableStickDTO;
    }

    @PostMapping("/list_stick_model")
    public List<WxStickModelRespDTO> listWxTableStick(@RequestBody WxStickModelReqDTO wxStickModelReqDTO) {
        log.info("微信服务，开始查询模板库信息，请求入参：{}", wxStickModelReqDTO);
        return wxStoreTableStickService.listStickModel(wxStickModelReqDTO);
    }

    @PostMapping("/list_stick_category")
    public List<WxCategoryDTO> listStickCategory() {
        log.info("开始查询模板分类");
        return wxStickModelClientService.listCategory();
    }

    @PostMapping("/delete_my_stick")
    public Boolean deleteMyStick(@RequestBody WxStickIsModelDTO wxStickIsModelDTO) {
        log.info("删除我的桌贴请求，入参：{}", wxStickIsModelDTO);
        return wxStoreTableStickService.deleteMyStick(wxStickIsModelDTO);
    }

    @PostMapping("/download_stick_zip")
    public WxStickDownloadRespDTO downloadStickZip(@RequestBody String downloadKey) {
        log.info("开始下载桌贴zip包：downloadKey:{}", downloadKey);
        return wxStoreTableStickService.downloadStickZip(downloadKey);
    }

    @PostMapping("/create_stick_zip")
    public String createStickZip(@RequestBody WxStickDownloadReqDTO wxStickDownloadReqDTO) {
        if (wxStickDownloadReqDTO.getQrCodeType() == 1) {
            WxBrandAuthRespDTO wxBrandAuthRespDTO = wxStoreAuthorizerInfoService.getByBrandGuid(wxStickDownloadReqDTO.getBrandGuid());
            log.info("已获取到当前品牌公众号绑定信息：{}", wxBrandAuthRespDTO);
        }
        JSONObject content = null;
        if (wxStickDownloadReqDTO.getQrCodeType() != null && Lists.newArrayList(WxQrTypeEnum.ZHUANCAN_QR.getCode(),
                WxQrTypeEnum.WX_CP_QR.getCode()).contains(wxStickDownloadReqDTO.getQrCodeType())) {
            String response = HttpsClientUtils.doPostJSON(weChatConfig.getTongChiDaoTableTicketInfo(), wxStickDownloadReqDTO);
            log.info("下载赚餐二维码返回{}", response);
            if (StringUtils.isEmpty(response)) {
                throw new BusinessException("赚餐二维码下载错误");
            }
            JSONObject jsonObject = JSONObject.parseObject(response);
            Object code = jsonObject.get("code");
            if (code != null && "0".equals(code.toString())) {
                //success
                content = jsonObject.getJSONObject("content");
            } else {
                Object message = jsonObject.get("message") == null ? "赚餐二维码下载错误" : jsonObject.get("message");
                throw new BusinessException(message.toString());
            }
        }
        if (!wxStoreTableStickService.isWeatherConfig(wxStickDownloadReqDTO.getStoreGuid())) {
            throw new BusinessException("当前门店暂未进行微信点餐配置");
        }
        String key = redisUtils.generateGuid(ModelName.WX + ":"
                + wxStickDownloadReqDTO.getStickGuid() + ":downloadZip");
        wxStickDownloadReqDTO.setDownloadKey(key);
        log.info("开始调用生成桌贴异步方法，downloadKey：{}", key);
        wxStoreTableStickService.createStickZip(wxStickDownloadReqDTO, EnterpriseIdentifier.getEnterpriseGuid(), content);
        return key;
    }

    @GetMapping("/qr_redirect")
    public String qrRedirect(String enterpriseTable, String lang) {
        log.info("查询二维码重定向地址：enterpriseTable:{}", enterpriseTable);
        //根据桌台Guid查询重定向地址
        String url = wxStoreTableStickService.getRedirectUrl(enterpriseTable, lang);
        log.info("二维码重定向地址：url:{}", url);
        return url;
    }
}
