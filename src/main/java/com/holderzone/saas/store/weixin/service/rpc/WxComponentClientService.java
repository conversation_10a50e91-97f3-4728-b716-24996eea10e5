package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.saas.store.dto.weixin.req.WxComponentConfigDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxComponentClientService
 * @date 2019/03/04 9:26
 * @description 微信三方平台配置存储Client
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-cloud-wechat", fallbackFactory = WxComponentClientService.WxComponentFallBack.class)
public interface WxComponentClientService {

    @PostMapping("/config")
    Boolean addWxConfig(WxComponentConfigDTO wxComponentConfigDTO);

    @PutMapping("/config")
    Boolean updateWxConfig(WxComponentConfigDTO wxComponentConfigDTO);

    @DeleteMapping("/config/{componentAppId}")
    Boolean deleteWxConfig(@PathVariable("componentAppId") String componentAppId);

    @GetMapping("/config/{componentAppId}")
    WxComponentConfigDTO findWxConfig(@PathVariable("componentAppId") String componentAppId);

    @Component
    @Slf4j
    class WxComponentFallBack implements FallbackFactory<WxComponentClientService> {
        @Override
        public WxComponentClientService create(Throwable throwable) {
            return new WxComponentClientService() {
                @Override
                public Boolean addWxConfig(WxComponentConfigDTO wxComponentConfigDTO) {
                    log.error("新增微信三方平台时Feign调用异常，msg:{}", throwable.getMessage());
                    throw new RuntimeException("新增微信三方平台时Feign调用异常，msg:{}", throwable);
                }

                @Override
                public Boolean updateWxConfig(WxComponentConfigDTO wxComponentConfigDTO) {
                    log.error("修改微信三方平台时Feign调用异常，msg:{}", throwable.getMessage());
                    throw new RuntimeException("修改微信三方平台时Feign调用异常，msg:{}", throwable);
                }

                @Override
                public Boolean deleteWxConfig(String componentAppId) {
                    log.error("删除微信三方平台时Feign调用异常，msg:{}", throwable.getMessage());
                    throw new RuntimeException("删除微信三方平台时Feign调用异常，msg:{}", throwable);
                }

                @Override
                public WxComponentConfigDTO findWxConfig(String componentAppId) {
                    log.error("查询微信三方平台时Feign调用异常，msg:{}", throwable.getMessage());
                    throw new RuntimeException("查询微信三方平台时Feign调用异常，msg:{}", throwable);
                }
            };
        }
    }

}
