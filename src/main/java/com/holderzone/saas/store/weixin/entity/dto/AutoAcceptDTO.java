package com.holderzone.saas.store.weixin.entity.dto;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AutoAcceptDTO {

	private UserContext userContext;

	private WxOperateReqDTO wxOperateReqDTO;
}
