package com.holderzone.saas.store.weixin.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> R
 * @date 2020/11/24 11:57
 * @description 微信卡片消息配置对象
 */
@Data
public class WeChatCardMessageConfig {
    @ApiModelProperty(value = "企业Guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "小程序appId")
    private String miniAppid;

    @ApiModelProperty(value = "小程序路径")
    private String pagepath;

    @ApiModelProperty(value = "公众号素材id")
    private String thumbMediaId;

    @ApiModelProperty(value = "看片标题")
    private String title;
}
