package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.holder.saas.member.wechat.dto.member.MemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberInfoVolumeQuery;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTradeOrderDetailsDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.query.WxStorePendingOrdersQuery;
import com.holderzone.saas.store.weixin.execption.CorrectResult;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInBillClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreOrderPayServiceImpl
 * @date 2019/6/24
 */
@Slf4j
@Service
public class WxStoreOrderPayServiceImpl implements WxStoreOrderPayService {

	private final String VOLUME_CODE = "volumeCode";

	private final String CARD = "card";

	@Autowired
	private WxStoreSessionDetailsService wxStoreSessionDetailsService;

	@Autowired
	private WxStoreMenuDetailsService wxStoreMenuDetailsService;
	@Autowired
	private WxStoreTableClientService wxStoreTableClientService;

	@Autowired
	private WxStorePersonOrderDetailsService wxStorePersonOrderDetailsService;

	@Autowired
	private WxStoreDineInBillClientService wxStoreDineInBillClientService;

	@Autowired
	private WxStoreMerchantOrderService wxStoreMerchantOrderService;

	@Autowired
	private MemberClientService memberClientService;

	@Autowired
	private WxStoreDineInOrderClientService wxStoreDineInOrderClientService;

	@Autowired
	private HsaBaseClientService hsaBaseClientService;

	@Override
	public WxStoreTradeOrderDetailsRespDTO orderPay(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		// 验证订单信息
		CorrectResult<String> validationResult = validate(wxStoreAdvanceConsumerReqDTO);
		if (!ObjectUtils.isEmpty(validationResult.getErrorMsg())) {
			return WxStoreTradeOrderDetailsRespDTO.builder().errorMsg(validationResult.getErrorMsg()).build();
		}

		WxStoreConsumerDTO consumerDTO = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO();
		WxPrepayReqDTO prepay = getPrepay(consumerDTO);

		// 验证会员信息并计算订单详情
		Map<String, Object> memberValidationMap = validateMember(prepay, wxStoreAdvanceConsumerReqDTO);
		CorrectResult<DineinOrderDetailRespDTO> orderDetailsResult = calculateDetails(validationResult.getObj(), consumerDTO, prepay, wxStoreAdvanceConsumerReqDTO);

		if (!ObjectUtils.isEmpty(orderDetailsResult.getErrorMsg())) {
			return WxStoreTradeOrderDetailsRespDTO.builder().errorMsg(orderDetailsResult.getErrorMsg()).build();
		}

		DineinOrderDetailRespDTO orderDetails = orderDetailsResult.getObj();
		if (orderDetails.getTradeMode() == 0) {
			prepay.setVersion(orderDetails.getVersion());
		}

		WxStoreTradeOrderDetailsRespDTO responseDTO = processOrderPayment(orderDetails, wxStoreAdvanceConsumerReqDTO);
		applyMemberDiscount(prepay, responseDTO, memberValidationMap, orderDetails);
		return responseDTO;
	}

	// 获取预支付信息
	private WxPrepayReqDTO getPrepay(WxStoreConsumerDTO consumerDTO) {
		WxPrepayReqDTO prepay = wxStoreSessionDetailsService.getPrepay(consumerDTO.getStoreGuid(), consumerDTO.getOpenId());
		if (ObjectUtils.isEmpty(prepay)) {
			prepay = WxPrepayReqDTO.builder()
					.enterpriseGuid(consumerDTO.getEnterpriseGuid())
					.brandGuid(consumerDTO.getBrandGuid())
					.storeGuid(consumerDTO.getStoreGuid())
					.tableGuid(consumerDTO.getDiningTableGuid())
					.openId(consumerDTO.getOpenId())
					.build();
		}
		return prepay;
	}

	// 计算订单详情
	private CorrectResult<DineinOrderDetailRespDTO> calculateDetails(String orderGuid, WxStoreConsumerDTO consumerDTO, WxPrepayReqDTO prepay, WxStoreAdvanceConsumerReqDTO advanceConsumerReqDTO) {
		return wxStoreSessionDetailsService.calculateDetails(orderGuid, consumerDTO.getOpenId(), prepay.getMemberInfoCardGuid(), advanceConsumerReqDTO.getMemberIntegral(), prepay.getVolumeCode());
	}

	// 验证会员信息并计算订单详情
	private Map<String, Object> validateMember(WxPrepayReqDTO prepay, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		HashMap<String, Object> map = new HashMap<>();
		map.put(CARD, 1);
		map.put(VOLUME_CODE, 1);
		WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO();
		prepay.setMemberIntegral(wxStoreAdvanceConsumerReqDTO.getMemberIntegral());
		try {
			ResponseMemberAndCardInfoDTO memberInfoAndCardList = wxStoreSessionDetailsService.getMemberInfoAndCardList(wxStoreConsumerDTO.getEnterpriseGuid(), wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getOpenId());
			if (verifyMemberAvailability(memberInfoAndCardList)) return map;
			prepay.setMemberInfoGuid(memberInfoAndCardList.getMemberInfoDTO().getMemberInfoGuid());
			updateMemberCard(prepay, map, memberInfoAndCardList);
			updateVolumeCode(prepay, map, wxStoreConsumerDTO, memberInfoAndCardList.getMemberInfoDTO());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return map;
	}

	private void updateVolumeCode(WxPrepayReqDTO prepay, HashMap<String, Object> map, WxStoreConsumerDTO wxStoreConsumerDTO, ResponseMemberInfoDTO memberInfo) {
		if (ObjectUtils.isEmpty(prepay.getVolumeCode())) {
			RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
			memberInfoVolumeQueryReqDTO.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
			memberInfoVolumeQueryReqDTO.setBrandGuid(wxStoreConsumerDTO.getBrandGuid());
			memberInfoVolumeQueryReqDTO.setMemberInfoGuid(memberInfo.getMemberInfoGuid());
			memberInfoVolumeQueryReqDTO.setStoreGuid(wxStoreConsumerDTO.getStoreGuid());
			memberInfoVolumeQueryReqDTO.setVolumeType(-1);
			memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
			memberInfoVolumeQueryReqDTO.setExcludeBind(1);
			ResponseMemberInfoVolume memberVolume = hsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO).getData();
			log.info("查询用户有效的优惠券:{}", memberVolume);
			if (!ObjectUtils.isEmpty(memberVolume) && 0 < memberVolume.getMayUseVolumeNum()) {
					List<MemberInfoVolume> collect = memberVolume.getMemberVolumeList().stream()
							.filter(x -> 0 == x.getMayUseVolume()&&0==x.getVolumeState()&&3!=x.getVolumeType()).collect(Collectors.toList());
					if (!ObjectUtils.isEmpty(collect) && collect.size() == 1) {
						MemberInfoVolume memberInfoVolumeDTO = collect.get(0);
						if(!ObjectUtils.isEmpty(memberInfoVolumeDTO)&&!StringUtils.isEmpty(memberInfoVolumeDTO.getVolumeCode())){
							log.info("唯一选中的优惠券:{}",memberInfoVolumeDTO);
							prepay.setVolumeCode(memberInfoVolumeDTO.getVolumeCode());
							map.put(VOLUME_CODE, 2);
						}
				} else if(collect.size()>1) {
					map.put(VOLUME_CODE, 3);
				}
			}
		} else if("-1".equals(prepay.getVolumeCode())){
			map.put(VOLUME_CODE, 1);
		}else{
			map.put(VOLUME_CODE, 2);
		}
	}

	private void updateMemberCard(WxPrepayReqDTO prepay, HashMap<String, Object> map, ResponseMemberAndCardInfoDTO memberInfoAndCardList) {
		if (ObjectUtils.isEmpty(prepay.getMemberCardGuid())) {
			List<ResponseMemberCard> memberCardListRespDTOs = memberInfoAndCardList.getMemberCardListRespDTOs();
			if (memberCardListRespDTOs.size() == 1) {
				map.put(CARD, 2);
				ResponseMemberCard memberCardRespDTO = memberCardListRespDTOs.get(0);
				prepay.setMemberCardGuid(memberCardRespDTO.getCardGuid());
				prepay.setCardGuid(memberCardRespDTO.getCardGuid());
				prepay.setMemberInfoCardGuid(memberCardRespDTO.getMemberInfoCardGuid());
				prepay.setSystemManagementGuid(memberCardRespDTO.getSystemManagementGuid());
			} else {
				map.put(CARD, 3);
			}
		} else if("-1".equals(prepay.getMemberInfoCardGuid())){
			map.put(CARD, 1);
		} else{
			map.put(CARD, 2);
		}
	}

	private boolean verifyMemberAvailability(ResponseMemberAndCardInfoDTO memberInfoAndCardList) {
		if (ObjectUtils.isEmpty(memberInfoAndCardList)||ObjectUtils.isEmpty(memberInfoAndCardList.getMemberInfoDTO())||ObjectUtils.isEmpty(memberInfoAndCardList.getMemberInfoDTO().getMemberInfoGuid())) {
			return true;
		}
		ResponseMemberInfoDTO memberInfoDTO = memberInfoAndCardList.getMemberInfoDTO();
		return ObjectUtils.isEmpty(memberInfoDTO) || memberInfoDTO.getStateCode() == 0 ||ObjectUtils.isEmpty(memberInfoAndCardList.getMemberCardListRespDTOs());
	}

	// 处理订单支付
	private WxStoreTradeOrderDetailsRespDTO processOrderPayment(DineinOrderDetailRespDTO orderDetails, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxStoreTradeOrderDetailsDTO tradeOrderDetails = getWxStoreTradeOrderDetails(orderDetails);
		tradeOrderDetails.setWxStoreAdvanceConsumerReqDTO(wxStoreAdvanceConsumerReqDTO);
		return wxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(orderDetails, tradeOrderDetails, wxStoreAdvanceConsumerReqDTO);
	}

	// 应用会员折扣
	private void applyMemberDiscount(WxPrepayReqDTO prepay, WxStoreTradeOrderDetailsRespDTO responseDTO, Map<String, Object> memberValidationMap, DineinOrderDetailRespDTO orderDetails) {
		if (!ObjectUtils.isEmpty(responseDTO)) {
			Integer volumeStatus = (Integer) memberValidationMap.get(VOLUME_CODE);
			Integer cardStatus = (Integer) memberValidationMap.get(CARD);
			responseDTO.setMemberVolumeStatus(volumeStatus);
			responseDTO.setMemberCardStatus(cardStatus);

			List<DiscountFeeDetailDTO> discountDetails = orderDetails.getDiscountFeeDetailDTOS();
			responseDTO.setSaveZero(discountDetails.stream()
					.filter(x -> 4 == x.getDiscountType())
					.map(DiscountFeeDetailDTO::getDiscountFee)
					.findAny()
					.orElse(BigDecimal.ZERO));

			prepay.setPurchaseGroupFee(discountDetails.stream()
					.filter(x -> x.getDiscountType() == 6)
					.findFirst()
					.get()
					.getDiscountFee());

			handleCouponDiscount(prepay, responseDTO, memberValidationMap, discountDetails, volumeStatus, orderDetails);
			handleMemberCardDiscount(prepay, responseDTO, discountDetails, cardStatus);

			log.info("折扣详情: {}", discountDetails);
			calculateAndSetDiscountFee(responseDTO, discountDetails, orderDetails);
			savePrepayInfo(prepay, orderDetails);
		}
	}

	// 处理优惠券折扣
	private void handleCouponDiscount(WxPrepayReqDTO prepay, WxStoreTradeOrderDetailsRespDTO responseDTO, Map<String, Object> memberValidationMap, List<DiscountFeeDetailDTO> discountDetails, Integer volumeStatus, DineinOrderDetailRespDTO orderDetails) {
		Optional<BigDecimal> firstVolumeDiscount = discountDetails.stream()
				.filter(x -> 7 == x.getDiscountType())
				.map(x -> Optional.ofNullable(x.getDiscountFee()).orElse(BigDecimal.ZERO))
				.findFirst();

		if (volumeStatus.equals(2) && firstVolumeDiscount.isPresent() && firstVolumeDiscount.get().compareTo(BigDecimal.ZERO) == 0) {
			memberValidationMap.put(VOLUME_CODE, 1);
			responseDTO.setMemberVolumeStatus(1);
			responseDTO.setMemberVolumeFee(BigDecimal.ZERO);
			prepay.setVolumeCode(null);
			log.info("默认优惠券不能使用: {}", orderDetails.getTip());
		}

		if (volumeStatus == 2) {
			responseDTO.setVolumeCode(prepay.getVolumeCode());
			responseDTO.setMemberVolumeFee(discountDetails.stream()
					.filter(x -> 7 == x.getDiscountType())
					.map(DiscountFeeDetailDTO::getDiscountFee)
					.findFirst()
					.orElse(BigDecimal.ZERO));
		}
	}

	// 处理会员卡折扣
	private void handleMemberCardDiscount(WxPrepayReqDTO prepay, WxStoreTradeOrderDetailsRespDTO responseDTO, List<DiscountFeeDetailDTO> discountDetails, Integer cardStatus) {
		if (cardStatus == 2) {
			responseDTO.setCardGuid(prepay.getCardGuid());
			responseDTO.setCardName(prepay.getCardGuid());
			responseDTO.setMemberInfoCardGuid(prepay.getMemberInfoCardGuid());
			responseDTO.setSystemManagementGuid(prepay.getSystemManagementGuid());
			responseDTO.setMemberIntegral(prepay.getMemberIntegral());
			responseDTO.setMemberCardFee(discountDetails.stream()
					.filter(x -> 1 == x.getDiscountType())
					.map(DiscountFeeDetailDTO::getDiscountFee)
					.findFirst()
					.orElse(BigDecimal.ZERO));
		}
	}

	// 计算并设置折扣费用
	private void calculateAndSetDiscountFee(WxStoreTradeOrderDetailsRespDTO responseDTO, List<DiscountFeeDetailDTO> discountDetails, DineinOrderDetailRespDTO orderDetails) {
		BigDecimal memberFee = discountDetails.stream()
				.filter(x -> Arrays.asList(1, 7, 8).contains(x.getDiscountType()))
				.map(x -> Optional.ofNullable(x.getDiscountFee()).orElse(BigDecimal.ZERO))
				.reduce(BigDecimal::add)
				.orElse(BigDecimal.ZERO);

		BigDecimal unMemberFee = orderDetails.getDiscountFee().subtract(memberFee);
		responseDTO.setDiscountFee(unMemberFee);

		responseDTO.setUnMemberTotalAmount(orderDetails.getActuallyPayFee().add(memberFee));

		List<DiscountFeeDetailDTO> nonMemberDiscounts = responseDTO.getDiscountFeeDetailDTOS().stream()
				.filter(x -> !Arrays.asList(1, 7, 8).contains(x.getDiscountType()))
				.collect(Collectors.toList());
		responseDTO.setDiscountFeeDetailDTOS(nonMemberDiscounts);
	}

	// 保存预支付信息
	private void savePrepayInfo(WxPrepayReqDTO prepay, DineinOrderDetailRespDTO orderDetails) {
		prepay.setOrderGuid(orderDetails.getGuid());
		prepay.setMode(orderDetails.getTradeMode());
		prepay.setPayAmount(orderDetails.getActuallyPayFee());
		prepay.setOrderGuid(orderDetails.getGuid());
		prepay.setOrderFee(orderDetails.getOrderFee());

		wxStoreSessionDetailsService.savePrepay(prepay);
	}

	/**
	 * 用户拦截
	 */
	private CorrectResult<String> validate(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO();
		String orderGuid = null;
		try {
			if (wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO)) {
				orderGuid = wxStoreTableClientService.getOrderGuid(wxStoreConsumerDTO.getDiningTableGuid());
				if (ObjectUtils.isEmpty(orderGuid))
					return CorrectResult.<String>builder().errorMsg("有订单商家还未处理，无法买单哦~").build();
				DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder().data(orderGuid).build());
				log.info("正餐买单前校验:{}",orderDetail);
				if (!ObjectUtils.isEmpty(orderDetail) && orderDetail.getOrderNo().startsWith("F")) {
					return CorrectResult.<String>builder().errorMsg("此订单已反结账，请联系商家处理!").build();
				}
				List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = wxStoreMerchantOrderService
						.tableListByMerchantGuidOrOrderGuid(WxStorePendingOrdersQuery.builder().combine(orderGuid)
						.orderGuid(wxStoreSessionDetailsService.getMerchantBatchGuid(wxStoreConsumerDTO.getDiningTableGuid())).build());
				if(ObjectUtils.isEmpty(wxStoreAdvanceConsumerReqDTO)||wxStoreMerchantOrderDOS.stream().anyMatch(x->0==x.getOrderState()))
					return CorrectResult.<String>builder().errorMsg("还有未处理的订单，无法买单").build();
				wxStoreSessionDetailsService.saveOrderGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid(), orderGuid);
			} else {
				orderGuid = wxStoreSessionDetailsService.getFastOrderGuid(wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getOpenId());
				if (ObjectUtils.isEmpty(orderGuid))
					return CorrectResult.<String>builder().errorMsg("当前订单已过期").build();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return CorrectResult.<String>builder().result(0).obj(orderGuid).build();
	}

	private WxStoreTradeOrderDetailsDTO getWxStoreTradeOrderDetails(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
		return wxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(dineinOrderDetailRespDTO);
	}
}
