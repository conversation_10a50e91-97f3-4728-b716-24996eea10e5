package com.holderzone.saas.store.weixin.utils;


import com.google.common.io.Files;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ZipUtil
 * @date 2019/03/15 9:30
 * @description zip打包工具类
 * @program holder-saas-store
 */
public class ZipUtil {
    private static final int UNIT = 2048;

    /**
     * 压缩文件(夹)并生成指定文件
     *
     * @param fileName
     * @param destFile
     * @throws IOException
     */
    public static void compressAndSave(String fileName, String destFile) throws IOException {
        File file = new File(fileName);
        List<File> files = listFiles(file);
        String dir = StringUtils.EMPTY;
        if (file.isDirectory()) {
            dir = fileName;
        }
        byte[] bytes = compress(dir, files);
        File df = new File(destFile);
        df.getParentFile().mkdirs();
        Files.write(bytes, df);
    }

    /**
     * 获取压缩byte[]
     *
     * @param fileName
     * @return
     * @throws IOException
     */
    public static byte[] compressByFile(String fileName) throws IOException {

        File file = new File(fileName);
        List<File> files = listFiles(file);
        String dir = StringUtils.EMPTY;
        if (file.isDirectory()) {
            dir = fileName;
        }
        return compress(dir, files);
    }

    /**
     * 压缩多个文件
     *
     * @throws IOException
     */
    public static byte[] compress(List<StreamEntry> entries) throws IOException {
        ZipOutputStream zos = null;
        try {
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            zos = new ZipOutputStream(arrayOutputStream);

            for (StreamEntry streamEntry : entries) {
                // 创建Zip条目
                ZipEntry entry = new ZipEntry(streamEntry.getName());
                entry.setTime(1);
                zos.putNextEntry(entry);

                InputStream input = streamEntry.getInputStream();

                byte[] b = new byte[UNIT];
                int len = 0;
                while ((len = input.read(b)) != -1) {
                    zos.write(b, 0, len);
                }
                zos.closeEntry();
                input.close();
            }
            zos.finish();
            return arrayOutputStream.toByteArray();
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     * 压缩文件(夹)
     *
     * @param fileName
     * @return
     * @throws IOException
     */
    public static byte[] compress(String fileName) throws IOException {
        File file = new File(fileName);
        List<File> files = listFiles(file);
        String dir = StringUtils.EMPTY;
        if (file.isDirectory()) {
            dir = fileName;
        }
        return compress(dir, files);
    }

    private static List<File> listFiles(File file) {
        List<File> list = new ArrayList<>();
        if (!file.isDirectory()) {
            list.add(file);
            return list;
        }
        File[] files = file.listFiles();
        for (File f : files) {
            list.addAll(listFiles(f));
        }
        return list;
    }

    private static byte[] compress(String dir, List<File> files) throws IOException {
        ZipOutputStream zos = null;
        BufferedInputStream bis = null;
        try {
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            zos = new ZipOutputStream(arrayOutputStream);
            for (File file : files) {
                String entryName = getEntryName(dir, file.getAbsolutePath());

                // 创建Zip条目
                ZipEntry entry = new ZipEntry(entryName);
                entry.setTime(1);
                zos.putNextEntry(entry);

                bis = new BufferedInputStream(new FileInputStream(file));

                byte[] b = new byte[UNIT];
                int len = 0;
                while ((len = bis.read(b)) != -1) {
                    zos.write(b, 0, len);
                }
                zos.closeEntry();
                bis.close();
            }
            zos.finish();
            return arrayOutputStream.toByteArray();
        } catch (IOException e) {
            throw e;
        }finally {
            assert bis != null;
            bis.close();
            zos.close();
        }
    }

    // 解析文件名
    private static String getEntryName(String dir, String srcFile) {
        if (StringUtils.isBlank(dir)) {
            int location = srcFile.lastIndexOf("/");
            String fileName = srcFile.substring(location + 1);
            return fileName;
        }
        String entryName = srcFile.replace(dir, StringUtils.EMPTY);
        while (entryName.startsWith("\\") || entryName.startsWith("/")) {
            entryName = entryName.substring(1);
        }
        return entryName;
    }

    public static class StreamEntry {
        private String name;
        private InputStream inputStream;

        public StreamEntry() {
        }

        public StreamEntry(String name, InputStream inputStream) {
            this.name = name;
            this.inputStream = inputStream;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public InputStream getInputStream() {
            return inputStream;
        }

        public void setInputStream(InputStream inputStream) {
            this.inputStream = inputStream;
        }

    }

}
