package com.holderzone.saas.store.weixin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxOrderRecordMapper
 * @date 2019/4/3
 */
@Repository
public interface WxOrderRecordMapper extends BaseMapper<WxOrderRecordDO> {


}
