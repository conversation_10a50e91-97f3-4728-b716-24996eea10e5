package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.OrderLockDTO;
import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.request.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayLockReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderMemberInfoReqDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 远程调用订单服务
 * @className WxStoreDineInOrderClientService
 * @date 2019/3/7
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = WxStoreDineInOrderClientService.WxStoreDineInOrderFullback.class)
public interface WxStoreDineInOrderClientService {

    String URL_FAST_PREFIX = "/fast_food";

    @PostMapping("/dine_in_order/create")
    CreateDineInOrderReqDTO creatOrder(CreateDineInOrderReqDTO creatOrderReqDTO);

    @PostMapping("/dine_in_order/batch_get_table_info")
    List<OrderTableInfoDTO> batchGetTableInfo(OrderGuidsDTO orderGuidsDTO);

    @PostMapping("/dine_in_order/update_remark")
    Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping("/dine_in_order/update_guest_count")
    Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping("/dine_in_order/cancel")
    Boolean cancelOrder(CancelOrderReqDTO cancelOrderReqDTO);

    @PostMapping("/dine_in_order/get_order_detail")
    DineinOrderDetailRespDTO getOrderDetail(SingleDataDTO singleDataDTO);

    @PostMapping("/dine_in_order/get_order_detail_for_wx")
    DineinOrderDetailRespDTO getOrderDetailForWx(OrderDetailQueryDTO orderDetailQueryDTO);

    /**
     * 获取订单详情
     */
    @GetMapping("/dine_in_order/get_order_detail/{orderGuid}")
    DineinOrderDetailRespDTO getOrderDetail(@PathVariable("orderGuid") String orderGuid);

    @PostMapping("/dine_in_order/print_pre_bill")
    Boolean printPreBill(SingleDataDTO singleDataDTO);

    @PostMapping("/dine_in_order/order_list")
    Page<DineInOrderListRespDTO> orderList(DineInOrderListReqDTO dineInOrderListReqDTO);

    @PostMapping("/we_chat/pay")
    Boolean pay(WeChatPayReqDTO weChatPayReqDTO);

    @PostMapping("/we_chat/prepay")
    Boolean prepay(@RequestBody WeChatPayLockReqDTO WeChatPayLockReqDTO);

    @PostMapping("/dine_in_order/order/lock")
    boolean lockorder(@RequestBody OrderLockDTO orderLockDto);

    @ApiOperation(value = "去掉订单锁", notes = "去掉订单锁")
    @PostMapping("/dine_in_order/order/unlock")
    void unLockOrder(@RequestBody OrderLockDTO orderLockDto);

    @ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
    @PostMapping("/we_chat/get_order")
    OrderWechatDTO getOrder(@RequestBody String orderGuid);

    @ApiOperation(value = "根据guid更新订单备注", notes = "根据guid更新订单备注")
    @PutMapping("/order_detail/update_remark/{guid}")
    boolean updateOrderRemarkById(@PathVariable("guid") String guid, @RequestParam("remark") String remark);

    @ApiOperation(value = "根据guid更新订单商品备注", notes = "根据guid更新订单商品备注")
    @PutMapping("/order_detail/update_item_remark/{guid}")
    boolean updateOrderItemRemarkById(@PathVariable("guid") String guid,
                                      @RequestParam("itemGuid") String itemGuid,
                                      @RequestParam("remark") String remark);

    /**
     * 下面是快餐
     */
    @PostMapping("/fast_food/add_item")
    EstimateItemRespDTO addItem(CreateFastFoodReqDTO createFastFoodReqDTO);

    @PostMapping("/order_item/add_item")
    EstimateItemRespDTO batchAddItems(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @ApiOperation(value = "更新订单会员信息")
    @PostMapping("/order_detail/update_order_member_info")
    void updateOrderMemberInfo(@RequestBody UpdateOrderMemberInfoReqDTO reqDTO);

    @ApiOperation(value = "计算账单优惠", notes = "计算账单优惠")
    @PostMapping("/dine_in_bill/calculate")
    DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO);

    @Component
    @Slf4j
    class WxStoreDineInOrderFullback implements FallbackFactory<WxStoreDineInOrderClientService> {
        @Override
        public WxStoreDineInOrderClientService create(Throwable throwable) {
            return new WxStoreDineInOrderClientService() {

                @Override
                public CreateDineInOrderReqDTO creatOrder(CreateDineInOrderReqDTO creatOrderReqDTO) {
                    throwable.printStackTrace();
                    log.error("远程创建订单失败", JacksonUtils.writeValueAsString(throwable),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OrderTableInfoDTO> batchGetTableInfo(OrderGuidsDTO orderGuidsDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean cancelOrder(CancelOrderReqDTO cancelOrderReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public DineinOrderDetailRespDTO getOrderDetail(SingleDataDTO singleDataDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public DineinOrderDetailRespDTO getOrderDetailForWx(OrderDetailQueryDTO orderDetailQueryDTO) {
                    log.error("查询订单详情失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("查询订单详情失败!!" + throwable.getMessage());
                }

                @Override
                public DineinOrderDetailRespDTO getOrderDetail(String orderGuid) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean printPreBill(SingleDataDTO singleDataDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Page<DineInOrderListRespDTO> orderList(DineInOrderListReqDTO dineInOrderListReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean pay(WeChatPayReqDTO weChatPayReqDTO) {
                    log.error("微信支付回调trade失败：{}", weChatPayReqDTO);
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean prepay(WeChatPayLockReqDTO WeChatPayLockReqDTO) {
                    log.error("支付前校验失败:{}", WeChatPayLockReqDTO);
                    throw new BusinessException("支付前校验失败");
                }

                @Override
                public boolean lockorder(OrderLockDTO orderLockDto) {
                    log.error("加锁失败:{}", orderLockDto);
                    throw new BusinessException("加锁失败");
                }

                @Override
                public void unLockOrder(OrderLockDTO orderLockDto) {
                    log.error("解锁失败:{}", orderLockDto);
                    throw new BusinessException("解锁失败");
                }

                @Override
                public OrderWechatDTO getOrder(String orderGuid) {
                    log.error("查询订单状态失败:{}", orderGuid);
                    return null;
                }

                @Override
                public boolean updateOrderRemarkById(String guid, String remark) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public boolean updateOrderItemRemarkById(String guid, String itemGuid, String remark) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public EstimateItemRespDTO addItem(CreateFastFoodReqDTO createFastFoodReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public EstimateItemRespDTO batchAddItems(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void updateOrderMemberInfo(UpdateOrderMemberInfoReqDTO reqDTO) {
                    log.error("更新订单会员信息失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO) {
                    log.error("计算账单优惠失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }
            };
        }
    }
}
