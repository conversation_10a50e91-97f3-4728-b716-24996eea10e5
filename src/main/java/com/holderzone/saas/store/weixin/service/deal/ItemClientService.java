package com.holderzone.saas.store.weixin.service.deal;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.weixin.resp.ItemImgDTO;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxItemClientService
 * @date 2019/01/23 14:27
 * @description //TODO
 * @program ${MODULE_NAME}
 */
@Component
@FeignClient(name = "holder-saas-store-item", fallbackFactory = ItemClientService.WxItemFallBack.class)
public interface ItemClientService {

    @PostMapping("/item/query_for_synchronize")
    ItemAndTypeForAndroidRespDTO getItemsForWeixin(@RequestBody BaseDTO baseDTO);

    @PostMapping(value = "/estimate/query_estimate_for_synchronize")
    List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(@RequestBody BaseDTO baseDTO);

    @PostMapping(value = "/estimate/dinein_fail")
    Boolean dineinFail(@RequestBody List<DineInItemDTO> request);

    @PostMapping(value = "/item/get_item_picture_urls")
    List<ItemImgDTO> getItemPictureUrls(@RequestBody SingleDataDTO singleDataDTO);

    @Slf4j
    @Component
    class WxItemFallBack implements FallbackFactory<ItemClientService> {
        @Override
        public ItemClientService create(Throwable throwable) {
            return new ItemClientService() {
                @Override
                public ItemAndTypeForAndroidRespDTO getItemsForWeixin(BaseDTO baseDTO) {
                    log.error("获取商品信息失败", throwable.getMessage());
                    ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
                    itemAndTypeForAndroidRespDTO.setItemList(Collections.emptyList());
                    itemAndTypeForAndroidRespDTO.setTypeList(Collections.emptyList());
                    return itemAndTypeForAndroidRespDTO;
                }

                @Override
                public List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(BaseDTO baseDTO) {
                    log.error("查询估清失败:{}", baseDTO);
                    return Collections.emptyList();
                }

                @Override
                public Boolean dineinFail(List<DineInItemDTO> request) {
                    log.error("退还库存失败:{}", request);
                    return false;
                }

                @Override
                public List<ItemImgDTO> getItemPictureUrls(SingleDataDTO singleDataDTO) {
                    log.error("查询商品图片失败");
                    return Collections.emptyList();
                }
            };
        }
    }
}
