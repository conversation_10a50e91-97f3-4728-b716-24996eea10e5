package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.weixin.req.WxReserveConfigDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.weixin.service.WxReserveConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxReserveConfigController
 * @date 2019/12/17 10:10
 * @description
 * @program holder-saas-store
 */
@RestController
@RequestMapping("/wx_reserve")
@Slf4j
public class WxReserveConfigController {

    private final WxReserveConfigService wxReserveConfigService;

    @Autowired
    public WxReserveConfigController(WxReserveConfigService wxReserveConfigService) {
        this.wxReserveConfigService = wxReserveConfigService;
    }

    @PostMapping("/list_config")
    public Page<WxReserveConfigDTO> listConfig(@RequestBody WxStorePageReqDTO wxStorePageReqDTO) {
        return wxReserveConfigService.listConfig(wxStorePageReqDTO);
    }

    @PostMapping("/update_config")
    public Boolean updateConfig(@RequestBody WxReserveConfigDTO wxReserveConfigDTO) {
        return wxReserveConfigService.updateConfig(wxReserveConfigDTO);
    }

    @PostMapping("/get_config")
    public WxReserveConfigDTO getConfig(@RequestBody SingleDataDTO singleDataDTO) {
        return wxReserveConfigService.getConfig(singleDataDTO.getData());
    }

    @PostMapping("/store")
	public WxReserveConfigDTO obtainByStore(@RequestParam String storeGuid) {
		log.info("根据门店id查询预订配置:{}",storeGuid);
		return wxReserveConfigService.store(storeGuid);
	}

	@PostMapping("/store_list")
	public List<WxReserveConfigDTO> storeList(@RequestParam List<String> storeGuidList) {
    	log.info("根据门店id查询预订配置:{}",storeGuidList);
		return wxReserveConfigService.storeList(storeGuidList);
	}
}
