package com.holderzone.saas.store.weixin.service.rpc.member;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberClientService
 * @date 2019/06/25 13:45
 * @description 会员服务Client
 * @program holder-saas-store
 */
@Service
@FeignClient(name = "holder-saas-cmember-wechat-base-service"
		//,url = "http://***************:5030"
		,fallbackFactory = MemberClientService.FallBackClass.class)
public interface MemberClientService {
//    /**
//     * 判断会员是否可登录
//     *
//     * @param hsmMemberBasicReqDTO
//     * @return
//     */
//    @PostMapping("/hsmcw/member/registerOrLoginMemberInfoThirdWechat")
//    WechatLoginResultRespDTO registerOrLoginMemberInfoThirdWechat(@RequestBody HsmMemberBasicReqDTO hsmMemberBasicReqDTO);

//    /**
//     * 判断会员是否被禁用MenuItemServiceImpl
//     *
//     * @param phoneNumOrOpenid
//     * @param enterpriseGuid
//     * @return
//     */
//    @GetMapping("/hsmcw/member/getMemberState")
//    AccountStatusRespDTO getMemberState(@RequestParam("phoneNumOrOpenid") String phoneNumOrOpenid, @RequestParam("enterpriseGuid") String enterpriseGuid);


//	@ApiOperation(value = "查询会员信息")
//	@GetMapping("/hsmcw/member/getMemberInfo")
//	HsmMemberInfoAndQrCodeRespDTO getMemberInfo(@ApiParam(name = "phoneNumOrOpenid", value = "会员电话号码或微信OpenId", required = true) @RequestParam(value = "phoneNumOrOpenid", required = true) String phoneNumOrOpenid,
//												@ApiParam(name = "enterpriseGuid", value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid);


//	@ApiOperation(value = "获取优惠券券码")
//	@GetMapping("/hsmcw/member/getVolumeQrcode")
//	String getVolumeQrcode(@ApiParam(name = "volumeCode", value = "优惠券券码", required = true) @RequestParam(value = "volumeCode", required = true) String volumeCode, @ApiParam(name = "type", value = "券码返回类型，0只返回条形码，1返回条形码以及下方券码内容", required = true) @RequestParam(value = "type") Integer type);

//	@PostMapping(value = "/hsmcw/card/getMemberCard")
//	MemberCardRespDTO getMemberCard(@RequestBody MemberCardListQueryReqDTO memberCardListQueryReqDTO);

//	@PostMapping(value = "/hsmcw/volume/getMemberVolume")
//	MemberInfoVolumeRespDTO getMemberVolume(@RequestBody MemberInfoVolumeQueryReqDTO memberInfoVolumeQueryReqDTO);
	
//	@PostMapping(value = "/hsmcw/volume/getMemberVolumeNumber")
//	Integer getMemberValidVolumeNumber(@RequestBody MemberInfoVolumeQueryReqDTO memberInfoVolumeQueryReqDTO);

//	@PostMapping(value = "/hsmcw/volume/getMemberVolume")
//	WxMemberVolumeInfoListRespDTO getMemberVolume2(@RequestBody MemberInfoVolumeQueryReqDTO memberInfoVolumeQueryReqDTO);

//	@PostMapping(value = "/hsmcw/card/getMemberCardSummaryInfo")
//	MemberCardSummaryRespDTO getMemberCardSummaryInfo(@RequestBody MemberCardSummaryQueryReqDTO memberCardSummaryQueryReqDTO);

//	@PostMapping(value = "/hsmcw/card/getCardRightDetails")
//	List<CardRightDetailsRespDTO> getCardRightDetails(@RequestBody CardRightDetailsReqDTO cardRightDetailsReqDTO);

//	@RequestMapping(value = "/hsmcw/volume/getMemberVolumeDetails")
//	MemberInfoVolumeDetailsRespDTO getMemberVolumeDetails(@RequestParam("memberVolumeGuid") String memberVolumeGuid);

//	@RequestMapping(value = "/hsmcw/volume/getMemberVolumeDetails")
//	WxMemberInfoVolumeDetailsRespDTO getMemberVolumeDetails2(@RequestParam("memberVolumeGuid") String memberVolumeGuid);

//	@PostMapping(value = "/hsmcw/volume/getEnterpriseStoreList")
//	StoreListRespDTO getMemberStoreList(@RequestBody StoreListReqDTO storeListReqDTO);

//	@PostMapping(value = "/hsmcw/volume/getVolumeStoreAndProduct")
//	List<VolumeStoreAndProductRespDTO> getUnableItem(@RequestBody VolumeStoreAndCardReqDTO volumeStoreAndCardReqDTO);

//	@PostMapping(value = "/hsmcw/volume/getVolumeStoreAndProduct")
//	List<WxVolumeStoreAndProductRespDTO> getUnableProductItem(@RequestBody VolumeStoreAndCardReqDTO volumeStoreAndCardReqDTO);

//	@PostMapping("/hsmcw/card/default/info")
//	MemberDefaultCardInfoRespDTO memberDefaultCard(@RequestBody DefaultCardQueryReqDTO defaultCardQueryReqDTO);
	
	//XW 2019/10/21 18:27:32
	
	
//	@GetMapping("/hsmcw/card/default")
//	MemberCardListOwnedRespDTO getDefaultCard(@RequestParam("brandGuid") String brandGuid,@RequestParam("enterpriseGuid") String enterpriseGuid);

//	/**
//	 * 开通会员卡
//	 */
//	@PostMapping(value = "/hsmcw/card/openMemberCard")
//	boolean openMemberCard(@RequestBody MemberCardOpenReqDTO memberCardOpenReqDTO);

//	@ApiOperation("分页查找会员卡及未开通个卡信息")
//	@PostMapping("/hsmcw/card/getMemberCardByPage")
//	List<UserMemberSessionCardItemDTO> getMemberCardByPage(@RequestBody @Validated MemberCardsOwnedPageReqDTO memberCardsOwnedPageReqDTO);


//	@ApiOperation("分页查询优惠券")
//	@PostMapping("/hsmcw/volume/getMemberVolumeByPage")
//	VolumePageRespDTO getMemberVolumeByPage(@RequestBody MemberInfoVolumeQueryReqDTO memberInfoVolumeQueryReqDTO,
//											@RequestParam("pageNo") Integer pageNo,
//											@RequestParam("pageSize") Integer pageSize);


//	@ApiOperation("查看会员卡余额配置规则")
//	@GetMapping("/hsmcw/product/getDiscountProducts")
//	ProductDiscountRespDTO getDiscountProducts(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid);

//	@ApiOperation("根据会员id查询openId")
//	@GetMapping("/hsmcw/member/findOpenIDByMemberGuid")
//	String findOpenIDByMemberGuid(@RequestParam("memberGuid")String memberGuid
//			, @RequestParam("enterpriseGuid")String enterpriseGuid);


	@Component
    @Slf4j
    class FallBackClass implements FallbackFactory<MemberClientService> {
        @Override
        public MemberClientService create(Throwable throwable) {
            return new MemberClientService() {
                private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

//                @Override
//                public WechatLoginResultRespDTO registerOrLoginMemberInfoThirdWechat(HsmMemberBasicReqDTO hsmMemberBasicReqDTO) {
//                    log.error(HYSTRIX_PATTERN, "checkLogin",
//                            JacksonUtils.writeValueAsString(hsmMemberBasicReqDTO),
//                            ThrowableUtils.asString(throwable));
//                    WechatLoginResultRespDTO wechatLoginResultRespDTO = new WechatLoginResultRespDTO();
//                    wechatLoginResultRespDTO.setFailReason(BusinessName.GLOBAL_DEFAULT_ERROR_MSG);
//                    return wechatLoginResultRespDTO;
//                }
//
//                @Override
//                public AccountStatusRespDTO getMemberState(String phoneNumOrOpenid, String enterpriseGuid) {
//                    log.error(HYSTRIX_PATTERN, "checkState",
//                            "phoneNumOrOpenid: " + phoneNumOrOpenid + ", enterpriseGuid: " + enterpriseGuid,
//                            ThrowableUtils.asString(throwable));
//                    AccountStatusRespDTO accountStatusRespDTO = new AccountStatusRespDTO();
//                    accountStatusRespDTO.setReasonForDisable(BusinessName.GLOBAL_DEFAULT_ERROR_MSG);
//                    accountStatusRespDTO.setAccountState(2);
//                    return accountStatusRespDTO;
//                }

//				@Override
//				public HsmMemberInfoAndQrCodeRespDTO getMemberInfo(String phoneNumOrOpenid,String enterpriseGuid) {
//					log.error("查询会员信息失败,phoneNumOrOpenid:{},enterpriseGuid:{},error:{}"
//							,phoneNumOrOpenid,enterpriseGuid,throwable.getMessage());
//					//throw new BusinessException("查询会员失败:"+ throwable.getMessage());
//					return null;
//				}
//
//				@Override
//				public String getVolumeQrcode(String volumeCode, Integer type) {
//					log.info("查询优惠券码失败,volumeCode:{},type:{}",volumeCode,type);
//					throw new BusinessException("查询会员失败:"+ throwable.getMessage());
//				}

//				@Override
//				public MemberCardRespDTO getMemberCard(MemberCardListQueryReqDTO memberCardListQueryReqDTO) {
//					log.error("查询会员所有会员卡失败:{}",memberCardListQueryReqDTO);
//					return null;
//				}

//				@Override
//				public MemberInfoVolumeRespDTO getMemberVolume(MemberInfoVolumeQueryReqDTO memberInfoVolumeQueryReqDTO) {
//					log.error("查询会员优惠券失败:{}",memberInfoVolumeQueryReqDTO);
//					throw new BusinessException("查询会员优惠券失败:" + throwable.getMessage());
//				}

//				@Override
//				public WxMemberVolumeInfoListRespDTO getMemberVolume2(MemberInfoVolumeQueryReqDTO memberInfoVolumeQueryReqDTO) {
//					log.error("查询会员优惠券失败:{}",memberInfoVolumeQueryReqDTO);
//					throw new BusinessException("查询会员优惠券失败:" + throwable.getMessage());
//				}
//
//				@Override
//				public MemberCardSummaryRespDTO getMemberCardSummaryInfo(MemberCardSummaryQueryReqDTO memberCardSummaryQueryReqDTO) {
//					log.error("查询会员卡概要失败:{}", memberCardSummaryQueryReqDTO);
//					throw new BusinessException("查询会员卡概要失败:" + throwable.getMessage());
//				}

//				@Override
//				public List<CardRightDetailsRespDTO> getCardRightDetails(CardRightDetailsReqDTO cardRightDetailsReqDTO) {
//					log.error("查询会员权益失败:{}",cardRightDetailsReqDTO);
//					throw new BusinessException(throwable.getMessage());
//				}

//				@Override
//				public MemberInfoVolumeDetailsRespDTO getMemberVolumeDetails(String memberVolumeGuid) {
//					log.error("查询优惠券失败:{}",memberVolumeGuid);
//					throw new BusinessException(throwable.getMessage());
//				}
//
//				@Override
//				public WxMemberInfoVolumeDetailsRespDTO getMemberVolumeDetails2(String memberVolumeGuid) {
//					log.error("查询优惠券失败:{}",memberVolumeGuid);
//					throw new BusinessException(throwable.getMessage());
//				}
//
//				@Override
//				public StoreListRespDTO getMemberStoreList(StoreListReqDTO storeListReqDTO) {
//					log.error("查询门店列表转换:{}",storeListReqDTO);
//					throw new BusinessException(throwable.getMessage());
//				}

//				@Override
//				public List<VolumeStoreAndProductRespDTO> getUnableItem(VolumeStoreAndCardReqDTO volumeStoreAndCardReqDTO) {
//					log.error("查询优惠券适用商品失败:{}",volumeStoreAndCardReqDTO);
//					throw new BusinessException(throwable.getMessage());
//				}

//				@Override
//				public List<WxVolumeStoreAndProductRespDTO> getUnableProductItem(VolumeStoreAndCardReqDTO volumeStoreAndCardReqDTO) {
//					log.error("查询优惠券适用商品失败:{}",volumeStoreAndCardReqDTO);
//					throw new BusinessException(throwable.getMessage());
//				}

//				@Override
//				public MemberDefaultCardInfoRespDTO memberDefaultCard(DefaultCardQueryReqDTO defaultCardQueryReqDTO) {
//					log.error("查询默认会员卡失败:{}",defaultCardQueryReqDTO);
//					throw new BusinessException(throwable.getMessage());
//				}

//				@Override
//				public MemberCardListOwnedRespDTO getDefaultCard(String brandGuid, String enterpriseGuid) {
//					log.error("查询商家配置的默认卡失败:brandGuid:{},enterpriseGuid:{}",brandGuid,enterpriseGuid);
//					 throw new ServerException();
//				}

//				@Override
//				public boolean openMemberCard(MemberCardOpenReqDTO memberCardOpenReqDTO) {
//					log.error("开通会员卡失败:{}",memberCardOpenReqDTO);
//					throw new BusinessException("开通会员卡失败");
//				}
//
//				@Override
//				public List<UserMemberSessionCardItemDTO> getMemberCardByPage(MemberCardsOwnedPageReqDTO memberCardsOwnedPageReqDTO) {
//					log.error("会员卡分页查询失败:{}",memberCardsOwnedPageReqDTO,throwable);
//					return null;
//				}
//
//
//				@Override
//				public VolumePageRespDTO getMemberVolumeByPage(MemberInfoVolumeQueryReqDTO memberInfoVolumeQueryReqDTO, Integer pageNo, Integer pageSize) {
//					log.error("优惠券分页查询失败:{}",memberInfoVolumeQueryReqDTO);
//					return null;
//				}
//
//				@Override
//				public ProductDiscountRespDTO getDiscountProducts(String memberInfoCardGuid) {
//					log.error("查询会员匹配规则失败:{}",memberInfoCardGuid);
//					return null;
//				}
//
//				@Override
//				public String findOpenIDByMemberGuid(String memberGuid, String enterpriseGuid) {
//					log.error("根据会员id查询openId失败:{},企业:{}",memberGuid,enterpriseGuid);
//					return null;
//				}
//
//				@Override
//				public Integer getMemberValidVolumeNumber(MemberInfoVolumeQueryReqDTO memberInfoVolumeQueryReqDTO) {
//					log.error("查询会员有效优惠券数量失败");
//					return 0;
//				}

			};
        }
    }
}

