package com.holderzone.saas.store.weixin.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.WxStickDownloadTableDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickDownloadReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickIsModelDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickDownloadRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickModelRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.util.URLUtils;
import com.holderzone.saas.store.weixin.constant.ModelName;
import com.holderzone.saas.store.weixin.entity.domain.WxConfigOverviewDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQrRedirectDo;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreTableStickDO;
import com.holderzone.saas.store.weixin.entity.enums.WxQrTypeEnum;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import com.holderzone.saas.store.weixin.mapper.WxQrCodeInfoMapper;
import com.holderzone.saas.store.weixin.mapper.WxQrRedirectMapper;
import com.holderzone.saas.store.weixin.mapper.WxStoreTableStickMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreTableStickMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.BaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStickModelClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.utils.PdfUtil;
import com.holderzone.saas.store.weixin.utils.QrCodeUtil;
import com.holderzone.saas.store.weixin.utils.ZipUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.error.WxMpErrorMsgEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.weixin.constant.MessageTypeConstant.CREATE_STICK;
import static com.holderzone.saas.store.weixin.constant.MessageTypeConstant.UPDATE_STICK;

import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTableStickServiceImpl
 * @date 2019/03/05 14:03
 * @description 微信桌贴Service实现类
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxStoreTableStickServiceImpl extends ServiceImpl<WxStoreTableStickMapper, WxStoreTableStickDO>
        implements WxStoreTableStickService {
    @Autowired
    WxStoreTableStickMapstruct wxStoreTableStickMapstruct;
    @Autowired
    WxStickModelClientService wxStickModelClientService;
    @Autowired
    WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;
    @Autowired
    BaseClientService baseClientService;
    @Autowired
    EnterpriseClientService enterpriseClientService;
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    PdfUtil pdfUtil;
    @Autowired
    WxStoreMpService wxStoreMpService;
    @Autowired
    WxStoreOrderConfigService wxStoreOrderConfigService;
    @Autowired
    WxConfigOverviewService wxConfigOverviewService;
    @Autowired
    WxSaasMpService wxSaasMpService;
    @Autowired
    WxStoreTableClientService wxStoreTableClientService;
    @Autowired
    @Lazy
    private OssClient ossClient;
    @Autowired
    WxQrRedirectMapper wxQrRedirectMapper;

    @Autowired
    WxQrCodeInfoService wxQrCodeInfoService;

    private static final String SCOPE_USERINFO = "scope=snsapi_userinfo";

    private static final String SCOPE_USERINFO_BASE = "scope=snsapi_base";

    private static final String REDIRECT_URI_PREFIX = "redirect_uri=";

    private static final String DECODE_UTF_8 = "UTF-8";

    private static String getRandomFileName() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 5);
    }

    @Override
    public void handleStickMessage(List<WxTableStickDTO> wxTableStickDTOList, String messageType) {
        switch (messageType) {
            case CREATE_STICK:
                saveOrUpdateModels(wxTableStickDTOList);
                break;
            case UPDATE_STICK:
                List<WxStoreTableStickDO> needUpdateDOList = wxStoreTableStickMapstruct.tableStickDTOList2DOList(wxTableStickDTOList);
                needUpdateDOList.forEach(wxStoreTableStickDO ->
                        update(wxStoreTableStickDO,
                                new LambdaQueryWrapper<WxStoreTableStickDO>()
                                        .eq(WxStoreTableStickDO::getModelGuid, wxStoreTableStickDO.getModelGuid()))
                );
                break;
            default:
                log.info("————暂不支持该类型请求————");
                break;
        }
    }

    @Override
    public List<WxTableStickDTO> listTableStick(Integer isModel) {
        List<WxStoreTableStickDO> wxStoreTableStickDOList = list(new LambdaQueryWrapper<WxStoreTableStickDO>()
                .eq(WxStoreTableStickDO::getIsModel, isModel));
        return wxStoreTableStickMapstruct.stickDOList2DTOList(wxStoreTableStickDOList);
    }

    @Override
    public boolean addOrUpdateStick(WxTableStickDTO wxTableStickDTO) {
        WxStoreTableStickDO wxStoreTableStickDO = wxStoreTableStickMapstruct.tableStickDTO2DO(wxTableStickDTO);
        String guid = wxStoreTableStickDO.getGuid();
        WxStoreTableStickDO isExistDO = getById(guid);
        if (!ObjectUtils.isEmpty(isExistDO) && 1 == isExistDO.getIsModel()) {
            log.info("不能修改桌贴模板");
            return false;
        }
        if (ObjectUtils.isEmpty(guid) || (ObjectUtils.isEmpty(isExistDO))) {
            wxStoreTableStickDO.setGuid(redisUtils.generateGuid(ModelName.WX + ":"
                    + wxTableStickDTO.getGuid() + ":modifyStick"));
        }
        FileDto fileDto = new FileDto();
        String imageName = getRandomFileName();
        String fileContent = pdfUtil.createPreviewImage(wxTableStickDTO, imageName);
        fileDto.setFileContent(fileContent);
        fileDto.setFileName(imageName + ".png");
        String fileUrl = baseClientService.upload(fileDto);
        wxStoreTableStickDO.setPreviewImg(fileUrl);
        return saveOrUpdate(wxStoreTableStickDO);
    }

    @Override
    public WxTableStickDTO findByGuid(String guid) {
        WxTableStickDTO wxTableStickDTO = wxStoreTableStickMapstruct.stickDO2stickDTO(getById(guid));
        wxTableStickDTO.setIsLogoShow(ObjectUtils.isEmpty(wxTableStickDTO.getIsLogoShow()) ? 0 : wxTableStickDTO.getIsLogoShow());
        return wxTableStickDTO;
    }

    @Override
    public List<WxStickModelRespDTO> listStickModel(WxStickModelReqDTO wxStickModelReqDTO) {
        List<WxStickModelRespDTO> respDTOList = new ArrayList<>();
        List<WxTableStickDTO> wxTableStickDTOList = wxStickModelClientService.getTableStickList(wxStickModelReqDTO);
        List<WxStoreTableStickDO> wxStoreTableStickDOList = list(new LambdaQueryWrapper<WxStoreTableStickDO>().eq(WxStoreTableStickDO::getIsModel, 1));
        Map<String, WxStoreTableStickDO> wxStoreTableStickDOMap = wxStoreTableStickDOList.stream()
                .collect(Collectors.toMap(WxStoreTableStickDO::getModelGuid, Function.identity()));
        wxTableStickDTOList.forEach(wxTableStickDTO -> {
            WxStickModelRespDTO wxStickModelRespDTO = new WxStickModelRespDTO();
            BeanUtils.copyProperties(wxTableStickDTO, wxStickModelRespDTO);
            wxStickModelRespDTO.setIsBought(0);
            if (!ObjectUtils.isEmpty(wxStoreTableStickDOMap.get(wxTableStickDTO.getGuid()))) {
                wxStickModelRespDTO.setIsBought(1);
            }
            respDTOList.add(wxStickModelRespDTO);
        });
        return respDTOList;
    }

    @Override
    public String downloadOssStickZip(String downloadKey) {
        String url = null;
        Object downloadUrl = redisUtils.get(downloadKey);
        if (!ObjectUtils.isEmpty(downloadUrl)) {
            log.info("redis获取到桌贴下载URL：{}", downloadUrl.toString());
            redisUtils.delete(downloadKey);
            log.info("已删除桌贴下载数据，downloadKey:{}", downloadKey);
            url = downloadUrl.toString();
        }
        return url;
    }

    @Override
    public WxStickDownloadRespDTO downloadStickZip(String downloadKey) {
        WxStickDownloadRespDTO wxStickDownloadRespDTO = (WxStickDownloadRespDTO) redisUtils.get(downloadKey);
        if (!ObjectUtils.isEmpty(wxStickDownloadRespDTO)) {
            log.info("redis获取到桌贴下载参数：{}", wxStickDownloadRespDTO.getFileName());
            redisUtils.delete(downloadKey);
            log.info("已删除桌贴下载数据，downloadKey:{}", downloadKey);
            if (!StringUtils.isEmpty(wxStickDownloadRespDTO.getException())) {
                try {
                    log.error("桌贴下载异常,e:{}", wxStickDownloadRespDTO.getException());
                    String exception = URLEncoder.encode(wxStickDownloadRespDTO.getException(), DECODE_UTF_8);
                    throw new BusinessException(URLEncoder.encode("桌贴下载失败  ", DECODE_UTF_8) + exception);
                } catch (UnsupportedEncodingException e) {
                    log.error("系统异常, e:{}", e.getMessage());
                }
            }
        }
        return wxStickDownloadRespDTO;
    }

    @Async
    @Override
    public void createStickZip(WxStickDownloadReqDTO wxStickDownloadReqDTO, String enterpriseGuid, JSONObject content) {
        // 异步线程 手动切库
        UserContextUtils.putErp(enterpriseGuid);
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);

        //未传桌台信息则为下载该门店下所有桌贴
        if (CollectionUtils.isEmpty(wxStickDownloadReqDTO.getTableList())) {
            TableBasicQueryDTO basicQueryDTO = new TableBasicQueryDTO();
            basicQueryDTO.setStoreGuid(wxStickDownloadReqDTO.getStoreGuid());
            List<TableBasicDTO> tableBasicDTOS = wxStoreTableClientService.queryTableByWeb(basicQueryDTO);
            if (CollectionUtils.isEmpty(tableBasicDTOS)) {
                log.info("桌贴下载桌贴信息为空！");
                return;
            }
            List<WxStickDownloadTableDTO> tableDTOList = Lists.newLinkedList();
            tableBasicDTOS.stream().forEach(e -> {
                WxStickDownloadTableDTO tableDTO = new WxStickDownloadTableDTO();
                tableDTO.setAreaGuid(e.getAreaGuid());
                tableDTO.setAreaName(e.getAreaName());
                tableDTO.setTableGuid(e.getGuid());
                tableDTO.setTableName(e.getTableCode());
                tableDTOList.add(tableDTO);
            });
            wxStickDownloadReqDTO.setTableList(tableDTOList);
        }
        createStickZipMethod(wxStickDownloadReqDTO, content);
    }

    private void createStickZipMethod(WxStickDownloadReqDTO wxStickDownloadReqDTO, JSONObject content) {
        // 生成桌贴
        List<WxStickDownloadTableDTO> wxStickDownloadTableDTOList = wxStickDownloadReqDTO.getTableList();
        String fileName = wxStickDownloadReqDTO.getStoreName();
        log.info("fileName:{}", fileName);
        // 根据桌贴guid判断当前下载是否使用默认桌贴
        WxStoreTableStickDO wxStoreTableStickDO = getById(wxStickDownloadReqDTO.getStickGuid());
        final boolean isDefault = ObjectUtils.isEmpty(wxStoreTableStickDO) || "0".equals(wxStickDownloadReqDTO.getStickGuid());
        if (isDefault) {
            wxStoreTableStickDO = new WxStoreTableStickDO();
        }
        WxTableStickDTO wxTableStickDTO = wxStoreTableStickMapstruct.stickDO2stickDTO(wxStoreTableStickDO);
        log.info("开始生成桌贴，请求入参：{}", wxStickDownloadReqDTO);
        List<ZipUtil.StreamEntry> streamEntryList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wxStickDownloadTableDTOList)) {
            log.info("桌贴下载请求入参中tableList为空！");
            return;
        }
        wxStickDownloadTableDTOList.forEach(tableDTO -> {
            log.info("正在处理桌贴图片生成参数，桌位号：{}", tableDTO.getTableName());
            // 调用微信接口，获取二维码
            String qrCodeUrl = buildQrCodeUrl(wxStickDownloadReqDTO, tableDTO, content);
            // 桌贴设置
            wxTableStickDTO.setAreaText(tableDTO.getAreaName());
            wxTableStickDTO.setTableNumberText(tableDTO.getTableName());
            wxTableStickDTO.setQrCode(qrCodeUrl);
            // 生成桌贴
            log.info("开始生成桌贴，图片生成参数：{}", wxTableStickDTO);
            InputStream is;
            ZipUtil.StreamEntry streamEntry = new ZipUtil.StreamEntry();
            streamEntry.setName(tableDTO.getAreaName() + File.separator + tableDTO.getTableName() + ".png");
            if (isDefault) { // 默认桌贴
                try {
                    is = QrCodeUtil.encode(qrCodeUrl);
                } catch (Exception e) {
                    log.error("默认桌贴生成失败，失败原因e:{}", e.getMessage());
                    throw new BusinessException("默认桌贴生成失败，失败原因e:{}", e);
                }
            } else {
                is = pdfUtil.createStickImage(wxTableStickDTO);
            }
            streamEntry.setInputStream(is);
            streamEntryList.add(streamEntry);
            log.info("桌位号：{}，桌贴图片生成成功", tableDTO.getTableName());

        });
        // 打包zip
        try {
            byte[] respByte = ZipUtil.compress(streamEntryList);
            redisUtils.set(wxStickDownloadReqDTO.getDownloadKey(), new WxStickDownloadRespDTO(fileName, respByte, null));
            log.info("桌贴zip包生成成功，downloadKey:{}", wxStickDownloadReqDTO.getDownloadKey());
        } catch (IOException e) {
            e.printStackTrace();
            EnterpriseIdentifier.remove();
            throw new BusinessException("桌贴打包失败，请稍后重试");
        }
        EnterpriseIdentifier.remove();
    }

    /**
     * 生成二维码内容
     */
    private String buildQrCodeUrl(WxStickDownloadReqDTO wxStickDownloadReqDTO, WxStickDownloadTableDTO tableDTO, JSONObject contentTemp) {
        // 获取二维码
        Integer qrCodeType = wxStickDownloadReqDTO.getQrCodeType();
        WxQrCodeUrlQuery wxQrCodeUrlQuery = new WxQrCodeUrlQuery(UserContextUtils.getEnterpriseGuid(), wxStickDownloadReqDTO.getStoreGuid(),
                wxStickDownloadReqDTO.getStoreName(), tableDTO.getAreaGuid(), tableDTO.getAreaName(), tableDTO.getTableGuid(),
                tableDTO.getTableName(), qrCodeType, wxStickDownloadReqDTO.getBrandGuid(), null, null, null, null);
        String qrCodeUrl = null;
        try {
            if (wxStickDownloadReqDTO.getQrCodeType() == WxQrTypeEnum.PARAMETERS_QR.getCode()) {
                // 带参数二维码不能转成短链接，否则会通过网页先进行跳转，导致url失效
                qrCodeUrl = wxStoreAuthorizerInfoService.getQrCodeUrl(wxQrCodeUrlQuery);
            } else if (wxStickDownloadReqDTO.getQrCodeType() == WxQrTypeEnum.NORMAL_QR.getCode()) {
                //写入桌贴二维码地址，写入区域信息
                qrCodeUrl = wxStoreMpService.shortenUrl(wxStoreMpService.getQrCodeUrl(wxQrCodeUrlQuery),
                        tableDTO.getTableGuid(), wxStickDownloadReqDTO.getQrCodeType());
            } else if (contentTemp != null && WxQrTypeEnum.ZHUANCAN_QR.getCode() == wxStickDownloadReqDTO.getQrCodeType()) {
                String tempUrl = contentTemp.getString(tableDTO.getTableGuid());
                if (StringUtils.isEmpty(tempUrl)) {
                    throw new BusinessException("赚餐二维码下载出错,没有查询到对应的桌台连接");
                }
                qrCodeUrl = wxStoreMpService.shortenUrl(tempUrl, tableDTO.getTableGuid(), wxStickDownloadReqDTO.getQrCodeType());
            } else if (contentTemp != null && WxQrTypeEnum.WX_CP_QR.getCode() == wxStickDownloadReqDTO.getQrCodeType()) {
                String tempUrl = contentTemp.getString(tableDTO.getTableGuid());
                if (StringUtils.isEmpty(tempUrl)) {
                    throw new BusinessException("企微二维码下载出错,没有查询到对应的桌台连接");
                }
                qrCodeUrl = wxStoreMpService.shortenUrl(tempUrl, tableDTO.getTableGuid(), wxStickDownloadReqDTO.getQrCodeType());
            }
        } catch (WxErrorException e) {
            log.info("微信服务：调用微信二维码API异常,e:", e);
            WxStickDownloadRespDTO wxStickDownloadRespDTO = new WxStickDownloadRespDTO();
            wxStickDownloadRespDTO.setException("调用微信二维码API异常：" +
                    Optional.ofNullable(WxMpErrorMsgEnum.findMsgByCode(e.getError().getErrorCode()))
                            .orElse(e.getError().getErrorMsg()));
            redisUtils.set(wxStickDownloadReqDTO.getDownloadKey(), wxStickDownloadRespDTO);
            throw new BusinessException("微信服务：调用微信二维码API异常：" +
                    Optional.ofNullable(WxMpErrorMsgEnum.findMsgByCode(e.getError().getErrorCode()))
                            .orElse(e.getError().getErrorMsg()));
        }
        return qrCodeUrl;
    }

    @Override
    public boolean isWeatherConfig(String storeGuid) {
        WxConfigOverviewDO one = wxConfigOverviewService.getOne(new LambdaQueryWrapper<WxConfigOverviewDO>().eq(WxConfigOverviewDO::getStoreGuid, storeGuid));
        if (one == null) {
            return false;
        }
        log.info("当前门店：{}，配置信息：{}", storeGuid, one);
        return Objects.equals(1, one.getForHereStatus());
    }


    @Override
    public Boolean deleteMyStick(WxStickIsModelDTO wxStickIsModelDTO) {
        Integer isModel = wxStickIsModelDTO.getIsModel();
        return remove(new LambdaQueryWrapper<WxStoreTableStickDO>()
                .eq(WxStoreTableStickDO::getGuid, wxStickIsModelDTO.getGuid())
                .eq(WxStoreTableStickDO::getIsModel, ObjectUtils.isEmpty(isModel) ? 0 : isModel));
    }

    @Override
    public Boolean checkIsBought(List<String> modelGuidList) {
        int boughtCount = count(new LambdaQueryWrapper<WxStoreTableStickDO>().in(WxStoreTableStickDO::getModelGuid, modelGuidList)
                .eq(WxStoreTableStickDO::getIsModel, 1));
        log.info("查询到已购买的模板数量：{}", boughtCount);
        return boughtCount <= 0;
    }

    @Override
    public void saveOrUpdateModels(List<WxTableStickDTO> wxTableStickDTOList) {
        List<WxStoreTableStickDO> newDOList = new ArrayList<>();
        List<WxStoreTableStickDO> isExistDOList = list(new LambdaQueryWrapper<WxStoreTableStickDO>().eq(WxStoreTableStickDO::getIsModel, 1));// 查询当前已有模板
        Map<String, WxStoreTableStickDO> isExistDOMap = isExistDOList.stream()
                .collect(Collectors.toMap(WxStoreTableStickDO::getModelGuid, Function.identity()));
        wxTableStickDTOList.forEach(wxTableStickDTO -> {
            WxStoreTableStickDO wxStoreTableStickDO = wxStoreTableStickMapstruct.tableStickDTO2DO(wxTableStickDTO);
            wxStoreTableStickDO.setId(null);
            wxStoreTableStickDO.setIsModel(1);
            wxStoreTableStickDO.setModelGuid(wxStoreTableStickDO.getGuid());
            WxStoreTableStickDO isExistDO = isExistDOMap.get(wxTableStickDTO.getGuid()); // 判断推送过来的模板是否已存在
            if (ObjectUtils.isEmpty(isExistDO)) { //未存在
                wxStoreTableStickDO.setGuid(redisUtils.generateGuid(ModelName.WX + ":"
                        + wxTableStickDTO.getGuid() + ":createStickModel"));
            } else { //已存在
                wxStoreTableStickDO.setGuid(isExistDO.getGuid());
            }
            newDOList.add(wxStoreTableStickDO);
        });
        saveOrUpdateBatch(newDOList);
    }

    private final static String REDIRECT_URL_KEY = "QR_REDIRECT_URL:";

    @Override
    public String getRedirectUrl(String enterpriseTable, String lang) {
        String[] params = enterpriseTable.split(",");
        String enterpriseGuid = params[0];
        String redirectId = params[1];
        //手动切库
        UserContextUtils.putErp(enterpriseGuid);
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        //从redis取数据
        Object redisUrl = redisUtils.hGet(REDIRECT_URL_KEY + enterpriseGuid, redirectId);
        if (ObjectUtil.isNotNull(redisUrl)) {
            // 直接处理缓存中的数据
            String redirectUrl = getNormalQrRedirectUrl(redisUrl.toString(), params);
            if (!StringUtils.isEmpty(redirectUrl)) {
                return redirectUrl;
            }
        }
        WxQrRedirectDo wxQrRedirectDo = wxQrRedirectMapper.selectOne(new LambdaQueryWrapper<WxQrRedirectDo>()
                .eq(WxQrRedirectDo::getId, redirectId));
        //若查询是空则根据桌台号查询
        if (ObjectUtil.isNull(wxQrRedirectDo)) {
            List<WxQrRedirectDo> wxQrRedirectDos = wxQrRedirectMapper.selectList(new LambdaQueryWrapper<WxQrRedirectDo>()
                    .eq(WxQrRedirectDo::getTableGuid, redirectId)
                    .orderByDesc(WxQrRedirectDo::getCreateTime));
            if (CollectionUtils.isEmpty(wxQrRedirectDos) || StringUtils.isEmpty(wxQrRedirectDos.get(0).getUrl())) {
                log.error("二维码信息不存在，enterpriseTable：{}", enterpriseTable);
                throw new BusinessException("二维码信息不存在");
            }
            wxQrRedirectDo = wxQrRedirectDos.get(0);
        }
        if (ObjectUtil.isNull(wxQrRedirectDo) || StringUtils.isEmpty(wxQrRedirectDo.getUrl())) {
            log.error("二维码信息不存在，enterpriseTable：{}", enterpriseTable);
            throw new BusinessException("二维码信息不存在");
        }
        String redirectUrl;
        //若是赚餐二维码直接返回url参数
        if (WxQrTypeEnum.ZHUANCAN_QR.getCode() == wxQrRedirectDo.getType()) {
            redirectUrl = buildRedirectParams(wxQrRedirectDo.getUrl(), enterpriseGuid, wxQrRedirectDo.getType());
        } else if (WxQrTypeEnum.WX_CP_QR.getCode() == wxQrRedirectDo.getType()) {
            // 企微二维码
            redirectUrl = buildRedirectParams(wxQrRedirectDo.getUrl(), enterpriseGuid, wxQrRedirectDo.getType());
        } else if (wxQrRedirectDo.getType() == WxQrTypeEnum.NORMAL_QR.getCode()) {
            redirectUrl = wxQrRedirectDo.getUrl() + "&type=" + wxQrRedirectDo.getType() + "&lang=" + lang;
            // 替换scope
            redirectUrl = redirectUrl.replace(SCOPE_USERINFO, SCOPE_USERINFO_BASE);
        } else {
            redirectUrl = wxQrRedirectDo.getUrl() + "&type=" + wxQrRedirectDo.getType() + "&lang=" + lang;
        }
        //将地址存在redis
        redisUtils.hPut(REDIRECT_URL_KEY + enterpriseGuid, redirectId, redirectUrl);
        if (wxQrRedirectDo.getType() == WxQrTypeEnum.NORMAL_QR.getCode()) {
            redirectUrl = appendNormalQrRedirectUrlMemberInfoGuid(redirectUrl, params);
        }
        return redirectUrl;
    }

    private String appendNormalQrRedirectUrlMemberInfoGuid(String redirectUrl, String[] params) {
        String memberInfoGuid = "";
        if (params.length >= 4) {
            memberInfoGuid = params[3];
        }
        if (StringUtils.isEmpty(memberInfoGuid)) {
            return redirectUrl;
        }
        String thirdAppId = "";
        if (params.length >= 5) {
            thirdAppId = params[4];
        }
        String thirdOpenId = "";
        if (params.length >= 6) {
            thirdOpenId = params[5];
        }
        try {
            // 1. 从URL中提取redirect_uri参数
            String[] redirectParams = redirectUrl.split("&");
            StringBuilder newUrl = new StringBuilder();

            for (String param : redirectParams) {
                if (param.startsWith(REDIRECT_URI_PREFIX)) {
                    // 2. 解码redirect_uri的值
                    String redirectUri = getFinalRedirectUri(param, memberInfoGuid, thirdAppId, thirdOpenId);
                    log.info("finalRedirectUri:{}", redirectUri);
                    // 4. 重新编码修改后的redirect_uri
                    String encodedRedirectUri = URLEncoder.encode(redirectUri, DECODE_UTF_8);
                    param = REDIRECT_URI_PREFIX + encodedRedirectUri;
                }
                // 重建URL
                if (newUrl.length() > 0) {
                    newUrl.append("&");
                }
                newUrl.append(param);
            }
            return newUrl.toString();
        } catch (UnsupportedEncodingException e) {
            log.error("处理redirect_uri参数失败", e);
            return redirectUrl;
        }
    }

    private String getFinalRedirectUri(String param, String memberInfoGuid, String thirdAppId, String thirdOpenId) {
        String redirectUri = param.substring(REDIRECT_URI_PREFIX.length());
        try {
            redirectUri = URLDecoder.decode(redirectUri, DECODE_UTF_8);
            // 3. 在redirect_uri后添加memberInfoGuid参数
            if (redirectUri.contains("?")) {
                redirectUri += "&memberInfoGuid=" + memberInfoGuid;
            } else {
                redirectUri += "?memberInfoGuid=" + memberInfoGuid;
            }
            if (!StringUtils.isEmpty(thirdAppId)) {
                redirectUri += "&thirdAppId=" + thirdAppId;
            }
            if (!StringUtils.isEmpty(thirdOpenId)) {
                redirectUri += "&thirdOpenId=" + thirdOpenId;
            }
        } catch (UnsupportedEncodingException e) {
            log.error("处理redirect_uri参数失败", e);
        }
        return redirectUri;
    }

    /**
     * 获取普通二维码重定向地址
     */
    private String getNormalQrRedirectUrl(String redirectUrl, String[] params) {
        if (!StringUtils.isEmpty(redirectUrl) && redirectUrl.contains("type=0")) {
            redirectUrl = redirectUrl.replace(SCOPE_USERINFO, SCOPE_USERINFO_BASE);
        }
        if (StringUtils.isEmpty(redirectUrl) || !redirectUrl.contains("type=0") || !redirectUrl.contains(BusinessName.REDIRECT_URI)) {
            return redirectUrl;
        }
        // 普通二维码
        // 查询是否是游客模式
        if (queryGuestFlag(redirectUrl)) {
            // 游客模式 跳过授权
            String redirectUri = URLUtils.getParamsFromURL(redirectUrl).get(BusinessName.REDIRECT_URI);
            return URLUtils.decodeUrl(redirectUri);
        } else {
            redirectUrl = appendNormalQrRedirectUrlMemberInfoGuid(redirectUrl, params);
            // 非游客模式
            return redirectUrl.replace(SCOPE_USERINFO, SCOPE_USERINFO_BASE);
        }
    }


    /**
     * 查询是否是游客模式
     */
    private boolean queryGuestFlag(String redirectUrl) {
        String qrCodeInfoGuid = parseRedirectUriQrCodeInfoGuid(redirectUrl);
        WxQrCodeInfoDO wxQrCodeInfoDO = wxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(qrCodeInfoGuid);
        if (Objects.isNull(wxQrCodeInfoDO)) {
            log.error("查询桌台码为空, 直接返回不授权模式, qrCodeInfoGuid:{}", qrCodeInfoGuid);
            return false;
        }
        // 根据门店查询点餐配置
        WxStoreReqDTO wxStoreReqDTO = WxStoreReqDTO.builder().storeGuid(wxQrCodeInfoDO.getStoreGuid()).build();
        WxOrderConfigDTO wxOrderConfigDTO = wxStoreOrderConfigService.getDetailConfig(wxStoreReqDTO);
        return Objects.nonNull(wxOrderConfigDTO) && Objects.equals(BooleanEnum.TRUE.getCode(), wxOrderConfigDTO.getGuestFlag());
    }

    /**
     * 解析url中的QrCodeInfoGuid
     */
    private String parseRedirectUriQrCodeInfoGuid(String redirectUrl) {
        String redirectUri = URLUtils.getParamsFromURL(redirectUrl).get(BusinessName.REDIRECT_URI);
        redirectUri = URLUtils.decodeUrl(redirectUri);
        String eventKey = URLUtils.getParamsFromURL(redirectUri).get(BusinessName.EVENT_KEY);
        String[] eventKeySplit = eventKey.split(",");
        return eventKeySplit[2];
    }

    private String buildRedirectParams(String url, String enterpriseGuid, Integer type) {
        JSONObject json = new JSONObject();
        String paramsSting = url.substring(url.indexOf("?") + 1);
        String[] split = paramsSting.split("&");
        for (String param : split) {
            String[] keyValue = param.split("=");
            json.put(keyValue[0], keyValue[1]);
        }
        json.put("enterpriseGuid", enterpriseGuid);
        Object storeGuid = json.get("storeGuid");
        if (Objects.nonNull(storeGuid)) {
            // 查询运营主体guid
            MultiMemberDTO memberInfoByOrganizationGuid = enterpriseClientService.findMemberInfoByOrganizationGuid(storeGuid.toString());
            if (Objects.nonNull(memberInfoByOrganizationGuid)) {
                json.put("operSubjectGuid", memberInfoByOrganizationGuid.getMultiMemberGuid());
            }
        }
        if (WxQrTypeEnum.WX_CP_QR.getCode() == type) {
            json.put("jumpUrl", url);
        }
        return json.toJSONString();
    }
}
