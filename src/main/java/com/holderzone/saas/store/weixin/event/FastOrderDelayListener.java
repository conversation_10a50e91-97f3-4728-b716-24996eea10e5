package com.holderzone.saas.store.weixin.event;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.dto.weixin.deal.mq.FastOrderDelayMQDTO;
import com.holderzone.saas.store.weixin.constant.RocketMqConfig;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
@RocketListenerHandler(topic = RocketMqConfig.FAST_ORDER_DELAY_TOPIC,tags = RocketMqConfig.FAST_ORDER_DELAY_TAG,consumerGroup = RocketMqConfig.FAST_ORDER_DELAY_GROUP)
public class FastOrderDelayListener extends AbstractRocketMqConsumer<RocketMqTopic, FastOrderDelayMQDTO> {

	@Resource
	private WxOrderRecordService wxOrderRecordService;
	@Override
	public boolean consumeMsg(FastOrderDelayMQDTO fastOrderDelayMQDTO, MessageExt messageExt) {
		log.info("接收快餐延迟:{}",fastOrderDelayMQDTO);
		UserContext userContext = new UserContext();
		userContext.setEnterpriseGuid(fastOrderDelayMQDTO.getEnterpriseGuid());
		EnterpriseIdentifier.setEnterpriseGuid(fastOrderDelayMQDTO.getEnterpriseGuid());
		UserContextUtils.put(userContext);

		return wxOrderRecordService.dealFastOrderTimeOut(fastOrderDelayMQDTO.getOrderRecordGuid());
	}
}
