package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EnterpriseClientService
 * @date 2019/04/11 10:00
 * @description 企业信息ClientService
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseClientService.EnterpriseFallBack.class)
public interface EnterpriseClientService {

    @PostMapping("/enterprise/find")
    EnterpriseDTO findEnterprise(BaseDTO baseDTO);

    @GetMapping("/multi/member/list")
    List<MultiMemberDTO> list(@RequestBody Set<String> multiMemberGuid);

    @GetMapping("/multi/member/default_multi_member")
    MultiMemberDTO defaultMultiMember(@RequestParam("enterpriseGuid") String enterpriseGuid);

    /**
     * 查询门店属于什么运营主体
     *
     * @param organizationGuid 门店guid
     * @return
     */
    @GetMapping("/enterprise/member/info/{organizationGuid}")
    MultiMemberDTO findMemberInfoByOrganizationGuid(@PathVariable(value = "organizationGuid") String organizationGuid);

    @Component
    @Slf4j
    class EnterpriseFallBack implements FallbackFactory<EnterpriseClientService> {
        private static final Logger logger = LoggerFactory.getLogger(EnterpriseFallBack.class);
        @Override
        public EnterpriseClientService create(Throwable throwable) {
            return new EnterpriseClientService() {
                @Override
                public EnterpriseDTO findEnterprise(BaseDTO baseDTO) {
                    return null;
                }

                @Override
                public List<MultiMemberDTO> list(Set<String> multiMemberGuid) {
                    logger.error("查询运营主体失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public MultiMemberDTO defaultMultiMember(String enterpriseGuid) {
                    logger.error("查询默认运营主体失败，msg={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public MultiMemberDTO findMemberInfoByOrganizationGuid(String organizationGuid) {
                    logger.error("查询门店属于什么运营主体失败，msg={}",throwable.getMessage());
                    return null;
                }
            };
        }
    }

}
