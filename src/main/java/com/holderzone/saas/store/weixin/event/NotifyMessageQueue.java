package com.holderzone.saas.store.weixin.event;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberModeNotifyReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberTradeNotifyReqDTO;
import com.holderzone.saas.store.weixin.entity.dto.WxMemberConsumeMsgDTO;
import com.holderzone.saas.store.weixin.service.WxOpenMessageService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.ExecutorService;

@Component
@Slf4j
public class NotifyMessageQueue {

	private final WxOpenMessageService wxOpenMessageService;
	private final WxStoreSessionDetailsService wxStoreSessionDetailsService;
	private final ExecutorService executorService;
	private DelayQueue<WxMemberModeNotifyReqDTO> queue = new DelayQueue<>();

	@Autowired
	public NotifyMessageQueue(WxOpenMessageService wxOpenMessageService, WxStoreSessionDetailsService wxStoreSessionDetailsService, ExecutorService executorService) {
		this.wxOpenMessageService = wxOpenMessageService;
		this.wxStoreSessionDetailsService = wxStoreSessionDetailsService;
		this.executorService = executorService;
	}

	@PostConstruct
	public void initial() {
		executorService.execute(() -> {
			while (true) {
				try {
					WxMemberModeNotifyReqDTO take = queue.take();
					if (take == null) {
						log.info("微信模板消息发送参数错误");
						return;
					}
					WxMemberTradeNotifyReqDTO wxMemberTradeNotifyReqDTO = take.getWxMemberTradeNotifyReqDTO();
					if (wxMemberTradeNotifyReqDTO == null || StringUtils.isEmpty(wxMemberTradeNotifyReqDTO.getOpenId())) {
						log.info("微信模板消息发送参数错误");
						return;
					}
					UserInfoDTO userInfoDTO = take.getUserInfoDTO();
					UserContextUtils.put(JacksonUtils.writeValueAsString(userInfoDTO));
					EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
					log.info("微信模板消息发送参数：{}", wxMemberTradeNotifyReqDTO);
					//WxMemberPayDTO wxMemberPayDTO = take.getWxMemberPayDTO();
					//userInfoDTO.getEnterpriseGuid().wxMemberPayDTO.getOpenId()
					//wxMemberPayDTO.getStoreGuid()wxMemberPayDTO.getStoreName()
					//calculate.getActuallyPayFee().setScale(2, BigDecimal.ROUND_HALF_UP
					//UserContextUtils.getStoreGuid()
					//billPayReqDTO.getOrderGuid()
					//WxPrepayReqDTO prepay = take.getPrepay();
					//DineinOrderDetailRespDTO calculate = take.getCalculate();
					//BillPayReqDTO billPayReqDTO = take.getBillPayReqDTO();

					ResponseMemberAndCardInfoDTO memberInfoAndCardList = wxStoreSessionDetailsService.getMemberInfoAndCardList(UserContextUtils.getEnterpriseGuid()
							, UserContextUtils.getStoreGuid(), wxMemberTradeNotifyReqDTO.getOpenId());
					log.info("结账后会员卡详情:{}", memberInfoAndCardList);
					if (!ObjectUtils.isEmpty(memberInfoAndCardList)) {
						ResponseMemberInfoDTO memberInfoDTO = memberInfoAndCardList.getMemberInfoDTO();
						List<ResponseMemberCard> memberCardListRespDTOs = memberInfoAndCardList.getMemberCardListRespDTOs();
						if (!ObjectUtils.isEmpty(memberInfoDTO) && !ObjectUtils.isEmpty(memberCardListRespDTOs)) {
							Optional<ResponseMemberCard> first = memberCardListRespDTOs.stream().filter(x -> x.getMemberInfoCardGuid()
									.equals(wxMemberTradeNotifyReqDTO.getMemberInfoCardGuid())).findFirst();
							try {
								first.ifPresent(x ->
										wxOpenMessageService.sendMemberMsg(WxMemberConsumeMsgDTO.builder()
												.brandGuid(wxMemberTradeNotifyReqDTO.getBrandGuid()).storeGuid(wxMemberTradeNotifyReqDTO.getStoreGuid())
												.openId(wxMemberTradeNotifyReqDTO.getOpenId()).orderGuid(wxMemberTradeNotifyReqDTO.getOrderGuid())
												.args(new String[]{wxMemberTradeNotifyReqDTO.getStoreName(), LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
														String.valueOf(wxMemberTradeNotifyReqDTO.getActuallyPayFee().setScale(2, BigDecimal.ROUND_HALF_UP))
														, String.valueOf(x.getCardMoney().setScale(2, BigDecimal.ROUND_HALF_UP)), String.valueOf(x.getCardIntegral())}).build())
								);
							} catch (Exception e) {
								log.error("sendMemberMsg error", e);
							}
						}
					}
				} catch (InterruptedException e) {
					log.error("", e);
					Thread.currentThread().interrupt();
				}
			}
		});
	}


	public void add(WxMemberModeNotifyReqDTO wxMemberConsumeMsgDTO) {
		queue.add(wxMemberConsumeMsgDTO);
	}
}
