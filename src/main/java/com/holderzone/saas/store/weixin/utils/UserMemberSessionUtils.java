package com.holderzone.saas.store.weixin.utils;

import com.holderzone.framework.dds.starter.utils.JacksonUtil;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberCardCacheDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 会员卡相关缓存
 */
@Component
@Slf4j
public class UserMemberSessionUtils {

	private final RedisUtils redisUtils;


	@Autowired
	public UserMemberSessionUtils(RedisUtils redisUtils) {
		this.redisUtils = redisUtils;
	}

	public void addUserMemberSession(UserMemberSessionDTO userMemberSessionDTO) {
		log.info("更新缓存中的addUserMemberSession:{}", JacksonUtil.writeValueAsString(userMemberSessionDTO));
		redisUtils.setEx(CacheName.USER_MEMBER_SESSION+":"+userMemberSessionDTO.getOpenId(),userMemberSessionDTO,10,TimeUnit.HOURS);
	}

	public UserMemberSessionDTO getUserMemberSession(String openId) {
		UserMemberSessionDTO userMemberSessionDTO = Optional.ofNullable((UserMemberSessionDTO)
				redisUtils.get(CacheName.USER_MEMBER_SESSION+":"+openId)).orElse(new UserMemberSessionDTO());
		userMemberSessionDTO.setOpenId(openId);
		return  userMemberSessionDTO;
	}


	public void delUserMemberSession(String openId){
		redisUtils.delete(CacheName.USER_MEMBER_SESSION+":"+openId);
	}

	public void addCardList(String storeGuid,String openId, List<UserMemberCardCacheDTO> userMemberSessionCardItemDTOS) {
		redisUtils.setEx(CacheName.USER_MEMBER_CARD+":"+storeGuid+":"+openId,userMemberSessionCardItemDTOS,1, TimeUnit.MINUTES);
	}

	public void delAllUserSession(Set<String> openIDS) {
		log.info("删除session:{}", openIDS);
		if (!CollectionUtils.isEmpty(openIDS)) {
			List<String> collect = openIDS.stream().map(x -> CacheName.USER_MEMBER_SESSION + ":" + x).collect(Collectors.toList());
			log.info("删除session集合:{}",collect);
			redisUtils.delete(collect);
		}
	}

	public void delTableCardList(String storeGuid, Set<String> openIDs) {
		log.info("批量删除会员卡缓存:{}",storeGuid);
		if (!CollectionUtils.isEmpty(openIDs)) {
			List<String> collect = openIDs.stream().map(x -> CacheName.USER_MEMBER_CARD + ":" + storeGuid + ":" + x)
					.collect(Collectors.toList());
			redisUtils.delete(collect);
		}
	}


}
