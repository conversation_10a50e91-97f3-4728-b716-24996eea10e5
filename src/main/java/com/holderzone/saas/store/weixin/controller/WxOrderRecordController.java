package com.holderzone.saas.store.weixin.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.weixin.common.ErrorCoderEnum;
import com.holderzone.holder.saas.weixin.entry.dto.OrderBatchItemDTO;
import com.holderzone.holder.saas.weixin.entry.dto.OrderDetailDTO;
import com.holderzone.holder.saas.weixin.entry.dto.RemindDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.req.WxOrderDetailReqDTO;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import com.holderzone.saas.store.dto.weixin.deal.PayBackOrderRecordDTO;
import com.holderzone.saas.store.dto.weixin.deal.UnMemberMessageDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.deal.WxStoreUserOrderItemDTO;
import com.holderzone.saas.store.dto.weixin.req.WxBrandUserOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxUpdateOrderRecordStateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandUserOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreUserOrderDTO;
import com.holderzone.saas.store.enums.weixin.WxOrderStateEnum;
import com.holderzone.saas.store.weixin.annotation.DynamicData;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.helper.OrderDetailHelper;
import com.holderzone.saas.store.weixin.manager.WxOrderManager;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import com.holderzone.saas.store.weixin.service.WxStoreMerchantOrderService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInBillClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxOrderRecordController
 * @date 2019/4/12
 */
@RestController
@RequestMapping(value = "/wx_store_order_record")
@Slf4j
@Api("微信我的订单记录")
public class WxOrderRecordController {
    @Resource
    private WxOrderRecordService wxOrderRecordService;
    @Resource
    private WxStoreMerchantOrderService wxStoreMerchantOrderService;
    @Resource
    private WxStoreSessionDetailsService wxStoreSessionDetailsService;

    @Resource
    private WxStoreTableClientService wxStoreTableClientService;
    @Resource
    private UserMemberSessionUtils userMemberSessionUtils;

    @Resource
    WxStoreDineInBillClientService wxStoreDineInBillClientService;
    @Resource
    WxOrderManager wxOrderManager;


    @ApiOperation("/查询用户订单记录")
    @PostMapping(value = "/get_all")
    @DynamicData(enterpriseGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.enterpriseGuid", storeGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid")
    public List<WxOrderRecordDO> getWxOrderRecordByCondition(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        log.info("查询用户订单记录入参wxStoreAdvanceConsumer:{}", wxStoreAdvanceConsumerReqDTO);
        return wxOrderRecordService.getWxOrderRecordByCondition(wxStoreAdvanceConsumerReqDTO);
    }

    @ApiOperation("删除用户订单记录")
    @PostMapping(value = "/del")
    public void delWxOrderRecord(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        log.info("查询用户订单记录入参wxStoreAdvanceConsumer:{}", wxStoreAdvanceConsumerReqDTO);
        wxOrderRecordService.delWxOrderRecord(wxStoreAdvanceConsumerReqDTO);
    }

    @ApiOperation("查询并封装我订单记录")
    @PostMapping(value = "/user_order")
    @DynamicData(enterpriseGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.enterpriseGuid", storeGuid = "#wxStoreAdvanceConsumerReqDTO.wxStoreConsumerDTO.storeGuid")
    public WxStoreUserOrderDTO getWxStoreUserOrder(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        log.info("查询用户订单记录入参wxStoreAdvanceConsumer:{}", wxStoreAdvanceConsumerReqDTO);
        return wxOrderRecordService.getWxStoreUserOrder(wxStoreAdvanceConsumerReqDTO);
    }

    @ApiOperation("查询并封装我的订单记录")
    @PostMapping(value = "/brand_user_order")
    @DynamicData(enterpriseGuid = "#wxBrandUserOrderReqDTO.wxStoreConsumerDTO.enterpriseGuid", storeGuid = "#wxBrandUserOrderReqDTO.wxStoreConsumerDTO.storeGuid")
    public WxBrandUserOrderDTO getWxBrandUserOrder(@RequestBody WxBrandUserOrderReqDTO wxBrandUserOrderReqDTO) {
        log.info("查询用户品牌订单记录入参:{}", JacksonUtils.writeValueAsString(wxBrandUserOrderReqDTO));
        return wxOrderRecordService.getWxBrandUserOrder(wxBrandUserOrderReqDTO);
    }


    private Result<OrderDetailDTO> calculate(WxOrderDetailReqDTO req) {
        String guid = req.getGuid();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("orderrecord查询");
        WxOrderRecordDO orderRecord = wxOrderRecordService.getById(guid);
        stopWatch.stop();
        if (orderRecord == null) {
            return Result.buildOpFailedResult("订单数据不存在");
        }
        //未支付
        if (orderRecord.getOrderMode().equals(0)) {
            stopWatch.start("正餐状态检查");
            int count = wxStoreMerchantOrderService.countByStatus(req.getGuid(), WxOrderStateEnum.PENDING.getCode(), 0);
            stopWatch.stop();
            if (count > 0) {
                OrderDetailDTO detailDTO = new OrderDetailDTO();
                detailDTO.setCode(51001);
                detailDTO.setMessage("有订单商家未处理，无法买单哦~");
                return Result.buildSuccessResult(detailDTO);
            }
        } else if (orderRecord.getOrderMode().equals(1) && orderRecord.getOrderState().equals(5)) {
            //快餐自动取消
            LocalDateTime gmtCreate = orderRecord.getGmtCreate();
            LocalDateTime now = LocalDateTime.now();
            Duration dur = java.time.Duration.between(gmtCreate, now);
            long diff = dur.toMillis();
            if (diff >= 1000 * 60 * 15) {
                orderRecord.setOrderState(3);
                //wxOrderRecordService.updateById(orderRecord);
                wxOrderRecordService.dealFastOrderTimeOut(req.getGuid());
                OrderDetailDTO detailDTO = new OrderDetailDTO();
                detailDTO.setCode(51005);
                detailDTO.setMessage("订单详情发生变化，请返回");
                return Result.buildSuccessResult(detailDTO);

            }
        }
        //更新订单orderRecord
        String orderGuid = orderRecord.getOrderGuid();
        String tableOrderGuid = null;
        if (orderRecord.getOrderMode().equals(0)) {
            boolean isNotPay = orderRecord.getOrderState().equals(0)
                    || orderRecord.getOrderState().equals(1)
                    || orderRecord.getOrderState().equals(5);
            if (!isNotPay) {
                //不是未支付
                OrderDetailDTO detailDTO = new OrderDetailDTO();
                detailDTO.setCode(51002);
                detailDTO.setMessage("订单详情发生变化，请返回");
                return Result.buildSuccessResult(detailDTO);
            }
            stopWatch.start("桌台订单获取");
            tableOrderGuid = wxStoreTableClientService.getOrderGuid(orderRecord.getTableGuid());
            stopWatch.stop();
            if (StringUtils.isEmpty(tableOrderGuid)) {
                OrderDetailDTO detailDTO = new OrderDetailDTO();
                detailDTO.setCode(51004);
                detailDTO.setMessage("订单详情发生变化，请返回");
                return Result.buildSuccessResult(detailDTO);
            }
            //更新
            if (!tableOrderGuid.equals(orderGuid)) {
                //return;
                //wxOrderManager.updateOrderRecord(orderRecord.getGuid(), tableOrderGuid);
                orderGuid = tableOrderGuid;
            }
        }
        if (req.getEnableMemberPrice() == null) {
            req.setEnableMemberPrice(false);
        }
        BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceId(WeixinUserThreadLocal.getOpenId());
        billCalculateReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        billCalculateReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        billCalculateReqDTO.setOrderGuid(orderGuid);
        billCalculateReqDTO.setDeviceType(12);
        String memberInfoCardGuid = req.getMemberInfoCardGuid();
        //req.setEnableMemberPrice(true);
        billCalculateReqDTO.setMemberPhone(req.getMemberPhone());
        if (!StringUtils.isEmpty(memberInfoCardGuid) && StringUtils.isEmpty(billCalculateReqDTO.getMemberPhone())) {
            billCalculateReqDTO.setMemberPhone(WeixinUserThreadLocal.getOpenId());
        }
        billCalculateReqDTO.setMemberIntegral(req.getMemberIntegral());
        Integer memberLogin = req.getMemberLogin();
        if (memberLogin != null && memberLogin.equals(-1)) {
            billCalculateReqDTO.setMemberLogin(-1);
        } else {
            billCalculateReqDTO.setMemberLogin(StringUtils.isEmpty(memberInfoCardGuid) || "-1".equals(memberInfoCardGuid) ? 2 : 1);
        }
        //TODO
        String volumeCode = req.getVolumeCode();
        boolean noneVolume = ObjectUtils.isEmpty(volumeCode) || "-1".equals(volumeCode) || "0".equals(volumeCode);
        billCalculateReqDTO.setVolumeCode(noneVolume ? null : volumeCode);
        billCalculateReqDTO.setVerify(noneVolume ? null : 3);
        billCalculateReqDTO.setMemberInfoCardGuid(getBillCalculateMemberInfoCardGuid(memberInfoCardGuid));
        DineinOrderDetailRespDTO orderDetailResp = null;
        stopWatch.start("trade calculate算价");
        try {
            log.info("calculate,算价.billCalculateReqDTO:{},req:{}", billCalculateReqDTO, req);
            orderDetailResp = wxStoreDineInBillClientService.calculate(billCalculateReqDTO);
            log.info("calculate,算价.orderDetailResp:{}", JacksonUtils.writeValueAsString(orderDetailResp));
        } catch (Exception e) {
            // throw new ParameterException("只有未结账订单可以结算");
            // throw new ParameterException("会员不可用");
            String message = e.getMessage();
            if (e.getMessage().contains("菜品信息不能为空")) {
                OrderDetailDTO detailDTO = new OrderDetailDTO();
                detailDTO.setCode(51007);
                detailDTO.setMessage("您还没选择菜品，无法买单，请先加菜");
                return Result.buildSuccessResult(detailDTO);
            } else if (e.getMessage().contains("只有未结账订单可以结算")) {
                OrderDetailDTO detailDTO = new OrderDetailDTO();
                detailDTO.setCode(51002);
                detailDTO.setMessage("订单已支付，将返回点餐首页！");
                return Result.buildSuccessResult(detailDTO);
            } else if (e.getMessage().contains("会员不可用")) {
                OrderDetailDTO detailDTO = new OrderDetailDTO();
                detailDTO.setCode(51003);
                detailDTO.setMessage("会员不可用");
                return Result.buildSuccessResult(detailDTO);
            } else {
                log.error("calculate错误", e);
                //e.getCause().getMessage()
                return Result.buildOpFailedResult(message);
            }
        } finally {
            stopWatch.stop();
        }
        if (orderDetailResp == null) {
            log.error("calculate错误");
            return Result.buildOpFailedResult("订单数据异常");
        }
        if (dineInItemEmpty(orderDetailResp)) {
            OrderDetailDTO detailDTO = new OrderDetailDTO();
            detailDTO.setCode(51007);
            detailDTO.setMessage("您还没选择菜品，无法买单，请先加菜");
            return Result.buildSuccessResult(detailDTO);
        }
        if (orderDetailResp.getReserveFee() != null && orderDetailResp.getReserveFee().compareTo(BigDecimal.ZERO) > 0) {
            OrderDetailDTO detailDTO = new OrderDetailDTO();
            detailDTO.setCode(51008);
            detailDTO.setMessage("您已支付了一笔预定金，请至前台结账！若有预定金票据请携带~");
            return Result.buildSuccessResult(detailDTO);
        }
        String openId = WeixinUserThreadLocal.getOpenId();
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(openId);
        String tip = orderDetailResp.getTip();
        log.info("订单算价:tip={}", tip);
        if (!StringUtils.isEmpty(tip)) {
            userMemberSession.setVolumeCode("-1");
            userMemberSessionUtils.addUserMemberSession(userMemberSession);
        }

        String orderNo = orderDetailResp.getOrderNo();
        if (orderNo != null && (orderNo.startsWith("F") || orderNo.startsWith("f"))) {
            OrderDetailDTO detailDTO = new OrderDetailDTO();
            detailDTO.setCode(51005);
            detailDTO.setMessage("反结账订单无法结账，请联系商家");
            return Result.buildSuccessResult(detailDTO);
        }
        //预支付
        OrderDetailHelper builder = new OrderDetailHelper(orderRecord,
                orderDetailResp, false, req.getEnableMemberPrice(), req.getMemberInfoCardGuid());
        stopWatch.start("getTableDetail数据");
        TableDTO tableDetail = wxStoreSessionDetailsService.getTableDetail(orderRecord.getTableGuid());
        stopWatch.stop();
        builder.setTableDTO(tableDetail);
        //builder.setLocalTableDTO(tableDetail);
        if (orderDetailResp.getUpperState() != null && orderDetailResp.getUpperState() != 1
                && !CollectionUtils.isEmpty(orderDetailResp.getSubOrderDetails())) {
            //并桌处理地区
            wxOrderManager.initAreaMap(orderRecord, builder);
            Map<String, WxStoreTableCombineDTO> tableMap = builder.getTableMap();
            Set<String> strings = tableMap.keySet();
            //是否存在未处理订单
            if(wxStoreMerchantOrderService.existPendingOrder(guid, strings)){
                OrderDetailDTO detailDTO = new OrderDetailDTO();
                detailDTO.setCode(51001);
                detailDTO.setMessage("有订单商家未处理，无法买单哦~");
                return Result.buildSuccessResult(detailDTO);
            }
            builder.getOrderDetailDTO().setCombine(1);
        }
        WxUserRecordDO wxUserRecordDO = new WxUserRecordDO();
        wxUserRecordDO.setNickName(WeixinUserThreadLocal.getNickName());
        wxUserRecordDO.setHeadImgUrl(WeixinUserThreadLocal.get().getWxUserInfoDTO().getHeadImgUrl());
        builder.setWxUserRecordDO(wxUserRecordDO);
        builder.getOrderDetailDTO().setOrderState(1);
        builder.structTrade();
        builder.hanldTables();
        OrderDetailDTO orderDetailDTO = builder.getOrderDetailDTO();
        if (builder.getGrouponCouponFee().compareTo(BigDecimal.ZERO) > 0) {
            //团券 其他优惠券不使用
            userMemberSession.setVolumeCode("-1");
            userMemberSessionUtils.addUserMemberSession(userMemberSession);
            orderDetailDTO.setCouponSelect(false);
        }

        if (CollectionUtils.isEmpty(orderDetailDTO.getTableOrderDetailDTO().get(0).getOrderBatchDTOs())) {
            log.error("批次不存在", orderDetailDTO);
        } else {
            orderDetailDTO.getTableOrderDetailDTO().get(0).getOrderBatchDTOs().get(0).setState(1);
        }
        orderDetailDTO.setBrandName(WeixinUserThreadLocal.get().getBrandName());
        orderDetailDTO.setAreaName(WeixinUserThreadLocal.get().getAreaName());
        orderDetailDTO.setLoginStatus(WeixinUserThreadLocal.getIsLogin());
        try {
            OrderBatchItemDTO orderBatchItemDTO = orderDetailDTO.getTableOrderDetailDTO().get(0).getOrderBatchDTOs().get(0);
            orderBatchItemDTO.setLoginStatus(WeixinUserThreadLocal.getIsLogin());
        } catch (Exception e) {

        }
        orderDetailDTO.setEnableMemberPrice(orderDetailResp.getSingleItemUsedMemeberPrice() != null && orderDetailResp.getSingleItemUsedMemeberPrice().equals(1));
        orderDetailDTO.setSaleTotlePrice(orderDetailResp.getActuallyPayFee());

        BigDecimal payAmount = orderDetailDTO.getPayAmount();
        BigDecimal discountFee = orderDetailResp.getDiscountFee();
        if (discountFee == null) {
            discountFee = BigDecimal.ZERO;
        }
        //BigDecimal discountFee1 = orderDetailResp.getDiscountFee();
        //BigDecimal bigDecimal = orderDetailResp.getAppendFee()==null?BigDecimal.ZERO:orderDetailResp.getAppendFee();
        BigDecimal totlePrice = payAmount.add(discountFee);
        //画线的金额跟实付相等 就设置0
        orderDetailDTO.setTotlePrice(payAmount.compareTo(totlePrice) >= 0 ? BigDecimal.ZERO : totlePrice);

        BigDecimal szor = orderDetailDTO.getSavingZero();
        szor = szor == null ? BigDecimal.ZERO : szor;
        if (orderDetailDTO.getSavingZeroType() != null &&
                orderDetailDTO.getSavingZeroType().equals(1)) {
            szor = szor.multiply(BigDecimal.valueOf(-1));
        }
        orderDetailDTO.setDeprecPayAmount(payAmount.add(szor));
        stopWatch.start("菜品图片信息获取");
        wxOrderManager.initOrderItemImg(builder);
        stopWatch.stop();
        //

        if (userMemberSession != null) {
            if (req.getMemberLogin() != null && req.getMemberLogin().equals(-1)) {//校验
                payAmount = userMemberSession.getPayAmount();
                BigDecimal actuallyPayFee = orderDetailResp.getActuallyPayFee();
                log.info("checkPayfee,actuallyPayFee:{},上次计算的payAmount:{}", actuallyPayFee, payAmount);
                if (actuallyPayFee.compareTo(payAmount) != 0) {
                    OrderDetailDTO detailDTO = new OrderDetailDTO();
                    detailDTO.setCode(51006);
                    detailDTO.setMessage("订单数据发生变动");
                    return Result.buildSuccessResult(detailDTO);
                }
            } else {
                userMemberSession.setPayAmount(orderDetailResp.getActuallyPayFee());
                userMemberSessionUtils.addUserMemberSession(userMemberSession);
            }
        }
        //更新
        if (tableOrderGuid != null && !tableOrderGuid.equals(orderGuid)) {
            //return;
            stopWatch.start("更新微信订单和trade绑定关系");
            wxOrderManager.updateOrderRecord(orderRecord.getGuid(), tableOrderGuid);
            stopWatch.stop();
        }
        log.info("买单支付页调用.\n{}", stopWatch.prettyPrint());
        OrderDetailDTO detailDTO = builder.getOrderDetailDTO();
        detailDTO.setDiningTableCode(tableDetail.getCode());
        return Result.buildSuccessResult(detailDTO);
    }

    private String getBillCalculateMemberInfoCardGuid(String memberInfoCardGuid) {
        if (StringUtils.isEmpty(memberInfoCardGuid) || "-1".equals(memberInfoCardGuid)) {
            return null;
        }
        return memberInfoCardGuid;
    }

    private boolean dineInItemEmpty(DineinOrderDetailRespDTO orderDetailResp) {
        List<DineInItemDTO> dineInItemDTOS = orderDetailResp.getDineInItemDTOS();
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetailResp.getSubOrderDetails();
        if(CollUtil.isNotEmpty(dineInItemDTOS)) {
            return false;
        }
        if(CollUtil.isEmpty(subOrderDetails)) {
            return true;
        }
        for(DineinOrderDetailRespDTO subOrder : subOrderDetails ) {
            if(CollUtil.isNotEmpty(subOrder.getDineInItemDTOS())) {
                return false;
            }
        }
        return true;
    }

    @ApiOperation(value = "查询订单预支付信息")
    @PostMapping(value = "/detail/calculate")
    public Result<OrderDetailDTO> detailCalculate(@RequestBody WxOrderDetailReqDTO req) {
        Result<OrderDetailDTO> calculate = calculate(req);
        //OrderDetailDTO orderDetailDTO = calculate.getTData();
        log.info("查询订单预支付信息,req:{},res:{}", req, JSON.toJSONString(calculate));
        return calculate;

    }

    @ApiOperation("查询订单预支付信息,code=51001存在待处理批次")
    @PostMapping(value = "/calculate/check")
    public Result<RemindDTO> detailCalculateCheck(@RequestBody WxOrderDetailReqDTO req) {
        //订单批次状态检查 正餐
        int count = wxStoreMerchantOrderService.countByStatus(req.getGuid(), WxOrderStateEnum.PENDING.getCode(), 0);
        if (count > 0) {
            return Result.buildSuccessResult(RemindDTO.valueOf(ErrorCoderEnum.ORDER_STATUS_BATCH_PENDING));
        }
        return Result.buildSuccessResult(new RemindDTO());

    }


    /**
     * 微信订单状态:
     * 0待确认，下单的初始状态
     * 1已下单，已接单
     * 2已支付，微信用户自己支付
     * 3已取消，
     * 4已退菜，
     * 5待支付，快餐
     * 6已完成,一体机结账
     * 7没有开台且直接拒单
     *
     * @see com.holderzone.saas.store.enums.weixin.WxOrderStateEnum
     **/
    @ApiOperation("查询订单明细")
    @PostMapping(value = "/detail")
    public OrderDetailDTO detail(@RequestBody WxOrderDetailReqDTO req) {
        if (StringUtils.isEmpty(req.getGuid())) {
            return null;
        }
        return wxOrderManager.detail(req);
    }


    /**
     * 根据聚合支付平台支付订单号查询订单
     */
    @GetMapping(value = "/get_by_order_holder_no")
    public String getByOrderHolderNo(@RequestParam(value = "orderHolderNo") String orderHolderNo) {
        log.info("根据聚合支付平台支付订单号查询订单入参:{}", orderHolderNo);
        WxOrderRecordDO byOrderHolderNo = wxOrderRecordService.getByOrderHolderNo(orderHolderNo);
        log.info("根据聚合支付平台支付订单号查询订单结果:{}", JacksonUtils.writeValueAsString(byOrderHolderNo));
        return byOrderHolderNo.getOrderGuid();
    }


    @ApiOperation("用户门店订单：重构")
    @GetMapping(value = "/store_list")
    public List<WxStoreUserOrderItemDTO> userStoreList() {
        return wxOrderRecordService.userStoreList();
    }

    @ApiOperation("查询我的或者桌台没有完结的订单")
    @GetMapping(value = "/un_finish_order_list")
    public List<WxStoreUserOrderItemDTO> getUnFinishStoreUserOrderList() {
        return wxOrderRecordService.getUnFinishStoreUserOrderList();
    }

    @ApiOperation("获取一体机订单id")
    @PostMapping(value = "/order_guid")
    public String getMerchantOrderGuid(@RequestParam(value = "orderGuid") String orderRecordGuid) {
        log.info("获取一体机订单id:{}", orderRecordGuid);
        return wxOrderRecordService.getMerchantOrderGuid(orderRecordGuid);
    }

    @ApiOperation("门店用户订单")
    @PostMapping(value = "/store_page")
    public IPage<WxStoreUserOrderItemDTO> userStorePage(Integer pageNo, Integer pageSize) {
        return wxOrderRecordService.userStorePage(pageNo, pageSize);
    }

    @ApiOperation("会员支付回调更新订单")
    @PostMapping(value = "/member_back")
    public void payBackOrder(@RequestBody PayBackOrderRecordDTO payBackOrderRecordDTO) {
        log.info("会员支付回调更新订单:{}", JacksonUtils.writeValueAsString(payBackOrderRecordDTO));
        wxOrderRecordService.payBackOrder(payBackOrderRecordDTO);
    }

    @ApiOperation("正餐订单初始化")
    @GetMapping(value = "/dine_initial")
    public String initialDineOrder() {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        return wxOrderRecordService.initialRecord(wxMemberSessionDTO);
    }

    @ApiOperation("非会员推送消息")
    @GetMapping(value = "/sendUnMemberMessage")
    public void sendUnMemberMessage(UnMemberMessageDTO unMemberMessageDTO) {
        log.info("非会员推送消息:{}", unMemberMessageDTO);
        wxOrderRecordService.sendUnMemberMessage(unMemberMessageDTO);
    }

    @ApiOperation("获取下单手机号")
    @PostMapping(value = "/get_merchant_order_phone")
    public WxStoreMerchantOrderDTO getMerchantOrderPhone(@RequestParam(value = "orderGuid") String orderGuid) {
        log.info("获取下单手机号请求参数:{}", orderGuid);
        return wxOrderRecordService.getMerchantOrderPhone(orderGuid);
    }

    @ApiOperation("快餐订单查微信订单")
    @PostMapping(value = "/list_by_order_guid")
    public List<WxStoreMerchantOrderDTO> listByOrderGuid(@RequestBody SingleDataDTO query) {
        log.info("[快餐订单查微信订单]query={}", JacksonUtils.writeValueAsString(query));
        return wxOrderRecordService.listByOrderGuid(query);
    }

    @ApiOperation("修改订单状态")
    @PostMapping(value = "/update_order_record_state")
    public void updateOrderRecordState(@RequestBody WxUpdateOrderRecordStateReqDTO reqDTO) {
        log.info("[修改订单状态]reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        wxOrderRecordService.updateOrderRecordState(reqDTO);
    }

}
