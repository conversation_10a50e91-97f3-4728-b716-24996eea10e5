package com.holderzone.saas.store.weixin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.weixin.open.WxOpenAuthDTO;
import com.holderzone.saas.store.dto.weixin.req.*;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandAuthRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreAuthorizerInfoService
 * @date 2019/02/28 9:29
 * @description 微信授权方账号信息Service
 * @program holder-saas-store
 */
public interface WxStoreAuthorizerInfoService extends IService<WxStoreAuthorizerInfoDO> {

    /**
     * 获取用户授权url
     *
     * @param wxPreCodReqDTO 品牌guid，企业guid
     * @return
     * @throws WxErrorException
     */
    String getPreAuthCode(WxPreCodReqDTO wxPreCodReqDTO) throws WxErrorException;

    /**
     * 通过授权码拉取获取授权方信息
     * 通过穿透字段brandGuid将授权方公众号信息与品牌绑定
     *
     * @param wxOpenAuthDTO
     * @return
     */
    String queryAuth(WxOpenAuthDTO wxOpenAuthDTO) throws WxErrorException, UnsupportedEncodingException;

    /**
     * 获取品牌-公众号绑定列表
     *
     * @return
     */
    List<WxBrandAuthRespDTO> getBrandAuthList();

    /**
     * 向当前用户发送短信验证码
     *
     * @param wxSendShortMsgReqDTO 用来获取当前用户信息
     * @return
     */
    String sendShortMessage(WxSendShortMsgReqDTO wxSendShortMsgReqDTO);

    /**
     * 解绑公众号
     * 注：该接口只是删除品牌与当前绑定公众号在商户平台的绑定状态，公众号向第三方平台的授权并未取消
     * 公众号取消授权需公众号管理员自主取消
     *
     * @param wxUnBandReqDTO
     * @return
     */
    Integer unBandBrand(WxUnBandReqDTO wxUnBandReqDTO);

    /**
     * 通过品牌guid查找品牌与公众号绑定情况
     *
     * @param brandGuid
     * @return
     */
    WxBrandAuthRespDTO getByBrandGuid(String brandGuid);

    /**
     * 获取点餐二维码url
     *
     * @param wxQrCodeUrlQuery
     * @return
     */
    String getQrCodeUrl(WxQrCodeUrlQuery wxQrCodeUrlQuery) throws WxErrorException;

    String getQueueQrCodeUrl(WxQueueInfoReqDTO wxQueueInfoReqDTO) throws WxErrorException;

    /**
     * 修改微信公众号信息
     *
     * @param wxStoreAuthorizerInfoDO
     * @return
     */
    boolean updateWxAuthorizeInfo(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO);

    /**
     * 通过appId获取当前公众号授权情况
     *
     * @param appId
     * @return
     */
    WxStoreAuthorizerInfoDO getByAppId(String appId);

    WxStoreAuthorizerInfoDO getByBrandId(String brandId);

    /***
     * 品牌绑定运营主体与微信公众号
     * @param wxOperSubjectBrandReqDTO
     * @return
     */
    Boolean bindSubject(@RequestBody WxOperSubjectBrandReqDTO wxOperSubjectBrandReqDTO);
}
