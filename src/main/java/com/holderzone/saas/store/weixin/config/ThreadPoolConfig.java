package com.holderzone.saas.store.weixin.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

	@Bean
	public ExecutorService executorService() {
		return new ThreadPoolExecutor(10, 100, 10L, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("takeout-mapping-pool-%d").build());
	}

	@Bean(value = "asyncOrderTcdExecutor")
	public ExecutorService asyncOrderTcdExecutor() {
		return new ThreadPoolExecutor(10, 100, 5L, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("async-order-tcd-pool-%d").build());
	}

	@Bean(value = "orderRecordExecutor")
	public ExecutorService orderRecordExecutor() {
		return new ThreadPoolExecutor(10, 100, 5L, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("order-record-pool-%d").build());
	}

	@Bean(value = "tableStaffRelationExecutor")
	public ExecutorService tableStaffRelationExecutor() {
		return new ThreadPoolExecutor(10, 100, 5L, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("table-staff-relation-pool-%d").build());
	}

	@Bean(value = "openIdRelationExecutor")
	public ExecutorService openIdRelationExecutor() {
		return new ThreadPoolExecutor(10, 100, 5L, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("openId-relation-pool-%d").build());
	}
}