package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.queue.*;
import com.holderzone.saas.store.dto.weixin.WxInQueueReqDTO;
import com.holderzone.saas.store.dto.weixin.WxQueueDetailDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxInQueueRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxTotalQueueDTO;
import com.holderzone.saas.store.weixin.service.WxOpenMessageService;
import com.holderzone.saas.store.weixin.service.WxQueueService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import com.holderzone.saas.store.weixin.service.rpc.QueueClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.error.WxMpErrorMsgEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueServiceImpl
 * @date 2019/05/16 11:01
 * @description 微信排队Service实现类
 * @program holder-saas-store
 */
@Slf4j
@Service
public class WxQueueServiceImpl implements WxQueueService {

    @Resource
    DynamicHelper dynamicHelper;
    @Resource
    RedisUtils redisUtils;
    @Resource
    QueueClientService queueClientService;
    @Resource
    OrganizationClientService organizationClientService;
    @Resource
    WxOpenMessageService wxOpenMessageService;

    @Override
    public WxQueueDetailDTO getDetail(WxPortalReqDTO wxPortalReqDTO) {
        dynamicHelper.changeDatasource(wxPortalReqDTO.getEnterpriseGuid());
        WxQueueInfoReqDTO wxQueueInfoReqDTO = (WxQueueInfoReqDTO) redisUtils.get(wxPortalReqDTO.getMsgKey());
        if (ObjectUtils.isEmpty(wxQueueInfoReqDTO)) {
            return null;
        }
        WxQueueDetailDTO wxQueueDetailDTO = new WxQueueDetailDTO();
        ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
        itemGuidDTO.setEnterpriseGuid(wxQueueInfoReqDTO.getEnterpriseGuid());
        itemGuidDTO.setItemGuid(wxQueueInfoReqDTO.getQueueGuid());
        itemGuidDTO.setStoreGuid(wxQueueInfoReqDTO.getStoreGuid());

        HolderQueueQueueRecordDTO obtain = safeObtainQueueRecord(itemGuidDTO);
        if (ObjectUtils.isEmpty(obtain)) {
            return WxQueueDetailDTO.builder().code(-1).build();
        }
        wxQueueDetailDTO.setQueue(obtain.getQueue());
        wxQueueDetailDTO.setQueueRecord(obtain.getQueueRecord());
        populateStoreAndBrandInfo(wxQueueDetailDTO, wxQueueInfoReqDTO.getStoreGuid());
        return wxQueueDetailDTO;
    }

    private HolderQueueQueueRecordDTO safeObtainQueueRecord(ItemGuidDTO itemGuidDTO) {
        try {
            return queueClientService.obtain(itemGuidDTO);
        } catch (Exception e) {
            log.error("Error obtaining queue record", e);
            return null;
        }
    }

    private void populateStoreAndBrandInfo(WxQueueDetailDTO wxQueueDetailDTO, String storeGuid) {
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(storeGuid);
        if (!ObjectUtils.isEmpty(storeDTO)) {
            wxQueueDetailDTO.getQueueRecord().setStoreName(storeDTO.getName());
            BrandDTO brandDTO = organizationClientService.queryBrandByGuid(storeDTO.getBelongBrandGuid());
            if (!ObjectUtils.isEmpty(brandDTO)) {
                wxQueueDetailDTO.setBrandGuid(brandDTO.getGuid());
                wxQueueDetailDTO.setBrandLogo(brandDTO.getLogoUrl());
                wxQueueDetailDTO.setBrandName(brandDTO.getName());
            }
            StoreConfigDTO storeConfigDTO = queueClientService.query(storeDTO.getGuid());
            if (ObjectUtils.isEmpty(storeConfigDTO)) {
                throw new BusinessException("该门店暂无排队配置");
            }
            wxQueueDetailDTO.setStoreConfigDTO(storeConfigDTO);
        }
    }

    @Override
    public WxInQueueRespDTO inQueueByWx(WxInQueueReqDTO wxInQueueReqDTO) {
        WxStoreConsumerDTO wxStoreConsumerDTO = wxInQueueReqDTO.getWxStoreConsumerDTO();
        dynamicHelper.changeDatasource(wxStoreConsumerDTO.getEnterpriseGuid());
        String countKey = BusinessName.QUEUE_INFO + ":" + wxInQueueReqDTO.getStoreGuid() + ":" + wxStoreConsumerDTO.getOpenId();
        Integer count = Optional.ofNullable((Integer) redisUtils.get(countKey)).orElse(0);
        if (count >= 10) {
            return new WxInQueueRespDTO(311, "今日已达最大取号次数，无法取号");
        }
        HolderQueueItemDTO holderQueueItemDTO = createHolderQueueItemDTO(wxInQueueReqDTO, wxStoreConsumerDTO);
        WxInQueueRespDTO wxPortalReqDTO = new WxInQueueRespDTO();
        wxPortalReqDTO.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
        HolderQueueItemDetailDTO holderQueueItemDetailDTO = queueClientService.inQueue(holderQueueItemDTO);
        if (!ObjectUtils.isEmpty(holderQueueItemDetailDTO)) {
            processSuccessfulQueue(holderQueueItemDetailDTO, wxStoreConsumerDTO, wxPortalReqDTO, countKey, count);
        } else {
            wxPortalReqDTO.setRespCode(500);
            wxPortalReqDTO.setRespMsg("系统异常，取号失败，请重试");
        }
        return wxPortalReqDTO;
    }

    private HolderQueueItemDTO createHolderQueueItemDTO(WxInQueueReqDTO wxInQueueReqDTO, WxStoreConsumerDTO wxStoreConsumerDTO) {
        HolderQueueItemDTO holderQueueItemDTO = new HolderQueueItemDTO();
        holderQueueItemDTO.setPeopleNumber(wxInQueueReqDTO.getPeopleNumber());
        holderQueueItemDTO.setStoreGuid(wxInQueueReqDTO.getStoreGuid());
        holderQueueItemDTO.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());
        holderQueueItemDTO.setDeviceType(12);
        holderQueueItemDTO.setDeviceId(wxStoreConsumerDTO.getOpenId());
        holderQueueItemDTO.setPhone(wxStoreConsumerDTO.getPhone());
        holderQueueItemDTO.setContact(wxStoreConsumerDTO.getNickName());
        holderQueueItemDTO.setGender(1 == wxStoreConsumerDTO.getSex());
        return holderQueueItemDTO;
    }

    private void processSuccessfulQueue(HolderQueueItemDetailDTO holderQueueItemDetailDTO, WxStoreConsumerDTO wxStoreConsumerDTO, WxInQueueRespDTO wxPortalReqDTO, String countKey, Integer count) {
        WxQueueInfoReqDTO wxQueueInfoReqDTO = new WxQueueInfoReqDTO();
        wxQueueInfoReqDTO.setQueueGuid(holderQueueItemDetailDTO.getGuid());
        wxQueueInfoReqDTO.setStoreGuid(holderQueueItemDetailDTO.getStoreGuid());
        wxQueueInfoReqDTO.setEnterpriseGuid(wxStoreConsumerDTO.getEnterpriseGuid());

        redisUtils.setEx(wxQueueInfoReqDTO.getQueueGuid(), wxQueueInfoReqDTO, 43200, TimeUnit.SECONDS);
        wxPortalReqDTO.setMsgKey(wxQueueInfoReqDTO.getQueueGuid());
        wxPortalReqDTO.setRespCode(0);
        wxPortalReqDTO.setRespMsg("成功");
        try {
            ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
            itemGuidDTO.setItemGuid(holderQueueItemDetailDTO.getGuid());
            itemGuidDTO.setStoreGuid(holderQueueItemDetailDTO.getStoreGuid());
            itemGuidDTO.setEnterpriseGuid(holderQueueItemDetailDTO.getEnterpriseGuid());

            HolderQueueQueueRecordDTO obtain = queueClientService.obtain(itemGuidDTO);
            if (!ObjectUtils.isEmpty(obtain)) {
                wxOpenMessageService.sendQueueMsg(wxStoreConsumerDTO, obtain, wxPortalReqDTO);
            }
        } catch (WxErrorException e) {
            log.error("微信排队消息推送异常，e:{}", WxMpErrorMsgEnum.findMsgByCode(e.getError().getErrorCode()));
        }
        count++;
        long lastTime = Duration.between(LocalTime.now(), LocalTime.of(23, 59, 59)).toMillis();
        log.info("当前人数剩余过期时间为：{}", lastTime);
        redisUtils.setEx(countKey, count, lastTime, TimeUnit.MILLISECONDS);
    }

    @Override
    public void callUpNotify(List<ItemGuidDTO> itemGuidDTOS) {
        if (ObjectUtils.isEmpty(itemGuidDTOS)) {
            return;
        }
        itemGuidDTOS.forEach(this::processCallUpNotify);
    }

    private void processCallUpNotify(ItemGuidDTO itemGuidDTO) {
        String enterpriseGuid = Optional.ofNullable(itemGuidDTO.getEnterpriseGuid()).orElse(UserContextUtils.getEnterpriseGuid());
        dynamicHelper.changeDatasource(enterpriseGuid);
        HolderQueueQueueRecordDTO obtain = queueClientService.obtain(itemGuidDTO);
        if (obtain != null) {
            WxStoreConsumerDTO wxStoreConsumerDTO = createWxStoreConsumerDTO(obtain, itemGuidDTO);
            WxPortalReqDTO wxPortalReqDTO = new WxPortalReqDTO();
            wxPortalReqDTO.setEnterpriseGuid(obtain.getQueueRecord().getEnterpriseGuid());
            wxPortalReqDTO.setMsgKey(obtain.getQueueRecord().getGuid());
            try {
                wxOpenMessageService.sendQueueMsg(wxStoreConsumerDTO, obtain, wxPortalReqDTO);
            } catch (WxErrorException e) {
                log.error("微信排队消息推送失败，失败原因：{}", WxMpErrorMsgEnum.findMsgByCode(e.getError().getErrorCode()));
            }
        }
    }

    private WxStoreConsumerDTO createWxStoreConsumerDTO(HolderQueueQueueRecordDTO obtain, ItemGuidDTO itemGuidDTO) {
        WxStoreConsumerDTO wxStoreConsumerDTO = new WxStoreConsumerDTO();
        wxStoreConsumerDTO.setOpenId(obtain.getQueueRecord().getDeviceId());
        wxStoreConsumerDTO.setEnterpriseGuid(itemGuidDTO.getEnterpriseGuid());
        String storeGuid = obtain.getQueueRecord().getStoreGuid();
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(storeGuid);
        wxStoreConsumerDTO.setBrandGuid(storeDTO.getBelongBrandGuid());
        wxStoreConsumerDTO.setEnterpriseGuid(itemGuidDTO.getEnterpriseGuid());
        return wxStoreConsumerDTO;
    }

    @Override
    public WxTotalQueueDTO getTotalDetail(WxPortalReqDTO wxPortalReqDTO) {
        dynamicHelper.changeDatasource(wxPortalReqDTO.getEnterpriseGuid());
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(wxPortalReqDTO.getStoreGuid());
        if (ObjectUtils.isEmpty(storeDTO)) {
            throw new BusinessException("该门店不存在，门店guid: " + wxPortalReqDTO.getStoreGuid());
        }
        BrandDTO brandDTO = organizationClientService.queryBrandByGuid(storeDTO.getBelongBrandGuid());
        StoreGuidDTO storeGuidDTO = new StoreGuidDTO();
        storeGuidDTO.setStoreGuid(storeDTO.getGuid());
        List<HolderQueueDTO> holderQueueDTOS = queueClientService.listByStore(storeGuidDTO);
        if (ObjectUtils.isEmpty(holderQueueDTOS)) {
            throw new BusinessException("该门店未配置排队功能信息");
        }
        WxTotalQueueDTO wxTotalQueueDTO = new WxTotalQueueDTO();
        wxTotalQueueDTO.setStoreName(storeDTO.getName());
        wxTotalQueueDTO.setStoreGuid(storeDTO.getGuid());
        wxTotalQueueDTO.setHolderQueueDTOList(holderQueueDTOS);
        wxTotalQueueDTO.setBrandGuid(brandDTO.getGuid());
        wxTotalQueueDTO.setBrandName(brandDTO.getName());
        wxTotalQueueDTO.setBrandLogo(brandDTO.getLogoUrl());
        return wxTotalQueueDTO;
    }

    @Override
    public Map<Integer, Integer> queryQueueStatusNum(QueueWechatDTO queueWechatDTO) {
        Map<Integer, Integer> map = new HashMap<>();
        List<WxQueueListDTO> wxQueueListDTOS = queueClientService.queryByUser(queueWechatDTO);
        Map<Byte, List<WxQueueListDTO>> collect = wxQueueListDTOS.stream().collect(Collectors.groupingBy(WxQueueListDTO::getStatus));
        map.put(0, collect.getOrDefault((byte) 0, Collections.emptyList()).size());
        map.put(1, collect.getOrDefault((byte) 1, Collections.emptyList()).size());
        map.put(3, collect.getOrDefault((byte) 3, Collections.emptyList()).size());
        map.put(4, collect.getOrDefault((byte) 4, Collections.emptyList()).size());
        return map;
    }
}
