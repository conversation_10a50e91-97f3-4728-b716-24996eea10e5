package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.AbstractMemberModelItemService;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DiningMemberModelItemServiceImpl extends AbstractMemberModelItemService {

	@Autowired
	private WxOrderRecordService wxOrderRecordService;

	@Override
	public WxMemberOverviewModelDTO getWxMemberOverviewModel(WxStoreConsumerDTO wxStoreConsumerDTO) {
//		WxBrandUserOrderDTO wxBrandUserOrder = wxOrderRecordService.getWxBrandUserOrder(WxBrandUserOrderReqDTO.builder().wxStoreConsumerDTO(wxStoreConsumerDTO).build());
		return new WxMemberOverviewModelDTO(5,"我的就餐",0);
	}
}
