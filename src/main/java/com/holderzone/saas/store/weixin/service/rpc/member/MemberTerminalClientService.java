package com.holderzone.saas.store.weixin.service.rpc.member;


import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberClientService
 * @date 2018/08/06 11:22
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-member-terminal", fallbackFactory = MemberTerminalClientService
        .MemberClientFallback.class)
public interface MemberTerminalClientService {

    @PostMapping("/hsmca/member/getMemberInfoAndCard")
    ResponseMemberAndCardInfoDTO getMemberInfoAndCard(@RequestBody(required=false) RequestQueryStoreAndMemberAndCard requestQueryStoreAndMemberAndCard);

    @Component
    class MemberClientFallback implements FallbackFactory<MemberTerminalClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberClientFallback.class);

        @Override
        public MemberTerminalClientService create(Throwable throwable) {
            return new MemberTerminalClientService() {

                @Override
                public ResponseMemberAndCardInfoDTO getMemberInfoAndCard(RequestQueryStoreAndMemberAndCard
                                                                             queryStoreAndMemberAndCardReqDTO) {
                    logger.error("获取会员信息调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员信息调用异常");
                }
            };
        }
    }

}
