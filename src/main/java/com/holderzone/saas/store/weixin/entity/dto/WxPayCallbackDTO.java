package com.holderzone.saas.store.weixin.entity.dto;

import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class WxPayCallbackDTO {

	String storeGuid;
	String openId;

	SaasNotifyDTO saasNotifyDTO;
}
