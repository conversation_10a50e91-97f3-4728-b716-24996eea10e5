package com.holderzone.saas.store.weixin.service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreAuthorizerBusiInfoService
 * @date 2019/02/28 15:27
 * @description 微信授权方功能开启情况service
 * @program holder-saas-store
 */
public interface WxStoreAuthorizerBusiInfoService {
    /**
     * 修改公众号功能情况
     *
     * @param authorizerAppId
     * @param businessInfo
     * @return
     */
    boolean saveOrUpdateStoreAuthorizeBusiInfo(String authorizerAppId, Map<String, Integer> businessInfo);
}
