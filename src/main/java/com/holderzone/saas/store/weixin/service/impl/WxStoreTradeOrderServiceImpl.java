package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardRightDetails;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRightDetail;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeList;
import com.holderzone.holder.saas.member.wechat.dto.member.MemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberInfoVolumeQuery;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolumeDetails;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.WxStoreMerchantDineInItemDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeConditionQuery;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxPaidOrderDetailsReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreSkuRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeDetailsGroupDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.enums.weixin.WxDistributionEnum;
import com.holderzone.saas.store.enums.weixin.WxOrderStateEnum;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.entity.query.WxStorePendingOrdersQuery;
import com.holderzone.saas.store.weixin.execption.CorrectResult;
import com.holderzone.saas.store.weixin.mapstruct.*;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * 微信门店订单服务层实现
 * @className WxStoreOrderServiceImpl
 * @date 2019/3/12
 */
@Service
@Slf4j
public class WxStoreTradeOrderServiceImpl implements WxStoreTradeOrderService {

    @Autowired
    private WxStoreDineInOrderClientService wxStoreDineInOrderClientService;
    @Autowired
    private WxStoreTableClientService wxStoreTableClientService;
    @Autowired
    private WxStoreCreateDineInOrderReqDTOMapStruct wxStoreCreateDineInOrderReqDTOMapStruct;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private WxStoreAdvanceOrderService wxStoreAdvanceOrderService;
    @Autowired
    private WxStoreDineInBillClientService wxStoreDineInBillClientService;
    @Autowired
    private WxStoreCreateFastFoodReqDTOMapstruct wxStoreCreateFastFoodReqDTOMapstruct;
    @Autowired
    private WxStoreMenuDetailsService wxStoreMenuDetailsService;
    @Autowired
    private WxStoreMerchantDineInItemService wxStoreMerchantDineInItemService;
    @Autowired
    private WxStoreMerchantOrderService wxStoreMerchantOrderService;
    @Autowired
    private WxStoreSessionDetailsService wxStoreSessionDetailsService;
    @Autowired
    private WxSocketMsgService wxSocketMsgService;

    @Autowired
    private WxOrderRecordService wxOrderRecordService;

    @Autowired
    private DynamicHelper dynamicHelper;

    @Autowired
    private WxStoreEstimateClientService wxStoreEstimateClientService;
    @Autowired
    private WxStorePersonOrderDetailsService wxStorePersonOrderDetailsService;
    @Autowired
    private WxStoreOrderPayService wxStoreOrderPayService;
    @Autowired
    private OrganizationClientService organizationClientService;
    @Autowired
    private MemberClientService memberClientService;
    @Autowired
    private WxUserRecordService wxUserRecordService;
    //	@Autowired
//	private MemberOrderClientService memberOrderClientService;
    @Autowired
    private HsaBaseClientService hsaBaseClientService;
    @Autowired
    private BusinessClientService businessClientService;

    @Autowired
    private WxStoreWeChatOrderClientService wxStoreWeChatOrderClientService;


    /**
     * 获取当前桌台微信订单详情
     * @param wxStoreAdvanceConsumerReqDTO 请求参数
     * @return WebSocketMessageDTO 包含订单详情的WebSocket消息
     */
    @Override
    public WebSocketMessageDTO getTableOrderDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        WebSocketMessageDTO<WxStoreTradeOrderDetailsRespDTO> webSocketMessageDTO = new WebSocketMessageDTO<>();
        
        if (isSecondPageRequest(wxStoreAdvanceConsumerReqDTO)) {
            return handleSecondPageRequest(wxStoreAdvanceConsumerReqDTO, webSocketMessageDTO);
        }
        
        clearPrepayInfo(wxStoreAdvanceConsumerReqDTO);
        
        WxStoreTradeOrderDetailsRespDTO orderDetails = fetchOrderDetails(wxStoreAdvanceConsumerReqDTO);
        webSocketMessageDTO.setContent(orderDetails);
        webSocketMessageDTO.setType(1);
        return webSocketMessageDTO;
    }

    /**
     * 判断是否为第二页请求
     */
    private boolean isSecondPageRequest(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        return wxStoreAdvanceConsumerReqDTO.getPageNum() == 2;
    }

    /**
     * 处理第二页请求
     */
    private WebSocketMessageDTO handleSecondPageRequest(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO, 
                                                        WebSocketMessageDTO<WxStoreTradeOrderDetailsRespDTO> webSocketMessageDTO) {
        webSocketMessageDTO.setType(2);
        webSocketMessageDTO.setContent(wxStoreOrderPayService.orderPay(wxStoreAdvanceConsumerReqDTO));
        return webSocketMessageDTO;
    }

    /**
     * 清除预支付信息
     */
    private void clearPrepayInfo(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        wxStoreSessionDetailsService.delPrepay(
            wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid(),
            wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId()
        );
    }

    /**
     * 获取订单详情
     */
    private WxStoreTradeOrderDetailsRespDTO fetchOrderDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        if (!ObjectUtils.isEmpty(wxStoreAdvanceConsumerReqDTO.getTradeOrderGuid())) {
            return wxStorePersonOrderDetailsService.getPersonOrderDetails(wxStoreAdvanceConsumerReqDTO);
        }
        return Boolean.TRUE.equals(wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO))
            ? getDineInOrderDetails(wxStoreAdvanceConsumerReqDTO) 
            : getFastDetails(wxStoreAdvanceConsumerReqDTO);
    }

    /**
     * 初始化正餐订单请求
     * @param wxStoreAdvanceConsumerReqDTO 请求参数
     * @return CreateDineInOrderReqDTO 创建的正餐订单请求
     */
    private CreateDineInOrderReqDTO getCreateInOrderReq(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        WxStoreAdvanceOrderPriceDTO tableOrder = wxStoreAdvanceOrderService.getTableWxStoreAdvanceOrder(wxStoreAdvanceConsumerReqDTO);
        List<WxStoreItemRespDTO> itemList = extractItemList(tableOrder);
        
        if (ObjectUtils.isEmpty(itemList)) {
            log.info("菜品数据为空");
            return null;
        }
        
        CreateDineInOrderReqDTO createDineInOrderReq = convertToOrderRequest(wxStoreAdvanceConsumerReqDTO, itemList);
        initializeOrderItems(createDineInOrderReq, itemList);
        return createDineInOrderReq;
    }

    /**
     * 提取菜品列表
     */
    private List<WxStoreItemRespDTO> extractItemList(WxStoreAdvanceOrderPriceDTO tableOrder) {
        List<WxStoreItemRespDTO> itemList = new LinkedList<>();
        for (WxStoreAdvanceOrderDTO orderDTO : tableOrder.getWxStoreAdvanceOrderDTOS()) {
            itemList.addAll(orderDTO.getItemList());
        }
        return itemList;
    }

    /**
     * 转换为订单请求
     */
    private CreateDineInOrderReqDTO convertToOrderRequest(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO, 
                                                          List<WxStoreItemRespDTO> itemList) {
        CreateDineInOrderReqDTO createDineInOrderReq = wxStoreCreateDineInOrderReqDTOMapStruct.getCreateDineInOrderReq(wxStoreAdvanceConsumerReqDTO, itemList);
        createDineInOrderReq.setDeviceId(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
        createDineInOrderReq.setEnterpriseName(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getEnterpriseName());
        initialCreateDineInOrder(createDineInOrderReq);
        return createDineInOrderReq;
    }

    /**
     * 初始化订单项
     */
    private void initializeOrderItems(CreateDineInOrderReqDTO createDineInOrderReq, List<WxStoreItemRespDTO> itemList) {
        List<DineInItemDTO> dineInItemDTOS = createDineInOrderReq.getDineInItemDTOS();
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            dineInItemDTO.setOriginalPrice(dineInItemDTO.getPrice());
            updateDiscountFlags(dineInItemDTO, itemList);
        }
    }

    /**
     * 更新折扣标志
     */
    private void updateDiscountFlags(DineInItemDTO dineInItemDTO, List<WxStoreItemRespDTO> itemList) {
        for (WxStoreItemRespDTO wxStoreItemRespDTO : itemList) {
            for (WxStoreSkuRespDTO sku : wxStoreItemRespDTO.getSkuList()) {
                if (sku.getSkuGuid().equals(dineInItemDTO.getSkuGuid())) {
                    dineInItemDTO.setIsWholeDiscount(sku.getIsWholeDiscount());
                    dineInItemDTO.setIsMemberDiscount(sku.getIsMemberDiscount());
                }
            }
        }
    }

    /**
     * @param createDineInOrderReqDTO
     * @describle 订单入参初始化createDineInOrderReqDTO
     */
    private void initialCreateDineInOrder(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        List<DineInItemDTO> dineInItemDTOS = createDineInOrderReqDTO.getDineInItemDTOS();
        //商品有些没有数据，随便设值
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            dineInItemDTO.setIsPay(0);
            dineInItemDTO.setItemState(1);
            dineInItemDTO.setFreeCount(BigDecimal.ZERO);
            dineInItemDTO.setReturnCount(BigDecimal.ZERO);
            dineInItemDTO.setIsMemberDiscount(0);
            dineInItemDTO.setIsWholeDiscount(0);
            dineInItemDTO.setMemberDiscount(BigDecimal.ZERO);
            dineInItemDTO.setFreeItemDTOS(Collections.emptyList());
        }
    }

    /**
     * @param createFastFoodReqDTO
     */
    private void initialCreateFastOrder(CreateFastFoodReqDTO createFastFoodReqDTO) {
        List<DineInItemDTO> dineInItemDTOS = createFastFoodReqDTO.getDineInItemDTOS();
        //商品有些没有数据，随便设值
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            dineInItemDTO.setIsPay(0);
            dineInItemDTO.setItemState(1);
            dineInItemDTO.setFreeCount(BigDecimal.ZERO);
            dineInItemDTO.setReturnCount(BigDecimal.ZERO);
            dineInItemDTO.setIsMemberDiscount(1);
            dineInItemDTO.setIsWholeDiscount(1);
            dineInItemDTO.setMemberDiscount(BigDecimal.ZERO);
            dineInItemDTO.setFreeItemDTOS(Collections.emptyList());
        }
    }


    /**
     * 封装正餐订单详情
     *
     * @param wxStoreAdvanceConsumerReqDTO 请求参数
     * @return WxStoreTradeOrderDetailsRespDTO 订单详情响应
     */
    private WxStoreTradeOrderDetailsRespDTO getDineInOrderDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        String tableGuid = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid();
        List<WxStoreTradeOrderDetailsDTO> orderDetailsList = new ArrayList<>();
        Integer orderState = wxStoreSessionDetailsService.getOrderState(tableGuid);
        String merchantBatchGuid = wxStoreSessionDetailsService.getMerchantBatchGuid(tableGuid);

        if (ObjectUtils.isEmpty(merchantBatchGuid)) {
            wxStoreSessionDetailsService.updateMerchantBatchGuid(tableGuid, redisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class));
        }

        if (isOpenTable(wxStoreAdvanceConsumerReqDTO)) {
            return processOpenTableOrders(wxStoreAdvanceConsumerReqDTO, tableGuid, orderDetailsList);
        } else {
            return processClosedTableOrders(wxStoreAdvanceConsumerReqDTO, orderDetailsList, orderState);
        }
    }

    /**
     * 处理开台订单
     */
    private WxStoreTradeOrderDetailsRespDTO processOpenTableOrders(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO,
                                                                   String tableGuid,
                                                                   List<WxStoreTradeOrderDetailsDTO> orderDetailsList) {
        DineinOrderDetailRespDTO dineinOrderDetailResp = getDineinOrderDetailResp(wxStoreAdvanceConsumerReqDTO);
        if (ObjectUtils.isEmpty(dineinOrderDetailResp)) {
            return createErrorResponse("开台后，无法查询到订单");
        }

        dineinOrderDetailResp = getMainOrderDetails(dineinOrderDetailResp);
        List<WxStoreTradeOrderDetailsDTO> pendingOrders = transformPendingOrders(wxStoreAdvanceConsumerReqDTO, dineinOrderDetailResp);
        List<WxStoreTradeOrderDetailsDTO> machineOrders = transformPendingOrders(dineinOrderDetailResp, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO());

        if (!ObjectUtils.isEmpty(machineOrders)) {
            orderDetailsList.addAll(machineOrders);
        }
        if (!ObjectUtils.isEmpty(pendingOrders)) {
            orderDetailsList.addAll(pendingOrders);
        }

        wxStoreSessionDetailsService.saveOrderGuid(tableGuid, dineinOrderDetailResp.getGuid());
        wxStoreSessionDetailsService.saveOrderState(tableGuid, WxOrderStateEnum.PROCESSED.getCode());

        if (ObjectUtils.isEmpty(orderDetailsList)) {
            throw new BusinessException("当前桌台没有订单");
        }

        return getOrderDetailsResp(orderDetailsList, dineinOrderDetailResp, wxStoreAdvanceConsumerReqDTO);
    }

    /**
     * 处理关台订单
     */
    private WxStoreTradeOrderDetailsRespDTO processClosedTableOrders(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO,
                                                                     List<WxStoreTradeOrderDetailsDTO> orderDetailsList,
                                                                     Integer orderState) {
        List<WxStoreTradeOrderDetailsDTO> pendingOrders = transformPendingOrders(wxStoreAdvanceConsumerReqDTO, null);
        boolean isPendingOrRejected = WxOrderStateEnum.PENDING.getCode().equals(orderState) || WxOrderStateEnum.REJECTED.getCode().equals(orderState);

        if (isPendingOrRejected && !ObjectUtils.isEmpty(pendingOrders)) {
            orderDetailsList.addAll(pendingOrders);
            return unAcceptOrders(orderDetailsList, wxStoreAdvanceConsumerReqDTO);
        } else {
            DineinOrderDetailRespDTO dineinOrderDetailResp = getDineinOrderDetailResp(wxStoreAdvanceConsumerReqDTO);
            if (!ObjectUtils.isEmpty(dineinOrderDetailResp)) {
                wxStoreAdvanceConsumerReqDTO.setTradeOrderGuid(dineinOrderDetailResp.getGuid());
                return updateTableDetails(wxStoreAdvanceConsumerReqDTO);
            }
            return createErrorResponse("当前桌台没有订单");
        }
    }

    /**
     * 创建错误响应
     */
    private WxStoreTradeOrderDetailsRespDTO createErrorResponse(String errorMsg) {
        WxStoreTradeOrderDetailsRespDTO response = new WxStoreTradeOrderDetailsRespDTO();
        response.setErrorMsg(errorMsg);
        return response;
    }


    /**
     * 未接单或全拒单
     *
     * @param wxStoreAdvanceConsumerReqDTO person
     * @return order
     */
    private WxStoreTradeOrderDetailsRespDTO unAcceptOrders(List<WxStoreTradeOrderDetailsDTO> wxStoreTradeOrderDetailsDTOS, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO();
        WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = new WxStoreTradeOrderDetailsRespDTO();
        wxStoreTradeOrderDetailsRespDTO.setTableCode(wxStoreConsumerDTO.getTableCode());
        wxStoreTradeOrderDetailsRespDTO.setAreaName(wxStoreConsumerDTO.getAreaName());
        wxStoreTradeOrderDetailsRespDTO.setStoreName(wxStoreConsumerDTO.getStoreName());
        wxStoreTradeOrderDetailsRespDTO.setBrandName(wxStoreConsumerDTO.getBrandName());
        wxStoreTradeOrderDetailsRespDTO.setTradeMode(0);
        wxStoreTradeOrderDetailsRespDTO.setGmtCreate(wxStoreTradeOrderDetailsDTOS.stream().map(WxStoreTradeOrderDetailsDTO::getGmtCreate).min(Comparator.comparing(x -> x)).get());
        wxStoreTradeOrderDetailsRespDTO.setOrderState(wxStoreTradeOrderDetailsDTOS.stream().allMatch(x -> WxOrderStateEnum.CANCELLED.getCode().equals(x.getState())) ? 3 : 0);
        wxStoreTradeOrderDetailsRespDTO.setWxStoreTradeDetailsGroupDTOS(Collections.singletonList(WxStoreTradeDetailsGroupDTO.builder().wxStoreTradeOrderDetailsDTOS(wxStoreTradeOrderDetailsDTOS).build()));
        wxStoreTradeOrderDetailsRespDTO.setGuestCount(wxStoreSessionDetailsService.getDinnerGuestsCount(wxStoreConsumerDTO.getDiningTableGuid()));
        return wxStoreTradeOrderDetailsRespDTO;
    }


    /**
     * 1.一体机突然结账或作废，一体机结账或作废，微信结账
     *
     * @param
     * @return
     */
    private WxStoreTradeOrderDetailsRespDTO updateTableDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        return wxStorePersonOrderDetailsService.getPersonOrderDetails(wxStoreAdvanceConsumerReqDTO);
    }


    /**
     * 封装待处理,接单，拒单，退菜等订单详情
     *
     * @param wxStoreAdvanceConsumerReqDTO
     * @return
     */
    private List<WxStoreTradeOrderDetailsDTO> transformPendingOrders(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO, DineinOrderDetailRespDTO dineinOrderDetailResp) {
        WxStorePendingOrdersQuery query = new WxStorePendingOrdersQuery();
        if (isOpenTable(wxStoreAdvanceConsumerReqDTO) && !ObjectUtils.isEmpty(dineinOrderDetailResp) && !ObjectUtils.isEmpty(dineinOrderDetailResp.getSubOrderDetails()) && !ObjectUtils.isEmpty(dineinOrderDetailResp.getDineInItemDTOS())) {
            log.info("正餐并桌查询:{}", dineinOrderDetailResp.getGuid());
            query.setCombine(dineinOrderDetailResp.getGuid());
        } else {
            log.info("正餐未并桌查询");
            query.setOrderGuid(wxStoreSessionDetailsService.getMerchantBatchGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid()));
        }
        query.setOrderStates(Arrays.asList(0, 2));
        //正餐还有待确认,接单，拒单，退菜的订单
        List<WxStoreMerchantOrderDTO> pendingOrders = wxStoreMerchantOrderService.getPendingOrders(query);
        return getOrderDetails(Optional.ofNullable(pendingOrders).orElse(null));
    }


    /**
     * 封装已经接单的订单详情
     *
     * @param dineinOrderDetailRespDTO
     * @return
     */
    private List<WxStoreTradeOrderDetailsDTO> transformPendingOrders(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, WxStoreConsumerDTO wxStoreConsumerDTO) {
        List<WxStoreTradeOrderDetailsDTO> list = new ArrayList<>();
        List<DineinOrderDetailRespDTO> orderDetailRespList = new ArrayList<>();
        WxStoreConsumerDTO wxStoreConsumerDTO1 = new WxStoreConsumerDTO();
        BeanUtils.copyProperties(wxStoreConsumerDTO, wxStoreConsumerDTO1);
        //如果并桌，遍历所有桌台，取出一体机下的商品
        dineinOrderSeparate(dineinOrderDetailRespDTO, orderDetailRespList);
        if (!ObjectUtils.isEmpty(orderDetailRespList)) {
            for (DineinOrderDetailRespDTO detailRespDTO : orderDetailRespList) {
                List<DineInItemDTO> dineInItemDTOS = detailRespDTO.getDineInItemDTOS();
                if (!ObjectUtils.isEmpty(dineInItemDTOS)) {
                    Map<String, List<DineInItemDTO>> collect = dineInItemDTOS.stream().collect(Collectors.groupingBy(DineInItemDTO::getWxBatch));
                    Set<Map.Entry<String, List<DineInItemDTO>>> entries = collect.entrySet();
                    for (Map.Entry<String, List<DineInItemDTO>> entry : entries) {
                        String merchantGuid = entry.getKey();
                        List<DineInItemDTO> value = entry.getValue();
                        if ("0".equals(merchantGuid)) {
                            list.add(combineWxStoreTradeOrderDetails(wxStoreConsumerDTO, detailRespDTO, value, null));
                        } else {
                            WxStoreMerchantOrderDO wxStoreMerchantOrderDO = wxStoreMerchantOrderService.getOneByGuid(merchantGuid);
                            log.info("根据商品查询微信订单:{}", wxStoreMerchantOrderDO);
                            list.add(combineWxStoreTradeOrderDetails(wxStoreConsumerDTO, detailRespDTO, value, wxStoreMerchantOrderDO));
                        }
                    }
                }
            }
        }
        return list;
    }

    /**
     * @param dineinOrderDetailRespDTO a
     * @param list                     a
     */
    private void dineinOrderSeparate(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, List<DineinOrderDetailRespDTO> list) {
        if (!ObjectUtils.isEmpty(dineinOrderDetailRespDTO)) {
            list.add(dineinOrderDetailRespDTO);
        }
        if (!ObjectUtils.isEmpty(dineinOrderDetailRespDTO) && !ObjectUtils.isEmpty(dineinOrderDetailRespDTO.getSubOrderDetails())) {
            list.addAll(dineinOrderDetailRespDTO.getSubOrderDetails());
        }
    }


    /**
     * 获取快餐订单详情
     * @param wxStoreAdvanceConsumerReqDTO 请求参数
     * @return WxStoreTradeOrderDetailsRespDTO 快餐订单详情响应
     */
    private WxStoreTradeOrderDetailsRespDTO getFastDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO();
        String fastOrderGuid = wxStoreSessionDetailsService.getFastOrderGuid(wxStoreConsumerDTO.getStoreGuid(), wxStoreConsumerDTO.getOpenId());

        if (!ObjectUtils.isEmpty(fastOrderGuid)) {
            DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder().data(fastOrderGuid).build());
            if (!ObjectUtils.isEmpty(orderDetail) && orderDetail.getState() == 1) {
                return wxStoreOrderPayService.orderPay(wxStoreAdvanceConsumerReqDTO);
            }
            wxStoreAdvanceConsumerReqDTO.setTradeOrderGuid(orderDetail.getGuid());
            return wxStorePersonOrderDetailsService.getPersonOrderDetails(wxStoreAdvanceConsumerReqDTO);
        }
        return createErrorResponse("查询订单失败");
    }

    /**
     * 一体机组装商品
     *
     * @param dineinOrderDetailRespDTO
     * @param dineInItemDTOS
     * @param wxStoreMerchantOrderDO
     * @return
     */
    private WxStoreTradeOrderDetailsDTO combineWxStoreTradeOrderDetails(WxStoreConsumerDTO wxStoreConsumerDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO, List<DineInItemDTO> dineInItemDTOS, WxStoreMerchantOrderDO wxStoreMerchantOrderDO) {
        WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO.setGuid(dineinOrderDetailRespDTO.getGuid());
        wxStoreTradeOrderDetailsDTO.setTableGuid(dineinOrderDetailRespDTO.getDiningTableGuid());
        wxStoreTradeOrderDetailsDTO.setOrderNo(dineinOrderDetailRespDTO.getOrderNo());
        wxStoreTradeOrderDetailsDTO.setTableCode(wxStoreConsumerDTO.getTableCode());
        wxStoreTradeOrderDetailsDTO.setDineInItemDTOS(dineInItemDTOS);
        wxStoreTradeOrderDetailsDTO.setTotalPrice(dineInItemDTOS.stream().map(DineInItemDTO::getItemPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
        if (ObjectUtils.isEmpty(wxStoreMerchantOrderDO)) {
            wxStoreTradeOrderDetailsDTO.setGmtCreate(dineinOrderDetailRespDTO.getGmtCreate());

            wxStoreTradeOrderDetailsDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder().wxStoreConsumerDTO(
                    WxStoreConsumerDTO.builder().nickName("服务员").headImgUrl("").sex(2).build()
            ).build());
        } else {
            wxStoreTradeOrderDetailsDTO.setGmtCreate(wxStoreMerchantOrderDO.getGmtCreate());
            wxStoreTradeOrderDetailsDTO.setWxGuid(wxStoreMerchantOrderDO.getGuid());
            wxStoreTradeOrderDetailsDTO.setBatchId(wxStoreMerchantOrderDO.getOrderGuid());
            wxStoreTradeOrderDetailsDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder().wxStoreConsumerDTO(
                    WxStoreConsumerDTO.builder().nickName(wxStoreMerchantOrderDO.getNickName()).headImgUrl(wxStoreMerchantOrderDO.getHeadImgUrl()).build()
            ).build());
        }
        wxStoreTradeOrderDetailsDTO.setState(WxOrderStateEnum.PROCESSED.getCode());
        return wxStoreTradeOrderDetailsDTO;
    }

    /**
     * @param wxStoreAdvanceConsumerReqDTO
     * @describle 计算订单优惠
     */
    @Override
    public DineinOrderDetailRespDTO calculate(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        String orderGuid = getOrderGuid(wxStoreAdvanceConsumerReqDTO);
        if (ObjectUtils.isEmpty(orderGuid)) {
            log.error("没有订单guid");
            return null;
        }
        billCalculateReqDTO.setOrderGuid(orderGuid);
        billCalculateReqDTO.setEnterpriseGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getEnterpriseGuid());
        billCalculateReqDTO.setStoreGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
        billCalculateReqDTO.setMemberLogin(-1);
        log.info("计算订单优惠入参:{}", billCalculateReqDTO);
        DineinOrderDetailRespDTO calculate = null;
        try {
            calculate = getDineinOrderDetailResp(wxStoreAdvanceConsumerReqDTO);
            if (calculate.getState() == 1) {
                calculate = wxStoreDineInBillClientService.calculate(billCalculateReqDTO);
                Integer state = calculate.getState();
                switch (state) {
                    case 1:
                    case 2:
                        state = 1;
                        break;
                    case 4:
                        state = 2;
                        break;
                    case 5:
                    case 6:
                        state = 4;
                }
                calculate.setState(state);
            }
        } catch (Exception e) {
            log.info("calculate订单结果:{}", calculate);
            return null;
        }
//		WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
//		weChatPayReqDTO.setActuallyPayFee(calculate.getActuallyPayFee());
//		weChatPayReqDTO.setOrderFee(calculate.getOrderFee());
//		weChatPayReqDTO.setDiscountFee(calculate.getDiscountFee());
//		weChatPayReqDTO.setDiscountFeeDetailDTOS(calculate.getDiscountFeeDetailDTOS());
//		weChatPayReqDTO.setOrderGuid(orderGuid);
//		redisUtils.set("WX:CALCULATE:" + orderGuid, weChatPayReqDTO);
        return calculate;
    }


    /**
     * @param wxStoreAdvanceConsumerReqDTO
     * @return
     * @describle 下单接口:有人买单时，无法下单
     */
    @Override
    public SubmitReturnDTO submitOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        String tableGuid = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid();
        StoreDeviceDTO masterDevice = organizationClientService.findMasterDevice(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
        if (ObjectUtils.isEmpty(masterDevice)) {
            return SubmitReturnDTO.builder().errorCode(0).errorMsg("当前门店没有配置主机").build();
        }
        if (wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO)) {
            judgeGuestsCount(wxStoreAdvanceConsumerReqDTO);
            Integer orderState = wxStoreSessionDetailsService.getOrderState(tableGuid);
            if (ObjectUtils.isEmpty(orderState)) {
                wxStoreSessionDetailsService.saveOrderState(tableGuid, 0);
            }
            String merchantBatchGuid = wxStoreSessionDetailsService.getMerchantBatchGuid(tableGuid);
            if (ObjectUtils.isEmpty(merchantBatchGuid)) {
                wxStoreSessionDetailsService.updateMerchantBatchGuid(tableGuid, redisUtils.generatdDTOGuid(WxStoreMerchantOrderDTO.class));
            }
            return submitDineInOrder(wxStoreAdvanceConsumerReqDTO);
        } else {
            return submitFast(wxStoreAdvanceConsumerReqDTO);
        }
    }

    /**
     * 查询门店是否有主机
     *
     * @param wxStoreAdvanceConsumerReqDTO person
     * @return bool
     */
    private boolean validateMasterDevice(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        StoreDeviceDTO masterDevice = organizationClientService.findMasterDevice(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
        return ObjectUtils.isEmpty(masterDevice) || StringUtils.isEmpty(masterDevice.getDeviceGuid());
    }

    /**
     * 判断是否输入过就餐人数
     *
     * @param wxStoreAdvanceConsumerReqDTO a
     */
    private void judgeGuestsCount(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        Integer guestsCount = wxStoreAdvanceConsumerReqDTO.getUserCount();
        String diningTableGuid = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid();
        if (ObjectUtils.isEmpty(guestsCount)) {
            guestsCount = wxStoreSessionDetailsService.getDinnerGuestsCount(diningTableGuid);
            if (isOpenTable(wxStoreAdvanceConsumerReqDTO)) {
                DineinOrderDetailRespDTO dineinOrderDetailResp = getDineinOrderDetailResp(wxStoreAdvanceConsumerReqDTO);
                if (!ObjectUtils.isEmpty(dineinOrderDetailResp) && !ObjectUtils.isEmpty(dineinOrderDetailResp.getGuestCount())) {
                    guestsCount = dineinOrderDetailResp.getGuestCount();
                }
            }
            wxStoreAdvanceConsumerReqDTO.setUserCount(guestsCount);
        } else {
            wxStoreSessionDetailsService.updateDinnerGuestsCount(diningTableGuid, guestsCount);
        }
    }

    private void dynamicHeader(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        EnterpriseIdentifier.setEnterpriseGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getEnterpriseGuid());
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getEnterpriseGuid());
        userContext.setStoreGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
        UserContextUtils.put(userContext);
    }

    /**
     * @param wxStoreAdvanceConsumerReqDTO
     * @return
     * @describle 快餐下单
     */
    private SubmitReturnDTO submitFast(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        userInfoCombine(wxStoreAdvanceConsumerReqDTO);
        WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = wxStoreAdvanceOrderService.getSingleWxStoreAdvanceOrder(wxStoreAdvanceConsumerReqDTO);
        CreateFastFoodReqDTO createFastFoodReq = wxStoreCreateFastFoodReqDTOMapstruct.getCreateFastFoodReq(wxStoreAdvanceOrderDTO);
        initialCreateFastOrder(createFastFoodReq);
//		Boolean result = false;
//		try {
//			result = wxStoreEstimateClientService.verifyDineInItemEstimate(createFastFoodReq.getDineInItemDTOS());
//			log.info("快餐下单估清结果:{}", result);
//		} catch (Exception e) {
//			log.error("估清失败", e.getCause());
//		}
        WxStoreAdvanceEstimateDTO wxStoreAdvanceEstimateDTO = wxStoreAdvanceOrderService.checkEstimate(wxStoreAdvanceOrderDTO);
        log.info("估清结果:{}", wxStoreAdvanceEstimateDTO);
        if (!ObjectUtils.isEmpty(wxStoreAdvanceEstimateDTO)) {
            Boolean estimateResult = wxStoreAdvanceEstimateDTO.getEstimateResult();
            List<WxStoreEstimateItem> wxStoreEstimateItemList = wxStoreAdvanceEstimateDTO.getWxStoreEstimateItemList();
            if (estimateResult && !ObjectUtils.isEmpty(wxStoreEstimateItemList)) {
                return SubmitReturnDTO.builder().errorCode(-1).errorMsg("订单所含商品已售完").build();
            }
        }
        createFastFoodReq.setGuestCount(wxStoreAdvanceConsumerReqDTO.getUserCount());
        createFastFoodReq.setRemark(wxStoreAdvanceConsumerReqDTO.getOrderRemark());
        List<DineInItemDTO> dineInItemDTOS = createFastFoodReq.getDineInItemDTOS();
        createFastFoodReq.setWeixinTableCode(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getTableCode());
        createFastFoodReq.setUserWxPublicOpenId(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
        String wxBatch = redisUtils.generatdDTOGuid(CreateFastFoodReqDTO.class);
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            dineInItemDTO.setWxBatch(wxBatch);
            dineInItemDTO.setIsWholeDiscount(0);
            dineInItemDTO.setIsMemberDiscount(0);
        }
        EstimateItemRespDTO estimateResult = wxStoreDineInOrderClientService.addItem(createFastFoodReq);
        String orderGuid = estimateResult.getOrderGuid();
        log.info("快餐id：{}", orderGuid);
        if (StringUtils.isEmpty(orderGuid)) {
            return SubmitReturnDTO.builder().errorCode(0).errorMsg("快餐没有下单成功").build();
        }
        //缓存快餐id
        String openId = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId();
        wxStoreSessionDetailsService.saveFastOrderGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid(), openId, orderGuid);
        DineinOrderDetailRespDTO dineinOrderDetailResp = getDineinOrderDetailResp(wxStoreAdvanceConsumerReqDTO);
        if (Objects.isNull(dineinOrderDetailResp)) {
            throw new BusinessException("没有查询到订单详情");
        }
        WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = intialFastWxStoreMerchantOrder(dineinOrderDetailResp, wxStoreAdvanceConsumerReqDTO);
        wxStoreMerchantOrderService.updateFastOrder(wxStoreMerchantOrderDTO);
        wxStoreAdvanceConsumerReqDTO.setTradeOrderGuid(orderGuid);
        WxStoreAccountDTO wxStoreAccountDTO = new WxStoreAccountDTO();
        wxStoreAccountDTO.setWxStoreAdvanceConsumerReqDTO(wxStoreAdvanceConsumerReqDTO);
        wxStoreAccountDTO.setDineinOrderDetailRespDTO(dineinOrderDetailResp);
        log.info("快餐我的订单:{}", wxStoreAccountDTO);
        wxOrderRecordService.update(wxStoreAccountDTO);
        redisUtils.set("estimate:" + orderGuid, wxStoreAdvanceOrderDTO);
        clearAdvanceDetails(wxStoreAdvanceConsumerReqDTO);
//		fastOrderDeal.add(orderGuid, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO());
        return SubmitReturnDTO.builder().errorCode(0).orderGuid(orderGuid).build();
    }

    private void userInfoCombine(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getEnterpriseGuid());
        userContext.setStoreGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
        userContext.setUserGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
        userContext.setUserName(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getNickName());
        UserContextUtils.put(userContext);
    }

    private Integer chargeItemCount(List<DineInItemDTO> dineInItemDTOS) {
        return dineInItemDTOS.stream().map((e) -> {
            BigDecimal count = e.getCurrentCount();
            if (e.getItemType() == 3) {
                count = BigDecimal.ONE;
            }
            return count;
        }).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
    }

    /**
     * @param wxStoreAdvanceConsumerReqDTO
     * @return
     * @describle 正餐下单
     */
    private SubmitReturnDTO submitDineInOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        dynamicHelper.changeDatasource(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getEnterpriseGuid());
        String tableGuid = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid();
        CreateDineInOrderReqDTO createDineInOrderReqDTO = getCreateInOrderReq(wxStoreAdvanceConsumerReqDTO);
        if (ObjectUtils.isEmpty(createDineInOrderReqDTO)) {
            return SubmitReturnDTO.builder().errorCode(0).errorMsg("当前没有菜品，无法下单").build();
        }
        log.info("正餐下单订单创建:{}", createDineInOrderReqDTO);
        //校验是否加锁
        if (validateMasterDevice(wxStoreAdvanceConsumerReqDTO))
            return SubmitReturnDTO.builder().errorCode(0).errorMsg("该门店暂无接单设备，无法下单").build();

        String advanceOrderRemark = wxStoreAdvanceOrderService.getAdvanceOrderRemark(wxStoreAdvanceConsumerReqDTO);
        //预订单总价
        BigDecimal advanceOrderPrice = wxStoreAdvanceOrderService.getTotalPrice(wxStoreAdvanceConsumerReqDTO);
        log.info("预订单总价:{}", advanceOrderPrice);
        //db存入订单批次
        WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = initialDineInOrderWxStoreMerchantOrder(wxStoreAdvanceConsumerReqDTO, advanceOrderPrice, createDineInOrderReqDTO);
        wxStoreMerchantOrderDTO.setRemark(advanceOrderRemark);
        wxStoreMerchantOrderDTO.setHeadImgUrl(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getHeadImgUrl());
        wxStoreMerchantOrderDTO.setOrderGuid(wxStoreSessionDetailsService.getMerchantBatchGuid(tableGuid));
        DineinOrderDetailRespDTO dineinOrderDetailResp = getDineinOrderDetailResp(wxStoreAdvanceConsumerReqDTO);
        if (!ObjectUtils.isEmpty(dineinOrderDetailResp) && !ObjectUtils.isEmpty(dineinOrderDetailResp.getMainOrderGuid())) {
            wxStoreMerchantOrderDTO.setCombine(dineinOrderDetailResp.getMainOrderGuid());
        }
        wxStoreMerchantOrderService.updateMerchantOrder(wxStoreMerchantOrderDTO);
        if (ObjectUtils.isEmpty(wxStoreMerchantOrderDTO.getGuid())) {
            log.error("没有获取到预订单id");
        }

        List<DineInItemDTO> list = createDineInOrderReqDTO.getDineInItemDTOS();
        for (DineInItemDTO dineInItemDTO : list) {
            dineInItemDTO.setUserWxPublicOpenId(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
            dineInItemDTO.setWxBatch(wxStoreMerchantOrderDTO.getGuid());
        }
        //将当前商品缓存24小时
        if (!ObjectUtils.isEmpty(wxStoreMerchantOrderDTO.getGuid())) {
            WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
            wxStoreMerchantDineInItemDTO.setGuid(wxStoreMerchantOrderDTO.getGuid());
            wxStoreMerchantDineInItemDTO.setDineInItemDTOS(list);
            wxStoreMerchantDineInItemDTO.setItemCount(chargeItemCount(list));
            wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Collections.emptyList());
            wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Collections.emptyList());
            wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Collections.emptyList());
            wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Collections.emptyList());
            wxStoreMerchantDineInItemService.update(wxStoreMerchantDineInItemDTO);
        }
        wxStoreSessionDetailsService.saveOrderState(tableGuid, WxOrderStateEnum.PENDING.getCode());
        clearAdvanceDetails(wxStoreAdvanceConsumerReqDTO);
        refreshTable(tableGuid, wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId(), 1);
        return SubmitReturnDTO.builder().errorCode(0).build();
    }


    /**
     * 清除预订单的整单备注
     *
     * @param wxStoreAdvanceConsumerReqDTO person
     */
    @Override
    public void clearAdvanceDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        wxStoreAdvanceOrderService.delAdvanceTableOrder(wxStoreAdvanceConsumerReqDTO);
        wxStoreAdvanceOrderService.delAdvanceOrderRemark(wxStoreAdvanceConsumerReqDTO);
    }


    /**
     * @param createDineInOrderReqDTO dine
     * @return bool
     * 修改整单备注
     */
    @Override
    public Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        return wxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO);
    }

    /**
     * @param createDineInOrderReqDTO dine
     * @return bool
     * 修改就餐人数
     */
    @Override
    public Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        return wxStoreDineInOrderClientService.updateGuestCount(createDineInOrderReqDTO);
    }

    /**
     * @param wxStoreAdvanceConsumerReqDTO
     * @return
     * @desrible 判断是否开台
     */
    private boolean isOpenTable(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        return wxStoreTableClientService.getOrderGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid()) != null;
    }

    /**
     * @param wxStoreAdvanceConsumerReqDTO
     * @return
     * @describle 获取订单
     */
    @Override
    public DineinOrderDetailRespDTO getDineinOrderDetailResp(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        dynamicHeader(wxStoreAdvanceConsumerReqDTO);
        String guid = getOrderGuid(wxStoreAdvanceConsumerReqDTO);
        if (ObjectUtils.isEmpty(guid)) {
            return null;
        }
        DineinOrderDetailRespDTO orderDetail;
        try {
            log.info("查询订单详情orderDetails:{},入参:{}", guid, wxStoreAdvanceConsumerReqDTO);
            orderDetail = wxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder().data(guid).build());
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
        return orderDetail;
    }

    @Override
    public String getOrderGuid(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        dynamicHeader(wxStoreAdvanceConsumerReqDTO);
        String orderGuid;
        if (wxStoreMenuDetailsService.judgeOrderType(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid())) {
            //正餐
            orderGuid = wxStoreTableClientService.getOrderGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
            log.info("查询订单的详情的orderGuid:{}", orderGuid);
            if (ObjectUtils.isEmpty(orderGuid)) {
                orderGuid = wxStoreSessionDetailsService.getOrderGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
            } else {
                wxStoreSessionDetailsService.saveOrderGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid(), orderGuid);
            }
        } else {
            //快餐
            orderGuid = wxStoreSessionDetailsService.getFastOrderGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid(), wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
        }
        return orderGuid;
    }


    /**
     * 封装快餐商户处理订单
     *
     * @param dineinOrderDetailRespDTO
     * @return
     */
    private WxStoreMerchantOrderDTO intialFastWxStoreMerchantOrder(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setTradeGuid(dineinOrderDetailRespDTO.getGuid());
        wxStoreMerchantOrderDTO.setTotalPrice(dineinOrderDetailRespDTO.getOrderFee());
        wxStoreMerchantOrderDTO.setOpenId(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
        wxStoreMerchantOrderDTO.setNickName(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getNickName());
        wxStoreMerchantOrderDTO.setDiningTableGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
        wxStoreMerchantOrderDTO.setTableCode(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getTableCode());
        wxStoreMerchantOrderDTO.setStoreGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
        wxStoreMerchantOrderDTO.setTradeMode(1);
        //快餐状态初始是待支付
        wxStoreMerchantOrderDTO.setOrderState(5);
        wxStoreMerchantOrderDTO.setItemCount(dineinOrderDetailRespDTO.getDineInItemDTOS().size());
        wxStoreMerchantOrderDTO.setActualGuestsNo(wxStoreAdvanceConsumerReqDTO.getUserCount());
        wxStoreMerchantOrderDTO.setRemark(dineinOrderDetailRespDTO.getRemark());
        wxStoreMerchantOrderDTO.setOperationName(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getNickName());
        return wxStoreMerchantOrderDTO;
    }

    /**
     * 正餐待处理订单初始化
     *
     * @param wxStoreAdvanceConsumerReqDTO
     * @param totalPrice
     * @param createDineInOrderReqDTO
     * @return
     */
    private WxStoreMerchantOrderDTO initialDineInOrderWxStoreMerchantOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO,
                                                                           BigDecimal totalPrice,
                                                                           CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setTotalPrice(totalPrice);
        wxStoreMerchantOrderDTO.setOpenId(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getOpenId());
        wxStoreMerchantOrderDTO.setNickName(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getNickName());
        wxStoreMerchantOrderDTO.setDiningTableGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableGuid());
        String tableCode = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getTableCode();
        String diningTableName = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getDiningTableName();
        if (ObjectUtils.isEmpty(tableCode)) {
            if (ObjectUtils.isEmpty(diningTableName)) {
                throw new BusinessException("当前订单没有桌位信息，请重新扫码获取");
            } else {
                tableCode = diningTableName;
            }
        }
        wxStoreMerchantOrderDTO.setTableCode(tableCode);
        wxStoreMerchantOrderDTO.setStoreGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getStoreGuid());
        wxStoreMerchantOrderDTO.setActualGuestsNo(wxStoreAdvanceConsumerReqDTO.getUserCount());
        wxStoreMerchantOrderDTO.setItemCount(createDineInOrderReqDTO.getDineInItemDTOS().size());
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setAreaGuid(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getAreaGuid());
        wxStoreMerchantOrderDTO.setAreaName(wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO().getAreaName());
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.now());
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.now());
        wxStoreMerchantOrderDTO.setOrderState(0);
        //正餐初始状态是待处理
        wxStoreMerchantOrderDTO.setOrderState(0);
        return wxStoreMerchantOrderDTO;
    }

    /**
     * @param wxStoreTradeOrderDetailsDTOS
     * @describle 设置订单详情总状态
     */
    public Integer setUpOrderState(List<WxStoreTradeOrderDetailsDTO> wxStoreTradeOrderDetailsDTOS) {
        Integer orderState = 0;
        if (!ObjectUtils.isEmpty(wxStoreTradeOrderDetailsDTOS)) {
            WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = wxStoreTradeOrderDetailsDTOS.get(0);
            if (!ObjectUtils.isEmpty(wxStoreTradeOrderDetailsDTO) && wxStoreTradeOrderDetailsDTO.getOrderModel() == 1) {
                Integer state = wxStoreTradeOrderDetailsDTO.getState();
                switch (state) {
                    case 5:
                        orderState = 1;
                        break;
                    case 6:
                    case 2:
                        orderState = 2;
                        break;
                    case 3:
                        //微信暂时没有退款
                        orderState = 3;
                        break;
                    default:
                        break;
                }
                return orderState;
            }
        }
        //已接单：至少一个已接单的，且没有待确认的
        if (wxStoreTradeOrderDetailsDTOS.stream().anyMatch(x -> x.getState() == 1) && wxStoreTradeOrderDetailsDTOS.stream().noneMatch(x -> x.getState() == 0)) {
            orderState = 1;
        }
        //已完成:至少一个已支付的
        if (wxStoreTradeOrderDetailsDTOS.stream().anyMatch(x -> x.getState() == 2 || x.getState() == 6)) {
            orderState = 2;
        }
        //已取消,所有批次都取消
        if (wxStoreTradeOrderDetailsDTOS.stream().allMatch(x -> x.getState() == 3)) {
            orderState = 3;
        }
        return orderState;
    }

    /**
     * @param dineinOrderDetailRespDTO
     * @describle 设置每个订单批次的状态 dineinOrderDetailsDTO:状态(1：未结账， 2：已结账， 3：已退款，4：已作废
     * wxStoreTradeOrderDetailsDTO:订单状态,0待确认，1已下单，2已支付，3已取消，4，已退菜，5待支付，6已完成
     */
    public void setUpOrderState(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO) {
        Integer state = dineinOrderDetailRespDTO.getState();
        Integer orderState = 0;
        switch (state) {
            case 1:
                orderState = dineinOrderDetailRespDTO.getTradeMode() == 0 ? 1 : 5;
                break;
            case 2:
                orderState = 2;
                break;
            case 3:
            case 4:
                orderState = 3;
                break;
            default:
                break;
        }
        wxStoreTradeOrderDetailsDTO.setState(orderState);
    }


    /**
     * 订单转换，没有加入wxStoreAdvanceConsumerDTO
     *
     * @param orderDetail 订单入参
     * @return WxStoreTradeOrderDetailsDTO
     */
    private WxStoreTradeOrderDetailsDTO getOrderDetails(DineinOrderDetailRespDTO orderDetail) {
        //重合商品
        //		combineItem(orderDetail);
        return getWxOrderDetails(orderDetail);
    }

    private WxStoreTradeOrderDetailsDTO getWxOrderDetails(DineinOrderDetailRespDTO orderDetail) {
        log.info("商户订单转微信订单:{}", orderDetail);
        WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = OrderDetailsMap.INSTANCE.getOrderDetails(orderDetail);
        log.info("商户订单转微信订单:{}", wxStoreTradeOrderDetailsDTO);
        wxStoreTradeOrderDetailsDTO.setMainTable(orderDetail.getGuid().equals(orderDetail.getMainOrderNo()) ? 0 : 1);
        //设置订单状态
        setUpOrderState(orderDetail, wxStoreTradeOrderDetailsDTO);
        return wxStoreTradeOrderDetailsDTO;
    }


    /**
     * @param pendingOrders
     * @return
     * @describle 订单转换加入了consumer
     */
    private List<WxStoreTradeOrderDetailsDTO> getOrderDetails(List<WxStoreMerchantOrderDTO> pendingOrders) {
        List<WxStoreTradeOrderDetailsDTO> wxStoreTradeOrderDetails = OrderDetailsMap.INSTANCE.getOrderDetails(pendingOrders);
        for (WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO : wxStoreTradeOrderDetails) {
            WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItem = wxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(wxStoreTradeOrderDetailsDTO.getBatchId());
            if (!ObjectUtils.isEmpty(wxStoreTradeOrderDetailsDTO) && !ObjectUtils.isEmpty(wxStoreMerchantDineInItem)) {
                wxStoreTradeOrderDetailsDTO.setDiscountFeeDetailDTOS(wxStoreMerchantDineInItem.getDiscountFeeDetailDTOS());
                wxStoreTradeOrderDetailsDTO.setDineInItemDTOS(wxStoreMerchantDineInItem.getDineInItemDTOS());
                wxStoreTradeOrderDetailsDTO.setActuallyPayFeeDetailDTOS(wxStoreMerchantDineInItem.getActuallyPayFeeDetailDTOS());
                wxStoreTradeOrderDetailsDTO.setOrderFeeDetailDTOS(wxStoreMerchantDineInItem.getOrderFeeDetailDTOS());
                wxStoreTradeOrderDetailsDTO.setReturnItemDTOS(wxStoreMerchantDineInItem.getReturnItemDTOS());
            }
        }
        return wxStoreTradeOrderDetails;
    }

    /**
     * 返回值封装
     *
     * @param wxStoreTradeOrderDetailsDTOS 1
     * @return WxStoreTradeOrderDetailsRespDTO
     */
    private WxStoreTradeOrderDetailsRespDTO getOrderDetailsResp(List<WxStoreTradeOrderDetailsDTO> wxStoreTradeOrderDetailsDTOS, DineinOrderDetailRespDTO dineinOrderDetailRespDTO, WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        if (!ObjectUtils.isEmpty(wxStoreTradeOrderDetailsDTOS)) {
            ArrayList<DineinOrderDetailRespDTO> orderDetailRespDTOS = new ArrayList<>();
            ArrayList<WxStoreTradeDetailsGroupDTO> groupDTOS = new ArrayList<>();
            dineinOrderSeparate(dineinOrderDetailRespDTO, orderDetailRespDTOS);
            Map<String, DineinOrderDetailRespDTO> respDTOMap = orderDetailRespDTOS.stream().collect(Collectors.toMap(DineinOrderDetailRespDTO::getDiningTableGuid, Function.identity()));

            Map<String, List<WxStoreTradeOrderDetailsDTO>> listMap = wxStoreTradeOrderDetailsDTOS.stream().collect(Collectors.groupingBy(WxStoreTradeOrderDetailsDTO::getTableGuid));
            Set<Map.Entry<String, List<WxStoreTradeOrderDetailsDTO>>> entries = listMap.entrySet();
            for (Map.Entry<String, List<WxStoreTradeOrderDetailsDTO>> entry : entries) {
                String tableGuid = entry.getKey();
                List<WxStoreTradeOrderDetailsDTO> value = entry.getValue();
                WxStoreTradeDetailsGroupDTO build = WxStoreTradeDetailsGroupDTO.builder().tableGuid(tableGuid).tableCode(respDTOMap.get(tableGuid).getDiningTableName()).areaName("").mainTable(tableGuid.equals(dineinOrderDetailRespDTO.getDiningTableGuid()) ? 0 : 1).wxStoreTradeOrderDetailsDTOS(value).build();
                groupDTOS.add(build);
            }
            List<String> collect = groupDTOS.stream().map(WxStoreTradeDetailsGroupDTO::getTableGuid).collect(Collectors.toList());
            List<DineinOrderDetailRespDTO> emptyTable = orderDetailRespDTOS.stream().filter(x -> !collect.contains(x.getDiningTableGuid())).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(emptyTable)) {
                for (DineinOrderDetailRespDTO x : emptyTable) {
                    WxStoreTradeDetailsGroupDTO buid = WxStoreTradeDetailsGroupDTO.builder().wxStoreTradeOrderDetailsDTOS(Collections.emptyList()).tableGuid(x.getDiningTableGuid()).tableCode(respDTOMap.get(x.getDiningTableGuid()).getDiningTableName()).areaName("")
                            .mainTable(x.getDiningTableGuid().equals(dineinOrderDetailRespDTO.getDiningTableGuid()) ? 0 : 1).build();
                    groupDTOS.add(buid);
                }
            }
            List<WxStoreTradeDetailsGroupDTO> collect1 = groupDTOS.stream().sorted(Comparator.comparing(WxStoreTradeDetailsGroupDTO::getMainTable)).collect(Collectors.toList());
            WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = new WxStoreTradeOrderDetailsRespDTO();
            WxStoreConsumerDTO wxStoreConsumerDTO = wxStoreAdvanceConsumerReqDTO.getWxStoreConsumerDTO();
            wxStoreTradeOrderDetailsRespDTO.setWxStoreTradeDetailsGroupDTOS(collect1);
            wxStoreTradeOrderDetailsRespDTO.setOrderNo(dineinOrderDetailRespDTO.getOrderNo());
            wxStoreTradeOrderDetailsRespDTO.setGmtCreate(dineinOrderDetailRespDTO.getGmtCreate());
            wxStoreTradeOrderDetailsRespDTO.setCheckoutTime(dineinOrderDetailRespDTO.getCheckoutTime());
            wxStoreTradeOrderDetailsRespDTO.setCombine(orderDetailRespDTOS.size() > 1 ? 0 : 1);

            Integer tradeMode = dineinOrderDetailRespDTO.getTradeMode();
            wxStoreTradeOrderDetailsRespDTO.setTableCode(tradeMode == 0 ? combineTableCode(dineinOrderDetailRespDTO) : wxStoreConsumerDTO.getAreaName().concat(wxStoreConsumerDTO.getTableCode()));
            wxStoreTradeOrderDetailsRespDTO.setGuestCount(combineGuestCount(dineinOrderDetailRespDTO));
            wxStoreTradeOrderDetailsRespDTO.setOrderState(wxStoreTradeOrderDetailsDTOS.stream().anyMatch(x -> 0 == x.getState()) ? 0 : 1);
            wxStoreTradeOrderDetailsRespDTO.setTradeMode(dineinOrderDetailRespDTO.getTradeMode());
            wxStoreTradeOrderDetailsRespDTO.setAreaName("");
            wxStoreTradeOrderDetailsRespDTO.setStoreName(wxStoreConsumerDTO.getStoreName());
            wxStoreTradeOrderDetailsRespDTO.setBrandName(wxStoreConsumerDTO.getBrandName());


            wxStoreTradeOrderDetailsRespDTO.setTotalPrice(wxStoreTradeOrderDetailsDTOS.stream().filter(x -> !Integer.valueOf(3).equals(x.getState())).map(WxStoreTradeOrderDetailsDTO::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add));

            OrderFeeDetailDTO orderFeeDetailDTO = dineinOrderDetailRespDTO.getOrderFeeDetailDTO();
            Optional.ofNullable(orderFeeDetailDTO).ifPresent(x -> wxStoreTradeOrderDetailsRespDTO.setOrderFeeDetailDTOS(Collections.singletonList(x)));
            Optional.ofNullable(dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS()).ifPresent(wxStoreTradeOrderDetailsRespDTO::setDiscountFeeDetailDTOS);
            List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS();
            wxStoreTradeOrderDetailsRespDTO.setActuallyPayFeeDetailDTOS(Optional.ofNullable(actuallyPayFeeDetailDTOS).orElse(Collections.emptyList()));
            if (!ObjectUtils.isEmpty(actuallyPayFeeDetailDTOS)) {
                wxStoreTradeOrderDetailsRespDTO.setPayWay(actuallyPayFeeDetailDTOS.get(0).getPaymentTypeName());
            }
            log.info("我的订单主单详情:{}", wxStoreTradeOrderDetailsRespDTO);
            return wxStoreTradeOrderDetailsRespDTO;
        }
        return null;
    }

    /**
     * 并桌合并区域
     *
     * @param dineinOrderDetailRespDTO
     * @return
     */
    private String combineTableCode(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        String diningTableName = dineinOrderDetailRespDTO.getDiningTableName();
        List<DineinOrderDetailRespDTO> subOrderDetails = dineinOrderDetailRespDTO.getSubOrderDetails();
        if (ObjectUtils.isEmpty(subOrderDetails)) {
            return diningTableName;
        }
        String collect = subOrderDetails.stream().map(DineinOrderDetailRespDTO::getDiningTableName).collect(Collectors.joining(","));
        log.info("我的订单合并区域:{}", collect);
        if (!org.apache.commons.lang3.StringUtils.isEmpty(collect)) {
            return diningTableName.concat(",").concat(collect);
        }
        return diningTableName;
    }

    /**
     * 并桌后合并就餐人数
     *
     * @param dineinOrderDetailRespDTO order
     * @return num
     */
    private Integer combineGuestCount(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        Integer totalGuestsCount = dineinOrderDetailRespDTO.getGuestCount();
        List<DineinOrderDetailRespDTO> subOrderDetails = dineinOrderDetailRespDTO.getSubOrderDetails();
        if (!ObjectUtils.isEmpty(subOrderDetails)) {
            totalGuestsCount += subOrderDetails.stream().mapToInt(DineinOrderDetailRespDTO::getGuestCount).sum();
        }
        return totalGuestsCount;
    }

    @Override
    public void refreshTable(String tableGuid) {
        wxSocketMsgService.distribute(WxSocketDistributionDTO.builder()
                .distribution(WxDistributionEnum.TABLE_MULTI.getCode())
                .tableGuid(tableGuid)
                .content(JacksonUtils.writeValueAsString(WebSocketMessageDTO.builder().isJump(1).content("").type(1).build()))
                .build()
        );
    }

    @Override
    public void refreshTable(String tableGuid, String openId, Integer isJump) {
        wxSocketMsgService.distribute(WxSocketDistributionDTO.builder()
                .distribution(WxDistributionEnum.TABLE_EXCEPT_CURRENT.getCode())
                .tableGuid(tableGuid)
                .openId(openId)
                .content(JacksonUtils.writeValueAsString(WebSocketMessageDTO.builder().isJump(0).content("").type(1).build()))
                .build()
        );
    }


    /**
     * 并桌订单详情
     *
     * @param dineinOrderDetailRespDTO
     * @return
     */
    @Override
    public DineinOrderDetailRespDTO getMainOrderDetails(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        log.info("订单详情:{}", dineinOrderDetailRespDTO);
        List<DineinOrderDetailRespDTO> subOrderDetails = dineinOrderDetailRespDTO.getSubOrderDetails();
        String mainOrderGuid = dineinOrderDetailRespDTO.getMainOrderGuid();
        Integer upperState = dineinOrderDetailRespDTO.getUpperState();
        if (!ObjectUtils.isEmpty(subOrderDetails)) {
            return dineinOrderDetailRespDTO;
        }

        if (!ObjectUtils.isEmpty(mainOrderGuid) && dineinOrderDetailRespDTO.getTradeMode() == 0 && !"0".equals(mainOrderGuid) && !ObjectUtils.isEmpty(upperState) && upperState == 2) {
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setData(mainOrderGuid);
            DineinOrderDetailRespDTO orderDetail = wxStoreDineInOrderClientService.getOrderDetail(singleDataDTO);
            log.info("并桌订单详情:{}", orderDetail);
            return orderDetail;
        }
        return dineinOrderDetailRespDTO;
    }

    @Override
    public CardAndVolumeDTO validateCardAndVolume(CardAndVolumeDiscountReqDTO cardAndVolumeDiscountReqDTO) {
        CorrectResult<DineinOrderDetailRespDTO> correctResult = wxStoreSessionDetailsService.calculateDetails(wxStoreSessionDetailsService.getOrderGuid(
                cardAndVolumeDiscountReqDTO.getStoreGuid(), cardAndVolumeDiscountReqDTO.getTableGuid(), cardAndVolumeDiscountReqDTO.getOpenId())
                , cardAndVolumeDiscountReqDTO.getOpenId(), cardAndVolumeDiscountReqDTO.getMemberInfoCardGuid(), 2, cardAndVolumeDiscountReqDTO.getVolumeCode());
        if (correctResult.getResult() == 2) {
            CardAndVolumeDTO.builder().result(2).errorMsg("订单状态发生变化").build();
            wxStoreSessionDetailsService.delPrepay(cardAndVolumeDiscountReqDTO.getStoreGuid(), cardAndVolumeDiscountReqDTO.getOpenId());
        }
        if (!ObjectUtils.isEmpty(correctResult.getErrorMsg()))
            return CardAndVolumeDTO.builder().result(1).errorMsg(correctResult.getErrorMsg()).build();
        DineinOrderDetailRespDTO calculate = correctResult.getObj();
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = calculate.getDiscountFeeDetailDTOS();

        List<DiscountFeeDetailDTO> collect;
        if (!ObjectUtils.isEmpty(discountFeeDetailDTOS)) {
            if (cardAndVolumeDiscountReqDTO.getDiscountType() == 0) {
                collect = discountFeeDetailDTOS.stream().filter(x -> 1 == x.getDiscountType()).collect(Collectors.toList());
            } else {
                collect = discountFeeDetailDTOS.stream().filter(x -> 7 == x.getDiscountType()).peek(k -> {
                    if (!ObjectUtils.isEmpty(cardAndVolumeDiscountReqDTO.getVolumeCode()) && !ObjectUtils.isEmpty(cardAndVolumeDiscountReqDTO.getVolumeName())) {
                        k.setDiscountName(cardAndVolumeDiscountReqDTO.getVolumeName());
                    }
                }).collect(Collectors.toList());
            }
            BigDecimal discountFee = collect.get(0).getDiscountFee();
            if (discountFee.compareTo(BigDecimal.ZERO) == 0) {
                return CardAndVolumeDTO.builder().result(0).memberDiscountDTOS(Collections.emptyList()).discountAmount(discountFee).build();
            }
            return CardAndVolumeDTO.builder().result(0).memberDiscountDTOS(MemberDiscountMap.INSTANCE.toMemberDiscount(collect)).discountAmount(discountFee).build();
        }
        return CardAndVolumeDTO.builder().result(1).errorMsg("没有会员优惠").build();
    }

    @Override
    public WxMemberCardRespDTO cardList(WxMemberCardListReqDTO wxMemberCardListReqDTO) {
        try {
            ResponseMemberAndCardInfoDTO memberInfoAndCardList = wxStoreSessionDetailsService
                    .getMemberInfoAndCardList(wxMemberCardListReqDTO.getEnterpriseGuid(), wxMemberCardListReqDTO.getStoreGuid(), wxMemberCardListReqDTO.getOpenId());
            log.info("接收会员卡列表:{}", memberInfoAndCardList);
            if (verifyMemberAvailability(memberInfoAndCardList))
                return WxMemberCardRespDTO.builder().result(1).errorMsg("会员不可用").build();
            List<ResponseMemberCard> memberCardListRespDTOs = memberInfoAndCardList.getMemberCardListRespDTOs();
            List<WxMemberCardDTO> wxMemberCardDTOS = WxMemberCardDTOMap.INSTANCE.fromNewMemberCardListResp(memberCardListRespDTOs);

            WxPrepayReqDTO prepay = wxStoreSessionDetailsService.getPrepay(wxMemberCardListReqDTO.getStoreGuid(), wxMemberCardListReqDTO.getOpenId());
            if (!ObjectUtils.isEmpty(prepay) && !ObjectUtils.isEmpty(prepay.getMemberCardGuid())) {
                wxMemberCardDTOS = wxMemberCardDTOS.stream().peek(x -> {
                    if (x.getMemberInfoCardGuid().equals(prepay.getMemberInfoCardGuid())) {
                        log.info("当前被选中会员卡:{}", x);
                        x.setUck(1);
                    }
                }).collect(Collectors.toList());
            }
            wxMemberCardDTOS = wxMemberCardDTOS.parallelStream().peek(x -> {
//				CardRightDetailsReqDTO cardRightDetailsReqDTO = new CardRightDetailsReqDTO();
//				cardRightDetailsReqDTO.setCardGuid(x.getCardGuid());
//				cardRightDetailsReqDTO.setSystemManagementGuid(x.getSystemManagementGuid());
                RequestCardRightDetails requestCardRightDetails = new RequestCardRightDetails();
                requestCardRightDetails.setMemberInfoCardGuid(x.getMemberInfoCardGuid());
                List<ResponseCardRight> cardRightDetails = hsaBaseClientService.getCardRightDetails(requestCardRightDetails).getData();
                if (!ObjectUtils.isEmpty(cardRightDetails)) {
                    cardRightDetails = cardRightDetails.stream().filter(k -> !ObjectUtils.isEmpty(k.getList()))
                            .filter(k -> k.getList().stream().anyMatch(i -> !StringUtils.isEmpty(i.getRightsName())))
                            .collect(Collectors.toList());
                    cardRightDetails.forEach(k -> {
                        List<ResponseCardRightDetail> collect = k.getList().stream().filter(i -> !StringUtils.isEmpty(i.getRightsName())).collect(Collectors.toList());
                        k.setList(collect);
                    });
                }
                x.setCardRightDetailsRespDTOS(ObjectUtils.isEmpty(cardRightDetails) ? Collections.emptyList() : cardRightDetails);
            }).collect(Collectors.toList());
            log.info("会员卡列表:{}", wxMemberCardDTOS);
            return WxMemberCardRespDTO.builder().result(0).wxMemberCardDTOS(wxMemberCardDTOS).build();
        } catch (Exception e) {
            e.printStackTrace();
            return WxMemberCardRespDTO.builder().errorMsg("查询会员卡列表失败").result(1).build();
        }
    }

    private boolean verifyMemberAvailability(ResponseMemberAndCardInfoDTO memberInfoAndCardList) {
        if (ObjectUtils.isEmpty(memberInfoAndCardList)) {
            return true;
        }
        ResponseMemberInfoDTO memberInfoDTO = memberInfoAndCardList.getMemberInfoDTO();
        return ObjectUtils.isEmpty(memberInfoDTO) || memberInfoDTO.getStateCode() == 0 || ObjectUtils.isEmpty(memberInfoAndCardList.getMemberCardListRespDTOs());
    }

    @Override
    public WxVolumeCodeRespDTO volumeCodeList(WxVolumeCodeReqDTO wxVolumeCodeReqDTO) {
        UserContext userContext = UserContextUtils.get();
        WxPrepayReqDTO prepay = wxStoreSessionDetailsService.getPrepay(wxVolumeCodeReqDTO.getStoreGuid(), wxVolumeCodeReqDTO.getOpenId());
        if (ObjectUtils.isEmpty(prepay) || StringUtils.isEmpty(prepay.getMemberInfoGuid())) {
            log.info("没有会员相关信息");
            return WxVolumeCodeRespDTO.builder().result(0).memberVolumeList(Collections.emptyList()).build();
        }
        try {
            RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
            memberInfoVolumeQueryReqDTO.setEnterpriseGuid(wxVolumeCodeReqDTO.getEnterpriseGuid());
            memberInfoVolumeQueryReqDTO.setBrandGuid(wxVolumeCodeReqDTO.getBrandGuid());
            memberInfoVolumeQueryReqDTO.setMemberInfoGuid(prepay.getMemberInfoGuid());
            memberInfoVolumeQueryReqDTO.setStoreGuid(wxVolumeCodeReqDTO.getStoreGuid());
            memberInfoVolumeQueryReqDTO.setVolumeType(-1);
            memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
            memberInfoVolumeQueryReqDTO.setExcludeBind(1);
            ResponseMemberInfoVolume memberVolume = hsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO).getData();
            log.info("查询会员优惠券列表:{}", memberVolume);
            if (ObjectUtils.isEmpty(memberVolume) || memberVolume.getMayUseVolumeNum() == 0 || ObjectUtils.isEmpty(memberVolume.getMemberVolumeList())) {
                return WxVolumeCodeRespDTO.builder().result(1).errorMsg("查询会员优惠券失败").build();
            }
            List<MemberInfoVolume> memberVolumeList = memberVolume.getMemberVolumeList();

            List<WxVolumeCodeDTO> collect = WxVolumeCodeMap.INSTANCE.fromNewMemberInfoVolumeList(memberVolumeList
                    .parallelStream().filter(x -> 0 == x.getMayUseVolume() && 3 != x.getVolumeType())
                    .collect(Collectors.toList())).parallelStream()
                    .peek(x -> x.setMemberInfoVolumeDetailsRespDTO(hsaBaseClientService.getMemberVolumeDetails(x.getMemberVolumeGuid()).getData()))
                    .collect(Collectors.toList());
//			if (collect.size() == 1) {
//				WxVolumeCodeDTO wxVolumeCodeDTO = collect.get(0);
//				if (wxVolumeCodeDTO.getVolumeCode().equals(prepay.getVolumeCode())) {
//					wxVolumeCodeDTO.setUck(1);
//				}
//			}
            if (allUnableClick(prepay))
                return WxVolumeCodeRespDTO.builder().result(0).memberVolumeList(collect.stream()
                        .peek(x -> x.setVolumeState(x.getVolumeInfoState() == 2 ? -2 : -1))
                        .collect(Collectors.toList())).build();
            collect = collect.parallelStream().peek(x -> {
                dynamicDataSource(userContext);
                BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
                billCalculateReqDTO.setVerify(3);
                billCalculateReqDTO.setVolumeCode(x.getVolumeCode());
                billCalculateReqDTO.setOrderGuid(prepay.getOrderGuid());
                billCalculateReqDTO.setMemberPhone(wxVolumeCodeReqDTO.getOpenId());
                billCalculateReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
                billCalculateReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
                billCalculateReqDTO.setMemberLogin(2);
                DineinOrderDetailRespDTO detailRespDTO = wxStoreWeChatOrderClientService.checkVolume(billCalculateReqDTO);
                log.info("优惠券置灰:{}", detailRespDTO);
                if (!StringUtils.isEmpty(detailRespDTO.getTip()) && (detailRespDTO.getTip().contains("该优惠劵需要达到满")
                        || detailRespDTO.getTip().contains("当前所选菜品不在该优惠") || detailRespDTO.getTip().contains("赠品菜不在优惠券"))) {
                    x.setVolumeState(x.getVolumeState() == 2 ? -2 : -1);
                }
                if (!ObjectUtils.isEmpty(prepay) && StringUtils.hasText(prepay.getVolumeCode()) && prepay.getVolumeCode().equals(x.getVolumeCode())) {
                    log.info("当前优惠券被选中:{}", x);
                    x.setUck(1);
                }
            }).collect(Collectors.toList());
            log.info("滤过后的优惠券列表:{}", JacksonUtils.writeValueAsString(collect));
            return WxVolumeCodeRespDTO.builder().result(0).memberVolumeList(collect).build();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询会员优惠券失败:{}", JacksonUtils.writeValueAsString(wxVolumeCodeReqDTO));
            return WxVolumeCodeRespDTO.builder().result(1).errorMsg("查询会员优惠券失败").build();
        }
    }

    /**
     * 团购券与一体机优惠券判断
     *
     * @param prepay pre
     * @return bool
     */
    private boolean allUnableClick(WxPrepayReqDTO prepay) {
        if (!ObjectUtils.isEmpty(prepay)) {
            String orderGuid = prepay.getOrderGuid();
            BigDecimal discountFee = prepay.getPurchaseGroupFee();
            if (!ObjectUtils.isEmpty(discountFee) && discountFee.compareTo(BigDecimal.ZERO) > 0) {
                return true;
            }
            if (!StringUtils.isEmpty(orderGuid)) {
//				List<VolumeListRespDTO> volumeListRespDTO = memberOrderClientService.verifyVolumeList(orderGuid);
                List<ResponseVolumeList> responseVolumeLists = hsaBaseClientService.consumeVolumeList(orderGuid).getData();
                return !ObjectUtils.isEmpty(responseVolumeLists);
            }
        }
        return false;
    }

//	private StoreDTO getMemberStoreList(StoreListReqDTO storeListReqDTO) {
//		StoreListRespDTO memberStoreList = memberClientService.getMemberStoreList(storeListReqDTO);
//		if (ObjectUtils.isEmpty(memberStoreList) || ObjectUtils.isEmpty(memberStoreList.getStoreList())) {
//			throw new BusinessException("查询会员门店失败");
//		}
//		StoreDTO storeDTO = memberStoreList.getStoreList().stream().filter(x -> x.getStoreKey().equals(UserContextUtils.getStoreGuid())).findFirst().get();
//		if (ObjectUtils.isEmpty(storeDTO) || ObjectUtils.isEmpty(storeDTO.getStoreGuid())) {
//			throw new BusinessException("查询会员门店失败");
//		}
//		return storeDTO;
//	}

    @Override
    public ResponseMemberInfoVolumeDetails volumeCodeDetails(WxVolumeCodeDetailsReqDTO wxVolumeCodeDetailsReqDTO) {
        UserContextUtils.put(JacksonUtils.writeValueAsString(UserInfoDTO.builder()
                .enterpriseGuid(wxVolumeCodeDetailsReqDTO.getEnterpriseGuid())
                .storeGuid(wxVolumeCodeDetailsReqDTO.getStoreGuid())));
        return hsaBaseClientService.getMemberVolumeDetails(wxVolumeCodeDetailsReqDTO.getMemberVolumeGuid()).getData();
    }

    @Override
    public WebSocketMessageDTO orderDetails(WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO) {
        WxUserRecordDO oneByOpenId = wxUserRecordService.getOneByOpenId(wxPaidOrderDetailsReqDTO.getOpenId());
        WxOrderRecordDTO orderRecord = wxOrderRecordService.getOrderRecord(wxPaidOrderDetailsReqDTO.getOrderGuid());
        TableDTO tableByGuid = wxStoreTableClientService.getTableByGuid(orderRecord.getTableGuid());
        WxStoreAdvanceConsumerReqDTO build = WxStoreAdvanceConsumerReqDTO.builder().tradeOrderGuid(wxPaidOrderDetailsReqDTO.getOrderGuid()).wxStoreConsumerDTO(
                WxUserRecordMapstruct.INSTANCE.getWxStoreConsumer(oneByOpenId)
        ).build();
        build.getWxStoreConsumerDTO().setDiningTableGuid(orderRecord.getTableGuid());
        build.getWxStoreConsumerDTO().setTableCode(tableByGuid.getCode());
        build.getWxStoreConsumerDTO().setAreaName(tableByGuid.getAreaName());
        build.getWxStoreConsumerDTO().setAreaName(tableByGuid.getAreaName());
        build.getWxStoreConsumerDTO().setStoreGuid(tableByGuid.getStoreGuid());
        build.getWxStoreConsumerDTO().setStoreName(tableByGuid.getStoreName());
        build.getWxStoreConsumerDTO().setBrandGuid(orderRecord.getBrandGuid());
        build.getWxStoreConsumerDTO().setEnterpriseGuid(wxPaidOrderDetailsReqDTO.getEnterpriseGuid());
        build.setPageNum(1);
        return getTableOrderDetails(build);
    }

    @Override
    public void removeFastGuestCount(String diningTableGuid, String openId) {
        redisUtils.delete(CacheName.USER_COUNT + ":" + diningTableGuid + ":" + openId);
    }

    @Override
    public void transformSurchargeCache(String tableGuid, String orderGuid) {
        // 查询订单附加费缓存
        String orderSurchargeStr = (String) redisUtils.get(CacheName.ORDER_SURCHARGE_KEY + orderGuid);
        if (StringUtils.isEmpty(orderSurchargeStr)) {
            return;
        }
        List<SurchargeLinkDTO> surchargeLinkList = JacksonUtils.toObjectList(SurchargeLinkDTO.class, orderSurchargeStr);
        for (SurchargeLinkDTO surchargeLink : surchargeLinkList) {
            redisUtils.setNx(String.format(CacheName.TABLE_SURCHARGE_KEY, tableGuid, surchargeLink.getSurchargeGuid()),
                    JacksonUtils.writeValueAsString(surchargeLink), surchargeLink.getEffectiveTime() * 60L);
        }
    }

    @Override
    public List<SurchargeLinkDTO> filterTimeLimitTableSurchargeList(String tableGuid, List<SurchargeLinkDTO> surchargeLinkList) {
        if (CollectionUtils.isEmpty(surchargeLinkList)) {
            return Collections.emptyList();
        }
        return surchargeLinkList.stream()
                .filter(e -> !redisUtils.hasKey(String.format(CacheName.TABLE_SURCHARGE_KEY, tableGuid, e.getSurchargeGuid())))
                .collect(Collectors.toList());
    }

    @Override
    public List<SurchargeLinkDTO> querySurchargeList(Integer orderModel, String areaGuid) {
        try {
            SurchargeConditionQuery surchargeQuery = new SurchargeConditionQuery();
            surchargeQuery.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
            surchargeQuery.setTradeMode(orderModel);
            surchargeQuery.setAreaGuid(areaGuid);
            List<SurchargeLinkDTO> surchargeLinkList = businessClientService.listSurchargeByCondition(surchargeQuery);
            log.info("微信扫码点餐查询附加费明细返回:{}", JacksonUtils.writeValueAsString(surchargeLinkList));
            // 查询是否需要过滤
            surchargeLinkList = filterTimeLimitTableSurchargeList(WeixinUserThreadLocal.getDiningTableGuid(), surchargeLinkList);
            log.info("微信扫码点餐查询附加费明细过滤之后:{}", JacksonUtils.writeValueAsString(surchargeLinkList));
            return surchargeLinkList;
        } catch (Exception e) {
            log.error("微信扫码点餐查询附加费明细异常，e:{}", e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public void setOrderSurchargeCache(String orderGuid, List<SurchargeLinkDTO> surchargeLinkList) {
        if (CollectionUtils.isEmpty(surchargeLinkList)) {
            return;
        }
        redisUtils.setEx(CacheName.ORDER_SURCHARGE_KEY + orderGuid, JacksonUtils.writeValueAsString(surchargeLinkList), 30, TimeUnit.MINUTES);
    }

    private void dynamicDataSource(UserContext userContext) {
        EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
        UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
    }

}
