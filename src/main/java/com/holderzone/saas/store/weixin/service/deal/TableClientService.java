package com.holderzone.saas.store.weixin.service.deal;

import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-table",
		fallbackFactory = TableClientService.TableClientServiceFullback.class)
public interface TableClientService {

	@ApiOperation("获取当前桌台guid")
	@PostMapping("/table/getOrderGuid/{tableGuid}")
	String getOrderGuid(@PathVariable("tableGuid") String tableGuid);

	@ApiModelProperty("根据桌台guid，查桌台详情")
	@PostMapping("/table/details/{tableGuid}")
	TableDTO getTableByGuid(@PathVariable("tableGuid") String tableGuid);

	@ApiOperation("查询桌台列表")
	@PostMapping("/table/web/query")
	List<TableBasicDTO> listByWeb(@RequestBody TableBasicQueryDTO tableBasicQueryDTO);

	@Component
	@Slf4j
	class TableClientServiceFullback implements FallbackFactory<TableClientService> {
		@Override
		public TableClientService create(Throwable throwable) {
			return new TableClientService() {

				@Override
				public String getOrderGuid(String tableGuid) {
					log.error("获取订单id失败:{}",tableGuid);
					return null;
				}

				@Override
				public TableDTO getTableByGuid(String tableGuid) {
					log.error("获取桌台详情失败:{}",tableGuid);
					return null;
				}

				@Override
				public List<TableBasicDTO> listByWeb(TableBasicQueryDTO tableBasicQueryDTO) {
					log.error("获取桌台列表失败:{}",tableBasicQueryDTO,throwable);
					return null;
				}
			};
		}
	}
}
