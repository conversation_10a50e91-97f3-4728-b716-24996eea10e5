package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.weixin.config.OverallConfig;
import com.holderzone.saas.store.weixin.service.TcdOrderService;
import com.holderzone.saas.store.weixin.service.rpc.TradeClientService;
import com.holderzone.saas.store.weixin.utils.HttpsClientUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executor;

/**
 * 赚餐订单服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TcdOrderServiceImpl implements TcdOrderService {

    private final Executor asyncOrderTcdExecutor;

    private final TradeClientService tradeClientService;

    private final OverallConfig overallConfig;

    @Override
    public void asyncOrder(String orderGuid) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        UserContext userContext = UserContextUtils.get();
        asyncOrderTcdExecutor.execute(() -> {
            // 切换数据源
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            WeixinUserThreadLocal.put(JacksonUtils.writeValueAsString(wxMemberSessionDTO));
            // 查询订单详情
            OrderGuidsDTO orderGuidsDTO = new OrderGuidsDTO();
            orderGuidsDTO.setOrderGuid(orderGuid);
            orderGuidsDTO.setEnterpriseGuid(WeixinUserThreadLocal.getEnterpriseGuid());
            OrderDetailPushMqDTO wxOrderDTO = tradeClientService.findByWxOrderGuid(orderGuidsDTO);
            // 快餐订单同步
            log.info("赚餐小程序下单快餐需要同步到赚餐:orderGuid:{},入参:{}", orderGuid, JacksonUtils.writeValueAsString(wxOrderDTO));
            // 赚餐推送订单
            String zcResponse = HttpsClientUtils.doPostJSON(overallConfig.getPushOrderDetailUrl(), wxOrderDTO);
            log.info("订单推送赚餐返回:{}", zcResponse);
            if (StringUtils.isEmpty(zcResponse)) {
                log.error("订单推送赚餐返回失败");
                throw new BusinessException("已结账订单推送错误");
            }
        });
    }
}
