package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTableStickDO
 * @date 2019/03/05 13:56
 * @description 微信桌贴DO
 * @program holder-saas-store
 */
@NoArgsConstructor
@AllArgsConstructor
@TableName("hsw_weixin_table_stick")
@Data
public class WxStoreTableStickDO {

    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @ApiModelProperty("唯一标识")
    @TableId("guid")
    private String guid;

    @ApiModelProperty("模板库guid，用于比对模板库模板是否已经购买")
    private String modelGuid;

    @ApiModelProperty("桌贴名字")
    private String name;

    @ApiModelProperty("所属模板分类GUID")
    private String categoryGuid;

    @ApiModelProperty("原价")
    private BigDecimal originalPrice;

    @ApiModelProperty("售价")
    private BigDecimal price;

    @ApiModelProperty("是否推荐:0/否,1/是")
    private String isRecommended;

    @ApiModelProperty("背景颜色")
    private String backColor;

    @ApiModelProperty("背景宽度")
    private Double backWidth;

    @ApiModelProperty("背景高度")
    private Double backHeight;

    @ApiModelProperty("单位:0/毫米,1/厘米,2/像素,3/英寸")
    private String unit;

    @ApiModelProperty("背景图片，包含背景颜色")
    private String bgUrl;

    @ApiModelProperty("背景图片")
    private String backImage;

    @ApiModelProperty("背景图展示策略:0/原图,1/高100%,2/宽100%,3/压缩")
    private String backStrategy;

    @ApiModelProperty("图片元素")
    private String elementImage;

    @ApiModelProperty("logo图片")
    private String elementLogo;

    @ApiModelProperty("logo是否可更换:0/否,1/是")
    private String logoSwitch;

    @ApiModelProperty("控制桌贴编辑时打开log，0关闭，1打开")
    private Integer isLogoShow;

    @ApiModelProperty("形状元素")
    private String elementShape;

    @ApiModelProperty("门店名称")
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String storeNameText;

    @ApiModelProperty("门店名称的字体颜色")
    private String storeNameTextColor;

    @ApiModelProperty("门店描述")
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String storeDescText;

    @ApiModelProperty("门店描述的字体颜色")
    private String storeDescTextColor;

    @ApiModelProperty("二维码描述")
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String qrCodeText;

    @ApiModelProperty("二维码描述的字体颜色")
    private String qrCodeTextColor;

    @ApiModelProperty("wifi描述")
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String wifiText;

    @ApiModelProperty("wifi描述的字体颜色")
    private String wifiTextColor;

    @ApiModelProperty("wifi密码")
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String wifiPassword;

    @ApiModelProperty("wifi密码字体颜色")
    private String wifiPasswordColor;

    @ApiModelProperty("赞助商字体的颜色")
    private String sponsorsTextColor;

    @ApiModelProperty("赞助商名字")
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String sponsorsText;

    @ApiModelProperty("区域是否显示:0/否,1/是")
    private String areaShow;

    @ApiModelProperty("区域名称")
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String areaText;

    @ApiModelProperty("区域名称的字体颜色")
    private String areaTextColor;

    @ApiModelProperty("桌号是否显示:0/否,1/是")
    private String tableNumberShow;

    @ApiModelProperty("桌号名称")
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String tableNumberText;

    @ApiModelProperty("桌号名称的字体颜色")
    private String tableNumberTextColor;

    @ApiModelProperty("二维码")
    private String qrCode;

    @ApiModelProperty("创建者GUID")
    private String createStaffGuid;

    @ApiModelProperty("更新者GUID")
    private String updateStaffGuid;

    @ApiModelProperty("是否启用/上架:0/否,1/是")
    private String isEnable;

    @ApiModelProperty("是否删除")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("缩略图")
    private String previewImg;

    @ApiModelProperty("是否是模板（0否，1是）")
    private Integer isModel;

}
