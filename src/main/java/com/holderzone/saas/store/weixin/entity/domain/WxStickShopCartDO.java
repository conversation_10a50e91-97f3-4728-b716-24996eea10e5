package com.holderzone.saas.store.weixin.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickShopCartDO
 * @date 2019/03/11 15:02
 * @description 微信桌贴购物车DO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("hsw_weixin_stick_shop_cart")
public class WxStickShopCartDO {

    private Long id;

    @TableId("guid")
    private String guid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 桌贴模板guid
     */
    private String modelGuid;

}
