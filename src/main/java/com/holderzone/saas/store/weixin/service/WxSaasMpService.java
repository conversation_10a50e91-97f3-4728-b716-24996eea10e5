package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxSaasMpService
 * @date 2019/03/22 17:38
 * @description 微信公众号相关功能Service
 * @program holder-saas-store
 */
public interface WxSaasMpService {

    /**
     * 第三方平台带公众号开发时，通过appId获取当前对应公众号的WxMpService
     *
     * @param wxStoreAuthorizerInfoDO 当前公众号的授权信息
     * @return
     * @throws WxErrorException
     */
    WxMpService getWxMpService(WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO) throws WxErrorException;
    
    
}
