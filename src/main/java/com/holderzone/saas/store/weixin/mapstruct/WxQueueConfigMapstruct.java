package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.saas.store.dto.weixin.resp.WxQueueConfigDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxQueueConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueConfigMapstruct
 * @date 2019/05/10 11:33
 * @description 门店微信线上排队配置mapstruct
 * @program holder-saas-store
 */
@Mapper(componentModel = "spring")
@Component
public interface WxQueueConfigMapstruct {

    @Mappings({
            @Mapping(target = "distant", ignore = true),
            @Mapping(target = "maxDistant", ignore = true)
    })
    WxQueueConfigDTO queueConfigDO2DTO(WxQueueConfigDO wxQueueConfigDO);

    @Mapping(target = "distant", ignore = true)
    WxQueueConfigDO queueConfigDTO2DO(WxQueueConfigDTO wxQueueConfigDTO);
}
