package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.table.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreTableAppClientService
 * @date 2019/3/28
 */
@Component
@FeignClient(name = "holder-saas-aggregation-app", fallbackFactory = WxStoreTableAppClientService.WxStoreTableAppFullback.class)
public interface WxStoreTableAppClientService {

    @PostMapping("/table/open")
    Result<String> openTable(OpenTableDTO openTableDTO);

    @PostMapping("/table/close")
    Result<String> closeTable(CancelOrderReqDTO cancelOrderReqDTO);

    @PostMapping("/table/separate")
    Result<String> separateTable(TableOrderCombineDTO tableOrderCombineDTO);

    @PostMapping("/table/query")
    Result<List<TableOrderDTO>> queryTable(TableBasicQueryDTO tableBasicQueryDTO);

    @PostMapping("/area/query")
    Result<List<AreaDTO>> queryArea(BaseDTO storeDTO);

    @PostMapping("/table/lock")
    Result<Boolean> tableLock(TableLockDTO tableLockDTO);

    @PostMapping("/release/lock")
    Result<Boolean> releaseLock(TableLockDTO tableLockDTO);

    @PostMapping("/table/combine")
    Result<List<String>> combine(TableCombineDTO tableCombineDTO);

    @PostMapping("/table/turn")
    Result<Boolean> turnTale(TurnTableDTO turnTableDTO);

    @Component
    @Slf4j
    class WxStoreTableAppFullback implements FallbackFactory<WxStoreTableAppClientService> {
        @Override
        public WxStoreTableAppClientService create(Throwable throwable) {
            return new WxStoreTableAppClientService() {

                @Override
                public Result<String> openTable(OpenTableDTO openTableDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<String> closeTable(CancelOrderReqDTO cancelOrderReqDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<String> separateTable(TableOrderCombineDTO tableOrderCombineDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<List<TableOrderDTO>> queryTable(TableBasicQueryDTO tableBasicQueryDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<List<AreaDTO>> queryArea(BaseDTO storeDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<Boolean> tableLock(TableLockDTO tableLockDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<Boolean> releaseLock(TableLockDTO tableLockDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<List<String>> combine(TableCombineDTO tableCombineDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Result<Boolean> turnTale(TurnTableDTO turnTableDTO) {
                    log.error("远程调用失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }
            };
        }
    }
}
