package com.holderzone.saas.store.weixin.service.deal;

import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = TradeClientService.TradeClientServiceFullback.class)
public interface TradeClientService {

    @ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
    @PostMapping("/we_chat/get_order")
    OrderWechatDTO getOrder(@RequestBody String orderGuid);

    @Component
    @Slf4j
    class TradeClientServiceFullback implements FallbackFactory<TradeClientService> {
        @Override
        public TradeClientService create(Throwable throwable) {
            return new TradeClientService() {

                @Override
                public OrderWechatDTO getOrder(String orderGuid) {
                    log.error("查询订单失败:{}", orderGuid);
                    return null;
                }
            };
        }
    }
}
