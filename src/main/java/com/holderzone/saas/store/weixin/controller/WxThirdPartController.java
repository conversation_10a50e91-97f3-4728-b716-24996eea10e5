package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.req.WxQueryThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxThirdPartUserInfoRespDTO;
import com.holderzone.saas.store.weixin.service.WxThirdPartUserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/9/16 16:54
 * @description
 */
@Api("会员第三方接口管理")
@RestController
@RequestMapping("/wx_third_part")
@Slf4j
public class WxThirdPartController {

    @Autowired
    private WxThirdPartUserInfoService wxThirdPartUserInfoService;

    @PostMapping("/save_or_update_third_part_user_info")
    @ApiOperation(value = "新增或更新第三方会员信息")
    public Boolean saveOrUpdateThirdPartUserInfo(@RequestBody WxThirdPartUserInfoReqDTO wxThirdPartUserInfoReqDTO) {
        log.info("新增或更新第三方会员信息请求入参：wxThirdPartUserInfoReqDTO{}", JacksonUtils.writeValueAsString(wxThirdPartUserInfoReqDTO));
        return wxThirdPartUserInfoService.saveOrUpdateThirdPartUserInfo(wxThirdPartUserInfoReqDTO);
    }


    @PostMapping("/check_third_part_user_info")
    @ApiOperation(value = "校验第三方会员信息")
    public WxThirdPartUserInfoRespDTO checkThirdPartUserInfo(@RequestBody WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO) {
        log.info("校验第三方会员信息请求入参：wxQueryThirdPartUserInfoReqDTO{}", JacksonUtils.writeValueAsString(wxQueryThirdPartUserInfoReqDTO));
        return wxThirdPartUserInfoService.checkThirdPartUserInfo(wxQueryThirdPartUserInfoReqDTO);
    }

    @PostMapping("/query_third_part_user_info")
    @ApiOperation(value = "查询第三方会员信息")
    public WxThirdPartUserInfoRespDTO queryThirdPartUserInfo(@RequestBody WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO) {
        log.info("查询第三方会员信息请求入参：wxQueryThirdPartUserInfoReqDTO{}", JacksonUtils.writeValueAsString(wxQueryThirdPartUserInfoReqDTO));
        return wxThirdPartUserInfoService.queryThirdPartUserInfo(wxQueryThirdPartUserInfoReqDTO);
    }
}
