package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxMemberVolumeInfoListReqDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;
import com.holderzone.saas.store.weixin.service.WxMemberCenterVolumeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/wx_member_center_volume")
@Slf4j
@Api("会员中心优惠券")
public class WxMemberCenterVolumeController {

	private final WxMemberCenterVolumeService wxMemberCenterVolumeService;

	@Autowired
	public WxMemberCenterVolumeController(WxMemberCenterVolumeService wxMemberCenterVolumeService) {
		this.wxMemberCenterVolumeService = wxMemberCenterVolumeService;
	}

	@ApiOperation("会员优惠券列表")
	@PostMapping(value = "/volume_list")
	public ResponseMemberInfoVolume volumeInfoList(@RequestBody WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO) {
		log.info("优惠券列表入参:{}", JacksonUtils.writeValueAsString(wxMemberVolumeInfoListReqDTO));
		return wxMemberCenterVolumeService.volumeInfoList(wxMemberVolumeInfoListReqDTO);
	}

	@ApiModelProperty(value = "优惠券详情")
	@PostMapping(value = "/volume_details")
	public WxMemberInfoVolumeDetailsRespDTO volumeCodeDetails(@RequestBody WxVolumeDetailReqDTO wxVolumeDetailReqDTO) {
		EnterpriseIdentifier.setEnterpriseGuid(wxVolumeDetailReqDTO.getEnterpriseGuid());
		log.info("优惠券详情入参:{}", JacksonUtils.writeValueAsString(wxVolumeDetailReqDTO));
		return wxMemberCenterVolumeService.volumeCodeDetails(wxVolumeDetailReqDTO);
	}
}
