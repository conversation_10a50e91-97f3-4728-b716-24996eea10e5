package com.holderzone.saas.store.weixin.mapstruct;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreDineinOrderDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreUserOrderDTO;
import io.swagger.annotations.ApiModelProperty;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreUserOrderMapper
 * @date 2019/4/8
 */

@Component
@Mapper(componentModel = "spring")
public interface WxStoreUserOrderMapper {

	default List<String> getDineInItems(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
		LinkedList<String> itemNames = new LinkedList<>();
		List<DineInItemDTO> dineInItemDTOS = dineinOrderDetailRespDTO.getDineInItemDTOS();
		for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
			itemNames.add(dineInItemDTO.getItemName());
		}
		return itemNames;
	}

	@Mappings({
			@Mapping(target = "guid",source = "guid"),
			@Mapping(target = "tradeMode",source = "tradeMode"),
			@Mapping(target = "actuallyPayFee",source = "orderFee"),
			@Mapping(target = "orderNo",source = "orderNo"),
			@Mapping(target = "checkoutTime",source = "checkoutTime"),
			@Mapping(target = "gmtCreate",source = "gmtCreate"),
			@Mapping(target = "itemNames",expression = "java(getDineInItems(dineinOrderDetailRespDTO))"),
	})
	WxStoreDineinOrderDetailsRespDTO getWxStoreDineinOrderDetails(DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

	@Mappings({
			@Mapping(target = "wxStoreAdvanceConsumerReqDTO",source = "wxStoreAdvanceConsumerReqDTO"),
			@Mapping(target = "orders",source = "orderDetailLis"),
	})
	WxStoreUserOrderDTO getWxStoreUserOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO,List<DineinOrderDetailRespDTO> orderDetailLis);
}
