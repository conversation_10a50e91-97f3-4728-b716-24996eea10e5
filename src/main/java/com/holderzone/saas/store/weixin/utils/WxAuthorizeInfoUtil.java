package com.holderzone.saas.store.weixin.utils;

import com.holderzone.saas.store.weixin.config.WxThirdOpenConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxAuthorizeInfoUtil
 * @date 2019/03/18 12:02
 * @description 微信公众号授权工具类
 * todo 用于获取授权方公众号信息，刷新用户accessToken等
 * @program holder-saas-store
 */
@Component
public class WxAuthorizeInfoUtil {

    public void getAccessToken(String appId, boolean forceRefresh){

    }

}
