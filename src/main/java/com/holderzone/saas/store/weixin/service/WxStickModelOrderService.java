package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.WxStickOrderCallBackDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickOrderRespDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickModelOrderService
 * @date 2019/03/15 16:43
 * @description 微信桌贴模板购买service
 * @program holder-saas-store
 */
public interface WxStickModelOrderService {

    /**
     * 下单接口
     *
     * @param wxStickModelOrderDTO 轮询返回支付结果
     * @return
     */
    WxStickOrderRespDTO order(WxStickModelOrderDTO wxStickModelOrderDTO);

    /**
     * 轮询接口
     *
     * @param wxStickOrderRespDTO
     * @return
     */
    WxStickOrderRespDTO polling(WxStickOrderRespDTO wxStickOrderRespDTO);

    String callBack(WxStickOrderCallBackDTO wxStickOrderCallBackDTO);
}
