package com.holderzone.saas.store.weixin.controller;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.queue.HolderQueueDTO;
import com.holderzone.saas.store.dto.queue.HolderQueueQueueRecordDTO;
import com.holderzone.saas.store.dto.queue.ItemGuidDTO;
import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import com.holderzone.saas.store.dto.queue.WxQueueListDTO;
import com.holderzone.saas.store.weixin.service.WxQueueService;
import com.holderzone.saas.store.weixin.service.rpc.QueueClientService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueController
 * @date 2019/09/16 11:02
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@RestController
@Slf4j
@RequestMapping("/wx_queue")
@Api("微信用户线上排队信息Controller")
public class WxQueueController {

    @Autowired
    WxQueueService wxQueueService;
    @Autowired
    QueueClientService queueClientService;

    @PostMapping("/query_by_guid")
    @ApiOperation(value = "用户所有门店的排队信息",notes = " 排队状态 0:队列中,1:过号,2:叫号中,3:已就餐,4:已取消")
    public List<WxQueueListDTO> queryByGuid(@RequestBody @Valid QueueWechatDTO queueWechatDTO) {
        log.info("获取用户排队信息请求入参：{}", JacksonUtils.writeValueAsString(queueWechatDTO));
		List<WxQueueListDTO> wxQueueListDTOS = queueClientService.queryByUser(queueWechatDTO);
		log.info("排队列表:{}",wxQueueListDTOS);
		if (!ObjectUtils.isEmpty(wxQueueListDTOS)) {
			wxQueueListDTOS = wxQueueListDTOS.stream().sorted(Comparator.comparing(WxQueueListDTO::getTime).reversed()).collect(Collectors.toList());
			log.info("有序排队列表:{}",JacksonUtils.writeValueAsString(wxQueueListDTOS));
		}
		return wxQueueListDTOS;
	}

    @PostMapping("/query_detail")
    @ApiOperation(value = "排队详情",notes = " 排队状态 0:队列中,1:过号,2:叫号中,3:已就餐,4:已取消")
    public HolderQueueQueueRecordDTO getQueueDetails(@RequestBody ItemGuidDTO itemGuidDTO) {
        log.info("微信排队获取详情请求入参：{}", itemGuidDTO);
        String enterpriseGuid = itemGuidDTO.getEnterpriseGuid();
		UserContextUtils.putErp(enterpriseGuid );
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        HolderQueueQueueRecordDTO queueDetails = queueClientService.getQueueDetails(itemGuidDTO);
        if(queueDetails==null||queueDetails.getQueue()==null) {
        	return null;
        }
        String storeGuid = queueDetails.getQueue().getStoreGuid();
        StoreConfigDTO query = queueClientService.query(storeGuid);
        
        HolderQueueDTO queue = queueDetails.getQueue();
        queue.setIsEnableRecovery(query.getIsEnableRecovery());
        if (query.getIsEnableRecovery().booleanValue()){
            queue.setRecoveryNum(query.getRecoveryNum());
        }
        return queueDetails;
    }

    @GetMapping("/update_cancel_queue")
    @ApiOperation(value = "取消排队")
    public Boolean cancelQueue(@RequestParam("queueGuid") String queueGuid, @RequestParam("enterpriseGuid") String enterpriseGuid) {
        log.info("取消排队请求入参：queueGuid{},enterpriseGuid{}", queueGuid,enterpriseGuid);
        return queueClientService.cancel(queueGuid,enterpriseGuid);
    }

    @PostMapping("/groupingByQueueStatus")
    @ApiOperation("统计各排队分组数量")
    public Map<Integer, Integer> queryQueueStatusNum(QueueWechatDTO queueWechatDTO){
        log.info("排队分组请求入参：{}", JacksonUtils.writeValueAsString(queueWechatDTO));
        return wxQueueService.queryQueueStatusNum(queueWechatDTO);
    }
}