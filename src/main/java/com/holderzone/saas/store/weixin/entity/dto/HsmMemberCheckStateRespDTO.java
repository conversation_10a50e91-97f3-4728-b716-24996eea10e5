package com.holderzone.saas.store.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmMemberCheckStateRespDTO
 * @date 2019/06/25 15:33
 * @description 验证会员是否被禁用请求入参
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ToString
@ApiModel(value = "验证会员是否被禁用请求入参")
@Deprecated
public class HsmMemberCheckStateRespDTO {

    /**
     * 登录接口返回状态  true 登录成功 false 登录失败
     */
    private Boolean loginState;
    /**
     * 登录失败原因
     */
    private String failReason;

    private String phoneNum;

    private String nickName;

    private String openId;

    @ApiModelProperty("0正常， 1禁用")
    private int accountState;

    private String reasonForDisable;
    @ApiModelProperty("是否为第一次注册")
    private boolean firstReg; 
}
