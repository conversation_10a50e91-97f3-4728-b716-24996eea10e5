package com.holderzone.saas.store.weixin;

import com.holderzone.saas.store.weixin.utils.SpringContextUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication
@EnableEurekaClient
@EnableSwagger2
@EnableFeignClients
@EnableAsync
@EnableScheduling
@EnableApolloConfig
@ComponentScan(basePackages = {"com.holderzone.saas.store.weixin","com.holderzone.holder.saas.weixin"})
public class HolderSaasStoreWeixinApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreWeixinApplication.class, args);
        // 设置Spring容器上下文
        SpringContextUtil.getInstance().setCfgContext(app);
    }

}

