package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = ErpClientService.ResourceClientServiceFallBack.class)
public interface ErpClientService {

	/**
	 * @param orderSkuDTO
	 */
	@PostMapping("/inOutDocument/reduceStockForOrder")
	void reduceStockForOrder(@RequestBody OrderSkuDTO orderSkuDTO);

	@Component
	class ResourceClientServiceFallBack implements FallbackFactory<ErpClientService> {

		private static final Logger logger = LoggerFactory.getLogger(ResourceClientServiceFallBack.class);

		@Override
		public ErpClientService create(Throwable throwable) {

			return new ErpClientService() {
				@Override
				public void reduceStockForOrder(OrderSkuDTO orderSkuDTO) {
					logger.error("扣减库存失败e={}", throwable.getMessage());
					throw new ParameterException("扣减库存失败!");
				}
			};
		}
	}

}

