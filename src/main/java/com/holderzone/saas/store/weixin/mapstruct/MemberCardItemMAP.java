package com.holderzone.saas.store.weixin.mapstruct;

import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberCardCacheDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionCardItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberCardItemMAP {

	MemberCardItemMAP INSTANCE = Mappers.getMapper(MemberCardItemMAP.class);

//	List<MemberCardItemDTO> fromMemberCardOwnList(List<MemberCardListOwnedRespDTO> memberCardListOwnedRespDTOS);
//
//	MemberCardItemDTO fromMemberCardOwn(MemberCardListOwnedRespDTO memberCardListOwnedRespDTO);

	UserMemberCardCacheDTO toUserMemberCache(UserMemberSessionCardItemDTO userMemberSessionCardItemDTO);

	List<UserMemberCardCacheDTO> toUserMemberCacheList(List<UserMemberSessionCardItemDTO> userMemberSessionCardItemDTOS);

	UserMemberCardCacheDTO toResponseUserMemberCache(ResponseMemberCardListOwned userMemberSessionCardItemDTO);

	List<UserMemberCardCacheDTO> toResponseUserMemberCacheList(List<ResponseMemberCardListOwned> userMemberSessionCardItemDTOS);
}
