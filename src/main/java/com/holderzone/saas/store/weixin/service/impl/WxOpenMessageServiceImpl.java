package com.holderzone.saas.store.weixin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.queue.HolderQueueQueueRecordDTO;
import com.holderzone.saas.store.dto.queue.ItemGuidDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.WxBossTokenDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.menu.WxMenuUrlDTO;
import com.holderzone.saas.store.dto.weixin.open.WxMessageHandleReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOrderItemReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxSendMessageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.*;
import com.holderzone.saas.store.dto.weixin.resp.WxConfigRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxSubjectRespDTO;
import com.holderzone.saas.store.enums.weixin.WxMenuTypeEnum;
import com.holderzone.saas.store.weixin.config.*;
import com.holderzone.saas.store.weixin.constant.RedisConstants;
import com.holderzone.saas.store.weixin.entity.domain.WxMpTemplateDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.dto.WxMemberConsumeMsgDTO;
import com.holderzone.saas.store.weixin.entity.enums.WxTemplateTypeEnum;
import com.holderzone.saas.store.weixin.manager.WeixinSdkManager;
import com.holderzone.saas.store.weixin.mapper.WxStoreMerchantOrderMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreOpenMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import com.holderzone.saas.store.weixin.service.rpc.QueueClientService;
import com.holderzone.saas.store.weixin.service.rpc.TradeClientService;
import com.holderzone.saas.store.weixin.utils.*;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.error.WxMpErrorMsgEnum;
import me.chanjar.weixin.common.util.RandomUtils;
import me.chanjar.weixin.common.util.crypto.SHA1;
import me.chanjar.weixin.common.util.http.URIUtil;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.WxMpTemplateMsgService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutImageMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutTextMessage;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.template.*;
import me.chanjar.weixin.mp.builder.outxml.ImageBuilder;
import me.chanjar.weixin.mp.builder.outxml.TextBuilder;
import me.chanjar.weixin.open.api.WxOpenComponentService;
import me.chanjar.weixin.open.bean.message.WxOpenXmlMessage;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static me.chanjar.weixin.common.api.WxConsts.EventType.TEMPLATE_SEND_JOB_FINISH;
import static me.chanjar.weixin.common.api.WxConsts.EventType.UNSUBSCRIBE;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WxOpenMessageServiceImpl
 * @date 2019/03/26 18:14
 * @description 微信公众号消息处理service实现类
 * @program holder-saas-store
 */
@Service
@Slf4j
public class WxOpenMessageServiceImpl implements WxOpenMessageService {

    public static final String SUCCESS = "success";
    public static final String ERROR = "微信消息模板发送失败, error:";

    @Autowired
    WxThirdOpenConfig wxThirdOpenConfig;

    @Autowired
    WxQrCodeInfoService wxQrCodeInfoService;

    @Autowired
    WxSaasMpService wxSaasMpService;
    @Resource
    private WxOpenComponentService wxOpenComponentService;

    @Autowired
    WxStoreAuthorizerInfoService wxStoreAuthorizerInfoService;
    @Autowired
    DynamicHelper dynamicHelper;
    @Autowired
    WxConsumerParsUtil wxConsumerParsUtil;
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    RedissonClient redissonClient;
    @Autowired
    QueueClientService queueClientService;
    @Autowired
    OrganizationClientService organizationClientService;
    @Autowired
    WxStoreOpenMapstruct wxStoreOpenMapstruct;
    @Autowired
    WxMpTemplateService wxMpTemplateService;
    @Autowired
    WeChatConfig weChatConfig;
    @Autowired
    WxAccountConfig wxAccountConfig;
    @Autowired
    EnterpriseClientService enterpriseClientService;
    @Autowired
    WeixinSdkManager weixinSdkManager;

    @Autowired
    private WxStoreMerchantOrderMapper wxStoreMerchantOrderMapper;

    @Resource
    private TradeClientService tradeClientService;

    @Autowired
    private WxH5AccountConfig wxH5AccountConfig;

    @Autowired
    private WxOpenAccountConfig wxOpenAccountConfig;

    @Value("${h5.componentAuthorizer:#{null}}")
    Boolean componentAuthorizerFlag;

    private static final String STATE = "holder";

    @Override
    public String getWxMpXmlMessage(WxMessageHandleReqDTO wxMessageHandleReqDTO) throws WxErrorException {
        String body = wxMessageHandleReqDTO.getBody();
        WxCommonReqDTO wxCommonReqDTO = wxMessageHandleReqDTO.getWxCommonReqDTO();

        if (!isValidRequest(wxCommonReqDTO)) {
            throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
        }

        WxMpXmlMessage inMessage = decryptMessage(body, wxCommonReqDTO);
        log.info("接收到微信消息：{}", JacksonUtils.writeValueAsString(inMessage));

        if (isKeywordMessage(inMessage, wxMessageHandleReqDTO.getAppId())) {
            return handleKeywordMessage(inMessage);
        }

        return handleEventMessage(inMessage, wxMessageHandleReqDTO);
    }

    private boolean isValidRequest(WxCommonReqDTO wxCommonReqDTO) {
        return StringUtils.equalsIgnoreCase("aes", wxCommonReqDTO.getEncrypt_type()) &&
                wxThirdOpenConfig.getWxOpenComponentService()
                        .checkSignature(wxCommonReqDTO.getTimestamp(), wxCommonReqDTO.getNonce(), wxCommonReqDTO.getSignature());
    }

    private WxMpXmlMessage decryptMessage(String body, WxCommonReqDTO wxCommonReqDTO) {
        return WxOpenXmlMessage.fromEncryptedMpXml(body, wxThirdOpenConfig.getWxOpenConfigStorage(),
                wxCommonReqDTO.getTimestamp(), wxCommonReqDTO.getNonce(), wxCommonReqDTO.getMsg_signature());
    }

    private boolean isKeywordMessage(WxMpXmlMessage inMessage, String appId) {
        return Objects.nonNull(inMessage.getAllFieldsMap().get("Content")) && weChatConfig.getAppId().equals(appId);
    }

    private String handleKeywordMessage(WxMpXmlMessage inMessage) {
        String messageText = inMessage.getAllFieldsMap().get("Content").toString();
        Map<String, String> keyWordsMap = weChatConfig.getKeyWordsMap();

        if (Objects.nonNull(keyWordsMap.get(messageText))) {
            return buildImageResponse(inMessage, keyWordsMap.get(messageText));
        }
        return null;
    }

    private String buildImageResponse(WxMpXmlMessage inMessage, String mediaId) {
        ImageBuilder imageBuilder = new ImageBuilder();
        WxMpXmlOutImageMessage imageMessage = imageBuilder.build();
        imageMessage.setCreateTime(System.currentTimeMillis());
        imageMessage.setFromUserName(inMessage.getToUser());
        imageMessage.setToUserName(inMessage.getFromUser());
        imageMessage.setMediaId(mediaId);
        log.info("关键词回复的微信消息为：\n{}", imageBuilder);
        return WxOpenXmlMessage.wxMpOutXmlMessageToEncryptedXml(imageMessage, wxThirdOpenConfig.getWxOpenConfigStorage());
    }

    private String handleEventMessage(WxMpXmlMessage inMessage, WxMessageHandleReqDTO wxMessageHandleReqDTO) throws WxErrorException {
        String event = inMessage.getEvent();
        if (TEMPLATE_SEND_JOB_FINISH.equals(event)) {
            return SUCCESS;
        } else if (UNSUBSCRIBE.equals(event)) {
            log.info("用户取消订阅，appId:{},fromUser:{}", wxMessageHandleReqDTO.getAppId(), inMessage.getFromUser());
            removeBossAuthToken(inMessage.getFromUser());
            return SUCCESS;
        } else {
            WxMpXmlOutMessage wxMpXmlOutMessage = createQrCodeMessage(wxMessageHandleReqDTO.getAppId(), inMessage);
            return ObjectUtils.isEmpty(wxMpXmlOutMessage) ? SUCCESS : WxOpenXmlMessage.wxMpOutXmlMessageToEncryptedXml(wxMpXmlOutMessage, wxThirdOpenConfig.getWxOpenConfigStorage());
        }
    }

    @Override
    public String getUserInfo(WxAuthorizeReqDTO wxAuthorizeReqDTO) throws WxErrorException {
        if (StringUtils.isNotEmpty(wxAuthorizeReqDTO.getMemberInfoGuid())) {
            return getOpenUserInfo(wxAuthorizeReqDTO);
        }
        String eventKey = wxConsumerParsUtil.changeDateSourceByEventKey(wxAuthorizeReqDTO.getEventKey());
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getByAppId(wxAuthorizeReqDTO.getAppId());
        if (ObjectUtils.isEmpty(wxStoreAuthorizerInfoDO)) {
            log.error("当前公众号暂未进行授权");
            try {
                String returnUrl = weChatConfig.getOrderPageUrl().substring(0, weChatConfig.getOrderPageUrl().lastIndexOf("?") + 1).concat("errorMsg=").concat(URLEncoder.encode("当前公众号暂未进行授权", "UTF-8"));
                log.info("returnUrl: {}", returnUrl);
                return returnUrl;
            } catch (UnsupportedEncodingException e) {
                throw new BusinessException("系统异常");
            }
        }
        WxMpService wxMpService = wxSaasMpService.getWxMpService(wxStoreAuthorizerInfoDO);
//        WxMpOAuth2AccessToken wxMpOAuth2AccessToken = wxMpService.oauth2getAccessToken(wxAuthorizeReqDTO.getCode());
        WxMpOAuth2AccessToken wxMpOAuth2AccessToken = wxThirdOpenConfig.getWxOpenComponentService().oauth2getAccessToken(wxStoreAuthorizerInfoDO.getAuthorizerAppid(), wxAuthorizeReqDTO.getCode());
        WxMpUser wxMpUser = wxMpService.getOAuth2Service().getUserInfo(wxMpOAuth2AccessToken, null);
//        WxMpUser wxMpUser = wxMpService.oauth2getUserInfo(wxMpOAuth2AccessToken, null);
        return wxConsumerParsUtil.parse2Consumer(eventKey, wxMpUser, wxStoreAuthorizerInfoDO);
    }

    @Override
    public String getOpenUserInfo(WxAuthorizeReqDTO wxAuthorizeReqDTO) throws WxErrorException {
        String eventKey = wxConsumerParsUtil.changeDateSourceByEventKey(wxAuthorizeReqDTO.getEventKey());
        WxMpService wxMpService = weixinSdkManager.buildWxMpService(3);
        WxMpOAuth2AccessToken accessToken = wxMpService.getOAuth2Service().getAccessToken(wxAuthorizeReqDTO.getCode());
        log.info("h5accessToken:{}", JacksonUtils.writeValueAsString(accessToken));
        WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setOpenId(accessToken.getOpenId());
        wxMpUser.setNickname("微信用户");
        return wxConsumerParsUtil.parse2Consumer(eventKey, wxMpUser, wxAuthorizeReqDTO);
    }

    /**
     * 根据二维码参数生成返回消息
     *
     * @param inMessage
     * @param appId
     * @return
     */
    private WxMpXmlOutMessage createQrCodeMessage(String appId, WxMpXmlMessage inMessage) throws WxErrorException {
        String eventKey = inMessage.getEventKey();
        TextBuilder textBuilder = new TextBuilder();
        if (StringUtils.isNotEmpty(eventKey)) {
            String[] arrayStr = eventKey.split(",");
            if (arrayStr.length == 3) {
                String content;
                String eventKeyType = arrayStr[0].replace("qrscene_", "");
                String enterpriseGuid = arrayStr[1];
                // 手动切库
                dynamicHelper.changeDatasource(enterpriseGuid);
                WeChatCardMessageConfig cardMessageConfig = weChatConfig.getCardMessageConfig(enterpriseGuid);
                log.info("企业Guid为:{},获取yml文件中的配置:{}", enterpriseGuid, JacksonUtils.writeValueAsString(cardMessageConfig));
                //如果为null 不发送卡片消息
                Boolean cardMessage = true;
                if (Objects.isNull(cardMessageConfig)) {
                    cardMessage = false;
                }
                // 获取当前公众号信息，
                WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getByAppId(appId);
                log.info("createQrCodeMessage 微信授权基础信息{}", JacksonUtils.writeValueAsString(wxStoreAuthorizerInfoDO));
                // 初始化wxMpService，刷新公众号accessToken等信息
                WxMpService wxMpService = wxSaasMpService.getWxMpService(wxStoreAuthorizerInfoDO);
                if (BusinessName.ORDER_CONFIG.equals(eventKeyType)
                        || BusinessName.ORDER_CONFIG_2.equals(eventKeyType)) {// 点餐二维码 返回消息
                    eventKey = eventKey.replace("qrscene_", "");
                    String redirectUrl = String.format(weChatConfig.getBaseJumpUrl(), appId, eventKey);
                    redirectUrl = redirectUrl + "&currentTime=" + System.currentTimeMillis();
                    String jumpUrl = wxThirdOpenConfig.getWxOpenComponentService().oauth2buildAuthorizationUrl(appId, redirectUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, "holder");
                    content = String.format(weChatConfig.getSendBackMessage(), jumpUrl);//欢迎光临，请<a href='%s'>点击此处</a>进行点餐
                    if (cardMessage && cardMessageConfig.getEnterpriseGuid() != null && cardMessageConfig.getEnterpriseGuid().contains(enterpriseGuid)) {
                        WxQrCodeInfoDO cacheWxQrCodeInfoByGuid = wxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(arrayStr[2]);
                        boolean sendCard = "all".equals(cardMessageConfig.getStoreGuid()) || cardMessageConfig.getStoreGuid().contains(cacheWxQrCodeInfoByGuid.getStoreGuid());
                        if (sendCard) {
                            try {
                                String parm = "brandGuid=%s&storeGuid=%s&areaGuid=%s&diningTableGuid=%s&tag=1&time=%s";
                                String format = String.format(parm, cacheWxQrCodeInfoByGuid.getBrandGuid()
                                        , cacheWxQrCodeInfoByGuid.getStoreGuid(), cacheWxQrCodeInfoByGuid.getAreaGuid()
                                        , cacheWxQrCodeInfoByGuid.getTableGuid(), System.currentTimeMillis());
                                //format="brandGuid=6560521287326760961&storeGuid=6670565239483793408&areaGuid=6670565240905662464&diningTableGuid=6670565241056657408&tag=1";
                                //format = URLEncoder.encode(format,"utf-8");
                                format = "?" + format;
                                //何师烧烤（storeName）欢迎您！当前桌号【tableCode】。
                                String titleNew = cardMessageConfig.getTitle().replace("storeName", cacheWxQrCodeInfoByGuid.getStoreName())
                                        .replace("tableCode", cacheWxQrCodeInfoByGuid.getTableName());
                                boolean success = sendCard(inMessage.getFromUser(), wxStoreAuthorizerInfoDO.getAuthorizerAccessToken(), format, titleNew, cardMessageConfig);
                                if (success) {
                                    return null;
                                }
                            } catch (Exception e) {
                                log.error("发送卡片消息失败", e);
                            }
                        }
                    }
                    log.info("点餐消息内容,content:{}", content);
                } else if (BusinessName.QUEUE_INFO.equals(eventKeyType)) {// 排队二维码推送的消息
                    WxQueueInfoReqDTO wxQueueInfoReqDTO = (WxQueueInfoReqDTO) redisUtils.get(arrayStr[2]);
                    ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
                    itemGuidDTO.setItemGuid(wxQueueInfoReqDTO.getQueueGuid());
                    itemGuidDTO.setStoreGuid(wxQueueInfoReqDTO.getStoreGuid());
                    itemGuidDTO.setEnterpriseGuid(arrayStr[1]);
                    String detailUrl = String.format(weChatConfig.getQUEUE_DETAIL_PAGE(), arrayStr[2], arrayStr[1]);
                    String msg = sendTemplateMsg(wxMpService, inMessage, itemGuidDTO, detailUrl);
                    if (ObjectUtils.isEmpty(msg)) {
                        return null;
                    }
                    content = msg;
                } else {
                    content = "不能识别到您输入的内容，您可点击下方按钮查看公众号功能";
                }
                textBuilder.content(content);
                WxMpXmlOutTextMessage wxMpXmlOutTextMessage = textBuilder.build();
                wxMpXmlOutTextMessage.setCreateTime(System.currentTimeMillis());
                wxMpXmlOutTextMessage.setFromUserName(inMessage.getToUser());
                wxMpXmlOutTextMessage.setToUserName(inMessage.getFromUser());
                log.info("回复的微信消息为：\n{}", wxMpXmlOutTextMessage);
                return wxMpXmlOutTextMessage;
            }
        }
        return null;
    }

    private String sendTemplateMsg(WxMpService wxMpService, WxMpXmlMessage inMessage, ItemGuidDTO itemGuidDTO, String queueDetailUrl) throws WxErrorException {
        WxStoreAuthorizerInfoDO byAppId = wxStoreAuthorizerInfoService.getByAppId(wxMpService.getWxMpConfigStorage().getAppId());
        HolderQueueQueueRecordDTO obtain = queueClientService.obtain(itemGuidDTO);
        if (ObjectUtils.isEmpty(obtain)) {
            return "亲，无此排号单，排号单仅12小时内有效哦";
        }
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(obtain.getQueueRecord().getStoreGuid());
        obtain.getQueueRecord().setStoreName(storeDTO.getName());
        WxMpTemplateMsgService templateMsgService = wxMpService.getTemplateMsgService();
        WxMpTemplateMessage wxMpTemplateMessage = createQueueTempMsg(obtain);
        wxMpTemplateMessage.setToUser(inMessage.getFromUser());
        WxMpTemplateDO one = wxMpTemplateService.getOne(new LambdaQueryWrapper<WxMpTemplateDO>()
                .eq(WxMpTemplateDO::getAppId, byAppId.getAuthorizerAppid())
                .eq(WxMpTemplateDO::getBrandGuid, byAppId.getBrandGuid())
                .eq(WxMpTemplateDO::getType, WxTemplateTypeEnum.QUEUE_TEMPLATE.getCode()));
        if (one == null || StringUtils.isEmpty(one.getTemplateId())) {
            return "商户暂未开启扫码排队功能";
        }
        wxMpTemplateMessage.setTemplateId(one.getTemplateId());
        wxMpTemplateMessage.setUrl(queueDetailUrl);
        templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
        return null;
    }

    private WxMpTemplateMessage createQueueTempMsg(HolderQueueQueueRecordDTO obtain) {
        WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
        WxMpTemplateData keyWord1 = new WxMpTemplateData();
        keyWord1.setName("keyword1");
        keyWord1.setValue(obtain.getQueueRecord().getStoreName());
        WxMpTemplateData keyWord2 = new WxMpTemplateData();
        keyWord2.setName("keyword2");
        keyWord2.setValue(obtain.getQueueRecord().getOrder());
        WxMpTemplateData keyWord3 = new WxMpTemplateData();
        keyWord3.setName("keyword3");
        keyWord3.setValue(obtain.getQueueRecord().getBefore() + "桌");
        WxMpTemplateData keyWord4 = new WxMpTemplateData();
        keyWord4.setName("keyword4");
        keyWord4.setValue("--");
        WxMpTemplateData keyWord5 = new WxMpTemplateData();
        keyWord5.setName("keyword5");
        String status;
        String remarkStr = "";
        String remarkColor = "";
        switch (obtain.getQueueRecord().getStatus()) {
            case 0:
                status = "排队中";
                if (obtain.getQueueRecord().getBefore() <= 2) {
                    remarkStr = "\n马上就排到您啦，请至餐厅门口等候！";
                    remarkColor = "#0000FF";
                }
                break;
            case 1:
                status = "已过号";
                break;
            case 2:
                status = "叫号中";
                remarkStr = "\n终于排到啦！餐厅正在呼叫您的排号，请至餐厅前台确认排号并用餐！";
                remarkColor = "#DC143C";
                break;
            case 3:
                status = "已就餐";
                break;
            default:
                status = "未知状态";
                break;
        }
        keyWord5.setValue(status);
        WxMpTemplateData remark = new WxMpTemplateData();
        remark.setName("remark");
        remark.setValue(remarkStr);
        remark.setColor(remarkColor);
        List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(keyWord1, keyWord2, keyWord3, keyWord4, keyWord5, remark);
        wxMpTemplateMessage.setData(wxMpTemplateDataList);
        return wxMpTemplateMessage;
    }

    @Override
    public void createQueueMsgTemp(TempMsgCreateDTO tempMsgCreateDTO) throws WxErrorException {
        String appId = tempMsgCreateDTO.getAppId();
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getByAppId(appId);
        if (ObjectUtils.isEmpty(wxStoreAuthorizerInfoDO)) {
            throw new BusinessException("该公众号暂未向掌控者平台授权");
        }
        WxMpService wxMpService = wxSaasMpService.getWxMpService(wxStoreAuthorizerInfoDO);
        WxMpTemplateMsgService wxMpTemplateMsgService = wxMpService.getTemplateMsgService();
        try { // 查询当前公众号是否配置了行业类别
            WxMpTemplateIndustry wxMpTemplateIndustry = wxMpTemplateMsgService.getIndustry();
            log.info("WxMpTemplateIndustry:{}", wxMpTemplateIndustry);
        } catch (WxErrorException e) { // 未配置行业类别的公众号 调用查询接口会跑WxErrorException,捕获后配置行业类别
            WxMpTemplateIndustry wxMpTemplateIndustry = new WxMpTemplateIndustry();
            WxMpTemplateIndustry.Industry industry1 = new WxMpTemplateIndustry.Industry();
//            industry1.setId("1");
//            industry1.setFirstClass("IT科技");
//            industry1.setSecondClass("互联网/电子商务");
//            WxMpTemplateIndustry.Industry industry2 = new WxMpTemplateIndustry.Industry();
//            industry2.setId("10");
//            industry2.setFirstClass("餐饮");
//            industry2.setSecondClass("餐饮");
            wxMpTemplateIndustry.setPrimaryIndustry(WxMpTemplateIndustryEnum.E_COMMERCE);
            wxMpTemplateIndustry.setSecondIndustry(WxMpTemplateIndustryEnum.REPAST);
            boolean flag = wxMpTemplateMsgService.setIndustry(wxMpTemplateIndustry);
            log.info("设置行业结果：{}", flag);
        }
        List<WxMpTemplate> wxMpTemplates = wxMpTemplateMsgService.getAllPrivateTemplate();
        log.info(JacksonUtils.writeValueAsString(wxMpTemplates));
        if (ObjectUtils.isEmpty(wxMpTemplates) && wxMpTemplates.size() == 25) {
            throw new BusinessException("该公众号消息模板已达到上限，请到公众号后台删除不需要的消息模板后重试");
        }
        // 统一将模板添加至公众号，通过短Id添加，并将对应id入库
        String result = wxMpTemplateMsgService.addTemplate("OPENTM207508364");
        wxStoreAuthorizerInfoDO.setTemplateMsgId(result);
        wxStoreAuthorizerInfoService.updateById(wxStoreAuthorizerInfoDO);
    }

    @Async
    @Override
    public void sendQueueMsg(WxStoreConsumerDTO wxStoreConsumerDTO, HolderQueueQueueRecordDTO obtain, WxPortalReqDTO wxPortalReqDTO) throws WxErrorException {
        String enterpriseGuid = wxStoreConsumerDTO.getEnterpriseGuid();
        if (StringUtils.isEmpty(enterpriseGuid)) {
            enterpriseGuid = wxPortalReqDTO.getEnterpriseGuid();
        }
        dynamicHelper.changeDatasource(enterpriseGuid);
        if (ObjectUtils.isEmpty(obtain)) {
            log.info("无排队信息");
            return;
        }
        WxStoreAuthorizerInfoDO authorizerInfoDO = wxStoreAuthorizerInfoService.getById(wxStoreConsumerDTO.getBrandGuid());
        if (ObjectUtils.isEmpty(authorizerInfoDO) || !authorizerInfoDO.isAuthService()) {
            return;
        }
        WxMpService wxMpService = wxSaasMpService.getWxMpService(authorizerInfoDO);
        WxMpTemplateMsgService templateMsgService = wxMpService.getTemplateMsgService();
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(obtain.getQueueRecord().getStoreGuid());
        obtain.getQueueRecord().setStoreName(storeDTO.getName());
        WxMpTemplateMessage wxMpTemplateMessage = createQueueTempMsg(obtain);
        wxMpTemplateMessage.setToUser(wxStoreConsumerDTO.getOpenId());
        WxMpTemplateDO one = wxMpTemplateService.getOne(new LambdaQueryWrapper<WxMpTemplateDO>()
                .eq(WxMpTemplateDO::getAppId, authorizerInfoDO.getAuthorizerAppid())
                .eq(WxMpTemplateDO::getType, 0));
        String templateMsgId = authorizerInfoDO.getTemplateMsgId();
        wxMpTemplateMessage.setTemplateId(
                Optional.ofNullable(templateMsgId)
                        .orElse(
                                Optional.ofNullable(
                                                Optional.ofNullable(one)
                                                        .orElse(new WxMpTemplateDO())
                                                        .getTemplateId())
                                        .orElse(null)));
        if (StringUtils.isEmpty(wxMpTemplateMessage.getTemplateId())) {
            log.info("enterpriseGuid:{},brandGuid:{},appId:{}, 未配置消息模板", enterpriseGuid, wxStoreConsumerDTO.getBrandGuid(), authorizerInfoDO.getAuthorizerAppid());
            return;
        }
        String queueDetailUrl = String.format(weChatConfig.getQUEUE_DETAIL_PAGE(), wxPortalReqDTO.getMsgKey(), enterpriseGuid);
        wxMpTemplateMessage.setUrl(queueDetailUrl);
        templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
    }

    @Override
    public String shopList(WxPortalReqDTO wxPortalReqDTO) {
        dynamicHelper.changeDatasource(wxPortalReqDTO.getEnterpriseGuid());
        WxStoreAuthorizerInfoDO authorizerInfoDO = wxStoreAuthorizerInfoService.getById(wxPortalReqDTO.getBrandGuid());
        if (ObjectUtils.isEmpty(authorizerInfoDO)) {
            return null;
        }
        String eventKey = BusinessName.STORE_LIST + "," + wxPortalReqDTO.getEnterpriseGuid() + "," + wxPortalReqDTO.getBrandGuid();
        String redirectUrl = String.format(weChatConfig.getBaseJumpUrl(), authorizerInfoDO.getAuthorizerAppid(), eventKey);
        return wxThirdOpenConfig.getWxOpenComponentService().oauth2buildAuthorizationUrl(authorizerInfoDO.getAuthorizerAppid(), redirectUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, "holder");
    }

    @Override
    public String getBossAuthorizeUrl(WxMenuUrlDTO wxMenuUrlDTO) {
        String redirectUrl = String.format(weChatConfig.getBossAuthorizeUrl(), wxMenuUrlDTO.getMenuType());
        String activeProfiles = SpringContextUtil.getInstance().getActiveProfiles();
        log.info("当前环境:{}", activeProfiles);
        if (SpringContextUtil.getInstance().isProdActiveProfiles()) {
            WxMpService wxService = weixinSdkManager.buildWxMpService(4);
            return wxService.getOAuth2Service().buildAuthorizationUrl(redirectUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, "holder");
        } else {
            return wxThirdOpenConfig.getWxOpenComponentService().oauth2buildAuthorizationUrl(wxAccountConfig.getAppId(),
                    redirectUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, "holder");
        }
    }

    @Override
    public String getBossRedirectUrl(WxAuthorizeReqDTO wxAuthorizeReqDTO) {
        String openId = "";
        try {
            WxMpService wxService = weixinSdkManager.buildWxMpService(4);
            WxMpOAuth2AccessToken accessToken = wxService.getOAuth2Service().getAccessToken(wxAuthorizeReqDTO.getCode());
            WxMpUser wxMpUser = wxService.getOAuth2Service().getUserInfo(accessToken, null);
            openId = wxMpUser.getOpenId();
        } catch (Exception e) {
            log.error("获取用户openId失败,e:{}", e.getMessage());
            throw new BusinessException("获取用户数据失败");
        }
        if (StringUtils.isEmpty(openId)) {
            throw new BusinessException("openId为空");
        }
        log.info("获取openId成功,openId:{}", openId);
        // 获取token
        WxBossTokenDTO bossTokenDTO = getBossToken(wxAuthorizeReqDTO.getMenuType(), openId);
        String bossToken = "";
        String enterpriseGuid = "";
        if (Objects.nonNull(bossTokenDTO)) {
            enterpriseGuid = bossTokenDTO.getEnterpriseGuid();
            try {
                bossToken = URLEncoder.encode(bossTokenDTO.getToken(), "utf-8");
            } catch (Exception e) {
                log.error("url token error, bossToken:{}", bossTokenDTO.getToken());
            }
        }
        String redirectUrl = weChatConfig.getBiBossRedirectUrl();
        // 这里返回给前端一个msgKey, msgKey为缓存key，存储openId
        String msgKey = createBossMsgKey(openId);
        return String.format(redirectUrl, bossToken, enterpriseGuid, wxAuthorizeReqDTO.getMenuType(), msgKey);
    }

    private String createBossMsgKey(String openId) {
        String msgKey = String.valueOf(SnowFlakeUtil.getInstance().nextId());
        redissonClient.getBucket(String.format(RedisConstants.BOSS_MSG_KEY, msgKey)).set(openId, 1, TimeUnit.DAYS);
        return msgKey;
    }

    @Override
    public void saveBossAuthToken(WxMenuUrlDTO wxMenuUrlDTO) {
        String msgKey = wxMenuUrlDTO.getMsgKey();
        if (StringUtils.isEmpty(msgKey)) {
            log.error("msgKey 为空, 请求参数:{}", JacksonUtils.writeValueAsString(wxMenuUrlDTO));
            return;
        }
        Object redisObj = redissonClient.getBucket(String.format(RedisConstants.BOSS_MSG_KEY, msgKey)).get();
        if (Objects.isNull(redisObj)) {
            log.error("msgKey查询缓存为空, 请求参数:{}", JacksonUtils.writeValueAsString(wxMenuUrlDTO));
            return;
        }
        String openId = redisObj.toString();
        String redisKey = String.format(RedisConstants.BOSS_TOKEN_KEY, wxMenuUrlDTO.getMenuType(), openId);
        WxBossTokenDTO wxBossTokenDTO = buildWxBossTokenDTO(wxMenuUrlDTO.getEnterpriseGuid(), wxMenuUrlDTO.getToken());
        redissonClient.getBucket(redisKey).set(JacksonUtils.writeValueAsString(wxBossTokenDTO));
    }

    @Override
    public void cleanBossAuthToken(WxMenuUrlDTO wxMenuUrlDTO) {
        String msgKey = wxMenuUrlDTO.getMsgKey();
        if (StringUtils.isEmpty(msgKey)) {
            log.error("msgKey 为空, 请求参数:{}", JacksonUtils.writeValueAsString(wxMenuUrlDTO));
            return;
        }
        Object openIdObj = redissonClient.getBucket(String.format(RedisConstants.BOSS_MSG_KEY, msgKey)).get();
        if (Objects.isNull(openIdObj)) {
            log.error("msgKey查询缓存为空, 请求参数:{}", JacksonUtils.writeValueAsString(wxMenuUrlDTO));
            return;
        }
        String openId = openIdObj.toString();
        String redisKey = String.format(RedisConstants.BOSS_TOKEN_KEY, wxMenuUrlDTO.getMenuType(), openId);
        redissonClient.getBucket(redisKey).delete();
    }

    @Override
    public void removeBossAuthToken(String openId) {
        log.info("删除老板助手登录缓存,openId:{}", openId);
        String bossRedisKey = String.format(RedisConstants.BOSS_TOKEN_KEY, WxMenuTypeEnum.BOSS.getSource(), openId);
        redissonClient.getBucket(bossRedisKey).delete();
        String biBossRedisKey = String.format(RedisConstants.BOSS_TOKEN_KEY, WxMenuTypeEnum.BI_BOSS.getSource(), openId);
        redissonClient.getBucket(biBossRedisKey).delete();
    }

    /**
     * 根据openId 获取token
     */
    private WxBossTokenDTO getBossToken(String menuType, String openId) {
        String redisKey = String.format(RedisConstants.BOSS_TOKEN_KEY, menuType, openId);
        Object tokenObj = redissonClient.getBucket(redisKey).get();
        log.info("获取老板助手token,redisKey:{},tokenObj:{}", redisKey, tokenObj);
        if (Objects.isNull(tokenObj)) {
            return null;
        }
        return JacksonUtils.toObject(WxBossTokenDTO.class, tokenObj.toString());
    }

    /**
     * 构建老板助手 缓存DTO
     */
    private WxBossTokenDTO buildWxBossTokenDTO(String enterpriseGuid, String token) {
        WxBossTokenDTO wxBossTokenDTO = new WxBossTokenDTO();
        wxBossTokenDTO.setEnterpriseGuid(enterpriseGuid);
        wxBossTokenDTO.setToken(token);
        return wxBossTokenDTO;
    }

    @Override
    public WxConfigRespDTO generateDTO(WxPortalReqDTO wxPortalReqDTO) throws WxErrorException {
        dynamicHelper.changeDatasource(wxPortalReqDTO.getEnterpriseGuid());
        String appId = wxPortalReqDTO.getAppId();
        WxStoreAuthorizerInfoDO authorizerInfoDO = wxStoreAuthorizerInfoService.getByAppId(appId);
        BrandDTO brandDTO;
        String jsApiTicket;
        if (Objects.isNull(authorizerInfoDO)) {
            // 没有授权的
            brandDTO = organizationClientService.queryDefaultBrand();
            WxMpService wxMpService = weixinSdkManager.buildWxMpService(3);
            jsApiTicket = wxMpService.getJsapiTicket();
        } else {
            // 已授权
            brandDTO = organizationClientService.queryBrandByGuid(authorizerInfoDO.getBrandGuid());
            jsApiTicket = wxThirdOpenConfig.getWxOpenConfigStorage().getJsapiTicket(appId);
            if (StringUtils.isEmpty(jsApiTicket)) {
                jsApiTicket = wxSaasMpService.getWxMpService(authorizerInfoDO).getJsapiTicket();
            }
        }
        long timestamp = System.currentTimeMillis() / 1000;
        String randomStr = RandomUtils.getRandomStr();
        String signature = SHA1.genWithAmple("jsapi_ticket=" + jsApiTicket,
                "noncestr=" + randomStr, "timestamp=" + timestamp, "url=" + wxPortalReqDTO.getUrl());
        WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature(signature);
        wxCommonReqDTO.setNonce(randomStr);
        wxCommonReqDTO.setTimestamp(Long.toString(timestamp));
        return new WxConfigRespDTO(wxCommonReqDTO, brandDTO.getName(), appId, wxPortalReqDTO.getEnterpriseGuid());
    }

    @Override
    public String memberLogin(WxPortalReqDTO wxPortalReqDTO) {
        dynamicHelper.changeDatasource(wxPortalReqDTO.getEnterpriseGuid());
        String appId = wxPortalReqDTO.getAppId();
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getById(wxPortalReqDTO.getBrandGuid());

        if (ObjectUtils.isEmpty(wxStoreAuthorizerInfoDO) || !Objects.equals(appId, wxStoreAuthorizerInfoDO.getAuthorizerAppid())) {
            throw new BusinessException("当前品牌【" + wxPortalReqDTO.getBrandGuid() + "】暂未授权绑定该公众号【" + appId + "】");
        }
        String eventKey;
        if (Objects.equals(0, wxPortalReqDTO.getReqType())) {
            eventKey = BusinessName.MEMBER_LOGIN + "," + wxPortalReqDTO.getEnterpriseGuid() + "," + wxPortalReqDTO.getBrandGuid();
        } else if (Objects.equals(1, wxPortalReqDTO.getReqType())) {
            eventKey = BusinessName.MEMBER_BINDING + "," + wxPortalReqDTO.getEnterpriseGuid() + "," + wxPortalReqDTO.getBrandGuid();
        } else {
            throw new BusinessException("无法识别当前请求类型");
        }
        String redirectUrl = String.format(weChatConfig.getBaseJumpUrl(), wxStoreAuthorizerInfoDO.getAuthorizerAppid(), eventKey);
        return wxThirdOpenConfig.getWxOpenComponentService().oauth2buildAuthorizationUrl(wxStoreAuthorizerInfoDO.getAuthorizerAppid(), redirectUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, "holder");
    }

    @Override
    public String newMemberLogin(WxPortalReqDTO wxPortalReqDTO) {
        dynamicHelper.changeDatasource(wxPortalReqDTO.getEnterpriseGuid());
        String appId = wxPortalReqDTO.getAppId();
        WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = wxStoreAuthorizerInfoService.getById(wxPortalReqDTO.getBrandGuid());

        if (ObjectUtils.isEmpty(wxStoreAuthorizerInfoDO) || !Objects.equals(appId, wxStoreAuthorizerInfoDO.getAuthorizerAppid())) {
            throw new BusinessException("当前品牌【" + wxPortalReqDTO.getBrandGuid() + "】暂未授权绑定该公众号【" + appId + "】");
        }
        log.info("wxStoreAuthorizerInfoDO的值是：{}，appId的值是：{}，wxStoreAuthorizerInfoDO.getAuthorizerAppid()的值是：{}", wxStoreAuthorizerInfoDO, appId, wxStoreAuthorizerInfoDO.getAuthorizerAppid());
        String eventKey = BusinessName.NEW_MEMBER_LOGIN + "," + wxPortalReqDTO.getEnterpriseGuid() + "," + wxPortalReqDTO.getBrandGuid();
        String redirectUrl = String.format(weChatConfig.getBaseJumpUrl(), wxStoreAuthorizerInfoDO.getAuthorizerAppid(), eventKey);
        return wxThirdOpenConfig.getWxOpenComponentService().oauth2buildAuthorizationUrl(wxStoreAuthorizerInfoDO.getAuthorizerAppid(),
                redirectUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, "holder");
    }

    @Override
    public String openMemberLogin(WxPortalReqDTO wxPortalReqDTO) {
        dynamicHelper.changeDatasource(wxPortalReqDTO.getEnterpriseGuid());
        String memberInfoGuid = wxPortalReqDTO.getMemberInfoGuid();
        String operSubjectGuid = wxPortalReqDTO.getOperSubjectGuid();
        String thirdOpenId = wxPortalReqDTO.getThirdOpenId();
        String thirdAppId = wxPortalReqDTO.getThirdAppId();
        // 查询默认品牌
        String brandGuid = organizationClientService.queryDefaultBrand().getGuid();
        if (StringUtils.isEmpty(brandGuid)) {
            throw new BusinessException("品牌为空");
        }
        String eventKey = BusinessName.NEW_MEMBER_LOGIN + "," + wxPortalReqDTO.getEnterpriseGuid() + "," + brandGuid;
        String jumpUrl = String.format(weChatConfig.getBaseJumpUrl(), wxH5AccountConfig.getAppId(), eventKey);
        if (StringUtils.isNotEmpty(memberInfoGuid)) {
            jumpUrl = jumpUrl + "&memberInfoGuid=" + memberInfoGuid;
        }
        if (StringUtils.isNotEmpty(operSubjectGuid)) {
            jumpUrl = jumpUrl + "&operSubjectGuid=" + operSubjectGuid;
        }
        if (StringUtils.isNotEmpty(thirdOpenId)) {
            jumpUrl = jumpUrl + "&thirdOpenId=" + thirdOpenId;
        }
        if (StringUtils.isNotEmpty(thirdAppId)) {
            jumpUrl = jumpUrl + "&thirdAppId=" + thirdAppId;
        }
        String redirectUrl = "";
        if (Boolean.TRUE.equals(componentAuthorizerFlag)) {
            redirectUrl = String.format("https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&connect_redirect=1" +
                            "&scope=snsapi_base&state=%s&component_appid=%s#wechat_redirect",
                    wxH5AccountConfig.getAppId(), URIUtil.encodeURIComponent(jumpUrl), StringUtils.trimToEmpty(STATE), wxOpenAccountConfig.getComponentAppId());
        } else {
            redirectUrl = String.format("https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&connect_redirect=1" +
                            "&scope=snsapi_base&state=%s#wechat_redirect",
                    wxH5AccountConfig.getAppId(), URIUtil.encodeURIComponent(jumpUrl), StringUtils.trimToEmpty(STATE));
        }
        return redirectUrl;
    }

    @Override
    public void sendMemberMsg(WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO) {
        log.info("会员结账通知模板:{}", wxMemberConsumeMsgDTO);
        WxStoreAuthorizerInfoDO authorizerInfoDO = wxStoreAuthorizerInfoService.getById(wxMemberConsumeMsgDTO.getBrandGuid());
        if (ObjectUtils.isEmpty(authorizerInfoDO) || !authorizerInfoDO.isAuthService()) {
            return;
        }
        WxMpTemplateDO one = wxMpTemplateService.getOne(new LambdaQueryWrapper<WxMpTemplateDO>()
                .eq(WxMpTemplateDO::getAppId, authorizerInfoDO.getAuthorizerAppid())
                .eq(WxMpTemplateDO::getBrandGuid, wxMemberConsumeMsgDTO.getBrandGuid())
                .eq(WxMpTemplateDO::getType, WxTemplateTypeEnum.MEMBER_AMOUNT_TEMPLATE.getCode()));
        if (one == null || StringUtils.isEmpty(one.getTemplateId())) {
            return;
        }
        String[] args = wxMemberConsumeMsgDTO.getArgs();
        if (args == null || args.length != 5) {
            return;
        }
        List<WxMpTemplateData> dataList = Lists.newArrayList();
        for (int i = 0; i < args.length; i++) {
            WxMpTemplateData wxMpTemplateData = new WxMpTemplateData();
            wxMpTemplateData.setName("keyword" + (i + 1));
            wxMpTemplateData.setValue(args[i]);
            dataList.add(wxMpTemplateData);
        }
        WxMpTemplateData first = new WxMpTemplateData();
        first.setName("first");
        first.setValue("会员，您好！您已成功消费" + args[2] + "元！");

        WxMpTemplateData remark = new WxMpTemplateData();
        remark.setName("remark");
        remark.setValue("点击查询订单详情，感谢您的使用。");
        dataList.add(first);
        dataList.add(remark);

        WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
        wxMpTemplateMessage.setTemplateId(one.getTemplateId());
        String url = String.format(weChatConfig.getMEMBER_ORDER_PAGE(), UserContextUtils.getEnterpriseGuid(),
                wxMemberConsumeMsgDTO.getStoreGuid(), wxMemberConsumeMsgDTO.getOpenId(),
                wxMemberConsumeMsgDTO.getOrderGuid());
        wxMpTemplateMessage.setUrl(url);
        wxMpTemplateMessage.setToUser(wxMemberConsumeMsgDTO.getOpenId());
        wxMpTemplateMessage.setData(dataList);
        try {
            wxSaasMpService.getWxMpService(authorizerInfoDO).getTemplateMsgService().sendTemplateMsg(wxMpTemplateMessage);
            List<WxMpTemplate> allPrivateTemplate = wxSaasMpService.getWxMpService(authorizerInfoDO).getTemplateMsgService().getAllPrivateTemplate();
        } catch (WxErrorException e) {
            log.error(ERROR, e);
        }
    }

    @Override
    public Result<String> sendMemberMsgNew(WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO) {
        List<String> dataList = wxMemberConsumeMsgDTO.getKeywordValues();
        WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
        for (int i = 0; i < dataList.size(); i++) {
            WxMpTemplateData wxMpTemplateData = new WxMpTemplateData();
            wxMpTemplateData.setName("keyword" + (i + 1));
            wxMpTemplateData.setValue(dataList.get(i));
            wxMpTemplateMessage.addData(wxMpTemplateData);
        }
        WxMpTemplateData first = new WxMpTemplateData();
        first.setName("first");
        first.setValue(wxMemberConsumeMsgDTO.getFirst());

        WxMpTemplateData remark = new WxMpTemplateData();
        remark.setName("remark");
        remark.setValue(wxMemberConsumeMsgDTO.getRemark());
        wxMpTemplateMessage.addData(first);
        wxMpTemplateMessage.addData(remark);
        wxMpTemplateMessage.setUrl(wxMemberConsumeMsgDTO.getUrl());
        wxMpTemplateMessage.setToUser(wxMemberConsumeMsgDTO.getOpenId());
        return sendMemberMsg(wxMemberConsumeMsgDTO.getBrandGuid(), wxMemberConsumeMsgDTO.getWxTemplateTypeEnum().getCode(), wxMpTemplateMessage);
    }

    @Override
    public WxSubjectRespDTO getWxSubject(WxSubjectReqDTO wxSubjectReqDTO) {
        EnterpriseIdentifier.setEnterpriseGuid(wxSubjectReqDTO.getEnterpriseGuid());
        WxStoreAuthorizerInfoDO infoDO = wxStoreAuthorizerInfoService.getByAppId(wxSubjectReqDTO.getAppId());
        log.info("品牌绑定的相关运营主体信息{}", JacksonUtils.writeValueAsString(infoDO));
        //运营主体是否为联盟
        Boolean isAlliance = false;
        //运营主体状态
        Boolean multiMemberStatus = false;
        //运营主体guid
        String operSubjectGuid = "";
        if (Objects.nonNull(infoDO)) {
            isAlliance = infoDO.getIsAlliance();
            operSubjectGuid = infoDO.getOperSubjectGuid();
        }
        if (StringUtils.isNotEmpty(operSubjectGuid)) {
            List<MultiMemberDTO> multiMemberDTOList = enterpriseClientService.list(new HashSet<>(Collections.singleton(operSubjectGuid)));
            log.info("查询运营主体列表返回:{}", JacksonUtils.writeValueAsString(multiMemberDTOList));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(multiMemberDTOList)) {
                multiMemberStatus = multiMemberDTOList.get(0).getEnabled();
            }
        } else {
            // 查询默认运营主体
            MultiMemberDTO multiMemberDTO = enterpriseClientService.defaultMultiMember(wxSubjectReqDTO.getEnterpriseGuid());
            log.info("查询默认运营主体列表返回:{}", JacksonUtils.writeValueAsString(multiMemberDTO));
            if (Objects.nonNull(multiMemberDTO)) {
                multiMemberStatus = multiMemberDTO.getEnabled();
                operSubjectGuid = multiMemberDTO.getMultiMemberGuid();
            }
        }
        return new WxSubjectRespDTO(isAlliance, operSubjectGuid, multiMemberStatus);
    }

    @Override
    public void sendCallMessage(WxSendMessageReqDTO sendMessageReqDTO) {
        List<WxOrderReqDTO> orderList = sendMessageReqDTO.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            log.warn("[发送商家出餐通知]参数为空");
            return;
        }
        List<String> orderGuidList = orderList.stream()
                .map(WxOrderReqDTO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        SingleDataDTO query = new SingleDataDTO();
        query.setDatas(orderGuidList);
        List<OrderDTO> orderDTOList = tradeClientService.listByOrderGuid(query);
        if (CollectionUtils.isEmpty(orderDTOList)) {
            log.warn("[发送商家出餐通知]未查询到订单");
            return;
        }

        // 一次调用接口只存在一个门店的订单
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(orderDTOList.get(0).getStoreGuid());
        if (ObjectUtils.isEmpty(storeDTO)) {
            log.warn("[发送商家出餐通知]未查询到门店信息");
            return;
        }
        String brandGuid = storeDTO.getBelongBrandGuid();
        WxStoreAuthorizerInfoDO authorizerInfoDO = wxStoreAuthorizerInfoService.getById(brandGuid);
        if (ObjectUtils.isEmpty(authorizerInfoDO)) {
            log.warn("[发送商家出餐通知]公众号授权信息查询为空，brandGuid={}", brandGuid);
            return;
        }
        List<WxMpTemplateDO> listByType = wxMpTemplateService.list(new LambdaQueryWrapper<WxMpTemplateDO>()
                .eq(WxMpTemplateDO::getAppId, authorizerInfoDO.getAuthorizerAppid())
                .eq(WxMpTemplateDO::getType, WxTemplateTypeEnum.MERCHANT_MEAL_NOTIFICATIONS.getCode())
                .orderByAsc(WxMpTemplateDO::getId));
        WxMpTemplateDO templateDO = CollectionUtils.isEmpty(listByType) ? null : listByType.get(0);
        if (ObjectUtils.isEmpty(templateDO)) {
            log.warn("模板不存在，联系管理员");
            return;
        }

        LambdaQueryWrapper<WxStoreMerchantOrderDO> wrapper = new LambdaQueryWrapper<WxStoreMerchantOrderDO>()
                .in(WxStoreMerchantOrderDO::getOrderGuid, orderGuidList)
                .orderByDesc(WxStoreMerchantOrderDO::getId);
        List<WxStoreMerchantOrderDO> orderDOList = wxStoreMerchantOrderMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(orderDOList)) {
            log.warn("[发送商家出餐通知]未查询到微信订单");
            return;
        }
        Map<String, WxStoreMerchantOrderDO> merchantOrderDTOMap = orderDOList.stream()
                .collect(Collectors.toMap(WxStoreMerchantOrderDO::getOrderGuid, Function.identity(), (v1, v2) -> v1));

        Map<String, OrderDTO> orderDTOMap = orderDTOList.stream()
                .collect(Collectors.toMap(OrderDTO::getGuid, Function.identity(), (v1, v2) -> v1));

        orderList.forEach(wxOrderReqDTO -> {
            OrderDTO orderDTO = orderDTOMap.get(wxOrderReqDTO.getOrderGuid());
            if (ObjectUtils.isEmpty(orderDTO)) {
                log.warn("[发送商家出餐通知]订单不存在");
                return;
            }

            WxMpTemplateMessage wxMpTemplateMessage = getWxMpTemplateMessage(wxOrderReqDTO, orderDTO, merchantOrderDTOMap,
                    brandGuid);
            if (wxMpTemplateMessage == null) {
                return;
            }
            String templateId = templateDO.getTemplateId();
            wxMpTemplateMessage.setTemplateId(templateId);
            sendMemberMsg(authorizerInfoDO, wxMpTemplateMessage);
        });
    }

    private void sendMemberMsg(WxStoreAuthorizerInfoDO authorizerInfoDO,
                               WxMpTemplateMessage wxMpTemplateMessage) {
        try {
            WxMpTemplateMsgService templateMsgService = wxSaasMpService.getWxMpService(authorizerInfoDO).getTemplateMsgService();
            String result = templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
            log.info("[sendTemplateMsg]wxMpTemplateMessage={},result={}", JacksonUtils.writeValueAsString(wxMpTemplateMessage), result);
        } catch (WxErrorException e) {
            log.error(ERROR, e);
            // 模板不存在 添加模板
            int errorCode = e.getError().getErrorCode();
            String msgByCode = WxMpErrorMsgEnum.findMsgByCode(errorCode);
            String message = msgByCode == null ? e.getMessage() : msgByCode;
            log.warn("msgByCode={},message={}", msgByCode, message);
        }
    }

    @Nullable
    private WxMpTemplateMessage getWxMpTemplateMessage(WxOrderReqDTO wxOrderReqDTO,
                                                       OrderDTO orderDTO,
                                                       Map<String, WxStoreMerchantOrderDO> merchantOrderDTOMap,
                                                       String belongBrandGuid) {
        WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
        // 取单号
        WxMpTemplateData wxMarkMpTemplateData = buildWxMpTemplateData("character_string9", orderDTO.getMark());
        wxMpTemplateMessage.addData(wxMarkMpTemplateData);

        // 商品名称
        String itemName = getItemName(wxOrderReqDTO);
        WxMpTemplateData wxItemNameMpTemplateData = buildWxMpTemplateData("thing11", itemName);
        wxMpTemplateMessage.addData(wxItemNameMpTemplateData);

        // 店铺名称
        WxMpTemplateData wxStoreNameMpTemplateData = buildWxMpTemplateData("thing7", orderDTO.getStoreName());
        wxMpTemplateMessage.addData(wxStoreNameMpTemplateData);


        WxStoreMerchantOrderDO merchantOrderDO = merchantOrderDTOMap.get(wxOrderReqDTO.getOrderGuid());
        if (ObjectUtils.isEmpty(merchantOrderDO)) {
            log.warn("[发送商家出餐通知]微信订单不存在");
            return null;
        }
        String openId = merchantOrderDO.getOpenId();
        String redisKey = String.format(RedisConstants.H5_WECHAT_OPENID_RELATION_KEY, belongBrandGuid, openId);
        String myselfOpenId = (String) redisUtils.get(redisKey);
        log.info("[从缓存查询H5公众号对应的openId]myselfOpenId={}", myselfOpenId);
        if (org.springframework.util.StringUtils.isEmpty(myselfOpenId)) {
            log.warn("[发送商家出餐通知]openId不存在");
            return null;
        }
        String url = String.format(weChatConfig.getMEMBER_ORDER_PAGE(), UserContextUtils.getEnterpriseGuid(),
                orderDTO.getStoreGuid(), myselfOpenId, merchantOrderDO.getOrderRecordGuid());
        wxMpTemplateMessage.setUrl(url);
        wxMpTemplateMessage.setToUser(myselfOpenId);
        return wxMpTemplateMessage;
    }

    @NotNull
    private String getItemName(WxOrderReqDTO wxOrderReqDTO) {
        List<WxOrderItemReqDTO> dstItemList = wxOrderReqDTO.getOrderItemList();
        WxOrderItemReqDTO wxOrderItemReqDTO = dstItemList.get(0);
        String itemName = wxOrderItemReqDTO.getItemName() + "*" + wxOrderItemReqDTO.getNumber();
        if (dstItemList.size() > 1) {
            itemName = itemName + "等";
        }
        return itemName;
    }

    @NotNull
    private WxMpTemplateData buildWxMpTemplateData(String name, String value) {
        WxMpTemplateData wxMpTemplateData = new WxMpTemplateData();
        wxMpTemplateData.setName(name);
        wxMpTemplateData.setValue(value);
        return wxMpTemplateData;
    }

    @Override
    public Result<String> sendMemberMsg(String brandGuid, int messageCode, WxMpTemplateMessage wxMpTemplateMessage) {
        try {
            WxStoreAuthorizerInfoDO authorizerInfoDO = wxStoreAuthorizerInfoService.getById(brandGuid);
            if (ObjectUtils.isEmpty(authorizerInfoDO)) {
                log.warn("公众号授权信息查询为空，直接返回 brandGuid={}", brandGuid);
                return Result.buildEmptySuccess();
            }
            List<WxMpTemplateDO> listByType = wxMpTemplateService.list(new LambdaQueryWrapper<WxMpTemplateDO>()
                    .eq(WxMpTemplateDO::getAppId, authorizerInfoDO.getAuthorizerAppid())
                    //.eq(WxMpTemplateDO::getBrandGuid, brandGuid)
                    .eq(WxMpTemplateDO::getType, messageCode)
                    .orderByAsc(WxMpTemplateDO::getId));
            WxMpTemplateDO one = CollectionUtils.isEmpty(listByType) ? null : listByType.get(0);
            String templateId = null;
            WxMpTemplateMsgService templateMsgService = wxSaasMpService.getWxMpService(authorizerInfoDO).getTemplateMsgService();
            if (one == null) {
                //模板不存在 添加模板
                String shortIdByCode = WxTemplateTypeEnum.getShortIdByCode(messageCode);
                templateId = templateMsgService.addTemplate(shortIdByCode);
                WxMpTemplateDO build = WxMpTemplateDO.builder().appId(authorizerInfoDO.getAuthorizerAppid())
                        .BrandGuid(authorizerInfoDO.getBrandGuid()).guid(String.valueOf(IdWorker.getId()))
                        .shortId(shortIdByCode).templateId(templateId).type(messageCode).build();
                if (!wxMpTemplateService.save(build)) {
                    String result = "公众号信息初始化失败，请稍后重试";
                    log.error(result + ",code:{}", messageCode);
                    //return result;
                }
                //templateMsgService.
            } else {
                templateId = one.getTemplateId();
            }
            wxMpTemplateMessage.setTemplateId(templateId);
            //wxMpTemplateMessage.setToUser(openId);
            String s = templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
            log.info("weixin_templateMsg_send:{},result:{}", wxMpTemplateMessage.toJson(), s);
            return Result.buildSuccessResult(s);
            //templateMsgService.
            /*List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
            for(WxMpTemplate wxMpTemplate:allPrivateTemplate){
                String content = wxMpTemplate.getContent();
                content
            }*/
        } catch (WxErrorException e) {
            log.error(ERROR, e);
            //模板不存在 添加模板
            int errorCode = e.getError().getErrorCode();
            String msgByCode = WxMpErrorMsgEnum.findMsgByCode(errorCode);
            String message = msgByCode == null ? e.getMessage() : msgByCode;
            return Result.buildFailResult(errorCode, message);

        }
    }


    public boolean sendCard(String openid, String authorizerAccessToken, String pagepathParameters, String title, WeChatCardMessageConfig cardMessageConfig) {
        try {
            //返回用户一个mini小程序卡片
            String accessToekn = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=" + authorizerAccessToken;
            //post参数
            JSONObject miniprogrampage = new JSONObject();
            miniprogrampage.put("title", title);
            miniprogrampage.put("pagepath", cardMessageConfig.getPagepath() + pagepathParameters);
            miniprogrampage.put("thumb_media_id", cardMessageConfig.getThumbMediaId());
            miniprogrampage.put("appid", cardMessageConfig.getMiniAppid());
            JSONObject json = new JSONObject();
            json.put("touser", openid);
            json.put("msgtype", "miniprogrampage");
            json.put("miniprogrampage", miniprogrampage);
            log.info("json data:{},发送卡片消息", json);
            String s = HttpsClientUtils.doPostJSON(accessToekn, json);
            log.info(s);
            JSONObject jsonRes = JSON.parseObject(s);
            return "0".equals(jsonRes.getString("errcode"));
        } catch (Exception e) {
            log.error("sendCard 发生卡片消息失败！", e);
        }
        return false;


    }
}
