package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.req.WxQueryThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxThirdPartUserInfoRespDTO;
import com.holderzone.saas.store.weixin.constant.ModelName;
import com.holderzone.saas.store.weixin.entity.domain.WxThirdPartUserInfo;
import com.holderzone.saas.store.weixin.mapper.WxThirdPartUserInfoMapper;
import com.holderzone.saas.store.weixin.service.WxThirdPartUserInfoService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Yu Ren
 * @date 2020/9/16 18:04
 * @description
 */
@Service
@Slf4j
public class WxThirdPartUserInfoServiceImpl extends ServiceImpl<WxThirdPartUserInfoMapper, WxThirdPartUserInfo> implements WxThirdPartUserInfoService {

    @Autowired
    RedisUtils redisUtils;
    @Autowired
    private DynamicHelper dynamicHelper;

    @Override
    public Boolean saveOrUpdateThirdPartUserInfo(WxThirdPartUserInfoReqDTO wxThirdPartUserInfoReqDTO) {
        if (Objects.isNull(wxThirdPartUserInfoReqDTO)) {
            log.info("请求参数不能为空！");
            return false;
        }
        dynamicHelper.changeDatasource(UserContextUtils.getEnterpriseGuid());
        WxThirdPartUserInfo userInfo = findUserInfo(wxThirdPartUserInfoReqDTO.getOpenId(), wxThirdPartUserInfoReqDTO.getTel(),
                wxThirdPartUserInfoReqDTO.getSource());
        if (Objects.nonNull(userInfo)) {
            updateUserInfo(userInfo, wxThirdPartUserInfoReqDTO);
            return true;
        }
        createUserInfo(wxThirdPartUserInfoReqDTO);
        return true;
    }

    @Override
    public WxThirdPartUserInfoRespDTO checkThirdPartUserInfo(WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO) {
        return queryUserInfo(wxQueryThirdPartUserInfoReqDTO);
    }

    @Override
    public WxThirdPartUserInfoRespDTO queryThirdPartUserInfo(WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO) {
        return queryUserInfo(wxQueryThirdPartUserInfoReqDTO);
    }

    // 私有方法：查找用户信息
    private WxThirdPartUserInfo findUserInfo(String openId, String phone, Integer source) {
        return getOne(new LambdaQueryWrapper<WxThirdPartUserInfo>()
                .eq(WxThirdPartUserInfo::getOpenId, openId)
                .eq(WxThirdPartUserInfo::getPhone, phone)
                .eq(WxThirdPartUserInfo::getSource, source)
        );
    }

    // 私有方法：更新用户信息
    private void updateUserInfo(WxThirdPartUserInfo userInfo, WxThirdPartUserInfoReqDTO reqDTO) {
        BeanUtils.copyProperties(reqDTO, userInfo);
        userInfo.setPhone(reqDTO.getTel());
        userInfo.setGmtModified(LocalDateTime.now());
        saveOrUpdate(userInfo);
    }

    // 私有方法：创建用户信息
    private void createUserInfo(WxThirdPartUserInfoReqDTO reqDTO) {
        WxThirdPartUserInfo userInfo = new WxThirdPartUserInfo();
        BeanUtils.copyProperties(reqDTO, userInfo);
        userInfo.setPhone(reqDTO.getTel());
        userInfo.setGuid(redisUtils.generateGuid(ModelName.WX + ":" + reqDTO.getOpenId() + ":consumer"));
        userInfo.setGmtCreate(LocalDateTime.now());
        userInfo.setGmtModified(LocalDateTime.now());
        saveOrUpdate(userInfo);
    }

    // 私有方法：查询用户信息
    private WxThirdPartUserInfoRespDTO queryUserInfo(WxQueryThirdPartUserInfoReqDTO reqDTO) {
        if (Objects.isNull(reqDTO)) {
            log.info("请求参数不能为空！");
            return null;
        }
        dynamicHelper.changeDatasource(UserContextUtils.getEnterpriseGuid());
        WxThirdPartUserInfo userInfo = findUserInfo(reqDTO.getOpenId(), reqDTO.getTel(), reqDTO.getSource());
        if (Objects.isNull(userInfo)) {
            log.info("无第三方用户信息！");
            return null;
        }
        log.info("queryThirdPartUserInfo---->查出第三方会员信息{}", JacksonUtils.writeValueAsString(userInfo));
        WxThirdPartUserInfoRespDTO respDTO = new WxThirdPartUserInfoRespDTO();
        BeanUtils.copyProperties(userInfo, respDTO);
        return respDTO;
    }
}
