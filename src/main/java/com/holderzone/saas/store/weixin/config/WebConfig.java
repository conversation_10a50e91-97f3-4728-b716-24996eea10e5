package com.holderzone.saas.store.weixin.config;

import javax.annotation.Resource;

import com.holderzone.saas.store.weixin.interceptor.MyLocaleChangeInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.holderzone.saas.store.weixin.interceptor.WeixinSessionInterceptor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebConfig
 * @date 2018-07-26 09:51:44
 * @description
 * @program holder-saas-store-order
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
	@Resource
	WeixinSessionInterceptor weixinSessionInterceptor;
	 @Override
	    public void addInterceptors(InterceptorRegistry registry) {
	        registry.addInterceptor(weixinSessionInterceptor).addPathPatterns("/**").order(10);
		    MyLocaleChangeInterceptor myLocaleChangeInterceptor = new MyLocaleChangeInterceptor();
		 	registry.addInterceptor(myLocaleChangeInterceptor);
	    }
}
