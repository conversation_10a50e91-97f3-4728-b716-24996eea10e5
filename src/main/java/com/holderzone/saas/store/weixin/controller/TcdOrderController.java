package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.weixin.service.TcdOrderService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/deal/tcd/order")
@Api(value = "赚餐订单")
public class TcdOrderController {

    private final TcdOrderService orderService;

    @GetMapping(value = "/async/{orderGuid}")
    public void asyncTcdOrder(@PathVariable String orderGuid) {
        log.info("同步小程序订单到赚餐,orderGuid:{}", orderGuid);
        orderService.asyncOrder(orderGuid);
    }

}
