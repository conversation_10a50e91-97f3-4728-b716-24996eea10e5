package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.AbstractMemberModelItemService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ReserveMemberModelItemServiceImpl extends AbstractMemberModelItemService {

	@Autowired
	private WxStoreSessionDetailsService wxStoreSessionDetailsService;


	@Override
	public WxMemberOverviewModelDTO getWxMemberOverviewModel(WxStoreConsumerDTO wxStoreConsumerDTO) {
		return new WxMemberOverviewModelDTO(3, "我的预订", 0);
	}
}
