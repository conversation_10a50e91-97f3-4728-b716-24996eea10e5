package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.queue.ItemGuidDTO;
import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import com.holderzone.saas.store.dto.weixin.WxInQueueReqDTO;
import com.holderzone.saas.store.dto.weixin.WxQueueDetailDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxInQueueRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxTotalQueueDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueService
 * @date 2019/05/16 10:52
 * @description 微信排队Service
 * @program holder-saas-store
 */
public interface WxQueueService {

    WxQueueDetailDTO getDetail(WxPortalReqDTO wxPortalReqDTO);

    WxInQueueRespDTO inQueueByWx(WxInQueueReqDTO wxInQueueReqDTO);

    void callUpNotify(List<ItemGuidDTO> itemGuidDTOS);

    WxTotalQueueDTO getTotalDetail(WxPortalReqDTO wxPortalReqDTO);

    Map<Integer, Integer> queryQueueStatusNum(QueueWechatDTO queueWechatDTO);
}
