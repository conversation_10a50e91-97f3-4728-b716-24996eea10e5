package com.holderzone.saas.store.weixin.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOperationDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.WechatOrderInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreMerchantOrderRespDTO;
import com.holderzone.saas.store.weixin.annotation.OrderToken;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.service.WxStoreMerchantOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Optional;

@Api("微信商户接单管理")
@RestController
@RequestMapping(value = "/wx_store_merchant_order")
@Slf4j
public class WxStoreMerchantOrderController {

    private final WxStoreMerchantOrderService wxStoreMerchantOrderService;

    public WxStoreMerchantOrderController(WxStoreMerchantOrderService wxStoreMerchantOrderService) {
        this.wxStoreMerchantOrderService = wxStoreMerchantOrderService;
    }

    @ApiOperation("获取当前门店所有订单")
    @PostMapping("/get_pend")
    public WxStoreMerchantOrderRespDTO getWxStoreMerchantOrderResp(@RequestBody WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO) {
        log.info("获取当前门店所有待处理订单入参wxStoreMerchantOrderReqDTO:{}", wxStoreMerchantOrderReqDTO);
        return wxStoreMerchantOrderService.getWxStoreMerchantOrderResp(wxStoreMerchantOrderReqDTO);
    }

    /**
     * 一体机获取订单详情
     * 包含pad和微信的订单
     *
     * @param wxStoreMerchantOrderReqDTO 预下单的guid（pad和微信）
     * @return 订单详情
     */
    @ApiOperation("获取订单详情")
    @PostMapping("/get_detail_pend")
    public WxStoreMerchantOrderDTO getDetailPend(@RequestBody WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO) {
        log.info("获取订单详情请求入参，wxStoreMerchantOrderReqDTO:{}", wxStoreMerchantOrderReqDTO);
        return wxStoreMerchantOrderService.getDetailPend(wxStoreMerchantOrderReqDTO);
    }

    /**
     * 微信和pad接单
     *
     * @param wxOperateReqDTO 微信和pad接单信息
     * @return 微信接单返回，pad也用这个返回
     */
    @ApiOperation("商户处理订单，商户接单")
    @PostMapping(value = "/operate")
    @OrderToken(orderGuid = "#wxOperateReqDTO.guid")
    public WxStoreMerchantOperationDTO operationMerchantOrder(@RequestBody WxOperateReqDTO wxOperateReqDTO) {
        log.info("商户接单入参:{}", wxOperateReqDTO);
        log.info("接单请求头:{}", UserContextUtils.get());
        return wxStoreMerchantOrderService.operationMerchantOrder(wxOperateReqDTO);
    }

    @ApiOperation("更新订单")
    @PostMapping(value = "/update")
    public void updateMerchantOrder(@RequestBody WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO) {
        log.info("获取当前门店所有待处理订单入参wxStoreMerchantOrderDTO:{}", wxStoreMerchantOrderDTO);
        wxStoreMerchantOrderService.updateMerchantOrder(wxStoreMerchantOrderDTO);
    }


    @ApiOperation("接单测试")
    @PostMapping("/accept")
    public void accept(String enterpriseGuid, String guid) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        userInfoDTO.setStoreGuid("6506453252643487745");
        UserContextUtils.put(JacksonUtils.writeValueAsString(userInfoDTO));
        WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO();
        wxOperateReqDTO.setGuid(guid);
        wxOperateReqDTO.setOrderState(1);
        EnterpriseIdentifier.setEnterpriseGuid("6506431195651982337");
        wxStoreMerchantOrderService.operationMerchantOrder(wxOperateReqDTO);
    }

    @ApiOperation("测试")
    @GetMapping("/id")
    public WxStoreMerchantOrderDO obtainById(@RequestParam String guid) {
        EnterpriseIdentifier.setEnterpriseGuid("6506431195651982337");
        return wxStoreMerchantOrderService.getById(guid);
    }

    public static void main(String[] args) {
        BigDecimal bigDecimal = Optional.ofNullable(new BigDecimal("110.00000")).orElse(BigDecimal.ZERO).stripTrailingZeros()
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        DecimalFormat format = new DecimalFormat("#.##");
        format.format(bigDecimal);
        System.out.println(format.format(bigDecimal));
        System.out.println(bigDecimal);
        System.out.println("“one” 仅剩" + bigDecimal
                + ",无法添加");
    }

    /**
     * 根据订单记录id获取订单详情
     *
     * @param orderRecordGuid 微信订单记录guid
     * @return 订单详情
     */
    @ApiOperation("根据订单记录id获取订单详情")
    @GetMapping("/get_detail_by_order_record_guid")
    public List<WxStoreMerchantOrderDTO> getDetailByOrderRecordGuid(@RequestParam("orderRecordGuid") String orderRecordGuid) {
        log.info("根据订单记录id获取订单详情，orderRecordGuid={}", orderRecordGuid);
        return wxStoreMerchantOrderService.getDetailByOrderRecordGuid(orderRecordGuid);
    }

    /**
     * 根据订单Guid查询微信订单金额（接单&未接单）
     *
     * @param orderGuid 订单guid
     * @return 微信订单金额（接单&未接单）
     */
    @ApiOperation(value = "根据订单查询订单金额")
    @GetMapping("/get_wechat_order_fee_by_order_guid")
    public BigDecimal getWechatOrderFeeByOrderGuid(@RequestParam("orderGuid") String orderGuid) {
        log.info("根据订单查询订单金额，orderGuid={}", orderGuid);
        return wxStoreMerchantOrderService.getWechatOrderFeeByOrderGuid(orderGuid);
    }

    /**
     * 根据订单Guid查询微信订单信息
     *
     * @param orderGuid 订单guid
     * @return 微信订单信息
     */
    @ApiOperation(value = "根据订单Guid查询微信订单信息")
    @GetMapping("/get_wechat_order_info_by_order_guid")
    public WechatOrderInfoDTO getWechatOrderInfoByOrderGuid(@RequestParam("orderGuid") String orderGuid) {
        log.info("根据订单Guid查询微信订单信息，orderGuid={}", orderGuid);
        return wxStoreMerchantOrderService.getWechatOrderInfoByOrderGuid(orderGuid);
    }

    /**
     * 根据Guid查询微信订单信息
     *
     * @param guid 订单guid/订单记录guid
     * @return 微信订单信息
     */
    @ApiOperation(value = "根据Guid查询微信订单信息")
    @GetMapping("/get_wechat_order_info_by_guid")
    public WechatOrderInfoDTO getWechatOrderInfoByGuid(@RequestParam("guid") String guid) {
        log.info("根据Guid查询微信订单信息，guid={}", guid);
        return wxStoreMerchantOrderService.getWechatOrderInfoByGuid(guid);
    }
}
