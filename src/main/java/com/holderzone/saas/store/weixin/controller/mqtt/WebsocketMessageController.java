package com.holderzone.saas.store.weixin.controller.mqtt;

import com.holderzone.holder.saas.weixin.entry.dto.req.MqttMessageReqDTO;
import com.holderzone.saas.store.weixin.helper.WebsocketMessageHelper;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api("发送mqtt消息")
@RestController
@RequestMapping("/mqtt")
@Slf4j
public class WebsocketMessageController {
	@Resource
	private WebsocketMessageHelper websocketMessageHelper;
	@PostMapping("/sendDinnerEmqMessage")
	public boolean sendDinnerEmqMessage(@RequestBody MqttMessageReqDTO mqttMessageReqDTO) {
		return websocketMessageHelper.sendDinnerEmqMessage(mqttMessageReqDTO.getTableGuid(),
				mqttMessageReqDTO.getOpenIds(),mqttMessageReqDTO.getNickname(),mqttMessageReqDTO.getHeadImgUrl());
	}
	@PostMapping("/sendOrderEmqMessage")
	public void sendOrderEmqMessage(@RequestBody MqttMessageReqDTO mqttMessageReqDTO) {
		websocketMessageHelper.sendOrderEmqMessage(mqttMessageReqDTO.getTableGuid(),
				mqttMessageReqDTO.getOpenIds());
	}
	@PostMapping("/sendShopcartEmqMessage")
	public void sendShopcartEmqMessage(@RequestBody MqttMessageReqDTO mqttMessageReqDTO) {
		websocketMessageHelper.sendShopcartEmqMessage(mqttMessageReqDTO.getTableGuid(),
				mqttMessageReqDTO.getOpenIds());
	}
	@PostMapping("/sendTableEmqMessage")
	public void sendTableEmqMessage(@RequestBody MqttMessageReqDTO mqttMessageReqDTO) {
//		websocketMessageHelper.sendTableEmqMessage(mqttMessageReqDTO.getTableGuid(),mqttMessageReqDTO.getCode(),
//				mqttMessageReqDTO.getOpenIds(),mqttMessageReqDTO.getOnTableDTO(),mqttMessageReqDTO.getOffTableDTO());
	}


	
}
