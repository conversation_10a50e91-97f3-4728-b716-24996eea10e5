package com.holderzone.saas.store.weixin.service.rpc;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseClientService
 * @date 2019/03/07 14:35
 * @description Base服务ClientService
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "base-service", fallbackFactory = BaseClientService.BaseFallBack.class)
public interface BaseClientService {
    @PostMapping("/message/sendMessage")
    boolean sendMessage(MessageDTO messageDTO);

    @PostMapping("/file")
    String upload(@RequestBody FileDto fileDto);

    @Slf4j
    @Component
    class BaseFallBack implements FallbackFactory<BaseClientService> {
        @Override
        public BaseClientService create(Throwable throwable) {
            return new BaseClientService() {
                @Override
                public boolean sendMessage(MessageDTO messageDTO) {
                    log.error("发送公众号解绑短信验证码时发生异常：{}", throwable.getMessage());
                    throw new RuntimeException("发送解绑验证码时发生异常：exception：{}", throwable);
                }

                @Override
                public String upload(FileDto fileDto) {
                    log.error("调用oss文件上传异常：{}", throwable.getMessage());
                    throw new HystrixBadRequestException("调用oss文件上传异常! " + throwable.getMessage());
                }
            };
        }
    }
}
