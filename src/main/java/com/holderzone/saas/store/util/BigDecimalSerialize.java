package com.holderzone.saas.store.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;


/**
 * <AUTHOR>
 * @date 2024/2/29
 * @description 自定义BigDecimal序列化
 */
public class BigDecimalSerialize extends JsonSerializer<BigDecimal> {

    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (null != bigDecimal) {
            jsonGenerator.writeString(bigDecimal.setScale(2, RoundingMode.HALF_DOWN).toPlainString());
        } else {
            jsonGenerator.writeString(BigDecimal.ZERO.toPlainString());
        }
    }
}

