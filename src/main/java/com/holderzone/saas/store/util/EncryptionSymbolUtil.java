package com.holderzone.saas.store.util;


import com.holderzone.saas.store.constant.Constant;
import org.apache.commons.lang3.StringUtils;

public class EncryptionSymbolUtil {

    private EncryptionSymbolUtil() {

    }

    public static boolean isNormal(String str) {
        return StringUtils.isNotEmpty(str) && !Constant.ENCRYPTION_SYMBOL.equals(str);
    }

    public static boolean isEncryption(String str) {
        return Constant.ENCRYPTION_SYMBOL.equals(str);
    }
}
