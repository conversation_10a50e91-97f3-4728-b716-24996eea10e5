package com.holderzone.saas.store.util;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class URLUtils {


    public static Map<String, String> getParamsFromURL(String urlString) {
        Map<String, String> params = new HashMap<>();

        try {
            URL url = new URL(urlString);
            String query = url.getQuery();
            String[] pairs = query.split("&");

            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                String key = keyValue[0];
                String value = keyValue[1];
                params.put(key, value);
            }
        } catch (Exception e) {
            log.error("getParamsFromURL error:", e);
        }
        return params;
    }

    /**
     * 对传入的编码后的 URL 字符串进行解码。
     * 使用 java.net.URLDecoder.decode 方法，将编码后的 URL 转换回原始格式，
     * 使程序能够正确处理其中的中文、空格和特殊字符。
     *
     * @param url 编码后的 URL 字符串
     * @return 解码后的原始 URL 字符串，如果发生异常则返回 null
     */
    public static String decodeUrl(String url) {
        if (url == null) {
            log.error("输入的编码 URL 不能为 null");
            return null;
        }
        try {
            // 使用 UTF-8 编码方式进行 URL 解码
            return URLDecoder.decode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // 捕获不支持的编码异常并打印错误信息
            log.error("不支持的编码格式：UTF-8, e:", e);
            return null;
        }
    }
}
