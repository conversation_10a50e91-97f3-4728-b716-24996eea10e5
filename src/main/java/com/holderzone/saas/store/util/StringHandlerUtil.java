package com.holderzone.saas.store.util;

import com.holderzone.framework.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StringHandlerUtil {

    private StringHandlerUtil() {
    }

    /**
     * 给手机号+区号信息
     *
     * @param phoneCountryCode 区号
     * @param phone            手机号
     * @return +区号+手机号
     */
    public static String phoneCountryCodeHandler(String phoneCountryCode, String phone) {
        if (StringUtils.isEmpty(phone)) {
            return "";
        }
        String phoneNum = Optional.of(phone).orElse("");
        String countryCode = Objects.nonNull(phoneCountryCode) ?
                ("+" + phoneCountryCode + " ") : "+86 ";
        return countryCode + phoneNum;
    }

    /**
     * 给手机号+区号信息
     * 判断用户是否有完整手机号权限
     * 没有权限脱敏展示
     *
     * @param phoneCountryCode 区号
     * @param phone            手机号
     * @return +区号+手机号
     */
    public static String phoneCountryCodeHandler(String phoneCountryCode, String phone, Boolean isTrue) {
        String phoneNum = "";
        if (StringUtils.isEmpty(phone)) {
            return phoneNum;
        }
        if (Boolean.TRUE.equals(isTrue)) {
            phoneNum = phoneCountryCodeHandler(phoneCountryCode, phone);
        } else {
            phoneNum = phoneDesensitizationHandler(phoneCountryCode, phone);
        }
        return phoneNum;
    }

    /**
     * 给手机号设置脱敏保护
     * 给手机号+区号信息
     *
     * @param phoneCountryCode 区号
     * @param phone            手机号
     * @return +区号+手机号
     */
    public static String phoneDesensitizationHandler(String phoneCountryCode, String phone) {
        if (StringUtils.isEmpty(phone)) {
            return "";
        }
        String phoneNum = phone.substring(0, 3) + "****" + phone.substring(7);
        String countryCode = Objects.nonNull(phoneCountryCode) ?
                ("+" + phoneCountryCode + " ") : "+86 ";
        return countryCode + phoneNum;
    }

    /**
     * 集合转换成字符串，每个字符用 、号分割
     */
    public static String arrayConvert(List<String> list) {
        if (StringUtils.isEmpty(list)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        int size = list.size() - 1;
        for (int i = 0; i <= size; i++) {
            String string = list.get(i);
            if (i != size) {
                sb.append(string).append("、");
            } else {
                sb.append(string);
            }
        }
        return sb.toString();
    }

    /**
     * jsonArr 转  "7075343000544477184"|"7075343000544477185"
     * eg:  a.card_guid_json regexp "7075343000544477184"|"7075343000544477185"
     *
     * @param jsonArr
     * @return
     */
    public static String toMysqlRegStr(String jsonArr) {
        return Optional.ofNullable(jsonArr)
                .map(s -> JacksonUtils.toObjectList(String.class, jsonArr))
                .map(StringHandlerUtil::toMysqlRegStr)
                .orElse(null);
    }

    public static String toMysqlRegStr(List<String> list) {
        return list.stream().map(l -> "\"" + l + "\"")
                .collect(Collectors.joining("|"));
    }

    public static String listToString(List<?> list, String separate) {
        if (CollectionUtils.isEmpty(list)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        StringBuilder sb = new StringBuilder();
        int size = list.size() - 1;
        for (int i = 0; i <= size; i++) {
            Object obj = list.get(i);
            if (i != size) {
                sb.append(obj).append(separate);
            } else {
                sb.append(obj);
            }
        }
        return sb.toString();
    }

}
