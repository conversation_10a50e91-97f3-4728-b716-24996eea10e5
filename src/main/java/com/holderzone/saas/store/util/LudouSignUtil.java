package com.holderzone.saas.store.util;

import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class LudouSignUtil {

    /**
     * map数据按key升序排序
     *
     * @param body 数据
     * @return {@link LinkedHashMap}
     */
    public static LinkedHashMap<String, Object> sortMap(Map<String, Object> body) {
        final LinkedHashMap<String, Object> result = new LinkedHashMap<>();
        body.keySet().stream().sorted().forEach(key -> {
            Object value = body.get(key);
            if (value instanceof Map) {
                result.put(key, sortMap((Map<String, Object>) value));
            } else if (value instanceof Collection) {
                result.put(key, sortCollection((Collection<Object>) value));
            } else {
                result.put(key, value);
            }
        });

        return result;
    }

    private static Collection<Object> sortCollection(Collection<Object> collection) {
        if (collection.isEmpty()) {
            return collection;
        } else {
            return collection.stream().map(item -> {
                if (item instanceof Map) {
                    return sortMap((Map<String, Object>) item);
                } else if (item instanceof Collection) {
                    return sortCollection((Collection<Object>) item);
                } else {
                    return item;
                }
            }).collect(Collectors.toList());
        }
    }

    /**
     * 构建签名数据
     *
     * @param sorted    有序参数
     * @param timestamp 时间戳
     * @param appSecret appSecret
     * @return String
     */
    public static String buildSign(LinkedHashMap<String, Object> sorted, Long timestamp, String appSecret) {
        String bodySigned = buildMapSigned(sorted);
        bodySigned = "appSecret=" + appSecret + "&timestamp=" + timestamp.toString() + "&" + bodySigned;

        return SecureUtil.sha256(bodySigned);
    }

    private static String buildMapSigned(Map<String, Object> map) {
        if (map.isEmpty()) {
            return "";
        }
        return map.entrySet().stream().map(entry -> {
            String valueStr;
            Object value = entry.getValue();
            if (value instanceof Map) {
                valueStr = buildMapSigned((Map<String, Object>) value);
            } else if (value instanceof Collection) {
                valueStr = buildCollectionSigned((Collection<Object>) value);
            } else {
                valueStr = String.valueOf(value);
            }
            return entry.getKey() + "=" + valueStr;
        }).collect(Collectors.joining("&"));
    }

    private static String buildCollectionSigned(Collection<Object> collection) {
        if (collection.isEmpty()) {
            return "[]";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        sb.append(collection.stream().map(item -> {
            if (item instanceof Map) {
                return buildMapSigned((Map<String, Object>) item);
            } else if (item instanceof Collection) {
                return buildCollectionSigned((Collection<Object>) item);
            } else {
                return String.valueOf(item);
            }
        }).collect(Collectors.joining(",")));
        sb.append("]");
        return sb.toString();
    }
}
