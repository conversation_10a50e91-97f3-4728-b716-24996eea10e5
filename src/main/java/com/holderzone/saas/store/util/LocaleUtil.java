package com.holderzone.saas.store.util;

import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.StringUtils;

import java.util.ResourceBundle;


/**
 * 国际化转换工具
 * <AUTHOR>
 */
@Slf4j
public class LocaleUtil {

    private LocaleUtil(){
    }

    public static String getMessage(LocaleMessageEnum localeMessageEnum) {
        try {
            ResourceBundle bundle = ResourceBundle.getBundle("i18n/messages", LocaleContextHolder.getLocale());
            return bundle.getString(localeMessageEnum.name());
        } catch (Exception e) {
            return localeMessageEnum.getMessage();
        }
    }

    public static String getMessage(String code) {
        try {
            ResourceBundle bundle = ResourceBundle.getBundle("i18n/messages", LocaleContextHolder.getLocale());
            return bundle.getString(code);
        } catch (Exception e) {
            return code;
        }
    }

}
