package com.holderzone.saas.store.enums.order;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemPriceChangeEnum
 * @date 2019/10/08 14:37
 * @description the type for price-change
 * @program IdeaProjects
 */
public enum  ItemPriceChangeEnum {

    NO_CHANGE(0,"没进行任何改价"),

    PRICE_CHANGE(1,"商品改价"),

    DISCOUNT(2,"商品折扣");


    private int code;

    private String desc;

    ItemPriceChangeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}