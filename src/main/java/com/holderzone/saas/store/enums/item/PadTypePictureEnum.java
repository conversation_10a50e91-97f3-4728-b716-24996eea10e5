package com.holderzone.saas.store.enums.item;

/**
 * <AUTHOR>
 * @description pad分类图片大小类型枚举
 * @date 2021/8/3 14:56
 */
public enum PadTypePictureEnum {

    SMALL_PICTURE(0, "列表小图模式"),

    VERTICAL_PICTURE(1, "列表竖图模式"),

    FULL_SCREEN_IMAGE(2, "列表整屏图模式"),
    ;

    private final int code;
    private final String desc;

    PadTypePictureEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
