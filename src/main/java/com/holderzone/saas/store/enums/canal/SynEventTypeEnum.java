package com.holderzone.saas.store.enums.canal;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className SynEventTypeEnum
 * @date 18-11-14 下午5:56
 * @description canal数据同步数据操作类型
 * @program holder-saas-store-dto
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SynEventTypeEnum {

    INSERT(0, "INSERT"),
    UPDATE(1, "UPDATE"),
    DELETE(2, "DELETE"),;

    private Integer code;

    private String desc;


    /**
     * 根据枚举code获取对应描述
     *
     * @param code code
     * @return 枚举描述
     */
    public static String getDescByCode(Integer code) {
        return Arrays.stream(SynEventTypeEnum.values()).filter(p -> p.getCode().equals(code))
                     .findFirst().orElseThrow(() -> new BusinessException("不存在当前code对应的desc")).getDesc();
    }

    /**
     * 根据枚举desc获取对应code
     *
     * @param desc code
     * @return 枚举描述
     */
    public static Integer getDescByCode(String desc) {
        return Arrays.stream(SynEventTypeEnum.values()).filter(p -> p.getDesc().equals(desc))
                .findFirst().orElseThrow(() -> new BusinessException("不存在当前desc对应的code")).getCode();
    }
}