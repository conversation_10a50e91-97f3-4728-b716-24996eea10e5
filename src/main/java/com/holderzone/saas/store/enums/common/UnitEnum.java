package com.holderzone.saas.store.enums.common;

import com.holderzone.saas.store.enums.marketing.portrayal.FieldTypeEnum;

/**
 * <AUTHOR>
 * @date 2024/12/26
 * @description 单位枚举
 */
public enum UnitEnum {

    CI(1, "次"),

    <PERSON><PERSON><PERSON>(2, "元"),

    <PERSON><PERSON><PERSON>(2, "角"),

    FEN(2, "分"),
    ;

    private int code;
    private String desc;

    UnitEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getName(int code) {
        for (UnitEnum unitEnum : UnitEnum.values()) {
            if (unitEnum.getCode() == code) {
                return unitEnum.getDesc();
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
