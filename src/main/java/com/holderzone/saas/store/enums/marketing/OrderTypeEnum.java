package com.holderzone.saas.store.enums.marketing;

import com.holderzone.saas.store.enums.order.TradeModeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 活动订单类型
 */
@Getter
@AllArgsConstructor
public enum OrderTypeEnum {

    /**
     * 正餐
     */
    DINNER(1, "正餐"),

    /**
     * 快餐
     */
    FAST_FOOD(2, "快餐"),

    UNKNOWN(999, "未知"),
    ;


    private final int code;

    private final String des;

    public static String getByCode(int code) {
        for (OrderTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDes();
            }
        }
        return UNKNOWN.getDes();
    }


    public static int transferCode(int code) {
        if (TradeModeEnum.DINEIN.getCode() == code) {
            return OrderTypeEnum.DINNER.getCode();
        }
        if (TradeModeEnum.FAST.getCode() == code) {
            return OrderTypeEnum.FAST_FOOD.getCode();
        }
        return OrderTypeEnum.UNKNOWN.getCode();
    }

}
