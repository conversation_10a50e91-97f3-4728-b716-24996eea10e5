package com.holderzone.saas.store.enums.member;

import com.holderzone.saas.store.constant.Constant;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 优惠券不可使用原因
 */
@Getter
@AllArgsConstructor
public enum VolumeUnableTipEnum {

    MEMBER_PRICE("使用了会员价不能使用该优惠券", Constant.OTHER_DISCOUNT_SHARE),
    MEMBER_DISCOUNT_PRICE("使用了会员折扣不能使用该优惠券", Constant.OTHER_DISCOUNT_SHARE),
    OTHER_DISCOUNT_PRICE("使用了其他营销活动不能使用该优惠券", Constant.OTHER_DISCOUNT_SHARE),
    UN_FULL_CONDITION("当前订单不满足该优惠券使用条件", Constant.UN_FULL_CONDITION),
    ;

    private final String tips;

    private final String view;

    public static String getView(String tips) {
        for (VolumeUnableTipEnum tipEnum : values()) {
            if (tipEnum.getTips().equals(tips)) {
                return tipEnum.getView();
            }
        }
        return UN_FULL_CONDITION.getView();
    }
}
