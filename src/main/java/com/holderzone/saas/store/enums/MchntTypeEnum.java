package com.holderzone.saas.store.enums;

public enum MchntTypeEnum {

    CATERING("10001", "餐饮"),

    RETAIL("10002", "零售"),

    CANTEEN("10003","食堂")

    ;

    private String code;

    private String desc;

    MchntTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
