package com.holderzone.saas.store.enums.print;

import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;

/**
 * 打印来源
 *
 * <AUTHOR>
 * @date 2018/10/25 9:15
 */
public enum PrintSourceEnum {

    AIO(1, "台式收银设备", "SunMi T1, SunMi T1 mini, SunMi T2, SunMi D1"),

    POS(2, "手持收银设备(集成打印)", "SunMi P1, SunMi V1, SunMi V1s, SunMi V2"),

    MBP(3, "手持收银设备(未集成打印)", "SunMi M1, SunMi M2, SunMi L2"),

    PAD(4, "安卓平板", "小米平板3, 小米平板4, 荣耀平板?"),

    KDS(9, "厨房显示系统", ""),

    BOSS(10, "老板助手（手机端）", "todo"),

    SELF(11, "自助点餐机", "todo"),

    WECHAT(12, "微信", "微信"),

    ALI(13, "支付宝", "支付宝"),

    KQS(14, "厨房取餐屏（TV）", "厨房取餐屏（TV）"),

    TAKEOUT(20, "外卖", "外部外卖系统"),

    ;

    /**
     * 设备类型
     */
    private int code;

    /**
     * 设备描述
     */
    private String desc;

    /**
     * 设备型号
     */
    private String model;

    PrintSourceEnum(int code, String desc, String model) {
        this.code = code;
        this.desc = desc;
        this.model = model;
    }

    /**
     * 根据code获取相应的来源实例
     *
     * @param code
     * @return
     */
    public static PrintSourceEnum getPrintSourceByCode(int code) {
        for (PrintSourceEnum printSourceEnum : PrintSourceEnum.values()) {
            if (printSourceEnum.code == code) {
                return printSourceEnum;
            }
        }
        return null;
    }

    /**
     * deviceType到printSource的适配
     *
     * @param deviceType
     * @return
     */
    public static PrintSourceEnum getPrintSourceByDeviceType(int deviceType) {
        switch (BaseDeviceTypeEnum.getDeviceTypeByCode(deviceType)) {
            case POS:
            case PV1:
                return POS;
            case M1:
                return MBP;
            case CLOUD_PANEL:
                return PAD;
            case KDS:
                return KDS;
            case BOSS:
                return BOSS;
            case SELF:
                return SELF;
            case WECHAT:
            case WECHAT_MINI:
            case WECHAT_UNUSED:
                return WECHAT;
            case ALI:
                return ALI;
            case KQS:
                return KQS;
            case TCD:
                return TAKEOUT;
            case All_IN_ONE:
            case CLOUD:
            case MERCHANT:
            default:
                return AIO;
        }
    }
}
