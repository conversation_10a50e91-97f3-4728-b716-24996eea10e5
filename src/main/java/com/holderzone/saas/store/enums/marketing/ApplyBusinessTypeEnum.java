package com.holderzone.saas.store.enums.marketing;


/**
 * <AUTHOR>
 * @date 2024/5/28
 * @description 业务类型枚举
 */
public enum ApplyBusinessTypeEnum {

    /**
     * 全部业务
     */
    ALL_BUSINESS(0,"全部业务"),

    /**
     * 部分业务
     */
    APPOINT_BUSINESS(1,"部分业务"),

    ;

    /**
     * 编号
     */
    private int code;

    /**
     * 描述
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }


    ApplyBusinessTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }
}
