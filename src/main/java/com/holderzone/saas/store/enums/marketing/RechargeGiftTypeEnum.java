package com.holderzone.saas.store.enums.marketing;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2024/6/4
 * @description 适用人群类型 0 不限制 1 所有注册会员 2 指定标签会员 3 指定等级会员 4 指定会员
 */
public enum RechargeGiftTypeEnum {

    /**
     * 不限制
     */
    RECHARGE_UNRESTRICTED(0, "不限制"),

    /**
     * 所有注册会员
     */
    RECHARGE_ALL_MEMBER(1, "所有注册会员"),

    /**
     * 指定标签会员
     */
    RECHARGE_LABEL_MEMBER(2, "指定标签会员"),

    /**
     * 指定等级会员
     */
    RECHARGE_GRADE_MEMBER(3, "指定等级会员"),

    /**
     * 指定会员
     */
    RECHARGE_MEMBER(4, "指定会员");

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    RechargeGiftTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static RechargeGiftTypeEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (RechargeGiftTypeEnum typeEnum : RechargeGiftTypeEnum.values()) {
            if (typeEnum.code == code) {
                return typeEnum;
            }
        }
        return null;
    }
}
