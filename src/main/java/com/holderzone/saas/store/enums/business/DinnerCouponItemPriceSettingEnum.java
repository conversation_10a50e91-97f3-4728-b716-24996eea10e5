package com.holderzone.saas.store.enums.business;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 正餐点餐页验券加购商品取值枚举
 *
 * <AUTHOR>
 * @date 2025/5/7
 * @since 1.8
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum DinnerCouponItemPriceSettingEnum {

    ORIGINAL_PRICE(1, 1, "商品原价"),

    PURCHASE_PRICE(1, 2, "商品购买价");

    private int typeCode;
    private int code;
    private String des;
}
