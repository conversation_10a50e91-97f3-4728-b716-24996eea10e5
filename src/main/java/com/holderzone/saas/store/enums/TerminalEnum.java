package com.holderzone.saas.store.enums;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className DeviceTypeEnum
 * @date 19-2-28 上午10:57
 * @description important：这个类的terminalCode和云端创建终端的code需保持一致！！！1
 * @program holder-saas-store-organization
 */
// todo 这个转换实际已经没用了， 后面重构的时候删了
@Deprecated
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TerminalEnum {
    /**
     * 一体机
     */
    AIO(3, "3"),

    /**
     * pos
     */
    POS(4, "4"),

    /**
     * pad
     */
    PAD(5, "5"),

    /**
     * 点菜宝
     */
    MOBILE(6, "4"),

    /**
     * 带刷卡的pos
      */
    POS_F(7, "4"),

    /**
     * 带刷卡的pos
      */
    KDS(9, "9"),

    KQS(14, "14"),

    SELF(11, "11"),

    ;


    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 终端code
     */
    private String terminalCode;


    /**
     * 根据设备类型获取终端code
     *
     * @param deviceType 设备类型
     * @return 终端code
     */
    public static String getTerminalCodeByDeviceType(Integer deviceType) {
        return Arrays.stream(TerminalEnum.values()).filter(p -> p.getDeviceType().equals(deviceType))
                     .findAny().orElseThrow(() -> new BusinessException("不存在该设备类型对应的终端code")).getTerminalCode();
    }
}