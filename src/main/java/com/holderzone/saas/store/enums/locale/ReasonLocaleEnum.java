package com.holderzone.saas.store.enums.locale;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Map;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
@Getter
public enum ReasonLocaleEnum {


    REASON_UNDO_CHECKOUT("反结账原因"),
    REASON_VOID_ORDER("作废订单原因"),
    REASON_CANCEL_RESERVATION("取消预定原因"),
    REASON_REJECT_TAKEAWAY_ORDER("外卖拒单原因"),
    REASON_RETURN_TAKEAWAY_ORDER("外卖退单原因"),
    REASON_REJECT_REFUND_FOR_TAKEAWAY("外卖拒绝退款原因"),
    REASON_RETURN_DISH("退菜原因"),
    REASON_COMPLIMENTARY_DISH("赠菜原因"),
    REASON_INCORRECT_SETTLEMENT("结错账"),
    REASON_REMAINING_DISHES_NOT_RETURNED("剩余菜品未退"),
    REASON_MODIFY_PAYMENT_METHOD("修改结账方式"),
    REASON_OTHER("其他"),
    REASON_CUSTOMER_LEAVES("顾客离店"),
    REASON_DUPLICATE_ORDER("订单重复"),
    REASON_CUSTOMER_CANCELS_TEMPORARILY("客户临时取消"),
    REASON_NO_AVAILABLE_TABLES("已无桌"),
    REASON_CUSTOMER_DIDNT_SHOW_UP("顾客没来"),
    REASON_ITEM_SOLD_OUT("商品售罄"),
    REASON_ITEM_ALREADY_SERVED("商品已送出"),
    REASON_AMOUNT_ALREADY_REFUNDED("金额已退还"),
    REASON_ORDER_PLACED_INCORRECTLY("点错了"),
    REASON_SLOW_SERVICE("上菜慢"),
    REASON_PROMOTIONAL_GIFT("活动赠送"),
    REASON_CONSUMPTION_GIFT("消费赠送"),
    REASON_BOSS_GIFT("老板赠送"),
    REASON_REFUND_REASON("退款原因"),
    REASON_CUSTOMER_ORDER_REFUND("顾客退单"),
    REASON_UNABLE_DELIVER("无法配送"),

    ;



    private final String message;



    ReasonLocaleEnum(String message){
        this.message = message;
    }

    private final static Map<String, ReasonLocaleEnum> LOCALE_MAP;

    static {
        LOCALE_MAP = initMap();

    }

    private static Map<String, ReasonLocaleEnum> initMap(){
        Map<String, ReasonLocaleEnum> localeMap = Maps.newHashMap();
        for (ReasonLocaleEnum reasonLocaleEnum : ReasonLocaleEnum.values()){
            localeMap.put(reasonLocaleEnum.message,reasonLocaleEnum);
        }
        return localeMap;
    }

    public static String getLocale(String message){
        ReasonLocaleEnum reasonLocaleEnum = LOCALE_MAP.get(message);
        return reasonLocaleEnum == null ? message : getLocaleMessage(reasonLocaleEnum);
    }

    private static String getLocaleMessage(ReasonLocaleEnum reasonLocaleEnum) {
        try {
            ResourceBundle bundle = ResourceBundle.getBundle("i18n/messages", LocaleContextHolder.getLocale());
            return bundle.getString(reasonLocaleEnum.name());
        } catch (Exception e) {
            return reasonLocaleEnum.getMessage();
        }
    }
}
