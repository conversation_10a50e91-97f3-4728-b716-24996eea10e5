package com.holderzone.saas.store.enums.trade;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存扣减策略枚举
 */
@Getter
@AllArgsConstructor
public enum StockReduceStrategyEnum {

    STORE_ERP("STORE-ERP", "门店ERP库存扣减"),
    MDM("MDM", "MDM库存扣减"),
    WEIHAI_ERP("WEIHAI-ERP", "微海供应链库存扣减");

    /**
     * 策略代码
     */
    private final String code;

    /**
     * 策略描述
     */
    private final String desc;
} 