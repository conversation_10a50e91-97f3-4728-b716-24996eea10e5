package com.holderzone.saas.store.enums;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.util.LocaleUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeEnum
 * @date 2018/09/04 17:52
 * @description
 * @program holder-saas-store-trade
 */
public enum PaymentTypeEnum {

    CASH(1, "现金支付"),
    AGG(2, "聚合支付"),
    CARD(3, "银联支付"),
    MEMBER(4, "会员余额支付"),
    FACE(5, "人脸支付"),
    TONGCHIDAO(6, "通吃岛支付"),
    RESERVE(7, "预付金支付"),
    DEBT_PAY(8, "挂账支付"),
    /**
     * 后来人如果看到9，请记录到这里
     */
    OTHER(10, "其他支付方式"),
    CANTEEN_ELECTRONIC_CARD(11, "食堂电子卡支付"),
    CANTEEN_PHYSICAL_CARD(12, "食堂实体卡支付"),
    THIRD_ACTIVITY(13, "第三方平台活动"),
    LUDOU_MEMBER_PAY(14, "麓豆支付"),
    MT_GROUPON(20, "美团团购"),
    MT_MAITON(70, "美团买单"),
    OFFLINE_REFUND(30, "线下退款"),
    ;

    private int code;
    private String desc;

    PaymentTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (PaymentTypeEnum c : PaymentTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        throw new ParameterException("支付方式类型不匹配");
    }

    public static PaymentTypeEnum getEnum(int code) {
        for (PaymentTypeEnum c : PaymentTypeEnum.values()) {
            if (c.getCode() == code) {
                return c;
            }
        }
        throw new ParameterException("支付方式类型不匹配");
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static int discountConvertPaymentCode(DiscountTypeEnum discountTypeEnum) {
        if (discountTypeEnum == DiscountTypeEnum.THIRD_ACTIVITY) {
            return THIRD_ACTIVITY.code;
        }
        if (discountTypeEnum == DiscountTypeEnum.GROUPON) {
            return MT_GROUPON.code;
        }
        if (discountTypeEnum == DiscountTypeEnum.DOU_YIN_GROUPON) {
            return OTHER.code;
        }
        return OTHER.code;
    }

    public static String discountConvertPaymentName(DiscountTypeEnum discountTypeEnum) {
        if (discountTypeEnum == DiscountTypeEnum.THIRD_ACTIVITY) {
            return THIRD_ACTIVITY.desc;
        }
        if (discountTypeEnum == DiscountTypeEnum.GROUPON) {
            return MT_GROUPON.desc;
        }
        if (discountTypeEnum == DiscountTypeEnum.DOU_YIN_GROUPON) {
            return OTHER.desc;
        }
        return OTHER.desc;
    }

    public static PaymentTypeEnum groupBuyConvertPaymentEnum(GroupBuyTypeEnum groupBuyTypeEnum) {
        if (groupBuyTypeEnum == GroupBuyTypeEnum.MEI_TUAN) {
            return MT_GROUPON;
        }
        return null;
    }


    public static List<String> getFilterGrouponPaymentTypeNames() {
        return Lists.newArrayList(PaymentTypeEnum.MT_GROUPON.getDesc(), DiscountTypeEnum.DOU_YIN_GROUPON.getDesc()
                , DiscountTypeEnum.ALIPAY_GROUPON.getDesc()
                , DiscountTypeEnum.DA_ZHONG_DIAN_PIN.getDesc()
                , DiscountTypeEnum.ABC_GROUPON.getDesc(), DiscountTypeEnum.MAITON_GROUPON.getDesc());
    }

    public static List<Integer> getFilterGrouponPaymentTypes() {
        return Lists.newArrayList(PaymentTypeEnum.MT_GROUPON.getCode(), DiscountTypeEnum.DOU_YIN_GROUPON.getCode()
                , DiscountTypeEnum.ALIPAY_GROUPON.getCode()
                , DiscountTypeEnum.DA_ZHONG_DIAN_PIN.getCode()
                , DiscountTypeEnum.ABC_GROUPON.getCode(), PaymentTypeEnum.MT_MAITON.getCode());
    }

    public static boolean otherOrGroupPay(int type) {
        return type != PaymentTypeEnum.TONGCHIDAO.getCode() && (type == OTHER.getCode() || GroupBuyTypeEnum.CODE_LIST.contains(type));
    }

    public static String getPaymentTypeNameByCode(int code) {
        for (PaymentTypeEnum c : PaymentTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        for (GroupBuyTypeEnum value : GroupBuyTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return null;
    }

    public enum PaymentType {
        JD(-4, "京东外卖"),

        ZHUAN_CAN(-3, "赚餐外卖"),

        MEIT_TUAN(-2, "美团外卖"),

        E_LE_MA(-1, "饿了么外卖"),

        CASH_PAY(0, "现金支付"),

        JH_PAY(1, "聚合支付"),

        BANK_CARD_PAY(2, "银联支付"),

        MEMBER_CARD_PAY(3, "会员余额支付"),

        CARD_PAY(31, "卡余额支付"),

        FACE_TO_PAY(4, "人脸支付"),
        //由于trade服务 存在为5、6、7、10的支付方式
        DEBT_PAY(8, "挂账支付"),

        CANTEEN_CARD_PAY(9, "食堂卡支付"),

        CANTEEN_ELECTRONIC_CARD(11, "食堂电子卡支付"),

        CANTEEN_PHYSICAL_CARD(12, "食堂实体卡支付"),

        MT_GROUPON_PAYMENT(20, "美团团购"),

        DOUYIN_GROUPON_PAYMENT(61, "抖音团购"),

        ALIPAY_PAYMENT(65, "支付宝团购"),

        ABC_PAYMENT(66, "农行团购"),

        THIRD_ACTIVITY(13, "第三方平台活动");

        private Integer id;

        private String name;

        PaymentType(Integer id, String name) {
            this.id = id;
            this.name = name;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public static String getLocaleNameById(Integer id) {
            return Arrays.stream(PaymentType.values())
                    .filter(paymentType -> Objects.equals(paymentType.id, id))
                    .map(paymentType -> LocaleUtil.getMessage(paymentType.name()))
                    .findFirst()
                    .orElse(null);
        }

        public static String getLocaleName(String name) {
            return Arrays.stream(PaymentType.values())
                    .filter(paymentType -> Objects.equals(paymentType.name, name))
                    .map(paymentType -> LocaleUtil.getMessage(paymentType.name()))
                    .findFirst()
                    .orElse(name);
        }

        public static Integer getIdByName(String name) {
            for (PaymentType value : values()) {
                if (value.getName().equals(name)) {
                    return value.getId();
                }
            }
            return null;
        }

    }

    private static final String PREFIX = "PAY_";

    public static String getPayLocaleNameByCode(Integer code) {
        return Arrays.stream(PaymentTypeEnum.values())
                .filter(paymentType -> Objects.equals(paymentType.code, code))
                .map(paymentType -> LocaleUtil.getMessage(PREFIX + paymentType.name()))
                .findFirst()
                .orElse(null);
    }
}
