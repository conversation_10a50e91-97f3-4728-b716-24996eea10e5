package com.holderzone.saas.store.enums.takeaway;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TikTokBusinessIdEnum {

    TUAN_GOU(1, "团购"),

    TAKEOUT(2, "外卖"),
    ;

    /**
     * 类型
     */
    private final int type;

    /**
     * 描述
     */
    private final String desc;


    public static TikTokBusinessIdEnum ofType(int type) {
        for (TikTokBusinessIdEnum value : TikTokBusinessIdEnum.values()) {
            if (type == value.type) {
                return value;
            }
        }
        throw new IllegalArgumentException("不存在业务类型businessId[" + type + "]");
    }
}
