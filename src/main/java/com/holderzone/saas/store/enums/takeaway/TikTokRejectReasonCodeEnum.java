package com.holderzone.saas.store.enums.takeaway;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 抖音外卖拒单 原因
 */
@Getter
@AllArgsConstructor
public enum TikTokRejectReasonCodeEnum {

    USER_INFO_ERROR(501, "用户信息错误"),
    PRODUCT_SOLD_OUT(502, "商品已经售完"),
    CLOSING(503, "商家已经打烊"),
    MERCHANT_BUSY(504, "商家现在太忙"),
    EXCEED_AREA(505, "超出配送范围"),
    ;

    /**
     * 错误码
     */
    private final int errorCode;

    /**
     * 错误描述
     */
    private final String description;

    public static TikTokRejectReasonCodeEnum ofErrorCode(int errorCode) {
        for (TikTokRejectReasonCodeEnum value : TikTokRejectReasonCodeEnum.values()) {
            if (errorCode == value.errorCode) {
                return value;
            }
        }
        return null;
    }

    public static Integer ofDescription(String description) {
        for (TikTokRejectReasonCodeEnum value : TikTokRejectReasonCodeEnum.values()) {
            if (value.getDescription().equals(description)) {
                return value.getErrorCode();
            }
        }
        return null;
    }
}
