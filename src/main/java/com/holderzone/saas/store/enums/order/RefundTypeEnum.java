package com.holderzone.saas.store.enums.order;

/**
 * 退款方式
 */
public enum RefundTypeEnum {

    BACKTRACK(0, "原路退回"),
    OFFLINE_REFUND(1, "线下退款"),
    ;

    private int code;
    private String desc;

    RefundTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static RefundTypeEnum getByCode(int code) {
        for (RefundTypeEnum value : RefundTypeEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
