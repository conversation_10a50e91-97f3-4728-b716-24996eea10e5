package com.holderzone.saas.store.enums.common;

/**
 * <AUTHOR>
 * @description 结果状态枚举
 * @date 2021/9/7 20:04
 * @className: ResultStateEnum
 */
public enum ResultStateEnum {

    SUCCESS(0, "成功"),
    FAIL(1, "失败"),
    MALFORMED_PHONE_NUMBER(2, "手机号格式错误"),
    VERIFICATION_CODE_ERROR(3, "验证码错误"),
    VERIFICATION_CODE_EXPIRED(4, "验证码已失效"),
    CURRENT_PHONE_NUMBER_IS_NOT_REGISTERED(5, "当前手机号未注册"),
    ;

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return desc;
    }

    ResultStateEnum() {
    }

    ResultStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
