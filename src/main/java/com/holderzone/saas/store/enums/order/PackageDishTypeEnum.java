package com.holderzone.saas.store.enums.order;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderStateEnum
 * @date 2018/09/04 17:52
 * @description 订单状态枚举
 * @program holder-saas-store-order
 */
public enum PackageDishTypeEnum {

    单规格(0, "单规格"),
    多规格(1, "多规格"),
    套餐主项(2, "套餐主项"),
    套餐子项(3, "套餐子项"),
    称重菜品(4, "称重菜品"),
    ;

    private int code;
    private String desc;

    private PackageDishTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (PackageDishTypeEnum c : PackageDishTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
