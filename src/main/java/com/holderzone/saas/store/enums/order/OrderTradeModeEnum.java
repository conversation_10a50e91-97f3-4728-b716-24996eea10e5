package com.holderzone.saas.store.enums.order;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTradeModeEnum
 * @date 2018/09/13 15:00
 * @description //订单交易模式
 * @program holder-saas-store-order
 */
public enum OrderTradeModeEnum {
    正餐(0, "正餐"),
    快餐(1, "快餐"),
//    外卖(2, "外卖"),

    ;

    private int code;
    private String desc;

    private OrderTradeModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (OrderTradeModeEnum c : OrderTradeModeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
