package com.holderzone.saas.store.enums.trade;

import com.holderzone.framework.exception.unchecked.ParameterException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/5
 * @description 排序类型枚举
 */
@Getter
public enum SortTypeEnum {

    DEFAULT(0, "默认排序"),

    GMT_CREATE_ASC(1, "下单时间正序"),

    GMT_CREATE_DESC(2, "下单时间倒序"),

    ORDER_FEE_ASC(3, "订单金额正序"),

    ORDER_FEE_DESC(4, "订单金额倒序"),

    CHECKOUT_TIME_ASC(5, "结账时间正序"),

    CHECKOUT_TIME_DESC(6, "结账时间倒序"),
    ;

    private final int code;

    private final String desc;

    SortTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (SortTypeEnum c : SortTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        throw new ParameterException("排序类型不匹配");
    }

    public static SortTypeEnum sortByType(int code) {
        for (SortTypeEnum c : SortTypeEnum.values()) {
            if (c.getCode() == code) {
                return c;
            }
        }
        throw new ParameterException("排序类型不匹配");
    }

}
