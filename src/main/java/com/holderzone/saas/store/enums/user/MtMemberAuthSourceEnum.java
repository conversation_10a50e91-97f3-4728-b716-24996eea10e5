package com.holderzone.saas.store.enums.user;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum MtMemberAuthSourceEnum {

    DINE_IN_REFUND("mt_member_auth", "第三方授权同步"),
    ;

    private final String sourceCode;

    private final String sourceName;

    MtMemberAuthSourceEnum(String sourceCode, String sourceName) {
        this.sourceCode = sourceCode;
        this.sourceName = sourceName;
    }

    public static MtMemberAuthSourceEnum getEnumBySourceCode(String sourceCode) {
        for (MtMemberAuthSourceEnum sourceEnum : MtMemberAuthSourceEnum.values()) {
            if (Objects.equals(sourceEnum.getSourceCode(), sourceCode)) {
                return sourceEnum;
            }
        }
        return null;
    }
}
