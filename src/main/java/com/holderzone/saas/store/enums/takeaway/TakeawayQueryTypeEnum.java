package com.holderzone.saas.store.enums.takeaway;


import com.holderzone.framework.exception.unchecked.ParameterException;
import lombok.Getter;

@Getter
public enum TakeawayQueryTypeEnum {

    mt(0, "美团"),

    ele(1, "饿了么"),

    own(2, "自营"),

    tcd(3, "赚餐"),

    TI<PERSON><PERSON><PERSON>(4, "抖音"),

    JD(5, "京东"),
    ;

    private final Integer code;

    private final String desc;

    TakeawayQueryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(int code) {
        for (TakeawayQueryTypeEnum typeEnum : TakeawayQueryTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum.desc;
            }
        }
        throw new ParameterException("外卖类型不匹配");
    }

}
