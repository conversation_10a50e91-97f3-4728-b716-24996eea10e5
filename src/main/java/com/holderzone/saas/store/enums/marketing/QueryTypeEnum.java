package com.holderzone.saas.store.enums.marketing;

import com.holderzone.saas.store.enums.order.DiscountTypeEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/5
 * @description 查询类型
 */
public enum QueryTypeEnum {

    UNKNOWN_TYPE(-1, "未知类型"),

    LIMIT_SPECIALS_ACTIVITY(1, "限时特价活动"),

    FOLLOW_RED_PACKET_ACTIVITY(2, "随行红包"),
    NTH_ACTIVITY(3, "第N份优惠活动"),

    ;

    private final int code;

    private final String des;

    QueryTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

    public static QueryTypeEnum getEnum(int code) {
        for (QueryTypeEnum typeEnum : QueryTypeEnum.values()) {
            if (Objects.equals(typeEnum.getCode(), code)) {
                return typeEnum;
            }
        }
        return QueryTypeEnum.UNKNOWN_TYPE;
    }

    public static Integer transferQueryType(int code) {
        DiscountTypeEnum discountTypeEnum = DiscountTypeEnum.get(code);
        switch (discountTypeEnum) {
            case LIMIT_SPECIALS_ACTIVITY:
                return LIMIT_SPECIALS_ACTIVITY.getCode();
            case NTH_ACTIVITY:
                return NTH_ACTIVITY.getCode();
            case FOLLOW_RED_PACKET:
                return FOLLOW_RED_PACKET_ACTIVITY.getCode();
            default:
                return UNKNOWN_TYPE.getCode();
        }
    }

}
