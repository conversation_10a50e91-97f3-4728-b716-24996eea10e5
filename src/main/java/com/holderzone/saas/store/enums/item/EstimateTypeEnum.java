package com.holderzone.saas.store.enums.item;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 估清类型
 *
 * <AUTHOR>
 * @date 2025/7/18
 * @since 1.8
 */
@Getter
public enum EstimateTypeEnum {

    TEMPORARY_ESTIMATE(1, "售完下架"),

    FOREVER_ESTIMATE(2, "长期估清");

    private static final Map<Integer, String> CODE_TO_DESC_MAP = new HashMap<>();

    static {
        for (EstimateTypeEnum c : EstimateTypeEnum.values()) {
            CODE_TO_DESC_MAP.put(c.getCode(), c.getDesc());
        }
    }

    private final int code;

    private final String desc;

    EstimateTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return CODE_TO_DESC_MAP.get(code);
    }
}
