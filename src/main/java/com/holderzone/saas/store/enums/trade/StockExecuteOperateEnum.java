package com.holderzone.saas.store.enums.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存执行操作
 */
@Getter
@AllArgsConstructor
public enum StockExecuteOperateEnum {

    REDUCE("REDUCE", "扣减库存"),
    RETURN("RETURN", "退回"),
    ADJUST("ADJUST", "调整库存");

    /**
     * 操作代码
     */
    private final String code;

    /**
     * 操作描述
     */
    private final String desc;

    public static StockExecuteOperateEnum getByCode(String code) {
        for (StockExecuteOperateEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new BusinessException("not support operate:" + code);
    }

} 