package com.holderzone.saas.store.enums.trade;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/10/27
 * @description 退菜操作节点枚举
 */
@Getter
public enum OperatorNodeEnum {

    RECOVERY(1, "反结账"),

    CHECK_OUT(2, "已结账"),

    NOT_CHECK_OUT(3, "未结账"),

    NOT_OUT_DINNER(4, "未出餐"),
    ;

    private final int code;

    private final String desc;

    OperatorNodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
