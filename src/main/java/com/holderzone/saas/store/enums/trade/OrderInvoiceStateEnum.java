package com.holderzone.saas.store.enums.trade;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/10
 * @description 订单开票状态枚举
 */
public enum OrderInvoiceStateEnum {

    UNBOUND_STORE("-1", "未绑定门店"),

    BAIWANG_RETURN_ERROR("-2", "百旺返回错误或无数据"),

    SUCCEED("1", "开具成功"),

    FAILURE("2", "开具失败"),

    NOT_GENERATE("3", "已开具未生成PDF（税控电子票）"),

    DELETE("6", "单据删除"),

    CERTIFICATION_REQUIRED("1003", "需要认证"),

    NEED_TO_LOG_IN("1004", "需要登录"),
    ;

    private String code;
    private String desc;

    OrderInvoiceStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getName(String code) {
        for (OrderInvoiceStateEnum stateEnum : OrderInvoiceStateEnum.values()) {
            if (Objects.equals(stateEnum.getCode(), code)) {
                return stateEnum.getDesc();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
