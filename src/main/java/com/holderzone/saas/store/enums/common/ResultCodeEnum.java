package com.holderzone.saas.store.enums.common;


/**
 * <AUTHOR>
 * @version 1.0
 * @className ResultCodeEnum
 * @date 2018/09/04 17:52
 * @description 自定义返回code
 * @program holder-saas-store-trade
 */
public enum ResultCodeEnum {

    TABLE_BUSINESS_LOCK_EXCEPTION(10001, "开台自定义code"),
    BIND_UP_ACCOUNTS_EXCEPTION(10031, "请清空桌台"),
    /**
     * 2开头为trade自定义返回code
     */
    ITEM_ESTIMATE_EXCEPTION(20001, "估清失败code"),
    ORDER_VERSION_EXCEPTION(20010, "订单数据发生变化"),
    ORDER_PAY_LOCK_EXCEPTION(20011, "订单已被锁定"),
    RETURN_FEE_NOT_MATCHEXCEPTIION(20012, "退款价格不对"),

    ORDER_ITEM_TRANSFER_VERIFY_EXCEPTION(200021, "订单转菜校验异常"),
    ORDER_REFUND_LOCK_EXCEPTION(90001, "退款操作存在冲突，需重新提交"),

    ;

    private int code;
    private String desc;

    ResultCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (ResultCodeEnum c : ResultCodeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
