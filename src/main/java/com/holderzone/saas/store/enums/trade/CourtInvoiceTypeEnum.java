package com.holderzone.saas.store.enums.trade;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.ParameterException;

import java.util.List;

/**
 * <AUTHOR>
 * 票据类型
 */
public enum CourtInvoiceTypeEnum {

    SPECIAL_INVOICE_TYPE ("01", "数电专票传"),
    ORDINARY_INVOICE_TYPE("02", "数电普票传"),
    ;

    private String code;
    private String desc;


    public static List<String> listCode(){
        List<String> list = Lists.newArrayList();
        for (CourtInvoiceTypeEnum c : CourtInvoiceTypeEnum.values()) {
            list.add(c.getCode());
        }
        return list;
    }


    CourtInvoiceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (CourtInvoiceTypeEnum c : CourtInvoiceTypeEnum.values()) {
            if (c.getCode().equals(String.valueOf(code))) {
                return c.desc;
            }
        }
        throw new ParameterException("团购类型不匹配");
    }
    public static CourtInvoiceTypeEnum groupBuyType(int code) {
        for (CourtInvoiceTypeEnum c : CourtInvoiceTypeEnum.values()) {
            if (c.getCode().equals(String.valueOf(code)) ) {
                return c;
            }
        }
        throw new ParameterException("团购类型不匹配");
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
