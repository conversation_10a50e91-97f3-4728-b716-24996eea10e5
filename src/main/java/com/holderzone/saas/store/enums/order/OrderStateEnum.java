package com.holderzone.saas.store.enums.order;

import com.holderzone.saas.store.util.LocaleUtil;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * <AUTHOR>
 * @description 订单状态枚举（pad，微信） 0:待处理，1：接单，2：拒单，3：未结帐，4：已结账,5:已作废,8:订单已失效,9:已退菜
 * @date 2021/8/23 11:57
 */
public enum OrderStateEnum {

    PENDING(0, "待处理"),
    ACCEPT_ORDER(1, "接单"),
    REJECT_ORDER(2, "拒单"),
    UNCHECKED(3, "未结帐"),
    CHECKED_OUT(4, "已结账"),
    CANCELLATION(5, "已作废"),
    ORDER_HAS_EXPIRED(8, "订单已失效"),
    RETURN_DISHE(9, "已退菜"),
    ;

    private final int code;
    private final String desc;

    OrderStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    private final static String ORDER_STATE = "ORDER_STATE_";
    public static String getLocaleByDesc(String desc) {
        //若本地是简体中文直接返回
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return desc;
        }
        for (OrderStateEnum c : OrderStateEnum.values()) {
            if (c.getDesc().equals(desc)) {
                return LocaleUtil.getMessage(ORDER_STATE + c.name());
            }
        }
        return desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
