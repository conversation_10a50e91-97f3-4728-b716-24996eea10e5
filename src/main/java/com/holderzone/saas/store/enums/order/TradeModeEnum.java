package com.holderzone.saas.store.enums.order;

import com.holderzone.saas.store.util.LocaleUtil;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeModeEnum
 * @date 2018/09/04 17:52
 * @description 订单交易模式枚举
 * @program holder-saas-store-trade
 */
public enum TradeModeEnum {

    DINEIN(0, "正餐"),
    FAST(1, "快餐"),
    ;

    private int code;
    private String desc;

    TradeModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (TradeModeEnum c : TradeModeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    private final static String TRADE_MODE = "TRADE_MODE_";
    public static String getLocaleDesc(int code) {
        //若本地是简体中文直接返回
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return getDesc(code);
        }
        for (TradeModeEnum c : TradeModeEnum.values()) {
            if (c.getCode() == code) {
                return LocaleUtil.getMessage(TRADE_MODE + c.name());
            }
        }
        return TradeModeEnum.DINEIN.desc;
    }
    public static String getLocaleByDesc(String desc) {
        //若本地是简体中文直接返回
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return desc;
        }
        for (TradeModeEnum c : TradeModeEnum.values()) {
            if (c.getDesc().equals(desc)) {
                return LocaleUtil.getMessage(TRADE_MODE + c.name());
            }
        }
        return desc;
    }
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
