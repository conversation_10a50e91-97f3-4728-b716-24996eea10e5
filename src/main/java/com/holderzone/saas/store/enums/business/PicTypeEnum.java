package com.holderzone.saas.store.enums.business;

/**
 * <AUTHOR>
 * @date 2025/2/25
 * @description 图片类型
 */
public enum PicTypeEnum {

    PICTURE(1, "副屏图片"),

    ORDER_PICTURE(2, "副屏点餐图片"),

    DISPLAY(3, "副屏显示设置"),

    ;

    private int code;
    private String desc;

    PicTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getName(int code) {
        for (PicTypeEnum picTypeEnum : PicTypeEnum.values()) {
            if (picTypeEnum.getCode() == code) {
                return picTypeEnum.getDesc();
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
