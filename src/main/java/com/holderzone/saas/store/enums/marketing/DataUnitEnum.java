package com.holderzone.saas.store.enums.marketing;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2024/5/28
 * @description 日期单位枚举
 */
public enum DataUnitEnum {

    /**
     * 天
     */
    DAY(0, "天"),

    /**
     * 周
     */
    WEEK(1, "周"),

    /**
     * 月
     */
    MONTH(2, "月"),

    /**
     * 年
     */
    YEAR(3, "年"),

    /**
     * 累计
     */
    TOTAL(4, "累计"),

    /**
     * 始终统计
     */
    FOREVER(-1, "始终统计");

    /**
     * 编号
     */
    private int code;

    /**
     * 描述
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    DataUnitEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static String getNameByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (DataUnitEnum type : DataUnitEnum.values()) {
            if (type.code == code) {
                return type.getDes();
            }
        }
        return "";
    }

    public static DataUnitEnum Enum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (DataUnitEnum type : DataUnitEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}
