package com.holderzone.saas.store.enums.table;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableStatusChangeEnum
 * @date 2019/01/10 9:52
 * @description
 * @program holder-saas-store-dto
 */
public enum TableStatusChangeEnum {

    ORDER_DISH_REFUND_ALL(1, "菜品退完"),

    DISABLED(2, "作废"),

    CHECKOUT(3, "结账完成"),

    RE_CHECKOUT(4, "反结账"),

    NUMBER_OF_MEALS_CHANGE(5, "就餐人数修改"),

    ORDER_DISH_CHANGE(6, "订单菜品变化(加菜，退菜)"),

	CLOSE_WEIXIN_NOTIFY(66, "关台微信通知");

    private Integer id;

    private String msg;

    TableStatusChangeEnum(Integer id, String msg) {
        this.id = id;
        this.msg = msg;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    TableStatusChangeEnum() {
    }

    public static String getMsgById(Integer id) {
        return Arrays.stream(TableStatusChangeEnum.values())
                .filter(tableStatusChangeEnum -> Objects.equals(tableStatusChangeEnum.getId(), id))
                .findAny()
                .map(TableStatusChangeEnum::getMsg)
                .orElse(null);
    }

    public static TableStatusChangeEnum getById(Integer id) {
        for (TableStatusChangeEnum value : values()) {
            if (value.getId().equals(id)) {
                return value;
            }
        }
        return null;
    }
}
