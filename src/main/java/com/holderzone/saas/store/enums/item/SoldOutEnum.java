package com.holderzone.saas.store.enums.item;

/**
 * <AUTHOR>
 * @date 2024/10/30
 * @description 估清状态
 */
public enum SoldOutEnum {

    NO(1, "否"),

    YSE(2, "是"),
    ;

    private int code;
    private String desc;

    SoldOutEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getName(int code) {
        for (SoldOutEnum unitEnum : SoldOutEnum.values()) {
            if (unitEnum.getCode() == code) {
                return unitEnum.getDesc();
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
