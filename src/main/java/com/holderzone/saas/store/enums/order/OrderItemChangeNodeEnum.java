package com.holderzone.saas.store.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.logging.log4j.util.Strings;

/**
 * 换菜节点
 */
@Getter
@AllArgsConstructor
public enum OrderItemChangeNodeEnum {

    ADD_ITEM_BEFORE("ADD_ITEM_BEFORE", "下单前"),
    ADD_ITEM_AFTER("ADD_ITEM_AFTER", "下单后"),
    ;

    private final String node;

    private final String desc;


    public static String getDescByNode(String node) {
        for (OrderItemChangeNodeEnum value : values()) {
            if (value.getNode().equals(node.trim())) {
                return value.getDesc();
            }
        }
        return Strings.EMPTY;
    }
}
