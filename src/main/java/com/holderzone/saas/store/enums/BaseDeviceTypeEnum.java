package com.holderzone.saas.store.enums;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.util.LocaleUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.List;
import java.util.Locale;
import java.util.Objects;

/**
 * | Endpoint | BACKEND | SSO | Gateway |
 * | :----: | :----: |  :----: | :----: |
 * | 云端 | 99 | 99 | 99 |
 * | 商户端 | 0 | 0 | 0 |
 * | 一体机 | 3 | 3 | 3 |
 * | POS机 | 4 | 4 | 4 |
 * | 平板 | 5 | 5 | 5 |
 * | 点菜宝 | 6 | 6 | 6 |
 * | 刷卡机 | 7 | 7 | 7 |
 * | 微信（公众号） | 12 | 8/12 | 8 |
 * | KDS | 9 | 9 | 9 |
 * | BOSS（PHONE） | 10 | 10 | 10 |
 * | SELF | 11 | 11 | 11 |
 * | 微信（小程序） | 13 | 13 | 13 |
 * | TV | 14 | 14 | 14 |
 *
 * <AUTHOR>
 * @version 1.0
 * @className 设备类型枚举
 * @date 2018/09/04 17:52
 * @description
 * @program holder-saas-store-dto
 */
public enum BaseDeviceTypeEnum {
    MERCHANT(0, "PC服务端", ""),
    All_IN_ONE(3, "一体机", "T"),
    POS(4, "POS机", ""),
    CLOUD_PANEL(5, "云平板", ""),
    M1(6, "点菜宝(M1)", ""),
    PV1(7, "PV1(带刷卡的点菜宝)", ""),
    WECHAT_UNUSED(8, "微信（公众号）保留字，8和12都暂时给微信公众号用，因为Gateway在用8", ""),
    KDS(9, "厨房显示系统", ""),
    BOSS(10, "老板助手（手机端）", ""),
    SELF(11, "自助点餐机", ""),
    WECHAT(12, "微信（公众号）", ""),
    WECHAT_MINI(13, "微信（小程序）", ""),
    KQS(14, "厨房取餐屏（TV）", "kitchen queue screen"),
    TCD(15, "通吃岛", ""),
    YHTX(16, "翼惠天下", ""),
    ALI(17, "支付宝", ""),
    CLOUD(99, "云端", ""),
    ;

    private int code;
    private String desc;
    private String head;

    /**
     * 2c的设备
     */
    public static List<Integer> DEVICE_TYPE_2C = Lists.newArrayList(WECHAT_UNUSED.getCode(),
            WECHAT.getCode(),
            WECHAT_MINI.getCode(),
            TCD.getCode(),
            ALI.getCode());

    BaseDeviceTypeEnum(int code, String desc, String head) {
        this.code = code;
        this.desc = desc;
        this.head = head;
    }

    public static String getKey(int code) {
        for (BaseDeviceTypeEnum c : BaseDeviceTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.name();
            }
        }
        return null;
    }

    public static String getKey(String message) {
        for (BaseDeviceTypeEnum c : BaseDeviceTypeEnum.values()) {
            if (null!=message && c.getDesc().equals(message)) {
                return c.name();
            }
        }
        return message;
    }

    public static String getDesc(int code) {
        for (BaseDeviceTypeEnum c : BaseDeviceTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public static String getHead(int code) {
        for (BaseDeviceTypeEnum c : BaseDeviceTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.head;
            }
        }
        return null;
    }

    public static BaseDeviceTypeEnum getDeviceTypeByCode(int code) {
        for (BaseDeviceTypeEnum baseDeviceTypeEnum : BaseDeviceTypeEnum.values()) {
            if (baseDeviceTypeEnum.code == code) {
                return baseDeviceTypeEnum;
            }
        }
        throw new BusinessException("未找到匹配的设备：" + code);
    }
    public static String getLocaleDesc(String desc) {
        if(StringUtils.isEmpty(desc)){
            return desc;
        }
        //若本地是简体中文直接返回
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return desc;
        }
        for (BaseDeviceTypeEnum c : BaseDeviceTypeEnum.values()) {
            if (c.getDesc().equals(desc)) {
                return LocaleUtil.getMessage(c.name());
            }
        }
        return desc;
    }

    public static boolean isApplet(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        return TCD.getCode() == code || ALI.getCode() == code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }
}
