package com.holderzone.saas.store.enums.order;

/**
 * <AUTHOR>
 * @version 1.0
 * @className logOperationTypeEnum
 * @date 2018/10/09 11:09
 * @description //TODO
 * @program holder-saas-store-order
 */
//1-4外卖 5-8订单
public enum logOperationTypeEnum {

    外卖接单(1, "外卖接单"),
    外卖取消订单(2,"外卖取消订单"),
    外卖配送中(3,"外卖配送中"),
    外卖订单完成(4,"外卖订单完成"),
    去结账下单(5,"去结账下单"),
    去结账更新订单(6,"去结账更新订单"),
    取消订单(7,"取消订单"),
    反结账订单(8,"反结账订单"),
    结账成功(9,"结账成功"),
    退款成功(10,"退款成功"),

    外卖拒单(11,"外卖拒单"),
    ;

    private int code;
    private String desc;

    private logOperationTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (logOperationTypeEnum c : logOperationTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
