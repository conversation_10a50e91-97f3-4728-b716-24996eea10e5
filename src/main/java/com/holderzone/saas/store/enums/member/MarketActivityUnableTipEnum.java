package com.holderzone.saas.store.enums.member;

import com.holderzone.saas.store.constant.Constant;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 满减满折活动不可使用原因
 */
@Getter
@AllArgsConstructor
public enum MarketActivityUnableTipEnum {

    OTHER_DISCOUNT("该活动不能与其它优惠共享", Constant.OTHER_DISCOUNT_SHARE),
    UN_FULL_ITEM_CONDITION("当前订单菜品不在活动范围内", Constant.UN_FULL_CONDITION),
    UN_FULL_AMOUNT_CONDITION("当前订单参与活动的商品总价未达到活动门槛", Constant.UN_FULL_CONDITION),
    UN_FULL_OBJ_CONDITION("该活动仅会员可参加", Constant.UN_FULL_CONDITION),
    UN_FULL_CARD_CONDITION("该活动不支持当前会员卡", Constant.UN_FULL_CONDITION),
    UN_FULL_CARD_LEVEL_CONDITION("该活动不支持当前会员卡等级", Constant.UN_FULL_CONDITION),
    UN_FULL_TIME_CONDITION("该活动不在活动时间段", Constant.UN_FULL_CONDITION),
    UN_FULL_TERMINAL_CONDITION("该活动不支持当前终端", Constant.UN_FULL_CONDITION),
    ;

    private final String tips;

    private final String view;

    public static String getView(String tips) {
        for (MarketActivityUnableTipEnum tipEnum : values()) {
            if (tipEnum.getTips().equals(tips)) {
                return tipEnum.getView();
            }
        }
        return UN_FULL_ITEM_CONDITION.getView();
    }
}
