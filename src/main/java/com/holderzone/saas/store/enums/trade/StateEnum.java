package com.holderzone.saas.store.enums.trade;


/**
 * <p>
 * 如果有修改状态类型的定义，请修改BusinessOrderStatisticsQueryDTO中的定义，目前枚举类不在dto项目，考虑迁移影响大。请悉知。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @className StateEnum
 * @date 2018/09/04 17:52
 * @description 订单状态枚举
 * @program holder-saas-store-trade
 * @see com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsQueryDTO
 */
public enum StateEnum {

    READY(1, "待支付"),
    PENDING(2, "支付中"),
    FAILURE(3, "支付失败"),
    SUCCESS(4, "支付成功"),
    REFUNDED(5, "退款"),
    CANCEL(6, "已作废"),
    ANTI_SETTLEMENT(7, "反结账"),
    // 无商品作废订单
    INVALID(11, "已作废"),
    // 子单支付成功，待结账完成
    SUB_SUCCESS(12, "子单支付成功，待结账完成"),

    ;

    private int code;
    private String desc;

    StateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (StateEnum c : StateEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
