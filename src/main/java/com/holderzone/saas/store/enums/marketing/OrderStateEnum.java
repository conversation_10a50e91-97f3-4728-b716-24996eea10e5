package com.holderzone.saas.store.enums.marketing;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 活动订单状态
 */
@Getter
@AllArgsConstructor
public enum OrderStateEnum {

    /**
     * 已结账
     */
    FINISH(1, "已结账"),

    /**
     * 订单退款
     */
    REFUND(-1, "订单退款"),

    UNKNOWN(999, "未知"),
    ;


    private final int code;

    private final String des;

    public static String getByCode(int code) {
        for (OrderStateEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDes();
            }
        }
        return UNKNOWN.getDes();
    }

}
