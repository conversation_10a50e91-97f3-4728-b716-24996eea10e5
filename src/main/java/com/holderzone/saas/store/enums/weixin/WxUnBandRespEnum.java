package com.holderzone.saas.store.enums.weixin;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxUnBandRespEnum
 * @date 2019/03/07 17:28
 * @description 微信解绑结果枚举
 * @program holder-saas-store
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum WxUnBandRespEnum {
    SUCCESS(0, "微信公众号解绑成功"),
    CODE_TIME_OUT(1, "验证码已过期，请重新获取"),
    ERROR_CODE(2, "验证码输入错误，请重新输入"),
    SYSTEM_ERROR(3, "系统错误,请稍后重试");

    private Integer code;

    private String message;

    public static String getDescByCode(Integer code) {
        return Arrays.stream(WxUnBandRespEnum.values()).filter(p -> p.getCode().equals(code))
                .findFirst().orElseThrow(() -> new BusinessException("不存在当前code对应的desc")).getMessage();
    }
}
