package com.holderzone.saas.store.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SpecialProvinceEnum
 * @date 2019/05/20 17:15
 * @description 直辖市，特别行政区的code和name枚举
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum SpecialProvinceEnum {
    BEIJING("110000", "北京"),
    TIANJIN("120000", "天津"),
    SHANGHAI("310000", "上海"),
    CHONGQING("500000", "重庆"),
    MACAO("820000", "澳门"),
    HONGKONG("810000", "香港"),
    TAIWAN("710000", "台湾"),
    DEFAULT("999", "其他");

    private String code;

    private String name;

    public static SpecialProvinceEnum getByCode(String code) {
        return Arrays.stream(SpecialProvinceEnum.values()).filter(p -> p.getCode().equals(code))
                .findFirst().orElse(DEFAULT);
    }

    public static SpecialProvinceEnum getByName(String name) {
        return Arrays.stream(SpecialProvinceEnum.values()).filter(p -> name.contains(p.getName())).findFirst().orElse(DEFAULT);
    }

    public static List<String> getSpecialCodeList() {
        return Arrays.stream(SpecialProvinceEnum.values())
                .filter(p -> !SpecialProvinceEnum.DEFAULT.getCode().equals(p.getCode()))
                .map(SpecialProvinceEnum::getCode).collect(Collectors.toList());
    }

}
