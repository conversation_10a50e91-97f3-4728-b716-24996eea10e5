package com.holderzone.saas.store.enums.user;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum TakeawayItemMappingSourceEnum {

    QUERY_ITEM_MAPPING("takeaway_query", "查询商品"),
    BIND_ITEM_MAPPING("takeaway_bind", "关联商品"),
    UNBIND_ITEM_MAPPING("takeaway_unbind", "解除关联"),
    ;

    private final String sourceCode;

    private final String sourceName;

    TakeawayItemMappingSourceEnum(String sourceCode, String sourceName) {
        this.sourceCode = sourceCode;
        this.sourceName = sourceName;
    }

    public static TakeawayItemMappingSourceEnum getEnumBySourceCode(String sourceCode) {
        for (TakeawayItemMappingSourceEnum sourceEnum : values()) {
            if (Objects.equals(sourceEnum.getSourceCode(), sourceCode)) {
                return sourceEnum;
            }
        }
        return null;
    }
}
