package com.holderzone.saas.store.enums.user;


import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * 原来的：
 * 商户:0, PC平板:1, 小店通:2, 一体机:3, pos:4, pad:5, m1:6, p1/v1:7, cloud:99
 */
public enum LoginSourceEnum {

    WEB(0, "商户后端设备", "商户后台登录"),

    AIO(10, "台式收银设备(未集成打印)", "SunMi D1, SunMi D2"),

    AIO_P(11, "台式收银设备(已集成打印)", "SunMi T1, SunMi T1 mini, SunMi T2"),

    AIO_P_F(12, "台式收银设备(已集成打印和金融)", "暂无"),

    POS(20, "手持收银设备(未集成打印)", "SunMi M1, SunMi M2, SunMi L2"),

    POS_P(21, "手持收银设备(已集成打印)", "SunMi V1, SunMi V1s, SunMi V2"),

    POS_P_F(22, "手持收银设备(已集成打印和金融)", "SunMi P1, SunMi P2"),

    PAD(30, "平板收银设备", "XiaoMi Pad"),

    PHONE(40, "手持移动电话", "暂无");

    private int source;

    private String desc;

    private String model;

    LoginSourceEnum(int source, String desc, String model) {
        this.source = source;
        this.desc = desc;
        this.model = model;
    }

    public int getSource() {
        return source;
    }

    public String getDesc() {
        return desc;
    }

    public String getModel() {
        return model;
    }

    public static LoginSourceEnum ofSource(int source) {
        for (LoginSourceEnum value : LoginSourceEnum.values()) {
            if (value.getSource() == source) {
                return value;
            }
        }
        throw new BusinessException("未找到匹配的登录设备类型");
    }
}