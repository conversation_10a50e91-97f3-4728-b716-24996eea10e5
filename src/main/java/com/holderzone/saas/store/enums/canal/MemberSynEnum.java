package com.holderzone.saas.store.enums.canal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberSynEnum
 * @date 2019/06/27 17:47
 * @description //TODO member 同步类型
 * @program holder-saas-aggregation-app
 */
public enum MemberSynEnum {

    PRODUCT_SYNC("全量同步<save%update>商品"),

    MODIFY_PRODUCT_PART("修改商品部分信息"),

    DELETE_PRODUCT("删除商品"),

    MODIFY_PRODUCT_CLASSIFICATION_PART("修改商品下的分类信息数据"),

    MODIFY_PRODUCT_SPEC_PART("修改商品下的规格信息数据"),

    DELETE_PRODUCT_CLASSIFICATION_PART("删除商品下的分类信息数据"),

    DELETE_PRODUCT_SPEC_PART("删除商品下的规格信息数据"),

    DELETE_STORE("删除门店"),

    SYNC_STORE("全量同步门店"),

    DELETE_ENTERPRISE("删除企业"),

    SYNC_ENTERPRISE("全量同步企业"),

    DELETE_BRAND("删除品牌"),

    SYNC_BRAND("全量同步品牌");

    private String describe;

    MemberSynEnum(String describe) {
        this.describe = describe;
    }
}
