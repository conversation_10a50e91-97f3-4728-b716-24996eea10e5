package com.holderzone.saas.store.enums.locale;

import com.holderzone.saas.store.util.LocaleUtil;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * <AUTHOR>
 * @create 2023-07-14
 * @description
 */

public enum LocaleMessageEnum {

    UNKNOWN_EXCEPTION("未知异常"),
    PHONE_REPEAT("手机号重复，创建失败"),
    MANUAL_DATABASE_ERROR("手动切库异常，enterpriseGuid为空"),
    SYSTEM_ERROR("系统异常"),
    AMAP_API_CALL_ERROR("高德地图API调用异常"),
    STICKER_IMAGE_DOWNLOAD_EMPTY("桌贴图片下载失败，无对应背景图片，请重新创建桌贴"),
    TABLE_STICKER_NULL("桌贴参数为空"),
    REQUEST_CANNOT_RECOGNIZED("无法识别到当前请求类型"),
    QR_CODE_NO_PARAMETERS("当前二维码无对应参数，请重新下载二维码"),
    ENTERPRISEGUID_ERROR("enterpriseGuid错误"),
    TABLEGUIDS_NULL("tableGuids不能为空"),
    TABLEGUIDS_ERROR("tableGuids错误"),
    TRY_AGAIN_TEN_MINUTES("10分钟后再试"),
    ORDER_NOT_EXIST("订单数据不存在"),
    NOT_CHOSEN_DISH("您还没选择菜品，无法买单，请先加菜"),
    ORDER_PAYED("订单已支付，将返回点餐首页！"),
    MEMBER_UNAVAILABLE("会员不可用"),
    ORDER_ERROR("订单数据异常"),
    PAYED_RESERVE_TO_SETTLE("您已支付了一笔预定金，请至前台结账！若有预定金票据请携带~"),
    COUNTER_CHECKOUT_CANT_SETTLE("反结账订单无法结账，请联系商家"),
    ORDER_NOT_PROCESSED("有订单商家未处理，无法买单哦~"),
    ORDER_CHANGED("订单数据发生变动"),
    ABNORMAL_ORDER_NUMBER("订单号异常"),
    ORDER_NOT_FOUND("未查询到订单"),
    FAILED_ADD_ORDER_VERSION("订单增加版本号失败"),
    ORDER_NUMBER_CANNOT_EMPTY("订单号不能为空"),
    STATUS_NOT_SUPPORT_ADJUSTMENT("此订单当前状态不支持调整！"),
    THERE_ARE_ADJUSTED_ORDER_DETAILS("存在已调整的订单明细，请重新选择商品！"),
    TRANSACTION_MODE_CANNOT_EMPTY("交易模式不能为空"),
    ORDER_GUID_CANNOT_EMPTY("订单guid为空"),
    CHANGE_STATUS_EMPTY("更改状态为空"),
    QUERY_TIME_CANNOT_EMPTY("查询时间不能为空"),
    QUERY_TIME_RANGE_UP_TO_30_DAYS("查询时间范围最多只能30天"),
    NO_RECORDS_FOUND_FOR_THIS_ORDER("没有该订单的记录：<>"),
    FAILED_TO_RETRIEVE_BUSINESS_DAY_INTERVAL("获取营业日区间失败"),
    SERVICE_DOES_NOT_EXIST("服务不存在"),
    STORE_GUID_CANNOT_BE_EMPTY("门店Guid不能为空!"),
    ORDER_DETAILS_NOT_FOUND_FOR_ORDER_GUID("根据orderGuid:<>没查到订单详情"),
    TAKEOUT_ORDER_DOES_NOT_EXIST("外卖订单不存在！"),
    THIS_DATA_ALREADY_EXISTS("该条数据已经存在"),
    FAILED_DISTRIBUTED_LOCK_TAKEOUT("获取外卖订单分布式锁失败，稍后重试!"),
    NO_DOUYIN_STORES("查询抖音门店为空"),
    PLEASE_ENTER_VALID_DOUYIN_STOR("请输入正确的抖音门店ID"),
    DOUYIN_STORE_EMPTY("抖音门店为空"),
    STORE_ALREADY_BOUND_DOUYIN("此门店已绑定抖音门店"),
    VALIDATION_PARAMETERS_ARE_EMPTY("验券入参为空"),
    VALIDATION_FAILED_STORE_NOT_BOUND("验券失败：门店未绑定"),
    INVALID_DOUYIN_VOUCHER_CODE("抖音团购券码有误"),
    EXCEPTION_REQUESTING_DOUYIN("请求抖音服务异常"),
    VOUCHER_CODE_EMPTY("券码为空"),
    VERIFICATION_STORE_CANNOT_EMPTY("验券门店不能为空"),
    DOUYIN_STORE_NOT_BOUND("抖音门店未绑定"),
    INVALID_VOUCHER_VERIFICATION("撤销验券信息有误"),
    EMPTY_RESPONSE_DOUYIN_VOUCHER_VERIFICATION_CANCEL("抖音撤销验券返回为空"),
    FAILED_BIND_DOUYIN_STORE("绑定抖音门店失败"),
    BINDING_STORE_SERVICE_NOT_EXIST("不存在绑定门店服务"),
    FAILED_CANCEL_MEITUAN_VOUCHER("撤销美团券失败"),
    FAILED_INITIALIZE_AREA_AND_TABLE("初始化门店区域、桌台异常!"),
    TABLE_INFORMATION_NULL("桌台信息null"),
    ORDER_GUID_NOT_FOUND("未查询到订单guid"),
    OLD_TABLE_NULL("旧桌台信息null"),
    ORDER_QUERY_EMPTY("订单查询为空"),
    FAILED_TO_GENERATE_GUID("guid生成失败"),
    FAILED_GENERATE_BATCH_GUID("批量生成guid失败"),
    CANNOT_LOCK_MULTIPLE_TABLES_TIME("不能同时锁定桌台"),
    THIS_TABLE_EXISTS("当前区域下已存在该桌台"),
    FAILED_ADD_TABLE("新增桌台失败！"),
    FAILED_UPDATE_TABLE("更新桌台失败！"),
    QUANTITY_CANNOT_LESS_ZERO("桌台数量不能小于0"),
    MAXIMUM_QUANTITY_99("最多数量为99"),
    MAXIMUM_QUANTITY_999("最多数量为999"),
    MAXIMUM_QUANTITY_9999("最多数量为9999"),
    STORE_GUID_LIST_CANNOT_EMPTY("storeGuidList不能为空"),
    FAILED_GENERATE_GUID("生成Guid失败：<>"),
    ACCOUNT_NOT_SERVICE_ACTIVATED("该账号未开通服务，请联系售后人员。"),
    ACCOUNT_NOT_LOGIN_PERMISSIONS("该账号无登陆权限。"),
    ENTERPRISE_PRODUCT_NO_RESOURCES("当前企业产品没有资源"),
    NO_ENABLED_MENU_UNDER_THIS_TERMINAL("该终端下没有已启用的菜单"),
    ROLE_ALREADY_EXISTS("已存在该角色名称，请修改"),
    FAILED_GENERATE_ROLE_GUID("BatchIdGenerator生成角色guid失败"),
    CANNOT_DELETE_ROLE("角色下有账号不可删除"),
    AUTHORIZED_PERSON_GUID_CANNOT_EMPTY("被授权人guid不能为空"),
    AUTHORIZATION_CODE_CANNOT_EMPTY("授权码不能为空"),
    PERMISSION_TABLE_EMPTY("用户权限名称表查询为空"),
    DUPLICATE_AUTHORIZATION_CODE_ENTERPRISE("同一企业下授权号不可重复"),
    SOURCE_CODE_CANNOT_EMPTY("sourceCode不能为空"),
    ADMINISTRATORS_CANNOT_MODIFIED("管理员数据权限不允许修改"),
    ACCOUNT_EXISTS("账号已存在，创建失败"),
    DUPLICATE_PHONE_NUMBER_MODIFY("手机号重复，修改失败"),
    DUPLICATE_ACCOUNT_MODIFY("账号重复，修改失败"),
    ASSOCIATED_CANNOT_DELETED("该账号已产生关联数据，不能删除"),
    USE_CANNOT_DELETED("当前账号正在门店使用，不能删除"),
    INCORRECT_ORIGINAL_PASSWORD("原密码错误"),
    USER_NOT_FOUND("不存在该用户"),
    EMPLOYEE_NOT_FOUND("不存在该员工"),
    DEVICE_NOT_REGISTERED("设备未注册"),
    STORE_NOT_EXIST("不存在当前门店"),
    STORE_NOT_AVAILABLE("门店不可用，请联系管理员"),
    PRODUCT_NOT_AUTHORIZED_EXPIRED("产品未授权或已过期"),
    INCONSISTENT_PRODUCT_SERVICE_DATA("产品服务数据不一致"),
    NOT_HAVE_TERMINAL_PERMISSIONS("用户无当前终端权限"),
    NOT_HAVE_STORE_PERMISSIONS("用户无当前门店权限"),
    DEVICE_HAS_BEEN_UNBOUND("设备已解绑，请重新绑定门店"),
    FAILED_CALL_ENTERPRISE("调用查询企业接口失败"),
    GIFT_DISH_REPORT("赠菜报表"),
    EXPORT_GIFT_DISH_REPORT("赠菜报表导出"),
    ORDER_ITEM_TYPE("订单商品类型"),
    ORDER_ITEM_CATEGORY("订单商品分类"),
    RETURNED_ITEM_LIST("退货商品列表"),
    RETURNED_DISH_DETAIL_LIST("退菜明细列表"),
    EXPORT_RETURNED_DISH_DETAIL("导出退菜明细"),
    BUSY_WITH_BUSINESS("业务繁忙，请稍后再试"),
    THREAD_DOES_HAVE_ENTERPRISE_GUID("当前线程没有enterpriseGuid"),
    FAILED_RETRIEVE_STORE("未获取到门店信息"),
    DATA_QUERY_EXCEPTION("数据查询异常"),
    PARAMETERS_CANNOT_EMPTY("请求参数不能为空"),
    UNKNOWN_ENTERPRISE_ELSE("未知的企业"),
    THREAD_NOT_ENTERPRISE("当前线程没有企业ID"),
    PARAMETER_ERROR("参数错误"),
    UNKNOWN_ENTERPRISES("未知企业"),
    UNKNOWN_STORES("未知门店"),
    UNKNOWN_ENTERPRISE_INFORMATION("未知企业信息"),
    UNKNOWN_STORE_INFORMATION("未知门店信息"),
    UNKNOWN_ENTERPRISE("未知的企业信息"),
    DIVISION_ZERO_EXCEPTION("除零异常"),
    MD_5_NOT_AVAILABLE("没有md5这个算法！"),
    PLEASE_SELECT_STORE("请选择门店"),
    QUEUE_NOT_EXIST_EXCEEDS("队列不存在或者人数超出限制"),
    CUSTOMERS_LESS_MINIMUM("顾客人数小于桌台配置最少人数"),
    CUSTOMERS_EXCEEDS_MAXIMUM("顾客人数超出桌台配置最大人数"),
    SYSTEM_BUSY("系统繁忙请稍后重试"),
    RECORD_DOES_NOT_EXIST("记录不存在,不能叫号"),
    FAILED_TO_CALL_NUMBER("叫号失败请稍候重试"),
    NUMBER_SKIPPED_CUSTOMER_SEATED("已过号或已就餐,请切换导航刷新查看"),
    NOT_ALLOW_RECOVERY("该门店不允许恢复至队列"),
    PLEASE_SWITCH_NAVIGATION("已恢复至队列,请切换导航刷新查看"),
    DIRECT_NOT_ALLOWED("不允许直接就餐操作"),
    NO_BRAND("无品牌信息"),
    QUEUE_CODE_DUPLICATED("队列编码重复<>"),
    QUEUE_NOT_EXIST("队列不存在"),
    QUEUE_DELETED_REFRESH("该队列已删除，请刷新页面"),
    NOT_ALLOW_MANUAL_CLEARING_QUEUE("该门店不允许手动清空队列"),
    DUPLICATE_TABLE_GUID("重复的桌台guid"),
    COUNT_DEVICE_PARAMETER_ERROR("countDevice参数错误"),
    UNSUPPORTED_PAPER("不支持的纸宽类型"),
    NO_BACKUP_PRINTER("当前没有备份过打印机信息"),
    MAXIMUM_OF_THREE_TEMPLATES("最多可创建3个模板"),
    QUANTITIES_UNABLE_TO_PRINT("所有菜品数量均为0，无法打印，<>"),
    CURRENTLY_ONLY_SUPPORTS("目前仅支持前台、后厨、标签"),
    DISH_LIST_IS_EMPTY("菜品列表为空，无法打印"),
    PRINTED_ITEM_EMPTY("打印的商品信息为空"),
    NO_CONNECTED_PRINTING("未找到已连接的打印设备"),
    NO_HOST_POS_MACHINES("该门店下无主机一体机"),
    PRINTER_NOT_EXIST("打印机不存在，请重新选择打印机！"),
    PRINTER_IP_ALREADY_EXISTS("该门店已存在此名称或ip相同的打印机"),
    ALREADY_HAS_PRINTER("该设备已存在本地打印机"),
    M_LOCALE_1("正餐、快餐、外卖统计不能全为空"),
    M_LOCALE_2("不支持的票据类型：<>"),
    M_LOCALE_3("该模板不支持自定义"),
    M_LOCALE_4("不支持当前自定义元素"),
    M_LOCALE_5("菜品列表为空，无法打印后厨单"),
    M_LOCALE_6("recordGuid不得为空"),
    M_LOCALE_7("品牌名称重复"),
    M_LOCALE_8("BatchIdGenerator生成品牌guid失败"),
    M_LOCALE_9("该品牌下已存在门店，请清理后删除"),
    M_LOCALE_10("该品牌下已存在商品，请清理后删除"),
    M_LOCALE_11("更新时品牌guid不能为空"),
    M_LOCALE_12("销售模式不为空"),
    M_LOCALE_13("组织名称已存在，请修改"),
    M_LOCALE_14("组织上下级最大深度不能大于5"),
    M_LOCALE_15("组织名称重复"),
    M_LOCALE_16("企业下组织最大深度为5，请修改当前组织的下级后重试"),
    M_LOCALE_17("组织下存在帐号，不可删除"),
    M_LOCALE_18("组织存在下级组织，不可删除"),
    M_LOCALE_19("企业【<>】初始化组织/门店数据发生异常"),
    M_LOCALE_20("BatchIdGenerator生成设备门店绑定guid失败"),
    M_LOCALE_21("仅一体机支持排序功能"),
    M_LOCALE_22("无排序值最小的一体机设备"),
    M_LOCALE_23("设备解绑失败"),
    M_LOCALE_24("只有一体机可设置为主机"),
    M_LOCALE_25("传入的设备编号未找到设备"),
    M_LOCALE_26("用户自主点餐模式必须传绑定桌台号"),
    M_LOCALE_27("未查询到桌台信息"),
    M_LOCALE_28("<>桌台已经被绑定，请重新选择桌台"),
    M_LOCALE_29("门店guid不能为空"),
    M_LOCALE_30("桌台guid不能为空"),
    M_LOCALE_31("桌台guid列表不能为空"),
    M_LOCALE_32("设备Guid不能为空"),
    M_LOCALE_33("超时请重试"),
    M_LOCALE_34("品牌不存在"),
    M_LOCALE_35("门店名称已存在，请修改"),
    M_LOCALE_36("删除失败，门店开通服务未到期"),
    M_LOCALE_37("传入门店GUID为空！"),
    M_LOCALE_38("门店不存在"),
    M_LOCALE_39("不支持的类型，<>"),
    M_LOCALE_40("不支持的厨房商品状态：<>"),
    M_LOCALE_41("不支持的交易模式：code=<>"),
    M_LOCALE_42("不支持的交易模式：displayType=<>"),
    M_LOCALE_43("不支持的延时等级：level=<>"),
    M_LOCALE_44("不支持的PointMode：<>"),
    M_LOCALE_45("不支持的Status：<>"),
    M_LOCALE_46("设备不存在"),
    M_LOCALE_47("一台设备只能绑定一台打印机，请解绑原打印机后重试！"),
    M_LOCALE_48("设备名称[<>]重复"),
    M_LOCALE_49("规则类型不能为空"),
    M_LOCALE_50("规则guid不能为空"),
    M_LOCALE_51("品牌guid不能为空"),
    M_LOCALE_52("门店{<>}已经被使用"),
    M_LOCALE_53("商品{<>}已经被使用"),
    M_LOCALE_54("显示状态不能为空"),
    M_LOCALE_55("延迟显示时延迟时间不能为空"),
    M_LOCALE_56("分批显示时批次不能为空"),
    M_LOCALE_57("生效状态不能为空"),
    M_LOCALE_58("商品列表不能为空"),
    M_LOCALE_59("门店列表不能为空"),
    M_LOCALE_60("部分区域已被绑定，请重新选择！"),
    M_LOCALE_61("菜品数量为0"),
    M_LOCALE_62("商品数量大于可退数量"),
    M_LOCALE_63("部分商品已被绑定"),
    M_LOCALE_64("最多创建4个堂口"),
    M_LOCALE_65("堂口名称[<>]重复"),
    M_LOCALE_66("堂口不存在"),
    M_LOCALE_67("该门店已存在此名称相同的打印机"),
    M_LOCALE_68("优惠券加券数量不能小于1"),
    M_LOCALE_69("头部信息不能为空！"),
    M_LOCALE_70("头部信息解析错误！"),
    M_LOCALE_71("运营主体不能为空！"),
    M_LOCALE_72("会员体系已被禁用,如有疑问请联系客服人员！"),
    M_LOCALE_73("运营主体不存在！"),
    M_LOCALE_74("超时请重试！"),
    M_LOCALE_75("优惠券信息不存在"),
    M_LOCALE_76("修改优惠券，券Guid必须填写"),
    M_LOCALE_77("需要修改的优惠券不存在"),
    M_LOCALE_78("优惠券数量不能小于已销售的券数量！"),
    M_LOCALE_79("优惠券列表查询需要带上状态参数"),
    M_LOCALE_80("输入的是错误的券码，请检查"),
    M_LOCALE_81("券码已过期"),
    M_LOCALE_82("券码已使用"),
    M_LOCALE_83("券数量不足"),
    M_LOCALE_84("券信息查询失败"),
    M_LOCALE_85("优惠券数量超过每单限用数量"),
    M_LOCALE_86("满多少可用必须填写"),
    M_LOCALE_87("满多少可用必须大于0"),
    M_LOCALE_88("请选择开始时间和结束时间"),
    M_LOCALE_89("请选择对应的生效时间和截止时间"),
    M_LOCALE_90("请选择对应的生效时间和有效期天数"),
    M_LOCALE_91("满多少可用必须大于等于0"),
    M_LOCALE_92("不存在当前活动类型"),
    M_LOCALE_93("当前页不能小于1"),
    M_LOCALE_94("参数guid有误"),
    M_LOCALE_95("已删除不能编辑"),
    M_LOCALE_96("参数guid有误"),
    M_LOCALE_97("只有在未过期的情况下，才能使用停止发送,请刷新查看状态"),
    M_LOCALE_98("只有在停止发送的情况下，才能使用继续发送,请刷新查看状态"),
    M_LOCALE_99("只有在未过期或者停止发送的情况下，才能使用作废,请刷新查看状态"),
    M_LOCALE_100("删除条件:除了状态为【未过期，停止发送】并且已发送给用户之后的其他情况才能删除"),
    M_LOCALE_101("优惠券加券数量不能超过999999"),
    M_LOCALE_102("该优惠券未不限量优惠券,不可加券"),
    M_LOCALE_103("该优惠券未已过期,不可加券"),
    M_LOCALE_104("该优惠券加券后数量不能超过999999"),
    M_LOCALE_105("未注册"),
    M_LOCALE_106("会员账号已被禁用，请联系管理员"),
    M_LOCALE_107("该会员的运营主体发生变更，无法反结账！"),
    M_LOCALE_108("会员二维码已过期"),
    M_LOCALE_109("手机号已存在"),
    M_LOCALE_110("会员已开通主卡"),
    M_LOCALE_111("未配置权益卡规格"),
    M_LOCALE_112("卡规则配置不允许此通道开通权益卡"),
    M_LOCALE_113("已达到发卡数量限制"),
    M_LOCALE_114("会员卡不存在"),
    M_LOCALE_115("可用积分为不足"),
    M_LOCALE_116("撤销的订单不存在"),
    M_LOCALE_117("手机号码格式错误"),
    M_LOCALE_118("会员卡余额不足"),
    M_LOCALE_119("此卡不是权益卡"),
    M_LOCALE_120("支付密码不能为空"),
    M_LOCALE_121("支付密码不正确，请重新输入"),
    M_LOCALE_122("会员不存在"),
    M_LOCALE_123("会员卡状态异常"),
    M_LOCALE_124("会员卡已被冻结"),
    M_LOCALE_125("该会员卡已禁用"),
    M_LOCALE_126("没有标签"),
    M_LOCALE_127("请配置主卡信息"),
    M_LOCALE_128("请配置主卡等级信息"),
    M_LOCALE_129("只能开通会员卡"),
    M_LOCALE_130("默认会员等级存在多个，请检查卡等级配置"),
    M_LOCALE_131("未配置充值规则"),
    M_LOCALE_132("未开启充值功能"),
    M_LOCALE_133("未查询到卡信息"),
    M_LOCALE_134("未查询到权益卡信息"),
    M_LOCALE_135("权益卡未开启充值功能"),
    M_LOCALE_136("权益卡充值金额错误"),
    M_LOCALE_137("会员卡不存在,请先设置会员卡"),
    M_LOCALE_138("卡已存在,请勿重复开卡"),
    M_LOCALE_139("当前卡已停止发放"),
    M_LOCALE_140("会员卡充值失败"),
    M_LOCALE_141("已超过充值上限"),
    M_LOCALE_142("当前用户不存在食堂卡"),
    INSUFFICIENT_BALANCE("余额不足，请充值"),
    ERROR_CANTEEN_CARD_RECHARGE("食堂卡充值失败"),
    ERROR_STORE_OPEN_CARD("当前门店无权限开通此卡"),
    ERROR_VOLUME_CANT_USE_IN_BANQUET("宴会套餐暂不能使用优惠券"),
    ERROR_VOLUME_IN_USE("优惠券正在使用中"),
    ERROR_VOLUME_ONLY_USE_ONE_TYPE("只能使用代金券或商品券的一种"),
    ERROR_VOLUME_EXCEED_NUM_LIMIT("超过该优惠券的使用张数上限"),
    ERROR_VOLUME_CANT_USE_TOGETHER("选择的优惠券不能同时使用"),
    ERROR_VOLUME_NOT_ACHIEVE_THRESHOLD("未达到优惠券使用门槛"),
    ERROR_VOLUME_NOT_SUPPORT_STORE("该券码不支持本门店使用"),
    ERROR_VOLUME_NOT_SAME_MEMBER("优惠券只能本人使用"),
    ERROR_VOLUME_MEMBER_DISABLE("优惠券所属会员被禁用"),
    ERROR_VOLUME_NOT_VALID("优惠券未生效"),
    ERROR_VOLUME_USED("优惠劵已被使用"),
    ERROR_VOLUME_VOID("优惠劵已作废"),
    ERROR_VOLUME_OVERDUE("优惠劵已过期"),
    ERROR_VOLUME_CANT_MEMBER_PRICE("该优惠券不能与会员价同享"),
    ERROR_VOLUME_EXCEED_PRODUCT_NUM("选择的券超过可抵扣商品数量"),
    ERROR_VOLUME_NOT_SUPPORT_PRODUCT("没有可使用优惠券的菜品"),
    ERROR_VOLUME_NOT_SUPPORT_GIFT("赠品菜不在优惠劵使用范围内"),
    ERROR_VOLUME_CODE_NOT_EXSITS("券码不存在"),
    ERROR_VOLUME_NOT_BELONG_ONE_PERSON("券码必须使用同一个会员"),
    ERROR_MEMBER_PASSWORD_EMPTY("支付密码不能为空"),
    ERROR_MEMBER_PASSWORD_INCORRECT("支付密码不正确"),
    ERROR_INTEGRAL_DEDUCTION_DISABLE("未启用积分抵扣"),
    ERROR_NOT_CONDUCT_ACTIVITIES("没有进行中的活动"),
    ERROR_NOT_STORE_CONDUCT_ACTIVITIES("当前门店没有进行中的活动"),
    LOGIN_INPUT_PARAMETER_IS_EMPTY("登录入参为空"),
    M_LOCALE_143("缺少需要被锁定的桌台信息"),
    M_LOCALE_144("该桌台正在被占用"),
    M_LOCALE_145("该桌台已被锁定"),
    M_LOCALE_146("更新会员信息不能为空"),
    M_LOCALE_147("手机号或卡号不能为空"),
    M_LOCALE_148("会员被禁用"),
    M_LOCALE_149("无此会员，请重新输入"),
    M_LOCALE_150("文件格式必须为jpg/png/bmp/jpeg/gif格式！！！"),
    M_LOCALE_151("请上传正确的图片"),
    M_LOCALE_152("文件大小超过限制"),
    M_LOCALE_153("单据guid不能为空"),
    M_LOCALE_155("未知的扫码类型"),
    M_LOCALE_156("图片Url不得为空"),
    M_LOCALE_157("图片读取出错"),
    M_LOCALE_158("文件大小不得超过"),
    M_LOCALE_159("参数有误"),
    M_LOCALE_160("查询消息count异常!!"),
    M_LOCALE_161("推送消息至安卓失败"),
    M_LOCALE_162("查询消息详情异常!!"),
    M_LOCALE_163("查询消息info异常!!"),
    M_LOCALE_164("查询门店信息"),
    M_LOCALE_165("失败!!"),
    M_LOCALE_166("保存扎帐信息"),
    M_LOCALE_167("获取门店的最新扎帐时间"),
    M_LOCALE_168("提示mq信息，能够开台操作"),
    M_LOCALE_169("聚合支付查询接口异常!!"),
    M_LOCALE_170("查询所有支付方式异常"),
    M_LOCALE_171("删除支付方式异常"),
    M_LOCALE_172("支付方式排序异常"),
    M_LOCALE_173("修改支付方式异常"),
    M_LOCALE_174("新增支付方式异常"),
    M_LOCALE_175("新增系统折扣异常"),
    M_LOCALE_176("修改系统折扣异常"),
    M_LOCALE_177("删除系统折扣异常"),
    M_LOCALE_178("查询所有系统折扣异常"),
    M_LOCALE_179("通过消费记录guid查询优惠信息"),
    M_LOCALE_180("获取会员消费日报调用异常"),
    M_LOCALE_181("获取会员充值日报调用异常"),
    M_LOCALE_182("权益卡充值异常"),
    M_LOCALE_183("查询失败"),
    M_LOCALE_184("在某门店查找会员及卡信息失败"),
    M_LOCALE_185("根据会员信息guid和门店查询会员基本信息和卡开通情况失败"),
    M_LOCALE_186("发送修改密码的验证码(忘记原密码)失败"),
    M_LOCALE_187("修改支付密码成功"),
    M_LOCALE_188("密码校验"),
    M_LOCALE_189("修改会员基本信息失败"),
    M_LOCALE_190("会员登录（手机号和密码）失败"),
    M_LOCALE_191("优惠劵的确认消费"),
    M_LOCALE_192("取消优惠劵的使用"),
    M_LOCALE_193("优惠劵的验证"),
    M_LOCALE_194("一体机预点餐菜谱验证异常"),
    M_LOCALE_195("商品安卓端同步异常"),
    M_LOCALE_196("获取打印商品列表异常"),
    M_LOCALE_197("安卓同步查询门店sku估清列表异常"),
    M_LOCALE_198("安卓配置商品估清估清列表异常"),
    M_LOCALE_199("安卓同步获取近三天销售模板执行时间列表异常"),
    M_LOCALE_200("支付失败!"),
    M_LOCALE_201("清除排队信息异常!!"),
    M_LOCALE_202("删除挂起订单失败!"),
    M_LOCALE_203("订单列表失败!"),
    M_LOCALE_204("分页历史记录异常!!"),
    M_LOCALE_205("获取订单详情失败!"),
    M_LOCALE_206("查询队列对应桌台异常!!"),
    M_LOCALE_207("聚合支付查询支付状态异常"),
    M_LOCALE_208("过号异常!!"),
    M_LOCALE_209("查询历史记录异常!!"),
    M_LOCALE_210("聚合支付轮询异常"),
    M_LOCALE_211("聚合支付回调失败!"),
    M_LOCALE_212("就餐并开台异常!!"),
    M_LOCALE_213("退货失败!"),
    M_LOCALE_214("赠送失败!"),
    M_LOCALE_215("恢复异常!!"),
    M_LOCALE_216("退款失败!"),
    M_LOCALE_217("退单失败!"),
    M_LOCALE_218("校验退款失败!"),
    M_LOCALE_219("确认就餐异常!!"),
    M_LOCALE_220("调用交易中心异常"),
    M_LOCALE_221("入队异常!!"),
    M_LOCALE_222("查询主题失败"),
    M_LOCALE_223("添加商品失败!"),
    M_LOCALE_224("打印结账单失败!"),
    M_LOCALE_225("批量取消赠送失败!"),
    M_LOCALE_226("叫号异常!!"),
    M_LOCALE_227("计算金额失败!"),
    M_LOCALE_228("聚合支付失败!"),
    M_LOCALE_229("扎帐保存错误"),
    M_LOCALE_230("查询排队信息异常!!"),
    M_LOCALE_231("挂起订单失败!"),
    M_LOCALE_232("查询队列桌台信息异常!!"),
    M_LOCALE_233("聚合支付撤销异常"),
    M_LOCALE_234("更新整单备注失败!"),
    M_LOCALE_235("挂起订单列表失败!"),
    M_LOCALE_236("验证失败"),
    M_LOCALE_237("查询桌台列表异常"),
    M_LOCALE_238("查询桌台区域列表异常"),
    M_LOCALE_239("查询桌台基础数据异常"),
    M_LOCALE_240("新增调整单失败"),
    M_LOCALE_241("轮询查询自动接单漏单订单"),
    M_LOCALE_242("反结账失败!"),
    M_LOCALE_243("调整订单商品失败!"),
    M_LOCALE_244("附加费列表失败!"),
    M_LOCALE_245("撤销聚合支付失败!"),
    M_LOCALE_246("获取订单桌位信息失败!"),
    M_LOCALE_247("更新就餐人数失败!"),
    M_LOCALE_248("作废订单失败!"),
    M_LOCALE_249("桌台旁边获取订单详情详情失败!"),
    M_LOCALE_250("打印预结单失败!"),
    M_LOCALE_251("打印菜品清单失败!"),
    M_LOCALE_252("打印结帐单失败!"),
    M_LOCALE_253("人脸支付补偿失败!"),
    M_LOCALE_254("人脸支付估清失败!"),
    M_LOCALE_255("人脸支付退估清失败!"),
    M_LOCALE_256("检查订单锁失败!"),
    M_LOCALE_257("增加版本号失败!"),
    M_LOCALE_258("获取版本号失败!"),
    M_LOCALE_259("订单加锁失败!"),
    M_LOCALE_260("订单解锁失败!"),
    M_LOCALE_261("离线订单上传"),
    M_LOCALE_262("本地化数据拉取失败!"),
    M_LOCALE_263("本地化数据保存!"),
    M_LOCALE_264("本地化单条数据拉取失败!"),
    M_LOCALE_265("校验服务是否可用失败!"),
    M_LOCALE_266("会员优惠券列表失败!"),
    M_LOCALE_267("创建调整单失败"),
    M_LOCALE_268("查询门店最早订单时间!!"),
    M_LOCALE_269("查询数量失败!!"),
    M_LOCALE_270("取单失败!"),
    M_LOCALE_271("挂单列表失败!"),
    M_LOCALE_272("挂起订单失败失败!"),
    M_LOCALE_273("通吃岛订单落库失败!"),
    M_LOCALE_274("快餐下单失败!"),
    M_LOCALE_275("撤销验券失败!"),
    M_LOCALE_276("验券失败!"),
    M_LOCALE_277("查询已验券列表失败!"),
    M_LOCALE_278("修改价格失败!"),
    M_LOCALE_279("叫起失败!"),
    M_LOCALE_280("催菜失败!"),
    M_LOCALE_281("划菜失败!"),
    M_LOCALE_282("取消赠送失败!"),
    M_LOCALE_283("正餐加菜失败!"),
    M_LOCALE_284("清台失败!"),
    M_LOCALE_285("支付链接已失效"),
    M_LOCALE_286("当前订单状态无法支付"),
    M_LOCALE_287("支付信息为空或已完成"),
    M_LOCALE_288("支付金额为0，请到前台结算"),
    M_LOCALE_289("开台失败!"),
    M_LOCALE_290("无匹配的日报类型"),
    M_LOCALE_291("图片上传失败，请稍后重试"),
    M_LOCALE_292("商品所属门店不能为空"),
    M_LOCALE_293("企业guid为空"),
    M_LOCALE_294("企业经营模式不支持"),
    COUPON_CODE_VOUCHER_INVALID("验券失败：代金券请在结账时进行验证"),
    ACTIVITY_WRONG("套餐活动未关联商品无法选择"),
    COUPON_UN_BIND_STORE("请在管理后台绑定团购平台门店"),
    COUPON_CODE_INVALID("验券失败：券码无效"),
    IS_THIRD_SHARE_TIPS("验券失败：不可与其他团购活动叠加"),
    WRONG_LOGIN_METHOD("登录方式错误"),
    WECHAT_AUTHORIZATION_CODE_IS_EMPTY("微信授权码为空"),
    OPERATION_SUBJECT_GUID_IS_EMPTY("运营主体guid为空"),
    GUESTS_QUANTITY_IS_EMPTY("就餐人数为空"),
    ORDER_GUID_IS_EMPTY("订单Guid为空"),
    M_LOCALE_295("请求参数为空"),
    M_LOCALE_296("精准推送功能企业按批次撤销赠送的优惠券-失败"),
    M_LOCALE_297("会员禁用失败！请重试！"),
    M_LOCALE_298("会员删除失败！请重试！"),
    M_LOCALE_299("会员增加失败！请重试！"),
    M_LOCALE_300("可导入商品为空"),
    M_LOCALE_301("门店唯一标识不能为空"),
    M_LOCALE_302("商户后台HEADER不可能有STOREGUID，请联系后台相关开发人员解决，谢谢。"),
    M_LOCALE_303("导入组织机构不能为空"),
    M_LOCALE_304("盘点记录Guid不得为空"),
    M_LOCALE_305("为找到对应的盘点单据信息"),
    M_LOCALE_306("未查询到品牌！"),
    M_LOCALE_307("请使用导入模板,且勿修改表头"),
    M_LOCALE_308("请先创建门店"),
    M_LOCALE_309("导入0个商品"),
    M_LOCALE_310("可解析商品数为：0个商品，请检查"),
    M_LOCALE_311("该账号所在商户未绑定门店信息！"),
    M_LOCALE_312("不是有效的Excel文件"),
    M_LOCALE_313("获取文件失败"),
    M_LOCALE_314("文件路径不能为空"),
    M_LOCALE_315("该企业未激活门店，请联系售后人员。"),
    M_LOCALE_316("文件大小不得超过<>"),
    M_LOCALE_317("使用门店选择了部分后,需选择对应的门店"),
    M_LOCALE_318("权益体系的guid不能为null"),
    M_LOCALE_319("月份为2月时,天数最多为28"),
    M_LOCALE_320("天数在1~30之间"),
    M_LOCALE_321("有效期的年参数不合法"),
    M_LOCALE_322("有效期的月份应在1~12"),
    M_LOCALE_323("最高获取积分必须大于0"),
    M_LOCALE_324("最高可抵扣积分必须大于0"),
    M_LOCALE_325("权益体系guid不能为null"),
    M_LOCALE_326("提醒方式已勾选后，对应的信息也要填写"),
    M_LOCALE_327("最多只支持2万条数据导出"),
    M_LOCALE_328("当前门店暂未绑定品牌"),
    M_LOCALE_329("云呼预订电话号码已被其他商户使用！"),
    M_LOCALE_330("商户不存在"),
    M_LOCALE_331("商户不允许预定"),
    M_LOCALE_332("没有匹配的预定时间段"),
    M_LOCALE_333("没有可用桌位"),
    M_LOCALE_334("没有区域"),
    M_LOCALE_335("获取验证码失败"),
    M_LOCALE_336("查询门店选项失败"),
    M_LOCALE_337("根据分页进行门店查询"),
    M_LOCALE_338("根据查询条件进行门店列表查询"),
    M_LOCALE_339("批量同步门店失败"),
    M_LOCALE_340("同步门店失败"),
    M_LOCALE_341("保存门店失败"),
    M_LOCALE_342("修改门店失败"),
    M_LOCALE_343("删除门店失败"),
    M_LOCALE_344("登出失败"),
    M_LOCALE_345("查询企业信息失败"),
    M_LOCALE_346("登陆后查询菜单列表失败"),
    M_LOCALE_347("数据传输错误，请稍后重试"),
    M_LOCALE_348("查询报表-订单详情发生异常"),
    M_LOCALE_349("查询报表-门店汇总信息发生异常"),
    M_LOCALE_350("查询报表-营业概况-营业数据信息发生异常"),
    M_LOCALE_351("查询报表-营业概况-历史趋势信息发生异常"),
    M_LOCALE_352("报表导出获取数据发生异常"),
    M_LOCALE_353("支付统计报表导出获取数据发生异常"),
    M_LOCALE_354("查询大屏营业额发生异常"),
    M_LOCALE_355("查询商品/分类销售额发生异常"),
    M_LOCALE_356("查询门店销售排行发生异常"),
    M_LOCALE_357("查询时间点销售额发生异常"),
    M_LOCALE_358("商超-营业概况数据查询异常"),
    M_LOCALE_359("商超-历史趋势查询异常"),
    M_LOCALE_360("商超-支付统计查询异常"),
    M_LOCALE_361("支付流水"),
    M_LOCALE_362("销售明细导出异常"),
    M_LOCALE_363("销售明细查询异常"),
    M_LOCALE_364("门店收款方式查询异常"),
    M_LOCALE_365("查询订单明细数据异常"),
    M_LOCALE_366("获取url异常"),
    M_LOCALE_367("加载文件发生异常"),
    M_LOCALE_368("查询文件发生异常"),
    M_LOCALE_369("批量创建文件发生异常"),
    M_LOCALE_370("创建文件发生异常"),
    M_LOCALE_371("删除文件发生异常"),
    M_LOCALE_372("移动文件发生异常"),
    M_LOCALE_373("重命名文件发生异常"),
    M_LOCALE_374("查询充值选项优惠劵信息失败"),
    M_LOCALE_375("查询优惠劵信息失败"),
    M_LOCALE_376("查询商品券信息失败"),
    M_LOCALE_377("查询优惠劵列表信息失败"),
    M_LOCALE_378("优惠劵保存失败"),
    M_LOCALE_379("商品劵保存失败"),
    M_LOCALE_380("优惠劵更新失败"),
    M_LOCALE_381("商品劵更新失败"),
    M_LOCALE_382("优惠劵操作失败"),
    M_LOCALE_383("优惠劵加券失败"),
    M_LOCALE_384("查询优惠劵详情失败"),
    M_LOCALE_385("查询第三类有效期的卡券"),
    M_LOCALE_386("查询优惠劵核销明细失败"),
    M_LOCALE_387("查询优惠劵核销明细金额合计失败"),
    M_LOCALE_388("查询优惠劵发放明细失败"),
    M_LOCALE_389("同步品牌数据失败"),
    M_LOCALE_390("通过企业Guid获取品牌列表失败"),
    M_LOCALE_391("不要参数获取品牌列表失败"),
    M_LOCALE_392("删除品牌失败"),
    M_LOCALE_393("保存品牌数据失败"),
    M_LOCALE_394("修改品牌数据失败"),
    M_LOCALE_395("同步企业数据失败"),
    M_LOCALE_396("同步企业列表数据失败"),
    M_LOCALE_397("保存企业数据失败"),
    M_LOCALE_398("修改企业数据失败"),
    M_LOCALE_399("删除企业数据失败"),
    M_LOCALE_400("根据登录用户的企业Guid查询是否关联体系"),
    M_LOCALE_401("同步商品数据失败"),
    M_LOCALE_402("查询商品数据失败"),
    M_LOCALE_403("删除商品数据失败"),
    M_LOCALE_404("保存商品数据失败"),
    M_LOCALE_405("修改商品数据失败"),
    M_LOCALE_406("门店分页查询失败"),
    M_LOCALE_407("门店列表查询失败"),
    M_LOCALE_408("根据品牌GUID进行列表查询失败"),
    M_LOCALE_409("同步门店列表数据失败"),
    M_LOCALE_410("同步门店数据失败"),
    M_LOCALE_411("保存门店数据失败"),
    M_LOCALE_412("修改门店数据失败"),
    M_LOCALE_413("删除门店数据失败"),
    M_LOCALE_414("获取会员详情失败"),
    M_LOCALE_415("更新会员渠道失败"),
    M_LOCALE_416("获取会员渠道失败"),
    M_LOCALE_417("查询体系下卡等级失败"),
    M_LOCALE_418("新增或更新会员等级失败"),
    M_LOCALE_419("删除会员等级失败"),
    M_LOCALE_420("复制会员权益失败"),
    M_LOCALE_421("查找权益失败"),
    M_LOCALE_422("获取会员等级"),
    M_LOCALE_423("删除体系"),
    M_LOCALE_424("保存/更新升级权益失败"),
    M_LOCALE_425("保存/更新折扣权益失败"),
    M_LOCALE_426("保存/更新生日权益失败"),
    M_LOCALE_427("更新成长值取值的勾选项失败"),
    M_LOCALE_428("更新会员卡的信息失败"),
    M_LOCALE_429("获取折扣权益失败"),
    M_LOCALE_430("获取升级权益失败"),
    M_LOCALE_431("获取生日权益失败"),
    M_LOCALE_432("获取列表"),
    M_LOCALE_433("删除优惠劵失败"),
    M_LOCALE_434("充值设置失败"),
    M_LOCALE_435("充值更新失败"),
    M_LOCALE_436("查询选择的门店信息失败"),
    M_LOCALE_437("成长值设置失败"),
    M_LOCALE_438("成长值更新失败"),
    M_LOCALE_439("创建品牌失败"),
    M_LOCALE_440("查询门店失败"),
    M_LOCALE_441("查询报表支付方式异常"),
    M_LOCALE_442("创建标签异常"),
    M_LOCALE_443("外卖订单基于给定时间段统计各个指标接口"),
    M_LOCALE_444("更新标签异常"),
    M_LOCALE_445("删除标签异常"),
    M_LOCALE_446("删除桌台异常"),
    M_LOCALE_447("查询标签列表异常"),
    M_LOCALE_448("导出报表异常！"),
    M_LOCALE_449("复制角色失败"),
    M_LOCALE_450("查询所有终端失败"),
    M_LOCALE_451("查询角色权限失败"),
    M_LOCALE_452("保存角色权限失败"),
    M_LOCALE_453("微信桌贴/模板查询出现异常"),
    M_LOCALE_454("创建微信桌贴时出现异常"),
    M_LOCALE_455("调用查询桌贴接口出现异常"),
    M_LOCALE_456("调用更新桌贴接口出现异常"),
    M_LOCALE_457("调用查询模板库接口出现异常"),
    M_LOCALE_458("调用查询模板分类接口出现异常"),
    M_LOCALE_459("调用桌贴下载接口出现异常"),
    M_LOCALE_460("调用桌贴创建接口出现异常"),
    M_LOCALE_461("调用桌贴删除接口出现异常"),
    M_LOCALE_462("前台数据源<>解析出错"),
    M_LOCALE_463("上传文件格式不正确"),
    M_LOCALE_464("第<>行的单位非法"),
    M_LOCALE_465("第<>行物料名称非法"),
    M_LOCALE_466("第<>行类型非法"),
    M_LOCALE_467("第<>行单位不能为空"),
    M_LOCALE_468("第<>行规格信息不能超过10个长度"),
    M_LOCALE_469("第<>行条码信息不符合规范"),
    M_LOCALE_470("第<>行物料信息重复:<>"),
    M_LOCALE_471("权益卡积分与成长值已汇总至会员主卡"),
    M_LOCALE_472("当前线程没有用户信息"),
    M_LOCALE_473("无法获取当前会话门店id"),
    M_LOCALE_474("商品Guid不能为空"),
    M_LOCALE_475("当前门店暂未开启预订"),
    M_LOCALE_476("异常"),
    M_LOCALE_477("获取用户未结账订单错误"),
    M_LOCALE_478("支付前校验失败"),
    M_LOCALE_479("加锁失败"),
    M_LOCALE_480("解锁失败"),
    M_LOCALE_481("开台失败"),
    M_LOCALE_482("查询门店区域异常"),
    M_LOCALE_483("尝试开台出现异常"),
    M_LOCALE_484("获取桌台状态列表信息失败"),
    SSO_REQUEST_FAILED("认证失败,请求sso服务失败"),
    M_LOCALE_485("没有拉取到apollo的敏感词配置"),
    M_LOCALE_486("没有读取到apollo的+file+配置"),
    M_LOCALE_487("认证失败"),
    M_LOCALE_488("没有source"),
    M_LOCALE_489("deviceGuid为空"),
    M_LOCALE_490("JSON转化异常！"),
    M_LOCALE_491("将对象转换为JSON字符串二进制数组错误！！"),
    M_LOCALE_492("json字符串转化错误！！"),
    M_LOCALE_493("json字符串转为list异常！！"),
    M_LOCALE_494("出错了，回滚"),
    M_LOCALE_495("更新支付方式，门店数据不能为空"),
    M_LOCALE_496("数据发生变更，请刷新后重试"),
    M_LOCALE_497("删除支付方式，门店数据不能为空"),
    M_LOCALE_498("查询多个门店支付方式name，传入门店为空！！"),
    M_LOCALE_499("state字段异常"),
    M_LOCALE_500("新增系统省零配置，门店数据不能为空"),
    M_LOCALE_501("门店数据不能为空"),
    M_LOCALE_502("设置聚合支付账户信息失败"),
    M_LOCALE_503("查询聚合支付账户名称是否存在失败"),
    M_LOCALE_504("根据支付信息guid删除聚合支付信息失败"),
    M_LOCALE_505("推送消息至安卓失败<>"),
    M_LOCALE_506("批量推送消息至安卓失败<>"),
    M_LOCALE_507("更新门店扎帐字段表信息"),
    M_LOCALE_508("设备类型为空"),
    M_LOCALE_509("钱箱记录保存失败，请联系管理员"),
    M_LOCALE_510("门店guid为空"),
    M_LOCALE_511("系统员工数据异常"),
    M_LOCALE_512("<>支付商户号或key错误，无法保存"),
    M_LOCALE_513("头部企业guid不能为空"),
    M_LOCALE_514("门店付款方式不存在"),
    M_LOCALE_515("更新付款方式的支付模式失败"),
    M_LOCALE_516("不支持的设备类型<>"),
    M_LOCALE_517("图片类型为空"),
    M_LOCALE_518("数据处理-修改交接班记录失败"),
    M_LOCALE_519("新增'原因列表'失败"),
    M_LOCALE_520("禁止重名"),
    M_LOCALE_521("最多可以上传6张图片"),
    M_LOCALE_522("修改交接班记录失败，请联系管理员"),
    M_LOCALE_523("至少需要保存一个支付账户"),
    M_LOCALE_524("同一类型下最多创建10个原因"),
    M_LOCALE_525("交接班记录<>不存在"),
    M_LOCALE_526("不存在开班记录"),
    M_LOCALE_527("type不能为空"),
    M_LOCALE_528("不存在该交班记录"),
    M_LOCALE_529("新增交接班记录失败，请联系管理员"),
    M_LOCALE_530("最多可添加10个账户"),
    M_LOCALE_531("不支持的图片类型"),
    M_LOCALE_532("<>账户名称重复，无法保存"),
    M_LOCALE_533("收款分流开关不能为空"),
    M_LOCALE_534("修改'原因列表'失败"),
    M_LOCALE_535("未查询到支付方式"),
    M_LOCALE_536("该门店已经初始化支付方式"),
    M_LOCALE_537("支付方式guid为空"),
    M_LOCALE_538("有未交班记录，无法操作"),
    M_LOCALE_539("复制'原因列表：<>"),
    M_LOCALE_540("只能设置一个默认账户"),
    M_LOCALE_541("账户不可重复"),
    M_LOCALE_542("钱箱记录<>不存在"),
    M_LOCALE_543("输入的时间范围为1-99"),
    M_LOCALE_544("新增门店配置失败，请联系管理员"),
    M_LOCALE_545("不存在门店接班记录，无法操作钱箱"),
    M_LOCALE_546("新增支付方式，门店guid不能为空！！"),
    M_LOCALE_547("账户名称不可重复"),
    M_LOCALE_548("查询交接班数据失败"),
    M_LOCALE_549("请设置一个默认账户"),
    M_LOCALE_550("请提供至少一个配置修改项"),
    M_LOCALE_551("修改门店配置失败，请联系管理员"),
    M_LOCALE_552("<>该门店已存在相同名称的附加费名称"),
    M_LOCALE_553("未找到指定的附加费"),
    M_LOCALE_554("请选择附加费"),
    M_LOCALE_555("同一门店下不能创建相同的规则"),
    M_LOCALE_556("省零规则重复"),
    M_LOCALE_557("推送到商品到门店失败"),
    M_LOCALE_558("门店、企业或sku不能为空"),
    M_LOCALE_559("门店或购物车信息不能为空"),
    M_LOCALE_560("菜品不能为空"),
    M_LOCALE_561("名称必填"),
    M_LOCALE_562("宴会套餐分类名称已被宴会套餐功能占用，请重新命名"),
    M_LOCALE_563("门店不能为空"),
    M_LOCALE_564("来源入参错误"),
    M_LOCALE_565("当前已有星期已达到最大限制"),
    M_LOCALE_566("星期最大还可选择<>个"),
    M_LOCALE_567("与已选时段冲突,请调整时段"),
    M_LOCALE_568("当前已有时间段已达到最大限制"),
    M_LOCALE_569("时间段最大还可选择<>个"),
    M_LOCALE_570("时间转换异常"),
    M_LOCALE_571("所属品牌或门店不能为空"),
    M_LOCALE_572("后台代码有误，请修改"),
    M_LOCALE_573("该名称已存在，请修改"),
    M_LOCALE_574("后台配置有误，属性组下属性不能为空"),
    M_LOCALE_575("商户后台配置不正确，有商品关联了属性组但是未关联属性"),
    M_LOCALE_576("默认选择属性不支持多选"),
    M_LOCALE_577("属性名称重复"),
    M_LOCALE_578("删除的属性值不存在"),
    M_LOCALE_579("属性组GUID不能为空"),
    M_LOCALE_580("重名"),
    M_LOCALE_581("属性组不存在"),
    M_LOCALE_582("BatchIdGenerator生成商品guid失败"),
    M_LOCALE_583("初始化数据失败"),
    M_LOCALE_584("系统繁忙请稍候重试"),
    M_LOCALE_585("开启库存后,库存数量不能小于0"),
    M_LOCALE_586("库存数量不能小于0"),
    M_LOCALE_587("批量保存失败"),
    M_LOCALE_588("取消估清失败，套餐子菜已估清"),
    M_LOCALE_589("估清商品规格为空"),
    M_LOCALE_590("未查询到门店营业时间信息"),
    M_LOCALE_591("批量取消估清商品规格Guid为空"),
    M_LOCALE_592("批量停售商品Guid为空"),
    M_LOCALE_593("商品guid不能为空"),
    M_LOCALE_594("所属门店或品牌不能为空"),
    M_LOCALE_595("请先创建分类"),
    M_LOCALE_596("套餐子菜品不能为空"),
    M_LOCALE_597("当前无可用商品"),
    M_LOCALE_598("商品编号重复"),
    M_LOCALE_599("BatchIdGenerator生成skuguid失败"),
    M_LOCALE_600("分组名称重复"),
    M_LOCALE_601("商品规格重复"),
    M_LOCALE_602("套餐的起卖数必须为整数"),
    M_LOCALE_603("商品已被删除"),
    M_LOCALE_604("由于存在于套餐:<>，以下门店无法解除分配：<>"),
    M_LOCALE_605("请到品牌库进行删除商品"),
    M_LOCALE_606("可多选属性组下属性数量必须大于1"),
    M_LOCALE_607("单选属性组配置的属性默认项数量不能大于1"),
    M_LOCALE_608("多规格商品的规格名称必填"),
    M_LOCALE_609("规格名称在同一商品下唯一"),
    M_LOCALE_610("多规格商品的编号长度位1-16位字符"),
    M_LOCALE_611("套餐下分组不能为空"),
    M_LOCALE_612("分组下至少关联一个商品"),
    M_LOCALE_613("规格编号已达到上限不可新建"),
    M_LOCALE_614("二维码guid"),
    M_LOCALE_615("商品以及被推送门店不能为空"),
    M_LOCALE_616("商品数据异常"),
    M_LOCALE_617("已存在相同名称的<>模板，保存失败"),
    M_LOCALE_618("方案guid不能为空"),
    M_LOCALE_619("存入的key不能为空"),
    M_LOCALE_620("模块入口参数错误"),
    M_LOCALE_621("商品类型排序交换失败"),
    M_LOCALE_622("查询不到规格数据"),
    M_LOCALE_623("规格guid不能为空"),
    M_LOCALE_624("listItemInfoBySalesModelNew,商品guid不能为空"),
    M_LOCALE_625("当前无上架商品"),
    M_LOCALE_626("商品guid集合不能为空"),
    M_LOCALE_627("未查询到品牌"),
    M_LOCALE_628("分类guid不能为空"),
    M_LOCALE_629("未查询到品牌信息"),
    M_LOCALE_630("浏览模式不能为空"),
    M_LOCALE_631("已存在同名方案"),
    M_LOCALE_632("菜谱方案商品名称请输入1-40个字符"),
    M_LOCALE_633("保存商品有误"),
    M_LOCALE_634("下架商品后<>菜谱商品将为空，请清除对应菜谱再保存"),
    M_LOCALE_635("方案菜品不能为空"),
    M_LOCALE_636("存在相同的父菜谱"),
    M_LOCALE_637("菜谱方案不存在"),
    M_LOCALE_638("方案信息为空"),
    M_LOCALE_639("方案Guid为空"),
    M_LOCALE_640("菜谱方案批量编辑菜品失败"),
    M_LOCALE_641("未查询到方案信息"),
    M_LOCALE_642("<>菜谱商品将为空，请清除对应菜谱再保存"),
    M_LOCALE_643("推送类型不能为空"),
    M_LOCALE_644("方案不能为空"),
    M_LOCALE_645("推送时间不能为空"),
    M_LOCALE_646("下架方式不能为空"),
    M_LOCALE_647("之前配置商品属性的时候配置错误了，属性组下面没有配置属性"),
    M_LOCALE_648("商品配置错误"),
    M_LOCALE_649("系统错误"),
    M_LOCALE_650("上下架商品查询异常"),
    M_LOCALE_651("套餐<>结构异常"),
    M_LOCALE_652("套餐<>下分组不能为空"),
    M_LOCALE_653("套餐<>下分组<>下关联商品为空"),
    M_LOCALE_654("上下架状态字段传输错误"),
    M_LOCALE_655("商品规格SKU简码有重复，请修改"),
    M_LOCALE_656("整单折扣状态字段传输错误"),
    M_LOCALE_657("门店必选"),
    M_LOCALE_658("来源入参不正确"),
    REASON_CANNOT_BE_NULL("原因不能为空"),
    REASON_NAME_CANNOT_BE_NULL("原因名字不能为空"),
    REASON_TYPE_CODE_CANNOT_BE_NULL("原因类型码不能为空"),
    FIELD_INCOMPLETE("字段不完整"),
    ITEM_TYPE_Duplicate("菜品分类重名"),
    BRAND_REQUIRED_CHOOSE("品牌必选"),
    BRAND_REQUIRED("品牌必填"),
    STORE_REQUIRED("门店必填"),
    LEAST_ONE_CATEGORY("至少选中一个分类"),
    TYPE_ASSOCIATED_ITEM("该分类下已关联商品，操作失败"),
    TYPE_ASSOCIATED_ATTRIBUTE("该分类下已关联属性，操作失败"),
    PLAN_GUID_NULL("菜谱方案guid不能为空"),
    CATEGORY_NAME("分类重名"),
    FAILED_TO_QUERY_STORES_BRAND("根据品牌列表查询品牌列表下的所有门店失败!"),
    WRONG_PARAMS("参数错误"),
    BRAND_CANNOT_BE_NULL("品牌不能为空"),
    OP_FAIL("操作失败"),
    DUPLICATE_ITEM_NAME("该商品名称已存在"),
    DEFAULT_NUM_CANNOT_GT_PICK_NUM("分组下默认选择的商品规格数不能大于分组的可选数"),
    PICK_NUM_GT_ACTUAL_NUM("分组的可选商品数大于实际可选数"),
    STORE_CANNOT_BE_NULL("门店不能为空"),
    STORE_UNDER_BRAND_IS_EMPTY("品牌下门店为空"),
    NOT_FOUND_PRICE_PLAN_INFO("被使用中，未查询到菜谱方案信息"),
    ITEM_EXIT_PLAN("商品存在以下菜谱方案<>，请手动清除"),
    DUPLICATE_UPC("商品条码已存在"),
    STORE_GUID_NOT_EMPTY("门店guid不能为空"),
    TIME_CONFLICT("时间冲突"),
    PRICE_PLAN_CANNOT_BE_EMPTY("方案不能为空"),
    WRONG_ITEM_STRUCTURE("商品结构异常"),
    DUPLICATE_CODE("sku简码已存在"),
    DUPLICATE_RETAIL_CODE("商品货号已存在"),

    NO_ACCESS_TO_STORE_DATA("没有门店数据查看权限"),

    INSUFFICIENT_STOCK("<>库存不足不可下单"),

    ORDER_LOCKED("订单已被锁定"),

    NOT_ALLOW_SETTLEMENT("订单状态不允许结账"),

    INVALID_BOOKING_TIME("非法预定时间"),

    ONLY_UNPAID_ORDERS_CAN_SETTLED("只有未结账订单可以结算"),

    PRODUCT_SPECIFICATIONS_EXIST("商品规格存在于以下套餐<>请手动清除"),

    CONTACT_ADMINISTRATOR_ACTIVATE("请联系管理员开通"),

    SUBMISSION_FAILED_REJECTED("提交失败，已拒单"),

    TABLES_EXIST_CANNOT_DELETED("该区域下存在桌台，无法删除"),

    DUPLICATE_PAYMENT("支付方式名称重复"),

    NOT_EMPTY_QUEUES_CAN_NOT_DELETED("只能删除非空队列"),

    PERFECT_RESERVATION_RULES_SYNCHRONIZING("请完善预订规则后再同步"),

    GROUP_STORE_NOT_EXIST("店铺不存在"),

    SELECT_STORE_SYNCHRONIZED("请选择需同步的门店"),

    PLEASE_ENTER_VALID_STORE("请输入正确的门店ID"),
            ;
    private final String message;


    public String getMessage() {
        return message;
    }

    LocaleMessageEnum(String message) {
        this.message =message;
    }

    private final static String SPLIT_SEPARATE = "<>";

    public static String getLocale(String message){
        if(message == null){
            return LocaleUtil.getMessage(UNKNOWN_EXCEPTION);
        }
        //若本地是简体中文直接返回
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return message;
        }
        //若存在多语言则通过中文寻找对应语言
        for(LocaleMessageEnum localeMessageEnum : LocaleMessageEnum.values()){
            //若完全匹配
            if(localeMessageEnum.getMessage().equals(message)){
                return LocaleUtil.getMessage(localeMessageEnum);
            }
            //若包含
            if(localeMessageEnum.getMessage().contains(SPLIT_SEPARATE)){
                String[] split = localeMessageEnum.getMessage().split(SPLIT_SEPARATE);
                if(!isContains(split,message)){
                   continue;
                }
                //截取替换元素
                String localeMessage = LocaleUtil.getMessage(localeMessageEnum);
                String[] localeSplit = localeMessage.split(SPLIT_SEPARATE);
                return replaceLocale(message,split,localeSplit);
            }
        }
        return message;
    }

    private static String replaceLocale(String message, String[] split, String[] localeSplit) {
        for (int i = 0; i < split.length; i++){
            message = message.replaceFirst(split[i],localeSplit[i]);
        }
        return message;
    }

    private static boolean isContains(String[] split, String message) {
        for (String s : split){
            if(!message.contains(s)){
                return false;
            }
        }
        return true;
    }

}
