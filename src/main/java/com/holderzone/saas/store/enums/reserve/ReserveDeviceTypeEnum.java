package com.holderzone.saas.store.enums.reserve;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @date 2024/9/13
 * @description 预订来源
 */
public enum ReserveDeviceTypeEnum {

    All_IN_ONE(3, "一体机预定"),

    POS(4, Constants.POS_RESERVE),

    M1(6, Constants.POS_RESERVE),

    PV1(7, Constants.POS_RESERVE),

    TCD(15, "小程序预定"),

    FROM_TABLE(31, "桌台预付");

    private final int code;

    private final String desc;

    ReserveDeviceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getReserveDeviceTypeNameByCode(int code) {
        for (ReserveDeviceTypeEnum deviceTypeEnum : ReserveDeviceTypeEnum.values()) {
            if (deviceTypeEnum.code == code) {
                return deviceTypeEnum.desc;
            }
        }
        throw new BusinessException("未找到匹配的类型：" + code);
    }

    private static class Constants {
        public static final String POS_RESERVE = "pos预定";
    }
}
