package com.holderzone.saas.store.enums.trade;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.ParameterException;

import java.util.List;

/**
 * <AUTHOR>
 * 税率类型
 */
public enum TaxRateTypeEnum {

    NORMAL_TAX_RATE (0, "正常税率"),
    EXEMPT_TAX_RATE(1, "免税"),
    NO_TAX_RATE(2,"不征税"),
    NORMAL_ZERO_TAX_RATE(3,"普通零税率"),
    ;

    private int code;
    private String desc;


    public static List<Integer> listCode(){
        List<Integer> list = Lists.newArrayList();
        for (TaxRateTypeEnum c : TaxRateTypeEnum.values()) {
            list.add(c.getCode());
        }
        return list;
    }


    TaxRateTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (TaxRateTypeEnum c : TaxRateTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        throw new ParameterException("团购类型不匹配");
    }
    public static TaxRateTypeEnum groupBuyType(int code) {
        for (TaxRateTypeEnum c : TaxRateTypeEnum.values()) {
            if (c.getCode() == code) {
                return c;
            }
        }
        throw new ParameterException("团购类型不匹配");
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
