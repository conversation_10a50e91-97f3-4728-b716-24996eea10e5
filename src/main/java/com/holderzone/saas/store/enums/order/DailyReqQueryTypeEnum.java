package com.holderzone.saas.store.enums.order;

import lombok.Getter;

@Getter
public enum DailyReqQueryTypeEnum {

    ALL(0, "全部"),
    DINNER(1, "堂食"),
    TAKEAWAY(2, "外卖"),
    ;

    private int code;
    private String desc;

    DailyReqQueryTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isDinner(Integer code) {
        if (code == null) {
            return true;
        }
        return DailyReqQueryTypeEnum.DINNER.getCode() == code || DailyReqQueryTypeEnum.ALL.getCode() == code;
    }

    public static boolean isTakeaway(Integer code) {
        if (code == null) {
            return true;
        }
        return DailyReqQueryTypeEnum.TAKEAWAY.getCode() == code || DailyReqQueryTypeEnum.ALL.getCode() == code;
    }
}
