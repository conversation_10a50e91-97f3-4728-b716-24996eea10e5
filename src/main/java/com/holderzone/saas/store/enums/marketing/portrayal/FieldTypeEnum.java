package com.holderzone.saas.store.enums.marketing.portrayal;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2024/12/23
 * @description 会员画像字段枚举
 */
public enum FieldTypeEnum {

    BASICS_INFO(1, "基础信息"),

    CONSUME_INFO(2, "消费信息"),

    RECHARGE_INFO(3, "充值信息"),
    ;

    private int code;
    private String desc;

    FieldTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getName(int code) {
        for (FieldTypeEnum fieldTypeEnum : FieldTypeEnum.values()) {
            if (fieldTypeEnum.getCode() == code) {
                return fieldTypeEnum.getDesc();
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static FieldTypeEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (FieldTypeEnum fieldTypeEnum : FieldTypeEnum.values()) {
            if (fieldTypeEnum.code == code) {
                return fieldTypeEnum;
            }
        }
        return null;
    }

}
