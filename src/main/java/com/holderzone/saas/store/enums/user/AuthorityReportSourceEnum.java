package com.holderzone.saas.store.enums.user;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Getter
@AllArgsConstructor
public enum AuthorityReportSourceEnum {

    /**
     * 主页
     */
    MENU_REPORT_HOME("menu_report_home", "", "主页"),
    BUSINESS_REPORT_HOME(MENU_REPORT_HOME.originalSourceCode, "business_report_home", "主页"),

    /**
     * 营业概况
     */
    DAY_BUSINESS_STATISTICS("day_business_statistics", "", "营业概况统计"),
    OVERVIEW_SALE_ORDER_COUNT(DAY_BUSINESS_STATISTICS.originalSourceCode, "overview_sale_order_count", "订单数"),
    OVERVIEW_SALE_GUEST_COUNT(DAY_BUSINESS_STATISTICS.originalSourceCode, "overview_sale_guest_count", "（正餐）客流量"),
    OVERVIEW_SALE_AMOUNT(DAY_BUSINESS_STATISTICS.originalSourceCode, "overview_sale_amount", "销售额"),
    OVERVIEW_SALE_ACTUALLY_AMOUNT(DAY_BUSINESS_STATISTICS.originalSourceCode, "overview_sale_actually_amount", "销售总净额"),
    OVERVIEW_SALE_DISCOUNT_AMOUNT(DAY_BUSINESS_STATISTICS.originalSourceCode, "overview_sale_discount_amount", "优惠总额"),
    OVERVIEW_SALE_ESTIMATED_AMOUNT(DAY_BUSINESS_STATISTICS.originalSourceCode, "overview_sale_estimated_amount", "预计应得金额"),
    OVERVIEW_SALE_OCCUPANCY_RATE_PERCENT(DAY_BUSINESS_STATISTICS.originalSourceCode, "overview_sale_occupancy_rate_percent", "（正餐）上座率"),
    OVERVIEW_SALE_OPEN_TABLE_RATE_PERCENT(DAY_BUSINESS_STATISTICS.originalSourceCode, "overview_sale_open_table_rate_percent", "（正餐）开台率"),
    OVERVIEW_SALE_FLIP_TABLE_RATE_PERCENT(DAY_BUSINESS_STATISTICS.originalSourceCode, "overview_sale_flip_table_rate_percent", "（正餐）翻台率"),
    OVERVIEW_SALE_AVG_DINE_IN_TIME(DAY_BUSINESS_STATISTICS.originalSourceCode, "overview_sale_avg_dine_in_time", "（正餐）平均用餐时长（分钟）"),

    /**
     * 收款方式
     */
    PAYMENT_TERM_STATISTICS("payment_term_statistics", "", "收款方式统计"),
    GATHER_SALE_TYPE_NAME(PAYMENT_TERM_STATISTICS.originalSourceCode, "gather_sale_type_name", "收款方式"),
    GATHER_SALE_ACTUALLY_AMOUNT(PAYMENT_TERM_STATISTICS.originalSourceCode, "gather_sale_actually_amount", "销售净额"),
    GATHER_SALE_RECHARGE_AMOUNT(PAYMENT_TERM_STATISTICS.originalSourceCode, "gather_sale_recharge_amount", "充值收入"),
    GATHER_SALE_RESERVE_AMOUNT(PAYMENT_TERM_STATISTICS.originalSourceCode, "gather_sale_reserve_amount", "预付金"),

    /**
     * 用餐类型
     */
    DINNER_TERM_STATISTICS("dinner_term_statistics", "", "用餐方式统计"),
    DINING_SALE_TYPE_NAME(DINNER_TERM_STATISTICS.originalSourceCode, "dining_sale_type_name", "用餐类型"),
    DINING_SALE_ORDER_COUNT(DINNER_TERM_STATISTICS.originalSourceCode, "dining_sale_order_count", "订单数"),
    DINING_SALE_GUEST_COUNT(DINNER_TERM_STATISTICS.originalSourceCode, "dining_sale_guest_count", "消费人数"),
    DINING_SALE_ACTUALLY_AMOUNT(DINNER_TERM_STATISTICS.originalSourceCode, "dining_sale_actually_amount", "销售净额"),
    DINING_SALE_ORDER_PRICE(DINNER_TERM_STATISTICS.originalSourceCode, "dining_sale_order_price", "单均消费"),
    DINING_SALE_GUEST_PRICE(DINNER_TERM_STATISTICS.originalSourceCode, "dining_sale_guest_price", "人均消费"),

    /**
     * 商品销售统计
     */
    GOODS_SELL_STATISTICS("goods_sell_statistics", "", "商品销售统计"),
    GOODS_SALE_ITEM_NAME(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_item_name", "商品名称"),
    GOODS_SALE_TRADE_UNIT_PRICE(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_trade_unit_price", "堂食单价"),
    GOODS_SALE_TRADE_COUNT(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_trade_count", "堂食销售数量"),
    GOODS_SALE_TRADE_SALE_AMOUNT(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_trade_sale_amount", "堂食销售金额"),
    GOODS_SALE_TRADE_ACTUALLY_AMOUNT(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_trade_actually_amount", "堂食实付金额"),
    GOODS_SALE_TAKEAWAY_UNIT_PRICE(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_takeaway_unit_price", "外卖单价"),
    GOODS_SALE_TAKEAWAY_COUNT(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_takeaway_count", "外卖销售数量"),
    GOODS_SALE_TAKEAWAY_SALE_AMOUNT(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_takeaway_sale_amount", "外卖销售金额"),
    GOODS_SALE_COUNT_TOTAL(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_count_total", "数量销售合计"),
    GOODS_SALE_SALE_AMOUNT_TOTAL(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_sale_amount_total", "销售金额合计"),
    GOODS_SALE_ACTUALLY_AMOUNT_TOTAL(GOODS_SELL_STATISTICS.originalSourceCode, "goods_sale_actually_amount_total", "实付金额合计"),

    /**
     * 分类销售统计
     */
    CATEGORY_SELL_STATISTICS("category_sell_statistics", "", "分类销售统计"),
    CATEGORY_SALE_NAME(CATEGORY_SELL_STATISTICS.originalSourceCode, "category_sale_name", "商品分类"),
    CATEGORY_SALE_COUNT(CATEGORY_SELL_STATISTICS.originalSourceCode, "category_sale_count", "销售数量"),
    CATEGORY_SALE_AMOUNT(CATEGORY_SELL_STATISTICS.originalSourceCode, "category_sale_amount", "销售金额"),
    CATEGORY_SALE_ACTUALLY_AMOUNT(CATEGORY_SELL_STATISTICS.originalSourceCode, "category_sale_actually_amount", "实付金额"),

    /**
     * 属性销售统计
     */
    ATTR_SELL_STATISTICS("attr_sell_statistics", "", "属性销售统计"),
    ATTR_SALE_GROUP_NAME(ATTR_SELL_STATISTICS.originalSourceCode, "attr_sale_group_name", "属性组"),
    ATTR_SALE_NAME(ATTR_SELL_STATISTICS.originalSourceCode, "attr_sale_name", "属性名称"),
    ATTR_SALE_UNIT_PRICE(ATTR_SELL_STATISTICS.originalSourceCode, "attr_sale_unit_price", "单价"),
    ATTR_SALE_COUNT(ATTR_SELL_STATISTICS.originalSourceCode, "attr_sale_count", "销售数量"),
    ATTR_SALE_AMOUNT(ATTR_SELL_STATISTICS.originalSourceCode, "attr_sale_amount", "金额"),


    /**
     * 退菜统计
     */
    RETURE_ORDER_STATISTICS("reture_order_statistics", "", "退菜统计"),
    RETURN_SALE_ITEM_NAME(RETURE_ORDER_STATISTICS.originalSourceCode, "return_sale_item_name", "商品名称"),
    RETURN_SALE_UNIT_PRICE(RETURE_ORDER_STATISTICS.originalSourceCode, "return_sale_unit_price", "单价"),
    RETURN_SALE_COUNT(RETURE_ORDER_STATISTICS.originalSourceCode, "return_sale_count", "退菜数量"),
    RETURN_SALE_AMOUNT(RETURE_ORDER_STATISTICS.originalSourceCode, "return_sale_amount", "金额"),

    /**
     * 赠菜统计
     */
    GIVE_ORDER_STATISTICS("give_order_statistics", "", "赠菜统计"),
    GIFT_SALE_ITEM_NAME(GIVE_ORDER_STATISTICS.originalSourceCode, "gift_sale_item_name", "商品名称"),
    GIFT_SALE_UNIT_PRICE(GIVE_ORDER_STATISTICS.originalSourceCode, "gift_sale_unit_price", "单价"),
    GIFT_SALE_COUNT(GIVE_ORDER_STATISTICS.originalSourceCode, "gift_sale_count", "赠菜数量"),
    GIFT_SALE_AMOUNT(GIVE_ORDER_STATISTICS.originalSourceCode, "gift_sale_amount", "金额"),

    /**
     * 会员消费统计
     */
    MENBER_CONSUME_RECORD("menber_consume_record", "", "会员消费统计"),
    MEMBER_CONSUME_COUNT(MENBER_CONSUME_RECORD.originalSourceCode, "member_consume_count", "消费单数"),
    MEMBER_CONSUME_NUMBER(MENBER_CONSUME_RECORD.originalSourceCode, "member_consume_number", "消费人数"),
    MEMBER_CONSUME_AMOUNT(MENBER_CONSUME_RECORD.originalSourceCode, "member_consume_amount", "消费金额"),

    /**
     * 会员充值统计
     */
    MENBER_CHARGE_RECORD("menber_charge_record", "", "会员充值统计"),
    MEMBER_RECHARGE_ORDER_NUM(MENBER_CHARGE_RECORD.originalSourceCode, "member_recharge_order_num", "充值单数"),
    MEMBER_CHARGE_NUMBER(MENBER_CHARGE_RECORD.originalSourceCode, "member_charge_number", "充值人数"),
    MEMBER_CHARGE_TOTAL_AMOUNT(MENBER_CHARGE_RECORD.originalSourceCode, "member_charge_total_amount", "充值金额"),
    MEMBER_CHARGE_GIFT_AMOUNT(MENBER_CHARGE_RECORD.originalSourceCode, "member_charge_gift_amount", "充值赠送"),
    MEMBER_CHARGE_AMOUNT(MENBER_CHARGE_RECORD.originalSourceCode, "member_charge_amount", "充值收入"),

    ;


    private final String originalSourceCode;

    private final String currentSourceCode;

    private final String sourceName;

    public static final List<String> ORIGINAL_SOURCE_CODES = Lists.newArrayList(MENU_REPORT_HOME.originalSourceCode, GOODS_SELL_STATISTICS.originalSourceCode,
            CATEGORY_SELL_STATISTICS.originalSourceCode, ATTR_SELL_STATISTICS.originalSourceCode, RETURE_ORDER_STATISTICS.originalSourceCode,
            GIVE_ORDER_STATISTICS.originalSourceCode, MENBER_CONSUME_RECORD.originalSourceCode, MENBER_CHARGE_RECORD.originalSourceCode,
            DAY_BUSINESS_STATISTICS.originalSourceCode, PAYMENT_TERM_STATISTICS.originalSourceCode, DINNER_TERM_STATISTICS.originalSourceCode);

    public static List<String> getOriginalSourceCodeByCurrentSourceCodes(List<String> currentSourceCodes) {
        if (CollectionUtils.isEmpty(currentSourceCodes)) {
            return Lists.newArrayList();
        }
        return Arrays.stream(values())
                .filter(e -> currentSourceCodes.contains(e.getCurrentSourceCode()))
                .map(AuthorityReportSourceEnum::getOriginalSourceCode)
                .distinct()
                .collect(Collectors.toList());
    }

}
