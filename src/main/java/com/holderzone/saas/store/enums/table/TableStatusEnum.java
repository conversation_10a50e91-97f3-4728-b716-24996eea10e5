package com.holderzone.saas.store.enums.table;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableStatusEnum
 * @date 2019/01/02 18:08
 * @description
 * @program holder-saas-store-dto
 */
public enum TableStatusEnum {

    UN_USED(-1, "暂停使用"),

    FREE(0, "空闲"),

    TAKE_UP(1, "占用"),

    TAKE_UP_WITHOUT_ORDER_DISH(10, "占用空台(开台后未点餐)"),

    /**
     * 该状态不会存入数据库，仅仅用于安卓桌台页面判断锁定使用
     */
    TAKE_UP_LOCK(11, "占用锁定(订单结账中)"),

    TAKE_UP_COMBINE(12, "占用并台(桌位订单关联中，可以合并结账)"),

    TAKE_UP_EATING(13, "占用客人用餐中"),

    TAKE_UP_ASSOCIATED(15, "占用联台(桌位订单关联中，可以合并结账)"),

    RESERVATION(2, "预定"),

    RESERVATION_WITHOUT_LOCK(20, "预定未锁定(已经约定的桌位，等待客人到店用餐)"),

    RESERVATION_LOCK(21, "预定锁定(已经预定的桌位，临近客人预定到达时间，锁定桌位)"),

    TO_BE_CLEAR(3, "待清理"),

    TO_BE_CLOSE(4, "已结清，未关台");

    private Integer status;

    private String msg;

    public static final Set<Integer> MULTI_TABLE_STATUS = Sets.newHashSet(TAKE_UP_COMBINE.getStatus(), TAKE_UP_ASSOCIATED.getStatus());


    TableStatusEnum() {
    }

    TableStatusEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public static List<Integer> getReservePayStatusList() {
        return Lists.newArrayList(TableStatusEnum.TAKE_UP.getStatus(), TableStatusEnum.RESERVATION.getStatus());
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static TableStatusEnum getByStatus(Integer status) {
        return Arrays.stream(TableStatusEnum.values())
                .filter(tableStatusEnum -> Objects.equals(tableStatusEnum.getStatus(), status))
                .findAny()
                .orElse(null);
    }
}
