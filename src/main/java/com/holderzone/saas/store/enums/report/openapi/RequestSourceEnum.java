package com.holderzone.saas.store.enums.report.openapi;


public enum RequestSourceEnum {

    COMMON("common", "公共"),

    SHI_YUAN_HUI("shiyuanhui", "世园会"),

    KU_BAN("kuban", "酷办"),

    ;

    private final String code;

    private final String desc;

    RequestSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static RequestSourceEnum getByCode(String code) {
        for (RequestSourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return RequestSourceEnum.COMMON;
    }
}
