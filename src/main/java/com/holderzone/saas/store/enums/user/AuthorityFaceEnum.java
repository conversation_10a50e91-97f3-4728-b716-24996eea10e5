package com.holderzone.saas.store.enums.user;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum AuthorityFaceEnum {

    DISH_REFUND_FACE("dish_refund_face", "退菜人脸"),

    REFUND_FACE("refund_face", "退款人脸"),

    ORDER_RECOVERY("order_recovery", "反结账人脸"),

    ;

    private final String sourceCode;

    private final String sourceName;

    AuthorityFaceEnum(String sourceCode, String sourceName) {
        this.sourceCode = sourceCode;
        this.sourceName = sourceName;
    }

    public static AuthorityFaceEnum getEnum(String sourceCode) {
        for (AuthorityFaceEnum faceEnum : AuthorityFaceEnum.values()) {
            if (Objects.equals(faceEnum.getSourceCode(), sourceCode)) {
                return faceEnum;
            }
        }
        return null;
    }
}
