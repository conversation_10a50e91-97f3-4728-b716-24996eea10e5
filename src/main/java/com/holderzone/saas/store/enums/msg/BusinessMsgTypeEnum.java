package com.holderzone.saas.store.enums.msg;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessMsgTypeEnum
 * @date 2018/10/11 18:04
 * @description
 * @program holder-saas-store-dto
 */
public enum BusinessMsgTypeEnum {

    UN_REDAN(0, "未读消息"),

    BUSINESS_MSG_TYPE(1, "业务消息"),

    SYSTEM_TYPE(2, "系统消息"),

    TAKEOUT_STATE_MSG_TYPE(3, "外卖状态变化消息"),

    TABLE_STATE_MSG_TYPE(4, "桌台状态变化消息"),

    ITEM_STATE_MSG_TYPE(5, "商品状态变化消息"),

    QUEUE_CHANGED_MSG_TYPE(6, "排队变化消息"),

    ITEM_TEMPLATE_CHANGED_MSG_TYPE(7, "商品模板变化消息"),

    BMT_DETAIL_MEMBER_CHECK(8, "会员到账"),

    ORDER_CHANGED_MSG_TYPE(9, "订单变化消息"),

    BMT_DETAIL_ORDER(11, "订单下单"),

    BMT_DETAIL_CHECK(12, "订单结账"),

    BMT_DETAIL_WECHAT_SERVICE(13, "微信服务"),

    BMT_DETAIL_TAKEOUT_NODE(14, "外卖重要节点消息"),

    RECOVERY(15, "反结账消息"),

    PRICE_PLAN_CHANGE(16, "价格方案变动消息"),

    PRICE_PLAN_END(17, "价格方案售卖结束消息"),

    ITEM_CHANGE(18, "商品信息变动消息"),

    PAD_MESSAGE(20, "PAD消息"),

    PAD_PAY_MESSAGE(21, "PAD支付消息"),

    PAD_SOLD_OUT_MESSAGE(22, "PAD估清消息"),

    PAD_PREPAYMENT_IS_SUCCESSFUL(23, "PAD预支付成功"),

    /**
     * 一体机并台
     */
    MERGE_TABLE(24, "一体机并台"),

    /**
     * 并台用户自主结账成功
     */
    MERGE_TABLE_PAY(25, "并台用户自主结账成功"),

    /**
     * 一体机转台(主动转台)
     */
    TRANSFER_TABLE_POSITIVE(26, "一体机转台(主动转台)"),

    /**
     * 一体机转台(被转)
     */
    TRANSFER_TABLE_PASSIVE(27, "一体机转台(被转)"),

    ACCEPT_ORDER(28, "一体机接单"),

    NON_PAD_SHUTDOWN(29, "非PAD关台消息"),

    MODIFIED_NUMBER(30, "非PAD修改桌台人数消息"),

    BIND_UP_ACCOUNTS(32, "扎帐提示消息"),

    BIND_UP_ACCOUNTS_STATUS(33, "门店扎帐"),

    TABLE_CHANGED(41, "桌台状态变更消息"),

    ITEM_CHANGED(51, "商品状态变更消息"),

    ITEM_TEMPLATE_TIME_CHANGDE(52, "商品模板时间变更消息"),

    MEAL_VOICE_REMINDERS(60, "出餐语音提醒"),

    HANDOVER_MESSAGE(70, "设备下线消息"),

    MULTI_HANDOVER(71, "多人汇总交接班"),

    WEIXIN_SUBMIT_ORDER(81, "微信订单变化消息"),

    WEIXIN_ORDER_STATE(82, "微信下单消息"),

    ORDER_CHANGED(91, "订单数据变更消息"),

    RESERVE_RECORD_TYPE(101, "预定单状态变化"),

    RESERVE_RECORD_SUBMIT(102, "预定单下单"),

    RESERVE_RECORD_CANCEL(103, "预定单自动取消"),

    RESERVE_RECORD_STATE_CHANGE(104, "预定单状态发生变化"),

    RESERVE_RECORD_STATE_CHANGE_POS(105, "POS预定单状态发生变化"),
    ;

    private Integer id;

    private String name;

    BusinessMsgTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
