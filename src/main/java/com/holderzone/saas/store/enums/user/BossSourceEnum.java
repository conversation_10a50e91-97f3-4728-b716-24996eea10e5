package com.holderzone.saas.store.enums.user;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023/8/2 14:44
 * @description 老板助手权限枚举
 */
public enum BossSourceEnum {

    DATA_REPORT("data_report", "数据报表", "15"),

    ITEM_MANAGE("item_manage", "商品管理", "10"),

    CLOUD_PRINT("cloud_print_config", "云打印配置", "cloud_print_config"),

    ORDER_MANAGE("order_manage", "订单管理", "order_manage"),

    SALE_REPORT("sale_report", "销售报表", "销售报表"),

    SALE_STATISTICS("sale_statistics", "销售统计", "销售统计"),

    TABLE_STATISTICS("table_statistics", "实时门店", "实时门店"),

    ITEM_STATISTICS("item_statistics", "商品统计", "商品统计"),

    USER_RUN("user_run", "用户运营", "用户运营"),
    ;

    private final String sourceCode;

    private final String sourceName;

    private final String convert;

    BossSourceEnum(String sourceCode, String sourceName, String convert) {
        this.sourceCode = sourceCode;
        this.sourceName = sourceName;
        this.convert = convert;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public String getSourceName() {
        return sourceName;
    }

    public String getConvert() {
        return convert;
    }

    public static String getConvert(String sourceCode) {
        for (BossSourceEnum sourceEnum : BossSourceEnum.values()) {
            if (Objects.equals(sourceEnum.getSourceCode(), sourceCode)) {
                return sourceEnum.getConvert();
            }
        }
        return null;
    }

}
