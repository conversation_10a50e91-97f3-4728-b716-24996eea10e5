package com.holderzone.saas.store.enums.weixin;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxDistributionEnum
 * @date 2019/5/20
 */
@AllArgsConstructor
@Getter
public enum WxDistributionEnum {
	/**
	 * 分发类型:0(向桌台每个用户分发，需要tableGuid),1(用于快餐分发，需要openId),2(向桌台具体某个用户分发，需要tableGuid,openId)
	 */
	TABLE_MULTI(0),
	FAST_SINGLE(1),
	TABLE_SINGLE(2),
	TABLE_EXCEPT_CURRENT(3);
	private Integer code;
}
