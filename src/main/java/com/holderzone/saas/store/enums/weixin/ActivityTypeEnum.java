package com.holderzone.saas.store.enums.weixin;

/**
 * <AUTHOR>
 * @date 2024/6/12
 * @description 活动类型
 */
public enum ActivityTypeEnum {

    /**
     * 限时特价
     */
    LIMIT_SPECIALS_ACTIVITY(1, "限时特价"),

    /**
     * 满减
     */
    FULL_MINUS(2, "满减"),

    /**
     * 满折
     */
    FULL_DISCOUNT(3, "满折"),

    /**
     * 第N份优惠
     */
    NTH_ACTIVITY(4, "第N份优惠"),

    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    ActivityTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

}
