package com.holderzone.saas.store.enums.item;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className WeekEnum
 * @date 18-11-14 下午5:56
 * @description 星期枚举
 * @program holder-saas-store-dto
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum WeekEnum {
    MONDAY(1, "星期一","MONDAY"),
    TUESDAY(2, "星期二","TUESDAY"),
    WEDNESDAY(3, "星期三","WEDNESDAY"),
    THURSDAY(4, "星期四","THURSDAY"),
    FRIDAY(5, "星期五","FRIDAY"),
    SATURDAY(6, "星期六","SATURDAY"),
    SUNDAY(0, "星期日","SUNDAY")
    ;

    private Integer code;

    private String desc;

    private String week;

    /**
     * 根据枚举code获取对应描述
     *
     * @param code code
     * @return 枚举描述
     */
    public static String getDescByCode(Integer code) {
        return Arrays.stream(WeekEnum.values()).filter(p -> p.getCode().equals(code))
                     .findFirst().orElseThrow(() -> new BusinessException("不存在当前code对应的desc")).getDesc();
    }

    /**
     * 根据枚举week获取对应code
     *
     * @param week week
     * @return 枚举code
     */
    public static Integer getCodeByWeek(String week) {
        return Arrays.stream(WeekEnum.values()).filter(p -> p.getWeek().equals(week))
                .findFirst().orElseThrow(() -> new BusinessException("不存在当前week对应的code")).getCode();
    }


}