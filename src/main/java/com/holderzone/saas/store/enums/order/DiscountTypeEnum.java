package com.holderzone.saas.store.enums.order;

import com.google.common.collect.Lists;
import com.holderzone.saas.store.util.LocaleUtil;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountTypeEnum
 * @date 2018/09/04 17:52
 * @description
 * @program holder-saas-store-trade
 */
public enum DiscountTypeEnum {

    MEMBER(1, "会员折扣", 2),
    WHOLE(2, "整单折扣", 3),
    CONCESSIONAL(3, "整单让价", 5),
    SYSTEM(4, "系统省零", 4),
    FREE(5, "赠送优惠", 0),
    GROUPON(6, "团购验券", 1),
    MEMBER_GROUPON(7, "会员代金券", 7),
    POINTS_DEDUCTION(8, "积分抵扣", 6),
    SINGLE_MEMBER(9, "会员优惠", 8),
    SINGLE_DISCOUNT(10, "单品折扣", 9),
    GOODS_GROUPON(11, "会员商品券", 7),
    ACTIVITY(12, "满减满折活动", 12),
    TONGCHIDAO(13, "通吃岛优惠", 13),
    THIRD_ACTIVITY(14, "第三方平台活动", 14),
    FOLLOW_RED_PACKET(15, "随行红包优惠", 15),
    LIMIT_SPECIALS_ACTIVITY(16, "限时特价活动", 16),
    NTH_ACTIVITY(17, "第N份优惠活动", 17),
    DOU_YIN_GROUPON(61, "抖音验券", 61),
    DA_ZHONG_DIAN_PIN(62, "大众点评验券", 62),
    ALIPAY_GROUPON(65, "支付宝验券", 65),
    ABC_GROUPON(66, "农行团购验券", 66),
    MAITON_GROUPON(70, "美团买单", 70),
    OTHER(-1, "其他优惠", -1),
    ;

    private int code;
    private String desc;
    private int memberCode;

    public static final List<DiscountTypeEnum> OLD_LIST;

    static {
        OLD_LIST = Lists.newArrayList();
        for (DiscountTypeEnum discountTypeEnum : DiscountTypeEnum.values()){
            if(discountTypeEnum == DOU_YIN_GROUPON || discountTypeEnum == DA_ZHONG_DIAN_PIN){
                continue;
            }
            OLD_LIST.add(discountTypeEnum);
        }
    }

    DiscountTypeEnum(int code, String desc, int memberCode) {
        this.code = code;
        this.desc = desc;
        this.memberCode = memberCode;
    }

    public static String getDesc(int code) {
        for (DiscountTypeEnum c : DiscountTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public static DiscountTypeEnum get(int code) {
        for (DiscountTypeEnum c : DiscountTypeEnum.values()) {
            if (c.getCode() == code) {
                return c;
            }
        }
        return DiscountTypeEnum.OTHER;
    }

    private final static String PREFIX = "DISCOUNT_";
    public static String getLocaleDesc(int code) {
        //若本地是简体中文直接返回
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return getDesc(code);
        }
        for (DiscountTypeEnum c : DiscountTypeEnum.values()) {
            if (c.getCode() == code) {
                return LocaleUtil.getMessage(c.name());
            }
        }
        return null;
    }

    private static final String DY = "抖音团购优惠";

    private static final String MT = "美团团购优惠";

    private static final String ABC = "农行团购优惠";

    private static final String ALIPAY = "支付宝团购优惠";

    private static final String DZ = "大众点评团购优惠";

    public static String getLocaleDescByName(String name) {
        //若本地是简体中文直接返回
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE || name == null){
            return name;
        }
        if(name.equals(DY)){
            name = DOU_YIN_GROUPON.getDesc();
        }
        if(name.equals(MT)){
            name = GROUPON.getDesc();
        }
        if(name.equals(ABC)){
            name = ABC_GROUPON.getDesc();
        }
        if(name.equals(ALIPAY)){
            name = ALIPAY_GROUPON.getDesc();
        }
        if(name.equals(DZ)){
            name = DA_ZHONG_DIAN_PIN.getDesc();
        }
        for (DiscountTypeEnum c : DiscountTypeEnum.values()) {
            if (c.getDesc().equals(name)) {
                return LocaleUtil.getMessage(PREFIX + c.name());
            }
        }
        return name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getMemberCode() {
        return memberCode;
    }

    public void setMemberCode(int memberCode) {
        this.memberCode = memberCode;
    }

    public static List<Integer> getPaymentType() {
        return Lists.newArrayList(DiscountTypeEnum.GROUPON.getCode(), DiscountTypeEnum.DOU_YIN_GROUPON.getCode(),
                DiscountTypeEnum.ALIPAY_GROUPON.getCode(),
                DiscountTypeEnum.THIRD_ACTIVITY.getCode(),
                DiscountTypeEnum.DA_ZHONG_DIAN_PIN.getCode(),
                DiscountTypeEnum.ABC_GROUPON.getCode());
    }
}
