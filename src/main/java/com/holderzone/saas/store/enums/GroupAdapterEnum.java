package com.holderzone.saas.store.enums;

import lombok.Getter;

/**
 * 团购适配枚举
 */
@Getter
public enum GroupAdapterEnum {

    MT("美团团购", 6, 19),

    DY("抖音团购", 61, 60),

    ;

    private final String name;

    private final Integer firstCode;

    private final Integer secondCode;

    GroupAdapterEnum(String name, Integer firstCode, Integer secondCode) {
        this.name = name;
        this.firstCode = firstCode;
        this.secondCode = secondCode;
    }

    public static GroupAdapterEnum getEnumByFirst(int firstCode) {
        for (GroupAdapterEnum groupAdapterEnum : GroupAdapterEnum.values()) {
            if (groupAdapterEnum.getFirstCode() == firstCode) {
                return groupAdapterEnum;
            }
        }
        return null;
    }

    public static GroupAdapterEnum getEnumBySecond(int secondCode) {
        for (GroupAdapterEnum groupAdapterEnum : GroupAdapterEnum.values()) {
            if (groupAdapterEnum.getSecondCode() == secondCode) {
                return groupAdapterEnum;
            }
        }
        return null;
    }
}
