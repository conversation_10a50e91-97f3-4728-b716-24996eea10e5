package com.holderzone.saas.store.enums.report;


public enum SaleDetailOpenSourceEnum {

    NORMAL("normal", "默认"),

    SHI_YUAN_HUI("shiyuanhui", "世园会"),

    ;

    private final String code;

    private final String desc;

    SaleDetailOpenSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static SaleDetailOpenSourceEnum getByCode(String code) {
        for (SaleDetailOpenSourceEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return NORMAL;
    }
}
