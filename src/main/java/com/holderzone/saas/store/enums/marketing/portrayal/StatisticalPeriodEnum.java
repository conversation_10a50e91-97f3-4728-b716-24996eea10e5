package com.holderzone.saas.store.enums.marketing.portrayal;


import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/23
 * @description 统计周期
 */
public enum StatisticalPeriodEnum {

    REGISTERED_TO_NOW(1, "注册至今"),

    SOME_TIME_RECENTLY(2, "近%s天"),

    THIS_YEAR(3, "今年"),

    CURRENT_MONTH(4, "本月"),
    ;

    private int code;
    private String desc;

    StatisticalPeriodEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getName(int code) {
        for (StatisticalPeriodEnum periodEnum : StatisticalPeriodEnum.values()) {
            if (periodEnum.getCode() == code) {
                return periodEnum.getDesc();
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static StatisticalPeriodEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (StatisticalPeriodEnum periodEnum : StatisticalPeriodEnum.values()) {
            if (Objects.equals(code, periodEnum.getCode())) {
                return periodEnum;
            }
        }
        return null;
    }

}
