package com.holderzone.saas.store.enums.user;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/28
 * @description 资源过滤枚举
 */
@Getter
public enum SourceFilterEnum {

    ITEM_STORE_CREATE("item_store_create", "新建单品"),

    STORE_BATCH_IMPORT_FROM_EXCEL("store_batch_import_from_excel", "批量导入"),

    ITEM_STORE_BATCH_IMPORT("item_store_batch_import", "批量导入商品"),

    ;

    private final String sourceCode;

    private final String sourceName;


    SourceFilterEnum(String sourceCode, String sourceName) {
        this.sourceCode = sourceCode;
        this.sourceName = sourceName;
    }

    public static SourceFilterEnum getEnumBySourceCode(String sourceCode) {
        for (SourceFilterEnum sourceEnum : SourceFilterEnum.values()) {
            if (Objects.equals(sourceEnum.getSourceCode(), sourceCode)) {
                return sourceEnum;
            }
        }
        return null;
    }

    public static List<String> getAllSourceCode() {
        return Arrays.stream(SourceFilterEnum.values())
                .map(SourceFilterEnum::getSourceCode)
                .collect(Collectors.toList());
    }
}
