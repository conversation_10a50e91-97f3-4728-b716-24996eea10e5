package com.holderzone.saas.store.enums.print;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ContentTypeEnum
 * @date 2018/07/26 13:49
 * @description //TODO
 * @program holder-saas-store-print
 */
public enum ContentTypeEnum {

    BAR_CODE("BarCode", "条形码"),

    LABEL_BAR_CODE("LabelBarCode", "标签单条形码"),

    BLANK_ROW("BlankRow", "空白行"),

    COORDINATE_ROW("CoordinateRow", "坐标行"),

    IMAGE("Image", "图片"),

    KEY_VALUE("KeyValue", "键值对"),

    LINE("Line", "简单横线"),

    QR_CODE("QrCode", "二维码"),

    REVERSE_TEXT("ReverseText", "反白文本"),

    SECTION("Section", "段落"),

    SEPARATOR("Separator", "分隔符"),

    TABLE("Table", "单元格"),

    TABLE_ROW("TableRow", "单元格行"),

    ;

    private String type;

    private String desc;

    ContentTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
