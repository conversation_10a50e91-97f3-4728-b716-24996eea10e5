package com.holderzone.saas.store.enums.common;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className AioPermissionEnum
 * @date 18-11-14 下午5:56
 * @description 公共配置枚举
 * @program holder-saas-store-dto
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ConfigEnum {
    ESTIMATE_RECOVERY_TIME(100, "估清置满时间"),
    QUEUE_CLEAN_TIMING(101, "排队自动清空时机"),
    RESERVE_MERCHANT_PHONE(102, "云呼商户预订电话"),
    DELETE_ESTIMATE_ITEM(103, "营业日移除估清时间"),
    PLAN_DELAYED_COMPENSATE(104, "方案延时补偿"),
    BIND_UP_ACCOUNTS(188, "扎帐定时功能"),
    ;

    private Integer code;

    private String desc;

    /**
     * 根据枚举code获取对应描述
     *
     * @param code code
     * @return 枚举描述
     */
    public static String getDescByCode(Integer code) {
        return Arrays.stream(ConfigEnum.values()).filter(p -> p.getCode().equals(code))
                .findFirst().orElseThrow(() -> new BusinessException("不存在当前code对应的desc")).getDesc();
    }
}