package com.holderzone.saas.store.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className 订单来源枚举
 * @date 2018/09/04 17:52
 * @description
 * @program holder-saas-store-order
 */
@Deprecated
public enum DeviceTypeEnum{
    T(3, "一体机","T"),
    MW(8, "美团外卖","MW"),
    EW(9, "饿了么外卖","EW"),
            ;

    private int code;
    private String desc;
    private String head;

    private DeviceTypeEnum(int code, String desc, String head) {
        this.code = code;
        this.desc = desc;
        this.head = head;
    }

    public static String getDesc(int code) {
        for (DeviceTypeEnum c : DeviceTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public static String getHead(int code) {
        for (DeviceTypeEnum c : DeviceTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.head;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }
}
