package com.holderzone.saas.store.enums.item;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTypeEnum
 * @date 2018/09/05 10:32
 * @description //商品类型
 * @program holder-saas-store-order
 */
public enum ItemTypeEnum {
    // 商品类型：1.套餐（不称重，无规格），2多规格商品（非单品，不称重），3.称重商品（单商品，称重），4.单品。
    PKG(1,"套餐"),
    MULTI_SKU(2,"多规格"),
    WEIGH(3,"称重商品"),
    SINGLE_UNWEIGH(4, "单品"),
    GROUP(5, "团餐");

    private int code;
    private String desc;

    ItemTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getName(int code){
        for (ItemTypeEnum unitEnum : ItemTypeEnum.values()) {
            if (unitEnum.getCode()==code){
                return unitEnum.getDesc();
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
