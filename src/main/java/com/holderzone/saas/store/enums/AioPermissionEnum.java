package com.holderzone.saas.store.enums;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className AioPermissionEnum
 * @date 18-11-14 下午5:56
 * @description 一体机权限枚举
 * @program holder-saas-store-dto
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AioPermissionEnum {
    ZENG_CAI(1001, "赠菜"),
    TUI_CAI(1002, "退菜"),
    ZHENG_DAN_ZHE_KOU(1003, "整单折扣"),
    ZHENG_DAN_RANG_JIA(1004, "整单让价"),
    QU_XIAO_DING_DAN(1005, "取消订单"),
    ZUO_FEI_DING_DAN(1006, "作废订单"),
    FAN_JIE_ZHANG(1007, "反结账"),
    QIAN_XIANG(1008, "钱箱");

    private Integer code;

    private String desc;

    /**
     * 根据枚举code获取对应描述
     *
     * @param code code
     * @return 枚举描述
     */
    public static String getDescByCode(Integer code) {
        return Arrays.stream(AioPermissionEnum.values()).filter(p -> p.getCode().equals(code))
                     .findFirst().orElseThrow(() -> new BusinessException("不存在当前code对应的desc")).getDesc();
    }
}