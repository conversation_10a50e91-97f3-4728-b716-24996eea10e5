package com.holderzone.saas.store.enums.marketing;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/29
 * @description 特价类型枚举
 */
public enum SpecialsTypeEnum {

    /**
     * 打折
     */
    DISCOUNT(1,"打折"),

    /**
     * 减价
     */
    SALE(2,"减价"),

    /**
     * 指定价格
     */
    SPECIFY_PRICE(3,"指定价格"),

    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }


    SpecialsTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static SpecialsTypeEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (SpecialsTypeEnum TypeEnum : SpecialsTypeEnum.values()) {
            if (TypeEnum.code == code) {
                return TypeEnum;
            }
        }
        return null;
    }

}
