package com.holderzone.saas.store.enums;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.ParameterException;

import java.util.List;

/**
 * <AUTHOR>
 */
public enum GroupBuyTypeEnum {

    MEI_TUAN(6, "美团团购"),
    DOU_YIN(61, "抖音团购"),
    DA_ZHONG_DIAN_PIN(62,"大众点评"),
    ZHUAN_CAN(63, "赚餐"),
    <PERSON><PERSON><PERSON>(64, "其他"),
    ALIPAY(65, "支付宝团购"),
    ABC(66, "农行团购"),

    MAITON(70, "美团买单");

    private int code;
    private String desc;

    public final static List<Integer> CODE_LIST;

    static {
        CODE_LIST = listCode();
    }

    public static List<Integer> listCode(){
        List<Integer> list = Lists.newArrayList();
        for (GroupBuyTypeEnum c : GroupBuyTypeEnum.values()) {
            list.add(c.getCode());
        }
        return list;
    }

    public static List<Integer> listNeedCode(){
        List<Integer> list = Lists.newArrayList();
        list.add(MEI_TUAN.code);
        list.add(DOU_YIN.code);
        return list;
    }

    GroupBuyTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (GroupBuyTypeEnum c : GroupBuyTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        throw new ParameterException("团购类型不匹配");
    }
    public static GroupBuyTypeEnum groupBuyType(int code) {
        for (GroupBuyTypeEnum c : GroupBuyTypeEnum.values()) {
            if (c.getCode() == code) {
                return c;
            }
        }
        throw new ParameterException("团购类型不匹配");
    }

    public static boolean containsDesc(String desc) {
        for (GroupBuyTypeEnum c : GroupBuyTypeEnum.values()) {
            if (c.getDesc().equals(desc)) {
                return true;
            }
        }
        return false;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
