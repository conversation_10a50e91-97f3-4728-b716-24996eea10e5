package com.holderzone.saas.store.enums.member;

import lombok.Getter;

@Getter
public enum MemberFeignModelEnum {

    SUCCESS(200, "成功！"),

    FAILED(-1, "失败！"),

    HEADER_ILLEGAL(-2, "请求头不合法！"),

    SYSTEM_ILLEGAL(-3, "系统来源不合法！"),
    ;
    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误描述
     */
    private final String des;

    MemberFeignModelEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }
}
