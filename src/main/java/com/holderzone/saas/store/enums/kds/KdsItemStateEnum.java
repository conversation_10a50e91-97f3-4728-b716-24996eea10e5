package com.holderzone.saas.store.enums.kds;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeModeEnum
 * @date 2018/09/04 17:52
 * @description 订单交易模式枚举
 * @program holder-saas-store-trade
 */
public enum KdsItemStateEnum {

    NULL(-1,  "不明确"),

    PREPARE(1,  "即起"),

    HANG_UP(2, "挂起"),

    CALL_UP(3,  "叫起"),

    DISTRIBUTED(8,  "已上菜"),

    RESERVED(9,  "预定"),

    REFUNDED(99,  "已退菜"),

    ;

    private int code;

    private String desc;

    KdsItemStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KdsItemStateEnum ofCode(int code) {
        for (KdsItemStateEnum kdsTradeModeEnum : values()) {
            if (kdsTradeModeEnum.code == code) {
                return kdsTradeModeEnum;
            }
        }
        throw new BusinessException("不支持的厨房商品状态：" + code);
    }

    public static String getDescByCode(int code) {
        return ofCode(code).getDesc();
    }
}
