package com.holderzone.saas.store.enums.weixin;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxServiceTypeEnum
 * @date 2019/03/04 14:33
 * @description 微信公众号类型枚举
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum WxServiceTypeEnum {
    SUBSCRIPTION_NUMBER(0, "订阅号"),
    LEVEL_UP_NUMBER(1, "升级后订阅号"),
    SERVICE_NUMBER(2, "服务号");

    private Integer code;

    private String desc;

    public static String getDescByCode(Integer code) {
        return Arrays.stream(WxServiceTypeEnum.values()).filter(p -> p.getCode().equals(code))
                .findFirst().orElseThrow(() -> new BusinessException("不存在当前code对应的desc")).getDesc();
    }
}
