package com.holderzone.saas.store.enums.report;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.enums.PaymentTypeEnum;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description 券来源
 */
public enum CouponSourceEnum {

    MT(0, "美团团购"),

    ELM(1, "抖音团购"),

    ;

    private final int code;

    private final String desc;

    CouponSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(int code) {
        for (CouponSourceEnum c : CouponSourceEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        throw new ParameterException("券来源不匹配");
    }
}
