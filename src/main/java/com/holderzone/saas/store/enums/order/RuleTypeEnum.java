package com.holderzone.saas.store.enums.order;

/**
 * <AUTHOR>
 * @description 第三方平台活动-规则类型枚举
 * @date 2021/12/13 12:02
 * @className: RuleTypeEnum
 */
public enum RuleTypeEnum {

    AMOUNT_DEDUCTION(0, "金额扣减"),
    PAY_DISCOUNT(1, "买单优惠"),
    ;

    private int code;
    private String desc;

    RuleTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
