package com.holderzone.saas.store.enums.trade;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/8/29 12:27
 * @description 订单反结账类型1：普通单 2：原单 3：新单 4：退单
 */
@Getter
public enum RecoveryTypeEnum {

    ORDINARY(1, "普通单"),

    ORIGINAL(2, "原单"),

    NEW(3, "新单"),

    REFUND(4, "退单"),
    ;

    private final int code;

    private final String desc;

    RecoveryTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}

