package com.holderzone.saas.store.enums.takeaway;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/13
 * @description 美团业务类型枚举
 */
@Getter
public enum MtBusinessTypeEnum {

    NEW_MEMBER(1, "会员联名卡新客"),

    INTEGRAL_CALLBACK(2, "会员卡积分推送回调"),
    ;

    private final Integer code;

    private final String desc;

    MtBusinessTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
