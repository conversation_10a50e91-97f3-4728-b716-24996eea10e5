package com.holderzone.saas.store.enums.user;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum PosPaySourceEnum {

    POS_MEMBER_FAST_PAY("pos_member_fast_pay", "会员支付"),
    ;

    private final String sourceCode;

    private final String sourceName;

    PosPaySourceEnum(String sourceCode, String sourceName) {
        this.sourceCode = sourceCode;
        this.sourceName = sourceName;
    }

    public static PosPaySourceEnum getEnumBySourceCode(String sourceCode) {
        for (PosPaySourceEnum sourceEnum : values()) {
            if (Objects.equals(sourceEnum.getSourceCode(), sourceCode)) {
                return sourceEnum;
            }
        }
        return null;
    }
}
