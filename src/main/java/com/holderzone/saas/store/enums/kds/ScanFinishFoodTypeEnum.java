package com.holderzone.saas.store.enums.kds;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/22
 * @description 扫码出餐类型枚举
 */
@Getter
public enum ScanFinishFoodTypeEnum {

    DINNER(1, "堂食"),
    FAST(2, "快餐"),
    TAKEAWAY(3, "外卖"),
    ;

    private final Integer code;
    private final String desc;

    ScanFinishFoodTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
