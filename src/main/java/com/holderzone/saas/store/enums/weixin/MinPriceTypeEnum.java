package com.holderzone.saas.store.enums.weixin;

/**
 * <AUTHOR>
 * @date 2024/6/11
 * @description 最低价类型
 */
public enum MinPriceTypeEnum {

    ORIGINAL_PRICE(0, "原价"),

    MEMBER_PRICE(1, "会员价"),

    MEMBER_DISCOUNT(2, "会员折扣"),

    LIMITED_SPECIAL_ACTIVITY(3, "限时特价"),

    GROUPON_PRICE(4, "团购价"),

    ;

    private final int code;

    private final String des;

    MinPriceTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

}
