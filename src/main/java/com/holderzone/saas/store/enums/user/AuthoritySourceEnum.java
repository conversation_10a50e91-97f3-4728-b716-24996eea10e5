package com.holderzone.saas.store.enums.user;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum AuthoritySourceEnum {

    DINE_IN_REFUND("dine_in_refund", "正餐退款", "dine_in_refund_operate_after_authorization"),

    FAST_REFUND("fast_refund", "快餐退款", "fast_refund_operate_after_authorization"),

    DINE_IN_DISH_REFUND("dine_in_dish_refund", "正餐退菜", "dine_in_dish_refund_operate_after_authorization"),

    SNACK_DISH_REFUND("snack_dish_refund", "快餐退菜", "snack_dish_refund_operate_after_authorization"),

    ORDER_DINE_IN_RECOVERY("order_dine_in_recovery", "正餐反结账", "order_dine_in_recovery_operate_after_authorization"),

    ORDER_SNACK_RECOVERY("order_snack_recovery", "快餐反结账", "order_snack_recovery_operate_after_authorization"),

    ;

    private final String sourceCode;

    private final String sourceName;

    private final String convertCode;

    AuthoritySourceEnum(String sourceCode, String sourceName, String convertCode) {
        this.sourceCode = sourceCode;
        this.sourceName = sourceName;
        this.convertCode = convertCode;
    }

    public static AuthoritySourceEnum getEnumBySourceCode(String sourceCode) {
        for (AuthoritySourceEnum sourceEnum : AuthoritySourceEnum.values()) {
            if (Objects.equals(sourceEnum.getSourceCode(), sourceCode)) {
                return sourceEnum;
            }
        }
        return null;
    }

    public static AuthoritySourceEnum getEnumByConvertCode(String convertCode) {
        for (AuthoritySourceEnum sourceEnum : AuthoritySourceEnum.values()) {
            if (Objects.equals(sourceEnum.getConvertCode(), convertCode)) {
                return sourceEnum;
            }
        }
        return null;
    }
}
