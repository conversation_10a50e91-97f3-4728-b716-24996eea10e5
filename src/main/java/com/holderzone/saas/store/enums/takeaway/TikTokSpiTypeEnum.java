package com.holderzone.saas.store.enums.takeaway;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 抖音SPI消息推送 消息类型
 */
@Getter
@AllArgsConstructor
public enum TikTokSpiTypeEnum {

    SENDHOME_ORDER_PAYMSG(1, "用户下单"),
    // 用户接单或者订单自动完单时候会触发该消息
    SENDHOME_ORDER_FINISHMSG(2, "订单完成消息"),
    SENDHOME_ORDER_ACCEPTMSG(3, "商家接单"),
    SENDHOME_ORDER_REJECTMSG(4, "商家拒单"),
    // 用户申请收款，商家同意。当用户收到退款时候会触发该消息
    SENDHOME_AFTERSALE_APPLYREFUNDMSG(5, "用户发起售后申请"),
    // 商家同意或者拒绝用户售后时触发
    SENDHOME_AFTERSALE_AUDITRESULTMSG(6, "售后结果通知"),
    // 当商家同意退款后，实际退款到账时，会推送此消息
    SENDHOME_AFTERSALE_COMPLETEREFUNDMSG(7, "退款已成功"),
    SENDHOME_AFTERSALE_CANCELORDERMSG(8, "订单取消消息"),
    SENDHOME_AFTERSALE_MODIFYREFUNDMSG(9, "用户修改售后消息"),
    SENDHOME_AFTERSALE_CLOSEREFUNDMSG(10, "当买家取消申请或系统超时机制导致退款取消时"),
    // 到家运力状态变化推送，运力状态包含：101:待骑手接单 102:骑手已接单 103:骑手已到店 104:骑手已取货 200:已送达 300:已取消
    SENDHOME_DELIVERY_DELIVERYSTATUSCHANGE(31, "到家运力状态变化推送"),
    UN_KNOW(-1, "未知消息"),
    ;

    private final Integer spiType;

    private final String desc;

    public static TikTokSpiTypeEnum getBySpiType(Integer spiType) {
        for (TikTokSpiTypeEnum value : values()) {
            if (value.getSpiType().equals(spiType)) {
                return value;
            }
        }
        return UN_KNOW;
    }

}
