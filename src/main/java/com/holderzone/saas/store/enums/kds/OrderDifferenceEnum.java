package com.holderzone.saas.store.enums.kds;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @description 订单区分枚举
 */
public enum OrderDifferenceEnum {

    FAST(0,"K"),

    TAKEOUT(1,"W"),
    ;

    private final Integer code;
    private final String desc;

    OrderDifferenceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
