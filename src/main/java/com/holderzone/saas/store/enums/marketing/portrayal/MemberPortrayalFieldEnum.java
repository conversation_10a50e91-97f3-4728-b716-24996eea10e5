package com.holderzone.saas.store.enums.marketing.portrayal;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2024/12/23
 * @description 会员画像字段枚举
 */
public enum MemberPortrayalFieldEnum {

    GENDER(1, 1, "性别"),

    BIRTHDAY(2, 1, "生日"),

    REGISTER_TIME(3, 1, "注册时间"),

    MEMBER_GRADE(4, 1, "会员等级"),

    CONSUME_COUNT(1, 1, "累计消费次数"),

    CONSUME_AMOUNT(2, 1, "累计消费金额"),

    GUEST_SINGLE_PRICE(3, 1, "客单价"),

    LAST_CONSUME_TIME(4, 1, "上次消费时间"),

    RECHARGE_COUNT(1, 1, "累计充值次数"),

    RECHARGE_AMOUNT(2, 1, "累计充值金额"),

    AVERAGE_RECHARGE_AMOUNT(3, 1, "次均充值金额"),

    LAST_RECHARGE_TIME(4, 1, "上次充值时间"),
    ;

    private int sort;
    private int type;
    private String name;

    MemberPortrayalFieldEnum(int sort, int type, String desc) {
        this.sort = sort;
        this.type = type;
        this.name = desc;
    }

    public static String getName(String field) {
        for (MemberPortrayalFieldEnum fieldEnum : MemberPortrayalFieldEnum.values()) {
            if (Objects.equals(fieldEnum.name, field)) {
                return fieldEnum.getName();
            }
        }
        return null;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public static MemberPortrayalFieldEnum getEnum(String field) {
        if (Objects.isNull(field)) {
            return null;
        }
        for (MemberPortrayalFieldEnum fieldEnum : MemberPortrayalFieldEnum.values()) {
            if (Objects.equals(field, fieldEnum.name())) {
                return fieldEnum;
            }
        }
        return null;
    }

}
