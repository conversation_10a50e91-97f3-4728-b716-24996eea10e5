package com.holderzone.saas.store.enums.item;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 解估类型
 *
 * <AUTHOR>
 * @since 2025/7/18
 */
@Getter
public enum CancelEstimateTypeEnum {

    NOT_CANCEL_ESTIMATE(1, "未解估"),

    AUTO_CANCEL_ESTIMATE(2, "自动解估"),

    MANUAL_CANCEL_ESTIMATE(3, "手动解估");

    private static final Map<Integer, String> CODE_TO_DESC_MAP = new HashMap<>();

    static {
        for (CancelEstimateTypeEnum c : CancelEstimateTypeEnum.values()) {
            CODE_TO_DESC_MAP.put(c.getCode(), c.getDesc());
        }
    }

    private final int code;

    private final String desc;

    CancelEstimateTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return CODE_TO_DESC_MAP.get(code);
    }
}
