package com.holderzone.saas.store.enums.weixin;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxVerifyTypeEnum
 * @date 2019/03/04 14:23
 * @description 品牌公众号认证类型枚举
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum WxVerifyTypeEnum {
    NOT_AUTH(-1, "未认证"),
    WECHAT_AUTH(0, "微信认证"),
    SINA_AUTH(1, "新浪认证"),
    TENCENT_AUTH(2, "腾讯认证"),
    QUALIFY_WITHOUT_NAME_AUTH(3, "已通过资质认证但未通过名称认证"),
    SINA_QUALIFY_WITHOUT_NAME_AUTH(4, "已通过资质认证且新浪认证"),
    TENCENT_QUALIFY_WITHOUT_NAME_AUTH(5, "已通过资质认证且腾讯认证");

    private Integer code;

    private String desc;

    public static String getDescByCode(Integer code) {
        return Arrays.stream(WxVerifyTypeEnum.values()).filter(p -> p.getCode().equals(code))
                .findFirst().orElseThrow(() -> new BusinessException("不存在当前code对应的desc")).getDesc();
    }
}
