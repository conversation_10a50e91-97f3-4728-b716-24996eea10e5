package com.holderzone.saas.store.enums.item;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ModuleEntranceEnum
 * @date 2019/2/12 17:18
 * @description 操作模块入口枚举
 * @program holder-saas-store-business
 */
public enum ModuleEntranceEnum {
    /**
     * 0
     * 从门店商品库入口进入
     */
    STORE(0, "从门店商品库入口进入"),

    /**
     * 1
     * 从品牌商品库入口进入
     */
    BRAND(1, "从品牌商品库入口进入"),

    /**
     * 2
     * 从推送入口进入
     */
    PUSH(2, "从推送入口进入"),

    /**
     * 4
     * 外卖入口
     */
    TAKEAWAY(4, "外卖入口");

    private int code;
    private String desc;

    ModuleEntranceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int code() {
        return code;
    }

    public String desc() {
        return desc;
    }}
