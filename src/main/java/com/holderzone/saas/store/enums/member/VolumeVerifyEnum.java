package com.holderzone.saas.store.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/17
 * @description 优惠券校验枚举
 */
@Getter
@AllArgsConstructor
public enum VolumeVerifyEnum {

    UNKNOWN(-1, "未知"),

    /**
     * 一体机
     */
    VERIFY(1, "验券"),

    /**
     * 微信小程序
     */
    CANCEL(2, "撤销"),

    /**
     * 微信公众号
     */
    QUERY(3, "查询（不验券，只给微信用）"),

    ;


    private final int code;

    private final String des;

    public static String getByCode(int code) {
        for (VolumeVerifyEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDes();
            }
        }
        return VolumeVerifyEnum.UNKNOWN.getDes();
    }
}
