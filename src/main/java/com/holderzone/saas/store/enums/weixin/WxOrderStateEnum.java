package com.holderzone.saas.store.enums.weixin;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxOrderStateEnum
 * @date 2019/5/9
 */
@AllArgsConstructor
@Getter
public enum WxOrderStateEnum {
	/**
	 * 微信订单状态:0待确认，1已下单，2已支付，3已取消，4，已退菜，5待支付，6已完成,7,没有开台且直接拒单
	 */
	PENDING(0,"待确认"),
	PROCESSED(1,"已下单"),
	PAID(2,"已支付"),
	CANCELLED(3,"已取消"),
	RETURNED(4,"已退菜"),
	UNPAID(5,"待支付"),
	COMPLETED(6,"已完成"),
	REJECTED(7,"没有开台且直接拒单");
	private Integer code;
	private String orderStateName;

	public static String getOrderStateName(Integer state) {
		WxOrderStateEnum[] values = WxOrderStateEnum.values();
		for (WxOrderStateEnum value : values) {
			if (state.equals(value.getCode())) {
				return value.getOrderStateName();
			}
		}
		return "error";
	}
}
