package com.holderzone.saas.store.enums.print;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.print.content.*;
import com.holderzone.saas.store.dto.print.content.base.FormatDefaults;
import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.base.TradeModeAware;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailCheckOutDTO;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailHandOverDTO;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailOpStatsDTO;
import com.holderzone.saas.store.dto.print.format.*;

import java.lang.reflect.Constructor;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * null 无
 * 0-199 通用、餐饮
 * 200-299 零售
 * 300-max 保留
 *
 * <p>
 * db迁移指南
 * <p>
 * 前台：
 * 菜品清单        5  ->   0
 * 预结单          2  ->   5
 * NEW并台预结单      ->   6
 * 结账单          3  ->   7
 * NEW并台结账单      ->   8
 * 储值单          4  ->   10
 * NEW转台单          ->   15
 * 外卖单          88 ->   20
 * 交接单          99 ->   25
 * NEW营业概况单      ->   40
 * NEW收款统计单      ->   41
 * NEW会员消费统计单  ->   42
 * NEW用餐类型统计单  ->   43
 * NEW分类销售统计单  ->   44
 * NEW商品销售统计单  ->   45
 * NEW属性销售统计单  ->   46
 * NEW退菜统计单      ->   47
 * NEW赠菜统计单      ->   48
 * <p>
 * <p>
 * 后厨：
 * 点菜单          1  ->   80
 * 退菜单          6  ->   81
 * NEW后厨转台单      ->   85
 * <p>
 * <p>
 * 标签：
 * 标签单          7  ->   100
 *
 * <AUTHOR>
 * @version 1.0
 * @className PrintBusiness
 * @date 2018/07/31 13:53
 * @description 打印票据类型
 * @program holder-saas-store-print
 */
@SuppressWarnings("ALL")
public enum InvoiceTypeEnum {

    NULL(
            null, "通用：基础清单", "基础清单",
            null, TradeModeEnum.NULL, null,
            PrintDTO.class, FormatDTO.class
    ),

    ITEM_LIST(
            0, "餐饮：菜品清单", "菜品清单",
            0, TradeModeEnum.DINE, null,
            PrintItemDetailDTO.class, ItemDetailFormatDTO.class
    ),

    ITEM_REPEAT_ORDER(
            1, "餐饮：菜品复单", "菜品清单",
            0, TradeModeEnum.DYNAMIC, null,
            PrintItemDetailDTO.class, ItemDetailFormatDTO.class
    ),

    PRE_CHECKOUT(
            5, "餐饮：预结单", "预结单",
            0, TradeModeEnum.DINE, null,
            PrintPreCheckoutDTO.class, PreCheckoutFormatDTO.class
    ),

    PRE_CHECKOUT_TABLES(
            6, "餐饮：并台预结单", "预结单",
            0, TradeModeEnum.DINE, PRE_CHECKOUT,
            PrintPreCoTableCbDTO.class, PreCheckoutFormatDTO.class
    ),

    CHECKOUT(
            7, "餐饮：结账单", "结账单",
            0, TradeModeEnum.DYNAMIC, null,
            PrintCheckOutDTO.class, CheckoutFormatDTO.class
    ),

    CHECKOUT_TABLES(
            8, "餐饮：并台结账单", "结账单",
            0, TradeModeEnum.DINE, CHECKOUT,
            PrintCoTableCbDTO.class, CheckoutFormatDTO.class
    ),

    STORED_CASH(
            10, "餐饮：储值单", "储值单",
            0, TradeModeEnum.NULL, null,
            PrintStoredCashDTO.class, StoredCashFormatDTO.class
    ),

    QUEUE(
            11, "餐饮：排队单", "排队单",
            0, TradeModeEnum.NULL, null,
            PrintQueueDTO.class, null
    ),

    TURN_TABLE(
            15, "餐饮：转台单", "转台单",
            0, TradeModeEnum.DINE, null,
            PrintTurnTableDTO.class, TurnTableFormatDTO.class
    ),

    TAKEOUT(
            20, "餐饮：外卖单", "外卖单",
            0, TradeModeEnum.TAKEOUT, null,
            PrintTakeoutDTO.class, TakeoutFormatDTO.class
    ),

    HANDOVER(
            25, "餐饮：交接单", "交接单",
            0, TradeModeEnum.NULL, null,
            PrintHandOverDTO.class, null
    ),

    OP_STATS(
            40, "餐饮：营业概况单", "营业概况",
            0, TradeModeEnum.NULL, null,
            PrintOpStatsDTO.class, null
    ),

    RECEIPT_STATS(
            41, "餐饮：收款统计单", "收款统计",
            0, TradeModeEnum.NULL, null,
            PrintReceiptStatsDTO.class, null
    ),

    MEM_STATS(
            42, "餐饮：会员消费统计单", "会员消费统计",
            0, TradeModeEnum.NULL, null,
            PrintMemStatsDTO.class, null
    ),

    TRADE_STATS(
            43, "餐饮：用餐类型统计单", "用餐类型统计",
            0, TradeModeEnum.NULL, null,
            PrintTradeStatsDTO.class, null
    ),

    TYPE_STATS(
            44, "餐饮：分类销售统计单", "分类销售统计",
            0, TradeModeEnum.NULL, null,
            PrintTypeStatsDTO.class, null
    ),

    ITEM_STATS(
            45, "餐饮：商品销售统计单", "商品销售统计",
            0, TradeModeEnum.NULL, null,
            PrintItemStatsDTO.class, null
    ),

    PROP_STATS(
            46, "餐饮：属性销售统计单", "属性销售统计",
            0, TradeModeEnum.NULL, null,
            PrintPropStatsDTO.class, null
    ),

    ITEM_REFUND_STATS(
            47, "餐饮：退菜统计单", "退菜统计",
            0, TradeModeEnum.NULL, null,
            PrintItemStatsDTO.class, null
    ),

    ITEM_GIFT_STATS(
            48, "餐饮：赠菜统计单", "赠菜统计",
            0, TradeModeEnum.NULL, null,
            PrintItemStatsDTO.class, null
    ),

    RESERVE_ITEM_STATS(
            49, "餐饮：预点餐统计单", "预点餐统计",
            0, TradeModeEnum.NULL, null,
            PrintReserveDTO.class, null
    ),

    MEM_CONSU_STATS(
            50, "餐饮：会员消费统计单", "会员消费统计",
            0, TradeModeEnum.NULL, null,
            PrintConsumeStatsDTO.class, null
    ),

    MEM_RECHAR_STATS(
            51, "餐饮：会员充值统计单", "会员充值统计",
            0, TradeModeEnum.NULL, null,
            PrintRechargeStatsDTO.class, null
    ),

    HANDOVER_NEW(
            52, "餐饮：交接单", "交接单",
            0, TradeModeEnum.NULL, null,
            PrintHandOverNewDTO.class, null
    ),

    HANDOVER_PRE_NEW(
            53, "餐饮：预打印交接单", "交接单",
            0, TradeModeEnum.NULL, null,
            PrintHandOverNewDTO.class, null
    ),

    DEBT_REPAYMENT(
            60, "餐饮：挂账还款单", "挂账还款",
            0, TradeModeEnum.NULL, null,
            PrintDebtRepaymentDTO.class, null
    ),

    REFUND_INVOICE(
            61, "餐饮：退款单", "退款单",
            0, TradeModeEnum.DYNAMIC, null,
            PrintRefundDTO.class, RefundFormatDTO.class
    ),

    ORDER_ITEM(
            80, "餐饮：点菜单", "点菜单",
            1, TradeModeEnum.DYNAMIC, null,
            PrintOrderItemDTO.class, OrderItemFormatDTO.class
    ),

    REFUND_ITEM(
            81, "餐饮：退菜单", "退菜单",
            1, TradeModeEnum.DYNAMIC, null,
            PrintRefundItemDTO.class, RefundItemFormatDTO.class
    ),

    CHANGE_ITEM(
            82, "餐饮：换菜单", "换菜",
            1, TradeModeEnum.DYNAMIC, null,
            PrintChangeItemDTO.class, null
    ),

    TRANSFER_ITEM(
            83, "餐饮：转菜单", "转菜",
            1, TradeModeEnum.DYNAMIC, null,
            PrintTransferItemDTO.class, null
    ),

    TURN_TABLE_ITEM(
            85, "餐饮：后厨转台单", "转台单",
            1, TradeModeEnum.DINE, null,
            PrintTurnTableDTO.class, TurnTableFormatDTO.class
    ),

    LABEL(
            100, "餐饮：标签单", "标签单",
            2, TradeModeEnum.DYNAMIC, null,
            PrintLabelDTO.class, null
    ),

    ITEM_LABEL(
            101, "餐饮：商品标签单", "商品标签单",
            2, TradeModeEnum.DYNAMIC, null,
            PrintItemLabelDTO.class, null
    ),

    RETAIL_CHECKOUT(
            200, "零售：结账单", "结账单",
            0, TradeModeEnum.NULL, null,
            PrintRetailCheckOutDTO.class, null
    ),

    RETAIL_HANDOVER(
            201, "零售：交接单", "交接单",
            0, TradeModeEnum.NULL, null,
            PrintRetailHandOverDTO.class, null
    ),

    RETAIL_TAKEOUT(
            220, "零售：外卖单", "外卖单",
            0, TradeModeEnum.TAKEOUT, null,
            PrintTakeoutDTO.class, null
    ),

    RETAIL_OP_STATS(
            250, "零售：营业概况单", "营业概况",
            0, TradeModeEnum.NULL, null,
            PrintRetailOpStatsDTO.class, null
    ),

    RETAIL_TYPE_STATS(
            251, "零售：分类销售统计单", "分类销售统计",
            0, TradeModeEnum.NULL, null,
            PrintTypeStatsDTO.class, null
    ),

    RETAIL_ITEM_STATS(
            252, "零售：商品销售统计单", "商品销售统计",
            0, TradeModeEnum.NULL, null,
            PrintItemStatsDTO.class, null
    ),

    RETAIL_ITEM_REFUND_STATS(
            253, "零售：退货统计单", "退货统计",
            0, TradeModeEnum.NULL, null,
            PrintItemStatsDTO.class, null
    ),

    RETAIL_ITEM_GIFT_STATS(
            254, "零售：赠送统计单", "赠送统计",
            0, TradeModeEnum.NULL, null,
            PrintItemStatsDTO.class, null
    ),

    SALE_REFUND_STATS(
            255, "餐饮：退款统计单", "退款统计",
            0, TradeModeEnum.NULL, null,
            PrintSaleRefundStatsDTO.class, null
    ),

    RESERVE_PAY_STATS(
            256, "餐饮：预付金单", "预付金",
            0, TradeModeEnum.NULL, null,
            PrintReservePayDTO.class, null
    ),
    ;

    private Integer type;

    private String name;

    private String title;

    private Integer bizType;

    private TradeModeEnum tradeMode;

    private InvoiceTypeEnum formatReference;

    private Class<? extends PrintDTO> printClass;

    private PrintDTO mockPrintDTO;

    private Class<? extends FormatDTO> formatClass;

    private FormatDTO defaultFormatDTO;

    InvoiceTypeEnum(Integer type, String name, String title,
                    Integer bizType, TradeModeEnum tradeMode, InvoiceTypeEnum formatReference,
                    Class<? extends PrintDTO> printClass, Class<? extends FormatDTO> formatClass) {
        this.type = type;
        this.name = name;
        this.title = title;
        this.bizType = bizType;
        this.tradeMode = tradeMode;
        this.formatReference = formatReference;
        this.printClass = printClass;
        this.mockPrintDTO = applyMock(printClass);
        if (this.mockPrintDTO != null) {
            this.mockPrintDTO.setInvoiceType(type);
        }
        this.formatClass = formatClass;
        this.defaultFormatDTO = applyDefaults(formatClass);
        if (this.defaultFormatDTO != null) {
            this.defaultFormatDTO.setInvoiceType(type);
        }
    }

    private static PrintDTO applyMock(Class<? extends PrintDTO> printClass) {
        if (!PrintDataMockito.class.isAssignableFrom(printClass)) {
            return null;
        }
        try {
            Constructor<? extends PrintDTO> constructor = printClass.getConstructor();
            constructor.setAccessible(true);
            PrintDTO printDTO = constructor.newInstance();
            ((PrintDataMockito) printDTO).applyMock();
            return printDTO;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static FormatDTO applyDefaults(Class<? extends FormatDTO> formatClass) {
        if (formatClass == null || !FormatDefaults.class.isAssignableFrom(formatClass)) {
            return null;
        }
        try {
            Constructor<? extends FormatDTO> constructor = formatClass.getConstructor();
            constructor.setAccessible(true);
            FormatDTO formatDTO = constructor.newInstance();
            formatDTO.applyDefault();
            return formatDTO;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getTitle() {
        return title;
    }

    public Integer getBizType() {
        return bizType;
    }

    public TradeModeEnum getTradeMode() {
        return tradeMode;
    }

    public InvoiceTypeEnum getFormatReference() {
        return formatReference == null ? this : formatReference;
    }

    public boolean isFrontPrinter() {
        return BusinessTypeEnum.FRONT_PRINTER
                .equals(BusinessTypeEnum.ofType(bizType));
    }

    public boolean isKitchenPrinter() {
        return BusinessTypeEnum.KITCHEN_PRINTER
                .equals(BusinessTypeEnum.ofType(bizType));
    }

    public boolean isLabelPrinter() {
        return BusinessTypeEnum.LABEL_PRINTER
                .equals(BusinessTypeEnum.ofType(bizType));
    }

    public BusinessTypeEnum getBusinessType() {
        return BusinessTypeEnum.ofType(bizType);
    }

    /**
     * 根据type值匹配相应的枚举值
     *
     * @param type
     * @return
     */
    public static InvoiceTypeEnum ofType(Integer type) {
        for (InvoiceTypeEnum invoiceTypeEnum : InvoiceTypeEnum.values()) {
            if (Objects.equals(invoiceTypeEnum.getType(), type)) {
                return invoiceTypeEnum;
            }
        }
        throw new BusinessException("不支持的票据类型：" + type);
    }

    /**
     * 获取匹配票据类型的名字
     *
     * @param type
     * @return
     */
    public static String getInvoiceName(Integer type) {
        return ofType(type).getName();
    }

    public <F extends PrintDTO> F resolvePrint(String jsonStr) {
        return (F) JacksonUtils.toObject(printClass, jsonStr);
    }

    public <F extends FormatDTO> F resolveFormat(String jsonStr) {
        F formatDTO = (F) JacksonUtils.toObject(formatClass, jsonStr);
        formatDTO.applyDefault();
        return formatDTO;
    }

    public <F extends PrintDTO> F mockPrint() {
        if (this.mockPrintDTO != null) {
            this.mockPrintDTO.setCreateTime(DateTimeUtils.nowMillis());
        }
        return (F) mockPrintDTO;
    }

    public <F extends FormatDTO> F defaultFormat() {
        return (F) defaultFormatDTO;
    }

    public boolean isFormatSupported() {
        return defaultFormatDTO != null;
    }

    public static <F extends PrintDTO> F resolvePrintBy(String jsonStr) {
        Integer invoiceType = (Integer) JacksonUtils.toJSONObject(jsonStr).get("invoiceType");
        return (F) resolvePrintBy(invoiceType, jsonStr);
    }

    public static <F extends PrintDTO> F resolvePrintBy(Integer invoiceType, String jsonStr) {
        return (F) ofType(invoiceType).resolvePrint(jsonStr);
    }

    public static <F extends FormatDTO> F resolveFormatBy(String jsonStr) {
        Integer invoiceType = (Integer) JacksonUtils.toJSONObject(jsonStr).get("invoiceType");
        return (F) ofType(invoiceType).resolveFormat(jsonStr);
    }

    public static <F extends FormatDTO> F defaultFormatBy(Integer invoiceType) {
        return (F) ofType(invoiceType).defaultFormat();
    }

    public static <F extends FormatDTO> F defaultFormatBy(String jsonStr) {
        Integer invoiceType = (Integer) JacksonUtils.toJSONObject(jsonStr).get("invoiceType");
        return (F) defaultFormatBy(invoiceType);
    }

    public static <F extends PrintDTO> F mockPrintBy(Integer invoiceType) {
        return (F) ofType(invoiceType).mockPrint();
    }

    public static TradeModeEnum getTradeMode(PrintDTO printDto) {
        InvoiceTypeEnum invoiceTypeEnum = ofType(printDto.getInvoiceType());
        if (!TradeModeEnum.DYNAMIC.equals(invoiceTypeEnum.getTradeMode())) {
            return invoiceTypeEnum.getTradeMode();
        }
        return TradeModeEnum.ofMode(((TradeModeAware) printDto).getTradeMode());
    }

    public static String getInvoiceTtile(Integer type) {
        return ofType(type).getTitle();
    }

    public static String converInvoiceTtile(List<String> invoiceTypeList, String separator) {
        List<String> typeList = invoiceTypeList.stream()
                .map(invoiceType -> InvoiceTypeEnum.getInvoiceTtile(Integer.valueOf(invoiceType)))
                .collect(Collectors.toList());
        return String.join(separator, typeList);
    }

}
