package com.holderzone.saas.store.enums.user;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 隐藏报表权限
 */
@Getter
@AllArgsConstructor
public enum AuthorityReportHideEnum {

    MT_TAKEAWAY("report_hide_meituan_takeaway", "美团外卖", "美团外卖"),

    ELE_TAKEAWAY("report_hide_ele_takeaway", "饿了么外卖", "饿了么外卖"),

    TCD_TAKEAWAY("report_hide_tcd_takeaway", "赚餐外卖", "赚餐自营外卖"),

    JD_TAKEAWAY("report_hide_jd_takeaway", "京东外卖", "京东外卖"),

    MT_GROUPON("report_hide_meituan_groupon", "美团团购", "美团团购"),

    TIKTOK_GROUPON("report_hide_tiktok_takeaway", "抖音团购", "抖音团购"),

    ;

    private final String code;

    private final String name;

    private final String otherName;


    public static List<String> getNamesByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        codes = codes.stream().distinct().collect(Collectors.toList());
        List<String> names = Lists.newArrayList();
        for (AuthorityReportHideEnum hideEnum : values()) {
            if (codes.contains(hideEnum.getCode())) {
                names.add(hideEnum.getName());
            }
        }
        if (codes.contains(TCD_TAKEAWAY.getCode())) {
            names.add(TCD_TAKEAWAY.getOtherName());
        }
        return names;
    }
}
