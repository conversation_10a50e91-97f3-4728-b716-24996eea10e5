package com.holderzone.saas.store.enums.weixin;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 我的就餐状态对应
 */
@Getter
@AllArgsConstructor
public enum WxBrandUserOrderStateEnum {

	BRAND_USER_ORDER_PENDING(0,"待确认"),
	BRAND_USER_ORDER_CANCEL(3,"已取消"),
	BRAND_USER_ORDER_UNPAID(5,"待支付"),
	BRAND_USER_ORDER_COMPLETE(6,"已完成");
	private Integer code;
	private String msg;

	public static String getMsg(Integer code) {
		WxBrandUserOrderStateEnum[] values = WxBrandUserOrderStateEnum.values();
		for (WxBrandUserOrderStateEnum wxBrandUserOrderStateEnum : values) {
			if (code.equals(wxBrandUserOrderStateEnum.getCode())) {
				return wxBrandUserOrderStateEnum.getMsg();
			}
		}
		return "没有对应枚举:" + code;
	}
}
