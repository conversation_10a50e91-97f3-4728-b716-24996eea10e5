package com.holderzone.saas.store.enums.report;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/2/26
 * @description 展示类型枚举
 */
@Getter
public enum ShowTypeEnum {

    SUMMARIZING(1,"按汇总展示"),

    SINGLE(2,"按单店展示"),

    SINGLE_DAY(3,"按单日单店展示"),

    ;

    private final int code;

    private final String desc;

    ShowTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
