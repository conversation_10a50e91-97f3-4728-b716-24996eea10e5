package com.holderzone.saas.store.enums.print;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/27
 * @description 用途小票类型
 */
@Getter
public enum UseInvoiceTypeEnum {

    POINT_MENU(1, "后厨点菜单"),

    CHECK_OUT(2, "结账单"),
    ;

    private final Integer code;
    private final String desc;

    UseInvoiceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
