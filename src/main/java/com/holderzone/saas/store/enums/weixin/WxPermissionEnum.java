package com.holderzone.saas.store.enums.weixin;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxPermissionEnum
 * @date 2019/03/04 14:37
 * @description 微信公众号权限集合
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum WxPermissionEnum {
    MESSAGE_PERMISSION(1, "消息管理权限"),
    USER_PERMISSION(2, "用户管理权限"),
    ACCOUNT_PERMISSION(3, "帐号服务权限"),
    WEB_PERMISSION(4, "网页服务权限"),
    MINI_STORE_PERMISSION(5, "微信小店权限"),
    CUSTOMER_SERVICE_PERMISSION(6, "微信多客服权限"),
    MASS_NOTIFY_PERMISSION(7, "群发与通知权限"),
    CARD_COUPON_PERMISSION(8, "微信卡券权限"),
    SWEEP_PERMISSION(9, "微信扫一扫权限"),
    WIFI_PERMISSION(10, "微信连WIFI权限"),
    MATERIAL_PERMISSION(11, "素材管理权限"),
    SHAKE_PERMISSION(12, "微信摇周边权限"),
    STORE_PERMISSION(13, "微信门店权限"),
    PAY_PERMISSION(14, "微信支付权限"),
    CUSTOMIZE_MENU_PERMISSION(15, "自定义菜单权限"),
    MINI_ACCOUNT_PERMISSION(17, "帐号管理权限"),
    MINI_DEVELOP_PERMISSION(18, "开发管理与数据分析权限"),
    MINI_CUSTOMER_SERVICE_PERMISSION(19, "客服消息管理权限"),
    CITY_SERVICE_PERMISSION(22, "城市服务接口权限"),
    BIND_PERMISSION(24, "微信开放平台帐号绑定权限"),
    ELE_INVOICE_PERMISSION(26, "微信电子发票权限"),
    MINI_BASE_INFO_PERMISSION(30, "小程序基本信息设置权限"),
    MINI_AROUND_INFO_PERMISSION(37, "小程序附近地点权限集"),
    MINI_PLUGIN_PERMISSION(40, "小程序插件管理权限"),
    DEFAULT_PERMISSION(0, "");

    private Integer code;

    private String desc;

    public static String getDescByCode(Integer code) {
        return Arrays.stream(WxPermissionEnum.values()).filter(p -> p.getCode().equals(code))
                .findFirst().orElse(DEFAULT_PERMISSION).getDesc();
    }
}
