package com.holderzone.saas.store.enums.takeaway;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 抖音订单取消 申请来源
 */
@Getter
@AllArgsConstructor
public enum TikTokRefundApplySourceEnum {

    USER("USER", "用户发起"),
    MERCHANT("MERCHANT", "商户发起发起"),
    CUSTOMER_SERVICE_STAFF("CUSTOMER_SERVICE_STAFF", "客服/运营"),
    JUDGMENT("JUDGMENT", " 判责（x项目）"),
    SYSTEM("SYSTEM", "系统"),
    REFUND_TIMEOUT_ARBITRATE("REFUND_TIMEOUT_ARBITRATE", "履约审核超时自动发起仲裁"),
    PAY_TIMEOUT_CANCEL("PAY_TIMEOUT_CANCEL", "过期自动退"),
    MERCHANT_CONFIRM_FAIL_REFUND("MERCHANT_CONFIRM_FAIL_REFUND", "商家发货确认失败"),
    EXPIRE_REFUND("EXPIRE_REFUND", "过期退"),
    PAY_CALLBACK_BUT_CANCELED("PAY_CALLBACK_BUT_CANCELED", "回调时超订单被取消导致的超付"),
    USER_FULFILMENT_REFUND("USER_FULFILMENT_REFUND", "用户履约后退款"),
    CUSTOMER_SERVICE_STAFF_FULFILMENT_REFUND("CUSTOMER_SERVICE_STAFF_FULFILMENT_REFUND", "运营/客服履约后退款"),
    MERCHANT_CONFIRM_REJECT_REFUND("MERCHANT_CONFIRM_REJECT_REFUND", "商家拒单自动退款"),
    MERCHANT_CONFIRM_TIMEOUT_REFUND("MERCHANT_CONFIRM_TIMEOUT_REFUND", "商家确认(接单)超时自动退款"),
    MERCHANT_CONFIRM_CANCEL_REFUND("MERCHANT_CONFIRM_CANCEL_REFUND", "商家取消订单自动退款"),
    MERCHANT_CONFIRM_CANCEL_REFUND_FULFILMEN("MERCHANT_CONFIRM_CANCEL_REFUND_FULFILMEN", "商家取消订单自动退款[40字符限制]"),
    MERCHANT_FULFILMENT_REFUND("MERCHANT_FULFILMENT_REFUND", "商家核销后退款"),
    DELIVER_CANCEL_REFUND("DELIVER_CANCEL_REFUND", "运单取消导致退款"),
    DATA_FIX("DATA_FIX", "数据修复"),
    ;

    /**
     * 申请来源
     */
    private final String source;

    /**
     * 描述
     */
    private final String description;

    public static TikTokRefundApplySourceEnum ofSource(String source) {
        for (TikTokRefundApplySourceEnum value : values()) {
            if (value.source.equalsIgnoreCase(source)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的applySource[" + source + "]");
    }
}
