package com.holderzone.saas.store.enums.user;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023年08月04日 16:27
 * @description 授权类型
 */
@Getter
public enum AuthorityTypeEnum {

    NORMAL(0, "正常权限"),

    FACE(1, "人脸授权"),

    AUTHORITY_CODE(2, "授权码授权"),

    ;

    private int code;

    private String desc;

    private AuthorityTypeEnum() {
    }

    AuthorityTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
