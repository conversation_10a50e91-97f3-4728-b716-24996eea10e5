package com.holderzone.saas.store.enums.trade;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/15
 * @since 1.8
 */
@Getter
@AllArgsConstructor
public enum RefundCodeEnum {

    REFUND_TYPE_DINEIN_PART_REFUND(1, "正餐部分退款"),
    REFUND_TYPE_DINEIN_RECOVERY(2, "正餐反结账"),
    REFUND_TYPE_FAST_PART_REFUND(3, "快销部分退款"),
    REFUND_TYPE_FAST_RECOVERY(4, "快销反结账"),
    REFUND_TYPE_TOTAL(5, "合计");

    private final int code;
    private final String desc;
}
