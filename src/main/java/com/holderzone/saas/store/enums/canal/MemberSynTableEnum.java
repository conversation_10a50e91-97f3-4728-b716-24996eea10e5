package com.holderzone.saas.store.enums.canal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberSynTableEnum
 * @date 2019/06/28 14:57
 * @description //TODO  member 同步表名枚举
 * @program holder-saas-aggregation-app
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum MemberSynTableEnum {


    HSI_TYPE("hsi_type", "商品分类"),
    HSI_ITEM("hsi_item", "商品信息"),
    HSI_SKU("hsi_sku", "商品规格"),;


    private String tableName;

    private String desc;



}
