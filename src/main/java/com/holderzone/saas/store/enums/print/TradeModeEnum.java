package com.holderzone.saas.store.enums.print;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @date 2018/12/17 14:31
 */
public enum TradeModeEnum {

    DYNAMIC(-2, "tradeMode由请求参数动态决定"),

    NULL(-1, "tradeMode不会影响业务决策"),

    DINE(0, "堂食，包括微信堂食"),

    SNACK(1, "快销，包括微信快销"),

    TAKEOUT(2, "外卖"),

    ;

    private Integer mode;

    private String desc;

    TradeModeEnum(Integer mode, String desc) {
        this.mode = mode;
        this.desc = desc;
    }

    public Integer getMode() {
        return mode;
    }

    public String getDesc() {
        return desc;
    }

    public static TradeModeEnum ofMode(Integer mode) {
        for (TradeModeEnum value : values()) {
            if (value.mode.equals(mode)) {
                return value;
            }
        }
        // todo 等其他服务传tradeMode过来后，抛出异常，而不是返回默认值
//        return SNACK;
        throw new BusinessException("不支持的TradeMode");
    }
}
