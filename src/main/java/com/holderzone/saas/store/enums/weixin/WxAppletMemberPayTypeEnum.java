package com.holderzone.saas.store.enums.weixin;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 微信小程序会员支付类型
 */
@AllArgsConstructor
@Getter
public enum WxAppletMemberPayTypeEnum {

    STORED_AMOUNT(6, "储值金额支付"),
    INCOME_AMOUNT(4, "收益余额支付"),
    ZERO_AMOUNT(0, "0元支付");

    private final Integer type;
    private final String message;

    public static boolean isExist(Integer type) {
        if (Objects.isNull(type)) {
            return false;
        }
        for (WxAppletMemberPayTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return true;
            }
        }
        return false;
    }
}
