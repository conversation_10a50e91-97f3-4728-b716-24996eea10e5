package com.holderzone.saas.store.enums.marketing;

/**
 * <AUTHOR>
 * @date 2024/5/29
 * @description 活动规则 共享互斥关系 0-互斥 1-共享
 */
public enum RelationRuleEnum {

    /**
     * 互斥
     */
    REPEL(0,"互斥"),

    /**
     * 共享
     */
    SHARE(1,"共享"),

    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    RelationRuleEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }
}
