package com.holderzone.saas.store.enums.member;

/**
 * <AUTHOR>
 * @description 登录状态枚举 0密码错误 1登录成功 2手机号未注册
 * @date 2021/8/9 9:46
 */
public enum LoginStateEnum {

    WRONG_PASSWORD(0, "密码错误"),
    LOGIN_SUCCESSFUL(1, "登录成功"),
    PHONE_NUMBER_NOT_REGISTERED(2, "手机号未注册"),
    VERIFICATION_CODE_ERROR(3, "验证码错误"),
    VERIFICATION_CODE_EXPIRED(4, "验证码已失效"),
    WECHAT_NOT_BOUND_PHONE_NUMBER(5, "微信未绑定手机号"),
    WECHAT_AUTHORIZATION_FAILED(6, "微信授权失败"),
    ;

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return desc;
    }

    LoginStateEnum() {
    }

    LoginStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
