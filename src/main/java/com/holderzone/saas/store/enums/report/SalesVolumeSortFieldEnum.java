package com.holderzone.saas.store.enums.report;

/**
 * 门店商品销量排序字段枚举
 */
public enum SalesVolumeSortFieldEnum {
    SPOT_RATE("spotRate", "点单率"),
    SALES_PROPORTION("salesProportion", "销售额占比"),
    SALES_VOLUME("salesVolume", "销售量"),
    REFUND_COUNT("refundCount", "退款数量"),
    FREE_COUNT("freeCount", "赠送数量"),
    SALES_AMOUNT("salesAmount", "销售金额");

    private final String code;
    private final String desc;

    SalesVolumeSortFieldEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SalesVolumeSortFieldEnum getByCode(String code) {
        for (SalesVolumeSortFieldEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 