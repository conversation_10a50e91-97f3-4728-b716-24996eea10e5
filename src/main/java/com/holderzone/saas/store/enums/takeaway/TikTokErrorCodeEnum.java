package com.holderzone.saas.store.enums.takeaway;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TikTokErrorCodeEnum {

    UNKNOWN(-1, "未知"),
    SUCCESS(0, "成功"),
    ACCESS_TOKEN_ERROR(2190002, "access_token无效"),
    TAKEOUT(2190004, "接口权限不足"),
    ACCESS_TOKEN_EXPIRE(2190008, "access_token过期,请刷新或重新授权"),
    PARAM_ERROR(2119001, "参数不合法"),
    SYSTEM_ERROR(2119002, "系统繁忙，请稍候再试"),
    REQUEST_LIMIT(2119003, "请求太过频繁，请稍后再试"),
    APP_NOT_AUTH(2119005, "应用未获商家授权"),
    BUSINESS_ERROR(5000001, "联系抖音处理"),
    MERCHANT_STORE_RELATION_ERROR(3000004, "门店未查询到商家"),
    CHECK_PARAM(4000004, "account_id，third_id，poi_id必须选择一个进行查询"),
    ;

    /**
     * 错误码，非 0 表示失败
     */
    private final int errorCode;

    /**
     * 错误描述
     */
    private final String description;

    public static TikTokErrorCodeEnum ofErrorCode(int errorCode) {
        for (TikTokErrorCodeEnum value : TikTokErrorCodeEnum.values()) {
            if (errorCode == value.errorCode) {
                return value;
            }
        }
        return TikTokErrorCodeEnum.UNKNOWN;
    }
}
