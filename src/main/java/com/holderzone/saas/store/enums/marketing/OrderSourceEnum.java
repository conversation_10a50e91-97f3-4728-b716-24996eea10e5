package com.holderzone.saas.store.enums.marketing;

import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 活动订单来源
 */
@Getter
@AllArgsConstructor
public enum OrderSourceEnum {

    /**
     * 一体机
     */
    AIO(1, "一体机"),

    /**
     * 微信小程序
     */
    WECHAT_APPLET(2, "微信小程序"),

    /**
     * 微信公众号
     */
    WECHAT_H5(3, "微信公众号"),

    /**
     * 支付宝小程序
     */
    ALIPAY(4, "支付宝小程序"),

    UNKNOWN(999, "未知"),
    ;


    private final int code;

    private final String des;

    public static String getByCode(int code) {
        for (OrderSourceEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDes();
            }
        }
        return UNKNOWN.getDes();
    }


    public static int transferCode(int code) {
        if (BaseDeviceTypeEnum.All_IN_ONE.getCode() == code) {
            return AIO.getCode();
        }
        if (BaseDeviceTypeEnum.TCD.getCode() == code || BaseDeviceTypeEnum.WECHAT_MINI.getCode() == code) {
            return WECHAT_APPLET.getCode();
        }
        if (BaseDeviceTypeEnum.WECHAT.getCode() == code) {
            return WECHAT_H5.getCode();
        }
        if (BaseDeviceTypeEnum.ALI.getCode() == code) {
            return ALIPAY.getCode();
        }
        return UNKNOWN.getCode();
    }

}
