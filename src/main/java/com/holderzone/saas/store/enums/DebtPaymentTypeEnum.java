package com.holderzone.saas.store.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.logging.log4j.util.Strings;

import java.util.Objects;


/**
 * 挂账还款支付方式
 */
@Getter
@AllArgsConstructor
public enum DebtPaymentTypeEnum {

    CASH(0, "现金"),
    ALIPAY(1, "支付宝"),
    WECHAT(2, "微信支付"),
    BANK(3, "银行卡"),
    CHEQUE(4, "支票支付"),
    MEMBER(5, "会员余额支付"),
    REFUND(30, "退款"),
    ;

    private final int code;
    private final String desc;

    public static String getDescByCode(Integer code) {
        if (Objects.isNull(code)) {
            return Strings.EMPTY;
        }
        for (DebtPaymentTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return Strings.EMPTY;
    }
}
