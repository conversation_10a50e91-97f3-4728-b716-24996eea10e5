package com.holderzone.saas.store.enums.item;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishUnitEnum
 * @date 2018/09/05 10:32
 * @description //TODO
 * @program holder-saas-store-order
 */
@ApiModel(value = "标签类")
public enum TagEnum {

    SIGN("isSign", "招牌"),
    BESTSELLER("isBestseller", "热销"),
    NEW("isNew", "新品"),
    RECOMMEND("isRecommend", "推荐"),
    ;

    @ApiModelProperty(value = "字段值，之后标签可以自由配置了就是GUID")
    private String id;
    @ApiModelProperty(value = "标签显示名称")
    private String name;

    TagEnum(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public static String getName(String id) {
        for (TagEnum tagEnum : TagEnum.values()) {
            if (tagEnum.getId() == id) {
                return tagEnum.getName();
            }
        }
        return null;
    }

    public static String getTagIds() {
        return Arrays.asList(TagEnum.values()).stream().map(TagEnum::getId).collect(Collectors.toList()).toString().replace(" ", "").replaceAll("\\[([^\\]]*)\\]", "$1");
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "TagEnum{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                '}';
    }

    public static void main(String[] args) {
        System.out.println(getTagIds());
    }
}
