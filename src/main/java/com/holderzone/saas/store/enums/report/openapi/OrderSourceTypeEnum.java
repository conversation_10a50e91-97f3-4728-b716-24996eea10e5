package com.holderzone.saas.store.enums.report.openapi;


public enum OrderSourceTypeEnum {

    ONLINE(1, "线上点餐"),

    OFFLINE(2, "线下点餐"),

    ;

    private final int code;

    private final String desc;

    OrderSourceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
