package com.holderzone.saas.store.enums.takeaway;

import lombok.Getter;

/**
 * OrderType=0时的OrderSubType枚举
 *
 * <AUTHOR>
 * @date 2025/6/5
 * @since 1.8
 */
@Getter
public enum OrderSubTypeEnum {

    mt(0, "美团外卖"),
    ele(1, "饿了么外卖"),
    own(5, "自营外卖"),
    tcd(6, "赚餐外卖");

    private final Integer code;
    private final String desc;

    OrderSubTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(int code) {
        for (OrderSubTypeEnum typeEnum : OrderSubTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum.desc;
            }
        }
        return null;
    }

    public static Integer getCodeByDesc(String desc) {
        for (OrderSubTypeEnum typeEnum : OrderSubTypeEnum.values()) {
            if (typeEnum.getDesc().equals(desc)) {
                return typeEnum.code;
            }
        }
        return null;
    }
}
