package com.holderzone.saas.store.enums.print;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterTypeEnum
 * @date 2018/9/18 16:04
 * @description 打印机类型枚举
 * @program holder-saas-store-print
 */
public enum PrinterTypeEnum {

    LOCAL_PRINTER(0, "本地打印机"),

    WLAN_PRINTER(1, "网络打印机"),

    USB_PRINTER(2, "USB打印机"),

    CLOUD_PRINTER(3, "云打印机"),
    ;

    private Integer type;

    private String desc;

    PrinterTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static PrinterTypeEnum ofType(Integer type) {
        for (PrinterTypeEnum printerTypeEnum : PrinterTypeEnum.values()) {
            if (printerTypeEnum.type.equals(type)) {
                return printerTypeEnum;
            }
        }
        throw new BusinessException("不支持的PrinterType：" + type);
    }

    public static String getPrinterTypeDesc(Integer type) {
        return ofType(type).getDesc();
    }
}
