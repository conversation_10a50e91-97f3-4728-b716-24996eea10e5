package com.holderzone.saas.store.enums.weixin;

/**
 * <AUTHOR>
 * @date 2024/5/31
 * @description 优惠类型
 */
public enum PreferentialTypeEnum {

    /**
     * 会员价
     */
    MEMBER_PRICE(1, "会员价"),

    /**
     * 会员折扣
     */
    MEMBER_DISCOUNTS(2, "会员折扣"),

    /**
     * 限时特价
     */
    LIMITED_TIME_SPECIAL(3, "限时特价"),

    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    PreferentialTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

}
