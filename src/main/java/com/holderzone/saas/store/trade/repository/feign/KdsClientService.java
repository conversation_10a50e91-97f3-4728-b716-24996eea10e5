package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DistributeItemDTO;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className KdsClientService
 * @date 2018/09/30 11:41
 * @description 微信调用
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = KdsClientService.FallBack.class)
public interface KdsClientService {

    @PostMapping("/kitchen_item/prepare")
    void prepare(@RequestBody ItemPrepareReqDTO itemPrepareReqDTO);

    @PostMapping("/kitchen_item/call")
    void call(@RequestBody ItemCallUpReqDTO itemCallUpReqDTO);

    @PostMapping("/kitchen_item/urge")
    void urge(@RequestBody ItemUrgeReqDTO itemUrgeReqDTO);

    @PostMapping("/kitchen_item/remark")
    void remark(@RequestBody OrderRemarkReqDTO orderRemarkReqDTO);

    @PostMapping("/kitchen_item/change_table")
    void changeTable(@RequestBody OrderTableReqDTO orderTableReqDTO);

    @PostMapping("/kitchen_item/refund")
    void refund(@RequestBody ItemBatchRefundReqDTO itemBatchRefundReqDTO);

    @PostMapping("/kitchen_item/query_by_order_item")
    @ApiOperation(value = "根据订单商品查询kds")
    List<KitchenItemRespDTO> queryByOrderItem(@RequestBody ItemBatchRefundReqDTO query);

    @PostMapping("/kitchen_item/distribute")
    @ApiOperation(value = "菜品出堂，参数要求同制作菜品")
    List<PrdDstItemDTO> distribute(@RequestBody @Validated ItemStateTransReqDTO itemStateTransReqDTO);

    @PostMapping("/kitchen_item/distribute_batch")
    @ApiOperation(value = "循环菜品出堂，参数要求同制作菜品")
    void batchDistribute(@RequestBody @Validated List<ItemStateTransReqDTO> reqDTOList);

    @PostMapping("/kitchen_item/query_by_order")
    @ApiOperation(value = "根据订单查询kds未出堂商品")
    List<PrdDstItemDTO> queryByOrder(@RequestBody PrdDstItemQueryDTO query);

    @ApiOperation(value = "根据sku查询出堂商品配置")
    @PostMapping("/distribute/query_distribute_item_by_sku")
    List<DistributeItemDTO> queryDistributeItemBySku(@RequestBody SingleDataDTO reqDTO);

    @Component
    class FallBack implements FallbackFactory<KdsClientService> {
        private static final Logger logger = LoggerFactory.getLogger(KdsClientService.FallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public KdsClientService create(Throwable throwable) {
            return new KdsClientService() {

                @Override
                public void prepare(ItemPrepareReqDTO itemPrepareReqDTO) {
                    logger.error("kds菜品入厨房异常，e={}", throwable.getMessage());
                    throw new ParameterException("kds菜品入厨房调用异常");
                }

                @Override
                public void call(ItemCallUpReqDTO itemCallUpReqDTO) {
                    logger.error("kds叫起异常，e={}", throwable.getMessage());
                    throw new ParameterException("kds叫起调用异常");
                }

                @Override
                public void urge(ItemUrgeReqDTO itemUrgeReqDTO) {
                    logger.error("kds催菜异常，e={}", throwable.getMessage());
                    throw new ParameterException("kds催菜调用异常");
                }

                @Override
                public void remark(OrderRemarkReqDTO orderRemarkReqDTO) {
                    logger.error("kds修改备注异常，e={}", throwable.getMessage());
                    throw new ParameterException("kds修改备注调用异常");
                }

                @Override
                public void changeTable(OrderTableReqDTO orderTableReqDTO) {
                    logger.error("kds换台异常，e={}", throwable.getMessage());
                    throw new ParameterException("kds换台调用异常");
                }

                @Override
                public void refund(ItemBatchRefundReqDTO itemBatchRefundReqDTO) {
                    logger.error("kds退菜异常，e={}", throwable.getMessage());
                    throw new ParameterException("kds退菜调用异常");
                }

                @Override
                public List<KitchenItemRespDTO> queryByOrderItem(ItemBatchRefundReqDTO query) {
                    logger.error(HYSTRIX_PATTERN, "queryByOrderItem",
                            JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PrdDstItemDTO> distribute(ItemStateTransReqDTO itemStateTransReqDTO) {
                    logger.error(HYSTRIX_PATTERN, "distribute",
                            JacksonUtils.writeValueAsString(itemStateTransReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void batchDistribute(List<ItemStateTransReqDTO> reqDTOList) {
                    logger.error(HYSTRIX_PATTERN, "batchDistribute",
                            JacksonUtils.writeValueAsString(reqDTOList),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PrdDstItemDTO> queryByOrder(PrdDstItemQueryDTO query) {
                    logger.error(HYSTRIX_PATTERN, "queryByOrder",
                            JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<DistributeItemDTO> queryDistributeItemBySku(SingleDataDTO reqDTO) {
                    logger.error(HYSTRIX_PATTERN, "queryDistributeItemBySku",
                            JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
