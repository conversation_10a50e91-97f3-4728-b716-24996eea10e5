package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.order.OrderDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.request.item.UpdateOrderItemInfoDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-order
 */
public interface FastFoodService {

    EstimateItemRespDTO addItem(CreateFastFoodReqDTO createFastFoodReqDTO);

    EstimateItemRespDTO createAppletOrder(CreateFastFoodReqDTO createFastFoodReqDTO);

    void updateFastFoodOrder(CreateFastFoodReqDTO createFastFoodReqDTO);

    void deleteItems(String orderGuid);

    /**
     * 删除门店当前快餐自动号牌数
     */
    void removeStoreAutoMark(String storeGuid);

    /**
     * 修改订单上的会员消费guid
     */
    void updateMemberConsumptionGuid(OrderDTO orderDTO);

    void updateOrderItemInfo(UpdateOrderItemInfoDTO orderItemInfoDTO);
}
