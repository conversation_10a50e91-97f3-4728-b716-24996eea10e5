package com.holderzone.saas.store.trade.factory;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.tcd.TcdAddOrderReqDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.RecoveryTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeTypeEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.ItemUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TcdFactory
 * @date 2019/11/26 11:18
 * @description //通吃岛factory
 * @program holder-saas-store-trade
 */
@Component
public class TcdFactory {

    private static OrderTransform orderTransform = OrderTransform.INSTANCE;

    private final DynamicHelper dynamicHelper;

    @Autowired
    public TcdFactory(DynamicHelper dynamicHelper) {
        this.dynamicHelper = dynamicHelper;
    }

    public OrderDO createOrderDO(TcdAddOrderReqDTO tcdAddOrderReqDTO, Map<String, String> orderGuids) {
        OrderDO orderDO = orderTransform.tcdAddOrderReqDTO2orderDO(tcdAddOrderReqDTO);
        Long orderGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER);
        orderGuids.put(String.valueOf(orderGuid), String.valueOf(orderDO.getGuid()));
        orderDO.setGuid(orderGuid);
        orderDO.setState(tcdAddOrderReqDTO.getState());
        orderDO.setTradeMode(TradeModeEnum.FAST.getCode());
        orderDO.setRecoveryType(RecoveryTypeEnum.GENERAL.getCode());
        orderDO.setDeviceType(BaseDeviceTypeEnum.TCD.getCode());
        tcdAddOrderReqDTO.setGuid(String.valueOf(orderDO.getGuid()));
        return orderDO;
    }


    public OrderSubsidiaryDO createOrderSubsidiaryDO(TcdAddOrderReqDTO tcdAddOrderReqDTO, Map<String, String> orderGuids) {
        OrderSubsidiaryDO orderSubsidiaryDO = new OrderSubsidiaryDO();
        if (StringUtils.isEmpty(orderGuids.get(String.valueOf(tcdAddOrderReqDTO.getGuid())))) {
            return orderSubsidiaryDO;
        } else {
            orderSubsidiaryDO.setThirdPartyId(orderGuids.get(String.valueOf(tcdAddOrderReqDTO.getGuid())));
            orderSubsidiaryDO.setGuid(Long.valueOf(tcdAddOrderReqDTO.getGuid()));
        }
        return orderSubsidiaryDO;
    }


    public List<OrderItemDO> createOrderItemDOS(TcdAddOrderReqDTO tcdAddOrderReqDTO, Map<String, String> itemGuids) {
        //替换为新的guid
        List<OrderItemDO> orderItemDOList = orderTransform.dineInItemDTOS2OrderItemDOS(tcdAddOrderReqDTO.getDineInItemDTOS());
        orderItemDOList = orderItemDOList.stream().peek(OrderItemDO -> {
            Long itemGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER_ITEM);
            itemGuids.put(String.valueOf(OrderItemDO.getGuid()), String.valueOf(itemGuid));
            OrderItemDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER_ITEM));
            OrderItemDO.setOrderGuid(Long.valueOf(tcdAddOrderReqDTO.getGuid()));
        }).collect(Collectors.toList());

        //处理子单
        orderItemDOList = orderItemDOList.stream().peek(OrderItemDO -> {
            if (ItemUtil.isOutOfGroup(OrderItemDO.getItemType())) {
                if (!StringUtils.isEmpty(OrderItemDO.getParentItemGuid()) && !"0".equals(OrderItemDO.getParentItemGuid())) {
                    OrderItemDO.setParentItemGuid(Long.valueOf(itemGuids.get(String.valueOf(OrderItemDO.getParentItemGuid()))));
                }
            }
        }).collect(Collectors.toList());
        return orderItemDOList;
    }

    public List<ItemAttrDO> createItemAttrDOS(TcdAddOrderReqDTO tcdAddOrderReqDTO, Map<String, String> itemGuids) {
        List<ItemAttrDO> itemAttrDOList = Collections.emptyList();
        if (ObjectUtils.isEmpty(tcdAddOrderReqDTO.getDineInItemDTOS())) {
            return itemAttrDOList;
        }

        for (Iterator<DineInItemDTO> iterator = tcdAddOrderReqDTO.getDineInItemDTOS().iterator(); iterator.hasNext(); ) {
            DineInItemDTO dineInItemDTO = iterator.next();
            itemAttrDOList = orderTransform.itemAttrDTOS2itemAttrDOS(dineInItemDTO.getItemAttrDTOS());
            if (!ObjectUtils.isEmpty(itemAttrDOList)) {
                itemAttrDOList = itemAttrDOList.stream().peek(ItemAttrDO -> {
                    Long attrGuid = dynamicHelper.generateGuid(GuidKeyConstant.ITEM_ATTR_GUID);
                    ItemAttrDO.setGuid(attrGuid);
                    ItemAttrDO.setOrderGuid(Long.valueOf(tcdAddOrderReqDTO.getGuid()));
                    if (!StringUtils.isEmpty(itemGuids.get(String.valueOf(ItemAttrDO.getOrderItemGuid())))) {
                        String orderItemGuid = itemGuids.get(String.valueOf(ItemAttrDO.getOrderItemGuid()));
                        ItemAttrDO.setOrderItemGuid(Long.valueOf(orderItemGuid));
                    }
                }).collect(Collectors.toList());
            }

        }
        return itemAttrDOList;
    }

    public DiscountDO createDiscountDO(TcdAddOrderReqDTO tcdAddOrderReqDTO) {
        DiscountDO discountDO = orderTransform.tcdAddOrderReqDTO2discountDO(tcdAddOrderReqDTO);
        Long discountGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_DISCOUNT);
        discountDO.setGuid(discountGuid);
        discountDO.setDiscountType(DiscountTypeEnum.TONGCHIDAO.getCode());
        discountDO.setDiscountName(DiscountTypeEnum.TONGCHIDAO.getDesc());
        discountDO.setDiscountState(0);
        discountDO.setStaffName(tcdAddOrderReqDTO.getCreateStaffName());
        discountDO.setOrderGuid(Long.valueOf(tcdAddOrderReqDTO.getGuid()));
        return discountDO;
    }

    public TransactionRecordDO createTransactionRecordDO(TcdAddOrderReqDTO tcdAddOrderReqDTO) {
        TransactionRecordDO transactionRecordDO = orderTransform.tcdAddOrderReqDTO2transactionRecordDO(tcdAddOrderReqDTO);
        Long transactionGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
        transactionRecordDO.setGuid(transactionGuid);
        transactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());

        //
        transactionRecordDO.setPaymentType(PaymentTypeEnum.TONGCHIDAO.getCode());
        transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.TONGCHIDAO.getDesc());
        transactionRecordDO.setOrderGuid(Long.valueOf(tcdAddOrderReqDTO.getGuid()));
        transactionRecordDO.setAmount(tcdAddOrderReqDTO.getActuallyPayFee());
        transactionRecordDO.setDiscountFee(tcdAddOrderReqDTO.getDiscountFee());
        transactionRecordDO.setRefundableFee(BigDecimal.ZERO);
        transactionRecordDO.setState(StateEnum.SUCCESS.getCode());
        transactionRecordDO.setCreateTime(DateTimeUtils.now());
        transactionRecordDO.setStaffName(tcdAddOrderReqDTO.getCreateStaffName());
        return transactionRecordDO;
    }

    public List<AppendFeeDO> createAppendFeeDOS(TcdAddOrderReqDTO tcdAddOrderReqDTO) {
        List<AppendFeeDO> appendFeeDOList = Collections.emptyList();
        if (ObjectUtils.isEmpty(tcdAddOrderReqDTO.getAppendFeeDetailDTOS())) {
            return appendFeeDOList;
        }

        appendFeeDOList = orderTransform.appendFeeDetailDTOS2appendFeeDOS(tcdAddOrderReqDTO.getAppendFeeDetailDTOS());
        appendFeeDOList = appendFeeDOList.stream().peek(AppendFeeDO -> {
            AppendFeeDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_APPEND_FEE));
            AppendFeeDO.setOrderGuid(Long.valueOf(tcdAddOrderReqDTO.getGuid()));
            AppendFeeDO.setStoreGuid(tcdAddOrderReqDTO.getStoreGuid());

        }).collect(Collectors.toList());
        return appendFeeDOList;
    }
}
