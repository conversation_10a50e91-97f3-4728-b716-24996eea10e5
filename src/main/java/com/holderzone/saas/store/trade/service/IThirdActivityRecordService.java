package com.holderzone.saas.store.trade.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.trade.entity.domain.ThirdActivityRecordDO;

import java.util.List;

/**
 * <p>
 * 第三方活动使用记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
public interface IThirdActivityRecordService extends IService<ThirdActivityRecordDO> {

    /**
     * 查询订单下使用的活动
     */
    List<ThirdActivityRecordDTO> listThirdActivityByOrderGuid(String orderGuid);

    /**
     * 查询活动绑定的团购券列表
     */
    List<GrouponListRespDTO> grouponCodesByActivityGuid(String orderGuid, String activityGuid);

    /**
     * 保存第三方活动使用信息
     */
    void saveThirdActivityInfo(RecordThirdActivityInfoReqDTO reqDTO);

    /**
     * 撤销第三方活动记录
     */
    void revokeThirdActivityRecord(String orderGuid, String activityGuid);

    /**
     * 批量查询订单的第三方平台活动记录
     */
    List<ThirdActivityRecordDO> listByOrderGuids(List<String> orderGuids);

    /**
     * 撤销第三方活动记录
     */
    void revokeThirdActivityRecord(List<String> orderGuids);

    /**
     * 根据平台撤销活动（仅适用于非美团、抖音验券的平台活动）
     */
    void revokeThirdActivityRecordByThirdType(String orderGuid, String thirdType);

    /**
     * 校验第三方活动
     */
    void checkRecord(GrouponReqDTO grouponReqDTO);

    /**
     * 团购验券，生成记录
     */
    void saveRecord(GrouponReqDTO grouponReqDTO);

    /**
     * 更新订单使用的活动记录,设置为新的订单guid
     */
    void updateOrderGuid(String orderGuid, String newOrderGuid);

}
