package com.holderzone.saas.store.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.ItemBarCodeReqDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.trade.entity.domain.FreeReturnItemDO;
import com.holderzone.saas.store.trade.entity.domain.ItemAttrDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;
import com.holderzone.saas.store.trade.entity.enums.FreeReturnTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.repository.feign.ItemClientService;
import com.holderzone.saas.store.trade.repository.impls.ItemAttrServiceImpl;
import com.holderzone.saas.store.trade.repository.interfaces.FreeReturnItemService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.DineInPrintService;
import com.holderzone.saas.store.trade.service.ItemQueryService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CalculateServiceImpl
 * @date 2019/01/28 17:28
 * @description
 * @program holder-saas-store-trade
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ItemQueryServiceImpl implements ItemQueryService {

    private final OrderService orderService;

    private final OrderItemService orderItemService;

    private final ItemAttrServiceImpl itemAttrService;

    private final FreeReturnItemService freeReturnItemService;

    private final ItemClientService itemClientService;

    private final DineInPrintService dineInPrintService;

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    @Override
    public List<DineInItemDTO> getAllItems(OrderDO orderDO) {
        OrderDO mainOrderDO = new OrderDO();
        List<String> orderGuidList = Lists.newArrayList();

        //有并单情况
        if (!orderDO.getUpperState().equals(UpperStateEnum.GENERAL.getCode())) {
            if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
                mainOrderDO = orderDO;
            }
            if (orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
                mainOrderDO = orderService.getById(orderDO.getMainOrderGuid());
            }
            orderGuidList.add(String.valueOf(mainOrderDO.getGuid()));
            List<OrderDO> subOrderDOS = orderService.listByMainOrderGuid(String.valueOf(mainOrderDO.getGuid()));
            for (OrderDO subOrderDO : subOrderDOS) {
                orderGuidList.add(String.valueOf(subOrderDO.getGuid()));
            }
        } else {
            orderGuidList.add(orderDO.getGuid().toString());
        }
        List<OrderItemDO> orderItemDOS = orderItemService.listByOrderLists(orderGuidList);

        Map<Long, List<OrderItemDO>> itemListMap = CollectionUtil.toListMap(orderItemDOS, "guid");
        List<ItemAttrDO> itemAttrDOList = new ArrayList<>(itemAttrService.listByItemGuids(new ArrayList<>(itemListMap
                .keySet())));


        //赠送信息
        List<FreeReturnItemDO> freeReturnItemDOS = null;
        if (AmountCalculationUtil.hasFree(orderItemDOS)) {
            Map<Long, OrderItemDO> itemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
            freeReturnItemDOS = freeReturnItemService.list(new
                    LambdaQueryWrapper<FreeReturnItemDO>().in(FreeReturnItemDO::getOrderItemGuid, new ArrayList<>
                    (itemDOMap.keySet())).eq(FreeReturnItemDO::getType, FreeReturnTypeEnum.FREE.getCode()));
        }
        //计算菜品相关
        List<DineInItemDTO> dineInItemDTOS = AmountCalculationUtil.buildItem(orderItemDOS, itemAttrDOList,
                freeReturnItemDOS);
        for (Iterator<DineInItemDTO> iterator = dineInItemDTOS.iterator(); iterator.hasNext(); ) {
            DineInItemDTO dineInItemDTO = iterator.next();
            if (BigDecimalUtil.equal(dineInItemDTO.getCurrentCount(), BigDecimal.ZERO) && BigDecimalUtil.equal
                    (dineInItemDTO.getFreeCount(), BigDecimal.ZERO)) {
                iterator.remove();
            }
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getFreeCount())) {
                List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
                freeItemDTOS.removeIf(freeItemDTO -> BigDecimalUtil.equal(freeItemDTO.getCount(), BigDecimal.ZERO));
            }
        }
        return dineInItemDTOS;
    }

    @Override
    public void printItemBarCode(ItemBarCodeReqDTO itemBarCodeReqDTO) {
        // 查询商品
        ItemStringListDTO querySku = new ItemStringListDTO();
        querySku.setStoreGuid(itemBarCodeReqDTO.getStoreGuid());
        querySku.setDataList(Lists.newArrayList(itemBarCodeReqDTO.getGuid()));
        List<SkuInfoRespDTO> skuInfoList = itemClientService.listSkuInfoByRecipeMode(querySku);
        if (CollectionUtils.isEmpty(skuInfoList)) {
            log.info("查询商品失败，商品不存在,入参:{}", JacksonUtils.writeValueAsString(querySku));
            return;
        }
        SkuInfoRespDTO skuInfo = skuInfoList.get(0);
        log.info("查询商品结果:{}", JacksonUtils.writeValueAsString(skuInfo));
        // OrderDO
        OrderDO orderDO = new OrderDO();
        orderDO.setTradeMode(TradeModeEnum.FAST.getCode());
        orderDO.setOrderNo(skuInfo.getSkuGuid() + "01");

        // DineInItemDTO
        DineInItemDTO dineInItemDTO = new DineInItemDTO();
        BeanUtils.copyProperties(skuInfo, dineInItemDTO);
        dineInItemDTO.setItemName(itemBarCodeReqDTO.getItemName() +
                (StringUtils.isEmpty(itemBarCodeReqDTO.getSkuName()) ? "" : "(" + itemBarCodeReqDTO.getSkuName() + ")"));
        dineInItemDTO.setPriceChangeType(0);
        dineInItemDTO.setOriginalPrice(skuInfo.getSalePrice());
        dineInItemDTO.setCurrentCount(itemBarCodeReqDTO.getCurrentCount());
        dineInItemDTO.setFreeCount(BigDecimal.ZERO);
        dineInItemDTO.setPrice(skuInfo.getSalePrice());
        dineInItemDTO.setItemPrice(itemBarCodeReqDTO.getItemPrice());
        dineInItemDTO.setItemTypeGuid(skuInfo.getItemTypeGuid());
        dineInItemDTO.setCode(generateWeightCode(skuInfo, itemBarCodeReqDTO.getCurrentCount()));

        // 打印条码
        dineInPrintService.printLabelItemBarCode(itemBarCodeReqDTO, orderDO, dineInItemDTO);
    }

    /**
     * 创建条形码
     */
    private String generateWeightCode(SkuInfoRespDTO skuInfo, BigDecimal currentCount) {
        String suffix = new DecimalFormat("000.000").format(currentCount).replace(".", "");
        return "w" + skuInfo.getId() + suffix;
    }
}
