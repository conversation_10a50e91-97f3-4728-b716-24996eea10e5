package com.holderzone.saas.store.trade.aop;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.saas.store.trade.anno.FeignCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Aspect
@Component
@Slf4j
public class FeignCacheAspect {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Pointcut("@annotation(com.holderzone.saas.store.trade.anno.FeignCache)")
    public void feignCache() {
    }

    @Around("FeignCacheAspect.feignCache()&&@annotation(feignCache)")
    public Object feignCache(ProceedingJoinPoint point, FeignCache feignCache) throws Throwable {
        String redisKey = null;
        try {
            String parameter = objectMapper.writeValueAsString(point.getArgs()) ;
            String parameterMd5 = DigestUtils.md5Hex(parameter);
            redisKey = "feignCache-" + feignCache.value() + parameterMd5;
            log.info(redisKey);
            String cacheRet = stringRedisTemplate.opsForValue().get(redisKey);
            MethodSignature methodSignature = (MethodSignature)point.getSignature();
            Method method = methodSignature.getMethod();
             if (StringUtils.isNotBlank(cacheRet) && method.getReturnType() != Void.TYPE) {
                Object ret = objectMapper.readValue(cacheRet, method.getReturnType());
                if (ret != null) {
                    return ret;
                }
            }
        } catch (Exception ex) {
            log.error("获取 feignCache 失败", ex);
        }
        Object ret = point.proceed();
        if(Objects.isNull(ret)){
            return null;
        }
        try {
            if(StringUtils.isNotBlank(redisKey)){
                String cache = objectMapper.writeValueAsString(ret);
                stringRedisTemplate.opsForValue().set(redisKey, cache, 5, TimeUnit.SECONDS);
            }
        } catch (Exception ex) {
            log.error("feignCache 失败", ex);
        }
        return ret;
    }

}