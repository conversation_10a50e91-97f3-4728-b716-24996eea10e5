package com.holderzone.saas.store.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.trade.entity.domain.MultipleTransactionRecordDO;
import com.holderzone.saas.store.trade.entity.enums.TradeStateEnum;
import com.holderzone.saas.store.trade.mapper.MultipleTransactionRecordMapper;
import com.holderzone.saas.store.trade.service.IMultipleTransactionRecordService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 订单多次支付交易记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
public class MultipleTransactionRecordServiceImpl extends ServiceImpl<MultipleTransactionRecordMapper, MultipleTransactionRecordDO>
        implements IMultipleTransactionRecordService {

    @Override
    public List<MultipleTransactionRecordDO> listByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<MultipleTransactionRecordDO>()
                .eq(MultipleTransactionRecordDO::getOrderGuid, orderGuid)
                .eq(MultipleTransactionRecordDO::getState, TradeStateEnum.SUCCESS.getCode())
        );
    }

    @Override
    public List<MultipleTransactionRecordDO> listByOriginalMultipleTransactionRecordGuids(List<String> originalMultipleTransactionRecordGuids) {
        return list(new LambdaQueryWrapper<MultipleTransactionRecordDO>()
                .in(MultipleTransactionRecordDO::getOriginalMultipleTransactionRecordGuid, originalMultipleTransactionRecordGuids)
        );
    }

    @Override
    public List<MultipleTransactionRecordDO> listByOrderGuidList(List<Long> orderGuidList) {
        if (CollectionUtils.isEmpty(orderGuidList)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<MultipleTransactionRecordDO>()
                .in(MultipleTransactionRecordDO::getOrderGuid, orderGuidList)
                .eq(MultipleTransactionRecordDO::getState, TradeStateEnum.SUCCESS.getCode())
        );
    }

    @Override
    public void addBatchRefundAmount(List<MultipleTransactionRecordDO> recordDOList) {
        baseMapper.addBatchRefundAmount(recordDOList);
    }
}
