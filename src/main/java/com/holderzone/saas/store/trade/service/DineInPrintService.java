package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;

import java.util.List;

public interface DineInPrintService {

    boolean printItemDetail(BaseDTO baseDTO, OrderDO orderDO, OrderFeeDetailDTO orderFeeDetailDTO, List<DineInItemDTO> dineInItemDTOS);

    boolean printItemDetail(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

    /**
     * 打印换菜单
     */
    boolean printChangeItem(BaseDTO baseDTO, DineinOrderDetailRespDTO orderDetailRespDTO, Boolean cancelFlag,
                            List<SubDineInItemDTO> originalSubDineInItemList, List<SubDineInItemDTO> changeSubDineInItemList,
                            OrderItemDO pkgOrderItemDO);

    boolean printOrderItem(BaseDTO baseDTO, OrderDO orderDO, List<DineInItemDTO> dineInItemDTOS, Integer itemInvoiceType);

    boolean printLabelItem(BaseDTO baseDTO, OrderDO orderDO, List<DineInItemDTO> dineInItemDTOS);

    boolean printLabelItemBarCode(BaseDTO baseDTO, OrderDO orderDO, DineInItemDTO dineInItem);

    boolean printRefundItem(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

    boolean printPreCheck(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

    boolean printCheckout(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

    /**
     * 打印菜品复单
     */
    boolean printItemRepeatOrder(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

    void printTransferItem(BaseDTO baseDTO,
                           DineinOrderDetailRespDTO newOrderDetailRespDTO,
                           DineinOrderDetailRespDTO oldOrderDetailRespDTO);

    /**
     * 退款单打印
     *
     * @param baseDTO                  基础对象
     * @param dineinOrderDetailRespDTO 传输对象
     * @return 打印结果
     */
    boolean printRefundOrder(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO);
}
