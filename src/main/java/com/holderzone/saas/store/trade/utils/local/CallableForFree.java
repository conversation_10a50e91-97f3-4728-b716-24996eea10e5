package com.holderzone.saas.store.trade.utils.local;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.saas.store.dto.order.local.FreeReturnItemDTO;
import com.holderzone.saas.store.trade.entity.domain.FreeReturnItemDO;
import com.holderzone.saas.store.trade.repository.interfaces.FreeReturnItemService;
import com.holderzone.saas.store.trade.transform.LocalTransform;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class CallableForFree implements Callable<List<FreeReturnItemDTO>> {

    //入参
    private List<String> orderGuidLists;

    private FreeReturnItemService freeReturnItemService;

    private LocalTransform localTransform;

    List<FreeReturnItemDTO> data = new ArrayList<>();

    private final CountDownLatch latch;

    private UserContext userContext;


    public CallableForFree(List<String> orderGuidLists, FreeReturnItemService freeReturnItemService,
                           LocalTransform localTransform, CountDownLatch latch, UserContext userContext) {
        this.orderGuidLists = orderGuidLists;
        this.freeReturnItemService = freeReturnItemService;
        this.localTransform = localTransform;
        this.latch = latch;
        this.userContext = userContext;
    }

    @Override
    public List<FreeReturnItemDTO> call() throws Exception {
        try {
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            log.info("查询赠送入参：{}", orderGuidLists);
            List<FreeReturnItemDO> freeLists = freeReturnItemService.listByOrderGuids(orderGuidLists);
            data = localTransform.freeDOList2DTOList(freeLists);
            log.info("查询赠送结束");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            latch.countDown();
        }
        return data;
    }
}
