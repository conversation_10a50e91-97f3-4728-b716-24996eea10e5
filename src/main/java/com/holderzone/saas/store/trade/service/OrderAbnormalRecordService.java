package com.holderzone.saas.store.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayReserveResultDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalListReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordRespDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderAbnormalRecordDO;

public interface OrderAbnormalRecordService extends IService<OrderAbnormalRecordDO> {

    /**
     * 新增异常订单记录
     *
     * @param orderAbnormalRecordReqDTO
     * @return
     */
    Integer saveAbnormalRecordOrder(OrderAbnormalRecordReqDTO orderAbnormalRecordReqDTO);

    /**
     * 查询异常订单列表
     *
     * @param orderAbnormalListReqDTO
     * @return
     */
    Page<OrderAbnormalRecordRespDTO> listAbnormalOrders(OrderAbnormalListReqDTO orderAbnormalListReqDTO);

    /**
     * 查询支付结果
     *
     * @param saasPollingDTO
     * @return
     */
    AggPayPollingRespDTO getPaymentResult(SaasPollingDTO saasPollingDTO);

    /**
     * 取消支付
     *
     * @param saasPollingDTO
     */
    AggPayReserveResultDTO cancelPayment(SaasPollingDTO saasPollingDTO);
}
