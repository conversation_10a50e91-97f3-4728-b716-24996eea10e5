package com.holderzone.saas.store.trade.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 封装可退款项（商品或附加费）及其相关信息
 *
 * <AUTHOR>
 * @date 2025/7/4
 * @since 1.8
 */
@Data
public class RefundableItemBO {

    // 可以是 OrderItemExtendsDO 或 AppendFeeDO
    private Object item;

    // 实付金额
    private BigDecimal paidAmount;

    // 可退金额
    private BigDecimal refundAmount = BigDecimal.ZERO;

    public RefundableItemBO() {
    }

    public RefundableItemBO(Object item, BigDecimal paidAmount) {
        this.item = item;
        this.paidAmount = paidAmount;
    }
}
