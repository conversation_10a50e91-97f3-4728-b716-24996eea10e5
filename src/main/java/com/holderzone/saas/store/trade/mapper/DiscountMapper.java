package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单优惠记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface DiscountMapper extends BaseMapper<DiscountDO> {

    int batchUpdate(List<DiscountDO> record);

    List<DiscountDO> listHasGrouponCompleteOrder(LocalDateTime startTime, String orderGuid);

    /**
     * 优惠方式分组统计
     *
     * @param request
     * @return
     */
    List<AmountItemDTO> handoverDiscountType(@Param("dto") HandoverPayQueryDTO request);
}
