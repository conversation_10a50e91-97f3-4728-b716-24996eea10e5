package com.holderzone.saas.store.trade.beans;

import com.beust.jcommander.internal.Lists;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityStatisticsRespDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public class ThirdActivityBeanUtils {

    public static List<AmountItemDTO.InnerDetails> copyProperties(List<ThirdActivityStatisticsRespDTO> statisticsRespDTOList) {
        List<AmountItemDTO.InnerDetails> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(statisticsRespDTOList)) {
            return result;
        }
        statisticsRespDTOList.forEach(statisticsRespDTO -> {
            AmountItemDTO.InnerDetails inner = new AmountItemDTO.InnerDetails();
            inner.setName(statisticsRespDTO.getThirdName());
            inner.setAmount(statisticsRespDTO.getThirdFee());
            // inner.setCount(statisticsRespDTO.getCount());
            inner.setInnerDetails(Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(statisticsRespDTO.getDetails())) {
                statisticsRespDTO.getDetails().forEach(record -> {
                    AmountItemDTO.InnerDetails innerDetails = new AmountItemDTO.InnerDetails();
                    innerDetails.setName(record.getThirdName());
                    innerDetails.setAmount(record.getThirdFee());
                    innerDetails.setCount(record.getCount());
                    inner.getInnerDetails().add(innerDetails);
                });
            }
            result.add(inner);
        });
        return result;
    }

}
