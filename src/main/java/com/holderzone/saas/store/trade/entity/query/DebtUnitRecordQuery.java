package com.holderzone.saas.store.trade.entity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> R
 * @date 2020/12/15 18:24
 * @description
 */
@NoArgsConstructor
@Data
public class DebtUnitRecordQuery {
    @ApiModelProperty(value = "单位guid")
    private String unitGuid;

    @ApiModelProperty(value = "是否还款（默认0，未还款，1已还款）")
    private Integer repaymentStatus;
}
