package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.response.dinein.RefundOrderRecordDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderRefundRecordDO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 订单退款记录 服务类
 * </p>
 */
public interface OrderRefundRecordService extends IService<OrderRefundRecordDO> {

    List<RefundOrderRecordDTO> listByRefundOrderGuid(Long refundOrderGuid);

    Long getMaxRecordNoByRefundOrderGuid(Long refundOrderGuid);

    BigDecimal handoverRefund(HandoverPayQueryDTO request);
}
