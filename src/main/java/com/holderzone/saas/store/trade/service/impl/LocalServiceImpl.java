package com.holderzone.saas.store.trade.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import com.holderzone.saas.store.dto.erp.SkuInfo;
import com.holderzone.saas.store.dto.order.local.FreeReturnItemDTO;
import com.holderzone.saas.store.dto.table.LocalOrderSaveMQDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.trade.config.RocketMqConfig;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.repository.feign.ErpClientService;
import com.holderzone.saas.store.trade.repository.feign.StoreClientService;
import com.holderzone.saas.store.trade.repository.impls.ItemAttrServiceImpl;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.LocalService;
import com.holderzone.saas.store.trade.transform.LocalTransform;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.CommonUtil;
import com.holderzone.saas.store.trade.utils.ItemUtil;
import com.holderzone.saas.store.trade.utils.RedisKeyUtil;
import com.holderzone.saas.store.trade.utils.local.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant.HST_ORDER_ITEM;

/**
 * <AUTHOR>
 * @version 1.0
 * @className
 * @date 2018/09/04 16:10
 * @description
 * @program holder-saas-store-trade
 */
@Service
@Slf4j
public class LocalServiceImpl implements LocalService {

    private final OrderService orderService;

    private final OrderSubsidiaryService orderSubsidiaryService;

    private final OrderItemService orderItemService;

    private final ItemAttrServiceImpl itemAttrService;

    private final FreeReturnItemService freeReturnItemService;

    private final TransactionRecordService transactionRecordService;

    private final DiscountService discountService;

    private final AppendFeeMpService appendFeeMpService;

    private final ErpClientService erpClientService;

    private final DynamicHelper dynamicHelper;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final StoreClientService storeClientService;

    private final RedisHelper redisHelper;

    @Resource(name = "checkOutThreadPool")
    private ExecutorService pool;

    private LocalTransform localTransform = LocalTransform.INSTANCE;

    @Autowired
    public LocalServiceImpl(OrderService orderService, OrderItemService orderItemService,
                            ItemAttrServiceImpl itemAttrService, FreeReturnItemService freeReturnItemService,
                            AppendFeeMpService appendFeeMpService, TransactionRecordService transactionRecordService,
                            DiscountService discountService, DynamicHelper dynamicHelper,
                            ErpClientService erpClientService, DefaultRocketMqProducer defaultRocketMqProducer,
                            StoreClientService storeClientService, OrderSubsidiaryService orderSubsidiaryService,
                            RedisHelper redisHelper) {
        this.orderService = orderService;
        this.orderItemService = orderItemService;
        this.itemAttrService = itemAttrService;
        this.freeReturnItemService = freeReturnItemService;
        this.transactionRecordService = transactionRecordService;
        this.discountService = discountService;
        this.appendFeeMpService = appendFeeMpService;
        this.dynamicHelper = dynamicHelper;
        this.erpClientService = erpClientService;
        this.defaultRocketMqProducer = defaultRocketMqProducer;
        this.storeClientService = storeClientService;
        this.orderSubsidiaryService = orderSubsidiaryService;
        this.redisHelper = redisHelper;
    }

    @Override
    public LocalDTO getSingle(SingleQuery singleQuery) {
        log.info("本地化拉取单条数据开始");
        LocalDTO localDTO = new LocalDTO();
        List<OrderDO> orderList = orderService.getListByIds(singleQuery.getOrderGuid());
        if (ObjectUtils.isEmpty(orderList)) {
            return localDTO;
        }

        List<String> orderGuidLists = orderList.stream().map(OrderDO::getGuid).map(String::valueOf).collect
                (Collectors.toList());
        List<Long> orderGuidList = orderGuidLists.stream().map(Long::parseLong).collect(Collectors.toList());
        List<OrderDTO> orders = localTransform.orderDOList2DTOList(orderList);
        //2.查询菜品
        List<OrderItemDO> orderItemLists = orderItemService.listByOrderLists(orderGuidLists);
        List<ItemDTO> dineInItemDTOList = localTransform.orderItemDO2ItemDTO(orderItemLists);
        List<Long> itemGuids = orderItemLists.stream().map(OrderItemDO::getGuid).collect(Collectors.toList());

        final CountDownLatch latch = new CountDownLatch(5);
        List future = new ArrayList(5);
        try {
            UserContext userContext = UserContextUtils.get();
            //1.1.查询属性
            Callable<List<ItemAttrDTO>> attr = new CallableForAttr(itemGuids, itemAttrService, localTransform, latch,
                    userContext);
            Future<List<ItemAttrDTO>> attrFuture = pool.submit(attr);
            future.add(attrFuture);

            //1.2.查询赠送
            Callable<List<FreeReturnItemDTO>> free = new CallableForFree(orderGuidLists, freeReturnItemService,
                    localTransform, latch, userContext);
            Future<List<FreeReturnItemDTO>> freeFuture = pool.submit(free);
            future.add(freeFuture);

            //1.3.查询交易记录
            Callable<List<TransactionRecordDTO>> transaction = new CallableForTransaction(orderGuidList,
                    transactionRecordService, localTransform, latch, userContext);
            Future<List<TransactionRecordDTO>> transactionFuture = pool.submit(transaction);
            future.add(transactionFuture);

            //1.4.查询附加费
            Callable<List<AppendFeeDTO>> append = new CallableForAppend(orderGuidLists, appendFeeMpService,
                    localTransform, latch, userContext);
            Future<List<AppendFeeDTO>> appendFuture = pool.submit(append);
            future.add(appendFuture);

            //1.5.查询优惠
            Callable<List<DiscountDTO>> discount = new CallableForDiscount(orderGuidLists, discountService,
                    localTransform, latch, userContext);
            Future<List<DiscountDTO>> discountFuture = pool.submit(discount);
            future.add(discountFuture);

            latch.await(30, TimeUnit.SECONDS);

            Future<List<ItemAttrDTO>> futureAttr = (Future<List<ItemAttrDTO>>) future.get(0);
            Future<List<FreeReturnItemDTO>> futureFree = (Future<List<FreeReturnItemDTO>>) future.get(1);
            Future<List<TransactionRecordDTO>> futureTransaction = (Future<List<TransactionRecordDTO>>) future.get(2);
            Future<List<AppendFeeDTO>> futureAppend = (Future<List<AppendFeeDTO>>) future.get(3);
            Future<List<DiscountDTO>> futureDiscount = (Future<List<DiscountDTO>>) future.get(4);

            List<ItemAttrDTO> attrList = futureAttr.get();
            List<FreeReturnItemDTO> freeList = futureFree.get();
            List<TransactionRecordDTO> transactionList = futureTransaction.get();
            List<AppendFeeDTO> appendList = futureAppend.get();
            List<DiscountDTO> discountList = futureDiscount.get();

            localDTO.setAttrList(attrList);
            localDTO.setFreeLists(freeList);
            localDTO.setTranList(transactionList);
            localDTO.setAppendFeeList(appendList);
            localDTO.setDiscountList(discountList);

        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }

        localDTO.setOrderItemList(dineInItemDTOList);
        localDTO.setOrderList(orders);
        log.info("本地化拉取单条数据执行结束");
        return localDTO;
    }

    @Override
    public LocalDTO getLocal(LocalQuery localQuery) {
        log.info("本地化数据提供开始");
        LocalDTO localDTO = new LocalDTO();
        //1.查询所有未结账单
        List<OrderDO> orderList = new ArrayList<>();
        orderList.addAll(orderService.getLocalUnpay(localQuery));
        if (ObjectUtils.isEmpty(orderList)) {
            return localDTO;
        }
        List<String> orderGuidLists = orderList.stream().map(OrderDO::getGuid).map(String::valueOf).collect
                (Collectors.toList());
        List<Long> orderGuidList = orderGuidLists.stream().map(Long::parseLong).collect(Collectors.toList());
        List<OrderDTO> orders = localTransform.orderDOList2DTOList(orderList);
        //2.查询菜品
        List<OrderItemDO> orderItemLists = orderItemService.listByOrderLists(orderGuidLists);
        List<ItemDTO> dineInItemDTOList = localTransform.orderItemDO2ItemDTO(orderItemLists);
        List<Long> itemGuids = orderItemLists.stream().map(OrderItemDO::getGuid).collect(Collectors.toList());

        final CountDownLatch latch = new CountDownLatch(5);
        List future = new ArrayList(5);
        try {
            UserContext userContext = UserContextUtils.get();
            //1.1.查询属性
            Callable<List<ItemAttrDTO>> attr = new CallableForAttr(itemGuids, itemAttrService, localTransform, latch,
                    userContext);
            Future<List<ItemAttrDTO>> attrFuture = pool.submit(attr);
            future.add(attrFuture);

            //1.2.查询赠送
            Callable<List<FreeReturnItemDTO>> free = new CallableForFree(orderGuidLists, freeReturnItemService,
                    localTransform, latch, userContext);
            Future<List<FreeReturnItemDTO>> freeFuture = pool.submit(free);
            future.add(freeFuture);

            //1.3.查询交易记录
            Callable<List<TransactionRecordDTO>> transaction = new CallableForTransaction(orderGuidList,
                    transactionRecordService, localTransform, latch, userContext);
            Future<List<TransactionRecordDTO>> transactionFuture = pool.submit(transaction);
            future.add(transactionFuture);

            //1.4.查询附加费
            Callable<List<AppendFeeDTO>> append = new CallableForAppend(orderGuidLists, appendFeeMpService,
                    localTransform, latch, userContext);
            Future<List<AppendFeeDTO>> appendFuture = pool.submit(append);
            future.add(appendFuture);

            //1.5.查询优惠
            Callable<List<DiscountDTO>> discount = new CallableForDiscount(orderGuidLists, discountService,
                    localTransform, latch, userContext);
            Future<List<DiscountDTO>> discountFuture = pool.submit(discount);
            future.add(discountFuture);

            latch.await(30, TimeUnit.SECONDS);

            Future<List<ItemAttrDTO>> futureAttr = (Future<List<ItemAttrDTO>>) future.get(0);
            Future<List<FreeReturnItemDTO>> futureFree = (Future<List<FreeReturnItemDTO>>) future.get(1);
            Future<List<TransactionRecordDTO>> futureTransaction = (Future<List<TransactionRecordDTO>>) future.get(2);
            Future<List<AppendFeeDTO>> futureAppend = (Future<List<AppendFeeDTO>>) future.get(3);
            Future<List<DiscountDTO>> futureDiscount = (Future<List<DiscountDTO>>) future.get(4);

            List<ItemAttrDTO> attrList = futureAttr.get();
            List<FreeReturnItemDTO> freeList = futureFree.get();
            List<TransactionRecordDTO> transactionList = futureTransaction.get();
            List<AppendFeeDTO> appendList = futureAppend.get();
            List<DiscountDTO> discountList = futureDiscount.get();

            localDTO.setAttrList(attrList);
            localDTO.setFreeLists(freeList);
            localDTO.setTranList(transactionList);
            localDTO.setAppendFeeList(appendList);
            localDTO.setDiscountList(discountList);

        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }

        localDTO.setOrderItemList(dineInItemDTOList);
        localDTO.setOrderList(orders);
        log.info("本地化数据提供执行结束");
        return localDTO;
    }

    /**
     * 校验菜品、订单数据完整性
     *
     * @param itemLists
     * @param appendLists
     * @param attrLists
     * @param discountLists
     * @param transLists
     * @param freeLists
     * @return
     */
    public void checkData(List<OrderDTO> orderFromAndroid, List<ItemDTO> itemLists,
                          List<AppendFeeDTO> appendLists, List<ItemAttrDTO> attrLists,
                          List<DiscountDTO> discountLists, List<TransactionRecordDTO> transLists,
                          List<FreeReturnItemDTO> freeLists
    ) throws BusinessException {

        List<String> orderS = new ArrayList<>();
        List<String> itemOrder = new ArrayList<>();
        List<String> appendOrder = new ArrayList<>();
        List<String> discountOrder = new ArrayList<>();
        List<String> transOrder = new ArrayList<>();
        List<String> attrItem = new ArrayList<>();
        List<String> freeItem = new ArrayList<>();

        if (!ObjectUtils.isEmpty(orderFromAndroid)) {
            orderS = orderFromAndroid.stream().map(OrderDTO::getGuid).map(String::valueOf).collect(Collectors.toList());
        }
        if (!ObjectUtils.isEmpty(itemLists)) {
            itemOrder = itemLists.stream().map(ItemDTO::getOrderGuid).collect(Collectors.toList());
        }
        if (!ObjectUtils.isEmpty(appendLists)) {
            appendOrder = appendLists.stream().map(AppendFeeDTO::getOrderGuid).map(String::valueOf).collect
                    (Collectors.toList());
        }
        if (!ObjectUtils.isEmpty(discountLists)) {
            discountOrder = discountLists.stream().map(DiscountDTO::getOrderGuid).map(String::valueOf).collect
                    (Collectors.toList());
        }
        if (!ObjectUtils.isEmpty(transLists)) {
            transOrder = transLists.stream().map(TransactionRecordDTO::getOrderGuid).map(String::valueOf).collect
                    (Collectors.toList());
        }
        if (!ObjectUtils.isEmpty(attrLists)) {
            attrItem = attrLists.stream().map(ItemAttrDTO::getOrderGuid).map(String::valueOf).collect(Collectors
                    .toList());
        }
        if (!ObjectUtils.isEmpty(freeLists)) {
            freeItem = freeLists.stream().map(FreeReturnItemDTO::getOrderGuid).map(String::valueOf).collect
                    (Collectors.toList());
        }

        //1.校验附加费
        if (!orderS.containsAll(appendOrder)) {
            appendOrder.removeAll(orderS);
            log.info("append中存在错误的orderGuid:{}", JacksonUtils.writeValueAsString(appendOrder));
            throw new BusinessException("append中存在错误的orderGuid" + appendOrder.toString());
        }
        //2.校验优惠
        if (!orderS.containsAll(discountOrder)) {
            discountOrder.removeAll(orderS);
            log.info("discount中存在错误的orderGuid:{}", JacksonUtils.writeValueAsString(discountOrder));
            throw new BusinessException("discount中存在错误的orderGuid" + discountOrder.toString());
        }

        //3.校验交易记录
        if (!orderS.containsAll(transOrder)) {
            transOrder.removeAll(orderS);
            log.info("trans中存在错误的orderGuid:{}", JacksonUtils.writeValueAsString(transOrder));
            throw new BusinessException("trans中存在错误的orderGuid" + transOrder.toString());
        }

        //4.校验属性
        if (!itemOrder.containsAll(attrItem)) {
            attrItem.removeAll(itemOrder);
            log.info("attr中存在错误的itemGuid:{}", JacksonUtils.writeValueAsString(attrItem));
            throw new BusinessException("attr中存在错误的itemGuid" + attrItem.toString());
        }

        //5.校验赠送
        if (!itemOrder.containsAll(freeItem)) {
            freeItem.removeAll(itemOrder);
            log.info("free中存在错误的itemGuid:{}", JacksonUtils.writeValueAsString(freeItem));
            throw new BusinessException("free中存在错误的itemGuid" + freeItem.toString());
        }

        //6.校验菜品
        if (!orderS.containsAll(itemOrder)) {
            itemOrder.removeAll(orderS);
            log.info("item中存在错误的orderGuid:{}", JacksonUtils.writeValueAsString(itemOrder));
            throw new BusinessException("item中存在错误的orderGuid" + itemOrder.toString());
        }

        //7.校验实付金额正确性
        Map<String, List<DiscountDTO>> mapDiscount = CollectionUtil.toListMap(discountLists, "orderGuid");
        List<OrderDTO> errorFee = orderFromAndroid.stream().filter(orderDTO -> {
            if (orderDTO.getState().equals(StateEnum.SUCCESS.getCode())
                    && !orderDTO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
                BigDecimal discountTotal = BigDecimal.ZERO;
                List<DiscountDTO> discountDTOS;
                if (!ObjectUtils.isEmpty(mapDiscount)) {
                    discountDTOS = mapDiscount.get(orderDTO.getGuid());
                    if (!ObjectUtils.isEmpty(discountDTOS)) {
                        for (DiscountDTO discountDTO : discountDTOS) {
                            discountTotal = discountTotal.add(discountDTO.getDiscountFee());
                        }
                    }
                }
                log.info("orderFee={},actuallyFee={},discountFee={}", orderDTO.getOrderFee(), orderDTO
                        .getActuallyPayFee(), discountTotal);
                BigDecimal orderFee = orderDTO.getActuallyPayFee().add(discountTotal);
                if (!(orderDTO.getOrderFee().compareTo(orderFee) == 0)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }).collect(Collectors.toList());

        if (!ObjectUtils.isEmpty(errorFee)) {
            List<String> error = errorFee.stream().map(OrderDTO::getGuid).map(String::valueOf).collect(Collectors
                    .toList());
            throw new BusinessException("已完成订单实付金额数据错误" + error.toString());
        }

    }


    /**
     * 保存本地化数据
     * 只有在本地结账的情况下，忽略安卓端传来的数据
     * 其他情况下，以安卓端传递的数据为准。只做基本的校验和保存。
     * 安卓传递所有未结账的数据过来
     *
     * @param localDTO
     * @return
     */
    @Override
    @Transactional
    public Map<String, String> saveLocal(LocalDTO localDTO) {
        List<OrderSkuDTO> erpList = new ArrayList<>();
        //旧的orderGuid为key，新的orderGuid为value
        Map<String, String> oldNewOrderGuidMap = new HashMap<>();
        //新的orderGuid为key，OrderDTO为value
        Map<String, OrderDTO> orderDTOValue = new HashMap<>();
        //旧的itemGuid为key，新的itemGuid为value
        Map<String, String> itemNewValue = new HashMap<>();
        //新的itemGuid为key，ItemDTO为value
        Map<String, ItemDTO> itemDTOValue = new HashMap<>();
        try {
            List<OrderDTO> orderListFromAndroid = localDTO.getOrderList();
            if (CollectionUtil.isEmpty(orderListFromAndroid)) {
                return oldNewOrderGuidMap;
            }
            List<ItemDTO> orderItemListFromAndroid = localDTO.getOrderItemList();
            List<AppendFeeDTO> appendFeeListFromAndroid = localDTO.getAppendFeeList();
            List<ItemAttrDTO> attrListFromAndroid = localDTO.getAttrList();
            List<DiscountDTO> discountListFromAndroid = localDTO.getDiscountList();
            List<TransactionRecordDTO> transListFromAndroid = localDTO.getTranList();
            List<FreeReturnItemDTO> freeRetuenListFromAndroid = localDTO.getFreeLists();

            //0.排除掉服务器端已结账的数据
            List<String> androidOrderGuids = orderListFromAndroid.stream().map(OrderDTO::getGuid).map
                    (String::valueOf).collect(Collectors.toList());
            List<Long> isPayOrderDOS = orderService.queryIsPayOrderList(androidOrderGuids).stream().map
                    (OrderDO::getGuid).collect(Collectors.toList());
            //安卓端传递过来的数据，去掉在服务器端已结账的订单
            List<OrderDTO> withOutIsPayOrderList;
            if (CollectionUtil.isEmpty(isPayOrderDOS)) {
                withOutIsPayOrderList = orderListFromAndroid;
            } else {
                withOutIsPayOrderList = orderListFromAndroid.stream()
                        .filter(orderDTO -> !isPayOrderDOS.contains(orderDTO.getGuid()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtil.isEmpty(withOutIsPayOrderList)) {
                return oldNewOrderGuidMap;
            }

            //1.数据完整性校验+已结账单金额校验
            checkData(withOutIsPayOrderList, orderItemListFromAndroid, appendFeeListFromAndroid, attrListFromAndroid,
                    discountListFromAndroid, transListFromAndroid, freeRetuenListFromAndroid);

            //2.并单填充子单数据
            List<OrderDO> orderDOS = dealSubOrder(withOutIsPayOrderList, oldNewOrderGuidMap, orderDTOValue);
            //3.删除数据
            List<String> deleteOrderGuids = new ArrayList<>();

            List<OrderDO> saveOrder = new ArrayList<>();
            List<OrderDO> updateOrder = new ArrayList<>();
            List<OrderItemDO> saveItem = new ArrayList<>();
            List<DiscountDO> saveDiscount = new ArrayList<>();
            List<AppendFeeDO> saveAppend = new ArrayList<>();
            List<ItemAttrDO> saveAttr = new ArrayList<>();
            List<TransactionRecordDO> saveTrans = new ArrayList<>();
            List<FreeReturnItemDO> saveFree = new ArrayList<>();
            List<OrderSubsidiaryDO> saveSubsidiary = new ArrayList<>();

            for (OrderDO orderDO : orderDOS) {

                LocalDate businessDay;
                if (orderDO.getState().equals(StateEnum.SUCCESS.getCode()) && !ObjectUtils.isEmpty(orderDO.getCheckoutTime())) {
                    businessDay = CommonUtil.getBusinessDayLocal(
                            storeClientService.queryStoreBizByGuid(UserContextUtils.getStoreGuid()),
                            orderDO.getCheckoutTime()
                    );
                    orderDO.setBusinessDay(businessDay);
                }

                String orderGuid = String.valueOf(orderDO.getGuid());
                OrderDTO orderDTO = orderDTOValue.get(orderGuid);
                String oldOrderGuid = orderDTO.getGuid();
                if (orderDTO.getOrderSource() != null && orderDTO.getOrderSource() == 1) {
                    saveOrder.add(orderDO);

                    OrderSubsidiaryDO orderSubsidiaryDO = new OrderSubsidiaryDO();
                    orderSubsidiaryDO.setGuid(Long.valueOf(orderGuid));
                    orderSubsidiaryDO.setLocalUploadId(orderDTO.getGuid());
                    saveSubsidiary.add(orderSubsidiaryDO);
                } else {
                    updateOrder.add(orderDO);
                    deleteOrderGuids.add(oldOrderGuid);
                }

                if (!ObjectUtils.isEmpty(orderItemListFromAndroid)) {

                    List<OrderItemDO> item = localTransform.itemDTO2OrderItemDO(
                            orderItemListFromAndroid.stream().filter(itemDTO -> itemDTO.getOrderGuid().equals
                                    (oldOrderGuid))
                                    .peek(itemDTO -> {
                                        long guid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
                                        itemNewValue.put(itemDTO.getGuid(), String.valueOf(guid));
                                        itemDTO.setGuid(String.valueOf(guid));
                                        if (orderDTO.getOrderSource() != null && orderDTO.getOrderSource() == 1) {
                                            itemDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
                                        }
                                        itemDTOValue.put(String.valueOf(guid), itemDTO);
                                    })
                                    .collect(Collectors.toList())
                    );

                    saveItem.addAll(item.stream().peek(itemDO -> {
                        if (ItemUtil.isOutOfGroup(itemDO.getItemType())) {
                            ItemDTO itemDTO = itemDTOValue.get(String.valueOf(itemDO.getGuid()));
                            if (!StringUtils.isEmpty(itemDTO.getParentItemGuid()) && !"0".equals(itemDTO
                                    .getParentItemGuid())) {
                                itemDO.setParentItemGuid(Long.valueOf(itemNewValue.get(String.valueOf(itemDTO
                                        .getParentItemGuid()))));
                            }
                        }
                    }).collect(Collectors.toList()));

                    //已结账订单处理库存
                    if (orderDO.getState().equals(StateEnum.SUCCESS.getCode())) {
                        OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
                        orderSkuDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
                        orderSkuDTO.setOperatorGuid(UserContextUtils.getUserGuid());
                        orderSkuDTO.setOperatorName(UserContextUtils.getUserName());
                        orderSkuDTO.setStoreGuid(UserContextUtils.getStoreGuid());
                        List<SkuInfo> skuList = new ArrayList<>();
                        for (int i = 0; i < saveItem.size(); i++) {
                            SkuInfo sku = new SkuInfo();
                            sku.setSkuGuid(saveItem.get(i).getSkuGuid());
                            sku.setCount(saveItem.get(i).getCurrentCount().subtract(saveItem.get(i).getReturnCount()));
                            skuList.add(sku);
                        }
                        orderSkuDTO.setSkuList(skuList);
                        erpList.add(orderSkuDTO);
                    }
                }

                if (!ObjectUtils.isEmpty(discountListFromAndroid)) {
                    saveDiscount.addAll(localTransform.discountList2DiscountList(
                            discountListFromAndroid.stream().filter(discountDTO -> discountDTO.getOrderGuid().equals
                                    (oldOrderGuid))
                                    .peek(discountDTO -> {
                                        discountDTO.setGuid(String.valueOf(dynamicHelper.generateGuid(GuidKeyConstant
                                                .HST_DISCOUNT)));
                                        if (orderDTO.getOrderSource() != null && orderDTO.getOrderSource() == 1) {
                                            discountDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
                                        }
                                    })
                                    .collect(Collectors.toList())
                    ));
                }


                if (!ObjectUtils.isEmpty(transListFromAndroid)) {
                    saveTrans.addAll(localTransform.tranDTOListS2TranDOList(
                            transListFromAndroid.stream().filter(transactionRecordDTO -> transactionRecordDTO
                                    .getOrderGuid().equals(oldOrderGuid))
                                    .peek(transactionRecordDTO -> {
                                        transactionRecordDTO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant
                                                .HST_TRANSACTION_RECORD));
                                        if (orderDTO.getOrderSource() != null && orderDTO.getOrderSource() == 1) {
                                            transactionRecordDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
                                        }
                                       /* if (orderDO.getState().equals(StateEnum.SUCCESS.getCode())) {
                                            LocalDate business = CommonUtil.getBusinessDayLocal(
                                                    storeClientService.queryStoreBizByGuid(UserContextUtils.getStoreGuid()),
                                                    orderDO.getCheckoutTime().toLocalTime()
                                            );

                                            transactionRecordDTO.setBusinessDay(business);
                                        }*/

                                    })
                                    .collect(Collectors.toList())
                    ));
                }


                if (!ObjectUtils.isEmpty(attrListFromAndroid)) {
                    saveAttr.addAll(localTransform.itemAttrDTOList2ItemAttrDOList(
                            attrListFromAndroid.stream().filter(itemAttrDTO -> itemAttrDTO.getOrderGuid().equals
                                    (oldOrderGuid))
                                    .peek(itemAttrDTO -> {
                                        itemAttrDTO.setGuid(String.valueOf(dynamicHelper.generateGuid(GuidKeyConstant
                                                .ITEM_ATTR_GUID)));
                                        if (orderDTO.getOrderSource() != null && orderDTO.getOrderSource() == 1) {
                                            itemAttrDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
                                        }
                                        itemAttrDTO.setOrderItemGuid(itemNewValue.get(String.valueOf(itemAttrDTO
                                                .getOrderItemGuid())));
                                    })
                                    .collect(Collectors.toList())
                    ));
                }

                if (!ObjectUtils.isEmpty(appendFeeListFromAndroid)) {
                    saveAppend.addAll(localTransform.appendFeeDTOList2AppendFeeDOList(
                            appendFeeListFromAndroid.stream().filter(appendFeeDTO -> appendFeeDTO.getOrderGuid()
                                    .equals(oldOrderGuid))
                                    .peek(appendFeeDTO -> {
                                        appendFeeDTO.setGuid(String.valueOf(dynamicHelper.generateGuid
                                                (GuidKeyConstant.HST_APPEND_FEE)));
                                        if (orderDTO.getOrderSource() != null && orderDTO.getOrderSource() == 1) {
                                            appendFeeDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
                                        }
                                    })
                                    .collect(Collectors.toList())
                    ));
                }

                if (!ObjectUtils.isEmpty(freeRetuenListFromAndroid)) {
                    saveFree.addAll(localTransform.freeReturnItemDToList2FreeReturnItemDOList(
                            freeRetuenListFromAndroid.stream().filter(freeReturnItemDTo -> freeReturnItemDTo
                                    .getOrderGuid().equals(oldOrderGuid))
                                    .peek(freeReturnItemDTo -> {
                                        freeReturnItemDTo.setGuid(String.valueOf(dynamicHelper.generateGuid
                                                (GuidKeyConstant.FREE_RETURN_ITEM)));
                                        if (orderDTO.getOrderSource() != null && orderDTO.getOrderSource() == 1) {
                                            freeReturnItemDTo.setOrderGuid(String.valueOf(orderDO.getGuid()));
                                        }
                                        freeReturnItemDTo.setOrderItemGuid(itemNewValue.get(freeReturnItemDTo
                                                .getOrderItemGuid()));
                                    })
                                    .collect(Collectors.toList())
                    ));
                }
            }
            //3.1删除订单相关数据
            if (!ObjectUtils.isEmpty(deleteOrderGuids)) {
                //orderService.removeByIds(deleteOrderGuids);
                orderSubsidiaryService.removeByIds(deleteOrderGuids);
                orderItemService.deleteByOrderGuids(deleteOrderGuids);
                discountService.deleteByOrderGuids(deleteOrderGuids);
                transactionRecordService.deleteByOrderGuids(deleteOrderGuids);
                appendFeeMpService.deleteByOrderGuids(deleteOrderGuids);
                freeReturnItemService.deleteByOrderGuids(deleteOrderGuids);
                itemAttrService.deleteByOrderGuids(deleteOrderGuids);
            }

            //4.保存相关数据
            if (!ObjectUtils.isEmpty(saveOrder)) {
                orderService.saveBatch(saveOrder);
            }

            log.info("updateOrder={}", JacksonUtils.writeValueAsString(updateOrder));
            if (!ObjectUtils.isEmpty(updateOrder)) {
                boolean flag = orderService.updateBatchById(updateOrder, updateOrder.size());
                log.info("flag={}", flag);
            }

            if (!ObjectUtils.isEmpty(saveSubsidiary)) {
                orderSubsidiaryService.saveBatch(saveSubsidiary);
            }

            if (!ObjectUtils.isEmpty(saveItem)) {
                orderItemService.saveBatch(saveItem);
            }

            if (!ObjectUtils.isEmpty(saveDiscount)) {
                discountService.saveBatch(saveDiscount);
            }

            if (!ObjectUtils.isEmpty(saveTrans)) {
                transactionRecordService.saveBatch(saveTrans);
            }

            if (!ObjectUtils.isEmpty(saveAttr)) {
                itemAttrService.saveBatch(saveAttr);
            }

            if (!ObjectUtils.isEmpty(saveAppend)) {
                appendFeeMpService.saveBatch(saveAppend);
            }

            if (!ObjectUtils.isEmpty(saveFree)) {
                freeReturnItemService.saveBatch(saveFree);
            }

            //保存成功调erp减库存
            log.info("保存成功调erp减库存,erpList={}", JacksonUtils.writeValueAsString(erpList));
            if (CollectionUtil.isNotEmpty(erpList)) {
                erpClientService.reduceStockForOrderList(erpList);
            }

            //清除缓存
            for (OrderDO orderDO : saveOrder) {
                String redisKey = RedisKeyUtil.getHstOrderItemKey(String.valueOf(orderDO.getGuid()));
                redisHelper.delete(redisKey);
            }

            defaultRocketMqProducer.sendMessage(new Message(RocketMqConfig.WECHAT_ORDER_CHANGE_MQ_TOPIC,
                    RocketMqConfig.WECHAT_ORDER_CHANGE_MQ_TAG,
                    JacksonUtils.toJsonByte(new LocalOrderSaveMQDTO(UserContextUtils.getEnterpriseGuid(),
                            oldNewOrderGuidMap))));

        } catch (Exception e) {
            e.printStackTrace();
            log.info("上传失败:{}", e.getMessage());
            throw new BusinessException("上传失败");
        }
        return oldNewOrderGuidMap;
    }

    @Override
    public void test() {
        OrderDO orderDO = orderService.getById("6616151583774212096");
        LocalDate businessDay;
        businessDay = CommonUtil.getBusinessDayLocal(
                storeClientService.queryStoreBizByGuid(UserContextUtils.getStoreGuid()),
                orderDO.getCheckoutTime()
        );
        System.out.println("businessDay=" + businessDay);
    }

    @Override
    @Transactional
    public Boolean deleteLocal(DeleteQuery deleteQuery) {
        Boolean flag = Boolean.TRUE;
        try {

            //1.查询所有本地化上传的数据
            List<OrderDO> orderLists = new ArrayList<>();
            //orderService.getLocal(deleteQuery);
            if (ObjectUtils.isEmpty(orderLists)) {
                flag = Boolean.FALSE;
                return flag;
            }
            List<Long> orderGuid = orderLists.stream().map(OrderDO::getGuid).collect(Collectors.toList());
            List<String> orderGuids = orderLists.stream().map(OrderDO::getGuid).map(String::valueOf).collect
                    (Collectors.toList());

            //2.查询相关数据
            List<OrderItemDO> orderItemLists = orderItemService.listByOrderLists(orderGuids);
            List<Long> itemGuids = new ArrayList<>();
            List<ItemAttrDO> attrLists = new ArrayList<>();

            if (!ObjectUtils.isEmpty(orderItemLists)) {
                itemGuids = orderItemLists.stream().map(OrderItemDO::getGuid).collect(Collectors.toList());
                attrLists = itemAttrService.listByItemGuids(itemGuids);
            }
            List<FreeReturnItemDO> freeLists = freeReturnItemService.listByOrderGuids(orderGuids);
            List<TransactionRecordDO> tranLists = transactionRecordService.listByOrderGuids(orderGuid);
            List<AppendFeeDO> appendFeeLists = appendFeeMpService.listByOrderGuids(orderGuids);
            List<DiscountDO> discountLists = discountService.listByOrderGuids(orderGuids);

            //3.删除数据
            if(CollectionUtil.isNotEmpty(orderGuids)){
                orderService.removeByIds(orderGuids);
            }

            if (!ObjectUtils.isEmpty(itemGuids)) {
                orderItemService.removeByIds(itemGuids);
            }

            if (!ObjectUtils.isEmpty(discountLists)) {
                List<Long> discountS = discountLists.stream().map(DiscountDO::getGuid).collect(Collectors.toList());
                discountService.removeByIds(discountS);
            }

            if (!ObjectUtils.isEmpty(tranLists)) {
                List<Long> trans = tranLists.stream().map(TransactionRecordDO::getGuid).collect(Collectors.toList());
                transactionRecordService.removeByIds(trans);
            }

            if (!ObjectUtils.isEmpty(attrLists)) {
                List<Long> attrs = attrLists.stream().map(ItemAttrDO::getGuid).collect(Collectors.toList());
                itemAttrService.removeByIds(attrs);
            }

            if (!ObjectUtils.isEmpty(appendFeeLists)) {
                List<Long> appends = appendFeeLists.stream().map(AppendFeeDO::getGuid).collect(Collectors.toList());
                appendFeeMpService.removeByIds(appends);
            }

            if (!ObjectUtils.isEmpty(freeLists)) {
                List<Long> frees = freeLists.stream().map(FreeReturnItemDO::getGuid).collect(Collectors.toList());
                freeReturnItemService.removeByIds(frees);
            }
        } catch (Exception e) {
            flag = Boolean.FALSE;
            e.printStackTrace();
        }
        return flag;
    }

    /**
     * 生成orderGuid+主单的guid更新到子单的mainOrderGuid
     *
     * @param dataList
     * @return
     */
    public List<OrderDO> dealSubOrder(List<OrderDTO> dataList, Map<String, String> oldNewOrderGuidMap,
                                      Map<String, OrderDTO> orderDTOValue)
            throws BusinessException {
        Map<String, String> mainOrderGuid = new HashMap<>();
        List<Long> orderBatchGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_ORDER, dataList.size());
        Iterator it = orderBatchGuids.iterator();
        List<OrderDTO> returnList = dataList.stream()
                .peek(orderDTO -> {
                    String orderGuid;
                    if (it.hasNext()) {
                        orderGuid = String.valueOf(it.next());
                    } else {
                        orderGuid = String.valueOf(dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER));
                    }
                    //如果是主单，旧的orderGuid为key，新的orderGuid为value，后续用来更新子单mainorderGuid
                    if (orderDTO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
                        if (orderDTO.getOrderSource() == 1) {
                            mainOrderGuid.put(orderDTO.getGuid(), orderGuid);
                        }
                    }
                    OrderDTO orderData = new OrderDTO();
                    orderData.setOrderSource(orderDTO.getOrderSource());
                    orderData.setGuid(orderDTO.getGuid());
                    orderDTOValue.put(orderGuid, orderData);

                    if (orderDTO.getOrderSource() == 1) {
                        oldNewOrderGuidMap.put(String.valueOf(orderDTO.getGuid()), orderGuid);
                    } else {
                        oldNewOrderGuidMap.put(String.valueOf(orderDTO.getGuid()), String.valueOf(orderDTO.getGuid()));
                    }

                    orderDTO.setGuid(orderGuid);
                    orderDTO.setCheckinTime(DateTimeUtils.now());
                })
                .collect(Collectors.toList());

        //更新子单mainOrderGuid
        if (!ObjectUtils.isEmpty(mainOrderGuid)) {
            returnList = returnList.stream()
                    .peek(orderDO -> {
                        if (orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
                            //循环取出map
                            for (String oldGuid : mainOrderGuid.keySet()) {
                                //如果子单的mainOrderGuid等于map的key值，则将新的orderGuid设置进去
                                if (String.valueOf(orderDO.getMainOrderGuid()).equals(oldGuid)) {
                                    orderDO.setMainOrderGuid(mainOrderGuid.get(oldGuid));
                                }
                            }
                        }
                    }).collect(Collectors.toList());
        }

        List<OrderDO> data = localTransform.orderDTOSList2DOList(returnList);
        return data;
    }

}
