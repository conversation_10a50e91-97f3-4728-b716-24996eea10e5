package com.holderzone.saas.store.trade.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FreeReturnTypeEnum
 * @date 2018/10/23 11:54
 * @description
 * @program holder-saas-store-trade
 */
public enum FreeReturnTypeEnum {
    RETURN(1, "退货"),
    FREE(2, "赠送"),;

    private int code;
    private String desc;

    FreeReturnTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (FreeReturnTypeEnum c : FreeReturnTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
