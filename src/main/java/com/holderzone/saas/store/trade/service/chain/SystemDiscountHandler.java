package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-26
 * @description
 */
@Component
@Slf4j
public class SystemDiscountHandler extends DiscountHandler {
    @Override
    void dealDiscount(DiscountContext context) {
        // 14.系统省零
        DiscountFeeDetailDTO system = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap().get
                (type()));
        system.setDiscountFee(AmountCalculationUtil.getSystemDiscountFee(context.getDiscountRuleBO().getSystemDiscountDTOS(),
                context.getOrderDetailRespDTO().getOrderSurplusFee()));
        context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(system
                .getDiscountFee()));
        log.info("7.系统省零计算后订单剩余金额：{}，优惠金额：{}", context.getOrderDetailRespDTO().getOrderSurplusFee(), system.getDiscountFee());
        context.getDiscountFeeDetailDTOS().add(system);
        handleDiscountTotalPrice(context, system);
        log.info("8.整单让价优惠菜品金额：{}", JacksonUtils.writeValueAsString(context.getAllItems()));

    }

    @Override
    Integer type() {
        return DiscountTypeEnum.SYSTEM.getCode();
    }


    /**
     * 商品实付金额处理
     */
    private void handleDiscountTotalPrice(DiscountContext context, DiscountFeeDetailDTO systemDiscount) {
        log.info("[系统省零处理]系统省零优惠信息：{}", JacksonUtils.writeValueAsString(systemDiscount));
        List<DineInItemDTO> allItems = context.getAllItems();
        Map<String, DineInItemDTO> itemDTOMap = context.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (v1, v2) -> v1));
        BigDecimal discountFee = systemDiscount.getDiscountFee();

        DineinOrderDetailRespDTO orderDetailRespDTO = context.getOrderDetailRespDTO();
        List<DineInItemDTO> calculateItemList = new ArrayList<>(allItems);
        if (BigDecimalUtil.greaterThanZero(orderDetailRespDTO.getAppendFee())) {
            DineInItemDTO appendFeeItem = new DineInItemDTO();
            appendFeeItem.setGuid("-1");
            appendFeeItem.setDiscountTotalPrice(orderDetailRespDTO.getAppendFee()
                    .subtract(Optional.ofNullable(orderDetailRespDTO.getAppendDiscountFee()).orElse(BigDecimal.ZERO)));
            calculateItemList.add(appendFeeItem);
        }

        Map<String, BigDecimal> itemDiscountPriceMap = PriceCalculationUtils.calculationItemDiscountPrice(discountFee, calculateItemList);
        itemDiscountPriceMap.forEach((guid, itemDiscountFee) -> {
            DineInItemDTO dineInItemDTO = itemDTOMap.get(guid);
            if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(itemDiscountFee));
            }
        });
        // 附加费优惠金额
        orderDetailRespDTO.setAppendDiscountFee(Optional.ofNullable(orderDetailRespDTO.getAppendDiscountFee()).orElse(BigDecimal.ZERO)
                .add(itemDiscountPriceMap.getOrDefault("-1", BigDecimal.ZERO)));
    }
}
