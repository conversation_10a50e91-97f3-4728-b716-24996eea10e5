package com.holderzone.saas.store.trade.context;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;

public class RequestContext {

    public static void setBaseDTO(BaseDTO baseDTO) {
        UserContext userContext = new UserContext();
        userContext.setUserName(baseDTO.getUserName());
        userContext.setUserGuid(baseDTO.getUserGuid());
        userContext.setStoreName(baseDTO.getStoreName());
        userContext.setStoreGuid(baseDTO.getStoreGuid());
        userContext.setEnterpriseGuid(baseDTO.getEnterpriseGuid());
        userContext.setAccount(baseDTO.getAccount());
        userContext.setEnterpriseName(baseDTO.getEnterpriseName());
        UserContextUtils.put(userContext);
    }
}
