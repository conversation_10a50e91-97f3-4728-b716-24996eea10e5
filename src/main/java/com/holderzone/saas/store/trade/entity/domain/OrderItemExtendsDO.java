package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单商品扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Data
@TableName("hst_order_item_extends")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="HstOrderItemExtends对象", description="订单商品扩展表")
public class OrderItemExtendsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "guid", type = IdType.INPUT)
    @ApiModelProperty(value = "全局唯一主键")
    private Long guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "是否删除 0：false,1:true")
    private Boolean isDelete;

    @ApiModelProperty(value = "订单guid")
    private Long orderGuid;

    @ApiModelProperty(value = "用户微信公众号openId")
    private String userWxPublicOpenId;

    @ApiModelProperty(value = "微信菜品批次")
    private String wxBatch;

    @ApiModelProperty(value = "pad下单guid")
    private String padOrderGuid;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "是否套餐子菜换菜")
    private Integer changeFlag;

    /**
     * 是否打厨
     * 默认ture
     */
    @ApiModelProperty(value = "是否打厨")
    private Boolean isKitchen;

    @ApiModelProperty(value = "是否转菜")
    private Boolean transferFlag;

    /**
     * 验券加购是否使用购买价，默认false
     */
    @ApiModelProperty(value = "验券加购是否使用购买价")
    private Boolean isUseCouponPrice;

    /**
     * 验券前价格，默认0
     */
    @ApiModelProperty(value = "验券前价格")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal beforeCouponPrice;

    /**
     * 验券前价格，默认0
     */
    @ApiModelProperty(value = "验券前子项加价价格")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal beforeCouponAddPrice;

    /**
     * 团购实付金额分摊
     */
    private BigDecimal grouponDiscountTotalPrice;

    /**
     * 自定义退款金额分摊
     */
    private BigDecimal refundPrice;
}
