package com.holderzone.saas.store.trade.transform;


import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderItemRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderRespDTO;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDO;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDetailsDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface AdjustOrderTransform {

    AdjustOrderTransform INSTANCE = Mappers.getMapper(AdjustOrderTransform.class);

    AdjustOrderDetailRespDTO adjustOrderDO2AdjustOrderDetailRespDTO(AdjustOrderDO adjustOrderDO);

    DineInItemDTO adjustOrderDetailsDO2DineInItemDTO(AdjustOrderDetailsDO adjustOrderDetailsDO);

    SubDineInItemDTO adjustOrderDetailsDO2SubDineInItemDTO(AdjustOrderDetailsDO subGroupOrderItemDO);

    AdjustOrderDetailsDO subDineInItemDTO2AdjustOrderDetailsDO(SubDineInItemDTO subDineInItemDTO);

    AdjustOrderDetailsDO dineInItemDTO2AdjustOrderDetailsDO(DineInItemDTO dineInItemDTO);

    AdjustOrderRespDTO adjustOrderDO2AdjustOrderDTO(AdjustOrderDO adjustOrderDO);

    List<AdjustOrderRespDTO> adjustOrderDOs2AdjustOrderDTOs(List<AdjustOrderDO> adjustOrderDOList);

    AdjustByOrderItemRespDTO adjustOrderDetailsDO2AdjustByOrderItemRespDTO(AdjustOrderDetailsDO detailsDO);

    List<AdjustByOrderItemRespDTO> adjustOrderDetailsDOs2AdjustByOrderItemRespDTOs(List<AdjustOrderDetailsDO> details);
}
