package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-06-26
 * @description
 */
@Component
@Slf4j
public class SingleDiscountHandler extends DiscountHandler{
    @Override
    void dealDiscount(DiscountContext context) {
        if(context.isRejectDiscount()){
            return;
        }
        DiscountFeeDetailDTO singeItemDiscountFee = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap()
                .get(type()));
        // 处理折扣金额
        BigDecimal singleDicountFee = dealWithDiscountFee(context.getOrderDetailRespDTO().getOrderSurplusFee(),
                AmountCalculationUtil.getSingleDicountFee(context.getAllItems(), context.isCanMemberPrice()));
        singeItemDiscountFee.setDiscountFee(singleDicountFee);
        context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(singeItemDiscountFee
                .getDiscountFee()));
        context.getDiscountFeeDetailDTOS().add(singeItemDiscountFee);
        log.info("2.3 .单品折扣优惠：{}，订单剩余金额：{}", singeItemDiscountFee.getDiscountFee(),
                context.getOrderDetailRespDTO().getOrderSurplusFee());
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.SINGLE_DISCOUNT.getCode();
    }
}
