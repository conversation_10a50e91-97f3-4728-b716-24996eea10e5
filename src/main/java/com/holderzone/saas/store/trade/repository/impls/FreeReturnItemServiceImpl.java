package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.trade.entity.domain.FreeReturnItemDO;
import com.holderzone.saas.store.trade.entity.enums.FreeReturnTypeEnum;
import com.holderzone.saas.store.trade.mapper.FreeReturnItemMapper;
import com.holderzone.saas.store.trade.repository.interfaces.FreeReturnItemService;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 赠送/退货记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
public class FreeReturnItemServiceImpl extends ServiceImpl<FreeReturnItemMapper, FreeReturnItemDO> implements
        FreeReturnItemService {

    @Override
    public List<FreeReturnItemDO> listFreeByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<FreeReturnItemDO>().eq(FreeReturnItemDO::getOrderGuid, orderGuid).eq
                (FreeReturnItemDO::getType, FreeReturnTypeEnum.FREE.getCode()));
    }

    @Override
    public List<FreeReturnItemDO> listByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<FreeReturnItemDO>().eq(FreeReturnItemDO::getOrderGuid, orderGuid));
    }


    @Override
    public List<FreeReturnItemDO> listByOrderGuids(List<String> orderGuid) {
        if (CollectionUtil.isEmpty(orderGuid)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<FreeReturnItemDO>().in(FreeReturnItemDO::getOrderGuid, orderGuid));
    }

    @Override
    public void deleteByOrderGuids(List<String> orderGuids) {
        if(CollectionUtil.isEmpty(orderGuids)){
            return;
        }
        remove(new LambdaQueryWrapper<FreeReturnItemDO>().in(FreeReturnItemDO::getOrderGuid, orderGuids));
    }

    @Override
    public void removeByItemGuids(ArrayList<Long> orderItemGuids) {
        if(CollectionUtil.isEmpty(orderItemGuids)){
            return;
        }
        remove(new LambdaQueryWrapper<FreeReturnItemDO>().in(FreeReturnItemDO::getOrderItemGuid, orderItemGuids).ne
                (FreeReturnItemDO::getType, 1));
    }

    /**
     * 根据itemGuid找赠送或者免费的菜
     */
    @Override
    public List<FreeReturnItemDO> listByItemGuids(List<Long> orderItemGuids, FreeReturnTypeEnum freeReturnTypeEnum) {
        if(CollectionUtils.isEmpty(orderItemGuids)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<FreeReturnItemDO> lambdaQueryWrapper = new LambdaQueryWrapper<FreeReturnItemDO>().in(FreeReturnItemDO::getOrderItemGuid, orderItemGuids);
        if(freeReturnTypeEnum != null){
            lambdaQueryWrapper.eq(FreeReturnItemDO::getType, freeReturnTypeEnum.getCode());
        }
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<FreeReturnItemDO> listByGuids(List<Long> guids) {
        if(CollectionUtils.isEmpty(guids)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<FreeReturnItemDO> lambdaQueryWrapper = new LambdaQueryWrapper<FreeReturnItemDO>().in(FreeReturnItemDO::getGuid, guids);
        return list(lambdaQueryWrapper);
    }

    @Override
    public void updateRefundCountByGuid(Long guid, BigDecimal refundCount) {
        UpdateWrapper<FreeReturnItemDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(FreeReturnItemDO::getGuid, guid);
        uw.setSql("refund_count = refund_count + " + refundCount.toPlainString());
        update(uw);
    }
}
