package com.holderzone.saas.store.trade.helper;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.request.item.TransferItemDetailsReqDTO;
import com.holderzone.saas.store.dto.order.request.item.TransferItemReqDTO;
import com.holderzone.saas.store.dto.pay.AggRefundRespDTO;
import com.holderzone.saas.store.dto.pay.constant.AggRefundStateEnum;
import com.holderzone.saas.store.dto.trade.exception.OrderItemTransferVerifyException;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.constant.CommonConstant;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-08-27
 * @description 结算校验工具
 */
@Slf4j
public class BillVerifyHelper {

    private BillVerifyHelper() {
        throw new UnsupportedOperationException();
    }

    /**
     * 校验聚合支付退款结果
     *
     * @param refund  返回参数
     * @param portion 是否是部分退款
     */
    public static void refundResult(AggRefundRespDTO refund, boolean portion) {
        log.info("调用聚合支付退款返回：{}", JacksonUtils.writeValueAsString(refund));
        //如果不是部分退款并且返回结果是已经被退则直接返回
        if (!portion && CommonConstant.AGG_REFUNDED.equals(refund.getCode()) && refund.getMsg().contains(CommonConstant.AGG_REFUNDED_MSG)) {
            return;
        }
        if (!(CommonConstant.AGG_SUCCESS.equals(refund.getCode()) || CommonConstant.AGG_SUCCESS_TOO
                .equals(refund.getCode()))) {
            throw new BusinessException(String.format(CommonConstant.AGG_REFUND_FAILURE_EXCEPTION_MSG, refund.getMsg()));
        }
        if (StringUtils.isNotEmpty(refund.getMsg()) && refund.getMsg().contains(CommonConstant.AGG_REFUND_MSG_FAILURE)) {
            throw new BusinessException(String.format(CommonConstant.AGG_REFUND_FAILURE_EXCEPTION_MSG, refund.getMsg()));
        }
        if (refund.getState().equals(AggRefundStateEnum.REFUND_FAILURE.getState())) {
            throw new BusinessException(String.format(CommonConstant.AGG_REFUND_FAILURE_EXCEPTION_MSG, refund.getMsg()));
        }
    }


    /**
     * 校验多笔聚合支付批量退款结果
     */
    public static void multipleRefundResult(AggRefundRespDTO refund, boolean portion) {
        log.info("调用多笔聚合支付批量退款返回：{}", JacksonUtils.writeValueAsString(refund));
        //如果不是部分退款并且返回结果是已经被退则直接返回
        if (!portion && CommonConstant.AGG_REFUNDED.equals(refund.getCode()) && refund.getMsg().contains(CommonConstant.AGG_REFUNDED_MSG)) {
            return;
        }
        if (!(CommonConstant.AGG_SUCCESS.equals(refund.getCode()) || CommonConstant.AGG_SUCCESS_TOO
                .equals(refund.getCode()))) {
            throw new BusinessException(refund.getMsg());
        }
        if (StringUtils.isNotEmpty(refund.getMsg()) && refund.getMsg().contains(CommonConstant.AGG_REFUND_MSG_FAILURE)) {
            throw new BusinessException(refund.getMsg());
        }
        if (refund.getState().equals(AggRefundStateEnum.REFUND_FAILURE.getState())) {
            throw new BusinessException(refund.getMsg());
        }
    }

    /**
     * 转菜校验
     */
    public static void verifyTransferItem(TransferItemReqDTO transferReq,
                                          OrderDO oldOrderDO,
                                          OrderDO newOrderDO,
                                          List<OrderItemDO> oldOrderItemDOList) {
        Integer oldOrderState = oldOrderDO.getState();
        Integer newOrderState = newOrderDO.getState();
        if (oldOrderState != StateEnum.READY.getCode() || newOrderState != StateEnum.READY.getCode()) {
            throw new OrderItemTransferVerifyException("该订单状态不支持转菜");
        }
        Map<Long, BigDecimal> oldOrderItemDOMap = oldOrderItemDOList.stream()
                .collect(Collectors.toMap(OrderItemDO::getGuid, OrderItemDO::getCurrentCount, (key1, key2) -> key1));
        List<TransferItemDetailsReqDTO> transferItemDetails = transferReq.getItemDetailsDTOList();
        for (TransferItemDetailsReqDTO transferItemDetail : transferItemDetails) {
            BigDecimal orderItemCount = oldOrderItemDOMap.get(Long.valueOf(transferItemDetail.getOrderItemGuid()));
            if (Objects.isNull(orderItemCount)) {
                throw new OrderItemTransferVerifyException("订单转菜商品异常，请刷新后重试");
            }
            BigDecimal transferCount = transferItemDetail.getTransferCount();
            if (orderItemCount.compareTo(transferCount) < 0) {
                throw new OrderItemTransferVerifyException("订单转菜商品数量不足，请刷新后重试");
            }
        }
    }

}
