package com.holderzone.saas.store.trade.utils;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.BaseTableDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.enums.trade.StateEnum;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderUtil
 * @date 2019/02/20 14:23
 * @description //订单工具类
 * @program holder-saas-store-trade
 */
public class OrderUtil {

    private OrderUtil() {
    }

    public static boolean unfinished(OrderDO orderDO) {
        return orderDO.getState().equals(StateEnum.READY.getCode()) || orderDO.getState().equals(StateEnum.FAILURE
                .getCode()) || orderDO.getState().equals(StateEnum.PENDING.getCode());
    }
    
    /**
     * 订单表state转换交易状态
     * 状态(1：未结账， 2：已结账， 3：已退款，4：已作废)
     */
    public static int fixOrderState(int state) {
        if (StateEnum.SUCCESS.getCode() == state) {
            return 2;
        }
        if (StateEnum.REFUNDED.getCode() == state) {
            return 3;
        }
        if (StateEnum.CANCEL.getCode() == state) {
            return 4;
        }
        if (StateEnum.ANTI_SETTLEMENT.getCode() == state) {
            return 7;
        }
        if (StateEnum.SUB_SUCCESS.getCode() == state) {
            return 12;
        }
        return  1;
    }

    /**
     * 获取订单id  要么继承BaseTableDto 要么在字段上加 @OrderLockField 注解
     */
    public static String getOrderGuidFromParm(Object parameter) {
        if (parameter instanceof BaseTableDTO) {
            return ((BaseTableDTO) parameter).getOrderGuid();
        } else if (parameter instanceof BaseDTO) {
            Class clazz = parameter.getClass();
            try {
                Field[] declaredFields = clazz.getDeclaredFields();
                for (Field field : declaredFields) {
                    if (field.getAnnotation(OrderLockField.class) != null) {
                        field.setAccessible(true);
                        return (String) field.get(parameter);
                    }
                }
            } catch (Exception e) {
                throw new ParameterException("guid 参数缺失");
            }
        }
        return null;
    }


}
