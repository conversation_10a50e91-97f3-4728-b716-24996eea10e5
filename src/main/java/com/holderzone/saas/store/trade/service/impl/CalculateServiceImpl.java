package com.holderzone.saas.store.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.volume.*;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.config.resp.DineFoodSettingRespDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BilMemberCardCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillMemberCardCalculateRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.member.MemberLoginEnum;
import com.holderzone.saas.store.enums.member.MemberLoginTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.trade.constants.UpperState;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.context.LocalizeSynchronizeContext;
import com.holderzone.saas.store.trade.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.dto.ludou.LudouMemberDTO;
import com.holderzone.saas.store.trade.entity.enums.FreeReturnTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.mapper.OrderMapper;
import com.holderzone.saas.store.trade.repository.external.LudouMemberExternalService;
import com.holderzone.saas.store.trade.repository.feign.*;
import com.holderzone.saas.store.trade.repository.impls.ItemAttrServiceImpl;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.*;
import com.holderzone.saas.store.trade.service.chain.DiscountChain;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.*;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CalculateServiceImpl
 * @date 2019/01/28 17:28
 * @description
 * @program holder-saas-store-trade
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CalculateServiceImpl implements CalculateService {

    private final OrderService orderService;

    private final OrderItemService orderItemService;

    private final OrderItemExtendsService orderItemExtendsService;

    private final ItemAttrServiceImpl itemAttrService;

    private final FreeReturnItemService freeReturnItemService;

    private final BusinessClientService businessClientService;

    private final DiscountService discountService;

    private final DynamicHelper dynamicHelper;

    private final AppendFeeService appendFeeService;

    private final GrouponMpService grouponMpService;

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    private final RedisHelper redisHelper;
    private final OrderMapper orderMapper;

    private final MemberTerminalClientService memberTerminalClientService;

    private final IThirdActivityRecordService thirdActivityRecordService;

    private final ThirdActivityClientService thirdActivityClientService;

    private final ReserveClientService reserveClientService;

    private final PadOrderService padOrderService;

    private final PackageSubgroupChangesService packageSubgroupChangesService;

    private final OrderMultiMemberService orderMultiMemberService;

    private final OrderExtendsService orderExtendsService;

    private final IMultipleTransactionRecordService multipleTransactionRecordService;

    private final DineInService dineInService;

    private final WxStoreClientService wxStoreClientService;

    private final LudouMemberExternalService ludouMemberExternalService;

    private final BusinessDataSettingService businessDataSettingService;

    private static final String LOGIN_TYPE = "LOGIN_TYPE:";


    /**
     * 结账时计算账单金额和获取订单详情
     *
     * @param billCalculateReqDTO billCalculateReqDTO
     * @return DineinOrderDetailRespDTO
     */
    @Override
    @Transactional
    public DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO) {
        String orderGuid = billCalculateReqDTO.getOrderGuid();
        //获取全部的订单
        OrderDO mainOrderDO = null;
        //查询订单（合并的订单有多条记录）
        List<OrderDO> allOrderList = orderMapper.selectByGuidOrMainOrderGuid(orderGuid);
        if (allOrderList.size() == 1 && !OrderUtil.unfinished(allOrderList.get(0))) {
            throw new ParameterException("只有未结账订单可以结算");
        }
        List<DineinOrderDetailRespDTO> subOrderDetails = new ArrayList<>();
        List<OrderDO> subOrderDOS = new ArrayList<>();
        DineinOrderDetailRespDTO orderDetailRespDTO = null;
        for (OrderDO orderDO : allOrderList) {
            if (orderDO.getState().equals(StateEnum.INVALID.getCode())
                    || orderDO.getState().equals(StateEnum.CANCEL.getCode()) || orderDO.getState().equals(StateEnum.SUCCESS.getCode())) {
                continue;
            }
            if (!OrderUtil.unfinished(orderDO)) {
                throw new ParameterException("只有未结账订单可以结算");
            }
            LocalizeSynchronizeContext.putOrderGuid(String.valueOf(orderDO.getGuid()));
            DineinOrderDetailRespDTO dineinOrderDetailRespDTO = getSingleOrderDetail(orderDO);

            if (orderDO.getUpperState() != UpperState.SUB_ORDER) {
                //主订单
                mainOrderDO = orderDO;
                orderDetailRespDTO = dineinOrderDetailRespDTO;
            } else {
                //子订单
                subOrderDOS.add(orderDO);
                subOrderDetails.add(dineinOrderDetailRespDTO);
            }
        }
        if (mainOrderDO == null) {
            throw new BusinessException("无法找到主订单");
        }
        orderDetailRespDTO.setSubOrderDetails(subOrderDetails);
        // 结账不清台
        queryOtherOrderDetails(orderDetailRespDTO);
        //金额计算
        calculateAmountChain(orderDetailRespDTO, mainOrderDO, billCalculateReqDTO, subOrderDOS);

        //附加费详情
        if (BigDecimalUtil.greaterThanZero(orderDetailRespDTO.getAppendFee())) {
            OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setData(orderDetailRespDTO.getGuid());
            orderFeeDetailDTO.setAppendFeeDetailDTOS(appendFeeService.appendFeeList(singleDataDTO));
            orderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        }
        // 预定单信息
        setReserveOrderInfo(orderDetailRespDTO);
        // 订单多卡信息
        setOrderMultiMemberInfo(orderDetailRespDTO);
        // 多次聚合支付信息
        List<Long> allOrderGuidList = allOrderList.stream()
                .map(OrderDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        setActuallyPayFeeDetailDTOS(orderDetailRespDTO, allOrderGuidList);
        // 麓豆会员信息查询
        queryLudouMemberInfo(orderDetailRespDTO);
        //不对快餐加锁，所以如果是正餐，version才增加
        dinnerVersionHandle(orderDetailRespDTO, mainOrderDO);
        //登录方式记录
        checkLoginType(billCalculateReqDTO, orderDetailRespDTO);
        return orderDetailRespDTO;
    }

    /**
     * 查询当前桌台其他订单
     */
    private void queryOtherOrderDetails(DineinOrderDetailRespDTO orderDetailRespDTO) {
        // 查询门店一体机配置
        StoreConfigQueryDTO storeConfigQueryDTO = new StoreConfigQueryDTO();
        storeConfigQueryDTO.setStoreGuid(orderDetailRespDTO.getStoreGuid());
        DineFoodSettingRespDTO dineFoodSettingRespDTO = businessClientService.queryDineFoodSetting(storeConfigQueryDTO);
        log.info("查询门店一体机正餐配置:{}", JacksonUtils.writeValueAsString(dineFoodSettingRespDTO));
        orderDetailRespDTO.setCheckoutUnCloseTableFlag(Objects.nonNull(dineFoodSettingRespDTO.getEnableHandleClose())
                && dineFoodSettingRespDTO.getEnableHandleClose() == 1);
        // 如果是H5扫码点餐下的单,则查询H5是否结账不清台配置
        if (Boolean.FALSE.equals(orderDetailRespDTO.getCheckoutUnCloseTableFlag())
                && Objects.equals(BaseDeviceTypeEnum.WECHAT.getCode(), orderDetailRespDTO.getDeviceType())) {
            WxOrderConfigDTO wxOrderConfigDTO = wxStoreClientService.getStoreConfig(orderDetailRespDTO.getStoreGuid());
            log.info("查询微信门店配置:{}", wxOrderConfigDTO);
            orderDetailRespDTO.setCheckoutUnCloseTableFlag(Objects.nonNull(wxOrderConfigDTO.getIsCheckoutUnCloseTable())
                    && wxOrderConfigDTO.getIsCheckoutUnCloseTable() == 1);
        }
        if (!UpperStateEnum.SAME_ORDER_STATE.contains(orderDetailRespDTO.getUpperState())) {
            return;
        }
        String mainOrderGuid = orderDetailRespDTO.getGuid();
        if (Objects.equals(UpperStateEnum.SAME_SUB.getCode(), orderDetailRespDTO.getUpperState())) {
            mainOrderGuid = orderDetailRespDTO.getMainOrderGuid();
        }
        List<DineinOrderDetailRespDTO> otherOrderDetails = new ArrayList<>();
        List<OrderDO> otherOrderDOS = orderService.otherListByMainOrderGuid(Long.valueOf(mainOrderGuid));
        otherOrderDOS.removeIf(e -> Long.valueOf(orderDetailRespDTO.getGuid()).equals(e.getGuid()));
        for (OrderDO subOrderDO : otherOrderDOS) {
            DineinOrderDetailRespDTO orderDetail = dineInService.getSingleOrderDetail(subOrderDO);
            otherOrderDetails.add(orderDetail);
        }
        // 多单结账
        orderDetailRespDTO.setOtherOrderDetails(otherOrderDetails);
        // 订单号显示主单的订单号
        OrderDO mainOrderDO = otherOrderDOS.stream()
                .filter(e -> Objects.equals(UpperStateEnum.SAME_MAIN.getCode(), e.getUpperState()))
                .findFirst().orElse(null);
        orderDetailRespDTO.setOrderNo(Objects.nonNull(mainOrderDO) ? mainOrderDO.getOrderNo() : orderDetailRespDTO.getOrderNo());
    }

    /**
     * 查询麓豆会员信息
     */
    private void queryLudouMemberInfo(DineinOrderDetailRespDTO orderDetailRespDTO) {
        String ludouMemberGuid = orderDetailRespDTO.getLudouMemberGuid();
        if (StringUtils.isEmpty(ludouMemberGuid)) {
            return;
        }
        LudouMemberDTO ludouMemberDTO = ludouMemberExternalService.query(null, ludouMemberGuid);
        if (Objects.isNull(ludouMemberDTO)) {
            return;
        }
        BillMemberCardCalculateRespDTO billMemberCardCalculateRespDTO = new BillMemberCardCalculateRespDTO();
        billMemberCardCalculateRespDTO.setLudouMemberGuid(ludouMemberDTO.getUserId());
        billMemberCardCalculateRespDTO.setLudouMemberName(ludouMemberDTO.getNickName());
        billMemberCardCalculateRespDTO.setLudouMemberPhone(ludouMemberDTO.getPhone());
        billMemberCardCalculateRespDTO.setLudouMemberBalance(ludouMemberDTO.getUsableAmount());
        orderDetailRespDTO.setLudouMemberDTO(billMemberCardCalculateRespDTO);
    }

    private void setActuallyPayFeeDetailDTOS(DineinOrderDetailRespDTO orderDetailRespDTO,
                                             List<Long> allOrderGuidList) {
        List<MultipleTransactionRecordDO> multipleTransactionRecordDOList = multipleTransactionRecordService.listByOrderGuidList(allOrderGuidList);
        if (CollectionUtils.isEmpty(multipleTransactionRecordDOList)) {
            return;
        }
        multipleTransactionRecordDOList.forEach(multipleRecordDO -> {
            multipleRecordDO.setAmount(multipleRecordDO.getAmount().subtract(multipleRecordDO.getRefundAmount()));
        });
        multipleTransactionRecordDOList.removeIf(multipleRecordDO -> !BigDecimalUtil.greaterThanZero(multipleRecordDO.getAmount()));
        BigDecimal multiplePayFee = multipleTransactionRecordDOList.stream()
                .map(MultipleTransactionRecordDO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (BigDecimalUtil.greaterThanZero(multiplePayFee)) {
            orderDetailRespDTO.setMultiplePayFee(multiplePayFee);
        }
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS =
                orderTransform.multipleRecordDOS2ActuallyPayFeeDetailDTOS(multipleTransactionRecordDOList);
        for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : actuallyPayFeeDetailDTOS) {
            if (PaymentTypeEnum.otherOrGroupPay(actuallyPayFeeDetailDTO.getPaymentType())) {
                actuallyPayFeeDetailDTO.setPaymentTypeName(actuallyPayFeeDetailDTO.getPaymentTypeName());
            } else {
                actuallyPayFeeDetailDTO.setPaymentTypeName(PaymentTypeEnum.getDesc(actuallyPayFeeDetailDTO.getPaymentType()));
            }
        }
        orderDetailRespDTO.setActuallyPayFeeDetailDTOS(actuallyPayFeeDetailDTOS);
    }

    /**
     * 订单设置预定单信息
     */
    private void setReserveOrderInfo(DineinOrderDetailRespDTO orderDetailRespDTO) {
        // 预定单信息
        if (StringUtils.isEmpty(orderDetailRespDTO.getReserveGuid())) {
            return;
        }
        try {
            ReserveRecordGuidDTO reserveRecordGuidDTO = new ReserveRecordGuidDTO();
            reserveRecordGuidDTO.setGuid(orderDetailRespDTO.getReserveGuid());
            Integer reserveDeviceType = reserveClientService.obtainDeviceType(reserveRecordGuidDTO);
            orderDetailRespDTO.setReserveDeviceType(reserveDeviceType);
        } catch (Exception e) {
            log.error("查询预定单信息失败, e:", e);
        }
        // 设置预付金信息
        setReservePayInfo(orderDetailRespDTO.getReserveGuid(), orderDetailRespDTO);
    }

    private void setReservePayInfo(String reserveGuid, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        // 查询预付金信息
        SingleDataDTO dataDTO = new SingleDataDTO();
        dataDTO.setData(reserveGuid);
        log.info("[根据guid查询预付金信息]dataDTO={}", JacksonUtils.writeValueAsString(dataDTO));
        ReserveRecordDTO reserveRecordDTO = reserveClientService.queryByGuid(dataDTO);
        log.info("[根据guid查询预付金信息]reserveRecordDTO={}", JacksonUtils.writeValueAsString(reserveRecordDTO));
        if (!ObjectUtils.isEmpty(reserveRecordDTO)) {
            dineinOrderDetailRespDTO.setReserveFee(reserveRecordDTO.getReserveAmount());
            dineinOrderDetailRespDTO.setReservePaymentType(reserveRecordDTO.getPaymentType());
            dineinOrderDetailRespDTO.setReservePaymentTypeName(reserveRecordDTO.getPaymentTypeName());
            dineinOrderDetailRespDTO.setReserveRemark(reserveRecordDTO.getRemark());
            dineinOrderDetailRespDTO.setReserveGuid(reserveRecordDTO.getGuid());
            dineinOrderDetailRespDTO.setOrderType(reserveRecordDTO.getOrderType());
            if (Objects.equals(reserveRecordDTO.getOrderType(), OrderTypeEnum.RESERVE.getCode())) {
                dineinOrderDetailRespDTO.setReserveRemark(getReserveRemark(reserveRecordDTO));
            }
        }
    }

    @NotNull
    private String getReserveRemark(ReserveRecordDTO reserveRecordDTO) {
        StringBuilder strB = new StringBuilder();
        strB.append("预定订金");
        if (com.holderzone.framework.util.StringUtils.hasText(reserveRecordDTO.getName())) {
            strB.append("  ");
            strB.append(reserveRecordDTO.getName());
        }
        if (com.holderzone.framework.util.StringUtils.hasText(reserveRecordDTO.getPhone())) {
            strB.append("  ");
            strB.append(reserveRecordDTO.getPhone());
        }
        return strB.toString();
    }

    private void checkLoginType(BillCalculateReqDTO billCalculateReqDTO,
                                DineinOrderDetailRespDTO orderDetailRespDTO) {
        String key = LOGIN_TYPE + billCalculateReqDTO.getOrderGuid();
        if (Objects.nonNull(billCalculateReqDTO.getMemberLogin())) {

            log.info("登录方式key：{}", key);
            if (billCalculateReqDTO.getMemberLogin().equals(1)
                    && Objects.nonNull(billCalculateReqDTO.getLoginType())) {
                log.info("缓存登录方式：{}", billCalculateReqDTO.getLoginType());
                redisHelper.setEx(key, String.valueOf(billCalculateReqDTO.getLoginType()), 6, TimeUnit.HOURS);
                orderDetailRespDTO.setLoginType(billCalculateReqDTO.getLoginType());
            }

            if (billCalculateReqDTO.getMemberLogin().equals(2)) {
                log.info("删除登录方式：{}", key);
                redisHelper.delete(key);
            }
        }
        String loginType = redisHelper.get(key);
        log.info("查询loginType：{}", loginType);
        if (StringUtils.isNotEmpty(loginType)) {
            orderDetailRespDTO.setLoginType(Integer.parseInt(loginType));
        }
    }

    private void dinnerVersionHandle(DineinOrderDetailRespDTO orderDetailRespDTO, OrderDO mainOrderDO) {
        if (orderDetailRespDTO.getTradeMode().equals(TradeModeEnum.DINEIN.getCode())) {
            String versionStr = redisHelper.get(RedisKeyUtil.getHstOrderVersionKey(String.valueOf(mainOrderDO.getGuid
                    ())));
            if (null != versionStr) {
                orderDetailRespDTO.setVersion(Integer.parseInt(versionStr));
            }
        }
    }

    @Override
    public DineinOrderDetailRespDTO checkVolume(BillCalculateReqDTO billCalculateReqDTO) {
        String orderGuid = billCalculateReqDTO.getOrderGuid();
        OrderDO orderDO = orderService.getById(orderGuid);
        OrderDO mainOrderDO = new OrderDO();
        DineinOrderDetailRespDTO orderDetailRespDTO;

        //有并单情况
        List<OrderDO> subOrderDOS = null;
        if (!orderDO.getUpperState().equals(UpperStateEnum.GENERAL.getCode())) {
            if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
                mainOrderDO = orderDO;
            }
            if (orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
                mainOrderDO = orderService.getById(orderDO.getMainOrderGuid());
            }
            orderDetailRespDTO = getSingleOrderDetail(mainOrderDO);
            subOrderDOS = orderService.listByMainOrderGuid(String.valueOf(mainOrderDO.getGuid()));
            List<DineinOrderDetailRespDTO> subOrderDetails = new ArrayList<>();
            for (OrderDO subOrderDO : subOrderDOS) {
                DineinOrderDetailRespDTO dineinOrderDetailRespDTO = getSingleOrderDetail(subOrderDO);
                subOrderDetails.add(dineinOrderDetailRespDTO);
            }
            orderDetailRespDTO.setSubOrderDetails(subOrderDetails);
        } else {
            orderDetailRespDTO = getSingleOrderDetail(orderDO);
            mainOrderDO = orderDO;
        }

        //金额计算
        checkVolume(orderDetailRespDTO, mainOrderDO, billCalculateReqDTO);
        return orderDetailRespDTO;
    }

    //方法只用作构建结算页面显示的订单详细信息和计算每个菜的小计，金额和优惠计算移到外层
    @Override
    public DineinOrderDetailRespDTO getSingleOrderDetail(OrderDO orderDO) {
        String orderGuid = String.valueOf(orderDO.getGuid());
        List<OrderItemDO> orderItemDOS = orderItemService.listByOrderGuidWithCache(orderDO.getGuid(), orderDO.getDeviceType());
        orderItemDOS.stream().filter(a -> a.getPriceChangeType() == 0 && a.getOriginalPrice() == null).forEach(a -> a
                .setOriginalPrice(a.getPrice()));
        Map<Long, List<OrderItemDO>> itemListMap = CollectionUtil.toListMap(orderItemDOS, "guid");
        List<ItemAttrDO> itemAttrDOList = new ArrayList<>(itemAttrService.listByItemGuids(new ArrayList<>(itemListMap
                .keySet())));

        DineinOrderDetailRespDTO orderDetailRespDTO = orderTransform.orderDO2DineinOrderDetailRespDTO(orderDO);
        // 订单扩展信息
        queryOrderExtendsInfo(orderDetailRespDTO);

        //赠送信息
        List<FreeReturnItemDO> freeReturnItemDOS = null;
        if (AmountCalculationUtil.hasFree(orderItemDOS)) {
            Map<Long, OrderItemDO> itemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
            freeReturnItemDOS = freeReturnItemService.list(new
                    LambdaQueryWrapper<FreeReturnItemDO>().in(FreeReturnItemDO::getOrderItemGuid, new ArrayList<>
                    (itemDOMap.keySet())).eq(FreeReturnItemDO::getType, FreeReturnTypeEnum.FREE.getCode()));
        }
        //计算菜品相关
        List<DineInItemDTO> dineInItemDTOList = AmountCalculationUtil.buildItem(orderItemDOS, itemAttrDOList,
                freeReturnItemDOS);

        Map<String, DineInItemDTO> dineInItemDTOMap = CollectionUtil.toMap(dineInItemDTOList, "guid");

        //退货信息
        List<FreeReturnItemDO> returnItemDOS = freeReturnItemService.list(new LambdaQueryWrapper<FreeReturnItemDO>()
                .eq(FreeReturnItemDO::getOrderGuid, orderGuid).eq(FreeReturnItemDO::getType, FreeReturnTypeEnum
                        .RETURN.getCode()));
        List<ReturnItemDTO> returnItemDTOS = AmountCalculationUtil.bulidReturnItemDTOS(returnItemDOS, dineInItemDTOMap);
        orderDetailRespDTO.setReturnItemDTOS(returnItemDTOS);
        orderDetailRespDTO.setDineInItemDTOS(dineInItemDTOList);
        AmountCalculationUtil.filterZeroItem(orderDetailRespDTO);
        // 换菜信息
        packageSubgroupChangesService.addOriginalItemInfo(orderDetailRespDTO);
        return orderDetailRespDTO;
    }

    private void calculateAppendFeeAndUpdate(OrderDO orderDO, DineinOrderDetailRespDTO orderDetailRespDTO, boolean isMainOrder) {
        List<DineInItemDTO> dineInItemDTOList = orderDetailRespDTO.getDineInItemDTOS();
        BigDecimal appendFee;
        if (!ObjectUtils.isEmpty(orderDO.getOriginalOrderGuid())) {
            appendFee = appendFeeService.getAndUpdateAppendFeeByOriginal(orderDO);
        } else {
            // 如果是多单结账，则查询当前订单最大的点餐人数
            orderDO.setPreGuestCount(getPreOrderGuestCount(orderDO));
            appendFee = appendFeeService.getAndUpdateAppendFee(orderDO, null);
        }
        BigDecimal orderFee = AmountCalculationUtil.getOrderFee(dineInItemDTOList, appendFee);
        boolean isModified = false;
        if (orderDO.getAppendFee().compareTo(appendFee) != 0) {
            isModified = true;
        }
        if (orderDO.getOrderFee().compareTo(orderFee) != 0) {
            isModified = true;
        }
        orderDO.setAppendFee(appendFee);
        orderDO.setOrderFee(orderFee);
        if (isModified && !isMainOrder) {
            orderDetailRespDTO.setAppendFee(appendFee);
            orderDetailRespDTO.setOrderFee(orderFee);
            orderMapper.updateOrderFeeAndAppendFeeByGuid(
                    orderDO.getGuid(),
                    orderFee,
                    appendFee);
        }
    }

    /**
     * 查询当前订单最大的点餐人数
     */
    private Integer getPreOrderGuestCount(OrderDO orderDOInDb) {
        if (!UpperStateEnum.SAME_ORDER_STATE.contains(orderDOInDb.getUpperState())) {
            return orderDOInDb.getGuestCount();
        }
        Long mainOrderGuid = orderDOInDb.getGuid();
        if (Objects.nonNull(orderDOInDb.getMainOrderGuid()) && !Objects.equals(0L, orderDOInDb.getMainOrderGuid())) {
            mainOrderGuid = orderDOInDb.getMainOrderGuid();
        }
        List<OrderDO> orderList = orderService.otherListByMainOrderGuid(mainOrderGuid);
        orderList.removeIf(e -> e.getGuid().equals(orderDOInDb.getGuid()));
        OrderDO maxGuestOrderDO = orderList.stream()
                .max(Comparator.comparing(OrderDO::getGuestCount))
                .orElse(null);
        if (Objects.isNull(maxGuestOrderDO)) {
            return orderDOInDb.getGuestCount();
        }
        return maxGuestOrderDO.getGuestCount();
    }

    @Resource
    private DiscountChain discountChain;

    private void calculateAmountChain(DineinOrderDetailRespDTO orderDetailRespDTO, OrderDO orderDO,
                                      BillCalculateReqDTO billCalculateReqDTO, List<OrderDO> subOrderDOS) {
        log.warn("========================================开始计算订单金额==============================================");
        log.warn("订单guid：{},订单号：{}", orderDetailRespDTO.getGuid(), orderDetailRespDTO.getOrderNo());

        //并单取主单优惠规则&&返回当前的登录会员信息
        DiscountRuleBO discountRuleBO = getDiscountRuleBO(orderDO, billCalculateReqDTO, orderDetailRespDTO);
        log.warn("优惠计算规则：{}", JacksonUtils.writeValueAsString(discountRuleBO));
        //每次计算优惠时刷新所有桌台的附加费和订单金额
        refreshAppendFee(orderDetailRespDTO, orderDO, subOrderDOS, orderDetailRespDTO.getSubOrderDetails());
        //每次计算优惠时存储当前最新的优惠规则
        List<DiscountDO> discountDOS = updateNewDiscount(orderDetailRespDTO, discountRuleBO);
        // 团购券明细
        grouponDetail(orderDetailRespDTO);
        boolean rejectDiscount = checkRejectDiscount(orderDetailRespDTO, String.valueOf(orderDO.getGuid()));
        log.info("团购验券是否与其他优惠互斥：{}", rejectDiscount);

        // 是否使用商品购买价
        boolean useBuyPrice = businessDataSettingService.queryIsUseCouponBuyPrice(billCalculateReqDTO.getStoreGuid());
        log.info("是否使用商品购买价：{}", useBuyPrice);
        DiscountContext discountContext = DiscountContext.init(orderDetailRespDTO, billCalculateReqDTO, orderDO,
                subOrderDOS, discountRuleBO, discountDOS, rejectDiscount, useBuyPrice);
        discountChain.doDiscount(discountContext);

        // ============================== 优惠计算结束 ==============================
        orderDetailRespDTO.setDiscountFeeDetailDTOS(discountContext.getDiscountFeeDetailDTOS());

        //应付金额，优惠金额
        BigDecimal totalDiscountFee = BigDecimal.ZERO;
        for (DiscountFeeDetailDTO discountFeeDetailDTO : discountContext.getDiscountFeeDetailDTOS()) {
            //单品折扣,单品改价不需要再减
            if (discountFeeDetailDTO.getDiscountFee() != null) {
                totalDiscountFee = totalDiscountFee.add(discountFeeDetailDTO.getDiscountFee());
            }
        }
        log.warn("全部优惠：{}", JacksonUtils.writeValueAsString(discountContext.getDiscountFeeDetailDTOS()));
        orderDetailRespDTO.setDiscountFee(totalDiscountFee);
        orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getOrderFee().subtract(totalDiscountFee));
        log.warn("优惠金额合计：{},实付金额合计：{}", orderDetailRespDTO.getDiscountFee(), orderDetailRespDTO.getActuallyPayFee());

        //处理让价
        DiscountFeeDetailDTO concessional = new DiscountFeeDetailDTO();
        for (DiscountFeeDetailDTO discountFeeDetailDTO : orderDetailRespDTO.getDiscountFeeDetailDTOS()) {
            if (discountFeeDetailDTO.getDiscountFee() != null && discountFeeDetailDTO.getDiscountType().equals
                    (DiscountTypeEnum.CONCESSIONAL.getCode())) {
                concessional = discountFeeDetailDTO;
            }
        }

        //应付金额小于0时清除整单让价并重新计算优惠和应收
        if (BigDecimalUtil.lessThanZero(orderDetailRespDTO.getActuallyPayFee())) {
            orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getActuallyPayFee().add(concessional
                    .getDiscountFee()));
            orderDetailRespDTO.setDiscountFee(orderDetailRespDTO.getDiscountFee().subtract(concessional
                    .getDiscountFee()));
            concessional.setDiscountFee(BigDecimal.ZERO);
            DiscountDO discountDO = orderTransform.discountFeeDetailDTO2DiscountDO(concessional);
            discountService.updateById(discountDO);
            log.warn("应付金额小于0时清除整单让价并重新计算优惠和应收,优惠金额合计：{},实付金额合计：{}", orderDetailRespDTO.getDiscountFee(),
                    orderDetailRespDTO.getActuallyPayFee());
            log.warn("清除整单让价后全部优惠：{}", JacksonUtils.writeValueAsString(discountContext.getDiscountFeeDetailDTOS()));
        }
        int calculateByMemberPrice = discountContext.isCanMemberPrice() ? 1 : 0;
        if (orderDO.getCalculateByMemberPrice() != calculateByMemberPrice) {
            orderDO.setCalculateByMemberPrice(calculateByMemberPrice);
        }
        //防止更新订单状态
        orderDO.setState(null);
        //防止更新实际支付金额
        orderDO.setActuallyPayFee(null);
        orderService.updateById(orderDO);
        //处理优惠金额拆分
        log.warn("========================================处理优惠金额拆分==============================================");

        // 持久化订单附加费优惠相关金额
        saveOrderAppendDiscount(orderDetailRespDTO);
        // 持久化订单商品明细优惠相关金额
        saveOrderItemDiscount(orderDO, discountContext);
        if (!CollectionUtils.isEmpty(subOrderDOS)) {
            subOrderDOS.forEach(e -> saveOrderItemDiscount(e, discountContext));
        }

        log.warn("========================================结束计算订单金额==============================================");
        //如果金额为负数，则置为0
        handleZero(orderDetailRespDTO);
    }

    /**
     * 持久化订单附加费优惠相关金额
     */
    private void saveOrderAppendDiscount(DineinOrderDetailRespDTO orderDetailRespDTO) {
        BigDecimal appendFee = orderDetailRespDTO.getAppendFee();
        if (!BigDecimalUtil.greaterThanZero(appendFee)) {
            return;
        }
        // 附加费优惠金额
        BigDecimal appendDiscountFee = orderDetailRespDTO.getAppendDiscountFee();
        if (!BigDecimalUtil.greaterThanZero(appendDiscountFee)) {
            return;
        }
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderDetailRespDTO.getGuid());
        if (Objects.isNull(orderExtendsDO)) {
            orderExtendsDO = new OrderExtendsDO();
            BeanUtils.copyProperties(orderDetailRespDTO, orderExtendsDO);
            orderExtendsDO.setGuid(Long.valueOf(orderDetailRespDTO.getGuid()));
        }
        orderExtendsDO.setTotalAppendDiscountAmount(appendDiscountFee);
        orderExtendsService.saveOrUpdate(orderExtendsDO);
    }

    /**
     * 持久化订单商品明细优惠相关金额
     */
    private void saveOrderItemDiscount(OrderDO orderDO, DiscountContext discountContext) {
        Map<String, DineInItemDTO> orderItemDTOMap = new ArrayList<>(discountContext.getDineInItemDTOMap().values())
                .stream().collect(Collectors.toMap(DineInItemDTO::getGuid,
                        Function.identity(), (entity1, entity2) -> entity1));
        Map<String, DineInItemDTO> orderItemDTOAllMap = discountContext.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        log.warn("orderItemDTOMap：{}", JacksonUtils.writeValueAsString(discountContext.getDineInItemDTOMap()));
        List<OrderItemDO> orderItemList = orderItemService.listByOrderGuid(orderDO.getGuid());
        log.warn("orderItemList：{}", JacksonUtils.writeValueAsString(orderItemList));
        for (OrderItemDO orderItemDO : orderItemList) {
            DineInItemDTO dineInItemDTO = orderItemDTOMap.get(String.valueOf(orderItemDO.getGuid()));
            if (Objects.nonNull(dineInItemDTO)) {
                orderItemDO.setDiscountPreferential(dineInItemDTO.getDiscountPreferential());
                orderItemDO.setMemberPreferential(dineInItemDTO.getMemberPreferential());
                orderItemDO.setTicketPreferential(dineInItemDTO.getTicketPreferential());
            }
            DineInItemDTO inItemDTO = orderItemDTOAllMap.get(String.valueOf(orderItemDO.getGuid()));
            if (Objects.nonNull(inItemDTO)) {
                orderItemDO.setDiscountTotalPrice(inItemDTO.getDiscountTotalPrice());
                orderItemDO.setGrouponDiscountTotalPrice(inItemDTO.getGrouponDiscountTotalPrice());
            }
            // 计算套餐子项的实付金额
            calculateSubDineInItemDiscountTotalPrice(orderItemDO, orderItemList);
        }
        log.warn("更新orderItemList：{}", JacksonUtils.writeValueAsString(orderItemList));
        // 团购实付金额持久化
        saveOrderItemGrouponDiscountTotalPrice(orderItemList);
        orderItemService.updateBatchByIdWithDeleteCache(orderItemList, String.valueOf(orderDO.getGuid()));
    }

    /**
     * 计算套餐子商品的价格分摊
     */
    private void calculateSubDineInItemDiscountTotalPrice(OrderItemDO orderItemDO, List<OrderItemDO> orderItemList) {
        if (ItemTypeEnum.GROUP.getCode() != orderItemDO.getItemType() && ItemTypeEnum.TEAMMEAL.getCode() != orderItemDO.getItemType()) {
            return;
        }
        List<OrderItemDO> subOrderItemList = orderItemList.stream()
                .filter(e -> e.getParentItemGuid().equals(orderItemDO.getGuid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subOrderItemList)) {
            log.warn("子订单明细丢失, parentItemGuid:{}", orderItemDO.getGuid());
            return;
        }
        List<DineInItemDTO> calculateItemList = subOrderItemList.stream().map(e -> {
            DineInItemDTO dineInItemDTO = new DineInItemDTO();
            dineInItemDTO.setGuid(String.valueOf(e.getGuid()));
            BigDecimal totalPrice = Optional.ofNullable(e.getPrice()).orElse(BigDecimal.ZERO)
                    .multiply(e.getCurrentCount()).multiply(e.getPackageDefaultCount()).multiply(orderItemDO.getCurrentCount());
            BigDecimal totalAddPrice = Optional.ofNullable(e.getAddPrice()).orElse(BigDecimal.ZERO)
                    .multiply(e.getCurrentCount()).multiply(orderItemDO.getCurrentCount());
            BigDecimal totalAttrTotal = Optional.ofNullable(e.getAttrTotal()).orElse(BigDecimal.ZERO)
                    .multiply(e.getCurrentCount()).multiply(e.getPackageDefaultCount()).multiply(orderItemDO.getCurrentCount());
            dineInItemDTO.setDiscountTotalPrice(totalPrice.add(totalAddPrice).add(totalAttrTotal));
            return dineInItemDTO;
        }).collect(Collectors.toList());
        Map<String, BigDecimal> itemDiscountTotalPriceMap = PriceCalculationUtils.calculationItemDiscountPrice(
                orderItemDO.getDiscountTotalPrice(), calculateItemList);
        // 更新子项的实付金额
        subOrderItemList.forEach(e -> e.setDiscountTotalPrice(
                itemDiscountTotalPriceMap.getOrDefault(String.valueOf(e.getGuid()), e.getDiscountTotalPrice())));
        // 团购实付金额
        BigDecimal grouponDiscountTotalPrice = orderItemDO.getGrouponDiscountTotalPrice();
        if (BigDecimalUtil.greaterThanZero(grouponDiscountTotalPrice)) {
            Map<String, BigDecimal> grouponItemDiscountTotalPriceMap = PriceCalculationUtils.calculationItemDiscountPrice(
                    grouponDiscountTotalPrice, calculateItemList);
            // 更新子项的实付金额
            subOrderItemList.forEach(e -> e.setGrouponDiscountTotalPrice(
                    grouponItemDiscountTotalPriceMap.getOrDefault(String.valueOf(e.getGuid()), e.getDiscountTotalPrice())));
        }
    }

    /**
     * 团购实付金额持久化
     */
    private void saveOrderItemGrouponDiscountTotalPrice(List<OrderItemDO> orderItemList) {
        List<OrderItemDO> hasGrouponItemList = orderItemList.stream()
                .filter(e -> BigDecimalUtil.greaterThanZero(e.getGrouponDiscountTotalPrice()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasGrouponItemList)) {
            return;
        }
        Map<Long, OrderItemDO> hasGrouponItemMap = hasGrouponItemList.stream()
                .collect(Collectors.toMap(OrderItemDO::getGuid, Function.identity(), (key1, key2) -> key1));
        List<OrderItemExtendsDO> orderItemExtendsList = orderItemExtendsService.listByIds(hasGrouponItemMap.keySet());
        Map<Long, OrderItemExtendsDO> orderItemExtendsMap = orderItemExtendsList.stream()
                .collect(Collectors.toMap(OrderItemExtendsDO::getGuid, Function.identity(), (key1, key2) -> key1));
        List<OrderItemExtendsDO> orderItemExtendsUpdateList = Lists.newArrayList();
        for (Map.Entry<Long, OrderItemDO> entry : hasGrouponItemMap.entrySet()) {
            OrderItemExtendsDO orderItemExtendsDO = orderItemExtendsMap.get(entry.getKey());
            if (Objects.isNull(orderItemExtendsDO)) {
                orderItemExtendsDO = new OrderItemExtendsDO();
                BeanUtils.copyProperties(entry.getValue(), orderItemExtendsDO);
            }
            orderItemExtendsDO.setGrouponDiscountTotalPrice(entry.getValue().getGrouponDiscountTotalPrice());
            orderItemExtendsUpdateList.add(orderItemExtendsDO);
        }
        orderItemExtendsService.saveOrUpdateBatch(orderItemExtendsUpdateList);
    }

    private static void handleZero(DineinOrderDetailRespDTO orderDetailRespDTO) {
        if (BigDecimalUtil.lessThanZero(orderDetailRespDTO.getOrderSurplusFee())) {
            orderDetailRespDTO.setOrderSurplusFee(BigDecimal.ZERO);
        }
        if (BigDecimalUtil.lessThanZero(orderDetailRespDTO.getActuallyPayFee())) {
            orderDetailRespDTO.setActuallyPayFee(BigDecimal.ZERO);
        }
    }

    private void grouponDetail(DineinOrderDetailRespDTO orderDetailRespDTO) {
        List<GrouponDO> grouponDOList = grouponMpService.listByOrderGuid(orderDetailRespDTO.getGuid());
        if (CollectionUtils.isEmpty(grouponDOList)) {
            grouponDOList = new ArrayList<>();
        }
        List<GrouponListRespDTO> grouponListRespDTOList = orderTransform.grouponDOS2GrouponListRespDTOS(grouponDOList);
        log.warn("团购券明细={}", JacksonUtils.writeValueAsString(grouponListRespDTOList));
        orderDetailRespDTO.setGrouponListRespDTOS(grouponListRespDTOList);
    }

    private boolean checkRejectDiscount(DineinOrderDetailRespDTO orderDetailRespDTO, String orderGuid) {
        List<GrouponListRespDTO> grouponDTOList = orderDetailRespDTO.getGrouponListRespDTOS();
        long nonThirdActivityCount = grouponDTOList.stream().filter(g -> StringUtils.isEmpty(g.getActivityGuid())).count();
        if (nonThirdActivityCount > 0) {
            log.warn("非三方活动互斥");
            return true;
        }
        List<ThirdActivityRecordDTO> thirdActivityRecordDTOList = thirdActivityRecordService.listThirdActivityByOrderGuid(orderGuid);
        log.info("[checkRejectDiscount]订单使用活动={}", JacksonUtils.writeValueAsString(thirdActivityRecordDTOList));
        if (!CollectionUtils.isEmpty(thirdActivityRecordDTOList)) {
            List<String> guidList = thirdActivityRecordDTOList.stream()
                    .map(ThirdActivityRecordDTO::getActivityGuid)
                    .collect(Collectors.toList());
            List<ThirdActivityRespDTO> thirdActivityList = thirdActivityClientService.inProcessListByGuid(guidList);
            log.info("[checkRejectDiscount]订单下可用活动列表={}", JacksonUtils.writeValueAsString(thirdActivityList));
            // IsActivityShare：是否与其他优惠活动类型共享
            long thirdShareCount = thirdActivityList.stream().filter(t -> BooleanEnum.FALSE.getCode() == t.getIsActivityShare()).count();
            log.warn("三方活动互斥数量：{}", thirdShareCount);
            return thirdShareCount > 0;
        }
        return false;
    }

    private void refreshAppendFee(DineinOrderDetailRespDTO orderDetailRespDTO, OrderDO orderDO, List<OrderDO> subOrderDOS,
                                  List<DineinOrderDetailRespDTO> subOrderDetails) {
        if (orderDO.getTradeMode().equals(TradeModeEnum.DINEIN.getCode())) {
            BigDecimal appendFee = orderDO.getAppendFee();
            BigDecimal orderFee = orderDO.getOrderFee();
            calculateAppendFeeAndUpdate(orderDO, orderDetailRespDTO, true);
            BigDecimal calculatedAppendFee = orderDO.getAppendFee();
            BigDecimal calculatedOrderFee = orderDO.getOrderFee();
            if (!CollectionUtils.isEmpty(subOrderDOS)) {
                Map<String, DineinOrderDetailRespDTO> orderDetailMap = CollectionUtil.toMap(subOrderDetails, "guid");
                for (OrderDO subOrder : subOrderDOS) {
                    DineinOrderDetailRespDTO sub = orderDetailMap.get("" + subOrder.getGuid());
                    if (sub != null) {
                        calculateAppendFeeAndUpdate(subOrder, sub, false);
                        calculatedAppendFee = calculatedAppendFee.add(subOrder.getAppendFee());
                        calculatedOrderFee = calculatedOrderFee.add(subOrder.getOrderFee());
                    }
                }
            }
            if (calculatedAppendFee.compareTo(appendFee) != 0 || calculatedOrderFee.compareTo(orderFee) != 0) {
                orderDO.setAppendFee(calculatedAppendFee);
                orderDO.setOrderFee(calculatedOrderFee);
                orderDetailRespDTO.setAppendFee(calculatedAppendFee);
                orderDetailRespDTO.setOrderFee(calculatedOrderFee);
                orderMapper.updateOrderFeeAndAppendFeeByGuid(
                        orderDO.getGuid(),
                        calculatedOrderFee,
                        calculatedAppendFee);
            }

        }
        log.warn("订单金额：{}，附加费：{}", orderDetailRespDTO.getOrderFee(), orderDetailRespDTO.getAppendFee());
    }


    private List<DiscountDO> updateNewDiscount(DineinOrderDetailRespDTO orderDetailRespDTO, DiscountRuleBO discountRuleBO) {
        List<DiscountDO> discountDOS = discountService.listByOrderGuid(orderDetailRespDTO.getGuid());
        List<Long> discountDOGuidList;
        if (CollectionUtils.isEmpty(discountDOS)) {
            discountDOS = new ArrayList<>();
            discountDOGuidList = new ArrayList<>();
        } else {
            discountDOGuidList = discountDOS.stream()
                    .map(DiscountDO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(discountDOS)) {

            List<Long> guids = dynamicHelper.generateGuids(GuidKeyConstant.HST_DISCOUNT, DiscountTypeEnum.values()
                    .length);
            //默认写入所有优惠方式
            discountDOS = AmountCalculationUtil.getInsertDiscountDOS(guids, discountDOS, discountRuleBO,
                    orderDetailRespDTO.getGuid());
        } else {
            // 补全折扣方式
            if (discountDOS.size() < DiscountTypeEnum.values().length - 1) {
                // old method {@links com.holderzone.saas.store.trade.service.impl.CalculateServiceImpl.dealData}
                log.info("[补全前]discountDOS={}", discountDOS);
                int guidCount = DiscountTypeEnum.values().length - 1 - discountDOS.size();
                List<Long> guidLists = dynamicHelper.generateGuids(GuidKeyConstant.HST_DISCOUNT, guidCount);
                List<DiscountDO> needInsertToDicountDos = AmountCalculationUtil.addNeedInsertDiscountDOS(new
                                LinkedList<>(guidLists), discountDOS, discountRuleBO,
                        orderDetailRespDTO.getGuid());
                discountDOS.addAll(needInsertToDicountDos);
            }
            AmountCalculationUtil.getDiscountDOS(discountDOS, discountRuleBO);
        }
        if (!CollectionUtil.isEmpty(discountDOS)) {
            List<DiscountDO> discountDOList = discountDOS.stream()
                    .filter(d -> discountDOGuidList.contains(d.getGuid()) ||
                            BigDecimalUtil.greaterThanZero(d.getDiscountFee()) ||
                            BigDecimalUtil.greaterThanZero(d.getDiscount()))
                    .collect(Collectors.toList());
            discountService.saveOrUpdateBatch(discountDOList);
        }
        return discountDOS;
    }

    private DineinOrderDetailRespDTO checkVolume(DineinOrderDetailRespDTO orderDetailRespDTO, OrderDO orderDO,
                                                 BillCalculateReqDTO billCalculateReqDTO) {
        //是否并单
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetailRespDTO.getSubOrderDetails();
        boolean combine = CollectionUtil.isNotEmpty(subOrderDetails);

        //并单把所有菜放在一起算
        List<DineInItemDTO> allItems = new ArrayList<>();
        allItems.addAll(orderDetailRespDTO.getDineInItemDTOS());
        if (combine) {
            for (DineinOrderDetailRespDTO subOrderDetail : subOrderDetails) {
                allItems.addAll(subOrderDetail.getDineInItemDTOS());
            }
        }
        AmountCalculationUtil.getFreeDiscountFee(allItems);
        List<RequestDishInfo> dishInfoDTOS = CommonUtil.dineInItem2DishList(allItems);

        if (StringUtils.isNotEmpty(billCalculateReqDTO.getVolumeCode())) {
            //用最新memberConsumptionGuid进行验券和撤销
            RequestVolumeCalculate volumeCalculateReq = new RequestVolumeCalculate();
            volumeCalculateReq.setVolumeCode(billCalculateReqDTO.getVolumeCode());
            volumeCalculateReq.setStoreGuid(UserContextUtils.getStoreGuid());
            volumeCalculateReq.setDishInfoDTOList(dishInfoDTOS);
            volumeCalculateReq.setHasMemberPrice(Integer.valueOf(1).equals(orderDO.getCalculateByMemberPrice()));
            //走查询的接口，但是不验券
            ResponseVolumeCalculate query =
                    memberTerminalClientService.calculate(billCalculateReqDTO.getVolumeCode(), volumeCalculateReq);
            //将错误原因放入返回值中，供微信使用
            orderDetailRespDTO.setTip(query.getTip());
            orderDO.setMemberConsumptionGuid(query.getMemberConsumptionGuid());
        }
        return orderDetailRespDTO;
    }

    /**
     * 登陆登出逻辑保持原来不变，调用换成新会员的
     * 已经去掉获取会员折扣的代码
     *
     * @param orderDO
     * @param billCalculateReqDTO
     * @param orderDetailRespDTO
     * @return
     */
    @Override
    public DiscountRuleBO getDiscountRuleBO(OrderDO orderDO, BillCalculateReqDTO billCalculateReqDTO, DineinOrderDetailRespDTO orderDetailRespDTO) {
        DiscountRuleBO discountRuleBO = orderTransform.billCalculateReqDTO2DiscountRuleBO(billCalculateReqDTO);
        String memberCardGuid = billCalculateReqDTO.getMemberInfoCardGuid() != null ? billCalculateReqDTO
                .getMemberInfoCardGuid() : orderDO.getMemberCardGuid();
        // 处理会员
        // 订单主卡会员 切换多卡
        orderMultiMemberHandler(billCalculateReqDTO, orderDO);
        log.info("查询会员卡MemberGuid={}.", orderDO.getMemberGuid());
        if (StringUtils.isNotEmpty(orderDO.getMemberGuid()) && !orderDO.getMemberGuid().equals("0")) {
            RequestQueryStoreAndMemberAndCard memberLoginDTO = new RequestQueryStoreAndMemberAndCard();
            String openIdOrPhone = orderDO.getMemberPhone();
            if (StringUtils.isEmpty(openIdOrPhone)) {
                openIdOrPhone = orderDO.getUserWxPublicOpenId();
            }
            log.info("orderGuid={},openIdOrPhone={}.", orderDO.getGuid(), openIdOrPhone);
            memberLoginDTO.setPhoneNumOrCardNum(openIdOrPhone);
            memberLoginDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            memberLoginDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            //只查询当前门店可用的卡
            memberLoginDTO.setIsCurrentStoreCard(1);
            boolean isMember = true;
            try {
                ResponseMemberAndCardInfoDTO memberInfoAndCard = memberTerminalClientService.getMemberInfoAndCard(memberLoginDTO);
                if (memberInfoAndCard.getMemberInfoDTO().getStateCode() == 0) {
                    isMember = false;
                } else {
                    if (!CollectionUtil.isEmpty(memberInfoAndCard.getMemberCardListRespDTOs())) {
                        discountRuleBO.setCanCaculatByMemberPrice(memberTerminalClientService.hasMemberPrice(memberCardGuid));
                    }
                    orderDetailRespDTO.setMemberInfoAndCard(memberInfoAndCard);
                    orderDetailRespDTO.setMemberGuid(orderDO.getMemberGuid());
                    orderDetailRespDTO.setMemberCardGuid(orderDO.getMemberCardGuid());
                }
            } catch (HystrixBadRequestException ex) {
                if (ex.getMessage().contains("未注册")) {
                    isMember = false;
                    log.warn("会员未注册 memberPhone={},wxOpenId {} ",
                            orderDO.getMemberPhone(),
                            orderDO.getUserWxPublicOpenId(),
                            ex);
                } else {
                    log.error("会员接口getMemberInfoAndCardTwo出错", ex);
                }
            }
            //如果非会员就移除会员信息
            if (!isMember) {
                billCalculateReqDTO.setMemberLogin(2);
                billCalculateReqDTO.setMemberPhone(null);
            }
        }
        if (StringUtils.isNotEmpty(billCalculateReqDTO.getMemberPhone())) {
            RequestQueryStoreAndMemberAndCard memberLoginDTO = new RequestQueryStoreAndMemberAndCard();
            memberLoginDTO.setPhoneNumOrCardNum(billCalculateReqDTO.getMemberPhone());
            memberLoginDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            memberLoginDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            memberLoginDTO.setIsCurrentStoreCard(BooleanEnum.TRUE.getCode());
            ResponseMemberAndCardInfoDTO memberInfoAndCard = memberTerminalClientService.getMemberInfoAndCard(memberLoginDTO);
            if (memberInfoAndCard.getMemberInfoDTO().getStateCode() == 0) {
                throw new ParameterException("会员不可用");
            } else {
                orderDetailRespDTO.setMemberInfoAndCard(memberInfoAndCard);
                if (billCalculateReqDTO.getMemberLogin() == 1) {
                    orderDO.setMemberGuid(memberInfoAndCard.getMemberInfoDTO().getMemberInfoGuid());
                    orderDO.setMemberName(memberInfoAndCard.getMemberInfoDTO().getNickName());
                    String memberPhoneNumber = memberInfoAndCard.getMemberInfoDTO().getPhoneNum();
                    log.info("memberPhoneNumber={}", memberPhoneNumber);
                    if (StringUtils.isEmpty(memberPhoneNumber)) {
                        memberPhoneNumber = billCalculateReqDTO.getMemberPhone();
                    }
                    orderDO.setMemberPhone(memberPhoneNumber);
                    orderDO.setMemberCardGuid(billCalculateReqDTO.getMemberInfoCardGuid());
                    orderDetailRespDTO.setMemberGuid(memberInfoAndCard.getMemberInfoDTO().getMemberInfoGuid());
                    orderDetailRespDTO.setMemberCardGuid(billCalculateReqDTO.getMemberInfoCardGuid());

                    orderService.updateById(orderDO);
                    if (!StringUtils.isEmpty(memberPhoneNumber)) {
                        padOrderService.updatePhone(orderDO.getGuid(), memberPhoneNumber);
                    }
                }
            }
        }
        // 会员登录标记处理
        memberLoginHandler(billCalculateReqDTO, orderDetailRespDTO, orderDO, discountRuleBO);
        //系统省零规则
        discountRuleBO.setSystemDiscountDTOS(setSystemDiscountList(billCalculateReqDTO));
        discountRuleBO.setActivityGuid(billCalculateReqDTO.getActivityGuid());
        return discountRuleBO;
    }


    /**
     * 会员登录标记处理
     */
    private void memberLoginHandler(BillCalculateReqDTO billCalculateReqDTO, DineinOrderDetailRespDTO orderDetailRespDTO,
                                    OrderDO orderDO, DiscountRuleBO discountRuleBO) {
        String memberCardGuid = billCalculateReqDTO.getMemberInfoCardGuid() != null ? billCalculateReqDTO
                .getMemberInfoCardGuid() : orderDO.getMemberCardGuid();
        if (billCalculateReqDTO.getMemberLogin() != 2 && StringUtils.isNotBlank(memberCardGuid) && !memberCardGuid.equals("0")) {
            if (Objects.nonNull(billCalculateReqDTO.getUseMemberDiscountFlag()) && Boolean.FALSE.equals(billCalculateReqDTO.getUseMemberDiscountFlag())) {
                discountRuleBO.setCanCaculatByMemberPrice(false);
                return;
            }
            try {
                discountRuleBO.setCanCaculatByMemberPrice(memberTerminalClientService.hasMemberPrice(memberCardGuid));
            } catch (HystrixBadRequestException ex) {
                if (ex.getMessage().contains("卡不存在")) {
                    billCalculateReqDTO.setMemberLogin(2);
                }
            }
        }
        //会员登出
        if (billCalculateReqDTO.getMemberLogin() == 2) {
            removeMember(orderDO, orderDetailRespDTO);
            discountRuleBO.setCanCaculatByMemberPrice(false);
            //规则清除标志
            discountRuleBO.setMemberIntegral(3);
        }
    }

    /**
     * 设置系统省零规则
     */
    private List<SystemDiscountDTO> setSystemDiscountList(BillCalculateReqDTO billCalculateReqDTO) {
        Boolean isWholeDiscount = billCalculateReqDTO.getIsWholeDiscount();
        if (Objects.isNull(isWholeDiscount) || Boolean.TRUE.equals(isWholeDiscount)) {
            return businessClientService.getSystemDiscount(UserContextUtils.getStoreGuid());
        }
        return Lists.newArrayList();
    }

    private void removeMember(OrderDO orderDO, DineinOrderDetailRespDTO orderDetailRespDTO) {
        orderDO.setMemberGuid("0");
        orderDO.setMemberName(StringUtils.EMPTY);
        orderDO.setMemberPhone(StringUtils.EMPTY);
        orderDO.setCalculateByMemberPrice(0);
        orderDO.setMemberCardGuid("0");
        orderDetailRespDTO.setSingleItemUsedMemeberPrice(0);
        orderDetailRespDTO.setMemberGuid("0");
        orderDetailRespDTO.setMemberCardGuid("0");
        orderDetailRespDTO.setMemberInfoAndCard(null);
        orderService.updateById(orderDO);
        //会员登出时调用下会员的接口
        log.info("delDiscount:.memberConsumptionGuid={}", orderDO.getMemberConsumptionGuid());
        if (!"0".equals(orderDO.getMemberConsumptionGuid())) {
            memberTerminalClientService.delDiscount(orderDO.getMemberConsumptionGuid());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDiscountInfo(CreateFastFoodReqDTO createFastFoodReqDTO) {
        // 记录折扣信息
        List<DiscountFeeDetailDTO> discountFeeDetailDTOList = createFastFoodReqDTO.getDiscountFeeDetailDTOS();
        String orderGuid = createFastFoodReqDTO.getGuid();
        List<DiscountDO> discountList = discountService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(discountList)) {
            // 初始化
            List<Long> guids = dynamicHelper.generateGuids(GuidKeyConstant.HST_DISCOUNT, DiscountTypeEnum.values().length);
            discountList = AmountCalculationUtil.getInitDiscountDOList(guids, orderGuid);
            // 折扣信息处理
            setDiscountFeeDetail(discountFeeDetailDTOList, discountList);
            discountList.removeIf(d -> !BigDecimalUtil.greaterThanZero(d.getDiscountFee()) &&
                    !BigDecimalUtil.greaterThanZero(d.getDiscount()));
            log.info("新增折扣信息,orderGuid:{}, discountList：{}", orderGuid, JacksonUtils.writeValueAsString(discountList));
            discountService.saveBatch(discountList);
        } else {
            // 折扣信息处理
            setDiscountFeeDetail(discountFeeDetailDTOList, discountList);
            log.info("编辑折扣信息,orderGuid:{}, discountList：{}", orderGuid, JacksonUtils.writeValueAsString(discountList));
            discountService.updateBatchById(discountList);
        }
        // 记录附加费
        appendFeeService.persistAppendFee(orderGuid);
    }

    /**
     * 折扣信息处理
     *
     * @param discountFeeDetailDTOList 传入的折扣信息
     * @param discountList             数据库的折扣信息
     */
    private void setDiscountFeeDetail(List<DiscountFeeDetailDTO> discountFeeDetailDTOList,
                                      List<DiscountDO> discountList) {
        if (!CollectionUtils.isEmpty(discountFeeDetailDTOList)) {
            Map<Integer, DiscountDO> discountDOMap = discountList.stream()
                    .collect(Collectors.toMap(DiscountDO::getDiscountType, Function.identity(), (v1, v2) -> v1));
            discountFeeDetailDTOList.forEach(discountFeeDetail -> {
                DiscountDO discountDO = discountDOMap.get(discountFeeDetail.getDiscountType());
                discountDO.setDiscount(discountFeeDetail.getDiscount());
                discountDO.setRule(discountFeeDetail.getRule());
                discountDO.setDiscountFee(discountFeeDetail.getDiscountFee());
                discountDO.setCouponsGuid(discountFeeDetail.getActivityGuid());
            });
            discountList.removeIf(d -> !BigDecimalUtil.greaterThanZero(d.getDiscountFee()));
        }
    }

    /**
     * 订单多卡信息
     */
    private void setOrderMultiMemberInfo(DineinOrderDetailRespDTO orderDetailRespDTO) {
        orderDetailRespDTO.setOrderMultiMembers(Lists.newArrayList());
        // 查询订单多卡支付信息
        List<OrderMultiMember> orderMultiMembers = orderMultiMemberService.listByOrderGuid(Long.valueOf(orderDetailRespDTO.getGuid()));
        log.info("查询订单多卡支付信息:{}", JacksonUtils.writeValueAsString(orderMultiMembers));
        if (CollectionUtils.isEmpty(orderMultiMembers)) {
            return;
        }
        List<OrderMultiMemberDTO> orderMultiMemberList = OrderTransform.INSTANCE.orderMultiMember2OrderMultiMemberDTO(orderMultiMembers);
        // 批量查询多卡会员信息
        queryBatchMemberCardInfo(orderMultiMemberList);
        orderDetailRespDTO.setOrderMultiMembers(orderMultiMemberList);
    }

    /**
     * 订单主卡会员 切换多卡
     */
    private void orderMultiMemberHandler(BillCalculateReqDTO billCalculateReqDTO, OrderDO orderDO) {
        if (Objects.equals(MemberLoginEnum.PHONE_NUMBER_NOT_REGISTERED.getType(), billCalculateReqDTO.getMemberLogin())) {
            // 不操作会员
            return;
        }
        // 查询多卡会员
        List<OrderMultiMember> orderMultiMembers = orderMultiMemberService.listByOrderGuid(orderDO.getGuid());
        if (Objects.equals(MemberLoginEnum.LOGIN.getType(), billCalculateReqDTO.getMemberLogin()) && !CollectionUtils.isEmpty(orderMultiMembers)) {
            // 切换卡
            OrderMultiMember orderMainMultiMember = orderMultiMembers.stream()
                    .min(Comparator.comparing(OrderMultiMember::getGmtCreate))
                    .orElse(null);
            if (!orderMainMultiMember.getMemberCardGuid().equals(billCalculateReqDTO.getMemberInfoCardGuid())) {
                // 不一样 则修改多卡表中的主卡数据
                orderMainMultiMember.setMemberCardGuid(billCalculateReqDTO.getMemberInfoCardGuid());
                orderMainMultiMember.setMemberCardNum(billCalculateReqDTO.getMemberCardNum());
                orderMultiMemberService.updateById(orderMainMultiMember);
            }
            return;
        }
        // 登出
        if (!Objects.equals(MemberLoginEnum.LOGOUT.getType(), billCalculateReqDTO.getMemberLogin())) {
            return;
        }
        // 订单上会员退出登录
        // 查询多卡会员
        if (CollectionUtils.isEmpty(orderMultiMembers)) {
            // 没有可自动切换的会员
            return;
        }
        // 删除订单上的卡
        String mainMemberCardGuid = orderDO.getMemberCardGuid();
        OrderMultiMember orderMultiMember = orderMultiMembers.stream()
                .filter(e -> !Objects.equals(e.getMemberCardGuid(), mainMemberCardGuid))
                .min(Comparator.comparing(OrderMultiMember::getGmtCreate))
                .orElse(null);
        orderMultiMemberService.removeByOrderGuidAndMemberCardGuid(orderDO.getGuid(), mainMemberCardGuid);
        if (Objects.isNull(orderMultiMember)) {
            return;
        }
        // 有其他的卡
        orderDO.setMemberGuid(null);
        billCalculateReqDTO.setMemberLogin(MemberLoginEnum.LOGIN.getType());
        billCalculateReqDTO.setLoginType(orderMultiMember.getLoginType());
        billCalculateReqDTO.setMemberPhone(StringUtils.isEmpty(orderMultiMember.getMemberPhone())
                ? orderMultiMember.getMemberCardNum() : orderMultiMember.getMemberPhone());
        billCalculateReqDTO.setMemberInfoCardGuid(orderMultiMember.getMemberCardGuid());
        billCalculateReqDTO.setMemberCardNum(orderMultiMember.getMemberCardNum());
    }

    @Override
    public void relationOrderMultiMember(OrderMultiMemberDTO reqDTO) {
        // 查询订单
        OrderDO orderDO = orderService.getByIdWithCache(reqDTO.getOrderGuid());
        if (Objects.isNull(orderDO)) {
            throw new BusinessException("订单不存在,请刷新后重试");
        }
        if (orderDO.getState().equals(StateEnum.SUCCESS.getCode())) {
            throw new BusinessException("订单已结账,请刷新后重试");
        }
        Integer memberLogin = reqDTO.getMemberLogin();
        if (Objects.equals(MemberLoginEnum.LOGIN.getType(), memberLogin)) {
            // 登录
            List<OrderMultiMember> orderMultiMembers = orderMultiMemberService.listByOrderGuid(orderDO.getGuid());
            if (CollectionUtils.isEmpty(orderMultiMembers)) {
                OrderMultiMember orderMainMember = buildMainOrderMultiMember(orderDO);
                if (Objects.nonNull(orderMainMember)) {
                    orderMultiMemberService.save(orderMainMember);
                }
            }
            OrderMultiMember orderMultiMemberDO = OrderTransform.INSTANCE.orderMultiMemberDTO2OrderMultiMember(reqDTO);
            orderMultiMemberDO.setGuid(dynamicHelper.generateGuid(OrderMultiMember.class.getSimpleName()));
            orderMultiMemberService.save(orderMultiMemberDO);
        } else {
            // 登出
            orderMultiMemberService.removeByOrderGuidAndMemberCardGuid(orderDO.getGuid(),
                    reqDTO.getMemberInfoCardGuid());
        }
    }

    @Override
    public BillMemberCardCalculateRespDTO relationOrderLudouMember(BilMemberCardCalculateReqDTO cardCalculateReqDTO) {
        String orderGuid = cardCalculateReqDTO.getOrderGuid();
        OrderDO orderDO = orderService.getById(orderGuid);
        if (Objects.isNull(orderDO)) {
            throw new BusinessException("订单不存在，请刷新后重试");
        }
        if (StringUtils.isEmpty(cardCalculateReqDTO.getPhoneNumOrCardNum())) {
            if (StringUtils.isEmpty(orderDO.getMemberPhone()) || orderDO.getMemberPhone().length() > 11) {
                log.warn("未登录任何会员信息, 直接返回空, orderDO:{}", JacksonUtils.writeValueAsString(orderDO));
                return null;
            }
            // 如果没有传入手机号，则使用订单上的会员信息
            cardCalculateReqDTO.setPhoneNumOrCardNum(orderDO.getMemberPhone());
        }
        // 查询会员信息
        LudouMemberDTO ludouMemberDTO = ludouMemberExternalService.query(cardCalculateReqDTO.getPhoneNumOrCardNum(), null);
        if (Objects.isNull(ludouMemberDTO)) {
            log.warn("未查询到麓豆会员信息, phoneNum:{}", cardCalculateReqDTO.getPhoneNumOrCardNum());
            return null;
        }
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderGuid);
        if (Objects.isNull(orderExtendsDO)) {
            orderExtendsDO = new OrderExtendsDO();
            BeanUtils.copyProperties(orderDO, orderExtendsDO);
        }
        orderExtendsDO.setLudouMemberGuid(ludouMemberDTO.getUserId());
        orderExtendsDO.setLudouMemberName(ludouMemberDTO.getNickName());
        orderExtendsDO.setLudouMemberPhone(ludouMemberDTO.getPhone());
        // 更新订单
        orderExtendsService.saveOrUpdate(orderExtendsDO);
        // 返回结果
        BillMemberCardCalculateRespDTO billMemberCardCalculateRespDTO = new BillMemberCardCalculateRespDTO();
        billMemberCardCalculateRespDTO.setLudouMemberGuid(ludouMemberDTO.getUserId());
        billMemberCardCalculateRespDTO.setLudouMemberName(ludouMemberDTO.getNickName());
        billMemberCardCalculateRespDTO.setLudouMemberPhone(ludouMemberDTO.getPhone());
        billMemberCardCalculateRespDTO.setLudouMemberBalance(ludouMemberDTO.getUsableAmount());
        return billMemberCardCalculateRespDTO;
    }

    @Override
    public void removeOrderLudouMember(BilMemberCardCalculateReqDTO cardCalculateReqDTO) {
        String orderGuid = cardCalculateReqDTO.getOrderGuid();
        OrderDO orderDO = orderService.getById(orderGuid);
        if (Objects.isNull(orderDO)) {
            throw new BusinessException("订单不存在，请刷新后重试");
        }
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderGuid);
        if (Objects.isNull(orderExtendsDO)) {
            return;
        }
        orderExtendsDO.setLudouMemberGuid(Strings.EMPTY);
        orderExtendsDO.setLudouMemberName(Strings.EMPTY);
        orderExtendsDO.setLudouMemberPhone(Strings.EMPTY);
        // 更新订单
        orderExtendsService.updateById(orderExtendsDO);
    }

    @Override
    public List<OrderMultiMemberDTO> queryRelationOrderMultiMember(String orderGuid) {
        List<OrderMultiMember> orderMultiMembers = orderMultiMemberService.listByOrderGuid(Long.valueOf(orderGuid));
        List<OrderMultiMemberDTO> orderMultiMemberDTOList = OrderTransform.INSTANCE.orderMultiMember2OrderMultiMemberDTO(orderMultiMembers);
        if (CollectionUtil.isEmpty(orderMultiMemberDTOList)) {
            return Lists.newArrayList();
        }
        // 批量查询多卡会员信息
        queryBatchMemberCardInfo(orderMultiMemberDTOList);
        return orderMultiMemberDTOList;
    }

    /**
     * 批量查询多卡会员信息
     */
    private void queryBatchMemberCardInfo(List<OrderMultiMemberDTO> orderMultiMemberList) {
        List<String> memberCardGuids = orderMultiMemberList.stream()
                .map(OrderMultiMemberDTO::getMemberInfoCardGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(memberCardGuids)) {
            return;
        }
        RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCard = new RequestQueryStoreAndMemberAndCard();
        queryStoreAndMemberAndCard.setMemberCardGuids(memberCardGuids);
        log.info("批量查询多卡会员信息入参：{}", JacksonUtils.writeValueAsString(queryStoreAndMemberAndCard));
        List<ResponseMemberCard> memberCardList = memberTerminalClientService.queryBatchMemberCardInfo(queryStoreAndMemberAndCard);
        log.info("批量查询多卡会员信息返回：{}", JacksonUtils.writeValueAsString(memberCardList));
        Map<String, ResponseMemberCard> memberCardMap = memberCardList.stream()
                .collect(Collectors.toMap(ResponseMemberCard::getMemberInfoCardGuid, Function.identity(), (key1, key2) -> key1));
        orderMultiMemberList.forEach(multiMember -> {
            ResponseMemberCard responseMemberCard = memberCardMap.get(multiMember.getMemberInfoCardGuid());
            multiMember.setCardMoney(BigDecimal.ZERO);
            multiMember.setCardIntegral(0);
            if (Objects.nonNull(responseMemberCard)) {
                multiMember.setFreezeMoney(responseMemberCard.getFreezeMoney());
                multiMember.setCardMoney(responseMemberCard.getCardMoney());
                multiMember.setCardIntegral(responseMemberCard.getCardIntegral());
            }
        });
    }

    /**
     * 构建订单关联会员
     */
    private OrderMultiMember buildMainOrderMultiMember(OrderDO orderDO) {
        if (!CommonUtil.hasGuid(orderDO.getMemberGuid()) && !CommonUtil.hasGuid(orderDO.getMemberPhone())) {
            return null;
        }
        OrderMultiMember orderMultiMemberDO = new OrderMultiMember();
        orderMultiMemberDO.setOrderGuid(orderDO.getGuid());
        String loginTypeKey = LOGIN_TYPE + orderDO.getGuid();
        String loginTypeStr = redisHelper.get(loginTypeKey);
        String loginType = Optional.ofNullable(loginTypeStr).orElse(String.valueOf(MemberLoginTypeEnum.PHONE.getType()));
        orderMultiMemberDO.setLoginType(Integer.valueOf(loginType));
        orderMultiMemberDO.setMemberGuid(orderDO.getMemberGuid());
        orderMultiMemberDO.setMemberName(orderDO.getMemberName());
        orderMultiMemberDO.setMemberPhone(orderDO.getMemberPhone());
        orderMultiMemberDO.setMemberCardGuid(orderDO.getMemberCardGuid());
        RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCard = new RequestQueryStoreAndMemberAndCard();
        queryStoreAndMemberAndCard.setMemberCardGuids(Lists.newArrayList(orderDO.getMemberCardGuid()));
        log.info("查询订单主卡会员信息入参：{}", JacksonUtils.writeValueAsString(queryStoreAndMemberAndCard));
        List<ResponseMemberCard> memberCardList = memberTerminalClientService.queryBatchMemberCardInfo(queryStoreAndMemberAndCard);
        log.info("查询订单主卡会员信息返回：{}", JacksonUtils.writeValueAsString(memberCardList));
        if (!CollectionUtils.isEmpty(memberCardList)) {
            orderMultiMemberDO.setMemberCardNum(memberCardList.get(0).getSystemManagementCardNum());
        }
        orderMultiMemberDO.setGuid(dynamicHelper.generateGuid(OrderMultiMember.class.getSimpleName()));
        return orderMultiMemberDO;
    }

    /**
     * 查询订单扩展信息
     */
    private void queryOrderExtendsInfo(DineinOrderDetailRespDTO dineinOrderDetailResp) {
        // 如果是正餐
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(dineinOrderDetailResp.getGuid());
        if (Objects.isNull(orderExtendsDO)) {
            return;
        }
        if (Objects.equals(dineinOrderDetailResp.getTradeMode(), com.holderzone.saas.store.enums.order.TradeModeEnum.DINEIN.getCode())) {
            dineinOrderDetailResp.setIsMultipleAggPay(orderExtendsDO.getIsMultipleAggPay());
            dineinOrderDetailResp.setAssociatedFlag(orderExtendsDO.getAssociatedFlag());
            if (Boolean.TRUE.equals(orderExtendsDO.getAssociatedFlag())) {
                dineinOrderDetailResp.setAssociatedSn(orderExtendsDO.getAssociatedSn());
                dineinOrderDetailResp.setAssociatedTableGuids(Objects.nonNull(orderExtendsDO.getAssociatedTableGuids()) ?
                        JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableGuids()) : com.beust.jcommander.internal.Lists.newArrayList());
                dineinOrderDetailResp.setAssociatedTableNames(Objects.nonNull(orderExtendsDO.getAssociatedTableNames()) ?
                        JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableNames()) : com.beust.jcommander.internal.Lists.newArrayList());
            }
        }
        dineinOrderDetailResp.setLudouMemberGuid(orderExtendsDO.getLudouMemberGuid());
        dineinOrderDetailResp.setLudouMemberName(orderExtendsDO.getLudouMemberName());
        dineinOrderDetailResp.setLudouMemberPhone(orderExtendsDO.getLudouMemberPhone());
    }
}
