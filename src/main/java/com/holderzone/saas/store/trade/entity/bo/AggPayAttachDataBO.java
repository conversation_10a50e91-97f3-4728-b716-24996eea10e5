package com.holderzone.saas.store.trade.entity.bo;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayAttachDataBO
 * @date 2019/03/21 10:43
 * @description
 * @program holder-saas-store-trade
 */
@Data
public class AggPayAttachDataBO extends BaseDTO {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "本次支付唯一标示", required = true)
    private String payGuid;

    @ApiModelProperty(value = "支付金额", required = true)
    private BigDecimal amount;

    /**
     * 注意：
     * 目前看此字段意思为是否组合支付, 跟之前的注释有区别，并非标识最后一次支付
     * 当组合支付时，安卓端传参为 false
     */
    @ApiModelProperty(value = "是否最后一次支付", required = true)
    private Boolean last;

    @ApiModelProperty(value = "是否最后结账")
    private Boolean checkoutSuccessFlag;
}
