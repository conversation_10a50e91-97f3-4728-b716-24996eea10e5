package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import com.holderzone.saas.store.trade.anno.LocalizeRequireNotifyOrderGuids;
import com.holderzone.saas.store.trade.anno.RequireOrderCheckLock;
import com.holderzone.saas.store.trade.service.CombineOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CombineOrderController
 * @date 2019/01/04 8:53
 * @description 并拆单接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/dine_in_order")
@Api(description = "并单接口")
@Slf4j
public class CombineOrderController {

    private final CombineOrderService combineOrderService;

    @Autowired
    public CombineOrderController(CombineOrderService combineOrderService) {
        this.combineOrderService = combineOrderService;
    }

    @ApiOperation(value = "并单", notes = "并单")
    @PostMapping("/combine")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean combine(@RequestBody TableOrderCombineDTO tableOrderCombineDTO) {
        log.info("并单入参：{}", JacksonUtils.writeValueAsString(tableOrderCombineDTO));
        return combineOrderService.combine(tableOrderCombineDTO);

    }

    @ApiOperation(value = "拆单", notes = "拆单")
    @PostMapping("/split")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean split(@RequestBody TableOrderCombineDTO tableOrderCombineDTO) {
        log.info("拆单入参：{}", JacksonUtils.writeValueAsString(tableOrderCombineDTO));
        return combineOrderService.split(tableOrderCombineDTO);

    }

    @ApiOperation(value = "转台", notes = "转台")
    @PostMapping("/transform")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean transform(@RequestBody TradeTableDTO tradeTableDTO) {
        log.info("转台入参：{}", JacksonUtils.writeValueAsString(tradeTableDTO));
        return combineOrderService.transform(tradeTableDTO);

    }

}
