package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.saas.store.dto.trade.MchntMappingDTO;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ResourceClientService
 * @date 2018/08/14 14:12
 * @description //
 * @program holder-saas-store-trading-center
 */
@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = ResourceClientService.ResourceClientServiceFallBack.class)
public interface ResourceClientService {

    @PostMapping("/getMchntMapping/{enterpriseGuid}")
    MchntMappingDTO getMchntMappingDtoByGuid(@PathVariable("enterpriseGuid") String enterpriseGuid);


    @GetMapping("/enterprise/app/{enterpriseGuid}/{storeGuid}")
    PaymentInfoDTO getApp(@PathVariable("enterpriseGuid") String enterpriseGuid, @PathVariable("storeGuid") String
            storeGuid);

    @GetMapping("/multi/member/list")
    List<MultiMemberDTO> list(@RequestBody Set<String> multiMemberGuid);

    @Component
    class ResourceClientServiceFallBack implements FallbackFactory<ResourceClientService> {

        private static final Logger logger = LoggerFactory.getLogger(ResourceClientServiceFallBack.class);

        @Override
        public ResourceClientService create(Throwable throwable) {

            return new ResourceClientService() {
                @Override
                public MchntMappingDTO getMchntMappingDtoByGuid(String enterpriseGuid) {
                    logger.error("调用资源中心失败 enterpriseGuid={}", enterpriseGuid);
                    return null;
                }

                @Override
                public PaymentInfoDTO getApp(String enterpriseGuid, String storeGuid) {
                    logger.error("调用资源中心失败 enterpriseGuid={}，storeGuid={}", enterpriseGuid, storeGuid);
                    return null;
                }

                @Override
                public List<MultiMemberDTO> list(Set<String> multiMemberGuid) {
                    logger.error("批量查询运营主体信息失败，msg={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }

}
