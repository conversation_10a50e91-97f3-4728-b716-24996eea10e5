package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.OrderItemRecordDO;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 订单商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface OrderItemRecordService extends IService<OrderItemRecordDO> {

    List<OrderItemRecordDO> listByOrderGuid(Long orderGuid);

    /**
     * 从缓存中拿订单，缓存没有再查数据库
     *
     * @param orderGuid
     * @return
     */
    List<OrderItemRecordDO> listByOrderGuidWithCache(Long orderGuid);

    boolean saveBatchWithDeleteCache(Collection<OrderItemRecordDO> entityList);

    boolean saveBatchWithDeleteCache(OrderItemRecordDO orderItemRecordDO);

    boolean updateBatchByIdWithDeleteCache(Collection<OrderItemRecordDO> entityList, String orderGuid);

    boolean removeByIdsWithDeleteCache(Collection<? extends Serializable> idList, String orderGuid);

    List<OrderItemRecordDO> listByOrderGuidWithOutIsPay(Long orderGuid);

    List<OrderItemRecordDO> listByOrderGuidWithOutSub(Long orderGuid);

    Boolean hasCurrentItem(Long orderGuid);

    List<OrderItemRecordDO> listByMainItemGuid(Long itemGuid);

    List<OrderItemRecordDO> listByMainItemGuids(Collection<? extends Serializable> idList);

    void deleteByOrderGuid(String orderGuid);

    void deleteByOrderGuids(List<String> orderGuids);

    void deleteByItemGuids(List<String> itemGuids);

    List<OrderItemRecordDO> listByIdsWithLock(Collection<? extends Serializable> idList);

    List<OrderItemRecordDO> listByOrderLists(List<String> orderLists);

    List<OrderItemRecordDO> listByItemGuids(Collection<? extends Serializable> idList);
}
