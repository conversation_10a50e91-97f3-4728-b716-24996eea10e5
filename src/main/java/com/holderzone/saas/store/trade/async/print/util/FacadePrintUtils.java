package com.holderzone.saas.store.trade.async.print.util;

import cn.hutool.core.util.DesensitizedUtil;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.kds.req.KdsItemDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsAttrGroupDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.RefundOrderDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.RefundOrderPrintDTO;
import com.holderzone.saas.store.dto.order.response.dinein.RefundOrderRecordDTO;
import com.holderzone.saas.store.dto.organization.StoreBizDTO;
import com.holderzone.saas.store.dto.print.content.*;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.trade.req.TransferItemDetailsDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import com.holderzone.saas.store.trade.builder.TransferItemBizBuilder;
import com.holderzone.saas.store.trade.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * {@link FacadePrintUtils}
 * Facade Pattern
 * 外观模式 将PrintUtils中的方法代理出来  {@link PrintUtils} 不暴露外层使用
 * {@code PrintUtils.*} 类似protect
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/13 14:05
 */
@Slf4j
public class FacadePrintUtils {

    private PrintUtils printUtils;

    public FacadePrintUtils() {
        this.printUtils = new PrintUtils();
    }

    /**
     * 设置基本信息
     */
    public void setPrintBaseInfo(PrintDTO printDTO, BaseDTO baseDTO,
                                 InvoiceTypeEnum invoiceTypeEnum, DineinOrderDetailRespDTO
                                         dineinOrderDetailRespDTO, Function<String, String> areaGuidFromTableGuidFunction) {
        if (dineinOrderDetailRespDTO.getTradeMode().equals(TradeModeEnum.DINE.getMode())
                && !StringUtils.isEmpty(dineinOrderDetailRespDTO.getDiningTableGuid())) {
            printDTO.setAreaGuid(areaGuidFromTableGuidFunction.apply(dineinOrderDetailRespDTO.getDiningTableGuid()));
            printDTO.setTableGuid(dineinOrderDetailRespDTO.getDiningTableGuid());
        }
        String orderNo = "";
        if (Objects.nonNull(dineinOrderDetailRespDTO.getOrderNo())) {
            orderNo = dineinOrderDetailRespDTO.getOrderNo();
        }
        printDTO.setInvoiceType(invoiceTypeEnum.getType())
                .setStoreGuid(baseDTO.getStoreGuid())
                .setEnterpriseGuid(baseDTO.getEnterpriseGuid())
                .setPrintUid(orderNo)
                .setOperatorStaffGuid(baseDTO.getUserGuid())
                .setOperatorStaffName(baseDTO.getUserName())
                .setCreateTime(DateTimeUtils.nowMillis())
                .setDeviceId(baseDTO.getDeviceId())
                .setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(baseDTO.getDeviceType()));
    }

    /**
     * 设置打印体
     */
    public void setPrintBody(PrintItemDetailDTO printItemDetailDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO,
                             Function<String, StoreBizDTO> storeInfoFunction, boolean isMerge) {
        printItemDetailDTO.setTotal(printUtils.extractItemPriceTotal(dineinOrderDetailRespDTO));
        printItemDetailDTO.setItemRecordList(printUtils.extractItemRecords(dineinOrderDetailRespDTO, false, isMerge));
        printItemDetailDTO.setDiscountAfterTotal(getDiscountAfterTotal(printItemDetailDTO.getItemRecordList()));
        printItemDetailDTO.setStoreName(UserContextUtils.getStoreName());
        String markNo = getMarkNo(dineinOrderDetailRespDTO);
        printItemDetailDTO.setMarkNo(markNo);
        printItemDetailDTO.setOrderNo(dineinOrderDetailRespDTO.getOrderNo());
        printItemDetailDTO.setPersonNumber(dineinOrderDetailRespDTO.getGuestCount());
        if (Objects.nonNull(dineinOrderDetailRespDTO.getGmtCreate())) {
            printItemDetailDTO.setOpenTableTime(DateTimeUtils.localDateTime2Mills(dineinOrderDetailRespDTO.getGmtCreate()));
        }
        printItemDetailDTO.setOrderRemark(dineinOrderDetailRespDTO.getRemark());
        printItemDetailDTO.setMemberPortrayalDTO(dineinOrderDetailRespDTO.getMemberPortrayalDTO());
        printItemDetailDTO.setAdditionalChargeList(printUtils.getAppendFee(dineinOrderDetailRespDTO));
        printItemDetailDTO.setTradeMode(dineinOrderDetailRespDTO.getTradeMode());
        if (!(printItemDetailDTO instanceof PrintPreCheckoutDTO)) {
            return;
        }
        //上层强转
        PrintPreCheckoutDTO printPreCheckoutDTO = (PrintPreCheckoutDTO) printItemDetailDTO;
        printPreCheckoutDTO.setReduceRecordList(printUtils.extractReduceRecordList(dineinOrderDetailRespDTO));
        // 应付金额
        printPreCheckoutDTO.setPayAble(UpperStateEnum.SAME_ORDER_STATE.contains(dineinOrderDetailRespDTO.getUpperState()) ?
                dineinOrderDetailRespDTO.getPayAble() : dineinOrderDetailRespDTO.getActuallyPayFee());
        // 多单结账有已支付记录
        printPreCheckoutDTO.setPayRecordList(printUtils.requirePayRecordList(dineinOrderDetailRespDTO));
        printPreCheckoutDTO.setActuallyPay(dineinOrderDetailRespDTO.getActuallyPayFee());
        StoreBizDTO storeBizDTO = storeInfoFunction.apply(UserContextUtils.getStoreGuid());
        printPreCheckoutDTO.setStoreAddress(storeBizDTO.getAddressDetail());
        printPreCheckoutDTO.setTel(storeBizDTO.getContactTel());
        printPreCheckoutDTO.setPayQrCode(dineinOrderDetailRespDTO.getPayQrCode());
        if (Objects.nonNull(dineinOrderDetailRespDTO.getOriginalPrice())) {
            printPreCheckoutDTO.setOriginalPrice(dineinOrderDetailRespDTO.getOriginalPrice());
        }
        if (!(printItemDetailDTO instanceof PrintCheckOutDTO)) {
            return;
        }
        //上层强转
        PrintCheckOutDTO printCheckOutDTO = (PrintCheckOutDTO) printPreCheckoutDTO;
        if (Objects.nonNull(dineinOrderDetailRespDTO.getCheckoutTime())) {
            printCheckOutDTO.setCheckOutTime(DateTimeUtils.localDateTime2Mills(dineinOrderDetailRespDTO.getCheckoutTime()));
        }
        printCheckOutDTO.setChangedPay(Optional.ofNullable(dineinOrderDetailRespDTO.getChangeFee()).orElse(BigDecimal.ZERO));
        printCheckOutDTO.setTradeMode(dineinOrderDetailRespDTO.getTradeMode());
        // 会员信息
        printCheckOutDTO.setMemberName(dineinOrderDetailRespDTO.getMemberName());
        printCheckOutDTO.setMemberPhone(DesensitizedUtil.mobilePhone(dineinOrderDetailRespDTO.getMemberPhone()));
        printCheckOutDTO.setMultiMemberPayRecords(OrderTransform.INSTANCE
                .orderMultiMemberPay2MultiMemberPayRecord(dineinOrderDetailRespDTO.getMultiMemberPays()));
        // 挂账信息
        printCheckOutDTO.setDebtUnitName(dineinOrderDetailRespDTO.getDebtUnitName());
        printCheckOutDTO.setDebtContactName(dineinOrderDetailRespDTO.getDebtContactName());
        printCheckOutDTO.setDebtContactTel(DesensitizedUtil.mobilePhone(dineinOrderDetailRespDTO.getDebtContactTel()));
        // 多单结账信息
        List<DineinOrderDetailRespDTO> otherOrderDetails = dineinOrderDetailRespDTO.getOtherOrderDetails();
        if (!CollectionUtils.isEmpty(otherOrderDetails)) {
            List<PrintCheckOutDTO> otherPrintCheckOuts = otherOrderDetails.stream().map(e -> {
                PrintCheckOutDTO innerPrintCheckOutDTO = new PrintCheckOutDTO();
                setMemberAndDebtInfo(innerPrintCheckOutDTO, e);
                return innerPrintCheckOutDTO;
            }).collect(Collectors.toList());
            printCheckOutDTO.setOtherPrintCheckOuts(otherPrintCheckOuts);
        }
    }

    /**
     * 设置会员和挂账信息
     */
    private void setMemberAndDebtInfo(PrintCheckOutDTO printCheckOutDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        // 会员信息
        printCheckOutDTO.setMemberName(dineinOrderDetailRespDTO.getMemberName());
        printCheckOutDTO.setMemberPhone(DesensitizedUtil.mobilePhone(dineinOrderDetailRespDTO.getMemberPhone()));
        printCheckOutDTO.setMultiMemberPayRecords(OrderTransform.INSTANCE
                .orderMultiMemberPay2MultiMemberPayRecord(dineinOrderDetailRespDTO.getMultiMemberPays()));
        // 挂账信息
        printCheckOutDTO.setDebtUnitName(dineinOrderDetailRespDTO.getDebtUnitName());
        printCheckOutDTO.setDebtContactName(dineinOrderDetailRespDTO.getDebtContactName());
        printCheckOutDTO.setDebtContactTel(DesensitizedUtil.mobilePhone(dineinOrderDetailRespDTO.getDebtContactTel()));
    }

    private BigDecimal getDiscountAfterTotal(List<PrintItemRecord> itemRecordList) {
        List<PrintItemRecord> printItemRecords = Optional.ofNullable(itemRecordList).orElse(Lists.newArrayList());
        return printItemRecords.stream()
                .map(PrintItemRecord::getItemDiscountAfterPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private String getMarkNo(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        String markNo = null;
        if (dineinOrderDetailRespDTO.getTradeMode().equals(TradeModeEnum.DINE.getMode())) {
            //正常 直接返回桌台号
            if (!StringUtils.isEmpty(dineinOrderDetailRespDTO.getDiningTableName())) {
                markNo = dineinOrderDetailRespDTO.getDiningTableName();
            }
            Boolean associatedFlag = dineinOrderDetailRespDTO.getAssociatedFlag();
            if (Boolean.TRUE.equals(associatedFlag)) {
                // 联台单
                List<String> associatedTableNames = Optional.ofNullable(dineinOrderDetailRespDTO.getAssociatedTableNames())
                        .orElse(Lists.newArrayList());
                int totalSize = associatedTableNames.size();
                String associatedTableNameStr = String.join("、", associatedTableNames);
                if (totalSize > 10) {
                    associatedTableNames = associatedTableNames.subList(0, 10);
                    associatedTableNameStr = String.join("、", associatedTableNames) + "等" + totalSize + "桌";
                }
                String associatedTableName = "联-%s (%s)";
                return String.format(associatedTableName, dineinOrderDetailRespDTO.getAssociatedSn(), associatedTableNameStr);
            }
        } else {
            //快餐如果没有单号 就直接
            markNo = StringUtils.isEmpty(dineinOrderDetailRespDTO.getDiningTableName()) ? dineinOrderDetailRespDTO.getMark()
                    //快餐没有单号
                    : dineinOrderDetailRespDTO.getDiningTableName() + "/" + dineinOrderDetailRespDTO.getMark();
        }
        return markNo;
    }

    /**
     * 设置打印体
     */
    public void setPrintBody(PrintRefundItemDTO printRefundItemDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO,
                             Integer itemInvoiceType, boolean isReturn) {
        printRefundItemDTO.setItemRecordList(printUtils.extractItemRecords(dineinOrderDetailRespDTO, isReturn));
        printRefundItemDTO.setMarkName("桌台");
        //待抽出
        printRefundItemDTO.setMarkNo(getMarkNo(dineinOrderDetailRespDTO));
        printRefundItemDTO.setOrderNo(dineinOrderDetailRespDTO.getOrderNo());
        printRefundItemDTO.setPersonNumber(dineinOrderDetailRespDTO.getGuestCount());
        printRefundItemDTO.setOrderTime(DateTimeUtils.localDateTime2Mills(dineinOrderDetailRespDTO.getGmtCreate()));
        printRefundItemDTO.setTradeMode(dineinOrderDetailRespDTO.getTradeMode());
        printRefundItemDTO.setPartRefundFlag(Objects.nonNull(dineinOrderDetailRespDTO.getRefundOrderGuid()));
        if (!(printRefundItemDTO instanceof PrintOrderItemDTO)) {
            return;
        }
        //上层强转
        PrintOrderItemDTO printOrderItemDTO = (PrintOrderItemDTO) printRefundItemDTO;
        printOrderItemDTO.setItemInvoiceType(itemInvoiceType);
        printOrderItemDTO.setRemark(dineinOrderDetailRespDTO.getRemark());
        // 判断设备类型
        Integer deviceType = dineinOrderDetailRespDTO.getDeviceType();
        if (BaseDeviceTypeEnum.isApplet(deviceType) || Objects.equals(BaseDeviceTypeEnum.WECHAT.getCode(), deviceType)
                || Objects.equals(BaseDeviceTypeEnum.WECHAT_MINI.getCode(), deviceType)) {
            printOrderItemDTO.setOrderTypeName("扫码点餐");
        } else {
            printOrderItemDTO.setOrderTypeName("堂食单");
        }
    }

    /**
     * 设置打印体
     */
    public void setPrintBody(PrintCoTableCbDTO printCoTableCbDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO, Function<String, StoreBizDTO> storeInfoFunction) {
        printCoTableCbDTO.setStoreName(UserContextUtils.getStoreName());
        List<PrintCoTableCbDTO.PrintTableDTO> tableOfCheckout = printUtils.getPrintTableOfCheckout(dineinOrderDetailRespDTO);
        printCoTableCbDTO.setTableOrderList(tableOfCheckout);
        printCoTableCbDTO.setTableTotal(tableOfCheckout.stream().map(PrintCoTableCbDTO.PrintTableDTO::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        printCoTableCbDTO.setReduceRecordList(printUtils.extractReduceRecordList(dineinOrderDetailRespDTO));
        printCoTableCbDTO.setPayAble(Objects.nonNull(dineinOrderDetailRespDTO.getPayAble()) ?
                dineinOrderDetailRespDTO.getPayAble() : dineinOrderDetailRespDTO.getActuallyPayFee());
        StoreBizDTO storeBizDTO = storeInfoFunction.apply(UserContextUtils.getStoreGuid());
        printCoTableCbDTO.setStoreAddress(storeBizDTO.getAddressDetail());
        printCoTableCbDTO.setTel(storeBizDTO.getContactTel());
        printCoTableCbDTO.setChangedPay(Optional.ofNullable(dineinOrderDetailRespDTO.getChangeFee()).orElse(BigDecimal.ZERO));
        printCoTableCbDTO.setActuallyPay(dineinOrderDetailRespDTO.getActuallyPayFee());
        printCoTableCbDTO.setPayRecordList(printUtils.requirePayRecordList(dineinOrderDetailRespDTO));
        printCoTableCbDTO.setAdditionalChargeList(printUtils.getAppendFee(dineinOrderDetailRespDTO));
        //会员信息
        printCoTableCbDTO.setMemberPhone(DesensitizedUtil.mobilePhone(dineinOrderDetailRespDTO.getMemberPhone()));
        printCoTableCbDTO.setMemberName(dineinOrderDetailRespDTO.getMemberName());
        printCoTableCbDTO.setMultiMemberPayRecords(OrderTransform.INSTANCE
                .orderMultiMemberPay2MultiMemberPayRecord(dineinOrderDetailRespDTO.getMultiMemberPays()));
        // 挂账信息
        printCoTableCbDTO.setDebtUnitName(dineinOrderDetailRespDTO.getDebtUnitName());
        printCoTableCbDTO.setDebtContactName(dineinOrderDetailRespDTO.getDebtContactName());
        printCoTableCbDTO.setDebtContactTel(DesensitizedUtil.mobilePhone(dineinOrderDetailRespDTO.getDebtContactTel()));
        // 多单结账信息
        List<DineinOrderDetailRespDTO> otherOrderDetails = dineinOrderDetailRespDTO.getOtherOrderDetails();
        if (!CollectionUtils.isEmpty(otherOrderDetails)) {
            List<PrintCoTableCbDTO> otherPrintCoTableCbDTO = otherOrderDetails.stream().map(e -> {
                PrintCoTableCbDTO innerPrintCoTableCbDTO = new PrintCoTableCbDTO();
                setMemberAndDebtInfo(innerPrintCoTableCbDTO, e);
                return innerPrintCoTableCbDTO;
            }).collect(Collectors.toList());
            printCoTableCbDTO.setOtherPrintCoTableCbs(otherPrintCoTableCbDTO);
        }
    }

    /**
     * 设置会员和挂账信息
     */
    private void setMemberAndDebtInfo(PrintCoTableCbDTO printCoTableCbDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        // 会员信息
        printCoTableCbDTO.setMemberName(dineinOrderDetailRespDTO.getMemberName());
        printCoTableCbDTO.setMemberPhone(DesensitizedUtil.mobilePhone(dineinOrderDetailRespDTO.getMemberPhone()));
        printCoTableCbDTO.setMultiMemberPayRecords(OrderTransform.INSTANCE
                .orderMultiMemberPay2MultiMemberPayRecord(dineinOrderDetailRespDTO.getMultiMemberPays()));
        // 挂账信息
        printCoTableCbDTO.setDebtUnitName(dineinOrderDetailRespDTO.getDebtUnitName());
        printCoTableCbDTO.setDebtContactName(dineinOrderDetailRespDTO.getDebtContactName());
        printCoTableCbDTO.setDebtContactTel(DesensitizedUtil.mobilePhone(dineinOrderDetailRespDTO.getDebtContactTel()));
    }

    /**
     * 设置打印体
     */
    public void setPrintBody(PrintPreCoTableCbDTO printPreCoTableCbDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO, Function<String, StoreBizDTO> storeInfoFunction) {
        printPreCoTableCbDTO.setStoreName(UserContextUtils.getStoreName());
        List<PrintPreCoTableCbDTO.PrintTableDTO> printTableDTOS = printUtils.getPrintTableOfPreCheckout(dineinOrderDetailRespDTO);
        printPreCoTableCbDTO.setTableOrderList(printTableDTOS);
        printPreCoTableCbDTO.setTableTotal(printTableDTOS.stream().map(PrintPreCoTableCbDTO.PrintTableDTO::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        printPreCoTableCbDTO.setReduceRecordList(printUtils.extractReduceRecordList(dineinOrderDetailRespDTO));
        printPreCoTableCbDTO.setPayAble(dineinOrderDetailRespDTO.getActuallyPayFee());
        StoreBizDTO storeBizDTO = storeInfoFunction.apply(UserContextUtils.getStoreGuid());
        printPreCoTableCbDTO.setStoreAddress(storeBizDTO.getAddressDetail());
        printPreCoTableCbDTO.setTel(storeBizDTO.getContactTel());
        printPreCoTableCbDTO.setAdditionalChargeList(printUtils.getAppendFee(dineinOrderDetailRespDTO));
        printPreCoTableCbDTO.setPayQrCode(dineinOrderDetailRespDTO.getPayQrCode());
    }


    public void setPrintBody(PrintLabelDTO printLabelDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO, Function<String, StoreBizDTO> storeInfoFunction) {
        printLabelDTO.setItemRecordList(printUtils.extractItemRecords(dineinOrderDetailRespDTO, false));
        printLabelDTO.setStoreName(UserContextUtils.getStoreName());
        printLabelDTO.setSerialNumber(dineinOrderDetailRespDTO.getTradeMode().equals(TradeModeEnum.DINE.getMode()) ?
                dineinOrderDetailRespDTO.getOrderNo() : dineinOrderDetailRespDTO.getMark());
        printLabelDTO.setTel(storeInfoFunction.apply(UserContextUtils.getStoreGuid()).getContactTel());
        printLabelDTO.setTradeMode(dineinOrderDetailRespDTO.getTradeMode());
    }

    public void setPrintBody(PrintItemLabelDTO printItemLabelDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        printItemLabelDTO.setItemRecordList(printUtils.extractItemRecords(dineinOrderDetailRespDTO, false));
        List<DineInItemDTO> dineInItemDTOS = dineinOrderDetailRespDTO.getDineInItemDTOS();
        DineInItemDTO singleItem = dineInItemDTOS.get(0);
        printItemLabelDTO.setItemName(singleItem.getItemName());
        printItemLabelDTO.setSerialNumber(singleItem.getCode());
        printItemLabelDTO.setCurrentCount(singleItem.getCurrentCount());
        printItemLabelDTO.setItemPrice(singleItem.getItemPrice());
        printItemLabelDTO.setTradeMode(dineinOrderDetailRespDTO.getTradeMode());
        printItemLabelDTO.setUnit(singleItem.getUnit());
    }


    public void setPrintBody(PrintChangeItemDTO printChangeItemDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        printChangeItemDTO.setItemRecordList(printUtils.extractItemRecords(dineinOrderDetailRespDTO, false));
        printChangeItemDTO.setOrderNo(dineinOrderDetailRespDTO.getOrderNo());
        printChangeItemDTO.setOrderGuid(dineinOrderDetailRespDTO.getGuid());
        printChangeItemDTO.setTradeMode(dineinOrderDetailRespDTO.getTradeMode());
        printChangeItemDTO.setDiningTableName(dineinOrderDetailRespDTO.getDiningTableName());
        printChangeItemDTO.setCreateStaffName(dineinOrderDetailRespDTO.getCreateStaffName());
        printChangeItemDTO.setGmtCreate(dineinOrderDetailRespDTO.getGmtCreate());
        // 原菜集合
        List<KdsItemDTO> originalKdsItemList = dineinOrderDetailRespDTO.getOriginalDineInItemList()
                .stream()
                .map(e -> buildKdsItemDTO(e, null))
                .collect(Collectors.toList());
        printChangeItemDTO.setOriginalKdsItemList(originalKdsItemList);
        // 新菜集合
        List<KdsItemDTO> changesKdsItemList = dineinOrderDetailRespDTO.getChangeDineInItemList()
                .stream()
                .map(e -> buildKdsItemDTO(e, null))
                .collect(Collectors.toList());
        printChangeItemDTO.setChangesKdsItemList(changesKdsItemList);
        printChangeItemDTO.setCancelFlag(dineinOrderDetailRespDTO.getChangeCancelFlag());
    }


    /**
     * 商品 to kds商品
     */
    public static KdsItemDTO buildKdsItemDTO(DineInItemDTO kdsItem, String orderRemark) {
        KdsItemDTO kdsItemDTO = new KdsItemDTO();
        if (BigDecimalUtil.greaterThanZero(kdsItem.getFreeCount())) {
            kdsItemDTO.setCurrentCount(kdsItem.getCurrentCount().add(kdsItem.getFreeCount()));
        } else {
            kdsItemDTO.setCurrentCount(kdsItem.getCurrentCount());
        }
        kdsItemDTO.setIsWeight(kdsItem.getItemType().equals(ItemTypeEnum.WEIGH.getCode()));
        kdsItemDTO.setItemGuid(kdsItem.getItemGuid());
        kdsItemDTO.setItemName(kdsItem.getItemName());
        kdsItemDTO.setItemRemark(kdsItem.getRemark());
        kdsItemDTO.setItemState(kdsItem.getItemState());
        kdsItemDTO.setOrderItemGuid(kdsItem.getGuid());
        kdsItemDTO.setOrderRemark(orderRemark);
        kdsItemDTO.setSkuCode(kdsItem.getCode());
        kdsItemDTO.setSkuGuid(kdsItem.getSkuGuid());
        kdsItemDTO.setSkuName(kdsItem.getSkuName());
        kdsItemDTO.setSkuUnit(kdsItem.getUnit());
        List<KdsAttrGroupDTO> kdsAttrGroupDTOS = new ArrayList<>();
        List<ItemAttrDTO> itemAttrDTOS = kdsItem.getItemAttrDTOS();
        Map<String, List<ItemAttrDTO>> attrGroupGuidMap = CollectionUtil.toListMap(itemAttrDTOS,
                "attrGroupGuid+attrGroupName");
        for (Map.Entry<String, List<ItemAttrDTO>> entry : attrGroupGuidMap.entrySet()) {
            String attrGroupGuid = entry.getKey();
            List<ItemAttrDTO> itemAttrDTOList = entry.getValue();
            KdsAttrGroupDTO kdsAttrGroupDTO = new KdsAttrGroupDTO();
            kdsAttrGroupDTOS.add(kdsAttrGroupDTO);
            String[] split = attrGroupGuid.split("\\+");
            kdsAttrGroupDTO.setGroupGuid(split[0]);
            kdsAttrGroupDTO.setGroupName(split[1]);
            List<KdsItemAttrDTO> kdsItemAttrDTOS = new ArrayList<>();
            for (ItemAttrDTO itemAttrDTO : itemAttrDTOList) {
                KdsItemAttrDTO kdsItemAttrDTO = new KdsItemAttrDTO();
                kdsItemAttrDTOS.add(kdsItemAttrDTO);
                kdsItemAttrDTO.setAttrGuid(itemAttrDTO.getAttrGuid());
                kdsItemAttrDTO.setAttrName(itemAttrDTO.getAttrName());
                kdsItemAttrDTO.setAttrNumber(itemAttrDTO.getNum());
            }
            kdsAttrGroupDTO.setAttrs(kdsItemAttrDTOS);
        }
        kdsItemDTO.setAttrGroup(kdsAttrGroupDTOS);
        return kdsItemDTO;
    }

    public void setPrintBody(PrintTransferItemDTO transferItemDTO,
                             DineinOrderDetailRespDTO newDineinOrderDetailRespDTO,
                             DineinOrderDetailRespDTO oldDineinOrderDetailRespDTO) {
        transferItemDTO.setItemRecordList(printUtils.extractItemRecords(newDineinOrderDetailRespDTO, false));
        transferItemDTO.setOrderNo(newDineinOrderDetailRespDTO.getOrderNo());
        transferItemDTO.setOrderGuid(newDineinOrderDetailRespDTO.getGuid());
        transferItemDTO.setTradeMode(newDineinOrderDetailRespDTO.getTradeMode());
        transferItemDTO.setNewDiningTableName(getMarkNo(newDineinOrderDetailRespDTO));
        transferItemDTO.setOldDiningTableName(getMarkNo(oldDineinOrderDetailRespDTO));
        transferItemDTO.setOperatorName(newDineinOrderDetailRespDTO.getOperatorName());
        transferItemDTO.setTransferTime(newDineinOrderDetailRespDTO.getTransferTime());
        List<TransferItemDetailsDTO> transferItemList =
                TransferItemBizBuilder.dineInItemDTOList2TransferItemDetailsDTOList(newDineinOrderDetailRespDTO.getDineInItemDTOS());
        transferItemDTO.setTransferItemList(transferItemList);
    }

    /**
     * 设置退款打印体
     *
     * @param printRefundDTO 退款单打印对象
     * @param respDTO        账单对象
     */
    public void setPrintBody(PrintRefundDTO printRefundDTO, DineinOrderDetailRespDTO respDTO) {
        printRefundDTO.setItemRecordList(printUtils.extractItemRecords(respDTO, false));
        printRefundDTO.setOrderNo(respDTO.getOrderNo());
        printRefundDTO.setTradeMode(respDTO.getTradeMode());
        printRefundDTO.setStoreName(UserContextUtils.getStoreName());
        printRefundDTO.setMarkNo(getMarkNo(respDTO));

        RefundOrderPrintDTO refundOrderPrintDTO = respDTO.getRefundOrderPrintDTO();
        log.info("接收到的退款记录: {}", JacksonUtils.writeValueAsString(refundOrderPrintDTO));
        // 退款时间
        printRefundDTO.setRefundTime(refundOrderPrintDTO.getRefundTime());
        // 退款金额
        printRefundDTO.setRefundAmount(refundOrderPrintDTO.getRefundAmount());
        // 退款方式
        printRefundDTO.setRefundMethod(refundOrderPrintDTO.getPaymentTypeName());
        // 退款方式类型
        printRefundDTO.setRefundMethodType(refundOrderPrintDTO.getRefundType());
        // 退款原因
        printRefundDTO.setRefundReason(refundOrderPrintDTO.getRefundReason());
        // 操作时间
        printRefundDTO.setOperationTime(refundOrderPrintDTO.getOperationTime());
        // 操作类型
        printRefundDTO.setOperationType(refundOrderPrintDTO.getTypeEnum());
        // 附加费
        printRefundDTO.setAdditionalChargeList(refundOrderPrintDTO.getAdditionalChargeList());
    }

}