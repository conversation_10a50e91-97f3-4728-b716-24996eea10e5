package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.journaling.req.JournalAppBaseReqDTO;
import com.holderzone.saas.store.dto.journaling.req.SaleStatisticsByHoursReqDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreStatisticsAppReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreGatherReportRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreSaleItemDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreStatisticsAppRespDTO;
import com.holderzone.saas.store.dto.order.BusinessDayDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.UpdateOrderReqDTO;
import com.holderzone.saas.store.dto.reserve.UpdateOrderReserveReqDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderInvoiceStateReqDTO;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderMemberInfoReqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByDineAndFastOrderRespDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.dto.BusinessHisTrendDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface OrderService extends IService<OrderDO> {

    /**
     * 查询反结账次数
     *
     * @param recoveryId
     * @return
     */
    int selectRecoveryCount(String recoveryId);

    /**
     * 查询反结账历史单
     *
     * @param recoveryId
     * @return
     */
    List<OrderDO> listByRecoveryId(String recoveryId);

    /**
     * 悲观锁查订单
     *
     * @param id
     * @return
     */
    OrderDO getByIdWithLock(Serializable id);

    /**
     * 分页查询订单列表
     */
    IPage<OrderInfoRespDTO> pageOrderInfo(IPage<OrderInfoRespDTO> page, DineInOrderListReqDTO reqDTO);

    boolean booleanIsLastOrder(OrderDO orderDO);

    /**
     * 查询主单下面的子单
     *
     * @param mainOrderGuid 主单guid
     * @return
     */
    List<OrderDO> listByMainOrderGuid(Serializable mainOrderGuid);

    /**
     * 查询主单和upperState状态为4的子单
     *
     * @param mainOrderGuid 主单guid
     * @return
     */
    List<OrderDO> otherListByMainOrderGuid(Long mainOrderGuid);


    List<OrderDO> otherListByMainOrderGuids(List<Long> mainOrderGuids);

    /**
     * 查询主单下面的子单
     *
     * @param mainOrderGuid 主单guid
     * @return
     */
    List<OrderDO> listOrderIdByMainOrderGuid(String mainOrderGuid);

    List<OrderDO> listByOrderNoAndCreate(String orderNo, LocalDateTime gmtCreated);

    /**
     * 从缓存中拿订单，缓存没有再查数据库
     *
     * @param id
     * @return
     */
    OrderDO getByIdWithCache(String id);

    boolean updateByIdWithDeleteCache(OrderDO orderDO);

    @Deprecated
    boolean updateBatchByIdWithDeleteCache(List<OrderDO> orderDOS);

    /**
     * 批量查询order信息
     *
     * @param guidLists
     * @return
     */
    List<OrderDO> getListByIds(List<String> guidLists);

    /**
     * 查询所有未结账的订单（此接口专门给本地化使用）
     */
    List<OrderDO> getLocalUnpay(LocalQuery localQuery);

    /**
     * 查询已结账的订单（此接口专门给本地化使用）
     *
     * @param guidLists
     * @return
     */
    List<OrderDO> queryIsPayOrderList(List<String> guidLists);


    Integer getTotalGuestCount(Long guid);

    boolean checkOrderPending(String openId);

    OrderDO findByEnterpriseGuid();

    int updateOrderIsHandleByOrderGuid(Long guid, Integer isHandle);

    /**
     * 统计商户后台/数据报表/订单统计 数据条数
     *
     * @param queryDTO 查询条件
     * @return 统计结果
     */
    Integer getOrderStatisticsCount(BusinessOrderStatisticsQueryDTO queryDTO);

    /**
     * 获取商户后台/数据报表/订单统计 分页数据  考虑导出可能会使用这个方法 固没有返回OrderDO 直接返回DTO
     *
     * @param queryDTO 　查询条件
     * @return 分页数据
     */
    List<BusinessOrderStatisticsRespDTO> getOrderStatisticsPage(BusinessOrderStatisticsQueryDTO queryDTO);

    /**
     * 查询商户后台/数据报表/订单统计 合计
     *
     * @param queryDTO 查询条件入参
     * @return 统计结果
     */
    BusinessOrderStatisticsCombinedRespDTO getOrderStatisticsCombined(BusinessOrderStatisticsQueryDTO queryDTO);

    /**
     * 查询商户后台/数据报表/订单统计 团购验券实际支付金额统计
     *
     * @param queryDTO 查询条件入参
     * @return 统计结果
     */
    BigDecimal getGrouponOrderStatisticsCombined(BusinessOrderStatisticsQueryDTO queryDTO);

    /**
     * 根据订单号获取 主单/子单信息
     *
     * @param orderGuid 订单号
     * @return 订单信息
     */
    List<OrderDO> getMergeOrderList(String orderGuid, Integer upperState);

    /**
     * 根据原单GUID查询新单信息
     *
     * @param guid 原单guid
     * @return 新单信息
     */
    List<OrderDO> getListByOriginalOrderGuid(Long guid);

    /**
     * 根据原单GUID查询退单信息
     */
    List<OrderDO> getRefundListByOriginalOrderGuid(List<Long> orderGuids);

    /***
     * 根据订单guid更新订单是否录入操作员
     * @param orderGuidList 集合
     * @return
     */
    int updateIsWaiters(List<String> orderGuidList);

    /**
     * 调整单-正餐/快餐订单列表
     * 正餐、快餐展示近30天已结账的订单数据
     *
     * @param query 关键字和门店guid
     * @return 正餐/快餐订单信息列表
     */
    Page<AdjustByDineAndFastOrderRespDTO> pageDineAndFastOrder(AdjustByOrderListQuery query);

    /**
     * 修改订单的调整单状态为已调整
     */
    void updateAdjustStateIsTrue(Long guid);

    /**
     * 随行红包使用设置
     *
     * @param updateOrderReqDTO 订单guid
     * @return 结果
     */
    Boolean setFollowRedPacket(UpdateOrderReqDTO updateOrderReqDTO);

    /**
     * 根据订单查询订单状态
     *
     * @param orderGuid 订单guid
     * @return 订单状态 (-1：未查询到订单 1：未结账， 2：已结账， 3：已退款，4：已作废)
     */
    Integer getOrderStateByGuid(String orderGuid);

    /**
     * 根据订单查询订单金额
     *
     * @param orderGuid 订单guid
     * @return 订单金额
     */
    BigDecimal getOrderFeeByGuid(String orderGuid);

    List<BusinessDayDTO> queryOrderBusinessDay(List<BusinessDayDTO> businessDayDTOList);

    BusinessDataRespDTO listOrderBusinessData(JournalAppBaseReqDTO reqDTO);

    List<BusinessHisTrendDTO> listOrderBusinessHisTrend(JournalAppBaseReqDTO reqDTO);

    IPage<StoreGatherReportRespDTO> listStoreGatherBusiness(IPage<StoreGatherReportRespDTO> iPage, StoreGatherReportReqDTO storeGatherReportReq);

    List<StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO> saleStoreStatistics(StoreStatisticsAppReqDTO storeStatisticsAppReqDTO);

    List<StoreSaleItemDTO> saleByHoursStatistics(SaleStatisticsByHoursReqDTO statisticsByHoursReqDTO);

    /**
     * 增加退款金额
     */
    void updateRefundAmountByGuid(Long orderGuid, BigDecimal refundAmount);

    /**
     * 在原订单上增加退款订单guid字段
     */
    void addRefundOrderGuid(Long orderGuid, Long refundOrderGuid);

    /**
     * 查询收银员信息
     *
     * @param businessOrderStatisticsQueryDTO 筛选条件
     * @return 收银员信息列表
     */
    List<UserBriefDTO> getCheckoutStaffs(BusinessOrderStatisticsQueryDTO businessOrderStatisticsQueryDTO);

    void updateOrderIsUpdatedEs(String orderGuid);

    List<String> queryReadyOrder(SingleListDTO singleListDTO);

    void updateOrderReserve(UpdateOrderReserveReqDTO reqDTO);

    void updateOrderMemberInfo(UpdateOrderMemberInfoReqDTO reqDTO);

    void updateOrderInvoiceState(UpdateOrderInvoiceStateReqDTO reqDTO);

    List<String> listPaymentTypeName();

    void updateSameOrderFeeForCombine(Long guid);

}
