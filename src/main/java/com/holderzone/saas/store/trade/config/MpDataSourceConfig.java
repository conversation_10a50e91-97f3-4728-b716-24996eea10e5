package com.holderzone.saas.store.trade.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MpDataSourceConfig
 * @date 2018/09/17 15:40
 * @description
 * @program holder-saas-store-trade
 */
@Configuration
public class MpDataSourceConfig {

    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

}
