package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterQueryDTO;
import com.holderzone.saas.store.dto.print.content.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintClientService
 * @date 2018/10/17 15:20
 * @description //TODO
 * @program holder-saas-store-order
 */
@Component
@FeignClient(name = "holder-saas-store-print", fallbackFactory = PrintClientService.FallBack.class)
public interface PrintClientService {

    @PostMapping("/print_record/send")
    String printItemDetail(@RequestBody PrintItemDetailDTO printItemDetailDTO);

    @PostMapping("/print_record/send")
    String printPreCheck(@RequestBody PrintPreCheckoutDTO printPreCheckoutDTO);

    @PostMapping("/print_record/send")
    String printPreCoTableCb(@RequestBody PrintPreCoTableCbDTO printPreCoTableCbDTO);

    @PostMapping("/print_record/send")
    String printCheckout(@RequestBody PrintCheckOutDTO printCheckOutDTO);

    @PostMapping("/print_record/send")
    String printCoTableCb(@RequestBody PrintCoTableCbDTO printCoTableCbDTO);

    @PostMapping("/print_record/send")
    String printRefundItem(@RequestBody PrintRefundItemDTO printRefundItemDTO);

    @PostMapping("/print_record/send")
    String printOrderItem(@RequestBody PrintOrderItemDTO printOrderItemDTO);

    @PostMapping("/print_record/send")
    String printLabelDTO(@RequestBody PrintLabelDTO printLabelDTO);

    @GetMapping("/format/judge_pre_qr")
    boolean judgeEnablePreCheckFormat(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/printer/query_by_condition")
    @ApiOperation(value = "根据条件查询打印机")
    List<PrinterDTO> queryByCondition(@RequestBody PrinterQueryDTO queryDTO);

    @Slf4j
    @Component
    class FallBack implements FallbackFactory<PrintClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public PrintClientService create(Throwable cause) {
            return new PrintClientService() {

                @Override
                public String printItemDetail(PrintItemDetailDTO printItemDetailDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printItemDetail",
                                JacksonUtils.writeValueAsString(printItemDetailDTO), ThrowableUtils.asString(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String printPreCheck(PrintPreCheckoutDTO printPreCheckoutDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printPreCheck",
                                JacksonUtils.writeValueAsString(printPreCheckoutDTO), ThrowableUtils.asString(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String printPreCoTableCb(PrintPreCoTableCbDTO printPreCoTableCbDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printPreCoTableCb",
                                JacksonUtils.writeValueAsString(printPreCoTableCbDTO), ThrowableUtils.asString(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String printCheckout(PrintCheckOutDTO printCheckOutDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printCheckout",
                                JacksonUtils.writeValueAsString(printCheckOutDTO), ThrowableUtils.asString(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String printCoTableCb(PrintCoTableCbDTO printCoTableCbDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printCoTableCb",
                                JacksonUtils.writeValueAsString(printCoTableCbDTO), ThrowableUtils.asString(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String printRefundItem(PrintRefundItemDTO printRefundItemDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printRefundItem",
                                JacksonUtils.writeValueAsString(printRefundItemDTO), ThrowableUtils.asString(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String printOrderItem(PrintOrderItemDTO printOrderItemDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printOrderItem",
                                JacksonUtils.writeValueAsString(printOrderItemDTO), ThrowableUtils.asString(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public String printLabelDTO(PrintLabelDTO printLabelDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printLabelDTO",
                                JacksonUtils.writeValueAsString(printLabelDTO), ThrowableUtils.asString(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public boolean judgeEnablePreCheckFormat(String storeGuid) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "printLabelDTO", storeGuid, ThrowableUtils.asString(cause));
                    }
                    throw new ServerException();
                }

                @Override
                public List<PrinterDTO> queryByCondition(PrinterQueryDTO queryDTO) {
                    log.error(HYSTRIX_PATTERN, "queryByCondition", JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
