package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 异常单表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_order_abnormal_record")
public class OrderAbnormalRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 订单guid
     */
    private String orderGuid;

    /**
     * 用餐方式id 0.堂食 1.快餐
     */
    private Integer diningMethodId;

    /**
     * 支付方式id 1.微信 2.支付宝
     */
    private Integer paymentMethodId;

    /**
     * 支付方式名称
     */
    private String paymentMethodName;

    /**
     * 支付状态 1.待支付 2：异常 3：支失败 4：成功
     */
    private Integer paymentStatu;

    /**
     * 支付时间:支付记录的发起时间
     */
    private LocalDateTime checkoutTime;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 异常信息
     */
    private String abnormalMessage;

    /**
     * 支付guid
     */
    private String payGuid;

    /**
     * 门店guid
     */
    private String storeGuid;
}
