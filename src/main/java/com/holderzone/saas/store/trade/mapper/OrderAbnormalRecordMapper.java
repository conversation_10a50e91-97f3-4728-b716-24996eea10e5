package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.trade.OrderAbnormalListReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordRespDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderAbnormalRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderAbnormalRecordMapper extends BaseMapper<OrderAbnormalRecordDO> {

    List<OrderAbnormalRecordRespDTO> listAbnormalOrders(@Param("dto") OrderAbnormalListReqDTO orderAbnormalListReqDTO);

    int deleteRecord(String orderGuid);
}
