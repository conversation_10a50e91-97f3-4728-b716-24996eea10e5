package com.holderzone.saas.store.trade.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.service.DebtUnitRecordPrintService;
import com.holderzone.saas.store.trade.service.DebtUnitRecordService;
import com.holderzone.saas.store.trade.service.DebtUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;

/**
 * 挂账Controller
 *
 * @since 2020-12-15
 */
@Slf4j
@Api(value = "挂账接口")
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/debt")
public class DebtController {

    private final DebtUnitService debtUnitService;
    private final DebtUnitRecordService debtUnitRecordService;
    private final DebtUnitRecordPrintService debtUnitRecordPrintService;
    private final ExecutorService debtRepaymentExecutor;
    private final DynamicHelper dynamicHelper;

    @PostMapping("/unit/save")
    @ApiOperation(value = "新建挂账单位")
    public Boolean saveUnit(@Validated @RequestBody DebtUnitSaveReqDTO reqDTO) {
        log.info("新建挂账单位接口入参：{}", JSON.toJSONString(reqDTO));
        return debtUnitService.saveUnit(reqDTO);
    }

    @PostMapping("/unit/page")
    @ApiOperation(value = "分页查询单位列表")
    public Page<DebtUnitPageRespDTO> unitPage(@RequestBody DebtUnitPageReqDTO reqDTO) {
        log.info("挂账单位分页查询入参：{}", JSON.toJSONString(reqDTO));
        return debtUnitService.unitPage(reqDTO);
    }

    @GetMapping("/unit/delete")
    @ApiOperation(value = "删除挂账单位")
    public Boolean unitDelete(@RequestParam("unitGuid") String unitGuid) {
        log.info("挂账单位删除单位guid：{}", unitGuid);
        return debtUnitService.unitDelete(unitGuid);
    }

    @PostMapping("/unit/page_debt_unit_record")
    @ApiOperation(value = "分页查询挂账单位流水")
    public Page<DebtUnitRecordPageRespDTO> pageDebtUnitRecord(@Validated @RequestBody DebtUnitRecordPageReqDTO reqDTO) {
        log.info("分页查询挂账单位流水接口入参：{}", JSON.toJSONString(reqDTO));
        return debtUnitRecordService.pageDebtUnitRecord(reqDTO);
    }

    @GetMapping("/unit/query_debt_unit_total")
    @ApiOperation(value = "查询挂账单位汇总")
    public DebtUnitRecordTotalDTO queryDebtUnitTotal(@RequestParam("unitCode") String unitCode) {
        log.info("查询挂账单位汇总接口入参：{}", unitCode);
        return debtUnitRecordService.queryDebtUnitTotal(unitCode);
    }

    @PostMapping("/unit/update_debt_unit")
    @ApiOperation(value = "挂账管理还款")
    public Boolean updateDebtUnit(@RequestBody DebtUnitRecordUpdateReqDTO updateReqDTO) {
        log.info("挂账管理还款入参：{}", JacksonUtils.writeValueAsString(updateReqDTO));
        Boolean success = debtUnitRecordService.updateDebtUnit(updateReqDTO);
        if (Boolean.TRUE.equals(success) && Objects.equals(BaseDeviceTypeEnum.All_IN_ONE.getCode(), updateReqDTO.getDeviceType())) {
            // 挂账还款单打印
            UserContext userContext = UserContextUtils.get();
            debtRepaymentExecutor.execute(() -> {
                UserContextUtils.put(userContext);
                dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
                debtUnitRecordPrintService.print(updateReqDTO);
                UserContextUtils.remove();
            });
        }
        return success;
    }

    @PostMapping("/unit/debt_repayment_record_print")
    @ApiOperation(value = "挂账记录重打印")
    public Boolean debtRepaymentRecordPrint(@RequestBody DebtUnitRecordUpdateReqDTO updateReqDTO) {
        log.info("挂账记录重打印入参：{}", JacksonUtils.writeValueAsString(updateReqDTO));
        // 挂账还款单打印
        debtUnitRecordPrintService.print(updateReqDTO);
        return Boolean.TRUE;
    }

    @GetMapping("/unit/query_debt_unit_list")
    @ApiOperation(value = "查询挂账单位")
    public List<DebtUnitDropdownListDTO> queryDebtUnitList() {
        return debtUnitService.unitDropdownList();
    }

    @GetMapping("/unit/query_debt_unit_total_by_guid")
    @ApiOperation(value = "根据单位Guid查询挂账单位汇总")
    public DebtUnitRecordTotalDTO queryDebtUnitTotalByUnitGuid(@RequestParam("unitGuid") String unitGuid) {
        log.info("查询挂账单位汇总接口入参：{}", unitGuid);
        return debtUnitRecordService.queryDebtUnitTotalByUnitGuid(unitGuid);
    }

    @PostMapping("/unit/h5login")
    @ApiOperation(value = "h5挂账页面登录")
    public Boolean h5Login(@Validated @RequestBody DebtUnitLoginH5ReqDTO reqDTO) {
        log.info("H5页面查询登录入参：{}", JSON.toJSONString(reqDTO));
        return debtUnitService.h5Login(reqDTO);
    }

    @PostMapping("/record/h5query")
    @ApiOperation(value = "H5查询挂账还款记录")
    public DebtRecordH5RespDTO queryDebtRecordH5(@Validated @RequestBody DebtUnitLoginH5ReqDTO reqDTO) {
        log.info("H5查询挂账还款记录入参：{}", JSON.toJSONString(reqDTO));
        return debtUnitRecordService.queryDebtRecordH5(reqDTO);
    }
}
