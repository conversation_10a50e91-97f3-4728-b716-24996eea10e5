package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestDiscount;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseDiscount;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-26
 * @description
 */
@Component
@Slf4j
@AllArgsConstructor
public class MemberDiscountHandler extends DiscountHandler{

    private final MemberTerminalClientService memberTerminalClientService;
    @Override
    void dealDiscount(DiscountContext context) {
        if(context.isRejectDiscount()){
            return;
        }
        // 9.会员折扣
        DiscountFeeDetailDTO member = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap()
                .get(DiscountTypeEnum.MEMBER.getCode()));
        if (context.isHasMember() && CommonUtil.hasGuid(context.getOrderDO().getMemberCardGuid()) && !context.isIntegralStore() && context.isMemberDiscount()) {
            RequestDiscount discountReqDTO = new RequestDiscount();

            discountReqDTO.setVolumeCode(context.getBillCalculateReqDTO().getVolumeCode());
            discountReqDTO.setMemberConsumptionGuid(context.getOrderDO().getMemberConsumptionGuid());
            discountReqDTO.setMemberInfoCardGuid(context.getOrderDO().getMemberCardGuid());
            BigDecimal payMoney = context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(context.getOrderDetailRespDTO().getAppendFee());
            discountReqDTO.setPayMoney(BigDecimalUtil.greaterThanZero(payMoney) ? payMoney : BigDecimal.ZERO);
            discountReqDTO.setTotalMoney(context.getOrderDetailRespDTO().getOrderFee());
            discountReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            discountReqDTO.setRequestDishInfoList(CommonUtil.dineInItem2DishList(context.getAllItems()));
            log.info("4.会员折扣请求：{}，memberInfoCardGuid：{}", JacksonUtils.writeValueAsString(discountReqDTO),
                    context.getOrderDO().getMemberCardGuid());
            ResponseDiscount discount = memberTerminalClientService.discount(discountReqDTO);
            log.info("4.会员折扣返回：{}", JacksonUtils.writeValueAsString(discount));

            Map<String, DineInItemDTO> itemDTOMap = context.getAllItems().stream()
                    .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
            discount.getRequestDishInfoList().forEach(dish -> {
                if (dish.getDiscountMoney() != null) {
                    DineInItemDTO dineInItemDTO = itemDTOMap.get(dish.getOrderItemGuid());
                    if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                        dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(dish.getDiscountMoney()));
                    }
                }
            });
            // singleItemDiscount(dineInItemDTOMap, member, discount.getRequestDishInfoList());
            // 会员折扣从返回的数据里去，从商品数据里取有问题，没有计算第三方活动
            memberDiscount(context.getDineInItemDTOMap(), member, discount);

            context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(member
                    .getDiscountFee()));
            context.getOrderDO().setMemberConsumptionGuid(discount.getMemberConsumptionGuid());
            log.info("4.会员折扣计算后订单剩余金额：{}，优惠金额：{}，菜品优惠：{}", context.getOrderDetailRespDTO().getOrderSurplusFee(), member
                    .getDiscountFee(), JacksonUtils.writeValueAsString(context.getDineInItemDTOMap()));
        } else {
            member.setDiscountFee(BigDecimal.ZERO);
        }
        context.getDiscountFeeDetailDTOS().add(member);
    }

    /**
     * 商品优惠价处理
     */
    private void handleDiscountTotalPrice(DiscountContext context, RequestDiscount discountReqDTO) {
        discountReqDTO.setRequestDishInfoList(CommonUtil.dineInItem2DishListByDiscount(context.getAllItems()));
        log.info("[商品优惠价处理]会员折扣请求：{}，memberInfoCardGuid：{}", JacksonUtils.writeValueAsString(discountReqDTO),
                context.getOrderDO().getMemberCardGuid());
        ResponseDiscount discountByDiscount = memberTerminalClientService.discount(discountReqDTO);
        log.info("[商品优惠价处理]会员折扣返回：{}", JacksonUtils.writeValueAsString(discountByDiscount));
        Map<String, DineInItemDTO> itemDTOMap = context.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (v1, v2) -> v1));
        discountByDiscount.getRequestDishInfoList().forEach(dish->{
            if (dish.getDiscountMoney() != null) {
                DineInItemDTO dineInItemDTO = itemDTOMap.get(dish.getOrderItemGuid());
                if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                    dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(dish.getDiscountMoney()));
                }
            }
        });
    }

    private void memberDiscount(Map<String, DineInItemDTO> dineInItemDTOMap, DiscountFeeDetailDTO member,
                                ResponseDiscount discount) {
        BigDecimal discountFee = discount.getDeductionMoney();
        for (RequestDishInfo dishInfoDTO : discount.getRequestDishInfoList()) {
            if (dishInfoDTO.getDiscountMoney() != null) {
                DineInItemDTO dineInItemDTO = dineInItemDTOMap.get(dishInfoDTO.getOrderItemGuid());
                if (dineInItemDTO != null && dishInfoDTO.getDiscountMoney() != null) {
                    dineInItemDTO.setTotalDiscountFee(dineInItemDTO.getTotalDiscountFee().add(dishInfoDTO.getDiscountMoney()));
                }
            }
        }
        member.setDiscountFee(BigDecimalUtil.setScale2(discountFee));
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.MEMBER.getCode();
    }
}
