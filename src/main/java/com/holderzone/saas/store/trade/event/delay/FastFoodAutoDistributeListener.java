package com.holderzone.saas.store.trade.event.delay;

import com.holderzone.saas.store.dto.weixin.deal.FastFoodAutoDistributeTaskDTO;
import com.holderzone.saas.store.trade.service.KdsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 快餐自动出餐延迟队列
 * @date 2021/8/31 11:51
 * @className:
 */
@Component
@Slf4j
public class FastFoodAutoDistributeListener implements RedisDelayedQueueListener<FastFoodAutoDistributeTaskDTO> {

    @Resource
    KdsService kdsService;

    @Override
    public void invoke(FastFoodAutoDistributeTaskDTO taskDTO) {
        log.info("[快餐自动出餐延迟队列],taskDTO={}", taskDTO);
        kdsService.dealFastFoodAutoDistributeDelayedTask(taskDTO);
    }
}
