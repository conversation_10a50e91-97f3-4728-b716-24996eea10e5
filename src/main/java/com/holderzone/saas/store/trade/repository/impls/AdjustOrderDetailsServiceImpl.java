package com.holderzone.saas.store.trade.repository.impls;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDetailsDO;
import com.holderzone.saas.store.trade.mapper.AdjustOrderDetailsMapper;
import com.holderzone.saas.store.trade.repository.interfaces.AdjustOrderDetailsService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 调整单明细 service
 */
@Service
public class AdjustOrderDetailsServiceImpl extends ServiceImpl<AdjustOrderDetailsMapper, AdjustOrderDetailsDO> implements AdjustOrderDetailsService {

    @Override
    public List<AdjustOrderDetailsDO> listByAdjustGuid(Long adjustGuid) {
        LambdaQueryWrapper<AdjustOrderDetailsDO> qw = new LambdaQueryWrapper<>();
        qw.eq(AdjustOrderDetailsDO::getAdjustGuid, adjustGuid);
        return list(qw);
    }

    @Override
    public List<AdjustOrderDetailsDO> listByOrderItemGuids(List<Long> orderItemGuids) {
        LambdaQueryWrapper<AdjustOrderDetailsDO> qw = new LambdaQueryWrapper<>();
        qw.in(AdjustOrderDetailsDO::getOrderItemGuid, orderItemGuids);
        return list(qw);
    }
}
