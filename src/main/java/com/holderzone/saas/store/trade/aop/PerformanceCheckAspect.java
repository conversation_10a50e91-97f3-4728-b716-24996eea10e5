package com.holderzone.saas.store.trade.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderPessimismLockAspect
 * @date 2019/09/16 16:27
 * @description //悲观锁，为了加入Order使切口有序，移动原来的逻辑到此
 * @program IdeaProjects
 */
@Aspect
@Component
@Slf4j
public class PerformanceCheckAspect {




    @Pointcut("@annotation(com.holderzone.saas.store.trade.anno.PerformanceCheck)")
    public void performanceCheck() {

    }
    /**
     * 性能时间
     */
    @Around("performanceCheck()")
    public Object performanceCheckAround(ProceedingJoinPoint point) throws Throwable {
        String methodName =  point.getSignature().getDeclaringTypeName();
        long  start = System.currentTimeMillis();
        try {
            return  point.proceed();
        }finally {
            log.info("方法 {} 耗时 {} ms ",methodName, System.currentTimeMillis()-start);
        }

    }


}