package com.holderzone.saas.store.trade.repository.impls;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderRespDTO;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDO;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDetailsDO;
import com.holderzone.saas.store.trade.entity.domain.ItemAttrDO;
import com.holderzone.saas.store.trade.helper.PageAdapter;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.mapper.AdjustOrderMapper;
import com.holderzone.saas.store.trade.repository.interfaces.AdjustOrderDetailsService;
import com.holderzone.saas.store.trade.repository.interfaces.AdjustOrderService;
import com.holderzone.saas.store.trade.repository.interfaces.ItemAttrService;
import com.holderzone.saas.store.trade.transform.AdjustOrderTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 调整单 service
 */
@Slf4j
@Service
@AllArgsConstructor
public class AdjustOrderServiceImpl extends ServiceImpl<AdjustOrderMapper, AdjustOrderDO> implements AdjustOrderService {

    private final ItemAttrService itemAttrService;

    private final AdjustOrderDetailsService adjustOrderDetailsService;

    private final RedisHelper redisHelper;

    public static AdjustOrderTransform adjustOrderTransform = AdjustOrderTransform.INSTANCE;

    @Override
    public AdjustOrderDO getByGuid(Long guid) {
        LambdaQueryWrapper<AdjustOrderDO> qw = new LambdaQueryWrapper<>();
        qw.eq(AdjustOrderDO::getGuid, guid);
        return getOne(qw);
    }

    @Override
    public AdjustOrderDO top1ByOrderGuid(Long orderGuid) {
        LambdaQueryWrapper<AdjustOrderDO> qw = new LambdaQueryWrapper<>();
        qw.eq(AdjustOrderDO::getOrderGuid, orderGuid);
        qw.orderByDesc(AdjustOrderDO::getGmtCreate);
        qw.last("limit 0,1");
        return getOne(qw);
    }

    @Override
    public AdjustOrderDetailRespDTO getSingleOrderDetail(AdjustOrderQueryDTO queryDTO) {
        AdjustOrderDO order = null;
        if (Objects.nonNull(queryDTO.getAdjustOrderGuid())) {
            order = getByGuid(queryDTO.getAdjustOrderGuid());
        }
        if (Objects.nonNull(queryDTO.getOrderGuid())) {
            order = top1ByOrderGuid(queryDTO.getOrderGuid());
        }
        if (order == null) {
            throw new ParameterException("没有该调整单的记录");
        }
        List<AdjustOrderDetailsDO> details = adjustOrderDetailsService.listByAdjustGuid(order.getGuid());
        Set<Long> detailGuids = details.stream().map(AdjustOrderDetailsDO::getGuid).collect(Collectors.toSet());

        AdjustOrderDetailRespDTO adjustOrderDetailRespDTO = adjustOrderTransform.adjustOrderDO2AdjustOrderDetailRespDTO(order);

        List<ItemAttrDO> itemAttrDOList = new ArrayList<>(itemAttrService.listByItemGuids(new ArrayList<>(detailGuids)));
        List<DineInItemDTO> adjustItemList = AmountCalculationUtil.buildAdjustItem(details, itemAttrDOList);
        adjustOrderDetailRespDTO.setAdjustItemList(adjustItemList);
        return adjustOrderDetailRespDTO;
    }

    @Override
    public void saveOrder(AdjustOrderDO adjustOrderDO) {
        String storeGuid = UserContextUtils.getStoreGuid();
        String adjustNo = redisHelper.generateAdjustNo(storeGuid);
        adjustOrderDO.setAdjustNo(adjustNo);
        save(adjustOrderDO);
    }

    @Override
    public List<Long> listByOrderGuids(List<Long> orderGuids) {
        if (CollectionUtils.isEmpty(orderGuids)) {
            return Lists.newArrayList();
        }
        return baseMapper.listByOrderGuids(orderGuids);
    }


    /**
     * 调整单-分页列表
     *
     * @param queryDTO 门店guid
     * @return 调整单列表
     */
    @Override
    public Page<AdjustOrderRespDTO> pageAdjustOrder(AdjustOrderQueryDTO queryDTO) {
        if (ObjectUtils.isEmpty(UserContextUtils.getStoreGuid())) {
            throw new BusinessException("头部门店guid不能为空");
        }
        // 忽略第一的最大id
        if (Objects.equals(1, queryDTO.getCurrentPage())) {
            queryDTO.setMaxId(null);
        }
        PageAdapter<AdjustOrderDO> page = this.page(new PageAdapter<>(queryDTO), new LambdaQueryWrapper<AdjustOrderDO>()
                .eq(AdjustOrderDO::getStoreGuid, UserContextUtils.getStoreGuid())
                .eq(AdjustOrderDO::getIsDelete, Boolean.FALSE)
                .le(!ObjectUtils.isEmpty(queryDTO.getMaxId()), AdjustOrderDO::getId, queryDTO.getMaxId())
                .orderByDesc(AdjustOrderDO::getGmtCreate)
        );
        List<AdjustOrderDO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            log.warn("未查询到门店调整单 storeGuid={}", UserContextUtils.getStoreGuid());
            return new PageAdapter<>(page.getSize(), page.getCurrent(), page.getTotal());
        }
        return new PageAdapter<>(page, adjustOrderTransform.adjustOrderDOs2AdjustOrderDTOs(records));
    }
}
