package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.trade.HandoverDTO;
import com.holderzone.saas.store.trade.entity.domain.TransactionRecordDO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单交易记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface TransactionRecordMapper extends BaseMapper<TransactionRecordDO> {

    List<HandoverDTO> handover(@Param("dto") HandoverPayQueryDTO request);

    BigDecimal calExcessAmount(@Param("dto") HandoverPayQueryDTO request);

    /**
     * 查询交接班中涉及的订单guids
     */
    List<String> handoverOrderGuids(@Param("dto") HandoverPayQueryDTO request);

    @Deprecated
    Integer handoverOrderCount(@Param("dto") HandoverPayQueryDTO request);

    List<HandoverDTO> retailHandover(@Param("dto") HandoverPayQueryDTO request);
}
