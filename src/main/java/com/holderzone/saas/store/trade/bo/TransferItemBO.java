package com.holderzone.saas.store.trade.bo;

import com.holderzone.saas.store.dto.trade.OrderItemDTO;
import com.holderzone.saas.store.trade.entity.domain.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 转菜bo
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "转菜bo")
public class TransferItemBO implements Serializable {

    private static final long serialVersionUID = 1580199351156418574L;

    @ApiModelProperty(value = "原订单guid")
    private String oldOrderGuid;

    @ApiModelProperty(value = "原订单")
    private OrderDO oldOrderDO;

    @ApiModelProperty(value = "新订单guid")
    private String newOrderGuid;

    @ApiModelProperty(value = "新订单")
    private OrderDO newOrderDO;

    @ApiModelProperty(value = "转菜数量")
    private Map<String, BigDecimal> transferItemMap;

    @ApiModelProperty(value = "原订单商品")
    private List<OrderItemDO> oldOrderItemDOList;

    @ApiModelProperty(value = "新订单商品")
    private List<OrderItemDO> newOrderItemDOList;

    @ApiModelProperty(value = "原订单商品属性")
    private List<ItemAttrDO> oldItemAttrDOList;

    @ApiModelProperty(value = "新订单商品属性")
    private List<ItemAttrDO> newItemAttrDOList;

    @ApiModelProperty(value = "原订单商品记录")
    private List<OrderItemRecordDO> oldItemRecordDOList;

    @ApiModelProperty(value = "新订单商品记录")
    private List<OrderItemRecordDO> newItemRecordDOList;

    @ApiModelProperty(value = "新旧对照map")
    private HashMap<Long, OrderItemDTO> old2NewMap;

    @ApiModelProperty(value = "原订单商品拓展")
    private List<OrderItemExtendsDO> oldItemExtendsDOList;

    @ApiModelProperty(value = "新订单商品拓展")
    private List<OrderItemExtendsDO> newItemExtendsDOList;

    @ApiModelProperty(value = "套餐子菜换菜数据")
    private List<OrderItemChangesDO> updateItemChangesList;

}
