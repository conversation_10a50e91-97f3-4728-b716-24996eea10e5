package com.holderzone.saas.store.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.PriceChangeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.item.*;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInItemService
 * @date 2018/09/04 16:08
 * @description 订单菜品操作服务类
 * @program holder-saas-store-trade
 */
public interface DineInItemService extends IService<OrderItemDO> {

    /**
     * 批量新增菜品（正餐，快餐，微信）
     *
     * @param createDineInOrderReqDTO
     * @return
     */
    EstimateItemRespDTO addItems(CreateDineInOrderReqDTO createDineInOrderReqDTO, Boolean isFastFood);


    /**
     * 批量新增菜品（正餐，快餐，微信）
     *
     * @param createDineInOrderReqDTO
     * @return
     */
    EstimateItemRespDTO batchAddItems(CreateDineInOrderReqDTO createDineInOrderReqDTO, Boolean isFastFood);

    /**
     * 赠、退菜
     *
     * @param batchItemReturnOrFreeReqDTO
     * @param isReturn
     * @param aFalse
     * @return
     */
    BatchItemReturnOrFreeReqDTO returnOrFreeItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO, Boolean
            isReturn, Boolean isCheck);

    Boolean cancelFree(CancelFreeItemReqDTO cancelFreeItemReqDTO);

    Boolean batchServeItem(ServeItemReqDTO serveItemReqDTO);

    /**
     * 批量催菜
     *
     * @param updateItemStateReqDTO
     * @return
     */
    Boolean batchUrgeItem(UpdateItemStateReqDTO updateItemStateReqDTO);

    /**
     * 批量叫起
     *
     * @param updateItemStateReqDTO
     * @return
     */
    Boolean callUpItem(UpdateItemStateReqDTO updateItemStateReqDTO);

    Boolean changePrice(PriceChangeItemReqDTO priceChangeItemReqDTO);

    /**
     * 根据pad下单guid查询下单商品
     *
     * @param padOrderGuid pad下单guid
     * @return 商品列表
     */
    List<DineInItemDTO> queryItemByPadGuid(String padOrderGuid);

    /**
     * 根据订单guid查询订单商品
     *
     * @param orderGuid 订单guid
     * @return 订单商品
     */
    List<DineInItemDTO> queryItemByOrderGuid(String orderGuid);

    /**
     * 套餐子菜品换菜
     */
    EstimateItemRespDTO changeGroupItem(ChangeGroupItemReqDTO changeGroupItemReqDTO);

    /**
     * 撤销套餐子菜换菜
     */
    EstimateItemRespDTO cancelChangeGroupItem(ChangeGroupItemReqDTO changeGroupItemReqDTO);

    List<DineInItemDTO> queryItemByGuid(SingleListDTO req);

    /**
     * 转菜
     */
    void transferItem(TransferItemReqDTO transferReq);

}
