package com.holderzone.saas.store.trade.strategy.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.trade.bo.StockErpBO;
import com.holderzone.saas.store.enums.trade.StockReduceStrategyEnum;
import com.holderzone.saas.store.trade.repository.feign.ErpClientService;
import com.holderzone.saas.store.trade.strategy.StockReduceStrategy;
import com.holderzone.saas.store.trade.utils.ItemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * ERP库存扣减策略实现
 */
@Slf4j
@Component
public class ErpStockReduceStrategy implements StockReduceStrategy {

    @Resource
    private ErpClientService erpClientService;

    @Override
    public String getStrategyName() {
        return StockReduceStrategyEnum.STORE_ERP.getCode();
    }

    @Override
    public boolean isSupport() {
        return true;
    }

    @Override
    public void reduceStock(StockErpBO stockErpBO) {
        List<DineInItemDTO> items = stockErpBO.getItems();
        String orderGuid = stockErpBO.getOrderGuid();
        try {
            OrderSkuDTO erpDTO = ItemUtil.getErpDTO(items);
            erpDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            erpDTO.setOperatorGuid(UserContextUtils.getUserGuid());
            erpDTO.setOperatorName(UserContextUtils.getUserName());
            erpDTO.setStoreGuid(UserContextUtils.getStoreGuid());

            log.info("调用ERP扣减库存：订单guid：{}，入参{}",
                    orderGuid,
                    JacksonUtils.writeValueAsString(erpDTO));

            erpClientService.reduceStockForOrder(erpDTO);
        } catch (Exception e) {
            log.error("调用ERP扣减库存异常：{}", e.getMessage(), e);
        }
    }

    @Override
    public void returnStock(StockErpBO stockErpBO) {
        List<DineInItemDTO> items = stockErpBO.getItems();
        String orderGuid = stockErpBO.getOrderGuid();
        try {
            OrderSkuDTO erpDTO = ItemUtil.getErpDTO(items, false);
            erpDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            erpDTO.setOperatorGuid(UserContextUtils.getUserGuid());
            erpDTO.setOperatorName(UserContextUtils.getUserName());
            erpDTO.setStoreGuid(UserContextUtils.getStoreGuid());

            log.info("调用ERP回退库存：订单guid：{}，入参{}",
                    orderGuid,
                    JacksonUtils.writeValueAsString(erpDTO));

            erpClientService.reduceStockForOrder(erpDTO);
        } catch (Exception e) {
            log.error("调用ERP回退库存异常：{}", e.getMessage(), e);
        }
    }

    @Override
    public void adjustStock(StockErpBO stockErpBO) {
        log.warn("erp暂不支持调整单");
    }
} 