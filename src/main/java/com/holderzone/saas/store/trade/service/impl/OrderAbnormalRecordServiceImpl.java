package com.holderzone.saas.store.trade.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayReserveResultDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.dto.trade.OrderAbnormalListReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordRespDTO;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.OrderAbnormalRecordDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.TransactionRecordDO;
import com.holderzone.saas.store.trade.entity.enums.*;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.mapper.OrderAbnormalRecordMapper;
import com.holderzone.saas.store.trade.repository.feign.AggPayClientService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.repository.interfaces.TransactionRecordService;
import com.holderzone.saas.store.trade.service.BillService;
import com.holderzone.saas.store.trade.service.OrderAbnormalRecordService;
import com.holderzone.saas.store.trade.service.RedisService;
import com.holderzone.saas.store.trade.service.TableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.alibaba.fastjson.JSON.parseObject;

@Service
@Slf4j
public class OrderAbnormalRecordServiceImpl extends ServiceImpl<OrderAbnormalRecordMapper, OrderAbnormalRecordDO>
        implements OrderAbnormalRecordService {

    @Resource
    private OrderAbnormalRecordMapper orderAbnormalRecordMapper;
    @Resource
    private OrderService orderService;
    @Resource
    private DynamicHelper dynamicHelper;
    @Resource
    private AggPayClientService aggPayClientService;
    @Resource
    private RedisService redisService;
    @Resource
    private TransactionRecordService transactionRecordService;
    @Resource
    private BillService billService;
    @Resource
    private TableService tableService;


    /**
     * 新增异常订单记录
     *
     * @param orderAbnormalRecordReqDTO
     * @return 0保存成功，1保存失败，2支付成功，3支付失败
     */
    @Override
    public Integer saveAbnormalRecordOrder(OrderAbnormalRecordReqDTO orderAbnormalRecordReqDTO) {
        String orderGuid = orderAbnormalRecordReqDTO.getOrderGuid();
        //获取支付结果
        SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setPayGuid(orderAbnormalRecordReqDTO.getPayGuid());
        saasPollingDTO.setOrderGuid(orderAbnormalRecordReqDTO.getOrderGuid());
        saasPollingDTO.setStoreGuid(orderAbnormalRecordReqDTO.getStoreGuid());
        saasPollingDTO.setEnterpriseGuid(orderAbnormalRecordReqDTO.getEnterpriseGuid());
        AggPayPollingRespDTO payPollingRespDTO = aggPayClientService.queryPaySt(saasPollingDTO);
        //支付结果不为支付中时，返回支付结果
        if (!AggPayStateEnum.PENDING.getCode().equals(payPollingRespDTO.getPaySt())) {
            if (AggPayStateEnum.SUCCESS.getCode().equals(payPollingRespDTO.getPaySt())) {
                return ResultEnum.PAY_SUCCESS.getCode();
            }
            if (AggPayStateEnum.FAILURE.getCode().equals(payPollingRespDTO.getPaySt())) {
                return ResultEnum.PAY_FAIL.getCode();
            }
        }
        OrderDO orderDO = orderService.getByIdWithCache(orderGuid);
        int count = this.count(new QueryWrapper<OrderAbnormalRecordDO>().lambda()
                .eq(OrderAbnormalRecordDO::getOrderGuid, orderGuid).eq(OrderAbnormalRecordDO::getIsDelete, false));
        if (count > 0) {
            return ResultEnum.SAVE_SUCCESS.getCode();
        }
        OrderAbnormalRecordDO orderAbnormalRecordDO = new OrderAbnormalRecordDO();
        orderAbnormalRecordDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER_ABNORMAL_RECORD));
        orderAbnormalRecordDO.setGmtCreate(LocalDateTime.now());
        orderAbnormalRecordDO.setGmtModified(LocalDateTime.now());
        orderAbnormalRecordDO.setOrderGuid(orderGuid);
        orderAbnormalRecordDO.setIsDelete(Boolean.FALSE);
        orderAbnormalRecordDO.setPaymentStatu(TradeStateEnum.PENDING.getCode());
        orderAbnormalRecordDO.setDiningMethodId(orderDO.getTradeMode());
        orderAbnormalRecordDO.setPayGuid(orderAbnormalRecordReqDTO.getPayGuid());
        List<TransactionRecordDO> transactionRecordDOS = transactionRecordService.list(new LambdaQueryWrapper<TransactionRecordDO>()
                .eq(TransactionRecordDO::getOrderGuid, orderGuid)
                .orderByDesc(TransactionRecordDO::getGmtCreate)
        );
        orderAbnormalRecordDO.setCheckoutTime(transactionRecordDOS.get(0).getCreateTime());
        //通过redis获取支付方式id,支付方式名称，支付金额
        OrderAbnormalRecordDO abnormalOrderRecord = redisService.getAbnormalOrderRecord(orderGuid);
        if (Objects.nonNull(abnormalOrderRecord)) {
            orderAbnormalRecordDO.setPaymentAmount(abnormalOrderRecord.getPaymentAmount());
            orderAbnormalRecordDO.setPaymentMethodId(abnormalOrderRecord.getPaymentMethodId());
            orderAbnormalRecordDO.setPaymentMethodName(abnormalOrderRecord.getPaymentMethodName());
        }
        orderAbnormalRecordDO.setStoreGuid(orderDO.getStoreGuid());
        boolean save = this.save(orderAbnormalRecordDO);
        //保存成功 且桌台guid不为空，释放桌台
        if (save && Objects.nonNull(orderAbnormalRecordReqDTO.getTableGuid())) {
            //通知桌台关闭
            tableService.checkOutChange(orderAbnormalRecordReqDTO, orderDO);
        }
        return save ? ResultEnum.SAVE_SUCCESS.getCode() : ResultEnum.SAVE_FAIL.getCode();
    }

    /**
     * 查询异常订单列表
     *
     * @param orderAbnormalListReqDTO 可选条件：开始时间，结束时间，桌号|牌号|订单号
     * @return
     */
    @Override
    public Page<OrderAbnormalRecordRespDTO> listAbnormalOrders(OrderAbnormalListReqDTO orderAbnormalListReqDTO) {
        Page<OrderAbnormalRecordRespDTO> pageResult = new Page<>(orderAbnormalListReqDTO.getCurrentPage(), orderAbnormalListReqDTO.getPageSize());
        List<OrderAbnormalRecordRespDTO> orderAbnormalRecordRespDTOS = orderAbnormalRecordMapper.listAbnormalOrders(orderAbnormalListReqDTO);
        orderAbnormalRecordRespDTOS.stream()
                .forEach(e -> e.setDiningMethodName(DiningMethodEnum.getDesc(e.getDiningMethodId())));
        pageResult.setTotalCount(orderAbnormalRecordRespDTOS.size());
        pageResult.setData(orderAbnormalRecordRespDTOS);
        return pageResult;
    }

    /**
     * 查询支付结果
     *
     * @param saasPollingDTO
     * @return
     */
    @Override
    public AggPayPollingRespDTO getPaymentResult(SaasPollingDTO saasPollingDTO) {
        OrderDO orderDO = orderService.getById(saasPollingDTO.getOrderGuid());
        AggPayPollingRespDTO payPollingRespDTO = new AggPayPollingRespDTO();
        payPollingRespDTO.setPayGUID(saasPollingDTO.getPayGuid());
        payPollingRespDTO.setOrderGUID(saasPollingDTO.getOrderGuid());
        // 当存在支付结果时
        if (orderDO.getState() == StateEnum.SUCCESS.getCode()) {
            log.info("订单{}状态为成功", JacksonUtils.writeValueAsString(orderDO));
            payPollingRespDTO.setCode("10000");
            payPollingRespDTO.setPaySt(AggPayStateEnum.SUCCESS.getCode());
            payPollingRespDTO.setBillPleaseState(ResultEnum.CHECKOUT_SUCCESS.getCode());
            baseMapper.deleteRecord(saasPollingDTO.getOrderGuid());
            return payPollingRespDTO;
        }
        if (orderDO.getState() == StateEnum.FAILURE.getCode()) {
            log.info("订单{}状态为失败", JacksonUtils.writeValueAsString(orderDO));
            payPollingRespDTO.setCode("10000");
            payPollingRespDTO.setPaySt(AggPayStateEnum.FAILURE.getCode());
            payPollingRespDTO.setBillPleaseState(ResultEnum.CHECKOUT_FAIL.getCode());
            baseMapper.deleteRecord(saasPollingDTO.getOrderGuid());
            return payPollingRespDTO;
        }
        //获取支付结果
        payPollingRespDTO = aggPayClientService.queryPaySt(saasPollingDTO);
        log.info("获取支付结果：{}", JacksonUtils.writeValueAsString(payPollingRespDTO));
        //状态为支付成功，调用回调接口(里面包含状态修改和打印)
        if (    AggPayStateEnum.SUCCESS.getCode().equals(payPollingRespDTO.getPaySt()) ||
                AggPayStateEnum.FAILURE.getCode().equals(payPollingRespDTO.getPaySt()) ||
                AggPayStateEnum.REFUND.getCode().equals(payPollingRespDTO.getPaySt()) ||
                AggPayStateEnum.CLOSED.getCode().equals(payPollingRespDTO.getPaySt()) ||
                AggPayStateEnum.CANCEL.getCode().equals(payPollingRespDTO.getPaySt())
        ) {
            SaasNotifyDTO saasNotifyDTO = getSaasNotifyDTO(saasPollingDTO, payPollingRespDTO);
            billService.aggCallBack(saasNotifyDTO);
            orderDO = orderService.getById(saasPollingDTO.getOrderGuid());
            //回调成功后查询订单状态，支付成功=结账成功，其他都是结账失败
            if (StateEnum.SUCCESS.getCode() == (orderDO.getState())) {
                payPollingRespDTO.setBillPleaseState(ResultEnum.CHECKOUT_SUCCESS.getCode());
            } else {
                payPollingRespDTO.setBillPleaseState(ResultEnum.CHECKOUT_FAIL.getCode());
            }
            baseMapper.deleteRecord(saasPollingDTO.getOrderGuid());
        }
        return payPollingRespDTO;
    }

    private SaasNotifyDTO getSaasNotifyDTO(SaasPollingDTO saasPollingDTO, AggPayPollingRespDTO payPollingRespDTO) {
        log.info("入参打印 saasPollingDTO:{}, payPollingRespDTO:{}",
                JacksonUtils.writeValueAsString(saasPollingDTO),
                JacksonUtils.writeValueAsString(payPollingRespDTO));
        SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        saasNotifyDTO.setAggPayPollingRespDTO(payPollingRespDTO);
        if (null != payPollingRespDTO.getAmount()) {
            // 将分转为元
            payPollingRespDTO.setAmount(payPollingRespDTO.getAmount()
                    .divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
        }
        JSONObject attachDataObj = null;
        if (payPollingRespDTO.getAttachData() != null && !"未找到相应订单".equals(payPollingRespDTO.getAttachData())) {
            String attachData = payPollingRespDTO.getAttachData();
            attachDataObj = parseObject(attachData);
        }
        //bug修复：18806
        if (payPollingRespDTO.getIsLast() == null && Objects.nonNull(attachDataObj)) {
            String attachData = payPollingRespDTO.getAttachData();
            JSONObject jsonObject = parseObject(attachData);
            Boolean isLast = jsonObject.getBoolean("isLast");
            if (isLast != null) {
                payPollingRespDTO.setIsLast(isLast);
            }
        }
        BaseInfo baseInfo = new BaseInfo();
        baseInfo.setStoreGuid(saasPollingDTO.getStoreGuid());
        baseInfo.setEnterpriseGuid(saasPollingDTO.getEnterpriseGuid());
        baseInfo.setAccount(saasPollingDTO.getAccount());
        baseInfo.setUserGuid(saasPollingDTO.getUserGuid());
        baseInfo.setUserName(saasPollingDTO.getUserName());
        baseInfo.setStoreName(saasPollingDTO.getStoreName());
        baseInfo.setEnterpriseName(saasPollingDTO.getEnterpriseName());
        baseInfo.setDeviceId(saasPollingDTO.getDeviceId());
        baseInfo.setDeviceType(saasPollingDTO.getDeviceType());
        baseInfo.setRequestTimestamp(saasPollingDTO.getRequestTimestamp());
        if (Objects.nonNull(attachDataObj)) {
            baseInfo.setCloseTableFlag(attachDataObj.getInteger("closeTableFlag"));
        }
        saasNotifyDTO.setBaseInfo(baseInfo);
        return saasNotifyDTO;
    }

    /**
     * 取消支付
     *
     * @param saasPollingDTO
     * @return
     */

    /**
     * 返回支付失败，代表撤销成功
     *
     * @param saasPollingDTO
     * @return
     */
    @Override
    public AggPayReserveResultDTO cancelPayment(SaasPollingDTO saasPollingDTO) {
        AggPayReserveResultDTO aggPayReserveResultDTO = new AggPayReserveResultDTO();
        //获取支付结果
        AggPayPollingRespDTO payPollingRespDTO = getPaymentResult(saasPollingDTO);

        if (!AggPayStateEnum.SUCCESS.getCode().equals(payPollingRespDTO.getPaySt()) &&
                !AggPayStateEnum.FAILURE.getCode().equals(payPollingRespDTO.getPaySt())) {
            aggPayReserveResultDTO = aggPayClientService.reservePay(saasPollingDTO);
            log.info("撤销支付返回：{}",JacksonUtils.writeValueAsString(aggPayReserveResultDTO));
            payPollingRespDTO.setPaySt(AggPayStateEnum.CANCEL.getCode());
            SaasNotifyDTO saasNotifyDTO = getSaasNotifyDTO(saasPollingDTO, payPollingRespDTO);
            billService.aggCallBack(saasNotifyDTO);
            return aggPayReserveResultDTO;
        }
        //成功或者已经失败，则不作处理
        aggPayReserveResultDTO.setCode("10000");
        aggPayReserveResultDTO.setMsg("成功");
        aggPayReserveResultDTO.setPaySt(payPollingRespDTO.getPaySt());
        return aggPayReserveResultDTO;
    }
}
