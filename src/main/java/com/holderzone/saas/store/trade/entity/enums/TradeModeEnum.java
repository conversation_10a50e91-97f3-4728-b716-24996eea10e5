package com.holderzone.saas.store.trade.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeModeEnum
 * @date 2018/09/04 17:52
 * @description 订单交易模式枚举
 * @program holder-saas-store-trade
 */
public enum TradeModeEnum {

    DINEIN(0, "正餐"),
    FAST(1, "快餐"),;

    private int code;
    private String desc;

    TradeModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (TradeModeEnum c : TradeModeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
