package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单 扩展表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_order_extends")
public class OrderExtendsDO implements Serializable {

    private static final long serialVersionUID = -8361326058325212492L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 营业日
     */
    private LocalDate businessDay;

    /**
     * 反结账操作人guid
     */
    private String recoveryStaffGuid;

    /**
     * 反结账操作人name
     */
    private String recoveryStaffName;

    /**
     * 反结账授权操作人guid
     */
    private String recoveryAuthStaffGuid;

    /**
     * 反结账授权操作人name
     */
    private String recoveryAuthStaffName;

    /**
     * 反结账授权操作人图片
     */
    private String recoveryAuthStaffPicture;

    /**
     * 预定金支付方式,1-现金，2-聚合支付，2银行卡支付，10，其他支付方式
     * {@link PaymentTypeEnum}
     */
    private Integer reservePaymentType;

    /**
     * 预定金支付方式名称
     */
    private String reservePaymentTypeName;

    /**
     * 是否开票 1是 0否
     */
    private Boolean isInvoice;

    /**
     * 发票号码
     */
    private String invoiceCode;

    /**
     * 是否联台单
     */
    private Boolean associatedFlag;

    /**
     * 联台单编号
     */
    private String associatedSn;

    /**
     * 联台单桌台列表
     */
    private String associatedTableGuids;

    /**
     * 联台单桌台列表
     */
    private String associatedTableNames;

    /**
     * 是否多次支付
     */
    private Boolean isMultipleAggPay;

    /**
     * 麓豆会员id
     */
    private String ludouMemberGuid;

    /**
     * 麓豆会员电话
     */
    private String ludouMemberPhone;

    /**
     * 麓豆会员名字
     */
    private String ludouMemberName;

    /**
     * 总团购实际支付金额
     */
    private BigDecimal totalCouponBuyPrice;

    /**
     * 总附加费优惠金额
     */
    private BigDecimal totalAppendDiscountAmount;
}
