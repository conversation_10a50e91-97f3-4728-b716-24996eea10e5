package com.holderzone.saas.store.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.member.merchant.dto.member.ResponseOperationMemberInfo;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeWebRespDTO;
import com.holderzone.saas.store.dto.journaling.req.*;
import com.holderzone.saas.store.dto.journaling.resp.*;
import com.holderzone.saas.store.dto.order.OrderMultiMemberPayDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsReqDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsRespDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.OrderItemChangeFlagEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.config.OldCouponCalculateConfig;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.dto.BusinessHisTrendDTO;
import com.holderzone.saas.store.trade.entity.dto.MemberResult;
import com.holderzone.saas.store.trade.entity.enums.FreeReturnTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.RecoveryTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.helper.PageAdapter;
import com.holderzone.saas.store.trade.repository.feign.*;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.*;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.transform.TransactionRecordTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 商户后台数据报表订单业务服务层
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Slf4j
@Service
@AllArgsConstructor
public class BusinessOrderReportServiceImpl implements BusinessOrderReportService {

    private final OrderService orderService;

    private final OrderExtendsService orderExtendsService;

    private final TransactionRecordService transactionRecordService;

    private final OrderItemService orderItemService;

    private final ItemAttrService itemAttrService;

    private final FreeReturnItemService freeReturnItemService;

    private final DiscountService discountService;

    private final AppendFeeService appendFeeService;

    private final GrouponService grouponService;

    private final StoreClientService storeClientService;

    private final StaffClientService staffClientService;

    private final ItemClientService itemClientService;

    private final MemberMerchantClientService memberMerchantClientService;

    private final OrderItemExtendsService orderItemExtendsService;

    private final PackageSubgroupChangesService packageSubgroupChangesService;

    private final DebtUnitRecordService debtUnitRecordService;

    private final AggPayClientService aggPayClientService;

    private final OrderMultiMemberService orderMultiMemberService;

    private final IMultipleTransactionRecordService multipleTransactionRecordService;

    private final OldCouponCalculateConfig oldCouponCalculateConfig;

    /**
     * 查询商户后台/数据报表/订单统计 分页数据
     *
     * @param queryDTO 查询条件入参
     * @return 分页数据
     */
    @Override
    public Page<BusinessOrderStatisticsRespDTO> getOrderStatisticsPage(BusinessOrderStatisticsQueryDTO queryDTO) {
        String deviceTypeInSql = queryDTO.getDeviceTypeInSql();
        log.info("deviceTypeInSql={}", deviceTypeInSql);
        if (!CollectionUtils.isEmpty(queryDTO.getPaymentTypeList())) {
            handlePaymentTypeQuery(queryDTO);
        }
        Integer totalCount = orderService.getOrderStatisticsCount(queryDTO);
        if (Objects.isNull(totalCount) || totalCount == 0) {
            return new Page<>();
        }
        List<BusinessOrderStatisticsRespDTO> list = orderService.getOrderStatisticsPage(queryDTO);
        if (CollectionUtils.isEmpty(list)) {
            return new Page<>();
        }
        List<String> orderGuids = list.stream().map(BusinessOrderStatisticsRespDTO::getGuid).collect(Collectors.toList());

        // 设置订单支付方式
        boolean paymentTypeContainFee = queryDTO.getPaymentTypeContainsFee() == null || queryDTO.getPaymentTypeContainsFee();
        setTransaction(list, paymentTypeContainFee);

        Map<Long, List<GrouponOrderDTO>> grouponOrderMap = Maps.newHashMap();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderGuids)) {
            // 查询团购和第三方活动支付金额
            List<GrouponOrderDTO> grouponOrderList = grouponService.useGrouponTotalAmountByOrderGuids(orderGuids);
            grouponOrderMap = grouponOrderList.stream()
                    .collect(Collectors.groupingBy(GrouponOrderDTO::getOrderGuid));
        }
        Map<Long, List<GrouponOrderDTO>> finalGrouponOrderMap = grouponOrderMap;
        List<BusinessOrderStatisticsRespDTO> data = list.stream().peek(e -> {
            handleHistoryData(e);
            e.setOrderSource(explainDeviceType(Integer.valueOf(e.getOrderSource())));
            e.setOrderState(explainOrderState(Integer.valueOf(e.getOrderState())));
            e.setTradeMode(explainTradeMode(Integer.valueOf(e.getTradeMode())));
            appendGroupBuyAmount(e, finalGrouponOrderMap, paymentTypeContainFee);
            // 已结账订单产生退款的销售净额需减去退款金额，操作有退款信息
            if (RecoveryTypeEnum.GENERAL.getCode() == e.getRecoveryType() && !StringUtils.isEmpty(e.getRefundOrderGuid())) {
                e.setRecoveryType(RecoveryTypeEnum.ORIGINAL.getCode());
            }
        }).collect(Collectors.toList());
        log.warn("[getOrderStatisticsPage]data={}", JacksonUtils.writeValueAsString(data));
        return new Page<>(queryDTO.getCurrentPage(), queryDTO.getPageSize(), totalCount, data);
    }

    private void handlePaymentTypeQuery(BusinessOrderStatisticsQueryDTO queryDTO) {
        List<GroupBuyTypeEnum> excludePaymentTypes = Lists.newArrayList(GroupBuyTypeEnum.MEI_TUAN, GroupBuyTypeEnum.DOU_YIN,
                GroupBuyTypeEnum.ALIPAY, GroupBuyTypeEnum.ABC);
        List<String> paymentTypeList = queryDTO.getPaymentTypeList();
        List<Integer> grouponTypeList = new ArrayList<>();
        paymentTypeList.removeIf(paymentType -> {
            for (GroupBuyTypeEnum excludePaymentType : excludePaymentTypes) {
                if (excludePaymentType.getDesc().equals(paymentType)) {
                    grouponTypeList.add(excludePaymentType.getCode());
                    return true;
                }
            }
            return false;
        });
        if (!CollectionUtils.isEmpty(grouponTypeList)) {
            queryDTO.setGrouponTypeList(grouponTypeList);
        }
    }

    private void setTransaction(List<BusinessOrderStatisticsRespDTO> orderStatisticsRespDTOS, boolean paymentTypeContainFee) {
        Map<Long, Long> orderGuidMap = Maps.newHashMap();
        List<Long> sameOrderGuids = orderStatisticsRespDTOS.stream()
                .filter(e -> UpperStateEnum.SAME_ORDER_STATE.contains(e.getIsMerge()))
                .map(e -> Long.valueOf(e.getGuid()))
                .distinct().collect(Collectors.toList());
        List<OrderDO> sameOrders = orderService.otherListByMainOrderGuids(sameOrderGuids);
        for (BusinessOrderStatisticsRespDTO dineInOrderListRespDTO : orderStatisticsRespDTOS) {
            Long orderGuid = Long.valueOf(dineInOrderListRespDTO.getGuid());
            if (!UpperStateEnum.SAME_ORDER_STATE.contains(dineInOrderListRespDTO.getIsMerge())
                    || Objects.equals(StateEnum.REFUNDED.getCode(), Integer.valueOf(dineInOrderListRespDTO.getOrderState()))) {
                orderGuidMap.put(orderGuid, orderGuid);
                continue;
            }
            // 单多结账
            List<OrderDO> sameOrderList = sameOrders.stream()
                    .filter(e -> orderGuid.equals(e.getGuid()) || orderGuid.equals(e.getMainOrderGuid()))
                    .collect(Collectors.toList());
            sameOrderList = sameOrderList.stream()
                    .filter(e -> Objects.equals(e.getState(), StateEnum.SUCCESS.getCode())
                            || Objects.equals(e.getState(), StateEnum.SUB_SUCCESS.getCode()))
                    .collect(Collectors.toList());
            // 是否存在退款单
            boolean hasRefundOrderGuid = sameOrderList.stream().anyMatch(e -> Objects.nonNull(e.getRefundOrderGuid()));
            dineInOrderListRespDTO.setRecoveryType(hasRefundOrderGuid ? 2 : dineInOrderListRespDTO.getRecoveryType());
            for (OrderDO orderDO : sameOrderList) {
                orderGuidMap.put(orderDO.getGuid(), orderGuid);
            }
            // 多单结账 实付金额
            BigDecimal totalActuallyPayFee = sameOrderList.stream().map(OrderDO::getActuallyPayFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            dineInOrderListRespDTO.setActuallyPayFee(totalActuallyPayFee.toPlainString());
        }
        setTransactionPayment(orderStatisticsRespDTOS, orderGuidMap, paymentTypeContainFee);
    }


    private void handleHistoryData(BusinessOrderStatisticsRespDTO e) {
        //===================== 处理未结账订单脏数据 =====================
        if (Objects.equals(String.valueOf(StateEnum.READY.getCode()), e.getOrderState())
                || Objects.equals(String.valueOf(StateEnum.PENDING.getCode()), e.getOrderState())
                || Objects.equals(String.valueOf(StateEnum.FAILURE.getCode()), e.getOrderState())
                || Objects.equals(String.valueOf(StateEnum.CANCEL.getCode()), e.getOrderState())) {
            e.setActuallyPayFee("0.00");
            e.setCheckoutTime(null);
            e.setCashier(null);
        }
    }

    private void setTransactionPayment(List<BusinessOrderStatisticsRespDTO> orderStatisticsRespDTOS, Map<Long, Long> orderGuidMap,
                                       boolean paymentTypeContainFee) {
        List<Integer> excludePaymentTypes = Lists.newArrayList(PaymentTypeEnum.MT_GROUPON.getCode(), GroupBuyTypeEnum.DOU_YIN.getCode(),
                GroupBuyTypeEnum.ALIPAY.getCode(), GroupBuyTypeEnum.ABC.getCode(), PaymentTypeEnum.MT_MAITON.getCode());
        List<TransactionRecordDO> transactionRecordList = transactionRecordService.listByOrderGuids(new ArrayList<>(orderGuidMap.keySet()));
        transactionRecordList = transactionRecordList.stream()
                .filter(e -> e.getTradeType() < TradeTypeEnum.GENERAL_OUT.getCode()
                        && !excludePaymentTypes.contains(e.getPaymentType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(transactionRecordList)) {
            return;
        }
        // 聚合支付 多次支付查询
        replenishBatchTransactionRecord(new ArrayList<>(orderGuidMap.keySet()), transactionRecordList);
        Map<Long, List<TransactionRecordDO>> paymentTypeNameMap = Maps.newHashMap();
        for (TransactionRecordDO transactionRecordDO : transactionRecordList) {
            Long targetOrderGuid = orderGuidMap.getOrDefault(transactionRecordDO.getOrderGuid(), transactionRecordDO.getOrderGuid());
            List<TransactionRecordDO> paymentList = paymentTypeNameMap.getOrDefault(targetOrderGuid, Lists.newArrayList());
            paymentList.add(transactionRecordDO);
            paymentTypeNameMap.put(targetOrderGuid, paymentList);
        }
        for (BusinessOrderStatisticsRespDTO dineInOrderListRespDTO : orderStatisticsRespDTOS) {
            List<TransactionRecordDO> paymentList = paymentTypeNameMap.get(Long.valueOf(dineInOrderListRespDTO.getGuid()));
            if (CollectionUtils.isEmpty(paymentList)) {
                continue;
            }
            // 支付方式
            dineInOrderListRespDTO.setPayWay(buildOrderPayWay(paymentList, paymentTypeContainFee));
            // 第三方支付方式 累加实付金额
            BigDecimal thirdActivityAmount = paymentList.stream()
                    .filter(e -> Objects.equals(PaymentTypeEnum.THIRD_ACTIVITY.getCode(), e.getPaymentType()))
                    .map(TransactionRecordDO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal actuallyPayFee = new BigDecimal(dineInOrderListRespDTO.getActuallyPayFee()).add(thirdActivityAmount);
            dineInOrderListRespDTO.setActuallyPayFee(actuallyPayFee.toPlainString());
        }
    }

    private String buildOrderPayWay(List<TransactionRecordDO> paymentList, boolean paymentTypeContainFee) {
        StringBuilder sb = new StringBuilder();
        // 合并相同的支付方式
        Map<String, TransactionRecordDO> mergePaymentMap = Maps.newHashMap();
        for (TransactionRecordDO transactionRecordDO : paymentList) {
            if (transactionRecordDO.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            TransactionRecordDO recordByPaymentTypeName = mergePaymentMap.get(transactionRecordDO.getPaymentTypeName());
            if (Objects.isNull(recordByPaymentTypeName)) {
                mergePaymentMap.put(transactionRecordDO.getPaymentTypeName(), transactionRecordDO);
            } else {
                recordByPaymentTypeName.setAmount(recordByPaymentTypeName.getAmount().add(transactionRecordDO.getAmount()));
                mergePaymentMap.put(transactionRecordDO.getPaymentTypeName(), recordByPaymentTypeName);
            }
        }
        List<TransactionRecordDO> mergePaymentList = new ArrayList<>(mergePaymentMap.values());
        for (int i = 0; i < mergePaymentList.size(); i++) {
            TransactionRecordDO transactionRecordDO = mergePaymentList.get(i);
            if (transactionRecordDO.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            sb.append(transactionRecordDO.getPaymentTypeName());
            if (paymentTypeContainFee) {
                sb.append(":").append(transactionRecordDO.getAmount()
                        .stripTrailingZeros().toPlainString());
            }
            if (i != mergePaymentList.size() - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    private void appendGroupBuyAmount(BusinessOrderStatisticsRespDTO e,
                                      Map<Long, List<GrouponOrderDTO>> finalGrouponOrderMap,
                                      boolean paymentTypeContainFee) {
        List<GrouponOrderDTO> grouponOrderList = finalGrouponOrderMap.get(Long.valueOf(e.getGuid()));
        if (CollectionUtils.isEmpty(grouponOrderList)) {
            return;
        }
        if (Objects.isNull(e.getActuallyPayFee())) {
            e.setActuallyPayFee("0.00");
        }
        // 实收金额 + 团购验券抵扣金额
        StringBuilder payWaySb = new StringBuilder(StringUtils.isEmpty(e.getPayWay()) ? Strings.EMPTY : e.getPayWay());
        BigDecimal couponAmount = BigDecimal.ZERO;
        Map<Integer, List<GrouponOrderDTO>> grouponOrderByGroupBuyTypeMap = grouponOrderList.stream()
                .collect(Collectors.groupingBy(GrouponOrderDTO::getGrouponType));
        for (Map.Entry<Integer, List<GrouponOrderDTO>> entry : grouponOrderByGroupBuyTypeMap.entrySet()) {
            Integer groupBuyType = entry.getKey();
            if (payWaySb.length() > 0) {
                payWaySb.append("、");
            }
            payWaySb.append(GroupBuyTypeEnum.groupBuyType(groupBuyType).getDesc());
            BigDecimal groupBuyTypeAmount = entry.getValue().stream().map(GrouponOrderDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (paymentTypeContainFee) {
                payWaySb.append(":").append(groupBuyTypeAmount
                        .stripTrailingZeros().toPlainString());
            }
            couponAmount = couponAmount.add(groupBuyTypeAmount);
        }
        e.setActuallyPayFee(new BigDecimal(e.getActuallyPayFee()).add(couponAmount).toPlainString());
        // 支付方式 + 美团团购
        e.setPayWay(payWaySb.toString());
    }

    /**
     * 查询商户后台/数据报表/订单统计 合计
     *
     * @param queryDTO 查询条件入参
     * @return 统计结果
     */
    @Override
    public BusinessOrderStatisticsCombinedRespDTO getOrderStatisticsCombined(BusinessOrderStatisticsQueryDTO
                                                                                     queryDTO) {
        if (!CollectionUtils.isEmpty(queryDTO.getPaymentTypeList())) {
            handlePaymentTypeQuery(queryDTO);
        }
        BusinessOrderStatisticsCombinedRespDTO orderStatisticsCombined = orderService.getOrderStatisticsCombined(queryDTO);
        BigDecimal totalCouponBuyPrice = orderService.getGrouponOrderStatisticsCombined(queryDTO);
        if (Objects.nonNull(totalCouponBuyPrice) && totalCouponBuyPrice.compareTo(BigDecimal.ZERO) > 0) {
            orderStatisticsCombined.setAggregateAmount(new BigDecimal(orderStatisticsCombined.getAggregateAmount())
                    .add(totalCouponBuyPrice).toPlainString());
        }
        return orderStatisticsCombined;
    }


    @Override
    public StoreGatherTotalRespDTO listStoreGatherBusiness(StoreGatherReportReqDTO baseReq) {
        //去判断开始和结束时间
        checkTime(baseReq.getBusinessStartDateTime(), baseReq.getBusinessEndDateTime());
        PageAdapter<StoreGatherReportRespDTO> pageAdapter = new PageAdapter<>(baseReq);
        //分页查询门店统计
        // 因bug【39174】变更规则：订单金额金额扣减团购券的抵扣金额（原来是扣减团购券的够买金额），如此才能满足bug【39174】的要求
        IPage<StoreGatherReportRespDTO> iPage = orderService.listStoreGatherBusiness(pageAdapter, baseReq);
        Page<StoreGatherReportRespDTO> page = new Page<>(iPage.getCurrent(), iPage.getSize(),
                iPage.getTotal());

        StoreGatherTotalRespDTO storeGatherTotalResp = new StoreGatherTotalRespDTO();
        //配置单条数据
        // 此处因bug【39174】涉及，不再查询团购券去减
//        List<AmountItemDTO> amountItemDTOList = grouponMpService.listRefundByRequest(baseReq);
//        Map<String, List<AmountItemDTO>> listMap = amountItemDTOList.stream().collect(Collectors.groupingBy(AmountItemDTO::getName));

        List<StoreGatherReportRespDTO> records = iPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            records = new ArrayList<>();
        }
        assemblyRecord(records, baseReq);

        page.setData(records);
        storeGatherTotalResp.setRespDataPage(page);

        return storeGatherTotalResp;
    }


    private void assemblyRecord(List<StoreGatherReportRespDTO> records, StoreGatherReportReqDTO baseReq) {
        List<QuickPayStatisticsRespDTO> quickPayList = queryQuickPayStatistics(baseReq);
        if (CollectionUtil.isEmpty(records)) {
            // 处理快速收款数据
            if (CollectionUtils.isEmpty(quickPayList)) {
                log.warn("[assemblyRecord]快速收款数据为空");
                return;
            }
            handleStoreGatherQuickPayStatistics(records, quickPayList);
            return;
        }
        Set<String> storeGuids = records.stream()
                .map(StoreGatherReportRespDTO::getStoreGuid)
                .collect(Collectors.toSet());
        List<String> quickStoreGuids = quickPayList.stream()
                .map(QuickPayStatisticsRespDTO::getStoreGuid)
                .distinct()
                .collect(Collectors.toList());
        storeGuids.addAll(quickStoreGuids);
        List<StoreDTO> storeDTOList = storeClientService.queryStoreAndBrandByIdList(new ArrayList<>(storeGuids));
        //根据门店分组
        Map<String, StoreDTO> storeMap = storeDTOList.stream()
                .collect(Collectors.toMap(StoreDTO::getGuid, o -> o));

        // 处理快速收款数据
        handleGatherQuickPayStatistics(records, quickPayList);

        for (StoreGatherReportRespDTO storeGather : records) {
            StoreDTO store = storeMap.get(storeGather.getStoreGuid());
            if (store != null) {
                storeGather.setStoreName(store.getName());
                storeGather.setBrandName(store.getBelongBrandName());
            }
            storeGather.setDiscountFee(storeGather.getOrderFee().subtract(storeGather.getActuallyPayFee()));
            storeGather.setGuestAverageFee(storeGather.getActuallyPayFee().divide(BigDecimal.valueOf(storeGather.getGuestCount()), 2, RoundingMode.HALF_UP));
            storeGather.setOrderAverageFee(storeGather.getActuallyPayFee().divide(BigDecimal.valueOf(storeGather.getOrderCount()), 2, RoundingMode.HALF_UP));
        }
    }

    private void handleGatherQuickPayStatistics(List<StoreGatherReportRespDTO> records,
                                                List<QuickPayStatisticsRespDTO> quickPayList) {
        if (CollectionUtils.isEmpty(quickPayList)) {
            log.warn("[handleGatherQuickPayStatistics]快速收款数据为空");
            return;
        }
        Map<String, List<StoreGatherReportRespDTO>> dateStoreGatherMap = records.stream()
                .collect(Collectors.groupingBy(StoreGatherReportRespDTO::getDate));
        Map<String, List<QuickPayStatisticsRespDTO>> payDateQuickPayMap = quickPayList.stream()
                .collect(Collectors.groupingBy(q -> DateTimeUtils.localDate2String(q.getPayDate(), "yyyy-MM-dd")));
        payDateQuickPayMap.forEach((payDate, payDateQuickPayList) -> {
            Map<String, List<QuickPayStatisticsRespDTO>> storeGuidQuickPayMap = payDateQuickPayList.stream()
                    .collect(Collectors.groupingBy(QuickPayStatisticsRespDTO::getStoreGuid));
            List<StoreGatherReportRespDTO> gatherReportList = dateStoreGatherMap.get(payDate);
            if (CollectionUtils.isEmpty(gatherReportList)) {
                // 新增该营业日
                storeGuidQuickPayMap.forEach((storeGuid, payStatisticsList) -> {
                    BigDecimal quickPayAmountTotal = payStatisticsList.stream()
                            .map(QuickPayStatisticsRespDTO::getAmountTotal)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    Integer quickPayOrderCount = payStatisticsList.stream()
                            .map(QuickPayStatisticsRespDTO::getOrderCount)
                            .filter(Objects::nonNull)
                            .reduce(0, Integer::sum);
                    Integer quickPayGuestCount = payStatisticsList.stream()
                            .map(QuickPayStatisticsRespDTO::getGuestCount)
                            .filter(Objects::nonNull)
                            .reduce(0, Integer::sum);
                    // 新增门店
                    StoreGatherReportRespDTO newRespDTO = new StoreGatherReportRespDTO();
                    newRespDTO.setDate(payDate);
                    newRespDTO.setStoreGuid(storeGuid);
                    newRespDTO.setActuallyPayFee(quickPayAmountTotal);
                    newRespDTO.setOrderFee(quickPayAmountTotal);
                    newRespDTO.setOrderCount(quickPayOrderCount);
                    newRespDTO.setGuestCount(quickPayGuestCount);
                    records.add(newRespDTO);
                });
            } else {
                Map<String, StoreGatherReportRespDTO> storeGatherReportMap = gatherReportList.stream()
                        .collect(Collectors.toMap(StoreGatherReportRespDTO::getStoreGuid, Function.identity(), (v1, v2) -> v1));
                storeGuidQuickPayMap.forEach((storeGuid, payStatisticsList) -> {
                    BigDecimal quickPayAmountTotal = payStatisticsList.stream()
                            .map(QuickPayStatisticsRespDTO::getAmountTotal)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    Integer quickPayOrderCount = payStatisticsList.stream()
                            .map(QuickPayStatisticsRespDTO::getOrderCount)
                            .filter(Objects::nonNull)
                            .reduce(0, Integer::sum);
                    Integer quickPayGuestCount = payStatisticsList.stream()
                            .map(QuickPayStatisticsRespDTO::getGuestCount)
                            .filter(Objects::nonNull)
                            .reduce(0, Integer::sum);
                    StoreGatherReportRespDTO respDTO = storeGatherReportMap.get(storeGuid);
                    if (ObjectUtils.isEmpty(respDTO)) {
                        // 新增门店
                        StoreGatherReportRespDTO newRespDTO = new StoreGatherReportRespDTO();
                        newRespDTO.setDate(payDate);
                        newRespDTO.setStoreGuid(storeGuid);
                        newRespDTO.setActuallyPayFee(quickPayAmountTotal);
                        newRespDTO.setOrderFee(quickPayAmountTotal);
                        newRespDTO.setOrderCount(quickPayOrderCount);
                        newRespDTO.setGuestCount(quickPayGuestCount);
                        records.add(newRespDTO);
                    } else {
                        respDTO.setActuallyPayFee(respDTO.getActuallyPayFee().add(quickPayAmountTotal));
                        respDTO.setOrderFee(respDTO.getOrderFee().add(quickPayAmountTotal));
                        respDTO.setGuestCount(respDTO.getGuestCount() + quickPayGuestCount);
                        respDTO.setOrderCount(respDTO.getOrderCount() + quickPayOrderCount);
                    }
                });
            }
        });
    }

    private void handleStoreGatherQuickPayStatistics(List<StoreGatherReportRespDTO> records,
                                                     List<QuickPayStatisticsRespDTO> quickPayList) {
        // 查询门店
        List<String> storeGuidList = quickPayList.stream()
                .map(QuickPayStatisticsRespDTO::getStoreGuid)
                .distinct()
                .collect(Collectors.toList());
        List<StoreDTO> storeDTOList = storeClientService.queryStoreAndBrandByIdList(storeGuidList);
        Map<String, StoreDTO> storeMap = storeDTOList.stream().collect(Collectors.toMap(StoreDTO::getGuid, o -> o));

        Map<LocalDate, List<QuickPayStatisticsRespDTO>> payDateQuickMap = quickPayList.stream()
                .collect(Collectors.groupingBy(QuickPayStatisticsRespDTO::getPayDate));
        for (Map.Entry<LocalDate, List<QuickPayStatisticsRespDTO>> entry : payDateQuickMap.entrySet()) {
            LocalDate payDate = entry.getKey();
            List<QuickPayStatisticsRespDTO> payDateQuickList = entry.getValue();
            StoreGatherReportRespDTO respDTO = new StoreGatherReportRespDTO();
            respDTO.setDate(DateTimeUtils.localDate2String(payDate, "yyyyMMdd"));
            Map<String, List<QuickPayStatisticsRespDTO>> storeQuickMap = payDateQuickList.stream()
                    .collect(Collectors.groupingBy(QuickPayStatisticsRespDTO::getStoreGuid));
            for (Map.Entry<String, List<QuickPayStatisticsRespDTO>> e : storeQuickMap.entrySet()) {
                String storeGuid = e.getKey();
                List<QuickPayStatisticsRespDTO> storeQuickList = e.getValue();
                StoreDTO storeDTO = storeMap.get(storeGuid);
                if (!ObjectUtils.isEmpty(storeDTO)) {
                    respDTO.setStoreGuid(storeGuid);
                    respDTO.setStoreName(storeDTO.getName());
                    respDTO.setBrandName(storeDTO.getBelongBrandName());
                }

                BigDecimal quickPayAmountTotal = storeQuickList.stream()
                        .map(QuickPayStatisticsRespDTO::getAmountTotal)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                respDTO.setActuallyPayFee(quickPayAmountTotal);
                respDTO.setOrderFee(quickPayAmountTotal);

                Integer quickPayOrderCount = storeQuickList.stream()
                        .map(QuickPayStatisticsRespDTO::getOrderCount)
                        .filter(Objects::nonNull)
                        .reduce(0, Integer::sum);
                respDTO.setOrderCount(quickPayOrderCount);

                Integer quickPayGuestCount = storeQuickList.stream()
                        .map(QuickPayStatisticsRespDTO::getGuestCount)
                        .filter(Objects::nonNull)
                        .reduce(0, Integer::sum);
                respDTO.setGuestCount(quickPayGuestCount);

                respDTO.setGuestAverageFee(respDTO.getActuallyPayFee().divide(BigDecimal.valueOf(respDTO.getGuestCount()), 2, RoundingMode.HALF_UP));
                respDTO.setOrderAverageFee(respDTO.getActuallyPayFee().divide(BigDecimal.valueOf(respDTO.getOrderCount()), 2, RoundingMode.HALF_UP));
                records.add(respDTO);
            }
        }
    }

    private List<QuickPayStatisticsRespDTO> queryQuickPayStatistics(StoreGatherReportReqDTO baseReq) {
        QuickPayStatisticsReqDTO quickPaReqDTO = new QuickPayStatisticsReqDTO();
        quickPaReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        quickPaReqDTO.setStartDateTime(baseReq.getBusinessStartDateTime());
        quickPaReqDTO.setEndDateTime(baseReq.getBusinessEndDateTime());
        quickPaReqDTO.setStoreGuidList(baseReq.getStoreGuids());
        return aggPayClientService.queryQuickPayStatistics(quickPaReqDTO);
    }

    @Override
    public BusinessDataRespDTO getBusinessData(JournalAppBaseReqDTO baseReq) {
        //去判断开始和结束时间
        checkTime(baseReq.getBusinessStartDateTime(), baseReq.getBusinessEndDateTime());
        // 因bug【39174】变更规则：订单金额金额扣减团购券的抵扣金额（原来是扣减团购券的够买金额），如此才能满足bug【39174】的要求
        return orderService.listOrderBusinessData(baseReq);
    }

    @Override
    public ScreenBusinessDataRespDTO getScreenData(JournalAppBaseReqDTO baseReq) {
        //去判断开始和结束时间
        checkTime(baseReq.getBusinessStartDateTime(), baseReq.getBusinessEndDateTime());

        BusinessDataRespDTO businessDataResp = orderService.listOrderBusinessData(baseReq);

        // 处理快速收款数据
        QuickPayStatisticsReqDTO quickPaReqDTO = new QuickPayStatisticsReqDTO();
        quickPaReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        quickPaReqDTO.setStartDateTime(baseReq.getBusinessStartDateTime());
        quickPaReqDTO.setEndDateTime(baseReq.getBusinessEndDateTime());
        quickPaReqDTO.setStoreGuidList(baseReq.getStoreGuidList());
        List<QuickPayStatisticsRespDTO> quickPayList = aggPayClientService.queryQuickPayStatistics(quickPaReqDTO);
        BigDecimal quickPayAmountTotal = BigDecimal.ZERO;
        Integer quickPayOrderCount = 0;
        Integer quickPayGuestCount = 0;
        if (!CollectionUtils.isEmpty(quickPayList)) {
            quickPayAmountTotal = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getAmountTotal)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            quickPayOrderCount = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getOrderCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);
            quickPayGuestCount = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getGuestCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);
        }

        ScreenBusinessDataRespDTO screenBusinessDataRespDTO = new ScreenBusinessDataRespDTO();
        Integer aliveStoreCount = staffClientService.getStaffAliveStoreCount();
        if (ObjectUtils.isEmpty(businessDataResp)) {
            screenBusinessDataRespDTO.setActuallyPayFee(quickPayAmountTotal);
            screenBusinessDataRespDTO.setBusinessFee(quickPayAmountTotal);
            screenBusinessDataRespDTO.setOrderCount(quickPayOrderCount);
            screenBusinessDataRespDTO.setGuestCount(quickPayGuestCount);
            return ScreenBusinessDataRespDTO.getInit(screenBusinessDataRespDTO, aliveStoreCount);
        }
        BeanUtils.copyProperties(businessDataResp, screenBusinessDataRespDTO);
        screenBusinessDataRespDTO.setActuallyPayFee(BigDecimalUtil.nonNullValue(screenBusinessDataRespDTO.getActuallyPayFee()).add(quickPayAmountTotal));
        screenBusinessDataRespDTO.setBusinessFee(BigDecimalUtil.nonNullValue(screenBusinessDataRespDTO.getBusinessFee()).add(quickPayAmountTotal));
        screenBusinessDataRespDTO.setOrderCount(screenBusinessDataRespDTO.getOrderCount() == null ? quickPayOrderCount : screenBusinessDataRespDTO.getOrderCount() + quickPayOrderCount);
        screenBusinessDataRespDTO.setGuestCount(screenBusinessDataRespDTO.getGuestCount() == null ? quickPayGuestCount : screenBusinessDataRespDTO.getGuestCount() + quickPayGuestCount);
        return ScreenBusinessDataRespDTO.getInit(screenBusinessDataRespDTO, aliveStoreCount);
    }

    @Override
    public BusinessHisTrendRespDTO getBusinessHisTrend(JournalAppBaseReqDTO baseReq) {
        //去判断开始和结束时间
        checkTime(baseReq.getBusinessStartDateTime(), baseReq.getBusinessEndDateTime());
        List<BusinessHisTrendDTO> list = orderService.listOrderBusinessHisTrend(baseReq);
        BusinessHisTrendRespDTO businessHisTrendResp = new BusinessHisTrendRespDTO();
        if (CollectionUtil.isEmpty(list)) {
            return businessHisTrendResp;
        }
        businessHisTrendResp.setGuestCountTrend(list.stream().map(BusinessHisTrendDTO::getGuestCount).collect(Collectors.toList()));
        businessHisTrendResp.setBusinessFeeTrend(list.stream().map(BusinessHisTrendDTO::getBusinessFee).collect(Collectors.toList()));
        businessHisTrendResp.setOrderCountTrend(list.stream().map(BusinessHisTrendDTO::getOrderCount).collect(Collectors.toList()));
        businessHisTrendResp.setDateData(list.stream().map(BusinessHisTrendDTO::getDate).collect(Collectors.toList()));
        return businessHisTrendResp;
    }

    private final static int MAX_QUERY_TIME = 30;

    private void checkTime(LocalDateTime businessStartDateTime, LocalDateTime businessEndDateTime) {
        if (businessEndDateTime == null || businessStartDateTime == null) {
            throw new BusinessException("查询时间不能为空");
        }
        long days = Duration.between(businessStartDateTime, businessEndDateTime).toDays();
        if (days > MAX_QUERY_TIME) {
            throw new BusinessException("查询时间范围最多只能30天");
        }
    }

    /**
     * 根据订单GUID获取订单明细
     *
     * @param orderGuid 订单guid
     * @return 明细
     */
    @Override
    public BusinessOrderStatisticsDetailRespDTO getOrderStatisticsDetail(String orderGuid) {
        OrderDO orderDO = orderService.getById(orderGuid);
        if (Objects.isNull(orderDO)) {
            throw new ParameterException("没有该订单的记录：" + orderGuid);
        }
        //===================== 转换数据类型 =====================
        BusinessOrderStatisticsDetailRespDTO response = getBusinessOrderStatisticsDetailRespDTO(orderDO);


        //===================== 处理订单商品信息 =====================
        dealOrderItemInfo(response, orderGuid);

        //===================== 并单信息 =====================
        Integer upperState = Optional.ofNullable(orderDO.getUpperState()).orElse(UpperStateEnum.GENERAL.getCode());
        if (upperState == UpperStateEnum.MAIN.getCode() || upperState == UpperStateEnum.SUB.getCode()) {
            List<OrderDO> mergeOrderList = orderService.getMergeOrderList(orderGuid, upperState);
            List<MergeOrderDetailRespDTO> mergeOrderDetailRespList = OrderTransform.INSTANCE.orderDo2MergeOrderDetailRespDTO(mergeOrderList);
            response.setMergeOrderDetails(mergeOrderDetailRespList);
            response.setHasMainOrder("1");
        }
        //===================== 反结账 新单/原单/退单信息 =====================
        dealAntiSettlementInfo(orderDO, response);

        //===================== 支付方式 =====================
        List<TransactionRecordDO> transactionRecordList = transactionRecordService.listByOrderGuid(orderGuid);
        if (!CollectionUtils.isEmpty(transactionRecordList)) {
            // 聚合支付 多次支付查询
            replenishTransactionRecord(orderGuid, transactionRecordList);
            response.setPayWayDetails(TransactionRecordTransform.INSTANCE.do2TransactionRecordRespDTO(transactionRecordList));
            response.setPayWayDetailsCount(transactionRecordList.stream().map(TransactionRecordDO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            // 订单使用会员多卡支付信息
            orderMultiMemberPayHandler(response);
        }
        // 构建团购信息
        Map<Integer, BigDecimal> grouponBuyPriceMap = buildGrouponTransactionRecordDO(orderGuid, transactionRecordList);
        if (oldCouponCalculateConfig.isBefore(orderDO.getCheckoutTime())) {
            if (MapUtils.isNotEmpty(grouponBuyPriceMap)) {
                // 实付金额增加
                BigDecimal grouponBuyPrice = grouponBuyPriceMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                response.setActuallyPayFee(new BigDecimal(response.getActuallyPayFee()).add(grouponBuyPrice).toPlainString());
                // 应收金额增加
                response.setAccountsReceivableAmount(new BigDecimal(response.getAccountsReceivableAmount()).add(grouponBuyPrice).toPlainString());
            }
        } else {
            BigDecimal totalCouponBuyPrice = Optional.ofNullable(response.getTotalCouponBuyPrice()).orElse(BigDecimal.ZERO);
            response.setActuallyPayFee(new BigDecimal(response.getActuallyPayFee()).add(totalCouponBuyPrice).toPlainString());
            // 应收金额增加
            response.setAccountsReceivableAmount(new BigDecimal(response.getAccountsReceivableAmount()).add(totalCouponBuyPrice).toPlainString());
        }
        //===================== 优惠信息 =====================
        List<DiscountDO> discountDoList = discountService.getUseDiscountListByOrderGuid(orderGuid);
        // 将团购优惠金额加入
        couponDiscountHandler(orderDO, discountDoList, grouponBuyPriceMap);

        if (!CollectionUtils.isEmpty(discountDoList)) {
            discountDoList.removeIf(e -> e.getDiscountFee().compareTo(BigDecimal.ZERO) == 0);
            discountDoList.removeIf(e -> DiscountTypeEnum.THIRD_ACTIVITY.getCode() == e.getDiscountType());
            response.setDiscountInfoList(OrderTransform.INSTANCE.discountDOS2DiscountDTOS(discountDoList));
            response.setDiscountCombined(discountDoList.stream().map(DiscountDO::getDiscountFee).reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        //附加费详情
        if (BigDecimalUtil.greaterThanZero(orderDO.getAppendFee())) {
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setData(String.valueOf(orderDO.getGuid()));
            response.setAppendFeeDetailList(appendFeeService.appendFeeList(singleDataDTO));
        }

        // 折扣需要补偿价格
        List<DineInItemDTO> discountItemList = response.getItemInfo().stream()
                .filter(dineInItemDTO -> dineInItemDTO.getPriceChangeType() != null && dineInItemDTO.getPriceChangeType() == 2)
                .collect(Collectors.toList());
        for (DineInItemDTO dineInItemDTO : discountItemList) {
            //折扣需要补偿价格
            BigDecimal reduiceprice = dineInItemDTO.getOriginalPrice()
                    .multiply(AmountCalculationUtil.ITEM_DISCOUNT_PERCENT_BASE.subtract(new BigDecimal
                                    (dineInItemDTO.getDiscountPercent()))
                            .divide(AmountCalculationUtil.ITEM_DISCOUNT_PERCENT_BASE, 2, BigDecimal
                                    .ROUND_HALF_DOWN))
                    .multiply(dineInItemDTO.getCurrentCount());
            dineInItemDTO.setItemPrice(dineInItemDTO.getItemPrice().subtract(reduiceprice).setScale(2, BigDecimal
                    .ROUND_HALF_UP));
        }
        //===================== 会员信息 =====================
        appendOrderMemberInfo(response);
        //===================== 挂账信息 =====================
        appendOrderDebtInfo(response);

        // 多单结账信息
        sameOrderInfoHandler(response, orderDO);
        return response;
    }

    /**
     * 团购优惠处理
     */
    private void couponDiscountHandler(OrderDO orderDO, List<DiscountDO> discountDoList, Map<Integer, BigDecimal> grouponBuyPriceMap) {
        //将团购优惠金额加入
        LocalDateTime checkoutTime = Optional.ofNullable(orderDO.getCheckoutTime()).orElse(orderDO.getGmtCreate());
        discountDoList.forEach(d -> {
            if (!grouponBuyPriceMap.containsKey(d.getDiscountType())) {
                return;
            }
            if (Objects.isNull(oldCouponCalculateConfig) || oldCouponCalculateConfig.isBefore(checkoutTime)) {
                BigDecimal grouponBuyPrice = grouponBuyPriceMap.get(d.getDiscountType());
                BigDecimal subtractDiscount = d.getDiscountFee().subtract(grouponBuyPrice);
                if (subtractDiscount.compareTo(BigDecimal.ZERO) <= 0) {
                    d.setDiscountFee(BigDecimal.ZERO);
                }
                d.setDiscountFee(subtractDiscount);
            }
            d.setDiscountName(GroupBuyTypeEnum.getDesc(d.getDiscountType()));
        });
    }

    private void replenishBatchTransactionRecord(List<Long> orderGuids, List<TransactionRecordDO> transactionRecordList) {
        if (CollectionUtils.isEmpty(transactionRecordList)) {
            return;
        }
        List<MultipleTransactionRecordDO> multipleTransactionRecordDOList = multipleTransactionRecordService.listByOrderGuidList(orderGuids);
        if (CollectionUtils.isEmpty(multipleTransactionRecordDOList)) {
            return;
        }
        multipleTransactionRecordDOList.forEach(multipleRecordDO -> multipleRecordDO.setAmount(
                multipleRecordDO.getAmount().subtract(multipleRecordDO.getRefundAmount())));
        // 过滤
        List<MultipleTransactionRecordDO> recordDOList = multipleTransactionRecordDOList.stream()
                .filter(t -> BigDecimalUtil.greaterThanZero(t.getAmount()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(recordDOList)) {
            return;
        }
        Map<Long, List<MultipleTransactionRecordDO>> multipleTransactionRecordDOMap = multipleTransactionRecordDOList.stream()
                .collect(Collectors.groupingBy(MultipleTransactionRecordDO::getOrderGuid));
        for (Map.Entry<Long, List<MultipleTransactionRecordDO>> entry : multipleTransactionRecordDOMap.entrySet()) {
            // 删除原有聚合支付记录
            transactionRecordList.removeIf(t -> entry.getKey().equals(t.getOrderGuid()) && PaymentTypeEnum.AGG.getCode() == t.getPaymentType());
            // 多笔聚合支付标记
            List<TransactionRecordDO> multipleRecordDOList = OrderTransform.INSTANCE.multipleRecordDO2TransactionRecordDO(entry.getValue());
            multipleRecordDOList.forEach(e -> e.setIsMultipleAggPay(multipleTransactionRecordDOList.size() > 1));
            transactionRecordList.addAll(multipleRecordDOList);
        }
    }

    private void replenishTransactionRecord(String orderGuid, List<TransactionRecordDO> transactionRecordList) {
        if (CollectionUtils.isEmpty(transactionRecordList)) {
            return;
        }
        transactionRecordList.removeIf(t -> PaymentTypeEnum.AGG.getCode() == t.getPaymentType()
                && t.getIsMultipleAggPay().equals(Boolean.TRUE)
                && !BigDecimalUtil.greaterThanZero(t.getAmount().subtract(t.getRefundAmount())));
        List<MultipleTransactionRecordDO> multipleTransactionRecordDOList = multipleTransactionRecordService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(multipleTransactionRecordDOList)) {
            return;
        }
        multipleTransactionRecordDOList.forEach(multipleRecordDO -> multipleRecordDO.setAmount(
                multipleRecordDO.getAmount().subtract(multipleRecordDO.getRefundAmount())));
        // 过滤
        List<MultipleTransactionRecordDO> recordDOList = multipleTransactionRecordDOList.stream()
                .filter(t -> BigDecimalUtil.greaterThanZero(t.getAmount()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(recordDOList)) {
            return;
        }
        // 删除原有聚合支付记录
        transactionRecordList.removeIf(t -> PaymentTypeEnum.AGG.getCode() == t.getPaymentType());
        List<TransactionRecordDO> multipleRecordDOList = OrderTransform.INSTANCE.multipleRecordDO2TransactionRecordDO(recordDOList);
        // 多笔聚合支付标记
        multipleRecordDOList.forEach(e -> e.setIsMultipleAggPay(multipleTransactionRecordDOList.size() > 1));
        transactionRecordList.addAll(multipleRecordDOList);
    }

    private void sameOrderInfoHandler(BusinessOrderStatisticsDetailRespDTO response, OrderDO orderDO) {
        if (!Objects.equals(UpperStateEnum.SAME_MAIN.getCode(), orderDO.getUpperState())) {
            return;
        }
        // 查询子单
        List<OrderDO> subOrderDOS = orderService.otherListByMainOrderGuid(orderDO.getGuid());
        subOrderDOS.removeIf(e -> e.getGuid().equals(orderDO.getGuid())
                || Objects.equals(StateEnum.REFUNDED.getCode(), e.getState()));
        List<BusinessOrderStatisticsDetailRespDTO> otherOrderDetails = Lists.newArrayList();
        for (OrderDO subOrderDO : subOrderDOS) {
            otherOrderDetails.add(getOrderStatisticsDetail(String.valueOf(subOrderDO.getGuid())));
        }
        // 复制一份主单到otherOrderDetails第一个
        BusinessOrderStatisticsDetailRespDTO mainOrder = new BusinessOrderStatisticsDetailRespDTO();
        BeanUtils.copyProperties(response, mainOrder);
        otherOrderDetails.add(0, mainOrder);
        response.setOtherOrderDetails(otherOrderDetails);
        // 多单结账合并商品金额
        mergeSameOrderInfo(response);
    }

    /**
     * 多单结账合并商品金额
     */
    private void mergeSameOrderInfo(BusinessOrderStatisticsDetailRespDTO orderDetail) {
        List<BusinessOrderStatisticsDetailRespDTO> otherOrderDetails = orderDetail.getOtherOrderDetails();
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            return;
        }
        // 实收金额
        BigDecimal actuallyPayFee = otherOrderDetails.stream()
                .filter(e -> !StringUtils.isEmpty(e.getActuallyPayFee()))
                .map(e -> new BigDecimal(e.getActuallyPayFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetail.setActuallyPayFee(actuallyPayFee.toPlainString());


        // 优惠金额 + 优惠明细
        List<DiscountDTO> discountFeeDetails = mergeDiscountFeeDetailList(orderDetail);
        orderDetail.setDiscountInfoList(discountFeeDetails);
        BigDecimal discountFee = discountFeeDetails.stream()
                .map(DiscountDTO::getDiscountFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetail.setDiscountCombined(discountFee);

        // 订单金额
        BigDecimal orderFee = otherOrderDetails.stream()
                .filter(e -> !StringUtils.isEmpty(e.getOrderFee()))
                .map(e -> new BigDecimal(e.getOrderFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetail.setOrderFee(orderFee.toPlainString());

        // 应收金额
        BigDecimal accountsReceivableAmount = otherOrderDetails.stream()
                .filter(e -> !StringUtils.isEmpty(e.getAccountsReceivableAmount()))
                .map(e -> new BigDecimal(e.getAccountsReceivableAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetail.setAccountsReceivableAmount(accountsReceivableAmount.toPlainString());

        // 付款金额 + 付款明细
        BigDecimal payWayDetailsCount = otherOrderDetails.stream()
                .map(BusinessOrderStatisticsDetailRespDTO::getPayWayDetailsCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetail.setPayWayDetailsCount(payWayDetailsCount);
        List<OrderDetailTransactionRecordRespDTO> actuallyPayFeeDetails = mergeActuallyPayFeeDetailList(orderDetail);
        orderDetail.setPayWayDetails(actuallyPayFeeDetails);

        // 退款金额 + 退款明细
        BigDecimal refundAmount = otherOrderDetails.stream()
                .map(BusinessOrderStatisticsDetailRespDTO::getRefundAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetail.setRefundAmount(refundAmount);
        orderDetail.setRefundDetails(mergeRefundDetailList(orderDetail));
    }

    /**
     * 交易记录合并
     */
    private List<OrderDetailTransactionRecordRespDTO> mergeActuallyPayFeeDetailList(BusinessOrderStatisticsDetailRespDTO orderDetail) {
        Map<String, OrderDetailTransactionRecordRespDTO> actuallyPayFeeDetailMap = Maps.newHashMap();
        for (BusinessOrderStatisticsDetailRespDTO otherOrderDetail : orderDetail.getOtherOrderDetails()) {
            List<OrderDetailTransactionRecordRespDTO> actuallyPayFeeDetailDTOS = Optional.ofNullable(otherOrderDetail.getPayWayDetails())
                    .orElse(Lists.newArrayList());
            for (OrderDetailTransactionRecordRespDTO actuallyPayFeeDetailDTO : actuallyPayFeeDetailDTOS) {
                OrderDetailTransactionRecordRespDTO totalActuallyPayFeeDetailDTO = actuallyPayFeeDetailMap.get(actuallyPayFeeDetailDTO.getPaymentTypeName());
                if (Objects.isNull(totalActuallyPayFeeDetailDTO)) {
                    actuallyPayFeeDetailMap.put(actuallyPayFeeDetailDTO.getPaymentTypeName(), actuallyPayFeeDetailDTO);
                } else {
                    totalActuallyPayFeeDetailDTO.setAmount(totalActuallyPayFeeDetailDTO.getAmount()
                            .add(actuallyPayFeeDetailDTO.getAmount()));
                    List<OrderMultiMemberPayDTO> orderMultiMemberPayDTOS = Optional.ofNullable(totalActuallyPayFeeDetailDTO.getMultiMemberPays())
                            .orElse(Lists.newArrayList());
                    orderMultiMemberPayDTOS.addAll(Optional.ofNullable(actuallyPayFeeDetailDTO.getMultiMemberPays()).orElse(Lists.newArrayList()));
                    totalActuallyPayFeeDetailDTO.setMultiMemberPays(orderMultiMemberPayDTOS);
                }
            }
        }
        return actuallyPayFeeDetailMap.values()
                .stream()
                .sorted(Comparator.comparing(OrderDetailTransactionRecordRespDTO::getPaymentType))
                .collect(Collectors.toList());
    }

    /**
     * 优惠信息合并
     */
    private List<DiscountDTO> mergeDiscountFeeDetailList(BusinessOrderStatisticsDetailRespDTO orderDetail) {
        // 优惠信息合并
        Map<Integer, DiscountDTO> discountFeeDetailMap = Maps.newHashMap();
        for (BusinessOrderStatisticsDetailRespDTO otherOrderDetail : orderDetail.getOtherOrderDetails()) {
            List<DiscountDTO> discountFeeDetailDTOS = Optional.ofNullable(otherOrderDetail.getDiscountInfoList())
                    .orElse(Lists.newArrayList());
            for (DiscountDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
                DiscountDTO totalDiscountFeeDetailDTO = discountFeeDetailMap.get(discountFeeDetailDTO.getDiscountType());
                if (Objects.isNull(totalDiscountFeeDetailDTO)) {
                    discountFeeDetailMap.put(discountFeeDetailDTO.getDiscountType(), discountFeeDetailDTO);
                } else {
                    totalDiscountFeeDetailDTO.setDiscountFee(totalDiscountFeeDetailDTO.getDiscountFee()
                            .add(discountFeeDetailDTO.getDiscountFee()));
                }
            }
        }
        return discountFeeDetailMap.values()
                .stream()
                .sorted(Comparator.comparing(DiscountDTO::getDiscountType))
                .collect(Collectors.toList());
    }

    /**
     * 退款信息合并
     */
    private List<RefundTransactionRecordDetailRespDTO> mergeRefundDetailList(BusinessOrderStatisticsDetailRespDTO orderDetail) {
        // 退款信息合并
        Map<String, RefundTransactionRecordDetailRespDTO> refundDetailMap = Maps.newHashMap();
        for (BusinessOrderStatisticsDetailRespDTO otherOrderDetail : orderDetail.getOtherOrderDetails()) {
            List<RefundTransactionRecordDetailRespDTO> refundTransactionRecordDetailRespDTOS = Optional.ofNullable(otherOrderDetail.getRefundDetails())
                    .orElse(Lists.newArrayList());
            for (RefundTransactionRecordDetailRespDTO refundTransactionRecordDetailRespDTO : refundTransactionRecordDetailRespDTOS) {
                RefundTransactionRecordDetailRespDTO totalRefundDetailDTO = refundDetailMap.get(refundTransactionRecordDetailRespDTO.getPaymentTypeName());
                if (Objects.isNull(totalRefundDetailDTO)) {
                    refundDetailMap.put(refundTransactionRecordDetailRespDTO.getPaymentTypeName(), refundTransactionRecordDetailRespDTO);
                } else {
                    totalRefundDetailDTO.setAmount(new BigDecimal(totalRefundDetailDTO.getAmount())
                            .add(new BigDecimal(refundTransactionRecordDetailRespDTO.getAmount())).toPlainString());
                }
            }
        }
        return refundDetailMap.values()
                .stream()
                .sorted(Comparator.comparing(RefundTransactionRecordDetailRespDTO::getPaymentTypeName))
                .collect(Collectors.toList());
    }

    /**
     * 订单使用会员多卡支付信息
     */
    private void orderMultiMemberPayHandler(BusinessOrderStatisticsDetailRespDTO response) {
        List<OrderDetailTransactionRecordRespDTO> payWayDetails = response.getPayWayDetails();
        if (CollectionUtils.isEmpty(payWayDetails)) {
            return;
        }
        OrderDetailTransactionRecordRespDTO memberPayment = payWayDetails.stream()
                .filter(e -> PaymentTypeEnum.MEMBER.getCode() == e.getPaymentType())
                .findFirst().orElse(null);
        if (Objects.isNull(memberPayment) || !BigDecimalUtil.greaterThanZero(memberPayment.getAmount())) {
            return;
        }
        List<OrderMultiMember> orderMultiMembers = orderMultiMemberService.listByOrderGuid(Long.valueOf(response.getGuid()));
        if (CollectionUtil.isEmpty(orderMultiMembers)) {
            return;
        }
        orderMultiMembers = orderMultiMembers.stream()
                .filter(e -> BigDecimalUtil.greaterThanZero(e.getAmount()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderMultiMembers)) {
            return;
        }
        memberPayment.setMultiMemberPays(OrderTransform.INSTANCE.orderMultiMember2OrderMultiMemberPayDTO(orderMultiMembers));
    }


    /**
     * 查询订单会员信息
     */
    private void appendOrderMemberInfo(BusinessOrderStatisticsDetailRespDTO response) {
        String memberName = response.getMemberName();
        if (!StringUtils.isEmpty(memberName)) {
            return;
        }
        String memberPhone = response.getMemberPhone();
        if (StringUtils.isEmpty(memberPhone) || memberPhone.length() <= 11) {
            return;
        }
        MemberResult<ResponseOperationMemberInfo> memberInfoResult = memberMerchantClientService.getMemberInfoByUerIdOrOpenId(memberPhone);
        log.info("查询会员信息返回:{}", JacksonUtils.writeValueAsString(memberInfoResult));
        if (Objects.isNull(memberInfoResult) || Objects.isNull(memberInfoResult.getTData())) {
            return;
        }
        ResponseOperationMemberInfo memberInfo = memberInfoResult.getTData();
        response.setMemberName(memberInfo.getUserName());
        response.setMemberPhone(memberInfo.getPhoneNum());
    }

    /**
     * 查询订单挂账信息
     */
    private void appendOrderDebtInfo(BusinessOrderStatisticsDetailRespDTO response) {
        List<OrderDetailTransactionRecordRespDTO> payWayDetails = response.getPayWayDetails();
        if (CollectionUtil.isEmpty(payWayDetails)) {
            return;
        }
        boolean isDebtPay = payWayDetails.stream().anyMatch(e -> PaymentTypeEnum.DEBT_PAY.getCode() == e.getPaymentType());
        if (!isDebtPay) {
            return;
        }
        DebtUnitRecordPageRespDTO debtUnitRecordByOrderGuid = debtUnitRecordService.debtUnitRecordByOrderGuid(Long.valueOf(response.getGuid()));
        if (Objects.isNull(debtUnitRecordByOrderGuid) || Objects.isNull(debtUnitRecordByOrderGuid.getOrderGuid())) {
            log.error("订单挂账信息丢失, orderGuid:{}", response.getGuid());
        }
        response.setDebtUnitRecord(debtUnitRecordByOrderGuid);
    }

    private Map<Integer, BigDecimal> buildGrouponTransactionRecordDO(String orderGuid, List<TransactionRecordDO> transactionRecordList) {
        Map<Integer, BigDecimal> grouponBuyPriceMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(transactionRecordList)) {
            return grouponBuyPriceMap;
        }
        // 团购验券使用
        List<TransactionRecordDO> couponPayRecordList = transactionRecordList.stream()
                .filter(e -> PaymentTypeEnum.getFilterGrouponPaymentTypes().contains(e.getPaymentType())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(couponPayRecordList)) {
            return grouponBuyPriceMap;
        }
        // 查询团购验券记录
        List<GrouponListRespDTO> grouponList = grouponService.listByOrderGuid(orderGuid, null);
        if (CollectionUtil.isEmpty(grouponList)) {
            log.error("订单记录异常，无团购验券使用记录,orderGuid:{}", orderGuid);
            return grouponBuyPriceMap;
        }
        Map<Integer, List<GrouponListRespDTO>> groupingByGroupBuyTypeMap = grouponList.stream()
                .collect(Collectors.groupingBy(GrouponListRespDTO::getGrouponType));


        // 更新团购验券的金额： 为顾客实际支付金额
        for (TransactionRecordDO transactionRecord : couponPayRecordList) {
            List<GrouponListRespDTO> grouponRecords;
            if (PaymentTypeEnum.MT_GROUPON.getCode() == transactionRecord.getPaymentType()) {
                grouponRecords = groupingByGroupBuyTypeMap.get(DiscountTypeEnum.GROUPON.getCode());
            } else {
                grouponRecords = groupingByGroupBuyTypeMap.get(transactionRecord.getPaymentType());
            }
            if (CollectionUtil.isEmpty(grouponRecords)) {
                continue;
            }
            BigDecimal grouponBuyPrice = grouponRecords.stream()
                    .map(GrouponListRespDTO::getCouponBuyPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            transactionRecord.setAmount(grouponBuyPrice);
            grouponBuyPriceMap.put(grouponRecords.get(0).getGrouponType(), grouponBuyPrice);
        }
        return grouponBuyPriceMap;
    }

    /**
     * 处理订单数据 转换成订单详细信息DTO
     */
    private BusinessOrderStatisticsDetailRespDTO getBusinessOrderStatisticsDetailRespDTO(OrderDO orderDO) {
        BusinessOrderStatisticsDetailRespDTO response = OrderTransform.INSTANCE.orderDo2OrderStatisticsDetailRespDTO(orderDO);
        //===================== 数据处理 =====================
        response.setOrderSource(explainDeviceType(orderDO.getDeviceType()));
        response.setCheckoutSource(explainDeviceType(orderDO.getCheckoutDeviceType()));
        response.setState(orderDO.getState());
        response.setOrderState(explainOrderState(orderDO.getState()));
        response.setRefundAmount(response.getRefundAmount().negate());
        response.setDiningTableName(orderDO.getTradeMode() == TradeModeEnum.DINEIN.getCode() ? orderDO.getDiningTableName() : orderDO.getMark());
        if (Objects.equals(StateEnum.READY.getCode(), orderDO.getState())
                || Objects.equals(StateEnum.PENDING.getCode(), orderDO.getState())
                || Objects.equals(StateEnum.FAILURE.getCode(), orderDO.getState())
                || Objects.equals(StateEnum.CANCEL.getCode(), orderDO.getState())) {
            response.setActuallyPayFee("0.00");
            response.setAccountsReceivableAmount("0.00");
            response.setCheckoutTime(null);
            response.setCheckoutStaffName(null);
        } else {
            //===================== 应收金额：用的 实收金额 + 找零，如果存在问题请排查这里 =====================
            response.setAccountsReceivableAmount(orderDO.getActuallyPayFee().toString());
        }
        // 拓展表补充
        OrderExtendsDO orderExtendsDO = orderExtendsService.getOne(new LambdaQueryWrapper<OrderExtendsDO>()
                .eq(OrderExtendsDO::getGuid, orderDO.getGuid()));
        if (ObjectUtils.isEmpty(orderExtendsDO)) {
            response.setIsInvoice(Boolean.FALSE);
            response.setAssociatedFlag(Boolean.FALSE);
        } else {
            response.setIsInvoice(orderExtendsDO.getIsInvoice());
            response.setInvoiceCode(orderExtendsDO.getInvoiceCode());

            response.setAssociatedFlag(orderExtendsDO.getAssociatedFlag());
            response.setAssociatedSn(orderExtendsDO.getAssociatedSn());
            response.setAssociatedTableGuids(Objects.nonNull(orderExtendsDO.getAssociatedTableGuids())
                    ? JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableGuids()) : null);
            response.setAssociatedTableNames(Objects.nonNull(orderExtendsDO.getAssociatedTableNames())
                    ? JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableNames()) : null);

            response.setLudouMemberName(orderExtendsDO.getLudouMemberName());
            response.setLudouMemberPhone(orderExtendsDO.getLudouMemberPhone());
            response.setTotalCouponBuyPrice(orderExtendsDO.getTotalCouponBuyPrice());
        }

        // 联台桌台显示
        if (Boolean.TRUE.equals(response.getAssociatedFlag())) {
            List<String> associatedTableNameList = response.getAssociatedTableNames();
            String associatedTableName = "联台-%s (%s)";
            response.setDiningTableName(String.format(associatedTableName, response.getAssociatedSn(), associatedTableNameList.size()));
        }
        return response;
    }

    @Override
    public BusinessOrderStatisticsDetailRespDTO getOrderStatisticsRefundInfo(String orderGuid) {
        OrderDO orderDO = orderService.getById(orderGuid);
        if (Objects.isNull(orderDO)) {
            throw new ParameterException("没有该订单的记录：" + orderGuid);
        }
        if (UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
            return buildRefundSameOrderDetailResp(orderDO);
        }
        return buildRefundOrderDetailResp(orderDO);
    }

    private BusinessOrderStatisticsDetailRespDTO buildRefundSameOrderDetailResp(OrderDO orderDO) {
        // 主单
        BusinessOrderStatisticsDetailRespDTO businessOrderStatisticsDetailRespDTO = buildRefundOrderDetailResp(orderDO);
        List<RefundTransactionRecordDetailRespDTO> refundDetails = businessOrderStatisticsDetailRespDTO.getRefundDetails();
        refundDetails = Optional.ofNullable(refundDetails).orElse(Lists.newArrayList());
        Long guid;
        if (Objects.nonNull(orderDO.getMainOrderGuid()) && !Objects.equals(0L, orderDO.getMainOrderGuid())) {
            guid = orderDO.getMainOrderGuid();
        } else {
            guid = orderDO.getGuid();
        }
        // 其他单
        List<OrderDO> orderDOList = orderService.otherListByMainOrderGuid(guid);
        orderDOList.removeIf(e -> e.getGuid().equals(guid) || Objects.isNull(e.getRefundOrderGuid()));
        // 合并
        BigDecimal totalOrderFee = new BigDecimal(Optional.ofNullable(businessOrderStatisticsDetailRespDTO.getOrderFee()).orElse("0"));
        for (OrderDO otherOrder : orderDOList) {
            BusinessOrderStatisticsDetailRespDTO otherDetailResp = buildRefundOrderDetailResp(otherOrder);
            if (!CollectionUtils.isEmpty(otherDetailResp.getRefundDetails())) {
                refundDetails.addAll(otherDetailResp.getRefundDetails());
            }
            totalOrderFee = totalOrderFee.add(new BigDecimal(Optional.ofNullable(otherDetailResp.getOrderFee()).orElse("0")));
        }
        businessOrderStatisticsDetailRespDTO.setOrderFee(totalOrderFee.toPlainString());
        businessOrderStatisticsDetailRespDTO.setRefundDetails(refundDetails);
        return businessOrderStatisticsDetailRespDTO;
    }

    private BusinessOrderStatisticsDetailRespDTO buildRefundOrderDetailResp(OrderDO orderDO) {
        BusinessOrderStatisticsDetailRespDTO response = getBusinessOrderStatisticsDetailRespDTO(orderDO);
        dealAntiSettlementInfo(orderDO, response);
        return response;
    }

    @Override
    public SaleRespDTO screenSale(SaleCountReqDTO saleCountReqDTO) {
        List<SaleCountRespDTO> statisticsDetailDTOS;
        if ("1".equals(saleCountReqDTO.getType())) {
            statisticsDetailDTOS = orderItemService.screenSaleItem(saleCountReqDTO);
            // 商品统计也要改成品牌库的（菜谱和普通模式合并），bug：37408
            if (!CollectionUtils.isEmpty(statisticsDetailDTOS)) {
                statisticsDetailDTOS = handleBrandItemStatistics(saleCountReqDTO, statisticsDetailDTOS);
            }
        } else {
            statisticsDetailDTOS = orderItemService.screenSaleType(saleCountReqDTO);
            // 产品要求，分类统计改为品牌库的分类，bug：37404
            if (!CollectionUtils.isEmpty(statisticsDetailDTOS)) {
                statisticsDetailDTOS = handleBrandTypeStatistics(saleCountReqDTO, statisticsDetailDTOS);
            }
        }
        SaleRespDTO respDTO = new SaleRespDTO();
        respDTO.setStatisticsDetailDTOS(statisticsDetailDTOS);
        return respDTO;
    }

    private List<SaleCountRespDTO> handleBrandItemStatistics(SaleCountReqDTO saleCountReqDTO,
                                                             List<SaleCountRespDTO> statisticsDetailDTOS) {
        List<String> itemGuidList = statisticsDetailDTOS.stream()
                .map(SaleCountRespDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(itemGuidList);
        List<ItemInfoRespDTO> itemInfoList = itemClientService.queryParentItemInfo(listDTO);
        if (!CollectionUtils.isEmpty(itemInfoList)) {
            Map<String, ItemInfoRespDTO> itemMap = itemInfoList.stream()
                    .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, Function.identity(), (v1, v2) -> v1));
            statisticsDetailDTOS.forEach(statistics -> {
                ItemInfoRespDTO itemDTO = itemMap.get(statistics.getItemGuid());
                if (ObjectUtils.isEmpty(itemDTO) || StringUtils.isEmpty(itemDTO.getParentGuid())) {
                    return;
                }
                ItemInfoRespDTO parentItemDTO = itemMap.get(itemDTO.getParentGuid());
                if (!ObjectUtils.isEmpty(parentItemDTO)) {
                    statistics.setItemGuid(parentItemDTO.getItemGuid());
                    statistics.setItemName(parentItemDTO.getName());
                }
            });
            Map<String, List<SaleCountRespDTO>> itemStatisticsMap = statisticsDetailDTOS.stream()
                    .collect(Collectors.groupingBy(SaleCountRespDTO::getItemGuid));
            List<SaleCountRespDTO> groupStatisticsDetailList = new ArrayList<>();
            itemStatisticsMap.forEach((itemGuid, saleList) -> {
                SaleCountRespDTO respDTO = buildSaleDTO(itemGuid, saleList);
                groupStatisticsDetailList.add(respDTO);
            });
            groupStatisticsDetailList.sort(Comparator.comparing(SaleCountRespDTO::getSalePrice).reversed());
            int index = Math.min(groupStatisticsDetailList.size(), Math.toIntExact(saleCountReqDTO.getPageSize()));
            statisticsDetailDTOS = groupStatisticsDetailList.subList(0, index);
        }
        return statisticsDetailDTOS;
    }

    private static SaleCountRespDTO buildSaleDTO(String itemGuid, List<SaleCountRespDTO> saleList) {
        SaleCountRespDTO respDTO = saleList.get(0);
        respDTO.setItemTypeGuid(itemGuid);
        BigDecimal salePrice = saleList.stream()
                .map(SaleCountRespDTO::getSalePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        respDTO.setSalePrice(salePrice);
        Double saleNumber = saleList.stream()
                .map(SaleCountRespDTO::getSaleNumber)
                .reduce(Double::sum)
                .orElse(0.0);
        respDTO.setSaleNumber(saleNumber);
        return respDTO;
    }

    private List<SaleCountRespDTO> handleBrandTypeStatistics(SaleCountReqDTO saleCountReqDTO,
                                                             List<SaleCountRespDTO> statisticsDetailDTOS) {
        List<String> typeGuidList = statisticsDetailDTOS.stream()
                .map(SaleCountRespDTO::getItemTypeGuid)
                .collect(Collectors.toList());
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(typeGuidList);
        List<TypeWebRespDTO> typeInfoList = itemClientService.querySourceTypeInfo(listDTO);
        if (!CollectionUtils.isEmpty(typeInfoList)) {
            Map<String, TypeWebRespDTO> typeMap = typeInfoList.stream()
                    .collect(Collectors.toMap(TypeWebRespDTO::getTypeGuid, Function.identity(), (v1, v2) -> v1));
            statisticsDetailDTOS.forEach(statistics -> {
                TypeWebRespDTO typeDTO = typeMap.get(statistics.getItemTypeGuid());
                if (ObjectUtils.isEmpty(typeDTO) || StringUtils.isEmpty(typeDTO.getParentGuid())) {
                    return;
                }
                TypeWebRespDTO parentTypeDTO = typeMap.get(typeDTO.getParentGuid());
                if (!ObjectUtils.isEmpty(parentTypeDTO)) {
                    statistics.setTypeName(parentTypeDTO.getName());
                    statistics.setItemTypeGuid(parentTypeDTO.getTypeGuid());
                }
            });
            Map<String, List<SaleCountRespDTO>> typeStatisticsMap = statisticsDetailDTOS.stream()
                    .collect(Collectors.groupingBy(SaleCountRespDTO::getItemTypeGuid));
            List<SaleCountRespDTO> groupStatisticsDetailList = new ArrayList<>();
            typeStatisticsMap.forEach((typeGuid, saleList) -> {
                SaleCountRespDTO respDTO = buildSaleDTO(typeGuid, saleList);
                groupStatisticsDetailList.add(respDTO);
            });
            groupStatisticsDetailList.sort(Comparator.comparing(SaleCountRespDTO::getSalePrice).reversed());
            int index = Math.min(groupStatisticsDetailList.size(), Math.toIntExact(saleCountReqDTO.getPageSize()));
            statisticsDetailDTOS = groupStatisticsDetailList.subList(0, index);
        }
        return statisticsDetailDTOS;
    }

    @Override
    public StoreStatisticsAppRespDTO saleStoreStatistics(StoreStatisticsAppReqDTO storeStatisticsAppReqDTO) {
        StoreStatisticsAppRespDTO respDTO = new StoreStatisticsAppRespDTO();
        List<StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO> statisticsDetailDTOS =
                orderService.saleStoreStatistics(storeStatisticsAppReqDTO);

        // 处理快速收款数据
        if (CollectionUtil.isEmpty(statisticsDetailDTOS)) {
            statisticsDetailDTOS = new ArrayList<>();
        }
        handleQuickPayStatistics(storeStatisticsAppReqDTO, statisticsDetailDTOS);

        respDTO.setStatisticsDetailDTOS(statisticsDetailDTOS);
        return respDTO;
    }

    /**
     * 处理快速收款数据
     */
    private void handleQuickPayStatistics(StoreStatisticsAppReqDTO storeStatisticsAppReqDTO,
                                          List<StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO> statisticsDetailDTOS) {
        QuickPayStatisticsReqDTO quickPaReqDTO = new QuickPayStatisticsReqDTO();
        quickPaReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        quickPaReqDTO.setStartDateTime(storeStatisticsAppReqDTO.getBusinessStartDateTime());
        quickPaReqDTO.setEndDateTime(storeStatisticsAppReqDTO.getBusinessEndDateTime());
        quickPaReqDTO.setStoreGuidList(storeStatisticsAppReqDTO.getStoreGuidList());
        List<QuickPayStatisticsRespDTO> quickPayList = aggPayClientService.queryQuickPayStatistics(quickPaReqDTO);
        if (CollectionUtil.isEmpty(quickPayList)) {
            log.warn("[handleQuickPayStatistics]快速收款数据为空");
            return;
        }
        Map<String, List<QuickPayStatisticsRespDTO>> payStatisticsDTOMap = quickPayList.stream()
                .collect(Collectors.groupingBy(QuickPayStatisticsRespDTO::getStoreGuid));

        // 查询门店
        List<String> storeGuidList = quickPayList.stream()
                .map(QuickPayStatisticsRespDTO::getStoreGuid)
                .distinct()
                .collect(Collectors.toList());
        List<StoreDTO> storeDTOList = storeClientService.queryStoreAndBrandByIdList(storeGuidList);
        Map<String, StoreDTO> storeMap = storeDTOList.stream()
                .collect(Collectors.toMap(StoreDTO::getGuid, o -> o));

        if (CollectionUtil.isEmpty(statisticsDetailDTOS)) {
            payStatisticsDTOMap.forEach((storeGuid, payList) -> {
                StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO detailDTO = getNewDetailDTO(storeGuid, payList, storeMap);
                statisticsDetailDTOS.add(detailDTO);
            });
        } else {
            Map<String, StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO> statisticsDetailDTOMap = statisticsDetailDTOS.stream()
                    .collect(Collectors.toMap(StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO::getStoreGuid, Function.identity(), (v1, v2) -> v1));
            payStatisticsDTOMap.forEach((storeGuid, quickPayDTOList) -> {
                StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO detailDTO = statisticsDetailDTOMap.get(storeGuid);
                if (ObjectUtils.isEmpty(detailDTO)) {
                    StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO newDetailDTO = getNewDetailDTO(storeGuid, quickPayDTOList, storeMap);
                    statisticsDetailDTOS.add(newDetailDTO);
                } else {
                    BigDecimal quickPayAmountTotal = quickPayDTOList.stream()
                            .map(QuickPayStatisticsRespDTO::getAmountTotal)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    detailDTO.setBusinessFee(BigDecimalUtil.nonNullValue(detailDTO.getBusinessFee()).add(quickPayAmountTotal));
                    Integer quickPayOrderCount = quickPayDTOList.stream()
                            .map(QuickPayStatisticsRespDTO::getOrderCount)
                            .filter(Objects::nonNull)
                            .reduce(0, Integer::sum);
                    detailDTO.setOrderCount(detailDTO.getOrderCount() == null ?
                            quickPayOrderCount : detailDTO.getOrderCount() + quickPayOrderCount);
                }
            });
        }

        statisticsDetailDTOS.sort(Comparator.comparing(StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO::getBusinessFee).reversed());
        if (statisticsDetailDTOS.size() > 5) {
            statisticsDetailDTOS.subList(0, 5);
        }
    }

    @NotNull
    private StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO getNewDetailDTO(String storeGuid,
                                                                               List<QuickPayStatisticsRespDTO> payList,
                                                                               Map<String, StoreDTO> storeMap) {
        StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO detailDTO = new StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO();
        detailDTO.setStoreGuid(storeGuid);
        StoreDTO storeDTO = storeMap.get(storeGuid);
        if (!ObjectUtils.isEmpty(storeDTO)) {
            detailDTO.setStoreName(storeDTO.getName());
        }
        BigDecimal quickPayAmountTotal = payList.stream()
                .map(QuickPayStatisticsRespDTO::getAmountTotal)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDTO.setBusinessFee(quickPayAmountTotal);

        Integer quickPayOrderCount = payList.stream()
                .map(QuickPayStatisticsRespDTO::getOrderCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        detailDTO.setOrderCount(quickPayOrderCount);
        return detailDTO;
    }

    @Override
    public SaleStatisticsByHoursRespDTO saleByHoursStatistics(SaleStatisticsByHoursReqDTO statisticsByHoursReqDTO) {
        LocalDateTime startDateTime;
        LocalDateTime endDateTime;
        if (Objects.isNull(statisticsByHoursReqDTO.getStartDate())
                && Objects.isNull(statisticsByHoursReqDTO.getEndDate())) {
            startDateTime = LocalDateTime.now().toLocalDate().minusDays(1).atTime(LocalTime.MIN);
            endDateTime = LocalDateTime.now().toLocalDate().atTime(LocalTime.MAX);
        } else if (Objects.isNull(statisticsByHoursReqDTO.getStartDate())) {
            startDateTime = statisticsByHoursReqDTO.getEndDate().minusDays(1).atTime(LocalTime.MIN);
            endDateTime = statisticsByHoursReqDTO.getEndDate().atTime(LocalTime.MAX);
        } else if (Objects.isNull(statisticsByHoursReqDTO.getEndDate())) {
            startDateTime = statisticsByHoursReqDTO.getStartDate().atTime(LocalTime.MIN);
            endDateTime = statisticsByHoursReqDTO.getStartDate().plusDays(1).atTime(LocalTime.MAX);
        } else {
            if (statisticsByHoursReqDTO.getStartDate().isAfter(statisticsByHoursReqDTO.getEndDate())) {
                throw new BusinessException("开始时间不能大于结束时间");
            }
            startDateTime = statisticsByHoursReqDTO.getStartDate().atTime(LocalTime.MIN);
            endDateTime = statisticsByHoursReqDTO.getEndDate().atTime(LocalTime.MAX);
        }
        statisticsByHoursReqDTO.setBusinessStartDateTime(startDateTime);
        statisticsByHoursReqDTO.setBusinessEndDateTime(endDateTime);
        List<StoreSaleItemDTO> statistics = orderService.saleByHoursStatistics(statisticsByHoursReqDTO);
        log.info("小时销售查询,query={},statistics={}", JacksonUtils.writeValueAsString(statisticsByHoursReqDTO),
                JacksonUtils.writeValueAsString(statistics));
        Map<String, StoreSaleItemDTO> statisticsMap = statistics.stream()
                .collect(Collectors.toMap(StoreSaleItemDTO::getTime, Function.identity()));

        SaleStatisticsByHoursRespDTO respDTO = getInitData(startDateTime, endDateTime);
        setBDData(respDTO, statisticsMap);
        return respDTO;
    }

    @Override
    public List<UserBriefDTO> getCheckoutStaffs(BusinessOrderStatisticsQueryDTO businessOrderStatisticsQueryDTO) {
        //避免前端传了结账员工信息
        businessOrderStatisticsQueryDTO.setCheckoutStaffGuids(null);
        return orderService.getCheckoutStaffs(businessOrderStatisticsQueryDTO);
    }

    private void setBDData(SaleStatisticsByHoursRespDTO respDTO, Map<String, StoreSaleItemDTO> statisticsMap) {
        if (Objects.isNull(respDTO) || MapUtils.isEmpty(respDTO.getSaleStatisticsData())) {
            return;
        }
        respDTO.getSaleStatisticsData().forEach((key, value) -> value.forEach(sale -> {
            StoreSaleItemDTO saleItemDTO = statisticsMap.get(sale.getTime());
            if (Objects.isNull(saleItemDTO)) {
                return;
            }
            sale.setSumActuallyPayFee(saleItemDTO.getSumActuallyPayFee());
        }));
    }

    public SaleStatisticsByHoursRespDTO getInitData(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        Map<String, List<StoreSaleItemDTO>> saleStatisticsData = new LinkedHashMap<>();
        // 计算时间范围内的天数
        long daysBetween = Duration.between(startDateTime, endDateTime).toDays();
        for (int i = 0; i <= daysBetween; i++) {
            String dateStr = startDateTime.plusDays(i).format(dateTimeFormatter);
            List<StoreSaleItemDTO> statisticsData = new ArrayList<>(24);
            for (int j = 0; j < 24; j++) {
                StoreSaleItemDTO storeSaleItemDTO = new StoreSaleItemDTO((dateStr + String.format(" %02d", j)), BigDecimal.ZERO);
                statisticsData.add(storeSaleItemDTO);
            }
            saleStatisticsData.put(dateStr, statisticsData);
        }
        SaleStatisticsByHoursRespDTO saleStatisticsByHoursRespDTO = new SaleStatisticsByHoursRespDTO();
        saleStatisticsByHoursRespDTO.setSaleStatisticsData(saleStatisticsData);
        return saleStatisticsByHoursRespDTO;
    }

    /**
     * 设置订单明细反结账信息
     */
    private void dealAntiSettlementInfo(OrderDO orderDO, BusinessOrderStatisticsDetailRespDTO response) {
        Integer recoveryType = Optional.ofNullable(orderDO.getRecoveryType()).orElse(RecoveryTypeEnum.GENERAL.getCode());
        if (recoveryType == RecoveryTypeEnum.ORIGINAL.getCode() || !StringUtils.isEmpty(orderDO.getRefundOrderGuid())) {
            List<OrderDO> originalOrderDoList = orderService.getListByOriginalOrderGuid(orderDO.getGuid());
            if (CollectionUtil.isEmpty(originalOrderDoList)) {
                return;
            }
            Map<Integer, List<OrderDO>> collect = originalOrderDoList.stream().collect(Collectors.groupingBy(OrderDO::getRecoveryType));
            List<OrderDO> newOrderList = collect.get(RecoveryTypeEnum.NEW.getCode());
            //===================== 新单信息 =====================
            if (CollectionUtil.isNotEmpty(newOrderList)) {
                response.setHasAntiSettlement("1");
                response.setNewOrderGuid(String.valueOf(newOrderList.get(0).getGuid()));
            }
            //===================== 退单退款信息 =====================
            List<OrderDO> returnOrderList = collect.get(RecoveryTypeEnum.RETURN.getCode());
            if (CollectionUtil.isEmpty(returnOrderList)) {
                return;
            }
            String returnNo = returnOrderList.get(0).getOrderNo();
            List<TransactionRecordDO> transactionRecordDoList = transactionRecordService.listByOrderGuid(String.valueOf(returnOrderList.get(0).getGuid()));
            //===================== 说明：如果是非线上支付，要用退款订单号，否则使用外部订单号 =====================
            List<RefundTransactionRecordDetailRespDTO> returnRespDtoList = TransactionRecordTransform.INSTANCE
                    .dos2TransactionRecordDetailsRespDTO(transactionRecordDoList);
            response.setRefundDetails(returnRespDtoList.stream().peek(e -> {
                if (StringUtils.isEmpty(e.getOrderNo())) {
                    e.setOrderNo(returnNo);
                }
                BigDecimal amount = new BigDecimal(e.getAmount()).negate();
                e.setAmount(amount.setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString());
                if (StringUtils.isEmpty(orderDO.getReserveGuid())) {
                    e.setAmount(amount.abs().setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString());
                }
            }).collect(Collectors.toList()));
            // 退款金额合计
            response.setRefundAmount(response.getRefundDetails().stream().map(e -> new BigDecimal(e.getAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            response.setHasRefund("1");
        }
    }

    /**
     * 处理商品信息
     */
    private void dealOrderItemInfo(BusinessOrderStatisticsDetailRespDTO response, String orderGuid) {
        List<OrderItemDO> orderItemList = orderItemService.listByOrderGuidWithOutIsDel(Long.valueOf(orderGuid));
        if (CollectionUtil.isNotEmpty(orderItemList)) {
            // 查询菜品扩展表
            List<Long> orderItemGuidList = orderItemList.stream().map(OrderItemDO::getGuid).collect(Collectors.toList());
            List<OrderItemExtendsDO> orderItemExtendsList = orderItemExtendsService.listByIds(orderItemGuidList);
            Map<Long, OrderItemExtendsDO> orderItemExtendsMap = orderItemExtendsList.stream()
                    .collect(Collectors.toMap(OrderItemExtendsDO::getGuid, Function.identity(), (key1, key2) -> key2));
            for (OrderItemDO orderItemDO : orderItemList) {
                OrderItemExtendsDO orderItemExtendsDO = orderItemExtendsMap.get(orderItemDO.getGuid());
                orderItemDO.setChangeFlag(OrderItemChangeFlagEnum.UN_CHANGE.getCode());
                if (Objects.nonNull(orderItemExtendsDO) && Objects.equals(orderItemExtendsDO.getChangeFlag(), OrderItemChangeFlagEnum.CHANGE.getCode())) {
                    orderItemDO.setChangeFlag(OrderItemChangeFlagEnum.CHANGE.getCode());
                }
            }
        }
        Map<Long, List<OrderItemDO>> itemListMap = CollectionUtil.toListMap(orderItemList, "guid");
        List<ItemAttrDO> itemAttrList = new ArrayList<>(itemAttrService.listByItemGuids(new ArrayList<>(itemListMap
                .keySet())));
        //===================== 菜品信息/赠送信息 =====================
        List<FreeReturnItemDO> freeReturnItemList = null;
        if (AmountCalculationUtil.hasFree(orderItemList)) {
            Map<Long, OrderItemDO> orderItemMap = CollectionUtil.toMap(orderItemList, "guid");
            freeReturnItemList = freeReturnItemService.list(new
                    LambdaQueryWrapper<FreeReturnItemDO>().in(FreeReturnItemDO::getOrderItemGuid, new ArrayList<>
                    (orderItemMap.keySet())).eq(FreeReturnItemDO::getType, FreeReturnTypeEnum.FREE.getCode()));
        }
        //===================== 菜品信息/计算菜品相关信息 =====================
        List<DineInItemDTO> dineInItemDtoList = AmountCalculationUtil.buildItem(orderItemList, itemAttrList, freeReturnItemList);

        // 套餐换菜信息
        addOriginalItemInfo(orderGuid, dineInItemDtoList);

        //===================== 设置菜品信息 =====================
        response.setItemInfo(dineInItemDtoList);
        Map<String, DineInItemDTO> dineInItemDtoMap = CollectionUtil.toMap(dineInItemDtoList, "guid");

        //退货信息
        List<FreeReturnItemDO> returnItemDoList = freeReturnItemService.list(new LambdaQueryWrapper<FreeReturnItemDO>()
                .eq(FreeReturnItemDO::getOrderGuid, orderGuid).eq(FreeReturnItemDO::getType, FreeReturnTypeEnum
                        .RETURN.getCode()).eq(FreeReturnItemDO::getIsDelete, false));
        List<ReturnItemDTO> returnItemDtoList = AmountCalculationUtil.bulidReturnItemDTOS(returnItemDoList, dineInItemDtoMap);
        //===================== 设置退菜信息 =====================
        response.setReturnItemInfoList(returnItemDtoList);
    }


    /**
     * 套餐换菜信息
     */
    private void addOriginalItemInfo(String orderGuid, List<DineInItemDTO> dineInItemDtoList) {
        // 过滤出套餐子商品换菜的商品
        List<SubDineInItemDTO> changeSubDineInItemList = dineInItemDtoList.stream()
                .filter(e -> org.apache.commons.collections.CollectionUtils.isNotEmpty(e.getPackageSubgroupDTOS()))
                .flatMap(e -> e.getPackageSubgroupDTOS().stream())
                .filter(e -> org.apache.commons.collections.CollectionUtils.isNotEmpty(e.getSubDineInItemDTOS()))
                .flatMap(e -> e.getSubDineInItemDTOS().stream())
                .filter(e -> Objects.equals(OrderItemChangeFlagEnum.CHANGE.getCode(), e.getChangeFlag()))
                .collect(Collectors.toList());
        packageSubgroupChangesService.addOriginalItemInfo(Long.valueOf(orderGuid), changeSubDineInItemList);
    }

    private String explainTradeMode(Integer tradeMode) {
        if (Objects.equals(tradeMode, TradeModeEnum.DINEIN.getCode())) {
            return TradeModeEnum.DINEIN.getDesc();
        }
        if (Objects.equals(tradeMode, TradeModeEnum.FAST.getCode())) {
            return TradeModeEnum.FAST.getDesc();
        }
        return null;
    }

    private String explainOrderState(Integer state) {
        if (Objects.equals(StateEnum.READY.getCode(), state)
                || Objects.equals(StateEnum.PENDING.getCode(), state)
                || Objects.equals(StateEnum.FAILURE.getCode(), state)
                || Objects.equals(StateEnum.SUB_SUCCESS.getCode(), state)) {
            return "未结账";
        }

        if (Objects.equals(StateEnum.SUCCESS.getCode(), state)) {
            return "已结账";
        }

//        if (Objects.equals(StateEnum.REFUNDED.getCode(), state)) {
//            return "已撤单";
//        }

        if (Objects.equals(StateEnum.CANCEL.getCode(), state)) {
            return "已作废";
        }

        if (Objects.equals(StateEnum.ANTI_SETTLEMENT.getCode(), state)) {
            return "反结账";
        }

        return null;

    }

    private String explainDeviceType(Integer deviceType) {
        if (deviceType == null) {
            return null;
        }
        String deviceTypeStr = String.valueOf(deviceType);
        if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.All_IN_ONE.getCode()), deviceTypeStr)) {
            return BaseDeviceTypeEnum.All_IN_ONE.getDesc();
        }
        if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.POS.getCode()), deviceTypeStr)) {
            return BaseDeviceTypeEnum.POS.getDesc();
        }
        if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.CLOUD_PANEL.getCode()), deviceTypeStr)) {
            return "PAD";
        }
        if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.SELF.getCode()), deviceTypeStr)) {
            return BaseDeviceTypeEnum.SELF.getDesc();
        }

        if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.M1.getCode()), deviceTypeStr)) {
            return BaseDeviceTypeEnum.M1.getDesc();
        }

        if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.PV1.getCode()), deviceTypeStr)) {
            return BaseDeviceTypeEnum.PV1.getDesc();
        }
        if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.WECHAT.getCode()), deviceTypeStr)
                || BaseDeviceTypeEnum.isApplet(deviceType)
                || Objects.equals(String.valueOf(BaseDeviceTypeEnum.WECHAT_MINI.getCode()), deviceTypeStr)) {
            return "扫码点餐";
        }
        return BaseDeviceTypeEnum.getDeviceTypeByCode(deviceType).getDesc();
    }
}
