package com.holderzone.saas.store.trade.entity.read;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("hst_adjust_order_record")
public class AdjustOrderRecordDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    private String guid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 订单guid
     */
    private String orderGuid;


    /**
     * 操作时间：调整单最后一次提交的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime operationTime;

    /**
     * 调整原因
     */
    private String reason;


    /**
     * 线下退款金额：调整单里面输入的退款金额
     */
    private BigDecimal refundFee;


    /**
     * 操作员guid
     */
    private String operatorGuid;

    /**
     * 操作员名称
     */
    private String operatorName;

    /**
     * 调整订单商品信息
     * 如果需要调整的商品过多，会出错。最大可以存储2000个字符串
     */
    private String adjustItmeInfo;

}
