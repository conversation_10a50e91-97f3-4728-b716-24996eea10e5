package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.trade.GrouponOrderDTO;
import com.holderzone.saas.store.trade.entity.domain.GrouponDO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 团购券 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-10
 */
public interface GrouponMapper extends BaseMapper<GrouponDO> {

    /**
     * 交接班统计
     */
    List<GrouponDO> handover(@Param("dto") HandoverPayQueryDTO request);

    /**
     * 根据券码查询订单信息
     */
    GrouponOrderDTO queryOrderByCode(@Param("code") String code);

    List<GrouponOrderDTO> useGrouponTotalAmountByOrderGuids(@Param("orderGuids") List<String> orderGuids);

    List<AmountItemDTO> listByRequest(@Param("request") DailyReqDTO request);

    /**
     * 团购券统计区分不同活动的券
     */
    List<AmountItemDTO> listByRequestGroupByName(@Param("request") DailyReqDTO request);

    List<AmountItemDTO> listRefundByRequest(@Param("request") StoreGatherReportReqDTO request);

    void updateCouponBuyPriceByCode(@Param("grouponType") Integer grouponType,
                                    @Param("code") String code,
                                    @Param("couponBuyPrice") BigDecimal couponBuyPrice);
}
