package com.holderzone.saas.store.trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.MemberOperateRecordDO;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.mapper.MemberOperateRecordMapper;
import com.holderzone.saas.store.trade.service.MemberOperateRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;

/**
 * <p>
 * 会员操作记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Slf4j
@Service
@AllArgsConstructor
public class MemberOperateRecordServiceImpl extends ServiceImpl<MemberOperateRecordMapper, MemberOperateRecordDO> implements MemberOperateRecordService {

    private MemberOperateRecordMapper memberOperateRecordMapper;

    private final DynamicHelper dynamicHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRecord(MemberOperateRecordReqDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO)) {
            return;
        }
        MemberOperateRecordDO recordDO = buildSaveDO(reqDTO);
        memberOperateRecordMapper.insert(recordDO);

    }

    @NotNull
    private MemberOperateRecordDO buildSaveDO(MemberOperateRecordReqDTO reqDTO) {
        MemberOperateRecordDO recordDO = new MemberOperateRecordDO();
        String guid = String.valueOf(dynamicHelper.generateGuid(GuidKeyConstant.HST_THIRD_ACTIVITY_RECORD));
        recordDO.setGuid(guid);
        recordDO.setGmtCreate(LocalDateTime.now());
        recordDO.setGmtModified(LocalDateTime.now());
        recordDO.setIsDelete(BooleanEnum.FALSE.getCode());
        recordDO.setDeviceType(reqDTO.getDeviceType());
        recordDO.setModuleType(reqDTO.getModuleType());
        recordDO.setLoginType(reqDTO.getLoginType());
        recordDO.setTradeMode(reqDTO.getTradeMode());
        recordDO.setOperateTime(LocalDateTime.now());
        recordDO.setOperatorGuid(reqDTO.getOperatorGuid());
        recordDO.setOperatorName(reqDTO.getOperatorName());
        recordDO.setPhoneNum(reqDTO.getPhoneNum());
        recordDO.setOrderGuid(reqDTO.getOrderGuid());
        recordDO.setStoreGuid(reqDTO.getStoreGuid());
        recordDO.setStoreName(reqDTO.getStoreName());
        return recordDO;
    }
}
