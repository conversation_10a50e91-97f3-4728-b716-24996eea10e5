package com.holderzone.saas.store.trade.transform;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseConfirmMultiPay;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.member.MemberInfoRespDTO;
import com.holderzone.saas.store.dto.member.MemberLoginRespDTO;
import com.holderzone.saas.store.dto.member.MemberNotifyDTO;
import com.holderzone.saas.store.dto.member.TransactionRecordDTO;
import com.holderzone.saas.store.dto.order.BusinessDayDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberPayDTO;
import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.local.FreeReturnItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.*;
import com.holderzone.saas.store.dto.order.request.dinein.*;
import com.holderzone.saas.store.dto.order.request.face.FacePayEstimateReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.request.item.BatchItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.dto.order.request.item.TransferItemReqDTO;
import com.holderzone.saas.store.dto.order.request.tcd.TcdAddOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.pay.SaasAggPayDTO;
import com.holderzone.saas.store.dto.pay.SaasAggRefundDTO;
import com.holderzone.saas.store.dto.print.content.nested.MultiMemberPayRecord;
import com.holderzone.saas.store.dto.table.BaseTableDTO;
import com.holderzone.saas.store.dto.table.TableStatusChangeDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.dto.trade.req.PadDineInItemDTO;
import com.holderzone.saas.store.dto.trade.req.PadOrderPlacementReqDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.PaymentDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByDineAndFastOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderItemRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.bo.AggPayAttachDataBO;
import com.holderzone.saas.store.trade.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.trade.entity.domain.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTransform
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-trade
 */
@Mapper(imports = JacksonUtils.class)
@Component
public interface OrderTransform {

    OrderTransform INSTANCE = Mappers.getMapper(OrderTransform.class);

    List<OrderTableInfoDTO> orderDOS2OrderTableInfoDTOS(Collection<OrderDO> orderDOS);

    OrderWechatDTO orderDO2OrderDTO(OrderDO orderDO);

    OrderTableInfoDTO orderDO2OrderTableInfoDTO(OrderDO orderDO);

    OrderItemDO dineInItemDTO2OrderItemDO(DineInItemDTO dineInItemDTO);

    ItemAttrDO itemAttrDTO2ItemAttrDO(ItemAttrDTO itemAttrDTO);

    DineinOrderDetailRespDTO orderDO2DineinOrderDetailRespDTO(OrderDO orderDO);

    List<DineInOrderListRespDTO> orderDOList2DineInOrderListRespDTOList(List<OrderDO> orderDOS);

    @Mappings({
            @Mapping(source = "guid", target = "orderGuid")
    })
    AdjustByDineAndFastOrderRespDTO orderDO2DAFAdjustOrder(OrderDO orderDO);

    List<AdjustByDineAndFastOrderRespDTO> orderDOList2DAFAdjustOrderList(List<OrderDO> orderDOS);

    @Mappings({
            @Mapping(target = "orderItemList", ignore = true),
            @Mapping(source = "guid", target = "orderGuid")
    })
    AdjustByOrderRespDTO orderDO2DineAdjustOrder(OrderDO orderDO);

    List<AdjustByOrderRespDTO> orderDOList2DineAdjustOrderList(List<OrderDO> orderDOS);

    DineInOrderListRespDTO orderDO2DineInOrderListRespDTO(OrderDO orderDO);

    DineInItemDTO orderItemDO2DineInItemDTO(OrderItemDO orderItemDO);

    List<DineInItemDTO> orderItemDO2DineInItemDTO(List<OrderItemDO> orderItemDO);

    List<ItemAttrDTO> itemAttrDOS2itemAttrDTOS(List<ItemAttrDO> itemAttrDOS);

    ItemAttrDTO itemAttrDO2itemAttrDTO(ItemAttrDO itemAttrDO);

    List<ItemAttrDO> itemAttrDTOS2itemAttrDOS(List<ItemAttrDTO> itemAttrDTOS);

    FreeItemDTO freeReturnItemDO2freeItemDTO(FreeReturnItemDO freeReturnItemDO);

    ReturnItemDTO freeReturnItemDO2rerurnItemDTO(FreeReturnItemDO freeReturnItemDO);

    @Mappings({
            @Mapping(target = "gmtCreate", ignore = true),
            @Mapping(target = "gmtModified", ignore = true),
    })
    FreeReturnItemDO orderItemDO2freeReturnItemDO(OrderItemDO orderItemDO);

    OrderItemDO orderItemDO2OrderItemDO(OrderItemDO orderItemDO);

    @Mappings({
            @Mapping(target = "gmtCreate", ignore = true),
            @Mapping(target = "gmtModified", ignore = true),
    })
    OrderItemRecordDO orderItemChangesDO2OrderItemRecordDO(OrderItemChangesDO orderItemChangesDO);

    @Mappings({
            @Mapping(target = "orderItemGuid", source = "guid"),
            @Mapping(target = "gmtCreate", ignore = true),
            @Mapping(target = "gmtModified", ignore = true),
    })
    OrderItemRecordDO orderItemDO2OrderItemRecordDO(OrderItemDO orderItemDO);

    List<OrderItemRecordDO> orderItemDOS2RecordDOS(List<OrderItemDO> orderItemDOS);

    List<OrderItemRecordDO> freeReturnItemDOS2RecordDOS(List<FreeReturnItemDO> freeReturnItemDOS);

    @Mappings({
            @Mapping(target = "orderItemGuid", source = "guid"),
            @Mapping(target = "gmtCreate", ignore = true),
            @Mapping(target = "gmtModified", ignore = true),
    })
    OrderItemRecordDO freeReturnItemDO2OrderItemRecordDO(FreeReturnItemDO freeReturnItemDO);

    OrderItemChangesDO subDineInItemDTO2OrderItemChangesDO(SubDineInItemDTO subDineInItemDTO);

    @Mappings({
            @Mapping(target = "guid", source = "orderItemGuid"),
    })
    SubDineInItemDTO orderItemChangesDO2SubDineInItemDTO(OrderItemChangesDO orderItemChangesDO);

    List<SubDineInItemDTO> orderItemChangesDO2SubDineInItemDTOList(List<OrderItemChangesDO> orderItemChangesDOList);

    OrderItemDO subDineInItemDTO2OrderItemDO(SubDineInItemDTO subDineInItemDTO);

    SubDineInItemDTO orderItemDO2SubDineInItemDTO(OrderItemDO subGroupOrderItemDO);

    MemberInfoRespDTO memberLoginRespDTO2MemberInfoRespDTO(MemberLoginRespDTO memberLoginRespDTO);

    DiscountRuleBO billCalculateReqDTO2DiscountRuleBO(BillCalculateReqDTO billCalculateReqDTO);

    List<DiscountRuleBO> systemDiscountDTOS2DiscountRuleBOS(List<SystemDiscountDTO> systemDiscountDTOS);

    DiscountFeeDetailDTO discountDO2DiscountFeeDetailDTO(DiscountDO discountDO);

    List<DiscountDO> discountFeeDetailDTOS2discountDOS(List<DiscountFeeDetailDTO> discountFeeDetailDTOS);

    TableStatusChangeDTO createDineInOrderReqDTO2TableStatusChangeDTO(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    TableStatusChangeDTO batchItemReturnOrFreeReqDTO2TableStatusChangeDTO(BatchItemReturnOrFreeReqDTO
                                                                                  batchItemReturnOrFreeReqDTO);

    List<ActuallyPayFeeDetailDTO> transactionRecordDOS2ActuallyPayFeeDetailDTOS(List<TransactionRecordDO>
                                                                                        transactionRecordDOS);

    List<DiscountFeeDetailDTO> discountDOS2DiscountFeeDetailDTOS(List<DiscountDO> discountDOS);

    TableStatusChangeDTO billPayReqDTO2TableStatusChangeDTO(BillPayReqDTO billPayReqDTO);

    TableStatusChangeDTO getBaseDTODFromDto(BaseDTO baseDTO);

    BaseTableDTO recoveryReqDTO2BaseTableDTO(RecoveryReqDTO recoveryReqDTO);

    TableStatusChangeDTO cancelOrderReqDTO2TableStatusChangeDTO(CancelOrderReqDTO cancelOrderReqDTO);

    CreateDineInOrderReqDTO createFastFoodReqDTO2CreateDineInOrderReqDTO(CreateFastFoodReqDTO createFastFoodReqDTO);

    MemberNotifyDTO recoveryReqDTO2MemberNotifyDTO(RecoveryReqDTO recoveryReqDTO);

    SaasAggPayDTO billPayReqDTO2SaasAggPayDTO(BillAggPayReqDTO billPayReqDTO);

    TableStatusChangeDTO aggPayAttachDataBO2TableStatusChangeDTO(AggPayAttachDataBO aggPayAttachDataBO);

    AggPayAttachDataBO baseInfo2AggPayAttachDataBO(BaseInfo baseInfo);

    SaasAggRefundDTO refundReqDTO2SaasAggRefundDTO(RefundReqDTO refundReqDTO);

    SaasAggRefundDTO cancelOrderReqDTO2SaasAggRefundDTO(BaseDTO baseDTO);

    TableStatusChangeDTO refundReqDTO2TableStatusChangeDTO(RefundReqDTO refundReqDTO);

    DiscountDO discountFeeDetailDTO2DiscountDO(DiscountFeeDetailDTO discountFeeDetailDTO);

    MemberNotifyDTO baseInfo2MemberNotifyDTO(BaseInfo baseInfo);

    SaasAggRefundDTO recoveryReqDTO2SaasAggRefundDTO(RecoveryReqDTO recoveryReqDTO);

    TableStatusChangeDTO weChatPayReqDTO2TableStatusChangeDTO(WeChatPayReqDTO weChatPayReqDTO);

    List<GrouponListRespDTO> grouponDOS2GrouponListRespDTOS(List<GrouponDO> grouponDOS);

    BillPayReqDTO billAggPayReqDTO2BillPayReqDTO(BillAggPayReqDTO billPayReqDTO);

    BillPayReqDTO billPayReqDTO2BillPayReqDTO(BillPayReqDTO billPayReqDTO);

    BillPayReqDTO facePayEstimateReqDTO2BillPayReqDTO(FacePayEstimateReqDTO facePayEstimateReqDTO);

    DineInItemDTO subDineInItemDTO2DineInItemDTO(SubDineInItemDTO subDineInItemDTO);

    List<DineInItemDTO> subDineInItemDTO2DineInItemDTOList(List<SubDineInItemDTO> subDineInItemList);

    BillPayReqDTO weChatPayReqDTODTO2BillPayReqDTO(WeChatPayReqDTO weChatPayReqDTO);

    OrderItemDO priceChangeItemReqDTO2OrderItemDO(PriceChangeItemReqDTO priceChangeItemReqDTO);

    @Mapping(
            target = "isDelete",
            expression = "java(orderDOS.getIsDelete()?1:0)"
    )
    OrderDTO orderDO2DTO(OrderDO orderDOS);

    List<OrderDTO> orderDOList2DTOList(List<OrderDO> orderDOS);

    List<AppendFeeDTO> appendDOList2DTOList(List<AppendFeeDO> append);

    @Mapping(
            target = "isDelete",
            expression = "java(append.getIsDelete()?1:0)"
    )
    AppendFeeDTO appendDO2DTO(AppendFeeDO append);

    List<FreeReturnItemDTO> freeDOList2DTOList(List<FreeReturnItemDO> free);

    @Mapping(
            target = "isDelete",
            expression = "java(free.getIsDelete()?1:0)"
    )
    FreeReturnItemDTO freeDO2DTO(FreeReturnItemDO free);

    List<TransactionRecordDTO> transactionRecordDOS2TransactionRecordDTOS(List<TransactionRecordDO>
                                                                                  transactionRecordDOS);

    List<DiscountDTO> discountDOS2DiscountDTOS(List<DiscountDO> discountDOS);

    @Mapping(
            target = "isDelete",
            expression = "java(discountDOS.getIsDelete()?1:0)"
    )
    DiscountDTO discountDOS2Discount(DiscountDO discountDOS);

    OrderDO tcdAddOrderReqDTO2orderDO(TcdAddOrderReqDTO tcdAddOrderReqDTO);

    DiscountDO tcdAddOrderReqDTO2discountDO(TcdAddOrderReqDTO tcdAddOrderReqDTO);

    TransactionRecordDO tcdAddOrderReqDTO2transactionRecordDO(TcdAddOrderReqDTO tcdAddOrderReqDTO);

    List<OrderItemDO> dineInItemDTOS2OrderItemDOS(List<DineInItemDTO> dineInItemDTOS);

    List<AppendFeeDO> appendFeeDetailDTOS2appendFeeDOS(List<AppendFeeDetailDTO> appendFeeDetailDTOS);

    OrderItemRecordDTO orderItemRecordDO2orderItemDTO(OrderItemRecordDO orderItemRecordDO);

    List<OrderItemRecordDTO> orderItemRecordListDO2orderItemDTO(List<OrderItemRecordDO> orderItemRecordDOS);

    ItemAttrDetailDTO itemAttrDO2ItemAttrDTO(ItemAttrDO attrDOS);

    List<ItemAttrDetailDTO> itemAttrDOList2ItemAttrDTO(List<ItemAttrDO> attrDOS);

    @Mappings({
            @Mapping(source = "gmtModified", target = "updateTime")
    })
    OrderItemDTO toOrderItemDTO(OrderItemDO orderItemDO);

    List<OrderItemDTO> toOrderItemDTOS(List<OrderItemDO> orderItemDOs);

    OrderAbnormalRecordRespDTO toOrderAbnormalRecordRespDTO(OrderAbnormalRecordDO orderAbnormalRecordDO);

    /**
     * 订单信息转DTO
     *
     * @param orderDO 订单记录信息
     * @return 商户后台订单统计订单明细DTO
     */
    @Mappings({
            @Mapping(source = "tradeMode", target = "tradeMode", qualifiedByName = "explainTradeMode"),
            @Mapping(target = "hasMainOrder", constant = "0"),
            @Mapping(target = "hasRefund", constant = "0"),
            @Mapping(target = "hasAntiSettlement", constant = "0"),
            @Mapping(source = "gmtCreate", target = "createTime"),
    })
    BusinessOrderStatisticsDetailRespDTO orderDo2OrderStatisticsDetailRespDTO(OrderDO orderDO);

    /**
     * 集合转换需要实体转换
     *
     * @param orderDO 订单信息
     * @return 并单信息
     */
    MergeOrderDetailRespDTO orderDo2MergeOrderDetailDTO(OrderDO orderDO);

    /**
     * 订单集合 转 并单集合
     *
     * @param orderDoList 订单记录集合
     * @return 并单记录
     */
    List<MergeOrderDetailRespDTO> orderDo2MergeOrderDetailRespDTO(List<OrderDO> orderDoList);

    @Mappings({
            @Mapping(source = "guid", target = "orderGuid")
    })
    BillCalculateReqDTO batchCreateFastFoodReqDTO2BillCalculateReqDTO(BatchCreateFastFoodReqDTO.FastFoodReqDTO fastFoodReqDTO);

    @Mappings({
            @Mapping(source = "guid", target = "orderGuid")
    })
    BillPayReqDTO batchCreateFastFoodReqDTO2BillPayReqDTO(BatchCreateFastFoodReqDTO.FastFoodReqDTO fastFoodReqDTO);

    @Mappings({
            @Mapping(source = "dineInItemDTOList", target = "dineInItemDTOS"),
            @Mapping(source = "orderGuid", target = "guid"),
            @Mapping(target = "addGoodsBatchUUID", ignore = true),
            @Mapping(target = "print", ignore = true),
            @Mapping(target = "reserveOrder", ignore = true),
            @Mapping(target = "userWxPublicOpenId", ignore = true)
    })
    CreateDineInOrderReqDTO orderPlacementDTO2CreateDineDTO(PadOrderPlacementReqDTO orderPlacementReqDTO);

    PadOrderRespDTO padOrderDO2OrderRespDTO(PadOrderDO padOrderDO);

    List<PadOrderRespDTO> padOrderDOList2OrderRespDTOList(List<PadOrderDO> padOrderDOList);

    PadOrderDO OrderRespDTO2padOrderDO(PadOrderRespDTO padOrderRespDTO);

    List<PadOrderDO> OrderRespDTOList2padOrderDOList(List<PadOrderRespDTO> padOrderRespDTOList);

    OrderItemDO dineInItemDTO2OrderItemDO(PadDineInItemDTO dineInItemDTO);

    DineInItemDTO dineInItemDTO2dineInItemDTO(PadDineInItemDTO padDineInItemDTO);

    List<DineInItemDTO> padDineInItemDTOList2DineInItemDTOList(List<PadDineInItemDTO> padDineInItemDTOList);

    @Mappings({
            @Mapping(source = "guid", target = "orderItemGuid"),
            @Mapping(target = "totalPrice", ignore = true)
    })
    AdjustByOrderItemRespDTO orderItemDO2AdjustOrderItemDTO(OrderItemDO orderItemDO);

    List<AdjustByOrderItemRespDTO> orderItemDOs2AdjustOrderItemDTOs(List<OrderItemDO> orderItemDOList);

    PaymentDTO transactionRecordDO2PaymentDTO(TransactionRecordDO transactionRecordDO);

    List<PaymentDTO> transDOList2PaymentDTOList(List<TransactionRecordDO> list);

    BusinessDayDTO orderDO2BusinessDayDTO(OrderDO orderDO);

    List<BusinessDayDTO> orderDOList2BusinessDayList(List<OrderDO> orderDOList);

    @Mappings({
            @Mapping(target = "maitonConsumeInfo", expression = "java(grouponDO.getMaitonConsumeInfo()!=null?JacksonUtils.toObject(com.holderzone.saas.store.dto.takeaway.response.MtMaitonConsumeDTO.class,grouponDO.getMaitonConsumeInfo()):null)"),
    })
    GrouponListRespDTO orderDO2GrouponListRespDTO(GrouponDO grouponDO);

    List<GrouponListRespDTO> orderDOListGrouponListRespDTOList(List<GrouponDO> groupons);

    @Mappings({
            @Mapping(source = "memberInfoCardGuid", target = "memberCardGuid"),
    })
    OrderMultiMember orderMultiMemberDTO2OrderMultiMember(OrderMultiMemberDTO orderMultiMemberDTO);

    @Mappings({
            @Mapping(source = "memberCardGuid", target = "memberInfoCardGuid"),
    })
    OrderMultiMemberDTO orderMultiMember2OrderMultiMemberDTO(OrderMultiMember orderMultiMember);

    List<OrderMultiMemberDTO> orderMultiMember2OrderMultiMemberDTO(List<OrderMultiMember> orderMultiMembers);

    OrderMultiMemberPayDTO orderMultiMember2OrderMultiMemberPayDTO(OrderMultiMember orderMultiMember);

    List<OrderMultiMemberPayDTO> orderMultiMember2OrderMultiMemberPayDTO(List<OrderMultiMember> orderMultiMembers);

    OrderMultiMemberPayDTO responseConfirmMultiPay2OrderMultiMemberPayDTO(ResponseConfirmMultiPay responseConfirmMultiPay);

    List<OrderMultiMemberPayDTO> responseConfirmMultiPay2OrderMultiMemberPayDTO(List<ResponseConfirmMultiPay> responseConfirmMultiPays);

    MultiMemberPayRecord orderMultiMemberPay2MultiMemberPayRecord(OrderMultiMemberPayDTO orderMultiMemberPay);

    List<MultiMemberPayRecord> orderMultiMemberPay2MultiMemberPayRecord(List<OrderMultiMemberPayDTO> orderMultiMemberPays);

    /**
     * 根据tradeMode 类型 提供中文支持
     *
     * @param tradeMode 交易模式
     * @return 模式描述
     */
    @Named("explainTradeMode")
    default String explainTradeMode(Integer tradeMode) {
        if (Objects.equals(tradeMode, TradeModeEnum.DINEIN.getCode())) {
            return TradeModeEnum.DINEIN.getDesc();
        }
        if (Objects.equals(tradeMode, TradeModeEnum.FAST.getCode())) {
            return TradeModeEnum.FAST.getDesc();
        }
        return null;
    }

    TableStatusChangeDTO transferReq2TableStatusChangeDTO(TransferItemReqDTO transferReq);

    TransactionRecordDO multipleRecordDO2TransactionRecordDO(MultipleTransactionRecordDO recordDO);

    List<TransactionRecordDO> multipleRecordDO2TransactionRecordDO(List<MultipleTransactionRecordDO> recordDO);

    ActuallyPayFeeDetailDTO multipleRecordDO2ActuallyPayFeeDetailDTO(MultipleTransactionRecordDO multipleRecordDO);

    List<ActuallyPayFeeDetailDTO> multipleRecordDOS2ActuallyPayFeeDetailDTOS(List<MultipleTransactionRecordDO> multipleRecordDOList);

}
