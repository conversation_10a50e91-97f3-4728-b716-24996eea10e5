package com.holderzone.saas.store.trade.service.chain;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestActivityUse;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseActivityUse;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseClientActivitiesList;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountRuleDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.CommonUtil;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 第N份优惠活动
 */
@Component
@Slf4j
@AllArgsConstructor
public class NthActivityDiscountHandler extends DiscountHandler {

    private final MemberTerminalClientService memberTerminalClientService;

    private final PriceCalculationUtils priceCalculationUtils;

    @Override
    void dealDiscount(DiscountContext context) {
        if (context.isRejectDiscount() || Objects.equals(context.getOrderDO().getTradeMode(), TradeModeEnum.FAST.getCode())) {
            return;
        }
        DiscountFeeDetailDTO activity = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap()
                .get(type()));
        RequestActivityUse activityUseReqDTO = new RequestActivityUse();
        activityUseReqDTO.setActivityGuid(context.getBillCalculateReqDTO().getNthActivityGuid());
        activityUseReqDTO.setMemberConsumptionGuid(context.getOrderDO().getMemberConsumptionGuid());
        activityUseReqDTO.setNeedActivityList(context.getBillCalculateReqDTO().getIsNeedActivityList() != null &&
                context.getBillCalculateReqDTO().getIsNeedActivityList());
        activityUseReqDTO.setMemberInfoCardGuid(context.getOrderDO().getMemberCardGuid());
        List<RequestDishInfo> requestDishInfos = CommonUtil.dineInItem2DishList(context.getAllItems());
        activityUseReqDTO.setDishInfoDTOList(requestDishInfos);
        activityUseReqDTO.setOrderDate(context.getOrderDO().getGmtCreate());
        activityUseReqDTO.setUnNeedPersistence(Optional.ofNullable(context.getBillCalculateReqDTO().getUnNeedPersistence()).orElse(false));
        activityUseReqDTO.setActivityType(DiscountTypeEnum.NTH_ACTIVITY.getCode());
        activityUseReqDTO.setOrderState(context.getOrderDO().getTradeMode());
        if (!StringUtils.isEmpty(context.getBillCalculateReqDTO().getActivityGuid())) {
            RequestActivityUse.InnerOtherActivity otherActivity = new RequestActivityUse.InnerOtherActivity();
            otherActivity.setActivityGuid(context.getBillCalculateReqDTO().getActivityGuid());
            otherActivity.setActivityType(DiscountTypeEnum.ACTIVITY.getCode());
            activityUseReqDTO.setOtherActivities(Lists.newArrayList(otherActivity));
        }
        priceCalculationUtils.xx90(requestDishInfos);
        ResponseActivityUse nthActivityUseRespDTO = memberTerminalClientService.activitySelect(activityUseReqDTO);
        log.info("第N份优惠活动返回：{}", JacksonUtils.writeValueAsString(nthActivityUseRespDTO));
        singleItemDiscount(context.getDineInItemDTOMap(), activity, nthActivityUseRespDTO.getDishInfoDTOList());
        // 商品实付金额处理
        handleDiscountTotalPrice(context, nthActivityUseRespDTO);
        context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(activity
                .getDiscountFee()));
        activity.setContent(nthActivityUseRespDTO.getActivityName());
        if (CollectionUtils.isNotEmpty(nthActivityUseRespDTO.getActivitiesList())) {
            List<ResponseClientActivitiesList> activitiesLists = Optional
                    .ofNullable(context.getOrderDetailRespDTO().getActivityInfos()).orElse(Lists.newArrayList());
            activitiesLists.addAll(nthActivityUseRespDTO.getActivitiesList());
            context.getOrderDetailRespDTO().setActivityInfos(activitiesLists);
        }
        context.getOrderDetailRespDTO().setNthActivityGuid(nthActivityUseRespDTO.getActivityGuid());
        context.getOrderDO().setMemberConsumptionGuid(nthActivityUseRespDTO.getMemberConsumptionGuid());
        if (!StringUtils.isEmpty(nthActivityUseRespDTO.getActivityGuid())) {
            DiscountRuleDTO discountRuleDTO = new DiscountRuleDTO();
            discountRuleDTO.setActivityGuid(nthActivityUseRespDTO.getActivityGuid());
            discountRuleDTO.setActivityName(nthActivityUseRespDTO.getActivityName());
            activity.setRule(JacksonUtils.writeValueAsString(discountRuleDTO));
        }
        context.getDiscountFeeDetailDTOS().add(activity);
        log.info("优惠活动计算后订单剩余金额：{}，优惠金额：{}，菜品优惠：{}", context.getOrderDetailRespDTO().getOrderSurplusFee(), activity
                .getDiscountFee(), JacksonUtils.writeValueAsString(context.getDineInItemDTOMap()));
    }

    /**
     * 商品实付优惠金额处理
     */
    private void handleDiscountTotalPrice(DiscountContext context, ResponseActivityUse activityUseRespDTO) {
        Map<String, DineInItemDTO> itemDTOMap = context.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (v1, v2) -> v1));
        activityUseRespDTO.getDishInfoDTOList().forEach(dish -> {
            if (dish.getDiscountMoney() != null) {
                DineInItemDTO dineInItemDTO = itemDTOMap.get(dish.getOrderItemGuid());
                if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                    dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(dish.getDiscountMoney()));
                }
            }
        });
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.NTH_ACTIVITY.getCode();
    }
}
