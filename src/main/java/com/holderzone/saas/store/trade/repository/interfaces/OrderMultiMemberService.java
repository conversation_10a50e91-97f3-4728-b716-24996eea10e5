package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.OrderMultiMember;

import java.util.List;

public interface OrderMultiMemberService extends IService<OrderMultiMember> {

    List<OrderMultiMember> listByOrderGuid(Long orderGuid);

    void updateBatchPayAmount(List<OrderMultiMember> orderMultiMembers);

    void removeByOrderGuid(Long orderGuid);

    void removeByOrderGuidAndMemberCardGuid(Long orderGuid, String memberCardGuid);

    List<OrderMultiMember> listByOrderGuidList(List<String> orderGuidList);
}
