package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.retry.anno.FeignRetry;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.trade.anno.PerformanceCheck;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayClientService
 * @date 2018/09/06 19:33
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(value = "holder-saas-store-pay", fallbackFactory = AggPayClientService.SaasAggPayClientFallBack.class)
public interface AggPayClientService {

    /**
     * 支付
     *
     * @param saasAggPayDTO
     * @return
     */
    @PostMapping("agg/pay")
    @PerformanceCheck
    AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO);

    /**
     * 退款
     *
     * @param saasAggRefundDTO
     * @return
     */
    @FeignRetry(include = {BusinessException.class})
    @PostMapping("agg/refund")
    AggRefundRespDTO refund(@RequestBody SaasAggRefundDTO saasAggRefundDTO);

    @PostMapping("agg/refund")
    AggRefundRespDTO portionRefund(@RequestBody SaasAggRefundDTO saasAggRefundDTO);
    /**
     * 轮询
     *
     * @param saasPollingDTO
     * @return
     */
    @PostMapping("agg/polling")
    AggPayPollingRespDTO polling(SaasPollingDTO saasPollingDTO);

    /**
     * 取消支付
     *
     * @param saasPollingDTO
     * @return
     */
    @PostMapping("agg/reserve")
    AggPayReserveResultDTO reservePay(SaasPollingDTO saasPollingDTO);

    /**
     * 查询支付状态
     *
     * @param saasPollingDTO
     * @return
     */
    @PostMapping("agg/queryPaySt")
    AggPayPollingRespDTO queryPaySt(SaasPollingDTO saasPollingDTO);

    @PostMapping("agg/query")
    @ApiOperation(value = "聚合支付查询支付结果接口")
    AggPayPollingRespDTO
    query(@RequestBody SaasPollingDTO saasPollingDTO);

    @PostMapping("/agg/record/quickPay/statistics")
    @ApiOperation(value = "快速收款统计")
    List<QuickPayStatisticsRespDTO> queryQuickPayStatistics(@RequestBody QuickPayStatisticsReqDTO request);

    String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

    @Slf4j
    @Component
    class SaasAggPayClientFallBack implements FallbackFactory<AggPayClientService> {
        @Override
        public AggPayClientService create(Throwable throwable) {
            return new AggPayClientService() {
                @Override
                public AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO) {
                    throw new BusinessException("调用交易中心异常");
                }

                @Override
                public AggRefundRespDTO refund(SaasAggRefundDTO saasAggRefundDTO) {
                    throw new BusinessException("聚合支付退款异常");
                }

                @Override
                public AggRefundRespDTO portionRefund(SaasAggRefundDTO saasAggRefundDTO) {
                    throw new BusinessException("聚合支付部分退款异常");
                }

                @Override
                public AggPayPollingRespDTO polling(SaasPollingDTO saasPollingDTO) {
                    throw new BusinessException("聚合支付轮询异常");
                }

                @Override
                public AggPayReserveResultDTO reservePay(SaasPollingDTO saasPollingDTO) {
                    throw new BusinessException("聚合支付撤销异常");
                }

                @Override
                public AggPayPollingRespDTO queryPaySt(SaasPollingDTO saasPollingDTO) {
                    throw new BusinessException("聚合支付查询支付状态异常");
                }

                @Override
                public AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO) {
                    throw new BusinessException("聚合支付查询支付状态异常");
                }

                @Override
                public List<QuickPayStatisticsRespDTO> queryQuickPayStatistics(QuickPayStatisticsReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "queryQuickPayStatistics", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
