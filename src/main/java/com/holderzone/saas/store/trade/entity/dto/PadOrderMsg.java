package com.holderzone.saas.store.trade.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021/8/29 16:07
 * @className: PadOrderMsg
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PadOrderMsg implements Serializable {

    private static final long serialVersionUID = 116087722670628321L;

    @ApiModelProperty("是否自动接单，0是，1否")
    private Integer autoRev;

    @ApiModelProperty("预订单guid")
    private String guid;

    @ApiModelProperty("订单状态")
    private Integer orderState;

    @ApiModelProperty("刷新栏目，0全部，1待处理，2已接单，3已拒单")
    private Integer bar;

    /**
     * 系统设备编号
     */
    @ApiModelProperty(value = "系统设备编号（云端生成）")
    private String deviceGuid;
}
