package com.holderzone.saas.store.trade.controller;


import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreGatherTotalRespDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.local.FreeReturnItemDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.response.OrderUploadRespDTO;
import com.holderzone.saas.store.dto.reserve.UpdateOrderReserveReqDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.dto.trade.req.FixGrouponTransactionRecordReqDTO;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderInvoiceStateReqDTO;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderMemberInfoReqDTO;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.manager.DineInBillManager;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.OrderDetailService;
import com.holderzone.saas.store.trade.service.inner.interfaces.AsyncTradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Yu Ren
 * @date 2020/6/1 11:35
 * @description
 */
@RestController
@RequestMapping("/order_detail")
@Api(tags = "订单接口")
@Slf4j
public class OrderDetailController {

    @Autowired
    private OrderDetailService orderDetailService;

    @Autowired
    private AsyncTradeService asyncTradeService;

    @Autowired
    private DynamicHelper dynamicHelper;

    @Resource
    private OrderService orderService;

    @Resource
    private DineInBillManager dineInBillManager;

    @ApiOperation(value = "订单列表", notes = "订单列表")
    @PostMapping("/order_list")
    public Page<OrderInfoRespDTO> pageOrderInfo(@RequestBody DineInOrderListReqDTO reqDTO) {
        return orderDetailService.pageOrderInfo(reqDTO);
    }

    @ApiOperation(value = "微信订单列表", notes = "微信订单列表")
    @PostMapping("/wx/order_list")
    public Page<OrderInfoRespDTO> pageWxOrderInfo(@RequestBody DineInOrderListReqDTO reqDTO) {
        return orderDetailService.pageWxOrderInfo(reqDTO);
    }

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/find_by_order_guid")
    public OrderDTO findByOrderGuid(@RequestParam("orderGuid") String orderGuid) {
        log.info("获取订单详情 orderGuid={}", orderGuid);
        return orderDetailService.findByOrderGuid(orderGuid);
    }

    @ApiOperation(value = "获取多个订单详情", notes = "获取多个订单详情")
    @PostMapping("/list_by_order_guid")
    public List<OrderDTO> listByOrderGuid(@RequestBody SingleDataDTO query) {
        log.info("[获取多个订单详情]query={}", JacksonUtils.writeValueAsString(query));
        return orderDetailService.listByOrderGuid(query);
    }

    @ApiOperation(value = "获取订单详情By订单编号", notes = "获取订单详情By订单编号")
    @GetMapping("/find_by_order_no_and_store_guid")
    public OrderDTO findByOrderNoAndStoreGuid(@RequestParam("orderNo") String orderNo,
                                              @RequestParam("storeGuid") String storeGuid) {
        log.info("[获取订单详情By订单编号],orderNo={},storeGuid={}", orderNo, storeGuid);
        return orderDetailService.findByOrderNoAndStoreGuid(orderNo, storeGuid);
    }

    @ApiOperation(value = "获取多个订单详情By订单编号", notes = "获取多个订单详情By订单编号")
    @PostMapping("/list_by_order_no_and_store_guid")
    public List<OrderDTO> listByOrderNoAndStoreGuid(@RequestBody SingleDataDTO query) {
        log.info("[获取多个订单详情By订单编号]query={}", JacksonUtils.writeValueAsString(query));
        return orderDetailService.listByOrderNoAndStoreGuid(query);
    }

    @ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
    @PostMapping("/find_by_wx_order_guid")
    public OrderDetailPushMqDTO findByWxOrderGuid(@RequestBody OrderGuidsDTO orderGuidsDTO) {
        log.info("获取微信订单详情 orderGuidsDTO:{} ", JacksonUtils.writeValueAsString(orderGuidsDTO));
        EnterpriseIdentifier.setEnterpriseGuid(orderGuidsDTO.getEnterpriseGuid());
        dynamicHelper.changeDatasource(orderGuidsDTO.getEnterpriseGuid());
        UserContextUtils.putErp(orderGuidsDTO.getEnterpriseGuid());
        return asyncTradeService.getOrderDetailPushMqDTO(orderGuidsDTO.getOrderGuid());
    }

    @ApiOperation(value = "获取订单流水详情", notes = "获取订单流水详情")
    @GetMapping("/get_order_item_record")
    public List<OrderItemRecordDTO> getOrderItemRecord(@RequestParam("orderGuid") String orderGuid) {
        return orderDetailService.getOrderItemRecord(orderGuid);
    }

    @ApiOperation(value = "获取订单商品属性", notes = "获取订单商品属性")
    @GetMapping("/get_item_attr")
    public List<ItemAttrDetailDTO> getItemAttr(@RequestParam("orderGuid") String orderGuid) {
        return orderDetailService.getItemAttr(orderGuid);
    }

    @ApiOperation(value = "根据企业id获取订单详情", notes = "根据企业id获取订单详情")
    @GetMapping("/find_by_enterprise_guid")
    public OrderDTO findByEnterpriseGuid(@RequestParam("enterpriseGuid") String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        return orderDetailService.findByEnterpriseGuid(enterpriseGuid);
    }

    @ApiOperation(value = "根据订单id修改is_handle为已处理", notes = "根据订单id修改is_handle")
    @GetMapping("/update_order_is_handle_by_order_guid")
    public Boolean updateOrderIsHandleByOrderGuid(@RequestParam("orderGuid") String orderGuid, @RequestParam("isHandle") Integer isHandle) {
        return orderDetailService.updateOrderIsHandleByOrderGuid(orderGuid, isHandle);
    }

    @ApiOperation(value = "recoveryId获取订单详情", notes = "获取订单详情")
    @GetMapping("/find_by_recovery_id")
    public List<OrderDTO> findByRecoveryId(@RequestParam("recoveryId") String recoveryId) {
        return orderDetailService.findByRecoveryId(recoveryId);
    }


    @ApiOperation(value = "获取附加费记录", notes = "获取附加费记录")
    @GetMapping("/get_append_fee")
    public List<AppendFeeDTO> getAppendFee(@RequestParam("orderGuid") String orderGuid, @RequestParam("enterpriseGuid") String enterpriseGuid) {
        return orderDetailService.getAppendFee(orderGuid, enterpriseGuid);
    }

    @ApiOperation(value = "获取订单交易记录", notes = "获取订单交易记录")
    @GetMapping("/get_transaction_record")
    public List<TransactionRecordDTO> getTransactionRecord(@RequestParam("orderGuid") String orderGuid, @RequestParam("enterpriseGuid") String enterpriseGuid) {
        return orderDetailService.getTransactionRecord(orderGuid, enterpriseGuid);
    }

    @ApiOperation(value = "获取订单优惠记录", notes = "获取订单优惠记录")
    @GetMapping("/get_discount")
    public List<DiscountDTO> getDiscount(@RequestParam("orderGuid") String orderGuid, @RequestParam("enterpriseGuid") String enterpriseGuid) {
        return orderDetailService.getDiscount(orderGuid, enterpriseGuid);
    }

    @ApiOperation(value = "获取赠送/退货记录", notes = "获取赠送/退货记录")
    @GetMapping("/get_free_return_item")
    public List<FreeReturnItemDTO> getFreeReturnItem(@RequestParam("orderGuid") String orderGuid, @RequestParam("enterpriseGuid") String enterpriseGuid) {
        return orderDetailService.getFreeReturnItem(orderGuid, enterpriseGuid);
    }


    @ApiOperation(value = "获取订单商品", notes = "获取订单商品")
    @GetMapping("/get_order_item")
    public List<OrderItemDTO> getOrderItem(@RequestParam("orderGuid") Long orderGuid, @RequestParam("enterpriseGuid") String enterpriseGuid) {
        return orderDetailService.getOrderItem(orderGuid, enterpriseGuid);
    }

    @ApiOperation(value = "批量获取订单商品", notes = "获取订单商品")
    @PostMapping("/get_order_items")
    public List<OrderItemDTO> getOrderItems(@RequestBody OrderGuidsDTO orderGuidsDTO) {
        return orderDetailService.getOrderItems(orderGuidsDTO.getOrderGuids(), orderGuidsDTO.getEnterpriseGuid());
    }

    @ApiOperation(value = "根据guid更新订单备注", notes = "根据guid更新订单备注")
    @PutMapping("/update_remark/{guid}")
    public boolean updateOrderRemarkById(@PathVariable String guid, @RequestParam("remark") String remark) {
        return orderDetailService.updateOrderRemarkById(guid, remark);
    }

    @ApiOperation(value = "根据guid更新订单商品备注", notes = "根据guid更新订单商品备注")
    @PutMapping("/update_item_remark/{guid}")
    boolean updateOrderItemRemarkById(@PathVariable String guid,
                                      @RequestParam("itemGuid") String itemGuid,
                                      @RequestParam("remark") String remark) {
        return orderDetailService.updateOrderItemRemarkById(guid, itemGuid, remark);
    }

    /**
     * 根据并台主单guid查询定订单信息列表
     *
     * @param combineOrderGuid 并台主单guid
     * @return 订单信息列表
     */
    @ApiOperation("根据并台主单guid查询定订单信息列表")
    @GetMapping(value = "/list_order_by_combine_order_guid")
    public List<OrderDTO> listOrderByCombineOrderGuid(@RequestParam("combineOrderGuid") String combineOrderGuid) {
        log.info("根据并台主单guid查询定订单信息列表 combineOrderGuid={}", combineOrderGuid);
        return orderDetailService.listOrderByCombineOrderGuid(combineOrderGuid);
    }

    @ApiOperation(value = "查询门店汇总", notes = "根据guid更新订单商品备注")
    @PostMapping("/storeGatherTotalList")
    public StoreGatherTotalRespDTO storeGatherTotalList(@RequestBody StoreGatherReportReqDTO storeGatherReportReqDTO) {
        return orderDetailService.storeGatherTotalList(storeGatherReportReqDTO, false);
    }

    @ApiOperation(value = "查询门店汇总", notes = "根据guid更新订单商品备注")
    @PostMapping("/storeGatherTotalListExport")
    public StoreGatherTotalRespDTO storeGatherTotalListExport(@RequestBody StoreGatherReportReqDTO storeGatherReportReqDTO) {
        return orderDetailService.storeGatherTotalList(storeGatherReportReqDTO, true);
    }

    @ApiOperation(value = "订单复制", notes = "将订单复制到指定门店")
    @GetMapping(value = "/copyOrders", produces = "application/json;charset=utf-8")
    public OrderUploadRespDTO copyOrders(@RequestParam("fileUrl") String fileUrl) {
        return orderDetailService.copyOrders(fileUrl);
    }

    @ApiOperation("根据门店guid查询是否有下单")
    @GetMapping(value = "/queryordernum")
    public Integer queryOrderNumForStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        return orderDetailService.queryOrderNumForStoreGuid(storeGuid);
    }

    @ApiOperation("根据门店guid查询第一笔订单日期")
    @GetMapping(value = "/getfristorder")
    public LocalDateTime getfristorderForStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        return orderDetailService.queryfristorderForStoreGuid(storeGuid);
    }

    @ApiOperation("设置结账是否更新优惠券关系")
    @GetMapping(value = "/updateOrderIsUpdatedEs")
    void updateOrderIsUpdatedEs(@RequestParam("orderGuid") String orderGuid){
        orderService.updateOrderIsUpdatedEs(orderGuid);
    }

    @ApiOperation(value = "查询订单中待支付的订单")
    @PostMapping("/query_ready_order")
    public List<String> queryReadyOrder(@RequestBody SingleListDTO singleListDTO) {
        return orderService.queryReadyOrder(singleListDTO);
    }

    @ApiOperation(value = "更新订单预定信息")
    @PostMapping("/update_order_reserve")
    public void updateOrderReserve(@RequestBody UpdateOrderReserveReqDTO reqDTO) {
        log.info("[更新订单预定信息]reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        orderService.updateOrderReserve(reqDTO);
    }

    @ApiOperation(value = "更新订单会员信息")
    @PostMapping("/update_order_member_info")
    public void updateOrderMemberInfo(@RequestBody UpdateOrderMemberInfoReqDTO reqDTO) {
        log.info("[更新订单会员信息]reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        orderService.updateOrderMemberInfo(reqDTO);
    }

    @ApiOperation(value = "更新订单开票状态")
    @PostMapping("/update_order_invoice_state")
    public void updateOrderInvoiceState(@RequestBody UpdateOrderInvoiceStateReqDTO reqDTO) {
        log.info("[更新订单开票状态]reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        orderService.updateOrderInvoiceState(reqDTO);
    }

    @ApiOperation(value = "根据订单查询桌台信息")
    @GetMapping("/find_table_by_order_guid")
    public OrderTableDTO findTableByOrderGuid(@RequestParam("orderGuid") String orderGuid) {
        log.info("findTableByOrderGuid orderGuid={}", orderGuid);
        return orderDetailService.findTableByOrderGuid(orderGuid);
    }

    @ApiOperation(value = "修复团购支付记录丢失问题")
    @PostMapping("/fix_groupon_transaction_record")
    public void fixGrouponTransactionRecord(@RequestBody FixGrouponTransactionRecordReqDTO reqDTO) {
        log.info("FixGrouponTransactionRecordReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        dineInBillManager.fixGrouponTransactionRecord(reqDTO);
    }
}
