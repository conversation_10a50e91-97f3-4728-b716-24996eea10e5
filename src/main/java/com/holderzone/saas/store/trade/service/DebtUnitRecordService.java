package com.holderzone.saas.store.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.trade.entity.domain.DebtUnitRecordDO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/12/15 16:06
 * @description
 */
public interface DebtUnitRecordService extends IService<DebtUnitRecordDO> {
    /***
     *  分页查单位挂账流水
     * @param reqDTO 请求参数
     * @return
     */
    Page<DebtUnitRecordPageRespDTO> pageDebtUnitRecord(DebtUnitRecordPageReqDTO reqDTO);

    /**
     * 根据还款批次号查询还款记录
     */
    List<DebtUnitRecordDO> listByRepaymentBatchNumber(String repaymentBatchNumber);

    /**
     * 查询订单挂账记录
     */
    DebtUnitRecordPageRespDTO debtUnitRecordByOrderGuid(Long orderGuid);

    /***
     *  根据单位Guid 查询 汇总还款数据
     * @param unitGuid
     * @return
     */
    DebtUnitRecordTotalDTO queryDebtUnitTotal(String unitGuid);

    /***
     *  更新单位挂账记录
     * @param updateReqDTO
     * @return
     */
    Boolean updateDebtUnit(DebtUnitRecordUpdateReqDTO updateReqDTO);
    /***
     *  保存挂账记录
     * @param saveReqDTO
     * @return
     */
    Boolean saveDebtUnit(DebtUnitRecordSaveReqDTO saveReqDTO);
    /**
     * 部分还款
     * @param saveReqDTO
     */
    void refundDebtUnit(DebtUnitRecordSaveReqDTO saveReqDTO);

    /***
     *  根据单位Guid 查询还款信息
     * @param unitGuid
     * @return
     */
    DebtUnitRecordTotalDTO queryDebtUnitTotalByUnitGuid(String unitGuid);

    /**
     * H5页面查询挂账记录
     * @param reqDTO DebtUnitLoginH5ReqDTO
     * @return DebtRecordH5RespDTO
     */
    DebtRecordH5RespDTO queryDebtRecordH5(DebtUnitLoginH5ReqDTO reqDTO);
    /**
     * 单位Guid汇总挂账金额
     * @return 金额
     */
    BigDecimal calculateDebtFeeTotal(String unitGuid);

}
