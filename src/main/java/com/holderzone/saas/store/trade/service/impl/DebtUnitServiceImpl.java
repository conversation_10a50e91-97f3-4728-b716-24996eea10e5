package com.holderzone.saas.store.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.DebtUnitDO;
import com.holderzone.saas.store.trade.entity.dto.DebtCreditChangeDTO;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.PageAdapter;
import com.holderzone.saas.store.trade.mapper.DebtUnitMapper;
import com.holderzone.saas.store.trade.mapper.DebtUnitRecordMapper;
import com.holderzone.saas.store.trade.service.DebtUnitRecordService;
import com.holderzone.saas.store.trade.service.DebtUnitService;
import com.holderzone.saas.store.trade.transform.DebtTransform;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 挂账单位Service
 *
 * <AUTHOR>
 * @since 2020-12-15
 */
@Slf4j
@Service
public class DebtUnitServiceImpl extends ServiceImpl<DebtUnitMapper, DebtUnitDO> implements DebtUnitService {

    private final DebtUnitMapper debtUnitMapper;

    private final DynamicHelper dynamicHelper;

    private final DebtUnitRecordMapper debtUnitRecordMapper;

    @Lazy
    @Resource
    private DebtUnitRecordService debtUnitRecordService;

    public DebtUnitServiceImpl(DebtUnitMapper debtUnitMapper, DynamicHelper dynamicHelper,
                               DebtUnitRecordMapper debtUnitRecordMapper) {
        this.debtUnitMapper = debtUnitMapper;
        this.dynamicHelper = dynamicHelper;
        this.debtUnitRecordMapper = debtUnitRecordMapper;
    }

    @Override
    public Boolean saveUnit(DebtUnitSaveReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getUnitGuid())) {
            //新建
            //判断单位代码与名称是否有重复
            if (debtUnitMapper.codeExists(reqDTO)) {
                log.warn("新建单位代码重复");
                throw new BusinessException("单位代码已存在");
            }
            if (debtUnitMapper.nameExists(reqDTO)) {
                log.warn("新建单位名称重复");
                throw new BusinessException("单位名称已存在");
            }
            if (debtUnitMapper.telExists(reqDTO)) {
                log.warn("新建单位电话重复");
                throw new BusinessException("单位联系电话已存在");
            }
            //将reqDTO转换为实体
            DebtUnitDO debtUnitDO = DebtTransform.INSTANCE.debtUnitReq2DO(reqDTO);
            //生成guid
            debtUnitDO.setGuid(String.valueOf(dynamicHelper.generateGuid(GuidKeyConstant.HST_DEBT_UNIT)));
            //设置可用额度
            debtUnitDO.setCreditLimitLeft(reqDTO.getCreditLimit());
            return this.save(debtUnitDO);
        } else {
            //编辑
            //查询原单位实体
            DebtUnitDO oldDebtUnitDO = this.getById(reqDTO.getUnitGuid());
            //判断单位代码与名称是否有重复
            if (!oldDebtUnitDO.getCode().equals(reqDTO.getCode()) && debtUnitMapper.codeExists(reqDTO)) {
                log.warn("修改单位代码重复");
                throw new BusinessException("单位代码已存在");
            }
            if (!oldDebtUnitDO.getName().equals(reqDTO.getName()) && debtUnitMapper.nameExists(reqDTO)) {
                log.warn("修改单位名称重复");
                throw new BusinessException("单位名称已存在");
            }
            if (!oldDebtUnitDO.getContactTel().equals(reqDTO.getContactTel()) && debtUnitMapper.telExists(reqDTO)) {
                log.warn("修改单位电话重复");
                throw new BusinessException("单位联系电话已存在");
            }
            DebtUnitDO debtUnitDO = DebtTransform.INSTANCE.debtUnitReq2DO(reqDTO);
            debtUnitDO.setGuid(reqDTO.getUnitGuid());
            BigDecimal bigDecimal = debtUnitRecordService.calculateDebtFeeTotal(debtUnitDO.getGuid());
            //更改了信用额度则可用额度也需要更新：总额度-挂账总额
            debtUnitDO.setCreditLimitLeft(debtUnitDO.getCreditLimit().subtract(bigDecimal));
            return this.updateById(debtUnitDO);
        }
    }

    @Override
    public Page<DebtUnitPageRespDTO> unitPage(DebtUnitPageReqDTO reqDTO) {
        IPage<DebtUnitPageRespDTO> respPage = debtUnitMapper.unitPage(new PageAdapter<>(reqDTO), reqDTO);
        return new PageAdapter<>(respPage, respPage.getRecords());
    }

    @Override
    public List<DebtUnitDropdownListDTO> unitDropdownList() {
        return debtUnitMapper.unitDropdownList();
    }

    @Override
    public Boolean creditLimitChange(DebtCreditChangeDTO changeDTO) {
        return debtUnitMapper.creditLimitChange(changeDTO);
    }

    @Override
    public Boolean unitDelete(String unitGuid) {
        if (debtUnitRecordMapper.existsRecord(unitGuid)) {
            log.warn("已存在挂账记录，无法删除");
            throw new BusinessException("该单位已有挂账记录，无法删除");
        }
        return this.removeById(unitGuid);
    }

    @Override
    public Boolean h5Login(DebtUnitLoginH5ReqDTO reqDTO) {
        //h5页面header中无企业信息，手动切库
        dynamicHelper.changeDatasource(reqDTO.getEnterpriseGuid());
        //查询单位信息
        DebtUnitDO unitDO = this.getOne(new LambdaQueryWrapper<DebtUnitDO>()
                .eq(DebtUnitDO::getContactTel, reqDTO.getContactTel()));
        if (unitDO == null) {
            throw new BusinessException("单位不存在");
        }
        if (StringUtils.isBlank(reqDTO.getContactTel())){
            throw new BusinessException("单位联系电话不能为空！");
        }
        //校验查询密码
        if (reqDTO.getPassword().equals(unitDO.getPassword())) {
            return Boolean.TRUE;
        } else {
            throw new BusinessException("密码不正确");
        }
    }
}
