package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseRespDTO;
import com.holderzone.saas.store.dto.common.RedisReqDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemPadCalculateDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.OrderItemReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.TurnMessageDTO;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.req.PadDineInItemDTO;
import com.holderzone.saas.store.dto.trade.req.PadModifyGuestsNoReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadOrderPlacementReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadPayInfoReqDTO;
import com.holderzone.saas.store.dto.trade.resp.*;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.ConfirmConfigTaskDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.common.ResultStateEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.order.OrderStateEnum;
import com.holderzone.saas.store.trade.constants.UpperState;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.dto.PadOrderMsg;
import com.holderzone.saas.store.trade.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.event.delay.PadAutoConfirmListener;
import com.holderzone.saas.store.trade.event.delay.PadConfirmPromptListener;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.mapper.OrderItemMapper;
import com.holderzone.saas.store.trade.mapper.OrderMapper;
import com.holderzone.saas.store.trade.mapper.PadOrderMapper;
import com.holderzone.saas.store.trade.repository.feign.*;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.AppendFeeService;
import com.holderzone.saas.store.trade.service.OrderDetailService;
import com.holderzone.saas.store.trade.service.PadOrderService;
import com.holderzone.saas.store.trade.service.RedisService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.RedisDelayedQueue;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.constant.Constant.*;
import static com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant.HST_ORDER_ITEM;
import static com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant.ITEM_ATTR_GUID;


/**
 * <p>
 * pad订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
@Service
@Slf4j
public class PadOrderServiceImpl extends ServiceImpl<PadOrderMapper, PadOrderDO> implements PadOrderService {

    @Value("${pad.payQrUrl}")
    private String payQrUrl;

    /**
     * 商品标签 0 ：否 1：是
     */
    private static final Integer ITEM_TAG = 1;

    private static final String NEW = "新品";

    private static final String SIGN = "招牌";

    private static final String BESTSELLER = "热销";

    public static final String APPEND_FEE_PREFIX = "appendFee-tableGuid-";

    @Resource
    private OrderItemMapper orderItemMapper;

    private final OrganizationClientService organizationClientService;

    private final WxStoreClientService wxStoreClientService;

    private final TableClientService tableClientService;

    private final DynamicHelper dynamicHelper;

    private final ItemClientService itemClientService;

    private final PriceCalculationUtils priceCalculationUtils;

    private final BusinessMessageService businessMessageService;

    private final RedisService redisService;

    private final RedisDelayedQueue redisDelayedQueue;

    private final OrderService orderService;

    private final OrderMapper orderMapper;

    private final ItemAttrService itemAttrService;

    private final AppendFeeService appendFeeService;

    private final OrderItemService orderItemService;

    private final OrderItemExtendsService orderItemExtendsService;

    private final AppendFeeMpService appendFeeMpService;

    private final OrderDetailService orderDetailService;

    private final RedisTemplate<String, List<AppendFeeDO>> appendFeeRedisTemplate;

    private static final OrderTransform orderTransform = OrderTransform.INSTANCE;

    @Autowired
    public PadOrderServiceImpl(OrganizationClientService organizationClientService, DynamicHelper dynamicHelper,
                               WxStoreClientService wxStoreClientService, TableClientService tableClientService,
                               ItemClientService itemClientService, OrderDetailService orderDetailService,
                               PriceCalculationUtils priceCalculationUtils, BusinessMessageService businessMessageService,
                               RedisService redisService, RedisDelayedQueue redisDelayedQueue, OrderService orderService,
                               OrderMapper orderMapper, ItemAttrService itemAttrService, AppendFeeService appendFeeService,
                               OrderItemService orderItemService, OrderItemExtendsService orderItemExtendsService,
                               AppendFeeMpService appendFeeMpService,
                               RedisTemplate<String, List<AppendFeeDO>> appendFeeRedisTemplate) {
        this.organizationClientService = organizationClientService;
        this.wxStoreClientService = wxStoreClientService;
        this.tableClientService = tableClientService;
        this.dynamicHelper = dynamicHelper;
        this.itemClientService = itemClientService;
        this.priceCalculationUtils = priceCalculationUtils;
        this.businessMessageService = businessMessageService;
        this.redisService = redisService;
        this.redisDelayedQueue = redisDelayedQueue;
        this.orderService = orderService;
        this.orderMapper = orderMapper;
        this.itemAttrService = itemAttrService;
        this.appendFeeService = appendFeeService;
        this.orderItemService = orderItemService;
        this.orderItemExtendsService = orderItemExtendsService;
        this.appendFeeMpService = appendFeeMpService;
        this.orderDetailService = orderDetailService;
        this.appendFeeRedisTemplate = appendFeeRedisTemplate;
    }

    /**
     * pad下单
     *
     * @param orderPlacementReqDTO pad下单请求实体
     * @return 订单guid
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public PadOrderPlacementRespDTO orderPlacement(PadOrderPlacementReqDTO orderPlacementReqDTO) {
        String storeGuid = orderPlacementReqDTO.getStoreGuid();
        if (ObjectUtils.isEmpty(storeGuid)) {
            return PadOrderPlacementRespDTO.upperLimit(STORE_CANNOT_BE_NULL);
        }
        // 给属性增加默认数量1，保证后厨制作没问题
        orderPlacementReqDTO.getDineInItemDTOList().forEach(item -> item.getItemAttrDTOS().forEach(attr -> attr.setNum(1)));

        // 查询门店设备信息
        StoreDeviceDTO masterDevice = organizationClientService.findMasterDevice(storeGuid);
        if (ObjectUtils.isEmpty(masterDevice)) {
            return PadOrderPlacementRespDTO.unCombineMasterDevice();
        }

        // 查询微信门店配置，万一在登陆后又改了
        WxOrderConfigDTO detailConfig = wxStoreClientService.getStoreConfig(storeGuid);
        return Optional.ofNullable(detailConfig).map(x -> {
            Integer orderModel = x.getOrderModel();
            if (ObjectUtils.isEmpty(orderModel)) {
                return PadOrderPlacementRespDTO.unSet();
            }
            return orderModel == 0 ? submitDineOrder(orderPlacementReqDTO) : PadOrderPlacementRespDTO.modelSetError();
        }).orElse(PadOrderPlacementRespDTO.unSet());

    }

    /**
     * 推送订单信息
     *
     * @param padOrderRespDTO pad下单信息
     * @return BusinessMessageDTO
     */
    @Override
    public BusinessMessageDTO pushOrderMsg(PadOrderRespDTO padOrderRespDTO) {
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid(String.valueOf(dynamicHelper.generateGuid(GuidKeyConstant.BUSINESS_MESSAGE_DTO)));
        businessMessageDTO.setMessageType(1);
        businessMessageDTO.setStoreGuid(padOrderRespDTO.getStoreGuid());
        businessMessageDTO.setDetailMessageType(82);
        // 微信反的1，先模仿
        businessMessageDTO.setPlatform(OrderType.PAD_ORDER.getDesc());
        String subject = "您有一个新的" + OrderType.PAD_ORDER.getDesc() + "需要处理。";
        businessMessageDTO.setSubject(subject);
        PadOrderMsg padOrderMsg = new PadOrderMsg();
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(padOrderRespDTO.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
        padOrderMsg.setAutoRev(0);
        padOrderMsg.setGuid(String.valueOf(padOrderRespDTO.getGuid()));
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(padOrderMsg));
        businessMessageDTO.setState("0");
        businessMessageDTO.setCreateTime(LocalDateTime.now());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        log.info("新订单businessMessageDTO:{}", businessMessageDTO);
        businessMessageService.msg(businessMessageDTO);
        return businessMessageDTO;
    }

    /**
     * 推送消息
     *
     * @param padOrderRespDTO pad下单信息
     */
    @Override
    public void pushMsg(PadOrderRespDTO padOrderRespDTO) {
        PadOrderMsg padOrderMsg = new PadOrderMsg();
        padOrderMsg.setGuid(String.valueOf(padOrderRespDTO.getGuid()));
        Integer orderState = padOrderRespDTO.getOrderState();
        padOrderMsg.setOrderState(orderState);
        switch (orderState) {
            case 1:
                padOrderMsg.setBar(2);
                break;
            case 2:
                padOrderMsg.setBar(3);
                break;
            default:
                padOrderMsg.setBar(0);
        }
        log.info("发送给商户端的接单消息初始化：{}", padOrderMsg);
        BusinessMessageDTO businessMessageDTO = pushOrderStateMsg(padOrderRespDTO, padOrderMsg);
        //修改时发消息
        log.info("发送给商户端的接单消息：{}", padOrderMsg);
        businessMessageService.msg(businessMessageDTO);
    }

    /**
     * 状态变更推送消息
     *
     * @param padOrderRespDTO pad预下单信息
     * @param padOrderMsg     pad订单消息
     * @return BusinessMessageDTO
     */
    private BusinessMessageDTO pushOrderStateMsg(PadOrderRespDTO padOrderRespDTO, PadOrderMsg padOrderMsg) {
        log.info("pad订单状态变更 padOrderRespDTO={}", padOrderRespDTO);
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid(String.valueOf(dynamicHelper.generateGuid(GuidKeyConstant.BUSINESS_MESSAGE_DTO)));
        businessMessageDTO.setMessageType(81);
        businessMessageDTO.setStoreGuid(padOrderRespDTO.getStoreGuid());
        businessMessageDTO.setDetailMessageType(81);
        businessMessageDTO.setPlatform(OrderType.PAD_ORDER.getDesc());
        String subject = "有一个微信订单" + OrderType.PAD_ORDER.getDesc() + "状态变更。";
        businessMessageDTO.setSubject(subject);
        WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = new WxStoreAdvanceConsumerReqDTO();
        WxStoreConsumerDTO wxStoreConsumerDTO = new WxStoreConsumerDTO();
        wxStoreAdvanceConsumerReqDTO.setWxStoreConsumerDTO(wxStoreConsumerDTO);
        wxStoreConsumerDTO.setStoreGuid(padOrderRespDTO.getStoreGuid());
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(padOrderRespDTO.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(padOrderMsg));
        businessMessageDTO.setState("0");
        businessMessageDTO.setCreateTime(padOrderRespDTO.getGmtCreate());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        return businessMessageDTO;
    }

    /**
     * 正餐下单
     *
     * @param orderSubmitReqDTO 就餐人数
     * @return 就餐返回
     */
    private PadOrderPlacementRespDTO submitDineOrder(PadOrderPlacementReqDTO orderSubmitReqDTO) {
        String orderGuid = orderSubmitReqDTO.getOrderGuid();
        if (StringUtils.isEmpty(orderGuid)) {
            return PadOrderPlacementRespDTO.upperLimit(ORDER_GUID_IS_EMPTY);
        }
        TableDTO tableDTO = tableClientService.getTableByGuid(orderSubmitReqDTO.getDiningTableGuid());
        if (ObjectUtils.isEmpty(tableDTO)) {
            log.error("查询桌台失败 diningTableGuid={}", orderSubmitReqDTO.getDiningTableGuid());
            return PadOrderPlacementRespDTO.submitFailed();
        }
        List<PadDineInItemDTO> dineItemDTOList = orderSubmitReqDTO.getDineInItemDTOList();
        if (CollectionUtils.isEmpty(dineItemDTOList)) {
            return PadOrderPlacementRespDTO.noItem();
        }
        String msg = orderUpperLimit(dineItemDTOList);
        if (!StringUtils.isEmpty(msg)) {
            return PadOrderPlacementRespDTO.upperLimit(msg);
        }

        try {
            // 校验商品有无不可下单的商品
            OrderItemReqDTO reqDTO = new OrderItemReqDTO();
            Map<String, Integer> skuAndQuantityMap = new HashMap<>();
            dineItemDTOList.forEach(item ->
                    skuAndQuantityMap.put(item.getSkuGuid(), item.getCurrentCount().intValue())
            );
            reqDTO.setSkuAndQuantityMap(skuAndQuantityMap);
            reqDTO.setStoreGuid(orderSubmitReqDTO.getStoreGuid());
            List<ItemPadCalculateDTO> skuList = itemClientService.checkOrderPlacementItem(reqDTO);
            if (CollectionUtils.isNotEmpty(skuList)) {
                return PadOrderPlacementRespDTO.errorItem(skuList);
            }

            // 下单初始化
            PadOrderDO padOrderDO = initialPadOrder(orderSubmitReqDTO);
            PadOrderRespDTO padOrderRespDTO = OrderTransform.INSTANCE.padOrderDO2OrderRespDTO(padOrderDO);

            // 批量加菜
            CreateDineInOrderReqDTO createDineDTO = OrderTransform.INSTANCE.orderPlacementDTO2CreateDineDTO(orderSubmitReqDTO);
            createDineDTO.setDeviceType(5);

            // 将加菜信息存入redis以供接单查询
            String key = padOrderDO.getOrderGuid() + ":" + padOrderDO.getGuid();
            redisService.putPadOrderAddItemInfo(key, createDineDTO);

            // pad在下单的时候保存OrderItem数据
            saveOrderItem(orderGuid, dineItemDTOList, padOrderDO.getGuid(), orderSubmitReqDTO);

            // 自动接单
            autoAccept(UserContextUtils.get(), padOrderRespDTO, orderSubmitReqDTO);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("下单失败 {}", e.getMessage());
            return PadOrderPlacementRespDTO.submitFailed();
        }
        return new PadOrderPlacementRespDTO().setOrderGuid(orderGuid);
    }

    /**
     * 保存orderItem表的数据
     *
     * @param orderGuid       订单guid
     * @param dineItemDTOList pad下单商品
     */
    private void saveOrderItem(String orderGuid, List<PadDineInItemDTO> dineItemDTOList, Long padOrderGuid,
                               PadOrderPlacementReqDTO orderSubmitReqDTO) {
        List<OrderItemDO> orderItemDOS = new ArrayList<>();
        List<OrderItemExtendsDO> orderItemExtendsDOS = new ArrayList<>();
        List<ItemAttrDO> itemAttrDOS = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        //商品标签
        List<String> itemGuidList = dineItemDTOList.stream()
                .map(PadDineInItemDTO::getItemGuid)
                .collect(Collectors.toList());
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(itemGuidList);
        List<ItemInfoRespDTO> itemInfoRespDTOS = itemClientService.selectItemInfoList(listDTO);
        Map<String, ItemInfoRespDTO> itemInfoRespDTOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(itemInfoRespDTOS)) {
            itemInfoRespDTOMap = itemInfoRespDTOS.stream().collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, e -> e));
        }

        for (PadDineInItemDTO dineInItemDTO : dineItemDTOList) {
            OrderItemDO orderItemDO = OrderTransform.INSTANCE.dineInItemDTO2OrderItemDO(dineInItemDTO);
            OrderItemExtendsDO extendsDO = new OrderItemExtendsDO();
            Long orderItemGuid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
            orderItemDO.setGuid(orderItemGuid);
            extendsDO.setGuid(orderItemGuid);
            orderItemDO.setGmtCreate(now);
            orderItemDO.setGmtModified(now);
            orderItemDO.setOrderGuid(Long.valueOf(orderGuid));
            extendsDO.setOrderGuid(Long.valueOf(orderGuid));
            orderItemDO.setCurrentCount(dineInItemDTO.getCurrentCount());
            orderItemDO.setPadOrderGuid(String.valueOf(padOrderGuid));
            extendsDO.setPadOrderGuid(String.valueOf(padOrderGuid));
            orderItemDO.setIsPay(BooleanEnum.FALSE.getCode());
            orderItemDO.setIsWholeDiscount(BooleanEnum.FALSE.getCode());
            orderItemDO.setIsMemberDiscount(BooleanEnum.FALSE.getCode());
            orderItemDO.setFreeCount(BigDecimal.ZERO);
            orderItemDO.setReturnCount(BigDecimal.ZERO);
            orderItemDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
            orderItemDO.setCreateStaffName(UserContextUtils.getUserName());
            orderItemDO.setSmallPicture(Optional.ofNullable(dineInItemDTO.getSmallPicture()).orElse(EMPTY));

            ItemInfoRespDTO itemInfoDTO = itemInfoRespDTOMap.get(dineInItemDTO.getItemGuid());
            handleItemInfo(dineInItemDTO, itemInfoDTO, orderItemDO, extendsDO);

            // 设置商品属性
            if (CollectionUtil.isNotEmpty(dineInItemDTO.getItemAttrDTOS())) {
                for (int j = 0; j < dineInItemDTO.getItemAttrDTOS().size(); j++) {
                    ItemAttrDTO itemAttrDTO = dineInItemDTO.getItemAttrDTOS().get(j);
                    ItemAttrDO itemAttrDO = orderTransform.itemAttrDTO2ItemAttrDO(itemAttrDTO);
                    Long itemAttrGuid = dynamicHelper.generateGuid(ITEM_ATTR_GUID);
                    itemAttrDO.setOrderItemGuid(orderItemGuid);
                    itemAttrDO.setGmtCreate(now);
                    itemAttrDO.setGuid(itemAttrGuid);
                    itemAttrDO.setOrderGuid(Long.valueOf(orderGuid));
                    itemAttrDO.setStoreGuid(orderSubmitReqDTO.getStoreGuid());
                    itemAttrDO.setStoreName(orderSubmitReqDTO.getStoreName());
                    itemAttrDTO.setGuid(String.valueOf(itemAttrGuid));
                    itemAttrDOS.add(itemAttrDO);
                }
                orderItemDO.setHasAttr(1);
            }

            //组装商品标签
            if (ObjectUtil.isNotNull(itemInfoDTO)) {
                StringBuilder str = new StringBuilder();
                //新品
                if (ITEM_TAG.equals(itemInfoDTO.getIsNew())) {
                    str.append(NEW).append("、");
                }
                //招牌
                if (ITEM_TAG.equals(itemInfoDTO.getIsSign())) {
                    str.append(SIGN).append("、");
                }
                //招牌
                if (ITEM_TAG.equals(itemInfoDTO.getIsBestseller())) {
                    str.append(BESTSELLER).append("、");
                }
                if (StringUtils.isNotBlank(str)) {
                    String resultStr = str.substring(0, str.length() - 1);
                    orderItemDO.setTag(resultStr);
                }
            }
            orderItemDOS.add(orderItemDO);
            orderItemExtendsDOS.add(extendsDO);

            //菜品为套餐时
            if (Objects.equals(ItemTypeEnum.GROUP.getCode(), dineInItemDTO.getItemType())) {
                List<PackageSubgroupDTO> packageSubgroupDTOS = dineInItemDTO.getPackageSubgroupDTOS();
                for (PackageSubgroupDTO packageSubgroupDTO : packageSubgroupDTOS) {
                    List<SubDineInItemDTO> subDineInItemDTOS = packageSubgroupDTO.getSubDineInItemDTOS();
                    for (SubDineInItemDTO subDineInItemDTO : subDineInItemDTOS) {
                        if (StringUtils.isEmpty(subDineInItemDTO.getItemTypeGuid())) {
                            throw new ParameterException("菜品分类不能为空");
                        }
                        OrderItemDO subOrderItemDO = OrderTransform.INSTANCE.subDineInItemDTO2OrderItemDO(subDineInItemDTO);
                        Long subOrderItemGuid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
                        subOrderItemDO.setGuid(subOrderItemGuid);
                        subOrderItemDO.setGmtCreate(now);
                        subOrderItemDO.setGmtModified(now);
                        subOrderItemDO.setIsPay(0);
                        if (ObjectUtils.isEmpty(subOrderItemDO.getPrice())) {
                            subOrderItemDO.setPrice(BigDecimal.ZERO);
                        }
                        subOrderItemDO.setOrderGuid(Long.valueOf(orderGuid));
                        subOrderItemDO.setCurrentCount(subDineInItemDTO.getCurrentCount());
                        subOrderItemDO.setFreeCount(BigDecimal.ZERO);
                        subOrderItemDO.setReturnCount(BigDecimal.ZERO);
                        subOrderItemDO.setPackageDefaultCount(subDineInItemDTO.getPackageDefaultCount());
                        subOrderItemDO.setParentItemGuid(orderItemGuid);
                        subOrderItemDO.setSubgroupGuid(packageSubgroupDTO.getSubgroupGuid());
                        subOrderItemDO.setSubgroupName(packageSubgroupDTO.getSubgroupName());
                        subOrderItemDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
                        subOrderItemDO.setCreateStaffName(UserContextUtils.getUserName());
                        if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getAddPrice())) {
                            subOrderItemDO.setAddPrice(subDineInItemDTO.getAddPrice());
                        }
                        subOrderItemDO.setPadOrderGuid(String.valueOf(padOrderGuid));
                        orderItemDOS.add(subOrderItemDO);
                    }
                }
            }
        }
        itemAttrService.saveBatch(itemAttrDOS);
        orderItemService.saveBatch(orderItemDOS);
        orderItemExtendsService.saveBatch(orderItemExtendsDOS);
    }

    private void handleItemInfo(PadDineInItemDTO dineInItemDTO,
                                ItemInfoRespDTO itemInfoDTO,
                                OrderItemDO orderItemDO,
                                OrderItemExtendsDO extendsDO) {
        if (!ObjectUtils.isEmpty(itemInfoDTO)) {
            Map<String, SkuInfoRespDTO> skuInfoRespDTOMap = itemInfoDTO.getSkuList().stream()
                    .collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity(), (dto1, dto2) -> dto2));
            SkuInfoRespDTO skuInfoRespDTO = skuInfoRespDTOMap.get(dineInItemDTO.getSkuGuid());
            if (!ObjectUtils.isEmpty(skuInfoRespDTO)) {
                orderItemDO.setIsWholeDiscount(skuInfoRespDTO.getIsWholeDiscount());
                if (BigDecimalUtil.greaterThanZero(skuInfoRespDTO.getCostPrice())) {
                    extendsDO.setCostPrice(skuInfoRespDTO.getCostPrice());
                }
            }
        }
    }

    private void autoAccept(UserContext userContext, PadOrderRespDTO padOrderRespDTO, PadOrderPlacementReqDTO orderSubmitReqDTO) {
        CompletableFuture.runAsync(() -> {
            userContext.setStoreGuid(orderSubmitReqDTO.getStoreGuid());
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            WxOrderConfigDTO detailConfig = wxStoreClientService.getStoreConfig(orderSubmitReqDTO.getStoreGuid());
            log.info("自动接单：门店配置={}", JacksonUtils.writeValueAsString(detailConfig));
            BusinessMessageDTO businessMessageDTO = this.pushOrderMsg(padOrderRespDTO);
            log.info("下单：异步发送mq:{}", businessMessageDTO);
            this.pushMsg(padOrderRespDTO);

            //自动接单标志
            boolean isAutoFlag = detailConfig != null && detailConfig.getAutoConfirm() != null
                    && detailConfig.getAutoConfirm().equals(1);
            boolean toAutoConfirm = true;

            //已结单的单子
            List<PadOrderDO> list = this.list(new LambdaQueryWrapper<PadOrderDO>()
                    .eq(PadOrderDO::getOrderGuid, orderSubmitReqDTO.getOrderGuid())
                    .eq(PadOrderDO::getOrderState, OrderStateEnum.ACCEPT_ORDER.getCode()));
            if (isAutoFlag) {
                //若自动确认时间为0分钟则立即确认
                if (ObjectUtils.isEmpty(detailConfig.getAutoConfirmTime()) || detailConfig.getAutoConfirmTime().equals(0L)) {
                    toAutoConfirm = false;
                    orderAccept(padOrderRespDTO, orderSubmitReqDTO);
                } else {
                    if (!ObjectUtils.isEmpty(list) && Objects.equals(1, detailConfig.getTakingModel())) {
                        toAutoConfirm = false;
                        orderAccept(padOrderRespDTO, orderSubmitReqDTO);
                    } else {
                        //若自动确认时间大于0则放入延时队列去通知
                        ConfirmConfigTaskDTO taskDTO = ConfirmConfigTaskDTO.builder()
                                .enterpriseGuid(userContext.getEnterpriseGuid())
                                .autoConfirm(true)
                                // pad处当作订单guid处理
                                .orderRecordGuid(orderSubmitReqDTO.getOrderGuid())
                                // pad下单表guid
                                .merchantGuid(String.valueOf(padOrderRespDTO.getGuid()))
                                .storeGuid(orderSubmitReqDTO.getStoreGuid())
                                .storeName(userContext.getStoreName())
                                .userGuid(userContext.getUserGuid())
                                .userName(userContext.getUserName())
                                .account(userContext.getAccount())
                                .deviceId(orderSubmitReqDTO.getDeviceId())
                                .remark(orderSubmitReqDTO.getRemark())
                                .actualGuestsNo(orderSubmitReqDTO.getGuestCount())
                                .takingModel(detailConfig.getTakingModel())
                                .build();
                        redisDelayedQueue.addQueue(taskDTO, detailConfig.getAutoConfirmTime()
                                , TimeUnit.MINUTES, PadAutoConfirmListener.class.getName());
                    }
                }
            } else {
                if (!ObjectUtils.isEmpty(list) && list.size() > 1 && detailConfig != null && detailConfig.getTakingModel().equals(1)) {
                    orderAccept(padOrderRespDTO, orderSubmitReqDTO);
                }
            }
            // 若填写了确认提示推到延时队列进行提醒
            if (detailConfig != null && !ObjectUtils.isEmpty(detailConfig.getConfirmPromptTime()) && toAutoConfirm) {
                ConfirmConfigTaskDTO taskDTO = ConfirmConfigTaskDTO.builder()
                        .enterpriseGuid(userContext.getEnterpriseGuid())
                        .confirmPrompt(true)
                        // pad处当作订单guid处理
                        .orderRecordGuid(orderSubmitReqDTO.getOrderGuid())
                        // pad下单表guid
                        .merchantGuid(String.valueOf(padOrderRespDTO.getGuid()))
                        .storeGuid(orderSubmitReqDTO.getStoreGuid())
                        .storeName(orderSubmitReqDTO.getStoreName())
                        .userGuid(orderSubmitReqDTO.getUserGuid())
                        .userName(orderSubmitReqDTO.getUserName())
                        .account(orderSubmitReqDTO.getAccount())
                        .deviceId(orderSubmitReqDTO.getDeviceId())
                        .remark(orderSubmitReqDTO.getRemark())
                        .confirmPromptTime(detailConfig.getConfirmPromptTime())
                        .takingModel(detailConfig.getTakingModel())
                        .build();
                redisDelayedQueue.addQueue(taskDTO, detailConfig.getConfirmPromptTime()
                        , TimeUnit.MINUTES, PadConfirmPromptListener.class.getName());
            }
        });
    }

    /**
     * 准备调用微信的接单
     * pad接单的逻辑也写在那里面
     *
     * @param padOrderRespDTO   pad下单信息
     * @param orderSubmitReqDTO 下单信息
     */
    private void orderAccept(PadOrderRespDTO padOrderRespDTO, PadOrderPlacementReqDTO orderSubmitReqDTO) {
        WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO();
        wxOperateReqDTO.setGuid(String.valueOf(padOrderRespDTO.getGuid()));
        wxOperateReqDTO.setOrderState(1);
        wxOperateReqDTO.setDeviceId(orderSubmitReqDTO.getDeviceId());
        wxOperateReqDTO.setDeviceType(BaseDeviceTypeEnum.CLOUD.getCode());
        wxOperateReqDTO.setUserGuid(orderSubmitReqDTO.getUserGuid());
        wxOperateReqDTO.setUserName(orderSubmitReqDTO.getUserName());
        wxOperateReqDTO.setActualGuestsNo(orderSubmitReqDTO.getGuestCount());
        wxOperateReqDTO.setRemark(orderSubmitReqDTO.getRemark());
        wxStoreClientService.operationMerchantOrder(wxOperateReqDTO);
    }

    /**
     * pad下单初始化
     *
     * @param orderSubmitReqDTO pad下单请求实体
     * @return PadOrderDO
     */
    private PadOrderDO initialPadOrder(PadOrderPlacementReqDTO orderSubmitReqDTO) {
        String orderGuid = orderSubmitReqDTO.getOrderGuid();
        String storeGuid = orderSubmitReqDTO.getStoreGuid();
        PadOrderDO padOrderDO = new PadOrderDO();
        Long padOrderGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_PAD_ORDER);
        padOrderDO.setGuid(padOrderGuid);
        padOrderDO.setOrderGuid(orderGuid);
        List<PadOrderDO> padOrderDOList = this.list(new LambdaQueryWrapper<PadOrderDO>()
                .eq(PadOrderDO::getOrderGuid, orderGuid));
        padOrderDO.setPadBatch(padOrderDOList.size() + 1);
        padOrderDO.setTotalPrice(orderSubmitReqDTO.getTotalPrice());
        padOrderDO.setActualGuestsNo(orderSubmitReqDTO.getGuestCount());
        padOrderDO.setOrderState(OrderStateEnum.PENDING.getCode());
        padOrderDO.setDenialReason("");
        padOrderDO.setTradeMode(TradeModeEnum.DINEIN.getCode());
        padOrderDO.setStoreGuid(storeGuid);
        padOrderDO.setRemark(orderSubmitReqDTO.getRemark());
        padOrderDO.setItemCount(orderSubmitReqDTO.getDineInItemDTOList().size());
        padOrderDO.setOrderSource(orderSubmitReqDTO.getDeviceType());
        padOrderDO.setDiningTableGuid(orderSubmitReqDTO.getDiningTableGuid());
        padOrderDO.setTableCode(orderSubmitReqDTO.getDiningTableName());
        padOrderDO.setAreaGuid(orderSubmitReqDTO.getAreaGuid());
        padOrderDO.setAreaName(orderSubmitReqDTO.getAreaName());
        OrderDO orderDO = orderService.getById(orderGuid);
        if (Objects.equals(UpperStateEnum.SUB.getCode(), orderDO.getUpperState())) {
            // 下单的是子桌
            Long mainOrderGuid = orderDO.getMainOrderGuid();
            padOrderDO.setCombineOrderGuid(String.valueOf(mainOrderGuid));

            // 并台情况下推送需要推送除自己的其他设备消息
            List<OrderDO> orderDOList = orderService.list(new LambdaQueryWrapper<OrderDO>()
                    .eq(OrderDO::getMainOrderGuid, mainOrderGuid));
            orderDOList.removeIf(o -> Objects.equals(orderGuid, String.valueOf(o.getGuid())));
            OrderDO mainOrderDO = orderService.getById(mainOrderGuid);
            orderDOList.add(mainOrderDO);
            List<String> tableGuidList = orderDOList.stream()
                    .map(OrderDO::getDiningTableGuid)
                    .distinct()
                    .collect(Collectors.toList());
            combineSubmitOrderPushMsg(orderDO, tableGuidList);
        } else if (Objects.equals(UpperStateEnum.MAIN.getCode(), orderDO.getUpperState())) {
            // 查询是否是主单
            List<OrderDO> subList = orderService.list(new LambdaQueryWrapper<OrderDO>()
                    .eq(OrderDO::getMainOrderGuid, orderGuid));
            if (!CollectionUtils.isEmpty(subList)) {
                // 下单的是主桌
                padOrderDO.setCombineOrderGuid(orderGuid);

                // 并台情况下推送需要推送除自己的其他设备消息
                List<String> tableGuidList = subList.stream()
                        .map(OrderDO::getDiningTableGuid)
                        .distinct()
                        .collect(Collectors.toList());
                combineSubmitOrderPushMsg(orderDO, tableGuidList);
            }
        }
        this.save(padOrderDO);
        return padOrderDO;
    }

    /**
     * 并台下单推送消息
     *
     * @param orderDO       门店guid,门店名字
     * @param tableGuidList 桌台guid列表
     */
    private void combineSubmitOrderPushMsg(OrderDO orderDO, List<String> tableGuidList) {
        String storeGuid = orderDO.getStoreGuid();
        BusinessMessageDTO mainTableMessageDTO = new BusinessMessageDTO();
        mainTableMessageDTO.setSubject(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getName());
        mainTableMessageDTO.setContent("并台下单消息");
        mainTableMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        mainTableMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getId());
        mainTableMessageDTO.setPlatform("2");
        mainTableMessageDTO.setStoreGuid(storeGuid);
        mainTableMessageDTO.setStoreName(orderDO.getStoreName());

        // 查询子桌设备号
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(storeGuid);
        reqDTO.setTableGuidList(tableGuidList);
        List<StoreDeviceDTO> storeDeviceDTOList = organizationClientService.listDeviceByStoreTable(reqDTO);
        if (CollectionUtils.isEmpty(storeDeviceDTOList)) {
            log.error("门店桌台对应设备信息列表为空 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        storeDeviceDTOList.forEach(device -> {
            mainTableMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + device.getDeviceNo());
            log.info("并台下单消息 mainTableMessageDTO={}", JacksonUtils.writeValueAsString(mainTableMessageDTO));
            businessMessageService.msg(mainTableMessageDTO);
        });
    }

    /**
     * 订单超重判断
     * pad点餐暂时不会有称重商品
     *
     * @param itemInfoDTOS 商品集合
     * @return 订单超重判断
     */
    private String orderUpperLimit(List<PadDineInItemDTO> itemInfoDTOS) {
        if (!ObjectUtils.isEmpty(itemInfoDTOS)) {
            boolean isWeight = itemInfoDTOS.stream().anyMatch(x -> x.getItemType() == 3);
            if (isWeight) {
                BigDecimal bigDecimal = itemInfoDTOS.stream().map(x -> x.getItemType() == 3 ? BigDecimal.ZERO
                        : x.getCurrentCount()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                if (bigDecimal.intValue() > 9999.999) {
                    return "单次下单数量不可超过9999.999";
                }
            } else {
                BigDecimal bigDecimal = itemInfoDTOS.stream().map(x -> x.getItemType() == 3 ? BigDecimal.ZERO
                        : x.getCurrentCount()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                if (bigDecimal.intValue() > 9999) {
                    return "单次下单数量不可超过9999";
                }
            }
        }
        return null;
    }

    /**
     * pad购物车价格计算
     *
     * @param orderPlacementReqDTO 购物车商品数据
     * @return 购物车总价
     */
    @Override
    public PadPriceRespDTO calculateShopCar(PadOrderPlacementReqDTO orderPlacementReqDTO) {
        List<PadDineInItemDTO> dineItemDTOList = orderPlacementReqDTO.getDineInItemDTOList();
        if (CollectionUtils.isEmpty(dineItemDTOList)) {
            return PadPriceRespDTO.returnEmpty();
        }
        String msg = orderUpperLimit(dineItemDTOList);
        if (!StringUtils.isEmpty(msg)) {
            throw new BusinessException(msg);
        }
        return priceCalculationUtils.shopCarPrice(dineItemDTOList);
    }

    /**
     * 查询门店下的下单表
     *
     * @param request 订单
     * @return 下单列表
     */
    @Override
    public List<PadOrderRespDTO> listPadOrder(WxStoreMerchantOrderReqDTO request) {
        String storeGuid = UserContextUtils.getStoreGuid();
        Assert.hasText(storeGuid, "门店guid不能为空");
        LambdaQueryWrapper<PadOrderDO> wrapper = new LambdaQueryWrapper<PadOrderDO>()
                .eq(PadOrderDO::getStoreGuid, storeGuid)
                .eq(PadOrderDO::getTradeMode, TradeModeEnum.DINEIN.getCode())
                .and(w -> w.eq(!ObjectUtils.isEmpty(request.getTableCode()), PadOrderDO::getTableCode, request.getTableCode())
                        .or()
                        .eq(PadOrderDO::getMemberPhone, request.getTableCode())
                )
                .eq(!ObjectUtils.isEmpty(request.getTableCode()), PadOrderDO::getTableCode, request.getTableCode())
                .eq(!ObjectUtils.isEmpty(request.getGuid()), PadOrderDO::getGuid, request.getGuid())
                .ge(!ObjectUtils.isEmpty(request.getStartTime()), PadOrderDO::getGmtCreate, request.getStartTime())
                .le(!ObjectUtils.isEmpty(request.getEndTime()), PadOrderDO::getGmtCreate, request.getEndTime());
        if (!ObjectUtils.isEmpty(request.getOrderState())) {
            wrapper.eq(PadOrderDO::getOrderState, request.getOrderState());
        } else {
            wrapper.in(PadOrderDO::getOrderState, Arrays.asList(0, 1, 2));
        }
        String lastGuid = request.getLastGuid();
        if (!StringUtils.isEmpty(lastGuid)) {
            PadOrderDO lastDO = getById(lastGuid);
            if (!ObjectUtils.isEmpty(lastDO) && !ObjectUtils.isEmpty(lastDO.getGmtCreate())) {
                wrapper = wrapper.lt(PadOrderDO::getGmtCreate, lastDO.getGmtCreate())
                        .ne(PadOrderDO::getGuid, lastGuid);
            }
        }
        wrapper.orderByDesc(PadOrderDO::getGmtModified);

        List<PadOrderDO> padOrderDOList = list(wrapper.last("limit " + request.getCount()));
        return OrderTransform.INSTANCE.padOrderDOList2OrderRespDTOList(padOrderDOList);
    }

    /**
     * 根据guid查询pad下单信息
     *
     * @param guid 下单表guid
     * @return 下单信息
     */
    @Override
    public PadOrderRespDTO getPadOrderByGuid(String guid) {
        if (StringUtils.isEmpty(guid)) {
            log.error(PAD_ORDER_GUID_IS_EMPTY);
            return new PadOrderRespDTO();
        }
        PadOrderDO padOrderDO = this.getById(guid);
        if (ObjectUtils.isEmpty(padOrderDO)) {
            log.warn(NO_RESULTS_FOUND + "guid={}", guid);
            return new PadOrderRespDTO();
        }
        return OrderTransform.INSTANCE.padOrderDO2OrderRespDTO(padOrderDO);
    }

    /**
     * 远程调用获取pad下单的加菜商品信息
     *
     * @param key 订单guid+下单表guid，以:分割
     * @return 加菜商品信息
     */
    @Override
    public CreateDineInOrderReqDTO getPadOrderAddItemInfoByRedis(String key) {
        return redisService.getPadOrderAddItemInfo(key);
    }

    /**
     * 功能描述：门店配置延迟处理订单确认和提示
     *
     * @param taskDTO 延时任务处理实体
     * @date 2021.08.31
     */
    @Override
    public void dealRedisDelayedTask(ConfirmConfigTaskDTO taskDTO) {
        if (ObjectUtils.isEmpty(taskDTO)) {
            log.warn("taskDTO为空");
            return;
        }
        UserContext useContext = UserContext.builder().enterpriseGuid(taskDTO.getEnterpriseGuid())
                .storeGuid(taskDTO.getStoreGuid())
                .storeName(taskDTO.getStoreName())
                .account(taskDTO.getAccount())
                .userGuid(taskDTO.getUserGuid())
                .userName(taskDTO.getUserName())
                .build();
        UserContextUtils.put(useContext);
        EnterpriseIdentifier.setEnterpriseGuid(taskDTO.getEnterpriseGuid());

        //去查询此单是否已经接单
        String padOrderGuid = taskDTO.getMerchantGuid();
        if (StringUtils.isEmpty(padOrderGuid)) {
            log.warn("padOrderGuid为空");
            return;
        }
        PadOrderDO orderDO = this.getById(padOrderGuid);
        if (ObjectUtils.isEmpty(orderDO)) {
            log.warn("orderDO为空");
            return;
        }
        if (!Objects.equals(orderDO.getOrderState(), 0)) {
            log.warn("pad下单数据状态不为待处理 padOrderGuid={}", padOrderGuid);
            return;
        }
        PadOrderRespDTO padOrderRespDTO = OrderTransform.INSTANCE.padOrderDO2OrderRespDTO(orderDO);

        //若是自动确认
        if (Objects.nonNull(taskDTO.getAutoConfirm()) && taskDTO.getAutoConfirm()) {
            WxStoreMerchantOrderDTO dto = new WxStoreMerchantOrderDTO();
            dto.setRemark(orderDO.getRemark());
            dto.setActualGuestsNo(taskDTO.getActualGuestsNo());
            dealOrderAutoConfirm(taskDTO, padOrderRespDTO);
        }

        //若是确认提示
        if (Objects.nonNull(taskDTO.getConfirmPrompt()) && taskDTO.getConfirmPrompt()) {
            //若是确认提示需要轮询发送
            redisDelayedQueue.addQueue(taskDTO, taskDTO.getConfirmPromptTime()
                    , TimeUnit.MINUTES, PadConfirmPromptListener.class.getName());
            WxStoreMerchantOrderDTO dto = new WxStoreMerchantOrderDTO();
            dto.setStoreGuid(taskDTO.getStoreGuid());
            dto.setGuid(taskDTO.getGuid());
            dto.setRemark(orderDO.getRemark());
            dealOrderConfirmPrompt(padOrderRespDTO, taskDTO);
        }
    }

    /**
     * 交易订单自动确认
     *
     * @param taskDTO         任务传入参数
     * @param padOrderRespDTO pad下单的参数
     */
    private void dealOrderAutoConfirm(ConfirmConfigTaskDTO taskDTO, PadOrderRespDTO padOrderRespDTO) {
        if (ObjectUtils.isEmpty(taskDTO.getTakingModel())) {
            return;
        }

        //所有订单均需确认
        if (Objects.equals(0, taskDTO.getTakingModel())) {
            prepareAcceptOrder(taskDTO, padOrderRespDTO);
        }

        //首单需确认
        if (Objects.equals(1, taskDTO.getTakingModel())) {
            List<PadOrderDO> list = this.list(new LambdaQueryWrapper<PadOrderDO>()
                    .eq(PadOrderDO::getOrderState, 0)
                    .eq(PadOrderDO::getOrderGuid, taskDTO.getOrderRecordGuid())
                    .orderByAsc(PadOrderDO::getGmtCreate)
            );
            if (!CollectionUtils.isEmpty(list)) {
                for (PadOrderDO orderDO : list) {
                    PadOrderRespDTO padOrderResp = OrderTransform.INSTANCE.padOrderDO2OrderRespDTO(orderDO);
                    prepareAcceptOrder(taskDTO, padOrderResp);
                }
            }
        }
    }

    /**
     * 准备调用接单
     *
     * @param taskDTO         任务传入参数
     * @param padOrderRespDTO pad下单的参数
     */
    private void prepareAcceptOrder(ConfirmConfigTaskDTO taskDTO, PadOrderRespDTO padOrderRespDTO) {
        PadOrderPlacementReqDTO dto = new PadOrderPlacementReqDTO();
        dto.setDeviceId(taskDTO.getDeviceId());
        dto.setUserGuid(taskDTO.getUserGuid());
        dto.setUserName(taskDTO.getUserName());
        dto.setRemark(taskDTO.getRemark());
        dto.setGuestCount(taskDTO.getActualGuestsNo());
        orderAccept(padOrderRespDTO, dto);
    }

    /**
     * 交易订单确认提示
     *
     * @param padOrderRespDTO pad下单表
     * @param taskDTO         门店自动接单和确认配置
     */
    private void dealOrderConfirmPrompt(PadOrderRespDTO padOrderRespDTO, ConfirmConfigTaskDTO taskDTO) {
        if (ObjectUtils.isEmpty(taskDTO.getTakingModel())) {
            return;
        }

        //所有订单均需确认
        if (Objects.equals(0, taskDTO.getTakingModel())) {
            BusinessMessageDTO businessMessageDTO = this.pushOrderMsg(padOrderRespDTO);
            log.info("下单：异步发送emq:{}", businessMessageDTO);
        }

        //首单需确认
        if (Objects.equals(1, taskDTO.getTakingModel())) {
            List<PadOrderDO> list = this.list(new LambdaQueryWrapper<PadOrderDO>()
                    .eq(PadOrderDO::getOrderState, 1)
                    .eq(PadOrderDO::getOrderGuid, taskDTO.getOrderRecordGuid())
            );
            if (CollectionUtils.isEmpty(list)) {
                BusinessMessageDTO businessMessageDTO = this.pushOrderMsg(padOrderRespDTO);
                log.info("下单：异步发送emq:{}", businessMessageDTO);
            }
        }
    }

    /**
     * 查询pad订单详情
     *
     * @param orderGuid 订单guid
     * @return pad订单详情
     */
    @Override
    public PadOrderInfoRespDTO queryPadOrderInfo(String orderGuid) {
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException(ORDER_GUID_IS_EMPTY);
        }

        PadOrderInfoRespDTO infoRespDTO = new PadOrderInfoRespDTO();
        OrderDO orderDO = orderService.getById(orderGuid);
        if (ObjectUtils.isEmpty(orderDO)) {
            log.warn("未查询到订单 orderGuid={}", orderGuid);
            return infoRespDTO;
        }
        infoRespDTO.setOrderGuid(orderGuid);
        infoRespDTO.setState(orderDO.getState());
        infoRespDTO.setActualGuestsNo(orderDO.getGuestCount());

        // 查询是否开启线上买单
        WxOrderConfigDTO storeConfig = wxStoreClientService.getStoreConfig(orderDO.getStoreGuid());
        if (ObjectUtils.isEmpty(storeConfig)) {
            log.info("微信门店未配置 storeGuid={}", orderDO.getStoreGuid());
            return infoRespDTO;
        }
        infoRespDTO.setIsOnlinePayed(storeConfig.getIsOnlinePayed());

        // 计算附加费
        BigDecimal allAppendFee = calculateAllAppendFee(orderGuid);

        // 附加费列表
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        singleDataDTO.setData(orderGuid);
        List<AppendFeeDetailDTO> appendFeeList = appendFeeService.appendFeeList(singleDataDTO);
        infoRespDTO.setAppendFeeList(appendFeeList);

        // pad点餐批次信息
        List<PadOrderBatchInfoRespDTO> batchInfoList = getBatchInfoRespDTOList(orderGuid);
        infoRespDTO.setBatchInfoList(batchInfoList);
        if (CollectionUtils.isEmpty(batchInfoList)) {
            log.info("点餐批次信息为空 orderGuid={}", orderGuid);
            infoRespDTO.setOrderTotalPrice(PadPriceRespDTO.returnEmpty()
                    .setOriginPrice(allAppendFee)
                    .setMemberPrice(allAppendFee)
            );
            return infoRespDTO;
        }

        // 只算已接单的
        List<PadOrderBatchInfoRespDTO> batchInfoDTOS = batchInfoList.stream()
                .filter(batch -> Objects.equals(OrderStateEnum.ACCEPT_ORDER.getCode(), batch.getOrderState()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchInfoDTOS)) {
            log.info(RECEIVED_ORDER_BATCH_INFOR_IS_EMPTY);
            infoRespDTO.setOrderTotalPrice(PadPriceRespDTO.returnEmpty()
                    .setOriginPrice(allAppendFee)
                    .setMemberPrice(allAppendFee)
            );
            return infoRespDTO;
        }

        // 订单总价计算
        PadPriceRespDTO priceDTO = padPriceCalculate(allAppendFee, batchInfoDTOS);
        infoRespDTO.setOrderTotalPrice(priceDTO);
        return infoRespDTO;
    }

    /**
     * 计算附加费金额
     *
     * @param orderDO 订单
     */
    private void calculateAppendFeeAndUpdate(OrderDO orderDO) {
        BigDecimal appendFee = appendFeeService.getAndUpdateAppendFee(orderDO,null);
        boolean isModified = false;
        if (orderDO.getAppendFee().compareTo(appendFee) != 0) {
            isModified = true;
        }
        orderDO.setAppendFee(appendFee);
        boolean isMainOrder = Objects.equals(1, orderDO.getUpperState());
        if (isModified && !isMainOrder) {
            orderService.updateById(orderDO);
        }
    }

    /**
     * 计算pad价格
     *
     * @param allAppendFee  总的附加费
     * @param batchInfoDTOS 批次信息
     * @return pad价格
     */
    private PadPriceRespDTO padPriceCalculate(BigDecimal allAppendFee, List<PadOrderBatchInfoRespDTO> batchInfoDTOS) {
        PadPriceRespDTO priceDTO = new PadPriceRespDTO();
        BigDecimal originPrice = batchInfoDTOS.stream()
                .map(b -> b.getPriceRespDTO().getOriginPrice())
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        BigDecimal memberPrice = batchInfoDTOS.stream()
                .map(b -> b.getPriceRespDTO().getMemberPrice())
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        priceDTO.setOriginPrice(originPrice.add(allAppendFee))
                .setMemberPrice(memberPrice.add(allAppendFee));
        return priceDTO;
    }

    /**
     * 计算总的附加费，包括并台的情况
     *
     * @param orderGuid 订单guid
     * @return 总的附加费
     */
    private BigDecimal calculateAllAppendFee(String orderGuid) {
        //获取全部的订单
        OrderDO mainOrderDO = null;
        // 查询订单（合并的订单有多条记录）
        List<OrderDO> allOrderList = orderMapper.selectByGuidOrMainOrderGuid(orderGuid);
        List<OrderDO> subOrderDOS = new ArrayList<>();
        for (OrderDO orderDO : allOrderList) {
            // 计算附加费
            calculateAppendFeeAndUpdate(orderDO);
            if (orderDO.getUpperState() != UpperState.SUB_ORDER) {
                //主订单
                mainOrderDO = orderDO;
            } else {
                //子订单
                subOrderDOS.add(orderDO);
            }
        }
        if (mainOrderDO == null) {
            throw new BusinessException("无法找到主订单");
        }

        // 计算总的附加费
        BigDecimal mainAppendFee = mainOrderDO.getAppendFee();
        BigDecimal subBigDecimal = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(subOrderDOS)) {
            subBigDecimal = subOrderDOS.stream()
                    .map(OrderDO::getAppendFee)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
        }
        return mainAppendFee.add(subBigDecimal);
    }

    /**
     * 通过订单guid查询pad点餐批次信息
     *
     * @param orderGuid 订单guid
     * @return pad点餐批次信息
     */
    private List<PadOrderBatchInfoRespDTO> getBatchInfoRespDTOList(String orderGuid) {
        StopWatch stopWatch = new StopWatch();
        List<PadOrderBatchInfoRespDTO> batchInfoList = new ArrayList<>();
        stopWatch.start("getPadOrderDOList耗时" + "-orderGuid：" + orderGuid);
        List<PadOrderDO> padOrderDOList = getPadOrderDOList(orderGuid);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(padOrderDOList)) {
            log.warn(stopWatch.prettyPrint());
            log.warn("通过订单guid查询pad点餐批次信息 pad下单数据为空 orderGuid={}", orderGuid);
            return batchInfoList;
        }

        // 每个下单批次信息
        ArrayList<Integer> list = new ArrayList<>();
        padOrderDOList.forEach(po -> {
            PadOrderBatchInfoRespDTO dto = new PadOrderBatchInfoRespDTO();
            dto.setPadOrderGuid(po.getGuid());

            if (ObjectUtil.isNull(po.getPadBatch())) {
                dto.setPadBatch(list.size() + 1);
                list.add(list.size() + 1);
            } else {
                // 并台下的订单批次要顺序展示
                if (list.contains(po.getPadBatch())) {
                    dto.setPadBatch(list.size() + 1);
                    list.add(list.size() + 1);
                } else {
                    dto.setPadBatch(po.getPadBatch());
                    list.add(po.getPadBatch());
                }
            }

            dto.setOrderState(po.getOrderState());

            // pad点餐批次商品信息
            stopWatch.start("getBatchItemInfoRespDTOList耗时" + "-padOrderGuid：" + po.getGuid());
            List<PadOrderBatchItemInfoRespDTO> batchItemInfoList = getBatchItemInfoRespDTOList(po);
            stopWatch.stop();
            dto.setBatchItemInfoList(batchItemInfoList);

            // 下单消费小计
            stopWatch.start("calculateConsumption耗时");
            PadPriceRespDTO priceDTO = calculateConsumption(batchItemInfoList);
            stopWatch.stop();
            dto.setPriceRespDTO(priceDTO);
            batchInfoList.add(dto);
        });
        log.warn(stopWatch.prettyPrint());
        return batchInfoList;
    }

    /**
     * 获取所有订单的pad下单数据
     *
     * @param orderGuid 当前订单guid
     * @return pad下单数据
     */
    public List<PadOrderDO> getPadOrderDOList(String orderGuid) {
        ArrayList<String> orderIdList = new ArrayList<>();
        List<PadOrderDO> padOrderDOList = new ArrayList<>();
        List<OrderDO> orderDOList = new ArrayList<>();
        OrderDO orderDO = orderService.getById(orderGuid);

        if (Objects.equals(UpperStateEnum.SUB.getCode(), orderDO.getUpperState())) {
            // 是子单
            orderDOList = orderService.list(new LambdaQueryWrapper<OrderDO>()
                    .eq(OrderDO::getMainOrderGuid, orderDO.getMainOrderGuid()));
            orderIdList.add(String.valueOf(orderDO.getMainOrderGuid()));
        } else if (Objects.equals(UpperStateEnum.MAIN.getCode(), orderDO.getUpperState())) {
            // 是主单
            orderDOList = orderService.list(new LambdaQueryWrapper<OrderDO>()
                    .eq(OrderDO::getMainOrderGuid, orderGuid));
            if (!CollectionUtils.isEmpty(orderDOList)) {
                orderIdList.add(orderGuid);
            }
        } else {
            orderIdList.add(orderGuid);
        }

        if (!CollectionUtils.isEmpty(orderDOList)) {
            for (OrderDO order : orderDOList) {
                orderIdList.add(String.valueOf(order.getGuid()));
            }
        }

        List<String> orderGuidList = orderIdList.stream()
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderGuidList)) {
            padOrderDOList = this.list(new LambdaQueryWrapper<PadOrderDO>()
                    .in(PadOrderDO::getOrderGuid, orderGuidList)
                    .eq(PadOrderDO::getIsDelete, BooleanEnum.FALSE.getCode())
            );
            //过滤出不是pad订单的订单信息
            if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(padOrderDOList)) {
                List<String> inPadOrderGuidList = padOrderDOList.stream().map(PadOrderDO::getOrderGuid).collect(Collectors.toList());
                List<String> notPadOrderGuidList = orderGuidList.stream().filter(obj -> !inPadOrderGuidList.contains(obj)).collect(Collectors.toList());
                if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(notPadOrderGuidList)) {
                    List<OrderDO> notPadOrderList = orderService.list(new LambdaQueryWrapper<OrderDO>()
                            .in(OrderDO::getGuid, notPadOrderGuidList)
                            .eq(OrderDO::getState, OrderStateEnum.ACCEPT_ORDER.getCode())
                            .eq(OrderDO::getIsDelete, BooleanEnum.FALSE.getCode()));

                    if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(notPadOrderList)) {
                        List<PadOrderDO> notPadOrderDOList = new ArrayList<>();
                        notPadOrderList.forEach(obj -> {
                            PadOrderDO padOrderDO = new PadOrderDO();
                            padOrderDO.setOrderGuid(String.valueOf(obj.getGuid()));
                            padOrderDO.setOrderState(OrderStateEnum.ACCEPT_ORDER.getCode());
                            notPadOrderDOList.add(padOrderDO);
                        });
                        padOrderDOList.addAll(notPadOrderDOList);
                    }
                }
            }

        }
        return padOrderDOList;
    }

    /**
     * 计算下单消费小计
     *
     * @param batchItemInfoList 批次商品信息
     * @return 下单消费小计
     */
    private PadPriceRespDTO calculateConsumption(List<PadOrderBatchItemInfoRespDTO> batchItemInfoList) {
        PadPriceRespDTO priceDTO = new PadPriceRespDTO();
        BigDecimal originPrice = batchItemInfoList.stream()
                .map(PadOrderBatchItemInfoRespDTO::getPrice)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        BigDecimal memberPrice = batchItemInfoList.stream()
                .map(PadOrderBatchItemInfoRespDTO::getMemberPrice)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        priceDTO.setMemberPrice(memberPrice).setOriginPrice(originPrice);
        return priceDTO;
    }

    /**
     * 获取pad点餐批次商品信息
     *
     * @param po 下单信息
     * @return pad点餐批次商品信息
     */
    private List<PadOrderBatchItemInfoRespDTO> getBatchItemInfoRespDTOList(PadOrderDO po) {
        // 订单商品
        List<OrderItemDO> orderItemDOList;
        StopWatch stopWatch = new StopWatch();
        if (ObjectUtil.isNull(po.getGuid())) {
            stopWatch.start("selectListByOrderGuid耗时" + "-orderGuid：" + po.getOrderGuid());
            orderItemDOList = orderItemMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                    .eq(OrderItemDO::getOrderGuid, po.getOrderGuid())
                    .eq(OrderItemDO::getIsDelete, BooleanEnum.FALSE.getCode())
            );
        } else {
            stopWatch.start("selectListByPadOrderGuid耗时" + "-padOrderGuid：" + po.getGuid());
            orderItemDOList = orderItemMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                    .eq(OrderItemDO::getPadOrderGuid, String.valueOf(po.getGuid()))
                    .eq(OrderItemDO::getIsDelete, BooleanEnum.FALSE.getCode())
            );
        }
        stopWatch.stop();

        if (CollectionUtils.isEmpty(orderItemDOList)) {
            log.warn("pad下单商品为空 padOrderGuid={}", po.getGuid());
            return new ArrayList<>();
        }

        Map<String, List<OrderItemDO>> orderItemMap;
        if (ObjectUtil.isNull(po.getGuid())) {
            // 下单商品
            orderItemMap = orderItemDOList.stream()
                    .collect(Collectors.groupingBy((obj -> String.valueOf(obj.getOrderGuid()))));

        } else {
            // 下单商品
            orderItemMap = orderItemDOList.stream()
                    .collect(Collectors.groupingBy((OrderItemDO::getPadOrderGuid)));
        }

        // 套餐子商品
        Map<Long, List<OrderItemDO>> subItemMap = new HashMap<>();
        List<Long> packageItemGuidList = orderItemDOList.stream()
                .filter(oi -> Objects.equals(1, oi.getItemType()))
                .map(OrderItemDO::getGuid)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(packageItemGuidList)) {
            List<OrderItemDO> subItemList;
            if (ObjectUtil.isNull(po.getGuid())) {
                stopWatch.start("selectListByOrderGuidAndPItem耗时" + "-orderGuid：" + po.getOrderGuid());
                subItemList = orderItemMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                        .in(OrderItemDO::getParentItemGuid, packageItemGuidList)
                        .eq(OrderItemDO::getOrderGuid, po.getOrderGuid())
                        .eq(OrderItemDO::getIsDelete, BooleanEnum.FALSE.getCode())
                );
            } else {
                stopWatch.start("selectListByPadOrderGuidAndPItem耗时" + "-padOrderGuid：" + po.getGuid());
                subItemList = orderItemMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                        .in(OrderItemDO::getParentItemGuid, packageItemGuidList)
                        .eq(OrderItemDO::getPadOrderGuid, String.valueOf(po.getGuid()))
                        .eq(OrderItemDO::getIsDelete, BooleanEnum.FALSE.getCode())
                );
            }
            stopWatch.stop();
            if (!CollectionUtils.isEmpty(subItemList)) {
                subItemMap = subItemList.stream().collect(Collectors.groupingBy((OrderItemDO::getParentItemGuid)));
            }
        }
        log.warn(stopWatch.prettyPrint());
        log.info("套餐子商品 subItemMap={}", JacksonUtils.writeValueAsString(subItemMap));

        // 查询订单商品对应属性
        List<Long> orderItemGuidList = orderItemDOList.stream()
                .map(OrderItemDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<ItemAttrDO> itemAttrDOList = itemAttrService.listByItemGuids(orderItemGuidList);
        Map<Long, List<ItemAttrDO>> attrMap = itemAttrDOList.stream()
                .collect(Collectors.groupingBy((ItemAttrDO::getOrderItemGuid)));

        List<PadOrderBatchItemInfoRespDTO> batchItemInfoList = new ArrayList<>();

        List<OrderItemDO> orderItemList;
        if (ObjectUtil.isNull(po.getGuid())) {
            orderItemList = orderItemMap.get(String.valueOf(po.getOrderGuid()));
        } else {
            orderItemList = orderItemMap.get(String.valueOf(po.getGuid()));
        }

        if (CollectionUtils.isEmpty(orderItemList)) {
            log.warn("订单批次商品为空");
            return batchItemInfoList;
        }
        for (OrderItemDO oi : orderItemList) {
            // 跳过套餐的子商品
            if (!Objects.equals(0L, oi.getParentItemGuid())) {
                continue;
            }
            PadOrderBatchItemInfoRespDTO itemDTO = new PadOrderBatchItemInfoRespDTO();
            itemDTO.setItemGuid(oi.getItemGuid());
            itemDTO.setItemName(oi.getItemName());
            itemDTO.setItemType(oi.getItemType());
            BigDecimal addPrice = BigDecimal.ZERO;

            // 组装规格和属性
            StringBuilder desc = new StringBuilder();
            if (!ObjectUtils.isEmpty(oi.getHasAttr()) && Objects.equals(BooleanEnum.TRUE.getCode(), oi.getHasAttr())) {
                // 属性只可能存在于单品和多规格
                List<ItemAttrDO> attrDOList = attrMap.get(oi.getGuid());
                if (CollectionUtils.isEmpty(attrDOList)) {
                    log.error("未查询到属性 orderItemGuid={}", oi.getGuid());
                    continue;
                }
                if (!Objects.equals(ItemTypeEnum.SINGLE.getCode(), oi.getItemType())) {
                    desc.append(oi.getSkuName()).append("/");
                }
                attrDOList.forEach(attr -> desc.append(attr.getAttrName()).append("/"));

                String skuAttrDescString = desc.toString();
                if (skuAttrDescString.endsWith("/")) {
                    skuAttrDescString = desc.substring(0, desc.length() - 1);
                }
                itemDTO.setSkuAttrDescString(skuAttrDescString);

                // 计算属性总价
                BigDecimal attrTotal = attrDOList.stream()
                        .map(i -> i.getAttrPrice().multiply(oi.getCurrentCount()))
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
                itemDTO.setAttrTotal(attrTotal);
            } else {
                // 无属性
                itemDTO.setAttrTotal(BigDecimal.ZERO);
                Integer itemType = oi.getItemType();
                switch (itemType) {
                    case 1:
                        List<OrderItemDO> subOrderItemDOS = subItemMap.get(oi.getGuid());
                        if (CollectionUtils.isEmpty(subOrderItemDOS)) {
                            log.error("套餐子商品为空 ItemGuid={}", oi.getItemGuid());
                            continue;
                        }
                        subOrderItemDOS.forEach(orderItemDO ->
                                desc.append(orderItemDO.getItemName())
                                        .append("*")
                                        .append(orderItemDO.getPackageDefaultCount().multiply(orderItemDO.getCurrentCount()).intValue())
                                        .append(orderItemDO.getUnit())
                                        .append("/")
                        );
                        String skuAttrDescString = desc.toString();
                        if (skuAttrDescString.endsWith("/")) {
                            skuAttrDescString = skuAttrDescString.substring(0, skuAttrDescString.length() - 1);
                        }
                        itemDTO.setSkuAttrDescString(skuAttrDescString);

                        // 套餐有子项加价
                        addPrice = subOrderItemDOS.stream()
                                .map(sub -> sub.getCurrentCount().multiply(sub.getAddPrice()))
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO);
                        break;
                    case 2:
                        desc.append(oi.getSkuName());
                        itemDTO.setSkuAttrDescString(desc.toString());
                        break;
                    case 4:
                        // 无属性单规格商品直接返回空
                        break;
                    default:
                        log.error("未知商品类型 itemType={}", itemType);
                }
            }

            itemDTO.setSkuGuid(oi.getSkuGuid());
            itemDTO.setPrice(oi.getCurrentCount().multiply(oi.getPrice().add(addPrice)).add(itemDTO.getAttrTotal()));
            if (ObjectUtils.isEmpty(oi.getMemberPrice())) {
                // 如果没有设置会员价则用原价
                itemDTO.setMemberPrice(oi.getCurrentCount().multiply(oi.getPrice().add(addPrice))
                        .add(itemDTO.getAttrTotal()));
            } else {
                itemDTO.setMemberPrice(oi.getCurrentCount().multiply(oi.getMemberPrice().add(addPrice))
                        .add(itemDTO.getAttrTotal()));
            }
            itemDTO.setCurrentCount(oi.getCurrentCount());
            itemDTO.setSmallPicture(Optional.ofNullable(oi.getSmallPicture()).orElse(EMPTY));
            batchItemInfoList.add(itemDTO);
        }
        return batchItemInfoList;
    }

    /**
     * 修改就餐人数
     *
     * @param modifyGuestsNoReqDTO 修改就餐人数请求实体
     * @return 结果
     */
    @Override
    public BaseRespDTO modifyGuestsNo(PadModifyGuestsNoReqDTO modifyGuestsNoReqDTO) {
        Integer actualGuestsNo = modifyGuestsNoReqDTO.getActualGuestsNo();
        if (ObjectUtils.isEmpty(actualGuestsNo)) {
            return BaseRespDTO.constMsg(GUESTS_QUANTITY_IS_EMPTY);
        }
        String orderGuid = modifyGuestsNoReqDTO.getOrderGuid();
        if (ObjectUtils.isEmpty(orderGuid)) {
            return BaseRespDTO.constMsg(ORDER_GUID_IS_EMPTY);
        }

        // 修改订单表
        OrderDO orderDO = orderService.getById(orderGuid);
        orderDO.setGuestCount(actualGuestsNo);
        orderService.updateById(orderDO);

        // 修改附加费表
        List<AppendFeeDO> appendFeeList = appendFeeMpService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(appendFeeList)) {
            // 修改附加费缓存
            String appendFeeRedisKey = APPEND_FEE_PREFIX + orderGuid;
            List<AppendFeeDO> feeDOList = appendFeeRedisTemplate.opsForValue().get(appendFeeRedisKey);
            if (!CollectionUtils.isEmpty(feeDOList)) {
                log.info("修改附加费缓存");
                feeDOList.forEach(append -> {
                    if (Objects.equals(0, append.getType())) {
                        append.setAmount(append.getUnitPrice().multiply(BigDecimal.valueOf(actualGuestsNo)));
                    }
                });
                appendFeeRedisTemplate.delete(appendFeeRedisKey);
                appendFeeRedisTemplate.opsForValue().set(appendFeeRedisKey, feeDOList, 24, TimeUnit.HOURS);
            }
        } else {
            appendFeeList.forEach(append -> {
                if (Objects.equals(0, append.getType())) {
                    append.setAmount(append.getUnitPrice().multiply(BigDecimal.valueOf(actualGuestsNo)));
                }
            });
            appendFeeMpService.updateBatchById(appendFeeList);
        }

        // 修改pad下单表
        List<PadOrderDO> padOrderDOList = this.list(new LambdaQueryWrapper<PadOrderDO>()
                .eq(PadOrderDO::getOrderGuid, orderGuid)
                .eq(PadOrderDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        padOrderDOList.forEach(padOrder -> padOrder.setActualGuestsNo(actualGuestsNo));
        this.updateBatchById(padOrderDOList);

        return BaseRespDTO.constSuccess();
    }

    /**
     * 保存pad支付信息到缓存
     *
     * @param padPayInfoReqDTO pad支付信息
     * @return Boolean
     */
    @Override
    public Boolean savePadPayInfoToRedis(PadPayInfoReqDTO padPayInfoReqDTO) {
        String orderGuid = padPayInfoReqDTO.getOrderGuid();
        PadPayInfoReqDTO padPayInfo = redisService.getPadPayInfo(orderGuid);
        if (!ObjectUtils.isEmpty(padPayInfo)) {
            redisService.deletePadPayInfo(orderGuid);
        }
        redisService.putPadPayInfo(orderGuid, padPayInfoReqDTO);
        return Boolean.TRUE;
    }

    /**
     * 获取缓存pad支付信息
     *
     * @param orderGuid 订单guid
     * @return pad支付信息
     */
    @Override
    public PadPayInfoReqDTO getPadPayInfo(String orderGuid) {
        return redisService.getPadPayInfo(orderGuid);
    }

    /**
     * 通过订单数据发起预支付下单
     * 生成pad支付二维码
     *
     * @param orderGuid 订单guid
     * @return pad支付二维码
     */
    @Override
    public PadQrCodeRespDTO getPadQrCode(String orderGuid) {
        PadQrCodeRespDTO respDTO = new PadQrCodeRespDTO();
        if (StringUtils.isEmpty(orderGuid)) {
            respDTO.setResultState(ResultStateEnum.FAIL.getCode());
            respDTO.setPromptMsg(ORDER_GUID_IS_EMPTY);
            return respDTO;
        }
        // 生成pad支付二维码
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            BitMatrix bitMatrix = qrCodeWriter.encode(getOrderPayQrCode(orderGuid), BarcodeFormat.QR_CODE, 300, 300);
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream);
        } catch (Exception e) {
            log.warn("生成pad支付二维码异常{}", e.getMessage());
            respDTO.setResultState(ResultStateEnum.FAIL.getCode());
            respDTO.setPromptMsg(e.getMessage());
            return respDTO;
        }
        byte[] padPayQr = outputStream.toByteArray();

        respDTO.setPadQrCode(padPayQr);
        respDTO.setResultState(ResultStateEnum.SUCCESS.getCode());
        return respDTO;
    }

    @Override
    public String getOrderPayQrCode(String orderGuid) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        String orderPayQrUrl = payQrUrl + "?orderGuid=" + orderGuid + "&enterpriseGuid=" + enterpriseGuid;
        log.info("padPayQrUrl={}", orderPayQrUrl);
        return orderPayQrUrl;

    }

    /**
     * 获取订单并台的所有下单信息
     *
     * @param orderGuid 订单guid
     * @return pad下单消息
     */
    @Override
    public List<PadOrderRespDTO> listPadOrderInfoOnCombine(String orderGuid) {
        List<PadOrderDO> padOrderDOList = getPadOrderDOList(orderGuid);
        if (CollectionUtils.isEmpty(padOrderDOList)) {
            log.warn("获取订单并台的所有下单信息 pad下单数据为空 orderGuid={}", orderGuid);
            return new ArrayList<>();
        }

        // 通过主单id查询所有的订单信息
        List<String> combineGuidList = padOrderDOList.stream()
                .map(PadOrderDO::getCombineOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(combineGuidList)) {
            log.warn("当前订单没有并台 orderGuid={}", orderGuid);
            return new ArrayList<>();
        }
        List<PadOrderDO> combinePadOrderDOList = this.list(new LambdaQueryWrapper<PadOrderDO>()
                .in(PadOrderDO::getCombineOrderGuid, combineGuidList)
                .eq(PadOrderDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        // 自己不推送消息
//        if (!CollectionUtils.isEmpty(combinePadOrderDOList)) {
//            padOrderDOList.addAll(combinePadOrderDOList);
//        }
        return OrderTransform.INSTANCE.padOrderDOList2OrderRespDTOList(combinePadOrderDOList);
    }

    /**
     * 保存pad转台信息
     *
     * @param redisReqDTO redis请求实体
     * @return 结果
     */
    @Override
    public Boolean savePadTurnInfo(RedisReqDTO redisReqDTO) {
        String redisKey = redisReqDTO.getRedisKey();
        if (StringUtils.isEmpty(redisKey)) {
            throw new BusinessException("保存pad转台信息 key为空");
        }
        if (StringUtils.isEmpty(redisReqDTO.getRedisValue())) {
            throw new BusinessException("保存pad转台信息 value为空");
        }
        String padTurnInfo = redisService.getPadTurnInfo(redisKey);
        if (!ObjectUtils.isEmpty(padTurnInfo)) {
            log.warn("重复保存pad转台信息");
            redisService.deletePadTurnInfo(redisKey);
        }
        redisService.putPadTurnInfo(redisKey, redisReqDTO.getRedisValue());

        // orderGuid:旧:新 table_turn:6619160598682123456:6619160598682796035:6678135271059357696
        String[] split = redisKey.split(":");
        String newTableGuid = split[3];

        // 查询新桌设备号
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(redisReqDTO.getStoreGuid());
        reqDTO.setTableGuid(newTableGuid);
        StoreDeviceDTO storeDeviceDTO = organizationClientService.queryDeviceByStoreTable(reqDTO);
        if (!ObjectUtils.isEmpty(storeDeviceDTO)) {

            // 发送消息给被动转台的设备
            BusinessMessageDTO turnMessageDTO = new BusinessMessageDTO();
            turnMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
            turnMessageDTO.setPlatform("2");
            turnMessageDTO.setStoreGuid(redisReqDTO.getStoreGuid());
            turnMessageDTO.setStoreName(redisReqDTO.getStoreName());
            turnMessageDTO.setSubject(BusinessMsgTypeEnum.TRANSFER_TABLE_PASSIVE.getName());
            turnMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.TRANSFER_TABLE_PASSIVE.getId());

            TurnMessageDTO messageDTO = new TurnMessageDTO();
            messageDTO.setRedisKey(redisKey);

            // 根据桌台查询orderGuid
            String orderGuid = tableClientService.getOrderGuidByTableGuid(newTableGuid);
            if (StringUtils.isEmpty(orderGuid)) {
                log.warn("未查询到订单guid newTableGuid={}", newTableGuid);
                return Boolean.FALSE;
            }
            messageDTO.setOrderGuid(orderGuid);
            OrderDTO orderDTO = orderDetailService.findByOrderGuid(orderGuid);
            messageDTO.setActualGuestsNo(orderDTO.getGuestCount());
            turnMessageDTO.setContent(JacksonUtils.writeValueAsString(messageDTO));
            turnMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + storeDeviceDTO.getDeviceNo());

            log.info("被动转台 turnMessageDTO={}", JacksonUtils.writeValueAsString(turnMessageDTO));
            businessMessageService.msg(turnMessageDTO);
        }
        return Boolean.TRUE;
    }

    /**
     * 获取pad转台信息
     *
     * @param redisKey key
     * @return pad转台信息
     */
    @Override
    public String getPadTurnInfo(String redisKey) {
        if (StringUtils.isEmpty(redisKey)) {
            throw new BusinessException("获取pad转台信息 key为空");
        }
        return redisService.getPadTurnInfo(redisKey);
    }

    /**
     * 根据桌台查询订单guid和人数
     *
     * @param tableGuid 桌台guid
     * @return 订单guid和人数
     */
    @Override
    public PadRebindRespDTO queryOrderAndGuestByTable(String tableGuid) {
        PadRebindRespDTO respDTO = new PadRebindRespDTO();
        String orderGuid = tableClientService.getOrderGuidByTableGuid(tableGuid);
        if (StringUtils.isEmpty(orderGuid)) {
            log.warn("未查询到桌台订单 tableGuid={}", tableGuid);
            return respDTO;
        }
        OrderDO orderDO = orderService.getById(orderGuid);
        if (ObjectUtils.isEmpty(orderDO)) {
            log.warn("未查询到订单信息 orderGuid={}", orderGuid);
            return respDTO.setOrderGuid(orderGuid);
        }
        return respDTO.setOrderGuid(orderGuid).setActualGuestsNo(orderDO.getGuestCount());
    }

    /**
     * 根据并台主单guid查询pad下单信息列表
     *
     * @param combineOrderGuid 并台主单guid
     * @return 下单信息列表
     */
    @Override
    public List<PadOrderRespDTO> listPadOrderByCombineOrderGuid(String combineOrderGuid) {
        if (StringUtils.isEmpty(combineOrderGuid)) {
            log.error(PAD_COMBINE_ORDER_GUID_IS_EMPTY);
            return new ArrayList<>();
        }
        List<PadOrderDO> padOrderDOList = this.list(new LambdaQueryWrapper<PadOrderDO>()
                .eq(PadOrderDO::getCombineOrderGuid, combineOrderGuid)
                .eq(PadOrderDO::getIsDelete, Boolean.FALSE)
        );
        if (ObjectUtils.isEmpty(padOrderDOList)) {
            log.warn(NO_RESULTS_FOUND + "combineOrderGuid={}", combineOrderGuid);
            return new ArrayList<>();
        }
        return OrderTransform.INSTANCE.padOrderDOList2OrderRespDTOList(padOrderDOList);
    }

    /**
     * 获取订单下所有下单信息
     *
     * @param orderGuid 当前订单guid
     * @return 所有下单信息
     */
    @Override
    public List<PadOrderRespDTO> listPadOrderDTOList(String orderGuid) {
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException("订单guid不能为空");
        }
        List<PadOrderDO> padOrderDOList = getPadOrderDOList(orderGuid);
        return OrderTransform.INSTANCE.padOrderDOList2OrderRespDTOList(padOrderDOList);
    }

    /**
     * pad退出登录处理
     * 撤销验券和删除支付信息
     *
     * @param billCalculateReqDTO 计算接口的入参，用于撤销验券
     */
    @Override
    public Boolean padSignOut(BillCalculateReqDTO billCalculateReqDTO) {
        // 删除支付信息
        String orderGuid = billCalculateReqDTO.getOrderGuid();
        PadPayInfoReqDTO padPayInfo = redisService.getPadPayInfo(orderGuid);
        if (!ObjectUtils.isEmpty(padPayInfo)) {
            redisService.deletePadPayInfo(orderGuid);
            log.info("pad退出登录删除支付信息 orderGuid={}", orderGuid);
        }
        return true;
    }

    @Override
    public void updatePhone(Long orderGuid, String memberPhoneNumber) {
        PadOrderDO padOrderDO = new PadOrderDO();
        padOrderDO.setOrderGuid(String.valueOf(orderGuid));
        padOrderDO.setMemberPhone(memberPhoneNumber);
        update(padOrderDO, new LambdaQueryWrapper<PadOrderDO>()
                .eq(PadOrderDO::getOrderGuid, orderGuid));
    }
}
