package com.holderzone.saas.store.trade.config;

import com.holderzone.saas.store.trade.interceptor.WebInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebConfig
 * @date 2018-07-26 09:51:44
 * @description
 * @program holder-saas-store-trade
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(webInterceptor()).addPathPatterns("/**");
    }

    @Bean
    public WebInterceptor webInterceptor() {
        return new WebInterceptor();
    }

}
