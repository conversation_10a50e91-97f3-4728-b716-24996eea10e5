package com.holderzone.saas.store.trade.transform;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.local.FreeReturnItemDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.trade.entity.domain.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTransform
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-trade
 */
@Mapper
public interface LocalTransform {

    LocalTransform INSTANCE = Mappers.getMapper(LocalTransform.class);

    List<OrderDTO> orderDOList2DTOList(List<OrderDO> orderDOS);

    @Mappings({
            @Mapping(
                    target = "isDelete",
                    expression = "java(orderDOS.getIsDelete()?1:0)"
            ),
            @Mapping(target = "localUploadId", ignore = true),
    })
    OrderDTO orderDO2DTO(OrderDO orderDOS);

    List<OrderDO> orderDTOSList2DOList(List<OrderDTO> orderDTOS);

    @Mappings({
            @Mapping(
                    target = "isDelete",
                    expression = "java(orderDTO.getIsDelete()==1?true:false)"
            ),
            @Mapping(
                    target = "guid",
                    expression = "java(Long.valueOf(orderDTO.getGuid()))"
            ),
            @Mapping(
                    target = "mainOrderGuid",
                    expression = "java(orderDTO.getMainOrderGuid()!=null&&!orderDTO.getMainOrderGuid().equals(null)?Long.valueOf(orderDTO.getMainOrderGuid()):0 )"
            ),

    })
    OrderDO orderDTO2DO(OrderDTO orderDTO);

    List<AppendFeeDTO> appendDOList2DTOList(List<AppendFeeDO> append);

    @Mapping(
            target = "isDelete",
            expression = "java(append.getIsDelete()?1:0)"
    )
    AppendFeeDTO appendDO2DTO(AppendFeeDO append);

    List<FreeReturnItemDTO> freeDOList2DTOList(List<FreeReturnItemDO> free);


    @Mapping(
            target = "isDelete",
            expression = "java(free.getIsDelete()?1:0)"
    )
    FreeReturnItemDTO freeDO2DTO(FreeReturnItemDO free);

    List<TransactionRecordDTO> transactionRecordDOS2TransactionRecordDTOS(List<TransactionRecordDO>
                                                                                  transactionRecordDOS);

    @Mapping(
            target = "isDelete",
            expression = "java(transactionRecordDO.getIsDelete()?1:0)"
    )
    TransactionRecordDTO transactionRecordDO2TransactionRecordDTO(TransactionRecordDO transactionRecordDO);

    List<DiscountDTO> discountDOS2DiscountDTOS(List<DiscountDO> discountDOS);

    @Mappings({
            @Mapping(
                    target = "isDelete",
                    expression = "java(discountDO.getIsDelete()?1:0)"
            ),
            @Mapping(
                    target = "rule",
                    expression = "java(discountDO.getRule()==null?null:java.net.URLEncoder.encode(discountDO.getRule()))"
            )
    })
    DiscountDTO discountDO2DiscountDTO(DiscountDO discountDO);

    DineInItemDTO orderItemDO2DineInItemDTO(OrderItemDO orderItemDO);

    List<DineInItemDTO> orderItemDO2DineInItemDTO(List<OrderItemDO> orderItemDO);

    List<ItemAttrDTO> itemAttrDOS2itemAttrDTOS(List<ItemAttrDO> itemAttrDOS);

    List<ItemDTO> orderItemDO2ItemDTO(List<OrderItemDO> orderItemDO);

    List<OrderItemDO> itemDTO2OrderItemDO(List<ItemDTO> itemDO);


    @Mapping(
            target = "isDelete",
            expression = "java(itemAttrDOS.getIsDelete()?1:0)"
    )
    ItemAttrDTO itemAttrDO2itemAttrDTO(ItemAttrDO itemAttrDOS);

    @Mapping(
            target = "isDelete",
            expression = "java(orderItemDO.getIsDelete()?1:0)"
    )
    ItemDTO orderItemDO2ItemDTO(OrderItemDO orderItemDO);


    @Mappings({
            @Mapping(
                    target = "isDelete",
                    expression = "java(itemDTO.getIsDelete()==1?true:false)"
            ),
            @Mapping(
                    target = "guid",
                    expression = "java(Long.valueOf(itemDTO.getGuid()))"
            ),
            @Mapping(target = "parentItemGuid", ignore = true),
    })
    OrderItemDO itemDTO2OrderItemDO(ItemDTO itemDTO);

    @Mapping(
            target = "isDelete",
            expression = "java(discountDTO.getIsDelete()==1?true:false)"
    )
    DiscountDO discountDTOS2DiscountDO(DiscountDTO discountDTO);

    List<DiscountDO> discountList2DiscountList(List<DiscountDTO> discountDOS);


    @Mapping(
            target = "isDelete",
            expression = "java(transactionRecordDTO.getIsDelete()==1?true:false)"
    )
    TransactionRecordDO tranDTOS2TranDO(TransactionRecordDTO transactionRecordDTO);

    List<TransactionRecordDO> tranDTOListS2TranDOList(List<TransactionRecordDTO> transactionRecordDTOS);


    @Mapping(
            target = "isDelete",
            expression = "java(itemAttrDTO.getIsDelete()==1?true:false)"
    )
    ItemAttrDO itemAttrDTO2ItemAttrDO(ItemAttrDTO itemAttrDTO);

    List<ItemAttrDO> itemAttrDTOList2ItemAttrDOList(List<ItemAttrDTO> itemAttrDTOS);


    @Mapping(
            target = "isDelete",
            expression = "java(appendFeeDTO.getIsDelete()==1?true:false)"
    )
    AppendFeeDO appendFeeDTO2AppendFeeDO(AppendFeeDTO appendFeeDTO);

    List<AppendFeeDO> appendFeeDTOList2AppendFeeDOList(List<AppendFeeDTO> appendFeeDTOS);


    @Mapping(
            target = "isDelete",
            expression = "java(freeReturnItemDTO.getIsDelete()==1?true:false)"
    )
    FreeReturnItemDO freeReturnItemDTo2FreeReturnItemDO(FreeReturnItemDTO freeReturnItemDTO);

    List<FreeReturnItemDO> freeReturnItemDToList2FreeReturnItemDOList(List<FreeReturnItemDTO> freeReturnItemDTOList);

    SubDineInItemDTO OrderItemDO2SubDineInItemDTO(OrderItemDO orderItemDO);

    List<SubDineInItemDTO> orderItemDOList2subDineInItemDTOList(List<OrderItemDO> orderItemDOList);
}
