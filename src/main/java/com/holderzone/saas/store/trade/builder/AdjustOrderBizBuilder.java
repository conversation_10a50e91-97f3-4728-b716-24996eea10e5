package com.holderzone.saas.store.trade.builder;

import com.beust.jcommander.internal.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderReqDTO;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.bo.AdjustOrderBO;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDO;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDetailsDO;
import com.holderzone.saas.store.trade.entity.domain.ItemAttrDO;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.transform.AdjustOrderTransform;
import com.holderzone.saas.store.trade.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant.*;
import static com.holderzone.saas.store.trade.utils.caculate.ItemUtils.orderTransform;

@Slf4j
public class AdjustOrderBizBuilder {

    public static AdjustOrderTransform adjustOrderTransform = AdjustOrderTransform.INSTANCE;

    private static DynamicHelper dynamicHelper;


    public static AdjustOrderBO buildBiz() {
        AdjustOrderBO biz = new AdjustOrderBO();
        biz.setOrder(new AdjustOrderDO());
        biz.setDetails(Lists.newArrayList());
        biz.setAttrList(Lists.newArrayList());
        biz.setOrderItemGuidList(Lists.newArrayList());
        biz.setOrderItemDTOList(Lists.newArrayList());
        biz.setAdjustOrderItemDTOList(Lists.newArrayList());
        return biz;
    }


    /**
     * 构建调整单业务对象
     */
    public static AdjustOrderBO build(AdjustOrderReqDTO reqDTO) {
        dynamicHelper = SpringContextUtil.getInstance().getBean(DynamicHelper.class);

        String storeGuid = UserContextUtils.getStoreGuid();
        String storeName = UserContextUtils.getStoreName();
        String userGuid = UserContextUtils.getUserGuid();
        String userName = UserContextUtils.getUserName();

        AdjustOrderBO biz = buildBiz();
        // 调整单信息
        AdjustOrderDO order = biz.getOrder();
        order.setStoreGuid(storeGuid);
        order.setStoreName(storeName);
        order.setAdjustCount(reqDTO.getOrderItemList().size());
        order.setAdjustPrice(reqDTO.getAdjustPrice());
        order.setOrderFee(reqDTO.getOrderFee());
        order.setDeviceType(reqDTO.getDeviceType());
        order.setTradeMode(reqDTO.getTradeMode());
        order.setReason(reqDTO.getReason());
        order.setOrderNo(reqDTO.getOrderNo());
        order.setOrderGuid(reqDTO.getOrderGuid());
        order.setCreateStaffGuid(userGuid);
        order.setCreateStaffName(userName);
        order.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.ADJUST_ORDER));
        // 调整单明细
        buildDetails(reqDTO, biz);

        // 原订单冗余信息
        biz.setOrderGuid(reqDTO.getOrderGuid());
        biz.setBusinessDay(reqDTO.getBusinessDay());
        biz.setOrderNo(reqDTO.getOrderNo());
        biz.setTakeoutOrderDetail(reqDTO.getTakeoutOrderDetail());

        // 订单明细guids
        List<Long> orderItemGuidList = biz.getDetails().stream().map(AdjustOrderDetailsDO::getOrderItemGuid).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderItemGuidList)) {
            biz.getOrderItemGuidList().addAll(orderItemGuidList);
        }
        // 原订单需要调整的商品明细
        List<DineInItemDTO> orderItemList = reqDTO.getDineInItemDTOS().stream()
                .filter(e -> orderItemGuidList.contains(Long.valueOf(e.getGuid()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderItemList)) {
            biz.getOrderItemDTOList().addAll(orderItemList);
        }
        // 调整后的商品明细
        if (TradeModeEnum.TAKEOUT.getMode().equals(reqDTO.getTradeMode())) {
            // 外卖处理调整后的商品明细
            List<DineInItemDTO> adjustOrderItemDTOList = Lists.newArrayList();
            List<AdjustOrderDetailsDO> detailsByAdjustTypeIsOne = biz.getDetails().stream().filter(e -> e.getAdjustType() == 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(detailsByAdjustTypeIsOne)) {
                List<DineInItemDTO> adjustDineInItems = AmountCalculationUtil.buildAdjustItem(detailsByAdjustTypeIsOne, biz.getAttrList());
                adjustOrderItemDTOList.addAll(adjustDineInItems);
            }
            List<AdjustOrderDetailsDO> detailsByAdjustTypeIsZero = biz.getDetails().stream().filter(e -> e.getAdjustType() == 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(detailsByAdjustTypeIsZero)) {
                Map<String, DineInItemDTO> dineInItemMap = orderItemList.stream().collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key2));
                for (AdjustOrderDetailsDO detailsDO : detailsByAdjustTypeIsZero) {
                    DineInItemDTO dineInItemDTOByOrderItemGuid = dineInItemMap.get(String.valueOf(detailsDO.getOrderItemGuid()));
                    if (Objects.nonNull(dineInItemDTOByOrderItemGuid)) {
                        DineInItemDTO dineInItemDTO = new DineInItemDTO();
                        BeanUtils.copyProperties(dineInItemDTOByOrderItemGuid, dineInItemDTO);
                        dineInItemDTO.setCurrentCount(detailsDO.getCurrentCount());
                        dineInItemDTO.setMappingCount(detailsDO.getMappingCount());
                        adjustOrderItemDTOList.add(dineInItemDTO);
                    }
                }
            }
            biz.getAdjustOrderItemDTOList().addAll(adjustOrderItemDTOList);
        } else {
            // 正餐快餐处理调整后的商品明细
            List<DineInItemDTO> adjustOrderItemDTOList = AmountCalculationUtil.buildAdjustItem(biz.getDetails(), biz.getAttrList());
            biz.getAdjustOrderItemDTOList().addAll(adjustOrderItemDTOList);
        }
        return biz;
    }

    private static void addOrderDetailsCommonInfo(AdjustOrderDetailsDO detail, Long orderGuid, String orderItemGuid,
                                                  Long adjustGuid, Integer adjustType, String createStaffGuid, String createStaffName) {
        detail.setOrderGuid(orderGuid);
        detail.setOrderItemGuid(Long.valueOf(orderItemGuid));
        detail.setCreateStaffGuid(createStaffGuid);
        detail.setCreateStaffName(createStaffName);
        detail.setAdjustGuid(adjustGuid);
        detail.setAdjustType(adjustType);
    }


    public static void buildDetails(AdjustOrderReqDTO reqDTO, AdjustOrderBO biz) {
        List<AdjustOrderReqDTO.AdjustOrderItem> orderItemList = reqDTO.getOrderItemList();
        if (CollectionUtils.isEmpty(orderItemList)) {
            throw new ParameterException("菜品信息不能为空");
        }

        Long orderGuid = reqDTO.getOrderGuid();
        AdjustOrderDO order = biz.getOrder();
        List<AdjustOrderDetailsDO> details = biz.getDetails();
        List<ItemAttrDO> attrList = biz.getAttrList();
        Long adjustGuid = order.getGuid();

        for (AdjustOrderReqDTO.AdjustOrderItem item : orderItemList) {
            List<DineInItemDTO> itemList = item.getItemList();
            itemList.forEach(sku -> {

                AdjustOrderDetailsDO detailsDO = adjustOrderTransform.dineInItemDTO2AdjustOrderDetailsDO(sku);
                Long orderItemGuid = dynamicHelper.generateGuid(ADJUST_ORDER_DETAILS);
                detailsDO.setGuid(orderItemGuid);
                log.info("设置外卖核算价{}", sku.getTakeawayAccountingPrice());
                detailsDO.setTakeawayAccountingPrice(sku.getTakeawayAccountingPrice());
                addOrderDetailsCommonInfo(detailsDO, orderGuid, item.getOrderItemGuid(), adjustGuid, item.getAdjustType(),
                        order.getCreateStaffGuid(), order.getCreateStaffName());
                details.add(detailsDO);

                sku.setGuid(String.valueOf(orderItemGuid));

                if (CollectionUtil.isNotEmpty(sku.getItemAttrDTOS())) {
                    List<Long> itemAttrGuids = dynamicHelper.generateGuids(ITEM_ATTR_GUID, sku.getItemAttrDTOS().size());
                    List<ItemAttrDO> itemAttrList = addItemAttrDOList(sku.getItemAttrDTOS(), itemAttrGuids, detailsDO,
                            order.getStoreGuid(), order.getStoreName());
                    attrList.addAll(itemAttrList);
                    detailsDO.setHasAttr(1);
                }
                // 菜品为套餐时
                if (ItemUtil.isGroup(sku.getItemType())) {
                    List<PackageSubgroupDTO> packageSubgroupList = sku.getPackageSubgroupDTOS();
                    for (PackageSubgroupDTO packageSubgroupDTO : packageSubgroupList) {
                        List<SubDineInItemDTO> subDineInItemList = packageSubgroupDTO.getSubDineInItemDTOS();
                        for (SubDineInItemDTO subDineInItemDTO : subDineInItemList) {
                            if (StringUtils.isEmpty(subDineInItemDTO.getItemTypeGuid())) {
                                throw new ParameterException("菜品分类不能为空");
                            }
                            AdjustOrderDetailsDO subOrderItemDO = adjustOrderTransform.subDineInItemDTO2AdjustOrderDetailsDO(subDineInItemDTO);
                            Long subOrderItemGuid = dynamicHelper.generateGuid(ADJUST_ORDER_DETAILS);
                            subOrderItemDO.setGuid(subOrderItemGuid);
                            if (ObjectUtils.isEmpty(subOrderItemDO.getPrice())) {
                                subOrderItemDO.setPrice(BigDecimal.ZERO);
                            }
                            addOrderDetailsCommonInfo(subOrderItemDO, orderGuid, item.getOrderItemGuid(), adjustGuid, item.getAdjustType(),
                                    order.getCreateStaffGuid(), order.getCreateStaffName());
                            subOrderItemDO.setParentItemGuid(Long.valueOf(sku.getGuid()));
                            subOrderItemDO.setSubgroupGuid(packageSubgroupDTO.getSubgroupGuid());
                            subOrderItemDO.setSubgroupName(packageSubgroupDTO.getSubgroupName());
                            if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getAddPrice())) {
                                subOrderItemDO.setAddPrice(subDineInItemDTO.getAddPrice());
                            }

                            subDineInItemDTO.setGuid(String.valueOf(subOrderItemGuid));
                            details.add(subOrderItemDO);

                            if (CollectionUtils.isNotEmpty(subDineInItemDTO.getItemAttrDTOS())) {
                                List<Long> itemAttrGuids = dynamicHelper.generateGuids(ITEM_ATTR_GUID, subDineInItemDTO.getItemAttrDTOS().size());
                                List<ItemAttrDO> subItemAttrList = addItemAttrDOList(subDineInItemDTO.getItemAttrDTOS(),
                                        itemAttrGuids, subOrderItemDO, order.getStoreGuid(), order.getStoreName());
                                attrList.addAll(subItemAttrList);
                                subOrderItemDO.setHasAttr(1);
                            }
                        }
                    }
                }
            });
        }
    }


    private static List<ItemAttrDO> addItemAttrDOList(List<ItemAttrDTO> subAttrList, List<Long> itemAttrGuids,
                                                      AdjustOrderDetailsDO subOrderItemDO, String storeGuid, String storeName) {
        List<ItemAttrDO> resultList = Lists.newArrayList();

        for (int i = 0; i < subAttrList.size(); i++) {
            ItemAttrDTO itemAttrDTO = subAttrList.get(i);

            ItemAttrDO itemAttrDO = orderTransform.itemAttrDTO2ItemAttrDO(itemAttrDTO);
            itemAttrDO.setOrderItemGuid(subOrderItemDO.getGuid());
            itemAttrDO.setGuid(itemAttrGuids.get(i));
            itemAttrDO.setOrderGuid(subOrderItemDO.getAdjustGuid());
            itemAttrDO.setStoreGuid(storeGuid);
            itemAttrDO.setStoreName(storeName);
            itemAttrDTO.setGuid(String.valueOf(itemAttrGuids.get(i)));

            resultList.add(itemAttrDO);
        }
        return resultList;
    }


}
