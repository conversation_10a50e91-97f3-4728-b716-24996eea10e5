package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.trade.service.LocalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/local")
@Api(description = "本地化接口")
@Slf4j
public class LocalController {

    private final LocalService localService;

    public LocalController(LocalService localService) {
        this.localService = localService;
    }

    @ApiOperation(value = "本地化数据提供", notes = "本地化数据提供")
    @PostMapping("/get_local")
    public LocalDTO getLocaL(@RequestBody LocalQuery localQuery) {
        LocalDTO localDTO = localService.getLocal(localQuery);
        return localDTO;
    }

    @ApiOperation(value = "本地化拉取单条数据", notes = "本地化拉取单条数据")
    @PostMapping("/get_single")
    public LocalDTO getSingle(@RequestBody SingleQuery singleQuery) {
        LocalDTO localDTO = localService.getSingle(singleQuery);
        return localDTO;
    }


    @ApiOperation(value = "本地化数据接收", notes = "本地化数据接收")
    @PostMapping("/save_local")
    public Map<String, String> saveLocal(@RequestBody LocalDTO localDTO) {
        if (!ObjectUtils.isEmpty(localDTO.getOrderList())) {
            Map<String, Integer> orderDeviceType = new HashMap<>();
            for (OrderDTO orderDTO : localDTO.getOrderList()) {
                if (ObjectUtils.isEmpty(orderDTO.getDeviceType()) || orderDTO.getDeviceType() == null) {
                    orderDeviceType.put(orderDTO.getGuid(), -1);
                } else {
                    orderDeviceType.put(orderDTO.getGuid(), orderDTO.getDeviceType());
                }
            }
            log.info("本地化数据接收入参：orderDeviceType={}", JacksonUtils.writeValueAsString(orderDeviceType));
        }
        Map<String, String> result = localService.saveLocal(localDTO);
        log.info("本地化数据接收返回：{}", JacksonUtils.writeValueAsString(result));
        return result;
    }

    @ApiOperation(value = "本地化数据还原", notes = "本地化数据还原")
    @PostMapping("/test")
    public void test() {
        localService.test();
    }

    @ApiOperation(value = "本地化数据还原", notes = "本地化数据还原")
    @PostMapping("/delete_local")
    public Boolean deleteLocal(@RequestBody DeleteQuery deleteQuery) {
        log.info("本地化数据还原入参:{}", JacksonUtils.writeValueAsString(deleteQuery));
        Boolean flag = localService.deleteLocal(deleteQuery);
        log.info("本地化数据还原完成:{}", JacksonUtils.writeValueAsString(flag));
        return flag;
    }

    @ApiOperation(value = "校验服务是否可用(给安卓端定时调用)", notes = "校验服务是否可用(给安卓端定时调用)")
    @GetMapping("/get_service")
    public String checkService() {
        return "ok";
    }

}
