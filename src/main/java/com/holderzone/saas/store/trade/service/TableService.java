package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.bill.RefundReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.item.BatchItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.trade.entity.bo.AggPayAttachDataBO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO; /**
 * <AUTHOR>
 * @version 1.0
 * @className TableService
 * @date 2019/03/25 14:40
 * @description //TODO
 * @program holder-saas-store-dto
 */
public interface TableService {

    void returnChange(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO, OrderDO orderDO);

    void checkOutChange(AggPayAttachDataBO aggPayAttachDataBO, OrderDO orderDO);

    void checkOutChange(BaseDTO baseDTO, OrderDO orderDO);

    void checkOutChange(RefundReqDTO refundReqDTO, OrderDO orderDO);

    void disabledChange(CancelOrderReqDTO cancelOrderReqDTO, OrderDO orderDO);

    void printPreBillChange(BaseDTO baseDTO, OrderDO orderDO);
}
