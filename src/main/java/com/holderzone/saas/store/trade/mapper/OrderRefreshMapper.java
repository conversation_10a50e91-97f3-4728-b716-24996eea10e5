package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface OrderRefreshMapper extends BaseMapper<OrderDO> {

    void refreshModifyTime(@Param("orderGuid") String orderGuid);

    String getNeedUpdateOrderGuid();

    public void refreshModifyTimeByDay(@Param("second") int second, @Param("gmtCreateTimeLimit") Date limitDate);
}
