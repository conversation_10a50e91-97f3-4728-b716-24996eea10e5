package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.base.dto.file.FileDto;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 上传文件
 *
 * <AUTHOR>
 * @date 2021/12/22 14:01
 */
@Component
@FeignClient(name = "base-service", fallbackFactory = FileUploadClient.FileUploadFallBack.class)
public interface FileUploadClient {

    @PostMapping("/file")
    String upload(@RequestBody FileDto fileDto);

    @DeleteMapping("/file")
    void delete(@RequestParam("fileUrl") String fileUrl);

    @Component
    class FileUploadFallBack implements FallbackFactory<FileUploadClient> {

        private static final Logger logger = LoggerFactory.getLogger(FileUploadFallBack.class);

        @Override
        public FileUploadClient create(Throwable throwable) {

            return new FileUploadClient() {
                @Override
                public String upload(FileDto fileDto) {
                    logger.error("调用oss文件上传异常 e={}", throwable.getMessage());
                    throw new HystrixBadRequestException("调用oss文件上传异常! " + throwable.getMessage());
                }

                @Override
                public void delete(String fileUrl) {
                    logger.error("调用oss文件删除异常 e={}", throwable.getMessage());
                }
            };
        }
    }
}
