package com.holderzone.saas.store.trade.controller;


import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.journaling.req.*;
import com.holderzone.saas.store.dto.journaling.resp.*;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsCombinedRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsDetailRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsQueryDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsRespDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.trade.service.BusinessOrderReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 商户后台数据报表订单业务接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@RestController
@RequestMapping("/order_report")
@Api(description = "商户后台数据报表订单业务接口")
@Slf4j
public class BusinessOrderReportController {

    private final BusinessOrderReportService businessOrderReportService;

    @Autowired
    public BusinessOrderReportController(BusinessOrderReportService businessOrderReportService) {
        this.businessOrderReportService = businessOrderReportService;
    }

    @ApiOperation(value = "获取订单统计分页数据")
    @PostMapping("/orderStatistics/page")
    public Page<BusinessOrderStatisticsRespDTO> getOrderStatisticsPage(@RequestBody @Validated BusinessOrderStatisticsQueryDTO queryDTO) {
        log.info("[获取订单统计分页数据]queryDTO={}", JacksonUtils.writeValueAsString(queryDTO));
        return businessOrderReportService.getOrderStatisticsPage(queryDTO);
    }

    @ApiOperation(value = "统计 条件下订单数/实收金额")
    @PostMapping("/orderStatistics/combined")
    public BusinessOrderStatisticsCombinedRespDTO getOrderStatisticsCombined(@RequestBody @Validated BusinessOrderStatisticsQueryDTO queryDTO) {
        return businessOrderReportService.getOrderStatisticsCombined(queryDTO);
    }

    @ApiOperation(value = "统计订单 订单明细")
    @GetMapping("/orderStatistics/detail")
    public BusinessOrderStatisticsDetailRespDTO getOrderStatisticsDetail(@RequestParam("orderGuid") String orderGuid) {
        return businessOrderReportService.getOrderStatisticsDetail(orderGuid);
    }

    @ApiOperation(value = "订单退款信息")
    @GetMapping("/orderStatistics/refundInfo")
    public BusinessOrderStatisticsDetailRespDTO getOrderStatisticsRefundInfo(@RequestParam("orderGuid") String orderGuid) {
        return businessOrderReportService.getOrderStatisticsRefundInfo(orderGuid);
    }

    @PostMapping("/orderStatistics/businessData")
    public BusinessDataRespDTO getOrderStatistics(@RequestBody JournalAppBaseReqDTO baseReq) {
        return businessOrderReportService.getBusinessData(baseReq);
    }

    @PostMapping("/orderStatistics/businessHisTrend")
    public BusinessHisTrendRespDTO getBusinessHisTrend(@RequestBody JournalAppBaseReqDTO baseReq) {
        return businessOrderReportService.getBusinessHisTrend(baseReq);
    }

    @PostMapping("/orderStatistics/storeGatherBusiness")
    public StoreGatherTotalRespDTO listStoreGatherBusiness(@RequestBody StoreGatherReportReqDTO baseReq) {
        return businessOrderReportService.listStoreGatherBusiness(baseReq);
    }

    @PostMapping("/orderStatistics/screen_business_data")
    public ScreenBusinessDataRespDTO screenBusinessData(@RequestBody JournalAppBaseReqDTO baseReq) {
        return businessOrderReportService.getScreenData(baseReq);
    }

    @ApiOperation(value = "大屏销售报表")
    @PostMapping("/orderStatistics/screen_sale")
    public SaleRespDTO screenSale(@RequestBody SaleCountReqDTO saleCountReqDTO) {
        return businessOrderReportService.screenSale(saleCountReqDTO);
    }

    @ApiOperation(value = "门店销售收入TOP5")
    @PostMapping("/orderStatistics/screen_sale_store_statistics")
    public StoreStatisticsAppRespDTO saleStoreStatistics(@RequestBody StoreStatisticsAppReqDTO storeStatisticsAppReqDTO) {
        return businessOrderReportService.saleStoreStatistics(storeStatisticsAppReqDTO);
    }

    @ApiOperation(value = "销售额按小时分段统计")
    @PostMapping("/orderStatistics/screen_sale_by_hours_statistics")
    public SaleStatisticsByHoursRespDTO saleByHoursStatistics(@RequestBody SaleStatisticsByHoursReqDTO statisticsByHoursReqDTO) {
        return businessOrderReportService.saleByHoursStatistics(statisticsByHoursReqDTO);
    }

    /**
     * 订单统计查询收银员信息
     *
     * @param businessOrderStatisticsQueryDTO 筛选条件
     * @return 收银员信息列表
     */
    @ApiOperation(value = "订单统计查询收银员信息")
    @PostMapping("/orderStatistics/get_checkout_staff")
    public List<UserBriefDTO> getCheckoutStaffs(@RequestBody BusinessOrderStatisticsQueryDTO businessOrderStatisticsQueryDTO) {
        return businessOrderReportService.getCheckoutStaffs(businessOrderStatisticsQueryDTO);
    }
}