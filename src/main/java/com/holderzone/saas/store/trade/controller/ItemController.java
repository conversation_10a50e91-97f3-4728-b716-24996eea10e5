package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.req.ItemBarCodeReqDTO;
import com.holderzone.saas.store.trade.service.ItemQueryService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 商品接口
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/item")
@Slf4j
public class ItemController {

    private final ItemQueryService itemQueryService;

    @ApiOperation(value = "打印商品标签单", notes = "打印商品标签单")
    @PostMapping("/print/barCode")
    public void printItemBarCode(@RequestBody @Valid ItemBarCodeReqDTO itemBarCodeReqDTO) {
        log.info("商品条码打印,入参:{}", JacksonUtils.writeValueAsString(itemBarCodeReqDTO));
        itemQueryService.printItemBarCode(itemBarCodeReqDTO);
    }

}
