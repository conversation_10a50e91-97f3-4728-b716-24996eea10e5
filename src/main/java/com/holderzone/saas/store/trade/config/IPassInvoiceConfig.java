package com.holderzone.saas.store.trade.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @date 2024/06/11
 * @description ipass配置类
 */
@Data
@RefreshScope
@Configuration
public class IPassInvoiceConfig {

    /**
     * 生成订单发票url
     */
    @Value("${ipass.orderInvoiceUrl}")
    private String orderInvoiceUrl;

    /**
     * 订单开票回调
     */
    @Value("${ipass.orderInvoiceCallbackUrl}")
    private String orderInvoiceCallbackUrl;

    /**
     * apiKey
     */
    @Value("${ipass.apiKey}")
    private String apiKey;

    /**
     * 订单开票回调
     */
    @Value("${ipass.apiSecret}")
    private String apiSecret;

    /**
     * code
     */
    public static final String CODE = "code";

    /**
     * message
     */
    public static final String MESSAGE = "message";

    /**
     * data
     */
    public static final String DATA = "data";

    /**
     * renson
     */
    public static final String RESSION = "ression";

    /**
     * 成功标识
     */
    public static final String SUCCESS_CODE = "0";

    /**
     * 失败标识
     */
    public static final String FAIL_CODE = "-1";

    /**
     * 短信发送成功标识
     */
    public static final String SMS_SUCCESS_CODE = "2001";

    /**
     * 餐饮编码
     */
    public static final String CATERING_CODE = "3070401000000000000";

    /**
     * 餐饮统称
     */
    public static final String CATERING_NAME = "餐饮服务";
}