package com.holderzone.saas.store.trade.config;

import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @version 1.0
 * @className MybatisPlusConfig
 * @date 2019/01/21 10:42
 * @description
 * @program holder-saas-store-trade
 */
@Configuration
@MapperScan("com.holderzone.saas.store.trade.mapper")
public class MybatisPlusConfig {

    /**
     * 分页插件
     * 与sdk中的PageInterceptor冲突了，一旦配置这个，RoutingStatementHandler会创建代理类，PageInterceptor反射获取delegate就会获取不到
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

}