package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.ItemAttrDO;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品属性 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-22
 */
public interface ItemAttrService extends IService<ItemAttrDO> {

    List<ItemAttrDO> listByItemGuids(Collection<? extends Serializable> itemGuids);

    void deleteByOrderGuid(String orderGuid);

    void deleteByOrderGuids(List<String> orderGuids);

    void removeByItemGuids(Collection<? extends Serializable> idList);

    List<ItemAttrDO> findByOrderGuid(String orderGuid);
}
