package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.ludou.*;
import com.holderzone.saas.store.trade.config.LudouFeignInterceptorConfiguration;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 麓豆接口调用
 */
@Component
@FeignClient(name = "ludou-server", fallbackFactory = LudouClientService.FallBack.class, url = "${ludou.host}",
        configuration = LudouFeignInterceptorConfiguration.class)
public interface LudouClientService {


    /**
     * 查询用户麓豆可抵金额
     */
    @PostMapping("/openapi/ludou/v1/usable-score")
    LudouResult<?> queryMember(@RequestBody LudouMemberQueryDTO queryDTO);

    /**
     * 麓豆支付/抵扣
     */
    @PostMapping("/openapi/ludou/v1/pay")
    LudouResult<?> pay(@RequestBody LudouMemberPayDTO payDTO);

    /**
     * 麓豆退款/回滚
     */
    @PostMapping("/openapi/ludou/v1/refund")
    LudouResult<?> refund(@RequestBody LudouMemberRefundDTO refundDTO);


    @Component
    class FallBack implements FallbackFactory<LudouClientService> {
        private static final Logger logger = LoggerFactory.getLogger(LudouClientService.FallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public LudouClientService create(Throwable throwable) {
            return new LudouClientService() {

                @Override
                public LudouResult<?> queryMember(LudouMemberQueryDTO queryDTO) {
                    logger.error(HYSTRIX_PATTERN, "queryMember", JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public LudouResult<?> pay(LudouMemberPayDTO payDTO) {
                    logger.error(HYSTRIX_PATTERN, "pay", JacksonUtils.writeValueAsString(payDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public LudouResult<?> refund(LudouMemberRefundDTO refundDTO) {
                    logger.error(HYSTRIX_PATTERN, "refund", JacksonUtils.writeValueAsString(refundDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

            };
        }
    }

}
