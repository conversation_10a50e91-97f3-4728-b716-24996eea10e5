package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtMaitonConsumeDTO;
import com.holderzone.saas.store.dto.trade.GrouponOrderDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.order.GrouponReceiptChannelEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.GrouponTradeDetailDO;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.mapper.GroupTradeDetailMapper;
import com.holderzone.saas.store.trade.mapper.GrouponMapper;
import com.holderzone.saas.store.trade.repository.interfaces.GrouponTradeDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;


@Slf4j
@Service
@RequiredArgsConstructor
public class GrouponTradeDetailServiceImpl extends ServiceImpl<GroupTradeDetailMapper, GrouponTradeDetailDO> implements GrouponTradeDetailService {

    private final DynamicHelper dynamicHelper;

    private final GrouponMapper grouponMapper;

    @Override
    public void create(MtCouponTradeDetailRespDTO mtCouponTradeDetailRespDTO) {
        if (Objects.isNull(mtCouponTradeDetailRespDTO)) {
            log.error("团购订单结算明细为空");
            return;
        }
        GrouponTradeDetailDO grouponTradeDetailDO = new GrouponTradeDetailDO();
        copyProperties(mtCouponTradeDetailRespDTO, grouponTradeDetailDO);

        // 查询使用记录
        GrouponOrderDTO order = grouponMapper.queryOrderByCode(grouponTradeDetailDO.getCouponCode());
        if (Objects.nonNull(order)) {
            log.info("填充订单信息:{}", JacksonUtils.writeValueAsString(order));
            grouponTradeDetailDO.setOrderGuid(String.valueOf(order.getOrderGuid()));
            grouponTradeDetailDO.setOrderNo(order.getOrderNo());
            // 一键买单
            appendMaitonPayPrice(order, grouponTradeDetailDO);
        }
        log.info("团购结算明细保存实体：{}", JacksonUtils.writeValueAsString(grouponTradeDetailDO));
        baseMapper.saveOrUpdate(grouponTradeDetailDO);
        // 更新groupon的coupon_buy_price
        if (Objects.isNull(order) || Objects.equals(GrouponReceiptChannelEnum.MAITON.getCode(), order.getReceiptChannel())) {
            return;
        }
        grouponMapper.updateCouponBuyPriceByCode(GroupBuyTypeEnum.MEI_TUAN.getCode(),
                grouponTradeDetailDO.getCouponCode(), grouponTradeDetailDO.getCouponBuyPrice());
    }

    /**
     * 一键买单
     */
    private void appendMaitonPayPrice(GrouponOrderDTO order, GrouponTradeDetailDO grouponTradeDetailDO) {
        try {
            String maitonConsumeInfo = order.getMaitonConsumeInfo();
            if (!Objects.equals(order.getReceiptChannel(), GrouponReceiptChannelEnum.MAITON.getCode())
                    || StringUtils.isEmpty(maitonConsumeInfo)) {
                return;
            }
            // 一键买单信息
            MtMaitonConsumeDTO mtMaitonConsumeDTO = JacksonUtils.toObject(MtMaitonConsumeDTO.class, maitonConsumeInfo);
            BigDecimal maitonPayPrice = new BigDecimal(mtMaitonConsumeDTO.getMaitonPayPrice());
            grouponTradeDetailDO.setCouponBuyPrice(grouponTradeDetailDO.getCouponBuyPrice().add(maitonPayPrice));
            grouponTradeDetailDO.setDealValue(grouponTradeDetailDO.getDealValue().add(maitonPayPrice));
            grouponTradeDetailDO.setDue(grouponTradeDetailDO.getDue().add(maitonPayPrice));
        } catch (Exception e) {
            log.error("美团一键买单信息解析异常, ", e);
        }
    }

    @Override
    public void cancel(String orderGuid, String couponCode) {
        if (StringUtils.isEmpty(couponCode) || StringUtils.isEmpty(orderGuid)) {
            return;
        }
        UpdateWrapper<GrouponTradeDetailDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(GrouponTradeDetailDO::getCouponCode, couponCode);
        uw.lambda().eq(GrouponTradeDetailDO::getOrderGuid, orderGuid);
        uw.lambda().set(GrouponTradeDetailDO::getCancel, 1);
        update(uw);
    }


    private void copyProperties(MtCouponTradeDetailRespDTO mtCouponTradeDetailRespDTO, GrouponTradeDetailDO grouponTradeDetailDO) {
        grouponTradeDetailDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.GROUP_TRADE_DETAIL));
        grouponTradeDetailDO.setStoreGuid(mtCouponTradeDetailRespDTO.getStoreGuid());
        grouponTradeDetailDO.setThirdStoreGuid(mtCouponTradeDetailRespDTO.getMtStoreGuid());
        grouponTradeDetailDO.setThirdStoreName(mtCouponTradeDetailRespDTO.getMtStoreName());
        grouponTradeDetailDO.setDealId(mtCouponTradeDetailRespDTO.getDealId());
        grouponTradeDetailDO.setCouponBuyPrice(BigDecimal.valueOf(mtCouponTradeDetailRespDTO.getCouponBuyPrice()));
        grouponTradeDetailDO.setBuyPrice(BigDecimal.valueOf(mtCouponTradeDetailRespDTO.getBuyPrice()));
        grouponTradeDetailDO.setDealValue(BigDecimal.valueOf(mtCouponTradeDetailRespDTO.getDealValue()));
        grouponTradeDetailDO.setBizCost(BigDecimal.valueOf(mtCouponTradeDetailRespDTO.getBizCost()));
        grouponTradeDetailDO.setDue(BigDecimal.valueOf(mtCouponTradeDetailRespDTO.getDue()));
        grouponTradeDetailDO.setVolume(mtCouponTradeDetailRespDTO.getVolume());
        grouponTradeDetailDO.setSingleValue(BigDecimal.valueOf(mtCouponTradeDetailRespDTO.getSingleValue()));
        grouponTradeDetailDO.setOrderId(mtCouponTradeDetailRespDTO.getOrderId());
        grouponTradeDetailDO.setCancel(0);

        MtCouponDetailRespDTO couponDetail = mtCouponTradeDetailRespDTO.getCouponDetail();
        if (Objects.nonNull(couponDetail)) {
            grouponTradeDetailDO.setCouponCode(couponDetail.getCouponCode());
            grouponTradeDetailDO.setCouponStatusDesc(couponDetail.getCouponStatusDesc());
            grouponTradeDetailDO.setDealTitle(couponDetail.getDealTitle());
            grouponTradeDetailDO.setDealBeginTime(couponDetail.getDealBeginTime());
            grouponTradeDetailDO.setCouponUseTime(couponDetail.getCouponUseTime());
            grouponTradeDetailDO.setCouponCancelStatus(couponDetail.getCouponCancelStatus());
            grouponTradeDetailDO.setVerifyAcct(couponDetail.getVerifyAcct());
            grouponTradeDetailDO.setVerifyType(couponDetail.getVerifyType());
            grouponTradeDetailDO.setIsVoucher(couponDetail.getIsVoucher());
        }
    }

}
