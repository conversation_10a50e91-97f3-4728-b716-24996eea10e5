package com.holderzone.saas.store.trade.entity.dto;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 保存用戶支付信息
 */
@Data
public class BillPayInfoDTO {

    @ApiModelProperty(value = "billPayReqDTO")
    private BillPayReqDTO billPayReqDTO;

    @ApiModelProperty(value = "userContext")
    private UserContext userContext;

}
