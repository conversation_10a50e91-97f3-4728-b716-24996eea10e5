package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 调整单 mapper
 */
@Repository
public interface AdjustOrderMapper extends BaseMapper<AdjustOrderDO> {

    List<Long> listByOrderGuids(@Param("orderGuids") List<Long> orderGuids);

}
