package com.holderzone.saas.store.trade.controller;

import com.holderzone.saas.store.trade.service.OrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/order_refresh")
@Api(description = "订单库刷新接口")
@Slf4j
public class RefreshOrderController {


    @Resource
    OrderDetailService orderDetailService;

    @ApiOperation(value = "刷新订单库时间", notes = "刷新订单库时间")
    @GetMapping("/refreshModifyTime")
    public Boolean refreshModifyTime(@RequestParam(value="enterpriseGuid",required = true) String enterpriseGuid,
                                     @RequestParam(value="orderGuid",required = false)String orderGuid){


            //Date dateTime = simpleDateFormat.parse(dayTime);
        orderDetailService.refreshModifyTime(enterpriseGuid,orderGuid);
        return true;



    }

    @ApiOperation(value = "刷新订单库时间", notes = "刷新订单库时间")
    @GetMapping("/refreshModifyTimeByDay")
    public boolean refreshModifyTimeByDay(@RequestParam(value="enterpriseGuid",required = true) String enterpriseGuid){
        //Date dateTime = simpleDateFormat.parse(dayTime);
       return orderDetailService.refreshModifyTimeByDay(enterpriseGuid);
    }

    @ApiOperation(value = "刷新订单库时间", notes = "刷新订单库时间")
    @GetMapping("/getNeedUpdateOrderGuid")
    public String getNeedUpdateOrderGuid(@RequestParam("enterpriseGuid") String enterpriseGuid){
        return orderDetailService.getNeedUpdateOrderGuid(enterpriseGuid);
    }
}
