package com.holderzone.saas.store.trade.entity.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.request.waiter.OrderWaiterOperatorDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/11/30 15:40
 * @description
 */
@NoArgsConstructor
@Data
public class OrderWaiterQueryDetails {
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "开始日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始日期时间不能为空")
    private LocalDateTime startDateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "营业结束日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "结束日期时间不能为空")
    private LocalDateTime endDateTime;

    @NotNull(message = "服务员类型")
    @ApiModelProperty(value = "服务员类型(1, 喊客员),(2, 服务员),(3, 传菜员),(4, 收台员),(5, 洗碗员)")
    private Integer waiterType;

    @NotNull(message = "查询类型")
    @ApiModelProperty(value = "查询类型(1, 开台时间),(2, 结账时间)")
    private Integer timeType;

    @ApiModelProperty(value = "门店Guid集合")
    private List<String> storeGuidList;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单金额前置运算条件")
    private OrderWaiterOperatorDTO frontOrderOperator;

    @ApiModelProperty(value = "订单金额后置运算条件")
    private OrderWaiterOperatorDTO rearOrderOperator;

    @ApiModelProperty(value = "桌台Guid集合")
    private List<String> tableGuidList;

}
