package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoCard;
import com.holderzone.saas.store.dto.print.content.PrintDebtRepaymentDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitRecordUpdateReqDTO;
import com.holderzone.saas.store.enums.DebtPaymentTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.trade.config.RocketMqConfig;
import com.holderzone.saas.store.trade.entity.domain.DebtUnitDO;
import com.holderzone.saas.store.trade.entity.domain.DebtUnitRecordDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.DebtUnitRecordPrintService;
import com.holderzone.saas.store.trade.service.DebtUnitRecordService;
import com.holderzone.saas.store.trade.service.DebtUnitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 挂账打印
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DebtUnitRecordPrintServiceImpl implements DebtUnitRecordPrintService {

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final DebtUnitRecordService debtUnitRecordService;

    private final DebtUnitService debtUnitService;

    private final OrderService orderService;

    private final MemberTerminalClientService memberTerminalClientService;

    @Override
    public void print(DebtUnitRecordUpdateReqDTO updateReqDTO) {
        if (StringUtils.isEmpty(updateReqDTO.getRepaymentBatchNumber())) {
            return;
        }
        PrintDebtRepaymentDTO printDTO = buildPrintDebtRepaymentDTO(updateReqDTO);
        if (Objects.isNull(printDTO)) {
            log.error("挂账还款打印DTO为null, updateReqDTO:{}", JacksonUtils.writeValueAsString(updateReqDTO));
            return;
        }
        sendPrintMessage(printDTO);
    }

    /**
     * 构建挂账小票打印
     */
    private PrintDebtRepaymentDTO buildPrintDebtRepaymentDTO(DebtUnitRecordUpdateReqDTO updateReqDTO) {
        // 查询此批次的还款记录
        List<DebtUnitRecordDO> debtUnitRecordList = debtUnitRecordService.listByRepaymentBatchNumber(updateReqDTO.getRepaymentBatchNumber());
        if (CollectionUtils.isEmpty(debtUnitRecordList)) {
            log.error("还款记录为空,repaymentBatchNumber:{}", updateReqDTO.getRepaymentBatchNumber());
            return null;
        }
        DebtUnitRecordDO debtUnitRecord = debtUnitRecordList.get(0);
        // 查询挂账单位
        DebtUnitDO debtUnitDO = debtUnitService.getById(debtUnitRecord.getUnitGuid());
        if (Objects.isNull(debtUnitDO)) {
            log.error("挂账单位为空, unitGuid:{}", debtUnitRecord.getUnitGuid());
            return null;
        }
        // 查询订单信息
        List<Long> orderGuidList = debtUnitRecordList.stream()
                .map(DebtUnitRecordDO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        List<OrderDO> orderList = orderService.listByIds(orderGuidList);
        Map<Long, OrderDO> orderMap = orderList.stream().collect(Collectors.toMap(OrderDO::getGuid,
                Function.identity(), (key1, key2) -> key1));

        PrintDebtRepaymentDTO printDebtRepaymentDTO = new PrintDebtRepaymentDTO();
        // 设置打印公有参数
        setBasePrintDebtRepaymentDTO(printDebtRepaymentDTO, updateReqDTO);
        // 打印内容
        ResponseMemberInfoCard memberCardInfo = memberTerminalClientService.queryMemberCardInfo(debtUnitRecord.getMemberCardGuid());
        if (!ObjectUtils.isEmpty(memberCardInfo)) {
            printDebtRepaymentDTO.setMemberMoney(memberCardInfo.getCardMoney());
        }
        printDebtRepaymentDTO.setMemberPhone(DesensitizedUtil.mobilePhone(debtUnitRecord.getMemberPhone()));
        printDebtRepaymentDTO.setMemberName(debtUnitRecord.getMemberName());
        printDebtRepaymentDTO.setUnitName(debtUnitDO.getName());
        printDebtRepaymentDTO.setUnitContactName(debtUnitDO.getContactName());
        printDebtRepaymentDTO.setUnitContactTel(DesensitizedUtil.mobilePhone(debtUnitDO.getContactTel()));
        // 还款记录
        List<PrintDebtRepaymentDTO.InnerRepaymentRecord> repaymentRecords = debtUnitRecordList.stream().map(r -> {
            PrintDebtRepaymentDTO.InnerRepaymentRecord repaymentRecord = new PrintDebtRepaymentDTO.InnerRepaymentRecord();
            repaymentRecord.setGmtCreate(r.getGmtCreate());
            repaymentRecord.setRepaymentFee(r.getRepaymentFee());
            OrderDO orderDO = orderMap.getOrDefault(r.getOrderGuid(), new OrderDO());
            repaymentRecord.setDiningTableName(orderDO.getDiningTableName());
            return repaymentRecord;
        }).collect(Collectors.toList());
        printDebtRepaymentDTO.setRecordList(repaymentRecords);
        printDebtRepaymentDTO.setPaymentTypeName(DebtPaymentTypeEnum.getDescByCode(debtUnitRecord.getPaymentType()));
        // 还款合计
        BigDecimal repaymentTotalFee = repaymentRecords.stream()
                .map(PrintDebtRepaymentDTO.InnerRepaymentRecord::getRepaymentFee)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        printDebtRepaymentDTO.setRepaymentTotalFee(repaymentTotalFee);
        printDebtRepaymentDTO.setUpdateStaffName(debtUnitRecord.getUpdateStaffName());
        printDebtRepaymentDTO.setGmtModified(debtUnitRecord.getGmtModified());
        return printDebtRepaymentDTO;
    }

    /**
     * 设置打印公有参数
     */
    private void setBasePrintDebtRepaymentDTO(PrintDebtRepaymentDTO printDebtRepaymentDTO, DebtUnitRecordUpdateReqDTO updateReqDTO) {
        UserContext userContext = UserContextUtils.get();
        printDebtRepaymentDTO.setInvoiceType(InvoiceTypeEnum.DEBT_REPAYMENT.getType());
        printDebtRepaymentDTO.setDeviceId(updateReqDTO.getDeviceId());
        printDebtRepaymentDTO.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(updateReqDTO.getDeviceType()));
        printDebtRepaymentDTO.setPrintUid(System.currentTimeMillis() + "");
        printDebtRepaymentDTO.setEnterpriseGuid(userContext.getEnterpriseGuid());
        printDebtRepaymentDTO.setStoreGuid(userContext.getStoreGuid());
        printDebtRepaymentDTO.setAreaGuid(null);
        printDebtRepaymentDTO.setOperatorStaffGuid(userContext.getUserGuid());
        printDebtRepaymentDTO.setOperatorStaffName(userContext.getUserName());
        //打印时间
        printDebtRepaymentDTO.setCreateTime(DateTimeUtils.nowMillis());
    }

    /**
     * 打印
     */
    private void sendPrintMessage(PrintDebtRepaymentDTO printDTO) {
        log.info("printBody:{}", JacksonUtils.writeValueAsString(printDTO));
        Message message = new Message(RocketMqConfig.PRINT_MESSAGE_TOPIC,
                RocketMqConfig.PRINT_MESSAGE_TAG, JacksonUtils.toJsonByte(printDTO));
        message.getProperties().put(RocketMqConfig.MESSAGE_CONTEXT, UserContextUtils.getJsonStr());
        message.getProperties().put(RocketMqConfig.MESSAGE_LOCALE, JacksonUtils.writeValueAsString(LocaleContextHolder.getLocale()));
        defaultRocketMqProducer.sendMessage(message);
    }
}
