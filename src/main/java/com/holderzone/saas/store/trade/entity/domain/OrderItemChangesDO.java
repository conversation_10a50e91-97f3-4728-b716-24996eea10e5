package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 订单商品换菜表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_order_item_changes")
public class OrderItemChangesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 订单商品明细
     */
    private Long orderItemGuid;

    /**
     * 上一次菜品明细guid
     */
    private Long originalOrderItemGuid;

    /**
     * 换菜营业日
     */
    private LocalDate businessDay;

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品编号
     */
    private String code;

    /**
     * 核算价
     */
    private BigDecimal accountingPrice;

    /**
     * 商品类别guid
     */
    private String itemTypeGuid;

    /**
     * 商品类别名称
     */
    private String itemTypeName;

    /**
     * 商品的规格
     */
    private String skuGuid;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * sku价格
     */
    private BigDecimal price;

    /**
     * 是否有属性（0：否，1：是）
     */
    private Integer hasAttr;

    /**
     * 属性总价
     */
    private BigDecimal attrTotal;

    /**
     * 商品类型(1.套餐主项，2.规格，3.称重，4.单品 5.团餐主项)
     */
    private Integer itemType;

    /**
     * 当前数量（不包括赠送，不要重复减赠送折扣）
     */
    private BigDecimal currentCount;

    /**
     * 赠送数量（销售统计=当前+赠送）
     */
    private BigDecimal freeCount;

    /**
     * 退货数量（赠送变为退之后不计入销售和增菜统计）
     */
    private BigDecimal returnCount;

    /**
     * 套餐预设数量
     */
    private BigDecimal packageDefaultCount;

    /**
     * 子项加价
     */
    private BigDecimal addPrice;

    /**
     * 计数单位
     */
    private String unit;

    /**
     * 商品备注
     */
    private String remark;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 是否撤销
     */
    private Integer cancelFlag;

    /**
     * 换菜节点
     */
    private String changeNode;

    /**
     * 换菜批次号
     */
    private String changeBatchNumber;

    /**
     * 创建操作人guid
     */
    private String createStaffGuid;

    /**
     * 创建操作人guid
     */
    private String createStaffName;

    /**
     * 反结账原换菜明细guid
     */
    @TableField(exist = false)
    private Long originalChangeItemGuid;
}
