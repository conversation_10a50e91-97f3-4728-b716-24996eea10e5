package com.holderzone.saas.store.trade.hystrix;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.netflix.hystrix.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CheckOutHystrixCommand extends HystrixCommand<String> {

    private BillPayReqDTO billPayReqDTO;

    private OrderDO orderDO;

    private UserContext userContext;

    public CheckOutHystrixCommand(BillPayReqDTO billPayReqDTO, OrderDO orderDO, UserContext userContext) {

        super(Setter.withGroupKey(
                //服务分组
                HystrixCommandGroupKey.Factory.asKey("OrderGroup"))
                //线程分组
                .andThreadPoolKey(HystrixThreadPoolKey.Factory.asKey("CheckOutPool"))

                //线程池配置
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withCoreSize(40)
                        .withMaximumSize(220)
                        .withKeepAliveTimeMinutes(5)
                        .withMaxQueueSize(1500)
                        .withQueueSizeRejectionThreshold(10000))

                .andCommandPropertiesDefaults(
                        HystrixCommandProperties.Setter()
                                .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy
                                        .THREAD))
        )
        ;
        this.billPayReqDTO = billPayReqDTO;
        this.orderDO = orderDO;
        this.userContext = userContext;
    }

    @Override
    public String run(){
        return null;
    }


}