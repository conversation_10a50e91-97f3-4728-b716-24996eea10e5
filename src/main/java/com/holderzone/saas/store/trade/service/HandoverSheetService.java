package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverSheetService
 * @date 2019/04/08 10:30
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
public interface HandoverSheetService {

    /**
     * 交接班详情
     *
     * @param handoverPayQueryDTO
     * @return
     */
    HandoverPayDTO handover(HandoverPayQueryDTO handoverPayQueryDTO);

    /**
     * 交接班详情（新），会员充值详情不合并，现金支付金额单独列出
     *
     * @param handoverPayQueryDTO
     * @return
     */
    HandoverPayDTO handoverNew(HandoverPayQueryDTO handoverPayQueryDTO);

    /**
     * 交接班查询第三方活动使用明细
     */
    List<AmountItemDTO.InnerDetails> handoverNewByThirdActivity(HandoverPayQueryDTO handoverPayQueryDTO);

    /**
     * 交接班查询团购验券统计及使用明细
     */
    List<AmountItemDTO> handoverNewByDiscount(HandoverPayQueryDTO handoverPayQueryDTO);

    /**
     * 查询零售交接班数据
     * @param handoverPayQueryDTO 查询条件
     * @return 零售数据
     */
    HandoverPayDTO retailHandover(HandoverPayQueryDTO handoverPayQueryDTO);
}
