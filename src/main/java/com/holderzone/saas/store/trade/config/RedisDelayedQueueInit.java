package com.holderzone.saas.store.trade.config;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.trade.event.delay.RedisDelayedQueueListener;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @description redis延时队列初始化
 * @date 2021/8/31 11:51
 */
@Component
@Slf4j
public class RedisDelayedQueueInit implements ApplicationContextAware {

    @Resource
    private RedissonClient redissonClient;

    @Resource(name = "queueInitExecutorService")
    private ExecutorService queueInitExecutorService;

    @Primary
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, RedisDelayedQueueListener> map = applicationContext.getBeansOfType(RedisDelayedQueueListener.class);
        for (Map.Entry<String, RedisDelayedQueueListener> taskEventListenerEntry : map.entrySet()) {
            String listenerName = taskEventListenerEntry.getValue().getClass().getName();
            startThread(listenerName, taskEventListenerEntry.getValue());
        }
    }

    /**
     * 功能描述：启动线程获取队列
     *
     * @param queueName                 队列名称
     * @param redisDelayedQueueListener 任务回调监听
     * @date 2021/4/14
     */
    private <T> void startThread(String queueName, RedisDelayedQueueListener redisDelayedQueueListener) {
        RBlockingQueue<T> blockingFairQueue = redissonClient.getBlockingQueue(queueName);
        //常驻线程不需要线程池
        Thread thread = new Thread(() -> {
            log.info("启动监听队列线程:{}", queueName);
            while (true) {
                try {
                    T t = blockingFairQueue.take();
                    log.info("监听队列线程{},获取到值:{}", queueName, JSON.toJSONString(t));
                    //执行线程调用线程池
                    queueInitExecutorService.execute(() -> {
                        redisDelayedQueueListener.invoke(t);
                    });
                } catch (Exception e) {
                    log.info("监听队列线程错误,{}", e.toString());
                    try {
                        Thread.sleep(10000);
                    } catch (InterruptedException ex) {
                    }
                }
            }
        });
        thread.setName(queueName);
        thread.start();
    }
}
