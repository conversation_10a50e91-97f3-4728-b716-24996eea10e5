package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.OrderItemExtendsDO;

import java.util.List;
import java.util.Set;


/**
 * <p>
 * 订单商品扩展表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface OrderItemExtendsService extends IService<OrderItemExtendsDO> {

    List<OrderItemExtendsDO> listByOrderGuid(Long orderGuid);

    void restoreOrderItemExtends(List<Long> orderItemGuidList);

    List<OrderItemExtendsDO> listAllByGuid(Set<String> oldOrderItemGuids);
}
