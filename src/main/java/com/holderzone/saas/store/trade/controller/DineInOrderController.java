package com.holderzone.saas.store.trade.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.BusinessDayDTO;
import com.holderzone.saas.store.dto.order.OrderLockDTO;
import com.holderzone.saas.store.dto.order.common.BaseOrderDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.request.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.*;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.print.PrintLabelReq;
import com.holderzone.saas.store.trade.anno.LocalizeRequireNotifyOrderGuids;
import com.holderzone.saas.store.trade.anno.RequireOrderCheckLock;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.BillService;
import com.holderzone.saas.store.trade.service.DineInOffLineService;
import com.holderzone.saas.store.trade.service.DineInService;
import com.holderzone.saas.store.trade.service.OrderLockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderController
 * @date 2019/01/04 8:53
 * @description 堂食订单接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/dine_in_order")
@Api(description = "正餐接口")
@AllArgsConstructor
@Slf4j
public class DineInOrderController {

    private final DineInService dineInService;

    private final DineInOffLineService dineInOffLineService;

    private final BillService billService;

    private final OrderLockService orderLockService;
    @Resource
    private OrderService orderService;

    @ApiOperation(value = "创建订单", notes = "创建订单")
    @PostMapping("/create")
    @LocalizeRequireNotifyOrderGuids
    public CreateDineInOrderReqDTO create(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        log.info("创建订单入参：{}", JacksonUtils.writeValueAsString(createDineInOrderReqDTO));
        return dineInService.createOrder(createDineInOrderReqDTO);

    }

    @ApiOperation(value = "批量保存离线订单", notes = "批量保存离线订单")
    @PostMapping("/batch_save_offline_order")
    @LocalizeRequireNotifyOrderGuids
    public BatchFastOfflineOrderResp batchSaveOfflineOrder(@RequestBody BatchCreateFastFoodReqDTO fastFoodReqDTO) {
        log.info("批量保存离线订单：{}", JacksonUtils.writeValueAsString(fastFoodReqDTO));
        return dineInOffLineService.batchSaveFastOrder(fastFoodReqDTO);

    }

    @ApiOperation(value = "增加版本号", notes = "增加版本号")
    @PostMapping("/add/version")
    public Boolean addVersion(@RequestBody String orderGuid) {
        log.info("增加版本号：{}", JacksonUtils.writeValueAsString(orderGuid));
        return dineInService.addVersion(orderGuid);
    }

    @ApiOperation(value = "获取版本号", notes = "获取版本号")
    @PostMapping("/get/version")
    public String getVersion(@RequestBody String orderGuid) {
        log.info("获取版本号：{}", JacksonUtils.writeValueAsString(orderGuid));
        return dineInService.getVersion(orderGuid);
    }


    @ApiOperation(value = "批量查询订单桌台信息", notes = "批量查询订单桌台信息")
    @PostMapping("/batch_get_table_info")
    public List<OrderTableInfoDTO> batchGetTableInfo(@RequestBody OrderGuidsDTO orderGuidsDTO) {
        return dineInService.batchGetTableInfo(orderGuidsDTO);
    }

    /**
     * 批量查询订单桌台信息
     * 专用于桌台查询所用
     *
     * @param orderGuidsDTO 订单
     * @return 订单信息
     */
    @ApiOperation(value = "批量查询订单桌台信息", notes = "批量查询订单桌台信息")
    @PostMapping("/batch_get_table_info2")
    public List<OrderTableInfoDTO> batchGetTableInfo2(@RequestBody OrderGuidsDTO orderGuidsDTO) {
        log.info("批量查询订单桌台信息,orderGuidsDTO={}", JacksonUtils.writeValueAsString(orderGuidsDTO));
        if (StringUtils.hasText(orderGuidsDTO.getEnterpriseGuid())) {
            UserContextUtils.putErp(orderGuidsDTO.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(orderGuidsDTO.getEnterpriseGuid());
        }
        return dineInService.batchGetTableInfo2(orderGuidsDTO);
    }

    @ApiOperation(value = "收款方式下拉", notes = "收款方式下拉")
    @GetMapping("/list_payment_type_name")
    public List<String> listPaymentTypeName() {
        return dineInService.listPaymentTypeName();
    }

    @ApiOperation(value = "订单列表", notes = "订单列表")
    @PostMapping("/order_list")
    public Page<DineInOrderListRespDTO> orderList(@RequestBody DineInOrderListReqDTO dineInOrderListReqDTO) {
        return dineInService.orderListNew(dineInOrderListReqDTO);
    }

    @ApiOperation(value = "获取订单菜品列表详情", notes = "获取订单菜品列表详情")
    @PostMapping("/get_order_detail_4_add_item")
    public DineinOrderDetailRespDTO getOrderDetail4AddItem(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("获取订单菜品列表详情 singleDataDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
        return dineInService.getOrderDetail4AddItem(singleDataDTO.getData());
    }

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @PostMapping("/get_order_detail")
    public DineinOrderDetailRespDTO getOrderDetail(@RequestBody OrderDetailQueryDTO orderDetailQueryDTO) {
        if (StringUtils.hasText(orderDetailQueryDTO.getEnterpriseGuid())) {
            UserContextUtils.putErp(orderDetailQueryDTO.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(orderDetailQueryDTO.getEnterpriseGuid());
        }
        return dineInService.getOrderDetailForAndroid(orderDetailQueryDTO);
    }

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @PostMapping("/get_order_detail_for_wx")
    public DineinOrderDetailRespDTO getOrderDetailForWx(@RequestBody OrderDetailQueryDTO orderDetailQueryDTO) {
        if (StringUtils.hasText(orderDetailQueryDTO.getEnterpriseGuid())) {
            UserContextUtils.putErp(orderDetailQueryDTO.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(orderDetailQueryDTO.getEnterpriseGuid());
        }
        OrderDO orderDO = orderService.getByIdWithCache(orderDetailQueryDTO.getData());
        if (Objects.nonNull(orderDO) && UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())
                && Objects.nonNull(orderDO.getMainOrderGuid()) && !Objects.equals(0L, orderDO.getMainOrderGuid())) {
            // 查询主单
            orderDetailQueryDTO.setData(String.valueOf(orderDO.getMainOrderGuid()));
        }
        return dineInService.getOrderDetailForAndroid(orderDetailQueryDTO);
    }

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/get_order_detail/{orderGuid}")
    public DineinOrderDetailRespDTO getOrderDetail(@PathVariable String orderGuid) {
        return dineInService.getOrderDetail(orderGuid);
    }

    /**
     * 根据订单guid获取订单商品详情
     */
    @GetMapping("/get_order_item_detail/{orderGuid}")
    public DineinOrderDetailRespDTO getOrderItemDetail(@PathVariable String orderGuid) {
        return dineInService.getOrderItemDetail(orderGuid);
    }

    @ApiOperation(value = "作废订单", notes = "作废订单")
    @PostMapping("/cancel")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean cancelOrder(@RequestBody CancelOrderReqDTO cancelOrderReqDTO) {
        log.info("作废订单入参:{}", JacksonUtils.writeValueAsString(cancelOrderReqDTO));
        Boolean result = dineInService.cancelOrder(cancelOrderReqDTO);
        // 多单结账 作废最后一笔单子
        if (Boolean.TRUE.equals(result) && Boolean.TRUE.equals(cancelOrderReqDTO.getCheckoutOrderFlag())) {
            BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
            BeanUtils.copyProperties(cancelOrderReqDTO, billPayReqDTO);
            billPayReqDTO.setOrderGuid(cancelOrderReqDTO.getOrderGuid());
            billService.orderCheckoutSuccess(billPayReqDTO);
        }
        return result;
    }

    @ApiOperation(value = "修改整单备注", notes = "修改整单备注")
    @PostMapping("/update_remark")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean updateRemark(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        return dineInService.updateRemark(createDineInOrderReqDTO);

    }

    @ApiOperation(value = "修改就餐人数", notes = "修改就餐人数")
    @PostMapping("/update_guest_count")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean updateGuestCount(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        log.info("修改就餐人数入参:{}", JacksonUtils.writeValueAsString(createDineInOrderReqDTO));
        return dineInService.updateGuestCount(createDineInOrderReqDTO);
    }

    @ApiOperation(value = "打印菜品清单", notes = "打印菜品清单")
    @PostMapping("/print_item_detail")
    public boolean printItemDetail(@RequestBody SingleDataDTO singleDataDTO) {
        return dineInService.printItemDetail(singleDataDTO, singleDataDTO.getData());
    }

    @ApiOperation(value = "打印菜品复单", notes = "打印菜品复单")
    @PostMapping("/print_item_repeat_order")
    public boolean printItemRepeatOrder(@RequestBody CreateDineInOrderReqDTO itemRepeatOrderReqDTO) {
        log.info("[打印菜品复单]itemRepeatOrderReqDTO={}", JacksonUtils.writeValueAsString(itemRepeatOrderReqDTO));
        return dineInService.printItemRepeatOrder(itemRepeatOrderReqDTO);
    }

    @ApiOperation(value = "打印预结单", notes = "打印预结单")
    @PostMapping("/print_pre_bill")
    public boolean printPreBill(@RequestBody SingleDataDTO singleDataDTO) {
        return billService.printPreBill(singleDataDTO);
    }

    @ApiOperation(value = "打印结账单", notes = "打印结账单")
    @PostMapping("/print_check_out")
    public boolean printCheckOut(@RequestBody SingleDataDTO singleDataDTO) {
        return dineInService.printCheckOut(singleDataDTO, singleDataDTO.getData());
    }

    @ApiOperation(value = "标签重打", notes = "标签重打")
    @PostMapping("/reprint_label")
    public void reprintLabel(@RequestBody PrintLabelReq printLabelReq) {
        log.info("[商品标签重打]printLabelReq={}", JacksonUtils.writeValueAsString(printLabelReq));
        dineInService.printLabel(printLabelReq);
    }

    @ApiOperation(value = "检查订单是否被锁住", notes = "检查订单是否被锁住")
    @PostMapping("/check/islock")
    public boolean checkOrderLock(@RequestBody BaseOrderDTO baseOrderDTO) {
        return orderLockService.checkOrderLock(baseOrderDTO.getOrderGuid());
    }

    @ApiOperation(value = "加订单锁", notes = "加订单锁")
    @PostMapping("/order/lock")
    public boolean lockorder(@RequestBody OrderLockDTO orderLockDto) {
        return orderLockService.tryLockwithDeviceId(orderLockDto.getOrderGuid(), orderLockDto.getDeviceId());
    }

    @ApiOperation(value = "去掉订单锁", notes = "去掉订单锁")
    @PostMapping("/order/unlock")
    public void unLockOrder(@RequestBody OrderLockDTO orderLockDto) {
        orderLockService.unlockBydeviceId(orderLockDto.getOrderGuid(), orderLockDto.getDeviceId());
    }


    @ApiOperation(value = "检查是否存在未结账的订单，true存在，false不存在", notes = "检查是否存在未结账的订单")
    @GetMapping("/checkOrder/pending/{memberInfoGuid}")
    public boolean checkOrderPending(@PathVariable("memberInfoGuid") String memberInfoGuid) {
        if (StringUtils.isEmpty(memberInfoGuid)) {
            return false;
        }
        return orderService.checkOrderPending(memberInfoGuid);
    }

    /**
     * 随行红包使用设置
     *
     * @param updateOrderReqDTO 订单guid
     * @return 结果
     */
    @ApiOperation(value = "随行红包使用设置")
    @PostMapping("/set_follow_red_packet")
    public Boolean setFollowRedPacket(@RequestBody UpdateOrderReqDTO updateOrderReqDTO) {
        log.info("随行红包使用设置 入参={}", JacksonUtils.writeValueAsString(updateOrderReqDTO));
        return orderService.setFollowRedPacket(updateOrderReqDTO);
    }

    /**
     * 根据订单查询订单状态
     *
     * @param orderGuid 订单guid
     * @return 订单状态 (-1：未查询到订单 1：未结账， 2：已结账， 3：已退款，4：已作废)
     */
    @ApiOperation(value = "根据订单查询订单状态")
    @GetMapping("/get_order_state_by_guid")
    public Integer getOrderStateByGuid(@RequestParam("orderGuid") String orderGuid) {
        return orderService.getOrderStateByGuid(orderGuid);
    }

    /**
     * 根据订单查询订单金额
     *
     * @param orderGuid 订单guid
     * @return 订单金额
     */
    @ApiOperation(value = "根据订单查询订单金额")
    @GetMapping("/get_order_fee_by_guid")
    public BigDecimal getOrderFeeByGuid(@RequestParam("orderGuid") String orderGuid) {
        return orderService.getOrderFeeByGuid(orderGuid);
    }

    /**
     * 查询订单营业日
     *
     * @param businessDayDTOList 企业，门店，订单编号
     * @return 订单营业日，为空则用结账日
     */
    @ApiOperation(value = "查询订单营业日")
    @PostMapping("/query_order_business_day")
    public List<BusinessDayDTO> queryOrderBusinessDay(@RequestBody List<BusinessDayDTO> businessDayDTOList) {
        return orderService.queryOrderBusinessDay(businessDayDTOList);
    }
}
