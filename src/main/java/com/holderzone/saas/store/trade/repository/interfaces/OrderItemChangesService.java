package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.OrderItemChangesDO;

import java.util.List;
import java.util.Set;

/**
 * 订单商品换菜表 服务类
 */
public interface OrderItemChangesService extends IService<OrderItemChangesDO> {

    /**
     * 查询换菜明细
     */
    List<OrderItemChangesDO> listByOrderItemGuidList(Long orderGuid, List<Long> orderItemGuidList);

    List<OrderItemChangesDO> listByOrderGuid(Long orderGuid);

    List<OrderItemChangesDO> getChangeItemByBatchNumbers(Long orderGuid, List<String> changeBatchNumbers);

    /**
     * 删除换菜记录
     */
    void removeByItemGuidList(List<Long> orderItemGuidList);

    /**
     * 删除换菜记录
     */
    void removeByOrderGuidList(List<Long> orderGuidList);

    List<OrderItemChangesDO> listByOrderItemGuidList(Set<String> oldOrderItemGuids);
}
