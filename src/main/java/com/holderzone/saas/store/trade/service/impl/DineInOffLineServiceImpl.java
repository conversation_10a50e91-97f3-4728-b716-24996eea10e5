package com.holderzone.saas.store.trade.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.*;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.BatchCreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.BatchFastOfflineOrderResp;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.*;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.*;
import com.holderzone.saas.store.trade.service.inner.impls.AsyncTradeServiceImpl;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;

import static com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant.*;

/**
 * 离线订单
 *
 * <AUTHOR>
 * @date 2021/5/26
 **/
@Service
@Slf4j
public class DineInOffLineServiceImpl implements DineInOffLineService {

    private final DynamicHelper dynamicHelper;

    private final OrderService orderService;

    private final FastFoodService fastFoodService;

    private final OrderItemService orderItemService;

    private final BillService billService;

    private final AppendFeeService appendFeeService;

    private final OrderItemRecordService orderItemRecordService;

    private final TransactionRecordService transactionRecordService;

    private final ItemAttrService itemAttrService;

    private final DineInService dineInService;

    private final DiscountService discountService;

    private final CalculateService calculateService;

    private final FreeReturnItemService freeReturnItemService;

    private final OrderTransform orderTransform = OrderTransform.INSTANCE;

    private final AsyncTradeServiceImpl asyncTradeService;

    @Resource(name = "batchOrderThreadPool")
    private ExecutorService batchOrderThreadPool;

    public DineInOffLineServiceImpl(DynamicHelper dynamicHelper, OrderService orderService,
                                    FastFoodService fastFoodService, OrderItemService orderItemService,
                                    BillService billService, AppendFeeService appendFeeService,
                                    OrderItemRecordService orderItemRecordService,
                                    TransactionRecordService transactionRecordService,
                                    ItemAttrService itemAttrService, DineInService dineInService,
                                    DiscountService discountService, CalculateService calculateService,
                                    FreeReturnItemService freeReturnItemService,
                                    AsyncTradeServiceImpl asyncTradeService) {
        this.dynamicHelper = dynamicHelper;
        this.orderService = orderService;
        this.fastFoodService = fastFoodService;
        this.orderItemService = orderItemService;
        this.billService = billService;
        this.appendFeeService = appendFeeService;
        this.orderItemRecordService = orderItemRecordService;
        this.transactionRecordService = transactionRecordService;
        this.itemAttrService = itemAttrService;
        this.dineInService = dineInService;
        this.discountService = discountService;
        this.calculateService = calculateService;
        this.freeReturnItemService = freeReturnItemService;
        this.asyncTradeService = asyncTradeService;
    }

    @Override
    @Transactional
    public BatchFastOfflineOrderResp batchSaveFastOrder(@RequestBody BatchCreateFastFoodReqDTO batchCreateFastFoodReqDTO) {
        List<BatchCreateFastFoodReqDTO.FastFoodReqDTO> foodReqDTOList =
                Optional.ofNullable(batchCreateFastFoodReqDTO).map(BatchCreateFastFoodReqDTO::getFoodReqDTOList).
                        orElseThrow(() -> new BusinessException("参数错误"));
        //按订单时间排序
        foodReqDTOList.sort(Comparator.comparing(BatchCreateFastFoodReqDTO.FastFoodReqDTO::getOrderTime));
        List<BatchFastOfflineOrderResp.FastFoodRespDTO> successList = new ArrayList<>();
        for (BatchCreateFastFoodReqDTO.FastFoodReqDTO foodReqDTO : foodReqDTOList) {
            //1.添加订单
            if (!saveOrder(foodReqDTO)) {
                log.info("订单已存在支付记录，不能重复导入:orderNo = {},orderTime = {}", foodReqDTO.getOrderNo(), foodReqDTO.getOrderTime());
                //订单已存在默认为成功
                successList.add(new BatchFastOfflineOrderResp.FastFoodRespDTO().setGuid(foodReqDTO.getGuid()).setOrderNo(foodReqDTO.getOrderNo()));
                continue;
            }

            //2.添加订单商品
            saveOrderItems(foodReqDTO);

            //3.结账时计算账单金额和获取订单详情
//            final BillCalculateReqDTO billCalculateReqDTO =
//                    orderTransform.batchCreateFastFoodReqDTO2BillCalculateReqDTO(foodReqDTO);
//            calculateAmount(billCalculateReqDTO);

            //只上传支付成功的订单
            if (!foodReqDTO.getState().equals(StateEnum.SUCCESS.getCode())) {
                break;
            }

            //4.支付部分
            savePlay(foodReqDTO);
            successList.add(new BatchFastOfflineOrderResp.FastFoodRespDTO().setGuid(foodReqDTO.getGuid()).setOrderNo(foodReqDTO.getOrderNo()));
        }
        BatchFastOfflineOrderResp offlineOrderResp = new BatchFastOfflineOrderResp();
        offlineOrderResp.setSuccessList(successList);
        return offlineOrderResp;
    }

    private void calculateAmount(BillCalculateReqDTO billCalculateReqDTO) {

        OrderDO orderDO = orderService.getById(billCalculateReqDTO.getOrderGuid());
        DineinOrderDetailRespDTO orderDetailRespDTO = calculateService.getSingleOrderDetail(orderDO);
        //并单取主单优惠规则&&返回当前的登录会员信息
        DiscountRuleBO discountRuleBO = calculateService.getDiscountRuleBO(orderDO, billCalculateReqDTO,
                orderDetailRespDTO);

        //每次计算优惠时存储当前最新的优惠规则
        List<DiscountDO> discountDOList = discountService.listByOrderGuid(billCalculateReqDTO.getOrderGuid());
        if (discountDOList == null) {
            discountDOList = new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(discountDOList)) {

            List<Long> guids = dynamicHelper.generateGuids(GuidKeyConstant.HST_DISCOUNT,
                    DiscountTypeEnum.values().length);
            //默认写入所有优惠方式
            discountDOList = AmountCalculationUtil.getInsertDiscountDOS(guids, discountDOList, discountRuleBO,
                    billCalculateReqDTO.getOrderGuid());
            updateDiscountDO(orderDO, billCalculateReqDTO, discountRuleBO, discountDOList);
            discountService.saveBatch(discountDOList);
        } else {
            //阿里云数据迁移到天翼云，解决缺少新增的优惠的问题
            if (discountDOList.size() < DiscountTypeEnum.values().length - 1) {
                log.info("进入阿里云特殊流程，discountDOS={}", discountDOList);
                int guidCount = DiscountTypeEnum.values().length - 1 - discountDOList.size();
                List<Long> guidLists = dynamicHelper.generateGuids(GuidKeyConstant.HST_DISCOUNT, guidCount);
                List<DiscountDO> needInsertToDicountDos =
                        AmountCalculationUtil.addNeedInsertDiscountDOS(new LinkedList<>(guidLists), discountDOList,
                                discountRuleBO, orderDetailRespDTO.getGuid());
                discountDOList.addAll(needInsertToDicountDos);
                updateDiscountDO(orderDO, billCalculateReqDTO, discountRuleBO, discountDOList);
                discountService.saveBatch(needInsertToDicountDos);
            }
            AmountCalculationUtil.getDiscountDOS(discountDOList, discountRuleBO);
            discountService.updateBatchById(discountDOList);
        }
        log.warn("优惠计算规则：{}", JacksonUtils.writeValueAsString(discountRuleBO));
    }

    private void updateDiscountDO(OrderDO orderDO, BillCalculateReqDTO billCalculateReqDTO,
                                  DiscountRuleBO discountRuleBO, List<DiscountDO> discountDOList) {
        //系统省零
        for (DiscountDO discountDO : discountDOList) {
            if (discountDO.getDiscountType() == DiscountTypeEnum.SYSTEM.getCode()) {
                discountDO.setDiscountFee(AmountCalculationUtil.getSystemDiscountFee(discountRuleBO.getSystemDiscountDTOS(), orderDO.getOrderFee()));
                break;
            }
        }
        //整单让价
        if (billCalculateReqDTO.getConcessional() != null) {
            for (DiscountDO discountDO : discountDOList) {
                if (discountDO.getDiscountType() == DiscountTypeEnum.CONCESSIONAL.getCode()) {
                    discountDO.setDiscountFee(billCalculateReqDTO.getConcessional());
                    break;
                }
            }
        }
    }

    private void savePlay(BatchCreateFastFoodReqDTO.FastFoodReqDTO foodReqDTO) {
        OrderDO orderDO = orderService.getById(foodReqDTO.getGuid());
        //保存附加费
        appendFeeService.persistAppendFee(foodReqDTO.getGuid());
        List<BillPayReqDTO.Payment> payments = foodReqDTO.getPayments();
        List<Long> guids = dynamicHelper.generateGuids(GuidKeyConstant.HST_TRANSACTION_RECORD, payments.size());
        List<TransactionRecordDO> transactionRecordDOList = new ArrayList<>();
        //快餐结账时校验估清，食堂快餐只能内网估清
        for (BillPayReqDTO.Payment payment : payments) {
            //会员需要传递全部的支付方式，但是已经在聚合支付发起的时候保存了记录，因此这里跳过。
            if (payment.getPaymentType() == PaymentTypeEnum.AGG.getCode()) {
                continue;
            }
            //订单交易记录
            TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
            Long guid = guids.remove(0);
            transactionRecordDO.setGuid(guid);
            transactionRecordDO.setOrderGuid(Long.valueOf(foodReqDTO.getGuid()));
            transactionRecordDO.setTerminalId(foodReqDTO.getDeviceId());
            transactionRecordDO.setAmount(payment.getAmount());
            transactionRecordDO.setPaymentType(payment.getPaymentType());
            transactionRecordDO.setBankTransactionId(payment.getBankTransactionId());
            //自定义支付
            if (payment.getPaymentType() == 10) {
                transactionRecordDO.setPaymentTypeName(payment.getPaymentTypeName());
            } else {
                transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.getDesc(payment.getPaymentType()));
            }
            //人脸支付字段
            if (payment.getPaymentType().equals(PaymentTypeEnum.FACE.getCode())) {
                if (StringUtils.isEmpty(payment.getBankTransactionId())) {
                    throw new ParameterException("人脸支付流水号不能为空");
                }
                transactionRecordDO.setFaceCode(payment.getFaceCode());
            }
            //银联支付
            if (payment.getPaymentType().equals(PaymentTypeEnum.CARD.getCode())) {
                transactionRecordDO.setFaceCode(payment.getFaceCode());
            }
            transactionRecordDO.setStaffGuid(foodReqDTO.getStaffGuid());
            transactionRecordDO.setStaffName(foodReqDTO.getStaffName());
            transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
            transactionRecordDO.setStoreGuid(foodReqDTO.getStoreGuid());
            transactionRecordDO.setStoreName(foodReqDTO.getStoreName());
            transactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
            //消费时间
            transactionRecordDO.setCreateTime(foodReqDTO.getConsumptionTime());
            transactionRecordDO.setBusinessDay(foodReqDTO.getBusinessDay());
            //食堂卡支付
            boolean isCanteenPay =
                    payment.getPaymentType().equals(PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode()) || payment.getPaymentType().equals(PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode());
            foodReqDTO.setCanteenPay(isCanteenPay);
            boolean memberPay =
                    CommonUtil.hasGuid(orderDO.getMemberGuid()) && payment.getPaymentType().equals(PaymentTypeEnum.MEMBER.getCode());
            if (isCanteenPay || memberPay) {
                BillPayReqDTO billPayReqDTO = orderTransform.batchCreateFastFoodReqDTO2BillPayReqDTO(foodReqDTO);
                //会员支付（含食堂卡） 离线订单上传先后顺序不同，余额扣减顺序也不同，保证最终金额一致
                String canteenConsumptionGuid = billService.saveMemberPay(orderDO, payment, billPayReqDTO);
                orderDO.setCanteenConsumptionGuid(canteenConsumptionGuid);
            }
            transactionRecordDOList.add(transactionRecordDO);
        }
        log.warn("会员支付安卓入参：{}，guid：{}，orderNo：{}", JacksonUtils.writeValueAsString(foodReqDTO), orderDO.getGuid(),
                orderDO.getOrderNo());
        //除了会员余额支付之外的和会员有关系的支付
        orderDO.setMemberConsumptionGuid(foodReqDTO.getMemberConsumptionGuid());

        transactionRecordService.saveBatch(transactionRecordDOList);
        orderService.updateById(orderDO);

        //支付成功之后更新优惠金额
        List<DiscountDO> discountDOList =
                orderTransform.discountFeeDetailDTOS2discountDOS(foodReqDTO.getDiscountFeeDetailDTOS());
        discountService.updateBatchById(discountDOList);

        //异步执行结账成功后的打印等操作
        DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(foodReqDTO.getGuid());
        //getOrderDetail引用的地方较多不宜改动，在下面重新设置附加费，调用查询订单下包含子单附加费的接口
        OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        orderFeeDetailDTO.setAppendFeeDetailDTOS(appendFeeService.getOrderAllAppendFeeList(foodReqDTO.getGuid()));
        orderDetail.setOrderFeeDetailDTO(orderFeeDetailDTO);
        log.info("结账最终打印订单信息:{}", JacksonUtils.writeValueAsString(orderDetail));
        //异步阶段 推送到赚餐
        asyncTradeService.asyncPushOrder(orderDO, UserContextUtils.get());
    }

    private void saveOrderItems(BatchCreateFastFoodReqDTO.FastFoodReqDTO foodReqDTO) {
        if (CollectionUtils.isEmpty(foodReqDTO.getDineInItemDTOS())) {
            throw new ParameterException("菜品信息不能为空");
        }
        //同一批次新增的菜品创建时间统一
        LocalDateTime createTime = foodReqDTO.getOrderTime();
        List<DineInItemDTO> itemDTOList = foodReqDTO.getDineInItemDTOS();
        //是否需要查询及for update
        OrderDO orderDO = orderService.getById(foodReqDTO.getGuid());
        boolean memberPhoneIsEmpty =
                StringUtils.isEmpty(orderDO.getMemberPhone()) || "0".equals(orderDO.getMemberPhone());
        if (memberPhoneIsEmpty && !StringUtils.isEmpty(foodReqDTO.getUserWxPublicOpenId())) {
            orderDO.setMemberPhone(foodReqDTO.getUserWxPublicOpenId());
        }

        //2.1 保存相关记录
        List<OrderItemRecordDO> itemRecordSave = new ArrayList<>();
        List<OrderItemDO> itemDOList = new ArrayList<>();
        List<ItemAttrDO> attrDOList = new ArrayList<>();
        List<FreeReturnItemDO> freeReturnItemDOList = new ArrayList<>();

        for (DineInItemDTO dineInItemDTO : itemDTOList) {

            if (dineInItemDTO.getPriceChangeType() != null && dineInItemDTO.getPriceChangeType() == 2) {
                dineInItemDTO.setPrice(dineInItemDTO.getOriginalPrice());
            }
            //安卓兼容以前字段
            if (dineInItemDTO.getPriceChangeType() == null || dineInItemDTO.getOriginalPrice() == null) {
                dineInItemDTO.setOriginalPrice(dineInItemDTO.getPrice());
            }
            OrderItemDO orderItemDO = orderTransform.dineInItemDTO2OrderItemDO(dineInItemDTO);
            Long orderItemGuid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
            orderItemDO.setGmtCreate(createTime);
            if (StringUtils.isNotEmpty(foodReqDTO.getUserWxPublicOpenId())) {
                orderItemDO.setUserWxPublicOpenId(foodReqDTO.getUserWxPublicOpenId());
            }
            orderItemDO.setGuid(orderItemGuid);
            orderItemDO.setOrderGuid(orderDO.getGuid());
            orderItemDO.setIsPay(0);
            orderItemDO.setFreeCount(BigDecimal.ZERO);
            orderItemDO.setReturnCount(BigDecimal.ZERO);
            orderItemDO.setCreateStaffName(foodReqDTO.getStaffName());
            orderItemDO.setCreateStaffGuid(foodReqDTO.getStaffGuid());
            itemDOList.add(orderItemDO);

            OrderItemRecordDO orderItemRecordDO = orderTransform.orderItemDO2OrderItemRecordDO(orderItemDO);
            Long recordGuid = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
            orderItemRecordDO.setGuid(recordGuid);
            orderItemRecordDO.setItemType(dineInItemDTO.getItemType());
            orderItemRecordDO.setSkuGuid(dineInItemDTO.getSkuGuid());
            orderItemRecordDO.setSkuName(dineInItemDTO.getSkuName());
            orderItemRecordDO.setType(1);
            orderItemRecordDO.setLog("【点菜】:点菜");
            orderItemRecordDO.setCreateStaffGuid(foodReqDTO.getStaffGuid());
            orderItemRecordDO.setCreateStaffName(foodReqDTO.getStaffName());

            if (BigDecimalUtil.greaterThanZero(orderItemRecordDO.getCurrentCount())) {
                itemRecordSave.add(orderItemRecordDO);
            }

            dineInItemDTO.setGuid(String.valueOf(orderItemGuid));
            //
            BigDecimal itemPrice = dineInItemDTO.getCurrentCount().multiply(dineInItemDTO.getPrice());
            orderDO.setOrderFee(orderDO.getOrderFee().add(itemPrice));


            //处理未下单赠送
            List<FreeItemDTO> freeItemDTOList = dineInItemDTO.getFreeItemDTOS();
            if (!CollectionUtils.isEmpty(freeItemDTOList)) {
                //记录未下单赠送数量
                BigDecimal freeSum = BigDecimal.ZERO;
                OrderItemRecordDO recordFreeDO = new OrderItemRecordDO();

                for (FreeItemDTO freeItemDTO : freeItemDTOList) {
                    FreeReturnItemDO freeReturnItemDO = new FreeReturnItemDO();
                    freeReturnItemDO.setGmtCreate(createTime);
                    freeReturnItemDO.setStaffName(foodReqDTO.getUserName());
                    freeReturnItemDO.setStaffGuid(foodReqDTO.getUserGuid());
                    freeReturnItemDO.setGuid(dynamicHelper.generateGuid(HST_ORDER_ITEM));
                    freeReturnItemDO.setOrderItemGuid(orderItemDO.getGuid());
                    freeReturnItemDO.setOrderGuid(orderDO.getGuid());
                    freeReturnItemDO.setCount(freeItemDTO.getCount());
                    freeSum = freeSum.add(freeItemDTO.getCount());
                    freeReturnItemDO.setReason(freeItemDTO.getReason());
                    freeReturnItemDO.setStoreGuid(foodReqDTO.getStoreGuid());
                    freeReturnItemDO.setStoreName(foodReqDTO.getStoreName());
                    freeReturnItemDO.setType(FreeReturnTypeEnum.FREE.getCode());
                    freeReturnItemDO.setItemGuid(orderItemDO.getItemGuid());
                    freeReturnItemDO.setItemName(orderItemDO.getItemName());
                    freeReturnItemDO.setPrice(orderItemDO.getOriginalPrice());
                    //未下单赠送不可能是已划
                    freeReturnItemDO.setServeCount(BigDecimal.ZERO);
                    freeReturnItemDO.setItemState(freeItemDTO.getItemState());
                    orderItemDO.setFreeCount(orderItemDO.getFreeCount().add(freeItemDTO.getCount()));
                    BigDecimal freeItemPrice = freeItemDTO.getCount().multiply(dineInItemDTO.getOriginalPrice());
                    orderDO.setOrderFee(orderDO.getOrderFee().add(freeItemPrice));
                    freeReturnItemDOList.add(freeReturnItemDO);
                    recordFreeDO = orderTransform.freeReturnItemDO2OrderItemRecordDO(freeReturnItemDO);
                    Long recordGuid2 = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
                    recordFreeDO.setGuid(recordGuid2);
                    recordFreeDO.setOrderItemGuid(String.valueOf(orderItemDO.getGuid()));
                }
                if (orderDO.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
                    recordFreeDO.setIsFromRecovery(1);
                }
                recordFreeDO.setFreeCount(freeSum);
                recordFreeDO.setItemType(dineInItemDTO.getItemType());
                recordFreeDO.setType(2);
                recordFreeDO.setSkuGuid(dineInItemDTO.getSkuGuid());
                recordFreeDO.setSkuName(dineInItemDTO.getSkuName());
                recordFreeDO.setItemTypeName(dineInItemDTO.getItemTypeName());
                recordFreeDO.setItemTypeGuid(dineInItemDTO.getItemTypeGuid());
                recordFreeDO.setUnit(dineInItemDTO.getUnit());
                recordFreeDO.setLog("【赠菜】:未下单赠送");
                recordFreeDO.setCreateStaffGuid(foodReqDTO.getUserGuid());
                recordFreeDO.setCreateStaffName(foodReqDTO.getUserName());

                String jsonStr = JacksonUtils.writeValueAsString(recordFreeDO);
                OrderItemRecordDO recordCurrentDO = JacksonUtils.toObject(OrderItemRecordDO.class, jsonStr);
                Long recordGuid3 = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
                recordCurrentDO.setGuid(recordGuid3);
                recordCurrentDO.setItemTypeGuid(dineInItemDTO.getItemTypeGuid());
                recordCurrentDO.setItemTypeName(dineInItemDTO.getItemTypeName());
                recordCurrentDO.setSkuGuid(dineInItemDTO.getSkuGuid());
                recordCurrentDO.setSkuName(dineInItemDTO.getSkuName());
                recordCurrentDO.setUnit(dineInItemDTO.getUnit());
                recordCurrentDO.setCurrentCount(freeSum);
                recordCurrentDO.setItemType(dineInItemDTO.getItemType());
                recordCurrentDO.setType(1);
                recordCurrentDO.setOrderItemGuid(String.valueOf(orderItemDO.getGuid()));
                recordCurrentDO.setLog("【点菜】:未下单赠送加入点菜");

                itemRecordSave.add(recordFreeDO);
                itemRecordSave.add(recordCurrentDO);
            }

            BigDecimal attrTotalPrice = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(dineInItemDTO.getItemAttrDTOS())) {
                for (int j = 0; j < dineInItemDTO.getItemAttrDTOS().size(); j++) {
                    ItemAttrDTO itemAttrDTO = dineInItemDTO.getItemAttrDTOS().get(j);
                    ItemAttrDO itemAttrDO = orderTransform.itemAttrDTO2ItemAttrDO(itemAttrDTO);
                    Long itemAttrGuid = dynamicHelper.generateGuid(ITEM_ATTR_GUID);
                    itemAttrDO.setOrderItemGuid(orderItemGuid);
                    itemAttrDO.setGmtCreate(createTime);
                    itemAttrDO.setGuid(itemAttrGuid);
                    itemAttrDO.setOrderGuid(orderDO.getGuid());
                    itemAttrDO.setStoreGuid(foodReqDTO.getStoreGuid());
                    itemAttrDO.setStoreName(foodReqDTO.getStoreName());
                    attrDOList.add(itemAttrDO);
                    itemAttrDTO.setGuid(String.valueOf(itemAttrGuid));
                    //暂时没有属性数量
                    BigDecimal attrPrice;
                    if (dineInItemDTO.getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
                        attrPrice = itemAttrDTO.getAttrPrice().multiply(BigDecimal.ONE);
                    } else {
                        attrPrice =
                                itemAttrDTO.getAttrPrice().multiply(dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount() == null ? BigDecimal.ZERO : dineInItemDTO.getFreeCount()));
                    }

                    attrTotalPrice = attrTotalPrice.add(attrPrice);
                }
                orderItemDO.setHasAttr(1);
            }
            //菜品为套餐时
            if (ItemUtil.isGroup(dineInItemDTO.getItemType())) {

                List<PackageSubgroupDTO> packageSubgroupDTOList = dineInItemDTO.getPackageSubgroupDTOS();
                for (PackageSubgroupDTO packageSubgroupDTO : packageSubgroupDTOList) {
                    List<SubDineInItemDTO> subDineInItemDTOList = packageSubgroupDTO.getSubDineInItemDTOS();
                    for (SubDineInItemDTO subDineInItemDTO : subDineInItemDTOList) {
                        if (StringUtils.isEmpty(subDineInItemDTO.getItemTypeGuid())) {
                            throw new ParameterException("菜品分类不能为空");
                        }
                        OrderItemDO subOrderItemDO = orderTransform.subDineInItemDTO2OrderItemDO(subDineInItemDTO);
                        Long subOrderItemGuid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
                        subOrderItemDO.setGuid(subOrderItemGuid);
                        subOrderItemDO.setIsPay(0);
                        subOrderItemDO.setGmtCreate(createTime);
                        if (StringUtils.isNotEmpty(foodReqDTO.getUserWxPublicOpenId())) {
                            subOrderItemDO.setUserWxPublicOpenId(foodReqDTO.getUserWxPublicOpenId());
                        }
                        subOrderItemDO.setOrderGuid(orderDO.getGuid());
                        subOrderItemDO.setCurrentCount(subDineInItemDTO.getCurrentCount());
                        subOrderItemDO.setFreeCount(BigDecimal.ZERO);
                        subOrderItemDO.setReturnCount(BigDecimal.ZERO);
                        subOrderItemDO.setPackageDefaultCount(subDineInItemDTO.getPackageDefaultCount());
                        subOrderItemDO.setParentItemGuid(Long.valueOf(dineInItemDTO.getGuid()));
                        subOrderItemDO.setSubgroupGuid(packageSubgroupDTO.getSubgroupGuid());
                        subOrderItemDO.setSubgroupName(packageSubgroupDTO.getSubgroupName());
                        subOrderItemDO.setCreateStaffGuid(foodReqDTO.getUserGuid());
                        subOrderItemDO.setCreateStaffName(foodReqDTO.getUserName());
                        if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getAddPrice())) {
                            subOrderItemDO.setAddPrice(subDineInItemDTO.getAddPrice());
                            BigDecimal subAttrNum =
                                    (dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount() == null ?
                                            BigDecimal.ZERO : dineInItemDTO.getFreeCount())).multiply(subDineInItemDTO.getCurrentCount());
                            orderDO.setOrderFee(orderDO.getOrderFee().add(subDineInItemDTO.getAddPrice().multiply(subAttrNum)));
                        }

                        subDineInItemDTO.setGuid(String.valueOf(subOrderItemGuid));
                        itemDOList.add(subOrderItemDO);

                        OrderItemRecordDO itemRecordDO = orderTransform.orderItemDO2OrderItemRecordDO(subOrderItemDO);
                        Long recordGuid4 = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
                        itemRecordDO.setGuid(recordGuid4);
                        itemRecordDO.setType(1);
                        itemRecordDO.setSkuName(dineInItemDTO.getSkuName());
                        itemRecordDO.setSkuGuid(dineInItemDTO.getSkuGuid());
                        itemRecordDO.setItemType(dineInItemDTO.getItemType());
                        itemRecordDO.setItemTypeGuid(dineInItemDTO.getItemTypeGuid());
                        itemRecordDO.setItemTypeName(dineInItemDTO.getItemTypeName());
                        itemRecordDO.setUnit(dineInItemDTO.getUnit());
                        itemRecordDO.setLog("【点菜】:子菜");
                        if (orderDO.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
                            itemRecordDO.setIsFromRecovery(1);
                        }
                        itemRecordSave.add(itemRecordDO);

                        BigDecimal subAttrTotalPrice = BigDecimal.ZERO;
                        if (!CollectionUtils.isEmpty(subDineInItemDTO.getItemAttrDTOS())) {
                            for (int l = 0; l < subDineInItemDTO.getItemAttrDTOS().size(); l++) {
                                ItemAttrDTO itemAttrDTO = subDineInItemDTO.getItemAttrDTOS().get(l);
                                ItemAttrDO itemAttrDO = orderTransform.itemAttrDTO2ItemAttrDO(itemAttrDTO);
                                Long itemAttrGuid = dynamicHelper.generateGuid(ITEM_ATTR_GUID);
                                itemAttrDO.setOrderItemGuid(subOrderItemGuid);
                                itemAttrDO.setGmtCreate(createTime);
                                itemAttrDO.setGuid(itemAttrGuid);
                                itemAttrDO.setOrderGuid(orderDO.getGuid());
                                itemAttrDO.setStoreGuid(foodReqDTO.getStoreGuid());
                                itemAttrDO.setStoreName(foodReqDTO.getStoreName());
                                itemAttrDTO.setGuid(String.valueOf(itemAttrGuid));
                                attrDOList.add(itemAttrDO);
                                //暂时没有属性数量
                                BigDecimal subAttrNum =
                                        (dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount() == null ?
                                                BigDecimal.ZERO : dineInItemDTO.getFreeCount())).multiply(subDineInItemDTO.getCurrentCount());
                                if (!subDineInItemDTO.getItemType().equals(ItemTypeEnum.WEIGH.getCode()) && BigDecimalUtil.greaterThanZero(subDineInItemDTO.getPackageDefaultCount())) {
                                    subAttrNum = subAttrNum.multiply(subDineInItemDTO.getPackageDefaultCount());
                                }
                                BigDecimal attrPrice = itemAttrDTO.getAttrPrice().multiply(subAttrNum);
                                subAttrTotalPrice = subAttrTotalPrice.add(attrPrice);
                                subOrderItemDO.setHasAttr(1);
                            }

                            //属性总价
                            attrTotalPrice = attrTotalPrice.add(subAttrTotalPrice);
                        }
                    }

                }
            }
            orderDO.setOrderFee(orderDO.getOrderFee().add(attrTotalPrice));
        }

        orderDO.setOrderFeeForCombine(orderDO.getOrderFee());
        orderService.updateByIdWithDeleteCache(orderDO);
        orderItemService.saveBatchWithDeleteCache(itemDOList, String.valueOf(orderDO.getGuid()));

        if (!ObjectUtils.isEmpty(itemRecordSave)) {
            //保存到菜品流水表中
            orderItemRecordService.saveBatchWithDeleteCache(itemRecordSave);
        }

        if (!CollectionUtils.isEmpty(attrDOList)) {
            itemAttrService.saveBatch(attrDOList);
        }

        if (!CollectionUtils.isEmpty(freeReturnItemDOList)) {
            freeReturnItemService.saveBatch(freeReturnItemDOList);
        }
    }

    private boolean saveOrder(BatchCreateFastFoodReqDTO.FastFoodReqDTO foodReqDTO) {
        if (StringUtils.isEmpty(foodReqDTO.getGuid())) {
            //防止重复导入判断订单号是否有重复
            final List<OrderDO> orderDOList = orderService.listByOrderNoAndCreate(foodReqDTO.getOrderNo(),
                    foodReqDTO.getOrderTime());
            if (!CollectionUtils.isEmpty(orderDOList)) {
                OrderDO orderDO = orderDOList.get(0);
                foodReqDTO.setGuid(String.valueOf(orderDO.getGuid()));
                //支付记录
                final List<TransactionRecordDO> recordDOList =
                        transactionRecordService.listByOrderGuid(Long.parseLong(foodReqDTO.getGuid()));
                if (!CollectionUtils.isEmpty(recordDOList)) {
                    //订单数据已存在！
                    return false;
                }
                //更新
                fastFoodService.updateFastFoodOrder(foodReqDTO);
                //删除之前的订单商品
                fastFoodService.deleteItems(foodReqDTO.getGuid());
                return true;
            }
            OrderDO orderDO = new OrderDO();
            //订单创建时间
            orderDO.setGmtCreate(foodReqDTO.getOrderTime());
            orderDO.setRemark(foodReqDTO.getRemark());
            orderDO.setDiningTableName(foodReqDTO.getWeixinTableCode());
            orderDO.setUserWxPublicOpenId(foodReqDTO.getUserWxPublicOpenId());
            //快餐牌号前端传递
            orderDO.setMark(foodReqDTO.getMark());
            if (foodReqDTO.getDeviceType() == null) {
                throw new ParameterException("设备类型不能为空");
            }
            orderDO.setDeviceType(foodReqDTO.getDeviceType());
            Long orderGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER);
            orderDO.setGuid(orderGuid);
            foodReqDTO.setGuid(String.valueOf(orderGuid));
            orderDO.setBusinessDay(foodReqDTO.getBusinessDay());
            //订单编号前端传递，为了区分订单号，StoreNo + YYYYMMDD + HHMMSS
            orderDO.setOrderNo(foodReqDTO.getOrderNo());
            orderDO.setGuestCount(foodReqDTO.getGuestCount());
            //门店也在订单中
            orderDO.setStoreGuid(foodReqDTO.getStoreGuid());
            orderDO.setStoreName(foodReqDTO.getStoreName());
            orderDO.setCreateStaffGuid(foodReqDTO.getStaffGuid());
            orderDO.setCreateStaffName(foodReqDTO.getStaffName());
            orderDO.setState(foodReqDTO.getState());
            orderDO.setTradeMode(TradeModeEnum.FAST.getCode());
            foodReqDTO.setFastFood(true);
            orderDO.setRecoveryType(RecoveryTypeEnum.GENERAL.getCode());
            orderDO.setPrintPreBillNum(0);
            orderDO.setOrderFee(BigDecimal.ZERO);
            orderDO.setCheckoutTime(foodReqDTO.getConsumptionTime());
            orderDO.setCheckoutStaffGuid(foodReqDTO.getStaffGuid());
            orderDO.setCheckoutStaffName(foodReqDTO.getStaffName());
            BigDecimal actuallyPayFee = foodReqDTO.getActuallyPayFee();
            //找零金额不为空 实收金额需减去找零金额
            if (null != foodReqDTO.getChangeFee()) {
                actuallyPayFee = actuallyPayFee.subtract(foodReqDTO.getChangeFee());
                orderDO.setChangeFee(foodReqDTO.getChangeFee());
            }
            orderDO.setActuallyPayFee(actuallyPayFee);
            orderService.save(orderDO);
        } else {
            //支付记录
            final List<TransactionRecordDO> recordDOList =
                    transactionRecordService.listByOrderGuid(Long.parseLong(foodReqDTO.getGuid()));
            if (!CollectionUtils.isEmpty(recordDOList)) {
                //订单数据已存在！
                return false;
            }
//            OrderDO orderDO = orderService.getById(foodReqDTO.getGuid());
            fastFoodService.updateFastFoodOrder(foodReqDTO);
            //删除之前的订单商品
            fastFoodService.deleteItems(foodReqDTO.getGuid());
        }
        return true;
    }


}
