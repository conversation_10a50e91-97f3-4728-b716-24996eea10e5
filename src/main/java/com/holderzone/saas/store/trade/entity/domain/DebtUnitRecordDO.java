package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.saas.store.enums.DebtPaymentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> R
 * @date 2020/12/15 15:36
 * @description 挂账单位流水表
 */
@TableName(value = "hst_debt_unit_record")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Data
public class DebtUnitRecordDO {
    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;
    /**
     * 是否删除 0：false,1:true
     */
    private Boolean isDelete;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
    /**
     * 整单备注
     */
    private String remark;
    /**
     * 门店guid
     */
    private String storeGuid;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 订单guid
     */
    private Long orderGuid;
    /**
     * 单位Guid
     */
    private String unitGuid;
    /**
     * 单位名称
     */
    private String unitName;
    /**
     * 单位编码
     */
    private String unitCode;
    /**
     * 还款票据编号
     */
    private String debtInvoiceCode;
    /**
     * 挂账金额
     */
    private BigDecimal debtFee;
    /**
     * 结算方式
     *
     * @see DebtPaymentTypeEnum
     */
    private Integer paymentType;
    /**
     * 还款金额
     */
    private BigDecimal repaymentFee;
    /**
     * 是否还款（默认0，未还款，1已还款）
     */
    private Integer repaymentStatus;

    /**
     * 创建操作人guid
     */
    private String createStaffGuid;

    /**
     * 创建操作人name
     */
    private String createStaffName;

    /**
     * 还款操作人guid
     */
    private String updateStaffGuid;

    /**
     * 还款操作人name
     */
    private String updateStaffName;

    /**
     * 是否余出
     */
    private Integer excessFlag;

    /**
     * 凭证
     */
    private String evidences;

    /**
     * 还款批次号
     */
    private String repaymentBatchNumber;

    /**
     * 单位联系人
     */
    @TableField(exist = false)
    private String unitContactName;

    /**
     * 单位联系电话
     */
    @TableField(exist = false)
    private String unitContactTel;

    /**
     * 会员GUID
     */
    private String memberInfoGuid;

    /**
     * 消费记录GUID
     */
    private String memberConsumptionGuid;

    /**
     * 会员卡guid
     */
    private String memberCardGuid;

    /**
     * 会员电话
     */
    @TableField(insertStrategy = FieldStrategy.NOT_NULL, updateStrategy = FieldStrategy.NOT_NULL)
    private String memberPhone;

    /**
     * 会员名字
     */
    @TableField(insertStrategy = FieldStrategy.NOT_NULL, updateStrategy = FieldStrategy.NOT_NULL)
    private String memberName;
}
