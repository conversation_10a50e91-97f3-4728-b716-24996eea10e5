package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.journaling.req.JournalAppBaseReqDTO;
import com.holderzone.saas.store.dto.journaling.req.SaleStatisticsByHoursReqDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreStatisticsAppReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreGatherReportRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreSaleItemDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreStatisticsAppRespDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.reserve.UpdateOrderReserveReqDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsCombinedRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsQueryDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsRespDTO;
import com.holderzone.saas.store.dto.trade.HandoverOrderOptimizationDTO;
import com.holderzone.saas.store.dto.trade.OrderInfoRespDTO;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderMemberInfoReqDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.trade.anno.PerformanceCheck;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.dto.BusinessHisTrendDTO;
import com.holderzone.saas.store.trade.entity.query.OrderWaiterQuery;
import com.holderzone.saas.store.trade.entity.query.OrderWaiterQueryDetails;
import com.holderzone.saas.store.trade.entity.read.OrderReadDO;
import com.holderzone.saas.store.trade.entity.read.OrderReadDetailsDO;
import com.holderzone.saas.store.trade.helper.PageAdapter;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */

public interface OrderMapper extends BaseMapper<OrderDO> {

    Integer handoverOrderCount(@Param("dto") HandoverPayQueryDTO request);

    @Select("select sum(guest_count) from hst_order where guid = #{guid} or main_order_guid = #{guid}")
    Integer getTotalGuestCount(String guid);

    @PerformanceCheck
    List<OrderDO> selectByGuidOrMainOrderGuid(String guid);

    IPage<OrderInfoRespDTO> pageOrderInfo(IPage<OrderInfoRespDTO> page, @Param("query") DineInOrderListReqDTO query);

    @PerformanceCheck
    int updateOrderFeeAndAppendFeeByGuid(Long guid, BigDecimal orderFee, BigDecimal appendFee);

    /***
     * @return
     */
    @Select("SELECT * FROM hst_order WHERE (is_handle IS NULL OR is_handle = -1)  AND recovery_type IN (1,3) AND " +
            "state IN (1,4,6) AND is_delete = 0 ORDER BY gmt_create DESC LIMIT 0,1")
    OrderDO findByEnterpriseGuid();

    @Update("UPDATE hst_order SET is_handle = #{isHandle},is_updated_es = 0 WHERE guid = #{guid}")
    int updateOrderIsHandleByOrderGuid(Long guid, Integer isHandle);

    void updateSameOrderFeeForCombine(@Param("guid") Long guid);

    BusinessDataRespDTO listOrderBusinessData(JournalAppBaseReqDTO reqDTO);

    List<BusinessHisTrendDTO> listOrderBusinessHisTrend(JournalAppBaseReqDTO reqDTO);

    IPage<StoreGatherReportRespDTO> listStoreGatherBusiness(IPage<StoreGatherReportRespDTO> iPage, @Param("req") StoreGatherReportReqDTO storeGatherReportReq);

    /**
     * 统计商户后台/数据报表/订单统计 数据条数
     *
     * @param queryDTO 查询条件
     * @return 统计结果
     */
    Integer getOrderStatisticsCount(BusinessOrderStatisticsQueryDTO queryDTO);

    /**
     * 获取商户后台/数据报表/订单统计 分页数据  考虑导出可能会使用这个方法 固没有返回OrderDO 直接返回DTO
     *
     * @param queryDTO 　查询条件
     * @return 分页数据
     */
    List<BusinessOrderStatisticsRespDTO> getOrderStatisticsPage(BusinessOrderStatisticsQueryDTO queryDTO);


    IPage<OrderReadDO> pageQueryOrder(IPage<OrderReadDO> iPage,
                                      @Param("orderWaiterQuery") OrderWaiterQuery orderWaiterQuery);

    IPage<OrderReadDO> pageQueryOrderWaiter(IPage<OrderReadDO> iPage,
                                            @Param("orderWaiterQuery") OrderWaiterQuery orderWaiterQuery);

    IPage<OrderReadDetailsDO> pageQueryOrderWaiterDetails(IPage<OrderReadDetailsDO> iPage, @Param(
            "orderWaiterQueryDetails") OrderWaiterQueryDetails orderWaiterQueryDetails);

    /**
     * 查询商户后台/数据报表/订单统计 合计
     *
     * @param queryDTO 查询条件入参
     * @return 统计结果
     */
    BusinessOrderStatisticsCombinedRespDTO getOrderStatisticsCombined(BusinessOrderStatisticsQueryDTO queryDTO);

    /**
     * 查询商户后台/数据报表/订单统计 团购验券实付金额统计
     */
    BigDecimal getGrouponOrderStatisticsCombined(BusinessOrderStatisticsQueryDTO queryDTO);

    /***
     * 分页查询订单操作人
     * @param iPage
     * @param orderWaiterQueryDetails
     * @return
     */
    IPage<OrderReadDO> pageOrderWaiterMakeUp(IPage<OrderReadDO> iPage,
                                             @Param("orderWaiterQueryDetails") OrderWaiterQueryDetails orderWaiterQueryDetails);

    /***
     * 根据订单guid更新订单是否录入操作员
     * @param orderGuidList 集合
     * @return
     */
    int updateIsWaiters(@Param("orderGuidList") List<String> orderGuidList);

    /**
     * 统计门店汇报
     *
     * @param storeGatherReportReqDTO 参数
     * @return
     */
    IPage<StoreGatherReportRespDTO> storeGatherTotalListByExport(IPage<StoreGatherReportRespDTO> iPage,
                                                                 @Param("reportReqDTO") StoreGatherReportReqDTO storeGatherReportReqDTO);

    /**
     * 统计门店汇报
     *
     * @param storeGatherReportReqDTO 参数
     * @return
     */
    IPage<StoreGatherReportRespDTO> storeGatherTotalList(IPage<StoreGatherReportRespDTO> iPage,
                                                         @Param("reportReqDTO") StoreGatherReportReqDTO storeGatherReportReqDTO);


    void updateAdjustStateIsTrue(@Param("guid") Long guid);

    void updateOrderIsUpdatedEs(@Param("guid") Long guid);

    LocalDateTime queryfristorderForStoreGuid(@Param("storeGuid") String storeGuid);

    List<StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO> saleStoreStatistics(@Param("query") StoreStatisticsAppReqDTO storeStatisticsAppReqDTO);

    List<StoreSaleItemDTO> saleByHoursStatistics(@Param("query") SaleStatisticsByHoursReqDTO statisticsByHoursReqDTO);

    List<UserBriefDTO> getCheckoutStaffs(BusinessOrderStatisticsQueryDTO businessOrderStatisticsQueryDTO);

    void updateOrderReserve(@Param("query") UpdateOrderReserveReqDTO reqDTO);

    void updateOrderMemberInfo(@Param("query") UpdateOrderMemberInfoReqDTO reqDTO);

    Integer retailHandoverOrderCount(@Param("dto")HandoverPayQueryDTO handoverPayQueryDTO);

    List<String> listPaymentTypeName();

    IPage<DineInOrderListRespDTO> orderPage(PageAdapter<Object> objectPageAdapter,
                                            @Param("query") DineInOrderListReqDTO dineInOrderListReqDTO);

    /**
     * 获取交接班已结账订单详情
     *
     * @param request
     * @return Integer
     */
    List<HandoverOrderOptimizationDTO> handoverOrderOptimization(@Param("dto") HandoverPayQueryDTO request);
}
