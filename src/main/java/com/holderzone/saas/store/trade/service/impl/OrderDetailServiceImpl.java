package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.aimilin.bean.ExcelResult;
import com.aimilin.bean.ExcelType;
import com.aimilin.utils.BeanUtils;
import com.aimilin.utils.ExcelUtils;
import com.aimilin.utils.ExcelWriteUtils;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreGatherReportRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreGatherTotalRespDTO;
import com.holderzone.saas.store.dto.order.OrderUploadDTO;
import com.holderzone.saas.store.dto.order.OrderUploadErrExlDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.local.FreeReturnItemDTO;
import com.holderzone.saas.store.dto.order.request.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.response.OrderUploadRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.RefundOrderDetailDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.OrderStateEnum;
import com.holderzone.saas.store.enums.trade.RecoveryTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.*;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.PageAdapter;
import com.holderzone.saas.store.trade.mapper.OrderExtendsMapper;
import com.holderzone.saas.store.trade.mapper.OrderMapper;
import com.holderzone.saas.store.trade.mapper.OrderRefreshMapper;
import com.holderzone.saas.store.trade.repository.feign.FileUploadClient;
import com.holderzone.saas.store.trade.repository.feign.StoreClientService;
import com.holderzone.saas.store.trade.repository.feign.TableClientService;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.AppendFeeService;
import com.holderzone.saas.store.trade.service.DineInService;
import com.holderzone.saas.store.trade.service.OrderDetailService;
import com.holderzone.saas.store.trade.transform.LocalTransform;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.transform.TransactionRecordTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.CommonUtil;
import com.holderzone.saas.store.trade.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.time.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Yu Ren
 * @date 2020/6/1 11:37
 * @description
 */
@Service
@Slf4j
public class OrderDetailServiceImpl implements OrderDetailService {

    @Resource
    private OrderService orderService;

    @Resource
    private OrderItemService orderItemService;

    @Resource
    private StoreClientService storeClientService;

    @Resource
    private ItemAttrService itemAttrService;

    @Resource
    private AppendFeeMpService appendFeeMpService;

    @Resource
    private OrderExtendsMapper orderExtendsMapper;

    @Resource
    private TransactionRecordService transactionRecordService;

    @Resource
    private FreeReturnItemService freeReturnItemService;

    @Resource
    private DiscountService discountService;

    @Resource
    private OrderItemRecordService orderItemRecordService;
    @Resource
    private OrderRefreshMapper orderRefreshMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private DynamicHelper dynamicHelper;
    @Resource
    private AppendFeeService appendFeeService;
    @Resource
    private TableClientService tableClientService;
    @Resource
    private FileUploadClient fileUploadClient;
    @Resource
    private Executor bossOrderExecutor;
    @Resource
    private Executor appletOrderExecutor;

    @Resource
    private OrderMultiMemberService orderMultiMemberService;

    private static final OrderTransform orderTransform = OrderTransform.INSTANCE;

    private static final LocalTransform localTransform = LocalTransform.INSTANCE;

    @Override
    public Page<OrderInfoRespDTO> pageOrderInfo(DineInOrderListReqDTO reqDTO) {
        // 校验查询参数
        checkQueryParams(reqDTO);
        PageAdapter<OrderInfoRespDTO> pageAdapter = new PageAdapter<>(reqDTO);
        IPage<OrderInfoRespDTO> page = orderService.pageOrderInfo(pageAdapter, reqDTO);
        List<OrderInfoRespDTO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        }
        List<String> orderGuidList = records.stream().map(OrderInfoRespDTO::getGuid).distinct().collect(Collectors.toList());
        UserContext userInfoDTO = UserContextUtils.get();
        // 查询订单上的/附加费明细/退款信息列表
        CompletableFuture<OrderInfoBatchDTO> orderExtendInfoFuture = CompletableFuture
                .supplyAsync(() ->
                        queryOrderInfoBatchDTO(reqDTO, orderGuidList, userInfoDTO), bossOrderExecutor)
                .exceptionally(throwable -> {
                    log.error("查询订单上的优惠信息/附加费明细/退款信息列表失败", throwable);
                    return null;
                });
        CompletableFuture<Void> all = CompletableFuture.allOf(orderExtendInfoFuture);
        try {
            all.get();
            // 返回订单处理
            buildOrderInfoRespDTO(records, orderExtendInfoFuture.get());
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal(), records);
        } catch (Exception e) {
            log.error("订单列表查询失败", e);
            Thread.currentThread().interrupt();
            throw new BusinessException("系统繁忙稍后再试");
        }
    }

    @Override
    public Page<OrderInfoRespDTO> pageWxOrderInfo(DineInOrderListReqDTO reqDTO) {
        reqDTO.setRecoveryTypes(Lists.newArrayList(RecoveryTypeEnum.ORDINARY.getCode(), RecoveryTypeEnum.ORIGINAL.getCode()));
        log.info("小程序查询订单入参:{}", JacksonUtils.writeValueAsString(reqDTO));
        PageAdapter<OrderInfoRespDTO> pageAdapter = new PageAdapter<>(reqDTO);
        IPage<OrderInfoRespDTO> page = orderService.pageOrderInfo(pageAdapter, reqDTO);
        List<OrderInfoRespDTO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        }
        List<String> orderGuidList = records.stream().map(OrderInfoRespDTO::getGuid).distinct().collect(Collectors.toList());
        UserContext userInfoDTO = UserContextUtils.get();
        // 查询商品明细
        CompletableFuture<List<DineInItemDTO>> orderItemInfoFuture = CompletableFuture
                .supplyAsync(() ->
                        queryOrderItemDTOList(orderGuidList, userInfoDTO), appletOrderExecutor)
                .exceptionally(throwable -> {
                    log.error("查询商品明细列表失败", throwable);
                    return null;
                });
        // 查询订单上的优惠信息
        CompletableFuture<OrderInfoBatchDTO> orderExtendInfoFuture = CompletableFuture
                .supplyAsync(() ->
                        queryOrderDiscountInfo(orderGuidList, userInfoDTO), appletOrderExecutor)
                .exceptionally(throwable -> {
                    log.error("查询订单上的优惠信息失败", throwable);
                    return null;
                });
        CompletableFuture<Void> all = CompletableFuture.allOf(orderItemInfoFuture, orderExtendInfoFuture);
        try {
            all.get();
            // 返回订单处理
            buildWxOrderInfoRespDTO(records, orderItemInfoFuture.get(), orderExtendInfoFuture.get());
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal(), records);
        } catch (Exception e) {
            log.error("订单列表查询失败", e);
            Thread.currentThread().interrupt();
            throw new BusinessException("系统繁忙稍后再试");
        }
    }

    /**
     * 校验查询参数
     */
    private void checkQueryParams(DineInOrderListReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getStates())) {
            throw new BusinessException("订单状态不能为空");
        }
    }


    /**
     * 查询订单上的商品信息列表
     */
    private List<DineInItemDTO> queryOrderItemDTOList(List<String> orderGuidList, UserContext userInfoDTO) {
        EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
        UserContextUtils.put(userInfoDTO);
        // 批量查询商品明细列表
        List<OrderItemDO> orderItemList = orderItemService.listByOrderLists(orderGuidList);
        if (CollectionUtils.isEmpty(orderItemList)) {
            log.error("商品明细丢失, orderGuidList：{}", JacksonUtils.writeValueAsString(orderGuidList));
            return Lists.newArrayList();
        }
        // originalPrice设置默认值
        orderItemList.stream()
                .filter(a -> a.getPriceChangeType() == 0 && a.getOriginalPrice() == null)
                .forEach(a -> a.setOriginalPrice(a.getPrice()));
        Map<Long, List<OrderItemDO>> itemListMap = CollectionUtil.toListMap(orderItemList, "guid");
        // 查询商品属性
        List<ItemAttrDO> itemAttrDOList = new ArrayList<>(itemAttrService.listByItemGuids(new ArrayList<>(itemListMap.keySet())));
        // 赠送菜品
        List<FreeReturnItemDO> freeReturnItemList = null;
        if (AmountCalculationUtil.hasFree(orderItemList)) {
            Map<Long, OrderItemDO> itemDOMap = CollectionUtil.toMap(orderItemList, "guid");
            freeReturnItemList = freeReturnItemService.list(new
                    LambdaQueryWrapper<FreeReturnItemDO>().in(FreeReturnItemDO::getOrderItemGuid, new ArrayList<>
                    (itemDOMap.keySet())).eq(FreeReturnItemDO::getType, FreeReturnTypeEnum.FREE.getCode()));
        }
        // 构建商品
        return AmountCalculationUtil.buildItem(orderItemList, itemAttrDOList, freeReturnItemList);
    }


    /**
     * 查询订单上的优惠信息/附加费明细/退款信息列表
     */
    private OrderInfoBatchDTO queryOrderInfoBatchDTO(DineInOrderListReqDTO reqDTO, List<String> orderGuidList, UserContext userInfoDTO) {
        EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
        UserContextUtils.put(userInfoDTO);
        OrderInfoBatchDTO orderInfoBatchDTO = new OrderInfoBatchDTO();
        // 批量查询退款信息
        if (reqDTO.getStates().contains(OrderStateEnum.CANCELLATION.getCode())) {
            List<RefundTransactionRecordDetailRespDTO> refundDetailList = queryBatchOrderRefundInfo(orderGuidList);
            if (CollectionUtils.isNotEmpty(refundDetailList)) {
                orderInfoBatchDTO.setRefundDetailMap(refundDetailList.stream()
                        .collect(Collectors.groupingBy(RefundTransactionRecordDetailRespDTO::getOrderGuid)));
            }
        }
        List<OrderMultiMember> orderMultiMembers = orderMultiMemberService.listByOrderGuidList(orderGuidList);
        if (CollectionUtils.isNotEmpty(orderMultiMembers)) {
            Map<String, List<MultiMemberDTO>> multiMemberDTOList = orderMultiMembers.stream()
                    .map(orderMultiMember -> {
                        MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
                        multiMemberDTO.setOrderGuid(String.valueOf(orderMultiMember.getOrderGuid()));
                        multiMemberDTO.setMemberGuid(orderMultiMember.getMemberGuid());
                        multiMemberDTO.setMemberName(orderMultiMember.getMemberName());
                        multiMemberDTO.setMemberPhone(orderMultiMember.getMemberPhone());
                        return multiMemberDTO;
                    })
                    .collect(Collectors.groupingBy(MultiMemberDTO::getOrderGuid));
            orderInfoBatchDTO.setMultiMemberDTOMap(multiMemberDTOList);
        }
        Map<String, DineinOrderDetailRespDTO> orderDetailMap = new HashMap<>();
        DineInService dineInService = SpringContextUtil.getInstance().getBean(DineInService.class);
        // 查询订单详情
        String enterpriseGuid = EnterpriseIdentifier.getEnterpriseGuid();
        orderGuidList.stream().parallel().forEach(orderGuid -> {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            UserContextUtils.putErp(enterpriseGuid);
            OrderDetailQueryDTO orderDetailQueryDTO = new OrderDetailQueryDTO();
            orderDetailQueryDTO.setData(orderGuid);
            orderDetailQueryDTO.setSingleFlag(false);
            DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetailForAndroid(orderDetailQueryDTO);
            orderDetailMap.put(orderGuid, orderDetail);
        });
        orderInfoBatchDTO.setOrderDetailMap(orderDetailMap);
        return orderInfoBatchDTO;
    }

    /**
     * 查询订单上的优惠信息
     */
    private OrderInfoBatchDTO queryOrderDiscountInfo(List<String> orderGuidList, UserContext userInfoDTO) {
        EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
        UserContextUtils.put(userInfoDTO);
        OrderInfoBatchDTO orderInfoBatchDTO = new OrderInfoBatchDTO();
        // 批量查询优惠列表
        List<DiscountDO> discountList = discountService.listByOrderGuids(orderGuidList);
        if (CollectionUtils.isNotEmpty(discountList)) {
            List<DiscountFeeDetailDTO> discountFeeDetailList = orderTransform.discountDOS2DiscountFeeDetailDTOS(discountList);
            orderInfoBatchDTO.setDiscountFeeDetailMap(discountFeeDetailList.stream()
                    .collect(Collectors.groupingBy(DiscountFeeDetailDTO::getOrderGuid)));
        }
        return orderInfoBatchDTO;
    }

    /**
     * 批量查询退款信息
     */
    private List<RefundTransactionRecordDetailRespDTO> queryBatchOrderRefundInfo(List<String> orderGuidList) {
        List<Long> orderGuids = orderGuidList.stream().map(Long::valueOf).collect(Collectors.toList());
        List<TransactionRecordDO> transactionRecordList = transactionRecordService.listByOrderGuids(orderGuids);
        List<RefundTransactionRecordDetailRespDTO> returnRespList = TransactionRecordTransform.INSTANCE
                .dos2TransactionRecordDetailsRespDTO(transactionRecordList);
        returnRespList.forEach(e -> {
            BigDecimal amount = new BigDecimal(e.getAmount()).negate();
            e.setAmount(amount.setScale(2, RoundingMode.DOWN).toPlainString());
        });
        return returnRespList;
    }


    /**
     * 构建订单返回信息
     */
    private void buildOrderInfoRespDTO(List<OrderInfoRespDTO> records, OrderInfoBatchDTO orderInfoBatchDTO) {
        orderInfoBatchDTO.setSurchargeLinkMap(MapUtils.isEmpty(orderInfoBatchDTO.getSurchargeLinkMap()) ?
                Maps.newHashMap() : orderInfoBatchDTO.getSurchargeLinkMap());
        orderInfoBatchDTO.setDiscountFeeDetailMap(MapUtils.isEmpty(orderInfoBatchDTO.getDiscountFeeDetailMap()) ?
                Maps.newHashMap() : orderInfoBatchDTO.getDiscountFeeDetailMap());
        orderInfoBatchDTO.setRefundDetailMap(MapUtils.isEmpty(orderInfoBatchDTO.getRefundDetailMap()) ?
                Maps.newHashMap() : orderInfoBatchDTO.getRefundDetailMap());
        orderInfoBatchDTO.setMultiMemberDTOMap(MapUtils.isEmpty(orderInfoBatchDTO.getMultiMemberDTOMap()) ?
                Maps.newHashMap() : orderInfoBatchDTO.getMultiMemberDTOMap());
        orderInfoBatchDTO.setOrderDetailMap(MapUtils.isEmpty(orderInfoBatchDTO.getOrderDetailMap()) ?
                Maps.newHashMap() : orderInfoBatchDTO.getOrderDetailMap());
        for (OrderInfoRespDTO respDTO : records) {
            buildOrderInfoRespDTO(respDTO, orderInfoBatchDTO);
        }
    }

    /**
     * 构建微信小程序订单返回信息
     */
    private void buildWxOrderInfoRespDTO(List<OrderInfoRespDTO> records, List<DineInItemDTO> itemList, OrderInfoBatchDTO orderInfoBatchDTO) {
        Map<String, List<DineInItemDTO>> itemMap = itemList.stream().collect(Collectors.groupingBy(DineInItemDTO::getOrderGuid));
        Map<String, List<DiscountFeeDetailDTO>> discountFeeDetailMap = Optional.ofNullable(orderInfoBatchDTO.getDiscountFeeDetailMap()).orElse(Maps.newHashMap());
        for (OrderInfoRespDTO respDTO : records) {
            // 商品信息
            respDTO.setItemList(itemMap.get(respDTO.getGuid()));
            if (Lists.newArrayList(StateEnum.READY.getCode(), StateEnum.PENDING.getCode(), StateEnum.FAILURE.getCode(),
                            StateEnum.CANCEL.getCode(), StateEnum.INVALID.getCode())
                    .contains(respDTO.getState())) {
                List<DiscountFeeDetailDTO> discountFeeDetailDTOList = discountFeeDetailMap.get(respDTO.getGuid());
                if (CollectionUtils.isEmpty(discountFeeDetailDTOList)) {
                    continue;
                }
                BigDecimal totalDiscount = discountFeeDetailDTOList.stream()
                        .map(DiscountFeeDetailDTO::getDiscountFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                respDTO.setActuallyPayFee(respDTO.getOrderFee().subtract(totalDiscount));
            }
            if (Objects.isNull(respDTO.getActuallyPayFee())) {
                respDTO.setActuallyPayFee(BigDecimal.ZERO);
            }
        }
    }

    /**
     * 构建订单返回信息
     */
    private void buildOrderInfoRespDTO(OrderInfoRespDTO respDTO, OrderInfoBatchDTO orderInfoBatchDTO) {
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = orderInfoBatchDTO.getOrderDetailMap().get(respDTO.getGuid());
        // 商品信息
        respDTO.setItemList(dineinOrderDetailRespDTO.getDineInItemDTOS());
        // 附加费信息
        respDTO.setSurchargeLinkList(dineinOrderDetailRespDTO.getSurchargeLinkList());
        // 优惠信息
        respDTO.setDiscountFeeDetailList(dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS());
        // 退款信息
        respDTO.setRefundDetailList(orderInfoBatchDTO.getRefundDetailMap().get(respDTO.getGuid()));
        // 多张会员卡支付信息
        respDTO.setMultiMemberList(orderInfoBatchDTO.getMultiMemberDTOMap().get(respDTO.getGuid()));
        // 商品信息 重设某些属性值
        orderItemHandler(respDTO);
        // 订单信息处理
        orderHandler(respDTO);
        // 实付金额修正
        respDTO.setActuallyPayFee(dineinOrderDetailRespDTO.getActuallyPayFee());
        if (Objects.nonNull(dineinOrderDetailRespDTO.getSubOrderDetails())) {
            int guestCount = 0;
            for (DineinOrderDetailRespDTO subOrderDetail : dineinOrderDetailRespDTO.getSubOrderDetails()) {
                guestCount += subOrderDetail.getGuestCount();
                respDTO.getItemList().addAll(subOrderDetail.getDineInItemDTOS());
            }
            respDTO.setGuestCount(respDTO.getGuestCount() + guestCount);
        }
    }

    /**
     * 构建订单优惠信息
     */
    private List<DiscountFeeDetailDTO> buildDiscountFeeDetailDTO(OrderInfoRespDTO respDTO, Map<String, List<DiscountFeeDetailDTO>> discountFeeDetailMap) {
        List<DiscountFeeDetailDTO> discountFeeDetailList = discountFeeDetailMap.get(respDTO.getGuid());
        if (CollectionUtils.isEmpty(discountFeeDetailList)) {
            return Lists.newArrayList();
        }
        Map<Integer, DiscountFeeDetailDTO> discountTypeMap = CollectionUtil.toMap(discountFeeDetailList, "discountType");
        // 第三方活动特殊处理
        DiscountFeeDetailDTO thirdActivityDiscount = discountTypeMap.get(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
        BigDecimal discountFee = BigDecimal.ZERO;
        if (!ObjectUtils.isEmpty(thirdActivityDiscount)) {
            if (BigDecimalUtil.greaterThanZero(thirdActivityDiscount.getDiscountFee())) {
                discountFee = thirdActivityDiscount.getDiscountFee();
                BigDecimal excessAmount = Optional.ofNullable(respDTO.getExcessAmount()).orElse(BigDecimal.ZERO);
                respDTO.setActuallyPayFee(respDTO.getActuallyPayFee()
                        .add(thirdActivityDiscount.getDiscountFee())
                        .subtract(excessAmount));
            }
            // 此处干掉第三方活动，避免显示
            discountFeeDetailList.removeIf(discount -> Objects.equals(DiscountTypeEnum.THIRD_ACTIVITY.getCode(), discount.getDiscountType()));
        }
        BigDecimal totalGroupAmount = discountFeeDetailList.stream()
                .filter(e -> GroupBuyTypeEnum.CODE_LIST.contains(e.getDiscountType()))
                .map(DiscountFeeDetailDTO::getDiscountFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        respDTO.setActuallyPayFee(respDTO.getActuallyPayFee().add(totalGroupAmount));
        // 优惠信息不展示团购信息
        discountFeeDetailList.removeIf(e -> GroupBuyTypeEnum.CODE_LIST.contains(e.getDiscountType()));
        // 如果有第三方活动，查询时算作实收
        if (BigDecimalUtil.greaterThanZero(discountFee)) {
            discountFee = discountFeeDetailList.stream()
                    .map(DiscountFeeDetailDTO::getDiscountFee)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            discountFee = respDTO.getOrderFee().subtract(respDTO.getActuallyPayFee());
        }
        respDTO.setDiscountFee(discountFee);
        // 过滤优惠为0的
        discountFeeDetailList.removeIf(e -> e.getDiscountFee().compareTo(BigDecimal.ZERO) <= 0);
        return discountFeeDetailList;
    }

    /**
     * 商品明细处理
     */
    private void orderItemHandler(OrderInfoRespDTO respDTO) {
        List<DineInItemDTO> itemList = respDTO.getItemList();
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        // 处理商品折扣
        orderItemChangePriceHandler(respDTO);
        // 处理商品优惠价
        orderItemDiscountPriceHandler(respDTO);
        // 处理多次部分退款的商品合并
        orderItemRefundMergeHandler(respDTO);
    }

    /**
     * 订单处理
     */
    private void orderHandler(OrderInfoRespDTO respDTO) {
        if (!Objects.equals(OrderStateEnum.CANCELLATION.getCode(), respDTO.getState())) {
            return;
        }
        // 退款单处理
        // 附加费处理
        List<SurchargeLinkDTO> surchargeLinkList = respDTO.getSurchargeLinkList();
        if (CollectionUtils.isNotEmpty(surchargeLinkList)) {
            Map<String, List<SurchargeLinkDTO>> surchargeLinkGroupByMap = surchargeLinkList.stream()
                    .collect(Collectors.groupingBy(SurchargeLinkDTO::getUniqueKey));
            List<SurchargeLinkDTO> surchargeLinkMergeList = Lists.newArrayList();
            for (Map.Entry<String, List<SurchargeLinkDTO>> entry : surchargeLinkGroupByMap.entrySet()) {
                List<SurchargeLinkDTO> innerList = entry.getValue();
                SurchargeLinkDTO surchargeLinkDTO = innerList.get(0);
                surchargeLinkDTO.setAmount(surchargeLinkDTO.getUnitPrice().multiply(new BigDecimal(innerList.size())));
                surchargeLinkMergeList.add(surchargeLinkDTO);
            }
            respDTO.setSurchargeLinkList(surchargeLinkMergeList);
        }
    }


    /**
     * 处理商品折扣
     */
    private void orderItemChangePriceHandler(OrderInfoRespDTO respDTO) {
        List<DiscountFeeDetailDTO> discountFeeDetailList = respDTO.getDiscountFeeDetailList();
        if (CollectionUtils.isEmpty(discountFeeDetailList)) {
            return;
        }
        discountFeeDetailList.removeIf(e -> !BigDecimalUtil.greaterThanZero(e.getDiscountFee()));
        BigDecimal singleDiscountFee = discountFeeDetailList.stream()
                .filter(e -> DiscountTypeEnum.SINGLE_DISCOUNT.getCode() == e.getDiscountType())
                .map(DiscountFeeDetailDTO::getDiscountFee)
                .findFirst()
                .orElse(BigDecimal.ZERO);
        if (singleDiscountFee.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        List<DineInItemDTO> itemList = respDTO.getItemList();
        for (DineInItemDTO item : itemList) {
            if (item.getPriceChangeType() != null && item.getPriceChangeType() == 2) {
                // 折扣需要补偿价格
                BigDecimal reductionPrice = item.getOriginalPrice()
                        .multiply(AmountCalculationUtil.ITEM_DISCOUNT_PERCENT_BASE.subtract(new BigDecimal
                                        (item.getDiscountPercent()))
                                .divide(AmountCalculationUtil.ITEM_DISCOUNT_PERCENT_BASE, 2, RoundingMode.HALF_DOWN))
                        .multiply(item.getCurrentCount());
                item.setItemPrice(item.getItemPrice().subtract(reductionPrice).setScale(2, RoundingMode.HALF_UP));
            }
        }
    }

    /**
     * 处理商品优惠价
     */
    private void orderItemDiscountPriceHandler(OrderInfoRespDTO respDTO) {
        List<DineInItemDTO> itemList = respDTO.getItemList();
        for (DineInItemDTO item : itemList) {
            // 处理redis中老数据的refundCount为null的处理
            item.setRefundCount(Objects.isNull(item.getRefundCount()) ? BigDecimal.ZERO : item.getRefundCount());
            // 验券加商品 : 优惠价 = 原价
            if (!StringUtils.isEmpty(item.getCouponCode())) {
                item.setDiscountPrice(null);
                item.setDiscountTotalPrice(null);
                continue;
            }
            // 赠品 ： 优惠价 = 0
            List<FreeItemDTO> freeItemList = item.getFreeItemDTOS();
            if (CollectionUtils.isNotEmpty(freeItemList)) {
                for (FreeItemDTO freeItemDTO : freeItemList) {
                    freeItemDTO.setDiscountPrice(BigDecimal.ZERO);
                    freeItemDTO.setDiscountTotalPrice(BigDecimal.ZERO);
                }
            }
            if (Objects.isNull(item.getDiscountTotalPrice())) {
                continue;
            }
            if (item.getItemPrice().compareTo(item.getDiscountTotalPrice()) > 0) {
                item.setDiscountPrice(item.getDiscountTotalPrice().divide(item.getCurrentCount(), 2, RoundingMode.DOWN));
            } else {
                item.setDiscountPrice(null);
                item.setDiscountTotalPrice(null);
            }
        }
    }

    /**
     * 处理多次部分退款的商品合并
     */
    private void orderItemRefundMergeHandler(OrderInfoRespDTO respDTO) {
        if (!Objects.equals(OrderStateEnum.CANCELLATION.getCode(), respDTO.getState())) {
            return;
        }
        List<DineInItemDTO> itemList = respDTO.getItemList();
        Map<Long, List<DineInItemDTO>> itemMap = itemList.stream().collect(Collectors.groupingBy(DineInItemDTO::getOriginalOrderItemGuid));
        List<DineInItemDTO> itemMergeList = Lists.newArrayList();
        for (Map.Entry<Long, List<DineInItemDTO>> entry : itemMap.entrySet()) {
            List<DineInItemDTO> innerList = entry.getValue();
            DineInItemDTO itemDTO = innerList.get(0);
            BigDecimal count = innerList.stream().map(DineInItemDTO::getCurrentCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            itemDTO.setCurrentCount(count);
            itemMergeList.add(itemDTO);
        }
        respDTO.setItemList(itemMergeList);
    }

    @Override
    public OrderDTO findByOrderGuid(String orderGuid) {
        log.info("enterpriseGuid={}", UserContextUtils.getEnterpriseGuid());
        EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        OrderDO order = orderService.getOne(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getGuid, orderGuid));
        return orderTransform.orderDO2DTO(order);
    }

    @Override
    public List<OrderItemRecordDTO> getOrderItemRecord(String orderGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        List<OrderItemRecordDO> orderItemRecordDOS = orderItemRecordService.listByOrderGuid(Long.parseLong(orderGuid));
        if (CollectionUtils.isEmpty(orderItemRecordDOS)) {
            return Lists.newArrayList();
        }
        List<OrderItemRecordDTO> dto = orderTransform.orderItemRecordListDO2orderItemDTO(orderItemRecordDOS);
        return dto;
    }

    @Override
    public List<ItemAttrDetailDTO> getItemAttr(String orderGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        List<ItemAttrDO> attrDOList = itemAttrService.findByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(attrDOList)) {
            return Lists.newArrayList();
        }
        List<ItemAttrDetailDTO> itemAttrDetailDTOS = orderTransform.itemAttrDOList2ItemAttrDTO(attrDOList);
        return itemAttrDetailDTOS;
    }

    @Override
    @Transactional
    public OrderDTO findByEnterpriseGuid(String enterpriseGuid) {

        OrderDO order = orderService.findByEnterpriseGuid();
        OrderDTO dto = orderTransform.orderDO2DTO(order);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrderIsHandleByOrderGuid(String orderGuid, Integer isHandle) {
        EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        int result = orderService.updateOrderIsHandleByOrderGuid(Long.parseLong(orderGuid), isHandle);
        return result == 1;
    }

    @Override
    public void refreshModifyTime(String enterpriseGuid, String orderGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        orderRefreshMapper.refreshModifyTime(orderGuid);
    }

    @Override
    public boolean refreshModifyTimeByDay(String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        try {
            Date day = getDate(-1);
            int second = getSecond();
            orderRefreshMapper.refreshModifyTimeByDay(second, day);
        } catch (Exception e) {
            log.info("refreshModifyTimeByDay fail .enterpriseGuid:{},", enterpriseGuid, e);
            return false;
        } finally {
            EnterpriseIdentifier.remove();
        }
        return true;

    }

    @Override
    public boolean updateOrderRemarkById(String guid, String remark) {
        OrderDO orderDO = orderService.getOne(Wrappers.<OrderDO>lambdaQuery().eq(OrderDO::getGuid, guid));
        if (ObjectUtils.isEmpty(orderDO)) {
            return false;
        }
        orderDO.setRemark(remark);
        return orderService.updateById(orderDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderItemRemarkById(String guid, String itemGuid, String remark) {
        OrderItemDO orderItemDO = orderItemService.getOne(Wrappers.<OrderItemDO>lambdaQuery()
                .eq(OrderItemDO::getOrderGuid, guid)
                .eq(OrderItemDO::getItemGuid, itemGuid));
        if (ObjectUtils.isEmpty(orderItemDO)) {
            return false;
        }
        orderItemDO.setRemark(remark);
        boolean result = orderItemService.updateById(orderItemDO);

        //更新交易订单商品记录的备注信息
        OrderItemRecordDO orderItemRecordDO = orderItemRecordService.getOne(Wrappers.<OrderItemRecordDO>lambdaQuery()
                .eq(OrderItemRecordDO::getOrderItemGuid, orderItemDO.getGuid()));
        if (!ObjectUtils.isEmpty(orderItemRecordDO)) {
            orderItemRecordDO.setRemark(remark);
            orderItemRecordService.updateById(orderItemRecordDO);
        }
        return result;
    }

    public static int getSecond() {
        int hour = LocalDateTime.now().getHour();
        return 1 * (hour % 2 == 0 ? 1 : (-1));
    }

    public static Date getDate(int diff) {
        LocalDate yesterday = LocalDate.now().plusDays(diff);
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = yesterday.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    @Override
    public String getNeedUpdateOrderGuid(String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        return orderRefreshMapper.getNeedUpdateOrderGuid();
    }

    @Override
    public List<AppendFeeDTO> getAppendFee(String orderGuid, String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        List<AppendFeeDO> appendFeeDOS = appendFeeMpService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(appendFeeDOS)) {
            return Lists.newArrayList();
        }
        List<AppendFeeDTO> appendFeeDTOS = orderTransform.appendDOList2DTOList(appendFeeDOS);
        return appendFeeDTOS;
    }

    @Override
    public List<TransactionRecordDTO> getTransactionRecord(String orderGuid, String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        List<TransactionRecordDO> transactionRecordDOS = transactionRecordService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(transactionRecordDOS)) {
            return Lists.newArrayList();
        }
        List<TransactionRecordDTO> transactionRecordDTOS =
                localTransform.transactionRecordDOS2TransactionRecordDTOS(transactionRecordDOS);
        return transactionRecordDTOS;
    }

    @Override
    public List<DiscountDTO> getDiscount(String orderGuid, String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        List<DiscountDO> discountDOS = discountService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(discountDOS)) {
            return Lists.newArrayList();
        }
        List<DiscountDTO> discountDTOS = orderTransform.discountDOS2DiscountDTOS(discountDOS);
        return discountDTOS;
    }

    @Override
    public List<FreeReturnItemDTO> getFreeReturnItem(String orderGuid, String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        List<FreeReturnItemDO> freeReturnItemDOS = freeReturnItemService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(freeReturnItemDOS)) {
            return Lists.newArrayList();
        }
        List<FreeReturnItemDTO> freeReturnItemDTOS = localTransform.freeDOList2DTOList(freeReturnItemDOS);
        return freeReturnItemDTOS;
    }

    @Override
    public List<OrderItemDTO> getOrderItem(Long orderGuid, String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        List<OrderItemDO> orderItemDOS = orderItemService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(orderItemDOS)) {
            return Lists.newArrayList();
        }
        List<OrderItemDTO> orderItemDTOS = orderTransform.toOrderItemDTOS(orderItemDOS);
        return orderItemDTOS;
    }

    @Override
    public List<OrderItemDTO> getOrderItems(List<String> orderGuids, String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        List<OrderItemDO> orderItemDOS = orderItemService.listByOrderLists(orderGuids);
        if (CollectionUtils.isEmpty(orderItemDOS)) {
            return Lists.newArrayList();
        }
        return orderTransform.toOrderItemDTOS(orderItemDOS);
    }

    @Override
    public List<OrderDTO> findByRecoveryId(String recoveryId) {
        EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        List<OrderDO> order = orderService.listByRecoveryId(recoveryId);
        return orderTransform.orderDOList2DTOList(order);
    }

    @Override
    public StoreGatherTotalRespDTO storeGatherTotalList(StoreGatherReportReqDTO storeGatherReportReqDTO,
                                                        boolean isExport) {
        log.info("商户后台-门店汇总-入参：storeGatherReportReqDTO={}", JacksonUtils.writeValueAsString(storeGatherReportReqDTO));
        //门店兼容
        if (CollectionUtils.isEmpty(storeGatherReportReqDTO.getStoreGuids())) {
            List<String> storeGuids = storeGatherReportReqDTO.getStoreGuids();
            storeGuids.add(storeGatherReportReqDTO.getStoreGuid());
            storeGatherReportReqDTO.setStoreGuids(storeGuids);
        }
        //时间兼容
        if (storeGatherReportReqDTO.getBusinessEndDateTime() == null) {
            storeGatherReportReqDTO.setBusinessEndDateTime(storeGatherReportReqDTO.getEndDate().atTime(LocalTime.MAX));
        }
        if (storeGatherReportReqDTO.getBusinessStartDateTime() == null) {
            storeGatherReportReqDTO.setBusinessStartDateTime(storeGatherReportReqDTO.getStartDate().atTime(LocalTime.MIN));
        }
        StoreGatherTotalRespDTO respDTO = new StoreGatherTotalRespDTO();
        if (org.springframework.util.CollectionUtils.isEmpty(storeGatherReportReqDTO.getStoreGuids())) {
            respDTO.setRespDataPage(new Page<>(storeGatherReportReqDTO.getCurrentPage(),
                    storeGatherReportReqDTO.getPageSize(), 0));
            return respDTO;
        }
        storeGatherReportReqDTO.setStoreGuidStr(String.join(",", storeGatherReportReqDTO.getStoreGuids()));
        IPage<StoreGatherReportRespDTO> respDTOIPage;
        final PageAdapter<StoreGatherReportRespDTO> pageAdapter = new PageAdapter<>(storeGatherReportReqDTO);
        if (isExport) {
            respDTOIPage = orderMapper.storeGatherTotalListByExport(pageAdapter, storeGatherReportReqDTO);
        } else {
            respDTOIPage = orderMapper.storeGatherTotalList(pageAdapter, storeGatherReportReqDTO);
        }
        List<StoreGatherReportRespDTO> pageRecords = respDTOIPage.getRecords();
        if (respDTOIPage.getPages() == 0) {
            log.info("storeGatherReportRespDTOS is empty");
            pageRecords = buildNoDataResp(storeGatherReportReqDTO);
            int pageFrom =
                    ((Long) ((storeGatherReportReqDTO.getCurrentPage() - 1) * storeGatherReportReqDTO.getPageSize())).intValue();
            int dtoSize = pageRecords.size();
            int pageTo = pageFrom + storeGatherReportReqDTO.getPageSize() > dtoSize ? dtoSize :
                    pageFrom + BigDecimal.valueOf(storeGatherReportReqDTO.getPageSize()).intValue();
            pageRecords = pageRecords.subList(pageFrom, pageTo);
        }
        List<String> storeGuids =
                pageRecords.stream().map(StoreGatherReportRespDTO::getStoreGuid).distinct().collect(Collectors.toList());
        //根据storeGuids获取门店详情，包括品牌信息
        log.info("当前线程enterpriseGuid:{}", UserContextUtils.getEnterpriseGuid());
        List<StoreDTO> storeDTOList = storeClientService.queryStoreAndBrandByIdList(storeGuids);
        // logger.info("门店统计-根据storeGuids获取门店列表，storeDTOS={}", JacksonUtils.writeValueAsString
        pageRecords.forEach(storeGatherReportRespDTO -> {
            storeGatherReportRespDTO.setStoreName(getStoreName(storeDTOList,
                    storeGatherReportRespDTO.getStoreGuid()));
            storeGatherReportRespDTO.setBrandName(getBrandName(storeDTOList,
                    storeGatherReportRespDTO.getStoreGuid()));
        });
        Page<StoreGatherReportRespDTO> page = new Page<>(respDTOIPage.getCurrent(), respDTOIPage.getSize(),
                respDTOIPage.getTotal());
        page.setData(pageRecords);
        respDTO.setRespDataPage(page);
        return respDTO;
    }

    private List<StoreGatherReportRespDTO> buildNoDataResp(StoreGatherReportReqDTO storeGatherReportReqDTO) {
        List<StoreGatherReportRespDTO> storeGatherReportRespDTOS = Lists.newArrayList();
        LocalDateTime beginDate = storeGatherReportReqDTO.getBusinessStartDateTime();
        LocalDateTime endDate = storeGatherReportReqDTO.getBusinessEndDateTime();
        List<LocalDate> periodDate = Lists.newArrayList();
        int period =
                ((Long) (endDate.toLocalDate().toEpochDay() - beginDate.toLocalDate().toEpochDay())).intValue() + 1;
        for (int i = 0; i < period; i++) {
            periodDate.add(beginDate.toLocalDate().plusDays(i));
        }
        List<String> storeGuids =
                storeGatherReportReqDTO.getStoreGuids().stream().sorted().collect(Collectors.toList());
        for (int i = 0; i < periodDate.size(); i++) {
            LocalDate queryDate = endDate.toLocalDate().minusDays(i);
            storeGuids.forEach(s -> {
                StoreGatherReportRespDTO storeGatherDTO = new StoreGatherReportRespDTO();
                storeGatherDTO.setDate(DateTimeUtils.localDate2String(queryDate, "yyyyMMdd"));
                storeGatherDTO.setStoreGuid(s);
                storeGatherDTO.setOrderFee(BigDecimal.ZERO);
                storeGatherDTO.setDiscountFee(BigDecimal.ZERO);
                storeGatherDTO.setActuallyPayFee(BigDecimal.ZERO);
                storeGatherDTO.setOrderCount(BigDecimal.ZERO.intValue());
                storeGatherDTO.setOrderAverageFee(BigDecimal.ZERO);
                storeGatherDTO.setGuestCount(BigDecimal.ZERO.intValue());
                storeGatherDTO.setGuestAverageFee(BigDecimal.ZERO);
                storeGatherReportRespDTOS.add(storeGatherDTO);
            });
        }
        return storeGatherReportRespDTOS;
    }

    /**
     * 获取门店名称
     *
     * @param storeDTOS
     * @param storeGuid
     * @return
     */
    private String getStoreName(List<StoreDTO> storeDTOS, String storeGuid) {
        return storeDTOS.stream().filter(storeDTO -> storeGuid.equals(storeDTO.getGuid())).findAny().get().getName();
    }

    /**
     * 获取品牌名称
     *
     * @param storeDTOS
     * @param storeGuid
     * @return
     */
    private String getBrandName(List<StoreDTO> storeDTOS, String storeGuid) {
        StoreDTO exitStoreDto =
                storeDTOS.stream().filter(storeDTO -> storeGuid.equals(storeDTO.getGuid())).findAny().get();
        if (Objects.isNull(exitStoreDto) || Objects.isNull(exitStoreDto.getBrandDTOList()) || exitStoreDto.getBrandDTOList().size() == 0) {
            return "";
        }
        return exitStoreDto.getBrandDTOList().get(0).getName();
    }

    /**
     * 根据并台主单guid查询定订单信息列表
     *
     * @param combineOrderGuid 并台主单guid
     * @return 订单信息列表
     */
    @Override
    public List<OrderDTO> listOrderByCombineOrderGuid(String combineOrderGuid) {
        if (StringUtils.isEmpty(combineOrderGuid)) {
            throw new BusinessException("并台主单guid为空");
        }
        List<OrderDO> orderDOList = orderService.list(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getMainOrderGuid, combineOrderGuid)
        );
        return orderTransform.orderDOList2DTOList(orderDOList);
    }

    @Override
    public OrderUploadRespDTO copyOrders(String fileUrl) {
        //读取Excel中订单信息
        List<OrderUploadDTO> orderUploadDTOS = readOrderExcel(fileUrl);
        if (CollectionUtils.isEmpty(orderUploadDTOS)) {
            throw new BusinessException("文件内容为空");
        }
        //门店guid默认取第一条数据
        String storeGuid = orderUploadDTOS.get(0).getStoreGuid();
        if (StringUtils.isEmpty(storeGuid)) {
            throw new BusinessException("门店guid不能为空");
        }
        //查询门店所有可用桌台
        TableBasicQueryDTO queryDTO = new TableBasicQueryDTO();
        queryDTO.setStoreGuid(storeGuid);
        List<TableBasicDTO> tables = tableClientService.listByWeb(queryDTO);
        List<String> importOrderGuids = orderUploadDTOS.stream().map(OrderUploadDTO::getOrderGuid).collect(Collectors.toList());
        log.info("订单列表：{}", JSONArray.toJSONString(importOrderGuids));
        //只处理支付成功且无并单的情况
        List<OrderDO> orderDOS = orderService.list(new LambdaQueryWrapper<OrderDO>()
                .in(OrderDO::getGuid, importOrderGuids)
                .eq(OrderDO::getTradeMode, 0)
                .eq(OrderDO::getState, StateEnum.SUCCESS.getCode())
                .eq(OrderDO::getUpperState, UpperStateEnum.GENERAL.getCode()));
        if (CollectionUtils.isEmpty(orderDOS)) {
            throw new BusinessException("未查询到订单");
        }
        //获取桌台占用信息
        Map<String, List<OrderDO>> tableMap = getBusyTableMap(storeGuid, orderDOS);
        List<String> orderGuids = orderDOS.stream().map(orderDO -> String.valueOf(orderDO.getGuid())).collect(Collectors.toList());
        //查询订单、订单商品、退菜、优惠、商品属性、附加费、优惠等
        List<OrderItemDO> orderItemDOS = orderItemService.listByOrderLists(orderGuids);
        List<OrderItemRecordDO> orderItemRecordDOS = orderItemRecordService.listByOrderLists(orderGuids);
        List<FreeReturnItemDO> freeReturnItemDOS = freeReturnItemService.listByOrderGuids(orderGuids);
        List<ItemAttrDO> itemAttrDOS = itemAttrService.listByItemGuids(orderItemDOS.stream().map(OrderItemDO::getGuid).collect(Collectors.toList()));
        List<TransactionRecordDO> transactionRecordDOS = transactionRecordService.list(
                new LambdaQueryWrapper<TransactionRecordDO>().in(TransactionRecordDO::getOrderGuid, orderGuids));
        List<AppendFeeDO> appendFeeDOS = appendFeeService.list(new LambdaQueryWrapper<AppendFeeDO>().in(AppendFeeDO::getOrderGuid, orderGuids));
        List<DiscountDO> discountDOS = discountService.listByOrderGuids(orderGuids);
        //复制的订单、订单商品、退菜、优惠、商品属性、附加费、优惠等
        List<OrderDO> copyOrders = new ArrayList<>();
        List<OrderItemDO> copyOrderItems = new ArrayList<>();
        List<OrderItemRecordDO> copyOrderItemRecords = new ArrayList<>();
        List<FreeReturnItemDO> copyFreeReturnItems = new ArrayList<>();
        List<ItemAttrDO> copyItemAttrDOs = new ArrayList<>();
        List<TransactionRecordDO> copyTransactionRecordDOs = new ArrayList<>();
        List<AppendFeeDO> copyAppendFees = new ArrayList<>();
        List<DiscountDO> copyDiscounts = new ArrayList<>();
        //无空闲桌台的订单guid
        List<String> notFreeTableOrderGuids = new ArrayList<>();
        OrderDO copyOrder;
        List<Long> copyOrderGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_ORDER, orderDOS.size());
        //循环复制订单
        int i = 0;
        for (OrderDO orderDO : orderDOS) {
            copyOrder = new OrderDO();
            BeanUtil.copyProperties(orderDO, copyOrder, "guid");
            //获取当前时间段空闲的桌台 如果没有空闲桌台 不进行复制
            TableBasicDTO tableBasicDTO = getFreeTable(tables, tableMap, orderDO, copyOrder);
            if (null == tableBasicDTO) {
                notFreeTableOrderGuids.add(String.valueOf(orderDO.getGuid()));
                continue;
            }
            copyOrder.setGuid(copyOrderGuids.get(i++));
            copyOrder.setDiningTableGuid(tableBasicDTO.getGuid());
            copyOrder.setDiningTableName(CommonUtil.jointDiningTableName(tableBasicDTO.getAreaName(), tableBasicDTO.getTableCode()));
            copyOrder.setStoreGuid(storeGuid);
            copyOrder.setStoreName(tableBasicDTO.getStoreName());
            copyOrder.setCopyOrderGuid(orderDO.getGuid());
            copyOrders.add(copyOrder);
            //订单商品、商品记录、增菜/退菜处理、订单商品属性
            List<OrderItemDO> itemDOS = orderItemDOS.stream().filter(orderItemDO ->
                    orderItemDO.getOrderGuid().equals(orderDO.getGuid())).collect(Collectors.toList());
            copyItem(orderItemRecordDOS, freeReturnItemDOS, itemAttrDOS, copyOrderItems, copyOrderItemRecords, copyFreeReturnItems, copyItemAttrDOs, copyOrder, tableBasicDTO, itemDOS);
            //订单支付记录
            List<TransactionRecordDO> transactionRecords = transactionRecordDOS.stream().filter(transactionRecordDO ->
                    transactionRecordDO.getOrderGuid().equals(orderDO.getGuid())).collect(Collectors.toList());
            copyTransaction(copyTransactionRecordDOs, copyOrder, tableBasicDTO, transactionRecords);
            //附加费
            List<AppendFeeDO> appendFees = appendFeeDOS.stream().filter(appendFeeDO ->
                    appendFeeDO.getOrderGuid().equals(orderDO.getGuid())).collect(Collectors.toList());
            copyAppendFee(appendFees, copyAppendFees, copyOrder, tableBasicDTO);
            //优惠
            List<DiscountDO> discountDOList = discountDOS.stream().filter(discountDO ->
                    discountDO.getOrderGuid().equals(orderDO.getGuid())).collect(Collectors.toList());
            copyDiscount(discountDOList, copyDiscounts, copyOrder, tableBasicDTO);
        }
        //批量保存订单相关数据
        orderService.saveBatch(copyOrders);
        if (CollectionUtils.isNotEmpty(copyOrderItems)) {
            orderItemService.saveBatch(copyOrderItems);
        }
        if (CollectionUtils.isNotEmpty(copyOrderItemRecords)) {
            orderItemRecordService.saveBatch(copyOrderItemRecords);
        }
        if (CollectionUtils.isNotEmpty(copyFreeReturnItems)) {
            freeReturnItemService.saveBatch(copyFreeReturnItems);
        }
        if (CollectionUtils.isNotEmpty(copyItemAttrDOs)) {
            itemAttrService.saveBatch(copyItemAttrDOs);
        }
        if (CollectionUtils.isNotEmpty(copyTransactionRecordDOs)) {
            transactionRecordService.saveBatch(copyTransactionRecordDOs);
        }
        if (CollectionUtils.isNotEmpty(copyAppendFees)) {
            appendFeeService.saveBatch(copyAppendFees);
        }
        if (CollectionUtils.isNotEmpty(copyDiscounts)) {
            discountService.saveBatch(copyDiscounts);
        }
        return getOrderUploadRespDTO(importOrderGuids, orderGuids, notFreeTableOrderGuids);
    }

    private Map<String, List<OrderDO>> getBusyTableMap(String storeGuid, List<OrderDO> orderDOS) {
        //获取导入订单时间极值
        List<LocalDateTime> timeList = Stream.concat(orderDOS.stream().map(OrderDO::getGmtCreate),
                orderDOS.stream().map(OrderDO::getCheckoutTime)).sorted().collect(Collectors.toList());
        LocalDateTime earlyDate = timeList.get(0);
        LocalDateTime laterDate = timeList.get(timeList.size() - 1);
        //查询时间段所有正餐订单
        List<OrderDO> existOrders = orderService.list(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getStoreGuid, storeGuid)
                .eq(OrderDO::getTradeMode, 0)
                .and(wrapper -> wrapper.between(OrderDO::getGmtCreate, earlyDate, laterDate)
                        .or().between(OrderDO::getCheckoutTime, earlyDate, laterDate)));
        return existOrders.stream().collect(Collectors.groupingBy(OrderDO::getDiningTableGuid));
    }

    /**
     * 获取空闲的桌台
     *
     * @param tables    门店所有桌台信息
     * @param tableMap  桌台订单map
     * @param orderDO   原始订单
     * @param copyOrder 新订单
     * @return 有空闲桌台即返回桌台信息 否则返回空
     */
    private TableBasicDTO getFreeTable(List<TableBasicDTO> tables, Map<String, List<OrderDO>> tableMap, OrderDO orderDO, OrderDO copyOrder) {
        for (TableBasicDTO table : tables) {
            List<OrderDO> tableOrders = tableMap.get(table.getGuid());
            //当前桌台没有订单
            if (CollectionUtils.isEmpty(tableOrders)) {
                tableMap.put(table.getGuid(), Collections.singletonList(copyOrder));
                return table;
            }
            //判断此桌台是否被占用
            Optional<OrderDO> optional = tableOrders.stream().filter(order ->
                    !(order.getCheckoutTime().isBefore(orderDO.getGmtCreate())
                            || order.getGmtCreate().isAfter(order.getCheckoutTime()))).findFirst();
            if (!optional.isPresent()) {
                List<OrderDO> orderDOS = new ArrayList<>(tableOrders);
                orderDOS.add(copyOrder);
                tableMap.put(table.getGuid(), orderDOS);
                return table;
            }
        }
        return null;
    }

    /**
     * 返回数据封装
     *
     * @param importOrderGuids       用户导入订单guid
     * @param orderGuids             数据库中存在的订单guid
     * @param notFreeTableOrderGuids 没有空闲桌台的订单guid
     * @return OrderUploadRespDTO
     */
    private OrderUploadRespDTO getOrderUploadRespDTO(List<String> importOrderGuids, List<String> orderGuids, List<String> notFreeTableOrderGuids) {
        importOrderGuids.removeAll(orderGuids);
        //未导入订单guid
        importOrderGuids.addAll(notFreeTableOrderGuids);
        OrderUploadRespDTO dto = new OrderUploadRespDTO();
        dto.setFail(importOrderGuids.size());
        dto.setSuccess(orderGuids.size() - notFreeTableOrderGuids.size());
        if (CollectionUtils.isNotEmpty(importOrderGuids)) {
            List<OrderUploadErrExlDTO> errExlDTOs = new ArrayList<>();
            importOrderGuids.forEach(guid -> {
                OrderUploadErrExlDTO exlDTO = new OrderUploadErrExlDTO();
                exlDTO.setOrderGuid(guid);
                if (notFreeTableOrderGuids.contains(guid)) {
                    exlDTO.setErrorMessage("该订单当前时段无空闲桌台");
                }
                errExlDTOs.add(exlDTO);
            });
            try {
                ExcelResult excelResult = BeanUtils.toResult(errExlDTOs);
                byte[] write = ExcelWriteUtils.write(excelResult, ExcelType.XLSX);
                FileDto fileDto = new FileDto();
                fileDto.setFileContent(com.holderzone.framework.security.SecurityManager.entryptBase64(write));
                fileDto.setFileName(UUID.randomUUID().toString().replace("-", "").substring(0, 5) + "." + ExcelType.XLSX);
                String failUrl = fileUploadClient.upload(fileDto);
                log.info("错误信息文件下载路径->>>>>{}", failUrl);
                dto.setFailUrl(failUrl);
            } catch (Exception e) {
                log.error("上传文件失败");
                dto.setFailUrl("上传错误信息失败");
            }
        }
        return dto;
    }

    private void copyItem(List<OrderItemRecordDO> orderItemRecordDOS, List<FreeReturnItemDO> freeReturnItemDOS, List<ItemAttrDO> itemAttrDOS, List<OrderItemDO> copyOrderItems, List<OrderItemRecordDO> copyOrderItemRecords, List<FreeReturnItemDO> copyFreeReturnItems, List<ItemAttrDO> copyItemAttrDOs, OrderDO copyOrder, TableBasicDTO tableBasicDTO, List<OrderItemDO> itemDOS) {
        if (CollectionUtils.isEmpty(itemDOS)) {
            return;
        }
        //处理非套餐子商品
        List<OrderItemDO> singleItems = itemDOS.stream().filter(orderItemDO -> null == orderItemDO.getParentItemGuid()
                || 0L == orderItemDO.getParentItemGuid()).collect(Collectors.toList());
        List<Long> copyOrderItemGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_ORDER_ITEM, singleItems.size());
        for (int j = 0; j < singleItems.size(); j++) {
            OrderItemDO itemDO = singleItems.get(j);
            OrderItemDO copyItemDO = new OrderItemDO();
            BeanUtil.copyProperties(itemDO, copyItemDO, "guid", "orderGuid");
            copyItemDO.setGuid(copyOrderItemGuids.get(j))
                    .setOrderGuid(copyOrder.getGuid());
            copyOrderItems.add(copyItemDO);
            //当前商品子商品处理
            copySonItem(orderItemRecordDOS, freeReturnItemDOS, itemAttrDOS, copyOrderItems, copyOrderItemRecords, copyFreeReturnItems, copyItemAttrDOs, copyOrder, tableBasicDTO, itemDOS, j, itemDO, copyItemDO);
            //商品相关数据处理
            copyItemAbout(orderItemRecordDOS, freeReturnItemDOS, itemAttrDOS, copyOrderItemRecords, copyFreeReturnItems, copyItemAttrDOs, copyOrder, tableBasicDTO, itemDO, copyItemDO, 0L);
        }
    }

    private void copySonItem(List<OrderItemRecordDO> orderItemRecordDOS, List<FreeReturnItemDO> freeReturnItemDOS, List<ItemAttrDO> itemAttrDOS, List<OrderItemDO> copyOrderItems, List<OrderItemRecordDO> copyOrderItemRecords, List<FreeReturnItemDO> copyFreeReturnItems, List<ItemAttrDO> copyItemAttrDOs, OrderDO copyOrder, TableBasicDTO tableBasicDTO, List<OrderItemDO> itemDOS, int j, OrderItemDO itemDO, OrderItemDO copyItemDO) {
        List<OrderItemDO> sonItems = itemDOS.stream().filter(orderItemDO -> itemDO.getGuid().equals(orderItemDO.getParentItemGuid())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sonItems)) {
            return;
        }
        List<Long> copySonItemGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_ORDER_ITEM, sonItems.size());
        for (int k = 0; k < sonItems.size(); k++) {
            OrderItemDO sonItem = sonItems.get(k);
            OrderItemDO copySonItemDO = new OrderItemDO();
            BeanUtil.copyProperties(sonItem, copySonItemDO, "guid", "orderGuid");
            copySonItemDO.setGuid(copySonItemGuids.get(k))
                    .setOrderGuid(copyOrder.getGuid())
                    .setParentItemGuid(copyItemDO.getGuid());
            copyOrderItems.add(copySonItemDO);
            copyItemAbout(orderItemRecordDOS, freeReturnItemDOS, itemAttrDOS, copyOrderItemRecords, copyFreeReturnItems, copyItemAttrDOs, copyOrder, tableBasicDTO, sonItem, copySonItemDO, copyItemDO.getGuid());
        }
    }

    private void copyItemAbout(List<OrderItemRecordDO> orderItemRecordDOS, List<FreeReturnItemDO> freeReturnItemDOS, List<ItemAttrDO> itemAttrDOS, List<OrderItemRecordDO> copyOrderItemRecords, List<FreeReturnItemDO> copyFreeReturnItems, List<ItemAttrDO> copyItemAttrDOs, OrderDO copyOrder, TableBasicDTO tableBasicDTO, OrderItemDO sonItem, OrderItemDO copySonItemDO, Long guid) {
        //商品记录处理
        List<OrderItemRecordDO> itemRecordDOS = orderItemRecordDOS.stream().filter(orderItemRecordDO ->
                orderItemRecordDO.getOrderItemGuid().equals(String.valueOf(sonItem.getGuid()))).collect(Collectors.toList());
        copyItemRecord(copyOrderItemRecords, copyOrder, copySonItemDO, itemRecordDOS, guid);
        //增菜/退菜处理
        List<FreeReturnItemDO> freeReturnItems = freeReturnItemDOS.stream().filter(freeReturnItemDO ->
                freeReturnItemDO.getOrderItemGuid().equals(sonItem.getGuid())).collect(Collectors.toList());
        copyFreeReturnItem(copyFreeReturnItems, copyOrder, tableBasicDTO, copySonItemDO, freeReturnItems);
        //订单商品属性处理
        List<ItemAttrDO> attrDOList = itemAttrDOS.stream().filter(itemAttrDO ->
                itemAttrDO.getOrderItemGuid().equals(sonItem.getGuid())).collect(Collectors.toList());
        copyItemAttr(copyItemAttrDOs, copyOrder, tableBasicDTO, copySonItemDO, attrDOList);
    }

    private void copyItemRecord(List<OrderItemRecordDO> copyOrderItemRecords, OrderDO copyOrder, OrderItemDO copyItemDO, List<OrderItemRecordDO> itemRecordDOS, Long parentItemGuid) {
        if (CollectionUtils.isEmpty(itemRecordDOS)) {
            return;
        }
        List<Long> copyOrderItemRecordGuids = dynamicHelper.generateGuids(GuidKeyConstant.ORDER_ITEM_RECORD, itemRecordDOS.size());
        for (int k = 0; k < itemRecordDOS.size(); k++) {
            OrderItemRecordDO orderItemRecordDO = itemRecordDOS.get(k);
            OrderItemRecordDO copyItemRecordDO = new OrderItemRecordDO();
            BeanUtil.copyProperties(orderItemRecordDO, copyItemRecordDO, "guid", "orderGuid", "orderItemGuid");
            copyItemRecordDO.setGuid(copyOrderItemRecordGuids.get(k))
                    .setOrderGuid(copyOrder.getGuid())
                    .setOrderItemGuid(String.valueOf(copyItemDO.getGuid()))
                    .setParentItemGuid(parentItemGuid);
            copyOrderItemRecords.add(copyItemRecordDO);
        }
    }

    private void copyFreeReturnItem(List<FreeReturnItemDO> copyFreeReturnItems, OrderDO copyOrder, TableBasicDTO tableBasicDTO, OrderItemDO copyItemDO, List<FreeReturnItemDO> freeReturnItems) {
        //没有赠菜
        if (CollectionUtils.isEmpty(freeReturnItems)) {
            return;
        }
        List<Long> copyFreeReturnItemGuids = dynamicHelper.generateGuids(GuidKeyConstant.FREE_RETURN_ITEM, freeReturnItems.size());
        for (int k = 0; k < freeReturnItems.size(); k++) {
            FreeReturnItemDO freeReturnItemDO = freeReturnItems.get(k);
            FreeReturnItemDO copyFreeReturnItemDO = new FreeReturnItemDO();
            BeanUtil.copyProperties(freeReturnItemDO, copyFreeReturnItemDO, "guid", "orderGuid", "orderItemGuid");
            copyFreeReturnItemDO.setGuid(copyFreeReturnItemGuids.get(k))
                    .setOrderGuid(copyOrder.getGuid())
                    .setOrderItemGuid(copyItemDO.getGuid())
                    .setStoreGuid(tableBasicDTO.getStoreGuid())
                    .setStoreName(tableBasicDTO.getStoreName());
            copyFreeReturnItems.add(copyFreeReturnItemDO);
        }
    }

    private void copyItemAttr(List<ItemAttrDO> copyItemAttrDOs, OrderDO copyOrder, TableBasicDTO tableBasicDTO, OrderItemDO copyItemDO, List<ItemAttrDO> attrDOList) {
        //没有商品属性
        if (CollectionUtils.isEmpty(attrDOList)) {
            return;
        }
        List<Long> copyAttrGuids = dynamicHelper.generateGuids(GuidKeyConstant.ITEM_ATTR_GUID, attrDOList.size());
        for (int k = 0; k < attrDOList.size(); k++) {
            ItemAttrDO itemAttrDO = attrDOList.get(k);
            ItemAttrDO copyItemAttrDO = new ItemAttrDO();
            BeanUtil.copyProperties(itemAttrDO, copyItemAttrDO, "guid", "orderGuid", "orderItemGuid");
            copyItemAttrDO.setGuid(copyAttrGuids.get(k))
                    .setOrderGuid(copyOrder.getGuid())
                    .setOrderItemGuid(copyItemDO.getGuid())
                    .setStoreGuid(tableBasicDTO.getStoreGuid())
                    .setStoreName(tableBasicDTO.getStoreName());
            copyItemAttrDOs.add(copyItemAttrDO);
        }
    }

    private void copyTransaction(List<TransactionRecordDO> copyTransactionRecordDOs, OrderDO copyOrder, TableBasicDTO tableBasicDTO, List<TransactionRecordDO> transactionRecords) {
        if (CollectionUtils.isEmpty(transactionRecords)) {
            return;
        }
        List<Long> copyTransactionRecordGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_TRANSACTION_RECORD, transactionRecords.size());
        for (int k = 0; k < transactionRecords.size(); k++) {
            TransactionRecordDO transactionRecordDO = transactionRecords.get(k);
            TransactionRecordDO copyTransactionRecordDO = new TransactionRecordDO();
            BeanUtil.copyProperties(transactionRecordDO, copyTransactionRecordDO, "guid", "orderGuid");
            copyTransactionRecordDO.setGuid(copyTransactionRecordGuids.get(k))
                    .setOrderGuid(copyOrder.getGuid())
                    .setStoreGuid(tableBasicDTO.getStoreGuid())
                    .setStoreName(tableBasicDTO.getStoreName());
            copyTransactionRecordDOs.add(copyTransactionRecordDO);
        }
    }

    private void copyAppendFee(List<AppendFeeDO> appendFees, List<AppendFeeDO> copyAppendFees, OrderDO copyOrder, TableBasicDTO tableBasicDTO) {
        if (CollectionUtils.isEmpty(appendFees)) {
            return;
        }
        List<Long> copyAppendFeeGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_APPEND_FEE, appendFees.size());
        for (int k = 0; k < appendFees.size(); k++) {
            AppendFeeDO appendFeeDO = appendFees.get(k);
            AppendFeeDO copyAppendFeeDO = new AppendFeeDO();
            BeanUtil.copyProperties(appendFeeDO, copyAppendFeeDO, "guid", "orderGuid");
            copyAppendFeeDO.setGuid(copyAppendFeeGuids.get(k))
                    .setOrderGuid(copyOrder.getGuid())
                    .setStoreGuid(tableBasicDTO.getStoreGuid())
                    .setStoreName(tableBasicDTO.getStoreName())
                    .setAreaGuid(tableBasicDTO.getAreaGuid());
            copyAppendFees.add(copyAppendFeeDO);
        }
    }

    private void copyDiscount(List<DiscountDO> discountDOS, List<DiscountDO> copyDiscounts, OrderDO copyOrder, TableBasicDTO tableBasicDTO) {
        if (CollectionUtils.isEmpty(discountDOS)) {
            return;
        }
        List<Long> copyDiscountGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_DISCOUNT, discountDOS.size());
        for (int k = 0; k < discountDOS.size(); k++) {
            DiscountDO discountDO = discountDOS.get(k);
            DiscountDO copyDiscountDO = new DiscountDO();
            BeanUtil.copyProperties(discountDO, copyDiscountDO, "guid", "orderGuid");
            copyDiscountDO.setGuid(copyDiscountGuids.get(k))
                    .setOrderGuid(copyOrder.getGuid())
                    .setStoreGuid(tableBasicDTO.getStoreGuid())
                    .setStoreName(tableBasicDTO.getStoreName());
            copyDiscounts.add(copyDiscountDO);
        }
    }

    /**
     * 读取Excel中订单信息
     *
     * @param fileUrl Excel下载地址
     * @return 订单信息
     */
    private List<OrderUploadDTO> readOrderExcel(String fileUrl) {
        List<OrderUploadDTO> orderUploadDTOS;
        BufferedInputStream inputStream = null;
        try {
            // 统一资源
            URL url = new URL(fileUrl);
            // 连接类的父类，抽象类
            URLConnection urlConnection = url.openConnection();
            // http的连接类
            HttpURLConnection httpURLConnection = (HttpURLConnection) urlConnection;
            //设置超时
            httpURLConnection.setDoInput(true);
            httpURLConnection.setReadTimeout(30 * 1000);
            httpURLConnection.setConnectTimeout(10 * 1000);
            httpURLConnection.setUseCaches(false);
            // 设置字符编码
            httpURLConnection.setRequestProperty("Charset", "UTF-8");
            httpURLConnection.setRequestProperty("Content-Type", "application/octet-stream");
            // 维持长连接
            httpURLConnection.setRequestProperty("Connection", "Keep-Alive");
            // 打开到此 URL引用的资源的通信链接（如果尚未建立这样的连接）。
            httpURLConnection.connect();
            // 建立链接从请求中获取数据
            URLConnection connection = url.openConnection();
            inputStream = new BufferedInputStream(connection.getInputStream());
            orderUploadDTOS = ExcelUtils.read(inputStream, OrderUploadDTO.class);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException("文件读取失败");
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return orderUploadDTOS;
    }

    @Override
    public Integer queryOrderNumForStoreGuid(String storeGuid) {
        QueryWrapper<OrderDO> wrapper = new QueryWrapper<>();
        wrapper.eq("store_guid", storeGuid);
        wrapper.last("limit 1");
        OrderDO orderDO = orderMapper.selectOne(wrapper);
        if (orderDO != null) {
            return 1;
        }
        return 0;
    }


    @Override
    public LocalDateTime queryfristorderForStoreGuid(String storeGuid) {
        return orderMapper.queryfristorderForStoreGuid(storeGuid);
    }

    @Override
    public OrderDTO findByOrderNoAndStoreGuid(String orderNo, String storeGuid) {
        OrderDO order = orderService.getOne(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getOrderNo, orderNo)
                .eq(OrderDO::getStoreGuid, storeGuid)
        );
        return orderTransform.orderDO2DTO(order);
    }

    /***
     * 根据订单编号查询订单信息
     * @param query 订单编号列表和店铺guid
     * @return 多个订单详情
     */
    @Override
    public List<OrderDTO> listByOrderNoAndStoreGuid(SingleDataDTO query) {
        // 参数校验
        if (ObjectUtils.isEmpty(query)
                || StringUtils.isEmpty(query.getStoreGuid())
                || CollectionUtils.isEmpty(query.getDatas())) {
            log.warn("[获取多个订单详情]参数为空, query={}", JacksonUtils.writeValueAsString(query));
            return Collections.emptyList();
        }

        // 查询数据库
        List<OrderDO> orderDOList = orderService.list(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getStoreGuid, query.getStoreGuid())
                .in(OrderDO::getOrderNo, query.getDatas()));

        // DO转DTO
        return orderTransform.orderDOList2DTOList(orderDOList);
    }

    /**
     * 获取多个订单详情
     *
     * @param query 订单guid列表
     * @return 多个订单详情
     */
    @Override
    public List<OrderDTO> listByOrderGuid(SingleDataDTO query) {
        if (ObjectUtils.isEmpty(query) || org.springframework.util.CollectionUtils.isEmpty(query.getDatas())) {
            log.warn("[获取多个订单详情]参数为空");
            return new ArrayList<>();
        }
        List<OrderDO> orderDOList = orderService.list(new LambdaQueryWrapper<OrderDO>()
                .in(OrderDO::getGuid, query.getDatas()));
        return orderTransform.orderDOList2DTOList(orderDOList);
    }

    @Override
    public OrderTableDTO findTableByOrderGuid(String orderGuid) {
        OrderTableDTO orderTableDTO = new OrderTableDTO();
        List<OrderTableGuestDTO> orderTableGuestDTOS = new ArrayList<>();

        OrderDTO orderDTO = findByOrderGuid(orderGuid);

        org.springframework.beans.BeanUtils.copyProperties(orderDTO, orderTableDTO);

        if (UpperStateEnum.COMBINE_ORDER_STATE.contains(orderTableDTO.getUpperState())) {
            //并台处理
            if (orderDTO.getUpperState() == UpperStateEnum.SUB.getCode()) {
                //获取主单
                OrderDO orderDO = orderMapper.selectOne(new LambdaQueryWrapper<OrderDO>()
                        .eq(OrderDO::getGuid, orderDTO.getMainOrderGuid()));
                log.info("获取主单：{}", orderDO);
                setMainOrder(orderDO.getDiningTableGuid(), orderDO.getGuestCount(), orderTableGuestDTOS, orderTableDTO);
                addTableGuidList(orderDTO.getMainOrderGuid(), orderTableGuestDTOS);
            } else {
                setMainOrder(orderDTO.getDiningTableGuid(), orderDTO.getGuestCount(), orderTableGuestDTOS, orderTableDTO);
                addTableGuidList(orderDTO.getGuid(), orderTableGuestDTOS);
            }
            orderTableDTO.setOrderTableType(2);
        } else {
            //校验是否联台
            OrderExtendsDO orderExtendsDO = orderExtendsMapper.selectOne(new LambdaQueryWrapper<OrderExtendsDO>()
                    .eq(OrderExtendsDO::getGuid, orderDTO.getGuid()));
            if (!ObjectUtils.isEmpty(orderExtendsDO)
                    && !StringUtils.isEmpty(orderExtendsDO.getAssociatedTableGuids())) {
                List<String> associatedTableGuids = JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableGuids());
                List<String> tableGuidList = new ArrayList<>(associatedTableGuids);
                orderTableDTO.setOrderTableType(1);
                orderTableDTO.setTableGuidList(tableGuidList);
                orderTableDTO.setTableGuid(orderDTO.getDiningTableGuid());
                orderTableDTO.setGuestCount(orderDTO.getGuestCount());
            } else {
                orderTableDTO.setTableGuidList(Collections.singletonList(orderDTO.getDiningTableGuid()));
                orderTableDTO.setGuestCount(orderDTO.getGuestCount());
                orderTableDTO.setTableGuid(orderDTO.getDiningTableGuid());
                orderTableDTO.setOrderTableType(0);
            }
        }
        orderTableDTO.setOrderTableGuestDTOS(orderTableGuestDTOS);
        return orderTableDTO;
    }

    private static void setMainOrder(String tableGuid, Integer guestCount, List<OrderTableGuestDTO> orderTableGuestDTOS, OrderTableDTO orderTableDTO) {
        OrderTableGuestDTO orderTableGuestDTO = new OrderTableGuestDTO();
        orderTableGuestDTO.setTableGuid(tableGuid);
        orderTableGuestDTO.setGuestCount(guestCount);
        orderTableGuestDTOS.add(orderTableGuestDTO);
        orderTableDTO.setTableGuid(tableGuid);
    }

    private void addTableGuidList(String mainOrderGuid, List<OrderTableGuestDTO> tableGuidList) {
        //获取所有子单
        List<OrderDO> orderList = orderMapper.selectList(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getMainOrderGuid, mainOrderGuid)
        );

        log.info("获取到子单：{}", orderList);

        for (OrderDO orderDO : orderList) {
            OrderTableGuestDTO orderTableGuestDTO = new OrderTableGuestDTO();
            orderTableGuestDTO.setTableGuid(orderDO.getDiningTableGuid());
            orderTableGuestDTO.setGuestCount(orderDO.getGuestCount());
            tableGuidList.add(orderTableGuestDTO);
        }
    }
}
