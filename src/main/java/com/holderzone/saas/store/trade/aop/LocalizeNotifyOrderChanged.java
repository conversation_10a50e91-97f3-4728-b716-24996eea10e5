package com.holderzone.saas.store.trade.aop;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LocalizeNotifyOrderChanged
 * @date 2019/11/18 10:02
 * @description //TODO
 * @program IdeaProjects
 */
//@Aspect
//@Component
//@Order(10)
//public class LocalizeNotifyOrderChanged {
//
//    @Autowired
//    private MessageService messageService;
//
//
////    @Pointcut("@annotation(com.holderzone.saas.store.trade.anno.LocalizeRequireNotifyOrderGuids)")
////    public void notyfiyAndroidOrderChanged() {
////
////    }
//
////    @Around("notyfiyAndroidOrderChanged()")
////    public Object notifyOrderGuids(ProceedingJoinPoint point){
////        Object parameter = point.getArgs()[0];
////        String orderGuid = OrderUtil.getOrderGuidFromParm(parameter);
////        if(orderGuid!=null){
////            LocalizeSynchronizeContext.putOrderGuid(orderGuid);
////        }
////        try{
////            return point.proceed();
////        } catch (Throwable throwable) {
////            throwable.printStackTrace();
////            throw new BusinessException(throwable.getMessage());
////        } finally {
////            messageService.notifyOrderChange(BaseDTO.class.cast(parameter));
////        }
////    }
//
//}