package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hst_member_operate_record")
public class MemberOperateRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "操作guid")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "设备类型 BaseDeviceTypeEnum 3一体机 4pos机")
    private Integer deviceType;

    @ApiModelProperty(value = "操作模块 1正餐 2快餐 3会员")
    private Integer moduleType;

    @ApiModelProperty(value = "登录方式 0-扫码 1-手机号录入")
    private Integer loginType;

    @ApiModelProperty(value = "交易模式(0：正餐，1：快餐)")
    private Integer tradeMode;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "操作员guid")
    private String operatorGuid;

    @ApiModelProperty(value = "操作员名称")
    private String operatorName;

    @ApiModelProperty(value = "会员手机号")
    private String phoneNum;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

}
