package com.holderzone.saas.store.trade.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountBO
 * @date 2019/02/23 16:47
 * @description
 * @program holder-saas-store-trade
 */
@Data
public class SystemDiscountBO {

    @ApiModelProperty(value = "折扣临界值")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "省零方式,1：四舍五入，2：只舍不入，3：只入不舍")
    private Integer roundType;

    @ApiModelProperty(value = "保留到，0:保留到元，1:保留到角")
    private Integer scale;

    @ApiModelProperty(value = "0:启用，1:禁用")
    private Integer state;

}
