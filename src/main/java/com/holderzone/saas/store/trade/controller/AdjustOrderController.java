package com.holderzone.saas.store.trade.controller;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderReqDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.*;
import com.holderzone.saas.store.trade.builder.AdjustOrderBizBuilder;
import com.holderzone.saas.store.trade.entity.bo.AdjustOrderBO;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDetailsDO;
import com.holderzone.saas.store.trade.manager.AdjustOrderManager;
import com.holderzone.saas.store.trade.repository.interfaces.AdjustOrderDetailsService;
import com.holderzone.saas.store.trade.repository.interfaces.AdjustOrderService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.transform.AdjustOrderTransform;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/adjust_order")
@Api(tags = "调整订单接口")
@AllArgsConstructor
public class AdjustOrderController {

    private final AdjustOrderService adjustOrderService;

    private final AdjustOrderDetailsService adjustOrderDetailsService;

    private final AdjustOrderManager adjustOrderManager;

    private final OrderService orderService;

    private final OrderItemService orderItemService;

    private static AdjustOrderTransform adjustOrderTransform = AdjustOrderTransform.INSTANCE;

    /**
     * 调整单-正餐/快餐订单列表
     * 正餐、快餐展示近30天已结账的订单数据
     *
     * @param query 关键字和门店guid
     * @return 正餐/快餐订单信息列表
     */
    @ApiOperation(value = "调整单-正餐/快餐订单列表")
    @PostMapping("/page_dine_and_fast_order")
    public Page<AdjustByDineAndFastOrderRespDTO> pageDineAndFastOrder(@RequestBody AdjustByOrderListQuery query) {
        log.info("调整单-正餐/快餐订单列表 入参 query={}", JacksonUtils.writeValueAsString(query));
        return orderService.pageDineAndFastOrder(query);
    }

    /**
     * 调整单-查询订单商品
     * 标记商品是否调整过
     *
     * @param query 订单guid，门店，交易模式
     * @return 订单商品信息列表
     */
    @ApiOperation(value = "调整单-查询订单商品")
    @PostMapping("/list_order_item")
    public AdjustByOrderRespDTO listOrderItem(@RequestBody AdjustByOrderItemQuery query) {
        log.info("调整单-查询订单商品 入参 query={}", JacksonUtils.writeValueAsString(query));
        return orderItemService.listOrderItem(query);
    }


    /**
     * 调整单-分页列表
     *
     * @param queryDTO 门店guid
     * @return 调整单列表
     */
    @ApiOperation(value = "调整单-分页列表")
    @PostMapping("/page_adjust_order")
    public Page<AdjustOrderRespDTO> pageAdjustOrder(@RequestBody AdjustOrderQueryDTO queryDTO) {
        log.info("调整单列表-分页 入参 basePageDTO={}", JacksonUtils.writeValueAsString(queryDTO));
        return adjustOrderService.pageAdjustOrder(queryDTO);
    }

    @ApiOperation(value = "调整单-查询调整单详情")
    @PostMapping("/query")
    public AdjustOrderDetailRespDTO queryAdjustOrder(@RequestBody AdjustOrderQueryDTO queryDTO) {
        return adjustOrderService.getSingleOrderDetail(queryDTO);
    }

    @ApiOperation(value = "调整单-创建调整单")
    @PostMapping("/create")
    public Long create(@RequestBody @Valid AdjustOrderReqDTO reqDTO) {
        log.info("调整单-创建调整单 入参 req={}", JacksonUtils.writeValueAsString(reqDTO));
        AdjustOrderBO biz = AdjustOrderBizBuilder.build(reqDTO);
        log.info("调整单-创建调整单 解析结构返回={}", JacksonUtils.writeValueAsString(biz));
        return adjustOrderManager.create(biz);
    }

    @ApiOperation(value = "调整单-根据订单明细guid查询调整明细")
    @PostMapping("/list/byOrderGuids")
    public List<AdjustByOrderItemRespDTO> listByOrderItemGuids(@RequestBody AdjustOrderQueryDTO queryDTO) {
        List<AdjustOrderDetailsDO> details = adjustOrderDetailsService.listByOrderItemGuids(queryDTO.getOrderItemGuids());
        if (CollectionUtils.isNotEmpty(details)) {
            return adjustOrderTransform.adjustOrderDetailsDOs2AdjustByOrderItemRespDTOs(details);
        }
        return Lists.newArrayList();
    }

    @ApiOperation(value = "根据订单guids查询对应是否调整")
    @PostMapping("/orderGuids")
    public List<Long> listByOrderGuids(@RequestBody AdjustOrderQueryDTO queryDTO) {
        return adjustOrderService.listByOrderGuids(queryDTO.getOrderGuids());
    }
}
