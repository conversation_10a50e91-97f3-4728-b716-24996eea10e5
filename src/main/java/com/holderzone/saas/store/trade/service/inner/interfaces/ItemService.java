package com.holderzone.saas.store.trade.service.inner.interfaces;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import org.springframework.data.util.Pair;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemService
 * @date 2019/12/12 11:43
 * @description //TODO
 * @program IdeaProjects
 */
public interface ItemService {
    Pair<List<DineInItemDTO>,List<ReturnItemDTO>> getItemDTOSAndReturnItemDTOS(String orderGuid);
}