package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.response.Result;
import com.holderzone.saas.store.dto.weixin.req.WxOrderNumberQuery;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxClientService
 * @date 2018/09/30 11:41
 * @description 微信调用
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-aggregation-weixin", fallbackFactory = WxClientService.FallBack.class)
public interface WxClientService {

    @PostMapping("/wx_store_trade_order/finish_order")
    void finishOrder(@RequestBody String orderGuid);

    /**
     * 删除缓存的微信点餐人数
     *
     * @param query 桌位信息
     * @return Boolean
     */
    @ApiOperation("删除缓存的微信点餐人数")
    @PostMapping("/wx-store-menu/delete_order_number")
    Result<Boolean> deleteOrderNumber(@RequestBody WxOrderNumberQuery query);

    @Component
    class FallBack implements FallbackFactory<WxClientService> {
        private static final Logger logger = LoggerFactory.getLogger(WxClientService.FallBack.class);

        @Override
        public WxClientService create(Throwable throwable) {
            return new WxClientService() {

                @Override
                public void finishOrder(String orderGuid) {
                    logger.error("微信订单完成调用异常，e={}", throwable.getMessage());
                    throw new ParameterException("微信订单完成调用异常");
                }

                @Override
                public Result<Boolean> deleteOrderNumber(WxOrderNumberQuery query) {
                    logger.error("删除缓存的微信点餐人数异常，e={}", throwable.getMessage());
                    return Result.buildSuccessResult(Boolean.FALSE);
                }
            };
        }
    }

}
