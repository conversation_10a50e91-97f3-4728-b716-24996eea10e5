package com.holderzone.saas.store.trade.helper;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.trade.bo.TransferItemBO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.repository.feign.BusinessMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @className MessagePushHelper
 * @date 2018/10/25 15:28
 * @description
 * @program holder-saas-store-trade
 */
@Component
@Slf4j
public class MessagePushHelper {

    private final BusinessMessageService businessMessageService;

    @Autowired
    public MessagePushHelper(BusinessMessageService businessMessageService) {
        this.businessMessageService = businessMessageService;
    }

    public void recoveryMsgPush() {
        BusinessMessageDTO messageDTO = BusinessMessageDTO.builder()
                .subject("反结账老板助手通知")
                .messageType(BusinessMsgTypeEnum.RECOVERY.getId())
                .detailMessageType(BusinessMsgTypeEnum.RECOVERY.getId())
                .content(StringUtils.EMPTY)
                .platform("2")
                //老板助手接受企业下所有门店，跟安卓约定storeGuid写成messageType
                .storeGuid(UserContextUtils.getStoreGuid())
                .storeName(UserContextUtils.getStoreName())
                .build();
        businessMessageService.msg(messageDTO);
    }

    public void transferItemMsgPush(TransferItemBO transferItemBO) {
        OrderDO newOrderDO = transferItemBO.getNewOrderDO();
        OrderDO oldOrderDO = transferItemBO.getOldOrderDO();
        List<String> tableGuidList = new ArrayList<>();
        tableGuidList.add(newOrderDO.getDiningTableGuid());
        tableGuidList.add(oldOrderDO.getDiningTableGuid());

        BusinessMessageDTO messageDTO = BusinessMessageDTO.builder()
                .subject("桌位状态变化通知")
                .messageType(BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId())
                .detailMessageType(BusinessMsgTypeEnum.TABLE_CHANGED.getId())
                .content(JacksonUtils.writeValueAsString(tableGuidList))
                .platform("2")
                .storeGuid(newOrderDO.getStoreGuid())
                .storeName(newOrderDO.getStoreName())
                .build();
        businessMessageService.msg(messageDTO);
        log.info("[转菜单桌台刷新推送]完成");
    }


}
