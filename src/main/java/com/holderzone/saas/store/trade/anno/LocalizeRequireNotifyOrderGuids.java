package com.holderzone.saas.store.trade.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LocalizeRequireNotifyOrderGuids
 * @date 2019/11/18 10:00
 * @description //本地化需要提醒安卓端接口
 * @program IdeaProjects
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LocalizeRequireNotifyOrderGuids {



}
