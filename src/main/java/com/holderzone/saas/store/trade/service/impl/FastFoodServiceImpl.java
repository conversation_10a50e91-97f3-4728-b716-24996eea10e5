package com.holderzone.saas.store.trade.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.order.OrderDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.AutoMarkReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.request.item.UpdateOrderItemInfoDTO;
import com.holderzone.saas.store.dto.order.response.AutoMarkRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.order.AppendFeeTypeEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;
import com.holderzone.saas.store.trade.entity.enums.RecoveryTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.repository.feign.BusinessClientService;
import com.holderzone.saas.store.trade.repository.feign.StoreClientService;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.*;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className
 * @date 2018/09/04 16:10
 * @description //TODO
 * @program holder-saas-store-order
 */
@Slf4j
@Service
public class FastFoodServiceImpl implements FastFoodService {

    private final DineInItemService dineInItemService;

    private final RedisHelper redisHelper;

    private final OrderService orderService;

    private final DineInService dineInService;

    private final OrderItemService orderItemService;

    private final OrderItemRecordService orderItemRecordService;

    private final ItemAttrService itemAttrService;

    private final FreeReturnItemService freeReturnItemService;

    private final DynamicHelper dynamicHelper;

    private final StoreClientService storeClientService;

    private final AppendFeeService appendFeeService;

    @Resource
    private CalculateService calculateService;

    @Resource
    private BusinessClientService businessClientService;

    @Resource
    private OrderItemChangesService orderItemChangesService;

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    @Autowired
    public FastFoodServiceImpl(DineInItemService dineInItemService, RedisHelper redisHelper, OrderService
            orderService, DineInService dineInService, OrderItemService orderItemService, ItemAttrService itemAttrService,
                               FreeReturnItemService freeReturnItemService, OrderItemRecordService orderItemRecordService,
                               DynamicHelper dynamicHelper, StoreClientService storeClientService,
                               AppendFeeService appendFeeService) {
        this.dineInItemService = dineInItemService;
        this.redisHelper = redisHelper;
        this.orderService = orderService;
        this.dineInService = dineInService;
        this.orderItemService = orderItemService;
        this.orderItemRecordService = orderItemRecordService;
        this.itemAttrService = itemAttrService;
        this.freeReturnItemService = freeReturnItemService;
        this.dynamicHelper = dynamicHelper;
        this.storeClientService = storeClientService;
        this.appendFeeService = appendFeeService;
    }

    @Override
    @Transactional
    public EstimateItemRespDTO addItem(CreateFastFoodReqDTO createFastFoodReqDTO) {
        String orderGuid = createFastFoodReqDTO.getGuid();
        CreateDineInOrderReqDTO createDineInOrderReqDTO = orderTransform.createFastFoodReqDTO2CreateDineInOrderReqDTO
                (createFastFoodReqDTO);
        if (StringUtils.isEmpty(orderGuid)) {
            OrderDO orderDO = createFastFoodOrder(createFastFoodReqDTO);
            createDineInOrderReqDTO.setGuid(String.valueOf(orderDO.getGuid()));
            orderGuid = String.valueOf(orderDO.getGuid());
        } else {
            updateFastFoodOrder(createFastFoodReqDTO);
            deleteItems(orderGuid);
        }
        if (CollectionUtil.isNotEmpty(createDineInOrderReqDTO.getDineInItemDTOS())) {
            EstimateItemRespDTO estimateItemRespDTO = dineInItemService.addItems(createDineInOrderReqDTO, Boolean.TRUE);
            if (Objects.nonNull(estimateItemRespDTO.getEstimate()) && Boolean.TRUE.equals(estimateItemRespDTO.getEstimate())) {
                return estimateItemRespDTO;
            }
        }
        EstimateItemRespDTO successEstimate = new EstimateItemRespDTO();
        successEstimate.setOrderGuid(orderGuid);
        successEstimate.setResult(Boolean.TRUE);
        successEstimate.setEstimate(Boolean.FALSE);
        return successEstimate;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EstimateItemRespDTO createAppletOrder(CreateFastFoodReqDTO createFastFoodReqDTO) {
        // add_item
        EstimateItemRespDTO estimateItemRespDTO = addItem(createFastFoodReqDTO);
        log.info("createFastFoodReqDTO:{}", JacksonUtils.writeValueAsString(estimateItemRespDTO));
        if (Boolean.FALSE.equals(estimateItemRespDTO.getResult())) {
            return estimateItemRespDTO;
        }
        // 小程序及H5下单快餐 保存优惠使用信息
        saveAppletOrderDiscount(createFastFoodReqDTO, estimateItemRespDTO);
        return estimateItemRespDTO;
    }


    /**
     * 小程序及H5下单快餐 保存优惠使用信息
     */
    private void saveAppletOrderDiscount(CreateFastFoodReqDTO createFastFoodReqDTO, EstimateItemRespDTO estimateItemRespDTO) {
        if (!(BaseDeviceTypeEnum.isApplet(createFastFoodReqDTO.getDeviceType()) ||
                Lists.newArrayList(BaseDeviceTypeEnum.WECHAT.getCode()).contains(createFastFoodReqDTO.getDeviceType()))) {
            return;
        }
        // 微信小程序/微信公众号/支付宝小程序下单
        // 下单成功，计算相关数据入库
        createFastFoodReqDTO.setGuid(estimateItemRespDTO.getOrderGuid());
        calculateService.saveDiscountInfo(createFastFoodReqDTO);
    }

    @Override
    public void updateFastFoodOrder(CreateFastFoodReqDTO createFastFoodReqDTO) {
        OrderDO orderDOInDb = orderService.getById(createFastFoodReqDTO.getGuid());
        OrderDO orderDO = new OrderDO();
        orderDO.setGuid(Long.valueOf(createFastFoodReqDTO.getGuid()));
        orderDO.setRemark(createFastFoodReqDTO.getRemark());
        orderDO.setGuestCount(createFastFoodReqDTO.getGuestCount());
        if (createFastFoodReqDTO.getAutoMark() == 0) {
            orderDO.setMark(createFastFoodReqDTO.getMark());
        }
        //反结账新单每次更新成原单订单金额(快餐反结账新单去结账时回传全量未下单部分，删除时不删除已下单的菜品)
        BigDecimal orderFee = BigDecimal.ZERO;
        if (orderDOInDb.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
            DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(String.valueOf(orderDOInDb.getGuid()));
            List<DineInItemDTO> dineInItemDTOS = orderDetail.getDineInItemDTOS();
            List<DineInItemDTO> isPayDineInItemDTOList = dineInItemDTOS.stream()
                    .filter(dineInItemDTO -> dineInItemDTO.getIsPay().equals(1))
                    .collect(Collectors.toList());
            orderFee = AmountCalculationUtil.getOrderFee(isPayDineInItemDTOList, BigDecimal.ZERO);
        }
        BigDecimal appendFee = appendFeeService.updateAppendFee(createFastFoodReqDTO.getGuid(), createFastFoodReqDTO.getGuestCount());
        orderDO.setAppendFee(appendFee);
        orderFee = orderFee.add(appendFee);
        orderDO.setOrderFee(orderFee);
        orderService.updateById(orderDO);
    }

    @NotNull
    private static BigDecimal getAppendFee(CreateFastFoodReqDTO createFastFoodReqDTO) {
        BigDecimal appendFee = BigDecimal.ZERO;
        if (!CollectionUtil.isEmpty(createFastFoodReqDTO.getSurchargeLinkList())) {
            for (SurchargeLinkDTO surchargeLinkDTO : createFastFoodReqDTO.getSurchargeLinkList()) {
                BigDecimal appendAmount = BigDecimal.ZERO;
                //按桌
                if (surchargeLinkDTO.getType() == AppendFeeTypeEnum.BY_TABLE.getCode()) {
                    appendAmount = surchargeLinkDTO.getAmount();
                }
                //按人
                if (surchargeLinkDTO.getType() == AppendFeeTypeEnum.BY_NUM.getCode()) {
                    appendAmount = surchargeLinkDTO.getAmount().multiply(new BigDecimal(createFastFoodReqDTO.getGuestCount()));
                }
                appendFee = appendFee.add(appendAmount);
            }
        }
        return appendFee;
    }

    @Override
    public void deleteItems(String orderGuid) {
//        OrderDO orderDO = new OrderDO();
//        orderDO.setGuid(Long.valueOf(orderGuid));
//        //反结账新单每次更新成原单订单金额(快餐反结账新单去结账时回传全量未下单部分，删除时不删除已下单的菜品)
//        if (orderDOInDb.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
//            DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(String.valueOf(orderDOInDb.getGuid()));
//            List<DineInItemDTO> dineInItemDTOS = orderDetail.getDineInItemDTOS();
//            List<DineInItemDTO> isPayDineInItemDTOList = dineInItemDTOS.stream()
//                    .filter(dineInItemDTO -> dineInItemDTO.getIsPay().equals(1))
//                    .collect(Collectors.toList());
//            BigDecimal orderFee = AmountCalculationUtil.getOrderFee(isPayDineInItemDTOList, BigDecimal.ZERO);
//            orderDO.setOrderFee(orderFee);
//        } else {
//            orderDO.setOrderFee(BigDecimal.ZERO);
//        }
//        orderService.updateByIdWithDeleteCache(orderDO);
        List<OrderItemDO> orderItemDOS = orderItemService.listByOrderGuidWithOutIsPay(Long.valueOf(orderGuid));
        Map<Long, OrderItemDO> itemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
        ArrayList<Long> orderItemGuids = new ArrayList<>(itemDOMap.keySet());
        if (CollectionUtil.isNotEmpty(orderItemGuids)) {
            orderItemService.removeByIdsWithDeleteCache(orderItemGuids, orderGuid);
            List<String> itemGuids = orderItemGuids.stream().map(String::valueOf).collect(Collectors.toList());
            orderItemRecordService.deleteByItemGuids(itemGuids);
            itemAttrService.deleteByOrderGuid(orderGuid);
            freeReturnItemService.removeByItemGuids(orderItemGuids);
            orderItemChangesService.removeByOrderGuidList(Lists.newArrayList(Long.valueOf(orderGuid)));
        }
    }

    @Override
    public void removeStoreAutoMark(String storeGuid) {
        // 查询门店当前时间的营业日
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        reqDTO.setStoreGuidList(Lists.newArrayList(storeGuid));
        reqDTO.setQueryDateTime(LocalDateTime.now());
        LocalDate businessDay = storeClientService.queryBusinessDay(reqDTO);
        String businessDayStr = DateTimeUtils.localDate2String(businessDay);
        redisHelper.removeMarkKey(storeGuid, businessDayStr);
    }

    @Override
    public void updateMemberConsumptionGuid(OrderDTO orderDTO) {
        if (StringUtils.isEmpty(orderDTO.getOrderGuid())) {
            log.error("orderGuid is empty");
            return;
        }
        OrderDO orderDO = new OrderDO();
        orderDO.setGuid(Long.valueOf(orderDTO.getOrderGuid()));
        orderDO.setMemberConsumptionGuid(orderDTO.getMemberConsumptionGuid());
        orderService.updateById(orderDO);
    }

    @Override
    public void updateOrderItemInfo(UpdateOrderItemInfoDTO orderItemInfoDTO) {
        List<DineInItemDTO> allItems = orderItemInfoDTO.getAllItems();
        Map<String, DineInItemDTO> itemDTOMap = allItems.stream()
                .collect(Collectors.toMap(DineInItemDTO::getItemGuid, Function.identity(), (v1, v2) -> v1));
        List<OrderItemDO> updateOrderItemDOList = orderItemService.listByOrderGuid(Long.valueOf(orderItemInfoDTO.getOrderGuid()));
        updateOrderItemDOList.forEach(orderItemDO -> {
            orderItemDO.setGmtCreate(null);
            orderItemDO.setGmtModified(null);
            DineInItemDTO itemDTO = itemDTOMap.get(orderItemDO.getItemGuid());
            if (!ObjectUtils.isEmpty(itemDTO)) {
                orderItemDO.setDiscountTotalPrice(itemDTO.getDiscountTotalPrice());
            }
        });
        orderItemService.updateBatchById(updateOrderItemDOList);
    }

    private OrderDO createFastFoodOrder(CreateFastFoodReqDTO createFastFoodReqDTO) {
        if (createFastFoodReqDTO.getDeviceType() == null) {
            throw new ParameterException("设备类型不能为空");
        }
        OrderDO orderDO = new OrderDO();
        orderDO.setRemark(createFastFoodReqDTO.getRemark());
        orderDO.setDiningTableGuid(createFastFoodReqDTO.getWeixinTablleGuid());
        orderDO.setDiningTableName(createFastFoodReqDTO.getWeixinTableCode());
        orderDO.setUserWxPublicOpenId(createFastFoodReqDTO.getUserWxPublicOpenId());
        // 查询门店营业日
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        reqDTO.setStoreGuidList(Lists.newArrayList(UserContextUtils.getStoreGuid()));
        reqDTO.setQueryDateTime(LocalDateTime.now());
        LocalDate businessDay = storeClientService.queryBusinessDay(reqDTO);
        // 快餐号牌
        orderDO.setMark(getFastFoodMark(createFastFoodReqDTO, businessDay));
        // 创建订单
        Long orderGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER);
        orderDO.setGuid(orderGuid);
        orderDO.setBusinessDay(businessDay);
        //===================== 新需求：为了区分订单号，订单号开头增加门店code =====================
        orderDO.setOrderNo(redisHelper.generateOrderNo(UserContextUtils.get().getStoreNo(), UserContextUtils.getStoreGuid()));
        orderDO.setGuestCount(createFastFoodReqDTO.getGuestCount());
        orderDO.setStoreGuid(UserContextUtils.getStoreGuid());
        orderDO.setStoreName(UserContextUtils.getStoreName());
        orderDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        orderDO.setCreateStaffName(UserContextUtils.getUserName());
        orderDO.setDeviceType(createFastFoodReqDTO.getDeviceType());
        orderDO.setState(StateEnum.READY.getCode());
        orderDO.setTradeMode(TradeModeEnum.FAST.getCode());
        orderDO.setRecoveryType(RecoveryTypeEnum.GENERAL.getCode());
        orderDO.setPrintPreBillNum(0);
        orderDO.setMemberIntegralStore(createFastFoodReqDTO.getMemberIntegralStore());
        orderDO.setMemberPhone(createFastFoodReqDTO.getMemberPhone());
        orderDO.setMemberGuid(createFastFoodReqDTO.getMemberGuid());
        orderDO.setMemberName(createFastFoodReqDTO.getMemberName());
        orderDO.setMemberCardGuid(createFastFoodReqDTO.getMemberCardGuid());
        // 根据前端传递的附加费信息保存记录入库
        BigDecimal appendFee = appendFeeService.calculateAppendFee(orderDO, createFastFoodReqDTO.getSurchargeLinkList());
        orderDO.setOrderFee(appendFee);
        orderDO.setAppendFee(appendFee);
        orderService.save(orderDO);
        return orderDO;
    }

    /**
     * 查询快餐自动号牌
     */
    private String getFastFoodMark(CreateFastFoodReqDTO createFastFoodReqDTO, LocalDate businessDay) {
        boolean autoMark = createFastFoodReqDTO.getAutoMark() != null && createFastFoodReqDTO.getAutoMark() == 1;
        if (!autoMark) {
            return createFastFoodReqDTO.getMark();
        }
        // 当前营业时间
        String businessDayStr = DateTimeUtils.localDate2String(businessDay);
        // 查询快餐自动号牌配置
        AutoMarkReqDTO query = new AutoMarkReqDTO();
        query.setStoreGuid(UserContextUtils.getStoreGuid());
        AutoMarkRespDTO autoMarkRespDTO = businessClientService.queryAutoMarkResp(query);
        if (Objects.isNull(autoMarkRespDTO)) {
            autoMarkRespDTO = new AutoMarkRespDTO();
            autoMarkRespDTO.setInitMark(Constant.FAST_FOOD_INTI_MARK);
        }
        String initMark = autoMarkRespDTO.getInitMark();
        String endMark = autoMarkRespDTO.getEndMark();

        String generateMark = initMark;
        long currentMarkValue = Long.parseLong(initMark);
        // 查询自动号牌设置
        if (Boolean.TRUE.equals(redisHelper.hasMarkKey(createFastFoodReqDTO.getStoreGuid(), businessDayStr))) {
            // 如果存在，则自增
            generateMark = redisHelper.generateMark(createFastFoodReqDTO.getStoreGuid(), businessDayStr);
            currentMarkValue = Long.parseLong(generateMark);
        } else {
            redisHelper.generateInitMark(createFastFoodReqDTO.getStoreGuid(), businessDayStr, initMark);
        }
        // 判断是否超过结束号牌
        if (StringUtils.isNotBlank(endMark)) {
            long endMarkValue = Long.parseLong(endMark);
            if (currentMarkValue > endMarkValue) {
                // 超过或等于结束号牌，重置为初始号牌
                redisHelper.removeMarkKey(createFastFoodReqDTO.getStoreGuid(), businessDayStr);
                redisHelper.generateInitMark(createFastFoodReqDTO.getStoreGuid(), businessDayStr, initMark);
                generateMark = initMark;
            }
        }
        String format = "%1$0" + initMark.length() + "d";
        return "#" + String.format(format, Long.valueOf(generateMark));
    }
}
