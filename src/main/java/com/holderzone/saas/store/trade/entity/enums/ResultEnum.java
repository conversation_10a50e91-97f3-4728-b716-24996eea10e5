package com.holderzone.saas.store.trade.entity.enums;

public enum ResultEnum {

    SAVE_SUCCESS(0,"保存成功"),
    SAVE_FAIL(1,"保存失败"),
    PAY_SUCCESS(2,"支付成功"),
    PAY_FAIL(3,"支付失败"),
    CHECKOUT_SUCCESS(4,"结账成功"),
    CHECKOUT_FAIL(5,"结账失败"),;

    private int code;
    private String desc;

    ResultEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (ResultEnum c : ResultEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
