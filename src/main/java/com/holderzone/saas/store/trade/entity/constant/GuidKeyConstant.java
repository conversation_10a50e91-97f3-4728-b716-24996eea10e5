package com.holderzone.saas.store.trade.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GuidKeyConstant
 * @date 2018/09/04 17:19
 * @description
 * @program holder-saas-store-trade
 */
public class GuidKeyConstant {

    public static final String HST_ORDER = "hst_order";

    public static final String HST_GROUPON = "hstGroupon";

    public static final String HST_APPEND_FEE = "hstAppendFee";

    public static final String RECOVERY_ID = "recoveryId";

    public static final String HST_ORDER_ITEM = "hstOrderItem";

    public static final String HST_DISCOUNT = "hstDiscount";

    public static final String ITEM_ATTR_GUID = "itemAttr";

    public static final String ITEM_CHANGE_GUID = "itemChange";

    public static final String FREE_RETURN_ITEM = "freeReturnItem";

    public static final String ORDER_ITEM_RECORD = "OrderItemRecord";

    public static final String HST_TRANSACTION_RECORD = "hstTransactionRecord";
    public static final String HST_MULTIPLE_TRANSACTION_RECORD = "HstMultipleTransactionRecord";

    public static final String RECOVERY = "recovery";

    public static final String HST_ORDER_ABNORMAL_RECORD = "hstOrderAbnormalRecord";

    public static final String HST_ORDER_WAITER = "hstOrderWaiter";

    public static final String HST_DEBT_UNIT = "hstDebtUnit";

    public static final String HST_DEBT_UNIT_RECORD = "hstDebtUnitRecord";

    public static final String HST_ORDER_REFUND_RECORD = "hstOrderRefundRecord";

    //调整订单记录表
    public static final String HST_ADJUST_ORDER_RECORD = "hst_adjust_order_record";

    //调整商品记录表
    public static final String HST_ADJUST_ITME_INFO = "hst_adjust_item_info";

    public static final String HST_PAD_ORDER = "hst_pad_order";

    public static final String HST_THIRD_ACTIVITY_RECORD = "hst_third_activity_record";

    public static final String HSM_THIRD_ACTIVITY_DETAILS = "hsm_third_activity_details";

    public static final String BUSINESS_MESSAGE_DTO = "BusinessMessageDTO";

    public static final String ADJUST_ORDER = "adjustOrder";

    public static final String ADJUST_ORDER_DETAILS = "adjustOrderDetails";

    public static final String GROUP_TRADE_DETAIL = "groupTradeDetail";

    public static final String RESERVE = "reserve";

}
