package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.trade.entity.domain.OrderExtendsDO;
import com.holderzone.saas.store.trade.mapper.OrderExtendsMapper;
import com.holderzone.saas.store.trade.repository.interfaces.OrderExtendsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 订单扩展 服务实现类
 * </p>
 */
@Service
@AllArgsConstructor
@Slf4j
public class OrderExtendsServiceImpl extends ServiceImpl<OrderExtendsMapper, OrderExtendsDO> implements OrderExtendsService {

    @Override
    public List<OrderExtendsDO> listByOrderGuidList(List<Long> subOrderGuidList) {
        return list(new LambdaQueryWrapper<OrderExtendsDO>()
                .in(OrderExtendsDO::getGuid, subOrderGuidList));
    }
}
