package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.TransactionRecordDO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 订单交易记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface TransactionRecordService extends IService<TransactionRecordDO> {

    List<TransactionRecordDO> listJhAndPreByOrderGuid(String orderGuid);

    List<TransactionRecordDO> listRefundByOrderGuid(String orderGuid);

    List<TransactionRecordDO> listGeneralByOrderGuid(String orderGuid);

    List<TransactionRecordDO> listByOrderGuid(String orderGuid);

    List<TransactionRecordDO> listByOrderGuids(List<Long> orderGuids);

    List<TransactionRecordDO> listByOrderGuid(Long orderGuid);

    void deleteByOrderGuids(List<String> orderGuids);

    void saveOrUpdateByGuid(String orderGuid, List<TransactionRecordDO> saveList);

    void updateRefundAmountByOrderGuid(String orderGuid, Integer paymentType, String paymentTypeName, BigDecimal refundAmount);

    void updateRefundAmountByGuid(Long guid, BigDecimal refundAmount);

    List<TransactionRecordDO> listJhByOrderGuid(String orderGuid);

    List<TransactionRecordDO> listJhByOrderGuidList(Collection<? extends Serializable> orderGuidList);

}
