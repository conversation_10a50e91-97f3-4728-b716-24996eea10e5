package com.holderzone.saas.store.trade.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeTypeEnum
 * @date 2018/09/04 17:52
 * @description 订单交易模式枚举
 * @program holder-saas-store-trade
 */
public enum TradeTypeEnum {

    GENERAL_IN(1, "正常支付转入"),
    MEMBER_IN(2, "会员充值转入"),
    PRE_IN(3, "预付金转入"),
    DEPOSIT_IN(4, "定金转入"),
    GENERAL_OUT(5, "正常支付退款"),
    REFUND_OUT(6, "退单退款"),
    MEMBER_OUT(7, "会员余额退款"),
    PRE_OUT(8, "预付金退款"),
    DEPOSIT_OUT(9, "定金退款"),
    CUSTOM_OUT(10, "自定义退款"),
    ;

    private int code;
    private String desc;

    TradeTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (TradeTypeEnum c : TradeTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
