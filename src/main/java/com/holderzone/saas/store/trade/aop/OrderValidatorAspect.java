package com.holderzone.saas.store.trade.aop;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.bo.UserDefinedCodeBO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import com.holderzone.saas.store.enums.common.ResultCodeEnum;
import com.holderzone.saas.store.trade.execption.OrderVersionNotMatchException;
import com.holderzone.saas.store.trade.service.DineInService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderValidatorAspect
 * @date 2019/09/09 17:55
 * @description .
 * @program IdeaProjects
 */
@Aspect
@Component
@Slf4j
@Order(1)
public class OrderValidatorAspect {

    @Autowired
    private DineInService dineInService;


    @Pointcut("@annotation(com.holderzone.saas.store.trade.anno.RequireTradePayLock)")
    public void whetherPay() {
        // nothing
    }


    private static final String LOCK_STR = "lockStr";

    private static final String DEVICE_ID = "deviceId";

//    private static final String FAST_FOOD = "fastFood";

    private static final String TABLE_GUID = "tableGuid";

    private static final String ORDER_GUID = "orderGuid";

    private String getOrderGuid(Object parameter) {
        String orderGuid = "";
        Class<?> aClass = parameter.getClass();
        for (Class<?> clazz = aClass; clazz != Object.class; clazz = clazz.getSuperclass()) {
            for (Field field : clazz.getDeclaredFields()) {
                if (field.getName().equals(ORDER_GUID)) {
                    try {
                        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), aClass);
                        Method readMethod = pd.getReadMethod();
                        orderGuid = (String) readMethod.invoke(parameter);
                    } catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return orderGuid;
    }

    /**
     * 校验结账乐观锁的切面
     *
     * @param point
     * @return
     */
    @Around("whetherPay()")
    public Object whetherPay(ProceedingJoinPoint point) {
        log.info("进入trade结账锁");
        Object parameter = point.getArgs()[0];
        Map<String, String> lockMap = getLockStr(parameter);
        String version = lockMap.get(LOCK_STR);
        String orderGuid = getOrderGuid(parameter);
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException("订单号不能为空");
        }
        String versionRedis = dineInService.getVersion(orderGuid);
        log.info("version={},versionRedis={},orderGuid={}", version, versionRedis, orderGuid);
        //如果返回为null，说明是快餐
        if (versionRedis != null && !version.equals(versionRedis)) {
            throw new OrderVersionNotMatchException(UserDefinedCodeBO.buildDefindResultJSONString(ResultCodeEnum
                    .ORDER_VERSION_EXCEPTION));
        }
        //查询出redis中的version
        try {
            log.info("结束trade结账锁,结账放行，orderGuid={}", orderGuid);
            return point.proceed();
        } catch (Throwable throwable) {
            log.error("Aspect校验业务锁异常 e:", throwable);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            throwable.printStackTrace(new PrintStream(baos));
            throw new BusinessException(throwable.getMessage());
            // throw new BusinessException(baos.toString());
        }
    }

    private Map<String, String> getLockStr(Object parameter) {
        String lockStr = null;
        String deviceId = null;
        Map<String, String> map = new HashMap<>();
        if (parameter instanceof BaseDTO) {
            deviceId = ((BaseDTO) parameter).getDeviceId();
        }
        Class<?> aClass = parameter.getClass();
        for (Class<?> clazz = aClass; clazz != Object.class; clazz = clazz.getSuperclass()) {
            for (Field field : clazz.getDeclaredFields()) {
                LockField lockField = field.getAnnotation(LockField.class);
                if (null != lockField) {
                    try {
                        // 暂时只支持一个字段锁定
                        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), aClass);
                        Method readMethod = pd.getReadMethod();
                        lockStr = readMethod.invoke(parameter) + "";
                        break;
                    } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
                        e.printStackTrace();
                    }
                } else if (field.getName().equals(TABLE_GUID)) {
                    try {
                        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), aClass);
                        Method readMethod = pd.getReadMethod();
                        lockStr = (String) readMethod.invoke(parameter);
                    } catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        map.put(LOCK_STR, lockStr);
        map.put(DEVICE_ID, deviceId);
        return map;
    }


    /*private boolean getFastFood(Object parameter) {
        boolean fastFood = false;
        Class<?> aClass = parameter.getClass();
        for (Class<?> clazz = aClass; clazz != Object.class; clazz = clazz.getSuperclass()) {
            for (Field field : clazz.getDeclaredFields()) {
                if (field.getName().equals(FAST_FOOD)) {
                    try {
                        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), aClass);
                        Method readMethod = pd.getReadMethod();
                        fastFood = (Boolean) readMethod.invoke(parameter);
                    } catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return fastFood;
    }*/
}