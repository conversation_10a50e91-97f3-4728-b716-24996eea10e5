package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityDetailsReqDTO;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityDetailsRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityStatisticsRespDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 第三方优惠活动
 */
@Component
@FeignClient(name = "holder-saas-member-terminal", fallbackFactory = ThirdActivityClientService.MemberFallback.class)
public interface ThirdActivityClientService {

    @ApiModelProperty("可用活动列表")
    @GetMapping("/hsm-third-activity/list")
    List<ThirdActivityRespDTO> list();

    /**
     * 批量保存第三方活动使用明细
     *
     * @param reqDTOList 第三方活动使用明细数组
     * @return 成功/失败
     */
    @ApiOperation(value = "批量保存第三方活动使用明细")
    @PostMapping("/hsm-third-activity/batch_save_third_activity_details")
    Boolean batchSaveThirdActivityInfo(@RequestBody List<ThirdActivityDetailsReqDTO> reqDTOList);

    @ApiOperation("通过活动guidList查询活动列表")
    @GetMapping("/hsm-third-activity/list/by_guid_list")
    List<ThirdActivityRespDTO> listByGuid(@RequestParam("guidList") List<String> guidList);

    @ApiOperation("通过活动guidList查询可用活动列表")
    @GetMapping("/hsm-third-activity/in_process_list/by_guid_list")
    List<ThirdActivityRespDTO> inProcessListByGuid(@RequestParam("guidList") List<String> guidList);

    @ApiOperation("查询订单使用活动明细")
    @GetMapping("/hsm-third-activity/detail/list_by_order")
    List<ThirdActivityDetailsRespDTO> detailByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    @ApiOperation("根据订单guid删除使用活动明细")
    @DeleteMapping("/hsm-third-activity/detail/by_order_guid/{orderGuid}")
    Boolean removeByOrderGuid(@PathVariable String orderGuid);

    /**
     * 查询对应订单第三方活动明细，按活动分组统计金额
     */
    @PostMapping("/hsm-third-activity/detail/group_by_third_name")
    List<ThirdActivityStatisticsRespDTO> selectThirdFeeGroupByThirdActivityGuid(@RequestBody SingleDataDTO singleDataDTO);

    @Slf4j
    @Component
    class MemberFallback implements FallbackFactory<ThirdActivityClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ThirdActivityClientService create(Throwable cause) {
            return new ThirdActivityClientService() {
                @Override
                public List<ThirdActivityRespDTO> list() {
                    log.error(HYSTRIX_PATTERN, "list", null, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Boolean batchSaveThirdActivityInfo(List<ThirdActivityDetailsReqDTO> reqDTOList) {
                    log.error(HYSTRIX_PATTERN, "batchSaveThirdActivityInfo", JacksonUtils.writeValueAsString(reqDTOList),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<ThirdActivityRespDTO> listByGuid(List<String> guidList) {
                    log.error(HYSTRIX_PATTERN, "listByGuid", guidList, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<ThirdActivityRespDTO> inProcessListByGuid(List<String> guidList) {
                    log.error(HYSTRIX_PATTERN, "inProcessListByGuid", guidList, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<ThirdActivityDetailsRespDTO> detailByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "detailByOrderGuid", orderGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Boolean removeByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "removeByOrderGuid", orderGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<ThirdActivityStatisticsRespDTO> selectThirdFeeGroupByThirdActivityGuid(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "selectThirdFeeGroupByThirdActivityGuid", JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}