package com.holderzone.saas.store.trade.service.converter;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.order.request.bill.BillAggPayReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.req.PadPayInfoReqDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import org.springframework.util.ObjectUtils;

import java.util.Objects;
import java.util.Optional;

import static com.holderzone.saas.store.constant.Constant.NUMBER_ZERO;
import static com.holderzone.saas.store.constant.Constant.TRUE;

/**
 * <AUTHOR>
 * @create 2023-06-05
 * @description
 */
public class OrderConverter {

    private OrderConverter(){

    }

    public static PadPayInfoReqDTO fromPreBillOrderToPayReq(DineinOrderDetailRespDTO orderDetailResp){

        PadPayInfoReqDTO padPayInfoReq = new PadPayInfoReqDTO();
        padPayInfoReq.setOrderGuid(orderDetailResp.getGuid());
        padPayInfoReq.setActuallyPayFee(orderDetailResp.getActuallyPayFee());
        padPayInfoReq.setMemberDiscountFee(orderDetailResp.getDiscountFee());
        padPayInfoReq.setOrderTotalFee(orderDetailResp.getOrderFee());
        padPayInfoReq.setAppendFee(orderDetailResp.getAppendFee());
        if(orderDetailResp.getIntegralOffsetResultRespDTO() != null){
            padPayInfoReq.setUseIntegral(orderDetailResp.getIntegralOffsetResultRespDTO().getUseIntegral());
            padPayInfoReq.setIntegralDeductedAmount(orderDetailResp.getIntegralOffsetResultRespDTO().getDeductionMoney());
        }
        padPayInfoReq.setVersion(orderDetailResp.getVersion());
        UserContext userContext = UserContextUtils.get();
        if (!StringUtils.isEmpty(userContext.getOperSubjectGuid())) {
            padPayInfoReq.setOperSubjectGuid(userContext.getOperSubjectGuid());
        }
        padPayInfoReq.setStoreGuid(orderDetailResp.getStoreGuid());
        padPayInfoReq.setStoreName(orderDetailResp.getStoreName());
        padPayInfoReq.setMemberInfoGuid(orderDetailResp.getMemberGuid());
        if(orderDetailResp.getMemberInfoAndCard() != null && orderDetailResp.getMemberInfoAndCard().getMemberInfoDTO() != null){
            padPayInfoReq.setNickName( orderDetailResp.getMemberInfoAndCard().getMemberInfoDTO().getNickName());
        }
        padPayInfoReq.setDiscountFeeDetailDTOS(orderDetailResp.getDiscountFeeDetailDTOS());
        padPayInfoReq.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        return padPayInfoReq;
    }

    public static BillAggPayReqDTO fromPreBillOrderToAggPayReq(DineinOrderDetailRespDTO orderDetail) {
        BillAggPayReqDTO aggPayReqDTO = new BillAggPayReqDTO();
        aggPayReqDTO.setOrderGuid(orderDetail.getGuid());
        aggPayReqDTO.setAmount(orderDetail.getActuallyPayFee());
        aggPayReqDTO.setLast(Boolean.TRUE);
        aggPayReqDTO.setCheckoutSuccessFlag(Boolean.TRUE);
        aggPayReqDTO.setOrderFee(orderDetail.getOrderFee());
        aggPayReqDTO.setAppendFee(orderDetail.getAppendFee());
        aggPayReqDTO.setActuallyPayFee(orderDetail.getActuallyPayFee());
        aggPayReqDTO.setDiscountFee(orderDetail.getDiscountFee());
        if(orderDetail.getIntegralOffsetResultRespDTO() != null){
            aggPayReqDTO.setUseIntegral(orderDetail.getIntegralOffsetResultRespDTO().getUseIntegral());
            aggPayReqDTO.setIntegralDiscountMoney(orderDetail.getIntegralOffsetResultRespDTO().getDeductionMoney());
        }
        aggPayReqDTO.setMemberIntegralStore(BooleanEnum.FALSE.getCode());
        aggPayReqDTO.setDiscountFeeDetailDTOS(orderDetail.getDiscountFeeDetailDTOS());
        aggPayReqDTO.setVersion(orderDetail.getVersion());
        aggPayReqDTO.setDeviceType(orderDetail.getDeviceType());
        aggPayReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        aggPayReqDTO.setStoreGuid(orderDetail.getStoreGuid());
        aggPayReqDTO.setStoreName(orderDetail.getStoreName());
        aggPayReqDTO.setUserGuid(Optional.ofNullable(orderDetail.getMemberGuid()).orElse("0"));
        aggPayReqDTO.setMemberInfoGuid(orderDetail.getMemberGuid());
        aggPayReqDTO.setMemberInfoCardGuid(orderDetail.getMemberCardGuid());
        if(orderDetail.getMemberInfoAndCard() != null && orderDetail.getMemberInfoAndCard().getMemberInfoDTO() != null){
            aggPayReqDTO.setUserName(Optional.ofNullable( orderDetail.getMemberInfoAndCard().getMemberInfoDTO().getNickName()).orElse("未登录用户"));
        }
        //如果名字为空设置用户名称 打印必传字段
        if(StringUtils.isEmpty(aggPayReqDTO.getUserName())){
            aggPayReqDTO.setUserName(StringUtils.isEmpty(orderDetail.getCreateStaffName()) ? "操作员" : orderDetail.getCreateStaffName());
        }
        UserContext userContext = UserContextUtils.get();
        if (!StringUtils.isEmpty(userContext.getOperSubjectGuid())) {
            aggPayReqDTO.setOperSubjectGuid(userContext.getOperSubjectGuid());
        }
        aggPayReqDTO.setActiveScan(1);
        return aggPayReqDTO;
    }
}
