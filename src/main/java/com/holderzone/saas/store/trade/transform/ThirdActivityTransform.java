package com.holderzone.saas.store.trade.transform;


import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityStatisticsRespDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.trade.entity.domain.ThirdActivityRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description 第三方活动转换类
 * @date 2021/12/10 11:27
 * @className: ThirdActivityTransform
 */
@Mapper
public interface ThirdActivityTransform {

    ThirdActivityTransform INSTANCE = Mappers.getMapper(ThirdActivityTransform.class);

    @Mappings({
            @Mapping(target = "thirdActivityCodeList", ignore = true)
    })
    ThirdActivityRecordDTO thirdActRecDO2ThirdActRecDTO(ThirdActivityRecordDO recordDO);

    List<ThirdActivityRecordDTO> thirdActRecDOList2ThirdActRecDTOList(List<ThirdActivityRecordDO> recordDOList);

    @Mappings({
            @Mapping(target = "gmtCreate", ignore = true),
            @Mapping(target = "gmtModified", ignore = true),
            @Mapping(target = "isDelete", ignore = true),
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "thirdActivityCodes", ignore = true)
    })
    ThirdActivityRecordDO thirdActRecDTO2ThirdActRecDO(ThirdActivityRecordDTO recordDTO);

    List<ThirdActivityRecordDO> thirdActRecDTOList2ThirdActRecDOList(List<ThirdActivityRecordDTO> recordDTOList);
}
