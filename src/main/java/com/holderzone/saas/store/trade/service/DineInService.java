package com.holderzone.saas.store.trade.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.request.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.ReserveBatchCreateOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.print.PrintLabelReq;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-order
 */
public interface DineInService {

    /**
     * 创建正餐订单
     */
    CreateDineInOrderReqDTO createOrder(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    /**
     * 批量查询订单桌台信息
     *
     * @param orderGuidsDTO
     * @return
     */
    List<OrderTableInfoDTO> batchGetTableInfo(OrderGuidsDTO orderGuidsDTO);

    /**
     * 修改就餐人数
     *
     * @param createDineInOrderReqDTO
     * @return
     */
    Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    /**
     * 修改整单备注
     *
     * @param createDineInOrderReqDTO
     * @return
     */
    Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    DineinOrderDetailRespDTO getSingleOrderDetail(OrderDO orderDO);

    DineinOrderDetailRespDTO getOrderDetail(String orderGuid);

    DineinOrderDetailRespDTO getOrderDetails(String orderGuid);

    DineinOrderDetailRespDTO getOrderDetailForAndroid(OrderDetailQueryDTO orderDetailQueryDTO);

    /**
     * 作废订单
     *
     * @param cancelOrderReqDTO
     * @return
     */
    Boolean cancelOrder(CancelOrderReqDTO cancelOrderReqDTO);

    /**
     * 查询订单列表
     *
     * @param dineInOrderListReqDTO
     * @return
     */
    Page<DineInOrderListRespDTO> orderList(DineInOrderListReqDTO dineInOrderListReqDTO);

    /**
     * 查询订单列表
     */
    Page<DineInOrderListRespDTO> orderListNew(DineInOrderListReqDTO dineInOrderListReqDTO);

    /**
     * 增加版本号
     *
     * @param orderGuid
     * @return
     */
    Boolean addVersion(String orderGuid);


    /**
     * 获取版本号
     *
     * @param orderGuid
     * @return
     */
    String getVersion(String orderGuid);

    DineinOrderDetailRespDTO getOrderDetail4AddItem(String data);

    boolean printItemDetail(BaseDTO baseDTO, String orderGuid);

    boolean printCheckOut(BaseDTO baseDTO, String orderGuid);

    String batchCreateOrder(ReserveBatchCreateOrderReqDTO reserveBatchCreateOrderReqDTO);

    /**
     * 批量查询订单桌台信息
     * 专用于桌台查询所用
     *
     * @param orderGuidsDTO 订单
     * @return 订单信息
     */
    List<OrderTableInfoDTO> batchGetTableInfo2(OrderGuidsDTO orderGuidsDTO);

    /**
     * 打印菜品复单
     */
    boolean printItemRepeatOrder(CreateDineInOrderReqDTO itemRepeatOrderReqDTO);

    /**
     * 收款方式下拉
     */
    List<String> listPaymentTypeName();


    /**
     * 根据订单Guid查询订单及关联商品信息
     *
     * @param orderGuid 订单Guid
     * @return 订单商品信息
     */
    DineinOrderDetailRespDTO getOrderItemDetail(String orderGuid);

    /**
     * 打印标签
     * @param printLabelReq 商品信息
     */
    void printLabel(PrintLabelReq printLabelReq);
}
