package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityStatisticsRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.statistics.RequestDutyStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseDutyStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponsePayWayDetail;
import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.member.request.HandoverReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.order.response.daily.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.trade.HandoverDTO;
import com.holderzone.saas.store.dto.trade.HandoverOrderOptimizationDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.GrouponReceiptChannelEnum;
import com.holderzone.saas.store.trade.beans.ThirdActivityBeanUtils;
import com.holderzone.saas.store.trade.entity.domain.GrouponDO;
import com.holderzone.saas.store.trade.entity.dto.DebtRepaymentFeeTotalDTO;
import com.holderzone.saas.store.trade.entity.enums.DebtPaymentTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.query.DebtRepaymentFeeTotalQuery;
import com.holderzone.saas.store.trade.mapper.DebtUnitRecordMapper;
import com.holderzone.saas.store.trade.mapper.GrouponMapper;
import com.holderzone.saas.store.trade.mapper.OrderMapper;
import com.holderzone.saas.store.trade.mapper.TransactionRecordMapper;
import com.holderzone.saas.store.trade.repository.feign.MemberDataClientService;
import com.holderzone.saas.store.trade.repository.feign.TableClientService;
import com.holderzone.saas.store.trade.repository.feign.ThirdActivityClientService;
import com.holderzone.saas.store.trade.repository.interfaces.DiscountService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderRefundRecordService;
import com.holderzone.saas.store.trade.service.HandoverSheetService;
import com.holderzone.saas.store.trade.transform.ThirdActivityTransform;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverSheetServiceImpl
 * @date 2019/04/08 10:31
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Service
@Slf4j
public class HandoverSheetServiceImpl implements HandoverSheetService {

    @Resource
    private TransactionRecordMapper transactionRecordMapper;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private GrouponMapper grouponMapper;

    @Resource
    private MemberDataClientService memberDataClientService;

    @Resource
    private DebtUnitRecordMapper debtUnitRecordMapper;

    @Autowired
    private ThirdActivityClientService thirdActivityClientService;

    @Autowired
    private TableClientService tableClientService;

    @Autowired
    private DiscountService discountService;

    @Autowired
    private OrderRefundRecordService orderRefundRecordService;

    private static final ThirdActivityTransform thirdActivityTransform = ThirdActivityTransform.INSTANCE;


    @Override
    public HandoverPayDTO handover(HandoverPayQueryDTO handoverPayQueryDTO) {
        if (null == handoverPayQueryDTO.getGmtModified()) {
            LocalDateTime now = DateTimeUtils.now();
            handoverPayQueryDTO.setGmtModified(now);
        }

        HandoverReqDTO handoverReqDTO = new HandoverReqDTO();
        handoverReqDTO.setBeginTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(handoverPayQueryDTO.getGmtCreate()));
        handoverReqDTO.setEndTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(handoverPayQueryDTO.getGmtModified()));
        handoverReqDTO.setStaffGuid(handoverPayQueryDTO.getUserGuid());
        handoverReqDTO.setStoreGuid(handoverPayQueryDTO.getStoreGuid());

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime start = LocalDateTime.parse(handoverReqDTO.getBeginTime(), df);
        LocalDateTime end = LocalDateTime.parse(handoverReqDTO.getEndTime(), df);

        RequestDutyStatis dutyReq = new RequestDutyStatis();
        dutyReq.setStartTime(start);
        dutyReq.setEndTime(end);
        dutyReq.setOperatorGuid(handoverPayQueryDTO.getUserGuid());
        dutyReq.setStoreGuid(handoverPayQueryDTO.getStoreGuid());

        //会员未返回去paycode  需要根据名称进行分组
        ResponseDutyStatis dutyResp = memberDataClientService.getStatisticsOfTotal(dutyReq);
        MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        List<AmountItemDTO> amountList = this.changeData(dutyResp.getRechargeDetailList());
        memberConsumeRespDTO.setPrepaidItems(amountList);
        log.info("memeber service invoke: entry {}, and return {}", JacksonUtils.writeValueAsString(dutyReq), JacksonUtils.writeValueAsString(dutyResp));
        List<HandoverDTO> handover = transactionRecordMapper.handover(handoverPayQueryDTO);
        Integer count = orderMapper.handoverOrderCount(handoverPayQueryDTO);
        Map<Integer, BigDecimal> incomingDetail = handover.stream().filter(o -> Optional.ofNullable(o.getPaymentName()).isPresent())
                .collect(Collectors.toMap(HandoverDTO::getPaymentType, HandoverDTO::getAmount, BigDecimal::add));//list 转set
        Map<String, BigDecimal> detail = handover.stream().filter(o -> Optional.ofNullable(o.getPaymentName()).isPresent())
                .collect(Collectors.toMap(HandoverDTO::getPaymentName, HandoverDTO::getAmount, BigDecimal::add));
        BigDecimal sumAmount = BigDecimal.ZERO;
        List<AmountItemDTO> amountItemDTOS = memberConsumeRespDTO.getPrepaidItems();
        if (amountItemDTOS != null) {
            amountItemDTOS.forEach((e) -> {
                BigDecimal a = detail.get(e.getName());
                a = a == null ? BigDecimal.valueOf(0) : a;
                a = a.add(e.getAmount());
                detail.put(e.getName(), a);
            });
        }
        //计算消费总金额、充值
        for (BigDecimal temp : incomingDetail.values()) {
            sumAmount = sumAmount.add(temp);
        }
        HandoverPayDTO handoverPayDTO = new HandoverPayDTO();
        //handoverPayDTO.setIncomingDetail(incomingDetail); //支付map
        handoverPayDTO.setIncomingDetail(Collections.emptyMap());
        handoverPayDTO.setIncomingDetailStr(detail);  //支付详情
        handoverPayDTO.setUserGuid(handoverPayQueryDTO.getUserGuid()); //用户guid
        handoverPayDTO.setGmtCreate(handoverPayQueryDTO.getGmtCreate());  //开班时间
        handoverPayDTO.setGmtModified(handoverPayQueryDTO.getGmtModified());  //交班时间
        handoverPayDTO.setUserName(handoverPayQueryDTO.getUserName()); //用户名称
        handoverPayDTO.setChargedCount(dutyResp.getRechargeCount() == null ? 0 : dutyResp.getRechargeCount());
        handoverPayDTO.setChargeIncoming(dutyResp.getRechargeMoney() == null ? BigDecimal.ZERO : dutyResp.getRechargeMoney());
        handoverPayDTO.setGmtCreate(handoverPayQueryDTO.getGmtCreate());
        handoverPayDTO.setGmtModified(handoverPayQueryDTO.getGmtModified());
        // 营业额 = 销售收入+充值收入- 会员卡消费金额
        BigDecimal b4 = BigDecimal.ZERO;
        if (incomingDetail != null) {
            b4 = incomingDetail.get(4) != null ? incomingDetail.get(4) : BigDecimal.ZERO;
        }
        BigDecimal memberPrepaidAmoun = BigDecimal.ZERO;//memberConsumeRespDTO.getPrepaidAmount() != null ?  memberConsumeRespDTO.getPrepaidAmount(): BigDecimal.ZERO; //会员充值金额
        BigDecimal businessIncoming = sumAmount.add(memberPrepaidAmoun).subtract(b4);
        handoverPayDTO.setBusinessIncoming(businessIncoming);
        handoverPayDTO.setPaymentMoney(sumAmount.add(memberPrepaidAmoun));
        // 已结账订单数
        handoverPayDTO.setCheckedCount(count);
        handoverPayDTO.setUserName(UserContextUtils.getUserName());
        handoverPayDTO.setUserGuid(UserContextUtils.getUserGuid());
        // 销售收入 = 全部收入
        BigDecimal saleIncoming = BigDecimal.ZERO;
        for (HandoverDTO temp : handover) {
            saleIncoming = saleIncoming.add(temp.getAmount());
        }
        handoverPayDTO.setSaleIncoming(saleIncoming);
        log.info("handover request: entry {}, and return {}", JacksonUtils.writeValueAsString(handoverPayQueryDTO), JacksonUtils.writeValueAsString(handoverPayDTO));
        return handoverPayDTO;
    }

    @Override
    public HandoverPayDTO handoverNew(HandoverPayQueryDTO handoverPayQueryDTO) {
        if (handoverPayQueryDTO.getGmtModified() == null) {
            LocalDateTime now = DateTimeUtils.now();
            handoverPayQueryDTO.setGmtModified(now);
        }

        HandoverReqDTO handoverReqDTO = new HandoverReqDTO();
        handoverReqDTO.setBeginTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(handoverPayQueryDTO.getGmtCreate()));
        handoverReqDTO.setEndTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(handoverPayQueryDTO.getGmtModified()));
        handoverReqDTO.setStaffGuid(handoverPayQueryDTO.getUserGuid());
        handoverReqDTO.setStoreGuid(handoverPayQueryDTO.getStoreGuid());

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime start = LocalDateTime.parse(handoverReqDTO.getBeginTime(), df);
        LocalDateTime end = LocalDateTime.parse(handoverReqDTO.getEndTime(), df);

        RequestDutyStatis dutyReq = new RequestDutyStatis();
        dutyReq.setStartTime(start);
        dutyReq.setEndTime(end);
        dutyReq.setOperatorGuid(UserContextUtils.getUserAccount());
        dutyReq.setStoreGuid(handoverPayQueryDTO.getStoreGuid());

        //查询并设置会员充值明细（充值笔数、充值总额、充值总额中现金支付金额、充值方式明细）
        ResponseDutyStatis dutyResp = memberDataClientService.getStatisticsOfTotal(dutyReq);
        log.info("memeber service invoke: entry {}, and return {}", JacksonUtils.writeValueAsString(dutyReq),
                JacksonUtils.writeValueAsString(dutyResp));
        HandoverPayDTO handoverPayDTO = new HandoverPayDTO();
        handoverPayDTO.setChargedCount(dutyResp.getRechargeCount() == null ? 0 : dutyResp.getRechargeCount());
        handoverPayDTO.setChargeIncoming(dutyResp.getRechargeMoney() == null ? BigDecimal.ZERO : dutyResp.getRechargeMoney());
        List<ResponsePayWayDetail> rechargeDetailList = dutyResp.getRechargeDetailList();
        //会员充值支付方式明细(Map<支付方式名称,支付金额>)
        Map<String, BigDecimal> chargeDetailStr = new HashMap<>();
        //会员充值支付方式明细(List，有支付方式code，方便匹配排序）
        List<AmountItemDTO> chargeDetailList = new ArrayList<>();
        BigDecimal chargeCash = BigDecimal.ZERO;
        for (ResponsePayWayDetail payWayDetail : rechargeDetailList) {
            if (payWayDetail.getPayWay() == 0) {
                //会员现金支付方式=0
                chargeCash = payWayDetail.getPayAmount();
            }
            chargeDetailStr.put(payWayDetail.getPayName(), payWayDetail.getPayAmount());
            AmountItemDTO amount = new AmountItemDTO();
            //会员的支付类型code+1=商户后台支付code
            amount.setCode(payWayDetail.getPayWay() + 1).setName(payWayDetail.getPayName()).setAmount(payWayDetail.getPayAmount());
            chargeDetailList.add(amount);
        }
        handoverPayDTO.setChargeCash(chargeCash);
        handoverPayDTO.setChargeDetailStr(chargeDetailStr);
        handoverPayDTO.setChargeDetailList(chargeDetailList);

        List<HandoverDTO> handover = transactionRecordMapper.handover(handoverPayQueryDTO);
        Integer count = orderMapper.handoverOrderCount(handoverPayQueryDTO);
        //销售收入支付方式明细(List，有支付方式code，方便匹配排序）
        List<AmountItemDTO> incomingDetailList = new ArrayList<>();
        handover.forEach(v -> {
            if (PaymentTypeEnum.MT_GROUPON.getCode() == v.getPaymentType()
                    || DiscountTypeEnum.DOU_YIN_GROUPON.getCode() == v.getPaymentType()
                    || DiscountTypeEnum.ALIPAY_GROUPON.getCode() == v.getPaymentType()
                    || DiscountTypeEnum.DA_ZHONG_DIAN_PIN.getCode() == v.getPaymentType()
                    || DiscountTypeEnum.ABC_GROUPON.getCode() == v.getPaymentType()
                    || v.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                return;
            }
            AmountItemDTO amount = new AmountItemDTO();
            amount.setCode(v.getPaymentType()).setName(v.getPaymentName()).setAmount(v.getAmount());
            if (PaymentTypeEnum.THIRD_ACTIVITY.getCode() == v.getPaymentType()) {
                // 如果有第三方活动，则查询是否有余出
                BigDecimal excessAmount = transactionRecordMapper.calExcessAmount(handoverPayQueryDTO);
                amount.setExcessAmount(excessAmount);
                // 第三方活动金额 = 第三方活动支付金额 - 余出金额
                final BigDecimal useTotalAmount = amount.getAmount();
                amount.setAmount(useTotalAmount.subtract(excessAmount));
                if (useTotalAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }
                List<AmountItemDTO.InnerDetails> innerDetails = handoverNewByThirdActivity(handoverPayQueryDTO);
                amount.setInnerDetails(innerDetails);
            }
            incomingDetailList.add(amount);
        });
        handoverPayDTO.setIncomingDetailList(incomingDetailList);
        // 追加团购验券统计 团购验券属于优惠方式不计入销售收入统计 暂时只查询groupon表
        handoverPayDTO.setDiscountDetailList(Lists.newArrayList());
        List<AmountItemDTO> grouponAmountItemList = handoverNewByDiscount(handoverPayQueryDTO);
        log.info("查询交接班数据,团购验券查询顾客实际支付返回:{}", JacksonUtils.writeValueAsString(grouponAmountItemList));
        if (CollectionUtils.isNotEmpty(grouponAmountItemList)) {
            handoverPayDTO.getDiscountDetailList().addAll(grouponAmountItemList);
        }
        Map<Integer, BigDecimal> incomingDetail = incomingDetailList.stream().filter(o -> Optional.ofNullable(o.getName()).isPresent())
                .collect(Collectors.toMap(AmountItemDTO::getCode, AmountItemDTO::getAmount, BigDecimal::add));
        Map<String, BigDecimal> detail = incomingDetailList.stream().filter(o -> Optional.ofNullable(o.getName()).isPresent())
                .collect(Collectors.toMap(AmountItemDTO::getName, AmountItemDTO::getAmount, BigDecimal::add));

        BigDecimal sumAmount = BigDecimal.ZERO;
        //计算消费总金额、充值
        for (BigDecimal temp : incomingDetail.values()) {
            sumAmount = sumAmount.add(temp);
        }
        handoverPayDTO.setSaleCash(incomingDetail.get(1) == null ? BigDecimal.ZERO : incomingDetail.get(1));  //现金支付金额（现金支付方式=1）
        handoverPayDTO.setIncomingDetail(Collections.emptyMap());
        handoverPayDTO.setIncomingDetailStr(detail);  //支付详情
        handoverPayDTO.setUserGuid(handoverPayQueryDTO.getUserGuid()); //用户guid
        handoverPayDTO.setGmtCreate(handoverPayQueryDTO.getGmtCreate());  //开班时间
        handoverPayDTO.setGmtModified(handoverPayQueryDTO.getGmtModified());  //交班时间
        handoverPayDTO.setUserName(handoverPayQueryDTO.getUserName()); //用户名称

        handoverPayDTO.setGmtCreate(handoverPayQueryDTO.getGmtCreate());
        handoverPayDTO.setGmtModified(handoverPayQueryDTO.getGmtModified());
        // 营业额 = 销售收入+充值收入- 会员卡消费金额
        BigDecimal b4;
        b4 = incomingDetail.get(4) != null ? incomingDetail.get(4) : BigDecimal.ZERO;
        //memberConsumeRespDTO.getPrepaidAmount() != null ?  memberConsumeRespDTO.getPrepaidAmount(): BigDecimal.ZERO; //会员充值金额
        BigDecimal memberPrepaidAmoun = BigDecimal.ZERO;
        BigDecimal businessIncoming = sumAmount.add(memberPrepaidAmoun).subtract(b4);
        handoverPayDTO.setBusinessIncoming(businessIncoming);
        handoverPayDTO.setPaymentMoney(sumAmount.add(memberPrepaidAmoun));
        // 已结账订单数
        handoverPayDTO.setCheckedCount(count);
        handoverPayDTO.setUserName(UserContextUtils.getUserName());
        handoverPayDTO.setUserGuid(UserContextUtils.getUserGuid());
        // 销售收入 = 收款方式之和 + 团购顾客实际支付金额
        BigDecimal saleIncoming = BigDecimal.ZERO;
        for (AmountItemDTO temp : incomingDetailList) {
            saleIncoming = saleIncoming.add(temp.getAmount());
        }
        for (AmountItemDTO grouponItem : grouponAmountItemList) {
            saleIncoming = saleIncoming.add(grouponItem.getAmount());
        }
        handoverPayDTO.setSaleIncoming(saleIncoming);

        //挂账还款交班汇总
        DebtRepaymentFeeTotalQuery query = new DebtRepaymentFeeTotalQuery();
        query.setStartDateTime(handoverPayQueryDTO.getGmtCreate());
        query.setEndDateTime(handoverPayQueryDTO.getGmtModified());
        query.setCreateStaffGuid(handoverPayQueryDTO.getUserGuid());
        query.setStoreGuid(handoverPayQueryDTO.getStoreGuid());
        List<DebtRepaymentFeeTotalDTO> debtRepaymentFeeTotalDTOS = debtUnitRecordMapper.queryRepaymentFeeTotal(query);
        Map<String, BigDecimal> repaymentStr = new HashMap<>();
        if (CollectionUtils.isNotEmpty(debtRepaymentFeeTotalDTOS)) {
            //结算方式（0：现金，1：支付宝，2：微信，3：银行卡，4：支票）
            List<AmountItemDTO> repaymentList = new ArrayList<>();
            BigDecimal resultTotal = BigDecimal.ZERO;
            int repaymentFeeCount = 0;
            for (DebtRepaymentFeeTotalDTO debtRepaymentFeeTotalDTO : debtRepaymentFeeTotalDTOS) {
                AmountItemDTO amountItemDTO = new AmountItemDTO();
                amountItemDTO.setAmount(debtRepaymentFeeTotalDTO.getRepaymentFeeTotal());
                amountItemDTO.setName(DebtPaymentTypeEnum.getNameById(debtRepaymentFeeTotalDTO.getPaymentType()));
                amountItemDTO.setCode(debtRepaymentFeeTotalDTO.getPaymentType());
                amountItemDTO.setSort(debtRepaymentFeeTotalDTO.getPaymentType());
                repaymentStr.put(amountItemDTO.getName(), amountItemDTO.getAmount());
                repaymentList.add(amountItemDTO);
                resultTotal = resultTotal.add(debtRepaymentFeeTotalDTO.getRepaymentFeeTotal());
                repaymentFeeCount = repaymentFeeCount + debtRepaymentFeeTotalDTO.getRepaymentFeeCount();
            }
            handoverPayDTO.setRepaymentList(repaymentList);
            handoverPayDTO.setRepaymentFeeTotal(resultTotal);
            handoverPayDTO.setRepaymentFeeCount(repaymentFeeCount);
        } else {
            handoverPayDTO.setRepaymentList(Lists.newArrayList());
            handoverPayDTO.setRepaymentFeeTotal(BigDecimal.ZERO);
            handoverPayDTO.setRepaymentFeeCount(0);
        }
        handoverPayDTO.setRepaymentStr(repaymentStr);
        setOptimizationHandoverData(handoverPayDTO, handoverPayQueryDTO);
        // 优惠总额
        setDisCountTotalFee(handoverPayDTO, handoverPayQueryDTO);
        // 退款总额
        setRefundAmount(handoverPayDTO, handoverPayQueryDTO);
        // 销售总额 = 销售净额 + 优惠总额 + 退款总额
        handoverPayDTO.setSaleAmount(handoverPayDTO.getSaleIncoming().add(handoverPayDTO.getDiscountAmount()).add(handoverPayDTO.getRefundAmount()));
        log.info("handover request: entry {}, and return {}", JacksonUtils.writeValueAsString(handoverPayQueryDTO), JacksonUtils.writeValueAsString(handoverPayDTO));
        return handoverPayDTO;
    }

    private void setRefundAmount(HandoverPayDTO handoverPayDTO, HandoverPayQueryDTO handoverPayQueryDTO) {
        BigDecimal bigDecimal = orderRefundRecordService.handoverRefund(handoverPayQueryDTO);
        if (Objects.nonNull(bigDecimal)) {
            handoverPayDTO.setRefundAmount(bigDecimal);
        } else {
            handoverPayDTO.setRefundAmount(BigDecimal.ZERO);
        }
    }

    private void setDisCountTotalFee(HandoverPayDTO handoverPayDTO, HandoverPayQueryDTO handoverPayQueryDTO) {
        List<AmountItemDTO> result = Lists.newArrayList();
        //查询优惠方式
        List<AmountItemDTO> amountItemDTOS = discountService.handoverDiscountType(handoverPayQueryDTO);
        // 优惠方式过滤团购验券、第三方活动展示
        amountItemDTOS.removeIf(e -> DiscountTypeEnum.THIRD_ACTIVITY.getCode() == e.getCode()
                || GroupBuyTypeEnum.CODE_LIST.contains(e.getCode()));
        result.addAll(amountItemDTOS);
        // 团购券
        List<AmountItemDTO> grouponDiscount = handoverGrouponDiscount(handoverPayQueryDTO);
        result.addAll(grouponDiscount);
        BigDecimal totalDiscount = result.stream().map(AmountItemDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        handoverPayDTO.setDiscountAmount(totalDiscount);
    }

    private List<AmountItemDTO> handoverGrouponDiscount(HandoverPayQueryDTO handoverPayQueryDTO) {
        // 查询了团购验券
        List<GrouponDO> grouponList = grouponMapper.handover(handoverPayQueryDTO);
        List<AmountItemDTO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(grouponList)) {
            return result;
        }
        Map<Integer, List<GrouponDO>> groupingByGroupBuyTypeMap = grouponList.stream().collect(Collectors.groupingBy(GrouponDO::getGrouponType));
        for (Map.Entry<Integer, List<GrouponDO>> entry : groupingByGroupBuyTypeMap.entrySet()) {
            Integer groupBuyType = entry.getKey();
            List<GrouponDO> grouponByGroupBuyTypeList = entry.getValue();
            BigDecimal totalAmount = grouponByGroupBuyTypeList.stream()
                    .map(grouponDO -> grouponDO.getDeductionAmount().subtract(grouponDO.getCouponBuyPrice()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            AmountItemDTO amountItemDTO = new AmountItemDTO();
            amountItemDTO.setCode(groupBuyType)
                    .setName(GroupBuyTypeEnum.getDesc(groupBuyType))
                    .setAmount(totalAmount)
                    .setInnerDetails(Lists.newArrayList());

            // 有匹配到活动的根据thirdCode分组合并
            List<GrouponDO> hasActivityGrouponList = grouponByGroupBuyTypeList.stream()
                    .filter(e -> !StringUtils.isEmpty(e.getActivityGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hasActivityGrouponList)) {
                Map<String, List<GrouponDO>> groupingByThirdCodeMap = hasActivityGrouponList.stream()
                        .collect(Collectors.groupingBy(GrouponDO::getThirdCode));
                amountItemDTO.getInnerDetails().addAll(buildGrouponDiscountInnerDetailsList(groupingByThirdCodeMap));
            }
            // 没有匹配到活动的根据名称分组合并
            List<GrouponDO> notHaveActivityGrouponList = grouponByGroupBuyTypeList.stream()
                    .filter(e -> StringUtils.isEmpty(e.getActivityGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notHaveActivityGrouponList)) {
                Map<String, List<GrouponDO>> groupingByNameMap = notHaveActivityGrouponList.stream()
                        .collect(Collectors.groupingBy(GrouponDO::getName));
                amountItemDTO.getInnerDetails().addAll(buildGrouponDiscountInnerDetailsList(groupingByNameMap));
            }
            result.add(amountItemDTO);
        }
        return result;
    }

    /**
     * 组装优化数据：客流量，上座率，开台率，翻台率，平均用餐时长
     * @param handoverPayDTO
     * @param handoverPayQueryDTO
     */
    private void setOptimizationHandoverData(HandoverPayDTO handoverPayDTO,
                                             HandoverPayQueryDTO handoverPayQueryDTO) {
        List<HandoverOrderOptimizationDTO> handoverOrderOptimizationDTOS = orderMapper.handoverOrderOptimization(handoverPayQueryDTO);
        log.info("handoverOrderOptimizationDTOS: {}", JacksonUtils.writeValueAsString(handoverOrderOptimizationDTOS));
        // 客流量
        int traffic = 0;
        // 桌台使用次数
        int tableUseCount = 0;
        // 总用餐时间，单位分钟
        long totalDineInTime = 0;
        // key = mainOrderGuid
        Map<String, List<HandoverOrderOptimizationDTO>> subOrderMap = handoverOrderOptimizationDTOS.stream()
                .filter(dto -> !org.apache.commons.lang3.StringUtils.equals(dto.getMainOrderGuid(), "0"))
                .collect(Collectors.groupingBy(HandoverOrderOptimizationDTO::getMainOrderGuid));
        for (HandoverOrderOptimizationDTO dto : handoverOrderOptimizationDTOS) {
            if (Objects.nonNull(dto.getGuestCount())) {
                traffic = traffic + dto.getGuestCount();
            }
            // 只处理主订单
            if (org.apache.commons.lang3.StringUtils.equals(dto.getMainOrderGuid(), "0")) {
                if (org.apache.commons.lang3.StringUtils.equals(dto.getAssociatedFlag(), "1")) {
                    long minutesBetween = DateUtil.minutesBetween(dto.getGmtCreate(), dto.getCheckoutTime());
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getAssociatedTableGuids())) {
                        List<String> list = JacksonUtils.toObjectList(String.class, dto.getAssociatedTableGuids());
                        tableUseCount = tableUseCount + list.size();
                        totalDineInTime = totalDineInTime + minutesBetween * list.size();
                    } else {
                        tableUseCount = tableUseCount + 1;
                        totalDineInTime = totalDineInTime + minutesBetween;
                    }
                } else {
                    tableUseCount = tableUseCount + 1;
                    long minutesBetween = DateUtil.minutesBetween(dto.getGmtCreate(), dto.getCheckoutTime());
                    totalDineInTime = totalDineInTime + minutesBetween;
                    List<HandoverOrderOptimizationDTO> subDTOs = subOrderMap.get(dto.getGuid());
                    if (CollectionUtils.isNotEmpty(subDTOs)) {
                        tableUseCount = tableUseCount + subDTOs.size();
                        for (HandoverOrderOptimizationDTO subDTO : subDTOs) {
                            long subMinutesBetween = DateUtil.minutesBetween(subDTO.getGmtCreate(), subDTO.getCheckoutTime());
                            totalDineInTime = totalDineInTime + subMinutesBetween;
                        }
                    }
                }
            }
        }
        TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid(handoverPayQueryDTO.getStoreGuid());
        List<TableBasicDTO> tableBasicDTOS = tableClientService.listByWeb(tableBasicQueryDTO);
        // 总桌台数
        int tableCount = tableBasicDTOS.size();
        // 总餐位数
        int totalSeats = tableBasicDTOS.stream().filter(Objects::nonNull).mapToInt(TableBasicDTO::getSeats).sum();
        handoverPayDTO.setTraffic(traffic);
        handoverPayDTO.setTableUseCount(tableUseCount);
        handoverPayDTO.setTotalDineInTime(totalDineInTime);
        handoverPayDTO.setTableCount(tableCount);
        handoverPayDTO.setTotalSeats(totalSeats);
        // 上座率
        setOccupancyRate(handoverPayDTO, totalSeats, traffic);
        // 开台率
        setOpenTableRate(handoverPayDTO, tableCount, tableUseCount);
        // 翻台率
        setFlipTableRate(handoverPayDTO, tableCount, tableUseCount);
        // 平均用餐时长
        setAvgDineInTime(handoverPayDTO, tableUseCount, totalDineInTime);
    }

    private static void setAvgDineInTime(HandoverPayDTO handoverPayDTO, int tableUseCount, long totalDineInTime) {
        // 平均用餐时长 = (离座时间 - 入座时间) ÷ 总桌数，单位分钟
        int avgDineInTime;
        if (tableUseCount == 0) {
            avgDineInTime = 0;
        } else {
            avgDineInTime = (int) Math.floor((double) totalDineInTime / tableUseCount + 0.5);
        }
        handoverPayDTO.setAvgDineInTime(avgDineInTime);
    }

    private static void setFlipTableRate(HandoverPayDTO handoverPayDTO, int tableCount, int tableUseCount) {
        // 翻台率 = (桌台使用次数 - 总桌台数) ÷ 总桌台数 × 100%
        double flipTableRate;
        if (tableCount == 0) {
            flipTableRate = 0;
        } else {
            flipTableRate = (double) (tableUseCount - tableCount) / tableCount;
        }
        // 翻台率
        String flipTableRatePercent = String.format("%.2f%%", flipTableRate * 100);
        handoverPayDTO.setFlipTableRatePercent(flipTableRatePercent);
    }

    private static void setOpenTableRate(HandoverPayDTO handoverPayDTO, int tableCount, int tableUseCount) {
        // 开台率 = 桌台使用次数 ÷ 总桌台数 × 100%
        double openTableRate;
        if (tableCount == 0) {
            openTableRate = 0;
        } else {
            openTableRate = (double) tableUseCount / tableCount;
        }
        // 开台率
        String openTableRatePercent = String.format("%.2f%%", openTableRate * 100);
        handoverPayDTO.setOpenTableRatePercent(openTableRatePercent);
    }

    private static void setOccupancyRate(HandoverPayDTO handoverPayDTO, int totalSeats, int traffic) {
        // 上座率 = 客流量 ÷ 总餐位数 × 100%
        double occupancyRate;
        if (totalSeats == 0) {
            occupancyRate = 0;
        } else {
            occupancyRate = (double) traffic / totalSeats;
        }
        // 上座率
        String occupancyRatePercent = String.format("%.2f%%", occupancyRate * 100);
        handoverPayDTO.setOccupancyRatePercent(occupancyRatePercent);
    }

    @Override
    public List<AmountItemDTO.InnerDetails> handoverNewByThirdActivity(HandoverPayQueryDTO handoverPayQueryDTO) {
        // 查询交接班所涉及订单
        List<String> orderGuids = transactionRecordMapper.handoverOrderGuids(handoverPayQueryDTO);
        if (CollectionUtils.isEmpty(orderGuids)) {
            return Lists.newArrayList();
        }
        log.info("调试交接班数据,根据orderGuids查询第三方活动:{}", JacksonUtils.writeValueAsString(orderGuids));
        // 查询所涉及订单第三方活动明细，进行分组统计
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        singleDataDTO.setDatas(orderGuids);
        List<ThirdActivityStatisticsRespDTO> thirdActivityDetails =
                thirdActivityClientService.selectThirdFeeGroupByThirdActivityGuid(singleDataDTO);
        if (CollectionUtils.isEmpty(thirdActivityDetails)) {
            return Lists.newArrayList();
        }
        return ThirdActivityBeanUtils.copyProperties(thirdActivityDetails);
    }

    @Override
    public List<AmountItemDTO> handoverNewByDiscount(HandoverPayQueryDTO handoverPayQueryDTO) {
        // 暂时只查询了团购验券，后期可能加其他优惠方式
        List<GrouponDO> grouponList = grouponMapper.handover(handoverPayQueryDTO);
        grouponList = grouponList.stream()
                .filter(e -> !Objects.equals(e.getReceiptChannel(), GrouponReceiptChannelEnum.MAITON.getCode()))
                .collect(Collectors.toList());
        List<AmountItemDTO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(grouponList)) {
            return result;
        }
        Map<Integer, List<GrouponDO>> groupingByGroupBuyTypeMap = grouponList.stream().collect(Collectors.groupingBy(GrouponDO::getGrouponType));
        for (Map.Entry<Integer, List<GrouponDO>> entry : groupingByGroupBuyTypeMap.entrySet()) {
            Integer groupBuyType = entry.getKey();
            List<GrouponDO> grouponByGroupBuyTypeList = entry.getValue();
            BigDecimal totalAmount = grouponByGroupBuyTypeList.stream().map(GrouponDO::getCouponBuyPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            AmountItemDTO amountItemDTO = new AmountItemDTO();
            amountItemDTO.setCode(groupBuyType)
                    .setName(GroupBuyTypeEnum.getDesc(groupBuyType))
                    .setAmount(totalAmount)
                    .setInnerDetails(Lists.newArrayList());

            // 有匹配到活动的根据thirdCode分组合并
            List<GrouponDO> hasActivityGrouponList = grouponByGroupBuyTypeList.stream()
                    .filter(e -> !StringUtils.isEmpty(e.getActivityGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hasActivityGrouponList)) {
                Map<String, List<GrouponDO>> groupingByThirdCodeMap = hasActivityGrouponList.stream()
                        .collect(Collectors.groupingBy(GrouponDO::getThirdCode));
                amountItemDTO.getInnerDetails().addAll(buildGrouponInnerDetailsList(groupingByThirdCodeMap));
            }
            // 没有匹配到活动的根据名称分组合并
            List<GrouponDO> notHaveActivityGrouponList = grouponByGroupBuyTypeList.stream()
                    .filter(e -> StringUtils.isEmpty(e.getActivityGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notHaveActivityGrouponList)) {
                Map<String, List<GrouponDO>> groupingByNameMap = notHaveActivityGrouponList.stream()
                        .collect(Collectors.groupingBy(GrouponDO::getName));
                amountItemDTO.getInnerDetails().addAll(buildGrouponInnerDetailsList(groupingByNameMap));
            }
            result.add(amountItemDTO);
        }
        return result;
    }

    private List<AmountItemDTO.InnerDetails> buildGrouponInnerDetailsList(Map<String, List<GrouponDO>> groupingByMap) {
        List<AmountItemDTO.InnerDetails> innerDetailsList = Lists.newArrayList();
        for (Map.Entry<String, List<GrouponDO>> detail : groupingByMap.entrySet()) {
            List<GrouponDO> grouponByThirdCodeList = detail.getValue();
            GrouponDO grouponDO = grouponByThirdCodeList.get(0);
            BigDecimal totalAmount = grouponByThirdCodeList.stream()
                    .map(GrouponDO::getCouponBuyPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            AmountItemDTO.InnerDetails innerDetails = new AmountItemDTO.InnerDetails();
            innerDetails.setName(grouponDO.getName());
            innerDetails.setAmount(totalAmount);
            innerDetails.setCount(grouponByThirdCodeList.size());
            innerDetailsList.add(innerDetails);
        }
        return innerDetailsList;
    }

    private List<AmountItemDTO.InnerDetails> buildGrouponDiscountInnerDetailsList(Map<String, List<GrouponDO>> groupingByMap) {
        List<AmountItemDTO.InnerDetails> innerDetailsList = Lists.newArrayList();
        for (Map.Entry<String, List<GrouponDO>> detail : groupingByMap.entrySet()) {
            List<GrouponDO> grouponByThirdCodeList = detail.getValue();
            GrouponDO grouponDO = grouponByThirdCodeList.get(0);
            BigDecimal totalAmount = grouponByThirdCodeList.stream()
                    .map(groupon -> groupon.getDeductionAmount().subtract(groupon.getCouponBuyPrice()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            AmountItemDTO.InnerDetails innerDetails = new AmountItemDTO.InnerDetails();
            innerDetails.setName(grouponDO.getName());
            innerDetails.setAmount(totalAmount);
            innerDetails.setCount(grouponByThirdCodeList.size());
            innerDetailsList.add(innerDetails);
        }
        return innerDetailsList;
    }

    public List<AmountItemDTO> changeData(List<ResponsePayWayDetail> list) {
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<AmountItemDTO> data = new ArrayList<>(list.size() + 1);
        for (int i = 0; i < list.size(); i++) {
            AmountItemDTO amountItemDTO = new AmountItemDTO();
            amountItemDTO.setCode(list.get(i).getPayWay());
            amountItemDTO.setName(list.get(i).getPayName());
            amountItemDTO.setAmount(list.get(i).getPayAmount());
            data.add(amountItemDTO);
        }
        return data;
    }

    @Override
    public HandoverPayDTO retailHandover(HandoverPayQueryDTO handoverPayQueryDTO) {
        HandoverPayDTO handoverPayDTO = new HandoverPayDTO();
        List<HandoverDTO> handover = transactionRecordMapper.retailHandover(handoverPayQueryDTO);
        if(CollUtil.isEmpty(handover)){
            log.info("零售营业数据为空");
            return handoverPayDTO;
        }
        Integer count = orderMapper.retailHandoverOrderCount(handoverPayQueryDTO);
        Map<String, BigDecimal> detail = handover.stream().filter(o -> Optional.ofNullable(o.getPaymentName()).isPresent())
                .collect(Collectors.toMap(HandoverDTO::getPaymentName, HandoverDTO::getAmount, BigDecimal::add));
        //支付详情
        handoverPayDTO.setIncomingDetailStr(detail);
        // 已结账订单数
        handoverPayDTO.setCheckedCount(count);
        // 销售收入
        handoverPayDTO.setSaleIncoming(handover.stream().map(HandoverDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        log.info("零售营业数据:{}",JacksonUtils.writeValueAsString(handoverPayDTO));
        return handoverPayDTO;
    }
}

