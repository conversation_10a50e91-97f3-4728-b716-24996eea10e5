package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 调整单
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_adjust_order")
public class AdjustOrderDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private Long guid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 调整单号
     */
    private String adjustNo;

    /**
     * 设备类型(订单来源 BaseDeviceTypeEnum)
     */
    private Integer deviceType;

    /**
     * 调整商品数量
     */
    private Integer adjustCount;

    /**
     * 调整金额
     */
    private BigDecimal adjustPrice;

    /**
     * 调整原因
     */
    private String reason;

    /**
     * 调整单金额
     */
    private BigDecimal orderFee;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    private String orderNo;

    /**
     * 交易模式(0：正餐，1：快餐，3：外卖)
     */
    private Integer tradeMode;

    /**
     * 操作人guid
     */
    private String createStaffGuid;

    /**
     * 操作人姓名
     */
    private String createStaffName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;
}
