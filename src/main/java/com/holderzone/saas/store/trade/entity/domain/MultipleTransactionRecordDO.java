package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单多次支付交易记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "订单多次支付交易记录")
@TableName("hst_multiple_transaction_record")
public class MultipleTransactionRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    @ApiModelProperty(value = "多次支付唯一主键")
    private Long guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "是否删除 0：false,1:true")
    private Boolean isDelete;

    @ApiModelProperty(value = "订单guid")
    private Long orderGuid;

    @ApiModelProperty(value = "交易记录guid")
    private Long transactionRecordGuid;

    @ApiModelProperty(value = "聚合支付商户的appId")
    private String appId;

    @ApiModelProperty(value = "聚合支付对应的支付方式")
    private Integer payPowerId;

    @ApiModelProperty(value = "聚合支付对应的支付方式名称")
    private String payPowerName;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品信息")
    private String body;

    @ApiModelProperty(value = "支付单附加描述")
    private String description;

    @ApiModelProperty(value = "设备号")
    private String terminalId;

    @ApiModelProperty(value = "聚合支付订单号")
    private String jhOrderGuid;

    @ApiModelProperty(value = "银行流水号（人脸支付流水号）")
    private String bankTransactionId;

    @ApiModelProperty(value = "支付二维码链接地址")
    private String codeUrl;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "聚合支付优惠金额")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "剩余可退款金额")
    private BigDecimal refundableFee;

    @ApiModelProperty(value = "交易类型，1:正常支付转入，2:会员充值转入，3:预付金转入，4:定金转入，5:正常支付退款，6:退单退款，7:会员余额退款，8:预付金退款，9:定金退款，10:自定义退款")
    private Integer tradeType;

    @ApiModelProperty(value = "1：待支付 2：支付中 3：支付失败 4：支付成功")
    private Integer state;

    @ApiModelProperty(value = "支付方式 1：现金支付，2：聚合支付，3：银行卡支付，4:会员卡支付，5:人脸支付，6:通吃岛支付，7:预定金支付，10：其他支付方式")
    private Integer paymentType;

    @ApiModelProperty(value = "支付方式名")
    private String paymentTypeName;

    @ApiModelProperty(value = "支付单创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "会员相关信息")
    private String memberGuid;

    @ApiModelProperty(value = "营业日时间")
    private LocalDate businessDay;

    @ApiModelProperty(value = "支付时间")
    private LocalDateTime paidTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "人脸支付失败时安卓自己随机加的3位数字")
    private String faceCode;

    @ApiModelProperty(value = "操作人员guid")
    private String staffGuid;

    @ApiModelProperty(value = "操作人名字")
    private String staffName;

    @ApiModelProperty(value = "门店名")
    private String storeName;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "反结账/并桌子桌原订单guid")
    private Long originalOrderGuid;

    @ApiModelProperty(value = "反结账原单支付记录id")
    private Long originalMultipleTransactionRecordGuid;
}
