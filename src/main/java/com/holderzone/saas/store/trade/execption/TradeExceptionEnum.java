package com.holderzone.saas.store.trade.execption;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeExceptionEnum
 * @date 2019/12/13 11:28
 * @description //TODO
 * @program IdeaProjects
 */
public enum TradeExceptionEnum {

    NO_NEED_PARMS("10000",null);

    String code;

    String message;

    TradeExceptionEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMessageByCode(String code){
        for(TradeExceptionEnum tradeExceptionEnum : values()){
            if(tradeExceptionEnum.code.equals(code)){
                return tradeExceptionEnum.message;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
