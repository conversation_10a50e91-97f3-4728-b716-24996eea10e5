package com.holderzone.saas.store.trade.utils.caculate;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.trade.entity.domain.FreeReturnItemDO;
import com.holderzone.saas.store.trade.entity.domain.ItemAttrDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;
import com.holderzone.saas.store.trade.entity.enums.FreeReturnTypeEnum;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/10 15:20
 */
public class ItemCaculatUtils extends ItemUtils{

    /**
     * 通过数据库查询结果包装ItemDto
     * @param orderItemDOS  itemdos
     * @param itemAttrDOList  attrlist
     * @param freeReturnItemDOList  dolist
     * @return  DineInItemDTO
     */
    public static List<DineInItemDTO> buildItem(List<OrderItemDO> orderItemDOS, List<ItemAttrDO> itemAttrDOList, List
            <FreeReturnItemDO> freeReturnItemDOList) {
        //附加费Attr  (String)ItemGuid -> List<ItemAttrDTO> 的映射
        Map<String, List<ItemAttrDTO>> guidToAttrDTOS = itemAttrDOList.stream()
                .collect(Collectors.groupingBy(consumer.compose(ItemAttrDO::getOrderItemGuid)
                        , Collectors.mapping(orderTransform::itemAttrDO2itemAttrDTO, Collectors.toList())));
        //赠送商品   (String)ItemGuid -> List<FreeItemDTO> 的映射
        Map<String, List<FreeItemDTO>> guidToFreeDTOS = freeReturnItemDOList.stream()
                .filter(returnItemDTO -> returnItemDTO.getType().equals(FreeReturnTypeEnum.FREE.getCode()))
                .collect(Collectors.groupingBy(consumer.compose(FreeReturnItemDO::getOrderItemGuid)
                        , Collectors.mapping(orderTransform::freeReturnItemDO2freeItemDTO, Collectors.toList())));
        //父子商品GUID,DO对应   (String)ItemParentGuid -> List<ChildOrderItemDO>
        Map<String, List<OrderItemDO>> parentGuidToChildGuid = orderItemDOS.stream()
                .filter(itemDo -> itemDo.getParentItemGuid() != null && itemDo.getParentItemGuid() != 0)
                .collect(Collectors.groupingBy(consumer.compose(OrderItemDO::getParentItemGuid)));
        //处理正式菜品
        return orderItemDOS.stream()
                //去掉 子类菜品
                .filter(itemDo -> itemDo.getParentItemGuid() == null || itemDo.getParentItemGuid() == 0)
                //转化为DTO
                .map(orderTransform::orderItemDO2DineInItemDTO)
                //装配附加费DTOS
                .peek(orderItemDto -> orderItemDto.setItemAttrDTOS(guidToAttrDTOS.get(orderItemDto.getGuid())))
                //装配单个属性价格
                .peek(orderItemDto -> orderItemDto.setSingleItemAttrTotal(getItemAttrTotal(orderItemDto, parentGuidToChildGuid, guidToAttrDTOS)))
                //装配套餐类商品 和 单位商品的套餐子项加价
                .peek(orderItemDto -> assemblingGroupInfo(orderItemDto, parentGuidToChildGuid.get(orderItemDto.getGuid()), guidToAttrDTOS))
                //赠送商品 装上
                .peek(orderItemDto -> assemblingFreeItemFunction(orderItemDto, guidToFreeDTOS.get(orderItemDto.getGuid())))
                //装配ItemPrice beforeItemPrice
                .peek(orderItemDto -> orderItemDto.setItemPiceAndBeforeItemPrice(getItemPrice(orderItemDto, orderItemDto.getCurrentCount(), false)))
                //集合
                .collect(Collectors.toList());
    }

    /**
     * 创建 ReturnItemDto
     * @param freeReturnItemDOList  赠送和免费的Item集合
     * @param dineInItemDTOS    原来得到的Item结果
     * @return
     */
    public static List<ReturnItemDTO> bulidReturnItem(List<FreeReturnItemDO> freeReturnItemDOList, List<DineInItemDTO> dineInItemDTOS) {
        Map<String, DineInItemDTO> guidToDto = dineInItemDTOS.stream().collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity()));
        // 返回退货的DTOS
        List<ReturnItemDTO> returnItemDTOS = freeReturnItemDOList.stream()
                .filter(returnItemDTO -> returnItemDTO.getType().equals(FreeReturnTypeEnum.RETURN.getCode()))
                .map(orderTransform::freeReturnItemDO2rerurnItemDTO)
                .peek(returnItemDTO -> returnItemDTO.setDineInItemDTO(guidToDto.get(returnItemDTO.getOrderItemGuid())))
                .peek(returnItemDTO -> returnItemDTO.setItemPrice(getItemPrice(returnItemDTO.getDineInItemDTO(),returnItemDTO.getCount(),false)))
                .collect(Collectors.toList());
        return returnItemDTOS;
    }



}