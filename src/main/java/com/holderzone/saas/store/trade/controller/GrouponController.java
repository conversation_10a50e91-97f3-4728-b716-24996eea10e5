package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.trade.anno.RequireOrderCheckLock;
import com.holderzone.saas.store.trade.repository.interfaces.GrouponMpService;
import com.holderzone.saas.store.trade.service.GrouponService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GrouponController
 * @date 2019/01/04 8:53
 * @description 团购接口
 * @program holder-saas-store-trade
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/groupon")
public class GrouponController {

    private final GrouponService grouponService;

    private final GrouponMpService grouponMpService;

    @ApiOperation(value = "验券", notes = "验券")
    @PostMapping("/verify")
    @RequireOrderCheckLock
    public List<GrouponListRespDTO> verify(@RequestBody GrouponReqDTO grouponReqDTO) {
        log.info("验券入参：{}", JacksonUtils.writeValueAsString(grouponReqDTO));
        return grouponService.verify(grouponReqDTO);
    }

    @ApiOperation(value = "撤销", notes = "撤销")
    @PostMapping("/undo")
    @RequireOrderCheckLock
    public void undo(@RequestBody GrouponReqDTO grouponReqDTO) {
        log.info("验券撤销入参：{}", JacksonUtils.writeValueAsString(grouponReqDTO));
        grouponService.revoke(grouponReqDTO);
    }

    @ApiOperation(value = "查询验券列表", notes = "查询验券列表")
    @GetMapping("/list")
    public List<GrouponListRespDTO> listByOrderGuid(@RequestParam("orderGuid") String orderGuid,
                                                    @RequestParam(value = "grouponType", required = false) Integer grouponType) {
        log.info("查询验券列表入参：orderGuid:{},grouponType:{} ", orderGuid, grouponType);
        return grouponService.listByOrderGuid(orderGuid, grouponType);
    }

    @ApiOperation(value = "处理历史数据", notes = "处理历史数据")
    @GetMapping("/handle")
    public void handleTradeDetailHistory(String enterpriseGuid, Long guid) {
        log.info("处理历史数据入参,enterpriseGuid:{}：guid:{}", enterpriseGuid, guid);
        grouponService.handleTradeDetailHistory(enterpriseGuid, guid);
    }

    @ApiOperation(value = "查询券信息", notes = "查询券信息")
    @PostMapping("/list_by_request")
    public List<AmountItemDTO> listByRequest(@RequestBody DailyReqDTO request) {
        log.info("查询券信息入参：request:{} ", request);
        return grouponMpService.listByRequest(request);
    }

    @ApiOperation(value = "查询券退款信息", notes = "查询券退款信息")
    @PostMapping("/list_refund_by_request")
    public List<AmountItemDTO> listRefundByRequest(@RequestBody StoreGatherReportReqDTO request) {
        log.info("[查询券退款信息]入参,request:{} ", request);
        return grouponMpService.listRefundByRequest(request);
    }

}
