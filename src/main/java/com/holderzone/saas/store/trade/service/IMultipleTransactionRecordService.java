package com.holderzone.saas.store.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.MultipleTransactionRecordDO;

import java.util.List;

/**
 * <p>
 * 订单多次支付交易记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface IMultipleTransactionRecordService extends IService<MultipleTransactionRecordDO> {

    List<MultipleTransactionRecordDO> listByOrderGuid(String orderGuid);

    List<MultipleTransactionRecordDO> listByOriginalMultipleTransactionRecordGuids(List<String> originalMultipleTransactionRecordGuids);

    List<MultipleTransactionRecordDO> listByOrderGuidList(List<Long> orderGuidList);

    void addBatchRefundAmount(List<MultipleTransactionRecordDO> recordDOList);
}
