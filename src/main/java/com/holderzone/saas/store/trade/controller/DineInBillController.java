package com.holderzone.saas.store.trade.controller;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.member.request.MemberConsumeReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberDTO;
import com.holderzone.saas.store.dto.order.request.bill.*;
import com.holderzone.saas.store.dto.order.response.OrderTableBillDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillMemberCardCalculateRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillRefundAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.trade.exception.OrderRefundLockException;
import com.holderzone.saas.store.dto.weixin.req.WxOrderNumberQuery;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.trade.anno.LocalizeRequireNotifyOrderGuids;
import com.holderzone.saas.store.trade.anno.RequireOrderCheckLock;
import com.holderzone.saas.store.trade.anno.RequireOrderPessimismLock;
import com.holderzone.saas.store.trade.anno.RequireTradePayLock;
import com.holderzone.saas.store.trade.constants.PaymentInitiateMethod;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.manager.DineInBillManager;
import com.holderzone.saas.store.trade.repository.feign.TableClientService;
import com.holderzone.saas.store.trade.repository.feign.WxClientService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderController
 * @date 2019/01/04 8:53
 * @description 结账接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/dine_in_bill")
@Api(description = "结账接口")
@Slf4j
@AllArgsConstructor
public class DineInBillController {

    private final BillService billService;

    private final CalculateService calculateService;

    private final AppendFeeService appendFeeService;

    private final RedisHelper redisHelper;

    private final RedisService redisService;

    private final WxClientService wxClientService;

    private final OrderService orderService;

    private final TableClientService tableClientService;

    private final DineInBillManager dineInBillManager;

    public static final String ORDER_RELATION_MEMBER_LOCK = "ORDER_RELATION_MEMBER_LOCK:%s";

    @ApiOperation(value = "计算账单优惠", notes = "计算账单优惠")
    @PostMapping("/calculate")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public DineinOrderDetailRespDTO calculate(@RequestBody BillCalculateReqDTO billCalculateReqDTO) {
        log.info("计算账单优惠入参：{}", JacksonUtils.writeValueAsString(billCalculateReqDTO));
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "门店：" + UserContextUtils.getStoreName() +
                "订单guid:" + billCalculateReqDTO.getOrderGuid() + "calculate耗时");
        String lockKey = "calculate:";
        boolean lockSuccess = false;
        try {
            lockKey += billCalculateReqDTO.getOrderGuid();
            lockSuccess = redisHelper.setNxEx(lockKey, "1", 30);
            if (!lockSuccess) {
                throw new ParameterException("正在计算账单优惠。。。");
            }
            DineinOrderDetailRespDTO calculate = calculateService.calculate(billCalculateReqDTO);
            stopWatch.stop();
            log.warn(stopWatch.prettyPrint());
            return calculate;
        } finally {
            if (lockSuccess) {
                redisHelper.delete(lockKey);
            }
        }
    }

    @ApiOperation(value = "支付", notes = "支付")
    @PostMapping("/pay")
    @RequireOrderPessimismLock
    @RequireTradePayLock
    @LocalizeRequireNotifyOrderGuids
    public EstimateItemRespDTO pay(@RequestBody BillPayReqDTO billPayReqDTO) {
        log.info("支付入参：{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        // 补充头部信息
        UserContext userContext = UserContextUtils.get();
        if (StringUtils.isEmpty(UserContextUtils.getStoreGuid()) && !StringUtils.isEmpty(billPayReqDTO.getStoreGuid())) {
            userContext.setStoreGuid(billPayReqDTO.getStoreGuid());
        }
        // pad支付增加结账人传入
        if (!ObjectUtils.isEmpty(billPayReqDTO.getDeviceType()) && StringUtils.isEmpty(UserContextUtils.getUserGuid())
                && Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), billPayReqDTO.getDeviceType())) {
            userContext.setUserGuid(billPayReqDTO.getUserGuid());
            userContext.setUserName(billPayReqDTO.getUserName());
        }
        UserContextUtils.put(userContext);
        String lockKey = "pay:";
        boolean lockSuccess = false;
        try {
            lockKey += billPayReqDTO.getOrderGuid();
            lockSuccess = redisHelper.setNxEx(lockKey, "1", 30);
            if (!lockSuccess) {
                log.info("pay重复调用");
                return null;
            }
            //解决重复打印的问题，已经聚合支付的请求不允许调用该接口;
            String paymentInitiateMethod = redisService.getOrderPaymentInitiateMethod(billPayReqDTO.getOrderGuid());
            if (!StringUtils.isEmpty(paymentInitiateMethod) && paymentInitiateMethod.equals(PaymentInitiateMethod.AGG_PAY)) {
                log.info("重复的支付请求，已经忽略");
                EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
                estimateItemRespDTO.setResult(Boolean.TRUE);
                return estimateItemRespDTO;
            }

            StopWatch stopWatch = new StopWatch();
            stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "门店：" + UserContextUtils.getStoreName() +
                    "订单guid:" + billPayReqDTO.getOrderGuid() + "pay耗时");
            EstimateItemRespDTO pay = billService.pay(billPayReqDTO);
            stopWatch.stop();
            // 调用接口删除扫码点餐的点餐人数
            removeWxOrderNumber(billPayReqDTO);
            log.warn(stopWatch.prettyPrint());
            return pay;
        } finally {
            if (lockSuccess) {
                redisHelper.delete(lockKey);
            }
        }

    }


    @ApiOperation(value = "订单结账完成", notes = "订单结账完成")
    @PostMapping("/checkout/success")
    public void orderCheckoutSuccess(@RequestBody BillPayReqDTO billPayReqDTO) {
        log.info("订单结账完成入参:{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        String lockKey = "checkout_success:";
        boolean lockSuccess = false;
        try {
            lockKey += billPayReqDTO.getOrderGuid();
            lockSuccess = redisHelper.setNxEx(lockKey, "1", 30);
            if (!lockSuccess) {
                log.warn("orderCheckoutSuccess重复调用");
                return;
            }
            billService.orderCheckoutSuccess(billPayReqDTO);
        } finally {
            if (lockSuccess) {
                redisHelper.delete(lockKey);
            }
        }
    }


    /**
     * 删除微信点餐人数
     */
    private void removeWxOrderNumber(BillPayReqDTO billPayReqDTO) {
        // 调用接口删除扫码点餐的点餐人数
        OrderDO orderDO = orderService.getById(billPayReqDTO.getOrderGuid());
        if (orderDO.getTradeMode() == 0 && !StringUtils.isEmpty(orderDO.getDiningTableGuid())) {
            WxOrderNumberQuery query = new WxOrderNumberQuery();
            query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            query.setStoreGuid(UserContextUtils.getStoreGuid());
            query.setDiningTableGuid(orderDO.getDiningTableGuid());
            query.setTradeMode(orderDO.getTradeMode());
            // 正餐
            TableBasicDTO tableBasicDTO = tableClientService.queryTableInfo(orderDO.getDiningTableGuid());
            log.info("tableBasicDTO={}", JacksonUtils.writeValueAsString(tableBasicDTO));
            if (Objects.nonNull(tableBasicDTO)) {
                query.setAreaGuid(tableBasicDTO.getAreaGuid());
            }
            log.info("删除缓存的微信点餐人数 query={}", JacksonUtils.writeValueAsString(query));
            wxClientService.deleteOrderNumber(query);
        }
    }

    private final static int AUTH_CODE_LENGTH = 18;

    @ApiOperation(value = "聚合支付", notes = "聚合支付")
    @PostMapping("/agg_pay")
    @RequireTradePayLock
    @LocalizeRequireNotifyOrderGuids
    public BillAggPayRespDTO aggPay(@RequestBody BillAggPayReqDTO billPayReqDTO) throws InterruptedException {
        log.info("聚合支付入参：{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        //bugfix:21675 必传authcode字段
        String authCode = billPayReqDTO.getAuthCode();
        checkAuthCodeEmpty(authCode, billPayReqDTO.getActiveScan());
        // 使用此接口支付，则必然是最后一次支付
        billPayReqDTO.setCheckoutSuccessFlag(Boolean.TRUE);

        if (StringUtils.isEmpty(authCode)) {
            return toAggPay(billPayReqDTO);
        }
        boolean wxOrAliPay = authCode.startsWith("1") || authCode.startsWith("2");
        if (wxOrAliPay && authCode.length() != AUTH_CODE_LENGTH) {
            throw new BusinessException("二维码有误请重新扫码！");
        }
        return toAggPay(billPayReqDTO);

    }


    private void checkAuthCodeEmpty(String authCode, Integer scan) {
        if (!StringUtils.isEmpty(authCode)) {
            return;
        }
        //如果是主扫
        if (isActiveScan(scan)) {
            return;
        }
        throw new BusinessException("请使用正确的二维码！");
    }

    private boolean isActiveScan(Integer scan) {
        //主扫
        return scan != null && scan == BooleanEnum.TRUE.getCode();
    }

    private BillAggPayRespDTO toAggPay(BillAggPayReqDTO billPayReqDTO) throws InterruptedException {
        String lockKey = String.format("aggpay:%s-%d", billPayReqDTO.getOrderGuid(), billPayReqDTO.getVersion());
        //预支付锁
        String advanceLockKey = String.format("ADVANCE_PAY:%s", billPayReqDTO.getOrderGuid());
        boolean lockSuccess = false;
        AtomicInteger count = new AtomicInteger(0);
        try {
            do {
                lockSuccess = redisHelper.setNxEx(lockKey, "1", 30);
                if (lockSuccess) {
                    break;
                }
                log.info("重复调用聚合支付 {}", billPayReqDTO.getOrderGuid());
                Thread.sleep(200);
                if (count.incrementAndGet() > 10) {
                    throw new BusinessException("调用聚合支付超时");
                }

            } while (true);
            String redisRetJson = redisHelper.get(advanceLockKey);
            if (!StringUtils.hasText(redisRetJson)) {
                return buildRespAgg(advanceLockKey, billPayReqDTO);
            }
            BillAggPayRespDTO redisRet = JacksonUtils.toObject(BillAggPayRespDTO.class, redisRetJson);
            if (redisRet.getResult() != null && redisRet.getResult()) {
                return redisRet;
            }
            return buildRespAgg(advanceLockKey, billPayReqDTO);
        } finally {
            if (lockSuccess) {
                redisHelper.delete(lockKey);
            }
        }
    }

    private BillAggPayRespDTO buildRespAgg(String advanceLockKey, BillAggPayReqDTO billPayReqDTO) {
        BillAggPayRespDTO billAggPayRespDTO = billService.aggPay(billPayReqDTO);
        if (billAggPayRespDTO.getResult()) {
            redisHelper.setEx(advanceLockKey, JacksonUtils.writeValueAsString(billAggPayRespDTO), 1, TimeUnit.MINUTES);
        }
        log.info("聚合支付返回值：{}", JacksonUtils.writeValueAsString(billAggPayRespDTO));
        return billAggPayRespDTO;
    }

    @ApiOperation(value = "聚合支付回调", notes = "聚合支付回调")
    @PostMapping("/callback")
    public String aggCallBack(@RequestBody SaasNotifyDTO saasNotifyDTO) {
        return billService.aggCallBack(saasNotifyDTO);
    }

    @ApiOperation(value = "反结账", notes = "反结账")
    @PostMapping("/recovery")
    @LocalizeRequireNotifyOrderGuids
    public String recovery(@RequestBody RecoveryReqDTO recoveryReqDTO) {
        log.info("反结账入参：{}", JacksonUtils.writeValueAsString(recoveryReqDTO));
        return billService.recovery(recoveryReqDTO);

    }

    @ApiOperation(value = "校验是否有聚合支付退款", notes = "校验是否有聚合支付退款")
    @PostMapping("/validat_agg_refund")
    public Boolean validatAggRefund(@RequestBody ValidatAggReturnReqDTO validatAggReturnReqDTO) {
        log.info("校验是否有聚合支付退款入参：{}", JacksonUtils.writeValueAsString(validatAggReturnReqDTO));
        return billService.validatAggRefund(validatAggReturnReqDTO);
    }

    @ApiOperation(value = "退款", notes = "退款")
    @PostMapping("/refund")
    @RequireTradePayLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean refund(@RequestBody RefundReqDTO refundReqDTO) throws InterruptedException {
        log.info("退款入参：{}", JacksonUtils.writeValueAsString(refundReqDTO));
        String lockKey = String.format("refund:%s-%d", refundReqDTO.getOrderGuid(), refundReqDTO.getVersion());
        boolean lockSuccess = false;
        AtomicInteger count = new AtomicInteger(0);
        try {
            do {
                lockSuccess = redisHelper.setNxEx(lockKey, "1", 30);
                if (!lockSuccess) {
                    log.info("重复调用退款");
                    Thread.sleep(200);
                    if (count.incrementAndGet() > 10) {
                        throw new BusinessException("调用退款超时");
                    }
                }
            } while (!lockSuccess);
            String redisRet = redisHelper.get(lockKey + ":result");
            if (StringUtils.hasText(redisRet) && "1".equals(redisRet)) {
                return true;
            }
            boolean ret = billService.refund(refundReqDTO);
            redisHelper.setEx(lockKey + ":result", ret ? "1" : "0", 24, TimeUnit.HOURS);
            return ret;
        } finally {
            if (lockSuccess) {
                redisHelper.delete(lockKey);
            }
        }
    }

    @ApiOperation(value = "会员消费记录列表", notes = "会员消费记录列表")
    @PostMapping("/member_consume_records")
    public Page<MemberConsumeRespDTO> memberConsumeRecords(@RequestBody MemberConsumeReqDTO memberConsumeReqDTO) {
        return billService.memberConsumeRecords(memberConsumeReqDTO);
    }

    @ApiOperation(value = "附加费列表", notes = "附加费列表")
    @PostMapping("/append_fee_list")
    public List<AppendFeeDetailDTO> appendFeeList(@RequestBody SingleDataDTO singleDataDTO) {
        return appendFeeService.appendFeeList(singleDataDTO);
    }

    @ApiOperation(value = "订单关联多卡", notes = "订单关联多卡")
    @PostMapping("/relation_multi_member")
    public void relationOrderMultiMember(@RequestBody OrderMultiMemberDTO reqDTO) {
        String lockKey = String.format(ORDER_RELATION_MEMBER_LOCK, reqDTO.getOrderGuid());
        try {
            boolean lockResult = RedissonLockUtil.tryLock(lockKey, 10, 30);
            if (!lockResult) {
                throw new OrderRefundLockException("订单添加多卡存在冲突，需重新提交");
            }
            calculateService.relationOrderMultiMember(reqDTO);
        } catch (Exception e) {
            log.error("订单关联多卡失败,e:", e);
            throw new BusinessException(e.getMessage());
        } finally {
            RedissonLockUtil.unlock(lockKey);
        }
    }

    @ApiOperation(value = "查询订单关联多卡", notes = "查询订单关联多卡")
    @GetMapping("/query_relation_multi_member")
    public List<OrderMultiMemberDTO> queryRelationOrderMultiMember(String orderGuid) {
        return calculateService.queryRelationOrderMultiMember(orderGuid);
    }

    @RequireTradePayLock
    @ApiOperation(value = "多次支付")
    @PostMapping("/multiple_pay")
    public BillAggPayRespDTO multiplePay(@RequestBody BillMultiplePayReqDTO multiplePayReqDTO) {
        log.info("多次支付入参：{}", JacksonUtils.writeValueAsString(multiplePayReqDTO));
        return dineInBillManager.multiplePay(multiplePayReqDTO);
    }

    @RequireTradePayLock
    @ApiOperation(value = "聚合支付部分退款")
    @PostMapping("/multiple_refund_agg_pay")
    public BillRefundAggPayRespDTO multipleRefundAggPay(@RequestBody BillMultipleRefundAggPayReqDTO multipleRefundAggPayReqDTO) {
        log.info("聚合支付部分退款入参：{}", JacksonUtils.writeValueAsString(multipleRefundAggPayReqDTO));
        return dineInBillManager.multipleRefundAggPay(multipleRefundAggPayReqDTO);
    }

    @Resource
    private OrderTableBillService orderTableBillService;

    @ApiOperation(value = "启动手动清台处理")
    @PostMapping("/deal_enable_handle_close")
    public OrderTableBillDTO dealEnableHandleClose(@RequestBody BillPayReqDTO billPayReqDTO) {
        return orderTableBillService.dealEnableHandleClose(billPayReqDTO);
    }

    @ApiOperation(value = "麓豆会员登录", notes = "麓豆会员登录")
    @PostMapping("/ludou_member_login")
    public BillMemberCardCalculateRespDTO ludouMemberLogin(@RequestBody BilMemberCardCalculateReqDTO cardCalculateReqDTO) {
        log.info("麓豆会员登录入参:{}", JacksonUtils.writeValueAsString(cardCalculateReqDTO));
        return calculateService.relationOrderLudouMember(cardCalculateReqDTO);
    }

    @ApiOperation(value = "麓豆会员登出", notes = "麓豆会员登出")
    @PostMapping("/ludou_member_logout")
    public void ludouMemberLogout(@RequestBody BilMemberCardCalculateReqDTO cardCalculateReqDTO) {
        log.info("麓豆会员登出入参:{}", JacksonUtils.writeValueAsString(cardCalculateReqDTO));
        calculateService.removeOrderLudouMember(cardCalculateReqDTO);
    }

    @ApiOperation(value = "订单反结账是否超时", notes = "订单反结账是否超时")
    @PostMapping("/recovery/time_limit")
    public Boolean recoveryTimeLimit(@RequestBody RecoveryReqDTO recoveryReqDTO) {
        log.info("订单反结账是否超时入参：{}", JacksonUtils.writeValueAsString(recoveryReqDTO));
        return billService.recoveryTimeLimit(recoveryReqDTO.getOrderGuid());
    }
}
