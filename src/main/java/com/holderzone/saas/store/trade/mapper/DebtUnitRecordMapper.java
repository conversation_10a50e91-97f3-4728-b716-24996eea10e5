package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.trade.entity.domain.DebtUnitRecordDO;
import com.holderzone.saas.store.trade.entity.dto.DebtRepaymentFeeTotalDTO;
import com.holderzone.saas.store.trade.entity.query.DebtRepaymentFeeTotalQuery;
import com.holderzone.saas.store.trade.entity.query.DebtUnitRecordQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/12/15 15:58
 * @description
 */
@Repository
public interface DebtUnitRecordMapper extends BaseMapper<DebtUnitRecordDO> {

    IPage<DebtUnitRecordDO> pageDebtUnitRecord(IPage<DebtUnitRecordDO> iPage, @Param("debtUnitRecordQuery") DebtUnitRecordQuery debtUnitRecordQuery);

    BigDecimal queryDebtUnitTotal(@Param("unitGuid")String unitGuid);

    DebtUnitRecordDO getByOrderGuid(@Param("orderGuid") Long orderGuid);

    /**
     * 查询单位是否已存在挂账记录
     * @param unitGuid 单位guid
     * @return 是否
     */
    Boolean existsRecord(@Param("unitGuid") String unitGuid);

    /***
     * 查询交接班还款汇总数据
     * @param queryRepaymentFeeTotal 请参数
     * @return 返回参数
     */
    List<DebtRepaymentFeeTotalDTO> queryRepaymentFeeTotal(@Param("dto")DebtRepaymentFeeTotalQuery queryRepaymentFeeTotal);
}
