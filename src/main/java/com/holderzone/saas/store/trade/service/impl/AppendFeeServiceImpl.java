package com.holderzone.saas.store.trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.enums.order.AppendFeeTypeEnum;
import com.holderzone.saas.store.trade.anno.PerformanceCheck;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.AppendFeeDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderExtendsDO;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.mapper.AppendFeeMapper;
import com.holderzone.saas.store.trade.repository.feign.BusinessClientService;
import com.holderzone.saas.store.trade.repository.feign.TableClientService;
import com.holderzone.saas.store.trade.repository.interfaces.AppendFeeMpService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderExtendsService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.AppendFeeService;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GrouponServiceImpl
 * @date 2019/01/28 17:28
 * @description
 * @program holder-saas-store-trade
 */
@Slf4j
@Service
@AllArgsConstructor
public class AppendFeeServiceImpl extends ServiceImpl<AppendFeeMapper, AppendFeeDO> implements AppendFeeService {

    public static final String APPEND_FEE_PREFIX = "appendFee-tableGuid-";
    private final BusinessClientService businessClientService;

    private final OrderService orderService;

    private final OrderExtendsService orderExtendsService;

    private final TableClientService tableClientService;

    private final AppendFeeMpService appendFeeMpService;

    private final DynamicHelper dynamicHelper;
    private final RedisTemplate<String, List<SurchargeLinkDTO>> surchargeRedisTemplate;
    private final RedisTemplate<String, List<AppendFeeDO>> appendFeeRedisTemplate;
    private final AppendFeeMapper appendFeeMapper;

    public BigDecimal getAppendFee(OrderDO orderDO, List<AppendFeeDO> appendFeeList, List<SurchargeLinkDTO> surchargeLinkList) {
        BigDecimal totalAppendFee = BigDecimal.ZERO;
        if (CollectionUtil.isEmpty(surchargeLinkList)) {
            return totalAppendFee;
        }
        // 查询订单扩展表
        OrderExtendsDO orderExtendsDO = Optional.ofNullable(orderDO.getExtendsDO())
                .orElse(orderExtendsService.getById(orderDO.getGuid()));
        orderExtendsDO = Optional.ofNullable(orderExtendsDO).orElse(new OrderExtendsDO());
        Boolean associatedFlag = Optional.ofNullable(orderExtendsDO.getAssociatedFlag()).orElse(Boolean.FALSE);
        int associatedTableSize = StringUtils.isNotEmpty(orderExtendsDO.getAssociatedTableGuids()) ?
                JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableGuids()).size() : 1;
        for (SurchargeLinkDTO surchargeLinkDTO : surchargeLinkList) {
            if (surchargeLinkDTO.getType() == 1 && UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
                continue;
            }
            AppendFeeDO appendFeeDO = new AppendFeeDO();
            appendFeeDO.setUnitPrice(surchargeLinkDTO.getAmount());
            appendFeeDO.setAreaGuid(surchargeLinkDTO.getAreaGuid());
            appendFeeDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_APPEND_FEE));
            appendFeeDO.setName(surchargeLinkDTO.getName());
            appendFeeDO.setOrderGuid(orderDO.getGuid());
            appendFeeDO.setType(surchargeLinkDTO.getType());
            appendFeeDO.setStaffGuid(UserContextUtils.getUserGuid());
            appendFeeDO.setStaffName(UserContextUtils.getUserName());
            appendFeeDO.setStoreGuid(UserContextUtils.getStoreGuid());
            appendFeeDO.setStoreName(UserContextUtils.getStoreName());
            BigDecimal amount = BigDecimal.ZERO;
            // 按人
            if (surchargeLinkDTO.getType() == 0) {
                amount = surchargeLinkDTO.getAmount().multiply(new BigDecimal(orderDO.getGuestCount()));
            }
            // 按桌
            if (surchargeLinkDTO.getType() == 1) {
                amount = surchargeLinkDTO.getAmount();
                // 如果是联台单 则需要乘以桌数
                if (Boolean.TRUE.equals(associatedFlag)) {
                    amount = amount.multiply(new BigDecimal(associatedTableSize));
                }
            }
            totalAppendFee = totalAppendFee.add(amount);
            appendFeeDO.setAmount(amount);
            appendFeeList.add(appendFeeDO);
        }
        return totalAppendFee;
    }

    private String getAppendFeeRedisKey(Long orderGuid) {
        return APPEND_FEE_PREFIX + orderGuid;
    }

    @Override
    public BigDecimal getAndUpdateAppendFee(OrderDO orderDO, String areaGuid) {
        log.info("计算订单附加费入参:{}", JacksonUtils.writeValueAsString(orderDO));
        // 重新生成orderDO 避免修改了DO的数据
        OrderDO orderInDb = JacksonUtils.toObject(OrderDO.class, JacksonUtils.writeValueAsString(orderDO));
        if (Objects.equals(UpperStateEnum.SAME_SUB.getCode(), orderInDb.getUpperState())) {
            // 如果是结账子单 则根据点餐人数判断是否不重复计算附加费
            orderInDb.setPreGuestCount(Optional.ofNullable(orderInDb.getPreGuestCount()).orElse(orderInDb.getGuestCount()));
            if (orderInDb.getGuestCount() <= orderInDb.getPreGuestCount()) {
                return BigDecimal.ZERO;
            }
            orderInDb.setGuestCount(orderInDb.getGuestCount() - orderInDb.getPreGuestCount());
        }
        String surchargeRedisKey = "surcharge-config-" + orderInDb.getDiningTableGuid();
        String appendFeeRedisKey = getAppendFeeRedisKey(orderInDb.getGuid());
        List<SurchargeLinkDTO> surchargeLinkDTOS = surchargeRedisTemplate.opsForValue().get(surchargeRedisKey);
        if (surchargeLinkDTOS == null) {
            //查询桌台信息
            String areaGuidByTableGuid = areaGuid == null ? tableClientService.getAreaGuidByTableGuid(orderInDb.getDiningTableGuid()) : areaGuid;
            //查询附加费信息
            Map<String, List<SurchargeLinkDTO>> stringListMap = businessClientService.listByAreaGuid(Lists.newArrayList(areaGuidByTableGuid));
            surchargeLinkDTOS = stringListMap.get(areaGuidByTableGuid);
            if (surchargeLinkDTOS == null) {
                surchargeLinkDTOS = new ArrayList<>();
            }
            surchargeRedisTemplate.opsForValue().set(surchargeRedisKey, surchargeLinkDTOS, 10, TimeUnit.SECONDS);
        }
        List<AppendFeeDO> appendFeeList = new ArrayList<>();
        BigDecimal totalAppendFee = getAppendFee(orderInDb, appendFeeList, surchargeLinkDTOS);
        //之前为了解决12152没有统计子订单附加费的问题，思路是将子单的附加费全部保存到主单中，但这种改法又引出了更多bug-_-|||
        //现在改为子单的附加费按子单保存，查询的时候将子单附加费也查询出来
//        if (Objects.nonNull(orderDO.getUpperState()) && orderDO.getUpperState() == UpperStateEnum.MAIN.getCode()) {
//            //如果是并台后的主单，将子单的附加费金额加到主单对应的附加费中
//            appendFeeList = mergeSubAppendFee(String.valueOf(orderDO.getGuid()), appendFeeList);
//        }
        appendFeeRedisTemplate.opsForValue().set(appendFeeRedisKey, appendFeeList, 24, TimeUnit.HOURS);
        return totalAppendFee;
    }

    @Override
    public BigDecimal getAndUpdateAppendFeeByOriginal(OrderDO orderDO) {
        Long originalOrderGuid = orderDO.getOriginalOrderGuid();
        List<AppendFeeDO> appendFeeDOList = appendFeeMpService.listByOrderGuid(originalOrderGuid);
        if (CollectionUtils.isEmpty(appendFeeDOList)) {
            return BigDecimal.ZERO;
        }

        // 查询订单扩展表
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderDO.getGuid());
        orderExtendsDO = Optional.ofNullable(orderExtendsDO).orElse(new OrderExtendsDO());
        Boolean associatedFlag = Optional.ofNullable(orderExtendsDO.getAssociatedFlag()).orElse(Boolean.FALSE);
        int associatedTableSize = StringUtils.isNotEmpty(orderExtendsDO.getAssociatedTableGuids()) ?
                JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableGuids()).size() : 1;

        BigDecimal appendFee = BigDecimal.ZERO;
        for (AppendFeeDO appendFeeDO : appendFeeDOList) {
            BigDecimal appendAmount = BigDecimal.ZERO;
            //按桌
            if (appendFeeDO.getType() == AppendFeeTypeEnum.BY_TABLE.getCode()) {
                appendAmount = appendFeeDO.getUnitPrice();
                // 如果是联台单 则需要乘以桌数
                if (Boolean.TRUE.equals(associatedFlag)) {
                    appendAmount = appendAmount.multiply(new BigDecimal(associatedTableSize));
                }
            }
            //按人
            if (appendFeeDO.getType() == AppendFeeTypeEnum.BY_NUM.getCode()) {
                appendAmount = appendFeeDO.getUnitPrice().multiply(new BigDecimal(orderDO.getGuestCount()));
            }
            appendFee = appendFee.add(appendAmount);
            appendFeeDO.setAmount(appendAmount);
            appendFeeDO.setOrderGuid(orderDO.getGuid());
            appendFeeDO.setStaffGuid(UserContextUtils.getUserGuid());
            appendFeeDO.setStaffName(UserContextUtils.getUserName());
        }

        String appendFeeRedisKey = getAppendFeeRedisKey(orderDO.getGuid());
        appendFeeRedisTemplate.opsForValue().set(appendFeeRedisKey, appendFeeDOList, 24, TimeUnit.HOURS);
        return appendFee;
    }

    @Override
    public BigDecimal calculateAppendFee(OrderDO orderDO, List<SurchargeLinkDTO> surchargeLinkList) {
        List<AppendFeeDO> appendFeeList = new ArrayList<>();
        BigDecimal totalAppendFee = getAppendFee(orderDO, appendFeeList, surchargeLinkList);
        if (!CollectionUtils.isEmpty(appendFeeList)) {
            appendFeeMpService.saveBatch(appendFeeList);
            String appendFeeRedisKey = getAppendFeeRedisKey(orderDO.getGuid());
            appendFeeRedisTemplate.opsForValue().set(appendFeeRedisKey, appendFeeList, 24, TimeUnit.HOURS);
        }
        return totalAppendFee;
    }

    @Override
    public BigDecimal updateAppendFee(String orderGuid, int guestCount) {
        List<AppendFeeDO> appendFeeDOList = appendFeeMpService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(appendFeeDOList)) {
            return BigDecimal.ZERO;
        }

        BigDecimal appendFee = BigDecimal.ZERO;
        for (AppendFeeDO appendFeeDO : appendFeeDOList) {
            BigDecimal appendAmount = BigDecimal.ZERO;
            //按桌
            if (appendFeeDO.getType() == AppendFeeTypeEnum.BY_TABLE.getCode()) {
                appendAmount = appendFeeDO.getUnitPrice();
            }
            //按人
            if (appendFeeDO.getType() == AppendFeeTypeEnum.BY_NUM.getCode()) {
                appendAmount = appendFeeDO.getUnitPrice().multiply(new BigDecimal(guestCount));
            }
            appendFee = appendFee.add(appendAmount);
            appendFeeDO.setAmount(appendAmount);
        }
        appendFeeMpService.updateBatchById(appendFeeDOList);
        return appendFee;
    }


    @Override
    public List<AppendFeeDetailDTO> appendFeeList(SingleDataDTO singleDataDTO) {
        List<AppendFeeDO> appendFeeList = appendFeeMpService.listByOrderGuid(singleDataDTO.getData());
        if (CollectionUtils.isEmpty(appendFeeList)) {
            String appendFeeRedisKey = getAppendFeeRedisKey(Long.valueOf(singleDataDTO.getData()));
            appendFeeList = appendFeeRedisTemplate.opsForValue().get(appendFeeRedisKey);
        }
        if (CollectionUtils.isEmpty(appendFeeList)) {
            return new ArrayList<>();
        }
        Map<String, AppendFeeDetailDTO> appendFeeDetailMap = new HashMap<>();
        appendFeeList.forEach(appendFeeDO -> {
            AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
            appendFeeDetailDTO.setAmount(appendFeeDO.getAmount());
            appendFeeDetailDTO.setName(appendFeeDO.getName());
            appendFeeDetailDTO.setType(appendFeeDO.getType());
            appendFeeDetailDTO.setUnitPrice(appendFeeDO.getUnitPrice());
            appendFeeDetailMap.merge(appendFeeDO.getName(), appendFeeDetailDTO, (prev, next) -> {
                prev.setAmount(prev.getAmount().add(next.getAmount()));
                return prev;
            });
        });
        return new ArrayList<>(appendFeeDetailMap.values());
    }

    @Override
    public List<AppendFeeDO> persistAppendFee(Long orderGuid) {
        //查询出子订单的guid，子订单的附加费也要保存（12152）
        List<Long> orderGuidList = getMainAndSubOrderGuid("" + orderGuid);
        List<AppendFeeDO> appendFeeDOList = Lists.newArrayList();
        orderGuidList.forEach(o -> {
            //bugfixed：20771
            List<AppendFeeDO> appendFeeDOS = appendFeeMpService.listByOrderGuid(o);
            if (CollectionUtil.isNotEmpty(appendFeeDOS)) {
                List<String> appends = appendFeeDOS.stream().map(AppendFeeDO::getGuid).map(String::valueOf).collect(Collectors.toList());
                appendFeeMapper.deleteAppendByIds(appends);
            }

            String appendFeeRedisKey = getAppendFeeRedisKey(o);
            List<AppendFeeDO> appendFeeList = appendFeeRedisTemplate.opsForValue().get(appendFeeRedisKey);
            if (CollectionUtil.isNotEmpty(appendFeeList)) {
                log.info("persistAppendFee appendFeeList={}", JacksonUtils.writeValueAsString(appendFeeList));
                appendFeeMpService.saveOrUpdateBatch(appendFeeList);
                appendFeeDOList.addAll(appendFeeList);
            }
        });
        return appendFeeDOList;
    }

    @Override
    public List<AppendFeeDO> persistAppendFee(String orderGuid) {
        if (StringUtils.isBlank(orderGuid)) {
            log.error("订单guid不能为空");
            throw new BusinessException("订单guid不能为空");
        }
        return persistAppendFee(Long.valueOf(orderGuid));
    }

    @Override
    @PerformanceCheck
    public List<AppendFeeDetailDTO> getOrderAllAppendFeeList(String orderGuid) {
        //appendFeeList接口引用地方较多，新加一个查所有附加费的接口
        List<Long> orderGuidList = getMainAndSubOrderGuid(orderGuid);
        List<AppendFeeDO> appendFeeList = appendFeeMpService.listByOrderGuids(orderGuidList);
        if (CollectionUtils.isEmpty(appendFeeList)) {
            //结账时才会将附加费入库，结账之前从缓存查询，也需查询所有子单的
            for (Long guid : orderGuidList) {
                String appendFeeRedisKey = getAppendFeeRedisKey(guid);
                appendFeeList.addAll(Optional.ofNullable(appendFeeRedisTemplate.opsForValue().get(appendFeeRedisKey))
                        .orElse(new ArrayList<>()));
            }
        }
        if (CollectionUtils.isEmpty(appendFeeList)) {
            return new ArrayList<>();
        }
        Map<String, AppendFeeDetailDTO> appendFeeDetailMap = new HashMap<>();
        appendFeeList.forEach(appendFeeDO -> {
            AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
            appendFeeDetailDTO.setGuid(appendFeeDO.getGuid());
            appendFeeDetailDTO.setOrderGuid(appendFeeDO.getOrderGuid());
            appendFeeDetailDTO.setAmount(appendFeeDO.getAmount());
            appendFeeDetailDTO.setName(appendFeeDO.getName());
            appendFeeDetailDTO.setType(appendFeeDO.getType());
            appendFeeDetailDTO.setUnitPrice(appendFeeDO.getUnitPrice());
            appendFeeDetailDTO.setDiscountAmount(Optional.ofNullable(appendFeeDO.getDiscountAmount()).orElse(BigDecimal.ZERO));
            appendFeeDetailDTO.setRefundAmount(appendFeeDO.getRefundAmount());
            appendFeeDetailDTO.setRefundCount(BigDecimal.ZERO);
            appendFeeDetailDTO.setRefundShareAmount(appendFeeDO.getRefundShareAmount());
            if (BigDecimalUtil.greaterThanZero(appendFeeDetailDTO.getRefundAmount())) {
                appendFeeDetailDTO.setRefundCount(appendFeeDetailDTO.getRefundAmount().divide(appendFeeDetailDTO.getUnitPrice(), 2, RoundingMode.DOWN));
                if (BigDecimalUtil.greaterThanZero(appendFeeDO.getRefundCount())) {
                    appendFeeDetailDTO.setRefundCount(appendFeeDO.getRefundCount());
                }
            }
            appendFeeDetailMap.merge(appendFeeDO.getName(), appendFeeDetailDTO, (prev, next) -> {
                prev.setAmount(prev.getAmount().add(next.getAmount()));

                BigDecimal prevDiscountAmount = Optional.ofNullable(prev.getDiscountAmount()).orElse(BigDecimal.ZERO);
                BigDecimal nextDiscountAmount = Optional.ofNullable(next.getDiscountAmount()).orElse(BigDecimal.ZERO);
                prev.setDiscountAmount(prevDiscountAmount.add(nextDiscountAmount));

                BigDecimal prevRefundAmount = Optional.ofNullable(prev.getRefundAmount()).orElse(BigDecimal.ZERO);
                BigDecimal nextRefundAmount = Optional.ofNullable(next.getRefundAmount()).orElse(BigDecimal.ZERO);
                prev.setRefundAmount(prevRefundAmount.add(nextRefundAmount));

                BigDecimal prevRefundCount = Optional.ofNullable(prev.getRefundCount()).orElse(BigDecimal.ZERO);
                BigDecimal nextRefundCount = Optional.ofNullable(next.getRefundCount()).orElse(BigDecimal.ZERO);
                prev.setRefundCount(prevRefundCount.add(nextRefundCount));
                return prev;
            });
        });
        return new ArrayList<>(appendFeeDetailMap.values());
    }

    @Override
    public List<SurchargeLinkDTO> getSurchargeLinkList(String orderGuid) {
        List<AppendFeeDO> appendFeeList = appendFeeMpService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(appendFeeList)) {
            return new ArrayList<>();
        }
        List<SurchargeLinkDTO> surchargeList = new ArrayList<>();
        appendFeeList.forEach(appendFee -> {
            SurchargeLinkDTO dto = new SurchargeLinkDTO();
            dto.setOrderGuid(String.valueOf(appendFee.getOrderGuid()));
            dto.setName(appendFee.getName());
            dto.setAmount(appendFee.getAmount());
            dto.setUnitPrice(appendFee.getUnitPrice());
            dto.setType(appendFee.getType());
            dto.setTradeMode(appendFee.getTradeMode());
            surchargeList.add(dto);
        });
        return surchargeList;
    }

    @Override
    public List<SurchargeLinkDTO> getSurchargeLinkList(List<String> orderGuidList) {
        List<AppendFeeDO> appendFeeList = appendFeeMpService.listByOrderGuids(orderGuidList);
        if (CollectionUtils.isEmpty(appendFeeList)) {
            return new ArrayList<>();
        }
        List<SurchargeLinkDTO> surchargeList = new ArrayList<>();
        appendFeeList.forEach(appendFee -> {
            SurchargeLinkDTO dto = new SurchargeLinkDTO();
            dto.setOrderGuid(String.valueOf(appendFee.getOrderGuid()));
            dto.setName(appendFee.getName());
            dto.setAmount(appendFee.getAmount());
            dto.setUnitPrice(appendFee.getUnitPrice());
            dto.setType(appendFee.getType());
            dto.setTradeMode(appendFee.getTradeMode());
            surchargeList.add(dto);
        });
        return surchargeList;
    }

    /**
     * 获取主单下的子单guid集合并与主单guid合并
     *
     * @param mainOrderGuid 主单guid
     * @return 主单与子单guid集合
     */
    private List<Long> getMainAndSubOrderGuid(String mainOrderGuid) {
        List<Long> orderGuidList = new ArrayList<>();
        orderGuidList.add(Long.parseLong(mainOrderGuid));
        List<OrderDO> subOrders = orderService.listByMainOrderGuid(mainOrderGuid);
        if (CollectionUtil.isNotEmpty(subOrders)) {
            orderGuidList.addAll(subOrders.stream().map(OrderDO::getGuid).collect(Collectors.toList()));
        }
        return orderGuidList;
    }
}
