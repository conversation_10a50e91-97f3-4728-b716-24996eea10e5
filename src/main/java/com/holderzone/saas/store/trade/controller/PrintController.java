package com.holderzone.saas.store.trade.controller;


import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 打印相关接口
 */
@RestController
@RequestMapping("/dine_in_order")
@Slf4j
public class PrintController {

    //@ApiOperation(value = "查询指定订单已绑定标签商品")
    //@PostMapping("/list_print_items_by_orderid")
    //public List<listPrintItemsResDTO> listPrintItemsByOrderGuid(@RequestBody ListPrintItemsReqDTO listPrintItemsReqDTO) {
    //    return businessOrderReportService.saleByHoursStatistics(statisticsByHoursReqDTO);
    //}
    //
    //@ApiOperation(value = "重打印标签")
    //@PostMapping("/print_label")
    //public List<UserBriefDTO> getCheckoutStaffs(@RequestBody PrintLabelReqDTO printLabelReqDTO) {
    //    return businessOrderReportService.getCheckoutStaffs(businessOrderStatisticsQueryDTO);
    //}
}