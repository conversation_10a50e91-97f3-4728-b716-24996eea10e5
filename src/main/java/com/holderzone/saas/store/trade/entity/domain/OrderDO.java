package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.holderzone.saas.store.dto.order.OrderMultiMemberPayDTO;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_order")
public class OrderDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;


    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    private String orderNo;

    /**
     * 交易模式(0：正餐，1：快餐)
     *
     * @see TradeModeEnum
     */
    private Integer tradeMode;

    /**
     * 设备类型(订单来源 BaseDeviceTypeEnum)
     */
    private Integer deviceType;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 营业日
     */
    private LocalDate businessDay;

    /**
     * 桌台guid
     */
    private String diningTableGuid;

    /**
     * 桌台名称(区域+桌台code)
     */
    private String diningTableName;

    /**
     * 是否虚拟台(0:否，1:是)
     */
    private Integer virtualTable;

    /**
     * 作废原因
     */
    private String cancelReason;

    /**
     * 整单备注
     */
    private String remark;

    /**
     * 快餐牌号
     */
    private String mark;

    /**
     * 预结单打印次数
     */
    private Integer printPreBillNum;

    /**
     * 并桌金额（只用于并桌情况加载桌台页面的金额展示）
     */
    private BigDecimal orderFeeForCombine;

    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;

    /**
     * 附加费
     */
    private BigDecimal appendFee;

    /**
     * 找零（收款-应收金额）
     */
    private BigDecimal changeFee;

    /**
     * 实收金额=订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））
     * 应收金额=订单金额-优惠金额-订金(押金)
     */
    private BigDecimal actuallyPayFee;

    /**
     * 预付金（反结账原单聚合支付转入）
     */
    private BigDecimal prepayFee;

    /**
     * 定金
     */
    private BigDecimal reserveFee;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废 7: 反结账
     */
    private Integer state;

    public String getStateName() {
        if (state != null) {
            if (StateEnum.READY.getCode() <= state && state <= StateEnum.FAILURE.getCode()) {
                return "未结账";
            }
            if (StateEnum.SUCCESS.getCode() == state) {
                return "已结账";
            }
            if (StateEnum.REFUNDED.getCode() == state) {
                return "已退款";
            }
            if (StateEnum.CANCEL.getCode() == state) {
                return "已作废";
            }
            if (StateEnum.ANTI_SETTLEMENT.getCode() == state) {
                return StateEnum.ANTI_SETTLEMENT.getDesc();
            }
        }
        return null;

    }

    /**
     * 订单反结账类型1：普通单 2：原单 3：新单 4：退单
     */
    private Integer recoveryType;

    /**
     * 0:无并单，1:主单， 2:子单
     *
     * @see com.holderzone.saas.store.trade.entity.enums.UpperStateEnum
     */
    private Integer upperState;

    /**
     * 主单guid
     */
    private Long mainOrderGuid;

    /**
     * 预定guid
     */
    private String reserveGuid;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 会员卡guid
     */
    private String memberCardGuid;

    /**
     * 会员支付id，反结账传给会员系统
     */
    private String memberConsumptionGuid;

    /**
     * 食堂记录支付id，反结账传给食堂记录
     */
    private String canteenConsumptionGuid;

    /**
     * 会员电话
     */
    @TableField(insertStrategy = FieldStrategy.NOT_NULL, updateStrategy = FieldStrategy.NOT_NULL)
    private String memberPhone;

    /**
     * 会员名字
     */
    @TableField(insertStrategy = FieldStrategy.NOT_NULL, updateStrategy = FieldStrategy.NOT_NULL)
    private String memberName;

    /**
     * 用户微信公众号openId
     */
    private String userWxPublicOpenId;

    /**
     * 反结账原单的guid
     */
    private Long originalOrderGuid;

    /**
     * 反结账id(多次反结账所有原单和新单此id相同)
     */
    private String recoveryId;

    /**
     * 计算是是否使用会员价（1：使用，0：不使用）
     */
    private Integer calculateByMemberPrice;
    /**
     * 反结账原因
     */
    private String recoveryReason;

    /**
     * 反结账设备类型（BaseDeviceTypeEnum）
     */
    private Integer recoveryDeviceType;

    /**
     * 结账设备类型（BaseDeviceTypeEnum）
     */
    private Integer checkoutDeviceType;

    /**
     * 取消订单设备类型（BaseDeviceTypeEnum）
     */
    private Integer cancelDeviceType;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 开台时间
     */
    private LocalDateTime checkinTime;

    /**
     * 结算时间
     */
    private LocalDateTime checkoutTime;

    /**
     * 作废时间
     */
    private LocalDateTime cancelTime;

    /**
     * 创建操作人guid
     */
    private String createStaffGuid;

    /**
     * 创建操作人name
     */
    private String createStaffName;

    /**
     * 反结账操作人guid
     */
    private String recoveryStaffGuid;

    /**
     * 反结账操作人name
     */
    private String recoveryStaffName;

    /**
     * 结账操作人guid
     */
    private String checkoutStaffGuid;

    /**
     * 结账操作人name
     */
    private String checkoutStaffName;

    /**
     * 作废订单操作人guid
     */
    private String cancelStaffGuid;

    /**
     * 作废订单操作人name
     */
    private String cancelStaffName;

    /**
     * 是否团餐
     */
    //private Integer isGroupMeal;

    /**
     * 订单来源
     *
     * @return
     */
    //private Integer orderSource;
    public Boolean isUnfinished() {
        if (this.state == null) {
            return Boolean.FALSE;
        }
        return !this.state.equals(StateEnum.READY.getCode()) && !this.state.equals(StateEnum.PENDING
                .getCode()) && !this.state.equals(StateEnum.FAILURE.getCode());
    }

    /***
     * 是否处理 销售明细订单相关数据 1为已处理 0和null为未处理
     */
    private Integer isHandle;
    /***
     * 是否录入操作员 0：未录入 1：已录入
     */
    private Integer isWaiter;

    /***
     * 是否是积分商城0：不是，1：是
     */
    private Integer memberIntegralStore;

    private Integer useIntegral;

    /**
     * 是否为调整单 0：不是调整单  1：是调整单
     */
    private Integer adjustState;

    /**
     * 聚合支付帐号appId
     */
    private String paymentAppId;

    /**
     * 复制的原订单guid 如果为0表示非复制的订单
     */
    private Long copyOrderGuid;

    /**
     * 超出金额
     * 目前只有第三方活动会有这玩意
     */
    private BigDecimal excessAmount;

    /**
     * 是否使用随行红包
     */
    private Boolean isFollowRedPacket;

    /**
     * 退款订单guid
     */
    private Long refundOrderGuid;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 是否需要释放订单优惠券 0：不需要 1：需要
     */
    private Integer isUpdatedEs;

    /**
     * 多卡/单卡支付返回信息
     */
    @TableField(exist = false)
    private List<OrderMultiMemberPayDTO> multiMemberPays;

    /**
     * 订单扩展信息
     */
    @TableField(exist = false)
    private OrderExtendsDO extendsDO;

    /**
     * 原客人数
     */
    @TableField(exist = false)
    private Integer preGuestCount;
}
