package com.holderzone.saas.store.trade.utils;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.trade.anno.PerformanceCheck;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.HttpClientBuilder;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HttpUtil
 * @date 2019/12/02 9:57
 * @description //TODO
 * @program IdeaProjects
 */
@Slf4j
public class HttpUtil {

    private static final int TIMEOUT = 5000;

    /**
     * HTTP post請求
     *
     * @param url        路径
     * @param jsonString 请求json字符串
     * @return
     * @throws IOException
     */
    @PerformanceCheck
    public static String doPostJson(String url, String jsonString) throws IOException {
        log.info("httpRequest:---> request-url:{},requestBody:{}", url, jsonString);
        HttpClient httpClient = HttpClientBuilder.create().build();
        StringEntity stringEntity = new StringEntity(jsonString, "utf-8");
        stringEntity.setContentType("application/json");
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(stringEntity);
        httpPost.addHeader("enterpriseGuid", UserContextUtils.getEnterpriseGuid());
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        String execute = httpClient.execute(httpPost, responseHandler);
        log.info("httppostresult：{}", execute);
        return execute;
    }

    /**
     * HTTP post請求
     *
     * @param url        路径
     * @param jsonString 请求json字符串
     * @param headerMap  要设置的头部信息
     * @return String
     * @throws IOException IO异常
     */
    public static String doPostJsonHeader(String url, String jsonString, Map<String, String> headerMap) throws IOException {
        log.info("httpRequest:---> request-url:{},requestBody:{} headerMap={}", url, jsonString, headerMap);
        HttpClient httpClient = HttpClientBuilder.create().disableAutomaticRetries().build();
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(TIMEOUT)
                .setConnectionRequestTimeout(TIMEOUT)
                .setSocketTimeout(TIMEOUT)
                .build();
        StringEntity stringEntity = new StringEntity(jsonString, "utf-8");
        stringEntity.setContentType("application/json");
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(requestConfig);
        httpPost.setEntity(stringEntity);
        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            httpPost.setHeader(name, value);
        }
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        String execute = httpClient.execute(httpPost, responseHandler);
        log.info("httppostresult：{}", execute);
        return execute;
    }
}