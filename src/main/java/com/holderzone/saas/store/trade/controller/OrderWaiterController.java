package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.order.request.waiter.*;
import com.holderzone.saas.store.trade.service.OrderWaiterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/11/18 18:20
 * @description
 */
@RestController
@RequestMapping("/order_waiter")
@Api(value = "订单服务员")
@Slf4j
public class OrderWaiterController {

    @Resource
    private OrderWaiterService orderWaiterService;

    @ApiOperation(value = "录入服务员接口", notes = "录入服务员接口")
    @PostMapping("/add_order_waiter")
    public Boolean addOrderWaiter(@RequestBody OrderWaiterReqDTO orderWaiterReqDTO) {
        return orderWaiterService.addOrderWaiter(orderWaiterReqDTO);
    }
    @ApiOperation(value = "订单查询服务员接口", notes = "订单查询服务员接口")
    @GetMapping("/get_order_waiter/{orderGuid}")
    public List<OrderWaiterInfoDTO> getOrderWaiter(@PathVariable("orderGuid") String orderGuid) {
        return orderWaiterService.getOrderWaiter(orderGuid);
    }

    @ApiOperation(value = "分页查询订单服务员接口", notes = "分页查询订单服务员接口")
    @PostMapping("/page_order_waiter")
    public Page<OrderWaiterPageRespDTO> pageOrderWaiter(@RequestBody OrderWaiterPageReqDTO orderWaiterPageReqDTO) {
        return orderWaiterService.pageOrderWaiter(orderWaiterPageReqDTO);
    }

    @ApiOperation(value = "分页查询订单服务员明细接口", notes = "分页查询订单服务员明细接口")
    @PostMapping("/page_order_waiter_details")
    public Page<OrderWaiterPageDetailsRespDTO> pageOrderWaiterDetails(@RequestBody OrderWaiterPageDetailsReqDTO orderWaiterPageDetailsReqDTO) {
        return orderWaiterService.pageOrderWaiterDetails(orderWaiterPageDetailsReqDTO);
    }

    @ApiOperation(value = "分页查询订单服务员汇总接口", notes = "分页查询订单服务员汇总接口")
    @PostMapping("/page_order_waiter_total")
    public Page<OrderWaiterPageTotalRespDTO> pageOrderWaiterTotal(@RequestBody OrderWaiterPageDetailsReqDTO orderWaiterPageDetailsReqDTO) {
        return orderWaiterService.pageOrderWaiterTotal(orderWaiterPageDetailsReqDTO);
    }

    @ApiOperation(value = "分页查询订单服务员接口---后台补录", notes = "分页查询订单服务员接口---后台补录")
    @PostMapping("/page_order_waiter_make_up")
    public Page<OrderWaiterPageMakeUpRespDTO> pageOrderWaiterMakeUp(@RequestBody OrderWaiterMakeUpPageReqDTO reqDTO) {
        return orderWaiterService.pageOrderWaiterMakeUp(reqDTO);
    }

    @ApiOperation(value = "补录订单服务员接口---后台补录", notes = "补录订单服务员接口---后台补录")
    @PostMapping("/order_waiter_make_up")
    public Boolean orderWaiterMakeUp(@RequestBody OrderWaiterMakeUpReqDTO makeUpReqDTO) {
        return orderWaiterService.orderWaiterMakeUp(makeUpReqDTO);
    }
}
