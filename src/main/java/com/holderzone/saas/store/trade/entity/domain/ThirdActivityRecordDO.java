package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 第三方活动使用记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "hst_third_activity_record")
@ApiModel(value = "HstThirdActivityRecord对象", description = "第三方活动使用记录表")
public class ThirdActivityRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "第三方活动记录guid")
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "是否删除 0未删除 1已删除")
    private Integer isDelete;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "第三方活动guid")
    private String activityGuid;

    @ApiModelProperty(value = "规则类型 0-金额扣减 1-买单优惠")
    private Integer ruleType;

    @ApiModelProperty(value = "第三方活动券码")
    private String thirdActivityCodes;

    @ApiModelProperty(value = "参与活动的金额")
    private BigDecimal joinFee;

    @ApiModelProperty(value = "不参与活动金额")
    private BigDecimal notJoinFee;

    @ApiModelProperty(value = "顾客购买金额或者抵扣金额")
    private BigDecimal costFee;

    @ApiModelProperty(value = "券面值")
    private BigDecimal couponFee;

    @ApiModelProperty(value = "第三方活动券码")
    @TableField(exist = false)
    private List<String> thirdActivityCodeList;
}
