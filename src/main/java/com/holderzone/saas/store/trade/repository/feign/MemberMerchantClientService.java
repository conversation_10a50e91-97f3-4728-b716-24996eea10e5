package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.holder.saas.member.merchant.dto.member.RequestCardRechargePageQO;
import com.holderzone.holder.saas.member.merchant.dto.member.ResponseCardRechargeStatisticsVO;
import com.holderzone.holder.saas.member.merchant.dto.member.ResponseOperationMemberInfo;
import com.holderzone.holder.saas.member.terminal.dto.statistics.RequestConsumptionStatistics;
import com.holderzone.holder.saas.member.terminal.dto.statistics.RequestDutyStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseConsumptionStatistics;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseDutyStatis;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.saas.store.trade.entity.dto.MemberResult;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Component
@FeignClient(name = "holder-saas-member-merchant", fallbackFactory = MemberMerchantClientService.MemberMerchantClientServiceFallback.class)
public interface MemberMerchantClientService {

    /**
     * 一体机查询充值订单列表
     *
     * @param request request model
     * @return page ResponseMemberConsumption
     */
    @ApiOperation("一体机查询充值订单列表")
    @PostMapping("/ter/card/recharge/getMemberCardRechargePage")
    MemberResult<ResponseCardRechargeStatisticsVO> getMemberCardRechargePage(@RequestBody RequestCardRechargePageQO request);

    /**
     * 根据支付宝用户id或微信用户id获取用户信息
     */
    @GetMapping("/hsa-member/query_by_applet_member/{openId}")
    MemberResult<ResponseOperationMemberInfo> getMemberInfoByUerIdOrOpenId(@PathVariable("openId") String openId);

    /**
     * 查询会员详情
     * @param requestQueryMemberInfo requestQueryMemberInfo
     * @return
     */
    @PostMapping("/hsa-member/get_member_info_detail")
    MemberResult<ResponseMemberInfo> getMemberInfoDetail(@RequestBody RequestQueryMemberInfo requestQueryMemberInfo);

    @Component
    class MemberMerchantClientServiceFallback implements FallbackFactory<MemberMerchantClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberMerchantClientService.MemberMerchantClientServiceFallback.class);

        @Override
        public MemberMerchantClientService create(Throwable throwable) {


            return new MemberMerchantClientService() {


                @Override
                public MemberResult<ResponseCardRechargeStatisticsVO> getMemberCardRechargePage(RequestCardRechargePageQO request) {
                    logger.error("getMemberCardRechargePage异常e={}", throwable.getMessage());
                    throw new ParameterException("getMemberCardRechargePage异常");
                }

                @Override
                public MemberResult<ResponseOperationMemberInfo> getMemberInfoByUerIdOrOpenId(String openId) {
                    logger.error("根据支付宝用户id或微信用户id获取用户信息异常e={}", throwable.getMessage());
                    throw new ParameterException("根据支付宝用户id或微信用户id获取用户信息异常");
                }

                @Override
                public MemberResult<ResponseMemberInfo> getMemberInfoDetail(RequestQueryMemberInfo requestQueryMemberInfo) {
                    logger.error("查询会员详情信息异常e={}", throwable.getMessage());
                    throw new ParameterException("查询会员详情信息异常");
                }
            };
        }
    }
}