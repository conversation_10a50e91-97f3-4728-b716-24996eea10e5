package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.response.dinein.RefundOrderRecordDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderRefundRecordDO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单退款记录 Mapper 接口
 * </p>
 */
public interface OrderRefundRecordMapper extends BaseMapper<OrderRefundRecordDO> {

    List<RefundOrderRecordDTO> listByRefundOrderGuid(@Param("refundOrderGuid") Long refundOrderGuid);

    BigDecimal handoverRefund(@Param("dto") HandoverPayQueryDTO request);
}
