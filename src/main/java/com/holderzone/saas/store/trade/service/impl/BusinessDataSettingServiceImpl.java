package com.holderzone.saas.store.trade.service.impl;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingQueryDTO;
import com.holderzone.saas.store.enums.business.DataSettingEnum;
import com.holderzone.saas.store.enums.business.DinnerCouponItemPriceSettingEnum;
import com.holderzone.saas.store.trade.repository.feign.BusinessClientService;
import com.holderzone.saas.store.trade.service.BusinessDataSettingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/12
 * @since 1.8
 */
@Service
@Slf4j
public class BusinessDataSettingServiceImpl implements BusinessDataSettingService {

    private final BusinessClientService businessClientService;

    @Autowired
    public BusinessDataSettingServiceImpl(BusinessClientService businessClientService) {
        this.businessClientService = businessClientService;
    }

    @Override
    public boolean queryIsUseCouponBuyPrice(String storeGuid) {
        if (StringUtils.isNotBlank(storeGuid)) {
            DataSettingQueryDTO dataSettingQueryDTO = new DataSettingQueryDTO();
            dataSettingQueryDTO.setStoreGuid(storeGuid);
            dataSettingQueryDTO.setDataSettingTypeList(Collections.singletonList(DataSettingEnum.DINNER_COUPON_ITEM_PRICE_SETTING.getCode()));
            try {
                List<DataSettingDTO> dataSettings = businessClientService.findDataSetting(dataSettingQueryDTO);
                log.info("查询数据取值设置结果：{}", JacksonUtils.writeValueAsString(dataSettings));
                return !CollectionUtils.isEmpty(dataSettings)
                        && DataSettingEnum.DINNER_COUPON_ITEM_PRICE_SETTING.getCode() == dataSettings.get(0).getDataSettingType()
                        && DinnerCouponItemPriceSettingEnum.PURCHASE_PRICE.getCode() == dataSettings.get(0).getDataSettingCode();
            } catch (Exception e) {
                log.error("查询数据取值设置失败,e:", e);
            }
        }
        return false;
    }
}
