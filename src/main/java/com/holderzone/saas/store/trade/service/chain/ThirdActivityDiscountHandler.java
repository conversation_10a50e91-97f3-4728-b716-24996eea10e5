package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.RuleTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.repository.feign.ThirdActivityClientService;
import com.holderzone.saas.store.trade.service.IThirdActivityRecordService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-25
 * @description
 */
@Component
@Slf4j
@AllArgsConstructor
public class ThirdActivityDiscountHandler extends DiscountHandler{

    private final IThirdActivityRecordService thirdActivityRecordService;

    private final ThirdActivityClientService thirdActivityClientService;

    @Override
    void dealDiscount(DiscountContext context) {
        DiscountFeeDetailDTO thirdActivityDiscount = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap().get(type()));
        if (ObjectUtils.isEmpty(thirdActivityDiscount)) {
            context.setThirdActivityDiscountFee(BigDecimal.ZERO);
            return;
        }

        BigDecimal discountFee = BigDecimal.ZERO;
        List<ThirdActivityRecordDTO> thirdActivityRecordDTOList
                = thirdActivityRecordService.listThirdActivityByOrderGuid(String.valueOf(context.getOrderDO().getGuid()));
        log.info("计算时查询订单下使用的活动结果 thirdActivityRecordDTOList={}", JacksonUtils.writeValueAsString(thirdActivityRecordDTOList));
        if (CollectionUtils.isEmpty(thirdActivityRecordDTOList)) {
            thirdActivityDiscount.setDiscountFee(discountFee);
            context.getDiscountFeeDetailDTOS().add(thirdActivityDiscount);
            context.setThirdActivityDiscountFee(discountFee);
            return ;
        }
        List<String> guidList = thirdActivityRecordDTOList.stream()
                .map(ThirdActivityRecordDTO::getActivityGuid)
                .collect(Collectors.toList());
        List<ThirdActivityRespDTO> thirdActivityList = thirdActivityClientService.inProcessListByGuid(guidList);
        log.info("计算时查询可用活动列表 thirdActivityList={}", JacksonUtils.writeValueAsString(thirdActivityList));
        boolean isDiscountActivity = dealThirdActivity(thirdActivityList,thirdActivityRecordDTOList);

        context.getOrderDetailRespDTO().setIsDiscountActivity(isDiscountActivity);
        discountFee = thirdActivityRecordDTOList.stream()
                .map(rec -> ObjectUtils.isEmpty(rec.getDeductionFee()) ? BigDecimal.ZERO : rec.getDeductionFee())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        thirdActivityDiscount.setDiscountFee(discountFee);
        context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(discountFee));
        context.getDiscountFeeDetailDTOS().add(thirdActivityDiscount);

        // 第三方活动的优惠按比例设置商品的折扣总价
        dealWithAllItems(context.getAllItems(), discountFee);

        log.info("14.第三方平台活动总扣减金额：{}，订单剩余金额：{}", thirdActivityDiscount.getDiscountFee(),
                context.getOrderDetailRespDTO().getOrderSurplusFee());
        context.setThirdActivityDiscountFee(discountFee);
    }

    private boolean dealThirdActivity(List<ThirdActivityRespDTO> thirdActivityList,List<ThirdActivityRecordDTO> thirdActivityRecordDTOList) {
        if(CollectionUtils.isEmpty(thirdActivityList)){
            return true;
        }
        Map<String, ThirdActivityRespDTO> activityMap = thirdActivityList.stream()
                .collect(Collectors.toMap(ThirdActivityRespDTO::getGuid, Function.identity(), (dto1, dto2) -> dto2));
        for (ThirdActivityRecordDTO rec : thirdActivityRecordDTOList) {
            ThirdActivityRespDTO thirdActivityDTO = activityMap.get(rec.getActivityGuid());
            if (ObjectUtils.isEmpty(thirdActivityDTO)) {
                // 此处应该存在活动
                log.error("计算时未匹配到活动 activityGuid={}", rec.getActivityGuid());
            } else {
                if (Objects.equals(RuleTypeEnum.AMOUNT_DEDUCTION.getCode(), rec.getRuleType())) {
                    // 金额扣减
                    int num = rec.getThirdActivityCodeList().size();
                    rec.setDeductionFee(BigDecimal.valueOf(num).multiply(thirdActivityDTO.getCouponFee()));
                } else {
                    // 买单优惠
                    rec.setDeductionFee(rec.getJoinFee().add(rec.getNotJoinFee()));
                }
            }
            if (0 == thirdActivityDTO.getIsActivityShare()) {
                return false;
            }
        }
        return true;

    }

    private void dealWithAllItems(List<DineInItemDTO> allItems, BigDecimal discountFee) {
        BigDecimal totalPrice = allItems.stream()
                .map(DineInItemDTO::getItemPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 定义最后一个商品的优惠值，每次计算一个商品的优惠值，从总优惠中减去
        BigDecimal lastReduceAmount = discountFee;
        // 将优惠金额按商品价格占总价比例分摊
        for (int i = 0; i < allItems.size(); i++) {
            DineInItemDTO dishInfo = allItems.get(i);
            if (i == allItems.size() - 1) {
                // 设置最后一个的优惠值
                dishInfo.setTotalDiscountFee(Optional.ofNullable(dishInfo.getTotalDiscountFee())
                        .orElse(BigDecimal.ZERO).add(lastReduceAmount));
            } else {
                // 单个商品优惠金额
                BigDecimal discountMoney = BigDecimalUtil.multiply2(discountFee, dishInfo.getItemPrice()).
                        divide(totalPrice, 2, RoundingMode.HALF_DOWN);
                dishInfo.setTotalDiscountFee(Optional.ofNullable(dishInfo.getTotalDiscountFee())
                        .orElse(BigDecimal.ZERO).add(discountMoney));
                lastReduceAmount = lastReduceAmount.subtract(discountMoney);
            }
        }
    }


    @Override
    Integer type() {
        return DiscountTypeEnum.THIRD_ACTIVITY.getCode();
    }
}
