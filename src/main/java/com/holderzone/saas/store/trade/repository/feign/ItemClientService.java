package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemPadCalculateDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.OrderItemReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemClientService
 * @date 2018/09/30 11:41
 * @description 微信调用
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-item", fallbackFactory = ItemClientService.FallBack.class)
public interface ItemClientService {

    @PostMapping("/estimate/verify_dinein_item_estimate")
    EstimateResultRespDTO estimate(@RequestBody List<DineInItemDTO> dineInItemDTOS);

    @PostMapping("/estimate/dinein_fail")
    Boolean returnEstimate(@RequestBody List<DineInItemDTO> dineInItemDTOS);

    @PostMapping("item/get_erp_sku_guid")
    Map<String, String> getErpSkuGuids(@RequestBody SingleDataDTO skuIdList);

    @PostMapping("/item/get_item_info")
    ItemInfoRespDTO getItemInfo(@RequestBody ItemSingleDTO itemSingleDTO);

    @ApiOperation(value = "获取商品详情列表", notes = "获取商品详情列表")
    @PostMapping("/item/get_item_info_list")
    List<ItemInfoRespDTO> selectItemInfoList(@RequestBody ItemStringListDTO itemStringListDTO);

    @ApiOperation(value = "获取商品列表 - 已删除商品也需查询", notes = "获取商品列表 - 已删除商品也需查询")
    @PostMapping("/item/get_items")
    List<ItemInfoRespDTO> selectItems(@RequestBody ItemStringListDTO itemStringListDTO);

    @GetMapping("/item_sku/info")
    SkuInfoRespDTO info(@RequestParam("skuGuid") String skuGuid);

    /**
     * guid,code
     *
     * @param skuGuidList
     * @return
     */
    @PostMapping("/item_sku/getCode")
    Map<String, String> getCode(@RequestBody List<String> skuGuidList);

    @PostMapping("/item/parent_sku")
    List<SkuInfoRespDTO> findParentSKUS(@RequestBody List<String> skuGuids);

    /**
     * 判断当前是否有不可下单的商品
     * 不可下单：商品下架、商品售罄、库存不足
     *
     * @param orderItemReqDTO 需要检查的规格guid
     * @return 不可下单的商品
     */
    @ApiOperation(value = "判断当前是否有不可下单的商品")
    @PostMapping(value = "/item_sku/check_order_placement_item")
    List<ItemPadCalculateDTO> checkOrderPlacementItem(@RequestBody OrderItemReqDTO orderItemReqDTO);

    @ApiOperation(value = "通过sku查询估清数据")
    @PostMapping(value = "/estimate/query_estimate_by_guids/{storeGuid}")
    List<ItemEstimateForAndroidDTO> queryEstimateByGuids(@PathVariable("storeGuid") String storeGuid, @RequestBody List<String> skuGuids);

    /**
     * 根据规格guid查询规格信息
     *
     * @param skuGuidList 规格guid列表
     * @return 规格信息列表
     */
    @ApiOperation(value = "根据规格guid查询规格信息")
    @PostMapping("/item_sku/list_sku_info")
    List<SkuInfoRespDTO> listSkuInfo(@RequestBody List<String> skuGuidList);

    @ApiOperation(value = "根据规格guid查询规格信息(区分销售模式)")
    @PostMapping("/item/list_sku_info/by_mode")
    List<SkuInfoRespDTO> listSkuInfoByRecipeMode(@RequestBody ItemStringListDTO itemStringListDTO);

    @ApiOperation(value = "根据商品guid获取门店商品详情列表（区分销售模式）")
    @PostMapping("/item/list_item_info_by_sales_model")
    List<ItemInfoRespDTO> listItemInfoBySalesModel(@RequestBody ItemStringListDTO itemStringListDTO);


    @ApiOperation(value = "获取规格详情", notes = "外卖下单获取规格详情")
    @PostMapping("/item/selectSkuInfo/v2")
    List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOListV2(@RequestBody ItemStringListDTO itemStringListDTO);

    @ApiOperation(value = "根据分类id查询源分类信息")
    @PostMapping("/type/query_source_type_info")
    List<TypeWebRespDTO> querySourceTypeInfo(@RequestBody ItemStringListDTO listDTO);

    @ApiOperation(value = "查询商品及父级信息 - 已删除商品也需查询")
    @PostMapping("/item/query_parent_item_info")
    List<ItemInfoRespDTO> queryParentItemInfo(@RequestBody ItemStringListDTO listDTO);

    @Component
    class FallBack implements FallbackFactory<ItemClientService> {

        private static final Logger logger = LoggerFactory.getLogger(ItemClientService.FallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ItemClientService create(Throwable throwable) {
            return new ItemClientService() {

                @Override
                public EstimateResultRespDTO estimate(List<DineInItemDTO> dineInItemDTOS) {
                    logger.error("菜品估清调用异常，e={}", throwable.getMessage());
                    throw new ParameterException("菜品估清调用异常");
                }

                @Override
                public Boolean returnEstimate(List<DineInItemDTO> dineInItemDTOS) {
                    logger.error("菜品估清调用异常，e={}", throwable.getMessage());
                    throw new ParameterException("菜品估清调用异常");
                }

                @Override
                public Map<String, String> getErpSkuGuids(SingleDataDTO skuguids) {
                    logger.error("获取erpGuid错误，e={}", throwable.getMessage());
                    throw new ParameterException("获取erpGuid调用异常");
                }

                @Override
                public ItemInfoRespDTO getItemInfo(ItemSingleDTO itemSingleDTO) {
                    logger.error("获取单个菜品信息错误，e={}", throwable.getMessage());
                    throw new ParameterException("获取单个菜品信息调用异常");
                }

                @Override
                public List<ItemInfoRespDTO> selectItemInfoList(ItemStringListDTO itemStringListDTO) {
                    logger.error("获取商品详情列表错误，e={}", throwable.getMessage());
                    throw new ParameterException("获取商品详情列表调用异常");
                }

                @Override
                public List<ItemInfoRespDTO> selectItems(ItemStringListDTO itemStringListDTO) {
                    logger.error("获取商品详情列表错误，e={}", throwable.getMessage());
                    throw new ParameterException("获取商品详情列表调用异常");
                }

                @Override
                public SkuInfoRespDTO info(String skuGuid) {
                    logger.error("获取商品SKU详情错误，e={}", throwable.getMessage());
                    throw new ParameterException("获取商品SKU详情调用异常");
                }

                @Override
                public Map<String, String> getCode(List<String> skuGuidList) {
                    logger.error("通过sku查询品牌库sku错误，e={}", throwable.getMessage());
                    throw new ParameterException("查询sku查询品牌库sku异常");
                }

                @Override
                public List<SkuInfoRespDTO> findParentSKUS(List<String> skuGuids) {
                    logger.error("通过sku查询品牌库sku错误，e={}", throwable.getMessage());
                    throw new ParameterException("查询sku查询品牌库sku异常");
                }

                @Override
                public List<ItemPadCalculateDTO> checkOrderPlacementItem(OrderItemReqDTO orderItemReqDTO) {
                    logger.error(HYSTRIX_PATTERN, "checkOrderPlacementItem", JacksonUtils.writeValueAsString(orderItemReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<ItemEstimateForAndroidDTO> queryEstimateByGuids(String StoreGuid, List<String> skuGuids) {
                    logger.error("通过sku查询估清表数据出错，e={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<SkuInfoRespDTO> listSkuInfo(List<String> skuGuidList) {
                    logger.error("根据规格guid查询规格信息出错，e={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<SkuInfoRespDTO> listSkuInfoByRecipeMode(ItemStringListDTO itemStringListDTO) {
                    logger.error("根据规格guid查询规格信息出错，e={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<ItemInfoRespDTO> listItemInfoBySalesModel(ItemStringListDTO itemStringListDTO) {
                    logger.error(HYSTRIX_PATTERN, "listItemInfoBySalesModel", JacksonUtils.writeValueAsString(itemStringListDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOListV2(ItemStringListDTO itemStringListDTO) {
                    logger.error(HYSTRIX_PATTERN, "selectSkuTakeawayInfoRespDTOListV2", JacksonUtils.writeValueAsString(itemStringListDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<TypeWebRespDTO> querySourceTypeInfo(ItemStringListDTO listDTO) {
                    logger.error(HYSTRIX_PATTERN, "querySourceTypeInfo", JacksonUtils.writeValueAsString(listDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemInfoRespDTO> queryParentItemInfo(ItemStringListDTO listDTO) {
                    logger.error(HYSTRIX_PATTERN, "queryParentItemInfo", JacksonUtils.writeValueAsString(listDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }

}