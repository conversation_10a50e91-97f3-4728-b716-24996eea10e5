package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.common.enums.BooleanEnum;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityDetailsRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseConfirmMultiPay;
import com.holderzone.holder.saas.member.terminal.dto.volume.RequestUpdateVolumeRelevance;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.kds.req.OrderRemarkReqDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberPayDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.request.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.ReserveBatchCreateOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.request.member.RequestUpdateCouponDTO;
import com.holderzone.saas.store.dto.order.response.bill.*;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.*;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.pay.AggRefundReqDTO;
import com.holderzone.saas.store.dto.pay.AggRefundRespDTO;
import com.holderzone.saas.store.dto.pay.SaasAggRefundDTO;
import com.holderzone.saas.store.dto.print.PrintLabelReq;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterQueryDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitRecordPageRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.RefundTypeEnum;
import com.holderzone.saas.store.enums.print.BusinessTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.table.TableStatusChangeEnum;
import com.holderzone.saas.store.enums.trade.SortTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.trade.config.OldCouponCalculateConfig;
import com.holderzone.saas.store.trade.config.ZhuanCanConfig;
import com.holderzone.saas.store.trade.entity.constant.CommonConstant;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.*;
import com.holderzone.saas.store.trade.helper.BillVerifyHelper;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.PageAdapter;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.mapper.OrderAbnormalRecordMapper;
import com.holderzone.saas.store.trade.mapper.OrderItemMapper;
import com.holderzone.saas.store.trade.mapper.OrderMapper;
import com.holderzone.saas.store.trade.repository.feign.*;
import com.holderzone.saas.store.trade.repository.impls.ItemAttrServiceImpl;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.*;
import com.holderzone.saas.store.trade.service.mq.KdsMqService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.transform.TransactionRecordTransform;
import com.holderzone.saas.store.trade.utils.*;
import jodd.util.StringUtil;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className
 * @date 2018/09/04 16:10
 * @description
 * @program holder-saas-store-trade
 */
@Service
@Slf4j
@AllArgsConstructor
public class DineInServiceImpl implements DineInService {

    private static final String NO_ORDER_RECORD = "没有该订单的记录：";

    private final OrderService orderService;

    private final OrderSubsidiaryService orderSubsidiaryService;

    private final OrderItemService orderItemService;

    private final ItemAttrServiceImpl itemAttrService;

    private final GrouponMpService grouponMpService;

    private final GrouponService grouponService;

    private final RedisHelper redisHelper;

    private final FreeReturnItemService freeReturnItemService;

    private final CombineOrderService combineOrderService;

    private final TableClientService tableClientService;

    private final TableService tableService;

    private final TransactionRecordService transactionRecordService;

    private final IMultipleTransactionRecordService multipleTransactionRecordService;

    private final DiscountService discountService;

    private final AggPayClientService aggPayClientService;

    private final StoreClientService storeClientService;

    private final AppendFeeService appendFeeService;

    private final ItemClientService itemClientService;

    private final DineInPrintService dineInPrintService;

    private final RedisService redisService;

    private final KdsMqService kdsMqService;

    private final ReserveClientService reserveClientService;

    private final OrderExtendsService orderExtendsService;

    private final MemberTerminalClientService memberTerminalClientService;

    private final OrderMapper orderMapper;

    @Resource
    private ZhuanCanConfig zhuanCanConfig;

    private final DynamicHelper dynamicHelper;

    private final static OrderTransform orderTransform = OrderTransform.INSTANCE;

    private final OrderWaiterService orderWaiterService;

    private final OrderAbnormalRecordMapper orderAbnormalRecordMapper;

    @Autowired
    private final OrganizationClientService organizationClientService;

    @Autowired
    private final BusinessMessageService businessMessageService;

    private final ThirdActivityClientService thirdActivityClientService;

    private final OrderRefundRecordService orderRefundRecordService;

    private final PrintClientService printClientService;

    private final TcdOrderService tcdOrderService;

    private final AppendFeeMpService appendFeeMpService;

    private final OrderMultiMemberService orderMultiMemberService;

    private final PackageSubgroupChangesService packageSubgroupChangesService;

    private final DebtUnitRecordService debtUnitRecordService;

    @Resource(name = "aggTransactionRecordExecutor")
    private ExecutorService aggTransactionRecordExecutor;
    @Autowired
    private OrderItemMapper orderItemMapper;

    private final OldCouponCalculateConfig oldCouponCalculateConfig;

    //当前订单商品已全部退款，无法打印
    public static final String PRINT_REFUND_ALL_ITEM = "当前订单商品已全部退款，无法打印";


    /**
     * 桌台旁边 的查询订单详情
     * 优化  考虑直接联合查询完？
     * 三次数据库查询？
     * 如果订单是来源是pad，只查询已接单的商品
     */
    @Override
    public DineinOrderDetailRespDTO getOrderDetail4AddItem(String orderGuid) {
        OrderDO orderDO = orderService.getById(orderGuid);
        if (orderDO == null) {
            throw new ParameterException(NO_ORDER_RECORD + orderGuid);
        }
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = getSingleOrderDetail(orderDO);
        // 查询同桌其他单菜品
        // 结账不清台
        if (UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
            Long mainOrderGuid = orderDO.getGuid();
            if (Objects.equals(UpperStateEnum.SAME_SUB.getCode(), orderDO.getUpperState())) {
                mainOrderGuid = orderDO.getMainOrderGuid();
            }
            List<DineinOrderDetailRespDTO> otherOrderDetails = new ArrayList<>();
            List<OrderDO> subOrderDOS = orderService.otherListByMainOrderGuid(mainOrderGuid);
            subOrderDOS.removeIf(e -> Long.valueOf(orderGuid).equals(e.getGuid()));
            for (OrderDO subOrderDO : subOrderDOS) {
                otherOrderDetails.add(getSingleOrderDetail(subOrderDO));
            }
            dineinOrderDetailRespDTO.setOtherOrderDetails(otherOrderDetails);
        }
        // 构建商品套餐子商品换菜
        packageSubgroupChangesService.addOriginalItemInfo(dineinOrderDetailRespDTO);
        OrderSubsidiaryDO orderSubsidiaryDO = orderSubsidiaryService.getById(orderGuid);
        //附表有数据而且预点餐未下单
        if (orderSubsidiaryDO != null && orderSubsidiaryDO.getReserveOrderState().equals(1)
                && (com.holderzone.framework.util.StringUtils.hasText(orderDO.getReserveGuid()))) {
            //调用reserve获取预点餐信息
            ReserveRecordGuidDTO guidDTO = new ReserveRecordGuidDTO();
            guidDTO.setGuid(orderDO.getReserveGuid());
            List<DineInItemDTO> items = reserveClientService.getItems(guidDTO);
            dineinOrderDetailRespDTO.setReserveItems(items);
        }
        // 设置预付金信息
        String mainOrderGuid = orderGuid;
        if (Objects.equals(orderDO.getUpperState(), UpperStateEnum.SUB.getCode())) {
            mainOrderGuid = String.valueOf(orderDO.getMainOrderGuid());
        }
        setReservePayInfo(mainOrderGuid, dineinOrderDetailRespDTO);
        return dineinOrderDetailRespDTO;
    }

    private void setReservePayInfo(String orderGuid, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        // 根据订单查询预付金信息
        SingleDataDTO dataDTO = new SingleDataDTO();
        dataDTO.setData(orderGuid);
        log.info("[根据订单查询预付金信息]dataDTO={}", JacksonUtils.writeValueAsString(dataDTO));
        ReserveRecordDTO reserveRecordDTO = reserveClientService.queryByOrderGuid(dataDTO);
        log.info("[根据订单查询预付金信息]reserveRecordDTO={}", JacksonUtils.writeValueAsString(reserveRecordDTO));
        if (!ObjectUtils.isEmpty(reserveRecordDTO)) {
            dineinOrderDetailRespDTO.setReserveFee(reserveRecordDTO.getReserveAmount());
            dineinOrderDetailRespDTO.setReserveRefundAmount(reserveRecordDTO.getReserveRefundAmount());
            dineinOrderDetailRespDTO.setReservePaymentType(reserveRecordDTO.getPaymentType());
            dineinOrderDetailRespDTO.setReservePaymentTypeName(reserveRecordDTO.getPaymentTypeName());
            dineinOrderDetailRespDTO.setReserveRemark(reserveRecordDTO.getRemark());
            dineinOrderDetailRespDTO.setReserveGuid(reserveRecordDTO.getGuid());
            if (Objects.equals(reserveRecordDTO.getOrderType(), OrderTypeEnum.RESERVE.getCode())) {
                StringBuilder strB = new StringBuilder();
                strB.append("预定订金");
                if (com.holderzone.framework.util.StringUtils.hasText(reserveRecordDTO.getName())) {
                    strB.append("  ");
                    strB.append(reserveRecordDTO.getName());
                }
                if (com.holderzone.framework.util.StringUtils.hasText(reserveRecordDTO.getPhone())) {
                    strB.append("  ");
                    strB.append(reserveRecordDTO.getPhone());
                }
                dineinOrderDetailRespDTO.setReserveRemark(strB.toString());
            }
        }
    }

    @Override
    public Boolean addVersion(String orderGuid) {
        try {
            this.redisService.putTradeVersion(orderGuid);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.info("新增版本号失败：{}", e.getMessage());
            return Boolean.FALSE;
        }
    }

    @Override
    public String getVersion(String orderGuid) {
        try {
            return redisHelper.get(RedisKeyUtil.getHstOrderVersionKey(orderGuid));
        } catch (Exception e) {
            log.info("获取版本号失败：{}", e.getMessage());
            return "-1";
        }
    }


    @Override
    @Transactional
    public CreateDineInOrderReqDTO createOrder(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        OrderDO orderDO = new OrderDO();
        OrderExtendsDO orderExtendsDO = new OrderExtendsDO();
        buildOrderDO(createDineInOrderReqDTO, orderDO, orderExtendsDO);
        orderService.save(orderDO);
        orderExtendsService.save(orderExtendsDO);
        //1.这里的key用固定的格式生成,version从1递增
        redisService.putTradeVersion(createDineInOrderReqDTO.getGuid());
        return createDineInOrderReqDTO;
    }

    private void buildOrderDO(CreateDineInOrderReqDTO createDineInOrderReqDTO, OrderDO orderDO, OrderExtendsDO orderExtendsDO) {
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        ArrayList<String> storeGuidList = new ArrayList<>();
        storeGuidList.add(UserContextUtils.getStoreGuid());
        reqDTO.setStoreGuidList(storeGuidList);
        reqDTO.setQueryDateTime(LocalDateTime.now());
        LocalDate businessDay = storeClientService.queryBusinessDay(reqDTO);
        orderDO.setGuid(Long.valueOf(createDineInOrderReqDTO.getGuid()));
        //===================== 新需求：为了区分订单号，订单号开头增加门店code =====================
        orderDO.setOrderNo(redisHelper.generateOrderNo(UserContextUtils.get().getStoreNo(), UserContextUtils.getStoreGuid()));
        orderDO.setGuestCount(createDineInOrderReqDTO.getGuestCount());
        orderDO.setStoreGuid(UserContextUtils.getStoreGuid());
        orderDO.setStoreName(UserContextUtils.getStoreName());
        orderDO.setBusinessDay(businessDay);
        orderDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        orderDO.setCreateStaffName(UserContextUtils.getUserName());
        orderDO.setDeviceType(createDineInOrderReqDTO.getDeviceType());
        orderDO.setState(StateEnum.READY.getCode());
        orderDO.setTradeMode(TradeModeEnum.DINEIN.getCode());
        orderDO.setRecoveryType(RecoveryTypeEnum.GENERAL.getCode());
        orderDO.setPrintPreBillNum(CommonConstant.ZERO);
        orderDO.setDiningTableGuid(createDineInOrderReqDTO.getDiningTableGuid());
        orderDO.setDiningTableName(CommonUtil.jointDiningTableName(createDineInOrderReqDTO.getAreaName(), createDineInOrderReqDTO.getDiningTableName
                ()));
        // 结账子单
        if (StringUtils.isNotEmpty(createDineInOrderReqDTO.getOriginalOrderGuid())) {
            orderDO.setUpperState(UpperStateEnum.SAME_SUB.getCode());
            orderDO.setMainOrderGuid(Long.valueOf(createDineInOrderReqDTO.getOriginalOrderGuid()));
            // 继承上个单子的信息
            extendsOrderInfo(orderDO);
        }

        // 订单扩展表
        BeanUtils.copyProperties(orderDO, orderExtendsDO);
        orderExtendsDO.setAssociatedFlag(Boolean.TRUE.equals(createDineInOrderReqDTO.getAssociatedFlag()));
        orderExtendsDO.setAssociatedSn(createDineInOrderReqDTO.getAssociatedSn());
        orderExtendsDO.setAssociatedTableGuids(CollectionUtils.isNotEmpty(createDineInOrderReqDTO.getAssociatedTableGuids()) ?
                JacksonUtils.writeValueAsString(createDineInOrderReqDTO.getAssociatedTableGuids()) : null);
        orderExtendsDO.setAssociatedTableNames(CollectionUtils.isNotEmpty(createDineInOrderReqDTO.getAssociatedTableNames()) ?
                JacksonUtils.writeValueAsString(createDineInOrderReqDTO.getAssociatedTableNames()) : null);
        orderDO.setExtendsDO(orderExtendsDO);

        //开台时计算并保存该桌台的附加费
        BigDecimal appendFee = appendFeeService.getAndUpdateAppendFee(orderDO, createDineInOrderReqDTO.getAreaGuid());
        orderDO.setOrderFee(appendFee);
        orderDO.setAppendFee(appendFee);
    }


    /**
     * 继承上个订单的会员登录信息
     */
    private void extendsOrderInfo(OrderDO orderDO) {
        List<OrderDO> sameOrderList = orderService.otherListByMainOrderGuid(orderDO.getMainOrderGuid());
        OrderDO preOrder = sameOrderList.stream()
                .max(Comparator.comparing(OrderDO::getGuid))
                .orElse(null);
        if (Objects.isNull(preOrder)) {
            return;
        }
        // 就餐人数
        orderDO.setGuestCount(preOrder.getGuestCount());
        // 会员信息
        orderDO.setMemberGuid(preOrder.getMemberGuid());
        orderDO.setMemberPhone(preOrder.getMemberPhone());
        orderDO.setMemberCardGuid(preOrder.getMemberCardGuid());
        orderDO.setMemberName(preOrder.getMemberName());
    }

    @Override
    public String batchCreateOrder(ReserveBatchCreateOrderReqDTO reserveBatchCreateOrderReqDTO) {
        String reserveGuid = reserveBatchCreateOrderReqDTO.getReserveGuid();
        Boolean containDish = reserveBatchCreateOrderReqDTO.getContainDish();
        String mainOrderGuid = reserveBatchCreateOrderReqDTO.getMainOrderGuid();
        List<CreateDineInOrderReqDTO> createDineInOrderReqDTOS = reserveBatchCreateOrderReqDTO
                .getCreateDineInOrderReqDTOS();
        List<OrderDO> orderDOS = Lists.newArrayList();
        OrderSubsidiaryDO orderSubsidiaryDO = null;
        if (CollectionUtil.isEmpty(createDineInOrderReqDTOS)) {
            throw new BusinessException("创建订单时桌台信息不能为空");
        }
        List<String> orderGuids = Lists.newArrayList();
        boolean hasCombine = createDineInOrderReqDTOS.size() > 1;
        for (CreateDineInOrderReqDTO createDineInOrderReqDTO : createDineInOrderReqDTOS) {
            orderGuids.add(createDineInOrderReqDTO.getGuid());
            OrderDO orderDO = new OrderDO();
            OrderExtendsDO orderExtendsDO = new OrderExtendsDO();
            orderDOS.add(orderDO);
            buildOrderDO(createDineInOrderReqDTO, orderDO, orderExtendsDO);
            orderDO.setReserveGuid(reserveGuid);
            orderDO.setReserveFee(reserveBatchCreateOrderReqDTO.getReserveFee());

            if (createDineInOrderReqDTO.getGuid().equals(mainOrderGuid)) {
                if (hasCombine) {
                    orderDO.setUpperState(UpperStateEnum.MAIN.getCode());
                    orderDO.setReserveFee(reserveBatchCreateOrderReqDTO.getReserveFee());
                }
                if (containDish) {
                    orderSubsidiaryDO = new OrderSubsidiaryDO();
                    orderSubsidiaryDO.setGuid(Long.valueOf(mainOrderGuid));
                    orderSubsidiaryDO.setReserveOrderState(1);
                }
            } else {
                if (hasCombine) {
                    orderDO.setUpperState(UpperStateEnum.SUB.getCode());
                    orderDO.setMainOrderGuid(Long.valueOf(mainOrderGuid));
                }
            }

        }

        if (CollectionUtil.isNotEmpty(orderDOS)) {
            orderService.saveBatch(orderDOS);
        }
        if (orderSubsidiaryDO != null) {
            orderSubsidiaryService.save(orderSubsidiaryDO);
        }

        redisService.batchPutTradeVersion(orderGuids);
        return "SUCCESS";
    }

    /**
     * 批量查询订单桌台信息
     * 专用于桌台查询所用
     *
     * @param orderGuidsDTO 订单
     * @return 订单信息
     */
    @Override
    public List<OrderTableInfoDTO> batchGetTableInfo2(OrderGuidsDTO orderGuidsDTO) {
        List<String> orderGuids = orderGuidsDTO.getOrderGuids();
        if (orderGuids == null) {
            throw new ParameterException("guid不能为空");
        }
        List<OrderDO> orderDOS = new ArrayList<>(orderService.listByIds(orderGuids));
        List<Long> mainGuidList = orderDOS.stream()
                .filter(o -> !ObjectUtils.isEmpty(o.getMainOrderGuid())
                        && o.getMainOrderGuid() != 0
                        && !orderGuids.contains(String.valueOf(o.getMainOrderGuid())))
                .map(OrderDO::getMainOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        if (!org.springframework.util.CollectionUtils.isEmpty(mainGuidList)) {
            List<OrderDO> mainOrderDOList = orderService.listByIds(mainGuidList);
            orderDOS.addAll(mainOrderDOList);
        }
        List<OrderTableInfoDTO> tableInfoDTOList = orderTransform.orderDOS2OrderTableInfoDTOS(orderDOS);
        Map<String, BigDecimal> orderFeeMap = redisService.batchGetOrderFee(orderGuids);
        if (!ObjectUtils.isEmpty(orderFeeMap)) {
            tableInfoDTOList.forEach(order -> {
                BigDecimal bigDecimal = orderFeeMap.get(order.getGuid());
                if (!ObjectUtils.isEmpty(bigDecimal)) {
                    order.setOrderFee(bigDecimal);
                }
            });
        }
        return tableInfoDTOList;
    }

    /**
     * 打印菜品复单
     */
    @Override
    public boolean printItemRepeatOrder(CreateDineInOrderReqDTO itemRepeatOrderReqDTO) {
        List<DineInItemDTO> dineInItemDTOS = itemRepeatOrderReqDTO.getDineInItemDTOS();
        if (org.springframework.util.CollectionUtils.isEmpty(dineInItemDTOS)) {
            throw new ParameterException("当前没有商品可打印");
        }

        // 校验单据打印机是否存在
        PrinterQueryDTO queryDTO = new PrinterQueryDTO();
        queryDTO.setStoreGuid(itemRepeatOrderReqDTO.getStoreGuid());
        queryDTO.setDeviceId(itemRepeatOrderReqDTO.getDeviceId());
        queryDTO.setBusinessType(BusinessTypeEnum.FRONT_PRINTER.getType());
        queryDTO.setInvoiceType(InvoiceTypeEnum.ITEM_REPEAT_ORDER.getType());
        List<PrinterDTO> dtoList = printClientService.queryByCondition(queryDTO);
        if (org.springframework.util.CollectionUtils.isEmpty(dtoList)) {
            throw new ParameterException("未添加菜品复单打印机");
        }

        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = buildDineinOrderDetailRespDTO(itemRepeatOrderReqDTO);
        return dineInPrintService.printItemRepeatOrder(itemRepeatOrderReqDTO, dineinOrderDetailRespDTO);
    }

    @Override
    public List<String> listPaymentTypeName() {
        return orderService.listPaymentTypeName();
    }

    private DineinOrderDetailRespDTO buildDineinOrderDetailRespDTO(CreateDineInOrderReqDTO itemRepeatOrderReqDTO) {
        List<DineInItemDTO> dineInItemDTOS = itemRepeatOrderReqDTO.getDineInItemDTOS();
        String orderGuid = itemRepeatOrderReqDTO.getGuid();
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO;
        if (com.holderzone.framework.util.StringUtils.isEmpty(orderGuid)) {
            dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
            dineinOrderDetailRespDTO.setTradeMode(TradeModeEnum.DINEIN.getCode());
        } else {
            OrderDO orderDO = orderService.getById(orderGuid);
            if (orderDO == null) {
                throw new ParameterException(NO_ORDER_RECORD + orderGuid);
            }
            dineinOrderDetailRespDTO = orderTransform.orderDO2DineinOrderDetailRespDTO(orderDO);
        }

        List<TablePlaceDTO> tables = itemRepeatOrderReqDTO.getTables();
        if (!CollectionUtils.isEmpty(tables)) {
            List<String> diningTableNameList = new ArrayList<>();
            tables.forEach(table -> diningTableNameList.add(CommonUtil.jointDiningTableName(table.getAreaName(), table.getName())));
            String diningTableName = String.join("、", diningTableNameList);
            dineinOrderDetailRespDTO.setDiningTableName(diningTableName);
        }
        AmountCalculationUtil.buildRepeatOrderItem(dineInItemDTOS);
        dineinOrderDetailRespDTO.setDineInItemDTOS(dineInItemDTOS);
        if (itemRepeatOrderReqDTO.getGuestCount() > 0) {
            dineinOrderDetailRespDTO.setGuestCount(itemRepeatOrderReqDTO.getGuestCount());
        }
        return dineinOrderDetailRespDTO;
    }

    @Override
    public List<OrderTableInfoDTO> batchGetTableInfo(OrderGuidsDTO orderGuidsDTO) {
        List<String> orderGuids = orderGuidsDTO.getOrderGuids();
        if (orderGuids == null) {
            throw new ParameterException("guid不能为空");
        }
        List<OrderDO> orderDOS = new ArrayList<>(orderService.listByIds(orderGuids));
        return orderTransform.orderDOS2OrderTableInfoDTOS(orderDOS);
    }


    @Override
    public Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        OrderDO orderDO = new OrderDO();
        orderDO.setGuid(Long.valueOf(createDineInOrderReqDTO.getGuid()));
        OrderDO orderDOInDb = orderService.getByIdWithLock(createDineInOrderReqDTO.getGuid());
        if (!orderDOInDb.getState().equals(StateEnum.READY.getCode())) {
            throw new ParameterException("订单状态异常");
        }
        if (createDineInOrderReqDTO.getGuestCount() <= 0) {
            throw new ParameterException("就餐人数需大于0");
        }
        orderDO.setGuestCount(createDineInOrderReqDTO.getGuestCount());
        // 如果是多单结账，则查询当前订单最大的点餐人数
        orderDOInDb.setPreGuestCount(getPreOrderGuestCount(orderDOInDb));
        orderDOInDb.setGuestCount(createDineInOrderReqDTO.getGuestCount());
        BigDecimal andUpdateAppendFee = appendFeeService.getAndUpdateAppendFee(orderDOInDb, null);
        if (!BigDecimalUtil.lessThanZero(andUpdateAppendFee)) {
            orderDO.setOrderFee(orderDOInDb.getOrderFee().subtract(orderDOInDb.getAppendFee() == null ? BigDecimal
                    .ZERO : orderDOInDb.getAppendFee()).add(andUpdateAppendFee));
            orderDO.setAppendFee(andUpdateAppendFee);
            orderDO.setOrderFeeForCombine(orderDO.getOrderFee());
        }
        boolean update = orderService.updateByIdWithDeleteCache(orderDO);

        // 更新多单结账的主单
        updateOrderFeeForSameOrder(orderDOInDb);

        //更新桌台状态
        updateGuestCountSendTableStatusChange(createDineInOrderReqDTO, orderDOInDb, orderDO);

        // 非pad修改人数推送给对应pad设备
        boolean notPad = !Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), createDineInOrderReqDTO.getDeviceType())
                && Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), orderDOInDb.getDeviceType());
        if (notPad) {
            //pad端订单，同时更新pad桌台人数
            adjustPopulationPushMsg(createDineInOrderReqDTO, orderDOInDb.getDiningTableGuid());
        }
        return update;
    }


    /**
     * 更新桌台状态
     */
    private void updateGuestCountSendTableStatusChange(CreateDineInOrderReqDTO createDineInOrderReqDTO,
                                                       OrderDO orderDOInDb, OrderDO orderDO) {
        TableStatusChangeDTO tableStatusChangeDTO = orderTransform.createDineInOrderReqDTO2TableStatusChangeDTO
                (createDineInOrderReqDTO);
        tableStatusChangeDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
        tableStatusChangeDTO.setTableGuid(orderDOInDb.getDiningTableGuid());
        tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.NUMBER_OF_MEALS_CHANGE.getId());
        tableStatusChangeDTO.setEnableManualClear(UpperStateEnum.SAME_ORDER_STATE.contains(orderDOInDb.getUpperState()) ?
                BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        // 如果是正餐先付的订单
        if (Objects.equals(BooleanEnum.TRUE.getCode(), tableStatusChangeDTO.getEnableManualClear())) {
            // 并且订单金额为0
            tableStatusChangeDTO.setCheckoutSuccessFlag(orderDO.getOrderFee()
                    .add(orderDO.getAppendFee()).compareTo(BigDecimal.ZERO) == 0);
        }
        tableClientService.tableStatusChange(tableStatusChangeDTO);
    }

    /**
     * 更新多单结账的主单
     */
    private void updateOrderFeeForSameOrder(OrderDO orderDO) {
        if (!Objects.equals(UpperStateEnum.SAME_SUB.getCode(), orderDO.getUpperState())) {
            return;
        }
        Long guid = orderDO.getGuid();
        Long mainOrderGuid = orderDO.getMainOrderGuid();
        if (Objects.nonNull(mainOrderGuid) && !Objects.equals(0L, mainOrderGuid)) {
            guid = mainOrderGuid;
        }
        orderService.updateSameOrderFeeForCombine(guid);
    }


    /**
     * 查询当前订单最大的点餐人数
     */
    private Integer getPreOrderGuestCount(OrderDO orderDOInDb) {
        if (!UpperStateEnum.SAME_ORDER_STATE.contains(orderDOInDb.getUpperState())) {
            return orderDOInDb.getGuestCount();
        }
        Long mainOrderGuid = orderDOInDb.getGuid();
        if (Objects.nonNull(orderDOInDb.getMainOrderGuid()) && !Objects.equals(0L, orderDOInDb.getMainOrderGuid())) {
            mainOrderGuid = orderDOInDb.getMainOrderGuid();
        }
        List<OrderDO> orderList = orderService.otherListByMainOrderGuid(mainOrderGuid);
        orderList.removeIf(e -> e.getGuid().equals(orderDOInDb.getGuid()));
        OrderDO maxGuestOrderDO = orderList.stream()
                .max(Comparator.comparing(OrderDO::getGuestCount))
                .orElse(null);
        if (Objects.isNull(maxGuestOrderDO)) {
            return orderDOInDb.getGuestCount();
        }
        return maxGuestOrderDO.getGuestCount();
    }

    /**
     * 封裝調整桌臺消息
     *
     * @param createDineInOrderReqDTO 修改桌臺就餐人數DTO
     */
    private void adjustPopulationPushMsg(CreateDineInOrderReqDTO createDineInOrderReqDTO, String tableGuid) {
        BusinessMessageDTO adjustMessageDTO = new BusinessMessageDTO();
        adjustMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        adjustMessageDTO.setPlatform("2");
        adjustMessageDTO.setStoreGuid(createDineInOrderReqDTO.getStoreGuid());
        adjustMessageDTO.setStoreName(createDineInOrderReqDTO.getStoreName());
        adjustMessageDTO.setSubject(BusinessMsgTypeEnum.MODIFIED_NUMBER.getName());
        adjustMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.MODIFIED_NUMBER.getId());
        adjustMessageDTO.setContent(String.valueOf(createDineInOrderReqDTO.getGuestCount()));

        // 查询门店桌台对应设备信息
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(createDineInOrderReqDTO.getStoreGuid());
        reqDTO.setTableGuid(tableGuid);
        StoreDeviceDTO storeDeviceDTO = organizationClientService.queryDeviceByStoreTable(reqDTO);
        if (ObjectUtils.isEmpty(storeDeviceDTO)) {
            log.warn("未查询到门店桌台对应设备信息 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        adjustMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + storeDeviceDTO.getDeviceNo());
        log.info("非PAD修改桌台人数消息 closeMessageDTO={}", JacksonUtils.writeValueAsString(adjustMessageDTO));
        businessMessageService.msg(adjustMessageDTO);
    }

    @Override
    public Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        OrderDO orderDO = new OrderDO();
        OrderDO orderDOInDb = orderService.getByIdWithLock(createDineInOrderReqDTO.getGuid());
        if (!orderDOInDb.getState().equals(StateEnum.READY.getCode())) {
            throw new ParameterException("订单状态异常");
        }
        orderDO.setGuid(Long.valueOf(createDineInOrderReqDTO.getGuid()));
        if (createDineInOrderReqDTO.getRemark() == null) {
            createDineInOrderReqDTO.setRemark(StringUtils.EMPTY);
        }
        orderDO.setRemark(createDineInOrderReqDTO.getRemark());
        OrderRemarkReqDTO orderRemarkReqDTO = new OrderRemarkReqDTO();
        orderRemarkReqDTO.setOrderGuid(createDineInOrderReqDTO.getGuid());
        orderRemarkReqDTO.setOrderRemark(orderDO.getRemark());
        kdsMqService.remark(orderRemarkReqDTO);
        return orderService.updateByIdWithDeleteCache(orderDO);
    }

    @Override
    public DineinOrderDetailRespDTO getOrderDetail(String orderGuid) {
        OrderDO orderDO = orderService.getById(orderGuid);
        if (orderDO == null) {
            throw new ParameterException(NO_ORDER_RECORD + orderGuid);
        }
        DineinOrderDetailRespDTO orderDetailRespDTO = getSingleOrderDetail(orderDO);
        // 主单需补全子单信息
        // 并台
        if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
            List<DineinOrderDetailRespDTO> subOrderDetails = new ArrayList<>();
            List<OrderDO> subOrderDOS = orderService.listByMainOrderGuid(orderDO.getGuid());
            // 去掉退款单
            subOrderDOS.removeIf(e -> Objects.equals(StateEnum.REFUNDED.getCode(), e.getState()));
            for (OrderDO subOrderDO : subOrderDOS) {
                subOrderDetails.add(getSingleOrderDetail(subOrderDO));
            }
            orderDetailRespDTO.setSubOrderDetails(subOrderDetails);
        }
        // 结账不清台
        if (UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
            List<DineinOrderDetailRespDTO> otherOrderDetails = new ArrayList<>();
            List<OrderDO> subOrderDOS = orderService.otherListByMainOrderGuid(orderDO.getGuid());
            subOrderDOS.removeIf(e -> e.getGuid().equals(Long.valueOf(orderDetailRespDTO.getGuid())));
            for (OrderDO subOrderDO : subOrderDOS) {
                otherOrderDetails.add(getSingleOrderDetail(subOrderDO));
            }
            orderDetailRespDTO.setOtherOrderDetails(otherOrderDetails);
        }
        // 附加费详情
        setOrderDetailRespAppendFeeDetail(orderDetailRespDTO);
        // 多单附加费
        if (CollectionUtils.isNotEmpty(orderDetailRespDTO.getOtherOrderDetails())) {
            orderDetailRespDTO.getOtherOrderDetails().forEach(this::setOrderDetailRespAppendFeeDetail);
        }
        // 设置预付金信息
        String mainOrderGuid = orderGuid;
        if (!Objects.equals(orderDO.getMainOrderGuid(), 0L)) {
            mainOrderGuid = String.valueOf(orderDO.getMainOrderGuid());
        }
        setReservePayInfo(mainOrderGuid, orderDetailRespDTO);
        return orderDetailRespDTO;
    }

    private void setOrderDetailRespAppendFeeDetail(DineinOrderDetailRespDTO orderDetailRespDTO) {
        if (BigDecimalUtil.greaterThanZero(orderDetailRespDTO.getAppendFee())) {
            OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setData(orderDetailRespDTO.getGuid());
            orderFeeDetailDTO.setAppendFeeDetailDTOS(appendFeeService.appendFeeList(singleDataDTO));
            orderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
            orderDetailRespDTO.setSurchargeLinkList(appendFeeService.getSurchargeLinkList(orderDetailRespDTO.getGuid()));
        }
    }

    @Override
    public DineinOrderDetailRespDTO getOrderDetails(String orderGuid) {
        DineinOrderDetailRespDTO orderDetail = getOrderDetail(orderGuid);
        //getOrderDetail引用的地方较多不宜改动，在下面重新设置附加费，调用查询订单下包含子单附加费的接口
        OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        orderFeeDetailDTO.setAppendFeeDetailDTOS(appendFeeService.getOrderAllAppendFeeList(orderGuid));
        orderDetail.setOrderFeeDetailDTO(orderFeeDetailDTO);

        // 构建商品套餐子商品换菜
        packageSubgroupChangesService.addOriginalItemInfo(orderDetail);

        // 处理商品折扣
        orderItemChangePriceHandler(orderDetail);

        // 查询团购验券
        buildItemCouponInfo(orderGuid, orderDetail);

        // 处理商品优惠价
        orderItemDiscountPriceHandler(orderDetail);

        return orderDetail;
    }


    @Override
    public DineinOrderDetailRespDTO getOrderDetailForAndroid(OrderDetailQueryDTO orderDetailQueryDTO) {
        String orderGuid = orderDetailQueryDTO.getData();
        DineinOrderDetailRespDTO orderDetail = getOrderDetails(orderGuid);
        // 退款单 商品明细需要+已退附加费商品
        orderItemAppendFeeHandler(orderDetail);

        //判断是否录入了 服务员
        orderDetail.setIfInputWaiter(orderWaiterService.checkInputWaiter(orderGuid));

        // 转换订单桌台名称
        transferOrderDiningTableName(orderDetail);

        // 多单结账合并商品金额
        if (Objects.isNull(orderDetailQueryDTO.getSingleFlag()) || Boolean.FALSE.equals(orderDetailQueryDTO.getSingleFlag())) {
            mergeSameOrderInfo(orderDetail);
        }
        return orderDetail;
    }

    @Override
    public DineinOrderDetailRespDTO getOrderItemDetail(String orderGuid) {
        //查询订单是否存在
        OrderDO order = orderMapper.selectById(Long.parseLong(orderGuid));
        if (ObjectUtil.isEmpty(order)) {
            return null;
        }

        //根据orderGuid查询商品信息
        List<OrderItemDO> itemList = listUnDeleteOrderItemByOrderGuid(orderGuid);

        return buildOrderDetail(order, itemList);
    }

    private List<OrderItemDO> listUnDeleteOrderItemByOrderGuid(String orderGuid) {
        LambdaQueryWrapper<OrderItemDO> queryWrapper = new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getIsDelete, false)
                .eq(OrderItemDO::getOrderGuid, orderGuid);

        return orderItemMapper.selectList(queryWrapper);
    }

    private DineinOrderDetailRespDTO buildOrderDetail(OrderDO order, List<OrderItemDO> itemList) {

        DineinOrderDetailRespDTO orderDetailRespDTO = orderTransform.orderDO2DineinOrderDetailRespDTO(order);

        List<DineInItemDTO> dineInItemList = new ArrayList<>();
        if (CollUtil.isNotEmpty(itemList)) {
            itemList.forEach(item -> {
                DineInItemDTO dineInItemDTO = new DineInItemDTO();
                dineInItemDTO.setGuid(item.getGuid().toString());
                dineInItemDTO.setItemGuid(item.getItemGuid());
                dineInItemDTO.setItemType(item.getItemType());
                dineInItemDTO.setItemName(item.getItemName());

                dineInItemList.add(dineInItemDTO);
            });
        }
        orderDetailRespDTO.setDineInItemDTOS(dineInItemList);

        return orderDetailRespDTO;
    }

    /**
     * 多单结账合并商品金额
     */
    private void mergeSameOrderInfo(DineinOrderDetailRespDTO orderDetail) {
        List<DineinOrderDetailRespDTO> otherOrderDetails = orderDetail.getOtherOrderDetails();
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            return;
        }
        // 此订单状态不为orderDO中的state
        List<DineinOrderDetailRespDTO> otherPayableOrderDetails = otherOrderDetails.stream()
                .filter(e -> Objects.equals(2, e.getState())
                        || Objects.equals(12, e.getState())
                        || Objects.equals(1, e.getState()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(otherPayableOrderDetails)) {
            return;
        }
        List<DineinOrderDetailRespDTO> otherActuallyPayOrderDetails = otherPayableOrderDetails.stream()
                .filter(e -> Objects.equals(2, e.getState())
                        || Objects.equals(12, e.getState()))
                .collect(Collectors.toList());
        // 实收金额 + 支付方式明细
        BigDecimal actuallyPayFee = otherActuallyPayOrderDetails.stream()
                .map(DineinOrderDetailRespDTO::getActuallyPayFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (Objects.equals(2, orderDetail.getState()) || Objects.equals(12, orderDetail.getState())) {
            actuallyPayFee = actuallyPayFee.add(Optional.ofNullable(orderDetail.getActuallyPayFee()).orElse(BigDecimal.ZERO));
        }
        orderDetail.setActuallyPayFee(actuallyPayFee);
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetails = mergeActuallyPayFeeDetailList(orderDetail);
        orderDetail.setActuallyPayFeeDetailDTOS(actuallyPayFeeDetails);

        // 优惠金额 + 优惠明细
        List<DiscountFeeDetailDTO> discountFeeDetails = mergeDiscountFeeDetailList(orderDetail);
        orderDetail.setDiscountFeeDetailDTOS(discountFeeDetails);
        BigDecimal discountFee = discountFeeDetails.stream()
                .map(DiscountFeeDetailDTO::getDiscountFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetail.setDiscountFee(discountFee);

        // 订单金额
        BigDecimal orderFee = otherPayableOrderDetails.stream()
                .map(DineinOrderDetailRespDTO::getOrderFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (Objects.equals(2, orderDetail.getState()) || Objects.equals(12, orderDetail.getState())) {
            orderFee = orderFee.add(Optional.ofNullable(orderDetail.getOrderFee()).orElse(BigDecimal.ZERO));
        }
        orderDetail.setOrderFee(orderFee);

        // 附加费 + 附加费明细
        BigDecimal totalAppendFee = otherPayableOrderDetails.stream()
                .map(DineinOrderDetailRespDTO::getAppendFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderDetail.setAppendFee(orderDetail.getAppendFee().add(totalAppendFee));
        orderDetail.setOrderFeeDetailDTO(mergeAppendDetailList(orderDetail));
    }

    /**
     * 交易记录合并
     */
    private List<ActuallyPayFeeDetailDTO> mergeActuallyPayFeeDetailList(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailList = dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS();
        Map<String, ActuallyPayFeeDetailDTO> actuallyPayFeeDetailMap = Optional.ofNullable(actuallyPayFeeDetailList).orElse(Lists.newArrayList())
                .stream()
                .filter(e -> e.getAmount().compareTo(BigDecimal.ZERO) >= 0)
                .collect(Collectors.toMap(e -> e.getPaymentTypeName() + e.getBankTransactionId(), Function.identity(), (key1, key2) -> key1));
        for (DineinOrderDetailRespDTO otherOrderDetail : dineinOrderDetailRespDTO.getOtherOrderDetails()) {
            List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = Optional.ofNullable(otherOrderDetail.getActuallyPayFeeDetailDTOS())
                    .orElse(Lists.newArrayList());
            for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : actuallyPayFeeDetailDTOS) {
                ActuallyPayFeeDetailDTO totalActuallyPayFeeDetailDTO = actuallyPayFeeDetailMap.get(actuallyPayFeeDetailDTO.getPaymentTypeName()
                        + actuallyPayFeeDetailDTO.getBankTransactionId());
                if (Objects.isNull(totalActuallyPayFeeDetailDTO)) {
                    actuallyPayFeeDetailMap.put(actuallyPayFeeDetailDTO.getPaymentTypeName() + actuallyPayFeeDetailDTO.getBankTransactionId(), actuallyPayFeeDetailDTO);
                } else {
                    totalActuallyPayFeeDetailDTO.setAmount(totalActuallyPayFeeDetailDTO.getAmount()
                            .add(actuallyPayFeeDetailDTO.getAmount()));
                }
            }
        }
        return actuallyPayFeeDetailMap.values()
                .stream()
                .sorted(Comparator.comparing(ActuallyPayFeeDetailDTO::getPaymentType))
                .collect(Collectors.toList());
    }

    /**
     * 优惠信息合并
     */
    private List<DiscountFeeDetailDTO> mergeDiscountFeeDetailList(DineinOrderDetailRespDTO orderDetail) {
        // 优惠信息合并
        List<DiscountFeeDetailDTO> discountFeeDetailList = orderDetail.getDiscountFeeDetailDTOS();
        Map<Integer, DiscountFeeDetailDTO> discountFeeDetailMap = Optional.ofNullable(discountFeeDetailList).orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(DiscountFeeDetailDTO::getDiscountType, Function.identity(), (key1, key2) -> key1));
        for (DineinOrderDetailRespDTO otherOrderDetail : orderDetail.getOtherOrderDetails()) {
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS = Optional.ofNullable(otherOrderDetail.getDiscountFeeDetailDTOS())
                    .orElse(Lists.newArrayList());
            for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
                DiscountFeeDetailDTO totalDiscountFeeDetailDTO = discountFeeDetailMap.get(discountFeeDetailDTO.getDiscountType());
                if (Objects.isNull(totalDiscountFeeDetailDTO)) {
                    discountFeeDetailMap.put(discountFeeDetailDTO.getDiscountType(), discountFeeDetailDTO);
                } else {
                    totalDiscountFeeDetailDTO.setDiscountFee(totalDiscountFeeDetailDTO.getDiscountFee()
                            .add(discountFeeDetailDTO.getDiscountFee()));
                }
            }
        }
        return discountFeeDetailMap.values()
                .stream()
                .sorted(Comparator.comparing(DiscountFeeDetailDTO::getDiscountType))
                .collect(Collectors.toList());
    }

    /**
     * 附加费信息合并
     */
    private OrderFeeDetailDTO mergeAppendDetailList(DineinOrderDetailRespDTO orderDetail) {
        OrderFeeDetailDTO orderFeeDetailDTO = orderDetail.getOrderFeeDetailDTO();
        orderFeeDetailDTO = Optional.ofNullable(orderFeeDetailDTO).orElse(new OrderFeeDetailDTO());
        List<AppendFeeDetailDTO> appendFeeDetailList = Optional.ofNullable(orderFeeDetailDTO.getAppendFeeDetailDTOS()).orElse(Lists.newArrayList());
        Map<String, AppendFeeDetailDTO> appendFeeDetailMap = appendFeeDetailList
                .stream()
                .collect(Collectors.toMap(e -> e.getType() + e.getName(), Function.identity(), (key1, key2) -> key1));
        for (DineinOrderDetailRespDTO otherOrderDetail : orderDetail.getOtherOrderDetails()) {
            OrderFeeDetailDTO otherOrderFeeDetailDTO = otherOrderDetail.getOrderFeeDetailDTO();
            if (Objects.isNull(otherOrderFeeDetailDTO) || CollectionUtils.isEmpty(otherOrderFeeDetailDTO.getAppendFeeDetailDTOS())) {
                continue;
            }
            List<AppendFeeDetailDTO> otherAppendFeeDetailDTOS = otherOrderFeeDetailDTO.getAppendFeeDetailDTOS();
            for (AppendFeeDetailDTO appendFeeDetailDTO : otherAppendFeeDetailDTOS) {
                AppendFeeDetailDTO totalAppendFeeDetailDTO = appendFeeDetailMap.get(appendFeeDetailDTO.getType() + appendFeeDetailDTO.getName());
                if (Objects.isNull(totalAppendFeeDetailDTO)) {
                    appendFeeDetailMap.put(appendFeeDetailDTO.getType() + appendFeeDetailDTO.getName(), appendFeeDetailDTO);
                } else {
                    totalAppendFeeDetailDTO.setAmount(totalAppendFeeDetailDTO.getAmount()
                            .add(appendFeeDetailDTO.getAmount()));
                }
            }
        }
        orderFeeDetailDTO.setAppendFeeDetailDTOS(new ArrayList<>(appendFeeDetailMap.values()));
        return orderFeeDetailDTO;
    }

    /**
     * 处理商品折扣
     */
    private void orderItemChangePriceHandler(DineinOrderDetailRespDTO orderDetail) {
        List<DiscountFeeDetailDTO> discountFeeDetailList = orderDetail.getDiscountFeeDetailDTOS();
        if (CollectionUtils.isEmpty(discountFeeDetailList)) {
            return;
        }
        BigDecimal singleDiscountFee = discountFeeDetailList.stream()
                .filter(e -> DiscountTypeEnum.SINGLE_DISCOUNT.getCode() == e.getDiscountType())
                .map(DiscountFeeDetailDTO::getDiscountFee)
                .findFirst()
                .orElse(BigDecimal.ZERO);
        if (singleDiscountFee.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        List<DineInItemDTO> dineInItemDTOS = orderDetail.getDineInItemDTOS();
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            if (dineInItemDTO.getPriceChangeType() != null && dineInItemDTO.getPriceChangeType() == 2) {
                //折扣需要补偿价格
                BigDecimal reduiceprice = dineInItemDTO.getOriginalPrice()
                        .multiply(AmountCalculationUtil.ITEM_DISCOUNT_PERCENT_BASE.subtract(new BigDecimal
                                        (dineInItemDTO.getDiscountPercent()))
                                .divide(AmountCalculationUtil.ITEM_DISCOUNT_PERCENT_BASE, 2, BigDecimal
                                        .ROUND_HALF_DOWN))
                        .multiply(dineInItemDTO.getCurrentCount());
                dineInItemDTO.setItemPrice(dineInItemDTO.getItemPrice().subtract(reduiceprice).setScale(2, BigDecimal
                        .ROUND_HALF_UP));
            }
        }
    }

    /**
     * 处理商品优惠价
     */
    private void orderItemDiscountPriceHandler(DineinOrderDetailRespDTO orderDetail) {
        handleItemList(orderDetail);
        // 子单处理
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetail.getSubOrderDetails();
        if (CollectionUtils.isEmpty(subOrderDetails)) {
            return;
        }
        for (DineinOrderDetailRespDTO subOrderDetail : subOrderDetails) {
            orderItemDiscountPriceHandler(subOrderDetail);
        }
    }

    private void handleItemList(DineinOrderDetailRespDTO orderDetail) {
        List<DineInItemDTO> itemList = orderDetail.getDineInItemDTOS();
        for (DineInItemDTO itemDTO : itemList) {
            // 处理redis中老数据的refundCount为null的处理
            itemDTO.setRefundCount(Objects.isNull(itemDTO.getRefundCount()) ? BigDecimal.ZERO : itemDTO.getRefundCount());
            // 赠品 ： 优惠价 = 0
            List<FreeItemDTO> freeItemList = itemDTO.getFreeItemDTOS();
            if (CollectionUtils.isNotEmpty(freeItemList)) {
                for (FreeItemDTO freeItemDTO : freeItemList) {
                    freeItemDTO.setDiscountPrice(BigDecimal.ZERO);
                    freeItemDTO.setDiscountTotalPrice(BigDecimal.ZERO);
                }
            }
            if (itemDTO.getItemPrice().compareTo(itemDTO.getDiscountTotalPrice()) > 0
                    && BigDecimalUtil.greaterThanZero(itemDTO.getCurrentCount())) {
                itemDTO.setDiscountPrice(itemDTO.getDiscountTotalPrice().divide(itemDTO.getCurrentCount(), 2, RoundingMode.DOWN));
            } else {
                itemDTO.setDiscountPrice(null);
                itemDTO.setDiscountTotalPrice(null);
            }
        }
    }

    /**
     * 退款单 商品明细需要+已退附加费商品
     */
    private void orderItemAppendFeeHandler(DineinOrderDetailRespDTO orderDetail) {
        // 订单状态
        if (orderDetail.getState() != 3 && !"0".equals(orderDetail.getRecoveryId())) {
            return;
        }
        // 存在附加费
        if (Objects.isNull(orderDetail.getOrderFeeDetailDTO()) || CollectionUtils.isEmpty(orderDetail.getOrderFeeDetailDTO().getAppendFeeDetailDTOS())) {
            return;
        }
        List<AppendFeeDetailDTO> appendFeeDetailList = orderDetail.getOrderFeeDetailDTO().getAppendFeeDetailDTOS();
        List<DineInItemDTO> appendFeeItemList = appendFeeDetailList.stream().map(appendFeeDetailDTO -> {
            DineInItemDTO itemDTO = new DineInItemDTO();
            // guid为附加费明细表中的guid
            itemDTO.setGuid(appendFeeDetailDTO.getGuid().toString());
            itemDTO.setItemName(appendFeeDetailDTO.getName());
            itemDTO.setCurrentCount(appendFeeDetailDTO.getAmount().divide(appendFeeDetailDTO.getUnitPrice(), 2, RoundingMode.DOWN));
            itemDTO.setFreeCount(BigDecimal.ZERO);
            itemDTO.setPrice(appendFeeDetailDTO.getUnitPrice());
            if (Objects.nonNull(appendFeeDetailDTO.getRefundShareAmount())) {
                itemDTO.setItemPrice(appendFeeDetailDTO.getRefundShareAmount());
            } else {
                itemDTO.setItemPrice(appendFeeDetailDTO.getAmount());
            }
            itemDTO.setUnit(appendFeeDetailDTO.getType() == 0 ? "人" : "桌");
            itemDTO.setItemType(ItemTypeEnum.SINGLE.getCode());
            return itemDTO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(appendFeeItemList)) {
            orderDetail.getDineInItemDTOS().addAll(0, appendFeeItemList);
        }
    }


    @Override
    public DineinOrderDetailRespDTO getSingleOrderDetail(OrderDO orderDO) {
        if (orderDO == null) {
            throw new ParameterException("没有该订单的记录");
        }
        String orderGuid = String.valueOf(orderDO.getGuid());
        Long guid = orderDO.getGuid();
//        Integer recoveryType = Optional.of(orderDO).map(OrderDO::getRecoveryType).orElse(-1);
//        Integer deviceType = Optional.of(orderDO).map(OrderDO::getDeviceType).orElse(-1);
//        Long originalOrderGuid = Optional.of(orderDO).map(OrderDO::getOriginalOrderGuid).orElse(guid);
//        boolean padOrder = deviceType == 5 && (recoveryType == 3 || recoveryType == 4);
//        if(padOrder){ // 3：新单 4：退单
//            guid = originalOrderGuid;
//        }
        List<OrderItemDO> orderItemDOS = orderItemService.listByOrderGuidWithCache(guid, orderDO.getDeviceType());
        log.info("获取菜品信息：{}", orderItemDOS);
        //========= 兼容版本 加上原价  ，数据库可也要改
        orderItemDOS.stream()
                .filter(a -> a.getPriceChangeType() == 0 && a.getOriginalPrice() == null)
                .forEach(a -> a.setOriginalPrice(a.getPrice()));
        Map<Long, List<OrderItemDO>> itemListMap = CollectionUtil.toListMap(orderItemDOS, "guid");
        List<ItemAttrDO> itemAttrDOList = new ArrayList<>(itemAttrService.listByItemGuids(new ArrayList<>(itemListMap
                .keySet())));

        DineinOrderDetailRespDTO orderDetailRespDTO = orderTransform.orderDO2DineinOrderDetailRespDTO(orderDO);
        // 订单扩展信息
        queryOrderExtendsInfo(orderDetailRespDTO);
        // 是否多次实际支付
        setIsMultipleActualAggPay(orderDetailRespDTO);

        orderDetailRespDTO.setRefundAmount(orderDO.getRefundAmount());
        orderDetailRespDTO.setState(OrderUtil.fixOrderState(orderDO.getState()));
        //订单的状态是支付中，或异常支付记录表里有该记录，则不能操作订单
        int count = orderAbnormalRecordMapper.selectCount(new LambdaQueryWrapper<OrderAbnormalRecordDO>()
                .eq(OrderAbnormalRecordDO::getOrderGuid, orderDO.getGuid())
                .eq(OrderAbnormalRecordDO::getIsDelete, Boolean.FALSE));
        if (orderDO.getState().equals(StateEnum.PENDING.getCode()) || count > 0) {
            orderDetailRespDTO.setCanOperated(Boolean.FALSE);
        } else {
            orderDetailRespDTO.setCanOperated(Boolean.TRUE);
        }

        orderDetailRespDTO.setDeviceTypeName(BaseDeviceTypeEnum.getDesc(orderDO.getDeviceType()));
        if (orderDO.getCheckoutDeviceType() != null) {
            orderDetailRespDTO.setCheckoutDeviceTypeName(BaseDeviceTypeEnum.getDesc(orderDO.getCheckoutDeviceType()));
        }
        orderDetailRespDTO.setTradeModeName(TradeModeEnum.getDesc(orderDO.getTradeMode()));
        //赠送信息
        List<FreeReturnItemDO> freeReturnItemDOS = null;
        if (AmountCalculationUtil.hasFree(orderItemDOS)) {
            Map<Long, OrderItemDO> itemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
            freeReturnItemDOS = freeReturnItemService.list(new
                    LambdaQueryWrapper<FreeReturnItemDO>().in(FreeReturnItemDO::getOrderItemGuid, new ArrayList<>
                    (itemDOMap.keySet())).eq(FreeReturnItemDO::getType, FreeReturnTypeEnum.FREE.getCode()));
        }
        // 构建商品
        List<DineInItemDTO> dineInItemDTOList = AmountCalculationUtil.buildItem(orderItemDOS, itemAttrDOList,
                freeReturnItemDOS);
        // 订单金额
        orderDetailRespDTO.setOrderFee(orderDO.getOrderFee());
        Map<String, DineInItemDTO> dineInItemDTOMap = CollectionUtil.toMap(dineInItemDTOList, "guid");

        //退货信息
        List<FreeReturnItemDO> returnItemDOS = freeReturnItemService.list(new LambdaQueryWrapper<FreeReturnItemDO>()
                .eq(FreeReturnItemDO::getOrderGuid, orderGuid)
                .eq(FreeReturnItemDO::getType, FreeReturnTypeEnum.RETURN.getCode())
                .eq(FreeReturnItemDO::getIsDelete, false)
                .eq(FreeReturnItemDO::getRefundCount, BigDecimal.ZERO));
        List<ReturnItemDTO> returnItemDTOS = AmountCalculationUtil.bulidReturnItemDTOS(returnItemDOS, dineInItemDTOMap);

        // 支付交易信息
        Map<Integer, List<TransactionRecordDO>> tradeType = getTransactionRecordDOMap(orderDO);

        //补全作废订单信息
        if (orderDO.getState().equals(StateEnum.CANCEL.getCode())) {
            List<TransactionRecordDO> cancelList = tradeType.get(TradeTypeEnum.GENERAL_OUT.getCode());
            AmountCalculationUtil.repairCancel(orderDetailRespDTO, orderDO, cancelList);

        }
        //补全反结账信息
        if (orderDO.getRecoveryType().equals(RecoveryTypeEnum.RETURN.getCode())) {
            List<TransactionRecordDO> returnList = tradeType.get(TradeTypeEnum.REFUND_OUT.getCode());
            AmountCalculationUtil.repairRecovery(orderDetailRespDTO, orderDO, returnList);

        }

        // 已结账并且不为子单补全金额明细
        List<DiscountDO> discountDOS = discountService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isNotEmpty(discountDOS)) {
            orderDetailRespDTO.setDiscountFeeDetailDTOS(orderTransform.discountDOS2DiscountFeeDetailDTOS(discountDOS));
            orderDetailRespDTO.setDiscountFee(orderDetailRespDTO.getDiscountFeeDetailDTOS().stream()
                    .map(DiscountFeeDetailDTO::getDiscountFee).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 支付交易记录 和 优惠处理
        transactionRecordAndDiscountHandler(orderDetailRespDTO, orderDO, tradeType, discountDOS);
        orderDetailRespDTO.setReturnItemDTOS(returnItemDTOS);
        orderDetailRespDTO.setDineInItemDTOS(dineInItemDTOList);
        AmountCalculationUtil.filterZeroItem(orderDetailRespDTO);
        // 补全退款信息
        buildRefundOrderInfo(orderDO, orderDetailRespDTO);
        // 退款单详情处理
        refundOrderHandler(orderDO, orderDetailRespDTO);
        //子单加主单单号
        if (orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
            OrderDO mainOrderDO = orderService.getById(orderDO.getMainOrderGuid());
            orderDetailRespDTO.setMainOrderNo(mainOrderDO.getOrderNo());
            orderDetailRespDTO.setMainOrderGuid(String.valueOf(mainOrderDO.getGuid()));
        }
        //设置是否是积分商城订单
        orderDetailRespDTO.setMemberIntegralStore(ObjectUtil.isNull(orderDO.getMemberIntegralStore()) ? 0 : orderDO.getMemberIntegralStore());
        //设置积分订单类型
        orderDetailRespDTO.setUseIntegral(orderDO.getUseIntegral());
        orderDetailRespDTO.setIntegralType(0);
        if (ObjectUtil.isNotNull(orderDO.getUseIntegral()) && orderDO.getUseIntegral() > 0) {
            orderDetailRespDTO.setIntegralType(1);
        }
        if (ObjectUtil.equal(orderDO.getMemberIntegralStore(), 1)) {
            orderDetailRespDTO.setIntegralType(2);
        }
        return orderDetailRespDTO;
    }

    private Map<Integer, List<TransactionRecordDO>> getTransactionRecordDOMap(OrderDO orderDO) {
        if (orderDO.getState().equals(StateEnum.READY.getCode()) && !UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
            return Maps.newHashMap();
        }
        String orderGuid = String.valueOf(orderDO.getGuid());
        List<TransactionRecordDO> transactionRecordDOS = transactionRecordService.listByOrderGuid(orderGuid);
        replenishTransactionRecord(orderGuid, transactionRecordDOS);
        return CollectionUtil.toListMap(transactionRecordDOS, "tradeType");
    }

    /**
     * 支付交易记录 和 优惠处理
     */
    private void transactionRecordAndDiscountHandler(DineinOrderDetailRespDTO orderDetailRespDTO, OrderDO orderDO,
                                                     Map<Integer, List<TransactionRecordDO>> tradeType, List<DiscountDO> discountDOS) {
        if ((orderDO.getState().equals(StateEnum.SUCCESS.getCode())
                || orderDO.getState().equals(StateEnum.ANTI_SETTLEMENT.getCode())
                || orderDO.getState().equals(StateEnum.SUB_SUCCESS.getCode()))
                && !orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
            List<TransactionRecordDO> successList = appendSuccessTransactionRecordDO(tradeType);
            // 实付记录
            List<TransactionRecordDO> transactionRecordS = buildSuccessTransactionRecord(successList);
            Map<Integer, DiscountDO> discountTypeMap = CollectionUtil.toMap(discountDOS, "discountType");
            DiscountDO thirdActivityDiscount = discountTypeMap.get(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
            BigDecimal discountFee = BigDecimal.ZERO;
            if (!ObjectUtils.isEmpty(thirdActivityDiscount)) {
                if (BigDecimalUtil.greaterThanZero(thirdActivityDiscount.getDiscountFee())) {
                    discountFee = thirdActivityDiscount.getDiscountFee();
                    BigDecimal excessAmount = Optional.ofNullable(orderDO.getExcessAmount()).orElse(BigDecimal.ZERO);
                    // 增加第三方活动相关内容
                    orderDetailRespDTO.setExcessAmount(excessAmount);
                    List<ThirdActivityDetailsRespDTO> detailList = thirdActivityClientService.detailByOrderGuid(String.valueOf(orderDO.getGuid()));
                    orderDetailRespDTO.setDetailList(detailList);

                    orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getActuallyPayFee()
                            .add(thirdActivityDiscount.getDiscountFee())
                            .subtract(excessAmount));
                }
                // 此处干掉第三方活动，避免显示
                discountDOS.removeIf(discountDO -> Objects.equals(thirdActivityDiscount.getGuid(), discountDO.getGuid()));
            }
            // 支付方式
            AmountCalculationUtil.repairAmount(oldCouponCalculateConfig, orderDetailRespDTO, transactionRecordS, discountDOS);
            // 会员支付
            orderMultiMemberPayHandler(orderDetailRespDTO);
            // 如果有第三方活动，查询时算作实收
            if (BigDecimalUtil.greaterThanZero(discountFee)) {
                discountFee = discountDOS.stream()
                        .map(DiscountDO::getDiscountFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                discountFee = orderDO.getOrderFee().subtract(orderDetailRespDTO.getActuallyPayFee());
            }
            orderDetailRespDTO.setDiscountFee(discountFee);
        }
    }


    private List<TransactionRecordDO> appendSuccessTransactionRecordDO(Map<Integer, List<TransactionRecordDO>> tradeType) {
        List<TransactionRecordDO> successList = Lists.newArrayList();
        List<TransactionRecordDO> generalOut = tradeType.get(TradeTypeEnum.GENERAL_OUT.getCode());
        List<TransactionRecordDO> preOut = tradeType.get(TradeTypeEnum.PRE_OUT.getCode());
        if (CollectionUtil.isNotEmpty(tradeType.get(TradeTypeEnum.GENERAL_IN.getCode()))) {
            successList.addAll(tradeType.get(TradeTypeEnum.GENERAL_IN.getCode()));
        }
        if (CollectionUtil.isEmpty(successList) && CollectionUtil.isNotEmpty(tradeType.get(TradeTypeEnum.PRE_IN.getCode()))) {
            successList.addAll(tradeType.get(TradeTypeEnum.PRE_IN.getCode()));
        } else {
            List<TransactionRecordDO> transactionRecordDOS1 = tradeType.get(TradeTypeEnum.PRE_IN.getCode());
            if (CollectionUtil.isNotEmpty(transactionRecordDOS1)) {
                successList.addAll(transactionRecordDOS1);
            }
        }
        if (CollectionUtil.isNotEmpty(generalOut)) {
            successList.addAll(generalOut);
        }
        if (CollectionUtil.isNotEmpty(preOut)) {
            successList.addAll(preOut);
        }
        if (CollectionUtil.isNotEmpty(tradeType.get(TradeTypeEnum.DEPOSIT_OUT.getCode()))) {
            successList.addAll(tradeType.get(TradeTypeEnum.DEPOSIT_OUT.getCode()));
        }
        if (CollectionUtil.isNotEmpty(tradeType.get(TradeTypeEnum.DEPOSIT_IN.getCode()))) {
            successList.addAll(tradeType.get(TradeTypeEnum.DEPOSIT_IN.getCode()));
        }
        return successList;
    }

    private void setIsMultipleActualAggPay(DineinOrderDetailRespDTO orderDetailRespDTO) {
        if (ObjectUtils.isEmpty(orderDetailRespDTO)) {
            return;
        }
        Boolean isMultipleActualAggPay = Boolean.FALSE;
        if (!ObjectUtils.isEmpty(orderDetailRespDTO.getIsMultipleAggPay()) && orderDetailRespDTO.getIsMultipleAggPay()) {
            List<MultipleTransactionRecordDO> multipleRecordDOList = multipleTransactionRecordService.listByOrderGuid(orderDetailRespDTO.getGuid());
            List<MultipleTransactionRecordDO> recordDOList = multipleRecordDOList.stream()
                    .filter(t -> t.getTradeType().equals(TradeTypeEnum.GENERAL_IN.getCode())
                            && BigDecimalUtil.greaterThanZero(t.getAmount().subtract(t.getRefundAmount())))
                    .collect(Collectors.toList());
            isMultipleActualAggPay = recordDOList.size() > 1;
        }
        orderDetailRespDTO.setIsMultipleActualAggPay(isMultipleActualAggPay);
    }

    private void replenishTransactionRecord(String orderGuid, List<TransactionRecordDO> transactionRecordDOS) {
        if (!org.springframework.util.CollectionUtils.isEmpty(transactionRecordDOS)) {
            transactionRecordDOS.removeIf(t -> PaymentTypeEnum.AGG.getCode() == t.getPaymentType()
                    && t.getIsMultipleAggPay().equals(Boolean.TRUE)
                    && !BigDecimalUtil.greaterThanZero(t.getAmount().subtract(t.getRefundAmount())));
        }
        List<MultipleTransactionRecordDO> multipleTransactionRecordDOList = multipleTransactionRecordService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(multipleTransactionRecordDOList)) {
            return;
        }
        multipleTransactionRecordDOList.forEach(multipleRecordDO -> multipleRecordDO.setAmount(
                multipleRecordDO.getAmount().subtract(multipleRecordDO.getRefundAmount())));
        List<MultipleTransactionRecordDO> recordDOList = multipleTransactionRecordDOList.stream()
                .filter(t -> BigDecimalUtil.greaterThanZero(t.getAmount()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(recordDOList)) {
            return;
        }
        // 删除原有聚合支付记录
        transactionRecordDOS.removeIf(t -> PaymentTypeEnum.AGG.getCode() == t.getPaymentType());
        List<TransactionRecordDO> multipleRecordDOList = OrderTransform.INSTANCE.multipleRecordDO2TransactionRecordDO(recordDOList);
        // 多笔聚合支付标记
        multipleRecordDOList.forEach(e -> e.setIsMultipleAggPay(multipleTransactionRecordDOList.size() > 1));
        transactionRecordDOS.addAll(multipleRecordDOList);
    }


    /**
     * 查询订单扩展信息
     */
    private void queryOrderExtendsInfo(DineinOrderDetailRespDTO orderDetailRespDTO) {
        OrderExtendsDO orderExtendsDO = orderExtendsService.getOne(new LambdaQueryWrapper<OrderExtendsDO>()
                .eq(OrderExtendsDO::getGuid, orderDetailRespDTO.getGuid()));
        if (Objects.isNull(orderExtendsDO)) {
            return;
        }
        log.info("订单扩展信息:{}", JacksonUtils.writeValueAsString(orderExtendsDO));
        orderDetailRespDTO.setTotalCouponBuyPrice(orderExtendsDO.getTotalCouponBuyPrice());
        orderDetailRespDTO.setReservePaymentType(orderExtendsDO.getReservePaymentType());
        orderDetailRespDTO.setReservePaymentTypeName(orderExtendsDO.getReservePaymentTypeName());
        orderDetailRespDTO.setAssociatedFlag(orderExtendsDO.getAssociatedFlag());
        orderDetailRespDTO.setAssociatedSn(orderExtendsDO.getAssociatedSn());
        orderDetailRespDTO.setAssociatedTableGuids(Objects.nonNull(orderExtendsDO.getAssociatedTableGuids()) ?
                JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableGuids()) : Lists.newArrayList());
        orderDetailRespDTO.setAssociatedTableNames(Objects.nonNull(orderExtendsDO.getAssociatedTableNames()) ?
                JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableNames()) : Lists.newArrayList());
        orderDetailRespDTO.setIsMultipleAggPay(orderExtendsDO.getIsMultipleAggPay());
        orderDetailRespDTO.setLudouMemberGuid(orderExtendsDO.getLudouMemberGuid());
        orderDetailRespDTO.setLudouMemberName(orderExtendsDO.getLudouMemberName());
        orderDetailRespDTO.setLudouMemberPhone(orderExtendsDO.getLudouMemberPhone());
        if (StringUtils.isNotEmpty(orderExtendsDO.getLudouMemberGuid())) {
            BillMemberCardCalculateRespDTO ludouMemberDTO = new BillMemberCardCalculateRespDTO();
            ludouMemberDTO.setLudouMemberName(orderExtendsDO.getLudouMemberName());
            ludouMemberDTO.setLudouMemberGuid(orderExtendsDO.getLudouMemberGuid());
            ludouMemberDTO.setLudouMemberPhone(orderExtendsDO.getLudouMemberPhone());
            orderDetailRespDTO.setLudouMemberDTO(ludouMemberDTO);
        }
    }

    private List<TransactionRecordDO> buildSuccessTransactionRecord(List<TransactionRecordDO> successList) {
        if (CollectionUtils.isEmpty(successList)) {
            return Lists.newArrayList();
        }
        List<TransactionRecordDO> transactionRecords = Lists.newArrayList();
        Map<Integer, List<TransactionRecordDO>> paymentType = CollectionUtil.toListMap(successList,
                "paymentType");
        for (Integer integer : paymentType.keySet()) {
            List<TransactionRecordDO> recordDOS = paymentType.get(integer);
            //============ 当支付方式为其他方式的时候，不需要合并支付 ================
            if (integer.equals(PaymentTypeEnum.OTHER.getCode())
                    || integer.equals(PaymentTypeEnum.RESERVE.getCode())
                    || integer.equals(PaymentTypeEnum.AGG.getCode())) {
                transactionRecords.addAll(recordDOS);
                continue;
            }
            if (CollectionUtil.isNotEmpty(recordDOS)) {
                if (recordDOS.size() > 1) {
                    BigDecimal agg = BigDecimal.ZERO;
                    for (TransactionRecordDO recordDO : recordDOS) {
                        agg = agg.add(recordDO.getAmount());
                    }
                    TransactionRecordDO transactionRecordDO = recordDOS.get(0);
                    transactionRecordDO.setAmount(agg);
                    transactionRecords.add(transactionRecordDO);
                } else {
                    transactionRecords.add(recordDOS.get(0));
                }
            }
        }
        return transactionRecords;
    }

    /**
     * 退款订单详情处理
     */
    private void refundOrderHandler(OrderDO orderDO, DineinOrderDetailRespDTO orderDetailRespDTO) {
        // 判断是否退款单
        if (orderDetailRespDTO.getState() != 3 || StringUtils.isBlank(orderDetailRespDTO.getRecoveryId())) {
            return;
        }
        // 查询原单
        OrderDO originalOrder = orderService.getById(orderDO.getOriginalOrderGuid());
        // 更新下单时间
        orderDetailRespDTO.setGmtCreate(originalOrder.getGmtCreate());
        // 补全退款单详情中的退款记录
        if ("0".equals(orderDetailRespDTO.getRecoveryId())) {
            // 部分退款单
            buildPartRefundOrder(orderDO, orderDetailRespDTO);
        } else {
            // 反结账退款单
            buildRecoveryOrder(originalOrder, orderDetailRespDTO);
        }
    }

    /**
     * 部分退款单
     */
    private void buildPartRefundOrder(OrderDO orderDO, DineinOrderDetailRespDTO orderDetailRespDTO) {
        // 查询退款记录
        List<RefundOrderRecordDTO> refundOrderRecordList = orderRefundRecordService.listByRefundOrderGuid(orderDO.getGuid());
        // 退款方式转换为前端展示
        for (RefundOrderRecordDTO recordDTO : refundOrderRecordList) {
            // 退款
            recordDTO.setRefundSource("退款");
            recordDTO.setDeviceTypeName(BaseDeviceTypeEnum.getDesc(recordDTO.getDeviceType()));
            if (StringUtils.isBlank(recordDTO.getRefundDetails()) || RefundTypeEnum.OFFLINE_REFUND.getCode() == recordDTO.getRefundType()) {
                continue;
            }
            List<AmountItemDTO> amountItemList = JacksonUtils.toObjectList(AmountItemDTO.class, recordDTO.getRefundDetails());
            recordDTO.setRefundDetails(buildRefundDetailsForAmountItem(amountItemList));
        }
        orderDetailRespDTO.setRefundOrderRecordList(refundOrderRecordList);
        // 退款单 商品列表 商品小计 = 数量 * 单价 (不加属性加价)
        for (DineInItemDTO itemDTO : orderDetailRespDTO.getDineInItemDTOS()) {
            if (Objects.nonNull(itemDTO.getRefundPrice())) {
                itemDTO.setItemPrice(itemDTO.getRefundPrice());
            } else {
                itemDTO.setItemPrice(itemDTO.getPrice().multiply(itemDTO.getCurrentCount()));
            }
        }
    }

    /**
     * 反结账单
     */
    private void buildRecoveryOrder(OrderDO originalOrder, DineinOrderDetailRespDTO orderDetailRespDTO) {
        RefundOrderRecordDTO refundOrderRecordDTO = new RefundOrderRecordDTO();
        ReturnOrderDetailDTO returnOrderDetailDTO = orderDetailRespDTO.getReturnOrderDetailDTO();
        refundOrderRecordDTO.setGmtCreate(returnOrderDetailDTO.getRecoveryTime());
        refundOrderRecordDTO.setRefundAmount(returnOrderDetailDTO.getRecoveryFee());
        refundOrderRecordDTO.setCreateStaffName(returnOrderDetailDTO.getRecoveryStaffName());
        refundOrderRecordDTO.setDeviceTypeName(returnOrderDetailDTO.getRecoveryDeviceTypeName());
        refundOrderRecordDTO.setRefundSource("反结账");
        refundOrderRecordDTO.setRefundReason(returnOrderDetailDTO.getRecoveryReason());
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailList = returnOrderDetailDTO.getActuallyPayFeeDetailDTOS();
        refundOrderRecordDTO.setRefundDetails(buildRefundDetailsForDetail(originalOrder, actuallyPayFeeDetailList));
        if (Boolean.TRUE.equals(orderDetailRespDTO.getIsMultipleAggPay()) && CollectionUtils.isNotEmpty(actuallyPayFeeDetailList)) {
            // 如果是多次聚合支付，则计算反结账金额
            BigDecimal refundAmount = actuallyPayFeeDetailList.stream()
                    .map(e -> e.getAmount().abs())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            refundOrderRecordDTO.setRefundAmount(refundAmount);
        }
        orderDetailRespDTO.setRefundOrderRecordList(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(actuallyPayFeeDetailList)) {
            orderDetailRespDTO.setRefundOrderRecordList(Lists.newArrayList(refundOrderRecordDTO));
        }
    }

    /**
     * 订单使用会员多卡支付信息
     */
    private void orderMultiMemberPayHandler(DineinOrderDetailRespDTO orderDetailRespDTO) {
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailList = orderDetailRespDTO.getActuallyPayFeeDetailDTOS();
        if (CollectionUtils.isEmpty(actuallyPayFeeDetailList)) {
            return;
        }
        ActuallyPayFeeDetailDTO memberPayment = actuallyPayFeeDetailList.stream()
                .filter(e -> PaymentTypeEnum.MEMBER.getCode() == e.getPaymentType())
                .findFirst().orElse(null);
        if (Objects.isNull(memberPayment) || !BigDecimalUtil.greaterThanZero(memberPayment.getAmount())) {
            return;
        }
        List<OrderMultiMember> orderMultiMembers = orderMultiMemberService.listByOrderGuid(Long.valueOf(orderDetailRespDTO.getGuid()));
        if (CollectionUtil.isEmpty(orderMultiMembers)) {
            return;
        }
        orderMultiMembers = orderMultiMembers.stream()
                .filter(e -> BigDecimalUtil.greaterThanZero(e.getAmount()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderMultiMembers)) {
            return;
        }
        memberPayment.setMultiMemberPays(OrderTransform.INSTANCE.orderMultiMember2OrderMultiMemberPayDTO(orderMultiMembers));
    }

    @Override
    @Transactional
    public Boolean cancelOrder(CancelOrderReqDTO cancelOrderReqDTO) {
        String orderGuid = cancelOrderReqDTO.getOrderGuid();
        OrderDO orderDO = orderService.getByIdWithLock(cancelOrderReqDTO.getOrderGuid());
        if (!OrderUtil.unfinished(orderDO)) {
            throw new ParameterException("只有未结账订单可以作废");
        }
        LocalDate businessDay = CommonUtil.getBusinessDay(storeClientService.queryStoreBizByGuid(
                cancelOrderReqDTO.getStoreGuid()));
        LocalDateTime now = LocalDateTime.now();
        orderDO.setCancelTime(now);
        orderDO.setCheckoutTime(now);
        orderDO.setCancelDeviceType(cancelOrderReqDTO.getDeviceType());
        orderDO.setCancelReason(cancelOrderReqDTO.getReason());
        orderDO.setCancelStaffGuid(UserContextUtils.getUserGuid());
        orderDO.setCancelStaffName(UserContextUtils.getUserName());
        orderDO.setState(Boolean.TRUE.equals(orderItemService.hasItem(Long.valueOf(orderGuid)))
                ? StateEnum.CANCEL.getCode() : StateEnum.INVALID.getCode());
        orderDO.setBusinessDay(businessDay);
        // 正餐 非反结账 进行拆单
        boolean recovery = !orderDO.getRecoveryType().equals(RecoveryTypeEnum.GENERAL.getCode());
        if (!cancelOrderReqDTO.isFastFood() && !recovery) {
            splitCancelHandler(orderDO);
        }
        // 查询订单详情
        DineinOrderDetailRespDTO orderDetail = getOrderDetail(orderGuid);
        if (!CollectionUtil.isEmpty(orderDetail.getSubOrderDetails())) {
            List<DineInItemDTO> collect = orderDetail.getSubOrderDetails().stream()
                    .flatMap(suborderDetail -> suborderDetail.getDineInItemDTOS().stream())
                    .collect(Collectors.toList());
            orderDetail.getDineInItemDTOS().addAll(collect);
        }
        //退团购券
        List<GrouponDO> availableGrouponList = grouponMpService.listByOrderGuid(orderGuid);
        if (CollectionUtil.isNotEmpty(availableGrouponList)) {
            availableGrouponList.forEach(g -> {
                GrouponReqDTO reqDTO = new GrouponReqDTO();
                reqDTO.setOrderGuid(orderGuid);
                reqDTO.setCouponCode(g.getCode());
                grouponService.revoke(reqDTO);
            });
        }
        // 快餐反结账已经退过不重复退
        if (CollectionUtil.isNotEmpty(orderDetail.getDineInItemDTOS()) && !orderDetail.getTradeMode().equals
                (TradeModeEnum.FAST.getCode())) {
            itemClientService.returnEstimate(orderDetail.getDineInItemDTOS());
        }
        // 退会员优惠
        if (CommonUtil.hasGuid(orderDO.getMemberConsumptionGuid())) {
            boolean member = memberTerminalClientService.cancelAll(orderDO.getMemberConsumptionGuid());
            log.error("会员优惠券撤销是否成功:{}", member);
        }
        log.info("[作废]orderDO={}", JacksonUtils.writeValueAsString(orderDO));
        if (BigDecimalUtil.greaterThanZero(orderDO.getReserveFee())) {
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setData(orderDO.getReserveGuid());
            log.info("作废订单退预定金：入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
            String refund = reserveClientService.refund(singleDataDTO);
            log.info("作废订单退预定金：返回：{}", refund);
            try {
                cancelReserveFee(cancelOrderReqDTO, orderDO);
            } catch (Exception e) {
                log.error("[作废订单撤销预付金]异常：{},e=｛｝", e.getMessage(), e);
            }
        }
        // 支付记录处理
        transactionRecordCancelHandler(cancelOrderReqDTO, orderDO);
        // 作废订单 子单预付金处理
        subOrderReserveFeeCancelHandler(orderDO);
        orderService.updateByIdWithDeleteCache(orderDO);
        // 删除订单商品缓存
        redisHelper.delete(RedisKeyUtil.getHstOrderItemKey(orderGuid));
        if (!cancelOrderReqDTO.isTable()) {
            // 更新桌台状态
            tableService.disabledChange(cancelOrderReqDTO, orderDO);
        }
        // 打印退菜单
        printCancelHandler(cancelOrderReqDTO, orderDetail);
        // 小程序取消订单 推送到赚餐
        cancelOrderAsyncTcd(cancelOrderReqDTO);
        //订单优惠券释放
        updateOrderCoupon(orderDetail, orderDO);
        // 多单结账 作废最后一笔单子
        if (Objects.equals(RecoveryTypeEnum.GENERAL.getCode(), orderDO.getRecoveryType())
                && UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())
                && Objects.nonNull(orderDO.getMainOrderGuid())) {
            cancelOrderReqDTO.setCheckoutOrderFlag(true);
        }
        return Boolean.TRUE;
    }

    private void cancelReserveFee(CancelOrderReqDTO cancelOrderReqDTO, OrderDO orderDO) {
        ReserveRecordGuidDTO guidDTO = new ReserveRecordGuidDTO();
        guidDTO.setGuid(orderDO.getReserveGuid());
        guidDTO.setReason(cancelOrderReqDTO.getReason());

        List<TableDTO> tables = Lists.newArrayList();
        if (Objects.equals(orderDO.getUpperState(), UpperStateEnum.MAIN.getCode())) {
            SingleDataDTO dto = new SingleDataDTO();
            dto.setData(cancelOrderReqDTO.getOrderGuid());
            List<TableBasicDTO> tableBasicDTOList = tableClientService.queryCombineListByMainOrder(dto);
            if (!org.springframework.util.CollectionUtils.isEmpty(tableBasicDTOList)) {
                tableBasicDTOList.forEach(t -> {
                    TableDTO tableDTO = new TableDTO();
                    tableDTO.setGuid(t.getGuid());
                    tableDTO.setName(t.getTableCode());
                    tableDTO.setAreaGuid(t.getAreaGuid());
                    tableDTO.setAreaName(t.getAreaName());
                    tables.add(tableDTO);
                });
            }
            guidDTO.setTables(tables);
        } else {
            TableBasicDTO tableBasicDTO = tableClientService.queryTableInfo(orderDO.getDiningTableGuid());
            if (!ObjectUtils.isEmpty(tableBasicDTO)) {
                TableDTO tableDTO = new TableDTO();
                tableDTO.setGuid(tableBasicDTO.getGuid());
                tableDTO.setName(tableBasicDTO.getTableCode());
                tableDTO.setAreaGuid(tableBasicDTO.getAreaGuid());
                tableDTO.setAreaName(tableBasicDTO.getAreaName());
                tables.add(tableDTO);
            }
            guidDTO.setTables(tables);
        }

        guidDTO.setOrderType(OrderTypeEnum.RESERVE_PAY.getCode());
        guidDTO.setOrderGuid(cancelOrderReqDTO.getOrderGuid());
        guidDTO.setReserveRefundAmount(orderDO.getReserveFee());
        guidDTO.setDeviceId(cancelOrderReqDTO.getDeviceId());
        guidDTO.setEnterpriseGuid(cancelOrderReqDTO.getEnterpriseGuid());
        guidDTO.setEnterpriseName(cancelOrderReqDTO.getEnterpriseName());
        guidDTO.setStoreGuid(cancelOrderReqDTO.getStoreGuid());
        guidDTO.setStoreName(cancelOrderReqDTO.getStoreName());
        guidDTO.setUserGuid(cancelOrderReqDTO.getUserGuid());
        guidDTO.setUserName(cancelOrderReqDTO.getUserName());
        guidDTO.setRequestTimestamp(cancelOrderReqDTO.getRequestTimestamp());
        guidDTO.setDeviceType(BaseDeviceTypeEnum.All_IN_ONE.getCode());
        log.info("[作废订单撤销预付金]guidDTO={}", JacksonUtils.writeValueAsString(guidDTO));
        reserveClientService.cancle(guidDTO);
    }

    private void updateOrderCoupon(DineinOrderDetailRespDTO orderDetail,
                                   OrderDO orderDO) {
        UserContext userContext = UserContextUtils.get();
        log.info("结账释放订单关联优惠券,订单={},会员={}", orderDetail.getGuid(), orderDetail.getMemberGuid());
        if ((StringUtils.isEmpty(orderDetail.getMemberGuid()) || orderDetail.getMemberGuid().equals("0"))
                && Objects.nonNull(orderDO.getIsUpdatedEs())
                && orderDO.getIsUpdatedEs() == BooleanEnum.TRUE.getCode()) {
            try {
                RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
                request.setOrderGuid(orderDetail.getGuid());
                request.setOperSubjectGuid(userContext.getOperSubjectGuid());
                log.info("结账门店优惠券关联修改入参：{}", JacksonUtils.writeValueAsString(request));
                //门店优惠券关联关系切换
                memberTerminalClientService.updateVolumeRelevance(request);
                //赚餐优惠券关联关系切换
                RequestUpdateCouponDTO redeemCodeApplyDTO = new RequestUpdateCouponDTO();
                redeemCodeApplyDTO.setSource(Integer.valueOf(userContext.getSource()));
                redeemCodeApplyDTO.setStoreGuid(userContext.getStoreGuid());
                redeemCodeApplyDTO.setStoreName(userContext.getStoreName());
                redeemCodeApplyDTO.setOrderGuid(orderDetail.getGuid());
                redeemCodeApplyDTO.setEnterpriseGuid(userContext.getEnterpriseGuid());
                redeemCodeApplyDTO.setOperSubjectGuid(userContext.getOperSubjectGuid());
                log.info("结账赚餐优惠券关联修改入参：{}", JacksonUtils.writeValueAsString(redeemCodeApplyDTO));
                HttpsClientUtils.doPost(zhuanCanConfig.getUpdateVolumeRelevance(), JacksonUtils.writeValueAsString(redeemCodeApplyDTO));
            } catch (Exception e) {
                log.info("一体机结账优惠券关联修改失败：{}", e.getMessage());
            }
        }
    }

    /**
     * 小程序取消订单 推送到赚餐
     */
    private void cancelOrderAsyncTcd(CancelOrderReqDTO cancelOrderReqDTO) {
        if (Objects.isNull(cancelOrderReqDTO.getDeviceType())) {
            return;
        }
        if (!BaseDeviceTypeEnum.isApplet(cancelOrderReqDTO.getDeviceType())) {
            return;
        }
        log.info("小程序取消订单推送到赚餐,orderGuid:{}", cancelOrderReqDTO.getOrderGuid());
        tcdOrderService.syncOrderState(cancelOrderReqDTO.getOrderGuid());
    }

    private void aggRefund(BaseDTO baseDTO, List<MultipleTransactionRecordDO> transactionRecordList) {
        if (CollectionUtils.isEmpty(transactionRecordList)) {
            return;
        }
        List<MultipleTransactionRecordDO> aggTransactionRecordList = transactionRecordList.stream()
                .filter(e -> e.getPaymentType().equals(PaymentTypeEnum.AGG.getCode()) &&
                        e.getTradeType().equals(TradeTypeEnum.GENERAL_IN.getCode()) &&
                        e.getState().equals(StateEnum.SUCCESS.getCode()) &&
                        BigDecimalUtil.greaterThan(e.getRefundableFee(), BigDecimal.ZERO))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aggTransactionRecordList)) {
            return;
        }
        for (MultipleTransactionRecordDO transactionRecordDO : aggTransactionRecordList) {
            AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
            if (transactionRecordDO.getRefundableFee() != null && transactionRecordDO.getAmount().equals
                    (transactionRecordDO.getRefundableFee())) {
                aggRefundReqDTO.setRefundType(0);
            } else {
                aggRefundReqDTO.setRefundType(1);
            }
            aggRefundReqDTO.setRefundFee(transactionRecordDO.getRefundableFee());
            aggRefundReqDTO.setPayGUID(String.valueOf(transactionRecordDO.getGuid()));
            aggRefundReqDTO.setOrderGUID(String.valueOf(transactionRecordDO.getOrderGuid()));
            if (Objects.nonNull(transactionRecordDO.getOriginalMultipleTransactionRecordGuid())) {
                // 如果是反结账之后的，则使用反结账之前的payGuid
                aggRefundReqDTO.setPayGUID(String.valueOf(transactionRecordDO.getOriginalMultipleTransactionRecordGuid()));
            }
            if (Objects.nonNull(transactionRecordDO.getOriginalOrderGuid())) {
                // 如果是反结账之后的，则使用反结账之前的orderGuid
                aggRefundReqDTO.setOrderGUID(String.valueOf(transactionRecordDO.getOriginalOrderGuid()));
            }
            aggRefundReqDTO.setTimestamp(DateTimeUtils.nowMillis());
            aggRefundReqDTO.setReason("退款");
            SaasAggRefundDTO saasAggRefundDTO = orderTransform.cancelOrderReqDTO2SaasAggRefundDTO(baseDTO);
            saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
            log.info("聚合作废订单时退款入参：{}", JacksonUtils.writeValueAsString(saasAggRefundDTO));
            AggRefundRespDTO refundRespDTO = aggPayClientService.refund(saasAggRefundDTO);
            BillVerifyHelper.refundResult(refundRespDTO, false);
            // 退款后可退款金额清零
            transactionRecordDO.setRefundAmount(transactionRecordDO.getAmount());
            transactionRecordDO.setRefundableFee(BigDecimal.ZERO);
            // 更新交易记录
            persistenceRefundAmount(transactionRecordDO);
        }
    }

    /**
     * 更新交易记录
     */
    private void persistenceRefundAmount(MultipleTransactionRecordDO transactionRecordDO) {
        UserContext userContext = UserContextUtils.get();
        aggTransactionRecordExecutor.execute(() -> {
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            log.info("保存交易记录退款信息:{}", JacksonUtils.writeValueAsString(transactionRecordDO));
            Long transactionRecordGuid = transactionRecordDO.getTransactionRecordGuid();
            if (Objects.isNull(transactionRecordGuid)) {
                // 如果为空，则说明是交易记录
                TransactionRecordDO transactionRecord = TransactionRecordTransform.INSTANCE
                        .multipleDO2TransactionRecordDO(transactionRecordDO);
                transactionRecordService.updateById(transactionRecord);
            } else {
                // 如果不为空，则说明是聚合支付多次支付
                multipleTransactionRecordService.updateById(transactionRecordDO);
                transactionRecordService.updateRefundAmountByGuid(transactionRecordGuid, transactionRecordDO.getAmount());
            }
        });
    }

    /**
     * 订单作废 交易支付记录处理
     */
    private void transactionRecordCancelHandler(CancelOrderReqDTO cancelOrderReqDTO, OrderDO orderDO) {
        //bugFixed:18527,只有主桌去扣减，子桌不扣减
        Boolean isMain = orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode());
        //bugFixed:20811,无并单的普通桌台关台时，也要生成退还预订金记录
        //bugFixed:20881,普通桌台isGeneral=true的判断；并台拆台后的子桌桌台不走判断isMain=false,isGeneral=false,isCalc=-1;
        Boolean isGeneral = orderDO.getUpperState().equals(UpperStateEnum.GENERAL.getCode())
                && (Objects.isNull(orderDO.getMainOrderGuid()) || orderDO.getMainOrderGuid() == 0);
        log.info("orderGuid={},upperState={},isMain={},isCalc={},isGeneral={}",
                orderDO.getGuid(), orderDO.getUpperState(), isMain, cancelOrderReqDTO.getIsCalc(), isGeneral);
        if (cancelOrderReqDTO.getIsCalc() != null && cancelOrderReqDTO.getIsCalc() == -1) {
            if ((isMain || isGeneral) && !StringUtils.isEmpty(orderDO.getReserveGuid()) && BigDecimalUtil.greaterThanZero(orderDO.getReserveFee())) {
                log.info(orderDO.getReserveGuid() + "插入相反的两条记录");
                List<TransactionRecordDO> saveTran = new ArrayList<>();
                TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
                Long guid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
                transactionRecordDO.setGuid(guid);
                transactionRecordDO.setOrderGuid(orderDO.getGuid());
                transactionRecordDO.setAmount(orderDO.getReserveFee().negate());
                transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
                transactionRecordDO.setPaymentType(PaymentTypeEnum.CASH.getCode());
                transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.CASH.getDesc());
                transactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_OUT.getCode());
                transactionRecordDO.setCreateTime(DateTimeUtils.now());
                transactionRecordDO.setStoreGuid(orderDO.getStoreGuid());
                saveTran.add(transactionRecordDO);
                String jsonStr = JacksonUtils.writeValueAsString(transactionRecordDO);
                TransactionRecordDO transactionRecordDO1 = JacksonUtils.toObject(TransactionRecordDO.class, jsonStr);
                Long guid1 = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
                transactionRecordDO1.setGuid(guid1);
                transactionRecordDO1.setPaymentTypeName(PaymentTypeEnum.RESERVE.getDesc());
                transactionRecordDO1.setPaymentType(PaymentTypeEnum.RESERVE.getCode());
                transactionRecordDO1.setAmount(orderDO.getReserveFee());
                saveTran.add(transactionRecordDO1);
                transactionRecordService.saveBatch(saveTran);
            }
        } else {
            if (!StringUtils.isEmpty(orderDO.getReserveGuid()) && BigDecimalUtil.greaterThanZero(orderDO.getReserveFee())) {
                log.info(orderDO.getReserveGuid() + "插入相反的两条记录");
                List<TransactionRecordDO> saveTran = new ArrayList<>();
                TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
                Long guid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
                transactionRecordDO.setGuid(guid);
                transactionRecordDO.setOrderGuid(orderDO.getGuid());
                transactionRecordDO.setAmount(orderDO.getReserveFee().negate());
                transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
                transactionRecordDO.setPaymentType(PaymentTypeEnum.CASH.getCode());
                transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.CASH.getDesc());
                transactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_OUT.getCode());
                transactionRecordDO.setCreateTime(DateTimeUtils.now());
                transactionRecordDO.setStoreGuid(orderDO.getStoreGuid());
                saveTran.add(transactionRecordDO);
                String jsonStr = JacksonUtils.writeValueAsString(transactionRecordDO);
                TransactionRecordDO transactionRecordDO1 = JacksonUtils.toObject(TransactionRecordDO.class, jsonStr);
                Long guid1 = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
                transactionRecordDO1.setGuid(guid1);
                transactionRecordDO1.setPaymentTypeName(PaymentTypeEnum.RESERVE.getDesc());
                transactionRecordDO1.setPaymentType(PaymentTypeEnum.RESERVE.getCode());
                transactionRecordDO1.setAmount(orderDO.getReserveFee());
                saveTran.add(transactionRecordDO1);
                transactionRecordService.saveBatch(saveTran);
            }
        }
        // 订单聚合支付退款
        aggTransactionRecordHandler(cancelOrderReqDTO, cancelOrderReqDTO.getOrderGuid());
        if (Boolean.TRUE.equals(isMain)) {
            // 并台
            // 查询子订单
            List<OrderDO> subOrders = orderService.listByMainOrderGuid(String.valueOf(orderDO.getGuid()));
            for (OrderDO subOrder : subOrders) {
                aggTransactionRecordHandler(cancelOrderReqDTO, String.valueOf(subOrder.getGuid()));
            }
        }
    }

    /**
     * 聚合支付交易记录处理
     */
    private void aggTransactionRecordHandler(BaseDTO baseDTO, String orderGuid) {
        // 查询订单信息
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderGuid);
        if (Objects.nonNull(orderExtendsDO) && Boolean.TRUE.equals(orderExtendsDO.getIsMultipleAggPay())) {
            // 聚合支付多次支付
            List<MultipleTransactionRecordDO> multipleTransactionRecordList =
                    multipleTransactionRecordService.listByOrderGuid(String.valueOf(orderExtendsDO.getGuid()));
            multipleTransactionRecordList = multipleTransactionRecordList.stream()
                    .filter(e -> e.getPaymentType().equals(PaymentTypeEnum.AGG.getCode()) &&
                            e.getTradeType().equals(TradeTypeEnum.GENERAL_IN.getCode()) &&
                            e.getState().equals(StateEnum.SUCCESS.getCode()) &&
                            BigDecimalUtil.greaterThan(e.getRefundableFee(), BigDecimal.ZERO))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(multipleTransactionRecordList)) {
                log.info("聚合支付多次支付开始退款:{}", JacksonUtils.writeValueAsString(multipleTransactionRecordList));
                aggRefund(baseDTO, multipleTransactionRecordList);
            }
        } else {
            // 正常订单作废时退款(聚合支付)
            List<TransactionRecordDO> transactionRecordDOS = transactionRecordService.listJhAndPreByOrderGuid(orderGuid);
            if (!CollectionUtil.isEmpty(transactionRecordDOS)) {
                List<MultipleTransactionRecordDO> transactionRecordList = TransactionRecordTransform.INSTANCE
                        .transactionRecordDO2multipleDOList(transactionRecordDOS);
                aggRefund(baseDTO, transactionRecordList);
            }
        }
    }


    /**
     * 作废订单 子单预付金处理
     */
    private void subOrderReserveFeeCancelHandler(OrderDO orderDO) {
        //BugFixed：18501 关闭主桌的时候，将子桌的reserveFee设置为0
        if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode()) && BigDecimalUtil.greaterThanZero(orderDO.getReserveFee())) {
            List<OrderDO> subOrders = orderService.listByMainOrderGuid(String.valueOf(orderDO.getGuid()));
            for (OrderDO order : subOrders) {
                order.setReserveFee(BigDecimal.ZERO);
            }
            if (!ObjectUtils.isEmpty(subOrders)) {
                orderService.saveOrUpdateBatch(subOrders);
            }
        }
    }

    /**
     * 订单作废 拆单
     */
    private void splitCancelHandler(OrderDO orderDO) {
        //作废订单时拆单
        TableOrderCombineDTO tableOrderCombineDTO;
        if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
            tableOrderCombineDTO = new TableOrderCombineDTO();
            tableOrderCombineDTO.setMainOrderGuid(String.valueOf(orderDO.getGuid()));
            combineOrderService.split(tableOrderCombineDTO);
        }
        if (orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
            //BugFixed：18527子单暂时不拆，最后全部关闭完毕之后，再恢复订单的upper_state
            if (StringUtils.isEmpty(orderDO.getReserveGuid())) {
                tableOrderCombineDTO = new TableOrderCombineDTO();
                tableOrderCombineDTO.setMainOrderGuid(String.valueOf(orderDO.getMainOrderGuid()));
                List<TableInfoDTO> tableInfoDTOS = new ArrayList<>();
                TableInfoDTO tableInfoDTO = new TableInfoDTO();
                tableInfoDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
                tableInfoDTOS.add(tableInfoDTO);
                tableOrderCombineDTO.setTableInfoDTOS(tableInfoDTOS);
                combineOrderService.split(tableOrderCombineDTO);
            }
        }
    }

    /**
     * 作废订单 打印作废单
     */
    private void printCancelHandler(CancelOrderReqDTO cancelOrderReqDTO, DineinOrderDetailRespDTO orderDetail) {
        List<DineInItemDTO> dineInItemDTOS = orderDetail.getDineInItemDTOS();
        Iterator<DineInItemDTO> iterator = dineInItemDTOS.iterator();
        while (iterator.hasNext()) {
            DineInItemDTO dineInItemDTO = iterator.next();
            if (dineInItemDTO.getItemState().equals(ItemStateEnum.HANG_UP.getCode())) {
                iterator.remove();
            } else {
                dineInItemDTO.setCurrentCount(dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount()));
            }
        }
        //快餐只有新单作废时打印退菜单(快餐的普通单不打印)
        if (!(orderDetail.getTradeMode().equals(TradeModeEnum.FAST.getCode()) && orderDetail.getRecoveryType().equals
                (RecoveryTypeEnum.GENERAL.getCode()))) {
            dineInPrintService.printRefundItem(cancelOrderReqDTO, orderDetail);
        }
    }


    @Override
    @SuppressWarnings("all")
    public Page<DineInOrderListRespDTO> orderList(DineInOrderListReqDTO dineInOrderListReqDTO) {
        LambdaQueryWrapper<OrderDO> orderDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderDOLambdaQueryWrapper.eq(OrderDO::getStoreGuid, UserContextUtils.getStoreGuid());
        orderDOLambdaQueryWrapper.eq(OrderDO::getTradeMode, dineInOrderListReqDTO.getTradeMode());
        //时间区间
        orderDOLambdaQueryWrapper.ge(dineInOrderListReqDTO.getBeginTime() != null, OrderDO::getGmtCreate,
                dineInOrderListReqDTO.getBeginTime()).le(dineInOrderListReqDTO.getEndTime() != null,
                OrderDO::getGmtCreate, dineInOrderListReqDTO.getEndTime());
        //订单状态
        if (dineInOrderListReqDTO.getState() != null) {
            //未结账
            if (dineInOrderListReqDTO.getState() == 0) {
                orderDOLambdaQueryWrapper.between(OrderDO::getState, StateEnum.READY.getCode(), StateEnum.FAILURE
                        .getCode());
                //不显示既是子单又是反结账的
                orderDOLambdaQueryWrapper.and(i -> i.ne(OrderDO::getUpperState, UpperStateEnum.SUB.getCode()).or()
                        .isNull(OrderDO::getRecoveryId));
                //快餐未结账不显示微信
                if (dineInOrderListReqDTO.getTradeMode().equals(TradeModeEnum.FAST.getCode())) {
                    orderDOLambdaQueryWrapper.ne(OrderDO::getDeviceType, BaseDeviceTypeEnum.WECHAT.getCode());
                }

            }
            //已结账
            if (dineInOrderListReqDTO.getState() == 1) {
                orderDOLambdaQueryWrapper.eq(OrderDO::getState, StateEnum.SUCCESS.getCode()).ne
                        (OrderDO::getUpperState, UpperStateEnum.SUB.getCode());
            }
            //已作废/退单
            if (dineInOrderListReqDTO.getState() == 2) {
                orderDOLambdaQueryWrapper.between(OrderDO::getState, StateEnum.REFUNDED.getCode(), StateEnum.CANCEL
                        .getCode());
                //不显示既是子单又是反结账的
                orderDOLambdaQueryWrapper.and(i -> i.ne(OrderDO::getUpperState, UpperStateEnum.SUB.getCode()).or()
                        .isNull(OrderDO::getRecoveryId));
            }
        } else {
            //全部
            orderDOLambdaQueryWrapper.ne(OrderDO::getUpperState, UpperStateEnum.SUB.getCode());
            orderDOLambdaQueryWrapper.ne(OrderDO::getState, StateEnum.INVALID.getCode());
            //快餐不显示既是微信又是未结账的
            if (dineInOrderListReqDTO.getTradeMode().equals(TradeModeEnum.FAST.getCode())) {
                orderDOLambdaQueryWrapper.and(i -> i.notIn(OrderDO::getDeviceType, BaseDeviceTypeEnum.DEVICE_TYPE_2C)
                        .or().ne(OrderDO::getState, StateEnum.READY.getCode()));
                orderDOLambdaQueryWrapper.and(i -> i.notIn(OrderDO::getCancelDeviceType, BaseDeviceTypeEnum.DEVICE_TYPE_2C)
                        .or().isNull(OrderDO::getCancelDeviceType));
            }
        }

        //关键字
        String searchKey = dineInOrderListReqDTO.getSearchKey();
        if (StringUtils.isNotEmpty(searchKey)) {
            orderDOLambdaQueryWrapper.and(
                    i -> i.like(OrderDO::getOrderNo, searchKey)
                            .or()
                            .like(OrderDO::getDiningTableName, searchKey)
                            .or()
                            .like(OrderDO::getMark, searchKey)
                            .or()
                            .like(OrderDO::getMemberPhone, searchKey)
            );
        }
        // 原来用的是wrapper构造器，在不进行大修改的情况下用int进行排序判断
        SortTypeEnum sortTypeEnum = SortTypeEnum.sortByType(dineInOrderListReqDTO.getSortType());
        switch (sortTypeEnum) {
            case GMT_CREATE_ASC:
                orderDOLambdaQueryWrapper.orderByAsc(OrderDO::getGmtCreate);
                break;
            case GMT_CREATE_DESC:
                orderDOLambdaQueryWrapper.orderByDesc(OrderDO::getGmtCreate);
                break;
            case ORDER_FEE_ASC:
                orderDOLambdaQueryWrapper.orderByAsc(OrderDO::getOrderFee);
                break;
            case ORDER_FEE_DESC:
                orderDOLambdaQueryWrapper.orderByDesc(OrderDO::getOrderFee);
                break;
            case CHECKOUT_TIME_ASC:
                orderDOLambdaQueryWrapper.last("ORDER BY IF(checkout_time IS NULL,NOW(),checkout_time) ASC");
                break;
            case CHECKOUT_TIME_DESC:
                orderDOLambdaQueryWrapper.last("ORDER BY IF(checkout_time IS NULL,NOW(),checkout_time) DESC");
                break;
            default:
                orderDOLambdaQueryWrapper.orderByDesc(OrderDO::getGmtCreate);
        }

        IPage<OrderDO> page = orderService.page(new PageAdapter<>(dineInOrderListReqDTO), orderDOLambdaQueryWrapper);
        List<DineInOrderListRespDTO> dineInOrderListRespList = orderTransform.orderDOList2DineInOrderListRespDTOList
                (page.getRecords());
        // 订单状态
        transferOrderState(dineInOrderListRespList);
        // 转换订单桌台名称
        transferOrderDiningTableName(dineInOrderListRespList);
        return new PageAdapter<>(page, dineInOrderListRespList);
    }


    /**
     * 转换订单状态
     */
    private void transferOrderState(List<DineInOrderListRespDTO> dineInOrderListRespList) {
        if (CollectionUtils.isEmpty(dineInOrderListRespList)) {
            return;
        }
        for (DineInOrderListRespDTO dineInOrderListRespDTO : dineInOrderListRespList) {
            Integer state = dineInOrderListRespDTO.getState();
            if (StateEnum.READY.getCode() <= state && state <= StateEnum.FAILURE.getCode()) {
                dineInOrderListRespDTO.setState(1);
            }
            if (StateEnum.SUCCESS.getCode() == state) {
                dineInOrderListRespDTO.setState(2);
            }
            if (StateEnum.REFUNDED.getCode() == state) {
                dineInOrderListRespDTO.setState(3);
            }
            if (StateEnum.CANCEL.getCode() == state) {
                dineInOrderListRespDTO.setState(4);
            }
        }
    }

    /**
     * 转换订单桌台名称
     * 联台单 -> 联台-1 (10)
     */
    private void transferOrderDiningTableName(DineinOrderDetailRespDTO dineinOrderDetailResp) {
        if (Objects.isNull(dineinOrderDetailResp)) {
            return;
        }
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(dineinOrderDetailResp.getGuid());
        if (Objects.isNull(orderExtendsDO) || !Boolean.TRUE.equals(orderExtendsDO.getAssociatedFlag())) {
            return;
        }
        String associatedTableNames = orderExtendsDO.getAssociatedTableNames();
        List<String> associatedTableNameList = JacksonUtils.toObjectList(String.class, associatedTableNames);
        String associatedTableName = "联台-%s (%s)";
        dineinOrderDetailResp.setDiningTableName(String.format(associatedTableName, orderExtendsDO.getAssociatedSn(), associatedTableNameList.size()));
    }


    private void setState(List<DineInOrderListRespDTO> dineInOrderListRespDTOS) {
        for (DineInOrderListRespDTO dineInOrderListRespDTO : dineInOrderListRespDTOS) {
            Integer state = dineInOrderListRespDTO.getState();
            if (StateEnum.READY.getCode() <= state && state <= StateEnum.FAILURE.getCode()) {
                dineInOrderListRespDTO.setState(1);
                dineInOrderListRespDTO.setStateName("未结账");
            }
            if (StateEnum.SUCCESS.getCode() == state) {
                dineInOrderListRespDTO.setState(2);
                dineInOrderListRespDTO.setStateName("已结账");
            }
            if (StateEnum.REFUNDED.getCode() == state) {
                dineInOrderListRespDTO.setState(3);
                dineInOrderListRespDTO.setStateName("已退款");
            }
            if (StateEnum.CANCEL.getCode() == state) {
                dineInOrderListRespDTO.setState(4);
                dineInOrderListRespDTO.setStateName("已作废");
            }
            if (StateEnum.ANTI_SETTLEMENT.getCode() == state) {
                dineInOrderListRespDTO.setStateName(StateEnum.ANTI_SETTLEMENT.getDesc());
            }
            if (StateEnum.SUB_SUCCESS.getCode() == state) {
                dineInOrderListRespDTO.setState(StateEnum.SUB_SUCCESS.getCode());
                dineInOrderListRespDTO.setStateName("未结账");
            }
        }
    }

    private void setPaymentTypeName(List<DineInOrderListRespDTO> dineInOrderListRespDTOS) {
        if (CollectionUtils.isEmpty(dineInOrderListRespDTOS)) {
            return;
        }
        Map<Long, Long> orderGuidMap = Maps.newHashMap();
        // 多单结账的订单处理相关状态返回
        sameOrderRespHandler(orderGuidMap, dineInOrderListRespDTOS);
        // 查询支付记录
        List<TransactionRecordDO> transactionRecordList = transactionRecordService.listByOrderGuids(new ArrayList<>(orderGuidMap.keySet()));
        List<TransactionRecordDO> filterTransactionRecordList = filterTransactionRecordList(transactionRecordList);
        // 退款单
        List<DineInOrderListRespDTO> hasRefundOrders = dineInOrderListRespDTOS.stream()
                .filter(e -> StringUtil.isNotEmpty(e.getRefundOrderGuid()))
                .collect(Collectors.toList());
        Map<String, String> hasRefundOrderMap = hasRefundOrders.stream()
                .collect(Collectors.toMap(DineInOrderListRespDTO::getRefundOrderGuid,
                        DineInOrderListRespDTO::getGuid, (key1, key2) -> key1));
        List<TransactionRecordDO> refundTransactionRecordList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(hasRefundOrders)) {
            List<Long> hasRefundOrderGuids = hasRefundOrderMap.keySet().stream().map(Long::valueOf).collect(Collectors.toList());
            refundTransactionRecordList = transactionRecordService.listByOrderGuids(hasRefundOrderGuids);
        }
        if (CollectionUtils.isNotEmpty(transactionRecordList)) {
            Map<Long, List<BigDecimal>> paymentTypeAmountMap = Maps.newHashMap();
            for (TransactionRecordDO transactionRecordDO : transactionRecordList) {
                Long targetOrderGuid = orderGuidMap.getOrDefault(transactionRecordDO.getOrderGuid(), transactionRecordDO.getOrderGuid());
                List<BigDecimal> paymentTypeAmountList = paymentTypeAmountMap.getOrDefault(targetOrderGuid, Lists.newArrayList());
                paymentTypeAmountList.add(transactionRecordDO.getAmount());
                paymentTypeAmountMap.put(targetOrderGuid, paymentTypeAmountList);
            }
            for (DineInOrderListRespDTO dineInOrderListRespDTO : dineInOrderListRespDTOS) {
                if ((dineInOrderListRespDTO.getState().equals(StateEnum.SUCCESS.getCode())
                        || dineInOrderListRespDTO.getState().equals(StateEnum.ANTI_SETTLEMENT.getCode())
                        || dineInOrderListRespDTO.getState().equals(StateEnum.SUB_SUCCESS.getCode())
                        ||  dineInOrderListRespDTO.getState().equals(StateEnum.REFUNDED.getCode()))
                        && !dineInOrderListRespDTO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
                    List<BigDecimal> paymentTypeAmountList = paymentTypeAmountMap.get(Long.valueOf(dineInOrderListRespDTO.getGuid()));
                    if (CollectionUtils.isNotEmpty(paymentTypeAmountList)) {
                        BigDecimal paymentTypeAmountTotal = paymentTypeAmountList.stream().map(BigDecimal::abs).reduce(BigDecimal.ZERO, BigDecimal::add);
                        dineInOrderListRespDTO.setActuallyPayFee(paymentTypeAmountTotal);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(filterTransactionRecordList) && CollectionUtils.isEmpty(refundTransactionRecordList)) {
            return;
        }
        Map<Long, List<String>> paymentTypeNameMap = Maps.newHashMap();
        for (TransactionRecordDO transactionRecordDO : filterTransactionRecordList) {
            Long targetOrderGuid = orderGuidMap.getOrDefault(transactionRecordDO.getOrderGuid(), transactionRecordDO.getOrderGuid());
            List<String> paymentTypeNameList = paymentTypeNameMap.getOrDefault(targetOrderGuid, Lists.newArrayList());
            paymentTypeNameList.add(transactionRecordDO.getPaymentTypeName());
            paymentTypeNameMap.put(targetOrderGuid, paymentTypeNameList);
        }
        for (TransactionRecordDO transactionRecordDO : refundTransactionRecordList) {
            Long refundOrderGuid = transactionRecordDO.getOrderGuid();
            String orderGuid = hasRefundOrderMap.get(String.valueOf(refundOrderGuid));
            if (Objects.isNull(orderGuid)) {
                continue;
            }
            Long targetOrderGuid = orderGuidMap.getOrDefault(Long.valueOf(orderGuid), transactionRecordDO.getOrderGuid());
            List<String> paymentTypeNameList = paymentTypeNameMap.getOrDefault(targetOrderGuid, Lists.newArrayList());
            paymentTypeNameList.add(transactionRecordDO.getPaymentTypeName());
            paymentTypeNameMap.put(targetOrderGuid, paymentTypeNameList);
        }
        for (DineInOrderListRespDTO dineInOrderListRespDTO : dineInOrderListRespDTOS) {
            List<String> paymentTypeNameList = paymentTypeNameMap.get(Long.valueOf(dineInOrderListRespDTO.getGuid()));
            if (CollectionUtils.isNotEmpty(paymentTypeNameList)) {
                paymentTypeNameList = paymentTypeNameList.stream()
                        .sorted(Comparator.comparing(e -> e))
                        .distinct()
                        .collect(Collectors.toList());
                dineInOrderListRespDTO.setPaymentTypeName(org.apache.commons.lang.StringUtils.join(paymentTypeNameList, ","));
            }
        }
    }

    /**
     * 多单结账的订单处理相关状态返回
     */
    private void sameOrderRespHandler(Map<Long, Long> orderGuidMap, List<DineInOrderListRespDTO> dineInOrderListRespDTOS) {
        List<Long> sameOrderGuids = dineInOrderListRespDTOS.stream()
                .filter(e -> UpperStateEnum.SAME_ORDER_STATE.contains(e.getUpperState()))
                .map(e -> Long.valueOf(e.getGuid()))
                .distinct().collect(Collectors.toList());
        List<Integer> paySuccessStates = Lists.newArrayList(StateEnum.SUCCESS.getCode(),
                StateEnum.REFUNDED.getCode(), StateEnum.SUB_SUCCESS.getCode());
        List<OrderDO> sameOrders = orderService.otherListByMainOrderGuids(sameOrderGuids);
        for (DineInOrderListRespDTO dineInOrderListRespDTO : dineInOrderListRespDTOS) {
            Long orderGuid = Long.valueOf(dineInOrderListRespDTO.getGuid());
            if (!UpperStateEnum.SAME_ORDER_STATE.contains(dineInOrderListRespDTO.getUpperState())
                    || Objects.equals(StateEnum.REFUNDED.getCode(), dineInOrderListRespDTO.getState())
                    || Objects.equals(StateEnum.ANTI_SETTLEMENT.getCode(), dineInOrderListRespDTO.getState())) {
                orderGuidMap.put(orderGuid, orderGuid);
                continue;
            }
            // 单多结账
            List<OrderDO> sameOrderList = sameOrders.stream()
                    .filter(e -> orderGuid.equals(e.getGuid()) || orderGuid.equals(e.getMainOrderGuid()))
                    .filter(e -> paySuccessStates.contains(e.getState()))
                    .collect(Collectors.toList());
            for (OrderDO orderDO : sameOrderList) {
                orderGuidMap.put(orderDO.getGuid(), orderGuid);
            }
            boolean hasRefundOrderFlag = sameOrderList.stream().anyMatch(e -> Objects.nonNull(e.getRefundOrderGuid()));
            dineInOrderListRespDTO.setHasRefundOrderFlag(hasRefundOrderFlag);
            boolean hasAdjustOrderFlag = sameOrderList.stream().anyMatch(e -> Objects.equals(BooleanEnum.TRUE.getCode(), e.getAdjustState()));
            dineInOrderListRespDTO.setAdjustState(hasAdjustOrderFlag ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        }
    }

    private List<TransactionRecordDO> filterTransactionRecordList(List<TransactionRecordDO> transactionRecordList) {
        return transactionRecordList.stream()
                .filter(e -> Objects.equals(PaymentTypeEnum.CASH.getCode(), e.getPaymentType())
                        || (!Objects.equals(PaymentTypeEnum.CASH.getCode(), e.getPaymentType())
                        && e.getAmount().abs().subtract(e.getRefundAmount()).compareTo(BigDecimal.ZERO) > 0))
                .collect(Collectors.toList());
    }

    @Override
    public Page<DineInOrderListRespDTO> orderListNew(DineInOrderListReqDTO dineInOrderListReqDTO) {
        IPage<DineInOrderListRespDTO> page = orderMapper.orderPage(new PageAdapter<>(dineInOrderListReqDTO), dineInOrderListReqDTO);
        // 设置订单支付方式
        setPaymentTypeName(page.getRecords());
        // 设置订单状态
        setState(page.getRecords());
        return new PageAdapter<>(page, page.getRecords());
    }

    @Override
    public boolean printItemDetail(BaseDTO baseDTO, String orderGuid) {
        DineinOrderDetailRespDTO orderDetail = getOrderDetail(orderGuid);
        return dineInPrintService.printItemDetail(baseDTO, orderDetail);
    }


    @Override
    public boolean printCheckOut(BaseDTO baseDTO, String orderGuid) {
        DineinOrderDetailRespDTO orderDetail = getOrderDetail(orderGuid);
        handleAggPaymentType(orderDetail);
        //过滤已退掉的商品
        checkBatchRollbackItem(orderDetail);
        // 查询会员消费记录
        queryOrderMemberFundingDetail(orderDetail);
        // 查询订单挂账信息
        queryOrderDebtInfo(orderDetail);
        return dineInPrintService.printCheckout(baseDTO, orderDetail);
    }

    private void handleAggPaymentType(DineinOrderDetailRespDTO orderDetail) {
        List<ActuallyPayFeeDetailDTO> payFeeDetailDTOList = orderDetail.getActuallyPayFeeDetailDTOS();
        Map<Object, List<ActuallyPayFeeDetailDTO>> paymentType = CollectionUtil.toListMap(payFeeDetailDTOList, "paymentType");
        List<ActuallyPayFeeDetailDTO> aggPayDetailDTOList = paymentType.get(PaymentTypeEnum.AGG.getCode());
        if (!org.springframework.util.CollectionUtils.isEmpty(aggPayDetailDTOList) && aggPayDetailDTOList.size() > 1) {
            ActuallyPayFeeDetailDTO transactionRecordDO = aggPayDetailDTOList.get(0);
            BigDecimal agg = aggPayDetailDTOList.stream()
                    .map(ActuallyPayFeeDetailDTO::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            transactionRecordDO.setAmount(agg);
            payFeeDetailDTOList.removeIf(pay -> pay.getPaymentType().equals(PaymentTypeEnum.AGG.getCode()));
            payFeeDetailDTOList.add(transactionRecordDO);
        }
    }

    /**
     * 查询会员消费记录
     */
    private void queryOrderMemberFundingDetail(DineinOrderDetailRespDTO orderDetail) {
        List<DineinOrderDetailRespDTO> otherOrderDetails = orderDetail.getOtherOrderDetails();
        if (CollectionUtils.isNotEmpty(otherOrderDetails)) {
            otherOrderDetails.forEach(this::queryOrderMemberFundingDetail);
        }
        String memberConsumptionGuid = orderDetail.getMemberConsumptionGuid();
        if (StringUtils.isEmpty(memberConsumptionGuid)) {
            return;
        }
        List<ResponseConfirmMultiPay> confirmMultiPays = memberTerminalClientService.queryFundingDetail(memberConsumptionGuid);
        if (CollectionUtils.isEmpty(confirmMultiPays)) {
            log.error("通过会员消费guid查询到资金变动记录为空,memberConsumptionGuid:{}", memberConsumptionGuid);
            return;
        }
        log.info("查询会员消费资金变动明细记录返回:{}", JacksonUtils.writeValueAsString(confirmMultiPays));
        // 会员卡支付返回信息
        List<OrderMultiMemberPayDTO> orderMultiMemberPayList = OrderTransform.INSTANCE
                .responseConfirmMultiPay2OrderMultiMemberPayDTO(confirmMultiPays);
        orderDetail.setMultiMemberPays(orderMultiMemberPayList);
    }

    /**
     * 查询订单挂账信息
     */
    private void queryOrderDebtInfo(DineinOrderDetailRespDTO orderDetail) {
        List<DineinOrderDetailRespDTO> otherOrderDetails = orderDetail.getOtherOrderDetails();
        if (CollectionUtils.isNotEmpty(otherOrderDetails)) {
            otherOrderDetails.forEach(this::queryOrderDebtInfo);
        }
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailList = orderDetail.getActuallyPayFeeDetailDTOS();
        if (CollectionUtils.isEmpty(actuallyPayFeeDetailList)) {
            return;
        }
        boolean debtPay = actuallyPayFeeDetailList.stream()
                .anyMatch(e -> Objects.equals(PaymentTypeEnum.DEBT_PAY.getCode(), e.getPaymentType()));
        if (!debtPay) {
            return;
        }
        DebtUnitRecordPageRespDTO debtUnitRecord = debtUnitRecordService.debtUnitRecordByOrderGuid(Long.valueOf(orderDetail.getGuid()));
        if (Objects.isNull(debtUnitRecord)) {
            log.error("订单挂账信息不存在, orderGuid:{}", orderDetail.getGuid());
            return;
        }
        orderDetail.setDebtContactName(debtUnitRecord.getUnitContactName());
        orderDetail.setDebtUnitName(debtUnitRecord.getUnitName());
        orderDetail.setDebtContactTel(debtUnitRecord.getUnitContactTel());
    }

    /**
     * 小票打印处理部分退款
     */
    private void checkBatchRollbackItem(DineinOrderDetailRespDTO orderDetail) {
        if (UpperStateEnum.SAME_ORDER_STATE.contains(orderDetail.getUpperState())) {
            // 主单
            checkRollbackItem(orderDetail);
            // 多单结账
            List<DineinOrderDetailRespDTO> otherOrderDetails = Optional.ofNullable(orderDetail.getOtherOrderDetails())
                    .orElse(Lists.newArrayList());
            otherOrderDetails = otherOrderDetails.stream()
                    .filter(e -> e.getState() != StateEnum.INVALID.getCode()
                            && e.getState() != StateEnum.CANCEL.getCode()).collect(Collectors.toList());
            List<DineInItemDTO> allSubDineInItemList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(otherOrderDetails)) {
                // 多单子单
                otherOrderDetails.forEach(this::checkRollbackItem);
                allSubDineInItemList = otherOrderDetails.stream()
                        .filter(e -> CollectionUtils.isNotEmpty(e.getDineInItemDTOS()))
                        .flatMap(e -> e.getDineInItemDTOS().stream()).collect(Collectors.toList());
            }
            // 判断是否所有商品全部退完
            // 若过滤后无商品  则无法打印
            if (CollectionUtils.isEmpty(orderDetail.getDineInItemDTOS()) && CollectionUtils.isEmpty(allSubDineInItemList)) {
                throw new BusinessException(PRINT_REFUND_ALL_ITEM);
            }
        } else {
            // 单个单或者并台主单子单
            checkRollbackItem(orderDetail);
        }
    }


    /**
     * 小票打印处理部分退款 优化
     *
     * @param orderDetail 订单详情
     */
    private void checkRollbackItem(DineinOrderDetailRespDTO orderDetail) {
        try {
            //若订单直接反结账 则不打印
            if (orderDetail.getState() == StateEnum.REFUNDED.getCode()) {
                throw new BusinessException(PRINT_REFUND_ALL_ITEM);
            }
            //过滤退款的商品  若无商品则不进行打印并提示
            if (Objects.nonNull(orderDetail.getRefundOrderGuid())) {
                List<DineInItemDTO> dineInItemDTOS = Lists.newArrayList();

                addDineInItemDTOS(orderDetail, dineInItemDTOS);

                log.info("过滤前原订单商品:{}", JacksonUtils.writeValueAsString(dineInItemDTOS));

                //需要过滤掉的商品
                List<String> filterItemGuidList = new ArrayList<>();

                //需要扣减的金额
                List<BigDecimal> abatementPriceAdd = new ArrayList<>();

                //需要过滤的团购券码
                List<String> invoiceGrouponCodeList = new ArrayList<>();

                //计算原订单与退款后的优惠差异金额
                List<BigDecimal> originalPriceAdd = new ArrayList<>();

                //处理主单部分退款的商品
                forFilterItem(dineInItemDTOS, filterItemGuidList, abatementPriceAdd, invoiceGrouponCodeList, originalPriceAdd);

                //处理子单退款
                getChildAbatementPriceAdd(orderDetail);

                //处理数量全退的商品
                dealFilterItem(orderDetail, filterItemGuidList, dineInItemDTOS);

                log.info("过滤后订单商品:{}", JacksonUtils.writeValueAsString(dineInItemDTOS));

                orderDetail.setDineInItemDTOS(dineInItemDTOS);

                //附加费扣减
                getAppendRefundFee(orderDetail, abatementPriceAdd);

                //处理实付金额
                dealActuallyPayFee(orderDetail);

                //处理需要过滤的团购券码
                dealInvoiceGrouponCode(orderDetail, invoiceGrouponCodeList);

                //处理支付方式金额变动
                dealPayWayAmount(orderDetail);

                if (CollUtil.isNotEmpty(originalPriceAdd)) {
                    BigDecimal originalPrice = originalPriceAdd.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (originalPrice.compareTo(BigDecimal.ZERO) > 0) {
                        orderDetail.setOriginalPrice(originalPrice.negate());
                        log.info("实付优惠累计差额:{}", orderDetail.getOriginalPrice());
                    }
                }
            }

        } catch (Exception e) {
            if (Objects.nonNull(e.getMessage())
                    && e.getMessage().equals(PRINT_REFUND_ALL_ITEM)) {
                throw new BusinessException(PRINT_REFUND_ALL_ITEM);
            } else {
                log.info("打印过滤退款商品时发生异常", e);
            }
        }
    }

    private static void getChildAbatementPriceAdd(DineinOrderDetailRespDTO orderDetail) {
        log.info("子单信息：{}", JSON.toJSONString(orderDetail.getSubOrderDetails()));

        List<BigDecimal> childAbatementPriceAdd = new ArrayList<>();
        //获取子单的商品
        if (CollUtil.isNotEmpty(orderDetail.getSubOrderDetails())) {
            for (DineinOrderDetailRespDTO subOrderDetail : orderDetail.getSubOrderDetails()) {
                //需要过滤掉的商品
                List<String> filterItemGuidList = new ArrayList<>();
                doChildAbatementPriceAdd(subOrderDetail, filterItemGuidList, childAbatementPriceAdd);
            }
        }
    }

    private static void doChildAbatementPriceAdd(DineinOrderDetailRespDTO subOrderDetail,
                                                 List<String> filterItemGuidList,
                                                 List<BigDecimal> childAbatementPriceAdd) {
        List<DineInItemDTO> dineInItemDTOS = subOrderDetail.getDineInItemDTOS();
        if (CollUtil.isNotEmpty(dineInItemDTOS)) {
            //商品过滤
            List<BigDecimal> abdList = forFilterItem(dineInItemDTOS, filterItemGuidList, childAbatementPriceAdd, new ArrayList<>(), new ArrayList<>());
            log.info("过滤后子单商品退款总额:{}", JSON.toJSONString(abdList));
            //累加子单退款金额
            childAbatementPriceAdd.addAll(abdList);

            //处理数量全退的商品
            if (CollUtil.isNotEmpty(filterItemGuidList)) {
                dineInItemDTOS.removeIf(e -> filterItemGuidList.contains(e.getGuid()));

                //过滤商品父级
                dineInItemDTOS.removeIf(e -> Objects.nonNull(e.getParentItemGuid())
                        && filterItemGuidList.contains(e.getParentItemGuid() + ""));
            }
            subOrderDetail.setDineInItemDTOS(dineInItemDTOS);
        }
    }

    private static void addDineInItemDTOS(DineinOrderDetailRespDTO orderDetail, List<DineInItemDTO> dineInItemDTOS) {
        //过滤套餐类别
        dineInItemDTOS.addAll(orderDetail.getDineInItemDTOS()
                .stream()
                .filter(e -> Objects.isNull(e.getParentItemGuid())
                        || e.getParentItemGuid() == 0)
                .collect(Collectors.toList()));
    }

    private void getAppendRefundFee(DineinOrderDetailRespDTO orderDetail, List<BigDecimal> abatementPriceAdd) {
        //获取订单附加费
        List<AppendFeeDO> appendFeeDOS = appendFeeMpService.listByOrderGuid(orderDetail.getGuid());

        if (CollUtil.isNotEmpty(appendFeeDOS)) {
            List<AppendFeeDetailDTO> appendFeeDetailDTOS = orderDetail.getOrderFeeDetailDTO().getAppendFeeDetailDTOS();

            Map<String, AppendFeeDO> appendFeeDOMap = appendFeeDOS.stream()
                    .filter(in -> in.getRefundAmount().compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toMap(AppendFeeDO::getName, Function.identity(), (entity1, entity2) -> entity1));

            List<AppendFeeDetailDTO> appendFeeDetailDTOList = new ArrayList<>();
            for (AppendFeeDetailDTO appendFeeDetailDTO : appendFeeDetailDTOS) {

                if (appendFeeDOMap.containsKey(appendFeeDetailDTO.getName())) {
                    AppendFeeDO appendFeeDO = appendFeeDOMap.get(appendFeeDetailDTO.getName());
                    //获取退取数量
                    BigDecimal checkAmount = appendFeeDetailDTO.getAmount().subtract(appendFeeDO.getRefundAmount());
                    log.info("附加费名称:{},附加费退款金额:{},附加费扣减后金额:{}", appendFeeDetailDTO.getName(), appendFeeDO.getRefundAmount(), checkAmount);
                    if (checkAmount.compareTo(BigDecimal.ZERO) > 0) {
                        appendFeeDetailDTO.setAmount(checkAmount);
                        appendFeeDetailDTOList.add(appendFeeDetailDTO);
                    }
                }
            }
            log.info("过滤附加费名称集合:{}", JacksonUtils.writeValueAsString(appendFeeDetailDTOList));
            orderDetail.getOrderFeeDetailDTO().setAppendFeeDetailDTOS(appendFeeDetailDTOList);

            //累加记录附加费退款金额
            BigDecimal appendRefundFee = appendFeeDOS.stream().map(AppendFeeDO::getRefundAmount)
                    .filter(refundAmount -> refundAmount.compareTo(BigDecimal.ZERO) > 0).reduce(BigDecimal.ZERO, BigDecimal::add);

            log.info("附加费扣减总金额:{}", appendRefundFee);

            abatementPriceAdd.add(appendRefundFee);
        }
    }

    private static void dealInvoiceGrouponCode(DineinOrderDetailRespDTO orderDetail, List<String> invoiceGrouponCodeList) {
        log.info("过滤部分退款商品,团购券码集合:{}", JacksonUtils.writeValueAsString(invoiceGrouponCodeList));
        if (CollUtil.isNotEmpty(invoiceGrouponCodeList)) {
            orderDetail.setInvoiceGrouponCode(invoiceGrouponCodeList);
        }
    }

    private static void dealFilterItem(DineinOrderDetailRespDTO orderDetail,
                                       List<String> filterItemGuidList, List<DineInItemDTO> dineInItemDTOS) {
        if (CollUtil.isNotEmpty(filterItemGuidList)) {
            log.info("过滤部分退款商品,过滤商品数量:{}", filterItemGuidList.size());
            dineInItemDTOS.removeIf(e -> filterItemGuidList.contains(e.getGuid()));

            //过滤商品父级
            dineInItemDTOS.removeIf(e -> Objects.nonNull(e.getParentItemGuid())
                    && filterItemGuidList.contains(e.getParentItemGuid() + ""));

            //若过滤后无商品  则无法打印
            if (!UpperStateEnum.SAME_ORDER_STATE.contains(orderDetail.getUpperState()) && CollUtil.isEmpty(dineInItemDTOS)) {
                throw new BusinessException(PRINT_REFUND_ALL_ITEM);
            }
        }
    }

    private static void dealActuallyPayFee(DineinOrderDetailRespDTO orderDetail) {
        log.info("退款金额:{}", orderDetail.getRefundAmount());
        BigDecimal orderFee = orderDetail.getActuallyPayFee().subtract(orderDetail.getRefundAmount());
        log.info("扣减后打印实际支付金额:{}", orderFee);
        orderDetail.setActuallyPayFee(orderFee);


        orderDetail.setPayAble(orderDetail.getActuallyPayFee());
    }

    private void dealPayWayAmount(DineinOrderDetailRespDTO orderDetail) {
        //获取订单支付方式详情
        List<TransactionRecordDO> transactionRecordDOS = transactionRecordService.listByOrderGuid(orderDetail.getGuid());

        if (CollUtil.isNotEmpty(transactionRecordDOS)) {
            //以支付方式为key进行分组
            Map<Integer, List<TransactionRecordDO>> transactionRecordDOMap = transactionRecordDOS
                    .stream().collect(Collectors.groupingBy(TransactionRecordDO::getPaymentType));
            for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : orderDetail.getActuallyPayFeeDetailDTOS()) {

                if (transactionRecordDOMap.containsKey(actuallyPayFeeDetailDTO.getPaymentType())) {
                    List<TransactionRecordDO> transactionRecordDOList = transactionRecordDOMap.get(actuallyPayFeeDetailDTO.getPaymentType());

                    BigDecimal refundAmount = transactionRecordDOList.stream().map(TransactionRecordDO::getRefundAmount).
                            reduce(BigDecimal.ZERO, BigDecimal::add);

                    //若改支付方式存在退款 则扣减金额
                    if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal amount = actuallyPayFeeDetailDTO.getAmount().subtract(refundAmount);

                        log.info("过滤部分退款商品,支付类型:{},扣减后支付金额:{}", actuallyPayFeeDetailDTO.getPaymentTypeName(), amount);
                        //设置退款后的金额
                        actuallyPayFeeDetailDTO.setAmount(amount);
                    }
                }
            }
        }

        //判断是否存在线下退款（需打印）
        checkOfflineRefund(orderDetail);

        //若支付方式支付金额为0 则不在展示
        orderDetail.getActuallyPayFeeDetailDTOS().removeIf(e -> e.getAmount().compareTo(BigDecimal.ZERO) <= 0);

        log.info("实收金额明细:{}", JacksonUtils.writeValueAsString(orderDetail.getActuallyPayFeeDetailDTOS()));
    }

    private void checkOfflineRefund(DineinOrderDetailRespDTO orderDetail) {
        //获取退款单支付方式
        List<TransactionRecordDO> recordDOList = transactionRecordService.listByOrderGuid(orderDetail.getRefundOrderGuid());

        if (CollUtil.isNotEmpty(recordDOList)) {
            recordDOList = recordDOList.stream().filter(e -> e.getPaymentType() == PaymentTypeEnum.OFFLINE_REFUND.getCode())
                    .collect(Collectors.toList());

            //若存在线下退款
            if (CollUtil.isNotEmpty(recordDOList)) {
                ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();

                //累计线下退款金额
                BigDecimal amount = recordDOList.stream().map(TransactionRecordDO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                if (amount.compareTo(BigDecimal.ZERO) < 0) {
                    amount = amount.abs();
                    log.info("线下退款金额:{}", amount);
                }

                //新增线下退款明细
                actuallyPayFeeDetailDTO.setAmount(amount);
                actuallyPayFeeDetailDTO.setPaymentType(PaymentTypeEnum.OFFLINE_REFUND.getCode());
                actuallyPayFeeDetailDTO.setPaymentTypeName(PaymentTypeEnum.OFFLINE_REFUND.getDesc());

                log.info("线下退款方式记录={}", JacksonUtils.writeValueAsString(actuallyPayFeeDetailDTO));
                orderDetail.getActuallyPayFeeDetailDTOS().add(actuallyPayFeeDetailDTO);
            }
        }
    }

    private static List<BigDecimal> forFilterItem(List<DineInItemDTO> dineInItemDTOS,
                                                  List<String> filterItemGuidList,
                                                  List<BigDecimal> bigDecimals,
                                                  List<String> invoiceGrouponCode,
                                                  List<BigDecimal> originalPriceAdd) {
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            log.info("商品:{},购买数量:{},退款数量:{}", dineInItemDTO.getItemName(), dineInItemDTO.getCurrentCount(), dineInItemDTO.getRefundCount());
            //判断是否存在部分退款
            BigDecimal currentCount = dineInItemDTO.getCurrentCount().subtract(dineInItemDTO.getRefundCount());
            if (currentCount.compareTo(dineInItemDTO.getCurrentCount()) == 0) {
                log.info("商品:{},无需过滤", dineInItemDTO.getItemName());
                continue;
            }

            if (currentCount.compareTo(BigDecimal.ZERO) == 0) {
                //全退
                allRollback(filterItemGuidList, bigDecimals, dineInItemDTO, invoiceGrouponCode, originalPriceAdd);
            } else {
                //剩余数量
                BigDecimal remainingCount = dineInItemDTO.getCurrentCount().subtract(dineInItemDTO.getRefundCount());
                log.info("部分剩余数量:{}", remainingCount);

                //部分退
                partRollback(bigDecimals, dineInItemDTO, remainingCount, originalPriceAdd);
            }
        }
        return bigDecimals;
    }

    private static void allRollback(List<String> filterItemGuidList,
                                    List<BigDecimal> bigDecimals,
                                    DineInItemDTO dineInItemDTO,
                                    List<String> invoiceGrouponCode,
                                    List<BigDecimal> originalPriceAdd) {

        //累计直接过滤的商品
        filterItemGuidList.add(dineInItemDTO.getGuid());

        //若有优惠总价则退优惠总价 若无则取原价
        BigDecimal totalPrice;
        if (Objects.nonNull(dineInItemDTO.getDiscountTotalPrice())
                && dineInItemDTO.getDiscountTotalPrice().compareTo(BigDecimal.ZERO) > 0) {
            totalPrice = dineInItemDTO.getDiscountTotalPrice();
        } else {
            totalPrice = dineInItemDTO.getCurrentCount().multiply(dineInItemDTO.getPrice());
        }
        //商品支付总价
        bigDecimals.add(totalPrice);

        //记录退款券码 需要过滤
        if (StringUtils.isNotEmpty(dineInItemDTO.getCouponCode())) {
            invoiceGrouponCode.add(dineInItemDTO.getCouponCode());
        }

        //计算原价与实付差额
        calculateDifferAmount(dineInItemDTO, originalPriceAdd, totalPrice);
    }

    private static void calculateDifferAmount(DineInItemDTO dineInItemDTO, List<BigDecimal> originalPriceAdd, BigDecimal totalPrice) {
        //优惠前单价
        BigDecimal beforeItemPrice = dineInItemDTO.getBeforeItemPrice().divide(dineInItemDTO.getCurrentCount(), 2, RoundingMode.DOWN);

        //实际支付单价
        BigDecimal laterItemPrice = totalPrice.divide(dineInItemDTO.getCurrentCount(), 2, RoundingMode.DOWN);

        log.info("商品:{},优惠前单价:{},优惠后单价:{}", dineInItemDTO.getItemName(), beforeItemPrice, laterItemPrice);

        //优惠差价
        BigDecimal differenceAmount = beforeItemPrice.subtract(laterItemPrice);

        log.info("商品:{},优惠差价:{}", dineInItemDTO.getItemName(), differenceAmount);

        //若存在优惠差额
        if (differenceAmount.compareTo(BigDecimal.ZERO) > 0) {
            //累加优惠差价
            originalPriceAdd.add(differenceAmount.multiply(dineInItemDTO.getCurrentCount()));
        }
    }


    private static void partRollback(List<BigDecimal> bigDecimals,
                                     DineInItemDTO dineInItemDTO,
                                     BigDecimal remainingCount,
                                     List<BigDecimal> originalPriceAdd) {
        //实退单价
        BigDecimal singlePrice;
        if (Objects.nonNull(dineInItemDTO.getDiscountTotalPrice())
                && dineInItemDTO.getDiscountTotalPrice().compareTo(BigDecimal.ZERO) > 0) {
            //优惠单价
            singlePrice = dineInItemDTO.getDiscountTotalPrice().divide(dineInItemDTO.getCurrentCount(), 2, RoundingMode.DOWN);
        } else {
            //原单价
            singlePrice = dineInItemDTO.getPrice();
        }
        log.info("商品:{},实付单价:{}", dineInItemDTO.getItemName(), singlePrice);

        //实退金额 = 单价 * 退款数量
        BigDecimal detainAmount = singlePrice.multiply(dineInItemDTO.getRefundCount());
        log.info("商品:{},实退金额:{}", dineInItemDTO.getItemName(), detainAmount);

        //累计实退金额
        bigDecimals.add(detainAmount);

        //商品单价(优惠前)  打印总额用的beforeItemPrice
        BigDecimal price = dineInItemDTO.getBeforeItemPrice().divide(dineInItemDTO.getCurrentCount(), 2, RoundingMode.DOWN);

        log.info("商品:{},单价:{}", dineInItemDTO.getItemName(), price);
        BigDecimal amount = price.multiply(remainingCount);
        log.info("商品:{},剩余总价:{}", dineInItemDTO.getItemName(), amount);

        //打印金额为剩余数量总额
        dineInItemDTO.setBeforeItemPrice(amount);

        //打印数量为剩余数量
        dineInItemDTO.setCurrentCount(remainingCount);

        //计算原价与实付差额
        calculateDifferAmount(dineInItemDTO, originalPriceAdd, price, singlePrice);
    }

    private static void calculateDifferAmount(DineInItemDTO dineInItemDTO, List<BigDecimal> originalPriceAdd, BigDecimal price, BigDecimal singlePrice) {
        BigDecimal differenceAmount = price.subtract(singlePrice);

        log.info("商品:{},金额优惠单个差价:{}", dineInItemDTO.getItemName(), differenceAmount);

        if (differenceAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal totalDifferenceAmount = differenceAmount.multiply(dineInItemDTO.getRefundCount());

            log.info("商品:{},金额优惠差价总额:{}", dineInItemDTO.getItemName(), totalDifferenceAmount);

            originalPriceAdd.add(totalDifferenceAmount);
        }
    }


    /**
     * 构建商品明细上的验券信息
     */
    private void buildItemCouponInfo(String guid, DineinOrderDetailRespDTO orderDetail) {
        List<DineInItemDTO> dineInItemDTOList = orderDetail.getDineInItemDTOS();
        // 默认没在结账页面使用团购验券
        orderDetail.setUseWholeGrouponFlag(false);
        if (CollectionUtils.isEmpty(dineInItemDTOList)) {
            return;
        }
        // 查询此订单使用团购验券
        List<GrouponDO> grouponList = grouponMpService.listByOrderGuid(guid);
        if (CollectionUtils.isEmpty(grouponList)) {
            return;
        }
        Set<String> allCouponCodeList = grouponList.stream().map(GrouponDO::getCode).collect(Collectors.toSet());
        List<DineInItemDTO> couponItemList = dineInItemDTOList.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getCouponCode()))
                .collect(Collectors.toList());
        Set<String> couponCodeList = couponItemList.stream().map(DineInItemDTO::getCouponCode).collect(Collectors.toSet());
        if (allCouponCodeList.size() > couponCodeList.size()) {
            orderDetail.setUseWholeGrouponFlag(true);
        }
        if (CollectionUtils.isNotEmpty(couponItemList)) {
            List<GrouponDO> grouponListByCodes = grouponList.stream().filter(e -> couponCodeList.contains(e.getCode())).collect(Collectors.toList());
            Map<String, GrouponDO> grouponMap = grouponListByCodes.stream().collect(Collectors.toMap(GrouponDO::getCode, Function.identity(), (key1, key2) -> key1));
            for (DineInItemDTO dineInItemDTO : couponItemList) {
                GrouponDO grouponDO = grouponMap.get(dineInItemDTO.getCouponCode());
                if (Objects.isNull(grouponDO)) {
                    continue;
                }
                dineInItemDTO.setUseCouponTime(grouponDO.getGmtCreate());
                MtCouponPreRespDTO couponPreRespDTO = new MtCouponPreRespDTO();
                couponPreRespDTO.setCouponType(grouponDO.getGrouponType());
                couponPreRespDTO.setCouponCode(grouponDO.getCode());
                couponPreRespDTO.setGroupBuyType(grouponDO.getGrouponType());
                couponPreRespDTO.setDeductionAmount(grouponDO.getDeductionAmount());
                couponPreRespDTO.setCouponBuyPrice(Double.parseDouble(grouponDO.getCouponBuyPrice().toPlainString()));
                // 前端需要有值 (并不是真实值)
                couponPreRespDTO.setCouponEndTime(Strings.EMPTY);
                couponPreRespDTO.setDealTitle(grouponDO.getName());
                dineInItemDTO.setCouponInfo(couponPreRespDTO);
            }
        }
        // 判断是否含有子单
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetail.getSubOrderDetails();
        if (CollectionUtils.isEmpty(subOrderDetails)) {
            return;
        }
        for (DineinOrderDetailRespDTO subOrderDetail : subOrderDetails) {
            buildItemCouponInfo(subOrderDetail.getGuid(), subOrderDetail);
        }
    }

    /**
     * 构建退款信息
     */
    private void buildRefundOrderInfo(OrderDO orderDO, DineinOrderDetailRespDTO orderDetailRespDTO) {
        if (Objects.isNull(orderDO.getRefundOrderGuid())) {
            return;
        }
        Long refundOrderGuid = orderDO.getRefundOrderGuid();
        // 查询退款订单
        OrderDO refundOrder = orderService.getById(refundOrderGuid);
        if (Objects.isNull(refundOrder)) {
            log.error("退款订单不存在,原订单:{},  refundOrderGuid:{}", orderDO.getGuid(), refundOrderGuid);
            return;
        }
        // 查询退款订单交易记录
        List<TransactionRecordDO> transactionRecordList = transactionRecordService.listByOrderGuid(String.valueOf(refundOrderGuid));
        Set<Integer> paymentTypeSet = transactionRecordList.stream().map(TransactionRecordDO::getPaymentType).collect(Collectors.toSet());
        StringBuilder sb = new StringBuilder();
        for (Integer paymentType : paymentTypeSet) {
            sb.append(PaymentTypeEnum.getPaymentTypeNameByCode(paymentType)).append("、");
        }
        RefundOrderDetailDTO refundOrderDetailDTO = new RefundOrderDetailDTO();
        refundOrderDetailDTO.setOrderGuid(String.valueOf(refundOrderGuid));
        refundOrderDetailDTO.setRefundTime(refundOrder.getCheckoutTime());
        refundOrderDetailDTO.setRefundAmount(refundOrder.getOrderFee());
        refundOrderDetailDTO.setRefundTypes("");
        if (sb.length() > 0) {
            refundOrderDetailDTO.setRefundTypes(sb.substring(0, sb.length() - 1));
        }
        // 判断该订单是否全部退款
        List<DineInItemDTO> dineInItemList = orderDetailRespDTO.getDineInItemDTOS();
        long availableRefundCount = dineInItemList.stream()
                .filter(e -> e.getCurrentCount().add(e.getFreeCount()).subtract(e.getRefundCount()).compareTo(BigDecimal.ZERO) > 0).count();
        // 菜品是否全部退完
        boolean residueItemFlag = availableRefundCount <= 0;
        // 如果商品全部退完，就查询附加费是否全部退完
        if (availableRefundCount <= 0 && BigDecimalUtil.greaterThanZero(orderDO.getAppendFee())) {
            // 查询附加费
            List<AppendFeeDetailDTO> appendFeeList = appendFeeService.getOrderAllAppendFeeList(String.valueOf(orderDO.getGuid()));
            long availableRefundAppendFeeCount = 0;
            if (CollectionUtils.isEmpty(appendFeeList)) {
                log.warn("附加费明细缺失, orderGuid:{}", orderDO.getGuid());
            } else {
                availableRefundAppendFeeCount = appendFeeList.stream()
                        .filter(e -> e.getAmount().subtract(e.getRefundAmount()).compareTo(BigDecimal.ZERO) > 0)
                        .count();
            }
            residueItemFlag = availableRefundAppendFeeCount <= 0;
        }
        // 金额是否全部退完
        boolean residueAmountFlag = !BigDecimalUtil.greaterThanZero(orderDetailRespDTO.getActuallyPayFee().subtract(refundOrderDetailDTO.getRefundAmount()));
        refundOrderDetailDTO.setResidueFlag(residueAmountFlag);
        orderDetailRespDTO.setRefundOrderDetailDTO(refundOrderDetailDTO);
    }

    private String buildRefundDetailsForAmountItem(List<AmountItemDTO> amountItemList) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < amountItemList.size(); i++) {
            AmountItemDTO itemDTO = amountItemList.get(i);
            sb.append(itemDTO.getName()).append(" ￥").append(itemDTO.getAmount().abs().setScale(2, RoundingMode.DOWN).toPlainString());
            if (i != amountItemList.size() - 1) {
                sb.append("\n");
            }
        }
        return sb.toString();
    }

    private String buildRefundDetailsForDetail(OrderDO originalOrder, List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailList) {
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isEmpty(actuallyPayFeeDetailList)) {
            return sb.toString();
        }
        for (int i = 0; i < actuallyPayFeeDetailList.size(); i++) {
            ActuallyPayFeeDetailDTO detail = actuallyPayFeeDetailList.get(i);
            BigDecimal amount = detail.getAmount().negate();
            if (StringUtils.isEmpty(originalOrder.getReserveGuid())) {
                amount = amount.abs();
            }
            sb.append(detail.getPaymentTypeName())
                    .append(" ￥")
                    .append(amount.setScale(2, RoundingMode.DOWN).toPlainString());
            if (i != actuallyPayFeeDetailList.size() - 1) {
                sb.append("\n");
            }
        }
        return sb.toString();
    }


    /**
     * 转换订单桌台名称
     * 联台单 -> 联台-1 (10)
     */
    private void transferOrderDiningTableName(List<DineInOrderListRespDTO> dineInOrderListRespList) {
        if (CollectionUtils.isEmpty(dineInOrderListRespList)) {
            return;
        }
        List<String> guids = dineInOrderListRespList.stream().map(DineInOrderListRespDTO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<OrderExtendsDO> orderExtendsList = orderExtendsService.listByIds(guids);
        Map<Long, OrderExtendsDO> orderExtendsMap = orderExtendsList.stream()
                .collect(Collectors.toMap(OrderExtendsDO::getGuid, Function.identity(), (key1, key2) -> key1));
        for (DineInOrderListRespDTO respDTO : dineInOrderListRespList) {
            OrderExtendsDO orderExtendsDO = orderExtendsMap.get(Long.valueOf(respDTO.getGuid()));
            if (Objects.isNull(orderExtendsDO) || !Boolean.TRUE.equals(orderExtendsDO.getAssociatedFlag())) {
                continue;
            }
            String associatedTableNames = orderExtendsDO.getAssociatedTableNames();
            List<String> associatedTableNameList = JacksonUtils.toObjectList(String.class, associatedTableNames);
            String associatedTableName = "联台-%s (%s)";
            respDTO.setDiningTableName(String.format(associatedTableName, orderExtendsDO.getAssociatedSn(), associatedTableNameList.size()));
        }
    }


    @Override
    public void printLabel(PrintLabelReq printLabelReq) {
        //根据guid获取订单信息
        String orderGuid = printLabelReq.getOrderGuid();
        DineinOrderDetailRespDTO orderDetails = getOrderDetails(orderGuid);
        log.info("获取订单详情:{}", JacksonUtils.writeValueAsString(orderDetails));
        if (Objects.isNull(orderDetails)) {
            throw new BusinessException("订单不存在，请刷新后重试");
        }
        OrderDO orderDO = new OrderDO();
        BeanUtils.copyProperties(orderDetails, orderDO);
        // 构建需要打印标签单的商品
        List<DineInItemDTO> dineInItemList = Lists.newArrayList();
        appendPrintLabelDineInItemDTOS(printLabelReq, orderDetails, dineInItemList);
        if (CollectionUtils.isEmpty(dineInItemList)) {
            throw new BusinessException("可打印标签单的商品为空");
        }
        log.info("标签单打印商品明细:{}", JacksonUtils.writeValueAsString(dineInItemList));
        //标签打印
        dineInPrintService.printLabelItem(printLabelReq, orderDO, dineInItemList);
    }

    /**
     * 构建需要打印标签单的商品
     */
    private void appendPrintLabelDineInItemDTOS(PrintLabelReq printLabelReq,
                                                DineinOrderDetailRespDTO orderDetails,
                                                List<DineInItemDTO> dineInItemList) {
        List<PrintLabelReq.InnerPrintLabelDetailsReq> printItemList = printLabelReq.getPrintItemList();
        Map<String, BigDecimal> printItemMap = printItemList.stream().collect(Collectors.toMap(PrintLabelReq.InnerPrintLabelDetailsReq::getOrderItemGuid,
                PrintLabelReq.InnerPrintLabelDetailsReq::getCount, (key1, key2) -> key1));
        List<DineInItemDTO> dineInItemDTOS = orderDetails.getDineInItemDTOS();
        List<DineInItemDTO> printDineInItemDTOS = dineInItemDTOS.stream()
                .filter(e -> printItemMap.containsKey(e.getGuid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(printDineInItemDTOS)) {
            // 设置打印数量
            setPrintLabelDineInItemCount(printDineInItemDTOS, printItemMap);
            dineInItemList.addAll(printDineInItemDTOS);
        }
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetails.getSubOrderDetails();
        if (CollectionUtils.isNotEmpty(subOrderDetails)) {
            // 并单
            subOrderDetails.forEach(e -> appendPrintLabelDineInItemDTOS(printLabelReq, e, dineInItemList));
        }
        List<DineinOrderDetailRespDTO> otherOrderDetails = orderDetails.getOtherOrderDetails();
        if (CollectionUtils.isNotEmpty(otherOrderDetails)) {
            // 多单结账
            otherOrderDetails.forEach(e -> appendPrintLabelDineInItemDTOS(printLabelReq, e, dineInItemList));
        }
    }

    /**
     * 设置打印数量
     */
    private void setPrintLabelDineInItemCount(List<DineInItemDTO> printDineInItemDTOS, Map<String, BigDecimal> printItemMap) {
        for (DineInItemDTO itemDTO : printDineInItemDTOS) {
            itemDTO.setCurrentCount(printItemMap.getOrDefault(itemDTO.getGuid(), itemDTO.getCurrentCount()));
            // 使用currentCount，将freeCount设置为0
            itemDTO.setFreeCount(BigDecimal.ZERO);
        }
    }
}

