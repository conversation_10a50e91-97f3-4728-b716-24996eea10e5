package com.holderzone.saas.store.trade.bo;

import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.trade.entity.domain.*;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;


/**
 * 订单加菜 - 套餐
 */
@Data
public class OrderPackageSubgroupBO {

    /**
     * 当前传入的商品信息
     */
    private DineInItemDTO dineInItemDTO;

    /**
     * 当前传入的套餐商品信息
     */
    private PackageSubgroupDTO packageSubgroupDTO;

    /**
     * 当前传入的套餐子商品信息
     */
    private SubDineInItemDTO subDineInItemDTO;

    /**
     * 当前传入的套餐子商品信息
     */
    private List<SubDineInItemDTO> subDineInItemList;

    /**
     * 最新换菜之后的
     */
    private List<SubDineInItemDTO> changeSubDineInItemList;

    /**
     * 商品信息
     */
    private Map<String, ItemInfoRespDTO> itemInfoRespDTOMap;

    /**
     * 属性商品
     */
    private List<ItemAttrDO> itemAttrDOS;

    /**
     * 商品明细操作记录
     */
    private List<OrderItemRecordDO> itemRecordSave;

    /**
     * 商品明细
     */
    private List<OrderItemDO> orderItemDOS;

    /**
     * 待删除的商品明细
     */
    private List<OrderItemDO> removeOrderItemDOS;

    /**
     * 恢复的商品明细
     */
    private List<OrderItemDO> restoreOrderItemDOS;

    /**
     * 商品扩展明细
     */
    private List<OrderItemExtendsDO> orderItemExtendsDOS;

    /**
     * 商品套餐换菜
     */
    private List<OrderItemChangesDO> orderItemChangesList;

    /**
     * 订单
     */
    private OrderDO orderDO;

    /**
     * 套餐商品
     */
    private OrderItemDO pkgOrderItemDO;

    /**
     * 订单商品扩展
     */
    private OrderItemExtendsDO extendsDO;

    /**
     * 是否加菜
     */
    private Boolean addItemFlag;

    /**
     * 是否新增订单商品明细 (菜品还原)
     */
    private Boolean addOrderItemFlag;

    /**
     * 营业日
     */
    private LocalDate businessDay;

    /**
     * 已添加的换菜批次号 (计算原商品的属性价和加价)
     */
    private List<String> addChangeBatchNumber;

}
