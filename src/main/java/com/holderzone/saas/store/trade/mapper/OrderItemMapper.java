package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.journaling.req.SaleCountReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SaleCountRespDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单商品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface OrderItemMapper extends BaseMapper<OrderItemDO> {

    void updateBatchIsAdjustItemIsTrue(@Param("guids") List<Long> guids);

    List<OrderItemDO> selectListByPadOrderGuid(@Param("padOrderGuid") String padOrderGuid);

    void clearGroupBuyFlag(@Param("orderGuid") Long orderGuid, @Param("couponCode") String couponCode);

    void batchClearGroupBuyFlag(@Param("list") List<String> orderItemGuids);

    List<SaleCountRespDTO> screenSaleType(@Param("query")SaleCountReqDTO saleCountReqDTO);

    List<SaleCountRespDTO> screenSaleItem(@Param("query")SaleCountReqDTO saleCountReqDTO);

    OrderItemDO getByGuidContainsDel(@Param("orderItemGuid") Long orderItemGuid);

    List<OrderItemDO> listByMainItemGuidContainsDel(@Param("orderItemGuid") Long orderItemGuid);

    void restoreOrderItem(@Param("restoreOrderItemList") List<OrderItemDO> restoreOrderItemList);
}
