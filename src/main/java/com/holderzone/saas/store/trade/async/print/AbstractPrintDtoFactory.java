package com.holderzone.saas.store.trade.async.print;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.StoreBizDTO;
import com.holderzone.saas.store.dto.print.content.*;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.trade.async.print.util.FacadePrintUtils;
import com.holderzone.saas.store.trade.execption.TradeExceptionEnum;
import com.holderzone.saas.store.trade.utils.AssertExceptionUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 打印工厂：
 * 将数据装入{@code com.holderzone.saas.store.trade.async.print.TradeDataContainer} contailer中
 * 根据 {@code com.holderzone.saas.store.enums.print.InvoiceTypeEnum} 来获取printDto对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/10 11:10
 */
@Slf4j
public abstract class AbstractPrintDtoFactory<T extends PrintDTO> {

    /**
     * 工厂Map  包含所有子工厂（本工厂的抽象类）
     */
    private static final Map<InvoiceTypeEnum, AbstractPrintDtoFactory> FACTORY_MAP = new ConcurrentHashMap<>();

    /**
     * 工厂工具类  可代表机器
     */
    final FacadePrintUtils facadePrintUtils = new FacadePrintUtils();

    /**
     * 生产对象的初始化构造方法
     */
    private Supplier<T> instance;

    AbstractPrintDtoFactory(Supplier<T> supplier) {
        this.instance = supplier;
    }

    /*
     * 初始化所有具体工厂
     */
    static {
        FACTORY_MAP.put(InvoiceTypeEnum.CHECKOUT, new AbstractPrintDtoFactory<PrintCheckOutDTO>(PrintCheckOutDTO::new) {
            @Override
            public void setPrintBody(PrintCheckOutDTO printDTO, TradeDataContainer tradeDataContainer) {
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO(), tradeDataContainer.getStoreInfoFunction(), true);
            }
        });
        FACTORY_MAP.put(InvoiceTypeEnum.ITEM_LIST, new AbstractPrintDtoFactory<PrintItemDetailDTO>(PrintItemDetailDTO::new) {
            @Override
            public void setPrintBody(PrintItemDetailDTO printDTO, TradeDataContainer tradeDataContainer) {
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO(), null, false);
            }
        });
        FACTORY_MAP.put(InvoiceTypeEnum.CHANGE_ITEM, new AbstractPrintDtoFactory<PrintChangeItemDTO>(PrintChangeItemDTO::new) {
            @Override
            public void setPrintBody(PrintChangeItemDTO printDTO, TradeDataContainer tradeDataContainer) {
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO());
            }
        });
        FACTORY_MAP.put(InvoiceTypeEnum.TRANSFER_ITEM, new AbstractPrintDtoFactory<PrintTransferItemDTO>(PrintTransferItemDTO::new) {
            @Override
            public void setPrintBody(PrintTransferItemDTO printDTO, TradeDataContainer tradeDataContainer) {
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO(), tradeDataContainer.getOldDineinOrderDetailRespDTO());
            }
        });
        FACTORY_MAP.put(InvoiceTypeEnum.ITEM_REPEAT_ORDER, new AbstractPrintDtoFactory<PrintItemDetailDTO>(PrintItemDetailDTO::new) {
            @Override
            public void setPrintBody(PrintItemDetailDTO printDTO, TradeDataContainer tradeDataContainer) {
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO(), null, false);
            }
        });
        FACTORY_MAP.put(InvoiceTypeEnum.LABEL, new AbstractPrintDtoFactory<PrintLabelDTO>(PrintLabelDTO::new) {
            @Override
            public void setPrintBody(PrintLabelDTO printDTO, TradeDataContainer tradeDataContainer) {
                Function<String, StoreBizDTO> storeInfoFunction = AssertExceptionUtils.assertNotNullAndReturn(tradeDataContainer.getStoreInfoFunction(), "打印container storeInfoFunction为空", TradeExceptionEnum.NO_NEED_PARMS);
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO(), storeInfoFunction);
            }
        });
        FACTORY_MAP.put(InvoiceTypeEnum.ITEM_LABEL, new AbstractPrintDtoFactory<PrintItemLabelDTO>(PrintItemLabelDTO::new) {
            @Override
            public void setPrintBody(PrintItemLabelDTO printDTO, TradeDataContainer tradeDataContainer) {
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO());
            }
        });
        FACTORY_MAP.put(InvoiceTypeEnum.ORDER_ITEM, new AbstractPrintDtoFactory<PrintOrderItemDTO>(PrintOrderItemDTO::new) {
                    @Override
                    public void setPrintBody(PrintOrderItemDTO printDTO, TradeDataContainer tradeDataContainer) {
                        facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO(), tradeDataContainer.getItemInvoiceType(), false);
                    }
                }
        );
        FACTORY_MAP.put(InvoiceTypeEnum.PRE_CHECKOUT, new AbstractPrintDtoFactory<PrintPreCheckoutDTO>(PrintPreCheckoutDTO::new) {
            @Override
            public void setPrintBody(PrintPreCheckoutDTO printDTO, TradeDataContainer tradeDataContainer) {
                Function<String, StoreBizDTO> storeInfoFunction = AssertExceptionUtils.assertNotNullAndReturn(tradeDataContainer.getStoreInfoFunction(), "打印container storeInfoFunction为空", TradeExceptionEnum.NO_NEED_PARMS);
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO(), storeInfoFunction, true);
            }
        });
        FACTORY_MAP.put(InvoiceTypeEnum.REFUND_ITEM, new AbstractPrintDtoFactory<PrintRefundItemDTO>(PrintRefundItemDTO::new) {
            @Override
            public void setPrintBody(PrintRefundItemDTO printDTO, TradeDataContainer tradeDataContainer) {
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO(), null, true);
            }
        });
        FACTORY_MAP.put(InvoiceTypeEnum.CHECKOUT_TABLES, new AbstractPrintDtoFactory<PrintCoTableCbDTO>(PrintCoTableCbDTO::new) {
            @Override
            public void setPrintBody(PrintCoTableCbDTO printDTO, TradeDataContainer tradeDataContainer) {
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO(), tradeDataContainer.getStoreInfoFunction());
            }
        });
        FACTORY_MAP.put(InvoiceTypeEnum.PRE_CHECKOUT_TABLES, new AbstractPrintDtoFactory<PrintPreCoTableCbDTO>(PrintPreCoTableCbDTO::new) {
            @Override
            public void setPrintBody(PrintPreCoTableCbDTO printDTO, TradeDataContainer tradeDataContainer) {
                Function<String, StoreBizDTO> storeInfoFunction = AssertExceptionUtils.assertNotNullAndReturn(tradeDataContainer.getStoreInfoFunction(), "打印container storeInfoFunction为空", TradeExceptionEnum.NO_NEED_PARMS);
                facadePrintUtils.setPrintBody(printDTO, tradeDataContainer.getDineinOrderDetailRespDTO(), storeInfoFunction);
            }
        });
    }

    /**
     * 提取公共的
     *
     * @param tradeDataContainer 乱七八糟的数据容器
     * @return 返回工厂创建的对象
     */
    private PrintDTO getPrintDTO(TradeDataContainer tradeDataContainer, InvoiceTypeEnum invoiceTypeEnum) {
        log.info("打印单类型="+invoiceTypeEnum);
        BaseDTO baseDTO = AssertExceptionUtils.assertNotNullAndReturn(tradeDataContainer.getBaseDTO(), "打印container baseDto为空", TradeExceptionEnum.NO_NEED_PARMS);
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = AssertExceptionUtils.assertNotNullAndReturn(tradeDataContainer.getDineinOrderDetailRespDTO(), "打印container dineinOrderDetailRespDTO为空", TradeExceptionEnum.NO_NEED_PARMS);
        Function<String, String> areaGuidFromTableGuidFunction = AssertExceptionUtils.assertNotNullAndReturn(tradeDataContainer.getAreaGuidFromTableGuidFunction(), "打印container areaGuidFromTableGuidFunction为空", TradeExceptionEnum.NO_NEED_PARMS);
        T printDTO = instance.get();
        facadePrintUtils.setPrintBaseInfo(printDTO, baseDTO, invoiceTypeEnum, dineinOrderDetailRespDTO, areaGuidFromTableGuidFunction);
        log.info("组装打印基础信息printDTO：{}", JacksonUtils.writeValueAsString(printDTO));
        setPrintBody(printDTO, tradeDataContainer);
        return printDTO;
    }

    public abstract void setPrintBody(T printDTO, TradeDataContainer tradeDataContainer);

    /**
     * 工厂模式  后期还要优化
     */
    public static PrintDTO create(InvoiceTypeEnum invoiceTypeEnum, TradeDataContainer dataContainer) {
        return Optional.ofNullable(FACTORY_MAP.get(invoiceTypeEnum))
                .map(factory -> factory.getPrintDTO(dataContainer, invoiceTypeEnum))
                .orElse(null);
    }

}