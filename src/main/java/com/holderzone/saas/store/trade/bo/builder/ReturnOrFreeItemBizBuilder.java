package com.holderzone.saas.store.trade.bo.builder;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.saas.store.trade.bo.ReturnOrFreeItemBO;
import lombok.Data;


/**
 * 退/赠菜 biz builder
 */
public class ReturnOrFreeItemBizBuilder {

    private ReturnOrFreeItemBizBuilder() {

    }

    public static ReturnOrFreeItemBO build() {
        ReturnOrFreeItemBO biz = new ReturnOrFreeItemBO();
        biz.setSaveOrderItemDOS(Lists.newArrayList());
        biz.setSaveItemAttrDOS(Lists.newArrayList());
        biz.setSaveOrderItemExtendDOS(Lists.newArrayList());
        biz.setSaveOrderItemChangesDOS(Lists.newArrayList());
        biz.setUpdateList(Lists.newArrayList());
        biz.setSaveList(Lists.newArrayList());
        biz.setItemUpdateList(Lists.newArrayList());
        biz.setOrderItemDOS(Lists.newArrayList());
        biz.setFreeReturnItemDOMap(Maps.newHashMap());
        biz.setOutDinnerMap(Maps.newHashMap());
        return biz;
    }

}
