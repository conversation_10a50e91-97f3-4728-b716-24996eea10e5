package com.holderzone.saas.store.trade.controller;


import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.exception.InterfaceDeprecatedException;
import com.holderzone.saas.store.dto.member.activity.ThirdActivityTypeEnum;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.trade.anno.RequireOrderCheckLock;
import com.holderzone.saas.store.trade.service.GrouponService;
import com.holderzone.saas.store.trade.service.IThirdActivityRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 第三方活动使用记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api(value = "第三方活动使用记录")
@RequestMapping("/third_activity_record")
public class ThirdActivityRecordController {

    private final IThirdActivityRecordService thirdActivityRecordService;

    private final GrouponService grouponService;

    /**
     * 查询订单下使用的活动
     */
    @ApiOperation(value = "查询订单下使用的活动")
    @GetMapping("/list_third_activity_by_order_guid")
    public List<ThirdActivityRecordDTO> listThirdActivityByOrderGuid(@RequestParam("orderGuid") String orderGuid) {
        log.info("查询订单下使用的活动 入参：orderGuid={}", orderGuid);
        return thirdActivityRecordService.listThirdActivityByOrderGuid(orderGuid);
    }


    @ApiOperation(value = "查询该活动绑定的券码列表", notes = "查询该活动绑定的券码列表")
    @GetMapping("/couponCodes/query_activity")
    public List<GrouponListRespDTO> grouponCodesByActivityGuid(String orderGuid, String activityGuid) {
        log.info("查询该活动绑定的券码列表,入参,orderGuid:{},activityGuid:{} ", orderGuid, activityGuid);
        return thirdActivityRecordService.grouponCodesByActivityGuid(orderGuid, activityGuid);
    }

    @ApiOperation(value = "保存单个第三方活动使用信息")
    @PostMapping("/save_third_activity_info")
    public void saveThirdActivityInfo(@RequestBody RecordThirdActivityInfoReqDTO reqDTO) {
        log.info("保存单个第三方活动使用信息 入参：reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        thirdActivityRecordService.saveThirdActivityInfo(reqDTO);
    }

    @ApiOperation(value = "撤销第三方活动记录")
    @GetMapping("/revoke_third_activity_record")
    public void revokeThirdActivityRecord(@RequestParam("orderGuid") String orderGuid,
                                          @RequestParam("grouponType") Integer grouponType) {
        log.info("撤销第三方活动记录 入参：orderGuid={},grouponType={}", orderGuid, grouponType);
        thirdActivityRecordService.revokeThirdActivityRecordByThirdType(orderGuid,
                ThirdActivityTypeEnum.transferThirdType(grouponType));
    }

    /**
     * 批量记录第三方活动使用信息
     */
    @ApiOperation(value = "批量记录第三方活动使用信息")
    @PostMapping("/batch_save_third_activity_info")
    public Boolean batchSaveThirdActivityInfo(@RequestBody RecordThirdActivityInfoReqDTO reqDTO) {
        log.info("批量记录第三方活动使用信息 入参：reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        throw new InterfaceDeprecatedException();
    }

    /**
     * 批量撤销第三方活动记录
     */
    @ApiOperation(value = "批量撤销第三方活动记录")
    @PostMapping("/batch_revoke_third_activity_record")
    public Boolean batchRevokeThirdActivityRecord(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("批量撤销第三方活动记录 入参：singleDataDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
        grouponService.revokeBatchOrder(singleDataDTO.getDatas());
        return true;
    }

    /**
     * 批量查询订单使用的第三方活动记录
     */
    @ApiOperation(value = "批量查询订单使用的第三方活动记录")
    @PostMapping("/has_third_activity_record_order_guids")
    public List<String> hasThirdActivityOrderGuids(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("批量查询订单使用的第三方活动记录入参：singleDataDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
        return grouponService.hasThirdActivityOrderGuids(singleDataDTO.getDatas());
    }


    /**
     * 根据券码撤销活动
     * 撤销第三方活动-美团团购验券
     */
    @ApiOperation(value = "根据券码撤销活动")
    @PostMapping("/revoke_by_code")
    public List<String> revokeByCode(@RequestBody GrouponReqDTO grouponReqDTO) {
        log.info("根据券码撤销活动,入参：grouponReqDTO={}", JacksonUtils.writeValueAsString(grouponReqDTO));
        throw new InterfaceDeprecatedException();
    }

    /**
     * 美团团购验券
     */
    @ApiOperation(value = "美团团购验券", notes = "美团团购验券")
    @PostMapping("/verify_code")
    @RequireOrderCheckLock
    public List<String> verifyCode(@RequestBody GrouponReqDTO grouponReqDTO) {
        log.info("美团团购验券,入参={}", JacksonUtils.writeValueAsString(grouponReqDTO));
        throw new InterfaceDeprecatedException();
    }
}
