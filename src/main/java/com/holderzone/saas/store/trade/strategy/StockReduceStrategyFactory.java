package com.holderzone.saas.store.trade.strategy;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存扣减策略工厂
 */
@Component
public class StockReduceStrategyFactory {
    
    private final Map<String, StockReduceStrategy> strategyMap = new HashMap<>();
    
    @Resource
    private List<StockReduceStrategy> strategies;
    
    @PostConstruct
    public void init() {
        for (StockReduceStrategy strategy : strategies) {
            strategyMap.put(strategy.getStrategyName(), strategy);
        }
    }
    
    /**
     * 获取指定名称的库存扣减策略
     * @param strategyName 策略名称
     * @return 库存扣减策略
     */
    public StockReduceStrategy getStrategy(String strategyName) {
        return strategyMap.get(strategyName);
    }
    
    /**
     * 获取所有可用的库存扣减策略
     * @return 库存扣减策略列表
     */
    public List<StockReduceStrategy> getAllStrategies() {
        return strategies;
    }
} 