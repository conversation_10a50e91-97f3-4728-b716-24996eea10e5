package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.member.terminal.dto.statistics.*;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.constants.OrderConstants;
import com.holderzone.saas.store.trade.mapper.BusinessDailyMapper;
import com.holderzone.saas.store.trade.repository.feign.MemberDataClientService;
import com.holderzone.saas.store.trade.repository.feign.StaffClientService;
import com.holderzone.saas.store.trade.repository.feign.TableClientService;
import com.holderzone.saas.store.trade.repository.interfaces.GrouponMpService;
import com.holderzone.saas.store.trade.service.BusinessDailyService;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessDailyServiceImpl
 * @date 2019/02/12 15:49
 * @description 营业日报业务服务
 * @program holder-saas-store-trade
 */
@Service
@Slf4j
public class BusinessDailyServiceImpl implements BusinessDailyService {

    @Resource
    private BusinessDailyMapper businessDailyMapper;

    @Resource
    private MemberDataClientService memberDataClientService;

    @Resource
    private GrouponMpService grouponMpService;

    @Resource
    private TableClientService tableClientService;

    @Resource
    private StaffClientService staffClientService;

    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final String DATE_TIME_START = " 00:00:00";

    private static final String DATE_TIME_END = " 23:59:59";

    @Override
    public List<ItemRespDTO> freeReturn(DailyReqDTO request, int type) {
        List<ItemRespDTO> list = businessDailyMapper.freeReturn(request, type);
        Map<String, List<ItemRespDTO>> ref = list.stream().collect(Collectors.groupingBy(ItemRespDTO::getGuid, Collectors.toList()));
        List<ItemRespDTO> result = new ArrayList<>(ref.size());
        ref.forEach((e, a) -> {
            if (a == null || a.isEmpty()) {
                return;
            }
            if (a.size() == 1) {
                a.get(0).setUnitPrice(a.get(0).getItemType() == 1 ? null : a.get(0).getUnitPrice());
                result.add(a.get(0));
                return;
            }
            ItemRespDTO template = a.get(0);
            ItemRespDTO itemRespDTO = new ItemRespDTO();
            itemRespDTO.setSubs(a);
            itemRespDTO.setHasSubInfo(1);
            itemRespDTO.setName(template.getName());
            BigDecimal count = new BigDecimal(0);
            BigDecimal amount = new BigDecimal(0);
            for (ItemRespDTO s : a) {
                s.setName("");
                s.setUnitPrice(s.getItemType() == 1 ? null : s.getUnitPrice());
                count = count.add(s.getQuantum());
                amount = amount.add(s.getAmount());
            }
            itemRespDTO.setAmount(amount);
            itemRespDTO.setItemType(template.getItemType());
            itemRespDTO.setSkuName(template.getSkuName());
            itemRespDTO.setQuantum(count);
            result.add(itemRespDTO);
        });
        return result;
    }

    @Override
    public List<ItemRespDTO> goods(DailyReqDTO request) {
        List<ItemRespDTO> list = Lists.newArrayList();
        if (Objects.isNull(request.getItemType())) {
            //22818：区分单品与套餐，兼容旧版本，当前端未传itemType时，则是旧版本apk，调用原方法查询全部
            list = businessDailyMapper.goods(request);
        } else if (request.getItemType() == 0) {
            //查询单品
            list = businessDailyMapper.singleGoods(request);
        } else if (request.getItemType() == 1) {
            //查询套餐
            list = businessDailyMapper.packageGoods(request);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            list = list.stream().filter(a -> a.getQuantum().compareTo(BigDecimal.ZERO) != 0
                    || a.getFreeNum().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            //过滤出修改前后名称不一致商品
            Map<String, List<ItemRespDTO>> ref = list.stream()
                    .collect(Collectors.groupingBy(ItemRespDTO::getGuid, Collectors.toList()));
            List<ItemRespDTO> retList = new ArrayList<>(ref.size());
            //未修改名称的商品循环
            ref.forEach((e, a) -> {
                if (a == null || a.isEmpty()) {
                    return;
                }
                if (a.size() == 1) {
                    a.get(0).setQuantum(a.get(0).getQuantum());
                    retList.add(a.get(0));
                    return;
                }
                ItemRespDTO itemRespDTO = new ItemRespDTO();
                itemRespDTO.setSubs(a);
                itemRespDTO.setHasSubInfo(1);
                itemRespDTO.setName(a.get(a.size() - 1).getName());
                BigDecimal count = new BigDecimal(0);
                BigDecimal amount = new BigDecimal(0);
                BigDecimal discountAmount = BigDecimal.ZERO;
                for (ItemRespDTO s : a) {
                    s.setName("");
                    count = count.add(s.getQuantum());
                    amount = amount.add(s.getAmount());
                    discountAmount = discountAmount.add(s.getDiscountAmount());
                }
                itemRespDTO.setAmount(amount);
                itemRespDTO.setQuantum(count);
                itemRespDTO.setDiscountAmount(discountAmount);
                retList.add(itemRespDTO);
            });
            log.info("goods request: entry {}, and return {}", JacksonUtils.writeValueAsString(request), JacksonUtils.writeValueAsString(retList));
            return retList;
        } else {
            return list;
        }
    }

    @Override
    public List<AttrItemRespDTO> attr(DailyReqDTO request) {
        List<AttrItemRespDTO> attrs = businessDailyMapper.attr(request);
        Map<String, List<AttrItemRespDTO>> ref = attrs.stream().collect(Collectors.groupingBy(AttrItemRespDTO::getAttrGroupName, Collectors.toList()));
        List<AttrItemRespDTO> result = new ArrayList<>(ref.size());
        ref.forEach((e, a) -> {
            if (a == null || a.isEmpty()) {
                return;
            }
            AttrItemRespDTO template = a.get(0);
            AttrItemRespDTO itemRespDTO = new AttrItemRespDTO();
            itemRespDTO.setAttrGroupGuid(template.getAttrGroupGuid());
            itemRespDTO.setAttrGroupName(template.getAttrGroupName());
            BigDecimal count = new BigDecimal(0);
            BigDecimal amount = new BigDecimal(0);
            for (AttrItemRespDTO s : a) {
                if (s.getItemType() == 3) {
                    count = count.add(s.getNum());
                    amount = amount.add(s.getNum().multiply(s.getUnitPrice()));
                    s.setAmount(s.getNum().multiply(s.getUnitPrice()));
                    s.setQuantum(s.getNum());
                } else {
                    count = count.add(s.getQuantum());
                    amount = amount.add(s.getAmount());
                }
            }
            Map<String, AttrItemRespDTO> refs = a.stream().collect(Collectors.toMap(
                    (s) -> s.getGuid() + s.getUnitPrice()
                    , s -> s,
                    (s, t) -> {
                        s.setQuantum(s.getQuantum().add(t.getQuantum()));
                        s.setAmount(s.getAmount().add(t.getAmount()));
                        return s;
                    })
            );
            itemRespDTO.setAttrs(new ArrayList<>(refs.values()));
            BigDecimal bigDecimal = amount.setScale(2);
            itemRespDTO.setUnitPrice(template.getUnitPrice());
            itemRespDTO.setAmount(bigDecimal);
            itemRespDTO.setName(template.getName());
            itemRespDTO.setQuantum(count);
            result.add(itemRespDTO);

        });
        return result;
    }

    @Override
    public List<ItemRespDTO> classify(DailyReqDTO request) {
        List<ItemRespDTO> list = businessDailyMapper.classify(request);
        Map<String, List<ItemRespDTO>> ref = list.stream().collect(Collectors.groupingBy(ItemRespDTO::getGuid, Collectors.toList()));
        List<ItemRespDTO> retList = new ArrayList<>(ref.size());
        ref.forEach((e, a) -> {
            if (a == null || a.isEmpty()) {
                return;
            }
            if (a.size() == 1) {
                a.get(0).setAmount(a.get(0).getAmount().setScale(2, RoundingMode.HALF_UP));
                a.get(0).setDiscountAmount(a.get(0).getDiscountAmount().setScale(2, RoundingMode.HALF_UP));
                retList.add(a.get(0));
                return;
            }
            ItemRespDTO template = a.get(0);
            ItemRespDTO itemRespDTO = new ItemRespDTO();
            itemRespDTO.setGuid(template.getGuid());
            itemRespDTO.setName(template.getName());
            BigDecimal count = new BigDecimal(0);
            BigDecimal amount = new BigDecimal(0);
            BigDecimal discountAmount = BigDecimal.ZERO;
            for (ItemRespDTO s : a) {
                count = count.add(s.getQuantum());
                amount = amount.add(s.getAmount());
                discountAmount = discountAmount.add(s.getDiscountAmount());
            }
            itemRespDTO.setQuantum(count);
            itemRespDTO.setAmount(amount.setScale(2, RoundingMode.HALF_UP));
            itemRespDTO.setDiscountAmount(discountAmount.setScale(2, RoundingMode.HALF_UP));
            retList.add(itemRespDTO);
        });
        retList.removeIf(r -> BigDecimalUtil.equelZero(r.getAmount()) && BigDecimalUtil.equelZero(r.getQuantum()));
        log.info("classify request: entry {}, and return {}", JacksonUtils.writeValueAsString(request), JacksonUtils.writeValueAsString(retList));
        return retList;
    }

    @Override
    public List<DiningTypeRespDTO> diningType(DailyReqDTO request) {
        //index 0:正餐  1：快餐
        List<DiningTypeRespDTO> datas = businessDailyMapper.diningType(request);
        log.warn("[diningType],datas={}", JacksonUtils.writeValueAsString(datas));
        if (Objects.isNull(datas)) {
            return Collections.emptyList();
        }
        //index 0:正餐  1：快餐
        BigDecimal[] amount = businessDailyMapper.consumerAmount(request);
        // 团购验券
        List<AmountItemDTO> grouponAmountItemList = businessDailyMapper.consumerGrouponAmount(request);
        Map<Integer, AmountItemDTO> grouponAmountItemMap = grouponAmountItemList.stream()
                .collect(Collectors.toMap(AmountItemDTO::getCode, Function.identity(), (key1, key2) -> key1));
        // 统计正餐余出金额（使用第三方活动超出金额）
//        BigDecimal excessAmount = businessDailyMapper.calExcessAmount(request);
        Pair<BigDecimal, BigDecimal> amountByTradeMode = getAmountByTradeMode(amount);
        //正餐
        BigDecimal dinner = amountByTradeMode.getLeft();
        //快餐
        BigDecimal fastFood = amountByTradeMode.getRight();
//        if (BigDecimalUtil.greaterThanZero(excessAmount)) {
//            dinner = dinner.subtract(excessAmount);
//        }
        for (int i = 0, len = datas.size(); i < len; i++) {
            datas.get(i).setAmount(i == 0 ? dinner : fastFood);
            AmountItemDTO grouponAmountItem = grouponAmountItemMap.get(i);
            if (Objects.nonNull(grouponAmountItem)) {
                datas.get(i).setAmount(datas.get(i).getAmount().add(grouponAmountItem.getAmount()));
            }
            //消费人数
            datas.get(i).setGuestCount(datas.get(i).getGuestCount() == null ? 0 : datas.get(i).getGuestCount());
            //人均消费
            datas.get(i).setGuestPrice(datas.get(i).getGuestCount() != 0 ? datas.get(i).getAmount().divide(new
                    BigDecimal(datas.get(i).getGuestCount()), 2, RoundingMode
                    .HALF_UP) : BigDecimal.ZERO);
            //单均消费
            datas.get(i).setOrderPrice(datas.get(i).getOrderCount() != 0 ? datas.get(i).getAmount().divide(new
                    BigDecimal(datas.get(i).getOrderCount()), 2, RoundingMode
                    .HALF_UP) : BigDecimal.ZERO);
        }
        return datas;
    }

    private Pair<BigDecimal, BigDecimal> getAmountByTradeMode(BigDecimal[] amount) {
        //快餐
        BigDecimal fastFood = BigDecimal.ZERO;
        //正餐
        BigDecimal dinner = BigDecimal.ZERO;
        if (amount != null) {
            dinner = amount[0] == null ? BigDecimal.ZERO : amount[0];
            if (amount.length >= 2) {
                fastFood = amount[1] == null ? BigDecimal.ZERO : amount[1];
            }
        }
        return Pair.of(dinner, fastFood);
    }

    @Override
    public MemberConsumeRespDTO memberConsume(DailyReqDTO request) {
        MemberConsumeRespDTO notifyMemberConsume = new MemberConsumeRespDTO();
        RequestConsumptionStatistics consumptionStatisticsReqDTO = new RequestConsumptionStatistics();
        consumptionStatisticsReqDTO.setEndDate(LocalDate.parse(request.getBeginTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        consumptionStatisticsReqDTO.setStartDate(LocalDate.parse(request.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        consumptionStatisticsReqDTO.setStoreGuid(request.getStoreGuid());

        ResponseConsumptionStatistics consumptionStatisticsRespDTO = memberDataClientService.getStatisticsOfWay(consumptionStatisticsReqDTO);
        log.info("会员营业日报返回：consumptionStatisticsRespDTO={}", consumptionStatisticsRespDTO);
        if (!ObjectUtils.isEmpty(consumptionStatisticsRespDTO)) {
            notifyMemberConsume.setConsumerCount(consumptionStatisticsRespDTO.getConsumptionNum() == null ? 0 : Integer.parseInt(consumptionStatisticsRespDTO.getConsumptionNum().toString()));
            notifyMemberConsume.setConsumerAmount(consumptionStatisticsRespDTO.getConsumptionAmount());
            notifyMemberConsume.setPrepaidAmount(consumptionStatisticsRespDTO.getRechargeAmount());
            notifyMemberConsume.setPrepaidCount(consumptionStatisticsRespDTO.getRechargeNum() == null ? 0 : Integer.parseInt(consumptionStatisticsRespDTO.getRechargeNum().toString()));
            notifyMemberConsume.setPrepaidGiveAmount(consumptionStatisticsRespDTO.getPresentAmount());
            notifyMemberConsume.setPrepaidTakeInAmount(consumptionStatisticsRespDTO.getIncomeAmount());
            notifyMemberConsume.setPrepaidItems(this.dealData(consumptionStatisticsRespDTO));
        }

        return notifyMemberConsume;
    }

    public List<AmountItemDTO> dealData(ResponseConsumptionStatistics consumptionStatisticsRespDTO) {
        List<AmountItemDTO> data = new ArrayList<>();
        List<ResponsePayWayDetail> payList = consumptionStatisticsRespDTO.getPayWayDetailList();
        log.info("会员营业日报返回：payList={}", payList);
        for (ResponsePayWayDetail pay : payList) {
            AmountItemDTO amountItemDTO = new AmountItemDTO();
            amountItemDTO.setAmount(pay.getPayAmount());
            amountItemDTO.setName(pay.getPayName());
            data.add(amountItemDTO);
        }
        log.info("会员营业日报支付方式：data={}", data);
        return data;
    }

    @Override
    public List<GatherRespDTO> gather(DailyReqDTO request) {
        List<GatherRespDTO> list = businessDailyMapper.gather(request);
       /* List<GatherRespDTO> list = new ArrayList<>(businessDailyMapper.gather(request).stream()
                .collect(Collectors.toMap(GatherRespDTO::getGatherName, Function.identity(),
                        (gatherRespDTO, gatherRespDTO2) -> {
                            gatherRespDTO.setGatherCode(gatherRespDTO2.getGatherCode());
                            gatherRespDTO.setGatherName("" +
                                    "");
                            gatherRespDTO.setConsumerAmount(
                                    Optional.ofNullable(gatherRespDTO.getConsumerAmount()).orElse(BigDecimal.ZERO)
                                            .add(gatherRespDTO2.getConsumerAmount()));
                            return gatherRespDTO;
                        })).values());*/
        log.warn("收入概况list = {}", JacksonUtils.writeValueAsString(list));

        // 聚合支付处理
        handleGatherAggPay(list);

        // 团购处理
        handleGatherGroupon(request, list);

        request.setEndTime(request.getEndTime().length() == 10 ? request.getEndTime() + DATE_TIME_END : request.getEndTime());
        request.setBeginTime(request.getBeginTime().length() == 10 ? request.getBeginTime() + DATE_TIME_START : request.getBeginTime());
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime start = LocalDateTime.parse(request.getBeginTime(), df);
        LocalDateTime end = LocalDateTime.parse(request.getEndTime(), df);
        RequestDutyStatis dutyReq = new RequestDutyStatis();
        dutyReq.setStartTime(start);
        dutyReq.setEndTime(end);
        dutyReq.setStoreGuid(request.getStoreGuid());
        //只包含会员充值
        ResponseDutyStatis dutyResp = memberDataClientService.getStatisticsOfTotal(dutyReq);
        list = this.changeData(dutyResp.getRechargeDetailList(), list);
        // 特殊处理第三方活动收入金额
        GatherRespDTO thirdGather = list.stream().filter(e -> PaymentTypeEnum.THIRD_ACTIVITY.getCode() == e.getGatherCode()).findFirst().orElse(null);
        if (Objects.nonNull(thirdGather)) {
            // 查询余出金额
            BigDecimal excessAmount = businessDailyMapper.calExcessAmount(request);
            thirdGather.setExcessAmount(excessAmount);
            // 第三方平台活动金额 = 第三方平台支付金额 - 余出金额
            if (Objects.nonNull(thirdGather.getConsumerAmount()) && thirdGather.getConsumerAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (Objects.nonNull(thirdGather.getExcessAmount()) && thirdGather.getExcessAmount().compareTo(BigDecimal.ZERO) > 0) {
                    thirdGather.setConsumerAmount(thirdGather.getConsumerAmount().subtract(thirdGather.getExcessAmount()));
                }
            }
        }
        log.warn("最终收入概况list = {}", JacksonUtils.writeValueAsString(list));
        Map<String, List<GatherRespDTO>> listMap = CollectionUtil.toListMap(list, "gatherName");
        list.forEach(s -> {
            if (listMap.containsKey(s.getGatherName())) {
                listMap.get(s.getGatherName()).get(0).setPrepaidAmount(s.getPrepaidAmount());
            } else {
                List<GatherRespDTO> gatherRespDTOList = new ArrayList<>();
                gatherRespDTOList.add(s);
                listMap.put(s.getGatherName(), gatherRespDTOList);
            }
        });
        List<GatherRespDTO> finalList = new ArrayList<>();
        listMap.forEach((m, a) ->
                finalList.add(a.get(0))
        );
        list.clear();
        list = finalList;
        return list;
    }

    private void handleGatherAggPay(List<GatherRespDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        boolean isAggPay = false;
        BigDecimal aggAmount = BigDecimal.ZERO;
        Iterator<GatherRespDTO> iterator = list.iterator();
        List<GatherRespDTO.InnerDetails> innerDetailsList = Lists.newArrayList();
        while (iterator.hasNext()) {
            GatherRespDTO gatherRespDTO = iterator.next();
            if (Objects.nonNull(gatherRespDTO) && PaymentTypeEnum.AGG.getCode() == gatherRespDTO.getGatherCode()) {
                isAggPay = true;
                aggAmount = aggAmount.add(gatherRespDTO.getConsumerAmount());
                GatherRespDTO.InnerDetails innerDetails = new GatherRespDTO.InnerDetails();
                innerDetails.setGatherName(PayPowerId.getPlatFormById(String.valueOf(gatherRespDTO.getPayPowerId())));
                innerDetails.setConsumerAmount(gatherRespDTO.getConsumerAmount());
                innerDetailsList.add(innerDetails);
                iterator.remove();
            }
        }
        if (isAggPay) {
            GatherRespDTO aggGatherRespDTO = new GatherRespDTO();
            aggGatherRespDTO.setGatherCode(PaymentTypeEnum.AGG.getCode());
            aggGatherRespDTO.setGatherName("聚合支付");
            aggGatherRespDTO.setConsumerAmount(aggAmount);
            aggGatherRespDTO.setInnerDetails(innerDetailsList);
            list.add(aggGatherRespDTO);
        }
    }

    private void handleGatherGroupon(DailyReqDTO request, List<GatherRespDTO> list) {
        List<AmountItemDTO> grouponAmountList = grouponMpService.listByRequest(request);
        if(CollUtil.isEmpty(grouponAmountList)){
            return;
        }
        Map<Integer, AmountItemDTO> grouponAmountMap = grouponAmountList.stream()
                .collect(Collectors.toMap(AmountItemDTO::getCode, Function.identity(), (v1, v2) -> v1));
        list.removeIf(g -> Objects.equals(DiscountTypeEnum.GROUPON.getCode(), g.getGatherCode()));
        if(CollUtil.isEmpty(list)){
            return;
        }
        list.forEach(g -> {
            // 美团团购枚举不一致特殊处理
            if (Objects.equals(PaymentTypeEnum.MT_GROUPON.getCode(), g.getGatherCode())) {
                AmountItemDTO amountItemDTO = grouponAmountMap.get(GroupBuyTypeEnum.MEI_TUAN.getCode());
                setAmount(g, amountItemDTO);
                return;
            }
            AmountItemDTO amountItemDTO = grouponAmountMap.get(g.getGatherCode());
            setAmount(g, amountItemDTO);
        });
    }

    private static void setAmount(GatherRespDTO g, AmountItemDTO amountItemDTO) {
        if (!ObjectUtils.isEmpty(amountItemDTO)) {
            g.setConsumerAmount(amountItemDTO.getAmount());
            g.setTotalAmount(amountItemDTO.getAmount());
        }
    }


    public List<GatherRespDTO> changeData(List<ResponsePayWayDetail> list, List<GatherRespDTO> data) {
        if (CollectionUtil.isEmpty(list)) {
            return data;
        }
        Map<String, GatherRespDTO> collect = data.stream()
                .collect(Collectors.toMap(GatherRespDTO::getGatherName, Functions.identity(), (key1, key2) -> key1));
        for (ResponsePayWayDetail payWayDetailCommon : list) {
            //String result = mapping(payWayDetailCommon.getPayName());
            String result = payWayDetailCommon.getPayName();
            GatherRespDTO gatherRespDTO = collect.get(result);
            if (gatherRespDTO != null) {
                gatherRespDTO.setGatherCode(payWayDetailCommon.getPayWay());
                gatherRespDTO.setGatherName(payWayDetailCommon.getPayName());
                gatherRespDTO.setPrepaidAmount(payWayDetailCommon.getPayAmount());
            } else {
                GatherRespDTO gatherRespDTO1 = new GatherRespDTO();
                gatherRespDTO1.setGatherCode(Optional.ofNullable(payWayDetailCommon.getPayWay()).isPresent() ? payWayDetailCommon.getPayWay() : 10);
                gatherRespDTO1.setGatherName(payWayDetailCommon.getPayName());
                gatherRespDTO1.setPrepaidAmount(payWayDetailCommon.getPayAmount());
                gatherRespDTO1.setConsumerAmount(BigDecimal.ZERO);
                data.add(gatherRespDTO1);
            }
        }
        return data;


    }

    /**
     * 刘一山
     * switch (payWay) {
     * case 0: // 现金支付
     * return 1;
     * case 1: // 聚合支付
     * return 2;
     * case 2: // 银联卡支付
     * return 3;
     * default:
     * return 0;
     * }
     *
     * @param
     * @return
     */
    /*private Integer mapping(Integer payWay) {
        switch (payWay) {
            case 0:
                // 现金支付
                return 1;
            case 1:
                // 聚合支付
                return 2;
            case 2:
                // 银联卡支付
                return 3;
            default:
                return 10;
        }
    }*/
    @Override
    public OverviewRespDTO overview(DailyReqDTO request) {
        OverviewRespDTO ov = businessDailyMapper.overview(request);
        if (ov == null) {
            return null;
        }
        //收款方式分组统计
        ov.setGatherItems(businessDailyMapper.paymentTypeCount(request));
        // 聚合支付方式处理
        handleAggPaymentType(ov);
        //优惠方式分组统计
        ov.setDiscountItems(businessDailyMapper.discountTypeCount(request));
        for (AmountItemDTO discountItem : ov.getDiscountItems()) {
            discountItem.setName(DiscountTypeEnum.getDesc(discountItem.getCode()));
        }
        // 团购券明细
        handleGroupon(request, ov);

        // 正餐客流量，上座率，开台率，翻台率，平均用餐时长
        handleStatisticData(request, ov);

        if (CollectionUtils.isNotEmpty(ov.getGatherItems())) {
            ov.getGatherItems().removeIf(e -> e.getAmount().compareTo(BigDecimal.ZERO) == 0);
            // 这里特殊处理销售收入，需要把第三方平台活动纳入销售收入
            AmountItemDTO thirdAmount = ov.getGatherItems().stream().filter(e -> PaymentTypeEnum.THIRD_ACTIVITY.getCode() == e.getCode())
                    .findFirst().orElse(null);
            if (Objects.nonNull(thirdAmount)) {
                // 查询余出金额
                BigDecimal excessAmount = businessDailyMapper.calExcessAmount(request);
                thirdAmount.setExcessAmount(excessAmount);
                // 第三方平台活动金额 = 第三方平台支付金额 - 余出金额
                if (Objects.nonNull(thirdAmount.getAmount()) && thirdAmount.getAmount().compareTo(BigDecimal.ZERO) > 0
                        && (Objects.nonNull(thirdAmount.getExcessAmount()) && thirdAmount.getExcessAmount().compareTo(BigDecimal.ZERO) > 0)) {
                    thirdAmount.setAmount(thirdAmount.getAmount().subtract(thirdAmount.getExcessAmount()));
                }
            }
        }
        //打印营业概况
        if (Objects.equals(1, request.getIsPrint())) {
            // 查询收银员信息
            List<String> checkoutStaffs = businessDailyMapper.getCheckoutStaffs(request);
            ov.setCheckoutStaffs(checkoutStaffs.stream().filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList()));
            // 营业概况新增打印信息
            handleOverviewStatisticPrintInfo(ov, request);
        }
        //商户后台收款明细 查询美团团购商家预计金额
        if (CollectionUtils.isNotEmpty(request.getStoreGuids())) {
            BigDecimal mtGrouponEstimatedAmount = businessDailyMapper.getMtGrouponEstimatedAmount(request);
            log.info("美团团购商家预计金额：{}", mtGrouponEstimatedAmount);
            ov.setMtGrouponEstimatedAmount(mtGrouponEstimatedAmount);
        }
        return ov;
    }

    private void handleAggPaymentType(OverviewRespDTO ov) {
        if (CollectionUtils.isEmpty(ov.getGatherItems())) {
            return;
        }
        boolean isAggPay = false;
        BigDecimal aggAmount = BigDecimal.ZERO;
        int aggOrderCount = 0;
        Iterator<AmountItemDTO> iterator = ov.getGatherItems().iterator();
        List<AmountItemDTO.InnerDetails> innerDetailsList = Lists.newArrayList();
        while (iterator.hasNext()) {
            AmountItemDTO amountItemDTO = iterator.next();
            if (Objects.nonNull(amountItemDTO) && PaymentTypeEnum.AGG.getCode() == amountItemDTO.getCode()) {
                isAggPay = true;
                aggAmount = aggAmount.add(amountItemDTO.getAmount());
                aggOrderCount += amountItemDTO.getOrderCount();

                AmountItemDTO.InnerDetails details = new AmountItemDTO.InnerDetails();
                details.setName(PayPowerId.getPlatFormById(String.valueOf(amountItemDTO.getPayPowerId())));
                details.setAmount(amountItemDTO.getAmount());
                details.setOrderCount(amountItemDTO.getOrderCount());
                innerDetailsList.add(details);
                // 移除聚合支付记录，下面合并成一个
                iterator.remove();
            }
        }
        if (isAggPay) {
            AmountItemDTO aggAmountItem = new AmountItemDTO();
            aggAmountItem.setName("聚合支付");
            aggAmountItem.setCode(PaymentTypeEnum.AGG.getCode());
            aggAmountItem.setAmount(aggAmount);
            aggAmountItem.setOrderCount(aggOrderCount);
            aggAmountItem.setInnerDetails(innerDetailsList);
            ov.getGatherItems().add(aggAmountItem);
        }
    }



    private void handleOverviewStatisticPrintInfo(OverviewRespDTO ov, DailyReqDTO request) {
        // 会员充值金额
        handleMemberRechargeInfo(ov, request);
    }

    private void handleMemberRechargeInfo(OverviewRespDTO ov, DailyReqDTO request) {
        LocalDateTime start = LocalDateTime.parse(request.getBeginTime().length() == 10 ?
                request.getBeginTime() + DATE_TIME_START : request.getBeginTime(), DATETIME_FORMATTER);
        LocalDateTime end = LocalDateTime.parse(request.getEndTime().length() == 10 ?
                request.getEndTime() + DATE_TIME_END : request.getEndTime(), DATETIME_FORMATTER);
        RequestDutyStatis dutyReq = new RequestDutyStatis();
        dutyReq.setStartTime(start);
        dutyReq.setEndTime(end);
        dutyReq.setStoreGuid(request.getStoreGuid());

        List<String> checkoutStaffGuids = request.getCheckoutStaffGuids();
        Set<String> checkoutStaffGuidSet = new HashSet<>(checkoutStaffGuids);
        if (CollectionUtils.isNotEmpty(checkoutStaffGuids)) {
            List<UserBriefDTO> userBriefDTOS = staffClientService.storeUsers(request.getStoreGuid());
            log.info("staffClientService invoke: entry {}, and return {}", request.getStoreGuid(), JacksonUtils.writeValueAsString(userBriefDTOS));
            List<String> userAccountList = userBriefDTOS.stream()
                    .filter(dto -> checkoutStaffGuidSet.contains(dto.getUserGuid()))
                    .map(UserBriefDTO::getAccount)
                    .filter(account -> !StringUtils.isEmpty(account))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userAccountList)) {
                dutyReq.setOperatorGuids(userAccountList);
            }
        }
        //只包含会员充值
        ResponseDutyStatis dutyResp = memberDataClientService.getStatisticsOfTotal(dutyReq);
        log.info("memberDataClientService invoke: entry {}, and return {}", JacksonUtils.writeValueAsString(dutyReq), JacksonUtils.writeValueAsString(dutyResp));
        ov.setRechargeMoney(dutyResp.getRechargeMoney());
    }

    private void handleStatisticData(DailyReqDTO request, OverviewRespDTO ov) {
        List<TableBasicDTO> tableBasicDTOS = getTableInfo(request);
        // 总桌台数
        int tableCount = tableBasicDTOS.size();
        // 总餐位数
        int totalSeats = tableBasicDTOS.stream()
                .filter(Objects::nonNull).mapToInt(TableBasicDTO::getSeats).sum();
        // 客流量
        int dineInGuestCount = 0;
        // 桌台使用次数
        int tableUseCount = 0;
        // 总用餐时间，单位分钟
        long totalDineInTime = 0;
        // 查询订单数据
        List<OverviewStatisticRespDTO> overviewStatisticRespDTOS = businessDailyMapper.overviewStatistic(request);
        // key = mainOrderGuid
        Map<String, List<OverviewStatisticRespDTO>> subOrderMap = overviewStatisticRespDTOS.stream()
                .filter(dto -> !OrderConstants.MAIN_ORDER_GUID.equals(dto.getMainOrderGuid()))
                .collect(Collectors.groupingBy(OverviewStatisticRespDTO::getMainOrderGuid));
        for (OverviewStatisticRespDTO dto : overviewStatisticRespDTOS) {
            if (dto.getGuestCount() != null) {
                dineInGuestCount += dto.getGuestCount();
            }
            // 只处理主订单
            if (OrderConstants.MAIN_ORDER_GUID.equals(dto.getMainOrderGuid())) {
                if (OrderConstants.ASSOCIATED_FLAG.equals(dto.getAssociatedFlag())) {
                    long minutesBetween = DateUtil.minutesBetween(dto.getGmtCreate(), dto.getCheckoutTime());
                    if (!StringUtils.isEmpty(dto.getAssociatedTableGuids())) {
                        int size = 0;
                        try {
                            size = JacksonUtils.toObjectList(String.class, dto.getAssociatedTableGuids()).size();
                        } catch (Exception e) {
                            log.error("解析联台数量失败, guid:{}, AssociatedTableGuids:{}",
                                    dto.getGuid(), dto.getAssociatedTableGuids());
                        }
                        tableUseCount += size;
                        totalDineInTime += minutesBetween * size;
                    } else {
                        tableUseCount++;
                        totalDineInTime += minutesBetween;
                    }
                } else {
                    tableUseCount++;
                    long minutesBetween = DateUtil.minutesBetween(dto.getGmtCreate(), dto.getCheckoutTime());
                    totalDineInTime += minutesBetween;
                    List<OverviewStatisticRespDTO> subDTOs = subOrderMap.get(dto.getGuid());
                    if (CollectionUtils.isNotEmpty(subDTOs)) {
                        tableUseCount += subDTOs.size();
                        totalDineInTime += subDTOs.stream()
                                .mapToLong(subDTO -> DateUtil.minutesBetween(subDTO.getGmtCreate(), subDTO.getCheckoutTime()))
                                .sum();
                    }
                }
            }
        }
        ov.setDineInGuestCount(dineInGuestCount);
        // 上座率
        setOccupancyRate(ov, totalSeats, dineInGuestCount);
        // 开台率
        setOpenTableRate(ov, tableCount, tableUseCount);
        // 翻台率
        setFlipTableRate(ov, tableCount, tableUseCount);
        // 平均用餐时长
        setAvgDineInTime(ov, tableUseCount, totalDineInTime);
    }

    private List<TableBasicDTO> getTableInfo(DailyReqDTO request) {
        TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid(request.getStoreGuid());
        List<TableBasicDTO> tableBasicDTOS = tableClientService.listByWeb(tableBasicQueryDTO);
        return tableBasicDTOS;
    }

    private void setAvgDineInTime(OverviewRespDTO overviewRespDTO, int tableUseCount, long totalDineInTime) {
        // 平均用餐时长 = (离座时间 - 入座时间) ÷ 总桌数，单位分钟
        int avgDineInTime;
        if (tableUseCount == 0) {
            avgDineInTime = 0;
        } else {
            avgDineInTime = (int) Math.floor((double) totalDineInTime / tableUseCount + 0.5);
        }
        overviewRespDTO.setAvgDineInTime(avgDineInTime);
    }

    private void setFlipTableRate(OverviewRespDTO overviewRespDTO, int tableCount, int tableUseCount) {
        // 翻台率 = (桌台使用次数 - 总桌台数) ÷ 总桌台数 × 100%
        double flipTableRate;
        if (tableCount == 0) {
            flipTableRate = 0;
        } else {
            flipTableRate = (double) (tableUseCount - tableCount) / tableCount;
        }
        // 翻台率
        String flipTableRatePercent = String.format("%.2f%%", flipTableRate * 100);
        overviewRespDTO.setFlipTableRatePercent(flipTableRatePercent);
    }

    private void setOpenTableRate(OverviewRespDTO overviewRespDTO, int tableCount, int tableUseCount) {
        // 开台率 = 桌台使用次数 ÷ 总桌台数 × 100%
        double openTableRate;
        if (tableCount == 0) {
            openTableRate = 0;
        } else {
            openTableRate = (double) tableUseCount / tableCount;
        }
        // 开台率
        String openTableRatePercent = String.format("%.2f%%", openTableRate * 100);
        overviewRespDTO.setOpenTableRatePercent(openTableRatePercent);
    }

    private void setOccupancyRate(OverviewRespDTO overviewRespDTO, int totalSeats, int traffic) {
        // 上座率 = 客流量 ÷ 总餐位数 × 100%
        double occupancyRate;
        if (totalSeats == 0) {
            occupancyRate = 0;
        } else {
            occupancyRate = (double) traffic / totalSeats;
        }
        // 上座率
        String occupancyRatePercent = String.format("%.2f%%", occupancyRate * 100);
        overviewRespDTO.setOccupancyRatePercent(occupancyRatePercent);
    }

    @Override
    public RefundRespDTO refund(DailyReqDTO request) {
        RefundRespDTO refundRespDTO = new RefundRespDTO();
        List<RefundAmountDTO> datas = businessDailyMapper.refund(request);
        log.warn("[refund],datas={}", JacksonUtils.writeValueAsString(datas));
        if (Objects.nonNull(datas)) {
            refundRespDTO.setRefundAmounts(datas);
        }
        //打印营业概况需要查询收银员信息
        if (Objects.equals(1, request.getIsPrint())) {
            List<String> checkoutStaffs = businessDailyMapper.getRefundCheckoutStaffs(request);
            refundRespDTO.setCheckoutStaffs(checkoutStaffs.stream().filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList()));
        }
        log.info("[refundRespDTO],datas={}", JacksonUtils.writeValueAsString(refundRespDTO));
        return refundRespDTO;
    }

    private void handleGroupon(DailyReqDTO request, OverviewRespDTO ov) {
        // 优惠方式过滤团购验券、第三方活动展示
        ov.getDiscountItems().removeIf(e -> DiscountTypeEnum.THIRD_ACTIVITY.getCode() == e.getCode()
                || GroupBuyTypeEnum.CODE_LIST.contains(e.getCode()));
        List<AmountItemDTO> grouponAmountList = grouponMpService.listByRequestGroupByName(request);
        if (CollectionUtils.isNotEmpty(grouponAmountList)) {
            log.warn("[handleGroupon],grouponAmountList={}", JacksonUtils.writeValueAsString(grouponAmountList));
            Map<Integer, List<AmountItemDTO>> grouponAmountMap = grouponAmountList.stream()
                    .collect(Collectors.groupingBy(AmountItemDTO::getCode));
            // 优惠总额处理
            handleDiscountFee(ov, grouponAmountMap);
            // 销售总净额处理
            handleSaleFee(ov, grouponAmountMap);
        }
    }

    private static void handleDiscountFee(OverviewRespDTO ov, Map<Integer, List<AmountItemDTO>> grouponAmountMap) {
        if (MapUtils.isEmpty(grouponAmountMap)) {
            return;
        }
        log.warn("[handleDiscountFee],groupBuyAmountItem={}", JacksonUtils.writeValueAsString(grouponAmountMap));
        grouponAmountMap.forEach((k, v) -> {
            if (CollectionUtils.isNotEmpty(v)) {
                AmountItemDTO discountAmountItem = new AmountItemDTO();
                discountAmountItem.setName(GroupBuyTypeEnum.getDesc(k) + "优惠");
                discountAmountItem.setCode(k * 3);
                BigDecimal discountTotalAmount = BigDecimal.ZERO;
                int discountOrderCount = 0;
                for (AmountItemDTO amountItemDTO : v) {
                    if (Objects.nonNull(amountItemDTO.getDiscountAmount())) {
                        discountTotalAmount = discountTotalAmount.add(amountItemDTO.getDiscountAmount());
                    }
                    if (Objects.nonNull(amountItemDTO.getDiscountOrderCount())) {
                        discountOrderCount += amountItemDTO.getDiscountOrderCount();
                    }
                }
                discountAmountItem.setAmount(discountTotalAmount);
                discountAmountItem.setOrderCount(discountOrderCount);
                ov.getDiscountItems().add(discountAmountItem);
            }
        });
    }

    private void handleSaleFee(OverviewRespDTO ov, Map<Integer, List<AmountItemDTO>> grouponAmountMap) {
        if (CollectionUtils.isEmpty(ov.getGatherItems())) {
            return;
        }
        log.warn("[handleSaleFee],gatherItems={}", JacksonUtils.writeValueAsString(ov.getGatherItems()));
        for (AmountItemDTO gather : ov.getGatherItems()) {
            // 团购验券取顾客购买金额
            Integer code = gather.getCode();
            if (PaymentTypeEnum.MT_GROUPON.getCode() == code) {
                code = GroupBuyTypeEnum.MEI_TUAN.getCode();
            }
            List<AmountItemDTO> amountItemDTOS = grouponAmountMap.get(code);
            if (CollectionUtils.isEmpty(amountItemDTOS)) {
                continue;
            }
            gatherAmountItemSum(gather, amountItemDTOS);
        }
        BigDecimal gatherAmount = ov.getGatherItems()
                .stream()
                .map(AmountItemDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        ov.setGatherAmount(gatherAmount);
    }

    private void gatherAmountItemSum(AmountItemDTO gather, List<AmountItemDTO> amountItemDTOS) {
        Integer code = gather.getCode();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalDisCountAmount = BigDecimal.ZERO;
        int totalOrderCount = 0;
        int totalGrouponCount = 0;
        List<AmountItemDTO.InnerDetails> innerDetails = new ArrayList<>();
        for (AmountItemDTO amountItemDTO : amountItemDTOS) {
            AmountItemDTO.InnerDetails innerDetail = getInnerDetail(amountItemDTO);
            innerDetails.add(innerDetail);
            if (Objects.nonNull(amountItemDTO.getAmount())) {
                totalAmount = totalAmount.add(amountItemDTO.getAmount());
            }
            if (Objects.nonNull(amountItemDTO.getDiscountAmount())) {
                totalDisCountAmount = totalDisCountAmount.add(amountItemDTO.getDiscountAmount());
            }
            if (Objects.nonNull(amountItemDTO.getOrderCount())) {
                totalOrderCount += amountItemDTO.getOrderCount();
            }
            if (Objects.nonNull(amountItemDTO.getGrouponCount())) {
                totalGrouponCount += amountItemDTO.getGrouponCount();
            }
        }
        gather.setInnerDetails(innerDetails);
        gather.setAmount(totalAmount);
        gather.setDiscountAmount(totalDisCountAmount);
        gather.setOrderCount(totalOrderCount);
        gather.setGrouponCount(totalGrouponCount);
        gather.setIsGroupon(true);
        if (PaymentTypeEnum.MT_MAITON.getCode() == code) {
            gather.setIsGroupon(false);
            gather.setInnerDetails(Lists.newArrayList());
        }
    }

    private static AmountItemDTO.@NotNull InnerDetails getInnerDetail(AmountItemDTO amountItemDTO) {
        AmountItemDTO.InnerDetails innerDetail = new AmountItemDTO.InnerDetails();
        innerDetail.setName(amountItemDTO.getName());
        innerDetail.setAmount(amountItemDTO.getAmount());
        innerDetail.setDiscountAmount(amountItemDTO.getDiscountAmount());
        innerDetail.setOrderCount(amountItemDTO.getOrderCount());
        innerDetail.setGrouponCount(amountItemDTO.getGrouponCount());
        innerDetail.setIsGroupon(true);
        return innerDetail;
    }
}