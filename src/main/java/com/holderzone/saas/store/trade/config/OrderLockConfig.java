package com.holderzone.saas.store.trade.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderLockConfig
 * @date 2019/10/08 11:50
 * @description //TODO
 * @program IdeaProjects
 */
@Component
@ConfigurationProperties(prefix = "order.lock")
@Data
public class OrderLockConfig {
    //TODO 自定义锁的配置

    private Integer releasesTime;

}