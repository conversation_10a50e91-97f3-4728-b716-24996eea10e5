package com.holderzone.saas.store.trade.repository.interfaces;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponTradeDetailRespDTO;
import com.holderzone.saas.store.trade.entity.domain.GrouponTradeDetailDO;

public interface GrouponTradeDetailService extends IService<GrouponTradeDetailDO> {

    void create(MtCouponTradeDetailRespDTO mtCouponTradeDetailRespDTO);

    void cancel(String orderGuid, String couponCode);

}
