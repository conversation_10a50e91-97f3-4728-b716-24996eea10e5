package com.holderzone.saas.store.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.RuleTypeEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.entity.domain.GrouponDO;
import com.holderzone.saas.store.trade.entity.domain.ThirdActivityRecordDO;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.mapper.ThirdActivityRecordMapper;
import com.holderzone.saas.store.trade.repository.feign.ThirdActivityClientService;
import com.holderzone.saas.store.trade.repository.interfaces.DiscountService;
import com.holderzone.saas.store.trade.repository.interfaces.GrouponMpService;
import com.holderzone.saas.store.trade.service.IThirdActivityRecordService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.transform.ThirdActivityTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.trade.constants.DiscountTypeConstants.*;

/**
 * <p>
 * 第三方活动使用记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
@Slf4j
@Service
@AllArgsConstructor
public class ThirdActivityRecordServiceImpl extends ServiceImpl<ThirdActivityRecordMapper, ThirdActivityRecordDO>
        implements IThirdActivityRecordService {

    private final DynamicHelper dynamicHelper;

    private final ThirdActivityClientService thirdActivityClientService;

    private final DiscountService discountService;

    private final GrouponMpService grouponMpService;

    private static final ThirdActivityTransform thirdActivityTransform = ThirdActivityTransform.INSTANCE;

    private static final OrderTransform orderTransform = OrderTransform.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveThirdActivityInfo(RecordThirdActivityInfoReqDTO reqDTO) {
        // 参数校验
        List<ThirdActivityRecordDTO> thirdActivityRecordList = reqDTO.getThirdActivityRecordList();
        // 校验当前活动参数
        checkParam(thirdActivityRecordList);
        ThirdActivityRecordDTO activityRecord = thirdActivityRecordList.get(0);
        // 校验活动和其他第三方活动是否共享
        // 查询此订单已添加的活动
        List<ThirdActivityRecordDTO> thirdActivityRecords = listThirdActivityByOrderGuid(activityRecord.getOrderGuid());
        ThirdActivityRespDTO activityResp = new ThirdActivityRespDTO();
        activityResp.setGuid(activityRecord.getActivityGuid());
        activityResp.setIsThirdShare(activityRecord.getIsThirdShare());
        try {
            checkActivityShare(activityRecord.getOrderGuid(), activityResp, thirdActivityRecords);
        } catch (Exception e) {
            throw new BusinessException("存在活动不可与其他平台活动共享");
        }
        // 删除原有的记录
        revokeThirdActivityRecord(activityRecord.getOrderGuid(), activityRecord.getActivityGuid());
        // 保存新的记录
        saveThirdActivityRecord(thirdActivityRecordList);
    }

    /**
     * 参数校验
     *
     * @param thirdActivityRecordList 入参
     */
    private void checkParam(List<ThirdActivityRecordDTO> thirdActivityRecordList) {
        if (CollectionUtils.isEmpty(thirdActivityRecordList)) {
            throw new BusinessException("保存第三方活动记录为空");
        }
        // ⑤点击“☑”按钮需要判断券码数量是否超过，超过提示tips：请输入正确的券码
        List<ThirdActivityRespDTO> thirdActivityList = thirdActivityClientService.list();
        if (CollectionUtils.isEmpty(thirdActivityList)) {
            throw new BusinessException(Constant.NOT_EXIST_ACTIVITY);
        }
        Map<String, ThirdActivityRespDTO> activityMap = thirdActivityList.stream()
                .collect(Collectors.toMap(ThirdActivityRespDTO::getGuid, Function.identity(), (dto1, dto2) -> dto2));

        thirdActivityRecordList.forEach(thirdActivityRecordDTO ->
                checkParamRecord(thirdActivityRecordDTO, activityMap, thirdActivityRecordList));
    }

    private void checkParamRecord(ThirdActivityRecordDTO thirdActivityRecordDTO, Map<String, ThirdActivityRespDTO> activityMap,
                                  List<ThirdActivityRecordDTO> thirdActivityRecordList) {
        thirdActivityRecordDTO.getThirdActivityCodeList().forEach(code -> {
            if (code.length() > 30) {
                throw new BusinessException("请输入正确的券码");
            }
        });
        // 校验订单guid 活动guid
        checkOrderActivityGuid(thirdActivityRecordDTO.getOrderGuid(), thirdActivityRecordDTO.getActivityGuid());
        if (StringUtils.isEmpty(thirdActivityRecordDTO.getRuleType())) {
            throw new BusinessException("规则类型不能为空");
        }
        // ⑦同一个活动券码不可重复录入，重复tips提示：券码不可重复
        long count = thirdActivityRecordDTO.getThirdActivityCodeList().stream().distinct().count();
        if (thirdActivityRecordDTO.getThirdActivityCodeList().size() != count) {
            throw new BusinessException("券码不可重复");
        }
        // ⑥点击“☑”按钮需要判断该活动的叠加数量是否达上限（判断数量为已展示在上方的券码），达上限提示：最多可叠加x张
        ThirdActivityRespDTO thirdActivity = activityMap.get(thirdActivityRecordDTO.getActivityGuid());
        if (ObjectUtils.isEmpty(thirdActivity)) {
            log.warn("活动不存在 activityGuid={}", thirdActivityRecordDTO.getActivityGuid());
            throw new BusinessException(Constant.NOT_EXIST_ACTIVITY);
        }
        thirdActivityRecordDTO.setActivityGuid(thirdActivity.getGuid());
        thirdActivityRecordDTO.setIsThirdShare(thirdActivity.getIsThirdShare());
        thirdActivityRecordDTO.setIsActivityShare(thirdActivity.getIsActivityShare());
        // 校验规则类型 0-金额扣减 1-买单优惠
        checkThirdActivityRuleType(thirdActivity, thirdActivityRecordDTO);
        // 该活动不可与其他第三方活动共享
        if (0 == thirdActivity.getIsThirdShare() && thirdActivityRecordList.size() > 1) {
            throw new BusinessException("存在活动不可与其他第三方活动共享");
        }
        // 计算其他活动类型的总金额，如果为不为空说明使用了  其他活动类型包括：整单折扣、整单让价、团购验券、优惠券、营销活动、积分抵扣（满减、满折）
        BigDecimal otherActivityFee = getOtherActivityFee(thirdActivityRecordDTO.getOrderGuid());
        if (0 == thirdActivity.getIsActivityShare() && BigDecimalUtil.greaterThanZero(otherActivityFee)) {
            throw new BusinessException("存在活动不可与其他类型活动共享");
        }
        //记录添加用户购买金额
        if (RuleTypeEnum.AMOUNT_DEDUCTION.getCode() == thirdActivity.getRuleType()) {
            thirdActivityRecordDTO.setCostFee(BigDecimalUtil.nonNullValue(thirdActivity.getCouponCostFee())
                    .multiply(BigDecimal.valueOf(thirdActivityRecordDTO.getThirdActivityCodeList().size())));
        } else {
            thirdActivityRecordDTO.setCostFee(BigDecimalUtil.nonNullValue(thirdActivityRecordDTO.getJoinFee())
                    .add(BigDecimalUtil.nonNullValue(thirdActivityRecordDTO.getNotJoinFee())));
        }
    }


    /**
     * 计算其他活动类型的总金额
     */
    private BigDecimal getOtherActivityFee(String orderGuid) {
        List<DiscountDO> discountDOS = discountService.listByOrderGuid(orderGuid);
        return discountDOS.stream().map(disc -> {
            Integer discountType = disc.getDiscountType();
            switch (discountType) {
                case WHOLE:
                case CONCESSIONAL:
                case MEMBER_GROUPON:
                case GOODS_GROUPON:
                case ACTIVITY:
                case POINTS_DEDUCTION:
                    return Objects.isNull(disc.getDiscountFee()) ? BigDecimal.ZERO : disc.getDiscountFee();
                default:
                    return BigDecimal.ZERO;
            }
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 校验 美团、抖音 这些已对接的第三方活动 并且使用团购验券
     */
    private void checkThirdActivityRule(ThirdActivityRecordDTO recordDTO, List<ThirdActivityRecordDTO> thirdActivityRecords) {
        log.info("校验美团抖音这些已对接的第三方活动并且使用团购验券入参,record:{},thirdActivityRecords:{}", JacksonUtils.writeValueAsString(recordDTO),
                JacksonUtils.writeValueAsString(thirdActivityRecords));
        // 校验订单guid 活动guid
        checkOrderActivityGuid(recordDTO.getOrderGuid(), recordDTO.getActivityGuid());
        // 查询对应活动
        List<ThirdActivityRespDTO> thirdActivityList = thirdActivityClientService.listByGuid(Lists.newArrayList(recordDTO.getActivityGuid()));
        if (CollectionUtils.isEmpty(thirdActivityList)) {
            throw new BusinessException(Constant.NOT_EXIST_ACTIVITY);
        }
        ThirdActivityRespDTO thirdActivity = thirdActivityList.get(0);
        recordDTO.setIsActivityShare(thirdActivity.getIsActivityShare());
        recordDTO.setIsThirdShare(thirdActivity.getIsThirdShare());
        // 校验规则类型 0-金额扣减 1-买单优惠
        checkThirdActivityRuleType(thirdActivity, recordDTO);
        // 校验活动共享规则
        checkActivityShare(recordDTO.getOrderGuid(), thirdActivity, thirdActivityRecords);
    }

    /**
     * 校验订单guid 活动guid
     */
    private void checkOrderActivityGuid(String orderGuid, String activityGuid) {
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException("订单guid不能为空");
        }
        if (StringUtils.isEmpty(activityGuid)) {
            throw new BusinessException(Constant.NOT_EXIST_ACTIVITYGUID);
        }
    }

    /**
     * 校验规则类型 0-金额扣减 1-买单优惠
     */
    private void checkThirdActivityRuleType(ThirdActivityRespDTO thirdActivity, ThirdActivityRecordDTO thirdActivityRecordDTO) {
        long count = thirdActivityRecordDTO.getThirdActivityCodeList().size();
        // ⑥点击“☑”按钮需要判断该活动的叠加数量是否达上限（判断数量为已展示在上方的券码），达上限提示：最多可叠加x张
        if (RuleTypeEnum.AMOUNT_DEDUCTION.getCode() == thirdActivity.getRuleType()) {
            Integer useLimit = thirdActivity.getUseLimit();
            if (count > useLimit) {
                throw new BusinessException("最多可叠加" + useLimit + "张");
            }
        } else {
            BigDecimal totalFee = thirdActivityRecordDTO.getJoinFee();
            BigDecimal limitFee = thirdActivity.getLimitFee();
            if (1 == thirdActivity.getDiscountType() && !ObjectUtils.isEmpty(limitFee) && totalFee.compareTo(limitFee) > 0) {
                throw new BusinessException("参与活动金额需小于" + limitFee + "元");
            }
        }
    }

    private void checkActivityShare(String orderGuid, ThirdActivityRespDTO thirdActivity, List<ThirdActivityRecordDTO> thirdActivityRecords) {
        // 该活动不可与其他第三方活动共享
        if (!CollectionUtils.isEmpty(thirdActivityRecords)) {
            // 判断订单上已使用的第三方活动使用规则
            List<ThirdActivityRecordDTO> unShareList = thirdActivityRecords.stream()
                    .filter(e -> !e.getActivityGuid().equals(thirdActivity.getGuid()))
                    .filter(e -> e.getIsThirdShare() == 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(unShareList)) {
                throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
            }
            if (thirdActivity.getIsThirdShare() == 0) {
                List<ThirdActivityRecordDTO> otherActivityList = thirdActivityRecords.stream()
                        .filter(e -> !e.getActivityGuid().equals(thirdActivity.getGuid())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(otherActivityList)) {
                    throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
                }
            }
        } else {
            if (thirdActivity.getIsThirdShare() == 0) {
                // 如果没有使用其他第三方活动，则需要去查询是否有团购验券
                List<GrouponDO> grouponList = grouponMpService.listByOrderGuid(orderGuid);
                if (!CollectionUtils.isEmpty(grouponList)) {
                    throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
                }
            }
        }
    }

    /**
     * 保存第三方活动使用记录
     */
    private void saveThirdActivityRecord(List<ThirdActivityRecordDTO> thirdActivityRecordList) {
        List<ThirdActivityRecordDO> thirdActivityRecordDOList =
                thirdActivityTransform.thirdActRecDTOList2ThirdActRecDOList(thirdActivityRecordList);
        Map<String, List<String>> codesMap = thirdActivityRecordList.stream()
                .collect(Collectors.toMap(ThirdActivityRecordDTO::getActivityGuid, ThirdActivityRecordDTO::getThirdActivityCodeList));
        Iterator<ThirdActivityRecordDO> iterator = thirdActivityRecordDOList.iterator();
        while (iterator.hasNext()) {
            ThirdActivityRecordDO rec = iterator.next();
            boolean isFilter = (RuleTypeEnum.PAY_DISCOUNT.getCode() == rec.getRuleType()
                    && BigDecimalUtil.equelZero(rec.getJoinFee()) && BigDecimalUtil.equelZero(rec.getNotJoinFee()))
                    || (RuleTypeEnum.AMOUNT_DEDUCTION.getCode() == rec.getRuleType() && CollectionUtils.isEmpty(rec.getThirdActivityCodeList()));
            if (isFilter) {
                iterator.remove();
                continue;
            }
            rec.setGuid(String.valueOf(dynamicHelper.generateGuid(GuidKeyConstant.HST_THIRD_ACTIVITY_RECORD)));
            List<String> codeList = codesMap.get(rec.getActivityGuid());
            String codesStr = "";
            if (!CollectionUtils.isEmpty(codeList)) {
                codesStr = codeList.stream().map(String::valueOf).collect(Collectors.joining(","));
            }
            rec.setThirdActivityCodes(codesStr);
        }
        if (CollectionUtils.isEmpty(thirdActivityRecordDOList)) {
            return;
        }
        saveBatch(thirdActivityRecordDOList);
    }

    @Override
    public void revokeThirdActivityRecord(String orderGuid, String activityGuid) {
        remove(new LambdaQueryWrapper<ThirdActivityRecordDO>()
                .eq(ThirdActivityRecordDO::getOrderGuid, orderGuid)
                .eq(ThirdActivityRecordDO::getActivityGuid, activityGuid));
    }

    @Override
    public List<ThirdActivityRecordDO> listByOrderGuids(List<String> orderGuids) {
        if (CollectionUtils.isEmpty(orderGuids)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<ThirdActivityRecordDO>()
                .in(ThirdActivityRecordDO::getOrderGuid, orderGuids));
    }

    @Override
    public void revokeThirdActivityRecord(List<String> orderGuids) {
        remove(new LambdaQueryWrapper<ThirdActivityRecordDO>()
                .in(ThirdActivityRecordDO::getOrderGuid, orderGuids));
    }

    @Override
    public void revokeThirdActivityRecordByThirdType(String orderGuid, String thirdType) {
        // 查询活动
        List<ThirdActivityRecordDTO> thirdActivityRecordList = listThirdActivityByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(thirdActivityRecordList)) {
            return;
        }
        List<ThirdActivityRecordDTO> thirdActivityRecordByThirdTypeList = thirdActivityRecordList.stream()
                .filter(e -> Objects.nonNull(e.getThirdType()) && e.getThirdType().equals(thirdType))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(thirdActivityRecordByThirdTypeList)) {
            return;
        }
        List<String> activityGuids = thirdActivityRecordByThirdTypeList.stream().map(ThirdActivityRecordDTO::getActivityGuid).collect(Collectors.toList());
        remove(new LambdaQueryWrapper<ThirdActivityRecordDO>()
                .eq(ThirdActivityRecordDO::getOrderGuid, orderGuid)
                .in(ThirdActivityRecordDO::getActivityGuid, activityGuids));
    }

    /**
     * 查询订单下使用的活动
     *
     * @param orderGuid 订单guid
     * @return 订单下使用的活动
     */
    @Override
    public List<ThirdActivityRecordDTO> listThirdActivityByOrderGuid(String orderGuid) {
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException("订单guid不能为空");
        }
        List<ThirdActivityRecordDO> recordDOList = this.list(new LambdaQueryWrapper<ThirdActivityRecordDO>()
                .eq(ThirdActivityRecordDO::getOrderGuid, orderGuid)
                .eq(ThirdActivityRecordDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        if (CollectionUtils.isEmpty(recordDOList)) {
            log.warn("未查询到订单使用的活动记录 orderGuid={}", orderGuid);
            return Lists.newArrayList();
        }
        Map<String, String> codeMap = recordDOList.stream()
                .collect(Collectors.toMap(ThirdActivityRecordDO::getGuid, ThirdActivityRecordDO::getThirdActivityCodes));
        List<ThirdActivityRecordDTO> responseList = thirdActivityTransform.thirdActRecDOList2ThirdActRecDTOList(recordDOList);
        responseList.forEach(res -> {
            String codes = codeMap.get(res.getGuid());
            if (!StringUtils.isEmpty(codes)) {
                res.setThirdActivityCodeList(Arrays.asList(codes.split(",")));
            }
        });
        // 查询活动规则
        Set<String> activityIds = recordDOList.stream().map(ThirdActivityRecordDO::getActivityGuid).collect(Collectors.toSet());
        List<ThirdActivityRespDTO> activities = thirdActivityClientService.listByGuid(new ArrayList<>(activityIds));
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(activities)) {
            Map<String, ThirdActivityRespDTO> activityMap = activities.stream()
                    .collect(Collectors.toMap(ThirdActivityRespDTO::getGuid, Function.identity(), (key1, key2) -> key2));
            for (ThirdActivityRecordDTO recordDTO : responseList) {
                ThirdActivityRespDTO activityResp = activityMap.get(recordDTO.getActivityGuid());
                if (Objects.nonNull(activityResp)) {
                    recordDTO.setThirdType(activityResp.getThirdType());
                    recordDTO.setIsActivityShare(activityResp.getIsActivityShare());
                    recordDTO.setIsThirdShare(activityResp.getIsThirdShare());
                    recordDTO.setUseLimit(activityResp.getUseLimit());
                }
            }
        }
        return responseList;
    }

    @Override
    public void saveRecord(GrouponReqDTO grouponReqDTO) {
        log.info("团购验券保存第三方活动使用记录入参:{}", JacksonUtils.writeValueAsString(grouponReqDTO));
        if (StringUtils.isEmpty(grouponReqDTO.getActivityGuid())) {
            return;
        }
        String activityGuid = grouponReqDTO.getActivityGuid();
        String orderGuid = grouponReqDTO.getOrderGuid();
        List<String> couponCodes = Lists.newArrayList(grouponReqDTO.getCouponCode());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(grouponReqDTO.getCouponCodeList())) {
            couponCodes = grouponReqDTO.getCouponCodeList();
        }
        // 查询第三方活动
        List<ThirdActivityRespDTO> activityInfo = thirdActivityClientService.listByGuid(Lists.newArrayList(activityGuid));
        if (CollectionUtils.isEmpty(activityInfo)) {
            log.error("第三方活动不存在,activityGuid:{}", activityGuid);
            return;
        }
        BigDecimal costFee = BigDecimalUtil.nonNullValue(grouponReqDTO.getCouponBuyPrice()).multiply(BigDecimal.valueOf(couponCodes.size()));
        BigDecimal couponFee = BigDecimalUtil.nonNullValue(grouponReqDTO.getDeductionAmount()).multiply(BigDecimal.valueOf(couponCodes.size()));
        // 保存第三方活动
        ThirdActivityRecordDO activityRecordDO = this.getOne(new LambdaQueryWrapper<ThirdActivityRecordDO>()
                .eq(ThirdActivityRecordDO::getActivityGuid, activityGuid)
                .eq(ThirdActivityRecordDO::getOrderGuid, orderGuid));
        if (ObjectUtils.isEmpty(activityRecordDO)) {
            activityRecordDO = new ThirdActivityRecordDO();
            activityRecordDO.setGuid(String.valueOf(dynamicHelper.generateGuid(GuidKeyConstant.HST_THIRD_ACTIVITY_RECORD)));
            activityRecordDO.setOrderGuid(orderGuid);
            activityRecordDO.setActivityGuid(activityGuid);
            activityRecordDO.setRuleType(RuleTypeEnum.AMOUNT_DEDUCTION.getCode());
            activityRecordDO.setThirdActivityCodes(couponCodes.stream().map(String::valueOf).collect(Collectors.joining(",")));
            activityRecordDO.setJoinFee(BigDecimal.ZERO);
            activityRecordDO.setNotJoinFee(BigDecimal.ZERO);
            activityRecordDO.setCostFee(costFee);
            activityRecordDO.setCouponFee(couponFee);
        } else {
            if (!StringUtils.isEmpty(activityRecordDO.getThirdActivityCodes())) {
                List<String> codeList = new ArrayList<>(Arrays.asList(activityRecordDO.getThirdActivityCodes().split(",")));
                couponCodes.addAll(codeList);
            }
            activityRecordDO.setCostFee(costFee);
            activityRecordDO.setCouponFee(couponFee);
            activityRecordDO.setThirdActivityCodes(couponCodes.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        saveOrUpdate(activityRecordDO);
    }

    @Override
    public void checkRecord(GrouponReqDTO grouponReqDTO) {
        String orderGuid = grouponReqDTO.getOrderGuid();
        // 查询第三方活动使用记录
        List<ThirdActivityRecordDTO> thirdActivityRecords = listThirdActivityByOrderGuid(orderGuid);
        List<ThirdActivityRecordDTO> unShareList = thirdActivityRecords.stream()
                .filter(e -> e.getIsThirdShare() == 0).collect(Collectors.toList());
        if (StringUtils.isEmpty(grouponReqDTO.getActivityGuid())) {
            if (!CollectionUtils.isEmpty(unShareList)) {
                throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
            }
            return;
        }
        List<String> couponCodes = mergeCouponCodes(grouponReqDTO);
        // 查询第三方活动
        String activityGuid = grouponReqDTO.getActivityGuid();
        ThirdActivityRecordDO activityRecordDO = this.getOne(new LambdaQueryWrapper<ThirdActivityRecordDO>()
                .eq(ThirdActivityRecordDO::getActivityGuid, activityGuid)
                .eq(ThirdActivityRecordDO::getOrderGuid, orderGuid));
        if (!ObjectUtils.isEmpty(activityRecordDO)) {
            List<String> codeList = new ArrayList<>(Arrays.asList(activityRecordDO.getThirdActivityCodes().split(",")));
            couponCodes.addAll(codeList);
        }
        // 参数校验
        ThirdActivityRecordDTO recordDTO = new ThirdActivityRecordDTO();
        recordDTO.setOrderGuid(orderGuid);
        recordDTO.setActivityGuid(activityGuid);
        recordDTO.setRuleType(RuleTypeEnum.AMOUNT_DEDUCTION.getCode());
        recordDTO.setThirdActivityCodeList(couponCodes);
        recordDTO.setJoinFee(BigDecimal.ZERO);
        checkThirdActivityRule(recordDTO, thirdActivityRecords);
    }

    private List<String> mergeCouponCodes(GrouponReqDTO grouponReqDTO) {
        List<String> couponCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(grouponReqDTO.getCouponCodeList())) {
            grouponReqDTO.setCount(grouponReqDTO.getCouponCodeList().size());
            couponCodes.addAll(grouponReqDTO.getCouponCodeList());
        }
        return couponCodes;
    }

    @Override
    public List<GrouponListRespDTO> grouponCodesByActivityGuid(String orderGuid, String activityGuid) {
        // 查询第三方活动使用明细
        List<ThirdActivityRecordDTO> thirdActivityRecords = listThirdActivityByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(thirdActivityRecords)) {
            return Lists.newArrayList();
        }
        // 过滤该活动
        ThirdActivityRecordDTO thirdActivityRecord = thirdActivityRecords.stream()
                .filter(e -> e.getActivityGuid().equals(activityGuid))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(thirdActivityRecord) || CollectionUtils.isEmpty(thirdActivityRecord.getThirdActivityCodeList())) {
            return Lists.newArrayList();
        }
        List<String> thirdActivityCodeList = thirdActivityRecord.getThirdActivityCodeList();
        // 查询groupon表
        List<GrouponDO> grouponList = grouponMpService.listByCodes(orderGuid, thirdActivityCodeList);
        return orderTransform.grouponDOS2GrouponListRespDTOS(grouponList);
    }

    @Override
    public void updateOrderGuid(String orderGuid, String newOrderGuid) {
        UpdateWrapper<ThirdActivityRecordDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(ThirdActivityRecordDO::getOrderGuid, orderGuid);
        uw.lambda().set(ThirdActivityRecordDO::getOrderGuid, newOrderGuid);
        update(uw);
    }
}
