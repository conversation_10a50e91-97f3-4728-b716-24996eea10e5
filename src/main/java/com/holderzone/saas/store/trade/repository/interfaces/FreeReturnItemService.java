package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.FreeReturnItemDO;
import com.holderzone.saas.store.trade.entity.enums.FreeReturnTypeEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 赠送/退货记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface FreeReturnItemService extends IService<FreeReturnItemDO> {

    List<FreeReturnItemDO> listFreeByOrderGuid(String orderGuid);

    List<FreeReturnItemDO> listByOrderGuid(String orderGuid);

    List<FreeReturnItemDO> listByOrderGuids(List<String> orderGuid);

    void deleteByOrderGuids(List<String> orderGuids);

    void removeByItemGuids(ArrayList<Long> orderItemGuids);

    List<FreeReturnItemDO> listByItemGuids(List<Long> orderItemGuids, FreeReturnTypeEnum freeReturnTypeEnum);

    List<FreeReturnItemDO> listByGuids(List<Long> guids);

    void updateRefundCountByGuid(Long guid, BigDecimal refundCount);
}
