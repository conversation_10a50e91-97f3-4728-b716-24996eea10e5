package com.holderzone.saas.store.trade.service.inner.impls;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.trade.entity.domain.FreeReturnItemDO;
import com.holderzone.saas.store.trade.entity.domain.ItemAttrDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;
import com.holderzone.saas.store.trade.repository.interfaces.FreeReturnItemService;
import com.holderzone.saas.store.trade.repository.interfaces.ItemAttrService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemService;
import com.holderzone.saas.store.trade.service.inner.interfaces.ItemService;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.caculate.ItemCaculatUtils;
import lombok.AllArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemServiceImpl
 * @date 2019/12/12 11:42
 * @description //TODO
 * @program IdeaProjects
 */
@Service
@AllArgsConstructor
public class ItemServiceImpl implements ItemService {

    private final OrderItemService orderItemService;

    private final ItemAttrService itemAttrService;

    private final FreeReturnItemService freeReturnItemService;

    @Override
    public Pair<List<DineInItemDTO>, List<ReturnItemDTO>> getItemDTOSAndReturnItemDTOS(String orderGuid) {
        //查ITEM表
        List<OrderItemDO> orderItemDOS = orderItemService.listByOrderGuidWithCache(Long.valueOf(orderGuid), null);
        //数据库查Attr
        List<ItemAttrDO> itemAttrDOS = itemAttrService.listByItemGuids(orderItemDOS.stream()
                .filter(orderItemDO -> orderItemDO.getHasAttr() != null && orderItemDO.getHasAttr() == 1)
                .map(OrderItemDO::getGuid)
                .collect(Collectors.toList()));
        //数据库查赠送和退货
        List<FreeReturnItemDO> freeReturnItemDOS = freeReturnItemService.listByItemGuids(orderItemDOS.stream()
                .filter(orderItemDO -> BigDecimalUtil.greaterThanZero(orderItemDO.getFreeCount()) || BigDecimalUtil.greaterThanZero(orderItemDO.getReturnCount()))
                .map(OrderItemDO::getGuid).collect(Collectors.toList()), null);
        //主要信息
        List<DineInItemDTO> dineInItemDTOS = ItemCaculatUtils.buildItem(orderItemDOS, itemAttrDOS, freeReturnItemDOS);
        //退货信息
        List<ReturnItemDTO> returnItemDTOS = ItemCaculatUtils.bulidReturnItem(freeReturnItemDOS, dineInItemDTOS);
        return Pair.of(dineInItemDTOS, returnItemDTOS);
    }

}