package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.trade.GrouponOrderDTO;
import com.holderzone.saas.store.trade.entity.domain.GrouponDO;
import com.holderzone.saas.store.trade.mapper.GrouponMapper;
import com.holderzone.saas.store.trade.repository.interfaces.GrouponMpService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 团购券 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-10
 */
@Service
public class GrouponMpMpServiceImpl extends ServiceImpl<GrouponMapper, GrouponDO> implements GrouponMpService {

    @Override
    public List<GrouponDO> listForGeGuid(Long guid) {
        QueryWrapper<GrouponDO> qw = new QueryWrapper<>();
        qw.lambda().ge(GrouponDO::getGuid, guid);
        qw.lambda().last("limit 3000");
        return list(qw);
    }

    @Override
    public List<GrouponDO> listByCodes(String orderGuid, List<String> codes) {
        QueryWrapper<GrouponDO> qw = new QueryWrapper<>();
        qw.lambda().eq(GrouponDO::getOrderGuid, orderGuid);
        qw.lambda().in(GrouponDO::getCode, codes);
        return list(qw);
    }

    @Override
    public List<GrouponOrderDTO> useGrouponTotalAmountByOrderGuids(List<String> orderGuids) {
        return baseMapper.useGrouponTotalAmountByOrderGuids(orderGuids);
    }

    @Override
    public GrouponDO getByCodeAndOrderGuid(String couponCode, String orderGuid) {
        return getOne(new LambdaQueryWrapper<GrouponDO>().eq(GrouponDO::getCode, couponCode).eq
                (GrouponDO::getOrderGuid, orderGuid));
    }

    @Override
    public List<GrouponDO> listByCodeAndOrderGuid(String couponCode, String orderGuid) {
        return list(new LambdaQueryWrapper<GrouponDO>()
                .eq(GrouponDO::getCode, couponCode)
                .eq(GrouponDO::getOrderGuid, orderGuid));
    }

    @Override
    public void appendRefundOrderGuid(Long orderGuid, Long refundOrderGuid) {
        UpdateWrapper<GrouponDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(GrouponDO::getOrderGuid, orderGuid);
        uw.lambda().set(GrouponDO::getRefundOrderGuid, refundOrderGuid);
        update(uw);
    }

    @Override
    public List<GrouponDO> listByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<GrouponDO>().eq(GrouponDO::getOrderGuid, orderGuid));
    }

    @Override
    public List<GrouponDO> listByOrderGuids(List<String> orderGuids) {
        if (CollectionUtils.isEmpty(orderGuids)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<GrouponDO>().in(GrouponDO::getOrderGuid, orderGuids));
    }

    @Override
    public List<GrouponDO> listByRefundOrderGuid(String refundOrderGuid) {
        return list(new LambdaQueryWrapper<GrouponDO>().eq(GrouponDO::getRefundOrderGuid, refundOrderGuid));
    }

    @Override
    public List<AmountItemDTO> listByRequest(DailyReqDTO request) {
        return baseMapper.listByRequest(request);
    }

    @Override
    public List<AmountItemDTO> listByRequestGroupByName(DailyReqDTO request) {
        return baseMapper.listByRequestGroupByName(request);
    }

    @Override
    public List<AmountItemDTO> listRefundByRequest(StoreGatherReportReqDTO request) {
        return baseMapper.listRefundByRequest(request);
    }
}
