package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.volume.RequestVolumePageList;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.member.MemberCouponListReqDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.repository.feign.ItemClientService;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.repository.feign.ThirdActivityClientService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.IThirdActivityRecordService;
import com.holderzone.saas.store.trade.service.ItemQueryService;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CommonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberGrouponController
 * @date 2019/01/04 8:53
 * @description 团购接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/member")
@Api(tags = "会员优惠券接口")
@Slf4j
public class MemberCouponController {

    private final ItemQueryService itemQueryService;

    private final MemberTerminalClientService memberTerminalClientService;

    private final OrderService orderService;

    private final ItemClientService itemClientService;

    private final IThirdActivityRecordService thirdActivityRecordService;

    private final ThirdActivityClientService thirdActivityClientService;

    @Autowired
    public MemberCouponController(ItemQueryService itemQueryService, OrderService orderService,
                                  MemberTerminalClientService memberTerminalClientService,
                                  ItemClientService itemClientService,
                                  IThirdActivityRecordService thirdActivityRecordService,
                                  ThirdActivityClientService thirdActivityClientService) {
        this.itemQueryService = itemQueryService;
        this.orderService = orderService;
        this.memberTerminalClientService = memberTerminalClientService;
        this.itemClientService = itemClientService;
        this.thirdActivityRecordService = thirdActivityRecordService;
        this.thirdActivityClientService = thirdActivityClientService;
    }

    @ApiOperation(value = "已登陆会员优惠券列表", notes = "已登陆会员优惠券列表")
    @PostMapping("/coupon_list")
    public List<ResponseVolumeList> verify(@RequestBody MemberCouponListReqDTO memberCouponListReqDTO) {
        log.info("已登陆会员优惠券列表 memberCouponListReqDTO={}", JacksonUtils.writeValueAsString(memberCouponListReqDTO));
        String orderGuid = memberCouponListReqDTO.getOrderGuid();
        OrderDO orderDO = orderService.getById(orderGuid);
        List<DineInItemDTO> allItems = itemQueryService.getAllItems(orderDO);
        // 只让非团购套餐商品参与
        allItems.removeIf(i -> !StringUtils.isEmpty(i.getCouponCode()));
        List<RequestDishInfo> dishInfoDTOS = CommonUtil.dineInItem2DishList(allItems);
        // 查询菜谱和规格的父对象
        List<String> dishGuidList = dishInfoDTOS.stream().map(RequestDishInfo::getDishGuid).collect(Collectors.toList());
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(dishGuidList);
        List<ItemInfoRespDTO> itemInfoRespDTOList = itemClientService.selectItemInfoList(listDTO);
        Map<String, ItemInfoRespDTO> itemInfoRespDTOMap = itemInfoRespDTOList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, a -> a, (k1, k2) -> k1));
        for (RequestDishInfo dishInfo : dishInfoDTOS) {
            String dishGuid = dishInfo.getDishGuid();
            String dishSpecification = dishInfo.getDishSpecification();
            ItemInfoRespDTO itemInfoRespDTO = itemInfoRespDTOMap.get(dishGuid);
            if(itemInfoRespDTO == null){
                continue;
            }
            dishInfo.setParentDishGuid(itemInfoRespDTO.getParentGuid());

            List<SkuInfoRespDTO> skuList = itemInfoRespDTO.getSkuList();
            for (SkuInfoRespDTO skuInfoRespDTO : skuList) {
                String skuGuid = skuInfoRespDTO.getSkuGuid();
                if (dishSpecification.equals(skuGuid)) {
                    dishInfo.setParentDishSpecification(skuInfoRespDTO.getParentGuid());
                    break;
                }
            }
        }

        // 处理第三方活动
        dealWithThirdActivity(orderGuid, dishInfoDTOS);

        RequestVolumePageList volumeListReqDTO = new RequestVolumePageList();
        volumeListReqDTO.setMemberInfoGuid(memberCouponListReqDTO.getMemberInfoGuid());
        volumeListReqDTO.setMemberInfoCardGuid(memberCouponListReqDTO.getMemberInfoCardGuid());
        volumeListReqDTO.setRequestDishInfoList(dishInfoDTOS);
        volumeListReqDTO.setVolumeCodes(memberCouponListReqDTO.getVolumeCodes());
        volumeListReqDTO.setMemberConsumptionGuid(orderDO.getMemberConsumptionGuid());
        volumeListReqDTO.setOrderGuid(memberCouponListReqDTO.getOrderGuid());
        volumeListReqDTO.setPageNo(1);
        volumeListReqDTO.setPageSize(1000);
        log.info("已登陆会员优惠券列表,入参 volumeListReqDTO={}", JacksonUtils.writeValueAsString(volumeListReqDTO));
        List<ResponseVolumeList> volumeListRespDTOS = memberTerminalClientService.queryVolumePage(volumeListReqDTO);
        log.info("已登陆会员优惠券列表,返回 volumeListRespDTOS={}", JacksonUtils.writeValueAsString(volumeListRespDTOS));
        if (CollectionUtils.isEmpty(allItems)) {
            volumeListRespDTOS.forEach(v -> v.setUseable(false));
        }
        return volumeListRespDTOS;
    }

    /**
     * 处理第三方活动
     *
     * @param orderGuid    订单guid
     * @param dishInfoDTOS 请求的商品信息
     */
    private void dealWithThirdActivity(String orderGuid, List<RequestDishInfo> dishInfoDTOS) {
        // 第三方活动存在时按比例扣减掉对应金额在商品里
        List<ThirdActivityRecordDTO> recordList = thirdActivityRecordService.listThirdActivityByOrderGuid(orderGuid);
        log.info("优惠券查询订单下使用的活动结果 recordList={}", JacksonUtils.writeValueAsString(recordList));
        if (!CollectionUtils.isEmpty(recordList)) {
            List<String> guidList = recordList.stream()
                    .map(ThirdActivityRecordDTO::getActivityGuid)
                    .collect(Collectors.toList());
            List<ThirdActivityRespDTO> thirdActivityList = thirdActivityClientService.inProcessListByGuid(guidList);
            log.info("优惠券查询可用活动列表 thirdActivityList={}", JacksonUtils.writeValueAsString(thirdActivityList));
            if (!CollectionUtils.isEmpty(thirdActivityList)) {
                Map<String, ThirdActivityRespDTO> activityMap = thirdActivityList.stream()
                        .collect(Collectors.toMap(ThirdActivityRespDTO::getGuid, Function.identity(), (dto1, dto2) -> dto2));
                for (ThirdActivityRecordDTO rec : recordList) {
                    ThirdActivityRespDTO thirdActivityDTO = activityMap.get(rec.getActivityGuid());
                    AmountCalculationUtil.handleActivityDeductionFee(rec, thirdActivityDTO);
                }
            }
        }

        BigDecimal discountFee = recordList.stream()
                .map(ThirdActivityRecordDTO::getDeductionFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (BigDecimalUtil.greaterThanZero(discountFee)) {
            BigDecimal subtotalPrice = dishInfoDTOS.stream()
                    .map(RequestDishInfo::getSubtotal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal payPricePrice = dishInfoDTOS.stream()
                    .map(RequestDishInfo::getPayPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 定义最后一个商品的优惠值，每次计算一个商品的优惠值，从总优惠中减去
            BigDecimal subLastReduceAmount = discountFee;
            BigDecimal payLastReduceAmount = discountFee;
            // 将优惠金额按商品价格占总价比例分摊
            for (int i = 0; i < dishInfoDTOS.size(); i++) {
                RequestDishInfo dishInfo = dishInfoDTOS.get(i);
                BigDecimal payDiscountMoney;
                if (i == dishInfoDTOS.size() - 1) {
                    // 设置最后一个的优惠值
                    dishInfo.setSubtotal(dishInfo.getSubtotal().subtract(subLastReduceAmount));
                    dishInfo.setPayPrice(dishInfo.getPayPrice().subtract(payLastReduceAmount));
                    payDiscountMoney = payLastReduceAmount;
                } else {
                    // 单个商品优惠金额
                    BigDecimal subDiscountMoney = BigDecimalUtil.multiply2(discountFee, dishInfo.getSubtotal().
                            divide(subtotalPrice, 2, BigDecimal.ROUND_HALF_DOWN));
                    dishInfo.setSubtotal(dishInfo.getSubtotal().subtract(subDiscountMoney));
                    subLastReduceAmount = subLastReduceAmount.subtract(subDiscountMoney);
                    payDiscountMoney = BigDecimalUtil.multiply2(discountFee, dishInfo.getPayPrice().
                            divide(payPricePrice, 2, BigDecimal.ROUND_HALF_DOWN));
                    dishInfo.setPayPrice(dishInfo.getPayPrice().subtract(payDiscountMoney));
                    payLastReduceAmount = payLastReduceAmount.subtract(payDiscountMoney);

                }
                if (ObjectUtils.isEmpty(dishInfo.getDishMemberPrice())) {
                    continue;
                }
                // 会员价优惠金额
                BigDecimal dishNum = dishInfo.getDishNum().subtract(dishInfo.getGiftDishNum()).compareTo(BigDecimal.ZERO)
                        > 0 ? dishInfo.getDishNum().subtract(dishInfo.getGiftDishNum()) : BigDecimal.ZERO;
                BigDecimal gapFee = dishInfo.getDishOriginalUnitPrice().subtract(dishInfo.getDishMemberPrice())
                        .multiply(dishNum);
                if (dishInfo.getPayPrice().compareTo(gapFee) > 0) {
                    dishInfo.setDishMemberPrice(dishInfo.getPayPrice().subtract(gapFee));
                } else {
                    dishInfo.setDishMemberPrice(BigDecimal.ZERO);
                }
            }
        } else {
            dishInfoDTOS.forEach(dish -> {
                if (ObjectUtils.isEmpty(dish.getDishMemberPrice())) {
                    return;
                }
                BigDecimal dishNum = dish.getDishNum().subtract(dish.getGiftDishNum()).compareTo(BigDecimal.ZERO) > 0 ?
                        dish.getDishNum().subtract(dish.getGiftDishNum()) : BigDecimal.ZERO;
                dish.setDishMemberPrice(dish.getDishMemberPrice().multiply(dishNum));
            });
        }
    }

}
