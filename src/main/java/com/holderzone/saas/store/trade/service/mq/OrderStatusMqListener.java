package com.holderzone.saas.store.trade.service.mq;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.trade.config.OverallConfig;
import com.holderzone.saas.store.trade.config.RocketMqConfig;
import com.holderzone.saas.store.trade.utils.HttpsClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/09/18 20:47
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.ORDER_STATUS_CHANGE_MQ_TOPIC,
        tags = RocketMqConfig.ORDER_STATUS_CHANGE__MQ_TAG,
        consumerGroup = RocketMqConfig.ORDER_STATUS_CHANGE_MQ_GROUP)
@RefreshScope
public class OrderStatusMqListener extends AbstractRocketMqConsumer<RocketMqTopic, OrderDetailPushMqDTO> {

    @Autowired
    private OverallConfig overallConfig;

    @Value("${member.center.pushOrderUrl}")
    private String memberPushOrderUrl;

    @Override
    public boolean consumeMsg(OrderDetailPushMqDTO tableStatusChangeDTO, MessageExt messageExt) {
        try {
            log.info("已结账订单消费Mq，{}", JacksonUtils.writeValueAsString(tableStatusChangeDTO));
            // 赚餐推送订单
            String zcResponse = HttpsClientUtils.doPostJSON(overallConfig.getPushOrderDetailUrl(), tableStatusChangeDTO);
            log.info("订单推送赚餐返回:{}", zcResponse);
            if (StringUtils.isEmpty(zcResponse)) {
                log.error("订单推送赚餐返回失败");
                throw new BusinessException("已结账订单推送错误");
            }
        } catch (Exception e) {
            log.error("已结账订单消费Mq,消息消费异常！", e);
            return false;
        }
        return true;
    }
}
