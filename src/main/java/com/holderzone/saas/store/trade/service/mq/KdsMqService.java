package com.holderzone.saas.store.trade.service.mq;

import cn.hutool.core.collection.CollUtil;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.trade.config.RocketMqConfig;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @className KdsMqClientService
 * @date 2019/08/08 11:10
 * @description 调用kds
 * @program holder-saas-store-trade
 */
public interface KdsMqService {

    void prepare(ItemPrepareReqDTO itemPrepareReqDTO);

    /**
     * 更换菜品
     */
    void changes(ItemChangesReqDTO itemChangesReqDTO);

    void call(ItemCallUpReqDTO itemCallUpReqDTO);

    void urge(ItemUrgeReqDTO itemUrgeReqDTO);

    void remark(OrderRemarkReqDTO orderRemarkReqDTO);

    void changeTable(OrderTableReqDTO orderTableReqDTO);

    void refund(ItemBatchRefundReqDTO itemBatchRefundReqDTO);

    void transferItem(KdsItemTransferReqDTO transferReqDTO);

    @Component
    class KdsMqServiceImpl implements KdsMqService {
        private static final Logger log = LoggerFactory.getLogger(KdsMqService.class);

        public static final String USER_INFO = "userInfo";

        private final DefaultRocketMqProducer defaultRocketMqProducer;

        @Autowired
        public KdsMqServiceImpl(DefaultRocketMqProducer defaultRocketMqProducer) {
            this.defaultRocketMqProducer = defaultRocketMqProducer;
        }

        @Override
        public void prepare(ItemPrepareReqDTO itemPrepareReqDTO) {
            if (log.isInfoEnabled()) {
                log.info("菜品入厨房入参Mq：{}", JacksonUtils.writeValueAsString(itemPrepareReqDTO));
            }
            Message message = new Message();
            message.setTopic(RocketMqConfig.KDS_MESSAGE_TOPIC);
            message.setTags(RocketMqConfig.KDS_PREPARE_TAG);
            message.putUserProperty(USER_INFO, UserContextUtils.getJsonStr());
            message.setBody(JacksonUtils.writeValueAsString(itemPrepareReqDTO).getBytes());
            defaultRocketMqProducer.sendMessage(message);
        }

        @Override
        public void changes(ItemChangesReqDTO itemChangesReqDTO) {
            if (log.isInfoEnabled()) {
                log.info("菜品更换到kds入参Mq：{}", JacksonUtils.writeValueAsString(itemChangesReqDTO));
            }
            Message message = new Message();
            message.setTopic(RocketMqConfig.KDS_MESSAGE_TOPIC);
            message.setTags(RocketMqConfig.KDS_CHANGES_TAG);
            message.putUserProperty(USER_INFO, UserContextUtils.getJsonStr());
            message.setBody(JacksonUtils.writeValueAsString(itemChangesReqDTO).getBytes());
            defaultRocketMqProducer.sendMessage(message);
        }

        @Override
        public void call(ItemCallUpReqDTO itemCallUpReqDTO) {
            if (log.isInfoEnabled()) {
                log.info("菜品叫起入参Mq：{}", JacksonUtils.writeValueAsString(itemCallUpReqDTO));
            }
            if (CollUtil.isEmpty(itemCallUpReqDTO.getOrderItemGuidList())) {
                log.error("叫起菜品为空");
                return;
            }
            Message message = new Message();
            message.setTopic(RocketMqConfig.KDS_MESSAGE_TOPIC);
            message.setTags(RocketMqConfig.KDS_CALL_TAG);
            message.putUserProperty(USER_INFO, UserContextUtils.getJsonStr());
            message.setBody(JacksonUtils.writeValueAsString(itemCallUpReqDTO).getBytes());

            defaultRocketMqProducer.sendMessage(message);
        }

        @Override
        public void urge(ItemUrgeReqDTO itemUrgeReqDTO) {
            if (log.isInfoEnabled()) {
                log.info("催菜入参Mq：{}", JacksonUtils.writeValueAsString(itemUrgeReqDTO));
            }

            Message message = new Message();
            message.setTopic(RocketMqConfig.KDS_MESSAGE_TOPIC);
            message.setTags(RocketMqConfig.KDS_URGE_TAG);
            message.putUserProperty(USER_INFO, UserContextUtils.getJsonStr());
            message.setBody(JacksonUtils.writeValueAsString(itemUrgeReqDTO).getBytes());

            defaultRocketMqProducer.sendMessage(message);
        }

        @Override
        public void remark(OrderRemarkReqDTO orderRemarkReqDTO) {
            if (log.isInfoEnabled()) {
                log.info("修改备注入参Mq：{}", JacksonUtils.writeValueAsString(orderRemarkReqDTO));
            }

            Message message = new Message();
            message.setTopic(RocketMqConfig.KDS_MESSAGE_TOPIC);
            message.setTags(RocketMqConfig.KDS_REMARK_TAG);
            message.putUserProperty(USER_INFO, UserContextUtils.getJsonStr());
            message.setBody(JacksonUtils.writeValueAsString(orderRemarkReqDTO).getBytes());

            defaultRocketMqProducer.sendMessage(message);
        }

        @Override
        public void changeTable(OrderTableReqDTO orderTableReqDTO) {
            if (log.isInfoEnabled()) {
                log.info("换台入参Mq：{}", JacksonUtils.writeValueAsString(orderTableReqDTO));
            }
            Message message = new Message();
            message.setTopic(RocketMqConfig.KDS_MESSAGE_TOPIC);
            message.setTags(RocketMqConfig.KDS_CHANGE_TABLE_TAG);
            message.putUserProperty(USER_INFO, UserContextUtils.getJsonStr());
            message.setBody(JacksonUtils.writeValueAsString(orderTableReqDTO).getBytes());

            defaultRocketMqProducer.sendMessage(message);
        }

        @Override
        public void refund(ItemBatchRefundReqDTO itemBatchRefundReqDTO) {
            if (log.isInfoEnabled()) {
                log.info("退菜入参Mq：{}", JacksonUtils.writeValueAsString(itemBatchRefundReqDTO));
            }

            Message message = new Message();
            message.setTopic(RocketMqConfig.KDS_MESSAGE_TOPIC);
            message.setTags(RocketMqConfig.KDS_REFUND_TAG);
            message.putUserProperty(USER_INFO, UserContextUtils.getJsonStr());
            message.setBody(JacksonUtils.writeValueAsString(itemBatchRefundReqDTO).getBytes());

            defaultRocketMqProducer.sendMessage(message);
        }

        @Override
        public void transferItem(KdsItemTransferReqDTO transferReqDTO) {
            if (log.isInfoEnabled()) {
                log.info("转菜处理kds入参Mq：{}", JacksonUtils.writeValueAsString(transferReqDTO));
            }
            Message message = new Message();
            message.setTopic(RocketMqConfig.KDS_MESSAGE_TOPIC);
            message.setTags(RocketMqConfig.KDS_TRANSFER_TAG);
            message.putUserProperty(USER_INFO, UserContextUtils.getJsonStr());
            message.setBody(JacksonUtils.writeValueAsString(transferReqDTO).getBytes());
            defaultRocketMqProducer.sendMessage(message);
        }

    }
}
