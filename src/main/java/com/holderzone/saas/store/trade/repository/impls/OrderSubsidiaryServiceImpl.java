package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.trade.entity.domain.OrderSubsidiaryDO;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.mapper.OrderSubsidiaryMapper;
import com.holderzone.saas.store.trade.repository.interfaces.OrderSubsidiaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
public class OrderSubsidiaryServiceImpl extends ServiceImpl<OrderSubsidiaryMapper, OrderSubsidiaryDO> implements OrderSubsidiaryService {

    private final RedisHelper redisHelper;

    private final OrderSubsidiaryMapper orderSubsidiaryMapper;

    @Autowired
    public OrderSubsidiaryServiceImpl(RedisHelper redisHelper, OrderSubsidiaryMapper orderSubsidiaryMapper) {
        this.redisHelper = redisHelper;
        this.orderSubsidiaryMapper = orderSubsidiaryMapper;
    }


}
