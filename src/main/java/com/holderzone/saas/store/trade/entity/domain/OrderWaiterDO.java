package com.holderzone.saas.store.trade.entity.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * <AUTHOR> R
 * @date 2020/11/18 17:37
 * @description 订单服务员表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_order_waiter")
public class OrderWaiterDO {
    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
    /**
     * 服务员Guid
     */
    private String waiterGuid;
    /**
     *      服务员类型
     *      CALL_WAITER(1, "喊客员"),
     *     SERVE_WAITER(2, "服务员"),
     *     PASS_DISHES_WAITER(3, "传菜员"),
     *     TIDY_WAITER(4, "收台员"),
     *     MOP_WAITER(5, "洗碗员");
     */
    private Integer waiterType;
    /**
     * 订单Guid
     */
    private String orderGuid;
    /**
     * 服务员名称
     */
    private String waiterName;
    /**
     * 服务员编号
     */
    private String waiterNo;
    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;
    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;
}
