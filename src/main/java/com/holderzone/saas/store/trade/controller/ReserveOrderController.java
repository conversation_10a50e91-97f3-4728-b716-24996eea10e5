package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.request.dinein.ReserveBatchCreateOrderReqDTO;
import com.holderzone.saas.store.trade.service.DineInService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WeChatOrderController
 * @date 2019/01/04 8:53
 * @description 微信订单接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/reserve")
@Api(description = "预定订单接口")
@Slf4j
public class ReserveOrderController {

    private final DineInService dineInService;

    @Autowired
    public ReserveOrderController(DineInService dineInService) {
        this.dineInService = dineInService;
    }

    @ApiOperation(value = "预定批量到点开台", notes = "预定批量到点开台")
    @PostMapping("/batch_create_order")
    public String batchCreateOrder(@RequestBody ReserveBatchCreateOrderReqDTO
                                           reserveBatchCreateOrderReqDTO) {
        log.info("预定批量到点开台,入参：{}", JacksonUtils.writeValueAsString(reserveBatchCreateOrderReqDTO));
        return dineInService.batchCreateOrder(reserveBatchCreateOrderReqDTO);
    }

}
