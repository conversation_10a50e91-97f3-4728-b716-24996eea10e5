package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.trade.GrouponOrderDTO;
import com.holderzone.saas.store.trade.entity.domain.GrouponDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GrouponMpService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-trade
 */
public interface GrouponService {

    List<GrouponOrderDTO> useGrouponTotalAmountByOrderGuids(List<String> orderGuids);

    List<GrouponListRespDTO> listByOrderGuid(String orderGuid, Integer grouponType);

    List<GrouponListRespDTO> verify(GrouponReqDTO grouponReqDTO);

    void revoke(GrouponReqDTO revokeReq);

    GrouponDO revokeGroupon(String couponCode, String orderGuid);

    void revokeBatchOrder(List<String> orderGuids);

    void saveBatchGroupon(List<GrouponReqDTO> grouponReqDTOList);

    void createTradeDetail(CouPonReqDTO grouponReqDTO, List<GroupVerifyDTO> results);

    void cancelTradeDetail(String orderGuid, String couponCode);

    void handleTradeDetailHistory(String enterpriseGuid, Long guid);

    List<String> hasThirdActivityOrderGuids(List<String> orderGuids);
}
