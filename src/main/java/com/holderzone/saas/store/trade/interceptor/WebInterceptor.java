package com.holderzone.saas.store.trade.interceptor;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import org.slf4j.MDC;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebInterceptor
 * @date 2018/09/10 16:26
 * @description
 * @program holder-saas-store-trade
 */
@Configuration
public class WebInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws
            Exception {
        String header = request.getHeader(USER_INFO);
        if (StringUtils.hasText(header)) {
            String userInfo = URLDecoder.decode(header, "utf-8");
            UserContext userContext = JacksonUtils.toObject(UserContext.class, userInfo);
            if (userContext != null && userContext.getStoreGuid() != null) {
                MDC.put("storeGuid", userContext.getStoreGuid() + " ");
            }
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView
            modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception
            ex) throws Exception {
    }

}
