package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import com.holderzone.saas.store.trade.anno.PerformanceCheck;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ErpClientService
 * @date 2018/08/14 14:12
 * @description //
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = ErpClientService.ResourceClientServiceFallBack.class)
public interface ErpClientService {

    /**
     * @param orderSkuDTO
     */
    @PostMapping("/inOutDocument/reduceStockForOrder")
    @PerformanceCheck
    void reduceStockForOrder(@RequestBody OrderSkuDTO orderSkuDTO);

    /**
     * @param orderSkuDTOList
     */
    @PostMapping("/inOutDocument/reduceStockForOrderBatch")
    @PerformanceCheck
    void reduceStockForOrderList(@RequestBody List<OrderSkuDTO> orderSkuDTOList);

    @Component
    class ResourceClientServiceFallBack implements FallbackFactory<ErpClientService> {

        private static final Logger logger = LoggerFactory.getLogger(ResourceClientServiceFallBack.class);

        @Override
        public ErpClientService create(Throwable throwable) {

            return new ErpClientService() {
                @Override
                public void reduceStockForOrder(OrderSkuDTO orderSkuDTO) {
                    logger.error("扣减库存失败e={}", throwable.getMessage());
                    throw new ParameterException("扣减库存失败!");
                }

                @Override
                public void reduceStockForOrderList(List<OrderSkuDTO> orderSkuDTOList) {
                    logger.error("批量扣减库存失败e={}", throwable.getMessage());
                    throw new ParameterException("批量扣减库存失败!");
                }
            };
        }
    }

}
