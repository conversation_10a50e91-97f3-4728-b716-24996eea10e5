package com.holderzone.saas.store.trade.utils.local;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.saas.store.dto.trade.AppendFeeDTO;
import com.holderzone.saas.store.trade.entity.domain.AppendFeeDO;
import com.holderzone.saas.store.trade.repository.interfaces.AppendFeeMpService;
import com.holderzone.saas.store.trade.transform.LocalTransform;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class CallableForAppend implements Callable<List<AppendFeeDTO>> {

    //入参
    private List<String> orderGuidLists;

    private AppendFeeMpService appendFeeMpService;

    private LocalTransform localTransform;

    private List<AppendFeeDTO> data = new ArrayList<>();

    private final CountDownLatch latch;

    private final UserContext userContext;

    public CallableForAppend(List<String> orderGuidLists, AppendFeeMpService appendFeeMpService,
                             LocalTransform localTransform, CountDownLatch latch, UserContext userContext) {
        this.orderGuidLists = orderGuidLists;
        this.appendFeeMpService = appendFeeMpService;
        this.localTransform = localTransform;
        this.latch = latch;
        this.userContext = userContext;
    }

    @Override
    public List<AppendFeeDTO> call() throws Exception {
        try {
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            log.info("查询附加费属性入参：{}", orderGuidLists);
            List<AppendFeeDO> appendFeeLists = appendFeeMpService.listByOrderGuids(orderGuidLists);
            data = localTransform.appendDOList2DTOList(appendFeeLists);
            log.info("查询附加费结束");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            latch.countDown();
        }
        return data;
    }
}