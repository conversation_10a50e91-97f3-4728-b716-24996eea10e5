package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.domain.TransactionRecordDO;
import com.holderzone.saas.store.trade.entity.enums.TradeStateEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeTypeEnum;
import com.holderzone.saas.store.trade.mapper.TransactionRecordMapper;
import com.holderzone.saas.store.trade.repository.interfaces.TransactionRecordService;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单交易记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
public class TransactionRecordServiceImpl extends ServiceImpl<TransactionRecordMapper, TransactionRecordDO>
        implements TransactionRecordService {

    @Override
    public List<TransactionRecordDO> listJhAndPreByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<TransactionRecordDO>().eq(TransactionRecordDO::getOrderGuid, orderGuid).eq
                (TransactionRecordDO::getPaymentType, PaymentTypeEnum.AGG.getCode()));

    }

    @Override
    public List<TransactionRecordDO> listRefundByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<TransactionRecordDO>().eq(TransactionRecordDO::getOrderGuid, orderGuid).eq
                (TransactionRecordDO::getPaymentType, PaymentTypeEnum.AGG.getCode()).eq
                (TransactionRecordDO::getTradeType, TradeTypeEnum.GENERAL_OUT.getCode()));
    }

    @Override
    public List<TransactionRecordDO> listGeneralByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<TransactionRecordDO>().eq(TransactionRecordDO::getOrderGuid, orderGuid).eq
                (TransactionRecordDO::getTradeType, TradeTypeEnum.GENERAL_IN.getCode()));
    }

    @Override
    public List<TransactionRecordDO> listByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<TransactionRecordDO>().eq(TransactionRecordDO::getOrderGuid, orderGuid)
                .eq(TransactionRecordDO::getState, TradeStateEnum.SUCCESS.getCode())
        );
    }

    @Override
    public List<TransactionRecordDO> listByOrderGuids(List<Long> orderGuids) {
        if (CollectionUtil.isEmpty(orderGuids)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<TransactionRecordDO>().in(TransactionRecordDO::getOrderGuid, orderGuids).eq
                (TransactionRecordDO::getState, TradeStateEnum.SUCCESS.getCode()));
    }

    @Override
    public List<TransactionRecordDO> listByOrderGuid(Long orderGuid){
        return list(new LambdaQueryWrapper<TransactionRecordDO>().eq(TransactionRecordDO::getOrderGuid,orderGuid));
    }


    @Override
    public void deleteByOrderGuids(List<String> orderGuids) {
        if (CollectionUtil.isEmpty(orderGuids)) {
            return;
        }
        remove(new LambdaQueryWrapper<TransactionRecordDO>().in(TransactionRecordDO::getOrderGuid, orderGuids));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateByGuid(String orderGuid, List<TransactionRecordDO> saveList) {
        //先根据guid查询交易记录
        List<Integer> includePaymentTypes = Lists.newArrayList(PaymentTypeEnum.AGG.getCode(), PaymentTypeEnum.MEMBER.getCode(),
                PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode(), PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode(),
                PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode());
        List<TransactionRecordDO> transactionRecordList = list(new LambdaQueryWrapper<TransactionRecordDO>()
                .eq(TransactionRecordDO::getOrderGuid, orderGuid)
                .in(TransactionRecordDO::getPaymentType, includePaymentTypes)
                .eq(TransactionRecordDO::getState, StateEnum.SUCCESS.getCode())
                .select(TransactionRecordDO::getGuid));
        LambdaQueryWrapper<TransactionRecordDO> lambdaQueryWrapper = new LambdaQueryWrapper<TransactionRecordDO>()
                .eq(TransactionRecordDO::getOrderGuid, orderGuid);

        if (CollectionUtil.isNotEmpty(transactionRecordList)) {
            List<Long> includeGuids = transactionRecordList.stream()
                    .map(TransactionRecordDO::getGuid)
                    .collect(Collectors.toList());
            lambdaQueryWrapper.notIn(TransactionRecordDO::getGuid, includeGuids);
        }
        //删除交易记录
        remove(lambdaQueryWrapper);
        saveBatch(saveList);
    }

    @Override
    public void updateRefundAmountByOrderGuid(String orderGuid, Integer paymentType, String paymentTypeName, BigDecimal refundAmount) {
        UpdateWrapper<TransactionRecordDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(TransactionRecordDO::getOrderGuid, orderGuid);
        uw.lambda().eq(TransactionRecordDO::getState, TradeStateEnum.SUCCESS.getCode());
        uw.lambda().eq(TransactionRecordDO::getTradeType, TradeTypeEnum.GENERAL_IN.getCode());
        uw.lambda().eq(TransactionRecordDO::getPaymentType, paymentType);
        uw.lambda().eq(TransactionRecordDO::getPaymentTypeName, paymentTypeName);
        uw.lambda().setSql("refund_amount = refund_amount + " + refundAmount.toPlainString());
        uw.lambda().setSql("refundable_fee = refundable_fee - " + refundAmount.toPlainString());
        update(uw);
    }

    @Override
    public void updateRefundAmountByGuid(Long guid, BigDecimal refundAmount) {
        UpdateWrapper<TransactionRecordDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(TransactionRecordDO::getGuid, guid);
        uw.lambda().setSql("refund_amount = refund_amount + " + refundAmount.toPlainString());
        uw.lambda().setSql("refundable_fee = refundable_fee - " + refundAmount.toPlainString());
        update(uw);
    }

    @Override
    public List<TransactionRecordDO> listJhByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<TransactionRecordDO>()
                .eq(TransactionRecordDO::getOrderGuid, orderGuid)
                .eq(TransactionRecordDO::getPaymentType, PaymentTypeEnum.AGG.getCode())
                .eq(TransactionRecordDO::getState, StateEnum.SUCCESS.getCode())
        );
    }

    @Override
    public List<TransactionRecordDO> listJhByOrderGuidList(Collection<? extends Serializable> orderGuidList) {
        if (CollectionUtils.isEmpty(orderGuidList)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<TransactionRecordDO>()
                .in(TransactionRecordDO::getOrderGuid, orderGuidList)
                .eq(TransactionRecordDO::getPaymentType, PaymentTypeEnum.AGG.getCode())
                .eq(TransactionRecordDO::getTradeType, TradeTypeEnum.GENERAL_IN.getCode())
                .eq(TransactionRecordDO::getState, StateEnum.SUCCESS.getCode())
        );
    }
}
