package com.holderzone.saas.store.trade.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CommonConstant
 * @date 2018/09/04 17:19
 * @description
 * @program holder-saas-store-trade
 */
public class CommonConstant {

    public static final Integer ZERO = 0;

    public static final String AGG_SUCCESS = "10000";
    public static final String AGG_SUCCESS_TOO = "20045";

    public static final String AGG_REFUNDED = "40000";

    public static final String AGG_REFUNDED_MSG = "相应订单已全额退款";
    public static final String AGG_CALLBACK_URL = "http://holder-saas-store-trade/dine_in_bill/callback";

    public static final String AGG_REFUND_MSG_FAILURE = "失败";

    public static final String AGG_REFUND_FAILURE_EXCEPTION_MSG = "聚合支付退款失败，原因：%s";

}
