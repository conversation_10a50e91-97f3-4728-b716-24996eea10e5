package com.holderzone.saas.store.trade.utils.local;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.saas.store.dto.trade.TransactionRecordDTO;
import com.holderzone.saas.store.trade.entity.domain.TransactionRecordDO;
import com.holderzone.saas.store.trade.repository.interfaces.TransactionRecordService;
import com.holderzone.saas.store.trade.transform.LocalTransform;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class CallableForTransaction implements Callable<List<TransactionRecordDTO>> {

    //入参
    private List<Long> orderGuidList;

    private TransactionRecordService transactionRecordService;

    private LocalTransform localTransform;

    List<TransactionRecordDTO> data = new ArrayList<>();

    private final CountDownLatch latch;

    private UserContext userContext;


    public CallableForTransaction(List<Long> orderGuidList, TransactionRecordService transactionRecordService,
                                  LocalTransform localTransform, CountDownLatch latch, UserContext userContext) {
        this.orderGuidList = orderGuidList;
        this.transactionRecordService = transactionRecordService;
        this.localTransform = localTransform;
        this.latch = latch;
        this.userContext = userContext;
    }

    @Override
    public List<TransactionRecordDTO> call() throws Exception {
        try {
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            log.info("查询交易记录入参：{}", orderGuidList);
            List<TransactionRecordDO> tranLists = transactionRecordService.listByOrderGuids(orderGuidList);
            data = localTransform.transactionRecordDOS2TransactionRecordDTOS(tranLists);
            log.info("查询交易记录结束");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            latch.countDown();
        }
        return data;
    }
}
