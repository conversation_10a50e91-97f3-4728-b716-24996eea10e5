package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDetailsDO;

import java.util.List;

/**
 * 调整单明细 service
 */
public interface AdjustOrderDetailsService extends IService<AdjustOrderDetailsDO> {

    List<AdjustOrderDetailsDO> listByAdjustGuid(Long adjustGuid);

    List<AdjustOrderDetailsDO> listByOrderItemGuids(List<Long> orderItemGuids);

}
