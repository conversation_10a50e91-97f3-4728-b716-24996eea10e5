package com.holderzone.saas.store.trade.service.impl;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.trade.config.OrderLockConfig;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.OrderLockService;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.RedisKeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderLockServiceImpl
 * @date 2019/09/09 18:37
 * @description
 * @program IdeaProjects
 */
@Service
@Slf4j
public class OrderLockServiceImpl implements OrderLockService {

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderLockConfig orderLockConfig;

    private static final String LUA_SCRIPT_FOR_LOCK = " local releaseTime = tonumber(ARGV[1])  for i, key in ipairs" +
            "(KEYS)" +
            "  do" +
            "    if (redis.call('get',key)=='0')" +
            "      then" +
            "        return 0;" +
            "    end" +
            "  end " +
            "for i, key in ipairs(KEYS)" +
            "  do" +
            "   redis.call('set',key,'0','EX',releaseTime)" +
            "  end" +
            " return 1";

    private static String LUA_SCRIPT_FOR_LOCK_WITH_DIVERSID = "local value = ARGV[1]  local releaseTime = tonumber" +
            "(ARGV[2]) " +
            "  for i, key in ipairs(KEYS)" +
            "  do  local a = redis.call('get',key)" +
            "    if a then " +
            "           if a == value then return 1 else return 0 end " +
            "    end " +
            "  end " +
            " for i, key in ipairs(KEYS)" +
            "  do redis.call(\"set\",key,value,'EX',releaseTime) end " +
            " return 1";

    /**
     * check the lock
     *
     * @param orderId
     * @return
     */
    @Override
    public boolean checkOrderLock(String orderId) {
        String key = RedisKeyUtil.getHstOrderLockKey(orderId);
        return !StringUtils.isEmpty(redisHelper.get(key));
    }

    /**
     * 获取该订单锁的机器号
     *
     * @param orderId
     * @return
     */
    @Override
    public String getDeviceLockId(String orderId) {
        String key = RedisKeyUtil.getHstOrderLockKey(orderId);
        return redisHelper.get(key);
    }

    /**
     * 通过主单id找子单和其他单
     *
     * @param orderGuid
     * @return
     */
    public List<String> getAllOrderByMainOrderGuidKeys(String orderGuid) {
        String subOrderGuidString = redisHelper.get(RedisKeyUtil.getSubOrderGuidCacheKey(orderGuid));
        if (subOrderGuidString != null) {
            String[] subOrderGuidArray = subOrderGuidString.split(",");
            String[] afterBighstSubOrderGuidArray = Arrays.copyOf(subOrderGuidArray, subOrderGuidArray.length +
                    1);
//            afterBighstSubOrderGuidArray[subOrderGuidArray.length] = RedisKeyUtil.getSubOrderGuidCacheKey
//                    (orderGuid);
            afterBighstSubOrderGuidArray[subOrderGuidArray.length] = RedisKeyUtil.getHstOrderLockKey(orderGuid);
            return Arrays.asList(afterBighstSubOrderGuidArray);
        }
        List<OrderDO> subOrderGuids = orderService.listOrderIdByMainOrderGuid(orderGuid);
        if (CollectionUtil.isEmpty(subOrderGuids)) {
            return Arrays.asList(RedisKeyUtil.getHstOrderLockKey(orderGuid));
        }
        List<String> subOrderRedisKeys = subOrderGuids.stream().map(OrderDO::getGuid)
                .map(String::valueOf)
                .map(RedisKeyUtil::getHstOrderLockKey)
                .collect(Collectors.toList());
        //===================缓存一下==================//
        redisHelper.setEx(RedisKeyUtil.getSubOrderGuidCacheKey(orderGuid),
                StringUtils.join(subOrderRedisKeys, ","),
                2, TimeUnit.SECONDS);
        subOrderRedisKeys.add(RedisKeyUtil.getHstOrderLockKey(orderGuid));
        return subOrderRedisKeys;
    }

//    ================== 因为要锁子单  所以直接去掉这个 ================
//    the result such as redis_conmand : set haha 6 EX 30 NX  ,return OK when the haha has not value the time before
//    public boolean tryLock(String orderId,String timeout) {
//        String key = RedisKeyUtil.getHstOrderLockKey(orderId);
//        String result = redisHelper.execute((connection)->{
//            JedisCommands commands = (JedisCommands)connection.getNativeConnection();
//            return commands.set(key, DEFAULT_VALUE,"NX","EX", DEFAULT_TIMEOUT);
//        });
//        //need redis version at least 2.6.12
//        return "OK".equals(result);
//    }

    /**
     * 锁主单  在订单中  由于对多订单的操作都会传主单id过来 ，因此 只需要管主单
     * 后期业务变动 这个类可能要锁多个订单，多个订单  键位主单  值为子单集合  ，键为子单  值为0
     *
     * @param deviceId 机器id或者其他
     * @param orderId  主单GUID
     * @return
     */
    @Override
    public boolean tryLockwithDeviceId(String orderId, String deviceId) {
        if (deviceId == null) {
            return tryLock(orderId);
        }
        List<String> allOrderGuid = getAllOrderByMainOrderGuidKeys(orderId);
        DefaultRedisScript<Long> defaultRedisScript = new DefaultRedisScript<>();
        defaultRedisScript.setResultType(Long.class);
        defaultRedisScript.setScriptText(LUA_SCRIPT_FOR_LOCK_WITH_DIVERSID);
        List<String> strings = redisHelper.multiGet(allOrderGuid);
        log.warn("结账分布式锁，defaultRedisScript：{},allOrderGuid:{},deviceId:{},releasesTime:{},此时redis中加锁情况：{}",
                JacksonUtils.writeValueAsString(defaultRedisScript), JacksonUtils.writeValueAsString
                        (allOrderGuid), deviceId, orderLockConfig.getReleasesTime().toString(), JacksonUtils
                        .writeValueAsString(strings));
        Long execute = redisHelper.execute(defaultRedisScript, allOrderGuid, deviceId, orderLockConfig
                .getReleasesTime().toString());
        boolean equals = execute.equals(1L);
        if (!equals) {
            strings = redisHelper.multiGet(allOrderGuid);
            log.warn("结账分布式锁冲突，defaultRedisScript：{},allOrderGuid:{},deviceId:{},releasesTime:{},此时redis中加锁情况：{}",
                    JacksonUtils.writeValueAsString(defaultRedisScript), JacksonUtils.writeValueAsString
                            (allOrderGuid), deviceId, orderLockConfig.getReleasesTime().toString(), JacksonUtils
                            .writeValueAsString(strings));
        }
        return equals;
    }

    /**
     * 锁主单  在订单中  由于对多订单的操作都会传主单id过来 ，因此 只需要管主单
     * 后期业务变动 这个类可能要锁多个订单，多个订单  键位主单  值为子单集合  ，键为子单  值为0
     *
     * @param orderId 主单GUID
     * @return
     */
    @Override
    @Deprecated
    public boolean tryLock(String orderId) {
        List<String> allOrderGuid = getAllOrderByMainOrderGuidKeys(orderId);
        DefaultRedisScript<Long> defaultRedisScript = new DefaultRedisScript<>();
        defaultRedisScript.setResultType(Long.class);
        defaultRedisScript.setScriptText(LUA_SCRIPT_FOR_LOCK);
        return redisHelper.execute(defaultRedisScript, allOrderGuid, orderLockConfig.getReleasesTime().toString())
                .equals(1L);
    }

    /**
     * 释放主单锁
     *
     * @param orderId 主单GUID
     */
    @Override
    public void unlock(String orderId) {
        List<String> allOrderByMainOrderGuid = getAllOrderByMainOrderGuidKeys(orderId);
        redisHelper.delete(allOrderByMainOrderGuid);
    }

    /**
     * 释放主单锁  需要deviceId
     *
     * @param orderId 主单GUID
     */
    @Override
    public boolean unlockBydeviceId(String orderId, String deviceId) {
        if (deviceId == null) {
            unlock(orderId);
            return true;
        }
        List<String> allOrderByMainOrderGuid = getAllOrderByMainOrderGuidKeys(orderId);
        List<String> strings = redisHelper.multiGet(allOrderByMainOrderGuid);
        if (!strings.contains(deviceId)) {
            return false;
        }
        redisHelper.delete(allOrderByMainOrderGuid);
        return true;
    }

    @Override
    public List<String> getMainAndSubGuids(String orderGuid) {
        List<OrderDO> subOrderDOs = orderService.listOrderIdByMainOrderGuid(orderGuid);
        if (CollectionUtil.isEmpty(subOrderDOs)) {
            return Arrays.asList(orderGuid);
        }
        List<String> mainAndSubOrderGuids = subOrderDOs.stream().map(OrderDO::getGuid).map(String::valueOf).collect
                (Collectors.toList());
        mainAndSubOrderGuids.add(orderGuid);
        return mainAndSubOrderGuids;
    }


}