package com.holderzone.saas.store.trade.config;


import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.util.LudouSignUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 麓豆+ 接口调用拦截器
 */
@Slf4j
//@Configuration
public class LudouFeignRequestInterceptor implements RequestInterceptor {

    public static final String APP_ID = "X-AppId";

    public static final String SIGN = "X-Sign";

    public static final String TIMESTAMP = "X-Timestamp";

    @Value("${ludou.appId}")
    private String ludouAppId;

    @Value("${ludou.secret}")
    private String ludouSecret;

    @Override
    public void apply(RequestTemplate template) {
        try {
            byte[] body = template.body();
            long timestamp = System.currentTimeMillis();
            Map<String, Object> bodyObj = JacksonUtils.toMap(new String(body));
            // 参数排序拼接转字符串
            LinkedHashMap<String, Object> sortedBody = LudouSignUtil.sortMap(bodyObj);
            String sign = LudouSignUtil.buildSign(sortedBody, timestamp, ludouSecret);
            template.header(APP_ID, ludouAppId);
            template.header(SIGN, sign);
            template.header(TIMESTAMP, String.valueOf(timestamp));
            log.info("麓豆feign拦截器传递头部参数：header={}", template.headers());
        } catch (Exception e) {
            log.error("LudouFeignRequestInterceptor error:", e);
        }
    }

}
