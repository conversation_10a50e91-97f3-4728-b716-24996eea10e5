package com.holderzone.saas.store.trade.helper;


import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.trade.utils.SpringContextUtil;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @className DynamicHelper
 * @date 2018/10/25 15:28
 * @description
 * @program holder-saas-store-trade
 */
@Component
public class DynamicHelper {

    private static final Logger log = LoggerFactory.getLogger(DynamicHelper.class);

    @Value("${self.open-dynamic-datasource}")
    private Boolean openDynamicDatasource;


    public void changeDatasource(String enterpriseGuid) {
        if (openDynamicDatasource) {
            try {
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            } catch (Exception e) {
                log.error("手动切换数据源异常！enterpriseGuid:" + enterpriseGuid + ":{}", e.getMessage());
            }
        }
    }

    public void removeThreadLocalDatabaseInfo() {
        if (openDynamicDatasource) {
            EnterpriseIdentifier.remove();
        }
    }


    /**
     * 生成guid
     *
     * @param redisKey
     * @return
     */
    public Long generateGuid(String redisKey) {

        RedisTemplate redisTemplate = SpringContextUtil.getInstance().getBean("redisTemplate");
        Long guid;
        try {
            guid = BatchIdGenerator.getGuid(redisTemplate, redisKey);
        } catch (Exception e) {
            log.error("生成guid失败，e={}", e.getMessage());
            throw new ParameterException("生成guid失败，e=" + e.getMessage());
        }
        return guid;
    }


    /**
     * 批量生成guid
     *
     * @param redisKey
     * @return
     */
    public List<Long> generateGuids(String redisKey, long count) {

        RedisTemplate redisTemplate = SpringContextUtil.getInstance().getBean("redisTemplate");
        List<Long> list = null;
        try {
            list = BatchIdGenerator.batchGetGuids(redisTemplate, redisKey, count);
        } catch (Exception e) {
            log.error("生成guid失败，e={}", e.getMessage());
            throw new ParameterException("生成guid失败，e=" + e.getMessage());
        }
        return list;
    }

    /**
     * 解析guid
     *
     * @param guid
     * @return
     */
    public List<Long> parseGuid(String guid) {
        return BatchIdGenerator.parseGuid(Long.valueOf(guid));
    }


}
