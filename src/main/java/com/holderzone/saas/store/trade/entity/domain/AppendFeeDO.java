package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 附加费记录
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_append_fee")
public class AppendFeeDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 名字
     */
    private String name;

    /**
     * 收费方式。0=按人，1=按桌。
     */
    private Integer type;

    /**
     * 场景：0=正餐，1=快餐
     */
    private String tradeMode;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 退款数量
     */
    private BigDecimal refundCount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 区域guid
     */
    private String areaGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 操作人guid
     */
    private String staffGuid;

    /**
     * 操作人姓名
     */
    private String staffName;

    /**
     * 自定义金额退款分摊金额
     */
    private BigDecimal refundShareAmount;

    /**
     * 退款单关联的附加费才有该字段，对应退款单原单关联的附加费
     */
    @TableField(exist = false)
    private Long originalGuid;
}
