package com.holderzone.saas.store.trade.repository.feign;

import com.google.common.collect.Lists;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/12/3 10:53
 * @description
 */
@Component
@FeignClient(name = "holder-saas-store-staff", fallbackFactory = StaffClientService.FallBack.class)
public interface StaffClientService {

    @ApiOperation(value = "查询所有门店Guid")
    @PostMapping(value = "/user_data/query_all_store_guid")
    List<String> queryAllStoreGuid();

    @ApiOperation(value = "查询有权限门店营业数")
    @PostMapping("/user_data/get_staff_alive_store_count")
    Integer getStaffAliveStoreCount();

    @ApiOperation(value = "查询门店有权限的员工信息")
    @PostMapping(value = "/user/store_users")
    List<UserBriefDTO> storeUsers(@RequestParam("storeGuid") String storeGuid);

    @Component
    class FallBack implements FallbackFactory<StaffClientService> {
        private static final Logger logger = LoggerFactory.getLogger(StaffClientService.FallBack.class);

        @Override
        public StaffClientService create(Throwable throwable) {
            return new StaffClientService() {

                @Override
                public List<String> queryAllStoreGuid() {
                    logger.error("查询所有门店Guid调用异常，e={}", throwable.getMessage());
                    return Lists.newArrayList();
                }

                @Override
                public Integer getStaffAliveStoreCount() {
                    logger.error("查询有权限门店营业数异常，e={}", throwable.getMessage());
                    return 0;
                }

                @Override
                public List<UserBriefDTO> storeUsers(String storeGuid) {
                    logger.error("查询门店有权限的员工信息异常，e={}", throwable.getMessage());
                    return Lists.newArrayList();
                }
            };
        }
    }
}
