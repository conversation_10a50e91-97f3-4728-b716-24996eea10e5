package com.holderzone.saas.store.trade.entity.enums;
/**
 * <AUTHOR> R
 * @date 2020/11/18 17:41
 * @description
 */

public enum  WaiterTypeEnum {
    CALL_WAITER(1, "喊客员"),
    SERVE_WAITER(2, "服务员"),
    PASS_DISHES_WAITER(3, "传菜员"),
    TIDY_WAITER(4, "收台员"),
    MOP_WAITER(5, "洗碗员");
    private int code;
    private String desc;

    WaiterTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (WaiterTypeEnum c : WaiterTypeEnum.values()) {
            if (c.getValue() == code) {
                return c.desc;
            }
        }
        return null;
    }
    public int getValue() {
        return code;
    }
    public String getName() {
        return desc;
    }
}