package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单优惠记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface DiscountService extends IService<DiscountDO> {

    List<DiscountDO> listByOrderGuid(String orderGuid);

    List<DiscountDO> listByOrderGuids(List<String> orderGuid);

    void removeByOrderGuids(List<Long> subOrderGuids);

    void deleteByOrderGuids(List<String> orderGuids);

    DiscountDO getGrouponDiscount(String orderGuid);

    DiscountDO getGrouponDiscountByType(String orderGuid, Integer groupType);

    DiscountDO getThirdActivityDiscount(String orderGuid);

    DiscountDO getFollowRedPacketDiscount(String orderGuid);

    List<DiscountDO> getGrouponDiscount(List<Long> orderGuids);

    /**
     * 根据订单号查询使用的折扣
     *
     * @param orderGuid 订单号
     * @return 使用的折扣信息
     */
    List<DiscountDO> getUseDiscountListByOrderGuid(String orderGuid);

    List<DiscountDO> listHasGrouponCompleteOrder(LocalDateTime startTime, String orderGuid);

    void handleDiscount(String orderGuid, Integer discountType, BigDecimal deductionAmount);

    List<AmountItemDTO> handoverDiscountType(HandoverPayQueryDTO handoverPayQueryDTO);
}
