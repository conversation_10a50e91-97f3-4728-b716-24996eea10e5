package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.OrderExtendsDO;

import java.util.List;


/**
 * <p>
 * 订单扩展 服务类
 * </p>
 */
public interface OrderExtendsService extends IService<OrderExtendsDO> {

    List<OrderExtendsDO> listByOrderGuidList(List<Long> subOrderGuidList);
}
