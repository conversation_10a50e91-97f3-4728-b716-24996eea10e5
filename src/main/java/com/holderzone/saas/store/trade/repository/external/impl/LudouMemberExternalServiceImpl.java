package com.holderzone.saas.store.trade.repository.external.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.ludou.*;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.trade.repository.external.LudouMemberExternalService;
import com.holderzone.saas.store.trade.repository.feign.LudouClientService;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 麓豆对接
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LudouMemberExternalServiceImpl implements LudouMemberExternalService {


    private final LudouClientService ludouClientService;

    private static final String PAY_ERROR_MSG = "麓豆支付失败";

    private static final String REFUND_ERROR_MSG = "麓豆退款失败";

    @Override
    public LudouMemberDTO query(String phone, String ludouUserId) {
        try {
            LudouMemberQueryDTO queryDTO = new LudouMemberQueryDTO();
            queryDTO.setPhone(phone);
            queryDTO.setUserId(ludouUserId);
            log.info("登录麓豆会员入参, queryDTO:{}", JacksonUtils.writeValueAsString(queryDTO));
            LudouResult<?> result = ludouClientService.queryMember(queryDTO);
            if (Objects.isNull(result)) {
                log.error("登录麓豆会员失败, result is null, queryDTO:{}", JacksonUtils.writeValueAsString(queryDTO));
                return null;
            }
            log.info("登录麓豆会员返回, result:{}", JacksonUtils.writeValueAsString(result));
            if (!Boolean.TRUE.equals(result.getSuccess())) {
                log.error("登录麓豆会员失败, result:{}", JacksonUtils.writeValueAsString(result));
                return null;
            }
            return JacksonUtils.toObject(LudouMemberDTO.class, JacksonUtils.writeValueAsString(result.getData()));
        } catch (Exception e) {
            log.error("登录麓豆会员异常:", e);
            return null;
        }
    }

    @Override
    public LudouMemberPayRespDTO pay(BillPayReqDTO billPayReqDTO) {
        try {
            BillPayReqDTO.Payment ludouPayment = billPayReqDTO.getPayments().stream()
                    .filter(e -> PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode() == e.getPaymentType())
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(ludouPayment)) {
                return null;
            }
            LudouMemberPayDTO payDTO = LudouMemberPayDTO.builder()
                    .deviceNo(billPayReqDTO.getDeviceNo())
                    .userId(billPayReqDTO.getLudouMemberDTO().getLudouMemberGuid())
                    .ludouAmount(ludouPayment.getAmount())
                    .totalAmount(billPayReqDTO.getActuallyPayFee())
                    .outTradeNo(billPayReqDTO.getOrderGuid())
                    .remark(billPayReqDTO.getRemark())
                    .build();
            log.info("麓豆支付入参, payDTO:{}", JacksonUtils.writeValueAsString(payDTO));
            LudouResult<?> result = ludouClientService.pay(payDTO);
            if (Objects.isNull(result)) {
                log.error("{}, result is null, payDTO:{}", PAY_ERROR_MSG, JacksonUtils.writeValueAsString(payDTO));
                throw new BusinessException(PAY_ERROR_MSG);
            }
            log.info("麓豆支付返回, result:{}", JacksonUtils.writeValueAsString(result));
            if (!Boolean.TRUE.equals(result.getSuccess())) {
                log.error("{}, result:{}", PAY_ERROR_MSG, JacksonUtils.writeValueAsString(result));
                if ("配置缺失[未找到对应shopId]".equals(result.getMessage())) {
                    throw new BusinessException(PAY_ERROR_MSG + ":" + "未绑定设备号");
                }
                throw new BusinessException(PAY_ERROR_MSG);
            }
            return JacksonUtils.toObject(LudouMemberPayRespDTO.class, JacksonUtils.writeValueAsString(result.getData()));
        } catch (BusinessException e) {
            log.error("麓豆支付异常:", e);
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            log.error("麓豆支付异常:", e);
            throw new BusinessException(PAY_ERROR_MSG);
        }
    }

    @Override
    public Boolean refund(String orderGuid, String ludouOrderGuid,
                          BigDecimal actuallyPayFee, BigDecimal ludouPayFee, String remark) {
        try {
            if (ludouPayFee.compareTo(BigDecimal.ZERO) < 0) {
                log.error("麓豆退款金额小于0, orderGuid:{},ludouPayFee:{}", orderGuid, ludouPayFee);
                return true;
            }
            LudouMemberRefundDTO refundDTO = LudouMemberRefundDTO.builder()
                    .ludouAmount(ludouPayFee)
                    .refundAmount(actuallyPayFee)
                    .remark(remark)
                    .outTradeNo(orderGuid)
                    .tradeNo(ludouOrderGuid)
                    .build();
            log.info("麓豆退款入参, refundDTO:{}", JacksonUtils.writeValueAsString(refundDTO));
            LudouResult<?> result = ludouClientService.refund(refundDTO);
            if (Objects.isNull(result)) {
                log.error("{}, result is null, refundDTO:{}", REFUND_ERROR_MSG, JacksonUtils.writeValueAsString(refundDTO));
                return false;
            }
            log.info("麓豆退款返回, result:{}", JacksonUtils.writeValueAsString(result));
            if (!Boolean.TRUE.equals(result.getSuccess())) {
                log.error("{}, result:{}", REFUND_ERROR_MSG, JacksonUtils.writeValueAsString(result));
                return false;
            }
            if (!Boolean.TRUE.equals(result.getData())) {
                log.error("{}, result data:{}", REFUND_ERROR_MSG, JacksonUtils.writeValueAsString(result));
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("麓豆退款异常:", e);
            throw new BusinessException(REFUND_ERROR_MSG);
        }
    }


}
