package com.holderzone.saas.store.trade.entity.read;

import com.holderzone.saas.store.trade.entity.domain.OrderWaiterDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/11/26 18:12
 * @description
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderReadDO {
    /**
     * 全局唯一主键
     */
    private Long guid;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 结算时间
     */
    private LocalDateTime checkoutTime;
    /**
     * 桌台名称(区域+桌台code)
     */
    private String diningTableName;
    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    private String orderNo;
    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;

    private BigDecimal actuallyPayFee;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 订单类型（0：正餐 1：快餐）
     */
    private Integer tradeMode;
    /***
     * 订单服务员
     */
    private List<OrderWaiterDO> orderWaiterDOList;
}
