package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.trade.entity.domain.OrderItemExtendsDO;
import com.holderzone.saas.store.trade.mapper.OrderItemExtendsMapper;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemExtendsService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 订单商品扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Service
public class OrderItemExtendsServiceImpl extends ServiceImpl<OrderItemExtendsMapper, OrderItemExtendsDO> implements OrderItemExtendsService {

    @Override
    public List<OrderItemExtendsDO> listByOrderGuid(Long orderGuid) {
        LambdaQueryWrapper<OrderItemExtendsDO> qw = new LambdaQueryWrapper<OrderItemExtendsDO>()
                .eq(OrderItemExtendsDO::getOrderGuid, orderGuid);
        return list(qw);
    }

    @Override
    public void restoreOrderItemExtends(List<Long> orderItemGuidList) {
        baseMapper.restoreOrderItemExtends(orderItemGuidList);
    }

    @Override
    public List<OrderItemExtendsDO> listAllByGuid(Set<String> oldOrderItemGuids) {
        LambdaQueryWrapper<OrderItemExtendsDO> qw = new LambdaQueryWrapper<OrderItemExtendsDO>()
                .in(OrderItemExtendsDO::getGuid, oldOrderItemGuids);
        return list(qw);
    }
}
