package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.trade.entity.domain.OrderItemRecordDO;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.mapper.OrderItemRecordMapper;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemRecordService;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.RedisKeyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 订单商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
public class OrderItemRecordServiceImpl extends ServiceImpl<OrderItemRecordMapper, OrderItemRecordDO> implements OrderItemRecordService {

    private final RedisHelper redisHelper;

    @Autowired
    public OrderItemRecordServiceImpl(RedisHelper redisHelper) {
        this.redisHelper = redisHelper;
    }

    @Override
    public List<OrderItemRecordDO> listByOrderGuid(Long orderGuid) {
        return list(new LambdaQueryWrapper<OrderItemRecordDO>().eq(OrderItemRecordDO::getOrderGuid, orderGuid).orderByDesc
                (OrderItemRecordDO::getGmtCreate));

    }

    @Override
    public List<OrderItemRecordDO> listByOrderGuidWithCache(Long orderGuid) {
        List<OrderItemRecordDO> orderItemDOS = list(new LambdaQueryWrapper<OrderItemRecordDO>()
                .eq(OrderItemRecordDO::getIsDelete, false)
                .eq(OrderItemRecordDO::getOrderGuid, orderGuid)
                .orderByDesc(OrderItemRecordDO::getGmtCreate).orderByAsc(OrderItemRecordDO::getGuid));
        return orderItemDOS;
    }

    @Override
    public boolean saveBatchWithDeleteCache(Collection<OrderItemRecordDO> entityList) {
        List<OrderItemRecordDO> saveList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(entityList)) {
            for (OrderItemRecordDO orderItemRecordDO : entityList) {

                if (orderItemRecordDO.getType() == 1) {
                    orderItemRecordDO.setFreeCount(BigDecimal.ZERO);
                    orderItemRecordDO.setReturnCount(BigDecimal.ZERO);
                }

                if (orderItemRecordDO.getType() == 2) {
                    orderItemRecordDO.setCurrentCount(BigDecimal.ZERO);
                    orderItemRecordDO.setReturnCount(BigDecimal.ZERO);
                }

                if (orderItemRecordDO.getType() == 3) {
                    orderItemRecordDO.setFreeCount(BigDecimal.ZERO);
                    orderItemRecordDO.setCurrentCount(BigDecimal.ZERO);
                }
                saveList.add(orderItemRecordDO);
            }
        }
        saveBatch(saveList);
        return true;
    }

    @Override
    public boolean saveBatchWithDeleteCache(OrderItemRecordDO orderItemRecordDO) {
        if (orderItemRecordDO.getType() == 1) {
            orderItemRecordDO.setFreeCount(BigDecimal.ZERO);
            orderItemRecordDO.setReturnCount(BigDecimal.ZERO);
        }

        if (orderItemRecordDO.getType() == 2) {
            orderItemRecordDO.setCurrentCount(BigDecimal.ZERO);
            orderItemRecordDO.setReturnCount(BigDecimal.ZERO);
        }

        if (orderItemRecordDO.getType() == 3) {
            orderItemRecordDO.setFreeCount(BigDecimal.ZERO);
            orderItemRecordDO.setCurrentCount(BigDecimal.ZERO);
        }
        save(orderItemRecordDO);
        return true;
    }

    @Override
    public boolean updateBatchByIdWithDeleteCache(Collection<OrderItemRecordDO> entityList, String orderGuid) {
        String redisKey = RedisKeyUtil.getHstOrderItemKey(orderGuid);
        boolean update = updateBatchById(entityList);
        if (update) {
            redisHelper.delete(redisKey);
        }
        return update;
    }

    @Override
    public boolean removeByIdsWithDeleteCache(Collection<? extends Serializable> idList, String orderGuid) {
        if (CollectionUtil.isEmpty(idList)) {
            return Boolean.TRUE;
        }
        String redisKey = RedisKeyUtil.getHstOrderItemKey(orderGuid);
        boolean update = removeByIds(idList);
        if (update) {
            redisHelper.delete(redisKey);
        }
        return update;
    }

    @Override
    public List<OrderItemRecordDO> listByOrderGuidWithOutIsPay(Long orderGuid) {
        return list(new LambdaQueryWrapper<OrderItemRecordDO>().eq(OrderItemRecordDO::getOrderGuid, orderGuid).ne
                (OrderItemRecordDO::getIsPay, 1));
    }

    @Override
    public List<OrderItemRecordDO> listByOrderGuidWithOutSub(Long orderGuid) {
        return list(new LambdaQueryWrapper<OrderItemRecordDO>().eq(OrderItemRecordDO::getOrderGuid, orderGuid).eq
                (OrderItemRecordDO::getParentItemGuid, 0));
    }

    @Override
    public Boolean hasCurrentItem(Long orderGuid) {
        LambdaQueryWrapper<OrderItemRecordDO> queryWrapper = new LambdaQueryWrapper<OrderItemRecordDO>()
                .eq(OrderItemRecordDO::getOrderGuid, orderGuid).eq(OrderItemRecordDO::getParentItemGuid, 0);
        queryWrapper.and(i -> i.gt(OrderItemRecordDO::getCurrentCount, 0).or().gt(OrderItemRecordDO::getFreeCount, 0));
        List<OrderItemRecordDO> orderItemDOS = list(queryWrapper);
        return !CollectionUtil.isEmpty(orderItemDOS);
    }

    @Override
    public List<OrderItemRecordDO> listByMainItemGuid(Long itemGuid) {
        return list(new LambdaQueryWrapper<OrderItemRecordDO>()
                .eq(OrderItemRecordDO::getParentItemGuid, itemGuid));
    }

    @Override
    public List<OrderItemRecordDO> listByMainItemGuids(Collection<? extends Serializable> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<OrderItemRecordDO>()
                .in(OrderItemRecordDO::getParentItemGuid, idList));
    }

    @Override
    public void deleteByOrderGuid(String orderGuid) {
        remove(new LambdaQueryWrapper<OrderItemRecordDO>().eq(OrderItemRecordDO::getOrderGuid, orderGuid).ne
                (OrderItemRecordDO::getIsPay, 1));
    }

    @Override
    public void deleteByOrderGuids(List<String> orderGuids) {
        if (CollectionUtil.isEmpty(orderGuids)) {
            return;
        }
        remove(new LambdaQueryWrapper<OrderItemRecordDO>().in(OrderItemRecordDO::getOrderGuid, orderGuids));
    }

    @Override
    public void deleteByItemGuids(List<String> itemGuids) {
        if (CollectionUtil.isEmpty(itemGuids)) {
            return;
        }
        remove(new LambdaQueryWrapper<OrderItemRecordDO>().in(OrderItemRecordDO::getOrderItemGuid, itemGuids));
    }

    @Override
    public List<OrderItemRecordDO> listByIdsWithLock(Collection<? extends Serializable> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<OrderItemRecordDO>().in(OrderItemRecordDO::getGuid, idList).last("for update"));
    }

    @Override
    public List<OrderItemRecordDO> listByOrderLists(List<String> orderLists) {
        if (CollectionUtil.isEmpty(orderLists)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<OrderItemRecordDO>().in(OrderItemRecordDO::getOrderGuid, orderLists));
    }

    @Override
    public List<OrderItemRecordDO> listByItemGuids(Collection<? extends Serializable> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<OrderItemRecordDO>()
                .in(OrderItemRecordDO::getOrderItemGuid, idList));
    }

}
