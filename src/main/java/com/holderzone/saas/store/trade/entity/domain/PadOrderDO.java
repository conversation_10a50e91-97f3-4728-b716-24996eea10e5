package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * pad订单表
 * </p>
 * hst_pad_order
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_pad_order")
public class PadOrderDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId("guid")
    private Long guid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 交易订单号
     */
    private String orderGuid;

    /**
     * pad点餐批次
     */
    private Integer padBatch;

    /**
     * 消费合计
     */
    private BigDecimal totalPrice;

    /**
     * 就餐人数
     */
    private Integer actualGuestsNo;

    /**
     * 区域guid
     */
    private String areaGuid;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 桌台guid
     */
    private String diningTableGuid;

    /**
     * 桌台号
     */
    private String tableCode;

    /**
     * see{@link com.holderzone.saas.store.enums.order.OrderStateEnum}
     * 下单状态 0:待处理，1：接单，2：拒单，3：未结帐，4：已结账,5:已做废, 8订单已失效,9.已退菜
     */
    private Integer orderState;

    /**
     * 拒单原因
     */
    private String denialReason;

    /**
     * see{@link com.holderzone.saas.store.enums.order.TradeModeEnum}
     * 用餐类型，0：正餐：1：快餐
     */
    private Integer tradeMode;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 整单备注
     */
    private String remark;

    /**
     * 商品数量
     */
    private Integer itemCount;

    /**
     * 操作人员guid
     */
    private String operationGuid;

    /**
     * 操作人员名字
     */
    private String operationName;

    /**
     * 并桌订单的id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String combineOrderGuid;

    /**
     * 订单来源
     */
    private Integer orderSource;

    /**
     * 会员电话
     */
    private String memberPhone;

}
