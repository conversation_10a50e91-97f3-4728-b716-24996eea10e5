package com.holderzone.saas.store.trade.service.mq;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.table.TableStatusChangeDTO;
import com.holderzone.saas.store.trade.config.RocketMqConfig;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

public interface TableMqService {


    boolean tableStatusChangeWithMq(TableStatusChangeDTO tableStatusChangeDTO);



    @Component
    class TableMqServiceImpl implements TableMqService{

        private static final Logger log = LoggerFactory.getLogger(KdsMqService.class);

        private final DefaultRocketMqProducer defaultRocketMqProducer;

        @Autowired
        public TableMqServiceImpl(DefaultRocketMqProducer defaultRocketMqProducer){
            this.defaultRocketMqProducer=defaultRocketMqProducer;
        }


        @Override
        public boolean tableStatusChangeWithMq(TableStatusChangeDTO tableStatusChangeDTO) {
            if (log.isInfoEnabled()) {
                log.info("桌台状态变化同步入参Mq：{}", JacksonUtils.writeValueAsString(tableStatusChangeDTO));
            }
            tableStatusChangeDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            Message message = new Message(
                    RocketMqConfig.TABLE_STATUS_CHANGE_MQ_TOPIC,
                    RocketMqConfig.TABLE_STATUS_CHANGE_MQ_TAG,
                    JacksonUtils.toJsonByte(tableStatusChangeDTO)
            );
            return defaultRocketMqProducer.sendMessage(message);
        }
    }



}
