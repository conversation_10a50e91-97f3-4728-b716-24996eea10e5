package com.holderzone.saas.store.trade.utils;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import com.holderzone.saas.store.dto.erp.SkuInfo;
import com.holderzone.saas.store.dto.order.common.*;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.enums.ItemTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemPushHelper
 * @date 2019/02/27 下午3:36
 * @description //商品相关辅助类
 * @program holder-saas-store-item
 */
@Slf4j
@Component
public class ItemUtil {

    /**
     * MDM  组装
     */
    public static PushOrderBillsBO combineMdmMessage(OrderDO orderDO, List<DineInItemDTO> erpDineInItemDTOS, boolean
            isRecovery, Function<SingleDataDTO, Map<String, String>> erpFunction) {
        PushOrderBillsBO pushOrderBillsBO = new PushOrderBillsBO();
        pushOrderBillsBO.setSalesOrderId(orderDO.getGuid().toString());
        pushOrderBillsBO.setSerialNumber(orderDO.getOrderNo());
        pushOrderBillsBO.setGuestCount(orderDO.getGuestCount());
        pushOrderBillsBO.setDiningTableName(orderDO.getDiningTableName());
        pushOrderBillsBO.setStoreId(orderDO.getStoreGuid());
        pushOrderBillsBO.setCreateTime(orderDO.getGmtCreate());
        pushOrderBillsBO.setConsumeTotal(orderDO.getActuallyPayFee());
        pushOrderBillsBO.setTradeMode(orderDO.getTradeMode());
        pushOrderBillsBO.setCheckStaffId(orderDO.getCheckoutStaffGuid());
        pushOrderBillsBO.setCheckOutStaffName(orderDO.getCheckoutStaffName());
        pushOrderBillsBO.setBusinessDay(orderDO.getBusinessDay());
        pushOrderBillsBO.setCheckTotal(orderDO.getOrderFee());
        pushOrderBillsBO.setPaymentDiscountTotal(orderDO.getOrderFee().subtract(orderDO.getActuallyPayFee()));
        pushOrderBillsBO.setAdditionalFeesTotal(orderDO.getAppendFee());
        pushOrderBillsBO.setIsRecovery(isRecovery);
        List<SalesOrderDisheseBO> saleDishheseBo = getSaleDishheseBo(erpDineInItemDTOS);
        List<String> storeDishIds = saleDishheseBo.stream().map(SalesOrderDisheseBO::getStoreDishesId).collect
                (Collectors.toList());
        Map<String, String> erpSkuGuids = erpFunction.apply(new SingleDataDTO("jh", storeDishIds));
        log.info("erpSkuGuids：{}", JacksonUtils.writeValueAsString(erpSkuGuids));
//        saleDishheseBo.forEach(e -> e.setDishesId(erpSkuGuids.get(e.getStoreDishesId())));
        for (SalesOrderDisheseBO bo : saleDishheseBo) {
            String storeDishesId = bo.getStoreDishesId();
            String s = erpSkuGuids.get(storeDishesId);
            if (StringUtils.isNotBlank(s)) {
                bo.setDishesId(s);
            } else {
                // 查不到，认为就是品牌库的规格GUID
                bo.setDishesId(storeDishesId);
            }
        }
        pushOrderBillsBO.setSalesOrderDisheses(saleDishheseBo);
        return pushOrderBillsBO;
    }

    //转化
    public static List<SalesOrderDisheseBO> getSaleDishheseBo(List<DineInItemDTO> erpDineInItemDTOS) {
        List<SalesOrderDisheseBO> salesOrderDisheseBOS = new ArrayList<>();
        for (DineInItemDTO d : erpDineInItemDTOS) {
            addSalesOrderDisheseBO(d, salesOrderDisheseBOS);
        }
        return salesOrderDisheseBOS;
    }


    /**
     * 添加 SalesOrderDisheseBO
     */
    private static void addSalesOrderDisheseBO(DineInItemDTO dineInItemDTO, List<SalesOrderDisheseBO> salesOrderDisheseBOList) {
        SalesOrderDisheseBO salesOrderDisheseBO = new SalesOrderDisheseBO();
        salesOrderDisheseBO.setStoreDishesId(dineInItemDTO.getSkuGuid());
        salesOrderDisheseBO.setPrice(dineInItemDTO.getPrice());
        salesOrderDisheseBO.setItemPrice(dineInItemDTO.getItemPrice());
        salesOrderDisheseBO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice());
        salesOrderDisheseBO.setCheckCount(dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount()));
        salesOrderDisheseBO.setPracticeSubTotal(dineInItemDTO.getItemPrice());
        salesOrderDisheseBO.setDishesName(dineInItemDTO.getItemName());
        salesOrderDisheseBO.setDishesSkuName(dineInItemDTO.getSkuName());
        salesOrderDisheseBO.setDishTypeName(dineInItemDTO.getItemTypeName());
        salesOrderDisheseBO.setDishesCode(dineInItemDTO.getCode());
        salesOrderDisheseBO.setCheckUnit(dineInItemDTO.getUnit());
        salesOrderDisheseBO.setPackageDishes(ItemUtil.isGroup(dineInItemDTO.getItemType()) ? 1 : 0);
        salesOrderDisheseBOList.add(salesOrderDisheseBO);
        if (CollectionUtil.isEmpty(dineInItemDTO.getPackageSubgroupDTOS())) {
            return;
        }
        List<SubDineInItemDTO> subDineInItemList = dineInItemDTO.getPackageSubgroupDTOS().stream()
                .filter(e -> CollectionUtil.isNotEmpty(e.getSubDineInItemDTOS()))
                .flatMap(e -> e.getSubDineInItemDTOS().stream())
                .collect(Collectors.toList());
        //套餐数量
        BigDecimal packageMealNumber = dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount());
        for (SubDineInItemDTO subDineInItemDTO : subDineInItemList) {
            SalesOrderDisheseBO subsalesOrderDisheseBO = new SalesOrderDisheseBO();
            String skuGuid = subDineInItemDTO.getSkuGuid();
            String itemName = subDineInItemDTO.getItemName();
            String code = subDineInItemDTO.getCode();
            String unit = subDineInItemDTO.getUnit();
            subsalesOrderDisheseBO.setStoreDishesId(skuGuid);
            subsalesOrderDisheseBO.setPrice(subDineInItemDTO.getPrice());
            subsalesOrderDisheseBO.setCheckCount(subDineInItemDTO.getCurrentCount());
            if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getCurrentCount())
                    && BigDecimalUtil.greaterThanZero(subDineInItemDTO.getPackageDefaultCount())) {
                //套餐内自菜品数量
                BigDecimal singleNumber = subDineInItemDTO.getCurrentCount().multiply(subDineInItemDTO.getPackageDefaultCount());
                if (BigDecimalUtil.greaterThanZero(packageMealNumber)) {
                    subsalesOrderDisheseBO.setCheckCount(singleNumber.multiply(packageMealNumber));
                }
            }
            subsalesOrderDisheseBO.setDishesName(itemName);
            subsalesOrderDisheseBO.setDishesSkuName(subDineInItemDTO.getSkuName());
            subsalesOrderDisheseBO.setDishTypeName(subDineInItemDTO.getItemTypeName());
            subsalesOrderDisheseBO.setDishesCode(code);
            subsalesOrderDisheseBO.setCheckUnit(unit);
            subsalesOrderDisheseBO.setPackageDishes(2);
            salesOrderDisheseBOList.add(subsalesOrderDisheseBO);
        }
    }


    public static OrderSkuDTO getErpDTO(List<DineInItemDTO> list) {
        Map<String, BigDecimal> stringBigDecimalMap = itemEstimateHander(list);
        OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        orderSkuDTO.setOperatorGuid(UserContextUtils.getUserGuid());
        orderSkuDTO.setOperatorName(UserContextUtils.getUserName());
        orderSkuDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        List<SkuInfo> skuList = new ArrayList<>();
        orderSkuDTO.setSkuList(skuList);
        for (String s : stringBigDecimalMap.keySet()) {
            SkuInfo skuInfo = new SkuInfo();
            skuInfo.setSkuGuid(s);
            skuInfo.setCount(stringBigDecimalMap.get(s));
            skuList.add(skuInfo);
        }
        return orderSkuDTO;
    }

    public static OrderSkuDTO getErpDTO(List<DineInItemDTO> list, boolean isout) {
        Map<String, BigDecimal> stringBigDecimalMap = itemEstimateHander(list);
        OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setOut(isout);
        orderSkuDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        orderSkuDTO.setOperatorGuid(UserContextUtils.getUserGuid());
        orderSkuDTO.setOperatorName(UserContextUtils.getUserName());
        orderSkuDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        List<SkuInfo> skuList = new ArrayList<>();
        orderSkuDTO.setSkuList(skuList);
        for (String s : stringBigDecimalMap.keySet()) {
            SkuInfo skuInfo = new SkuInfo();
            skuInfo.setSkuGuid(s);
            skuInfo.setCount(stringBigDecimalMap.get(s));
            skuList.add(skuInfo);
        }
        return orderSkuDTO;
    }

    /**
     * 传入订单 返回需检验商品的SKU和数量
     *
     * @param list
     * @return
     */
    public static Map<String, BigDecimal> itemEstimateHander(List<DineInItemDTO> list) {
        Map<String, BigDecimal> result = new HashMap<>();
        for (DineInItemDTO a : list) {
            result = getDineInNumber(a, result);
        }
        return result;
    }

    //传入一个菜 ,计算其赠菜
    public static Map<String, BigDecimal> getDineInNumber(DineInItemDTO dineInItemDTO, Map<String, BigDecimal> map) {
        BigDecimal currentCount = dineInItemDTO.getCurrentCount();
        //判断是否有赠菜
        if (Optional.ofNullable(dineInItemDTO.getFreeItemDTOS()).isPresent() && !dineInItemDTO.getFreeItemDTOS()
                .isEmpty()) {
            //获取到赠菜
            List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
            //计算赠送数量
            BigDecimal freeCount = freeItemDTOS.stream().map(FreeItemDTO::getCount).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            //加上赠送数量
            currentCount = currentCount.add(freeCount);
        }
        //判断是否是套餐
        if (CollectionUtil.isNotEmpty(dineInItemDTO.getPackageSubgroupDTOS())) {
            addPackageSubgroupCount(dineInItemDTO, map, currentCount);
        } else { //单品、称重
            if (map.containsKey(dineInItemDTO.getSkuGuid())) {
                BigDecimal count = map.get(dineInItemDTO.getSkuGuid());
                count = count.add(currentCount);
                map.put(dineInItemDTO.getSkuGuid(), count);
            } else { //不存在
                map.put(dineInItemDTO.getSkuGuid(), currentCount);
            }
        }
        return map;
    }

    /**
     * 计算套餐商品数量
     */
    private static void addPackageSubgroupCount(DineInItemDTO dineInItemDTO, Map<String, BigDecimal> map, BigDecimal currentCount) {
        List<PackageSubgroupDTO> packageSubgroupDTOS = dineInItemDTO.getPackageSubgroupDTOS();
        //套餐主项sku存在
        if (map.containsKey(dineInItemDTO.getSkuGuid())) {
            BigDecimal count = map.get(dineInItemDTO.getSkuGuid());
            count = count.add(currentCount);
            map.put(dineInItemDTO.getSkuGuid(), count);
        } else { //套餐主项sku不存在
            map.put(dineInItemDTO.getSkuGuid(), currentCount);
        }
        for (PackageSubgroupDTO s : packageSubgroupDTOS) {
            List<SubDineInItemDTO> subDineInItemDTOS = s.getSubDineInItemDTOS();
            if (CollectionUtil.isEmpty(subDineInItemDTOS)) {
                continue;
            }
            subDineInItemDTOS.forEach(a -> {
                String skuGuid = a.getSkuGuid();
                // map已经存在相同sku
                if (map.containsKey(skuGuid)) {
                    BigDecimal count = map.get(skuGuid);
                    count = count.add(a.getCurrentCount().multiply(a.getPackageDefaultCount()).multiply(currentCount));
                    map.put(skuGuid, count);
                } else {
                    //不存在
                    map.put(skuGuid, a.getCurrentCount().multiply(a.getPackageDefaultCount()).multiply(currentCount));
                }
            });
        }

    }


    /**
     * 是否非套餐 (是-true；否-false)
     * 2-规格 或  3-称重 或  4-单品   ----->  true
     * 1-套餐 或  5团餐套餐           ----->  false
     *
     * @param itemType
     * @return
     */
    public static Boolean isOutOfGroup(Integer itemType) {
        Boolean flag = Boolean.FALSE;
        if (itemType.equals(ItemTypeEnum.SKU.getCode()) || itemType.equals(ItemTypeEnum.WEIGH.getCode())
                || itemType.equals(ItemTypeEnum.SINGLE.getCode())) {
            flag = Boolean.TRUE;
        }
        return flag;
    }


    /**
     * 是否非套餐 (是-true；否-false)
     * 1-套餐 或  5团餐套餐           ----->  true
     * 2-规格 或  3-称重 或  4-单品   ----->  false
     *
     * @param itemType
     * @return
     */
    public static Boolean isGroup(Integer itemType) {
        Boolean flag = Boolean.FALSE;
        if (itemType.equals(ItemTypeEnum.GROUP.getCode()) || itemType.equals(ItemTypeEnum.TEAMMEAL.getCode())) {
            flag = Boolean.TRUE;
        }
        return flag;
    }
}
