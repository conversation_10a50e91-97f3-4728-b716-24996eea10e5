package com.holderzone.saas.store.trade.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RequireOrderCheckLock
 * @date 2019/09/10 9:31
 * @description //TODO  检查订单是否被锁住
 * @program IdeaProjects
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireOrderCheckLock {
}