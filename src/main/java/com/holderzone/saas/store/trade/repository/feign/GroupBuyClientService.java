package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GroupBuyClientService
 * @date 2018/09/06 19:33
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-takeaway-consumer", fallbackFactory = GroupBuyClientService.ServiceFallBack.class)
public interface GroupBuyClientService {

    @PostMapping("/groupbuy/do_check")
    MtCouponDoCheckRespDTO doCheck(@RequestBody CouPonReqDTO couPonReqDTO);

    @PostMapping("/groupbuy/cancel_ticket")
    MtDelCouponRespDTO cancelCheckTicket(@RequestBody CouponDelReqDTO couponDelReqDTO);

    @PostMapping("/groupbuy/trade/detail")
    MtCouponTradeDetailRespDTO queryGroupTradeDetail(@RequestBody CouPonReqDTO couPonReqDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<GroupBuyClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public GroupBuyClientService create(Throwable cause) {
            return new GroupBuyClientService() {

                @Override
                public MtDelCouponRespDTO cancelCheckTicket(CouponDelReqDTO couponDelReqDTO) {
                    log.error(HYSTRIX_PATTERN, "cancelCheckTicket", JacksonUtils.writeValueAsString(couponDelReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }


                @Override
                public MtCouponDoCheckRespDTO doCheck(CouPonReqDTO couPonReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doCheck", JacksonUtils.writeValueAsString(couPonReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCouponTradeDetailRespDTO queryGroupTradeDetail(CouPonReqDTO couPonReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryGroupTradeDetail", JacksonUtils.writeValueAsString(couPonReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

            };


        }
    }
}