package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 订单
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_order_subsidiary")
public class OrderSubsidiaryDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 本地化上传Id
     */
    private String localUploadId;

    /**
     * 第三方订单id
     */
    private String thirdPartyId;

    /**
     * 预点餐状态（1：预点餐未下单，2：预点餐已下单）
     */
    private Integer reserveOrderState;

}
