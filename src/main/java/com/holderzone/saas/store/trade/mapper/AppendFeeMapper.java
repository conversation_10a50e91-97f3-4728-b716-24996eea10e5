package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.trade.entity.domain.AppendFeeDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 附加费记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-22
 */
public interface AppendFeeMapper extends BaseMapper<AppendFeeDO> {

    boolean deleteAppendByIds(@Param("guids") List<String> guids);

}
