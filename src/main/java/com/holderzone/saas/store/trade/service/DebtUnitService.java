package com.holderzone.saas.store.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.trade.entity.domain.DebtUnitDO;
import com.holderzone.saas.store.trade.entity.dto.DebtCreditChangeDTO;

import java.util.List;

/**
 * 挂账单位IService
 *
 * <AUTHOR>
 * @since 2020-12-15
 */
public interface DebtUnitService extends IService<DebtUnitDO> {

    /**
     * 保存挂账单位
     * @param reqDTO DebtUnitSaveReqDTO
     * @return 结果
     */
    Boolean saveUnit(DebtUnitSaveReqDTO reqDTO);

    /**
     * 分页查询挂账单位列表
     * @param reqDTO 单位名称
     * @return 单位列表
     */
    Page<DebtUnitPageRespDTO> unitPage(DebtUnitPageReqDTO reqDTO);

    /**
     * 挂账单位列表（下拉框显示）
     * @return 单位下拉列表
     */
    List<DebtUnitDropdownListDTO> unitDropdownList();

    /**
     * 信用额度变更
     * @param changeDTO DebtCreditChangeDTO
     */
    Boolean creditLimitChange(DebtCreditChangeDTO changeDTO);

    /**
     * 挂账单位删除
     * @param unitGuid 单位guid
     * @return 是否
     */
    Boolean unitDelete(String unitGuid);

    /**
     * h5页面查询挂账记录登录（验证密码）
     * @param reqDTO DebtUnitLoginH5ReqDTO
     * @return 是否成功
     */
    Boolean h5Login(DebtUnitLoginH5ReqDTO reqDTO);
}
