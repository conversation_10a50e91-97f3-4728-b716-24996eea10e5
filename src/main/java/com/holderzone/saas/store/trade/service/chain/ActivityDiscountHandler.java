package com.holderzone.saas.store.trade.service.chain;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.order.*;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.CommonUtil;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-26
 * @description
 */
@Component
@Slf4j
@AllArgsConstructor
public class ActivityDiscountHandler extends DiscountHandler{

    private final MemberTerminalClientService memberTerminalClientService;

    private final PriceCalculationUtils priceCalculationUtils;
    @Override
    void dealDiscount(DiscountContext context) {
        if(context.isRejectDiscount()){
            return;
        }
        //  =========================== 10.满减，11.满折 ========================
        DiscountFeeDetailDTO activity = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap()
                .get(type()));
        RequestActivityUse activityUseReqDTO = new RequestActivityUse();
        activityUseReqDTO.setActivityGuid(context.getBillCalculateReqDTO().getActivityGuid());
        activityUseReqDTO.setMemberConsumptionGuid(context.getOrderDO().getMemberConsumptionGuid());
        activityUseReqDTO.setNeedActivityList(context.getBillCalculateReqDTO().getIsNeedActivityList() != null &&
                context.getBillCalculateReqDTO().getIsNeedActivityList());
        activityUseReqDTO.setMemberInfoCardGuid(context.getOrderDO().getMemberCardGuid());
        List<RequestDishInfo> requestDishInfos = CommonUtil.dineInItem2DishList(context.getAllItems());
        activityUseReqDTO.setDishInfoDTOList(requestDishInfos);
        activityUseReqDTO.setOrderDate(context.getOrderDO().getGmtCreate());
        activityUseReqDTO.setUnNeedPersistence(Optional.ofNullable(context.getBillCalculateReqDTO().getUnNeedPersistence()).orElse(false));
        activityUseReqDTO.setActivityType(DiscountTypeEnum.ACTIVITY.getCode());
        activityUseReqDTO.setOrderState(context.getOrderDO().getTradeMode());
        if (!StringUtils.isEmpty(context.getBillCalculateReqDTO().getNthActivityGuid())) {
            RequestActivityUse.InnerOtherActivity otherActivity = new RequestActivityUse.InnerOtherActivity();
            otherActivity.setActivityGuid(context.getBillCalculateReqDTO().getNthActivityGuid());
            otherActivity.setActivityType(DiscountTypeEnum.NTH_ACTIVITY.getCode());
            activityUseReqDTO.setOtherActivities(Lists.newArrayList(otherActivity));
        }
        priceCalculationUtils.xx90(requestDishInfos);
        ResponseActivityUse activityUseRespDTO = memberTerminalClientService.activitySelect(activityUseReqDTO);
        log.info("满减满折活动返回：{}", JacksonUtils.writeValueAsString(activityUseRespDTO));
        singleItemDiscount(context.getDineInItemDTOMap(), activity, activityUseRespDTO.getDishInfoDTOList());
        Map<String, DineInItemDTO> itemDTOMap = context.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        activityUseRespDTO.getDishInfoDTOList().forEach(dish -> {
            if (dish.getDiscountMoney() != null) {
                DineInItemDTO dineInItemDTO = itemDTOMap.get(dish.getOrderItemGuid());
                if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                    dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(dish.getDiscountMoney()));
                }
            }
        });
        context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(activity
                .getDiscountFee()));
        activity.setContent(activityUseRespDTO.getActivityName());
        if (CollectionUtils.isNotEmpty(activityUseRespDTO.getActivitiesList())) {
            List<ResponseClientActivitiesList> activitiesLists = Optional
                    .ofNullable(context.getOrderDetailRespDTO().getActivityInfos()).orElse(Lists.newArrayList());
            activitiesLists.addAll(activityUseRespDTO.getActivitiesList());
            context.getOrderDetailRespDTO().setActivityInfos(activitiesLists);
        }
        context.getOrderDetailRespDTO().setActivityGuid(activityUseRespDTO.getActivityGuid());
        context.getOrderDO().setMemberConsumptionGuid(activityUseRespDTO.getMemberConsumptionGuid());
        context.getDiscountFeeDetailDTOS().add(activity);
        log.info("优惠活动计算后订单剩余金额：{}，优惠金额：{}，菜品优惠：{}", context.getOrderDetailRespDTO().getOrderSurplusFee(), activity
                .getDiscountFee(), JacksonUtils.writeValueAsString(context.getDineInItemDTOMap()));
    }

    /**
     * 优惠金额处理
     */
    private void handleDiscountTotalPrice(DiscountContext context, RequestActivityUse activityUseReqDTO) {
        List<RequestDishInfo> requestDishInfos = CommonUtil.dineInItem2DishListByDiscount(context.getAllItems());
        priceCalculationUtils.xx90(requestDishInfos);
        activityUseReqDTO.setDishInfoDTOList(requestDishInfos);
        ResponseActivityUse activityUseRespDTO = memberTerminalClientService.activitySelect(activityUseReqDTO);
        log.info("[优惠金额处理]满减满折活动返回：{}", JacksonUtils.writeValueAsString(activityUseRespDTO));
        Map<String, DineInItemDTO> itemDTOMap = context.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (v1, v2) -> v1));
        activityUseRespDTO.getDishInfoDTOList().forEach(dish->{
            if (dish.getDiscountMoney() != null) {
                DineInItemDTO dineInItemDTO = itemDTOMap.get(dish.getOrderItemGuid());
                if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                    dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(dish.getDiscountMoney()));
                }
            }
        });
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.ACTIVITY.getCode();
    }
}
