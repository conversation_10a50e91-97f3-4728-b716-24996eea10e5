package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 团购券
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_groupon")
public class GrouponDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 名字
     */
    private String name;

    /**
     * 券码
     */
    private String code;

    /**
     * 三方平台活动编码
     */
    private String thirdCode;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 操作人guid
     */
    private String staffGuid;

    /**
     * 操作人姓名
     */
    private String staffName;

    /**
     * 券平台
     */
    private Integer grouponType;

    /**
     * 券类型
     * 团购类型（type=1团餐券; type=2代金券; type=3次卡）
     */
    private Integer couponType;

    /**
     * 顾客购买金额
     */
    private BigDecimal couponBuyPrice;

    /**
     * 抵扣金额
     */
    private BigDecimal deductionAmount;

    /**
     * 代表券码一次核销的标识（抖音撤销时需要）
     */
    private String verifyId;

    /**
     * 代表一张券码的标识（抖音撤销时需要）
     */
    private String certificateId;

    /**
     * 第三方活动guid
     */
    private String activityGuid;

    /**
     * 退款订单guid
     */
    private Long refundOrderGuid;

    /**
     * 凭证归属支付宝用户id
     */
    private String userId;

    /**
     * 券码渠道 买单:1004, 团购: 1000
     */
    private Integer receiptChannel;

    /**
     * 一键买单消费信息
     */
    private String maitonConsumeInfo;
}
