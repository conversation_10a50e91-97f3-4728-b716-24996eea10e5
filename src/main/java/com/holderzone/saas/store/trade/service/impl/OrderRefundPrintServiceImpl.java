package com.holderzone.saas.store.trade.service.impl;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.OrderRefundReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.RecoveryReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.RefundOrderPrintDTO;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.RefundTypeEnum;
import com.holderzone.saas.store.enums.print.RefundPrintTypeEnum;
import com.holderzone.saas.store.trade.entity.bo.RefundOrderBO;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.service.DineInPrintService;
import com.holderzone.saas.store.trade.service.OrderRefundPrintService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 退款单打印service实现类
 *
 * <AUTHOR>
 * @date 2025/7/5 11:28
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderRefundPrintServiceImpl implements OrderRefundPrintService {

    private final DineInPrintService dineInPrintService;

    @Override
    public void printByRefund(OrderRefundReqDTO orderRefundReqDTO, RefundOrderBO refundOrderBO) {
        // 打印退菜单
        OrderDO originalOrder = refundOrderBO.getOriginalOrder();
        originalOrder.setRefundOrderGuid(refundOrderBO.getRefundOrderGuid());
        // 部分退款需要打印退菜单
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = OrderTransform.INSTANCE.orderDO2DineinOrderDetailRespDTO(originalOrder);
        List<DineInItemDTO> returnPrintDineInItemDTOS = AmountCalculationUtil.buildItem(refundOrderBO.getOrderItemList(),
                Collections.emptyList(), Collections.emptyList());

        dineinOrderDetailRespDTO.setDineInItemDTOS(returnPrintDineInItemDTOS);
        dineInPrintService.printRefundItem(orderRefundReqDTO, dineinOrderDetailRespDTO);

        // 打印退款单
        try {
            // 重新新建对象
            DineinOrderDetailRespDTO newRespDTO = OrderTransform.INSTANCE.orderDO2DineinOrderDetailRespDTO(originalOrder);
            // 退款菜单，将小计修改为退款金额
            updatePrintRefundPrice(returnPrintDineInItemDTOS, refundOrderBO);
            newRespDTO.setDineInItemDTOS(returnPrintDineInItemDTOS);
            RefundOrderPrintDTO refundOrderPrintDTO = new RefundOrderPrintDTO();
            OrderRefundRecordDO orderRefundRecord = refundOrderBO.getOrderRefundRecord();
            // 退款时间
            refundOrderPrintDTO.setRefundTime(DateTimeUtils.localDateTime2Mills(refundOrderBO.getOrder().getGmtCreate()));
            // 退款金额
            refundOrderPrintDTO.setRefundAmount(orderRefundRecord.getRefundAmount());
            // 退款理由
            refundOrderPrintDTO.setRefundReason(orderRefundRecord.getRefundReason());
            // 退款方式
            RefundTypeEnum refundTypeEnum = Objects.requireNonNull(RefundTypeEnum.getByCode(orderRefundRecord.getRefundType()));
            if (!Objects.equals(RefundTypeEnum.OFFLINE_REFUND, refundTypeEnum)) {
                refundOrderPrintDTO.setRefundType(refundTypeEnum.getDesc());
                // 退款支付方式
                refundOrderPrintDTO.setPaymentTypeName(getPrintRefundPayType(refundOrderBO));
            } else {
                // 默认支付方式为线下退款
                refundOrderPrintDTO.setPaymentTypeName(refundTypeEnum.getDesc());
            }
            // 操作时间
            refundOrderPrintDTO.setOperationTime(DateTimeUtils.nowMillis());
            // 类型为退款
            refundOrderPrintDTO.setTypeEnum(RefundPrintTypeEnum.REFUND);
            // 附加费
            refundOrderPrintDTO.setAdditionalChargeList(getAdditionalChargeList(refundOrderBO.getAppendFeeList()));
            newRespDTO.setRefundOrderPrintDTO(refundOrderPrintDTO);
            dineInPrintService.printRefundOrder(orderRefundReqDTO, newRespDTO);
            log.info("[打印退款单推送]完成: {}", JacksonUtils.writeValueAsString(newRespDTO));
        } catch (Exception e) {
            log.error("[打印退款单推送]失败", e);
        }
    }

    @Override
    public void printByRecovery(RecoveryReqDTO recoveryReqDTO, DineinOrderDetailRespDTO orderDetail, LocalDateTime now) {
        try {
            RefundOrderPrintDTO refundOrderPrintDTO = new RefundOrderPrintDTO();
            // 退款时间
            refundOrderPrintDTO.setRefundTime(DateTimeUtils.localDateTime2Mills(now));
            // 退款金额
            refundOrderPrintDTO.setRefundAmount(orderDetail.getOrderFee());
            // 退款理由
            refundOrderPrintDTO.setRefundReason(recoveryReqDTO.getReason());
            // 退款方式，原路返回
            refundOrderPrintDTO.setRefundType(RefundTypeEnum.BACKTRACK.getDesc());
            // 退款支付方式
            refundOrderPrintDTO.setPaymentTypeName(getPrintRefundPayType(orderDetail.getActuallyPayFeeDetailDTOS()));
            // 操作时间
            refundOrderPrintDTO.setOperationTime(DateTimeUtils.nowMillis());
            // 类型为反结账
            refundOrderPrintDTO.setTypeEnum(RefundPrintTypeEnum.RECOVERY);
            // 附加费
            refundOrderPrintDTO.setAdditionalChargeList(getAdditionalChargeList(orderDetail.getOrderFeeDetailDTO()));
            orderDetail.setRefundOrderPrintDTO(refundOrderPrintDTO);

            dineInPrintService.printRefundOrder(recoveryReqDTO, orderDetail);
            log.info("反结账[打印退款单推送]完成: {}", JacksonUtils.writeValueAsString(orderDetail));
        } catch (Exception e) {
            log.error("反结账[打印退款单推送]失败", e);
        }
    }

    /**
     * 修改打印退款单商品小计价格
     *
     * @param returnPrintDineInItemDTOS 打印商品
     * @param refundOrderBO             退款对象
     */
    private void updatePrintRefundPrice(List<DineInItemDTO> returnPrintDineInItemDTOS, RefundOrderBO refundOrderBO) {
        if (CollectionUtils.isEmpty(refundOrderBO.getOrderItemExtendsList())) {
            return;
        }
        // 修改商品小计价格
        for (DineInItemDTO itemDTO : returnPrintDineInItemDTOS) {
            // 将单价、小计价格、优惠后价格修改为退款价格
            for (OrderItemExtendsDO orderItemExtendsDO : refundOrderBO.getOrderItemExtendsList()) {
                if (String.valueOf(orderItemExtendsDO.getGuid()).equals(itemDTO.getGuid())) {
                    itemDTO.setPrice(orderItemExtendsDO.getRefundPrice());
                    itemDTO.setItemPrice(orderItemExtendsDO.getRefundPrice());
                    itemDTO.setDiscountTotalPrice(orderItemExtendsDO.getRefundPrice());
                }
            }
        }
    }

    /**
     * 获取退款方式文案
     *
     * @param refundOrderBO 对象信息
     * @return 退款方式文案
     */
    private String getPrintRefundPayType(RefundOrderBO refundOrderBO) {
        StringJoiner stringJoiner = new StringJoiner(",");
        if (!CollectionUtils.isEmpty(refundOrderBO.getTransactionRecordList())) {
            for (TransactionRecordDO aDo : refundOrderBO.getTransactionRecordList()) {
                stringJoiner.add(aDo.getPaymentTypeName());
            }
        } else if (!CollectionUtils.isEmpty(refundOrderBO.getMultipleTransactionRecordList())) {
            stringJoiner.add(PaymentTypeEnum.AGG.getDesc());
        }
        return stringJoiner.toString();
    }

    /**
     * 获取退款方式文案
     *
     * @param actuallyPayFeeDetailDTOS 退款方式
     * @return 退款方式文案
     */
    private String getPrintRefundPayType(List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS) {
        StringJoiner stringJoiner = new StringJoiner(",");
        if (!CollectionUtils.isEmpty(actuallyPayFeeDetailDTOS)) {
            for (ActuallyPayFeeDetailDTO detailDTO : actuallyPayFeeDetailDTOS) {
                stringJoiner.add(detailDTO.getPaymentTypeName());
            }
        }
        return stringJoiner.toString();
    }

    /**
     * 退款操作获取附加费
     *
     * @param appendFeeList 附加费信息
     * @return 查询结果
     */
    private List<AdditionalCharge> getAdditionalChargeList(List<AppendFeeDO> appendFeeList) {
        if (!CollectionUtils.isEmpty(appendFeeList)) {
            // 封装参数，金额设置为自定义退款分摊金额
            return appendFeeList.stream()
                    .map(item -> {
                        AdditionalCharge additionalCharge = new AdditionalCharge(item.getName(),
                                item.getRefundShareAmount(), item.getRefundShareAmount());
                        // 设置数量
                        additionalCharge.setCount(item.getRefundCount());
                        return additionalCharge;
                    })
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 反结账操作获取附加费
     *
     * @param orderFeeDetailDTO 订单金额明细
     * @return 查询结果
     */
    private List<AdditionalCharge> getAdditionalChargeList(OrderFeeDetailDTO orderFeeDetailDTO) {
        if (Objects.isNull(orderFeeDetailDTO) || CollectionUtils.isEmpty(orderFeeDetailDTO.getAppendFeeDetailDTOS())) {
            return Collections.emptyList();
        }
        return orderFeeDetailDTO.getAppendFeeDetailDTOS().stream()
                .map(item -> new AdditionalCharge(item.getName(),
                        item.getAmount(), item.getUnitPrice()))
                .collect(Collectors.toList());
    }
}
