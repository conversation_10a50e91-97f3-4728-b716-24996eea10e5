package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.dto.trade.DebtUnitPageReqDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitPageRespDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitSaveReqDTO;
import com.holderzone.saas.store.trade.entity.domain.DebtUnitDO;
import com.holderzone.saas.store.trade.entity.dto.DebtCreditChangeDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitDropdownListDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 挂账单位Mapper
 *
 * <AUTHOR>
 * @since 2020-12-15
 */
@Repository
public interface DebtUnitMapper extends BaseMapper<DebtUnitDO> {

    /**
     * 检查单位名称是否已存在
     * @param reqDTO DebtUnitSaveReqDTO
     * @return 是否
     */
    Boolean nameExists(@Param("req") DebtUnitSaveReqDTO reqDTO);

    /**
     * 检查单位代码是否已存在
     * @param reqDTO DebtUnitSaveReqDTO
     * @return 是否
     */
    Boolean codeExists(@Param("req") DebtUnitSaveReqDTO reqDTO);

    /**
     * 检查电话是否已存在
     * @param reqDTO DebtUnitSaveReqDTO
     * @return 是否
     */
    Boolean telExists(@Param("req") DebtUnitSaveReqDTO reqDTO);

    /**
     * 分页查询挂账单位列表
     * @param page 分页
     * @param reqDTO DebtUnitPageReqDTO
     * @return 单位列表
     */
    IPage<DebtUnitPageRespDTO> unitPage(IPage<DebtUnitPageRespDTO> page, @Param("req") DebtUnitPageReqDTO reqDTO);

    /**
     * 挂账单位列表（下拉框显示）
     * @return 单位列表
     */
    List<DebtUnitDropdownListDTO> unitDropdownList();

    /**
     * 信用额度变更
     * @param changeDTO DebtCreditChangeDTO
     */
    Boolean creditLimitChange(@Param("changeDTO") DebtCreditChangeDTO changeDTO);

}
