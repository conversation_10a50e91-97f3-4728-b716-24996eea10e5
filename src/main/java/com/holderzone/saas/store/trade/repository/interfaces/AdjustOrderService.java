package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderRespDTO;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDO;

import java.util.List;


/**
 * 调整单 service
 */
public interface AdjustOrderService extends IService<AdjustOrderDO> {

    /**
     * 调整单-分页列表
     *
     * @param queryDTO 门店guid
     * @return 调整单列表
     */
    Page<AdjustOrderRespDTO> pageAdjustOrder(AdjustOrderQueryDTO queryDTO);

    /**
     * 查询调整单详情（根据调整单guid查询）
     */
    AdjustOrderDetailRespDTO getSingleOrderDetail(AdjustOrderQueryDTO queryDTO);

    AdjustOrderDO getByGuid(Long guid);

    AdjustOrderDO top1ByOrderGuid(Long orderGuid);

    void saveOrder(AdjustOrderDO adjustOrderDO);

    List<Long> listByOrderGuids(List<Long> orderGuids);
}
