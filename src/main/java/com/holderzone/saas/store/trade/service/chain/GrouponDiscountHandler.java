package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.GrouponCalculateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-06-25
 * @description
 */
@Component
@Slf4j
@AllArgsConstructor
public class GrouponDiscountHandler extends DiscountHandler {

    private final MemberTerminalClientService memberTerminalClientService;

    /**
     * 1.团购验券-套餐券
     * 先团购,再第三方
     */
    @Override
    void dealDiscount(DiscountContext context) {
        DineinOrderDetailRespDTO orderDetailRespDTO = context.getOrderDetailRespDTO();
        Map<Integer, DiscountDO> discountTypeMap = context.getDiscountTypeMap();
        List<DineInItemDTO> allItems = context.getAllItems();
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = context.getDiscountFeeDetailDTOS();
        // 美团团购
        GrouponCalculateUtil.handleGroupon(orderDetailRespDTO, true, discountTypeMap, allItems,
                discountFeeDetailDTOS, GroupBuyTypeEnum.MEI_TUAN, type());
        // 抖音团购
        GrouponCalculateUtil.handleGroupon(orderDetailRespDTO, true, discountTypeMap, allItems,
                discountFeeDetailDTOS, GroupBuyTypeEnum.DOU_YIN, DiscountTypeEnum.DOU_YIN_GROUPON.getCode());
        // 支付宝
        GrouponCalculateUtil.handleGroupon(orderDetailRespDTO, true, discountTypeMap, allItems,
                discountFeeDetailDTOS, GroupBuyTypeEnum.ALIPAY, DiscountTypeEnum.ALIPAY_GROUPON.getCode());
        // 农行
        GrouponCalculateUtil.handleGroupon(orderDetailRespDTO, true, discountTypeMap, allItems,
                discountFeeDetailDTOS, GroupBuyTypeEnum.ABC, DiscountTypeEnum.ABC_GROUPON.getCode());

        // 优惠互斥处理：默认使用团购券，会导致商品券、会员价、单品折扣、代金券，会员折扣，满减、满折、整单折扣无效，可通过第三方活动进行优惠共享设置
        if (context.isRejectDiscount()) {
            grouponMutualExclusionDealWith(context);
        }
    }

    /**
     * 优惠互斥处理：商品券、会员价、单品折扣、代金券，会员折扣，满减、满折、整单折扣
     */
    private void grouponMutualExclusionDealWith(DiscountContext context) {
        // 互斥优惠清零
        zeroDiscount(context, DiscountTypeEnum.MEMBER_GROUPON.getCode());
        zeroDiscount(context, DiscountTypeEnum.ACTIVITY.getCode());
        // 使用团购券时撤销所有会员优惠券
        if (context.isHasMember()) {
            boolean member = memberTerminalClientService.cancelAll(context.getOrderDO().getMemberConsumptionGuid());
            if (member) {
                log.info("会员优惠券撤销成功");
            } else {
                log.error("会员优惠券撤销失败");
            }
        }
        context.getOrderDetailRespDTO().setSingleItemUsedMemeberPrice(0);
        zeroDiscount(context, DiscountTypeEnum.MEMBER.getCode());
        zeroDiscount(context, DiscountTypeEnum.WHOLE.getCode());
        zeroDiscount(context, DiscountTypeEnum.SINGLE_MEMBER.getCode());
        zeroDiscount(context, DiscountTypeEnum.SINGLE_DISCOUNT.getCode());
    }

    private void zeroDiscount(DiscountContext context, int code) {
        DiscountDO discountDO = context.getDiscountTypeMap().get(code);
        if (discountDO != null) {
            DiscountFeeDetailDTO discountFeeDetailDTO = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(discountDO);
            discountFeeDetailDTO.setDiscountFee(BigDecimal.ZERO);
            context.getDiscountFeeDetailDTOS().add(discountFeeDetailDTO);
        }
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.GROUPON.getCode();
    }
}
