package com.holderzone.saas.store.trade.entity.enums;

import com.holderzone.saas.store.trade.entity.constant.CommonConstant;

/**
 * <AUTHOR>
 * @create 2023-07-06
 * @description 聚合支付状态
 */
public enum AggCallBackStateEnum {
    /**
     * 支付成功
     */
    SUCCESS,

    /**
     * 进行中
     */
    PROCESS,

    /**
     * 失败
     */
    FAIL;

    private final static String TO_BE_PAY = "待支付";
    public static AggCallBackStateEnum transferRsp(String code,String paySet,String msg){
        if(CommonConstant.AGG_SUCCESS.equals(code) && paySet.equals(AggPayStateEnum.SUCCESS.getCode())){
            return SUCCESS;
        }
        if(AggPayStateEnum.PENDING.getCode().equals(paySet)){
            return PROCESS;
        }
        if(AggPayStateEnum.FAILURE.getCode().equals(paySet) && msg.equals(TO_BE_PAY)){
            return PROCESS;
        }
        return FAIL;
    }
}
