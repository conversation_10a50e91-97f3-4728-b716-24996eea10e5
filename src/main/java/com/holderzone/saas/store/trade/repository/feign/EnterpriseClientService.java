package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.enterprise.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;


@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseClientService.EnterClientFallBack.class)
public interface EnterpriseClientService {


    @ApiOperation(value = "根据主键查询单个企业", notes = "uid、企业guid、企业电话必传其一（其他值无效）")
    @PostMapping("/enterprise/find")
    EnterpriseDTO findEnterprise(@RequestBody EnterpriseQueryDTO query);


    @GetMapping("/multi/member/list")
    List<MultiMemberDTO> list(@RequestBody Set<String> multiMemberGuid);

    @Slf4j
    @Component
    class EnterClientFallBack implements FallbackFactory<EnterpriseClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public EnterpriseClientService create(Throwable throwable) {
            return new EnterpriseClientService() {

                @Override
                public EnterpriseDTO findEnterprise(EnterpriseQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "findEnterprise", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<MultiMemberDTO> list(Set<String> multiMemberGuid) {
                    log.error(HYSTRIX_PATTERN, "list", JacksonUtils.writeValueAsString(multiMemberGuid),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
