package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.common.enums.OrderSourceEnum;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityDetailsReqDTO;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestBaseInfo;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDiscountInfo;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestOrderInfo;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestPayInfo;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.order.*;
import com.holderzone.saas.store.dto.business.brand.BrandConfigDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.call.PayCallMemberDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.config.resp.FinishFoodRespDTO;
import com.holderzone.saas.store.dto.invoice.RequestGenerateInvoiceDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.ludou.LudouMemberPayRespDTO;
import com.holderzone.saas.store.dto.marketing.specials.UniteActivityOrderRefundDTO;
import com.holderzone.saas.store.dto.member.pay.RedPacketOrderSettleCallBackQO;
import com.holderzone.saas.store.dto.member.pay.RedPacketSettleAccountsVO;
import com.holderzone.saas.store.dto.member.request.MemberConsumeReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberPayDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.*;
import com.holderzone.saas.store.dto.order.request.item.BatchItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.dto.order.request.item.ItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.*;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderGiftRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.reserve.NotifyPayReqDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRecoveryDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRefundDTO;
import com.holderzone.saas.store.dto.table.BaseTableDTO;
import com.holderzone.saas.store.dto.table.TableWhetherOpenDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitRecordSaveReqDTO;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.dto.weixin.deal.FastFoodAutoDistributeTaskDTO;
import com.holderzone.saas.store.dto.weixin.req.WxUpdateOrderRecordStateReqDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.marketing.QueryTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.RuleTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.enums.weixin.WxOrderStateEnum;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.trade.constants.AggCallbackResultConstants;
import com.holderzone.saas.store.trade.constants.ExceptionConstants;
import com.holderzone.saas.store.trade.constants.PaymentInitiateMethod;
import com.holderzone.saas.store.trade.context.LocalizeSynchronizeContext;
import com.holderzone.saas.store.trade.entity.bo.AggPayAttachDataBO;
import com.holderzone.saas.store.trade.entity.constant.CommonConstant;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.dto.BillPayInfoDTO;
import com.holderzone.saas.store.trade.entity.enums.*;
import com.holderzone.saas.store.trade.event.delay.FastFoodAutoDistributeListener;
import com.holderzone.saas.store.trade.helper.*;
import com.holderzone.saas.store.trade.repository.external.LudouMemberExternalService;
import com.holderzone.saas.store.trade.repository.feign.*;
import com.holderzone.saas.store.trade.repository.impls.ItemAttrServiceImpl;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.*;
import com.holderzone.saas.store.trade.service.converter.OrderConverter;
import com.holderzone.saas.store.trade.service.inner.interfaces.AsyncTradeService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.*;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant.ORDER_ITEM_RECORD;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillServiceImpl
 * @date 2019/01/28 17:28
 * @description ..
 * @program holder-saas-store-dto
 */
@Slf4j
@Service
public class BillServiceImpl implements BillService {
    private static final OrderTransform orderTransform = OrderTransform.INSTANCE;

    private static final String RECOVERY_MARK = "F";

    private static final String REFUND_MARK = "T";

    @Resource
    private OrderDetailService orderDetailService;

    @Resource(name = "checkOutThreadPool")
    private ExecutorService checkOutThreadPool;

    @Value("${mdm.host:#{null}}")
    private String mdmRequestHost;

    @Value("${member.center.hostUrl}")
    private String memberCenterHostUrl;

    private final OrderService orderService;

    private final ItemClientService itemClientService;

    private final OrderItemService orderItemService;

    private final OrderItemRecordService orderItemRecordService;

    private final ItemAttrServiceImpl itemAttrService;

    private final FreeReturnItemService freeReturnItemService;

    private final BusinessClientService businessClientService;

    private final DiscountService discountService;

    private final DynamicHelper dynamicHelper;

    private final TransactionRecordService transactionRecordService;

    private final DineInPrintService dineInPrintService;

    private final KdsService kdsService;

    private final AggPayClientService aggPayClientService;

    private final MessageClientService messageClientService;

    private final TableService tableService;

    private final TableClientService tableClientService;

    private final StoreClientService storeClientService;

    private final AppendFeeService appendFeeService;

    private final GrouponMpService grouponMpService;

    private final MessagePushHelper messagePushHelper;

    private final AppendFeeMpService appendFeeMpService;

    @Resource
    @Lazy
    private CalculateService calculateService;

    private OrderLockService orderLockService;

    private final RedisService redisService;

    @Autowired
    private ErpClientService erpClientService;

    private MessageService messageService;

    private final AsyncTradeService asyncTradeService;

    private final RedisDelayedQueue redisDelayedQueue;

    private final OrganizationClientService organizationClientService;

    @Resource
    @Lazy
    private OrderTableBillService orderTableBillService;

    @Resource
    private RedisHelper redisHelper;

    @Autowired
    private DebtUnitRecordService debtUnitRecordService;
    @Autowired
    private DebtUnitService debtUnitService;

    private final MemberTerminalClientService memberTerminalClientService;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Value("${erp.host}")
    private String erpHost;

    private final ThirdActivityClientService thirdActivityClientService;

    private final IThirdActivityRecordService thirdActivityRecordService;

    @Resource
    private PadOrderService padOrderService;

    @Resource
    private PrintClientService printClientService;

    @Resource
    private GrouponService grouponService;

    @Resource
    private OrderExtendsService orderExtendsService;

    @Resource
    private OrderItemExtendsService orderItemExtendsService;

    @Resource
    private TcdOrderService tcdOrderService;

    @Resource
    private DineInItemService dineInItemService;

    @Resource
    private OrderItemChangesService orderItemChangesService;

    @Resource
    private OrderMultiMemberService orderMultiMemberService;

    @Resource
    private MemberMarketingClientService memberMarketingClientService;

    @Resource
    private ReserveClientService reserveClientService;

    @Resource
    private IMultipleTransactionRecordService multipleTransactionRecordService;

    @Resource
    private WxStoreClientService wxStoreClientService;

    @Resource
    private LudouMemberExternalService ludouMemberExternalService;

    @Resource
    @Lazy
    private DineInService dineInService;

    @Resource
    private ExecuteStockReduceService executeStockReduceService;

    @Resource
    private OrderRefundPrintService orderRefundPrintService;

    @Resource(name = "erpStockExecutor")
    private ExecutorService erpStockExecutor;

    @Resource(name = "memberMarketingExecutor")
    private ExecutorService memberMarketingThreadPool;

    @Resource(name = "asyncMemberMarketingOrderExecutor")
    private ExecutorService asyncMemberMarketingOrderExecutor;

    @Resource(name = "orderSaveExecutor")
    private ExecutorService orderSaveExecutor;

    //是否手动清台
    private static final String HANDLE_CLOSE_FLAG_REDIS_KEY = "CLOSE_TABLE_FLAG_REDIS_KEY:";

    //开票二维码RedisKey
    public static final String INVOICE_CODE_PRINT_REDIS_KEY = "INVOICE_CODE_PRINT_REDIS_KEY:";

    @Autowired
    public BillServiceImpl(OrderService orderService, ItemClientService itemClientService,
                           OrderItemService orderItemService, ItemAttrServiceImpl itemAttrService,
                           FreeReturnItemService freeReturnItemService,
                           BusinessClientService businessClientService, DiscountService discountService,
                           DynamicHelper dynamicHelper, TransactionRecordService transactionRecordService,
                           DineInPrintService dineInPrintService, KdsService kdsService,
                           AggPayClientService aggPayClientService, MessageClientService messageClientService,
                           TableService tableService, TableClientService tableClientService,
                           StoreClientService storeClientService,
                           AppendFeeService appendFeeService, GrouponMpService grouponMpService,
                           MessagePushHelper messagePushHelper, AppendFeeMpService appendFeeMpService,
                           OrderLockService orderLockService,
                           RedisService redisService, MessageService messageService,
                           AsyncTradeService asyncTradeService, OrderItemRecordService orderItemRecordService,
                           RedisDelayedQueue redisDelayedQueue, OrganizationClientService organizationClientService,
                           MemberTerminalClientService memberTerminalClientService,
                           ThirdActivityClientService thirdActivityClientService,
                           IThirdActivityRecordService thirdActivityRecordService) {
        this.orderService = orderService;
        this.itemClientService = itemClientService;
        this.orderItemService = orderItemService;
        this.itemAttrService = itemAttrService;
        this.freeReturnItemService = freeReturnItemService;
        this.businessClientService = businessClientService;
        this.discountService = discountService;
        this.dynamicHelper = dynamicHelper;
        this.transactionRecordService = transactionRecordService;
        this.dineInPrintService = dineInPrintService;
        this.kdsService = kdsService;
        this.aggPayClientService = aggPayClientService;
        this.messageClientService = messageClientService;
        this.tableService = tableService;
        this.tableClientService = tableClientService;
        this.storeClientService = storeClientService;
        this.appendFeeService = appendFeeService;
        this.grouponMpService = grouponMpService;
        this.messagePushHelper = messagePushHelper;
        this.appendFeeMpService = appendFeeMpService;
        this.orderLockService = orderLockService;
        this.redisService = redisService;
        this.messageService = messageService;
        this.asyncTradeService = asyncTradeService;
        this.orderItemRecordService = orderItemRecordService;
        this.redisDelayedQueue = redisDelayedQueue;
        this.organizationClientService = organizationClientService;
        this.memberTerminalClientService = memberTerminalClientService;
        this.thirdActivityClientService = thirdActivityClientService;
        this.thirdActivityRecordService = thirdActivityRecordService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EstimateItemRespDTO pay(BillPayReqDTO billPayReqDTO) {
        log.info("支付入参:{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        // 查询订单
        OrderDO orderDO = orderService.getById(billPayReqDTO.getOrderGuid());
        log.info("支付前,order:{}", JacksonUtils.writeValueAsString(orderDO));
        // 校验订单
        EstimateItemRespDTO verifyResult = verifyPayOrder(billPayReqDTO, orderDO, null);
        if (Objects.nonNull(verifyResult)) {
            // is not null 表示直接返回校验结果
            return verifyResult;
        }
        // 支付参数billPayReqDTO 前置处理
        fillBillPayReqDTOBeforeHandler(billPayReqDTO, orderDO);
        log.info("支付参数billPayReqDTO处理之后:{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        // 预更新 order表中参数, 支付业务中需要使用
        payOrderFieldPreHandler(billPayReqDTO, orderDO);
        log.info("orderDO预处理更新:{}", JacksonUtils.writeValueAsString(orderDO));
        // 构建支付记录
        List<BillPayReqDTO.Payment> payments = buildBillPayReqPayments(billPayReqDTO);
        List<TransactionRecordDO> transactionRecordList = buildTransactionPayRecord(billPayReqDTO,
                payments, orderDO.getBusinessDay());
        // 支付/组合支付
        combinePayHandler(orderDO, transactionRecordList, billPayReqDTO);
        // 更新订单优惠
        List<DiscountDO> discountList = updateDiscountAfterPay(billPayReqDTO.getOrderGuid(), billPayReqDTO.getDiscountFeeDetailDTOS());
        // order表中参数 后置处理
        payOrderFieldAfterHandler(billPayReqDTO, orderDO);
        log.info("orderDO支付完成:{}, transactionRecordList:{}", JacksonUtils.writeValueAsString(orderDO),
                JacksonUtils.writeValueAsString(transactionRecordList));
        // 完成支付 持久化订单
        persistenceOrderPayAfterHandler(billPayReqDTO, orderDO, transactionRecordList);
        // 支付成功之后消息通知
        notifyMsgPayOrderAfterHandler(billPayReqDTO, orderDO, discountList);
        // 订单结账完成之后处理
        orderCheckoutCompletedAfterHandler(billPayReqDTO, orderDO);
        // 结账完成
        estimateItemRespDTO.setResult(Boolean.TRUE);
        return estimateItemRespDTO;
    }

    /**
     * 校验订单
     */
    private EstimateItemRespDTO verifyPayOrder(BillPayReqDTO billPayReqDTO, OrderDO orderDO,
                                               DineinOrderDetailRespDTO orderDetail) {
        EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        // 如果支付成功直接返回
        if (orderDO.getState().equals(StateEnum.SUCCESS.getCode())) {
            estimateItemRespDTO.setResult(Boolean.TRUE);
            return estimateItemRespDTO;
        }
        // 校验聚合支付 多次支付
        checkMultipleAggPay(billPayReqDTO.getOrderGuid(), billPayReqDTO.getDeviceType());
        // 校验订单状态
        if (Boolean.TRUE.equals(orderDO.isUnfinished())) {
            throw new BusinessException("订单状态不允许结账");
        }
        // 快餐结账时校验估清 (聚合支付回调中不重复校验估清)
        if (!Boolean.TRUE.equals(billPayReqDTO.getCallbackPayFlag())
                && orderDO.getTradeMode().equals(TradeModeEnum.FAST.getCode())
                && !billPayReqDTO.facePayFlag()) {
            if (Objects.isNull(orderDetail)) {
                // 查询订单详情
                orderDetail = dineInService.getOrderDetail(billPayReqDTO.getOrderGuid());
            }
            EstimateResultRespDTO estimateResult = itemClientService.estimate(orderDetail.getDineInItemDTOS());
            if (!Boolean.TRUE.equals(estimateResult.getSuccess())) {
                estimateItemRespDTO.setResult(Boolean.FALSE);
                estimateItemRespDTO.setEstimate(Boolean.TRUE);
                estimateItemRespDTO.setEstimateInfo(estimateResult.getErrorMsg());
                estimateItemRespDTO.setEstimateSkuGuids(estimateResult.getSkuGuids());
                return estimateItemRespDTO;
            }
        }
        return null;
    }


    /**
     * 支付参数billPayReqDTO 前置处理
     */
    private void fillBillPayReqDTOBeforeHandler(BillPayReqDTO billPayReqDTO, OrderDO orderDO) {
        // 如果是pad订单并且用的子单支付,改为用主单进行支付
        if (billPayReqDTO.padOrderFlag() && orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
            orderDO = orderService.getById(orderDO.getMainOrderGuid());
        }
        // 如果来源是pad，设置会员guid到订单表
        if (billPayReqDTO.padOrderFlag()) {
            orderDO.setMemberGuid(billPayReqDTO.getMemberInfoGuid());
        }
        if (orderDO.getTradeMode().equals(TradeModeEnum.FAST.getCode())) {
            // 是否快餐支付
            billPayReqDTO.setFastFood(Boolean.TRUE);
        }
        // 是否食堂卡支付
        boolean isCanteenPay = billPayReqDTO.getPayments().stream().anyMatch(e ->
                e.getPaymentType() == PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode()
                        || e.getPaymentType() == PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode());
        billPayReqDTO.setCanteenPay(isCanteenPay);
        // 支付方式处理 (团购支付记录)
        paymentsHandler(billPayReqDTO);
    }


    /**
     * 支付参数billPayReqDTO 前置处理
     */
    private void fillBillAggPayReqDTOBeforeHandler(BillAggPayReqDTO billAggPayReqDTO) {
        BillPayReqDTO billPayReqDTO = billAggPayReqDTO.getBillPayReqDTO();
        if (Objects.isNull(billPayReqDTO)) {
            billPayReqDTO = new BillPayReqDTO();
            BeanUtils.copyProperties(billAggPayReqDTO, billPayReqDTO);
            billPayReqDTO.setPayments(Lists.newArrayList());
            billAggPayReqDTO.setBillPayReqDTO(billPayReqDTO);
        }
        billPayReqDTO.setDeviceId(billAggPayReqDTO.getDeviceId());
        billPayReqDTO.setDeviceType(billAggPayReqDTO.getDeviceType());
        billPayReqDTO.setDiscountFeeDetailDTOS(billAggPayReqDTO.getDiscountFeeDetailDTOS());
        billPayReqDTO.setAggPayFlag(true);
    }

    /**
     * 预更新 order表中参数, 支付业务中需要使用
     */
    private void payOrderFieldPreHandler(BillPayReqDTO billPayReqDTO, OrderDO orderDO) {
        orderDO.setIsHandle(-1);
        orderDO.setState(StateEnum.SUCCESS.getCode());
        orderDO.setCheckoutStaffGuid(billPayReqDTO.getUserGuid());
        orderDO.setCheckoutStaffName(billPayReqDTO.getUserName());
        orderDO.setCheckoutTime(LocalDateTime.now());
        orderDO.setCheckoutDeviceType(billPayReqDTO.getDeviceType());
        orderDO.setOrderFee(billPayReqDTO.getOrderFee());
        orderDO.setChangeFee(billPayReqDTO.getChangeFee());
        orderDO.setActuallyPayFee(billPayReqDTO.getActuallyPayFee());
        orderDO.setAppendFee(billPayReqDTO.getAppendFee());
        // 区分积分商城订单
        orderDO.setMemberIntegralStore(billPayReqDTO.getMemberIntegralStore());
        // 查询营业日
        LocalDate businessDay = queryStoreBusinessDay(UserContextUtils.getStoreGuid());
        orderDO.setBusinessDay(businessDay);
    }

    /**
     * 预更新 order表中参数, 支付业务中需要使用
     */
    private void aggPayOrderFieldPreHandler(BillAggPayReqDTO billPayReqDTO, OrderDO orderDO) {
        orderDO.setOrderFee(billPayReqDTO.getOrderFee());
        orderDO.setAppendFee(billPayReqDTO.getAppendFee());
        orderDO.setActuallyPayFee(billPayReqDTO.getActuallyPayFee());
        orderDO.setCheckoutDeviceType(billPayReqDTO.getDeviceType());
        // 查询营业日
        LocalDate businessDay = queryStoreBusinessDay(UserContextUtils.getStoreGuid());
        orderDO.setBusinessDay(businessDay);
    }

    /**
     * order表中参数 后置处理
     */
    private void payOrderFieldAfterHandler(BillPayReqDTO billPayReqDTO, OrderDO orderDO) {
        // 设置超额
        orderDO.setExcessAmount(calculateExcessAmount(orderDO));
        // 订单结账不清台
        orderCloseHandler(billPayReqDTO.getCloseTableFlag(), orderDO);
    }

    /**
     * 支付方式处理
     */
    private void paymentsHandler(BillPayReqDTO billPayReqDTO) {
        List<BillPayReqDTO.Payment> payments = billPayReqDTO.getPayments();
        // 会员需要传递全部的支付方式，但是已经在聚合支付发起的时候保存了记录，因此这里跳过。2、跳过美团团购得方式从优惠信息里面取
        payments.removeIf(p -> p.getPaymentType() == PaymentTypeEnum.AGG.getCode()
                || p.getPaymentType() == PaymentTypeEnum.MT_GROUPON.getCode());
        // 从优惠信息中取团购优惠
        List<BillPayReqDTO.Payment> discountPayList = transferGrouponDiscountList(billPayReqDTO.getDiscountFeeDetailDTOS());
        if (CollectionUtil.isNotEmpty(discountPayList)) {
            payments.addAll(discountPayList);
            log.info("payments更新之后:{}", JacksonUtils.writeValueAsString(payments));
        }
    }

    /**
     * 查询门店营业日
     */
    private LocalDate queryStoreBusinessDay(String storeGuid) {
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        reqDTO.setStoreGuidList(Lists.newArrayList(storeGuid));
        reqDTO.setQueryDateTime(LocalDateTime.now());
        return storeClientService.queryBusinessDay(reqDTO);
    }

    /**
     * 支付完成 持久化处理
     */
    private void persistenceOrderPayAfterHandler(BillPayReqDTO billPayReqDTO, OrderDO orderDO,
                                                 List<TransactionRecordDO> transactionRecordList) {
        orderService.updateById(orderDO);
        // 子单结账
        if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
            checkoutSubOrder(orderDO);
        }
        // 支付方式保存
        transactionRecordService.saveOrUpdateByGuid(billPayReqDTO.getOrderGuid(), transactionRecordList);
        // 并桌转移多次支付记录
        payCombineTransferRecord(orderDO);
        // 支付成功更新会员多卡支付信息
        saveMultiMemberPayRecord(billPayReqDTO);
        // 保存附加费
        if (!Boolean.TRUE.equals(billPayReqDTO.getCallbackPayFlag()) && Objects.equals(TradeModeEnum.DINEIN.getCode(), orderDO.getTradeMode())) {
            List<AppendFeeDO> appendFeeDOList = appendFeeService.persistAppendFee(billPayReqDTO.getOrderGuid());
            persistenceOrderAppendDiscountAmount(orderDO, appendFeeDOList);
        }
        // 支付成功之后保存使用的第三方活动信息
        saveThirdActivityDetails(orderDO);
        // 保存团购实付金额到订单扩展表上
        persistenceOrderCouponBuyAmount(orderDO, transactionRecordList);
    }


    /**
     * 聚合支付前 订单持久化处理
     * 聚合支付没有事务
     */
    private void persistenceOrderAggPayPreHandler(OrderDO orderDO) {
        // 进行状态锁定
        orderDO.setState(StateEnum.PENDING.getCode());
        // 结账后的is_handle
        orderDO.setIsHandle(-1);
        orderService.updateById(orderDO);
    }


    /**
     * 持久化订单团购实付金额
     */
    private void persistenceOrderCouponBuyAmount(OrderDO orderDO,
                                                 List<TransactionRecordDO> transactionRecordList) {
        List<TransactionRecordDO> grouponPayments = transactionRecordList.stream()
                .filter(e -> PaymentTypeEnum.getFilterGrouponPaymentTypes().contains(e.getPaymentType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(grouponPayments)) {
            return;
        }
        log.info("订单使用团购明细:{}", JacksonUtils.writeValueAsString(grouponPayments));
        BigDecimal totalCouponBuyPrice = grouponPayments.stream().map(TransactionRecordDO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        OrderExtendsDO orderExtendsDO = new OrderExtendsDO();
        BeanUtils.copyProperties(orderDO, orderExtendsDO);
        orderExtendsDO.setGuid(orderDO.getGuid());
        orderExtendsDO.setTotalCouponBuyPrice(totalCouponBuyPrice);
        orderExtendsService.saveOrUpdate(orderExtendsDO);
    }


    /**
     * 持久化订单附加费优惠金额
     */
    private void persistenceOrderAppendDiscountAmount(OrderDO orderDO,
                                                      List<AppendFeeDO> appendFeeDOList) {
        if (CollectionUtils.isEmpty(appendFeeDOList)) {
            return;
        }
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderDO.getGuid());
        if (Objects.isNull(orderExtendsDO) || !BigDecimalUtil.greaterThanZero(orderExtendsDO.getTotalAppendDiscountAmount())) {
            return;
        }
        // 附加费金额分摊计算
        log.info("附加费金额分摊计算,orderGuid:{}, appendFeeDOList:{}", orderDO.getGuid(), JacksonUtils.writeValueAsString(appendFeeDOList));
        BigDecimal totalAppendDiscountAmount = orderExtendsDO.getTotalAppendDiscountAmount();
        List<DineInItemDTO> dineInItemList = appendFeeDOList.stream().map(e -> {
            DineInItemDTO dineInItemDTO = new DineInItemDTO();
            dineInItemDTO.setGuid(String.valueOf(e.getGuid()));
            dineInItemDTO.setDiscountTotalPrice(e.getAmount());
            return dineInItemDTO;
        }).collect(Collectors.toList());
        Map<String, BigDecimal> discountAmountMap = PriceCalculationUtils.calculationItemDiscountPrice(totalAppendDiscountAmount, dineInItemList);
        log.info("附加费金额分摊计算完成:{}", JacksonUtils.writeValueAsString(discountAmountMap));
        appendFeeDOList.forEach(e -> e.setDiscountAmount(discountAmountMap.getOrDefault(String.valueOf(e.getGuid()), BigDecimal.ZERO)));
        appendFeeMpService.updateBatchById(appendFeeDOList);
    }

    /**
     * 其他支付完成 订单持久化处理
     * 聚合支付没有事务
     */
    private void persistenceOrderAggPayAfterHandler(BillAggPayReqDTO billPayReqDTO,
                                                    OrderDO orderDO,
                                                    List<TransactionRecordDO> transactionRecordList) {
        boolean success = orderService.updateById(orderDO);
        log.info("更新聚合支付结果，orderGuid={},success={}", orderDO.getGuid(), success);
        if (!success) {
            log.error("发起聚合支付失败，orderDO：{}", JacksonUtils.writeValueAsString(orderDO));
            throw new BusinessException("发起聚合支付失败,请重试");
        }
        // 附加费持久化
        appendFeeService.persistAppendFee(orderDO.getGuid());
        // 保存折扣信息
        updateDiscountAfterPay(billPayReqDTO.getOrderGuid(), billPayReqDTO.getDiscountFeeDetailDTOS());
        // 保存支付记录
        transactionRecordService.saveOrUpdateByGuid(billPayReqDTO.getOrderGuid(), transactionRecordList);
        // 保存订单团购实付总金额
        persistenceOrderCouponBuyAmount(orderDO, transactionRecordList);
    }

    private void notifyMsgPayOrderAfterHandler(BillPayReqDTO billPayReqDTO,
                                               OrderDO orderDO, List<DiscountDO> discountList) {
        if (Objects.equals(RecoveryTypeEnum.GENERAL.getCode(), orderDO.getRecoveryType())) {
            // 支付成功后通知到会员中心 (随行红包)
            DiscountDO followRedPacketDiscountDO = discountList.stream()
                    .filter(e -> DiscountTypeEnum.FOLLOW_RED_PACKET.getCode() == e.getDiscountType())
                    .findFirst()
                    .orElse(null);
            // 随性红包通知
            notifyRedPacketMember(orderDO, followRedPacketDiscountDO);
        }
        // 会员余额支付和食堂卡支付成功消息
        notifyCardPayMsg(billPayReqDTO, orderDO);
    }


    /**
     * 麓豆支付
     */
    private void ludouMemberAggPay(OrderDO orderDO, List<TransactionRecordDO> transactionRecordDOList,
                                   BillPayReqDTO billPayReqDTO) {
        if (Objects.isNull(billPayReqDTO)) {
            return;
        }
        if (CollectionUtils.isEmpty(billPayReqDTO.getPayments())) {
            clearOrderExtendsLudouMemberInfo(Long.valueOf(billPayReqDTO.getOrderGuid()), null);
            return;
        }
        BillPayReqDTO.Payment ludouPayment = billPayReqDTO.getPayments().stream()
                .filter(e -> PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode() == e.getPaymentType())
                .findFirst()
                .orElse(null);
        if (Objects.isNull(ludouPayment)) {
            clearOrderExtendsLudouMemberInfo(Long.valueOf(billPayReqDTO.getOrderGuid()), null);
            return;
        }
        TransactionRecordDO ludouTransactionRecordDO = transactionRecordDOList.stream()
                .filter(e -> PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode() == e.getPaymentType())
                .findFirst()
                .orElse(null);
        if (Objects.isNull(ludouTransactionRecordDO)) {
            ludouTransactionRecordDO = new TransactionRecordDO();
            ludouTransactionRecordDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD));
            ludouTransactionRecordDO.setOrderGuid(Long.valueOf(billPayReqDTO.getOrderGuid()));
            ludouTransactionRecordDO.setPaymentType(PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode());
            ludouTransactionRecordDO.setPaymentTypeName(PaymentTypeEnum.LUDOU_MEMBER_PAY.getDesc());
            transactionRecordDOList.add(ludouTransactionRecordDO);
        }
        ludouTransactionRecordDO.setTerminalId(billPayReqDTO.getDeviceId());
        ludouTransactionRecordDO.setAmount(ludouPayment.getAmount());
        ludouTransactionRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
        ludouTransactionRecordDO.setStaffName(UserContextUtils.getUserName());
        ludouTransactionRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
        ludouTransactionRecordDO.setStoreName(UserContextUtils.getStoreName());
        ludouTransactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
        ludouTransactionRecordDO.setCreateTime(LocalDateTime.now());
        ludouTransactionRecordDO.setBusinessDay(orderDO.getBusinessDay());
        ludouTransactionRecordDO.setState(TradeStateEnum.READY.getCode());
        // 麓豆支付
        ludouMemberPay(orderDO, billPayReqDTO, transactionRecordDOList);
        ludouTransactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
    }

    private List<TransactionRecordDO> buildTransactionAggPayRecord(BillAggPayReqDTO billPayReqDTO, LocalDate businessDay) {
        List<TransactionRecordDO> transactionRecordList = Lists.newArrayList();
        // 聚合支付记录
        TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
        // 获取支付guid
        Long payGuid = getPayGuid(billPayReqDTO, billPayReqDTO.getOrderGuid(), transactionRecordDO, businessDay);
        billPayReqDTO.setPayGuid(payGuid);
        TransactionRecordDO recordDO = transactionRecordService.getById(payGuid);
        log.info("根据生成的payGuid查询recordDO={}", JacksonUtils.writeValueAsString(recordDO));
        if (ObjectUtils.isEmpty(recordDO)) {
            // 生成聚合支付记录
            buildAggPayTransactionRecordDO(billPayReqDTO, payGuid, businessDay, transactionRecordDO);
            transactionRecordList.add(transactionRecordDO);
        }
        List<BillPayReqDTO.Payment> payments = billPayReqDTO.getBillPayReqDTO().getPayments();
        // 其他支付在聚合支付中需生成的支付记录
        List<BillPayReqDTO.Payment> aggPayments = new ArrayList<>(payments);
        List<Integer> aggPaymentTypes = Lists.newArrayList(PaymentTypeEnum.MEMBER.getCode(),
                PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode(),
                PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode(),
                PaymentTypeEnum.DEBT_PAY.getCode());
        aggPayments = aggPayments.stream().filter(e -> aggPaymentTypes.contains(e.getPaymentType())).collect(Collectors.toList());

        // 构建支付记录
        List<TransactionRecordDO> otherTransactionRecordList = buildTransactionPayRecord(billPayReqDTO.getBillPayReqDTO(),
                aggPayments, businessDay);
        if (CollectionUtils.isNotEmpty(otherTransactionRecordList)) {
            transactionRecordList.addAll(otherTransactionRecordList);
        }

        // 部分优惠使用需要转换为支付记录
        List<TransactionRecordDO> discountTransactionRecordList = transferTransactionPayRecord(billPayReqDTO, businessDay);
        if (CollectionUtils.isNotEmpty(discountTransactionRecordList)) {
            transactionRecordList.addAll(discountTransactionRecordList);
        }

        return transactionRecordList;
    }


    /**
     * 如果是聚合支付回调，则排除以下支付方式，因为这些支付方式已经在发起聚合支付时已经生成
     */
    private List<BillPayReqDTO.Payment> buildBillPayReqPayments(BillPayReqDTO billPayReqDTO) {
        if (!Boolean.TRUE.equals(billPayReqDTO.getCallbackPayFlag())) {
            return billPayReqDTO.getPayments();
        }
        List<Integer> aggPaymentTypes = Lists.newArrayList(PaymentTypeEnum.AGG.getCode(),
                PaymentTypeEnum.MEMBER.getCode(), PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode(),
                PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode(), PaymentTypeEnum.DEBT_PAY.getCode(),
                PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode());
        return billPayReqDTO.getPayments().stream()
                .filter(e -> !aggPaymentTypes.contains(e.getPaymentType())).collect(Collectors.toList());
    }

    private List<TransactionRecordDO> buildTransactionPayRecord(BillPayReqDTO billPayReqDTO,
                                                                List<BillPayReqDTO.Payment> payments,
                                                                LocalDate businessDay) {
        List<TransactionRecordDO> transactionRecordList = new ArrayList<>();
        if (CollectionUtils.isEmpty(payments)) {
            return transactionRecordList;
        }
        List<Long> guids = dynamicHelper.generateGuids(GuidKeyConstant.HST_TRANSACTION_RECORD, payments.size());
        for (BillPayReqDTO.Payment payment : payments) {
            TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
            Long guid = guids.remove(0);
            transactionRecordDO.setGuid(guid);
            transactionRecordDO.setOrderGuid(Long.valueOf(billPayReqDTO.getOrderGuid()));
            transactionRecordDO.setTerminalId(billPayReqDTO.getDeviceId());
            transactionRecordDO.setAmount(payment.getAmount());
            transactionRecordDO.setRefundableFee(payment.getAmount());
            transactionRecordDO.setPaymentType(payment.getPaymentType());
            transactionRecordDO.setBankTransactionId(payment.getBankTransactionId());
            //自定义支付或者是团购信息
            if (PaymentTypeEnum.otherOrGroupPay(payment.getPaymentType())) {
                transactionRecordDO.setPaymentTypeName(payment.getPaymentTypeName());
            } else {
                transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.getDesc(payment.getPaymentType()));
            }
            //人脸支付字段
            if (payment.getPaymentType().equals(PaymentTypeEnum.FACE.getCode())) {
                if (StringUtils.isEmpty(payment.getBankTransactionId())) {
                    throw new ParameterException("人脸支付流水号不能为空");
                }
                transactionRecordDO.setFaceCode(payment.getFaceCode());
            }
            if (payment.getPaymentType().equals(PaymentTypeEnum.CARD.getCode())) {
                transactionRecordDO.setFaceCode(payment.getFaceCode());
            }
            transactionRecordDO.setStaffGuid(billPayReqDTO.getUserGuid());
            transactionRecordDO.setStaffName(billPayReqDTO.getUserName());
            transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
            transactionRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
            transactionRecordDO.setStoreName(UserContextUtils.getStoreName());
            transactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
            transactionRecordDO.setCreateTime(LocalDateTime.now());
            transactionRecordDO.setBusinessDay(businessDay);
            transactionRecordList.add(transactionRecordDO);
        }
        return transactionRecordList;
    }

    private List<TransactionRecordDO> transferTransactionPayRecord(BillAggPayReqDTO billPayReqDTO, LocalDate businessDay) {
        // 部分优惠使用需要转换为支付记录
        List<DiscountFeeDetailDTO> discountPayList = billPayReqDTO.getDiscountFeeDetailDTOS().stream()
                .filter(d -> BigDecimalUtil.greaterThanZero(d.getDiscountFee())
                        && (DiscountTypeEnum.THIRD_ACTIVITY.getCode() == d.getDiscountType() || GroupBuyTypeEnum.CODE_LIST.contains(d.getDiscountType())))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(discountPayList)) {
            return Lists.newArrayList();
        }
        List<TransactionRecordDO> transactionRecordDOList = Lists.newArrayList();
        discountPayList.forEach(discountPay -> {
            TransactionRecordDO thirdActivityRecordDO = new TransactionRecordDO();
            Long thirdActivityGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
            thirdActivityRecordDO.setGuid(thirdActivityGuid);
            thirdActivityRecordDO.setOrderGuid(Long.valueOf(billPayReqDTO.getOrderGuid()));
            thirdActivityRecordDO.setTerminalId(billPayReqDTO.getDeviceId());
            thirdActivityRecordDO.setAmount(discountPay.getDiscountFee());
            String rule = discountPay.getRule();
            if (StringUtils.isNotEmpty(rule)) {
                DiscountRuleDTO discountRuleDTO = JacksonUtils.toObject(DiscountRuleDTO.class, rule);
                BigDecimal couponBuyTotalPrice = discountRuleDTO.getCouponBuyTotalPrice();
                if (Objects.nonNull(couponBuyTotalPrice)) {
                    // 团购实际支付金额
                    thirdActivityRecordDO.setAmount(couponBuyTotalPrice);
                    // 优惠金额 = 抵扣金额 - 实际支付金额
                    discountPay.setDiscountFee(discountPay.getDiscountFee().subtract(couponBuyTotalPrice));
                }
            }
            if (discountPay.getDiscountType() == DiscountTypeEnum.THIRD_ACTIVITY.getCode()) {
                thirdActivityRecordDO.setPaymentType(PaymentTypeEnum.THIRD_ACTIVITY.getCode());
                thirdActivityRecordDO.setPaymentTypeName(PaymentTypeEnum.THIRD_ACTIVITY.getDesc());
            } else if (discountPay.getDiscountType() == DiscountTypeEnum.GROUPON.getCode()) {
                thirdActivityRecordDO.setPaymentType(PaymentTypeEnum.MT_GROUPON.getCode());
                thirdActivityRecordDO.setPaymentTypeName(PaymentTypeEnum.MT_GROUPON.getDesc());
            } else {
                thirdActivityRecordDO.setPaymentType(discountPay.getDiscountType());
                thirdActivityRecordDO.setPaymentTypeName(GroupBuyTypeEnum.getDesc(discountPay.getDiscountType()));
            }

            thirdActivityRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
            thirdActivityRecordDO.setStaffName(UserContextUtils.getUserName());
            thirdActivityRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
            thirdActivityRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
            thirdActivityRecordDO.setStoreName(UserContextUtils.getStoreName());
            thirdActivityRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
            thirdActivityRecordDO.setCreateTime(LocalDateTime.now());
            thirdActivityRecordDO.setBusinessDay(businessDay);
            transactionRecordDOList.add(thirdActivityRecordDO);
        });
        return transactionRecordDOList;
    }

    /**
     * 麓豆支付
     */
    private void ludouMemberPay(OrderDO orderDO, BillPayReqDTO billPayReqDTO,
                                List<TransactionRecordDO> transactionRecordList) {
        // 开始麓豆支付
        BillMemberCardCalculateRespDTO ludouMemberDTO = billPayReqDTO.getLudouMemberDTO();
        if (Objects.isNull(ludouMemberDTO)) {
            throw new BusinessException("麓豆支付信息不能为空");
        }
        TransactionRecordDO ludouTransactionRecord = transactionRecordList.stream()
                .filter(e -> PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode() == e.getPaymentType())
                .findFirst()
                .orElse(null);
        if (Objects.isNull(ludouTransactionRecord)) {
            log.error("ludou pay transactionRecord is null, transactionRecordList:{}", JacksonUtils.writeValueAsString(transactionRecordList));
            return;
        }
        billPayReqDTO.setRemark(orderDO.getRemark());
        log.info("麓豆支付入参:{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        LudouMemberPayRespDTO payResp;
        try {
            payResp = ludouMemberExternalService.pay(billPayReqDTO);
        } catch (Exception e) {
            DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(billPayReqDTO.getOrderGuid());
            // 回滚
            ludouTransactionRecord.setState(StateEnum.FAILURE.getCode());
            rollbackCombinePayHandler(orderDO, transactionRecordList, orderDetail);
            throw new BusinessException(e.getMessage());
        }
        // 支付成功
        ludouTransactionRecord.setBankTransactionId(payResp.getTradeNo());
        // 查询订单
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(billPayReqDTO.getOrderGuid());
        addOrderExtendsLudouMemberInfo(orderExtendsDO, billPayReqDTO);
    }

    /**
     * 清除订单麓豆会员信息
     */
    private void clearOrderExtendsLudouMemberInfo(Long orderGuid, OrderExtendsDO orderExtendsDO) {
        if (Objects.isNull(orderExtendsDO)) {
            orderExtendsDO = orderExtendsService.getById(orderGuid);
            if (Objects.isNull(orderExtendsDO)) {
                return;
            }
        }
        orderExtendsDO.setLudouMemberGuid(Strings.EMPTY);
        orderExtendsDO.setLudouMemberName(Strings.EMPTY);
        orderExtendsDO.setLudouMemberPhone(Strings.EMPTY);
        orderExtendsService.saveOrUpdate(orderExtendsDO);
    }

    /**
     * 添加订单麓豆会员信息
     */
    private void addOrderExtendsLudouMemberInfo(OrderExtendsDO orderExtendsDO, BillPayReqDTO billPayReqDTO) {
        if (Objects.isNull(orderExtendsDO)) {
            return;
        }
        BillMemberCardCalculateRespDTO ludouMemberDTO = billPayReqDTO.getLudouMemberDTO();
        orderExtendsDO.setLudouMemberGuid(ludouMemberDTO.getLudouMemberGuid());
        orderExtendsDO.setLudouMemberName(ludouMemberDTO.getLudouMemberName());
        orderExtendsDO.setLudouMemberPhone(ludouMemberDTO.getLudouMemberPhone());
        orderExtendsService.saveOrUpdate(orderExtendsDO);
    }

    /**
     * 订单结账不清台
     */
    private void orderCloseHandler(Integer closeTableFlag, OrderDO orderDO) {
        if (!Objects.equals(BooleanEnum.TRUE.getCode(), closeTableFlag)
                || UpperStateEnum.COMBINE_ORDER_STATE.contains(orderDO.getUpperState())) {
            return;
        }
        log.info("结账不清台业务,closeTableFlag:{}, orderDO:{}", closeTableFlag, JacksonUtils.writeValueAsString(orderDO));
        orderDO.setState(StateEnum.SUB_SUCCESS.getCode());
        // 主单
        if (!Objects.equals(UpperStateEnum.SAME_SUB.getCode(), orderDO.getUpperState())) {
            orderDO.setUpperState(UpperStateEnum.SAME_MAIN.getCode());
        }
        log.info("结账不清台业务,orderDO:{}", JacksonUtils.writeValueAsString(orderDO));
    }

    /**
     * 订单结账完成 补偿处理
     * 1. 订单状态修改为已结账
     * 2. 异步处理
     */
    @Override
    public void orderCheckoutSuccess(BillPayReqDTO billPayReqDTO) {
        if (StringUtils.isNotEmpty(billPayReqDTO.getTableGuid())) {
            // 为了防止前端传入的orderGuid，不是当前桌台关联的orderGuid，则若传入tableGuid，则主动查询当前桌台的orderGuid
            String orderGuid = tableClientService.getOrderGuidByTableGuid(billPayReqDTO.getTableGuid());
            if (StringUtils.isNotEmpty(orderGuid)) {
                billPayReqDTO.setOrderGuid(orderGuid);
            }
        }
        OrderDO orderDO = orderService.getById(billPayReqDTO.getOrderGuid());
        log.info("当前订单信息:{}", orderDO);
        OrderDO mainOrder = orderDO;
        if (UpperStateEnum.COMBINE_ORDER_STATE.contains(orderDO.getUpperState())) {
            if (Objects.nonNull(orderDO.getMainOrderGuid()) && !Objects.equals(0L, orderDO.getMainOrderGuid())) {
                mainOrder = orderService.getById(orderDO.getMainOrderGuid());
            }
            if (Objects.equals(StateEnum.SUCCESS.getCode(), mainOrder.getState())) {
                log.warn("订单已结账");
                return;
            }
            List<OrderDO> orderDOList = orderService.listByMainOrderGuid(String.valueOf(mainOrder.getGuid()));
            combineOrderCheckoutSuccess(orderDOList, mainOrder);
            // 更改桌台状态
            tableService.checkOutChange(billPayReqDTO, orderDO);
            return;
        } else {
            Long mainOrderGuid = orderDO.getMainOrderGuid();
            // 查询子单是否都已结账成功
            List<OrderDO> orderDOList = orderService.otherListByMainOrderGuid(mainOrderGuid);
            mainOrder = orderDOList.stream()
                    .filter(e -> Objects.equals(UpperStateEnum.SAME_MAIN.getCode(), e.getUpperState()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(mainOrder)) {
                throw new BusinessException("订单数据异常，请联系管理员");
            }
            if (Objects.equals(StateEnum.SUCCESS.getCode(), mainOrder.getState())) {
                log.warn("订单已结账");
                return;
            }
            sameOrderCheckoutSuccess(orderDOList, mainOrder);
        }
        // 订单结账完成之后处理
        billPayReqDTO.setCurrentTableOrderGuid(billPayReqDTO.getOrderGuid());
        billPayReqDTO.setCloseTableFlag(BooleanEnum.FALSE.getCode());
        billPayReqDTO.setOrderGuid(String.valueOf(mainOrder.getGuid()));
        orderCheckoutCompletedAfterHandler(billPayReqDTO, mainOrder);
        // 微信订单 需要同步订单状态
        updateWxOrderRecordState(orderDO);
    }

    /**
     * 订单反结账是否超时
     * @param orderGuid 订单guid
     * @return true:超时 false:未超时
     */
    @Override
    public Boolean recoveryTimeLimit(String orderGuid) {
        // 订单不存在则超时
        if (StringUtils.isEmpty(orderGuid)) {
            return true;
        }
        OrderDO orderDO = orderService.getById(orderGuid);
        if (Objects.isNull(orderDO)) {
            return true;
        }
        // 未查询出时效限制则配置则不超时
        BrandConfigDTO brandConfigDTO = businessClientService.getBrandConfigByStoreGuid(orderDO.getStoreGuid());
        log.info("品牌配置返回参数: {}", JacksonUtils.writeValueAsString(brandConfigDTO));
        if (Objects.isNull(brandConfigDTO)) {
            return false;
        }
        Integer recoveryTimeLimitUnit = brandConfigDTO.getRecoveryTimeLimitUnit();
        Integer recoveryTimeLimit = brandConfigDTO.getRecoveryTimeLimit();
        if (Objects.isNull(recoveryTimeLimit)
                || Objects.isNull(recoveryTimeLimitUnit)) {
            return false;
        }
        // 检查是否超时
        return DateUtil.checkRefundTimeLimit(orderDO.getCheckoutTime(), recoveryTimeLimit, recoveryTimeLimitUnit);
    }

    private void updateWxOrderRecordState(OrderDO orderDO) {
        try {
            WxUpdateOrderRecordStateReqDTO wxUpdateOrderRecordStateReqDTO = new WxUpdateOrderRecordStateReqDTO();
            wxUpdateOrderRecordStateReqDTO.setOrderGuid(orderDO.getGuid());
            wxUpdateOrderRecordStateReqDTO.setState(WxOrderStateEnum.PAID.getCode());
            wxStoreClientService.updateOrderRecordState(wxUpdateOrderRecordStateReqDTO);
        } catch (Exception e) {
            log.error("修改微信订单状态异常, ", e);
        }
    }

    private void sameOrderCheckoutSuccess(List<OrderDO> orderDOList, OrderDO mainOrder) {
        // 主单结账状态修改
        mainOrder.setState(StateEnum.SUCCESS.getCode());
        List<OrderDO> subOrderList = orderDOList.stream()
                .filter(e -> !e.getGuid().equals(mainOrder.getGuid())
                        && !Objects.equals(StateEnum.CANCEL.getCode(), e.getState())
                        && !Objects.equals(StateEnum.INVALID.getCode(), e.getState()))
                .collect(Collectors.toList());
        for (OrderDO otherOrder : subOrderList) {
            if (otherOrder.getOrderFee().compareTo(BigDecimal.ZERO) == 0) {
                // 没有加菜的订单
                otherOrder.setState(StateEnum.INVALID.getCode());
                continue;
            }
            if (!Lists.newArrayList(StateEnum.CANCEL.getCode(), StateEnum.INVALID.getCode(), StateEnum.SUB_SUCCESS.getCode())
                    .contains(otherOrder.getState())) {
                throw new BusinessException("订单存在未付款成功的订单，请刷新后重试");
            }
            otherOrder.setState(StateEnum.SUCCESS.getCode());
        }
        OrderDO lastOrder = subOrderList.stream().max(Comparator.comparing(OrderDO::getGmtCreate)).orElse(null);
        if (Objects.nonNull(lastOrder)) {
            // 将主单的就餐人数修改为最后有效一单的就餐人数，方便报表统计
            mainOrder.setGuestCount(lastOrder.getGuestCount());
        }
        if (CollectionUtils.isNotEmpty(orderDOList)) {
            orderService.updateBatchById(orderDOList);
        }
    }

    private void combineOrderCheckoutSuccess(List<OrderDO> orderDOList, OrderDO mainOrder) {
        orderDOList.add(mainOrder);
        List<OrderDO> allOrderList = orderDOList.stream()
                .filter(e -> !Objects.equals(StateEnum.CANCEL.getCode(), e.getState())
                        && !Objects.equals(StateEnum.INVALID.getCode(), e.getState()))
                .collect(Collectors.toList());
        for (OrderDO otherOrder : allOrderList) {
            if (otherOrder.getOrderFee().compareTo(BigDecimal.ZERO) == 0) {
                // 没有加菜的订单
                otherOrder.setState(StateEnum.INVALID.getCode());
                continue;
            }
            if (!Lists.newArrayList(StateEnum.CANCEL.getCode(), StateEnum.INVALID.getCode(), StateEnum.SUB_SUCCESS.getCode())
                    .contains(otherOrder.getState())) {
                throw new BusinessException("订单存在未付款成功的订单，请刷新后重试");
            }
        }
        if (CollectionUtils.isNotEmpty(orderDOList)) {
            orderService.updateBatchById(orderDOList);
        }
    }

    /**
     * 订单结账完成之后处理
     */
    private void orderCheckoutCompletedAfterHandler(BillPayReqDTO billPayReqDTO, OrderDO orderDO) {
        // 订单结账不清台
        if (Objects.equals(BooleanEnum.TRUE.getCode(), billPayReqDTO.getCloseTableFlag())) {
            // 创建订单
            orderTableBillService.dealEnableHandleClose(billPayReqDTO);
            // 更新同桌台订单金额
            orderService.updateSameOrderFeeForCombine(orderDO.getGuid());
            if (UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
                return;
            }
        }
        // 异步执行结账成功后的打印等操作
        DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetails(billPayReqDTO.getOrderGuid());
        orderDetail.setCurrentTableOrderGuid(billPayReqDTO.getCurrentTableOrderGuid());
        // 填充挂账信息
        fillOrderDetailRespDebtInfo(billPayReqDTO, orderDetail);
        log.info("结账最终打印订单信息:{}", JacksonUtils.writeValueAsString(orderDetail));
        // 自动出餐：支付成功后若备餐时间大于0将备餐信息入延迟队列
        autoDistribute(billPayReqDTO.getDeviceType(), billPayReqDTO.getDeviceId(), orderDO);
        // 异步阶段
        asyncTradeService.asyncCheckOutCall(billPayReqDTO, orderDO, orderDetail);
    }

    private void payCombineTransferRecord(OrderDO orderDO) {
        List<TransactionRecordDO> transactionRecordDOList = transactionRecordService.listJhAndPreByOrderGuid(String.valueOf(orderDO.getGuid()));
        if (CollectionUtils.isEmpty(transactionRecordDOList)) {
            return;
        }
        List<TransactionRecordDO> recordDOList = transactionRecordDOList.stream()
                .filter(t -> t.getTradeType().equals(TradeTypeEnum.GENERAL_IN.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(recordDOList)) {
            return;
        }
        TransactionRecordDO transactionRecordDO = recordDOList.get(0);
        TransactionRecordDO updateRecordDO = new TransactionRecordDO();
        updateRecordDO.setGuid(transactionRecordDO.getGuid());
        updateRecordDO.setOrderGuid(transactionRecordDO.getOrderGuid());
        updateRecordDO.setAmount(transactionRecordDO.getAmount());
        updateRecordDO.setRefundableFee(transactionRecordDO.getRefundableFee());
        combineTransferRecord(orderDO, updateRecordDO);
    }

    private void checkMultipleAggPay(String orderGuid, Integer deviceType) {
        if (BaseDeviceTypeEnum.All_IN_ONE.getCode() == deviceType) {
            return;
        }
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderGuid);
        if (!ObjectUtils.isEmpty(orderExtendsDO) && Boolean.TRUE.equals(orderExtendsDO.getIsMultipleAggPay())) {
            throw new BusinessException(ExceptionConstants.ORDER_HAD_MULTIPLE_AGG_PAY);
        }
    }

    private void notMemberPayHandler(OrderDO orderDO, BillPayReqDTO billPayReqDTO, Map<Integer, DiscountFeeDetailDTO> discountFeeDetailDTOMap) {
        List<DiscountFeeDetailDTO> activityDiscountFeeDetails = discountFeeDetailDTOMap.values().stream()
                .filter(e -> (Objects.equals(DiscountTypeEnum.MEMBER_GROUPON.getCode(), e.getDiscountType())
                        || Objects.equals(DiscountTypeEnum.GOODS_GROUPON.getCode(), e.getDiscountType())
                        || Objects.equals(DiscountTypeEnum.ACTIVITY.getCode(), e.getDiscountType())
                        || Objects.equals(DiscountTypeEnum.NTH_ACTIVITY.getCode(), e.getDiscountType()))
                        && BigDecimalUtil.greaterThanZero(e.getDiscountFee())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activityDiscountFeeDetails)) {
            orderDO.setMemberConsumptionGuid("0");
        } else {
            String memberConsumptionGuid = notmemberPay(orderDO, null, billPayReqDTO);
            orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
        }
    }


    /**
     * 查询并更新入参中的挂账单位信息
     */
    private void queryAndUpdateBillPayReqDTODebtInfo(BillPayReqDTO billPayReqDTO) {
        String unitGuid = billPayReqDTO.getUnitGuid();
        if (StringUtils.isEmpty(unitGuid)) {
            return;
        }
        // 查询挂账单位信息
        DebtUnitDO debtUnitDO = debtUnitService.getById(unitGuid);
        if (Objects.isNull(debtUnitDO)) {
            log.error("挂账信息丢失,unitGuid:{}", unitGuid);
            return;
        }
        billPayReqDTO.setUnitName(debtUnitDO.getName());
        billPayReqDTO.setUnitContactName(debtUnitDO.getContactName());
        billPayReqDTO.setUnitContactTel(debtUnitDO.getContactTel());
    }

    /**
     * 保存多会员支付记录
     */
    private void saveMultiMemberPayRecord(BillPayReqDTO billPayReqDTO) {
        List<BillPayReqDTO.Payment> payments = billPayReqDTO.getPayments();
        if (CollectionUtils.isEmpty(payments)) {
            return;
        }
        BillPayReqDTO.Payment memberPayment = payments.stream()
                .filter(e -> Objects.equals(PaymentTypeEnum.MEMBER.getCode(), e.getPaymentType()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(memberPayment)) {
            return;
        }
        List<OrderMultiMemberDTO> multiMembers = memberPayment.getMultiMembers();
        if (CollectionUtils.isEmpty(multiMembers)) {
            return;
        }
        List<OrderMultiMemberDTO> payMultiMembers = multiMembers.stream()
                .filter(e -> BigDecimalUtil.greaterThanZero(e.getAmount()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(payMultiMembers)) {
            return;
        }
        List<OrderMultiMember> updateOrderMultiMembers = payMultiMembers.stream().map(payMultiMember -> {
            OrderMultiMember orderMultiMember = new OrderMultiMember();
            orderMultiMember.setOrderGuid(Long.valueOf(billPayReqDTO.getOrderGuid()));
            orderMultiMember.setMemberCardGuid(payMultiMember.getMemberInfoCardGuid());
            orderMultiMember.setAmount(payMultiMember.getAmount());
            return orderMultiMember;
        }).collect(Collectors.toList());
        orderMultiMemberService.updateBatchPayAmount(updateOrderMultiMembers);
    }

    /**
     * 填充挂账信息
     */
    private void fillOrderDetailRespDebtInfo(BillPayReqDTO billPayReqDTO, DineinOrderDetailRespDTO orderDetail) {
        orderDetail.setDebtUnitName(billPayReqDTO.getUnitName());
        orderDetail.setDebtContactName(billPayReqDTO.getUnitContactName());
        orderDetail.setDebtContactTel(billPayReqDTO.getUnitContactTel());
    }


    private List<DiscountDO> updateDiscountAfterPay(String orderGuid,
                                                    List<DiscountFeeDetailDTO> discountFeeDetailDTOS) {
        List<DiscountDO> discountDOS = orderTransform.discountFeeDetailDTOS2discountDOS(discountFeeDetailDTOS);
        if (CollectionUtils.isEmpty(discountDOS)) {
            return Lists.newArrayList();
        }
        // 查询db
        List<DiscountDO> discountDbList = discountService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isNotEmpty(discountDbList)) {
            // 更新db
            Map<Integer, Long> discountDbMap = discountDbList.stream()
                    .collect(Collectors.toMap(DiscountDO::getDiscountType, DiscountDO::getGuid));
            discountDOS.forEach(e -> e.setGuid(discountDbMap.getOrDefault(e.getDiscountType(), e.getGuid())));
            // 优惠金额变为0 则删除优惠明细
            List<Long> equelZeroDiscountGuids = discountDOS.stream()
                    .filter(e -> BigDecimalUtil.equelZero(e.getDiscountFee()) && discountDbMap.containsValue(e.getGuid()))
                    .map(DiscountDO::getGuid)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(equelZeroDiscountGuids)) {
                discountService.removeByIds(equelZeroDiscountGuids);
            }
        }
        // 处理discountDOS
        // 过滤金额等于0的
        discountDOS.removeIf(d -> BigDecimalUtil.equelZero(d.getDiscountFee()));
        discountDOS.forEach(discountDO -> {
            discountDO.setStaffGuid(UserContextUtils.getUserGuid());
            discountDO.setStaffName(UserContextUtils.getUserName());
            discountDO.setStoreGuid(UserContextUtils.getStoreGuid());
            discountDO.setStoreName(UserContextUtils.getStoreName());
            discountDO.setOrderGuid(Long.valueOf(orderGuid));
        });
        discountService.saveOrUpdateBatch(discountDOS);
        return discountDOS;
    }

    /**
     * 自动出餐：支付成功后若备餐时间大于0将备餐信息入延迟队列
     */
    private void autoDistribute(Integer deviceType, String deviceId, OrderDO orderDO) {
        if (com.holderzone.saas.store.enums.order.TradeModeEnum.DINEIN.getCode() == orderDO.getTradeMode()) {
            log.warn("[autoDistribute]正餐类型不处理自动出餐");
            return;
        }

        // 查询出餐设置
        StoreConfigQueryDTO configQueryDTO = new StoreConfigQueryDTO();
        configQueryDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        FinishFoodRespDTO finishFoodConfig = businessClientService.queryFinishFood(configQueryDTO);
        if (finishFoodConfig.getPrepTime() > 0) {
            if (!Objects.equals(BaseDeviceTypeEnum.All_IN_ONE.getCode(), deviceType)) {
                // 查询一体机设备
                StoreDeviceDTO masterDevice = organizationClientService.findMasterDevice(UserContextUtils.getStoreGuid());
                deviceId = masterDevice.getDeviceGuid();
            }
            FastFoodAutoDistributeTaskDTO taskDTO = FastFoodAutoDistributeTaskDTO.builder()
                    .enterpriseGuid(UserContextUtils.getEnterpriseGuid())
                    .storeGuid(UserContextUtils.getStoreGuid())
                    .storeName(UserContextUtils.getStoreName())
                    .deviceId(deviceId)
                    .orderDesc(orderDO.getMark())
                    .orderGuid(String.valueOf(orderDO.getGuid()))
                    .build();
            redisDelayedQueue.addQueue(taskDTO, finishFoodConfig.getPrepTime()
                    , TimeUnit.MINUTES, FastFoodAutoDistributeListener.class.getName());
        }
    }

    /**
     * 处理使用团购的金额
     * 团购既是作为优惠 又是当作支付方式
     * 被调整为 用户实际支付的钱做为支付方式的金额，抵扣金额-实际支付金额=优惠金额
     */
    private List<BillPayReqDTO.Payment> transferGrouponDiscountList(List<DiscountFeeDetailDTO> discountFeeDetailList) {
        if (CollectionUtil.isEmpty(discountFeeDetailList)) {
            return Collections.emptyList();
        }
        List<DiscountFeeDetailDTO> discountPayList = discountFeeDetailList.stream()
                .filter(d -> GroupBuyTypeEnum.CODE_LIST.contains(d.getDiscountType())
                        && d.getDiscountFee().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(discountPayList)) {
            return Collections.emptyList();
        }
        List<BillPayReqDTO.Payment> payList = Lists.newArrayList();
        discountPayList.forEach(e -> {
            BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
            // 实付金额
            payment.setAmount(e.getDiscountFee());
            if (StringUtils.isNotEmpty(e.getRule())) {
                BigDecimal couponBuyTotalPrice = JacksonUtils.toObject(DiscountRuleDTO.class, e.getRule()).getCouponBuyTotalPrice();
                if (Objects.nonNull(couponBuyTotalPrice)) {
                    // 团购实际支付金额
                    payment.setAmount(couponBuyTotalPrice);
                    // 优惠金额 = 抵扣金额 - 实际支付金额
                    e.setDiscountFee(e.getDiscountFee().subtract(couponBuyTotalPrice));
                }
            }
            payment.setPaymentTypeName(GroupBuyTypeEnum.getDesc(e.getDiscountType()));
            payment.setPaymentType(e.getDiscountType() == GroupBuyTypeEnum.MEI_TUAN.getCode() ?
                    PaymentTypeEnum.MT_GROUPON.getCode() : e.getDiscountType());
            payList.add(payment);
        });
        return payList;
    }

    /**
     * 支付成功后通知到会员中心
     *
     * @param orderDO                 主单信息
     * @param followRedPacketDiscount 随行红包优惠信息
     */
    private void notifyRedPacketMember(OrderDO orderDO, DiscountDO followRedPacketDiscount) {
        log.info("支付后，followRedPacketDiscount={}", JacksonUtils.writeValueAsString(followRedPacketDiscount));
        String orderGuid = String.valueOf(orderDO.getGuid());
        log.info("支付成功后通知到会员中心 orderGuid={}", orderGuid);
        String redPacketInfoKey = RedisKeyUtil.getRedPacketInfoKey(orderGuid);
        if (!Boolean.TRUE.equals(redisHelper.hasKey(redPacketInfoKey))) {
            return;
        }
        List<OrderDO> subOrderList = orderService.listByMainOrderGuid(orderGuid);
        List<RedPacketSettleAccountsVO> redPacketList = redisService.getRedPacketInfo(redPacketInfoKey);
        log.info("从redis获取到redPacketList={}", JacksonUtils.writeValueAsString(redPacketList));
        PayCallMemberDTO payCallMemberDTO = new PayCallMemberDTO();
        payCallMemberDTO.setOperSubjectGuid(UserContextUtils.get().getOperSubjectGuid());
        payCallMemberDTO.setOrderSource(orderDO.getDeviceType());
        payCallMemberDTO.setConsumptionTime(orderDO.getGmtCreate());
        payCallMemberDTO.setStoreGuid(orderDO.getStoreGuid());
        payCallMemberDTO.setStoreName(orderDO.getStoreName());

        List<RedPacketOrderSettleCallBackQO> redPacketCallBackList = buildRedPacketCallBackList(subOrderList,
                orderDO, followRedPacketDiscount, redPacketList);

        payCallMemberDTO.setRedPacketOrderSettleCallBackQOS(redPacketCallBackList);

        String callBackRequestUrl = String.format("%s/hsa_follow/red_packet_order_settle_call_back",
                memberCenterHostUrl).intern();
        log.info("支付成功后通知到会员中心 callBackRequestUrl={}", callBackRequestUrl);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("source", "2");
        try {
            String callBackResult = HttpUtil.doPostJsonHeader(callBackRequestUrl,
                    JacksonUtils.writeValueAsString(payCallMemberDTO), headerMap);
            log.info("支付成功后通知到会员中心 callBackResult={}", callBackResult);
        } catch (IOException e) {
            log.error("支付成功后通知到会员异常:{}", e, e);
        }
        redisService.deleteRedPacketInfo(redPacketInfoKey);
    }

    private List<RedPacketOrderSettleCallBackQO> buildRedPacketCallBackList(List<OrderDO> subOrderList,
                                                                            OrderDO orderDO,
                                                                            DiscountDO followRedPacketDiscount,
                                                                            List<RedPacketSettleAccountsVO> redPacketList) {
        List<RedPacketOrderSettleCallBackQO> redPacketCallBackList = new ArrayList<>();
        if (CollectionUtils.isEmpty(redPacketList)) {
            return redPacketCallBackList;
        }
        int num = redPacketList.size();
        Map<String, OrderDO> subOrderMap = subOrderList.stream().collect(Collectors.toMap(
                sub -> String.valueOf(sub.getGuid()), Function.identity(), (v1, v2) -> v2));
        for (RedPacketSettleAccountsVO r : redPacketList) {
            RedPacketOrderSettleCallBackQO qo = buildRedPacketOrderSettleCallBackQO(num,
                    orderDO, followRedPacketDiscount, subOrderMap, r);
            if (Objects.isNull(qo)) {
                continue;
            }
            redPacketCallBackList.add(qo);
        }
        return redPacketCallBackList;
    }


    private RedPacketOrderSettleCallBackQO buildRedPacketOrderSettleCallBackQO(int num,
                                                                               OrderDO orderDO,
                                                                               DiscountDO followRedPacketDiscount,
                                                                               Map<String, OrderDO> subOrderMap,
                                                                               RedPacketSettleAccountsVO r) {
        RedPacketOrderSettleCallBackQO qo = new RedPacketOrderSettleCallBackQO();
        qo.setOrderNum(r.getOrderNumber());
        qo.setRedPacketAmount(r.getRedPacketSettleAccount());
        qo.setActivityGuid(r.getActivityGuid());
        if (num > 1) {
            OrderDO subOrder = subOrderMap.get(r.getOrderNumber());
            if (!ObjectUtils.isEmpty(followRedPacketDiscount)) {
                qo.setRedPacketActualAmount(followRedPacketDiscount.getDiscountFee());
            }
            if (Objects.equals(String.valueOf(orderDO.getGuid()), r.getOrderNumber())) {
                qo.setOrderNo(orderDO.getOrderNo());
                qo.setOrderAmount(orderDO.getOrderFee());
                qo.setOrderPaidAmount(orderDO.getActuallyPayFee());
                // 传0以判断是不是主单
                qo.setParentOrderNumber("0");
            } else {
                if (ObjectUtils.isEmpty(subOrder)) {
                    log.warn("未获取到对应子单信息 subOrderGuid={}", r.getOrderNumber());
                    return null;
                }
                qo.setOrderNo(subOrder.getOrderNo());
                qo.setOrderAmount(BigDecimal.ZERO);
                qo.setOrderPaidAmount(BigDecimal.ZERO);
                qo.setRedPacketActualAmount(BigDecimal.ZERO);
                qo.setParentOrderNumber(String.valueOf(orderDO.getGuid()));
                qo.setParentOrderNo(orderDO.getOrderNo());
            }
        } else {
            qo.setOrderNo(orderDO.getOrderNo());
            qo.setOrderAmount(orderDO.getOrderFee());
            qo.setOrderPaidAmount(orderDO.getActuallyPayFee());
            if (!ObjectUtils.isEmpty(followRedPacketDiscount)) {
                qo.setRedPacketActualAmount(followRedPacketDiscount.getDiscountFee());
            }
        }
        return qo;
    }

    /**
     * 支付成功之后保存使用的第三方活动信息
     *
     * @param orderDO 订单详情
     */
    private void saveThirdActivityDetails(OrderDO orderDO) {
        UserContext userContext = UserContextUtils.get();
        CompletableFuture.runAsync(() -> {
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            UserContextUtils.put(userContext);
            //团购信息
            List<GrouponDO> grouponList = grouponMpService.listByOrderGuid(String.valueOf(orderDO.getGuid()));
            List<ThirdActivityRecordDO> recordDOList = thirdActivityRecordService.list(new LambdaQueryWrapper<ThirdActivityRecordDO>()
                    .eq(ThirdActivityRecordDO::getOrderGuid, orderDO.getGuid())
                    .eq(ThirdActivityRecordDO::getIsDelete, BooleanEnum.FALSE.getCode())
            );
            log.info("支付成功 查询订单下使用的活动结果 recordDOList={}", JacksonUtils.writeValueAsString(recordDOList));
            if (CollectionUtils.isEmpty(recordDOList)) {
                return;
            }
            List<String> guidList = recordDOList.stream()
                    .map(ThirdActivityRecordDO::getActivityGuid)
                    .collect(Collectors.toList());
            List<ThirdActivityRespDTO> thirdActivityList = thirdActivityClientService.listByGuid(guidList);
            log.info("支付成功 通过活动guidList查询活动列表 thirdActivityList={}", JacksonUtils.writeValueAsString(thirdActivityList));
            if (CollectionUtils.isEmpty(thirdActivityList)) {
                return;
            }
            Map<String, GrouponDO> groupMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(grouponList)) {
                groupMap = grouponList.stream()
                        .filter(e -> ObjectUtil.isNotNull(e.getActivityGuid()))
                        .collect(Collectors.toMap(GrouponDO::getActivityGuid, obj -> obj, (v1, v2) -> v1));
            }
            Map<String, ThirdActivityRespDTO> activityMap = thirdActivityList.stream()
                    .collect(Collectors.toMap(ThirdActivityRespDTO::getGuid, Function.identity(),
                            (dto1, dto2) -> dto2));
            List<ThirdActivityDetailsReqDTO> reqDTOList = new ArrayList<>();
            for (ThirdActivityRecordDO rec : recordDOList) {
                ThirdActivityRespDTO thirdActivity = activityMap.get(rec.getActivityGuid());
                if (ObjectUtils.isEmpty(thirdActivity)) {
                    // 此处应该存在活动
                    log.error("支付成功 未匹配到活动 activityGuid={}", rec.getActivityGuid());
                    continue;
                }
                ThirdActivityDetailsReqDTO dto = new ThirdActivityDetailsReqDTO();
                String guid = String.valueOf(dynamicHelper.generateGuid(GuidKeyConstant.HSM_THIRD_ACTIVITY_DETAILS));
                dto.setGuid(guid);
                dto.setActivityGuid(rec.getActivityGuid());
                dto.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
                dto.setOperSubjectGuid(UserContextUtils.get().getOperSubjectGuid());
                dto.setStoreGuid(UserContextUtils.getStoreGuid());
                dto.setOrderNo(orderDO.getOrderNo());
                dto.setOrderGuid(String.valueOf(orderDO.getGuid()));
                dto.setCheckoutTime(orderDO.getCheckoutTime());
                if (StringUtils.isNotEmpty(orderDO.getMemberPhone())) {
                    dto.setMemberPhone(orderDO.getMemberPhone());
                }
                if (StringUtils.isNotEmpty(orderDO.getMemberName())) {
                    dto.setMemberName(orderDO.getMemberName());
                }
                dto.setOrderFee(orderDO.getOrderFee());
                if (StringUtils.isNotEmpty(rec.getThirdActivityCodes())) {
                    dto.setUseCount(Arrays.asList(rec.getThirdActivityCodes().split(",")).size());
                }
                dto.setCreateUserGuid(UserContextUtils.getUserGuid());
                if (StringUtils.isNotEmpty(rec.getThirdActivityCodes())) {
                    dto.setThirdActivityCodes(rec.getThirdActivityCodes());
                }
                BigDecimal thirdFee;
                BigDecimal thirdCostFee;
                BigDecimal transactionFee = BigDecimalUtil.greaterThanZero(thirdActivity.getTransactionFee()) ?
                        thirdActivity.getTransactionFee() : BigDecimal.ZERO;
                BigDecimal otherFee = BigDecimalUtil.greaterThanZero(thirdActivity.getOtherFee()) ?
                        thirdActivity.getOtherFee() : BigDecimal.ZERO;
                if (RuleTypeEnum.AMOUNT_DEDUCTION.getCode() == thirdActivity.getRuleType() &&
                        StringUtils.isNotEmpty(rec.getThirdActivityCodes())) {
                    int useCount = Arrays.asList(rec.getThirdActivityCodes().split(",")).size();
                    BigDecimal couponFee = BigDecimal.ZERO;
                    BigDecimal couponCostFee = BigDecimal.ZERO;
                    if (thirdActivity.getCouponFee() != null) {
                        couponFee = thirdActivity.getCouponFee();
                    }
                    if (thirdActivity.getCouponFee() == null && groupMap.get(rec.getActivityGuid()) != null) {
                        couponFee = BigDecimalUtil.nonNullValue(groupMap.get(rec.getActivityGuid()).getDeductionAmount());
                    }
                    if (thirdActivity.getCouponCostFee() != null) {
                        couponCostFee = thirdActivity.getCouponCostFee();
                    }
                    if (thirdActivity.getCouponCostFee() == null && groupMap.get(rec.getActivityGuid()) != null) {
                        couponCostFee = BigDecimalUtil.nonNullValue(groupMap.get(rec.getActivityGuid()).getCouponBuyPrice());
                    }
                    thirdFee = BigDecimal.valueOf(useCount).multiply(couponFee);
                    thirdCostFee = BigDecimal.valueOf(useCount).multiply(couponCostFee);
                    dto.setTransactionFee(transactionFee.multiply(BigDecimal.valueOf(useCount)));
                    dto.setOtherFee(otherFee.multiply(BigDecimal.valueOf(useCount)));
                } else {
                    thirdFee = rec.getJoinFee().add(rec.getNotJoinFee());
                    if (0 == thirdActivity.getDiscountType()) {
                        // 折扣：参与金额*折扣+不参与金额
                        thirdCostFee = rec.getJoinFee().multiply(thirdActivity.getDiscount()
                                .divide(new BigDecimal(10), 2, RoundingMode.DOWN)).add(rec.getNotJoinFee());
                    } else {
                        // 满减：订单参与金额除以每满（取整数）*扣减金额+不参与金额
                        BigDecimal divide = rec.getJoinFee().divide(thirdActivity.getFullFee(), RoundingMode.DOWN);
                        thirdCostFee = rec.getJoinFee().subtract(divide.multiply(thirdActivity.getReduceFee()))
                                .add(rec.getNotJoinFee());
                    }
                    transactionFee = thirdFee.multiply(transactionFee
                            .divide(new BigDecimal(100), 2, RoundingMode.DOWN));
                    dto.setTransactionFee(transactionFee);
                    otherFee = thirdFee.multiply(otherFee
                            .divide(new BigDecimal(100), 2, RoundingMode.DOWN));
                    dto.setOtherFee(otherFee);
                }
                dto.setThirdFee(thirdFee);
                dto.setThirdCostFee(thirdCostFee);
                dto.setDiscountFee(thirdFee.subtract(thirdCostFee));
                reqDTOList.add(dto);
            }
            thirdActivityClientService.batchSaveThirdActivityInfo(reqDTOList);
        }).exceptionally(e -> {
            log.error("保存第三方活动记录失败，orderGuid：{}", orderDO.getGuid(), e);
            return null;
        });
    }

    private String deductionIntegral(OrderDO orderDO, BillPayReqDTO billPayReqDTO) {
        RequestDeductionIntegral requestDeductionIntegral = new RequestDeductionIntegral();
        //积分商城
        requestDeductionIntegral.setIntegralOrderType(2);
        requestDeductionIntegral.setUseIntegral(billPayReqDTO.getUseIntegral());
        requestDeductionIntegral.setIntegralDiscountMoney(billPayReqDTO.getIntegralDiscountMoney());

        List<RequestDiscountInfo> discountInfoDTOList = new ArrayList<>();
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = billPayReqDTO.getDiscountFeeDetailDTOS();
        if (CollectionUtil.isNotEmpty(discountFeeDetailDTOS)) {
            for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
                RequestDiscountInfo discountInfoDTO = new RequestDiscountInfo();
                discountInfoDTOList.add(discountInfoDTO);
                discountInfoDTO.setDiscountType(DiscountTypeEnum.get(discountFeeDetailDTO.getDiscountType())
                        .getMemberCode());
                discountInfoDTO.setDiscountPrice(discountFeeDetailDTO.getDiscountFee());
            }
        }

        requestDeductionIntegral.setRequestDiscountInfoList(discountInfoDTOList);
        RequestBaseInfo baseInfoReqDTO = new RequestBaseInfo();
        if (StringUtils.isNotEmpty(billPayReqDTO.getMemberInfoCardGuid())) {
            baseInfoReqDTO.setMemberInfoCardGuid(billPayReqDTO.getMemberInfoCardGuid());
        } else {
            baseInfoReqDTO.setMemberInfoCardGuid(orderDO.getMemberCardGuid());
        }
        if (StringUtils.isNotEmpty(billPayReqDTO.getMemberInfoGuid())) {
            baseInfoReqDTO.setMemberInfoGuid(billPayReqDTO.getMemberInfoGuid());
        } else {
            baseInfoReqDTO.setMemberInfoGuid(orderDO.getMemberGuid());
        }
        baseInfoReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        baseInfoReqDTO.setEnterpriseName("");
        baseInfoReqDTO.setBrandGuid("");
        baseInfoReqDTO.setBrandName("");
        baseInfoReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        baseInfoReqDTO.setStoreName(UserContextUtils.getStoreName());

        requestDeductionIntegral.setRequestBaseInfo(baseInfoReqDTO);
        RequestOrderInfo orderInfoReqDTO = new RequestOrderInfo();
        orderInfoReqDTO.setOrderPaymentAmount(orderDO.getActuallyPayFee());
        orderInfoReqDTO.setOrderRealPaymentAmount(orderDO.getOrderFee());
        orderInfoReqDTO.setOrderDiscountAmount(orderDO.getOrderFee().subtract(orderDO.getActuallyPayFee()));
        orderInfoReqDTO.setConsumptionType(1);
        //消费时间
        if (billPayReqDTO.getConsumptionTime() != null) {
            //离线订单取前端时间
            orderInfoReqDTO.setConsumptionTime(billPayReqDTO.getConsumptionTime());
        } else {
            orderInfoReqDTO.setConsumptionTime(LocalDateTime.now());
        }
        orderInfoReqDTO.setOrderType(0);
        orderInfoReqDTO.setDinnerTableNum(orderDO.getDiningTableName());
        orderInfoReqDTO.setOrderTime(orderDO.getGmtCreate());
        Integer guestCount = orderDO.getGuestCount();
        if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
            guestCount = orderService.getTotalGuestCount(orderDO.getGuid());
        }
        orderInfoReqDTO.setDinnerNum(guestCount);
        if (orderDO.getDeviceType().equals(BaseDeviceTypeEnum.All_IN_ONE.getCode())) {
            orderInfoReqDTO.setOrderSource(1);
        } else if (orderDO.getDeviceType().equals(BaseDeviceTypeEnum.POS.getCode())) {
            orderInfoReqDTO.setOrderSource(2);
        } else if (orderDO.getDeviceType().equals(BaseDeviceTypeEnum.WECHAT.getCode())) {
            orderInfoReqDTO.setOrderSource(0);
        } else {
            orderInfoReqDTO.setOrderSource(2);
        }
        orderInfoReqDTO.setOrderNumber(orderDO.getOrderNo());
        requestDeductionIntegral.setRequestOrderInfo(orderInfoReqDTO);
        return memberTerminalClientService.deductionIntegral(requestDeductionIntegral);

    }

    /**
     * switch (payWay) {
     * case 0: // 现金支付
     * return 1;
     * case 1: // 聚合支付
     * return 2;
     * case 2: // 银联卡支付
     * return 3;
     * default:
     * return 0;
     * }
     *
     * @param payWay
     * @return
     */
    private Integer mapping(Integer payWay) {
        if (payWay == PaymentTypeEnum.OTHER.getCode()) {
            return 5;
        }
        if (payWay == PaymentTypeEnum.DEBT_PAY.getCode()) {
            return 8;
        }
        if (payWay == PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode() || payWay == PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode()
                || payWay == PaymentTypeEnum.THIRD_ACTIVITY.getCode() || payWay == PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode()) {
            return payWay;
        }
        return payWay - 1;
    }

    public List<RequestPayInfo> payment2PayInfo(List<BillPayReqDTO.Payment> payments) {
        // 可能会出现组合支付时payment丢失的问题（如：先调用一次聚合支付，然后现金支付之后，聚合支付的支付方式会丢失的问题）
        List<RequestPayInfo> payList = new ArrayList<>();
        for (BillPayReqDTO.Payment pay : payments) {
            RequestPayInfo payInfo = new RequestPayInfo();
            payInfo.setPayAmount(pay.getAmount());
            payInfo.setPayWay(this.mapping(pay.getPaymentType()));
            payInfo.setPayName(pay.getPaymentTypeName());
            payList.add(payInfo);
        }
        return payList;
    }

    @Override
    public String memberPay(OrderDO orderDO, BillPayReqDTO.Payment payment, BillPayReqDTO billPayReqDTO) {
        if ((StringUtils.isEmpty(orderDO.getMemberCardGuid()) || "0".equals(orderDO.getMemberCardGuid()))
                && StringUtils.isNotEmpty(billPayReqDTO.getMemberInfoCardGuid())) {
            orderDO.setMemberCardGuid(billPayReqDTO.getMemberInfoCardGuid());
        }
        if (StringUtils.isNotEmpty(billPayReqDTO.getMemberInfoCardGuid())
                && !billPayReqDTO.getMemberInfoCardGuid().equals(orderDO.getMemberCardGuid())) {
            orderDO.setMemberCardGuid(billPayReqDTO.getMemberInfoCardGuid());
        }
        RequestConfirmPay confirmPayReqDTO = getMemberPayRequestConfirmPay(orderDO, payment, billPayReqDTO);
        String pay;
        try {
            UserContext userContext = UserContextUtils.get();
            userContext.setStoreName(orderDO.getStoreName());
            ResponseConfirmPay responseConfirmPay = memberTerminalClientService.payInfo(confirmPayReqDTO);
            log.warn("会员支付返回：{}", JacksonUtils.writeValueAsString(responseConfirmPay));
            pay = responseConfirmPay.getMemberConsumptionGuid();
            // 会员卡支付返回信息
            List<OrderMultiMemberPayDTO> orderMultiMemberPayList = OrderTransform.INSTANCE
                    .responseConfirmMultiPay2OrderMultiMemberPayDTO(responseConfirmPay.getConfirmPayList());
            orderDO.setMultiMemberPays(orderMultiMemberPayList);
        } catch (Exception e) {
            //快餐会员支付失败退估清
            DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(billPayReqDTO.getOrderGuid());
            if (orderDetail.getTradeMode().equals(TradeModeEnum.FAST.getCode())) {
                itemClientService.returnEstimate(orderDetail.getDineInItemDTOS());
            }
            throw new BusinessException(e.getMessage());
        }
        return pay;
    }

    @Override
    public String saveMemberPay(OrderDO orderDO, BillPayReqDTO.Payment payment, BillPayReqDTO billPayReqDTO) {
        RequestConfirmPay confirmPayReqDTO = getMemberPayRequestConfirmPay(orderDO, payment, billPayReqDTO);
        String pay;
        try {
            UserContext userContext = UserContextUtils.get();
            userContext.setStoreName(orderDO.getStoreName());
            pay = memberTerminalClientService.savePay(confirmPayReqDTO);
        } catch (Exception e) {
            //快餐会员支付失败:无操作
            throw new BusinessException(e.getMessage());
        }
        log.warn("会员支付返回：{}", pay);

        return pay;
    }

    private RequestConfirmPay getMemberPayRequestConfirmPay(OrderDO orderDO, BillPayReqDTO.Payment payment, BillPayReqDTO billPayReqDTO) {
        RequestConfirmPay confirmPayReqDTO = new RequestConfirmPay();
        handlePaymentsInfo(orderDO, billPayReqDTO, confirmPayReqDTO);
        //判断使用积分的订单类型。积分抵扣还是积分商城
        handleIntegralOrderType(billPayReqDTO, confirmPayReqDTO);

        boolean canteenPay = billPayReqDTO.getCanteenPay() != null && billPayReqDTO.getCanteenPay();
        confirmPayReqDTO.setCanteenPay(billPayReqDTO.getCanteenPay());
        confirmPayReqDTO.setUseIntegral(billPayReqDTO.getUseIntegral());
        confirmPayReqDTO.setIntegralDiscountMoney(billPayReqDTO.getIntegralDiscountMoney());
        // 构建商品信息
        confirmPayReqDTO.setRequestDishInfoList(CommonUtil.dineInItem2DishList(dineInService.getOrderDetail(String
                .valueOf(orderDO.getGuid())).getDineInItemDTOS()));
        // 构建优惠信息
        confirmPayReqDTO.setRequestDiscountInfoList(buildRequestDiscountInfoList(billPayReqDTO));

        if (!canteenPay && StringUtils.isNotEmpty(orderDO.getMemberConsumptionGuid()) && !"0".equals(orderDO.getMemberConsumptionGuid
                ())) {
            confirmPayReqDTO.setMemberConsumptionGuid(orderDO.getMemberConsumptionGuid());
        }
        //若是食堂卡支付
        boolean canteenConsumption = canteenPay && StringUtils.isNotEmpty(orderDO.getCanteenConsumptionGuid())
                && !"0".equals(orderDO.getCanteenConsumptionGuid());
        if (canteenConsumption) {
            confirmPayReqDTO.setMemberConsumptionGuid(orderDO.getCanteenConsumptionGuid());
        }
        if (payment != null) {
            confirmPayReqDTO.setCardBalancePayAmount(payment.getAmount());
        }
        confirmPayReqDTO.setPayPassword(billPayReqDTO.getMemberPassWord());
        confirmPayReqDTO.setNeedPassword(billPayReqDTO.getNeedPassword());

        // 构建公共信息
        confirmPayReqDTO.setRequestBaseInfo(getRequestBaseInfo(orderDO, billPayReqDTO));

        // 构建订单信息
        RequestOrderInfo orderInfoReqDTO = getRequestOrderInfo(orderDO, billPayReqDTO);
        // 处理余出金额
        handelExcessAmount(orderDO, billPayReqDTO, orderInfoReqDTO);

        confirmPayReqDTO.setRequestOrderInfo(orderInfoReqDTO);

        // 多卡支付
        confirmPayReqDTO.setMultiPayList(getOrderMultiPayList(payment));

        log.warn("会员支付入参：{}，guid：{}，orderNo：{}", JacksonUtils.writeValueAsString(confirmPayReqDTO), orderDO.getGuid()
                , orderDO.getOrderNo());
        return confirmPayReqDTO;
    }

    private static void handleIntegralOrderType(BillPayReqDTO billPayReqDTO, RequestConfirmPay confirmPayReqDTO) {
        int integralOrderType = 0;
        if (ObjectUtil.isNotNull(billPayReqDTO.getUseIntegral()) && billPayReqDTO.getUseIntegral() > 0) {
            integralOrderType = 1;
        }
        if (ObjectUtil.equal(1, billPayReqDTO.getMemberIntegralStore())) {
            integralOrderType = 2;
        }
        confirmPayReqDTO.setIntegralOrderType(integralOrderType);
    }


    /**
     * 构建优惠信息
     */
    private List<RequestDiscountInfo> buildRequestDiscountInfoList(BillPayReqDTO billPayReqDTO) {
        List<RequestDiscountInfo> discountInfoDTOList = new ArrayList<>();
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = billPayReqDTO.getDiscountFeeDetailDTOS();
        if (CollectionUtil.isEmpty(discountFeeDetailDTOS)) {
            return discountInfoDTOList;
        }
        for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
            RequestDiscountInfo discountInfoDTO = new RequestDiscountInfo();
            discountInfoDTO.setDiscountType(DiscountTypeEnum.get(discountFeeDetailDTO.getDiscountType())
                    .getMemberCode());
            discountInfoDTO.setDiscountPrice(discountFeeDetailDTO.getDiscountFee());
            // 如果是满减满折活动
            if (Objects.equals(DiscountTypeEnum.ACTIVITY.getCode(), discountFeeDetailDTO.getDiscountType())
                    && StringUtils.isNotEmpty(discountFeeDetailDTO.getRule())) {
                DiscountRuleDTO discountRuleDTO = JacksonUtils.toObject(DiscountRuleDTO.class, discountFeeDetailDTO.getRule());
                discountInfoDTO.setDiscountGuid(discountRuleDTO.getActivityGuid());
            }
            discountInfoDTOList.add(discountInfoDTO);
        }
        return discountInfoDTOList;
    }

    /**
     * 构建订单信息
     */
    private RequestOrderInfo getRequestOrderInfo(OrderDO orderDO, BillPayReqDTO billPayReqDTO) {
        RequestOrderInfo orderInfoReqDTO = new RequestOrderInfo();
        orderInfoReqDTO.setOrderPaymentAmount(orderDO.getActuallyPayFee().add(getCouponBuyPriceByOrderGuid(orderDO.getGuid())));
        orderInfoReqDTO.setOrderRealPaymentAmount(orderDO.getOrderFee());
        orderInfoReqDTO.setOrderDiscountAmount(orderInfoReqDTO.getOrderRealPaymentAmount().subtract(orderInfoReqDTO.getOrderPaymentAmount()));
        orderInfoReqDTO.setConsumptionType(1);
        //消费时间
        if (billPayReqDTO.getConsumptionTime() != null) {
            //离线订单取前端时间
            orderInfoReqDTO.setConsumptionTime(billPayReqDTO.getConsumptionTime());
        } else {
            orderInfoReqDTO.setConsumptionTime(LocalDateTime.now());
        }
        orderInfoReqDTO.setOrderType(0);
        orderInfoReqDTO.setDinnerTableNum(orderDO.getDiningTableName());
        orderInfoReqDTO.setOrderTime(orderDO.getGmtCreate());
        Integer guestCount = orderDO.getGuestCount();
        if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
            guestCount = orderService.getTotalGuestCount(orderDO.getGuid());
        }
        orderInfoReqDTO.setDinnerNum(guestCount);
        //设置会员端订单来源
        orderInfoReqDTO.setOrderSource(baseDeviceTypeToOrderSource(orderDO.getDeviceType()));
        orderInfoReqDTO.setOrderNumber(orderDO.getOrderNo());
        return orderInfoReqDTO;
    }

    /**
     * 构建公共信息参数
     */
    private RequestBaseInfo getRequestBaseInfo(OrderDO orderDO, BillPayReqDTO billPayReqDTO) {
        boolean canteenPay = billPayReqDTO.getCanteenPay() != null && billPayReqDTO.getCanteenPay();
        RequestBaseInfo baseInfoReqDTO = new RequestBaseInfo();
        if (canteenPay || Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), billPayReqDTO.getDeviceType())) {
            baseInfoReqDTO.setMemberInfoCardGuid(billPayReqDTO.getMemberInfoCardGuid());
            baseInfoReqDTO.setMemberInfoGuid(billPayReqDTO.getMemberInfoGuid());
        } else {
            baseInfoReqDTO.setMemberInfoCardGuid(orderDO.getMemberCardGuid());
            baseInfoReqDTO.setMemberInfoGuid(orderDO.getMemberGuid());
        }

        // 如果这样还是为空说明业务有问题，订单里面也没有会员信息
        if (StringUtils.isEmpty(baseInfoReqDTO.getMemberInfoGuid())) {
            baseInfoReqDTO.setMemberInfoGuid(orderDO.getMemberGuid());
        }
        if (StringUtils.isEmpty(baseInfoReqDTO.getMemberInfoCardGuid())) {
            baseInfoReqDTO.setMemberInfoCardGuid(orderDO.getMemberCardGuid());
        }
        baseInfoReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        baseInfoReqDTO.setEnterpriseName("");
        baseInfoReqDTO.setBrandGuid("");
        baseInfoReqDTO.setBrandName("");
        baseInfoReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        baseInfoReqDTO.setStoreName(UserContextUtils.getStoreName());
        return baseInfoReqDTO;
    }

    /**
     * 构建订单多卡支付信息
     */
    private List<RequestConfirmMultiPay> getOrderMultiPayList(BillPayReqDTO.Payment payment) {
        List<OrderMultiMemberDTO> multiMembers = Optional.ofNullable(payment)
                .orElse(new BillPayReqDTO.Payment())
                .getMultiMembers();
        if (Objects.isNull(payment) || CollectionUtils.isEmpty(multiMembers)) {
            return Lists.newArrayList();
        }
        BigDecimal totalAmount = payment.getAmount();
        BigDecimal multiMemberTotalAmount = multiMembers.stream().map(OrderMultiMemberDTO::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("会员支付总金额:{}, 支付明细总金额:{}", totalAmount, multiMemberTotalAmount);
        if (totalAmount.compareTo(multiMemberTotalAmount) != 0) {
            throw new BusinessException("会员余额支付异常，请联系管理员");
        }
        return multiMembers.stream().map(orderMultiMemberDTO -> {
            RequestConfirmMultiPay requestConfirmMultiPay = new RequestConfirmMultiPay();
            BeanUtils.copyProperties(orderMultiMemberDTO, requestConfirmMultiPay);
            return requestConfirmMultiPay;
        }).collect(Collectors.toList());
    }

    private Integer baseDeviceTypeToOrderSource(Integer deviceType) {
        try {
            switch (BaseDeviceTypeEnum.getDeviceTypeByCode(deviceType)) {
                case All_IN_ONE:
                    return OrderSourceEnum.SOURCE_ONE_MACHINE.getCode();
                case CLOUD_PANEL:
                    return OrderSourceEnum.SOURCE_PAD.getCode();
                case WECHAT:
                case TCD:
                    return OrderSourceEnum.SOURCE_WECHAT.getCode();
                case ALI:
                    return OrderSourceEnum.SOURCE_ALI.getCode();
                case POS:
                    return OrderSourceEnum.SOURCE_POS.getCode();
                default:
                    return deviceType;
            }
        } catch (Exception e) {
            log.error("未知设备类型：{}", deviceType);
        }
        return OrderSourceEnum.UNDEFINED.getCode();
    }

    private static void handelExcessAmount(OrderDO orderDO, BillPayReqDTO billPayReqDTO, RequestOrderInfo orderInfoReqDTO) {
        BigDecimal excessAmount = BigDecimal.ZERO;
        Map<Object, DiscountFeeDetailDTO> discountType = CollectionUtil.toMap(billPayReqDTO.getDiscountFeeDetailDTOS(), "discountType");
        DiscountFeeDetailDTO thirdDiscount = discountType.get(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
        if (thirdDiscount != null && BigDecimalUtil.greaterThanZero(thirdDiscount.getDiscountFee())) {
            BigDecimal subtract = thirdDiscount.getDiscountFee().subtract(orderDO.getOrderFee());
            if (BigDecimalUtil.greaterThanZero(subtract)) {
                excessAmount = subtract;
            }
        }
        orderInfoReqDTO.setExcessAmount(excessAmount);
    }

    private void handlePaymentsInfo(OrderDO orderDO, BillPayReqDTO billPayReqDTO, RequestConfirmPay confirmPayReqDTO) {
        if (billPayReqDTO.getPayments() != null) {
            List<BillPayReqDTO.Payment> payments = new ArrayList<>(billPayReqDTO.getPayments());
            List<TransactionRecordDO> recordDOList = transactionRecordService.listByOrderGuid(orderDO.getGuid());
            if (CollectionUtils.isNotEmpty(recordDOList)) {
                TransactionRecordDO aggPayRecordDO = recordDOList.stream()
                        .filter(r -> PaymentTypeEnum.AGG.getCode() == r.getPaymentType() && r.getState() == StateEnum.SUCCESS.getCode())
                        .findFirst().orElse(null);
                if (!ObjectUtils.isEmpty(aggPayRecordDO)) {
                    BillPayReqDTO.Payment payInfo = new BillPayReqDTO.Payment();
                    payInfo.setAmount(aggPayRecordDO.getAmount());
                    payInfo.setPaymentType(aggPayRecordDO.getPaymentType());
                    payInfo.setPaymentTypeName(aggPayRecordDO.getPaymentTypeName());
                    payments.add(payInfo);
                }
            }
            confirmPayReqDTO.setRequestPayInfoList(this.payment2PayInfo(payments));
        }
    }

    /**
     * 查询订单使用团购验券的顾客实际购买金额总和
     */
    private BigDecimal getCouponBuyPriceByOrderGuid(Long orderGuid) {
        List<GrouponDO> grouponList = grouponMpService.listByOrderGuid(String.valueOf(orderGuid));
        if (CollectionUtils.isEmpty(grouponList)) {
            return BigDecimal.ZERO;
        }
        return grouponList.stream()
                .filter(e -> Objects.isNull(e.getRefundOrderGuid()))
                .map(GrouponDO::getCouponBuyPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 非会员优惠活动
     *
     * @param orderDO
     * @param payment
     * @param billPayReqDTO
     * @return
     */
    @Override
    public String notmemberPay(OrderDO orderDO, BillPayReqDTO.Payment payment, BillPayReqDTO billPayReqDTO) {
        RequestActivityPay activityPayReqDTO = new RequestActivityPay();
        if (billPayReqDTO.getPayments() != null) {
            activityPayReqDTO.setPayInfoDTOList(this.payment2PayInfo(billPayReqDTO.getPayments()));
        }
        activityPayReqDTO.setDishInfoDTOList(CommonUtil.dineInItem2DishList(dineInService.getOrderDetail(String
                .valueOf(orderDO.getGuid())).getDineInItemDTOS()));

        List<RequestDiscountInfo> discountInfoDTOList = new ArrayList<>();
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = billPayReqDTO.getDiscountFeeDetailDTOS();
        if (CollectionUtil.isNotEmpty(discountFeeDetailDTOS)) {
            for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
                RequestDiscountInfo discountInfoDTO = new RequestDiscountInfo();
                discountInfoDTOList.add(discountInfoDTO);
                discountInfoDTO.setDiscountType(DiscountTypeEnum.get(discountFeeDetailDTO.getDiscountType())
                        .getMemberCode());
                discountInfoDTO.setDiscountPrice(discountFeeDetailDTO.getDiscountFee());
            }
        }

        activityPayReqDTO.setDiscountInfoDTOList(discountInfoDTOList);

        if (StringUtils.isNotEmpty(orderDO.getMemberConsumptionGuid()) && !"0".equals(orderDO.getMemberConsumptionGuid
                ())) {
            activityPayReqDTO.setMemberConsumptionGuid(orderDO.getMemberConsumptionGuid());
        }

        RequestOrderInfo orderInfoReqDTO = new RequestOrderInfo();
        orderInfoReqDTO.setOrderPaymentAmount(orderDO.getActuallyPayFee());
        orderInfoReqDTO.setOrderRealPaymentAmount(orderDO.getOrderFee());
        orderInfoReqDTO.setOrderDiscountAmount(orderDO.getOrderFee().subtract(orderDO.getActuallyPayFee()));
        orderInfoReqDTO.setConsumptionType(1);
        orderInfoReqDTO.setConsumptionTime(LocalDateTime.now());
        orderInfoReqDTO.setOrderType(0);
        orderInfoReqDTO.setDinnerTableNum(orderDO.getDiningTableName());
        orderInfoReqDTO.setOrderTime(orderDO.getGmtCreate());
        orderInfoReqDTO.setDinnerNum(orderDO.getGuestCount());
        if (orderDO.getDeviceType().equals(BaseDeviceTypeEnum.All_IN_ONE.getCode())) {
            orderInfoReqDTO.setOrderSource(1);
        } else if (orderDO.getDeviceType().equals(BaseDeviceTypeEnum.POS.getCode())) {
            orderInfoReqDTO.setOrderSource(2);
        } else if (orderDO.getDeviceType().equals(BaseDeviceTypeEnum.WECHAT.getCode())) {
            orderInfoReqDTO.setOrderSource(0);
        } else if (orderDO.getDeviceType().equals(BaseDeviceTypeEnum.ALI.getCode())) {
            orderInfoReqDTO.setOrderSource(OrderSourceEnum.SOURCE_ALI.getCode());
        } else {
            orderInfoReqDTO.setOrderSource(2);
        }
        orderInfoReqDTO.setOrderNumber(orderDO.getOrderNo());

        activityPayReqDTO.setOrderInfoReqDTO(orderInfoReqDTO);
        log.warn("非会员支付入参：{}，guid：{}，orderNo：{}", JacksonUtils.writeValueAsString(activityPayReqDTO), orderDO.getGuid()
                , orderDO.getOrderNo());
        String pay = memberTerminalClientService.activityPay(activityPayReqDTO);
        log.warn("非会员支付返回：{}", pay);

        return pay;
    }


    @Override
    public BillAggPayRespDTO aggPay(BillAggPayReqDTO billPayReqDTO) {
        String orderGuid = billPayReqDTO.getOrderGuid();
        BillAggPayRespDTO billAggPayRespDTO = new BillAggPayRespDTO();
        // 查询订单
        OrderDO orderDO = orderService.getById(orderGuid);
        log.info("支付前,order:{}", JacksonUtils.writeValueAsString(orderDO));
        // 查询订单详情
        DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(billPayReqDTO.getOrderGuid());
        log.info("支付前,orderDetail:{}", JacksonUtils.writeValueAsString(orderDetail));
        // 填充支付参数
        fillBillAggPayReqDTOBeforeHandler(billPayReqDTO);
        log.info("填充支付参数之后billPayReqDTO:{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        // 校验订单
        EstimateItemRespDTO verifyResult = verifyPayOrder(billPayReqDTO.getBillPayReqDTO(), orderDO, orderDetail);
        if (Objects.nonNull(verifyResult)) {
            // is not null 表示直接返回校验结果
            BeanUtils.copyProperties(verifyResult, billAggPayRespDTO);
            return billAggPayRespDTO;
        }
        // 聚合支付前 订单持久化处理
        persistenceOrderAggPayPreHandler(orderDO);
        // 聚合支付请求参数预处理
        aggPayBillPayReqDTOPreHandler(billPayReqDTO);
        log.info("预处理入参完成, billPayReqDTO:{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        // 预更新 order表中参数
        aggPayOrderFieldPreHandler(billPayReqDTO, orderDO);
        log.info("orderDO预处理更新:{}", JacksonUtils.writeValueAsString(orderDO));
        // 构建支付记录
        List<TransactionRecordDO> transactionRecordList = buildTransactionAggPayRecord(billPayReqDTO, orderDO.getBusinessDay());
        // 组合支付
        combinePayHandler(orderDO, transactionRecordList, billPayReqDTO.getBillPayReqDTO());
        // 其他支付完成 订单持久化处理
        persistenceOrderAggPayAfterHandler(billPayReqDTO, orderDO, transactionRecordList);

        // 构建聚合支付入参
        SaasAggPayDTO aggPayDTO = buildSaasAggPayDTO(billPayReqDTO);
        String payGUID = aggPayDTO.getReqDTO().getPayGUID();
        // 这里若报出异常，则不会执行缓存的清除：redisService.deleteOrderPaymentInitiateMethod(orderGuid);从而导致无法切换支付方式
        // 解决方式：建议在store-pay 中不捕获异常，返回code。并且保证这个接口不会因为任何原因报错，从而熔断。
        log.info("发起支付，入参：{}", JacksonUtils.writeValueAsString(aggPayDTO));
        AggPayRespDTO payRespDTO = aggPayClientService.pay(aggPayDTO);
        log.info("发起支付，返回：{}", JacksonUtils.writeValueAsString(payRespDTO));

        // 将异常单所需数据对象存入redis
        putAbnormalOrderRecord(billPayReqDTO);

        String code = payRespDTO.getCode();
        if (CommonConstant.AGG_SUCCESS.equalsIgnoreCase(code)) {
            billAggPayRespDTO.setPayGuid(payGUID);
            billAggPayRespDTO.setJhOrderGuid(orderGuid);
            billAggPayRespDTO.setResult(Boolean.TRUE);
            redisService.putOrderPayGuid(orderGuid, payGUID);
            orderDO.setPaymentAppId(payRespDTO.getPaymentAppId());
            orderService.updateById(orderDO);
            //开票二维码字段缓存
            invoiceCache(billPayReqDTO, orderGuid);
        } else {
            // 清除多次支付信息
            clearMultipleRecord(billPayReqDTO);

            // 清除其他支付信息
            if (Objects.nonNull(billPayReqDTO.getBillPayReqDTO())) {
                redisService.deleteBillPayInfo(orderGuid);
            }
            // 清除支付信息
            redisService.deleteOrderPaymentInitiateMethod(orderGuid);
            redisService.deleteOrderPayGuid(orderGuid);

            // 回滚业务
            rollbackCombinePayHandler(orderDO, transactionRecordList, orderDetail);

            // 支付失败
            if (CollectionUtils.isNotEmpty(transactionRecordList)) {
                transactionRecordList.forEach(e -> e.setState(TradeStateEnum.FAILURE.getCode()));
                transactionRecordService.updateBatchById(transactionRecordList);
            }
            orderDO.setState(StateEnum.READY.getCode());
            orderService.updateById(orderDO);
            log.warn("发起聚合支付失败，参数：{}，返回：{}",
                    JacksonUtils.writeValueAsString(aggPayDTO),
                    JacksonUtils.writeValueAsString(payRespDTO));
            throw new BusinessException(String.format("发起聚合支付失败 %s", payRespDTO.getMsg()));
        }
        return billAggPayRespDTO;
    }


    /**
     * 回滚业务
     */
    private void rollbackCombinePayHandler(OrderDO orderDO, List<TransactionRecordDO> transactionRecordList,
                                           DineinOrderDetailRespDTO orderDetail) {
        // 快餐聚合支付失败退估清
        if (orderDetail.getTradeMode().equals(TradeModeEnum.FAST.getCode())) {
            itemClientService.returnEstimate(orderDetail.getDineInItemDTOS());
        }
        TransactionRecordDO memberTransactionRecordDO = transactionRecordList.stream()
                .filter(e -> PaymentTypeEnum.MEMBER.getCode() == e.getPaymentType() && StateEnum.SUCCESS.getCode() == e.getState())
                .findFirst()
                .orElse(null);
        if (Objects.nonNull(memberTransactionRecordDO) || CommonUtil.hasGuid(orderDO.getMemberConsumptionGuid())) {
            RequestCancelPay cancelPayReqDTO = new RequestCancelPay();
            cancelPayReqDTO.setOrderNumber(orderDO.getOrderNo());
            cancelPayReqDTO.setIsSettlement(Boolean.FALSE);
            cancelPayReqDTO.setMemberConsumptionGuid(orderDetail.getMemberConsumptionGuid());
            log.info("发起聚合支付失败撤销会员支付，cancelPayReqDTO：{}", JacksonUtils.writeValueAsString(cancelPayReqDTO));
            String memberConsumptionGuid = memberTerminalClientService.cancelPay(cancelPayReqDTO);
            // 更新订单会员消费guid
            updateOrderMemberConsumptionGuid(Long.valueOf(orderDetail.getGuid()),
                    memberConsumptionGuid, null);
        }

        List<TransactionRecordDO> canteenPayTransactionRecordList = transactionRecordList.stream()
                .filter(e -> (PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode() == e.getPaymentType()
                        || PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode() == e.getPaymentType()) && StateEnum.SUCCESS.getCode() == e.getState())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(canteenPayTransactionRecordList)) {
            RequestCancelPay cancelPayReqDTO = new RequestCancelPay();
            cancelPayReqDTO.setOrderNumber(orderDO.getOrderNo());
            cancelPayReqDTO.setIsSettlement(Boolean.FALSE);
            cancelPayReqDTO.setCanteenPay(true);
            cancelPayReqDTO.setMemberConsumptionGuid(orderDetail.getMemberConsumptionGuid());
            log.info("发起聚合支付失败撤销食堂卡支付，cancelPayReqDTO：{}", JacksonUtils.writeValueAsString(cancelPayReqDTO));
            String memberConsumptionGuid = memberTerminalClientService.cancelPay(cancelPayReqDTO);
            // 更新订单会员消费guid
            updateOrderMemberConsumptionGuid(Long.valueOf(orderDetail.getGuid()),
                    memberConsumptionGuid, memberConsumptionGuid);
        }

        // 聚合支付失败反向添加预付金
        TransactionRecordDO prepayTransactionRecordDO = transactionRecordList.stream()
                .filter(e -> PaymentTypeEnum.RESERVE.getCode() == e.getPaymentType() && StateEnum.SUCCESS.getCode() == e.getState())
                .findFirst()
                .orElse(null);
        if (Objects.nonNull(prepayTransactionRecordDO)) {
            log.info("聚合支付失败反向添加预付金: {}", JacksonUtils.writeValueAsString(prepayTransactionRecordDO));
            transactionRecordService.removeById(prepayTransactionRecordDO.getGuid());
            orderDO.setPrepayFee(prepayTransactionRecordDO.getAmount());
        }

        // 聚合支付失败回退麓豆支付
        callbackLudouMemberPay(transactionRecordList, orderDO);
    }

    private void updateOrderMemberConsumptionGuid(Long orderGuid,
                                                  String memberConsumptionGuid,
                                                  String canteenConsumptionGuid) {
        String jsonStr = UserContextUtils.getJsonStr();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        orderSaveExecutor.execute(() -> {
            UserContextUtils.put(jsonStr);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            OrderDO rollbackOrderDO = new OrderDO();
            rollbackOrderDO.setGuid(orderGuid);
            rollbackOrderDO.setMemberConsumptionGuid(memberConsumptionGuid);
            rollbackOrderDO.setCanteenConsumptionGuid(canteenConsumptionGuid);
            orderService.updateByIdWithDeleteCache(rollbackOrderDO);
        });
    }

    private SaasAggPayDTO buildSaasAggPayDTO(BillAggPayReqDTO billPayReqDTO) {
        SaasAggPayDTO aggPayDTO = orderTransform.billPayReqDTO2SaasAggPayDTO(billPayReqDTO);
        AggPayPreTradingReqDTO payPreTradingReqDTO = new AggPayPreTradingReqDTO();
        payPreTradingReqDTO.setTimestamp(DateTimeUtils.nowMillis());
        payPreTradingReqDTO.setStoreName(UserContextUtils.getStoreName());
        payPreTradingReqDTO.setAmount(billPayReqDTO.getAmount());
        payPreTradingReqDTO.setAuthCode(billPayReqDTO.getAuthCode());
        payPreTradingReqDTO.setTerminalId(billPayReqDTO.getDeviceId());
        payPreTradingReqDTO.setGoodsName("商品");
        payPreTradingReqDTO.setBody("聚合支付订单：" + billPayReqDTO.getOrderGuid());
        // 聚合支付用分为单位
        payPreTradingReqDTO.setAmount(billPayReqDTO.getAmount());
        payPreTradingReqDTO.setOrderGUID(billPayReqDTO.getOrderGuid());

        String payGUID = String.valueOf(billPayReqDTO.getPayGuid());
        if (!ObjectUtils.isEmpty(billPayReqDTO.getMultipleRecordGuid())) {
            payGUID = String.valueOf(billPayReqDTO.getMultipleRecordGuid());
        }
        payPreTradingReqDTO.setPayGUID(payGUID);
        if (billPayReqDTO.getActiveScan() != null && billPayReqDTO.getActiveScan() == BooleanEnum.TRUE.getCode()) {
            payPreTradingReqDTO.setPayPowerId(StringUtils.isEmpty(billPayReqDTO.getPayPowerId()) ?
                    PayPowerId.YL_WX_SCAN_CODE.getId() : billPayReqDTO.getPayPowerId());
        }
        aggPayDTO.setIsLast(billPayReqDTO.getLast());
        aggPayDTO.setCheckoutSuccessFlag(billPayReqDTO.getCheckoutSuccessFlag());
        aggPayDTO.setReqDTO(payPreTradingReqDTO);
        aggPayDTO.setSaasCallBackUrl(CommonConstant.AGG_CALLBACK_URL);
        return aggPayDTO;
    }

    private void buildAggPayTransactionRecordDO(BillAggPayReqDTO billPayReqDTO, Long payGuid, LocalDate businessDay,
                                                TransactionRecordDO transactionRecordDO) {
        transactionRecordDO.setGuid(payGuid);
        transactionRecordDO.setOrderGuid(Long.valueOf(billPayReqDTO.getOrderGuid()));
        transactionRecordDO.setTerminalId(billPayReqDTO.getDeviceId());
        transactionRecordDO.setPaymentType(PaymentTypeEnum.AGG.getCode());
        transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
        transactionRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
        transactionRecordDO.setStaffName(UserContextUtils.getUserName());
        transactionRecordDO.setState(TradeStateEnum.READY.getCode());
        transactionRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
        transactionRecordDO.setStoreName(UserContextUtils.getStoreName());
        transactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
        transactionRecordDO.setCreateTime(LocalDateTime.now());
        transactionRecordDO.setBusinessDay(businessDay);
        if (billPayReqDTO.getActiveScan() != null && billPayReqDTO.getActiveScan() == BooleanEnum.TRUE.getCode()) {
            String payPowerIdStr = StringUtils.isEmpty(billPayReqDTO.getPayPowerId()) ?
                    PayPowerId.YL_WX_SCAN_CODE.getId() : billPayReqDTO.getPayPowerId();
            try {
                int payPowerId = Integer.parseInt(payPowerIdStr);
                transactionRecordDO.setPayPowerId(payPowerId);
            } catch (Exception e) {
                log.error("payPowerId设置失败", e);
            }
        }
    }

    /**
     * 聚合支付请求参数预处理
     */
    private void aggPayBillPayReqDTOPreHandler(BillAggPayReqDTO billPayReqDTO) {
        if (Objects.isNull(billPayReqDTO.getBillPayReqDTO())) {
            return;
        }
        // 将其他支付信息存入Redis
        String memberInfoGuid = billPayReqDTO.getBillPayReqDTO().getMemberInfoGuid();
        String memberInfoCardGuid = billPayReqDTO.getBillPayReqDTO().getMemberInfoCardGuid();
        // 解决前端传递的支付方式缺少聚合支付的问题
        List<BillPayReqDTO.Payment> paymentList = billPayReqDTO.getBillPayReqDTO().getPayments()
                .stream()
                .filter(p -> p.getPaymentType() != null && PaymentTypeEnum.AGG.getCode() != p.getPaymentType())
                .collect(Collectors.toList());
        // 加入聚合支付
        BillPayReqDTO.Payment pay = new BillPayReqDTO.Payment();
        pay.setAmount(billPayReqDTO.getAmount());
        pay.setPaymentType(PaymentTypeEnum.AGG.getCode());
        pay.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
        paymentList.add(pay);
        billPayReqDTO.getBillPayReqDTO().setPayments(paymentList);

        BillPayInfoDTO billPayInfoDTO = new BillPayInfoDTO();
        billPayInfoDTO.setBillPayReqDTO(billPayReqDTO.getBillPayReqDTO());
        BeanUtils.copyProperties(billPayReqDTO, billPayInfoDTO.getBillPayReqDTO());
        billPayInfoDTO.getBillPayReqDTO().setMemberInfoGuid(memberInfoGuid);
        billPayInfoDTO.getBillPayReqDTO().setMemberInfoCardGuid(memberInfoCardGuid);
        billPayInfoDTO.setUserContext(UserContextUtils.get());
        log.info("支付billPayInfoDTO保存redis:{}", billPayInfoDTO);
        redisService.putBillPayInfo(billPayReqDTO.getOrderGuid(), billPayInfoDTO);
        redisService.putOrderPaymentInitiateMethod(billPayReqDTO.getOrderGuid(), PaymentInitiateMethod.AGG_PAY);
        //清台配置
        if (Objects.nonNull(billPayReqDTO.getCloseTableFlag()) && billPayReqDTO.getCloseTableFlag() == BooleanEnum.TRUE.getCode()) {
            log.info("清台配置：{}", billPayReqDTO.getCloseTableFlag());
            redisHelper.set(HANDLE_CLOSE_FLAG_REDIS_KEY + billPayReqDTO.getOrderGuid(),
                    JSON.toJSONString(billPayReqDTO.getCloseTableFlag()));
        }
    }

    /**
     * 其他支付 / 组合支付
     */
    private void combinePayHandler(OrderDO orderDO, List<TransactionRecordDO> transactionRecordDOList,
                                   BillPayReqDTO billPayReqDTO) {
        // 如果是聚合支付回调 则不重复进行以下几种方式的支付
        if (!Boolean.TRUE.equals(billPayReqDTO.getCallbackPayFlag())) {
            // 三方支付
            thirdPay(orderDO, transactionRecordDOList, billPayReqDTO);
        }
        if (Boolean.TRUE.equals(billPayReqDTO.getAggPayFlag())) {
            return;
        }

        if (billPayReqDTO.debtPayFlag()) {
            // 挂账支付
            DebtUnitRecordSaveReqDTO saveReqDTO = new DebtUnitRecordSaveReqDTO();
            BeanUtils.copyProperties(billPayReqDTO, saveReqDTO);
            saveReqDTO.setOrderGuid(orderDO.getGuid().toString());
            saveReqDTO.setDebtFee(orderDO.getActuallyPayFee());
            saveReqDTO.setChangeType(2);
            saveReqDTO.setDebtInvoiceCode(redisHelper.generateDebtInvoiceCode(UserContextUtils.get().getStoreNo(), UserContextUtils.getStoreGuid()));
            debtUnitRecordService.saveDebtUnit(saveReqDTO);
            // 查询并更新入参中的挂账单位信息
            queryAndUpdateBillPayReqDTODebtInfo(billPayReqDTO);
        }

        // 积分商城会员积分扣减
        if ((StringUtils.isEmpty(orderDO.getMemberConsumptionGuid()) || ObjectUtil.equal("0", orderDO.getMemberConsumptionGuid()))
                && ObjectUtil.equal(billPayReqDTO.getMemberIntegralStore(), 1)) {
            try {
                //扣减积分
                log.info("支付，积分抵扣：billPayReqDTO={},orderDO={}", billPayReqDTO, orderDO);
                String memberConsumptionGuid = deductionIntegral(orderDO, billPayReqDTO);
                orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
            } catch (Exception e) {
                log.error("积分商城扣减积分异常：{}", e.getMessage());
            }
        }
    }

    private void thirdPay(OrderDO orderDO, List<TransactionRecordDO> transactionRecordDOList,
                          BillPayReqDTO billPayReqDTO) {
        Map<Integer, DiscountFeeDetailDTO> discountFeeDetailMap = Optional.ofNullable(billPayReqDTO.getDiscountFeeDetailDTOS())
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(DiscountFeeDetailDTO::getDiscountType, Function.identity(), (key1, key2) -> key1));

        DiscountFeeDetailDTO memberGrouponDiscount = discountFeeDetailMap.get(DiscountTypeEnum.MEMBER_GROUPON.getCode());
        DiscountFeeDetailDTO goodsGrouponDiscount = discountFeeDetailMap.get(DiscountTypeEnum.GOODS_GROUPON.getCode());
        // 是否参与会员活动
        boolean joinMemberActivity = BigDecimalUtil.greaterThanZero(
                Optional.ofNullable(memberGrouponDiscount)
                        .orElse(new DiscountFeeDetailDTO())
                        .getDiscountFee())
                ||
                BigDecimalUtil.greaterThanZero(
                        Optional.ofNullable(goodsGrouponDiscount)
                                .orElse(new DiscountFeeDetailDTO())
                                .getDiscountFee());
        // 会员余额支付
        BillPayReqDTO.Payment memberPayPayment = Optional.ofNullable(billPayReqDTO.getPayments())
                .orElse(Lists.newArrayList())
                .stream()
                .filter(p -> p.getPaymentType() != null && PaymentTypeEnum.MEMBER.getCode() == p.getPaymentType())
                .findFirst()
                .orElse(null);
        boolean memberPay = Objects.nonNull(memberPayPayment);
        if (CommonUtil.hasGuid(orderDO.getMemberGuid()) || joinMemberActivity) {
            BillPayReqDTO copyBillPayReqDTO = orderTransform.billPayReqDTO2BillPayReqDTO(billPayReqDTO);
            log.info("支付，调用会员：copyBillPayReqDTO={},orderDO={}", copyBillPayReqDTO, orderDO);
            String memberConsumptionGuid = memberPay(orderDO, memberPayPayment, copyBillPayReqDTO);
            orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
        }

        // 食堂卡支付
        List<BillPayReqDTO.Payment> canteenPayPayments = Optional.ofNullable(billPayReqDTO.getPayments())
                .orElse(Lists.newArrayList())
                .stream()
                .filter(p1 -> p1.getPaymentType() != null)
                .filter(p -> PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode() == p.getPaymentType()
                        || PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode() == p.getPaymentType())
                .collect(Collectors.toList());
        boolean canteenPay = CollectionUtils.isNotEmpty(canteenPayPayments);
        if (canteenPay) {
            BillPayReqDTO copyBillPayReqDTO = orderTransform.billPayReqDTO2BillPayReqDTO(billPayReqDTO);
            copyBillPayReqDTO.setCanteenPay(true);
            log.info("支付，调用食堂卡：copyBillPayReqDTO={},orderDO={}", copyBillPayReqDTO, orderDO);
            String canteenConsumptionGuid = memberPay(orderDO, canteenPayPayments.get(0), copyBillPayReqDTO);
            orderDO.setCanteenConsumptionGuid(canteenConsumptionGuid);
        }

        // 非会员
        if (!CommonUtil.hasGuid(orderDO.getMemberGuid()) && !memberPay && !canteenPay) {
            BillPayReqDTO copyBillPayReqDTO = orderTransform.billPayReqDTO2BillPayReqDTO(billPayReqDTO);
            log.info("支付，未登录会员参与营销活动：copyBillPayReqDTO={},orderDO={}", copyBillPayReqDTO, orderDO);
            notMemberPayHandler(orderDO, copyBillPayReqDTO, discountFeeDetailMap);
        }

        // 预付金
        if (orderDO.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode()) && BigDecimalUtil.greaterThanZero
                (orderDO.getPrepayFee())) {
            // 增加预付金使用记录
            TransactionRecordDO prepayTransactionRecordDO =
                    getTransactionRecordDO(orderDO, billPayReqDTO.getOrderGuid(), billPayReqDTO.getDeviceId());
            //预付金清零
            orderDO.setPrepayFee(BigDecimal.ZERO);
            transactionRecordDOList.add(prepayTransactionRecordDO);
        }
        // 麓豆支付
        ludouMemberAggPay(orderDO, transactionRecordDOList, billPayReqDTO);
    }


    private void putAbnormalOrderRecord(BillAggPayReqDTO billPayReqDTO) {
        //将异常单所需数据对象存入redis
        OrderAbnormalRecordDO orderAbnormalRecordDO = new OrderAbnormalRecordDO();
        orderAbnormalRecordDO.setPaymentAmount(billPayReqDTO.getAmount());
        //支付授权码转换
        String authCode = billPayReqDTO.getAuthCode();
        if (StringUtils.isNotEmpty(authCode)) {
            if (authCode.startsWith("1")) {
                orderAbnormalRecordDO.setPaymentMethodId(Integer.valueOf(PayPowerId.YL_WX_SCAN_CODE.getId()
                        .replace(PayPowerId.YL_WX_SCAN_CODE.getId(), "1")));
                orderAbnormalRecordDO.setPaymentMethodName(PayPowerId.YL_WX_SCAN_CODE.getName());
            } else if (authCode.startsWith("2")) {
                orderAbnormalRecordDO.setPaymentMethodId(Integer.valueOf(PayPowerId.TL_AL_SCAN_CODE.getId()
                        .replace(PayPowerId.TL_AL_SCAN_CODE.getId(), "2")));
                orderAbnormalRecordDO.setPaymentMethodName(PayPowerId.TL_AL_SCAN_CODE.getName());
            } else if (authCode.startsWith("6235")) {
                orderAbnormalRecordDO.setPaymentMethodId(Integer.valueOf("6235"));
                orderAbnormalRecordDO.setPaymentMethodName(PayPowerId.NY_BMP_SCAN_PAY.getName());
            } else if (authCode.startsWith("6")) {
                orderAbnormalRecordDO.setPaymentMethodId(Integer.valueOf(PayPowerId.YL_UNION_PAY.getId()
                        .replace(PayPowerId.YL_UNION_PAY.getId(), "6")));
                orderAbnormalRecordDO.setPaymentMethodName(PayPowerId.YL_UNION_PAY.getName());
            } else if (authCode.startsWith("0100")) {
                orderAbnormalRecordDO.setPaymentMethodId(Integer.valueOf(PayPowerId.DIGITAL_CURRENCY.getId()
                        .replace(PayPowerId.DIGITAL_CURRENCY.getId(), "0100")));
                orderAbnormalRecordDO.setPaymentMethodName(PayPowerId.DIGITAL_CURRENCY.getName());
            }
            redisService.putAbnormalOrderRecord(billPayReqDTO.getOrderGuid(), orderAbnormalRecordDO);
        }
    }

    private void clearMultipleRecord(BillAggPayReqDTO billPayReqDTO) {
        if (!ObjectUtils.isEmpty(billPayReqDTO.getMultipleRecordGuid())) {
            multipleTransactionRecordService.removeById(billPayReqDTO.getMultipleRecordGuid());
        }
    }

    private Long getPayGuid(BillAggPayReqDTO billPayReqDTO,
                            String orderGuid,
                            TransactionRecordDO transactionRecordDO,
                            LocalDate businessDay) {
        Long payGuid = billPayReqDTO.getPayGuid();
        if (ObjectUtils.isEmpty(payGuid)) {
            List<MultipleTransactionRecordDO> multipleRecordDOList = multipleTransactionRecordService.listByOrderGuid(orderGuid);
            if (CollectionUtils.isEmpty(multipleRecordDOList)) {
                payGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
                transactionRecordDO.setAmount(billPayReqDTO.getAmount());
                //正常支付的聚合支付新增时增加可退款金额
                transactionRecordDO.setRefundableFee(billPayReqDTO.getAmount());
            } else {
                // 多次支付最终结账
                payGuid = multipleRecordDOList.get(0).getTransactionRecordGuid();
                BigDecimal amount = multipleRecordDOList.stream()
                        .map(MultipleTransactionRecordDO::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .add(billPayReqDTO.getAmount());
                transactionRecordDO.setAmount(amount);
                //正常支付的聚合支付新增时增加可退款金额
                transactionRecordDO.setRefundableFee(amount);
                Long multipleRecordGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_MULTIPLE_TRANSACTION_RECORD);
                billPayReqDTO.setMultipleRecordGuid(multipleRecordGuid);
                saveMultipleRecord(billPayReqDTO, multipleRecordGuid, payGuid, orderGuid, businessDay);
            }
        }
        return payGuid;
    }

    private void saveMultipleRecord(BillAggPayReqDTO billPayReqDTO,
                                    Long multipleRecordGuid,
                                    Long payGuid,
                                    String orderGuid,
                                    LocalDate businessDay) {
        MultipleTransactionRecordDO multipleRecordDO = new MultipleTransactionRecordDO();
        multipleRecordDO.setGuid(multipleRecordGuid);
        multipleRecordDO.setTransactionRecordGuid(payGuid);
        multipleRecordDO.setOrderGuid(Long.valueOf(orderGuid));
        multipleRecordDO.setTerminalId(billPayReqDTO.getDeviceId());
        multipleRecordDO.setAmount(billPayReqDTO.getAmount());
        //正常支付的聚合支付新增时增加可退款金额
        multipleRecordDO.setRefundableFee(billPayReqDTO.getAmount());
        multipleRecordDO.setPaymentType(PaymentTypeEnum.AGG.getCode());
        multipleRecordDO.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
        multipleRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
        multipleRecordDO.setStaffName(UserContextUtils.getUserName());
        multipleRecordDO.setState(TradeStateEnum.READY.getCode());
        multipleRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
        multipleRecordDO.setStoreName(UserContextUtils.getStoreName());
        multipleRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
        multipleRecordDO.setCreateTime(LocalDateTime.now());
        multipleRecordDO.setBusinessDay(businessDay);
        multipleTransactionRecordService.save(multipleRecordDO);
    }

    private void invoiceCache(BillAggPayReqDTO billPayReqDTO, String orderGuid) {
        if (Objects.nonNull(billPayReqDTO.getIsInvoiceCode())
                && billPayReqDTO.getIsInvoiceCode() == BooleanEnum.TRUE.getCode()) {
            RequestGenerateInvoiceDTO request = new RequestGenerateInvoiceDTO();
            request.setAccountName(billPayReqDTO.getAccountName());
            request.setAccount(billPayReqDTO.getInvoicePhone());
            request.setStoreGuid(billPayReqDTO.getStoreGuid());
            request.setOrderNo(orderGuid);
            log.info("聚合支付开票二维码缓存: {}", JacksonUtils.writeValueAsString(request));
            redisHelper.set(INVOICE_CODE_PRINT_REDIS_KEY + orderGuid, JSON.toJSONString(request));
        }
    }

    @Override
    public String recovery(RecoveryReqDTO recoveryReqDTO) {
        String orderGuid = recoveryReqDTO.getOrderGuid();
        OrderDO orderDO = orderService.getByIdWithLock(orderGuid);

        // 检测是否在反结账时效时间内
        checkRecoveryTimeLimit(orderDO);

        if (orderDO.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
            throw new ParameterException("订单只能被反结账一次，不允许重复反结账");
        }
        if (!orderDO.getRecoveryType().equals(RecoveryTypeEnum.GENERAL.getCode())) {
            throw new ParameterException("订单状态不允许反结账");
        }
        if (orderDO.getMemberGuid() != null && !orderService.booleanIsLastOrder(orderDO)) {
            throw new ParameterException("会员订单只能反结账最新一笔记录");
        }
        if (orderDO.getAdjustState() == 1) {
            throw new ParameterException("已调整的订单不可进行反结账");
        }
        if (Objects.nonNull(orderDO.getRefundOrderGuid())) {
            throw new ParameterException("已退款的订单不可进行反结账");
        }
        boolean fastFood = false;
        if (orderDO.getTradeMode().equals(TradeModeEnum.FAST.getCode())) {
            fastFood = true;
        }

        if (orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
            throw new ParameterException("只有主单可以操作并单反结账");
        }


        //是否虚拟台
        Integer virtualTable = 1;

        boolean combine = false;
        LocalDateTime now = LocalDateTime.now();

        if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
            combine = true;
        }

        Pair<Long, Long> guid = singleOrderRecovery(recoveryReqDTO, orderDO, virtualTable,
                combine, now, null, fastFood);
        if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
            List<OrderDO> subOrderDOS = orderService.listByMainOrderGuid(String.valueOf(orderDO.getGuid()));
            for (OrderDO subOrderDO : subOrderDOS) {
                LocalizeSynchronizeContext.putOrderGuid(subOrderDO.getGuid());
                singleOrderRecovery(recoveryReqDTO, subOrderDO, virtualTable, combine, now, guid, fastFood);
            }

        }

        // 反结账 删除第三方的结算信息
        thirdActivityClientService.removeByOrderGuid(orderGuid);
        // 反结账不删除第三方活动信息， 更新记录上的订单guid为新的guid
        thirdActivityRecordService.updateOrderGuid(orderGuid, String.valueOf(guid.getKey()));

        //==============================  待优化 =================================
        UserContext userContext = UserContextUtils.get();
        DineinOrderGiftRespDTO orderGiftRespDTO = new DineinOrderGiftRespDTO();
        checkOutThreadPool.submit(() -> {
            UserContextUtils.put(userContext);
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            // 订单详情
            DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetails(orderGuid);
            orderDetail.setCheckoutTime(orderDO.getCheckoutTime());
            orderGiftRespDTO.setActuallyPayFee(orderDetail.getActuallyPayFee());
            orderGiftRespDTO.setMemberGuid(orderDetail.getMemberGuid());
            orderGiftRespDTO.setGuid(orderDetail.getGuid());
            // erp 库存撤回
            reduceStockForOrder(userContext, orderDO, orderDetail);
            // 营销活动退款
            asyncMemberMarketingOrder(userContext, orderDetail);
            //消费有礼回退
            revocationConsumptionGift(orderGiftRespDTO);
            // 打印退款單
            orderRefundPrintService.printByRecovery(recoveryReqDTO, orderDetail, now);
        });

        // 同步订单反结账状态到赚餐
        asyncOrder2Tcd(orderDO);
        //==========================================================================
        //老板助手推送
        messagePushHelper.recoveryMsgPush();
        return String.valueOf(guid.getKey());
    }

    private void revocationConsumptionGift(DineinOrderGiftRespDTO orderDetail) {
        try {
            log.info("消费有礼回退会员guid:{}，回退实付金额:{}", orderDetail.getMemberGuid(), orderDetail.getActuallyPayFee());
            if (StringUtils.isNotBlank(orderDetail.getMemberGuid())
                    && orderDetail.getActuallyPayFee().compareTo(BigDecimal.ZERO) > 0) {
                log.info("消费有礼回退订单={}", orderDetail.getGuid());
                memberMarketingClientService.revocationConsumptionGift(orderDetail.getGuid());
            }
        } catch (Exception e) {
            log.error("消费有礼回退异常", e);
        }
    }

    /**
     * 库存撤回
     */
    private void reduceStockForOrder(UserContext userContext, OrderDO orderDO, DineinOrderDetailRespDTO orderDetail) {
        erpStockExecutor.execute(() -> {
            UserContextUtils.put(userContext);
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            // 订单商品集合
            List<DineInItemDTO> erpDineInItemDTOS = JacksonUtils.toObjectList(DineInItemDTO.class, JacksonUtils
                    .writeValueAsString(orderDetail.getDineInItemDTOS()));
            //子单商品erp减库存
            if (CollectionUtil.isNotEmpty(orderDetail.getSubOrderDetails())) {
                for (DineinOrderDetailRespDTO dineinOrderDetailRespDTO : orderDetail.getSubOrderDetails()) {
                    erpDineInItemDTOS.addAll(JacksonUtils.toObjectList(DineInItemDTO.class, JacksonUtils
                            .writeValueAsString(dineinOrderDetailRespDTO.getDineInItemDTOS())));
                }
            }
            // 反结账加库存
            executeStockReduceService.executeStockReturn(erpDineInItemDTOS, orderDetail.getGuid(), orderDO);
        });
    }

    /**
     * 营销活动退款
     */
    private void asyncMemberMarketingOrder(UserContext userContext, DineinOrderDetailRespDTO orderDetail) {
        List<DiscountFeeDetailDTO> discountFeeDetailList = orderDetail.getDiscountFeeDetailDTOS();
        // 目前推送参与了限时特价活动、第N份优惠活动的订单
        List<DiscountFeeDetailDTO> discountFeeDetails = discountFeeDetailList.stream()
                .filter(e -> (DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode() == e.getDiscountType()
                        || DiscountTypeEnum.NTH_ACTIVITY.getCode() == e.getDiscountType())
                        && e.getDiscountFee().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(discountFeeDetails)) {
            return;
        }
        asyncMemberMarketingOrderExecutor.execute(() -> {
            UserContextUtils.put(userContext);
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            // 构建退款参数
            UniteActivityOrderRefundDTO uniteActivityOrderRefundDTO = buildUniteActivityOrderPayDTO(orderDetail, discountFeeDetails);
            // 下单
            log.info("参与营销活动订单退款推送入参:{}", JacksonUtils.writeValueAsString(uniteActivityOrderRefundDTO));
            memberMarketingClientService.uniteActivityOrderRefund(uniteActivityOrderRefundDTO);
            log.info("参与营销活动订单退款推送成功");
            UserContextUtils.remove();
        });
    }


    /**
     * 构建营销活动订单退款入参
     */
    private UniteActivityOrderRefundDTO buildUniteActivityOrderPayDTO(DineinOrderDetailRespDTO orderDetail,
                                                                      List<DiscountFeeDetailDTO> discountFeeDetails) {
        UniteActivityOrderRefundDTO uniteActivityOrderRefundDTO = new UniteActivityOrderRefundDTO();
        uniteActivityOrderRefundDTO.setOrderGuid(orderDetail.getGuid());
        List<Integer> activityTypes = discountFeeDetails.stream()
                .map(e -> QueryTypeEnum.transferQueryType(e.getDiscountType()))
                .distinct()
                .collect(Collectors.toList());
        uniteActivityOrderRefundDTO.setActivityTypes(activityTypes);
        return uniteActivityOrderRefundDTO;
    }

    private void fastFoodRecoveryReturnItem(RecoveryReqDTO recoveryReqDTO, OrderDO orderDO,
                                            List<OrderItemDO> newOrderItemDOInReturn,
                                            List<FreeReturnItemDO> newFreeItemDOInReturn) {
        if (Objects.equals(TradeModeEnum.DINEIN.getCode(), orderDO.getTradeMode())) {
            return;
        }
        if (Objects.nonNull(recoveryReqDTO.getReturnItemFlag()) && Boolean.FALSE.equals(recoveryReqDTO.getReturnItemFlag())) {
            return;
        }
        log.info("[异常排查1]newOrderItemDOInReturn={}newFreeItemDOInReturn={}", JacksonUtils.writeValueAsString(newOrderItemDOInReturn),
                JacksonUtils.writeValueAsString(newFreeItemDOInReturn));
        BatchItemReturnOrFreeReqDTO recoveryReturnItemReqDTO = new BatchItemReturnOrFreeReqDTO();
        BeanUtils.copyProperties(recoveryReqDTO, recoveryReturnItemReqDTO);
        String returnReason = "【反结账退菜】" + recoveryReqDTO.getReason();
        recoveryReturnItemReqDTO.setFastFood(true);
        List<BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq> itemReturnOrFreeReq = new ArrayList<>();
        List<FreeReturnItemDO> returnItemDOList = newFreeItemDOInReturn.stream()
                .filter(n -> Objects.equals(FreeReturnTypeEnum.FREE.getCode(), n.getType()))
                .collect(Collectors.toList());
        Map<Long, FreeReturnItemDO> freeReturnItemDOMap = new HashMap<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(returnItemDOList)) {
            freeReturnItemDOMap = returnItemDOList.stream()
                    .collect(Collectors.toMap(FreeReturnItemDO::getOrderItemGuid, Function.identity(), (k, v) -> v));
        }
        List<OrderItemDO> orderItemDOList = newOrderItemDOInReturn.stream()
                .filter(ni -> 0 == ni.getParentItemGuid())
                .collect(Collectors.toList());
        for (OrderItemDO orderItemDO : orderItemDOList) {
            BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq req = new BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq();
            List<ItemReturnOrFreeReqDTO> itemReturnOrFreeReqDTOS = new ArrayList<>();
            if (BigDecimalUtil.greaterThanZero(orderItemDO.getCurrentCount())) {
                ItemReturnOrFreeReqDTO reqDTO = new ItemReturnOrFreeReqDTO();
                reqDTO.setReason(returnReason);
                reqDTO.setCount(orderItemDO.getCurrentCount());
                reqDTO.setIsFree(BooleanEnum.FALSE.getCode());
                reqDTO.setFreeGuid(StringUtils.EMPTY);
                reqDTO.setGuid(String.valueOf(orderItemDO.getGuid()));
                itemReturnOrFreeReqDTOS.add(reqDTO);
            }
            if (BigDecimalUtil.greaterThanZero(orderItemDO.getFreeCount())) {
                ItemReturnOrFreeReqDTO reqDTO = new ItemReturnOrFreeReqDTO();
                reqDTO.setReason(returnReason);
                reqDTO.setCount(orderItemDO.getFreeCount());
                reqDTO.setIsFree(BooleanEnum.TRUE.getCode());
                reqDTO.setGuid(String.valueOf(orderItemDO.getGuid()));
                if (!org.springframework.util.CollectionUtils.isEmpty(freeReturnItemDOMap)) {
                    FreeReturnItemDO freeReturnItemDO = freeReturnItemDOMap.get(orderItemDO.getGuid());
                    reqDTO.setFreeGuid(String.valueOf(freeReturnItemDO.getGuid()));
                }
                itemReturnOrFreeReqDTOS.add(reqDTO);
            }
            req.setItemReturnOrFreeReqDTOS(itemReturnOrFreeReqDTOS);
            req.setOrderItemGuid(String.valueOf(orderItemDO.getGuid()));
            itemReturnOrFreeReq.add(req);
        }

        recoveryReturnItemReqDTO.setItemReturnOrFreeReqs(itemReturnOrFreeReq);
        log.info("[异常排查2]recoveryReturnItemReqDTO={}", JacksonUtils.writeValueAsString(recoveryReturnItemReqDTO));
        dineInItemService.returnOrFreeItem(recoveryReturnItemReqDTO, Boolean.TRUE, Boolean.FALSE);
    }

    /**
     * 同步订单反结账状态到赚餐
     */
    private void asyncOrder2Tcd(OrderDO orderDO) {
        if (!BaseDeviceTypeEnum.isApplet(orderDO.getDeviceType()) && TradeModeEnum.FAST.getCode() != orderDO.getTradeMode()) {
            return;
        }
        log.info("小程序快餐下单反结账同步到赚餐, orderGuid:{}", orderDO.getGuid());
        tcdOrderService.asyncOrderState(String.valueOf(orderDO.getGuid()));
    }

    @Override
    public Boolean validatAggRefund(ValidatAggReturnReqDTO validatAggReturnReqDTO) {
        String orderGuid = validatAggReturnReqDTO.getOrderGuid();
        OrderDO orderDO = orderService.getById(orderGuid);
        //校验是否聚合支付退款时查询所有历史单
        List<OrderDO> orderDOS = orderService.listByRecoveryId(orderDO.getRecoveryId());
        Map<Long, OrderDO> orderDOMap = CollectionUtil.toMap(orderDOS, "guid");
        List<TransactionRecordDO> transactionRecordDOS = transactionRecordService.listByOrderGuids(new ArrayList<>
                (orderDOMap.keySet()));
        Boolean result = Boolean.FALSE;
        for (TransactionRecordDO transactionRecordDO : transactionRecordDOS) {
            //正常支付的聚合支付并且可退款金额大于要退的金额
            if (transactionRecordDO.getPaymentType().equals(PaymentTypeEnum.AGG.getCode()) && transactionRecordDO
                    .getTradeType().equals(TradeTypeEnum.GENERAL_IN.getCode()) && BigDecimalUtil
                    .greaterEqual(transactionRecordDO.getRefundableFee(), validatAggReturnReqDTO.getAmount())) {
                result = Boolean.TRUE;
            }
        }
        return result;
    }

    @Override
    @Transactional
    public Boolean refund(RefundReqDTO refundReqDTO) {
        OrderDO orderDO;
        OrderDO newOrderDO = orderService.getById(refundReqDTO.getOrderGuid());
        // 预定金
        BigDecimal reserveFee = newOrderDO.getReserveFee();
        //结账后的修改状态
        newOrderDO.setIsHandle(-1);
        String originalOrderGuid = refundReqDTO.getOriginalOrderGuid();
        List<TransactionRecordDO> transactionRecordDOS;
        if (StringUtils.isNotEmpty(originalOrderGuid)) {
            orderDO = orderService.getById(originalOrderGuid);
            //反结账聚合支付退款时查询所有历史单
            List<OrderDO> orderDOS = orderService.listByRecoveryId(orderDO.getRecoveryId());
            Map<Long, OrderDO> orderDOMap = CollectionUtil.toMap(orderDOS, "guid");
            transactionRecordDOS = transactionRecordService.listByOrderGuids(new ArrayList<>
                    (orderDOMap.keySet()));
        } else {
            //????? 和上面newOrder一樣？
            orderDO = orderService.getById(refundReqDTO.getOrderGuid());
            transactionRecordDOS = transactionRecordService.listByOrderGuid(refundReqDTO
                    .getOrderGuid());
        }

        List<TransactionRecordDO> savaTransactionRecordDOS = new ArrayList<>();

        //退款
        TransactionRecordDO refundTransactionRecordDO = new TransactionRecordDO();
        Long refundGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
        refundTransactionRecordDO.setGuid(refundGuid);
        refundTransactionRecordDO.setBusinessDay(newOrderDO.getBusinessDay());
        refundTransactionRecordDO.setOrderGuid(Long.valueOf(refundReqDTO.getOrderGuid()));
        refundTransactionRecordDO.setTerminalId(refundReqDTO.getDeviceId());
        refundTransactionRecordDO.setAmount(BigDecimalUtil.negative(refundReqDTO.getAmount()));

        refundTransactionRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
        refundTransactionRecordDO.setStaffName(UserContextUtils.getUserName());
        refundTransactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
        refundTransactionRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
        refundTransactionRecordDO.setStoreName(UserContextUtils.getStoreName());
        refundTransactionRecordDO.setCreateTime(LocalDateTime.now());

        if (refundReqDTO.getPaymentType() == PaymentTypeEnum.AGG.getCode()) {
            refundTransactionRecordDO.setPaymentType(PaymentTypeEnum.AGG.getCode());
            refundTransactionRecordDO.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
            if (refundReqDTO.getPre() == 1) {
                refundTransactionRecordDO.setTradeType(TradeTypeEnum.PRE_OUT.getCode());
            } else {
                refundTransactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_OUT.getCode());
            }
            savaTransactionRecordDOS.add(refundTransactionRecordDO);
            AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
//            aggRefundReqDTO.setOrderGUID(String.valueOf(orderDO.getGuid()));

            aggRefundReqDTO.setRefundFee(refundReqDTO.getAmount());
            aggRefundReqDTO.setReason("聚合支付退款");
            for (TransactionRecordDO transactionRecordDO : transactionRecordDOS) {
                //正常支付的聚合支付并且可退款金额大于要退的金额
                if (transactionRecordDO.getPaymentType().equals(PaymentTypeEnum.AGG.getCode()) && transactionRecordDO
                        .getTradeType().equals(TradeTypeEnum.GENERAL_IN.getCode()) && BigDecimalUtil
                        .greaterEqual(transactionRecordDO.getRefundableFee(), refundReqDTO.getAmount())) {
                    if (BigDecimalUtil.equal(transactionRecordDO.getAmount(), refundReqDTO.getAmount())) {
                        aggRefundReqDTO.setRefundType(0);
                    } else {
                        aggRefundReqDTO.setRefundType(1);
                    }
                    aggRefundReqDTO.setPayGUID(String.valueOf(transactionRecordDO.getGuid()));
                    aggRefundReqDTO.setOrderGUID(String.valueOf(transactionRecordDO.getOrderGuid()));
                    //扣钱可退金额
                    transactionRecordDO.setRefundableFee(transactionRecordDO.getRefundableFee().subtract(refundReqDTO
                            .getAmount()));
                    transactionRecordService.updateById(transactionRecordDO);
                    break;
                }
            }
            if (StringUtils.isNotEmpty(aggRefundReqDTO.getPayGUID())) {
                SaasAggRefundDTO saasAggRefundDTO = orderTransform.refundReqDTO2SaasAggRefundDTO(refundReqDTO);
                saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
                AggRefundRespDTO refund = aggPayClientService.refund(saasAggRefundDTO);
                log.info("聚合支付结账时退款入参：{}", JacksonUtils.writeValueAsString(saasAggRefundDTO));
                BillVerifyHelper.refundResult(refund, false);
            }

        } else {
            refundTransactionRecordDO.setPaymentType(refundReqDTO.getPaymentType());
            refundTransactionRecordDO.setPaymentTypeName(getRefundPaymentTypeName(refundReqDTO.getPaymentType(), refundReqDTO.getPaymentTypeName()));
            if (refundReqDTO.getReserve() != null && refundReqDTO.getReserve() == 1) {
                refundTransactionRecordDO.setTradeType(TradeTypeEnum.DEPOSIT_OUT.getCode());
                // 预定金退款
                reserveRefund(refundReqDTO, newOrderDO, refundTransactionRecordDO);
                // 附加费保存
                appendFeeService.persistAppendFee(refundReqDTO.getOrderGuid());
                // 预定金修改
                newOrderDO.setReserveFee(newOrderDO.getReserveFee().subtract(refundReqDTO.getAmount()));
            } else {
                refundTransactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_OUT.getCode());
            }
            savaTransactionRecordDOS.add(refundTransactionRecordDO);
        }

        newOrderDO.setCheckoutDeviceType(refundReqDTO.getDeviceType());
        newOrderDO.setCheckoutTime(LocalDateTime.now());
        newOrderDO.setCheckoutStaffGuid(UserContextUtils.getUserGuid());
        newOrderDO.setCheckoutStaffName(UserContextUtils.getUserName());
        newOrderDO.setBusinessDay(newOrderDO.getBusinessDay());
        newOrderDO.setOrderFee(refundReqDTO.getOrderFee());
        newOrderDO.setActuallyPayFee(refundReqDTO.getActuallyPayFee());
        newOrderDO.setAppendFee(refundReqDTO.getAppendFee());
        List<DiscountDO> discountDOS = new ArrayList<>();

        //营业日新接口
        BusinessDateReqDTO queryDTO = new BusinessDateReqDTO();
        ArrayList<String> storeGuidList = new ArrayList<>();
        storeGuidList.add(UserContextUtils.getStoreGuid());
        queryDTO.setStoreGuidList(storeGuidList);
        queryDTO.setQueryDateTime(LocalDateTime.now());
        LocalDate businessDay = storeClientService.queryBusinessDay(queryDTO);

        if (refundReqDTO.getPre() == 1 || newOrderDO.getPrepayFee().compareTo(BigDecimal.ZERO) > 0) {
            log.info("是否有预付金：{}，预付金额：{}，反结账id：{}", refundReqDTO.getPre(), newOrderDO.getPrepayFee(),
                    newOrderDO.getRecoveryId());
            //增加预付金使用记录
            TransactionRecordDO transactionRecordDO = getTransactionRecordDO(newOrderDO, refundReqDTO.getOrderGuid(),
                    refundReqDTO.getDeviceId());
            savaTransactionRecordDOS.add(transactionRecordDO);
            //预付金清零
            orderDO.setPrepayFee(BigDecimal.ZERO);
            newOrderDO.setState(StateEnum.SUCCESS.getCode());
            if (newOrderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
                checkoutSubOrder(newOrderDO);
            }

            orderDO.setIsHandle(-1);
            ArrayList<OrderDO> orderDOS = Lists.newArrayList(orderDO, newOrderDO);
            orderService.updateBatchByIdWithDeleteCache(orderDOS);

            //支付成功之后更新优惠金额
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS = refundReqDTO.getDiscountFeeDetailDTOS();
            discountDOS = orderTransform.discountFeeDetailDTOS2discountDOS(discountFeeDetailDTOS);
            discountService.updateBatchById(discountDOS);

            //通知桌台时用新订单guid
            orderDO.setGuid(Long.valueOf(refundReqDTO.getOrderGuid()));

        }

        if (refundReqDTO.getReserve() != null && refundReqDTO.getReserve() == 1) {
            //增加预定金使用记录
            TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
            Long guid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
            transactionRecordDO.setGuid(guid);
            transactionRecordDO.setBusinessDay(newOrderDO.getBusinessDay());
            transactionRecordDO.setOrderGuid(Long.valueOf(refundReqDTO.getOrderGuid()));
            transactionRecordDO.setTerminalId(refundReqDTO.getDeviceId());
            transactionRecordDO.setAmount(reserveFee);
            transactionRecordDO.setPaymentType(PaymentTypeEnum.RESERVE.getCode());
            transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.RESERVE.getDesc());
            transactionRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
            transactionRecordDO.setStaffName(UserContextUtils.getUserName());
            transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
            transactionRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
            transactionRecordDO.setStoreName(UserContextUtils.getStoreName());
            transactionRecordDO.setTradeType(TradeTypeEnum.DEPOSIT_IN.getCode());
            transactionRecordDO.setCreateTime(LocalDateTime.now());
            savaTransactionRecordDOS.add(transactionRecordDO);
            //预定金清零
//            newOrderDO.setReserveFee(BigDecimal.ZERO);
            newOrderDO.setState(StateEnum.SUCCESS.getCode());
            if (newOrderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
                checkoutSubOrder(newOrderDO);
            }

            // 订单结账不清台
            orderCloseHandler(refundReqDTO.getCloseTableFlag(), newOrderDO);

            //支付成功之后更新优惠金额
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS = refundReqDTO.getDiscountFeeDetailDTOS();
            discountDOS = orderTransform.discountFeeDetailDTOS2discountDOS(discountFeeDetailDTOS);
            discountService.updateBatchById(discountDOS);

            //通知桌台时用新订单guid
            orderDO.setGuid(Long.valueOf(refundReqDTO.getOrderGuid()));
        }

        // 设置超额
        BigDecimal excessAmount = calculateExcessAmount(orderDO);
        orderDO.setExcessAmount(excessAmount);
        orderService.updateById(newOrderDO);

        // 支付成功之后保存使用的第三方活动信息
        saveThirdActivityDetails(orderDO);

        // 保存第三方活动交易明细
        if (CollectionUtils.isEmpty(discountDOS)) {
            discountDOS = discountService.listByOrderGuid(refundReqDTO.getOrderGuid());
        }
        Map<Integer, DiscountDO> discountTypeMap = CollectionUtil.toMap(discountDOS, "discountType");
        DiscountDO thirdActivity = discountTypeMap.get(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
        if (BigDecimalUtil.greaterThanZero(thirdActivity.getDiscountFee())) {
            TransactionRecordDO thirdActivityRecordDO = new TransactionRecordDO();
            Long thirdActivityGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
            thirdActivityRecordDO.setGuid(thirdActivityGuid);
            thirdActivityRecordDO.setOrderGuid(Long.valueOf(refundReqDTO.getOrderGuid()));
            thirdActivityRecordDO.setTerminalId(refundReqDTO.getDeviceId());
            thirdActivityRecordDO.setAmount(thirdActivity.getDiscountFee());
            thirdActivityRecordDO.setPaymentType(PaymentTypeEnum.THIRD_ACTIVITY.getCode());
            thirdActivityRecordDO.setPaymentTypeName(PaymentTypeEnum.THIRD_ACTIVITY.getDesc());
            thirdActivityRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
            thirdActivityRecordDO.setStaffName(UserContextUtils.getUserName());
            thirdActivityRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
            thirdActivityRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
            thirdActivityRecordDO.setStoreName(UserContextUtils.getStoreName());
            thirdActivityRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
            thirdActivityRecordDO.setCreateTime(LocalDateTime.now());
            thirdActivityRecordDO.setBusinessDay(businessDay);
            savaTransactionRecordDOS.add(thirdActivityRecordDO);
        }

        log.warn("refundReqDTO:{},savaTransactionRecordDOS:{},orderDO:{}", JacksonUtils.writeValueAsString
                (refundReqDTO), JacksonUtils.writeValueAsString(savaTransactionRecordDOS), JacksonUtils
                .writeValueAsString
                        (orderDO));
        transactionRecordService.saveBatch(savaTransactionRecordDOS);
        // 退款后结账逻辑处理
        refundAfterCheckoutHandler(refundReqDTO, orderDO);
        return Boolean.TRUE;
    }

    /**
     * 退款后结账逻辑处理
     */
    private void refundAfterCheckoutHandler(RefundReqDTO refundReqDTO, OrderDO orderDO) {
        boolean checkout = refundReqDTO.getPre() == 1 || (refundReqDTO.getReserve() != null && refundReqDTO
                .getReserve() == 1);
        if (checkout) {
            // 订单结账不清台
            if (Objects.equals(BooleanEnum.TRUE.getCode(), refundReqDTO.getCloseTableFlag())) {
                // 创建订单
                BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
                BeanUtils.copyProperties(refundReqDTO, billPayReqDTO);
                billPayReqDTO.setOrderGuid(refundReqDTO.getOrderGuid());
                orderTableBillService.dealEnableHandleClose(billPayReqDTO);
                if (UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
                    return;
                }
            }
            // 异步打印结账单
            UserContext userContext = UserContextUtils.get();
            checkOutThreadPool.execute(() -> {
                UserContextUtils.put(userContext);
                dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
                boolean succeed = dineInService.printCheckOut(refundReqDTO, refundReqDTO.getOrderGuid());
                if (!succeed) {
                    log.warn("预定金 结账单打印失败");
                }
                tableService.checkOutChange(refundReqDTO, orderDO);
            });
        }
    }

    /**
     * 预定金退款
     */
    private void reserveRefund(RefundReqDTO refundReqDTO, OrderDO newOrderDO, TransactionRecordDO refundTransactionRecordDO) {
        ReserveRefundDTO reserveRefundDTO = new ReserveRefundDTO();
        reserveRefundDTO.setGuid(newOrderDO.getReserveGuid());
        reserveRefundDTO.setReserveAmount(refundReqDTO.getAmount());
        BigDecimal refundAmount = reserveClientService.partRefund(reserveRefundDTO);
        if (BigDecimalUtil.greaterThanZero(refundAmount)) {
            log.info("预定金部分退款成功, refundReqDTO:{}", JacksonUtils.writeValueAsString(refundReqDTO));
            refundTransactionRecordDO.setPaymentType(PaymentTypeEnum.RESERVE.getCode());
            refundTransactionRecordDO.setPaymentTypeName(PaymentTypeEnum.RESERVE.getDesc());
            // 退款走这里应该只有超付的情况，因此要当成成功支付去处理
            notifyPay(newOrderDO);
        }
    }

    private void notifyPay(OrderDO newOrderDO) {
        NotifyPayReqDTO reqDTO = new NotifyPayReqDTO();
        reqDTO.setReserveGuid(newOrderDO.getReserveGuid());
        if (com.holderzone.framework.util.StringUtils.hasText(newOrderDO.getMemberPhone())) {
            reqDTO.setMemberPhone(newOrderDO.getMemberPhone());
        }
        try {
            reserveClientService.notifyPay(reqDTO);
        } catch (Exception e) {
            log.error("[预付金支付成功通知]异常：{},e=｛｝", e.getMessage(), e);
        }
    }


    private String getRefundPaymentTypeName(Integer paymentType, String paymentTypeName) {
        if (PaymentTypeEnum.OTHER.getCode() != paymentType || StringUtils.isEmpty(paymentTypeName)) {
            return PaymentTypeEnum.getDesc(paymentType);
        }
        return paymentTypeName;
    }

    /**
     * 预付金使用肯定是全部使用，需要退款和支付的插入新的退款和支付记录
     *
     * @param orderDO
     * @param orderGuid
     * @param deviceId
     * @return
     */
    private TransactionRecordDO getTransactionRecordDO(OrderDO orderDO, String orderGuid, String deviceId) {
        BigDecimal prepayFee = orderDO.getPrepayFee();
        TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
        //营业日新接口
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        ArrayList<String> storeGuidList = new ArrayList<>();
        storeGuidList.add(UserContextUtils.getStoreGuid());
        reqDTO.setStoreGuidList(storeGuidList);
        reqDTO.setQueryDateTime(LocalDateTime.now());
        LocalDate businessDay = storeClientService.queryBusinessDay(reqDTO);

        Long guid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
        transactionRecordDO.setGuid(guid);
        transactionRecordDO.setBusinessDay(businessDay);
        transactionRecordDO.setOrderGuid(Long.valueOf(orderGuid));
        transactionRecordDO.setTerminalId(deviceId);
        transactionRecordDO.setAmount(prepayFee);
        transactionRecordDO.setPaymentType(PaymentTypeEnum.AGG.getCode());
        transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
        transactionRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
        transactionRecordDO.setStaffName(UserContextUtils.getUserName());
        transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
        transactionRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
        transactionRecordDO.setStoreName(UserContextUtils.getStoreName());
        transactionRecordDO.setTradeType(TradeTypeEnum.PRE_IN.getCode());
        transactionRecordDO.setCreateTime(LocalDateTime.now());
        return transactionRecordDO;
    }

    private Pair<Long, Long> singleOrderRecovery(RecoveryReqDTO recoveryReqDTO, OrderDO orderDO, Integer
            virtualTable, boolean combine, LocalDateTime now, Pair<Long, Long> mainOrderGuid, boolean fastFood) {
        String orderGuid = String.valueOf(orderDO.getGuid());
        int guidNum = 3;
        int count = 1;
        //预付金
        BigDecimal prepayFee = BigDecimal.ZERO;
        /**
         * 当前订单商品  orderItemDOS
         */
        List<OrderItemDO> orderItemDOS = orderItemService.listByOrderGuidWithCache(Long.valueOf(orderGuid), orderDO.getDeviceType());
        /**
         * 当前订单商品记录  orderItemRecordDOS
         */
        List<OrderItemRecordDO> orderItemRecordDOS = orderItemRecordService.listByOrderGuidWithCache(Long.valueOf(orderGuid));
        if (CollectionUtil.isNotEmpty(orderItemDOS)) {
            /**
             * 订单商品数*2 +3
             */
            guidNum = guidNum + orderItemDOS.size() * 2;
        }

        /**
         * 赠送、退货商品
         */
        List<FreeReturnItemDO> freeItemDOS = freeReturnItemService.listByOrderGuid(orderGuid);
        if (CollectionUtil.isNotEmpty(freeItemDOS)) {
            guidNum = guidNum + freeItemDOS.size() * 2;
        }

        /**
         * 通过orderguid 查询订单交易信息
         */
        List<TransactionRecordDO> transactionRecordDOS = transactionRecordService.listByOrderGuid(orderGuid);
        if (CollectionUtil.isNotEmpty(transactionRecordDOS)) {
            guidNum = guidNum + transactionRecordDOS.size() * 2;
        }

        /**
         * 订单优惠
         */
        List<DiscountDO> discountDOS = discountService.listByOrderGuid(orderGuid);
        if (CollectionUtil.isNotEmpty(discountDOS)) {
            guidNum = guidNum + discountDOS.size() * 2;
        }

        /**
         * 团购卷
         */
        List<GrouponDO> grouponDOS = grouponMpService.listByOrderGuid(orderGuid);
        if (CollectionUtil.isNotEmpty(grouponDOS)) {
            guidNum = guidNum + grouponDOS.size() * 2;
        }

        /**
         * 附加费用
         */
        List<AppendFeeDO> appendFeeDOS = appendFeeMpService.listByOrderGuid(orderGuid);
        if (CollectionUtil.isNotEmpty(appendFeeDOS)) {
            guidNum = guidNum + appendFeeDOS.size() * 2;
        }

        /**
         * 去重  itemGuids
         */
        Map<Long, OrderItemDO> orderItemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
        ArrayList<Long> itemGuids = new ArrayList<>(orderItemDOMap.keySet());

        /**
         * 商品属性
         */
        List<ItemAttrDO> itemAttrDOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(itemGuids)) {
            itemAttrDOS = itemAttrService.listByItemGuids(itemGuids);
        }

        if (CollectionUtil.isNotEmpty(itemAttrDOS)) {
            guidNum = guidNum + itemAttrDOS.size() * 2;
        }

        OrderDO newOrderDO = JacksonUtils.toObject(OrderDO.class, JacksonUtils.writeValueAsString(orderDO));
        OrderDO refundOrderDO = JacksonUtils.toObject(OrderDO.class, JacksonUtils.writeValueAsString(orderDO));
        boolean hasRecoveryId = orderDO.getRecoveryId() != null && Long.parseLong(orderDO.getRecoveryId()) > 0;
        if (hasRecoveryId) {
            count = orderService.selectRecoveryCount(orderDO.getRecoveryId());
        }
        List<Long> guids = dynamicHelper.generateGuids(GuidKeyConstant.RECOVERY, guidNum);
        String originalOrderNo = orderDO.getOrderNo();
        String newOrderNo = RECOVERY_MARK + count + (originalOrderNo.contains(RECOVERY_MARK) ? originalOrderNo
                .substring(2) : originalOrderNo);

        String refundOrderNo = newOrderNo.replace(RECOVERY_MARK, REFUND_MARK);
        String recoveryId = hasRecoveryId ? orderDO.getRecoveryId() : String.valueOf(guids.get(guids.size() - 1));
        //原订单号
        newOrderDO.setOriginalOrderGuid(orderDO.getGuid());
        refundOrderDO.setOriginalOrderGuid(orderDO.getGuid());
        //反结账类型
        if (!recoveryReqDTO.isFacePay()) {
            //人脸支付补偿接口更新
            orderDO.setRecoveryType(RecoveryTypeEnum.ORIGINAL.getCode());
        }

        newOrderDO.setRecoveryType(RecoveryTypeEnum.NEW.getCode());
        newOrderDO.setState(StateEnum.READY.getCode());
        refundOrderDO.setRecoveryType(RecoveryTypeEnum.RETURN.getCode());
        refundOrderDO.setState(StateEnum.REFUNDED.getCode());
        refundOrderDO.setCancelTime(now);
        //此字段退单暂存反结账次数
        refundOrderDO.setPrintPreBillNum(count);
        //清除原单信息
        Long newOrderGuid = guids.remove(0);
        Long refundOrderGuid = guids.remove(0);
        LocalizeSynchronizeContext.putOrderGuid(newOrderGuid, refundOrderGuid);

        newOrderDO.setGuid(newOrderGuid);
        newOrderDO.setPrintPreBillNum(0);
        newOrderDO.setOrderNo(newOrderNo);
        newOrderDO.setGmtCreate(now);
        refundOrderDO.setGuid(refundOrderGuid);
        refundOrderDO.setOrderNo(refundOrderNo);
        refundOrderDO.setGmtCreate(now);

        //退单结账时间更新为反结账时间
        refundOrderDO.setCheckoutTime(now);

        //会员撤销支付
        boolean memberPay = CommonUtil.hasGuid(orderDO.getMemberGuid());
        //食堂卡支付
        boolean canteenPay = ObjectUtil.isNotNull(recoveryReqDTO.getCanteenPay()) && recoveryReqDTO.getCanteenPay();
        log.warn("反结账是否会员：{}", memberPay);
        if (memberPay && CommonUtil.hasGuid(orderDO.getMemberConsumptionGuid())) {
            log.warn("反结账会员撤销支付入参：{}", orderDO.getMemberConsumptionGuid());
            RequestCancelPay cancelPayReqDTO = new RequestCancelPay();
            cancelPayReqDTO.setIsSettlement(Boolean.TRUE);
            cancelPayReqDTO.setOrderNumber(orderDO.getOrderNo());
            cancelPayReqDTO.setMemberConsumptionGuid(orderDO.getMemberConsumptionGuid());
            String memberPayGuid = memberTerminalClientService.cancelPay(cancelPayReqDTO);
            newOrderDO.setMemberConsumptionGuid(memberPayGuid);
        }
        if (canteenPay && CommonUtil.hasGuid(orderDO.getCanteenConsumptionGuid())) {
            log.warn("反结账食堂撤销支付入参：{}", orderDO.getMemberConsumptionGuid());
            RequestCancelPay cancelPayReqDTO = new RequestCancelPay();
            cancelPayReqDTO.setCanteenPay(canteenPay);
            cancelPayReqDTO.setCanteenPayType(recoveryReqDTO.getCanteenPayType());
            cancelPayReqDTO.setIsSettlement(Boolean.TRUE);
            cancelPayReqDTO.setOrderNumber(String.valueOf(newOrderGuid));
            cancelPayReqDTO.setMemberConsumptionGuid(orderDO.getCanteenConsumptionGuid());
            String memberPayGuid = memberTerminalClientService.cancelPay(cancelPayReqDTO);
            newOrderDO.setCanteenConsumptionGuid(memberPayGuid);
        }
        if (!canteenPay && !memberPay && CommonUtil.hasGuid(orderDO.getMemberConsumptionGuid())) {
            log.warn("反结账非会员撤销支付入参：{}", orderDO.getMemberConsumptionGuid());
            RequestCancelPay cancelPayReqDTO = new RequestCancelPay();
            cancelPayReqDTO.setIsSettlement(Boolean.TRUE);
            cancelPayReqDTO.setOrderNumber(orderDO.getOrderNo());
            cancelPayReqDTO.setMemberConsumptionGuid(orderDO.getMemberConsumptionGuid());
            String memberPayGuid = memberTerminalClientService.activityCancelPay(cancelPayReqDTO);
            newOrderDO.setMemberConsumptionGuid(memberPayGuid);
        }

        /**
         * 桌台信息
         */

        //反结账信息
        newOrderDO.setRecoveryDeviceType(recoveryReqDTO.getRecoveryDeviceType());
        newOrderDO.setRecoveryId(recoveryId);
        orderDO.setRecoveryId(recoveryId);
        orderDO.setRecoveryReason(recoveryReqDTO.getReason());
        newOrderDO.setRecoveryReason(recoveryReqDTO.getReason());
        newOrderDO.setRecoveryStaffGuid(UserContextUtils.getUserGuid());
        newOrderDO.setRecoveryStaffName(UserContextUtils.getUserName());
        refundOrderDO.setRecoveryDeviceType(recoveryReqDTO.getRecoveryDeviceType());
        refundOrderDO.setRecoveryId(recoveryId);
        refundOrderDO.setRecoveryReason(recoveryReqDTO.getReason());
        refundOrderDO.setRecoveryStaffGuid(UserContextUtils.getUserGuid());
        refundOrderDO.setRecoveryStaffName(UserContextUtils.getUserName());

        List<OrderItemDO> orderItemDOSMap = JacksonUtils.toObjectList(OrderItemDO.class, JacksonUtils
                .writeValueAsString(orderItemDOS));
        /**
         * orderItemMap 订单商品guid
         */
        Map<Long, OrderItemDO> orderItemMap = CollectionUtil.toMap(orderItemDOSMap, "guid");
        List<OrderItemDO> refundOrderItemDOS = JacksonUtils.toObjectList(OrderItemDO.class, JacksonUtils
                .writeValueAsString(orderItemDOS));
        List<OrderItemRecordDO> refundOrderItemRecordDOS = JacksonUtils.toObjectList(OrderItemRecordDO.class, JacksonUtils
                .writeValueAsString(orderItemRecordDOS));

        /**
         * refundOrderItemMap 订单商品记录guid
         */
        Map<Long, OrderItemDO> refundOrderItemMap = CollectionUtil.toMap(refundOrderItemDOS, "guid");
        List<OrderItemDO> newOrderItemDOS = new ArrayList<>();
        List<OrderItemDO> newOrderItemDOInReturn = new ArrayList<>();
        List<OrderItemRecordDO> newOrderItemRecordDOS = new ArrayList<>();
        List<ItemAttrDO> newItemAttrDOS = new ArrayList<>();
        List<DiscountDO> newDiscountDOS = new ArrayList<>();
        List<FreeReturnItemDO> newFreeItemDOS = new ArrayList<>();
        List<FreeReturnItemDO> newFreeItemDOInReturn = new ArrayList<>();
//        List<TransactionRecordDO> newTransactionRecordDOS = new ArrayList<>();
        Map<String, String> itemGuidMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(orderItemDOS)) {

            for (OrderItemDO orderItemDO : orderItemDOS) {
                Long itemGuid = guids.remove(0);
                OrderItemDO orderItemDOInMap = orderItemMap.get(orderItemDO.getGuid());
                /**
                 * 将订单商品guid作为 k    随机生成的反结账guid作为  v
                 */
                itemGuidMap.put(String.valueOf(orderItemDOInMap.getGuid()), String.valueOf(itemGuid));
                orderItemDOInMap.setGuid(itemGuid);
                OrderItemDO newOrderItemDO = JacksonUtils.toObject(OrderItemDO.class, JacksonUtils
                        .writeValueAsString(orderItemDO));
                newOrderItemDO.setGuid(itemGuid);
                newOrderItemDO.setOriginalOrderItemGuid(orderItemDO.getGuid());
                newOrderItemDO.setGmtCreate(now);
                newOrderItemDO.setIsPay(1);
                newOrderItemDO.setGmtModified(now);
                newOrderItemDO.setOrderGuid(newOrderGuid);
                newOrderItemDOS.add(newOrderItemDO);
                newOrderItemDOInReturn.add(newOrderItemDO);

            }
            for (OrderItemDO orderItemDO : refundOrderItemDOS) {
                Long itemGuid = guids.remove(0);
                Long originalOrderItemGuid = orderItemDO.getGuid();
                OrderItemDO orderItemDOInMap = refundOrderItemMap.get(orderItemDO.getGuid());
                OrderItemDO refundOrderItemDO = JacksonUtils.toObject(OrderItemDO.class, JacksonUtils
                        .writeValueAsString(orderItemDO));
                orderItemDOInMap.setGuid(itemGuid);
                refundOrderItemDO.setGuid(itemGuid);
                refundOrderItemDO.setOriginalOrderItemGuid(originalOrderItemGuid);
                refundOrderItemDO.setGmtCreate(now);
                refundOrderItemDO.setGmtModified(now);
                refundOrderItemDO.setOrderGuid(refundOrderGuid);
                newOrderItemDOS.add(refundOrderItemDO);
            }
            handleOrderItemValue(newOrderItemDOS, newOrderGuid, orderItemMap, refundOrderGuid, refundOrderItemMap);

            orderItemService.saveBatchWithDeleteCache(newOrderItemDOS, String.valueOf(newOrderGuid));
        }


        if (CollectionUtil.isNotEmpty(orderItemRecordDOS)) {
            for (OrderItemRecordDO orderItemRecordDO : orderItemRecordDOS) {
                Long recordGuid = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
                OrderItemRecordDO newOrderItemDO = JacksonUtils.toObject(OrderItemRecordDO.class, JacksonUtils
                        .writeValueAsString(orderItemRecordDO));
                newOrderItemDO.setGuid(recordGuid);
                newOrderItemDO.setOriginalOrderItemGuid(orderItemRecordDO.getGuid());
                newOrderItemDO.setGmtCreate(now);
                newOrderItemDO.setIsPay(1);
                if (!ObjectUtils.isEmpty(itemGuidMap.get(orderItemRecordDO.getOrderItemGuid()))) {
                    newOrderItemDO.setOrderItemGuid(itemGuidMap.get(orderItemRecordDO.getOrderItemGuid()));
                } else {
                    newOrderItemDO.setOrderItemGuid(orderItemRecordDO.getOrderItemGuid());
                }
                newOrderItemDO.setGmtModified(now);
                newOrderItemDO.setOrderGuid(newOrderGuid);
                newOrderItemRecordDOS.add(newOrderItemDO);

            }
            for (OrderItemRecordDO orderItemRecordDO : refundOrderItemRecordDOS) {
                Long recordGuid = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
                OrderItemRecordDO refundOrderItemDO = JacksonUtils.toObject(OrderItemRecordDO.class, JacksonUtils
                        .writeValueAsString(orderItemRecordDO));
                refundOrderItemDO.setGuid(recordGuid);
                refundOrderItemDO.setOriginalOrderItemGuid(orderItemRecordDO.getGuid());
                refundOrderItemDO.setGmtCreate(now);
                refundOrderItemDO.setGmtModified(now);
                refundOrderItemDO.setOrderItemGuid(orderItemRecordDO.getOrderItemGuid());
                refundOrderItemDO.setOrderGuid(refundOrderGuid);
                newOrderItemRecordDOS.add(refundOrderItemDO);
            }
        }
        if (!ObjectUtils.isEmpty(newOrderItemRecordDOS)) {
            //保存到菜品流水表中
            orderItemRecordService.saveBatchWithDeleteCache(newOrderItemRecordDOS);
        }

        if (CollectionUtil.isNotEmpty(freeItemDOS)) {
            List<FreeReturnItemDO> refundFreeItemDOS = JacksonUtils.toObjectList(FreeReturnItemDO.class, JacksonUtils
                    .writeValueAsString(freeItemDOS));
            for (FreeReturnItemDO freeItemDO : freeItemDOS) {
                Long guid = guids.remove(0);
                OrderItemDO orderItemDO = orderItemMap.get(freeItemDO.getOrderItemGuid());
                freeItemDO.setGuid(guid);
                freeItemDO.setGmtCreate(now);
                freeItemDO.setOrderGuid(newOrderGuid);
                freeItemDO.setGmtModified(now);
                freeItemDO.setOrderItemGuid(orderItemDO.getGuid());
                newFreeItemDOS.add(freeItemDO);
                newFreeItemDOInReturn.add(freeItemDO);
            }
            for (FreeReturnItemDO freeItemDO : refundFreeItemDOS) {
                Long guid = guids.remove(0);
                OrderItemDO orderItemDO = refundOrderItemMap.get(freeItemDO.getOrderItemGuid());
                freeItemDO.setGuid(guid);
                freeItemDO.setGmtCreate(now);
                freeItemDO.setGmtModified(now);
                freeItemDO.setOrderGuid(refundOrderGuid);
                freeItemDO.setOrderItemGuid(orderItemDO.getGuid());
                newFreeItemDOS.add(freeItemDO);
            }
            freeReturnItemService.saveBatch(newFreeItemDOS);
        }

        if (CollectionUtil.isNotEmpty(itemAttrDOS)) {
            List<ItemAttrDO> refundItemAttrDOS = JacksonUtils.toObjectList(ItemAttrDO.class, JacksonUtils
                    .writeValueAsString(itemAttrDOS));

            for (ItemAttrDO itemAttrDO : itemAttrDOS) {
                Long guid = guids.remove(0);
                OrderItemDO orderItemDO = orderItemMap.get(itemAttrDO.getOrderItemGuid());
                itemAttrDO.setGuid(guid);
                itemAttrDO.setOrderGuid(newOrderGuid);
                itemAttrDO.setGmtCreate(now);
                itemAttrDO.setGmtModified(now);
                itemAttrDO.setOrderItemGuid(orderItemDO.getGuid());
                newItemAttrDOS.add(itemAttrDO);
            }
            for (ItemAttrDO itemAttrDO : refundItemAttrDOS) {
                Long guid = guids.remove(0);
                OrderItemDO orderItemDO = refundOrderItemMap.get(itemAttrDO.getOrderItemGuid());
                itemAttrDO.setOrderGuid(refundOrderGuid);
                itemAttrDO.setGuid(guid);
                itemAttrDO.setGmtCreate(now);
                itemAttrDO.setGmtModified(now);
                itemAttrDO.setOrderItemGuid(orderItemDO.getGuid());
                newItemAttrDOS.add(itemAttrDO);
            }
            itemAttrService.saveBatch(newItemAttrDOS);
        }
        // 交易记录处理
        recoveryTransactionRecordHandler(recoveryReqDTO, guids, now, transactionRecordDOS, newOrderGuid, refundOrderGuid, orderDO);
        //桌台相关
        if (!fastFood) {
            recoveryTableHandler(recoveryReqDTO, orderDO, newOrderDO, refundOrderDO, virtualTable, combine, mainOrderGuid);
        }

        //原单聚合支付转入新单预付金
        newOrderDO.setPrepayFee(BigDecimal.ZERO);

        if (CollectionUtil.isNotEmpty(discountDOS)) {
            List<DiscountDO> refundDiscountDOS = JacksonUtils.toObjectList(DiscountDO.class, JacksonUtils
                    .writeValueAsString(discountDOS));

            for (DiscountDO discountDO : discountDOS) {
                Long guid = guids.remove(0);
                discountDO.setGuid(guid);
                discountDO.setGmtCreate(now);
                discountDO.setGmtModified(now);
                ArrayList<Integer> discountTypeList = new ArrayList<>();
                //团购券不能撤销，价格重新计算的时候  是以团购券价格为基准的
                discountTypeList.add(DiscountTypeEnum.GROUPON.getCode());
                // 随行红包的金额跟着反结账带过去
                discountTypeList.add(DiscountTypeEnum.FOLLOW_RED_PACKET.getCode());
                if (!discountTypeList.contains(discountDO.getDiscountType())) {
                    discountDO.setDiscountFee(BigDecimal.ZERO);
                }
                discountDO.setOrderGuid(newOrderGuid);
                discountDO.setDiscountState(1);
                newDiscountDOS.add(discountDO);
            }
            for (DiscountDO discountDO : refundDiscountDOS) {
                Long guid = guids.remove(0);
                discountDO.setGuid(guid);
                discountDO.setGmtCreate(now);
                discountDO.setDiscountFee(BigDecimalUtil.negative(discountDO.getDiscountFee()));
                discountDO.setGmtModified(now);
                discountDO.setOrderGuid(refundOrderGuid);
                discountDO.setDiscountState(1);
                newDiscountDOS.add(discountDO);
            }
            discountService.saveBatch(newDiscountDOS);
        }

        if (CollectionUtil.isNotEmpty(grouponDOS)) {
            // 删除之前的
            List<Long> grouponGuids = grouponDOS.stream().map(GrouponDO::getGuid).collect(Collectors.toList());
            grouponMpService.removeByIds(grouponGuids);
            for (GrouponDO grouponDO : grouponDOS) {
                Long guid = guids.remove(0);
                grouponDO.setGuid(guid);
                grouponDO.setGmtCreate(now);
                grouponDO.setGmtModified(now);
                grouponDO.setOrderGuid(newOrderGuid);
            }
            grouponMpService.saveBatch(grouponDOS);
        }
        // 保存附加费
        saveBatchAppendFee(appendFeeDOS, now, guids, newOrderGuid, refundOrderGuid);
        //不影响is_handle
        newOrderDO.setIsHandle(null);
        refundOrderDO.setIsHandle(null);

        List<OrderDO> orderDOS = new ArrayList<>();
        orderDOS.add(newOrderDO);

        if (!fastFood) {
            //反结账回来的新单，需要在redis中设置默认值
            redisService.putTradeVersion(String.valueOf(newOrderDO.getGuid()));
        }

        orderDOS.add(refundOrderDO);
        orderService.saveBatch(orderDOS);
        //===================== 反结账将原单状态改成反结账 =====================
        orderDO.setState(StateEnum.ANTI_SETTLEMENT.getCode());
        orderService.updateByIdWithDeleteCache(orderDO);
        // 新增退款订单扩展表、新单订单扩展表
        saveBatchOrderExtends(orderDO, newOrderDO, refundOrderDO, recoveryReqDTO);
        // 新增退款订单商品明细扩展表、新单订单商品明细扩展表，换菜表
        saveBatchOrderItemExtends(recoveryReqDTO, newOrderGuid, refundOrderGuid, newOrderItemDOS);
        if (orderDO.getTradeMode().equals(TradeModeEnum.FAST.getCode())) {
            //反结账退估清
            DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(orderGuid);
            itemClientService.returnEstimate(orderDetail.getDineInItemDTOS());
        }
        // 快餐反结账时退菜：点菜、赠送
        fastFoodRecoveryReturnItem(recoveryReqDTO, newOrderDO, newOrderItemDOInReturn, newFreeItemDOInReturn);

        // 预付金处理
        handleRecoveryReserve(orderDO, orderGuid, newOrderGuid);

        return new Pair<>(newOrderGuid, refundOrderGuid);
    }

    private void recoveryTableHandler(RecoveryReqDTO recoveryReqDTO, OrderDO orderDO, OrderDO newOrderDO,
                                      OrderDO refundOrderDO, Integer virtualTable, boolean combine, Pair<Long, Long> mainOrderGuid) {
        TableWhetherOpenDTO tableWhetherOpenDTO = new TableWhetherOpenDTO();
        if (orderDO.getUpperState().equals(UpperStateEnum.GENERAL.getCode())
                || UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
            BaseTableDTO baseTableDTO = orderTransform.recoveryReqDTO2BaseTableDTO(recoveryReqDTO);
            baseTableDTO.setOrderGuid(String.valueOf(newOrderDO.getGuid()));
            baseTableDTO.setTableGuid(orderDO.getDiningTableGuid());
            tableWhetherOpenDTO = tableClientService.tableWhetherOpen(baseTableDTO);
            if (tableWhetherOpenDTO.isWhetherOpened()) {
                virtualTable = 0;
            }
        }
        newOrderDO.setVirtualTable(virtualTable);
        refundOrderDO.setVirtualTable(virtualTable);
        if (virtualTable == 0 && !combine) {
            newOrderDO.setDiningTableName(CommonUtil.jointDiningTableName(tableWhetherOpenDTO.getNewTableAreaName(), tableWhetherOpenDTO
                    .getNewTableCode()));
            refundOrderDO.setDiningTableName(CommonUtil.jointDiningTableName(tableWhetherOpenDTO.getNewTableAreaName(), tableWhetherOpenDTO
                    .getNewTableCode()));

        }
        if (combine && mainOrderGuid != null) {
            newOrderDO.setMainOrderGuid(mainOrderGuid.getKey());
            refundOrderDO.setMainOrderGuid(mainOrderGuid.getValue());
        }
    }


    /**
     * 反结账退款 交易记录处理
     */
    private void recoveryTransactionRecordHandler(RecoveryReqDTO recoveryReqDTO, List<Long> guids, LocalDateTime now,
                                                  List<TransactionRecordDO> transactionRecordDOS, Long newOrderGuid, Long refundOrderGuid,
                                                  OrderDO orderDO) {
        if (CollectionUtil.isEmpty(transactionRecordDOS)) {
            return;
        }
        Iterator<TransactionRecordDO> iterator = transactionRecordDOS.iterator();
        while (iterator.hasNext()) {
            TransactionRecordDO transactionRecordDO = iterator.next();
            Long guid = guids.remove(0);
            Long oldGuid = transactionRecordDO.getGuid();
            transactionRecordDO.setGuid(guid);
            transactionRecordDO.setCreateTime(now);
            transactionRecordDO.setGmtCreate(now);
            transactionRecordDO.setGmtModified(now);
            transactionRecordDO.setOrderGuid(refundOrderGuid);
            //反结账时需更新操作人信息(bug fix:18151)
            transactionRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
            transactionRecordDO.setStaffName(UserContextUtils.getUserName());
            if (recoveryReqDTO.isFacePay()) {
                //人脸支付再开接口确认退款状态
                transactionRecordDO.setState(TradeStateEnum.READY.getCode());
            }
            // 麓豆支付回退
            if (transactionRecordDO.getPaymentType().equals(PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode())) {
                Boolean refundFlag = ludouMemberExternalService.refund(recoveryReqDTO.getOrderGuid(),
                        transactionRecordDO.getBankTransactionId(), orderDO.getActuallyPayFee(),
                        transactionRecordDO.getAmount(), recoveryReqDTO.getReason());
                if (Boolean.FALSE.equals(refundFlag)) {
                    throw new BusinessException("麓豆退款失败");
                }
            }
            if (TradeTypeEnum.GENERAL_IN.getCode() == transactionRecordDO.getTradeType()
                    && transactionRecordDO.getPaymentType().equals(PaymentTypeEnum.AGG.getCode())
                    && TradeStateEnum.SUCCESS.getCode() == transactionRecordDO.getState()) {
                recoveryAggPay(iterator, recoveryReqDTO, oldGuid, newOrderGuid, transactionRecordDO);
            }
            //反结账退挂账
            if (transactionRecordDO.getPaymentType().equals(PaymentTypeEnum.DEBT_PAY.getCode())) {
                DebtUnitRecordSaveReqDTO saveReqDTO = new DebtUnitRecordSaveReqDTO();
                saveReqDTO.setOrderGuid(orderDO.getGuid().toString());
                saveReqDTO.setDebtFee(orderDO.getActuallyPayFee());
                saveReqDTO.setChangeType(1);
                debtUnitRecordService.saveDebtUnit(saveReqDTO);
            }
            transactionRecordDO.setAmount(BigDecimalUtil.negative(transactionRecordDO.getAmount()));
            transactionRecordDO.setTradeType(TradeTypeEnum.REFUND_OUT.getCode());
        }
        if (CollectionUtils.isNotEmpty(transactionRecordDOS)) {
            // 插入退单相反的支付记录
            transactionRecordService.saveBatch(transactionRecordDOS);
        }
    }

    /**
     * 聚合支付反结账退款
     */
    private void recoveryAggPay(Iterator<TransactionRecordDO> iterator, RecoveryReqDTO recoveryReqDTO,
                                Long oldTransactionRecordGuid, Long newOrderGuid, TransactionRecordDO transactionRecordDO) {
        String orderGuid = recoveryReqDTO.getOrderGuid();
        // 查询扩展
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderGuid);
        if (Objects.isNull(orderExtendsDO) || !Boolean.TRUE.equals(orderExtendsDO.getIsMultipleAggPay())) {
            // 反结账聚合支付
            // 聚合支付退款
            refundAggPay(recoveryReqDTO, oldTransactionRecordGuid, recoveryReqDTO.getOrderGuid(),
                    transactionRecordDO);
            transactionRecordService.update(new LambdaUpdateWrapper<TransactionRecordDO>()
                    .eq(TransactionRecordDO::getGuid, oldTransactionRecordGuid)
                    .set(TransactionRecordDO::getRefundableFee, BigDecimal.ZERO)
                    .set(TransactionRecordDO::getRefundAmount, transactionRecordDO.getAmount())
            );
            return;
        }
        // 查询是否多次支付
        List<MultipleTransactionRecordDO> multipleTransactionRecordList = multipleTransactionRecordService.listByOrderGuid(recoveryReqDTO.getOrderGuid());
        List<MultipleTransactionRecordDO> payedTransactionRecordList = multipleTransactionRecordList.stream()
                .filter(e -> e.getAmount().compareTo(e.getRefundAmount()) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(multipleTransactionRecordList)) {
            return;
        }
        if (payedTransactionRecordList.size() == 1) {
            // 聚合支付退款
            MultipleTransactionRecordDO multipleTransactionRecordDO = payedTransactionRecordList.get(0);
            Long multipleTransactionOrderGuid = multipleTransactionRecordDO.getOrderGuid();
            if (Objects.nonNull(multipleTransactionRecordDO.getOriginalOrderGuid())) {
                // 如果是并桌结账 子桌的支付记录上orderGuid被修改为主桌orderGuid，该字段记录原订单guid
                multipleTransactionOrderGuid = multipleTransactionRecordDO.getOriginalOrderGuid();
            }
            // 直接退款
            refundAggPay(recoveryReqDTO, multipleTransactionRecordDO.getGuid(), String.valueOf(multipleTransactionOrderGuid),
                    transactionRecordDO);
            transactionRecordService.update(new LambdaUpdateWrapper<TransactionRecordDO>()
                    .eq(TransactionRecordDO::getGuid, oldTransactionRecordGuid)
                    .set(TransactionRecordDO::getRefundableFee, BigDecimal.ZERO)
                    .set(TransactionRecordDO::getRefundAmount, transactionRecordDO.getAmount())
            );
            multipleTransactionRecordService.update(new LambdaUpdateWrapper<MultipleTransactionRecordDO>()
                    .eq(MultipleTransactionRecordDO::getGuid, oldTransactionRecordGuid)
                    .set(MultipleTransactionRecordDO::getRefundableFee, BigDecimal.ZERO)
                    .set(MultipleTransactionRecordDO::getRefundAmount, transactionRecordDO.getAmount())
            );
            // 退款单信息
            MultipleTransactionRecordDO refundMultipleTransactionRecordDO = new MultipleTransactionRecordDO();
            BeanUtils.copyProperties(multipleTransactionRecordDO, refundMultipleTransactionRecordDO);
            refundMultipleTransactionRecordDO.setGuid(dynamicHelper.generateGuid(MultipleTransactionRecordDO.class.getSimpleName()));
            refundMultipleTransactionRecordDO.setAmount(BigDecimalUtil.negative(refundMultipleTransactionRecordDO.getAmount()));
            refundMultipleTransactionRecordDO.setTradeType(TradeTypeEnum.REFUND_OUT.getCode());
            refundMultipleTransactionRecordDO.setTransactionRecordGuid(transactionRecordDO.getGuid());
            refundMultipleTransactionRecordDO.setOrderGuid(transactionRecordDO.getOrderGuid());
            refundMultipleTransactionRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
            refundMultipleTransactionRecordDO.setStaffName(UserContextUtils.getUserName());
            refundMultipleTransactionRecordDO.setOriginalOrderGuid(multipleTransactionRecordDO.getOrderGuid());
            refundMultipleTransactionRecordDO.setOriginalMultipleTransactionRecordGuid(multipleTransactionRecordDO.getGuid());
            multipleTransactionRecordService.save(refundMultipleTransactionRecordDO);
            return;
        }
        // 多次支付
        // 记录在新的订单上
        TransactionRecordDO newAggPayTransactionRecordDO = new TransactionRecordDO();
        BeanUtils.copyProperties(transactionRecordDO, newAggPayTransactionRecordDO);
        newAggPayTransactionRecordDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.RECOVERY));
        newAggPayTransactionRecordDO.setOrderGuid(newOrderGuid);
        transactionRecordService.save(newAggPayTransactionRecordDO);
        // 多次聚合支付记录记录在新单上
        List<Long> multipleTransactionRecordGuids = dynamicHelper.generateGuids(
                MultipleTransactionRecordDO.class.getSimpleName(), multipleTransactionRecordList.size());
        for (MultipleTransactionRecordDO multipleTransactionRecord : multipleTransactionRecordList) {
            // 原单信息
            Long originalMultipleTransactionRecordGuid = multipleTransactionRecord.getGuid();
            Long originalOrderGuid = multipleTransactionRecord.getOrderGuid();
            if (Objects.nonNull(multipleTransactionRecord.getOriginalOrderGuid())) {
                // 如果是并桌结账 子桌的支付记录上orderGuid被修改为主桌orderGuid，该字段记录原订单guid
                originalOrderGuid = multipleTransactionRecord.getOriginalOrderGuid();
            }
            // 新单信息
            multipleTransactionRecord.setGuid(multipleTransactionRecordGuids.remove(0));
            multipleTransactionRecord.setTransactionRecordGuid(newAggPayTransactionRecordDO.getGuid());
            multipleTransactionRecord.setOrderGuid(newOrderGuid);
            multipleTransactionRecord.setStaffGuid(UserContextUtils.getUserGuid());
            multipleTransactionRecord.setStaffName(UserContextUtils.getUserName());
            multipleTransactionRecord.setOriginalOrderGuid(originalOrderGuid);
            multipleTransactionRecord.setOriginalMultipleTransactionRecordGuid(originalMultipleTransactionRecordGuid);
        }
        multipleTransactionRecordService.saveBatch(multipleTransactionRecordList);
        iterator.remove();
    }

    /**
     * 聚合支付退款
     */
    private void refundAggPay(RecoveryReqDTO recoveryReqDTO, Long oldTransactionRecordGuid, String orderGuid,
                              TransactionRecordDO transactionRecordDO) {
        AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setOrderGUID(orderGuid);
        aggRefundReqDTO.setRefundType(0);
        aggRefundReqDTO.setRefundFee(transactionRecordDO.getAmount()
                .subtract(Optional.ofNullable(transactionRecordDO.getRefundAmount()).orElse(BigDecimal.ZERO)));
        aggRefundReqDTO.setReason("聚合支付退款");
        aggRefundReqDTO.setPayGUID(String.valueOf(oldTransactionRecordGuid));
        SaasAggRefundDTO saasAggRefundDTO = orderTransform.recoveryReqDTO2SaasAggRefundDTO(recoveryReqDTO);
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        log.info("订单反结账时退款入参：{}", JacksonUtils.writeValueAsString(saasAggRefundDTO));
        AggRefundRespDTO refund = aggPayClientService.refund(saasAggRefundDTO);
        //退款结果校验
        BillVerifyHelper.refundResult(refund, false);
    }


    private void handleRecoveryReserve(OrderDO orderDO, String orderGuid, Long newOrderGuid) {
        if (!StringUtils.isEmpty(orderDO.getReserveGuid())) {
            ReserveRecoveryDTO recoveryDTO = new ReserveRecoveryDTO();
            recoveryDTO.setOrderGuid(orderGuid);
            recoveryDTO.setReserveGuid(orderDO.getReserveGuid());
            recoveryDTO.setNewOrderGuid(String.valueOf(newOrderGuid));
            try {
                reserveClientService.recovery(recoveryDTO);
            } catch (Exception e) {
                log.error("[预付金反结账]异常：{},e=｛｝", e.getMessage(), e);
            }
        }
    }

    /**
     * 新增退款订单附加费，新单附加费
     */
    private void saveBatchAppendFee(List<AppendFeeDO> appendFeeList, LocalDateTime now,
                                    List<Long> guids, Long newOrderGuid, Long refundOrderGuid) {
        if (CollectionUtils.isEmpty(appendFeeList)) {
            return;
        }
        // 退款单
        for (AppendFeeDO appendFeeDO : appendFeeList) {
            Long guid = guids.remove(0);
            appendFeeDO.setGuid(guid);
            appendFeeDO.setGmtCreate(now);
            appendFeeDO.setGmtModified(now);
            appendFeeDO.setOrderGuid(refundOrderGuid);
        }
        appendFeeMpService.saveBatch(appendFeeList);
        // 新单
        for (AppendFeeDO appendFeeDO : appendFeeList) {
            Long guid = guids.remove(0);
            appendFeeDO.setGuid(guid);
            appendFeeDO.setGmtCreate(now);
            appendFeeDO.setGmtModified(now);
            appendFeeDO.setOrderGuid(newOrderGuid);
        }
        appendFeeMpService.saveBatch(appendFeeList);
    }

    /**
     * 新增退款订单扩展表、新单订单扩展表
     */
    private void saveBatchOrderExtends(OrderDO orderDO, OrderDO newOrderDO, OrderDO refundOrderDO, RecoveryReqDTO recoveryReqDTO) {
        // 查询原扩展表
        OrderExtendsDO oldOrderExtendsDO = orderExtendsService.getById(orderDO.getGuid());
        // 原单扩展表
        OrderExtendsDO orderExtendsDO = new OrderExtendsDO();
        BeanUtils.copyProperties(orderDO, orderExtendsDO);
        orderExtendsDO.setRecoveryAuthStaffGuid(recoveryReqDTO.getAuthStaffGuid());
        orderExtendsDO.setRecoveryAuthStaffName(recoveryReqDTO.getAuthStaffName());
        orderExtendsDO.setRecoveryAuthStaffPicture(recoveryReqDTO.getAuthStaffPicture());
        // 退款单扩展表
        OrderExtendsDO refundOrderExtendsDO = new OrderExtendsDO();
        if (Objects.nonNull(oldOrderExtendsDO)) {
            BeanUtils.copyProperties(oldOrderExtendsDO, refundOrderExtendsDO);
        }
        BeanUtils.copyProperties(refundOrderDO, refundOrderExtendsDO);
        refundOrderExtendsDO.setInvoiceCode(null);
        refundOrderExtendsDO.setIsInvoice(Boolean.FALSE);
        refundOrderExtendsDO.setRecoveryAuthStaffGuid(recoveryReqDTO.getAuthStaffGuid());
        refundOrderExtendsDO.setRecoveryAuthStaffName(recoveryReqDTO.getAuthStaffName());
        refundOrderExtendsDO.setRecoveryAuthStaffPicture(recoveryReqDTO.getAuthStaffPicture());
        // 新单扩展表
        OrderExtendsDO newOrderExtendsDO = new OrderExtendsDO();
        if (Objects.nonNull(oldOrderExtendsDO)) {
            BeanUtils.copyProperties(oldOrderExtendsDO, newOrderExtendsDO);
        }
        BeanUtils.copyProperties(newOrderDO, newOrderExtendsDO);
        newOrderExtendsDO.setInvoiceCode(null);
        newOrderExtendsDO.setIsInvoice(Boolean.FALSE);
        newOrderExtendsDO.setRecoveryAuthStaffGuid(recoveryReqDTO.getAuthStaffGuid());
        newOrderExtendsDO.setRecoveryAuthStaffName(recoveryReqDTO.getAuthStaffName());
        newOrderExtendsDO.setRecoveryAuthStaffPicture(recoveryReqDTO.getAuthStaffPicture());

        // 根据订单查询预付金信息
        SingleDataDTO dataDTO = new SingleDataDTO();
        dataDTO.setData(String.valueOf(orderDO.getGuid()));
        log.info("[根据订单查询预付金信息]dataDTO={}", JacksonUtils.writeValueAsString(dataDTO));
        ReserveRecordDTO reserveRecordDTO = reserveClientService.queryByOrderGuid(dataDTO);
        log.info("[根据订单查询预付金信息]reserveRecordDTO={}", JacksonUtils.writeValueAsString(reserveRecordDTO));
        if (!ObjectUtils.isEmpty(reserveRecordDTO)) {
            orderExtendsDO.setReservePaymentType(reserveRecordDTO.getPaymentType());
            orderExtendsDO.setReservePaymentTypeName(reserveRecordDTO.getPaymentTypeName());
            refundOrderExtendsDO.setReservePaymentType(reserveRecordDTO.getPaymentType());
            refundOrderExtendsDO.setReservePaymentTypeName(reserveRecordDTO.getPaymentTypeName());
            newOrderExtendsDO.setReservePaymentType(reserveRecordDTO.getPaymentType());
            newOrderExtendsDO.setReservePaymentTypeName(reserveRecordDTO.getPaymentTypeName());
        }
        List<OrderExtendsDO> orderExtendsList = Lists.newArrayList(orderExtendsDO, refundOrderExtendsDO, newOrderExtendsDO);
        orderExtendsService.saveOrUpdateBatch(orderExtendsList);
    }


    /**
     * 新增退款订单商品明细扩展表、新单订单商品明细扩展表，换菜表
     */
    private void saveBatchOrderItemExtends(RecoveryReqDTO recoveryReqDTO, Long newOrderGuid, Long refundOrderGuid, List<OrderItemDO> newOrderItemDOList) {
        if (CollectionUtils.isEmpty(newOrderItemDOList)) {
            return;
        }
        Long originalOrderGuid = Long.valueOf(recoveryReqDTO.getOrderGuid());
        // 查询原单的商品明细扩展表
        List<OrderItemExtendsDO> orderItemExtendsList = orderItemExtendsService.listByOrderGuid(originalOrderGuid);
        if (CollectionUtil.isEmpty(orderItemExtendsList)) {
            return;
        }
        newOrderItemDOList = newOrderItemDOList.stream()
                .filter(e -> Objects.nonNull(e.getOriginalOrderItemGuid()))
                .collect(Collectors.toList());
        List<OrderItemDO> refundOrderItemList = newOrderItemDOList.stream()
                .filter(e -> e.getOrderGuid().equals(refundOrderGuid))
                .collect(Collectors.toList());
        // 原订单商品明细id -> 退款单商品明细id
        Map<Long, OrderItemDO> refundOrderItemMap = refundOrderItemList.stream()
                .collect(Collectors.toMap(OrderItemDO::getOriginalOrderItemGuid, Function.identity(), (key1, key2) -> key2));
        // 退款订单商品明细扩展表
        List<OrderItemExtendsDO> recoveryItemExtendsList = buildRecoveryOrderItemExtends(refundOrderGuid, refundOrderItemMap, orderItemExtendsList);
        // 新单订单商品明细扩展表
        List<OrderItemDO> newOrderItemList = newOrderItemDOList.stream()
                .filter(e -> e.getOrderGuid().equals(newOrderGuid))
                .collect(Collectors.toList());
        // 原订单商品明细id -> 新单商品明细id
        Map<Long, OrderItemDO> newOrderItemMap = newOrderItemList.stream()
                .collect(Collectors.toMap(OrderItemDO::getOriginalOrderItemGuid, Function.identity(), (key1, key2) -> key2));
        List<OrderItemExtendsDO> newItemExtendsList = buildRecoveryOrderItemExtends(newOrderGuid, newOrderItemMap, orderItemExtendsList);
        if (CollectionUtils.isNotEmpty(newItemExtendsList)) {
            recoveryItemExtendsList.addAll(newItemExtendsList);
        }
        // 持久化
        if (CollectionUtils.isNotEmpty(recoveryItemExtendsList)) {
            orderItemExtendsService.saveBatch(recoveryItemExtendsList);
        }
        // 处理换菜明细
        packageSubgroupChangesItemHandler(originalOrderGuid, newOrderGuid, refundOrderGuid, refundOrderItemMap, newOrderItemMap);
    }


    /**
     * 处理换菜明细
     */
    private void packageSubgroupChangesItemHandler(Long originalOrderGuid, Long newOrderGuid, Long refundOrderGuid,
                                                   Map<Long, OrderItemDO> refundOrderItemMap,
                                                   Map<Long, OrderItemDO> newOrderItemMap) {
        // 查询原订单上换菜明细
        List<OrderItemChangesDO> orderItemChangesList = orderItemChangesService.listByOrderGuid(originalOrderGuid);
        if (CollectionUtils.isEmpty(orderItemChangesList)) {
            return;
        }
        // 退款订单换菜
        List<OrderItemChangesDO> recoveryItemChangesList = buildRecoveryOrderItemChanges(refundOrderGuid, refundOrderItemMap, orderItemChangesList);
        // 新单订单换菜
        List<OrderItemChangesDO> newItemChangesList = buildRecoveryOrderItemChanges(newOrderGuid, newOrderItemMap, orderItemChangesList);
        if (CollectionUtils.isNotEmpty(newItemChangesList)) {
            recoveryItemChangesList.addAll(newItemChangesList);
        }
        // 持久化
        if (CollectionUtils.isNotEmpty(recoveryItemChangesList)) {
            orderItemChangesService.saveBatch(recoveryItemChangesList);
        }
        Map<Long, OrderItemChangesDO> recoveryItemChangesMap = recoveryItemChangesList.stream()
                .collect(Collectors.toMap(OrderItemChangesDO::getOriginalChangeItemGuid, Function.identity(), (key1, key2) -> key1));
        Map<Long, OrderItemChangesDO> newItemChangesMap = newItemChangesList.stream()
                .collect(Collectors.toMap(OrderItemChangesDO::getOriginalChangeItemGuid, Function.identity(), (key1, key2) -> key1));
        // 处理换菜的属性组
        packageSubgroupChangesItemAttrHandler(newOrderGuid, refundOrderGuid, orderItemChangesList,
                recoveryItemChangesMap, newItemChangesMap);
    }


    /**
     * 处理换菜的属性组
     */
    private void packageSubgroupChangesItemAttrHandler(Long newOrderGuid, Long refundOrderGuid,
                                                       List<OrderItemChangesDO> orderItemChangesList,
                                                       Map<Long, OrderItemChangesDO> recoveryItemChangesMap,
                                                       Map<Long, OrderItemChangesDO> newItemChangesMap) {
        // 查询换菜原菜明细
        List<Long> orderItemChangesGuidList = orderItemChangesList.stream().map(OrderItemChangesDO::getGuid).collect(Collectors.toList());
        List<ItemAttrDO> changesItemAttrList = itemAttrService.listByItemGuids(orderItemChangesGuidList);
        if (CollectionUtils.isEmpty(changesItemAttrList)) {
            return;
        }
        log.info("反结账换菜原菜明细列表,changesItemAttrList:{}", JacksonUtils.writeValueAsString(changesItemAttrList));
        // 退菜单 换菜原菜属性
        List<ItemAttrDO> recoveryItemAttrList = buildRecoveryOrderItemAttr(refundOrderGuid, recoveryItemChangesMap, changesItemAttrList);
        // 新单 换菜原菜属性
        List<ItemAttrDO> newItemAttrList = buildRecoveryOrderItemAttr(newOrderGuid, newItemChangesMap, changesItemAttrList);
        if (CollectionUtils.isNotEmpty(newItemAttrList)) {
            recoveryItemAttrList.addAll(newItemAttrList);
        }
        // 持久化
        if (CollectionUtils.isNotEmpty(recoveryItemAttrList)) {
            log.info("反结账换菜原菜明细持久化,recoveryItemAttrList:{}", JacksonUtils.writeValueAsString(recoveryItemAttrList));
            itemAttrService.saveBatch(recoveryItemAttrList);
        }
    }

    /**
     * 构建换菜原菜属性表
     */
    private List<ItemAttrDO> buildRecoveryOrderItemAttr(Long refundOrderGuid, Map<Long, OrderItemChangesDO> orderChangeItemMap,
                                                        List<ItemAttrDO> changesItemAttrList) {
        LocalDateTime now = LocalDateTime.now();
        List<Long> guids = dynamicHelper.generateGuids(ItemAttrDO.class.getSimpleName(), changesItemAttrList.size());
        List<ItemAttrDO> refundItemAttrList = Lists.newArrayList();
        for (ItemAttrDO itemAttrDO : changesItemAttrList) {
            OrderItemChangesDO orderItemChangesDO = orderChangeItemMap.get(itemAttrDO.getOrderItemGuid());
            if (Objects.isNull(orderItemChangesDO)) {
                continue;
            }
            ItemAttrDO recoveryItemAttrDO = new ItemAttrDO();
            BeanUtils.copyProperties(itemAttrDO, recoveryItemAttrDO);
            recoveryItemAttrDO.setGuid(guids.remove(0));
            recoveryItemAttrDO.setOrderGuid(refundOrderGuid);
            recoveryItemAttrDO.setOrderItemGuid(orderItemChangesDO.getGuid());
            recoveryItemAttrDO.setGmtCreate(now);
            recoveryItemAttrDO.setGmtModified(now);
            refundItemAttrList.add(recoveryItemAttrDO);
        }
        return refundItemAttrList;
    }


    /**
     * 构建订单商品明细扩展表
     */
    private List<OrderItemExtendsDO> buildRecoveryOrderItemExtends(Long refundOrderGuid, Map<Long, OrderItemDO> orderItemMap,
                                                                   List<OrderItemExtendsDO> orderItemExtendsList) {
        LocalDateTime now = LocalDateTime.now();
        List<OrderItemExtendsDO> refundItemExtendsList = Lists.newArrayList();
        for (OrderItemExtendsDO itemExtendsDO : orderItemExtendsList) {
            OrderItemDO originalOrderItemDO = orderItemMap.get(itemExtendsDO.getGuid());
            if (Objects.isNull(originalOrderItemDO)) {
                continue;
            }
            OrderItemExtendsDO recoveryItemExtendsDO = new OrderItemExtendsDO();
            BeanUtils.copyProperties(itemExtendsDO, recoveryItemExtendsDO);
            recoveryItemExtendsDO.setGuid(originalOrderItemDO.getGuid());
            recoveryItemExtendsDO.setOrderGuid(refundOrderGuid);
            recoveryItemExtendsDO.setGmtCreate(now);
            recoveryItemExtendsDO.setGmtModified(now);
            refundItemExtendsList.add(recoveryItemExtendsDO);
        }
        return refundItemExtendsList;
    }

    /**
     * 构建订单商品换菜
     */
    private List<OrderItemChangesDO> buildRecoveryOrderItemChanges(Long refundOrderGuid, Map<Long, OrderItemDO> orderItemMap,
                                                                   List<OrderItemChangesDO> orderItemChangesList) {
        if (CollectionUtils.isEmpty(orderItemChangesList)) {
            return Lists.newArrayList();
        }
        LocalDateTime now = LocalDateTime.now();
        List<OrderItemChangesDO> refundItemChangesList = Lists.newArrayList();
        List<Long> guids = dynamicHelper.generateGuids(OrderItemChangesDO.class.getSimpleName(), orderItemChangesList.size());
        for (OrderItemChangesDO changesDO : orderItemChangesList) {
            OrderItemDO originalOrderItemDO = orderItemMap.get(changesDO.getOrderItemGuid());
            OrderItemChangesDO recoveryItemChangesDO = new OrderItemChangesDO();
            BeanUtils.copyProperties(changesDO, recoveryItemChangesDO);
            if (Objects.isNull(originalOrderItemDO)) {
                recoveryItemChangesDO.setOrderItemGuid(changesDO.getOrderItemGuid());
            } else {
                recoveryItemChangesDO.setOrderItemGuid(originalOrderItemDO.getGuid());
            }
            recoveryItemChangesDO.setGuid(guids.remove(0));
            recoveryItemChangesDO.setOrderGuid(refundOrderGuid);
            recoveryItemChangesDO.setGmtCreate(now);
            recoveryItemChangesDO.setGmtModified(now);
            recoveryItemChangesDO.setOriginalChangeItemGuid(changesDO.getGuid());
            refundItemChangesList.add(recoveryItemChangesDO);
        }
        return refundItemChangesList;
    }

    private void handleOrderItemValue(List<OrderItemDO> newOrderItemDOS,
                                      Long newOrderGuid,
                                      Map<Long, OrderItemDO> orderItemMap,
                                      Long refundOrderGuid,
                                      Map<Long, OrderItemDO> refundOrderItemMap) {
        for (OrderItemDO newOrderItemDO : newOrderItemDOS) {
            if (newOrderItemDO.getParentItemGuid() != null && newOrderItemDO.getParentItemGuid() > 0) {
                if (newOrderItemDO.getOrderGuid().equals(newOrderGuid)) {
                    OrderItemDO orderItemDO = orderItemMap.get(newOrderItemDO.getParentItemGuid());
                    newOrderItemDO.setParentItemGuid(orderItemDO.getGuid());
                }
                if (newOrderItemDO.getOrderGuid().equals(refundOrderGuid)) {
                    newOrderItemDO.setParentItemGuid(refundOrderItemMap.get(newOrderItemDO.getParentItemGuid())
                            .getGuid());
                }
            }
        }
    }

    @Override
    public boolean printPreBill(SingleDataDTO singleDataDTO) {
        List<OrderDO> orderDOS = new ArrayList<>();
        //=====================这里存在并发情况导致数据存在问题，新建对象，只修改预打印次数=====================
        OrderDO orderDO = orderService.getById(singleDataDTO.getData());
//        orderDO.setPrintPreBillNum(orderDO.getPrintPreBillNum() + 1);
//        orderDOS.add(orderDO);
        OrderDO toUpdateDo = new OrderDO();
        toUpdateDo.setGuid(orderDO.getGuid());
        toUpdateDo.setPrintPreBillNum(orderDO.getPrintPreBillNum() + 1);
        orderDOS.add(toUpdateDo);

        BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setOrderGuid(singleDataDTO.getData());
        billCalculateReqDTO.setMemberLogin(-1);
        DineinOrderDetailRespDTO orderDetail = calculateService.calculate(billCalculateReqDTO);
        // calculate逻辑不好修改，单独再设置附加费详情，需要查询出子单的附加费
        setOrderFeeDetailDTO(orderDetail);
        if (CollectionUtils.isNotEmpty(orderDetail.getOtherOrderDetails())) {
            orderDetail.getOtherOrderDetails().forEach(this::setOrderFeeDetailDTO);
        }

        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetail.getSubOrderDetails();
        if (CollectionUtil.isNotEmpty(subOrderDetails)) {
            for (DineinOrderDetailRespDTO subOrderDetail : subOrderDetails) {
                OrderDO updateOrderDO = new OrderDO();
                updateOrderDO.setGuid(Long.valueOf(subOrderDetail.getGuid()));
                updateOrderDO.setPrintPreBillNum(orderDO.getPrintPreBillNum() + 1);
                orderDOS.add(updateOrderDO);
            }
        }

        //设置主扫订单信息
        BillAggPayReqDTO billAggPayReq = OrderConverter.fromPreBillOrderToAggPayReq(orderDetail);
        billAggPayReq.setDeviceId(singleDataDTO.getDeviceId());
        billAggPayReq.setDeviceType(singleDataDTO.getDeviceType());

        String qrCodeUrl = requestAggQrCodeUrl(billAggPayReq);
        if (qrCodeUrl != null) {
            //设置支付二维码地址
            orderDetail.setPayQrCode(qrCodeUrl);
        }

        log.info("预结账，打印前信息：{}", orderDetail);
        dineInPrintService.printPreCheck(singleDataDTO, orderDetail);
        //通知一体机修改桌台状态
        tableService.printPreBillChange(singleDataDTO, orderDO);
        return orderService.updateBatchByIdWithDeleteCache(orderDOS);
    }

    private void setOrderFeeDetailDTO(DineinOrderDetailRespDTO orderDetail) {
        OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        orderFeeDetailDTO.setAppendFeeDetailDTOS(appendFeeService.getOrderAllAppendFeeList(orderDetail.getGuid()));
        orderDetail.setOrderFeeDetailDTO(orderFeeDetailDTO);
    }

    private String requestAggQrCodeUrl(BillAggPayReqDTO billAggPayReq) {
        try {
            //需要判断打印服务是否开启了二维码扫码
            if (!printClientService.judgeEnablePreCheckFormat(billAggPayReq.getStoreGuid())) {
                return null;
            }
            log.info("发起预支付 aggPayReqDTO={}", JacksonUtils.writeValueAsString(billAggPayReq));
            // 预下单获取码
            BillAggPayRespDTO aggPayRespDTO = aggPay(billAggPayReq);
            log.info("发起预支付返回结果 aggPayRespDTO={}", JacksonUtils.writeValueAsString(aggPayRespDTO));
            if (aggPayRespDTO.getEstimate() != null && aggPayRespDTO.getEstimate()) {
                throw new BusinessException(aggPayRespDTO.getEstimateInfo());
            }
            SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
            saasPollingDTO.setOrderGuid(billAggPayReq.getOrderGuid());
            saasPollingDTO.setPayGuid(aggPayRespDTO.getPayGuid());
            saasPollingDTO.setEnterpriseGuid(billAggPayReq.getEnterpriseGuid());
            saasPollingDTO.setStoreGuid(billAggPayReq.getStoreGuid());
            long starTime = System.currentTimeMillis();
            String codeUrl = pollingGetCodeUrl(saasPollingDTO, starTime);
            redisService.deleteOrderPaymentInitiateMethod(billAggPayReq.getOrderGuid());
            return codeUrl;
        } catch (Exception e) {
            log.error("获取预接单支付码失败", e);
            redisService.deleteOrderPaymentInitiateMethod(billAggPayReq.getOrderGuid());
        }
        return null;
    }

    private String pollingGetCodeUrl(SaasPollingDTO saasPollingDTO, long starTime) throws InterruptedException {
        AggPayPollingRespDTO query = aggPayClientService.query(saasPollingDTO);
        log.info("聚合支付查询支付结果 query={}", JacksonUtils.writeValueAsString(query));
        String codeUrl = query.getCodeUrl();
        long payTime = 30000;

        // 没得法子，聚合支付就是这么返回的 “轮询接口：看实体中paySt的描述，付款中则需要继续轮询”
        if (com.holderzone.framework.util.StringUtils.isEmpty(codeUrl) && Objects.equals("支付中，请稍等！", query.getMsg())) {
            long endTime = System.currentTimeMillis();
            long now = endTime - starTime;
            log.info("扫码支付轮询 starTime={} endTime={} now={}", starTime, endTime, now);
            if (payTime > now) {
                Thread.sleep(2000);
                codeUrl = pollingGetCodeUrl(saasPollingDTO, starTime);
            }
        }
        log.info("聚合支付查询扫码地址 codeUrl：{}", codeUrl);
        return codeUrl;
    }

    @Override
    public String aggCallBack(SaasNotifyDTO saasNotifyDTO) {
        log.info("收到交易中心回调 saasNotifyDTO={}", JacksonUtils.writeValueAsString(saasNotifyDTO));
        String lockKey = "callback:";
        boolean lockSuccess = false;
        try {
            AggPayPollingRespDTO aggPayPollingRespDTO = saasNotifyDTO.getAggPayPollingRespDTO();
            String payGuid = redisService.getOrderPayGuid(aggPayPollingRespDTO.getOrderGUID());
            //如果是冲正则无条件放行
            if (!saasNotifyDTO.getAggPayPollingRespDTO().getMsg().contains("冲正")) {
                if (StringUtils.isBlank(payGuid)) {
                    return AggCallbackResultConstants.SUCCESS;
                }
                if (!aggPayPollingRespDTO.getPayGUID().equals(payGuid)) {
                    log.info("payGuid不一致，忽略回调");
                    return AggCallbackResultConstants.SUCCESS;
                }
            }
            lockKey = lockKey + aggPayPollingRespDTO.getPayGUID();
            lockSuccess = redisHelper.setNxEx(lockKey, "1", 60);
            if (!lockSuccess) {
                log.info("重复回调");
                return AggCallbackResultConstants.SUCCESS;
            }
            this.runAggCallBack(saasNotifyDTO);
            return AggCallbackResultConstants.SUCCESS;
        } finally {
            if (lockSuccess) {
                redisHelper.delete(lockKey);
            }
        }
    }

    private void runAggCallBack(SaasNotifyDTO saasNotifyDTO) {

        log.info("聚合支付回调入参，saasNotifyDTO：{}", JacksonUtils.writeValueAsString(saasNotifyDTO));
        OrderDO orderDO = orderService.getById(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());

        //当订单不处于支付失败或者支付中则跳过回调
        if (!orderDO.getState().equals(StateEnum.PENDING.getCode()) && !saasNotifyDTO.getAggPayPollingRespDTO().getMsg().contains("冲正")) {
            log.info("订单不处于支付中,忽略回调 {},order:{}", JacksonUtils.writeValueAsString(saasNotifyDTO), JacksonUtils.writeValueAsString(orderDO));
            return;
        }
        //回调状态
        AggCallBackStateEnum aggCallBackStateEnum = AggCallBackStateEnum.transferRsp(
                saasNotifyDTO.getAggPayPollingRespDTO().getCode(),
                saasNotifyDTO.getAggPayPollingRespDTO().getPaySt(),
                saasNotifyDTO.getAggPayPollingRespDTO().getMsg());
        switch (aggCallBackStateEnum) {
            case SUCCESS:
                successHandler(saasNotifyDTO, orderDO);
                break;
            case FAIL:
                failHandler(saasNotifyDTO, orderDO);
                break;
            default:
                break;
        }
    }

    private void failHandler(SaasNotifyDTO saasNotifyDTO, OrderDO orderDO) {
        //待支付，支付失败
        String payGUID = saasNotifyDTO.getAggPayPollingRespDTO().getPayGUID();
        MultipleTransactionRecordDO multipleTransactionRecordDO = multipleTransactionRecordService.getById(payGUID);
        boolean isMultiplePay = !ObjectUtils.isEmpty(multipleTransactionRecordDO);
        if (isMultiplePay) {
            multipleTransactionRecordDO.setState(TradeStateEnum.FAILURE.getCode());
            multipleTransactionRecordService.updateById(multipleTransactionRecordDO);
        }
        TransactionRecordDO transactionRecordDO = getTransactionRecordDO(isMultiplePay, multipleTransactionRecordDO, payGUID);
        // 第一次支付，并且失败则忽略
        if (transactionRecordDO == null) {
            log.warn("交易记录不存在");
            return;
        }
        if (transactionRecordDO.getState() != null && transactionRecordDO.getState() == TradeStateEnum.FAILURE.getCode()) {
            //原来成功或者失败 直接跳过
            log.info("支付失败,重复回调");
            return;
        }
        transactionRecordDO.setState(TradeStateEnum.FAILURE.getCode());
        if (!isMultiplePay) {
            transactionRecordService.updateById(transactionRecordDO);
        }
        if (orderDO.getState() == StateEnum.SUCCESS.getCode() && saasNotifyDTO.getAggPayPollingRespDTO().getPaySt().equals(AggPayStateEnum.FAILURE.getCode())) {
            log.warn("订单已经支付成功,禁止修改为失败，orderId:{},payGuid: {}"
                    , saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID(), payGUID);
            return;
        }
        // 查询订单支付记录
        List<TransactionRecordDO> transactionRecordList = transactionRecordService.listByOrderGuid(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        log.info("聚合支付失败,查询订单支付记录:{}", JacksonUtils.writeValueAsString(transactionRecordList));
        log.warn("聚合支付失败,开始回滚业务,orderGuid:{}", saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        // 快餐聚合支付失败退估清
        DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        log.info("聚合支付失败,查询订单详情:{}", JacksonUtils.writeValueAsString(orderDetail));
        // 支付失败，修改订单
        orderDO.setState(StateEnum.FAILURE.getCode());
        // 结账后的is_handle
        orderDO.setIsHandle(-1);
        orderService.updateById(orderDO);
        // 回滚业务
        if (StringUtils.isNotEmpty(saasNotifyDTO.getAggPayPollingRespDTO().getAttachData())) {
            rollbackCombinePayHandler(orderDO, transactionRecordList, orderDetail);
        }
        // 支付失败 支付记录状态处理
        updateTransactionRecordByFail(transactionRecordList);

        redisService.deleteOrderPaymentInitiateMethod(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());

        redisService.deleteOrderPayGuid(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        updateAdvanceLockKey(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        orderLockService.unlock(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        redisService.deleteAdvancePay(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
    }

    private void updateTransactionRecordByFail(List<TransactionRecordDO> transactionRecordDOS) {
        if (CollectionUtils.isEmpty(transactionRecordDOS)) {
            return;
        }
        transactionRecordDOS.forEach(e -> e.setState(TradeStateEnum.FAILURE.getCode()));
        transactionRecordService.updateBatchById(transactionRecordDOS);
    }

    private void callbackLudouMemberPay(List<TransactionRecordDO> transactionRecordDOS, OrderDO orderDO) {
        TransactionRecordDO ludouTransactionRecord = transactionRecordDOS.stream()
                .filter(e -> PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode() == e.getPaymentType() && StateEnum.SUCCESS.getCode() == e.getState())
                .findFirst()
                .orElse(null);
        if (Objects.isNull(ludouTransactionRecord) || !BigDecimalUtil.greaterThanZero(ludouTransactionRecord.getAmount())) {
            return;
        }
        Boolean successFlag = ludouMemberExternalService.refund(String.valueOf(orderDO.getGuid()),
                ludouTransactionRecord.getBankTransactionId(), orderDO.getActuallyPayFee(),
                ludouTransactionRecord.getAmount(), null);
        if (Boolean.TRUE.equals(successFlag)) {
            log.info("麓豆支付退款成功, orderGuid:{}, 更新支付记录:{}", orderDO.getGuid(), JacksonUtils.writeValueAsString(ludouTransactionRecord));
            // 更新transactionRecordDOS
            ludouTransactionRecord.setState(TradeStateEnum.FAILURE.getCode());
        }
    }

    private void successHandler(SaasNotifyDTO saasNotifyDTO, OrderDO orderDO) {
        String payGUID = saasNotifyDTO.getAggPayPollingRespDTO().getPayGUID();
        MultipleTransactionRecordDO multipleTransactionRecordDO = multipleTransactionRecordService.getById(payGUID);
        boolean isMultiplePay = !ObjectUtils.isEmpty(multipleTransactionRecordDO);
        TransactionRecordDO transactionRecordDO = getTransactionRecordDO(isMultiplePay, multipleTransactionRecordDO, payGUID);
        if (checkRepeatedCallBack(orderDO, multipleTransactionRecordDO, transactionRecordDO)) return;

        //营业日新接口
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        ArrayList<String> storeGuidList = new ArrayList<>();
        storeGuidList.add(UserContextUtils.getStoreGuid());
        reqDTO.setStoreGuidList(storeGuidList);
        reqDTO.setQueryDateTime(LocalDateTime.now());
        LocalDate businessDay = storeClientService.queryBusinessDay(reqDTO);

        transactionRecordDO.setBusinessDay(businessDay);
        transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
        //银行交易流水号为空 设置为orderNO
        String bankTransactionId = StringUtils.isEmpty(saasNotifyDTO.getAggPayPollingRespDTO().getBankTransactionId()) ?
                saasNotifyDTO.getAggPayPollingRespDTO().getOrderNo() : saasNotifyDTO.getAggPayPollingRespDTO().getBankTransactionId();
        transactionRecordDO.setBankTransactionId(bankTransactionId);
        Integer payPowerId = null;
        try {
            payPowerId = Integer.parseInt(saasNotifyDTO.getAggPayPollingRespDTO().getPayPowerId());
        } catch (Exception e) {
            log.error("获取支付方式失败", e);
        }
        transactionRecordDO.setPayPowerId(payPowerId);
        Boolean isLast = saasNotifyDTO.getAggPayPollingRespDTO().getIsLast();
        setOrderState(orderDO, isLast);
        if (isMultiplePay) {
            MultipleTransactionRecordDO recordDO = new MultipleTransactionRecordDO();
            recordDO.setGuid(multipleTransactionRecordDO.getGuid());
            recordDO.setBankTransactionId(bankTransactionId);
            recordDO.setBusinessDay(businessDay);
            recordDO.setState(TradeStateEnum.SUCCESS.getCode());
            recordDO.setPayPowerId(payPowerId);
            multipleTransactionRecordService.updateById(recordDO);
            BigDecimal amount = transactionRecordDO.getAmount().add(saasNotifyDTO.getAggPayPollingRespDTO().getAmount());
            transactionRecordDO.setAmount(amount);
            transactionRecordDO.setRefundableFee(amount);
            transactionRecordDO.setIsMultipleAggPay(Boolean.TRUE);

            // 记录订单多次支付状态
            recordMultiplePayState(orderDO);
        }
        // 并桌转移多次支付记录
        combineTransferRecord(orderDO, transactionRecordDO);

        LocalizeSynchronizeContext.putOrderGuid(String.valueOf(orderDO.getGuid()));
        orderDO.setBusinessDay(businessDay);
        orderDO.setCheckoutStaffGuid(UserContextUtils.getUserGuid());
        orderDO.setCheckoutStaffName(UserContextUtils.getUserName());
        orderDO.setCheckoutTime(LocalDateTime.now());
        if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode()) && Boolean.TRUE.equals(isLast)) {
            checkoutSubOrder(orderDO);
        }
        AggPayAttachDataBO aggPayAttachDataBO = orderTransform.baseInfo2AggPayAttachDataBO(saasNotifyDTO.getBaseInfo());
        aggPayAttachDataBO.setLast(isLast);
        aggPayAttachDataBO.setCheckoutSuccessFlag(saasNotifyDTO.getAggPayPollingRespDTO().getCheckoutSuccessFlag());
        aggPayAttachDataBO.setPayGuid(payGUID);
        aggPayAttachDataBO.setOrderGuid(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        aggPayAttachDataBO.setAmount(saasNotifyDTO.getAggPayPollingRespDTO().getAmount());
        if (isMultiplePay && Boolean.FALSE.equals(aggPayAttachDataBO.getLast())
                && !Boolean.TRUE.equals(aggPayAttachDataBO.getCheckoutSuccessFlag())) {
            orderService.updateById(orderDO);
            afterHandle(saasNotifyDTO);
            return;
        }
        if (Boolean.TRUE.equals(aggPayAttachDataBO.getLast())) {
            lastAggPay(saasNotifyDTO, aggPayAttachDataBO, orderDO);
        } else {
            //只有是組合支付，然後在pay
            //聚合支付成功后调用其他支付
            otherAggPay(saasNotifyDTO);
        }
        afterHandle(saasNotifyDTO);
    }

    private void lastAggPay(SaasNotifyDTO saasNotifyDTO, AggPayAttachDataBO aggPayAttachDataBO, OrderDO orderDO) {
        //结账后的is_handle/
        orderDO.setIsHandle(-1);

        // 聚合支付回调设置超额
        BigDecimal excessAmount = calculateExcessAmount(orderDO);
        orderDO.setExcessAmount(excessAmount);

        // 多笔结账
        orderCloseHandler(aggPayAttachDataBO.getCloseTableFlag(), orderDO);

        // 支付成功之后保存使用的第三方活动信息
        saveThirdActivityDetails(orderDO);

        orderService.updateById(orderDO);

        BillPayReqDTO billPayReqDTO = buildAggBillPayReqDTO(saasNotifyDTO);
        // 订单结账不清台
        if (Objects.equals(BooleanEnum.TRUE.getCode(), billPayReqDTO.getCloseTableFlag())) {
            // 创建订单
            orderTableBillService.dealEnableHandleClose(billPayReqDTO);
        } else {
            DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetails(aggPayAttachDataBO.getOrderGuid());
            //判断是否需要打印二维码
            queryInvoiceCache(aggPayAttachDataBO);
            // 打印结账单
            asyncTradeService.asyncCheckOutCall(aggPayAttachDataBO, orderDO, orderDetail);
            // 支付成功后通知到会员中心
            if (Objects.equals(RecoveryTypeEnum.GENERAL.getCode(), orderDO.getRecoveryType())) {
                DiscountDO followRedPacketDiscount = discountService.getFollowRedPacketDiscount(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
                notifyRedPacketMember(orderDO, followRedPacketDiscount);
            }
            // 自动出餐：支付成功后若备餐时间大于0将备餐信息入延迟队列
            autoDistribute(saasNotifyDTO.getBaseInfo().getDeviceType(), saasNotifyDTO.getBaseInfo().getDeviceId(), orderDO);
        }
    }

    private void otherAggPay(SaasNotifyDTO saasNotifyDTO) {
        BillPayInfoDTO billPayInfo = redisService.getBillPayInfo(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        if (Objects.isNull(billPayInfo)) {
            return;
        }
        UserContext userContext = billPayInfo.getUserContext();
        UserContextUtils.put(userContext);
        BillPayReqDTO billPayReqDTO = billPayInfo.getBillPayReqDTO();
        log.info("聚合支付回调其他支付，redis取参:{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        billPayReqDTO.getPayments().removeIf(p -> PaymentTypeEnum.AGG.getCode() == p.getPaymentType());
        billPayReqDTO.setCallbackPayFlag(true);
        this.pay(billPayReqDTO);
        redisService.deleteBillPayInfo(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
    }

    private BillPayReqDTO buildAggBillPayReqDTO(SaasNotifyDTO saasNotifyDTO) {
        BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        AggPayPollingRespDTO aggPayPollingRespDTO = saasNotifyDTO.getAggPayPollingRespDTO();
        BeanUtils.copyProperties(aggPayPollingRespDTO, billPayReqDTO);
        billPayReqDTO.setOrderGuid(aggPayPollingRespDTO.getOrderGUID());
        String attachData = saasNotifyDTO.getAggPayPollingRespDTO().getAttachData();
        if (StringUtils.isNotEmpty(attachData)) {
            BaseDTO baseDTO = JacksonUtils.toObject(BaseDTO.class, attachData);
            BeanUtils.copyProperties(baseDTO, billPayReqDTO);
        }
        return billPayReqDTO;
    }

    private void afterHandle(SaasNotifyDTO saasNotifyDTO) {
        BigDecimal divide = saasNotifyDTO.getAggPayPollingRespDTO().getAmount();
        String nameById = PayPowerId.getNameById(saasNotifyDTO.getAggPayPollingRespDTO().getPayPowerId());
        BusinessMessageDTO build = BusinessMessageDTO.builder()
                .storeGuid(saasNotifyDTO.getBaseInfo().getStoreGuid())
                .storeName(saasNotifyDTO.getBaseInfo().getStoreName())
                .messageType(1)
                .detailMessageType(12)
                .platform("2")
                .subject("收款成功，" + nameById + "到账" + divide.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString() + "元")
                .content(saasNotifyDTO.getBaseInfo().getDeviceId())
                .build();
        log.info("通知消息 build:{}", JacksonUtils.writeValueAsString(build));
        messageClientService.notifyMsg(build);
        redisService.deleteOrderPaymentInitiateMethod(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        redisService.deleteOrderPayGuid(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        //支付成功删除主扫二维码支付信息
        redisService.deletePadPayInfo(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        orderLockService.unlock(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
        redisService.deleteAdvancePay(saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID());
    }

    private void setOrderState(OrderDO orderDO,
                               Boolean isLast) {
        if (Boolean.TRUE.equals(isLast)) {
            orderDO.setState(StateEnum.SUCCESS.getCode());
        } else {
            orderDO.setState(StateEnum.READY.getCode());
        }
    }

    private void combineTransferRecord(OrderDO orderDO,
                                       TransactionRecordDO transactionRecordDO) {
        if (UpperStateEnum.MAIN.getCode() == orderDO.getUpperState()) {
            List<OrderDO> subOrderList = orderService.listByMainOrderGuid(orderDO.getGuid());
            List<Long> subOrderGuidList = subOrderList.stream()
                    .map(OrderDO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<MultipleTransactionRecordDO> subMultipleRecordDOList =
                    multipleTransactionRecordService.listByOrderGuidList(subOrderGuidList).stream()
                            .filter(t -> BigDecimalUtil.greaterThanZero(t.getAmount().subtract(t.getRefundAmount())))
                            .collect(Collectors.toList());
            List<Long> subRecordDOGuidList = transactionRecordService.listJhByOrderGuidList(subOrderGuidList)
                    .stream()
                    .filter(t -> BigDecimalUtil.greaterThanZero(t.getAmount().subtract(t.getRefundAmount())))
                    .map(TransactionRecordDO::getGuid)
                    .collect(Collectors.toList());
            if (!org.springframework.util.CollectionUtils.isEmpty(subMultipleRecordDOList)) {
                subMultipleRecordDOList.forEach(multipleRecordDO -> {
                    multipleRecordDO.setAmount(multipleRecordDO.getAmount().subtract(multipleRecordDO.getRefundAmount()));
                });
                if (!org.springframework.util.CollectionUtils.isEmpty(subMultipleRecordDOList)) {
                    BigDecimal subAmountTotal = subMultipleRecordDOList.stream()
                            .map(MultipleTransactionRecordDO::getAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    List<MultipleTransactionRecordDO> updateRecordDOList = subMultipleRecordDOList.stream().map(t -> {
                        MultipleTransactionRecordDO recordDO = new MultipleTransactionRecordDO();
                        recordDO.setGuid(t.getGuid());
                        recordDO.setTransactionRecordGuid(transactionRecordDO.getGuid());
                        recordDO.setOrderGuid(transactionRecordDO.getOrderGuid());
                        recordDO.setOriginalOrderGuid(t.getOrderGuid());
                        return recordDO;
                    }).collect(Collectors.toList());
                    transactionRecordDO.setAmount(transactionRecordDO.getAmount().add(subAmountTotal));
                    transactionRecordDO.setRefundableFee(transactionRecordDO.getRefundableFee().add(subAmountTotal));
                    multipleTransactionRecordService.updateBatchById(updateRecordDOList);
                    transactionRecordService.removeByIds(subRecordDOGuidList);

                    // 更改子桌多次支付状态
                    List<OrderExtendsDO> orderExtendsDOList = orderExtendsService.listByOrderGuidList(subOrderGuidList);
                    if (!org.springframework.util.CollectionUtils.isEmpty(orderExtendsDOList)) {
                        List<OrderExtendsDO> updateExtendsDOList = orderExtendsDOList.stream().map(orderExtendsDO -> {
                            OrderExtendsDO oldOrderExtendsDO = new OrderExtendsDO();
                            oldOrderExtendsDO.setGuid(orderExtendsDO.getGuid());
                            oldOrderExtendsDO.setIsMultipleAggPay(Boolean.FALSE);
                            return oldOrderExtendsDO;
                        }).collect(Collectors.toList());
                        orderExtendsService.updateBatchById(updateExtendsDOList);
                    }
                }
            }
        }
        transactionRecordService.updateById(transactionRecordDO);
    }

    private TransactionRecordDO getTransactionRecordDO(boolean isMultiplePay,
                                                       MultipleTransactionRecordDO multipleTransactionRecordDO,
                                                       String payGUID) {
        TransactionRecordDO transactionRecordDO;
        if (isMultiplePay) {
            log.info("聚合支付多笔支付, multipleTransactionRecordDO:{}", JacksonUtils.writeValueAsString(multipleTransactionRecordDO));
            transactionRecordDO = transactionRecordService.getById(multipleTransactionRecordDO.getTransactionRecordGuid());
        } else {
            transactionRecordDO = transactionRecordService.getById(payGUID);
        }
        return transactionRecordDO;
    }

    private void recordMultiplePayState(OrderDO orderDO) {
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderDO.getGuid());
        if (ObjectUtils.isEmpty(orderExtendsDO)) {
            OrderExtendsDO newOrderExtendsDO = new OrderExtendsDO();
            newOrderExtendsDO.setGuid(orderDO.getGuid());
            newOrderExtendsDO.setStoreGuid(orderDO.getStoreGuid());
            newOrderExtendsDO.setBusinessDay(orderDO.getBusinessDay());
            newOrderExtendsDO.setIsMultipleAggPay(Boolean.TRUE);
            orderExtendsService.save(newOrderExtendsDO);
        } else {
            OrderExtendsDO oldOrderExtendsDO = new OrderExtendsDO();
            oldOrderExtendsDO.setGuid(orderDO.getGuid());
            oldOrderExtendsDO.setIsMultipleAggPay(Boolean.TRUE);
            orderExtendsService.updateById(oldOrderExtendsDO);
        }
    }

    private static boolean checkRepeatedCallBack(OrderDO orderDO,
                                                 MultipleTransactionRecordDO multipleTransactionRecordDO,
                                                 TransactionRecordDO transactionRecordDO) {
        if (ObjectUtils.isEmpty(multipleTransactionRecordDO)) {
            if (transactionRecordDO.getState() != null && transactionRecordDO.getState() == TradeStateEnum.SUCCESS.getCode()
                    && orderDO.getState().equals(StateEnum.SUCCESS.getCode())) {
                //如果已经成功了  直接跳过处理
                log.info("支付成功,重复回调");
                return true;
            }
        } else {
            if (multipleTransactionRecordDO.getState() != null && multipleTransactionRecordDO.getState() == TradeStateEnum.SUCCESS.getCode()
                    && orderDO.getState().equals(StateEnum.SUCCESS.getCode())) {
                //如果已经成功了  直接跳过处理
                log.info("该次支付成功,重复回调");
                return true;
            }
        }
        return false;
    }

    private void queryInvoiceCache(AggPayAttachDataBO aggPayAttachDataBO) {
        String invoiceJson = redisHelper.get(INVOICE_CODE_PRINT_REDIS_KEY + aggPayAttachDataBO.getOrderGuid());
        log.info("缓存查询开票二维码信息：{}", invoiceJson);
        if (StringUtils.isNotEmpty(invoiceJson)) {
            redisHelper.delete(INVOICE_CODE_PRINT_REDIS_KEY + aggPayAttachDataBO.getOrderGuid());
            RequestGenerateInvoiceDTO request = JSON.parseObject(invoiceJson, RequestGenerateInvoiceDTO.class);
            aggPayAttachDataBO.setInvoicePhone(request.getAccount());
            aggPayAttachDataBO.setOrderGuid(request.getOrderNo());
            aggPayAttachDataBO.setAccountName(request.getAccountName());
            aggPayAttachDataBO.setStoreGuid(request.getStoreGuid());
            aggPayAttachDataBO.setIsInvoiceCode(BooleanEnum.TRUE.getCode());
        }

        String handleCloseFlag = redisHelper.get(HANDLE_CLOSE_FLAG_REDIS_KEY + aggPayAttachDataBO.getOrderGuid());
        log.info("缓存查询是否走手动清台逻辑：{}", handleCloseFlag);
        if (StringUtils.isNotEmpty(handleCloseFlag)) {
            aggPayAttachDataBO.setCloseTableFlag(BooleanEnum.TRUE.getCode());
        }
    }

    void updateAdvanceLockKey(String orderGuid) {
        //支付失败更新预支付锁
        String advanceLockKey = String.format("ADVANCE_PAY:%s", orderGuid);
        String redisRetJson = redisHelper.get(advanceLockKey);
        if (com.holderzone.framework.util.StringUtils.hasText(redisRetJson)) {
            BillAggPayRespDTO redisRet = JacksonUtils.toObject(BillAggPayRespDTO.class, redisRetJson);
            redisRet.setResult(Boolean.FALSE);
            redisHelper.setEx(advanceLockKey, JacksonUtils.writeValueAsString(redisRet), 24, TimeUnit.HOURS);
        }
    }

    /**
     * 计算超出金额
     *
     * @param orderDO 订单实体
     * @return 超出金额
     */
    private BigDecimal calculateExcessAmount(OrderDO orderDO) {
        BigDecimal excessAmount = BigDecimal.ZERO;
        List<DiscountDO> discountDOS = discountService.listByOrderGuid(String.valueOf(orderDO.getGuid()));
        Map<Integer, DiscountDO> discountTypeMap = CollectionUtil.toMap(discountDOS, "discountType");
        DiscountDO thirdDiscount = discountTypeMap.get(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
        if (ObjectUtils.isEmpty(thirdDiscount)) {
            return excessAmount;
        }
        if (BigDecimalUtil.greaterThanZero(thirdDiscount.getDiscountFee())) {
            DiscountDO freeDiscount = discountTypeMap.get(DiscountTypeEnum.FREE.getCode());
            BigDecimal freeDiscountDiscountFee = BigDecimal.ZERO;
            if (!ObjectUtils.isEmpty(freeDiscount)) {
                freeDiscountDiscountFee = freeDiscount.getDiscountFee();
            }
            BigDecimal subtract = thirdDiscount.getDiscountFee().subtract(orderDO.getOrderFee()
                    .subtract(freeDiscountDiscountFee));
            if (BigDecimalUtil.greaterThanZero(subtract)) {
                excessAmount = subtract;
            }
        }
        return excessAmount;
    }

    /**
     * 结账子单
     *
     * @param mainOrderDO 主单实体
     */
    private void checkoutSubOrder(OrderDO mainOrderDO) {
        List<OrderDO> orderDOS = orderService.listByMainOrderGuid(String.valueOf(mainOrderDO.getGuid()));
        for (OrderDO orderDO : orderDOS) {
            LocalizeSynchronizeContext.putOrderGuid(String.valueOf(orderDO.getGuid()));
            orderDO.setState(StateEnum.SUCCESS.getCode());
            orderDO.setCheckoutStaffGuid(UserContextUtils.getUserGuid());
            orderDO.setCheckoutStaffName(UserContextUtils.getUserName());
            orderDO.setCheckoutTime(LocalDateTime.now());
            //结账后的is_handle
            orderDO.setIsHandle(-1);
        }
        orderService.updateBatchByIdWithDeleteCache(orderDOS);
    }

    @Override
    public Page<MemberConsumeRespDTO> memberConsumeRecords(MemberConsumeReqDTO memberConsumeReqDTO) {
        LambdaQueryWrapper<OrderDO> orderDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //时间区间
        if (memberConsumeReqDTO.getTimeType() == 0) {
            orderDOLambdaQueryWrapper.ge(memberConsumeReqDTO.getBeginTime() != null, OrderDO::getBusinessDay,
                    memberConsumeReqDTO.getBeginTime()).le(memberConsumeReqDTO.getEndTime() != null,
                    OrderDO::getBusinessDay, memberConsumeReqDTO.getEndTime());
            orderDOLambdaQueryWrapper.orderByDesc(OrderDO::getBusinessDay);
        }
        if (memberConsumeReqDTO.getTimeType() == 1) {
            orderDOLambdaQueryWrapper.ge(memberConsumeReqDTO.getBeginTime() != null, OrderDO::getCheckoutTime,
                    memberConsumeReqDTO.getBeginTime()).le(memberConsumeReqDTO.getEndTime() != null,
                    OrderDO::getCheckoutTime, memberConsumeReqDTO.getEndTime());
            orderDOLambdaQueryWrapper.orderByDesc(OrderDO::getCheckoutTime);
        }

        orderDOLambdaQueryWrapper.in(OrderDO::getState, Lists.newArrayList(StateEnum.SUCCESS.getCode(), StateEnum
                .REFUNDED.getCode()));
        //门店
        if (CollectionUtil.isNotEmpty(memberConsumeReqDTO.getStoreGuids())) {
            orderDOLambdaQueryWrapper.in(OrderDO::getStoreGuid, memberConsumeReqDTO.getStoreGuids());
        }

        //用餐类型
        orderDOLambdaQueryWrapper.eq(OrderDO::getTradeMode, memberConsumeReqDTO.getTradeMode());
        orderDOLambdaQueryWrapper.like(StringUtils.isNotEmpty(memberConsumeReqDTO.getOrderGuid()), OrderDO::getOrderNo,
                memberConsumeReqDTO.getOrderGuid());
        orderDOLambdaQueryWrapper.eq(OrderDO::getMemberGuid, memberConsumeReqDTO.getMemberGuid());

        IPage<OrderDO> page = orderService.page(new PageAdapter<>(memberConsumeReqDTO), orderDOLambdaQueryWrapper);
        List<OrderDO> records = page.getRecords();
        List<MemberConsumeRespDTO> memberConsumeRespDTOS = new ArrayList<>();
        for (OrderDO record : records) {
            MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
            memberConsumeRespDTOS.add(memberConsumeRespDTO);
            if (record.getRecoveryType().equals(RecoveryTypeEnum.RETURN.getCode())) {
                memberConsumeRespDTO.setActuallyPayFee(BigDecimalUtil.negative(record.getActuallyPayFee()));
            } else {
                memberConsumeRespDTO.setActuallyPayFee(record.getActuallyPayFee());
            }

            memberConsumeRespDTO.setCheckoutedTimestamp(record.getCheckoutTime());
            memberConsumeRespDTO.setCheckoutStaffName(record.getCheckoutStaffName());
            memberConsumeRespDTO.setOrderGuid(String.valueOf(record.getOrderNo()));
            memberConsumeRespDTO.setShouldPayFee(record.getOrderFee());
            memberConsumeRespDTO.setStoreName(record.getStoreName());
            memberConsumeRespDTO.setTotalDiscountFee(record.getOrderFee().subtract(record.getActuallyPayFee()));
            memberConsumeRespDTO.setTradeMode(record.getTradeMode());
        }


        return new PageAdapter<>(page, memberConsumeRespDTOS);
    }

    /**
     * 会员余额支付和食堂卡支付成功消息
     */
    private void notifyCardPayMsg(BillPayReqDTO billPayReqDTO, OrderDO orderDO) {
        List<BillPayReqDTO.Payment> payments = billPayReqDTO.getPayments();
        List<Integer> canteenPayments = Lists.newArrayList(PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode(),
                PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode());
        for (BillPayReqDTO.Payment payment : payments) {
            notifyMemberPayMsg(orderDO, canteenPayments, billPayReqDTO, payment);
        }
    }

    private void notifyMemberPayMsg(OrderDO orderDO, List<Integer> canteenPayments,
                                    BillPayReqDTO billPayReqDTO, BillPayReqDTO.Payment payment) {
        String cardName = "";
        boolean isCanteenPay = canteenPayments.contains(payment.getPaymentType());
        if (isCanteenPay) {
            // 食堂卡支付
            cardName = "食堂卡";
        }
        if (CommonUtil.hasGuid(orderDO.getMemberGuid()) && payment.getPaymentType().equals(PaymentTypeEnum.MEMBER.getCode())) {
            // 会员余额支付
            RequestQueryStoreAndMemberAndCard memberLoginDTO = new RequestQueryStoreAndMemberAndCard();
            memberLoginDTO.setPhoneNumOrCardNum(orderDO.getMemberPhone());
            memberLoginDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            memberLoginDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            ResponseMemberAndCardInfoDTO memberInfoAndCard = memberTerminalClientService.getMemberInfoAndCard(memberLoginDTO);
            if (memberInfoAndCard != null && CollectionUtils.isNotEmpty(memberInfoAndCard.getMemberCardListRespDTOs())) {
                ResponseMemberCard cardByGuid = memberInfoAndCard.getMemberCardListRespDTOs().stream()
                        .filter(e -> e.getMemberInfoCardGuid().equals(orderDO.getMemberCardGuid()))
                        .findFirst().orElse(null);
                if (cardByGuid != null) {
                    cardName = cardByGuid.getCardName();
                }
            }
        }
        if (StringUtils.isNotEmpty(cardName)) {
            BusinessMessageDTO build = BusinessMessageDTO.builder()
                    .storeGuid(billPayReqDTO.getStoreGuid())
                    .storeName(billPayReqDTO.getStoreName())
                    .messageType(1)
                    .detailMessageType(BusinessMsgTypeEnum.BMT_DETAIL_MEMBER_CHECK.getId())
                    .platform("2")
                    .subject(cardName + "成功支付" + payment.getAmount().setScale(2, RoundingMode.HALF_UP)
                            .stripTrailingZeros().toPlainString() + "元")
                    .content(billPayReqDTO.getDeviceId())
                    .build();
            log.info("通知消息 build:{}", JacksonUtils.writeValueAsString(build));
            messageClientService.notifyMsg(build);
        }
    }

    private void checkRecoveryTimeLimit(OrderDO orderDO) {
        String storeGuid = orderDO.getStoreGuid();
        if (StringUtils.isBlank(storeGuid)) {
            return;
        }
        BrandConfigDTO brandConfigDTO = businessClientService.getBrandConfigByStoreGuid(storeGuid);
        log.info("品牌配置返回参数: {}", JacksonUtils.writeValueAsString(brandConfigDTO));
        if (Objects.isNull(brandConfigDTO)) {
            return;
        }
        Integer recoveryTimeLimitUnit = brandConfigDTO.getRecoveryTimeLimitUnit();
        Integer recoveryTimeLimit = brandConfigDTO.getRecoveryTimeLimit();
        if (Objects.isNull(recoveryTimeLimit)
            || Objects.isNull(recoveryTimeLimitUnit)) {
            return;
        }
        if (DateUtil.checkRefundTimeLimit(orderDO.getCheckoutTime(), recoveryTimeLimit, recoveryTimeLimitUnit)) {
            throw new BusinessException("超时不可操作");
        }
    }
}
