package com.holderzone.saas.store.trade.async.print;

import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalDetailsDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.StoreBizDTO;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.function.Function;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/10 10:41
 * 数据容器，乱七八糟打印数据全放这里,  对象原则上不可改
 */
@Data
@Builder
@Accessors(chain = true)
public class TradeDataContainer {

    private BaseDTO baseDTO;

    private DineinOrderDetailRespDTO dineinOrderDetailRespDTO;

    private DineinOrderDetailRespDTO oldDineinOrderDetailRespDTO;

    private Function<String,String> areaGuidFromTableGuidFunction;

    private Function<String, StoreBizDTO> storeInfoFunction;

    /**
     * {@link PrintOrderItemDTO.ItemInvoiceTypeEnum}
     */
    private Integer itemInvoiceType;

}