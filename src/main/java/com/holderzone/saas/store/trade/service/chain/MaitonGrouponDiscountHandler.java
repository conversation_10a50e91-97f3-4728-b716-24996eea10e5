package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountRuleDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.GrouponCalculateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 美团一键买单优惠
 */
@Slf4j
@Component
@AllArgsConstructor
public class MaitonGrouponDiscountHandler extends DiscountHandler {

    /**
     * 美团一键买单
     */
    @Override
    void dealDiscount(DiscountContext context) {
        DineinOrderDetailRespDTO orderDetailRespDTO = context.getOrderDetailRespDTO();
        Map<Integer, DiscountDO> discountTypeMap = context.getDiscountTypeMap();
        List<DineInItemDTO> allItems = context.getAllItems();
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = context.getDiscountFeeDetailDTOS();
        // 处理优惠
        DiscountFeeDetailDTO grouponDiscount = discountFeeDetailDTOS.stream()
                .filter(e -> type().equals(e.getDiscountType()))
                .findFirst()
                .orElse(null);
        boolean isNotExist = ObjectUtils.isEmpty(grouponDiscount);
        if (isNotExist) {
            DiscountDO grouponDiscountDO = discountTypeMap.get(type());
            grouponDiscount = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(grouponDiscountDO);
            grouponDiscount.setDiscountFee(BigDecimal.ZERO);
            grouponDiscount.setRule(JacksonUtils.writeValueAsString(new DiscountRuleDTO()));
            discountFeeDetailDTOS.add(grouponDiscount);
        }
        Pair<BigDecimal, BigDecimal> grouponFeePair = GrouponCalculateUtil.calculateMaitonGrouponFee(orderDetailRespDTO, allItems);
        log.warn("[{}]优惠金额：{}", DiscountTypeEnum.MAITON_GROUPON.getDesc(), JacksonUtils.writeValueAsString(grouponFeePair));
        BigDecimal grouponFee = grouponFeePair.getKey();
        if (!BigDecimalUtil.greaterThanZero(grouponFee)) {
            return;
        }
        log.info("[{}]开始计算", DiscountTypeEnum.MAITON_GROUPON.getDesc());
        // 附加费
        BigDecimal appendFee = Optional.ofNullable(orderDetailRespDTO.getAppendFee()).orElse(BigDecimal.ZERO);
        if (BigDecimalUtil.greaterThanZero(orderDetailRespDTO.getAppendDiscountFee())) {
            appendFee = appendFee.subtract(orderDetailRespDTO.getAppendDiscountFee());
        }
        // 当前订单可抵扣的金额
        BigDecimal actualGrouponFee = AmountCalculationUtil.dealWithDiscountFee(orderDetailRespDTO.getOrderSurplusFee().add(appendFee), grouponFee);
        // 如果团购非验券加购，则将团购的优惠金额分摊
        GrouponCalculateUtil.calculateItemGrouponDiscountTotalPrice(orderDetailRespDTO, grouponFeePair, allItems, actualGrouponFee);

        grouponDiscount.setDiscountFee(grouponDiscount.getDiscountFee().add(actualGrouponFee));
        DiscountRuleDTO discountRuleDTO = JacksonUtils.toObject(DiscountRuleDTO.class, grouponDiscount.getRule());
        discountRuleDTO.setCouponBuyTotalPrice(Optional.ofNullable(discountRuleDTO.getCouponBuyTotalPrice()).orElse(BigDecimal.ZERO)
                .add(grouponFeePair.getRight()));
        grouponDiscount.setRule(JacksonUtils.writeValueAsString(discountRuleDTO));
        if (BigDecimalUtil.greaterThanZero(actualGrouponFee)) {
            orderDetailRespDTO.setOrderSurplusFee(orderDetailRespDTO.getOrderSurplusFee().subtract(actualGrouponFee));
            orderDetailRespDTO.setGrouponFee(orderDetailRespDTO.getGrouponFee().add(actualGrouponFee));
            log.warn("{}：{}，订单剩余金额：{}", DiscountTypeEnum.MAITON_GROUPON.getDesc(), actualGrouponFee,
                    orderDetailRespDTO.getOrderSurplusFee());
        }
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.MAITON_GROUPON.getCode();
    }
}
