package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.trade.entity.domain.MultipleTransactionRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单多次支付交易记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface MultipleTransactionRecordMapper extends BaseMapper<MultipleTransactionRecordDO> {

    void addBatchRefundAmount(@Param("list") List<MultipleTransactionRecordDO> list);
}
