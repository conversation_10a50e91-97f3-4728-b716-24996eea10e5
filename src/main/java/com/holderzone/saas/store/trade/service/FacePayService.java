package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.face.FacePayCompensateReqDTO;
import com.holderzone.saas.store.dto.order.request.face.FacePayEstimateReqDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-trade
 */
public interface FacePayService {


    Boolean recoveryCompensate(FacePayCompensateReqDTO facePayCompensateReqDTO);

    /**
     * 人脸支付先校验估清
     *
     * @param facePayEstimateReqDTO
     * @return
     */
    EstimateItemRespDTO estimate(FacePayEstimateReqDTO facePayEstimateReqDTO);

    /**
     * 人脸支付失败退估清并且退积分
     * @param singleDataDTO
     * @return
     */
    Boolean returnEstimate(SingleDataDTO singleDataDTO);
}
