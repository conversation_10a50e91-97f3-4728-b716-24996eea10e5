package com.holderzone.saas.store.trade.utils.caculate;

import com.holderzone.saas.store.dto.order.common.*;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;
import com.holderzone.saas.store.trade.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemUtils
 * @date 2019/12/11 16:57
 * @description //TODO
 * @program IdeaProjects
 */
public class ItemUtils {

    public static OrderTransform orderTransform = OrderTransform.INSTANCE;

    public static Function<Long, String> consumer = String::valueOf;

    /**
     * 装配DineinItemDTO
     */
    protected static void assemblingFreeItemFunction(DineInItemDTO dineInItemDTO, List<FreeItemDTO> argsfreeItems) {
        Function<List<FreeItemDTO>, List<FreeItemDTO>> function = freeItemDTOS -> Optional.ofNullable(freeItemDTOS)
                .map(freeItems -> freeItems.stream()
                        .peek(freeItemDTO -> freeItemDTO.setItemPrice(getItemPrice(dineInItemDTO, freeItemDTO.getCount(), true)))
                        .collect(Collectors.toList())
                )
                .orElse(null);
        dineInItemDTO.setFreeItemDTOS(function.apply(argsfreeItems));
    }

    /**
     * 组装套餐 和算分组里面的附加费
     */
    protected static void assemblingGroupInfo(DineInItemDTO dineInItemDTO, List<OrderItemDO> orderItemDOS, Map<String, List<ItemAttrDTO>> guidToAttrDTOS) {
        if (!dineInItemDTO.getItemType().equals(ItemTypeEnum.GROUP.getCode())) {
            return;
        }

        //每一分组转化为子组   List<OrderItemDO> --> List<SubDineInItemDTO>  为了组装
        Function<List<OrderItemDO>, List<SubDineInItemDTO>> itemToGroupItemFunction = itemDos -> itemDos.stream()
                //转化为  SubDineInItemDTO
                .map(orderTransform::orderItemDO2SubDineInItemDTO)
                //装配属性DTO
                .peek(subDineInItemDTO -> subDineInItemDTO.setItemAttrDTOS(guidToAttrDTOS.get(subDineInItemDTO.getGuid())))
                //装配属性总价
                .peek(subDineInItemDTO -> subDineInItemDTO.setSingleItemAttrTotal(getSingleAttrTotal(subDineInItemDTO.getGuid(), guidToAttrDTOS)))
                .collect(Collectors.toList());

        //分组获取分组的function   集合转对象
        Function<List<OrderItemDO>, PackageSubgroupDTO> itemsToSubGroupPacket = itemDos -> itemDos.stream()
                //名称和GUID  为什么不直接转化为NEW？  防止减少new对象成本
                .map(itemDo -> Pair.of(itemDo.getSubgroupGuid(), itemDo.getSubgroupName()))
                //一直用来的 dto
                .reduce((o, n) -> o)
                //获取分组名称，按道理来说同一组只有一个名称
                .map(pair -> new PackageSubgroupDTO(pair.getFirst(), pair.getSecond()))
                //组装 SubDineInItemDTO
                .map(packageSubgroupDTO -> packageSubgroupDTO.setSubDineInItemDTOS(itemToGroupItemFunction.apply(itemDos)))
                //如果为空，直接返回null
                .orElse(null);

        // suborderGroupGuid  ->  orderItemDo
        List<PackageSubgroupDTO> packageSubgroupDTOS = orderItemDOS.stream()
                // 先转化为map  取其值
                .collect(Collectors.groupingBy(OrderItemDO::getSubgroupGuid)).values().stream()
                //List<OrderItemDO> ->  PackageSubgroupDTO
                .map(itemsToSubGroupPacket)
                //组装
                .collect(Collectors.toList());

        //单个套餐内附加费合计
        BigDecimal singleAddPrice = orderItemDOS.stream()
                //子项加价 （菜品赠送数量 + 菜品实际数量） *  套餐内子项的数量 * 套餐内子项的实际价格
                .map(orderItemDO -> orderItemDO.getCurrentCount().multiply(orderItemDO.getAddPrice()))
                //累加结果
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        dineInItemDTO.setSingleAddPrice(singleAddPrice);
        dineInItemDTO.setPackageSubgroupDTOS(packageSubgroupDTOS);
    }

    /**
     * 秋ItemPrice
     */
    protected static BigDecimal getItemPrice(DineInItemDTO dineInItemDTO, BigDecimal
            currentCount, boolean isFree) {
        //赠送  直接原价  A
        BigDecimal currentPrice = isFree ? dineInItemDTO.getOriginalPrice() : dineInItemDTO.getPrice();
        BigDecimal itemPrice = dineInItemDTO.getItemType().equals(ItemTypeEnum.GROUP.getCode()) ?
                //【套餐】 ---> （价格 + 属性价格 + 单个子项加价） * count
                dineInItemDTO.getPrice().add(dineInItemDTO.getSingleItemAttrTotal()).add(dineInItemDTO.getSingleAddPrice()).multiply(currentCount) :
                    dineInItemDTO.getItemType().equals(ItemTypeEnum.WEIGH.getCode()) ?
                            // 【称重】 ---> 价格 * count + 属性价格
                            currentPrice.multiply(currentCount).add(dineInItemDTO.getSingleItemAttrTotal()) :
                            // 【普通】 ---> （价格 + 属性价格） * count
                            dineInItemDTO.getSingleItemAttrTotal().add(currentPrice).multiply(currentCount);
        return itemPrice;
    }

    /**
     * 获取属性总价
     */
    protected static BigDecimal getItemAttrTotal(DineInItemDTO dineInItemDTO, Map<String,
            List<OrderItemDO>> parentGuidToChildGuid, Map<String, List<ItemAttrDTO>> guidToAttrDTOS) {
        if (dineInItemDTO.getItemType().equals(ItemTypeEnum.GROUP.getCode())) {
            return getGroupAttrTotal(dineInItemDTO.getGuid(), parentGuidToChildGuid, guidToAttrDTOS);
        }
        return getSingleAttrTotal(dineInItemDTO.getGuid(), guidToAttrDTOS);
    }

    /**
     * 获取单个dineInItemDTO 的属性总价,单个商品
     */
    protected static BigDecimal getSingleAttrTotal(String itemGuid, Map<String, List<ItemAttrDTO>> guidToAttrDTOS) {
        return guidToAttrDTOS.get(itemGuid).stream().map(ItemAttrDTO::getAttrPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取单个dineInItemDTO 的属性总价,分组商品
     */
    protected static BigDecimal getGroupAttrTotal(String itemGuid, Map<String,
            List<OrderItemDO>> parentGuidToChildGuid, Map<String, List<ItemAttrDTO>> guidToAttrDTOS) {
        return parentGuidToChildGuid.get(itemGuid).stream()
                // OrderItemDO -> Stream<Pari<ItemAttrDTO,OrderItemDO>>
                .flatMap(childItemDo -> Optional.ofNullable(
                        //OrderItemDO -> List<ItemAttrDTO>
                        consumer.compose(OrderItemDO::getGuid).andThen(guidToAttrDTOS::get).apply(childItemDo))
                        // List<ItemAttrDTO> -> Stream<Pari<ItemAttrDTO,OrderItemDO>>
                        .map(itemAttrDTOS -> itemAttrDTOS.stream().map(itemAttrDTO -> Pair.of(itemAttrDTO, childItemDo)))
                        //if attrList is null return empty Stream
                        .orElse(Stream.empty()))
                // Pari<ItemAttrDTO,OrderItemDO> -> bigDecimal
                //第一个元素是itemAttrDTO,第二个元素是OrderItemDO
                .map(pair -> {
                    BigDecimal subAttrTotal = pair.getFirst().getAttrPrice().multiply(pair.getSecond().getCurrentCount());
                    //处理套餐称重
                    if (!pair.getSecond().getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
                        subAttrTotal = subAttrTotal.multiply(pair.getSecond().getPackageDefaultCount());
                    }
                    return subAttrTotal;
                })
                //累加
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}