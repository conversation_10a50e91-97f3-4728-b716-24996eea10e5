package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-06-25
 * @description
 */
@Component
@Slf4j
public class SingleMemberDiscountHandler extends DiscountHandler{
    @Override
    void dealDiscount(DiscountContext context) {
        if(context.isRejectDiscount()){
            return;
        }
        if (context.isCanMemberPrice() && !context.isIntegralStore()) {
            DiscountFeeDetailDTO singleMemberCard = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap()
                    .get(type()));
            // 如果使用了第三方活动，应该使用活动扣减后的金额去计算会员价
            BigDecimal singleMemeberDicountFee = dealWithDiscountFee(context.getOrderDetailRespDTO().getOrderSurplusFee(),
                    AmountCalculationUtil.getSingleMemeberDicountFee(context.getAllItems(), context.getDineInItemDTOMap(), context.isUseBuyPrice()));
            singleMemberCard.setDiscountFee(singleMemeberDicountFee);
            context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(singleMemberCard
                    .getDiscountFee()));
            context.getDiscountFeeDetailDTOS().add(singleMemberCard);
            log.info("2.2 会员单品价优惠：{}，订单剩余金额：{}", singleMemberCard.getDiscountFee(), context.getOrderDetailRespDTO()
                    .getOrderSurplusFee());
        }
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.SINGLE_MEMBER.getCode();
    }
}
