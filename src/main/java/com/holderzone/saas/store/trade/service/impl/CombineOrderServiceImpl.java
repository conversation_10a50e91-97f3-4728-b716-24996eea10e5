package com.holderzone.saas.store.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.OrderTableReqDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.trade.context.LocalizeSynchronizeContext;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.PadOrderDO;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.repository.feign.KdsClientService;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.repository.interfaces.DiscountService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.CombineOrderService;
import com.holderzone.saas.store.trade.service.PadOrderService;
import com.holderzone.saas.store.trade.service.mq.KdsMqService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.CommonUtil;
import com.holderzone.saas.store.trade.utils.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CombineOrderServiceImpl
 * @date 2019/01/25 10:04
 * @description 并单拆单
 * @program holder-saas-store-trade
 */
@Service
@Slf4j
public class CombineOrderServiceImpl implements CombineOrderService {

    private final OrderService orderService;

    private final DiscountService discountService;

    private final MemberTerminalClientService memberTerminalClientService;

    private final KdsClientService kdsClientService;

    private final KdsMqService kdsMqService;

    private final PadOrderService padOrderService;

    @Autowired
    public CombineOrderServiceImpl(OrderService orderService, DiscountService discountService,
                                   KdsClientService kdsClientService, KdsMqService kdsMqService,
                                   MemberTerminalClientService memberTerminalClientService, PadOrderService padOrderService) {
        this.orderService = orderService;
        this.discountService = discountService;
        this.kdsClientService = kdsClientService;
        this.kdsMqService = kdsMqService;
        this.memberTerminalClientService = memberTerminalClientService;
        this.padOrderService = padOrderService;
    }

    @Override
    public Boolean split(TableOrderCombineDTO tableOrderCombineDTO) {
        List<OrderDO> orderDOS = new ArrayList<>();
        List<Long> subOrderGuids = new ArrayList<>();
        List<TableInfoDTO> tableInfoDTOS = tableOrderCombineDTO.getTableInfoDTOS();
        String dtoMainOrderGuid = tableOrderCombineDTO.getMainOrderGuid();
        List<OrderDO> subOrderDOS = orderService.listByMainOrderGuid(dtoMainOrderGuid);
        Map<Long, OrderDO> subOrderDOMap = CollectionUtil.toMap(subOrderDOS, "guid");

        //为空时拆主单
        if (CollectionUtils.isEmpty(tableInfoDTOS)) {
            OrderDO mainOrder = new OrderDO();
            Long mainOrderGuid = Long.valueOf(dtoMainOrderGuid);
            mainOrder.setGuid(mainOrderGuid);
            mainOrder.setUpperState(UpperStateEnum.GENERAL.getCode());

            /* BugFix:17696
               在订单中冗余了orderFeeForCombine字段，此时拆主桌时，将这个字段赋值到orderFee中。
               但是要兼容老数据、以及做好判空，不影响主流程
             */
            OrderDO order = orderService.getByIdWithCache(String.valueOf(mainOrderGuid));
            if (!ObjectUtils.isEmpty(order) && order.getOrderFeeForCombine() != null) {
                mainOrder.setOrderFee(order.getOrderFeeForCombine());
            }
            orderDOS.add(mainOrder);
            if (!CollectionUtils.isEmpty(subOrderDOS)) {
                for (OrderDO orderDO : subOrderDOS) {
                    OrderDO subOrder = new OrderDO();
                    //本地化
                    LocalizeSynchronizeContext.putOrderGuid(String.valueOf(subOrder.getGuid()));
                    //不清楚主单，状态判断
                    subOrder.setGuid(orderDO.getGuid());
                    removeMemberInfo(subOrder);
                    subOrderGuids.add(orderDO.getGuid());
                    subOrder.setUpperState(UpperStateEnum.GENERAL.getCode());
                    subOrder.setMainOrderGuid(0L);
                    //BugFixed：20881拆单时，判断是主单，且有预定金的情况下，将所有子单的reserveFee设置为0
                    if (orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode()) && BigDecimalUtil.greaterThanZero(orderDO.getReserveFee())) {
                        subOrder.setReserveFee(BigDecimal.ZERO);
                    }
                    orderDOS.add(subOrder);
                }
            }
            discountService.removeByOrderGuids(subOrderGuids);

            // 清空pad点餐的并单guid
            clearPadCombineMainGuid(tableOrderCombineDTO.getMainOrderGuid());

            return orderService.updateBatchByIdWithDeleteCache(orderDOS);

        }
        //考虑数据同步延迟问题对比前端数据和数据库数据
        for (TableInfoDTO tableInfoDTO : tableInfoDTOS) {
            if (!subOrderDOMap.containsKey(Long.valueOf(tableInfoDTO.getOrderGuid()))) {
                throw new ParameterException("拆单数据不一致，请刷新");
            }
        }

        //要拆的单库里都存在并且要拆的数量等于库里的数量说明是最后一次拆单
        if (tableInfoDTOS.size() == subOrderDOS.size()) {
            //拆主单
            OrderDO mainOrder = new OrderDO();
            Long mainOrderGuid = Long.valueOf(dtoMainOrderGuid);
            mainOrder.setGuid(mainOrderGuid);
            mainOrder.setUpperState(UpperStateEnum.GENERAL.getCode());
            mainOrder.setMainOrderGuid(0L);
            orderDOS.add(mainOrder);
        }

        for (TableInfoDTO tableInfoDTO : tableInfoDTOS) {
            OrderDO subOrder = new OrderDO();
            //不清楚主单，状态判断
            Long subGuid = Long.valueOf(tableInfoDTO.getOrderGuid());
            LocalizeSynchronizeContext.putOrderGuid(tableInfoDTO.getOrderGuid());
            subOrder.setGuid(subGuid);
            subOrder.setUpperState(UpperStateEnum.GENERAL.getCode());
            subOrder.setMainOrderGuid(0L);
            //BugFixed：20881拆单时，判断是子单，且有预定金的情况下，将子单的reserveFee设置为0
            OrderDO orderInDB = orderService.getByIdWithCache(String.valueOf(subGuid));
            if (BigDecimalUtil.greaterThanZero(orderInDB.getReserveFee()) && orderInDB.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
                subOrder.setReserveFee(BigDecimal.ZERO);
            }
            orderDOS.add(subOrder);
            removeMemberInfo(subOrder);
            subOrderGuids.add(subGuid);
        }
        //移除子单优惠记录
        discountService.removeByOrderGuids(subOrderGuids);

        // 清空pad点餐的并单guid
        List<String> subOrderGuidList = tableInfoDTOS.stream()
                .map(TableInfoDTO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        clearPadCombineGuid(tableOrderCombineDTO, subOrderGuidList);

        return orderService.updateBatchByIdWithDeleteCache(orderDOS);
    }

    /**
     * 清空pad点餐的并单guid（拆主单）
     *
     * @param mainOrderGuid 主单Guid
     */
    private void clearPadCombineMainGuid(String mainOrderGuid) {
        List<PadOrderDO> padOrderDOList = padOrderService.list(new LambdaQueryWrapper<PadOrderDO>()
                .eq(PadOrderDO::getCombineOrderGuid, mainOrderGuid)
                .eq(PadOrderDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        if (!CollectionUtils.isEmpty(padOrderDOList)) {
            padOrderDOList.forEach(pad -> pad.setCombineOrderGuid(null));
            padOrderService.updateBatchById(padOrderDOList);
        }
    }

    /**
     * 清空pad点餐的并单guid
     *
     * @param tableOrderCombineDTO TableOrderCombineDTO
     * @param subOrderGuidList     List<String>
     */
    private void clearPadCombineGuid(TableOrderCombineDTO tableOrderCombineDTO, List<String> subOrderGuidList) {
        // 主单也加上
        subOrderGuidList.add(tableOrderCombineDTO.getMainOrderGuid());
        List<PadOrderDO> padOrderDOList = padOrderService.list(new LambdaQueryWrapper<PadOrderDO>()
                .in(PadOrderDO::getOrderGuid, subOrderGuidList)
                .eq(PadOrderDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        if (!CollectionUtils.isEmpty(padOrderDOList)) {
            padOrderDOList.forEach(pad -> pad.setCombineOrderGuid(null));
            padOrderService.updateBatchById(padOrderDOList);
        }
    }

    @Override
    public Boolean combine(TableOrderCombineDTO tableOrderCombineDTO) {
        //增量并单
        List<TableInfoDTO> tableInfoDTOS = tableOrderCombineDTO.getTableInfoDTOS();

        List<String> orderGuids = new ArrayList<>();
        List<Long> subOrderGuids = new ArrayList<>();

        //校验结账之后不允许和单
        orderGuids.add(tableOrderCombineDTO.getMainOrderGuid());
        for (TableInfoDTO tableInfoDTO : tableInfoDTOS) {
            orderGuids.add(tableInfoDTO.getOrderGuid());
            subOrderGuids.add(Long.valueOf(tableInfoDTO.getOrderGuid()));
        }
        List<OrderDO> orderDOSInDb = new ArrayList<>(orderService.listByIds(orderGuids));
        Map<Long, OrderDO> objectOrderDOMap = CollectionUtil.toMap(orderDOSInDb, "guid");
        for (OrderDO orderDO : orderDOSInDb) {
            if (!OrderUtil.unfinished(orderDO)) {
                throw new ParameterException("只有未结账订单可以并单，" + orderDO.getOrderNo() + "订单状态异常");
            }
            if (!orderDO.getGuid().toString().equals(tableOrderCombineDTO.getMainOrderGuid()) &&
                    StringUtils.isNotBlank(orderDO.getReserveGuid()) && !orderDO.getReserveGuid().equals("0")
                    && (orderDO.getUpperState() == 1 || orderDO.getUpperState() == 0)) {
                throw new ParameterException("预定主台不能被并台");
            }
            if (!Boolean.TRUE.equals(tableOrderCombineDTO.getIgnoreVerifySameOrderFlag())
                    && UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
                throw new BusinessException("产生结账的桌台不能并台");
            }
        }
        List<OrderDO> orderDOS = new ArrayList<>();
        OrderDO mainOrder = new OrderDO();
        Long mainOrderGuid = Long.valueOf(tableOrderCombineDTO.getMainOrderGuid());
        mainOrder.setGuid(mainOrderGuid);
        mainOrder.setUpperState(UpperStateEnum.MAIN.getCode());
        mainOrder.setMainOrderGuid(0L);
        //每次并单都更新主单
        orderDOS.add(mainOrder);

        for (TableInfoDTO tableInfoDTO : tableInfoDTOS) {
            OrderDO subOrder = new OrderDO();
            subOrder.setGuid(Long.valueOf(tableInfoDTO.getOrderGuid()));
            //本地化嵌入
//            LocalizeSynchronizeContext.putOrderGuid(tableInfoDTO.getOrderGuid());
            subOrder.setUpperState(UpperStateEnum.SUB.getCode());
            subOrder.setMainOrderGuid(mainOrderGuid);
            OrderDO orderDO = objectOrderDOMap.get(Long.valueOf(tableInfoDTO.getOrderGuid()));
            if (orderDO != null) {
                subOrder.setMemberConsumptionGuid(orderDO.getMemberConsumptionGuid());
            }
            removeMemberInfo(subOrder);
            orderDOS.add(subOrder);
        }
        //移除子单优惠记录
        discountService.removeByOrderGuids(subOrderGuids);

        // 修改pad下单表信息
        List<String> subOrderGuidList = tableInfoDTOS.stream()
                .map(TableInfoDTO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        // 主单也加上
        subOrderGuidList.add(tableOrderCombineDTO.getMainOrderGuid());
        List<PadOrderDO> padOrderDOList = padOrderService.list(new LambdaQueryWrapper<PadOrderDO>()
                .in(PadOrderDO::getOrderGuid, subOrderGuidList)
                .eq(PadOrderDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        if (!CollectionUtils.isEmpty(padOrderDOList)) {
            padOrderDOList.forEach(pad -> pad.setCombineOrderGuid(tableOrderCombineDTO.getMainOrderGuid()));
            padOrderService.updateBatchById(padOrderDOList);
        }

        return orderService.updateBatchByIdWithDeleteCache(orderDOS);
    }

    /**
     * 移除子单会员相关信息
     *
     * @param subOrder
     */
    private void removeMemberInfo(OrderDO subOrder) {
        subOrder.setMemberGuid("0");
        subOrder.setMemberPhone(StringUtils.EMPTY);
        subOrder.setMemberName(StringUtils.EMPTY);
        subOrder.setMemberCardGuid("0");
        //退会员优惠
        log.warn("拆台时撤销会员优惠券，subOrder:{}", JacksonUtils.writeValueAsString(subOrder));
        if (StringUtils.isNotEmpty(subOrder.getMemberConsumptionGuid())) {
            boolean member = memberTerminalClientService.cancelAll(subOrder.getMemberConsumptionGuid());
            if (member) {
                log.info("会员优惠券撤销成功");
            } else {
                log.error("会员优惠券撤销失败");
            }
        }
    }

    @Override
    public Boolean transform(TradeTableDTO tradeTableDTO) {
        OrderDO orderDO = orderService.getByIdWithCache(tradeTableDTO.getOrderGuid());
        if (UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
            // 多单结账转台
            sameOrderTransformHandler(tradeTableDTO, orderDO);
        } else {
            // 转台
            businessTransformHandler(tradeTableDTO, orderDO);
        }
        return true;
    }

    private void businessTransformHandler(TradeTableDTO tradeTableDTO, OrderDO orderDO) {
        OrderTableReqDTO orderTableReqDTO = new OrderTableReqDTO();
        orderTableReqDTO.setOriTableGuid(orderDO.getDiningTableGuid());
        orderDO.setGuid(Long.valueOf(tradeTableDTO.getOrderGuid()));
        orderDO.setDiningTableGuid(tradeTableDTO.getTableGuid());
        orderDO.setDiningTableName(CommonUtil.jointDiningTableName(tradeTableDTO.getAreaName(), tradeTableDTO.getTableName()));
        orderTableReqDTO.setOrderGuid(tradeTableDTO.getOrderGuid());
        orderTableReqDTO.setAreaGuid(tradeTableDTO.getAreaName());
        orderTableReqDTO.setNewTableGuid(tradeTableDTO.getTableGuid());
        orderTableReqDTO.setNewTableName(tradeTableDTO.getTableName());
        kdsMqService.changeTable(orderTableReqDTO);

        // 转台更新pad下单表
        if (Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), orderDO.getDeviceType())) {
            List<PadOrderRespDTO> padOrderDTOList = padOrderService.listPadOrderDTOList(tradeTableDTO.getOrderGuid());
            if (!CollectionUtils.isEmpty(padOrderDTOList)) {
                padOrderDTOList.forEach(pad -> {
                    pad.setDiningTableGuid(tradeTableDTO.getTableGuid());
                    pad.setTableCode(tradeTableDTO.getTableName());
                });
                List<PadOrderDO> padOrderDOList = OrderTransform.INSTANCE.OrderRespDTOList2padOrderDOList(padOrderDTOList);
                padOrderService.updateBatchById(padOrderDOList);
            }
        }
        orderService.updateByIdWithDeleteCache(orderDO);
    }

    /**
     * 多单结账转台
     */
    private void sameOrderTransformHandler(TradeTableDTO tradeTableDTO, OrderDO orderDO) {
        Long orderGuid;
        if (Objects.nonNull(orderDO.getMainOrderGuid()) && !Objects.equals(0L, orderDO.getMainOrderGuid())) {
            orderGuid = orderDO.getMainOrderGuid();
        } else {
            orderGuid = orderDO.getGuid();
        }
        List<OrderDO> subOrderDOS = orderService.otherListByMainOrderGuid(orderGuid);
        for (OrderDO subOrderDO : subOrderDOS) {
            TradeTableDTO otherTradeTableDTO = new TradeTableDTO();
            BeanUtils.copyProperties(tradeTableDTO, otherTradeTableDTO);
            otherTradeTableDTO.setOrderGuid(String.valueOf(subOrderDO.getGuid()));
            businessTransformHandler(otherTradeTableDTO, subOrderDO);
        }
    }

}
