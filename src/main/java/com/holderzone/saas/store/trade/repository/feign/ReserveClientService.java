package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.reserve.NotifyPayReqDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRecoveryDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRefundDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDetailDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveClientService
 * @date 2018/08/14 14:12
 * @description //
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-reserve", fallbackFactory = ReserveClientService.ReserveClientServiceFallBack
        .class)
public interface ReserveClientService {

    @PostMapping("/reserve/getItems")
    List<DineInItemDTO> getItems(@RequestBody ReserveRecordGuidDTO guidDTO);

    @PostMapping("/refund")
    String refund(@RequestBody SingleDataDTO singleDataDTO);

    /**
     * 部分退款
     */
    @PostMapping("/part_refund")
    BigDecimal partRefund(@RequestBody ReserveRefundDTO reserveRefundDTO);

    /**
     * 查询指定预订记录来源
     */
    @PostMapping("/reserve/obtainDeviceType")
    Integer obtainDeviceType(@RequestBody ReserveRecordGuidDTO guidDTO);

    @ApiOperation("根据订单查询预付金信息")
    @PostMapping("/reserve/queryByOrderGuid")
    ReserveRecordDTO queryByOrderGuid(@RequestBody SingleDataDTO query);

    @ApiOperation("根据guid查询预付金信息")
    @PostMapping("/reserve/queryByGuid")
    ReserveRecordDTO queryByGuid(@RequestBody SingleDataDTO query);

    @ApiOperation("支付成功通知")
    @PostMapping("/reserve/notifyPay")
    void notifyPay(NotifyPayReqDTO reqDTO);

    @ApiOperation("预付金反结账")
    @PostMapping("/reserve/recovery")
    void recovery(ReserveRecoveryDTO recoveryDTO);

    @ApiOperation("取消预定")
    @PostMapping("/reserve/cancle")
    ReserveRecordDetailDTO cancle(@RequestBody ReserveRecordGuidDTO guidDTO);

    @Component
    class ReserveClientServiceFallBack implements FallbackFactory<ReserveClientService> {

        private static final Logger logger = LoggerFactory.getLogger(ReserveClientServiceFallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReserveClientService create(Throwable throwable) {

            return new ReserveClientService() {
                @Override
                public List<DineInItemDTO> getItems(ReserveRecordGuidDTO guidDTO) {
                    logger.error("获取预订信息失败 guidDTO={}", JacksonUtils.writeValueAsString(guidDTO));
                    throw new ParameterException("获取预订信息失败!");
                }

                @Override
                public String refund(SingleDataDTO singleDataDTO) {
                    logger.error("退预定金失败 guidDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
                    throw new ParameterException("退预定金失败!");
                }

                @Override
                public BigDecimal partRefund(ReserveRefundDTO reserveRefundDTO) {
                    logger.error("部分退预定金失败 reserveRefundDTO={}", JacksonUtils.writeValueAsString(reserveRefundDTO));
                    throw new ParameterException("部分退预定金失败!");
                }

                @Override
                public Integer obtainDeviceType(ReserveRecordGuidDTO guidDTO) {
                    logger.error("查询预定单来源失败 guidDTO={}", JacksonUtils.writeValueAsString(guidDTO));
                    throw new ParameterException("查询预定单来源失败!");
                }

                @Override
                public ReserveRecordDTO queryByOrderGuid(SingleDataDTO query) {
                    logger.error(HYSTRIX_PATTERN, "queryByOrderGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ReserveRecordDTO queryByGuid(SingleDataDTO query) {
                    logger.error(HYSTRIX_PATTERN, "queryByGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void notifyPay(NotifyPayReqDTO reqDTO) {
                    logger.error(HYSTRIX_PATTERN, "notifyPay", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void recovery(ReserveRecoveryDTO recoveryDTO) {
                    logger.error(HYSTRIX_PATTERN, "recovery", JacksonUtils.writeValueAsString(recoveryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ReserveRecordDetailDTO cancle(ReserveRecordGuidDTO guidDTO) {
                    logger.error(HYSTRIX_PATTERN, "cancle", JacksonUtils.writeValueAsString(guidDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
