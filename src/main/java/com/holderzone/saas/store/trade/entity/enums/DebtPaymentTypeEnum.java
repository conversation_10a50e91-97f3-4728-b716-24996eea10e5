package com.holderzone.saas.store.trade.entity.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> R
 * @date 2021/1/7 10:48
 * @description
 */
public enum DebtPaymentTypeEnum {
    //结算方式（0：现金，1：支付宝，2：微信，3：银行卡，4：支票）
    CASH_PAY(0, "现金支付"),

    ALI_PAY(1, "支付宝"),

    WeChat_PAY(2, "微信"),

    BANK_CARD_PAY(3, "银联卡支付"),

    CHECK_PAY(4, "支票");

    private Integer id;

    private String name;

    DebtPaymentTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameById(Integer id) {
        return Arrays.stream(DebtPaymentTypeEnum.values())
                .filter(paymentType -> Objects.equals(paymentType.id, id))
                .map(paymentType -> paymentType.name)
                .findFirst()
                .orElse("未知类型");
    }

    public static List<Integer> getAllId() {
        return Arrays.stream(DebtPaymentTypeEnum.values())
                .map(DebtPaymentTypeEnum::getId)
                .collect(Collectors.toList());
    }
}
