package com.holderzone.saas.store.trade.entity.read;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "调整单商品")
@Data
@TableName("hst_adjust_item_info")
public class AdjustItmeDO {

    private String guid;

    private String orderGuid;

    @ApiModelProperty(value = "订单商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "订单商品名称")
    private String itemName;

    @ApiModelProperty(value = "订单商品价格")
    private BigDecimal price;

    @ApiModelProperty(value = "订单商品数量")
    private BigDecimal count;

    @ApiModelProperty(value = "调整商品数量")
    private BigDecimal adjustCount;

    @ApiModelProperty(value = "回滚数量")
    private BigDecimal backCount;

    /**
     * 商品类型  0：普通商品  1：赠送商品  2：套餐商品
     */
    private Integer type;
}