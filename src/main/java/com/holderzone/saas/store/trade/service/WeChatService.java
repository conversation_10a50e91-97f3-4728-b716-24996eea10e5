package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-order
 */
public interface WeChatService {


    List<DineinOrderDetailRespDTO> getOrderDetailList(SingleListDTO singleListDTO);

    List<DineinOrderDetailRespDTO> getOrderDetails(SingleListDTO singleListDTO);

    OrderWechatDTO getOrder(String orderGuid);

    Boolean hasItem(Long orderGuid);

    Boolean pay(WeChatPayReqDTO weChatPayReqDTO);
}
