package com.holderzone.saas.store.trade.manager;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.constant.RedisKeyConstant;
import com.holderzone.saas.store.dto.order.BillMultipleRefundAggPayDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillAggPayReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillMultiplePayReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillMultipleRefundAggPayReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillRefundAggPayRespDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.pay.AggRefundReqDTO;
import com.holderzone.saas.store.dto.pay.AggRefundRespDTO;
import com.holderzone.saas.store.dto.pay.SaasAggRefundDTO;
import com.holderzone.saas.store.dto.trade.MergeOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.dto.trade.req.FixGrouponTransactionRecordReqDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.TradeStateEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.helper.BillVerifyHelper;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.repository.feign.AggPayClientService;
import com.holderzone.saas.store.trade.repository.feign.StoreClientService;
import com.holderzone.saas.store.trade.repository.interfaces.DiscountService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderExtendsService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.repository.interfaces.TransactionRecordService;
import com.holderzone.saas.store.trade.service.BillService;
import com.holderzone.saas.store.trade.service.IMultipleTransactionRecordService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/14
 * @description 结账业务
 */
@Slf4j
@Component
public class DineInBillManager {

    private final RedisHelper redisHelper;

    private final BillService billService;

    private final OrderService orderService;

    private final OrderExtendsService orderExtendsService;

    private final IMultipleTransactionRecordService multipleTransactionRecordService;

    private final DynamicHelper dynamicHelper;

    private final StoreClientService storeClientService;

    private final TransactionRecordService transactionRecordService;

    private final AggPayClientService aggPayClientService;

    private final DiscountService discountService;

    @Resource(name = "aggTransactionRecordExecutor")
    private ExecutorService aggTransactionRecordExecutor;

    @Autowired
    public DineInBillManager(RedisHelper redisHelper,
                             BillService billService,
                             OrderService orderService, OrderExtendsService orderExtendsService,
                             IMultipleTransactionRecordService multipleTransactionRecordService,
                             DynamicHelper dynamicHelper,
                             StoreClientService storeClientService,
                             TransactionRecordService transactionRecordService,
                             AggPayClientService aggPayClientService,
                             DiscountService discountService) {
        this.redisHelper = redisHelper;
        this.billService = billService;
        this.orderService = orderService;
        this.orderExtendsService = orderExtendsService;
        this.multipleTransactionRecordService = multipleTransactionRecordService;
        this.dynamicHelper = dynamicHelper;
        this.storeClientService = storeClientService;
        this.transactionRecordService = transactionRecordService;
        this.aggPayClientService = aggPayClientService;
        this.discountService = discountService;
    }

    public BillAggPayRespDTO multiplePay(BillMultiplePayReqDTO multiplePayReqDTO) {
        String authCode = multiplePayReqDTO.getAuthCode();
        checkAuthCodeEmpty(authCode, multiplePayReqDTO.getActiveScan());
        return toMultiplePay(multiplePayReqDTO);
    }

    /**
     * 聚合支付部分退款
     */
    public BillRefundAggPayRespDTO multipleRefundAggPay(BillMultipleRefundAggPayReqDTO multipleRefundAggPayReqDTO) {
        // 查询订单
        OrderDO orderDO = orderService.getByIdWithCache(multipleRefundAggPayReqDTO.getOrderGuid());
        // 校验订单
        verifyOrderMultipleRefundAggPay(orderDO);
        // 查询订单多次聚合支付记录
        List<MultipleTransactionRecordDO> multipleTransactionRecordList = queryMultipleTransactionRecordList(orderDO);
        // 校验订单多次聚合支付记录
        verifyMultipleTransactionRecord(multipleRefundAggPayReqDTO.getRefundAggPays(), multipleTransactionRecordList);
        // 开始退款
        BillRefundAggPayRespDTO refundAggPayRespDTO = returnAggPay(multipleRefundAggPayReqDTO);
        // 更新聚合支付记录
        persistenceRefundAmount(refundAggPayRespDTO);
        return refundAggPayRespDTO;
    }

    /**
     * 查询订单多次聚合支付记录
     */
    private List<MultipleTransactionRecordDO> queryMultipleTransactionRecordList(OrderDO orderDO) {
        List<Long> mergeOrderGuidList = Lists.newArrayList(orderDO.getGuid());
        // 查询是否有无并桌
        Integer upperState = Optional.ofNullable(orderDO.getUpperState()).orElse(UpperStateEnum.GENERAL.getCode());
        if (upperState == UpperStateEnum.MAIN.getCode() || upperState == UpperStateEnum.SUB.getCode()) {
            List<OrderDO> mergeOrderList = orderService.getMergeOrderList(String.valueOf(orderDO.getGuid()), upperState);
            mergeOrderGuidList = mergeOrderList.stream()
                    .map(OrderDO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return multipleTransactionRecordService.listByOrderGuidList(mergeOrderGuidList);
    }

    /**
     * 校验订单
     */
    private void verifyOrderMultipleRefundAggPay(OrderDO orderDO) {
        // 校验订单状态
        if (Objects.isNull(orderDO) || !Lists.newArrayList(StateEnum.READY.getCode(),
                StateEnum.PENDING.getCode(), StateEnum.FAILURE.getCode()).contains(orderDO.getState())) {
            throw new BusinessException("订单状态发生变化，请刷新后重试");
        }
    }

    /**
     * 校验订单聚合支付多次金额
     */
    private void verifyMultipleTransactionRecord(List<BillMultipleRefundAggPayDTO> refundAggPays,
                                                 List<MultipleTransactionRecordDO> multipleTransactionRecordList) {
        if (CollectionUtils.isEmpty(multipleTransactionRecordList)) {
            throw new BusinessException("订单未多次使用聚合支付，请刷新后重试");
        }
        Map<Long, MultipleTransactionRecordDO> multipleTransactionRecordMap = multipleTransactionRecordList.stream()
                .collect(Collectors.toMap(MultipleTransactionRecordDO::getGuid, Function.identity(), (key1, key2) -> key1));
        for (BillMultipleRefundAggPayDTO refundAggPay : refundAggPays) {
            if (!BigDecimalUtil.greaterThanZero(refundAggPay.getAmount())) {
                throw new BusinessException("退款金额不能小于0");
            }
            MultipleTransactionRecordDO multipleTransactionRecordDO = multipleTransactionRecordMap.get(Long.parseLong(refundAggPay.getGuid()));
            if (Objects.isNull(multipleTransactionRecordDO)) {
                log.error("multipleTransactionRecordDO is null, guid:{}", refundAggPay.getGuid());
                throw new BusinessException("退款明细不存在");
            }
            BigDecimal refundableFee = multipleTransactionRecordDO.getAmount().subtract(multipleTransactionRecordDO.getRefundAmount());
            if (refundAggPay.getAmount().compareTo(refundableFee) > 0) {
                throw new BusinessException("超过可退金额，请刷新后重试");
            }
            refundAggPay.setOrderGuid(String.valueOf(multipleTransactionRecordDO.getOrderGuid()));
            refundAggPay.setTransactionRecordGuid(String.valueOf(multipleTransactionRecordDO.getTransactionRecordGuid()));
            refundAggPay.setOriginalPayGuid(Objects.nonNull(multipleTransactionRecordDO.getOriginalMultipleTransactionRecordGuid()) ?
                    String.valueOf(multipleTransactionRecordDO.getOriginalMultipleTransactionRecordGuid()) : null);
            refundAggPay.setOriginalOrderGuid(Objects.nonNull(multipleTransactionRecordDO.getOriginalOrderGuid()) ?
                    String.valueOf(multipleTransactionRecordDO.getOriginalOrderGuid()) : null);
            refundAggPay.setRefundType(0);
            if (refundAggPay.getAmount().compareTo(multipleTransactionRecordDO.getAmount()) == 0) {
                refundAggPay.setRefundType(1);
            }
        }
    }

    private BillRefundAggPayRespDTO returnAggPay(BillMultipleRefundAggPayReqDTO multipleRefundAggPayReqDTO) {
        List<BillMultipleRefundAggPayDTO> refundAggPays = multipleRefundAggPayReqDTO.getRefundAggPays();
        BillRefundAggPayRespDTO billRefundAggPayRespDTO = new BillRefundAggPayRespDTO();
        List<List<BillMultipleRefundAggPayDTO>> refundAggPayReqPartition = Lists.partition(refundAggPays, Constant.BATCH_AGG_REFUND_REQ_PARTITION);
        CompletableFuture.allOf(refundAggPayReqPartition.stream()
                .map(refundAggPayReqList ->
                        CompletableFuture.runAsync(() ->
                                                refundAggPayReqList.forEach(e -> aggPortionRefund(multipleRefundAggPayReqDTO, e)),
                                        aggTransactionRecordExecutor)
                                .whenComplete((result, throwable) -> {
                                    if (throwable != null) {
                                        log.error("completableFuture error:{}", throwable.getMessage());
                                    }
                                })).toArray(CompletableFuture[]::new)).whenComplete((v, th) -> {
        }).join();
        log.info("聚合支付多次支付退款结果:{}", JacksonUtils.writeValueAsString(refundAggPays));
        billRefundAggPayRespDTO.setRefundAggPays(refundAggPays);
        return billRefundAggPayRespDTO;
    }

    /**
     * 聚合支付部分退款
     */
    private void aggPortionRefund(BillMultipleRefundAggPayReqDTO multipleRefundAggPayReqDTO,
                                  BillMultipleRefundAggPayDTO refundAggPay) {
        try {
            // 构建退款请求参数
            AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
            aggRefundReqDTO.setRefundType(refundAggPay.getRefundType());
            aggRefundReqDTO.setRefundFee(refundAggPay.getAmount().abs());
            aggRefundReqDTO.setReason("聚合支付部分退款");
            aggRefundReqDTO.setPayGUID(String.valueOf(refundAggPay.getGuid()));
            aggRefundReqDTO.setOrderGUID(refundAggPay.getOrderGuid());
            if (Objects.nonNull(refundAggPay.getOriginalPayGuid())) {
                aggRefundReqDTO.setPayGUID(refundAggPay.getOriginalPayGuid());
            }
            if (Objects.nonNull(refundAggPay.getOriginalOrderGuid())) {
                aggRefundReqDTO.setOrderGUID(refundAggPay.getOriginalOrderGuid());
            }
            SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
            BeanUtils.copyProperties(multipleRefundAggPayReqDTO, saasAggRefundDTO);
            saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
            log.info("聚合订单部分退款入参：{}", JacksonUtils.writeValueAsString(saasAggRefundDTO));
            AggRefundRespDTO refund = aggPayClientService.portionRefund(saasAggRefundDTO);
            BillVerifyHelper.multipleRefundResult(refund, true);
            refundAggPay.setSuccessFlag(true);
        } catch (Exception e) {
            log.error("聚合支付退款失败, e", e);
            refundAggPay.setSuccessFlag(false);
            refundAggPay.setErrorMsg(e.getMessage().replace("退款失败，", ""));
        }
    }

    /**
     * 保存聚合支付记录退款金额
     */
    private void persistenceRefundAmount(BillRefundAggPayRespDTO refundAggPayRespDTO) {
        // multipleTransactionRecord
        List<MultipleTransactionRecordDO> multipleTransactionRecordList = buildRefundMultipleTransactionRecordDO(refundAggPayRespDTO);
        if (CollectionUtils.isEmpty(multipleTransactionRecordList)) {
            return;
        }
        multipleTransactionRecordService.addBatchRefundAmount(multipleTransactionRecordList);
        // 退款总金额
        Map<Long, BigDecimal> multipleTransactionRecordMap = multipleTransactionRecordList.stream()
                .collect(Collectors.groupingBy(MultipleTransactionRecordDO::getTransactionRecordGuid,
                        Collectors.reducing(BigDecimal.ZERO,
                                MultipleTransactionRecordDO::getAmount,
                                BigDecimal::add)));
        for (Map.Entry<Long, BigDecimal> entry : multipleTransactionRecordMap.entrySet()) {
            transactionRecordService.updateRefundAmountByGuid(entry.getKey(), entry.getValue());
        }
        Map<Long, List<MultipleTransactionRecordDO>> transactionRecordMap = multipleTransactionRecordList.stream()
                .collect(Collectors.groupingBy(MultipleTransactionRecordDO::getOrderGuid));
        for (Map.Entry<Long, List<MultipleTransactionRecordDO>> entry : transactionRecordMap.entrySet()) {
            Long orderGuid = entry.getKey();
            List<MultipleTransactionRecordDO> transactionRecordList = multipleTransactionRecordService.listByOrderGuid(String.valueOf(orderGuid));
            transactionRecordList = transactionRecordList.stream()
                    .filter(e -> TradeTypeEnum.GENERAL_IN.getCode() == e.getTradeType()
                            && e.getAmount().subtract(e.getRefundAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(transactionRecordList)) {
                OrderExtendsDO orderExtendsDO = new OrderExtendsDO();
                orderExtendsDO.setGuid(orderGuid);
                orderExtendsDO.setIsMultipleAggPay(false);
                orderExtendsService.updateById(orderExtendsDO);
            }
        }
    }

    private List<MultipleTransactionRecordDO> buildRefundMultipleTransactionRecordDO(BillRefundAggPayRespDTO refundAggPayRespDTO) {
        List<BillMultipleRefundAggPayDTO> refundAggPays = refundAggPayRespDTO.getRefundAggPays();
        return refundAggPays.stream()
                .filter(e -> Boolean.TRUE.equals(e.getSuccessFlag()))
                .map(e -> {
                    MultipleTransactionRecordDO refundRecordDO = new MultipleTransactionRecordDO();
                    refundRecordDO.setGuid(Long.parseLong(e.getGuid()));
                    refundRecordDO.setAmount(e.getAmount());
                    refundRecordDO.setOrderGuid(Long.valueOf(e.getOrderGuid()));
                    refundRecordDO.setTransactionRecordGuid(Long.parseLong(e.getTransactionRecordGuid()));
                    return refundRecordDO;
                }).collect(Collectors.toList());
    }

    private void checkAuthCodeEmpty(String authCode, Integer scan) {
        if (!StringUtils.isEmpty(authCode)) {
            boolean wxOrAliPay = authCode.startsWith("1") || authCode.startsWith("2");
            if (wxOrAliPay && authCode.length() != Constant.AUTH_CODE_LENGTH) {
                throw new BusinessException("二维码有误请重新扫码！");
            }
            return;
        }
        //如果是主扫
        if (isActiveScan(scan)) {
            return;
        }
        throw new BusinessException("请使用正确的二维码！");
    }

    private boolean isActiveScan(Integer scan) {
        //主扫
        return scan != null && scan == BooleanEnum.TRUE.getCode();
    }

    private BillAggPayRespDTO toMultiplePay(BillAggPayReqDTO billPayReqDTO) {
        String multiplePayLockKey = String.format(RedisKeyConstant.MULTIPLE_PAY_LOCK_KEY, billPayReqDTO.getOrderGuid(), billPayReqDTO.getVersion());
        //预支付锁
        String advanceLockKey = String.format(RedisKeyConstant.ADVANCE_PAY_LOCK_KEY, billPayReqDTO.getOrderGuid());
        boolean lockSuccess = false;
        AtomicInteger count = new AtomicInteger(0);
        try {
            do {
                lockSuccess = redisHelper.setNxEx(multiplePayLockKey, "1", 30);
                if (lockSuccess) {
                    break;
                }
                log.info("重复调用聚合支付 {}", billPayReqDTO.getOrderGuid());
                if (count.incrementAndGet() > 10) {
                    throw new BusinessException("调用聚合支付超时");
                }

            } while (true);
            String redisRetJson = redisHelper.get(advanceLockKey);
            if (!StringUtils.hasText(redisRetJson)) {
                return buildRespAgg(advanceLockKey, billPayReqDTO);
            }
            BillAggPayRespDTO redisRet = JacksonUtils.toObject(BillAggPayRespDTO.class, redisRetJson);
            if (redisRet.getResult() != null && redisRet.getResult()) {
                log.warn("预支付已有结果redisRet={}", JacksonUtils.writeValueAsString(redisRet));
                return redisRet;
            }
            return buildRespAgg(advanceLockKey, billPayReqDTO);
        } finally {
            if (lockSuccess) {
                redisHelper.delete(multiplePayLockKey);
            }
        }
    }

    private BillAggPayRespDTO buildRespAgg(String advanceLockKey,
                                           BillAggPayReqDTO billPayReqDTO) {
        // 多次支付交易记录
        List<TransactionRecordDO> transactionRecordDOList = transactionRecordService.listJhByOrderGuid(billPayReqDTO.getOrderGuid());
        Long payGuid;
        if (CollectionUtils.isEmpty(transactionRecordDOList)) {
            payGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
        } else {
            payGuid = transactionRecordDOList.get(0).getGuid();
        }
        Long multipleRecordGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_MULTIPLE_TRANSACTION_RECORD);
        saveMultipleTransactionRecord(billPayReqDTO, payGuid, multipleRecordGuid);
        billPayReqDTO.setPayGuid(payGuid);
        billPayReqDTO.setMultipleRecordGuid(multipleRecordGuid);
        billPayReqDTO.setCheckoutSuccessFlag(Boolean.FALSE);
        BillAggPayRespDTO billAggPayRespDTO = billService.aggPay(billPayReqDTO);
        if (Boolean.TRUE.equals(billAggPayRespDTO.getResult())) {
            redisHelper.setEx(advanceLockKey, JacksonUtils.writeValueAsString(billAggPayRespDTO), 1, TimeUnit.MINUTES);
        }
        log.info("[多次支付]聚合支付返回值：{}", JacksonUtils.writeValueAsString(billAggPayRespDTO));
        return billAggPayRespDTO;
    }

    private void saveMultipleTransactionRecord(BillAggPayReqDTO billPayReqDTO,
                                               Long payGuid,
                                               Long multipleRecordGuid) {
        MultipleTransactionRecordDO multipleRecordDO = new MultipleTransactionRecordDO();
        multipleRecordDO.setGuid(multipleRecordGuid);
        multipleRecordDO.setTransactionRecordGuid(payGuid);
        String orderGuid = billPayReqDTO.getOrderGuid();
        multipleRecordDO.setOrderGuid(Long.valueOf(orderGuid));
        multipleRecordDO.setTerminalId(billPayReqDTO.getDeviceId());
        multipleRecordDO.setAmount(billPayReqDTO.getAmount());
        //正常支付的聚合支付新增时增加可退款金额
        multipleRecordDO.setRefundableFee(billPayReqDTO.getAmount());
        multipleRecordDO.setPaymentType(PaymentTypeEnum.AGG.getCode());
        multipleRecordDO.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
        multipleRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
        multipleRecordDO.setStaffName(UserContextUtils.getUserName());
        multipleRecordDO.setState(TradeStateEnum.READY.getCode());
        multipleRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
        multipleRecordDO.setStoreName(UserContextUtils.getStoreName());
        multipleRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
        multipleRecordDO.setCreateTime(LocalDateTime.now());
        //营业日新接口
        BusinessDateReqDTO queryDTO = new BusinessDateReqDTO();
        queryDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        queryDTO.setQueryDateTime(LocalDateTime.now());
        StoreDTO storeDTO = storeClientService.queryBusinessDayInfo(queryDTO);
        multipleRecordDO.setBusinessDay(storeDTO.getBusinessDay());
        if (billPayReqDTO.getActiveScan() != null && billPayReqDTO.getActiveScan() == BooleanEnum.TRUE.getCode()) {
            String payPowerIdStr = org.apache.commons.lang3.StringUtils.isEmpty(billPayReqDTO.getPayPowerId()) ?
                    PayPowerId.YL_WX_SCAN_CODE.getId() : billPayReqDTO.getPayPowerId();
            try {
                int payPowerId = Integer.parseInt(payPowerIdStr);
                multipleRecordDO.setPayPowerId(payPowerId);
            } catch (Exception e) {
                log.error("payPowerId设置失败", e);
            }
        }
        multipleTransactionRecordService.save(multipleRecordDO);
    }


    public void fixGrouponTransactionRecord(FixGrouponTransactionRecordReqDTO reqDTO) {
        aggTransactionRecordExecutor.execute(() -> {
            UserContextUtils.putErp(reqDTO.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(reqDTO.getEnterpriseGuid());
            // 查询使用了团购验券的订单
            List<DiscountDO> discountList = discountService.listHasGrouponCompleteOrder(reqDTO.getStartTime(), reqDTO.getOrderGuid());
            if (CollectionUtils.isEmpty(discountList)) {
                log.info("无需修复的订单");
                return;
            }
            List<Long> orderGuids = discountList.stream().map(DiscountDO::getOrderGuid).distinct().collect(Collectors.toList());
            log.info("使用团购验券的订单:{}", JacksonUtils.writeValueAsString(orderGuids));
            List<TransactionRecordDO> transactionRecordList = transactionRecordService.listByOrderGuids(orderGuids);

            List<TransactionRecordDO> grouponTransactionRecords = transactionRecordList.stream()
                    .filter(e -> PaymentTypeEnum.THIRD_ACTIVITY.getCode() == e.getPaymentType()
                            || PaymentTypeEnum.MT_GROUPON.getCode() == e.getPaymentType()
                            || DiscountTypeEnum.DOU_YIN_GROUPON.getCode() == e.getPaymentType()
                            || DiscountTypeEnum.DA_ZHONG_DIAN_PIN.getCode() == e.getPaymentType()
                            || DiscountTypeEnum.ALIPAY_GROUPON.getCode() == e.getPaymentType()
                            || DiscountTypeEnum.ABC_GROUPON.getCode() == e.getPaymentType()).collect(Collectors.toList());
            List<Long> hasOrderGuids = grouponTransactionRecords.stream().map(TransactionRecordDO::getOrderGuid).distinct().collect(Collectors.toList());

            orderGuids.removeAll(hasOrderGuids);

            log.info("需要修复的订单:{}", JacksonUtils.writeValueAsString(orderGuids));
            if (CollectionUtils.isEmpty(orderGuids)) {
                return;
            }
            Map<Long, TransactionRecordDO> transactionRecordMap = transactionRecordList.stream()
                    .collect(Collectors.toMap(TransactionRecordDO::getOrderGuid, Function.identity(), (key1, key2) -> key1));

            List<DiscountDO> fixDiscountList = discountList.stream().filter(e -> orderGuids.contains(e.getOrderGuid())).collect(Collectors.toList());

            List<TransactionRecordDO> transactionRecordDOList = Lists.newArrayList();
            List<Long> transactionRecordDOGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_TRANSACTION_RECORD, fixDiscountList.size());
            fixDiscountList.forEach(discountPay -> {
                TransactionRecordDO transactionRecordDO = new TransactionRecordDO();

                TransactionRecordDO hasTransactionRecordDO = transactionRecordMap.get(discountPay.getOrderGuid());
                if (Objects.isNull(hasTransactionRecordDO)) {
                    hasTransactionRecordDO = new TransactionRecordDO();
                    hasTransactionRecordDO.setStaffGuid(discountPay.getStaffGuid());
                    hasTransactionRecordDO.setStaffName(discountPay.getStaffName());
                    hasTransactionRecordDO.setStoreGuid(discountPay.getStoreGuid());
                    hasTransactionRecordDO.setStoreName(discountPay.getStoreName());
                    hasTransactionRecordDO.setCreateTime(discountPay.getGmtCreate());
                    hasTransactionRecordDO.setBusinessDay(discountPay.getGmtCreate().toLocalDate());
                }

                transactionRecordDO.setGuid(transactionRecordDOGuids.remove(0));
                transactionRecordDO.setOrderGuid(discountPay.getOrderGuid());
                transactionRecordDO.setTerminalId(hasTransactionRecordDO.getTerminalId());
                transactionRecordDO.setAmount(discountPay.getDiscountFee());
                if (discountPay.getDiscountType() == DiscountTypeEnum.THIRD_ACTIVITY.getCode()) {
                    transactionRecordDO.setPaymentType(PaymentTypeEnum.THIRD_ACTIVITY.getCode());
                    transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.THIRD_ACTIVITY.getDesc());
                } else if (discountPay.getDiscountType() == DiscountTypeEnum.GROUPON.getCode()) {
                    transactionRecordDO.setPaymentType(PaymentTypeEnum.MT_GROUPON.getCode());
                    transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.MT_GROUPON.getDesc());
                } else {
                    transactionRecordDO.setPaymentType(discountPay.getDiscountType());
                    transactionRecordDO.setPaymentTypeName(GroupBuyTypeEnum.getDesc(discountPay.getDiscountType()));
                }
                transactionRecordDO.setStaffGuid(hasTransactionRecordDO.getStaffGuid());
                transactionRecordDO.setStaffName(hasTransactionRecordDO.getStaffName());
                transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
                transactionRecordDO.setStoreGuid(hasTransactionRecordDO.getStoreGuid());
                transactionRecordDO.setStoreName(hasTransactionRecordDO.getStoreName());
                transactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
                transactionRecordDO.setCreateTime(hasTransactionRecordDO.getCreateTime());
                transactionRecordDO.setBusinessDay(hasTransactionRecordDO.getBusinessDay());
                transactionRecordDOList.add(transactionRecordDO);
            });
            if (CollectionUtils.isNotEmpty(transactionRecordDOList)) {
                log.info("需要修复的支付数据:{}", JacksonUtils.writeValueAsString(transactionRecordDOList));
                transactionRecordService.saveBatch(transactionRecordDOList);
            }
            log.info("修复完成");
        });
    }
}
