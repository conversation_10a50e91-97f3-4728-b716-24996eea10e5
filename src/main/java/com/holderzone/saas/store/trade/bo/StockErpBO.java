package com.holderzone.saas.store.trade.bo;

import com.holderzone.resource.common.dto.enterprise.EnterpriseSupplyChainConfigDTO;
import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import lombok.Data;

import java.util.List;

@Data
public class StockErpBO {

    private String operateCode;

    private String orderGuid;

    private OrderDO orderDO;

    private List<DineInItemDTO> items;

    /**
     * 调整单业务对象
     */
    private DepositErpSyncDTO depositErpSyncDTO;

    private EnterpriseSupplyChainConfigDTO enterpriseSupplyChainConfigDTO;
}
