package com.holderzone.saas.store.trade.utils;

import com.holderzone.saas.store.dto.trade.exception.HolderAssertException;
import com.holderzone.saas.store.trade.execption.TradeExceptionEnum;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/13 11:19
 */
public class AssertExceptionUtils<T> {

    /**
     * @param t  the object need check   需要检查的对象
     * @param message  the message of exception  异常信息 可为空
     * @param tradeExceptionEnum   the enum of exception 异常信息  不能为空
     * @param <T>   the type of input object  传输的对象
     * @return   the input object  输入的需要检查的对象
     */
    public static <T> T assertNotNullAndReturn(T t,String message,TradeExceptionEnum tradeExceptionEnum){
        if(t != null) {
            return t;
        }
        if(message == null){
            message = TradeExceptionEnum.getMessageByCode(tradeExceptionEnum.getCode());
        }
        throw new HolderAssertException(tradeExceptionEnum.getCode(),message);
    }

}