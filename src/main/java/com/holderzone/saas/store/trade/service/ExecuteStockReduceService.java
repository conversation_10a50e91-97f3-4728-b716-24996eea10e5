package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;

import java.util.List;

/**
 * 库存扣减入口
 */
public interface ExecuteStockReduceService {

    void executeStockReduce(List<DineInItemDTO> items, String orderGuid, OrderDO orderDO);

    void executeStockReturn(List<DineInItemDTO> items, String orderGuid, OrderDO orderDO);

    void executeStockAdjust(DepositErpSyncDTO depositErpSyncDTO);

}
