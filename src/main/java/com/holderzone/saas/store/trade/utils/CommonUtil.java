package com.holderzone.saas.store.trade.utils;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.organization.StoreBizDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.order.ItemPriceChangeEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @version 1.0
 * @className CommonUtils
 * @date 2018/09/04 11:26
 * @description //通用工具类
 * @program holder-saas-store-trade
 */
public class CommonUtil {

    public static boolean hasGuid(String guid) {
        return StringUtils.isNotEmpty(guid) && !"0".equals(guid);
    }

    /**
     * 获取当天结束时间
     *
     * @return
     */
    public static Date getTodayEndTime() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        return todayEnd.getTime();
    }

    /**
     * 获取当天结束时间
     *
     * @return
     */
    public static LocalDate getBusinessDay(StoreBizDTO storeBizDTO) {
        //todo 对接新营业日接口
        LocalTime businessStartTime = storeBizDTO.getBusinessStart();
        LocalTime businessEndTime = storeBizDTO.getBusinessEnd();
        //营业日为当天
        LocalDate businessDay;
        if (businessEndTime == null || businessStartTime == null) {
            throw new ParameterException("获取营业日区间失败");
        }
        LocalTime now = LocalTime.now();
        LocalTime min = LocalTime.MIN;

        businessDay = LocalDate.now();

        //跨天(结束时间小于等于开始时间)
        if (businessStartTime.compareTo(businessEndTime) >= 0) {
            //营业日为前一天（0：00<=当前时间<开始时间）
            if (now.compareTo(min) >= 0 && now.isBefore(businessStartTime)) {
                businessDay = LocalDate.now().minusDays(1);
            }
        }

        return businessDay;
    }


    /**
     * 获取当天结束时间
     *
     * @return
     */
    public static LocalDate getBusinessDay(StoreDTO storeDTO) {

        LocalTime businessStartTime = storeDTO.getBusinessStart();
        LocalTime businessEndTime = storeDTO.getBusinessEnd();
        //营业日为当天
        LocalDate businessDay;
        if (businessEndTime == null || businessStartTime == null) {
            throw new ParameterException("获取营业日区间失败");
        }
        LocalTime now = LocalTime.now();
        LocalTime min = LocalTime.MIN;

        businessDay = LocalDate.now();

        //跨天(结束时间小于等于开始时间)
        if (businessStartTime.compareTo(businessEndTime) >= 0) {
            //营业日为前一天（0：00<=当前时间<开始时间）
            if (min.compareTo(now) >= 0 && now.isBefore(businessStartTime)) {
                businessDay = LocalDate.now().minusDays(1);
            }
        }

        return businessDay;
    }


    /**
     * 获取当天结束时间
     *
     * @return
     */
    public static LocalDate getBusinessDayLocal(StoreBizDTO storeBizDTO, LocalDateTime checkoutTime) {
        LocalTime businessStartTime = storeBizDTO.getBusinessStart();
        LocalTime businessEndTime = storeBizDTO.getBusinessEnd();
        //营业日为当天
        LocalDate businessDay;
        if (businessEndTime == null || businessStartTime == null) {
            throw new ParameterException("获取营业日区间失败");
        }
        LocalTime now = checkoutTime.toLocalTime();
        LocalTime min = LocalTime.MIN;

        businessDay = checkoutTime.toLocalDate();

        //跨天(结束时间小于等于开始时间)
        if (businessStartTime.compareTo(businessEndTime) >= 0) {
            //营业日为前一天（0：00<=当前时间<开始时间）
            if (now.compareTo(min) >= 0 && now.isBefore(businessStartTime)) {
                businessDay = businessDay.minusDays(1);
            }
        }

        return businessDay;
    }

    public static List<RequestDishInfo> dineInItem2DishList(List<DineInItemDTO> dineInItemDTOS) {
        List<RequestDishInfo> dishInfoDTOlist = Lists.newArrayList();
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            dishInfoDTOlist.add(dineInItem2Dish(dineInItemDTO));
        }
        return dishInfoDTOlist;
    }

    /**
     * 将DineInItemDTO转换为会员项目的DishInfo
     * 和convertFromDineInItemDTO其实是一样的，因会员重构，DishInfo换了对象，目前只改部分接口，
     * 为避免影响其余接口，所以新加一个方法
     *
     * @param dineInItemDTO
     * @return
     */
    private static RequestDishInfo dineInItem2Dish(DineInItemDTO dineInItemDTO) {
        RequestDishInfo dishInfo = new RequestDishInfo();
        dishInfo.setOrderItemGuid(dineInItemDTO.getGuid());
        dishInfo.setDishGuid(dineInItemDTO.getItemGuid());
        dishInfo.setDishName(dineInItemDTO.getItemName());
        dishInfo.setDishSpecification(dineInItemDTO.getSkuGuid());
        dishInfo.setDishUnit(dineInItemDTO.getUnit());
        //9.21早与蔡商议，因加商品券增加赠送数量GiftDishNum字段，原DishNum字段仍然传当前数量与赠送数量之和
        dishInfo.setDishNum(dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount()));
        dishInfo.setGiftDishNum(dineInItemDTO.getFreeCount());
        dishInfo.setMainGoodGuid(null);
        dishInfo.setIsMainGood(1);
        dishInfo.setSurcharge(BigDecimal.ZERO);
        dishInfo.setRemark(dineInItemDTO.getRemark());
        dishInfo.setDishType(dineInItemDTO.getItemType());
        dishInfo.setSubtotal(dineInItemDTO.getItemPrice());
        dishInfo.setDishOriginalUnitPrice(dineInItemDTO.getOriginalPrice());
        dishInfo.setProductItemNum(dineInItemDTO.getIsGoodsReduceDiscount());
        dishInfo.setDishSellUnitPrice(dineInItemDTO.getPrice());
        if (BigDecimalUtil.greaterEqual(dineInItemDTO.getItemPrice(), dineInItemDTO.getTotalDiscountFee())) {
            dishInfo.setPayPrice(dineInItemDTO.getItemPrice().subtract(dineInItemDTO.getTotalDiscountFee()));
        } else {
            dishInfo.setPayPrice(BigDecimal.ZERO);
        }
        if (Objects.equals(ItemPriceChangeEnum.PRICE_CHANGE.getCode(), dineInItemDTO.getPriceChangeType())) {
            dishInfo.setDishMemberPrice(dishInfo.getPayPrice());
        } else {
            dishInfo.setDishMemberPrice(dineInItemDTO.getMemberPrice());
        }
        return dishInfo;

    }

    public static List<RequestDishInfo> dineInItem2DishListByDiscount(List<DineInItemDTO> dineInItemDTOS) {
        List<RequestDishInfo> requestDishInfos = Lists.newArrayList();
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            requestDishInfos.add(dineInItem2DishByDiscount(dineInItemDTO));
        }
        return requestDishInfos;
    }

    private static RequestDishInfo dineInItem2DishByDiscount(DineInItemDTO dineInItemDTO) {
        RequestDishInfo requestDishInfo = new RequestDishInfo();
        requestDishInfo.setOrderItemGuid(dineInItemDTO.getGuid());
        requestDishInfo.setDishGuid(dineInItemDTO.getItemGuid());
        requestDishInfo.setDishName(dineInItemDTO.getItemName());
        requestDishInfo.setDishSpecification(dineInItemDTO.getSkuGuid());
        requestDishInfo.setDishUnit(dineInItemDTO.getUnit());
        requestDishInfo.setDishNum(dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount()));
        requestDishInfo.setGiftDishNum(dineInItemDTO.getFreeCount());
        requestDishInfo.setMainGoodGuid(null);
        requestDishInfo.setIsMainGood(1);
        requestDishInfo.setSurcharge(BigDecimal.ZERO);
        requestDishInfo.setRemark(dineInItemDTO.getRemark());
        requestDishInfo.setDishType(dineInItemDTO.getItemType());
        requestDishInfo.setSubtotal(dineInItemDTO.getItemPrice());
        requestDishInfo.setDishOriginalUnitPrice(dineInItemDTO.getOriginalPrice());
        requestDishInfo.setDishSellUnitPrice(dineInItemDTO.getPrice());
        requestDishInfo.setPayPrice(dineInItemDTO.getDiscountTotalPrice());
        if (StringUtils.isNotEmpty(dineInItemDTO.getCouponCode())) {
            requestDishInfo.setPayPrice(BigDecimal.ZERO);
        }
        if (Objects.equals(ItemPriceChangeEnum.PRICE_CHANGE.getCode(), dineInItemDTO.getPriceChangeType())) {
            requestDishInfo.setDishMemberPrice(requestDishInfo.getPayPrice());
        } else {
            requestDishInfo.setDishMemberPrice(dineInItemDTO.getMemberPrice());
        }
        return requestDishInfo;
    }

    private final static String JOINT = "-";
    public static String jointDiningTableName(String areaName,String tableName){
        if(StringUtils.isEmpty(areaName) && StringUtils.isEmpty(tableName)){
            return null;
        }
        if(StringUtils.isEmpty(areaName) && StringUtils.isNotEmpty(tableName)){
            return tableName;
        }
        if(StringUtils.isNotEmpty(areaName) && StringUtils.isEmpty(tableName)){
            return areaName;
        }
        return areaName + JOINT + tableName;
    }
}
