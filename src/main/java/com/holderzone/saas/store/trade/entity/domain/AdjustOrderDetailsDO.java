package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 调整单明细
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_adjust_order_details")
public class AdjustOrderDetailsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private Long guid;

    /**
     * 调整单guid
     */
    private Long adjustGuid;

    /**
     * 调整类型(0.数量调整，1.菜品更换)
     */
    private Integer adjustType;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 订单商品明细guid
     */
    private Long orderItemGuid;

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品类别guid
     */
    private String itemTypeGuid;

    /**
     * 商品类别名称
     */
    private String itemTypeName;

    /**
     * 套餐分组guid
     */
    private String subgroupGuid;

    /**
     * 套餐分组名字
     */
    private String subgroupName;

    /**
     * 商品类型(1.套餐主项，2.规格，3.称重，4.单品 )
     */
    private Integer itemType;

    /**
     * 商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂，8.已上菜(已划菜) ，9.预定)
     */
    private Integer itemState;

    /**
     * 商品的规格
     */
    private String skuGuid;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * 商品单价
     */
    private BigDecimal price;

    /**
     * 商品数量
     */
    private BigDecimal currentCount;

    /**
     * 计数单位
     */
    private String unit;

    /**
     * 套餐主项guid
     */
    private Long parentItemGuid;

    /**
     * 子项加价
     */
    private BigDecimal addPrice;

    /**
     * 套餐预设数量
     */
    private BigDecimal packageDefaultCount;

    /**
     * 是否有属性（0：否，1：是）
     */
    private Integer hasAttr;

    /**
     * 操作人guid
     */
    private String createStaffGuid;

    /**
     * 操作人姓名
     */
    private String createStaffName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;


    @TableField(exist = false)
    private BigDecimal mappingCount;


    private BigDecimal takeawayAccountingPrice;

    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;
}
