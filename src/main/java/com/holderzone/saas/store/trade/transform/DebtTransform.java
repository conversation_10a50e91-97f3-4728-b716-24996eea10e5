package com.holderzone.saas.store.trade.transform;

import com.holderzone.saas.store.dto.trade.DebtUnitSaveReqDTO;
import com.holderzone.saas.store.trade.entity.domain.DebtUnitDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 挂账功能转换接口
 *
 * <AUTHOR>
 * @since 2020-12-15
 */
@Mapper
public interface DebtTransform {

    DebtTransform INSTANCE = Mappers.getMapper(DebtTransform.class);

    DebtUnitDO debtUnitReq2DO(DebtUnitSaveReqDTO reqDTO);
}
