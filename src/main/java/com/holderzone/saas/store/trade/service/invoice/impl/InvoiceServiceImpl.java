package com.holderzone.saas.store.trade.service.invoice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.invoice.RequestGenerateInvoiceDTO;
import com.holderzone.saas.store.dto.invoice.ipass.IpassGenerateOrderInvoiceDTO;
import com.holderzone.saas.store.dto.invoice.ipass.IpassOrderItemsDTO;
import com.holderzone.saas.store.enums.trade.CourtInvoiceTypeEnum;
import com.holderzone.saas.store.enums.trade.TaxRateTypeEnum;
import com.holderzone.saas.store.trade.config.IPassInvoiceConfig;
import com.holderzone.saas.store.trade.service.invoice.InvoiceService;
import com.holderzone.saas.store.trade.utils.HttpsClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {

    @Resource
    private IPassInvoiceConfig iPassInvoiceConfig;

    @Override
    public String generateOrderInvoice(RequestGenerateInvoiceDTO requestAuthenticationDTO) {
        log.info("调用生成订单发票二维码入参={}", JacksonUtils.writeValueAsString(requestAuthenticationDTO));

        if (StringUtils.isEmpty(requestAuthenticationDTO.getAccount())) {
            return null;
        }
        try {
            requestAuthenticationDTO.setCallbackUrl(iPassInvoiceConfig.getOrderInvoiceCallbackUrl());
            IpassGenerateOrderInvoiceDTO generateOrderInvoiceDTO = getIpassGenerateOrderInvoiceDTO(requestAuthenticationDTO);

            log.info("生成订单发票二维码参数={}", JSON.toJSONString(generateOrderInvoiceDTO));
            //生成认证二维码
            String response = HttpsClientUtils.doPost(iPassInvoiceConfig.getOrderInvoiceUrl(), JacksonUtils.writeValueAsString(generateOrderInvoiceDTO));

            log.info("生成订单发票二维码返回参数={}", response);

            JSONObject jsonObject = JSON.parseObject(response);

            String code = jsonObject.get(IPassInvoiceConfig.CODE).toString();
            if (!code.equals(IPassInvoiceConfig.SUCCESS_CODE)) {
                String message = jsonObject.get(IPassInvoiceConfig.MESSAGE).toString();
                log.info("生成订单发票二维码异常={}", message);
            } else {
                String data = jsonObject.get(IPassInvoiceConfig.DATA).toString();
                log.info("订单号={} 发票二维码={}", requestAuthenticationDTO.getOrderNo(), data);
                return data;
            }
        } catch (Exception e) {
            log.info("生成订单发票二维码调用异常={}", e.getMessage());
        }
        return null;
    }

    private static IpassGenerateOrderInvoiceDTO getIpassGenerateOrderInvoiceDTO(RequestGenerateInvoiceDTO requestAuthenticationDTO) {
        IpassGenerateOrderInvoiceDTO generateOrderInvoiceDTO = new IpassGenerateOrderInvoiceDTO();
        generateOrderInvoiceDTO.setStoreId(requestAuthenticationDTO.getStoreGuid());
        generateOrderInvoiceDTO.setAccount(requestAuthenticationDTO.getAccount());
        String timestampStr = Long.toString(System.currentTimeMillis());
        String last6Digits = timestampStr.substring(timestampStr.length() - 6);
        generateOrderInvoiceDTO.setOrderNo(requestAuthenticationDTO.getOrderNo() + "-" + UserContextUtils.getEnterpriseGuid() + "-" + last6Digits);
        generateOrderInvoiceDTO.setCallbackUrl(requestAuthenticationDTO.getCallbackUrl());

        generateOrderInvoiceDTO.setKpr(requestAuthenticationDTO.getAccountName());
        generateOrderInvoiceDTO.setSqr(requestAuthenticationDTO.getAccountName());

        generateOrderInvoiceDTO.setJshj(String.valueOf(requestAuthenticationDTO.getOrderAmount()));
        generateOrderInvoiceDTO.setFplxdm(CourtInvoiceTypeEnum.ORDINARY_INVOICE_TYPE.getCode());

        List<IpassOrderItemsDTO> ipassOrderItemsDTOS = getIpassOrderItemsDTOS(requestAuthenticationDTO);

        generateOrderInvoiceDTO.setOrderItems(ipassOrderItemsDTOS);
        return generateOrderInvoiceDTO;
    }

    private static List<IpassOrderItemsDTO> getIpassOrderItemsDTOS(RequestGenerateInvoiceDTO requestAuthenticationDTO) {
        List<IpassOrderItemsDTO> ipassOrderItemsDTOS = new ArrayList<>();
        IpassOrderItemsDTO ipassOrderItemsDTO = new IpassOrderItemsDTO();

        ipassOrderItemsDTO.setDj(String.valueOf(requestAuthenticationDTO.getOrderAmount()));
        ipassOrderItemsDTO.setJe(String.valueOf(requestAuthenticationDTO.getOrderAmount()));
        ipassOrderItemsDTO.setLslbs(String.valueOf(TaxRateTypeEnum.NORMAL_TAX_RATE.getCode()));
        ipassOrderItemsDTO.setSl("1");
        ipassOrderItemsDTO.setSpbm(IPassInvoiceConfig.CATERING_CODE);
        ipassOrderItemsDTO.setSpmc(IPassInvoiceConfig.CATERING_NAME);
        ipassOrderItemsDTOS.add(ipassOrderItemsDTO);
        return ipassOrderItemsDTOS;
    }
}
