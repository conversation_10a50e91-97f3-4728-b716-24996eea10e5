package com.holderzone.saas.store.trade.strategy;

import com.holderzone.saas.store.trade.bo.StockErpBO;


/**
 * 库存扣减策略接口
 */
public interface StockReduceStrategy {

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 是否支持
     */
    boolean isSupport();

    /**
     * 执行库存扣减
     */
    void reduceStock(StockErpBO stockErpBO);

    /**
     * 执行库存退回
     */
    void returnStock(StockErpBO stockErpBO);

    /**
     * 执行库存调整
     */
    void adjustStock(StockErpBO stockErpBO);
}