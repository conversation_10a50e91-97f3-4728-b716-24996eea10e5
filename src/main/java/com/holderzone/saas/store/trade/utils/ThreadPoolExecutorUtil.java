package com.holderzone.saas.store.trade.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.stereotype.Component;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ThreadPoolExecutorUtil
 * @date 2018/07/26 下午 17:58
 * @description
 * @program holder-saas-store-trade
 */
@Component
public class ThreadPoolExecutorUtil {

    private ExecutorService executorService = new ThreadPoolExecutor(5, 20,
            5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(20),
            new ThreadFactoryBuilder().setNameFormat("cache-pool-%d").build());

    public void cache() {
        executorService.submit(() -> System.out.println("执行"));
    }
}
