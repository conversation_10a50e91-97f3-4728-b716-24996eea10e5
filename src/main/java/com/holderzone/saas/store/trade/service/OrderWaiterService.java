package com.holderzone.saas.store.trade.service;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.order.request.waiter.*;
import com.holderzone.saas.store.dto.trade.OrderDTO;

import java.util.List;
/**
 * <AUTHOR> R
 * @date 2020/11/18 18:02
 * @description
 */
public interface OrderWaiterService {
    /***
     * 录入服务员
     * @param orderWaiterReqDTO 请参数
     * @return true or false
     */
    Boolean addOrderWaiter(OrderWaiterReqDTO orderWaiterReqDTO);
    /***
     *  订单guid查询服务员信息
     * @param orderGuid 订单Guid
     * @return 返回DTO
     */
    List<OrderWaiterInfoDTO> getOrderWaiter(String orderGuid);
    /***
     *  校验订单录入服务员信息
     * @param orderGuid 订单Guid
     * @return 返回DTO
     */
    Boolean checkInputWaiter(String orderGuid);

    /***
     * 分页查询服务员
     * @param orderWaiterPageReqDTO 请求参数
     * @return 返回参数
     */
    Page<OrderWaiterPageRespDTO> pageOrderWaiter(OrderWaiterPageReqDTO orderWaiterPageReqDTO);

    /***
     * 分页查询服务员明细
     * @param orderWaiterPageDetailsReqDTO 请求参数
     * @return 返回参数
    */
    Page<OrderWaiterPageDetailsRespDTO> pageOrderWaiterDetails(OrderWaiterPageDetailsReqDTO orderWaiterPageDetailsReqDTO);

    /***
     *  订单服务员信息更新
     * @param orderDTO 订单
     * @return
     */
    Boolean updateOrderWaiterFee(OrderDTO orderDTO);

    /***
     *  分页查询订单服务员 汇总信息
     * @param orderWaiterPageDetailsReqDTO 请求参数
     * @return
     */
    Page<OrderWaiterPageTotalRespDTO> pageOrderWaiterTotal(OrderWaiterPageDetailsReqDTO orderWaiterPageDetailsReqDTO);

    /***
     *  分页查询订单服务员 后台补录列表
     * @param reqDTO 请求参数
     * @return
     */
    Page<OrderWaiterPageMakeUpRespDTO> pageOrderWaiterMakeUp(OrderWaiterMakeUpPageReqDTO reqDTO);

    /***
     *  订单补录 后台补录  支持批量单个
     * @param makeUpReqDTO 请求参数
     * @return
     */
    Boolean orderWaiterMakeUp(OrderWaiterMakeUpReqDTO makeUpReqDTO);
}