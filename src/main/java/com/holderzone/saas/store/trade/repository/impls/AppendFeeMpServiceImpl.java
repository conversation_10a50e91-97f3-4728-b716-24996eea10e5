package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.trade.entity.domain.AppendFeeDO;
import com.holderzone.saas.store.trade.mapper.AppendFeeMapper;
import com.holderzone.saas.store.trade.repository.interfaces.AppendFeeMpService;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 附加费记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-22
 */
@Service
public class AppendFeeMpServiceImpl extends ServiceImpl<AppendFeeMapper, AppendFeeDO> implements AppendFeeMpService {

    @Override
    public List<AppendFeeDO> listByOrderGuid(Serializable orderGuid) {
        return list(new LambdaQueryWrapper<AppendFeeDO>().eq(AppendFeeDO::getOrderGuid, orderGuid));

    }

    @Override
    public List<AppendFeeDO> listByOrderGuids(Collection<? extends Serializable> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<AppendFeeDO>().in(AppendFeeDO::getOrderGuid, idList));
    }

    @Override
    public void updateRefundAmountByOrderGuid(Serializable orderGuid, AppendFeeDO appendFeeDO) {
        UpdateWrapper<AppendFeeDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(AppendFeeDO::getOrderGuid, orderGuid);
        uw.lambda().eq(AppendFeeDO::getType, appendFeeDO.getType());
        uw.lambda().eq(AppendFeeDO::getName, appendFeeDO.getName());
        if (Objects.nonNull(appendFeeDO.getRefundShareAmount())) {
            uw.setSql("refund_amount = refund_amount + " + appendFeeDO.getRefundShareAmount());
        } else {
            uw.setSql("refund_amount = refund_amount + " + appendFeeDO.getAmount());
        }
        uw.setSql("refund_count = refund_count + " + appendFeeDO.getRefundCount());
        update(uw);
    }

    @Override
    public void deleteByOrderGuid(Serializable orderGuid) {
        remove(new LambdaQueryWrapper<AppendFeeDO>().eq(AppendFeeDO::getOrderGuid, orderGuid));
    }

    @Override
    public void deleteByOrderGuids(List<String> orderGuids) {
        if (CollectionUtil.isEmpty(orderGuids)) {
            return;
        }
        remove(new LambdaQueryWrapper<AppendFeeDO>().in(AppendFeeDO::getOrderGuid, orderGuids));
    }
}
