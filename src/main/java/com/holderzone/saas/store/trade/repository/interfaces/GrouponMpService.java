package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.trade.GrouponOrderDTO;
import com.holderzone.saas.store.trade.anno.PerformanceCheck;
import com.holderzone.saas.store.trade.entity.domain.GrouponDO;

import java.util.List;

/**
 * <p>
 * 团购券 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-10
 */
public interface GrouponMpService extends IService<GrouponDO> {

    List<GrouponDO> listForGeGuid(Long guid);

    List<GrouponDO> listByCodes(String orderGuid, List<String> codes);

    List<GrouponOrderDTO> useGrouponTotalAmountByOrderGuids(List<String> orderGuids);

    @PerformanceCheck
    List<GrouponDO> listByOrderGuid(String orderGuid);

    List<GrouponDO> listByOrderGuids(List<String> orderGuids);

    List<GrouponDO> listByRefundOrderGuid(String refundOrderGuid);

    List<AmountItemDTO> listByRequest(DailyReqDTO request);

    List<AmountItemDTO> listByRequestGroupByName(DailyReqDTO request);

    GrouponDO getByCodeAndOrderGuid(String couponCode, String orderGuid);

    List<GrouponDO> listByCodeAndOrderGuid(String couponCode, String orderGuid);

    void appendRefundOrderGuid(Long orderGuid, Long refundOrderGuid);

    List<AmountItemDTO> listRefundByRequest(StoreGatherReportReqDTO request);
}
