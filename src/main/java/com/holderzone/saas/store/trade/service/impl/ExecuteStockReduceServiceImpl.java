package com.holderzone.saas.store.trade.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.MdcContextUtils;
import com.holderzone.feign.spring.boot.util.TraceContextUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseSupplyChainConfigDTO;
import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.enums.trade.StockExecuteOperateEnum;
import com.holderzone.saas.store.enums.trade.StockReduceStrategyEnum;
import com.holderzone.saas.store.trade.bo.StockErpBO;
import com.holderzone.saas.store.trade.bo.builder.StockErpBizBuilder;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.repository.feign.EnterpriseClientService;
import com.holderzone.saas.store.trade.service.ExecuteStockReduceService;
import com.holderzone.saas.store.trade.strategy.StockReduceStrategy;
import com.holderzone.saas.store.trade.strategy.StockReduceStrategyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExecuteStockReduceServiceImpl implements ExecuteStockReduceService {

    private final StockReduceStrategyFactory stockReduceStrategyFactory;

    private final DynamicHelper dynamicHelper;

    private final EnterpriseClientService enterpriseClientService;

    @Resource(name = "stockReduceThreadPool")
    private ExecutorService stockReduceThreadPool;

    /**
     * 执行库存扣减
     *
     * @param items     商品列表
     * @param orderGuid 订单号
     * @param orderDO   订单信息
     */
    @Override
    public void executeStockReduce(List<DineInItemDTO> items, String orderGuid, OrderDO orderDO) {
        // 构建BO
        StockErpBO stockErpBO = StockErpBizBuilder.build(StockExecuteOperateEnum.REDUCE.getCode(), items, orderGuid, orderDO);
        execute(stockErpBO);
    }

    @Override
    public void executeStockReturn(List<DineInItemDTO> items, String orderGuid, OrderDO orderDO) {
        // 构建BO
        StockErpBO stockErpBO = StockErpBizBuilder.build(StockExecuteOperateEnum.RETURN.getCode(), items, orderGuid, orderDO);
        execute(stockErpBO);
    }

    @Override
    public void executeStockAdjust(DepositErpSyncDTO depositErpSyncDTO) {
        // 构建BO
        StockErpBO stockErpBO = StockErpBizBuilder.build(depositErpSyncDTO);
        execute(stockErpBO);
    }

    private void execute(StockErpBO stockErpBO) {
        log.info("执行库存扣减/退回策略入参:{}", JacksonUtils.writeValueAsString(stockErpBO));
        // 获取所有可用的库存扣减策略并执行
        List<StockReduceStrategy> strategies = stockReduceStrategyFactory.getAllStrategies();
        log.info("当前所有策略:{}", JacksonUtils.writeValueAsString(strategies.stream()
                .map(StockReduceStrategy::getStrategyName).collect(Collectors.toList())));
        // 过滤策略
        strategies = filterStockReduceStrategy(strategies, stockErpBO);
        log.info("过滤之后可执行策略:{}", JacksonUtils.writeValueAsString(strategies.stream()
                .map(StockReduceStrategy::getStrategyName).collect(Collectors.toList())));
        UserContext userContext = UserContextUtils.get();
        String traceId = TraceContextUtils.getTraceId();
        // 异步执行所有策略，不等待完成
        strategies.forEach(strategy ->
                stockReduceThreadPool.execute(() -> {
                    try {
                        UserContextUtils.put(userContext);
                        TraceContextUtils.setTraceId(traceId);
                        MdcContextUtils.fillByPreContext();
                        dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
                        StockExecuteOperateEnum executeOperateEnum = StockExecuteOperateEnum.getByCode(stockErpBO.getOperateCode());
                        switch (executeOperateEnum) {
                            case REDUCE:
                                strategy.reduceStock(stockErpBO);
                                break;
                            case RETURN:
                                strategy.returnStock(stockErpBO);
                                break;
                            case ADJUST:
                                strategy.adjustStock(stockErpBO);
                                break;
                            default:
                                throw new BusinessException("not support operate:" + stockErpBO.getOperateCode());
                        }
                        TraceContextUtils.remove();
                        UserContextUtils.remove();
                    } catch (Exception e) {
                        log.error("执行库存策略[{}]失败: {}", strategy.getStrategyName(), e.getMessage(), e);
                        TraceContextUtils.remove();
                        UserContextUtils.remove();
                    }
                })
        );
    }

    /**
     * 过滤策略
     */
    private List<StockReduceStrategy> filterStockReduceStrategy(List<StockReduceStrategy> strategies, StockErpBO stockErpBO) {
        if (CollectionUtils.isEmpty(strategies)) {
            return Lists.newArrayList();
        }
        // 查询企业微海erp配置
        EnterpriseSupplyChainConfigDTO enterpriseSupplyChainConfigDTO = queryWeihaiErpEnable();
        stockErpBO.setEnterpriseSupplyChainConfigDTO(enterpriseSupplyChainConfigDTO);
        boolean weihaiErpEnable = Objects.nonNull(enterpriseSupplyChainConfigDTO);
        // 暂时只有微海和holder ERP
        return strategies.stream()
                .filter(e -> weihaiErpEnable ? !StockReduceStrategyEnum.MDM.getCode().equals(e.getStrategyName())
                        : !StockReduceStrategyEnum.WEIHAI_ERP.getCode().equals(e.getStrategyName()))
                .collect(Collectors.toList());
    }

    private EnterpriseSupplyChainConfigDTO queryWeihaiErpEnable() {
        // 查询当前企业配置
        EnterpriseQueryDTO enterpriseQueryDTO = new EnterpriseQueryDTO();
        enterpriseQueryDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        EnterpriseDTO enterprise = enterpriseClientService.findEnterprise(enterpriseQueryDTO);
        log.info("当前企业信息, enterprise:{}", JacksonUtils.writeValueAsString(enterprise));
        Boolean supportWeiHaiSupplyChain = enterprise.getSupportWeiHaiSupplyChain();
        if (!Boolean.TRUE.equals(supportWeiHaiSupplyChain)) {
            return null;
        }
        EnterpriseSupplyChainConfigDTO supplyChainConfig = enterprise.getSupplyChainConfig();
        if (Objects.isNull(supplyChainConfig) || StringUtils.isEmpty(supplyChainConfig.getYicanAppId())) {
            log.error("微海供应链未配置,enterpriseGuid:{}", UserContextUtils.getEnterpriseGuid());
            return null;
        }
        return supplyChainConfig;
    }

}
