package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.trade.entity.domain.OrderMultiMember;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface OrderMultiMemberMapper extends BaseMapper<OrderMultiMember> {

    void updateBatchPayAmount(@Param("orderMultiMembers") List<OrderMultiMember> orderMultiMembers);
}
