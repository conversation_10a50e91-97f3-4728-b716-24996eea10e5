package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.saas.store.dto.order.response.bill.DiscountRuleDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单优惠记录
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_discount")
public class DiscountDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 订单商品guid
     */
    private Long orderItemGuid;

    /**
     * 优惠券guid  或者优惠活动GUID
     */
    private String couponsGuid;

    /**
     * 折扣方式名字
     */
    private String discountName;

    /**
     * {@link DiscountTypeEnum}
     * '1-会员折扣，2-整单折扣，3-整单让价,4-系统省零，5-赠送优惠，6-团购验券，7-会员优惠券，8-积分抵扣'
     */
    private Integer discountType;

    /**
     * 打几折（整单折扣和会员折扣）
     */
    private BigDecimal discount;

    /**
     * 系统省零规则（DiscountRuleBO）
     * @see DiscountRuleDTO
     */
    private String rule;

    /**
     * 折扣总额
     */
    private BigDecimal discountFee;

    /**
     * 折扣状态 0表示正常，1表示是反结账折扣
     */
    private Integer discountState;

    /**
     * 操作人guid
     */
    private String staffGuid;

    /**
     * 操作人姓名
     */
    private String staffName;


}
