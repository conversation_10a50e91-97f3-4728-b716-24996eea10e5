package com.holderzone.saas.store.trade.utils.local;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.saas.store.dto.trade.ItemAttrDTO;
import com.holderzone.saas.store.trade.entity.domain.ItemAttrDO;
import com.holderzone.saas.store.trade.repository.interfaces.ItemAttrService;
import com.holderzone.saas.store.trade.transform.LocalTransform;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class CallableForAttr implements Callable<List<ItemAttrDTO>> {

    //入参
    private List<Long> itemGuids;

    private ItemAttrService itemAttrService;

    private LocalTransform localTransform;

    List<ItemAttrDTO> data = new ArrayList<>();

    private final CountDownLatch latch;

    private UserContext userContext;


    public CallableForAttr(List<Long> itemGuids, ItemAttrService itemAttrService,
                           LocalTransform localTransform, CountDownLatch latch, UserContext userContext) {
        this.userContext = userContext;
        this.itemGuids = itemGuids;
        this.itemAttrService = itemAttrService;
        this.localTransform = localTransform;
        this.latch = latch;
    }

    @Override
    public List<ItemAttrDTO> call() throws Exception {
        try {
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            log.info("查询属性入参：{}", itemGuids);
            List<ItemAttrDO> attrLists;
            if (!ObjectUtils.isEmpty(itemGuids)) {
                attrLists = itemAttrService.listByItemGuids(itemGuids);
                data = localTransform.itemAttrDOS2itemAttrDTOS(attrLists);
            } else {
                data = Collections.emptyList();
            }
            log.info("查询属性结束");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            latch.countDown();
        }
        return data;
    }
}
