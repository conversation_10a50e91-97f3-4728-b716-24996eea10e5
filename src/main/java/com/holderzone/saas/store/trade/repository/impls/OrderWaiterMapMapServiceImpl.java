package com.holderzone.saas.store.trade.repository.impls;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.trade.entity.domain.OrderWaiterDO;
import com.holderzone.saas.store.trade.mapper.OrderWaiterMapper;
import com.holderzone.saas.store.trade.repository.interfaces.OrderWaiterMapService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * <AUTHOR> R
 * @date 2020/11/18 17:58
 * @description
 */
@Service
@AllArgsConstructor
@Slf4j
public class OrderWaiterMapMapServiceImpl extends ServiceImpl<OrderWaiterMapper, OrderWaiterDO> implements OrderWaiterMapService {
}