package com.holderzone.saas.store.trade.transform;

import com.holderzone.saas.store.dto.trade.OrderDetailTransactionRecordRespDTO;
import com.holderzone.saas.store.dto.trade.RefundTransactionRecordDetailRespDTO;
import com.holderzone.saas.store.dto.trade.TransactionRecordDTO;
import com.holderzone.saas.store.trade.entity.domain.MultipleTransactionRecordDO;
import com.holderzone.saas.store.trade.entity.domain.TransactionRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <p>
 * 转换交易记录工具
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-10-29
 */
@Mapper
public interface TransactionRecordTransform {

    TransactionRecordTransform INSTANCE = Mappers.getMapper(TransactionRecordTransform.class);

    /**
     * 转换订单交易记录DO 到 订单统计明细中 支付方式DTO
     *
     * @param recordDO 　交易记录DO
     * @return 交易纪律DTO
     */
    OrderDetailTransactionRecordRespDTO do2TransactionRecordRespDTO(TransactionRecordDO recordDO);


    /**
     * 转换订单交易记录DO 到 订单统计明细中 支付方式DTO
     *
     * @param recordDO 　交易记录DO
     * @return 交易纪律DTO
     */
    List<OrderDetailTransactionRecordRespDTO> do2TransactionRecordRespDTO(List<TransactionRecordDO> recordDO);

    /**
     * 交易记录DO 转 退款信息明细DTO
     *
     * @param recordDO 交易记录DO
     * @return 退款信息明细DTO
     */
    @Mappings({
            @Mapping(source = "jhOrderGuid", target = "orderNo"),
            @Mapping(source = "gmtCreate", target = "refundTime")
    })
    RefundTransactionRecordDetailRespDTO do2TransactionRecordDetailRespDTO(TransactionRecordDO recordDO);

    /**
     * 交易记录DO集合 转 退款信息明细DTO集合
     *
     * @param recordDO 交易记录DO
     * @return 退款信息明细DTO
     */
    List<RefundTransactionRecordDetailRespDTO> dos2TransactionRecordDetailsRespDTO(List<TransactionRecordDO> recordDO);

    TransactionRecordDO multipleDO2TransactionRecordDO(MultipleTransactionRecordDO multipleTransactionRecord);

    MultipleTransactionRecordDO transactionRecordDO2multipleDO(TransactionRecordDO transactionRecordDO);

    @Mappings({
            @Mapping(target = "isDelete", ignore = true),
    })
    TransactionRecordDTO multipleRecordDO2TransactionRecordDTO(MultipleTransactionRecordDO recordDO);

    List<TransactionRecordDTO> multipleRecordDO2TransactionRecordDTO(List<MultipleTransactionRecordDO> recordDO);

    List<MultipleTransactionRecordDO> transactionRecordDO2multipleDOList(List<TransactionRecordDO> transactionRecordDOList);
}
