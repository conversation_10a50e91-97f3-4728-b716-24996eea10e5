package com.holderzone.saas.store.trade.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

@Configuration
@Slf4j
public class TreadPoolConfig {

    /**
     * 批量保存订单线程
     *
     * @return
     */
    @Bean(value = "batchOrderThreadPool")
    public ExecutorService batchOrderThreadPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("batch-order-thread-%d").build();

        ExecutorService pool = new CustomThreadPoolExecutor(
                5, 100, 100L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(1500), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

        return pool;
    }


    /**
     * 结账线程
     *
     * @return
     */
    @Bean(value = "checkOutThreadPool")
    public ExecutorService buildCheckOutThreadPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("check-out-thread-%d").build();

        ExecutorService pool = new CustomThreadPoolExecutor(
                5, 100, 100L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(1500), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

        return pool;
    }

    /**
     * 订单结账成功推送MQ消息
     *
     * @return
     */
    @Bean(value = "orderStatusThreadPool")
    public ExecutorService orderStatusThreadPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("order-status-thread-%d").build();

        ExecutorService pool = new CustomThreadPoolExecutor(
                5, 20, 100L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(1500), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

        return pool;
    }

    /**
     * 订单服务员金额
     *
     * @return
     */
    @Bean(value = "orderWaiterOrderFeeThreadPool")
    public ExecutorService orderWaiterOrderFeeThreadPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("order-waiter-fee-thread-%d").build();

        ExecutorService pool = new CustomThreadPoolExecutor(
                5, 20, 100L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(1500), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

        return pool;
    }

    class CustomThreadPoolExecutor extends ThreadPoolExecutor {
        public CustomThreadPoolExecutor(
                int corePoolSize,
                int maximumPoolSize,
                long keepAliveTime,
                TimeUnit unit,
                BlockingQueue<Runnable> workQueue,
                ThreadFactory threadFactory,
                RejectedExecutionHandler handler) {
            super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
        }

        @Override
        public void afterExecute(Runnable r, Throwable t) {
            super.afterExecute(r, t);
            if (t != null) {
                log.error("异步线程异常：{}", t);
            }
            // ... Perform task-specific cleanup actions
        }

        @Override
        public void terminated() {
            super.terminated();
            // ... Perform final clean-up actions
        }
    }

    @Bean(value = "queueInitExecutorService")
    public ExecutorService queueInitExecutorService() {
        return new ThreadPoolExecutor(10, 100, 10L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("queue-init-executor-service-%d").build());
    }

    @Bean(value = "asyncOrderTcdExecutor")
    public ExecutorService asyncOrderTcdExecutor() {
        return new ThreadPoolExecutor(10, 100, 10L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("async-order-tcd-executor-%d").build());
    }

    /**
     * 会员营销线程池
     */
    @Bean(value = "memberMarketingExecutor")
    public ExecutorService memberMarketingExecutor() {
        return new ThreadPoolExecutor(10, 100, 10L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("member-marketing-executor-%d").build());
    }


    /**
     * 参与营销活动订单推送
     */
    @Bean(value = "asyncMemberMarketingOrderExecutor")
    public ExecutorService asyncMemberMarketingOrderExecutor() {
        return new ThreadPoolExecutor(10, 100, 10L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("async-member-marketing-order-executor-%d").build());
    }


    /**
     * erp 库存
     */
    @Bean(value = "erpStockExecutor")
    public ExecutorService erpStockExecutor() {
        return new ThreadPoolExecutor(10, 100, 10L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("erp-stock-executor-%d").build());
    }

//    /**
//     * 事件处理类线程池
//     *
//     * @return
//     */
//    @Bean(value = "eventThreadPool")
//    public ExecutorService buildEventThreadPool() {
//        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
//                .setNameFormat("event-thread-%d").build();
//
//        ExecutorService pool = new ThreadPoolExecutor(10, 100, 3000L, TimeUnit.MILLISECONDS,
//                new ArrayBlockingQueue<>(1500), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());
//
//        return pool;
//    }

    /**
     * 挂账还款 线程
     */
    @Bean(value = "debtRepaymentExecutor")
    public ExecutorService debtRepaymentExecutor() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("debt-repayment-thread-%d").build();
        return new CustomThreadPoolExecutor(
                5, 25, 10L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(500), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());
    }


    /**
     * 交易记录退款 线程
     */
    @Bean(value = "aggTransactionRecordExecutor")
    public ExecutorService aggTransactionRecordExecutor() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("agg-transactionRecord-thread-%d").build();
        return new CustomThreadPoolExecutor(
                5, 25, 10L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(500), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());
    }

    /**
     * order 保存
     */
    @Bean(value = "orderSaveExecutor")
    public ExecutorService orderSaveExecutor() {
        return new ThreadPoolExecutor(10, 100, 10L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("order-save-executor-%d").build());
    }
}