package com.holderzone.saas.store.trade.entity.bo;

import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CombineCalculateBO
 * @date 2019/04/13 17:54
 * @description
 * @program holder-saas-store-trade
 */
@Data
public class CombineCalculateBO {

    @ApiModelProperty(value = "折扣保存")
    List<DiscountDO> saveDiscountDOS;

    @ApiModelProperty(value = "折扣更新")
    List<DiscountDO> updateDiscountDOS;
}
