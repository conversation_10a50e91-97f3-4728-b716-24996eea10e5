package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.order.request.dinein.BatchCreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.BatchFastOfflineOrderResp;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 离线订单操作
 *
 * <AUTHOR>
 * @date 2021/5/27
 **/
public interface DineInOffLineService {

    /**
     * 批量保存离线快餐订单
     *
     * @param foodReqDTO
     * @return 错误的orderNo
     */
    BatchFastOfflineOrderResp batchSaveFastOrder(@RequestBody BatchCreateFastFoodReqDTO foodReqDTO);
}
