package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-26
 * @description
 */
@Component
@Slf4j
public class ConcessionalDiscountHandler extends DiscountHandler {

    @Override
    void dealDiscount(DiscountContext context) {
        // 15.整单让价
        DiscountFeeDetailDTO concessionalDiscount = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap().get
                (type()));
        concessionalDiscount.setDiscountFee(context.getDiscountRuleBO().getConcessional() == null ? BigDecimal.ZERO : context.getDiscountRuleBO()
                .getConcessional());
        context.getDiscountFeeDetailDTOS().add(concessionalDiscount);
        log.info("8.整单让价优惠金额：{}", concessionalDiscount.getDiscountFee());
        handleDiscountTotalPrice(context, concessionalDiscount);
        log.info("8.整单让价优惠菜品金额：{}", JacksonUtils.writeValueAsString(context.getAllItems()));
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.CONCESSIONAL.getCode();
    }


    /**
     * 商品实付金额处理
     */
    private void handleDiscountTotalPrice(DiscountContext context, DiscountFeeDetailDTO concessionalDiscount) {
        log.info("[商品优惠价处理]整单让价优惠信息：{}", JacksonUtils.writeValueAsString(concessionalDiscount));
        List<DineInItemDTO> allItems = context.getAllItems();
        Map<String, DineInItemDTO> itemDTOMap = context.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (v1, v2) -> v1));

        DineinOrderDetailRespDTO orderDetailRespDTO = context.getOrderDetailRespDTO();
        List<DineInItemDTO> calculateItemList = new ArrayList<>(allItems);
        if (BigDecimalUtil.greaterThanZero(orderDetailRespDTO.getAppendFee())) {
            DineInItemDTO appendFeeItem = new DineInItemDTO();
            appendFeeItem.setGuid("-1");
            appendFeeItem.setDiscountTotalPrice(orderDetailRespDTO.getAppendFee()
                    .subtract(Optional.ofNullable(orderDetailRespDTO.getAppendDiscountFee()).orElse(BigDecimal.ZERO)));
            calculateItemList.add(appendFeeItem);
        }
        BigDecimal discountFee = concessionalDiscount.getDiscountFee();
        Map<String, BigDecimal> itemDiscountPriceMap = PriceCalculationUtils.calculationItemDiscountPrice(discountFee, calculateItemList);
        itemDiscountPriceMap.forEach((guid, itemDiscountFee) -> {
            DineInItemDTO dineInItemDTO = itemDTOMap.get(guid);
            if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(itemDiscountFee));
            }
        });
        // 附加费优惠金额
        orderDetailRespDTO.setAppendDiscountFee(Optional.ofNullable(orderDetailRespDTO.getAppendDiscountFee()).orElse(BigDecimal.ZERO)
                .add(itemDiscountPriceMap.getOrDefault("-1", BigDecimal.ZERO)));
    }

}
