package com.holderzone.saas.store.trade.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableWhetherLocked
 * @date 2019/01/17 9:55
 * @description 只做业务锁校验
 * @program holder-saas-store-table
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireTradePayLock {
}
