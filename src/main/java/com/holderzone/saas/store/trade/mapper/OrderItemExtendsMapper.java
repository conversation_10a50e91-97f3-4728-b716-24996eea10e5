package com.holderzone.saas.store.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.trade.entity.domain.OrderItemExtendsDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单商品扩展表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface OrderItemExtendsMapper extends BaseMapper<OrderItemExtendsDO> {

    void restoreOrderItemExtends(@Param("orderItemGuidList") List<Long> orderItemGuidList);
}
