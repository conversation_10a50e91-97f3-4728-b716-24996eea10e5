package com.holderzone.saas.store.trade.strategy.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseSupplyChainConfigDTO;
import com.holderzone.saas.store.dto.deposit.req.DepositDish;
import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.mdm.MDMResult;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.PushOrderBillsBO;
import com.holderzone.saas.store.dto.order.common.SalesOrderDisheseBO;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.trade.bo.StockErpBO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.dto.weihai.WeihaiCommonResponse;
import com.holderzone.saas.store.dto.weihai.WeihaiSaleOutRequest;
import com.holderzone.saas.store.enums.trade.StockReduceStrategyEnum;
import com.holderzone.saas.store.trade.repository.feign.EnterpriseClientService;
import com.holderzone.saas.store.trade.repository.feign.ItemClientService;
import com.holderzone.saas.store.trade.repository.feign.MdmServiceClient;
import com.holderzone.saas.store.trade.strategy.StockReduceStrategy;
import com.holderzone.saas.store.trade.utils.ItemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 微海供应链库存扣减策略实现
 */
@Slf4j
@Component
public class WeihaiStockReduceStrategy implements StockReduceStrategy {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";

    private static final Integer SUCCESS_CODE = 200;

    @Resource
    private ItemClientService itemClientService;

    @Resource
    private EnterpriseClientService enterpriseClientService;

    @Resource
    private MdmServiceClient mdmServiceClient;

    @Resource
    @Qualifier(value = "weihaiRestTemplate")
    private RestTemplate weihaiRestTemplate;

    @Value("${weihai.erp.host}")
    private String weihaiErpHost;

    private static final String NOT_EXIST_CONFIG_TIPS = "微海供应链未配置,enterpriseGuid:{}";

    private static final String ORDER_REQUEST_URI = "/api/cost-open/openApi/powerHolder/order/sale/out";

    @Override
    public String getStrategyName() {
        return StockReduceStrategyEnum.WEIHAI_ERP.getCode();
    }

    @Override
    public boolean isSupport() {
        return true;
    }

    @Override
    public void reduceStock(StockErpBO stockErpBO) {
        List<DineInItemDTO> items = stockErpBO.getItems();
        String orderGuid = stockErpBO.getOrderGuid();
        OrderDO orderDO = stockErpBO.getOrderDO();
        EnterpriseSupplyChainConfigDTO supplyChainConfig = stockErpBO.getEnterpriseSupplyChainConfigDTO();
        try {
            if (Objects.isNull(supplyChainConfig)) {
                log.error(NOT_EXIST_CONFIG_TIPS, UserContextUtils.getEnterpriseGuid());
                return;
            }
            // 构建请求参数
            WeihaiSaleOutRequest request = buildSaleOutRequest(items, orderDO, false);

            String unique = IDUtils.nextId();
            String sign = sign(request, supplyChainConfig.getYicanAppSecret(), unique);

            log.info("调用微海供应链扣减库存：订单guid：{}，appId:{}, unique:{}, sign:{}, 入参{}",
                    orderGuid,
                    supplyChainConfig.getYicanAppId(),
                    unique,
                    sign,
                    JacksonUtils.writeValueAsString(request));
            WeihaiCommonResponse<?> response = request(supplyChainConfig, weihaiErpHost + ORDER_REQUEST_URI,
                    unique, sign, request);
            log.info("调用微海供应链扣减库存：返回{}", response);
        } catch (Exception e) {
            log.error("调用微海供应链扣减库存异常：{}", e.getMessage(), e);
        }
    }

    @Override
    public void returnStock(StockErpBO stockErpBO) {
        List<DineInItemDTO> items = stockErpBO.getItems();
        String orderGuid = stockErpBO.getOrderGuid();
        OrderDO orderDO = stockErpBO.getOrderDO();
        EnterpriseSupplyChainConfigDTO supplyChainConfig = stockErpBO.getEnterpriseSupplyChainConfigDTO();
        try {
            if (Objects.isNull(supplyChainConfig)) {
                log.error(NOT_EXIST_CONFIG_TIPS, UserContextUtils.getEnterpriseGuid());
                return;
            }
            // 构建请求参数
            WeihaiSaleOutRequest request = buildSaleOutRequest(items, orderDO, true);

            String unique = IDUtils.nextId();
            String sign = sign(request, supplyChainConfig.getYicanAppSecret(), unique);

            log.info("调用微海供应链退回库存：订单guid：{}，appId:{}, unique:{}, sign:{}, 入参{}",
                    orderGuid,
                    supplyChainConfig.getYicanAppId(),
                    unique,
                    sign,
                    JacksonUtils.writeValueAsString(request));
            WeihaiCommonResponse<?> response = request(supplyChainConfig, weihaiErpHost + ORDER_REQUEST_URI,
                    unique, sign, request);
            log.info("调用微海供应链退回库存：返回{}", response);
        } catch (Exception e) {
            log.error("调用微海供应链退回库存异常：{}", e.getMessage(), e);
        }
    }

    @Override
    public void adjustStock(StockErpBO stockErpBO) {
        EnterpriseSupplyChainConfigDTO supplyChainConfig = stockErpBO.getEnterpriseSupplyChainConfigDTO();
        try {
            if (Objects.isNull(supplyChainConfig)) {
                log.error(NOT_EXIST_CONFIG_TIPS, UserContextUtils.getEnterpriseGuid());
                return;
            }
            DepositErpSyncDTO depositErpSyncDTO = stockErpBO.getDepositErpSyncDTO();
            String orderGuid = depositErpSyncDTO.getOrderGuid();
            // 构建请求参数
            WeihaiSaleOutRequest request = buildSaleOutRequest(depositErpSyncDTO);

            String unique = IDUtils.nextId();
            String sign = sign(request, supplyChainConfig.getYicanAppSecret(), unique);

            log.info("调用微海供应链调整库存：订单guid：{}，appId:{}, unique:{}, sign:{}, 入参{}",
                    orderGuid,
                    supplyChainConfig.getYicanAppId(),
                    unique,
                    sign,
                    JacksonUtils.writeValueAsString(request));
            WeihaiCommonResponse<?> response = request(supplyChainConfig, weihaiErpHost + ORDER_REQUEST_URI,
                    unique, sign, request);
            log.info("调用微海供应链调整库存：返回{}", response);
        } catch (Exception e) {
            log.error("调用微海供应链调整库存异常：{}", e.getMessage(), e);
        }
    }

    private WeihaiCommonResponse<?> request(EnterpriseSupplyChainConfigDTO supplyChainConfig,
                                            String url, String unique, String sign,
                                            WeihaiSaleOutRequest request) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        // 添加签名相关的头信息
        httpHeaders.add("appId", supplyChainConfig.getYicanAppId());
        httpHeaders.add("unique", unique);
        httpHeaders.add("Authorization", sign);

        HttpEntity<Object> httpEntity = new HttpEntity<>(JacksonUtils.writeValueAsString(request), httpHeaders);
        ResponseEntity<WeihaiCommonResponse<?>> exchange;
        try {
            log.info("请求入参 -> url:{},httpHeaders:{},data:{}", url,
                    JacksonUtils.writeValueAsString(httpHeaders.toSingleValueMap()),
                    JacksonUtils.writeValueAsString(request));
            ParameterizedTypeReference<WeihaiCommonResponse<?>> typeRef = new ParameterizedTypeReference<WeihaiCommonResponse<?>>() {
            };
            exchange = weihaiRestTemplate.exchange(url, HttpMethod.POST,
                    httpEntity, typeRef);
            log.info("请求返回 -> Http StatusCode: {}, rsp body:{}", exchange.getStatusCode(),
                    JacksonUtils.writeValueAsString(exchange.getBody()));
        } catch (Exception e) {
            log.error("出现异常情况 ->  req body：{}, exception：{}", JacksonUtils.writeValueAsString(httpEntity.getBody()),
                    e.getMessage(), e);
            throw new BusinessException("请求微海异常");
        }
        if (exchange.getStatusCodeValue() != HttpStatus.OK.value()
                || (exchange.getBody() != null && !SUCCESS_CODE.equals(exchange.getBody().getStatus()))) {
            log.error("请求微海返回错误 ->  url: {}, data: {}，HttpStatus：{}，rsp body：{}",
                    url, JacksonUtils.writeValueAsString(request), exchange.getStatusCode(), JacksonUtils.writeValueAsString(exchange.getBody()));
            throw new BusinessException("请求微海异常");
        }
        return exchange.getBody();
    }

    /**
     * 构建销售出库请求参数
     */
    private WeihaiSaleOutRequest buildSaleOutRequest(List<DineInItemDTO> items, OrderDO orderDO, boolean isRecovery) {
        // 构建请求
        PushOrderBillsBO pushOrderBillsBO = ItemUtil.combineMdmMessage(
                orderDO,
                items,
                isRecovery,
                itemClientService::getErpSkuGuids
        );
        if (isRecovery) {
            // 反结账，更新金额和数量
            pushOrderBillsBO.setPaymentDiscountTotal(BigDecimal.ZERO);
            pushOrderBillsBO.setCheckTotal(BigDecimal.ZERO);
            pushOrderBillsBO.setConsumeTotal(BigDecimal.ZERO);
            // 清0
            pushOrderBillsBO.getSalesOrderDisheses().forEach(e -> {
                e.setCheckCount(BigDecimal.ZERO);
                e.setItemPrice(BigDecimal.ZERO);
                e.setPrice(BigDecimal.ZERO);
                e.setDiscountTotalPrice(BigDecimal.ZERO);
                e.setPracticeSubTotal(BigDecimal.ZERO);
            });
        }
        log.info("pushOrderBillsBO:{}", JacksonUtils.writeValueAsString(pushOrderBillsBO));
        return transferSaleOutRequest(pushOrderBillsBO);
    }

    /**
     * 构建销售出库请求参数
     */
    private WeihaiSaleOutRequest buildSaleOutRequest(DepositErpSyncDTO depositErpSyncDTO) {
        // 构建请求
        return transferSaleOutRequest(depositErpSyncDTO);
    }

    private WeihaiSaleOutRequest transferSaleOutRequest(PushOrderBillsBO pushOrderBillsBO) {
        // 构建请求对象
        WeihaiSaleOutRequest request = new WeihaiSaleOutRequest();
        if (StringUtils.isEmpty(pushOrderBillsBO.getDiningTableName())) {
            pushOrderBillsBO.setDiningTableName("快餐-快餐");
        }
        String[] diningTableNameSplit = pushOrderBillsBO.getDiningTableName().split("-");
        request.setAreaName(diningTableNameSplit[0]);
        request.setBillingType("堂食");
        request.setBusinessDate(pushOrderBillsBO.getBusinessDay().format(DATE_FORMATTER));
        request.setDeskName(diningTableNameSplit[1]);
        request.setDiscountAmount(pushOrderBillsBO.getPaymentDiscountTotal());
        request.setFlowAmount(pushOrderBillsBO.getCheckTotal());
        request.setIsAdjustOrder(0);
        request.setLcDepartId(transferLcDepartId(pushOrderBillsBO.getStoreId()));
        request.setOpenTime(pushOrderBillsBO.getCreateTime().format(DATETIME_FORMATTER));
        request.setOrderNo(pushOrderBillsBO.getSalesOrderId());
        request.setPaidAmount(pushOrderBillsBO.getConsumeTotal());
        request.setPassengerFlow(Optional.ofNullable(pushOrderBillsBO.getGuestCount()).orElse(1).toString());
        request.setProductList(transferProductDetails(request, pushOrderBillsBO));
        return request;
    }

    private WeihaiSaleOutRequest transferSaleOutRequest(DepositErpSyncDTO depositErpSyncDTO) {
        // 构建请求对象
        String billingType = "堂食";
        String diningTableName = depositErpSyncDTO.getDiningTableName();
        WeihaiSaleOutRequest request = new WeihaiSaleOutRequest();
        if (Boolean.TRUE.equals(depositErpSyncDTO.getTakeawayFlag())) {
            // 如果是外卖
            billingType = "外卖";
            diningTableName = "外卖-外卖";
        }
        if (StringUtils.isEmpty(diningTableName)) {
            diningTableName = "快餐-快餐";
        }
        String[] diningTableNameSplit = diningTableName.split("-");
        request.setAreaName(diningTableNameSplit[0]);
        request.setBillingType(billingType);
        request.setBusinessDate(depositErpSyncDTO.getBusinessDate().format(DATE_FORMATTER));
        request.setDeskName(diningTableNameSplit[1]);
        request.setDiscountAmount(depositErpSyncDTO.getOrderFee().subtract(depositErpSyncDTO.getActuallyPayFee()));
        request.setFlowAmount(depositErpSyncDTO.getOrderFee());
        request.setIsAdjustOrder(1);
        request.setLcDepartId(transferLcDepartId(depositErpSyncDTO.getStoreId()));
        request.setOpenTime(depositErpSyncDTO.getOrderCreateTime().format(DATETIME_FORMATTER));
        request.setOrderNo(depositErpSyncDTO.getOrderGuid());
        request.setPaidAmount(depositErpSyncDTO.getActuallyPayFee());
        request.setPassengerFlow(Optional.ofNullable(depositErpSyncDTO.getGuestCount()).orElse(1).toString());
        request.setProductList(transferProductDetails(request, depositErpSyncDTO));
        return request;
    }

    private List<WeihaiSaleOutRequest.ProductDetail> transferProductDetails(WeihaiSaleOutRequest request, PushOrderBillsBO pushOrderBillsBO) {
        List<SalesOrderDisheseBO> salesOrderDishes = pushOrderBillsBO.getSalesOrderDisheses();
        // 过滤套餐项
        salesOrderDishes.removeIf(e -> e.getPackageDishes() == 1);
        return salesOrderDishes.stream().map(item -> {
            WeihaiSaleOutRequest.ProductDetail detail = new WeihaiSaleOutRequest.ProductDetail();
            detail.setFoodCode(item.getDishesId());
            BigDecimal itemPrice = Optional.ofNullable(item.getItemPrice()).orElse(item.getPracticeSubTotal());
            itemPrice = Optional.ofNullable(itemPrice).orElse(item.getPrice().multiply(item.getCheckCount()));
            detail.setFoodOriginalAmount(itemPrice);
            BigDecimal discountTotalPrice = Objects.isNull(item.getDiscountTotalPrice()) ? itemPrice : item.getDiscountTotalPrice();
            detail.setFoodIncomeAmount(discountTotalPrice);
            detail.setFoodDiscountAmount(itemPrice.subtract(discountTotalPrice));
            detail.setFoodName(item.getDishesName());
            if (!StringUtils.isEmpty(item.getDishesSkuName())) {
                detail.setFoodName(detail.getFoodName() + "(" + item.getDishesSkuName() + ")");
            }
            detail.setFoodStyle(item.getDishTypeName());
            detail.setIsAdjustOrder(request.getIsAdjustOrder());
            detail.setIsCost(1);
            detail.setLcDepartId(request.getLcDepartId());
            detail.setOrderNo(pushOrderBillsBO.getSalesOrderId());
            detail.setPrice(item.getPrice().stripTrailingZeros().toPlainString());
            detail.setSalesQuantity(item.getCheckCount().stripTrailingZeros().toPlainString());
            // 技术人员让传单位
            detail.setSpecification(item.getCheckUnit());
            return detail;
        }).collect(Collectors.toList());
    }

    private List<WeihaiSaleOutRequest.ProductDetail> transferProductDetails(WeihaiSaleOutRequest request, DepositErpSyncDTO depositErpSyncDTO) {
        List<DepositDish> depositDishes = depositErpSyncDTO.getDepositDishes();
        // 过滤套餐项
        depositDishes.removeIf(e -> ItemUtil.isGroup(e.getItemType()));
        return depositDishes.stream().map(item -> {
            WeihaiSaleOutRequest.ProductDetail detail = new WeihaiSaleOutRequest.ProductDetail();
            detail.setFoodCode(item.getSkuId());
            detail.setFoodOriginalAmount(item.getPrice().multiply(item.getSkuCount()));
            detail.setFoodIncomeAmount(item.getPrice().multiply(item.getSkuCount()));
            detail.setFoodDiscountAmount(BigDecimal.ZERO);
            detail.setFoodName(item.getItemName());
            if (!StringUtils.isEmpty(item.getSkuName())) {
                detail.setFoodName(detail.getFoodName() + "(" + item.getSkuName() + ")");
            }
            detail.setFoodStyle(item.getTypeName());
            detail.setIsAdjustOrder(request.getIsAdjustOrder());
            detail.setIsCost(1);
            detail.setLcDepartId(request.getLcDepartId());
            detail.setOrderNo(depositErpSyncDTO.getOrderGuid());
            detail.setPrice(item.getPrice().stripTrailingZeros().toPlainString());
            BigDecimal skuCount = item.getSkuCount().abs();
            if (item.getType() == 0) {
                skuCount = skuCount.negate();
            }
            detail.setSalesQuantity(skuCount.stripTrailingZeros().toPlainString());
            detail.setSpecification(item.getUnit());
            return detail;
        }).collect(Collectors.toList());
    }

    private String sign(WeihaiSaleOutRequest request, String secretKey, String unique) {
        if (StringUtils.isEmpty(secretKey)) {
            log.error("secretKey is null");
            throw new BusinessException("secretKey is null");
        }
        // 获取请求体
        String body = JacksonUtils.writeValueAsString(request);
        // 请求方法和URL
        String method = "post";
        String requestUri = "/openApi/powerHolder/order/sale/out";

        // 按照签名规则：unique + body + secretKey + method + requestUri
        String stringToSign = unique + body + secretKey + method + requestUri;
        log.info("微海API待签名字符串: {}", stringToSign);
        try {
            // 创建HmacSHA256签名对象
            Mac sha256HMAC = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA256_ALGORITHM);
            sha256HMAC.init(secretKeySpec);
            // 计算签名
            byte[] data = sha256HMAC.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            // Base64编码
            String signature = Base64.getUrlEncoder().encodeToString(data);
            log.info("微海API签名结果: {}", signature);
            return signature;
        } catch (Exception e) {
            log.error("微海API签名生成失败", e);
            throw new BusinessException(String.format("签名生成错误: %s",
                    Optional.ofNullable(e.getMessage()).orElse(e.getClass().getSimpleName())));
        }
    }

    private String transferLcDepartId(String storeGuid) {
        MDMResult<OrganizationDTO> organizationResp = mdmServiceClient.queryOrganization(storeGuid);
        log.warn("查询mdm组织信息返回:{}", JacksonUtils.writeValueAsString(organizationResp));
        if (Objects.isNull(organizationResp.getData())) {
            log.warn("当前组织未同步到mdm, storeGuid:{}", storeGuid);
            return storeGuid;
        }
        String weihaiId = organizationResp.getData().getWeihaiId();
        if (StringUtils.isEmpty(weihaiId)) {
            log.warn("当前组织未同步到mdm, storeGuid:{}", storeGuid);
            return storeGuid;
        }
        return weihaiId;
    }

}