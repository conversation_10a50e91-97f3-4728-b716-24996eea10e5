package com.holderzone.saas.store.trade.service.impl;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.bill.RefundReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.item.BatchItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.dto.table.TableStatusChangeDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.enums.table.TableStatusChangeEnum;
import com.holderzone.saas.store.trade.entity.bo.AggPayAttachDataBO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemService;
import com.holderzone.saas.store.trade.service.TableService;
import com.holderzone.saas.store.trade.service.mq.TableMqService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @className
 * @date 2018/09/04 16:10
 * @description //TODO
 * @program holder-saas-store-order
 */
@Service
public class TableServiceImpl implements TableService {

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    private final OrderItemService orderItemService;

    private final TableMqService tableMqService;

    @Autowired
    public TableServiceImpl(OrderItemService orderItemService, TableMqService tableMqService) {
        this.orderItemService = orderItemService;
        this.tableMqService = tableMqService;
    }

    @Override
    public void returnChange(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO, OrderDO orderDO) {
        TableStatusChangeDTO tableStatusChangeDTO = orderTransform
                .batchItemReturnOrFreeReqDTO2TableStatusChangeDTO(batchItemReturnOrFreeReqDTO);
        tableStatusChangeDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
        tableStatusChangeDTO.setTableGuid(orderDO.getDiningTableGuid());
        tableStatusChangeDTO.setEnableManualClear(UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())
                ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        if (orderItemService.hasCurrentItem(orderDO.getGuid())) {
            tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.ORDER_DISH_CHANGE.getId());
        } else {
            tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.ORDER_DISH_REFUND_ALL.getId());
        }

        tableMqService.tableStatusChangeWithMq(tableStatusChangeDTO);
    }

    @Override
    public void checkOutChange(AggPayAttachDataBO aggPayAttachDataBO, OrderDO orderDO) {
        TableStatusChangeDTO tableStatusChangeDTO = orderTransform.aggPayAttachDataBO2TableStatusChangeDTO
                (aggPayAttachDataBO);
        tableStatusChangeDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
        tableStatusChangeDTO.setTableGuid(orderDO.getDiningTableGuid());
        tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.CHECKOUT.getId());
        tableMqService.tableStatusChangeWithMq(tableStatusChangeDTO);
    }

    @Override
    public void checkOutChange(BaseDTO baseDTO, OrderDO orderDO) {
        TableStatusChangeDTO tableStatusChangeDTO = orderTransform.getBaseDTODFromDto(baseDTO);
        tableStatusChangeDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
        tableStatusChangeDTO.setTableGuid(orderDO.getDiningTableGuid());
        tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.CHECKOUT.getId());
        tableMqService.tableStatusChangeWithMq(tableStatusChangeDTO);
    }

    @Override
    public void checkOutChange(RefundReqDTO refundReqDTO, OrderDO orderDO) {
        TableStatusChangeDTO tableStatusChangeDTO = orderTransform.refundReqDTO2TableStatusChangeDTO
                (refundReqDTO);
        tableStatusChangeDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
        tableStatusChangeDTO.setTableGuid(orderDO.getDiningTableGuid());
        tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.CHECKOUT.getId());
        tableMqService.tableStatusChangeWithMq(tableStatusChangeDTO);
    }

    @Override
    public void disabledChange(CancelOrderReqDTO cancelOrderReqDTO, OrderDO orderDO) {
        TableStatusChangeDTO tableStatusChangeDTO = orderTransform.cancelOrderReqDTO2TableStatusChangeDTO
                (cancelOrderReqDTO);
        tableStatusChangeDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
        tableStatusChangeDTO.setTableGuid(orderDO.getDiningTableGuid());
        //BugFix：20283 只有快餐的情况下发送更新桌台的Mq
        if (orderDO.getTradeMode().equals(TradeModeEnum.DINEIN.getCode())) {
            tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.DISABLED.getId());
            tableMqService.tableStatusChangeWithMq(tableStatusChangeDTO);
        }
    }

    @Override
    public void printPreBillChange(BaseDTO baseDTO, OrderDO orderDO) {
        TableStatusChangeDTO tableStatusChangeDTO = orderTransform.getBaseDTODFromDto(baseDTO);
        tableStatusChangeDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
        tableStatusChangeDTO.setTableGuid(orderDO.getDiningTableGuid());
        tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.ORDER_DISH_CHANGE.getId());
        tableMqService.tableStatusChangeWithMq(tableStatusChangeDTO);
    }
}
