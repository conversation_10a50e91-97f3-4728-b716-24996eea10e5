package com.holderzone.saas.store.trade.service.impl;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.trade.repository.feign.MessageClientService;
import com.holderzone.saas.store.trade.service.MessageService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageServiceImpl
 * @date 2019/11/14 11:28
 * @description //TODO
 * @program IdeaProjects
 */
@Log4j2
@Service
public class MessageServiceImpl implements MessageService {

    @Resource(name = "checkOutThreadPool")
    private ExecutorService executorService;

    @Autowired
    private MessageClientService messageClientService;

    @Override
    public void notifyOrderChange(BaseDTO baseDTO){
        return;
//        String userInfoJson = UserContextUtils.getJsonStr();
//        String notifyOrderGuidList = LocalizeSynchronizeContext.getOrderGuidJsonString();
//        log.info("notify order change state : {} ",notifyOrderGuidList);
//        LocalizeSynchronizeContext.remove();
//        executorService.submit(()->{
//            UserContextUtils.put(userInfoJson);
//            BusinessMessageDTO build = BusinessMessageDTO.builder()
//                    .storeGuid(baseDTO.getStoreGuid())
//                    .storeName(baseDTO.getStoreName())
//                    .messageType(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getId())
//                    .detailMessageType(BusinessMsgTypeEnum.ORDER_CHANGED.getId())
//                    .platform("2")
//                    .subject("订单数据变化")
//                    .content(notifyOrderGuidList)
//                    .build();
//            messageClientService.notifyMsg(build);
//        });
    }
}