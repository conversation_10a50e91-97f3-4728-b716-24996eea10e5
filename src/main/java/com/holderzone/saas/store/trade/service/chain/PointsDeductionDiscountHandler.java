package com.holderzone.saas.store.trade.service.chain;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestIntegralStore;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseIntegralOffset;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-26
 * @description
 */
@Component
@Slf4j
@AllArgsConstructor
public class PointsDeductionDiscountHandler extends DiscountHandler {

    private final MemberTerminalClientService memberTerminalClientService;

    @Override
    void dealDiscount(DiscountContext context) {

        // 13.积分抵扣
        DiscountFeeDetailDTO pointsDeduction = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap().get
                (type()));
        context.getDiscountFeeDetailDTOS().add(pointsDeduction);
        String memberInfoCardGuid = context.getBillCalculateReqDTO().getMemberInfoCardGuid();

        //判断是否主卡  权益卡不享受积分抵扣
        if (StringUtils.isNotEmpty(memberInfoCardGuid)
                && memberTerminalClientService.checkCardInfoType(memberInfoCardGuid)) {
            pointsDeduction.setDiscountFee(BigDecimal.ZERO);
            log.info("权益卡不享受积分抵扣memberCardGuid：{}", memberInfoCardGuid);
        } else {
            if (context.isHasMember() || StringUtils.isNotEmpty(memberInfoCardGuid)) {
                log.info("6.积分抵扣请求金额：{}，卡号：{}", JacksonUtils.writeValueAsString(context.getOrderDetailRespDTO().getOrderSurplusFee()),
                        context.getOrderDO().getMemberCardGuid());
                ResponseIntegralOffset compute = buildCompute(context);

                if (ObjectUtil.equal(context.getBillCalculateReqDTO().getMemberIntegral(), 1)) {
                    context.getOrderDO().setUseIntegral(compute.getUseIntegral());
                } else {
                    context.getOrderDO().setUseIntegral(0);
                }
                log.info("6.积分抵扣返回：{}", JacksonUtils.writeValueAsString(compute));
                verifyComputeDeductionMoney(compute, pointsDeduction, context);

                context.getOrderDetailRespDTO().setIntegralOffsetResultRespDTO(compute);
                log.info("6.积分抵扣计算后订单剩余金额：{}，优惠金额：{}", context.getOrderDetailRespDTO().getOrderSurplusFee(), pointsDeduction
                        .getDiscountFee());
            } else {
                pointsDeduction.setDiscountFee(BigDecimal.ZERO);
            }
        }

        if (Objects.nonNull(pointsDeduction.getDiscountFee())) {
            pointsDeduction.setDiscountFee(pointsDeduction.getDiscountFee().setScale(2, RoundingMode.HALF_UP));
            handleDiscountTotalPrice(context, pointsDeduction);
        } else {
            pointsDeduction.setDiscountFee(BigDecimal.ZERO);
        }
    }

    private void verifyComputeDeductionMoney(ResponseIntegralOffset compute, DiscountFeeDetailDTO pointsDeduction, DiscountContext context) {
        if (!BigDecimalUtil.greaterThanZero(compute.getDeductionMoney())) {
            return;
        }
        if (context.getDiscountRuleBO().getMemberIntegral() != null && context.getDiscountRuleBO().getMemberIntegral() == 1) {
            log.info("6.积分抵扣金额大于0且开启积分抵扣");
            pointsDeduction.setDiscountFee(compute.getDeductionMoney());
            context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract
                    (pointsDeduction.getDiscountFee()));

            pointsDeduction.setIntegral(compute.getUseIntegral());
            log.info(("6.积分抵扣数量为：" + compute.getUseIntegral()));
        } else {
            pointsDeduction.setDiscountFee(BigDecimal.ZERO);
            compute.setRuleState(0).setIntegralFailReason("用户未开启积分抵扣");
        }

    }

    private ResponseIntegralOffset buildCompute(DiscountContext context) {
        ResponseIntegralOffset compute = new ResponseIntegralOffset();
        if (context.isIntegralStore()) {
            //构建积分商城传递参数
            DineInItemDTO itemDTO = context.getOrderDetailRespDTO().getDineInItemDTOS().get(0);
            RequestIntegralStore requestIntegralStore = new RequestIntegralStore();
            BigDecimal count = context.getOrderDetailRespDTO().getDineInItemDTOS().stream().map(DineInItemDTO::getCurrentCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            itemDTO.setCurrentCount(count);
            context.getOrderDetailRespDTO().setDineInItemDTOS(Collections.singletonList(itemDTO));
            requestIntegralStore.setCurrentCount(count);
            requestIntegralStore.setItemGuid(itemDTO.getItemGuid());
            requestIntegralStore.setSkuGuid(itemDTO.getSkuGuid());
            requestIntegralStore.setStoreGuid(context.getBillCalculateReqDTO().getStoreGuid());
            requestIntegralStore.setOrderMoney(context.getOrderDetailRespDTO().getOrderSurplusFee());
            if (StringUtils.isNotEmpty(context.getOrderDO().getMemberCardGuid()) && !"0".equals(context.getOrderDO().getMemberCardGuid())) {
                requestIntegralStore.setMemberInfoCardGuid(context.getOrderDO().getMemberCardGuid());
                compute = memberTerminalClientService.computeIntegralStore(requestIntegralStore);
            } else {
                if (StringUtils.isNotEmpty(context.getBillCalculateReqDTO().getMemberInfoCardGuid()) && !"0".equals
                        (context.getBillCalculateReqDTO().getMemberInfoCardGuid())) {
                    requestIntegralStore.setMemberInfoCardGuid(context.getBillCalculateReqDTO().getMemberInfoCardGuid());
                    compute = memberTerminalClientService.computeIntegralStore(requestIntegralStore);
                }
            }
            return compute;
        }
        if (StringUtils.isNotEmpty(context.getOrderDO().getMemberCardGuid()) && !"0".equals(context.getOrderDO().getMemberCardGuid())) {
            compute = memberTerminalClientService.compute(context.getOrderDO().getMemberCardGuid(),
                    context.getOrderDetailRespDTO().getOrderSurplusFee());
        } else {
            if (StringUtils.isNotEmpty(context.getBillCalculateReqDTO().getMemberInfoCardGuid()) && !"0".equals
                    (context.getBillCalculateReqDTO().getMemberInfoCardGuid())) {
                compute = memberTerminalClientService.compute(context.getBillCalculateReqDTO().getMemberInfoCardGuid(),
                        context.getOrderDetailRespDTO().getOrderSurplusFee());
            }
        }

        return compute;
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.POINTS_DEDUCTION.getCode();
    }


    /**
     * 商品实付金额处理
     */
    private void handleDiscountTotalPrice(DiscountContext context, DiscountFeeDetailDTO pointsDeduction) {
        log.info("[积分抵扣处理]积分抵扣优惠信息：{}", JacksonUtils.writeValueAsString(pointsDeduction));
        if (!BigDecimalUtil.greaterThanZero(pointsDeduction.getDiscountFee())) {
            return;
        }
        List<DineInItemDTO> allItems = context.getAllItems();
        Map<String, DineInItemDTO> itemDTOMap = context.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (v1, v2) -> v1));
        BigDecimal discountFee = pointsDeduction.getDiscountFee();

        DineinOrderDetailRespDTO orderDetailRespDTO = context.getOrderDetailRespDTO();
        List<DineInItemDTO> calculateItemList = new ArrayList<>(allItems);
        if (BigDecimalUtil.greaterThanZero(orderDetailRespDTO.getAppendFee())) {
            DineInItemDTO appendFeeItem = new DineInItemDTO();
            appendFeeItem.setGuid("-1");
            appendFeeItem.setDiscountTotalPrice(orderDetailRespDTO.getAppendFee()
                    .subtract(Optional.ofNullable(orderDetailRespDTO.getAppendDiscountFee()).orElse(BigDecimal.ZERO)));
            calculateItemList.add(appendFeeItem);
        }

        Map<String, BigDecimal> itemDiscountPriceMap = PriceCalculationUtils.calculationItemDiscountPrice(discountFee, calculateItemList);
        itemDiscountPriceMap.forEach((guid, itemDiscountFee) -> {
            DineInItemDTO dineInItemDTO = itemDTOMap.get(guid);
            if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(itemDiscountFee));
            }
        });
        // 附加费优惠金额
        orderDetailRespDTO.setAppendDiscountFee(Optional.ofNullable(orderDetailRespDTO.getAppendDiscountFee()).orElse(BigDecimal.ZERO)
                .add(itemDiscountPriceMap.getOrDefault("-1", BigDecimal.ZERO)));
    }
}
