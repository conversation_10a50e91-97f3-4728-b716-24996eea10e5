package com.holderzone.saas.store.trade.controller;


import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import com.holderzone.saas.store.trade.service.MemberOperateRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 会员操作记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api(tags = "会员操作记录接口")
@RequestMapping("/member_operate_record")
public class MemberOperateRecordController {

    private final MemberOperateRecordService memberOperateRecordService;

    @ApiOperation(value = "保存记录")
    @PostMapping("/save_record")
    public void saveRecord(@RequestBody MemberOperateRecordReqDTO reqDTO) {
        log.info("[会员操作记录]保存记录,reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        memberOperateRecordService.saveRecord(reqDTO);
    }

}
