package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.enums.VolumeTypeEnum;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-25
 * @description
 */
@Component
@Slf4j
@AllArgsConstructor
public class GoodsGrouponDiscountHandler extends DiscountHandler{

    private final MemberTerminalClientService memberTerminalClientService;

    private final PriceCalculationUtils priceCalculationUtils;
    @Override
    void dealDiscount(DiscountContext context) {
        if(context.isRejectDiscount()){
            return;
        }
        Integer volumeCodeType = -1;
        //2. =====================校验券 START =====================================
        boolean checkParmVolumeCode = StringUtils.isNotBlank(context.getBillCalculateReqDTO().getVolumeCode());
        boolean checkParmMemberConsumptionGuid = StringUtils.isNotBlank(context.getOrderDO().getMemberConsumptionGuid()) &&
                !"0".equals(context.getOrderDO().getMemberConsumptionGuid());
        if (checkParmVolumeCode || checkParmMemberConsumptionGuid || CollectionUtil.isNotEmpty(context.getBillCalculateReqDTO().getVolumeCodes())) {
            log.info("验券类型入参{}", "VolumeCode: " + context.getBillCalculateReqDTO().getVolumeCode() + "  ," +
                    "MemberConsumptionGuid:" + context.getOrderDO().getMemberConsumptionGuid());
            if (context.getBillCalculateReqDTO().getVolumeCode() == null && CollectionUtil.isNotEmpty(context.getBillCalculateReqDTO().getVolumeCodes())) {
                volumeCodeType = memberTerminalClientService.getVolumeType(context.getBillCalculateReqDTO().getVolumeCodes().get(0), context.getOrderDO()
                        .getMemberConsumptionGuid());
            } else {
                volumeCodeType = memberTerminalClientService.getVolumeType(context.getBillCalculateReqDTO().getVolumeCode(), context.getOrderDO()
                        .getMemberConsumptionGuid());
            }
            log.info("验券类型结果：{}", volumeCodeType);
        }
        // ===================================== END ===========================

        // ===================== 4.商品券 START =====================================
        if (volumeCodeType == VolumeTypeEnum.PRODUCT_COUPON.getCcCode()) {
            List<RequestDishInfo> dishInfoDTOList = priceCalculationUtils.dealwithVolume(context);
            priceCalculationUtils.dealwithVolumeByDiscount(context,dishInfoDTOList);
            DiscountDO memberGoodsGrouponDO = context.getDiscountTypeMap().get(type());
            DiscountFeeDetailDTO memberGoodsGroupon = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO
                    (memberGoodsGrouponDO);
            if (CollectionUtil.isNotEmpty(dishInfoDTOList)) {
                singleItemReduceDiscount(context.getDineInItemDTOMap(), memberGoodsGroupon, dishInfoDTOList);
                log.info("3.会员单品券最终菜品：{}", JacksonUtils.writeValueAsString(memberGoodsGroupon));
            } else {
                memberGoodsGroupon.setDiscountFee(BigDecimal.ZERO);
            }
            context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract
                    (memberGoodsGroupon
                            .getDiscountFee()));
            context.getDiscountFeeDetailDTOS().add(memberGoodsGroupon);
            log.info("3.会员商品券计算后订单剩余金额：{}，优惠金额：{}，会员商品券计算后菜品：{}", context.getOrderDetailRespDTO().getOrderSurplusFee(),
                    memberGoodsGroupon.getDiscountFee(), JacksonUtils.writeValueAsString(context.getDineInItemDTOMap()));
        }
        context.setVolumeCodeType(volumeCodeType);
    }

    private void singleItemReduceDiscount(Map<String, DineInItemDTO> dineInItemDTOMap, DiscountFeeDetailDTO
            memberGroupon,
                                          List<RequestDishInfo> dishInfoDTOList) {
        BigDecimal discountFee = BigDecimal.ZERO;
        for (RequestDishInfo dishInfoDTO : dishInfoDTOList) {
            if (dishInfoDTO.getDiscountMoney() != null) {
                discountFee = discountFee.add(dishInfoDTO.getDiscountMoney());
                DineInItemDTO dineInItemDTO = dineInItemDTOMap.get(dishInfoDTO.getOrderItemGuid());
                if (dineInItemDTO != null && dishInfoDTO.getDiscountMoney() != null && dishInfoDTO.getDiscountMoney()
                        .floatValue() > 0) {
                    dineInItemDTO.setTotalDiscountFee(dineInItemDTO.getTotalDiscountFee().add(dishInfoDTO
                            .getDiscountMoney()));
                    dineInItemDTO.setIsGoodsReduceDiscount(dishInfoDTO.getProductItemNum());
                    dineInItemDTO.setTicketPreferential(dishInfoDTO.getDiscountMoney());
                }

            }
        }
        memberGroupon.setDiscountFee(BigDecimalUtil.setScale2(discountFee));

    }

    @Override
    Integer type() {
        return DiscountTypeEnum.GOODS_GROUPON.getCode();
    }
}
