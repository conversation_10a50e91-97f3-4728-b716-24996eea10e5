package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-06-25
 * @description
 */
@Slf4j
public abstract class DiscountHandler {

    private DiscountHandler nextDiscountHandler;

    void setNextDiscountHandler(DiscountHandler discountHandler){
        nextDiscountHandler = discountHandler;
    }

    public void doDiscount(DiscountContext context){
        dealDiscount(context);
        if(nextDiscountHandler != null){
            nextDiscountHandler.doDiscount(context);
        }
    }
    public BigDecimal dealWithDiscountFee(BigDecimal orderSurplusFee, BigDecimal discountFee) {
        if (discountFee.compareTo(orderSurplusFee) > 0) {
            discountFee = BigDecimalUtil.greaterThanZero(orderSurplusFee) ?
                    orderSurplusFee : BigDecimal.ZERO;
        }
        return discountFee;
    }
    public void singleItemDiscount(Map<String, DineInItemDTO> dineInItemDTOMap, DiscountFeeDetailDTO memberGroupon,
                                    List<RequestDishInfo> dishInfoDTOList) {
        BigDecimal discountFee = BigDecimal.ZERO;
        for (RequestDishInfo dishInfoDTO : dishInfoDTOList) {
            if (dishInfoDTO.getDiscountMoney() != null) {
                discountFee = discountFee.add(dishInfoDTO.getDiscountMoney());
                DineInItemDTO dineInItemDTO = dineInItemDTOMap.get(dishInfoDTO.getOrderItemGuid());
                if (dineInItemDTO != null && dishInfoDTO.getDiscountMoney() != null) {
                    dineInItemDTO.setTotalDiscountFee(dineInItemDTO.getTotalDiscountFee().add(dishInfoDTO
                            .getDiscountMoney()));

                    dineInItemDTO.setDiscountPreferential(dishInfoDTO.getDiscountMoney());
                    log.info("满折优惠金额,{}", dishInfoDTO.getDiscountMoney());
                }
            }
        }
        memberGroupon.setDiscountFee(BigDecimalUtil.setScale2(discountFee));
    }
    abstract void dealDiscount(DiscountContext context);

    abstract Integer type();
}
