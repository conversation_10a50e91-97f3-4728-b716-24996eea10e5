package com.holderzone.saas.store.trade.event.delay;

import com.holderzone.saas.store.dto.weixin.deal.ConfirmConfigTaskDTO;
import com.holderzone.saas.store.trade.service.PadOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 确认提示监听
 * @date 2021/8/31 11:51
 */
@Component
@Slf4j
public class PadConfirmPromptListener implements RedisDelayedQueueListener<ConfirmConfigTaskDTO> {

    @Resource
    PadOrderService padOrderService;

    @Override
    public void invoke(ConfirmConfigTaskDTO taskDTO) {
        log.info("处理延时队列确认提示数据：{}", taskDTO);
        padOrderService.dealRedisDelayedTask(taskDTO);
    }
}
