package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.common.enums.OrderSourceEnum;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestBaseInfo;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestOrderInfo;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestPayInfo;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestConfirmPay;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseConfirmPay;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.DebtPaymentTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.DebtUnitDO;
import com.holderzone.saas.store.trade.entity.domain.DebtUnitRecordDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.dto.DebtCreditChangeDTO;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.entity.query.DebtUnitRecordQuery;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.PageAdapter;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.mapper.DebtUnitRecordMapper;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.DebtUnitRecordService;
import com.holderzone.saas.store.trade.service.DebtUnitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> R
 * @date 2020/12/15 16:07
 * @description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DebtUnitRecordServiceImpl extends ServiceImpl<DebtUnitRecordMapper, DebtUnitRecordDO> implements DebtUnitRecordService {

    private final DebtUnitRecordMapper debtUnitMapper;
    private final RedisHelper redisHelper;
    private final DynamicHelper dynamicHelper;
    private final DebtUnitService debtUnitService;

    private final MemberTerminalClientService memberTerminalClientService;

    private final OrderService orderService;

    @Override
    public Page<DebtUnitRecordPageRespDTO> pageDebtUnitRecord(DebtUnitRecordPageReqDTO reqDTO) {
        Assert.notNull(reqDTO, "请求参数不能为空！");
        DebtUnitRecordQuery dbQuery = new DebtUnitRecordQuery();
        BeanUtil.copyProperties(reqDTO, dbQuery);
        IPage<DebtUnitRecordDO> debtUnitRecordDOIPage = debtUnitMapper.pageDebtUnitRecord(new PageAdapter<>(reqDTO), dbQuery);
        return transformDetailsDTO(debtUnitRecordDOIPage);
    }

    @Override
    public List<DebtUnitRecordDO> listByRepaymentBatchNumber(String repaymentBatchNumber) {
        if (StringUtils.isEmpty(repaymentBatchNumber)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<DebtUnitRecordDO> qw = new LambdaQueryWrapper<DebtUnitRecordDO>()
                .eq(DebtUnitRecordDO::getRepaymentBatchNumber, repaymentBatchNumber)
                .eq(DebtUnitRecordDO::getIsDelete, Boolean.FALSE);
        return list(qw);
    }

    @Override
    public DebtUnitRecordPageRespDTO debtUnitRecordByOrderGuid(Long orderGuid) {
        DebtUnitRecordDO debtUnitRecordDO = baseMapper.getByOrderGuid(orderGuid);
        DebtUnitRecordPageRespDTO debtUnitRecordRespDTO = new DebtUnitRecordPageRespDTO();
        BeanUtil.copyProperties(debtUnitRecordDO, debtUnitRecordRespDTO);
        return debtUnitRecordRespDTO;
    }

    @Override
    public DebtUnitRecordTotalDTO queryDebtUnitTotal(String unitCode) {
        if (StringUtils.isBlank(unitCode)) {
            return null;
        }
        DebtUnitDO unitDO = debtUnitService.getOne(new LambdaQueryWrapper<DebtUnitDO>().eq(DebtUnitDO::getCode, unitCode));
        if (Objects.isNull(unitDO)) {
            log.info("无挂账单位信息！");
            return null;
        }
        DebtUnitRecordTotalDTO totalDTO = new DebtUnitRecordTotalDTO();
        totalDTO.setCreditLimitTotal(unitDO.getCreditLimit());
        BigDecimal bigDecimal = debtUnitMapper.queryDebtUnitTotal(unitDO.getGuid());
        if (bigDecimal != null) {
            totalDTO.setDebtFeeTotal(bigDecimal);
        } else {
            totalDTO.setDebtFeeTotal(BigDecimal.ZERO);
        }
        totalDTO.setAvailableFeeTotal(unitDO.getCreditLimit().subtract(totalDTO.getDebtFeeTotal()));
        totalDTO.setCode(unitDO.getCode());
        totalDTO.setGuid(unitDO.getGuid());
        totalDTO.setName(unitDO.getName());
        totalDTO.setContactName(unitDO.getContactName());
        totalDTO.setContactTel(unitDO.getContactTel());
        log.info(" queryDebtUnitTotal 最终返回参数:{}", JacksonUtils.writeValueAsString(totalDTO));
        return totalDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDebtUnit(DebtUnitRecordUpdateReqDTO updateReqDTO) {
        if (CollectionUtils.isEmpty(updateReqDTO.getUpdateList())) {
            log.error("还款记录为空！");
            return Boolean.FALSE;
        }
        String lockKey = "updateDebtUnit:";
        boolean lockSuccess = false;
        try {
            lockKey += updateReqDTO.getUnitGuid();
            lockSuccess = redisHelper.setNxEx(lockKey, "1", 30);
            if (!lockSuccess) {
                log.error("更新单位流水重复调用");
                return Boolean.FALSE;
            }
            return debtRepayment(updateReqDTO);
        } finally {
            if (lockSuccess) {
                redisHelper.delete(lockKey);
            }
        }
    }

    /**
     * 挂账还款
     */
    private Boolean debtRepayment(DebtUnitRecordUpdateReqDTO updateReqDTO) {
        List<String> recordList = updateReqDTO.getUpdateList()
                .stream()
                .map(DebtUnitRecordUpdateDTO::getGuid)
                .collect(Collectors.toList());
        List<DebtUnitRecordDO> debtUnitRecordList = list(new LambdaQueryWrapper<DebtUnitRecordDO>()
                .in(DebtUnitRecordDO::getGuid, recordList)
                .eq(DebtUnitRecordDO::getIsDelete, Boolean.FALSE));
        if (CollectionUtils.isEmpty(debtUnitRecordList)) {
            log.error("无还款记录！");
            return Boolean.FALSE;
        }
        List<DebtUnitRecordDO> unRepaymentRecordList = debtUnitRecordList.stream()
                .filter(e -> e.getRepaymentStatus() == 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unRepaymentRecordList)) {
            log.error("无还款记录！");
            return Boolean.FALSE;
        }
        // 凭证
        String evidences = JacksonUtils.writeValueAsString(Optional.ofNullable(updateReqDTO.getEvidenceList())
                .orElse(Lists.newArrayList()));
        Map<String, DebtUnitRecordUpdateDTO> updateDTOMap = updateReqDTO.getUpdateList()
                .stream()
                .collect(Collectors.toMap(DebtUnitRecordUpdateDTO::getGuid, e -> e));
        UserContext userContext = UserContextUtils.get();
        LocalDateTime now = LocalDateTime.now();
        updateReqDTO.setRepaymentBatchNumber(String.valueOf(System.currentTimeMillis()));
        BigDecimal creditChange = BigDecimal.ZERO;
        // 会员支付
        Map<String, String> memberPayMap = memberPay(updateReqDTO, unRepaymentRecordList);
        for (DebtUnitRecordDO recordDO : unRepaymentRecordList) {
            if (MapUtils.isNotEmpty(updateDTOMap)) {
                DebtUnitRecordUpdateDTO debtUnitRecordUpdateDTO = updateDTOMap.get(String.valueOf(recordDO.getGuid()));
                //校验还款金额是否等于挂账金额
                if (Objects.nonNull(debtUnitRecordUpdateDTO) && recordDO.getDebtFee().compareTo(debtUnitRecordUpdateDTO.getRepaymentFee()) == 0) {
                    recordDO.setRepaymentFee(debtUnitRecordUpdateDTO.getRepaymentFee());
                    creditChange = creditChange.add(debtUnitRecordUpdateDTO.getRepaymentFee());
                } else {
                    log.info("该挂账订单还款金额异常！还款金额信息：{}", JacksonUtils.writeValueAsString(debtUnitRecordUpdateDTO));
                    continue;
                }
            }
            recordDO.setRepaymentStatus(1);
            recordDO.setUpdateStaffGuid(userContext.getUserGuid());
            recordDO.setUpdateStaffName(userContext.getUserName());
            recordDO.setRepaymentBatchNumber(updateReqDTO.getRepaymentBatchNumber());
            recordDO.setGmtModified(now);
            recordDO.setPaymentType(updateReqDTO.getPaymentType());
            recordDO.setRemark(updateReqDTO.getRemark());
            recordDO.setEvidences(evidences);
            setMemberInfo(updateReqDTO, recordDO, memberPayMap);
        }
        log.info("最终还款记录:{}", JacksonUtils.writeValueAsString(unRepaymentRecordList));
        this.saveOrUpdateBatch(unRepaymentRecordList);
        DebtCreditChangeDTO changeDTO = new DebtCreditChangeDTO();
        changeDTO.setChangeType(1);
        changeDTO.setCreditChange(creditChange);
        changeDTO.setUnitGuid(updateReqDTO.getUnitGuid());
        log.info("提交修改额度记录:{}", JacksonUtils.writeValueAsString(changeDTO));
        if (creditChange.compareTo(BigDecimal.ZERO) > 0) {
            debtUnitService.creditLimitChange(changeDTO);
        }
        return Boolean.TRUE;
    }

    private void setMemberInfo(DebtUnitRecordUpdateReqDTO updateReqDTO,
                               DebtUnitRecordDO recordDO,
                               Map<String, String> memberPayMap) {
        if (!org.springframework.util.CollectionUtils.isEmpty(memberPayMap)) {
            String memberConsumptionGuid = memberPayMap.get(String.valueOf(recordDO.getGuid()));
            if (com.holderzone.framework.util.StringUtils.hasText(memberConsumptionGuid)) {
                recordDO.setMemberConsumptionGuid(memberConsumptionGuid);
            }
            recordDO.setMemberInfoGuid(updateReqDTO.getMemberInfoGuid());
            recordDO.setMemberCardGuid(updateReqDTO.getMemberInfoCardGuid());
            recordDO.setMemberPhone(updateReqDTO.getMemberPhone());
            recordDO.setMemberName(updateReqDTO.getMemberName());
        }
    }

    private Map<String, String> memberPay(DebtUnitRecordUpdateReqDTO updateReqDTO,
                                          List<DebtUnitRecordDO> unRepaymentRecordList) {
        if (DebtPaymentTypeEnum.MEMBER.getCode() != updateReqDTO.getPaymentType()) {
            return new HashMap<>();
        }
        if (org.springframework.util.CollectionUtils.isEmpty(unRepaymentRecordList)) {
            return new HashMap<>();
        }
        List<DebtUnitRecordUpdateDTO> updateList = updateReqDTO.getUpdateList();
        Map<String, DebtUnitRecordUpdateDTO> updateDTOMap = updateList.stream()
                .collect(Collectors.toMap(DebtUnitRecordUpdateDTO::getGuid, Function.identity(), (v1, v2) -> v1));
        List<String> orderGuidList = unRepaymentRecordList.stream()
                .map(DebtUnitRecordDO::getOrderGuid)
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());
        List<OrderDO> orderDOList = orderService.getListByIds(orderGuidList);
        Map<Long, OrderDO> orderDOMap = orderDOList.stream()
                .collect(Collectors.toMap(OrderDO::getGuid, Function.identity(), (v1, v2) -> v1));

        Map<String, String> memberPayMap = new HashMap<>();
        for (DebtUnitRecordDO debtUnitRecordDO : unRepaymentRecordList) {
            RequestConfirmPay confirmPayReqDTO = getMemberPayRequestConfirmPay(updateReqDTO,
                    updateDTOMap.get(String.valueOf(debtUnitRecordDO.getGuid())),
                    orderDOMap.get(debtUnitRecordDO.getOrderGuid()));
            try {
                log.info("[挂账][会员余额支付]入参={},挂账Guid={}", JacksonUtils.writeValueAsString(confirmPayReqDTO),
                        debtUnitRecordDO.getGuid());
                ResponseConfirmPay responseConfirmPay = memberTerminalClientService.payInfo(confirmPayReqDTO);
                log.warn("[挂账][会员余额支付]返回={}", JacksonUtils.writeValueAsString(responseConfirmPay));
                memberPayMap.put(String.valueOf(debtUnitRecordDO.getGuid()), responseConfirmPay.getMemberConsumptionGuid());
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }
        }
        return memberPayMap;
    }

    private RequestConfirmPay getMemberPayRequestConfirmPay(DebtUnitRecordUpdateReqDTO updateReqDTO,
                                                            DebtUnitRecordUpdateDTO updateDTO,
                                                            OrderDO orderDO) {
        RequestConfirmPay confirmPayReqDTO = new RequestConfirmPay();

        // 支付信息
        setRequestPayInfoList(updateDTO, confirmPayReqDTO);

        confirmPayReqDTO.setIntegralOrderType(0);
        confirmPayReqDTO.setCanteenPay(Boolean.FALSE);
        confirmPayReqDTO.setUseIntegral(BooleanEnum.FALSE.getCode());
        confirmPayReqDTO.setIntegralDiscountMoney(BigDecimal.ZERO);
        confirmPayReqDTO.setRequestDishInfoList(Lists.newArrayList());
        confirmPayReqDTO.setRequestDiscountInfoList(new ArrayList<>());
        confirmPayReqDTO.setCardBalancePayAmount(updateDTO.getRepaymentFee());
        confirmPayReqDTO.setNeedPassword(updateReqDTO.getNeedPassword());
        confirmPayReqDTO.setPayPassword(updateReqDTO.getMemberPassWord());

        // 构建公共信息
        confirmPayReqDTO.setRequestBaseInfo(getRequestBaseInfo(updateReqDTO));

        // 构建订单信息
        RequestOrderInfo orderInfoReqDTO = getRequestOrderInfo(orderDO, updateDTO);
        confirmPayReqDTO.setRequestOrderInfo(orderInfoReqDTO);
        return confirmPayReqDTO;
    }

    /**
     * 构建订单信息
     */
    private RequestOrderInfo getRequestOrderInfo(OrderDO orderDO,
                                                 DebtUnitRecordUpdateDTO updateDTO) {
        RequestOrderInfo orderInfoReqDTO = new RequestOrderInfo();
        orderInfoReqDTO.setOrderPaymentAmount(updateDTO.getRepaymentFee());
        orderInfoReqDTO.setOrderRealPaymentAmount(updateDTO.getRepaymentFee());
        orderInfoReqDTO.setOrderDiscountAmount(BigDecimal.ZERO);
        orderInfoReqDTO.setConsumptionType(1);
        orderInfoReqDTO.setConsumptionTime(LocalDateTime.now());

        orderInfoReqDTO.setOrderType(0);
        orderInfoReqDTO.setOrderTime(orderDO.getGmtCreate());
        Integer guestCount = orderDO.getGuestCount();
        if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
            guestCount = orderService.getTotalGuestCount(orderDO.getGuid());
        }
        orderInfoReqDTO.setDinnerNum(guestCount);
        //设置会员端订单来源
        orderInfoReqDTO.setOrderSource(baseDeviceTypeToOrderSource(orderDO.getDeviceType()));
        orderInfoReqDTO.setOrderNumber(orderDO.getOrderNo());
        return orderInfoReqDTO;
    }

    private Integer baseDeviceTypeToOrderSource(Integer deviceType) {
        try {
            switch (BaseDeviceTypeEnum.getDeviceTypeByCode(deviceType)) {
                case All_IN_ONE:
                    return OrderSourceEnum.SOURCE_ONE_MACHINE.getCode();
                case CLOUD_PANEL:
                    return OrderSourceEnum.SOURCE_PAD.getCode();
                case WECHAT:
                case TCD:
                    return OrderSourceEnum.SOURCE_WECHAT.getCode();
                case ALI:
                    return OrderSourceEnum.SOURCE_ALI.getCode();
                case POS:
                    return OrderSourceEnum.SOURCE_POS.getCode();
                default:
                    return deviceType;
            }
        } catch (Exception e) {
            log.error("未知设备类型：{}", deviceType);
        }
        return OrderSourceEnum.UNDEFINED.getCode();
    }

    /**
     * 构建公共信息参数
     */
    private RequestBaseInfo getRequestBaseInfo(DebtUnitRecordUpdateReqDTO updateReqDTO) {
        RequestBaseInfo baseInfoReqDTO = new RequestBaseInfo();
        baseInfoReqDTO.setMemberInfoCardGuid(updateReqDTO.getMemberInfoCardGuid());
        baseInfoReqDTO.setMemberInfoGuid(updateReqDTO.getMemberInfoGuid());
        baseInfoReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        baseInfoReqDTO.setEnterpriseName("");
        baseInfoReqDTO.setBrandGuid("");
        baseInfoReqDTO.setBrandName("");
        baseInfoReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        baseInfoReqDTO.setStoreName(UserContextUtils.getStoreName());
        return baseInfoReqDTO;
    }


    private void setRequestPayInfoList(DebtUnitRecordUpdateDTO updateDTO,
                                       RequestConfirmPay confirmPayReqDTO) {
        List<RequestPayInfo> payList = new ArrayList<>();
        RequestPayInfo payInfo = new RequestPayInfo();
        payInfo.setPayAmount(updateDTO.getRepaymentFee());
        payInfo.setPayWay(3);
        payInfo.setPayName("会员支付");
        payList.add(payInfo);
        confirmPayReqDTO.setRequestPayInfoList(payList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDebtUnit(DebtUnitRecordSaveReqDTO saveReqDTO) {
        log.info("保存挂账信息请求参数：{}", JacksonUtils.writeValueAsString(saveReqDTO));
        Assert.notNull(saveReqDTO, "请求参数不能为空！");
        Assert.notNull(saveReqDTO.getDebtFee(), "挂账金额不能为空！");
        DebtUnitRecordDO unitRecordDO = getOne(new LambdaQueryWrapper<DebtUnitRecordDO>()
                .eq(DebtUnitRecordDO::getOrderGuid, saveReqDTO.getOrderGuid())
                .eq(DebtUnitRecordDO::getIsDelete, Boolean.FALSE));
        //扣减单位挂账额度
        DebtCreditChangeDTO changeDTO = new DebtCreditChangeDTO();
        if (Objects.isNull(unitRecordDO)) {
            Assert.notNull(saveReqDTO.getUnitGuid(), "挂账单位不能为空！");
            unitRecordDO = new DebtUnitRecordDO();
            BeanUtil.copyProperties(saveReqDTO, unitRecordDO);
            unitRecordDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_DEBT_UNIT_RECORD));
            unitRecordDO.setGmtCreate(LocalDateTime.now());
            unitRecordDO.setRepaymentFee(BigDecimal.ZERO);
            unitRecordDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
            unitRecordDO.setCreateStaffName(UserContextUtils.getUserName());
        }
        if (saveReqDTO.getChangeType() == 1) {
            //反结账 逻辑删除挂账订单流水
            unitRecordDO.setIsDelete(Boolean.TRUE);
        }
        //变更类型 反结账增加挂账单位可用余额 新生成订单扣减可用余额
        changeDTO.setChangeType(saveReqDTO.getChangeType());
        changeDTO.setCreditChange(unitRecordDO.getDebtFee());
        changeDTO.setUnitGuid(unitRecordDO.getUnitGuid());
        debtUnitService.creditLimitChange(changeDTO);
        log.info("最终保存挂账信息：{}", JacksonUtils.writeValueAsString(unitRecordDO));
        return this.saveOrUpdate(unitRecordDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundDebtUnit(DebtUnitRecordSaveReqDTO refundReq) {
        log.info("挂账部分退款信息请求参数：{}", JacksonUtils.writeValueAsString(refundReq));
        Assert.notNull(refundReq.getDebtFee(), "挂账退款金额不能为空！");
        Assert.notNull(refundReq.getOrderGuid(), "挂账订单不能为空！");
        // 查询原订单挂账记录
        DebtUnitRecordDO oldDebtUnitRecord = getOne(new LambdaQueryWrapper<DebtUnitRecordDO>()
                .eq(DebtUnitRecordDO::getOrderGuid, refundReq.getOrderGuid())
                .eq(DebtUnitRecordDO::getIsDelete, Boolean.FALSE));
        if (Objects.isNull(oldDebtUnitRecord)) {
            log.error("原订单挂账记录未找到, 请求参数:{}", JacksonUtils.writeValueAsString(refundReq));
            return;
        }
        DebtUnitRecordDO unitRecordDO = new DebtUnitRecordDO();
        BeanUtils.copyProperties(oldDebtUnitRecord, unitRecordDO);
        // 原订单挂账记录是未还款
        if (0 == oldDebtUnitRecord.getRepaymentStatus()) {
            // 如果未还款 则需要更新之前的挂账记录
            if (oldDebtUnitRecord.getDebtFee().compareTo(refundReq.getDebtFee()) > 0) {
                oldDebtUnitRecord.setDebtFee(oldDebtUnitRecord.getDebtFee().subtract(refundReq.getDebtFee()));
                updateById(oldDebtUnitRecord);
            } else if (oldDebtUnitRecord.getDebtFee().compareTo(refundReq.getDebtFee()) == 0) {
                // 还款完成之后 更新金额和状态
                oldDebtUnitRecord.setDebtFee(BigDecimal.ZERO);
                oldDebtUnitRecord.setRepaymentStatus(1);
                updateById(oldDebtUnitRecord);
            }
        } else {
            // 原订单挂账记录为 已还款，说明此次还款为余出
            unitRecordDO.setExcessFlag(1);
        }
        unitRecordDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_DEBT_UNIT_RECORD));
        unitRecordDO.setGmtCreate(LocalDateTime.now());
        unitRecordDO.setGmtModified(LocalDateTime.now());
        unitRecordDO.setOrderGuid(Long.valueOf(refundReq.getRefundOrderGuid()));
        unitRecordDO.setCreateStaffGuid(oldDebtUnitRecord.getCreateStaffGuid());
        unitRecordDO.setCreateStaffName(oldDebtUnitRecord.getCreateStaffName());
        unitRecordDO.setUpdateStaffGuid(UserContextUtils.getUserGuid());
        unitRecordDO.setUpdateStaffName(UserContextUtils.getUserName());
        unitRecordDO.setDebtInvoiceCode(redisHelper.generateDebtInvoiceCode(UserContextUtils.get().getStoreNo(), UserContextUtils.getStoreGuid()));
        unitRecordDO.setDebtFee(refundReq.getDebtFee());
        unitRecordDO.setPaymentType(PaymentTypeEnum.OFFLINE_REFUND.getCode());
        unitRecordDO.setRepaymentFee(refundReq.getDebtFee());
        unitRecordDO.setRepaymentStatus(1);
        log.info("保存挂账退款信息：{}", JacksonUtils.writeValueAsString(unitRecordDO));
        save(unitRecordDO);
        // 变更额度
        DebtCreditChangeDTO changeDTO = new DebtCreditChangeDTO();
        changeDTO.setChangeType(1);
        changeDTO.setCreditChange(refundReq.getDebtFee());
        changeDTO.setUnitGuid(unitRecordDO.getUnitGuid());
        debtUnitService.creditLimitChange(changeDTO);
    }

    @Override
    public DebtUnitRecordTotalDTO queryDebtUnitTotalByUnitGuid(String unitGuid) {
        DebtUnitRecordTotalDTO totalDTO = new DebtUnitRecordTotalDTO();
        if (StringUtils.isBlank(unitGuid)) {
            return totalDTO;
        }
        DebtUnitDO unitDO = debtUnitService.getById(unitGuid);
        if (Objects.isNull(unitDO)) {
            log.error("无挂账单位信息！unitGuid:{}", unitGuid);
            return totalDTO;
        }
        totalDTO.setCreditLimitTotal(unitDO.getCreditLimit());
        BigDecimal debtUnitTotal = debtUnitMapper.queryDebtUnitTotal(unitDO.getGuid());
        debtUnitTotal = Optional.ofNullable(debtUnitTotal).orElse(BigDecimal.ZERO);
        totalDTO.setDebtFeeTotal(debtUnitTotal);
        totalDTO.setName(unitDO.getName());
        totalDTO.setGuid(unitDO.getGuid());
        totalDTO.setAvailableFeeTotal(unitDO.getCreditLimit().subtract(totalDTO.getDebtFeeTotal()));
        log.info(" queryDebtUnitTotal 最终返回参数:{}", JacksonUtils.writeValueAsString(totalDTO));
        return totalDTO;
    }

    @Override
    public DebtRecordH5RespDTO queryDebtRecordH5(DebtUnitLoginH5ReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getContactTel())) {
            throw new BusinessException("单位联系电话不能为空！");
        }
        //h5页面header中无企业信息，手动切库
        dynamicHelper.changeDatasource(reqDTO.getEnterpriseGuid());
        //查询单位信息
        DebtUnitDO unitDO = debtUnitService.getOne(new LambdaQueryWrapper<DebtUnitDO>()
                .eq(DebtUnitDO::getContactTel, reqDTO.getContactTel())
                .eq(DebtUnitDO::getPassword, reqDTO.getPassword())
        );
        if (unitDO == null) {
            throw new BusinessException("手机号或密码有误！");
        }
        //查询挂账记录
        DebtUnitRecordQuery dbQuery = new DebtUnitRecordQuery();
        dbQuery.setUnitGuid(unitDO.getGuid());
        dbQuery.setRepaymentStatus(reqDTO.getRepaymentStatus());
        IPage<DebtUnitRecordDO> page = debtUnitMapper.pageDebtUnitRecord(new PageAdapter<>(reqDTO), dbQuery);
        //定义挂账记录返回列表
        List<DebtRecordDetailH5DTO> recordDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            List<DebtUnitRecordDO> recordDOList = page.getRecords();
            if (CollectionUtils.isNotEmpty(recordDOList)) {
                recordDOList.forEach(r -> {
                    DebtRecordDetailH5DTO recordDetail = new DebtRecordDetailH5DTO();
                    recordDetail.setOrderNo(r.getDebtInvoiceCode());
                    recordDetail.setDebtAmount(r.getDebtFee());
                    recordDetail.setDebtDate(r.getGmtCreate());
                    recordDetail.setRepaymentAmount(r.getRepaymentFee());
                    recordDetail.setRepaymentStatus(r.getRepaymentStatus());
                    recordDetails.add(recordDetail);
                });
            }
        }
        PageAdapter<DebtRecordDetailH5DTO> pageAdapter = new PageAdapter<>(page, recordDetails);
        DebtRecordH5RespDTO respDTO = new DebtRecordH5RespDTO();
        respDTO.setCreditLimit(unitDO.getCreditLimit());
        respDTO.setCode(unitDO.getCode());
        respDTO.setName(unitDO.getName());
        respDTO.setRecordDetails(pageAdapter);

        QueryWrapper<DebtUnitRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("sum(debt_fee) debtFeeTotal", "sum(repayment_fee) repaymentFeeTotal")
                .eq("unit_code", unitDO.getCode())
                .eq("repayment_status", 0)
                .eq("is_delete", Boolean.FALSE);

        List<Map<String, Object>> resultList = debtUnitMapper.selectMaps(queryWrapper);
        log.info("查询出的流水合计：{}", JacksonUtils.writeValueAsString(resultList));
        if (CollectionUtils.isNotEmpty(resultList) && MapUtils.isNotEmpty(resultList.get(0))) {
            //将记录中的挂账金额与还款金额合计
            Map<String, Object> resultMap = resultList.get(0);
            Object debtFeeTotal = resultMap.get("debtFeeTotal");
            Object repaymentFeeTotal = resultMap.get("repaymentFeeTotal");
            if (Objects.nonNull(debtFeeTotal)) {
                respDTO.setDebtAmount(new BigDecimal(debtFeeTotal.toString()));
            } else {
                respDTO.setDebtAmount(BigDecimal.ZERO);
            }
            if (Objects.nonNull(repaymentFeeTotal)) {
                respDTO.setRepaymentAmount(new BigDecimal(repaymentFeeTotal.toString()));
            } else {
                respDTO.setRepaymentAmount(BigDecimal.ZERO);
            }
        } else {
            //没有挂账记录，则金额都为0
            respDTO.setDebtAmount(BigDecimal.ZERO);
            respDTO.setRepaymentAmount(BigDecimal.ZERO);
        }
        //可用额度 实时计算
        respDTO.setCreditLimitLeft(unitDO.getCreditLimit().subtract(respDTO.getDebtAmount()));
        log.info("H5最终返回参数:{}", JacksonUtils.writeValueAsString(respDTO));
        return respDTO;
    }

    @Override
    public BigDecimal calculateDebtFeeTotal(String unitGuid) {
        QueryWrapper<DebtUnitRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("sum(debt_fee) debtFeeTotal", "sum(repayment_fee) repaymentFeeTotal")
                .eq("unit_guid", unitGuid)
                .eq("repayment_status", 0)
                .eq("is_delete", Boolean.FALSE);

        List<Map<String, Object>> resultList = debtUnitMapper.selectMaps(queryWrapper);
        log.info("查询出的流水合计：{}", JacksonUtils.writeValueAsString(resultList));
        if (CollectionUtils.isNotEmpty(resultList) && MapUtils.isNotEmpty(resultList.get(0))) {
            //将记录中的挂账金额与还款金额合计
            Map<String, Object> resultMap = resultList.get(0);
            Object debtFeeTotal = resultMap.get("debtFeeTotal");
            if (Objects.nonNull(debtFeeTotal)) {
                return new BigDecimal(debtFeeTotal.toString());
            } else {
                return BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    private Page<DebtUnitRecordPageRespDTO> transformDetailsDTO(IPage<DebtUnitRecordDO> page) {
        List<DebtUnitRecordDO> records = page.getRecords();
        List<DebtUnitRecordPageRespDTO> respDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(records)) {
            log.info("分页数据为空！");
            return new PageAdapter<>(page, respDTOList);
        }
        for (DebtUnitRecordDO record : records) {
            DebtUnitRecordPageRespDTO debtUnitRecordPageRespDTO = new DebtUnitRecordPageRespDTO();
            BeanUtil.copyProperties(record, debtUnitRecordPageRespDTO);
            if (StringUtils.isNotEmpty(record.getEvidences())) {
                debtUnitRecordPageRespDTO.setEvidenceList(JacksonUtils.toObjectList(String.class, record.getEvidences()));
            }
            if (StringUtils.isNotEmpty(record.getUpdateStaffName())) {
                debtUnitRecordPageRespDTO.setCreateStaffName(record.getUpdateStaffName());
            }
            respDTOList.add(debtUnitRecordPageRespDTO);
        }
        log.info("组装好的的分页参数:{}", JacksonUtils.writeValueAsString(respDTOList));
        return new PageAdapter<>(page, respDTOList);
    }
}
