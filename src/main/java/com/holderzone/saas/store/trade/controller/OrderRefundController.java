package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.request.bill.OrderRefundReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.RecoveryReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.exception.OrderRefundLockException;
import com.holderzone.saas.store.trade.manager.OrderRefundManager;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;


/**
 * 订单退款
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/dine_in_bill/order/refund")
public class OrderRefundController {

    private final OrderRefundManager orderRefundManager;

    public static final String ORDER_REFUND_LOCK = "ORDER_REFUND_LOCK:%s";

    @ApiOperation(value = "查询可退款的订单详情")
    @GetMapping("/available/{orderGuid}")
    public DineinOrderDetailRespDTO getAvailableRefundDetail(@PathVariable String orderGuid) {
        log.info("查询可退款的订单详情入参：{}", JacksonUtils.writeValueAsString(orderGuid));
        return orderRefundManager.getAvailableRefundDetail(orderGuid);
    }

    @ApiOperation(value = "订单退款", notes = "订单退款")
    @PostMapping
    public String orderRefund(@RequestBody @Valid OrderRefundReqDTO orderRefundReqDTO) {
        log.info("订单退款入参：{}", JacksonUtils.writeValueAsString(orderRefundReqDTO));
        String lockKey = String.format(ORDER_REFUND_LOCK, orderRefundReqDTO.getOrderGuid());
        try {
            boolean lockResult = RedissonLockUtil.tryLock(lockKey, 10, 30);
            if (!lockResult) {
                throw new OrderRefundLockException("退款操作存在冲突，需重新提交");
            }
            return orderRefundManager.orderRefund(orderRefundReqDTO);
        } catch (Exception e) {
            log.error("订单退款失败,e:{}", printStackTrace(e));
            throw new BusinessException(e.getMessage());
        } finally {
            RedissonLockUtil.unlock(lockKey);
        }
    }

    @ApiOperation(value = "订单部分退款是否超时", notes = "订单部分退款是否超时")
    @PostMapping("/time_limit")
    public Boolean refundTimeLimit(@RequestBody OrderRefundReqDTO orderRefundReqDTO) {
        log.info("订单部分退款是否超时入参：{}", JacksonUtils.writeValueAsString(orderRefundReqDTO));
        return orderRefundManager.refundTimeLimit(orderRefundReqDTO.getOrderGuid());
    }

    private String printStackTrace(Exception e) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        e.printStackTrace(new PrintStream(baos));
        return baos.toString();
    }
}
