package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.trade.bo.OrderPackageSubgroupBO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemChangesDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemRecordDO;

import java.util.List;


/**
 * 套餐换菜
 */
public interface PackageSubgroupChangesService {

    /**
     * 换菜
     */
    void changes(OrderPackageSubgroupBO packageSubgroupBiz);

    /**
     * 撤销
     */
    void cancel(OrderPackageSubgroupBO packageSubgroupBiz);

    /**
     * 商品明细记录
     */
    void addChangeOrderItemRecordDOList(OrderDO orderDO,
                                        List<OrderItemChangesDO> orderItemChangesDOList,
                                        List<OrderItemRecordDO> itemRecordSave);

    /**
     * 换菜明细添加原菜信息
     */
    void addOriginalItemInfo(DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

    /**
     * 换菜明细添加原菜信息
     */
    void addOriginalItemInfo(Long orderGuid, List<SubDineInItemDTO> changeSubDineInItemList);

}
