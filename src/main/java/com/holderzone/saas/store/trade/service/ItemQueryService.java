package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.item.req.ItemBarCodeReqDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemQueryService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-trade
 */
public interface ItemQueryService {

    List<DineInItemDTO> getAllItems(OrderDO orderGuid);

    void printItemBarCode(ItemBarCodeReqDTO itemBarCodeReqDTO);
}
