package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.order.request.bill.OrderRefundReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.RecoveryReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.trade.entity.bo.RefundOrderBO;

import java.time.LocalDateTime;

/**
 * 退款单打印service
 *
 * <AUTHOR>
 * @date 2025/7/5 11:27
 */
public interface OrderRefundPrintService {

    /**
     * 退款后打印
     *
     * @param orderRefundReqDTO 退款请求
     * @param refundOrderBO     退款订单
     */
    void printByRefund(OrderRefundReqDTO orderRefundReqDTO, RefundOrderBO refundOrderBO);

    /**
     * 反结账后打印
     *
     * @param recoveryReqDTO 反结账单申请
     * @param orderDetail    订单详情
     * @param now            退款时间
     */
    void printByRecovery(RecoveryReqDTO recoveryReqDTO, DineinOrderDetailRespDTO orderDetail, LocalDateTime now);
}
