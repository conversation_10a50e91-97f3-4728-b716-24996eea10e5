package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.request.item.TransferItemReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.OrderItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.FastFoodAutoDistributeTaskDTO;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-trade
 */
public interface KdsService {

    void prepare(DineinOrderDetailRespDTO orderDetail, BaseDTO baseDTO);

    void refound(DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

    /**
     * 换菜
     */
    void changes(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, Boolean cancelFlag,
                 List<SubDineInItemDTO> originalSubDineInItemList, List<SubDineInItemDTO> changeSubDineInItemList);

    /**
     * 快餐自动出餐延迟队列
     */
    void dealFastFoodAutoDistributeDelayedTask(FastFoodAutoDistributeTaskDTO taskDTO);

    void transferItem(TransferItemReqDTO transferReq, DineinOrderDetailRespDTO orderDetailRespDTO, HashMap<Long, OrderItemDTO> old2NewMap);
}
