package com.holderzone.saas.store.trade.bo.builder;

import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.enums.trade.StockExecuteOperateEnum;
import com.holderzone.saas.store.trade.bo.StockErpBO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;

import java.util.List;

public class StockErpBizBuilder {

    private StockErpBizBuilder() {

    }

    public static StockErpBO build() {
        return new StockErpBO();
    }

    public static StockErpBO build(String operateCode, List<DineInItemDTO> items, String orderGuid, OrderDO orderDO) {
        StockErpBO biz = build();
        biz.setOperateCode(operateCode);
        biz.setItems(items);
        biz.setOrderGuid(orderGuid);
        biz.setOrderDO(orderDO);
        return biz;
    }


    public static StockErpBO build(DepositErpSyncDTO depositErpSyncDTO) {
        StockErpBO biz = build();
        biz.setOperateCode(StockExecuteOperateEnum.ADJUST.getCode());
        biz.setDepositErpSyncDTO(depositErpSyncDTO);
        return biz;
    }

}
