package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.order.OrderMultiMemberDTO;
import com.holderzone.saas.store.dto.order.request.bill.BilMemberCardCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillMemberCardCalculateRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.trade.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @className CalculateService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-trade
 */
public interface CalculateService {

    /**
     * 结账时计算账单金额和获取订单详情
     *
     * @param billCalculateReqDTO billCalculateReqDTO
     * @return DineinOrderDetailRespDTO
     */
    DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO);


    DineinOrderDetailRespDTO checkVolume(BillCalculateReqDTO billCalculateReqDTO);

    //方法只用作构建结算页面显示的订单详细信息和计算每个菜的小计，金额和优惠计算移到外层
    DineinOrderDetailRespDTO getSingleOrderDetail(OrderDO orderDO);

    DiscountRuleBO getDiscountRuleBO(OrderDO orderDO, BillCalculateReqDTO billCalculateReqDTO, DineinOrderDetailRespDTO orderDetailRespDTO);

    /**
     * 保存优惠信息
     */
    void saveDiscountInfo(CreateFastFoodReqDTO createFastFoodReqDTO);

    /**
     * 订单关联多卡
     */
    void relationOrderMultiMember(OrderMultiMemberDTO reqDTO);

    /**
     * 查询订单关联多卡
     */
    List<OrderMultiMemberDTO> queryRelationOrderMultiMember(String orderGuid);

    /**
     * 订单关联麓豆会员
     */
    BillMemberCardCalculateRespDTO relationOrderLudouMember(BilMemberCardCalculateReqDTO cardCalculateReqDTO);

    /**
     * 订单麓豆会员退出登录
     */
    void removeOrderLudouMember(BilMemberCardCalculateReqDTO cardCalculateReqDTO);

}
