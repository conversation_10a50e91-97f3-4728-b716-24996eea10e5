package com.holderzone.saas.store.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.trade.entity.domain.AppendFeeDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GrouponMpService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-trade
 */
public interface AppendFeeService extends IService<AppendFeeDO> {

    BigDecimal getAndUpdateAppendFee(OrderDO orderDO,String areaGuid);

    /**
     * 根据传入的附加费信息计算附加费
     * 不查询实时的附加费
     */
    BigDecimal calculateAppendFee(OrderDO orderDO, List<SurchargeLinkDTO> surchargeLinkList);

    BigDecimal updateAppendFee(String orderGuid, int guestCount);

    List<AppendFeeDetailDTO> appendFeeList(SingleDataDTO singleDataDTO);

    List<AppendFeeDO> persistAppendFee(Long orderGuid);

    List<AppendFeeDO> persistAppendFee(String orderGuid);

    /**
     * 查询订单所有附加费详情（包含子单，一体机查询订单详情用）
     * @param orderGuid 订单guid
     * @return 附加费详情
     */
    List<AppendFeeDetailDTO> getOrderAllAppendFeeList(String orderGuid);

    List<SurchargeLinkDTO> getSurchargeLinkList(String orderGuid);

    List<SurchargeLinkDTO> getSurchargeLinkList(List<String> orderGuidList);

    BigDecimal getAndUpdateAppendFeeByOriginal(OrderDO orderDO);

}
