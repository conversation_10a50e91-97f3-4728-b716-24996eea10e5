package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.journaling.req.JournalAppBaseReqDTO;
import com.holderzone.saas.store.dto.journaling.req.SaleStatisticsByHoursReqDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreStatisticsAppReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreGatherReportRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreSaleItemDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreStatisticsAppRespDTO;
import com.holderzone.saas.store.dto.order.BusinessDayDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.UpdateOrderReqDTO;
import com.holderzone.saas.store.dto.reserve.UpdateOrderReserveReqDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderInvoiceStateReqDTO;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderMemberInfoReqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByDineAndFastOrderRespDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.enums.trade.OrderInvoiceStateEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderExtendsDO;
import com.holderzone.saas.store.trade.entity.dto.BusinessHisTrendDTO;
import com.holderzone.saas.store.trade.entity.enums.RecoveryTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.PageAdapter;
import com.holderzone.saas.store.trade.mapper.OrderMapper;
import com.holderzone.saas.store.trade.repository.feign.ResourceClientService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderExtendsService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
@AllArgsConstructor
@Slf4j
public class OrderServiceImpl extends ServiceImpl<OrderMapper, OrderDO> implements OrderService {

    private static final OrderTransform orderTransform = OrderTransform.INSTANCE;
    public static final String ORDER_GUID_NOT_NULL = "订单guid不能为空";
    public static final String ENTERPRISE_GUID_NOT_NULL = "企业guid不能为空";
    public static final String ORDER_NOT_FOUND = "订单不存在";

    private final OrderMapper orderMapper;

    private final DynamicHelper dynamicHelper;

    private final ResourceClientService resourceClientService;

    private final OrderExtendsService orderExtendsService;

    @Override
    public int selectRecoveryCount(String recoveryId) {
        return count(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getRecoveryId, recoveryId).ne
                (OrderDO::getRecoveryType, RecoveryTypeEnum.RETURN.getCode()));
    }

    @Override
    public List<OrderDO> listByRecoveryId(String recoveryId) {
        return list(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getRecoveryId, recoveryId));
    }

    @Override
    public OrderDO getByIdWithLock(Serializable id) {
        return getOne(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getGuid, id).last("for update"));
    }

    @Override
    public IPage<OrderInfoRespDTO> pageOrderInfo(IPage<OrderInfoRespDTO> page, DineInOrderListReqDTO reqDTO) {
        return baseMapper.pageOrderInfo(page, reqDTO);
    }

    /**
     * 判断当前时候是最后一单
     *
     * @param orderDO
     * @return
     */
    @Override
    public boolean booleanIsLastOrder(OrderDO orderDO) {
        if (orderDO.getMemberGuid() == null || orderDO.getMemberGuid().equals("0")) {
            return true;
        }
        //判断是否最后一单用结账时间来判断（Bug16918）
        return count(new LambdaQueryWrapper<OrderDO>().gt(OrderDO::getCheckoutTime, orderDO.getCheckoutTime())
                .eq(OrderDO::getState, StateEnum.SUCCESS.getCode())
                .eq(OrderDO::getMemberGuid, orderDO.getMemberGuid())) == 0;
    }

    @Override
    public List<OrderDO> listByMainOrderGuid(Serializable mainOrderGuid) {
        return list(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getMainOrderGuid, mainOrderGuid).eq
                (OrderDO::getUpperState, UpperStateEnum.SUB.getCode()));
    }

    @Override
    public List<OrderDO> otherListByMainOrderGuid(Long mainOrderGuid) {
        return list(new LambdaQueryWrapper<OrderDO>()
                .and(wrapper -> wrapper
                        .eq(OrderDO::getGuid, mainOrderGuid)
                        .eq(OrderDO::getUpperState, UpperStateEnum.SAME_MAIN.getCode())
                        .or()
                        .eq(OrderDO::getMainOrderGuid, mainOrderGuid)
                        .eq(OrderDO::getUpperState, UpperStateEnum.SAME_SUB.getCode())
                )
                .ne(OrderDO::getState, StateEnum.REFUNDED.getCode()));
    }

    @Override
    public List<OrderDO> otherListByMainOrderGuids(List<Long> mainOrderGuids) {
        if (CollectionUtils.isEmpty(mainOrderGuids)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<OrderDO>()
                .and(wrapper -> wrapper
                        .in(OrderDO::getGuid, mainOrderGuids)
                        .eq(OrderDO::getUpperState, UpperStateEnum.SAME_MAIN.getCode())
                        .or()
                        .in(OrderDO::getMainOrderGuid, mainOrderGuids)
                        .eq(OrderDO::getUpperState, UpperStateEnum.SAME_SUB.getCode())
                )
                .ne(OrderDO::getState, StateEnum.REFUNDED.getCode()));
    }

    @Override
    public List<OrderDO> listOrderIdByMainOrderGuid(String mainOrderGuid) {
        return list(new LambdaQueryWrapper<OrderDO>()
                .select(OrderDO::getGuid)
                .eq(OrderDO::getMainOrderGuid, mainOrderGuid)
                .eq(OrderDO::getUpperState, UpperStateEnum.SUB.getCode()));
    }

    @Override
    public List<OrderDO> listByOrderNoAndCreate(String orderNo, LocalDateTime gmtCreated) {
        return list(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getOrderNo, orderNo).eq(OrderDO::getGmtCreate, gmtCreated));
    }

    @Override
    public OrderDO getByIdWithCache(String id) {
        return getById(id);
    }

    @Override
    public boolean updateByIdWithDeleteCache(OrderDO orderDO) {
        return updateById(orderDO);
    }

    @Override
    public boolean updateBatchByIdWithDeleteCache(List<OrderDO> orderDOS) {
        return updateBatchById(orderDOS);
    }

    @Override
    public List<OrderDO> getListByIds(List<String> guidLists) {
        if (CollectionUtils.isEmpty(guidLists)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<OrderDO>().in(OrderDO::getGuid, guidLists));
    }

    @Override
    public List<OrderDO> getLocalUnpay(LocalQuery localQuery) {
        return list(
                new LambdaQueryWrapper<OrderDO>()
                        .eq(OrderDO::getState, StateEnum.READY.getCode())
                        .eq(OrderDO::getStoreGuid, localQuery.getStoreGuid()));
    }

    @Override
    public List<OrderDO> queryIsPayOrderList(List<String> guidLists) {
        if (CollectionUtil.isEmpty(guidLists)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<OrderDO>().in(OrderDO::getGuid, guidLists)
                .eq(OrderDO::getState, StateEnum.SUCCESS.getCode()));
    }


    @Override
    public Integer getTotalGuestCount(Long guid) {
        return orderMapper.getTotalGuestCount(String.valueOf(guid));
    }

    @Override
    public OrderDO findByEnterpriseGuid() {
        return orderMapper.findByEnterpriseGuid();
    }

    @Override
    public int updateOrderIsHandleByOrderGuid(Long guid, Integer isHandle) {
        return orderMapper.updateOrderIsHandleByOrderGuid(guid, isHandle);
    }

    /**
     * 统计商户后台/数据报表/订单统计 数据条数
     *
     * @param queryDTO 查询条件
     * @return 统计结果
     */
    @Override
    public Integer getOrderStatisticsCount(BusinessOrderStatisticsQueryDTO queryDTO) {
        return orderMapper.getOrderStatisticsCount(queryDTO);
    }

    /**
     * 获取商户后台/数据报表/订单统计 分页数据  考虑导出可能会使用这个方法 固没有返回OrderDO 直接返回DTO
     *
     * @param queryDTO 　查询条件
     * @return 分页数据
     */
    @Override
    public List<BusinessOrderStatisticsRespDTO> getOrderStatisticsPage(BusinessOrderStatisticsQueryDTO queryDTO) {
        return orderMapper.getOrderStatisticsPage(queryDTO);
    }

    @Override
    public BusinessOrderStatisticsCombinedRespDTO getOrderStatisticsCombined(BusinessOrderStatisticsQueryDTO queryDTO) {
        return orderMapper.getOrderStatisticsCombined(queryDTO);
    }

    @Override
    public BigDecimal getGrouponOrderStatisticsCombined(BusinessOrderStatisticsQueryDTO queryDTO) {
        return orderMapper.getGrouponOrderStatisticsCombined(queryDTO);
    }

    @Override
    public List<OrderDO> getMergeOrderList(String orderGuid, Integer upperState) {
        if (Objects.equals(UpperStateEnum.SUB.getCode(), upperState)) {
            OrderDO orderDO = getOne(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getGuid, orderGuid));
            if (orderDO == null) {
                return Collections.emptyList();
            }
            orderGuid = String.valueOf(orderDO.getMainOrderGuid());
        }
        if (StringUtils.isEmpty(orderGuid)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getGuid, orderGuid).or().eq(OrderDO::getMainOrderGuid, orderGuid));
    }

    @Override
    public List<OrderDO> getListByOriginalOrderGuid(Long guid) {
        return list(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getOriginalOrderGuid, guid));
    }

    @Override
    public List<OrderDO> getRefundListByOriginalOrderGuid(List<Long> orderGuids) {
        return list(new LambdaQueryWrapper<OrderDO>()
                .in(OrderDO::getOriginalOrderGuid, orderGuids)
                .eq(OrderDO::getRecoveryType, RecoveryTypeEnum.RETURN.getCode()));
    }

    @Override
    public int updateIsWaiters(List<String> orderGuidList) {
        return orderMapper.updateIsWaiters(orderGuidList);
    }


    @Override
    public boolean checkOrderPending(String memberInfoGuid) {
        OrderDO one = getOne(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getMemberGuid, memberInfoGuid)
                .in(OrderDO::getState,
                        Lists.newArrayList(StateEnum.READY.getCode(), StateEnum.PENDING.getCode(), StateEnum.FAILURE.getCode()))
                .last("limit 1")

        );
        return one != null;
    }

    /**
     * 调整单-正餐/快餐订单列表
     * 正餐、快餐展示近30天已结账的订单数据
     *
     * @param query 关键字和门店guid
     * @return 正餐/快餐订单信息列表
     */
    @Override
    public Page<AdjustByDineAndFastOrderRespDTO> pageDineAndFastOrder(AdjustByOrderListQuery query) {
        if (ObjectUtils.isEmpty(query.getTradeMode())) {
            throw new BusinessException("交易模式不能为空");
        }
        LambdaQueryWrapper<OrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderDO::getStoreGuid, UserContextUtils.getStoreGuid());
        queryWrapper.eq(OrderDO::getTradeMode, query.getTradeMode());
        queryWrapper.eq(OrderDO::getIsDelete, Boolean.FALSE);

        // 时间区间: 自定义时间范围
        if (query.getBeginTime() != null && query.getFinishTime() != null) {
            queryWrapper.ge(OrderDO::getGmtCreate, query.getBeginTime());
            queryWrapper.le(OrderDO::getGmtCreate, query.getFinishTime());
        } else {
            // 如果没有提供自定义时间范围，默认查询近30天的数据
            queryWrapper.ge(OrderDO::getGmtCreate, DateTimeUtils.now().minusDays(30).toLocalDate().atStartOfDay());
        }

        // 订单状态：已结账
        queryWrapper.eq(OrderDO::getState, StateEnum.SUCCESS.getCode());
        queryWrapper.ne(OrderDO::getUpperState, UpperStateEnum.SUB.getCode());

        //关键字
        String searchKey = query.getSearchKey();
        if (!StringUtils.isEmpty(searchKey)) {
            queryWrapper.and(i -> i.like(OrderDO::getOrderNo, searchKey)
                    .or()
                    .like(OrderDO::getDiningTableName, searchKey)
                    .or()
                    .like(OrderDO::getMark, searchKey));
        }
        queryWrapper.orderByDesc(OrderDO::getGmtCreate);

        IPage<OrderDO> orderPage = this.page(new PageAdapter<>(query), queryWrapper);
        List<OrderDO> orderList = orderPage.getRecords();
        if (CollectionUtils.isEmpty(orderList)) {
            return new PageAdapter<>(orderPage.getSize(), orderPage.getCurrent(), orderPage.getTotal());
        }
        List<AdjustByDineAndFastOrderRespDTO> adjustOrderList = orderTransform.orderDOList2DAFAdjustOrderList(orderList);
        for (AdjustByDineAndFastOrderRespDTO orderRespDTO : adjustOrderList) {
            Integer state = orderRespDTO.getState();
            if (StateEnum.SUCCESS.getCode() == state) {
                orderRespDTO.setState(2);
            }
            if (null == orderRespDTO.getCheckoutStaffName()) {
                orderRespDTO.setCheckoutStaffName("");
            }
        }
        return new PageAdapter<>(orderPage, adjustOrderList);
    }

    @Override
    public void updateAdjustStateIsTrue(Long guid) {
        baseMapper.updateAdjustStateIsTrue(guid);
    }

    /**
     * 随行红包使用设置
     *
     * @param updateOrderReqDTO 订单guid
     * @return 结果
     */
    @Override
    public Boolean setFollowRedPacket(UpdateOrderReqDTO updateOrderReqDTO) {
        String orderGuid = updateOrderReqDTO.getOrderGuid();
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException("订单guid为空");
        }
        if (StringUtils.isEmpty(updateOrderReqDTO.getState())) {
            throw new BusinessException("更改状态为空");
        }
        OrderDO orderDO = this.getById(orderGuid);
        if (ObjectUtils.isEmpty(orderDO)) {
            log.warn("未查询到订单 orderGuid={}", orderGuid);
            return Boolean.FALSE;
        }
        orderDO.setIsFollowRedPacket(updateOrderReqDTO.getState());
        return this.updateById(orderDO);
    }

    /**
     * 根据订单查询订单状态
     *
     * @param orderGuid 订单guid
     * @return 订单状态 (-1：未查询到订单 1：未结账， 2：已结账， 3：已退款，4：已作废)
     */
    @Override
    public Integer getOrderStateByGuid(String orderGuid) {
        OrderDO orderDO = this.getById(orderGuid);
        if (ObjectUtils.isEmpty(orderDO)) {
            log.warn("未查询到订单 orderGuid={}", orderGuid);
            return -1;
        }
        return orderDO.getState();
    }

    /**
     * 根据订单查询订单金额
     *
     * @param orderGuid 订单guid
     * @return 订单金额
     */
    @Override
    public BigDecimal getOrderFeeByGuid(String orderGuid) {
        OrderDO orderDO = this.getById(orderGuid);
        if (ObjectUtils.isEmpty(orderDO)) {
            log.warn("未查询到订单 orderGuid={}", orderGuid);
            return BigDecimal.ZERO;
        }
        return orderDO.getOrderFee();
    }

    @Override
    public List<BusinessDayDTO> queryOrderBusinessDay(List<BusinessDayDTO> businessDayDTOList) {
        /*Map<String, List<BusinessDayDTO>> enterpriseMap = businessDayDTOList.stream()
                .filter(b -> !StringUtils.isEmpty(b.getEnterpriseGuid()))
                .collect(Collectors.groupingBy(BusinessDayDTO::getEnterpriseGuid));
        enterpriseMap.forEach((enterpriseGuid, storeList) -> {
            if (CollectionUtils.isEmpty(storeList)) {
                return;
            }
            Map<String, List<BusinessDayDTO>> storeMap = storeList.stream()
                    .filter(s -> !StringUtils.isEmpty(s.getStoreGuid()))
                    .collect(Collectors.groupingBy(BusinessDayDTO::getStoreGuid));
            try {
                storeMap.forEach((storeGuid, orderList) -> {
                    if (CollectionUtils.isEmpty(orderList)) {
                        return;
                    }
                    List<String> orderNoList = orderList.stream()
                            .map(BusinessDayDTO::getOrderNo)
                            .filter(orderNo -> !StringUtils.isEmpty(orderNo))
                            .distinct()
                            .collect(Collectors.toList());

                    UserContextUtils.putErp(enterpriseGuid);
                    dynamicHelper.changeDatasource(enterpriseGuid);
                    List<OrderDO> orderDOList = new ArrayList<>();
                    orderDOList = this.list(new LambdaQueryWrapper<OrderDO>()
                            .eq(OrderDO::getStoreGuid, storeGuid)
                            .in(OrderDO::getOrderNo, getInSql(orderNoList)));
                    orderDOList.forEach(orderDO -> {
                        if (ObjectUtils.isEmpty(orderDO.getBusinessDay())) {
                            orderDO.setBusinessDay(orderDO.getCheckoutTime().toLocalDate());
                        }
                    });
                    Map<String, OrderDO> orderMap = orderDOList.stream()
                            .collect(Collectors.toMap(OrderDO::getOrderNo, Function.identity(), (v1, v2) -> v2));

                    orderList.forEach(order -> {
                        OrderDO orderDO = orderMap.get(order.getOrderNo());
                        if (!ObjectUtils.isEmpty(orderDO)) {
//                            order.setBusinessDay(orderDO.getBusinessDay());
                        }
                    });
                });
            } catch (Exception e) {
                log.error("企业guid={},异常信息={}", enterpriseGuid, e.getMessage());
            }
        });*/
        return businessDayDTOList;
    }

    public String getInSql(List<String> idList) {
        StringBuilder stringBuffer = new StringBuilder();
        int inNum = 1;
        for (int i = 0; i < idList.size(); i++) {
            if (i == (idList.size() - 1)) {
                stringBuffer.append("'").append(idList.get(i)).append("'");
            } else if (inNum == 1000) {
                stringBuffer.append("'").append(idList.get(i)).append("')OR id IN (");
                inNum = 1;
            } else {
                stringBuffer.append("'").append(idList.get(i)).append("',");
                inNum++;
            }
        }
        return "(" + stringBuffer.toString() + ")";
    }

    @Override
    public BusinessDataRespDTO listOrderBusinessData(JournalAppBaseReqDTO reqDTO) {
        return orderMapper.listOrderBusinessData(reqDTO);
    }

    @Override
    public List<BusinessHisTrendDTO> listOrderBusinessHisTrend(JournalAppBaseReqDTO reqDTO) {
        return orderMapper.listOrderBusinessHisTrend(reqDTO);
    }

    @Override
    public IPage<StoreGatherReportRespDTO> listStoreGatherBusiness(IPage<StoreGatherReportRespDTO> iPage, StoreGatherReportReqDTO storeGatherReportReq) {
        return orderMapper.listStoreGatherBusiness(iPage, storeGatherReportReq);
    }

    @Override
    public List<StoreStatisticsAppRespDTO.StoreStatisticsDetailDTO> saleStoreStatistics(StoreStatisticsAppReqDTO storeStatisticsAppReqDTO) {
        return orderMapper.saleStoreStatistics(storeStatisticsAppReqDTO);
    }

    @Override
    public List<StoreSaleItemDTO> saleByHoursStatistics(SaleStatisticsByHoursReqDTO statisticsByHoursReqDTO) {
        return orderMapper.saleByHoursStatistics(statisticsByHoursReqDTO);
    }

    @Override
    public void updateRefundAmountByGuid(Long orderGuid, BigDecimal refundAmount) {
        UpdateWrapper<OrderDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(OrderDO::getGuid, orderGuid);
        uw.lambda().set(OrderDO::getRefundAmount, refundAmount);
        if (!update(uw)) {
            throw new BusinessException("更新退款金额失败");
        }
    }

    @Override
    public void addRefundOrderGuid(Long orderGuid, Long refundOrderGuid) {
        UpdateWrapper<OrderDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(OrderDO::getGuid, orderGuid);
        uw.lambda().set(OrderDO::getRefundOrderGuid, refundOrderGuid);
        update(uw);
    }

    @Override
    public List<UserBriefDTO> getCheckoutStaffs(BusinessOrderStatisticsQueryDTO businessOrderStatisticsQueryDTO) {
        List<UserBriefDTO> checkoutStaffs = orderMapper.getCheckoutStaffs(businessOrderStatisticsQueryDTO);

        return checkoutStaffs.stream()
                //排除为空的数据
                .filter(userBriefDTO -> Objects.nonNull(userBriefDTO) &&
                        !StringUtils.isEmpty(userBriefDTO.getUserGuid()) &&
                        !StringUtils.isEmpty(userBriefDTO.getUserName()) &&
                        //排除checkout_staff_guid为openId的(只留全数字的)
                        userBriefDTO.getUserGuid().chars().allMatch(Character::isDigit))
                .collect(Collectors.toList());
    }

    @Override
    public void updateOrderIsUpdatedEs(String orderGuid) {
        log.info("更新订单是否结账释放订单,orderGuid={}", orderGuid);
        orderMapper.updateOrderIsUpdatedEs(Long.parseLong(orderGuid));
    }

    @Override
    public List<String> queryReadyOrder(SingleListDTO singleListDTO) {
        if (ObjectUtils.isEmpty(singleListDTO) || CollectionUtils.isEmpty(singleListDTO.getList())) {
            return Lists.newArrayList();
        }
        List<OrderDO> orderDOList = orderMapper.selectList(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getState, StateEnum.READY.getCode())
                .in(OrderDO::getGuid, singleListDTO.getList()));
        if (CollectionUtils.isEmpty(orderDOList)) {
            return Lists.newArrayList();
        }
        return orderDOList.stream()
                .map(o -> String.valueOf(o.getGuid()))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public void updateOrderReserve(UpdateOrderReserveReqDTO reqDTO) {
        checkOrder(reqDTO.getOrderGuid());
        orderMapper.updateOrderReserve(reqDTO);
    }

    @Override
    public void updateOrderMemberInfo(UpdateOrderMemberInfoReqDTO reqDTO) {
        checkOrder(reqDTO.getOrderGuid());
        orderMapper.updateOrderMemberInfo(reqDTO);
    }

    @Override
    public void updateOrderInvoiceState(UpdateOrderInvoiceStateReqDTO reqDTO) {
        if (!Objects.equals(OrderInvoiceStateEnum.SUCCEED.getCode(), reqDTO.getStatus())) {
            log.warn("[开票失败直接返回]reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        String orderNo = reqDTO.getOrderNo();
        String[] split = orderNo.split("-");
        String enterpriseGuid = split[1];
        if (StringUtils.isEmpty(enterpriseGuid)) {
            throw new BusinessException(ENTERPRISE_GUID_NOT_NULL);
        }
        UserContextUtils.putErp(enterpriseGuid);
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);

        String orderGuid = split[0];
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException(ORDER_GUID_NOT_NULL);
        }
        OrderDO orderDO = getById(orderGuid);
        if (ObjectUtils.isEmpty(orderDO)) {
            throw new BusinessException(ORDER_NOT_FOUND);
        }
        OrderExtendsDO orderExtendsDO = orderExtendsService.getOne(new LambdaQueryWrapper<OrderExtendsDO>()
                .eq(OrderExtendsDO::getGuid, orderGuid));
        if (ObjectUtils.isEmpty(orderExtendsDO)) {
            orderExtendsDO = new OrderExtendsDO();
            orderExtendsDO.setGuid(Long.valueOf(orderGuid));
            orderExtendsDO.setStoreGuid(orderDO.getStoreGuid());
            orderExtendsDO.setBusinessDay(orderDO.getBusinessDay());
            orderExtendsDO.setIsInvoice(Objects.equals(OrderInvoiceStateEnum.SUCCEED.getCode(), reqDTO.getStatus()));
            orderExtendsDO.setInvoiceCode(reqDTO.getInvoiceNo());
        } else {
            orderExtendsDO.setIsInvoice(Objects.equals(OrderInvoiceStateEnum.SUCCEED.getCode(), reqDTO.getStatus()));
            orderExtendsDO.setInvoiceCode(reqDTO.getInvoiceNo());
        }
        orderExtendsService.saveOrUpdate(orderExtendsDO);
    }

    @Override
    public List<String> listPaymentTypeName() {
        return orderMapper.listPaymentTypeName();
    }

    @Override
    public void updateSameOrderFeeForCombine(Long guid) {
        orderMapper.updateSameOrderFeeForCombine(guid);
    }

    private void checkOrder(String orderGuid) {
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException(ORDER_GUID_NOT_NULL);
        }
        OrderDO orderDO = getById(orderGuid);
        if (ObjectUtils.isEmpty(orderDO)) {
            throw new BusinessException(ORDER_NOT_FOUND);
        }
    }

}
