package com.holderzone.saas.store.trade.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.constant.RedisKeyConstant;
import com.holderzone.saas.store.dto.member.pay.RedPacketSettleAccountsVO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadPayInfoReqDTO;
import com.holderzone.saas.store.trade.entity.constant.OrderRedisConstant;
import com.holderzone.saas.store.trade.entity.domain.OrderAbnormalRecordDO;
import com.holderzone.saas.store.trade.entity.dto.BillPayInfoDTO;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.service.RedisService;
import com.holderzone.saas.store.trade.utils.RedisKeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 官方文档 https://redis.io/commands/eval
 * 中翻文档 https://www.cnblogs.com/PatrickLiu/p/8656675.html
 * <p>
 * JacksonUtils.writeValueAsString()
 * Integer类型 eg: 1
 * String类型 eg: "1" note: 引号也存进redis了
 * Object类型 eg: ["com.holder.saas.print.entity.domain.PrintRecordDO",{"deviceId":"123456"}]
 * List类型 eg: ["java.util.Arrays$ArrayList",[["com.holder.saas.print.entity.domain.PrintRecordDO",
 * {"deviceId":"123456"}]]]
 * List嵌套类型 eg: ["java.util.Arrays$ArrayList",[["java.util.Arrays$ArrayList",[["com.holder.saas.print.entity.domain
 * .PrintRecordDO",{"deviceId":"123456"}]]]]]
 * 以上即是 lua 脚本中如下代码的编写原因：
 * cjson.decode(ARGV[?]) 针对String和包装类型
 * cjson.decode(ARGV[?])[2] 针对Object、List等(非String和包装类型)类型
 *
 * <AUTHOR>
 * @version 1.0
 * @className RedisServiceImpl
 * @date 2018/12/27 11:12
 * @description 缓存实现类
 * @program holder-saas-store-table
 */
@Slf4j
@Service
@SuppressWarnings("unchecked")
public class RedisServiceImpl implements RedisService {

    private final RedisTemplate redisTemplate;

    private final RedisHelper redisHelper;


    @Autowired
    public RedisServiceImpl(RedisTemplate redisTemplate, RedisHelper redisHelper) {
        this.redisTemplate = redisTemplate;
        this.redisHelper = redisHelper;
    }


    @Override
    public void putTradeVersion(String orderGuid) {
        String key = RedisKeyUtil.getHstOrderVersionKey(orderGuid);
        redisHelper.generated(key, getEndTime());
    }

    @Override
    public void batchPutTradeVersion(List<String> orderGuids) {
        HashMap<String, Long> batchPutTradeVersionMap = Maps.newHashMap();
        for (String orderGuid : orderGuids) {
            batchPutTradeVersionMap.put(orderGuid, 1L);
        }
        redisTemplate.opsForValue().multiSet(batchPutTradeVersionMap);
    }

    @Override
    public void putAbnormalOrderRecord(String orderGuid, OrderAbnormalRecordDO orderAbnormalRecordDO) {
        String redisKey = RedisKeyUtil.getAbnormalOrder(orderGuid);
        redisHelper.setEx(redisKey, JacksonUtils.writeValueAsString(orderAbnormalRecordDO), 1, TimeUnit.DAYS);
    }

    @Override
    public OrderAbnormalRecordDO getAbnormalOrderRecord(String orderGuid) {
        String redisKey = RedisKeyUtil.getAbnormalOrder(orderGuid);
        String redisValue = redisHelper.get(redisKey);
        return Optional.ofNullable(redisValue).map(o -> JacksonUtils.toObject(OrderAbnormalRecordDO.class, redisValue)).orElse(null);
    }

    @Override
    public void putBillPayInfo(String orderGuid, BillPayInfoDTO billPayReqDTO) {
        String redisKey = RedisKeyUtil.getBillPayInfo(orderGuid);
        redisHelper.setEx(redisKey, JacksonUtils.writeValueAsString(billPayReqDTO), 1, TimeUnit.DAYS);
    }


    @Override
    public BillPayInfoDTO getBillPayInfo(String orderGuid) {
        String redisKey = RedisKeyUtil.getBillPayInfo(orderGuid);
        String redisValue = redisHelper.get(redisKey);
        return Optional.ofNullable(redisValue).map(o -> JacksonUtils.toObject(BillPayInfoDTO.class, redisValue)).orElse(null);
    }

    @Override
    public void deleteBillPayInfo(String orderGuid) {
        String redisKey = RedisKeyUtil.getBillPayInfo(orderGuid);
        redisHelper.delete(redisKey);
    }

    public void putOrderPaymentInitiateMethod(String orderGuid, String paymentInitiateMethod) {
        String redisKey = RedisKeyUtil.getOrderPaymentInitiateMethod(orderGuid);
        redisHelper.setEx(redisKey, paymentInitiateMethod, 3, TimeUnit.MINUTES);
    }

    public String getOrderPaymentInitiateMethod(String orderGuid) {
        String redisKey = RedisKeyUtil.getOrderPaymentInitiateMethod(orderGuid);
        return redisHelper.get(redisKey);
    }

    public void deleteOrderPaymentInitiateMethod(String orderGuid) {
        String redisKey = RedisKeyUtil.getOrderPaymentInitiateMethod(orderGuid);
        redisHelper.delete(redisKey);
    }

    public void putOrderPayGuid(String orderGuid, String payGuid) {
        String redisKey = RedisKeyUtil.getOrderPayGuid(orderGuid);
        redisHelper.setEx(redisKey, payGuid, 3, TimeUnit.MINUTES);
    }

    public String getOrderPayGuid(String orderGuid) {
        String redisKey = RedisKeyUtil.getOrderPayGuid(orderGuid);
        return redisHelper.get(redisKey);
    }

    public void deleteOrderPayGuid(String orderGuid) {
        String redisKey = RedisKeyUtil.getOrderPayGuid(orderGuid);
        redisHelper.delete(redisKey);
    }

    /**
     * 加菜商品信息存redis
     *
     * @param key           订单guid+下单表guid，以:分割
     * @param createDineDTO 加菜商品信息
     */
    @Override
    public void putPadOrderAddItemInfo(String key, CreateDineInOrderReqDTO createDineDTO) {
        String redisKey = RedisKeyUtil.getPadOrderAddItemInfo(key);
        redisHelper.setEx(redisKey, JacksonUtils.writeValueAsString(createDineDTO), 1, TimeUnit.DAYS);
    }

    /**
     * 取加菜商品信息
     *
     * @param key 订单guid+下单表guid，以:分割
     * @return 加菜商品信息
     */
    @Override
    public CreateDineInOrderReqDTO getPadOrderAddItemInfo(String key) {
        String redisKey = RedisKeyUtil.getPadOrderAddItemInfo(key);
        String redisValue = redisHelper.get(redisKey);
        return Optional.ofNullable(redisValue).map(o -> JacksonUtils.toObject(CreateDineInOrderReqDTO.class, redisValue))
                .orElse(null);
    }

    /**
     * 保存pad支付信息到缓存
     *
     * @param orderGuid        订单guid
     * @param padPayInfoReqDTO pad支付信息
     */
    @Override
    public void putPadPayInfo(String orderGuid, PadPayInfoReqDTO padPayInfoReqDTO) {
        String redisKey = RedisKeyUtil.getPadPayInfo(orderGuid);
        redisHelper.setEx(redisKey, JacksonUtils.writeValueAsString(padPayInfoReqDTO), 1, TimeUnit.DAYS);
    }

    /**
     * 获取缓存pad支付信息
     *
     * @param orderGuid 订单guid
     * @return pad支付信息
     */
    @Override
    public PadPayInfoReqDTO getPadPayInfo(String orderGuid) {
        String redisKey = RedisKeyUtil.getPadPayInfo(orderGuid);
        String redisValue = redisHelper.get(redisKey);
        return Optional.ofNullable(redisValue).map(o -> JacksonUtils.toObject(PadPayInfoReqDTO.class, redisValue))
                .orElse(null);
    }

    /**
     * 删除缓存pad支付信息
     *
     * @param orderGuid 订单guid
     */
    @Override
    public void deletePadPayInfo(String orderGuid) {
        String redisKey = RedisKeyUtil.getPadPayInfo(orderGuid);
        redisHelper.delete(redisKey);
    }

    /**
     * 保存pad转台信息
     *
     * @param redisKey 键
     * @param value    值
     */
    @Override
    public void putPadTurnInfo(String redisKey, String value) {
        redisHelper.setEx(redisKey, value, 1, TimeUnit.DAYS);
    }

    /**
     * 获取pad转台信息
     *
     * @param redisKey 键
     * @return 值
     */
    @Override
    public String getPadTurnInfo(String redisKey) {
        return redisHelper.get(redisKey);
    }

    /**
     * 删除pad转台信息
     *
     * @param redisKey 键
     */
    @Override
    public void deletePadTurnInfo(String redisKey) {
        redisHelper.delete(redisKey);
    }

    /**
     * 获取随行红包信息
     *
     * @param redPacketInfoKey 随心红包信息key
     * @return 随行红包信息列表
     */
    @Override
    public List<RedPacketSettleAccountsVO> getRedPacketInfo(String redPacketInfoKey) {
        String data = redisHelper.get(redPacketInfoKey);
        return JSONObject.parseArray(data, RedPacketSettleAccountsVO.class);
    }

    /**
     * 删除随行红包信息
     *
     * @param redisKey 随行红包信息key
     */
    @Override
    public void deleteRedPacketInfo(String redisKey) {
        redisHelper.delete(redisKey);
    }

    /**
     * 获取订单金额
     * 加菜退菜时会存一分到缓存，没有则用数据库的
     *
     * @param orderGuids 订单guid列表
     * @return 订单金额Map
     */
    @Override
    public Map<String, BigDecimal> batchGetOrderFee(List<String> orderGuids) {
        Map<String, BigDecimal> orderFeeMap = new HashMap<>();
        orderGuids.forEach(orderGuid -> {
            String orderFee = redisHelper.get(OrderRedisConstant.REDIS_ORDER_FEE + orderGuid);
            if (!StringUtils.isEmpty(orderFee)) {
                orderFeeMap.put(orderGuid, new BigDecimal(orderFee));
            }
        });
        return orderFeeMap;
    }

    @Override
    public void deleteAdvancePay(String orderGUID) {
        String advanceLockKey = String.format(RedisKeyConstant.ADVANCE_PAY_LOCK_KEY, orderGUID);
        redisHelper.delete(advanceLockKey);
    }

    /**
     * 获取三天后的结束时间
     *
     * @return
     */
    public static Date getEndTime() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.add(Calendar.DATE, 3);
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        return todayEnd.getTime();
    }
}
