package com.holderzone.saas.store.trade.utils;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DateUtil
 * @date 2019/07/04 10:36
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Slf4j
public class DateUtil {

    //将java.util.Date 转换为java8 的java.time.LocalDateTime,默认时区为东8区
    public static LocalDateTime dateConvertToLocalDateTime(Date date) {
        return date.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime();
    }


    //将java8 的 java.time.LocalDateTime 转换为 java.util.Date，默认时区为东8区
    public static Date localDateTimeConvertToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.toInstant(ZoneOffset.of("+8")));
    }

    /**
     * 计算两个 LocalDateTime 之间相差的分钟数
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 相差的分钟数
     */
    public static long minutesBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return Math.abs(Duration.between(start, end).toMinutes());
    }

    /**
     * 检查是否超过时间限制
     *
     * @param checkOutTime 订单结账时间
     * @param timeLimit 允许退单的时间数值
     * @param timeLimitUnit 时间单位（0:小时，1:天）
     * @return true: 超时，false: 未超时
     */
    public static boolean checkRefundTimeLimit(LocalDateTime checkOutTime, int timeLimit, int timeLimitUnit) {
        // 订单时间为空，直接返回超时
        if (checkOutTime == null) {
            return true;
        }

        LocalDateTime now = LocalDateTime.now();

        // 默认未超时
        boolean overTime = false;

        // 计算时间差（单位为秒）
        long diff = Math.abs(Duration.between(checkOutTime, now).getSeconds());
        if (timeLimitUnit == 0) {
            // 单位为小时
            long limit = timeLimit * 3600L;
            if (diff > limit) {
                overTime = true;
            }
        } else if (timeLimitUnit == 1) {
            // 单位为天
            long limit = timeLimit * 24L * 3600L;
            if (diff > limit) {
                overTime = true;
            }
        } else {
            // 时间单位无效则返回超时
            overTime = true;
        }
        return overTime;
    }
}
