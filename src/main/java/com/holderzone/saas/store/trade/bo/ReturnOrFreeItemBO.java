package com.holderzone.saas.store.trade.bo;

import com.holderzone.saas.store.trade.entity.domain.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 退/赠菜 biz
 */
@Data
public class ReturnOrFreeItemBO {

    /**
     * 订单信息
     */
    private OrderDO orderDO;

    /**
     * 订单明细
     */
    private List<OrderItemDO> saveOrderItemDOS;

    /**
     * 属性
     */
    private List<ItemAttrDO> saveItemAttrDOS;

    /**
     * 商品扩展
     */
    private List<OrderItemExtendsDO> saveOrderItemExtendDOS;

    /**
     * 套餐换菜
     */
    private List<OrderItemChangesDO> saveOrderItemChangesDOS;

    private List<FreeReturnItemDO> saveList;

    private List<FreeReturnItemDO> updateList;

    private List<OrderItemDO> itemUpdateList;

    private List<OrderItemDO> orderItemDOS;

    private Map<Long, FreeReturnItemDO> freeReturnItemDOMap;

    private Map<String, Boolean> outDinnerMap;

    private List<OrderItemDO> returnPrintList;

    private List<OrderItemDO> returnEstimateList;

    private Boolean isReturn;

    private Boolean isCheck;

    private Map<Long, OrderItemDO> itemDOMap;

    private boolean isAdd;

    private OrderItemDO saveOrderItemDO;

}
