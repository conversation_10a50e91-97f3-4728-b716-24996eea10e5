package com.holderzone.saas.store.trade.builder;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.resp.KdsAttrGroupDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.request.item.TransferItemReqDTO;
import com.holderzone.saas.store.dto.trade.OrderItemDTO;
import com.holderzone.saas.store.dto.trade.req.TransferItemDetailsDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.trade.bo.TransferItemBO;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.transform.PrintTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant.HST_ORDER_ITEM;
import static com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant.ITEM_ATTR_GUID;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 转菜构建
 */
@Slf4j
public class TransferItemBizBuilder {

    private TransferItemBizBuilder() {
    }

    public static TransferItemBO buildTransferItemBiz(TransferItemReqDTO transferReq,
                                                      OrderDO newOrderDO,
                                                      List<OrderItemDO> oldOrderItemDOList,
                                                      List<ItemAttrDO> oldItemAttrDOList,
                                                      List<OrderItemRecordDO> oldItemRecordDOList,
                                                      List<OrderItemExtendsDO> oldItemExtendsDOList,
                                                      List<OrderItemChangesDO> orderItemChangesList) {
        TransferItemBO transferItemBO = new TransferItemBO();
        transferItemBO.setOldOrderGuid(transferReq.getOldOrderGuid());
        transferItemBO.setNewOrderGuid(transferReq.getNewOrderGuid());
        transferItemBO.setTransferItemMap(transferReq.getTransferItemMap());
        transferItemBO.setNewOrderDO(newOrderDO);
        buildNewOrderItem(transferItemBO, oldOrderItemDOList, oldItemAttrDOList, oldItemRecordDOList, oldItemExtendsDOList, orderItemChangesList);
        return transferItemBO;
    }

    public static void buildNewOrderItem(TransferItemBO transferItemBO,
                                         List<OrderItemDO> oldOrderItemDOList,
                                         List<ItemAttrDO> oldItemAttrDOList,
                                         List<OrderItemRecordDO> oldItemRecordDOList,
                                         List<OrderItemExtendsDO> oldItemExtendsDOList,
                                         List<OrderItemChangesDO> orderItemChangesList) {
        log.info("[buildNewOrderItem][开始]transferItemBO={}", JacksonUtils.writeValueAsString(transferItemBO));
        DynamicHelper dynamicHelper = SpringContextUtil.getInstance().getBean(DynamicHelper.class);
        List<Long> orderItemAttrGuidList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(oldItemAttrDOList)) {
            orderItemAttrGuidList = dynamicHelper.generateGuids(ITEM_ATTR_GUID, oldItemAttrDOList.size());
        }
        Map<Long, OrderItemChangesDO> orderItemChangesMap = orderItemChangesList.stream()
                .collect(Collectors.toMap(OrderItemChangesDO::getOrderItemGuid, Function.identity(), (key1, key2) -> key2));
        List<OrderItemChangesDO> newItemChangesList = new ArrayList<>();
        Map<Long, OrderItemExtendsDO> orderItemExtendsMap = oldItemExtendsDOList.stream()
                .collect(Collectors.toMap(OrderItemExtendsDO::getGuid, Function.identity(), (key1, key2) -> key2));
        List<OrderItemExtendsDO> newItemExtendsDOList = new ArrayList<>();
        HashMap<Long, OrderItemDTO> old2NewMap = new HashMap<>();
        List<ItemAttrDO> newItemAttrDOList = new ArrayList<>();
        List<OrderItemRecordDO> newItemRecordDOList = new ArrayList<>();
        Map<Long, List<ItemAttrDO>> orderItemAttrMap = oldItemAttrDOList.stream()
                .collect(Collectors.groupingBy(ItemAttrDO::getOrderItemGuid));
        Map<String, OrderItemRecordDO> itemRecordMap = oldItemRecordDOList.stream()
                .collect(Collectors.toMap(OrderItemRecordDO::getOrderItemGuid, Function.identity(), (v1, v2) -> v1));
        List<OrderItemDO> newOrderItemDOList = new ArrayList<>();
        List<Long> orderItemGuidList = dynamicHelper.generateGuids(HST_ORDER_ITEM, oldOrderItemDOList.size());
        List<Long> itemRecordGuidList = dynamicHelper.generateGuids(GuidKeyConstant.ORDER_ITEM_RECORD, oldItemRecordDOList.size());
        List<Long> itemChangeGuidList = dynamicHelper.generateGuids(GuidKeyConstant.ITEM_CHANGE_GUID, oldOrderItemDOList.size());
        HashMap<Long, Long> map = new HashMap<>();
        for (OrderItemDO orderItemDO : oldOrderItemDOList) {
            BigDecimal transferCount = transferItemBO.getTransferItemMap().get(String.valueOf(orderItemDO.getGuid()));
            OrderItemDO newOrderItemDO = buildNewOrderItemDO(transferItemBO, orderItemDO, orderItemGuidList, transferCount);
            newOrderItemDOList.add(newOrderItemDO);
            if (Objects.equals(ItemTypeEnum.GROUP.getCode(), orderItemDO.getItemType())
                    && orderItemDO.getParentItemGuid() == 0) {
                map.put(orderItemDO.getGuid(), newOrderItemDO.getGuid());
            }

            OrderItemDTO orderItemDTO = buildOld2NewDTO(orderItemDO, newOrderItemDO);
            old2NewMap.put(orderItemDO.getGuid(), orderItemDTO);

            OrderItemExtendsDO oldOrderItemExtendsDO = orderItemExtendsMap.get(orderItemDO.getGuid());
            if (!ObjectUtils.isEmpty(oldOrderItemExtendsDO)) {
                OrderItemExtendsDO newOrderItemExtendsDO = new OrderItemExtendsDO();
                BeanUtils.copyProperties(oldOrderItemExtendsDO, newOrderItemExtendsDO);
                newOrderItemExtendsDO.setGuid(newOrderItemDO.getGuid());
                newOrderItemExtendsDO.setGmtCreate(null);
                newOrderItemExtendsDO.setGmtModified(null);
                newOrderItemExtendsDO.setIsDelete(Boolean.FALSE);
                newOrderItemExtendsDO.setOrderGuid(newOrderItemDO.getOrderGuid());
                newOrderItemExtendsDO.setTransferFlag(Boolean.TRUE);
                newItemExtendsDOList.add(newOrderItemExtendsDO);
            }
            OrderItemChangesDO orderItemChangesDO = orderItemChangesMap.get(orderItemDO.getGuid());
            if (!ObjectUtils.isEmpty(orderItemChangesDO)) {
                OrderItemChangesDO newOrderItemChangesDO = new OrderItemChangesDO();
                BeanUtils.copyProperties(orderItemChangesDO, newOrderItemChangesDO);
                newOrderItemChangesDO.setGuid(itemChangeGuidList.remove(0));
                newOrderItemChangesDO.setOrderGuid(newOrderItemDO.getOrderGuid());
                newOrderItemChangesDO.setOrderItemGuid(newOrderItemDO.getGuid());
                newOrderItemChangesDO.setGmtCreate(null);
                newOrderItemChangesDO.setGmtModified(null);
                newOrderItemChangesDO.setIsDelete(Boolean.FALSE);
                newItemChangesList.add(newOrderItemChangesDO);
            }

            handleItemAttr(transferItemBO, orderItemDO, orderItemAttrMap, orderItemAttrGuidList, newOrderItemDO, newItemAttrDOList);
            handleItemRecord(transferItemBO, orderItemDO, itemRecordMap, itemRecordGuidList, newOrderItemDO, transferCount, newItemRecordDOList);
        }
        transferItemBO.setNewItemAttrDOList(newItemAttrDOList);
        newOrderItemDOList.forEach(newOrderItemDO -> {
            if (newOrderItemDO.getParentItemGuid() != 0) {
                Long newOrderItemGuid = map.get(newOrderItemDO.getParentItemGuid());
                newOrderItemDO.setParentItemGuid(newOrderItemGuid);
            }
        });
        transferItemBO.setNewOrderItemDOList(newOrderItemDOList);
        transferItemBO.setNewItemRecordDOList(newItemRecordDOList);
        transferItemBO.setOld2NewMap(old2NewMap);
        transferItemBO.setNewItemExtendsDOList(newItemExtendsDOList);
        transferItemBO.setUpdateItemChangesList(newItemChangesList);
    }

    @NotNull
    private static OrderItemDO buildNewOrderItemDO(TransferItemBO transferItemBO,
                                                   OrderItemDO orderItemDO,
                                                   List<Long> orderItemGuidList,
                                                   BigDecimal transferCount) {
        OrderItemDO newOrderItemDO = new OrderItemDO();
        BeanUtils.copyProperties(orderItemDO, newOrderItemDO);
        newOrderItemDO.setGuid(orderItemGuidList.remove(0));
        newOrderItemDO.setGmtCreate(null);
        newOrderItemDO.setGmtModified(null);
        newOrderItemDO.setIsDelete(Boolean.FALSE);
        newOrderItemDO.setOrderGuid(Long.valueOf(transferItemBO.getNewOrderGuid()));
        newOrderItemDO.setOriginalOrderItemGuid(null);
        if (orderItemDO.getParentItemGuid() == 0) {
            newOrderItemDO.setCurrentCount(transferCount);
        }
        newOrderItemDO.setFreeCount(BigDecimal.ZERO);
        newOrderItemDO.setReturnCount(BigDecimal.ZERO);
        newOrderItemDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        newOrderItemDO.setCreateStaffName(UserContextUtils.getUserName());
        newOrderItemDO.setRefundCount(BigDecimal.ZERO);
        newOrderItemDO.setFreeRefundCount(BigDecimal.ZERO);
        return newOrderItemDO;
    }

    private static void handleItemRecord(TransferItemBO transferItemBO,
                                         OrderItemDO orderItemDO,
                                         Map<String, OrderItemRecordDO> itemRecordMap,
                                         List<Long> itemRecordGuidList,
                                         OrderItemDO newOrderItemDO,
                                         BigDecimal transferCount,
                                         List<OrderItemRecordDO> newItemRecordDOList) {
        OrderItemRecordDO orderItemRecordDO = itemRecordMap.get(String.valueOf(orderItemDO.getGuid()));
        if (!ObjectUtils.isEmpty(orderItemRecordDO)) {
            OrderItemRecordDO newItemRecordDO = new OrderItemRecordDO();
            BeanUtils.copyProperties(orderItemRecordDO, newItemRecordDO);
            newItemRecordDO.setGuid(itemRecordGuidList.remove(0));
            newItemRecordDO.setOrderGuid(Long.valueOf(transferItemBO.getNewOrderGuid()));
            newItemRecordDO.setOrderItemGuid(String.valueOf(newOrderItemDO.getGuid()));
            newItemRecordDO.setType(5);
            newItemRecordDO.setLog(orderItemDO.getParentItemGuid() == 0L ? "【转菜】:点菜" : "【转菜】:子菜");
            newItemRecordDO.setGmtCreate(null);
            newItemRecordDO.setGmtModified(null);
            if (orderItemDO.getParentItemGuid() == 0) {
                newItemRecordDO.setCurrentCount(transferCount);
            }
            newItemRecordDO.setBeforeTransferOrderGuid(String.valueOf(orderItemDO.getOrderGuid()));
            newItemRecordDO.setBeforeTransferOrderItemGuid(String.valueOf(orderItemDO.getGuid()));
            newItemRecordDOList.add(newItemRecordDO);
        }
    }

    private static void handleItemAttr(TransferItemBO transferItemBO,
                                       OrderItemDO orderItemDO,
                                       Map<Long, List<ItemAttrDO>> orderItemAttrMap,
                                       List<Long> orderItemAttrGuidList,
                                       OrderItemDO newOrderItemDO,
                                       List<ItemAttrDO> newItemAttrDOList) {
        List<ItemAttrDO> itemAttrDOS = orderItemAttrMap.get(orderItemDO.getGuid());
        if (!CollectionUtils.isEmpty(itemAttrDOS)) {
            for (ItemAttrDO itemAttrDO : itemAttrDOS) {
                ItemAttrDO newAttrDO = new ItemAttrDO();
                BeanUtils.copyProperties(itemAttrDO, newAttrDO);
                newAttrDO.setGuid(orderItemAttrGuidList.remove(0));
                newAttrDO.setGmtCreate(null);
                newAttrDO.setGmtModified(null);
                newAttrDO.setIsDelete(Boolean.FALSE);
                newAttrDO.setOrderGuid(Long.valueOf(transferItemBO.getNewOrderGuid()));
                newAttrDO.setOrderItemGuid(newOrderItemDO.getGuid());
                newItemAttrDOList.add(newAttrDO);
            }
        }
    }

    @NotNull
    private static OrderItemDTO buildOld2NewDTO(OrderItemDO orderItemDO,
                                                OrderItemDO newOrderItemDO) {
        OrderItemDTO orderItemDTO = new OrderItemDTO();
        orderItemDTO.setGuid(newOrderItemDO.getGuid());
        orderItemDTO.setItemType(newOrderItemDO.getItemType());
        orderItemDTO.setCurrentCount(newOrderItemDO.getCurrentCount());
        if (Objects.equals(ItemTypeEnum.GROUP.getCode(), orderItemDO.getItemType())
                && orderItemDO.getParentItemGuid() == 0) {
            orderItemDTO.setCurrentCount(newOrderItemDO.getCurrentCount().multiply(newOrderItemDO.getPackageDefaultCount()));
        }
        return orderItemDTO;
    }

    @NotNull
    public static BigDecimal calculateOrderItemFee(List<ItemAttrDO> itemAttrDOList,
                                                   List<OrderItemDO> orderItemDOList) {
        BigDecimal newOrderFee = orderItemDOList.stream()
                .filter(oi -> oi.getParentItemGuid() == 0)
                .map(oi -> oi.getCurrentCount().multiply(oi.getPrice()
                        .add(oi.getAddPrice())
                        .add(oi.getAttrTotal()))
                )
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        Map<Long, List<ItemAttrDO>> orderItemAttrMap = itemAttrDOList.stream()
                .collect(Collectors.groupingBy(ItemAttrDO::getOrderItemGuid));
        BigDecimal attrTotalPrice = orderItemDOList.stream()
                .filter(oi -> oi.getParentItemGuid() == 0)
                .filter(oi -> !ObjectUtils.isEmpty(oi.getHasAttr()) && oi.getHasAttr() == BooleanEnum.TRUE.getCode())
                .map(oi -> {
                    List<ItemAttrDO> itemAttrDOS = orderItemAttrMap.get(oi.getGuid());
                    if (CollectionUtils.isEmpty(itemAttrDOS)) {
                        return BigDecimal.ZERO;
                    }
                    return itemAttrDOS.stream()
                            .map(itemAttrDO -> getAttrPrice(oi, itemAttrDO))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return newOrderFee.add(attrTotalPrice);
    }

    @NotNull
    private static BigDecimal getAttrPrice(OrderItemDO orderItemDO,
                                           ItemAttrDO itemAttrDO) {
        BigDecimal attrPrice;
        if (Objects.equals(orderItemDO.getItemType(), ItemTypeEnum.WEIGH.getCode())) {
            attrPrice = itemAttrDO.getAttrPrice().multiply(BigDecimal.ONE);
        } else {
            attrPrice = itemAttrDO.getAttrPrice().multiply(orderItemDO.getCurrentCount());
        }
        return attrPrice;
    }

    public static List<TransferItemDetailsDTO> dineInItemDTOList2TransferItemDetailsDTOList(List<DineInItemDTO> dineInItemDTOS) {
        return dineInItemDTOS
                .stream()
                .map(TransferItemBizBuilder::buildTransferItemDTO)
                .collect(Collectors.toList());
    }

    private static TransferItemDetailsDTO buildTransferItemDTO(DineInItemDTO dineInItem) {
        TransferItemDetailsDTO transferItemDTO = new TransferItemDetailsDTO();
        if (BigDecimalUtil.greaterThanZero(dineInItem.getFreeCount())) {
            transferItemDTO.setCurrentCount(dineInItem.getCurrentCount().add(dineInItem.getFreeCount()));
        } else {
            transferItemDTO.setCurrentCount(dineInItem.getCurrentCount());
        }
        transferItemDTO.setIsWeight(dineInItem.getItemType().equals(ItemTypeEnum.WEIGH.getCode()));
        transferItemDTO.setItemGuid(dineInItem.getItemGuid());
        transferItemDTO.setItemName(dineInItem.getItemName());
        transferItemDTO.setItemRemark(dineInItem.getRemark());
        transferItemDTO.setItemState(dineInItem.getItemState());
        transferItemDTO.setOrderItemGuid(dineInItem.getGuid());
        transferItemDTO.setSkuCode(dineInItem.getCode());
        transferItemDTO.setSkuGuid(dineInItem.getSkuGuid());
        transferItemDTO.setSkuName(dineInItem.getSkuName());
        transferItemDTO.setSkuUnit(dineInItem.getUnit());
        // 属性组
        transferItemDTO.setAttrGroup(buildKdsAttrGroupDTO(dineInItem.getItemAttrDTOS()));
        // 套餐
        if ((dineInItem.getItemType() == ItemTypeEnum.GROUP.getCode() || dineInItem.getItemType() == ItemTypeEnum.TEAMMEAL.getCode())
                && !CollectionUtil.isEmpty(dineInItem.getPackageSubgroupDTOS())) {
            transferItemDTO.setSubItemList(getSubItemRecords(dineInItem));
        }
        return transferItemDTO;
    }

    private static List<KdsAttrGroupDTO> buildKdsAttrGroupDTO(List<ItemAttrDTO> itemAttrDTOS) {
        List<KdsAttrGroupDTO> kdsAttrGroupDTOS = new ArrayList<>();
        Map<String, List<ItemAttrDTO>> attrGroupGuidMap = CollectionUtil.toListMap(itemAttrDTOS,
                "attrGroupGuid+attrGroupName");
        for (Map.Entry<String, List<ItemAttrDTO>> entry : attrGroupGuidMap.entrySet()) {
            String attrGroupGuid = entry.getKey();
            List<ItemAttrDTO> itemAttrDTOList = entry.getValue();
            KdsAttrGroupDTO kdsAttrGroupDTO = new KdsAttrGroupDTO();
            kdsAttrGroupDTOS.add(kdsAttrGroupDTO);
            String[] split = attrGroupGuid.split("\\+");
            kdsAttrGroupDTO.setGroupGuid(split[0]);
            kdsAttrGroupDTO.setGroupName(split[1]);
            List<KdsItemAttrDTO> kdsItemAttrDTOS = new ArrayList<>();
            for (ItemAttrDTO itemAttrDTO : itemAttrDTOList) {
                KdsItemAttrDTO kdsItemAttrDTO = new KdsItemAttrDTO();
                kdsItemAttrDTOS.add(kdsItemAttrDTO);
                kdsItemAttrDTO.setAttrGuid(itemAttrDTO.getAttrGuid());
                kdsItemAttrDTO.setAttrName(itemAttrDTO.getAttrName());
                kdsItemAttrDTO.setAttrNumber(itemAttrDTO.getNum());
            }
            kdsAttrGroupDTO.setAttrs(kdsItemAttrDTOS);
        }
        return kdsAttrGroupDTOS;
    }

    /**
     * 子项
     */
    private static List<TransferItemDetailsDTO> getSubItemRecords(DineInItemDTO dineInItemDTO) {
        return dineInItemDTO.getPackageSubgroupDTOS().stream()
                .map(PackageSubgroupDTO::getSubDineInItemDTOS)
                .filter(subDineInItemDtos -> !CollectionUtils.isEmpty(subDineInItemDtos))
                .flatMap(Collection::stream)
                .map(subDineInItemDTO -> {
                    DineInItemDTO transferSubDineInItemDTO = PrintTransform.INSTANCE.createDineInItemDTOFromSub(subDineInItemDTO);
                    TransferItemDetailsDTO transferItemDetailsDTO = turn2TransferItemDetailsDTO(transferSubDineInItemDTO);
                    if (Objects.nonNull(subDineInItemDTO.getPackageDefaultCount()) && subDineInItemDTO.getPackageDefaultCount().compareTo(BigDecimal.ONE) > 0) {
                        transferItemDetailsDTO.setCurrentCount(transferItemDetailsDTO.getCurrentCount().multiply(subDineInItemDTO.getPackageDefaultCount()));
                    }
                    return transferItemDetailsDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换
     */
    private static TransferItemDetailsDTO turn2TransferItemDetailsDTO(DineInItemDTO dineInItemDTO) {
        return new TransferItemDetailsDTO().setOrderItemGuid(dineInItemDTO.getGuid())
                .setItemGuid(dineInItemDTO.getItemGuid())
                .setSkuUnit(dineInItemDTO.getUnit())
                .setItemName(ItemTypeEnum.SKU.getCode() == dineInItemDTO.getItemType() ? dineInItemDTO.getItemName()
                        + "(" + dineInItemDTO.getSkuName() + ")" : dineInItemDTO.getItemName())
                .setSkuGuid(dineInItemDTO.getSkuGuid())
                .setSkuName(dineInItemDTO.getSkuName())
                .setSkuCode(dineInItemDTO.getCode())
                .setItemState(dineInItemDTO.getItemState())
                .setIsWeight(Integer.valueOf(ItemTypeEnum.WEIGH.getCode()).equals(dineInItemDTO.getItemType()))
                .setCurrentCount(dineInItemDTO.getCurrentCount())
                .setItemRemark(dineInItemDTO.getRemark())
                .setAttrGroup(buildKdsAttrGroupDTO(dineInItemDTO.getItemAttrDTOS()))
                ;
    }

    public static List<OrderItemDO> handleOldOrderData(List<OrderItemDO> oldOrderItemDOList,
                                                       TransferItemBO transferItemBO) {
        List<OrderItemDO> updateOrderItemDOList = new ArrayList<>();
        for (OrderItemDO orderItemDO : oldOrderItemDOList) {
            BigDecimal oldCurrentCount = orderItemDO.getCurrentCount();
            BigDecimal transferCount = transferItemBO.getTransferItemMap().get(String.valueOf(orderItemDO.getGuid()));
            if (orderItemDO.getParentItemGuid() == 0 && BigDecimalUtil.greaterEqual(oldCurrentCount, transferCount)) {
                OrderItemDO itemDO = new OrderItemDO();
                itemDO.setGuid(orderItemDO.getGuid());
                itemDO.setCurrentCount(oldCurrentCount.subtract(transferCount));
                updateOrderItemDOList.add(itemDO);
            } else {
                BigDecimal otherCount = orderItemDO.getFreeCount().add(orderItemDO.getReturnCount());
                if (BigDecimalUtil.greaterThanZero(otherCount)) {
                    OrderItemDO itemDO = new OrderItemDO();
                    itemDO.setGuid(orderItemDO.getGuid());
                    itemDO.setCurrentCount(BigDecimal.ZERO);
                    updateOrderItemDOList.add(itemDO);
                }
            }
        }
        log.info("[handleOldOrderData]oldOrderItemDOList={}", JacksonUtils.writeValueAsString(oldOrderItemDOList));
        return updateOrderItemDOList;
    }

    public static void setOrderFeeForCombine(OrderDO newOrderDO,
                                             BigDecimal orderItemFee) {
        BigDecimal orderFeeForCombine;
        if (ObjectUtils.isEmpty(newOrderDO.getOrderFeeForCombine())) {
            orderFeeForCombine = orderItemFee.add(newOrderDO.getAppendFee());
        } else {
            orderFeeForCombine = newOrderDO.getOrderFeeForCombine().add(orderItemFee);
        }
        newOrderDO.setOrderFeeForCombine(orderFeeForCombine);
    }

}
