package com.holderzone.saas.store.trade.controller;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseRespDTO;
import com.holderzone.saas.store.dto.common.RedisReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.req.PadModifyGuestsNoReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadOrderPlacementReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadPayInfoReqDTO;
import com.holderzone.saas.store.dto.trade.resp.*;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.trade.entity.domain.PadOrderDO;
import com.holderzone.saas.store.trade.service.CalculateService;
import com.holderzone.saas.store.trade.service.PadOrderService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description pad订单接口
 * @date 2021/8/23 10:05
 */
@RestController
@RequestMapping("/pad_order")
@Api(tags = "pad订单接口")
@Slf4j
@RequiredArgsConstructor
public class PadOrderController {

    private final PadOrderService padOrderService;

    private final CalculateService calculateService;

    /**
     * pad下单
     *
     * @param orderPlacementReqDTO pad下单请求实体
     * @return 订单guid
     */
    @ApiOperation("pad下单")
    @PostMapping(value = "/order_placement")
    public PadOrderPlacementRespDTO orderPlacement(@RequestBody PadOrderPlacementReqDTO orderPlacementReqDTO) {
        log.info("pad下单 createDineInOrderDTO={}", JacksonUtils.writeValueAsString(orderPlacementReqDTO));
        return padOrderService.orderPlacement(orderPlacementReqDTO);
    }

    /**
     * pad购物车价格计算
     *
     * @param orderPlacementReqDTO 购物车商品数据
     * @return 购物车总价
     */
    @ApiOperation("购物车价格计算")
    @PostMapping(value = "/calculate_shop_car")
    public PadPriceRespDTO calculateShopCar(@RequestBody PadOrderPlacementReqDTO orderPlacementReqDTO) {
        log.info("购物车价格计算 orderPlacementReqDTO={}", JacksonUtils.writeValueAsString(orderPlacementReqDTO));
        return padOrderService.calculateShopCar(orderPlacementReqDTO);
    }

    /**
     * 查询门店下的下单表
     *
     * @param request 条件信息
     * @return 下单列表
     */
    @ApiOperation("查询门店下的下单表")
    @PostMapping(value = "/list_pad_order")
    public List<PadOrderRespDTO> listPadOrder(@RequestBody WxStoreMerchantOrderReqDTO request) {
        log.info("查询门店下的下单表 request={}", JacksonUtils.writeValueAsString(request));
        return padOrderService.listPadOrder(request);
    }

    /**
     * 根据guid查询pad下单信息
     *
     * @param guid 下单表guid
     * @return 下单信息
     */
    @ApiOperation("根据guid查询pad下单信息")
    @GetMapping(value = "/get_pad_order_by_guid")
    public PadOrderRespDTO getPadOrderByGuid(@RequestParam("guid") String guid) {
        log.info("根据guid查询pad下单信息 guid={}", guid);
        return padOrderService.getPadOrderByGuid(guid);
    }

    /**
     * 远程调用获取pad下单的加菜商品信息
     *
     * @param key 订单guid+下单表guid，以:分割
     * @return 加菜商品信息
     */
    @ApiOperation("远程调用获取pad下单的加菜商品信息")
    @GetMapping(value = "/get_pad_order_add_item_info_by_redis")
    public CreateDineInOrderReqDTO getPadOrderAddItemInfoByRedis(@RequestParam("key") String key) {
        log.info("远程调用获取pad下单的加菜商品信息 key={}", key);
        return padOrderService.getPadOrderAddItemInfoByRedis(key);
    }

    /**
     * 根据guid更新数据
     *
     * @param padOrderRespDTO padOrderRespDTO
     * @return Boolean
     */
    @ApiOperation("根据guid更新数据")
    @PostMapping(value = "/update_by_id")
    public Boolean updateById(@RequestBody PadOrderRespDTO padOrderRespDTO) {
        log.info("根据guid更新数据 padOrderRespDTO={}", JacksonUtils.writeValueAsString(padOrderRespDTO));
        PadOrderDO padOrderDO = OrderTransform.INSTANCE.OrderRespDTO2padOrderDO(padOrderRespDTO);
        return padOrderService.updateById(padOrderDO);
    }

    /**
     * 查询pad订单详情
     *
     * @param orderGuid 订单guid
     * @return pad订单详情
     */
    @ApiOperation("查询pad订单详情")
    @GetMapping(value = "/query_pad_order_info")
    public PadOrderInfoRespDTO queryPadOrderInfo(@RequestParam("orderGuid") String orderGuid) {
        log.info("查询pad订单详情 orderGuid={}", orderGuid);
        return padOrderService.queryPadOrderInfo(orderGuid);
    }

    /**
     * 根据guid删除pad下单数据
     *
     * @param padOrderGuid pad下单guid
     * @return Boolean
     */
    @ApiOperation("根据guid删除pad下单数据")
    @GetMapping(value = "/remove_by_guid")
    public Boolean removeByGuid(@RequestParam("padOrderGuid") String padOrderGuid) {
        log.info("根据guid删除pad下单数据 padOrderGuid={}", padOrderGuid);
        return padOrderService.removeById(padOrderGuid);
    }

    /**
     * 修改就餐人数
     *
     * @param modifyGuestsNoReqDTO 修改就餐人数请求实体
     * @return 结果
     */
    @ApiOperation("修改就餐人数")
    @PostMapping(value = "/modify_guests_no")
    public BaseRespDTO modifyGuestsNo(@RequestBody PadModifyGuestsNoReqDTO modifyGuestsNoReqDTO) {
        log.info("修改就餐人数 modifyGuestsNoReqDTO={}", JacksonUtils.writeValueAsString(modifyGuestsNoReqDTO));
        return padOrderService.modifyGuestsNo(modifyGuestsNoReqDTO);
    }

    /**
     * 保存pad支付信息到缓存
     *
     * @param padPayInfoReqDTO pad支付信息
     * @return Boolean
     */
    @ApiOperation("保存pad支付信息到缓存")
    @PostMapping(value = "/save_pad_pay_info_to_redis")
    public Boolean savePadPayInfoToRedis(@RequestBody PadPayInfoReqDTO padPayInfoReqDTO) {
        log.info("保存pad支付信息到缓存 padPayInfoReqDTO={}", JacksonUtils.writeValueAsString(padPayInfoReqDTO));
        return padOrderService.savePadPayInfoToRedis(padPayInfoReqDTO);
    }

    /**
     * 获取缓存pad支付信息
     *
     * @param orderGuid 订单guid
     * @return pad支付信息
     */
    @ApiOperation("获取缓存pad支付信息")
    @GetMapping(value = "/get_pad_pay_info")
    public PadPayInfoReqDTO getPadPayInfo(@RequestParam("orderGuid") String orderGuid) {
        log.info("获取缓存pad支付信息 orderGuid={}", orderGuid);
        return padOrderService.getPadPayInfo(orderGuid);
    }

    /**
     * 获取pad支付二维码
     *
     * @param orderGuid 订单guid
     * @return pad支付二维码
     */
    @ApiOperation("获取pad支付二维码")
    @PostMapping(value = "/get_pad_qr_code")
    public PadQrCodeRespDTO getPadQrCode(@RequestParam("orderGuid") String orderGuid) {
        log.info("获取pad支付二维码 orderGuid={}", orderGuid);
        return padOrderService.getPadQrCode(orderGuid);
    }

    /**
     * 获取所有订单的pad下单数据
     *
     * @param orderGuid 当前订单guid
     * @return pad下单数据
     */
    @ApiOperation("获取所有订单的pad下单数据")
    @GetMapping(value = "/list_pad_order_dto_list")
    public List<PadOrderRespDTO> listPadOrderDTOList(@RequestParam("orderGuid") String orderGuid) {
        log.info("获取所有订单的pad下单数据 orderGuid={}", orderGuid);
        return padOrderService.listPadOrderDTOList(orderGuid);
    }

    /**
     * 获取订单并台的所有下单信息
     *
     * @param orderGuid 订单guid
     * @return pad下单消息
     */
    @ApiOperation("获取pad订单并台的所有下单信息")
    @GetMapping(value = "/list_pad_order_info_on_combine")
    public List<PadOrderRespDTO> listPadOrderInfoOnCombine(@RequestParam("orderGuid") String orderGuid) {
        log.info("获取订单并台的所有下单信息 orderGuid={}", orderGuid);
        return padOrderService.listPadOrderInfoOnCombine(orderGuid);
    }

    /**
     * 保存pad转台信息
     *
     * @param redisReqDTO redis请求实体
     * @return 结果
     */
    @ApiOperation("保存pad转台信息")
    @PostMapping(value = "/save_pad_turn_info")
    public Boolean savePadTurnInfo(@RequestBody RedisReqDTO redisReqDTO) {
        log.info("保存pad转台信息 redisReqDTO={}", JacksonUtils.writeValueAsString(redisReqDTO));
        return padOrderService.savePadTurnInfo(redisReqDTO);
    }

    /**
     * 获取pad转台信息
     *
     * @param redisKey key
     * @return pad转台信息
     */
    @ApiOperation("获取pad转台信息")
    @GetMapping(value = "/get_pad_turn_info")
    public String getPadTurnInfo(@RequestParam("redisKey") String redisKey) {
        log.info("获取pad转台信息 redisKey={}", redisKey);
        return padOrderService.getPadTurnInfo(redisKey);
    }

    /**
     * 根据桌台查询订单guid和人数
     *
     * @param tableGuid 桌台guid
     * @return 订单guid和人数
     */
    @ApiOperation("根据桌台查询订单guid和人数")
    @GetMapping(value = "/query_order_and_guest_by_table")
    public PadRebindRespDTO queryOrderAndGuestByTable(@RequestParam("tableGuid") String tableGuid) {
        log.info("根据桌台查询订单guid和人数 tableGuid={}", tableGuid);
        return padOrderService.queryOrderAndGuestByTable(tableGuid);
    }

    /**
     * 根据并台主单guid查询pad下单信息列表
     *
     * @param combineOrderGuid 并台主单guid
     * @return 下单信息列表
     */
    @ApiOperation("根据并台主单guid查询pad下单信息列表")
    @GetMapping(value = "/list_pad_order_by_combine_order_guid")
    public List<PadOrderRespDTO> listPadOrderByCombineOrderGuid(@RequestParam("combineOrderGuid") String combineOrderGuid) {
        log.info("根据并台主单guid查询pad下单信息列表 combineOrderGuid={}", combineOrderGuid);
        return padOrderService.listPadOrderByCombineOrderGuid(combineOrderGuid);
    }

    /**
     * pad退出登录处理
     * 撤销验券和删除支付信息
     *
     * @param billCalculateReqDTO 计算接口的入参，用于撤销验券
     */
    @ApiOperation(value = "pad退出登录处理")
    @PostMapping("/pad_sign_out")
    public Boolean padSignOut(@RequestBody BillCalculateReqDTO billCalculateReqDTO) {
        if (ObjectUtils.isEmpty(billCalculateReqDTO.getStoreGuid())) {
            throw new BusinessException("门店guid不能为空");
        }
        log.info("pad退出登录处理 入参：{}", JacksonUtils.writeValueAsString(billCalculateReqDTO));
        UserContext userContext = UserContextUtils.get();
        userContext.setStoreGuid(billCalculateReqDTO.getStoreGuid());
        UserContextUtils.put(userContext);
        // 撤销验券
        DineinOrderDetailRespDTO calculate = calculateService.calculate(billCalculateReqDTO);
        log.info("撤销验券调用计算接口成功 calculate={}", JacksonUtils.writeValueAsString(calculate));
        return padOrderService.padSignOut(billCalculateReqDTO);
    }
}
