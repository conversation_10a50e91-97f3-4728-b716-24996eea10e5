package com.holderzone.saas.store.trade.config;

public class RocketMqConfig {

    public static final String KDS_MESSAGE_TOPIC = "kds-message-topic";

    public static final String KDS_PREPARE_TAG = "kds-prepare-tag";

    public static final String KDS_CHANGES_TAG = "kds-changes-tag";

    public static final String KDS_TRANSFER_TAG = "kds-transfer-tag";

    public static final String KDS_CALL_TAG = "kds-call-tag";

    public static final String KDS_URGE_TAG = "kds-urge-tag";

    public static final String KDS_REMARK_TAG = "kds-remark-tag";

    public static final String KDS_CHANGE_TABLE_TAG = "kds-change-table-tag";

    public static final String KDS_REFUND_TAG = "kds-refund-tag";

    public static final String PRINT_MESSAGE_TOPIC = "print-message-topic";

    public static final String PRINT_MESSAGE_TAG = "print-message-tag";

    public static final String MESSAGE_CONTEXT = "message-context";

    public static final String MESSAGE_LOCALE = "message-locale";

    public static final String PRINT_MESSAGE_GROUP = "print-message-group";

    public static final String TABLE_STATUS_CHANGE_MQ_TOPIC = "table-status-change-topic";

    public static final String TABLE_STATUS_CHANGE_MQ_TAG = "table-status-change-tag";

    public static final String WECHAT_ORDER_CHANGE_MQ_TOPIC = "weChat-order-change-topic";

    public static final String WECHAT_ORDER_CHANGE_MQ_TAG = "weChat-order-change-tag";

    public static final String ORDER_STATUS_CHANGE_MQ_TOPIC = "order-status-change-topic";

    public static final String ORDER_STATUS_CHANGE__MQ_TAG = "order-status-change-tag";

    public static final String ORDER_STATUS_CHANGE_MQ_GROUP = "order-status-end-change-group";
}
