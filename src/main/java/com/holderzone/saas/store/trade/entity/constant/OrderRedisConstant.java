package com.holderzone.saas.store.trade.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRedisConstant
 * @date 2018-07-25 18:25:11
 * @description
 * @program holder-saas-store-trade
 */
public class OrderRedisConstant {

    public static final String ORDER_GROUP = "order";

    public static final String HANG_GROUP = "hang";

    public static final String ORDER_NO_GROUP = "orderNo";

    public static final String ADJUST_NO_GROUP = "adjustNo";

    public static final String HST_ORDER_GROUP = "hstOrder";

    public static final String HST_ORDER_ITEM_GROUP = "hstOrderItem";

    public static final String HST_ORDER_VERSION_GROUP = "hstOrderVersion";

    public static final String HST_ORDER_PAYMENT_INITIATE_METHOD = "hstOrderPaymentInitiateMethod";

    public static final String HST_ORDER_PAY_GUID = "hstOrderPayGuid";

    public static final String HST_ORDER_LOCK_GROUP = "paylockOrder";

    public static final String HST_ORDER_CHILD_CACHE_GROUP = "orderCache";

    public static final String INCR_GROUP = "incr";

    public static final String ABNORMAL_ORDER = "abnormalOrder:";

    public static final String BILL_PAY_INFO = "billPayInfo:";

    public static final String IS_PRINT = "isPrint:";

    public static final int ORDER_MINUTES_TIMEOUT = 60;

    public static final String DEBT_INVOICE_CODE = "debtInvoiceCode";

    public static final String PAD_ORDER = "padOrder:";

    public static final String PAD_PAY_INFO = "padPayInfo:";

    public static final String RED_PACKET_INFO = "redPacketInfo:";

    public static final String REDIS_ORDER_FEE = "redisOrderFee:";

}
