package com.holderzone.saas.store.trade.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.member.request.MemberConsumeReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.order.request.bill.*;
import com.holderzone.saas.store.dto.order.response.bill.BillAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-trade
 */
public interface BillService {

    EstimateItemRespDTO pay(BillPayReqDTO billPayReqDTO);

    String memberPay(OrderDO orderDO, BillPayReqDTO.Payment payment, BillPayReqDTO billPayReqDTO);

    String saveMemberPay(OrderDO orderDO, BillPayReqDTO.Payment payment, BillPayReqDTO billPayReqDTO);

    String notmemberPay(OrderDO orderDO, BillPayReqDTO.Payment payment, BillPayReqDTO billPayReqDTO);

    BillAggPayRespDTO aggPay(BillAggPayReqDTO billPayReqDTO);

    String recovery(RecoveryReqDTO recoveryReqDTO);

    Boolean validatAggRefund(ValidatAggReturnReqDTO validatAggReturnReqDTO);

    Boolean refund(RefundReqDTO refundReqDTO);

    boolean printPreBill(SingleDataDTO singleDataDTO);

    String aggCallBack(SaasNotifyDTO saasNotifyDTO);

    Page<MemberConsumeRespDTO> memberConsumeRecords(MemberConsumeReqDTO memberConsumeReqDTO);

    void orderCheckoutSuccess(BillPayReqDTO billPayReqDTO);

    Boolean recoveryTimeLimit(String orderGuid);
}
