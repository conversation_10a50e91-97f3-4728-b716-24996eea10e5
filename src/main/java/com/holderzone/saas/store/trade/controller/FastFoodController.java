package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.order.OrderDTO;
import com.holderzone.saas.store.dto.order.common.HangOrderDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.request.item.UpdateOrderItemInfoDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.trade.exception.OrderLockException;
import com.holderzone.saas.store.trade.anno.LocalizeRequireNotifyOrderGuids;
import com.holderzone.saas.store.trade.service.FastFoodService;
import com.holderzone.saas.store.trade.service.HangOrderService;
import com.holderzone.saas.store.trade.service.OrderLockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FastFoodController
 * @date 2019/01/04 8:53
 * @description 快餐接口
 * @program holder-saas-store-trade
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/fast_food")
@Api(description = "快餐接口")
@Slf4j
public class FastFoodController {

    private final FastFoodService fastFoodService;

    private final HangOrderService hangOrderService;

    private final OrderLockService orderLockService;

    @ApiOperation(value = "去结账", notes = "去结账")
    @PostMapping("/add_item")
    @LocalizeRequireNotifyOrderGuids
    public EstimateItemRespDTO addItem(@RequestBody CreateFastFoodReqDTO createFastFoodReqDTO) {
        log.info("去结账,createFastFoodReqDTO={}", JacksonUtils.writeValueAsString(createFastFoodReqDTO));
        // 快餐加菜校验锁
        String orderGuid = createFastFoodReqDTO.getGuid();
        if (!StringUtils.isEmpty(orderGuid)) {
            String orderDeviceId = orderLockService.getDeviceLockId(orderGuid);
            log.info("快餐加菜校验锁,orderDeviceId={}", orderDeviceId);
            if (orderDeviceId != null) {
                throw new OrderLockException();
            }
        }
        return fastFoodService.addItem(createFastFoodReqDTO);
    }

    @ApiOperation(value = "小程序快餐下单", notes = "小程序快餐下单")
    @PostMapping("/create_applets_order")
    public EstimateItemRespDTO createAppletOrder(@RequestBody CreateFastFoodReqDTO createFastFoodReqDTO) {
        log.info("小程序快餐下单,createFastFoodReqDTO={}", JacksonUtils.writeValueAsString(createFastFoodReqDTO));
        return fastFoodService.createAppletOrder(createFastFoodReqDTO);
    }


    @ApiOperation(value = "挂起订单", notes = "挂起订单")
    @PostMapping("/hang_order")
    public String hangOrder(@RequestBody HangOrderDTO hangOrderDTO) {
        return hangOrderService.hangOrder(hangOrderDTO);
    }

    @ApiOperation(value = "挂起订单列表", notes = "挂起订单列表")
    @PostMapping("/hang_order_list")
    public Map<String, String> hangOrderList(@RequestBody HangOrderDTO hangOrderDTO) {
        return hangOrderService.hangOrderList(hangOrderDTO);
    }

    @ApiOperation(value = "删除挂起订单", notes = "删除挂起订单")
    @PostMapping("/gain_order")
    public Boolean gainOrder(@RequestBody HangOrderDTO hangOrderDTO) {
        return hangOrderService.gainOrder(hangOrderDTO);
    }

    @ApiOperation(value = "删除门店当前快餐自动号牌数", notes = "删除门店当前快餐自动号牌数")
    @DeleteMapping("/remove/store/auto_mark/{storeGuid}")
    public void removeStoreAutoMark(@PathVariable String storeGuid) {
        log.info("删除门店当前快餐自动号牌数,storeGuid:{}", storeGuid);
        fastFoodService.removeStoreAutoMark(storeGuid);
    }

    @ApiOperation(value = "绑定会员消费guid", notes = "绑定会员消费guid")
    @PostMapping("/update_member_consumption_guid")
    public void updateMemberConsumptionGuid(@RequestBody OrderDTO orderDTO) {
        log.info("绑定会员消费guid入参,orderDTO:{}", JacksonUtils.writeValueAsString(orderDTO));
        fastFoodService.updateMemberConsumptionGuid(orderDTO);
    }

    @ApiOperation(value = "更新订单商品信息")
    @PostMapping("/update_order_item_info")
    public void updateOrderItemInfo(@RequestBody UpdateOrderItemInfoDTO orderItemInfoDTO) {
        log.info("[更新订单商品信息]入参,orderItemInfoDTO={}", JacksonUtils.writeValueAsString(orderItemInfoDTO));
        fastFoodService.updateOrderItemInfo(orderItemInfoDTO);
    }

}
