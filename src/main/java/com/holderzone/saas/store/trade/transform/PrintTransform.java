package com.holderzone.saas.store.trade.transform;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintTransform
 * @date 2019/12/09 17:30
 * @description //TODO
 * @program IdeaProjects
 */
@Mapper
public interface PrintTransform {

    PrintTransform INSTANCE = Mappers.getMapper(PrintTransform.class);

    DineInItemDTO createDineInItemDTOFromSub(SubDineInItemDTO subDineInItemDTO);

}
