package com.holderzone.saas.store.trade.aop;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.bo.UserDefinedCodeBO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.exception.TableBusinessLockException;
import com.holderzone.saas.store.dto.trade.exception.OrderLockException;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.ResultCodeEnum;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.DineInService;
import com.holderzone.saas.store.trade.service.OrderLockService;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.CommonUtil;
import com.holderzone.saas.store.trade.utils.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderPessimismLockAspect
 * @date 2019/09/16 16:27
 * @description //悲观锁，为了加入Order使切口有序，移动原来的逻辑到此
 * @program IdeaProjects
 */
@Aspect
@Component
@Slf4j
@Order(2)
public class OrderPessimismLockAspect {

    @Autowired
    OrderLockService orderLockService;

    @Autowired
    private DineInService dineInService;

    @Autowired
    private OrderService orderService;


    @Pointcut("@annotation(com.holderzone.saas.store.trade.anno.RequireOrderCheckLock)")
    public void checkOrderIsLock() {

    }

    @Pointcut("@annotation(com.holderzone.saas.store.trade.anno.RequireOrderPessimismLock)")
    public void doOrderPessimismLock() {

    }


    /**
     * 加锁解锁redis悲观锁的切面（目前只切了普通支付的加锁解锁，聚合支付的加锁解锁操作在trade服务）
     * 方法参数  要么继承BaseTableDto 要么在字段上加 @OrderLockField 注解
     *
     * @param point
     * @return
     */
    @Around("doOrderPessimismLock()")
    public Object lockOrder(ProceedingJoinPoint point) throws Throwable {
        BillPayReqDTO parameter = (BillPayReqDTO) point.getArgs()[0];
        String orderId = parameter.getOrderGuid();
        String deviecId = parameter.getDeviceId();
        List<BillPayReqDTO.Payment> payments = parameter.getPayments();
        if (payments != null && payments.size() == 1) {
            for (BillPayReqDTO.Payment payment : payments) {
                //========= 通联支付和人脸支付需要特别处理 =======================
                boolean isFaceOrTonglianpay = payment.getPaymentType().equals(PaymentTypeEnum.FACE.getCode())
                        || (payment.getPaymentType().equals(PaymentTypeEnum.CARD.getCode()) && StringUtils.isNotEmpty(payment.getFaceCode()));
                if (isFaceOrTonglianpay) {
                    return point.proceed();
                }
            }
        }
        if (!CommonUtil.hasGuid(orderId)) {
            throw new BusinessException("订单号异常");
        }
        boolean lockResult = orderLockService.tryLockwithDeviceId(orderId, deviecId);
        if (!lockResult) {
            throw new OrderLockException(UserDefinedCodeBO.buildDefindResultJSONString(ResultCodeEnum
                    .ORDER_PAY_LOCK_EXCEPTION));
        }
        try {
            return point.proceed();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            log.warn("Aspect分布式锁异常 e={}", throwable.getMessage());
            throw new BusinessException(throwable.getMessage());
        } finally {
            orderLockService.unlock(orderId);
        }
    }


    /**
     * 校验是否锁定的切面，校验通过后增加redis乐观锁的版本号
     *
     * @param point
     * @return
     */
    @Around("checkOrderIsLock()")
    public Object whetherLocked(ProceedingJoinPoint point) {
        Object parameter = point.getArgs()[0];
        String orderGuid = OrderUtil.getOrderGuidFromParm(parameter);
        if (!CommonUtil.hasGuid(orderGuid)) {
            throw new BusinessException("订单号异常");
        }
        boolean lockResult = orderLockService.tryLockwithDeviceId(orderGuid, null);
        try {
            //如果订单被锁住了
            if (!lockResult) {
                throw new OrderLockException();
            } else if (verifyCalculate(parameter)) {
                boolean flag = true;
                OrderDO orderDO = orderService.getByIdWithCache(orderGuid);

                //兼容本地化
                if (orderDO == null) {
                    throw new BusinessException("未查询到订单");
                }

                //1.如果是快餐，不锁定，直接放行，返回
                if (orderDO.getTradeMode().equals(TradeModeEnum.FAST.getCode())) {
                    return point.proceed();
                }

                //2.如果是并桌方法，则需要特殊处理
                String method = point.getSignature().getName();
                List<String> orderGuidList;
                if ("combine".equals(method)) {
                    TableOrderCombineDTO tableOrderCombineDTO = (TableOrderCombineDTO) parameter;
                    List<TableInfoDTO> tableInfoLists = tableOrderCombineDTO.getTableInfoDTOS();
                    orderGuidList = tableInfoLists.stream().map(TableInfoDTO::getOrderGuid).collect(Collectors.toList());
                    for (int i = 0; i < orderGuidList.size(); i++) {
                        flag = dineInService.addVersion(orderGuidList.get(i));
                        if (!flag) {
                            throw new TableBusinessLockException("订单增加版本号失败");
                        }
                    }
                    flag = dineInService.addVersion(tableOrderCombineDTO.getMainOrderGuid());
                    if (!flag) {
                        throw new TableBusinessLockException("订单增加版本号失败");
                    }
                    return point.proceed();
                }
                //2.若无并台的情况，直接递增，并返回
                if (orderDO.getUpperState().equals(UpperStateEnum.GENERAL.getCode())) {
                    flag = dineInService.addVersion(orderGuid);
                    if (!flag) {
                        throw new TableBusinessLockException("订单增加版本号失败");
                    }
                    return point.proceed();
                }
                //3.若有并台的情况，先查询到主单
                String mainOrderGuid = orderGuid;
                if (orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
                    mainOrderGuid = String.valueOf(orderDO.getMainOrderGuid());
                }
                //4.查出主单+子单,version加1
                List<String> mainAndSubGuids = orderLockService.getMainAndSubGuids(mainOrderGuid);
                if (CollectionUtil.isNotEmpty(mainAndSubGuids)) {
                    for (int i = 0; i < mainAndSubGuids.size(); i++) {
                        flag = dineInService.addVersion(mainAndSubGuids.get(i));
                    }
                }
                if (!flag) {
                    throw new TableBusinessLockException("订单增加版本号失败");
                }
            }
            return point.proceed();
        } catch (Throwable throwable) {
            log.warn("Aspect校验业务锁异常 ", throwable);
            throw new BusinessException(throwable.getMessage());
        } finally {
            orderLockService.unlock(orderGuid);
        }
    }

    /**
     * 校验计算价格
     *
     * @param parameter 切点参数
     * @return 如果需要加锁，返回true
     */
    public boolean verifyCalculate(Object parameter) {
        if (parameter instanceof BillCalculateReqDTO) {
            BillCalculateReqDTO billCalculateReqDTO = (BillCalculateReqDTO) parameter;
            //不需要锁
            boolean decideVerifynotneedLock = billCalculateReqDTO.getVerify() == null || (billCalculateReqDTO.getVerify() != 1 && billCalculateReqDTO.getVerify() != 2);
            boolean decideMemberLoginnotneedlock = billCalculateReqDTO.getMemberLogin() == null || (billCalculateReqDTO.getMemberLogin() != 1 && billCalculateReqDTO.getMemberLogin() != 2);
            return !(decideMemberLoginnotneedlock && decideVerifynotneedLock);
        }
        return true;
    }

}