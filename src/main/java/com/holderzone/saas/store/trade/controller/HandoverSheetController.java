package com.holderzone.saas.store.trade.controller;


import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.trade.service.HandoverSheetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverSheetController
 * @date 2019/04/08 10:28
 * @description 交接班接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/handover_sheet")
@Api(description = "交接班接口")
@Slf4j
public class HandoverSheetController {

    private final HandoverSheetService handoverSheetService;


    @Autowired
    public HandoverSheetController(HandoverSheetService handoverSheetService) {
        this.handoverSheetService = handoverSheetService;
    }

    @ApiOperation(value = "交接班查询记录")
    @PostMapping("/handover")
    public HandoverPayDTO handover(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        return handoverSheetService.handover(handoverPayQueryDTO);
    }

    @ApiOperation(value = "交接班查询记录")
    @PostMapping("/retail/handover")
    public HandoverPayDTO retailHandover(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        log.info("查询零售交接班数据，入参：{}",JacksonUtils.writeValueAsString(handoverPayQueryDTO));
        return handoverSheetService.retailHandover(handoverPayQueryDTO);
    }

    @ApiOperation(value = "交接班查询记录（新）", notes = "会员充值详情不合并，现金支付金额单独列出")
    @PostMapping("/handoverNew")
    public HandoverPayDTO handoverNew(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        log.info("交接班查询记录（新）,handoverPayQueryDTO={}", JacksonUtils.writeValueAsString(handoverPayQueryDTO));
        return handoverSheetService.handoverNew(handoverPayQueryDTO);
    }

    @ApiOperation(value = "交接班查询第三方活动汇总")
    @PostMapping("/handoverNew/third_activity")
    public List<AmountItemDTO.InnerDetails> handoverNewByThirdActivity(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        return handoverSheetService.handoverNewByThirdActivity(handoverPayQueryDTO);
    }

    @ApiOperation(value = "交接班查询优惠方式汇总")
    @PostMapping("/handoverNew/discount")
    public List<AmountItemDTO> handoverNewByDiscount(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        return handoverSheetService.handoverNewByDiscount(handoverPayQueryDTO);
    }
}
