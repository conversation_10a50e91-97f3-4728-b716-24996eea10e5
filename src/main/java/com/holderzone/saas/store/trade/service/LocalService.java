package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.trade.DeleteQuery;
import com.holderzone.saas.store.dto.trade.LocalDTO;
import com.holderzone.saas.store.dto.trade.LocalQuery;
import com.holderzone.saas.store.dto.trade.SingleQuery;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-order
 */
public interface LocalService {

    /**
     * 本地化数据拉取
     */
    LocalDTO getLocal(LocalQuery localQuery);


    /**
     * 本地化数据接收
     */
    Map<String, String> saveLocal(LocalDTO localDTO);

    /**
     * 本地化单条数据拉取
     */
    LocalDTO getSingle(SingleQuery singleQuery);

    void test();

    /**
     * 本地化数据还原
     */
    Boolean deleteLocal(DeleteQuery deleteQuery);

}
