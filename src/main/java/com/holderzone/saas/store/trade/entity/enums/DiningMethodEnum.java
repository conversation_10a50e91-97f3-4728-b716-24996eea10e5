package com.holderzone.saas.store.trade.entity.enums;

public enum DiningMethodEnum {

    DINNER(0, "堂食"),
    FASTFOOD(1, "快餐"),;

    private int code;
    private String desc;

    DiningMethodEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (DiningMethodEnum c : DiningMethodEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
