package com.holderzone.saas.store.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.common.BaseRespDTO;
import com.holderzone.saas.store.dto.common.RedisReqDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadModifyGuestsNoReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadOrderPlacementReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadPayInfoReqDTO;
import com.holderzone.saas.store.dto.trade.resp.*;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.ConfirmConfigTaskDTO;
import com.holderzone.saas.store.trade.entity.domain.PadOrderDO;

import java.util.List;

/**
 * <p>
 * pad订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
public interface PadOrderService extends IService<PadOrderDO> {

    /**
     * pad下单
     *
     * @param orderPlacementReqDTO pad下单请求实体
     * @return 订单guid
     */
    PadOrderPlacementRespDTO orderPlacement(PadOrderPlacementReqDTO orderPlacementReqDTO);

    /**
     * 推送订单信息
     *
     * @param padOrderRespDTO pad下单信息
     * @return BusinessMessageDTO
     */
    BusinessMessageDTO pushOrderMsg(PadOrderRespDTO padOrderRespDTO);

    /**
     * 推送消息
     *
     * @param padOrderRespDTO pad下单信息
     */
    void pushMsg(PadOrderRespDTO padOrderRespDTO);

    /**
     * pad购物车价格计算
     *
     * @param orderPlacementReqDTO 购物车商品数据
     * @return 购物车总价
     */
    PadPriceRespDTO calculateShopCar(PadOrderPlacementReqDTO orderPlacementReqDTO);

    /**
     * 查询门店下的下单表
     *
     * @param request 订单
     * @return 下单列表
     */
    List<PadOrderRespDTO> listPadOrder(WxStoreMerchantOrderReqDTO request);

    /**
     * 根据guid查询pad下单信息
     *
     * @param guid 下单表guid
     * @return 下单信息
     */
    PadOrderRespDTO getPadOrderByGuid(String guid);

    /**
     * 远程调用获取pad下单的加菜商品信息
     *
     * @param key 订单guid+下单表guid，以:分割
     * @return 加菜商品信息
     */
    CreateDineInOrderReqDTO getPadOrderAddItemInfoByRedis(String key);

    /**
     * 功能描述：门店配置延迟处理订单确认和提示
     *
     * @param taskDTO 延时任务处理实体
     * @date 2021.08.31
     */
    void dealRedisDelayedTask(ConfirmConfigTaskDTO taskDTO);

    /**
     * 查询pad订单详情
     *
     * @param orderGuid 订单guid
     * @return pad订单详情
     */
    PadOrderInfoRespDTO queryPadOrderInfo(String orderGuid);

    /**
     * 修改就餐人数
     *
     * @param modifyGuestsNoReqDTO 修改就餐人数请求实体
     * @return 结果
     */
    BaseRespDTO modifyGuestsNo(PadModifyGuestsNoReqDTO modifyGuestsNoReqDTO);

    /**
     * 保存pad支付信息到缓存
     *
     * @param padPayInfoReqDTO pad支付信息
     * @return Boolean
     */
    Boolean savePadPayInfoToRedis(PadPayInfoReqDTO padPayInfoReqDTO);

    /**
     * 获取缓存pad支付信息
     *
     * @param orderGuid 订单guid
     * @return pad支付信息
     */
    PadPayInfoReqDTO getPadPayInfo(String orderGuid);

    /**
     * 通过订单数据发起预支付下单
     * 生成pad支付二维码
     *
     * @param orderGuid 订单guid
     * @return pad支付二维码
     */
    PadQrCodeRespDTO getPadQrCode(String orderGuid);

    String getOrderPayQrCode(String orderGuid);

    /**
     * 获取订单并台的所有下单信息
     *
     * @param orderGuid 订单guid
     * @return pad下单消息
     */
    List<PadOrderRespDTO> listPadOrderInfoOnCombine(String orderGuid);

    /**
     * 保存pad转台信息
     *
     * @param redisReqDTO redis请求实体
     * @return 结果
     */
    Boolean savePadTurnInfo(RedisReqDTO redisReqDTO);

    /**
     * 获取pad转台信息
     *
     * @param redisKey key
     * @return pad转台信息
     */
    String getPadTurnInfo(String redisKey);

    /**
     * 根据桌台查询订单guid和人数
     *
     * @param tableGuid 桌台guid
     * @return 订单guid和人数
     */
    PadRebindRespDTO queryOrderAndGuestByTable(String tableGuid);

    /**
     * 根据并台主单guid查询pad下单信息列表
     *
     * @param combineOrderGuid 并台主单guid
     * @return 下单信息列表
     */
    List<PadOrderRespDTO> listPadOrderByCombineOrderGuid(String combineOrderGuid);

    /**
     * 获取所有订单的pad下单数据
     *
     * @param orderGuid 当前订单guid
     * @return pad下单数据
     */
    List<PadOrderRespDTO> listPadOrderDTOList(String orderGuid);

    /**
     * pad退出登录处理
     * 撤销验券和删除支付信息
     *
     * @param billCalculateReqDTO 计算接口的入参，用于撤销验券
     */
    Boolean padSignOut(BillCalculateReqDTO billCalculateReqDTO);

    void updatePhone(Long guid, String memberPhoneNumber);

}
