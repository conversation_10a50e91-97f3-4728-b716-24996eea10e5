package com.holderzone.saas.store.trade.utils;


import com.holderzone.saas.store.trade.entity.constant.OrderRedisConstant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisKeyUtil
 * @date 2018/09/04 11:26
 * @description
 * @program holder-saas-store-trade
 */
public class RedisKeyUtil {

    private RedisKeyUtil() {
    }

    /**
     * 生成redisKey
     *
     * @param businessGroup
     * @param businessKey
     * @return
     */
    public static String getKey(String businessGroup, String businessKey) {
        StringBuilder buffer = new StringBuilder();
        buffer.append(OrderRedisConstant.ORDER_GROUP).append(":");
        buffer.append(businessGroup).append(":");
        buffer.append(businessKey);
        return buffer.toString();
    }

    public static String getHstOrderKey(String businessKey) {
        return getKey(OrderRedisConstant.HST_ORDER_GROUP, businessKey);
    }

    public static String getHstOrderItemKey(String businessKey) {
        return getKey(OrderRedisConstant.HST_ORDER_ITEM_GROUP, businessKey);
    }

    public static String getHstOrderVersionKey(String businessKey) {
        return getKey(OrderRedisConstant.HST_ORDER_VERSION_GROUP, businessKey);
    }

    public static String getHstOrderLockKey(String lockKey) {
        return getKey(OrderRedisConstant.HST_ORDER_LOCK_GROUP, lockKey);
    }

    public static String getSubOrderGuidCacheKey(String key) {
        return getKey(OrderRedisConstant.HST_ORDER_CHILD_CACHE_GROUP, key);
    }

    public static String getAbnormalOrder(String key) {
        return OrderRedisConstant.ABNORMAL_ORDER + key;
    }

    public static String getBillPayInfo(String key) {
        return OrderRedisConstant.BILL_PAY_INFO + key;
    }

    public static String getOrderPaymentInitiateMethod(String key) {
        return getKey(OrderRedisConstant.HST_ORDER_PAYMENT_INITIATE_METHOD, key);
    }

    public static String getOrderPayGuid(String key) {
        return getKey(OrderRedisConstant.HST_ORDER_PAY_GUID, key);
    }

    public static String getPadOrderAddItemInfo(String key) {
        return OrderRedisConstant.PAD_ORDER + key;
    }

    /**
     * 生成pad支付信息key
     *
     * @param orderGuid 订单guid
     * @return pad支付信息key
     */
    public static String getPadPayInfo(String orderGuid) {
        return OrderRedisConstant.PAD_PAY_INFO + orderGuid;
    }

    /**
     * 生成随行红包信息key
     *
     * @param orderGuid 主单guid
     * @return key
     */
    public static String getRedPacketInfoKey(String orderGuid) {
        return OrderRedisConstant.RED_PACKET_INFO + orderGuid;
    }
}