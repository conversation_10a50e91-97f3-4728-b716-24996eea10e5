package com.holderzone.saas.store.trade.context;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.trade.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-06-25
 * @description
 */
@Data
@Slf4j
public class DiscountContext {

    private Map<Integer, DiscountDO> discountTypeMap;

    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    private BillCalculateReqDTO billCalculateReqDTO;

    private DiscountFeeDetailDTO freeDiscount;

    private Map<String, DineInItemDTO> dineInItemDTOMap;

    private List<DineInItemDTO> allItems;

    private DineinOrderDetailRespDTO orderDetailRespDTO;

    private BigDecimal thirdActivityDiscountFee;

    private OrderDO orderDO;

    private DiscountRuleBO discountRuleBO;

    private boolean hasMember;

    private boolean canMemberPrice;

    private Integer volumeCodeType;

    private boolean integralStore;

    private List<OrderDO> subOrderDOS;

    private boolean rejectDiscount;

    /**
     * 是否会员折扣
     */
    private boolean isMemberDiscount;

    /**
     * 是否取商品购买价
     */
    private boolean isUseBuyPrice;

    public static DiscountContext init(DineinOrderDetailRespDTO orderDetailRespDTO,
                                       BillCalculateReqDTO billCalculateReqDTO, OrderDO orderDO,
                                       List<OrderDO> subOrderDOS, DiscountRuleBO discountRuleBO,
                                       List<DiscountDO> discountDOS, boolean rejectDiscount, boolean useBuyPrice) {
        DiscountContext discountContext = new DiscountContext();
        discountContext.setBillCalculateReqDTO(billCalculateReqDTO);
        discountContext.setOrderDO(orderDO);
        discountContext.setSubOrderDOS(subOrderDOS);
        discountContext.setOrderDetailRespDTO(orderDetailRespDTO);
        //每次calculate由第一个会员优惠接口生成新memberConsumptionGuid
        discountRuleBO.setFristMemberDiscountClient(true);
        discountContext.setDiscountRuleBO(discountRuleBO);
        //是否并单
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetailRespDTO.getSubOrderDetails();
        boolean combine = CollectionUtil.isNotEmpty(subOrderDetails);
        log.warn("是否并单：{}", combine);
        boolean hasMember = StringUtils.isNotEmpty(orderDO.getMemberGuid()) && !"0".equals(orderDO.getMemberGuid());
        log.warn("是否登陆会员：{}", hasMember);
        discountContext.setHasMember(hasMember);
        boolean canMemberPrice = discountRuleBO.isCanCaculatByMemberPrice();
        orderDetailRespDTO.setSingleItemUsedMemeberPrice(canMemberPrice ? 1 : 0);
        log.warn("是否能用会员价：{}", canMemberPrice);
        discountContext.setCanMemberPrice(canMemberPrice);
        discountContext.setMemberDiscount(billCalculateReqDTO.getIsMemberDiscount());
        //积分商城
        boolean integralStore = ObjectUtil.isNotNull(billCalculateReqDTO.getMemberIntegralStore())
                && ObjectUtil.equal(1, billCalculateReqDTO.getMemberIntegralStore());
        log.warn("是否是积分商城：{}", integralStore);
        discountContext.setIntegralStore(integralStore);
        //剩余可优惠金额
        orderDetailRespDTO.setOrderSurplusFee(orderDetailRespDTO.getOrderFee());
        // 团购加菜处理
        orderDetailRespDTO.setGrouponFee(BigDecimal.ZERO);

        //计算折扣优惠
        Map<Integer, DiscountDO> discountTypeMap = CollectionUtil.toMap(discountDOS, "discountType");
        discountContext.setDiscountFeeDetailDTOS(Lists.newArrayList());
        discountContext.setDiscountTypeMap(discountTypeMap);
        //并单把所有菜放在一起算
        List<DineInItemDTO> allItems = Lists.newArrayList(orderDetailRespDTO.getDineInItemDTOS());
        if (combine) {
            for (DineinOrderDetailRespDTO subOrderDetail : subOrderDetails) {
                allItems.addAll(subOrderDetail.getDineInItemDTOS());
            }
        }
        allItems.forEach(i -> i.setDiscountTotalPrice(i.getItemPrice()));
        log.warn("所有菜品：{}", JacksonUtils.writeValueAsString(allItems));
        discountContext.setAllItems(allItems);
        //所有菜品map用于修改单个菜品优惠金额
        Map<String, DineInItemDTO> dineInItemDTOMap = CollectionUtil.toMap(allItems, "guid");
        discountContext.setDineInItemDTOMap(dineInItemDTOMap);
        discountContext.setRejectDiscount(rejectDiscount);
        discountContext.setUseBuyPrice(useBuyPrice);
        return discountContext;
    }

}
