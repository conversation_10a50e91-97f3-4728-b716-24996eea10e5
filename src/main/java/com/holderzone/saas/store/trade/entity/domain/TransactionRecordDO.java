package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单交易记录
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_transaction_record")
public class TransactionRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 聚合支付商户的appId
     */
    private String appId;

    /**
     * 聚合支付对应的支付方式
     */
    private Integer payPowerId;

    /**
     * 聚合支付对应的支付方式名称
     */
    private String payPowerName;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品信息
     */
    private String body;

    /**
     * 支付单附加描述
     */
    private String description;

    /**
     * 设备号
     */
    private String terminalId;

    /**
     * 聚合支付订单号
     */
    private String jhOrderGuid;

    /**
     * 银行流水号
     */
    private String bankTransactionId;

    /**
     * 支付二维码链接地址
     */
    private String codeUrl;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 聚合支付折扣金额
     */
    private BigDecimal discountFee;

    /**
     * 剩余可退款金额
     */
    private BigDecimal refundableFee;

    /**
     * 交易类型，1:正常支付转入，2:会员充值转入，3:预付金转入，4:定金转入，5:正常支付退款，6:退单退款，7:会员余额退款，8:预付金退款，9:定金退款，10:自定义退款
     */
    private Integer tradeType;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功
     */
    private Integer state;

    /**
     * 支付方式 1：现金支付，2：聚合支付，3：银行卡支付，4:会员卡支付，5:人脸支付，6:通吃岛支付，7:预定金支付，10：其他支付方式
     */
    private Integer paymentType;

    /**
     * 支付方式名
     */
    private String paymentTypeName;

    /**
     * 支付单创建时间
     */
    private LocalDateTime createTime;

    /**
     * 会员相关信息
     */
    private String memberGuid;

    /**
     * 营业日时间
     */
    private LocalDate businessDay;

    /**
     * 支付时间
     */
    private LocalDateTime paidTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 人脸支付失败时安卓自己随机加的3位数字
     */
    private String faceCode;

    /**
     * 操作人员guid
     */
    private String staffGuid;

    /**
     * 操作人名字
     */
    private String staffName;

    /**
     * 门店名
     */
    private String storeName;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 是否多次支付
     */
    private Boolean isMultipleAggPay;

    /**
     * 交易记录guid
     * 多笔聚合支付关联的 transactionRecord表的guid
     */
    @TableField(exist = false)
    private Long transactionRecordGuid;
}
