package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 退款记录表
 * </p>
 */
@Data
@TableName("hst_order_refund_record")
public class OrderRefundRecordDO implements Serializable {

    private static final long serialVersionUID = -7038994595837071569L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 退款记录编号
     */
    private Long recordNo;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 退款订单guid
     */
    private Long refundOrderGuid;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款方式 0原路退回 1线下退款
     */
    private Integer refundType;

    /**
     * 退款明细
     */
    private String refundDetails;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 授权操作人guid
     */
    private String authStaffGuid;

    /**
     * 授权操作人name
     */
    private String authStaffName;

    /**
     * 创建操作人guid
     */
    private String createStaffGuid;

    /**
     * 创建操作人name
     */
    private String createStaffName;

    /**
     * 设备类型(订单来源BaseDeviceTypeEnum)
     */
    private Integer deviceType;

    /**
     * 授权截图
     */
    private String picture;
}
