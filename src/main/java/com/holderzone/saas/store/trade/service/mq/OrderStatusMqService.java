package com.holderzone.saas.store.trade.service.mq;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.trade.config.RocketMqConfig;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/9/24 10:12
 * @description
 */
public interface OrderStatusMqService {

    boolean orderStatusChangeWithMq(OrderDetailPushMqDTO orderDetailPushMqDTO);

    @Component
    class OrderStatusMqServiceImpl implements OrderStatusMqService{

        private static final Logger log = LoggerFactory.getLogger(KdsMqService.class);

        private final DefaultRocketMqProducer defaultRocketMqProducer;

        @Autowired
        public OrderStatusMqServiceImpl(DefaultRocketMqProducer defaultRocketMqProducer){
            this.defaultRocketMqProducer=defaultRocketMqProducer;
        }


        @Override
        public boolean orderStatusChangeWithMq(OrderDetailPushMqDTO orderDetailPushMqDTO) {
            if (log.isInfoEnabled()) {
                log.info("订单结账完成同步入参Mq：{}", JacksonUtils.writeValueAsString(orderDetailPushMqDTO));
            }
            Message message = new Message(
                    RocketMqConfig.ORDER_STATUS_CHANGE_MQ_TOPIC,
                    RocketMqConfig.ORDER_STATUS_CHANGE__MQ_TAG,
                    JacksonUtils.toJsonByte(orderDetailPushMqDTO)
            );
            return defaultRocketMqProducer.sendMessage(message);
        }
    }
}
