package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOperationDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.open.WxSendMessageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxUpdateOrderRecordStateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> R
 * @date 2020/10/26 9:31
 * @description
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxStoreClientService.FallBack.class)
public interface WxStoreClientService {

    @ApiOperation("获取下单手机号")
    @PostMapping(value = "/wx_store_order_record/get_merchant_order_phone")
    WxStoreMerchantOrderDTO getMerchantOrderPhone(@RequestParam(value = "orderGuid") String orderGuid);

    /**
     * 查询门店配置
     *
     * @param storeGuid 门店guid
     * @return 门店配置
     */
    @ApiOperation("查询门店配置")
    @GetMapping("/wx-store-menu-provide/store_config")
    WxOrderConfigDTO getStoreConfig(@RequestParam("storeGuid") String storeGuid);

    /**
     * 微信和pad接单
     *
     * @param wxOperateReqDTO 微信和pad接单信息
     * @return 微信接单返回，pad也用这个返回
     */
    @ApiOperation("商户处理订单，商户接单")
    @PostMapping(value = "/wx_store_merchant_order/operate")
    WxStoreMerchantOperationDTO operationMerchantOrder(@RequestBody WxOperateReqDTO wxOperateReqDTO);

    @PostMapping("/wx_handler/send_call_message")
    @ApiOperation("发送商家出餐通知")
    void sendCallMessage(@RequestBody WxSendMessageReqDTO sendMessageReqDTO);

    @ApiOperation("修改订单状态")
    @PostMapping(value = "/wx_store_order_record/update_order_record_state")
    void updateOrderRecordState(@RequestBody WxUpdateOrderRecordStateReqDTO reqDTO);

    @Slf4j
    @Component
    class FallBack implements FallbackFactory<WxStoreClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WxStoreClientService create(Throwable throwable) {
            return new WxStoreClientService() {

                @Override
                public WxStoreMerchantOrderDTO getMerchantOrderPhone(String orderGuid) {
                    log.error("微信获取下单手机号调用异常，e={}", throwable.getMessage());
                    return new WxStoreMerchantOrderDTO();
                }

                @Override
                public WxOrderConfigDTO getStoreConfig(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getStoreConfig", storeGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public WxStoreMerchantOperationDTO operationMerchantOrder(WxOperateReqDTO wxOperateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "operationMerchantOrder", JacksonUtils.writeValueAsString(wxOperateReqDTO)
                            , ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void sendCallMessage(WxSendMessageReqDTO sendMessageReqDTO) {
                    log.error(HYSTRIX_PATTERN, "sendCallMessage", JacksonUtils.writeValueAsString(sendMessageReqDTO)
                            , ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateOrderRecordState(WxUpdateOrderRecordStateReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateOrderRecordState", JacksonUtils.writeValueAsString(reqDTO)
                            , ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
