package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.response.dinein.RefundOrderRecordDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderRefundRecordDO;
import com.holderzone.saas.store.trade.mapper.OrderRefundRecordMapper;
import com.holderzone.saas.store.trade.repository.interfaces.OrderRefundRecordService;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 订单退款记录 服务实现类
 * </p>
 */
@Service
@RequiredArgsConstructor
public class OrderRefundRecordServiceImpl extends ServiceImpl<OrderRefundRecordMapper, OrderRefundRecordDO> implements OrderRefundRecordService {

    @Override
    public List<RefundOrderRecordDTO> listByRefundOrderGuid(Long refundOrderGuid) {
        return baseMapper.listByRefundOrderGuid(refundOrderGuid);
    }

    @Override
    public Long getMaxRecordNoByRefundOrderGuid(Long refundOrderGuid) {
        QueryWrapper<OrderRefundRecordDO> qw = new QueryWrapper<>();
        qw.lambda().eq(OrderRefundRecordDO::getRefundOrderGuid, refundOrderGuid);
        qw.lambda().orderByDesc(OrderRefundRecordDO::getGmtCreate);
        qw.last("limit 0,1");
        OrderRefundRecordDO orderRefundRecord = getOne(qw);
        if (Objects.isNull(orderRefundRecord)) {
            return null;
        }
        return orderRefundRecord.getRecordNo();
    }

    @Override
    public BigDecimal handoverRefund(@Param("dto") HandoverPayQueryDTO request) {
        return baseMapper.handoverRefund(request);
    }
}
