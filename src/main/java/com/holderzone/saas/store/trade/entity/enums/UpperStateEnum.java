package com.holderzone.saas.store.trade.entity.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UpperStateEnum
 * @date 2018/10/23 11:54
 * @description
 * @program holder-saas-store-trade
 */
public enum UpperStateEnum {

    GENERAL(0, "无并单"),
    /**
     * 并台订单
     */
    MAIN(1, "主单"),
    SUB(2, "子单"),
    /**
     * 结账不关台的订单
     */
    SAME_MAIN(3, "同桌台主单"),
    SAME_SUB(4, "同桌台子单"),
    ;

    private int code;
    private String desc;

    /**
     * 并台桌台订单的状态
     */
    public static final List<Integer> COMBINE_ORDER_STATE = Lists.newArrayList(MAIN.getCode(), SUB.getCode());

    /**
     * 结账不关台订单的状态
     */
    public static final List<Integer> SAME_ORDER_STATE = Lists.newArrayList(SAME_MAIN.getCode(), SAME_SUB.getCode());

    UpperStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (UpperStateEnum c : UpperStateEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
