package com.holderzone.saas.store.trade.service;


import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreGatherTotalRespDTO;
import com.holderzone.saas.store.dto.order.local.FreeReturnItemDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.response.OrderUploadRespDTO;
import com.holderzone.saas.store.dto.trade.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/1 11:37
 * @description
 */
public interface OrderDetailService {

    /**
     * 分页查询订单数据
     * (包含订单数据、商品数据、优惠数据)
     */
    Page<OrderInfoRespDTO> pageOrderInfo(DineInOrderListReqDTO reqDTO);

    /**
     * 微信小程序分页查询订单数据
     * (包含订单数据、商品数据)
     */
    Page<OrderInfoRespDTO> pageWxOrderInfo(DineInOrderListReqDTO reqDTO);


    /***
     * 查询订单数据
     * @param orderGuid
     * @return
     */
    OrderDTO findByOrderGuid(String orderGuid);

    /***
     *  查订单流水数据
     * @param orderGuid
     * @return
     */
    List<OrderItemRecordDTO> getOrderItemRecord(String orderGuid);

    /***
     *  查属性
     * @param orderGuid
     * @return
     */
    List<ItemAttrDetailDTO> getItemAttr(String orderGuid);

    /**
     * 查附加费
     *
     * @param orderGuid
     * @param enterpriseGuid
     * @return
     */
    List<AppendFeeDTO> getAppendFee(String orderGuid, String enterpriseGuid);

    /**
     * 获取订单交易记录
     *
     * @param orderGuid
     * @param enterpriseGuid
     * @return
     */
    List<TransactionRecordDTO> getTransactionRecord(String orderGuid, String enterpriseGuid);

    /**
     * 获取订单优惠记录
     *
     * @param orderGuid
     * @param enterpriseGuid
     * @return
     */
    List<DiscountDTO> getDiscount(String orderGuid, String enterpriseGuid);

    /**
     * 获取赠送/退货记录
     *
     * @param orderGuid
     * @param enterpriseGuid
     * @return
     */
    List<FreeReturnItemDTO> getFreeReturnItem(String orderGuid, String enterpriseGuid);

    /***
     * 根据企业号查询order
     * @param enterpriseGuid
     * @return
     */
    OrderDTO findByEnterpriseGuid(String enterpriseGuid);

    /***
     *  根据订单id修改订单is_handel状态
     * @param orderGuid
     * @return
     */
    Boolean updateOrderIsHandleByOrderGuid(String orderGuid, Integer isHandle);

    /**
     * 获取订单商品
     *
     * @param orderGuid
     * @param enterpriseGuid
     * @return
     */
    List<OrderItemDTO> getOrderItem(Long orderGuid, String enterpriseGuid);

    /**
     * 批量获取订单商品
     */
    List<OrderItemDTO> getOrderItems(List<String> orderGuids, String enterpriseGuid);

    /***
     *  反结账id查订单详情
     * @param recoveryId
     * @return
     */
    List<OrderDTO> findByRecoveryId(String recoveryId);


    void refreshModifyTime(String enterpriseGuid, String orderGuid);

    String getNeedUpdateOrderGuid(String enterpriseGuid);

    boolean refreshModifyTimeByDay(String enterpriseGuid);

    /**
     * 通过订单guid更新订单整单备注
     *
     * @param guid
     * @param remark
     * @return
     */
    boolean updateOrderRemarkById(String guid, String remark);

    /**
     * 通过订单商品guid更新商品备注
     *
     * @param guid
     * @param remark
     * @return
     */
    boolean updateOrderItemRemarkById(String guid, String itemGuid, String remark);

    /**
     * 统计门店汇总
     *
     * @param storeGatherReportReqDTO
     * @return
     */
    StoreGatherTotalRespDTO storeGatherTotalList(StoreGatherReportReqDTO storeGatherReportReqDTO, boolean isExport);

    /**
     * 根据并台主单guid查询定订单信息列表
     *
     * @param combineOrderGuid 并台主单guid
     * @return 订单信息列表
     */
    List<OrderDTO> listOrderByCombineOrderGuid(String combineOrderGuid);

    /**
     * 将订单复制到制定门店
     *
     * @param fileUrl 文件地址
     * @return 操作结果
     */
    OrderUploadRespDTO copyOrders(String fileUrl);

    /**
     * 门店订单数据统计
     *
     * @param storeGuid
     * @return
     */
    Integer queryOrderNumForStoreGuid(String storeGuid);

    /**
     * 查询门店第一笔订单信息
     *
     * @param storeGuid
     * @return
     */
    LocalDateTime queryfristorderForStoreGuid(String storeGuid);

    OrderDTO findByOrderNoAndStoreGuid(String orderNo, String storeGuid);

    /**
     * 获取多个订单详情
     *
     * @param query 订单guid列表
     * @return 多个订单详情
     */
    List<OrderDTO> listByOrderGuid(SingleDataDTO query);

    /***
     * 根据订单查询桌台信息
     */
    OrderTableDTO findTableByOrderGuid(String orderGuid);

    /***
     * 根据订单编号查询订单信息
     */
    List<OrderDTO> listByOrderNoAndStoreGuid(SingleDataDTO query);
}
