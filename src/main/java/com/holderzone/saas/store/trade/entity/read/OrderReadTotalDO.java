package com.holderzone.saas.store.trade.entity.read;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR> R
 * @date 2020/12/2 15:10
 * @description
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderReadTotalDO {
    /**
     * 全局唯一主键
     */
    private Long guid;
    /**
     * 订单Guid
     */
    private String orderGuid;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /***
     * 30以下
     */
    private Integer orLessCount;

    /***
     * 30-100之间
     */
    private Integer betweenCount;

    /***
     * 100 以上
     */
    private Integer theAboveCount;
    /**
     * 门店名称
     */
    private String storeGuid;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 服务员Guid
     */
    private String waiterGuid;
    /**
     *      服务员类型
     *      CALL_WAITER(1, "喊客员"),
     *     SERVE_WAITER(2, "服务员"),
     *     PASS_DISHES_WAITER(3, "传菜员"),
     *     TIDY_WAITER(4, "收台员"),
     *     MOP_WAITER(5, "洗碗员");
     */
    private Integer waiterType;
    /**
     * 服务员名称
     */
    private String waiterName;
    /**
     * 服务员编号
     */
    private String waiterNo;
}
