package com.holderzone.saas.store.trade.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayReserveResultDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalListReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordRespDTO;
import com.holderzone.saas.store.trade.service.OrderAbnormalRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/order_abnormal_record")
public class OrderAbnormalRecordController {

    @Resource
    private OrderAbnormalRecordService orderAbnormalRecordService;

    @ApiOperation("新增异常订单记录")
    @PostMapping("/save_abnormal_record_order")
    public Integer saveAbnormalRecordOrder(@RequestBody OrderAbnormalRecordReqDTO orderAbnormalRecordReqDTO) {

        return orderAbnormalRecordService.saveAbnormalRecordOrder(orderAbnormalRecordReqDTO);
    }

    @ApiOperation("查询异常订单列表")
    @PostMapping("/abnormal_order_list")
    public Page<OrderAbnormalRecordRespDTO> listAbnormalOrders(@RequestBody OrderAbnormalListReqDTO orderAbnormalListReqDTO) {
        return orderAbnormalRecordService.listAbnormalOrders(orderAbnormalListReqDTO);
    }

    @ApiOperation("查询支付结果")
    @PostMapping("/query_payment_result")
    public AggPayPollingRespDTO getPaymentResult(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("查询支付结果 参数{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        AggPayPollingRespDTO ret =orderAbnormalRecordService.getPaymentResult(saasPollingDTO);
        log.info("查询支付结果 返回值{}", JacksonUtils.writeValueAsString(ret));
        return ret;
    }

    @ApiOperation("取消支付")
    @PostMapping("/cancel_payment")
    public AggPayReserveResultDTO cancelPayment(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("取消支付入参,saasPollingDTO={},操作人信息={}", JacksonUtils.writeValueAsString(saasPollingDTO), UserContextUtils.getJsonStr());
        return orderAbnormalRecordService.cancelPayment(saasPollingDTO);
    }
}
