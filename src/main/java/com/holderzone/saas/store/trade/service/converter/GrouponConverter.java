package com.holderzone.saas.store.trade.service.converter;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtMaitonConsumeDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.trade.entity.domain.GrouponDO;
import com.holderzone.saas.store.trade.entity.dto.ClearItemGroupBuyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2023-06-27
 * @description
 */
@Slf4j
public class GrouponConverter {

    private  GrouponConverter(){

    }

    public static GroupVerifyDTO fromGroupRecord(GrouponDO grouponDO){
        if(grouponDO.getGrouponType() == null){
            throw new GroupBuyException("团购类型不能为空");
        }
        GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setGroupBuyType(grouponDO.getGrouponType());
        switch (GroupBuyTypeEnum.groupBuyType(grouponDO.getGrouponType())){
            case  MEI_TUAN:
                groupVerifyDTO.setCode(grouponDO.getCode());
                groupVerifyDTO.setErpId(UserContextUtils.getStoreGuid());
                groupVerifyDTO.setErpName(UserContextUtils.getStoreName());
                groupVerifyDTO.setStoreGuid(UserContextUtils.getStoreGuid());
                groupVerifyDTO.setReceiptChannel(grouponDO.getReceiptChannel());
                groupVerifyDTO.setMaitonConsumeDTO(StringUtils.isEmpty(grouponDO.getMaitonConsumeInfo()) ?
                        null : JacksonUtils.toObject(MtMaitonConsumeDTO.class, grouponDO.getMaitonConsumeInfo()));
                return groupVerifyDTO;
            case DOU_YIN:
                groupVerifyDTO.setCertificateId(grouponDO.getCertificateId());
                groupVerifyDTO.setVerifyId(grouponDO.getVerifyId());
                return groupVerifyDTO;
            case  ALIPAY:
                groupVerifyDTO.setVerifyId(grouponDO.getVerifyId());
                groupVerifyDTO.setErpOrderId(String.valueOf(grouponDO.getOrderGuid()));
                groupVerifyDTO.setUserId(grouponDO.getUserId());
                return groupVerifyDTO;
            case  ABC:
                groupVerifyDTO.setCode(grouponDO.getCode());
                return groupVerifyDTO;
            default:
                throw new GroupBuyException("团购类型不能为空");
        }
    }

    public static GroupVerifyDTO fromGroupMTRecord(String code){
        GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setGroupBuyType(GroupBuyTypeEnum.MEI_TUAN.getCode());
        groupVerifyDTO.setCode(code);
        groupVerifyDTO.setErpId(UserContextUtils.getStoreGuid());
        groupVerifyDTO.setErpName(UserContextUtils.getStoreName());
        groupVerifyDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        return groupVerifyDTO;
    }

    public static ClearItemGroupBuyDTO fromThirdActivityAndOrderCode(ThirdActivityRespDTO thirdActivityDTO, String couponCode, String orderGuid) {
        ClearItemGroupBuyDTO clearItemGroupBuy = new ClearItemGroupBuyDTO();
        clearItemGroupBuy.setItemGuid(thirdActivityDTO.getItemGuid());
        clearItemGroupBuy.setSkuGuid(thirdActivityDTO.getSkuGuid());
        clearItemGroupBuy.setOrderGuid(orderGuid);
        clearItemGroupBuy.setCouponCode(couponCode);
        return clearItemGroupBuy;
    }
}
