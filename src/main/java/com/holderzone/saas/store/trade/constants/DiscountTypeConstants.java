package com.holderzone.saas.store.trade.constants;

/**
 * <AUTHOR>
 * @description 活动类型常量类
 * @date 2021/12/13 18:24
 * @className:
 */
public class DiscountTypeConstants {

    private DiscountTypeConstants() {
    }

    /**
     * 会员折扣
     */
    public static final int MEMBER = 1;

    /**
     * 整单折扣
     */
    public static final int WHOLE = 2;

    /**
     * 整单让价
     */
    public static final int CONCESSIONAL = 3;

    /**
     * 系统省零
     */
    public static final int SYSTEM = 4;

    /**
     * 赠送优惠
     */
    public static final int FREE = 5;

    /**
     * 团购验券
     */
    public static final int GROUPON = 6;

    /**
     * 会员代金券
     */
    public static final int MEMBER_GROUPON = 7;

    /**
     * 积分抵扣
     */
    public static final int POINTS_DEDUCTION = 8;

    /**
     * 会员优惠
     */
    public static final int SINGLE_MEMBER = 9;

    /**
     * 单品折扣
     */
    public static final int SINGLE_DISCOUNT = 10;

    /**
     * 会员商品券
     */
    public static final int GOODS_GROUPON = 11;

    /**
     * 营销活动
     */
    public static final int ACTIVITY = 12;

    /**
     * 通吃岛优惠
     */
    public static final int TONGCHIDAO = 13;

    /**
     * 第三方平台活动
     */
    public static final int THIRD_ACTIVITY = 14;

    /**
     * 其他优惠
     */
    public static final int OTHER = -1;

    /**
     * 优惠字段
     */
    public static final String DISCOUNT = "优惠";


}