package com.holderzone.saas.store.trade.repository;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import com.holderzone.saas.store.dto.erp.SkuInfo;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.tcd.TcdAddOrderReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.repository.feign.ErpClientService;
import com.holderzone.saas.store.trade.repository.feign.StoreClientService;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.DineInPrintService;
import com.holderzone.saas.store.trade.service.DineInService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TcdOrderRepository
 * @date 2019/11/26 14:09
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Component
@Slf4j
public class TcdOrderRepository {

    private final OrderService orderService;
    private final OrderSubsidiaryService orderSubsidiaryService;
    private final OrderItemService orderItemService;
    private final ItemAttrService itemAttrService;
    private final DiscountService discountService;
    private final TransactionRecordService transactionRecordService;
    private final AppendFeeMpService appendFeeMpService;
    private final ErpClientService erpClientService;
    private final DineInService dineInService;
    private final StoreClientService storeClientService;
    private final DineInPrintService dineInPrintService;
    @Resource(name = "checkOutThreadPool")
    private ExecutorService checkOutThreadPool;


    @Autowired
    public TcdOrderRepository(OrderService orderService, OrderItemService orderItemService, ItemAttrService
            itemAttrService, DiscountService discountService, TransactionRecordService transactionRecordService,
                              AppendFeeMpService appendFeeMpService, OrderSubsidiaryService orderSubsidiaryService,
                              ErpClientService erpClientService, DineInService dineInService,
                              StoreClientService storeClientService, DineInPrintService dineInPrintService) {
        this.orderService = orderService;
        this.orderItemService = orderItemService;
        this.itemAttrService = itemAttrService;
        this.discountService = discountService;
        this.transactionRecordService = transactionRecordService;
        this.appendFeeMpService = appendFeeMpService;
        this.orderSubsidiaryService = orderSubsidiaryService;
        this.erpClientService = erpClientService;
        this.dineInService = dineInService;
        this.storeClientService = storeClientService;
        this.dineInPrintService = dineInPrintService;
    }

    public void addTcdOrder(OrderDO orderDO, OrderSubsidiaryDO orderSubsidiaryDO, List<OrderItemDO> orderItemDOS,
                            List<ItemAttrDO> itemAttrDOS, DiscountDO discountDO,
                            TransactionRecordDO transactionRecordDO, List<AppendFeeDO> appendFeeDOS) {

        if (!ObjectUtils.isEmpty(orderDO)) {
            orderService.save(orderDO);
        }

        if (!ObjectUtils.isEmpty(orderSubsidiaryDO)) {
            orderSubsidiaryService.save(orderSubsidiaryDO);
        }

        if (!ObjectUtils.isEmpty(orderItemDOS)) {
            orderItemService.saveBatch(orderItemDOS);
        }

        if (!ObjectUtils.isEmpty(discountDO)) {
            discountService.save(discountDO);
        }

        if (!ObjectUtils.isEmpty(transactionRecordDO)) {
            transactionRecordService.save(transactionRecordDO);
        }

        if (!ObjectUtils.isEmpty(itemAttrDOS)) {
            itemAttrService.saveBatch(itemAttrDOS);
        }

        if (!ObjectUtils.isEmpty(appendFeeDOS)) {
            appendFeeMpService.saveBatch(appendFeeDOS);
        }
    }

    public void reduceStock(List<OrderItemDO> orderItemDOS) {
        List<OrderSkuDTO> erpList = new ArrayList<>();
        OrderSkuDTO orderSkuDTO = new OrderSkuDTO();

        orderSkuDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        orderSkuDTO.setOperatorGuid(UserContextUtils.getUserGuid());
        orderSkuDTO.setOperatorName(UserContextUtils.getUserName());
        orderSkuDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        List<SkuInfo> skuList = new ArrayList<>();

        for (OrderItemDO orderItemDO : orderItemDOS) {
            SkuInfo sku = new SkuInfo();
            sku.setSkuGuid(orderItemDO.getSkuGuid());
            sku.setCount(orderItemDO.getCurrentCount().subtract(orderItemDO.getReturnCount()));
            skuList.add(sku);
        }

        orderSkuDTO.setSkuList(skuList);
        erpList.add(orderSkuDTO);
        //调erp减库存
        log.info("保存成功调erp减库存,erpList={}", JacksonUtils.writeValueAsString(erpList));
        erpClientService.reduceStockForOrderList(erpList);
    }


    public void printBill(TcdAddOrderReqDTO tcdAddOrderReqDTO, OrderDO orderDO) {
        //异步执行结账成功后的打印等操作
        List<DineInItemDTO> dineInItemDTOS = tcdAddOrderReqDTO.getDineInItemDTOS();
        UserContext userContext = UserContextUtils.get();
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setEnterpriseName(tcdAddOrderReqDTO.getEnterpriseName());
        baseDTO.setEnterpriseGuid(tcdAddOrderReqDTO.getEnterpriseGuid());
        baseDTO.setStoreGuid(tcdAddOrderReqDTO.getStoreGuid());
        baseDTO.setStoreName(tcdAddOrderReqDTO.getStoreName());

        baseDTO.setUserGuid(tcdAddOrderReqDTO.getUserGuid());
        baseDTO.setUserName(tcdAddOrderReqDTO.getUserName());
        baseDTO.setAccount(tcdAddOrderReqDTO.getAccount());
        StoreDeviceDTO storeDeviceDTO = storeClientService.getMasterDeviceByStoreGuid(tcdAddOrderReqDTO.getStoreGuid());
        baseDTO.setDeviceId(storeDeviceDTO.getDeviceGuid());
        baseDTO.setDeviceType(storeDeviceDTO.getDeviceType());

        checkOutThreadPool.execute(() -> {
            UserContextUtils.put(userContext);
            dineInPrintService.printOrderItem(baseDTO, orderDO, dineInItemDTOS,
                    PrintOrderItemDTO.ItemInvoiceTypeEnum.ORDER.getType());
        });
    }

}
