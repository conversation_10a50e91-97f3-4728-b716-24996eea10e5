package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CombineOrderService
 * @date 2018/09/04 16:08
 * @description 并拆单
 * @program holder-saas-store-trade
 */
public interface CombineOrderService {

    /**
     * 拆单
     *
     * @param tableOrderCombineDTO
     * @return
     */
    Boolean split(TableOrderCombineDTO tableOrderCombineDTO);

    /**
     * 并单
     *
     * @param tableOrderCombineDTO
     * @return
     */
    Boolean combine(TableOrderCombineDTO tableOrderCombineDTO);

    /**
     * 转台
     *
     * @param tradeTableDTO
     * @return
     */
    Boolean transform(TradeTableDTO tradeTableDTO);
}
