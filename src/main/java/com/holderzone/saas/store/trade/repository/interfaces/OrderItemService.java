package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.journaling.req.SaleCountReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SaleCountRespDTO;
import com.holderzone.saas.store.dto.order.request.item.TransferItemReqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 订单商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface OrderItemService extends IService<OrderItemDO> {

    List<OrderItemDO> listByOrderGuid(Long orderGuid);

    /**
     * 根据订单guid查询未被删除商品
     *
     * @param orderGuid 订单guid
     * @return 商品信息
     */
    List<OrderItemDO> listByOrderGuidWithOutIsDel(Long orderGuid);

    /**
     * 从缓存中拿订单，缓存没有再查数据库
     *
     * @param orderGuid
     * @return
     */
    List<OrderItemDO> listByOrderGuidWithCache(Long orderGuid, Integer deviceType);

    boolean saveBatchWithDeleteCache(Collection<OrderItemDO> entityList, String orderGuid);

    boolean updateBatchByIdWithDeleteCache(Collection<OrderItemDO> entityList, String orderGuid);

    boolean removeByIdsWithDeleteCache(Collection<? extends Serializable> idList, String orderGuid);

    List<OrderItemDO> listByOrderGuidWithOutIsPay(Long orderGuid);

    List<OrderItemDO> listByOrderGuidWithOutSub(Long orderGuid);

    Boolean hasCurrentItem(Long orderGuid);

    Boolean hasItem(Long orderGuid);

    List<OrderItemDO> listByMainItemGuid(Long itemGuid);

    List<OrderItemDO> listByMainItemGuids(Collection<? extends Serializable> idList);

    void deleteByOrderGuid(String orderGuid);

    void deleteByOrderGuids(List<String> orderGuids);

    List<OrderItemDO> listByIdsWithLock(Collection<? extends Serializable> idList);

    List<OrderItemDO> listByOrderLists(List<String> orderLists);

    AdjustByOrderRespDTO listOrderItem(AdjustByOrderItemQuery query);

    void updateBatchIsAdjustItemIsTrue(List<Long> guids);

    void clearGroupBuyFlag(Long orderGuid, String couponCode);

    void batchClearGroupBuyFlag(Long orderGuid, List<String> orderItemGuids);

    List<SaleCountRespDTO> screenSaleType(SaleCountReqDTO saleCountReqDTO);

    List<SaleCountRespDTO> screenSaleItem(SaleCountReqDTO saleCountReqDTO);

    void updateRefundCount(Long orderGuid, Long orderItemGuid, BigDecimal refundCount, BigDecimal freeRefundCount);

    OrderItemDO getByGuidContainsDel(Long orderItemGuid);

    List<OrderItemDO> listByMainItemGuidContainsDel(Long orderItemGuid);

    /**
     * 恢复商品明细
     */
    void restoreOrderItem(List<OrderItemDO> restoreOrderItemList);

    List<OrderItemDO> listAllByGuid(Set<String> guids);
}
