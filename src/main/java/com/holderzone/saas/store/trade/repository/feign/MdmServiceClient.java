package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.mdm.MDMResult;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.dto.user.UserDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


@Component
@FeignClient(value = "holder-saas-store-mdm", fallbackFactory = MdmServiceClient.MdmServiceClientFallBack.class)
public interface MdmServiceClient {

    /**
     * 查询员工
     */
    @GetMapping("/sync_user/find")
    MDMResult<UserDTO> findUser(@RequestParam("userGuid") String userGuid);

    /**
     * 查询组织
     */
    @GetMapping("/sync_organization/query")
    MDMResult<OrganizationDTO> queryOrganization(@RequestParam("thirdNo") String thirdNo);


    @Slf4j
    @Component
    class MdmServiceClientFallBack implements FallbackFactory<MdmServiceClient> {

        @Override
        public MdmServiceClient create(Throwable throwable) {
            return new MdmServiceClient() {
                @Override
                public MDMResult<UserDTO> findUser(String userGuid) {
                    log.error("查询mdm员工信息失败:{}", userGuid, throwable);
                    throw new BusinessException("查询mdm员工信息失败");
                }

                @Override
                public MDMResult<OrganizationDTO> queryOrganization(String thirdNo) {
                    log.error("查询mdm组织信息失败:{}", thirdNo, throwable);
                    throw new BusinessException("查询mdm组织信息失败");
                }
            };
        }
    }
}
