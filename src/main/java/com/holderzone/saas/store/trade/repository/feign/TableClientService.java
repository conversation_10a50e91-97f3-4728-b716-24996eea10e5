package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.trade.OrderTableDTO;
import com.holderzone.saas.store.dto.trade.OrderTableVO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreClientService
 * @date 2018/09/30 11:41
 * @description //桌台调用
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-table", fallbackFactory = TableClientService.FallBack.class)
public interface TableClientService {

    /**
     * 修改订单关联的桌台
     */
    @ApiModelProperty("修改订单关联的桌台")
    @PostMapping("/table/update_table_order")
    OrderTableVO updateTableOrder(@RequestBody OrderTableDTO orderTableDTO);

    @PostMapping("/table/status/change_with_mq")
    boolean tableStatusChange(@RequestBody TableStatusChangeDTO tableStatusChangeDTO);

    @PostMapping("/table/recheck/open")
    TableWhetherOpenDTO tableWhetherOpen(@RequestBody BaseTableDTO baseTableDTO);

    @PostMapping("/table/getAreaGuid/{tableGuid}")
    String getAreaGuidByTableGuid(@PathVariable("tableGuid") String tableGuid);

    @PostMapping("/table/details/{tableGuid}")
    TableDTO getTableByGuid(@PathVariable("tableGuid") String tableGuid);

    @PostMapping("/table/pay_close")
    boolean payClose(@RequestBody CancelOrderReqDTO cancelOrderReqDTO);

    /**
     * 根据桌台查询桌位
     *
     * @param areaGuid 桌台guid
     * @return 区域信息
     */
    @ApiOperation(value = "根据桌台查询区域信息")
    @GetMapping("/area/query_area/{areaGuid}")
    AreaDTO queryAreaByTable(@PathVariable("areaGuid") String areaGuid);

    /**
     * 查询桌台信息
     *
     * @param tableGuid 桌台信息
     * @return 桌台信息
     */
    @ApiModelProperty("查询桌台信息")
    @GetMapping("/table/query_table_info")
    TableBasicDTO queryTableInfo(@RequestParam("tableGuid") String tableGuid);

    @ApiOperation("根据桌台Guid查询订单Guid")
    @PostMapping("/table/getOrderGuid/{tableGuid}")
    String getOrderGuidByTableGuid(@PathVariable("tableGuid") String tableGuid);

    @ApiOperation("查询桌台列表")
    @PostMapping("/table/web/query")
    List<TableBasicDTO> listByWeb(@RequestBody TableBasicQueryDTO tableBasicQueryDTO);

    @ApiModelProperty("根据主单guid查出所有并桌")
    @PostMapping("/table/query_combine_List_by_main_order")
    List<TableBasicDTO> queryCombineListByMainOrder(@RequestBody SingleDataDTO singleDataDTO);

    @Component
    class FallBack implements FallbackFactory<TableClientService> {

        private static final Logger logger = LoggerFactory.getLogger(TableClientService.FallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TableClientService create(Throwable throwable) {
            return new TableClientService() {

                @Override
                public OrderTableVO updateTableOrder(OrderTableDTO orderTableDTO) {
                    logger.error("updateTableOrder调用异常e={}", throwable.getMessage());
                    throw new ParameterException("updateTableOrder调用失败!");
                }

                @Override
                public boolean tableStatusChange(TableStatusChangeDTO tableStatusChangeDTO) {
                    logger.error("桌台信息变化调用异常e={}", throwable.getMessage());
                    throw new ParameterException("桌台信息变化调用失败!");
                }

                @Override
                public TableWhetherOpenDTO tableWhetherOpen(BaseTableDTO baseTableDTO) {
                    logger.error("获取桌台信息异常e={}", throwable.getMessage());
                    throw new ParameterException("获取桌台信息失败!");
                }

                @Override
                public String getAreaGuidByTableGuid(String tableGuid) {
                    logger.error("获取桌台信息异常e={}", throwable.getMessage());
                    throw new ParameterException("获取桌台信息失败!");
                }

                @Override
                public TableDTO getTableByGuid(String tableGuid) {
                    logger.error("获取桌台信息异常e={}", throwable.getMessage());
                    throw new ParameterException("获取桌台信息失败!");
                }

                @Override
                public boolean payClose(CancelOrderReqDTO cancelOrderReqDTO) {
                    logger.error("聚合支付关台异常e={}", throwable.getMessage());
                    throw new ParameterException("聚合支付关台失败!");
                }

                @Override
                public AreaDTO queryAreaByTable(String areaGuid) {
                    logger.error("根据桌台查询区域信息异常e={}", throwable.getMessage());
                    return null;
                }

                @Override
                public TableBasicDTO queryTableInfo(String tableGuid) {
                    logger.error("查询桌台信息异常e={}", throwable.getMessage());
                    return null;
                }

                @Override
                public String getOrderGuidByTableGuid(String tableGuid) {
                    logger.error(HYSTRIX_PATTERN, "getOrderGuidByTableGuid", tableGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<TableBasicDTO> listByWeb(TableBasicQueryDTO tableBasicQueryDTO) {
                    logger.error(HYSTRIX_PATTERN, "listByWeb", tableBasicQueryDTO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<TableBasicDTO> queryCombineListByMainOrder(SingleDataDTO singleDataDTO) {
                    logger.error(HYSTRIX_PATTERN, "queryCombineListByMainOrder", JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
