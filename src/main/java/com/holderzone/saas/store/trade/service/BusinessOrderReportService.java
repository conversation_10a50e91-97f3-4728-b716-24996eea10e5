package com.holderzone.saas.store.trade.service;


import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.journaling.req.*;
import com.holderzone.saas.store.dto.journaling.resp.*;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsCombinedRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsDetailRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsQueryDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsRespDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;

import java.util.List;

/**
 * <p>
 * 商户后台数据报表订单业务服务层
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
public interface BusinessOrderReportService {

    /**
     * 查询商户后台/数据报表/订单统计 分页数据
     *
     * @param queryDTO 查询条件入参
     * @return 分页数据
     */
    Page<BusinessOrderStatisticsRespDTO> getOrderStatisticsPage(BusinessOrderStatisticsQueryDTO queryDTO);

    /**
     * 查询商户后台/数据报表/订单统计 合计
     *
     * @param queryDTO 查询条件入参
     * @return 统计结果
     */
    BusinessOrderStatisticsCombinedRespDTO getOrderStatisticsCombined(BusinessOrderStatisticsQueryDTO queryDTO);

    StoreGatherTotalRespDTO listStoreGatherBusiness(StoreGatherReportReqDTO baseReq);

    BusinessDataRespDTO getBusinessData(JournalAppBaseReqDTO baseReq);

    ScreenBusinessDataRespDTO getScreenData(JournalAppBaseReqDTO baseReq);

    BusinessHisTrendRespDTO getBusinessHisTrend(JournalAppBaseReqDTO baseReq);

    /**
     * 根据订单GUID获取订单明细
     *
     * @param orderGuid 订单guid
     * @return 明细
     */
    BusinessOrderStatisticsDetailRespDTO getOrderStatisticsDetail(String orderGuid);

    /**
     * 根据订单GUID获取退款信息
     *
     * @param orderGuid 订单guid
     * @return 退款信息
     */
    BusinessOrderStatisticsDetailRespDTO getOrderStatisticsRefundInfo(String orderGuid);

    SaleRespDTO screenSale(SaleCountReqDTO saleCountReqDTO);

    StoreStatisticsAppRespDTO saleStoreStatistics(StoreStatisticsAppReqDTO storeStatisticsAppReqDTO);

    SaleStatisticsByHoursRespDTO saleByHoursStatistics(SaleStatisticsByHoursReqDTO statisticsByHoursReqDTO);

    /**
     * 订单统计查询收银员信息
     *
     * @param businessOrderStatisticsQueryDTO 筛选条件
     * @return 收银员信息列表
     */
    List<UserBriefDTO> getCheckoutStaffs(BusinessOrderStatisticsQueryDTO businessOrderStatisticsQueryDTO);
}
