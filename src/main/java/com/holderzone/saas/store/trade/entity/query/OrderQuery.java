package com.holderzone.saas.store.trade.entity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderQuery
 * @date 2018/07/27 上午 11:56
 * @description
 * @program holder-saas-store-trade
 */
@NoArgsConstructor
@Data
public class OrderQuery {

    @ApiModelProperty(value = "交易guid")
    private String orderGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "员工guid")
    private String operationStaffGuid;

    @ApiModelProperty(value = "员工姓名")
    private String operationStaffName;

    @ApiModelProperty(value = "操作时间")
    private Long operationTimestamp;

}
