package com.holderzone.saas.store.trade.entity.bo;

import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountRuleBO
 * @date 2019/02/23 16:03
 * @description
 * @program holder-saas-store-trade
 */
@Data
public class DiscountRuleBO {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "整单折扣")
    private BigDecimal wholeDiscount;

    @ApiModelProperty(value = "会员折扣")
    private BigDecimal memberDiscount;

    @ApiModelProperty(value = "整单让价")
    private BigDecimal concessional;

    @ApiModelProperty(value = "是否计算积分1：计算，2：不计算")
    private Integer memberIntegral;

    @ApiModelProperty(value = "是否可以用会员价计算")
    private boolean canCaculatByMemberPrice;

    @ApiModelProperty(value = "系统省零规则")
    private List<SystemDiscountDTO> systemDiscountDTOS;

    private Boolean fristMemberDiscountClient;

    @ApiModelProperty(value = "会员折扣guid")
    private String activityGuid;

    @ApiModelProperty(value = "券购买金额")
    private BigDecimal couponBuyTotalPrice;
}
