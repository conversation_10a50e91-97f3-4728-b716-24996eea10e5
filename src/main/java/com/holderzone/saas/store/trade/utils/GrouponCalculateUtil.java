package com.holderzone.saas.store.trade.utils;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountRuleDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.order.GrouponReceiptChannelEnum;
import com.holderzone.saas.store.enums.trade.CouponTypeEnum;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年07月14日 09:44
 * @description 团购验券计算工具类
 */
@Slf4j
public class GrouponCalculateUtil {

    private GrouponCalculateUtil() {
    }

    /**
     * 美团团购券
     * 团购验券优惠不能抵消附加费
     */
    public static void handleGroupon(DineinOrderDetailRespDTO orderDetailRespDTO, boolean isItem,
                                     Map<Integer, DiscountDO> discountTypeMap, List<DineInItemDTO> allItems,
                                     List<DiscountFeeDetailDTO> discountFeeDetailDTOS,
                                     GroupBuyTypeEnum groupBuyType, int discountType) {
        Map<Integer, DiscountFeeDetailDTO> feeDetailDTOMap = discountFeeDetailDTOS.stream()
                .collect(Collectors.toMap(DiscountFeeDetailDTO::getDiscountType, Function.identity(), (v1, v2) -> v1));
        DiscountFeeDetailDTO grouponDiscount = feeDetailDTOMap.get(discountType);
        boolean isNotExist = ObjectUtils.isEmpty(grouponDiscount);
        if (isNotExist) {
            DiscountDO grouponDiscountDO = discountTypeMap.get(discountType);
            grouponDiscount = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(grouponDiscountDO);
            grouponDiscount.setDiscountFee(BigDecimal.ZERO);
            grouponDiscount.setRule(JacksonUtils.writeValueAsString(new DiscountRuleDTO()));
            discountFeeDetailDTOS.add(grouponDiscount);
        }
        Pair<BigDecimal, BigDecimal> grouponFeePair;
        if (isItem) {
            grouponFeePair = calculateItemGrouponFee(orderDetailRespDTO, groupBuyType, allItems);
        } else {
            grouponFeePair = calculateNotItemGrouponFee(orderDetailRespDTO, groupBuyType, allItems);
        }
        log.warn("[{}]优惠金额：{}", groupBuyType.getDesc(), JacksonUtils.writeValueAsString(grouponFeePair));

        BigDecimal grouponFee = grouponFeePair.getKey();
        // 团购券可以无限验券，计算金额时最多抵扣shouldPay
        if (!BigDecimalUtil.greaterThanZero(grouponFee)) {
            return;
        }
        log.info("[{}]团购开始计算", groupBuyType.getDesc());
        BigDecimal actualGrouponFee;
        BigDecimal shouldPay = AmountCalculationUtil.getOrderFee(allItems, BigDecimal.ZERO);
        //商品总额+附加费
        log.info("商品总额:{},附加费:{}", shouldPay, orderDetailRespDTO.getAppendFee());
        if (Objects.nonNull(orderDetailRespDTO.getAppendFee())) {
            shouldPay = shouldPay.add(orderDetailRespDTO.getAppendFee());
        }
        if (BigDecimalUtil.greaterEqual(grouponFee, shouldPay)) {
            actualGrouponFee = shouldPay;
        } else {
            actualGrouponFee = AmountCalculationUtil.dealWithDiscountFee(orderDetailRespDTO.getOrderSurplusFee(), grouponFee);
        }
        // 如果团购非验券加购，则将团购的优惠金额分摊
        if (!isItem) {
            calculateItemGrouponDiscountTotalPrice(orderDetailRespDTO, grouponFeePair, allItems, actualGrouponFee);
        }
        grouponDiscount.setDiscountFee(grouponDiscount.getDiscountFee().add(actualGrouponFee));
        DiscountRuleDTO discountRuleDTO = JacksonUtils.toObject(DiscountRuleDTO.class, grouponDiscount.getRule());
        discountRuleDTO.setCouponBuyTotalPrice(Optional.ofNullable(discountRuleDTO.getCouponBuyTotalPrice()).orElse(BigDecimal.ZERO)
                .add(grouponFeePair.getRight()));
        grouponDiscount.setRule(JacksonUtils.writeValueAsString(discountRuleDTO));
        if (BigDecimalUtil.greaterThanZero(actualGrouponFee)) {
            orderDetailRespDTO.setOrderSurplusFee(orderDetailRespDTO.getOrderSurplusFee().subtract(actualGrouponFee));
            orderDetailRespDTO.setGrouponFee(orderDetailRespDTO.getGrouponFee().add(actualGrouponFee));
            log.warn("是否套餐券：{}，{}：{}，订单剩余金额：{}", isItem, groupBuyType.getDesc(), actualGrouponFee,
                    orderDetailRespDTO.getOrderSurplusFee());
        }
    }

    public static void calculateItemGrouponDiscountTotalPrice(DineinOrderDetailRespDTO orderDetailRespDTO,
                                                              Pair<BigDecimal, BigDecimal> grouponFeePair, List<DineInItemDTO> allItems,
                                                              BigDecimal actualGrouponFee) {
        // 优惠金额 = 抵扣金额 - 用户实付金额
        BigDecimal grouponDiscountFee = actualGrouponFee.subtract(grouponFeePair.getRight());
        log.info("团购计算优惠金额:{}", grouponDiscountFee);
        List<DineInItemDTO> calculateItemList = new ArrayList<>(allItems);
        if (BigDecimalUtil.greaterThanZero(orderDetailRespDTO.getAppendFee())) {
            DineInItemDTO appendFeeItem = new DineInItemDTO();
            appendFeeItem.setGuid("-1");
            appendFeeItem.setDiscountTotalPrice(orderDetailRespDTO.getAppendFee()
                    .subtract(Optional.ofNullable(orderDetailRespDTO.getAppendDiscountFee()).orElse(BigDecimal.ZERO)));
            calculateItemList.add(appendFeeItem);
        }
        Map<String, BigDecimal> itemDiscountPriceMap = PriceCalculationUtils.calculationItemDiscountPrice(grouponDiscountFee, calculateItemList);
        log.info("订单上团购优惠分摊:{}", JacksonUtils.writeValueAsString(itemDiscountPriceMap));
        Map<String, BigDecimal> itemCouponBuyPriceMap = PriceCalculationUtils.calculationItemDiscountPrice(grouponFeePair.getRight(), calculateItemList);
        log.info("订单上团购实际购买金额分摊:{}", JacksonUtils.writeValueAsString(itemCouponBuyPriceMap));
        Map<String, DineInItemDTO> itemDTOMap = allItems.stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        // 更新商品实付金额
        itemDiscountPriceMap.forEach((guid, itemDiscountFee) -> {
            DineInItemDTO dineInItemDTO = itemDTOMap.get(guid);
            if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(itemDiscountFee));
            }
        });
        itemCouponBuyPriceMap.forEach((guid, itemCouponBuyPrice) -> {
            DineInItemDTO dineInItemDTO = itemDTOMap.get(guid);
            if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                dineInItemDTO.setGrouponDiscountTotalPrice(itemCouponBuyPrice);
            }
        });
        // 附加费优惠金额
        orderDetailRespDTO.setAppendDiscountFee(Optional.ofNullable(orderDetailRespDTO.getAppendDiscountFee()).orElse(BigDecimal.ZERO)
                .add(itemDiscountPriceMap.getOrDefault("-1", BigDecimal.ZERO)));
        Map<String, BigDecimal> itemDiscountTotalPriceMap = allItems.stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, DineInItemDTO::getDiscountTotalPrice));
        log.info("订单上团购优惠分摊后商品:{}", JacksonUtils.writeValueAsString(itemDiscountTotalPriceMap));
    }

    /**
     * 套餐券优惠金额
     * left: 优惠金额
     * right: 用户实付金额
     */
    private static Pair<BigDecimal, BigDecimal> calculateItemGrouponFee(DineinOrderDetailRespDTO orderDetailRespDTO,
                                                                        GroupBuyTypeEnum groupBuyType, List<DineInItemDTO> allItems) {
        // 套餐匹配
        List<GrouponListRespDTO> grouponDTOList = orderDetailRespDTO.getGrouponListRespDTOS();
        Set<String> couponCodeSet = allItems.stream()
                .map(DineInItemDTO::getCouponCode)
                .filter(c -> !StringUtils.isEmpty(c))
                .collect(Collectors.toSet());
        BigDecimal discountFee = grouponDTOList.stream()
                .filter(g -> Objects.equals(CouponTypeEnum.ITEM.getCode(), g.getCouponType()))
                .filter(g -> Objects.equals(groupBuyType.getCode(), g.getGrouponType()))
                .filter(g -> !StringUtils.isEmpty(g.getActivityGuid()))
                .filter(g -> couponCodeSet.contains(g.getCode()))
                .map(GrouponListRespDTO::getDeductionAmount)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        if (!BigDecimalUtil.greaterThanZero(discountFee)) {
            return Pair.of(discountFee, null);
        }
        // 团购套餐优惠处理
        Map<String, BigDecimal> grouponDTOMap = grouponDTOList.stream()
                .filter(e -> !StringUtils.isEmpty(e.getCode()))
                .collect(Collectors.toMap(GrouponListRespDTO::getCode, GrouponListRespDTO::getCouponBuyPrice, (key1, key2) -> key1));
        BigDecimal couponBuyTotalPrice = BigDecimal.ZERO;
        for (DineInItemDTO dineInItemDTO : allItems) {
            BigDecimal couponBuyPrice = grouponDTOMap.get(dineInItemDTO.getCouponCode());
            if (Objects.nonNull(couponBuyPrice)) {
                dineInItemDTO.setTotalDiscountFee(dineInItemDTO.getItemPrice());
                // 优惠价：用户实付金额
                dineInItemDTO.setDiscountTotalPrice(couponBuyPrice);
                dineInItemDTO.setGrouponDiscountTotalPrice(couponBuyPrice);
                couponBuyTotalPrice = couponBuyTotalPrice.add(couponBuyPrice);
            }
        }
        return Pair.of(discountFee, couponBuyTotalPrice);
    }

    /**
     * 非套餐券优惠金额
     * left: 优惠金额
     * right: 用户实付金额
     */
    public static Pair<BigDecimal, BigDecimal> calculateNotItemGrouponFee(DineinOrderDetailRespDTO orderDetailRespDTO,
                                                                          GroupBuyTypeEnum groupBuyType, List<DineInItemDTO> allItems) {
        List<GrouponListRespDTO> grouponDTOList = orderDetailRespDTO.getGrouponListRespDTOS();
        // 老版本设备适配
        grouponDTOList.forEach(groupon -> {
            if (BigDecimalUtil.equelZero(groupon.getDeductionAmount())) {
                groupon.setDeductionAmount(groupon.getAmount());
            }
        });
        // 代金券
        List<GrouponListRespDTO> voucherCouponList = grouponDTOList.stream()
                .filter(g -> !Objects.equals(CouponTypeEnum.ITEM.getCode(), g.getCouponType())
                        && !Objects.equals(GrouponReceiptChannelEnum.MAITON.getCode(), g.getReceiptChannel()))
                .filter(g -> Objects.equals(groupBuyType.getCode(), g.getGrouponType()))
                .collect(Collectors.toList());
        // 代金券抵扣总金额
        BigDecimal grouponFee = voucherCouponList.stream()
                .map(GrouponListRespDTO::getDeductionAmount)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);

        // 已使用的验券加购的套餐券码
        Set<String> couponCodeSet = allItems.stream()
                .map(DineInItemDTO::getCouponCode)
                .filter(c -> !StringUtils.isEmpty(c))
                .collect(Collectors.toSet());
        // 是套餐券但是不是验券加购的
        List<GrouponListRespDTO> unUseItemCouponList = grouponDTOList.stream()
                .filter(g -> Objects.equals(CouponTypeEnum.ITEM.getCode(), g.getCouponType())
                        && !Objects.equals(GrouponReceiptChannelEnum.MAITON.getCode(), g.getReceiptChannel()))
                .filter(g -> Objects.equals(groupBuyType.getCode(), g.getGrouponType()))
                .filter(g -> !couponCodeSet.contains(g.getCode()))
                .collect(Collectors.toList());

        BigDecimal grouponItemFee = unUseItemCouponList.stream()
                .map(GrouponListRespDTO::getDeductionAmount)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        BigDecimal discountFee = grouponFee.add(grouponItemFee);
        if (!BigDecimalUtil.greaterThanZero(discountFee)) {
            return Pair.of(discountFee, null);
        }
        // 团购优惠按比例设置商品的折扣总价
        AmountCalculationUtil.dealWithAllItems(allItems, discountFee);

        // 用户实付金额
        BigDecimal voucherCouponBuyTotalPrice = voucherCouponList.stream()
                .map(GrouponListRespDTO::getCouponBuyPrice)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        BigDecimal couponBuyTotalPrice = unUseItemCouponList.stream()
                .map(GrouponListRespDTO::getCouponBuyPrice)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        return Pair.of(discountFee, voucherCouponBuyTotalPrice.add(couponBuyTotalPrice));
    }


    /**
     * 美团一键买单
     * left: 优惠金额
     * right: 用户实付金额
     */
    public static Pair<BigDecimal, BigDecimal> calculateMaitonGrouponFee(DineinOrderDetailRespDTO orderDetailRespDTO,
                                                                         List<DineInItemDTO> allItems) {
        List<GrouponListRespDTO> grouponDTOList = orderDetailRespDTO.getGrouponListRespDTOS();
        // 老版本设备适配
        grouponDTOList.forEach(groupon -> {
            if (BigDecimalUtil.equelZero(groupon.getDeductionAmount())) {
                groupon.setDeductionAmount(groupon.getAmount());
            }
        });
        // 美团一键买单
        List<GrouponListRespDTO> maitonCouponList = grouponDTOList.stream()
                .filter(g -> GrouponReceiptChannelEnum.MAITON.getCode() == g.getReceiptChannel())
                .collect(Collectors.toList());
        // 抵扣总金额
        BigDecimal discountFee = maitonCouponList.stream()
                .map(GrouponListRespDTO::getDeductionAmount)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);

        if (!BigDecimalUtil.greaterThanZero(discountFee)) {
            return Pair.of(discountFee, null);
        }
        // 团购优惠按比例设置商品的折扣总价
        AmountCalculationUtil.dealWithAllItems(allItems, discountFee);
        // 用户实付金额
        BigDecimal maitonCouponBuyTotalPrice = maitonCouponList.stream()
                .map(GrouponListRespDTO::getCouponBuyPrice)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        return Pair.of(discountFee, maitonCouponBuyTotalPrice);
    }


}
