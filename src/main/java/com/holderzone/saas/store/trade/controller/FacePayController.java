package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.face.FacePayCompensateReqDTO;
import com.holderzone.saas.store.dto.order.request.face.FacePayEstimateReqDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.trade.service.FacePayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FacePayController
 * @date 2019/01/04 8:53
 * @description 人脸支付接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/face_pay")
@Api(description = "人脸支付")
@Slf4j
public class FacePayController {

    private final FacePayService facePayService;

    @Autowired
    public FacePayController(FacePayService facePayService) {
        this.facePayService = facePayService;
    }

    @ApiOperation(value = "人脸支付反结账补偿接口", notes = "人脸支付反结账补偿接口")
    @PostMapping("/compensate")
    public Boolean recoveryCompensate(@RequestBody FacePayCompensateReqDTO facePayCompensateReqDTO) {
        log.info("支付入参：{}", JacksonUtils.writeValueAsString(facePayCompensateReqDTO));
        return facePayService.recoveryCompensate(facePayCompensateReqDTO);

    }

    @ApiOperation(value = "人脸校验估清", notes = "人脸校验估清")
    @PostMapping("/estimate")
    public EstimateItemRespDTO payCompensate(@RequestBody FacePayEstimateReqDTO facePayEstimateReqDTO) {
        log.info("人脸校验估清入参：{}", JacksonUtils.writeValueAsString(facePayEstimateReqDTO));
        return facePayService.estimate(facePayEstimateReqDTO);

    }

    @ApiOperation(value = "人脸支付退估清", notes = "人脸支付退估清")
    @PostMapping("/return_estimate")
    public Boolean returnEstimate(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("人脸支付退估清入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        return facePayService.returnEstimate(singleDataDTO);

    }

}
