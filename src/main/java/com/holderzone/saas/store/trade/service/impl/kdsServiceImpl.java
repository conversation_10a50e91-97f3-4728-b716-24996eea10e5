package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.config.resp.FinishFoodRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DistributeItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemTableDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.request.item.TransferItemDetailsReqDTO;
import com.holderzone.saas.store.dto.order.request.item.TransferItemReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.OrderItemDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.deal.FastFoodAutoDistributeTaskDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOrderItemReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxSendMessageReqDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.kds.ScanFinishFoodTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.trade.async.print.util.FacadePrintUtils;
import com.holderzone.saas.store.trade.config.ZhuanCanConfig;
import com.holderzone.saas.store.trade.entity.domain.OrderExtendsDO;
import com.holderzone.saas.store.trade.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.repository.feign.*;
import com.holderzone.saas.store.trade.repository.interfaces.OrderExtendsService;
import com.holderzone.saas.store.trade.service.KdsService;
import com.holderzone.saas.store.trade.service.OrderDetailService;
import com.holderzone.saas.store.trade.service.mq.KdsMqService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.HttpsClientUtils;
import com.holderzone.saas.store.trade.utils.ItemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className kdsServiceImpl
 * @date 2019/01/28 17:28
 * @description
 * @program holder-saas-store-trade
 */
@Slf4j
@Service
public class kdsServiceImpl implements KdsService {

    private final KdsMqService kdsMqService;

    private final TableClientService tableClientService;

    private final ItemClientService itemClientService;

    private final BusinessClientService businessClientService;

    private final MessageClientService messageClientService;

    private final KdsClientService kdsClientService;

    private final ZhuanCanConfig zhuanCanConfig;

    private final OrderDetailService orderDetailService;

    private final WxStoreClientService wxStoreClientService;

    private final OrderExtendsService orderExtendsService;

    private final OrderTransform orderTransform = OrderTransform.INSTANCE;

    @Autowired
    public kdsServiceImpl(KdsMqService kdsMqService,
                          TableClientService tableClientService,
                          ItemClientService itemClientService,
                          BusinessClientService businessClientService,
                          MessageClientService messageClientService,
                          KdsClientService kdsClientService,
                          ZhuanCanConfig zhuanCanConfig,
                          OrderDetailService orderDetailService,
                          WxStoreClientService wxStoreClientService,
                          OrderExtendsService orderExtendsService) {
        this.kdsMqService = kdsMqService;
        this.tableClientService = tableClientService;
        this.itemClientService = itemClientService;
        this.businessClientService = businessClientService;
        this.messageClientService = messageClientService;
        this.kdsClientService = kdsClientService;
        this.zhuanCanConfig = zhuanCanConfig;
        this.orderDetailService = orderDetailService;
        this.wxStoreClientService = wxStoreClientService;
        this.orderExtendsService = orderExtendsService;
    }

    @Override
    public void prepare(DineinOrderDetailRespDTO orderDetail, BaseDTO baseDTO) {
        ItemPrepareReqDTO itemPrepareReqDTO = getItemPrepareReqDTO(orderDetail, baseDTO);

        List<KdsItemDTO> kdsItemDTOS = new ArrayList<>();
        List<DineInItemDTO> kdsItems = orderDetail.getDineInItemDTOS().stream()
                .filter(DineInItemDTO::getIsKitchen)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(kdsItems)) {
            log.info("没有需要打厨的商品");
            return;
        }
        for (DineInItemDTO kdsItem : kdsItems) {
            SkuInfoRespDTO info = itemClientService.info(kdsItem.getSkuGuid());
            if (Objects.nonNull(info) && info.getIsJoinKds() == 0) {
                //sku商品未上架kds 跳过
                continue;
            }
            handleGroupItem(orderDetail, kdsItem, kdsItemDTOS);
        }
        itemPrepareReqDTO.setItems(kdsItemDTOS);
        kdsMqService.prepare(itemPrepareReqDTO);
    }

    private void handleGroupItem(DineinOrderDetailRespDTO orderDetail, DineInItemDTO kdsItem, List<KdsItemDTO> kdsItemDTOS) {
        if (ItemUtil.isGroup(kdsItem.getItemType())) {
            List<PackageSubgroupDTO> packageSubgroupDTOS = kdsItem.getPackageSubgroupDTOS();
            for (PackageSubgroupDTO packageSubgroupDTO : packageSubgroupDTOS) {
                List<SubDineInItemDTO> subDineInItemDTOS = packageSubgroupDTO.getSubDineInItemDTOS();
                for (SubDineInItemDTO subDineInItemDTO : subDineInItemDTOS) {
                    SkuInfoRespDTO subInfoRespDTO = itemClientService.info(subDineInItemDTO.getSkuGuid());
                    if (Objects.nonNull(subInfoRespDTO) && subInfoRespDTO.getIsJoinKds() == 0) {
                        //sku商品未上架kds 跳过
                        continue;
                    }
                    handleSubItem(orderDetail, kdsItem, subDineInItemDTO, kdsItemDTOS);
                }
            }
        } else {
            buildKdsItem(orderDetail, kdsItem, kdsItemDTOS);
        }
    }

    private void handleSubItem(DineinOrderDetailRespDTO orderDetail,
                               DineInItemDTO kdsItem,
                               SubDineInItemDTO subDineInItemDTO,
                               List<KdsItemDTO> kdsItemDTOS) {
        DineInItemDTO subItem = orderTransform.subDineInItemDTO2DineInItemDTO(subDineInItemDTO);
        subItem.setItemState(kdsItem.getItemState());
        subItem.setRemark(kdsItem.getRemark());
        BigDecimal parentCount = kdsItem.getCurrentCount().add(BigDecimalUtil.greaterThanZero
                (kdsItem.getFreeCount()) ? kdsItem.getFreeCount() : BigDecimal.ZERO);
        if (subItem.getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
            if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getPackageDefaultCount())) {
                int subCount = subItem.getCurrentCount().multiply(parentCount).intValue();
                for (int i = 0; i < subCount; i++) {
                    subItem.setCurrentCount(subDineInItemDTO.getPackageDefaultCount());
                    buildKdsItem(orderDetail, subItem, kdsItemDTOS);
                }
            }
        } else {
            subItem.setCurrentCount(subItem.getCurrentCount().multiply(parentCount).multiply
                    (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getPackageDefaultCount()) ?
                            subDineInItemDTO.getPackageDefaultCount() : BigDecimal.ONE));
            buildKdsItem(orderDetail, subItem, kdsItemDTOS);
        }
    }

    @NotNull
    private ItemPrepareReqDTO getItemPrepareReqDTO(DineinOrderDetailRespDTO orderDetail,
                                                   BaseDTO baseDTO) {
        ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setOrderGuid(orderDetail.getGuid());
        itemPrepareReqDTO.setDeviceId(baseDTO.getDeviceId());
        itemPrepareReqDTO.setStoreGuid(baseDTO.getStoreGuid());
        itemPrepareReqDTO.setOrderRemark(orderDetail.getRemark());
        itemPrepareReqDTO.setTradeMode(orderDetail.getTradeMode());
        if (orderDetail.getTradeMode().equals(TradeModeEnum.FAST.getCode())) {
            TradeSnackInfoDTO tradeSnackInfoDTO = new TradeSnackInfoDTO();
            //微信快餐订单打印桌台号
            if (StringUtils.isNotEmpty(orderDetail.getDiningTableName())) {
                tradeSnackInfoDTO.setMarkNo(orderDetail.getDiningTableName() + "/" + orderDetail.getMark());
            } else {
                tradeSnackInfoDTO.setMarkNo(orderDetail.getMark());
            }

            tradeSnackInfoDTO.setOrderNo(orderDetail.getOrderNo());
            itemPrepareReqDTO.setTradeSnackInfoDTO(tradeSnackInfoDTO);
        } else {
            TradeDineInInfoDTO tradeDineInInfoDTO = new TradeDineInInfoDTO();
            TableDTO tableByGuid = tableClientService.getTableByGuid(orderDetail.getDiningTableGuid());
            tradeDineInInfoDTO.setAreaGuid(tableByGuid.getAreaGuid());
            tradeDineInInfoDTO.setAreaName(tableByGuid.getAreaName());
            tradeDineInInfoDTO.setTableGuid(orderDetail.getDiningTableGuid());
            tradeDineInInfoDTO.setTableName(tableByGuid.getCode());
            tradeDineInInfoDTO.setOrderNo(orderDetail.getOrderNo());
            // 联台单
            OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderDetail.getGuid());
            if (Objects.nonNull(orderExtendsDO) && Boolean.TRUE.equals(orderExtendsDO.getAssociatedFlag())) {
                tradeDineInInfoDTO.setAssociatedFlag(orderExtendsDO.getAssociatedFlag());
                tradeDineInInfoDTO.setAssociatedSn(orderExtendsDO.getAssociatedSn());
                tradeDineInInfoDTO.setAssociatedTableGuids(Objects.nonNull(orderExtendsDO.getAssociatedTableGuids()) ?
                        JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableGuids()) : null
                );
                tradeDineInInfoDTO.setAssociatedTableNames(Objects.nonNull(orderExtendsDO.getAssociatedTableNames()) ?
                        JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableNames()) : null
                );
            }
            itemPrepareReqDTO.setTradeDineInInfoDTO(tradeDineInInfoDTO);
        }
        return itemPrepareReqDTO;
    }

    private void buildKdsItem(DineinOrderDetailRespDTO orderDetail, DineInItemDTO kdsItem, List<KdsItemDTO>
            kdsItemDTOS) {
        KdsItemDTO kdsItemDTO = FacadePrintUtils.buildKdsItemDTO(kdsItem, orderDetail.getRemark());
        kdsItemDTOS.add(kdsItemDTO);
    }

    @Override
    public void refound(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        List<ItemRefundReqDTO> itemRefundList = new ArrayList<>();
        List<DineInItemDTO> dineInItemDTOS = dineinOrderDetailRespDTO.getDineInItemDTOS();
        log.warn("kds退菜：itemRecordList：{}", JacksonUtils.writeValueAsString(dineInItemDTOS));
        if (CollectionUtil.isEmpty(dineInItemDTOS)) {
            return;
        }
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            if (CollectionUtil.isNotEmpty(dineInItemDTO.getPackageSubgroupDTOS())) {
                List<PackageSubgroupDTO> subItemRecords = dineInItemDTO.getPackageSubgroupDTOS();
                for (PackageSubgroupDTO subItemRecord : subItemRecords) {
                    for (SubDineInItemDTO subDineInItemDTO : subItemRecord.getSubDineInItemDTOS()) {
                        ItemRefundReqDTO itemRefundReqDTO = new ItemRefundReqDTO();
                        if (subDineInItemDTO.getOriginalOrderItemGuid() != null) {
                            itemRefundReqDTO.setOrderItemGuid(String.valueOf(subDineInItemDTO.getOriginalOrderItemGuid()));
                        } else {
                            itemRefundReqDTO.setOrderItemGuid(String.valueOf(subDineInItemDTO.getGuid()));
                        }
                        if (Integer.valueOf(3).equals(subDineInItemDTO.getItemType())) {
                            itemRefundReqDTO.setIsWeight(Boolean.TRUE);
                            BigDecimal number = subDineInItemDTO.getCurrentCount().multiply(dineInItemDTO.getCurrentCount());
                            itemRefundReqDTO.setNumber(number.intValue());
                        } else {
                            itemRefundReqDTO.setIsWeight(Boolean.FALSE);
                            BigDecimal number = subDineInItemDTO.getCurrentCount().multiply(dineInItemDTO.getCurrentCount());
                            if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getPackageDefaultCount())) {
                                number = number.multiply(subDineInItemDTO.getPackageDefaultCount());
                            }
                            itemRefundReqDTO.setNumber(number.intValue());
                        }
                        itemRefundList.add(itemRefundReqDTO);
                    }
                }
            } else {
                ItemRefundReqDTO itemRefundReqDTO = new ItemRefundReqDTO();
                if (dineInItemDTO.getOriginalOrderItemGuid() != null) {
                    itemRefundReqDTO.setOrderItemGuid(String.valueOf(dineInItemDTO.getOriginalOrderItemGuid()));
                } else {
                    itemRefundReqDTO.setOrderItemGuid(dineInItemDTO.getGuid());
                }
                if (Integer.valueOf(3).equals(dineInItemDTO.getItemType())) {
                    itemRefundReqDTO.setIsWeight(Boolean.TRUE);
                    itemRefundReqDTO.setNumber(1);
                } else {
                    itemRefundReqDTO.setIsWeight(Boolean.FALSE);
                    itemRefundReqDTO.setNumber(dineInItemDTO.getCurrentCount().intValue());
                }
                itemRefundList.add(itemRefundReqDTO);
                log.warn("kds退菜：非套餐：itemRefundReqDTO：{}", JacksonUtils.writeValueAsString(itemRefundReqDTO));
            }

        }
        ItemBatchRefundReqDTO itemBatchRefundReqDTO = new ItemBatchRefundReqDTO(itemRefundList);
        log.warn("kds退菜请求：itemBatchRefundReqDTO：{}", JacksonUtils.writeValueAsString(itemBatchRefundReqDTO));
        kdsMqService.refund(itemBatchRefundReqDTO);
    }

    @Override
    public void changes(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, Boolean cancelFlag,
                        List<SubDineInItemDTO> originalSubDineInItemList, List<SubDineInItemDTO> changeSubDineInItemList) {
        log.info("kds换菜请求：dineinOrderDetailRespDTO：{}, originalSubDineInItemList:{}, changeSubDineInItemList:{}",
                JacksonUtils.writeValueAsString(dineinOrderDetailRespDTO), JacksonUtils.writeValueAsString(originalSubDineInItemList),
                JacksonUtils.writeValueAsString(changeSubDineInItemList));
        kdsMqService.changes(buildItemChangesReqDTO(dineinOrderDetailRespDTO, cancelFlag, originalSubDineInItemList, changeSubDineInItemList));
    }


    /**
     * 构建kds换菜请求参数
     */
    private ItemChangesReqDTO buildItemChangesReqDTO(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, Boolean cancelFlag,
                                                     List<SubDineInItemDTO> originalSubDineInItemList, List<SubDineInItemDTO> changeSubDineInItemList) {
        ItemChangesReqDTO itemChangesReqDTO = new ItemChangesReqDTO();
        if (TradeModeEnum.DINEIN.getCode() == dineinOrderDetailRespDTO.getTradeMode()) {
            TableDTO table = tableClientService.getTableByGuid(dineinOrderDetailRespDTO.getDiningTableGuid());
            itemChangesReqDTO.setAreaGuid(table.getAreaGuid());
        }
        itemChangesReqDTO.setTradeMode(dineinOrderDetailRespDTO.getTradeMode());
        itemChangesReqDTO.setOrderGuid(dineinOrderDetailRespDTO.getGuid());
        itemChangesReqDTO.setOrderNo(dineinOrderDetailRespDTO.getOrderNo());
        itemChangesReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        itemChangesReqDTO.setDiningTableGuid(dineinOrderDetailRespDTO.getDiningTableGuid());
        itemChangesReqDTO.setDiningTableName(dineinOrderDetailRespDTO.getDiningTableName());
        itemChangesReqDTO.setGmtCreate(LocalDateTime.now());
        itemChangesReqDTO.setCreateStaffName(UserContextUtils.getUserName());
        // 原菜品
        List<KdsItemDTO> originalKdsItemList = buildKdsItemDTOList(cancelFlag, originalSubDineInItemList);
        // 更换后菜品
        List<KdsItemDTO> changesKdsItemList = buildKdsItemDTOList(cancelFlag, changeSubDineInItemList);
        itemChangesReqDTO.setOriginalKdsItemList(originalKdsItemList);
        itemChangesReqDTO.setChangesKdsItemList(changesKdsItemList);
        itemChangesReqDTO.setCancelFlag(cancelFlag);
        return itemChangesReqDTO;
    }

    private List<KdsItemDTO> buildKdsItemDTOList(Boolean cancelFlag, List<SubDineInItemDTO> subDineInItemList) {
        List<KdsItemDTO> kdsItemDTOList = Lists.newArrayList();
        for (SubDineInItemDTO subDineInItemDTO : subDineInItemList) {
            boolean hasOriginalItem = (Boolean.FALSE.equals(cancelFlag) && Objects.isNull(subDineInItemDTO.getOriginalItem()))
                    || (Boolean.TRUE.equals(cancelFlag) && Objects.nonNull(subDineInItemDTO.getOriginalItem()));
            if (hasOriginalItem && subDineInItemDTO.getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
                // 如果是称重商品，则根据packageDefaultCount分割
                int subCount = subDineInItemDTO.getCurrentCount().intValue();
                for (int i = 0; i < subCount; i++) {
                    DineInItemDTO dineInItemDTO = OrderTransform.INSTANCE.subDineInItemDTO2DineInItemDTO(subDineInItemDTO);
                    dineInItemDTO.setCurrentCount(subDineInItemDTO.getPackageDefaultCount());
                    KdsItemDTO kdsItemDTO = FacadePrintUtils.buildKdsItemDTO(dineInItemDTO, null);
                    kdsItemDTOList.add(kdsItemDTO);
                }
            } else {
                DineInItemDTO dineInItemDTO = OrderTransform.INSTANCE.subDineInItemDTO2DineInItemDTO(subDineInItemDTO);
                dineInItemDTO.setCurrentCount(dineInItemDTO.getCurrentCount().multiply(subDineInItemDTO.getPackageDefaultCount()));
                KdsItemDTO kdsItemDTO = FacadePrintUtils.buildKdsItemDTO(dineInItemDTO, null);
                kdsItemDTOList.add(kdsItemDTO);
            }
        }
        return kdsItemDTOList;
    }

    /**
     * 快餐自动出餐延迟队列
     */
    @Override
    public void dealFastFoodAutoDistributeDelayedTask(FastFoodAutoDistributeTaskDTO taskDTO) {
        if (ObjectUtils.isEmpty(taskDTO)) {
            log.warn("taskDTO为空");
            return;
        }
        UserContext useContext = UserContext.builder()
                .enterpriseGuid(taskDTO.getEnterpriseGuid())
                .storeGuid(taskDTO.getStoreGuid())
                .storeName(taskDTO.getStoreName())
                .build();
        UserContextUtils.put(useContext);
        EnterpriseIdentifier.setEnterpriseGuid(taskDTO.getEnterpriseGuid());

        // 查询未出堂商品
        PrdDstItemQueryDTO queryDTO = new PrdDstItemQueryDTO();
        queryDTO.setOrderGuid(taskDTO.getOrderGuid());
        List<PrdDstItemDTO> prdDstItemDTOList = kdsClientService.queryByOrder(queryDTO);
        if (!CollectionUtils.isEmpty(prdDstItemDTOList)) {
            batchDistribute(taskDTO, prdDstItemDTOList);

            // 查询出餐设置
            StoreConfigQueryDTO configQueryDTO = new StoreConfigQueryDTO();
            configQueryDTO.setStoreGuid(taskDTO.getStoreGuid());
            FinishFoodRespDTO finishFoodConfig = businessClientService.queryFinishFood(configQueryDTO);
            if (Objects.equals(BooleanEnum.TRUE.getCode(), finishFoodConfig.getFinishFoodVoiceSwitch())) {
                // 向一体机推送语音消息
                autoDistributeVoiceMsg(taskDTO.getStoreGuid(), taskDTO.getStoreName(), taskDTO.getDeviceId(), taskDTO.getOrderDesc());
            }

            // 消息通知
            sendCallMessage(taskDTO.getOrderGuid(), prdDstItemDTOList);
        }
    }

    @Override
    public void transferItem(TransferItemReqDTO transferReq,
                             DineinOrderDetailRespDTO orderDetailRespDTO,
                             HashMap<Long, OrderItemDTO> old2NewMap) {
        log.info("[kds转菜]请求");
        KdsItemTransferReqDTO transferReqDTO = new KdsItemTransferReqDTO();
        List<TransferItemDetailsReqDTO> itemDetailsDTOList = transferReq.getItemDetailsDTOList();
        itemDetailsDTOList.clear();
        old2NewMap.forEach((oldItemGuid, newItem) -> {
            TransferItemDetailsReqDTO reqDTO = new TransferItemDetailsReqDTO();
            reqDTO.setOrderItemGuid(String.valueOf(oldItemGuid));
            reqDTO.setTransferCount(newItem.getCurrentCount());
            reqDTO.setOrderItemType(newItem.getItemType());
            itemDetailsDTOList.add(reqDTO);
        });
        transferReqDTO.setTransferReq(transferReq);
        transferReqDTO.setOrderDetailRespDTO(orderDetailRespDTO);
        transferReqDTO.setOld2NewMap(old2NewMap);

        kdsMqService.transferItem(transferReqDTO);
        log.info("[kds转菜]完成");
    }

    /**
     * 批量出堂
     */
    private void batchDistribute(FastFoodAutoDistributeTaskDTO taskDTO, List<PrdDstItemDTO> prdDstItemDTOList) {
        // 根据商品sku查询对应出堂设备id
        SingleDataDTO queryDistribute = new SingleDataDTO();
        queryDistribute.setData(taskDTO.getStoreGuid());
        List<String> skuGuidList = prdDstItemDTOList.stream()
                .map(PrdDstItemDTO::getSkuGuid)
                .filter(org.springframework.util.StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());
        queryDistribute.setDatas(skuGuidList);
        List<DistributeItemDTO> itemDTOList = kdsClientService.queryDistributeItemBySku(queryDistribute);
        Map<String, List<DistributeItemDTO>> deviceIdItemDTO = itemDTOList.stream()
                .collect(Collectors.groupingBy(DistributeItemDTO::getDeviceId));
        // 调用出堂接口
        List<ItemStateTransReqDTO> reqDistributeList = new ArrayList<>();
        deviceIdItemDTO.forEach((deviceId, itemList) -> {
            ItemStateTransReqDTO reqDistributeDTO = new ItemStateTransReqDTO();
            List<String> dstSkuGuidList = itemList.stream()
                    .map(DistributeItemDTO::getSkuGuid)
                    .collect(Collectors.toList());
            List<PrdDstItemDTO> prdDstItemList = prdDstItemDTOList.stream()
                    .filter(prd -> dstSkuGuidList.contains(prd.getSkuGuid()))
                    .collect(Collectors.toList());
            reqDistributeDTO.setPrdDstItemList(prdDstItemList);
            reqDistributeDTO.setDeviceType(BaseDeviceTypeEnum.KDS.getCode());
            reqDistributeDTO.setStoreGuid(taskDTO.getStoreGuid());
            reqDistributeDTO.setStoreName(taskDTO.getStoreName());
            reqDistributeDTO.setEnterpriseGuid(taskDTO.getEnterpriseGuid());
            reqDistributeDTO.setDeviceId(deviceId);
            reqDistributeDTO.setOrderDifference(ScanFinishFoodTypeEnum.FAST.getCode());
            reqDistributeDTO.setOrderDesc(taskDTO.getOrderDesc());
            reqDistributeList.add(reqDistributeDTO);
        });
        // 调用出堂接口
        kdsClientService.batchDistribute(reqDistributeList);
    }

    /**
     * 发送消息
     */
    private void sendCallMessage(String orderGuid, List<PrdDstItemDTO> prdDstItemDTOList) {
        OrderDTO orderDTO = orderDetailService.findByOrderGuid(orderGuid);
        if (!ObjectUtils.isEmpty(orderDTO) && TradeModeEnum.FAST.getCode() == orderDTO.getTradeMode()) {
            WxStoreMerchantOrderDTO merchantOrderPhone = wxStoreClientService.getMerchantOrderPhone(orderGuid);
            log.info("[自动出餐]查询微信订单信息={},请求订单Guid为={}", JacksonUtils.writeValueAsString(merchantOrderPhone), orderGuid);
            if (ObjectUtil.isNotNull(merchantOrderPhone)) {
                // 赚餐发送消息
                zcSendCallMessage(merchantOrderPhone);

                // h5发送消息
                wxSendCallMessage(prdDstItemDTOList, Lists.newArrayList(merchantOrderPhone));
            }
        }
    }

    private void zcSendCallMessage(WxStoreMerchantOrderDTO merchantOrderPhone) {
        String orderRecordGuid = merchantOrderPhone.getOrderRecordGuid();
        // 赚餐推送小程序消息
        Map<String, Object> params = new HashMap<>();
        params.put("orderGuid", orderRecordGuid);
        String zcResponse = HttpsClientUtils.doGet(zhuanCanConfig.getSendCallMessage(), params);
        log.info("[自动出餐][赚餐推送小程序消息]返回={}", zcResponse);
    }

    /**
     * h5发送消息
     */
    private void wxSendCallMessage(List<PrdDstItemDTO> prdDstItemList,
                                   List<WxStoreMerchantOrderDTO> merchantOrderDTOList) {
        Map<String, List<WxOrderItemReqDTO>> wxOrderItemMap = buildWxOrderItemMap(prdDstItemList);
        WxSendMessageReqDTO wxSendMessageReqDTO = new WxSendMessageReqDTO();
        List<WxOrderReqDTO> orderList = new ArrayList<>();
        List<String> wxOrderGuidList = merchantOrderDTOList.stream()
                .map(WxStoreMerchantOrderDTO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        wxOrderGuidList.forEach(wxOrderGuid -> {
            List<WxOrderItemReqDTO> wxOrderItemList = wxOrderItemMap.get(wxOrderGuid);
            if (CollectionUtils.isEmpty(wxOrderItemList)) {
                wxOrderItemList = new ArrayList<>();
            }
            WxOrderReqDTO orderReqDTO = new WxOrderReqDTO();
            orderReqDTO.setOrderGuid(wxOrderGuid);
            orderReqDTO.setOrderItemList(wxOrderItemList);
            orderList.add(orderReqDTO);
        });
        wxSendMessageReqDTO.setOrderList(orderList);
        wxStoreClientService.sendCallMessage(wxSendMessageReqDTO);
    }

    private Map<String, List<WxOrderItemReqDTO>> buildWxOrderItemMap(List<PrdDstItemDTO> prdDstItemList) {
        Map<String, List<WxOrderItemReqDTO>> wxOrderItemMap = new HashMap<>();
        for (PrdDstItemDTO dstItem : prdDstItemList) {
            List<PrdDstItemTableDTO> kitchenItemList = dstItem.getKitchenItemList();
            if (!CollectionUtils.isEmpty(kitchenItemList)) {
                Map<String, List<PrdDstItemTableDTO>> orderItemMap = kitchenItemList.stream()
                        .collect(Collectors.groupingBy(PrdDstItemTableDTO::getOrderGuid));

                orderItemMap.forEach((orderGuid, dstItemList) -> {
                    List<WxOrderItemReqDTO> itemList = wxOrderItemMap.get(orderGuid);
                    WxOrderItemReqDTO wxOrderItemReqDTO = new WxOrderItemReqDTO();
                    wxOrderItemReqDTO.setItemGuid(dstItem.getItemGuid());
                    wxOrderItemReqDTO.setItemName(dstItem.getItemName());
                    wxOrderItemReqDTO.setSkuGuid(dstItem.getSkuGuid());
                    wxOrderItemReqDTO.setSkuName(dstItem.getSkuName());
                    wxOrderItemReqDTO.setNumber(dstItemList.size());

                    if (CollectionUtils.isEmpty(itemList)) {
                        List<WxOrderItemReqDTO> newSkuGuidSet = new ArrayList<>();
                        newSkuGuidSet.add(wxOrderItemReqDTO);
                        wxOrderItemMap.put(orderGuid, newSkuGuidSet);
                        return;
                    }
                    itemList.add(wxOrderItemReqDTO);
                });
            }
        }
        return wxOrderItemMap;
    }

    /**
     * 自动出餐发送出餐语音消息
     */
    private void autoDistributeVoiceMsg(String storeGuid, String storeName, String deviceId, String orderDesc) {
        BusinessMessageDTO build = BusinessMessageDTO.builder()
                .storeGuid(storeGuid)
                .storeName(storeName)
                .messageType(1)
                .detailMessageType(BusinessMsgTypeEnum.MEAL_VOICE_REMINDERS.getId())
                .platform("2")
                .subject("请" + orderDesc + "号顾客前来取餐")
                .content(deviceId)
                .build();
        log.info("[自动出餐语音消息],build={}", JacksonUtils.writeValueAsString(build));
        messageClientService.notifyMsg(build);
    }

}
