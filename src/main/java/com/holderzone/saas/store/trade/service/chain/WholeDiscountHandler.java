package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-26
 * @description
 */
@Component
@Slf4j
public class WholeDiscountHandler extends DiscountHandler {
    @Override
    void dealDiscount(DiscountContext context) {
        if (context.isRejectDiscount()) {
            return;
        }
        // 12.整单折扣
        DiscountFeeDetailDTO whole = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap()
                .get(type()));
        BigDecimal wholeDiscountFee = BigDecimal.ZERO;
        BigDecimal discountCalculateFee = BigDecimal.ZERO;
        if (context.getDiscountRuleBO().getWholeDiscount() != null) {
            log.info("[整单折扣]allItems={}", JacksonUtils.writeValueAsString(context.getAllItems()));
            BigDecimal realDiscount = BigDecimalUtil.getRealDiscount(context.getDiscountRuleBO().getWholeDiscount());
            log.info("[整单折扣]realDiscount={}", realDiscount);
            for (DineInItemDTO dineInItemDTO : context.getAllItems()) {
                //菜品是否能参加整单折扣
                discountCalculateFee = getBigDecimal(dineInItemDTO, discountCalculateFee);
            }
            log.info("[整单折扣]discountCalculateFee={}", discountCalculateFee);
            wholeDiscountFee = discountCalculateFee.multiply(realDiscount);
            wholeDiscountFee = BigDecimalUtil.setScale2(wholeDiscountFee);
            // 实付金额分摊
            Map<String, BigDecimal> itemDiscountPriceMap = PriceCalculationUtils.calculationItemDiscountPrice(wholeDiscountFee, realDiscount, context.getAllItems());
            log.info("[整单折扣]分摊优惠计算:{}", JacksonUtils.writeValueAsString(itemDiscountPriceMap));
            Map<String, DineInItemDTO> itemDTOMap = context.getAllItems().stream()
                    .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (v1, v2) -> v1));
            itemDiscountPriceMap.forEach((guid, itemDiscountFee) -> {
                DineInItemDTO dineInItemDTO = itemDTOMap.get(guid);
                if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                    dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(itemDiscountFee));
                }
            });
        }
        whole.setDiscountFee(wholeDiscountFee);
        context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(whole
                .getDiscountFee()));
        log.info("5.整单折扣计算后订单剩余金额：{}，优惠金额：{}", context.getOrderDetailRespDTO().getOrderSurplusFee(), whole.getDiscountFee());
        context.getDiscountFeeDetailDTOS().add(whole);
    }

    private static BigDecimal getBigDecimal(DineInItemDTO dineInItemDTO, BigDecimal discountCalculateFee) {
        if (dineInItemDTO.getIsWholeDiscount() == 1) {
            BigDecimal subtract = dineInItemDTO.getItemPrice()
                    .subtract(BigDecimalUtil.greaterThanZero(dineInItemDTO.getTotalDiscountFee()) ?
                            dineInItemDTO.getTotalDiscountFee() : BigDecimal.ZERO);
            discountCalculateFee = discountCalculateFee.add(BigDecimalUtil.greaterThanZero(subtract) ?
                    subtract : BigDecimal.ZERO);
        }
        return discountCalculateFee;
    }



    @Override
    Integer type() {
        return DiscountTypeEnum.WHOLE.getCode();
    }
}
