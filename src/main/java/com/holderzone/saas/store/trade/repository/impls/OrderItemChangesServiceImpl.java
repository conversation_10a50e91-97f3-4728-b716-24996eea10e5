package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.holderzone.saas.store.trade.entity.domain.OrderItemChangesDO;
import com.holderzone.saas.store.trade.mapper.OrderItemChangesMapper;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemChangesService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单商品换菜表 服务实现类
 */
@Service
public class OrderItemChangesServiceImpl extends ServiceImpl<OrderItemChangesMapper, OrderItemChangesDO> implements OrderItemChangesService {

    @Override
    public List<OrderItemChangesDO> listByOrderItemGuidList(Long orderGuid, List<Long> orderItemGuidList) {
        if (Objects.isNull(orderGuid) || CollectionUtils.isEmpty(orderItemGuidList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<OrderItemChangesDO> qw = new LambdaQueryWrapper<OrderItemChangesDO>()
                .eq(OrderItemChangesDO::getOrderGuid, orderGuid)
                .in(OrderItemChangesDO::getOrderItemGuid, orderItemGuidList);
        List<OrderItemChangesDO> orderItemChangesDOList = list(qw);
        if (CollectionUtils.isEmpty(orderItemChangesDOList)) {
            return Lists.newArrayList();
        }
        List<Long> originalOrderItemGuidList = orderItemChangesDOList.stream()
                .map(OrderItemChangesDO::getOriginalOrderItemGuid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<OrderItemChangesDO> originalOrderItemChangesDOList = listByIds(originalOrderItemGuidList);
        if (CollectionUtils.isNotEmpty(originalOrderItemChangesDOList)) {
            orderItemChangesDOList.addAll(originalOrderItemChangesDOList);
        }
        List<OrderItemChangesDO> orderItemChangesSortedDOList = orderItemChangesDOList.stream()
                .sorted(Comparator.comparing(OrderItemChangesDO::getGmtCreate).reversed())
                .collect(Collectors.toList());
        Map<Long, OrderItemChangesDO> orderItemChangesSortedDOMap = orderItemChangesSortedDOList.stream()
                .collect(Collectors.toMap(OrderItemChangesDO::getGuid, Function.identity(), (key1, key2) -> key1));
        return new ArrayList<>(orderItemChangesSortedDOMap.values());
    }

    @Override
    public List<OrderItemChangesDO> listByOrderGuid(Long orderGuid) {
        LambdaQueryWrapper<OrderItemChangesDO> qw = new LambdaQueryWrapper<OrderItemChangesDO>()
                .eq(OrderItemChangesDO::getOrderGuid, orderGuid);
        return list(qw);
    }

    @Override
    public List<OrderItemChangesDO> getChangeItemByBatchNumbers(Long orderGuid, List<String> changeBatchNumbers) {
        if (CollectionUtils.isEmpty(changeBatchNumbers)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<OrderItemChangesDO> qw = new LambdaQueryWrapper<OrderItemChangesDO>()
                .eq(OrderItemChangesDO::getOrderGuid, orderGuid)
                .in(OrderItemChangesDO::getChangeBatchNumber, changeBatchNumbers);
        return list(qw);
    }

    @Override
    public void removeByItemGuidList(List<Long> orderItemGuidList) {
        LambdaUpdateWrapper<OrderItemChangesDO> qw = new LambdaUpdateWrapper<OrderItemChangesDO>()
                .in(OrderItemChangesDO::getOrderItemGuid, orderItemGuidList);
        remove(qw);
    }

    @Override
    public void removeByOrderGuidList(List<Long> orderGuidList) {
        LambdaUpdateWrapper<OrderItemChangesDO> qw = new LambdaUpdateWrapper<OrderItemChangesDO>()
                .in(OrderItemChangesDO::getOrderGuid, orderGuidList);
        remove(qw);
    }

    @Override
    public List<OrderItemChangesDO> listByOrderItemGuidList(Set<String> oldOrderItemGuids) {
        if (CollectionUtils.isEmpty(oldOrderItemGuids)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<OrderItemChangesDO> qw = new LambdaQueryWrapper<OrderItemChangesDO>()
                .in(OrderItemChangesDO::getOrderItemGuid, oldOrderItemGuids);
        return list(qw);
    }
}
