package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.holder.saas.member.terminal.dto.statistics.RequestConsumptionStatistics;
import com.holderzone.holder.saas.member.terminal.dto.statistics.RequestDutyStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseConsumptionStatistics;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseDutyStatis;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;


@Component
@FeignClient(name = "holder-saas-member-terminal", fallbackFactory = MemberDataClientService.MemberDataClientServiceFallback.class)
public interface MemberDataClientService {

    @PostMapping("/hsmdc/duty/statistics")
    ResponseDutyStatis getStatisticsOfTotal(RequestDutyStatis dutyStatisReqDTO);

    @PostMapping("hsmdc/store/business/consumptionStatistics")
    ResponseConsumptionStatistics getStatisticsOfWay(RequestConsumptionStatistics consumptionStatisticsReqDTO);

    @Component
    class MemberDataClientServiceFallback implements FallbackFactory<MemberDataClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberDataClientService.MemberDataClientServiceFallback.class);

        @Override
        public MemberDataClientService create(Throwable throwable) {

            return new MemberDataClientService() {

                @Override
                public ResponseDutyStatis getStatisticsOfTotal(RequestDutyStatis dutyStatisReqDTO) {
                    logger.error("获取会员概况调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员概况调用异常");
                }

                @Override
                public ResponseConsumptionStatistics getStatisticsOfWay(RequestConsumptionStatistics consumptionStatisticsReqDTO) {
                    logger.error("获取会员消费支付方式调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员消费支付方式调用异常");
                }
            };
        }
    }
}