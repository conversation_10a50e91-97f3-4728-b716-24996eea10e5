package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.merchant.dto.member.RequestCardRechargePageQO;
import com.holderzone.holder.saas.member.merchant.dto.member.ResponseCardRechargeStatisticsVO;
import com.holderzone.saas.store.trade.entity.dto.MemberResult;
import com.holderzone.saas.store.trade.repository.feign.MemberMerchantClientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/recharge_order")
@Api(tags = "充值订单接口")
@AllArgsConstructor
public class RechargeOrderController {

    @Resource
    private MemberMerchantClientService memberMerchantClientService;

    /**
     * 一体机查询充值订单列表
     *
     * @param request request model
     * @return page ResponseMemberConsumption
     */
    @ApiOperation("一体机查询充值订单列表")
    @PostMapping("/getMemberCardRechargePage")
    ResponseCardRechargeStatisticsVO getMemberCardRechargePage(@RequestBody RequestCardRechargePageQO request) {
        MemberResult<ResponseCardRechargeStatisticsVO> rechargePage = memberMerchantClientService.getMemberCardRechargePage(request);
        log.info("一体机查询充值列表出参:{}", JacksonUtils.writeValueAsString(rechargePage));
        return rechargePage.getTData();
    }
}
