package com.holderzone.saas.store.trade.config;

import com.holderzone.framework.util.StringUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/9/25 11:45
 * @description 配置
 */
@Configuration
@RefreshScope
@Data
public class OverallConfig {
    @Value("${tongchidao.push_order_detail_url}")
    String pushOrderDetailUrl;
    @Value("${tongchidao.enterprise_guid_list:887f2181-eb06-4d77-b914-7c37c884952c}")
    private String enterpriseGuidList;

    /**
     * 获取对应的企业id
     * @return
     */
    public static List<String> getKeyWords(String enterpriseGuidList){
        if(StringUtils.isEmpty(enterpriseGuidList)){
            return Collections.emptyList();
        }
        String[] strArr = enterpriseGuidList.split(",");
        return Arrays.asList(strArr);
    }

    /**
     * 获取对应的企业id
     * @return map
     */
    public List<String> getEnterpriseGuidList(){
        return getKeyWords(enterpriseGuidList);
    }
}
