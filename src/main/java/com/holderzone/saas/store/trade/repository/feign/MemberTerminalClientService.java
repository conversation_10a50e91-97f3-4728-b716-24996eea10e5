package com.holderzone.saas.store.trade.repository.feign;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;

import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalDetailsDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.order.*;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoCard;
import com.holderzone.holder.saas.member.terminal.dto.volume.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员重构C端接口
 * <AUTHOR>
 * @since 2020-07-15
 */
@Component
@FeignClient(value = "holder-saas-member-terminal",
        fallbackFactory = MemberTerminalClientService.MemberTerminalFallback.class)
public interface MemberTerminalClientService {

    @PostMapping("/hsmca/order/discount")
    ResponseDiscount discount(@RequestBody RequestDiscount discountReqDTO);

    @GetMapping("/hsmca/order/integral/compute")
    ResponseIntegralOffset compute(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid,
                                   @RequestParam("orderMoney") BigDecimal orderMoney);

    @PostMapping("/hsmca/order/pay")
    String pay(@RequestBody RequestConfirmPay confirmPayReqDTO);

    @PostMapping("/hsmca/order/pay_info")
    ResponseConfirmPay payInfo(@RequestBody RequestConfirmPay confirmPayReqDTO);

    @PostMapping("/hsmca/order/savePay")
    String savePay(@RequestBody RequestConfirmPay confirmPayReqDTO);

    @PostMapping("/hsmca/order/cancel")
    String cancelPay(@RequestBody RequestCancelPay cancelPayReqDTO);

    @PostMapping("/hsmca/order/refund")
    String refundPay(@RequestBody RequestRefundPay refundPayReqDTO);

    @PostMapping("/hsmca/member/getMemberInfoAndCard")
    ResponseMemberAndCardInfoDTO getMemberInfoAndCard(@RequestBody RequestQueryStoreAndMemberAndCard
                                                             queryStoreAndMemberAndCardReqDTO);

    @PostMapping("/hsmca/member/queryBatchMemberCardInfo")
    List<ResponseMemberCard> queryBatchMemberCardInfo(@RequestBody RequestQueryStoreAndMemberAndCard
                                                              queryStoreAndMemberAndCardReqDTO);

    @GetMapping("/hsmca/card/hasMemberPrice")
    boolean hasMemberPrice(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid);

    @GetMapping("/hsmca/order/delDiscount")
    boolean delDiscount(@RequestParam("memberConsumptionGuid") String memberConsumptionGuid);

    //优惠券相关
    @PostMapping("/hsmca/volume/consume")
    ResponseVolumeConsume consume(@RequestBody RequestVolumeConsume volumeConsumeReqDTO);

    @PostMapping("/hsmca/volume/queryVolumePage")
    List<ResponseVolumeList> queryVolumePage(@RequestBody RequestVolumePageList reqDTO);

    @PostMapping("/hsmca/volume/{volumeCode}/calculate")
    ResponseVolumeCalculate calculate(@PathVariable("volumeCode") String volumeCode,
                                      @RequestBody RequestVolumeCalculate requestVolumeCalculate);

    @PostMapping("/hsmca/volume/{volumeCode}/cancel")
    List<RequestDishInfo> cancel(@PathVariable("volumeCode") String volumeCode,
                                 @RequestBody RequestVolumeCancel volumeCancelReqDTO);

    @PostMapping("/hsmca/volume/calculateAgain")
    ResponseVolumeCalculateAgain calculateAgain(@RequestBody RequestVolumeCalculateAgain volumeCalculateAgainDTO);

    @GetMapping("/hsmca/volume/getVolumeType")
    Integer getVolumeType(@RequestParam("volumeCode") String volumeCode,
                          @RequestParam("consumptionGuid") String consumptionGuid);

    @PostMapping("/hsmca/volume/cancelAll")
    boolean cancelAll(@RequestParam("memberConsumptionGuid") String memberConsumptionGuid);

    //营销活动相关
    @PostMapping("/hsmca/activity/select")
    ResponseActivityUse activitySelect(@RequestBody RequestActivityUse reqDTO);

    @PostMapping("/hsmca/activity/pay")
    String activityPay(@RequestBody RequestActivityPay activityPayReqDTO);

    @PostMapping("/hsmca/activity/cancelPay")
    String activityCancelPay(@RequestBody RequestCancelPay cancelPayReqDTO);

    @PostMapping("/hsmca/order/integral/computeIntegralStore")
    ResponseIntegralOffset computeIntegralStore(@RequestBody RequestIntegralStore requestIntegralStore);

    @PostMapping("/hsmca/order/deductionIntegral")
    String deductionIntegral(@RequestBody RequestDeductionIntegral requestDeductionIntegral);

    @ApiOperation("校验是否主卡")
    @GetMapping("/hsmca/card/checkCardInfoType")
    Boolean checkCardInfoType(@RequestParam(value = "memberInfoCardGuid") String memberInfoCardGuid);

    /**
     * 切换券关联关系
     * @param request request
     */
    @PostMapping("/hsmca/volume/updateVolumeRelevance")
    @ApiOperation(value = "切换券关联关系", response = Boolean.class)
    void updateVolumeRelevance(@RequestBody RequestUpdateVolumeRelevance request);

    /**
     * 查询资金明细记录
     */
    @GetMapping("/hsmca/order/query_funding_detail")
    List<ResponseConfirmMultiPay> queryFundingDetail(@RequestParam("memberConsumptionGuid") String memberConsumptionGuid);

    /**
     * 根据 会员信息guid 和 门店查询会员基本信息和 卡开通情况
     */
    @GetMapping(value = "/hsmca/member/getMemberInfoAndCardByMemberInfoGuid")
    ResponseMemberAndCardInfoDTO getMemberInfoAndCardByMemberInfoGuid(@RequestParam("memberInfoGuid") String memberInfoGuid);

    @ApiOperation("获取会员卡基本信息")
    @GetMapping("/hsmca/card/queryMemberCardInfo")
    ResponseMemberInfoCard queryMemberCardInfo(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid);

    @ApiOperation("查询会员画像信息")
    @GetMapping("/hsmca/member/query_member_portrayal")
    MemberPortrayalDetailsDTO queryMemberPortrayal(@RequestParam("memberGuid") String memberGuid);

    @Component
    @Slf4j
    class MemberTerminalFallback implements FallbackFactory<MemberTerminalClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MemberTerminalClientService create(Throwable throwable) {
            return new MemberTerminalClientService() {
                @Override
                public String deductionIntegral(RequestDeductionIntegral requestDeductionIntegral) {
                    log.error("会员折扣调用失败，args={}", JSON.toJSONString(requestDeductionIntegral));
                    throw new ParameterException("会员折扣调用失败");
                }

                @Override
                public Boolean checkCardInfoType(String memberInfoCardGuid) {
                    log.error("校验是否主卡调用失败，args={}", JSON.toJSONString(memberInfoCardGuid));
                    throw new ParameterException("校验是否主卡调用失败");
                }

                @Override
                public void updateVolumeRelevance(RequestUpdateVolumeRelevance request) {
                    log.error("关联优惠券失败，request={}", JSON.toJSONString(request));
                    throw new ParameterException("关联优惠券失败");
                }

                @Override
                public List<ResponseConfirmMultiPay> queryFundingDetail(String memberConsumptionGuid) {
                    log.error("查询资金明细记录失败，request={}", memberConsumptionGuid);
                    throw new ParameterException("查询资金明细记录失败");
                }

                @Override
                public ResponseMemberAndCardInfoDTO getMemberInfoAndCardByMemberInfoGuid(String memberInfoGuid) {
                    log.error(HYSTRIX_PATTERN, "getMemberInfoAndCardByMemberInfoGuid", memberInfoGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseMemberInfoCard queryMemberCardInfo(String memberInfoCardGuid) {
                    log.error(HYSTRIX_PATTERN, "queryMemberCardInfo", memberInfoCardGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseDiscount discount(RequestDiscount discountReqDTO) {
                    log.error("会员折扣调用失败，args={}", JSON.toJSONString(discountReqDTO));
                    throw new ParameterException("会员折扣调用失败");
                }

                @Override
                public ResponseIntegralOffset compute(String cardGuid, BigDecimal orderMoney) {
                    log.error("积分抵扣计算失败，args={},{}", cardGuid, orderMoney);
                    throw new ParameterException("积分抵扣计算失败");
                }

                @Override
                public ResponseIntegralOffset computeIntegralStore(RequestIntegralStore requestIntegralStore) {
                    log.error("积分抵扣计算失败，args={}", JSON.toJSONString(requestIntegralStore));
                    throw new ParameterException("积分抵扣计算失败");
                }

                @Override
                public String pay(RequestConfirmPay confirmPayReqDTO) {
                    log.error("会员支付失败，args={}", JSON.toJSONString(confirmPayReqDTO));
                    throw new ParameterException("会员支付失败");
                }

                @Override
                public ResponseConfirmPay payInfo(RequestConfirmPay confirmPayReqDTO) {
                    log.error("会员支付失败，args={}", JacksonUtils.writeValueAsString(confirmPayReqDTO));
                    throw new ParameterException("会员支付失败");
                }

                @Override
                public String savePay(RequestConfirmPay confirmPayReqDTO) {
                    log.error("会员离线支付记录写入失败，args={}", JSON.toJSONString(confirmPayReqDTO));
                    throw new ParameterException("会员离线支付写入失败");
                }

                @Override
                public String cancelPay(RequestCancelPay cancelPayReqDTO) {
                    log.error("会员撤销支付失败，args={}", JSON.toJSONString(cancelPayReqDTO));
                    throw new ParameterException("会员撤销支付失败");
                }

                @Override
                public String refundPay(RequestRefundPay refundPayReqDTO) {
                    log.error("会员部分退款失败，args={}", JSON.toJSONString(refundPayReqDTO));
                    throw new ParameterException("会员部分退款失败");
                }

                @Override
                public ResponseMemberAndCardInfoDTO getMemberInfoAndCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
                    log.error("获取会员卡信息失败，args={}", JSON.toJSONString(queryStoreAndMemberAndCardReqDTO));
                    throw new ParameterException("获取会员卡信息失败");
                }

                @Override
                public List<ResponseMemberCard> queryBatchMemberCardInfo(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
                    log.error("批量查询卡信息失败，args={}", JacksonUtils.writeValueAsString(queryStoreAndMemberAndCardReqDTO));
                    throw new ParameterException("批量查询卡信息失败");
                }

                @Override
                public boolean hasMemberPrice(String memberInfoCardGuid) {
                    log.error("查询会员卡是否享受会员价失败，args={}", memberInfoCardGuid);
                    throw new ParameterException("查询会员卡是否享受会员价失败");
                }

                @Override
                public boolean delDiscount(String memberConsumptionGuid) {
                    log.error("删除会员优惠失败，args={}", memberConsumptionGuid);
                    throw new ParameterException("删除会员优惠失败");
                }

                @Override
                public ResponseVolumeConsume consume(RequestVolumeConsume volumeConsumeReqDTO) {
                    log.error("优惠券核销失败，args={}", JSON.toJSONString(volumeConsumeReqDTO));
                    throw new ParameterException("优惠券核销失败");
                }

                @Override
                public List<ResponseVolumeList> queryVolumePage(RequestVolumePageList reqDTO) {
                    log.error("优惠券列表查询失败，args={}", JSON.toJSONString(reqDTO));
                    throw new ParameterException("优惠券列表查询失败");
                }

                @Override
                public ResponseVolumeCalculate calculate(String volumeCode, RequestVolumeCalculate requestVolumeCalculate) {
                    log.error("优惠券计算优惠失败，args={}", JSON.toJSONString(requestVolumeCalculate));
                    throw new ParameterException("优惠券计算优惠失败");
                }

                @Override
                public List<RequestDishInfo> cancel(String volumeCode, RequestVolumeCancel volumeCancelReqDTO) {
                    log.error("取消优惠券失败，args={}", JSON.toJSONString(volumeCancelReqDTO));
                    throw new ParameterException("取消优惠券失败");
                }

                @Override
                public ResponseVolumeCalculateAgain calculateAgain(RequestVolumeCalculateAgain volumeCalculateAgainDTO) {
                    log.error("重新验券失败，args={}", JSON.toJSONString(volumeCalculateAgainDTO));
                    throw new ParameterException("重新验券失败");
                }

                @Override
                public Integer getVolumeType(String volumeCode, String consumptionGuid) {
                    log.error("获取优惠券类型失败，volumeCode={}, consumptionGuid={}", volumeCode, consumptionGuid);
                    throw new ParameterException("获取优惠券类型失败");
                }

                @Override
                public boolean cancelAll(String memberConsumptionGuid) {
                    log.error("撤销所有优惠券失败，memberConsumptionGuid={}", memberConsumptionGuid);
                    throw new ParameterException("撤销所有优惠券失败");
                }

                @Override
                public ResponseActivityUse activitySelect(RequestActivityUse reqDTO) {
                    log.error("查询或选择营销活动失败，args={}", JSON.toJSONString(reqDTO));
                    throw new ParameterException("查询或选择营销活动失败");
                }

                @Override
                public String activityPay(RequestActivityPay activityPayReqDTO) {
                    log.error("营销活动支付失败，args={}", JSON.toJSONString(activityPayReqDTO));
                    throw new ParameterException("营销活动支付失败");
                }

                @Override
                public String activityCancelPay(RequestCancelPay cancelPayReqDTO) {
                    log.error("营销活动反结账失败，args={}", JSON.toJSONString(cancelPayReqDTO));
                    throw new ParameterException("营销活动反结账失败");
                }

                @Override
                public MemberPortrayalDetailsDTO queryMemberPortrayal(String memberGuid) {
                    log.error(HYSTRIX_PATTERN, "queryMemberPortrayal", memberGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
