package com.holderzone.saas.store.trade.service.impl;

import com.holderzone.holder.saas.member.terminal.dto.order.RequestCancelPay;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.face.FacePayCompensateReqDTO;
import com.holderzone.saas.store.dto.order.request.face.FacePayEstimateReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.TransactionRecordDO;
import com.holderzone.saas.store.trade.entity.enums.RecoveryTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeStateEnum;
import com.holderzone.saas.store.trade.repository.feign.ItemClientService;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.repository.interfaces.TransactionRecordService;
import com.holderzone.saas.store.trade.service.BillService;
import com.holderzone.saas.store.trade.service.DineInService;
import com.holderzone.saas.store.trade.service.FacePayService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillServiceImpl
 * @date 2019/01/28 17:28
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Slf4j
@Service
public class FacePayServiceImpl implements FacePayService {

    private final TransactionRecordService transactionRecordService;

    private final OrderService orderService;

    private final DineInService dineInService;

    private final BillService billService;

    private final ItemClientService itemClientService;

    private final MemberTerminalClientService memberTerminalClientService;

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    @Autowired
    public FacePayServiceImpl(TransactionRecordService transactionRecordService, OrderService orderService,
                              DineInService dineInService, BillService billService, ItemClientService itemClientService,
                              MemberTerminalClientService memberTerminalClientService) {
        this.transactionRecordService = transactionRecordService;
        this.orderService = orderService;
        this.dineInService = dineInService;
        this.billService = billService;
        this.itemClientService = itemClientService;
        this.memberTerminalClientService = memberTerminalClientService;
    }

    @Override
    public Boolean recoveryCompensate(FacePayCompensateReqDTO facePayCompensateReqDTO) {
        TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
        transactionRecordDO.setGuid(Long.valueOf(facePayCompensateReqDTO.getGuid()));
        if (facePayCompensateReqDTO.isSuccess()) {
            transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
        } else {
            //人脸支付反结账退款失败时退款方式修改为现金支付
            log.info("人脸支付反结账：支付记录id:", facePayCompensateReqDTO.getGuid());
            transactionRecordDO.setPaymentType(PaymentTypeEnum.CASH.getCode());
            transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.CASH.getDesc());
            transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
        }
        OrderDO orderDO = new OrderDO();
        orderDO.setGuid(Long.valueOf(facePayCompensateReqDTO.getOrderGuid()));
        orderDO.setRecoveryType(RecoveryTypeEnum.ORIGINAL.getCode());
        orderService.updateByIdWithDeleteCache(orderDO);
        transactionRecordService.updateById(transactionRecordDO);
        return Boolean.TRUE;
    }

    @Override
    public EstimateItemRespDTO estimate(FacePayEstimateReqDTO facePayEstimateReqDTO) {
        //快餐结账时校验估清
        EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(facePayEstimateReqDTO.getData());
        if (facePayEstimateReqDTO.getUseIntegral() != null && facePayEstimateReqDTO.getUseIntegral() > 0) {
            OrderDO orderDO = orderService.getByIdWithCache(facePayEstimateReqDTO.getData());
            BillPayReqDTO billPayReqDTO1 = orderTransform.facePayEstimateReqDTO2BillPayReqDTO(facePayEstimateReqDTO);
            String memberConsumptionGuid = billService.memberPay(orderDO, null, billPayReqDTO1);
            orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
            orderService.updateByIdWithDeleteCache(orderDO);
        }
        EstimateResultRespDTO estimateResult = itemClientService.estimate(orderDetail.getDineInItemDTOS());
        if (!estimateResult.getSuccess()) {
            estimateItemRespDTO.setResult(Boolean.FALSE);
            estimateItemRespDTO.setEstimate(Boolean.TRUE);
            estimateItemRespDTO.setEstimateInfo(estimateResult.getErrorMsg());
            estimateItemRespDTO.setEstimateSkuGuids(estimateResult.getSkuGuids());
            return estimateItemRespDTO;
        }
        estimateItemRespDTO.setResult(Boolean.TRUE);
        return estimateItemRespDTO;
    }

    @Override
    public Boolean returnEstimate(SingleDataDTO singleDataDTO) {
        //快餐结账时校验估清
        DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(singleDataDTO.getData());
        if (orderDetail.getMemberConsumptionGuid() != null && !"0".equals(orderDetail.getMemberConsumptionGuid())) {
            //会员退积分
            log.warn("人脸支付失败退会员积分，MemberConsumptionGuid：{}", orderDetail.getMemberConsumptionGuid());
            RequestCancelPay cancelPayReqDTO = new RequestCancelPay();
            cancelPayReqDTO.setIsSettlement(Boolean.FALSE);
            //Bugfix：21871,撤销的时候传递会员消费guid过去，这样重新发起的时候才能成功。这一行理论没报错，是代码走查的时候发现的。
            cancelPayReqDTO.setMemberConsumptionGuid(orderDetail.getMemberConsumptionGuid());
            memberTerminalClientService.cancelPay(cancelPayReqDTO);
        }
        if (!itemClientService.returnEstimate(orderDetail.getDineInItemDTOS())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
