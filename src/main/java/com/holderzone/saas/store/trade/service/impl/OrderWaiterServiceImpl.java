package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.order.request.waiter.*;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderWaiterDO;
import com.holderzone.saas.store.trade.entity.enums.WaiterTypeEnum;
import com.holderzone.saas.store.trade.entity.query.OrderWaiterQuery;
import com.holderzone.saas.store.trade.entity.query.OrderWaiterQueryDetails;
import com.holderzone.saas.store.trade.entity.read.OrderReadDO;
import com.holderzone.saas.store.trade.entity.read.OrderReadDetailsDO;
import com.holderzone.saas.store.trade.entity.read.OrderReadTotalDO;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.PageAdapter;
import com.holderzone.saas.store.trade.mapper.OrderMapper;
import com.holderzone.saas.store.trade.mapper.OrderWaiterMapper;
import com.holderzone.saas.store.trade.repository.feign.StaffClientService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.service.OrderWaiterService;
import com.holderzone.saas.store.trade.utils.EnumUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> R
 * @date 2020/11/18 18:03
 * @description
 */
@Service
@Slf4j
public class OrderWaiterServiceImpl extends ServiceImpl<OrderWaiterMapper, OrderWaiterDO> implements OrderWaiterService {
    @Resource
    private DynamicHelper dynamicHelper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderWaiterMapper orderWaiterMapper;
    @Resource
    private StaffClientService staffClientService;
    @Resource
    private OrderService orderService;

    @Override
    public Boolean addOrderWaiter(OrderWaiterReqDTO orderWaiterReqDTO) {
        log.info("录入服务员请求参数：{}", JacksonUtils.writeValueAsString(orderWaiterReqDTO));
        Assert.notNull(orderWaiterReqDTO, "请求参数不能为空！");
        Assert.notEmpty(orderWaiterReqDTO.getOrderGuid(), "订单GUID不能为空！");
        Assert.notEmpty(orderWaiterReqDTO.getOrderWaiterInfoDTOList(), "服务员不能为空！");
        List<String> typeEnumList = Lists.newArrayList();
        for (WaiterTypeEnum value : WaiterTypeEnum.values()) {
            if (value.getValue() < 1) {
                continue;
            }
            typeEnumList.add(value.getName());
        }
        int size = typeEnumList.size();
        List<OrderWaiterInfoDTO> orderWaiterInfoDTOList = orderWaiterReqDTO.getOrderWaiterInfoDTOList();
        String orderGuid = orderWaiterReqDTO.getOrderGuid();
        if (orderWaiterInfoDTOList.size() < size) {
            throw new BusinessException("录入服务员信息不足【" + size + "】位！");
        }
        //每次新增 清除之前的记录
        remove(new LambdaQueryWrapper<OrderWaiterDO>().eq(OrderWaiterDO::getOrderGuid, orderWaiterReqDTO.getOrderGuid()));
        List<OrderWaiterDO> orderWaiterDOList = Lists.newLinkedList();
        for (OrderWaiterInfoDTO orderWaiterInfoDTO : orderWaiterInfoDTOList) {
            OrderWaiterDO orderWaiterDO = new OrderWaiterDO();
            orderWaiterDO.setGmtCreate(LocalDateTime.now());
            orderWaiterDO.setGmtModified(LocalDateTime.now());
            orderWaiterDO.setOrderGuid(orderGuid);
            orderWaiterDO.setWaiterGuid(orderWaiterInfoDTO.getWaiterGuid());
            orderWaiterDO.setWaiterType(orderWaiterInfoDTO.getWaiterType());
            orderWaiterDO.setWaiterName(orderWaiterInfoDTO.getWaiterName());
            orderWaiterDO.setWaiterNo(orderWaiterInfoDTO.getWaiterNo());
            orderWaiterDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER_WAITER));
            orderWaiterDOList.add(orderWaiterDO);
        }
        log.info("录入服务员最终参数:{}", JacksonUtils.writeValueAsString(orderWaiterDOList));
        saveBatch(orderWaiterDOList);
        //更新订单录入操作员状态
        orderService.updateIsWaiters(Lists.newArrayList(orderGuid));
        return Boolean.TRUE;
    }

    @Override
    public List<OrderWaiterInfoDTO> getOrderWaiter(String orderGuid) {
        log.info("订单查询服务员请求参数：{}", orderGuid);
        Assert.notEmpty(orderGuid, "订单GUID不能为空！");
        List<OrderWaiterDO> orderWaiterDOList = list(new LambdaQueryWrapper<OrderWaiterDO>().eq(OrderWaiterDO::getOrderGuid, orderGuid));
        if (CollectionUtils.isEmpty(orderWaiterDOList)) {
            log.info("查询出的服务员数据为空！");
            return Lists.newArrayList();
        }
        log.info("查询出的订单服务员数据:{}", JacksonUtils.writeValueAsString(orderWaiterDOList));
        List<OrderWaiterInfoDTO> waiterInfoDTOList = Lists.newLinkedList();
        for (OrderWaiterDO orderWaiterDO : orderWaiterDOList) {
            OrderWaiterInfoDTO orderWaiterInfoDTO = new OrderWaiterInfoDTO();
            orderWaiterInfoDTO.setWaiterType(orderWaiterDO.getWaiterType());
            orderWaiterInfoDTO.setWaiterGuid(orderWaiterDO.getWaiterGuid());
            orderWaiterInfoDTO.setWaiterName(orderWaiterDO.getWaiterName());
            orderWaiterInfoDTO.setWaiterNo(orderWaiterDO.getWaiterNo());
            waiterInfoDTOList.add(orderWaiterInfoDTO);
        }
        log.info("组装完成的订单服务员数据:{}", JacksonUtils.writeValueAsString(waiterInfoDTOList));
        return waiterInfoDTOList;
    }

    @Override
    public Boolean checkInputWaiter(String orderGuid) {
        log.info("订单查询服务员请求参数：{}", orderGuid);
        if (StringUtils.isBlank(orderGuid)) {
            return Boolean.FALSE;
        }
        List<OrderWaiterDO> orderWaiterDOList = list(new LambdaQueryWrapper<OrderWaiterDO>().eq(OrderWaiterDO::getOrderGuid, orderGuid));
        if (CollectionUtils.isEmpty(orderWaiterDOList)) {
            log.info("查询出的服务员数据为空！");
            return Boolean.FALSE;
        }
        log.info("校验录入订单服务员查询出的订单服务员数据:{}", JacksonUtils.writeValueAsString(orderWaiterDOList));
        int count = 0;
        Map<Object, String> waiterTypeEnumMap = EnumUtils.EnumToMap(WaiterTypeEnum.class);
        for (OrderWaiterDO orderWaiterDO : orderWaiterDOList) {
            if (Objects.nonNull(waiterTypeEnumMap.get(orderWaiterDO.getWaiterType()))) {
                count += 1;
            }
        }
        if (waiterTypeEnumMap.size() == count) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Page<OrderWaiterPageRespDTO> pageOrderWaiter(OrderWaiterPageReqDTO orderWaiterPageReqDTO) {
        log.info("分页查询订单服务员入参：{}", JacksonUtils.writeValueAsString(orderWaiterPageReqDTO));
        Assert.notNull(orderWaiterPageReqDTO, "请求参数不能为空！");
        OrderWaiterQuery orderWaiterQuery = new OrderWaiterQuery();
        orderWaiterQuery.setEndDateTime(orderWaiterPageReqDTO.getEndDateTime());
        orderWaiterQuery.setStartDateTime(orderWaiterPageReqDTO.getStartDateTime());
        orderWaiterQuery.setStoreGuid(UserContextUtils.getStoreGuid());
        IPage<OrderReadDO> page = null;
        if (orderWaiterPageReqDTO.getInputStatus()) {
            page = orderMapper.pageQueryOrderWaiter(new PageAdapter<>(orderWaiterPageReqDTO), orderWaiterQuery);
        } else {
            page = orderMapper.pageQueryOrder(new PageAdapter<>(orderWaiterPageReqDTO), orderWaiterQuery);
        }
        return transformDTO(page);
    }

    @Override
    public Page<OrderWaiterPageDetailsRespDTO> pageOrderWaiterDetails(OrderWaiterPageDetailsReqDTO orderWaiterPageDetailsReqDTO) {
        log.info("分页查询订单服务员明细入参：{}", JacksonUtils.writeValueAsString(orderWaiterPageDetailsReqDTO));
        Assert.notNull(orderWaiterPageDetailsReqDTO, "请求参数不能为空！");
        OrderWaiterQueryDetails queryDetails = new OrderWaiterQueryDetails();
        BeanUtil.copyProperties(orderWaiterPageDetailsReqDTO, queryDetails);
        UserContext userContext = UserContextUtils.get();
        List<String> checkStoreGuid = checkStoreGuid(userContext.getUserGuid(), orderWaiterPageDetailsReqDTO.getStoreGuidList());
        log.info("pageOrderWaiterDetails 门店Guid:{}", JacksonUtils.writeValueAsString(checkStoreGuid));
        queryDetails.setStoreGuidList(checkStoreGuid);
        IPage<OrderReadDetailsDO> orderReadDOIPage = orderMapper.pageQueryOrderWaiterDetails(new PageAdapter<>(orderWaiterPageDetailsReqDTO), queryDetails);
        return transformDetailsDTO(orderReadDOIPage);
    }

    @Override
    public Boolean updateOrderWaiterFee(OrderDTO orderDTO) {
        log.info("订单服务信息更新请求参数：{}", JacksonUtils.writeValueAsString(orderDTO));
        if (Objects.isNull(orderDTO)) {
            return Boolean.FALSE;
        }
        int i = orderWaiterMapper.updateOrderFeeByOrderGuid(orderDTO.getGuid(), orderDTO.getOrderFee(), orderDTO.getStoreGuid(), orderDTO.getStoreName());
        return i > 0;
    }

    @Override
    public Page<OrderWaiterPageTotalRespDTO> pageOrderWaiterTotal(OrderWaiterPageDetailsReqDTO orderWaiterPageDetailsReqDTO) {
        log.info("分页查询订单服务员汇总入参：{}", JacksonUtils.writeValueAsString(orderWaiterPageDetailsReqDTO));
        Assert.notNull(orderWaiterPageDetailsReqDTO, "请求参数不能为空！");
        OrderWaiterQueryDetails queryDetails = new OrderWaiterQueryDetails();
        BeanUtil.copyProperties(orderWaiterPageDetailsReqDTO, queryDetails);
        UserContext userContext = UserContextUtils.get();
        List<String> checkStoreGuid = checkStoreGuid(userContext.getUserGuid(), orderWaiterPageDetailsReqDTO.getStoreGuidList());
        log.info("pageOrderWaiterDetails 门店Guid:{}", JacksonUtils.writeValueAsString(checkStoreGuid));
        queryDetails.setStoreGuidList(checkStoreGuid);
        IPage<OrderReadTotalDO> pageQueryOrderWaiterTotal = orderWaiterMapper.pageQueryOrderWaiterTotal(new PageAdapter<>(orderWaiterPageDetailsReqDTO), queryDetails);
        return transformTotalDTO(pageQueryOrderWaiterTotal);
    }

    @Override
    public Page<OrderWaiterPageMakeUpRespDTO> pageOrderWaiterMakeUp(OrderWaiterMakeUpPageReqDTO reqDTO) {
        log.info("pageOrderWaiterMakeUp 分页查询订单补录入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        Assert.notNull(reqDTO, "请求参数不能为空！");
        OrderWaiterQueryDetails queryDetails = new OrderWaiterQueryDetails();
        BeanUtil.copyProperties(reqDTO, queryDetails);
        queryDetails.setFrontOrderOperator(reqDTO.getFrontOrderOperator());
        queryDetails.setRearOrderOperator(reqDTO.getRearOrderOperator());
        log.info("分页查询入参：{}",JacksonUtils.writeValueAsString(queryDetails));
        IPage<OrderReadDO> orderReadDOIPage = orderMapper.pageOrderWaiterMakeUp(new PageAdapter<>(reqDTO), queryDetails);
        return transformMakeUp(orderReadDOIPage);
    }

    @Override
    public Boolean orderWaiterMakeUp(OrderWaiterMakeUpReqDTO makeUpReqDTO) {
        log.info("orderWaiterMakeUp 订单补录入参：{}", JacksonUtils.writeValueAsString(makeUpReqDTO));
        Assert.notNull(makeUpReqDTO, "请求参数不能为空！");
        Assert.notNull(makeUpReqDTO.getOrderWaiterInfoDTOList(), "请求参数不能为空！");
        List<OrderWaiterInfoDTO> orderWaiterInfoDTOList = makeUpReqDTO.getOrderWaiterInfoDTOList();
        List<Integer> waiterTypeList = orderWaiterInfoDTOList.stream().map(OrderWaiterInfoDTO::getWaiterType).collect(Collectors.toList());
        List<String> orderL = makeUpReqDTO.getOrderGuidList();
        List<OrderDO> orderDOList = orderService.getListByIds(orderL);
        if (CollectionUtils.isEmpty(orderDOList)) {
            return false;
        }
        Map<Long, OrderDO> orderMap = orderDOList.stream().collect(Collectors.toMap(OrderDO::getGuid, e -> e));
        // 删除指定类型的订单服务员信息
        List<OrderWaiterDO> oldList = list(new LambdaQueryWrapper<OrderWaiterDO>().in(OrderWaiterDO::getOrderGuid, orderL).in(OrderWaiterDO::getWaiterType, waiterTypeList));
        List<OrderWaiterDO> orderWaiterDOList = Lists.newLinkedList();
        Set<String> oldOrderList = new HashSet<>();
        Map<Integer, OrderWaiterInfoDTO> infoDTOMap = makeUpReqDTO.getOrderWaiterInfoDTOList().stream().collect(Collectors.toMap(OrderWaiterInfoDTO::getWaiterType, e -> e));
        //排除掉重复的订单guid
        Set<String> orderGuidList = new HashSet<>();
        //最终需要落库的订单服务员
        Set<String> finalOrderGuidList = new HashSet<>();
        Map<String, List<OrderWaiterDO>> orderWaiterMap = oldList.stream().collect(Collectors.groupingBy(OrderWaiterDO::getOrderGuid));
        if (CollectionUtils.isNotEmpty(oldList)) {
            for (OrderWaiterDO orderWaiterDO : oldList) {
                OrderWaiterInfoDTO orderWaiterInfoDTO = infoDTOMap.get(orderWaiterDO.getWaiterType());
                if (Objects.nonNull(orderWaiterInfoDTO)) {
                    orderWaiterDO.setWaiterGuid(orderWaiterInfoDTO.getWaiterGuid());
                    orderWaiterDO.setWaiterType(orderWaiterInfoDTO.getWaiterType());
                    orderWaiterDO.setWaiterName(orderWaiterInfoDTO.getWaiterName());
                    orderWaiterDO.setWaiterNo(orderWaiterInfoDTO.getWaiterNo());
                    orderWaiterDO.setGmtModified(LocalDateTime.now());
                }
                orderWaiterDOList.add(orderWaiterDO);
                oldOrderList.add(orderWaiterDO.getOrderGuid() + orderWaiterDO.getWaiterType());
//                orderWaiterInfoDTOList.remove(orderWaiterInfoDTO);
            }
            //一个订单只会有一个订单类型的服务员 做去重
            for (Integer integer : infoDTOMap.keySet()) {
                for (String s : orderL) {
                    orderGuidList.add(s + integer);
                }
            }
            orderGuidList.removeAll(oldOrderList);
            for (String orderGuid : orderGuidList) {
                finalOrderGuidList.add(orderGuid.substring(0, orderGuid.length() - 1));
            }
        } else {
            //订单未录入过服务员
            finalOrderGuidList.addAll(orderL);
        }
        log.info("最终处理:{}", JacksonUtils.writeValueAsString(finalOrderGuidList));
        for (String orderGuid : finalOrderGuidList) {
            OrderDO orderDO = orderMap.get(Long.parseLong(orderGuid));
            List<OrderWaiterDO> orderWaiterDOS = orderWaiterMap.get(orderGuid);
            Map<Integer, OrderWaiterDO> collect = new HashMap<>();
            if (CollectionUtils.isNotEmpty(orderWaiterDOS)) {
                collect = orderWaiterDOS.stream().collect(Collectors.toMap(OrderWaiterDO::getWaiterType, e -> e));
            }
            for (OrderWaiterInfoDTO orderWaiterInfoDTO : orderWaiterInfoDTOList) {
                if (Objects.isNull(collect.get(orderWaiterInfoDTO.getWaiterType()))) {
                    OrderWaiterDO orderWaiterDO = new OrderWaiterDO();
                    orderWaiterDO.setGmtCreate(LocalDateTime.now());
                    orderWaiterDO.setGmtCreate(LocalDateTime.now());
                    orderWaiterDO.setOrderGuid(orderGuid);
                    orderWaiterDO.setStoreGuid(orderDO.getStoreGuid());
                    orderWaiterDO.setStoreName(orderDO.getStoreName());
                    orderWaiterDO.setOrderFee(orderDO.getOrderFee());
                    orderWaiterDO.setWaiterGuid(orderWaiterInfoDTO.getWaiterGuid());
                    orderWaiterDO.setWaiterType(orderWaiterInfoDTO.getWaiterType());
                    orderWaiterDO.setWaiterName(orderWaiterInfoDTO.getWaiterName());
                    orderWaiterDO.setWaiterNo(orderWaiterInfoDTO.getWaiterNo());
                    orderWaiterDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER_WAITER));
                    orderWaiterDOList.add(orderWaiterDO);
                }
            }
        }
        log.info("orderWaiterMakeUp 补录入服务员最终参数:{}", JacksonUtils.writeValueAsString(orderWaiterDOList));
        saveOrUpdateBatch(orderWaiterDOList);
        //更新订单录入操作员状态
        orderService.updateIsWaiters(orderL);
        return Boolean.TRUE;
    }

    private Page<OrderWaiterPageRespDTO> transformDTO(IPage<OrderReadDO> page) {
        List<OrderReadDO> records = page.getRecords();
        List<OrderWaiterPageRespDTO> respDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(records)) {
            log.info("分页数据为空！");
            return new PageAdapter<>(page, respDTOList);
        }
        for (OrderReadDO record : records) {
            OrderWaiterPageRespDTO orderWaiterInfoDTO = new OrderWaiterPageRespDTO();
            orderWaiterInfoDTO.setOrderGuid(record.getGuid().toString());
            orderWaiterInfoDTO.setDiningTableName(record.getDiningTableName());
            orderWaiterInfoDTO.setGmtCreate(record.getGmtCreate());
            if (CollectionUtils.isNotEmpty(record.getOrderWaiterDOList())) {
                orderWaiterInfoDTO.setIfInputWaiter(true);
            } else {
                orderWaiterInfoDTO.setIfInputWaiter(false);
            }
            if (1 == record.getTradeMode()) {
                //如果为快餐 桌台名称标识为 快餐
                orderWaiterInfoDTO.setDiningTableName("快餐");
            }
            respDTOList.add(orderWaiterInfoDTO);
        }
        log.info("组装好的的分页参数:{}", JacksonUtils.writeValueAsString(respDTOList));
        return new PageAdapter<>(page, respDTOList);
    }

    private Page<OrderWaiterPageDetailsRespDTO> transformDetailsDTO(IPage<OrderReadDetailsDO> page) {
        List<OrderReadDetailsDO> records = page.getRecords();
        List<OrderWaiterPageDetailsRespDTO> respDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(records)) {
            log.info("分页数据为空！");
            return new PageAdapter<>(page, respDTOList);
        }
        for (OrderReadDetailsDO record : records) {
            OrderWaiterPageDetailsRespDTO orderWaiterInfoDTO = new OrderWaiterPageDetailsRespDTO();
            BeanUtil.copyProperties(record, orderWaiterInfoDTO);
            orderWaiterInfoDTO.setOrderGuid(record.getGuid().toString());
            if (1 == record.getTradeMode()) {
                //如果为快餐 桌台名称标识为 快餐
                orderWaiterInfoDTO.setDiningTableName("快餐");
            }
            respDTOList.add(orderWaiterInfoDTO);
        }
        log.info("组装好的的分页参数:{}", JacksonUtils.writeValueAsString(respDTOList));
        return new PageAdapter<>(page, respDTOList);
    }

    private Page<OrderWaiterPageTotalRespDTO> transformTotalDTO(IPage<OrderReadTotalDO> page) {
        List<OrderReadTotalDO> records = page.getRecords();
        List<OrderWaiterPageTotalRespDTO> respDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(records)) {
            log.info("分页数据为空！");
            return new PageAdapter<>(page, respDTOList);
        }
        for (OrderReadTotalDO record : records) {
            OrderWaiterPageTotalRespDTO orderWaiterInfoDTO = new OrderWaiterPageTotalRespDTO();
            BeanUtil.copyProperties(record, orderWaiterInfoDTO);
            respDTOList.add(orderWaiterInfoDTO);
        }
        log.info("组装好的的分页参数:{}", JacksonUtils.writeValueAsString(respDTOList));
        return new PageAdapter<>(page, respDTOList);
    }

    private List<String> checkStoreGuid(String userGuid, List<String> storeGuidList) {
        List<String> storeGuid = staffClientService.queryAllStoreGuid();
        log.info("员工所属门店信息：{}", JacksonUtils.writeValueAsString(storeGuid));
        if (CollectionUtils.isEmpty(storeGuid)) {
            return Lists.newArrayList();
        }
//        List<String> findStoreGuid = userStoreData.stream().map(UserDataDTO::getGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeGuidList)) {
            return storeGuid;
        }
        storeGuidList.retainAll(storeGuid);
        return storeGuidList;
    }

    private Page<OrderWaiterPageMakeUpRespDTO> transformMakeUp(IPage<OrderReadDO> page) {
        List<OrderReadDO> records = page.getRecords();
        List<OrderWaiterPageMakeUpRespDTO> respDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(records)) {
            log.info("分页数据为空！");
            return new PageAdapter<>(page, respDTOList);
        }
        for (OrderReadDO record : records) {
            OrderWaiterPageMakeUpRespDTO dto = new OrderWaiterPageMakeUpRespDTO();
            BeanUtil.copyProperties(record, dto);
            dto.setOrderGuid(record.getGuid().toString());
            if (1 == record.getTradeMode()) {
                //如果为快餐 桌台名称标识为 快餐
                dto.setDiningTableName("快餐");
            }
            List<OrderWaiterDO> orderWaiterDOList = record.getOrderWaiterDOList();
            //转换成前端需要的数据
            if (CollectionUtils.isNotEmpty(orderWaiterDOList)) {
                orderWaiterDOList.stream().forEach(e -> {
                    if (WaiterTypeEnum.CALL_WAITER.getValue() == e.getWaiterType()) {
                        dto.setCallWaiter(transformWaiterMakeUp(e));
                    }
                    if (WaiterTypeEnum.SERVE_WAITER.getValue() == e.getWaiterType()) {
                        dto.setServeWaiter(transformWaiterMakeUp(e));
                    }
                    if (WaiterTypeEnum.PASS_DISHES_WAITER.getValue() == e.getWaiterType()) {
                        dto.setPassDishesWaiter(transformWaiterMakeUp(e));
                    }
                    if (WaiterTypeEnum.TIDY_WAITER.getValue() == e.getWaiterType()) {
                        dto.setTidyWaiter(transformWaiterMakeUp(e));
                    }
                    if (WaiterTypeEnum.MOP_WAITER.getValue() == e.getWaiterType()) {
                        dto.setMopWaiter(transformWaiterMakeUp(e));
                    }
                });
            }
            respDTOList.add(dto);
        }
        return new PageAdapter<>(page, respDTOList);
    }

    private OrderWaiterInfoDTO transformWaiterMakeUp(OrderWaiterDO orderWaiterDO) {
        OrderWaiterInfoDTO orderWaiterInfoDTO = new OrderWaiterInfoDTO();
        BeanUtil.copyProperties(orderWaiterDO, orderWaiterInfoDTO);
        return orderWaiterInfoDTO;
    }
}