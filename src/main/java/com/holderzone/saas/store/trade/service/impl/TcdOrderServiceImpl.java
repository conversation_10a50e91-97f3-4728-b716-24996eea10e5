package com.holderzone.saas.store.trade.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.trade.config.OverallConfig;
import com.holderzone.saas.store.trade.service.TcdOrderService;
import com.holderzone.saas.store.trade.service.inner.interfaces.AsyncTradeService;
import com.holderzone.saas.store.trade.utils.HttpsClientUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executor;


/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TcdOrderServiceImpl implements TcdOrderService {

    private final OverallConfig overallConfig;

    private final AsyncTradeService asyncTradeService;

    private final Executor asyncOrderTcdExecutor;

    @Override
    public void asyncOrderState(String orderGuid) {
        UserContext userContext = UserContextUtils.get();
        asyncOrderTcdExecutor.execute(() -> {
            // 切换数据源
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            tcdOrderStateHandler(orderGuid);
        });
    }

    @Override
    public void syncOrderState(String orderGuid) {
        tcdOrderStateHandler(orderGuid);
    }

    private void tcdOrderStateHandler(String orderGuid) {
        // 查询订单详情
        OrderDetailPushMqDTO wxOrderDTO = asyncTradeService.getOrderDetailPushMqDTO(orderGuid);
        // 快餐订单同步
        log.info("赚餐小程序快餐订单状态同步到赚餐:orderGuid:{},入参:{}", orderGuid, JacksonUtils.writeValueAsString(wxOrderDTO));
        // 赚餐推送订单
        String zcResponse = HttpsClientUtils.doPostJSON(overallConfig.getPushOrderDetailUrl(), wxOrderDTO);
        log.info("订单状态推送赚餐返回:{}", zcResponse);
        if (StringUtils.isEmpty(zcResponse)) {
            log.error("订单推送赚餐返回失败");
            throw new BusinessException("已结账订单推送错误");
        }
    }
}
