package com.holderzone.saas.store.trade.utils;

import com.beust.jcommander.internal.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.saas.store.dto.member.activity.ThirdActivityTypeEnum;
import com.holderzone.saas.store.dto.order.common.*;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.CancelDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnOrderDetailDTO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.ItemPriceChangeEnum;
import com.holderzone.saas.store.enums.order.RuleTypeEnum;
import com.holderzone.saas.store.trade.config.OldCouponCalculateConfig;
import com.holderzone.saas.store.trade.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.trade.transform.AdjustOrderTransform;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AmountCalculationUtil
 * @date 2019/02/20 14:23
 * @description //订单金额计算工具类
 * @program holder-saas-store-trade
 */
@Slf4j
public class AmountCalculationUtil {

    private static OrderTransform orderTransform = OrderTransform.INSTANCE;

    private static AdjustOrderTransform adjustOrderTransform = AdjustOrderTransform.INSTANCE;

    /**
     * ITEM_DISCOUNT_PERCENT_BASE 字段为商品单品打折的时候的汇率     875代表8.75折   计算价格用 ：
     * 价格     *     折扣
     * ——————————————————————————
     * ITEM_DISCOUNT_PERCENT_BASE
     */
    public static final BigDecimal ITEM_DISCOUNT_PERCENT_BASE = new BigDecimal(1000);

    public static List<DineInItemDTO> buildItem(List<OrderItemDO> orderItemDOS, List<ItemAttrDO> itemAttrDOList, List
            <FreeReturnItemDO> freeReturnItemDOList) {
        List<DineInItemDTO> dineInItemDTOS = new ArrayList<>();
        Map<Long, List<OrderItemDO>> subItemMap = CollectionUtil.toListMap(orderItemDOS, "parentItemGuid");
        Map<Long, List<ItemAttrDO>> itemAttrMap = CollectionUtil.toListMap(itemAttrDOList, "orderItemGuid");
        for (OrderItemDO orderItemDO : orderItemDOS) {
            if (orderItemDO.getParentItemGuid() != null && orderItemDO.getParentItemGuid() != 0) {
                continue;
            }
            DineInItemDTO dineInItemDTO = orderTransform.orderItemDO2DineInItemDTO(orderItemDO);

            //套餐
            if (ItemUtil.isGroup(orderItemDO.getItemType())) {
                List<OrderItemDO> subOrderItemDOS = subItemMap.get(orderItemDO.getGuid());
                Map<String, List<OrderItemDO>> subgroupGuidMap = CollectionUtil.toListMap(subOrderItemDOS,
                        "subgroupGuid");

                List<PackageSubgroupDTO> packageSubgroupDTOS = new ArrayList<>();
                dineInItemDTO.setPackageSubgroupDTOS(packageSubgroupDTOS);
                for (String subgroupGuid : subgroupGuidMap.keySet()) {
                    List<OrderItemDO> subGroupOrderItemDOS = subgroupGuidMap.get(subgroupGuid);
                    PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
                    packageSubgroupDTOS.add(packageSubgroupDTO);
                    List<SubDineInItemDTO> subDineInItemDTOS = new ArrayList<>();
                    packageSubgroupDTO.setSubDineInItemDTOS(subDineInItemDTOS);
                    for (OrderItemDO subGroupOrderItemDO : subGroupOrderItemDOS) {
                        SubDineInItemDTO subDineInItemDTO = orderTransform.orderItemDO2SubDineInItemDTO
                                (subGroupOrderItemDO);
                        packageSubgroupDTO.setSubgroupName(subGroupOrderItemDO.getSubgroupName());
                        packageSubgroupDTO.setSubgroupGuid(subGroupOrderItemDO.getSubgroupGuid());
                        subDineInItemDTOS.add(subDineInItemDTO);
                        if (subGroupOrderItemDO.getHasAttr() != null && subGroupOrderItemDO.getHasAttr() == 1) {

                            List<ItemAttrDO> itemAttrDOS = itemAttrMap.get(subGroupOrderItemDO.getGuid());
                            List<ItemAttrDTO> itemAttrDTOS = orderTransform.itemAttrDOS2itemAttrDTOS(itemAttrDOS);
                            subDineInItemDTO.setItemAttrDTOS(itemAttrDTOS);
                            subDineInItemDTO.setSingleItemAttrTotal(getSingleAttrTotal(subGroupOrderItemDO,
                                    itemAttrMap, subItemMap));
                        }
                    }
                }
            }
            // 获取套餐加价
            BigDecimal addPriceTotal = getGroupAddPriceTotal(orderItemDO, dineInItemDTO);
            setDineInItemSingleAddPriceTotal(dineInItemDTO, addPriceTotal);
            //有属性
            if (orderItemDO.getHasAttr() != null && orderItemDO.getHasAttr() == 1) {

                List<ItemAttrDO> itemAttrDOS = itemAttrMap.get(orderItemDO.getGuid());
                List<ItemAttrDTO> itemAttrDTOS = orderTransform.itemAttrDOS2itemAttrDTOS(itemAttrDOS);
                dineInItemDTO.setItemAttrDTOS(itemAttrDTOS);
            }
            dineInItemDTO.setSingleItemAttrTotal(getSingleAttrTotal(orderItemDO, itemAttrMap, subItemMap));

            //计算普通菜品价格小计
            BigDecimal itemPrice = getItemPrice(dineInItemDTO, addPriceTotal, dineInItemDTO.getCurrentCount(), false);
            dineInItemDTO.setItemPrice(itemPrice);
            dineInItemDTO.setBeforeItemPrice(itemPrice);
            //有赠送
            if (BigDecimalUtil.greaterThanZero(orderItemDO.getFreeCount())) {
                Map<Object, List<FreeReturnItemDO>> listMap = CollectionUtil.toListMap(freeReturnItemDOList,
                        "orderItemGuid");
                List<FreeReturnItemDO> freeReturnItemDOS = listMap.get(orderItemDO.getGuid());
                List<FreeItemDTO> freeItemDTOS = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(freeReturnItemDOS)) {
                    for (FreeReturnItemDO freeReturnItemDO : freeReturnItemDOS) {
                        FreeItemDTO freeItemDTO = orderTransform.freeReturnItemDO2freeItemDTO(freeReturnItemDO);
                        //计算赠送菜品价格小计
                        BigDecimal freeItemPrice = getItemPrice(dineInItemDTO, addPriceTotal, freeReturnItemDO
                                .getCount(), true);
                        freeItemDTO.setItemPrice(freeItemPrice);
                        freeItemDTOS.add(freeItemDTO);
                    }
                }
                dineInItemDTO.setFreeItemDTOS(freeItemDTOS);
            }
            dineInItemDTOS.add(dineInItemDTO);
        }
        return dineInItemDTOS;
    }


    /**
     * 设置商品单个加价
     */
    private static void setDineInItemSingleAddPriceTotal(DineInItemDTO dineInItemDTO, BigDecimal addPriceTotal) {
        dineInItemDTO.setSingleAddPriceTotal(BigDecimal.ZERO);
        if (Objects.isNull(addPriceTotal) || addPriceTotal.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        BigDecimal count = dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount());
        if (count.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        dineInItemDTO.setSingleAddPriceTotal(addPriceTotal.divide(count, 2, RoundingMode.HALF_UP));
    }

    /**
     * 获取套餐加价
     */
    private static BigDecimal getGroupAddPriceTotal(OrderItemDO orderItemDO, DineInItemDTO dineInItemDTO) {
        List<PackageSubgroupDTO> packageSubgroupList = dineInItemDTO.getPackageSubgroupDTOS();
        if (CollectionUtil.isEmpty(packageSubgroupList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal count = dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount());
        if (Objects.equals(BooleanEnum.TRUE.getCode(), orderItemDO.getHasAttr())) {
            return orderItemDO.getAddPrice().multiply(count);
        }
        BigDecimal addPriceTotal = BigDecimal.ZERO;
        for (PackageSubgroupDTO packageSubgroupDTO : packageSubgroupList) {
            List<SubDineInItemDTO> subDineInItemList = packageSubgroupDTO.getSubDineInItemDTOS();
            if (CollectionUtil.isEmpty(subDineInItemList)) {
                continue;
            }
            for (SubDineInItemDTO subDineInItemDTO : subDineInItemList) {
                BigDecimal addPrice = subDineInItemDTO.getAddPrice();
                if (BigDecimalUtil.greaterThanZero(addPrice)) {
                    addPriceTotal = addPriceTotal.add(subDineInItemDTO.getAddPrice().multiply(count)
                            .multiply(subDineInItemDTO.getCurrentCount()));
                }
            }
        }
        return addPriceTotal;
    }

    /**
     * 构建调整单商品明细
     */
    public static List<DineInItemDTO> buildAdjustItem(List<AdjustOrderDetailsDO> details, List<ItemAttrDO> itemAttrDOList) {
        List<DineInItemDTO> dineInItemDTOS = new ArrayList<>();
        Map<Long, List<AdjustOrderDetailsDO>> subItemMap = CollectionUtil.toListMap(details, "parentItemGuid");
        Map<Long, List<ItemAttrDO>> itemAttrMap = CollectionUtil.toListMap(itemAttrDOList, "orderItemGuid");
        for (AdjustOrderDetailsDO detail : details) {
            if (detail.getParentItemGuid() != null && detail.getParentItemGuid() != 0) {
                continue;
            }
            DineInItemDTO dineInItemDTO = adjustOrderTransform.adjustOrderDetailsDO2DineInItemDTO(detail);
            dineInItemDTO.setGuid(String.valueOf(detail.getOrderItemGuid()));
            dineInItemDTO.setItemTypeGuid(Objects.isNull(dineInItemDTO.getItemTypeGuid()) ? "" : dineInItemDTO.getItemTypeGuid());
            dineInItemDTO.setItemTypeName(Objects.isNull(dineInItemDTO.getItemTypeName()) ? "" : dineInItemDTO.getItemTypeName());

            //套餐
            BigDecimal addPrice = BigDecimal.ZERO;
            if (ItemUtil.isGroup(detail.getItemType())) {
                List<AdjustOrderDetailsDO> subOrderItemDOS = subItemMap.get(detail.getGuid());
                Map<String, List<AdjustOrderDetailsDO>> subgroupGuidMap = CollectionUtil.toListMap(subOrderItemDOS, "subgroupGuid");

                List<PackageSubgroupDTO> packageSubgroupDTOS = new ArrayList<>();
                dineInItemDTO.setPackageSubgroupDTOS(packageSubgroupDTOS);
                for (String subgroupGuid : subgroupGuidMap.keySet()) {
                    List<AdjustOrderDetailsDO> subGroupOrderItemDOS = subgroupGuidMap.get(subgroupGuid);
                    PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
                    packageSubgroupDTOS.add(packageSubgroupDTO);
                    List<SubDineInItemDTO> subDineInItemDTOS = new ArrayList<>();
                    packageSubgroupDTO.setSubDineInItemDTOS(subDineInItemDTOS);
                    for (AdjustOrderDetailsDO subGroupOrderItemDO : subGroupOrderItemDOS) {
                        SubDineInItemDTO subDineInItemDTO = adjustOrderTransform.adjustOrderDetailsDO2SubDineInItemDTO(subGroupOrderItemDO);
                        packageSubgroupDTO.setSubgroupName(subGroupOrderItemDO.getSubgroupName());
                        packageSubgroupDTO.setSubgroupGuid(subGroupOrderItemDO.getSubgroupGuid());
                        subDineInItemDTOS.add(subDineInItemDTO);
                        if (BigDecimalUtil.greaterThanZero(subGroupOrderItemDO.getAddPrice())) {
                            // BigDecimal count = detail.getCurrentCount();
                            addPrice = addPrice.add(subGroupOrderItemDO.getAddPrice()
                                    .multiply(subGroupOrderItemDO.getCurrentCount()));
                        }

                        if (subGroupOrderItemDO.getHasAttr() != null && subGroupOrderItemDO.getHasAttr() == 1) {
                            List<ItemAttrDO> itemAttrDOS = itemAttrMap.get(subGroupOrderItemDO.getGuid());
                            List<ItemAttrDTO> itemAttrDTOS = orderTransform.itemAttrDOS2itemAttrDTOS(itemAttrDOS);
                            subDineInItemDTO.setItemAttrDTOS(itemAttrDTOS);
                            subDineInItemDTO.setSingleItemAttrTotal(getAdjustSingleAttrTotal(subGroupOrderItemDO,
                                    itemAttrMap, subItemMap));
                        }
                    }
                }
            }
            //有属性
            if (detail.getHasAttr() != null && detail.getHasAttr() == 1) {
                List<ItemAttrDO> itemAttrDOS = itemAttrMap.get(detail.getGuid());
                List<ItemAttrDTO> itemAttrDTOS = orderTransform.itemAttrDOS2itemAttrDTOS(itemAttrDOS);
                dineInItemDTO.setItemAttrDTOS(itemAttrDTOS);
            }
            dineInItemDTO.setSingleItemAttrTotal(getAdjustSingleAttrTotal(detail, itemAttrMap, subItemMap));
            //计算普通菜品价格小计
            BigDecimal addPriceTotal = addPrice.multiply(detail.getCurrentCount());
            BigDecimal itemPrice = getItemPrice(dineInItemDTO, addPriceTotal, dineInItemDTO.getCurrentCount(), false);
            dineInItemDTO.setItemPrice(itemPrice);
            dineInItemDTO.setBeforeItemPrice(itemPrice);
            // 属性总价 + 各子项属性加价
            dineInItemDTO.setSingleItemAttrTotal(dineInItemDTO.getSingleItemAttrTotal().add(addPrice));
            dineInItemDTOS.add(dineInItemDTO);
        }
        return dineInItemDTOS;
    }

    private static BigDecimal getItemPrice(DineInItemDTO dineInItemDTO, BigDecimal addPriceTotal, BigDecimal
            currentCount, boolean isFree) {
        //赠送  直接原价  A
        BigDecimal currentPrice = isFree ? dineInItemDTO.getOriginalPrice().multiply(currentCount)
                : dineInItemDTO.getPrice().multiply(currentCount);

        BigDecimal attrPrice = dineInItemDTO.getSingleItemAttrTotal();
        BigDecimal itemPrice;
        if (!dineInItemDTO.getItemType().equals(ItemTypeEnum.GROUP.getCode())) {

            if (!dineInItemDTO.getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
                attrPrice = dineInItemDTO.getSingleItemAttrTotal().multiply(currentCount);
            }
            if (BigDecimalUtil.greaterThanZero(currentPrice) || BigDecimalUtil.equelZero(currentPrice)) {
                itemPrice = BigDecimalUtil.setScale2(currentPrice.add(attrPrice));
            } else {
                itemPrice = BigDecimal.ZERO;
            }

        } else {
            itemPrice = (dineInItemDTO.getPrice().add(dineInItemDTO.getSingleItemAttrTotal()))
                    .multiply(currentCount);
            if (BigDecimalUtil.greaterThanZero(currentCount)) {
                itemPrice = itemPrice.add(addPriceTotal);
            }
        }
        return itemPrice;
    }

    private static BigDecimal getAdjustSingleAttrTotal(AdjustOrderDetailsDO orderItemDO, Map<Long, List<ItemAttrDO>> itemListMap,
                                                       Map<Long, List<AdjustOrderDetailsDO>> subItemMap) {
        BigDecimal attrTotal = BigDecimal.ZERO;
        if (ItemUtil.isGroup(orderItemDO.getItemType())) {
            List<AdjustOrderDetailsDO> adjustOrderItemDOS = subItemMap.get(orderItemDO.getGuid());
            if (CollectionUtil.isNotEmpty(adjustOrderItemDOS)) {
                for (AdjustOrderDetailsDO adjustItemDO : adjustOrderItemDOS) {
                    List<ItemAttrDO> itemAttrDOS = itemListMap.get(adjustItemDO.getGuid());
                    if (CollectionUtil.isNotEmpty(itemAttrDOS)) {
                        for (ItemAttrDO itemAttrDO : itemAttrDOS) {
                            BigDecimal subAttrTotal = itemAttrDO.getAttrPrice().multiply(adjustItemDO.getCurrentCount());
                            //处理套餐称重
                            if (!adjustItemDO.getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
                                subAttrTotal = subAttrTotal.multiply(adjustItemDO.getPackageDefaultCount());
                            }
                            attrTotal = attrTotal.add(subAttrTotal);
                        }
                    }
                }
            }
        } else {
            List<ItemAttrDO> adjustItemAttrDOS = itemListMap.get(orderItemDO.getGuid());
            if (CollectionUtil.isNotEmpty(adjustItemAttrDOS)) {
                for (ItemAttrDO itemAttrDO : adjustItemAttrDOS) {
                    attrTotal = attrTotal.add(itemAttrDO.getAttrPrice());
                }
            }
        }
        return attrTotal;
    }

    private static BigDecimal getSingleAttrTotal(OrderItemDO orderItemDO, Map<Long, List<ItemAttrDO>> itemListMap,
                                                 Map<Long, List<OrderItemDO>> subItemMap) {
        if (Boolean.TRUE.equals(ItemUtil.isGroup(orderItemDO.getItemType()))) {
            // 套餐商品
            if (Objects.equals(BooleanEnum.TRUE.getCode(), orderItemDO.getHasAttr())) {
                return orderItemDO.getAttrTotal();
            } else {
                return getOriginalGroupSingleAttrTotal(orderItemDO, itemListMap, subItemMap);
            }
        }
        // 非套餐商品
        List<ItemAttrDO> itemAttrDOS = itemListMap.getOrDefault(orderItemDO.getGuid(), Lists.newArrayList());
        return itemAttrDOS.stream().map(ItemAttrDO::getAttrPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 原 套餐获取属性价
     */
    private static BigDecimal getOriginalGroupSingleAttrTotal(OrderItemDO orderItemDO, Map<Long, List<ItemAttrDO>> itemListMap,
                                                              Map<Long, List<OrderItemDO>> subItemMap) {
        BigDecimal attrTotal = BigDecimal.ZERO;
        List<OrderItemDO> orderItemDOS = subItemMap.getOrDefault(orderItemDO.getGuid(), Lists.newArrayList());
        for (OrderItemDO itemDO : orderItemDOS) {
            boolean isWeigh = !itemDO.getItemType().equals(ItemTypeEnum.WEIGH.getCode());
            List<ItemAttrDO> itemAttrDOS = itemListMap.getOrDefault(itemDO.getGuid(), Lists.newArrayList());
            for (ItemAttrDO itemAttrDO : itemAttrDOS) {
                BigDecimal subAttrTotal = itemAttrDO.getAttrPrice().multiply(itemDO.getCurrentCount());
                //处理套餐称重
                if (!isWeigh) {
                    subAttrTotal = subAttrTotal.multiply(itemDO.getPackageDefaultCount());
                }
                attrTotal = attrTotal.add(subAttrTotal);
            }
        }
        return attrTotal;
    }

    public static BigDecimal getOrderFee(List<DineInItemDTO> dineInItemDTOS, BigDecimal appendFee) {
        BigDecimal orderFee = BigDecimal.ZERO;
        //暂时只加菜品小计，没有附加费
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getCurrentCount())) {
                orderFee = orderFee.add(dineInItemDTO.getItemPrice());
            }
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getFreeCount())) {
                List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
                for (FreeItemDTO freeItemDTO : freeItemDTOS) {
                    orderFee = orderFee.add(freeItemDTO.getItemPrice());
                }
            }

        }
        return orderFee.add(appendFee);
    }

    /**
     * 获取订单的价格
     *
     * @param dineInItemDTOS
     * @param appendFee
     * @return
     */
    public static BigDecimal getOrderFeePuty(List<DineInItemDTO> dineInItemDTOS, BigDecimal appendFee) {
        BigDecimal orderFee = BigDecimal.ZERO;
        //暂时只加菜品小计，没有附加费
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getCurrentCount())) {
                orderFee = orderFee.add(dineInItemDTO.getItemPrice());
            }
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getFreeCount())) {
                List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
                for (FreeItemDTO freeItemDTO : freeItemDTOS) {
                    orderFee = orderFee.add(freeItemDTO.getItemPrice());
                }
            }

        }
        return orderFee.add(appendFee);
    }

    public static boolean hasFree(List<OrderItemDO> orderItemDOS) {
        boolean hasFree = false;
        for (OrderItemDO orderItemDO : orderItemDOS) {
            if (BigDecimalUtil.greaterThanZero(orderItemDO.getFreeCount())) {
                hasFree = true;
            }
        }
        return hasFree;
    }

    public static void repairCancel(DineinOrderDetailRespDTO orderDetailRespDTO, OrderDO orderDO, List
            <TransactionRecordDO> transactionRecordDOS) {
        //作废信息

        CancelDetailDTO cancelDetailDTO = new CancelDetailDTO();
        cancelDetailDTO.setCancelDeviceName(BaseDeviceTypeEnum.getDesc(orderDO.getCancelDeviceType()));
        cancelDetailDTO.setCancelDeviceType(orderDO.getCancelDeviceType());
        cancelDetailDTO.setCancelReason(orderDO.getCancelReason());
        cancelDetailDTO.setCancelStaffGuid(orderDO.getCancelStaffGuid());
        cancelDetailDTO.setCancelStaffName(orderDO.getCancelStaffName());
        cancelDetailDTO.setCancelTime(orderDO.getCancelTime());
        //退款信息

        if (CollectionUtil.isNotEmpty(transactionRecordDOS)) {
            List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = orderTransform
                    .transactionRecordDOS2ActuallyPayFeeDetailDTOS(transactionRecordDOS);
            cancelDetailDTO.setActuallyPayFeeDetailDTOS(actuallyPayFeeDetailDTOS);
        }

        orderDetailRespDTO.setCancelDetailDTO(cancelDetailDTO);

    }

    public static List<DiscountFeeDetailDTO> getDiscountFeeDetailDTOS(List<DineInItemDTO> dineInItemDTOList,
                                                                      DiscountRuleBO discountRuleBO, List<DiscountDO>
                                                                              discountDOS, Boolean isSub, Boolean
                                                                              isCombine, BigDecimal appendFee,
                                                                      boolean groupon) {
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = new ArrayList<>();
        DiscountFeeDetailDTO systemDiscountFeeDetailDTO = new DiscountFeeDetailDTO();
        for (DiscountDO discountDO : discountDOS) {
            DiscountFeeDetailDTO discountFeeDetailDTO = orderTransform.discountDO2DiscountFeeDetailDTO(discountDO);
            switch (DiscountTypeEnum.get(discountDO.getDiscountType())) {
                case GROUPON:
                    if (!isCombine) {
                        //团购验券优惠不能抵消附加费
                        BigDecimal shouldPay = getOrderFee(dineInItemDTOList, BigDecimal.ZERO).subtract
                                (getFreeDiscountFee
                                        (dineInItemDTOList));
                        if (BigDecimalUtil.greaterThanZero(discountDO.getDiscountFee())) {
                            if (BigDecimalUtil.greaterEqual(discountDO.getDiscountFee(), shouldPay)) {
                                discountFeeDetailDTO.setDiscountFee(shouldPay);
                            } else {
                                discountFeeDetailDTO.setDiscountFee(discountDO.getDiscountFee());
                            }
                        }
                    }
                    break;
                case MEMBER:
                    if (!groupon) {
                        discountFeeDetailDTO.setDiscountFee(getMemberDiscountFee(dineInItemDTOList, discountRuleBO
                                .getMemberDiscount()));
                    } else {
                        discountFeeDetailDTO.setDiscountFee(BigDecimal.ZERO);
                    }
                    break;
                case WHOLE:
                    if (!groupon) {
                        discountFeeDetailDTO.setDiscountFee(getWholeDiscountFee(dineInItemDTOList, discountRuleBO
                                .getWholeDiscount(), discountRuleBO.getMemberDiscount()));
                    } else {
                        discountFeeDetailDTO.setDiscountFee(BigDecimal.ZERO);
                    }
                    break;
                case CONCESSIONAL:
                    if (!isSub) {
                        discountFeeDetailDTO.setDiscountFee(discountRuleBO.getConcessional() == null ? BigDecimal.ZERO :
                                discountRuleBO.getConcessional());
                    } else {
                        discountFeeDetailDTO.setDiscountFee(BigDecimal.ZERO);
                    }
                    break;
                case FREE:
                    discountFeeDetailDTO.setDiscountFee(getFreeDiscountFee(dineInItemDTOList));
                    break;
                case SYSTEM:
                    //清空库里的系统省零金额
                    discountFeeDetailDTO.setDiscountFee(BigDecimal.ZERO);
                    systemDiscountFeeDetailDTO = discountFeeDetailDTO;
                    break;
                default:
                    break;
            }
            discountFeeDetailDTOS.add(discountFeeDetailDTO);
        }
        if (!isCombine) {
            systemDiscountFeeDetailDTO.setDiscountFee(getSystemDiscountFee(discountRuleBO.getSystemDiscountDTOS(),
                    getShouldPay(discountFeeDetailDTOS, getOrderFee(dineInItemDTOList, appendFee))));
        }
        return discountFeeDetailDTOS;
    }

    public static BigDecimal getWholeDiscountFee(List<DineInItemDTO> dineInItemDTOList, BigDecimal wholeDiscount,
                                                 BigDecimal memberDiscount) {
        BigDecimal wholeDiscountFee = BigDecimal.ZERO;
        if (wholeDiscount != null) {
            for (DineInItemDTO dineInItemDTO : dineInItemDTOList) {
                if (dineInItemDTO.getIsWholeDiscount() == 1) {
                    if (dineInItemDTO.getIsMemberDiscount() == 1 && memberDiscount != null) {
                        BigDecimal singleMemberDiscount = BigDecimal.ONE;
                        if (BigDecimalUtil.greaterThanZero(memberDiscount)) {
                            singleMemberDiscount = BigDecimalUtil.divide(memberDiscount, BigDecimal.TEN);
                        }
                        wholeDiscountFee = wholeDiscountFee.add(dineInItemDTO.getItemPrice().multiply
                                (singleMemberDiscount).multiply(BigDecimalUtil.getRealDiscount(wholeDiscount)));

                    } else {
                        wholeDiscountFee = wholeDiscountFee.add(dineInItemDTO.getItemPrice().multiply(BigDecimalUtil
                                .getRealDiscount(wholeDiscount)));
                    }
                }
            }
        }

        return BigDecimalUtil.setScale2(wholeDiscountFee);
    }

    public static BigDecimal getMemberDiscountFee(List<DineInItemDTO> dineInItemDTOList, BigDecimal memberDiscount) {
        BigDecimal memberDiscountFee = BigDecimal.ZERO;
        BigDecimal memberItemFee = BigDecimal.ZERO;
        if (memberDiscount != null) {
            for (DineInItemDTO dineInItemDTO : dineInItemDTOList) {
                if (dineInItemDTO.getIsMemberDiscount() == 1 && BigDecimalUtil.greaterThanZero(dineInItemDTO
                        .getCurrentCount())) {
                    memberItemFee = memberItemFee.add(dineInItemDTO.getItemPrice());
                }
            }
            memberDiscountFee = memberDiscountFee.add(memberItemFee.multiply(BigDecimalUtil
                    .getRealDiscount(memberDiscount)));
        }


        return BigDecimalUtil.setScale2(memberDiscountFee);

    }

    public static BigDecimal getFreeDiscountFee(List<DineInItemDTO> dineInItemDTOS) {
        BigDecimal freeDiscount = BigDecimal.ZERO;
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getFreeCount())) {
                BigDecimal totalDiscountFee = BigDecimal.ZERO;
                List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
                for (FreeItemDTO freeItemDTO : freeItemDTOS) {
                    freeDiscount = freeDiscount.add(freeItemDTO.getItemPrice());
                    //Itemprice 中不包含赠送商品金额   这个地方不加  ，后面要itemPrice- totalDiscountFee
//                    totalDiscountFee = totalDiscountFee.add(freeItemDTO.getItemPrice());
                }
                dineInItemDTO.setTotalDiscountFee(totalDiscountFee);
            }

        }
        return freeDiscount;
    }

    public static BigDecimal getSystemDiscountFee(List<SystemDiscountDTO> systemDiscountDTOS, BigDecimal
            shouldPay) {

        // 系统省零计算
        SystemDiscountDTO systemDiscountDTO = getSystemDiscount(systemDiscountDTOS, shouldPay);
        if (null != systemDiscountDTO) {
            Integer roundType = systemDiscountDTO.getRoundType();
            Integer scale = systemDiscountDTO.getScale();
            if (roundType == 1) {
                return BigDecimalUtil.setScale2(shouldPay.subtract(shouldPay.setScale(scale, RoundingMode.HALF_UP)));
            }
            if (roundType == 2) {
                return BigDecimalUtil.setScale2(shouldPay.subtract(shouldPay.setScale(scale, RoundingMode.FLOOR)));
            }
            return BigDecimalUtil.setScale2(shouldPay.subtract(shouldPay.setScale(scale, RoundingMode.CEILING)));
        }
        return BigDecimal.ZERO;
    }

    private static void addReturnItemPrice(ReturnItemDTO returnItemDTO) {
        BigDecimal count = returnItemDTO.getCount();
        if (ObjectUtils.isEmpty(returnItemDTO.getDineInItemDTO())) {
            log.warn("商品查询异常[空指针预警]，returnItemDTO={}", JacksonUtils.writeValueAsString(returnItemDTO));
            return;
        }
        BigDecimal singleItemAttrTotal = returnItemDTO.getDineInItemDTO().getSingleItemAttrTotal();
        // 返回原价
        BigDecimal price = returnItemDTO.getDineInItemDTO().getPrice();
        BigDecimal itemPrice;
        if (!returnItemDTO.getDineInItemDTO().getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
            BigDecimal add = price.add(singleItemAttrTotal);
            itemPrice = count.multiply(add);
        } else {
            itemPrice = count.multiply(price).add(singleItemAttrTotal);
        }

        if (returnItemDTO.getDineInItemDTO().getItemType().equals(ItemTypeEnum.GROUP.getCode())) {
            itemPrice = returnItemDTO.getDineInItemDTO().getItemPrice();
        }
        returnItemDTO.setItemPrice(itemPrice);
    }

    public static List<ReturnItemDTO> bulidReturnItemDTOS(List<FreeReturnItemDO> returnItemDOS, Map<String,
            DineInItemDTO> dineInItemDTOMap) {
        List<ReturnItemDTO> returnItemDTOS = new ArrayList<>();
        for (FreeReturnItemDO freeReturnItemDO : returnItemDOS) {
            ReturnItemDTO returnItemDTO = orderTransform.freeReturnItemDO2rerurnItemDTO(freeReturnItemDO);
            returnItemDTO.setDineInItemDTO(dineInItemDTOMap.get(String.valueOf(freeReturnItemDO.getOrderItemGuid())));
            addReturnItemPrice(returnItemDTO);
            returnItemDTOS.add(returnItemDTO);
        }
        return returnItemDTOS;
    }

    public static SystemDiscountDTO getSystemDiscount(List<SystemDiscountDTO> systemDiscountDTOS, BigDecimal
            shouldPay) {
        return systemDiscountDTOS.stream().filter(systemDiscountDO -> Objects.equals(systemDiscountDO.getState(), 0))
                .sorted(Comparator.comparing(SystemDiscountDTO::getDiscountFee).reversed()).filter(systemDiscountDO
                        -> systemDiscountDO.getDiscountFee().compareTo(shouldPay) <= 0).findFirst().orElse(null);

    }

    public static List<DiscountDO> getDiscountDOS(List<DiscountDO> discountDOS, DiscountRuleBO discountRuleBO) {
        for (DiscountDO discountDO : discountDOS) {
            switch (DiscountTypeEnum.get(discountDO.getDiscountType())) {
                case MEMBER:
                    if (discountRuleBO.getMemberDiscount() == null) {
                        discountRuleBO.setMemberDiscount(discountDO.getDiscount());
                    } else {
                        discountDO.setDiscount(discountRuleBO.getMemberDiscount());
                    }
                    break;
                case WHOLE:
                    if (discountRuleBO.getWholeDiscount() == null) {
                        discountRuleBO.setWholeDiscount(discountDO.getDiscount());
                    } else {
                        discountDO.setDiscount(discountRuleBO.getWholeDiscount());
                    }
                    break;
                case CONCESSIONAL:
                    if (discountRuleBO.getConcessional() == null) {
                        discountRuleBO.setConcessional(discountDO.getDiscountFee());
                    } else {
                        discountDO.setDiscountFee(discountRuleBO.getConcessional());
                    }
                    break;
                case SYSTEM:
                    List<DiscountRuleBO> discountRuleBOS = discountRuleBO.getSystemDiscountDTOS().stream().map(src -> {
                        DiscountRuleBO item = new DiscountRuleBO();
                        BeanUtils.copyProperties(src, item);
                        return item;
                    }).collect(Collectors.toList());
                    discountDO.setRule(JacksonUtils.writeValueAsString(discountRuleBOS));
                    break;
                case POINTS_DEDUCTION:
                    if (discountRuleBO.getMemberIntegral() == null) {
                        if (StringUtils.isNotEmpty(discountDO.getRule())) {
                            discountRuleBO.setMemberIntegral(Integer.valueOf(discountDO.getRule()));
                        }
                    } else if (discountRuleBO.getMemberIntegral() == 3) {
                        //会员退出登录清除计算规则
                        discountDO.setRule(null);
                    } else {
                        discountDO.setRule(String.valueOf(discountRuleBO.getMemberIntegral()));
                    }
                    break;
//                case ACTIVITY:
//                    if (discountRuleBO.getActivityGuid() == null) {
//                        if(StringUtils.isNotEmpty(discountDO.getCouponsGuid())){
//                            discountRuleBO.setActivityGuid(discountDO.getCouponsGuid());
//                        }
//                    } else {
//                        discountDO.setRule(discountRuleBO.getActivityGuid());
//                    }
//                    break;
                default:
                    break;
            }
        }
        return discountDOS;

    }

    public static List<DiscountDO> getInsertDiscountDOS(List<Long> guids, List<DiscountDO> discountDOS, DiscountRuleBO
            discountRuleBO, String orderGuid) {
        List<DiscountDO> initDiscountList = getInitDiscountDOList(guids, orderGuid);
        if (!CollectionUtils.isEmpty(initDiscountList)) {
            discountDOS.addAll(initDiscountList);
        }
        return getDiscountDOS(discountDOS, discountRuleBO);
    }

    public static List<DiscountDO> getInitDiscountDOList(List<Long> guids, String orderGuid) {
        List<DiscountDO> discountDOS = Lists.newArrayList();
        for (DiscountTypeEnum discountTypeEnum : DiscountTypeEnum.values()) {
            if (!discountTypeEnum.equals(DiscountTypeEnum.OTHER)) {
                Long guid = guids.remove(guids.size() - 1);
                DiscountDO discountDO = new DiscountDO();
                discountDO.setGuid(guid);
                discountDO.setDiscountType(discountTypeEnum.getCode());
                discountDO.setDiscountName(discountTypeEnum.getDesc());
                discountDO.setStaffGuid(UserContextUtils.getUserGuid());
                discountDO.setStaffName(UserContextUtils.getUserName());
                discountDO.setStoreGuid(UserContextUtils.getStoreGuid());
                discountDO.setStoreName(UserContextUtils.getStoreName());
                discountDO.setOrderGuid(Long.valueOf(orderGuid));
                discountDO.setDiscountFee(BigDecimal.ZERO);
                discountDOS.add(discountDO);
            }
        }
        return discountDOS;
    }

    /**
     * 兼容阿里云  和  以后规则新增的问题
     *
     * @param guids
     * @param discountDOS
     * @param discountRuleBO
     * @param orderGuid
     * @return
     */
    public static List<DiscountDO> addNeedInsertDiscountDOS(Queue<Long> guids, List<DiscountDO> discountDOS, DiscountRuleBO
            discountRuleBO, String orderGuid) {
        List<Integer> exitsDiscountTypes = discountDOS.stream().map(DiscountDO::getDiscountType).collect(Collectors.toList());
        List<DiscountDO> needAdddiscountDOs = Stream.of(DiscountTypeEnum.values()).filter(a -> !a.equals(DiscountTypeEnum.OTHER))
                .filter(a -> !exitsDiscountTypes.contains(Integer.valueOf(a.getCode())))
                .map(discountTypeEnum -> {
                            DiscountDO discountDO = new DiscountDO();
                            discountDO.setGuid(guids.remove());
                            discountDO.setDiscountType(discountTypeEnum.getCode());
                            discountDO.setDiscountName(discountTypeEnum.getDesc());
                            discountDO.setStaffGuid(UserContextUtils.getUserGuid());
                            discountDO.setStaffName(UserContextUtils.getUserName());
                            discountDO.setStoreGuid(UserContextUtils.getStoreGuid());
                            discountDO.setStoreName(UserContextUtils.getStoreName());
                            discountDO.setOrderGuid(Long.valueOf(orderGuid));
                            return discountDO;
                        }
                ).collect(Collectors.toList());
        return needAdddiscountDOs;
    }

    public static BigDecimal getShouldPay(List<DiscountFeeDetailDTO> discountFeeDetailDTOS, BigDecimal orderFee) {
        BigDecimal shouldPay = orderFee;
        for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
            if (discountFeeDetailDTO.getDiscountFee() != null && !discountFeeDetailDTO
                    .getDiscountType().equals(DiscountTypeEnum.CONCESSIONAL.getCode())) {
                shouldPay = shouldPay.subtract(discountFeeDetailDTO.getDiscountFee());
            }
        }
        return shouldPay;
    }

    public static void calculationMainOrderAmount(DineinOrderDetailRespDTO orderDetailRespDTO, DiscountRuleBO
            discountRuleBO) {

        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetailRespDTO.getSubOrderDetails();
        for (DineinOrderDetailRespDTO subOrderDetail : subOrderDetails) {
            orderDetailRespDTO.setOrderFee(orderDetailRespDTO.getOrderFee().add(subOrderDetail.getOrderFee()));
            orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getActuallyPayFee().add(subOrderDetail
                    .getActuallyPayFee()));
            orderDetailRespDTO.setAppendFee(orderDetailRespDTO.getAppendFee().add(subOrderDetail.getAppendFee()));
            if (orderDetailRespDTO.getDiscountFee() == null) {
                orderDetailRespDTO.setDiscountFee(BigDecimal.ZERO);
            }
            if (subOrderDetail.getDiscountFee() == null) {
                subOrderDetail.setDiscountFee(BigDecimal.ZERO);
            }
            orderDetailRespDTO.setDiscountFee(orderDetailRespDTO.getDiscountFee().add(subOrderDetail.getDiscountFee()));
            //优惠明细
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS = orderDetailRespDTO.getDiscountFeeDetailDTOS();
            Map<Integer, DiscountFeeDetailDTO> discountTypeMap = CollectionUtil.toMap(subOrderDetail
                    .getDiscountFeeDetailDTOS(), "discountType");
            for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {

                if (discountTypeMap == null || discountTypeMap.size() == 0) {
                    continue;
                }
                DiscountFeeDetailDTO subDiscountFeeDetailDTO = discountTypeMap.get(discountFeeDetailDTO
                        .getDiscountType());
                if (subDiscountFeeDetailDTO == null || subDiscountFeeDetailDTO.getDiscountFee() == null) {
                    continue;
                }
                discountFeeDetailDTO.setDiscountFee(discountFeeDetailDTO.getDiscountFee().add(subDiscountFeeDetailDTO
                        .getDiscountFee()));
            }

        }
        //并单优惠计算时最后计算省零
        if (discountRuleBO != null) {
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS = orderDetailRespDTO.getDiscountFeeDetailDTOS();

            //计算并单省零时忽略让价
            BigDecimal systemDiscountFee = getSystemDiscountFee(discountRuleBO.getSystemDiscountDTOS(),
                    orderDetailRespDTO.getActuallyPayFee().add(discountRuleBO.getConcessional() == null ? BigDecimal
                            .ZERO : discountRuleBO.getConcessional()));
            for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
                if (discountFeeDetailDTO.getDiscountType().equals(DiscountTypeEnum.SYSTEM.getCode())) {
                    discountFeeDetailDTO.setDiscountFee(systemDiscountFee);
                }
            }
            orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getActuallyPayFee().subtract(systemDiscountFee));
        }
    }

    public static void repairAmount(OldCouponCalculateConfig oldCouponCalculateConfig,
                                    DineinOrderDetailRespDTO orderDetailRespDTO,
                                    List<TransactionRecordDO> transactionRecordDOS,
                                    List<DiscountDO> discountDOS) {

        filterGroupDiscount(oldCouponCalculateConfig, orderDetailRespDTO, discountDOS);
        orderDetailRespDTO.setDiscountFeeDetailDTOS(orderTransform.discountDOS2DiscountFeeDetailDTOS(discountDOS));

        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = orderTransform
                .transactionRecordDOS2ActuallyPayFeeDetailDTOS
                        (transactionRecordDOS);
        for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : actuallyPayFeeDetailDTOS) {
            if (PaymentTypeEnum.otherOrGroupPay(actuallyPayFeeDetailDTO.getPaymentType())) {
                actuallyPayFeeDetailDTO.setPaymentTypeName(actuallyPayFeeDetailDTO.getPaymentTypeName());
            } else {
                actuallyPayFeeDetailDTO.setPaymentTypeName(PaymentTypeEnum.getDesc(actuallyPayFeeDetailDTO.getPaymentType()));
            }
            // 填充聚合支付平台
            if (PaymentTypeEnum.AGG.getCode() == actuallyPayFeeDetailDTO.getPaymentType()) {
                String platForm = PayPowerId.getPlatFormById(String.valueOf(actuallyPayFeeDetailDTO.getPayPowerId()));
                actuallyPayFeeDetailDTO.setPaymentTypeName(actuallyPayFeeDetailDTO.getPaymentTypeName() + "-" + platForm + "支付");
            }
        }
        orderDetailRespDTO.setActuallyPayFeeDetailDTOS(actuallyPayFeeDetailDTOS);
    }

    private static void filterGroupDiscount(OldCouponCalculateConfig oldCouponCalculateConfig,
                                            DineinOrderDetailRespDTO orderDetailRespDTO, List<DiscountDO> discountDOS) {
        if (Objects.nonNull(oldCouponCalculateConfig.getOldCalculateMaxDateTime())
                && orderDetailRespDTO.getCheckoutTime().isAfter(oldCouponCalculateConfig.getOldCalculateMaxDateTime())) {
            orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getActuallyPayFee()
                    .add(Optional.ofNullable(orderDetailRespDTO.getTotalCouponBuyPrice()).orElse(BigDecimal.ZERO)));
        } else {
            // 老版本团购计算采用之前的逻辑
            BigDecimal totalGroupAmount = discountDOS.stream()
                    .filter(e -> GroupBuyTypeEnum.CODE_LIST.contains(e.getDiscountType()))
                    .map(DiscountDO::getDiscountFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getActuallyPayFee().add(totalGroupAmount));
            //优惠信息不展示团购信息
            discountDOS.removeIf(e -> GroupBuyTypeEnum.CODE_LIST.contains(e.getDiscountType()));
        }
    }

    public static void filterZeroItem(List<DineInItemDTO> dineInItemDTOS) {
        for (Iterator<DineInItemDTO> iterator = dineInItemDTOS.iterator(); iterator.hasNext(); ) {
            DineInItemDTO dineInItemDTO = iterator.next();
            if (BigDecimalUtil.equal(dineInItemDTO.getCurrentCount(), BigDecimal.ZERO) && BigDecimalUtil.equal
                    (dineInItemDTO.getFreeCount(), BigDecimal.ZERO)) {
                iterator.remove();
            }
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getFreeCount())) {
                List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
                freeItemDTOS.removeIf(freeItemDTO -> BigDecimalUtil.equal(freeItemDTO.getCount(), BigDecimal.ZERO));
            }
        }
    }

    public static void filterZeroItem(DineinOrderDetailRespDTO orderDetailRespDTO) {
        filterZeroItem(orderDetailRespDTO.getDineInItemDTOS());
    }

    public static void repairRecovery(DineinOrderDetailRespDTO orderDetailRespDTO, OrderDO orderDO,
                                      List<TransactionRecordDO> transactionRecordDOS) {

        ReturnOrderDetailDTO returnOrderDetailDTO = new ReturnOrderDetailDTO();
        returnOrderDetailDTO.setRecoveryDeviceTypeName(BaseDeviceTypeEnum.getDesc(orderDO.getRecoveryDeviceType()));
        returnOrderDetailDTO.setRecoveryFee(orderDO.getOrderFee());
        returnOrderDetailDTO.setRecoveryReason(orderDO.getRecoveryReason());
        returnOrderDetailDTO.setRecoveryStaffGuid(orderDO.getRecoveryStaffGuid());
        returnOrderDetailDTO.setRecoveryStaffName(orderDO.getRecoveryStaffName());
        returnOrderDetailDTO.setRecoveryNum(orderDO.getPrintPreBillNum());
        returnOrderDetailDTO.setRecoveryTime(orderDO.getCancelTime());


        if (CollectionUtil.isNotEmpty(transactionRecordDOS)) {
            List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = orderTransform
                    .transactionRecordDOS2ActuallyPayFeeDetailDTOS(transactionRecordDOS);
            returnOrderDetailDTO.setActuallyPayFeeDetailDTOS(actuallyPayFeeDetailDTOS);
        }

        orderDetailRespDTO.setReturnOrderDetailDTO(returnOrderDetailDTO);
    }

    /**
     * 会员价格补偿接口，由于安卓和其他客户端传过来的price是打折和改价后的商品，
     * 当价格有会员的时候  我们需要补偿价格  主要修改{@link DineInItemDTO} 改价主体类
     * 主要根据改价类型{@link ItemPriceChangeEnum}来判断,如果没有会员价直接跳过
     * case 1：
     * 当没有折扣的时候，我们需要补偿的价格为 -->  原价减去会员价
     * case 2:
     * 当使用改价的时候，不用会员价，补偿价格为  --->  0
     * case 3:
     * 当使用折扣的时候，补偿价格为 --->   (原价-会员价)*打折率
     *
     * @param dineInItemDTOS 逻辑类实体集合
     * @return 应该补偿的差价
     */
    public static BigDecimal getSingleMemeberDicountFee(List<DineInItemDTO> dineInItemDTOS,
                                                        Map<String, DineInItemDTO> dineInItemDTOMap,
                                                        boolean useBuyPrice) {
        BigDecimal freeDiscount = BigDecimal.ZERO;
        log.info("会员价补偿开始 dineInItemDTOS: {}", JacksonUtils.writeValueAsString(dineInItemDTOS));
        log.info("会员价补偿开始 dineInItemDTOMap: {}", JacksonUtils.writeValueAsString(dineInItemDTOMap));
        log.info("会员价补偿开始 useBuyPrice: {}", useBuyPrice);
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            //有会员价，如果他是1 不支持会员价，如果他是0，直接减去  如果他是2  （原价-会员价）*折扣
            if (dineInItemDTO.getMemberPrice() != null) {
                if (ItemPriceChangeEnum.PRICE_CHANGE.getCode() == dineInItemDTO.getPriceChangeType()) {
                    continue;
                }
                if (StringUtils.isNotEmpty(dineInItemDTO.getCouponCode())) {
                    continue;
                }
                dineInItemDTO.setIsCaculatByMemberPrice(1);
                //用了会员价后的差价
                BigDecimal disparityPrice = dineInItemDTO.getOriginalPrice().subtract(dineInItemDTO.getMemberPrice());
                if (dineInItemDTO.getIsGoodsReduceDiscount() != null) {
                    BigDecimal finalDisparityPrice = disparityPrice.multiply(dineInItemDTO.getCurrentCount()
                            .subtract(dineInItemDTO.getIsGoodsReduceDiscount()));
                    dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(finalDisparityPrice));
                    freeDiscount = freeDiscount.add(finalDisparityPrice);
                    dineInItemDTO.setItemPrice(BigDecimalUtil.setScale2(dineInItemDTO.getItemPrice()
                            .subtract(finalDisparityPrice)));
                } else {
                    BigDecimal discountFee = disparityPrice.multiply(dineInItemDTO.getCurrentCount());
                    BigDecimal itemNowFee = dineInItemDTO.getItemPrice().subtract(dineInItemDTO.getTotalDiscountFee());
                    BigDecimal nowMemberFee = itemNowFee.compareTo(discountFee) > 0 ? discountFee : itemNowFee;
                    freeDiscount = freeDiscount.add(nowMemberFee);
                    dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(discountFee));
                    dineInItemDTO.setItemPrice(BigDecimalUtil.setScale2(dineInItemDTO.getItemPrice().subtract(discountFee)));

                    setMemberPreferential(dineInItemDTO, dineInItemDTOMap, discountFee);
                }
//                dineInItemDTO.setPrice(dineInItemDTO.getPrice().subtract(disparityPrice));
//                dineInItemDTO.setTotalDiscountFee(dineInItemDTO.getTotalDiscountFee().add(disparityPrice.multiply(dineInItemDTO.getCurrentCount())));
            }
        }
        if (BigDecimalUtil.lessThanZero(freeDiscount)) {
            freeDiscount = BigDecimal.ZERO;
        }
        return BigDecimalUtil.setScale2(freeDiscount);
    }

    /**
     * 设置商品会员优惠
     */
    private static void setMemberPreferential(DineInItemDTO dineInItemDTO, Map<String, DineInItemDTO> dineInItemDTOMap, BigDecimal discountFee) {
        log.info("设置商品会员优惠 dineInItemDTOS: {}", JacksonUtils.writeValueAsString(dineInItemDTO));
        log.info("设置商品会员优惠 dineInItemDTOMap: {}", JacksonUtils.writeValueAsString(dineInItemDTOMap));
        log.info("设置商品会员优惠 useBuyPrice: {}", discountFee);
        if (dineInItemDTOMap.containsKey(dineInItemDTO.getGuid())) {
            DineInItemDTO item = dineInItemDTOMap.get(dineInItemDTO.getGuid());
            item.setMemberPreferential(discountFee);
            item.setDiscountPrice(dineInItemDTO.getDiscountPrice());
            log.warn("会员价优惠金额：{}，", discountFee);
        }
    }

    /**
     * 由于前端传过来的数据已经经过单品折扣了，这些数据将不会参与总价的计算，只是计算折扣
     * *  所以我们在计算的时候，分两种是否有会员
     * * case 1:
     * *         当有会员的时候，折扣费 = 会员 * (1000-折扣)
     * * case 2:
     * *         当没有会员的，折扣费 = 原价 * (1000-折扣)
     *
     * @param allItems
     * @return
     */
    public static BigDecimal getSingleDicountFee(List<DineInItemDTO> allItems, boolean isCaculateByMemeber) {
        BigDecimal discountReduce = BigDecimal.ZERO;
        for (DineInItemDTO dineInItemDTO : allItems) {
            if (dineInItemDTO.getPriceChangeType() != ItemPriceChangeEnum.DISCOUNT.getCode()) {
                continue;
            }
//            if(dineInItemDTO.getIsGoodsReduceDiscount().intValue() == 1){
//                continue;
//            }
//            BigDecimal discountPrice = dineInItemDTO.getOriginalPrice().subtract(dineInItemDTO.getPrice());
            BigDecimal singlediscountPrice = null;
            if (isCaculateByMemeber && dineInItemDTO.getMemberPrice() != null) {
                singlediscountPrice = dineInItemDTO.getMemberPrice()
                        .multiply(ITEM_DISCOUNT_PERCENT_BASE.subtract(new BigDecimal(dineInItemDTO.getDiscountPercent())))
                        .divide(ITEM_DISCOUNT_PERCENT_BASE, 2, BigDecimal.ROUND_HALF_DOWN);
            } else {
                singlediscountPrice = dineInItemDTO.getOriginalPrice()
                        .multiply(ITEM_DISCOUNT_PERCENT_BASE.subtract(new BigDecimal(dineInItemDTO.getDiscountPercent())))
                        .divide(ITEM_DISCOUNT_PERCENT_BASE, 2, BigDecimal.ROUND_HALF_DOWN);
            }
            BigDecimal discountPrice;
            if (dineInItemDTO.getIsGoodsReduceDiscount() != null) {
                discountPrice = singlediscountPrice.multiply(dineInItemDTO.getCurrentCount().subtract(dineInItemDTO.getIsGoodsReduceDiscount()));
            } else {
                discountPrice = singlediscountPrice.multiply(dineInItemDTO.getCurrentCount());
            }
            dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(discountPrice));
            if (dineInItemDTO.getPriceChangeType() != null && dineInItemDTO.getPriceChangeType().intValue() == 2) {
//                BigDecimal reduiceprice = dineInItemDTO.getOriginalPrice()
//                        .multiply(new BigDecimal(1000).subtract(new BigDecimal(dineInItemDTO.getDiscountPercent())))
//                        .divide(new BigDecimal(1000), 2,BigDecimal.ROUND_HALF_UP)
//                        .multiply(dineInItemDTO.getCurrentCount());
                dineInItemDTO.setItemPrice(dineInItemDTO.getItemPrice().subtract(discountPrice.setScale(2, BigDecimal.ROUND_HALF_DOWN)));
                dineInItemDTO.setPrice(dineInItemDTO.getPrice().subtract(singlediscountPrice));

            }
            discountReduce = discountReduce.add(discountPrice);
        }
        return BigDecimalUtil.setScale2(discountReduce);
    }


    /**
     * 单品改价  （原价-减价后的价格） *
     *
     * @param allItems
     * @return
     */
    public static BigDecimal getSinglePriceChangeFee(List<DineInItemDTO> allItems) {
        BigDecimal discountReduce = BigDecimal.ZERO;
        for (DineInItemDTO dineInItemDTO : allItems) {
            if (!(dineInItemDTO.getPriceChangeType().intValue() == ItemPriceChangeEnum.PRICE_CHANGE.getCode())) {
                continue;
            }
            BigDecimal discountPrice = dineInItemDTO.getOriginalPrice().subtract(dineInItemDTO.getPrice());
            discountReduce = discountReduce.add(discountPrice);
        }
        return discountReduce;
    }

    /**
     * 折扣转换
     *
     * @return
     */
    public static BigDecimal turnDicountPercentToDot(Integer percent) {
        return new BigDecimal(percent).divide(ITEM_DISCOUNT_PERCENT_BASE);
    }

    public static void handleActivityDeductionFee(ThirdActivityRecordDTO rec, ThirdActivityRespDTO thirdActivityDTO) {
        // 此处应该存在活动
        if (ObjectUtils.isEmpty(thirdActivityDTO)) {
            log.error("计算时未匹配到活动 activityGuid={}", rec.getActivityGuid());
            return;
        }

        // 老版本兼容处理
        if (BigDecimalUtil.greaterThanZero(thirdActivityDTO.getCouponFee())) {
            calculateThirdActivity(rec, thirdActivityDTO);
            return;
        }

        // 抖音只有绑定券才能用
        if (Objects.equals(ThirdActivityTypeEnum.DY.getThirdType(), thirdActivityDTO.getThirdType()) ||
                Objects.equals(ThirdActivityTypeEnum.ZF.getThirdType(), thirdActivityDTO.getThirdType())) {
            return;
        }

        // 美团金额扣减只能绑定才可以用，但是买单优惠属于纯第三方
        if (Objects.equals(ThirdActivityTypeEnum.MT.getThirdType(), thirdActivityDTO.getThirdType())) {
            if (Objects.equals(RuleTypeEnum.AMOUNT_DEDUCTION.getCode(), rec.getRuleType())) {
                return;
            }
            rec.setDeductionFee(rec.getJoinFee().add(rec.getNotJoinFee()));
        }
        calculateThirdActivity(rec, thirdActivityDTO);
    }

    private static void calculateThirdActivity(ThirdActivityRecordDTO rec, ThirdActivityRespDTO thirdActivityDTO) {
        if (Objects.equals(RuleTypeEnum.AMOUNT_DEDUCTION.getCode(), rec.getRuleType())) {
            // 金额扣减
            int num = rec.getThirdActivityCodeList().size();
            rec.setDeductionFee(BigDecimal.valueOf(num).multiply(thirdActivityDTO.getCouponFee()));
        } else {
            // 买单优惠
            rec.setDeductionFee(rec.getJoinFee().add(rec.getNotJoinFee()));
        }
    }

    /**
     * 处理折扣金额
     *
     * @param orderSurplusFee 订单剩余可以优惠金额
     * @param discountFee     折扣金额
     */
    public static BigDecimal dealWithDiscountFee(BigDecimal orderSurplusFee, BigDecimal discountFee) {
        if (discountFee.compareTo(orderSurplusFee) > 0) {
            discountFee = BigDecimalUtil.greaterThanZero(orderSurplusFee) ?
                    orderSurplusFee : BigDecimal.ZERO;
        }
        return discountFee;
    }

    /**
     * 活动的优惠按比例扣减掉商品的价格
     *
     * @param allItems    所有商品列表
     * @param discountFee 活动的优惠金额
     */
    public static void dealWithAllItems(List<DineInItemDTO> allItems, BigDecimal discountFee) {
        List<DineInItemDTO> itemDTOList = allItems.stream()
                .filter(i -> StringUtils.isEmpty(i.getCouponCode()))
                .collect(Collectors.toList());
        BigDecimal totalPrice = itemDTOList.stream()
                .map(DineInItemDTO::getItemPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 定义最后一个商品的优惠值，每次计算一个商品的优惠值，从总优惠中减去
        BigDecimal lastReduceAmount = discountFee;
        // 将优惠金额按商品价格占总价比例分摊
        for (int i = 0; i < itemDTOList.size(); i++) {
            DineInItemDTO dishInfo = itemDTOList.get(i);
            if (i == itemDTOList.size() - 1) {
                // 设置最后一个的优惠值
                dishInfo.setTotalDiscountFee(Optional.ofNullable(dishInfo.getTotalDiscountFee())
                        .orElse(BigDecimal.ZERO).add(lastReduceAmount));
            } else {
                // 单个商品优惠金额
                BigDecimal discountMoney = BigDecimal.ZERO;
                if (!BigDecimalUtil.equelZero(totalPrice)) {
                    discountMoney = BigDecimalUtil.multiply2(discountFee, dishInfo.getItemPrice()).
                            divide(totalPrice, 2, RoundingMode.HALF_DOWN);
                }
                dishInfo.setTotalDiscountFee(Optional.ofNullable(dishInfo.getTotalDiscountFee())
                        .orElse(BigDecimal.ZERO).add(discountMoney));
                lastReduceAmount = lastReduceAmount.subtract(discountMoney);
            }
        }
    }

    public static void buildRepeatOrderItem(List<DineInItemDTO> dineInItemDTOS) {
        for (DineInItemDTO item : dineInItemDTOS) {
            if (item.getParentItemGuid() != null && item.getParentItemGuid() != 0) {
                continue;
            }
            // 套餐
            BigDecimal addPriceTotal = getAddPriceTotal(item);

            // 属性
            BigDecimal attrTotal = getAttrTotal(item);
            item.setSingleItemAttrTotal(attrTotal);

            //计算普通菜品价格小计
            BigDecimal itemPrice = getItemPrice(item, addPriceTotal, item.getCurrentCount(), false);
            item.setItemPrice(itemPrice);
            item.setBeforeItemPrice(itemPrice);
            // 会员价
            item.setMemberPrice(getMemberPrice(item, addPriceTotal));

            //有赠送
            if (CollectionUtil.isNotEmpty(item.getFreeItemDTOS())) {
                List<FreeItemDTO> freeItemDTOS = item.getFreeItemDTOS();
                for (FreeItemDTO freeItemDTO : freeItemDTOS) {
                    //计算赠送菜品价格小计
                    BigDecimal freeItemPrice = getItemPrice(item, addPriceTotal, freeItemDTO.getCount(), true);
                    freeItemDTO.setItemPrice(freeItemPrice);
                }
            }
        }
    }


    private static BigDecimal getMemberPrice(DineInItemDTO item, BigDecimal addPriceTotal) {
        BigDecimal memberPrice = item.getMemberPrice();
        if (Objects.isNull(memberPrice)) {
            return null;
        }
        // 加价
        if (BigDecimalUtil.greaterThanZero(item.getCurrentCount())) {
            memberPrice = memberPrice.add(addPriceTotal);
        }
        BigDecimal singleItemAttrTotal = item.getSingleItemAttrTotal();
        if (BigDecimalUtil.greaterThanZero(singleItemAttrTotal)) {
            memberPrice = memberPrice.add(singleItemAttrTotal);
        }
        return memberPrice;
    }

    private static BigDecimal getAddPriceTotal(DineInItemDTO item) {
        BigDecimal addPriceTotal = BigDecimal.ZERO;
        if (ItemUtil.isGroup(item.getItemType())) {
            List<PackageSubgroupDTO> packageSubgroupDTOS = item.getPackageSubgroupDTOS();
            for (PackageSubgroupDTO packageSubgroupDTO : packageSubgroupDTOS) {
                List<SubDineInItemDTO> subDineInItemDTOS = packageSubgroupDTO.getSubDineInItemDTOS();
                for (SubDineInItemDTO subDineInItemDTO : subDineInItemDTOS) {
                    if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getAddPrice())) {
                        BigDecimal count = item.getCurrentCount().add(item.getFreeCount());
                        addPriceTotal = addPriceTotal.add(subDineInItemDTO.getAddPrice().multiply(count)
                                .multiply(subDineInItemDTO.getCurrentCount()));
                    }
                }
            }
        }
        return addPriceTotal;
    }

    private static BigDecimal getSubAttrTotal(SubDineInItemDTO subDineInItemDTO) {
        List<ItemAttrDTO> itemAttrDTOS = subDineInItemDTO.getItemAttrDTOS();
        BigDecimal subAttrTotal = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(itemAttrDTOS)) {
            for (ItemAttrDTO itemAttrDTO : itemAttrDTOS) {
                BigDecimal subAttr = itemAttrDTO.getAttrPrice().multiply(subDineInItemDTO.getCurrentCount());
                // 处理套餐称重
                if (!subDineInItemDTO.getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
                    subAttr = subAttr.multiply(subDineInItemDTO.getPackageDefaultCount());
                }
                subAttrTotal = subAttrTotal.add(subAttr);
            }
        }
        return subAttrTotal;
    }

    private static BigDecimal getAttrTotal(DineInItemDTO item) {
        BigDecimal attrTotal = BigDecimal.ZERO;
        if (ItemUtil.isGroup(item.getItemType())) {
            List<PackageSubgroupDTO> packageSubgroupDTOS = item.getPackageSubgroupDTOS();
            for (PackageSubgroupDTO packageSubgroupDTO : packageSubgroupDTOS) {
                List<SubDineInItemDTO> subDineInItemDTOS = packageSubgroupDTO.getSubDineInItemDTOS();
                for (SubDineInItemDTO subDineInItemDTO : subDineInItemDTOS) {
                    BigDecimal attrPrice = getSubAttrTotal(subDineInItemDTO);
                    attrTotal = attrTotal.add(attrPrice);
                }
            }
            return attrTotal;
        } else {
            if (CollectionUtil.isNotEmpty(item.getItemAttrDTOS())) {
                BigDecimal attrPrice = item.getItemAttrDTOS().stream()
                        .map(ItemAttrDTO::getAttrPrice)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                attrTotal = attrTotal.add(attrPrice);
            }
        }
        return attrTotal;
    }
}
