package com.holderzone.saas.store.trade.controller;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.saas.store.dto.order.request.tcd.TcdAddOrderReqDTO;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.factory.TcdFactory;
import com.holderzone.saas.store.trade.repository.TcdOrderRepository;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TongchidaoOrderController
 * @date 2019/01/04 8:53
 * @description 通吃岛订单接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/tcd")
@Api(description = "通吃岛订单接口")
@Slf4j
public class TongchidaoOrderController {

    private final TcdOrderRepository tcdOrderRepository;

    private final TcdFactory tcdFactory;

    @Autowired
    public TongchidaoOrderController(TcdOrderRepository tcdOrderRepository, TcdFactory tcdFactory) {
        this.tcdOrderRepository = tcdOrderRepository;
        this.tcdFactory = tcdFactory;
    }

    @ApiOperation(value = "通吃岛订单落库并打印结账单", notes = "通吃岛订单落库并打印结账单")
    @PostMapping("/add_order")
    @Transactional
    public String tcdAddOrderAndPrintBill(@RequestBody TcdAddOrderReqDTO tcdAddOrderReqDTO) {
        Map<String, String> itemGuid = new HashMap<>();
        Map<String, String> orderGuid = new HashMap<>();

        EnterpriseIdentifier.setEnterpriseGuid(tcdAddOrderReqDTO.getEnterpriseGuid());
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid(tcdAddOrderReqDTO.getEnterpriseGuid());
        userContext.setStoreGuid(tcdAddOrderReqDTO.getStoreGuid());
        userContext.setUserGuid(tcdAddOrderReqDTO.getUserGuid());
        userContext.setUserName(tcdAddOrderReqDTO.getUserName());
        UserContextUtils.put(userContext);
        OrderDO orderDO = tcdFactory.createOrderDO(tcdAddOrderReqDTO, orderGuid);
        OrderSubsidiaryDO orderSubsidiaryDO = tcdFactory.createOrderSubsidiaryDO(tcdAddOrderReqDTO, orderGuid);
        List<OrderItemDO> orderItemDOS = tcdFactory.createOrderItemDOS(tcdAddOrderReqDTO, itemGuid);
        List<ItemAttrDO> itemAttrDOS = tcdFactory.createItemAttrDOS(tcdAddOrderReqDTO, itemGuid);
        DiscountDO discountDO = tcdFactory.createDiscountDO(tcdAddOrderReqDTO);
        TransactionRecordDO transactionRecordDO = tcdFactory.createTransactionRecordDO(tcdAddOrderReqDTO);
        List<AppendFeeDO> appendFeeDOS = tcdFactory.createAppendFeeDOS(tcdAddOrderReqDTO);

        //入库
        tcdOrderRepository.addTcdOrder(orderDO, orderSubsidiaryDO, orderItemDOS, itemAttrDOS, discountDO,
                transactionRecordDO, appendFeeDOS);

        //扣库存
        if (orderDO.getState().equals(StateEnum.SUCCESS.getCode())) {
            tcdOrderRepository.reduceStock(orderItemDOS);
        }

        //打印结账单
        tcdOrderRepository.printBill(tcdAddOrderReqDTO, orderDO);

        EnterpriseIdentifier.remove();
        return "入库成功";
    }


}
