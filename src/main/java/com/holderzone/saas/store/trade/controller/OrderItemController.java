package com.holderzone.saas.store.trade.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.PriceChangeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.item.*;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.trade.anno.LocalizeRequireNotifyOrderGuids;
import com.holderzone.saas.store.trade.anno.RequireOrderCheckLock;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.service.DineInItemService;
import com.holderzone.saas.store.trade.utils.RedisKeyUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemController
 * @date 2019/01/04 8:53
 * @description 正餐商品接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/order_item")
@Api(tags = "正餐商品接口")
@Slf4j
public class OrderItemController {

    private final DineInItemService dineInItemService;

    private final RedisHelper redisHelper;

    @Autowired
    public OrderItemController(DineInItemService dineInItemService, RedisHelper redisHelper) {
        this.dineInItemService = dineInItemService;
        this.redisHelper = redisHelper;
    }


    @ApiOperation(value = "商品新增接口", notes = "商品新增接口")
    @PostMapping("/add_item")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public EstimateItemRespDTO addItem(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        log.info("加菜入参：{}", JacksonUtils.writeValueAsString(createDineInOrderReqDTO));
        EstimateItemRespDTO estimateItemRespDTO = dineInItemService.addItems(createDineInOrderReqDTO, Boolean.FALSE);
        log.info("加菜完成：{}", JacksonUtils.writeValueAsString(createDineInOrderReqDTO));
        String redisKey = RedisKeyUtil.getHstOrderItemKey(createDineInOrderReqDTO.getGuid());
        redisHelper.delete(redisKey);
        return estimateItemRespDTO;
    }

    @ApiOperation(value = "批量划菜", notes = "批量划菜")
    @PostMapping("/serve_item")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean serveItem(@RequestBody ServeItemReqDTO serveItemReqDTO) {
        return dineInItemService.batchServeItem(serveItemReqDTO);

    }

    @ApiOperation(value = "批量催菜", notes = "批量催菜")
    @PostMapping("/urge")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean urge(@RequestBody UpdateItemStateReqDTO updateItemStateReqDTO) {
        return dineInItemService.batchUrgeItem(updateItemStateReqDTO);

    }

    @ApiOperation(value = "批量叫起", notes = "批量叫起")
    @PostMapping("/call_up")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean callUp(@RequestBody UpdateItemStateReqDTO updateItemStateReqDTO) {
        log.info("[批量叫起]updateItemStateReqDTO={}", JacksonUtils.writeValueAsString(updateItemStateReqDTO));
        return dineInItemService.callUpItem(updateItemStateReqDTO);

    }

    @ApiOperation(value = "批量退货接口", notes = "批量退菜接口")
    @PostMapping("/return")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public BatchItemReturnOrFreeReqDTO returnItem(@RequestBody BatchItemReturnOrFreeReqDTO
                                                          batchItemReturnOrFreeReqDTO) {
        return dineInItemService.returnOrFreeItem(batchItemReturnOrFreeReqDTO, Boolean.TRUE, Boolean.TRUE);

    }

    @ApiOperation(value = "批量赠送接口", notes = "批量赠送接口")
    @PostMapping("/free")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public BatchItemReturnOrFreeReqDTO freeItem(@RequestBody BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO) {
        return dineInItemService.returnOrFreeItem(batchItemReturnOrFreeReqDTO, Boolean.FALSE, Boolean.TRUE);

    }

    @ApiOperation(value = "批量取消赠送接口", notes = "批量取消赠送接口")
    @PostMapping("/cancel_free")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean cancelFree(@RequestBody CancelFreeItemReqDTO cancelFreeItemReqDTO) {
        return dineInItemService.cancelFree(cancelFreeItemReqDTO);

    }

    @ApiOperation(value = "改价折扣接口", notes = "批量取消赠送接口")
    @PostMapping("/change_price")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public Boolean changePrice(@RequestBody PriceChangeItemReqDTO priceChangeItemReqDTO) {
        return dineInItemService.changePrice(priceChangeItemReqDTO);
    }

    /**
     * 根据pad下单guid查询下单商品
     *
     * @param padOrderGuid pad下单guid
     * @return 商品列表
     */
    @ApiOperation(value = "根据pad下单guid查询下单商品", notes = "根据pad下单guid查询下单商品")
    @GetMapping("/query_item_by_pad_guid")
    public List<DineInItemDTO> queryItemByPadGuid(@RequestParam("padOrderGuid") String padOrderGuid) {
        log.info("根据pad下单guid查询下单商品 padOrderGuid={}", padOrderGuid);
        return dineInItemService.queryItemByPadGuid(padOrderGuid);
    }

    /**
     * 根据订单guid查询订单商品
     *
     * @param orderGuid 订单guid
     * @return 订单商品
     */
    @ApiOperation(value = "根据订单guid查询定单商品")
    @GetMapping("/query_item_by_order_guid")
    public List<DineInItemDTO> queryItemByOrderGuid(@RequestParam("orderGuid") String orderGuid) {
        log.info("根据订单guid查询订单商品 orderGuid={}", orderGuid);
        return dineInItemService.queryItemByOrderGuid(orderGuid);
    }


    @ApiOperation(value = "套餐子菜换菜", notes = "套餐子菜换菜")
    @PostMapping("/change_item")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public EstimateItemRespDTO changeGroupItem(@RequestBody ChangeGroupItemReqDTO changeGroupItemReqDTO) {
        log.info("套餐子菜换菜入参,changeGroupItemReqDTO:{}", JacksonUtils.writeValueAsString(changeGroupItemReqDTO));
        EstimateItemRespDTO estimateItemRespDTO = dineInItemService.changeGroupItem(changeGroupItemReqDTO);
        // 删除缓存
        String redisKey = RedisKeyUtil.getHstOrderItemKey(changeGroupItemReqDTO.getOrderGuid());
        redisHelper.delete(redisKey);
        return estimateItemRespDTO;
    }

    @ApiOperation(value = "撤销套餐子菜换菜")
    @PostMapping("/cancel_change_item")
    @RequireOrderCheckLock
    @LocalizeRequireNotifyOrderGuids
    public EstimateItemRespDTO cancelChangeGroupItem(@RequestBody ChangeGroupItemReqDTO changeGroupItemReqDTO) {
        log.info("撤销套餐子菜换菜,changeGroupItemReqDTO:{}", JacksonUtils.writeValueAsString(changeGroupItemReqDTO));
        EstimateItemRespDTO estimateItemRespDTO = dineInItemService.cancelChangeGroupItem(changeGroupItemReqDTO);
        // 删除缓存
        String redisKey = RedisKeyUtil.getHstOrderItemKey(changeGroupItemReqDTO.getOrderGuid());
        redisHelper.delete(redisKey);
        return estimateItemRespDTO;
    }

    @ApiOperation(value = "根据guid查询订单商品")
    @PostMapping("/query_item_by_guid")
    public List<DineInItemDTO> queryItemByGuid(@RequestBody SingleListDTO req) {
        log.info("[根据guid查询订单商品]req={}", req);
        return dineInItemService.queryItemByGuid(req);
    }

    @ApiOperation("转菜")
    @RequireOrderCheckLock
    @PostMapping("/transfer_item")
    public void transferItem(@RequestBody TransferItemReqDTO transferReq) {
        log.info("[转菜]req={}", JacksonUtils.writeValueAsString(transferReq));
        dineInItemService.transferItem(transferReq);
        // 删除缓存
        String redisKeyOldOrder = RedisKeyUtil.getHstOrderItemKey(transferReq.getOldOrderGuid());
        redisHelper.delete(redisKeyOldOrder);
        String redisKeyNewOrder = RedisKeyUtil.getHstOrderItemKey(transferReq.getNewOrderGuid());
        redisHelper.delete(redisKeyNewOrder);
    }

}
