package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreBizDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreClientService
 * @date 2018/09/30 11:41
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = StoreClientService.FallBack.class)
public interface StoreClientService {

    @PostMapping("store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    @GetMapping("device/get_master_device_by_storeguid/{storeGuid}")
    StoreDeviceDTO getMasterDeviceByStoreGuid(@PathVariable("storeGuid") String storeGuid);

    /**
     * 根据门店guid查询门店营业信息
     * @param storeGuid
     * @return
     */
    @PostMapping("store/query_store_biz_by_guid")
    StoreBizDTO queryStoreBizByGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 获取营业日期
     * @param businessDateReqDTO
     * @return
     */
    @PostMapping("store/query_business_day")
    LocalDate queryBusinessDay(@RequestBody BusinessDateReqDTO businessDateReqDTO);

    /**
     * 获取门店信息及营业日期
     */
    @PostMapping("store/query_business_day_info")
    StoreDTO queryBusinessDayInfo(@RequestBody BusinessDateReqDTO businessDateReqDTO);

    @PostMapping("/store/query_store_and_brand_by_id_list")
    List<StoreDTO> queryStoreAndBrandByIdList(@RequestBody List<String> storeGuidList);

    @PostMapping("/store/get_alive_store_count")
    Integer getAliveStoreCount();

    @Component
    class FallBack implements FallbackFactory<StoreClientService> {
        private static final Logger logger = LoggerFactory.getLogger(StoreClientService.FallBack.class);

        @Override
        public StoreClientService create(Throwable throwable) {
            return new StoreClientService() {

                @Override
                public StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid) {
                    logger.error("获取营业日异常e={}", throwable.getMessage());
                    throw new ParameterException("获取营业日失败!");
                }

                @Override
                public StoreDeviceDTO getMasterDeviceByStoreGuid(String storeGuid) {
                    logger.error("获取主机异常e={}", throwable.getMessage());
                    throw new ParameterException("获取主机失败!");
                }


                @Override
                public StoreBizDTO queryStoreBizByGuid(String storeGuid) {
                    logger.error("根据门店guid查询门店营业信息异常e={}", throwable.getMessage());
                    throw new ParameterException("根据门店guid查询门店营业信息失败!");
                }

                @Override
                public List<StoreDTO> queryStoreAndBrandByIdList(List<String> storeGuidList){
                    logger.error("queryStoreAndBrandByIdList，e={}", throwable.getMessage());
                    throw new BusinessException("获取门店列表信息和品牌信息");
                }

                @Override
                public Integer getAliveStoreCount() {
                    logger.error("getAliveStoreCount，e={}", throwable.getMessage());
                    throw new BusinessException("获取营业门店数量失败！");
                }

                @Override
                public LocalDate queryBusinessDay(BusinessDateReqDTO businessDateReqDTO) {
                    logger

                            .error("获取营业日异常e={}", throwable.getMessage());
                    throw new ParameterException("获取营业日期失败!");
                }

                @Override
                public StoreDTO queryBusinessDayInfo(BusinessDateReqDTO businessDateReqDTO) {
                    logger.error("queryBusinessDayInfo，e={}", throwable.getMessage());
                    throw new BusinessException("获取门店信息及营业日期失败！");
                }
            };
        }
    }
}
