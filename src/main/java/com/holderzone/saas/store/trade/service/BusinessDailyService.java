package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessDailyService
 * @date 2019/02/12 15:00
 * @description 营业日报
 * @program holder-saas-store-trade
 */
public interface BusinessDailyService {
    /**
     * 赠/退菜统计
     * @param request
     * @param type
     * @return
     */
    List<ItemRespDTO> freeReturn(DailyReqDTO request, int type);

    /**
     * 商品销售统计
     * @param request
     * @return
     */
    List<ItemRespDTO> goods(DailyReqDTO request);

    /**
     * 属性销售统计
     * @param request
     * @return
     */
    List<AttrItemRespDTO> attr(DailyReqDTO request);

    /**
     * 分类销售统计
     * @param request
     * @return
     */
    List<ItemRespDTO> classify(DailyReqDTO request);

    /**
     * 用餐类型统计
     * @param request
     * @return
     */
    List<DiningTypeRespDTO> diningType(DailyReqDTO request);

    /**
     * 会员消费统计
     * @param request
     * @return
     */
    MemberConsumeRespDTO memberConsume(DailyReqDTO request);

    /**
     * 收款统计
     * @param request
     * @return
     */
    List<GatherRespDTO> gather(DailyReqDTO request);

    /**
     * 营业概况
     * @param request
     * @return
     */
    OverviewRespDTO overview(DailyReqDTO request);

    /**
     * 退款统计
     * @param request
     * @return
     */
    RefundRespDTO refund(DailyReqDTO request);
}
