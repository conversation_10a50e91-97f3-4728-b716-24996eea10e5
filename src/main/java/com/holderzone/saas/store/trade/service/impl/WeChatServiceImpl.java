package com.holderzone.saas.store.trade.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.RequestContext;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;
import com.holderzone.saas.store.trade.entity.domain.TransactionRecordDO;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeStateEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.repository.feign.MessageClientService;
import com.holderzone.saas.store.trade.repository.feign.StoreClientService;
import com.holderzone.saas.store.trade.repository.feign.TableClientService;
import com.holderzone.saas.store.trade.repository.interfaces.DiscountService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.repository.interfaces.TransactionRecordService;
import com.holderzone.saas.store.trade.service.*;
import com.holderzone.saas.store.trade.service.inner.interfaces.AsyncTradeService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className
 * @date 2018/09/04 16:10
 * @description
 * @program holder-saas-store-trade
 */
@Service
@Slf4j
public class WeChatServiceImpl implements WeChatService {

    private final DineInService dineInService;

    private final DynamicHelper dynamicHelper;

    private final OrderService orderService;

    private final DiscountService discountService;

    private final TransactionRecordService transactionRecordService;

    private final StoreClientService storeClientService;

    private final TableClientService tableClientService;

    private final BillService billService;

    private final OrderItemService orderItemService;

    private final MessageClientService messageClientService;

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    private final AsyncTradeService asyncTradeService;

    @Autowired
    private AppendFeeService appendFeeService;

    @Resource
    private OrderTableBillService orderTableBillService;

    @Autowired
    public WeChatServiceImpl(DineInService dineInService, DynamicHelper dynamicHelper, OrderService orderService,
                             DiscountService discountService, TransactionRecordService transactionRecordService,
                             StoreClientService storeClientService, TableClientService tableClientService,
                             BillService billService, MessageClientService
                                     messageClientService, OrderItemService orderItemService, AsyncTradeService asyncTradeService) {
        this.dineInService = dineInService;
        this.dynamicHelper = dynamicHelper;
        this.orderService = orderService;
        this.discountService = discountService;
        this.transactionRecordService = transactionRecordService;
        this.storeClientService = storeClientService;
        this.tableClientService = tableClientService;
        this.billService = billService;
        this.messageClientService = messageClientService;
        this.orderItemService = orderItemService;
        this.asyncTradeService = asyncTradeService;
    }

    @Override
    public OrderWechatDTO getOrder(String orderGuid) {
        OrderDO order = orderService.getById(orderGuid);
        OrderWechatDTO dto = orderTransform.orderDO2OrderDTO(order);
        return dto;
    }

    @Override
    public Boolean hasItem(Long orderGuid) {
        return orderItemService.hasCurrentItem(orderGuid);
    }

    @Override
    public List<DineinOrderDetailRespDTO> getOrderDetailList(SingleListDTO singleListDTO) {
        log.info("进入微信订单详情，singleListDTO={}", singleListDTO);
        //1.查询orderGuidLists
        List<OrderDO> orderLists = orderService.getListByIds(singleListDTO.getList());
        log.info("orderLists={}", orderLists);
        List<String> orderGuids = orderLists.stream().map(OrderDO::getGuid).map(String::valueOf).collect(Collectors
                .toList());
        //2.查询itemGuidLists
        List<OrderItemDO> itemLists = orderItemService.listByOrderLists(orderGuids);
        log.info("itemLists={}", itemLists);
        Map<String, List<OrderItemDO>> itemMap = itemLists.stream().collect(Collectors.groupingBy(a -> String.valueOf
                (a.getOrderGuid())));
        //3.查询优惠信息
        List<DiscountDO> discountList = discountService.listByOrderGuids(orderGuids);
        List<DiscountFeeDetailDTO> discountFeeDetailList = orderTransform.discountDOS2DiscountFeeDetailDTOS(discountList);
        Map<String, List<DiscountFeeDetailDTO>> discountFeeDetailMap = discountFeeDetailList.stream()
                .collect(Collectors.groupingBy(DiscountFeeDetailDTO::getOrderGuid));
        //4.组装数据
        List<DineinOrderDetailRespDTO> dataList = buildDineOrderDetailRespDTO(orderLists, itemMap, discountFeeDetailMap);
        log.info("dataList={}", JacksonUtils.writeValueAsString(dataList));
        return dataList;
    }


    private List<DineinOrderDetailRespDTO> buildDineOrderDetailRespDTO(List<OrderDO> orderLists, Map<String, List<OrderItemDO>> itemMap,
                                                                       Map<String, List<DiscountFeeDetailDTO>> discountFeeDetailMap) {
        // 筛选是否有多单结账的订单
        setSameOrderInfo(orderLists, itemMap, discountFeeDetailMap);

        List<DineinOrderDetailRespDTO> dataList = new ArrayList<>();
        for (OrderDO orderDO : orderLists) {
            DineinOrderDetailRespDTO data = new DineinOrderDetailRespDTO();
            data.setGuid(orderDO.getGuid().toString());
            data.setState(OrderUtil.fixOrderState(orderDO.getState()));
            List<OrderItemDO> items = itemMap.get(orderDO.getGuid().toString());
            List<DineInItemDTO> dineInItemLists = orderTransform.orderItemDO2DineInItemDTO(items);
            data.setDineInItemDTOS(dineInItemLists);
            data.setOrderFee(orderDO.getOrderFee());
            data.setActuallyPayFee(orderDO.getActuallyPayFee());
            data.setGmtCreate(orderDO.getGmtCreate());
            data.setCheckoutTime(orderDO.getCheckoutTime());
            data.setTradeMode(orderDO.getTradeMode());
            data.setUpperState(orderDO.getUpperState());
            // 优惠信息
            data.setDiscountFeeDetailDTOS(buildDiscountFeeDetailDTO(data, discountFeeDetailMap));
            data.setDiscountFee(data.getDiscountFeeDetailDTOS().stream()
                    .map(DiscountFeeDetailDTO::getDiscountFee)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            dataList.add(data);
        }
        return dataList;
    }

    private void setSameOrderInfo(List<OrderDO> orderLists, Map<String, List<OrderItemDO>> itemMap,
                                  Map<String, List<DiscountFeeDetailDTO>> discountFeeDetailMap) {
        // 筛选是否有多单结账的订单
        List<OrderDO> sameOrderList = orderLists.stream()
                .filter(e -> UpperStateEnum.SAME_ORDER_STATE.contains(e.getUpperState()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sameOrderList)) {
            return;
        }
        List<Long> sameOrderGuids = sameOrderList.stream().map(e -> {
            if (Objects.nonNull(e.getMainOrderGuid()) && !Objects.equals(0L, e.getMainOrderGuid())) {
                return e.getMainOrderGuid();
            }
            return e.getGuid();
        }).collect(Collectors.toList());
        // 查询多单
        List<OrderDO> sameOrders = orderService.otherListByMainOrderGuids(sameOrderGuids);
        List<Integer> states = Lists.newArrayList(StateEnum.READY.getCode(), StateEnum.PENDING.getCode(),
                StateEnum.FAILURE.getCode(), StateEnum.SUCCESS.getCode(), StateEnum.SUB_SUCCESS.getCode());
        sameOrders = sameOrders.stream().filter(e -> states.contains(e.getState())).collect(Collectors.toList());
        List<String> sameAllOrders = sameOrders.stream().map(e -> String.valueOf(e.getGuid())).collect(Collectors.toList());

        // 查询itemGuidLists
        List<OrderItemDO> itemLists = orderItemService.listByOrderLists(sameAllOrders);
        // 查询优惠信息
        List<DiscountDO> discountList = discountService.listByOrderGuids(sameAllOrders);
        List<DiscountFeeDetailDTO> discountFeeDetailList = orderTransform.discountDOS2DiscountFeeDetailDTOS(discountList);

        for (OrderDO orderDO : orderLists) {
            if (!UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
                continue;
            }
            Long orderGuid = orderDO.getGuid();
            // 单多结账
            List<OrderDO> sameOrderListByOrderGuid = sameOrders.stream()
                    .filter(e -> orderGuid.equals(e.getGuid())
                            || orderDO.getMainOrderGuid().equals(e.getMainOrderGuid())
                            || orderDO.getMainOrderGuid().equals(e.getGuid()))
                    .collect(Collectors.toList());
            // 如果没有结账完成 就是待支付
            if (!Objects.equals(StateEnum.SUCCESS.getCode(), orderDO.getState())) {
                orderDO.setState(StateEnum.READY.getCode());
            }
            List<Long> innerSameOrderList = sameOrderListByOrderGuid.stream().map(OrderDO::getGuid).collect(Collectors.toList());

            // total orderFee
            BigDecimal totalOrderFee = sameOrderListByOrderGuid.stream().map(OrderDO::getOrderFee)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            orderDO.setOrderFee(totalOrderFee);
            BigDecimal totalActuallyFee = sameOrderListByOrderGuid.stream().map(OrderDO::getActuallyPayFee)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            orderDO.setActuallyPayFee(totalActuallyFee);
            // item
            List<OrderItemDO> innerItemLists = itemLists.stream()
                    .filter(e -> innerSameOrderList.contains(e.getOrderGuid()))
                    .collect(Collectors.toList());
            List<OrderItemDO> orderItemDOS = itemMap.get(String.valueOf(orderGuid));
            itemMap.put(String.valueOf(orderGuid), mergeSameOrderItemDO(innerItemLists, orderItemDOS));
            // discount
            List<DiscountFeeDetailDTO> innerDiscountDOs = discountFeeDetailList.stream()
                    .filter(e -> innerSameOrderList.contains(Long.valueOf(e.getOrderGuid())))
                    .filter(e -> !Long.valueOf(e.getGuid()).equals(orderGuid))
                    .collect(Collectors.toList());
            List<DiscountFeeDetailDTO> discountDOs = discountFeeDetailMap.get(String.valueOf(orderGuid));
            discountFeeDetailMap.put(String.valueOf(orderGuid), mergeSameOrderDiscountDO(innerDiscountDOs, discountDOs));
        }
        log.info("多单结账orderDO构建完成: {}", JacksonUtils.writeValueAsString(orderLists));
    }

    private List<OrderItemDO> mergeSameOrderItemDO(List<OrderItemDO> innerItemLists, List<OrderItemDO> orderItemDOS) {
        orderItemDOS = Optional.ofNullable(orderItemDOS).orElse(Lists.newArrayList());
        Map<Long, OrderItemDO> orderItemDOMap = orderItemDOS.stream()
                .collect(Collectors.toMap(OrderItemDO::getGuid, Function.identity(), (key1, key2) -> key1));

        for (OrderItemDO innerItem : innerItemLists) {
            boolean hasItem = orderItemDOMap.containsKey(innerItem.getGuid());
            if (hasItem) {
                continue;
            }
            orderItemDOMap.put(innerItem.getGuid(), innerItem);
        }
        return new ArrayList<>(orderItemDOMap.values());
    }

    private List<DiscountFeeDetailDTO> mergeSameOrderDiscountDO(List<DiscountFeeDetailDTO> innerDiscountDOs,
                                                                List<DiscountFeeDetailDTO> discountDOs) {
        discountDOs = Optional.ofNullable(discountDOs).orElse(Lists.newArrayList());
        Map<Integer, DiscountFeeDetailDTO> orderDiscountDOMap = discountDOs.stream()
                .collect(Collectors.toMap(DiscountFeeDetailDTO::getDiscountType, Function.identity(), (key1, key2) -> key1));
        for (DiscountFeeDetailDTO innerDiscount : innerDiscountDOs) {
            DiscountFeeDetailDTO discountFeeDetailDTO = orderDiscountDOMap.get(innerDiscount.getDiscountType());
            if (Objects.nonNull(discountFeeDetailDTO)) {
                discountFeeDetailDTO.setDiscountFee(discountFeeDetailDTO.getDiscountFee().add(innerDiscount.getDiscountFee()));
            } else {
                discountFeeDetailDTO = innerDiscount;
            }
            orderDiscountDOMap.put(innerDiscount.getDiscountType(), discountFeeDetailDTO);
        }
        return new ArrayList<>(orderDiscountDOMap.values());
    }



    /**
     * 构建订单优惠信息
     */
    private List<DiscountFeeDetailDTO> buildDiscountFeeDetailDTO(DineinOrderDetailRespDTO data,
                                                                 Map<String, List<DiscountFeeDetailDTO>> discountFeeDetailMap) {
        List<DiscountFeeDetailDTO> discountFeeDetailList = discountFeeDetailMap.get(data.getGuid());
        if (CollectionUtils.isEmpty(discountFeeDetailList)) {
            return Lists.newArrayList();
        }
        Map<Integer, DiscountFeeDetailDTO> discountTypeMap = CollectionUtil.toMap(discountFeeDetailList, "discountType");
        // 第三方活动特殊处理
        DiscountFeeDetailDTO thirdActivityDiscount = discountTypeMap.get(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
        if (!ObjectUtils.isEmpty(thirdActivityDiscount)) {
            if (BigDecimalUtil.greaterThanZero(thirdActivityDiscount.getDiscountFee())) {
                BigDecimal excessAmount = Optional.ofNullable(data.getExcessAmount()).orElse(BigDecimal.ZERO);
                data.setActuallyPayFee(data.getActuallyPayFee()
                        .add(thirdActivityDiscount.getDiscountFee())
                        .subtract(excessAmount));
            }
            // 此处干掉第三方活动，避免显示
            discountFeeDetailList.removeIf(discount -> Objects.equals(DiscountTypeEnum.THIRD_ACTIVITY.getCode(), discount.getDiscountType()));
        }
        BigDecimal totalGroupAmount = discountFeeDetailList.stream()
                .filter(e -> GroupBuyTypeEnum.CODE_LIST.contains(e.getDiscountType()))
                .map(DiscountFeeDetailDTO::getDiscountFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        data.setActuallyPayFee(data.getActuallyPayFee().add(totalGroupAmount));
        // 优惠信息不展示团购信息
        discountFeeDetailList.removeIf(e -> GroupBuyTypeEnum.CODE_LIST.contains(e.getDiscountType()));
        // 过滤优惠为0的
        discountFeeDetailList.removeIf(e -> e.getDiscountFee().compareTo(BigDecimal.ZERO) <= 0);
        return discountFeeDetailList;
    }


    @Override
    public List<DineinOrderDetailRespDTO> getOrderDetails(SingleListDTO singleListDTO) {
        List<String> list = singleListDTO.getList();
        List<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOS = new ArrayList<>();
        for (String s : list) {
            dineinOrderDetailRespDTOS.add(dineInService.getOrderDetail(s));
        }
        return dineinOrderDetailRespDTOS;
    }

    @Override
    public Boolean pay(WeChatPayReqDTO weChatPayReqDTO) {
        TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
        Long guid = Long.valueOf(weChatPayReqDTO.getPayGuid());
        log.info("微信支付：payGuid={},weChatPayReqDTO={}", guid, weChatPayReqDTO);
        transactionRecordDO.setGuid(guid);
        transactionRecordDO.setOrderGuid(Long.valueOf(weChatPayReqDTO.getOrderGuid()));
        transactionRecordDO.setTerminalId(weChatPayReqDTO.getDeviceId());
        transactionRecordDO.setAmount(weChatPayReqDTO.getAmount());
        transactionRecordDO.setRefundableFee(weChatPayReqDTO.getAmount());
        transactionRecordDO.setPaymentType(PaymentTypeEnum.AGG.getCode());
        transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
        transactionRecordDO.setStaffGuid(weChatPayReqDTO.getUserGuid());
        transactionRecordDO.setStaffName(weChatPayReqDTO.getUserName());
        transactionRecordDO.setState(TradeStateEnum.READY.getCode());
        transactionRecordDO.setStoreGuid(weChatPayReqDTO.getStoreGuid());
        transactionRecordDO.setStoreName(weChatPayReqDTO.getStoreName());
        transactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
        transactionRecordDO.setCreateTime(LocalDateTime.now());
        transactionRecordDO.setBankTransactionId(weChatPayReqDTO.getBankTransactionId());
        try {
            Integer payPowerId = Integer.valueOf(weChatPayReqDTO.getPayPowerId());
            transactionRecordDO.setPayPowerId(payPowerId);
        } catch (Exception e) {
            log.error("payPowerId设置失败", e);
        }
        BigDecimal integralPrice = BigDecimal.ZERO;  //积分抵扣金额
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = weChatPayReqDTO.getDiscountFeeDetailDTOS();
        if(CollectionUtil.isNotEmpty(discountFeeDetailDTOS)){
            Optional<DiscountFeeDetailDTO> first = discountFeeDetailDTOS.stream()
                    .filter(x -> !StringUtils.isEmpty(x) &&    //优惠卷对象不能为空
                            !StringUtils.isEmpty(x.getDiscountType()) &&   //优惠卷类型不能为空
                            x.getDiscountType().equals(DiscountTypeEnum.POINTS_DEDUCTION.getCode()))  //优惠卷为积分抵扣
                    .findFirst();
            integralPrice = first.map(DiscountFeeDetailDTO::getDiscountFee).orElse(BigDecimal.ZERO);
        }
        log.info("支付时,积分抵扣金额：{}",integralPrice);
        List<DiscountDO> discountDOS = orderTransform.discountFeeDetailDTOS2discountDOS(discountFeeDetailDTOS);
        Map<Integer, DiscountFeeDetailDTO> discountFeeDetailDTOMap = CollectionUtil.toMap(discountFeeDetailDTOS,
                "discountType");
        DiscountFeeDetailDTO discountFeeDetailDTO = discountFeeDetailDTOMap.get(DiscountTypeEnum.ACTIVITY.getCode
                ());
        discountService.updateBatchById(discountDOS);
        OrderDO orderDO = orderService.getById(weChatPayReqDTO.getOrderGuid());
        boolean isCanteenPay = CommonUtil.hasGuid(orderDO.getCanteenConsumptionGuid());
        orderDO.setOrderFee(weChatPayReqDTO.getOrderFee());
        orderDO.setActuallyPayFee(weChatPayReqDTO.getActuallyPayFee());
        orderDO.setCheckoutDeviceType(weChatPayReqDTO.getDeviceType());
        //解决线上微信登陆了会员后结账，没有更新MemberConsumptionGuid导致反结账调用会员撤销支付时的异常
        if (CommonUtil.hasGuid(orderDO.getMemberGuid())) {
            BillPayReqDTO billPayReqDTO1 = orderTransform.weChatPayReqDTODTO2BillPayReqDTO(weChatPayReqDTO);
            billPayReqDTO1.setAppendFee(orderDO.getAppendFee());
            billPayReqDTO1.setChangeFee(orderDO.getChangeFee());
            billPayReqDTO1.setCanteenPay(isCanteenPay);
            billPayReqDTO1.setUseIntegral(Optional.ofNullable(orderDO).map(OrderDO::getUseIntegral).orElse(0));  //解决订单不扣除积分问题
            billPayReqDTO1.setIntegralDiscountMoney(integralPrice); //解决微信支付时,积分抵扣价格为null
            if (orderDO.getTradeMode() == 0) {
                billPayReqDTO1.setFastFood(false);
            } else if (orderDO.getTradeMode() == 1) {
                billPayReqDTO1.setFastFood(true);
            }
            billPayReqDTO1.setMemberInfoCardGuid(orderDO.getMemberCardGuid());
            billPayReqDTO1.setMemberConsumptionGuid(orderDO.getMemberConsumptionGuid());
            if(isCanteenPay){
                billPayReqDTO1.setMemberConsumptionGuid(orderDO.getCanteenConsumptionGuid());
            }
            List<BillPayReqDTO.Payment> payLists = new ArrayList<>();
            BillPayReqDTO.Payment pay = new BillPayReqDTO.Payment();
            pay.setAmount(weChatPayReqDTO.getAmount());
            pay.setPaymentType(PaymentTypeEnum.AGG.getCode());
            pay.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
            payLists.add(pay);
            //微信支付写为聚合支付
            billPayReqDTO1.setPayments(payLists);
            if(CommonUtil.hasGuid(orderDO.getMemberGuid())){
                log.info("微信支付：billPayReqDTO1={}，orderDO={}", billPayReqDTO1, orderDO);
                String memberConsumptionGuid = billService.memberPay(orderDO, null, billPayReqDTO1);
                orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
                if(isCanteenPay){
                    orderDO.setCanteenConsumptionGuid(memberConsumptionGuid);
                }
            }else if(discountFeeDetailDTO != null && discountFeeDetailDTO.getDiscountFee() != null && discountFeeDetailDTO
                    .getDiscountFee().compareTo(BigDecimal.ZERO) > 0){
                String memberConsumptionGuid = billService.notmemberPay(orderDO, null, billPayReqDTO1);
                orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
            }else{
                orderDO.setMemberConsumptionGuid("0");
            }
        }

        log.info("微信支付：weChatPayReqDTO.isSuccess()={}", weChatPayReqDTO.isSuccess());
        if (weChatPayReqDTO.isSuccess()) {
            //营业日新接口
            BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
            ArrayList<String> storeGuidList = new ArrayList<>();
            storeGuidList.add(UserContextUtils.getStoreGuid());
            reqDTO.setStoreGuidList(storeGuidList);
            reqDTO.setQueryDateTime(LocalDateTime.now());
            LocalDate businessDay = storeClientService.queryBusinessDay(reqDTO);

            transactionRecordDO.setBusinessDay(businessDay);
            transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
            orderDO.setState(StateEnum.SUCCESS.getCode());
            orderDO.setBusinessDay(businessDay);
            orderDO.setCheckoutStaffGuid(weChatPayReqDTO.getUserGuid());
            orderDO.setCheckoutStaffName(weChatPayReqDTO.getUserName());
            orderDO.setCheckoutTime(LocalDateTime.now());
            if (orderDO.getUpperState().equals(UpperStateEnum.MAIN.getCode())) {
                List<OrderDO> orderDOS = orderService.listByMainOrderGuid(String.valueOf(orderDO.getGuid()));
                for (OrderDO subOrderDO : orderDOS) {
                    subOrderDO.setState(StateEnum.SUCCESS.getCode());
                    subOrderDO.setCheckoutStaffGuid(weChatPayReqDTO.getUserGuid());
                    subOrderDO.setCheckoutStaffName(weChatPayReqDTO.getUserName());
                    subOrderDO.setCheckoutTime(LocalDateTime.now());
                }
            }
            RequestContext.setBaseDTO(weChatPayReqDTO);
            // 订单结账不清台
            orderCloseHandler(weChatPayReqDTO.getCloseTableFlag(), orderDO);

            orderService.updateById(orderDO);
            transactionRecordService.save(transactionRecordDO);
            // 保存附加费明细
            if (Objects.equals(TradeModeEnum.DINEIN.getCode(), orderDO.getTradeMode())) {
                appendFeeService.persistAppendFee(orderDO.getGuid());
            }

            // 订单结账不清台
            if (Objects.equals(BooleanEnum.TRUE.getCode(), weChatPayReqDTO.getCloseTableFlag())) {
                // 创建订单
                BillPayReqDTO billPayReqDTO = JacksonUtils.toObject(BillPayReqDTO.class, JacksonUtils.writeValueAsString(weChatPayReqDTO));
                orderTableBillService.dealEnableHandleClose(billPayReqDTO);
                if (!UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())) {
                    // 打印结账单
                    asyncTradeService.asyncCheckOutCall(weChatPayReqDTO, orderDO, dineInService.getOrderDetails
                            (String.valueOf(orderDO.getGuid())));
                }
            } else {
                // 打印结账单
                asyncTradeService.asyncCheckOutCall(weChatPayReqDTO, orderDO, dineInService.getOrderDetails
                        (String.valueOf(orderDO.getGuid())));
            }
            BigDecimal divide = weChatPayReqDTO.getAmount();
            StoreDeviceDTO masterDeviceByStoreGuid = storeClientService.getMasterDeviceByStoreGuid(weChatPayReqDTO
                    .getStoreGuid());
            BusinessMessageDTO build = BusinessMessageDTO.builder()
                    .storeGuid(weChatPayReqDTO.getStoreGuid())
                    .storeName(weChatPayReqDTO.getStoreName())
                    .messageType(1)
                    .detailMessageType(12)
                    .platform("2")
                    .subject("收款成功，微信到账" + divide.doubleValue() + "元")
                    .content(masterDeviceByStoreGuid.getDeviceGuid())
                    .build();
            messageClientService.notifyMsg(build);
        } else {
            transactionRecordDO.setState(TradeStateEnum.FAILURE.getCode());
            orderDO.setState(StateEnum.FAILURE.getCode());
            orderService.updateById(orderDO);
            transactionRecordService.save(transactionRecordDO);
        }
        return Boolean.TRUE;
    }


    /**
     * 订单结账不清台
     */
    private void orderCloseHandler(Integer closeTableFlag, OrderDO orderDO) {
        if (!Objects.equals(BooleanEnum.TRUE.getCode(), closeTableFlag)
                || UpperStateEnum.COMBINE_ORDER_STATE.contains(orderDO.getUpperState())) {
            return;
        }
        orderDO.setState(StateEnum.SUB_SUCCESS.getCode());
        // 主单
        if (!Objects.equals(UpperStateEnum.SAME_SUB.getCode(), orderDO.getUpperState())) {
            orderDO.setUpperState(UpperStateEnum.SAME_MAIN.getCode());
        }
    }
}
