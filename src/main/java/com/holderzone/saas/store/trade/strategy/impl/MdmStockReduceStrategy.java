package com.holderzone.saas.store.trade.strategy.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.trade.bo.StockErpBO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.enums.trade.StockReduceStrategyEnum;
import com.holderzone.saas.store.trade.repository.feign.ItemClientService;
import com.holderzone.saas.store.trade.strategy.StockReduceStrategy;
import com.holderzone.saas.store.trade.utils.HttpUtil;
import com.holderzone.saas.store.trade.utils.ItemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * MDM库存扣减策略实现
 */
@Slf4j
@Component
public class MdmStockReduceStrategy implements StockReduceStrategy {

    @Value("${mdm.host:#{null}}")
    private String mdmRequestHost;

    @Resource
    private ItemClientService itemClientService;

    @Override
    public String getStrategyName() {
        return StockReduceStrategyEnum.MDM.getCode();
    }

    @Override
    public boolean isSupport() {
        if (mdmRequestHost == null) {
            log.info("mdmHost未配置，跳过MDM库存操作");
            return false;
        }
        return true;
    }

    @Override
    public void reduceStock(StockErpBO stockErpBO) {
        if (!isSupport()) {
            return;
        }
        List<DineInItemDTO> items = stockErpBO.getItems();
        String orderGuid = stockErpBO.getOrderGuid();
        OrderDO orderDO = stockErpBO.getOrderDO();
        try {
            Object pushOrderBillsBO = ItemUtil.combineMdmMessage(
                    orderDO,
                    items,
                    false,
                    itemClientService::getErpSkuGuids
            );
            String httpRequestUrl = String.format("%s/api/mdm/Store/bill", mdmRequestHost).intern();
            log.info("调用MDM扣减库存：订单guid：{}，入参{}",
                    orderGuid,
                    JacksonUtils.writeValueAsString(pushOrderBillsBO));
            HttpUtil.doPostJson(httpRequestUrl, JacksonUtils.writeValueAsString(pushOrderBillsBO));
        } catch (Exception e) {
            log.error("调用MDM扣减库存异常：{}", e.getMessage(), e);
        }
    }

    @Override
    public void returnStock(StockErpBO stockErpBO) {
        if (!isSupport()) {
            return;
        }
        List<DineInItemDTO> items = stockErpBO.getItems();
        String orderGuid = stockErpBO.getOrderGuid();
        OrderDO orderDO = stockErpBO.getOrderDO();
        try {
            Object pushOrderBillsBO = ItemUtil.combineMdmMessage(
                    orderDO,
                    items,
                    true,
                    itemClientService::getErpSkuGuids
            );
            String httpRequestUrl = String.format("%s/api/mdm/Store/bill", mdmRequestHost).intern();
            log.info("调用MDM回退库存：订单guid：{}，入参{}",
                    orderGuid,
                    JacksonUtils.writeValueAsString(pushOrderBillsBO));

            HttpUtil.doPostJson(httpRequestUrl, JacksonUtils.writeValueAsString(pushOrderBillsBO));
        } catch (Exception e) {
            log.error("调用MDM回退库存异常：{}", e.getMessage(), e);
        }
    }

    @Override
    public void adjustStock(StockErpBO stockErpBO) {
        UserContext userContext = UserContextUtils.get();
        DepositErpSyncDTO erpSyncDTO = stockErpBO.getDepositErpSyncDTO();
        String url = mdmRequestHost + "/api/inventory/deposit/adjust";
        log.info("调整商品调用erp,url:{}，请求参数：{}", url, JSONUtil.parse(erpSyncDTO));
        String result = HttpRequest.post(url)
                .header("enterpriseGuid", userContext.getEnterpriseGuid())
                .body(JSONUtil.parse(erpSyncDTO).toString())
                .execute().body();
        log.info("调整商品调用erp,返回结果：{}", result);
    }

}