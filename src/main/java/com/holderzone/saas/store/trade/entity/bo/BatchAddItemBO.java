package com.holderzone.saas.store.trade.entity.bo;

import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.trade.entity.domain.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/19
 * @description 批量加菜bo
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "批量加菜bo")
public class BatchAddItemBO {

    private OrderDO orderDO;

    private LocalDateTime now;

    private Long orderGuid;

    private List<FreeReturnItemDO> freeReturnItemDOS;

    private List<OrderItemRecordDO> itemRecordSave;

    private List<ItemAttrDO> itemAttrDOS;

    private List<OrderItemDO> orderItemDOS;

    private Map<String, ItemInfoRespDTO> itemInfoRespDTOMap;

    private List<OrderItemExtendsDO> orderItemExtendsDOS;

    private List<OrderItemChangesDO> orderItemChangesList;

}
