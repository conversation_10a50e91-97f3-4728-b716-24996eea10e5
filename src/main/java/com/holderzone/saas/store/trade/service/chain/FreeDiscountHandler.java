package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023-06-25
 * @description
 */
@Component
@Slf4j
public class FreeDiscountHandler extends DiscountHandler{

    @Override
    void dealDiscount(DiscountContext context) {
        // 菜品赠送优惠
        DiscountFeeDetailDTO freeDiscount = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap()
                .get(type()));
        //getFreeDiscountFee方法新增计算赠送优惠金额字段逻辑
        freeDiscount.setDiscountFee(AmountCalculationUtil.getFreeDiscountFee(context.getAllItems()));
        context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(freeDiscount
                .getDiscountFee()));
        context.getDiscountFeeDetailDTOS().add(freeDiscount);
        context.getOrderDetailRespDTO().setThirdActivityMaxFee(context.getOrderDetailRespDTO().getOrderSurplusFee());
        context.setFreeDiscount(freeDiscount);
        log.info("菜品赠送优惠：{}，订单剩余金额：{}", freeDiscount.getDiscountFee(), context.getOrderDetailRespDTO().getOrderSurplusFee());
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.FREE.getCode();
    }
}
