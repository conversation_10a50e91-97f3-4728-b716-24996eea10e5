package com.holderzone.saas.store.trade.utils.caculate;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.volume.*;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.req.PadDineInItemDTO;
import com.holderzone.saas.store.dto.trade.resp.PadPriceRespDTO;
import com.holderzone.saas.store.dto.weixin.PricePairDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.order.ItemPriceChangeEnum;
import com.holderzone.saas.store.trade.constants.DiscountTypeConstants;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.repository.feign.ItemClientService;
import com.holderzone.saas.store.trade.repository.feign.MemberTerminalClientService;
import com.holderzone.saas.store.trade.repository.interfaces.GrouponMpService;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 价格计算工具类
 */
@Component
@Slf4j
@AllArgsConstructor
public class PriceCalculationUtils {

    private final GrouponMpService grouponMpService;

    /**
     * 属性加价计算
     *
     * @param itemInfoAttrGroupDTOS 属性集合
     * @return 价格
     */
    public BigDecimal getAttrTotalPrice(List<ItemInfoAttrGroupDTO> itemInfoAttrGroupDTOS) {
        return ObjectUtils.isEmpty(itemInfoAttrGroupDTOS)
                ? BigDecimal.ZERO
                : itemInfoAttrGroupDTOS.stream().map(x -> {
            List<ItemInfoAttrDTO> attrList = x.getAttrList();
            if (!ObjectUtils.isEmpty(attrList)) {
                return attrList.stream().filter(k -> k.getUck() == 1).map(k ->
                                Optional.ofNullable(k.getPrice()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }
            return BigDecimal.ZERO;
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }


    /**
     * 计算优惠分摊到商品金额
     */
    public static Map<String, BigDecimal> calculationItemDiscountPrice(BigDecimal discountTotalFee, BigDecimal proportion, List<DineInItemDTO> allItems) {
        allItems = allItems.stream().filter(e -> StringUtils.isEmpty(e.getCouponCode())).collect(Collectors.toList());
        // 计算所有商品的总实付金额
        BigDecimal totalDiscountPrice = allItems.stream()
                .map(DineInItemDTO::getDiscountTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 如果总实付金额为0，返回空map
        if (totalDiscountPrice.compareTo(BigDecimal.ZERO) == 0) {
            return allItems.stream()
                    .collect(Collectors.toMap(DineInItemDTO::getGuid, item -> BigDecimal.ZERO));
        }

        // 按照实付金额比例分配优惠金额
        Map<String, BigDecimal> result = allItems.stream()
                .collect(Collectors.toMap(
                        DineInItemDTO::getGuid,
                        item -> (item.getDiscountTotalPrice().subtract(Optional.ofNullable(item.getGrouponDiscountTotalPrice()).orElse(BigDecimal.ZERO)))
                                .multiply(proportion).setScale(2, RoundingMode.HALF_UP)
                ));
        log.info("分摊比例计算:{}", JacksonUtils.writeValueAsString(result));

        // 计算实际分摊的总金额
        BigDecimal distributedTotal = result.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算差额
        BigDecimal difference = discountTotalFee.subtract(distributedTotal);

        // 如果有差额，将差额分配给金额最大的商品
        if (difference.compareTo(BigDecimal.ZERO) != 0) {
            DineInItemDTO maxPriceItem = allItems.stream()
                    .max(Comparator.comparing(DineInItemDTO::getDiscountTotalPrice))
                    .orElse(null);

            if (maxPriceItem != null) {
                String maxPriceItemGuid = maxPriceItem.getGuid();
                result.computeIfPresent(maxPriceItemGuid, (guid, itemDiscountFee) -> itemDiscountFee.add(difference));
            }
        }
        return result;

    }

    /**
     * 计算优惠分摊到商品金额
     */
    public static Map<String, BigDecimal> calculationItemDiscountPrice(BigDecimal discountFee, List<DineInItemDTO> allItems) {
        allItems = allItems.stream().filter(e -> StringUtils.isEmpty(e.getCouponCode())).collect(Collectors.toList());
        // 计算所有商品的总实付金额
        BigDecimal totalDiscountPrice = allItems.stream()
                .map(DineInItemDTO::getDiscountTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 如果总实付金额为0，返回空map
        if (totalDiscountPrice.compareTo(BigDecimal.ZERO) == 0) {
            return allItems.stream()
                    .collect(Collectors.toMap(DineInItemDTO::getGuid, item -> BigDecimal.ZERO));
        }

        // 按照实付金额比例分配优惠金额
        Map<String, BigDecimal> result = allItems.stream()
                .collect(Collectors.toMap(
                        DineInItemDTO::getGuid,
                        item -> item.getDiscountTotalPrice()
                                .multiply(discountFee)
                                .divide(totalDiscountPrice, 2, RoundingMode.HALF_UP)
                ));

        // 计算实际分摊的总金额
        BigDecimal distributedTotal = result.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算差额
        BigDecimal difference = discountFee.subtract(distributedTotal);

        // 如果有差额，将差额分配给金额最大的商品
        if (difference.compareTo(BigDecimal.ZERO) != 0) {
            DineInItemDTO maxPriceItem = allItems.stream()
                    .max(Comparator.comparing(DineInItemDTO::getDiscountTotalPrice))
                    .orElse(null);

            if (maxPriceItem != null) {
                String maxPriceItemGuid = maxPriceItem.getGuid();
                result.computeIfPresent(maxPriceItemGuid, (guid, itemDiscountFee) -> itemDiscountFee.add(difference));
            }
        }
        return result;
    }

    /**
     * 订单价格
     *
     * @param itemInfoDTOS 订单
     * @return 价格
     */
    public PricePairDTO orderPrice(List<ItemInfoDTO> itemInfoDTOS) {
        return ObjectUtils.isEmpty(itemInfoDTOS)
                ? new PricePairDTO(BigDecimal.ZERO, BigDecimal.ZERO)
                : new PricePairDTO()
                .setOriginPrice(itemInfoDTOS.stream().map(x -> itemPrice(x).getOriginPrice()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .setMemberPrice(itemInfoDTOS.stream().map(x -> itemPrice(x).getMemberPrice()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
    }

    /**
     * 商品价格
     *
     * @param itemInfoDTO 商品
     * @return 价格
     */
    public PricePairDTO itemPrice(ItemInfoDTO itemInfoDTO) {
        Integer itemType = itemInfoDTO.getItemType();
        switch (itemType) {
            case 1:
            case 5:
                return packageItemPrice(itemInfoDTO);
            case 3:
                return weightItemPrice(itemInfoDTO);
            case 2:
            case 4:
                return nonWeightItemPrice(itemInfoDTO);
        }
        return new PricePairDTO(BigDecimal.ZERO, BigDecimal.ZERO);
    }

    public static PricePairDTO itemPrice(DineInItemDTO dineInItemDTO) {
        Integer itemType = dineInItemDTO.getItemType();
        switch (itemType) {
            case 1:
                return packageItemPrice(dineInItemDTO);
            case 3:
                return weightItemPrice(dineInItemDTO);
            case 2:
            case 4:
                return nonWeightItemPrice(dineInItemDTO);
            default:
                return new PricePairDTO(BigDecimal.ZERO, BigDecimal.ZERO);
        }
    }

    /**
     * pad没有称重
     *
     * @param padDineInItemDTO pad购物车商品
     * @return 价格
     */
    public static PadPriceRespDTO itemPrice(PadDineInItemDTO padDineInItemDTO) {
        Integer itemType = padDineInItemDTO.getItemType();
        switch (itemType) {
            case 1:
                return packageItemPrice(padDineInItemDTO);
            case 2:
            case 4:
                return nonWeightItemPrice(padDineInItemDTO);
            default:
                return new PadPriceRespDTO(BigDecimal.ZERO, BigDecimal.ZERO);
        }
    }

    private static BigDecimal attrTotalPrice(List<ItemAttrDTO> itemAttrDTOS) {
        return CollectionUtils.isEmpty(itemAttrDTOS)
                ? BigDecimal.ZERO
                : itemAttrDTOS.stream().map(x -> Optional.ofNullable(x.getAttrPrice())
                .orElse(BigDecimal.ZERO)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * @param dineInItemDTO 商品
     * @return 非称重计算
     */
    private static PricePairDTO nonWeightItemPrice(DineInItemDTO dineInItemDTO) {
        BigDecimal memberPrice = dineInItemDTO.getMemberPrice();
        BigDecimal price = dineInItemDTO.getPrice();
        BigDecimal attrTotalPrice = attrTotalPrice(dineInItemDTO.getItemAttrDTOS());
        BigDecimal originPrice = dineInItemDTO.getCurrentCount().multiply(price.add(attrTotalPrice));
        BigDecimal preferencePrice = memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0
                ? dineInItemDTO.getCurrentCount().multiply(memberPrice.add(attrTotalPrice))
                : BigDecimal.ZERO;
        return new PricePairDTO(originPrice, preferencePrice);
    }

    /**
     * 非称重计算
     *
     * @param padDineInItemDTO pad商品
     * @return pad价格实体
     */
    private static PadPriceRespDTO nonWeightItemPrice(PadDineInItemDTO padDineInItemDTO) {
        BigDecimal memberPrice = padDineInItemDTO.getMemberPrice();
        BigDecimal attrTotalPrice = attrTotalPrice(padDineInItemDTO.getItemAttrDTOS());
        BigDecimal originPrice = padDineInItemDTO.getCurrentCount().multiply(padDineInItemDTO.getPrice().add(attrTotalPrice));
        BigDecimal preferencePrice = memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0
                ? originPrice.subtract(padDineInItemDTO.getCurrentCount().multiply(memberPrice.add(attrTotalPrice)))
                : BigDecimal.ZERO;
        return new PadPriceRespDTO(originPrice, preferencePrice);
    }

    private static PricePairDTO weightItemPrice(DineInItemDTO dineInItemDTO) {
        BigDecimal memberPrice = dineInItemDTO.getMemberPrice();
        BigDecimal price = dineInItemDTO.getPrice();
        BigDecimal attrTotalPrice = attrTotalPrice(dineInItemDTO.getItemAttrDTOS());

        BigDecimal originPrice = dineInItemDTO.getCurrentCount().multiply(price).add(attrTotalPrice);
        BigDecimal preferencePrice = memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0
                ? dineInItemDTO.getCurrentCount().multiply(memberPrice).add(attrTotalPrice)
                : BigDecimal.ZERO;
        return new PricePairDTO(originPrice, preferencePrice);
    }

    private static PricePairDTO packageItemPrice(DineInItemDTO dineInItemDTO) {
        BigDecimal memberPrice = dineInItemDTO.getMemberPrice();
        BigDecimal salePrice = dineInItemDTO.getPrice();


        List<SubDineInItemDTO> collect = dineInItemDTO.getPackageSubgroupDTOS().stream()
                .flatMap(x -> x.getSubDineInItemDTOS().stream())
                .collect(Collectors.toList());

        BigDecimal attrFee = subAttrFee2(collect);

        BigDecimal preferencePrice = memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0
                ? dineInItemDTO.getCurrentCount().multiply(memberPrice.add(attrFee))
                : BigDecimal.ZERO;
        BigDecimal originPrice = dineInItemDTO.getCurrentCount().multiply(salePrice.add(attrFee));
        return new PricePairDTO(originPrice, preferencePrice);
    }

    /**
     * 套餐商品的价格处理
     *
     * @param dineInItemDTO 套餐商品
     * @return 价格实体
     */
    private static PadPriceRespDTO packageItemPrice(PadDineInItemDTO dineInItemDTO) {
        BigDecimal memberPrice = dineInItemDTO.getMemberPrice();

        List<SubDineInItemDTO> subDineInItemDTOList = dineInItemDTO.getPackageSubgroupDTOS().stream()
                .flatMap(x -> x.getSubDineInItemDTOS().stream())
                .collect(Collectors.toList());

        // pad的套餐没有属性，但是有子菜加价
        BigDecimal attrFee = BigDecimal.ZERO;
        BigDecimal addPrice = subDineInItemDTOList.stream()
                .map(sub -> sub.getCurrentCount().multiply(sub.getAddPrice()))
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);

        BigDecimal originPrice = dineInItemDTO.getCurrentCount()
                .multiply(dineInItemDTO.getPrice().add(attrFee).add(addPrice));
        BigDecimal preferencePrice = memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0
                ? originPrice.subtract(dineInItemDTO.getCurrentCount().multiply(memberPrice.add(attrFee).add(addPrice)))
                : BigDecimal.ZERO;
        return new PadPriceRespDTO(originPrice, preferencePrice);
    }

    private static BigDecimal subAttrFee2(List<SubDineInItemDTO> subDineInItemDTOS) {
        return ObjectUtils.isEmpty(subDineInItemDTOS)
                ? BigDecimal.ZERO
                : subDineInItemDTOS.stream().map(x -> {
            BigDecimal itemNum = x.getPackageDefaultCount();
            BigDecimal defaultNum = x.getCurrentCount();
            BigDecimal attrTotalPrice = attrTotalPrice(x.getItemAttrDTOS());
            return attrTotalPrice.multiply(
                    defaultNum.multiply(x.getItemType() == 3 ? BigDecimal.ONE : itemNum)
                            .add(x.getAddPrice().multiply(defaultNum)));
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * 非称重商品计算
     *
     * @param itemInfoDTO 商品
     * @return 价格
     */
    private PricePairDTO nonWeightItemPrice(ItemInfoDTO itemInfoDTO) {
        ItemInfoSkuDTO uckSku = getUckSku(itemInfoDTO);
        BigDecimal memberPrice = uckSku.getMemberPrice();
        BigDecimal salePrice = uckSku.getSalePrice();
        BigDecimal originPrice = itemInfoDTO.getCurrentCount().multiply(salePrice.add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList())));
        BigDecimal preferencePrice = memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0
                ? itemInfoDTO.getCurrentCount().multiply(memberPrice.add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList())))
                : BigDecimal.ZERO;
        return new PricePairDTO(originPrice, preferencePrice);
    }

    /**
     * 称重商品计算
     *
     * @param itemInfoDTO 商品
     * @return 价格
     */
    private PricePairDTO weightItemPrice(ItemInfoDTO itemInfoDTO) {
        ItemInfoSkuDTO uckSku = getUckSku(itemInfoDTO);
        BigDecimal memberPrice = uckSku.getMemberPrice();
        BigDecimal salePrice = uckSku.getSalePrice();

        BigDecimal originPrice = itemInfoDTO.getCurrentCount().multiply(salePrice).add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList()));
        BigDecimal preferencePrice = memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0
                ? itemInfoDTO.getCurrentCount().multiply(memberPrice).add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList()))
                : BigDecimal.ZERO;
        return new PricePairDTO(originPrice, preferencePrice);
    }

    /**
     * 套餐计算
     *
     * @param itemInfoDTO 商品
     * @return 价格
     */
    private PricePairDTO packageItemPrice(ItemInfoDTO itemInfoDTO) {
        ItemInfoSkuDTO uckSku = getUckSku(itemInfoDTO);
        BigDecimal memberPrice = uckSku.getMemberPrice();
        BigDecimal salePrice = uckSku.getSalePrice();
        List<ItemInfoSubgroupDTO> subgroupList = itemInfoDTO.getSubgroupList();
        List<ItemInfoSubSkuDTO> collect = subgroupList.stream().flatMap(x -> x.getSubItemSkuList().stream()).collect(Collectors.toList());
        BigDecimal bigDecimal = subAttrFee(collect);

        BigDecimal preferencePrice = memberPrice.compareTo(BigDecimal.ZERO) > 0
                ? itemInfoDTO.getCurrentCount().multiply(memberPrice.add(bigDecimal))
                : BigDecimal.ZERO;
        return new PricePairDTO(itemInfoDTO.getCurrentCount().multiply(salePrice.add(bigDecimal))
                , preferencePrice);
    }

    /**
     * 套餐子项属性加价
     *
     * @param itemInfoSubSkuDTOS 套餐子项商品
     * @return 价格
     */
    public BigDecimal subAttrFee(List<ItemInfoSubSkuDTO> itemInfoSubSkuDTOS) {
        return ObjectUtils.isEmpty(itemInfoSubSkuDTOS)
                ? BigDecimal.ZERO
                : itemInfoSubSkuDTOS.stream().map(x -> {
            BigDecimal itemNum = x.getItemNum();
            Integer defaultNum = x.getDefaultNum();
            BigDecimal attrTotalPrice = getAttrTotalPrice(x.getAttrGroupList());
            if (itemNum == null) {
                itemNum = BigDecimal.ONE;
            }
            return attrTotalPrice.multiply(new BigDecimal(defaultNum))
                    .multiply(x.getItemType() == 3 ? BigDecimal.ONE : itemNum)
                    .add(x.getAddPrice().multiply(new BigDecimal(defaultNum)));
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    public ItemInfoSkuDTO getUckSku(ItemInfoDTO itemInfoDTO) {
        List<ItemInfoSkuDTO> skuList = itemInfoDTO.getSkuList();
        Assert.isTrue(!ObjectUtils.isEmpty(skuList), "规格不能为空");
        if (skuList.size() == 1) {
            return skuList.get(0);
        }
        Optional<ItemInfoSkuDTO> first = skuList.stream().filter(x -> x.getUck() == 1).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        log.error("商品:{}", JacksonUtils.writeValueAsString(itemInfoDTO));
        throw new RuntimeException("规格必选");
    }

    /**
     * pad购物车商品价格计算
     *
     * @param dineItemDTOList pad购物车商品
     * @return 价格实体
     */
    public PadPriceRespDTO shopCarPrice(List<PadDineInItemDTO> dineItemDTOList) {
        if (CollectionUtils.isEmpty(dineItemDTOList)) {
            return new PadPriceRespDTO(BigDecimal.ZERO, BigDecimal.ZERO);
        }
        BigDecimal originPrice = dineItemDTOList.stream()
                .map(x -> itemPrice(x).getOriginPrice())
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        BigDecimal memberPrice = dineItemDTOList.stream()
                .map(x -> itemPrice(x).getMemberPrice())
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        return new PadPriceRespDTO().setOriginPrice(originPrice).setMemberPrice(memberPrice);
    }

    private final MemberTerminalClientService memberTerminalClientService;

    private final ItemClientService itemClientService;

    public List<RequestDishInfo> dealwithVolume(DiscountContext context) {
        List<RequestDishInfo> dishInfoDTOS = CommonUtil.dineInItem2DishList(context.getAllItems());
        // 处理会员价
        Map<String, DineInItemDTO> itemDTOMap = context.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getItemGuid, Function.identity(), (v1, v2) -> v2));
        dealWithRequestDishInfoByLadder(BigDecimalUtil.nonNullValue(context.getThirdActivityDiscountFee()), dishInfoDTOS, itemDTOMap);
//        dealWithRequestDishInfo(thirdActivityDiscountFee, dishInfoDTOS);
        List<RequestDishInfo> dishInfoDTOList = new ArrayList<>();
        if (CommonUtil.hasGuid(context.getOrderDO().getMemberConsumptionGuid())) {
            RequestVolumeCalculateAgain volumeCalculateAgainDTO = new RequestVolumeCalculateAgain();

            //again接口需要传oldMemberConsumptionGuid，反结账用之前的memberConsumptionGuid
            volumeCalculateAgainDTO.setMemberConsumptionGuid(context.getOrderDO().getMemberConsumptionGuid());
            volumeCalculateAgainDTO.setHasMemberPrice(context.isCanMemberPrice());
            //本期优惠券为会员折扣中的第一位
            volumeCalculateAgainDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            volumeCalculateAgainDTO.setRequestDishInfoList(dishInfoDTOS);
            volumeCalculateAgainDTO.setMemberInfoGuid(context.getOrderDetailRespDTO().getMemberGuid());
            log.warn("3.会员优惠券进入重新验券逻辑，入参：{}", JacksonUtils.writeValueAsString(volumeCalculateAgainDTO));

            xx90(dishInfoDTOS);

            ResponseVolumeCalculateAgain volumeCalculateAgainRespDTO =
                    memberTerminalClientService.calculateAgain(volumeCalculateAgainDTO);
            dishInfoDTOList = volumeCalculateAgainRespDTO.getRequestDishInfoList();

            log.warn("3.会员优惠券重新验券返回结果：{}", volumeCalculateAgainRespDTO);
            context.getDiscountRuleBO().setFristMemberDiscountClient(false);
//                orderDO.setMemberConsumptionGuid(volumeCalculateAgainRespDTO.getMemberConsumptionGuid());
        }

        List<String> volumeCodes = context.getBillCalculateReqDTO().getVolumeCodes();
        if (volumeCodes == null) {
            volumeCodes = new ArrayList<>();
        }
        if (StringUtils.isNotEmpty(context.getBillCalculateReqDTO().getVolumeCode())) {
            volumeCodes.add(context.getBillCalculateReqDTO().getVolumeCode());
        }
        boolean hasVolumeCode = CollectionUtil.isNotEmpty(volumeCodes);
        if (!hasVolumeCode) {
            return dishInfoDTOList;
        }

        //用最新memberConsumptionGuid进行验券和撤销
        if (context.getBillCalculateReqDTO().getVerify() == 1 || context.getBillCalculateReqDTO().getVerify() == 3) {
            RequestVolumeConsume volumeConsumeReqDTO = new RequestVolumeConsume();
            volumeConsumeReqDTO.setVolumeCode(context.getBillCalculateReqDTO().getVolumeCode());
            volumeConsumeReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            volumeConsumeReqDTO.setHasMemberPrice(context.isCanMemberPrice());
            if (StringUtils.isNotEmpty(context.getOrderDO().getMemberConsumptionGuid()) && !"0".equals(context.getOrderDO()
                    .getMemberConsumptionGuid())) {
                volumeConsumeReqDTO.setMemberConsumptionGuid(context.getOrderDO().getMemberConsumptionGuid());
            }

            volumeConsumeReqDTO.setVolumeCodes(volumeCodes);
            volumeConsumeReqDTO.setMemberInfoGuid(context.getOrderDO().getMemberGuid());
            volumeConsumeReqDTO.setMemberName(context.getOrderDO().getMemberName());
            volumeConsumeReqDTO.setOrderNumber(String.valueOf(context.getOrderDO().getGuid()));
            volumeConsumeReqDTO.setRequestDishInfoList(dishInfoDTOS);
            log.warn("3.会员优惠券进入验券逻辑，VolumeCode：{}", context.getBillCalculateReqDTO().getVolumeCode());
            if (context.getBillCalculateReqDTO().getVerify() == 1) {
                xx90(dishInfoDTOS);

                ResponseVolumeConsume consume = memberTerminalClientService.consume(volumeConsumeReqDTO);
                dishInfoDTOList = consume.getRequestDishInfoList();
                log.warn("3.会员优惠券验券返回结果：{}", consume);
                context.getDiscountRuleBO().setFristMemberDiscountClient(false);
                context.getOrderDO().setMemberConsumptionGuid(consume.getMemberConsumptionGuid());
            } else {
                //走查询的接口，但是不验券
                RequestVolumeCalculate volumeCalculateReq = new RequestVolumeCalculate();
                volumeCalculateReq.setVolumeCode(context.getBillCalculateReqDTO().getVolumeCode());
                volumeCalculateReq.setStoreGuid(UserContextUtils.getStoreGuid());
                volumeCalculateReq.setDishInfoDTOList(dishInfoDTOS);
                volumeCalculateReq.setHasMemberPrice(context.isCanMemberPrice());
                ResponseVolumeCalculate query = memberTerminalClientService
                        .calculate(context.getBillCalculateReqDTO().getVolumeCode(), volumeCalculateReq);
                //将错误原因放入返回值中，供微信使用
                context.getOrderDetailRespDTO().setTip(query.getTip());
                dishInfoDTOList = query.getDishInfoDTOList();
                log.warn("3.会员优惠券验券返回结果：{}", query);
                context.getDiscountRuleBO().setFristMemberDiscountClient(false);
                context.getOrderDO().setMemberConsumptionGuid(query.getMemberConsumptionGuid());
            }

        }
        if (context.getBillCalculateReqDTO().getVerify() == 2) {
            RequestVolumeCancel cancelReqDTO = new RequestVolumeCancel();
            cancelReqDTO.setVolumeCode(context.getBillCalculateReqDTO().getVolumeCode());
            cancelReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            cancelReqDTO.setMemberConsumptionGuid(context.getOrderDO().getMemberConsumptionGuid());
            cancelReqDTO.setNum(1);
            cancelReqDTO.setRequestDishInfoList(dishInfoDTOS);
            log.warn("3.会员优惠券进入撤销验券逻辑，VolumeCode：{}", context.getBillCalculateReqDTO().getVolumeCode());
            List<RequestDishInfo> cancel = memberTerminalClientService
                    .cancel(context.getBillCalculateReqDTO().getVolumeCode(), cancelReqDTO);
            dishInfoDTOList = cancel;
            log.warn("3.会员优惠券撤销验券返回结果：{}", cancel);
        }

        return dishInfoDTOList;
    }

    public void xx90(List<RequestDishInfo> dishInfoDTOS) {
        // 查询菜谱和规格的父对象
        List<String> dishGuidList = dishInfoDTOS.stream().map(RequestDishInfo::getDishGuid).collect(Collectors.toList());
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(dishGuidList);
        List<ItemInfoRespDTO> itemInfoRespDTOList = itemClientService.selectItems(listDTO);
        Map<String, ItemInfoRespDTO> itemInfoRespDTOMap = itemInfoRespDTOList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, a -> a, (k1, k2) -> k1));
        for (RequestDishInfo dishInfo : dishInfoDTOS) {
            String dishGuid = dishInfo.getDishGuid();
            String dishSpecification = dishInfo.getDishSpecification();
            ItemInfoRespDTO itemInfoRespDTO = itemInfoRespDTOMap.get(dishGuid);
            dishInfo.setParentDishGuid(itemInfoRespDTO.getParentGuid());

            List<SkuInfoRespDTO> skuList = itemInfoRespDTO.getSkuList();
            for (SkuInfoRespDTO skuInfoRespDTO : skuList) {

                if (dishSpecification.equals(skuInfoRespDTO.getSkuGuid())) {
                    dishInfo.setParentDishSpecification(skuInfoRespDTO.getParentGuid());
                    break;
                }
            }
        }
    }

    public void dealWithRequestDishInfoByLadder(BigDecimal thirdActivityDiscountFee, List<RequestDishInfo> requestDishInfos,
                                                Map<String, DineInItemDTO> itemDTOMap) {
        BigDecimal totalPrice = requestDishInfos.stream()
                .map(dish -> dish.getDishOriginalUnitPrice().multiply(dish.getDishNum().subtract(dish.getGiftDishNum())
                        .compareTo(BigDecimal.ZERO) > 0 ? dish.getDishNum().subtract(dish.getGiftDishNum()) : BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        for (RequestDishInfo dishInfo : requestDishInfos) {
            if (ObjectUtils.isEmpty(dishInfo.getDishMemberPrice())) {
                log.warn("商品会员价为空");
                continue;
            }
            DineInItemDTO dineInItemDTO = itemDTOMap.get(dishInfo.getDishGuid());
            if (!ObjectUtils.isEmpty(dineInItemDTO) &&
                    Objects.equals(ItemPriceChangeEnum.PRICE_CHANGE.getCode(), dineInItemDTO.getPriceChangeType())) {
                log.warn("商品改价，与会员价互斥，因此这里让会员价等于支付价就行");
                continue;
            }
            // 新设置一个支付价，避免受到前面计算的影响
            BigDecimal dishNum = dishInfo.getDishNum().subtract(dishInfo.getGiftDishNum()).compareTo(BigDecimal.ZERO)
                    > 0 ? dishInfo.getDishNum().subtract(dishInfo.getGiftDishNum()) : BigDecimal.ZERO;
            BigDecimal payPrice = dishInfo.getDishOriginalUnitPrice().multiply(dishNum);

            // 会员价优惠金额
            BigDecimal gapFee = dishInfo.getDishOriginalUnitPrice().subtract(dishInfo.getDishMemberPrice())
                    .multiply(dishInfo.getDishNum());
            BigDecimal itemDiscountMoney = BigDecimalUtil.multiply2(thirdActivityDiscountFee,
                    payPrice.divide(totalPrice, 2, RoundingMode.HALF_DOWN));
            if (payPrice.compareTo(itemDiscountMoney.add(gapFee)) > 0) {
                dishInfo.setDishMemberPrice(payPrice.subtract(itemDiscountMoney.add(gapFee)));
            } else {
                dishInfo.setDishMemberPrice(BigDecimal.ZERO);
            }

        }
    }

    public void splitGroupBuyDiscount(DineinOrderDetailRespDTO orderDetail) {
        orderDetail.getDiscountFeeDetailDTOS().forEach(g -> {
            if (GroupBuyTypeEnum.CODE_LIST.contains(g.getDiscountType()) && g.getDiscountFee().compareTo(BigDecimal.ZERO) > 0) {
                g.setDiscountName(GroupBuyTypeEnum.getDesc(g.getDiscountType()) + DiscountTypeConstants.DISCOUNT);
            }
        });
    }

    public void dealwithVolumeByDiscount(DiscountContext context, List<RequestDishInfo> dishInfoDTOList) {
        if (CollectionUtil.isNotEmpty(dishInfoDTOList)) {
            Map<String, DineInItemDTO> orderItemDTOAllMap = context.getAllItems().stream()
                    .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (entity1, entity2) -> entity1));
            dishInfoDTOList.forEach(dish -> {
                if (dish.getDiscountMoney() != null) {
                    DineInItemDTO dineInItemDTO = orderItemDTOAllMap.get(dish.getOrderItemGuid());
                    if (dineInItemDTO != null && dish.getDiscountMoney() != null && dish.getDiscountMoney()
                            .floatValue() > 0) {
                        dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(dish.getDiscountMoney()));
                    }
                }
            });
        }
    }
}
