package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.business.brand.BrandConfigDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingQueryDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.config.resp.DineFoodSettingRespDTO;
import com.holderzone.saas.store.dto.config.resp.FinishFoodRespDTO;
import com.holderzone.saas.store.dto.order.request.AutoMarkReqDTO;
import com.holderzone.saas.store.dto.order.response.AutoMarkRespDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeQueryDTO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillClientService
 * @date 2018/09/06 19:33
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = BusinessClientService.FallBack.class)
public interface BusinessClientService {

    @PostMapping("/system/getAll/{storeGuid}")
    List<SystemDiscountDTO> getSystemDiscount(@PathVariable("storeGuid") String storeGuid);

    @PostMapping("surcharge/list_by_area_store/{storeGuid}")
    Map<String, List<SurchargeLinkDTO>> listByAreaStoreGuid(@PathVariable("storeGuid") String storeGuid, @RequestBody List<String> areaList);


    @PostMapping("surcharge/list_by_area_guid")
    Map<String, List<SurchargeLinkDTO>> listByAreaGuid(@RequestBody List<String> areaList);

    /**
     * 查询自动号牌设置
     */
    @PostMapping("/storeConfig/auto_mark/query")
    AutoMarkRespDTO queryAutoMarkResp(@RequestBody AutoMarkReqDTO autoMarkReqDTO);

    @ApiOperation(value = "查询正餐设置")
    @PostMapping("/storeConfig/dine_food_setting/query")
    DineFoodSettingRespDTO queryDineFoodSetting(@RequestBody StoreConfigQueryDTO configQueryDTO);

    @ApiOperation(value = "查询出餐设置", notes = "查询出餐设置")
    @ApiImplicitParam(name = "configQueryDTO", value = "configQueryDTO", required = true, dataType = "StoreConfigQueryDTO")
    @PostMapping("/storeConfig/finish_food/query")
    FinishFoodRespDTO queryFinishFood(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO);

    @PostMapping("pay/type/getAll/android")
    List<PaymentTypeDTO> getPaymentTypes(@RequestBody PaymentTypeQueryDTO paymentTypeQueryDTO);

    /**
     * 查询商品打印顺序配置
     */
    @PostMapping("/storeConfig/print_item_order/query")
    String queryPrintItemOrderConfig(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO);

    @PostMapping(value = "/data_setting/find_data_setting")
    List<DataSettingDTO> findDataSetting(@RequestBody @Valid DataSettingQueryDTO dataSettingQueryDTO);

    @ApiOperation(value = "根据门店GUID查询品牌配置")
    @GetMapping("/brand_config/get_by_store/{storeGuid}")
    BrandConfigDTO getBrandConfigByStoreGuid(@PathVariable("storeGuid") String storeGuid);

    @Component
    class FallBack implements FallbackFactory<BusinessClientService> {
        private static final Logger logger = LoggerFactory.getLogger(FallBack.class);

        @Override
        public BusinessClientService create(Throwable throwable) {
            return new BusinessClientService() {


                @Override
                public List<SystemDiscountDTO> getSystemDiscount(String storeGuid) {
                    logger.error("e={}", throwable.getMessage());
                    throw new ParameterException("异常");
                }

                @Override
                public Map<String, List<SurchargeLinkDTO>> listByAreaStoreGuid(String storeGuid, List<String> areaList) {
                    logger.error("根据门店获取附加费规则异常，e={}", throwable.getMessage());
                    throw new ParameterException("获取附加费规则异常");
                }

                @Override
                public Map<String, List<SurchargeLinkDTO>> listByAreaGuid(List<String> areaList) {
                    logger.error("获取附加费规则异常，e={}", throwable.getMessage());
                    throw new ParameterException("获取附加费规则异常");
                }

                @Override
                public AutoMarkRespDTO queryAutoMarkResp(AutoMarkReqDTO autoMarkReqDTO) {
                    logger.error("查询自动号牌设置异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询自动号牌设置异常");
                }

                @Override
                public DineFoodSettingRespDTO queryDineFoodSetting(StoreConfigQueryDTO configQueryDTO) {
                    logger.error("查询正餐设置异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询正餐设置异常");
                }

                @Override
                public FinishFoodRespDTO queryFinishFood(StoreConfigQueryDTO configQueryDTO) {
                    logger.error("查询出餐设置异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询出餐设置异常");
                }

                @Override
                public List<PaymentTypeDTO> getPaymentTypes(PaymentTypeQueryDTO paymentTypeQueryDTO) {
                    logger.error("查询门店支付配置异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询门店支付配置异常");
                }

                @Override
                public String queryPrintItemOrderConfig(StoreConfigQueryDTO configQueryDTO) {
                    logger.error("查询商品打印顺序配置异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询商品打印顺序配置");
                }

                @Override
                public List<DataSettingDTO> findDataSetting(DataSettingQueryDTO dataSettingQueryDTO) {
                    logger.error("查询数据取值设置异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询数据取值设置");
                }

                @Override
                public BrandConfigDTO getBrandConfigByStoreGuid(String storeGuid) {
                    logger.error("查询品牌配置异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询品牌配置");
                }

            };
        }
    }


}
