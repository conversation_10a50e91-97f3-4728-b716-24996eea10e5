package com.holderzone.saas.store.trade.manager;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestPayInfo;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestRefundPay;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.business.brand.BrandConfigDTO;
import com.holderzone.saas.store.dto.deposit.req.DepositDish;
import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.OrderRefundReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.pay.AggRefundReqDTO;
import com.holderzone.saas.store.dto.pay.AggRefundRespDTO;
import com.holderzone.saas.store.dto.pay.SaasAggRefundDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRefundDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitRecordSaveReqDTO;
import com.holderzone.saas.store.dto.trade.TransactionRecordDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.RefundTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.bo.RefundableItemBO;
import com.holderzone.saas.store.trade.entity.bo.RefundOrderBO;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.*;
import com.holderzone.saas.store.trade.helper.BillVerifyHelper;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.repository.external.LudouMemberExternalService;
import com.holderzone.saas.store.trade.repository.feign.*;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.*;
import com.holderzone.saas.store.trade.service.converter.GrouponConverter;
import com.holderzone.saas.store.trade.transform.TransactionRecordTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CommonUtil;
import com.holderzone.saas.store.trade.utils.DateUtil;
import com.holderzone.saas.store.trade.utils.ItemUtil;
import joptsimple.internal.Strings;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 订单退款 业务实现类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderRefundManager {

    private final DineInService dineInService;

    private final OrderService orderService;

    private final OrderExtendsService orderExtendsService;

    private final OrderItemService orderItemService;

    private final OrderItemExtendsService orderItemExtendsService;

    private final OrderItemChangesService orderItemChangesService;

    private final OrderItemRecordService orderItemRecordService;

    private final AppendFeeMpService appendFeeMpService;

    private final FreeReturnItemService freeReturnItemService;

    private final ItemAttrService itemAttrService;

    private final TransactionRecordService transactionRecordService;

    private final TransactionRecordService transactionRecordService2;

    private final OrderRefundRecordService orderRefundRecordService;

    private final GrouponMpService grouponMpService;

    private final DebtUnitRecordService debtUnitRecordService;

    private final StoreClientService storeClientService;

    private final ItemClientService itemClientService;

    private final GroupClientService groupClientService;

    private final MemberTerminalClientService memberTerminalClientService;

    private final AggPayClientService aggPayClientService;

    private final ReserveClientService reserveClientService;

    private final DineInPrintService dineInPrintService;

    private final DynamicHelper dynamicHelper;

    private final IMultipleTransactionRecordService multipleTransactionRecordService;

    private final LudouMemberExternalService ludouMemberExternalService;

    private final ExecuteStockReduceService executeStockReduceService;

    private final OrderRefundPrintService orderRefundPrintService;

    private final BusinessClientService businessClientService;

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Value("${erp.host}")
    private String erpHost;

    private static final String REFUND_MARK = "T";

    private static final String RECOVERY_PREFIX = "F1";

    public DineinOrderDetailRespDTO getAvailableRefundDetail(String orderGuid) {
        // 查询订单
        DineinOrderDetailRespDTO orderInfo = dineInService.getOrderDetails(orderGuid);
        log.info("查询订单详情返回:{}", JacksonUtils.writeValueAsString(orderInfo));
        if (Objects.isNull(orderInfo)) {
            throw new BusinessException(Constant.ORDER_NOT_EXIST);
        }
        // 校验订单状态
        verifyOrderStatus(orderInfo);
        // 处理并台商品列表
        combineOrderItemHandler(orderInfo);
        List<DineInItemDTO> itemList = orderInfo.getDineInItemDTOS();
        // 特殊处理单个商品的折扣
        specialSingleItemHandler(orderInfo);
        // 处理商品金额
        orderItemDiscountPriceHandler(itemList);
        // 处理商品数量
        orderItemCurrentCountHandler(itemList);
        // 处理订单可退金额
        refundAbleFeeHandler(orderInfo);
        // 处理团购验券标记是否能够退款
        refundAbleGrouponHandler(orderInfo);
        // 合并预付金退款
        handleReservePay(orderInfo);
        // 附加费明细
        List<DineInItemDTO> appendFeeItemList = buildAppendFeeItemList(orderInfo);
        if (CollectionUtils.isNotEmpty(appendFeeItemList)) {
            log.info("附加费商品:{}", JacksonUtils.writeValueAsString(appendFeeItemList));
            itemList.addAll(0, appendFeeItemList);
        }
        if (!BigDecimalUtil.greaterThanZero(orderInfo.getRefundAbleFee())) {
            throw new BusinessException(Constant.ORDER_REFUND_END);
        }
        return orderInfo;
    }

    /**
     * 合并预付金退款
     */
    private void handleReservePay(DineinOrderDetailRespDTO orderInfo) {
        if (CollectionUtils.isEmpty(orderInfo.getActuallyPayFeeDetailDTOS())) {
            return;
        }
        List<ActuallyPayFeeDetailDTO> reserveFeeDetailDTOList = orderInfo.getActuallyPayFeeDetailDTOS().stream()
                .filter(p -> Objects.equals(p.getPaymentType(), PaymentTypeEnum.RESERVE.getCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(reserveFeeDetailDTOList)) {
            List<ActuallyPayFeeDetailDTO> payFeeDetailDTOList = orderInfo.getActuallyPayFeeDetailDTOS().stream()
                    .filter(p -> !Objects.equals(p.getPaymentType(), PaymentTypeEnum.RESERVE.getCode()))
                    .collect(Collectors.toList());
            orderInfo.setActuallyPayFeeDetailDTOS(payFeeDetailDTOList);
            // 取金额为正的那一条，这种情况应该只有一正一负两条数据，至少有一条正数
            List<ActuallyPayFeeDetailDTO> payFeeDetailDTOS = reserveFeeDetailDTOList.stream()
                    .filter(r -> BigDecimalUtil.greaterThanZero(r.getAmount()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(payFeeDetailDTOS)) {
                return;
            }
            ActuallyPayFeeDetailDTO reservePayFeeDetailDTO = payFeeDetailDTOS.get(0);
            BigDecimal totalAmount = reserveFeeDetailDTOList.stream()
                    .map(ActuallyPayFeeDetailDTO::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            reservePayFeeDetailDTO.setAmount(totalAmount);
            payFeeDetailDTOList.add(reservePayFeeDetailDTO);
        }
    }

    /**
     * 校验订单状态
     */
    private void verifyOrderStatus(DineinOrderDetailRespDTO orderInfo) {
        if (orderInfo.getState() != 2) {
            throw new BusinessException(Constant.ORDER_REFUND_STATUS_ERROR);
        }
        if (orderInfo.getRecoveryType() != RecoveryTypeEnum.GENERAL.getCode() && orderInfo.getRecoveryType() != RecoveryTypeEnum.NEW.getCode()) {
            throw new BusinessException(Constant.ORDER_REFUND_STATUS_ERROR);
        }
        if (Objects.equals(1, orderInfo.getAdjustState())) {
            throw new BusinessException(Constant.ORDER_ADJUST_REFUND_ERROR);
        }
    }

    /**
     * 处理商品数量
     */
    private void orderItemCurrentCountHandler(List<DineInItemDTO> itemList) {
        // 过滤已退商品 下单数量 + 赠菜数量 - 退款数量 = 可退数量
        itemList.removeIf(e -> e.getCurrentCount().add(e.getFreeCount()).subtract(e.getRefundCount()).compareTo(BigDecimal.ZERO) <= 0);
        for (DineInItemDTO itemDTO : itemList) {
            BigDecimal totalFreeRefundCount = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(itemDTO.getFreeItemDTOS())) {
                // 查询退款数量
                totalFreeRefundCount = itemDTO.getFreeItemDTOS().stream().map(FreeItemDTO::getRefundCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 减去赠送退款数量
                itemDTO.setFreeCount(itemDTO.getFreeCount().subtract(totalFreeRefundCount));
                // 全部退完就删除
                itemDTO.getFreeItemDTOS().removeIf(e -> e.getCount().compareTo(e.getRefundCount()) == 0);
            }
            // 商品退款数量
            BigDecimal totalCurrentRefundCount = itemDTO.getRefundCount().subtract(totalFreeRefundCount);
            itemDTO.setCurrentCount(itemDTO.getCurrentCount().subtract(totalCurrentRefundCount));
        }
    }

    /**
     * 处理退款金额
     */
    private void refundAbleFeeHandler(DineinOrderDetailRespDTO orderInfo) {
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailList = orderInfo.getActuallyPayFeeDetailDTOS();
        // 非多笔聚合支付记录
        List<ActuallyPayFeeDetailDTO> notMultipleAggPayDetails = actuallyPayFeeDetailList.stream()
                .filter(e -> !Boolean.TRUE.equals(e.getIsMultipleAggPay()))
                .collect(Collectors.toList());
        // 多笔聚合支付记录
        List<ActuallyPayFeeDetailDTO> multipleAggPayDetails = actuallyPayFeeDetailList.stream()
                .filter(e -> Boolean.TRUE.equals(e.getIsMultipleAggPay()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(multipleAggPayDetails)) {
            // 查询退款记录
            List<String> multipleAggPayGuids = multipleAggPayDetails.stream()
                    .map(ActuallyPayFeeDetailDTO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<MultipleTransactionRecordDO> multipleTransactionRecordList = multipleTransactionRecordService
                    .listByOriginalMultipleTransactionRecordGuids(multipleAggPayGuids);
            if (CollectionUtils.isNotEmpty(multipleTransactionRecordList)) {
                Map<Long, BigDecimal> multipleTransactionRecordMap = multipleTransactionRecordList.stream()
                        .collect(Collectors.groupingBy(MultipleTransactionRecordDO::getOriginalMultipleTransactionRecordGuid,
                                Collectors.mapping(MultipleTransactionRecordDO::getAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                for (ActuallyPayFeeDetailDTO multipleAggPayDetail : multipleAggPayDetails) {
                    multipleAggPayDetail.setAmount(multipleAggPayDetail.getAmount()
                            .subtract(multipleTransactionRecordMap.getOrDefault(Long.valueOf(multipleAggPayDetail.getGuid()), BigDecimal.ZERO).abs()));
                }
            }
        }
        // 查询支付方式
        List<TransactionRecordDO> transactionRecordList = transactionRecordService.listByOrderGuid(orderInfo.getGuid());
        Map<Long, TransactionRecordDO> transactionRecordMap = transactionRecordList.stream()
                .collect(Collectors.toMap(TransactionRecordDO::getGuid, Function.identity(), (key1, key2) -> key1));
        for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : notMultipleAggPayDetails) {
            TransactionRecordDO transactionRecordDO = transactionRecordMap.get(Long.valueOf(actuallyPayFeeDetailDTO.getGuid()));
            if (Objects.nonNull(transactionRecordDO)) {
                actuallyPayFeeDetailDTO.setAmount(actuallyPayFeeDetailDTO.getAmount().subtract(transactionRecordDO.getRefundAmount()));
            }
        }
        // 退款金额处理之后 过滤可退款金额小于等于0的支付方式
        actuallyPayFeeDetailList.removeIf(e -> !BigDecimalUtil.greaterThanZero(e.getAmount()));
        BigDecimal refundAbleFee = BigDecimal.ZERO;
        for (TransactionRecordDO recordDO : transactionRecordList) {
            refundAbleFee = refundAbleFee.add(recordDO.getAmount()).subtract(recordDO.getRefundAmount());
        }
        // 订单可退金额(所有支付方式 + 团购验券)
        orderInfo.setRefundAbleFee(refundAbleFee);
    }

    /**
     * 处理团购验券标记是否能够退款
     */
    private void refundAbleGrouponHandler(DineinOrderDetailRespDTO orderInfo) {
        orderInfo.getActuallyPayFeeDetailDTOS().forEach(e -> {
            e.setGrouponRefundableFlag(true);
            if (GroupBuyTypeEnum.ABC.getCode() == e.getPaymentType()) {
                e.setGrouponRefundableFlag(false);
            }
        });
        // 查询支付宝、抖音团购验券 验券时间是否超过1小时
        List<Integer> verifyGrouponTypeList = Lists.newArrayList(GroupBuyTypeEnum.DOU_YIN.getCode(), GroupBuyTypeEnum.ALIPAY.getCode());
        List<ActuallyPayFeeDetailDTO> grouponPaymentTypeList = orderInfo.getActuallyPayFeeDetailDTOS().stream()
                .filter(e -> verifyGrouponTypeList.contains(e.getPaymentType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(grouponPaymentTypeList)) {
            return;
        }
        Map<Integer, ActuallyPayFeeDetailDTO> grouponPaymentTypeMap = grouponPaymentTypeList.stream()
                .collect(Collectors.toMap(ActuallyPayFeeDetailDTO::getPaymentType, Function.identity(), (key1, key2) -> key1));
        List<Integer> refundGrouponTypeList = new ArrayList<>(grouponPaymentTypeMap.keySet());
        // 查询订单使用团购券
        List<GrouponDO> grouponRecordList = grouponMpService.listByOrderGuid(orderInfo.getGuid());
        // 过滤本次需要退款的团购券
        grouponRecordList = grouponRecordList.stream().filter(e -> refundGrouponTypeList.contains(e.getGrouponType())).collect(Collectors.toList());
        // 查询支付宝、抖音团购验券 验券时间是否超过1小时
        LocalDateTime now = LocalDateTime.now();
        grouponRecordList.forEach(e -> {
            if (verifyGrouponTypeList.contains(e.getGrouponType()) && e.getGmtCreate().plusHours(1).compareTo(now) < 0) {
                log.error("{}券超过1小时, groupon:{}", GroupBuyTypeEnum.getDesc(e.getGrouponType()), JacksonUtils.writeValueAsString(e));
                grouponPaymentTypeMap.get(e.getGrouponType()).setGrouponRefundableFlag(false);
            }
        });
    }


    /**
     * 处理商品金额
     */
    private void orderItemDiscountPriceHandler(List<DineInItemDTO> itemList) {
        for (DineInItemDTO itemDTO : itemList) {
            if (itemDTO.getCurrentCount().compareTo(BigDecimal.ZERO) == 0) {
                itemDTO.setPrice(BigDecimal.ZERO);
            } else {
                itemDTO.setPrice(itemDTO.getItemPrice().divide(itemDTO.getCurrentCount(), 2, RoundingMode.DOWN));
            }
            if (Objects.nonNull(itemDTO.getDiscountPrice()) && itemDTO.getDiscountPrice().compareTo(BigDecimal.ZERO) >= 0) {
                itemDTO.setPrice(itemDTO.getDiscountPrice());
            }
            List<FreeItemDTO> freeItemList = itemDTO.getFreeItemDTOS();
            if (CollectionUtils.isNotEmpty(freeItemList)) {
                for (FreeItemDTO freeItemDTO : freeItemList) {
                    freeItemDTO.setPrice(BigDecimal.ZERO);
                }
            }
        }
    }

    /**
     * 处理并台商品
     */
    private void combineOrderItemHandler(DineinOrderDetailRespDTO orderInfo) {
        List<DineinOrderDetailRespDTO> subOrderDetailList = orderInfo.getSubOrderDetails();
        if (CollectionUtils.isEmpty(subOrderDetailList)) {
            return;
        }
        List<DineInItemDTO> subItemList = subOrderDetailList.stream().flatMap(e -> e.getDineInItemDTOS().stream()).collect(Collectors.toList());
        orderInfo.getDineInItemDTOS().addAll(subItemList);
    }

    /**
     * 特殊处理单个商品的折扣
     */
    private void specialSingleItemHandler(DineinOrderDetailRespDTO orderInfo) {
        orderInfo.setSingleItemFlag(false);
        List<DineInItemDTO> itemList = orderInfo.getDineInItemDTOS();
        if (itemList.size() != 1) {
            return;
        }
        DineInItemDTO singleItemDTO = itemList.get(0);
        // 称重商品不校验数量
        if (ItemTypeEnum.WEIGH.getCode() != singleItemDTO.getItemType() && singleItemDTO.getCurrentCount().add(singleItemDTO.getFreeCount()).compareTo(BigDecimal.ONE) > 0) {
            // 非称重商品
            return;
        }
        OrderFeeDetailDTO orderFeeDetail = orderInfo.getOrderFeeDetailDTO();
        if (Objects.nonNull(orderFeeDetail) && CollectionUtils.isNotEmpty(orderFeeDetail.getAppendFeeDetailDTOS())) {
            return;
        }
        // 处理订单只有一份商品的情况， 标记此商品是唯一商品
        orderInfo.setSingleItemFlag(true);
        // 1.处理使用在订单上的团购验券
        // 查询订单使用的团购验券
        List<GrouponDO> grouponList = grouponMpService.listByOrderGuid(orderInfo.getGuid());
        if (StringUtils.isBlank(singleItemDTO.getCouponCode()) && CollectionUtils.isNotEmpty(grouponList)) {
            log.warn("单个商品使用到了整单上的团购验券, orderGuid:{},券信息:{}", orderInfo.getGuid(), JacksonUtils.writeValueAsString(grouponList));
            singleItemDTO.setCouponInfos(grouponList.stream().map(e -> {
                MtCouponPreRespDTO couponPreRespDTO = new MtCouponPreRespDTO();
                couponPreRespDTO.setDeductionAmount(e.getDeductionAmount());
                couponPreRespDTO.setGroupBuyType(e.getGrouponType());
                couponPreRespDTO.setCouponCode(e.getCode());
                return couponPreRespDTO;
            }).collect(Collectors.toList()));
        }
    }

    /**
     * 订单退款
     */
    @Transactional(rollbackFor = Exception.class)
    public String orderRefund(OrderRefundReqDTO orderRefundReqDTO) {
        // 查询订单
        DineinOrderDetailRespDTO availableRefundDetail = getAvailableRefundDetail(orderRefundReqDTO.getOrderGuid());
        log.info("查询订单详情返回:{}", JacksonUtils.writeValueAsString(availableRefundDetail));
        // 检测是否在反结账时效时间内
        checkRefundTimeLimit(availableRefundDetail);
        // 填充字段
        fillReqField(orderRefundReqDTO, availableRefundDetail);
        log.info("填充后请求入参为:{}", JacksonUtils.writeValueAsString(orderRefundReqDTO));
        // 校验订单
        verifyOrderRefund(orderRefundReqDTO, availableRefundDetail);
        log.info("校验订单后请求入参为:{}", JacksonUtils.writeValueAsString(orderRefundReqDTO));
        // 生成退款订单
        RefundOrderBO refundOrderBO = buildRefundOrderBO(orderRefundReqDTO);
        log.info("生成相关退款单:{}", JacksonUtils.writeValueAsString(refundOrderBO));
        // 持久化
        persistenceRefundOrderBiz(refundOrderBO);
        // 更新原订单
        updateOriginalOrder(orderRefundReqDTO, refundOrderBO);
        // 原路退回
        backtrack(orderRefundReqDTO, refundOrderBO.getTransactionRecordList());
        // 回退会员消费
        returnMemberPay(orderRefundReqDTO, refundOrderBO);
        // 回退麓豆支付
        returnLudouPay(orderRefundReqDTO);
        // 回退库存
        returnStock(refundOrderBO);
        // 退沽清
        returnEstimate(orderRefundReqDTO.getDineInItemDTOS());
        // 打印
        orderRefundPrintService.printByRefund(orderRefundReqDTO, refundOrderBO);
        return String.valueOf(refundOrderBO.getRefundOrderGuid());
    }

    /**
     * 填充请求DTO 参数
     */
    private void fillReqField(OrderRefundReqDTO orderRefundReqDTO, DineinOrderDetailRespDTO availableRefundDetail) {
        UserContext userContext = UserContextUtils.get();
        // 老板助手的token没有storeGuid
        if (StringUtils.isEmpty(userContext.getStoreGuid()) && StringUtils.isNotEmpty(orderRefundReqDTO.getStoreGuid())) {
            userContext.setStoreGuid(orderRefundReqDTO.getStoreGuid());
            UserContextUtils.put(userContext);
        }
        // 填充商品信息 (商品估清使用)
        fillDineInItemList(orderRefundReqDTO, availableRefundDetail);
        // 填充订单支付记录
        fillTransactionRecordList(orderRefundReqDTO);
        // 预定单guid
        orderRefundReqDTO.setReserveGuid(availableRefundDetail.getReserveGuid());
    }

    /**
     * 填充商品信息 (商品估清、删除商品缓存使用)
     */
    private void fillDineInItemList(OrderRefundReqDTO orderRefundReqDTO, DineinOrderDetailRespDTO availableRefundDetail) {
        List<DineInItemDTO> dineInItemList = orderRefundReqDTO.getDineInItemDTOS();
        if (CollectionUtils.isEmpty(dineInItemList)) {
            return;
        }
        Map<String, DineInItemDTO> originalDineInItemMap = availableRefundDetail.getDineInItemDTOS().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        // 赠送商品明细
        Map<String, String> freeItemMappingOrderGuidMap = Maps.newHashMap();
        for (DineInItemDTO dineInItemDTO : availableRefundDetail.getDineInItemDTOS()) {
            if (CollectionUtils.isNotEmpty(dineInItemDTO.getFreeItemDTOS())) {
                for (FreeItemDTO freeItemDTO : dineInItemDTO.getFreeItemDTOS()) {
                    freeItemMappingOrderGuidMap.put(freeItemDTO.getGuid(), dineInItemDTO.getOrderGuid());
                }
            }
        }
        for (DineInItemDTO inItemDTO : dineInItemList) {
            DineInItemDTO originalItem = originalDineInItemMap.get(inItemDTO.getGuid());
            if (Objects.isNull(originalItem)) {
                // 可能是赠菜
                String freeOrderGuid = freeItemMappingOrderGuidMap.get(inItemDTO.getGuid());
                if (StringUtils.isNotEmpty(freeOrderGuid)) {
                    inItemDTO.setOrderGuid(freeOrderGuid);
                }
                continue;
            }
            inItemDTO.setOrderGuid(originalItem.getOrderGuid());
            inItemDTO.setSkuGuid(originalItem.getSkuGuid());
            inItemDTO.setSkuName(originalItem.getSkuName());
            inItemDTO.setGmtCreate(originalItem.getGmtCreate());
            inItemDTO.setPackageSubgroupDTOS(originalItem.getPackageSubgroupDTOS());
        }
    }

    /**
     * 填充订单支付记录
     */
    private void fillTransactionRecordList(OrderRefundReqDTO orderRefundReqDTO) {
        // 查询订单的支付记录
        List<TransactionRecordDO> transactionRecordList = transactionRecordService2
                .listByOrderGuids(Lists.newArrayList(Long.valueOf(orderRefundReqDTO.getOrderGuid())));
        log.info("查询原订单支付记录,orderGuid:{}, transactionRecordList:{}", orderRefundReqDTO.getOrderGuid(),
                JacksonUtils.writeValueAsString(transactionRecordList));
        List<TransactionRecordDTO> allTransactionRecordDTOList = transactionRecordList.stream().map(e -> {
            TransactionRecordDTO recordDTO = new TransactionRecordDTO();
            BeanUtils.copyProperties(e, recordDTO);
            return recordDTO;
        }).collect(Collectors.toList());
        orderRefundReqDTO.setAllTransactionRecordList(allTransactionRecordDTOList);

        List<ActuallyPayFeeDetailDTO> refundTypeList = orderRefundReqDTO.getRefundTypeList();
        List<String> actuallyPayFeeDetailGuids = refundTypeList.stream().map(ActuallyPayFeeDetailDTO::getGuid)
                .collect(Collectors.toList());
        List<ActuallyPayFeeDetailDTO> aggPayRefundTypeList = refundTypeList.stream()
                .filter(e -> Boolean.TRUE.equals(e.getIsMultipleAggPay())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(aggPayRefundTypeList)) {
            // 关联transactionRecordGuid
            List<String> multipleTransactionRecordGuids = aggPayRefundTypeList.stream()
                    .map(ActuallyPayFeeDetailDTO::getTransactionRecordGuid)
                    .distinct()
                    .collect(Collectors.toList());
            actuallyPayFeeDetailGuids.addAll(multipleTransactionRecordGuids);
            // 查询多笔聚合支付使用明细
            // 关联transactionRecordGuid
            List<String> multipleRecordGuids = aggPayRefundTypeList.stream()
                    .map(ActuallyPayFeeDetailDTO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<MultipleTransactionRecordDO> multipleTransactionRecordDOList = multipleTransactionRecordService.listByIds(multipleRecordGuids);
            orderRefundReqDTO.setMultipleTransactionRecordList(TransactionRecordTransform.
                    INSTANCE.multipleRecordDO2TransactionRecordDTO(multipleTransactionRecordDOList));
        }
        // 过滤本次退款的支付记录
        transactionRecordList = transactionRecordList.stream()
                .filter(e -> actuallyPayFeeDetailGuids.contains(String.valueOf(e.getGuid())))
                .collect(Collectors.toList());
        List<TransactionRecordDTO> transactionRecordDTOList = transactionRecordList.stream().map(e -> {
            TransactionRecordDTO recordDTO = new TransactionRecordDTO();
            BeanUtils.copyProperties(e, recordDTO);
            return recordDTO;
        }).collect(Collectors.toList());
        orderRefundReqDTO.setTransactionRecordList(transactionRecordDTOList);


    }

    /**
     * 校验订单
     * 校验同时 会设置入参orderRefundReqDTO中的参数:
     * transactionRecord
     * thirdActivityTransactionRecord
     * dineInItemDTOS : originalOrderItemGuid
     */
    private void verifyOrderRefund(OrderRefundReqDTO orderRefundReqDTO, DineinOrderDetailRespDTO availableRefundDetail) {
        List<DineInItemDTO> dineInItemList = orderRefundReqDTO.getDineInItemDTOS();
        if (CollectionUtils.isEmpty(dineInItemList) && !BigDecimalUtil.greaterThanZero(orderRefundReqDTO.getRefundAmount())) {
            throw new BusinessException("退菜商品为空和退款金额不能同时为空");
        }
        // 校验订单：是否多次聚合支付
        verifyIsMultipleActualAggPay(orderRefundReqDTO, availableRefundDetail);
        // 校验订单: 退款金额是否超过实付金额
        verifyRefundAmount(orderRefundReqDTO, availableRefundDetail);
        // 校验订单: 原路退回 抖音验券、支付宝验券
        verifyCoupon(orderRefundReqDTO);
        // 校验订单: 退款菜品是否超过可退数量（现在支持为空）
        verifyRefundOrderItem(orderRefundReqDTO, availableRefundDetail);
    }

    /**
     * 校验订单：是否多次聚合支付
     */
    private void verifyIsMultipleActualAggPay(OrderRefundReqDTO orderRefundReqDTO, DineinOrderDetailRespDTO availableRefundDetail) {
        if (Boolean.TRUE.equals(availableRefundDetail.getIsMultipleActualAggPay())
                && RefundTypeEnum.BACKTRACK.getCode() == orderRefundReqDTO.getRefundType()) {
            List<ActuallyPayFeeDetailDTO> refundTypeList = Optional.ofNullable(orderRefundReqDTO.getRefundTypeList())
                    .orElse(Lists.newArrayList());
            long aggPayCount = refundTypeList.stream().filter(e -> PaymentTypeEnum.AGG.getCode() == e.getPaymentType()).count();
            if (aggPayCount > 1) {
                throw new BusinessException("该订单存在多笔聚合支付，一次只能选择一笔退款");
            }
        }
    }


    /**
     * 校验退款金额是否超过实付金额
     */
    private void verifyRefundAmount(OrderRefundReqDTO orderRefundReqDTO, DineinOrderDetailRespDTO availableRefundDetail) {
        // 线下退款不校验
        if (RefundTypeEnum.OFFLINE_REFUND.getCode() == orderRefundReqDTO.getRefundType()) {
            return;
        }

        List<ActuallyPayFeeDetailDTO> refundTypeList = orderRefundReqDTO.getRefundTypeList();
        if (CollectionUtils.isEmpty(refundTypeList)) {
            throw new BusinessException("原路返回未选择退款方式");
        }

        // 原有总金额校验
        BigDecimal payAmount = availableRefundDetail.getRefundAbleFee();
        if (payAmount.compareTo(orderRefundReqDTO.getRefundAmount()) < 0) {
            throw new BusinessException("原路返回的金额不足");
        }

        // 新增：按支付方式校验退款金额
        verifyRefundAmountByPaymentType(orderRefundReqDTO, availableRefundDetail);
    }

    /**
     * 按支付方式校验退款金额
     */
    private void verifyRefundAmountByPaymentType(OrderRefundReqDTO orderRefundReqDTO, DineinOrderDetailRespDTO availableRefundDetail) {
        List<ActuallyPayFeeDetailDTO> refundTypeList = orderRefundReqDTO.getRefundTypeList();
        Map<String, ActuallyPayFeeDetailDTO> refundRequestMap = refundTypeList.stream()
                .collect(Collectors.toMap(ActuallyPayFeeDetailDTO::getGuid, Function.identity(), (key1, key2) -> key1));

        for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : availableRefundDetail.getActuallyPayFeeDetailDTOS()) {
            ActuallyPayFeeDetailDTO detailDTO = refundRequestMap.get(actuallyPayFeeDetailDTO.getGuid());
            if (Objects.nonNull(detailDTO) && detailDTO.getAmount().compareTo(actuallyPayFeeDetailDTO.getAmount()) > 0) {
                throw new BusinessException("原路返回的金额不足");
            }
        }
    }

    /**
     * 校验订单原路退回
     * 抖音验券是否超过1小时
     * 支付宝验券是否超过1小时
     */
    private void verifyCoupon(OrderRefundReqDTO orderRefundReqDTO) {
        // 只校验原路退回
        if (orderRefundReqDTO.getRefundType() != RefundTypeEnum.BACKTRACK.getCode()) {
            return;
        }
        List<ActuallyPayFeeDetailDTO> refundTypeList = orderRefundReqDTO.getRefundTypeList();
        // 退款方式是否包含团购验券的
        List<ActuallyPayFeeDetailDTO> grouponPaymentTypeList = refundTypeList.stream()
                .filter(e -> PaymentTypeEnum.getFilterGrouponPaymentTypes().contains(e.getPaymentType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(grouponPaymentTypeList)) {
            return;
        }
        List<Integer> refundGrouponTypeList = grouponPaymentTypeList.stream()
                .map(ActuallyPayFeeDetailDTO::getPaymentType)
                .collect(Collectors.toList());
        if (refundGrouponTypeList.contains(PaymentTypeEnum.MT_GROUPON.getCode())) {
            refundGrouponTypeList.add(GroupBuyTypeEnum.MEI_TUAN.getCode());
        }
        // 查询订单使用团购券
        List<GrouponDO> grouponRecordList = grouponMpService.listByOrderGuid(orderRefundReqDTO.getOrderGuid());
        // 过滤本次需要退款的团购券
        grouponRecordList = grouponRecordList.stream()
                .filter(e -> refundGrouponTypeList.contains(e.getGrouponType()))
                .collect(Collectors.toList());
        // 农行券不能退款
        List<GrouponDO> abcGrouponRecordList = grouponRecordList.stream()
                .filter(e -> GroupBuyTypeEnum.ABC.getCode() == e.getGrouponType())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(abcGrouponRecordList)) {
            throw new BusinessException("农行验券不支持退款");
        }
        // 查询支付宝、抖音团购验券 验券时间是否超过1小时
        List<Integer> grouponTypeList = Lists.newArrayList(GroupBuyTypeEnum.DOU_YIN.getCode(), GroupBuyTypeEnum.ALIPAY.getCode());
        LocalDateTime now = LocalDateTime.now();
        grouponRecordList.forEach(e -> {
            if (grouponTypeList.contains(e.getGrouponType()) && e.getGmtCreate().plusHours(1).compareTo(now) < 0) {
                log.error("{}券超过1小时, groupon:{}", GroupBuyTypeEnum.getDesc(e.getGrouponType()), JacksonUtils.writeValueAsString(e));
                throw new BusinessException(GroupBuyTypeEnum.getDesc(e.getGrouponType()) + "券已超过1小时不可退回");
            }
        });
        // 券抵扣金额
        BigDecimal couponDeductionAmount = grouponRecordList.stream()
                .map(GrouponDO::getDeductionAmount)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        orderRefundReqDTO.setCurrentCouponDeductionAmount(couponDeductionAmount);
        // 券购买金额
        BigDecimal couponBuyAmount = grouponRecordList.stream()
                .map(GrouponDO::getCouponBuyPrice)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        List<ActuallyPayFeeDetailDTO> maitonPayments = grouponPaymentTypeList.stream()
                .filter(e -> Objects.equals(PaymentTypeEnum.MT_MAITON.getCode(), e.getPaymentType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(maitonPayments)) {
            BigDecimal maitonCouponBuyAmount = maitonPayments.stream()
                    .map(ActuallyPayFeeDetailDTO::getAmount)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
            couponBuyAmount = couponBuyAmount.add(maitonCouponBuyAmount);
        }
        orderRefundReqDTO.setCurrentCouponBuyAmount(couponBuyAmount);
    }

    /**
     * 校验退款菜品是否超过可退数量
     */
    private void verifyRefundOrderItem(OrderRefundReqDTO orderRefundReqDTO, DineinOrderDetailRespDTO availableRefundDetail) {
        // 新增：支持商品列表为空的自定义金额退款
        if (CollectionUtils.isEmpty(orderRefundReqDTO.getDineInItemDTOS())) {
            return;
        }
        // 商品列表
        List<DineInItemDTO> dineInItemList = availableRefundDetail.getDineInItemDTOS();
        Map<String, DineInItemDTO> dineInItemMap = dineInItemList.stream().collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));

        // 赠品列表
        List<FreeItemDTO> freeItemList = dineInItemList.stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getFreeItemDTOS()))
                .flatMap(e -> e.getFreeItemDTOS().stream()).collect(Collectors.toList());
        Map<String, FreeItemDTO> freeItemMap = freeItemList.stream().collect(Collectors.toMap(FreeItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));

        for (DineInItemDTO refundDineInItem : orderRefundReqDTO.getDineInItemDTOS()) {
            DineInItemDTO dineInItem = dineInItemMap.get(refundDineInItem.getGuid());
            if (Objects.isNull(dineInItem)) {
                // 可能是赠菜
                FreeItemDTO freeItemDTO = freeItemMap.get(refundDineInItem.getGuid());
                if (Objects.isNull(freeItemDTO)) {
                    log.error("退单商品不存在订单中, orderGuid:{}, dineInItem:{}", orderRefundReqDTO.getOrderGuid(), JacksonUtils.writeValueAsString(dineInItem));
                    throw new BusinessException("退单商品不存在订单中");
                }
                if (refundDineInItem.getCurrentCount().compareTo(freeItemDTO.getCount()) > 0) {
                    throw new BusinessException("退款失败：超过最大可退数量");
                }
                refundDineInItem.setOriginalOrderItemGuid(freeItemDTO.getOrderItemGuid());
            } else {
                // 商品列表
                BigDecimal availableRefundCount = dineInItem.getCurrentCount().add(dineInItem.getFreeCount());
                if (refundDineInItem.getCurrentCount().compareTo(availableRefundCount) > 0) {
                    throw new BusinessException("退款失败：超过最大可退数量");
                }
            }
        }
    }


    /**
     * 构建订单退款 业务对象
     */
    private RefundOrderBO buildRefundOrderBO(OrderRefundReqDTO orderRefundReqDTO) {
        // 查询原订单
        OrderDO orderDO = orderService.getById(orderRefundReqDTO.getOrderGuid());
        RefundOrderBO biz = new RefundOrderBO();
        // 原单
        biz.setOriginalOrder(orderDO);
        // 退款订单
        biz.setOrder(buildRefundOrderDO(orderDO, orderRefundReqDTO));
        // 退款订单guid
        Long refundOrderGuid = biz.getRefundOrderGuid();
        // 退款订单扩展
        biz.setOrderExtends(buildRefundOrderExtendsDO(orderDO, biz.getOrder()));
        // 设置退款订单guid
        orderRefundReqDTO.setRefundOrderGuid(refundOrderGuid);
        // 设置biz
        // 订单商品明细
        biz.setOrderItemList(buildRefundOrderItemDOList(orderRefundReqDTO));
        // 订单商品扩展明细
        biz.setOrderItemExtendsList(buildRefundOrderItemExtendsDOList(orderRefundReqDTO, biz.getOrderItemList()));
        // 退款单商品换菜明细
        biz.setOrderItemChangesList(buildRefundOrderItemChangesDOList(orderRefundReqDTO, biz.getOrderItemList()));
        // 订单记录明细
        biz.setOrderItemRecordList(buildRefundOrderRecordDOList(biz.getOrderItemList()));
        // 附加费明细
        biz.setAppendFeeList(buildRefundOrderAppendFeeDOList(orderRefundReqDTO));
        // 赠菜明细
        biz.setFreeItemList(buildRefundOrderFreeItemDOList(orderRefundReqDTO, biz.getOrderItemList()));
        // 退款退菜明细
        biz.setRefundItemList(buildRefundReturnItemDOList(orderRefundReqDTO, orderDO));
        // 属性明细
        biz.setItemAttrList(buildRefundOrderItemAttrDOList(orderRefundReqDTO, biz.getOrderItemList()));
        // 换菜商品属性明细
        biz.setChangeItemAttrList(buildRefundOrderItemChangeAttrList(orderRefundReqDTO, biz.getOrderItemChangesList()));
        // 交易记录明细
        biz.setTransactionRecordList(buildRefundOrderTransactionRecordDOList(orderRefundReqDTO));
        // 多笔聚合支付交易记录明细
        biz.setMultipleTransactionRecordList(buildRefundOrderMultipleTransactionRecordDOList(orderRefundReqDTO));
        // 订单退款记录明细
        biz.setOrderRefundRecord(buildOrderRefundRecordDO(biz.getOrder().getOrderNo(), orderRefundReqDTO, biz.getTransactionRecordList()));
        // 计算商品退款金额分摊
        calculateItemRefundPrice(orderRefundReqDTO, biz);
        return biz;
    }

    private void calculateItemRefundPrice(OrderRefundReqDTO orderRefundReqDTO, RefundOrderBO biz) {
        // 退单商品并且附加费均为空则无需处理
        List<OrderItemExtendsDO> orderItemExtendsDOList = biz.getOrderItemExtendsList();
        List<AppendFeeDO> appendFeeList = biz.getAppendFeeList();
        if (CollectionUtils.isEmpty(orderItemExtendsDOList) && CollectionUtils.isEmpty(appendFeeList)) {
            return;
        }
        // 退款金额为0则无需处理
        BigDecimal totalRefundAmount = orderRefundReqDTO.getRefundAmount();
        if (!BigDecimalUtil.greaterThanZero(totalRefundAmount)) {
            log.info("退单金额小于等于0, 计算商品退款金额分摊结束");
            return;
        }

        // 订单商品明细
        List<OrderItemDO> orderItemList = biz.getOrderItemList();
        Map<Long, OrderItemDO> refundOrderItemMap = orderItemList.stream()
                .collect(Collectors.toMap(OrderItemDO::getGuid, Function.identity(), (key1, key2) -> key1));

        // 请求中的退单商品
        Map<String, DineInItemDTO> dineInItemDTOMap = Optional
                .ofNullable(orderRefundReqDTO.getDineInItemDTOS())
                .map(list -> list.stream()
                        .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (existing, replacement) -> existing)))
                .orElse(Collections.emptyMap());

        // 用于存储需要参与退款分摊的条目（商品 + 附加费）
        List<RefundableItemBO> refundableItems = new ArrayList<>();

        // 处理附加费
        for (AppendFeeDO appendFee : appendFeeList) {
            DineInItemDTO dineInItemDTO = dineInItemDTOMap.get(String.valueOf(appendFee.getOriginalGuid()));
            if (Objects.isNull(dineInItemDTO)) {
                continue;
            }
            BigDecimal price = getItemPrice(dineInItemDTO);
            BigDecimal count = getItemCount(dineInItemDTO);
            BigDecimal paidAmount = price.multiply(count);

            refundableItems.add(new RefundableItemBO(appendFee, paidAmount));
        }

        // 处理订单商品
        for (OrderItemExtendsDO itemExtends : orderItemExtendsDOList) {
            OrderItemDO item = refundOrderItemMap.get(itemExtends.getGuid());
            if (Objects.isNull(item)) {
                continue;
            }
            DineInItemDTO dineInItemDTO = dineInItemDTOMap.get(String.valueOf(item.getOriginalOrderItemGuid()));
            if (Objects.isNull(dineInItemDTO)) {
                continue;
            }
            refundableItems.add(new RefundableItemBO(itemExtends, getItemPaidAmount(itemExtends, refundOrderItemMap, dineInItemDTOMap)));
        }

        if (refundableItems.isEmpty()) {
            log.info("无退款商品和附加费，计算商品退款金额分摊结束");
            return;
        }

        // 计算所有退款商品和附加费的实付金额总和
        BigDecimal totalPaidAmount = refundableItems.stream()
                .map(RefundableItemBO::getPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (!BigDecimalUtil.greaterThanZero(totalPaidAmount)) {
            throw new BusinessException("退款商品和附加费实付金额合计不能小于等于0");
        }

        // 按比例分摊退款金额
        BigDecimal allocatedAmount = BigDecimal.ZERO;
        for (int i = 0; i < refundableItems.size(); i++) {
            RefundableItemBO item = refundableItems.get(i);

            if (i == refundableItems.size() - 1) {
                // 最后一个使用差额，避免精度问题
                BigDecimal lastRefundAmount = totalRefundAmount.subtract(allocatedAmount);
                item.setRefundAmount(lastRefundAmount);
            } else {
                BigDecimal itemRefundAmount = totalRefundAmount
                        .multiply(item.getPaidAmount())
                        .divide(totalPaidAmount, 2, RoundingMode.DOWN);

                item.setRefundAmount(itemRefundAmount);
                allocatedAmount = allocatedAmount.add(itemRefundAmount);
            }
        }

        // 更新原始对象中的退款金额
        for (RefundableItemBO item : refundableItems) {
            if (item.getItem() instanceof OrderItemExtendsDO) {
                ((OrderItemExtendsDO) item.getItem()).setRefundPrice(item.getRefundAmount());
            } else if (item.getItem() instanceof AppendFeeDO) {
                ((AppendFeeDO) item.getItem()).setRefundShareAmount(item.getRefundAmount());
            }
        }

        log.info("商品和附加费退款金额分摊完成，总退款金额：{}，明细：{}",
                totalRefundAmount, JacksonUtils.writeValueAsString(refundableItems));
    }

    private List<FreeReturnItemDO> buildRefundReturnItemDOList(OrderRefundReqDTO orderRefundReqDTO, OrderDO orderDO) {
        List<DineInItemDTO> dineInItemList = orderRefundReqDTO.getDineInItemDTOS();
        if (CollectionUtils.isEmpty(dineInItemList)) {
            log.info("自定义金额退款，无退款退菜明细");
            return Collections.emptyList();
        }
        Map<String, DineInItemDTO> dineInItemMap = dineInItemList.stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        Set<String> orderItemGuids = dineInItemList.stream().map(DineInItemDTO::getGuid).collect(Collectors.toSet());
        // 查询原订单明细
        List<OrderItemDO> orderItemList = orderItemService.listByIds(orderItemGuids);
        if (CollectionUtils.isEmpty(orderItemList)) {
            log.warn("[getFreeReturnItemDOList]原订单明细为空");
            return Collections.emptyList();
        }
        List<FreeReturnItemDO> returnItemDOList = new ArrayList<>();
        List<Long> returnItemGuids = dynamicHelper.generateGuids(GuidKeyConstant.FREE_RETURN_ITEM, dineInItemList.size());
        orderItemList.forEach(order -> {
            FreeReturnItemDO returnItemDO = new FreeReturnItemDO();
            returnItemDO.setGuid(returnItemGuids.remove(0));
            returnItemDO.setOrderGuid(order.getOrderGuid());
            returnItemDO.setOrderItemGuid(order.getGuid());
            returnItemDO.setItemType(order.getItemType());
            returnItemDO.setItemGuid(order.getItemGuid());
            returnItemDO.setSkuGuid(order.getSkuGuid());
            returnItemDO.setSkuName(order.getSkuName());
            returnItemDO.setItemName(order.getItemName());
            returnItemDO.setPrice(order.getPrice());
            returnItemDO.setType(FreeReturnTypeEnum.RETURN.getCode());
            returnItemDO.setIsFree(BooleanEnum.FALSE.getCode());
            returnItemDO.setTotalDiscountFee(order.getTotalDiscountFee());
            returnItemDO.setReason(orderRefundReqDTO.getRefundReason());
            returnItemDO.setStaffName(orderRefundReqDTO.getUserName());
            returnItemDO.setStaffGuid(orderRefundReqDTO.getUserGuid());
            returnItemDO.setStoreGuid(orderRefundReqDTO.getStoreGuid());
            returnItemDO.setStoreName(orderRefundReqDTO.getStoreName());
            returnItemDO.setAuthStaffGuid(orderRefundReqDTO.getAuthStaffGuid());
            returnItemDO.setAuthStaffName(orderRefundReqDTO.getAuthStaffName());
            returnItemDO.setAuthStaffPicture(orderRefundReqDTO.getPicture());
            returnItemDO.setOrderState(orderDO.getState());
            returnItemDO.setRecoveryType(orderDO.getRecoveryType());
            // 这里的出堂未查询kds
            returnItemDO.setIsOutDinner(Boolean.TRUE);
            DineInItemDTO dineInItemDTO = dineInItemMap.get(String.valueOf(order.getGuid()));
            if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                returnItemDO.setRefundCount(dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount()));
                returnItemDO.setActuallyRefundFee(dineInItemDTO.getPrice());
                returnItemDOList.add(returnItemDO);
            }
        });
        log.info("退款退菜记录={}", JacksonUtils.writeValueAsString(returnItemDOList));
        return returnItemDOList;
    }

    /**
     * 构建退款订单
     */
    private OrderDO buildRefundOrderDO(OrderDO orderDO, OrderRefundReqDTO orderRefundReqDTO) {
        // 本次退款金额
        BigDecimal originalRefundAmount = orderRefundReqDTO.getRefundAmount();
        BigDecimal refundAmount = orderRefundReqDTO.getRefundAmount();
        BigDecimal currentCouponBuyAmount = Optional.ofNullable(orderRefundReqDTO.getCurrentCouponBuyAmount()).orElse(BigDecimal.ZERO);
        if (RefundTypeEnum.BACKTRACK.getCode() == orderRefundReqDTO.getRefundType()
                && BigDecimalUtil.greaterThanZero(currentCouponBuyAmount)) {
            refundAmount = refundAmount.subtract(currentCouponBuyAmount);
        }
        // 需要判断 是否已经产生退款单， 如果存在，则在原退款单上做编辑
        Long refundOrderGuid = orderDO.getRefundOrderGuid();
        // copy order
        OrderDO refundOrderDO = JacksonUtils.toObject(OrderDO.class, JacksonUtils.writeValueAsString(orderDO));
        if (Objects.isNull(refundOrderGuid)) {
            refundOrderGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER);
            refundOrderDO.setGuid(refundOrderGuid);
            refundOrderDO.setOrderNo(generateRefundOrderNo(orderDO.getOrderNo()));
            refundOrderDO.setOriginalOrderGuid(orderDO.getGuid());
            refundOrderDO.setRecoveryId(String.valueOf(0));
            refundOrderDO.setRecoveryType(RecoveryTypeEnum.RETURN.getCode());
            refundOrderDO.setRecoveryDeviceType(orderRefundReqDTO.getDeviceType());
            refundOrderDO.setState(StateEnum.REFUNDED.getCode());
            refundOrderDO.setOrderFeeForCombine(originalRefundAmount);
            refundOrderDO.setOrderFee(originalRefundAmount);
            refundOrderDO.setActuallyPayFee(refundAmount);
            refundOrderDO.setAppendFee(BigDecimal.ZERO);
            refundOrderDO.setChangeFee(BigDecimal.ZERO);
            refundOrderDO.setGmtCreate(LocalDateTime.now());
            refundOrderDO.setCheckinTime(LocalDateTime.now());
        } else {
            // 查询退款订单
            refundOrderDO = orderService.getById(refundOrderGuid);
            refundOrderDO.setOrderFeeForCombine(refundOrderDO.getOrderFeeForCombine().add(refundAmount));
            refundOrderDO.setOrderFee(refundOrderDO.getOrderFee().add(refundAmount));
            refundOrderDO.setActuallyPayFee(refundOrderDO.getActuallyPayFee().add(refundAmount));
        }
        // 退单时间更新为最近一次更新
        refundOrderDO.setCancelTime(LocalDateTime.now());
        refundOrderDO.setCheckoutTime(refundOrderDO.getCancelTime());
        // 退单信息 (退款订单上记录 最新的一次订单的退款信息)
        refundOrderDO.setRecoveryReason(orderRefundReqDTO.getRefundReason());
        refundOrderDO.setRecoveryDeviceType(orderRefundReqDTO.getDeviceType());
        refundOrderDO.setRecoveryStaffGuid(UserContextUtils.getUserGuid());
        refundOrderDO.setRecoveryStaffName(UserContextUtils.getUserName());
        return refundOrderDO;
    }

    /**
     * 构建退款订单扩展
     */
    private OrderExtendsDO buildRefundOrderExtendsDO(OrderDO orderDO, OrderDO refundOrderDO) {
        // 查询原订单扩展
        OrderExtendsDO orderExtendsDO = orderExtendsService.getById(orderDO.getGuid());
        if (Objects.isNull(orderExtendsDO)) {
            return null;
        }
        OrderExtendsDO refundOrderExtendsDO = new OrderExtendsDO();
        refundOrderExtendsDO.setGuid(refundOrderDO.getGuid());
        refundOrderExtendsDO.setStoreGuid(refundOrderDO.getStoreGuid());
        refundOrderExtendsDO.setBusinessDay(refundOrderDO.getBusinessDay());
        refundOrderExtendsDO.setAssociatedTableNames(orderExtendsDO.getAssociatedTableNames());
        refundOrderExtendsDO.setAssociatedFlag(orderExtendsDO.getAssociatedFlag());
        refundOrderExtendsDO.setAssociatedSn(orderExtendsDO.getAssociatedSn());
        refundOrderExtendsDO.setAssociatedTableGuids(orderExtendsDO.getAssociatedTableGuids());

        refundOrderExtendsDO.setLudouMemberPhone(orderExtendsDO.getLudouMemberPhone());
        refundOrderExtendsDO.setLudouMemberName(orderExtendsDO.getLudouMemberName());
        refundOrderExtendsDO.setLudouMemberGuid(orderExtendsDO.getLudouMemberGuid());
        return refundOrderExtendsDO;
    }

    /**
     * 生成 退款单单号
     */
    private String generateRefundOrderNo(String orderNo) {
        int index = 1;
        if (orderNo.contains(RECOVERY_PREFIX)) {
            orderNo = orderNo.replace(RECOVERY_PREFIX, "");
            index = 2;
        }
        return REFUND_MARK + index + orderNo;
    }


    /**
     * 构建退款订单商品明细
     */
    private List<OrderItemDO> buildRefundOrderItemDOList(OrderRefundReqDTO orderRefundReqDTO) {
        Long refundOrderGuid = orderRefundReqDTO.getRefundOrderGuid();
        List<DineInItemDTO> dineInItemList = orderRefundReqDTO.getDineInItemDTOS();
        if (CollectionUtils.isEmpty(dineInItemList)) {
            log.info("自定义金额退款，无商品明细");
            return Collections.emptyList();
        }
        Map<String, DineInItemDTO> dineInItemMap = dineInItemList.stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));

        // 赠菜列表
        Map<Long, DineInItemDTO> freeDineInItemMap = dineInItemList.stream()
                .filter(e -> Objects.nonNull(e.getOriginalOrderItemGuid()))
                .collect(Collectors.toMap(DineInItemDTO::getOriginalOrderItemGuid, Function.identity(), (key1, key2) -> key1));

        List<Long> orderItemGuids = dineInItemMap.keySet().stream().map(Long::valueOf).collect(Collectors.toList());
        if (MapUtils.isNotEmpty(freeDineInItemMap)) {
            orderItemGuids.addAll(new ArrayList<>(freeDineInItemMap.keySet()));
        }
        if (CollectionUtils.isEmpty(orderItemGuids)) {
            return Collections.emptyList();
        }
        // 查询原订单明细
        List<OrderItemDO> orderItemList = orderItemService.listByIds(orderItemGuids);
        if (CollectionUtils.isEmpty(orderItemList)) {
            return Collections.emptyList();
        }
        // guid
        List<Long> newOrderItemGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_ORDER_ITEM, orderItemList.size());
        List<OrderItemDO> newOrderItemList = orderItemList.stream().map(orderItem -> {
            OrderItemDO refundOrderItemDO = new OrderItemDO();
            BeanUtils.copyProperties(orderItem, refundOrderItemDO);
            refundOrderItemDO.setGuid(newOrderItemGuids.remove(0));
            refundOrderItemDO.setOrderGuid(refundOrderGuid);
            refundOrderItemDO.setOriginalOrderItemGuid(orderItem.getGuid());
            refundOrderItemDO.setReturnCount(BigDecimal.ZERO);
            refundOrderItemDO.setFreeCount(BigDecimal.ZERO);
            refundOrderItemDO.setCurrentCount(BigDecimal.ZERO);
            refundOrderItemDO.setRefundCount(BigDecimal.ZERO);

            DineInItemDTO dineInItemDTO = dineInItemMap.get(String.valueOf(orderItem.getGuid()));
            if (Objects.nonNull(dineInItemDTO)) {
                refundOrderItemDO.setCurrentCount(dineInItemDTO.getCurrentCount());
                refundOrderItemDO.setPrice(dineInItemDTO.getPrice());
            }
            DineInItemDTO freeDineInItemDTO = freeDineInItemMap.get(orderItem.getGuid());
            if (Objects.nonNull(freeDineInItemDTO)) {
                refundOrderItemDO.setFreeCount(freeDineInItemDTO.getCurrentCount());
            }
            refundOrderItemDO.setGmtCreate(null);
            refundOrderItemDO.setGmtModified(null);
            refundOrderItemDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
            refundOrderItemDO.setCreateStaffName(UserContextUtils.getUserName());
            return refundOrderItemDO;
        }).collect(Collectors.toList());
        // 套餐子商品
        List<OrderItemDO> subOrderItemList = buildSubOrderItemList(refundOrderGuid, orderItemGuids, newOrderItemList);
        if (CollectionUtils.isNotEmpty(subOrderItemList)) {
            newOrderItemList.addAll(subOrderItemList);
        }
        return newOrderItemList;
    }


    /**
     * 构建退款订单商品明细
     */
    private List<OrderItemExtendsDO> buildRefundOrderItemExtendsDOList(OrderRefundReqDTO orderRefundReqDTO, List<OrderItemDO> orderItemList) {
        // 支持自定义金额退款（无商品明细）
        if (CollectionUtils.isEmpty(orderItemList)) {
            log.info("自定义金额退款，无商品明细");
            return Collections.emptyList();
        }

        // 查询原订单扩展明细
        List<OrderItemExtendsDO> orderItemExtendsList = orderItemExtendsService.listByOrderGuid(Long.valueOf(orderRefundReqDTO.getOrderGuid()));
        if (CollectionUtils.isEmpty(orderItemExtendsList)) {
            return Collections.emptyList();
        }
        Long refundOrderGuid = orderRefundReqDTO.getRefundOrderGuid();
        Map<Long, OrderItemDO> refundOrderItemMap = orderItemList.stream()
                .collect(Collectors.toMap(OrderItemDO::getOriginalOrderItemGuid, Function.identity(), (key1, key2) -> key1));
        List<OrderItemExtendsDO> newOrderItemExtendsList = Lists.newArrayList();
        for (OrderItemExtendsDO oldOrderItemExtendsDO : orderItemExtendsList) {
            OrderItemDO refundOrderItemDO = refundOrderItemMap.get(oldOrderItemExtendsDO.getGuid());
            if (Objects.nonNull(refundOrderItemDO)) {
                OrderItemExtendsDO refundOrderItemExtendDO = new OrderItemExtendsDO();
                BeanUtils.copyProperties(oldOrderItemExtendsDO, refundOrderItemExtendDO);
                refundOrderItemDO.setChangeFlag(oldOrderItemExtendsDO.getChangeFlag());
                refundOrderItemExtendDO.setGuid(refundOrderItemDO.getGuid());
                refundOrderItemExtendDO.setOrderGuid(refundOrderGuid);
                refundOrderItemExtendDO.setGmtCreate(LocalDateTime.now());
                refundOrderItemExtendDO.setGmtModified(LocalDateTime.now());
                newOrderItemExtendsList.add(refundOrderItemExtendDO);
            }
        }
        return newOrderItemExtendsList;
    }

    /**
     * 获取商品单价（优先 DTO，否则原始 OrderItemDO）
     */
    private BigDecimal getItemPrice(DineInItemDTO dto) {
        return Optional.ofNullable(dto)
                .map(dineInItemDTO -> {
                    return BigDecimalUtil.nonNullValue(dineInItemDTO.getPrice());
                })
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 获取商品数量（优先 DTO，否则原始 OrderItemDO）
     */
    private BigDecimal getItemCount(DineInItemDTO dto) {
        return Optional.ofNullable(dto)
                .map(dineInItemDTO -> {
                    return BigDecimalUtil.nonNullValue(dineInItemDTO.getCurrentCount());
                })
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 获取商品实付金额（price * count）
     */
    private BigDecimal getItemPaidAmount(OrderItemExtendsDO itemExtends,
                                         Map<Long, OrderItemDO> refundOrderItemMap,
                                         Map<String, DineInItemDTO> dineInItemDTOMap) {
        Long originalOrderItemGuid = refundOrderItemMap.get(itemExtends.getGuid()).getOriginalOrderItemGuid();
        BigDecimal price = getItemPrice(dineInItemDTOMap.get(String.valueOf(originalOrderItemGuid)));
        BigDecimal count = getItemCount(dineInItemDTOMap.get(String.valueOf(originalOrderItemGuid)));
        return price.multiply(count);
    }

    /**
     * 构建退款订单商品换菜明细
     */
    private List<OrderItemChangesDO> buildRefundOrderItemChangesDOList(OrderRefundReqDTO orderRefundReqDTO, List<OrderItemDO> orderItemList) {
        // 支持自定义金额退款（无商品明细）
        if (CollectionUtils.isEmpty(orderItemList)) {
            log.info("自定义金额退款，无商品换菜明细");
            return Collections.emptyList();
        }

        // 查询原订单换菜明细
        List<OrderItemChangesDO> orderItemChangesList = orderItemChangesService.listByOrderGuid(Long.valueOf(orderRefundReqDTO.getOrderGuid()));
        if (CollectionUtils.isEmpty(orderItemChangesList)) {
            return Collections.emptyList();
        }
        Long refundOrderGuid = orderRefundReqDTO.getRefundOrderGuid();
        Map<Long, OrderItemDO> refundOrderItemMap = orderItemList.stream()
                .collect(Collectors.toMap(OrderItemDO::getOriginalOrderItemGuid, Function.identity(), (key1, key2) -> key1));
        List<Long> guids = dynamicHelper.generateGuids(OrderItemChangesDO.class.getSimpleName(), orderItemChangesList.size());
        List<OrderItemChangesDO> newOrderItemChangesList = Lists.newArrayList();
        for (OrderItemChangesDO oldOrderItemChangesDO : orderItemChangesList) {
            OrderItemDO refundOrderItemDO = refundOrderItemMap.get(oldOrderItemChangesDO.getOrderItemGuid());
            if (Objects.nonNull(refundOrderItemDO)) {
                OrderItemChangesDO refundOrderItemChangesDO = new OrderItemChangesDO();
                BeanUtils.copyProperties(oldOrderItemChangesDO, refundOrderItemChangesDO);
                refundOrderItemChangesDO.setGuid(guids.remove(0));
                refundOrderItemChangesDO.setOrderGuid(refundOrderGuid);
                refundOrderItemChangesDO.setOrderItemGuid(refundOrderItemDO.getGuid());
                refundOrderItemChangesDO.setOriginalOrderItemGuid(oldOrderItemChangesDO.getGuid());
                refundOrderItemChangesDO.setCurrentCount(refundOrderItemDO.getCurrentCount());
                refundOrderItemChangesDO.setGmtCreate(LocalDateTime.now());
                refundOrderItemChangesDO.setGmtModified(LocalDateTime.now());
                newOrderItemChangesList.add(refundOrderItemChangesDO);
            }
        }
        return newOrderItemChangesList;
    }


    /**
     * 退款 套餐子商品明细
     */
    private List<OrderItemDO> buildSubOrderItemList(Long refundOrderGuid, List<Long> orderItemGuids, List<OrderItemDO> newOrderItemList) {
        List<OrderItemDO> subOrderItemList = orderItemService.listByMainItemGuids(orderItemGuids);
        if (CollectionUtils.isEmpty(subOrderItemList)) {
            return Collections.emptyList();
        }
        Map<Long, Long> orderItemMappingMap = newOrderItemList.stream().collect(Collectors.toMap(OrderItemDO::getOriginalOrderItemGuid, OrderItemDO::getGuid));
        // guid
        List<Long> newOrderItemGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_ORDER_ITEM, subOrderItemList.size());
        return subOrderItemList.stream().map(orderItem -> {
            OrderItemDO refundOrderItemDO = new OrderItemDO();
            BeanUtils.copyProperties(orderItem, refundOrderItemDO);
            refundOrderItemDO.setGuid(newOrderItemGuids.remove(0));
            refundOrderItemDO.setOrderGuid(refundOrderGuid);
            refundOrderItemDO.setOriginalOrderItemGuid(orderItem.getGuid());
            refundOrderItemDO.setReturnCount(BigDecimal.ZERO);
            refundOrderItemDO.setRefundCount(BigDecimal.ZERO);
            refundOrderItemDO.setGmtCreate(null);
            refundOrderItemDO.setGmtModified(null);
            refundOrderItemDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
            refundOrderItemDO.setCreateStaffName(UserContextUtils.getUserName());
            Long originalItemGuid = orderItemMappingMap.get(orderItem.getParentItemGuid());
            if (Objects.nonNull(originalItemGuid)) {
                refundOrderItemDO.setParentItemGuid(originalItemGuid);
            }
            return refundOrderItemDO;
        }).collect(Collectors.toList());
    }


    /**
     * 构建退单商品记录
     */
    private List<OrderItemRecordDO> buildRefundOrderRecordDOList(List<OrderItemDO> refundOrderItemList) {
        if (CollectionUtils.isEmpty(refundOrderItemList)) {
            return Collections.emptyList();
        }
        // guid
        List<Long> orderRecordGuids = dynamicHelper.generateGuids(GuidKeyConstant.ORDER_ITEM_RECORD, refundOrderItemList.size());
        return refundOrderItemList.stream().map(refundOrderItem -> {
            OrderItemRecordDO orderItemRecordDO = new OrderItemRecordDO();
            BeanUtils.copyProperties(refundOrderItem, orderItemRecordDO);
            orderItemRecordDO.setGuid(orderRecordGuids.remove(0));
            orderItemRecordDO.setOrderItemGuid(String.valueOf(refundOrderItem.getGuid()));
            orderItemRecordDO.setType(4);
            orderItemRecordDO.setLog(refundOrderItem.getParentItemGuid() == 0L ? "【退款】:点菜" : "【退款】:子菜");
            orderItemRecordDO.setGmtCreate(null);
            orderItemRecordDO.setGmtModified(null);
            return orderItemRecordDO;
        }).collect(Collectors.toList());
    }


    /**
     * 构建退单附加费明细
     */
    private List<AppendFeeDO> buildRefundOrderAppendFeeDOList(OrderRefundReqDTO orderRefundReqDTO) {
        Long refundOrderGuid = orderRefundReqDTO.getRefundOrderGuid();
        List<DineInItemDTO> dineInItemList = orderRefundReqDTO.getDineInItemDTOS();
        if (CollectionUtils.isEmpty(dineInItemList)) {
            log.info("自定义金额退款，无附加费明细");
            return Collections.emptyList();
        }
        Map<String, DineInItemDTO> dineInItemMap = dineInItemList.stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        // 查询原订单附加费明细
        List<AppendFeeDO> appendFeeList = appendFeeMpService.listByIds(dineInItemMap.keySet());
        if (CollectionUtils.isEmpty(appendFeeList)) {
            return Collections.emptyList();
        }
        // guid
        List<Long> appendFeeGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_APPEND_FEE, appendFeeList.size());
        return appendFeeList.stream().map(appendFeeItem -> {
            AppendFeeDO appendFeeDO = new AppendFeeDO();
            appendFeeDO.setGuid(appendFeeGuids.remove(0));
            appendFeeDO.setOrderGuid(refundOrderGuid);
            DineInItemDTO dineInItemDTO = dineInItemMap.get(String.valueOf(appendFeeItem.getGuid()));
            appendFeeDO.setAmount(dineInItemDTO.getCurrentCount().multiply(dineInItemDTO.getPrice()));
            appendFeeDO.setRefundCount(dineInItemDTO.getCurrentCount());
            appendFeeDO.setName(dineInItemDTO.getItemName());
            appendFeeDO.setType(appendFeeItem.getType());
            appendFeeDO.setUnitPrice(dineInItemDTO.getPrice());
            appendFeeDO.setAreaGuid(appendFeeItem.getAreaGuid());
            appendFeeDO.setStoreGuid(appendFeeItem.getStoreGuid());
            appendFeeDO.setStoreName(appendFeeItem.getStoreName());
            appendFeeDO.setStaffGuid(UserContextUtils.getUserGuid());
            appendFeeDO.setStaffName(UserContextUtils.getUserName());
            appendFeeDO.setOriginalGuid(appendFeeItem.getGuid());
            return appendFeeDO;
        }).collect(Collectors.toList());
    }

    /**
     * 构建退单赠菜商品明细
     */
    private List<FreeReturnItemDO> buildRefundOrderFreeItemDOList(OrderRefundReqDTO orderRefundReqDTO, List<OrderItemDO> orderItemList) {
        Long refundOrderGuid = orderRefundReqDTO.getRefundOrderGuid();
        List<DineInItemDTO> dineInItemList = orderRefundReqDTO.getDineInItemDTOS();
        if (CollectionUtils.isEmpty(dineInItemList)) {
            log.info("自定义金额退款，无赠菜明细");
            return Collections.emptyList();
        }
        Map<String, DineInItemDTO> dineInItemMap = dineInItemList.stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));

        // 查询原订单赠菜商品明细
        List<Long> orderItemGuids = dineInItemMap.keySet().stream().map(Long::valueOf).collect(Collectors.toList());
        List<FreeReturnItemDO> freeReturnItemList = freeReturnItemService.listByIds(orderItemGuids);
        if (CollectionUtils.isEmpty(freeReturnItemList)) {
            return Collections.emptyList();
        }
        Map<Long, Long> orderItemGuidMappingMap = orderItemList.stream().collect(Collectors.toMap(OrderItemDO::getOriginalOrderItemGuid, OrderItemDO::getGuid));
        // guid
        List<Long> freeReturnItemGuids = dynamicHelper.generateGuids(GuidKeyConstant.FREE_RETURN_ITEM, freeReturnItemList.size());
        return freeReturnItemList.stream().map(freeReturnItem -> {
            FreeReturnItemDO freeReturnItemDO = new FreeReturnItemDO();
            BeanUtils.copyProperties(freeReturnItem, freeReturnItemDO);
            freeReturnItemDO.setGuid(freeReturnItemGuids.remove(0));
            freeReturnItemDO.setOrderGuid(refundOrderGuid);
            freeReturnItemDO.setOriginalGuid(freeReturnItem.getGuid());
            Long refundOrderItemGuid = orderItemGuidMappingMap.get(freeReturnItem.getOrderItemGuid());
            freeReturnItemDO.setOrderItemGuid(refundOrderItemGuid);
            DineInItemDTO dineInItemDTO = dineInItemMap.get(String.valueOf(freeReturnItem.getGuid()));
            freeReturnItemDO.setCount(dineInItemDTO.getCurrentCount());
            freeReturnItemDO.setStaffGuid(UserContextUtils.getUserGuid());
            freeReturnItemDO.setStaffName(UserContextUtils.getUserName());
            freeReturnItemDO.setGmtCreate(null);
            freeReturnItemDO.setGmtModified(null);
            return freeReturnItemDO;
        }).collect(Collectors.toList());
    }

    /**
     * 构建退款订单 商品属性明细
     */
    private List<ItemAttrDO> buildRefundOrderItemAttrDOList(OrderRefundReqDTO orderRefundReqDTO, List<OrderItemDO> orderItemList) {
        Long refundOrderGuid = orderRefundReqDTO.getRefundOrderGuid();
        List<DineInItemDTO> dineInItemList = orderRefundReqDTO.getDineInItemDTOS();
        if (CollectionUtils.isEmpty(dineInItemList)) {
            log.info("自定义金额退款，无商品属性明细");
            return Collections.emptyList();
        }
        Set<String> itemGuids = new HashSet<>();
        Set<String> mainItemGuids = dineInItemList.stream()
                .filter(i -> Objects.equals(ItemTypeEnum.GROUP.getCode(), i.getItemType()))
                .map(DineInItemDTO::getGuid)
                .collect(Collectors.toSet());
        List<OrderItemDO> subOrderItemList = orderItemService.listByMainItemGuids(mainItemGuids);
        if (!CollectionUtils.isEmpty(subOrderItemList)) {
            itemGuids.addAll(subOrderItemList.stream().map(si -> String.valueOf(si.getGuid())).collect(Collectors.toSet()));
        }
        Map<String, DineInItemDTO> dineInItemMap = dineInItemList.stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        itemGuids.addAll(dineInItemMap.keySet());
        // 查询原订单赠菜商品明细
        List<ItemAttrDO> itemAttrList = itemAttrService.listByItemGuids(itemGuids);
        if (CollectionUtils.isEmpty(itemAttrList)) {
            log.warn("[buildRefundOrderItemAttrDOList]原订单赠菜商品明细为空");
            return Collections.emptyList();
        }
        Map<Long, Long> orderItemGuidMappingMap = orderItemList.stream()
                .collect(Collectors.toMap(OrderItemDO::getOriginalOrderItemGuid, OrderItemDO::getGuid));
        // guid
        List<Long> itemAttrGuids = dynamicHelper.generateGuids(GuidKeyConstant.ITEM_ATTR_GUID, itemAttrList.size());
        return itemAttrList.stream().map(itemAttr -> {
            ItemAttrDO itemAttrDO = new ItemAttrDO();
            BeanUtils.copyProperties(itemAttr, itemAttrDO);
            itemAttrDO.setGuid(itemAttrGuids.remove(0));
            itemAttrDO.setOrderGuid(refundOrderGuid);
            Long refundOrderItemGuid = orderItemGuidMappingMap.get(itemAttr.getOrderItemGuid());
            itemAttrDO.setOrderItemGuid(refundOrderItemGuid);
            itemAttrDO.setGmtCreate(null);
            itemAttrDO.setGmtModified(null);
            return itemAttrDO;
        }).collect(Collectors.toList());
    }

    /**
     * 构建退款订单 换菜商品属性明细
     */
    private List<ItemAttrDO> buildRefundOrderItemChangeAttrList(OrderRefundReqDTO orderRefundReqDTO, List<OrderItemChangesDO> orderItemChangesList) {
        Long refundOrderGuid = orderRefundReqDTO.getRefundOrderGuid();
        if (CollectionUtils.isEmpty(orderItemChangesList)) {
            log.warn("换菜明细为空,没有退款的属性明细");
            return Collections.emptyList();
        }
        Map<Long, OrderItemChangesDO> orderItemChangesMap = orderItemChangesList.stream()
                .collect(Collectors.toMap(OrderItemChangesDO::getOriginalOrderItemGuid, Function.identity(), (key1, key2) -> key1));

        // 查询原订单赠菜商品明细
        List<ItemAttrDO> itemAttrList = itemAttrService.listByItemGuids(new ArrayList<>(orderItemChangesMap.keySet()));
        if (CollectionUtils.isEmpty(itemAttrList)) {
            log.warn("[buildRefundOrderItemAttrDOList]原订单换菜商品属性明细为空");
            return Collections.emptyList();
        }
        // guid
        List<Long> itemAttrGuids = dynamicHelper.generateGuids(GuidKeyConstant.ITEM_ATTR_GUID, itemAttrList.size());
        List<ItemAttrDO> refundItemAttrList = Lists.newArrayList();
        for (ItemAttrDO oldItemAttrDO : itemAttrList) {
            OrderItemChangesDO orderItemChangesDO = orderItemChangesMap.get(oldItemAttrDO.getOrderItemGuid());
            if (Objects.isNull(orderItemChangesDO)) {
                continue;
            }
            ItemAttrDO itemAttrDO = new ItemAttrDO();
            BeanUtils.copyProperties(oldItemAttrDO, itemAttrDO);
            itemAttrDO.setGuid(itemAttrGuids.remove(0));
            itemAttrDO.setOrderGuid(refundOrderGuid);
            itemAttrDO.setOrderItemGuid(orderItemChangesDO.getGuid());
            itemAttrDO.setGmtCreate(null);
            itemAttrDO.setGmtModified(null);
            refundItemAttrList.add(itemAttrDO);
        }
        return refundItemAttrList;
    }

    /**
     * 构建退款订单 交易明细
     */
    private List<TransactionRecordDO> buildRefundOrderTransactionRecordDOList(OrderRefundReqDTO orderRefundReqDTO) {
        Long refundOrderGuid = orderRefundReqDTO.getRefundOrderGuid();
        // 查询营业日
        orderRefundReqDTO.setBusinessDay(getBusinessDay());
        List<TransactionRecordDO> transactionRecordList = Lists.newArrayList();
        if (orderRefundReqDTO.getRefundType() == RefundTypeEnum.OFFLINE_REFUND.getCode()) {
            // 线下退款
            TransactionRecordDO transactionRecordDO = generateOfflineTransactionRecordDO(refundOrderGuid, orderRefundReqDTO);
            transactionRecordList.add(transactionRecordDO);
        } else {
            // 原路退回
            List<TransactionRecordDO> transactionRecordDOList = generateNegateTransactionRecordDOList(refundOrderGuid, orderRefundReqDTO);
            if (CollectionUtils.isNotEmpty(transactionRecordDOList)) {
                transactionRecordList.addAll(transactionRecordDOList);
            }
        }
        return transactionRecordList;
    }

    /**
     * 构建退款订单 多笔聚合支付交易明细
     */
    private List<MultipleTransactionRecordDO> buildRefundOrderMultipleTransactionRecordDOList(OrderRefundReqDTO orderRefundReqDTO) {
        if (orderRefundReqDTO.getRefundType() == RefundTypeEnum.OFFLINE_REFUND.getCode()) {
            // 线下退款
            return Lists.newArrayList();
        }
        List<ActuallyPayFeeDetailDTO> refundTypeList = orderRefundReqDTO.getRefundTypeList();
        ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = refundTypeList.stream()
                .filter(e -> Boolean.TRUE.equals(e.getIsMultipleAggPay()))
                .findFirst().orElse(null);
        if (Objects.isNull(actuallyPayFeeDetailDTO)) {
            return Lists.newArrayList();
        }
        return generateNegateMultipleTransactionRecordDOList(orderRefundReqDTO, actuallyPayFeeDetailDTO);
    }


    /**
     * 生成 线下退款交易记录DO
     */
    private TransactionRecordDO generateOfflineTransactionRecordDO(Long refundOrderGuid, OrderRefundReqDTO orderRefundReqDTO) {
        TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
        transactionRecordDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD));
        transactionRecordDO.setOrderGuid(refundOrderGuid);
        transactionRecordDO.setTerminalId(orderRefundReqDTO.getDeviceId());
        transactionRecordDO.setAmount(orderRefundReqDTO.getRefundAmount().negate());
        transactionRecordDO.setTradeType(TradeTypeEnum.REFUND_OUT.getCode());
        transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
        transactionRecordDO.setPaymentType(PaymentTypeEnum.OFFLINE_REFUND.getCode());
        transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.OFFLINE_REFUND.getDesc());
        transactionRecordDO.setCreateTime(LocalDateTime.now());
        transactionRecordDO.setBusinessDay(orderRefundReqDTO.getBusinessDay());
        transactionRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
        transactionRecordDO.setStaffName(UserContextUtils.getUserName());
        transactionRecordDO.setStoreGuid(UserContextUtils.getStoreGuid());
        transactionRecordDO.setStoreName(UserContextUtils.getStoreName());
        return transactionRecordDO;
    }


    /**
     * 生成 对应交易记录的退款记录DO
     */
    private List<TransactionRecordDO> generateNegateTransactionRecordDOList(Long refundOrderGuid, OrderRefundReqDTO orderRefundReqDTO) {
        List<ActuallyPayFeeDetailDTO> refundTypeList = orderRefundReqDTO.getRefundTypeList();
        List<TransactionRecordDTO> transactionRecordList = orderRefundReqDTO.getTransactionRecordList();
        Map<Long, TransactionRecordDTO> transactionRecordMap = transactionRecordList.stream()
                .collect(Collectors.toMap(TransactionRecordDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        List<TransactionRecordDO> negateTransactionRecordList = Lists.newArrayList();
        for (ActuallyPayFeeDetailDTO detailDTO : refundTypeList) {
            Long negateTransactionRecordGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
            long actuallyPayGuid = Long.parseLong(detailDTO.getGuid());
            if (Boolean.TRUE.equals(detailDTO.getIsMultipleAggPay()) && StringUtils.isNotEmpty(detailDTO.getTransactionRecordGuid())) {
                actuallyPayGuid = Long.parseLong(detailDTO.getTransactionRecordGuid());
                detailDTO.setRefundTransactionRecordGuid(String.valueOf(negateTransactionRecordGuid));
            }
            TransactionRecordDTO transactionRecordDTO = transactionRecordMap.get(actuallyPayGuid);
            if (Objects.isNull(transactionRecordDTO)) {
                continue;
            }
            TransactionRecordDO negateTransactionRecord = new TransactionRecordDO();
            BeanUtils.copyProperties(transactionRecordDTO, negateTransactionRecord);
            negateTransactionRecord.setGuid(negateTransactionRecordGuid);
            negateTransactionRecord.setOrderGuid(refundOrderGuid);
            negateTransactionRecord.setTerminalId(orderRefundReqDTO.getDeviceId());
            negateTransactionRecord.setAmount(detailDTO.getAmount().negate());
            negateTransactionRecord.setRefundAmount(BigDecimal.ZERO);
            negateTransactionRecord.setTradeType(TradeTypeEnum.REFUND_OUT.getCode());
            negateTransactionRecord.setState(TradeStateEnum.SUCCESS.getCode());
            negateTransactionRecord.setBusinessDay(orderRefundReqDTO.getBusinessDay());
            negateTransactionRecord.setStaffGuid(UserContextUtils.getUserGuid());
            negateTransactionRecord.setStaffName(UserContextUtils.getUserName());
            negateTransactionRecord.setStoreGuid(UserContextUtils.getStoreGuid());
            negateTransactionRecord.setStoreName(UserContextUtils.getStoreName());
            negateTransactionRecord.setGmtCreate(LocalDateTime.now());
            negateTransactionRecord.setGmtModified(LocalDateTime.now());
            negateTransactionRecordList.add(negateTransactionRecord);
        }
        return negateTransactionRecordList;
    }


    /**
     * 生成 对应多笔聚合支付交易记录的退款记录DO
     */
    private List<MultipleTransactionRecordDO> generateNegateMultipleTransactionRecordDOList(OrderRefundReqDTO orderRefundReqDTO,
                                                                                            ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO) {
        Long refundOrderGuid = orderRefundReqDTO.getRefundOrderGuid();
        List<TransactionRecordDTO> multipleTransactionRecordList = orderRefundReqDTO.getMultipleTransactionRecordList();
        TransactionRecordDTO multipleTransactionRecord = multipleTransactionRecordList.stream()
                .filter(e -> actuallyPayFeeDetailDTO.getGuid().equals(String.valueOf(e.getGuid())))
                .findFirst().orElse(null);
        if (Objects.isNull(multipleTransactionRecord)) {
            log.error("多笔聚合支付明细查询丢失,multipleTransactionRecordList:{}", JacksonUtils.writeValueAsString(multipleTransactionRecordList));
            return Lists.newArrayList();
        }
        MultipleTransactionRecordDO refundMultipleTransactionRecordDO = new MultipleTransactionRecordDO();
        BeanUtils.copyProperties(multipleTransactionRecord, refundMultipleTransactionRecordDO);
        refundMultipleTransactionRecordDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_MULTIPLE_TRANSACTION_RECORD));
        refundMultipleTransactionRecordDO.setOrderGuid(refundOrderGuid);
        refundMultipleTransactionRecordDO.setTransactionRecordGuid(Long.valueOf(actuallyPayFeeDetailDTO.getRefundTransactionRecordGuid()));
        refundMultipleTransactionRecordDO.setOriginalMultipleTransactionRecordGuid(Long.valueOf(actuallyPayFeeDetailDTO.getGuid()));
        refundMultipleTransactionRecordDO.setBusinessDay(orderRefundReqDTO.getBusinessDay());
        refundMultipleTransactionRecordDO.setTerminalId(orderRefundReqDTO.getDeviceId());
        refundMultipleTransactionRecordDO.setAmount(actuallyPayFeeDetailDTO.getAmount().negate());
        refundMultipleTransactionRecordDO.setRefundableFee(BigDecimal.ZERO);
        refundMultipleTransactionRecordDO.setRefundAmount(BigDecimal.ZERO);
        refundMultipleTransactionRecordDO.setTradeType(TradeTypeEnum.REFUND_OUT.getCode());
        refundMultipleTransactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
        refundMultipleTransactionRecordDO.setStaffGuid(UserContextUtils.getUserGuid());
        refundMultipleTransactionRecordDO.setStaffName(UserContextUtils.getUserName());
        refundMultipleTransactionRecordDO.setGmtCreate(LocalDateTime.now());
        refundMultipleTransactionRecordDO.setGmtModified(LocalDateTime.now());
        return Lists.newArrayList(refundMultipleTransactionRecordDO);
    }


    /**
     * 构建订单退款记录
     */
    private OrderRefundRecordDO buildOrderRefundRecordDO(String refundOrderNo, OrderRefundReqDTO orderRefundReqDTO, List<TransactionRecordDO> transactionRecordList) {
        Long refundOrderGuid = orderRefundReqDTO.getRefundOrderGuid();
        // 查询最大recordNo
        Long maxRecordNo = orderRefundRecordService.getMaxRecordNoByRefundOrderGuid(refundOrderGuid);
        OrderRefundRecordDO orderRefundRecordDO = new OrderRefundRecordDO();
        orderRefundRecordDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_ORDER_REFUND_RECORD));
        orderRefundRecordDO.setOrderGuid(Long.valueOf(orderRefundReqDTO.getOrderGuid()));
        orderRefundRecordDO.setRecordNo(Objects.nonNull(maxRecordNo) ? maxRecordNo + 1 : generateRefundRecordNo(refundOrderNo));
        orderRefundRecordDO.setRefundOrderGuid(refundOrderGuid);
        orderRefundRecordDO.setRefundType(orderRefundReqDTO.getRefundType());
        orderRefundRecordDO.setRefundDetails(RefundTypeEnum.OFFLINE_REFUND.getDesc());
        if (RefundTypeEnum.BACKTRACK.getCode() == orderRefundReqDTO.getRefundType()) {
            // 原路退回 支付明细
            List<AmountItemDTO> refundDetailList = generateAmountItemList(transactionRecordList);
            orderRefundRecordDO.setRefundDetails(JacksonUtils.writeValueAsString(refundDetailList));
        }
        orderRefundRecordDO.setRefundAmount(orderRefundReqDTO.getRefundAmount());
        orderRefundRecordDO.setRefundReason(orderRefundReqDTO.getRefundReason());
        orderRefundRecordDO.setAuthStaffGuid(orderRefundReqDTO.getAuthStaffGuid());
        orderRefundRecordDO.setAuthStaffName(orderRefundReqDTO.getAuthStaffName());
        orderRefundRecordDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        orderRefundRecordDO.setCreateStaffName(UserContextUtils.getUserName());
        orderRefundRecordDO.setDeviceType(orderRefundReqDTO.getDeviceType());
        orderRefundRecordDO.setPicture(orderRefundReqDTO.getPicture());
        return orderRefundRecordDO;
    }

    /**
     * 生成退款支付明细
     */
    private List<AmountItemDTO> generateAmountItemList(List<TransactionRecordDO> transactionRecordList) {
        if (CollectionUtils.isEmpty(transactionRecordList)) {
            return Collections.emptyList();
        }
        return transactionRecordList.stream().map(transactionRecord -> {
            AmountItemDTO amountItemDTO = new AmountItemDTO();
            amountItemDTO.setCode(transactionRecord.getPaymentType());
            amountItemDTO.setName(transactionRecord.getPaymentTypeName());
            amountItemDTO.setAmount(transactionRecord.getAmount());
            return amountItemDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 生成退款订单记录编号
     */
    private Long generateRefundRecordNo(String refundOrderNo) {
        String recordNo = refundOrderNo.replace(REFUND_MARK + "1", Strings.EMPTY);
        recordNo = recordNo.substring(recordNo.length() - 12);
        recordNo = recordNo + "0001";
        return Long.valueOf(recordNo);
    }


    /**
     * 构建附加费商品明细
     */
    private List<DineInItemDTO> buildAppendFeeItemList(DineinOrderDetailRespDTO orderInfo) {
        OrderFeeDetailDTO orderFeeDetail = orderInfo.getOrderFeeDetailDTO();
        if (Objects.isNull(orderFeeDetail) || CollectionUtils.isEmpty(orderFeeDetail.getAppendFeeDetailDTOS())) {
            return Lists.newArrayList();
        }
        List<AppendFeeDetailDTO> appendFeeDetailList = orderFeeDetail.getAppendFeeDetailDTOS();
        return appendFeeDetailList.stream().map(appendFeeDetailDTO -> {
            DineInItemDTO itemDTO = new DineInItemDTO();
            // guid为附加费明细表中的guid
            itemDTO.setGuid(appendFeeDetailDTO.getGuid().toString());
            itemDTO.setOrderGuid(String.valueOf(appendFeeDetailDTO.getOrderGuid()));
            itemDTO.setItemName(appendFeeDetailDTO.getName());
            itemDTO.setRefundCount(Objects.isNull(appendFeeDetailDTO.getRefundCount()) ? BigDecimal.ZERO : appendFeeDetailDTO.getRefundCount());
            itemDTO.setCurrentCount(appendFeeDetailDTO.getAmount().divide(appendFeeDetailDTO.getUnitPrice(), 2, RoundingMode.HALF_UP));
            itemDTO.setCurrentCount(itemDTO.getCurrentCount().subtract(itemDTO.getRefundCount()));
            itemDTO.setFreeCount(BigDecimal.ZERO);
            itemDTO.setPrice(appendFeeDetailDTO.getUnitPrice());
            itemDTO.setItemPrice(appendFeeDetailDTO.getUnitPrice().multiply(itemDTO.getCurrentCount()));
            itemDTO.setItemType(ItemTypeEnum.SINGLE.getCode());
            if (BigDecimalUtil.greaterThanZero(itemDTO.getCurrentCount())) {
                // 实付金额
                // 优惠金额
                if (BigDecimalUtil.greaterThanZero(itemDTO.getRefundCount())) {
                    // 存在退款
                    // 剩余可退总金额
                    BigDecimal availableRefundAmount = appendFeeDetailDTO.getAmount()
                            .subtract(Optional.ofNullable(appendFeeDetailDTO.getDiscountAmount()).orElse(BigDecimal.ZERO))
                            .subtract(appendFeeDetailDTO.getRefundAmount());
                    itemDTO.setDiscountTotalPrice(availableRefundAmount);
                    itemDTO.setPrice(itemDTO.getDiscountTotalPrice().divide(itemDTO.getCurrentCount(), 2, RoundingMode.HALF_UP));
                    itemDTO.setDiscountPrice(itemDTO.getPrice());
                    return itemDTO;
                }
                if (BigDecimalUtil.greaterThanZero(appendFeeDetailDTO.getDiscountAmount())) {
                    // 实付单价
                    itemDTO.setDiscountTotalPrice(itemDTO.getItemPrice().subtract(appendFeeDetailDTO.getDiscountAmount()));
                    itemDTO.setDiscountPrice(itemDTO.getDiscountTotalPrice().divide(itemDTO.getCurrentCount(), 2, RoundingMode.HALF_UP));
                    itemDTO.setPrice(itemDTO.getDiscountPrice());
                }
            }
            return itemDTO;
        }).filter(e -> e.getCurrentCount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
    }

    /**
     * 查询当前时间当前门店的营业日
     */
    private LocalDate getBusinessDay() {
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        ArrayList<String> storeGuidList = new ArrayList<>();
        storeGuidList.add(UserContextUtils.getStoreGuid());
        reqDTO.setStoreGuidList(storeGuidList);
        reqDTO.setQueryDateTime(LocalDateTime.now());
        return storeClientService.queryBusinessDay(reqDTO);
    }

    /**
     * 持久化
     */
    private void persistenceRefundOrderBiz(RefundOrderBO refundOrderBO) {
        OrderDO order = refundOrderBO.getOrder();
        if (Objects.nonNull(order)) {
            orderService.saveOrUpdate(order);
        }
        OrderExtendsDO orderExtendsDO = refundOrderBO.getOrderExtends();
        if (Objects.nonNull(orderExtendsDO)) {
            orderExtendsService.saveOrUpdate(orderExtendsDO);
        }
        List<OrderItemDO> orderItemList = refundOrderBO.getOrderItemList();
        if (CollectionUtils.isNotEmpty(orderItemList)) {
            orderItemService.saveBatchWithDeleteCache(orderItemList, String.valueOf(refundOrderBO.getRefundOrderGuid()));
        }
        List<OrderItemExtendsDO> orderItemExtendsList = refundOrderBO.getOrderItemExtendsList();
        if (CollectionUtils.isNotEmpty(orderItemExtendsList)) {
            orderItemExtendsService.saveBatch(orderItemExtendsList);
        }
        List<OrderItemChangesDO> orderItemChangesList = refundOrderBO.getOrderItemChangesList();
        if (CollectionUtils.isNotEmpty(orderItemChangesList)) {
            orderItemChangesService.saveBatch(orderItemChangesList);
        }
        List<AppendFeeDO> appendFeeList = refundOrderBO.getAppendFeeList();
        if (CollectionUtils.isNotEmpty(appendFeeList)) {
            appendFeeMpService.saveBatch(appendFeeList);
        }
        List<FreeReturnItemDO> freeItemList = refundOrderBO.getFreeItemList();
        if (CollectionUtils.isNotEmpty(freeItemList)) {
            freeReturnItemService.saveBatch(freeItemList);
        }
        List<ItemAttrDO> itemAttrList = refundOrderBO.getItemAttrList();
        if (CollectionUtils.isNotEmpty(itemAttrList)) {
            itemAttrService.saveBatch(itemAttrList);
        }
        List<ItemAttrDO> changeItemAttrList = refundOrderBO.getChangeItemAttrList();
        if (CollectionUtils.isNotEmpty(changeItemAttrList)) {
            itemAttrService.saveBatch(changeItemAttrList);
        }
        List<OrderItemRecordDO> orderItemRecordList = refundOrderBO.getOrderItemRecordList();
        if (CollectionUtils.isNotEmpty(orderItemRecordList)) {
            orderItemRecordService.saveBatch(orderItemRecordList);
        }
        List<TransactionRecordDO> transactionRecordList = refundOrderBO.getTransactionRecordList();
        if (CollectionUtils.isNotEmpty(transactionRecordList)) {
            transactionRecordService.saveBatch(transactionRecordList);
        }
        List<MultipleTransactionRecordDO> multipleTransactionRecordDOList = refundOrderBO.getMultipleTransactionRecordList();
        if (CollectionUtils.isNotEmpty(multipleTransactionRecordDOList)) {
            multipleTransactionRecordService.saveBatch(multipleTransactionRecordDOList);
        }
        OrderRefundRecordDO orderRefundRecord = refundOrderBO.getOrderRefundRecord();
        if (Objects.nonNull(orderRefundRecord)) {
            orderRefundRecordService.save(orderRefundRecord);
        }
        List<FreeReturnItemDO> refundItemList = refundOrderBO.getRefundItemList();
        if (CollectionUtils.isNotEmpty(refundItemList)) {
            freeReturnItemService.saveBatch(refundItemList);
        }
    }

    /**
     * 更新原订单
     */
    private void updateOriginalOrder(OrderRefundReqDTO orderRefundReqDTO, RefundOrderBO refundOrderBO) {
        String orderGuid = orderRefundReqDTO.getOrderGuid();
        // 更新原订单退款金额
        orderService.updateRefundAmountByGuid(Long.valueOf(orderGuid), refundOrderBO.getOrder().getActuallyPayFee());
        // 更新原订单绑定的退款订单guid
        orderService.addRefundOrderGuid(Long.valueOf(orderGuid), refundOrderBO.getRefundOrderGuid());
        // 订单明细
        List<DineInItemDTO> dineInItemList = orderRefundReqDTO.getDineInItemDTOS();
        Map<String, String> dineInItemByGuidMap = dineInItemList.stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, DineInItemDTO::getOrderGuid, (existingValue, newValue) -> existingValue));

        // 更新原订单商品明细的已退数量
        List<OrderItemDO> orderItemList = refundOrderBO.getOrderItemList();
        if (CollectionUtils.isNotEmpty(orderItemList)) {
            List<OrderItemDO> originalOrderItemList = orderItemList.stream().map(refundOrderItem -> {
                OrderItemDO originalOrderItem = new OrderItemDO();
                originalOrderItem.setGuid(refundOrderItem.getOriginalOrderItemGuid());
                String originalOrderGuid = dineInItemByGuidMap.getOrDefault(String.valueOf(originalOrderItem.getGuid()), orderGuid);
                originalOrderItem.setOrderGuid(Long.valueOf(originalOrderGuid));
                originalOrderItem.setRefundCount(refundOrderItem.getCurrentCount().add(refundOrderItem.getFreeCount()));
                originalOrderItem.setFreeRefundCount(refundOrderItem.getFreeCount());
                return originalOrderItem;
            }).collect(Collectors.toList());
            log.info("更新原订单商品数据:{}", JacksonUtils.writeValueAsString(originalOrderItemList));
            for (OrderItemDO orderItemDO : originalOrderItemList) {
                orderItemService.updateRefundCount(orderItemDO.getOrderGuid(), orderItemDO.getGuid(),
                        orderItemDO.getRefundCount(), orderItemDO.getFreeRefundCount());
            }
        }
        // 更新原订单附加费金额
        List<AppendFeeDO> appendFeeList = refundOrderBO.getAppendFeeList();
        if (CollectionUtils.isNotEmpty(appendFeeList)) {
            for (AppendFeeDO appendFeeDO : appendFeeList) {
                appendFeeMpService.updateRefundAmountByOrderGuid(orderGuid, appendFeeDO);
            }
        }
        // 更新原订单赠菜退款数量
        List<FreeReturnItemDO> freeItemList = refundOrderBO.getFreeItemList();
        if (CollectionUtils.isNotEmpty(freeItemList)) {
            for (FreeReturnItemDO freeReturnItemDO : freeItemList) {
                freeReturnItemService.updateRefundCountByGuid(freeReturnItemDO.getOriginalGuid(), freeReturnItemDO.getCount());
            }
        }
        // 如果原路退回 更新原订单交易记录的退款金额
        List<TransactionRecordDO> transactionRecordList = refundOrderBO.getTransactionRecordList();
        if (RefundTypeEnum.BACKTRACK.getCode() == orderRefundReqDTO.getRefundType() && CollectionUtils.isNotEmpty(transactionRecordList)) {
            for (TransactionRecordDO transactionRecordDO : transactionRecordList) {
                transactionRecordService.updateRefundAmountByOrderGuid(orderGuid, transactionRecordDO.getPaymentType(),
                        transactionRecordDO.getPaymentTypeName(), transactionRecordDO.getAmount().abs());
            }
        }
    }


    /**
     * 原路退回
     * 会员余额支付
     * 团购验券
     * 挂账支付
     * 聚合支付
     */
    private void backtrack(OrderRefundReqDTO orderRefundReqDTO, List<TransactionRecordDO> transactionRecordList) {
        if (orderRefundReqDTO.getRefundType() != RefundTypeEnum.BACKTRACK.getCode()) {
            return;
        }
        // 会员余额：回退会员消费时判断是否使用了会员余额
        // 团购验券
        returnCoupon(orderRefundReqDTO, transactionRecordList);
        // 挂账支付
        returnDebtPay(orderRefundReqDTO, transactionRecordList);
        // 预定金支付
        returnReservePay(orderRefundReqDTO, transactionRecordList);
        // 聚合支付
        returnAggPay(orderRefundReqDTO, transactionRecordList);
    }

    /**
     * 回退团购验券
     */
    private void returnCoupon(OrderRefundReqDTO orderRefundReqDTO, List<TransactionRecordDO> transactionRecordList) {
        // 团购验券原路返回 只能整单全退
        TransactionRecordDO grouponTransaction = transactionRecordList.stream()
                .filter(e -> PaymentTypeEnum.getFilterGrouponPaymentTypes().contains(e.getPaymentType()))
                .findFirst().orElse(null);
        if (Objects.isNull(grouponTransaction)) {
            return;
        }
        // 团购验券原记录不删除 只标记退款订单
        grouponMpService.appendRefundOrderGuid(Long.valueOf(orderRefundReqDTO.getOrderGuid()), orderRefundReqDTO.getRefundOrderGuid());
        // 只退券
        List<GrouponDO> grouponList = grouponMpService.listByOrderGuid(orderRefundReqDTO.getOrderGuid());
        for (GrouponDO grouponDO : grouponList) {
            GroupVerifyDTO groupVerifyDTO = GrouponConverter.fromGroupRecord(grouponDO);
            log.info("订单部分退款调用第三方撤销验券参数:{}", JacksonUtils.writeValueAsString(groupVerifyDTO));
            try {
                MtDelCouponRespDTO mtDelCouponRespDTO = groupClientService.revokeCoupon(groupVerifyDTO);
                log.info("订单部分退款调用第三方撤销验券返回:{}", JacksonUtils.writeValueAsString(mtDelCouponRespDTO));
            } catch (Exception e) {
                log.error("订单部分退款调用第三方撤销验券失败, ", e);
            }
        }
    }

    /**
     * 回退会员支付
     */
    private void returnMemberPay(OrderRefundReqDTO orderRefundReqDTO, RefundOrderBO refundOrderBO) {
        // 退款金额
        BigDecimal refundAmount = orderRefundReqDTO.getRefundAmount();
        // 原路返回
        if (RefundTypeEnum.BACKTRACK.getCode() == orderRefundReqDTO.getRefundType()) {
            ActuallyPayFeeDetailDTO memberRefundRequest = orderRefundReqDTO.getRefundTypeList()
                    .stream()
                    .filter(e -> PaymentTypeEnum.MEMBER.getCode() == e.getPaymentType()).findFirst()
                    .orElse(null);
            if (Objects.isNull(memberRefundRequest) || memberRefundRequest.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                refundAmount = BigDecimal.ZERO;
            } else {
                refundAmount = memberRefundRequest.getAmount();
            }
        }
        // 查询订单
        OrderDO order = orderService.getById(orderRefundReqDTO.getOrderGuid());
        if (CommonUtil.hasGuid(order.getMemberConsumptionGuid()) || CommonUtil.hasGuid(order.getCanteenConsumptionGuid())) {
            RequestRefundPay refundPay = new RequestRefundPay();
            refundPay.setCanteenPayFlag(false);
            refundPay.setRefundType(orderRefundReqDTO.getRefundType());
            refundPay.setRefundAmount(refundAmount);
            refundPay.setMemberConsumptionGuid(order.getMemberConsumptionGuid());
            if (CommonUtil.hasGuid(order.getCanteenConsumptionGuid())) {
                refundPay.setMemberConsumptionGuid(order.getCanteenConsumptionGuid());
                refundPay.setCanteenPayFlag(true);
            }
            // 构建支付方式
            List<RequestPayInfo> requestPayInfoList = refundOrderBO.getTransactionRecordList().stream().map(e -> {
                RequestPayInfo requestPayInfo = new RequestPayInfo();
                requestPayInfo.setPayWay(mapping(e.getPaymentType()));
                requestPayInfo.setPayName(e.getPaymentTypeName());
                requestPayInfo.setPayCode(String.valueOf(requestPayInfo.getPayWay()));
                requestPayInfo.setPayAmount(e.getAmount());
                return requestPayInfo;
            }).collect(Collectors.toList());
            refundPay.setRequestPayInfoList(requestPayInfoList);
            // 订单编号
            refundPay.setOrderNo(refundOrderBO.getOrder().getOrderNo());
            log.info("会员消费部分退款,入参:{}", JacksonUtils.writeValueAsString(refundPay));
            memberTerminalClientService.refundPay(refundPay);
        }
    }

    /**
     * 回退麓豆支付
     */
    private void returnLudouPay(OrderRefundReqDTO orderRefundReqDTO) {
        List<TransactionRecordDTO> transactionRecordList = orderRefundReqDTO.getAllTransactionRecordList();
        TransactionRecordDTO ludouPayTransactionRecordDTO = transactionRecordList.stream()
                .filter(e -> PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode() == e.getPaymentType()
                        && StateEnum.SUCCESS.getCode() == e.getState())
                .findFirst()
                .orElse(null);
        if (Objects.isNull(ludouPayTransactionRecordDTO)) {
            return;
        }
        log.info("麓豆支付部分退款, ludouPayTransactionRecordDTO:{}", JacksonUtils.writeValueAsString(ludouPayTransactionRecordDTO));
        // 总退款金额
        BigDecimal refundTotalAmount = orderRefundReqDTO.getRefundAmount();
        // 麓豆退款金额
        BigDecimal ludouRefundAmount = BigDecimal.ZERO;
        // 原路返回
        if (RefundTypeEnum.BACKTRACK.getCode() == orderRefundReqDTO.getRefundType()) {
            ActuallyPayFeeDetailDTO ludouMemberRefundRequest = orderRefundReqDTO.getRefundTypeList()
                    .stream()
                    .filter(e -> PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode() == e.getPaymentType())
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(ludouMemberRefundRequest)) {
                ludouRefundAmount = ludouMemberRefundRequest.getAmount();
            }
        }
        ludouMemberExternalService.refund(orderRefundReqDTO.getOrderGuid(), ludouPayTransactionRecordDTO.getBankTransactionId(),
                refundTotalAmount, ludouRefundAmount, orderRefundReqDTO.getRefundReason());
    }

    /**
     * 回退挂账支付
     */
    private void returnDebtPay(OrderRefundReqDTO orderRefundReqDTO, List<TransactionRecordDO> transactionRecordList) {
        TransactionRecordDO debtPayTransaction = transactionRecordList.stream()
                .filter(e -> PaymentTypeEnum.DEBT_PAY.getCode() == e.getPaymentType())
                .findFirst().orElse(null);
        if (Objects.isNull(debtPayTransaction)) {
            return;
        }
        log.info("挂账支付部分退款,入参:{}", JacksonUtils.writeValueAsString(orderRefundReqDTO));
        DebtUnitRecordSaveReqDTO refundReq = new DebtUnitRecordSaveReqDTO();
        refundReq.setOrderGuid(orderRefundReqDTO.getOrderGuid());
        refundReq.setRefundOrderGuid(String.valueOf(orderRefundReqDTO.getRefundOrderGuid()));
        refundReq.setDebtFee(debtPayTransaction.getAmount().abs());
        debtUnitRecordService.refundDebtUnit(refundReq);
    }

    /**
     * 回退预定金支付
     */
    private void returnReservePay(OrderRefundReqDTO orderRefundReqDTO, List<TransactionRecordDO> transactionRecordList) {
        TransactionRecordDO reservePayTransaction = transactionRecordList.stream()
                .filter(e -> PaymentTypeEnum.RESERVE.getCode() == e.getPaymentType())
                .findFirst().orElse(null);
        if (Objects.isNull(reservePayTransaction)) {
            return;
        }
        log.info("预定金支付部分退款,入参:{}", JacksonUtils.writeValueAsString(orderRefundReqDTO));
        ReserveRefundDTO reserveRefundDTO = new ReserveRefundDTO();
        reserveRefundDTO.setGuid(orderRefundReqDTO.getReserveGuid());
        reserveRefundDTO.setReserveAmount(reservePayTransaction.getAmount().abs());
        BigDecimal refundAmount = reserveClientService.partRefund(reserveRefundDTO);
        log.info("预定金部分退款返回结果, refundAmount:{}", refundAmount);
    }

    /**
     * 回退库存
     */
    private void returnStock(RefundOrderBO refundOrderBO) {
        LocalDate businessDay = refundOrderBO.getRefundBusinessDay();
        List<OrderItemDO> orderItemList = refundOrderBO.getOrderItemList();
        Long recordNo = refundOrderBO.getOrderRefundRecord().getRecordNo();
        UserContext userContext = UserContextUtils.get();
        taskExecutor.execute(() -> {
            UserContextUtils.putErpAndStore(userContext.getEnterpriseGuid(), userContext.getStoreGuid());
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            // 菜品信息
            List<DineInItemDTO> dineInItemList = AmountCalculationUtil.buildItem(orderItemList, Lists.newArrayList(), Lists.newArrayList());
            log.info("订单信息：{}", JacksonUtils.writeValueAsString(dineInItemList));
            Map<String, BigDecimal> dineInItemMap = ItemUtil.itemEstimateHander(dineInItemList);

            // 通过商品skuGuid查询sku基本信息
            List<SkuInfoRespDTO> skus = itemClientService.listSkuInfo(new ArrayList<>(dineInItemMap.keySet()));
            log.info("通过商品skuGuid查询sku基本信息：{}", JacksonUtils.writeValueAsString(skus));
            Map<String, SkuInfoRespDTO> skuMap = skus.stream()
                    .collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key2));

            List<DepositDish> dishes = new ArrayList<>();
            dineInItemMap.forEach((skuGuid, count) -> {
                SkuInfoRespDTO skuInfo = skuMap.get(skuGuid);
                if (Objects.nonNull(skuInfo)) {
                    DepositDish dish = new DepositDish();
                    // erp库存需要根据品牌库sku进行操作
                    dish.setSkuId(skuInfo.getParentGuid());
                    dish.setItemName(skuInfo.getItemName());
                    dish.setSkuName(skuInfo.getName());
                    dish.setTypeName(skuInfo.getTypeName());
                    dish.setPrice(skuInfo.getSalePrice());
                    dish.setSkuCount(count);
                    dish.setUnit(skuInfo.getUnit());
                    dish.setItemType(skuInfo.getItemType());
                    // 0 入库 1 出库
                    dish.setType(0);
                    dishes.add(dish);
                }
            });
            if (CollectionUtils.isEmpty(dishes)) {
                return;
            }
            DepositErpSyncDTO erpSyncDTO = new DepositErpSyncDTO();
            // 冗余字段
            OrderDO originalOrder = refundOrderBO.getOriginalOrder();
            OrderDO refundOrder = refundOrderBO.getOrder();
            erpSyncDTO.setDiningTableName(originalOrder.getDiningTableName());
            erpSyncDTO.setOrderFee(originalOrder.getOrderFee().subtract(refundOrder.getOrderFee()));
            erpSyncDTO.setActuallyPayFee(originalOrder.getActuallyPayFee().subtract(refundOrder.getActuallyPayFee()));
            erpSyncDTO.setGuestCount(originalOrder.getGuestCount());
            erpSyncDTO.setTakeawayFlag(false);
            erpSyncDTO.setOrderCreateTime(originalOrder.getGmtCreate());
            // 传参
            erpSyncDTO.setBusinessType(1);
            erpSyncDTO.setBusinessDate(businessDay);
            erpSyncDTO.setOrderGuid(String.valueOf(originalOrder.getGuid()));
            erpSyncDTO.setThirdNo(String.valueOf(recordNo));
            erpSyncDTO.setStoreId(userContext.getStoreGuid());
            erpSyncDTO.setUserGuid(userContext.getUserGuid());
            erpSyncDTO.setUsername(userContext.getUserName());
            erpSyncDTO.setDepositDishes(dishes);
            executeStockReduceService.executeStockAdjust(erpSyncDTO);
        });
    }

    /**
     * 回退聚合支付
     */
    private void returnAggPay(OrderRefundReqDTO orderRefundReqDTO, List<TransactionRecordDO> transactionRecordList) {
        // 回退聚合支付有两种情况，1. 1笔订单只有单笔聚合支付， 2. 1笔订单存在多笔聚合支付
        TransactionRecordDO aggPayTransaction = transactionRecordList.stream()
                .filter(e -> PaymentTypeEnum.AGG.getCode() == e.getPaymentType())
                .findFirst().orElse(null);
        if (Objects.isNull(aggPayTransaction)) {
            return;
        }
        TransactionRecordDTO oldTransactionRecord = orderRefundReqDTO.getTransactionRecordList().stream()
                .filter(e -> e.getPaymentType() == PaymentTypeEnum.AGG.getCode())
                .findFirst().orElse(new TransactionRecordDTO());
        if (CollectionUtils.isNotEmpty(orderRefundReqDTO.getMultipleTransactionRecordList())) {
            oldTransactionRecord = orderRefundReqDTO.getMultipleTransactionRecordList().stream()
                    .filter(e -> e.getPaymentType() == PaymentTypeEnum.AGG.getCode())
                    .findFirst().orElse(new TransactionRecordDTO());
        }
        if (Objects.isNull(oldTransactionRecord.getGuid())) {
            log.error("聚合支付退款异常，参数丢失， orderGuid：{}", orderRefundReqDTO.getOrderGuid());
            throw new BusinessException("聚合支付退款异常，参数丢失");
        }
        // 构建退款请求参数
        AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setOrderGUID(String.valueOf(orderRefundReqDTO.getOrderGuid()));
        aggRefundReqDTO.setRefundType(1);
        aggRefundReqDTO.setRefundFee(aggPayTransaction.getAmount().abs());
        aggRefundReqDTO.setReason("聚合支付部分退款");
        aggRefundReqDTO.setPayGUID(String.valueOf(oldTransactionRecord.getGuid()));
        SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        BeanUtils.copyProperties(orderRefundReqDTO, saasAggRefundDTO);
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        log.info("聚合订单部分退款入参：{}", JacksonUtils.writeValueAsString(saasAggRefundDTO));
        AggRefundRespDTO refund = aggPayClientService.portionRefund(saasAggRefundDTO);
        BillVerifyHelper.refundResult(refund, true);
    }

    /**
     * 退估清
     */
    private void returnEstimate(List<DineInItemDTO> dineInItemList) {
        if (CollectionUtils.isEmpty(dineInItemList)) {
            return;
        }
        // 排除附加费商品
        dineInItemList = dineInItemList.stream().filter(e -> !StringUtils.isEmpty(e.getSkuGuid())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dineInItemList)) {
            List<DineInItemDTO> finalDineInItemList = dineInItemList;
            UserContext userContext = UserContextUtils.get();
            taskExecutor.execute(() -> {
                UserContextUtils.putErpAndStore(userContext.getEnterpriseGuid(), userContext.getStoreGuid());
                itemClientService.returnEstimate(finalDineInItemList);
            });
        }
    }

    private Integer mapping(Integer payWay) {
        if (payWay == PaymentTypeEnum.OTHER.getCode()) {
            return 5;
        }
        if (payWay == PaymentTypeEnum.DEBT_PAY.getCode()) {
            return 8;
        }
        if (payWay == PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode() || payWay == PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode()
                || payWay == PaymentTypeEnum.THIRD_ACTIVITY.getCode() || payWay == PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode()) {
            return payWay;
        }
        return payWay - 1;
    }

    private void checkRefundTimeLimit(DineinOrderDetailRespDTO availableRefundDetail) {
        String storeGuid = availableRefundDetail.getStoreGuid();
        if (StringUtils.isBlank(storeGuid)) {
            return;
        }
        BrandConfigDTO brandConfigDTO = businessClientService.getBrandConfigByStoreGuid(storeGuid);
        log.info("品牌配置返回参数: {}", JacksonUtils.writeValueAsString(brandConfigDTO));
        if (Objects.isNull(brandConfigDTO)) {
            return;
        }
        Integer refundTimeLimit = brandConfigDTO.getRefundTimeLimit();
        Integer refundTimeLimitUnit = brandConfigDTO.getRefundTimeLimitUnit();
        if (Objects.isNull(refundTimeLimit)
                || Objects.isNull(refundTimeLimitUnit)) {
            return;
        }
        if (DateUtil.checkRefundTimeLimit(availableRefundDetail.getCheckoutTime(), refundTimeLimit, refundTimeLimitUnit)) {
            throw new BusinessException("超时不可操作");
        }
    }

    public Boolean refundTimeLimit(String orderGuid) {
        // 订单不存在则超时
        if (StringUtils.isEmpty(orderGuid)) {
            return true;
        }
        OrderDO orderDO = orderService.getById(orderGuid);
        if (Objects.isNull(orderDO)) {
            return true;
        }
        // 未查询出时效限制则配置则不超时
        BrandConfigDTO brandConfigDTO = businessClientService.getBrandConfigByStoreGuid(orderDO.getStoreGuid());
        log.info("品牌配置返回参数: {}", JacksonUtils.writeValueAsString(brandConfigDTO));
        if (Objects.isNull(brandConfigDTO)) {
            return false;
        }
        Integer refundTimeLimit = brandConfigDTO.getRefundTimeLimit();
        Integer refundTimeLimitUnit = brandConfigDTO.getRefundTimeLimitUnit();
        if (Objects.isNull(refundTimeLimit)
                || Objects.isNull(refundTimeLimitUnit)) {
            return false;
        }
        // 检查是否超时
        return DateUtil.checkRefundTimeLimit(orderDO.getCheckoutTime(), refundTimeLimit, refundTimeLimitUnit);
    }
}