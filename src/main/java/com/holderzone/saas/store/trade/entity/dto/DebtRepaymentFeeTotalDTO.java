package com.holderzone.saas.store.trade.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> R
 * @date 2021/1/7 10:17
 * @description
 */
@Data
public class DebtRepaymentFeeTotalDTO {
    /**
     * 结算方式
     */
    private Integer paymentType;
    /**
     * 还款金额汇总
     */
    private BigDecimal repaymentFeeTotal;
    /***
     * 订单数
     */
    private Integer repaymentFeeCount;
}
