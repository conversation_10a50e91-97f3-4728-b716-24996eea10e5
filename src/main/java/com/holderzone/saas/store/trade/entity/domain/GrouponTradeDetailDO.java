package com.holderzone.saas.store.trade.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 团购订单结算明细
 */
@Data
@TableName("hst_groupon_trade_detail")
public class GrouponTradeDetailDO implements Serializable {

    private static final long serialVersionUID = 2325781618061731291L;

    @TableId(value = "guid", type = IdType.INPUT)
    private Long guid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 券来源 0:美团 1：饿了么
     */
    private Integer source;

    /**
     * 业务系统订单guid
     */
    private String orderGuid;

    /**
     * 业务系统订单单号
     */
    private String orderNo;

    /**
     * 业务系统门店GUID
     */
    private String storeGuid;

    /**
     * 三方门店GUID
     */
    private String thirdStoreGuid;

    /**
     * 三方门店名字
     */
    private String thirdStoreName;

    /**
     * 三方订单id
     */
    private Long orderId;

    /**
     * 券码
     */
    private String couponCode;

    /**
     * 券状态
     */
    private String couponStatusDesc;

    /**
     * 项目id
     */
    private Integer dealId;

    /**
     * 项目名称
     */
    private String dealTitle;

    /**
     * 券码使用时间
     */
    private String couponUseTime;

    /**
     * 项目开始时间
     */
    private String dealBeginTime;

    /**
     * 券码是否可撤销，1表示可撤销，0表示不可撤销
     */
    private Integer couponCancelStatus;

    /**
     * 团购券码购买价格
     */
    private BigDecimal couponBuyPrice;

    /**
     * 券进价，即美团从商家获取的团购券结算批发价，也是开店宝中的“顾客实付”-“服务费”
     */
    private BigDecimal buyPrice;

    /**
     * 市场价
     */
    private BigDecimal dealValue;

    /**
     * 商家促销金额
     */
    private BigDecimal bizCost;

    /**
     * 商家预计应得金额
     */
    private BigDecimal due;

    /**
     * 验券帐号
     */
    private String verifyAcct;

    /**
     * 验券方式
     */
    private String verifyType;

    /**
     * 是否代金券，true代表代金券,false代表套餐券
     */
    private Boolean isVoucher;

    /**
     * 是否量贩：0：不是，1：是
     */
    private Integer volume;

    /**
     * 量贩项目的单张券原价（普通券单张券原价与项目总价相同）
     */
    private BigDecimal singleValue;

    /**
     * 是否撤销 0：否 1：是
     */
    private Integer cancel;
}
