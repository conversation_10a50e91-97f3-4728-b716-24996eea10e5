package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.trade.entity.domain.OrderMultiMember;
import com.holderzone.saas.store.trade.mapper.OrderMultiMemberMapper;
import com.holderzone.saas.store.trade.repository.interfaces.OrderMultiMemberService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class OrderMultiMemberServiceImpl extends ServiceImpl<OrderMultiMemberMapper, OrderMultiMember> implements OrderMultiMemberService {


    @Override
    public List<OrderMultiMember> listByOrderGuid(Long orderGuid) {
        LambdaQueryWrapper<OrderMultiMember> qw = new LambdaQueryWrapper<OrderMultiMember>()
                .eq(OrderMultiMember::getOrderGuid, orderGuid);
        return list(qw);
    }

    @Override
    public void updateBatchPayAmount(List<OrderMultiMember> orderMultiMembers) {
        if (CollectionUtils.isEmpty(orderMultiMembers)) {
            return;
        }
        baseMapper.updateBatchPayAmount(orderMultiMembers);
    }

    @Override
    public void removeByOrderGuid(Long orderGuid) {
        LambdaUpdateWrapper<OrderMultiMember> uw = new LambdaUpdateWrapper<OrderMultiMember>()
                .eq(OrderMultiMember::getOrderGuid, orderGuid);
        remove(uw);
    }

    @Override
    public void removeByOrderGuidAndMemberCardGuid(Long orderGuid, String memberCardGuid) {
        LambdaUpdateWrapper<OrderMultiMember> uw = new LambdaUpdateWrapper<OrderMultiMember>()
                .eq(OrderMultiMember::getOrderGuid, orderGuid)
                .eq(OrderMultiMember::getMemberCardGuid, memberCardGuid);
        remove(uw);
    }

    @Override
    public List<OrderMultiMember> listByOrderGuidList(List<String> orderGuidList) {
        if (CollectionUtils.isEmpty(orderGuidList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderMultiMember> qw = new LambdaQueryWrapper<OrderMultiMember>()
                .eq(OrderMultiMember::getIsDelete, false)
                .in(OrderMultiMember::getOrderGuid, orderGuidList);
        return list(qw);
    }
}
