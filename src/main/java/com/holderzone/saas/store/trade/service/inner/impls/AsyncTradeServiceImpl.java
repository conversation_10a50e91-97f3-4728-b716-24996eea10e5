package com.holderzone.saas.store.trade.service.inner.impls;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.TraceContextUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseConfirmMultiPay;
import com.holderzone.holder.saas.member.terminal.dto.volume.RequestUpdateVolumeRelevance;
import com.holderzone.holder.saas.member.wechat.dto.activitie.GrouponListRespDTO;
import com.holderzone.holder.saas.member.wechat.dto.activitie.MemberConsumptionGiftDTO;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.marketing.specials.UniteActivityOrderPayDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberPayDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.member.RequestUpdateCouponDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountRuleDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.reserve.NotifyPayReqDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.marketing.OrderSourceEnum;
import com.holderzone.saas.store.enums.marketing.OrderTypeEnum;
import com.holderzone.saas.store.enums.marketing.QueryTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.config.OverallConfig;
import com.holderzone.saas.store.trade.config.ZhuanCanConfig;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;
import com.holderzone.saas.store.trade.entity.domain.TransactionRecordDO;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.repository.feign.*;
import com.holderzone.saas.store.trade.repository.interfaces.DiscountService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderService;
import com.holderzone.saas.store.trade.repository.interfaces.TransactionRecordService;
import com.holderzone.saas.store.trade.service.*;
import com.holderzone.saas.store.trade.service.ExecuteStockReduceService;
import com.holderzone.saas.store.trade.service.inner.interfaces.AsyncTradeService;
import com.holderzone.saas.store.trade.service.mq.OrderStatusMqService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.transform.TransactionRecordTransform;
import com.holderzone.saas.store.trade.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;


/**
 * {@link AsyncTradeServiceImpl}
 * 异步调用的时候 调该方法
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/16 13:56
 */
@Slf4j
@Service
public class AsyncTradeServiceImpl implements AsyncTradeService {

    @Value("${mdm.host:#{null}}")
    private String mdmRequestHost;

    @Resource(name = "checkOutThreadPool")
    private ExecutorService checkOutThreadPool;

    @Resource(name = "orderStatusThreadPool")
    private ExecutorService orderStatusThreadPool;

    @Resource(name = "asyncMemberMarketingOrderExecutor")
    private ExecutorService asyncMemberMarketingOrderExecutor;

    @Resource(name = "orderWaiterOrderFeeThreadPool")
    private ExecutorService orderWaiterOrderFeeThreadPool;

    @Resource(name = "memberMarketingExecutor")
    private ExecutorService memberMarketingThreadPool;

    private final ErpClientService erpClientService;

    private final DineInPrintService dineInPrintService;

    private final ItemClientService itemClientService;

    private final TableService tableService;

    private final KdsService kdsService;

    private final DynamicHelper dynamicHelper;

    private final OrderItemService orderItemService;

    private final OrderStatusMqService orderStatusMqService;

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    private final OverallConfig overallConfig;

    private final WxStoreClientService wxStoreClientService;

    private final OrderWaiterService orderWaiterService;

    private final DiscountService discountService;

    private final AppendFeeService appendFeeService;

    private final TransactionRecordService transactionRecordService;

    private final OrderDetailService orderDetailService;

    private final ReserveClientService reserveClientService;

    @Resource
    private BusinessClientService businessClient;

    @Resource
    private OrderService orderService;

    @Resource
    private MemberMerchantClientService memberMerchantClientService;

    @Resource
    private MemberMarketingClientService memberMarketingClientService;

    @Resource
    private MemberTerminalClientService memberInfoClientService;

    @Resource
    private ZhuanCanConfig zhuanCanConfig;

    @Resource
    private MemberTerminalClientService memberTerminalClientService;

    @Resource
    private DebtUnitRecordService debtUnitRecordService;

    @Resource
    private ExecuteStockReduceService executeStockReduceService;

    /**
     * 商品标签 0 ：否 1：是
     */
    private static final Integer ITEM_TAG = 1;

    private static final String NEW = "新品";
    private static final String SIGN = "招牌";
    private static final String BESTSELLER = "热销";

    public AsyncTradeServiceImpl(ExecutorService orderWaiterOrderFeeThreadPool,
                                 ErpClientService erpClientService,
                                 DineInPrintService dineInPrintService,
                                 ItemClientService itemClientService,
                                 TableService tableService,
                                 KdsService kdsService,
                                 DynamicHelper dynamicHelper,
                                 OrderItemService orderItemService,
                                 OrderStatusMqService orderStatusMqService,
                                 OverallConfig overallConfig,
                                 WxStoreClientService wxStoreClientService,
                                 OrderWaiterService orderWaiterService,
                                 DiscountService discountService,
                                 AppendFeeService appendFeeService,
                                 TransactionRecordService transactionRecordService,
                                 OrderDetailService orderDetailService,
                                 ReserveClientService reserveClientService) {
        this.orderWaiterOrderFeeThreadPool = orderWaiterOrderFeeThreadPool;
        this.erpClientService = erpClientService;
        this.dineInPrintService = dineInPrintService;
        this.itemClientService = itemClientService;
        this.tableService = tableService;
        this.kdsService = kdsService;
        this.dynamicHelper = dynamicHelper;
        this.orderItemService = orderItemService;
        this.orderStatusMqService = orderStatusMqService;
        this.overallConfig = overallConfig;
        this.wxStoreClientService = wxStoreClientService;
        this.orderWaiterService = orderWaiterService;
        this.discountService = discountService;
        this.appendFeeService = appendFeeService;
        this.transactionRecordService = transactionRecordService;
        this.orderDetailService = orderDetailService;
        this.reserveClientService = reserveClientService;
    }

    /**
     * 异步结账成功调用
     */
    @Override
    public void asyncCheckOutCall(BaseDTO baseDTO, OrderDO orderDO, DineinOrderDetailRespDTO orderDetail) {
        UserContext userContext = UserContextUtils.get();
        String traceId = TraceContextUtils.getTraceId();
        // 打印
        handleAggPaymentType(orderDetail);
        checkOutThreadPool.execute(() -> {
            UserContextUtils.put(userContext);
            TraceContextUtils.setTraceId(traceId);
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            printCheckOutCall(baseDTO, orderDO, orderDetail);
        });
        // 同步订单到赚餐
        asyncPushOrder(orderDO, userContext);
        // 订单服务员信息
        orderWaiterOrderFeeThreadPool.execute(() -> {
            UserContextUtils.put(userContext);
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            OrderDTO orderDTO = orderTransform.orderDO2DTO(orderDO);
            Boolean result = orderWaiterService.updateOrderWaiterFee(orderDTO);
            if (result) {
                log.info("更新成功！订单：{}", orderDTO.getGuid());
            } else {
                log.info("更新失败！订单：{}", orderDTO.getGuid());
            }

            //未登录下 释放未使用的优惠券
            updateOrderCoupon(orderDetail, userContext, orderDO);
        });
        // 参与营销活动订单推送
        asyncMemberMarketingOrder(orderDetail, userContext);
        // 支付成功预付金处理
        handleReservePay(orderDO);
    }

    private void handleAggPaymentType(DineinOrderDetailRespDTO orderDetail) {
        List<ActuallyPayFeeDetailDTO> payFeeDetailDTOList = orderDetail.getActuallyPayFeeDetailDTOS();
        Map<Object, List<ActuallyPayFeeDetailDTO>> paymentType = CollectionUtil.toListMap(payFeeDetailDTOList, "paymentType");
        List<ActuallyPayFeeDetailDTO> aggPayDetailDTOList = paymentType.get(PaymentTypeEnum.AGG.getCode());
        if (!CollectionUtils.isEmpty(aggPayDetailDTOList) && aggPayDetailDTOList.size() > 1) {
            ActuallyPayFeeDetailDTO transactionRecordDO = aggPayDetailDTOList.get(0);
            BigDecimal agg = aggPayDetailDTOList.stream()
                    .map(ActuallyPayFeeDetailDTO::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            transactionRecordDO.setAmount(agg);
            payFeeDetailDTOList.removeIf(pay -> pay.getPaymentType().equals(PaymentTypeEnum.AGG.getCode()));
            payFeeDetailDTOList.add(transactionRecordDO);
        }
    }

    /**
     * 支付成功预付金处理
     */
    private void handleReservePay(OrderDO orderDO) {
        if (StringUtils.hasText(orderDO.getReserveGuid())) {
            NotifyPayReqDTO reqDTO = new NotifyPayReqDTO();
            reqDTO.setReserveGuid(orderDO.getReserveGuid());
            if (StringUtils.hasText(orderDO.getMemberPhone())) {
                reqDTO.setMemberPhone(orderDO.getMemberPhone());
            }
            try {
                reserveClientService.notifyPay(reqDTO);
            } catch (Exception e) {
                log.error("[预付金支付成功通知]异常：{},e=｛｝", e.getMessage(), e);
            }
        }
    }

    private void updateOrderCoupon(DineinOrderDetailRespDTO orderDetail,
                                   UserContext userContext,
                                   OrderDO orderDO) {
        log.info("结账释放订单关联优惠券,订单={},会员={}", orderDetail.getGuid(), orderDetail.getMemberGuid());

        if ((StringUtils.isEmpty(orderDetail.getMemberGuid()) || orderDetail.getMemberGuid().equals("0"))
                && Objects.nonNull(orderDO.getIsUpdatedEs())
                && orderDO.getIsUpdatedEs() == BooleanEnum.TRUE.getCode()) {
            try {
                RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
                request.setOrderGuid(orderDetail.getGuid());
                request.setOperSubjectGuid(userContext.getOperSubjectGuid());
                log.info("结账门店优惠券关联修改入参：{}", JacksonUtils.writeValueAsString(request));
                //门店优惠券关联关系切换
                memberInfoClientService.updateVolumeRelevance(request);
                //赚餐优惠券关联关系切换
                RequestUpdateCouponDTO redeemCodeApplyDTO = new RequestUpdateCouponDTO();
                redeemCodeApplyDTO.setSource(Integer.valueOf(userContext.getSource()));
                redeemCodeApplyDTO.setStoreGuid(userContext.getStoreGuid());
                redeemCodeApplyDTO.setStoreName(userContext.getStoreName());
                redeemCodeApplyDTO.setOrderGuid(orderDetail.getGuid());
                redeemCodeApplyDTO.setEnterpriseGuid(userContext.getEnterpriseGuid());
                redeemCodeApplyDTO.setOperSubjectGuid(userContext.getOperSubjectGuid());
                log.info("结账赚餐优惠券关联修改入参：{}", JacksonUtils.writeValueAsString(redeemCodeApplyDTO));
                HttpsClientUtils.doPost(zhuanCanConfig.getUpdateVolumeRelevance(), JacksonUtils.writeValueAsString(redeemCodeApplyDTO));
            } catch (Exception e) {
                log.info("一体机结账优惠券关联修改失败：{}", e.getMessage());
            }
        }
    }

    private void dealConsumptionGift(DineinOrderDetailRespDTO orderDetail, UserContext userContext, DineinOrderDetailRespDTO orderDetailCopy) {
        try {
            log.info("userContext={}", JacksonUtils.writeValueAsString(userContext));
            if (!StringUtils.isEmpty(orderDetail.getMemberGuid())
                    && orderDetail.getActuallyPayFee().compareTo(BigDecimal.ZERO) > 0) {
                RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
                requestQueryMemberInfo.setMemberInfoGuid(orderDetail.getMemberGuid());
                //查询会员详情
                ResponseMemberInfo responseMemberInfo = memberMerchantClientService.getMemberInfoDetail(requestQueryMemberInfo).getTData();
                if (Objects.isNull(responseMemberInfo)) {
                    log.info("【消费有礼】会员信息不存在，订单号：{}", orderDetail.getGuid());
                    return;
                }
                log.info("会员详情={}", JacksonUtils.writeValueAsString(responseMemberInfo));
                log.info("orderDetail订单详情={}", JacksonUtils.writeValueAsString(orderDetail));
                log.info("orderDetailCopy订单详情={}", JacksonUtils.writeValueAsString(orderDetailCopy));
                MemberConsumptionGiftDTO memberConsumptionGiftQO = new MemberConsumptionGiftDTO();
                BeanUtils.copyProperties(orderDetail, memberConsumptionGiftQO);
                getMemberConsumptionGiftQO(orderDetail, memberConsumptionGiftQO);
                //会员
                memberConsumptionGiftQO.setOperSubjectGuid(!StringUtils.isEmpty(userContext.getOperSubjectGuid()) ?
                        userContext.getOperSubjectGuid() : responseMemberInfo.getOperSubjectGuid());
                memberConsumptionGiftQO.setEnterpriseGuid(!StringUtils.isEmpty(userContext.getEnterpriseGuid()) ?
                        userContext.getEnterpriseGuid() : responseMemberInfo.getEnterpriseGuid());
                memberConsumptionGiftQO.setMemberInfoGuid(orderDetail.getMemberGuid());
                memberConsumptionGiftQO.setMemberPhone(orderDetail.getMemberPhone());
                memberConsumptionGiftQO.setMemberName(StringUtils.isEmpty(responseMemberInfo.getNickName()) ?
                        responseMemberInfo.getUserName() : responseMemberInfo.getNickName());

                //订单
                memberConsumptionGiftQO.setOrderAmount(orderDetailCopy.getActuallyPayFee());
                memberConsumptionGiftQO.setOrderNumber(orderDetail.getGuid());
                memberConsumptionGiftQO.setOrderTime(Objects.nonNull(orderDetail.getCheckoutTime()) ?
                        orderDetail.getCheckoutTime() : orderDetail.getGmtCreate());
                memberConsumptionGiftQO.setActivitySceneType(orderDetail.getTradeMode());
                memberConsumptionGiftQO.setRechargeStatus(1);

                //门店
                memberConsumptionGiftQO.setStoreName(orderDetail.getStoreName());
                memberConsumptionGiftQO.setStoreGuid(orderDetail.getStoreGuid());

                memberConsumptionGiftQO.setChangeSource(BaseDeviceTypeEnum.getDesc(orderDetail.getDeviceType()));
                if (Objects.equals(memberConsumptionGiftQO.getChangeSource(), BaseDeviceTypeEnum.TCD.getDesc())) {
                    memberConsumptionGiftQO.setChangeSource("微信");
                }

                memberConsumptionGiftQO.setMemberInfoGradeGuid(responseMemberInfo.getOperationMemberInfoCardLevelGuid());
                memberConsumptionGiftQO.setMemberLabelGuid(responseMemberInfo.getLabelSettingGuid());

                log.info("调用会员消费有礼入参：{}", JacksonUtils.writeValueAsString(memberConsumptionGiftQO));
                memberMarketingClientService.dealConsumptionGift(memberConsumptionGiftQO);
            }
        } catch (Exception e) {
            log.info("消费有礼调用异常", e);
        }
    }

    private static void getMemberConsumptionGiftQO(DineinOrderDetailRespDTO orderDetail, MemberConsumptionGiftDTO memberConsumptionGiftQO) {
        if (CollUtil.isNotEmpty(orderDetail.getGrouponListRespDTOS())) {
            //团购列表
            memberConsumptionGiftQO.setGrouponListRespDTOS(orderDetail.getGrouponListRespDTOS().stream()
                    .map(groupon -> {
                        GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
                        BeanUtils.copyProperties(groupon, grouponListRespDTO);
                        return grouponListRespDTO;
                    }).collect(Collectors.toList()));
        } else {
            //团购列表
            memberConsumptionGiftQO.setGrouponListRespDTOS(new ArrayList<>());
        }
    }

    /**
     * 参与营销活动订单推送
     */
    private void asyncMemberMarketingOrder(DineinOrderDetailRespDTO orderDetail, UserContext userContext) {
        log.info("参与营销活动订单推送,订单信息:{}", JacksonUtils.writeValueAsString(orderDetail));
        List<DiscountFeeDetailDTO> discountFeeDetailList = orderDetail.getDiscountFeeDetailDTOS();
        if (CollectionUtils.isEmpty(discountFeeDetailList)) {
            return;
        }
        // 目前推送参与了限时特价活动、第N份优惠活动的订单
        List<DiscountFeeDetailDTO> discountFeeDetails = discountFeeDetailList.stream()
                .filter(e -> (DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode() == e.getDiscountType()
                        || DiscountTypeEnum.NTH_ACTIVITY.getCode() == e.getDiscountType())
                        && e.getDiscountFee().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(discountFeeDetails)) {
            return;
        }
        asyncMemberMarketingOrderExecutor.execute(() -> {
            UserContextUtils.put(userContext);
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            // 构建下单参数
            UniteActivityOrderPayDTO uniteActivityOrderPayDTO = buildUniteActivityOrderPayDTO(orderDetail, discountFeeDetails);
            // 下单
            log.info("参与营销活动订单推送入参:{}", JacksonUtils.writeValueAsString(uniteActivityOrderPayDTO));
            memberMarketingClientService.uniteActivityOrderPay(uniteActivityOrderPayDTO);
            log.info("参与营销活动订单推送成功");
            UserContextUtils.remove();
        });
    }


    /**
     * 构建下单参数
     */
    private UniteActivityOrderPayDTO buildUniteActivityOrderPayDTO(DineinOrderDetailRespDTO orderDetail,
                                                                   List<DiscountFeeDetailDTO> discountFeeDetails) {
        UniteActivityOrderPayDTO uniteActivityOrderPayDTO = new UniteActivityOrderPayDTO();
        List<UniteActivityOrderPayDTO.InnerActivity> activities = discountFeeDetails.stream().map(e -> {
            UniteActivityOrderPayDTO.InnerActivity activity = new UniteActivityOrderPayDTO.InnerActivity();
            DiscountRuleDTO discountRuleDTO = JacksonUtils.toObject(DiscountRuleDTO.class, e.getRule());
            activity.setActivityGuid(discountRuleDTO.getActivityGuid());
            activity.setActivityType(QueryTypeEnum.transferQueryType(e.getDiscountType()));
            return activity;
        }).collect(Collectors.toList());
        uniteActivityOrderPayDTO.setActivities(activities);
        uniteActivityOrderPayDTO.setStoreName(orderDetail.getStoreName());
        uniteActivityOrderPayDTO.setOrderTime(orderDetail.getCheckoutTime());
        uniteActivityOrderPayDTO.setOrderGuid(orderDetail.getGuid());
        uniteActivityOrderPayDTO.setOrderNo(orderDetail.getOrderNo());
        uniteActivityOrderPayDTO.setOrderType(OrderTypeEnum.transferCode(orderDetail.getTradeMode()));
        uniteActivityOrderPayDTO.setOrderFee(orderDetail.getOrderFee());
        uniteActivityOrderPayDTO.setOrderDiscountFee(orderDetail.getDiscountFee());
        uniteActivityOrderPayDTO.setOrderActuallyFee(orderDetail.getActuallyPayFee());
        uniteActivityOrderPayDTO.setOrderSource(OrderSourceEnum.transferCode(orderDetail.getDeviceType()));
        uniteActivityOrderPayDTO.setMemberName(orderDetail.getMemberName());
        uniteActivityOrderPayDTO.setMemberPhone(orderDetail.getMemberPhone());
        return uniteActivityOrderPayDTO;
    }

    @Override
    public void asyncPushOrder(OrderDO orderDO, UserContext userContext) {
        orderStatusThreadPool.execute(() -> {
            UserContextUtils.put(userContext);
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            //何师订单 或者 来源是 15 通吃岛（赚餐） 则推送消息
            //            if (overallConfig.getEnterpriseGuidList().contains(UserContextUtils.getEnterpriseGuid()) || orderDO.getDeviceType() == 15){
            //                log.info("开始进入推送已结账订单到赚餐！{}",JacksonUtils.writeValueAsString(orderDO));
            //                pushOrderDetailMQ(orderDO);
            //                log.info("已结账订单推送完成！");
            //            }
            //修改为所有会员订单都同步到赚餐
            boolean memberOrder = !StringUtils.isEmpty(orderDO.getMemberGuid()) && ObjectUtil.notEqual("0", orderDO.getMemberGuid())
                    && !StringUtils.isEmpty(orderDO.getMemberCardGuid()) && ObjectUtil.notEqual("0", orderDO.getMemberCardGuid());
            boolean canteenCard = !StringUtils.isEmpty(orderDO.getCanteenConsumptionGuid()) && ObjectUtil.notEqual("0", orderDO.getCanteenConsumptionGuid());
            // 支付宝小程序 + 通吃岛小程序
            if (memberOrder || canteenCard || BaseDeviceTypeEnum.isApplet(orderDO.getDeviceType())) {
                log.info("开始进入推送已结账订单到赚餐！{}", JacksonUtils.writeValueAsString(orderDO));
                pushOrderDetailMQ(orderDO);
                log.info("已结账订单推送完成！");
            }
        });
    }

    /**
     * 同步打印成功调用
     */
    @Override
    public boolean printCheckOutCall(BaseDTO baseDTO, OrderDO orderDO, DineinOrderDetailRespDTO orderDetail) {
        orderDetail.setCheckoutTime(orderDO.getCheckoutTime());
        // 会员支付返回信息
        orderDetail.setMultiMemberPays(orderDO.getMultiMemberPays());
        // 多单结账查询多个订单的会员支付信息
        sameOrderMemberInfoHandler(orderDetail);
        // 多单结账查询多个订单的挂账支付信息
        sameOrderDebtInfoHandler(orderDetail);
        List<DineInItemDTO> erpDineInItemDTOS = JacksonUtils.toObjectList(DineInItemDTO.class,
                JacksonUtils.writeValueAsString(orderDetail.getDineInItemDTOS()));
        //子单商品erp减库存  和 调erp
        if (CollectionUtil.isNotEmpty(orderDetail.getSubOrderDetails())) {
            orderDetail.getSubOrderDetails().stream().flatMap(a -> a.getDineInItemDTOS().stream()).forEach(erpDineInItemDTOS::add);
        }
        //深拷贝orderDetail
        String jsonStr = JacksonUtils.writeValueAsString(orderDetail);
        DineinOrderDetailRespDTO orderDetailCopy = JacksonUtils.toObject(DineinOrderDetailRespDTO.class, jsonStr);
        log.info("打印前的参数:{}", JacksonUtils.writeValueAsString(orderDetailCopy));
        UserContext userContext = UserContextUtils.get();
        //消费有礼查询
        memberMarketingThreadPool.execute(() -> {
            UserContextUtils.put(userContext);
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            dealConsumptionGift(orderDetail, userContext, orderDetailCopy);
        });

        //打印结账单   没考虑打印报错？？？3
        try {
            dineInPrintService.printCheckout(baseDTO, orderDetailCopy);
        } catch (Exception e) {
            log.error("打印结账单失败:{}", e.getStackTrace());
        }
        // 库存扣减
        executeStockReduceService.executeStockReduce(erpDineInItemDTOS, orderDetail.getGuid(), orderDO);

        if (orderDO.getTradeMode() == 1) {
            //快餐打印,注意这个地方会改变resp对象的item，！！！！！
            fastPrint(orderDetail, baseDTO, orderDO);
        } else {
            if (Objects.isNull(baseDTO.getCloseTableFlag()) || baseDTO.getCloseTableFlag() == 0) {
                //更改桌台状态
                if (!StringUtils.isEmpty(orderDetail.getCurrentTableOrderGuid())) {
                    OrderDO currentOrderDO = JacksonUtils.toObject(OrderDO.class, JacksonUtils.writeValueAsString(orderDO));
                    currentOrderDO.setGuid(Long.valueOf(orderDetail.getCurrentTableOrderGuid()));
                    tableService.checkOutChange(baseDTO, currentOrderDO);
                } else {
                    tableService.checkOutChange(baseDTO, orderDO);
                }
            }
        }
        return true;
    }

    /**
     * 多单结账查询多个订单的会员支付信息
     */
    private void sameOrderMemberInfoHandler(DineinOrderDetailRespDTO orderDetail) {
        List<DineinOrderDetailRespDTO> otherOrderDetails = orderDetail.getOtherOrderDetails();
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            return;
        }
        queryOrderMemberFundingDetail(orderDetail);
        for (DineinOrderDetailRespDTO otherOrderDetail : otherOrderDetails) {
            queryOrderMemberFundingDetail(otherOrderDetail);
        }
    }


    /**
     * 多单结账查询多个订单的挂账信息
     */
    private void sameOrderDebtInfoHandler(DineinOrderDetailRespDTO orderDetail) {
        List<DineinOrderDetailRespDTO> otherOrderDetails = orderDetail.getOtherOrderDetails();
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            return;
        }
        queryOrderDebtInfo(orderDetail);
        for (DineinOrderDetailRespDTO otherOrderDetail : otherOrderDetails) {
            queryOrderDebtInfo(otherOrderDetail);
        }
    }

    /**
     * 查询会员消费记录
     */
    private void queryOrderMemberFundingDetail(DineinOrderDetailRespDTO orderDetail) {
        String memberConsumptionGuid = orderDetail.getMemberConsumptionGuid();
        if (StringUtils.isEmpty(memberConsumptionGuid) || "0".equals(memberConsumptionGuid)) {
            return;
        }
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = Optional.ofNullable(orderDetail.getActuallyPayFeeDetailDTOS())
                .orElse(Lists.newArrayList());
        boolean memberPay = actuallyPayFeeDetailDTOS.stream()
                .anyMatch(e -> Objects.equals(PaymentTypeEnum.MEMBER.getCode(), e.getPaymentType()));
        if (!memberPay) {
            return;
        }
        List<ResponseConfirmMultiPay> confirmMultiPays = memberTerminalClientService.queryFundingDetail(memberConsumptionGuid);
        if (CollectionUtils.isEmpty(confirmMultiPays)) {
            log.error("通过会员消费guid查询到资金变动记录为空,memberConsumptionGuid:{}", memberConsumptionGuid);
            return;
        }
        log.info("查询会员消费资金变动明细记录返回:{}", JacksonUtils.writeValueAsString(confirmMultiPays));
        // 会员卡支付返回信息
        List<OrderMultiMemberPayDTO> orderMultiMemberPayList = OrderTransform.INSTANCE
                .responseConfirmMultiPay2OrderMultiMemberPayDTO(confirmMultiPays);
        orderDetail.setMultiMemberPays(orderMultiMemberPayList);
    }


    /**
     * 查询会员消费记录
     */
    private void queryOrderDebtInfo(DineinOrderDetailRespDTO orderDetail) {
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailList = orderDetail.getActuallyPayFeeDetailDTOS();
        if (CollectionUtils.isEmpty(actuallyPayFeeDetailList)) {
            return;
        }
        boolean debtPay = actuallyPayFeeDetailList.stream()
                .anyMatch(e -> Objects.equals(PaymentTypeEnum.DEBT_PAY.getCode(), e.getPaymentType()));
        if (!debtPay) {
            return;
        }
        DebtUnitRecordPageRespDTO debtUnitRecord = debtUnitRecordService.debtUnitRecordByOrderGuid(Long.valueOf(orderDetail.getGuid()));
        if (Objects.isNull(debtUnitRecord)) {
            log.error("订单挂账信息不存在, orderGuid:{}", orderDetail.getGuid());
            return;
        }
        orderDetail.setDebtContactName(debtUnitRecord.getUnitContactName());
        orderDetail.setDebtUnitName(debtUnitRecord.getUnitName());
        orderDetail.setDebtContactTel(debtUnitRecord.getUnitContactTel());
    }

    @Override
    public OrderDetailPushMqDTO getOrderDetailPushMqDTO(String orderGuid) {
        OrderDO orderDO = orderService.getById(orderGuid);
        return getOrderDetailPushMqDTO(orderDO);
    }

    @Override
    public OrderDetailPushMqDTO getOrderDetailPushMqDTO(OrderDO orderDO) {
        OrderDTO orderDTO = orderTransform.orderDO2DTO(orderDO);
        Long orderGuid = orderDO.getGuid();
        List<OrderItemDO> orderItemDOList = orderItemService.listByOrderGuidWithCache(orderGuid, orderDO.getDeviceType());
        List<OrderItemDTO> dto = orderTransform.toOrderItemDTOS(orderItemDOList);
        OrderDetailPushMqDTO orderDetailPushMqDTO = new OrderDetailPushMqDTO();
        orderDetailPushMqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        orderDetailPushMqDTO.setOrderDTO(orderDTO);
        UserContext userContext = UserContextUtils.get();
        if (!StringUtils.isEmpty(userContext.getOperSubjectGuid())) {
            orderDetailPushMqDTO.setOperSubjectGuid(userContext.getOperSubjectGuid());
        }
        WxStoreMerchantOrderDTO merchantOrderPhone = wxStoreClientService.getMerchantOrderPhone(String.valueOf(orderGuid));
        log.info("pushOrderDetailMQ 查询微信订单信息：{},请求订单Guid为：{}", JacksonUtils.writeValueAsString(merchantOrderPhone), orderGuid);
        if (ObjectUtil.isNotNull(merchantOrderPhone)) {
            if (!StringUtils.isEmpty(merchantOrderPhone.getPhoneNum())) {
                orderDetailPushMqDTO.setPhone(merchantOrderPhone.getPhoneNum());
            }
            orderDetailPushMqDTO.setOrderRecordGuid(merchantOrderPhone.getOrderRecordGuid());
            orderDetailPushMqDTO.setOpenId(merchantOrderPhone.getOpenId());
        }
        //===================== 优惠信息 =====================
        List<DiscountDO> discountDoList = discountService.getUseDiscountListByOrderGuid(String.valueOf(orderGuid));
        if (!CollectionUtils.isEmpty(discountDoList)) {
            orderDetailPushMqDTO.setDiscountInfoList(OrderTransform.INSTANCE.discountDOS2DiscountDTOS(discountDoList));
            orderDetailPushMqDTO.setDiscountCombined(discountDoList.stream().map(DiscountDO::getDiscountFee).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        //附加费详情
        if (BigDecimalUtil.greaterThanZero(orderDO.getAppendFee())) {
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setData(String.valueOf(orderDO.getGuid()));
            orderDetailPushMqDTO.setAppendFeeDetailList(appendFeeService.appendFeeList(singleDataDTO));
        }
        //===================== 支付方式 =====================
        List<TransactionRecordDO> transactionRecordList = transactionRecordService.listByOrderGuid(String.valueOf(orderGuid));
        if (!CollectionUtils.isEmpty(transactionRecordList)) {
            orderDetailPushMqDTO.setPayWayDetails(TransactionRecordTransform.INSTANCE.do2TransactionRecordRespDTO(transactionRecordList));
            orderDetailPushMqDTO.setPayWayDetailsCount(transactionRecordList.stream().map(TransactionRecordDO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        //属性加价
        List<ItemAttrDetailDTO> itemAttr = orderDetailService.getItemAttr(String.valueOf(orderGuid));
        if (CollectionUtil.isNotEmpty(itemAttr)) {
            Map<Long, List<ItemAttrDetailDTO>> itemAttrMap = itemAttr.stream().collect(Collectors.groupingBy(ItemAttrDetailDTO::getOrderItemGuid));
            for (OrderItemDTO orderItemDTO : dto) {
                List<ItemAttrDetailDTO> attrDetailDTOS = itemAttrMap.get(orderItemDTO.getGuid());
                orderItemDTO.setItemAttr(attrDetailDTOS);
            }
        }
        orderDetailPushMqDTO.setOrderItemDTOList(dto);
        return orderDetailPushMqDTO;
    }

    /***
     * 推送已结账订单信息
     * @param orderDO
     */
    private void pushOrderDetailMQ(OrderDO orderDO) {
        OrderDetailPushMqDTO orderDetailPushMqDTO = getOrderDetailPushMqDTO(orderDO);
        orderStatusMqService.orderStatusChangeWithMq(orderDetailPushMqDTO);
    }

    /**
     * 快餐打印
     */
    private void fastPrint(DineinOrderDetailRespDTO orderDetail, BaseDTO baseDTO, OrderDO orderDO) {
        List<DineInItemDTO> dineInItemDTOS = orderDetail.getDineInItemDTOS().stream()
                .filter(dineInItemDTO -> dineInItemDTO.getIsPay() != 1).collect(Collectors.toList());
        orderDetail.setDineInItemDTOS(dineInItemDTOS);
        if (CollectionUtil.isEmpty(dineInItemDTOS)) {
            return;
        }
        //kds
        kdsService.prepare(orderDetail, baseDTO);
        //打印单
        dineInPrintService.printOrderItem(baseDTO, orderDO, dineInItemDTOS, null);
        //标签单
        dineInPrintService.printLabelItem(baseDTO, orderDO, dineInItemDTOS);
    }
}