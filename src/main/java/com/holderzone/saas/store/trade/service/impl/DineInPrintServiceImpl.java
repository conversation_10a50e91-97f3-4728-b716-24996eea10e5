package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalDetailsDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.invoice.RequestGenerateInvoiceDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.business.PrintItemOrderEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrinterTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.async.print.AbstractPrintDtoFactory;
import com.holderzone.saas.store.trade.async.print.TradeDataContainer;
import com.holderzone.saas.store.trade.config.RocketMqConfig;
import com.holderzone.saas.store.trade.constants.DiscountTypeConstants;
import com.holderzone.saas.store.trade.entity.domain.GrouponDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.OrderExtendsDO;
import com.holderzone.saas.store.trade.entity.domain.OrderItemDO;
import com.holderzone.saas.store.trade.repository.feign.*;
import com.holderzone.saas.store.trade.repository.interfaces.GrouponMpService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderExtendsService;
import com.holderzone.saas.store.trade.service.AppendFeeService;
import com.holderzone.saas.store.trade.service.DineInPrintService;
import com.holderzone.saas.store.trade.service.KdsService;
import com.holderzone.saas.store.trade.service.invoice.InvoiceService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.common.message.Message;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@AllArgsConstructor
public class DineInPrintServiceImpl implements DineInPrintService {

    private final TableClientService tableClientService;

    private final StoreClientService storeClientService;

    private final KdsService kdsService;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final OrganizationClientService organizationClientService;

    private final PriceCalculationUtils priceCalculationUtils;

    private final InvoiceService invoiceService;

    private final OrderExtendsService orderExtendsService;

    private final GrouponMpService grouponMpService;

    private final BusinessClientService businessClientService;

    private final MemberTerminalClientService terminalClientService;

    private final AppendFeeService appendFeeService;

    @Override
    public boolean printItemDetail(BaseDTO baseDTO, OrderDO orderDO, OrderFeeDetailDTO orderFeeDetailDTO, List<DineInItemDTO> dineInItemDTOS) {
        updateThreadStoreName();
        OrderTransform orderTransform = OrderTransform.INSTANCE;
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = orderTransform.orderDO2DineinOrderDetailRespDTO(orderDO);
        // 查询订单扩展信息
        queryOrderExtendsInfo(dineinOrderDetailRespDTO);
        // 附加费
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        // 菜品清单处理商品清单会员价
        dineInItemDTOS.forEach(e -> e.setMemberPrice(null));
        // 打印商品排序
        List<DineInItemDTO> sortedDineInItemDTOList = printItemOrder(orderDO, dineInItemDTOS);
        dineinOrderDetailRespDTO.setDineInItemDTOS(sortedDineInItemDTOList);
        // 会员画像
        if (StringUtils.hasText(orderDO.getMemberGuid()) && !"0".equals(orderDO.getMemberGuid())) {
            try {
                MemberPortrayalDetailsDTO portrayalDetailsDTO = terminalClientService.queryMemberPortrayal(orderDO.getMemberGuid());
                dineinOrderDetailRespDTO.setMemberPortrayalDTO(portrayalDetailsDTO);
            } catch (Exception e) {
                log.warn("[打印菜品清单]查询会员画像信息失败=", e);
            }
        }
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(dineinOrderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .storeInfoFunction(storeClientService::queryStoreBizByGuid)
                .build();
        PrintDTO printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.ITEM_LIST, tradeDataContainer);
        return print(printDTO);
    }


    /**
     * 打印商品排序
     */
    private List<DineInItemDTO> printItemOrder(OrderDO orderDO, List<DineInItemDTO> dineInItemDTOS) {
        StoreConfigQueryDTO storeConfigQueryDTO = new StoreConfigQueryDTO();
        storeConfigQueryDTO.setStoreGuid(orderDO.getStoreGuid());
        String printItemOrderConfig = businessClientService.queryPrintItemOrderConfig(storeConfigQueryDTO);
        if (PrintItemOrderEnum.DESC.name().equals(printItemOrderConfig)) {
            return dineInItemDTOS;
        }
        return dineInItemDTOS.stream()
                .sorted(Comparator.comparing(DineInItemDTO::getGuid).reversed())
                .collect(Collectors.toList());
    }

    private void updateThreadStoreName() {
        String storeGuid = UserContextUtils.getStoreGuid();
        if (StringUtils.hasText(storeGuid)) {
            StoreDTO storeDTO = storeClientService.queryStoreByGuid(storeGuid);
            UserContext userContext = UserContextUtils.get();
            userContext.setStoreName(storeDTO.getName());
            UserContextUtils.put(userContext);
        }
    }

    private void updateThreadStoreGuid(BaseDTO baseDTO) {
        String storeGuid = baseDTO.getStoreGuid();
        UserContext userContext = UserContextUtils.get();
        String contextStoreGuid = userContext.getStoreGuid();
        if (StringUtils.hasText(storeGuid) && StringUtils.isEmpty(contextStoreGuid)) {
            userContext.setStoreGuid(storeGuid);
            UserContextUtils.put(userContext);
        }
    }


    @Override
    public boolean printItemDetail(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        updateThreadStoreName();
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(dineinOrderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .storeInfoFunction(storeClientService::queryStoreBizByGuid)
                .build();
        PrintDTO printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.ITEM_LIST, tradeDataContainer);
        //设置手动打印配置
        printDTO.setManualPrint(true);
        return print(printDTO);
    }

    @Override
    public boolean printChangeItem(BaseDTO baseDTO, DineinOrderDetailRespDTO orderDetailRespDTO, Boolean cancelFlag,
                                   List<SubDineInItemDTO> originalSubDineInItemList, List<SubDineInItemDTO> changeSubDineInItemList,
                                   OrderItemDO pkgOrderItemDO) {
        updateThreadStoreName();
        // 查询订单扩展信息
        queryOrderExtendsInfo(orderDetailRespDTO);
        try {
            // 更新kds 并打印kds后厨单
            kdsService.changes(orderDetailRespDTO, cancelFlag, originalSubDineInItemList, changeSubDineInItemList);
        } catch (Exception e) {
            log.error("更新kds失败,e:", e);
        }
        List<DineInItemDTO> originalDineInItemList = originalSubDineInItemList.stream().map(e -> {
            DineInItemDTO dineInItemDTO = OrderTransform.INSTANCE.subDineInItemDTO2DineInItemDTO(e);
            dineInItemDTO.setCurrentCount(dineInItemDTO.getCurrentCount().multiply(e.getPackageDefaultCount()).multiply(pkgOrderItemDO.getCurrentCount()));
            return dineInItemDTO;
        }).collect(Collectors.toList());
        // 新菜(多个)
        List<DineInItemDTO> changeDineInItemList = changeSubDineInItemList.stream().map(e -> {
            DineInItemDTO dineInItemDTO = OrderTransform.INSTANCE.subDineInItemDTO2DineInItemDTO(e);
            dineInItemDTO.setCurrentCount(dineInItemDTO.getCurrentCount().multiply(e.getPackageDefaultCount()).multiply(pkgOrderItemDO.getCurrentCount()));
            return dineInItemDTO;
        }).collect(Collectors.toList());
        // 原菜品、新菜品
        orderDetailRespDTO.setChangeCancelFlag(cancelFlag);
        orderDetailRespDTO.setOriginalDineInItemList(originalDineInItemList);
        orderDetailRespDTO.setChangeDineInItemList(changeDineInItemList);
        List<DineInItemDTO> dineInItemDTOList = Stream.of(originalDineInItemList, changeDineInItemList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        orderDetailRespDTO.setDineInItemDTOS(dineInItemDTOList);
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(orderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .build();
        PrintDTO printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.CHANGE_ITEM, tradeDataContainer);
        return print(printDTO);
    }

    @Override
    public boolean printOrderItem(BaseDTO baseDTO, OrderDO orderDO, List<DineInItemDTO> dineInItemDTOS,
                                  Integer itemInvoiceType) {
        updateThreadStoreName();
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = OrderTransform.INSTANCE.orderDO2DineinOrderDetailRespDTO(orderDO);
        // 打印商品排序
        List<DineInItemDTO> sortedDineInItemDTOList = printItemOrder(orderDO, dineInItemDTOS);
        // 订单信息填充
        queryOrderExtendsInfo(dineinOrderDetailRespDTO);
        dineinOrderDetailRespDTO.setDineInItemDTOS(sortedDineInItemDTOList);
        log.info("组装打印后厨单对象入参：{}", JacksonUtils.writeValueAsString(dineinOrderDetailRespDTO));
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(dineinOrderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .storeInfoFunction(storeClientService::queryStoreBizByGuid)
                .itemInvoiceType(itemInvoiceType)
                .build();
        PrintDTO printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.ORDER_ITEM, tradeDataContainer);
        return print(printDTO);
    }

    @Override
    public boolean printLabelItem(BaseDTO baseDTO, OrderDO orderDO, List<DineInItemDTO> dineInItemDTOS) {
        updateThreadStoreName();
        OrderTransform orderTransform = OrderTransform.INSTANCE;
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = orderTransform.orderDO2DineinOrderDetailRespDTO(orderDO);
        // 查询订单扩展信息
        queryOrderExtendsInfo(dineinOrderDetailRespDTO);
        dineinOrderDetailRespDTO.setDineInItemDTOS(dineInItemDTOS);
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(dineinOrderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .storeInfoFunction(storeClientService::queryStoreBizByGuid)
                .build();
        PrintDTO printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.LABEL, tradeDataContainer);
        return print(printDTO);
    }

    @Override
    public boolean printLabelItemBarCode(BaseDTO baseDTO, OrderDO orderDO, DineInItemDTO dineInItem) {
        OrderTransform orderTransform = OrderTransform.INSTANCE;
        DineinOrderDetailRespDTO orderDetailRespDTO = orderTransform.orderDO2DineinOrderDetailRespDTO(orderDO);
        orderDetailRespDTO.setDineInItemDTOS(Lists.newArrayList(dineInItem));
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(orderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .storeInfoFunction(storeClientService::queryStoreBizByGuid)
                .build();
        PrintDTO printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.ITEM_LABEL, tradeDataContainer);
        return print(printDTO);
    }

    @Override
    public boolean printRefundItem(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        //bugfixed:17758
        if (!ObjectUtils.isEmpty(dineinOrderDetailRespDTO.getDineInItemDTOS())) {
            //Kds 退菜  后面需要转移
            try {
                kdsService.refound(dineinOrderDetailRespDTO);
            } catch (Exception e) {
                log.error("kds退菜失败", e);
            }
            updateThreadStoreName();
            // 查询订单扩展信息
            queryOrderExtendsInfo(dineinOrderDetailRespDTO);
            TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                    .baseDTO(baseDTO)
                    .dineinOrderDetailRespDTO(dineinOrderDetailRespDTO)
                    .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                    .storeInfoFunction(storeClientService::queryStoreBizByGuid)
                    .build();
            PrintDTO printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.REFUND_ITEM, tradeDataContainer);
            return print(printDTO);
        } else {
            return false;
        }
    }

    @Override
    public boolean printPreCheck(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        // 如果没有使用会员价，则不打印会员价
        handleMemberPrice(dineinOrderDetailRespDTO);
        // 拆分第三方团购信息
        priceCalculationUtils.splitGroupBuyDiscount(dineinOrderDetailRespDTO);
        // 多单结账处理
        sameTableOrderHandler(true, dineinOrderDetailRespDTO);
        // 排序
        List<DineInItemDTO> sortedDineInItemDTOList = dineinOrderDetailRespDTO.getDineInItemDTOS().stream()
                .sorted(Comparator.comparing(DineInItemDTO::getGuid))
                .collect(Collectors.toList());
        dineinOrderDetailRespDTO.setDineInItemDTOS(sortedDineInItemDTOList);
        log.info("预结账打印前信息：{}", JacksonUtils.writeValueAsString(dineinOrderDetailRespDTO));
        updateThreadStoreName();
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(dineinOrderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .storeInfoFunction(storeClientService::queryStoreBizByGuid)
                .build();
        PrintDTO printDTO;
        if (CollectionUtils.isEmpty(dineinOrderDetailRespDTO.getSubOrderDetails())) {
            printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.PRE_CHECKOUT, tradeDataContainer);
        } else {
            printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.PRE_CHECKOUT_TABLES, tradeDataContainer);
        }
        return print(printDTO);
    }


    @Override
    public boolean printCheckout(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        //构建优惠以及支付方式打印
        structureDiscountFeeAndPayFeePrint(dineinOrderDetailRespDTO);
        //更新门店信息
        updateThreadStoreGuid(baseDTO);
        updateThreadStoreName();
        // 如果没有使用会员价，则不打印会员价
        handleMemberPrice(dineinOrderDetailRespDTO);
        // 多单结账处理
        sameTableOrderHandler(false, dineinOrderDetailRespDTO);
        // 排序
        List<DineInItemDTO> sortedDineInItemDTOList = dineinOrderDetailRespDTO.getDineInItemDTOS().stream()
                .sorted(Comparator.comparing(DineInItemDTO::getGuid))
                .collect(Collectors.toList());
        dineinOrderDetailRespDTO.setDineInItemDTOS(sortedDineInItemDTOList);
        log.info("结账，打印前信息：{}", JacksonUtils.writeValueAsString(dineinOrderDetailRespDTO));
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(dineinOrderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .storeInfoFunction(storeClientService::queryStoreBizByGuid)
                .build();
        PrintDTO printDTO;
        if (CollectionUtils.isEmpty(dineinOrderDetailRespDTO.getSubOrderDetails())) {
            printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.CHECKOUT, tradeDataContainer);
        } else {
            printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.CHECKOUT_TABLES, tradeDataContainer);
        }
        if (StringUtils.isEmpty(printDTO.getDeviceId())) {
            String deviceId = getDeviceId(dineinOrderDetailRespDTO);
            printDTO.setDeviceId(deviceId);
        }
        //生成开票二维码
        checkInvoiceCode(baseDTO, dineinOrderDetailRespDTO, printDTO);
        // 老板助手打印只打印云打印机
        if (Objects.equals(BaseDeviceTypeEnum.BOSS.getCode(), baseDTO.getDeviceType())) {
            printDTO.setAppointPrinterType(PrinterTypeEnum.CLOUD_PRINTER.getType());
        }
        log.info("printDTO信息={}", printDTO);
        return print(printDTO);
    }

    /**
     * 多单结账处理
     */
    private void sameTableOrderHandler(boolean prePrintFlag, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        List<DineinOrderDetailRespDTO> otherOrderDetails = dineinOrderDetailRespDTO.getOtherOrderDetails();
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            return;
        }
        // 过滤已作废/未结账的订单
        if (prePrintFlag) {
            // 预打印 返回的作废是 StateEnum
            otherOrderDetails.removeIf(e -> Objects.equals(StateEnum.READY.getCode(), e.getState())
                    || Objects.equals(StateEnum.CANCEL.getCode(), e.getState())
                    || Objects.equals(StateEnum.INVALID.getCode(), e.getState()));
        } else {
            // 其他返回的作废是4
            otherOrderDetails.removeIf(e -> Objects.equals(4, e.getState()) || Objects.equals(1, e.getState()));
        }
        // 附加费合并
        // 附加费 + 附加费明细
        BigDecimal totalAppendFee = otherOrderDetails.stream()
                .map(DineinOrderDetailRespDTO::getAppendFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        dineinOrderDetailRespDTO.setAppendFee(dineinOrderDetailRespDTO.getAppendFee().add(totalAppendFee));
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(mergeAppendDetailList(dineinOrderDetailRespDTO));

        // 商品列表合并
        List<DineInItemDTO> dineInItemDTOS = mergeDineInItemDTOS(otherOrderDetails);
        if (CollectionUtils.isNotEmpty(dineInItemDTOS)) {
            dineinOrderDetailRespDTO.getDineInItemDTOS().addAll(dineInItemDTOS);
        }
        // 应付金额
        dineinOrderDetailRespDTO.setPayAble(calculateSameOrderPayAble(dineinOrderDetailRespDTO));
        // 实付金额
        dineinOrderDetailRespDTO.setActuallyPayFee(mergeTotalActuallyPayFee(dineinOrderDetailRespDTO));
        // 优惠信息合并
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(mergeDiscountFeeDetailList(dineinOrderDetailRespDTO));
        // 交易记录合并
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(mergeActuallyPayFeeDetailList(dineinOrderDetailRespDTO));
    }

    /**
     * 附加费信息合并
     */
    private OrderFeeDetailDTO mergeAppendDetailList(DineinOrderDetailRespDTO detailRespDTO) {
        OrderFeeDetailDTO orderFeeDetail = detailRespDTO.getOrderFeeDetailDTO();
        orderFeeDetail = Optional.ofNullable(orderFeeDetail).orElse(new OrderFeeDetailDTO());
        List<AppendFeeDetailDTO> appendFeeDetailList = Optional.ofNullable(orderFeeDetail.getAppendFeeDetailDTOS()).orElse(Lists.newArrayList());
        Map<String, AppendFeeDetailDTO> appendFeeDetailMap = appendFeeDetailList
                .stream()
                .collect(Collectors.toMap(e -> e.getType() + e.getName(), Function.identity(), (key1, key2) -> key1));
        for (DineinOrderDetailRespDTO otherOrderDetail : detailRespDTO.getOtherOrderDetails()) {
            OrderFeeDetailDTO otherOrderFeeDetailDTO = otherOrderDetail.getOrderFeeDetailDTO();
            if (Objects.isNull(otherOrderFeeDetailDTO) || CollectionUtils.isEmpty(otherOrderFeeDetailDTO.getAppendFeeDetailDTOS())) {
                continue;
            }
            List<AppendFeeDetailDTO> otherAppendFeeDetailDTOS = otherOrderFeeDetailDTO.getAppendFeeDetailDTOS();
            for (AppendFeeDetailDTO appendFeeDetailDTO : otherAppendFeeDetailDTOS) {
                AppendFeeDetailDTO totalAppendFeeDetailDTO = appendFeeDetailMap.get(appendFeeDetailDTO.getType() + appendFeeDetailDTO.getName());
                if (Objects.isNull(totalAppendFeeDetailDTO)) {
                    appendFeeDetailMap.put(appendFeeDetailDTO.getType() + appendFeeDetailDTO.getName(), appendFeeDetailDTO);
                } else {
                    totalAppendFeeDetailDTO.setAmount(totalAppendFeeDetailDTO.getAmount()
                            .add(appendFeeDetailDTO.getAmount()));
                }
            }
        }
        orderFeeDetail.setAppendFeeDetailDTOS(new ArrayList<>(appendFeeDetailMap.values()));
        return orderFeeDetail;
    }

    private List<DineInItemDTO> mergeDineInItemDTOS(List<DineinOrderDetailRespDTO> otherOrderDetails) {
        List<DineInItemDTO> dineInItemDTOS = Lists.newArrayList();
        for (DineinOrderDetailRespDTO otherOrderDetail : otherOrderDetails) {
            List<DineInItemDTO> otherDineInItemDTOS = otherOrderDetail.getDineInItemDTOS();
            if (CollectionUtils.isNotEmpty(otherDineInItemDTOS)) {
                dineInItemDTOS.addAll(otherDineInItemDTOS);
            }
        }
        return dineInItemDTOS;
    }

    private BigDecimal mergeTotalActuallyPayFee(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        List<DineinOrderDetailRespDTO> otherOrderDetails = dineinOrderDetailRespDTO.getOtherOrderDetails();
        // 订单详情返回给前端的状态 已结账是2 没有指定枚举
        BigDecimal totalActuallyPayFee = otherOrderDetails.stream()
                .filter(e -> Objects.equals(2, e.getState())
                        || Objects.equals(StateEnum.SUB_SUCCESS.getCode(), e.getState())
                        || Objects.equals(StateEnum.SUCCESS.getCode(), e.getState()))
                .map(DineinOrderDetailRespDTO::getActuallyPayFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (Objects.equals(2, dineinOrderDetailRespDTO.getState())
                || Objects.equals(StateEnum.SUB_SUCCESS.getCode(), dineinOrderDetailRespDTO.getState())
                || Objects.equals(StateEnum.SUCCESS.getCode(), dineinOrderDetailRespDTO.getState())) {
            totalActuallyPayFee = totalActuallyPayFee.add(Optional.ofNullable(dineinOrderDetailRespDTO.getActuallyPayFee()).orElse(BigDecimal.ZERO));
        }
        return totalActuallyPayFee;
    }

    /**
     * 计算多单结账的应付金额
     */
    private BigDecimal calculateSameOrderPayAble(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        List<DineinOrderDetailRespDTO> otherOrderDetails = dineinOrderDetailRespDTO.getOtherOrderDetails();
        List<Integer> availableStateList = Lists.newArrayList(StateEnum.READY.getCode(),
                StateEnum.PENDING.getCode(), StateEnum.FAILURE.getCode(), StateEnum.SUCCESS.getCode(),
                StateEnum.SUB_SUCCESS.getCode());
        BigDecimal actuallyPayFee = otherOrderDetails.stream().filter(e -> availableStateList.contains(e.getState()))
                .map(DineinOrderDetailRespDTO::getActuallyPayFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (availableStateList.contains(dineinOrderDetailRespDTO.getState())) {
            actuallyPayFee = actuallyPayFee.add(Optional.ofNullable(dineinOrderDetailRespDTO.getActuallyPayFee()).orElse(BigDecimal.ZERO));
        }
        return actuallyPayFee;
    }

    /**
     * 交易记录合并
     */
    private List<ActuallyPayFeeDetailDTO> mergeActuallyPayFeeDetailList(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailList = dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS();
        Map<String, ActuallyPayFeeDetailDTO> actuallyPayFeeDetailMap = Optional.ofNullable(actuallyPayFeeDetailList).orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(ActuallyPayFeeDetailDTO::getPaymentTypeName, Function.identity(), (key1, key2) -> key1));
        for (DineinOrderDetailRespDTO otherOrderDetail : dineinOrderDetailRespDTO.getOtherOrderDetails()) {
            List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = Optional.ofNullable(otherOrderDetail.getActuallyPayFeeDetailDTOS())
                    .orElse(Lists.newArrayList());
            for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : actuallyPayFeeDetailDTOS) {
                ActuallyPayFeeDetailDTO totalActuallyPayFeeDetailDTO = actuallyPayFeeDetailMap.get(actuallyPayFeeDetailDTO.getPaymentTypeName());
                if (Objects.isNull(totalActuallyPayFeeDetailDTO)) {
                    actuallyPayFeeDetailMap.put(actuallyPayFeeDetailDTO.getPaymentTypeName(), actuallyPayFeeDetailDTO);
                } else {
                    totalActuallyPayFeeDetailDTO.setAmount(totalActuallyPayFeeDetailDTO.getAmount()
                            .add(actuallyPayFeeDetailDTO.getAmount()));
                }
            }
        }
        return actuallyPayFeeDetailMap.values()
                .stream()
                .sorted(Comparator.comparing(ActuallyPayFeeDetailDTO::getPaymentType))
                .collect(Collectors.toList());
    }

    /**
     * 优惠信息合并
     */
    private List<DiscountFeeDetailDTO> mergeDiscountFeeDetailList(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        // 优惠信息合并
        List<DiscountFeeDetailDTO> discountFeeDetailList = dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS();
        Map<Integer, DiscountFeeDetailDTO> discountFeeDetailMap = Optional.ofNullable(discountFeeDetailList).orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(DiscountFeeDetailDTO::getDiscountType, Function.identity(), (key1, key2) -> key1));
        for (DineinOrderDetailRespDTO otherOrderDetail : dineinOrderDetailRespDTO.getOtherOrderDetails()) {
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS = Optional.ofNullable(otherOrderDetail.getDiscountFeeDetailDTOS())
                    .orElse(Lists.newArrayList());
            for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
                DiscountFeeDetailDTO totalDiscountFeeDetailDTO = discountFeeDetailMap.get(discountFeeDetailDTO.getDiscountType());
                if (Objects.isNull(totalDiscountFeeDetailDTO)) {
                    discountFeeDetailMap.put(discountFeeDetailDTO.getDiscountType(), discountFeeDetailDTO);
                } else {
                    totalDiscountFeeDetailDTO.setDiscountFee(totalDiscountFeeDetailDTO.getDiscountFee()
                            .add(discountFeeDetailDTO.getDiscountFee()));
                }
            }
        }
        return discountFeeDetailMap.values()
                .stream()
                .sorted(Comparator.comparing(DiscountFeeDetailDTO::getDiscountType))
                .collect(Collectors.toList());
    }

    private void structureDiscountFeeAndPayFeePrint(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        if (CollectionUtils.isEmpty(dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS())) {
            return;
        }
        // 支付方式中的团购信息
        List<ActuallyPayFeeDetailDTO> grouponPayFeeList = dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS().stream()
                .filter(pay -> PaymentTypeEnum.getFilterGrouponPaymentTypes().contains(pay.getPaymentType())).collect(Collectors.toList());
        // 支付方式中的第三方活动信息
        ActuallyPayFeeDetailDTO thirdPayFeeDetail = dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS().stream()
                .filter(pay -> PaymentTypeEnum.THIRD_ACTIVITY.getCode() == pay.getPaymentType()).findFirst().orElse(null);
        // 如果是空直接返回
        if (CollUtil.isEmpty(grouponPayFeeList) && Objects.isNull(thirdPayFeeDetail)) {
            return;
        }
        // 处理第三方活动
        buildThirdDiscountFeeAndPayFee(dineinOrderDetailRespDTO, thirdPayFeeDetail);
        // 处理团购优惠名称
        buildGrouponDiscountName(dineinOrderDetailRespDTO);
        // 多单结账处理
        List<DineinOrderDetailRespDTO> otherOrderDetails = dineinOrderDetailRespDTO.getOtherOrderDetails();
        if (CollectionUtils.isNotEmpty(otherOrderDetails)) {
            otherOrderDetails.forEach(this::structureDiscountFeeAndPayFeePrint);
        }
    }

    /**
     * 处理团购优惠名称
     */
    private void buildGrouponDiscountName(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        List<DiscountFeeDetailDTO> discountFeeDetails = dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS();
        if (CollUtil.isEmpty(discountFeeDetails)) {
            return;
        }
        for (DiscountFeeDetailDTO detailDTO : discountFeeDetails) {
            for (GroupBuyTypeEnum groupBuyTypeEnum : GroupBuyTypeEnum.values()) {
                if (Objects.equals(groupBuyTypeEnum.getCode(), detailDTO.getDiscountType())) {
                    detailDTO.setDiscountName(groupBuyTypeEnum.getDesc() + DiscountTypeConstants.DISCOUNT);
                }
            }
        }
    }


    private void buildThirdDiscountFeeAndPayFee(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, ActuallyPayFeeDetailDTO thirdPayFeeDetail) {
        if (Objects.isNull(thirdPayFeeDetail)) {
            return;
        }
        // 优惠 + 第三方活动
        BigDecimal discountFee = thirdPayFeeDetail.getAmount();
        dineinOrderDetailRespDTO.setDiscountFee(dineinOrderDetailRespDTO.getDiscountFee().add(discountFee));
        DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
        discountFeeDetailDTO.setDiscountName(DiscountTypeEnum.THIRD_ACTIVITY.getDesc());
        discountFeeDetailDTO.setDiscountFee(thirdPayFeeDetail.getAmount());
        dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS()
                .add(discountFeeDetailDTO);
        // 打印金额为负数时处理为0
        BigDecimal actuallyPayFee = dineinOrderDetailRespDTO.getActuallyPayFee()
                .subtract(thirdPayFeeDetail.getAmount());
        if (BigDecimalUtil.lessThanZero(actuallyPayFee)) {
            actuallyPayFee = BigDecimal.ZERO;
        }
        dineinOrderDetailRespDTO.setActuallyPayFee(actuallyPayFee);
        // 实付去掉
        dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS()
                .removeIf(pay -> Objects.equals(PaymentTypeEnum.THIRD_ACTIVITY.getCode(), pay.getPaymentType()));
    }

    private void checkInvoiceCode(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO, PrintDTO printDTO) {
        try {
            if (Objects.nonNull(baseDTO.getIsInvoiceCode())
                    && baseDTO.getIsInvoiceCode() == BooleanEnum.TRUE.getCode()) {
                RequestGenerateInvoiceDTO request = new RequestGenerateInvoiceDTO();
                request.setAccount(baseDTO.getInvoicePhone());
                request.setOrderNo(dineinOrderDetailRespDTO.getGuid());
                request.setAccountName(baseDTO.getAccountName());
                request.setStoreGuid(dineinOrderDetailRespDTO.getStoreGuid());
                request.setOrderAmount(dineinOrderDetailRespDTO.getActuallyPayFee());
                log.info("顾客实付金额={}", dineinOrderDetailRespDTO.getActuallyPayFee());
                String invoiceCode = invoiceService.generateOrderInvoice(request);
                log.info("生成开票二维码小票：{}", invoiceCode);
                if (!StringUtils.isEmpty(invoiceCode)) {
                    printDTO.setInvoiceCode(invoiceCode);
                    printDTO.setInvoiceAmount(String.valueOf(request.getOrderAmount()));
                }
            }
        } catch (Exception e) {
            log.error("打印生成开票二维码失败", e);
        }
    }

    /**
     * 打印菜品复单
     */
    @Override
    public boolean printItemRepeatOrder(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        // 排序
        Collections.reverse(dineinOrderDetailRespDTO.getDineInItemDTOS());
        // 打印商品排序
        printItemOrder(dineinOrderDetailRespDTO);
        // 查询订单扩展信息
        queryOrderExtendsInfo(dineinOrderDetailRespDTO);
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(dineinOrderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .storeInfoFunction(storeClientService::queryStoreBizByGuid)
                .build();
        PrintDTO printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.ITEM_REPEAT_ORDER, tradeDataContainer);
        return print(printDTO);
    }

    @Override
    public void printTransferItem(BaseDTO baseDTO,
                                  DineinOrderDetailRespDTO newOrderDetailRespDTO,
                                  DineinOrderDetailRespDTO oldOrderDetailRespDTO) {
        updateThreadStoreName();
        // 查询新单订单扩展信息
        queryOrderExtendsInfo(newOrderDetailRespDTO);
        // 查询原单订单扩展信息
        queryOrderExtendsInfo(oldOrderDetailRespDTO);
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(newOrderDetailRespDTO)
                .oldDineinOrderDetailRespDTO(oldOrderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .build();
        PrintDTO printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.TRANSFER_ITEM, tradeDataContainer);
        print(printDTO);
        log.info("[打印转菜单推送]完成");
    }

    @Override
    public boolean printRefundOrder(BaseDTO baseDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        TradeDataContainer tradeDataContainer = TradeDataContainer.builder()
                .baseDTO(baseDTO)
                .dineinOrderDetailRespDTO(dineinOrderDetailRespDTO)
                .areaGuidFromTableGuidFunction(tableClientService::getAreaGuidByTableGuid)
                .storeInfoFunction(storeClientService::queryStoreBizByGuid)
                .build();
        PrintDTO printDTO = AbstractPrintDtoFactory.create(InvoiceTypeEnum.REFUND_INVOICE, tradeDataContainer);
        return print(printDTO);
    }

    /**
     * 打印商品排序
     */
    private void printItemOrder(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        StoreConfigQueryDTO storeConfigQueryDTO = new StoreConfigQueryDTO();
        String storeGuid = dineinOrderDetailRespDTO.getStoreGuid();
        if (StringUtils.isEmpty(storeGuid)) {
            storeGuid = UserContextUtils.getStoreGuid();
        }
        storeConfigQueryDTO.setStoreGuid(storeGuid);
        String printItemOrderConfig = businessClientService.queryPrintItemOrderConfig(storeConfigQueryDTO);
        if (PrintItemOrderEnum.DESC.name().equals(printItemOrderConfig)) {
            return;
        }
        Collections.reverse(dineinOrderDetailRespDTO.getDineInItemDTOS());
    }

    /**
     * 获取设备GUID
     *
     * @param dineinOrderDetailRespDTO
     * @return 设备guid
     */
    private String getDeviceId(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        if (ObjectUtil.isNull(dineinOrderDetailRespDTO)) {
            return "";
        }
        String storeGuid = dineinOrderDetailRespDTO.getStoreGuid();
        String tableGuid = dineinOrderDetailRespDTO.getDiningTableGuid();
        if (org.apache.commons.lang3.StringUtils.isEmpty(storeGuid) || org.apache.commons.lang3.StringUtils.isEmpty(tableGuid)) {
            return "";
        }
        PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO();
        padOrderTypeReqDTO.setStoreGuid(storeGuid);
        padOrderTypeReqDTO.setTableGuid(tableGuid);
        StoreDeviceDTO storeDeviceDTO = organizationClientService.queryDeviceByStoreTable(padOrderTypeReqDTO);
        return Optional.ofNullable(storeDeviceDTO).map(StoreDeviceDTO::getDeviceGuid).orElse("");
    }


    public boolean print(PrintDTO printDTO) {
        if (printDTO == null) {
            log.error("打印失败，打印体组装失败");
            return false;
        }
        log.info("printBody : {}", JacksonUtils.writeValueAsString(printDTO));
        Message message = new Message(RocketMqConfig.PRINT_MESSAGE_TOPIC,
                RocketMqConfig.PRINT_MESSAGE_TAG, JacksonUtils.toJsonByte(printDTO));
        message.getProperties().put(RocketMqConfig.MESSAGE_CONTEXT, UserContextUtils.getJsonStr());
        message.getProperties().put(RocketMqConfig.MESSAGE_LOCALE, JacksonUtils.writeValueAsString(LocaleContextHolder.getLocale()));
        return defaultRocketMqProducer.sendMessage(message);
    }


    private void handleMemberPrice(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        // 如果没有使用会员价，则不打印会员价
        try {
            setDineInItemMemberPrice(dineinOrderDetailRespDTO, dineinOrderDetailRespDTO.getDineInItemDTOS());
            if (CollectionUtils.isNotEmpty(dineinOrderDetailRespDTO.getSubOrderDetails())) {
                // 并台
                List<DineInItemDTO> dineInItemDTOS = dineinOrderDetailRespDTO.getSubOrderDetails().stream()
                        .filter(e -> CollectionUtils.isNotEmpty(e.getDineInItemDTOS()))
                        .flatMap(e -> e.getDineInItemDTOS().stream()).collect(Collectors.toList());
                setDineInItemMemberPrice(dineinOrderDetailRespDTO, dineInItemDTOS);
            }
            if (CollectionUtils.isNotEmpty(dineinOrderDetailRespDTO.getOtherOrderDetails())) {
                // 多单结账
                List<DineInItemDTO> dineInItemDTOS = dineinOrderDetailRespDTO.getOtherOrderDetails().stream()
                        .filter(e -> CollectionUtils.isNotEmpty(e.getDineInItemDTOS()))
                        .flatMap(e -> e.getDineInItemDTOS().stream()).collect(Collectors.toList());
                setDineInItemMemberPrice(dineinOrderDetailRespDTO, dineInItemDTOS);
            }

        } catch (Exception e) {
            log.error("判断会员价错误：e:", e);
        }
    }


    /**
     * 设置商品会员价
     */
    private void setDineInItemMemberPrice(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, List<DineInItemDTO> dineInItemDTOS) {
        if (CollectionUtils.isEmpty(dineInItemDTOS)) {
            return;
        }
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            if (Objects.isNull(dineinOrderDetailRespDTO.getCalculateByMemberPrice()) || dineinOrderDetailRespDTO.getCalculateByMemberPrice() == 0) {
                dineInItemDTO.setMemberPrice(null);
            } else {
                if (CollectionUtils.isNotEmpty(dineInItemDTO.getPackageSubgroupDTOS())) {
                    BigDecimal addTotalPrice = dineInItemDTO.getPackageSubgroupDTOS().stream()
                            .flatMap(e -> e.getSubDineInItemDTOS().stream())
                            .filter(e -> Objects.nonNull(e.getAddPrice()) && e.getAddPrice().compareTo(BigDecimal.ZERO) > 0)
                            .map(e -> e.getAddPrice().multiply(e.getCurrentCount()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (Objects.nonNull(dineInItemDTO.getMemberPrice()) && addTotalPrice.compareTo(BigDecimal.ZERO) > 0) {
                        dineInItemDTO.setMemberPrice(dineInItemDTO.getMemberPrice().add(addTotalPrice));
                    }
                }
            }
        }
    }

    /**
     * 查询订单扩展信息
     */
    private void queryOrderExtendsInfo(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        if (Objects.equals(dineinOrderDetailRespDTO.getTradeMode(), TradeModeEnum.DINEIN.getCode())) {
            // 如果是正餐
            OrderExtendsDO orderExtendsDO = orderExtendsService.getById(dineinOrderDetailRespDTO.getGuid());
            if (Objects.isNull(orderExtendsDO)) {
                return;
            }
            dineinOrderDetailRespDTO.setAssociatedFlag(orderExtendsDO.getAssociatedFlag());
            if (Boolean.TRUE.equals(orderExtendsDO.getAssociatedFlag())) {
                dineinOrderDetailRespDTO.setAssociatedSn(orderExtendsDO.getAssociatedSn());
                dineinOrderDetailRespDTO.setAssociatedTableGuids(Objects.nonNull(orderExtendsDO.getAssociatedTableGuids()) ?
                        JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableGuids()) : Lists.newArrayList());
                dineinOrderDetailRespDTO.setAssociatedTableNames(Objects.nonNull(orderExtendsDO.getAssociatedTableNames()) ?
                        JacksonUtils.toObjectList(String.class, orderExtendsDO.getAssociatedTableNames()) : Lists.newArrayList());
            }
        }
    }
}
