package com.holderzone.saas.store.trade.service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderLockService
 * @date 2019/09/09 18:36
 * @description //TODO
 * @program IdeaProjects
 */
public interface OrderLockService {

    boolean checkOrderLock(String orderId);

    String getDeviceLockId(String orderId);

    boolean tryLockwithDeviceId(String orderId, String deviceId);

    boolean tryLock(String orderId);

    void unlock(String orderId);

    boolean unlockBydeviceId(String orderId, String deviceId);

    List<String> getMainAndSubGuids(String mainOrderId);


}