package com.holderzone.saas.store.trade.entity.read;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "调整单商品")
@Data
public class AdjustItme {

    @ApiModelProperty(value = "订单商品guid")
    private String itmeGuid;

    @ApiModelProperty(value = "订单商品名称")
    private String itemName;

    @ApiModelProperty(value = "订单商品价格")
    private BigDecimal price;

    @ApiModelProperty(value = "订单商品数量")
    private BigDecimal count;

    @ApiModelProperty(value = "调整商品数量")
    private BigDecimal adjustCount;

    @ApiModelProperty(value = "回滚数量")
    private BigDecimal backCount;
}
