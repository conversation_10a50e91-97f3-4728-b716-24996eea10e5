package com.holderzone.saas.store.trade.service.impl;

import com.holderzone.framework.util.IDUtils;
import com.holderzone.saas.store.dto.order.common.HangOrderDTO;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.service.HangOrderService;
import com.holderzone.saas.store.trade.utils.RedisKeyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.holderzone.saas.store.trade.entity.constant.OrderRedisConstant.HANG_GROUP;


/**
 * <AUTHOR>
 * @version 1.0
 * @className
 * @date 2018/09/04 16:10
 * @description //TODO
 * @program holder-saas-store-order
 */
@Service
public class HangOrderServiceImpl implements HangOrderService {

    @Autowired
    private RedisHelper redisHelper;

    @Override
    public Boolean gainOrder(HangOrderDTO hangOrderDTO) {
        String hangOrderKeys = hangOrderDTO.getHangOrderKey();
        String[] split = hangOrderKeys.split(",");
        for (String hangOrderKey : split) {
            redisHelper.zRemove(RedisKeyUtil.getKey(HANG_GROUP, hangOrderDTO.getStoreGuid()), hangOrderKey);
            redisHelper.delete(RedisKeyUtil.getKey(HANG_GROUP, hangOrderKey));
        }

        return Boolean.TRUE;
    }

    @Override
    public String hangOrder(HangOrderDTO hangOrderDTO) {
        String hangOrderKey = IDUtils.nextId();
        String key = RedisKeyUtil.getKey(HANG_GROUP, hangOrderKey);

        Calendar nowTime = Calendar.getInstance();
        nowTime.add(Calendar.HOUR, 2);
        Date expireTime = nowTime.getTime();
        redisHelper.set(key, hangOrderDTO.getOrder());
        redisHelper.expireAt(key, expireTime);
        redisHelper.zAdd(RedisKeyUtil.getKey(HANG_GROUP, hangOrderDTO.getStoreGuid()), hangOrderKey, nowTime
                .getTimeInMillis());

        return hangOrderKey;
    }


    @Override
    public Map<String, String> hangOrderList(HangOrderDTO hangOrderDTO) {
        String storeKey = RedisKeyUtil.getKey(HANG_GROUP, hangOrderDTO.getStoreGuid());
        redisHelper.zRemoveRangeByScore(storeKey, 1L, Calendar
                .getInstance().getTimeInMillis());
        Set<String> keys = redisHelper.zRange(storeKey, 0L, -1L);
        Map<String, String> map = new HashMap<>();
        for (String key : keys) {
            String order = redisHelper.get(RedisKeyUtil.getKey(HANG_GROUP, key));
            map.put(key, order);
        }
        return map;
    }
}
