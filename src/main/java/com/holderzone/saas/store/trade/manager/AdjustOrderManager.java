package com.holderzone.saas.store.trade.manager;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.TraceContextUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.deposit.req.DepositDish;
import com.holderzone.saas.store.dto.deposit.req.DepositErpSyncDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.order.common.*;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.takeaway.response.StockStoreBindResqDTO;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.bo.AdjustOrderBO;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.repository.feign.ItemClientService;
import com.holderzone.saas.store.trade.repository.feign.ProducerFeignClient;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.DineInService;
import com.holderzone.saas.store.trade.service.ExecuteStockReduceService;
import com.holderzone.saas.store.trade.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 调整单 业务层
 */
@Slf4j
@Component("adjustOrderManager")
public class AdjustOrderManager {

    @Autowired
    private AdjustOrderService adjustOrderService;

    @Autowired
    private AdjustOrderDetailsService adjustOrderDetailsService;

    @Autowired
    private DineInService dineInService;

    @Autowired
    private ItemAttrService itemAttrService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderItemService orderItemService;

    @Autowired
    private ItemClientService itemClientService;

    @Autowired
    private ProducerFeignClient producerFeignClient;

    @Resource
    private ExecuteStockReduceService executeStockReduceService;

    @Resource
    private DynamicHelper dynamicHelper;

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Value("${erp.host}")
    private String erpHost;

    /**
     * 新增调整单
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(AdjustOrderBO biz) {
        AdjustOrderDO order = biz.getOrder();
        List<AdjustOrderDetailsDO> details = biz.getDetails();
        List<ItemAttrDO> attrList = biz.getAttrList();
        List<Long> orderItemGuidList = biz.getOrderItemGuidList();
        List<DineInItemDTO> orderItemDTOList = biz.getOrderItemDTOList();
        List<DineInItemDTO> adjustOrderItemDTOList = biz.getAdjustOrderItemDTOList();

        // 校验订单明细
        checkAdjustOrderItem(details);

        if (!TradeModeEnum.TAKEOUT.getMode().equals(order.getTradeMode())) {
            // 查询堂食订单
            OrderDO originalOrder = orderService.getById(biz.getOrderGuid());
            biz.setOriginalOrder(originalOrder);
            biz.setBusinessDay(originalOrder.getBusinessDay());
            // 业务校验
            checkOrder(originalOrder);
            checkOrderItem(orderItemDTOList);
        }

        // 查询并设置 商品核算价
        setItemAccountingPrice(order.getStoreGuid(), details);

        // 保存调整单
        adjustOrderService.saveOrder(order);
        adjustOrderDetailsService.saveBatch(details);
        if (CollectionUtils.isNotEmpty(attrList)) {
            itemAttrService.saveBatch(attrList);
        }
        if (!TradeModeEnum.TAKEOUT.getMode().equals(order.getTradeMode())) {
            // 修改订单和订单商品明细上的是否调整字段
            orderService.updateAdjustStateIsTrue(biz.getOrderGuid());
            orderItemService.updateBatchIsAdjustItemIsTrue(orderItemGuidList);
        } else {
            // 处理外卖商品映射数量
            orderItemDTOList.forEach(e -> {
                if (Objects.nonNull(e.getMappingCount()) && e.getMappingCount().compareTo(e.getCurrentCount()) != 0) {
                    e.setCurrentCount(e.getMappingCount());
                }
            });
            adjustOrderItemDTOList.forEach(e -> {
                if (Objects.nonNull(e.getMappingCount()) && e.getMappingCount().compareTo(e.getCurrentCount()) != 0) {
                    e.setCurrentCount(e.getMappingCount());
                }
            });
        }
        // 计算erp库存调整
        Map<String, BigDecimal> adjustMap = calculate(orderItemDTOList, adjustOrderItemDTOList);
        log.info("调整erp库存:{}", JacksonUtils.writeValueAsString(adjustMap));
        modifyErpRepertory(biz, adjustMap);
        return order.getGuid();
    }

    /**
     * 校验原订单状态是否可以更改
     */
    private void checkOrder(OrderDO originalOrder) {
        if (StateEnum.SUCCESS.getCode() != originalOrder.getState()) {
            throw new BusinessException("此订单当前状态不支持调整！");
        }
        if (Objects.nonNull(originalOrder.getRefundOrderGuid())) {
            throw new BusinessException("该订单已产生退款，无法进行调整！");
        }
    }

    /**
     * 校验原订单商品明细是否存在，并且无调整记录
     */
    private void checkOrderItem(List<DineInItemDTO> orderItemList) {
        for (DineInItemDTO item : orderItemList) {
            if (Boolean.TRUE.equals(item.getIsAdjustItem())) {
                throw new BusinessException("存在已调整的订单明细，请重新选择商品！");
            }
        }
    }

    private void checkAdjustOrderItem(List<AdjustOrderDetailsDO> details) {
        if (CollectionUtils.isEmpty(details)) {
            throw new BusinessException("调整商品明细为空！");
        }
    }

    /**
     * 查询并设置 商品核算价
     */
    private void setItemAccountingPrice(String storeGuid, List<AdjustOrderDetailsDO> details) {
        ItemStringListDTO query = new ItemStringListDTO();
        List<String> skuIds = details.stream().map(AdjustOrderDetailsDO::getSkuGuid).collect(Collectors.toList());
        query.setStoreGuid(storeGuid);
        query.setDataList(skuIds);
        log.info("远程获取商品核算价参数：{}", JacksonUtils.writeValueAsString(query));
        List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTO = itemClientService.selectSkuTakeawayInfoRespDTOListV2(query);
        log.info("获取到商品核算价参数：{}", JacksonUtils.writeValueAsString(skuTakeawayInfoRespDTO));
        Map<String, SkuTakeawayInfoRespDTO> takeawayInfoRespMap = skuTakeawayInfoRespDTO.stream()
                .collect(Collectors.toMap(SkuTakeawayInfoRespDTO::getSkuGuid, e -> e));
        if (MapUtils.isEmpty(takeawayInfoRespMap)) {
            return;
        }
        for (AdjustOrderDetailsDO detail : details) {
            SkuTakeawayInfoRespDTO infoRespDTO = takeawayInfoRespMap.get(detail.getSkuGuid());
            if (Objects.nonNull(infoRespDTO)) {
                detail.setTakeawayAccountingPrice(infoRespDTO.getTakeawayAccountingPrice());
                detail.setAccountingPrice(infoRespDTO.getAccountingPrice());
            }
        }
    }

    /**
     * 计算商品库存加减
     * 计算思路：
     * 1.假如需要调整的商品(原订单商品)都要回退对应的库存 -> A集合回退库存
     * 2.新调整的商品都是要扣减的库存 -> B集合扣减库存
     * 3。A、B集合合并，得到一个key为商品guid，value为调整数量的map，备注：（调整数量为正的表示需要入库，调整数量为负的表示需要出库，调整数量为0的剔除）
     */
    private Map<String, BigDecimal> calculate(List<DineInItemDTO> orderItemList, List<DineInItemDTO> adjustOrderDetailList) {
        // oldItemMap: key:商品guid，value:需要回退的库存
        Map<String, BigDecimal> oldItemMap = ItemUtil.itemEstimateHander(orderItemList);

        // adjustItemMap: key:商品guid，value:需要扣减的库存
        Map<String, BigDecimal> adjustItemMap = ItemUtil.itemEstimateHander(adjustOrderDetailList);

        // 合并
        for (Map.Entry<String, BigDecimal> entry : oldItemMap.entrySet()) {
            BigDecimal count = adjustItemMap.get(entry.getKey());
            if (Objects.nonNull(count)) {
                oldItemMap.put(entry.getKey(), entry.getValue().subtract(count));
                adjustItemMap.remove(entry.getKey());
            }
        }
        if (MapUtils.isNotEmpty(adjustItemMap)) {
            for (Map.Entry<String, BigDecimal> entry : adjustItemMap.entrySet()) {
                oldItemMap.put(entry.getKey(), entry.getValue().negate());
            }
        }
        // clear 调整数为0的商品
        oldItemMap.entrySet().removeIf(entry -> Objects.nonNull(entry.getValue()) && entry.getValue().compareTo(BigDecimal.ZERO) == 0);
        return oldItemMap;
    }


    private void modifyErpRepertory(AdjustOrderBO biz, Map<String, BigDecimal> adjustMap) {
        String orderNo = biz.getOrderNo();
        LocalDate businessDay = biz.getBusinessDay();
        Integer tradeMode = biz.getOrder().getTradeMode();
        AdjustOrderDO adjustOrder = biz.getOrder();

        if (MapUtils.isEmpty(adjustMap)) {
            return;
        }
        UserContext userContext = UserContextUtils.get();
        String traceId = TraceContextUtils.getTraceId();
        taskExecutor.execute(() -> {
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            UserContextUtils.putErpAndStore(userContext.getEnterpriseGuid(), userContext.getStoreGuid());
            TraceContextUtils.setTraceId(traceId);
            Set<String> skuGuids = adjustMap.keySet();
            // 通过商品skuGuid查询sku基本信息
            List<SkuInfoRespDTO> skus = itemClientService.listSkuInfo(new ArrayList<>(skuGuids));
            log.info("通过商品skuGuid查询sku基本信息：{}", JacksonUtils.writeValueAsString(skus));
            Map<String, SkuInfoRespDTO> skuMap = skus.stream()
                    .collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key2));

            List<DepositDish> dishes = new ArrayList<>();
            adjustMap.forEach((key, value) -> {
                SkuInfoRespDTO skuInfo = skuMap.get(key);
                if (Objects.nonNull(skuInfo)) {
                    DepositDish dish = new DepositDish();
                    // erp库存需要根据品牌库sku进行操作
                    dish.setSkuId(Optional.ofNullable(skuInfo.getParentGuid()).orElse(skuInfo.getSkuGuid()));
                    dish.setItemName(skuInfo.getItemName());
                    dish.setSkuName(skuInfo.getName());
                    dish.setTypeName(skuInfo.getTypeName());
                    dish.setPrice(skuInfo.getSalePrice());
                    dish.setSkuCount(value.abs());
                    dish.setUnit(skuInfo.getUnit());
                    dish.setItemType(skuInfo.getItemType());
                    // 0 入库 1 出库
                    dish.setType(value.compareTo(BigDecimal.ZERO) > 0 ? 0 : 1);
                    dishes.add(dish);
                }
            });
            if (CollectionUtils.isEmpty(dishes)) {
                return;
            }

            DepositErpSyncDTO erpSyncDTO = new DepositErpSyncDTO();
            String storeGuid = userContext.getStoreGuid();
            if (TradeModeEnum.TAKEOUT.getMode().equals(tradeMode)) {
                // 如果是外卖，需要查询下单门店对应绑定的库存门店
                storeGuid = getStockBindStore(storeGuid);
                TakeoutOrderDTO takeoutOrderDetail = biz.getTakeoutOrderDetail();
                erpSyncDTO.setOrderFee(takeoutOrderDetail.getTotal().add(adjustOrder.getAdjustPrice()));
                erpSyncDTO.setActuallyPayFee(takeoutOrderDetail.getCustomerActualPay().add(adjustOrder.getAdjustPrice()));
                erpSyncDTO.setTakeawayFlag(true);
                erpSyncDTO.setOrderCreateTime(takeoutOrderDetail.getCreateTime());
            } else {
                OrderDO originalOrder = biz.getOriginalOrder();
                erpSyncDTO.setDiningTableName(originalOrder.getDiningTableName());
                erpSyncDTO.setOrderFee(originalOrder.getOrderFee());
                erpSyncDTO.setActuallyPayFee(originalOrder.getActuallyPayFee());
                erpSyncDTO.setGuestCount(originalOrder.getGuestCount());
                erpSyncDTO.setTakeawayFlag(false);
                erpSyncDTO.setOrderCreateTime(originalOrder.getGmtCreate());
            }
            erpSyncDTO.setBusinessType(1);
            erpSyncDTO.setBusinessDate(businessDay);
            erpSyncDTO.setOrderGuid(String.valueOf(biz.getOrderGuid()));
            erpSyncDTO.setThirdNo(orderNo);
            erpSyncDTO.setStoreId(storeGuid);
            erpSyncDTO.setUserGuid(userContext.getUserGuid());
            erpSyncDTO.setUsername(userContext.getUserName());
            erpSyncDTO.setDepositDishes(dishes);
            executeStockReduceService.executeStockAdjust(erpSyncDTO);
            UserContextUtils.remove();
            TraceContextUtils.remove();
        });
    }

    /**
     * 查询库存绑定门店
     */
    private String getStockBindStore(String storeGuid) {
        StockStoreBindResqDTO storeBindDO = producerFeignClient.getBindStockStore(storeGuid);
        if (Objects.nonNull(storeBindDO)) {
            String branchStoreGuid = storeBindDO.getBranchStoreGuid();
            if (!StringUtils.isEmpty(storeGuid)) {
                storeGuid = branchStoreGuid;
            }
        }
        return storeGuid;
    }
}
