package com.holderzone.saas.store.trade.context;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LicalizeSynchronizeContext
 * @date 2019/11/18 9:25
 * @description //TODO
 * @program IdeaProjects
 */
public class LocalizeSynchronizeContext {

//    private static ThreadLocal<Set<String>> localizeOrderContext = new ThreadLocal<>();

//    public static void setOrderGuidList(Set<String> orderGuids){
//        localizeOrderContext.set(orderGuids);
//    }

    public static void putOrderGuid(String orderGuid){
        return;
//        Set<String> guids = localizeOrderContext.get();
//        if(guids == null){
//            guids = new TreeSet<>();
//        }
//        guids.add(orderGuid);
//        localizeOrderContext.set(guids);
    }

    public static void putOrderGuid(Long... orderGuid){
        return;
//        Stream.of(orderGuid).map(String::valueOf).forEach(LocalizeSynchronizeContext::putOrderGuid);
    }


//    public static String getOrderGuidJsonString(){
////        Set<String> list = localizeOrderContext.get();
////        return CollectionUtil.isEmpty(list) ? null : JacksonUtils.writeValueAsString(list);
////    }

//    public static void remove(){
//        localizeOrderContext.remove();
//    }
}